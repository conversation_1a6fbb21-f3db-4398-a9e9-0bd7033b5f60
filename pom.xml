<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <groupId>com.biz.crm.tpm</groupId>
    <artifactId>tpm</artifactId>
    <packaging>pom</packaging>
    <version>202405</version>
    <modelVersion>4.0.0</modelVersion>
    <description>tpm project for Spring Boot</description>
    <name>crm-tpm</name>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.9</version>
    </parent>

    <properties>
        <springboot.version>2.6.9</springboot.version>
        <spring.cloud.version>2021.0.3</spring.cloud.version>
        <spring.cloud.netflix.version>2.2.9.RELEASE</spring.cloud.netflix.version>
        <spring.cloud.nacos.discovery.version>2021.0.1.0</spring.cloud.nacos.discovery.version>

        <business.version>202405</business.version>
        <crm.workflow.version>202405</crm.workflow.version>
        <crm.mdm.version>202405</crm.mdm.version>
        <crm.dms.version>202405</crm.dms.version>

        <business.common.version>2024.05-DEV-SNAPSHOT</business.common.version>

        <crm.common.version>24.07.09</crm.common.version>
        <nebula.version>24.07.09</nebula.version>

        <mybatis.plus.boot.starter>3.4.2</mybatis.plus.boot.starter>
        <lombok.version>1.18.10</lombok.version>
        <springfox.version>2.10.5</springfox.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <fel.version>0.8</fel.version>
        <org.quartz.scheduler>2.2.1</org.quartz.scheduler>
        <!--修改maven-jar-plugin版本-->
        <maven.jar.plugin.version>3.2.1</maven.jar.plugin.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <xiaoymin.version>2.0.9</xiaoymin.version>
        <snakeyaml.version>1.33</snakeyaml.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>23.0</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.boot.starter}</version>
            </dependency>
            <!-- nebula 组件 -->
            <dependency>
                <groupId>com.biz-united.nebula.datasource</groupId>
                <artifactId>nebula-datasource</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.init</groupId>
                <artifactId>nebula-init</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.rbac</groupId>
                <artifactId>rbac-sdk</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.saturn</groupId>
                <artifactId>saturn-core</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.script</groupId>
                <artifactId>script-sdk</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.security</groupId>
                <artifactId>security-sdk</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.task</groupId>
                <artifactId>task-sdk</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.tenant</groupId>
                <artifactId>nebula-tenant</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.utils</groupId>
                <artifactId>nebula-utils</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.venus</groupId>
                <artifactId>venus-sdk</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <dependency>
                <groupId>com.biz-united.nebula.competence</groupId>
                <artifactId>competence-sdk</artifactId>
                <version>${nebula.version}</version>
            </dependency>
            <!--CRM common -->
            <dependency>
                <groupId>com.biz.crm</groupId>
                <artifactId>crm-business-common</artifactId>
                <version>${crm.common.version}</version>
            </dependency>
            <!--Lombok -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${xiaoymin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!--     rocketmq start -->
            <dependency>
                <groupId>com.biz.crm.business.common</groupId>
                <artifactId>crm-common-rocketmq</artifactId>
                <version>${business.common.version}</version>
            </dependency>
            <!-- rocketmq end -->
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!-- business-common -->
        <dependency>
            <groupId>com.biz.crm</groupId>
            <artifactId>crm-business-common</artifactId>
            <version>${crm.common.version}</version>
        </dependency>
        <!-- base-common start -->
        <dependency>
            <groupId>com.biz.crm.business.common</groupId>
            <artifactId>crm-base-common</artifactId>
            <version>${business.common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.biz.crm.business.common.page.cache</groupId>
            <artifactId>crm-common-page-cache</artifactId>
            <version>${business.common.version}</version>
        </dependency>
        <!--     base-common end -->
    </dependencies>
    <repositories>
        <repository>
            <id>dev.biz-united.com.cn</id>
            <name>ip822-releases</name>
            <url>http://maven.biz-united.com.cn:28080/artifactory/repo</url>
        </repository>
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/central/</url>
        </repository>
        <repository>
            <id>central</id>
            <name>Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>dev.biz-united.com.cn</id>
            <name>ip822-releases</name>
            <url>http://maven.biz-united.com.cn:28080/artifactory/ext-release-local</url>
        </repository>
        <snapshotRepository>
            <id>dev.biz-united.com.cn</id>
            <name>ip822-snapshots</name>
            <url>http://maven.biz-united.com.cn:28080/artifactory/ext-snapshot-local</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!--修改maven-jar-plugin版本-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven.jar.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>com.biz.crm.tpm.boot</module>
        <module>com.biz.crm.tpm.business.budget</module>
        <module>com.biz.crm.tpm.business.activities</module>
        <module>com.biz.crm.tpm.business.pay</module>
        <module>com.biz.crm.tpm.business.rebate</module>
    </modules>
</project>
