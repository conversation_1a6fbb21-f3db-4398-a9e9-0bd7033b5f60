{"author": "saturn", "version": "1.2.4", "userSecure": "", "currTypeMapperGroupName": "<PERSON><PERSON><PERSON>", "currTemplateGroupName": "CrmMybatisPlus", "currColumnConfigGroupName": "<PERSON><PERSON><PERSON>", "currGlobalConfigGroupName": "<PERSON><PERSON><PERSON>", "typeMapper": {}, "template": {"CrmMybatisPlus": {"name": "CrmMybatisPlus", "elementList": [{"name": "controller.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Controller\")\n\n##保存文件（宏定义）\n#save(\"/controller\", \"Controller.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"controller\")\n\n##定义服务名\n#set($serviceName = $!tool.append($!tool.firstLowerCase($!tableInfo.name), \"Service\"))\n\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\nimport $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;\nimport com.biz.crm.business.common.sdk.model.Result;\nimport io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport io.swagger.annotations.ApiParam;\nimport lombok.extern.slf4j.Slf4j;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.web.bind.annotation.GetMapping;\nimport org.springframework.web.bind.annotation.PatchMapping;\nimport org.springframework.web.bind.annotation.PathVariable;\nimport org.springframework.web.bind.annotation.PostMapping;\nimport org.springframework.web.bind.annotation.RequestBody;\nimport org.springframework.web.bind.annotation.RequestMapping;\nimport org.springframework.web.bind.annotation.DeleteMapping;\nimport org.springframework.web.bind.annotation.RestController;\nimport org.springframework.data.domain.Pageable;\nimport org.springframework.data.web.PageableDefault;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\n\nimport java.util.List;\n\n##表注释（宏定义）\n#tableComment(\"表相关的http接口\")\n@RestController\n@RequestMapping(\"/v1/$tool.firstLowerCase($!tableInfo.name)\")\n@Slf4j\n@Api(tags = \"$!{tableInfo.comment}\")\npublic class $!{tableName} {\n  /**\n  * 服务对象\n  */\n  @Autowired\n  private $!{tableInfo.name}Service $!{serviceName};\n  \n  /**\n   * 分页查询所有数据\n   *\n   * @param pageable 分页对象\n   * @param $entityName 查询实体\n   * @return 所有数据\n  */\n  @ApiOperation(value = \"分页查询所有数据\")\n  @GetMapping(\"findByConditions\")\n  public Result<Page<$!{tableInfo.name}>> findByConditions(@ApiParam(name = \"pageable\", value = \"分页对象\") @PageableDefault(50) Pageable pageable,\n\t\t\t\t\t\t\t\t\t\t\t\t   @ApiParam(name = \"$entityName\", value = \"$!{tableInfo.comment}\") $!tableInfo.name $entityName) {\n    try {\n      Page<$!{tableInfo.name}> page =  this.$!{serviceName}.findByConditions(pageable,$entityName);\n      return Result.ok(page);\n    } catch (RuntimeException e) {\n      log.error(e.getMessage(), e);\n      return Result.error(e.getMessage());\n    }\n  }\n  \n  /**\n   * 通过主键查询单条数据\n   *\n   * @param id 主键\n   * @return 单条数据\n  */\n  @ApiOperation(value = \"通过主键查询单条数据\")\n  @GetMapping(\"{id}\")\n  public Result<$!{tableInfo.name}> findById(@PathVariable @ApiParam(name = \"id\", value = \"主键id\") String id) {\n    try {\n      $!{tableInfo.name} $entityName = this.$!{serviceName}.findById(id);\n      return Result.ok($entityName);\n    } catch (RuntimeException e) {\n      log.error(e.getMessage(), e);\n      return Result.error(e.getMessage());\n    }\n  }\n  \n  /**\n   * 新增数据\n   *\n   * @param $entityName 实体对象\n   * @return 新增结果\n  */\n  @ApiOperation(value = \"新增数据\")\n  @PostMapping\n  public Result<$!{tableInfo.name}> create(@ApiParam(name = \"$entityName\", value = \"$!{tableInfo.comment}\") @RequestBody $!tableInfo.name $entityName) {\n    try {\n      $!{tableInfo.name} result = this.$!{serviceName}.create($entityName);\n      return Result.ok(result);\n    } catch (RuntimeException e) {\n      log.error(e.getMessage(), e);\n      return Result.error(e.getMessage());\n    }\n  }\n  \n  /**\n   * 修改数据\n   *\n   * @param $entityName 实体对象\n   * @return 修改结果\n  */\n  @ApiOperation(value = \"修改数据\")\n  @PatchMapping\n  public Result<$!{tableInfo.name}> update(@ApiParam(name = \"$entityName\", value = \"$!{tableInfo.comment}\") @RequestBody $!tableInfo.name $entityName) {\n    try {\n      $!{tableInfo.name} result = this.$!{serviceName}.update($entityName);\n      return Result.ok(result);\n    } catch (RuntimeException e) {\n      log.error(e.getMessage(), e);\n      return Result.error(e.getMessage());\n    }\n  }\n  \n  /**\n   * 删除数据\n   *\n   * @param idList 主键结合\n   * @return 删除结果\n   */\n  @DeleteMapping\n  public Result<?> delete(@ApiParam(name = \"idList\", value = \"主键集合\") @RequestParam(\"idList\") List<String> idList) {\n    try {\n      this.$!{serviceName}.delete(idList);\n      return Result.ok();\n    } catch (RuntimeException e) {\n      log.error(e.getMessage(), e);\n      return Result.error(e.getMessage());\n    }\n  }\n}"}, {"name": "service.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Service\")\n\n##保存文件（宏定义）\n#save(\"/service\", \"Service.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"service\")\n\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport org.springframework.data.domain.Pageable;\nimport java.util.List;\n\n##表注释（宏定义）\n#tableComment(\"表服务接口\")\npublic interface $!{tableName}{\n\n  /**\n   * 分页查询数据\n   * @param pageable 分页对象\n   * @param $entityName 实体对象\n   * @return\n   */\n  Page<$!{tableInfo.name}> findByConditions(Pageable pageable, $!{tableInfo.name} $entityName);\n  \n   /**\n   * 通过主键查询单条数据\n   * @param id 主键\n   * @return 单条数据\n   */\n  $!{tableInfo.name} findById(String id);\n  \n   /**\n   * 新增数据\n   * @param $entityName 实体对象\n   * @return 新增结果\n   */\n  $!{tableInfo.name} create($!{tableInfo.name} $entityName);\n  \n   /**\n   * 修改新据\n   * @param $entityName 实体对象\n   * @return 修改结果\n   */\n  $!{tableInfo.name} update($!{tableInfo.name} $entityName);\n  \n  /**\n   * 删除数据\n   * @param idList 主键结合\n   * @return 删除结果\n   */\n  void delete(List<String> idList);\n\n}\n"}, {"name": "serviceImpl.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"ServiceImpl\")\n\n##保存文件（宏定义）\n#save(\"/service/internal\", \"ServiceImpl.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"service.internal\")\n\n##定义repository名\n#set($repositoryName = $!tool.append($!tool.firstLowerCase($!tableInfo.name), \"Repository\"))\n\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport $!{tableInfo.savePackageName}.repository.$!{tableInfo.name}Repository;\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name};\nimport $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;\nimport org.springframework.stereotype.Service;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.apache.commons.lang3.ObjectUtils;\nimport org.apache.commons.lang3.StringUtils;\nimport org.apache.commons.lang3.Validate;\nimport org.springframework.data.domain.Pageable;\nimport org.springframework.data.web.PageableDefault;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.transaction.annotation.Transactional;\nimport org.springframework.util.CollectionUtils;\n\nimport java.util.List;\nimport java.util.Objects;\n\n##表注释（宏定义）\n#tableComment(\"表服务实现类\")\n@Service(\"$!tool.firstLowerCase($tableInfo.name)Service\")\npublic class $!{tableName} implements $!{tableInfo.name}Service {\n\n  @Autowired\n  private $!{tableInfo.name}Repository $!{repositoryName};\n\n  /**\n   * 分页查询数据\n   * @param pageable 分页对象\n   * @param $entityName 实体对象\n   * @return\n   */\n  @Override\n  public Page<$!{tableInfo.name}> findByConditions(Pageable pageable, $!{tableInfo.name} $!{entityName}) {\n    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));\n    if (Objects.isNull($!{entityName})) {\n      $!{entityName} = new $!{tableInfo.name}();\n    }\n    return this.$!{repositoryName}.findByConditions(pageable, $!{entityName});\n  }\n  \n  /**\n   * 通过主键查询单条数据\n   * @param id 主键\n   * @return 单条数据\n   */\n  @Override\n  public $!{tableInfo.name} findById(String id) {\n    if (StringUtils.isBlank(id)) {\n\t  return null;\n\t}\n    return this.$!{repositoryName}.getById(id);\n  }\n  \n  /**\n   * 新增数据\n   * @param $entityName 实体对象\n   * @return 新增结果\n   */\n  @Transactional\n  @Override\n  public $!{tableInfo.name} create($!{tableInfo.name} $!{entityName}) {\n    this.createValidate($!{entityName});\n    this.$!{repositoryName}.saveOrUpdate($!{entityName});\n    return $!{entityName};\n  }\n  \n  /**\n   * 修改新据\n   * @param $entityName 实体对象\n   * @return 修改结果\n   */\n  @Transactional\n  @Override\n  public $!{tableInfo.name} update($!{tableInfo.name} $!{entityName}) {\n    this.updateValidate($!{entityName});\n    this.$!{repositoryName}.saveOrUpdate($!{entityName});\n    return $!{entityName};\n  }\n  \n  /**\n   * 删除数据\n   * @param idList 主键结合\n   * @return 删除结果\n   */\n  @Transactional\n  @Override\n  public void delete(List<String> idList) {\n    Validate.isTrue(!CollectionUtils.isEmpty(idList), \"删除数据时，主键集合不能为空！\");\n    this.$!{repositoryName}.removeByIds(idList);\n  }\n  \n  /**\n   * 创建验证\n   * @param $!{entityName}\n   */\n  private void createValidate($!{tableInfo.name} $!{entityName}) {\n    Validate.notNull($!{entityName}, \"新增时，对象信息不能为空！\");\n\t$!{entityName}.setId(null);\n    #foreach($column in $tableInfo.otherColumn)\n#if($!column.obj.isNotNull())\n#if($!column.shortType == \"String\")\n  Validate.notBlank($!{entityName}.get$!tool.firstUpperCase($!{column.name})(), \"新增数据时，$!column.comment不能为空！\");\n#else  \n  Validate.notNull($!{entityName}.get$!tool.firstUpperCase($!{column.name})(), \"新增数据时，$!column.comment不能为空！\");\n#end\n  #end   \n#end\n \n  }\n  \n   /**\n   * 修改验证\n   * @param $!{entityName}\n   */\n  private void updateValidate($!{tableInfo.name} $!{entityName}) {\n    Validate.notNull($!{entityName}, \"修改时，对象信息不能为空！\");\n    #foreach($column in $tableInfo.fullColumn)\n#if($!column.obj.isNotNull())\n#if($!column.shortType == \"String\")\n  Validate.notBlank($!{entityName}.get$!tool.firstUpperCase($!{column.name})(), \"新增数据时，$!column.comment不能为空！\");\n#else  \n  Validate.notNull($!{entityName}.get$!tool.firstUpperCase($!{column.name})(), \"新增数据时，$!column.comment不能为空！\");\n#end\n  #end   \n#end\n  \n  }\n}\n"}, {"name": "mapper.xml.vm", "code": "##引入mybatis支持\n$!{mybatisSupport.vm}\n\n##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Mapper\")\n\n##保存文件（宏定义）\n#save(\"/mapper\", \"Mapper.xml\")\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"$!{tableInfo.savePackageName}.mapper.$!{tableInfo.name}Mapper\">\n\n  <resultMap type=\"$!{tableInfo.savePackageName}.entity.$!{tableInfo.name}\" id=\"$!{tableInfo.name}Map\">\n#foreach($column in $tableInfo.fullColumn)\n    <result property=\"$!column.name\" column=\"$!column.obj.name\" jdbcType=\"$!column.ext.jdbcType\"/>\n#end\n  </resultMap>\n  \n  <sql id = \"$tool.firstLowerCase($!{tableInfo.name})\">\n#foreach($column in $tableInfo.otherColumn)\n    $!column.obj.name $!column.name,\n#end\n    id id\n  </sql>\n\n  <select id=\"findByConditions\" resultMap=\"$!{tableInfo.name}Map\">\n    select\n      *\n    from $!{tableInfo.obj.name}\n  </select>\n</mapper>\n"}, {"name": "repository.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Repository\")\n\n##保存文件（宏定义）\n#save(\"/repository\", \"Repository.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"repository\")\n\n##定义mapper名\n#set($mapperName = $!tool.append($!tool.firstLowerCase($!tableInfo.name), \"Mapper\"))\n\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\nimport $!{tableInfo.savePackageName}.mapper.$!{tableInfo.name}Mapper;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;\nimport org.springframework.stereotype.Component;\nimport org.springframework.data.domain.Pageable;\n\n\n##表注释（宏定义）\n#tableComment(\"表数据库访问层\")\n@Component\npublic class $!{tableName} extends ServiceImpl<$!{tableInfo.name}Mapper, $!{tableInfo.name}> {\n\n  @Autowired\n  private $!{tableInfo.name}Mapper $!{mapperName};\n  \n   /**\n   * 分页查询数据\n   * @param pageable 分页对象\n   * @param $!{entityName} 实体对象\n   * @return\n   */\n  public Page<$!{tableInfo.name}> findByConditions(Pageable pageable, $!{tableInfo.name} $!{entityName}) {\n    Page<$!{tableInfo.name}> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());\n    Page<$!{tableInfo.name}> pageList = this.$!{mapperName}.findByConditions(page, $!{entityName});\n    return pageList;\n  }\n}\n"}, {"name": "mapper.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Mapper\")\n\n##保存文件（宏定义）\n#save(\"/mapper\", \"Mapper.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"mapper\")\n\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport org.apache.ibatis.annotations.Param;\n\n##表注释（宏定义）\n#tableComment(\"表mybatis访问层\")\npublic interface $!{tableName} extends BaseMapper<$!tableInfo.name> {\n\n  /**\n   * 分页查询所有数据\n   *\n   * @param page 分页对象\n   * @param $entityName 查询实体\n   * @return 所有数据\n  */\n  public Page<$!{tableInfo.name}> findByConditions(@Param(\"page\") Page<$!tableInfo.name> page, @Param(\"$!{entityName}\") $!{tableInfo.name} ${entityName});\n}\n"}]}}, "columnConfig": {}, "globalConfig": {}}