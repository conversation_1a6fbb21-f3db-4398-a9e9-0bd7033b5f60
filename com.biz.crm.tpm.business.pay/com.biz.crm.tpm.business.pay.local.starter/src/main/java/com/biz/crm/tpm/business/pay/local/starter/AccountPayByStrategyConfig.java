package com.biz.crm.tpm.business.pay.local.starter;

import com.biz.crm.tpm.business.pay.local.service.strategy.DiscountAccountPayByStrategy;
import com.biz.crm.tpm.business.pay.local.service.strategy.RestockAccountPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.DiscountPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.RestockPayByStrategy;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @describe: 支付策略配置
 * @createTime 2022年06月23日 09:50:00
 */
@Configuration
public class AccountPayByStrategyConfig {


  @Bean
  @ConditionalOnBean(DiscountPayByStrategy.class)
  public DiscountAccountPayByStrategy getDiscountAccountPayByStrategy(DiscountPayByStrategy discountPayByStrategy) {
    DiscountAccountPayByStrategy discountAccountPayByStrategy = new DiscountAccountPayByStrategy();
    discountPayByStrategy.addAccountPayByStrategy(discountAccountPayByStrategy);
    return discountAccountPayByStrategy;
  }

  @Bean
  @ConditionalOnBean(RestockPayByStrategy.class)
  public RestockAccountPayByStrategy getRestockAccountPayByStrategy(RestockPayByStrategy restockPayByStrategy) {
    RestockAccountPayByStrategy restockAccountPayByStrategy = new RestockAccountPayByStrategy();
    restockPayByStrategy.addAccountPayByStrategy(restockAccountPayByStrategy);
    return restockAccountPayByStrategy;
  }
}
