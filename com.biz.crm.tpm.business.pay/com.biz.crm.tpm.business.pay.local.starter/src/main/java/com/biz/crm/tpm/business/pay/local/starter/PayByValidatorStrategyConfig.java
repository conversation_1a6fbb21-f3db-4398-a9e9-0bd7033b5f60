package com.biz.crm.tpm.business.pay.local.starter;

import com.biz.crm.tpm.business.pay.local.service.validator.PayByDiscountValidator;
import com.biz.crm.tpm.business.pay.local.service.validator.PayByRestockValidator;
import com.biz.crm.tpm.business.pay.local.service.validator.PayByTransferValidator;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.DiscountPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.RestockPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.TransferPayByStrategy;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 描述：</br>预算科目控制类型配置类
 *
 * <AUTHOR>
 * @date 2022/5/18
 */
@Configuration
public class PayByValidatorStrategyConfig {

  @Bean
  @ConditionalOnBean(TransferPayByStrategy.class)
  public PayByTransferValidator getPayByTransferValidator(TransferPayByStrategy transferPayByStrategy) {
    PayByTransferValidator payByTransferValidator = new PayByTransferValidator();
    transferPayByStrategy.addGenericValidator(payByTransferValidator);
    return payByTransferValidator;
  }

  @Bean
  @ConditionalOnBean(DiscountPayByStrategy.class)
  public PayByDiscountValidator getPayByDiscountValidator(DiscountPayByStrategy discountPayByStrategy) {
    PayByDiscountValidator payByTransferValidator = new PayByDiscountValidator();
    discountPayByStrategy.addGenericValidator(payByTransferValidator);
    return payByTransferValidator;
  }

  @Bean
  @ConditionalOnBean(RestockPayByStrategy.class)
  public PayByRestockValidator getPayByRestockValidator(RestockPayByStrategy restockPayByStrategy) {
    PayByRestockValidator payByTransferValidator = new PayByRestockValidator();
    restockPayByStrategy.addGenericValidator(payByTransferValidator);
    return payByTransferValidator;
  }

}
