package com.biz.crm.tpm.business.pay.sdk.event;

import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailVo;

/**
 * 费用核销明细;(tpm_audit_detail)相关的事件通知
 * <AUTHOR> Keller
 * @date : 2022-6-24
 */
public interface AuditDetailEventListener{

  /**
   * 当费用核销明细数据被创建时，该事件被触发
   * @param auditDetailVo
   */
  void onCreated(AuditDetailVo auditDetailVo);
  /**
   * 当费用核销明细数据被修改时，该事件被触发
   * @param oldAuditDetailVo 修改前数据
   * @param auditDetailVo  修改后数据
   */
  void onUpdate(AuditDetailVo oldAuditDetailVo, AuditDetailVo auditDetailVo);
  /**
   * 当费用核销明细数据被删除时（逻辑删除），该事件被触发
   * @param auditDetailVo
   */
  void onDeleted(AuditDetailVo auditDetailVo);

}
