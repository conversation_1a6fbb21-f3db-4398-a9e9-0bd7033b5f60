package com.biz.crm.tpm.business.pay.sdk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 费控回调参数
 *
 * @return
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/8 21:25
 */
@Data
public class HecCallbackDto implements Serializable {

    private static final long serialVersionUID = -984561507955794366L;


    @ApiModelProperty("单据类型")
    private String businessType;

    @ApiModelProperty("单据编码")
    private String businessCode;

    @ApiModelProperty("单据明细编码")
    private String businessDetailCode;

    @ApiModelProperty("对方单据编码")
    private String orderCode;

    @ApiModelProperty("对方单据状态")
    private String orderStatus;

    @ApiModelProperty("付款成功时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paySucessDate;

    @ApiModelProperty("入账日期")
    private String postingDate;

    @ApiModelProperty("入账日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date postingDateDate;
}
