package com.biz.crm.tpm.business.pay.sdk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe: 费用冲销Dto
 * @createTime 2022年06月25日 16:01:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WithHoldingWriteOffDto {

  private String id;

  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;

  @ApiModelProperty(name = "推送状态", notes = "推送状态")
  private String pushStatus;

  @ApiModelProperty("费控单号")
  private String externalCode;

  @ApiModelProperty(name = "预提类型", notes = "预提类型")
  private String withHoldingType;

  @ApiModelProperty(name = "预提编号",notes = "预提编号")
  private String withHoldingCode;

  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM")
  @DateTimeFormat(pattern = "yyyy-MM")
  private Date withHoldingYears;

  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  private String activitiesCode;

  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  private String activitiesName;

  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value= "活动明细编码")
  private String activitiesDetailCode;

  @ApiModelProperty(name = "预算科目编码",notes = "")
  private String budgetSubjectsCode;

  @ApiModelProperty(name = "预算科目名称",notes = "")
  private String budgetSubjectsName;

  /**
   * 活动大类编码
   */
  @ApiModelProperty(name = "活动大类编码", notes = "活动大类编码")
  private String costTypeCategoryCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "活动大类名称", notes = "活动大类名称")
  private String costTypeCategoryName;

  /**
   * 活动细类编码
   */
  @ApiModelProperty(name = "活动细类编码", notes = "活动细类编码")
  private String costTypeDetailCode;

  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "活动细类名称", notes = "活动系类名称")
  private String costTypeDetailName;

  /**
   * 组织编号
   */
  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  private String orgName;


  /**
   * 客户编号
   */
  @ApiModelProperty(name = "客户编号", notes = "客户编号")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty(name = "客户名称", notes = "客户名称")
  private String customerName;

  /**
   * 门店编号
   */
  @ApiModelProperty(name = "门店编号", notes = "门店编号")
  private String terminalCode;

  /**
   * 门店名称
   */
  @ApiModelProperty(name = "门店名称", notes = "门店名称")
  private String terminalName;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "申请金额", notes = "申请金额")
  private BigDecimal applyAmount;

  /**
   * 预提金额
   */
  @ApiModelProperty(name = "预提金额", notes = "预提金额")
  private BigDecimal withHoldingAmount;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "支付方式", notes = "支付方式")
  private String payBy;

  /**
   * 上账金额
   */
  @ApiModelProperty(name = "上账金额", notes = "上账金额")
  private String accountAmount;


  /** 开始时间 */
  @ApiModelProperty(name = "beginTime",notes = "开始时间", value= "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /** 结束时间 */
  @ApiModelProperty(name = "endTime",notes = "结束时间", value= "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;



  @ApiModelProperty("费用归属年月")
  private String years;

  @ApiModelProperty("预提年月")
  private String yearMonthLy;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("订单编码")
  private String orderCode;

  @ApiModelProperty("订单名称")
  private String orderName;

  @ApiModelProperty("归属部门编码")
  private String belongDepartmentCode;

  @ApiModelProperty("归属部门名称")
  private String belongDepartmentName;

  @ApiModelProperty("承担部门编码")
  private String bearDepartmentCode;

  @ApiModelProperty("承担部门名称")
  private String bearDepartmentName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("计提来源")
  private String withHoldingSource;

  @ApiModelProperty("管报计提金额")
  private BigDecimal withHoldingReportAmount;

  @ApiModelProperty("调减金额")
  private BigDecimal reduceAmount;

  @ApiModelProperty("实际金额")
  private BigDecimal actualAmount;

  @ApiModelProperty("公式及取值")
  private String formulaInfo;

  @ApiModelProperty("凭证号")
  private String voucherCode;

  @ApiModelProperty("失败原因")
  private String failMsg;

  @ApiModelProperty("审批状态")
  private String status;

  @ApiModelProperty("推送日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("审批流程编码")
  private String processKey;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("预算编码")
  private String budgetCode;

  @ApiModelProperty("冲销类型")
  private String writeOffType;

  @ApiModelProperty("冲销金额")
  private BigDecimal writeOffAmount;

  /** 冲销时间 */
  @ApiModelProperty(name = "writeOffTime",notes = "冲销时间", value= "冲销时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date writeOffTime;

  @ApiModelProperty("冲销年月")
  private String writeOffYears;

  @ApiModelProperty("冲销编码")
  private String writeOffCode;

  @ApiModelProperty("业务编码")
  private String businessCode;

  @ApiModelProperty("是否当期费用(Y/N)")
  private String beThisFee;

  @ApiModelProperty("是否完全兑付")
  private String beWholeCash;

  @ApiModelProperty("查看流程日志所需地址")
  private String hecReceiptUrl;

  /**
   * 是否完全核销
   */
  @ApiModelProperty(name = "是否完全核销", notes = "是否完全核销")
  private String beFullAudit;

  @ApiModelProperty("结案创建人员编码")
  private String auditCreateAccount;

  @ApiModelProperty("核销金额")
  private BigDecimal auditAmount;
  private BigDecimal historyAmount;

  @ApiModelProperty("冲销来源单号")
  private String sourceCode;

  @ApiModelProperty("是否DMS")
  private String beDms;

  @ApiModelProperty("是否补偿冲销")
  private String beReimburse;

  /**
   * 创建时间
   */
  @ApiModelProperty(name = "创建时间", notes = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  @ApiModelProperty("编码规则")
  private String ruleCode;
}
