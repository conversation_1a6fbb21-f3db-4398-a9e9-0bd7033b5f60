package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingCollectDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingCollectVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface WithHoldingCollectService {

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    WithHoldingCollectVo findByCode(String cacheKey, String code);

    /**
     * 生成汇总
     *
     * @param dto
     * @return
     */
    WithHoldingCollectVo save(WithHoldingCollectDto dto);

    /**
     * 删除数据
     *
     * @param ids 主键结合
     */
    void delete(List<String> ids);

    /**
     * 提交审批
     *
     * @param idList
     */
    void submit(List<String> idList);

    /**
     * OA撤回
     *
     * @param code
     * @return
     */
    void recover(String code, String remark);

    /**
     * OA审批回调
     *
     * @param dto
     * @return
     */
    void oaCallback(WithHoldingCollectDto dto);

    /**
     *
     * 预提明细操作预付明细
     *
     * @param dto
     */
    void modifyActivityPrepayRecord(WithHoldingCollectDto dto);

    Page<MarketingPlanCaseVo> findGiftAndSurroundingList(Pageable pageable, MarketingPlanCaseVo caseVo, String collectCode);

    Page<FeeCashDetailVo> findAuditedAndCashedActivityList(Pageable pageable, MarketingPlanCaseVo caseVo, String collectCode);
}
