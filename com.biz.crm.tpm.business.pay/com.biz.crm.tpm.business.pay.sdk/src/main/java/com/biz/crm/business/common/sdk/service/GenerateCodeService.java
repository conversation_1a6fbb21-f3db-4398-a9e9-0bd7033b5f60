package com.biz.crm.business.common.sdk.service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 重新编码规则类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 9:29
 */
public interface GenerateCodeService {


    /**
     * 默认加 yyyyMMdd
     *
     * @param ruleCode
     * @return
     */
    default String generateCodeString(String ruleCode) {
        return this.generateCode(ruleCode, 1).get(0);
    }


    /**
     * 默认加 yyyyMMdd
     *
     * @param ruleCode
     * @return
     */
    default String generateCode(String ruleCode) {
        return this.generateCode(ruleCode, 1).get(0);
    }

    /**
     * 默认加 yyyyMMdd
     *
     * @param ruleCode 编码规则
     * @param number   编码个数
     * @return
     */
    default List<String> generateCode(String ruleCode, int number) {
        return this.generateCode(ruleCode, number, 4);
    }


    /**
     * 默认加 yyyyMMdd
     *
     * @param ruleCode
     * @param number
     * @param mixStrLen
     * @param expire
     * @param unit
     * @return
     */
    default List<String> generateCode(String ruleCode, int number, Integer mixStrLen, long expire, TimeUnit unit) {
        return this.generateCode(ruleCode, number, mixStrLen);
    }



    /**
     * 默认加 yyyyMMdd
     *
     * @param ruleCode  编码规则
     * @param number    编码个数
     * @param mixStrLen 流水长度
     * @return
     */
    List<String> generateCode(String ruleCode, int number, Integer mixStrLen);

    /**
     * 默认加 yyyyMM
     *
     * @param ruleCode 编码规则
     * @return
     */
    default String generateCodeYearMonth(String ruleCode) {
        return this.generateCodeYearMonth(ruleCode, 1).get(0);
    }

    /**
     * 默认加 yyyyMM
     *
     * @param ruleCode 编码规则
     * @param number   编码个数
     * @return
     */
    default List<String> generateCodeYearMonth(String ruleCode, int number) {
        return this.generateCodeYearMonth(ruleCode, number, 4);
    }

    /**
     * 默认加 yyyyMM
     *
     * @param ruleCode  编码规则
     * @param number    编码个数
     * @param mixStrLen 流水长度
     * @return
     */
    List<String> generateCodeYearMonth(String ruleCode, int number, Integer mixStrLen);

    /**
     * 默认加 yyyy
     *
     * @param ruleCode 编码规则
     * @return
     */
    default String generateCodeYear(String ruleCode) {
        return this.generateCodeYear(ruleCode, 1).get(0);
    }

    /**
     * 默认加 yyyy
     *
     * @param ruleCode 编码规则
     * @param number   编码个数
     * @return
     */
    default List<String> generateCodeYear(String ruleCode, int number) {
        return this.generateCodeYear(ruleCode, number, 4);
    }

    /**
     * 默认加 yyyy
     *
     * @param ruleCode  编码规则
     * @param number    编码个数
     * @param mixStrLen 流水长度
     * @return
     */
    List<String> generateCodeYear(String ruleCode, int number, Integer mixStrLen);

    /**
     * 没有时间
     *
     * @param ruleCode 编码规则
     * @return
     */
    default String generateCodeNotDate(String ruleCode) {
        return this.generateCodeNotDate(ruleCode, 1).get(0);
    }

    /**
     * 没有时间
     *
     * @param ruleCode 编码规则
     * @param number   编码个数
     * @return
     */
    default List<String> generateCodeNotDate(String ruleCode, int number) {
        return this.generateCodeNotDate(ruleCode, number, 4);
    }

    /**
     * 没有时间
     *
     * @param ruleCode  编码规则
     * @param number    编码个数
     * @param mixStrLen 流水长度
     * @return
     */
    List<String> generateCodeNotDate(String ruleCode, int number, Integer mixStrLen);


}
