package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.biz.crm.tpm.business.pay.sdk.enums.TpmPrepayTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 实体：活动预付明细跟踪;
 *
 * <AUTHOR> yaoyongming
 * @date : 2024-6-5
 */
@ApiModel(value = "ActivityPrepayDetailRecordVo", description = "活动预付明细跟踪")
@Data
public class ActivityPrepayDetailRecordVo extends TenantFlagOpVo {

  @ApiModelProperty("预付编号")
  private String prepayCode;

  @ApiModelProperty("预付名称")
  private String prepayName;

  @ApiModelProperty("预付明细编号")
  private String prepayDetailCode;

  @ApiModelProperty("核销申请编号")
  private String auditCode;

  @ApiModelProperty("核销明细编号")
  private String auditDetailCode;

  @ApiModelProperty("方案编码")
  private String schemeCode;

  @ApiModelProperty("方案名称")
  private String schemeName;

  @ApiModelProperty("方案明细编码")
  private String schemeDetailCode;

  @ApiModelProperty("活动大类编码")
  private String categoryCode;

  @ApiModelProperty("活动大类名称")
  private String categoryName;

  @ApiModelProperty("活动细类编码")
  private String detailCode;

  @ApiModelProperty("活动细类名称")
  private String detailName;

  @ApiModelProperty("年月")
  private String years;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("供应商编码")
  private String payeeCode;

  @ApiModelProperty("供应商名称")
  private String payeeName;

  @ApiModelProperty("申请金额")
  private BigDecimal applyAmount;

  @ApiModelProperty("实际支付金额")
  private BigDecimal prepayAmount;

  @ApiModelProperty("申请预付金额")
  private BigDecimal prepayApplyAmount;

  @ApiModelProperty("待结转金额")
  private BigDecimal prepareCarryAmount;

  @ApiModelProperty("关联结转金额")
  private BigDecimal relateCarryAmount;

  @ApiModelProperty("已结转金额")
  private BigDecimal carriedAmount;

  @ApiModelProperty("预付可冲销金额")
  private BigDecimal availableReversedAmount;

  @ApiModelProperty("已冲销金额")
  private BigDecimal reversedAmount;

  @ApiModelProperty("预付描述")
  private String description;

  @ApiModelProperty("收款行编号")
  private String lineCode;

  @ApiModelProperty("付款状态")
  private String payStatus;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("付款成功时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date paySucessDate;

  /**
   * @see TpmPrepayTypeEnum
   */
  @ApiModelProperty("预付类型")
  private String prepayType;

  @ApiModelProperty("明细")
  private List<ActivityPrepayDetailRecordItemVo> items;

  @ApiModelProperty("兑付编号")
  private String cashCode;

  @ApiModelProperty("id对兑付编码")
  private String idCashCode;

  @ApiModelProperty("本次冲抵金额")
  private BigDecimal thisReversedAmount;

}