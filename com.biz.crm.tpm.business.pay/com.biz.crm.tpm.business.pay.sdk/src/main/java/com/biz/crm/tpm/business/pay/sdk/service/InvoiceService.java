package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.InvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceInputStreamFileVo;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * 发票池(Invoice)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
public interface InvoiceService {

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    Page<InvoiceVo> findByConditions(Pageable pageable, InvoiceDto dto);

    /**
     * 通过发票主键集合查询多条数据
     */
    List<InvoiceVo> findByIds(Set<String> ids);

    /**
     * 通过发票号码查询单条数据
     *
     * @param invoiceNo 发票号码
     * @return 单条数据
     */
    InvoiceVo findByInvoiceNo(String invoiceNo);

    List<InvoiceVo> findByInvoiceNoList(Set<String> invoiceNos);


    /**
     * 新增数据
     *
     * @param Invoice 实体对象
     * @return 新增结果
     */
    InvoiceVo create(InvoiceDto Invoice);

    /**
     * 修改新据
     *
     * @param Invoice 实体对象
     * @return 修改结果
     */
    InvoiceVo update(InvoiceDto Invoice);

    /**
     * 删除数据
     *
     * @param idList 主键结合
     */
    void delete(List<String> idList);

    /**
     * 关联兑付单
     *
     * @param invoiceNos
     */
    void bindCashCode(List<String> invoiceNos, String cashCode);

    /**
     * 清除兑付单关联
     *
     * @param cashCodes
     */
    void clearCashCode(List<String> cashCodes);

    /**
     * 发票验证
     * @param invoiceNo
     */
    void checkInvoiceList(String invoiceNo);

    String uploadInvoiceFileIdentify(List<InvoiceInputStreamFileVo> fileVoList);
}
