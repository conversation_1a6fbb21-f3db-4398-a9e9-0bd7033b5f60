package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;


@ApiModel(value = "FeeCashMaterialVo", description = "采购需求信息")
@Data
@CrmExcelImport
public class FeeCashMaterialImportVo extends CrmExcelVo {

    @CrmExcelColumn("物料编码")
    private String materialCode;

    @CrmExcelColumn("物料名称")
    private String materialName;

//    @CrmExcelColumn("商品参考链接")
//    private String url;

    @CrmExcelColumn("数量")
    private String quantityStr;
    private BigDecimal quantity;

//    @CrmExcelColumn("单价")
//    private String priceStr;
    private BigDecimal price;

//    @CrmExcelColumn("金额")
//    private String amountStr;
    private BigDecimal amount;

    @CrmExcelColumn("需求到货时间")
    private String requiredDeliveryTime;

    @CrmExcelColumn("联系人")
    private String contactPerson;

    @CrmExcelColumn("联系电话")
    private String contactPhone;

    @CrmExcelColumn("邮寄地址")
    private String address;

}
