package com.biz.crm.tpm.business.pay.sdk.event.log;

import com.biz.crm.tpm.business.pay.sdk.dto.log.WithHoldingLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年07月12日 10:24:00
 */
public interface WithHoldingLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(WithHoldingLogEventDto eventDto);
  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(WithHoldingLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(WithHoldingLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdateBatch(WithHoldingLogEventDto eventDto);
  /**
   * 启用
   *
   * @param eventDto
   */
  void onEnable(WithHoldingLogEventDto eventDto);
  /**
   * 禁用
   *
   * @param eventDto
   */
  void onDisable(WithHoldingLogEventDto eventDto);
}
