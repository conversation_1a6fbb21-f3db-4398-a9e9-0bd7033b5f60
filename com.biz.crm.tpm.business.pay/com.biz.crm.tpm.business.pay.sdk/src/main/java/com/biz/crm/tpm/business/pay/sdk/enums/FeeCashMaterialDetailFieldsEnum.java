package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum FeeCashMaterialDetailFieldsEnum {
    materialCode("materialCode"),
    materialName("materialName"),
    remark("remark"),
    url("url"),
    price("price"),
    quantity("quantity"),
    amount("amount"),
    requiredDeliveryTime("requiredDeliveryTime"),
    contactPerson("contactPerson"),
    contactPhone("contactPhone"),
    address("address"),
    ;

    private String dictCode;

    public static FeeCashMaterialDetailFieldsEnum findByCode(String code) {
        Optional<FeeCashMaterialDetailFieldsEnum> first = Stream.of(FeeCashMaterialDetailFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}