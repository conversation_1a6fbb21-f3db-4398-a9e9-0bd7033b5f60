package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 费控操作类型
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/13 16:15
 */
@AllArgsConstructor
@Getter
public enum HecOperationTypeEnum {
    CREATE("CREATE", "新建"),
    UPDATE("UPDATE", "更新"),
    DELETE("DELETE", "删除"),
    RESUBMIT("RESUBMIT", "失败重新提交"),
    ;
    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String desc;

    public static HecOperationTypeEnum findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return Arrays.stream(HecOperationTypeEnum.values()).filter(o -> o.getCode().equals(code)).findFirst().orElse(null);
    }
}
