package com.biz.crm.tpm.business.pay.sdk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 参数传递dto：费用核销关联活动;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "AuditActivities", description = "费用核销关联活动")
@Getter
@Setter
public class AuditActivitiesDto implements Serializable, Cloneable {
  private static final long serialVersionUID = 3928719455471146442L;
  /**
   * 操作的预授权标记
   */
  @ApiModelProperty(name = "操作的预授权标记", notes = "操作的预授权标记", value = "操作的预授权标记", required = true)
  private String prefix;
  /**
   * 主键
   */
  @ApiModelProperty(name = "id", notes = "主键", value = "主键")
  private String id;
  /**
   * 租户编号
   */
  @ApiModelProperty(name = "tenantCode", notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /**
   * 创建人账号
   */
  @ApiModelProperty(name = "createAccount", notes = "创建人账号", value = "创建人账号")
  private String createAccount;
  /**
   * 创建人名称
   */
  @ApiModelProperty(name = "createName", notes = "创建人名称", value = "创建人名称")
  private String createName;
  /**
   * 创建时间
   */
  @ApiModelProperty(name = "createTime", notes = "创建时间", value = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesCode", notes = "活动编号", value = "活动编号")
  private String activitiesCode;
  /**
   * 活动名称
   */
  @ApiModelProperty(name = "activitiesName", notes = "活动名称", value = "活动名称")
  private String activitiesName;
  /**
   * 开始时间
   */
  @ApiModelProperty(name = "beginTime", notes = "开始时间", value = "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /**
   * 结束时间
   */
  @ApiModelProperty(name = "endTime", notes = "结束时间", value = "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /**
   * 核销申请编码
   */
  @ApiModelProperty(name = "auditCode", notes = "核销申请编码", value = "核销申请编码")
  private String auditCode;
}
