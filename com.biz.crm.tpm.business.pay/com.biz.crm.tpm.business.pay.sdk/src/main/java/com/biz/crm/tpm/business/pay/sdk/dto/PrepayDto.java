package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * 参数传递dto：活动预付;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-22
 */
@ApiModel(value = "PrepaidActivities", description = "活动预付")
@Getter
@Setter
public class PrepayDto extends TenantFlagOpDto {
  /**
   * 操作的预授权标记
   */
  @ApiModelProperty(name = "操作的预授权标记", notes = "操作的预授权标记", value = "操作的预授权标记", required = true)
  private String prefix;
  /**
   * 主键
   */
  @ApiModelProperty(name = "id", notes = "主键", value = "主键")
  private String id;
  /**
   * 预付编号
   */
  @ApiModelProperty(name = "prepayCode", notes = "预付编号", value = "预付编号")
  private String prepayCode;
  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesCode", notes = "活动编号", value = "活动编号")
  private String activitiesCode;
  /**
   * 活动明细编号
   */
  @ApiModelProperty(name = "activitiesDetailCode", notes = "活动明细编号", value = "活动明细编号")
  private String activitiesDetailCode;
  /**
   * 活动名称
   */
  @ApiModelProperty(name = "activitiesName", notes = "活动名称", value = "活动名称")
  private String activitiesName;
  /**
   * 预付金额合计
   */
  @ApiModelProperty(name = "totalPrepayAmount", notes = "预付金额", value = "预付金额")
  private BigDecimal totalPrepayAmount;
  /**
   * 预付明细
   */
  @ApiModelProperty(name = "prepayDetails", notes = "预付明细", value = "预付明细")
  private Set<PrepayDetailDto> prepayDetails;
  /**
   * 开始时间
   */
  @ApiModelProperty(name = "开始时间", notes = "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /**
   * 结束时间
   */
  @ApiModelProperty(name = "结束时间", notes = "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty(value = "工作流对象")
  private ProcessBusinessDto processBusiness;

}