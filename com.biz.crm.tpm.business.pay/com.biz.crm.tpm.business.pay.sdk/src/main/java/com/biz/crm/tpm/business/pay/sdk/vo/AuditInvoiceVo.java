package com.biz.crm.tpm.business.pay.sdk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Vo：费用核销发票;
 * <AUTHOR> Keller
 * @date : 2022-7-6
 */
@ApiModel(value = "AuditInvoice",description = "费用核销发票")
@Getter
@Setter
public class AuditInvoiceVo extends InvoiceVo {
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
  private String tenantCode;
  /** 费用核销编号 */
  @ApiModelProperty(name = "auditCode",notes = "方案编号", value= "方案编号")
  private String auditCode;
  /** 发票编号 */
  @ApiModelProperty(name = "invoiceNo",notes = "发票编号", value= "发票编号")
  private String invoiceNo;
  /** 使用金额 */
  @ApiModelProperty(name = "useAmount",notes = "使用金额", value= "使用金额")
  private BigDecimal useAmount;
  /** 创建时间 */
  @ApiModelProperty(name = "createTime",notes = "创建时间", value= "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /** 创建人名称 */
  @ApiModelProperty(name = "createName",notes = "创建人名称", value= "创建人名称")
  private String createName;
  /** 创建人账号 */
  @ApiModelProperty(name = "createAccount",notes = "创建人账号", value= "创建人账号")
  private String createAccount;
  /** 修改时间 */
  @ApiModelProperty(name = "modifyTime",notes = "修改时间", value= "修改时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date modifyTime;
  /** 修改人名称 */
  @ApiModelProperty(name = "modifyName",notes = "修改人名称", value= "修改人名称")
  private String modifyName;
  /** 修改人账号 */
  @ApiModelProperty(name = "modifyAccount",notes = "修改人账号", value= "修改人账号")
  private String modifyAccount;

}
