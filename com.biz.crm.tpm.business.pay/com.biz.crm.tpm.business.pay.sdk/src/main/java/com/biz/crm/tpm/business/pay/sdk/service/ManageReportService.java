package com.biz.crm.tpm.business.pay.sdk.service;

import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ManageReportDto;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportImportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo;

import java.util.List;

/**
 * 管报收入
 */
public interface ManageReportService {

    /**
     * 批量保存
     *
     * @param importVoList
     */
    void saveBatch(List<ManageReportImportVo> importVoList);

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    void delete(List<String> idList);

    /**
     * 按条件查询
     *
     * @param dto
     */
    List<ManageReportVo> findByDto(ManageReportDto dto);

    List<ManageReportVo> findListByCondition(WithholdingIncomeQueryDto dto);
}
