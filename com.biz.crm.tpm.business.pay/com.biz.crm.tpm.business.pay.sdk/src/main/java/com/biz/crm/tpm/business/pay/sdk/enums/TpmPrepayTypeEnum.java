package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 预付类型
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@AllArgsConstructor
@Getter
public enum TpmPrepayTypeEnum {

    activity("activity", "活动预付"),
    deduction("deduction", "账扣预付"),
    telegraphic_transfer("telegraphic_transfer", "电汇预付"),
    ;

    private String code;
    private String value;

}
