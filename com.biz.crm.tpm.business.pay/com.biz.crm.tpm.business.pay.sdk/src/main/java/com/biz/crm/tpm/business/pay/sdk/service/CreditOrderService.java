package com.biz.crm.tpm.business.pay.sdk.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.CreditOrderTicketDto;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderSapVo;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderTicketVo;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderVo;
import org.springframework.data.domain.Pageable;

import java.util.LinkedHashMap;
import java.util.List;

public interface CreditOrderService {

    /**
     * 推送sap
     * @param ids
     */
    void pushSap(List<String> ids, List<String> codes);

    /**
     * 推送sap定时任务
     */
    void pushSapTask();

    /**
     * 按编码查询票扣详情
     *
     * @return
     */
    List<CreditOrderTicketVo> findByCode(String code);

    /**
     * 按条件查询票扣明细
     *
     * @return
     */
    List<CreditOrderTicketVo> findByDtoList(List<CreditOrderTicketVo>  dtoList);

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    Page<CreditOrderTicketVo> findByConditions(Pageable pageable, CreditOrderTicketDto dto);

    /**
     * 费用计提合计
     *
     * @param params
     * @return
     */
    CreditOrderVo findTotalByConditions(LinkedHashMap<String, Object> params);

    /**
     * SAP回传贷项订单付款状态
     *
     * @param dtoList
     */
    void updateCreditOrderTicketStatus(List<CreditOrderSapVo> dtoList);
}
