package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 兑付方式
 */
@AllArgsConstructor
@Getter
public enum CashMethodEnum {
  WIRE_TRANSFER("wire_transfer", "wire_transfer","CASH", "电汇", "1"),
  DEDUCTIONS("deductions", "deductions", "ACCOUNT", "账扣", "2"),
  TICKET_BUCKLE("ticket_buckle", "ticket_buckle", "INVOICE", "票扣", "3"),
  REPLENISHMENT("replenishment", "replenishment", "ORDER", "货补", "4"),
  CLOSE("close", "close", "CLOSE", "关闭", "5")
  ;
  /** 系统key */
  private String key;
  /** 字典编码 */
  private String dictCode;
  /**
   * 费控编码
   */
  private String hecCode;
  /** 描述 */
  private String value;
  /** 字典排序 */
  private String order;

  public static CashMethodEnum findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    return Arrays.stream(CashMethodEnum.values()).filter(o->o.getDictCode().equals(code)).findFirst().orElse(null);
  }
}
