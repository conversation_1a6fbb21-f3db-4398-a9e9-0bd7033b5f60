package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "CreditOrderTicket", description = "贷项订单票扣明细")
@Data
public class CreditOrderTicketDto extends TenantFlagOpDto {

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectsCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectsName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("销售单位")
    private String saleUnit;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("核销申请名称")
    private String auditName;

    @ApiModelProperty("核销申请编号")
    private String auditCode;

    @ApiModelProperty("批次号")
    private String btNo;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("贷项订单编码")
    private String creditCode;

    @ApiModelProperty("行号")
    private Integer lineNumber;

    @ApiModelProperty("是否已调整Y/N")
    private String beAdjusted;

}
