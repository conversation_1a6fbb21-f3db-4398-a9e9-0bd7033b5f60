package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe: 管报收入
 * @createTime 2022年06月25日 10:19:00
 */
@ApiModel("管报收入")
@Data
@CrmExcelImport(startRow = 4)
public class ManageReportImportVo extends CrmExcelVo {

  @CrmExcelColumn("收入年月")
  private String yearMonthLy;

  @CrmExcelColumn("公司编码")
  private String companyCode;

  @CrmExcelColumn("部门编码")
  private String departmentCode;

  @CrmExcelColumn("部门名称")
  private String departmentName;
  private Integer levelNum;

  @CrmExcelColumn("成本中心编码")
  private String costCenterCode;

  @CrmExcelColumn("成本中心名称")
  private String costCenterName;

  @CrmExcelColumn("客户ERP编号")
  private String erpCode;
  private String customerCode;

  @CrmExcelColumn("客户名称")
  private String customerName;

  @CrmExcelColumn("品项编码")
  private String itemCode;

  @CrmExcelColumn("品项名称")
  private String itemName;

  @CrmExcelColumn("产品编码")
  private String productCode;

  @CrmExcelColumn("产品名称")
  private String productName;

  @CrmExcelColumn("销售数量")
  private String quantityStr;
  private BigDecimal quantity;

  @CrmExcelColumn("销售金额")
  private String amountStr;
  private BigDecimal amount;
}
