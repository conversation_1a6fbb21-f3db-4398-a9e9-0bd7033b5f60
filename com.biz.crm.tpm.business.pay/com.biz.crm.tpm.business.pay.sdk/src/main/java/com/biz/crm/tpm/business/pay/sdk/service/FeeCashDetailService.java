package com.biz.crm.tpm.business.pay.sdk.service;


import com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheService;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTocDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;

import java.util.List;

public interface FeeCashDetailService extends BusinessPageCacheService<FeeCashDetailVo, FeeCashDetailDto> {

    /**
     * 是否展示TOC
     *
     * @param cacheKeyDetail
     * @param payeeErpCode
     * @return
     */
    String showToc(String cacheKeyDetail, String payeeErpCode);

    /**
     * 是否展示TOC
     *
     * @param cacheList
     * @param payeeErpCode
     * @return
     */
    String showToc(List<FeeCashDetailDto> cacheList, String payeeErpCode);

    /**
     * 按客户+结案单号查询兑付单
     *
     * @param auditCodes
     * @param customerCodes
     * @return
     */
    List<FeeCashDetailVo> findByAuditCustomer(List<String> auditCodes, List<String> customerCodes);
}
