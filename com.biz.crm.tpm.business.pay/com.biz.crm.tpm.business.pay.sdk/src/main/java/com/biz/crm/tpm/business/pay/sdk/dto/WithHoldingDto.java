package com.biz.crm.tpm.business.pay.sdk.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe: 费用预提Dto
 * @createTime 2022年06月25日 16:01:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WithHoldingDto extends TenantFlagOpDto {


  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /**
   * 推送状态 枚举
   */
  @ApiModelProperty(name = "推送费控状态", notes = "推送费控状态")
  private String pushStatus;

  /**
   * 预提类型 枚举
   */
  @ApiModelProperty(name = "预提类型", notes = "预提类型")
  private String withHoldingType;

  /**
   * 预提编号
   */
  @ApiModelProperty(name = "预提编号",notes = "预提编号")
  private String withHoldingCode;

  /**
   * 预提年月
   */
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM")
  @DateTimeFormat(pattern = "yyyy-MM")
  private Date withHoldingYears;

  @ApiModelProperty("审批通过日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date passDate;

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  private String activitiesName;

  /**
   * 活动明细编码
   */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value= "活动明细编码")
  private String activitiesDetailCode;

  /**
   * 预算科目编码
   */
  @ApiModelProperty(name = "预算科目编码",notes = "")
  private String budgetSubjectsCode;

  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "预算科目名称",notes = "")
  private String budgetSubjectsName;

  /**
   * 活动大类编码
   */
  @ApiModelProperty(name = "活动大类编码", notes = "活动大类编码")
  private String costTypeCategoryCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "活动大类名称", notes = "活动大类名称")
  private String costTypeCategoryName;

  /**
   * 活动细类编码
   */
  @ApiModelProperty(name = "活动细类编码", notes = "活动细类编码")
  private String costTypeDetailCode;

  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "活动细类名称", notes = "活动系类名称")
  private String costTypeDetailName;

  /**
   * 组织编号
   */
  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  private String orgName;


  /**
   * 客户编号
   */
  @ApiModelProperty(name = "客户编号", notes = "客户编号")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty(name = "客户名称", notes = "客户名称")
  private String customerName;

  /**
   * 门店编号
   */
  @ApiModelProperty(name = "门店编号", notes = "门店编号")
  private String terminalCode;

  /**
   * 门店名称
   */
  @ApiModelProperty(name = "门店名称", notes = "门店名称")
  private String terminalName;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "申请金额", notes = "申请金额")
  private BigDecimal applyAmount;

  /**
   * 预提金额
   */
  @ApiModelProperty(name = "预提金额", notes = "预提金额")
  private BigDecimal withHoldingAmount;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "支付方式", notes = "支付方式")
  private String payBy;

  /**
   * 上账金额
   */
  @ApiModelProperty(name = "上账金额", notes = "上账金额")
  private BigDecimal accountAmount;


  /** 开始时间 */
  @ApiModelProperty(name = "beginTime",notes = "开始时间", value= "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /** 结束时间 */
  @ApiModelProperty(name = "endTime",notes = "结束时间", value= "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty("活动描述")
  private String actDesc;



  @ApiModelProperty("费用归属年月")
  private String years;

  @ApiModelProperty("预提年月")
  private String yearMonthLy;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("订单编码")
  private String orderCode;

  @ApiModelProperty("订单名称")
  private String orderName;

  @ApiModelProperty("归属部门编码")
  private String belongDepartmentCode;

  @ApiModelProperty("归属部门名称")
  private String belongDepartmentName;

  @ApiModelProperty("承担部门编码")
  private String bearDepartmentCode;

  @ApiModelProperty("承担部门名称")
  private String bearDepartmentName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("计提来源")
  private String withHoldingSource;

  @ApiModelProperty("管报计提金额")
  private BigDecimal withHoldingReportAmount;

  @ApiModelProperty("调减金额")
  private BigDecimal reduceAmount;

  @ApiModelProperty("实际金额")
  private BigDecimal actualAmount;

  @ApiModelProperty("管报实际金额（未税）")
  private BigDecimal noTaxActualReportAmount;

  @ApiModelProperty("公式及取值")
  private String formulaInfo;

  @ApiModelProperty("凭证号")
  private String voucherCode;

  @ApiModelProperty("失败原因")
  private String failMsg;

  @ApiModelProperty("审批状态")
  private String status;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("预算编码")
  private String budgetCode;

  @ApiModelProperty("费控单号")
  private String externalCode;

  @ApiModelProperty("业务编码")
  private String businessCode;

  @ApiModelProperty("管报计提调减金额")
  private BigDecimal withHoldingReportReduceAmount;

  @ApiModelProperty("管报实际金额")
  private BigDecimal actualReportAmount;

  @ApiModelProperty("汇总单号")
  private String collectCode;

  @ApiModelProperty("是否当期费用(Y/N)")
  private String beThisFee;

  @ApiModelProperty("推送费控单号")
  private String pushHecCode;

  @ApiModelProperty("手动冲销查询-Y")
  private String manualWriteOff;

  @ApiModelProperty("是否进行过调整(Y/N)")
  private String beAdjust;

  @ApiModelProperty("方案创建人名称")
  private String schemeCreateName;

  @ApiModelProperty("是否选中")
  private String checked;

  private String cacheKey;

  @ApiModelProperty("费用归属年月开始")
  private String yearsStart;

  @ApiModelProperty("费用归属年月结束")
  private String yearsEnd;

  @ApiModelProperty("确认状态")
  private String confirmStatus;

  @ApiModelProperty("部门编码")
  private String departmentCode;

  @ApiModelProperty("费率")
  private BigDecimal ratio;

  @ApiModelProperty("费率")
  private String ratioStr;

}
