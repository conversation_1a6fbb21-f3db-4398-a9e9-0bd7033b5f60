package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "FeeCashToc", description = "TOC支付明细")
@Data
@CrmExcelImport(startRow = 4)
public class FeeCashTocImportVo extends CrmExcelVo {

    @CrmExcelColumn("打款平台")
    private String platform;

    @CrmExcelColumn("姓名")
    private String payeeName;

    @CrmExcelColumn("银行账号")
    private String bankNo;

    @CrmExcelColumn("身份证号码")
    private String idCard;

    @CrmExcelColumn("手机号")
    private String phone;

    @CrmExcelColumn("收款金额")
    private String payeeAmountStr;
    private BigDecimal payeeAmount;

    @CrmExcelColumn("实际打款金额")
    private String payAmountStr;
    private BigDecimal payAmount;

    @CrmExcelColumn("备注")
    private String remark;

    private String id;
}
