package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/26 17:37
 **/
@Getter
@AllArgsConstructor
public enum WithholdingReportTypeEnum {

    CUSTOMER_GAINS_LOSSES("customer_gains_losses", "客户损益报表"),
    DEPARTMENT_ESTIMATION("department_estimation", "三级部门损益预测"),
    PRODUCT_PHASE_ESTIMATION("product_phase_estimation", "品相损益预测"),
    MARKETING_ESTIMATION("estimation", "营销损益预测"),
    WITHHOLDING_DETAIL("withholding_detail", "计提明细"),
    GIFT_SURROUND("gift_surround", "搭赠及周边"),
    AUDITED_CASHED_DETAIL("audited_cashed_detail", "已结案兑付"),
    ;


    private String code;

    private String desc;
}
