package com.biz.crm.tpm.business.pay.sdk.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单达成统计：
 * 送达方-组织 映射关系
 *
 * @Author: yangrui
 * @Date: 2024-12-02 13:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDestinationOrgMapping {
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 送达方编码
     */
    private String destinationCode;
    /**
     * 组织编码
     */
    private String orgCode;

    public CustomerDestinationOrgMapping(String destinationCode, String orgCode) {
        this.destinationCode = destinationCode;
        this.orgCode = orgCode;
    }
}
