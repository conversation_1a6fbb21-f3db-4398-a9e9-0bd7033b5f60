package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "FeeCashTicket", description = "费用票扣明细")
@Data
@CrmExcelImport
public class FeeCashTicketImportVo extends CrmExcelVo {

    private String id;

    @CrmExcelColumn("年月")
    private String years;

    @CrmExcelColumn("预算科目编码")
    private String budgetSubjectsCode;

    @CrmExcelColumn("预算科目名称")
    private String budgetSubjectsName;

    @CrmExcelColumn("公司编码")
    private String companyCode;

    @CrmExcelColumn("客户ERP编码")
    private String erpCode;
    private String customerCode;

    @CrmExcelColumn("客户名称")
    private String customerName;

    @CrmExcelColumn("产品编码")
    private String productCode;

    @CrmExcelColumn("产品名称")
    private String productName;

//    @CrmExcelColumn("销售单位")
    private String saleUnit;

//    @CrmExcelColumn("数量")
    private BigDecimal quantity;

//    @CrmExcelColumn("单价")
    private BigDecimal price;

    @CrmExcelColumn("金额")
    private String amountStr;
    private BigDecimal amount;

}
