package com.biz.crm.tpm.business.pay.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@ApiModel(value = "CreditOrderTicket", description = "贷项订单票扣明细")
@Data
public class CreditOrderTicketSapVo {

    @ApiModelProperty("开票状态")
    private String TICKETSTATUS;

    @ApiModelProperty("未开票金额")
    private BigDecimal AMOUNT;

    @ApiModelProperty("行号")
    private Integer LINENUMBER;

}
