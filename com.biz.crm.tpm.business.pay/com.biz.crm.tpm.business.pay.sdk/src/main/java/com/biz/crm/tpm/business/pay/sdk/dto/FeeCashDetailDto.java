package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "FeeCashDetail", description = "费用兑付明细")
@Data
public class FeeCashDetailDto extends TenantFlagOpDto {

    @ApiModelProperty("兑付名称")
    private String cashName;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("兑付明细编号")
    private String cashDetailCode;

    @ApiModelProperty("核销申请编号")
    private String auditCode;

    @ApiModelProperty("核销明细编号")
    private String auditDetailCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("核销金额")
    private BigDecimal auditAmount;

    @ApiModelProperty("供应商编码")
    private String payeeCode;

    @ApiModelProperty("供应商Erp编码")
    private String payeeErpCode;

    @ApiModelProperty("供应商名称")
    private String payeeName;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("已兑付金额")
    private BigDecimal cashAmount;

    @ApiModelProperty("可兑付金额")
    private BigDecimal availableCashAmount;

    @ApiModelProperty("预付可冲销金额")
    private BigDecimal availableReversedAmount;

    @ApiModelProperty("本次冲销预付金额")
    private BigDecimal thisReversedAmount;

    @ApiModelProperty("本次兑付金额")
    private BigDecimal thisCashAmount;

    @ApiModelProperty("是否完全兑付")
    private String beCash;

    @ApiModelProperty("是否完全核销")
    private String beFullAudit;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("活动描述")
    private String actDesc;

    @ApiModelProperty("结案完结日期")
    private String auditDate;

    @ApiModelProperty("紧急程度")
    private String urgency;

    @ApiModelProperty("需求紧急原因")
    private String urgencyReason;

    @ApiModelProperty("合作类型")
    private String cooperateType;

    @ApiModelProperty("本次兑付税额")
    private BigDecimal thisCashTaxAmount;

    @ApiModelProperty("本次兑付未税金额")
    private BigDecimal thisCashNoTaxAmount;

    @ApiModelProperty("实付金额")
    private BigDecimal actualPayAmount;


    /**
     * 是否选中，0否1是
     */
    @ApiModelProperty("是否选中，0否1是")
    private String checked;
    private String cacheKey;
    @ApiModelProperty("发票")
    private List<FeeCashInvoiceDto> invoiceList;


    @ApiModelProperty("客户搜索项")
    private String searchName;


    @ApiModelProperty("归属部门编码")
    private List<String> belongDepartmentCodes;


    @ApiModelProperty("票据类型")
    private String billType;

    @ApiModelProperty("收据说明")
    private String receiptDescription;


}
