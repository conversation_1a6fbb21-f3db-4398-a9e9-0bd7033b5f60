package com.biz.crm.tpm.business.pay.sdk.service;


import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayVo;

import java.util.List;

/**
 * 活动预付
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/15 20:35
 */
public interface ActivityPrepaySendHecService {


    /**
     * 新增并提交
     *
     * @param dto dto对象
     * @return
     */
    Result<String> submitCreate(ActivityPrepayDto dto, String cacheKey);

    /**
     * 修改并提交
     *
     * @param dto dto对象
     * @return
     */
    Result<String> submitUpdate(ActivityPrepayDto dto, String cacheKey);

    /**
     * 批量提交
     *
     * @param idList 主键结合
     * @return
     */
    Result<String> submitBatch(List<String> idList);

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return
     */
    Result<?> delete(List<String> idList);

    Result<String> prepaySendHec(List<ActivityPrepayDto> sendHecDtoList);

    void modifyActivityPrepayRecord(List<ActivityPrepayVo> entityList);
}
