package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 计提余额数据查询DTO
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
@Data
@ApiModel(value = "WithHoldingBalanceDto", description = "计提余额查询条件")
public class WithHoldingBalanceDto extends TenantFlagOpDto {

    @ApiModelProperty("预提编号")
    @JsonProperty("with_holding_code")
    private String withHoldingCode;

    @ApiModelProperty("预提年月")
    @JsonProperty("year_month_ly")
    private String yearMonthLy;

    @ApiModelProperty("费用归属年月")
    @JsonProperty("years")
    private String years;

    @ApiModelProperty("活动明细编码")
    @JsonProperty("activities_detail_code")
    private String activitiesDetailCode;

    @ApiModelProperty("活动编号")
    @JsonProperty("activities_code")
    private String activitiesCode;

    @ApiModelProperty("活动名称")
    @JsonProperty("activities_name")
    private String activitiesName;

    @ApiModelProperty("订单编码")
    @JsonProperty("order_code")
    private String orderCode;

    @ApiModelProperty("订单名称")
    @JsonProperty("order_name")
    private String orderName;

    @ApiModelProperty("活动大类编码")
    @JsonProperty("cost_type_category_code")
    private String costTypeCategoryCode;

    @ApiModelProperty("活动大类名称")
    @JsonProperty("cost_type_category_name")
    private String costTypeCategoryName;

    @ApiModelProperty("活动细类编码")
    @JsonProperty("cost_type_detail_code")
    private String costTypeDetailCode;

    @ApiModelProperty("活动细类名称")
    @JsonProperty("cost_type_detail_name")
    private String costTypeDetailName;

    @ApiModelProperty("预算科目编码")
    @JsonProperty("budget_subjects_code")
    private String budgetSubjectsCode;

    @ApiModelProperty("预算科目名称")
    @JsonProperty("budget_subjects_name")
    private String budgetSubjectsName;

    @ApiModelProperty("归属部门编码")
    @JsonProperty("belong_department_code")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    @JsonProperty("belong_department_name")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    @JsonProperty("bear_department_code")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    @JsonProperty("bear_department_name")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    @JsonProperty("cost_center_code")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @JsonProperty("cost_center_name")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    @JsonProperty("customer_code")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @JsonProperty("customer_name")
    private String customerName;

    @ApiModelProperty("品项名称")
    @JsonProperty("item_name")
    private String itemName;

    @ApiModelProperty("预提类型")
    @JsonProperty("with_holding_type")
    private String withHoldingType;

    @ApiModelProperty("兑付方式")
    @JsonProperty("pay_by")
    private String payBy;

    @ApiModelProperty(value = "排序字段", notes = "格式：字段名,排序方式(asc/desc)，如：year_month_ly,desc。支持排序的字段：year_month_ly、years、apply_amount_total、with_holding_amount_total、write_off_amount_total、balance")
    @JsonProperty("sort")
    private String sort;

    @ApiModelProperty(value = "每页条数", notes = "每页条数")
//    @JsonProperty("page_size")
    private Integer pageSize;

    @ApiModelProperty(value = "页码", notes = "页码")
//    @JsonProperty("page_num")
    private Integer pageNum;
}
