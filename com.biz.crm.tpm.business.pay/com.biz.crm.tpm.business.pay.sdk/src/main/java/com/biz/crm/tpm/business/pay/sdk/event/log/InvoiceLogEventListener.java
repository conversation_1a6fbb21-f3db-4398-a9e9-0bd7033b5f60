package com.biz.crm.tpm.business.pay.sdk.event.log;

import com.biz.crm.tpm.business.pay.sdk.dto.log.InvoiceLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 发票池 业务日志监听
 * <AUTHOR> <PERSON>
 * @date : 2022-7-1
 */
public interface InvoiceLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(InvoiceLogEventDto eventDto);
  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(InvoiceLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(InvoiceLogEventDto eventDto);
  /**
   * 启用
   *
   * @param eventDto
   */
  void onEnable(InvoiceLogEventDto eventDto);
  /**
   * 禁用
   *
   * @param eventDto
   */
  void onDisable(InvoiceLogEventDto eventDto);
}
