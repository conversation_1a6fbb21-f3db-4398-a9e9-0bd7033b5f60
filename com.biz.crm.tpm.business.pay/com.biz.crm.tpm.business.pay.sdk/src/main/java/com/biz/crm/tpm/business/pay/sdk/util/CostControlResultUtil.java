package com.biz.crm.tpm.business.pay.sdk.util;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.vo.CostControlResult;
import com.biz.crm.business.common.sdk.model.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/4 16:20
 */
public class CostControlResultUtil {


    private static final List<String> SUCCESSList = Lists.newArrayList("S");

    public static final String DATA = "data";

    public static final String ERR_MSG = "err_msg";


    /**
     * 返回json字符串
     *
     * @param result
     * @return
     */
    public static String validateResult(Result<String> result) {
        Assert.isTrue(result.isSuccess(), "费控返回提示:" + result.getMessage());
        Assert.hasLength(result.getResult(), "费控返回信息为空:" + result.getMessage());
        CostControlResult costControlResult = JSONObject.parseObject(result.getResult(), CostControlResult.class);
        Assert.isTrue(SUCCESSList.contains(costControlResult.getStatus()), "费控返回提示:" + costControlResult.getMessage());
        return JSONObject.toJSONString(costControlResult.getResult());
    }

    public static Map<String, String> validateOcrResult(Result<String> result) {
        Assert.isTrue(result.isSuccess(), "费控返回提示:" + result.getMessage());
        Assert.hasLength(result.getResult(), "费控返回信息为空:" + result.getMessage());
        CostControlResult costControlResult = JSONObject.parseObject(result.getResult(), CostControlResult.class);
        Map<String, String> map = Maps.newHashMap();
        map.put(DATA, JSONObject.toJSONString(costControlResult.getResult()));
        //判断如果不存在
        if (!SUCCESSList.contains(costControlResult.getStatus())) {
            map.put(ERR_MSG, "费控返回提示:" + costControlResult.getMessage());
        }
        return map;
    }
}
