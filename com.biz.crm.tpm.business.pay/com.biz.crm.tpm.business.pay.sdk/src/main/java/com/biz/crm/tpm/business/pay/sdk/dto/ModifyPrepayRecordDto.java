package com.biz.crm.tpm.business.pay.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@ApiModel("预付明细mq vo")
public class ModifyPrepayRecordDto {

    @ApiModelProperty("预提审批通过")
    private String withholdingJson;

    @ApiModelProperty("完全结案")
    private String allEndCaseJson;

    @ApiModelProperty("预付审批结束")
    private String prepayJson;
}
