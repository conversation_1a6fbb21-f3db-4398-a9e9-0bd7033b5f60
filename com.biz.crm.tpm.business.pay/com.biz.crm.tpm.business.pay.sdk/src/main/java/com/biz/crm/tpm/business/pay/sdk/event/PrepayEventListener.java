package com.biz.crm.tpm.business.pay.sdk.event;

import com.biz.crm.tpm.business.pay.sdk.vo.PrepayVo;

/**
 * 活动预付;(tpm_prepay_activities)相关的事件通知
 *
 * <AUTHOR> Keller
 * @date : 2022-6-22
 */
public interface PrepayEventListener {

  /**
   * 当活动预付数据被创建时，该事件被触发
   *
   * @param prepayVo
   */
  void onCreated(PrepayVo prepayVo);

  /**
   * 当活动预付数据被修改时，该事件被触发
   *
   * @param oldPrepayVo 修改前数据
   * @param prepayVo    修改后数据
   */
  void onUpdate(PrepayVo oldPrepayVo, PrepayVo prepayVo);

  /**
   * 当活动预付数据被删除时（逻辑删除），该事件被触发
   *
   * @param prepayVo
   */
  void onDeleted(PrepayVo prepayVo);

  /**
   * 当活动预付审批状态变更，触发事件
   *
   * @param prepayVo
   * @param processStatus
   */
  void onUpdateProcessStatus(PrepayVo prepayVo, String processStatus);
}
