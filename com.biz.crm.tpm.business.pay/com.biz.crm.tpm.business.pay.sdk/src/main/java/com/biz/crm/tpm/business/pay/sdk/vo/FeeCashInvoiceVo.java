package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 实体：费用兑付发票;
 * <AUTHOR> Keller
 * @date : 2022-7-6
 */
@ApiModel(value = "FeeCashInvoice",description = "费用兑付发票")
@Data
public class FeeCashInvoiceVo extends TenantFlagOpVo {

  @ApiModelProperty("兑付名称")
  private String cashName;

  @ApiModelProperty("兑付编号")
  private String cashCode;

  @ApiModelProperty("核销明细编号")
  private String auditDetailCode;

  /** 发票编号 */
  @ApiModelProperty(name = "invoiceNo",notes = "发票编号", value= "发票编号")
  private String invoiceNo;

  /** 发票明细编号 */
  @ApiModelProperty("发票明细号")
  private String invoiceDetailNo;

  /** 使用金额 */
  @ApiModelProperty(name = "useAmount",notes = "使用金额", value= "使用金额")
  private BigDecimal useAmount;

  @ApiModelProperty("货物或应税劳务名称")
  private String name;

  @ApiModelProperty("税价合计")
  private BigDecimal priceAndTax;

  @ApiModelProperty("不含税金额")
  private BigDecimal amountWithoutTax;

  @ApiModelProperty("税额")
  private BigDecimal taxAmount;

  @ApiModelProperty("发票类型")
  private String type;

  @ApiModelProperty("开票日期")
  private String billingDate;

  @ApiModelProperty("校验码(只允许填写数字和字母)")
  private String checkCode;

  @ApiModelProperty("发票代码")
  private String code;

  @ApiModelProperty("税率")
  private BigDecimal taxRate;

  @ApiModelProperty("购买方名称")
  private String purchaser;

  @ApiModelProperty("购买方税号")
  private String pNo;

  @ApiModelProperty("购买方开户行及账号")
  private String pBankAndAccount;

  @ApiModelProperty("购买方地址电话")
  private String pAddressAndPhone;

  @ApiModelProperty("销售方名称")
  private String seller;

  @ApiModelProperty("销售方税号")
  private String sNo;

  @ApiModelProperty("销售方开户行及账号")
  private String sBankAndAccount;

  @ApiModelProperty("销售方地址电话")
  private String sAddressAndPhone;

  @ApiModelProperty("是否验证")
  private String checked;

  @ApiModelProperty("校验信息")
  private String checkMsg;

  @ApiModelProperty("文件id")
  private String fileCode;

  @ApiModelProperty("实际报销金额")
  private BigDecimal reimbursementAmount;

  @ApiModelProperty("剩余可报销金额")
  private BigDecimal availableReimbursementAmount;

  @ApiModelProperty("活动细类编码")
  private String detailCode;

  @ApiModelProperty("活动细类名称")
  private String detailName;

  @ApiModelProperty("兑付明细编号")
  private String cashDetailCode;

  @ApiModelProperty("税价合计汇总")
  private BigDecimal priceAndTaxTotal;
  @ApiModelProperty("实际报销金额汇总")
  private BigDecimal reimbursementAmountTotal;

}