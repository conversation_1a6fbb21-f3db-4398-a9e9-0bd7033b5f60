package com.biz.crm.tpm.business.pay.sdk.event.log;

import com.biz.crm.tpm.business.pay.sdk.dto.log.AccountLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 费用上账主表 业务日志监听
 * <AUTHOR> <PERSON>
 * @date : 2022-7-1
 */
public interface AccountLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(AccountLogEventDto eventDto);
  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(AccountLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(AccountLogEventDto eventDto);
  /**
   * 启用
   *
   * @param eventDto
   */
  void onEnable(AccountLogEventDto eventDto);
  /**
   * 禁用
   *
   * @param eventDto
   */
  void onDisable(AccountLogEventDto eventDto);
}
