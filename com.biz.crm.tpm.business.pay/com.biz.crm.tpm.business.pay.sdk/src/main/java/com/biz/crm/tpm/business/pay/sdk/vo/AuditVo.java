package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Vo：费用核销;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "Audit", description = "费用核销")
@Getter
@Setter
public class AuditVo implements Serializable {
  private static final long serialVersionUID = -5325679193605699436L;
  /**
   * 主键
   */
  @ApiModelProperty(name = "id", notes = "主键", value = "主键")
  private String id;
  /**
   * 租户编号
   */
  @ApiModelProperty(name = "tenantCode", notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /**
   * 备注
   */
  @ApiModelProperty(name = "remark", notes = "备注", value = "备注")
  private String remark;
  /**
   * 修改人名称
   */
  @ApiModelProperty(name = "modifyName", notes = "修改人名称", value = "修改人名称")
  private String modifyName;
  /**
   * 修改人账号
   */
  @ApiModelProperty(name = "modifyAccount", notes = "修改人账号", value = "修改人账号")
  private String modifyAccount;
  /**
   * 修改时间
   */
  @ApiModelProperty(name = "modifyTime", notes = "修改时间", value = "修改时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date modifyTime;
  /**
   * 创建人名称
   */
  @ApiModelProperty(name = "createName", notes = "创建人名称", value = "创建人名称")
  private String createName;
  /**
   * 创建人账号
   */
  @ApiModelProperty(name = "createAccount", notes = "创建人账号", value = "创建人账号")
  private String createAccount;
  /**
   * 创建时间
   */
  @ApiModelProperty(name = "createTime", notes = "创建时间", value = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /**
   * 核销申请名称
   */
  @ApiModelProperty(name = "auditName", notes = "核销申请名称", value = "核销申请名称")
  private String auditName;
  /**
   * 核销申请编号
   */
  @ApiModelProperty(name = "auditCode", notes = "核销申请编号", value = "核销申请编号")
  private String auditCode;
  /**
   * 核销金额汇总
   */
  @ApiModelProperty(name = "totalApplyAmount", notes = "核销金额汇总", value = "核销金额汇总")
  private BigDecimal totalApplyAmount;
  /**
   * 费用核销活动信息
   */
  @ApiModelProperty(name = "auditActivities", notes = "费用核销活动信息", value = "费用核销活动信息")
  private List<AuditActivitiesVo> auditActivities;
  /**
   * 费用核销明细信息
   */
  @ApiModelProperty(name = "auditDetails", notes = "费用核销明细信息", value = "费用核销明细信息")
  private List<AuditDetailVo> auditDetails;
  /**
   * 费用核销附件信息
   */
  @ApiModelProperty(name = "auditFiles", notes = "费用核销明细信息", value = "费用核销明细信息")
  private List<AuditFilesVo> auditFiles;
  /**
   * 自动核销
   */
  @ApiModelProperty(name = "isAutoAudit", notes = "自动核销", value = "自动核销")
  private String isAutoAudit;
  /**
   * 核销发票信息
   */
  @ApiModelProperty(value = "核销发票信息")
  private List<AuditInvoiceVo> auditInvoices;

  /**
   * 活动明细
   */
  @ApiModelProperty("普通活动明细")
  private Map<String, List<BaseActivityItemVo>> items;
}