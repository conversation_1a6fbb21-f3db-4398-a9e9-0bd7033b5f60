package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 实体：活动预付明细;
 *
 * <AUTHOR> ya<PERSON><PERSON>ming
 * @date : 2024-6-4
 */
@ApiModel(value = "ActivityPrepayDetailVo", description = "活动预付明细")
@Data
@CrmExcelImport
public class ActivityPrepayDetailImportVo extends CrmExcelVo {

  @CrmExcelColumn("方案明细编码")
  private String schemeDetailCode;

  @CrmExcelColumn("方案编码")
  private String schemeCode;

  @CrmExcelColumn("方案名称")
  private String schemeName;

  @CrmExcelColumn("活动细类名称")
  private String detailName;

  @CrmExcelColumn("年月")
  private String years;

  @CrmExcelColumn("客户编码")
  private String customerCode;

  @CrmExcelColumn("客户名称")
  private String customerName;

  @CrmExcelColumn("成本中心名称")
  private String costCenterName;

  @CrmExcelColumn("品项名称")
  private String itemName;

  @CrmExcelColumn("申请金额")
  private String applyAmountStr;

  @CrmExcelColumn("已预付金额")
  private String prepayAmountStr;

  @CrmExcelColumn("预付可冲销金额")
  private String availableReversedAmountStr;

  @CrmExcelColumn("兑付金额")
  private String cashAmountStr;

  @CrmExcelColumn("可预付金额")
  private String availablePrepayAmountStr;

  @CrmExcelColumn("预付申请金额")
  private String thisPrepayAmountStr;
  private BigDecimal thisPrepayAmount;

  @CrmExcelColumn("本次支付金额")
  private String thisPayAmountStr;

  @CrmExcelColumn("关联结转金额")
  private String relateCarryAmountStr;
}