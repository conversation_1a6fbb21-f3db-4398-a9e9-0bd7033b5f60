package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectVo;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 费用核销明细;(tpm_audit_detail)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
public interface AuditDetailService {
  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<AuditDetailVo> findByConditions(Pageable pageable, AuditDetailDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AuditDetailVo findById(String id);

  /**
   * 通过核销明细编号查询单条数据
   *
   * @param auditDetailCode 核销明细编号
   * @return 单挑数据
   */
  AuditDetailVo findByAuditDetailCode(String auditDetailCode);

  /**
   * 通过费用核销编号查询单条数据
   *
   * @param auditCode 费用核销编号
   * @return 多条数据
   */
  List<AuditDetailVo> findByAuditCode(String auditCode);

  /**
   * 新增数据
   *
   * @param auditDetailDto 实体对象
   * @return 新增结果
   */
  AuditDetailVo create(AuditDetailDto auditDetailDto);

  /**
   * 新增数据
   *
   * @param auditDetailDtos 实体对象
   * @return 新增结果
   */
  List<AuditDetailVo> createBatch(List<AuditDetailDto> auditDetailDtos);

  /**
   * 修改新据
   *
   * @param auditDetailDto 实体对象
   * @return 修改结果
   */
  AuditDetailVo update(AuditDetailDto auditDetailDto);

  /**
   * 批量更新
   *
   * @param auditDetailDtos
   * @return
   */
  List<AuditDetailVo> updateBatch(List<AuditDetailDto> auditDetailDtos);

  /**
   * 更新活动明细名称
   *
   * @param costTypeDetailCode
   * @param costTypeDetailName
   */
  void updateCostTypeDetailName(String costTypeDetailCode, String costTypeDetailName);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(List<String> ids);

  /**
   * 根据核销编号删除核销明细信息
   *
   * @param auditCode
   */
  void deleteByAuditCode(String auditCode);

  /**
   * 根据核销编号删除核销明细信息
   *
   * @param auditCodes
   */
  void deleteByAuditCodes(Collection<String> auditCodes);

  /**
   * 根据核销明细编号进行退费
   *
   * @param auditDetailCode
   */
  void doRefund(String auditDetailCode);

  /**
   * 获取在核销状态中的活动细类编号,并根据活动细类编号过滤
   *
   * @return
   */
  Set<String> findActivitiesDetailByAuditing(Set<String> costTypeDetailCodes);

  /**
   * 获取在核销状态中的活动细类编号,并根据活动细类编号过滤
   *
   * @return
   */
  Set<String> findActivitiesDetailByAudited();

  /**
   * 根据在核销中为完全核销的活动获取活动细类编号
   *
   * @return
   */
  Set<String> findActivitiesDetailByFullAudit();

  /**
   * 查看核销执行明细数据
   *
   * @param activitiesDetailCode
   * @return
   */
  List<ActivitiesDetailCollectVo> findDetailCollectByActivitiesDetailCode(String activitiesDetailCode);

  /**
   * 统计核销次数
   * @param costTypeDetailCode
   * @param activitiesDetailCode
   * @return
   */
  Integer countByCostTypeAndActivitiesDetail(String costTypeDetailCode, String activitiesDetailCode);
}