package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @describe: 上账类型枚举
 * @createTime 2022年06月16日 19:43:00
 */
@AllArgsConstructor
@Getter
public enum SapOrderTypeEnum {

  /** 上账类型枚举 */
  ZDR1("ZDR1", "ZDR1", "借项订单"),
  ZCR1("ZCR1", "ZCR1", "贷项订单"),
      ;
  /** 系统key */
  private String key;
  /** 字典编码 */
  private String dictCode;
  /** 字典值 */
  private String value;
}
