package com.biz.crm.tpm.business.pay.sdk.service;

import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.model.Result;

import java.util.List;

/**
 * 费用计提提交
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/13 17:27
 */
public interface WithHoldingSendHecService {

    /**
     * 费用计提 自动异步提交审批
     *
     * @param idList
     * @param crmUserIdentity
     */
    void pushCollectAutoAsync(List<String> idList, AbstractCrmUserIdentity crmUserIdentity);

    /**
     * 计提汇总 提交费控
     *
     * @param idList
     * @return
     */
    Result<?> pushCollect(List<String> idList);

    /**
     * 费用计提 提交费控
     *
     * @param idList
     * @return
     */
    Result<?> push(List<String> idList);
}
