package com.biz.crm.tpm.business.pay.sdk.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingCollectVo;

public interface WithHoldingCollectOaService {

    /**
     * 推送OA
     *
     * @param
     * @return
     */
    JSONObject pushOa(WithHoldingCollectVo order);

    /**
     * 重新提交OA
     *
     * @param
     * @return
     */
    JSONObject resubmitOa(WithHoldingCollectVo order);

    /**
     * OA撤回
     *
     * @param code
     * @return
     */
    boolean oaWithdraw(String code, String remark);
}
