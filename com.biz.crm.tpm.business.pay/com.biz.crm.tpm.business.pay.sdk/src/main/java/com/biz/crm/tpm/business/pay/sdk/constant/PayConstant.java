package com.biz.crm.tpm.business.pay.sdk.constant;

public interface PayConstant {
  /**
   * 费用核销编号生成前缀
   */
  String AUDIT_LADDER_CODE = "HXSQ";
  /**
   * 费用核销明细编号生成前缀
   */
  String AUDIT_DETAIL_LADDER_CODE = "HXMX";

  /**
   * 预付明细编号生成前缀
   */
  String PREPAY_LADDER_CODE = "YFSQ";
  /**
   * 预付明细编号生成前缀
   */
  String PREPAY_DETAIL_LADDER_CODE = "YFMX";
  /**
   * 审核流程标识 费用核销
   */
  String PROCESS_AUDIT_ACTIVITIES = "PAY_AUDIT";

  /**
   * 审核流程标识 预付
   */
  String PROCESS_PREPAY_ACTIVITIES = "PAY_PREPAY";

  String ACCOUNT_CODE_GENERATE_RULE = "SZ";

  /**
   * 费用核销核销锁 根据租户编号以及活动明细编号
   */
  String AUDIT_LOCK_PREFIX_FORMAT = "bz:crm:tpm:audit:lock:%s:%s";

  /**
   * 费用预付付款锁 根据租户编号以及活动明细编号
   */
  String PREPAY_LOCK_PREFIX_FORMAT = "bz:crm:tpm:audit:lock:%s:%s";

  /**
   * 费用预提编码生成前缀
   */
  String WITH_HOLDING_CODE_RULE = "YT";

  /**
   * 发票池操作redis lock键值
   */
  String INVOICE_REDIS_LOCK_KEY = "INVOICE_REDIS_LOCK_KEY:%s";

  /**
   * 订单调整产品key
   */
  String ORDER_ADJUST_PRODUCT_CACHE_KEY = "order_adjust_product_cache_key:%s";
}
