package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum DeliveryDetailTypeEnum {
    DELIVERY("delivery", "发货"),
    MANUAL_CLEAR("manualClear", "清空"),
    AUDIT("audit", "结案差异"),
    FEE_CASH("fee_cash", "费用兑付"),
    FEE_CLOSE("fee_close", "费用关闭"),
    ;
    private String code;

    private String desc;

    public static DeliveryDetailTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (DeliveryDetailTypeEnum typeEnum : values()) {
            if (StringUtils.equals(code, typeEnum.getCode())) {
                return typeEnum;
            }
        }
        return null;
    }

}
