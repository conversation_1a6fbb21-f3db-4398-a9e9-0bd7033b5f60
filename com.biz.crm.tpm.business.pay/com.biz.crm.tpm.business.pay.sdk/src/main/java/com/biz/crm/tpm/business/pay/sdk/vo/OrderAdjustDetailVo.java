package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "OrderAdjustDetail", description = "订单调整明细")
@Data
public class OrderAdjustDetailVo extends TenantFlagOpVo {

    @ApiModelProperty("调整编码")
    private String adjustCode;

    @ApiModelProperty("调整名称")
    private String adjustName;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectsCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectsName;

    @ApiModelProperty("核销申请名称")
    private String auditName;

    @ApiModelProperty("核销申请编号")
    private String auditCode;

    @ApiModelProperty("批次号")
    private String btNo;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("销售单位")
    private String saleUnit;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("贷项订单编码")
    private String creditCode;

    @ApiModelProperty("行号")
    private Integer lineNumber;

    @ApiModelProperty("调整金额")
    private BigDecimal adjustAmount;

}
