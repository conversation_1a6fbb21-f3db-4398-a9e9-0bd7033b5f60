package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.biz.crm.tpm.business.pay.sdk.enums.TpmPrepayTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 实体：活动预付;
 *
 * <AUTHOR> yaoyongming
 * @date : 2024-6-4
 */
@Data
@ApiModel(value = "ActivityPrepayVo", description = "活动预付")
public class ActivityPrepayVo extends TenantFlagOpVo {

    @ApiModelProperty("预付编号")
    private String prepayCode;

    @ApiModelProperty("预付名称")
    private String prepayName;

    @ApiModelProperty("部门编码")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("操作人")
    private String operateName;

    @ApiModelProperty("审批状态")
    private String status;

    @ApiModelProperty("推送日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("审批流程编码")
    private String processKey;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("公司代码")
    private String companyCode;
    private List<String> companyCodes;

    @ApiModelProperty("操作类型")
    private String hecOperationType;

    @ApiModelProperty("查看流程日志所需地址")
    private String hecReceiptUrl;

    @ApiModelProperty("付款状态")
    private String payStatus;

    @ApiModelProperty("收款方编码")
    private String payeeCode;
    private List<String> payeeCodes;

    @ApiModelProperty("收款方Sap编码")
    private String supplierErpCode;

    @ApiModelProperty("收款方名称")
    private String payeeName;

    @ApiModelProperty("付款成功时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paySucessDate;

    @ApiModelProperty("缓存键")
    private String cacheKey;

    @ApiModelProperty("预付明细")
    private List<ActivityPrepayDetailVo> detailList;

    @ApiModelProperty("收款信息")
    private List<ActivityPrepayPayeeVo> payeeList;

    @ApiModelProperty("附件")
    private List<ActivityPrepayFilesVo> filesList;


    /**
     * @see TpmPrepayTypeEnum
     */
    @ApiModelProperty("预付类型")
    private String prepayType;

    private List<String> schemeDetailCodeList;
}