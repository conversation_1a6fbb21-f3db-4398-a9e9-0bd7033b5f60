package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Set;

/**
 * <AUTHOR>
 * @describe: 管报收入
 * @createTime 2022年06月25日 10:19:00
 */
@ApiModel("管报收入")
@Data
public class ManageReportDto extends TenantFlagOpDto {


  @ApiModelProperty("收入年月")
  private String yearMonthLy;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("部门编码")
  private String departmentCode;

  @ApiModelProperty("部门名称")
  private String departmentName;

  @ApiModelProperty("部门层级")
  private Integer levelNum;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty(name = "客户编号", notes = "客户编号")
  private String customerCode;

  @ApiModelProperty(name = "客户名称", notes = "客户名称")
  private String customerName;

  @ApiModelProperty("产品编码")
  private String productCode;

  @ApiModelProperty("产品名称")
  private String productName;

  @ApiModelProperty("数量")
  private BigDecimal quantity;

  @ApiModelProperty("金额")
  private BigDecimal amount;

  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  private String orgCode;

  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  private String orgName;

  @ApiModelProperty("部门编码集合")
  private Set<String> deptCodeSet;

  @ApiModelProperty("年月集合")
  private Set<String> yearMonthLySet;
}
