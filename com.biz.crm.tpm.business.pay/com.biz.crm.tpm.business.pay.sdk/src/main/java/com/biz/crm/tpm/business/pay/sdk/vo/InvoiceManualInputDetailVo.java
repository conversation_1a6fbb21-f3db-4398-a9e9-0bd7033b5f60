package com.biz.crm.tpm.business.pay.sdk.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.biz.crm.business.common.sdk.vo.TenantOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel(value = "InvoiceDetailVo", description = "发票使用明细")
public class InvoiceManualInputDetailVo extends TenantOpVo {

    @ApiModelProperty("发票明细号")
    private String invoiceDetailNo;

    @ApiModelProperty("发票号码(只允许填写数字和字母)")
    private String invoiceNo;

    @ApiModelProperty("货物或应税劳务名称")
    private String name;

    @ApiModelProperty("税价合计")
    @JSONField(name = "amount")
    private BigDecimal priceAndTax;

    @ApiModelProperty("不含税金额")
    @JSONField(name = "withoutTaxAmount")
    private BigDecimal amountWithoutTax;

    @ApiModelProperty("税额")
    @JSONField(name = "taxAmount")
    private BigDecimal taxAmount;
}
