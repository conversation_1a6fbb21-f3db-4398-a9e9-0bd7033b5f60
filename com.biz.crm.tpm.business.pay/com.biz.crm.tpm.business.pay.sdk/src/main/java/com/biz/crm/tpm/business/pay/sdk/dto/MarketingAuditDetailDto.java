package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 实体：方案结案明细;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "MarketingAuditDetail", description = "方案结案明细")
@Data
public class MarketingAuditDetailDto extends TenantFlagOpDto {

    /**
     * 核销申请名称
     */
    @ApiModelProperty("核销申请名称")
    private String auditName;

    /**
     * 核销申请编号
     */
    @ApiModelProperty("核销申请编号")
    private String auditCode;

    /**
     * 核销明细编号
     */
    @ApiModelProperty("核销明细编号")
    private String auditDetailCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    private String actExecuteCode;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    private String customerCode;
    private Set<String> customerCodes;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("终端编码")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("终端类型")
    private String terminalType;

    @ApiModelProperty("兑付方式")
    private String cashType;

    @ApiModelProperty("执行描述")
    private String executeDesc;

    @ApiModelProperty("预估费用")
    private BigDecimal estimatedCost;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("预估费率")
    private BigDecimal ratio;

    @ApiModelProperty("结案费率")
    private BigDecimal auditRatio;

    @ApiModelProperty("费用依据")
    private String costBasis;

    @ApiModelProperty("本次核销金额")
    private BigDecimal auditAmount;

    @ApiModelProperty("已核销金额")
    private BigDecimal auditedAmount;

    @ApiModelProperty("是否完全核销")
    private String beFullAudit;

    @ApiModelProperty("是否完全兑付")
    private String beFullCash;

    @ApiModelProperty("票据类型")
    private String billType;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("返利品项名称")
    private String flItemNameStr;

    @ApiModelProperty("返利产品名称")
    private String flProductNameStr;

    @ApiModelProperty("是否预付")
    private String bePrepay;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("审批状态")
    private String status;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;

    @ApiModelProperty("活动可结案金额")
    private BigDecimal availableAuditAmount;

    @ApiModelProperty("自动兑付编码")
    private String autoCashCode;

    @ApiModelProperty("上账状态")
    private String accountStatus;

    @ApiModelProperty("错误描述")
    private String errMsg;

    @ApiModelProperty("是否推送SFA")
    private String isSendSfa;

    @ApiModelProperty("结案明细编码集合（修改兑付方式用）")
    private List<String> auditDetailCodeList;

    @ApiModelProperty("兑付明细编码集合")
    private List<String> cashDetailCodeList;

    /**
     * 是否选中，0否1是
     */
    @ApiModelProperty("是否选中，0否1是")
    private String checked;

    @ApiModelProperty("关联统筹方案编码")
    private String releaseCode;

    @ApiModelProperty("关联统筹方案名称")
    private String releaseName;

    private String groupKey;

    private String cacheKey;

    @ApiModelProperty("本次兑付金额")
    private BigDecimal thisCashAmount;

    @ApiModelProperty("活动描述")
    private String actDesc;

    @ApiModelProperty("结案完结日期")
    private String auditDate;

    @ApiModelProperty("兑付超时长")
    private String overDay;

    @ApiModelProperty("票扣兑付方式")
    private String ticketCashType;

    @ApiModelProperty("合作类型")
    private String cooperateType;

    @ApiModelProperty("合作类型")
    private String hzlx;

    @ApiModelProperty("一级部门编码")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称")
    private String departmentOneName;

    @ApiModelProperty("兑付方式剔除")
    private String cashTypeExclude;

    @ApiModelProperty("兑付编码剔除")
    private String cashCodeExclude;

    @ApiModelProperty("归属部门编码集合")
    private List<String> belongDepartmentCodeList;

    @ApiModelProperty("归属部门名称集合")
    private List<String> belongDepartmentNameList;

    @ApiModelProperty("年月集合")
    private Set<String> yearsSet;
    @ApiModelProperty("预算科目编码集合")
    private Set<String> subjectCodes;
    @ApiModelProperty("兑付方式集合")
    private Set<String> cashTypeSet;
    @ApiModelProperty("对象集合")
    private List<MarketingAuditDetailDto> dtoList;
    @ApiModelProperty("费用项目编码集合")
    private List<String> detailCodeList;

    @ApiModelProperty("是否物料采购")
    private String materialPurchaseFlag;

    @ApiModelProperty("费用关闭")
    private String costCloseFlag;

    @ApiModelProperty("方案明细类型")
    private List<String> caseTypeList;

    @ApiModelProperty("兑付方式类型")
    private String cashTypeFlag;

    @ApiModelProperty("过滤掉 可冲销预付余额>0的开关")
    private Boolean skipClose=true;

    @ApiModelProperty("政策形式编码")
    private String conditionFormula;

    @ApiModelProperty("政策形式名称")
    private String conditionFormulaName;

    @ApiModelProperty("条件数量")
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    private String giveNum;

    @ApiModelProperty("陈列卡板数")
    private BigDecimal displayCardNum;

    @ApiModelProperty("结案明细编码集合，逗号分割")
    private String auditDetailCodeListStr;
}
