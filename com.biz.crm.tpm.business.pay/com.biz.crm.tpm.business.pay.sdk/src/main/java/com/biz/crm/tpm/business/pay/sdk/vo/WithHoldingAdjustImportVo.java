package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;

import java.math.BigDecimal;

@Data
@CrmExcelImport(startRow = 4)
public class WithHoldingAdjustImportVo extends CrmExcelVo {

//    @CrmExcelColumn("计提编号")
//    private String withHoldingCode;
//
//    @CrmExcelColumn("审批状态")
//    private String status;
//
//    @CrmExcelColumn("推送费控单号")
//    private String pushHecCode;
//
//    @CrmExcelColumn("费控单号")
//    private String externalCode;
//
//    @CrmExcelColumn("推送状态")
//    private String pushStatus;
//
//    @CrmExcelColumn("预提年月")
//    private String yearMonthLy;
//
//    @CrmExcelColumn("活动归属年月")
//    private String years;
//
//    @CrmExcelColumn("公司编码")
//    private String companyCode;

    @CrmExcelColumn("业务编码")
    private String businessCode;

//    @CrmExcelColumn("方案规划编号")
//    private String activitiesCode;
//
//    @CrmExcelColumn("方案规划名称")
//    private String activitiesName;
//
//    @CrmExcelColumn("费用项目")
//    private String costTypeDetailName;
//
//    @CrmExcelColumn("预算科目")
//    private String budgetSubjectsName;
//
//    @CrmExcelColumn("成本中心")
//    private String costCenterName;
//
//    @CrmExcelColumn("客户编号")
//    private String customerCode;
//
//    @CrmExcelColumn("客户ERP编码")
//    private String erpCode;
//
//    @CrmExcelColumn("客户名称")
//    private String customerName;
//
//    @CrmExcelColumn("品项编码")
//    private String itemCode;
//
//    @CrmExcelColumn("品项名称")
//    private String itemName;
//
//    @CrmExcelColumn("活动描述")
//    private String actDesc;
//
//    @CrmExcelColumn("预提类型")
//    private String withHoldingType;
//
//    @CrmExcelColumn("计提来源")
//    private String withHoldingSource;
//
//    @CrmExcelColumn("申请金额")
//    private String applyAmountStr;
//
    @CrmExcelColumn("计提金额")
    private String withHoldingAmountStr;

//    @CrmExcelColumn("实际金额")
//    private String actualAmountStr;
//
//    @CrmExcelColumn("兑付方式")
//    private String payBy;
//
//    @CrmExcelColumn("凭证号")
//    private String voucherCode;
//
//    @CrmExcelColumn("失败原因")
//    private String failMsg;
//
//    @CrmExcelColumn("创建人")
//    private String createName;
//
//    @CrmExcelColumn("创建时间")
//    private String createTime;
//
//    @CrmExcelColumn("更新人")
//    private String modifyName;
//
//    @CrmExcelColumn("更新时间")
//    private String modifyTime;
//
//    @CrmExcelColumn("汇总单号")
//    private String collectCode;
//
//    @CrmExcelColumn("是否当期费用")
//    private String beThisFee;
//
//    @CrmExcelColumn("是否调整")
//    private String beAdjust;
//
//    @CrmExcelColumn("管报计提金额")
//    private String withHoldingReportAmountStr;

    @CrmExcelColumn("调整金额")
    private String reduceAmountStr;

//    @CrmExcelColumn("管报计提调减金额")
//    private String withHoldingReportReduceAmountStr;
    private BigDecimal withHoldingReportReduceAmount;

    @CrmExcelColumn("管报实际金额")
    private String actualReportAmountStr;
//
//    @CrmExcelColumn("活动明细编号")
//    private String activitiesDetailCode;
//
//    @CrmExcelColumn("承担部门编码")
//    private String bearDepartmentCode;
//
//    @CrmExcelColumn("承担部门名称")
//    private String bearDepartmentName;
//
//    @CrmExcelColumn("使用部门编码")
//    private String belongDepartmentCode;
//
//    @CrmExcelColumn("使用部门名称")
//    private String belongDepartmentName;
}
