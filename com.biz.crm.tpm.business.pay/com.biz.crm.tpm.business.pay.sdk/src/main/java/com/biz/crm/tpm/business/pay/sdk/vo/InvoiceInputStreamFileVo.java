package com.biz.crm.tpm.business.pay.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/4 21:56
 */
@Data
@ApiModel("文件流")
public class InvoiceInputStreamFileVo {

    @ApiModelProperty("文件base64")
    private String fileInputStream;

    @ApiModelProperty("文件fileCode")
    private String fileCode;

    @ApiModelProperty("文件相对路径")
    private String fileUrl;

    @ApiModelProperty("文件名")
    private String fileName;
}
