package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单调整  贷项订单
 *
 * <AUTHOR>
 * @version v1.0.0
 * @date 2024/9/28 17:51
 */
@Data
@CrmExcelImport(startRow = 4)
public class OrderAdjustImportVo extends CrmExcelVo {

    @CrmExcelColumn("商品编码")
    private String productCode;

    @CrmExcelColumn("商品名称")
    private String productName;

    @ApiModelProperty("销售单位")
    private String saleUnit;

    @CrmExcelColumn("订单金额")
    private String amountStr;

    @CrmExcelColumn("订单金额")
    private BigDecimal amount;
}
