package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：方案结案明细;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "MarketingAuditDetailVo", description = "方案结案明细")
@Data
public class MarketingAuditDetailVo extends TenantFlagOpVo {

  /**
   * 核销申请名称
   */
  @ApiModelProperty("核销申请名称")
  private String auditName;

  /**
   * 核销申请编号
   */
  @ApiModelProperty("核销申请编号")
  private String auditCode;

  /**
   * 核销明细编号
   */
  @ApiModelProperty("核销明细编号")
  private String auditDetailCode;

  @ApiModelProperty("方案编码")
  private String schemeCode;

  @ApiModelProperty("方案名称")
  private String schemeName;

  @ApiModelProperty("方案明细编码")
  private String schemeDetailCode;

  @ApiModelProperty("活动执行编码")
  private String actExecuteCode;

  @ApiModelProperty("活动名称")
  private String actName;

  @ApiModelProperty("活动大类编码")
  private String categoryCode;

  @ApiModelProperty("活动大类名称")
  private String categoryName;

  @ApiModelProperty("活动细类编码")
  private String detailCode;

  @ApiModelProperty("活动细类名称")
  private String detailName;

  @ApiModelProperty("开始时间")
  private String startDate;

  @ApiModelProperty("结束时间")
  private String endDate;

  @ApiModelProperty("年月")
  private String years;

  @ApiModelProperty("归属部门编码")
  private String belongDepartmentCode;

  @ApiModelProperty("归属部门名称")
  private String belongDepartmentName;

  @ApiModelProperty("承担部门编码")
  private String bearDepartmentCode;

  @ApiModelProperty("承担部门名称")
  private String bearDepartmentName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("终端编码")
  private String terminalCode;

  @ApiModelProperty("终端名称")
  private String terminalName;

  @ApiModelProperty("终端类型")
  private String terminalType;

  @ApiModelProperty("兑付方式")
  private String cashType;

  @ApiModelProperty("执行描述")
  private String executeDesc;

  @ApiModelProperty("预估费用")
  private BigDecimal estimatedCost;

  @ApiModelProperty("申请金额")
  private BigDecimal applyAmount;

  @ApiModelProperty("预估费率")
  private BigDecimal ratio;

  @ApiModelProperty("结案费率")
  private BigDecimal auditRatio;

  @ApiModelProperty("费用依据")
  private String costBasis;

  @ApiModelProperty("本次核销金额")
  private BigDecimal auditAmount;

  @ApiModelProperty("已核销金额")
  private BigDecimal auditedAmount;

  /**
   * 本次核销金额（结案金额） - 已核销金额
   */
  @ApiModelProperty("剩余核销金额")
  private BigDecimal surplusAuditAmount;

  @ApiModelProperty("是否完全核销")
  private String beFullAudit;

  @ApiModelProperty("是否完全核销")
  private String beFullAuditStr;

  @ApiModelProperty("是否完全兑付")
  private String beFullCash;

  @ApiModelProperty("票据类型")
  private String billType;

  @ApiModelProperty("产品编码")
  private String productCode;

  @ApiModelProperty("产品名称")
  private String productName;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("返利品项名称")
  private String flItemNameStr;

  @ApiModelProperty("返利产品名称")
  private String flProductNameStr;

  @ApiModelProperty("是否预付")
  private String bePrepay;

  @ApiModelProperty("预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("物料编码")
  private String materialCode;

  @ApiModelProperty("物料名称")
  private String materialName;

  @ApiModelProperty("兑付金额")
  private BigDecimal cashAmount;

  @ApiModelProperty("活动可结案金额")
  private BigDecimal availableAuditAmount;

  @ApiModelProperty("自动兑付编码")
  private String autoCashCode;

  @ApiModelProperty("上账状态")
  private String accountStatus;

  @ApiModelProperty("错误描述")
  private String errMsg;

  @ApiModelProperty("是否推送SFA")
  private String isSendSfa;

  @ApiModelProperty("预付金额")
  private BigDecimal prepayAmount;
  @ApiModelProperty("已冲销金额")
  private BigDecimal reversedAmount;
  @ApiModelProperty("预付可冲销金额")
  private BigDecimal availableReversedAmount;
  @ApiModelProperty("可兑付金额汇总")
  private BigDecimal availableCashAmountTotal;
  @ApiModelProperty("可冲销预付余额汇总")
  private BigDecimal availableReversedAmountTotal;

  /**
   * 是否选中，0否1是
   */
  @ApiModelProperty("是否选中，0否1是")
  private String checked;

  /**
   * 兑付明细：检索活动用
   */
  @ApiModelProperty("可兑付金额")
  private BigDecimal availableCashAmount;
  @ApiModelProperty("本次兑付金额")
  private BigDecimal thisCashAmount;
  @ApiModelProperty("是否完全兑付")
  private String beCash;

  @ApiModelProperty("关联统筹方案编码")
  private String releaseCode;

  @ApiModelProperty("关联统筹方案名称")
  private String releaseName;

  @ApiModelProperty("活动描述")
  private String actDesc;

  @ApiModelProperty("结案完结日期")
  private String auditDate;

  @ApiModelProperty("兑付超时长")
  private String overDay;

  @ApiModelProperty("关闭金额")
  private BigDecimal closeAmount;

  @ApiModelProperty("兑付余额")
  private BigDecimal cashBalance;

  @ApiModelProperty("剩余可兑付金额（包含预付）")
  private BigDecimal availableCashPrepayAmount;

  @ApiModelProperty("审批状态")
  private String status;

  @ApiModelProperty("一级部门编码")
  private String departmentOneCode;

  @ApiModelProperty("一级部门名称")
  private String departmentOneName;

  @ApiModelProperty("票扣兑付方式")
  private String ticketCashType;

  @ApiModelProperty("合作类型")
  private String cooperateType;

  @ApiModelProperty("合作类型")
  private String hzlx;

  @ApiModelProperty("客户搜索项")
  private String searchName;

  @ApiModelProperty("临时存储字段-没实际意义")
  private String ruleCode;


  @ApiModelProperty("活动结束时间")
  private Date activityEndDate;

  @ApiModelProperty("活动创建人")
  private String activityCreateAccount;


  @ApiModelProperty("政策形式编码")
  private String conditionFormula;

  @ApiModelProperty("政策形式名称")
  private String conditionFormulaName;


  @ApiModelProperty("条件数量")
  private String conditionNum;

  @ApiModelProperty("搭赠/优惠数量")
  private String giveNum;

  @ApiModelProperty("陈列卡板数")
  private BigDecimal displayCardNum;
}
