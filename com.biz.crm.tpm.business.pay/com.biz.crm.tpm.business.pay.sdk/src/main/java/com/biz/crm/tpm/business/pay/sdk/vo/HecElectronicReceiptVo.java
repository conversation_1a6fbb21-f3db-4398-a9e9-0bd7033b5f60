package com.biz.crm.tpm.business.pay.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 费控电子回单 返回Vo
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/9 17:37
 */
@ApiModel(value = "HecElectronicReceiptVo", description = "费控电子回单返回VO")
@Getter
@Setter
public class HecElectronicReceiptVo implements Serializable {

    @ApiModelProperty("来源单据类型")
    private String sourceOrderType;

    @ApiModelProperty("来源单据号")
    private String sourceOrderNumber;

    @ApiModelProperty("TPM计划付款行行号")
    private String tpmPmtLineNumber;

    @ApiModelProperty("电子回单地址")
    private String ftpPath;
}
