package com.biz.crm.tpm.business.pay.sdk.service;

import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingWriteOffDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingWriteOffVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 费用冲销提交
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/13 17:27
 */
public interface WithHoldingWriteOffSendHecService {


    /**
     * 冲销
     *
     * @param dtoList
     * @return
     */
    Map<String, List<WithHoldingWriteOffVo>> writeOff(List<WithHoldingWriteOffDto> dtoList);

    /**
     * 费用计提 自动异步提交审批
     *
     * @param idList
     * @param crmUserIdentity
     */
    void pushAutoAsync(List<String> idList, AbstractCrmUserIdentity crmUserIdentity);

    /**
     * 提交审批
     *
     * @param idList
     * @return
     */
    Result<?> push(List<String> idList);
}
