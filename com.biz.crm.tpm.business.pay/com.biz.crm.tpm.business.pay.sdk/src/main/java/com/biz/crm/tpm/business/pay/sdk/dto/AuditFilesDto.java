package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.FileDto;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 参数传递dto：费用核销附件;
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "AuditFiles",description = "费用核销附件")
@Getter
@Setter
public class AuditFilesDto extends FileDto implements Serializable,Cloneable{
  /** 费用核销编号 */
  @ApiModelProperty(name = "auditCode",notes = "方案编号", value = "方案编号")
  private String auditCode;
  /** 活动细类编码 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编码", value = "活动细类编码")
  private String costTypeDetailCode;
  /** 活动细类名称 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value = "活动细类名称")
  private String costTypeDetailName;
  /** 要求核销资料编号 */
  @ApiModelProperty(name = "requestCode",notes = "要求核销资料编号", value = "要求核销资料编号")
  private String requestCode;
}