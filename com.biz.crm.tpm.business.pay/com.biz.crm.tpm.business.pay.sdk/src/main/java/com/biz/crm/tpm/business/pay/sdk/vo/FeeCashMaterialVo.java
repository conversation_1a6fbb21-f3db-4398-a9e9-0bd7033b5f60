package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@ApiModel(value = "FeeCashMaterialVo", description = "采购需求信息")
@Data
public class FeeCashMaterialVo extends TenantFlagOpVo {

    @ApiModelProperty("兑付名称")
    private String cashName;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("商品参考链接")
    private String url;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("需求到货时间")
    private String requiredDeliveryTime;

    @ApiModelProperty(name = "contactPerson",notes = "联系人", value= "联系人")
    private String contactPerson;

    @ApiModelProperty(name = "contactPhone",notes = "联系电话", value= "联系电话")
    private String contactPhone;

    @ApiModelProperty(name = "address",notes = "邮寄地址", value= "邮寄地址")
    private String address;

}
