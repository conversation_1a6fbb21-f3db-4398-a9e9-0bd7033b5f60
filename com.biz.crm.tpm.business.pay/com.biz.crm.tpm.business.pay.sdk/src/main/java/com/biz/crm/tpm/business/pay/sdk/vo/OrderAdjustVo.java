package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@ApiModel(value = "OrderAdjust", description = "订单调整")
@Data
public class OrderAdjustVo extends TenantFlagOpVo {

    @ApiModelProperty("调整编码")
    private String adjustCode;

    @ApiModelProperty("调整名称")
    private String adjustName;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("部门编码")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("确认状态")
    private String confirmStatus;

    @ApiModelProperty("订单调整明细")
    List<OrderAdjustDetailVo> detailList;
    @ApiModelProperty("订单调整产品")
    List<OrderAdjustProductVo> productList;
}
