package com.biz.crm.tpm.business.pay.sdk.service;


import com.biz.crm.business.common.page.cache.service.BusinessPageCacheService;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayVo;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;

import java.util.List;

/**
 * 活动预付
 */
public interface ActivityPrepayService extends BusinessPageCacheService<ActivityPrepayDetailVo, ActivityPrepayDetailDto> {

    /**
     * 通过编码查询单条数据
     *
     * @param code 编码
     * @return 单条数据
     */
    ActivityPrepayVo findByCode(String code);

    /**
     * 新增数据
     *
     * @param dto dto对象
     * @return
     */
    void create(ActivityPrepayDto dto, String cacheKey, boolean commit);

    /**
     * 修改新据
     *
     * @param dto dto对象
     * @return
     */
    void update(ActivityPrepayDto dto, String cacheKey, boolean commit);

    /**
     * 更新流程状态
     *
     * @param dto
     */
    void updateProcessStatus(ActivityPrepayDto dto);

    /**
     * 费控电汇付款状态回传
     * @param dtoList
     */
    void hecPayStatusCallback(List<HecCallbackDto> dtoList);

    /**
     *
     * 预付明细操作回滚-新增操作明细(加锁预查询)
     *
     * @param dto
     * @return
     */
    List<String> rollbackModifyActivityPrepayRecordPre(ActivityPrepayDto dto);


    /**
     *
     * 预付明细操作回滚-新增操作明细
     *
     * @param dto
     */
    void rollbackModifyActivityPrepayRecord(ActivityPrepayDto dto);

    void compensateOaCallback(String prepayCode);
}
