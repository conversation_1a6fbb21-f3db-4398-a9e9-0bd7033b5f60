package com.biz.crm.tpm.business.pay.sdk.dto;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 参数传递dto：活动预付明细;
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@ApiModel(value = "PrepayDetail",description = "活动预付明细")
@Getter
@Setter
public class PrepayDetailDto implements Serializable,Cloneable{
  /** 操作的预授权标记 */
  @ApiModelProperty(name = "操作的预授权标记", notes = "操作的预授权标记", value = "操作的预授权标记", required=true)
  private String prefix;
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value = "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 预付编号 */
  @ApiModelProperty(name = "prepayCode",notes = "预付编号", value = "预付编号")
  private String prepayCode;
  /** 预付申请明细编号 */
  @ApiModelProperty(name = "预付申请明细编号", notes = "预付申请明细编号")
  private String prepayDetailCode;
  /** 申请金额 */
  @ApiModelProperty(name = "applyAmount",notes = "申请金额", value = "申请金额")
  private BigDecimal applyAmount;
  /** 活动明细编码 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value = "活动明细编码")
  private String activitiesDetailCode;
  /** 预付金额 */
  @ApiModelProperty(name = "prepayAmount",notes = "预付金额", value = "预付金额")
  private BigDecimal prepayAmount;
  /** 支付方式 */
  @ApiModelProperty(name = "payBy",notes = "支付方式", value = "支付方式")
  private String payBy;
  /** 支付方式名称 */
  @ApiModelProperty(name = "支付方式名称", notes = "支付方式名称")
  private String payByName;
  /** 预付原因 */
  @ApiModelProperty(name = "reason",notes = "预付原因", value = "预付原因")
  private String reason;
  /** 付款方 */
  @ApiModelProperty(name = "payer",notes = "付款方", value = "付款方")
  private String payer;
  /** 付款方账号 */
  @ApiModelProperty(name = "payerAccount",notes = "付款方账号", value = "付款方账号")
  private String payerAccount;
  /** 收款方 */
  @ApiModelProperty(name = "payee",notes = "收款方", value = "收款方")
  private String payee;
  /** 收款方账号 */
  @ApiModelProperty(name = "payeeAccount",notes = "收款方账号", value = "收款方账号")
  private String payeeAccount;
  /** 数据状态（删除状态） */
  @ApiModelProperty(name = "delFlag",notes = "数据状态（删除状态）", value = "数据状态（删除状态）")
  private String delFlag;
  /** 活动细类编号 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编号细类编号", value = "活动细类编号细类编号")
  private String costTypeDetailCode;
  /** 活动细类名称 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value = "活动细类名称")
  private String costTypeDetailName;
  /** 客户名称 */
  @ApiModelProperty(name = "customerName",notes = "客户名称", value = "客户名称")
  private String customerName;
  /** 客户编号 */
  @ApiModelProperty(name = "customerCode",notes = "客户编号", value = "客户编号")
  private String customerCode;
  /** 终端名称 */
  @ApiModelProperty(name = "terminalName",notes = "终端名称", value = "终端名称")
  private String terminalName;
  /** 终端编号 */
  @ApiModelProperty(name = "terminalCode",notes = "终端编号", value = "终端编号")
  private String terminalCode;

}