package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "FeeCashTicket", description = "费用票扣明细")
@Data
public class FeeCashTicketDto extends TenantFlagOpDto {

    @ApiModelProperty("兑付名称")
    private String cashName;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectsCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectsName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("销售单位")
    private String saleUnit;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("核销申请名称")
    private String auditName;

    @ApiModelProperty("核销申请编号")
    private String auditCode;

    @ApiModelProperty("批次号")
    private String btNo;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("活动归属年月")
    private String yearMonthLy;

    @ApiModelProperty("未开票金额")
    private BigDecimal unInvoicedAmount;

    @ApiModelProperty("累计未开票金额")
    private BigDecimal unInvoicedCumulativeAmount;

    @ApiModelProperty("单次折让率")
    private BigDecimal singleDiscountsRate;

    @ApiModelProperty("单次折让率(显示)")
    private String singleDiscountsRateStr;

    @ApiModelProperty("累计折让率")
    private BigDecimal cumulativeDiscountsRate;

    @ApiModelProperty("累计折让率(显示)")
    private String cumulativeDiscountsRateStr;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("贷项订单编码")
    private String creditCode;

    @ApiModelProperty("行号")
    private Integer lineNumber;

    /**
     * 是否选中，0否1是
     */
    @ApiModelProperty("是否选中，0否1是")
    private String checked;

    private String cacheKey;

    @ApiModelProperty("错误描述")
    private String errMsg;

    @ApiModelProperty("校验状态")
    private Boolean checkFlag = Boolean.TRUE;

}
