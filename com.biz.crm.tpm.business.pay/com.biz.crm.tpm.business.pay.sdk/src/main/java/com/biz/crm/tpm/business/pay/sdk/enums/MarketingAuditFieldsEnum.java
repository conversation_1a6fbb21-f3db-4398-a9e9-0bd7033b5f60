package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MarketingAuditFieldsEnum {
    auditName("auditName"),
    auditCode("auditCode"),
    totalApplyAmount("totalApplyAmount"),
    remark("remark"),
    deptCode("deptCode"),
    title("title"),
    ;

    private String dictCode;

    public static MarketingAuditFieldsEnum findByCode(String code) {
        Optional<MarketingAuditFieldsEnum> first = Stream.of(MarketingAuditFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}