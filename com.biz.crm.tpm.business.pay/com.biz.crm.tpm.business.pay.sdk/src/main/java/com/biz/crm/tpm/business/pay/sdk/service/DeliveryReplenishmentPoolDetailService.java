package com.biz.crm.tpm.business.pay.sdk.service;

import com.biz.crm.tpm.business.pay.sdk.dto.DeliveryReplenishmentPoolDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.DeliveryReplenishmentPoolDetailVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface DeliveryReplenishmentPoolDetailService {

    /**
     * 获取发货费用定时任务
     */
    void generateDeliveryReplenishment();

    /**
     * 获取发货费用定时任务
     */
    void generateDeliveryReplenishmentDate(String startDate, String endDate);

    /**
     * 发货费用冲销定时任务
     */
    void deliveryReplenishmentWriteOff(List<String> businessCodes);

    /**
     * 创建
     *
     * @param dtoList
     */
    void create(List<DeliveryReplenishmentPoolDetailDto> dtoList);

    /**
     * 根据活动编码查询金额汇总
     *
     * @param schemeDetailCodes
     * @param yearMonthLy
     * @return
     */
    Map<String, BigDecimal> findBySchemeDetailCodes(List<String> schemeDetailCodes, String yearMonthLy);

    /**
     * 根据活动编码查询金额汇总
     *
     * @param schemeDetailCodes
     * @return
     */
    Map<String, BigDecimal> findManualClearAndDeliveryBySchemeDetailCodes(List<String> schemeDetailCodes, String years);

    /**
     * 根据结案明细编码查询操作类型=清空或费用关闭的“费用金额”并汇总
     *
     * @param auditDetailCodes
     * @return
     */
    Map<String, BigDecimal> findByAuditDetailCodes(List<String> auditDetailCodes);

    /**
     * 根据结案明细编码查询
     *
     * @param auditDetailCodes
     * @return
     */
    Map<String, List<DeliveryReplenishmentPoolDetailVo>> findByAuditDetailCodesAll(List<String> auditDetailCodes);

    /**
     * 根据活动明细编码查询
     *
     * @param schemeDetailCodes
     * @return
     */
    Map<String, List<DeliveryReplenishmentPoolDetailVo>> findBySchemeDetailCodesAll(List<String> schemeDetailCodes);

    void updateDeliveryReplenishmentPoolDetailStatus(List<String> ruleCodes);

    void backDeliveryReplenishmentWriteOff(List<String> businessCodes);
}
