package com.biz.crm.tpm.business.pay.sdk.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/4 22:55
 */
@Data
@ApiModel("发票明细识别结果")
public class InvoiceDetailIdentityVo {

    @ApiModelProperty("发票明细号")
    @JSONField(name = "lineNumber")
    private String invoiceDetailNo;

    @ApiModelProperty("发票号码(只允许填写数字和字母)")
    private String invoiceNo;

    @ApiModelProperty("货物或应税劳务名称")
    @JSONField(name = "name")
    private String name;

    @ApiModelProperty("税价合计")
    private BigDecimal priceAndTax;

    @ApiModelProperty("不含税金额")
    @JSONField(name = "amount")
    private BigDecimal amountWithoutTax;

    @ApiModelProperty("税额")
    @JSONField(name = "tax")
    private BigDecimal taxAmount;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    public BigDecimal getPriceAndTax() {
        this.amountWithoutTax = ObjectUtils.defaultIfNull(this.amountWithoutTax, BigDecimal.ZERO);
        this.taxAmount = ObjectUtils.defaultIfNull(this.taxAmount, BigDecimal.ZERO);
        this.priceAndTax = this.amountWithoutTax.add(this.taxAmount);
        return this.priceAndTax;
    }
}
