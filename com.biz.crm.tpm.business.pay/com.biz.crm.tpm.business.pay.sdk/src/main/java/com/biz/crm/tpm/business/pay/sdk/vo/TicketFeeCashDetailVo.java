package com.biz.crm.tpm.business.pay.sdk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

import java.util.Date;

/**
 * @Author: haiyang
 * @Date: 2024-12-23 16:43
 * @Desc:
 */
@Data
public class TicketFeeCashDetailVo {

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "创建人编码")
    private String createAccount;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "兑付编码")
    private String cashCode;

    @ApiModelProperty(value = "费用科目名称")
    private String budgetSubjectsName;

    @ApiModelProperty(value = "费用科目编码")
    private String budgetSubjectsCode;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "备注")
    private String cashName;

    @ApiModelProperty(value = "费用期间")
    private String years;

    @ApiModelProperty(value = "客户主体")
    private String companyCode;
}
