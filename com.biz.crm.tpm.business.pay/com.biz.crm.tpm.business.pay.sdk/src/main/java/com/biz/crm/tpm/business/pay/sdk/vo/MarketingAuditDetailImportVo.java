package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("方案结案明细")
@Data
@CrmExcelImport
public class MarketingAuditDetailImportVo extends CrmExcelVo {

    @CrmExcelColumn("公司编码")
    private String companyCode;

    @CrmExcelColumn("方案编码")
    private String schemeCode;

    @CrmExcelColumn("方案明细编码")
    private String schemeDetailCode;

    @CrmExcelColumn("活动名称")
    private String actName;

    @CrmExcelColumn("活动描述")
    private String actDesc;

    @CrmExcelColumn("活动细类名称")
    private String detailName;

    @CrmExcelColumn("开始时间")
    private String startDate;

    @CrmExcelColumn("结束时间")
    private String endDate;

    @CrmExcelColumn("年月")
    private String years;

    @CrmExcelColumn("归属部门名称")
    private String belongDepartmentName;

    @CrmExcelColumn("成本中心名称")
    private String costCenterName;

    @CrmExcelColumn("客户名称")
    private String customerName;

    @CrmExcelColumn("门店名称")
    private String terminalName;

    @CrmExcelColumn("品项名称")
    private String itemName;

    @CrmExcelColumn("返利品项名称")
    private String flItemNameStr;

    @CrmExcelColumn("返利产品名称")
    private String flProductNameStr;

    @CrmExcelColumn("兑付方式")
    private String cashType;

    @CrmExcelColumn("执行描述")
    private String executeDesc;

    @CrmExcelColumn("规划费用")
    private String applyAmount;

    @CrmExcelColumn("已结案金额")
    private String auditedAmount;

    @CrmExcelColumn("本次结案金额")
    private String auditAmountStr;
    private BigDecimal auditAmount;

    @CrmExcelColumn("是否完全结案")
    private String beFullAudit;

    @CrmExcelColumn("票据类型")
    private String billType;
}
