package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 兑付类型
 */
@AllArgsConstructor
@Getter
public enum CashTypeEnum {
  WIRE("wire", "wire", "电汇预付", "CSH002", "1"),
  ACCOUNT("account", "account", "账扣预付","CSH012", "2"),
  FEE("fee", "fee", "费用兑付", "", "3"),
  CLOSE("close", "close", "费用关闭", "", "4"),
  MATERIAL("material", "material", "物料采购", "", "5"),
  WIREDUIFU("wiredf", "wiredf", "电汇兑付", "", "6"),
  ;
  /** 系统key */
  private String key;
  /** 字典编码 */
  private String dictCode;
  /** 描述 */
  private String value;
  /** 费控编码 */
  private String hecCode;
  /** 字典排序 */
  private String order;

  public static CashTypeEnum findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    return Arrays.stream(CashTypeEnum.values()).filter(o->o.getDictCode().equals(code)).findFirst().orElse(null);
  }
}
