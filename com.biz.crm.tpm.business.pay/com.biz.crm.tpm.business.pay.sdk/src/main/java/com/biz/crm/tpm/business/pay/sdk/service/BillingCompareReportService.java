package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.BillingCompareReportVo;
import org.springframework.data.domain.Pageable;

public interface BillingCompareReportService {

    /**
     * 按条件查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    Page<BillingCompareReportVo> findByConditions(Pageable pageable, BillingCompareReportVo dto);
}
