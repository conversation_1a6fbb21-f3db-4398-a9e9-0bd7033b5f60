package com.biz.crm.tpm.business.pay.sdk.service;


import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingWriteOffDto;
import com.biz.crm.tpm.business.pay.sdk.vo.BackWithHoldingWriteOffVo;

import java.util.List;
import java.util.Map;

/**
 * 进货返利费用冲销服务接口
 */
public interface BackWithHoldingWriteOffService {

    Map<String, List<BackWithHoldingWriteOffVo>> writeOff(List<WithHoldingWriteOffDto> dtoList);

    Map<String, List<BackWithHoldingWriteOffVo>> cashOrderCloseWriteOff(List<WithHoldingWriteOffDto> dtoList, String type);

}
