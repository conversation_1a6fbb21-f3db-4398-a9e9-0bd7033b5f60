package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum FeeCashFieldsEnum {
    auditName("cashName"),
    auditCode("cashCode"),
    totalApplyAmount("totalCashAmount"),
    remark("remark"),
    title("title"),
    ;

    private String dictCode;

    public static FeeCashFieldsEnum findByCode(String code) {
        Optional<FeeCashFieldsEnum> first = Stream.of(FeeCashFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}