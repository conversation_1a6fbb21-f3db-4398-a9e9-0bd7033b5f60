package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "FeeCashToc", description = "TOC支付明细")
@Data
public class FeeCashTocVo extends TenantFlagOpVo {

    @ApiModelProperty("兑付名称")
    private String cashName;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("打款平台")
    private String platform;

    @ApiModelProperty("姓名")
    private String payeeName;

    @ApiModelProperty("银行账号")
    private String bankNo;

    @ApiModelProperty("身份证号码")
    private String idCard;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("收款金额")
    private BigDecimal payeeAmount;

    @ApiModelProperty("实际打款金额")
    private BigDecimal payAmount;

    @ApiModelProperty("收款金额汇总")
    private BigDecimal payeeAmountTotal;

    @ApiModelProperty("实际打款金额汇总")
    private BigDecimal payAmountTotal;

    @ApiModelProperty("申请人账号")
    private String cashCreateAccount;
    @ApiModelProperty("申请人")
    private String cashCreateName;
    @ApiModelProperty("创建人所在部门编码")
    private String orgCode;
    @ApiModelProperty("创建人所在部门")
    private String orgName;
    @ApiModelProperty("费用归属年月")
    private String yearMonthLy;

    @ApiModelProperty("审批流程编码")
    private String processKey;

    @ApiModelProperty("付款成功时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paySucessDate;

    @ApiModelProperty("错误描述")
    private String errMsg;

    @ApiModelProperty("校验状态")
    private Boolean checkFlag;

}
