package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 描述：</br>针对活动细类进行封装，提供给预付中选择活动明细中的数据
 *
 * <AUTHOR>
 * @date 2022/6/18
 */
@ApiModel(value = "PrepayActivityItemVo", description = "针对活动细类进行封装，提供给预付中选择活动明细中的数据")
@Getter
@Setter
public class PrepayActivityItemVo extends ActivitiesDetailVo {
  @ApiModelProperty("已预付金额")
  private BigDecimal prepaidAmount;
}
