package com.biz.crm.tpm.business.pay.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/5 17:32
 */
@Data
@ApiModel("发票明细分页")
public class InvoiceDetailPageVo {

    @ApiModelProperty("发票类型")
    private String type;

    @ApiModelProperty("发票代码")
    private String code;

    @ApiModelProperty("发票号码(只允许填写数字和字母)")
    private String invoiceNo;

    @ApiModelProperty("开票日期")
    private String billingDate;

    @ApiModelProperty("校验码(只允许填写数字和字母)")
    private String checkCode;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("购买方名称")
    private String purchaser;

    @ApiModelProperty("购买方税号")
    private String pNo;

    @ApiModelProperty("购买方开户行及账号")
    private String pBankAndAccount;

    @ApiModelProperty("购买方地址电话")
    private String pAddressAndPhone;

    @ApiModelProperty("销售方名称")
    private String seller;

    @ApiModelProperty("销售方税号")
    private String sNo;

    @ApiModelProperty("销售方开户行及账号")
    private String sBankAndAccount;

    @ApiModelProperty("销售方地址电话")
    private String sAddressAndPhone;

    @ApiModelProperty("是否验证")
    private String checked;

    @ApiModelProperty("校验信息")
    private String checkMsg;

    @ApiModelProperty("文件id")
    private String fileCode;

    @ApiModelProperty("发票明细号")
    private String invoiceDetailNo;

    @ApiModelProperty("货物或应税劳务名称")
    private String name;

    @ApiModelProperty("税价合计")
    private BigDecimal priceAndTax;

    @ApiModelProperty("不含税金额")
    private BigDecimal amountWithoutTax;

    @ApiModelProperty("税额")
    private BigDecimal taxAmount;

    @ApiModelProperty("关联兑付单")
    private String cashCode;
}
