package com.biz.crm.tpm.business.pay.sdk.event;

import com.biz.crm.tpm.business.pay.sdk.vo.AuditVo;

/**
 * 费用核销;(tpm_audit)相关的事件通知
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
public interface AuditEventListener{

  /**
   * 当费用核销数据被创建时，该事件被触发
   * @param auditVo
   */
  void onCreated(AuditVo auditVo);
  /**
   * 当费用核销数据被修改时，该事件被触发
   * @param oldAuditVo 修改前数据
   * @param auditVo  修改后数据
   */
  void onUpdate(AuditVo oldAuditVo, AuditVo auditVo);
  /**
   * 当费用核销数据被删除时（逻辑删除），该事件被触发
   * @param auditVo
   */
  void onDeleted(AuditVo auditVo);
  /**
   * 当费用核销数据被启用时，该事件被触发
   * @param auditVo
   */
  void onEnable(AuditVo auditVo);
  /**
   * 当费用核销数据被禁用时，该事件被触发
   * @param auditVo
   */
  void onDisable(AuditVo auditVo);
}
