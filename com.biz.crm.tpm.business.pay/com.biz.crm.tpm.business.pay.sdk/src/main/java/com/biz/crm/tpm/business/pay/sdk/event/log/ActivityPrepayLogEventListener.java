package com.biz.crm.tpm.business.pay.sdk.event.log;

import com.biz.crm.tpm.business.pay.sdk.dto.log.ActivityPrepayLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

public interface ActivityPrepayLogEventListener extends NebulaEvent {

    /**
     * 创建事件
     *
     * @param eventDto
     */
    void onCreate(ActivityPrepayLogEventDto eventDto);
    /**
     * 删除事件
     *
     * @param eventDto
     */
    void onDelete(ActivityPrepayLogEventDto eventDto);
    /**
     * 更新日志
     *
     * @param eventDto
     */
    void onUpdate(ActivityPrepayLogEventDto eventDto);
}
