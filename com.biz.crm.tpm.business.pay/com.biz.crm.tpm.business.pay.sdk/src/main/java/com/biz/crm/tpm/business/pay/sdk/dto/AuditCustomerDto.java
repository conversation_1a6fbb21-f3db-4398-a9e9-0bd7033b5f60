package com.biz.crm.tpm.business.pay.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 参数传递dto：费用明细客户;
 * <AUTHOR> Keller
 * @date : 2022-6-27
 */
@ApiModel(value = "AuditCustomer",description = "费用明细客户")
@Getter
@Setter
public class AuditCustomerDto implements Serializable,Cloneable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value = "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 费用核销编号 */
  @ApiModelProperty(name = "auditCode",notes = "费用核销编号", value = "费用核销编号")
  private String auditCode;
  /** 费用核销明细编号 */
  @ApiModelProperty(name = "auditDetailCode",notes = "费用核销明细编号", value = "费用核销明细编号")
  private String auditDetailCode;
  /** 客户编码 */
  @ApiModelProperty(name = "customerCode",notes = "客户编码编码", value = "客户编码编码")
  private String customerCode;
  /** 客户名称 */
  @ApiModelProperty(name = "customerName",notes = "客户名称", value = "客户名称")
  private String customerName;
  /** 核销金额 */
  @ApiModelProperty(name = "auditAmount",notes = "核销金额", value= "核销金额")
  private BigDecimal auditAmount;

}
