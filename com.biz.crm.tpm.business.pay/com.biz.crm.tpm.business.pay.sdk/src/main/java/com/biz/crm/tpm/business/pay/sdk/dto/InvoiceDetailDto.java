package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.vo.TenantOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel(value = "InvoiceDetailDto", description = "发票使用明细")
public class InvoiceDetailDto extends TenantOpVo {

    @ApiModelProperty("发票明细号")
    private String invoiceDetailNo;

    @ApiModelProperty("发票号码(只允许填写数字和字母)")
    private String invoiceNo;

    @ApiModelProperty("货物或应税劳务名称")
    private String name;

    @ApiModelProperty("税价合计")
    private BigDecimal priceAndTax;

    @ApiModelProperty("不含税金额")
    private BigDecimal amountWithoutTax;

    @ApiModelProperty("税额")
    private BigDecimal taxAmount;

    @ApiModelProperty("是否被使用Y/N")
    private String used;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;
}
