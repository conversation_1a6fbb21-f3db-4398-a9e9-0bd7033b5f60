package com.biz.crm.tpm.business.pay.sdk.service;



import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AccountVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 费用上账主表(AccountVo)表Vo服务接口
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
public interface AccountVoService {

  /**
   * 分页查询数据
   *
   * @param pageable  分页对象
   * @param dto 实体对象
   * @return
   */
  Page<AccountVo> findByConditions(Pageable pageable, AccountDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AccountVo findById(String id);

  /**
   * 新增数据
   *
   * @param dto 实体对象
   * @return 新增结果
   */
  AccountVo create(List<AccountDto>  dto);

  /**
   * 修改新据
   *
   * @param dto 实体对象
   * @return 修改结果
   */
  AccountVo update(AccountDto dto);

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  void delete(List<String> idList);

  /**
   * 新增数据时，查询分页信息
   * @param pageable
   * @param account
   * @return
   */
  Page<AccountVo> findAuditByDto(Pageable pageable, AccountDto account);

  /**
   * 通过核销明细编码集合查询可上账金额，返回MAP k-核销明细编码
   * @param codes
   * @return
   */
  Map<String, AccountVo> findAmountByAuditDetailCode(Set<String> codes);

  /**
   * 启用
   * @param idList
   */
  void enable(List<String> idList);

  /**
   * 通过ID集合查询
   * @return
   */
  List<AccountVo> findByIds(List<String> idList);

  /**
   * 禁用
   * @param idList
   */
  void disable(List<String> idList);
}

