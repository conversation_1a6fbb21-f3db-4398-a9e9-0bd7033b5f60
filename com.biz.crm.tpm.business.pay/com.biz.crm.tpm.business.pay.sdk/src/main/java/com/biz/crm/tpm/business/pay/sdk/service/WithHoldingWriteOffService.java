package com.biz.crm.tpm.business.pay.sdk.service;

import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingWriteOffVo;

import java.util.List;

/**
 * 费用冲销
 */
public interface WithHoldingWriteOffService {

    /**
     * 手动冲销
     *
     * @param codeList
     */
    void handleManual(List<String> codeList);

    /**
     * 费控凭证回传
     *
     * @param dtoList
     */
    void hecVoucherCallback(List<HecCallbackDto> dtoList);

    /**
     * 根据预提vo查询冲销
     * @param voList
     */
    void handleManualByEntities(List<WithHoldingVo> voList);

    List<WithHoldingWriteOffVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes);

    List<WithHoldingWriteOffVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes, List<String> writeOffTypes);

    List<WithHoldingWriteOffVo> findListBySourceCodes(List<String> sourceCodes);
}
