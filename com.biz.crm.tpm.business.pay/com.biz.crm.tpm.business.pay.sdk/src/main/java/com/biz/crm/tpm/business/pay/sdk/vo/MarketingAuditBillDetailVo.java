package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 实体：方案票扣明细;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "MarketingAuditBillDetailVo", description = "方案票扣明细")
@Data
public class MarketingAuditBillDetailVo extends TenantFlagOpVo {

  private static final long serialVersionUID = 9026718001904502872L;
  /**
   * 核销申请名称
   */
  @ApiModelProperty("核销申请名称")
  private String auditName;

  /**
   * 核销申请编号
   */
  @ApiModelProperty("核销申请编号")
  private String auditCode;

  @ApiModelProperty("批次号")
  private String btNo;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("销售单位")
  private String saleUnit;

  @ApiModelProperty("产品编码")
  private String productCode;

  @ApiModelProperty("产品名称")
  private String productName;

  @ApiModelProperty("物料编码")
  private String materialCode;

  @ApiModelProperty("物料名称")
  private String materialName;

  @ApiModelProperty("活动归属年月")
  private String years;

  @ApiModelProperty("未开票金额")
  private BigDecimal unInvoicedAmount;

  @ApiModelProperty("累计未开票金额")
  private BigDecimal unInvoicedCumulativeAmount;

  @ApiModelProperty("单次折让率")
  private BigDecimal singleDiscountsRate;

  @ApiModelProperty("单次折让率(显示)")
  private String singleDiscountsRateStr;

  @ApiModelProperty("累计折让率")
  private BigDecimal cumulativeDiscountsRate;

  @ApiModelProperty("累计折让率(显示)")
  private String cumulativeDiscountsRateStr;

  @ApiModelProperty("订单数量")
  private BigDecimal quantity;

  @ApiModelProperty("订单单价")
  private BigDecimal price;

  @ApiModelProperty("订单金额")
  private BigDecimal amount;

  @ApiModelProperty("错误描述")
  private String errMsg;

  @ApiModelProperty("校验状态")
  private Boolean checkFlag = Boolean.TRUE;
}
