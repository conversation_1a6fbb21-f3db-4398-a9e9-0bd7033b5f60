package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "CreditOrder", description = "贷项订单")
@Data
public class CreditOrderVo extends TenantFlagOpVo {

    @ApiModelProperty("贷项订单编号")
    private String creditCode;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("订单类型")
    private String orderType;

    @ApiModelProperty("审批状态")
    private String status;

    @ApiModelProperty("批次号")
    private String btNo;

    @ApiModelProperty("一级部门编码")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称")
    private String departmentOneName;

    @ApiModelProperty("二级部门编码")
    private String departmentTwoCode;

    @ApiModelProperty("二级部门名称")
    private String departmentTwoName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("凭证号")
    private String voucherCode;

    @ApiModelProperty("推送状态")
    private String pushStatus;

    @ApiModelProperty("失败原因")
    private String failMsg;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("核销申请名称")
    private String auditName;

    @ApiModelProperty("核销申请编号")
    private String auditCode;

    @ApiModelProperty("上账日期")
    private String accountDate;
    private String accountDateStart;
    private String accountDateEnd;

    @ApiModelProperty("金额汇总")
    private BigDecimal amountTotal;

}
