package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditBillDetailVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 实体：方案结案;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "MarketingAudit", description = "方案结案")
@Data
public class MarketingAuditDto extends TenantFlagOpDto {
  /**
   * 核销申请名称
   */
  @ApiModelProperty(name = "核销申请名称", notes = "核销申请名称")
  private String auditName;

  /**
   * 核销申请编号
   */
  @ApiModelProperty(name = "核销申请编号", notes = "核销申请编号")
  private String auditCode;

  /**
   * 核销金额汇总
   */
  @ApiModelProperty(name = "核销金额汇总", notes = "核销金额汇总")
  private BigDecimal totalApplyAmount;

  /**
   * 结案状态
   */
  @ApiModelProperty("结案状态")
  private String auditStatus;

  /**
   * 审批状态
   */
  @ApiModelProperty("审批状态")
  private String status;

  @ApiModelProperty("审批单号")
  private String processNumber;

  @ApiModelProperty("推送日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("OA人员id")
  private String oaId;

  @ApiModelProperty("OA人员账号")
  private String oaUserName;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  /**
   * 费用核销附件信息
   */
  @ApiModelProperty(name = "auditFiles", notes = "费用核销明细信息", value = "费用核销明细信息")
  private List<AuditFilesDto> auditFiles;

  /**
   * 票扣明细
   */
  @ApiModelProperty("票扣明细")
  private List<MarketingAuditBillDetailVo> auditBillDetails;

  @ApiModelProperty("兑付明细")
  private List<MarketingAuditDetailDto> cacheList;
}
