package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：活动预付收款信息;
 *
 * <AUTHOR> yaoyongming
 * @date : 2024-6-4
 */
@Data
@ApiModel(value = "ActivityPrepayPayee", description = "活动预付收款信息")
public class ActivityPrepayPayeeDto extends TenantFlagOpDto {

    @ApiModelProperty("预付编号")
    private String prepayCode;

    @ApiModelProperty("收款方类型")
    private String payeeType;

    @ApiModelProperty("收款方编码")
    private String payeeCode;

    @ApiModelProperty("收款方Sap编码")
    private String supplierErpCode;

    @ApiModelProperty("收款方名称")
    private String payeeName;

    @ApiModelProperty("收款方账号")
    private String payeeAccount;

    @ApiModelProperty("户名")
    private String accountName;

    @ApiModelProperty("银联行号")
    private String interbankNumber;

    @ApiModelProperty("开户行")
    private String bankName;

    @ApiModelProperty("本次付款金额")
    private BigDecimal thisPayAmount;

    @ApiModelProperty("期望付款日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date payDate;

    @ApiModelProperty("付款方式")
    private String payType;

    @ApiModelProperty("付款用途")
    private String payPurpose;

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("回单")
    private String receipt;

    @ApiModelProperty("付款状态")
    private String payStatus;

    @ApiModelProperty("收款行编号")
    private String lineCode;

    @ApiModelProperty("付款成功时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paySucessDate;
}