package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @describe: 费用预提类型枚举
 * @createTime 2022年06月27日 14:39:00
 */
@AllArgsConstructor
@Getter
public enum WithHoldingTypeEnum {
  NOT_AUDIT("not_audit", "not_audit", "已申请未结案", "1"),
  HANDLE("handle", "handle", "手动预提", "2"),
  NOT_CASH("not_cash", "not_cash", "已结案未兑付", "3"),
  POD_DIFF("podDiff", "podDiff", "pod差异", "4"),
  ORDER_CLOSE_DETAIL("orderCloseDetail", "orderCloseDetail", "订单关闭", "5"),
  POD_DIFF_ADD("podDiffAdd", "podDiffAdd", "pod差异-增加", "6"),
  POD_DIFF_REDUCE("podDiffReduce", "podDiffReduce", "pod差异-减少", "7"),
  ;
  /** 系统key */
  private String key;
  /** 字典编码 */
  private String dictCode;
  /** 字典值 */
  private String value;
  /** 字典排序 */
  private String order;

  public static WithHoldingTypeEnum findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    return Arrays.stream(WithHoldingTypeEnum.values()).filter(o->o.getDictCode().equals(code)).findFirst().orElse(null);
  }
}
