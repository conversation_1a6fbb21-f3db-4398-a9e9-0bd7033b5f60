package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "DeliveryReplenishmentPoolVo", description = "发货费用报表")
@Data
public class DeliveryReplenishmentPoolVo extends TenantFlagOpVo {

    /**
     * 费用池编号
     */
    @ApiModelProperty(value = "费用池编号")
    private String poolCode;

    /**
     * 费用池明细编号
     */
    @ApiModelProperty(value = "费用池明细编号")
    private String poolDetailCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * @see com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolTypeEnum
     * 货补费用池类型
     */
    @ApiModelProperty(value = "货补费用池类型")
    private String replenishmentPoolType;

    @ApiModelProperty(value = "出库单")
    private String businessCode;

    @ApiModelProperty(value = "业务编码")
    private String parentBusinessCode;

    @ApiModelProperty(value = "出库带走费用")
    private BigDecimal operationAmount;

    @ApiModelProperty(value = "出库时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    @ApiModelProperty("是否应冲销(Y/N)")
    private String beWriteOff;

    @ApiModelProperty(value = "冲销状态")
    private String writeOffStatus;

    @ApiModelProperty("职位编码")
    private String positionCode;
}
