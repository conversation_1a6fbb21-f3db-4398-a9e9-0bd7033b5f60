package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailPageVo;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 发票使用明细(InvoiceDeail)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
public interface InvoiceDetailService {

  void saveBatchList(List<InvoiceDetailVo> detailList,String oldInvoiceNo,String nowInvoiceNo);

  List<InvoiceDetailVo> findListByInvoiceNoList(List<String> invoiceNoList);

  Page<InvoiceDetailPageVo> findInvoiceDetailList(Pageable pageable,InvoiceDetailPageVo vo);

  void deleteByInvoiceNoList(List<String> invoiceNoList);

}
