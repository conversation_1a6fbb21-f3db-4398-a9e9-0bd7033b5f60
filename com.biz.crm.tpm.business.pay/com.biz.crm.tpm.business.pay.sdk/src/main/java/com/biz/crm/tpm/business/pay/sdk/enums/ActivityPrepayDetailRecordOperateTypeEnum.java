package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 预付明细操作类型
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@AllArgsConstructor
public enum ActivityPrepayDetailRecordOperateTypeEnum {

    INIT("init", "期初"),
    ADD("add", "追加"),
    CARRY("carry", "结转"),
    WRITE_OFF("write_off", "冲销"),
    ;

    private String code;
    private String value;

    public static ActivityPrepayDetailRecordOperateTypeEnum getEnumByCode(String code) {
        for (ActivityPrepayDetailRecordOperateTypeEnum value : values()) {
            if (StringUtils.equals(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
