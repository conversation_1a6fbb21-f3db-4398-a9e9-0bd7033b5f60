package com.biz.crm.tpm.business.pay.sdk.service;


import com.biz.crm.business.common.page.cache.service.BusinessPageCacheService;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashInvoiceVo;

import java.util.List;

public interface FeeCashInvoiceService extends BusinessPageCacheService<FeeCashInvoiceVo, FeeCashInvoiceDto> {

    /**
     * 按结案明细更新关联的发票
     *
     * @param cacheKey
     * @param auditDetailCodes
     * @param itemList
     */
    void updateItemCache(String cacheKey, List<String> auditDetailCodes, List<FeeCashInvoiceDto> itemList);

    /**
     * 发票金额汇总
     *
     * @param cacheKey
     * @return
     */
    FeeCashInvoiceVo invoiceTotal(String cacheKey);

    /**
     * 查询剩余可报销金额
     *
     * @param cacheKey
     * @param auditDetailCode
     * @return
     */
    List<FeeCashInvoiceDto> availableReimbursementAmount(String cacheKey, String auditDetailCode);
}
