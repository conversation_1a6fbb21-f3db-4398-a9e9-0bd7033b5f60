package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 实体：活动预付明细跟踪;
 *
 * <AUTHOR> ya<PERSON><PERSON><PERSON>
 * @date : 2024-6-5
 */
@ApiModel(value = "ActivityPrepayDetailRecordItem", description = "活动预付明细跟踪明细")
@Data
public class ActivityPrepayDetailRecordItemDto extends TenantFlagOpDto {

  @ApiModelProperty("预付编号")
  private String prepayCode;

  @ApiModelProperty("预付名称")
  private String prepayName;

  @ApiModelProperty("预付明细编号")
  private String prepayDetailCode;

  @ApiModelProperty("方案编码")
  private String schemeCode;

  @ApiModelProperty("方案名称")
  private String schemeName;

  @ApiModelProperty("方案明细编码")
  private String schemeDetailCode;

  @ApiModelProperty("类型")
  private String type;

  @ApiModelProperty("业务编码")
  private String businessCode;

  @ApiModelProperty("业务名称")
  private String businessName;

  @ApiModelProperty("金额")
  private BigDecimal amount;
}