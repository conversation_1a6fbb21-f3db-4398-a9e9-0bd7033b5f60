package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月27日 15:33:00
 */
@Data
public class WithHoldingWriteOffVo extends TenantFlagOpVo {


  /**
   * 推送状态 枚举
   */
  @ApiModelProperty(name = "推送状态", notes = "推送状态")
  private String pushStatus;

  @ApiModelProperty("费控单号")
  private String externalCode;

  /**
   * 预提类型 枚举
   */
  @ApiModelProperty(name = "预提类型", notes = "预提类型")
  private String withHoldingType;

  /**
   * 预提编号
   */
  @ApiModelProperty(name = "预提编号", notes = "预提编号")
  private String withHoldingCode;

  /**
   * 预提年月
   */
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM")
  @DateTimeFormat(pattern = "yyyy-MM")
  private Date withHoldingYears;


  /**
   * 活动编号
   */
  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  private String activitiesName;

  /**
   * 活动明细编码 预提表字段
   */
  @ApiModelProperty(name = "activitiesDetailCode", notes = "活动明细编码 预提表字段", value = "活动明细编码 预提表字段")
  private String activitiesDetailCode;

  /**
   * 支付方式 冗余活动表字段
   */
  @ApiModelProperty("支付方式")
  private String payType;

  /**
   * 支付方式名称 冗余活动表字段
   */
  @ApiModelProperty("支付方式名称")
  private String payTypeName;


  /**
   * 预算科目编码
   */
  @ApiModelProperty(name = "预算科目编码", notes = "")
  private String budgetSubjectsCode;

  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "预算科目名称", notes = "")
  private String budgetSubjectsName;

  /**
   * 活动大类编码
   */
  @ApiModelProperty(name = "活动大类编码", notes = "活动大类编码")
  private String costTypeCategoryCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "活动大类名称", notes = "活动大类名称")
  private String costTypeCategoryName;

  /**
   * 活动细类编码
   */
  @ApiModelProperty(name = "活动细类编码", notes = "活动细类编码")
  private String costTypeDetailCode;

  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "活动细类名称", notes = "活动系类名称")
  private String costTypeDetailName;

  /**
   * 组织编号
   */
  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  private String orgName;


  /**
   * 客户编号
   */
  @ApiModelProperty(name = "客户编号", notes = "客户编号")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty(name = "客户名称", notes = "客户名称")
  private String customerName;

  /**
   * 门店编号
   */
  @ApiModelProperty(name = "门店编号", notes = "门店编号")
  private String terminalCode;

  /**
   * 门店名称
   */
  @ApiModelProperty(name = "门店名称", notes = "门店名称")
  private String terminalName;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "申请金额", notes = "申请金额")
  private BigDecimal applyAmount;

  /**
   * 预提金额
   */
  @ApiModelProperty(name = "预提金额", notes = "预提金额")
  private BigDecimal withHoldingAmount;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "支付方式", notes = "支付方式")
  private String payBy;

  /**
   * 支付方式名称
   */
  @ApiModelProperty(name = "支付方式名称", notes = "支付方式名称")
  private String payByName;

  /**
   * 上账金额
   */
  @ApiModelProperty(name = "上账金额", notes = "上账金额")
  private BigDecimal accountAmount;



  @ApiModelProperty("费用归属年月")
  private String years;

  @ApiModelProperty("预提年月")
  private String yearMonthLy;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("订单编码")
  private String orderCode;

  @ApiModelProperty("订单名称")
  private String orderName;

  @ApiModelProperty("归属部门编码")
  private String belongDepartmentCode;

  @ApiModelProperty("归属部门名称")
  private String belongDepartmentName;

  @ApiModelProperty("承担部门编码")
  private String bearDepartmentCode;

  @ApiModelProperty("承担部门名称")
  private String bearDepartmentName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("计提来源")
  private String withHoldingSource;

  @ApiModelProperty("管报计提金额")
  private BigDecimal withHoldingReportAmount;

  @ApiModelProperty("调减金额")
  private BigDecimal reduceAmount;

  @ApiModelProperty("实际金额")
  private BigDecimal actualAmount;

  @ApiModelProperty("公式及取值")
  private String formulaInfo;

  @ApiModelProperty("凭证号")
  private String voucherCode;

  @ApiModelProperty("失败原因")
  private String failMsg;

  @ApiModelProperty("审批状态")
  private String status;

  @ApiModelProperty("推送日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("审批流程编码")
  private String processKey;

  @ApiModelProperty("预算编码")
  private String budgetCode;

  @ApiModelProperty("冲销类型")
  private String writeOffType;

  @ApiModelProperty("冲销金额")
  private BigDecimal writeOffAmount;

  @ApiModelProperty("冲销编码")
  private String writeOffCode;

  /** 冲销时间 */
  @ApiModelProperty(name = "writeOffTime",notes = "冲销时间", value= "冲销时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date writeOffTime;

  @ApiModelProperty("冲销年月")
  private String writeOffYears;

  @ApiModelProperty("业务编码")
  private String businessCode;

  @ApiModelProperty("是否当期费用(Y/N)")
  private String beThisFee;

  @ApiModelProperty("查看流程日志所需地址")
  private String hecReceiptUrl;

  @ApiModelProperty("冲销来源单号")
  private String sourceCode;

  @ApiModelProperty("编码规则")
  private String ruleCode;
}
