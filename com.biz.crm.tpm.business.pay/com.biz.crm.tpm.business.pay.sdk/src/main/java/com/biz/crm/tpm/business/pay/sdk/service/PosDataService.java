package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
public interface PosDataService {

    Page<PosDataVo> findList(Pageable pageable, PosDataVo vo);

    void create(PosDataVo vo);

    void confirmData(List<String> idList);

    void deleteBatchByIdList(List<String> idList);

}
