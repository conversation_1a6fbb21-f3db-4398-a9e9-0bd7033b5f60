package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 冲销类型
 */
@AllArgsConstructor
@Getter
public enum WriteOffTypeEnum {
  AUDIT("audit", "audit", "结案冲销", "1"),
  CASH("cash", "cash", "兑付冲销", "2"),
  CLOSE("close", "close", "关闭冲销", "3"),
  ORDER("order", "order", "订单冲销", "4"),
  SPECIAL("special", "special", "特殊计提冲销", "5"),
  HANDLE("handle", "handle", "手动冲销", "6"),
  ;
  /** 系统key */
  private String key;
  /** 字典编码 */
  private String dictCode;
  /** 字典值 */
  private String value;
  /** 字典排序 */
  private String order;

  public static WriteOffTypeEnum findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    return Arrays.stream(WriteOffTypeEnum.values()).filter(o->o.getDictCode().equals(code)).findFirst().orElse(null);
  }
}
