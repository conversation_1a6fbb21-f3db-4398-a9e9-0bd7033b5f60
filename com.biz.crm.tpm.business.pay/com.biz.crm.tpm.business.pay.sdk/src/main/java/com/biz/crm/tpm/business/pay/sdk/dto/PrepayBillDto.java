package com.biz.crm.tpm.business.pay.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 参数传递dto：活动预付账单;
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@ApiModel(value = "PrepayBill",description = "活动预付账单")
@Getter
@Setter
public class PrepayBillDto implements Serializable,Cloneable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value = "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 活动明细编码 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value = "活动明细编码")
  private String activitiesDetailCode;
  /** 申请金额 */
  @ApiModelProperty(name = "applyAmount",notes = "申请金额", value = "申请金额")
  private BigDecimal applyAmount;
  /** 已预付金额 */
  @ApiModelProperty(name = "prepaidAmount",notes = "预付金额", value = "预付金额")
  private BigDecimal prepaidAmount;

}