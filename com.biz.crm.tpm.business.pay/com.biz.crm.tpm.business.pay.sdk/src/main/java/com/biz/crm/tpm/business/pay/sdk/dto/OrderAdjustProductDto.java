package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "OrderAdjustProduct", description = "订单调整产品")
@Data
public class OrderAdjustProductDto extends TenantFlagOpDto {

    @ApiModelProperty("调整编码")
    private String adjustCode;

    @ApiModelProperty("调整名称")
    private String adjustName;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("销售单位")
    private String saleUnit;

    @ApiModelProperty("金额")
    private BigDecimal amount;

}
