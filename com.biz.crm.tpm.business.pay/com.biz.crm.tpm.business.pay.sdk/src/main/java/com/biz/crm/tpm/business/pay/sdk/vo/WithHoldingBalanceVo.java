package com.biz.crm.tpm.business.pay.sdk.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 计提余额数据视图VO
 * 对应WithHoldingBalanceDataViewRegister的SQL查询结果
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
@Data
@ApiModel(value = "WithHoldingBalanceVo", description = "计提余额数据")
public class WithHoldingBalanceVo {

    @ApiModelProperty("预提编号")
    @JsonProperty("with_holding_code")
    private String withHoldingCode;

    @ApiModelProperty("预提年月")
    @JsonProperty("year_month_ly")
    private String yearMonthLy;

    @ApiModelProperty("费用归属年月")
    @JsonProperty("years")
    private String years;

    @ApiModelProperty("业务编码")
    @JsonProperty("business_code")
    private String businessCode;

    @ApiModelProperty("活动明细编码")
    @JsonProperty("activities_detail_code")
    private String activitiesDetailCode;

    @ApiModelProperty("活动编号")
    @JsonProperty("activities_code")
    private String activitiesCode;

    @ApiModelProperty("活动名称")
    @JsonProperty("activities_name")
    private String activitiesName;

    @ApiModelProperty("订单编码")
    @JsonProperty("order_code")
    private String orderCode;

    @ApiModelProperty("订单名称")
    @JsonProperty("order_name")
    private String orderName;

    @ApiModelProperty("活动大类编码")
    @JsonProperty("cost_type_category_code")
    private String costTypeCategoryCode;

    @ApiModelProperty("活动大类名称")
    @JsonProperty("cost_type_category_name")
    private String costTypeCategoryName;

    @ApiModelProperty("活动细类编码")
    @JsonProperty("cost_type_detail_code")
    private String costTypeDetailCode;

    @ApiModelProperty("活动细类名称")
    @JsonProperty("cost_type_detail_name")
    private String costTypeDetailName;

    @ApiModelProperty("预算科目编码")
    @JsonProperty("budget_subjects_code")
    private String budgetSubjectsCode;

    @ApiModelProperty("预算科目名称")
    @JsonProperty("budget_subjects_name")
    private String budgetSubjectsName;

    @ApiModelProperty("归属部门编码")
    @JsonProperty("belong_department_code")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    @JsonProperty("belong_department_name")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    @JsonProperty("bear_department_code")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    @JsonProperty("bear_department_name")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    @JsonProperty("cost_center_code")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @JsonProperty("cost_center_name")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    @JsonProperty("customer_code")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @JsonProperty("customer_name")
    private String customerName;

    @ApiModelProperty("品项名称")
    @JsonProperty("item_name")
    private String itemName;

    @ApiModelProperty("预提类型")
    @JsonProperty("with_holding_type")
    private String withHoldingType;

    @ApiModelProperty("兑付方式")
    @JsonProperty("pay_by")
    private String payBy;

    @ApiModelProperty("申请金额总计")
    @JsonProperty("apply_amount_total")
    private BigDecimal applyAmountTotal;

    @ApiModelProperty("预提金额总计")
    @JsonProperty("with_holding_amount_total")
    private BigDecimal withHoldingAmountTotal;

    @ApiModelProperty("冲销金额总计")
    @JsonProperty("write_off_amount_total")
    private BigDecimal writeOffAmountTotal;

    @ApiModelProperty("余额")
    @JsonProperty("balance")
    private BigDecimal balance;

    // 根据TPM-计提余额报表_副本.docx文档要求新增的金额字段

    @ApiModelProperty("计提前兑付金额")
    @JsonProperty("pre_cash_amount")
    private BigDecimal preCashAmount;

    @ApiModelProperty("结案冲销金额")
    @JsonProperty("audit_write_off_amount")
    private BigDecimal auditWriteOffAmount;

    @ApiModelProperty("兑付冲销金额")
    @JsonProperty("cash_write_off_amount")
    private BigDecimal cashWriteOffAmount;

    @ApiModelProperty("关闭冲销金额")
    @JsonProperty("close_write_off_amount")
    private BigDecimal closeWriteOffAmount;

    @ApiModelProperty("发货冲销金额")
    @JsonProperty("delivery_write_off_amount")
    private BigDecimal deliveryWriteOffAmount;

    @ApiModelProperty("账扣未核销金额")
    @JsonProperty("account_unverified_amount")
    private BigDecimal accountUnverifiedAmount;

    @ApiModelProperty("电汇未核销金额")
    @JsonProperty("wire_unverified_amount")
    private BigDecimal wireUnverifiedAmount;

    @ApiModelProperty("结案未兑付金额")
    @JsonProperty("audit_uncashed_amount")
    private BigDecimal auditUncashedAmount;

    @ApiModelProperty("未完全结案余额")
    @JsonProperty("incomplete_audit_balance")
    private BigDecimal incompleteAuditBalance;

    @ApiModelProperty("管报实际计提金额")
    @JsonProperty("actual_report_amount")
    private BigDecimal actualReportAmount;

    @ApiModelProperty("进货返利冲销金额")
    @JsonProperty("back_write_off_amount_total")
    private BigDecimal backWriteOffAmountTotal;

    @ApiModelProperty("管报计提余额")
    @JsonProperty("back_balance")
    private BigDecimal backBalance;
}
