package com.biz.crm.tpm.business.pay.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 客户+公司+产品+年月 SAP未开票金额
 *
 * <AUTHOR>
 * @version v1.0.0
 * @date 2024/9/29 18:01
 */
@ApiModel(value = "MarketingAuditSapInvoiceVo", description = "客户+公司+产品+年月SAP未开票金额")
@Data
public class MarketingAuditSapInvoiceVo implements Serializable {

    private static final long serialVersionUID = -3739359168048581701L;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("活动归属年月")
    private String yearMonthLy;

    @ApiModelProperty("未开票金额")
    private BigDecimal unInvoicedAmount;

}
