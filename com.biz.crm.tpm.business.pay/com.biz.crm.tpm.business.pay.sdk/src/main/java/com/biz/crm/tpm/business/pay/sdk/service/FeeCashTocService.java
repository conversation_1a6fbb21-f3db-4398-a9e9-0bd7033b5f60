package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTocDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTocVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * TOC
 */
public interface FeeCashTocService {

    /**
     * 获取所有缓存
     *
     * @param cacheKey
     * @return
     */
    List<FeeCashTocDto> findAllCacheList(String cacheKey);

    /**
     * 清空缓存
     *
     * @param cacheKey
     */
    void clearAllCache(String cacheKey);

    /**
     * TOC汇总
     *
     * @param cacheKey
     */
    FeeCashTocVo findTocTotal(String cacheKey);

    /**
     * 分页查询数据
     *
     * @param pageable        分页对象
     * @param dto 实体对象
     */
    Page<FeeCashTocVo> findByConditions(Pageable pageable, FeeCashTocVo dto);
}
