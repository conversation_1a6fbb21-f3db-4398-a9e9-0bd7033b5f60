package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 冲销状态
 */
@AllArgsConstructor
@Getter
public enum WriteOffStatusEnum {

  WAIT_WRITE_OFF("wait_write_off", "wait_write_off", "待冲销", "1"),
  ON_WRITE_OFF("on_write_off", "on_write_off", "已冲销", "2"),
      ;
  /** 系统key */
  private String key;
  /** 字典编码 */
  private String dictCode;
  /** 字典值 */
  private String value;
  /** 字典排序 */
  private String order;
}
