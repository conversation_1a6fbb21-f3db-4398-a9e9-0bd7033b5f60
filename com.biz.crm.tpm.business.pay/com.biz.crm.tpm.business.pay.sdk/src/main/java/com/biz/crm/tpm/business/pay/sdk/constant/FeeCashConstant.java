package com.biz.crm.tpm.business.pay.sdk.constant;

public interface FeeCashConstant {

    /**
     * 缓存分页键
     */
    String CACHE_KEY_PREFIX = "fee_cash_ticket:item_cache:new:";
    String CACHE_KEY_PREFIX_TOC = "fee_cash_toc:item_cache:new:";
    String CACHE_KEY_PREFIX_DETAIL = "fee_cash_detail:item_cache:new:";
    String CACHE_KEY_PREFIX_INVOICE = "fee_cash_invoice:item_cache:new:";
    String CACHE_KEY_PREFIX_PREPAY = "fee_cash_prepay:item_cache:new:";
    String CACHE_KEY_PREFIX_MATERIAL = "fee_cash_material:item_cache:new:";

    /**
     * 编码前缀
     */
    String PREFIX_CODE = "DF";

    /**
     * 明细编码前缀
     */
    String PREFIX_CODE_MX = "DFMX";

    /**
     * 收款明细编码前缀
     */
    String PREFIX_CODE_SKMX = "DFSKMX";

    /**
     * 贷项订单编码前缀
     */
    String PREFIX_CODE_DXDD = "DXDD";

    /**
     * 贷项订单推送SAP锁
     */
    String PUSH_SAP_LOCK_PREFIX = "push:sap:creditOrder:";

    /**
     * 贷项订单推送SAP接口
     */
    String PUSH_SAP_INTERFACE = "ZSTDINF012";

    /**
     * TOC支付
     */
    String TOC_PAYMENT = "TOC_payment";

    /**
     * 费用兑付锁
     */
    String LOCK_PREFIX = "tpm:fee_cash:lock:";
}
