package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 费控回传
 * 电汇付款状态 单据类型
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/9 14:08
 */
@Getter
@AllArgsConstructor
public enum HecPayBusinessTypeEnum {
    TPM_ACTIVITY_PREPAY_HEC("TPM_ACTIVITY_PREPAY_HEC","EXP_REQUISITION", "活动预付"),
    TPM_FEE_CASH_HEC("TPM_FEE_CASH_HEC", "EXP_REPORT", "费用兑付"),
    ;
    private String code;

    private String hecCode;

    private String desc;

    public static HecPayBusinessTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (HecPayBusinessTypeEnum typeEnum : values()) {
            if (StringUtils.equals(code, typeEnum.getCode())) {
                return typeEnum;
            }
        }
        return null;
    }

}
