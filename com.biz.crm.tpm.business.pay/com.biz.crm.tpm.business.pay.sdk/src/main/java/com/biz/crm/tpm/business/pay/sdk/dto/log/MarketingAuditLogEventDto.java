package com.biz.crm.tpm.business.pay.sdk.dto.log;

import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDto;
import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import lombok.Data;

import java.util.List;

/**
 * 费用核销 业务日志Dto
 * <AUTHOR> <PERSON>
 * @date : 2022-7-1
 */
@Data
public class MarketingAuditLogEventDto implements NebulaEventDto {
  /**
   * 原始
   */
  private MarketingAuditDto original;
  /**
   * 最新
   */
  private MarketingAuditDto newest;

  /**
   * 最新
   */
  private List<MarketingAuditDto> newestList;
}
