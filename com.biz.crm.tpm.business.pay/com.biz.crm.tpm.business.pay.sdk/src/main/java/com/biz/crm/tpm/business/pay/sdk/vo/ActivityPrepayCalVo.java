package com.biz.crm.tpm.business.pay.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@ApiModel(value = "ActivityPrepayCalVo", description = "活动预付计算vo")
public class ActivityPrepayCalVo {

    @ApiModelProperty("活动预付id")
    private String id;

    @ApiModelProperty("预付编号")
    private String prepayCode;

    @ApiModelProperty("公司代码")
    private String companyCode;

    @ApiModelProperty("收款方编码")
    private String payeeCode;

    @ApiModelProperty("收款方Sap编码")
    private String supplierErpCode;

    @ApiModelProperty("收款方名称")
    private String payeeName;

    @ApiModelProperty("预付明细")
    private List<ActivityPrepayDetailVo> prepayDetailVos;

    @ApiModelProperty("收款信息")
    private List<ActivityPrepayPayeeVo> payeeList;
}
