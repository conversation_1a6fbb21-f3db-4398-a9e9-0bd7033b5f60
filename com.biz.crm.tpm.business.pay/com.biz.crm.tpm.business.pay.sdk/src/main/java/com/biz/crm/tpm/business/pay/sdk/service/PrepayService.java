package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDto;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayActivityItemVo;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 活动预付;(tpm_prepay_activities)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-22
 */
public interface PrepayService {

  /**
   * 生成操作标记
   */
  String preSave();

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<PrepayVo> findByConditions(Pageable pageable, PrepayDto dto);

  /**
   * 分页活动数据
   *
   * @param pageable
   * @param dto
   * @return
   */
  Page<ActivitiesVo> findActivitiesByConditions(Pageable pageable, ActivitiesDto dto);

  /**
   * 分页活动明细数据
   *
   * @param pageable
   * @param dto
   * @return
   */
  Page<PrepayActivityItemVo> findActivitiesDetailByConditions(Pageable pageable, ActivitiesDetailDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  PrepayVo findById(String id);

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<PrepayVo> findByIds(List<String> ids);

  /**
   * 通过比编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  PrepayVo findByCode(String code);

  /**
   * 根绝业务编号prepayCode获取业务数据
   *
   * @param prepayCode
   * @return 单条数据
   */
  PrepayVo findByPrepayCode(String prepayCode);

  /**
   * 根绝业务编号prepayCode获取业务数据
   *
   * @param prepayCodes
   * @return 多条数据
   */
  List<PrepayVo> findByPrepayCodes(List<String> prepayCodes);

  /**
   * 新增数据
   *
   * @param prepayDto 实体对象
   * @return 新增结果
   */
  PrepayVo create(PrepayDto prepayDto);

  /**
   * 批量新增
   *
   * @param prepayDtos
   * @return
   */
  List<PrepayVo> createBatch(List<PrepayDto> prepayDtos);

  /**
   * 修改数据
   *
   * @param prepayDto 实体对象
   * @return 修改结果
   */
  PrepayVo update(PrepayDto prepayDto);

  /**
   * 批量修改据
   *
   * @param prepayDtos 实体对象
   * @return 修改结果
   */
  List<PrepayVo> updateBatch(List<PrepayDto> prepayDtos);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);

}