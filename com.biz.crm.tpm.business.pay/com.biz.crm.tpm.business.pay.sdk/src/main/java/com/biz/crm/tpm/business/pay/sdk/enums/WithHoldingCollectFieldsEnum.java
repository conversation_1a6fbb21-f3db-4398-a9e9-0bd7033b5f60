package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum WithHoldingCollectFieldsEnum {
    collectCode("collectCode"),
    deptCode("deptCode"),
    yearMonthLy("yearMonthLy"),
    withHoldingAmount("withHoldingAmount"),
    withHoldingRatio("withHoldingRatio"),
    budgetRatio("budgetRatio"),
    remark("remark"),
    title("title"),
    ;

    private String dictCode;

    public static WithHoldingCollectFieldsEnum findByCode(String code) {
        Optional<WithHoldingCollectFieldsEnum> first = Stream.of(WithHoldingCollectFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}