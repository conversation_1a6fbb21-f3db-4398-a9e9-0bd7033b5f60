package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DeliveryReplenishmentPoolDetailDto extends TenantFlagOpDto {

    /**
     * 费用池编号
     */
    @ApiModelProperty(value = "费用池编号")
    private String poolCode;

    /**
     * 费用池明细编号
     */
    @ApiModelProperty(value = "费用池明细编号")
    private String poolDetailCode;

    @ApiModelProperty(value = "费用池明细编号")
    private String parentCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * @see com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolTypeEnum
     * 货补费用池类型
     */
    @ApiModelProperty(value = "货补费用池类型")
    private String replenishmentPoolType;

    @ApiModelProperty(value = "出库单")
    private String businessCode;

    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "业务编码")
    private String parentBusinessCode;

    @ApiModelProperty(value = "出库带走费用")
    private BigDecimal operationAmount;

    @ApiModelProperty(value = "出库时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    @ApiModelProperty("是否应冲销(Y/N)")
    private String beWriteOff;

    @ApiModelProperty(value = "冲销状态")
    private String writeOffStatus;

    @ApiModelProperty("唯一标识")
    private String uniqueKey;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("发货时间")
    private String deliveryTime;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("兑付明细编号")
    private String cashDetailCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("核销申请编号")
    private String auditCode;

    @ApiModelProperty("核销明细编号")
    private String auditDetailCode;

    private String tempCode;

    @ApiModelProperty("编码规则")
    private String ruleCode;
}
