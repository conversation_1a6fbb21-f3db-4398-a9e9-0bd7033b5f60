package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 费控回传
 * 电汇付款状态 付款状态
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/9 14:08
 */
@Getter
@AllArgsConstructor
public enum HecPayStatusTypeEnum {
    SUCCESS_PAY("success_pay", "付款成功"),
    RETURN_TICKETS("return_tickets", "退票"),
    CASHIER_RETURNS("cashier_returns", "出纳退回"),
    NOT_PAY("not_pay", "待支付"),
    ;
    private String code;

    private String desc;

    public static HecPayStatusTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (HecPayStatusTypeEnum typeEnum : values()) {
            if (StringUtils.equals(code, typeEnum.getCode())) {
                return typeEnum;
            }
        }
        return null;
    }

}
