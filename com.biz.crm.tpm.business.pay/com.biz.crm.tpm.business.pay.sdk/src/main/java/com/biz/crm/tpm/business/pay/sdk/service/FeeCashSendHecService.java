package com.biz.crm.tpm.business.pay.sdk.service;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDto;

import java.util.List;

/**
 * 费用兑付 推送 费控(hec)
 *
 * @return
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/9 16:40
 */
public interface FeeCashSendHecService {

    /**
     * 提交身体
     *
     * @param idList
     * @return com.biz.crm.business.common.sdk.model.Result<java.lang.String>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/15 12:57
     */
    List<String> submitBeforeGetCodes(List<String> idList);

    /**
     * 新增保存并提交
     *
     * @param dto         实体对象
     * @param cacheKey
     * @param cacheKeyToc
     * @return 新增结果
     */
    List<String> submitBeforeCreateGetCodes(FeeCashDto dto, String cacheKey, String cacheKeyToc,
                                            String cacheKeyDetail, String cacheKeyPrepay, String cacheKeyInvoice, String cacheKeyMaterial);

    /**
     * 编辑保存并提交
     *
     * @param dto         实体对象
     * @param cacheKey
     * @param cacheKeyToc
     * @return 修改结果
     */
    List<String> submitBeforeUpdateGetCode(FeeCashDto dto, String cacheKey, String cacheKeyToc,
                                           String cacheKeyDetail, String cacheKeyPrepay, String cacheKeyInvoice, String cacheKeyMaterial);

    /**
     * 修改银行卡信息保存并提交
     *
     * @param dto         实体对象
     * @param cacheKey
     * @param cacheKeyToc
     * @return 修改结果
     */
    Result<String> submitUpdateBankInfo(FeeCashDto dto, String cacheKey, String cacheKeyToc);

    /**
     * 删除
     *
     * @param idList
     * @return
     */
    Result<String> delete(List<String> idList);

    /**
     * 推送费控  提兑付类型=费用兑付 兑付方式=电汇/账扣 时 推送费控
     *
     * @param sendHecList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/9 17:04
     */
    Result<String> feeSendHec(List<FeeCashDto> sendHecList);

    /**
     * 推送费控 兑付类型=账扣预付/电汇预付
     *
     * @param sendHecList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/9 17:04
     */
    Result<String> wireAndAccountSendHec(List<FeeCashDto> sendHecList);

    /**
     * 查询费控电子回单
     *
     * @param
     * @param flagType
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/11 19:08
     */
    void queryHecElectronicReceipt(String flagType);

    Result<String> valAndSendList(List<String> codes, boolean beBank);
}
