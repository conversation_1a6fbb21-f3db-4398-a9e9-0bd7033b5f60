package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

@ApiModel(value = "FeeCashPrepay", description = "费用预付明细")
@Data
public class FeeCashPrepayDto extends TenantFlagOpDto {

    @ApiModelProperty("兑付名称")
    private String cashName;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("预付编号")
    private String prepayCode;

    @ApiModelProperty("预付名称")
    private String prepayName;

    @ApiModelProperty("预付明细编号")
    private String prepayDetailCode;

    @ApiModelProperty("核销申请编号")
    private String auditCode;

    @ApiModelProperty("核销明细编号")
    private String auditDetailCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("供应商编码")
    private String payeeCode;

    @ApiModelProperty("供应商名称")
    private String payeeName;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("已预付金额")
    private BigDecimal prepayAmount;

    @ApiModelProperty("预付可冲销金额")
    private BigDecimal availableReversedAmount;

    @ApiModelProperty("已冲销金额")
    private BigDecimal reversedAmount;

    @ApiModelProperty("本次冲抵金额")
    private BigDecimal thisReversedAmount;

    @ApiModelProperty("待结转金额")
    private BigDecimal prepareCarryAmount;

    @ApiModelProperty("扣减待结转金额")
    private BigDecimal kouJianPrepareCarryAmount;

    @ApiModelProperty("已结转金额")
    private BigDecimal carriedAmount;

    @ApiModelProperty("预付描述")
    private String description;

    @ApiModelProperty("收款行编号")
    private String lineCode;


    /**
     * 是否选中，0否1是
     */
    @ApiModelProperty("是否选中，0否1是")
    private String checked;
}
