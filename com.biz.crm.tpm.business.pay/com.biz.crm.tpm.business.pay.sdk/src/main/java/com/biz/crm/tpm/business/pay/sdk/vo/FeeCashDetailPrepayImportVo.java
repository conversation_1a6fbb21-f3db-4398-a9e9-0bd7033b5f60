package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "FeeCashDetail", description = "费用兑付明细")
@Data
@CrmExcelImport
public class FeeCashDetailPrepayImportVo extends CrmExcelVo {


    @CrmExcelColumn("核销明细编号")
    private String auditDetailCode;

    @CrmExcelColumn("核销申请编号")
    private String auditCode;

    @CrmExcelColumn("方案编码")
    private String schemeCode;

    @CrmExcelColumn("方案名称")
    private String schemeName;

    @CrmExcelColumn("方案明细编码")
    private String schemeDetailCode;

    @CrmExcelColumn("活动细类名称")
    private String detailName;

    @CrmExcelColumn("年月")
    private String years;

    @CrmExcelColumn("客户编码")
    private String customerCode;

    @CrmExcelColumn("客户名称")
    private String customerName;

    @CrmExcelColumn("成本中心名称")
    private String costCenterName;

    @CrmExcelColumn("品项名称")
    private String itemName;

    @CrmExcelColumn("核销金额")
    private String auditAmountStr;

    @CrmExcelColumn("已兑付金额")
    private String cashAmountStr;

    @CrmExcelColumn("可兑付金额")
    private String availableCashAmountStr;

    @CrmExcelColumn("预付可冲销金额")
    private String availableReversedAmountStr;

    @CrmExcelColumn("本次兑付金额")
    private String thisCashAmountStr;
    private BigDecimal thisCashAmount;

    @CrmExcelColumn("兑付描述")
    private String remark;
}
