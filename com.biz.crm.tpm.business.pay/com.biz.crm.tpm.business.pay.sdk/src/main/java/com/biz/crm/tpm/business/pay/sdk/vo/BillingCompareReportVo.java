package com.biz.crm.tpm.business.pay.sdk.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillingCompareReportVo {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("兑付类型")
    private String cashType;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("科目层级")
    private String level;

    @ApiModelProperty("账单导入未完结")
    private BigDecimal unFinishAmount;

    @ApiModelProperty("结案未兑付")
    private BigDecimal unCashAmount;

    @ApiModelProperty("方案未结案")
    private BigDecimal unAuditAmount;

    @ApiModelProperty("账单未兑付差异")
    private BigDecimal unCashDiffAmount;

    @ApiModelProperty("账单需计提差异")
    private BigDecimal unWithHoldingDiffAmount;
}
