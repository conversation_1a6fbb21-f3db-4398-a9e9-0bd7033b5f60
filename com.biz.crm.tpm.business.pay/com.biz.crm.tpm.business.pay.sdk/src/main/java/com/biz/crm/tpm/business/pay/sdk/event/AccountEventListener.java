package com.biz.crm.tpm.business.pay.sdk.event;

import com.biz.crm.tpm.business.pay.sdk.vo.AccountVo;

import java.util.List;

/**
 * <AUTHOR>
 * @describe: 费用上账表;(tpm_account)相关的事件通知
 * @createTime 2022年06月16日 19:25:00
 */
public interface AccountEventListener {
  /**
   * 当费用上账商品表数据被创建时，该事件被触发
   *
   * @param accountVo
   */
  void onCreated(List<AccountVo> accountVo);

  /**
   * 当费用上账商品表数据被修改时，该事件被触发
   *
   * @param oldAccountVo 修改前数据
   * @param accountVo    修改后数据
   */
  void onUpdate(AccountVo oldAccountVo, AccountVo accountVo);

  /**
   * 当费用上账商品表数据被删除时（逻辑删除），该事件被触发
   *
   * @param accountVo
   */
  void onDeleted(List<AccountVo> accountVo);
}
