package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum FeeCashMaterialMainFieldsEnum {
    demandType("demandType"),
    totalCashAmount("totalCashAmount"),
    totalDemandQuantity("totalDemandQuantity"),
    totalDemandAmount("totalDemandAmount"),
    remark("remark"),
    title("title"),
    ;

    private String dictCode;

    public static FeeCashMaterialMainFieldsEnum findByCode(String code) {
        Optional<FeeCashMaterialMainFieldsEnum> first = Stream.of(FeeCashMaterialMainFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}