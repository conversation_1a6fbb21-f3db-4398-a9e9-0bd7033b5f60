package com.biz.crm.tpm.business.pay.sdk.dto.log;

import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @describe: 费用预提日志DTO
 * @createTime 2022年07月12日 10:21:00
 */
@Data
public class WithHoldingLogEventDto  implements NebulaEventDto  {
  /**
   * 原始
   */
  private WithHoldingVo original;
  /**
   * 最新
   */
  private WithHoldingVo newest;
  /**
   * 原始
   */
  private List<WithHoldingVo> originalList;
  /**
   * 最新
   */
  private List<WithHoldingVo> newestList;
}
