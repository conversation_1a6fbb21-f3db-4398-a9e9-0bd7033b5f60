package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 实体：活动预付明细;
 *
 * <AUTHOR> ya<PERSON><PERSON><PERSON>
 * @date : 2024-6-4
 */
@ApiModel(value = "ActivityPrepayDetailVo", description = "活动预付明细")
@Data
public class ActivityPrepayDetailVo extends TenantFlagOpVo {

  @ApiModelProperty("预付编号")
  private String prepayCode;

  @ApiModelProperty("预付名称")
  private String prepayName;

  @ApiModelProperty("预付明细编号")
  private String prepayDetailCode;

  @ApiModelProperty("方案编码")
  private String schemeCode;

  @ApiModelProperty("方案名称")
  private String schemeName;

  @ApiModelProperty("方案明细编码")
  private String schemeDetailCode;

  @ApiModelProperty("活动大类编码")
  private String categoryCode;

  @ApiModelProperty("活动大类名称")
  private String categoryName;

  @ApiModelProperty("活动细类编码")
  private String detailCode;

  @ApiModelProperty("活动细类名称")
  private String detailName;

  @ApiModelProperty("开始时间")
  private String startDate;

  @ApiModelProperty("结束时间")
  private String endDate;

  @ApiModelProperty("年月")
  private String years;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("供应商编码")
  private String payeeCode;

  @ApiModelProperty("供应商名称")
  private String payeeName;

  @ApiModelProperty("申请金额")
  private BigDecimal applyAmount;

  @ApiModelProperty("已预付金额")
  private BigDecimal prepayAmount;

  @ApiModelProperty("兑付金额")
  private BigDecimal cashAmount;

  @ApiModelProperty("预付可冲销金额")
  private BigDecimal availableReversedAmount;

  @ApiModelProperty("可预付金额")
  private BigDecimal availablePrepayAmount;

  @ApiModelProperty("预付申请金额")
  private BigDecimal thisPrepayAmount;

  @ApiModelProperty("本次支付金额")
  private BigDecimal thisPayAmount;

  @ApiModelProperty("关联结转金额")
  private BigDecimal relateCarryAmount;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;
}