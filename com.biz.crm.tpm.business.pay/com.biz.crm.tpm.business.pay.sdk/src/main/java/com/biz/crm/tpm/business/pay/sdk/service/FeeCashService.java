package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheService;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPayeeDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTicketDto;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 费用兑付
 */
public interface FeeCashService extends BusinessPageCacheService<FeeCashTicketVo, FeeCashTicketDto>{

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    FeeCashVo findByCode(String code);


    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @param cacheKey 实体对象
     * @param cacheKeyToc 实体对象
     * @return 新增结果
     */
    List<String> create(FeeCashDto dto, boolean autoCash,String cacheKey, String cacheKeyToc,
                        String cacheKeyDetail, String cacheKeyPrepay, String cacheKeyInvoice, String cacheKeyMaterial, boolean commit, boolean staging);

    /**
     * 修改新据
     *
     * @param dto 实体对象
     * @param cacheKey 实体对象
     * @param cacheKeyToc 实体对象
     * @return 修改结果
     */
    List<String> update(FeeCashDto dto, String cacheKey, String cacheKeyToc,
                        String cacheKeyDetail, String cacheKeyPrepay, String cacheKeyInvoice, String cacheKeyMaterial, boolean commit, boolean staging,boolean saveFlag);

    /**
     * 修改银行卡信息保存
     *
     * @param dto 实体对象
     * @param cacheKey 实体对象
     * @param cacheKeyToc 实体对象
     * @return 修改结果
     */
    List<String> updateBankInfo(FeeCashDto dto, String cacheKey, String cacheKeyToc);

    /**
     * 获取所有缓存
     *
     * @param cacheKey
     * @return
     */
    List<FeeCashTicketDto> findAllCacheList(String cacheKey);


    /**
     * 检索活动
     *
     * @param cacheKey
     * @return
     */
    List<MarketingAuditDetailVo> searchAuditDetail(String cacheKey, String cashType, String cashCode);

    /**
     * OA撤回
     *
     * @param code
     * @return
     */
    void recover(String code, String remark);

    /**
     * OA回调
     *
     * @param code
     */
    void oaCallback(String code, String status, boolean beAuto);

    /**
     * 贷项订单生成定时任务
     */
    void createCreditOrderTask();

    /**
     * 按贷项单号查询票扣
     *
     * @param code
     */
    List<FeeCashTicketVo> findTicketByCreditCode(String code);


    /**
     * 更新流程状态
     * 单条数据不用加事务
     *
     * @param dto
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/11 16:43
     */
    void updateProcessStatus(FeeCashDto dto);

    /**
     * 分页查询付款信息
     *
     * @param pageable
     * @param dto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPayeeVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/11 20:40
     */
    Page<FeeCashPayeeVo> findByConditions(Pageable pageable, FeeCashPayeeDto dto);

    /**
     * 按结案明细编码查询兑付明细
     *
     * @param auditDetailCodes
     * @return
     */
    List<FeeCashDetailVo> findByAuditDetailCodes(List<String> auditDetailCodes);

    /**
     * 按结案明细编码查询兑付明细（只查兑付）
     *
     * @param auditDetailCodes
     * @return
     */
    List<FeeCashDetailVo> findByAuditDetailCodesPass(List<String> auditDetailCodes, String status, List<String> cashDetailCodes, List<String> cashTypeList);

    /**
     * 按客户+结案单号查询兑付单
     *
     * @param
     * @return
     */
    List<FeeCashVo> findByAuditCustomer(List<String> auditCodes, List<String> customerCodes);


    /**
     * 更新付款信息的回单字段
     *
     * @param receiptVoList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/11 20:47
     */
    void updateReceipt(List<HecElectronicReceiptVo> receiptVoList);

    /**
     * 费控凭证回传
     *
     * @param list
     */
    void hecVoucherCallback(List<HecCallbackDto> list);

    /**
     * 费控电汇付款状态回传
     * @param dtoList
     */
    void hecPayStatusCallback(List<HecCallbackDto> dtoList);

    List<FeeCashDetailVo> findCashAmountBySchemeDetailCodes(List<String> schemeDetailCodes);

    List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesPass(List<String> schemeDetailCodes);

    List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesApproved(List<String> schemeDetailCodes);

    List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesApprovedBefore(List<String> orgCodes, String yearMonthLy);


    List<FeeCashDto> findListByTicketBuckle();

    void brushFeeCashTaxAmount(String cashCode);

    void updateFeeCashStatus(List<String> codes);

    Page<TicketFeeCashDetailVo> getTicketDeductionDetail(Pageable pageable, FeeCashTicketDto dto);

    List<FeeCashDetailVo> findNoTaxCashAmountByYearsAndOrgCodes(String years, List<String> orgCodeList);
}
