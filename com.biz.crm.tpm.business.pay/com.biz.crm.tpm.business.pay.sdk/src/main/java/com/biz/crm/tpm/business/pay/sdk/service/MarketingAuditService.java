package com.biz.crm.tpm.business.pay.sdk.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDto;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditBillDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditVo;
import com.biz.crm.workflow.sdk.dto.OaCallbackDto;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 方案结案
 */
public interface MarketingAuditService {

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    MarketingAuditVo findByCode(String code);

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    List<String> create(MarketingAuditDto dto, String cacheKey, boolean submit);

    /**
     * 修改新据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    void update(MarketingAuditDto dto, String cacheKey, boolean submit);

    /**
     * 新增保存并提交
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    void submitCreate(MarketingAuditDto dto, String cacheKey);

    /**
     * 编辑保存并提交
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    void submitUpdate(MarketingAuditDto dto, String cacheKey);

    /**
     * 批量选择提交
     *
     * @param dto
     * @return
     */
    void batchSelect(MarketingPlanCaseVo dto, String cacheKey);

    /**
     * 删除数据
     *
     * @param ids 主键结合
     */
    void delete(List<String> ids);

    /**
     * 生成票扣明细
     *
     * @param cacheKey
     * @return
     */
    List<MarketingAuditBillDetailVo> generateBill(String cacheKey);

    /**
     * 提交审批
     *
     * @param idList
     */
    void submit(List<String> idList);

    /**
     * 自动结案
     */
    void autoAudit();

    /**
     * 周边物料自动结案
     *
     * @param schemeDetailCodes
     */
    void autoAuditPlanCasePass(List<String> schemeDetailCodes);

    /**
     * 获取所有缓存
     *
     * @param cacheKey
     * @return
     */
    List<MarketingAuditDetailDto> findAllCacheList(String cacheKey);

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    Page<MarketingAuditDetailVo> findByConditions(Pageable pageable, MarketingAuditDetailDto dto);

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    Page<MarketingAuditDetailVo> findDataViewByConditions(Pageable pageable, MarketingAuditDetailDto dto);

    /**
     * 批量查询
     *
     * @param dtoList
     * @return
     */
    List<MarketingAuditDetailVo> findByDtoList(List<MarketingAuditDetailDto> dtoList);

    /**
     * 审批通过
     *
     * @param codes
     */
    void approved(List<String> codes);


    /**
     * 生成费用兑付/关闭明细、冲销数据
     */
    void generateDeliveryReplenishmentAndWriteOff(List<String> auditCodeList);


    /**
     * 方案结案审批通过后 生成费用兑付/关闭明细
     * @param code
     */
    void approvedCreateDeliveryReplenishmentPool(String code);

    /**
     * 推送货补
     * @param auditCodeList
     */
    void pushDmsReplenishmentList(List<String> auditCodeList);

    /**
     * 票扣生成费用兑付
     *
     * @param codes
     */
    void createFeeCash(List<String> codes);

    /**
     * 推送货补池
     *
     * @param codes
     */
    void push(List<String> codes);

    /**
     * 自动兑付补偿
     *
     * @param codes
     */
    void autoCashReimburse(List<String> codes);

    /**
     * 回写活动明细数据
     *
     * @param
     */
    void updateSchemeDetail(List<MarketingAuditDetailDto> auditDtoList, List<MarketingPlanCaseVo> caseVoList, boolean beAuto);

    /**
     * OA回调
     *
     * @param code
     */
    void oaCallback(String code, String status);

    /**
     * OA撤回
     *
     * @param code
     * @return
     */
    void recover(String code, String remark);

    /**
     * 查询审批中的结案
     *
     * @return
     */
    List<MarketingAuditVo> findCommit(String yearMonthLy);

    /**
     * 定时查询DMS费用池回写兑付金额
     */
    void replenishmentPoolCashAmount();

    /**
     * 修改兑付方式
     *
     * @param auditDetailDto
     */
    void updateCashType(MarketingAuditDetailDto auditDetailDto);

    /**
     *
     * 预提明细操作预付明细
     *
     * @param dto
     */
    void modifyActivityPrepayRecord(OaCallbackDto dto);

    /**
     * 是否展示票扣
     *
     * @param cacheKey
     * @return
     */
    String showTicket(String cacheKey);

    /**
     * 按活动编码查询审批通过
     *
     * @param codes
     * @return
     */
    List<MarketingAuditDetailVo> findBySchemeDetailCodesAll(List<String> codes);

    List<MarketingAuditDetailVo> findAuditAmountBySchemeDetailCodes(List<String> schemeDetailCodes);

    List<MarketingAuditDetailVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes);

    List<MarketingAuditDetailVo> findListBySchemeDetailCodesApproved(List<String> schemeDetailCodes);

    void scheduleNotClosureDetailMsgPush();

    void scheduleNotReplenishmentMsgPush();

    List<MarketingAuditDetailVo> findCommitAndPassBySchemeDetailCodes(List<String> codes);

}
