package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @describe: 票据类型
 * @createTime 2022年06月16日 19:43:00
 */
@AllArgsConstructor
@Getter
public enum BillTypeEnum {

  /** 票据类型枚举 */
  INVOICE("invoice", "invoice", "发票", "1"),
  RECEIPT("receipt", "receipt", "收据", "2"),
      ;
  /** 系统key */
  private String key;
  /** 字典编码 */
  private String dictCode;
  /** 字典值 */
  private String value;
  /** 字典排序 */
  private String order;

  public static BillTypeEnum findByValue(String value){
    if(StringUtils.isBlank(value)){
      return null;
    }
    for(BillTypeEnum e : values()){
      if(StringUtils.equals(e.getValue(),value)){
        return e;
      }
    }
    return null;
  }
}
