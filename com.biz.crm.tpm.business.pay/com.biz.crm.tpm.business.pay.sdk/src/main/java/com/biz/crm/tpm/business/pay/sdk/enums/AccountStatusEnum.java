package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @describe: 上账类型枚举
 * @createTime 2022年06月16日 19:43:00
 */
@AllArgsConstructor
@Getter
public enum AccountStatusEnum {

  /** 上账类型枚举 */
  PENDING("pending", "pending", "待上账", "1"),
  ON("on", "on", "已上账", "2"),
  FAIL("fail", "fail", "上账失败", "3"),
      ;
  /** 系统key */
  private String key;
  /** 字典编码 */
  private String dictCode;
  /** 字典值 */
  private String value;
  /** 字典排序 */
  private String order;
}
