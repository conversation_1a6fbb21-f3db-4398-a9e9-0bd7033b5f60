package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "FeeCashToc", description = "TOC支付明细")
@Data
public class FeeCashTocDto extends TenantFlagOpDto {

    @ApiModelProperty("兑付名称")
    private String cashName;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("打款平台")
    private String platform;

    @ApiModelProperty("姓名")
    private String payeeName;

    @ApiModelProperty("银行账号")
    private String bankNo;

    @ApiModelProperty("身份证号码")
    private String idCard;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("收款金额")
    private BigDecimal payeeAmount;

    @ApiModelProperty("实际打款金额")
    private BigDecimal payAmount;

    /**
     * 是否选中，0否1是
     */
    @ApiModelProperty("是否选中，0否1是")
    private String checked;

    @ApiModelProperty("错误描述")
    private String errMsg;

    @ApiModelProperty("校验状态")
    private Boolean checkFlag;
}
