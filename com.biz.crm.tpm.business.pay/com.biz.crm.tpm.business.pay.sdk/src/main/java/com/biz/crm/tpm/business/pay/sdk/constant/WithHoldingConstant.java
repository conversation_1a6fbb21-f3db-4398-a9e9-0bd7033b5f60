package com.biz.crm.tpm.business.pay.sdk.constant;

public interface WithHoldingConstant {

    /**
     * 编码前缀
     */
    String PREFIX_CODE = "JT";

    /**
     * 汇总编码前缀
     */
    String PREFIX_CODE_HZ = "HZ";

    /**
     * 推送费控编码前缀
     */
    String PREFIX_CODE_TSFK = "TSFK";

    /**
     * 费用池计提活动细类数据字典
     */
    String PENNYAGE_ACTIVITY_SUBCATEGORIES = "pennyage_activity_subcategories";
    /**
     * STD-费用池业态数据字典
     */
    String BUSINESS_FORMAT_FYC = "business_format_fyc";

    /**
     * 缓存分页键
     */
    String CACHE_KEY_PREFIX = "with_holding:item_cache:new:";

    /**
     * 计提锁
     */
    String WITH_HOLDING_LOCK = "with_holding:lock:";

    /**
     * 默认加锁时间
     */
    int DEFAULT_LOCK_TIME = 20 * 60;

    /**
     * 等待时间：3
     */
    int DEFAULT_WAITE_TIME_FIVE = 3;
}
