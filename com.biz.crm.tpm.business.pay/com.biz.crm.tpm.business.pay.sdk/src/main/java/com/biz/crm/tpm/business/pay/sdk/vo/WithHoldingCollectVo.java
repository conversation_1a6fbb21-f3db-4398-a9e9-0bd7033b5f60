package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @describe: 费用预提实体
 * @createTime 2022年06月25日 10:19:00
 */
@Data
@ApiModel(value = "WithHoldingCollectVo", description = "费用预提汇总")
public class WithHoldingCollectVo extends TenantFlagOpVo {

  @ApiModelProperty("汇总单号")
  private String collectCode;

  @ApiModelProperty(name = "推送状态", notes = "推送状态")
  private String pushStatus;

  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  private String orgCode;

  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  private String orgName;

  @ApiModelProperty(name = "预提金额", notes = "预提金额")
  private BigDecimal withHoldingAmount;

  @ApiModelProperty("预提年月")
  private String yearMonthLy;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("部门编码")
  private String departmentCode;

  @ApiModelProperty("部门名称")
  private String departmentName;

  @ApiModelProperty("凭证号")
  private String voucherCode;

  @ApiModelProperty("失败原因")
  private String failMsg;

  @ApiModelProperty("审批状态")
  private String status;

  @ApiModelProperty("推送日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("审批通过日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date passDate;

  @ApiModelProperty("审批流程编码")
  private String processKey;

  @ApiModelProperty("费控单号")
  private String externalCode;

  @ApiModelProperty("业务编码")
  private String businessCode;

  @ApiModelProperty("操作类型")
  private String hecOperationType;

  @ApiModelProperty("查看流程日志所需地址")
  private String hecReceiptUrl;

  @ApiModelProperty("单点url")
  private String tpmUrl;

  private String deptCode;
  private BigDecimal withHoldingRatio;
  private BigDecimal budgetRatio;

  @ApiModelProperty("营销测算表")
  private List<RegionCollectMarketingEstimationVo> marketingEstimationList;

  @ApiModelProperty("三级部门测算表")
  private List<RegionCollectDepartmentEstimationVo> departmentEstimationList;

  @ApiModelProperty("客户损益预测表")
  private List<RegionCollectGainsAndLossesVo> gainsAndLossesList;

  @ApiModelProperty("品项分析表")
  private List<RegionCollectItemEstimationVo> itemEstimationList;

  @ApiModelProperty("计提明细表")
  private List<WithHoldingVo> withHoldingList;

  @ApiModelProperty("搭赠及周边")
  private List<MarketingPlanCaseVo> giftAndSurroundingList;

  @ApiModelProperty("已结案兑付活动")
  private List<FeeCashDetailVo> auditedAndCashedActivityList;


}
