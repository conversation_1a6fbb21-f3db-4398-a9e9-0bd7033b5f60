package com.biz.crm.tpm.business.pay.sdk.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/4 22:54
 */
@Data
@ApiModel("发票识别返回结果")
public class InvoiceIdentityVo {

    @ApiModelProperty("文件编码")
    @JSONField(name = "vatAttId")
    private String fileCode;

    @ApiModelProperty("是否需要验证")
    @JSONField(name = "needVerifyFlag")
    private Boolean needVerifyFlag;

    @ApiModelProperty("发票类型")
    @JSONField(name = "invoiceTypeCode")
    private String type;

    @ApiModelProperty("发票代码")
    @JSONField(name = "invoiceCode")
    private String code;

    @ApiModelProperty("发票号码(只允许填写数字和字母)")
    @JSONField(name = "invoiceNumber")
    private String invoiceNo;

    @ApiModelProperty("开票日期")
    @JSONField(name = "invoiceDate")
    private String billingDate;

    @ApiModelProperty("校验码(只允许填写数字和字母)")
    @JSONField(name = "checkCodeLastChars")
    private String checkCode;

    @ApiModelProperty("税价合计")
    @JSONField(name = "totalAmount")
    private BigDecimal priceAndTax;

    @ApiModelProperty("不含税金额")
    @JSONField(name = "amount")
    private BigDecimal amountWithoutTax;

    @ApiModelProperty("税率")
    @JSONField(name = "")
    private BigDecimal taxRate;

    @ApiModelProperty("税额")
    @JSONField(name = "taxAmount")
    private BigDecimal taxAmount;

    @ApiModelProperty("购买方名称")
    @JSONField(name = "buyerName")
    private String purchaser;

    @ApiModelProperty("购买方税号")
    @JSONField(name = "buyerId")
    private String pNo;

    @ApiModelProperty("购买方开户行及账号")
    @JSONField(name = "buyerBank")
    private String pBankAndAccount;

    @ApiModelProperty("购买方地址电话")
    @JSONField(name = "buyerAddress")
    private String pAddressAndPhone;

    @ApiModelProperty("销售方名称")
    @JSONField(name = "sellerName")
    private String seller;

    @ApiModelProperty("销售方税号")
    @JSONField(name = "sellerId")
    private String sNo;

    @ApiModelProperty("销售方开户行及账号")
    @JSONField(name = "sellerBank")
    private String sBankAndAccount;

    @ApiModelProperty("销售方地址电话")
    @JSONField(name = "sellerAddress")
    private String sAddressAndPhone;

    @ApiModelProperty("员工账号")
    @JSONField(name = "employeeCode")
    private String employeeCode;

    @ApiModelProperty("来源系统")
    @JSONField(name = "sourceSystem")
    private String sourceSystem;

    @ApiModelProperty("核算公司代码")
    private String accEntityCode;

    @ApiModelProperty("公司代码-创建人所属公司的OAId")
    private String companyCode;

    @ApiModelProperty("是否验证")
    @JSONField(name = "checkedFlag")
    private String checked;


    @ApiModelProperty("明细")
    @JSONField(name = "itemList")
    private List<InvoiceDetailIdentityVo> items;

    private String message;
}
