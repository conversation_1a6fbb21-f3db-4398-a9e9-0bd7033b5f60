package com.biz.crm.tpm.business.pay.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordItemDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordItemVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayVo;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ActivityPrepayDetailRecordService {


    Page<ActivityPrepayDetailRecordVo> findListByCondition(@PageableDefault(50) Pageable pageable, ActivityPrepayDetailRecordDto dto);


    /**
     * 通过供应商编码查询待冲销明细
     *
     * @param
     * @return
     */
    Page<ActivityPrepayDetailRecordVo> findByPayeeCode(Pageable pageable, ActivityPrepayDetailRecordDto dto);

    /**
     * 通过活动明细编码查询待冲销明细
     *
     * @param
     * @return
     */
    List<ActivityPrepayDetailRecordVo> findBySchemeDetailCode(ActivityPrepayDetailRecordDto dto);

    /**
     * 通过预付明细编码查询
     *
     * @param code
     * @return
     */
    ActivityPrepayDetailRecordVo findByCode(String code);

    /**
     * 生成预付明细跟踪
     *
     * @param list
     */
    void create(List<ActivityPrepayDetailRecordDto> list);

    /**
     *
     * 新增编辑页面计算
     *
     * @param vo
     */
    ActivityPrepayVo cal(ActivityPrepayVo vo);

    /**
     *
     * 操作加锁/解锁
     *
     * @param codeList
     * @param lock 是否加锁
     */
    void operateLock(List<String> codeList, boolean lock);

    /**
     *
     * 预付明细操作
     *
     * @param list
     */
    void operate(List<ActivityPrepayDetailRecordItemDto> list);

    /**
     *
     * 预付明细分页
     *
     * @param pageable
     * @param dto
     * @return
     */
    Page<ActivityPrepayDetailRecordItemVo> findItemByConditions(Pageable pageable, ActivityPrepayDetailRecordItemDto dto);

    /**
     * 更新支付状态
     *
     * @param dtoList
     */
    void updatePayStatus(List<HecCallbackDto> dtoList);

    /**
     * 通过【活动明细编码】查询【预付明细跟踪表】 统计已预付金额
     *
     * @param schemeDetailCodes
     * @return
     */
    Map<String, BigDecimal> findprepayAmount(List<String> schemeDetailCodes);

    Page<ActivityPrepayDetailRecordVo> findByPayeeCodes(Pageable pageable, List<String> payeeCodes);

    /**
     * 通过活动明细编码实时查询结转、冲销明细
     *
     * @param code
     * @return
     */
    List<ActivityPrepayDetailRecordItemVo> findItemByPrepayDetailCode(String code);


    Map<String,BigDecimal> findAlreadyPrepayAmount(ActivityPrepayVo vo);

    BigDecimal getAvailable_reversed_amount(String detailCode);

    void koujianPrepayCarreAmount(List<FeeCashPrepayDto> prepayList,Integer koujianOrRecover);
}
