package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 费控回传
 * 凭证 单据类型
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/9 14:08
 */
@Getter
@AllArgsConstructor
public enum HecBusinessTypeEnum {
    TPM_WITH_HOLDING_HEC("TPM_WITH_HOLDING_HEC", "费用计提"),
    TPM_WITH_HOLDING_WRITE_OFF_HEC("TPM_WITH_HOLDING_WRITE_OFF_HEC", "费用冲销"),
    TPM_FEE_CASH_HEC("TPM_FEE_CASH_HEC", "费用兑付"),
    TPM_ACTIVITY_PREPAY_HEC("TPM_ACTIVITY_PREPAY_HEC", "活动预付"),
    ;
    private String code;

    private String desc;

    public static HecBusinessTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (HecBusinessTypeEnum typeEnum : values()) {
            if (StringUtils.equals(code, typeEnum.getCode())) {
                return typeEnum;
            }
        }
        return null;
    }

}
