package com.biz.crm.tpm.business.pay.sdk.service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 计提加锁
 */
public interface WithHoldingLockVoService {

    /**
     * 根据计提编码加锁
     *
     * @param withHoldingCode
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 14:21
     **/
    boolean lock(String withHoldingCode, TimeUnit timeUnit, int time);

    /**
     * 根据计提编码批量加锁
     *
     * @param withHoldingCodeList
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 14:21
     **/
    boolean lock(List<String> withHoldingCodeList, TimeUnit timeUnit, int time);

    /**
     * 根据计提编码批量加锁
     *
     * @param withHoldingCodeList
     * @param timeUnit
     * @param lockTime
     * @param waiteTime
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 14:21
     **/
    boolean lock(List<String> withHoldingCodeList, TimeUnit timeUnit, int lockTime, int waiteTime);

    /**
     * 解锁
     *
     * @param withHoldingCode
     * <AUTHOR>
     * @date 2022/11/1 14:46
     **/
    void unLock(String withHoldingCode);

    /**
     * 批量解锁
     *
     * @param withHoldingCodeList
     * <AUTHOR>
     * @date 2022/11/1 14:46
     **/
    void unLock(List<String> withHoldingCodeList);
}
