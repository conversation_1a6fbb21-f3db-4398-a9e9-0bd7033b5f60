package com.biz.crm.tpm.business.pay.sdk.service;

import com.biz.crm.tpm.business.pay.sdk.dto.AuditBillDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditBillVo;

import java.math.BigDecimal;


/**
 * 核销账单;(tpm_audit_bill)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-17
 */
public interface AuditBillService {
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AuditBillVo findById(String id);

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  AuditBillVo findByActivitiesDetailCode(String code);

  /**
   * 新增数据
   *
   * @param auditBillDto 实体对象
   * @return 新增结果
   */
  AuditBillVo create(AuditBillDto auditBillDto);

  /**
   * 新增已经核销金额
   *
   * @param activitiesDetailCode 活动明细编码
   * @param amount               新增金额
   */
  void auditAmountByActivityDetailCode(String activitiesDetailCode, String auditDetailCode, BigDecimal amount);

  /**
   * 退回已经核销金额
   *
   * @param activitiesDetailCode 活动明细编码
   * @param amount               新增金额
   */
  void backAuditAmountByActivityDetailCode(String activitiesDetailCode, String auditDetailCode, BigDecimal amount);

  /**
   * 根据核销编号进行活动明细金额核销
   *
   * @param auditCode
   */
  void auditAmountByAuditCode(String auditCode);

  /**
   * 根据核销编号进行活动明细金额核销退回
   *
   * @param auditCode
   */
  void backAuditAmountByAuditCode(String auditCode);

  /**
   * 根据活动明细编号更新完全核销标识
   *
   * @param activitiesDetailCode
   * @param isFullAudit
   */
  void updateIsFullAudit(String activitiesDetailCode, String isFullAudit);

}
