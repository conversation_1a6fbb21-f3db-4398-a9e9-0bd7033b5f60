package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Vo：费用核销明细;
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "AuditDetail",description = "费用核销明细")
@Getter
@Setter
public class AuditDetailVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
  private String tenantCode;
  /** 核销申请编码 */
  @ApiModelProperty(name = "auditCode",notes = "核销申请编码", value= "核销申请编码")
  private String auditCode;
  /** 核销申请明细编号 */
  @ApiModelProperty(name = "auditDetailCode",notes = "核销申请编号", value= "核销申请编号")
  private String auditDetailCode;
  /** 活动编码 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编码", value= "活动编码")
  private String activitiesCode;
  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value= "活动名称")
  private String activitiesName;
  /** 活动细类编码 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编码", value= "活动细类编码")
  private String costTypeDetailCode;
  /** 活动细类名称 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value= "活动细类名称")
  private String costTypeDetailName;
  /** 活动大类编码 */
  @ApiModelProperty(name = "costTypeCategoryCode",notes = "活动大类编码", value= "活动大类编码")
  private String costTypeCategoryCode;
  /** 活动大类名称 */
  @ApiModelProperty(name = "costTypeCategoryName",notes = "活动大类名称", value= "活动大类名称")
  private String costTypeCategoryName;
  /** 组织编号 */
  @ApiModelProperty(name = "orgCode",notes = "组织编号", value= "组织编号")
  private String orgCode;
  /** 组织名称 */
  @ApiModelProperty(name = "orgName",notes = "组织名称", value= "组织名称")
  private String orgName;
  /** 支付方式 */
  @ApiModelProperty(name = "payBy",notes = "支付方式", value= "支付方式")
  private String payBy;
  /** 支付方式名称 */
  @ApiModelProperty(name = "payByName",notes = "支付方式名称", value= "支付方式名称")
  private String payByName;
  /** 申请金额 */
  @ApiModelProperty(name = "applyAmount",notes = "申请金额", value= "申请金额")
  private BigDecimal applyAmount;
  /** 已核销金额 */
  @ApiModelProperty(name = "auditedAmount",notes = "已核销金额", value= "已核销金额")
  private BigDecimal auditedAmount;
  /** 核销金额 */
  @ApiModelProperty(name = "auditAmount",notes = "核销金额", value= "核销金额")
  private BigDecimal auditAmount;
  /** 是否完全核销 */
  @ApiModelProperty(name = "isFullAudit",notes = "是否完全核销", value= "是否完全核销")
  private String isFullAudit;
  /** 产品层级编号 */
  @ApiModelProperty(name = "productLevelCode",notes = "产品层级编号", value= "产品层级编号")
  private String productLevelCode;
  /** 产品层级名称 */
  @ApiModelProperty(name = "productLevelName",notes = "产品层级名称", value= "产品层级名称")
  private String productLevelName;
  /** 产品信息 */
  @ApiModelProperty(name = "auditProducts",notes = "产品信息", value = "产品信息")
  private List<AuditProductVo> auditProducts;
  /** 客户信息 */
  @ApiModelProperty(name = "auditCustomers",notes = "客户信息", value = "客户信息")
  private List<AuditCustomerVo> auditCustomers;
  /** 活动明细编码 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value= "活动明细编码")
  private String activitiesDetailCode;
  /** ERP会计科目编码 */
  @ApiModelProperty(name = "accountingSubjectsCode",notes = "ERP会计科目编码", value= "ERP会计科目编码")
  private String accountingSubjectsCode;
  /** ERP会计科目名称 */
  @ApiModelProperty(name = "accountingSubjectsName",notes = "ERP会计科目名称", value= "ERP会计科目名称")
  private String accountingSubjectsName;
  /** 超额金额 */
  @ApiModelProperty(name = "excessAmount",notes = "超额金额", value= "超额金额")
  private BigDecimal excessAmount;
  /** 核销明细备注 */
  @ApiModelProperty(name = "核销明细备注",notes = "")
  private String remark;
  /** 开始时间 */
  @ApiModelProperty(name = "beginTime",notes = "开始时间", value = "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /** 结束时间 */
  @ApiModelProperty(name = "endTime",notes = "结束时间", value = "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value = "预算科目名称")
  private String budgetSubjectsName;
  /** 预算科目编号 */
  @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编号", value = "预算科目编号")
  private String budgetSubjectsCode;
  /** 是否多次核销(Y/N) */
  @ApiModelProperty(name = "是否多次核销(Y/N)",notes = "")
  private String isMultipleAudit;
  /** 退费金额 */
  @ApiModelProperty(name = "refundAmount",notes = "退费金额", value= "退费金额")
  private BigDecimal refundAmount;
  /** 活动细类信息 */
  @ApiModelProperty(name = "costTypeDetailVo",notes = "活动细类信息", value= "活动细类信息")
  private CostTypeDetailVo costTypeDetailVo;
  /** 活动明细详细信息 */
  @ApiModelProperty(name = "costTypeDetailVo",notes = "活动细类信息", value= "活动细类信息")
  private ActivitiesDetailVo activitiesDetailVo;
  /** 是否修改客户 */
  @ApiModelProperty(name = "是否修改客户",notes = "是否修改客户")
  private String isEditCustomer;
  /** 费用预算编号 */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编号", value= "费用预算编号")
  private String costBudgetCode;
  /** 动态表单编号 */
  @ApiModelProperty(name = "formMappingCode",notes = "动态表单编号", value= "动态表单编号")
  private String formMappingCode;
}