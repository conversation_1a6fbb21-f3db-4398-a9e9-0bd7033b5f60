package com.biz.crm.tpm.business.pay.sdk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @describe: 核销明细客户维度
 * @createTime 2022年07月11日 16:30:00
 */
@ApiModel(value = "AuditDetailWithCustomerVo", description = "核销明细客户维度")
@Getter
@Setter
public class AuditDetailWithCustomerVo {
  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesCode", notes = "活动编号", value = "活动编号")
  private String activitiesCode;
  /**
   * 活动名称
   */
  @ApiModelProperty(name = "activitiesName", notes = "活动名称", value = "活动名称")
  private String activitiesName;

  /**
   * 活动明细编码
   */
  @ApiModelProperty(name = "activitiesDetailCode", notes = "活动明细编码", value = "活动明细编码")
  private String activitiesDetailCode;

  /**
   * 核销编号
   */
  @ApiModelProperty(name = "auditCode", notes = "核销编号", value = "核销编号")
  private String auditCode;
  /**
   * 核销明细编号
   */
  @ApiModelProperty(name = "auditDetailCode", notes = "核销明细编号", value = "核销明细编号")
  private String auditDetailCode;
  /**
   * 开始时间
   */
  @ApiModelProperty(name = "beginTime", notes = "开始时间", value = "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /**
   * 预算科目编码
   */
  @ApiModelProperty(name = "budgetSubjectsCode", notes = "预算科目编码", value = "预算科目编码")
  private String budgetSubjectsCode;
  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "budgetSubjectsName", notes = "预算科目名称", value = "预算科目名称")
  private String budgetSubjectsName;
  /**
   * 活动大类编码
   */
  @ApiModelProperty(name = "costTypeCategoryCode", notes = "活动大类编码", value = "活动大类编码")
  private String costTypeCategoryCode;
  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "costTypeCategoryName", notes = "活动大类名称", value = "活动大类名称")
  private String costTypeCategoryName;
  /**
   * 活动细类编码
   */
  @ApiModelProperty(name = "costTypeDetailCode", notes = "活动细类编码", value = "活动细类编码")
  private String costTypeDetailCode;
  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "costTypeDetailName", notes = "活动细类名称", value = "活动细类名称")
  private String costTypeDetailName;
  /**
   * 客户编号
   */
  @ApiModelProperty(name = "customerCode", notes = "客户编号", value = "客户编号")
  private String customerCode;
  /**
   * 客户名称
   */
  @ApiModelProperty(name = "customerName", notes = "客户名称", value = "客户名称")
  private String customerName;
  /**
   * 结束时间
   */
  @ApiModelProperty(name = "endTime", notes = "结束时间", value = "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /**
   * 组织编号
   */
  @ApiModelProperty(name = "orgCode", notes = "组织编号", value = "组织编号")
  private String orgCode;
  /**
   * 组织名称
   */
  @ApiModelProperty(name = "orgName", notes = "组织名称", value = "组织名称")
  private String orgName;
  /**
   * 支付方式
   */
  @ApiModelProperty(name = "payBy", notes = "支付方式", value = "支付方式")
  private String payBy;
  /**
   * 支付方式
   */
  @ApiModelProperty(name = "payByName", notes = "支付方式名称", value = "支付方式名称")
  private String payByName;
  /**
   * 商品层级编码
   */
  @ApiModelProperty(name = "productLevelCode", notes = "商品层级编码", value = "商品层级编码")
  private String productLevelCode;
  /**
   * 商品层级名称
   */
  @ApiModelProperty(name = "productLevelName", notes = "商品层级名称", value = "商品层级名称")
  private String productLevelName;
  /**
   * 门店编号
   */
  @ApiModelProperty(name = "terminalCode", notes = "门店编号", value = "门店编号")
  private String terminalCode;
  /**
   * 门店名称
   */
  @ApiModelProperty(name = "terminalName", notes = "门店名称", value = "门店名称")
  private String terminalName;

  /**
   * ERP会计科目编码
   */
  @ApiModelProperty(name = "ERP会计科目编码", notes = "")
  private String accountingSubjectsCode;

  /**
   * ERP会计科目名称
   */
  @ApiModelProperty(name = "ERP会计科目名称", notes = "")
  private String accountingSubjectsName;

  /**
   * 核销金额
   */
  @ApiModelProperty(name = "核销金额", notes = "核销金额")
  private BigDecimal auditAmount;

  /**
   * 关联客户核销金额
   */
  @ApiModelProperty(name = "关联客户核销金额", notes = "关联客户核销金额")
  private BigDecimal customerAuditAmount;


  /**
   * 自动核销
   */
  @ApiModelProperty(name = "自动核销", notes = "自动核销")
  private String isAutoAudit;

  /**
   * 商品集合
   */
  @ApiModelProperty(name = "productVos", notes = "商品集合", value = "商品集合")
  private List<AuditProductVo> productList;
}
