package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MarketingAuditDetailFieldsEnum {
    detailName("detailName"),
    belongDepartmentName("belongDepartmentName"),
    customerName("customerName"),
    costCenterName("costCenterName"),
    years("years"),
    applyAmount("applyAmount"),
    auditedAmount("auditedAmount"),
    auditAmount("auditAmount"),
    beFullAudit("beFullAudit"),
    beFullAuditStr("beFullAuditStr"),
    ;

    private String dictCode;

    public static MarketingAuditDetailFieldsEnum findByCode(String code) {
        Optional<MarketingAuditDetailFieldsEnum> first = Stream.of(MarketingAuditDetailFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}