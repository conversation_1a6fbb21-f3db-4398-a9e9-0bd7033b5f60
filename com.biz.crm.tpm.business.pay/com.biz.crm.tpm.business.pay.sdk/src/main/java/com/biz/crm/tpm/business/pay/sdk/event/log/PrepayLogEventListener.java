package com.biz.crm.tpm.business.pay.sdk.event.log;

import com.biz.crm.tpm.business.pay.sdk.dto.log.PrepayLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 活动预付 业务日志监听
 * <AUTHOR> <PERSON>
 * @date : 2022-7-1
 */
public interface PrepayLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(PrepayLogEventDto eventDto);
  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(PrepayLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(PrepayLogEventDto eventDto);
}
