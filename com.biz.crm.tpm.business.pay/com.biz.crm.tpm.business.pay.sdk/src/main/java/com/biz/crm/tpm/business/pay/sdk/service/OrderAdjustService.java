package com.biz.crm.tpm.business.pay.sdk.service;


import com.biz.crm.tpm.business.pay.sdk.dto.OrderAdjustDto;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustVo;

import java.util.List;

public interface OrderAdjustService {

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    OrderAdjustVo findByCode(String code);

    /**
     * 创建
     *
     * @param dto
     */
    String create(OrderAdjustDto dto);

    /**
     * 编辑
     *
     * @param dto
     */
    void update(OrderAdjustDto dto);

    /**
     * 创建并确认
     *
     * @param dto
     */
    void submitCreate(OrderAdjustDto dto);

    /**
     * 编辑并确认
     *
     * @param dto
     */
    void submitUpdate(OrderAdjustDto dto);

    /**
     * 根据调整编号确认
     *
     * @param codeList
     */
    void confirm(List<String> codeList);

    /**
     * 删除数据
     *
     * @param idList
     * @return
     */
    void delete(List<String> idList);
}
