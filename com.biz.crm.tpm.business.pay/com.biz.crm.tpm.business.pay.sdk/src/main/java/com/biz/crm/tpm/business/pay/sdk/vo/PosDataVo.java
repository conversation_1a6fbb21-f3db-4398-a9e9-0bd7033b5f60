package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Data
@ApiModel("POS数据")
public class PosDataVo extends TenantFlagOpVo {

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("末级部门编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("客户编码")
    private String customerCode;
    private List<String> customerCodes;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("销售数量")
    private BigDecimal quantity;

    @ApiModelProperty("销售金额")
    private BigDecimal amount;

    @ApiModelProperty("确认状态")
    private String confirmStatus;

    @ApiModelProperty("创建人组织")
    private String createOrgCode;

    @ApiModelProperty("创建人组织名称")
    private String createOrgName;

    @ApiModelProperty("创建人职位编码")
    private String createPostCode;

    @ApiModelProperty("创建人职位")
    private String createPostName;
}
