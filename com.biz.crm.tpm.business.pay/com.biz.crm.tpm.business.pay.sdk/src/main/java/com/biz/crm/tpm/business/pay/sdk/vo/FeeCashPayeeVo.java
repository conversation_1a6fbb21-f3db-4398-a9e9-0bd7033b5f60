package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "FeeCashPayee", description = "费用兑付收款明细")
@Data
public class FeeCashPayeeVo extends TenantFlagOpVo {

    @ApiModelProperty("兑付名称")
    private String cashName;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("核销明细编号")
    private String auditDetailCode;

    @ApiModelProperty("收款方类型")
    private String payeeType;

    @ApiModelProperty("收款方编码")
    private String payeeCode;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("收款方名称")
    private String payeeName;

    @ApiModelProperty("收款方Erp编码")
    private String payeeErpCode;

    @ApiModelProperty("供应商Erp编码")
    private String supplierErpCode;

    @ApiModelProperty("收款方账号")
    private String payeeAccount;

    @ApiModelProperty("户名")
    private String accountName;

    @ApiModelProperty("银联行号")
    private String interbankNumber;

    @ApiModelProperty("开户行")
    private String bankName;

    @ApiModelProperty("本次付款金额")
    private BigDecimal thisPayAmount;

    @ApiModelProperty("期望付款日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date payDate;

    @ApiModelProperty("付款用途")
    private String payPurpose;

    @ApiModelProperty("付款方式")
    private String payType;

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("回单标记")
    private String receiptFlag;

    @ApiModelProperty("回单")
    private String receipt;

    @ApiModelProperty("预付描述")
    private String description;

    @ApiModelProperty("付款状态")
    private String payStatus;

    @ApiModelProperty("收款行编号")
    private String lineCode;

    @ApiModelProperty("付款成功时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paySucessDate;

    @ApiModelProperty("待结转金额")
    private BigDecimal kouJianPrepareCarryAmount;
}
