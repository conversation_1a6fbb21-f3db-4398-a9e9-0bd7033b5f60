package com.biz.crm.tpm.business.pay.sdk.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditActivityItemVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 费用核销;(tpm_audit)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
public interface AuditService {

  /**
   * 生成操作标记
   */
  String preSave();

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<AuditVo> findByConditions(Pageable pageable, AuditDto dto);

  /**
   * 分页活动明细数据
   *
   * @param pageable
   * @param dto
   * @return
   */
  Page<AuditActivityItemVo> findActivitiesDetailByConditions(Pageable pageable, ActivitiesDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AuditVo findById(String id);

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  AuditVo findByCode(String code);

  /**
   * 通过活动明细编号查询核销数据(核销中只有一条或没有，不允许多条明细同时核销)
   *
   * @param activitiesDetailCode
   * @return
   */
  List<AuditVo> findByActivitiesDetailCode(String activitiesDetailCode);

  /**
   * 新增数据
   *
   * @param auditDto 实体对象
   * @return 新增结果
   */
  AuditVo create(AuditDto auditDto);

  /**
   * 自动核销新增数据
   *
   * @param auditDto
   * @return
   */
  AuditVo creatForAutoAudit(AuditDto auditDto);

  /**
   * 修改新据
   *
   * @param auditDto 实体对象
   * @return 修改结果
   */
  AuditVo update(AuditDto auditDto);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(List<String> ids);

  /**
   * 通过启用状态查询数据
   *
   * @param enableStatus 状态
   * @return 集合数据
   */
  List<AuditVo> findByEnableStatus(String enableStatus);

  /**
   * 根据核销编号，对核销申请的明细进行退预算占用的检查与执行
   *
   * @param auditCode
   */
  void doRefund(String auditCode);

  /**
   * 更新活动核销状态
   */
  void updateActivitiesAuditStatusByAuditCode(String auditCode);

  /**
   * 更新活动核销状态
   */
  void updateActivitiesAuditStatusByActivitiesDetails(Collection<ActivitiesDetailVo> activitiesDetailCodes);

}
