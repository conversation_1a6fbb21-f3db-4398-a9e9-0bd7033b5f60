package com.biz.crm.tpm.business.pay.sdk.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class InvoiceManualInputVo extends TenantFlagOpVo {
    @ApiModelProperty("发票类型")
    @JSONField(name = "invoiceTypeCode")
    private String type;

    @ApiModelProperty("是否需要验证")
    @JSONField(name = "needVerifyFlag")
    private Boolean needVerifyFlag;

    @ApiModelProperty("发票代码")
    @JSONField(name = "invoiceCode")
    private String code;

    @ApiModelProperty("发票号码(只允许填写数字和字母)")
    @JSONField(name = "invoiceNumber")
    private String invoiceNo;

    @ApiModelProperty("开票日期")
    @JSONField(name = "invoiceDate")
    private String billingDate;

    @ApiModelProperty("校验码(只允许填写数字和字母)")
    @JSONField(name = "checkCodeLastChars")
    private String checkCode;

    @ApiModelProperty("税价合计")
    @JSONField(name = "amount")
    private BigDecimal priceAndTax;

    @ApiModelProperty("不含税金额")
    @JSONField(name = "withoutTaxAmount")
    private BigDecimal amountWithoutTax;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("税额")
    private BigDecimal taxAmount;

    @ApiModelProperty("购买方名称")
    private String purchaser;

    @ApiModelProperty("购买方税号")
    private String pNo;

    @ApiModelProperty("购买方开户行及账号")
    private String pBankAndAccount;

    @ApiModelProperty("购买方地址电话")
    private String pAddressAndPhone;

    @ApiModelProperty("销售方名称")
    private String seller;

    @ApiModelProperty("销售方税号")
    private String sNo;

    @ApiModelProperty("销售方开户行及账号")
    private String sBankAndAccount;

    @ApiModelProperty("销售方地址电话")
    private String sAddressAndPhone;

    @ApiModelProperty("是否验证")
    private String checked;

    @ApiModelProperty("校验信息")
    private String checkMsg;

    @ApiModelProperty("文件id")
    private String fileCode;

    @ApiModelProperty("来源系统")
    private String sourceSystem;

    @ApiModelProperty("员工账号")
    private String employeeCode;

    @ApiModelProperty("核算公司代码")
    private String accEntityCode;

    @ApiModelProperty("公司代码-创建人所属公司的OAId")
    private String companyCode;

    @ApiModelProperty("发票明细")
    @JSONField(name = "lines")
    private List<InvoiceManualInputDetailVo> items;
}
