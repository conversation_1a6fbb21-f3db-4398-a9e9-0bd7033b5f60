package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @describe:费用上账商品子表Dto
 * @createTime 2022年06月16日 18:53:00
 */
@ApiModel(value = "AccountProductDto", description = "费用上账商品子表Dto")
@Getter
@Setter
public class AccountProductDto extends TenantFlagOpDto {

  /**
   * 费用上账编码
   */
  @ApiModelProperty(name = "accountCode", notes = "费用上账编码", value = "费用上账编码")
  private String accountCode;
  /**
   * 商品编码
   */
  @ApiModelProperty(name = "productCode", notes = "商品编码", value = "商品编码")
  private String productCode;
  /**
   * 商品名称
   */
  @ApiModelProperty(name = "productName", notes = "商品名称", value = "商品名称")
  private String productName;
}
