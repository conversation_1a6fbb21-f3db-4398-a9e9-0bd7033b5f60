package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 收款方类型
 */
@AllArgsConstructor
@Getter
public enum PaeeTypeEnum {
  PAYEE("payee", "VENDER","供应商"),
  PERSON("person", "EMPLOYEE", "个人"),
  CUSTOMER("customer", "CUSTOMER", "客户"),
  ;
  /** 系统key */
  private String code;
  /**
   * 费控编码
   */
  private String hecCode;
  /** 字典编码 */
  private String desc;

  public static PaeeTypeEnum findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    return Arrays.stream(PaeeTypeEnum.values()).filter(o->o.getCode().equals(code)).findFirst().orElse(null);
  }
}
