package com.biz.crm.tpm.business.pay.sdk.event.log;

import com.biz.crm.tpm.business.pay.sdk.dto.log.AuditLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 费用核销 业务日志监听
 * <AUTHOR> <PERSON>
 * @date : 2022-7-1
 */
public interface AuditLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(AuditLogEventDto eventDto);
  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(AuditLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(AuditLogEventDto eventDto);

}
