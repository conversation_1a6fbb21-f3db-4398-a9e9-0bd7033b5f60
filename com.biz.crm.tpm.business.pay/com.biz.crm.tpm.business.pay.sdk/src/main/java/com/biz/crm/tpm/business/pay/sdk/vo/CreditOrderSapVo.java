package com.biz.crm.tpm.business.pay.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@ApiModel(value = "CreditOrder", description = "贷项订单")
@Data
public class CreditOrderSapVo {

    @ApiModelProperty("贷项订单编号")
    private String CREDITCODE;

    @ApiModelProperty("凭证号")
    private String VOUCHERCODE;

    @ApiModelProperty("贷项订单票扣明细")
    private List<CreditOrderTicketSapVo> DETAILS;
}
