package com.biz.crm.tpm.business.pay.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 参数传递dto：费用核销;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "Audit", description = "费用核销")
@Getter
@Setter
public class AuditDto extends TenantFlagOpDto {
  /**
   * 操作的预授权标记
   */
  @ApiModelProperty(name = "操作的预授权标记", notes = "操作的预授权标记", value = "操作的预授权标记", required = true)
  private String prefix;
  /**
   * 主键
   */
  @ApiModelProperty(name = "id", notes = "主键", value = "主键")
  private String id;
  /**
   * 核销申请名称
   */
  @ApiModelProperty(name = "auditName", notes = "核销申请名称", value = "核销申请名称")
  private String auditName;
  /**
   * 核销申请编号
   */
  @ApiModelProperty(name = "auditCode", notes = "核销申请编号", value = "核销申请编号")
  private String auditCode;
  /**
   * 核销金额汇总
   */
  @ApiModelProperty(name = "totalApplyAmount", notes = "核销金额汇总", value = "核销金额汇总")
  private BigDecimal totalApplyAmount;
  /**
   * 费用核销活动信息
   */
  @ApiModelProperty(name = "auditActivities", notes = "费用核销活动信息", value = "费用核销活动信息")
  private List<AuditActivitiesDto> auditActivities;
  /**
   * 费用核销明细信息
   */
  @ApiModelProperty(name = "auditDetails", notes = "费用核销明细信息", value = "费用核销明细信息")
  private List<AuditDetailDto> auditDetails;
  /**
   * 费用核销附件信息
   */
  @ApiModelProperty(name = "auditFiles", notes = "费用核销明细信息", value = "费用核销明细信息")
  private List<AuditFilesDto> auditFiles;
  /**
   * 核销发票信息
   */
  @ApiModelProperty(value = "核销发票信息")
  private List<AuditInvoiceDto> auditInvoices;
  /**
   * 审批状态
   */
  @ApiModelProperty(name = "processStatus", notes = "审批状态", value = "审批状态")
  private String processStatus;
  /**
   * 自动核销
   */
  @ApiModelProperty(name = "自动核销", notes = "自动核销")
  private String isAutoAudit;

  /**
   * 租户编号
   */
  @ApiModelProperty(name = "tenantCode", value = "租户编号")
  private String tenantCode;
  /**
   * 数据状态（删除状态）
   */
  private String delFlag;

  /**
   * 数据业务状态（启用状态）
   */
  private String enableStatus;

  /**
   * 备注
   */
  private String remark;

  /**
   * 工作流对象
   */
  @ApiModelProperty(value = "工作流对象")
  private ProcessBusinessDto processBusiness;

  /**
   * 活动明细
   */
  @ApiModelProperty("普通活动明细")
  private Map<String, List<?>> items;

}
