package com.biz.crm.tpm.business.pay.sdk.event.log;

import com.biz.crm.tpm.business.pay.sdk.dto.log.MarketingAuditLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

public interface MarketingAuditLogEventListener extends NebulaEvent {

    /**
     * 创建事件
     *
     * @param eventDto
     */
    void onCreate(MarketingAuditLogEventDto eventDto);
    /**
     * 删除事件
     *
     * @param eventDto
     */
    void onDelete(MarketingAuditLogEventDto eventDto);
    /**
     * 更新日志
     *
     * @param eventDto
     */
    void onUpdate(MarketingAuditLogEventDto eventDto);
}
