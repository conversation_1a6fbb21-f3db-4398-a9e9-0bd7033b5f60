package com.biz.crm.tpm.business.pay.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 推送费控状态
 *
 * <AUTHOR>
 * @version v1.0.0
 * @date 2024/7/9 14:08
 */
@Getter
@AllArgsConstructor
public enum HecSendStatusEnum {
    Y("Y", "已推送"),
    N("N", "未推送"),
    NOT_NEED_PUSH("not_need_push", "无需推送"),
    ;
    private final String code;

    private final String desc;

    public static HecSendStatusEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (HecSendStatusEnum typeEnum : values()) {
            if (StringUtils.equals(code, typeEnum.getCode())) {
                return typeEnum;
            }
        }
        return null;
    }

}
