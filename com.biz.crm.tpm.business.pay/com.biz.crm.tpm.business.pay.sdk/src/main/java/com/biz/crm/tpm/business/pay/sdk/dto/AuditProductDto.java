package com.biz.crm.tpm.business.pay.sdk.dto;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 参数传递dto：费用明细商品;
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "AuditProduct",description = "费用明细商品")
@Getter
@Setter
public class AuditProductDto implements Serializable,Cloneable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value = "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 费用核销编号 */
  @ApiModelProperty(name = "auditCode",notes = "费用核销编号", value = "费用核销编号")
  private String auditCode;
  /** 费用核销明细编号 */
  @ApiModelProperty(name = "auditDetailCode",notes = "费用核销明细编号", value = "费用核销明细编号")
  private String auditDetailCode;
  /** 商品编码 */
  @ApiModelProperty(name = "productCode",notes = "商品编码", value = "商品编码")
  private String productCode;
  /** 商品名称 */
  @ApiModelProperty(name = "productName",notes = "商品名称", value = "商品名称")
  private String productName;

}