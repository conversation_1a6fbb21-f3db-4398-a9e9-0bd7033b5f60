package com.biz.crm.tpm.business.pay.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "FeeCash", description = "费用兑付")
@Data
public class FeeCashVo extends TenantFlagOpVo {

    @ApiModelProperty("兑付名称")
    private String cashName;

    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("公司代码")
    private String companyCode;

    @ApiModelProperty("兑付金额汇总")
    private BigDecimal totalCashAmount;

    @ApiModelProperty("申请金额汇总")
    private BigDecimal totalApplyAmount;

    @ApiModelProperty("兑付方式")
    private String cashMethod;

    @ApiModelProperty("兑付类型")
    private String cashType;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("组织编号")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("审批状态")
    private String status;

    @ApiModelProperty("审批流程编码")
    private String processKey;

    @ApiModelProperty("推送日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    private String oaUserName;

    @ApiModelProperty("回传TPM接口唯一标识（接口用）")
    private String businessCode;

    @ApiModelProperty("url")
    private String tpmUrl;

    @ApiModelProperty("下游单号")
    private String externalCode;

    @ApiModelProperty("操作类型")
    private String hecOperationType;

    @ApiModelProperty("凭证号")
    private String voucherCode;

    @ApiModelProperty("核销申请名称")
    private String auditName;

    @ApiModelProperty("核销申请编号")
    private String auditCode;

    @ApiModelProperty("查看流程日志所需地址")
    private String hecReceiptUrl;

    @ApiModelProperty("供应商编码")
    private String payeeCode;

    @ApiModelProperty("供应商SAP编码")
    private String payeeErpCode;

    @ApiModelProperty("供应商名称")
    private String payeeName;

    @ApiModelProperty("付款状态")
    private String payStatus;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户责任人")
    private String dockingName;

    @ApiModelProperty("是否暂存")
    private String beStaging;

    @ApiModelProperty("付款成功时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paySucessDate;

    @ApiModelProperty("审批通过日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date passDate;

    @ApiModelProperty("凭证回传日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date voucherCallbackDate;

    @ApiModelProperty("贷项订单编号")
    private String creditCode;

    @ApiModelProperty("需求类型")
    private String demandType;

    @ApiModelProperty("需求总数量")
    private BigDecimal totalDemandQuantity;

    @ApiModelProperty("需求总金额")
    private BigDecimal totalDemandAmount;

    @ApiModelProperty("预付/兑付明细")
    private List<FeeCashDetailVo> detailList;

    @ApiModelProperty("预付明细")
    private List<FeeCashPrepayVo> prepayList;

    @ApiModelProperty("附件")
    private List<FeeCashFilesVo> filesList;

    @ApiModelProperty("收款明细")
    private List<FeeCashPayeeVo> payeeList;

    @ApiModelProperty("发票")
    private List<FeeCashInvoiceVo> invoiceList;

    @ApiModelProperty("TOC")
    private List<FeeCashTocVo> tocList;

    @ApiModelProperty("票扣")
    private List<FeeCashTicketVo> ticketList;

    @ApiModelProperty("物料采购")
    private List<FeeCashMaterialVo> materialList;
}
