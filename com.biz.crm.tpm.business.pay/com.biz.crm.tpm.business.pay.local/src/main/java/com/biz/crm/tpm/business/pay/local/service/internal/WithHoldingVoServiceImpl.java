package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.PayByStrategy;
import com.biz.crm.tpm.business.pay.local.entity.Account;
import com.biz.crm.tpm.business.pay.local.entity.WithHolding;
import com.biz.crm.tpm.business.pay.local.service.AccountService;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingVoService;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月27日 15:53:00
 */
@Service
public class WithHoldingVoServiceImpl implements WithHoldingVoService {

  @Autowired(required = false)
  private ActivitiesService activitiesService;
  @Autowired(required = false)
  private ActivitiesDetailService activitiesDetailService;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private AccountService accountService;
  @Autowired
  private WithHoldingService withHoldingService;
  @Autowired
  private List<PayByStrategy> payByStrategies;


  /**
   * 查询活动信息
   *
   * @param codes
   * @return
   */
  @Override
  public List<WithHoldingVo> findActivitiesByConditions(List<String> codes) {
    Validate.notEmpty(codes, "活动编码不能为空!");
    Map<String, List<ActivitiesDetailVo>> byActivitiesCodes = activitiesDetailService.findByActivitiesCodes(codes);
    if (CollectionUtils.isEmpty(byActivitiesCodes)) {
      return new ArrayList<>();
    }
    //整理数据
    List<WithHoldingVo> resList = new ArrayList<>();
    byActivitiesCodes.forEach((k, v) -> {
      if (CollectionUtils.isEmpty(v)) {
        return;
      }
      Collection<WithHoldingVo> withHoldingVos = this.nebulaToolkitService.copyCollectionByWhiteList(v, ActivitiesDetailVo.class, WithHoldingVo.class, HashSet.class, ArrayList.class);
      resList.addAll(withHoldingVos);
    });
    if (CollectionUtils.isEmpty(resList)) {
      return resList;
    }
    //初始化上账金额 设置活动明细编码
    for (WithHoldingVo withHoldingVo : resList) {
      withHoldingVo.setAccountAmount(BigDecimal.ZERO);
    }
    List<String> detailCodes = resList.stream().map(WithHoldingVo::getActivitiesDetailCode).collect(Collectors.toList());
    List<Account> byActivitiesDetailCode = accountService.findByActivitiesDetailCode(detailCodes);
    if (CollectionUtils.isEmpty(byActivitiesDetailCode)) {
      return resList;
    }
    Map<String, List<Account>> collect = byActivitiesDetailCode.stream().collect(Collectors.groupingBy(Account::getActivitiesDetailCode));
    Map<String, BigDecimal> amountMap = new HashMap<>();
    collect.forEach((k, v) -> {
      BigDecimal amount = v.stream().filter(z -> Objects.nonNull(z.getAmount()))
              .map(Account::getAmount)
              .filter(Objects::nonNull)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      amountMap.put(k, amount);
    });
    //封装数据 计算金额
    for (WithHoldingVo vo : resList) {
      String activitiesDetailCode = vo.getActivitiesDetailCode();
      BigDecimal amount = amountMap.get(activitiesDetailCode);
      if (Objects.isNull(amount)) {
        amount = BigDecimal.ZERO;
      }
      vo.setAccountAmount(amount);
    }
    List<WithHoldingVo> list = resList.stream().filter(e -> e.getApplyAmount().compareTo(e.getAccountAmount()) > 0).collect(Collectors.toList());
    return list;
  }

  /**
   * 通过ID查询
   *
   * @param id
   * @return
   */
  @Override
  public WithHoldingVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    WithHolding byId = this.withHoldingService.findById(id);
    if (Objects.isNull(byId)) {
      return null;
    }
    String code = byId.getActivitiesDetailCode();
    WithHoldingVo withHoldingVo = this.nebulaToolkitService.copyObjectByWhiteList(byId, WithHoldingVo.class, HashSet.class, ArrayList.class);
    List<Account> accountList = this.accountService.findByActivitiesDetailCode(Lists.newArrayList(code));
    if (!CollectionUtils.isEmpty(accountList)) {
      BigDecimal reduce = accountList.stream().filter(k -> Objects.nonNull(k.getAmount()))
              .map(Account::getAmount)
              .filter(Objects::nonNull)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      withHoldingVo.setAccountAmount(reduce);
    } else {
      withHoldingVo.setAccountAmount(BigDecimal.ZERO);
    }
    Map<String, String> payBy = payByStrategies.stream().collect(Collectors.toMap(PayByStrategy::getCode, PayByStrategy::getName));
    if (!CollectionUtils.isEmpty(payBy)) {
      withHoldingVo.setPayByName(payBy.get(withHoldingVo.getPayBy()));
    }
    return withHoldingVo;
  }

  /**
   * 根据活动明细编码查询计提金额
   *
   * @param codes
   * @return
   */
  @Override
  public Map<String, BigDecimal> findBySchemeDetailCodes(List<String> codes) {
    return withHoldingService.findBySchemeDetailCodes(codes);
  }

  /**
   * 根据业务明细编码查询管报实际金额
   *
   * @param codes
   * @return
   */
  @Override
  public Map<String, BigDecimal> findByBusinessCodes(List<String> codes) {
    return withHoldingService.findByBusinessCodes(codes);
  }

  @Override
  public List<WithHoldingVo> findByCollectCode(String collectCode) {
    if (StringUtils.isBlank(collectCode)) {
      return Lists.newArrayList();
    }
    return withHoldingService.findByCollectCode(collectCode);
  }
}
