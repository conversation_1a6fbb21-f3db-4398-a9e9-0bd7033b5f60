package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashInvoiceRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashInvoiceVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

/**
 * 费用兑付预付明细分页缓存
 */
@Slf4j
@Component
public class FeeCashInvoicePageCacheHelper extends BusinessPageCacheHelper<FeeCashInvoiceVo, FeeCashInvoiceDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private FeeCashInvoiceRepository feeCashInvoiceRepository;
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return FeeCashConstant.CACHE_KEY_PREFIX_INVOICE;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<FeeCashInvoiceDto> getDtoClass() {
        return FeeCashInvoiceDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<FeeCashInvoiceVo> getVoClass() {
        return FeeCashInvoiceVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param feeCashInvoiceDto
     * @param cacheKey
     */
    @Override
    public List<FeeCashInvoiceDto> findDtoListFromRepository(FeeCashInvoiceDto feeCashInvoiceDto, String cacheKey) {
        if (StringUtils.isBlank(feeCashInvoiceDto.getCashCode())) {
            return new ArrayList<>();
        }
        List<FeeCashInvoiceVo> feeCashInvoiceVos = feeCashInvoiceRepository.findByCode(feeCashInvoiceDto.getCashCode());
        return CollectionUtils.isNotEmpty(feeCashInvoiceVos) ? (List<FeeCashInvoiceDto>) nebulaToolkitService.copyCollectionByBlankList(feeCashInvoiceVos, FeeCashInvoiceVo.class, FeeCashInvoiceDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashInvoiceDto> newItem(String cacheKey, List<FeeCashInvoiceDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashInvoiceDto> copyItem(String cacheKey, List<FeeCashInvoiceDto> itemList) {
        List<FeeCashInvoiceDto> newItemList = (List<FeeCashInvoiceDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, FeeCashInvoiceDto.class, FeeCashInvoiceDto.class, HashSet.class, ArrayList.class);
        for (FeeCashInvoiceDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 获取Dto中的主键
     *
     * @param feeCashInvoiceDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(FeeCashInvoiceDto feeCashInvoiceDto) {
        return feeCashInvoiceDto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param feeCashInvoiceDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(FeeCashInvoiceDto feeCashInvoiceDto) {
        return feeCashInvoiceDto.getChecked();
    }

}
