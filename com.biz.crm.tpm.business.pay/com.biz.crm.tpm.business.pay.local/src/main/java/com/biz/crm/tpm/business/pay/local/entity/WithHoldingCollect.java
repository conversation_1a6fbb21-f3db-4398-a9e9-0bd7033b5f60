package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe: 费用预提汇总
 * @createTime 2022年06月25日 10:19:00
 */
@ApiModel(value = "tpm_with_holding_collect", description = "费用预提汇总")
@TableName("tpm_with_holding_collect")
@Getter
@Setter
@Entity(name = "tpm_with_holding_collect")
@org.hibernate.annotations.Table(appliesTo = "tpm_with_holding_collect", comment = "费用预提汇总")
@Table(name = "tpm_with_holding_collect", indexes = {
        @Index(name = "tpm_with_holding_collect_uq1", columnList = "collect_code",unique = true),
        @Index(name = "tpm_with_holding_collect_idx1", columnList = "voucher_code"),
})
public class WithHoldingCollect extends TenantFlagOpEntity {

  @ApiModelProperty("汇总单号")
  @Column(name = "collect_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '汇总单号 '")
  private String collectCode;

  /**
   * 推送状态 枚举
   */
  @ApiModelProperty(name = "推送状态", notes = "推送状态")
  @Column(name = "push_status",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '预提类型 '")
  private String pushStatus;

  /**
   * 组织编号
   */
  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  @Column(name = "org_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编号 '")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  @Column(name = "org_name",length = 128, columnDefinition = "VARCHAR(128) COMMENT '组织名称 '")
  private String orgName;


  /**
   * 预提金额
   */
  @ApiModelProperty(name = "预提金额", notes = "预提金额")
  @Column(name = "with_holding_amount", length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '预提金额 '")
  private BigDecimal withHoldingAmount;



  @ApiModelProperty("预提年月")
  @Column(name = "year_month_ly", columnDefinition = "varchar(20) comment '预提年月'")
  private String yearMonthLy;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("部门编码")
  @Column(name = "department_code", columnDefinition = "varchar(32) comment '部门编码'")
  private String departmentCode;

  @ApiModelProperty("部门名称")
  @Column(name = "department_name", columnDefinition = "varchar(64) comment '部门名称'")
  private String departmentName;

  @ApiModelProperty("凭证号")
  @Column(name = "voucher_code", length = 64, columnDefinition = " varchar(64) COMMENT '凭证号'")
  private String voucherCode;

  @ApiModelProperty("失败原因")
  @Column(name = "fail_msg", length = 500, columnDefinition = "varchar(500) COMMENT '失败原因'")
  private String failMsg;

  @ApiModelProperty("审批状态")
  @Column(name = "status",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '审批状态 '")
  private String status;

  @ApiModelProperty("推送日期")
  @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("审批通过日期")
  @Column(name = "pass_date", columnDefinition = "datetime COMMENT '审批通过日期'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date passDate;

  @ApiModelProperty("OA人员id")
  @Column(name = "oa_id", columnDefinition = "varchar(32) COMMENT 'OA人员id'")
  private String oaId;

  @ApiModelProperty("OA人员账号")
  @Column(name = "oa_user_name", columnDefinition = "varchar(32) COMMENT 'OA人员账号'")
  private String oaUserName;

  @ApiModelProperty("审批流程编码")
  @Column(name = "process_key",  length = 32, columnDefinition = "VARCHAR(32) COMMENT '审批流程编码 '")
  private String processKey;

  @ApiModelProperty("费控单号")
  @Column(name = "external_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '费控单号 '")
  private String externalCode;

  @ApiModelProperty("业务编码")
  @Column(name = "business_code", length = 64, columnDefinition = "varchar(64) COMMENT '业务编码'")
  private String businessCode;

  @ApiModelProperty("查看流程日志所需地址")
  @Column(name = "hec_receipt_url", columnDefinition = "VARCHAR(1024) COMMENT '查看流程日志所需地址 '")
  private String hecReceiptUrl;
}
