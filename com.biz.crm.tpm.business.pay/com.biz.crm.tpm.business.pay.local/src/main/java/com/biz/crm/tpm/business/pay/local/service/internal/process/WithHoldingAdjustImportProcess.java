package com.biz.crm.tpm.business.pay.local.service.internal.process;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingRepository;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.enums.WithHoldingTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingAdjustImportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WithHoldingAdjustImportProcess implements ImportProcess<WithHoldingAdjustImportVo> {

    @Autowired(required = false)
    private WithHoldingService withHoldingService;
    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;
    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, WithHoldingAdjustImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, WithHoldingAdjustImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");

        for (Map.Entry<Integer, WithHoldingAdjustImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            WithHoldingAdjustImportVo vo = row.getValue();
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getBusinessCode()), "业务编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getActualReportAmountStr()), "管报实际计提金额，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getReduceAmountStr()), "调整金额不能为空！");
            try {
                new BigDecimal(vo.getActualReportAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "管报实际计提金额类型转换失败！");
            }
            try {
                new BigDecimal(vo.getActualReportAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "调整金额类型转换失败！");
            }
            this.validateIsTrue(new BigDecimal(vo.getReduceAmountStr()).compareTo(BigDecimal.ZERO)>0, "调整金额不能为负数！");
            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, WithHoldingAdjustImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        List<String> businessCodes = new ArrayList<>();
        data.values().forEach(e -> businessCodes.add(e.getBusinessCode()));
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findByBusinessCodesVo(businessCodes);
        Map<String, WithHoldingVo> whMap = withHoldingVos.stream().collect(Collectors.toMap(WithHoldingVo::getBusinessCode, Function.identity(), (a, b) -> a));

        MarketingPlanCaseVo planCaseDto = new MarketingPlanCaseVo();
        planCaseDto.setChangeFlag(BooleanEnum.FALSE.getCapital());
        planCaseDto.setSchemeDetailCodeList(businessCodes);
        Page<MarketingPlanCaseVo> caseVoPage = withHoldingService.findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseDto);
        Map<String, MarketingPlanCaseVo> caseVoMap = (caseVoPage.getTotal() > 0 ? caseVoPage.getRecords() : new ArrayList<MarketingPlanCaseVo>()).stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));

        for (Map.Entry<Integer, WithHoldingAdjustImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            WithHoldingAdjustImportVo vo = row.getValue();
            WithHoldingVo withHoldingVo = whMap.get(vo.getBusinessCode());
            if (withHoldingVo != null) {
                validateIsTrue(WithHoldingTypeEnum.NOT_AUDIT.getDictCode().equals(withHoldingVo.getWithHoldingType()), "仅允许调整“已申请未结案”的计提数据");
                validateIsTrue(ConfirmStatusEnum.UNCONFIRMED.getCode().equals(withHoldingVo.getConfirmStatus()), "仅允许调整“未确认”的计提数据");
                validateIsTrue(!ProcessStatusEnum.PASS.getDictCode().equals(withHoldingVo.getStatus()) && !ProcessStatusEnum.COMMIT.getDictCode().equals(withHoldingVo.getStatus()),
                        "审批通过、审批中的计提数据不允许调整");
                vo.setWithHoldingReportReduceAmount(new BigDecimal(vo.getActualReportAmountStr()));
                if (vo.getWithHoldingReportReduceAmount().compareTo(withHoldingVo.getWithHoldingReportAmount()) <= 0) {
                    if (MapUtils.isNotEmpty(caseVoMap) && caseVoMap.containsKey(withHoldingVo.getBusinessCode())) {
                        MarketingPlanCaseVo caseVo = caseVoPage.getRecords().get(0);
                        //调整后金额不允许小于当前活动的已结案金额
                        Validate.isTrue(vo.getWithHoldingReportReduceAmount().compareTo(caseVo.getAuditAmount()) >= 0, "活动%s已结案%s元，计提的费用不允许小于结案的金额，请调整“计提实际金额”！", caseVo.getSchemeDetailCode(), caseVo.getAuditAmount());
                        if (MarketingPlanCaseTypeEnum.back.getCode().equals(caseVo.getCaseType())) {
                            withHoldingVo.setActualReportAmount(vo.getWithHoldingReportReduceAmount());
                            withHoldingVo.setWithHoldingReportReduceAmount(withHoldingVo.getWithHoldingReportAmount().subtract(withHoldingVo.getActualReportAmount()));
                        } else {
                            withHoldingVo.setActualAmount(vo.getWithHoldingReportReduceAmount());
                            withHoldingVo.setReduceAmount(withHoldingVo.getWithHoldingAmount().subtract(withHoldingVo.getActualAmount()));
                            withHoldingVo.setActualReportAmount(vo.getWithHoldingReportReduceAmount());
                            withHoldingVo.setWithHoldingReportReduceAmount(withHoldingVo.getWithHoldingReportAmount().subtract(withHoldingVo.getActualReportAmount()));
                        }
                    } else {
                        withHoldingVo.setActualAmount(vo.getWithHoldingReportReduceAmount());
                        withHoldingVo.setReduceAmount(withHoldingVo.getWithHoldingAmount().subtract(withHoldingVo.getActualAmount()));
                        withHoldingVo.setActualReportAmount(vo.getWithHoldingReportReduceAmount());
                        withHoldingVo.setWithHoldingReportReduceAmount(withHoldingVo.getWithHoldingReportAmount().subtract(withHoldingVo.getActualReportAmount()));
                    }
                    withHoldingVo.setBeAdjust(BooleanEnum.TRUE.getCapital());
                } else {
                    this.validateIsTrue(false, "计提实际金额，必须小于计提金额");
                }
            } else {
                this.validateIsTrue(false, "未找到计提编号所对应的计提数据！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        withHoldingService.importUpdate(new ArrayList<>(whMap.values()));
        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<WithHoldingAdjustImportVo> findCrmExcelVoClass() {
        return WithHoldingAdjustImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "WITH_HOLDING_ADJUST_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "费用计提批量调整导入模板";
    }
}
