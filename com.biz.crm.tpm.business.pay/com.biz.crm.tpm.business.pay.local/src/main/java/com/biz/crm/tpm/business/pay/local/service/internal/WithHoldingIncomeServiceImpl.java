package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.collection.CollUtil;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingIncome;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingIncomeRepository;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingIncomeService;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WithHoldingIncomeServiceImpl implements WithHoldingIncomeService {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private WithHoldingIncomeRepository withHoldingIncomeRepository;

    @Resource
    private OrgVoService orgVoService;

    @Override
    public List<WithHoldingIncomeVo> findByYear(List<String> year) {
        List<WithHoldingIncome> incomes = this.withHoldingIncomeRepository.findByYear(year);
        if (CollUtil.isEmpty(incomes)) {
            return CollUtil.newArrayList();
        }
        List<WithHoldingIncomeVo> incomeVoList = (List<WithHoldingIncomeVo>) this.nebulaToolkitService.copyCollectionByWhiteList(incomes,
                WithHoldingIncome.class, WithHoldingIncomeVo.class, HashSet.class, ArrayList.class);
        return incomeVoList;
    }


    @Override
    public List<WithHoldingIncomeVo> findListByCondition(WithholdingIncomeQueryDto dto) {
        return withHoldingIncomeRepository.findListByCondition(dto);
    }

    @Override
    public List<WithHoldingIncomeVo> findListByYearsAndChildrenOrgCodes(WithholdingIncomeQueryDto dto) {
        List<OrgVo> orgVos = orgVoService.findAllChildrenByOrgCodes(dto.getOrgCodes());
        if (CollUtil.isEmpty(orgVos)) {
            return Lists.newArrayList();
        }
        List<String> orgCodes = orgVos.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
        dto.setOrgCodes(orgCodes);
        return withHoldingIncomeRepository.findListByYearsAndChildrenOrgCodes(dto);
    }
}
