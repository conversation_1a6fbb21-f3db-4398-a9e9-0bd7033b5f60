package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：费用兑付发票;
 * <AUTHOR> Keller
 * @date : 2022-7-6
 */
@ApiModel(value = "FeeCashInvoice",description = "费用兑付发票")
@TableName("tpm_fee_cash_invoice")
@Getter
@Setter
@Entity(name = "tpm_fee_cash_invoice")
@org.hibernate.annotations.Table(appliesTo = "tpm_fee_cash_invoice", comment = "费用兑付发票")
@Table(name = "tpm_fee_cash_invoice")
public class FeeCashInvoice extends TenantOpEntity {

  @ApiModelProperty("兑付名称")
  @Column(name = "cash_name", columnDefinition = "VARCHAR(255) COMMENT '兑付名称 '")
  private String cashName;

  @ApiModelProperty("兑付编号")
  @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
  private String cashCode;

  @ApiModelProperty("核销明细编号")
  @Column(name = "audit_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销明细编号 '")
  private String auditDetailCode;

  /** 发票编号 */
  @ApiModelProperty(name = "invoiceNo",notes = "发票编号", value= "发票编号")
  @Column(name = "invoice_no", columnDefinition = "VARCHAR(255) COMMENT '发票编号 '")
  private String invoiceNo;

  /** 发票明细编号 */
  @ApiModelProperty("发票明细号")
  @Column(name = "invoice_detail_no", columnDefinition = "varchar(128) comment '发票明细编码'")
  private String invoiceDetailNo;

  /** 使用金额 */
  @ApiModelProperty(name = "useAmount",notes = "使用金额", value= "使用金额")
  @Column(name = "use_amount", length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '使用金额 '")
  private BigDecimal useAmount;

  @ApiModelProperty("货物或应税劳务名称")
  @Column(name = "name",columnDefinition = "varchar(256) comment '货物或应税劳务名称'")
  private String name;

  @ApiModelProperty("税价合计")
  @Column(name = "price_and_tax", columnDefinition = "decimal(20,4) COMMENT '税价合计'")
  private BigDecimal priceAndTax;

  @ApiModelProperty("不含税金额")
  @Column(name = "amount_without_tax", columnDefinition = "decimal(20,4) COMMENT '不含税金额'")
  private BigDecimal amountWithoutTax;

  @ApiModelProperty("税额")
  @Column(name = "tax_amount", columnDefinition = "decimal(20,4) COMMENT '税额'")
  private BigDecimal taxAmount;

  @ApiModelProperty("发票类型")
  @Column(name = "type", length = 64, columnDefinition = "varchar(64) COMMENT '发票类型'")
  private String type;

  @ApiModelProperty("开票日期")
  @Column(name = "billing_date", length = 20, columnDefinition = "varchar(20) COMMENT '开票日期 '")
  private String billingDate;

  @ApiModelProperty("校验码(只允许填写数字和字母)")
  @Column(name = "check_code", columnDefinition = "varchar(64) COMMENT '校验码(只允许填写数字和字母)'")
  private String checkCode;

  @ApiModelProperty("发票代码")
  @Column(name = "code", length = 64, columnDefinition = "varchar(64) COMMENT '发票代码'")
  private String code;

  @ApiModelProperty("税率")
  @Column(name = "tax_rate", columnDefinition = "decimal(20,4) COMMENT '税率'")
  private BigDecimal taxRate;

  @ApiModelProperty("购买方名称")
  @Column(name = "purchaser", columnDefinition = "varchar(64) COMMENT '购买方名称'")
  private String purchaser;

  @ApiModelProperty("购买方税号")
  @Column(name = "p_no", columnDefinition = "varchar(64) COMMENT '购买方税号'")
  private String pNo;

  @ApiModelProperty("购买方开户行及账号")
  @Column(name = "p_bank_and_account", columnDefinition = "varchar(64) COMMENT '购买方开户行及账号'")
  private String pBankAndAccount;

  @ApiModelProperty("购买方地址电话")
  @Column(name = "p_address_and_phone", columnDefinition = "varchar(64) COMMENT '购买方地址电话'")
  private String pAddressAndPhone;

  @ApiModelProperty("销售方名称")
  @Column(name = "seller", columnDefinition = "varchar(64) COMMENT '销售方名称'")
  private String seller;

  @ApiModelProperty("销售方税号")
  @Column(name = "s_no", columnDefinition = "varchar(64) COMMENT '销售方税号'")
  private String sNo;

  @ApiModelProperty("销售方开户行及账号")
  @Column(name = "s_bank_and_account", columnDefinition = "varchar(64) COMMENT '销售方开户行及账号'")
  private String sBankAndAccount;

  @ApiModelProperty("销售方地址电话")
  @Column(name = "s_address_and_phone", columnDefinition = "varchar(64) COMMENT '销售方地址电话'")
  private String sAddressAndPhone;

  @ApiModelProperty("是否验证")
  @Column(name = "checked", columnDefinition = "varchar(10) comment '是否验证'")
  private String checked;

  @ApiModelProperty("校验信息")
  @Column(name = "check_msg", columnDefinition = "text comment '校验信息'")
  private String checkMsg;

  @ApiModelProperty("文件id")
  @Column(name = "file_code", columnDefinition = "varchar(256) comment '文件Code'")
  private String fileCode;

  @ApiModelProperty("实际报销金额")
  @Column(name = "reimbursement_amount", columnDefinition = "decimal(20,4) COMMENT '实际报销金额'")
  private BigDecimal reimbursementAmount;

  @ApiModelProperty("剩余可报销金额")
  @Column(name = "available_reimbursement_amount", columnDefinition = "decimal(20,4) COMMENT '剩余可报销金额'")
  private BigDecimal availableReimbursementAmount;

  @ApiModelProperty("活动细类编码")
  @Column(name = "detail_code", columnDefinition = "varchar(32) comment '活动细类编码'")
  private String detailCode;

  @ApiModelProperty("活动细类名称")
  @Column(name = "detail_name", columnDefinition = "varchar(64) comment '活动细类名称'")
  private String detailName;

  @ApiModelProperty("兑付明细编号")
  @Column(name = "cash_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付明细编号 '")
  private String cashDetailCode;

}