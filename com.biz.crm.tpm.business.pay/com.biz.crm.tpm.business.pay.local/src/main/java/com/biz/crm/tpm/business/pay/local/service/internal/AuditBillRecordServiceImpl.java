package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.tpm.business.pay.local.dto.AuditBillRecordDto;
import com.biz.crm.tpm.business.pay.local.entity.AuditBillRecord;
import com.biz.crm.tpm.business.pay.local.repository.AuditBillRecordRepository;
import com.biz.crm.tpm.business.pay.local.service.AuditBillRecordService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.LinkedHashSet;

/**
 * 核销账单明细记录;(tpm_audit_bill_record)表服务实现类
 *
 * <AUTHOR> <PERSON>
 * @date : 2022-6-23
 */
@Service("auditBillRecordService")
public class AuditBillRecordServiceImpl implements AuditBillRecordService {
  @Autowired
  private AuditBillRecordRepository auditBillRecordRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 新增数据
   *
   * @param auditBillRecordDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AuditBillRecord create(AuditBillRecordDto auditBillRecordDto) {

    this.createValidate(auditBillRecordDto);
    AuditBillRecord auditBillRecord = this.nebulaToolkitService.copyObjectByWhiteList(auditBillRecordDto, AuditBillRecord.class, LinkedHashSet.class, ArrayList.class);
    auditBillRecord.setTenantCode(TenantUtils.getTenantCode());
    this.auditBillRecordRepository.saveOrUpdate(auditBillRecord);
    return auditBillRecord;
  }

  /**
   * 创建验证
   *
   * @param auditBillRecordDto
   */
  private void createValidate(AuditBillRecordDto auditBillRecordDto) {
    Validate.notNull(auditBillRecordDto, "新增时，对象信息不能为空！");
    Validate.isTrue(auditBillRecordDto.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(auditBillRecordDto.getActivitiesDetailCode(), "新增数据时，活动明细编码不能为空！");
    Validate.notNull(auditBillRecordDto.getChangeAmount(), "新增数据时，申请金额不能为空！");
    Validate.notNull(auditBillRecordDto.getType(), "新增数据时，操作类型不能为空！");
  }

}
