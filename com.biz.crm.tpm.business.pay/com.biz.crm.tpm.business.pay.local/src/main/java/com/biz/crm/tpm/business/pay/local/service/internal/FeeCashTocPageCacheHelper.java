package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashTocRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTocDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTocVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 费用兑付分页缓存
 */
@Slf4j
@Component
public class FeeCashTocPageCacheHelper extends BusinessPageCacheHelper<FeeCashTocVo, FeeCashTocDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private FeeCashTocRepository feeCashTocRepository;
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return FeeCashConstant.CACHE_KEY_PREFIX_TOC;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<FeeCashTocDto> getDtoClass() {
        return FeeCashTocDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<FeeCashTocVo> getVoClass() {
        return FeeCashTocVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param feeCashTocDto
     * @param cacheKey
     */
    @Override
    public List<FeeCashTocDto> findDtoListFromRepository(FeeCashTocDto feeCashTocDto, String cacheKey) {
        if (StringUtils.isBlank(feeCashTocDto.getCashCode())) {
            return new ArrayList<>();
        }
        List<FeeCashTocVo> feeCashTocVos = feeCashTocRepository.findByCode(feeCashTocDto.getCashCode());
        return CollectionUtils.isNotEmpty(feeCashTocVos) ? (List<FeeCashTocDto>) nebulaToolkitService.copyCollectionByBlankList(feeCashTocVos, FeeCashTocVo.class, FeeCashTocDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashTocDto> newItem(String cacheKey, List<FeeCashTocDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashTocDto> copyItem(String cacheKey, List<FeeCashTocDto> itemList) {
        List<FeeCashTocDto> newItemList = (List<FeeCashTocDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, FeeCashTocDto.class, FeeCashTocDto.class, HashSet.class, ArrayList.class);
        for (FeeCashTocDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 导入新增数据
     * @param cacheKey
     * @param itemList
     * @return
     */
    public void importNewItem(String cacheKey, List<FeeCashTocDto> itemList) {
        String redisCacheIdKey = this.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = this.getRedisCacheDataKey(cacheKey);

        Object[] newIdArr = itemList.stream().map(this::getDtoKey).toArray();
        redisService.lPushAll(redisCacheIdKey, this.getExpireTime(),newIdArr);

        Map<Object, FeeCashTocDto> updateMap = itemList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }

    /**
     * 获取Dto中的主键
     *
     * @param feeCashTocDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(FeeCashTocDto feeCashTocDto) {
        return feeCashTocDto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param feeCashTocDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(FeeCashTocDto feeCashTocDto) {
        return feeCashTocDto.getChecked();
    }

}
