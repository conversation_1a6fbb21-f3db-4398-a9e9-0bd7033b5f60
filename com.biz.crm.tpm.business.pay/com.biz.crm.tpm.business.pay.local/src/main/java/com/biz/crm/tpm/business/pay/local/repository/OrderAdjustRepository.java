package com.biz.crm.tpm.business.pay.local.repository;



import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.OrderAdjust;
import com.biz.crm.tpm.business.pay.local.entity.OrderAdjust;
import com.biz.crm.tpm.business.pay.local.mapper.OrderAdjustMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 订单调整(OrderAdjust)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-01 20:23:43
 */
@Component
public class OrderAdjustRepository extends ServiceImpl<OrderAdjustMapper, OrderAdjust> {

  @Autowired
  private OrderAdjustMapper orderAdjustMapper;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public OrderAdjustVo findByCode(String code) {
    List<OrderAdjust> list = this.lambdaQuery().eq(OrderAdjust::getAdjustCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? nebulaToolkitService.copyObjectByWhiteList(list.get(0), OrderAdjustVo.class, LinkedHashSet.class, ArrayList.class) :
            new OrderAdjustVo();
  }

  /**
   * 按编码查询
   *
   * @param codes
   * @return
   */
  public List<OrderAdjustVo> findByCodes(List<String> codes) {
    List<OrderAdjust> list = this.lambdaQuery().in(OrderAdjust::getAdjustCode, codes).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, OrderAdjust.class, OrderAdjustVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param codes
   * @return
   */
  public void deleteByCodes(List<String> codes) {
    this.lambdaUpdate().in(OrderAdjust::getAdjustCode, codes).remove();
  }

  /**
   * 确认
   *
   * @param codes
   */
  public void confirm(List<String> codes) {
    this.lambdaUpdate().in(OrderAdjust::getAdjustCode, codes)
            .set(OrderAdjust::getConfirmStatus, ConfirmStatusEnum.CONFIRMED.getCode()).update();
  }
}

