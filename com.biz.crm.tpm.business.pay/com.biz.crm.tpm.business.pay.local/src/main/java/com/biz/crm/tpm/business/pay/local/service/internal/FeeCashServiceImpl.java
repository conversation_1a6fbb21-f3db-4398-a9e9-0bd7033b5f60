package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.eunm.CashStatusEnum;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.dms.business.order.common.sdk.enums.ItemTypeEnum;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerDockingVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.pay.local.calcomponent.CalCloseReversComponent;
import com.biz.crm.tpm.business.pay.local.entity.*;
import com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayDetailRecordMapper;
import com.biz.crm.tpm.business.pay.local.repository.*;
import com.biz.crm.tpm.business.pay.sdk.constant.DeliveryReplenishmentConstant;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.constant.MarketingAuditConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.*;
import com.biz.crm.tpm.business.pay.sdk.dto.log.FeeCashLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.enums.*;
import com.biz.crm.tpm.business.pay.sdk.event.log.FeeCashLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.*;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FeeCashServiceImpl extends BusinessPageCacheServiceImpl<FeeCashTicketVo, FeeCashTicketDto> implements FeeCashService {

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private FeeCashRepository feeCashRepository;
    @Autowired(required = false)
    private FeeCashDetailRepository feeCashDetailRepository;
    @Autowired(required = false)
    private FeeCashFilesRepository feeCashFilesRepository;
    @Autowired(required = false)
    private FeeCashPayeeRepository feeCashPayeeRepository;
    @Autowired(required = false)
    private FeeCashInvoiceRepository feeCashInvoiceRepository;
    @Autowired(required = false)
    private FeeCashTicketRepository feeCashTicketRepository;
    @Autowired(required = false)
    private FeeCashPrepayRepository feeCashPrepayRepository;
    @Autowired(required = false)
    private FeeCashTocRepository feeCashTocRepository;
    @Autowired(required = false)
    private FeeCashMaterialRepository feeCashMaterialRepository;
    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;
    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;
    @Autowired(required = false)
    private FeeCashTocService feeCashTocService;
    @Autowired(required = false)
    private FeeCashDetailService feeCashDetailService;
    @Autowired(required = false)
    private FeeCashPrepayService feeCashPrepayService;
    @Autowired(required = false)
    private FeeCashInvoiceService feeCashInvoiceService;
    @Autowired(required = false)
    private FeeCashMaterialService feeCashMaterialService;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private FeeCashOaService feeCashOaService;
    @Autowired(required = false)
    private CreditOrderRepository creditOrderRepository;
    @Autowired(required = false)
    private CreditOrderTicketRepository creditOrderTicketRepository;
    @Autowired(required = false)
    private CustomerVoService customerVoService;
    @Autowired(required = false)
    private OrgVoService orgVoService;
    @Autowired(required = false)
    private ActivityPrepayDetailRecordService activityPrepayDetailRecordService;
    @Autowired(required = false)
    private InvoiceDetailRepository invoiceDetailRepository;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private InvoiceService invoiceService;
    @Autowired(required = false)
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;
    @Autowired(required = false)
    private BudgetSubjectsVoService budgetSubjectsVoService;
    @Autowired(required = false)
    private MaterialVoService materialVoService;

    @Autowired(required = false)
    private FeeCashSendOaMsgService feeCashSendOaMsgService;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired(required = false)
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;

    @Autowired(required = false)
    private ActivityPrepayDetailRecordRepository activityPrepayDetailRecordRepository;

    private final static String INDIVIDUAL_REIMBURSEMENT_TYPE = "individual_reimbursement_type";

    @Autowired
    ActivityPrepayDetailRecordMapper activityPrepayDetailRecordMapper;

    @Autowired(required = false)
    private FeeCashSendHecService  feeCashSendHecService;

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    @Override
    public FeeCashVo findByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return null;
        }
        FeeCash feeCash = feeCashRepository.findByCode(code);
        if (Objects.isNull(feeCash)) {
            return null;
        }
        FeeCashVo feeCashVo = nebulaToolkitService.copyObjectByWhiteList(feeCash, FeeCashVo.class, LinkedHashSet.class, ArrayList.class);
        List<FeeCashDetailVo> detailVos = feeCashDetailRepository.findByCode(code);
        List<FeeCashFilesVo> filesVos = feeCashFilesRepository.findByCode(code);
        List<FeeCashPayeeVo> payeeVos = feeCashPayeeRepository.findByCode(code);
        List<FeeCashInvoiceVo> invoiceVos = feeCashInvoiceRepository.findByCode(code);
        List<FeeCashPrepayVo> prepayVos = feeCashPrepayRepository.findByCode(code);
        List<FeeCashTocVo> tocVoList = feeCashTocRepository.findByCode(code);
        List<FeeCashTicketVo> ticketVos = feeCashTicketRepository.findByCode(code);
        List<FeeCashMaterialVo> materialVos = feeCashMaterialRepository.findByCode(code);

        feeCashVo.setDetailList(detailVos);
        feeCashVo.setFilesList(filesVos);
        feeCashVo.setPayeeList(payeeVos);
        feeCashVo.setInvoiceList(invoiceVos);
        feeCashVo.setPrepayList(prepayVos);
        feeCashVo.setTocList(tocVoList);
        feeCashVo.setTicketList(ticketVos);
        feeCashVo.setMaterialList(materialVos);
        feeCashVo.setBusinessCode(feeCashVo.getCashCode());
        return feeCashVo;
    }

    /**
     * 新增数据
     *
     * @param dto      实体对象
     * @param cacheKey
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> create(FeeCashDto dto, boolean autoCash, String cacheKey, String cacheKeyToc,
                               String cacheKeyDetail, String cacheKeyPrepay, String cacheKeyInvoice, String cacheKeyMaterial, boolean commit, boolean staging) {
        if (StringUtils.isNotBlank(cacheKey)) {
            List<FeeCashTicketDto> ticketDtoList = findCacheList(cacheKey);
            dto.setTicketList(ticketDtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyToc)) {
            List<FeeCashTocDto> tocDtoList = feeCashTocService.findAllCacheList(cacheKeyToc);
            dto.setTocList(tocDtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyDetail)) {
            List<FeeCashDetailDto> dtoList = feeCashDetailService.findCacheList(cacheKeyDetail);
            dto.setDetailList(dtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyPrepay)) {
            List<FeeCashPrepayDto> dtoList = feeCashPrepayService.findCacheList(cacheKeyPrepay);
            dto.setPrepayList(dtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyInvoice)) {
            List<FeeCashInvoiceDto> dtoList = feeCashInvoiceService.findCacheList(cacheKeyInvoice);
            dto.setInvoiceList(dtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyMaterial)) {
            List<FeeCashMaterialDto> dtoList = feeCashMaterialService.findCacheList(cacheKeyMaterial);
            dto.setMaterialList(dtoList);
        }
        if (!staging) {
            createValidate(dto, autoCash, cacheKey, commit);
        } else {

            // 暂存兑付金额校验
            List<FeeCashDetailDto> detailList = dto.getDetailList();
            if(CollectionUtil.isNotEmpty(detailList)){
                Optional<FeeCashDetailDto> op = detailList.stream().filter(item -> Objects.isNull(item.getThisCashAmount()) || item.getThisCashAmount().compareTo(BigDecimal.ZERO) < 0).findFirst();
                if(op.isPresent()){
                    throw new IllegalArgumentException("本次兑付金额不能为空");
                }
            }

            // 本次冲账金额校验
            List<FeeCashPrepayDto> prepayList = dto.getPrepayList();
            if(CollectionUtil.isNotEmpty(prepayList)) {
                Optional<FeeCashPrepayDto> op = prepayList.stream().filter(item -> Objects.isNull(item.getThisReversedAmount()) || item.getThisReversedAmount().compareTo(BigDecimal.ZERO) < 0).findFirst();
                if(op.isPresent()){
                    throw new IllegalArgumentException("本次冲抵金额不能为空");
                }
            }

            if (StringUtil.isEmpty(dto.getPositionCode())) {
                FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
                if (Objects.nonNull(loginUserDetails)) {
                    dto.setOrgCode(loginUserDetails.getOrgCode());
                    dto.setOrgName(loginUserDetails.getOrgName());
                    dto.setPositionCode(loginUserDetails.getPostCode());
                }
            }
        }

        //账扣预付、票扣兑付，保存数据时需要根据账扣明细按客户维度拆分
        List<FeeCashDto> dtoList = splitByCustomer(dto, staging);

        List<String> codes = store(dtoList, commit);

        //新增业务日志
        FeeCashLogEventDto logEventDto = new FeeCashLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(dto);
        SerializableBiConsumer<FeeCashLogEventListener, FeeCashLogEventDto> onCreate = FeeCashLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, FeeCashLogEventListener.class, onCreate);

        if (!commit) {
            if (StringUtils.isNotBlank(cacheKey)) {
                clearCache(cacheKey);
            }
            if (StringUtils.isNotBlank(cacheKeyToc)) {
                feeCashTocService.clearAllCache(cacheKeyToc);
            }
            if (StringUtils.isNotBlank(cacheKeyDetail)) {
                feeCashDetailService.clearCache(cacheKeyDetail);
            }
            if (StringUtils.isNotBlank(cacheKeyPrepay)) {
                feeCashPrepayService.clearCache(cacheKeyPrepay);
            }
            if (StringUtils.isNotBlank(cacheKeyInvoice)) {
                feeCashInvoiceService.clearCache(cacheKeyInvoice);
            }
            if (StringUtils.isNotBlank(cacheKeyMaterial)) {
                feeCashMaterialService.clearCache(cacheKeyMaterial);
            }
        }
        return codes;
    }

    /**
     * 修改新据
     *
     * @param dto      实体对象
     * @param cacheKey
     * @return 修改结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> update(FeeCashDto dto, String cacheKey, String cacheKeyToc,
                               String cacheKeyDetail, String cacheKeyPrepay, String cacheKeyInvoice, String cacheKeyMaterial, boolean commit, boolean staging,boolean saveFlag) {
        if (StringUtils.isNotBlank(cacheKey)) {
            List<FeeCashTicketDto> ticketDtoList = findCacheList(cacheKey);
            dto.setTicketList(ticketDtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyToc)) {
            List<FeeCashTocDto> tocDtoList = feeCashTocService.findAllCacheList(cacheKeyToc);
            dto.setTocList(tocDtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyDetail)) {
            List<FeeCashDetailDto> dtoList = feeCashDetailService.findCacheList(cacheKeyDetail);
            dto.setDetailList(dtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyPrepay)) {
            List<FeeCashPrepayDto> dtoList = feeCashPrepayService.findCacheList(cacheKeyPrepay);
            dto.setPrepayList(dtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyInvoice)) {
            List<FeeCashInvoiceDto> dtoList = feeCashInvoiceService.findCacheList(cacheKeyInvoice);
            dto.setInvoiceList(dtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyMaterial)) {
            List<FeeCashMaterialDto> dtoList = feeCashMaterialService.findCacheList(cacheKeyMaterial);
            dto.setMaterialList(dtoList);
        }
        if (!staging) {
            updateValidate(dto, cacheKey, commit,saveFlag);
        }
        FeeCash entityOld = feeCashRepository.findByCode(dto.getCashCode());
        Validate.notNull(entityOld, "未找到需要更新的数据");
        Assert.isTrue(!ProcessStatusEnum.PASS.getDictCode().equals(entityOld.getStatus())
                        && !ProcessStatusEnum.COMMIT.getDictCode().equals(entityOld.getStatus()),
                "审批中和审批通过的数据不允许编辑");
        //账扣预付、票扣兑付，保存数据时需要根据账扣明细按客户维度拆分
        List<FeeCashDto> dtoList = splitByCustomer(dto, staging);

        //如果被拆分，删除原数据
        if (dtoList.size() > 1) {
            feeCashTicketRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
            feeCashDetailRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
            feeCashRepository.removeById(dto.getId());
        }

        List<String> codes = store(dtoList, commit);

        //编辑业务日志
        FeeCashLogEventDto logEventDto = new FeeCashLogEventDto();
        logEventDto.setOriginal(this.nebulaToolkitService.copyObjectByWhiteList(entityOld, FeeCashDto.class, LinkedHashSet.class, ArrayList.class));
        logEventDto.setNewest(dto);
        SerializableBiConsumer<FeeCashLogEventListener, FeeCashLogEventDto> onUpdate =
                FeeCashLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, FeeCashLogEventListener.class, onUpdate);
        return codes;
    }

    /**
     * 按客户拆分明细
     *
     * @param dto
     * @return
     */
    private List<FeeCashDto> splitByCustomer(FeeCashDto dto, boolean staging) {
        List<FeeCashDto> dtoList = new ArrayList<>();
        if (staging) {
            return Collections.singletonList(dto);
        }
        if (CashTypeEnum.ACCOUNT.getDictCode().equals(dto.getCashType())
                || (CashTypeEnum.FEE.getDictCode().equals(dto.getCashType()) && CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(dto.getCashMethod()))) {
            if (!CollectionUtils.isEmpty(dto.getTicketList())) {
                Map<String, List<FeeCashDetailDto>> detailMap = dto.getDetailList().stream().collect(Collectors.groupingBy(FeeCashDetailDto::getCustomerCode));
                Map<String, List<FeeCashTicketDto>> ticketMap = dto.getTicketList().stream().collect(Collectors.groupingBy(FeeCashTicketDto::getCustomerCode));
                Map<String, CustomerVo> customerVoMap = customerVoService.findByCustomerCodes(new ArrayList<>(ticketMap.keySet())).stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity(), (a, b) -> a));
                if (ticketMap.size() == 1) {
                    dto.setCustomerCode(dto.getTicketList().get(0).getCustomerCode());
                    dto.setCustomerName(dto.getTicketList().get(0).getCustomerName());
                    dto.setDockingName(String.join(",", Optional.ofNullable(customerVoMap.get(dto.getTicketList().get(0).getCustomerCode()).getDockingList()).orElse(new ArrayList<>()).stream().map(CustomerDockingVo::getFullName).collect(Collectors.toSet())));
                    return Collections.singletonList(dto);
                }
                for (Map.Entry<String, List<FeeCashTicketDto>> entry : ticketMap.entrySet()) {
                    FeeCashDto dtoNew = nebulaToolkitService.copyObjectByWhiteList(dto, FeeCashDto.class, LinkedHashSet.class, ArrayList.class);
                    dtoNew.setId(null);
                    dtoNew.setDetailList(detailMap.getOrDefault(entry.getKey(), new ArrayList<>()));
                    dtoNew.setTicketList(entry.getValue());
                    dtoNew.setCustomerCode(dtoNew.getTicketList().get(0).getCustomerCode());
                    dtoNew.setCustomerName(dtoNew.getTicketList().get(0).getCustomerName());
                    dtoNew.setDockingName(String.join(",", Optional.ofNullable(customerVoMap.get(dtoNew.getTicketList().get(0).getCustomerCode()).getDockingList()).orElse(new ArrayList<>()).stream().map(CustomerDockingVo::getFullName).collect(Collectors.toSet())));
                    dtoList.add(dtoNew);
                }
            } else {
                dtoList = Collections.singletonList(dto);
            }
        } else {
            dtoList = Collections.singletonList(dto);
        }
        return dtoList;
    }

    /**
     * 修改银行卡信息保存
     *
     * @param dto      实体对象
     * @param cacheKey
     * @return 修改结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> updateBankInfo(FeeCashDto dto, String cacheKey, String cacheKeyToc) {
        if (StringUtils.isNotBlank(cacheKey)) {
            List<FeeCashTicketDto> ticketDtoList = findCacheList(cacheKey);
            dto.setTicketList(ticketDtoList);
        }
        if (StringUtils.isNotBlank(cacheKeyToc)) {
            List<FeeCashTocDto> tocDtoList = feeCashTocService.findAllCacheList(cacheKeyToc);
            dto.setTocList(tocDtoList);
        }
//        updateValidate(dto, cacheKey);
        FeeCash entityOld = feeCashRepository.findByCode(dto.getCashCode());
        Validate.notNull(entityOld, "未找到需要更新的数据");

        storeUpdateBankInfo(Arrays.asList(dto));

        //编辑业务日志
        FeeCashLogEventDto logEventDto = new FeeCashLogEventDto();
        logEventDto.setOriginal(this.nebulaToolkitService.copyObjectByWhiteList(entityOld, FeeCashDto.class, LinkedHashSet.class, ArrayList.class));
        logEventDto.setNewest(dto);
        SerializableBiConsumer<FeeCashLogEventListener, FeeCashLogEventDto> onUpdate =
                FeeCashLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, FeeCashLogEventListener.class, onUpdate);

        if (StringUtils.isNotBlank(cacheKey)) {
            clearCache(cacheKey);
        }
        if (StringUtils.isNotBlank(cacheKeyToc)) {
            feeCashTocService.clearAllCache(cacheKeyToc);
        }
        return Lists.newArrayList(entityOld.getCashCode());
    }

    /**
     * 保存
     *
     * @param dtoList
     */
    public List<String> storeUpdateBankInfo(List<FeeCashDto> dtoList) {
        // redis生成编码code
        List<String> codes = dtoList.stream().map(FeeCashDto::getCashCode).collect(Collectors.toList());
        Map<String, FeeCashPayeeDto> payeeDtoMap = Maps.newHashMap();
        dtoList.forEach(dto -> {
            if (CollectionUtil.isNotEmpty(dto.getPayeeList())) {
                dto.getPayeeList().forEach(e -> {
                    Assert.hasLength(e.getId(), "ID不能为空");
                    Assert.hasLength(e.getLineCode(), "兑付编码[" + dto.getCashCode()
                            + "]明细编码不能为空");
                    e.setCashCode(dto.getCashCode());
                    e.setCashName(dto.getCashName());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                    payeeDtoMap.put(e.getId(), e);
                });
            }
        });
        if (CollectionUtil.isNotEmpty(payeeDtoMap)) {
            List<FeeCashPayee> payeeList = feeCashPayeeRepository.findByIds(payeeDtoMap.keySet());
            Assert.notEmpty(payeeList, "编辑的明细不存在1!");
            Map<String, FeeCashPayee> payeeMap = payeeList.stream().collect(Collectors.toMap(FeeCashPayee::getId, v -> v, (n, o) -> n));
            Assert.isTrue(payeeMap.size() == payeeDtoMap.size(), "编辑的明细不存在2!");
            payeeDtoMap.forEach((id, dto) -> {
                Assert.isTrue(payeeMap.containsKey(id), "编辑的明细[" + id + "]不存在!");
                Assert.isTrue(dto.getLineCode().equals(payeeMap.get(id).getLineCode()), "兑付编码[" + dto.getCashCode()
                        + "]明细编码不能编辑!");
            });
            feeCashPayeeRepository.updateBatchById(nebulaToolkitService.copyCollectionByWhiteList(payeeDtoMap.values(), FeeCashPayeeDto.class, FeeCashPayee.class, LinkedHashSet.class, ArrayList.class));
        }
        return codes;
    }

    /**
     * 保存
     *
     * @param dtoList
     */
    public List<String> store(List<FeeCashDto> dtoList, boolean commit) {
        // redis生成编码code
        List<FeeCashDto> newDtoList = dtoList.stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());
        List<String> codes;
        if (!CollectionUtils.isEmpty(newDtoList)) {
            codes = generateCodeService.generateCode(FeeCashConstant.PREFIX_CODE, newDtoList.size());
        } else {
            codes = dtoList.stream().map(FeeCashDto::getCashCode).collect(Collectors.toList());
        }
        int sum = dtoList.stream().mapToInt(e -> e.getDetailList().size()).sum();
        List<String> detailCodes = generateCodeService.generateCode(FeeCashConstant.PREFIX_CODE_MX, sum);
        int sumPayee = dtoList.stream().filter(e -> !CollectionUtils.isEmpty(e.getPayeeList())).mapToInt(e -> e.getPayeeList().size()).sum();
        List<String> payeeCodes = new ArrayList<>();
        if (sumPayee > 0) {
            payeeCodes = generateCodeService.generateCode(FeeCashConstant.PREFIX_CODE_SKMX, sumPayee);
        }
        AtomicInteger index = new AtomicInteger(0);
        AtomicInteger indexDetail = new AtomicInteger(0);
        AtomicInteger indexPayee = new AtomicInteger(0);
        List<String> finalCodes = codes;
        List<String> finalPayeeCodes = payeeCodes;
        List<FeeCashInvoiceDto> invoiceDtos = new ArrayList<>();
        List<FeeCashFilesDto> filesDtos = new ArrayList<>();
        List<FeeCashPayeeDto> payeeDtos = new ArrayList<>();
        List<FeeCashDetailDto> detailDtos = new ArrayList<>();
        List<FeeCashTicketDto> ticketDtos = new ArrayList<>();
        List<FeeCashTocDto> tocDtos = new ArrayList<>();
        List<FeeCashPrepayDto> prepayDtos = new ArrayList<>();
        List<FeeCashMaterialDto> materialDtos = new ArrayList<>();
        List<String> sendCodeList = Lists.newArrayList();
        dtoList.forEach(dto -> {
            if (StringUtils.isBlank(dto.getId())) {
                dto.setCashCode(finalCodes.get(index.getAndIncrement()));
                dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                dto.setStatus(StringUtils.isBlank(dto.getStatus()) ? ProcessStatusEnum.PREPARE.getDictCode() : dto.getStatus());
                dto.setTenantCode(TenantUtils.getTenantCode());
            } else {
                List<FeeCashInvoiceVo> voList = feeCashInvoiceRepository.findByCode(dto.getCashCode());
                if (!CollectionUtils.isEmpty(voList)) {
                    // 释放状态
                    List<String> uniqueKeyList = voList.stream().map(v -> v.getInvoiceNo() + v.getInvoiceDetailNo()).distinct().collect(Collectors.toList());
                    this.invoiceDetailRepository.updateUsedByUniqueKeyList(uniqueKeyList);
                }
                feeCashInvoiceRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
                feeCashFilesRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
                feeCashPayeeRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
                feeCashTicketRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
                feeCashDetailRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
                feeCashTocRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
                feeCashPrepayRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
                feeCashMaterialRepository.deleteByCodes(Collections.singletonList(dto.getCashCode()));
            }
            AtomicReference<BigDecimal> totalApplyAmount = new AtomicReference<>(BigDecimal.ZERO);
            AtomicReference<BigDecimal> totalCashAmount = new AtomicReference<>(BigDecimal.ZERO);
            AtomicReference<BigDecimal> totalDemandAmount = new AtomicReference<>(BigDecimal.ZERO);
            AtomicReference<BigDecimal> totalDemandQuantity = new AtomicReference<>(BigDecimal.ZERO);
            if (!CollectionUtils.isEmpty(dto.getDetailList())) {
                dto.getDetailList().forEach(e -> {
                    e.setId(null);
                    e.setCashDetailCode(detailCodes.get(indexDetail.getAndIncrement()));
                    e.setCashCode(dto.getCashCode());
                    e.setCashName(dto.getCashName());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                    if (!CollectionUtils.isEmpty(dto.getPayeeList())) {
                        FeeCashPayeeDto feeCashPayeeDto = dto.getPayeeList().get(0);
                        e.setPayeeCode(feeCashPayeeDto.getPayeeCode());
                        e.setPayeeName(feeCashPayeeDto.getPayeeName());
                        e.setPayeeErpCode(feeCashPayeeDto.getPayeeErpCode());
                    }
                    if (CollectionUtils.isEmpty(dto.getTicketList())) {
                        totalApplyAmount.set(totalApplyAmount.get().add(Optional.ofNullable(e.getAuditAmount()).orElse(BigDecimal.ZERO)));
                    }
                    totalCashAmount.set(totalCashAmount.get().add(Optional.ofNullable(e.getThisCashAmount()).orElse(BigDecimal.ZERO)));
                });

                //票扣或者兑付类型为物料采购,实付金额=本次兑付金额
                //账扣兑付或电汇兑付：
                //1、若该兑付单中同一活动只对应一条兑付明细，实付金额=本次兑付金额-该行对应的预付明细的本次冲抵金额汇总
                //2、若该兑付单中同一活动对应多条兑付明细，实付金额=（本次兑付金额汇总-对应的预付明细的本次冲抵金额汇总）/兑付明细条数
                if (CashTypeEnum.FEE.getDictCode().equals(dto.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(dto.getCashType())) {
                    if (CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(dto.getCashMethod()) && CashTypeEnum.FEE.getDictCode().equals(dto.getCashType()) || CashMethodEnum.DEDUCTIONS.getDictCode().equals(dto.getCashMethod())) {
                        Map<String, List<FeeCashDetailDto>> schemeDetailCodeMap = dto.getDetailList().stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode()));
                        Map<String, BigDecimal> thisReversedAmountMap = dto.getPrepayList().stream().filter(Objects::nonNull)
                                .collect(Collectors.groupingBy(FeeCashPrepayDto::getSchemeDetailCode,
                                        Collectors.mapping(e -> Optional.ofNullable(e.getThisReversedAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                        schemeDetailCodeMap.forEach((k, v) -> {
                            BigDecimal thisReversedAmount = Optional.ofNullable(thisReversedAmountMap.get(k)).orElse(BigDecimal.ZERO);
                            BigDecimal thisCashAmount = v.stream().map(FeeCashDetailDto::getThisCashAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal actualPayAmountTotal = thisCashAmount.subtract(thisReversedAmount);
                            BigDecimal actualPayAmountSingle = actualPayAmountTotal.divide(new BigDecimal(v.size()), 2, BigDecimal.ROUND_HALF_UP);
                            for (int i = 0; i < v.size(); i++) {
                                if (i == v.size() - 1) {
                                    v.get(i).setActualPayAmount(actualPayAmountTotal);
                                } else {
                                    v.get(i).setActualPayAmount(actualPayAmountSingle);
                                    actualPayAmountTotal = actualPayAmountTotal.subtract(actualPayAmountSingle);
                                }
                            }
                        });
                    } else if (CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(dto.getCashMethod()) && CashTypeEnum.WIREDUIFU.getDictCode().equals(dto.getCashType())) {
                        AtomicReference<BigDecimal>[] totalCaAmount = new AtomicReference[]{new AtomicReference<>(totalCashAmount.get())};
                        //结案明细不根据方案明细编码分组汇总本次兑付金额分摊到预付明细的本次冲抵金额去，不能超过预付明细的预付可冲销金额
                        Map<String, BigDecimal> prepayDetailCodeAmountMap = new HashMap<>();
                        dto.getPrepayList().stream().sorted(Comparator.comparing(FeeCashPrepayDto::getPrepayDetailCode)).forEach(prepay -> {
                            if (totalCaAmount[0].get().compareTo(BigDecimal.ZERO) >= 0) {
                                BigDecimal availableReversedAmount = Optional.ofNullable(prepay.getAvailableReversedAmount()).orElse(BigDecimal.ZERO);
                                BigDecimal prepareCarryAmount = Optional.ofNullable(prepay.getPrepareCarryAmount()).orElse(BigDecimal.ZERO);
                                log.info("prepareCarryAmount :{}", prepareCarryAmount);
                                if (prepareCarryAmount.compareTo(BigDecimal.ZERO) > 0) {
                                    availableReversedAmount = availableReversedAmount.subtract(prepareCarryAmount);
                                    if (availableReversedAmount.compareTo(BigDecimal.ZERO) < 0) {
                                        availableReversedAmount = BigDecimal.ZERO;
                                    }
                                    prepayDetailCodeAmountMap.put(prepay.getPrepayDetailCode(), prepareCarryAmount);
                                }
                                BigDecimal thisReversedAmount = totalCaAmount[0].get().compareTo(availableReversedAmount) > 0 ? availableReversedAmount : totalCaAmount[0].get();
                                totalCaAmount[0].set(totalCaAmount[0].get().subtract(thisReversedAmount));
                                prepay.setThisReversedAmount(thisReversedAmount);
                            } else {
                                prepay.setThisReversedAmount(BigDecimal.ZERO);
                            }
                        });
                        if (totalCaAmount[0].get().compareTo(BigDecimal.ZERO) > 0 && prepayDetailCodeAmountMap.size() > 0) {
                            AtomicReference<BigDecimal> finalTotalCashAmount = new AtomicReference<>(totalCaAmount[0].get());
                            dto.getPrepayList().stream().sorted(Comparator.comparing(FeeCashPrepayDto::getPrepayDetailCode)).forEach(feeCashPrepayDto -> {
                                        BigDecimal bigDecimal = prepayDetailCodeAmountMap.get(feeCashPrepayDto.getPrepayDetailCode());
                                        if (null != bigDecimal && finalTotalCashAmount.get().compareTo(BigDecimal.ZERO) > 0) {
                                            BigDecimal thisReversedAmount = feeCashPrepayDto.getThisReversedAmount();
                                            BigDecimal availableReversedAmount = feeCashPrepayDto.getAvailableReversedAmount();
                                            BigDecimal maxAddAmount = availableReversedAmount.subtract(thisReversedAmount);
                                            BigDecimal addAmount = maxAddAmount.compareTo(finalTotalCashAmount.get()) > 0 ? finalTotalCashAmount.get() : maxAddAmount;
                                            if(addAmount.compareTo(bigDecimal) > 0) {
                                                addAmount = bigDecimal;
                                            }
                                            feeCashPrepayDto.setThisReversedAmount(feeCashPrepayDto.getThisReversedAmount().add(addAmount));
                                            finalTotalCashAmount.set(finalTotalCashAmount.get().subtract(addAmount));
                                        }
                                    }
                            );
                        }

                        BigDecimal actualPayAmountTotal = totalCaAmount[0].get();
                        BigDecimal actualPayAmountSingle = actualPayAmountTotal.divide(new BigDecimal(dto.getDetailList().size()), 2, BigDecimal.ROUND_HALF_UP);
                        for (int i = 0; i < dto.getDetailList().size(); i++) {
                            if (i == dto.getDetailList().size() - 1) {
                                dto.getDetailList().get(i).setActualPayAmount(actualPayAmountTotal);
                            } else {
                                dto.getDetailList().get(i).setActualPayAmount(actualPayAmountSingle);
                                actualPayAmountTotal = actualPayAmountTotal.subtract(actualPayAmountSingle);
                            }
                        }
                    } else {
                        dto.getDetailList().forEach(e -> e.setActualPayAmount(e.getThisCashAmount()));
                    }
                    //根据结案明细编码查询历史兑付+本次兑付+历史预付+本次 跟结案金额比较。
                    dto.getDetailList().forEach(e -> {
                        String auditDetailCode = e.getAuditDetailCode();
                        if (StringUtils.isNotBlank(auditDetailCode)) {
                            BigDecimal totalDuifuJeHistory = feeCashDetailRepository.getAllDuifuJeBAuditDetailCode(auditDetailCode);
                            Validate.isTrue(null != e.getThisCashAmount(), "兑付金额不能为空");
                            BigDecimal totalDuiFuJe = totalDuifuJeHistory.add(e.getThisCashAmount());
                            BigDecimal totalYuFuJeHistory = feeCashDetailRepository.getAllYuFuAvailableJe(auditDetailCode, dto.getCashCode());
                            BigDecimal totalAmout = totalDuiFuJe.add(totalYuFuJeHistory);
                            if (!CollectionUtils.isEmpty(dto.getPrepayList())) {
                                List<FeeCashPrepayDto> relatePrepay = dto.getPrepayList().stream().filter(o -> auditDetailCode.equals(o.getAuditDetailCode())).collect(Collectors.toList());
                                if (!CollectionUtils.isEmpty(relatePrepay)) {
                                    Optional<BigDecimal> collect = relatePrepay.stream().map(o -> o.getThisReversedAmount()).collect(Collectors.reducing(BigDecimal::add));
                                    if (collect.isPresent()) {
                                        totalAmout = totalAmout.subtract(collect.get());
                                    }
                                }
                            }
                            Validate.isTrue(totalAmout.compareTo(e.getAuditAmount()) <= 0, "结案明细:" + e.getAuditDetailCode() + "的实际支付金额超结案金额，不允许兑付!");
                        }

                    });
                } else if (CashTypeEnum.MATERIAL.getDictCode().equals(dto.getCashType()) ||
                        CashTypeEnum.WIRE.getDictCode().equals(dto.getCashType()) ||
                        CashTypeEnum.ACCOUNT.getDictCode().equals(dto.getCashType())
                ) {
                    dto.getDetailList().forEach(e -> e.setActualPayAmount(e.getThisCashAmount()));
                }

                //1、物料采购：未税金额同含税金额，税额=0；
                //2、发票明细上需要将费控返回的发票行税率存上
                //3、电汇/账扣兑付：根据发票明细行的税率计算，税率/100*本次兑付金额=本次兑付税额，本次未税兑付金额=含税-税额
                //4、票扣兑付：根据票扣明细上的产品税率计算每行票扣明细的未税金额与税额，汇总票扣明细的未税金额与税额，按兑付明细的兑付金额占比分摊未税金额与税额。
                if (CashTypeEnum.FEE.getDictCode().equals(dto.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(dto.getCashType())) {
                    if (CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(dto.getCashMethod()) || CashMethodEnum.DEDUCTIONS.getDictCode().equals(dto.getCashMethod())) {
                        Map<String, List<FeeCashInvoiceDto>> invoiceMap = new HashMap<>();
                        if (CollectionUtil.isNotEmpty(dto.getInvoiceList())) {
                            invoiceMap = dto.getInvoiceList().stream().filter(Objects::nonNull).collect(Collectors.groupingBy(e -> e.getAuditDetailCode()));
                        }
                        Map<String, List<FeeCashInvoiceDto>> finalInvoiceMap = invoiceMap;
                        dto.getDetailList().forEach(e -> {
                            // 根据票据类型处理税额计算
                            if (BillTypeEnum.RECEIPT.getDictCode().equals(e.getBillType())) {
                                // 票据类型为收据时，税额为0，未税金额等于兑付金额
                                e.setThisCashTaxAmount(BigDecimal.ZERO);
                                e.setThisCashNoTaxAmount(e.getThisCashAmount());
                            } else if (BillTypeEnum.INVOICE.getDictCode().equals(e.getBillType()) && finalInvoiceMap.containsKey(e.getAuditDetailCode())) {
                                // 票据类型为发票时，按发票税率计算
                                BigDecimal thisCashNotTaxAmount = BigDecimal.ZERO;
                                List<FeeCashInvoiceDto> cashInvoiceDtos = finalInvoiceMap.get(e.getAuditDetailCode());
                                for (FeeCashInvoiceDto invoiceDto : cashInvoiceDtos) {
                                    BigDecimal reimbursementAmount = invoiceDto.getReimbursementAmount();
                                    if (null == reimbursementAmount) {
                                        reimbursementAmount = BigDecimal.ZERO;
                                    }
                                    // 电汇、账扣兑付税额计算公式错误（现在是取发票上的税率*兑付金额得到税额，这个计算公式错误，应该是兑付金额/（1+税率）=未税金额，税额=兑付金额-未税金额）
//                                  thisCashTaxAmount = thisCashTaxAmount.add((e.getThisCashAmount().multiply(Optional.ofNullable(invoiceDto.getTaxRate()).orElse(BigDecimal.ZERO))).setScale(2, BigDecimal.ROUND_HALF_UP));
                                    thisCashNotTaxAmount = thisCashNotTaxAmount.add(reimbursementAmount.divide(Optional.ofNullable(invoiceDto.getTaxRate()).orElse(BigDecimal.ZERO).add(BigDecimal.ONE), 2, BigDecimal.ROUND_HALF_UP));
                                }
                                e.setThisCashTaxAmount(e.getThisCashAmount().subtract(thisCashNotTaxAmount));
                                e.setThisCashNoTaxAmount(thisCashNotTaxAmount);
                            } else {
                                // 其他情况（未设置票据类型或发票类型但无发票关联）
                                e.setThisCashTaxAmount(BigDecimal.ZERO);
                                e.setThisCashNoTaxAmount(e.getThisCashAmount());
                            }
                        });
                    } else if (CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(dto.getCashMethod())) {
                        //查询物料税率
                        Set<String> materialCodes = dto.getTicketList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductCode())).filter(Objects::nonNull)
                                .map(FeeCashTicketDto::getProductCode).collect(Collectors.toSet());
                        Set<String> companyCodeSet = dto.getTicketList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode())).filter(Objects::nonNull)
                                .map(FeeCashTicketDto::getCompanyCode).collect(Collectors.toSet());
                        MaterialSearchDto searchDto = new MaterialSearchDto();
                        searchDto.setMaterialCodeSet(materialCodes);
//                        searchDto.setCompanyCodeSet(companyCodeSet);
                        List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
                        Map<String, BigDecimal> materialTaxRateMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getTaxRate()).orElse(BigDecimal.ZERO), (a, b) -> a));

                        //票扣税额汇总
                        AtomicReference<BigDecimal> thisCashNoTaxAmount = new AtomicReference<>(BigDecimal.ZERO);
                        dto.getTicketList().forEach(e -> {
                            BigDecimal taxRate = materialTaxRateMap.getOrDefault(e.getProductCode(), BigDecimal.ZERO);
                            thisCashNoTaxAmount.set(thisCashNoTaxAmount.get().add((e.getAmount().divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_UP))));
                        });
                        BigDecimal cashAmountTotal = dto.getDetailList().stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal tail = thisCashNoTaxAmount.get();
                        for (int i = 0; i < dto.getDetailList().size(); i++) {
                            FeeCashDetailDto feeCashDetailDto = dto.getDetailList().get(i);
                            if (i == dto.getDetailList().size() - 1) {
                                feeCashDetailDto.setThisCashNoTaxAmount(tail);
                            } else {
                                BigDecimal thisCashTaxAmountSingle = BigDecimal.ZERO;
                                if (feeCashDetailDto.getThisCashAmount().compareTo(BigDecimal.ZERO) != 0) {
                                    thisCashTaxAmountSingle = thisCashNoTaxAmount.get().multiply(feeCashDetailDto.getThisCashAmount().divide(cashAmountTotal, 8, BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                                }
                                feeCashDetailDto.setThisCashNoTaxAmount(thisCashTaxAmountSingle);
                                tail = tail.subtract(thisCashTaxAmountSingle);
                            }
                            feeCashDetailDto.setThisCashTaxAmount(feeCashDetailDto.getThisCashAmount().subtract(feeCashDetailDto.getThisCashNoTaxAmount()));
                        }
                    }
                } else if (CashTypeEnum.MATERIAL.getDictCode().equals(dto.getCashType())) {
                    dto.getDetailList().forEach(e -> {
                        e.setThisCashTaxAmount(BigDecimal.ZERO);
                        e.setThisCashNoTaxAmount(e.getThisCashAmount());
                    });
                }

                detailDtos.addAll(dto.getDetailList());
            }
            if (!CollectionUtils.isEmpty(dto.getInvoiceList())) {
                Map<String, FeeCashDetailDto> detailMap = dto.getDetailList().stream().collect(Collectors.toMap(FeeCashDetailDto::getAuditDetailCode, Function.identity(), (a, b) -> a));
                dto.getInvoiceList().forEach(e -> {
                    FeeCashDetailDto detailDto = detailMap.get(e.getAuditDetailCode());
                    e.setId(null);
                    e.setCashCode(dto.getCashCode());
                    e.setCashName(dto.getCashName());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                    e.setCashDetailCode(detailDto.getCashDetailCode());
                    e.setDetailCode(detailDto.getDetailCode());
                    e.setDetailName(detailDto.getDetailName());
                });
                invoiceDtos.addAll(dto.getInvoiceList());
            }
            if (!CollectionUtils.isEmpty(dto.getTicketList())) {
                dto.getTicketList().forEach(e -> {
                    e.setId(null);
                    e.setQuantity(BigDecimal.ONE);
                    e.setPrice(e.getAmount());
                    e.setCashCode(dto.getCashCode());
                    e.setCashName(dto.getCashName());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                    totalApplyAmount.set(totalApplyAmount.get().add(Optional.ofNullable(e.getAmount()).orElse(BigDecimal.ZERO)));
                });
                ticketDtos.addAll(dto.getTicketList());
            }
            if (!CollectionUtils.isEmpty(dto.getPayeeList())) {
                dto.getPayeeList().forEach(e -> {
                    e.setId(null);
                    e.setLineCode(finalPayeeCodes.get(indexPayee.get()));
                    e.setCashCode(dto.getCashCode());
                    e.setCashName(dto.getCashName());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                    indexPayee.getAndAdd(1);
                });
                payeeDtos.addAll(dto.getPayeeList());
            }
            if (!CollectionUtils.isEmpty(dto.getTocList())) {
                dto.getTocList().forEach(e -> {
                    e.setId(null);
                    e.setCashCode(dto.getCashCode());
                    e.setCashName(dto.getCashName());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                });
                tocDtos.addAll(dto.getTocList());
            }
            if (!CollectionUtils.isEmpty(dto.getPrepayList())) {
                dto.getPrepayList().forEach(e -> {
                    e.setId(null);
                    e.setCashCode(dto.getCashCode());
                    e.setCashName(dto.getCashName());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                });
                prepayDtos.addAll(dto.getPrepayList());
            }
            if (!CollectionUtils.isEmpty(dto.getMaterialList())) {
                dto.getMaterialList().forEach(e -> {
                    e.setId(null);
                    e.setCashCode(dto.getCashCode());
                    e.setCashName(dto.getCashName());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                    totalDemandAmount.set(totalDemandAmount.get().add(e.getAmount()));
                    totalDemandQuantity.set(totalDemandQuantity.get().add(e.getQuantity()));
                });
                materialDtos.addAll(dto.getMaterialList());
            }
            if (!CollectionUtils.isEmpty(dto.getFilesList())) {
                dto.getFilesList().forEach(e -> {
                    e.setId(null);
                    e.setCashCode(dto.getCashCode());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                });
                filesDtos.addAll(dto.getFilesList());
            }
            dto.setTotalApplyAmount(totalApplyAmount.get());
            if (CashTypeEnum.CLOSE.getDictCode().equals(dto.getCashType())) {
                MarketingAuditDetailDto queryDto = new MarketingAuditDetailDto();
                queryDto.setAuditDetailCodeList(dto.getDetailList().stream().map(FeeCashDetailDto::getAuditDetailCode).collect(Collectors.toList()));
                Page<MarketingAuditDetailVo> auditDetailVoPage = marketingAuditService.findByConditions(PageRequest.of(1, Integer.MAX_VALUE), queryDto);
                BigDecimal closeAmount = auditDetailVoPage.getRecords().stream().map(e -> e.getAvailableCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                dto.setTotalCashAmount(closeAmount);
            } else {
                dto.setTotalCashAmount(totalCashAmount.get());
            }
            dto.setTotalDemandAmount(totalDemandAmount.get());
            dto.setTotalDemandQuantity(totalDemandQuantity.get());
        });
        feeCashRepository.saveOrUpdateBatch(nebulaToolkitService.copyCollectionByWhiteList(dtoList, FeeCashDto.class, FeeCash.class, LinkedHashSet.class, ArrayList.class));
        feeCashDetailRepository.saveBatch(nebulaToolkitService.copyCollectionByWhiteList(detailDtos, FeeCashDetailDto.class, FeeCashDetail.class, LinkedHashSet.class, ArrayList.class));
        if (!CollectionUtils.isEmpty(invoiceDtos)) {
            List<String> uniqueKeyList =
                    invoiceDtos.stream().map(v -> v.getInvoiceNo() + v.getInvoiceDetailNo()).distinct().collect(Collectors.toList());
            List<InvoiceDetail> invoiceDetails =
                    this.invoiceDetailRepository.findByUniqueKeyList(uniqueKeyList);
            List<String> used =
                    invoiceDetails.stream().filter(v -> BooleanEnum.TRUE.getCapital().equals(v.getUsed())).map(InvoiceDetail::getInvoiceDetailNo).collect(Collectors.toList());
            Validate.isTrue(CollectionUtils.isEmpty(used), "发票【%s】已被关联", String.join("、", used));
            invoiceDetails.forEach(v -> v.setUsed(BooleanEnum.TRUE.getCapital()));
            this.invoiceDetailRepository.updateBatchById(invoiceDetails);
            Set<String> invoiceNos = invoiceDtos.stream().map(FeeCashInvoiceDto::getInvoiceNo).collect(Collectors.toSet());
            invoiceService.clearCashCode(codes);
            invoiceService.bindCashCode(new ArrayList<>(invoiceNos), codes.get(0));
            feeCashInvoiceRepository.saveBatch(nebulaToolkitService.copyCollectionByWhiteList(invoiceDtos, FeeCashInvoiceDto.class, FeeCashInvoice.class, LinkedHashSet.class, ArrayList.class));
        }
        if (!CollectionUtils.isEmpty(filesDtos)) {
            feeCashFilesRepository.saveBatch(nebulaToolkitService.copyCollectionByWhiteList(filesDtos, FeeCashFilesDto.class, FeeCashFiles.class, LinkedHashSet.class, ArrayList.class));
        }

        if (!CollectionUtils.isEmpty(payeeDtos)) {
            feeCashPayeeRepository.saveBatch(nebulaToolkitService.copyCollectionByWhiteList(payeeDtos, FeeCashPayeeDto.class, FeeCashPayee.class, LinkedHashSet.class, ArrayList.class));
        }
        if (!CollectionUtils.isEmpty(ticketDtos)) {
            feeCashTicketRepository.saveBatch(nebulaToolkitService.copyCollectionByWhiteList(ticketDtos, FeeCashTicketDto.class, FeeCashTicket.class, LinkedHashSet.class, ArrayList.class));
        }
        if (!CollectionUtils.isEmpty(tocDtos)) {
            feeCashTocRepository.saveBatch(nebulaToolkitService.copyCollectionByWhiteList(tocDtos, FeeCashTocDto.class, FeeCashToc.class, LinkedHashSet.class, ArrayList.class));
        }
        if (!CollectionUtils.isEmpty(prepayDtos)) {
            feeCashPrepayRepository.saveBatch(nebulaToolkitService.copyCollectionByWhiteList(prepayDtos, FeeCashPrepayDto.class, FeeCashPrepay.class, LinkedHashSet.class, ArrayList.class));
        }
        if (!CollectionUtils.isEmpty(materialDtos)) {
            feeCashMaterialRepository.saveBatch(nebulaToolkitService.copyCollectionByWhiteList(materialDtos, FeeCashMaterialDto.class, FeeCashMaterial.class, LinkedHashSet.class, ArrayList.class));
        }

        if (!commit) {
            // 新增编辑发送钉钉消息，提交不发送
            this.feeCashSendOaMsgService.sendOADingTalkMsgByCodes(codes,
                    BeanUtil.copyProperties(loginUserService.getLoginDetails(FacturerUserDetails.class), FacturerUserDetails.class));
        }
        return codes;
    }

    /**
     * 创建校验
     *
     * @param dto
     */
    private void createValidate(FeeCashDto dto, boolean autoCash, String cacheKey, boolean commit) {
        Validate.notNull(dto, "新增时，对象信息不能为空！");
        dto.setId(null);
        commonValidate(dto, autoCash, cacheKey, commit,false);
        if (StringUtil.isEmpty(dto.getPositionCode())) {
            FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
            if (Objects.nonNull(loginUserDetails)) {
                dto.setOrgCode(loginUserDetails.getOrgCode());
                dto.setOrgName(loginUserDetails.getOrgName());
                dto.setPositionCode(loginUserDetails.getPostCode());
            }
        }
        Validate.notEmpty(dto.getPositionCode(), "新增时，职位信息不能为空！");
        dto.setPayStatus(HecPayStatusTypeEnum.NOT_PAY.getCode());
        //费用兑付且兑付方式=票扣/账扣或 账扣预付单据创建后“付款状态”为空  1044543
        if (CashTypeEnum.ACCOUNT.getDictCode().equals(dto.getCashType())
                || CashMethodEnum.DEDUCTIONS.getDictCode().equals(dto.getCashMethod())
                || CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(dto.getCashMethod())) {
            dto.setPayStatus("");
        }
    }

    /**
     * 修改验证
     *
     * @param dto
     */
    private void updateValidate(FeeCashDto dto, String cacheKey, boolean commit,boolean saveFlag) {
        Validate.notNull(dto, "修改时，对象信息不能为空！");
        Validate.notBlank(dto.getId(), "修改数据时，主键不能为空！");
        Validate.notBlank(dto.getCashCode(), "兑付编码，不能为空！");
        commonValidate(dto, false, cacheKey, commit,saveFlag);
    }

    /**
     * 公有验证
     *
     * @param dto
     */
    private void commonValidate(FeeCashDto dto, boolean autoCash, String cacheKey, boolean commit,boolean saveFlag) {
        //自动生成的兑付，无需校验
        if (autoCash) {
            return;
        }
        if (CashTypeEnum.WIREDUIFU.getDictCode().equals(dto.getCashType())) {
            Validate.isTrue(CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(dto.getCashMethod()), "不支持的操作类型");
        }
        Validate.notBlank(dto.getCashName(), "兑付名称，不能为空！");
        Validate.isTrue(dto.getCashName().length() <= 40, "兑付名称需控制在40字符内");
        Validate.notBlank(dto.getCashType(), "兑付类型，不能为空！");
        Validate.notBlank(dto.getOrgCode(), "费用承担部门，不能为空！");
        Validate.isTrue(CashTypeEnum.findByCode(dto.getCashType()) != null, "兑付类型，不正确！");
        if (StringUtils.isNotBlank(dto.getRemark())) {
            Validate.isTrue(dto.getRemark().length() <= 300, "备注需控制在300字符内");
        }
        if (CollectionUtil.isNotEmpty(dto.getInvoiceList())) {
            dto.getInvoiceList().forEach(e -> {
                Assert.notNull(e.getInvoiceNo(), "发票号码不能为空!");
                Assert.notNull(e.getReimbursementAmount(), "实际报销金额不能为空!");
                Assert.notNull(e.getPriceAndTax(), "发票价税合计金额不能为空!");
                Assert.isTrue(e.getPriceAndTax().compareTo(e.getReimbursementAmount()) >= 0,
                        "结案明细[" + e.getAuditDetailCode() + "]关联的发票[" + e.getInvoiceNo() + "]报销金额高于了发票金额，请修改报销金额或更换发票！!");

            });
            Map<String, List<FeeCashInvoiceDto>> invoceUsedMap = dto.getInvoiceList().stream().collect(Collectors.groupingBy(FeeCashInvoiceDto::getInvoiceNo));
            List<InvoiceVo> invoiceVos = invoiceService.findByInvoiceNoList(invoceUsedMap.keySet());
            Map<String, InvoiceVo> invoiceVoMap = invoiceVos.stream().filter(x -> ObjectUtils.isNotEmpty(x) && ObjectUtils.isNotEmpty(x.getInvoiceNo()))
                    .collect(Collectors.toMap(x -> x.getInvoiceNo(), Function.identity()));
            invoceUsedMap.forEach((k, list) -> {
                FeeCashInvoiceDto e = list.get(list.size() - 1);
                InvoiceVo invoiceVo = invoiceVoMap.get(k);
                BigDecimal reimbursementAmount = list.stream().map(FeeCashInvoiceDto::getReimbursementAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                cn.hutool.core.lang.Assert.isTrue(invoiceVo.getPriceAndTax().compareTo(reimbursementAmount) >= 0,
                        "结案明细[" + e.getAuditDetailCode() + "]关联的发票[" + e.getInvoiceNo() + "]报销金额高于了发票金额，请修改报销金额或更换发票！!");
                invoiceVo.setPriceAndTax(invoiceVo.getPriceAndTax().subtract(reimbursementAmount));
            });
        }
        //费用兑付
        if (CashTypeEnum.FEE.getDictCode().equals(dto.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(dto.getCashType())) {
            Validate.notBlank(dto.getCashMethod(), "兑付方式，不能为空！");
            Validate.isTrue(CashMethodEnum.findByCode(dto.getCashMethod()) != null, "兑付方式，不正确！");
            Map<String, List<FeeCashInvoiceDto>> invoiceGroup = new HashMap<>();

            if (CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(dto.getCashMethod())) {
                if (commit || "Y".equals(dto.getBeConfirm())) {
                    Validate.notEmpty(dto.getDetailList(), "兑付明细，不能为空！");
                    dto.getDetailList().forEach(o -> {
                        Validate.notBlank(o.getRemark(), "兑付描述不能为空");
                    });
                }
            }

            //电汇
            if (CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(dto.getCashMethod())) {
                // 根据票据类型校验发票
                validateInvoiceByBillType(dto);
                if (CollectionUtil.isNotEmpty(dto.getInvoiceList())) {
                    invoiceGroup = dto.getInvoiceList().stream().collect(Collectors.groupingBy(FeeCashInvoiceDto::getAuditDetailCode));
                }
                if (!CollectionUtils.isEmpty(dto.getPayeeList())) {
                    Set<String> payeeCodes = new HashSet<>();
                    dto.getPayeeList().forEach(e -> {
                        Validate.notBlank(e.getPayeeType(), "收款方类型，必填");
                        Validate.notBlank(e.getPayeeName(), "收款方名称，必填");
                        Validate.notBlank(e.getPayeeAccount(), "收款账号，必填");
                        Validate.notBlank(e.getAccountName(), "户名，必填");
                        Validate.notBlank(e.getInterbankNumber(), "银联行号，必填");
//                        Validate.notBlank(e.getBankName(), "开户行，必填");
                        Validate.notNull(e.getThisPayAmount(), "本次付款金额，必填");
                        Validate.notNull(e.getPayDate(), "期望付款日期，必填");
                        Validate.notBlank(e.getPayType(), "付款方式，必填");
                        payeeCodes.add(e.getPayeeCode());
                    });
                    Validate.isTrue(payeeCodes.size() == 1, "收款信息不能跨供应商");
                    //如果收款方是数据字典TOC_payment下的供应商，兑付明细或预付明细对应的预算科目必须是数据字典TOC_payment下的预算科目
                    List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(FeeCashConstant.TOC_PAYMENT);
                    Map<String, Map<String, DictDataVo>> dictMap = dictDataVos.stream().collect(Collectors.groupingBy(e -> e.getExt2(),
                            Collectors.toMap(e -> e.getExt1(), Function.identity(), (a, b) -> a)));
                    if (dictMap.containsKey(dto.getPayeeList().get(0).getSupplierErpCode())) {
                        Map<String, DictDataVo> dataVoMap = dictMap.get(dto.getPayeeList().get(0).getSupplierErpCode());
                        dto.getDetailList().forEach(e -> Validate.isTrue(dataVoMap.containsKey(e.getBudgetSubjectCode()), "收款方是三方（天津晨景），科目非销售奖励-推广，陈列发布费的，不允许发起兑付流程！"));
                    }
                }
                //票扣
            } else if (CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(dto.getCashMethod())) {
                Validate.notEmpty(dto.getTicketList(), "票扣明细不能为空");
                List<String> subjectList = new ArrayList<>();
                // 判断dto.getTicketList()中的年月字段如果填了一个剩余其他行年月都需要填
                Optional<String> any = dto.getTicketList().stream().map(FeeCashTicketDto::getYears).filter(StringUtils::isNotBlank).findAny();
                if (any.isPresent()) {
                    dto.getTicketList().forEach(e -> {
                        Validate.notBlank(e.getYears(), "年月，不能为空");
                    });
                }
                dto.getTicketList().forEach(e -> {
                    Validate.notNull(e.getAmount(), "票扣金额，不能为空！");
                    Validate.notBlank(e.getCustomerCode(), "客户，不能为空");
                    Validate.notBlank(e.getProductCode(), "产品，不能为空");
                    if (StringUtils.isNotBlank(e.getBudgetSubjectsCode())) {
                        subjectList.add(e.getBudgetSubjectsCode());
                    }
                });
                Validate.isTrue(subjectList.size() == dto.getTicketList().size() || subjectList.size() == 0, "票扣明细的“预算科目”层级不一致，请调整票扣明细的预算科目！");
                if (!CollectionUtils.isEmpty(subjectList)) {
                    Set<String> levelSet = budgetSubjectsVoService.findByCodes(new HashSet<>(subjectList)).stream().map(e -> e.getLevel()).collect(Collectors.toSet());
                    Validate.isTrue(levelSet.size() == 1, "票扣明细的“预算科目”层级不一致，请调整票扣明细的预算科目！");
                }
//                ticketValidate(dto.getTicketList());
//                dto.getTicketList().forEach(e -> {
//                    if (!e.getCheckFlag()) {
//                        if (StringUtils.isNotBlank(cacheKey)) {
//                            saveCurrentPageCache(cacheKey, dto.getTicketList());
//                        }
//                        Validate.isTrue(false, "票扣折让率校验未通过");
//                    }
//                });
            } else {
                // 根据票据类型校验发票
                validateInvoiceByBillType(dto);

                Set<String> customerCodes = dto.getDetailList().stream().map(FeeCashDetailDto::getCustomerCode).collect(Collectors.toSet());
                Validate.isTrue(customerCodes.size() == 1, "关联的结案明细不允许跨客户");
                if (CollectionUtil.isNotEmpty(dto.getInvoiceList())) {
                    invoiceGroup = dto.getInvoiceList().stream().collect(Collectors.groupingBy(FeeCashInvoiceDto::getAuditDetailCode));
                }
                if (!CollectionUtils.isEmpty(dto.getPrepayList())) {
                    Map<String, BigDecimal> thisCashAmountTotal = dto.getDetailList().stream().filter(k -> Objects.nonNull(k.getThisCashAmount()))
                            .collect(Collectors.groupingBy(FeeCashDetailDto::getSchemeDetailCode, Collectors.mapping(e -> Optional.ofNullable(e.getThisCashAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                    Map<String, BigDecimal> thisReversedAmountTotal = dto.getPrepayList().stream().filter(k -> Objects.nonNull(k.getThisReversedAmount()))
                            .collect(Collectors.groupingBy(FeeCashPrepayDto::getSchemeDetailCode, Collectors.mapping(e -> Optional.ofNullable(e.getThisReversedAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                    dto.getPrepayList().forEach(e -> {
                        BigDecimal thisReversedAmount = Optional.ofNullable(e.getThisReversedAmount()).orElse(BigDecimal.ZERO);
                        BigDecimal reversedAmount = Optional.ofNullable(e.getReversedAmount()).orElse(BigDecimal.ZERO);
                        BigDecimal prepayAmount = Optional.ofNullable(e.getPrepayAmount()).orElse(BigDecimal.ZERO);
             /*           if(saveFlag){
                            Validate.isTrue(thisReversedAmount.compareTo(prepayAmount)>0, "本次冲抵金额不能超过该行的预付金额");
                        }else {

                        }*/
                        Validate.isTrue((prepayAmount.subtract(reversedAmount)).compareTo(thisReversedAmount) >= 0, "本次冲抵金额不能超过该行的“预付金额-已冲抵金额”");
                    });
                    thisCashAmountTotal.forEach((k, v) -> {
                        BigDecimal thisCashAmount = Optional.ofNullable(v).orElse(BigDecimal.ZERO);
                        BigDecimal prepayAmount = Optional.ofNullable(thisReversedAmountTotal.get(k)).orElse(BigDecimal.ZERO);
                        Validate.isTrue(thisCashAmount.compareTo(prepayAmount) == 0, "活动【%s】的本次冲抵金额之和应等于本次兑付金额之和", k);
                    });
                }
                dto.setCustomerCode(dto.getDetailList().get(0).getCustomerCode());
                dto.setCustomerName(dto.getDetailList().get(0).getCustomerName());
/*                List<FeeCashPrepayDto> del = dto.getPrepayList().stream().filter(item -> (item.getThisReversedAmount()
                        .compareTo(item.getPrepayAmount()) == 0 || item.getPrepayAmount().subtract(item.getReversedAmount())
                        .compareTo(item.getThisReversedAmount()) ==0)).collect(Collectors.toList());
                List<String> delSchemaCodes = null;
                if(!CollectionUtils.isEmpty(del)){
                     delSchemaCodes = del.stream().map(item -> item.getSchemeDetailCode()).distinct().collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(delSchemaCodes)){
                    ActivityPrepayDetailRecordDto delReqDto =  new ActivityPrepayDetailRecordDto();
                    delReqDto.setSchemeDetailCodeList(delSchemaCodes);
                    Page<ActivityPrepayDetailRecordVo> willDel = activityPrepayDetailRecordMapper.findByPayeeCode2(new Page<>(1, 10000), delReqDto);
                    if(CollectionUtils.isEmpty(willDel.getRecords())){
                        List<String> id2CashCodeList = willDel.getRecords().stream().filter(item -> StringUtils.isNotBlank(item.getIdCashCode())).map(ActivityPrepayDetailRecordVo::getIdCashCode).collect(Collectors.toList());
                        String bef = JSONObject.toJSONString(id2CashCodeList);
                        String cashCode = dto.getCashCode();
                        List<String> exclCashCode = id2CashCodeList.stream().filter(item -> item.contains(":")).filter(item -> cashCode.equals(item.split(":")[1])).collect(Collectors.toList());
                        boolean b = id2CashCodeList.removeAll(exclCashCode);
                        log.error("费用兑付===== 删除是否成功 {} \r\n 删除前 {} \r\n 删除后 {}",b, bef,JSONObject.toJSONString(id2CashCodeList));
                        // 删除
                        if(!CollectionUtils.isEmpty(id2CashCodeList)){
                            List<String> oldFeeCashIds = id2CashCodeList.stream().filter(StringUtils::isNoneBlank).filter(item -> item.contains(":")).map(item -> item.split(":")[0]).distinct().collect(Collectors.toList());
                            if(!CollectionUtils.isEmpty(oldFeeCashIds)){
                                log.info("费用兑付=== 删除feeCashId {}",JSONObject.toJSONString(oldFeeCashIds));
                                feeCashSendHecService.delete(oldFeeCashIds);
                            }
                        }
                    }
                }*/

            }
            AtomicReference<BigDecimal> thisCashAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
            if (CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(dto.getCashMethod()) || CashMethodEnum.DEDUCTIONS.getDictCode().equals(dto.getCashMethod())) {
                if (commit || "Y".equals(dto.getBeConfirm())) {
                    Validate.notEmpty(dto.getDetailList(), "兑付明细，不能为空！");
                }
                dto.getDetailList().forEach(e -> {
                    Validate.notNull(e.getThisCashAmount(), "本次兑付金额，不能为空！");
                    Validate.notBlank(e.getBeCash(), "是否完全兑付，不能为空！");
                    Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(e.getBeCash()), "是否完全兑付，必须为否！");
                });
                Map<String, List<FeeCashInvoiceDto>> finalInvoiceGroup = invoiceGroup;
                dto.getDetailList().forEach(v -> {
                    // 本次兑付金额不允许超结案金额
                    Validate.notNull(v.getAuditAmount(), "结案明细编码【%s】结案金额不能为空", v.getAuditDetailCode());
                    // 本次兑付金额不允许超结案金额
                    Validate.notNull(v.getThisCashAmount(), "结案明细编码【%s】本次兑付金额不能为空", v.getAuditDetailCode());
                    // 本次兑付金额不允许超结案金额
                    Validate.isTrue(v.getAuditAmount().compareTo(v.getThisCashAmount()) >= 0, "结案明细编码【%s】本次兑付金额不允许超结案金额",
                            v.getAuditDetailCode());
                    // 根据票据类型校验发票
                    validateDetailInvoiceByBillType(v, finalInvoiceGroup);

                    // 只有票据类型为发票时才校验发票金额
                    if (BillTypeEnum.INVOICE.getDictCode().equals(v.getBillType())) {
                        List<FeeCashInvoiceDto> invoiceList = finalInvoiceGroup.get(v.getAuditDetailCode());
                        if (invoiceList != null) {
                            BigDecimal reimbursementAmountTotal = invoiceList.stream().map(FeeCashInvoiceDto::getReimbursementAmount)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO,
                                            BigDecimal::add);
                            Validate.isTrue(reimbursementAmountTotal.compareTo(v.getThisCashAmount()) == 0, "结案明细编码【%s" +
                                    "】发票的实际报销金额之和必须等于本次兑付金额", v.getAuditDetailCode());
                        }
                    }
                    thisCashAmountTotal.getAndAccumulate(v.getThisCashAmount(), BigDecimal::add);
                });
            }

            if (CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(dto.getCashMethod())) {
                // 电汇校验收款明细
                Validate.notEmpty(dto.getPayeeList(), "收款明细不能为空");
                BigDecimal thisPayAmountTotal =
                        dto.getPayeeList().stream().map(FeeCashPayeeDto::getThisPayAmount)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO,
                                        BigDecimal::add);
                Validate.isTrue(thisPayAmountTotal.compareTo(thisCashAmountTotal.get()) == 0,
                        "收款信息中的兑付金额之和必须等于兑付明细中兑付金额之和");
            }

            //电汇预付
        } else if (CashTypeEnum.WIRE.getDictCode().equals(dto.getCashType())) {
            Validate.isTrue(!CollectionUtils.isEmpty(dto.getDetailList()), "预付明细不能为空");
            //结案明细编码
            List<String> auditDetailCodes = dto.getDetailList().stream().map(x -> x.getAuditDetailCode()).distinct().collect(Collectors.toList());
            //查询费用兑付的结案金额+加上了还未通过的预付 不管是任何状态 除了删除的
            List<FeeCashDetailVo> cashDetailList = feeCashRepository.findFeeCashAuditDetailListByAuditDetailCodes(auditDetailCodes, dto.getCashCode());
            Map<String, BigDecimal> cashDetailMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(cashDetailList)) {
                cashDetailMap = cashDetailList.stream().collect(Collectors.toMap(x -> x.getAuditDetailCode(), l -> l.getThisCashAmount()));
            }
            //查询预付金额
            List<ActivityPrepayDetailRecord> prepayDetails = activityPrepayDetailRecordRepository.findByAuditDetailCodes(auditDetailCodes);
            Map<String, BigDecimal> prepayDetailMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(prepayDetails)) {
                prepayDetailMap = prepayDetails.stream().collect(Collectors.groupingBy(x -> x.getAuditDetailCode(), Collectors.mapping(x -> x.getAvailableReversedAmount(),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            }
//            //查询预付金额 排除本次的预付金额
//            List<FeeCashDetailVo> prepayAuditDetailList = feeCashRepository.findPrepayListByAuditDetailCodes(auditDetailCodes, dto.getCashCode());
//            Map<String, BigDecimal> prepayAuditDetailMap = Maps.newHashMap();
//            if (!CollectionUtils.isEmpty(prepayAuditDetailList)) {
//                prepayAuditDetailMap = prepayAuditDetailList.stream().collect(Collectors.toMap(x -> x.getAuditDetailCode(), l -> l.getThisCashAmount()));
//            }
            for (FeeCashDetailDto e : dto.getDetailList()) {
                Validate.notNull(e.getThisCashAmount(), "本次预付金额，不能为空！");
                BigDecimal thisCashAmount = e.getThisCashAmount();
                BigDecimal cashAmount = cashDetailMap.getOrDefault(e.getAuditDetailCode(), BigDecimal.ZERO);
                BigDecimal availableReversedAmount = prepayDetailMap.getOrDefault(e.getAuditDetailCode(), BigDecimal.ZERO);
                BigDecimal totalAmount = thisCashAmount.add(cashAmount).add(availableReversedAmount);
                log.info("jine {} {} {} {} {}", thisCashAmount, cashAmount, availableReversedAmount, totalAmount, e.getAuditAmount());
                Validate.isTrue(e.getAuditAmount().compareTo(totalAmount) > -1, "本次预付金额应小于或等于（结案金额-已兑付金额-预付可冲销金额）");
            }

            if (!CollectionUtils.isEmpty(dto.getPayeeList())) {
                String showToc = feeCashDetailService.showToc(dto.getDetailList(), dto.getPayeeList().get(0).getPayeeErpCode());
                BigDecimal thisPayAmountTotal = dto.getPayeeList().stream().map(e -> Optional.ofNullable(e.getThisPayAmount()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal thisCashAmountTotal = dto.getDetailList().stream().map(e -> Optional.ofNullable(e.getThisCashAmount()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Validate.isTrue(thisPayAmountTotal.compareTo(thisCashAmountTotal) == 0,
                        "收款信息中的本次应付金额之和必须等于预付明细中本次预付金额之和");
                //如果收款方是数据字典TOC_payment下的供应商，兑付明细或预付明细对应的预算科目必须是数据字典TOC_payment下的预算科目
                List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(FeeCashConstant.TOC_PAYMENT);
                Map<String, Map<String, DictDataVo>> dictMap = dictDataVos.stream().collect(Collectors.groupingBy(e -> e.getExt2(),
                        Collectors.toMap(e -> e.getExt1(), Function.identity(), (a, b) -> a)));
                if (dictMap.containsKey(dto.getPayeeList().get(0).getPayeeErpCode())) {
                    Map<String, DictDataVo> dataVoMap = dictMap.get(dto.getPayeeList().get(0).getPayeeErpCode());
                    dto.getDetailList().forEach(e -> Validate.isTrue(dataVoMap.containsKey(e.getBudgetSubjectCode()), "收款方是三方（天津晨景），科目非销售奖励-推广，陈列发布费的，不允许发起兑付流程！"));
                }
                //TOC校验
                if (BooleanEnum.TRUE.getCapital().equals(showToc)) {
                    Validate.notEmpty(dto.getTocList(), "TOC支付不能为空！");
                    dto.getTocList().forEach(e -> {
                        Validate.notBlank(e.getPlatform(), "打款平台，不能为空！");
                        Validate.notBlank(e.getPayeeName(), "姓名，不能为空！");
                        Validate.notBlank(e.getBankNo(), "银行账号，不能为空！");
                        Validate.notBlank(e.getIdCard(), "身份证号码，不能为空！");
                        Validate.notBlank(e.getPhone(), "手机号，不能为空！");
                        Validate.notNull(e.getPayeeAmount(), "达人收款金额，不能为空！");
                        Validate.notNull(e.getPayAmount(), "实际打款金额，不能为空！");
                    });
                    BigDecimal payAmount = dto.getTocList().stream()
                            .map(e -> Optional.ofNullable(e.getPayAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal thisCashAmount = dto.getDetailList().stream()
                            .map(e -> Optional.ofNullable(e.getThisCashAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                    Validate.isTrue(payAmount.compareTo(thisCashAmount) == 0, "预付明细的“本次预付金额”汇总与TOC支付的“实际打款金额”汇总必须相等");
                }
            }
            //账扣预付
        } else if (CashTypeEnum.ACCOUNT.getDictCode().equals(dto.getCashType())) {
            Validate.isTrue(!CollectionUtils.isEmpty(dto.getDetailList()) || "N".equals(dto.getBeConfirm()) || (!commit && !"Y".equals(dto.getBeConfirm())), "预付明细不能为空");
            //结案明细编码
            List<String> auditDetailCodes = dto.getDetailList().stream().map(x -> x.getAuditDetailCode()).distinct().collect(Collectors.toList());
            //查询费用兑付的结案金额+加上了还未通过的预付 不管是任何状态 除了删除的
            Map<String, BigDecimal> cashDetailMap = Maps.newHashMap();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(auditDetailCodes)) {
                List<FeeCashDetailVo> cashDetailList = feeCashRepository.findFeeCashAuditDetailListByAuditDetailCodes(auditDetailCodes, dto.getCashCode());
                if (!CollectionUtils.isEmpty(cashDetailList)) {
                    cashDetailMap = cashDetailList.stream().collect(Collectors.toMap(x -> x.getAuditDetailCode(), l -> l.getThisCashAmount()));
                }
            }
            //查询预付金额
            Map<String, BigDecimal> prepayDetailMap = Maps.newHashMap();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(auditDetailCodes)) {
                List<ActivityPrepayDetailRecord> prepayDetails = activityPrepayDetailRecordRepository.findByAuditDetailCodes(auditDetailCodes);
                if (!CollectionUtils.isEmpty(prepayDetails)) {
                    prepayDetailMap = prepayDetails.stream().collect(Collectors.groupingBy(x -> x.getAuditDetailCode(), Collectors.mapping(x -> x.getAvailableReversedAmount(),
                            Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                }
            }
            Map<String, BigDecimal> prepayAuditDetailMap = Maps.newHashMap();
//            //查询预付金额 排除本次的预付金额
//            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(auditDetailCodes)) {
//                List<FeeCashDetailVo> prepayAuditDetailList = feeCashRepository.findPrepayListByAuditDetailCodes(auditDetailCodes, dto.getCashCode());
//                if (!CollectionUtils.isEmpty(prepayAuditDetailList)) {
//                    prepayAuditDetailMap = prepayAuditDetailList.stream().collect(Collectors.toMap(x -> x.getAuditDetailCode(), l -> l.getThisCashAmount()));
//                }
//            }

            for (FeeCashDetailDto e : dto.getDetailList()) {
                Validate.notNull(e.getThisCashAmount(), "本次预付金额，不能为空！");
                Validate.notBlank(e.getCustomerCode(), "客户，不能为空！");
                Validate.notBlank(e.getYears(), "年月，不能为空");

                BigDecimal thisCashAmount = e.getThisCashAmount();
                BigDecimal cashAmount = cashDetailMap.getOrDefault(e.getAuditDetailCode(), BigDecimal.ZERO);
                BigDecimal availableReversedAmount = prepayDetailMap.getOrDefault(e.getAuditDetailCode(), BigDecimal.ZERO);
                BigDecimal totalAmount = thisCashAmount.add(cashAmount).add(availableReversedAmount);

                Validate.isTrue(e.getAuditAmount().compareTo(totalAmount) > -1, "本次预付金额应小于或等于（结案金额-已兑付金额-预付可冲销金额）");
            }

            if (!CollectionUtils.isEmpty(dto.getTicketList())) {
                List<String> subjectList = new ArrayList<>();
                dto.getTicketList().forEach(e -> {
                    if (StringUtils.isNotBlank(e.getBudgetSubjectsCode())) {
                        subjectList.add(e.getBudgetSubjectsCode());
                    }
                });
                Validate.isTrue(subjectList.size() == dto.getTicketList().size() || subjectList.size() == 0, "账扣明细的“预算科目”层级不一致，请调整票扣明细的预算科目！");
                if (!CollectionUtils.isEmpty(subjectList)) {
                    Set<String> levelSet = budgetSubjectsVoService.findByCodes(new HashSet<>(subjectList)).stream().map(e -> e.getLevel()).collect(Collectors.toSet());
                    Validate.isTrue(levelSet.size() == 1, "账扣明细的“预算科目”层级不一致，请调整票扣明细的预算科目！");
                }
            }
            //物料采购
        } else if (CashTypeEnum.MATERIAL.getDictCode().equals(dto.getCashType())) {
            Validate.notEmpty(dto.getDetailList(), "兑付明细，不能为空！");
            Validate.notEmpty(dto.getMaterialList(), "采购需求，不能为空！");

            Set<String> customerCodes = dto.getDetailList().stream().map(FeeCashDetailDto::getCustomerCode).collect(Collectors.toSet());
            Validate.isTrue(customerCodes.size() == 1, "关联的结案明细不允许跨客户");

            dto.getMaterialList().forEach(e -> {
                Validate.notBlank(e.getMaterialName(), "物料名称，不能为空!");
//                Validate.notBlank(e.getUrl(), "商品参考链接，不能为空!");
                Validate.notNull(e.getQuantity(), "数量，不能为空!");
                Validate.notNull(e.getPrice(), "单价，不能为空!");
                Validate.notBlank(e.getRequiredDeliveryTime(), "需求到货时间，不能为空!");
                Validate.notBlank(e.getContactPerson(), "联系人，不能为空!");
                Validate.notBlank(e.getContactPhone(), "联系电话，不能为空!");
                Validate.notBlank(e.getAddress(), "邮寄地址，不能为空!");

                e.setAmount((e.getPrice().multiply(e.getQuantity())).setScale(2, BigDecimal.ROUND_HALF_DOWN));
            });
            dto.setCustomerCode(dto.getDetailList().get(0).getCustomerCode());
            dto.setCustomerName(dto.getDetailList().get(0).getCustomerName());

            dto.getDetailList().forEach(e -> {
                Validate.notNull(e.getThisCashAmount(), "本次兑付金额，不能为空！");
                Validate.notBlank(e.getBeCash(), "是否完全兑付，不能为空！");
                Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(e.getBeCash()), "是否完全兑付，必须为否！");
                // 本次兑付金额不允许超结案金额
                Validate.notNull(e.getAuditAmount(), "结案明细编码【%s】结案金额不能为空", e.getAuditDetailCode());
                // 本次兑付金额不允许超结案金额
                Validate.notNull(e.getThisCashAmount(), "结案明细编码【%s】本次兑付金额不能为空", e.getAuditDetailCode());
                // 本次兑付金额不允许超结案金额
                Validate.isTrue(e.getAuditAmount().compareTo(e.getThisCashAmount()) >= 0, "结案明细编码【%s】本次兑付金额不允许超结案金额",
                        e.getAuditDetailCode());
            });
            BigDecimal amount = dto.getMaterialList().stream().map(e -> e.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal thisCashAmount = dto.getDetailList().stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            Validate.isTrue(amount.compareTo(thisCashAmount) == 0, "物料金额之和必须等于本次采购金额之和");

            //关闭兑付
        } else {
            Validate.notEmpty(dto.getDetailList(), "兑付明细，不能为空！");
            dto.getDetailList().forEach(e -> {
                Validate.notNull(e.getThisCashAmount(), "本次兑付金额，不能为空！");
                Validate.notBlank(e.getBeCash(), "是否完全兑付，不能为空！");
                Validate.isTrue(BooleanEnum.TRUE.getCapital().contains(e.getBeCash()), "是否完全兑付，必须为是！");
                Validate.isTrue(e.getThisCashAmount().compareTo(BigDecimal.ZERO) == 0, "本次兑付金额必须为0");
                BigDecimal available_reversed_amount = activityPrepayDetailRecordService.getAvailable_reversed_amount(e.getSchemeDetailCode());
                Validate.isTrue(available_reversed_amount == null || available_reversed_amount.compareTo(BigDecimal.ZERO) == 0, "活动明细：" + e.getSchemeDetailCode() + "预付未完全核销，不允许关闭！");
            });
            ActivityPrepayDetailRecordDto activityPrepayDetailRecordDto = new ActivityPrepayDetailRecordDto();
            Set<String> schemeDetailCodes = dto.getDetailList().stream().map(FeeCashDetailDto::getSchemeDetailCode).collect(Collectors.toSet());
            activityPrepayDetailRecordDto.setSchemeDetailCodeList(new ArrayList<>(schemeDetailCodes));
            Page<ActivityPrepayDetailRecordVo> prepayDetailRecordVoPage = activityPrepayDetailRecordService.findByPayeeCode(PageRequest.of(1, Integer.MAX_VALUE), activityPrepayDetailRecordDto);
            Validate.isTrue(prepayDetailRecordVoPage.getTotal() == 0, "活动【%s】历史预付申请未来票核销完全，不允许关闭费用！",
                    String.join(",", schemeDetailCodes));
        }

        // 1044889 兑付类型=费用兑付 且 兑付方式=电汇时，当收款方类型选择“员工”时，需要校验兑付明细下的活动细类是否是数据字典individual_reimbursement_type下的
        if ((CashTypeEnum.FEE.getDictCode().equals(dto.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(dto.getCashType()))
                && CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(dto.getCashMethod())) {
            List<FeeCashPayeeDto> payeeList = dto.getPayeeList().stream()
                    .filter(o -> PaeeTypeEnum.PERSON.getCode().equals(o.getPayeeType()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(payeeList)) {
                List<String> dictDataVoList = dictDataVoService.findByDictTypeCode(INDIVIDUAL_REIMBURSEMENT_TYPE).stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
                List<String> detailCodeList = dto.getDetailList().stream().map(FeeCashDetailDto::getDetailCode).collect(Collectors.toList());
                // 当前兑付明细的活动细类编码在字典中全存在
                Validate.isTrue(dictDataVoList.containsAll(detailCodeList), "收款方类型与当前兑付明细活动细类不匹配");
            }
        }

        if (!CollectionUtils.isEmpty(dto.getDetailList())) {
            //取消费用兑付、账扣预付、电汇预付不允许针对同一结案明细同时发起兑付申请的控制，并改为在走费用关闭流程和已关闭的结案明细不允许发起费用兑付、账扣预付、电汇预付
            List<String> auditDetailCodes = dto.getDetailList().stream().map(FeeCashDetailDto::getAuditDetailCode).collect(Collectors.toList());
            List<FeeCashDetail> cashDetails = dto.getCashType().equals(CashTypeEnum.CLOSE.getDictCode()) ? feeCashDetailRepository.findByAuditDetailCodesBeCommit(auditDetailCodes) : feeCashDetailRepository.findByAuditDetailCodesBeClose(auditDetailCodes);
            if (CollectionUtil.isNotEmpty(cashDetails)) {
                //排除自己
                List<FeeCashDetail> filterCashDetails = cashDetails.stream().filter(e -> StringUtils.isBlank(dto.getCashCode()) || !dto.getCashCode().equals(e.getCashCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(filterCashDetails)) {
                    Set<String> cashCodes = filterCashDetails.stream().map(e -> e.getCashCode()).collect(Collectors.toSet());
                    Set<String> filterAuditDetailCodes = filterCashDetails.stream().map(e -> e.getAuditDetailCode()).collect(Collectors.toSet());
                    Validate.isTrue(false, "结案明细【%s】已在兑付单【%s】中存在，不允许保存", String.join(",", filterAuditDetailCodes), String.join(",", cashCodes));
                }
            }

//            if (CashTypeEnum.FEE.getDictCode().equals(dto.getCashType())) {
//                //若本次兑付金额=剩余可兑付金额，则是否完全兑付必须等于【是】
//                dto.getDetailList().forEach(e -> {
//                    Validate.notNull(e.getThisCashAmount(), "本次兑付金额，不能为空！");
//                    Validate.notBlank(e.getBeCash(), "是否完全兑付，不能为空！");
//                    if (Optional.ofNullable(e.getAvailableCashAmount()).orElse(BigDecimal.ZERO).compareTo(e.getThisCashAmount()) == 0 || BigDecimal.ZERO.compareTo(e.getThisCashAmount()) == 0) {
//                        Validate.isTrue(BooleanEnum.TRUE.getCapital().equals(e.getBeCash()), "【%s】是否完全兑付必须等于【是】", e.getAuditDetailCode());
//                    }
//                });
//            }
//            MarketingAuditDetailDto queryDto = new MarketingAuditDetailDto();
//            queryDto.setAuditDetailCodeList(auditDetailCodes);
//            Page<MarketingAuditDetailVo> auditDetailVoPage = marketingAuditService.findByConditions(PageRequest.of(1, Integer.MAX_VALUE), queryDto);
//            Validate.isTrue(auditDetailVoPage.getTotal() > 0, "结案明细【%s】已完全兑付，不允许保存", String.join(",", auditDetailCodes));
//            List<String> filterAuditDetailCodes = auditDetailVoPage.getRecords().stream().map(e -> e.getAuditDetailCode()).collect(Collectors.toList());
//            auditDetailCodes.removeAll(filterAuditDetailCodes);
//            Validate.isTrue(CollectionUtils.isEmpty(auditDetailCodes), "结案明细【%s】已完全兑付，不允许保存", String.join(",", auditDetailCodes));
        }
        initCreateAccount(dto.getDetailList());
    }

    /**
     * 非自动兑付初始化创建人信息
     *
     * @param list
     */
    private void initCreateAccount(List<FeeCashDetailDto> list) {
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(e -> {
                e.setCreateTime(null);
                e.setCreateAccount(null);
                e.setCreateName(null);
                e.setModifyTime(null);
                e.setModifyAccount(null);
                e.setModifyName(null);
            });
        }
    }

    /**
     * 票扣折让率校验
     *
     * @param ticketList
     */
    private void ticketValidate(List<FeeCashTicketDto> ticketList) {
        for (FeeCashTicketDto ticketDto : ticketList) {
            TpmWarehouseDetailSearchDto searchDto = new TpmWarehouseDetailSearchDto();
            searchDto.setCustomerCode(ticketDto.getCustomerCode());
            searchDto.setSignSearchStartTime(ticketDto.getYears() + "-01 00:00:00");
            searchDto.setSignSearchEndTime(DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(ticketDto.getYears() + "-01", "yyyy-MM-dd")), "yyyy-MM-dd") + " 23:59:59");
            searchDto.setProductCodes(Collections.singletonList(ticketDto.getProductCode()));
            List<DmsWarehouseOrderDetailVo> orderListTemp = dmsWarehouseOrderDetailVoService.findBySearchDto(searchDto);
            if (!CollectionUtils.isEmpty(orderListTemp)) {
                //只保留本品的发货数据
                orderListTemp = orderListTemp.stream().filter(e -> ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(e.getItemType())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(orderListTemp)) {
                    return;
                }
                List<DmsWarehouseOrderDetailVo> delivery = orderListTemp.stream().filter(e -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()) &&
                        e.getSignAmount() != null && e.getSignAmount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(delivery)) {
                    return;
                }
                List<DmsWarehouseOrderDetailVo> stored = orderListTemp.stream().filter(e -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(e.getWarehouseOrderType()) &&
                        e.getTotalAmount() != null && e.getTotalAmount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());

                //获取票扣折让率上限
                List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(MarketingAuditConstant.PKZRLSX);
                BigDecimal limitRate = new BigDecimal(dictDataVos.get(0).getDictValue());

                BigDecimal deliveryAmount = delivery.stream().map(e -> e.getSignAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal storedAmount = CollectionUtils.isEmpty(stored) ? BigDecimal.ZERO : stored.stream().map(e -> e.getTotalAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                //折扣率=票扣金额/本次发货金额
                //折扣率不允许超数据字典维护的票扣折让率上限：pkzrlsx
                BigDecimal rate = ticketDto.getAmount().divide((deliveryAmount.subtract(storedAmount)), 2, BigDecimal.ROUND_HALF_UP);
                if (limitRate.compareTo(rate) <= 0) {
                    ticketDto.setCheckFlag(Boolean.FALSE);
                    ticketDto.setErrMsg("当前折让率：" + rate + "，超过票扣折让率上限：" + limitRate + "");
                }
            }
        }

    }


    /**
     * 获取所有缓存
     *
     * @param cacheKey
     * @return
     */
    @Override
    public List<FeeCashTicketDto> findAllCacheList(String cacheKey) {
        return findCacheList(cacheKey);
    }

    /**
     * 检索活动
     *
     * @param cacheKey
     * @return
     */
    @Override
    public List<MarketingAuditDetailVo> searchAuditDetail(String cacheKey, String cashType, String cashCode) {
        List<FeeCashTicketDto> cacheList = findCacheList(cacheKey);
        if (CollectionUtils.isEmpty(cacheList)) {
            return new ArrayList<>();
        }
        cacheList.forEach(e -> {
            Validate.notBlank(e.getCustomerCode(), "票扣明细的客户必填！");
            Validate.notNull(e.getAmount(), "票扣明细的金额必填！");
        });
        List<String> budgetOneSubjectsCodes = cacheList.stream().map(FeeCashTicketDto::getBudgetSubjectsCode)
                .distinct().collect(Collectors.toList());
        List<BudgetSubjectsVo> budgetTwoSubjectsVos = budgetSubjectsVoService.findChildrensByCodes(budgetOneSubjectsCodes);
        Map<String, List<BudgetSubjectsVo>> budgetTwoSubjectsMap = Maps.newHashMap();
        Map<String, List<BudgetSubjectsVo>> budgetThreeSubjectsMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(budgetTwoSubjectsVos)) {
            budgetTwoSubjectsMap.putAll(budgetTwoSubjectsVos.stream()
                    .collect(Collectors.groupingBy(BudgetSubjectsVo::getParentBudgetSubjectsCode)));
            List<String> budgetTwoSubjectsCodes = budgetTwoSubjectsVos.stream().map(BudgetSubjectsVo::getBudgetSubjectsCode)
                    .distinct().collect(Collectors.toList());
            List<BudgetSubjectsVo> budgetThreeSubjectsVos = budgetSubjectsVoService.findChildrensByCodes(budgetTwoSubjectsCodes);
            if (CollectionUtil.isNotEmpty(budgetThreeSubjectsVos)) {
                budgetThreeSubjectsMap.putAll(budgetThreeSubjectsVos.stream()
                        .collect(Collectors.groupingBy(BudgetSubjectsVo::getParentBudgetSubjectsCode)));
            }
        }


        List<MarketingAuditDetailDto> dtoList = new ArrayList<>();
        cacheList.forEach(e -> {
            String cashTypeItem = CashTypeEnum.FEE.getDictCode().equals(cashType)
                    ? CashMethodEnum.TICKET_BUCKLE.getDictCode() : CashMethodEnum.DEDUCTIONS.getDictCode();
            String years = e.getYears();
            String customerCode = e.getCustomerCode();
            MarketingAuditDetailDto dto = new MarketingAuditDetailDto();
            dto.setYears(years);
            dto.setBudgetSubjectCode(e.getBudgetSubjectsCode());
            dto.setCustomerCode(customerCode);
            dto.setCashType(cashTypeItem);
            dtoList.add(dto);
            if (CollectionUtil.isNotEmpty(budgetTwoSubjectsMap.get(e.getBudgetSubjectsCode()))) {
                List<BudgetSubjectsVo> budgetTwoSubjectsVosTemp = budgetTwoSubjectsMap.get(e.getBudgetSubjectsCode());
                if (CollectionUtil.isNotEmpty(budgetTwoSubjectsVosTemp)) {
                    budgetTwoSubjectsVosTemp.forEach(e1 -> {
                        MarketingAuditDetailDto dto1 = new MarketingAuditDetailDto();
                        dto1.setYears(years);
                        dto1.setBudgetSubjectCode(e1.getBudgetSubjectsCode());
                        dto1.setCustomerCode(customerCode);
                        dto1.setCashType(cashTypeItem);
                        dtoList.add(dto1);
                        if (CollectionUtil.isNotEmpty(budgetThreeSubjectsMap.get(e1.getBudgetSubjectsCode()))) {
                            List<BudgetSubjectsVo> budgetThreeSubjectsVosTemp = budgetThreeSubjectsMap.get(e1.getBudgetSubjectsCode());
                            if (CollectionUtil.isNotEmpty(budgetThreeSubjectsVosTemp)) {
                                budgetThreeSubjectsVosTemp.forEach(e2 -> {
                                    MarketingAuditDetailDto dto2 = new MarketingAuditDetailDto();
                                    dto2.setYears(years);
                                    dto2.setBudgetSubjectCode(e2.getBudgetSubjectsCode());
                                    dto2.setCustomerCode(customerCode);
                                    dto2.setCashType(cashTypeItem);
                                    dtoList.add(dto2);
                                });

                            }
                        }
                    });
                }
            }
        });

        MarketingAuditDetailDto dto = new MarketingAuditDetailDto();
        dto.setBeFullCash(BooleanEnum.FALSE.getCapital());
        dto.setDtoList(dtoList);
        //如果是编辑，需要过滤掉当前的兑付明细
        if (StringUtils.isNotBlank(cashCode)) {
            List<FeeCashDetailVo> cashDetailVos = feeCashDetailRepository.findByCode(cashCode);
            if (!CollectionUtils.isEmpty(cashDetailVos)) {
                List<String> cashDetailCodes = cashDetailVos.stream().map(e -> e.getCashDetailCode()).collect(Collectors.toList());
                dto.setCashDetailCodeList(cashDetailCodes);
            }
        }
        Page<MarketingAuditDetailVo> detailVoPage = marketingAuditService.findByConditions(PageRequest.of(1, Integer.MAX_VALUE), dto);

        if (detailVoPage.getTotal() == 0) {
            return new ArrayList<>();
        }
        List<MarketingAuditDetailVo> detailVos = detailVoPage.getRecords();

        //票扣按客户汇总金额
        Map<String, BigDecimal> ticketMap = cacheList.stream().collect(Collectors.groupingBy(FeeCashTicketDto::getCustomerCode,
                Collectors.mapping(FeeCashTicketDto::getAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //结案明细按客户分组
        Map<String, List<MarketingAuditDetailVo>> auditDetailMap = detailVos.stream().collect(Collectors.groupingBy(MarketingAuditDetailVo::getCustomerCode));
        for (Map.Entry<String, List<MarketingAuditDetailVo>> entry : auditDetailMap.entrySet()) {
            BigDecimal amount = ticketMap.get(entry.getKey());
            if (amount == null) {
                continue;
            }
            List<MarketingAuditDetailVo> cusAuditDetailList = entry.getValue().stream().sorted(Comparator.comparing(MarketingAuditDetailVo::getEndDate)).collect(Collectors.toList());
            for (MarketingAuditDetailVo e : cusAuditDetailList) {
                if (BigDecimal.ZERO.compareTo(amount) == 0) {
                    continue;
                }
                if (ObjectUtils.isEmpty(e.getCashAmount())) {
                    e.setCashAmount(BigDecimal.ZERO);
                }
                if (ObjectUtils.isEmpty(e.getAvailableReversedAmount())) {
                    e.setAvailableReversedAmount(BigDecimal.ZERO);
                }
                e.setAvailableCashAmount(e.getAuditAmount().subtract(e.getCashAmount() == null ? BigDecimal.ZERO : e.getCashAmount()));
                if (amount.compareTo(e.getAvailableCashAmount()) < 0) {
                    e.setThisCashAmount(amount);
                    amount = BigDecimal.ZERO;
                } else if (amount.compareTo(e.getAvailableCashAmount()) == 0) {
                    e.setThisCashAmount(amount);
                    amount = BigDecimal.ZERO;
                } else {
                    e.setThisCashAmount(e.getAvailableCashAmount());
                    amount = amount.subtract(e.getThisCashAmount());
                }
                //以上逻辑保留 放到后面只是为了保证他是对的
                BigDecimal thisCashAmount = e.getAuditAmount().subtract(e.getCashAmount()).subtract(e.getAvailableReversedAmount());
                thisCashAmount = thisCashAmount.compareTo(e.getThisCashAmount()) > 0 ? e.getThisCashAmount() : thisCashAmount;
                e.setThisCashAmount(thisCashAmount);
            }
        }
        //过滤掉未匹配到的结案明细
        return detailVos.stream().filter(e -> e.getThisCashAmount() != null).collect(Collectors.toList());
    }


    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public void recover(String code, String remark) {
        if (feeCashOaService.oaWithdraw(code, remark)) {
            FeeCash entity = feeCashRepository.findByCode(code);
            entity.setStatus(ProcessStatusEnum.RECOVER.getDictCode());
            feeCashRepository.saveOrUpdate(entity);
        } else {
            Validate.isTrue(false, "流程撤回失败");
        }
    }

    /**
     * OA回调
     *
     * @param code
     * @param status
     */
    @Override
    public void oaCallback(String code, String status, boolean beAuto) {
        FeeCash entity = feeCashRepository.findByCode(code);
        if (Objects.isNull(entity)) {
            return;
        }
        if (StringUtils.equalsAny(entity.getStatus(), ProcessStatusEnum.PASS.getDictCode()) && !beAuto) {
            return;
        }
        entity.setStatus(status);
        entity.setPassDate(new Date());
        feeCashRepository.saveOrUpdate(entity);

        Map<String, String> feeMap = Maps.newHashMap();
        feeMap.put(entity.getCashCode(), entity.getCashType());
        //审批通过生成贷项订单
        if (ProcessStatusEnum.PASS.getDictCode().equals(status)) {
            List<FeeCashDetailVo> detailVoList = this.feeCashDetailRepository.findByCode(code);
            List<FeeCashTicketVo> ticketList = feeCashTicketRepository.findByCode(code);

            List<String> ruleCodes = generateCodeService.generateCode(DeliveryReplenishmentConstant.PREFIX_CODE, detailVoList.size());
            Integer index = 0;
            for (FeeCashDetailVo vo : detailVoList) {
                vo.setRuleCode(ruleCodes.get(index++));
            }
            //生成费用兑付/关闭明细、冲销数据、修改费用兑付/关闭明细状态
            CalCloseReversComponent calCloseReversComponent = ApplicationContextHolder.getContext().getBean(CalCloseReversComponent.class);
            List<DeliveryReplenishmentPoolDetailDto> closeRuleList = calCloseReversComponent.feeCashGenerateDeliveryReplenishmentList(detailVoList, feeMap);
            Future future = null;
            if (CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(entity.getCashMethod())) {
                future = calCloseReversComponent.feeCashGenerateWriteOff(detailVoList, null);
                if (!CollectionUtils.isEmpty(ticketList)) {
                    createCreditOrder(entity, detailVoList, ticketList);
                }
            } else if (CashTypeEnum.CLOSE.getDictCode().equals(entity.getCashType())) {
                future = calCloseReversComponent.feeCashGenerateWriteOff(detailVoList, closeRuleList);
            }
            try {
                future.get();
                //修改
                if (!CollectionUtils.isEmpty(closeRuleList)) {
                    ruleCodes.addAll(closeRuleList.stream().map(e -> e.getRuleCode()).collect(Collectors.toList()));
                }
                deliveryReplenishmentPoolDetailService.updateDeliveryReplenishmentPoolDetailStatus(ruleCodes);
            } catch (Exception e) {
                log.error("生成冲销单据失败,失败原因:{},{}", e, e.getMessage());
            }
        }
    }

    /**
     * 贷项订单生成定时任务
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createCreditOrderTask() {
        List<FeeCash> feeCashes = feeCashRepository.findCreditOrderNull();
        if (CollectionUtils.isEmpty(feeCashes)) {
            return;
        }
        feeCashes.forEach(entity -> {
            List<FeeCashDetailVo> detailVoList = this.feeCashDetailRepository.findByCode(entity.getCashCode());
            List<FeeCashTicketVo> ticketList = feeCashTicketRepository.findByCode(entity.getCashCode());
            createCreditOrder(entity, detailVoList, ticketList);
        });
    }

    private void createCreditOrder(FeeCash entity, List<FeeCashDetailVo> detailVoList, List<FeeCashTicketVo> ticketList) {
        //兑付金额汇总=0时，不生成贷项订单和调用SAP接口
        if (!CollectionUtils.isEmpty(detailVoList)) {
            BigDecimal cashAmount = detailVoList.stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (BigDecimal.ZERO.compareTo(cashAmount) == 0) {
                updateAuditCase(detailVoList);
                return;
            }
        }
        //如果票扣明细行的金额＜0.01，则当前明细不生成对应的贷项订单明细
        ticketList = ticketList.stream().filter(e -> e.getAmount().compareTo(new BigDecimal("0.01")) >= 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ticketList)) {
            updateAuditCase(detailVoList);
            return;
        }
        //费用兑付单生成贷项订单唯一性校验：贷项订单对应的费用兑付编码
        List<CreditOrder> check = creditOrderRepository.findByCashCode(entity.getCashCode());
        if (!CollectionUtils.isEmpty(check)) {
            updateAuditCase(detailVoList);
            return;
        }
        //获取客户所对应的一级、二级部门
        Set<String> customerCodeSet = ticketList.stream().map(FeeCashTicketVo::getCustomerCode).collect(Collectors.toSet());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodeSet));
        Map<String, CustomerVo> cusOrgMap = customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity(), (a, b) -> a));
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllParentByOrgCodesMap(new ArrayList<>(cusOrgMap.values().stream().map(e -> e.getOrgList().get(0).getOrgCode()).collect(Collectors.toSet())));

        //按批次号+客户+公司汇总(如果非自动兑付，批次号为兑付编号)
        if (StringUtils.isBlank(entity.getAuditCode())) {
            ticketList.forEach(e -> e.setBtNo(e.getCashCode()));
        }

        Map<String, List<FeeCashTicketVo>> map = ticketList.stream().collect(Collectors.groupingBy(e -> e.getBtNo() + ";" + e.getCustomerCode()));
        List<CreditOrder> list = new ArrayList<>();

        List<String> codes = generateCodeService.generateCode(FeeCashConstant.PREFIX_CODE_DXDD, map.size());
        AtomicInteger index = new AtomicInteger(0);
        map.forEach((k, v) -> {
            String[] item = k.split(";");
            CreditOrder creditOrder = new CreditOrder();
            creditOrder.setAuditCode(entity.getAuditCode());
            creditOrder.setAuditName(entity.getAuditName());
            creditOrder.setCashCode(entity.getCashCode());
            creditOrder.setOrderType(SapOrderTypeEnum.ZCR1.getDictCode());
            creditOrder.setBtNo(item[0]);
            CustomerVo customerVo = cusOrgMap.get(item[1]);
            creditOrder.setCompanyCode(customerVo.getCompanyCode());
            List<OrgVo> orgVos = orgMap.get(customerVo.getOrgList().get(0).getOrgCode());
            OrgVo one = orgVos.stream().filter(e -> e.getLevelNum().equals(2)).findFirst().orElse(new OrgVo());
            OrgVo two = orgVos.stream().filter(e -> e.getLevelNum().equals(3)).findFirst().orElse(new OrgVo());
            creditOrder.setDepartmentOneCode(one.getOrgCode());
            creditOrder.setDepartmentOneName(one.getOrgName());
            creditOrder.setDepartmentTwoCode(two.getOrgCode());
            creditOrder.setDepartmentTwoName(two.getOrgName());

            creditOrder.setCustomerCode(customerVo.getCustomerCode());
            creditOrder.setCustomerName(customerVo.getCustomerName());
            creditOrder.setErpCode(customerVo.getErpCode());
            creditOrder.setChannelCode(customerVo.getChannelCode());
            creditOrder.setCreditCode(codes.get(index.get()));
            creditOrder.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            creditOrder.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            creditOrder.setTenantCode(TenantUtils.getTenantCode());
            creditOrder.setPushStatus(DmsWarehouseOrderEnum.SAP_PUSH_STATUS.NO_PUSH.getCode());
            AtomicInteger lineNumber = new AtomicInteger(10);
            AtomicReference<BigDecimal> amount = new AtomicReference<>(BigDecimal.ZERO);
            v.forEach(e -> {
                e.setCreditCode(creditOrder.getCreditCode());
                e.setLineNumber(lineNumber.get());
                lineNumber.getAndAdd(10);
                amount.set(amount.get().add(e.getAmount()));
            });
            creditOrder.setAmount(amount.get());
            index.getAndAdd(1);
            list.add(creditOrder);
        });
        creditOrderRepository.saveBatch(list);
        List<String> ids = list.stream().map(e -> e.getId()).collect(Collectors.toList());
        Collection<FeeCashTicket> feeCashTickets = nebulaToolkitService.copyCollectionByWhiteList(ticketList, FeeCashTicketVo.class, FeeCashTicket.class, HashSet.class, ArrayList.class);
        feeCashTicketRepository.saveOrUpdateBatch(feeCashTickets);

        Collection<CreditOrderTicket> creditOrderTickets = nebulaToolkitService.copyCollectionByWhiteList(feeCashTickets, FeeCashTicket.class, CreditOrderTicket.class, HashSet.class, ArrayList.class, "id");
        creditOrderTickets.forEach(e -> {
            e.setId(null);
            e.setUnTicketAmount(e.getAmount());
        });
        creditOrderTicketRepository.saveBatch(creditOrderTickets);

        updateAuditCase(detailVoList);

        entity.setCreditCode(codes.get(0));
        feeCashRepository.saveOrUpdate(entity);
        //自动推送SAP
//                creditOrderService.pushSap(ids, null);
    }

    /**
     * 回写活动明细和结案明细
     *
     * @param detailVoList
     */
    private void updateAuditCase(List<FeeCashDetailVo> detailVoList) {
        if (CollectionUtils.isEmpty(detailVoList)) {
            return;
        }
        List<String> auditDetailCodes = detailVoList.stream().map(FeeCashDetailVo::getAuditDetailCode).collect(Collectors.toList());
        List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findByAuditDetailCodes(auditDetailCodes);
        if (!CollectionUtils.isEmpty(auditDetails)) {
            Map<String, FeeCashDetailVo> detialMap =
                    detailVoList.stream().collect(Collectors.toMap(FeeCashDetailVo::getAuditDetailCode,
                            Function.identity()));
            List<MarketingAuditDetailDto> auditDetailList = auditDetails.stream().map(v -> {
                FeeCashDetailVo cashDetailVo = detialMap.get(v.getAuditDetailCode());
                MarketingAuditDetailDto vo = new MarketingAuditDetailDto();
                vo.setAuditDetailCode(v.getAuditDetailCode());
                vo.setCashAmount(Optional.ofNullable(v.getCashAmount()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(cashDetailVo.getThisCashAmount()).orElse(BigDecimal.ZERO)));
                vo.setBeFullCash(cashDetailVo.getBeCash());
                return vo;
            }).collect(Collectors.toList());
            marketingAuditDetailRepository.updateCashAmount(auditDetailList);
        }
        List<String> schemeDetailCodes = detailVoList.stream().map(FeeCashDetailVo::getSchemeDetailCode).collect(Collectors.toList());
        List<MarketingPlanCase> planCaseList = this.marketingPlanCaseService.findListBySchemeDetailCodes(schemeDetailCodes);
        if (!CollectionUtils.isEmpty(planCaseList)) {
            //费用兑付审批通过时，活动的完全兑付状态回写：1、兑付明细关联的活动下的结案明细要有一条为完全结案，且这个活动的所有结案明细为完全兑付，此时活动为完全兑付
            Map<String, List<MarketingAuditDetail>> planCaseAuditMap = marketingAuditDetailRepository.findBySchemeDetailCodes(schemeDetailCodes).stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode()));
            Map<String, FeeCashDetailVo> detialMap =
                    detailVoList.stream().collect(Collectors.toMap(FeeCashDetailVo::getSchemeDetailCode,
                            Function.identity()));
            List<MarketingPlanCaseVo> planCaseVoList = planCaseList.stream().map(v -> {
                boolean beFullCash = true;
                boolean beFullAudit = false;
                FeeCashDetailVo cashDetailVo = detialMap.get(v.getSchemeDetailCode());
                MarketingPlanCaseVo vo = new MarketingPlanCaseVo();
                vo.setSchemeDetailCode(v.getSchemeDetailCode());
                vo.setCashAmount(Optional.ofNullable(v.getCashAmount()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(cashDetailVo.getThisCashAmount()).orElse(BigDecimal.ZERO)));
                if (BooleanEnum.TRUE.getCapital().equals(cashDetailVo.getBeCash())) {
                    List<MarketingAuditDetail> planCaseAuditDetails = planCaseAuditMap.get(v.getSchemeDetailCode());
                    if (!CollectionUtils.isEmpty(planCaseAuditDetails)) {
                        for (MarketingAuditDetail auditDetail : planCaseAuditDetails) {
                            if (!(BooleanEnum.TRUE.getCapital().equals(auditDetail.getBeFullCash()))) {
                                beFullCash = false;
                            }
                            if (BooleanEnum.TRUE.getCapital().equals(auditDetail.getBeFullAudit())) {
                                beFullAudit = true;
                            }
                        }
                    }
                } else {
                    beFullCash = false;
                }
                vo.setCashStatus(beFullCash && beFullAudit ?
                        CashStatusEnum.WHOLE_CASH.getCode() : CashStatusEnum.PART_CASH.getCode());
                return vo;
            }).collect(Collectors.toList());
            this.marketingPlanCaseService.rewriteEndCaseOrWithholding(planCaseVoList);
        }
    }

    /**
     * 兑付冲销
     *
     * @param cashDetailVos
     */
//    public Map<String, List<WithHoldingWriteOffVo>> ticketWriteOff(List<FeeCashDetailVo> cashDetailVos) {
//        List<WithHoldingWriteOffDto> writeOffDtoList = new ArrayList<>();
//        List<String> auditDetailList = cashDetailVos.stream().map(FeeCashDetailVo::getAuditDetailCode).collect(Collectors.toList());
//        List<FeeCashDetailVo> historyCashDetailVos = feeCashDetailRepository.findByAuditDetailCodes(auditDetailList);
//        Set<String> historyCashCodes = historyCashDetailVos.stream().map(e -> e.getCashCode()).collect(Collectors.toSet());
//        //只取审批通过和兑付的历史数据
//        List<FeeCash> cashVos = feeCashRepository.findByCodes(new ArrayList<>(historyCashCodes));
//        cashVos = cashVos.stream().filter(e -> ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus()) && (CashTypeEnum.FEE.getDictCode().equals(e.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(e.getCashType()) )).collect(Collectors.toList());
//        List<String> cashCodes = cashVos.stream().map(e -> e.getCashCode()).collect(Collectors.toList());
//        Map<String, BigDecimal> historyMap = historyCashDetailVos.stream().filter(e -> cashCodes.contains(e.getCashCode())).collect(Collectors.groupingBy(e -> e.getAuditDetailCode(), Collectors.mapping(e -> e.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
//        cashDetailVos.forEach(e -> {
//            WithHoldingWriteOffDto dto = new WithHoldingWriteOffDto();
//            dto.setActivitiesDetailCode(e.getSchemeDetailCode());
//            dto.setWriteOffType(WriteOffTypeEnum.CASH.getDictCode());
//            dto.setWriteOffAmount(e.getThisCashAmount());
//            dto.setBeWholeCash(e.getBeCash());
//            dto.setSourceCode(e.getCashDetailCode());
//            dto.setAuditAmount(e.getAuditAmount());
//            dto.setHistoryAmount(historyMap.getOrDefault(e.getAuditDetailCode(), BigDecimal.ZERO));
//            dto.setWriteOffYears(e.getPostingDate());
//            writeOffDtoList.add(dto);
//        });
//        return withHoldingWriteOffSendHecService.writeOff(writeOffDtoList);
//    }


    /**
     * 按贷项单号查询票扣
     *
     * @param code
     */
    @Override
    public List<FeeCashTicketVo> findTicketByCreditCode(String code) {
        return feeCashTicketRepository.findTicketByCreditCode(code);
    }

    /**
     * 更新流程状态
     * 单条数据不用加事务
     *
     * @param dto
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/11 16:43
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessStatus(FeeCashDto dto) {
        Assert.notNull(dto, "对象不能为空");
        Assert.hasLength(dto.getCashCode(), "兑付编号不能为空");
        if (StringUtil.isEmpty(dto.getStatus())) {
            return;
        }
        feeCashRepository.updateProcessStatus(dto);
        if (ProcessStatusEnum.PASS.getDictCode().equals(dto.getStatus())) {
            FeeCash entity = feeCashRepository.findByCode(dto.getCashCode());
            if (entity == null) {
                return;
            }
            //费用兑付不需要生成预付明细跟踪
            if (CashTypeEnum.FEE.getDictCode().equals(entity.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(entity.getCashType())) {
//                updateAuditCase(feeCashDetailRepository.findByCode(dto.getCashCode()));
                return;
            }
            generateRecords(new HashSet<>(Collections.singletonList(entity.getCashCode())));
        } else if (ProcessStatusEnum.REJECT.getDictCode().equals(dto.getStatus())) {
            FeeCash entity = feeCashRepository.findByCode(dto.getCashCode());
            if (null == entity) {
                log.error("费用兑付数据：{},不存在", dto.getCashCode());
                return;
            }
            if (entity.getCashType().equals(CashTypeEnum.WIREDUIFU.getDictCode())) {
                List<FeeCashPrepayDto> byCode = feeCashPrepayRepository.findByCodeTwo(dto.getCashCode());
                if (!CollectionUtils.isEmpty(byCode)) {
                    activityPrepayDetailRecordService.koujianPrepayCarreAmount(byCode, 2);
                }
            }
        }
    }

    /**
     * 分页查询付款信息
     *
     * @param pageable
     * @param dto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPayeeVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/11 20:40
     */
    @Override
    public Page<FeeCashPayeeVo> findByConditions(Pageable pageable, FeeCashPayeeDto dto) {
        return feeCashPayeeRepository.findByConditions(pageable, dto);
    }

    /**
     * 按结案明细编码查询兑付明细
     *
     * @param auditDetailCodes
     * @return
     */
    @Override
    public List<FeeCashDetailVo> findByAuditDetailCodes(List<String> auditDetailCodes) {
        return feeCashDetailRepository.findByAuditDetailCodes(auditDetailCodes);
    }

    /**
     * 按结案明细编码查询兑付明细
     *
     * @param auditDetailCodes
     * @return
     */
    @Override
    public List<FeeCashDetailVo> findByAuditDetailCodesPass(List<String> auditDetailCodes, String status, List<String> cashDetailCodes, List<String> cashTypeList) {
        return feeCashDetailRepository.findByAuditDetailCodesPass(auditDetailCodes, status, cashDetailCodes, cashTypeList);
    }

    /**
     * 按客户+结案单号查询兑付单
     *
     * @param auditCodes
     * @param customerCodes
     * @return
     */
    @Override
    public List<FeeCashVo> findByAuditCustomer(List<String> auditCodes, List<String> customerCodes) {
        return feeCashRepository.findByAuditCustomer(auditCodes, customerCodes);
    }


    /**
     * 更新付款信息的回单字段
     *
     * @param receiptVoList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/11 20:47
     */
    @Override
    public void updateReceipt(List<HecElectronicReceiptVo> receiptVoList) {
        feeCashPayeeRepository.updateReceipt(receiptVoList);
    }

    /**
     * 费控凭证回传
     *
     * @param dtoList
     */
    @Override
    public void hecVoucherCallback(List<HecCallbackDto> dtoList) {
        Validate.notEmpty(dtoList, "回传参数不能为空");
        dtoList.forEach(dto -> {
            Validate.notBlank(dto.getBusinessCode(), "单据编码不能为空");
            Validate.notBlank(dto.getOrderCode(), "凭证编码不能为空");
            dto.setPaySucessDate(new Date());

            //设置入账日期
            if (ObjectUtils.isNotEmpty(dto.getPostingDate())) {
                dto.setPostingDateDate(DateUtil.parse(dto.getPostingDate(), "yyyy-MM-dd"));
                LocalDate localDate = LocalDate.parse(dto.getPostingDate(), DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH_DAY));
                String postingDate = localDate.format(DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH));
                dto.setPostingDate(postingDate);
            } else {
                dto.setPostingDate(LocalDate.now().format(DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH)));
            }
        });
        Map<String, HecCallbackDto> dtoMap = dtoList.stream()
                .collect(Collectors.toMap(HecCallbackDto::getBusinessCode, v -> v, (n, o) -> n));
        List<String> cashCodes = dtoMap.values().stream().map(e -> e.getBusinessCode()).collect(Collectors.toList());
        Map<String, String> cashPostingDateMap = dtoMap.values().stream().collect(Collectors.toMap(x -> x.getBusinessCode(), l -> l.getPostingDate()));
        Map<String, Date> cashPostingDateDateMap = dtoMap.values().stream().collect(Collectors.toMap(x -> x.getBusinessCode(), l -> l.getPostingDateDate()));
        //电汇兑付与账扣兑付上的已兑付金额回写结案明细
        List<FeeCash> feeCashList = feeCashRepository.findByCodes(cashCodes).stream().filter(e -> StringUtils.isBlank(e.getVoucherCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(feeCashList)) {
            return;
        }
        cashCodes = feeCashList.stream()
                .filter(e -> Arrays.asList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.CLOSE.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()).contains(e.getCashType()))
                .map(e -> e.getCashCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cashCodes)) {
            return;
        }
        List<FeeCashDetailVo> cashDetailVos = feeCashDetailRepository.findByCodes(cashCodes);
        cashDetailVos.forEach(l -> {
            l.setPostingDate(cashPostingDateMap.getOrDefault(l.getCashCode(), null));
            l.setPostingDateDate(cashPostingDateDateMap.getOrDefault(l.getCashCode(), null));
        });
//        Map<String, List<FeeCashDetailVo>> cashDetailMap = cashDetailVos.stream().collect(Collectors.groupingBy(e -> e.getAuditDetailCode()));
//        Set<String> auditDetailCodes = cashDetailVos.stream().map(FeeCashDetailVo::getAuditDetailCode).collect(Collectors.toSet());
//        List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findByAuditDetailCodes(new ArrayList<>(auditDetailCodes));
//        auditDetails.forEach(e -> {
//            List<FeeCashDetailVo> voList = cashDetailMap.get(e.getAuditDetailCode());
//            BigDecimal thisCashAmount = voList.stream().map(FeeCashDetailVo::getThisCashAmount)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            e.setCashAmount((e.getCashAmount() == null ? BigDecimal.ZERO : e.getCashAmount()).add(thisCashAmount));
//        });
//        marketingAuditDetailRepository.updateBatchById(auditDetails);

//        updateAuditCase(cashDetailVos);

        Map<String, String> feeMap = feeCashList.stream().collect(Collectors.toMap(x -> x.getCashCode(), l -> l.getCashType()));


        List<String> ruleCodes = generateCodeService.generateCode(DeliveryReplenishmentConstant.PREFIX_CODE, cashDetailVos.size());
        Integer index = 0;
        for (FeeCashDetailVo vo : cashDetailVos) {
            vo.setRuleCode(ruleCodes.get(index++));
        }
        //生成费用兑付/关闭明细、冲销数据、修改费用兑付/关闭明细状态
        CalCloseReversComponent calCloseReversComponent = ApplicationContextHolder.getContext().getBean(CalCloseReversComponent.class);
        calCloseReversComponent.feeCashGenerateDeliveryReplenishmentList(cashDetailVos, feeMap);
        Future future = calCloseReversComponent.feeCashGenerateWriteOff(cashDetailVos, null);
        try {
            future.get();
            //修改
            deliveryReplenishmentPoolDetailService.updateDeliveryReplenishmentPoolDetailStatus(ruleCodes);
        } catch (Exception e) {
            log.error("生成冲销单据失败,失败原因:{},{}", e, e.getMessage());
        }
        feeCashRepository.hecVoucherCallback(new ArrayList<>(dtoMap.values()));
//
//        //生成费用兑付/关闭明细
//        this.generateDelivery(cashDetailVos, feeMap);
//        //兑付冲销
//        this.ticketWriteOff(cashDetailVos,Boolean.FALSE);

    }

    /**
     * 费控电汇付款状态回传
     *
     * @param dtoList
     */
    @Override
    public void hecPayStatusCallback(List<HecCallbackDto> dtoList) {
        Validate.notEmpty(dtoList, "回传参数不能为空");
        Set<String> successCodes = new HashSet<>();
        Set<String> codes = new HashSet<>();
        dtoList.forEach(dto -> {
            Validate.notBlank(dto.getBusinessCode(), "单据编码不能为空");
            Validate.notBlank(dto.getBusinessDetailCode(), "单据明细编码不能为空");
            Validate.notBlank(dto.getOrderStatus(), "付款状态不能为空");
            codes.add(dto.getBusinessCode());
            dto.setPaySucessDate(new Date());
        });
        feeCashPayeeRepository.hecPayStatusCallback(dtoList);
//        List<FeeCashPayee> payeeList = feeCashPayeeRepository.findPayStatus(codes);
//        if (CollectionUtil.isNotEmpty(payeeList)) {
//
//            List<HecCallbackDto> dtoHeadList = Lists.newArrayList();
//            Map<String, List<FeeCashPayee>> payeeMap = payeeList.stream()
//                    .filter(k -> StringUtil.isNotEmpty(k.getCashCode()))
//                    .filter(k -> StringUtil.isNotEmpty(k.getPayStatus()))
//                    .collect(Collectors.groupingBy(FeeCashPayee::getCashCode));
//            payeeMap.forEach((cashCode, list) -> {
//                if (CollectionUtil.isEmpty(list)) {
//                    return;
//                }
//                HecCallbackDto dto = new HecCallbackDto();
//                dto.setBusinessCode(cashCode);
//                List<String> payStatusList = list.stream()
//                        .filter(k -> StringUtil.isNotEmpty(k.getPayStatus()))
//                        .map(FeeCashPayee::getPayStatus)
//                        .distinct()
//                        .collect(Collectors.toList());
//                if (payStatusList.size() == 1) {
//                    //if收款信息行“付款状态”全是“付款成功”时，主列表付款状态为“付款成功”
//                    dto.setOrderStatus(payStatusList.get(0));
//
//                    dto.setPaySucessDate(new Date());
//                    dtoHeadList.add(dto);
//                } else {
//                    //if收款信息行“付款状态”有“出纳退回”时，主列表付款状态为“出纳退回”
//                    //if收款信息行“付款状态”没有“出纳退回”但有“退票”时，主列表付款状态为“退票”
//                    //if收款信息行“付款状态”全是“付款成功”时，主列表付款状态为“付款成功”
//                    //除以上3种场景外，“付款状态”为“待支付”
//                    if (payStatusList.contains(HecPayStatusTypeEnum.CASHIER_RETURNS.getCode())) {
//                        dto.setOrderStatus(HecPayStatusTypeEnum.CASHIER_RETURNS.getCode());
//                        dto.setPaySucessDate(new Date());
//                        dtoHeadList.add(dto);
//                    } else if (payStatusList.contains(HecPayStatusTypeEnum.RETURN_TICKETS.getCode())) {
//                        dto.setOrderStatus(HecPayStatusTypeEnum.RETURN_TICKETS.getCode());
//                        dto.setPaySucessDate(new Date());
//                        dtoHeadList.add(dto);
//                    }
//                }
//            });
//            feeCashRepository.hecPayStatusCallback(dtoHeadList);
//        }
//
//        if (!CollectionUtils.isEmpty(successCodes)) {
//            feeCashRepository.hecPayStatusSuccessAccount(successCodes);
//        }

        Map<String, HecCallbackDto> callbackDtoMap = dtoList.stream().collect(Collectors.toMap(e -> e.getBusinessCode(), Function.identity(), (a, b) -> a));
        feeCashRepository.hecPayStatusCallback(new ArrayList<>(callbackDtoMap.values()));
        //同步更新对应预算明细跟踪付款状态
        activityPrepayDetailRecordService.updatePayStatus(dtoList);
    }


    /**
     * 通过方案明细编码查询完全兑付并且审批通过的数据
     *
     * @param schemeDetailCodes
     * @return
     */
    @Override
    public List<FeeCashDetailVo> findCashAmountBySchemeDetailCodes(List<String> schemeDetailCodes) {
        return feeCashRepository.findCashAmountBySchemeDetailCodes(schemeDetailCodes);
    }


    /**
     * 查询在计提前进行兑付的金额
     *
     * @param schemeDetailCodes
     * @return
     */
    @Override
    public List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesPass(List<String> schemeDetailCodes) {
        return feeCashRepository.findCashAmountBySchemeDetailCodesPass(schemeDetailCodes);
    }


    @Override
    public List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesApproved(List<String> schemeDetailCodes) {
        return feeCashRepository.findCashAmountBySchemeDetailCodesApproved(schemeDetailCodes);
    }


    @Override
    public List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesApprovedBefore(List<String> orgCodes, String yearMonthLy) {
        return feeCashRepository.findCashAmountBySchemeDetailCodesApprovedBefore(orgCodes, yearMonthLy);
    }

    @Override
    public Page<TicketFeeCashDetailVo> getTicketDeductionDetail(Pageable pageable, FeeCashTicketDto dto) {
        return feeCashRepository.getTicketDeductionDetail(pageable, dto);
    }

    @Override
    public List<FeeCashDetailVo> findNoTaxCashAmountByYearsAndOrgCodes(String years, List<String> orgCodeList) {
        return feeCashRepository.findNoTaxCashAmountByYearsAndOrgCodes(years, orgCodeList);
    }

    /**
     * 生成预付跟踪数据
     *
     * @param codes
     */
    private void generateRecords(Set<String> codes) {
        List<FeeCashDetailVo> details = feeCashDetailRepository.findByCodes(new ArrayList<>(codes));
        Validate.notEmpty(details, "未找到对应的预付明细数据");
        Map<String, String> relateCodeMap = details.stream().collect(Collectors.toMap(FeeCashDetailVo::getCashDetailCode, FeeCashDetailVo::getCashCode));
        List<FeeCash> feeCashes = this.feeCashRepository.findByCodes(new ArrayList<>(codes));
        Map<String, FeeCash> feeCashMap = feeCashes.stream().collect(Collectors.toMap(FeeCash::getCashCode, Function.identity()));
        List<FeeCashPayeeVo> payeeList = feeCashPayeeRepository.findByCodes(new ArrayList<>(codes));
        Map<String, FeeCashPayeeVo> payeeMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(payeeList)) {
            payeeMap = payeeList.stream().collect(Collectors.toMap(e -> e.getCashCode(), Function.identity(), (a, b) -> a));
        }
        details.forEach(e -> {
            e.setPrepayCode(e.getCashCode());
            e.setPrepayName(e.getCashName());
            e.setPrepayDetailCode(e.getCashDetailCode());
        });
        Map<String, FeeCashDetailVo> map = details.stream().collect(Collectors.toMap(e -> e.getPrepayDetailCode(), Function.identity(), (a, b) -> a));
        Collection<ActivityPrepayDetailRecordDto> records = nebulaToolkitService.copyCollectionByWhiteList(details, FeeCashDetailVo.class, ActivityPrepayDetailRecordDto.class, LinkedHashSet.class, ArrayList.class, "id");
        Map<String, FeeCashPayeeVo> finalPayeeMap = payeeMap;
        records.forEach(e -> {
            FeeCash feeCash = feeCashMap.get(relateCodeMap.get(e.getPrepayDetailCode()));
            if (StringUtils.equals(CashMethodEnum.DEDUCTIONS.getDictCode(), feeCash.getCashMethod())) {
                e.setPrepayType(TpmPrepayTypeEnum.deduction.getCode());
            } else if (StringUtils.equals(CashMethodEnum.WIRE_TRANSFER.getDictCode(), feeCash.getCashMethod())) {
                e.setPrepayType(TpmPrepayTypeEnum.telegraphic_transfer.getCode());
            }
            e.setPrepayAmount(map.get(e.getPrepayDetailCode()).getThisCashAmount());
            e.setLineCode(finalPayeeMap.getOrDefault(e.getPrepayCode(), new FeeCashPayeeVo()).getLineCode());
            e.setPayStatus(HecPayStatusTypeEnum.NOT_PAY.getCode());
            //电汇预付取收款信息上的供应商、账扣预付取客户
            if (CashTypeEnum.WIRE.getDictCode().equals(feeCash.getCashType())) {
                FeeCashPayeeVo payeeVo = finalPayeeMap.getOrDefault(e.getPrepayCode(), new FeeCashPayeeVo());
                e.setPayeeCode(payeeVo.getPayeeCode());
                e.setPayeeName(payeeVo.getPayeeName());
            } else {
                e.setPayeeCode(map.get(e.getPrepayDetailCode()).getCustomerCode());
                e.setPayeeName(map.get(e.getPrepayDetailCode()).getCustomerName());
            }
        });
        activityPrepayDetailRecordService.create(new ArrayList<>(records));
        marketingAuditDetailRepository.updateBePrepay(details.stream().map(e -> e.getAuditDetailCode()).collect(Collectors.toList()));
    }

    /**
     * 生成发货明细报表
     *
     * @param cashDetailVos
     */
    private void generateDelivery(List<FeeCashDetailVo> cashDetailVos, Map<String, List<WithHoldingWriteOffVo>> writeOffMap) {
        if (CollectionUtils.isEmpty(cashDetailVos)) {
            return;
        }
        List<DeliveryReplenishmentPoolDetailDto> deliveryList = new ArrayList<>();
        List<String> auditDetailList = cashDetailVos.stream().map(FeeCashDetailVo::getAuditDetailCode).collect(Collectors.toList());
        Map<String, BigDecimal> historyMap = feeCashDetailRepository.findByAuditDetailCodes(auditDetailList)
                .stream().filter(x -> ObjectUtils.isNotEmpty(x.getThisCashAmount()))
                .collect(Collectors.groupingBy(e -> e.getAuditDetailCode(), Collectors.mapping(e -> e.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findByAuditDetailCodes(auditDetailList);
        Map<String, MarketingAuditDetail> auditDetailMap = auditDetails.stream().collect(Collectors.toMap(e -> e.getAuditDetailCode(), Function.identity(), (a, b) -> a));
        for (FeeCashDetailVo detail : cashDetailVos) {
            MarketingAuditDetail auditDetail = auditDetailMap.getOrDefault(detail.getAuditDetailCode(), new MarketingAuditDetail());
            DeliveryReplenishmentPoolDetailDto delivery = new DeliveryReplenishmentPoolDetailDto();
            delivery.setBusinessCode(detail.getCashDetailCode());
            delivery.setCustomerCode(detail.getCustomerCode());
            delivery.setCustomerName(detail.getCustomerName());
            delivery.setOperationType(DeliveryDetailTypeEnum.FEE_CASH.getCode());
            delivery.setDeliveryTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
            delivery.setOperationAmount(detail.getThisCashAmount());
            delivery.setSchemeDetailCode(detail.getSchemeDetailCode());
            delivery.setAuditCode(detail.getAuditCode());
            delivery.setAuditDetailCode(detail.getAuditDetailCode());
            delivery.setCashCode(detail.getCashCode());
            delivery.setCashDetailCode(detail.getCashDetailCode());
            delivery.setSchemeCode(detail.getSchemeCode());
            delivery.setCreateAccount(auditDetail.getCreateAccount());
            delivery.setPositionCode(auditDetail.getPositionCode());

            //判断是否冲销
            if (!CollectionUtils.isEmpty(writeOffMap) && writeOffMap.containsKey(detail.getSchemeDetailCode())) {
                delivery.setBeWriteOff(BooleanEnum.TRUE.getCapital());
                delivery.setWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
            } else {
                delivery.setBeWriteOff(BooleanEnum.FALSE.getCapital());
                delivery.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
            }
            //如果是完全结案，并且结案明细结案金额-“完全兑付金额（本次兑付金额+已兑付金额）”大于0，生成费用关闭
            if (BooleanEnum.TRUE.getCapital().equals(detail.getBeCash())) {
                BigDecimal subtract = (Optional.ofNullable(auditDetail.getAuditAmount()).orElse(BigDecimal.ZERO)).subtract(historyMap.get(detail.getAuditDetailCode()));
                if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                    DeliveryReplenishmentPoolDetailDto closeDelivery = nebulaToolkitService.copyObjectByWhiteList(delivery, DeliveryReplenishmentPoolDetailDto.class, LinkedHashSet.class, ArrayList.class);

                    //判断是否冲销
                    if (!CollectionUtils.isEmpty(writeOffMap) && writeOffMap.containsKey(detail.getSchemeDetailCode()) && writeOffMap.get(detail.getSchemeDetailCode()).size() > 1) {
                        closeDelivery.setBeWriteOff(BooleanEnum.TRUE.getCapital());
                        closeDelivery.setWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
                        WithHoldingWriteOffVo close = writeOffMap.get(detail.getSchemeDetailCode()).stream().filter(e -> e.getWriteOffType().equals(WriteOffTypeEnum.CLOSE.getDictCode())).collect(Collectors.toList()).get(0);
                        closeDelivery.setOperationAmount(close.getWriteOffAmount());
                    } else {
                        closeDelivery.setBeWriteOff(BooleanEnum.FALSE.getCapital());
                        closeDelivery.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
                        closeDelivery.setOperationAmount(subtract);
                    }
                    closeDelivery.setOperationType(DeliveryDetailTypeEnum.FEE_CLOSE.getCode());

                    deliveryList.add(closeDelivery);
                }
            }
            deliveryList.add(delivery);
        }
        deliveryReplenishmentPoolDetailService.create(deliveryList);
    }


    @Override
    public List<FeeCashDto> findListByTicketBuckle() {
        List<FeeCashDto> list = feeCashRepository.findListByTicketBuckle();
        return list;
    }

    /**
     * 刷税额-未税
     *
     * @param cashCode
     */
    @Override
    public void brushFeeCashTaxAmount(String cashCode) {
        loginUserService.refreshAuthentication(null);
        List<FeeCashDetail> detailList = feeCashDetailRepository.findListByCashCode(cashCode);
        List<FeeCashTicket> ticketList = feeCashTicketRepository.findListByCashCode(cashCode);

        Set<String> materialCodes = ticketList.stream().map(x -> x.getProductCode()).collect(Collectors.toSet());
        MaterialSearchDto searchDto = new MaterialSearchDto();
        searchDto.setMaterialCodeSet(materialCodes);
        List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
        Map<String, BigDecimal> materialTaxRateMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getTaxRate()).orElse(BigDecimal.ZERO), (a, b) -> a));

        //票扣税额汇总
        AtomicReference<BigDecimal> thisCashNoTaxAmount = new AtomicReference<>(BigDecimal.ZERO);
        ticketList.forEach(e -> {
            BigDecimal taxRate = materialTaxRateMap.getOrDefault(e.getProductCode(), BigDecimal.ZERO);
            thisCashNoTaxAmount.set(thisCashNoTaxAmount.get().add((e.getAmount().divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_UP))));
        });
        BigDecimal cashAmountTotal = detailList.stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (cashAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        BigDecimal tail = thisCashNoTaxAmount.get();
        for (int i = 0; i < detailList.size(); i++) {
            FeeCashDetail feeCashDetailDto = detailList.get(i);
            if (i == detailList.size() - 1) {
                feeCashDetailDto.setThisCashNoTaxAmount(tail);
            } else {
                BigDecimal thisCashTaxAmountSingle = thisCashNoTaxAmount.get().multiply(feeCashDetailDto.getThisCashAmount().divide(cashAmountTotal, 2, BigDecimal.ROUND_HALF_UP));
                feeCashDetailDto.setThisCashNoTaxAmount(thisCashTaxAmountSingle);
                tail = tail.subtract(thisCashTaxAmountSingle);
            }
            feeCashDetailDto.setThisCashTaxAmount(feeCashDetailDto.getThisCashAmount().subtract(feeCashDetailDto.getThisCashNoTaxAmount()));
        }
        feeCashDetailRepository.updateBatchById(detailList);
    }


    @Override
    public void updateFeeCashStatus(List<String> codes) {
        feeCashRepository.updateFeeCashStatus(codes);
    }

    /**
     * 根据票据类型校验发票
     * @param dto 费用兑付DTO
     */
    private void validateInvoiceByBillType(FeeCashDto dto) {
        if (CollectionUtil.isEmpty(dto.getDetailList())) {
            return;
        }

        // 检查是否所有明细都是收据类型
        boolean allReceipt = dto.getDetailList().stream()
                .allMatch(detail -> BillTypeEnum.RECEIPT.getDictCode().equals(detail.getBillType()));

        if (allReceipt) {
            // 如果所有明细都是收据类型，则不能存在发票明细
            Validate.isTrue(CollectionUtil.isEmpty(dto.getInvoiceList()), "所有明细都是收据类型时，不能存在发票明细");
            return;
        }

        // 如果有发票类型的明细，则需要校验发票
        boolean hasInvoiceType = dto.getDetailList().stream()
                .anyMatch(detail -> BillTypeEnum.INVOICE.getDictCode().equals(detail.getBillType()));

        if (hasInvoiceType) {
            Validate.notEmpty(dto.getInvoiceList(), "存在发票类型的明细时，发票不能为空");
        }
    }

    /**
     * 根据票据类型校验明细发票
     * @param detail 费用兑付明细
     * @param invoiceGroup 发票分组
     */
    private void validateDetailInvoiceByBillType(FeeCashDetailDto detail, Map<String, List<FeeCashInvoiceDto>> invoiceGroup) {
        if (BillTypeEnum.INVOICE.getDictCode().equals(detail.getBillType())) {
            // 票据类型为发票时，必须有发票关联
            Validate.isTrue(invoiceGroup.containsKey(detail.getAuditDetailCode()),
                    "结案明细编码【%s】票据类型为发票时，发票不能为空", detail.getAuditDetailCode());
        } else if (BillTypeEnum.RECEIPT.getDictCode().equals(detail.getBillType())) {
            // 票据类型为收据时，不需要发票关联，但需要收据说明
            Validate.notBlank(detail.getReceiptDescription(),
                    "结案明细编码【%s】票据类型为收据时，收据说明不能为空", detail.getAuditDetailCode());
        } else {
            // 票据类型未设置或无效时，按原逻辑处理（需要发票）
            Validate.isTrue(invoiceGroup.containsKey(detail.getAuditDetailCode()),
                    "结案明细编码【%s】发票不能为空", detail.getAuditDetailCode());
        }
    }
}
