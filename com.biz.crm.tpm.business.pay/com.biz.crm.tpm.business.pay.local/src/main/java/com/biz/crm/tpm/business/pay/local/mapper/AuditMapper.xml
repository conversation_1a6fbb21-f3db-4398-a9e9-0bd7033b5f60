<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AuditMapper">
  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.Audit" id="AuditMap">
  </resultMap>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.AuditVo">
    select t.*
    from tpm_audit t
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus != ''">
        and t.enable_status = #{dto.enableStatus}
      </if>
      <if test="dto.auditName != null and dto.auditName != ''">
        <bind name="auditName" value="'%' + dto.auditName + '%'"/>
        and t.audit_name like #{auditName}
      </if>
      <if test="dto.auditCode != null and dto.auditCode != ''">
        <bind name="auditCode" value="'%' + dto.auditCode + '%'"/>
        and t.audit_code like #{auditCode}
      </if>
      and t.del_flag = '${@<EMAIL>()}'
    </where>
    order by t.create_time desc, t.id
  </select>

  <select id="findByActivitiesDetailCode" resultType="com.biz.crm.tpm.business.pay.sdk.vo.AuditVo">
    select ta.*
    from tpm_audit ta
    left join tpm_audit_detail tad on ta.audit_code = tad.audit_code and ta.tenant_code = tad.tenant_code
    <where>
      ta.tenant_code = #{tenantCode}
      and tad.activities_detail_code = #{activitiesDetailCode}
    </where>
  </select>
</mapper>