package com.biz.crm.tpm.business.pay.local.notifier.log;

import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.pay.sdk.dto.log.PrepayLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.event.log.PrepayLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动预付 业务日志监听实现
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */
@Component
public class PrepayLogEventListenerImpl implements PrepayLogEventListener {
  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;
  /**
   * 创建事件
   *
   * @param eventDto
   */
  public void onCreate(PrepayLogEventDto eventDto){
    PrepayVo newest = eventDto.getNewest();
    PrepayVo original = eventDto.getOriginal();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
  /**
   * 删除事件
   *
   * @param eventDto
   */
  public void onDelete(PrepayLogEventDto eventDto){
    PrepayVo newest = eventDto.getNewest();
    PrepayVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
  /**
   * 更新日志
   *
   * @param eventDto
   */
  public void onUpdate(PrepayLogEventDto eventDto){
    PrepayVo newest = eventDto.getNewest();
    PrepayVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
}
