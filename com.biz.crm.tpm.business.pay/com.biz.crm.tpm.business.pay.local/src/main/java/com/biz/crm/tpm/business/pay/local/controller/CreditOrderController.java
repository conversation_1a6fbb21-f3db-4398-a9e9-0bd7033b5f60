package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.CreditOrderTicketDto;
import com.biz.crm.tpm.business.pay.sdk.service.CreditOrderService;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderTicketVo;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Api(tags = "贷项订单功能接口")
@RestController
@RequestMapping("/v1/creditOrder")
@Slf4j
public class CreditOrderController {

    /**
     * 服务对象
     */
    @Autowired(required = false)
    private CreditOrderService creditOrderService;
    @Autowired(required = false)
    private RedisLockService redisLockService;

    /**
     * 分页查询所有数据
     *
     * @param pageable 分页对象
     * @param dto      查询实体
     * @return 所有数据
     */
    @ApiOperation(value = "分页查询所有数据")
    @GetMapping("findByConditions")
    public Result<Page<CreditOrderTicketVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                  @ApiParam(name = "dto", value = "查询对象") CreditOrderTicketDto dto) {
        try {
            Page<CreditOrderTicketVo> page = this.creditOrderService.findByConditions(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 推送sap
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "推送sap")
    @PostMapping("/pushSap")
    public Result<?> pushSap(@RequestBody List<String> ids) {
        Validate.isTrue(this.redisLockService.batchLock(FeeCashConstant.PUSH_SAP_LOCK_PREFIX, ids,
                TimeUnit.MINUTES, 30), "有数据被锁定,请稍后再试");
        try {
            StringBuilder errorMsg = new StringBuilder();
            ids.forEach(v -> {
                try {
                    this.creditOrderService.pushSap(Lists.newArrayList(v), null);
                } catch (Exception e) {
                    log.error(String.format("贷项订单【%s】推送sap失败：%s", v, e.getMessage()), e);
                    errorMsg.append(e.getMessage()).append(";");
                }
            });
            if (StringUtils.isNoneBlank(errorMsg)) {
                throw new IllegalArgumentException(errorMsg.toString());
            }
        } finally {
            if (this.redisLockService.isBatchLock(FeeCashConstant.PUSH_SAP_LOCK_PREFIX, ids)) {
                this.redisLockService.batchUnLock(FeeCashConstant.PUSH_SAP_LOCK_PREFIX, ids);
            }
        }
        return Result.ok();
    }

    /**
     * 推送sap定时任务
     *
     * @param
     * @return
     */
    @ApiOperation(value = "推送sap定时任务")
    @GetMapping("/pushSapTask")
    public Result<?> pushSapTask() {
        try {
            this.creditOrderService.pushSapTask();
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过编号查询票扣详情
     *
     * @param code
     * @return
     */
    @ApiOperation(value = "通过编号查询票扣详情")
    @GetMapping("findByCode")
    public Result<List<CreditOrderTicketVo>> findByCode(@ApiParam(name = "code", value = "编号", required = true) String code) {
        try {
            List<CreditOrderTicketVo> vo = this.creditOrderService.findByCode(code);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 贷项订单合计
     *
     * @param
     * @return
     */
    @ApiOperation(value = "贷项订单合计")
    @GetMapping("findTotalByConditions")
    public Result<CreditOrderVo> findTotalByConditions(@ApiParam(name = "params", value = "贷项订单") @RequestParam LinkedHashMap<String, Object> params) {
        try {
            CreditOrderVo vo = this.creditOrderService.findTotalByConditions(params);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
