package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.Account;
import com.biz.crm.tpm.business.pay.local.mapper.AccountMapper;
import com.biz.crm.tpm.business.pay.local.mapper.AccountVoMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AccountVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.HashSet;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


/**
 * 费用上账主表(TpmAccount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
@Component
public class AccountVoRepository extends ServiceImpl<AccountVoMapper, AccountVo> {

  @Resource
  private AccountMapper accountMapper;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  public List<AccountVo> findByIds(List<String> idList) {
    List<Account> accounts = accountMapper.selectList(new LambdaQueryWrapper<Account>()
        .eq(Account::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(Account::getTenantCode, TenantUtils.getTenantCode())
        .in(Account::getId, idList));
    ArrayList<AccountVo> accountVos = Lists.newArrayList(
        nebulaToolkitService.copyCollectionByWhiteList(accounts, Account.class, AccountVo.class, HashSet.class, ArrayList.class));
    return accountVos;

/*    return this.lambdaQuery()
        .eq(AccountVo::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(AccountVo::getTenantCode, TenantUtils.getTenantCode())
        .in(AccountVo::getId,idList)
        .list();*/
  }

  public void enable(List<String> idList) {
    String enableCode = EnableStatusEnum.ENABLE.getCode();
    accountMapper.enableOrDisableByIds(idList, enableCode);

  /*this.lambdaUpdate()
      .set(AccountVo::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
      .eq(AccountVo::getTenantCode,TenantUtils.getTenantCode())
      .in(AccountVo::getId,idList)
      .update();*/
  }

  public void disable(List<String> idList) {
    String enableCode = EnableStatusEnum.DISABLE.getCode();
    accountMapper.enableOrDisableByIds(idList, enableCode);

    /*this.lambdaUpdate()
        .set(AccountVo::getEnableStatus, EnableStatusEnum.DISABLE.getCode())
        .eq(AccountVo::getTenantCode,TenantUtils.getTenantCode())
        .in(AccountVo::getId,idList)
        .update();*/
  }

  public Page<AccountVo> findByConditions(Pageable pageable, AccountDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<AccountVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    Page<AccountVo> pageList = this.baseMapper.findByConditions(page, dto);
    return pageList;
  }

  /**
   * 查询所有上账金额
   * @param account
   */
  public Map<String,BigDecimal> findAllAccountAmount(AccountDto account) {
    account.setTenantCode(TenantUtils.getTenantCode());
    List<AccountVo> allAccountAmount = this.baseMapper.findAllAccountAmount(account);
    if (CollectionUtils.isEmpty(allAccountAmount)){
      return new HashMap<>(0);
    }
    Map<String,BigDecimal> res =new HashMap<>();
    for (AccountVo accountVo : allAccountAmount) {
      BigDecimal amount = accountVo.getAmount();
      String customerCode = accountVo.getCustomerCode();
      String auditDetailCode = accountVo.getAuditDetailCode();
      String key = auditDetailCode.concat(customerCode!=null?customerCode:"");
      res.put(key,amount);
    }
    return res;
  }
}

