package com.biz.crm.tpm.business.pay.local.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.pay.local.calcomponent.WithholdingCalComponent;
import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingBalanceDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingCollectDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingDto;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingCollectService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingLockVoService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingSendHecService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffService;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingBalanceVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingCollectVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import liquibase.pro.packaged.G;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 费用预提(WithHolding)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-06-25 14:20:20
 */
@RestController
@RequestMapping("/v1/pay/WithHolding")
@Slf4j
@Api(tags = "费用预提")
public class WithHoldingController extends BusinessPageCacheController<WithHoldingVo, WithHoldingDto> {
    /**
     * 服务对象
     */
    @Autowired(required = false)
    private WithHoldingService withHoldingService;

    @Autowired(required = false)
    private WithHoldingSendHecService withHoldingSendHecService;

    @Autowired(required = false)
    private WithHoldingLockVoService withHoldingLockVoService;

    @Autowired(required = false)
    private WithholdingCalComponent withholdingCalComponent;

    @Autowired(required = false)
    private WithHoldingCollectService withHoldingCollectService;


    /**
     * 分页查询所有数据
     *
     * @param pageable    分页对象
     * @param WithHolding 查询实体
     * @return 所有数据
     */
    @ApiOperation(value = "分页查询所有数据")
    @GetMapping("findByConditions")
    public Result<Page<WithHoldingVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                        @ApiParam(name = "WithHolding", value = "费用预提") WithHoldingDto WithHolding) {
        try {
            Page<WithHoldingVo> page = this.withHoldingService.findByConditions(pageable, WithHolding);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 提交审批
     *
     * @param dto 主键结合
     * @return
     */
    @ApiOperation(value = "提交审批")
    @PostMapping("submit")
    public Result<WithHoldingCollectVo> submit(
            @ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
            @RequestBody WithHoldingDto dto) {
        return Result.ok(this.withHoldingService.submit(cacheKey, dto));
    }

    /**
     * 提交审批保存
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "提交审批保存")
    @PostMapping("submitSave")
    public Result<?> submitSave(
            @ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
            @ApiParam(name = "WithHolding", value = "费用预提") @RequestBody WithHoldingCollectDto dto) {
        String key = dto.getYearMonthLy() + dto.getDepartmentCode();
        boolean lock = withHoldingLockVoService.lock(key, TimeUnit.SECONDS, 30);
        Validate.isTrue(lock, "操作计提失败，获取操作锁失败！");
        try {
            String id = this.withHoldingService.submitSave(cacheKey, dto);
//            //调用OA审批
//            withHoldingCollectService.submit(Lists.newArrayList(id));
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            withHoldingLockVoService.unLock(key);
        }
    }

    /**
     * 自动预提
     *
     * @param withHolding 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "自动预提")
    @PostMapping("handleAuto")
    public Result<?> handleAuto(@ApiParam(name = "WithHolding", value = "费用预提") @RequestBody WithHoldingDto withHolding) {
        try {
//            this.withholdingCalComponent.calWithholdingSync(withHolding);
            withHoldingService.syncHandleAuto(withHolding);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 调整暂存
     *
     * @param withHolding 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "调整暂存")
    @PostMapping("adjust")
    public Result<?> adjust(@ApiParam(name = "WithHolding", value = "费用预提") @RequestBody WithHoldingDto withHolding) {
        try {
            this.withHoldingService.adjust(withHolding, ConfirmStatusEnum.UNCONFIRMED);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 调整确认
     *
     * @param withHolding 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "调整确认")
    @PostMapping("adjustConfirm")
    public Result<?> adjustConfirm(@ApiParam(name = "WithHolding", value = "费用预提") @RequestBody WithHoldingDto withHolding) {
        try {
            this.withHoldingService.adjust(withHolding, ConfirmStatusEnum.CONFIRMED);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam List<String> idList) {
        try {
            this.withHoldingService.delete(idList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 推送
     *
     * @param idList 主键结合
     * @return
     */
    @ApiOperation(value = "推送")
    @PatchMapping("push")
    public Result<?> push(@ApiParam(name = "idList", value = "主键集合") @RequestParam List<String> idList) {
        boolean lock = withHoldingLockVoService.lock(idList, TimeUnit.SECONDS, 30);
        Validate.isTrue(lock, "操作计提失败，获取操作锁失败！");
        try {
            return this.withHoldingSendHecService.push(idList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            withHoldingLockVoService.unLock(idList);
        }
    }

    @ApiOperation(value = "更新计提定时任务", httpMethod = "GET")
    @GetMapping("/generateWithHolding")
    public Result generateWithHolding() {
        try {
            this.withHoldingService.taskAuto();
            return Result.ok("更新计提定时任务");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据活动明细编码查询审批通过的计提金额
     *
     * @param codes 主键结合
     * @return
     */
    @ApiOperation(value = "根据活动明细编码查询审批通过的计提金额")
    @PostMapping("findBySchemeDetailCodesPass")
    public Result<List<WithHoldingVo>> findBySchemeDetailCodesPass(@ApiParam(name = "codes", value = "活动明细编码") @RequestBody List<String> codes) {
        try {
            return Result.ok(this.withHoldingService.findBySchemeDetailCodesPass(codes));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "缓存明细导出")
    @GetMapping("findCachePageListImport")
    public Result<Page<WithHoldingVo>> findCachePageListImport(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                               @ApiParam(name = "dto", value = "缓存键") WithHoldingDto dto) {
        try {
            return this.findCachePageList(pageable, dto.getCacheKey(), dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 费用计提合计
     *
     * @param
     * @return
     */
    @ApiOperation(value = "费用计提合计")
    @GetMapping("findTotalByConditions")
    public Result<WithHoldingVo> findTotalByConditions(@ApiParam(name = "params", value = "费用预提") @RequestParam LinkedHashMap<String, Object> params) {
        try {
            WithHoldingVo vo = this.withHoldingService.findTotalByConditions(params);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 确认
     *
     * @param codes 主键结合
     * @return
     */
    @ApiOperation(value = "确认")
    @PostMapping("confirm")
    public Result<?> confirm(@RequestBody List<String> codes) {
        try {
            this.withHoldingService.confirm(codes, ConfirmStatusEnum.CONFIRMED);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 取消确认
     *
     * @param codes 主键结合
     * @return
     */
    @ApiOperation(value = "取消确认")
    @PostMapping("cancelConfirm")
    public Result<?> cancelConfirm(@RequestBody List<String> codes) {
        try {
            this.withHoldingService.confirm(codes, ConfirmStatusEnum.UNCONFIRMED);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/findActualReportAmountByYears")
    @ApiOperation("根据年月查询管报实际金额")
    public Result<List<WithHoldingIncomeVo>> findActualReportAmountByYears(@RequestBody List<String> years) {
        try {
            List<WithHoldingIncomeVo> voList = this.withHoldingService.findActualReportAmountByYears(years);
            return Result.ok(voList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "查询计提金额-管报实际金额")
    @PostMapping("findAmountByOrgCodesAndYears")
    public Result<BigDecimal> findAmountByOrgCodesAndYears(@RequestBody List<String> orgCodes, @RequestParam("years") String years) {
        return Result.ok(withHoldingService.findAmountByOrgCodesAndYears(orgCodes, years));
    }


    @ApiOperation(value = "查询预提数据-通过年月+组织编码")
    @PostMapping("findWithholdingListByYearsAndOrgCodes")
    public Result<List<WithHoldingVo>> findWithholdingListByYearsAndOrgCodes(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(withHoldingService.findWithholdingListByYearsAndOrgCodes(dto));
    }

    /**
     * 分页查询计提余额数据
     * 实现WithHoldingBalanceDataViewRegister的SQL逻辑，保留下划线返回形式
     *
     * 支持排序的字段：
     * - year_month_ly: 预提年月
     * - years: 费用归属年月
     * - apply_amount_total: 申请金额总计
     * - with_holding_amount_total: 预提金额总计
     * - write_off_amount_total: 冲销金额总计
     * - balance: 余额
     *
     * 排序格式：字段名,排序方式(asc/desc)
     * 示例：year_month_ly,desc 或 balance,asc
     * 如果不传排序方式或传asc，则为升序
     *
     * @param pageable 分页对象
     * @param dto 查询条件（包含排序字段sort）
     * @return 计提余额分页数据
     */
    @ApiOperation(value = "分页查询计提余额数据", notes = "支持年月和金额字段排序，排序格式：字段名,排序方式(asc/desc)。使用POST请求，查询条件通过请求体传递")
    @PostMapping("findWithHoldingBalanceByConditions")
    public Result<Page<WithHoldingBalanceVo>> findWithHoldingBalanceByConditions(
            @ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
            @ApiParam(name = "dto", value = "查询条件，支持sort字段排序") @RequestBody WithHoldingBalanceDto dto) {
        try {
            // 根据TPM-计提余额报表_副本.docx文档要求实现计提余额报表逻辑
            // 数据呈现维度：计提编码，更新频率：每月2号
            Page<WithHoldingBalanceVo> page = this.withHoldingService.findWithHoldingBalanceByConditions(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
