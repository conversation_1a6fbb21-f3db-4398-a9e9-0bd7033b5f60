package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepay;
import com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;


/**
 * 活动预付(ActivityPrepay)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-04 20:03:02
 */
@Component
public class ActivityPrepayRepository extends ServiceImpl<ActivityPrepayMapper, ActivityPrepay> {

    @Autowired
    private ActivityPrepayMapper activityPrepayMapper;

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    public ActivityPrepay findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return this.getOne(Wrappers.lambdaQuery(ActivityPrepay.class)
                .eq(ActivityPrepay::getPrepayCode, code)
                .eq(ActivityPrepay::getTenantCode, TenantUtils.getTenantCode())
                .eq(ActivityPrepay::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        );
    }

    /**
     * 按id查询
     *
     * @param id
     * @return
     */
    public ActivityPrepay findById(String id) {
        if (StringUtil.isEmpty(id)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(ActivityPrepay::getTenantCode, TenantUtils.getTenantCode())
                .eq(ActivityPrepay::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ActivityPrepay::getId, id)
                .one();
    }

    /**
     * 按id集合查询
     *
     * @param idList
     * @return
     */
    public List<ActivityPrepay> findByIdList(List<String> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(ActivityPrepay::getTenantCode, TenantUtils.getTenantCode())
                .eq(ActivityPrepay::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(ActivityPrepay::getId, idList)
                .list();
    }

    /**
     * 按id批量删除
     *
     * @param codeList
     */
    public void deleteByCodes(List<String> codeList) {
        if (CollectionUtil.isEmpty(codeList)) {
            return;
        }
        this.lambdaUpdate().in(ActivityPrepay::getPrepayCode, codeList).remove();
    }

    public void updateProcessStatus(ActivityPrepayDto dto) {
        if (Objects.isNull(dto)
                || StringUtil.isEmpty(dto.getPrepayCode())
                || StringUtil.isEmpty(dto.getStatus())) {
            return;
        }
        if (ProcessStatusEnum.COMMIT.getDictCode().equals(dto.getStatus())) {
            this.lambdaUpdate()
                    .eq(ActivityPrepay::getPrepayCode, dto.getPrepayCode())
                    .set(ActivityPrepay::getStatus, dto.getStatus())
                    .set(Objects.nonNull(dto.getProcessDate()), ActivityPrepay::getProcessDate, dto.getProcessDate())
                    .set(StringUtil.isNotEmpty(dto.getProcessKey()), ActivityPrepay::getProcessKey, dto.getProcessKey())
                    .set(StringUtil.isNotBlank(dto.getHecReceiptUrl()), ActivityPrepay::getHecReceiptUrl, dto.getHecReceiptUrl())
                    .update();
        } else {
            this.lambdaUpdate()
                    .eq(ActivityPrepay::getPrepayCode, dto.getPrepayCode())
                    .eq(ActivityPrepay::getProcessKey, dto.getProcessKey())
                    .set(ActivityPrepay::getStatus, dto.getStatus())
                    .update();
        }

    }

    public void hecPayStatusCallback(List<HecCallbackDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        this.baseMapper.hecPayStatusCallback(dtoList);
    }

    public List<ActivityPrepay> findByProcessKey(ActivityPrepayDto dto) {
        return this.lambdaQuery()
            .eq(ActivityPrepay::getProcessKey, dto.getProcessKey())
            .list();
    }

    public List<ActivityPrepay> findByCodes(Set<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(ActivityPrepay::getPrepayCode, codes)
                .eq(ActivityPrepay::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }
}

