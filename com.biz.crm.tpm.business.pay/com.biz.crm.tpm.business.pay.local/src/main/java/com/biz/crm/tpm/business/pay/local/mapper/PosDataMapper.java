package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.PosData;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
public interface PosDataMapper extends BaseMapper<PosData> {

    Page<PosDataVo> findList(Page<PosDataVo> page, @Param("vo")PosDataVo vo);
}
