package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.service.BillingCompareReportService;
import com.biz.crm.tpm.business.pay.sdk.vo.BillingCompareReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "账单费用对比功能接口")
@RestController
@RequestMapping("/v1/pay/billingCompareReport")
@Slf4j
public class BillingCompareReportController {

    @Autowired(required = false)
    private BillingCompareReportService billingCompareReportService;

    @ApiOperation(value = "按条件查询")
    @GetMapping("findByConditions")
    public Result<Page<BillingCompareReportVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                 @ApiParam(name = "dto", value = "对象") BillingCompareReportVo dto) {
        try {
            Page<BillingCompareReportVo> page = billingCompareReportService.findByConditions(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
