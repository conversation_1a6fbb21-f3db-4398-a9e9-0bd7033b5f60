package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "tpm_with_holding_write_off", description = "费用冲销")
@TableName("tpm_with_holding_write_off")
@Data
@Entity(name = "tpm_with_holding_write_off")
@org.hibernate.annotations.Table(appliesTo = "tpm_with_holding_write_off", comment = "费用冲销")
@Table(name = "tpm_with_holding_write_off", indexes = {
        @Index(name = "tpm_with_holding_write_off_uq1", columnList = "write_off_code",unique = true),
        @Index(name = "tpm_with_holding_write_off_idx2", columnList = "with_holding_code"),
})
public class WithHoldingWriteOff extends TenantFlagOpEntity {

  /**
   * 推送状态 枚举
   */
  @ApiModelProperty(name = "推送费控状态", notes = "推送费控状态")
  @Column(name = "push_status",   columnDefinition = "VARCHAR(32) DEFAULT 'N' COMMENT '推送费控状态'")
  private String pushStatus;

  @ApiModelProperty("费控单号")
  @Column(name = "external_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '费控单号 '")
  private String externalCode;

  /**
   * 预提类型 枚举
   */
  @ApiModelProperty(name = "预提类型", notes = "预提类型")
  @Column(name = "with_holding_type",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '预提类型 '")
  private String withHoldingType;

  /**
   * 预提编号
   */
  @ApiModelProperty(name = "预提编号",notes = "预提编号")
  @Column(name = "with_holding_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预提编号 '")
  private String withHoldingCode;

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  @Column(name = "activities_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  @Column(name = "activities_Name", length = 64, columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 活动明细编码
   */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value= "活动明细编码")
  @Column(name = "activities_detail_code", length = 64, columnDefinition = "VARCHAR(128) COMMENT '活动明细编码 '")
  private String activitiesDetailCode;

  /**
   * 预算科目编码
   */
  @ApiModelProperty(name = "预算科目编码",notes = "")
  @Column(name = "budget_subjects_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
  private String budgetSubjectsCode;

  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "预算科目名称",notes = "")
  @Column(name = "budget_subjects_name", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;

  /**
   * 活动大类编码
   */
  @ApiModelProperty(name = "活动大类编码", notes = "活动大类编码")
  @Column(name = "cost_type_category_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动大类编码 '")
  private String costTypeCategoryCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "活动大类名称", notes = "活动大类名称")
  @Column(name = "cost_type_category_name",  length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动大类名称 '")
  private String costTypeCategoryName;

  /**
   * 活动细类编码
   */
  @ApiModelProperty(name = "活动细类编码", notes = "活动细类编码")
  @Column(name = "cost_type_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动细类编码 '")
  private String costTypeDetailCode;

  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "活动细类名称", notes = "活动系类名称")
  @Column(name = "cost_type_detail_name",  length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动细类名称 '")
  private String costTypeDetailName;

  /**
   * 组织编号
   */
  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  @Column(name = "org_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编号 '")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  @Column(name = "org_name",length = 128, columnDefinition = "VARCHAR(128) COMMENT '组织名称 '")
  private String orgName;


  /**
   * 客户编号
   */
  @ApiModelProperty(name = "客户编号", notes = "客户编号")
  @Column(name = "customer_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '客户编号 '")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty(name = "客户名称", notes = "客户名称")
  @Column(name = "customer_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '客户名称 '")
  private String customerName;

  /**
   * 门店编号
   */
  @ApiModelProperty(name = "门店编号", notes = "门店编号")
  @Column(name = "terminal_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '门店编号 '")
  private String terminalCode;

  /**
   * 门店名称
   */
  @ApiModelProperty(name = "门店名称", notes = "门店名称")
  @Column(name = "terminal_name",  length = 128, columnDefinition = "VARCHAR(128) COMMENT '门店名称 '")
  private String terminalName;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "申请金额", notes = "申请金额")
  @Column(name = "apply_amount", length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '申请金额 '")
  private BigDecimal applyAmount;

  /**
   * 预提金额
   */
  @ApiModelProperty(name = "预提金额", notes = "预提金额")
  @Column(name = "with_holding_amount", length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '预提金额 '")
  private BigDecimal withHoldingAmount;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "支付方式", notes = "支付方式")
  @Column(name = "pay_by", length = 64, columnDefinition = "VARCHAR(64) COMMENT '支付方式 '")
  private String payBy;



  @ApiModelProperty("费用归属年月")
  @Column(name = "years", columnDefinition = "varchar(20) comment '费用归属年月'")
  private String years;

  @ApiModelProperty("预提年月")
  @Column(name = "year_month_ly", columnDefinition = "varchar(20) comment '预提年月'")
  private String yearMonthLy;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
  private String erpCode;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
  private String channelCode;

  @ApiModelProperty("订单编码")
  @Column(name = "order_code", columnDefinition = "varchar(32) comment '订单编码'")
  private String orderCode;

  @ApiModelProperty("订单名称")
  @Column(name = "order_name", columnDefinition = "varchar(255) comment '订单名称'")
  private String orderName;

  @ApiModelProperty("归属部门编码")
  @Column(name = "belong_department_code", columnDefinition = "varchar(32) comment '归属部门编码'")
  private String belongDepartmentCode;

  @ApiModelProperty("归属部门名称")
  @Column(name = "belong_department_name", columnDefinition = "varchar(64) comment '归属部门名称'")
  private String belongDepartmentName;

  @ApiModelProperty("承担部门编码")
  @Column(name = "bear_department_code", columnDefinition = "varchar(32) comment '承担部门编码'")
  private String bearDepartmentCode;

  @ApiModelProperty("承担部门名称")
  @Column(name = "bear_department_name", columnDefinition = "varchar(64) comment '承担部门名称'")
  private String bearDepartmentName;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "varchar(64) comment '成本中心名称'")
  private String costCenterName;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("计提来源")
  @Column(name = "with_holding_source",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '计提来源 '")
  private String withHoldingSource;

  @ApiModelProperty("管报计提金额")
  @Column(name = "with_holding_report_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '管报计提金额 '")
  private BigDecimal withHoldingReportAmount;

  @ApiModelProperty("调减金额")
  @Column(name = "reduce_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '调减金额 '")
  private BigDecimal reduceAmount;

  @ApiModelProperty("实际金额")
  @Column(name = "actual_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '实际金额 '")
  private BigDecimal actualAmount;

  @ApiModelProperty("公式及取值")
  @Column(name = "formula_info", columnDefinition = "VARCHAR(255) COMMENT '公式及取值'")
  private String formulaInfo;

  @ApiModelProperty("凭证号")
  @Column(name = "voucher_code", length = 64, columnDefinition = " varchar(64) COMMENT '凭证号'")
  private String voucherCode;

  @ApiModelProperty("失败原因")
  @Column(name = "fail_msg", length = 500, columnDefinition = "varchar(500) COMMENT '失败原因'")
  private String failMsg;

  @ApiModelProperty("审批状态")
  @Column(name = "status",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '审批状态 '")
  private String status;

  @ApiModelProperty("推送日期")
  @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("审批流程编码")
  @Column(name = "process_key",  length = 32, columnDefinition = "VARCHAR(32) COMMENT '审批流程编码 '")
  private String processKey;

  @ApiModelProperty("预算编码")
  @Column(name = "budget_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '预算编码 '")
  private String budgetCode;

  @ApiModelProperty("冲销类型")
  @Column(name = "write_off_type",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '冲销类型 '")
  private String writeOffType;

  @ApiModelProperty("冲销金额")
  @Column(name = "write_off_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '冲销金额 '")
  private BigDecimal writeOffAmount;

  @ApiModelProperty(value = "管报冲销状态")
  @Column(name = "manage_report_write_off_status", length = 32, columnDefinition = "VARCHAR(32) DEFAULT 'wait_write_off' COMMENT '管报冲销状态'")
  private String manageReportWriteOffStatus;

  /** 冲销时间 */
  @ApiModelProperty(name = "writeOffTime",notes = "冲销时间", value= "冲销时间")
  @Column(name = "write_off_time",  columnDefinition = "DATETIME COMMENT '冲销时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date writeOffTime;

  @ApiModelProperty("冲销年月")
  @Column(name = "write_off_years",  length = 32, columnDefinition = "VARCHAR(32) COMMENT '冲销年月 '")
  private String writeOffYears;

  @ApiModelProperty("冲销编码")
  @Column(name = "write_off_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '冲销编码 '")
  private String writeOffCode;

  @ApiModelProperty("业务编码")
  @Column(name = "business_code", length = 64, columnDefinition = "varchar(64) COMMENT '业务编码'")
  private String businessCode;

  @ApiModelProperty("是否当期费用(Y/N)")
  @Column(name = "be_this_fee", length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否当期费用(Y/N) '")
  private String beThisFee;

  @ApiModelProperty("查看流程日志所需地址")
  @Column(name = "hec_receipt_url", columnDefinition = "VARCHAR(1024) COMMENT '查看流程日志所需地址 '")
  private String hecReceiptUrl;

  @ApiModelProperty("结案创建人员编码")
  @Column(name = "audit_create_account", columnDefinition = "VARCHAR(32) COMMENT '结案创建人员编码 '")
  private String auditCreateAccount;

  @ApiModelProperty("冲销来源单号")
  @Column(name = "source_code", columnDefinition = "VARCHAR(64) COMMENT '冲销来源单号 '")
  private String sourceCode;

  @ApiModelProperty("操作类型")
  @Transient
  @TableField(exist = false)
  private String hecOperationType;

  @ApiModelProperty("推送费控单号")
  @Transient
  @TableField(exist = false)
  private String pushHecCode;

  @ApiModelProperty("编码规则")
  @Column(name = "rule_code",columnDefinition = "varchar(32) comment '编码规则'")
  private String ruleCode;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("管报实际计提金额")
  private BigDecimal actualReportAmount;
}
