package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashInvoiceService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashInvoiceVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "费用兑付-发票明细功能接口")
@RestController
@RequestMapping("/v1/pay/feeCashInvoice")
@Slf4j
public class FeeCashInvoiceController extends BusinessPageCacheController<FeeCashInvoiceVo, FeeCashInvoiceDto> {

    @Autowired(required = false)
    private FeeCashInvoiceService feeCashInvoiceService;

    /**
     * 按结案明细更新关联的发票
     *
     * @param cacheKey 缓存key
     * @param itemList 要保存的当前页数据
     * @return
     */
    @ApiOperation(value = "按结案明细更新关联的发票")
    @PostMapping("updateItemCache")
    public Result<?> updateItemCache(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                     @ApiParam(name = "auditDetailCodes", value = "结案明细编码集合") @RequestParam List<String> auditDetailCodes,
                                         @ApiParam(value = "当前页数据") @RequestBody List<FeeCashInvoiceDto> itemList) {
        try {
            this.feeCashInvoiceService.updateItemCache(cacheKey, auditDetailCodes, itemList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 发票金额汇总
     *
     * @param cacheKey 缓存key
     * @return
     */
    @ApiOperation(value = "发票金额汇总")
    @GetMapping("invoiceTotal")
    public Result<FeeCashInvoiceVo> invoiceTotal(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey) {
        try {
            return Result.ok(this.feeCashInvoiceService.invoiceTotal(cacheKey));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 查询剩余可报销金额
     *
     * @param cacheKey 缓存key
     * @return
     */
    @ApiOperation(value = "查询剩余可报销金额")
    @GetMapping("availableReimbursementAmount")
    public Result<List<FeeCashInvoiceDto>> availableReimbursementAmount(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                                                        @ApiParam(name = "auditDetailCode", value = "结案明细编码") @RequestParam String auditDetailCode) {
        try {
            return Result.ok(this.feeCashInvoiceService.availableReimbursementAmount(cacheKey, auditDetailCode));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
