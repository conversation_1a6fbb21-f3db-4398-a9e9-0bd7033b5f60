<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.DeliveryReplenishmentPoolDetailMapper">

    <select id="findBackUnWriteOff"
            resultType="com.biz.crm.tpm.business.pay.local.entity.DeliveryReplenishmentPoolDetail">
        select t.*
        from tpm_delivery_replenishment_pool_detail t
        left join tpm_marketing_plan_case m on t.scheme_detail_code = m.scheme_detail_code
        <where>
            t.del_flag = '${@<EMAIL>()}'
            and t.enable_status = '${@<EMAIL>()}'
            and m.case_type = 'BDMB0006'
            and t.manage_report_write_off_status = 'wait_write_off'
            and t.operation_type = 'audit'
            and t.delivery_time like concat(#{month},'%')
        </where>
    </select>
</mapper>

