package com.biz.crm.tpm.business.pay.local.notifier.log;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.MarketingAuditDetailLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.event.log.MarketingAuditDetailLogEventListener;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class MarketingAuditDetailLogEventListenerImpl implements MarketingAuditDetailLogEventListener {

    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 更新日志
     *
     * @param eventDto
     */
    @Override
    public void onUpdate(MarketingAuditDetailLogEventDto eventDto) {
        List<MarketingAuditDetailDto> newList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(eventDto.getNewestList())) {
            newList.addAll(eventDto.getNewestList());
        }
        Map<String, MarketingAuditDetailDto> oldMap = eventDto.getOriginalList().stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(MarketingAuditDetailDto::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            MarketingAuditDetailDto original = oldMap.getOrDefault(newest.getId(), new MarketingAuditDetailDto());
            crmBusinessLogDto.setOldObject(original);
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }
}
