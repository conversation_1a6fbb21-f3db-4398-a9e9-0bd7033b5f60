package com.biz.crm.tpm.business.pay.local.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.Account;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 费用上账主表(account)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
public interface AccountService {

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param account  实体对象
   * @return
   */
  Page<Account> findByConditions(Pageable pageable, AccountDto account);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  Account findById(String id);

  /**
   * 通过主键集合查询多条数据
   *
   * @param ids 主键
   * @return 单条数据
   */
  List<Account> findByIds(List<String> ids);

  /**
   * 通过活动明细集合查询多条数据
   *
   * @param codes 主键
   * @return 单条数据
   */
  List<Account> findByActivitiesDetailCode(List<String> codes);

  /**
   * 新增数据
   *
   * @param account 实体对象
   * @return 新增结果
   */
  Account create(Account account);

  /**
   * 批量新增数据
   * @param accounts
   */
  void createBatch(List<Account> accounts);
  /**
   * 修改新据
   *
   * @param account 实体对象
   * @return 修改结果
   */
  Account update(Account account);

  /**
   * 批量逻辑删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  void deleteBatch(List<String> idList);

}

