package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordDto;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayVo;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动预付明细跟踪(ActivityPrepayDetailRecord)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-05 15:40:38
 */
public interface ActivityPrepayDetailRecordMapper extends BaseMapper<ActivityPrepayDetailRecord> {


    Page<ActivityPrepayDetailRecordVo> findListByCondition(Page<ActivityPrepayDetailRecordVo> page, @Param("dto") ActivityPrepayDetailRecordDto dto);

    /**
     * 根据收款方+公司查询关联结转金额，倒序返回
     *
     * @param vo
     * @return
     */
    List<ActivityPrepayDetailRecord> findCalList(@Param("vo") ActivityPrepayVo vo);

    /**
     * 更新付款状态
     *
     * @param dtoList
     */
    void updatePayStatus(@Param("dtoList") List<HecCallbackDto> dtoList);

    List<MarketingAuditDetailVo> findAuditedAmountBySchemeDetailCodes(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    Page<ActivityPrepayDetailRecordVo> findByPayeeCode(Page<ActivityPrepayDetailRecordVo> page, @Param("dto") ActivityPrepayDetailRecordDto dto);

    Page<ActivityPrepayDetailRecordVo> findByPayeeCode2(Page<ActivityPrepayDetailRecordVo> page, @Param("dto") ActivityPrepayDetailRecordDto dto);
    List<ActivityPrepayDetailRecord> findListBySchemeDetailCodes(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    List<ActivityPrepayDetailRecord> findListByAuditDetailCodes(@Param("auditDetailCodes") List<String> auditDetailCodes);


    List<ActivityPrepayDetail> findAlreadySchemeDetailCodeByCondition(@Param("prepayCode") String prepayCode, @Param("schemeDetailCodes") List<String> schemeDetailCodes);


    List<ActivityPrepayDetailRecord> findReportBySchemeDetailCodes(@Param("schemeDetailCodes")List<String> schemeDetailCodes);


    List<ActivityPrepayDetailRecord> findO2OPrepayDetailRecord(@Param("schemeDetailCodes")List<String> schemeDetailCodes);
}

