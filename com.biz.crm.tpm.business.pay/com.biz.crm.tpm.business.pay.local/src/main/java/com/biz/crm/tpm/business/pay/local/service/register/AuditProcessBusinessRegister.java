package com.biz.crm.tpm.business.pay.local.service.register;

import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.workflow.sdk.register.ProcessBusinessRegister;
import org.springframework.stereotype.Component;

/**
 * 核销申请工作流注册业务编码
 *
 * <AUTHOR>
 * @date 2022/9/5
 */
@Component
public class AuditProcessBusinessRegister implements ProcessBusinessRegister {
  @Override
  public String getBusinessCode() {
    return PayConstant.PROCESS_AUDIT_ACTIVITIES;
  }

  @Override
  public String getBusinessName() {
    return "核销申请";
  }
}
