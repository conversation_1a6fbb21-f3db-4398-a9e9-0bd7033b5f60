package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.eunm.AuditStatusEnum;
import com.biz.crm.business.common.base.eunm.CashStatusEnum;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.ReplenishmentPoolDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolOperationType;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolTypeEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.renyang.ReplenishmentPoolDetailVoService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.renyang.ReplenishmentPoolLockService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.renyang.ReplenishmentPoolVoService;
import com.biz.crm.dms.business.order.common.sdk.enums.OrderStatusEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsPolicyProductDeliveryVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerDockingVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.common.enums.TicketCashTypeEnum;
import com.biz.crm.mdm.business.org.sdk.dto.OrgQueryDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.user.sdk.service.UserInfoVoService;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserInfoVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCaseExtend;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseExtendRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.budget.sdk.dto.OperateBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetOperateType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetLockVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetOperateVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.local.calcomponent.CalCloseReversComponent;
import com.biz.crm.tpm.business.pay.local.entity.*;
import com.biz.crm.tpm.business.pay.local.repository.*;
import com.biz.crm.tpm.business.pay.local.service.AuditFilesService;
import com.biz.crm.tpm.business.pay.local.service.MarketingAuditInvoiceSapService;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.constant.DeliveryReplenishmentConstant;
import com.biz.crm.tpm.business.pay.sdk.constant.MarketingAuditConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.*;
import com.biz.crm.tpm.business.pay.sdk.dto.log.MarketingAuditDetailLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.MarketingAuditLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.enums.*;
import com.biz.crm.tpm.business.pay.sdk.event.log.MarketingAuditDetailLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.event.log.MarketingAuditLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.*;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.biz.crm.workflow.sdk.dto.OaCallbackDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.biz.crm.workflow.sdk.vo.oa.RyOaMsgVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 方案结案
 */
@Slf4j
@Service
public class MarketingAuditServiceImpl extends BusinessPageCacheServiceImpl<MarketingAuditDetailVo, MarketingAuditDetailDto> implements MarketingAuditService {

    @Autowired(required = false)
    private MarketingAuditRepository marketingAuditRepository;
    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;
    @Autowired(required = false)
    private MarketingAuditBillDetailRepository marketingAuditBillDetailRepository;
    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;
    @Autowired(required = false)
    private WithHoldingService withHoldingService;
    @Autowired(required = false)
    private AuditFilesService auditFilesService;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;
    @Autowired(required = false)
    private ReplenishmentPoolDetailVoService replenishmentPoolDetailVoService;
    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;
    @Autowired(required = false)
    private MarketingPlanRepository marketingPlanRepository;
    @Autowired(required = false)
    private CostBudgetLockVoService costBudgetLockVoService;
    @Autowired(required = false)
    private CostBudgetOperateVoService costBudgetOperateVoService;
    @Autowired(required = false)
    private ReplenishmentPoolVoService replenishmentPoolVoService;
    @Autowired(required = false)
    private CustomerVoService customerVoService;
    @Autowired(required = false)
    private FeeCashService feeCashService;
    @Autowired(required = false)
    private FeeCashDetailService feeCashDetailService;
    @Autowired(required = false)
    private FeeCashDetailRepository feeCashDetailRepository;
    @Autowired(required = false)
    private CostTypeDetailVoService costTypeDetailVoService;
    @Autowired(required = false)
    private MarketingAuditOaService marketingAuditOaService;
    @Autowired(required = false)
    private WithHoldingWriteOffSendHecService withHoldingWriteOffSendHecService;
    @Autowired(required = false)
    private ReplenishmentPoolLockService replenishmentPoolLockService;
    @Autowired(required = false)
    private CreditOrderService creditOrderService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private ActivityPrepayDetailRecordRepository activityPrepayDetailRecordRepository;
    @Resource
    private ActivityPrepayDetailRecordService activityPrepayDetailRecordService;
    @Resource
    private ProductPhaseVoService productPhaseVoService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Autowired(required = false)
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;
    @Autowired(required = false)
    private OrgVoService orgVoService;
    @Autowired(required = false)
    private ProductVoService productVoService;
    @Autowired(required = false)
    private MarketingAuditInvoiceSapService marketingAuditInvoiceSapService;
    @Autowired(required = false)
    private MarketingPlanCaseRepository planCaseRepository;

    @Autowired
    private UserInfoVoService userInfoVoService;

    @Value("${oa.msgSource:}")
    private String msgSource;

    @Autowired(required = false)
    private RedisService redisService;

    @Autowired(required = false)
    private RedisTemplate redisTemplate;
    @Autowired
    private MarketingPlanCaseExtendRepository marketingPlanCaseExtendRepository;

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    @Override
    public MarketingAuditVo findByCode(String code) {
        MarketingAudit audit = marketingAuditRepository.findByCode(code);
        Validate.notNull(audit, "未找到对应的数据");
        MarketingAuditVo marketingAuditVo = nebulaToolkitService.copyObjectByWhiteList(audit, MarketingAuditVo.class, LinkedHashSet.class, ArrayList.class);
        List<MarketingAuditBillDetailVo> billDetailVos = marketingAuditBillDetailRepository.findByCode(code);
        List<AuditFilesVo> filesVos = auditFilesService.findByAuditCode(code);
        marketingAuditVo.setAuditBills(billDetailVos);
        marketingAuditVo.setAuditFiles(filesVos);
        return marketingAuditVo;
    }

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> create(MarketingAuditDto dto, String cacheKey, boolean submit) {
        List<MarketingAuditDetailDto> cacheList = findCacheList(cacheKey);
        dto.setCacheList(cacheList);
        createValidate(dto);
        List<MarketingAuditBillDetailVo> billDetailVoList = generateBillByAuditDetail(cacheList);
        dto.setAuditBillDetails(billDetailVoList);
        List<String> codes = store(Arrays.asList(dto));

        //新增业务日志
        MarketingAuditLogEventDto logEventDto = new MarketingAuditLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(dto);
        SerializableBiConsumer<MarketingAuditLogEventListener, MarketingAuditLogEventDto> onCreate =
                MarketingAuditLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, MarketingAuditLogEventListener.class, onCreate);

        if (!submit) {
            clearCache(cacheKey);
        }
        return codes;
    }

    /**
     * 修改新据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(MarketingAuditDto dto, String cacheKey, boolean submit) {
        List<MarketingAuditDetailDto> cacheList = findCacheList(cacheKey);
        dto.setCacheList(cacheList);
        updateValidate(dto);
        MarketingAudit entityOld = marketingAuditRepository.findByCode(dto.getAuditCode());
        Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(entityOld.getStatus()) || ProcessStatusEnum.PREPARE.getDictCode().equals(entityOld.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(entityOld.getStatus()),
                "只有【待提交】、【驳回】、【追回】核销数据能修改");
        List<MarketingAuditBillDetailVo> billDetailVoList = generateBillByAuditDetail(cacheList);
        dto.setAuditBillDetails(billDetailVoList);
        store(Arrays.asList(dto));

        //编辑业务日志
        MarketingAuditLogEventDto logEventDto = new MarketingAuditLogEventDto();
        logEventDto.setOriginal(this.nebulaToolkitService.copyObjectByWhiteList(entityOld, MarketingAuditDto.class, LinkedHashSet.class, ArrayList.class));
        logEventDto.setNewest(dto);
        SerializableBiConsumer<MarketingAuditLogEventListener, MarketingAuditLogEventDto> onUpdate =
                MarketingAuditLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, MarketingAuditLogEventListener.class, onUpdate);

        if (!submit) {
            clearCache(cacheKey);
        }
    }

    /**
     * 新增保存并提交
     *
     * @param dto      实体对象
     * @param cacheKey
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitCreate(MarketingAuditDto dto, String cacheKey) {
        List<String> codes = create(dto, cacheKey, true);

        //活动在计提过程中时不允许提交
        List<MarketingAuditDetailDto> list = dto.getCacheList();
        List<String> schemeDetailCodes = list.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findBySchemeDetailCodes(schemeDetailCodes);
        //获取最新的活动申请金额
        List<MarketingPlanCaseVo> caseVos = marketingPlanCaseService.findByCaseCodes(schemeDetailCodes);
        Map<String, MarketingPlanCaseVo> caseVoMap = caseVos.stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));
        if (!CollectionUtils.isEmpty(withHoldingVos)) {
            List<WithHoldingVo> withHoldingVosNotPass = withHoldingVos.stream().filter(e -> !ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus())).collect(Collectors.toList());
            Validate.isTrue(CollectionUtils.isEmpty(withHoldingVosNotPass), "活动明细【%s】在计提过程中，不允许提交", String.join("；", withHoldingVosNotPass.stream().map(e -> e.getBusinessCode()).collect(Collectors.toList())));

            //结案提交时还需要再判断一次规划金额（计提/申请）与已结案金额的对比
            List<WithHoldingVo> withHoldingVosPass = withHoldingVos.stream().filter(e -> ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(withHoldingVosPass)) {
                Map<String, WithHoldingVo> withHoldingVoMap = withHoldingVosPass.stream().collect(Collectors.toMap(e -> e.getActivitiesDetailCode(), Function.identity(), (a, b) -> a));
                list.forEach(e -> {
                    WithHoldingVo withHoldingVo = withHoldingVoMap.get(e.getSchemeDetailCode());
                    e.setApplyAmount(withHoldingVo == null ? caseVoMap.get(e.getSchemeDetailCode()).getApplyAmount() : withHoldingVo.getActualAmount());
                });
            } else {
                list.forEach(e -> {
                    MarketingPlanCaseVo planCaseVo = caseVoMap.get(e.getSchemeDetailCode());
                    e.setApplyAmount(planCaseVo.getApplyAmount());
                });
            }
            List<String> errorSchemeDetailCodes = new ArrayList<>();
            list.forEach(e -> {
                //本次结案金额+已结案金额
                BigDecimal decimal = e.getAuditAmount().add(Optional.ofNullable(e.getAuditedAmount()).orElse(BigDecimal.ZERO));
                if (decimal.compareTo(e.getApplyAmount()) > 0) {
                    errorSchemeDetailCodes.add(e.getSchemeDetailCode());
                }
                //1045914 【方案结案】已结案金额+本次结案金额等于规划费用时，应校验“是否完全结案”必须为“是”
                //1050286 【方案结案】增加校验：本次结案金额=0时，是否完全结案必须选择“是”
                if (decimal.compareTo(e.getApplyAmount()) == 0 || BigDecimal.ZERO.compareTo(e.getAuditAmount()) == 0) {
                    Validate.isTrue(BooleanEnum.TRUE.getCapital().equals(e.getBeFullAudit()), "本次结案金额+已结案金额" +
                            "等于规划金额时/本次结案金额=0时,是否完全结案必须为是！");
                }
            });
//            Validate.isTrue(CollectionUtils.isEmpty(errorSchemeDetailCodes), "活动【%s】的本次结案金额+已结案金额不能大于申请/计提费用+计提前兑付金额，请调整该活动的“本次结案金额”！", String.join(",", errorSchemeDetailCodes));
        }
        //活动在变更过程中时不允许提交
        List<MarketingPlanCaseVo> changing = marketingPlanCaseService.findChanging(schemeDetailCodes);
        Validate.isTrue(CollectionUtils.isEmpty(changing), "活动在变更过程中时不允许提交");

        //票扣校验
        Set<String> belongDepartmentCodeList = list.stream().map(e -> e.getBelongDepartmentCode()).collect(Collectors.toSet());
        Map<String, String> orgMap = orgVoService.findByOrgCodes(new ArrayList<>(belongDepartmentCodeList)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), e -> Optional.ofNullable(e.getTicketCashType()).orElse(""), (a, b) -> a));
        List<MarketingAuditDetailDto> ticketList = list.stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode()) && CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(e.getCashType()) &&
                ("1".equals(e.getHzlx()) || "2".equals(e.getHzlx())) &&
                TicketCashTypeEnum.AUTO.getCode().equals(orgMap.get(e.getBelongDepartmentCode()))).collect(Collectors.toList());
        Set<String> customerCodeSet = ticketList.stream().filter(Objects::nonNull).map(e -> e.getCustomerCode()).collect(Collectors.toSet());
        //“经销商”或“直采”的客户，且兑付方式为“票扣”，需要校验票扣
        if (!CollectionUtils.isEmpty(customerCodeSet)) {
            List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodeSet)).stream().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(customerVos)) {
                if (!CollectionUtils.isEmpty(ticketList)) {
                    //如果结案金额=0，不控制当前客户的票扣明细不能为空
                    BigDecimal auditAmount = ticketList.stream().map(e -> e.getAuditAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (auditAmount.compareTo(BigDecimal.ZERO) != 0) {
                        Validate.notEmpty(dto.getAuditBillDetails(), "经销商、直采类客户票扣费用结案时，票扣明细不允许为空");
                        dto.getAuditBillDetails().forEach(e -> Validate.isTrue(e.getCheckFlag(), "票扣折让率校验未通过"));
                        //校验是否全部经销商或直采客户的票扣活动都生成了票扣明细
                        Map<String, List<MarketingAuditBillDetailVo>> billMap = dto.getAuditBillDetails().stream().collect(Collectors.groupingBy(e -> e.getCompanyCode() + "," + e.getCustomerCode() + "," + e.getYears()));
                        Map<String, List<MarketingAuditDetailDto>> detialMap = ticketList.stream().collect(Collectors.groupingBy(e -> e.getCompanyCode() + "," + e.getCustomerCode() + "," + e.getStartDate().substring(0, 7)));
                        if (billMap.size() != detialMap.size()) {
                            Set<String> billSet = billMap.keySet();
                            Set<String> detailSet = detialMap.keySet();
                            detailSet.removeAll(billSet);
                            List<String> detailSetName = detialMap.entrySet().stream().filter(e -> detailSet.contains(e.getKey()))
                                    .map(e -> e.getValue().get(0).getCompanyCode() + "+" + e.getValue().get(0).getCustomerName() + "+" + e.getValue().get(0).getStartDate().substring(0, 7)).collect(Collectors.toList());
                            Validate.isTrue(false, "公司+客户+年月【%s】没有生成票扣明细", String.join("；", detailSetName));
                        }
                        billMap.forEach((k, v) -> {
                            List<MarketingAuditDetailDto> detailVos = detialMap.get(k);
                            BigDecimal auditAmountGroup = detailVos.stream().map(e -> e.getAuditAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal amount = v.stream().map(e -> e.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                            Validate.isTrue(auditAmountGroup.compareTo(amount) == 0, "公司+客户+年月【%s】维度下结案金额汇总与对应维度下的票扣明细订单金额汇总不相等，请检查数据",
                                    v.get(0).getCompanyCode() + "+" + v.get(0).getCustomerName() + "+" + v.get(0).getYears());
                        });
                    }
                }
            }
        }

        marketingAuditOaService.pushOa(codes.get(0));

        clearCache(cacheKey);
    }

    /**
     * 编辑保存并提交
     *
     * @param dto      实体对象
     * @param cacheKey
     * @return 修改结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitUpdate(MarketingAuditDto dto, String cacheKey) {
        String status = marketingAuditRepository.findByCode(dto.getAuditCode()).getStatus();
        update(dto, cacheKey, true);

        //活动在计提过程中时不允许提交
        List<MarketingAuditDetailDto> list = dto.getCacheList();
        List<String> schemeDetailCodes = list.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findBySchemeDetailCodes(schemeDetailCodes);
        //获取最新的活动申请金额
        List<MarketingPlanCaseVo> caseVos = marketingPlanCaseService.findByCaseCodes(schemeDetailCodes);
        Map<String, MarketingPlanCaseVo> caseVoMap = caseVos.stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));
        if (!CollectionUtils.isEmpty(withHoldingVos)) {
            List<WithHoldingVo> withHoldingVosNotPass = withHoldingVos.stream().filter(e -> !ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus())).collect(Collectors.toList());
            Validate.isTrue(CollectionUtils.isEmpty(withHoldingVosNotPass), "活动明细【%s】在计提过程中，不允许提交", String.join("；", withHoldingVosNotPass.stream().map(e -> e.getBusinessCode()).collect(Collectors.toList())));

            //结案提交时还需要再判断一次规划金额（计提/申请）与已结案金额的对比
            List<WithHoldingVo> withHoldingVosPass = withHoldingVos.stream().filter(e -> ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(withHoldingVosPass)) {
                Map<String, WithHoldingVo> withHoldingVoMap = withHoldingVosPass.stream().collect(Collectors.toMap(e -> e.getActivitiesDetailCode(), Function.identity(), (a, b) -> a));
                list.forEach(e -> {
                    WithHoldingVo withHoldingVo = withHoldingVoMap.get(e.getSchemeDetailCode());
                    e.setApplyAmount(withHoldingVo == null ? caseVoMap.get(e.getSchemeDetailCode()).getApplyAmount() : withHoldingVo.getActualAmount());
                });
            } else {
                list.forEach(e -> {
                    MarketingPlanCaseVo planCaseVo = caseVoMap.get(e.getSchemeDetailCode());
                    e.setApplyAmount(planCaseVo.getApplyAmount());
                });
            }
            List<String> errorSchemeDetailCodes = new ArrayList<>();
            list.forEach(e -> {
                //本次结案金额+已结案金额
                BigDecimal decimal = e.getAuditAmount().add(Optional.ofNullable(e.getAuditedAmount()).orElse(BigDecimal.ZERO));
                if (decimal.compareTo(e.getApplyAmount()) > 0) {
                    errorSchemeDetailCodes.add(e.getSchemeDetailCode());
                }
                //1045914 【方案结案】已结案金额+本次结案金额等于规划费用时，应校验“是否完全结案”必须为“是”
                //1050286 【方案结案】增加校验：本次结案金额=0时，是否完全结案必须选择“是”
                if (decimal.compareTo(e.getApplyAmount()) == 0 || BigDecimal.ZERO.compareTo(e.getAuditAmount()) == 0) {
                    Validate.isTrue(BooleanEnum.TRUE.getCapital().equals(e.getBeFullAudit()), "本次结案金额+已结案金额" +
                            "等于规划金额时/本次结案金额=0时,是否完全结案必须为是！");
                }
            });
//            Validate.isTrue(CollectionUtils.isEmpty(errorSchemeDetailCodes), "活动【%s】的本次结案金额+已结案金额不能大于申请/计提费用+计提前兑付金额，请调整该活动的“本次结案金额”！", String.join(",", errorSchemeDetailCodes));
        }
        //活动在变更过程中时不允许提交
        List<MarketingPlanCaseVo> changing = marketingPlanCaseService.findChanging(schemeDetailCodes);
        Validate.isTrue(CollectionUtils.isEmpty(changing), "活动在变更过程中时不允许提交");


        //票扣校验
        Set<String> belongDepartmentCodeList = list.stream().map(e -> e.getBelongDepartmentCode()).collect(Collectors.toSet());
        Map<String, String> orgMap = orgVoService.findByOrgCodes(new ArrayList<>(belongDepartmentCodeList)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), e -> Optional.ofNullable(e.getTicketCashType()).orElse(""), (a, b) -> a));
        List<MarketingAuditDetailDto> ticketList = list.stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode()) && CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(e.getCashType()) &&
                ("1".equals(e.getHzlx()) || "2".equals(e.getHzlx())) &&
                TicketCashTypeEnum.AUTO.getCode().equals(orgMap.get(e.getBelongDepartmentCode()))).collect(Collectors.toList());
        Set<String> customerCodeSet = ticketList.stream().filter(Objects::nonNull).map(e -> e.getCustomerCode()).collect(Collectors.toSet());
        //“经销商”或“直采”的客户，且兑付方式为“票扣”，需要校验票扣
        if (!CollectionUtils.isEmpty(customerCodeSet)) {
            List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodeSet)).stream().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(customerVos)) {
                if (!CollectionUtils.isEmpty(ticketList)) {
                    //如果结案金额=0，不控制当前客户的票扣明细不能为空
                    BigDecimal auditAmount = ticketList.stream().map(e -> e.getAuditAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (auditAmount.compareTo(BigDecimal.ZERO) != 0) {
                        Validate.notEmpty(dto.getAuditBillDetails(), "经销商、直采类客户票扣费用结案时，票扣明细不允许为空");
                        dto.getAuditBillDetails().forEach(e -> Validate.isTrue(e.getCheckFlag(), "票扣折让率校验未通过"));
                        //校验是否全部经销商或直采客户的票扣活动都生成了票扣明细
                        Map<String, List<MarketingAuditBillDetailVo>> billMap = dto.getAuditBillDetails().stream().collect(Collectors.groupingBy(e -> e.getCompanyCode() + "," + e.getCustomerCode() + "," + e.getYears()));
                        Map<String, List<MarketingAuditDetailDto>> detialMap = ticketList.stream().collect(Collectors.groupingBy(e -> e.getCompanyCode() + "," + e.getCustomerCode() + "," + e.getStartDate().substring(0, 7)));
                        if (billMap.size() != detialMap.size()) {
                            Set<String> billSet = billMap.keySet();
                            Set<String> detailSet = detialMap.keySet();
                            detailSet.removeAll(billSet);
                            List<String> detailSetName = detialMap.entrySet().stream().filter(e -> detailSet.contains(e.getKey()))
                                    .map(e -> e.getValue().get(0).getCompanyCode() + "+" + e.getValue().get(0).getCustomerName() + "+" + e.getValue().get(0).getStartDate().substring(0, 7)).collect(Collectors.toList());
                            Validate.isTrue(false, "公司+客户+年月【%s】没有生成票扣明细", String.join("；", detailSetName));
                        }
                        billMap.forEach((k, v) -> {
                            List<MarketingAuditDetailDto> detailVos = detialMap.get(k);
                            BigDecimal auditAmountGroup = detailVos.stream().map(e -> e.getAuditAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal amount = v.stream().map(e -> e.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                            Validate.isTrue(auditAmountGroup.compareTo(amount) == 0, "公司+客户+年月【%s】维度下结案金额汇总与对应维度下的票扣明细订单金额汇总不相等，请检查数据",
                                    v.get(0).getCompanyCode() + "+" + v.get(0).getCustomerName() + "+" + v.get(0).getYears());
                        });
                    }
                }
            }
        }

        if (ProcessStatusEnum.PREPARE.getDictCode().equals(status)) {
            marketingAuditOaService.pushOa(dto.getAuditCode());
        } else {
            marketingAuditOaService.resubmitOa(dto.getAuditCode());
        }

        clearCache(cacheKey);
    }

    /**
     * 批量选择提交
     *
     * @param dto
     * @param cacheKey
     * @return
     */
    @Override
    public void batchSelect(MarketingPlanCaseVo dto, String cacheKey) {
        StopWatch stopWatch=new StopWatch("方案结案批量选择提交");
        dto.setExcludeWholeAudit(BooleanEnum.TRUE.getCapital());
        dto.setChangeFlag(BooleanEnum.FALSE.getCapital());
        stopWatch.start("不分页查找case");
        Page<MarketingPlanCaseVo> caseVoPage = marketingPlanCaseService.findCaseListByWithholdingEndCase(PageRequest.of(0, Integer.MAX_VALUE), dto, ProcessStatusEnum.PASS.getDictCode());
        stopWatch.stop();
        if (caseVoPage.getTotal() > 0) {
            List<MarketingPlanCaseVo> records = caseVoPage.getRecords();
            List<MarketingAuditDetailDto> details = (List<MarketingAuditDetailDto>) nebulaToolkitService.copyCollectionByWhiteList(records, MarketingPlanCaseVo.class, MarketingAuditDetailDto.class, LinkedHashSet.class, ArrayList.class);
            List<String> schemeDetailCodes = details.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
            stopWatch.start("查询计提");
            Map<String, WithHoldingVo> withHoldingVoMap = withHoldingRepository.findBySchemeDetailCodesPass(schemeDetailCodes).stream().filter(Objects::nonNull).collect(Collectors.toMap(e -> e.getActivitiesDetailCode(), Function.identity(), (a, b) -> a));
            stopWatch.stop();
            details.forEach(e -> {
                e.setChecked(BooleanEnum.TRUE.getNumStr());
                e.setAuditAmount(null);
                if (MapUtils.isNotEmpty(withHoldingVoMap) && withHoldingVoMap.containsKey(e.getSchemeDetailCode())) {
                    e.setApplyAmount(withHoldingVoMap.get(e.getSchemeDetailCode()).getActualAmount());
                }
                if (e.getAvailableAuditAmount() != null) {
                    e.setAuditAmount(e.getAvailableAuditAmount());
                }
            });
            stopWatch.start("缓存相关");
            clearCache(cacheKey);
            copyItemListCache(cacheKey, details);
            stopWatch.stop();
            log.info("批量选择总共时间 {}",stopWatch.prettyPrint(TimeUnit.SECONDS));
        }
    }

    /**
     * 保存
     *
     * @param dtoList
     */
    public List<String> store(List<MarketingAuditDto> dtoList) {
        List<MarketingAuditBillDetailVo> auditBillDetails = new ArrayList<>();
        List<MarketingAuditDetailDto> cacheList = new ArrayList<>();
        List<MarketingAuditDto> newDtoList = dtoList.stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());
        List<String> codes;
        if (!CollectionUtils.isEmpty(newDtoList)) {
            codes = generateCodeService.generateCode(MarketingAuditConstant.PREFIX_CODE, newDtoList.size());
        } else {
            codes = dtoList.stream().map(e -> e.getAuditCode()).collect(Collectors.toList());
        }
        int sum = dtoList.stream().mapToInt(e -> e.getCacheList().size()).sum();
        List<String> detailCodes = generateCodeService.generateCode(MarketingAuditConstant.PREFIX_CODE_MX, sum);
        AtomicInteger index = new AtomicInteger(0);
        AtomicInteger indexDetail = new AtomicInteger(0);
        List<String> finalCodes = codes;
        dtoList.forEach(dto -> {
            if (StringUtils.isBlank(dto.getId())) {
                dto.setAuditCode(finalCodes.get(index.get()));
                dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                dto.setAuditStatus(StringUtils.isBlank(dto.getAuditStatus()) ? AuditStatusEnum.NOT_AUDIT.getCode() : dto.getAuditStatus());
                dto.setStatus(StringUtils.isBlank(dto.getStatus()) ? ProcessStatusEnum.PREPARE.getDictCode() : dto.getStatus());
                dto.setTenantCode(TenantUtils.getTenantCode());
                index.getAndAdd(1);
            } else {
                marketingAuditDetailRepository.deleteByCodes(Arrays.asList(dto.getAuditCode()));
                marketingAuditBillDetailRepository.deleteByCodes(Arrays.asList(dto.getAuditCode()));
                auditFilesService.deleteByAuditCode(Arrays.asList(dto.getAuditCode()));
            }
            dto.getAuditBillDetails().forEach(e -> {
                e.setAuditCode(dto.getAuditCode());
                e.setAuditName(dto.getAuditName());
            });
            AtomicReference<BigDecimal> totalApplyAmount = new AtomicReference<>(BigDecimal.ZERO);
            dto.getCacheList().forEach(e -> {
                e.setOrgCode(dto.getOrgCode());
                e.setOrgName(dto.getOrgName());
                e.setPositionCode(dto.getPositionCode());
                e.setAuditDetailCode(detailCodes.get(indexDetail.get()));
                e.setAuditCode(dto.getAuditCode());
                e.setAuditName(dto.getAuditName());
                e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                e.setTenantCode(TenantUtils.getTenantCode());
                totalApplyAmount.set(totalApplyAmount.get().add(e.getAuditAmount()));
                indexDetail.getAndAdd(1);
            });
            if (!CollectionUtils.isEmpty(dto.getAuditFiles())) {
                dto.getAuditFiles().forEach(e -> {
                    e.setAuditCode(dto.getAuditCode());
                    e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    e.setTenantCode(TenantUtils.getTenantCode());
                });
                auditFilesService.createBatch(dto.getAuditFiles());
            }
            dto.setTotalApplyAmount(totalApplyAmount.get());

            auditBillDetails.addAll(dto.getAuditBillDetails());
            cacheList.addAll(dto.getCacheList());
        });

        Collection<MarketingAudit> marketingAuditList = nebulaToolkitService.copyCollectionByWhiteList(dtoList, MarketingAuditDto.class, MarketingAudit.class, LinkedHashSet.class, ArrayList.class);
        Collection<MarketingAuditBillDetail> marketingAuditBillDetails = nebulaToolkitService.copyCollectionByWhiteList(auditBillDetails, MarketingAuditBillDetailVo.class, MarketingAuditBillDetail.class, LinkedHashSet.class, ArrayList.class);
        Collection<MarketingAuditDetail> marketingAuditDetails = nebulaToolkitService.copyCollectionByWhiteList(cacheList, MarketingAuditDetailDto.class, MarketingAuditDetail.class, LinkedHashSet.class, ArrayList.class);
        marketingAuditBillDetailRepository.saveBatch(marketingAuditBillDetails);
        marketingAuditDetailRepository.saveBatch(marketingAuditDetails);
        marketingAuditRepository.saveOrUpdateBatch(marketingAuditList);

        return codes;
    }

    /**
     * 创建验证
     *
     * @param auditDto
     */
    private void createValidate(MarketingAuditDto auditDto) {
        Validate.notNull(auditDto, "新增时，对象信息不能为空！");
        auditDto.setId(null);
        Validate.notBlank(auditDto.getAuditName(), "新增数据时，核销申请名称不能为空！");
        if (auditDto.getAuditFiles() == null) {
            auditDto.setAuditFiles(Lists.newArrayList());
        }
        if (StringUtils.isBlank(auditDto.getOrgCode()) || StringUtils.isBlank(auditDto.getPositionCode())) {
            FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
            Validate.notNull(loginUserDetails, "未找到当前登陆人信息！");
            Validate.notBlank(loginUserDetails.getOrgCode(), "未找到当前登陆人的组织信息！");
            Validate.notBlank(loginUserDetails.getPostCode(), "未找到当前登陆人的职位信息！");
            auditDto.setOrgCode(loginUserDetails.getOrgCode());
            auditDto.setOrgName(loginUserDetails.getOrgName());
            auditDto.setPositionCode(loginUserDetails.getPostCode());
        }
        commonValidate(auditDto);
    }

    /**
     * 修改验证
     *
     * @param auditDto
     */
    private void updateValidate(MarketingAuditDto auditDto) {
        Validate.notNull(auditDto, "修改时，对象信息不能为空！");
        Validate.notBlank(auditDto.getId(), "修改数据时，核销主键不能为空！");
        Validate.notBlank(auditDto.getAuditName(), "修改数据时，核销申请名称不能为空！");
        Validate.notBlank(auditDto.getAuditCode(), "修改数据时，核销申请编号不能为空！");
        commonValidate(auditDto);
    }

    /**
     * 公有验证
     *
     * @param auditDto
     */
    private void commonValidate(MarketingAuditDto auditDto) {
        Validate.isTrue(auditDto.getAuditName().length() <= 40, "核销申请名称需控制在40字符内");
        if (StringUtils.isNotBlank(auditDto.getRemark())) {
            Validate.isTrue(auditDto.getRemark().length() <= 300, "备注需控制在300字符内");
        }
        Validate.notBlank(auditDto.getOrgCode(), "组织信息不能为空！");
        Validate.notEmpty(auditDto.getCacheList(), "结案明细，必填");
        List<String> schemeDetailCodes = auditDto.getCacheList().stream().map(MarketingAuditDetailDto::getSchemeDetailCode).collect(Collectors.toList());
        //查找活动明细
        MarketingPlanCaseVo planCaseDto = new MarketingPlanCaseVo();
        planCaseDto.setChangeFlag(BooleanEnum.FALSE.getCapital());
        planCaseDto.setSchemeDetailCodeList(schemeDetailCodes);
        Page<MarketingPlanCaseVo> caseVoPage = withHoldingService.findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseDto);
        Map<String, MarketingPlanCaseVo> planCaseVoMap = caseVoPage.getRecords().stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));

//        Map<String, WithHoldingVo> withHoldingVoMap = withHoldingService.findBySchemeDetailCodesPass(schemeDetailCodes).stream().filter(Objects::nonNull).collect(Collectors.toMap(e -> e.getBusinessCode(), Function.identity(), (a, b) -> a));
        //获取计提前兑付金额
        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesApproved(schemeDetailCodes);
        Map<String, BigDecimal> collect=Maps.newHashMap();
        if (!CollectionUtils.isEmpty(feeCashDetailVos)){
            collect = feeCashDetailVos.stream().filter(o->null!=o&&StringUtils.isNotBlank(o.getSchemeDetailCode())).collect(Collectors.groupingBy(FeeCashDetailVo::getSchemeDetailCode, Collectors.mapping(e -> e.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        Set<String> costTypeDetailCodeSet = planCaseVoMap.values().stream().map(e -> e.getDetailCode()).collect(Collectors.toSet());
        Map<String, String> costTypeDetailMap = costTypeDetailVoService.findByCodes(new ArrayList<>(costTypeDetailCodeSet)).stream().collect(Collectors.toMap(e -> e.getDetailCode(), e -> e.getBeAuditEarly(), (a, b) -> a));

        List<String> errorSchemeDetailCodes = new ArrayList<>();

        List<MarketingPlanCaseVo> auditData = planCaseRepository.findCaseListAuditData(schemeDetailCodes);
        Map<String, BigDecimal> finalCollect = collect;
        auditDto.getCacheList().forEach(e -> {
            //实时查询已结案金额o
            Optional<MarketingPlanCaseVo> first = auditData.stream().filter(o -> e.getSchemeDetailCode().equals(o.getSchemeDetailCode())).findFirst();
            if(first.isPresent()){
                e.setAuditedAmount(first.get().getAuditAmount());
            }
            Validate.notNull(e.getAuditAmount(), "本次结案金额，不能为空！");
            e.setAvailableAuditAmount(ObjectUtils.defaultIfNull(e.getAvailableAuditAmount(), BigDecimal.ZERO));
            Validate.notNull(e.getApplyAmount(), "规划金额，不能为空！");
            Validate.notEmpty(e.getBeFullAudit(), "是否完全结案，不能为空！");
            //1045913 【方案结案】兑付方式=货补/票扣时，不校验“票据类型”不能为空
            CashMethodEnum cashMethodEnum = CashMethodEnum.findByCode(e.getCashType());
            if (!CashMethodEnum.REPLENISHMENT.equals(cashMethodEnum)
                    && !CashMethodEnum.TICKET_BUCKLE.equals(cashMethodEnum)) {
                Validate.notBlank(e.getBillType(), "票据类型，不能为空！");
            }
            //本次结案金额+已结案金额
            BigDecimal decimal = e.getAuditAmount().add(Optional.ofNullable(e.getAuditedAmount()).orElse(BigDecimal.ZERO));
            BigDecimal applyAmount = e.getApplyAmount();
            if (finalCollect.containsKey(e.getSchemeDetailCode())) {
                //计提前兑付金额
                applyAmount = applyAmount.add(Optional.ofNullable(finalCollect.get(e.getSchemeDetailCode())).orElse(BigDecimal.ZERO));
            }
            MarketingPlanCaseVo planCaseVo = planCaseVoMap.get(e.getSchemeDetailCode());
            log.info("common a1:{},a2:{},de:{},ap:{} ava:{} year {}",e.getAuditAmount(),e.getAuditedAmount(),decimal,applyAmount,e.getAvailableAuditAmount(),e.getYears());
            boolean b = false;
            // 进货返利才当月判断
            if(MarketingPlanCaseTypeEnum.back.getCode().equals(planCaseVo.getCaseType())){
                YearMonth nowYearMonth = YearMonth.now();
                String years = e.getYears();
                if(StringUtils.isNotBlank(years)){
                    YearMonth yearMonth = YearMonth.parse(years);
                    log.info("===========进入进货返利当月判断 nowYearMonth {} , years {}  availableAuditAmount {}  ",nowYearMonth,years,e.getAvailableAuditAmount());
                    if(nowYearMonth.equals(yearMonth)){
                        BigDecimal availableAuditAmount = e.getAvailableAuditAmount();
 //                           1                              2
                        if (decimal.compareTo(availableAuditAmount) >0) {
                            errorSchemeDetailCodes.add(e.getSchemeDetailCode());
                        }
                        b = true;
                    }
                }
            }
            // 3                       1
            if (decimal.compareTo(applyAmount) > 0) {
                if(!b){
                    errorSchemeDetailCodes.add(e.getSchemeDetailCode());
                }
            }
            //活动如果为完全结案，不允许保存
            //1045914 【方案结案】已结案金额+本次结案金额等于规划费用时，应校验“是否完全结案”必须为“是”
            //1050286 【方案结案】增加校验：本次结案金额=0时，是否完全结案必须选择“是”
            if (decimal.compareTo(e.getApplyAmount()) == 0 || BigDecimal.ZERO.compareTo(e.getAuditAmount()) == 0) {
                Validate.isTrue(BooleanEnum.TRUE.getCapital().equals(e.getBeFullAudit()), "本次结案金额+已结案金额" +
                        "等于规划金额时/本次结案金额=0时,是否完全结案必须为是！");
            }
            Validate.isTrue(!AuditStatusEnum.WHOLE_AUDIT.getCode().equals(planCaseVo.getAuditStatus()), "活动【%s】已完全结案", e.getSchemeDetailCode());
            //后返类费用结案时，结案金额不能超过系统自动算出的结案金额
            String caseType = planCaseVo.getCaseType();
            if (MarketingPlanCaseTypeEnum.back.getCode().equals(caseType)) {
                Validate.isTrue(e.getAvailableAuditAmount().compareTo(e.getAuditAmount()) >= 0, "活动【%s】结案金额不能超活动可结案金额", e.getSchemeDetailCode());
            }

            //如果活动行费用项目的“是否允许提前结案”=“否”，则需要判断活动明细的结束日期和当期日期
            if (BooleanEnum.FALSE.getCapital().equals(costTypeDetailMap.get(e.getDetailCode()))) {
                Validate.isTrue(DateUtil.parse(planCaseVo.getEndDate(), "yyyy-MM-dd").compareTo(DateUtil.parse(DateUtil.today(), "yyyy-MM-dd")) < 0,
                        String.format("活动：%s不允许提前结案，请活动结束后再申请结案", planCaseVo.getSchemeDetailCode()));
            }
            // 1070753  【市场推广类】 如果存在计提，优先判断最新计提金额是否大于本次结案金额
            if (MarketingPlanCaseTypeEnum.display.getCode().equals(caseType)) {
                BigDecimal withholdingAmount = planCaseVo.getWithholdingAmount();
                if(Objects.nonNull(withholdingAmount)){
                    if (withholdingAmount.compareTo(decimal) < 0) {
                        errorSchemeDetailCodes.add(e.getSchemeDetailCode());
                    }
                }
            }

            e.setId(null);
            e.setCreateTime(null);
            e.setCreateAccount(null);
            e.setCreateName(null);
            e.setModifyTime(null);
            e.setModifyAccount(null);
            e.setModifyName(null);
        });
        Validate.isTrue(CollectionUtils.isEmpty(errorSchemeDetailCodes), "活动【%s】的本次结案金额+已结案金额不能大于申请/计提费用+计提前兑付金额，请调整该活动的“本次结案金额”！", String.join(",", errorSchemeDetailCodes));
        List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findBySchemeDetailCodesBeCommit(schemeDetailCodes);
        if (CollectionUtil.isNotEmpty(auditDetails)) {
            //排除自己
            List<MarketingAuditDetail> filterAuditDetails = auditDetails.stream().filter(e -> StringUtils.isBlank(auditDto.getAuditCode()) || !auditDto.getAuditCode().equals(e.getAuditCode())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(filterAuditDetails)) {
                Set<String> auditCodes = filterAuditDetails.stream().map(e -> e.getAuditCode()).collect(Collectors.toSet());
                Set<String> filterSchemeDetailCodes = filterAuditDetails.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toSet());
                Validate.isTrue(false, "活动【%s】已在结案【%s】中存在，不允许保存", String.join(",", filterSchemeDetailCodes), String.join(",", auditCodes));
            }
        }

        //票扣校验
        if (!CollectionUtils.isEmpty(auditDto.getAuditBillDetails())) {
            auditDto.getAuditBillDetails().forEach(e -> Validate.isTrue(e.getCheckFlag(), "票扣折让率校验未通过"));
        }
    }

    /**
     * 删除数据
     *
     * @param ids 主键结合
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
        List<MarketingAudit> audits = this.marketingAuditRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(audits)) {
            return;
        }
        List<String> auditCodes = audits.stream().map(MarketingAudit::getAuditCode).collect(Collectors.toList());
        audits.forEach(item -> Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) || ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】核销数据能删除"));

        //删除OA接口
        audits = audits.stream().filter(x ->
                StringUtils.equalsAny(x.getStatus(), ProcessStatusEnum.REJECT.getDictCode(),
                        ProcessStatusEnum.RECOVER.getDictCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(audits)) {
            audits.forEach(e -> ryOaProcessService.deleteWorkflow(e.getProcessNumber(), e.getOaId()));
        }

        auditFilesService.deleteByAuditCode(auditCodes);
        marketingAuditBillDetailRepository.deleteByCodes(auditCodes);
        marketingAuditDetailRepository.deleteByCodes(auditCodes);
        marketingAuditRepository.removeByIds(ids);

        //删除业务日志
        Collection<MarketingAuditDto> dtoList = nebulaToolkitService.copyCollectionByWhiteList(audits,
                MarketingAudit.class, MarketingAuditDto.class, HashSet.class, ArrayList.class);
        MarketingAuditLogEventDto logEventDto = new MarketingAuditLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(new ArrayList<>(dtoList));
        SerializableBiConsumer<MarketingAuditLogEventListener, MarketingAuditLogEventDto> onDelete =
                MarketingAuditLogEventListener::onDelete;
        this.nebulaNetEventClient.publish(logEventDto, MarketingAuditLogEventListener.class, onDelete);
    }

    /**
     * 生成票扣明细
     *
     * @param cacheKey
     * @return
     */
    @Override
    public List<MarketingAuditBillDetailVo> generateBill(String cacheKey) {
        Validate.notBlank(cacheKey, "缓存键不能为空");
        return generateBillByAuditDetail(findCacheList(cacheKey));
    }

    /**
     * 提交审批
     *
     * @param idList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submit(List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "提交时，主键集合不能为空！");
        List<MarketingAudit> audits = this.marketingAuditRepository.findByIds(idList);
        if (CollectionUtils.isEmpty(audits)) {
            return;
        }
        audits.forEach(item -> Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) || ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】核销数据能提交"));

        //活动在计提过程中时不允许提交
        List<String> auditCodes = audits.stream().map(e -> e.getAuditCode()).collect(Collectors.toList());
        List<MarketingAuditDetailVo> list = marketingAuditDetailRepository.findByCodes(auditCodes);
        List<String> schemeDetailCodes = list.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
        //查找活动明细
        MarketingPlanCaseVo planCaseDto = new MarketingPlanCaseVo();
        planCaseDto.setChangeFlag(BooleanEnum.FALSE.getCapital());
        planCaseDto.setSchemeDetailCodeList(schemeDetailCodes);
        Page<MarketingPlanCaseVo> caseVoPage = withHoldingService.findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseDto);
        // 活动明细结案周期判断
        List<String> detailCodes = caseVoPage.getRecords().stream().map(MarketingPlanCaseVo::getDetailCode).distinct().collect(Collectors.toList());
        Map<String,Long> costTypeDetailVoMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(detailCodes)){
            List<CostTypeDetailVo> costTypeDetailVos = costTypeDetailVoService.findByCodes(detailCodes);
            if(!CollectionUtils.isEmpty(costTypeDetailVos)){
                for (CostTypeDetailVo costTypeDetailVo : costTypeDetailVos) {
                    if(StringUtils.isNotBlank(costTypeDetailVo.getDetailCode()) && Objects.nonNull(costTypeDetailVo.getAuditValidityTime())){
                        costTypeDetailVoMap.put(costTypeDetailVo.getDetailCode(),costTypeDetailVo.getAuditValidityTime().longValue());
                    }
                }
            }
        }
        caseVoPage.getRecords().forEach(item->{
            if(StringUtils.isNotBlank(item.getEndDate())){
                String detailCode = item.getDetailCode();
                if(StringUtils.isNotBlank(detailCode) && costTypeDetailVoMap.containsKey(detailCode)){
                    LocalDate actEndDate = LocalDate.parse(item.getEndDate(), DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH_DAY));
                    LocalDate nowDate = LocalDate.now();
                    Long auditExpireDay = costTypeDetailVoMap.get(detailCode);
                    Validate.isTrue((actEndDate.plusDays(auditExpireDay).isAfter(nowDate) || actEndDate.plusDays(auditExpireDay).equals(nowDate)),"活动明细【%s】超过可结案周期%s天",item.getSchemeDetailCode(),auditExpireDay);
                }
            }
        });
        Map<String, MarketingPlanCaseVo> planCaseVoMap = caseVoPage.getRecords().stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findBySchemeDetailCodes(schemeDetailCodes);

        //获取计提前兑付金额
        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesApproved(schemeDetailCodes);
        Map<String, BigDecimal> collect=Maps.newHashMap();
        if (!CollectionUtils.isEmpty(feeCashDetailVos)){
            collect = feeCashDetailVos.stream().filter(o->null!=o&&StringUtils.isNotBlank(o.getSchemeDetailCode())).collect(Collectors.groupingBy(FeeCashDetailVo::getSchemeDetailCode, Collectors.mapping(e -> e.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        //获取最新的活动申请金额
        List<MarketingPlanCaseVo> caseVos = marketingPlanCaseService.findByCaseCodes(schemeDetailCodes);
        Map<String, MarketingPlanCaseVo> caseVoMap = caseVos.stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));

        Set<String> costTypeDetailCodeSet = caseVos.stream().map(e -> e.getDetailCode()).collect(Collectors.toSet());
        Map<String, String> costTypeDetailMap = costTypeDetailVoService.findByCodes(new ArrayList<>(costTypeDetailCodeSet)).stream().collect(Collectors.toMap(e -> e.getDetailCode(), e -> e.getBeAuditEarly(), (a, b) -> a));
//        Map<String, BigDecimal> withHoldingCashMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(withHoldingVos)) {
            List<WithHoldingVo> withHoldingVosNotPass = withHoldingVos.stream().filter(e -> !ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus())).collect(Collectors.toList());
            Validate.isTrue(CollectionUtils.isEmpty(withHoldingVosNotPass), "活动明细【%s】在计提过程中，不允许提交", String.join("；", withHoldingVosNotPass.stream().map(e -> e.getBusinessCode()).collect(Collectors.toList())));

            //结案提交时还需要再判断一次规划金额（计提/申请）与已结案金额的对比
            List<WithHoldingVo> withHoldingVosPass = withHoldingVos.stream().filter(e -> ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(withHoldingVosPass)) {
                Map<String, WithHoldingVo> withHoldingVoMap = withHoldingVosPass.stream().collect(Collectors.toMap(e -> e.getActivitiesDetailCode(), Function.identity(), (a, b) -> a));
                list.forEach(e -> {
                    WithHoldingVo withHoldingVo = withHoldingVoMap.get(e.getSchemeDetailCode());
                    e.setApplyAmount(withHoldingVo == null ? caseVoMap.get(e.getSchemeDetailCode()).getApplyAmount() : withHoldingVo.getActualAmount());
//                    withHoldingCashMap.put(e.getSchemeDetailCode(), withHoldingVo.getCashAmount());
                });
            } else {
                list.forEach(e -> {
                    MarketingPlanCaseVo planCaseVo = caseVoMap.get(e.getSchemeDetailCode());
                    e.setApplyAmount(planCaseVo.getApplyAmount());
                });
            }
            List<String> errorSchemeDetailCodes = new ArrayList<>();
            List<MarketingPlanCaseVo> auditData = planCaseRepository.findCaseListAuditData(schemeDetailCodes);
            Map<String, BigDecimal> finalCollect = collect;
            list.forEach(e -> {
                //实时查询已结案金额o
                Optional<MarketingPlanCaseVo> first = auditData.stream().filter(o -> e.getSchemeDetailCode().equals(o.getSchemeDetailCode())).findFirst();
                if(first.isPresent()){
                    e.setAuditedAmount(first.get().getAuditAmount());
                }
                //本次结案金额+已结案金额
                BigDecimal decimal = e.getAuditAmount().add(Optional.ofNullable(e.getAuditedAmount()).orElse(BigDecimal.ZERO));
                BigDecimal applyAmount = e.getApplyAmount();
                if (finalCollect.containsKey(e.getSchemeDetailCode())) {
                    //计提前兑付金额
                    applyAmount = applyAmount.add(Optional.ofNullable(finalCollect.get(e.getSchemeDetailCode())).orElse(BigDecimal.ZERO));
                }
                log.info("a1:{},a2:{},de:{},ap:{}",e.getAuditAmount(),e.getAuditedAmount(),decimal,applyAmount);
                if (decimal.compareTo(applyAmount) > 0) {
                    errorSchemeDetailCodes.add(e.getSchemeDetailCode());
                }

                //1045914 【方案结案】已结案金额+本次结案金额等于规划费用时，应校验“是否完全结案”必须为“是”
                //1050286 【方案结案】增加校验：本次结案金额=0时，是否完全结案必须选择“是”
                if (decimal.compareTo(e.getApplyAmount()) == 0 || BigDecimal.ZERO.compareTo(e.getAuditAmount()) == 0) {
                    Validate.isTrue(BooleanEnum.TRUE.getCapital().equals(e.getBeFullAudit()), "本次结案金额+已结案金额" +
                            "等于规划金额时/本次结案金额=0时,是否完全结案必须为是！");
                }
                MarketingPlanCaseVo planCaseVo = planCaseVoMap.get(e.getSchemeDetailCode());
                //后返类费用结案时，结案金额不能超过系统自动算出的结案金额
                String caseType = planCaseVo.getCaseType();
                if (MarketingPlanCaseTypeEnum.back.getCode().equals(caseType)) {
                    Validate.isTrue(e.getAvailableAuditAmount().compareTo(e.getAuditAmount()) >= 0, "活动【%s】结案金额不能超活动可结案金额", e.getSchemeDetailCode());
                }
                // 1070753  【市场推广类】 如果存在计提，优先判断最新计提金额是否大于本次结案金额
                if (MarketingPlanCaseTypeEnum.display.getCode().equals(caseType)) {
                    BigDecimal withholdingAmount = planCaseVo.getWithholdingAmount();
                    if(Objects.nonNull(withholdingAmount)){
                        if (withholdingAmount.compareTo(decimal) < 0) {
                            errorSchemeDetailCodes.add(e.getSchemeDetailCode());
                        }
                    }
                }

            });
            Validate.isTrue(CollectionUtils.isEmpty(errorSchemeDetailCodes), "活动【%s】的本次结案金额+已结案金额不能大于申请/计提费用+计提前兑付金额，请调整该活动的“本次结案金额”！", String.join(",", errorSchemeDetailCodes));
        }
        //活动在变更过程中时不允许提交
        List<MarketingPlanCaseVo> changing = marketingPlanCaseService.findChanging(schemeDetailCodes);
        Validate.isTrue(CollectionUtils.isEmpty(changing), "活动在变更过程中时不允许提交");

        list.forEach(e -> {
            //活动如果为完全结案，不允许保存
            MarketingPlanCaseVo planCaseVo = planCaseVoMap.get(e.getSchemeDetailCode());
            Validate.isTrue(!AuditStatusEnum.WHOLE_AUDIT.getCode().equals(planCaseVo.getAuditStatus()), "活动【%s】已完全结案", e.getSchemeDetailCode());

            //如果活动行费用项目的“是否允许提前结案”=“否”，则需要判断活动明细的结束日期和当期日期
            if (BooleanEnum.FALSE.getCapital().equals(costTypeDetailMap.get(e.getDetailCode()))) {
                Validate.isTrue(DateUtil.parse(planCaseVo.getEndDate(), "yyyy-MM-dd").compareTo(DateUtil.parse(DateUtil.today(), "yyyy-MM-dd")) < 0,
                        String.format("活动：%s不允许提前结案，请活动结束后再申请结案", planCaseVo.getSchemeDetailCode()));
            }
        });

        //票扣校验
        Set<String> belongDepartmentCodeList = list.stream().map(e -> e.getBelongDepartmentCode()).collect(Collectors.toSet());
        Map<String, String> orgMap = orgVoService.findByOrgCodes(new ArrayList<>(belongDepartmentCodeList)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), e -> Optional.ofNullable(e.getTicketCashType()).orElse(""), (a, b) -> a));
        List<MarketingAuditDetailVo> ticketList = list.stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode()) && CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(e.getCashType()) &&
                ("1".equals(e.getHzlx()) || "2".equals(e.getHzlx())) &&
                TicketCashTypeEnum.AUTO.getCode().equals(orgMap.get(e.getBelongDepartmentCode()))).collect(Collectors.toList());
        Set<String> customerCodeSet = ticketList.stream().filter(Objects::nonNull).map(e -> e.getCustomerCode()).collect(Collectors.toSet());
        //“经销商”或“直采”的客户，且兑付方式为“票扣”，需要校验票扣
        if (!CollectionUtils.isEmpty(customerCodeSet)) {
            List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodeSet)).stream().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(customerVos)) {
                if (!CollectionUtils.isEmpty(ticketList)) {
                    //如果结案金额=0，不控制当前客户的票扣明细不能为空
                    BigDecimal auditAmount = ticketList.stream().map(e -> e.getAuditAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (auditAmount.compareTo(BigDecimal.ZERO) != 0) {
                        List<MarketingAuditBillDetailVo> billList = marketingAuditBillDetailRepository.findByCodes(auditCodes);
                        Validate.notEmpty(billList, "经销商、直采类客户票扣费用结案时，票扣明细不允许为空");
                        billList.forEach(e -> Validate.isTrue(e.getCheckFlag(), "票扣折让率校验未通过"));
                        Set<String> ticketCodes = ticketList.stream().map(e -> e.getAuditCode()).collect(Collectors.toSet());
                        Set<String> billCodes = billList.stream().map(e -> e.getAuditCode()).collect(Collectors.toSet());
                        Validate.isTrue(billCodes.containsAll(ticketCodes), "经销商、直采类客户票扣费用结案时，票扣明细不允许为空");
                        //校验是否全部经销商或直采客户的票扣活动都生成了票扣明细
                        Map<String, List<MarketingAuditBillDetailVo>> billMap = billList.stream().collect(Collectors.groupingBy(e -> e.getCompanyCode() + "," + e.getCustomerCode() + "," + e.getYears()));
                        Map<String, List<MarketingAuditDetailVo>> detialMap = ticketList.stream().collect(Collectors.groupingBy(e -> e.getCompanyCode() + "," + e.getCustomerCode() + "," + e.getStartDate().substring(0, 7)));
                        if (billMap.size() != detialMap.size()) {
                            Set<String> billSet = billMap.keySet();
                            Set<String> detailSet = detialMap.keySet();
                            detailSet.removeAll(billSet);
                            List<String> detailSetName = detialMap.entrySet().stream().filter(e -> detailSet.contains(e.getKey()))
                                    .map(e -> e.getValue().get(0).getCompanyCode() + "+" + e.getValue().get(0).getCustomerName() + "+" + e.getValue().get(0).getStartDate().substring(0, 7)).collect(Collectors.toList());
                            Validate.isTrue(false, "公司+客户+年月【%s】没有生成票扣明细", String.join("；", detailSetName));
                        }
                        billMap.forEach((k, v) -> {
                            List<MarketingAuditDetailVo> detailVos = detialMap.get(k);
                            BigDecimal auditAmountGroup = detailVos.stream().map(e -> e.getAuditAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal amout = v.stream().map(e -> e.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                            Validate.isTrue(auditAmountGroup.compareTo(amout) == 0, "公司+客户+年月【%s】维度下结案金额汇总与对应维度下的票扣明细订单金额汇总不相等，请检查数据",
                                    v.get(0).getCompanyCode() + "+" + v.get(0).getCustomerName() + "+" + v.get(0).getYears());
                        });
                    }
                }
            }
        }

        audits.forEach(e -> {
            if (ProcessStatusEnum.PREPARE.getDictCode().equals(e.getStatus()) && StringUtils.isBlank(e.getProcessNumber())) {
                marketingAuditOaService.pushOa(e.getAuditCode());
            } else {
                marketingAuditOaService.resubmitOa(e.getAuditCode());
            }
        });
    }

    /**
     * 自动结案
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoAudit() {
        List<MarketingPlanCaseVo> caseGift = getMarketingPlanCase(MarketingPlanCaseTypeEnum.matching_gift);
//        List<MarketingPlanCaseVo> caseMaterial = getMarketingPlanCase(MarketingPlanCaseTypeEnum.material);
        List<MarketingAuditDto> auditDtoList = new ArrayList<>();
        auditDtoList.addAll(buildAuditDto(caseGift, MarketingPlanCaseTypeEnum.matching_gift));
//        auditDtoList.addAll(buildAuditDto(caseMaterial, MarketingPlanCaseTypeEnum.material));

        if (CollectionUtils.isEmpty(auditDtoList)) {
            log.warn("没有可以自动结案的数据");
        } else {
            auditDtoList.forEach(e -> e.setAuditBillDetails(generateBillByAuditDetail(e.getCacheList())));
            List<String> codes = store(auditDtoList);
//            caseGift.addAll(caseMaterial);
            List<MarketingAuditDetailDto> auditDetailDtoList = new ArrayList<>();
            auditDtoList.forEach(e -> auditDetailDtoList.addAll(e.getCacheList()));
            updateSchemeDetail(auditDetailDtoList, caseGift, true);

            //推送结案数据到dms
            this.pushDmsReplenishmentList(codes);
            //写入费用兑付/关闭明细、冲销数据
            this.generateDeliveryReplenishmentAndWriteOff(codes);
//            marketingAuditService.approved(codes);
            this.createFeeCash(codes);
        }
    }


    /**
     * 周边物料自动结案
     *
     * @param schemeDetailCodes
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoAuditPlanCasePass(List<String> schemeDetailCodes) {
        List<MarketingPlanCaseVo> caseMaterial = marketingPlanCaseService.findByCaseCodes(schemeDetailCodes).stream().filter(Objects::nonNull)
                .filter(e -> MarketingPlanCaseTypeEnum.material.getCode().equals(e.getCaseType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(caseMaterial)) {
            return;
        }
        Set<String> schemeCodes = caseMaterial.stream().map(e -> e.getSchemeCode()).collect(Collectors.toSet());
        List<MarketingPlan> marketingPlans = marketingPlanRepository.queryBySchemeCodes(schemeCodes);
        Map<String, MarketingPlan> marketingPlanMap = marketingPlans.stream().collect(Collectors.toMap(e -> e.getSchemeCode(), Function.identity(), (a, b) -> a));
        caseMaterial.forEach(e -> {
            MarketingPlan marketingPlan = marketingPlanMap.getOrDefault(e.getSchemeCode(), new MarketingPlan());
            e.setDepartmentName(marketingPlan.getDepartmentName());
            e.setSchemeName(marketingPlan.getSchemeName());
            e.setOrgCode(marketingPlan.getOrgCode());
            e.setOrgName(marketingPlan.getOrgName());
            e.setPositionCode(marketingPlan.getPositionCode());
        });
        List<MarketingAuditDto> auditDtoList = buildAuditDto(caseMaterial, MarketingPlanCaseTypeEnum.material);
        auditDtoList.forEach(e -> e.setAuditBillDetails(generateBillByAuditDetail(e.getCacheList())));
        List<String> codes = store(auditDtoList);
        List<MarketingAuditDetailDto> auditDetailDtoList = new ArrayList<>();
        auditDtoList.forEach(e -> auditDetailDtoList.addAll(e.getCacheList()));
        updateSchemeDetail(auditDetailDtoList, caseMaterial, true);

        //推送结案数据到dms
        this.pushDmsReplenishmentList(codes);
        //写入费用兑付/关闭明细、冲销数据
        this.generateDeliveryReplenishmentAndWriteOff(codes);
//        marketingAuditService.approved(codes);
        this.createFeeCash(codes);
    }

    /**
     * 获取所有缓存
     *
     * @param cacheKey
     * @return
     */
    @Override
    public List<MarketingAuditDetailDto> findAllCacheList(String cacheKey) {
        return findCacheList(cacheKey);
    }


    private final String FEE_MATERIAL = "fee_material";

    private final static String TPM_COST_CLOSE_NOT_CASE_TYPE = "tpm_cost_close_not_case_type";

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    @Override
    public Page<MarketingAuditDetailVo> findByConditions(Pageable pageable, MarketingAuditDetailDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new MarketingAuditDetailDto();
        }
        dto.setBeFullCash(null);
        Pageable pageableAll = PageRequest.of(0, Integer.MAX_VALUE);

        //根据票扣明细、账扣明细选择的客户过滤
        if (StringUtils.isNotBlank(dto.getCacheKey())) {
            List<FeeCashTicketDto> ticketDtos = feeCashService.findCacheList(dto.getCacheKey());
            if (!CollectionUtils.isEmpty(ticketDtos)) {
                dto.setCustomerCodes(ticketDtos.stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode())).map(e -> e.getCustomerCode()).collect(Collectors.toSet()));
            }
        }

        //是否物料采购
        if (BooleanEnum.TRUE.getCapital().equals(dto.getMaterialPurchaseFlag())) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(FEE_MATERIAL);
            if (!CollectionUtils.isEmpty(dictDataVos)) {
                dto.setDetailCodeList(dictDataVos.stream().map(x -> x.getDictCode()).collect(Collectors.toList()));
            }
        }
        //是否费用关闭
        if (BooleanEnum.TRUE.getCapital().equals(dto.getCostCloseFlag())) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(TPM_COST_CLOSE_NOT_CASE_TYPE);
            if (!CollectionUtils.isEmpty(dictDataVos)) {
                dto.setCaseTypeList(dictDataVos.stream().map(x -> x.getDictCode()).collect(Collectors.toList()));
            }
        }
        if (StringUtils.isNotEmpty(dto.getAuditDetailCodeListStr())) {
            List<String> auditDetailCodes = Arrays.asList(dto.getAuditDetailCodeListStr().split(","));
            if (!CollectionUtils.isEmpty(auditDetailCodes) && auditDetailCodes.size() > 200) {
                throw new IllegalArgumentException("结案明细编码数量不能超过200个");
            }
            dto.setAuditDetailCodeList(auditDetailCodes);
        }

        Page<MarketingAuditDetailVo> page = this.marketingAuditDetailRepository.findByConditions(pageableAll, dto);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<MarketingAuditDetailVo> marketingAuditDetailVos = page.getRecords();
            List<String> auditDetailCodes = marketingAuditDetailVos.stream().map(MarketingAuditDetailVo::getAuditDetailCode)
                    .filter(StringUtil::isNotEmpty).collect(Collectors.toList());
            //查询一级部门
            Set<String> belongDepartmentCodes = marketingAuditDetailVos.stream().map(MarketingAuditDetailVo::getBelongDepartmentCode)
                    .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());

            //关闭金额
            Map<String, BigDecimal> map = deliveryReplenishmentPoolDetailService.findByAuditDetailCodes(auditDetailCodes);
            //预付金额
            Map<String, List<ActivityPrepayDetailRecord>> prepayDetailMap = new HashMap<>();
            List<ActivityPrepayDetailRecord> prepayDetails = activityPrepayDetailRecordRepository.findByAuditDetailCodes(auditDetailCodes);
            if (!CollectionUtils.isEmpty(prepayDetails)) {
                prepayDetailMap = prepayDetails.stream().collect(Collectors.groupingBy(ActivityPrepayDetailRecord::getAuditDetailCode));
            }

            //查询兑付信息
            if (StringUtils.isNotBlank(dto.getCashCodeExclude())) {
                dto.setCashDetailCodeList(feeCashDetailRepository.findByCode(dto.getCashCodeExclude()).stream().filter(Objects::nonNull).map(e -> e.getCashDetailCode()).collect(Collectors.toList()));
            }
            List<FeeCashDetailVo> cashDetailVos = feeCashService.findByAuditDetailCodesPass(auditDetailCodes, null, dto.getCashDetailCodeList(), Lists.newArrayList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.MATERIAL.getDictCode(),CashTypeEnum.WIREDUIFU.getDictCode()));
            Map<String, List<FeeCashDetailVo>> cashDetailMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(cashDetailVos)) {
                cashDetailMap = cashDetailVos.stream().collect(Collectors.groupingBy(e -> e.getAuditDetailCode()));
            }
            //查询货补信息
            Map<String, List<DeliveryReplenishmentPoolDetailVo>> deliveryMap = deliveryReplenishmentPoolDetailService.findByAuditDetailCodesAll(auditDetailCodes);

            Map<String, List<ActivityPrepayDetailRecord>> finalPrepayDetailMap = prepayDetailMap;
            Map<String, List<FeeCashDetailVo>> finalCashDetailMap = cashDetailMap;
            AtomicReference<BigDecimal> availableCashAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
            AtomicReference<BigDecimal> availableReversedAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
            MarketingAuditDetailDto finalDto = dto;
            page.getRecords().forEach(v -> {
                v.setAuditedAmount(Optional.ofNullable(v.getAuditedAmount()).orElse(BigDecimal.ZERO));
                v.setSurplusAuditAmount(v.getAuditAmount().subtract(v.getAuditedAmount()));
                v.setAvailableCashAmount(v.getAuditAmount().subtract(Optional.ofNullable(v.getCashAmount()).orElse(BigDecimal.ZERO)));
                availableCashAmountTotal.set(availableCashAmountTotal.get().add(v.getAvailableCashAmount()));

                //兑付方式=电汇、账扣、票扣的结案明细：查询结案明细关联的费用兑付明细的“本次兑付金额”汇总（兑付类型=费用兑付的费用兑付明细数据）；
                //查询结案明细关联的费用兑付明细是否有完全兑付的单据（取审批通过状态且兑付类型=费用兑付的费用兑付明细数据）；如果有，是否完全兑付=是；如果没有，是否完全兑付=否；
                String beFullCash = BooleanEnum.FALSE.getCapital();
                BigDecimal cashAmount = BigDecimal.ZERO;
                if (StringUtils.equalsAny(v.getCashType(), CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode()) && finalCashDetailMap.containsKey(v.getAuditDetailCode())) {
                    List<FeeCashDetailVo> cashDetailVoList = finalCashDetailMap.get(v.getAuditDetailCode());
                    for (FeeCashDetailVo cashDetailVo : cashDetailVoList) {
                        cashAmount = cashAmount.add(Optional.ofNullable(cashDetailVo.getThisCashAmount()).orElse(BigDecimal.ZERO));
                        if (BooleanEnum.TRUE.getCapital().equals(cashDetailVo.getBeCash())) {
                            beFullCash = BooleanEnum.TRUE.getCapital();
                        }
                    }
                    //兑付方式=货补的结案明细：通过“结案明细编码”在【费用兑付/关闭明细】下查询“业务编码”且“操作类型=发货”，对查询出来的数据的“费用金额”汇总；
                } else if (CashMethodEnum.REPLENISHMENT.getDictCode().equals(v.getCashType()) && deliveryMap.containsKey(v.getAuditDetailCode())) {
                    List<DeliveryReplenishmentPoolDetailVo> deliveryVoList = deliveryMap.get(v.getAuditDetailCode());
                    for (DeliveryReplenishmentPoolDetailVo deliveryVo : deliveryVoList) {
                        cashAmount = cashAmount.add(deliveryVo.getOperationAmount());
                    }
                }
                v.setCashAmount(cashAmount);

                BigDecimal closeAmount = map.getOrDefault(v.getAuditDetailCode(), BigDecimal.ZERO);
                v.setCloseAmount(closeAmount);
                v.setCashBalance(v.getAuditAmount().subtract(Optional.ofNullable(v.getCashAmount()).orElse(BigDecimal.ZERO)).subtract(closeAmount));
                //兑付方式=货补的结案明细：如果“核销余额”≤0，则是否完全兑付=是，反之，是否完全兑付=否；
                if (StringUtils.equalsAny(v.getCashType(), CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(),
                        CashMethodEnum.TICKET_BUCKLE.getDictCode(), CashMethodEnum.REPLENISHMENT.getDictCode())
                        && v.getCashBalance().compareTo(BigDecimal.ZERO) <= 0) {
                    beFullCash = BooleanEnum.TRUE.getCapital();
                }
                v.setBeFullCash(beFullCash);

                if (finalPrepayDetailMap.containsKey(v.getAuditDetailCode())) {
                    List<ActivityPrepayDetailRecord> prepayDetailRecords = finalPrepayDetailMap.get(v.getAuditDetailCode());
                    BigDecimal prepayAmount = BigDecimal.ZERO;
                    BigDecimal reversedAmount = BigDecimal.ZERO;
                    BigDecimal availableReversedAmount = BigDecimal.ZERO;
                    for (ActivityPrepayDetailRecord prepayDetailRecord : prepayDetailRecords) {
                        prepayAmount = prepayAmount.add(Optional.ofNullable(prepayDetailRecord.getPrepayAmount()).orElse(BigDecimal.ZERO));
                        reversedAmount = reversedAmount.add(Optional.ofNullable(prepayDetailRecord.getReversedAmount()).orElse(BigDecimal.ZERO));
                        availableReversedAmount = availableReversedAmount.add(Optional.ofNullable(prepayDetailRecord.getAvailableReversedAmount()).orElse(BigDecimal.ZERO));
                    }
                    v.setPrepayAmount(prepayAmount);
                    v.setReversedAmount(reversedAmount);
                    v.setAvailableReversedAmount(availableReversedAmount);
                    if (finalDto.getSkipClose()) {
                        availableReversedAmountTotal.set(availableReversedAmountTotal.get().add(v.getAvailableReversedAmount()));
                    }
                }
                v.setAvailableCashAmount(v.getAuditAmount().subtract(Optional.ofNullable(v.getCashAmount()).orElse(BigDecimal.ZERO)));
                //剩余可兑付金额=结案金额-已核销金额-预付可核销金额-关闭金额
                v.setAvailableCashPrepayAmount(v.getAvailableCashAmount().subtract(Optional.ofNullable(v.getAvailableReversedAmount()).orElse(BigDecimal.ZERO)).subtract(v.getCloseAmount()));
                //“超节点时长（天）”，取值逻辑：如果剩余可兑付金额＞0，则超节点时长（天）=当前时间-结案完结时间；如果剩余可兑付金额≤0或结案明细是完全兑付时，则超节点时长（天）=空；
                if (v.getAuditDate() != null && !(v.getAvailableCashPrepayAmount().compareTo(BigDecimal.ZERO) <= 0 || BooleanEnum.TRUE.getCapital().equals(v.getBeFullCash()))) {
                    v.setOverDay(String.valueOf(DateUtil.betweenDay(DateUtil.parse(v.getAuditDate(), "yyyy-MM-dd"), new Date(), true)));
                }
            });
            page.getRecords().forEach(v -> {
                v.setAvailableCashAmountTotal(availableCashAmountTotal.get());
                v.setAvailableReversedAmountTotal(availableReversedAmountTotal.get());
            });
            if (!dto.getSkipClose()) {
                List<MarketingAuditDetailVo> collect = page.getRecords().stream().filter(o -> null == o.getAvailableReversedAmount() || BigDecimal.ZERO.compareTo(o.getAvailableReversedAmount()) == 0).collect(Collectors.toList());
                page.setRecords(collect);
            }
            //根据兑付金额查询结案明细，按照结案明细升序查询结案明细的“可冲销预付余额”，用“可冲销预付余额”凑够“本次兑付金额”，满足Σ可冲销预付余额≥本次兑付金额
            BigDecimal sumCashAmount = BigDecimal.ZERO;
            List<MarketingAuditDetailVo> pageRecords = page.getRecords();
            List<MarketingAuditDetailVo> records = new ArrayList<>();
            if (dto.getThisCashAmount() != null) {
                for (MarketingAuditDetailVo v : pageRecords) {
                    sumCashAmount = sumCashAmount.add(Optional.ofNullable(v.getAvailableReversedAmount()).orElse(BigDecimal.ZERO));
                    records.add(v);
                    if (sumCashAmount.compareTo(dto.getThisCashAmount()) >= 0) {
                        break;
                    }
                }
            } else {
                records = pageRecords;
            }

            //过滤是否完全兑付≠是，核销余额＞0的结案明细
            if (!(BooleanEnum.TRUE.getCapital().equals(dto.getBeFullCash()))) {
                if (BooleanEnum.TRUE.getCapital().equals(dto.getCashTypeFlag())) {
                    records = records.stream().filter(e -> BooleanEnum.FALSE.getCapital().equals(e.getBeFullCash()) &&
                            BigDecimal.ZERO.compareTo(e.getAvailableCashAmount()) < 0).collect(Collectors.toList());
                } else {
                    records = records.stream().filter(e -> BooleanEnum.FALSE.getCapital().equals(e.getBeFullCash()) &&
                            BigDecimal.ZERO.compareTo(e.getAvailableCashPrepayAmount()) < 0).collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(records)) {
                    return new Page<>();
                }
            }

            //手动分页
            Page<MarketingAuditDetailVo> newPage = new Page<>();
            newPage.setTotal(records.size());
            newPage.setPages(records.size() / pageable.getPageSize() + (records.size() % pageable.getPageSize() == 0 ? 0 : 1));
            newPage.setSize(pageable.getPageSize());
            newPage.setCurrent(pageable.getPageNumber());
            List<MarketingAuditDetailVo> res = records.stream()
                    .skip((pageable.getPageNumber() - 1) * pageable.getPageSize()).limit(pageable.getPageSize()).collect(Collectors.toList());
            newPage.setRecords(res);
            page = newPage;
        }
        return page;
    }

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    @Override
    public Page<MarketingAuditDetailVo> findDataViewByConditions(Pageable pageable, MarketingAuditDetailDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new MarketingAuditDetailDto();
        }
        //查找一级部门所对应的所有部门
        if (StringUtils.isNotBlank(dto.getDepartmentOneName())) {
            OrgQueryDto orgQueryDto = new OrgQueryDto();
            orgQueryDto.setOrgName(dto.getDepartmentOneName());
            Set<String> orgQuerySet = orgVoService.findByOrgQueryDto(orgQueryDto);
            Validate.notEmpty(orgQuerySet, "未找到搜索条件“一级部门名称”所对应的一级部门");
            List<OrgVo> orgVos = orgVoService.findByOrgCodes(new ArrayList<>(orgQuerySet));
            List<OrgVo> orgVoFilter = orgVos.stream().filter(m -> m.getLevelNum().equals(2)).collect(Collectors.toList());
            Validate.notEmpty(orgVoFilter, "未找到搜索条件“一级部门名称”所对应的一级部门");
            List<OrgVo> allChildren = orgVoService.findAllChildrenByOrgCode(orgVoFilter.get(0).getOrgCode());
            dto.setBelongDepartmentNameList(allChildren.stream().map(OrgVo::getOrgName).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(dto.getDepartmentOneCode())) {
            List<OrgVo> orgVos = orgVoService.findByOrgCodes(Collections.singletonList(dto.getDepartmentOneCode()));
            Validate.notEmpty(orgVos, "未找到搜索条件“一级部门编码”所对应的一级部门");
            List<OrgVo> orgVoFilter = orgVos.stream().filter(m -> m.getLevelNum().equals(2)).collect(Collectors.toList());
            Validate.notEmpty(orgVoFilter, "未找到搜索条件“一级部门编码”所对应的一级部门");
            List<OrgVo> allChildren = orgVoService.findAllChildrenByOrgCode(orgVoFilter.get(0).getOrgCode());
            dto.setBelongDepartmentCodeList(allChildren.stream().map(OrgVo::getOrgCode).collect(Collectors.toList()));
        }
        Page<MarketingAuditDetailVo> page = this.marketingAuditDetailRepository.findByConditions(pageable, dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        page.getRecords().forEach(x -> x.setAuditAmount(x.getAuditAmount().setScale(2, BigDecimal.ROUND_HALF_DOWN)));
        List<MarketingAuditDetailVo> marketingAuditDetailVos = page.getRecords();
        List<String> auditDetailCodes = marketingAuditDetailVos.stream().map(MarketingAuditDetailVo::getAuditDetailCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        List<String> schemeDetailCodes = marketingAuditDetailVos.stream().map(MarketingAuditDetailVo::getSchemeDetailCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        //查询一级部门
        Set<String> belongDepartmentCodes = marketingAuditDetailVos.stream().map(MarketingAuditDetailVo::getBelongDepartmentCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());

        Map<String, List<OrgVo>> allParentByOrgCodesMap = orgVoService.findAllParentByOrgCodesMap(new ArrayList<>(belongDepartmentCodes));
        //关闭金额
        Map<String, BigDecimal> map = deliveryReplenishmentPoolDetailService.findByAuditDetailCodes(auditDetailCodes);
        //预付金额
        Map<String, List<ActivityPrepayDetailRecord>> prepayDetailMap = new HashMap<>();
        List<ActivityPrepayDetailRecord> prepayDetails = activityPrepayDetailRecordRepository.findByAuditDetailCodes(auditDetailCodes);
        if (!CollectionUtils.isEmpty(prepayDetails)) {
            prepayDetailMap = prepayDetails.stream().collect(Collectors.groupingBy(ActivityPrepayDetailRecord::getAuditDetailCode));
        }

        //查询兑付信息
        List<FeeCashDetailVo> cashDetailVos = feeCashService.findByAuditDetailCodesPass(auditDetailCodes, ProcessStatusEnum.PASS.getDictCode(), null, Collections.singletonList(CashTypeEnum.FEE.getDictCode()));
        Map<String, List<FeeCashDetailVo>> cashDetailMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(cashDetailVos)) {
            cashDetailMap = cashDetailVos.stream().collect(Collectors.groupingBy(e -> e.getAuditDetailCode()));
        }
        //查询活动信息
        Map<String, List<MarketingPlanCaseVo>> caseMap = marketingPlanCaseService.findByCaseCodes(schemeDetailCodes).stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode()));
        //查询货补信息
        Map<String, List<DeliveryReplenishmentPoolDetailVo>> deliveryMap = deliveryReplenishmentPoolDetailService.findByAuditDetailCodesAll(auditDetailCodes);

        Map<String, List<ActivityPrepayDetailRecord>> finalPrepayDetailMap = prepayDetailMap;
        Map<String, List<FeeCashDetailVo>> finalCashDetailMap = cashDetailMap;
        page.getRecords().forEach(e -> {
            //如果结案明细对应的活动明细的表单类型为“随单搭赠”或“周边物料”，则结案明细的兑付金额=本次结案金额，兑付状态=完全兑付
            String beFullCash = BooleanEnum.FALSE.getCapital();
            BigDecimal cashAmount = BigDecimal.ZERO;
            if (caseMap.containsKey(e.getSchemeDetailCode()) && Arrays.asList(MarketingPlanCaseTypeEnum.matching_gift.getCode(),
                    MarketingPlanCaseTypeEnum.material.getCode()).contains(caseMap.get(e.getSchemeDetailCode()).get(0).getCaseType())) {
                cashAmount = e.getAuditAmount();
                beFullCash = BooleanEnum.TRUE.getCapital();
            } else if (StringUtils.equalsAny(e.getCashType(), CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(),
                    CashMethodEnum.TICKET_BUCKLE.getDictCode()) && finalCashDetailMap.containsKey(e.getAuditDetailCode())) {
                List<FeeCashDetailVo> cashDetailVoList = finalCashDetailMap.get(e.getAuditDetailCode());
                for (FeeCashDetailVo cashDetailVo : cashDetailVoList) {
                    cashAmount = cashAmount.add(cashDetailVo.getThisCashAmount());
                }
                //兑付方式=货补的结案明细：通过“结案明细编码”在【费用兑付/关闭明细】下查询“业务编码”且“操作类型=发货”，对查询出来的数据的“费用金额”汇总；
            } else if (CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType()) && deliveryMap.containsKey(e.getAuditDetailCode())) {
                List<DeliveryReplenishmentPoolDetailVo> deliveryVoList = deliveryMap.get(e.getAuditDetailCode());
                for (DeliveryReplenishmentPoolDetailVo deliveryVo : deliveryVoList) {
                    cashAmount = cashAmount.add(deliveryVo.getOperationAmount());
                }
            }
            e.setCashAmount(cashAmount);

            BigDecimal closeAmount = map.getOrDefault(e.getAuditDetailCode(), BigDecimal.ZERO);
            e.setCloseAmount(closeAmount);
            //核销余额=本次结案金额-核销金额-关闭金额
            e.setCashBalance((e.getAuditAmount().subtract(Optional.ofNullable(e.getCashAmount()).orElse(BigDecimal.ZERO)).subtract(closeAmount)).setScale(2, BigDecimal.ROUND_HALF_DOWN));
            //兑付方式=货补、电汇、账扣、票扣的结案明细：如果“核销余额”≤0，则是否完全兑付=是，反之，是否完全兑付=否；
            if (StringUtils.equalsAny(e.getCashType(), CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode(), CashMethodEnum.REPLENISHMENT.getDictCode())
                    && e.getCashBalance().compareTo(BigDecimal.ZERO) <= 0) {
                beFullCash = BooleanEnum.TRUE.getCapital();
            }
            //如果结案明细的本次结案金额=0，则是否完全兑付=是
            if (e.getAuditAmount().compareTo(BigDecimal.ZERO) == 0) {
                beFullCash = BooleanEnum.TRUE.getCapital();
            }
            e.setBeFullCash(beFullCash);
            e.setAvailableReversedAmount(BigDecimal.ZERO);
            if (finalPrepayDetailMap.containsKey(e.getAuditDetailCode())) {
                List<ActivityPrepayDetailRecord> prepayDetailRecords = finalPrepayDetailMap.get(e.getAuditDetailCode());
                BigDecimal prepayAmount = BigDecimal.ZERO;
                BigDecimal reversedAmount = BigDecimal.ZERO;
                BigDecimal availableReversedAmount = BigDecimal.ZERO;
                for (ActivityPrepayDetailRecord prepayDetailRecord : prepayDetailRecords) {
                    prepayAmount = prepayAmount.add(Optional.ofNullable(prepayDetailRecord.getPrepayAmount()).orElse(BigDecimal.ZERO));
                    reversedAmount = reversedAmount.add(Optional.ofNullable(prepayDetailRecord.getReversedAmount()).orElse(BigDecimal.ZERO));
                    availableReversedAmount = availableReversedAmount.add(Optional.ofNullable(prepayDetailRecord.getAvailableReversedAmount()).orElse(BigDecimal.ZERO));
                }
                e.setPrepayAmount(prepayAmount);
                e.setReversedAmount(reversedAmount);
                e.setAvailableReversedAmount(availableReversedAmount);
            }
            e.setAvailableCashAmount(e.getAuditAmount().subtract(Optional.ofNullable(e.getCashAmount()).orElse(BigDecimal.ZERO)));
            e.setAvailableCashPrepayAmount(BigDecimal.ZERO);
            //兑付方式=货补、票扣的结案明细，兑付余额=核销余额
            if (StringUtils.equalsAny(e.getCashType(), CashMethodEnum.REPLENISHMENT.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode())) {
                e.setAvailableCashPrepayAmount(e.getCashBalance());
            } else if (StringUtils.equalsAny(e.getCashType(), CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode())) {
                //剩余可兑付金额=结案金额-已核销金额-预付可核销金额-关闭金额
                e.setAvailableCashPrepayAmount((e.getAvailableCashAmount().subtract(e.getCloseAmount())).subtract(e.getAvailableReversedAmount()).setScale(2, BigDecimal.ROUND_HALF_DOWN));
            }
            //“超节点时长（天）”，取值逻辑：如果剩余可兑付金额＞0，则超节点时长（天）=当前时间-结案完结时间；如果剩余可兑付金额≤0或结案明细是完全兑付时，则超节点时长（天）=空；
            if (e.getAuditDate() != null && !(e.getAvailableCashPrepayAmount().compareTo(BigDecimal.ZERO) <= 0 || BooleanEnum.TRUE.getCapital().equals(e.getBeFullCash()))) {
                e.setOverDay(String.valueOf(DateUtil.betweenDay(DateUtil.parse(e.getAuditDate(), "yyyy-MM-dd"), new Date(), true)));
            }

            //一级部门
            if (allParentByOrgCodesMap.containsKey(e.getBelongDepartmentCode())) {
                List<OrgVo> orgVos = allParentByOrgCodesMap.get(e.getBelongDepartmentCode());
                List<OrgVo> orgVoFilter = orgVos.stream().filter(m -> m.getLevelNum().equals(2)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(orgVoFilter)) {
                    e.setDepartmentOneCode(orgVoFilter.get(0).getOrgCode());
                    e.setDepartmentOneName(orgVoFilter.get(0).getOrgName());
                }
            }
        });

        return page;
    }

    /**
     * 批量查询
     *
     * @param dtoList
     * @return
     */
    @Override
    public List<MarketingAuditDetailVo> findByDtoList(List<MarketingAuditDetailDto> dtoList) {
        return marketingAuditDetailRepository.findByDtoList(dtoList);
    }

    /**
     * 审批通过
     *
     * @param codes
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approved(List<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return;
        }
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditDetailRepository.findByCodes(codes);
        List<MarketingAudit> auditList = marketingAuditRepository.findByCodes(codes);

        //更新结案完结日期
        marketingAuditDetailRepository.updateAuditDate(codes);

        //结案冲销
        log.warn("++++++++++++++++++++++++++++++++++开始结案冲销");
        List<WithHoldingWriteOffDto> dtoList = new ArrayList<>();
        auditDetailVos.forEach(e -> {
            if (BooleanEnum.TRUE.getCapital().equals(e.getBeFullAudit())) {
                WithHoldingWriteOffDto dto = new WithHoldingWriteOffDto();
                dto.setActivitiesDetailCode(e.getSchemeDetailCode());
                dto.setWriteOffType(WriteOffTypeEnum.AUDIT.getDictCode());
                dto.setWriteOffAmount(e.getAuditAmount());
                dto.setBeFullAudit(e.getBeFullAudit());
                dto.setAuditCreateAccount(e.getCreateAccount());
                dto.setPositionCode(e.getPositionCode());
                dto.setSourceCode(e.getAuditDetailCode());
                dtoList.add(dto);
            }
        });
        Map<String, List<WithHoldingWriteOffVo>> schemeDetailMap = withHoldingWriteOffSendHecService.writeOff(dtoList);
        log.warn("++++++++++++++++++++++++++++++++++结束结案冲销");

        log.warn("++++++++++++++++++++++++++++++++++开始生成费用兑付/关闭明细");
        if (!CollectionUtils.isEmpty(schemeDetailMap)) {
            List<DeliveryReplenishmentPoolDetailDto> poolDetailDtos = new ArrayList<>();
            auditDetailVos.forEach(e -> {
                DeliveryReplenishmentPoolDetailDto poolDetailDto = new DeliveryReplenishmentPoolDetailDto();
                poolDetailDto.setBusinessCode(e.getAuditDetailCode());
                poolDetailDto.setCustomerCode(e.getCustomerCode());
                poolDetailDto.setCustomerName(e.getCustomerName());
                if (schemeDetailMap.keySet().contains(e.getSchemeDetailCode())) {
                    poolDetailDto.setBeWriteOff(BooleanEnum.TRUE.getCapital());
                    poolDetailDto.setWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
                    poolDetailDto.setOperationAmount(schemeDetailMap.get(e.getSchemeDetailCode()).get(0).getWriteOffAmount());
                } else {
                    poolDetailDto.setBeWriteOff(BooleanEnum.FALSE.getCapital());
                    poolDetailDto.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
                    poolDetailDto.setOperationAmount(e.getAuditAmount());
                }
                poolDetailDto.setOperationType(DeliveryDetailTypeEnum.AUDIT.getCode());
                poolDetailDto.setDeliveryTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
                poolDetailDto.setSchemeDetailCode(e.getSchemeDetailCode());
                poolDetailDto.setAuditCode(e.getAuditCode());
                poolDetailDto.setAuditDetailCode(e.getAuditDetailCode());
                poolDetailDto.setSchemeCode(e.getSchemeCode());
                poolDetailDto.setCreateAccount(e.getCreateAccount());
                poolDetailDto.setPositionCode(e.getPositionCode());
                poolDetailDtos.add(poolDetailDto);
            });
            deliveryReplenishmentPoolDetailService.create(poolDetailDtos);
        } else {
            List<DeliveryReplenishmentPoolDetailDto> poolDetailDtos = new ArrayList<>();
            auditDetailVos.forEach(e -> {
                DeliveryReplenishmentPoolDetailDto poolDetailDto = new DeliveryReplenishmentPoolDetailDto();
                poolDetailDto.setBusinessCode(e.getAuditDetailCode());
                poolDetailDto.setCustomerCode(e.getCustomerCode());
                poolDetailDto.setCustomerName(e.getCustomerName());
                poolDetailDto.setBeWriteOff(BooleanEnum.FALSE.getCapital());
                poolDetailDto.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
                poolDetailDto.setOperationType(DeliveryDetailTypeEnum.AUDIT.getCode());
                poolDetailDto.setDeliveryTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
                poolDetailDto.setOperationAmount(e.getAuditAmount());
                poolDetailDto.setSchemeDetailCode(e.getSchemeDetailCode());
                poolDetailDto.setAuditCode(e.getAuditCode());
                poolDetailDto.setAuditDetailCode(e.getAuditDetailCode());
                poolDetailDto.setSchemeCode(e.getSchemeCode());
                poolDetailDto.setCreateAccount(e.getCreateAccount());
                poolDetailDto.setPositionCode(e.getPositionCode());
                poolDetailDtos.add(poolDetailDto);
            });
            deliveryReplenishmentPoolDetailService.create(poolDetailDtos);
        }
        log.warn("++++++++++++++++++++++++++++++++++结束费用兑付/关闭明细");

        //兑付方式为货补时，费用结案审批通过后，TPM自动推送上账数据到DMS货补池；并记录上账状态（上账成功、上账失败），上账失败时可手工重新推送。
        List<MarketingAuditDetailVo> replenishmentList = auditDetailVos.stream().filter(e -> CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(replenishmentList)) {
            log.warn("++++++++++++++++++++++++++++++++++开始推送货补池");
            pushReplenishment(replenishmentList);
            marketingAuditDetailRepository.saveOrUpdateBatch(nebulaToolkitService.copyCollectionByWhiteList(replenishmentList, MarketingAuditDetailVo.class, MarketingAuditDetail.class, LinkedHashSet.class, ArrayList.class));
            log.warn("++++++++++++++++++++++++++++++++++结束推送货补池");
        }

        auditList.forEach(e -> e.setStatus(ProcessStatusEnum.PASS.getDictCode()));
        marketingAuditRepository.saveOrUpdateBatch(auditList);
    }


    /**
     * 生成费用兑付/关闭明细、冲销数据
     * 第一步：先推送DMS货补池
     * 第二步：生成费用兑付关闭明细
     * 第三步：生成冲销数据
     *
     * @param auditCodeList
     */
    @Override
    public void generateDeliveryReplenishmentAndWriteOff(List<String> auditCodeList) {
        if (CollectionUtil.isEmpty(auditCodeList)) {
            return;
        }
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditDetailRepository.findByCodes(auditCodeList);
        List<MarketingAudit> auditList = marketingAuditRepository.findByCodes(auditCodeList);

        //更新结案完结日期
        marketingAuditDetailRepository.updateAuditDate(auditCodeList);

        auditList.forEach(e -> e.setStatus(ProcessStatusEnum.PASS.getDictCode()));
        marketingAuditRepository.saveOrUpdateBatch(auditList);

        List<String> schemeDetailCodes = auditDetailVos.stream().filter(x -> BooleanEnum.TRUE.getCapital().equals(x.getBeFullAudit()))
                .map(x -> x.getSchemeDetailCode()).collect(Collectors.toList());

        List<MarketingAuditDetailVo> marketingSchemeDetailAuditAmountList = marketingAuditDetailRepository.findListBySchemeDetailCodes(schemeDetailCodes);
        Map<String, BigDecimal> auditAmountMap = marketingSchemeDetailAuditAmountList.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), l -> l.getAuditAmount()));
        List<DeliveryReplenishmentPoolDetailDto> poolDetailDtos = new ArrayList<>();
        Long count = auditDetailVos.stream().filter(x -> BooleanEnum.TRUE.getCapital().equals(x.getBeFullAudit())).count();
        List<String> ruleCodes = Lists.newArrayList();
        if (count > 0) {
            ruleCodes = generateCodeService.generateCode(DeliveryReplenishmentConstant.PREFIX_CODE, Integer.valueOf(count.toString()));
        } else {
            return;
        }
        //构建费用兑付/关闭明细数据
        Integer index = 0;
        for (MarketingAuditDetailVo e : auditDetailVos) {
            if (!BooleanEnum.TRUE.getCapital().equals(e.getBeFullAudit())) {
                continue;
            }
            String ruleCode = ruleCodes.get(index++);
            e.setRuleCode(ruleCode);
            BigDecimal operationAmount = auditAmountMap.get(e.getSchemeDetailCode());
            DeliveryReplenishmentPoolDetailDto poolDetailDto = new DeliveryReplenishmentPoolDetailDto();
            poolDetailDto.setRuleCode(ruleCode);
            poolDetailDto.setBusinessCode(e.getAuditDetailCode());
            poolDetailDto.setCustomerCode(e.getCustomerCode());
            poolDetailDto.setCustomerName(e.getCustomerName());
            poolDetailDto.setBeWriteOff(BooleanEnum.FALSE.getCapital());
            poolDetailDto.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
            poolDetailDto.setOperationType(DeliveryDetailTypeEnum.AUDIT.getCode());
            poolDetailDto.setDeliveryTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
            poolDetailDto.setOperationAmount(operationAmount);
            poolDetailDto.setSchemeDetailCode(e.getSchemeDetailCode());
            poolDetailDto.setAuditCode(e.getAuditCode());
            poolDetailDto.setAuditDetailCode(e.getAuditDetailCode());
            poolDetailDto.setSchemeCode(e.getSchemeCode());
            poolDetailDto.setCreateAccount(e.getCreateAccount());
            poolDetailDto.setPositionCode(e.getPositionCode());
            poolDetailDtos.add(poolDetailDto);
        }
        //构建冲销数据
        List<WithHoldingWriteOffDto> dtoList = new ArrayList<>();
        auditDetailVos.forEach(e -> {
            if (BooleanEnum.TRUE.getCapital().equals(e.getBeFullAudit())) {
                WithHoldingWriteOffDto dto = new WithHoldingWriteOffDto();
                dto.setActivitiesDetailCode(e.getSchemeDetailCode());
                dto.setWriteOffType(WriteOffTypeEnum.AUDIT.getDictCode());
                dto.setWriteOffAmount(e.getAuditAmount());
                dto.setBeFullAudit(e.getBeFullAudit());
                dto.setAuditCreateAccount(e.getCreateAccount());
                dto.setPositionCode(e.getPositionCode());
                dto.setSourceCode(e.getAuditDetailCode());
                dto.setRuleCode(e.getRuleCode());
                dtoList.add(dto);
            }
        });


        //生成费用兑付/关闭明细、冲销数据、修改费用兑付/关闭明细状态
        CalCloseReversComponent calCloseReversComponent = ApplicationContextHolder.getContext().getBean(CalCloseReversComponent.class);
        calCloseReversComponent.generateDeliveryReplenishmentList(poolDetailDtos);
        Future future = calCloseReversComponent.generateWriteOff(dtoList);
        try {
            future.get();
            //修改
            deliveryReplenishmentPoolDetailService.updateDeliveryReplenishmentPoolDetailStatus(ruleCodes);
        } catch (Exception e) {
            log.error("生成冲销单据失败,失败原因:{},{}", e, e.getMessage());
        }

    }


    @Autowired(required = false)
    private WithHoldingWriteOffService withHoldingWriteOffService;


    /**
     * 自动结案和审批通过需要调用
     *
     * @param auditCodeList
     */
    @Override
    public void pushDmsReplenishmentList(List<String> auditCodeList) {
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditDetailRepository.findByCodes(auditCodeList);
        //兑付方式为货补时，费用结案审批通过后，TPM自动推送上账数据到DMS货补池；并记录上账状态（上账成功、上账失败），上账失败时可手工重新推送。
        List<MarketingAuditDetailVo> replenishmentList = auditDetailVos.stream().filter(e -> CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(replenishmentList)) {
            log.warn("++++++++++++++++++++++++++++++++++开始推送货补池");
            pushReplenishment(replenishmentList);
            marketingAuditDetailRepository.saveOrUpdateBatch(nebulaToolkitService.copyCollectionByWhiteList(replenishmentList, MarketingAuditDetailVo.class, MarketingAuditDetail.class, LinkedHashSet.class, ArrayList.class));
            log.warn("++++++++++++++++++++++++++++++++++结束推送货补池");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approvedCreateDeliveryReplenishmentPool(String code) {

        List<MarketingAuditDetailVo> replenishmentAuditDetailVos = marketingAuditDetailRepository.findByCodes(Lists.newArrayList(code));
        //兑付方式为货补时，费用结案审批通过后，TPM自动推送上账数据到DMS货补池；并记录上账状态（上账成功、上账失败），上账失败时可手工重新推送。
        List<MarketingAuditDetailVo> replenishmentList = replenishmentAuditDetailVos.stream()
                .filter(e -> CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(replenishmentList)) {
            log.warn("++++++++++++++++++++++++++++++++++开始推送货补池");
            pushReplenishment(replenishmentList);
            marketingAuditDetailRepository.saveOrUpdateBatch(nebulaToolkitService.copyCollectionByWhiteList(replenishmentList, MarketingAuditDetailVo.class, MarketingAuditDetail.class, LinkedHashSet.class, ArrayList.class));
            log.warn("++++++++++++++++++++++++++++++++++结束推送货补池");
        }

        //判断是否完全结案 如果完全结案的话 进行金额对比 对比计提金额+计提前费用兑付金额
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditRepository.findListByApprovedAndFullAudit(code);
        if (CollectionUtils.isEmpty(auditDetailVos)) {
            return;
        }
        List<String> schemeDetailCodes = auditDetailVos.stream().map(x -> x.getSchemeDetailCode()).collect(Collectors.toList());
        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesPass(schemeDetailCodes);
        List<WithHoldingVo> withHoldingVoList = withHoldingService.findListBySchemeDetailCodes(schemeDetailCodes);
        Map<String, BigDecimal> feeCashMap = feeCashDetailVos.stream().collect(Collectors.groupingBy(x -> x.getSchemeDetailCode(),
                Collectors.mapping(l -> l.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, BigDecimal> withholdingMap = withHoldingVoList.stream().collect(Collectors.groupingBy(x -> x.getActivitiesDetailCode(),
                Collectors.mapping(l -> l.getActualAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, BigDecimal> auditDetailMap = auditDetailVos.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), k -> k.getAuditAmount()));
        List<String> schemeDetailCodeList = Lists.newArrayList();
        Map<String, BigDecimal> schemeDetailCodeAmountMap = Maps.newHashMap();
        schemeDetailCodes.forEach(x -> {
            BigDecimal auditAmount = auditDetailMap.getOrDefault(x, BigDecimal.ZERO);
            BigDecimal compareAmount = feeCashMap.getOrDefault(x, BigDecimal.ZERO);
            compareAmount = compareAmount.add(withholdingMap.getOrDefault(x, BigDecimal.ZERO));
            if (compareAmount.compareTo(auditAmount) > 0) {
                schemeDetailCodeList.add(x);
                schemeDetailCodeAmountMap.put(x, compareAmount.subtract(auditAmount));
            }
        });
        if (CollectionUtils.isEmpty(schemeDetailCodeList)) {
            return;
        }
        List<MarketingAuditDetailVo> auditDetailVoList = marketingAuditDetailRepository.findDetailListBySchemeDetailCodes(schemeDetailCodeList);
        //更新结案完结日期
        marketingAuditDetailRepository.updateAuditDate(schemeDetailCodeList);
        //结案冲销
        log.warn("++++++++++++++++++++++++++++++++++开始结案冲销");
        List<WithHoldingWriteOffDto> dtoList = new ArrayList<>();
        auditDetailVoList.forEach(e -> {
            if (BooleanEnum.TRUE.getCapital().equals(e.getBeFullAudit())) {
                WithHoldingWriteOffDto dto = new WithHoldingWriteOffDto();
                dto.setActivitiesDetailCode(e.getSchemeDetailCode());
                dto.setWriteOffType(WriteOffTypeEnum.AUDIT.getDictCode());
                dto.setWriteOffAmount(e.getAuditAmount());
                dto.setBeFullAudit(e.getBeFullAudit());
                dto.setAuditCreateAccount(e.getCreateAccount());
                dto.setPositionCode(e.getPositionCode());
                dto.setSourceCode(e.getAuditDetailCode());
                dtoList.add(dto);
            }
        });

        Map<String, List<WithHoldingWriteOffVo>> schemeDetailMap = withHoldingWriteOffSendHecService.writeOff(dtoList);
        log.warn("++++++++++++++++++++++++++++++++++结束结案冲销");


        log.warn("++++++++++++++++++++++++++++++++++开始生成费用兑付/关闭明细");
        List<DeliveryReplenishmentPoolDetailDto> poolDetailDtos = new ArrayList<>();
        auditDetailVoList.forEach(e -> {
            DeliveryReplenishmentPoolDetailDto poolDetailDto = new DeliveryReplenishmentPoolDetailDto();
            poolDetailDto.setBusinessCode(e.getAuditDetailCode());
            poolDetailDto.setCustomerCode(e.getCustomerCode());
            poolDetailDto.setCustomerName(e.getCustomerName());
            poolDetailDto.setBeWriteOff(BooleanEnum.TRUE.getCapital());
            poolDetailDto.setWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
            poolDetailDto.setOperationAmount(schemeDetailCodeAmountMap.get(e.getSchemeDetailCode()));

            poolDetailDto.setOperationType(DeliveryDetailTypeEnum.AUDIT.getCode());
            poolDetailDto.setDeliveryTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
            poolDetailDto.setSchemeDetailCode(e.getSchemeDetailCode());
            poolDetailDto.setAuditCode(e.getAuditCode());
            poolDetailDto.setAuditDetailCode(e.getAuditDetailCode());
            poolDetailDto.setSchemeCode(e.getSchemeCode());
            poolDetailDto.setCreateAccount(e.getCreateAccount());
            poolDetailDto.setPositionCode(e.getPositionCode());
            poolDetailDtos.add(poolDetailDto);
        });
        deliveryReplenishmentPoolDetailService.create(poolDetailDtos);

        log.warn("++++++++++++++++++++++++++++++++++结束费用兑付/关闭明细");
    }

    /**
     * 票扣生成费用兑付
     *
     * @param codes
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createFeeCash(List<String> codes) {
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditDetailRepository.findByCodes(codes);
        List<MarketingAudit> auditList = marketingAuditRepository.findByCodes(codes);
        List<MarketingAuditBillDetailVo> ticketBillList = marketingAuditBillDetailRepository.findByCodes(codes);
        //客户的“合作类型”为“经销商”或“直采”且兑付方式为“票扣”类费用且“使用部门”上对应“票扣兑付方式”为“自动兑付”，在结案流程完结时自动生成费用兑付单据
        Set<String> belongDepartmentCodeList = auditDetailVos.stream().map(e -> e.getBelongDepartmentCode()).collect(Collectors.toSet());
        Map<String, String> orgMap = orgVoService.findByOrgCodes(new ArrayList<>(belongDepartmentCodeList)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), e -> Optional.ofNullable(e.getTicketCashType()).orElse(""), (a, b) -> a));
        List<MarketingAuditDetailVo> ticketList = auditDetailVos.stream().filter(e -> CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(e.getCashType()) &&
                ("1".equals(e.getHzlx()) || "2".equals(e.getHzlx())) &&
                TicketCashTypeEnum.AUTO.getCode().equals(orgMap.get(e.getBelongDepartmentCode()))).collect(Collectors.toList());
        Date nowDate = new Date();
        if (!CollectionUtils.isEmpty(ticketList)) {
            Set<String> customerCodes = ticketList.stream().map(MarketingAuditDetailVo::getCustomerCode).collect(Collectors.toSet());
            List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodes));
            if (!CollectionUtils.isEmpty(customerVos)) {
                Map<String, CustomerVo> customerVoMap = customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity(), (a, b) -> a));
                List<MarketingAuditDetailVo> detailVos = ticketList.stream().filter(e -> customerVoMap.containsKey(e.getCustomerCode())).collect(Collectors.toList());
                List<MarketingAuditBillDetailVo> billVos = ticketBillList.stream().filter(e -> customerVoMap.containsKey(e.getCustomerCode())).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(detailVos) && (detailVos.stream().map(e -> e.getAuditAmount()).reduce(BigDecimal.ZERO, BigDecimal::add)).compareTo(BigDecimal.ZERO) != 0) {
                    log.warn("++++++++++++++++++++++++++++++++++开始生成费用兑付");
                    List<MarketingAuditDetailVo> updateAuditDetailVoList = new ArrayList<>();
                    Map<String, List<MarketingAuditDetailVo>> detailMap = detailVos.stream().collect(Collectors.groupingBy(e -> e.getAuditCode()));
                    Map<String, List<MarketingAuditBillDetailVo>> billMap = billVos.stream().collect(Collectors.groupingBy(e -> e.getAuditCode()));
                    Map<String, MarketingAudit> auditMap = auditList.stream().filter(e -> detailMap.containsKey(e.getAuditCode())).collect(Collectors.toMap(e -> e.getAuditCode(), Function.identity(), (a, b) -> a));
                    List<String> checkCash = feeCashDetailService.findByAuditCustomer(codes, new ArrayList<>(customerCodes)).stream().map(e -> e.getAuditCode() + e.getCustomerCode()).collect(Collectors.toList());
                    auditMap.forEach((k, v) -> {
                        //按客户+公司拆分
                        List<MarketingAuditDetailVo> auditDetailList = detailMap.get(k);
                        Map<String, List<MarketingAuditDetailVo>> listMap = auditDetailList.stream().collect(Collectors.groupingBy(e -> e.getCompanyCode() + "," + e.getCustomerCode()));
                        List<MarketingAuditBillDetailVo> auditBillList = billMap.get(k);
                        Map<String, List<MarketingAuditBillDetailVo>> billListMap = auditBillList.stream().collect(Collectors.groupingBy(e -> e.getCompanyCode() + "," + e.getCustomerCode()));

                        //结案票扣生成批次号
                        List<String> btCodes = generateCodeService.generateCode(MarketingAuditConstant.PREFIX_CODE_BT, billListMap.size());
                        AtomicInteger mapIndex = new AtomicInteger(0);
                        billListMap.forEach((bk, bv) -> {
                            bv.forEach(e -> e.setBtNo(btCodes.get(mapIndex.get())));
                            mapIndex.getAndIncrement();
                        });

                        if (!CollectionUtils.isEmpty(billListMap)) {
                            log.error("=============================票扣明细key：" + billListMap.keySet());
                        } else {
                            log.error("=============================票扣明细为空");
                        }

                        for (Map.Entry<String, List<MarketingAuditDetailVo>> entry : listMap.entrySet()) {
                            String[] split = entry.getKey().split(",");
                            //不生成重复的兑付
                            if (checkCash.contains(k + split[1])) {
                                continue;
                            }

                            FeeCashDto feeCash = new FeeCashDto();
                            feeCash.setCreateAccount(v.getCreateAccount());
                            feeCash.setCreateName(v.getCreateName());
                            feeCash.setCreateTime(nowDate);
                            feeCash.setModifyAccount(v.getCreateAccount());
                            feeCash.setModifyName(v.getCreateName());
                            feeCash.setModifyTime(nowDate);

                            feeCash.setCashName(v.getAuditName());
                            feeCash.setOrgCode(v.getOrgCode());
                            feeCash.setOrgName(v.getOrgName());
                            feeCash.setPositionCode(v.getPositionCode());
                            feeCash.setCashMethod(CashMethodEnum.TICKET_BUCKLE.getDictCode());
                            feeCash.setCashType(CashTypeEnum.FEE.getDictCode());
                            feeCash.setAuditCode(v.getAuditCode());
                            feeCash.setAuditName(v.getAuditName());
                            feeCash.setStatus(ProcessStatusEnum.PASS.getDictCode());
                            feeCash.setCompanyCode(split[0]);
                            feeCash.setCustomerCode(split[1]);
                            CustomerVo customerVo = customerVoMap.get(split[1]);
                            feeCash.setCustomerName(customerVo.getCustomerName());
                            feeCash.setDockingName(String.join(",", Optional.ofNullable(customerVo.getDockingList()).orElse(new ArrayList<>()).stream().map(CustomerDockingVo::getFullName).collect(Collectors.toSet())));
                            Collection<FeeCashDetailDto> feeCashDetailDtos = nebulaToolkitService.copyCollectionByWhiteList(entry.getValue(), MarketingAuditDetailVo.class, FeeCashDetailDto.class, LinkedHashSet.class, ArrayList.class);
                            AtomicReference<BigDecimal> amount = new AtomicReference<>(BigDecimal.ZERO);
                            feeCashDetailDtos.forEach(e -> {
                                e.setCreateAccount(v.getCreateAccount());
                                e.setCreateName(v.getCreateName());
                                e.setCreateTime(nowDate);
                                e.setModifyAccount(v.getCreateAccount());
                                e.setModifyName(v.getCreateName());
                                e.setModifyTime(nowDate);
                                e.setCashAmount(e.getAuditAmount());
                                e.setThisCashAmount(e.getAuditAmount());
                                //自动兑付，默认为完全兑付
                                e.setBeCash(BooleanEnum.FALSE.getCapital());
                                amount.set(amount.get().add(e.getThisCashAmount()));
                            });
                            feeCash.setDetailList(new ArrayList<>(feeCashDetailDtos));
                            feeCash.setTotalCashAmount(amount.get());
                            feeCash.setTotalApplyAmount(amount.get());

                            log.error("=============================item：" + entry.getKey());

                            Collection<FeeCashTicketDto> feeCashTicketDtos = nebulaToolkitService.copyCollectionByWhiteList(billListMap.get(entry.getKey()), MarketingAuditBillDetailVo.class, FeeCashTicketDto.class, LinkedHashSet.class, ArrayList.class);
                            feeCash.setTicketList(new ArrayList<>(feeCashTicketDtos));
                            List<String> feeCashCodes = feeCashService.create(feeCash, true, null, null, null, null, null, null, true, false);
                            feeCashService.oaCallback(feeCash.getCashCode(), ProcessStatusEnum.PASS.getDictCode(), true);
                            entry.getValue().forEach(e -> e.setAutoCashCode(feeCashCodes.get(0)));
                            updateAuditDetailVoList.addAll(entry.getValue());
                        }
                    });

                    Collection<MarketingAuditBillDetail> billDetails = nebulaToolkitService.copyCollectionByWhiteList(ticketBillList, MarketingAuditBillDetailVo.class, MarketingAuditBillDetail.class, LinkedHashSet.class, ArrayList.class);
                    marketingAuditBillDetailRepository.saveOrUpdateBatch(billDetails);
                    if (!CollectionUtils.isEmpty(updateAuditDetailVoList)) {
                        Collection<MarketingAuditDetail> updateAuditDetailList = nebulaToolkitService.copyCollectionByWhiteList(updateAuditDetailVoList, MarketingAuditDetailVo.class, MarketingAuditDetail.class, LinkedHashSet.class, ArrayList.class);
                        marketingAuditDetailRepository.saveOrUpdateBatch(updateAuditDetailList);
                    }
                    log.warn("++++++++++++++++++++++++++++++++++结束生成费用兑付");
                }
            }
        }
    }

    /**
     * 推送货补池
     *
     * @param codes
     */
    @Override
    public void push(List<String> codes) {
        Validate.notEmpty(codes, "结案明细编码不能为空");
        List<MarketingAuditDetail> details = marketingAuditDetailRepository.findByAuditDetailCodes(codes);
        List<MarketingAuditDetail> replenishmentList = details.stream().filter(e -> CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType()) &&
                (AccountStatusEnum.FAIL.getDictCode().equals(e.getAccountStatus()) || AccountStatusEnum.PENDING.getDictCode().equals(e.getAccountStatus()))).collect(Collectors.toList());
        Validate.notEmpty(replenishmentList, "未找到可以上账的明细数据");
        List<MarketingAuditDetailVo> replenishmentVoList = new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(replenishmentList, MarketingAuditDetail.class, MarketingAuditDetailVo.class, LinkedHashSet.class, ArrayList.class));
        pushReplenishment(replenishmentVoList);
        marketingAuditDetailRepository.saveOrUpdateBatch(nebulaToolkitService.copyCollectionByWhiteList(replenishmentVoList, MarketingAuditDetailVo.class, MarketingAuditDetail.class, LinkedHashSet.class, ArrayList.class));
    }

    /**
     * 自动兑付补偿
     *
     * @param codes
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoCashReimburse(List<String> codes) {
        Validate.notEmpty(codes, "结案明细编码不能为空");
        List<MarketingAuditDetail> details = marketingAuditDetailRepository.findByAuditDetailCodes(codes);
        //客户的“合作类型”为“经销商”或“直采”且兑付方式为“票扣”类费用且“使用部门”上对应“票扣兑付方式”为“自动兑付”，在结案流程完结时自动生成费用兑付单据
        Set<String> belongDepartmentCodeList = details.stream().map(e -> e.getBelongDepartmentCode()).collect(Collectors.toSet());
        Set<String> auditSet = details.stream().map(e -> e.getAuditCode()).collect(Collectors.toSet());
        List<MarketingAudit> auditList = marketingAuditRepository.findByCodes(new ArrayList<>(auditSet)).stream().filter(e -> ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus())).collect(Collectors.toList());
        Validate.notEmpty(auditList, "数据不符合自动兑付条件");

        Map<String, String> orgMap = orgVoService.findByOrgCodes(new ArrayList<>(belongDepartmentCodeList)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), e -> Optional.ofNullable(e.getTicketCashType()).orElse(""), (a, b) -> a));
        List<MarketingAuditDetail> ticketList = details.stream().filter(e -> CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(e.getCashType()) &&
                ("1".equals(e.getHzlx()) || "2".equals(e.getHzlx())) &&
                TicketCashTypeEnum.AUTO.getCode().equals(orgMap.get(e.getBelongDepartmentCode()))).collect(Collectors.toList());
        Validate.notEmpty(ticketList, "数据不符合自动兑付条件");

        Set<String> customerCodes = ticketList.stream().map(MarketingAuditDetail::getCustomerCode).collect(Collectors.toSet());
        List<String> checkCash = feeCashDetailService.findByAuditCustomer(new ArrayList<>(auditSet), new ArrayList<>(customerCodes)).stream().map(e -> e.getAuditCode() + e.getCustomerCode()).collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isEmpty(checkCash), "该结案明细已自动兑付");

        List<MarketingAuditBillDetailVo> ticketBillList = marketingAuditBillDetailRepository.findByCodes(new ArrayList<>(auditSet));


        Map<String, String> detialMap = ticketList.stream().collect(Collectors.toMap(e -> e.getCompanyCode() + "," + e.getCustomerCode() + "," + e.getYears(),
                e -> e.getCompanyCode() + "+" + e.getCustomerName() + "+" + e.getYears(), (a, b) -> a));
        if (CollectionUtils.isEmpty(ticketBillList)) {
            List<String> detailSetName = detialMap.entrySet().stream().map(e -> e.getValue()).collect(Collectors.toList());
            Validate.isTrue(false, "公司+客户+年月【%s】没有生成票扣明细", String.join("；", detailSetName));
        }
        //校验是否全部经销商或直采客户的票扣活动都生成了票扣明细
        Map<String, String> billMap = ticketBillList.stream().collect(Collectors.toMap(e -> e.getCompanyCode() + "," + e.getCustomerCode() + "," + e.getYears(),
                e -> e.getCompanyCode() + "+" + e.getCustomerName() + "+" + e.getYears(), (a, b) -> a));
        if (billMap.size() != detialMap.size()) {
            Set<String> billSet = billMap.keySet();
            Set<String> detailSet = detialMap.keySet();
            detailSet.removeAll(billSet);
            List<String> detailSetName = detialMap.entrySet().stream().filter(e -> detailSet.contains(e.getKey())).map(e -> e.getValue()).collect(Collectors.toList());
            Validate.isTrue(false, "公司+客户+年月【%s】没有生成票扣明细", String.join("；", detailSetName));
        }

        createFeeCash(new ArrayList<>(auditSet));
    }

    /**
     * 推送货补池
     *
     * @param replenishmentList
     */
    public void pushReplenishment(List<MarketingAuditDetailVo> replenishmentList) {
        if (CollectionUtil.isEmpty(replenishmentList)) {
            return;
        }
        //根据品项查业态
        Set<String> itemCodes = replenishmentList.stream().map(MarketingAuditDetailVo::getItemCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, String> businessTypeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(itemCodes)) {
            List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(itemCodes);
            businessTypeMap.putAll(productPhaseVos.stream().filter(e -> StringUtils.isNotBlank(e.getZeroLevelCode()))
                    .collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, ProductPhaseVo::getZeroLevelCode, (v1, v2) -> v1)));
        }

        replenishmentList.forEach(e -> {
            ReplenishmentPoolDto poolDto = new ReplenishmentPoolDto();
            poolDto.setCustomerCode(e.getCustomerCode());
            poolDto.setCustomerName(e.getCustomerName());
            poolDto.setCompanyCode(e.getCompanyCode());
            poolDto.setErpCode(e.getErpCode());
            poolDto.setProductGroupCode(e.getProductGroupCode());
            poolDto.setChannelCode(e.getChannelCode());
            poolDto.setAccountAmount(e.getAuditAmount());
            poolDto.setReplenishmentPoolType(ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode());
            poolDto.setOperationType(ReplenishmentPoolOperationType.ACTIVITY_ACCOUNT.getCode());
//                poolDto.setBusinessType("CW01");
            if (StringUtils.isBlank(e.getItemCode())) {
                poolDto.setBusinessType(MarketingAuditConstant.DEFAULT_ZERO_LEVEL_CODE);
            } else {
                poolDto.setBusinessType(businessTypeMap.getOrDefault(e.getItemCode(), MarketingAuditConstant.DEFAULT_ZERO_LEVEL_CODE));
            }
            poolDto.setBusinessCode(e.getAuditDetailCode());
            poolDto.setOrgCode(e.getOrgCode());
            poolDto.setPositionCode(e.getPositionCode());
            poolDto.setActivityType(e.getDetailName());
            boolean lock = false;
            try {
                lock = this.replenishmentPoolLockService.lock(poolDto);
                Validate.isTrue(lock, "该客户【%s】货补池正在被操作，请稍后再试", poolDto.getCustomerCode());
                replenishmentPoolVoService.handelAccount(poolDto);
                e.setAccountStatus(AccountStatusEnum.ON.getDictCode());
                e.setErrMsg("上账成功!");
            } catch (Exception exception) {
                String msg = "推送货补池错误：" + exception.getMessage();
                log.error(msg, exception);
                if (StringUtil.isNotEmpty(msg)
                        && msg.length() > 500) {
                    msg = msg.substring(0, 499);
                }
                e.setErrMsg(msg);
                e.setAccountStatus(AccountStatusEnum.FAIL.getDictCode());
            } finally {
                if (lock && this.replenishmentPoolLockService.isLock(poolDto)) {
                    this.replenishmentPoolLockService.unLock(poolDto);
                }
            }
        });
    }

    /**
     * OA回调
     * 留痕：免得后面扯
     * 2024-07-27  产品说回调的状态修改不能和业务挂一起，业务处理失败不能影响回调状态的修改
     * 所以，下面我要改一下逻辑，取消这最外层的事务，将更新状态和逻辑处理事务隔离开
     *
     * @param code
     * @param status
     */
    @Override
    public void oaCallback(String code, String status) {
        // 修改状态，不要事务，直接更新
        MarketingAudit entity = marketingAuditRepository.findByCode(code);
        if (entity == null || StringUtils.equalsAny(entity.getStatus(), ProcessStatusEnum.PASS.getDictCode())) {
            return;
        }
        entity.setStatus(status);
        marketingAuditRepository.saveOrUpdate(entity);

        // 状态修改完之后执行下面的判断
        if (ProcessStatusEnum.PASS.getDictCode().equals(status)) {
            //更新结案完结日期
            marketingAuditDetailRepository.updateAuditDate(Lists.newArrayList(code));
            //结案明细如果需要上账的明细改为待上账
            marketingAuditDetailRepository.updateAccountStatus(Collections.singletonList(code), AccountStatusEnum.PENDING.getDictCode());
            // 如果是审批通过，则进行相关的业务逻辑处理
            try {
                //回写活动
                log.warn("++++++++++++++++++++++++++++++++++开始回写活动");
                List<MarketingAuditDetailVo> auditDetailVos = marketingAuditDetailRepository.findByCodes(Collections.singletonList(code));
                List<MarketingPlanCaseVo> marketingPlanCaseVos = marketingPlanCaseService.findByCaseCodes(auditDetailVos.stream().map(MarketingAuditDetailVo::getSchemeDetailCode).collect(Collectors.toList()));
                this.updateSchemeDetail(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(auditDetailVos, MarketingAuditDetailVo.class, MarketingAuditDetailDto.class, LinkedHashSet.class, ArrayList.class)),
                        marketingPlanCaseVos, false);
                log.warn("++++++++++++++++++++++++++++++++++结束回写活动");
                log.warn("++++++++++++++++++++++++++++++++++开始结案审批");
                //推送结案数据到dms
                this.pushDmsReplenishmentList(Lists.newArrayList(code));
                //写入费用兑付/关闭明细、冲销数据
                this.generateDeliveryReplenishmentAndWriteOff(Lists.newArrayList(code));
//                marketingAuditService.approved(Collections.singletonList(code));
                this.createFeeCash(Collections.singletonList(code));
                log.warn("++++++++++++++++++++++++++++++++++结束结案审批");
            } catch (Exception e) {
                // 不做处理
                log.error(e.getMessage(), e);
            }

        }
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public void recover(String code, String remark) {
        MarketingAudit entity = marketingAuditRepository.findByCode(code);
        if (marketingAuditOaService.oaWithdraw(code, remark)) {
            entity.setStatus(ProcessStatusEnum.RECOVER.getDictCode());
            marketingAuditRepository.saveOrUpdate(entity);
        } else {
            Validate.isTrue(false, "流程撤回失败");
        }
    }

    /**
     * 查询审批中的结案
     *
     * @return
     */
    @Override
    public List<MarketingAuditVo> findCommit(String yearMonthLy) {
        return marketingAuditRepository.findCommit(yearMonthLy);
    }

    /**
     * 定时查询DMS费用池回写兑付金额
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void replenishmentPoolCashAmount() {
        replenishmentPoolCashAmountExcute();

        //调用获取发货费用
        deliveryReplenishmentPoolDetailService.generateDeliveryReplenishment();
    }

    /**
     * 查询DMS费用池回写兑付金额
     */
    public void replenishmentPoolCashAmountExcute() {
        //查询兑付方式=货补，且“是否完全兑付”≠“是”的审批通过的结案明细
        List<MarketingAuditDetail> entities = marketingAuditDetailRepository.findForReplenishmentPool();
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        List<String> auditDetailCodes = entities.stream().map(e -> e.getAuditDetailCode()).collect(Collectors.toList());
        Map<String, BigDecimal> deliveryAmountMap = replenishmentPoolDetailVoService.findDeliveryAmountByActivityCodes(auditDetailCodes);
        if (CollectionUtils.isEmpty(deliveryAmountMap)) {
            return;
        }
        Set<String> schemeDetailCodes = new HashSet<>();
        for (MarketingAuditDetail detail : entities) {
            BigDecimal amount = deliveryAmountMap.get(detail.getAuditDetailCode());
            if (amount == null) {
                continue;
            }
            if (amount.compareTo(detail.getAuditAmount()) >= 0) {
                detail.setBeFullCash(BooleanEnum.TRUE.getCapital());
            }
            detail.setCashAmount(amount);
            schemeDetailCodes.add(detail.getSchemeDetailCode());
        }
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return;
        }
        marketingAuditDetailRepository.updateBatchById(entities);
        //查询活动明细编码对应的结案明细，汇总兑付金额，更新活动明细上
        List<MarketingAuditDetail> auditDetailList = marketingAuditDetailRepository.findBySchemeDetailCodes(new ArrayList<>(schemeDetailCodes));
        Map<String, List<MarketingAuditDetail>> auditDetailMap = auditDetailList.stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode()));
        List<MarketingPlanCaseVo> marketingPlanCaseVos = marketingPlanCaseService.findByCaseCodes(new ArrayList<>(schemeDetailCodes));

        marketingPlanCaseVos.forEach(e -> {
            List<MarketingAuditDetail> auditDetails = auditDetailMap.get(e.getSchemeDetailCode());
            //活动下的结案明细要有一条为完全结案，且这个活动的所有结案明细为完全兑付，此时活动为完全兑付
            AtomicBoolean beFullAudit = new AtomicBoolean(false);
            AtomicBoolean beFullCash = new AtomicBoolean(true);
            AtomicReference<BigDecimal> cashAmount = new AtomicReference<>(BigDecimal.ZERO);
            auditDetails.forEach(d -> {
                if (BooleanEnum.TRUE.getCapital().equals(d.getBeFullAudit())) {
                    beFullAudit.set(true);
                }
                if (!(BooleanEnum.TRUE.getCapital().equals(d.getBeFullCash()))) {
                    beFullCash.set(false);
                }
                cashAmount.set(cashAmount.get().add((d.getCashAmount() == null ? BigDecimal.ZERO : d.getCashAmount())));
            });
            e.setCashAmount(cashAmount.get());
            String cashStatus = CashStatusEnum.NOT_CASH.getCode();
            if (beFullCash.get() && beFullAudit.get()) {
                cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
            } else if (e.getCashAmount().compareTo(BigDecimal.ZERO) > 0) {
                cashStatus = CashStatusEnum.PART_CASH.getCode();
            }
            e.setCashStatus(cashStatus);
        });
        //调用活动明细修改接口
        marketingPlanCaseService.rewriteEndCaseOrWithholding(marketingPlanCaseVos);
    }

    /**
     * 修改兑付方式
     *
     * @param auditDetailDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCashType(MarketingAuditDetailDto auditDetailDto) {
        Validate.notBlank(auditDetailDto.getCashType(), "兑付方式，不能为空！");
        Validate.notEmpty(auditDetailDto.getAuditDetailCodeList(), "结案明细编码集合，不能为空！");
        List<MarketingAuditDetail> auditDetailList = marketingAuditDetailRepository.findByAuditDetailCodes(auditDetailDto.getAuditDetailCodeList());
        if (CollectionUtils.isEmpty(auditDetailList)) {
            Validate.notEmpty(auditDetailList, "未找到对应的修改数据！");
        }

        List<String> auditDetailCodes = auditDetailList.stream().map(e -> e.getAuditDetailCode()).collect(Collectors.toList());
        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findByAuditDetailCodes(auditDetailCodes);
        Validate.isTrue(CollectionUtils.isEmpty(feeCashDetailVos), "不允许对已兑付的费用变更兑付方式！");

        List<String> schemeDetailCodes = auditDetailList.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
        Map<String, String> caseMap = marketingPlanCaseService.findByCaseCodes(schemeDetailCodes).stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), e -> e.getCaseType(), (a, b) -> a));

        List<String> detailCodes = auditDetailList.stream().map(MarketingAuditDetail::getDetailCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CostTypeDetailVo> costTypeDetailVos = costTypeDetailVoService.findByCodes(detailCodes);
        Map<String, Set<String>> payBysMap = costTypeDetailVos.stream().collect(Collectors.toMap(CostTypeDetailVo::getDetailCode, CostTypeDetailVo::getPayBys));

        // hzlx
        // 0-直营
        // 1-经销商
        // 2-直采
        // 3-垫资商
        auditDetailList.forEach(e -> {
            Validate.isTrue(!Arrays.asList(MarketingPlanCaseTypeEnum.matching_gift.getCode(), MarketingPlanCaseTypeEnum.material.getCode()).contains(caseMap.get(e.getSchemeDetailCode())),
                    "活动表单类型为“随单搭赠”或“周边物料”的活动的结案明细不允许修改兑付方式");
            Validate.isTrue(!CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType()),
                    "不允许变更兑付方式为“货补”的单据！");
            Set<String> cashTypeSet = payBysMap.get(e.getDetailCode());
            Validate.notEmpty(cashTypeSet, "费用项目【" + e.getDetailCode() + "】未配置任何兑付方式！");
            if (CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(e.getCashType())) {
                // 若【合作类型】为经销商或直采，不允许修改；【合作类型】为垫资商、直营可修改为当前【费用项目】所配置的除【票扣】以外的兑付方式
                Validate.isTrue(!Lists.newArrayList("1", "2").contains(e.getHzlx()), "客户合作类型=经销商、直采的不允许修改！");
                Validate.isTrue(cashTypeSet.contains(auditDetailDto.getCashType()), "不允许修改为当前费用项目所配置的以外的兑付方式！");
            } else if (CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(e.getCashType())) {
                if (Lists.newArrayList("1", "2").contains(e.getHzlx())) {
                    Validate.isTrue(!CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(auditDetailDto.getCashType()), "客户合作类型=经销商、直采的不允许修改为“票扣”！");
                    Validate.isTrue(cashTypeSet.contains(auditDetailDto.getCashType()), "不允许修改为当前费用项目所配置的以外的兑付方式！");
                } else {
                    Validate.isTrue(cashTypeSet.contains(auditDetailDto.getCashType()), "不允许修改为当前费用项目所配置的以外的兑付方式！");
                }
            } else if (CashMethodEnum.DEDUCTIONS.getDictCode().equals(e.getCashType())) {
                if (Lists.newArrayList("1", "2").contains(e.getHzlx())) {
                    Validate.isTrue(!CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(auditDetailDto.getCashType()), "客户合作类型=经销商、直采的不允许修改为“票扣”！");
                    Validate.isTrue(cashTypeSet.contains(auditDetailDto.getCashType()), "不允许修改为当前费用项目所配置的以外的兑付方式！");
                } else {
                    Validate.isTrue(cashTypeSet.contains(auditDetailDto.getCashType()), "不允许修改为当前费用项目所配置的以外的兑付方式！");
                }
            }
        });


        Collection<MarketingAuditDetailDto> originalList = this.nebulaToolkitService.copyCollectionByWhiteList(auditDetailList, MarketingAuditDetail.class, MarketingAuditDetailDto.class, LinkedHashSet.class, ArrayList.class);

        auditDetailList.forEach(e -> {
            e.setCashType(auditDetailDto.getCashType());
            if (CashMethodEnum.REPLENISHMENT.getDictCode().equals(auditDetailDto.getCashType())) {
                e.setAccountStatus(AccountStatusEnum.PENDING.getDictCode());
            }
        });
        marketingAuditDetailRepository.saveOrUpdateBatch(auditDetailList);
        //判断是否需要推送货补
        if (CashMethodEnum.REPLENISHMENT.getDictCode().equals(auditDetailDto.getCashType())) {
            push(auditDetailDto.getAuditDetailCodeList());
        }

        //编辑业务日志
        MarketingAuditDetailLogEventDto logEventDto = new MarketingAuditDetailLogEventDto();
        logEventDto.setOriginalList(new ArrayList<>(originalList));
        logEventDto.setNewestList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(auditDetailList, MarketingAuditDetail.class, MarketingAuditDetailDto.class, LinkedHashSet.class, ArrayList.class)));
        SerializableBiConsumer<MarketingAuditDetailLogEventListener, MarketingAuditDetailLogEventDto> onUpdate =
                MarketingAuditDetailLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, MarketingAuditDetailLogEventListener.class, onUpdate);
    }

    @Override
    public void modifyActivityPrepayRecord(OaCallbackDto dto) {
        MarketingAudit entity = marketingAuditRepository.findByCode(dto.getBusinessCode());
        if (entity == null) {
            return;
        }
        if (!StringUtils.equals(ProcessStatusEnum.PASS.getDictCode(), entity.getStatus())) {
            return;
        }
        List<MarketingAuditDetailVo> voList = marketingAuditDetailRepository.findByCodes(Collections.singletonList(dto.getBusinessCode()));
        voList = voList.stream().filter(e -> StringUtils.equals(BooleanEnum.TRUE.getCapital(), e.getBeFullAudit())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        List<String> activitiesDetailCodes = voList.stream().map(MarketingAuditDetailVo::getSchemeDetailCode).collect(Collectors.toList());
        //判断活动是否发生过预付（预付类型为“活动预付）
        List<ActivityPrepayDetailRecord> detailRecordList = activityPrepayDetailRecordRepository.findBySchemeDetailCodes(activitiesDetailCodes);
        detailRecordList = detailRecordList.stream().filter(e -> StringUtils.equals(TpmPrepayTypeEnum.activity.getCode(), e.getPrepayType())).collect(Collectors.toList());
        List<String> filterCodes = detailRecordList.stream().map(ActivityPrepayDetailRecord::getSchemeDetailCode).collect(Collectors.toList());
        List<String> prepayDetailCodes = detailRecordList.stream().map(ActivityPrepayDetailRecord::getPrepayDetailCode).collect(Collectors.toList());
        voList = voList.stream().filter(e -> filterCodes.contains(e.getSchemeDetailCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        activitiesDetailCodes = voList.stream().map(MarketingAuditDetailVo::getSchemeDetailCode).collect(Collectors.toList());
        //完全结案金额
        List<MarketingAuditDetailVo> auditedAmountList = activityPrepayDetailRecordRepository.findAuditedAmountBySchemeDetailCodes(activitiesDetailCodes);
        Map<String, BigDecimal> auditedAmountMap = auditedAmountList.stream().collect(Collectors.toMap(MarketingAuditDetailVo::getSchemeDetailCode, MarketingAuditDetailVo::getAuditedAmount));
        for (MarketingAuditDetailVo vo : voList) {
            vo.setAuditedAmount(auditedAmountMap.getOrDefault(vo.getSchemeDetailCode(), BigDecimal.ZERO));
        }
        activityPrepayDetailRecordService.operateLock(prepayDetailCodes, true);
        try {
            Map<String, List<ActivityPrepayDetailRecord>> detailRecordListMap =
                    detailRecordList.stream().collect(Collectors.groupingBy(ActivityPrepayDetailRecord::getSchemeDetailCode));
            List<ActivityPrepayDetailRecordItemDto> list = new ArrayList<>();
            for (MarketingAuditDetailVo vo : voList) {
                List<ActivityPrepayDetailRecord> prepayDetailRecords = detailRecordListMap.get(vo.getSchemeDetailCode());
                //完全结案审批通过时判断完全结案金额是否小于“预付金额-待结转-已结转”，若小于，则需更新该活动对应的预付明细跟踪数据的“待结转金额字段”（按“预付金额-待结转-已结转-完全结案”追加
                BigDecimal totalPrepayApplyAmount = prepayDetailRecords.stream().map(ActivityPrepayDetailRecord::getPrepayApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalPrepareCarryAmount = prepayDetailRecords.stream().map(ActivityPrepayDetailRecord::getPrepareCarryAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalCarriedAmount = prepayDetailRecords.stream().map(ActivityPrepayDetailRecord::getCarriedAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalPrepayAmount = totalPrepayApplyAmount.subtract(totalPrepareCarryAmount).subtract(totalCarriedAmount);
                //完全结案金额<预付金额 才更新待结转金额
                if (vo.getAuditedAmount().compareTo(totalPrepayAmount) >= 0) {
                    continue;
                }
                //更新预付明细的“待结转金额”时若寻到多条预付明细，待结转金额按预付时间从当前往历史顺序更新待结转金额，待结转金额不能大于预付明细的“实际支付金额-已结转金额”
                prepayDetailRecords.sort(Comparator.comparing(ActivityPrepayDetailRecord::getPrepayDetailCode).reversed());
                BigDecimal totalAmount = totalPrepayAmount.subtract(vo.getAuditedAmount());

                for (ActivityPrepayDetailRecord prepayDetailRecord : prepayDetailRecords) {
                    BigDecimal maxAmount = Optional.ofNullable(prepayDetailRecord.getPrepayApplyAmount()).orElse(BigDecimal.ZERO)
                            .subtract(Optional.ofNullable(prepayDetailRecord.getCarriedAmount()).orElse(BigDecimal.ZERO))
                            .subtract(Optional.ofNullable(prepayDetailRecord.getPrepareCarryAmount()).orElse(BigDecimal.ZERO));
                    BigDecimal amount = BigDecimal.ZERO;
                    if (maxAmount.compareTo(totalAmount) >= 0) {
                        amount = totalAmount;
                    } else {
                        amount = maxAmount;
                    }
                    totalAmount = totalAmount.subtract(amount);
//                    log.info("1 {} 2 {} 3{} 4 {} 5{}",maxAmount,totalAmount,amount,dto.getBusinessCode(),voList.size());
                    if (amount.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    //构建操作明细
                    ActivityPrepayDetailRecordItemDto itemDto = new ActivityPrepayDetailRecordItemDto();
                    itemDto.setPrepayCode(prepayDetailRecord.getPrepayCode());
                    itemDto.setPrepayName(prepayDetailRecord.getPrepayName());
                    itemDto.setPrepayDetailCode(prepayDetailRecord.getPrepayDetailCode());
                    itemDto.setSchemeCode(prepayDetailRecord.getSchemeCode());
                    itemDto.setSchemeName(prepayDetailRecord.getSchemeName());
                    itemDto.setSchemeDetailCode(prepayDetailRecord.getSchemeDetailCode());
                    itemDto.setType(ActivityPrepayDetailRecordOperateTypeEnum.ADD.getCode());
                    itemDto.setBusinessCode(vo.getAuditDetailCode());
                    itemDto.setBusinessName("完全结案审批通过");
                    itemDto.setAmount(amount);
                    list.add(itemDto);
                }
            }
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            //更新预付明细，生成操作记录
            activityPrepayDetailRecordService.operate(list);
        } finally {
            activityPrepayDetailRecordService.operateLock(prepayDetailCodes, false);
        }


    }

    /**
     * 是否展示票扣
     *
     * @param cacheKey
     * @return
     */
    @Override
    public String showTicket(String cacheKey) {
        String beShow = BooleanEnum.FALSE.getCapital();
        List<MarketingAuditDetailDto> auditDetailDtos = findCacheList(cacheKey);
        Set<String> belongDepartmentCodeList = auditDetailDtos.stream().map(e -> e.getBelongDepartmentCode()).collect(Collectors.toSet());
        Map<String, String> orgMap = orgVoService.findByOrgCodes(new ArrayList<>(belongDepartmentCodeList)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), e -> Optional.ofNullable(e.getTicketCashType()).orElse(""), (a, b) -> a));
        Set<String> customerCodeSet = auditDetailDtos.stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode()) && CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(e.getCashType()) &&
                        ("1".equals(e.getHzlx()) || "2".equals(e.getHzlx())) &&
                        TicketCashTypeEnum.AUTO.getCode().equals(orgMap.get(e.getBelongDepartmentCode())))
                .filter(Objects::nonNull)
                .map(e -> e.getCustomerCode()).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(customerCodeSet)) {
            List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodeSet)).stream().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(customerVos)) {
                beShow = BooleanEnum.TRUE.getCapital();
            }
        }
        return beShow;
    }

    /**
     * 按活动编码查询审批通过
     *
     * @param codes
     * @return
     */
    @Override
    public List<MarketingAuditDetailVo> findBySchemeDetailCodesAll(List<String> codes) {
        return marketingAuditDetailRepository.findBySchemeDetailCodesAll(codes);
    }


    @Override
    public List<MarketingAuditDetailVo> findAuditAmountBySchemeDetailCodes(List<String> schemeDetailCodes) {
        return marketingAuditDetailRepository.findAuditAmountBySchemeDetailCodes(schemeDetailCodes);
    }


    @Override
    public List<MarketingAuditDetailVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        return marketingAuditDetailRepository.findListBySchemeDetailCodes(schemeDetailCodes);
    }


    @Override
    public List<MarketingAuditDetailVo> findListBySchemeDetailCodesApproved(List<String> schemeDetailCodes) {
        return marketingAuditDetailRepository.findListBySchemeDetailCodesApproved(schemeDetailCodes);
    }

    @Override
    public void scheduleNotClosureDetailMsgPush() {
        RateLimiter rateLimiter = RateLimiter.create(10);
        List<DictDataVo> expenseItemFilterList = dictDataVoService.findByDictTypeCode("expense_item_filter_list");
        List<String> expenseItemCodes = expenseItemFilterList.stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
        List<MarketingAuditDetailVo> detailVos = marketingAuditDetailRepository.scheduleNotClosureDetailMsgPush();
        if (CollectionUtils.isEmpty(detailVos)) {
            log.info("没有临期结案数据需要推送");
            return;
        }
        detailVos = detailVos.stream().filter(x -> !expenseItemCodes.contains(x.getDetailCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(detailVos)) {
            log.info("过滤随单和周边物料数据后没有临期结案数据需要推送");
            return;
        }
        Set<String> userNames = detailVos.stream().map(MarketingAuditDetailVo::getActivityCreateAccount).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        String context = "当前方案明细%s距离完全结案规定时间，不足10天，请尽快完成结案";
        //接收人
        List<UserInfoVo> userInfoVos = userInfoVoService.findByUserNames(userNames);
        Map<String, String> userOaIdMap = userInfoVos.stream().collect(Collectors.toMap(UserInfoVo::getUserName, UserInfoVo::getOaId, (a, b) -> a));

        detailVos.forEach(k -> {
            rateLimiter.tryAcquire();
            String oaId = userOaIdMap.get(k.getActivityCreateAccount());
            if (StringUtils.isEmpty(oaId)) {
                log.warn("用户不存在或已离职被禁用,account: {}", k.getActivityCreateAccount());
                return;
            }
            RyOaMsgVo msgVo = new RyOaMsgVo();
            msgVo.setMessageId(UuidCrmUtil.randomUuid());
            msgVo.setCode(msgSource);
            msgVo.setTitle("完全结案提醒");
            msgVo.setUserIdList(oaId);
            msgVo.setContext(String.format(context, k.getSchemeDetailCode()));
            msgVo.setCreater(oaId);
            try {
                ryOaProcessService.sendMsg(msgVo);
            } catch (Exception e) {
                log.error("完全结案提醒发送钉钉消息异常!", e);
            }
        });
    }

    @Override
    public void scheduleNotReplenishmentMsgPush() {
        RateLimiter rateLimiter = RateLimiter.create(10);
        List<DictDataVo> expenseItemFilterList = dictDataVoService.findByDictTypeCode("expense_item_filter_list");
        List<String> expenseItemCodes = expenseItemFilterList.stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
        List<MarketingAuditDetailVo> detailVos = marketingAuditDetailRepository.scheduleNotReplenishmentMsgPush();
        if (CollectionUtils.isEmpty(detailVos)) {
            log.info("没有临期兑付数据需要推送");
            return;
        }
        detailVos = detailVos.stream().filter(x -> !expenseItemCodes.contains(x.getDetailCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(detailVos)) {
            log.info("过滤随单和周边物料数据后没有临期结案数据需要推送");
            return;
        }
        Set<String> userNames = detailVos.stream().map(MarketingAuditDetailVo::getActivityCreateAccount).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        String context = "当前方案明细%s距离兑付规定时间，不足10天，请尽快完成兑付";
        //接收人
        List<UserInfoVo> userInfoVos = userInfoVoService.findByUserNames(userNames);
        Map<String, String> userOaIdMap = userInfoVos.stream().collect(Collectors.toMap(UserInfoVo::getUserName, UserInfoVo::getOaId, (a, b) -> a));

        detailVos.forEach(k -> {
            rateLimiter.tryAcquire();
            String oaId = userOaIdMap.get(k.getActivityCreateAccount());
            if (StringUtils.isEmpty(oaId)) {
                log.warn("用户不存在或已离职被禁用,account: {}", k.getActivityCreateAccount());
                return;
            }
            RyOaMsgVo msgVo = new RyOaMsgVo();
            msgVo.setMessageId(UuidCrmUtil.randomUuid());
            msgVo.setCode(msgSource);
            msgVo.setTitle("完全兑付提醒");
            msgVo.setUserIdList(oaId);
            msgVo.setContext(String.format(context, k.getSchemeDetailCode()));
            msgVo.setCreater(oaId);
            try {
                ryOaProcessService.sendMsg(msgVo);
            } catch (Exception e) {
                log.error("完全兑付提醒发送钉钉消息异常!", e);
            }
        });
    }

    @Override
    public List<MarketingAuditDetailVo> findCommitAndPassBySchemeDetailCodes(List<String> codes) {
        return marketingAuditDetailRepository.findCommitAndPassBySchemeDetailCodes(codes);
    }

    /**
     * 回写活动明细数据
     *
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSchemeDetail(List<MarketingAuditDetailDto> auditDetailDtoList, List<MarketingPlanCaseVo> caseVoList, boolean beAuto) {
        Map<String, MarketingAuditDetailDto> detailDtoMap = auditDetailDtoList.stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));
        List<MarketingPlanCaseVo> operateCaseVos = caseVoList.stream().filter(e -> detailDtoMap.containsKey(e.getSchemeDetailCode())).collect(Collectors.toList());

        //如果有计提，按计提金额来判度是否需要预算调整，如果没有计提，按活动的申请金额来判断
        Map<String, WithHolding> withHoldingMap = new HashMap();
        List<WithHolding> withHoldingList = withHoldingRepository.findByActivitiesDetailCodes(new ArrayList<>(detailDtoMap.keySet()));
        if (!CollectionUtils.isEmpty(withHoldingList)) {
            withHoldingMap = withHoldingList.stream().collect(Collectors.toMap(e -> e.getActivitiesDetailCode(), Function.identity(), (a, b) -> a));
        }

        List<OperateBudgetDto> operateList = new ArrayList<>();
        Map<String, WithHolding> finalWithHoldingMap = withHoldingMap;
        operateCaseVos.forEach(e -> {
            BigDecimal applyAmount = finalWithHoldingMap.get(e.getSchemeDetailCode()) != null ? finalWithHoldingMap.get(e.getSchemeDetailCode()).getActualAmount() : e.getApplyAmount();
            BigDecimal auditAmount = detailDtoMap.get(e.getSchemeDetailCode()).getAuditAmount();
            if (beAuto || BooleanEnum.TRUE.getCapital().equals(detailDtoMap.get(e.getSchemeDetailCode()).getBeFullAudit())) {
                if (auditAmount.compareTo(applyAmount) != 0) {
                    BigDecimal subtract = applyAmount.subtract(auditAmount);
                    OperateBudgetDto operateBudgetDtoOut = new OperateBudgetDto();
                    operateBudgetDtoOut.setBudgetCode(e.getBudgetCode());
                    operateBudgetDtoOut.setBusinessCode(e.getSchemeDetailCode());
                    operateBudgetDtoOut.setOperationAmount(subtract);
                    if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                        operateBudgetDtoOut.setOperationType(CostBudgetOperateType.APPEND.getCode());
                    } else {
                        operateBudgetDtoOut.setOperationType(CostBudgetOperateType.REDUCE.getCode());
                    }
                    operateList.add(operateBudgetDtoOut);
                }
                e.setAuditStatus(AuditStatusEnum.WHOLE_AUDIT.getCode());
            } else {
                e.setAuditStatus(AuditStatusEnum.PART_AUDIT.getCode());
            }
            e.setAuditAmount((e.getAuditAmount() != null ? e.getAuditAmount() : BigDecimal.ZERO).add(auditAmount));
        });
        //调用活动明细修改接口
        marketingPlanCaseService.rewriteEndCaseOrWithholding(operateCaseVos);

        //预算处理
//        if (!CollectionUtils.isEmpty(operateList)) {
//            Set<String> budgetCodeAll = operateList.stream().map(OperateBudgetDto::getBudgetCode).collect(Collectors.toSet());
//            boolean lock = costBudgetLockVoService.lock(new ArrayList<>(budgetCodeAll), TimeUnit.MINUTES, 30);
//            Validate.isTrue(lock, "操作预算失败，获取操作锁失败！");
//            try {
//                costBudgetOperateVoService.operateBudget(operateList, false);
//            } finally {
//                costBudgetLockVoService.unLock(new ArrayList<>(budgetCodeAll));
//            }
//        }
    }

    /**
     * 构建结案dto
     *
     * @param caseVoList
     * @return
     */
    public List<MarketingAuditDto> buildAuditDto(List<MarketingPlanCaseVo> caseVoList, MarketingPlanCaseTypeEnum marketingPlanCaseTypeEnum) {
        List<MarketingAuditDto> auditDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(caseVoList)) {
            return auditDtoList;
        }
        List<String> activityCodes = caseVoList.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
        Map<String, BigDecimal> deliveryAmountMap = new HashMap<>();
        if (marketingPlanCaseTypeEnum.getCode().equals(MarketingPlanCaseTypeEnum.matching_gift.getCode())) {
            List<DmsPolicyProductDeliveryVo> deliveryVoList = dmsWarehouseOrderDetailVoService.findByActivityCodes(activityCodes);
            // 剔除暂存dms 订单
            deliveryVoList = deliveryVoList.stream().filter(o -> !o.getOrderStatus().equals(OrderStatusEnum.STAGING.getDictCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deliveryVoList)) {
                deliveryAmountMap = deliveryVoList.stream().collect(Collectors.groupingBy(e -> e.getActivityDetailCode(), Collectors.reducing(BigDecimal.ZERO, e -> e.getOriginalTotalAmount() == null ? BigDecimal.ZERO : e.getOriginalTotalAmount(), BigDecimal::add)));
            }
        }
        Map<String, List<MarketingPlanCaseVo>> listMap = caseVoList.stream().collect(Collectors.groupingBy(e -> e.getSchemeCode()));
        Map<String, BigDecimal> finalDeliveryAmountMap = deliveryAmountMap;
        listMap.forEach((k, v) -> {
            MarketingAuditDto dto = new MarketingAuditDto();
            MarketingPlanCaseVo planCaseVo = v.get(0);
            if (marketingPlanCaseTypeEnum.getCode().equals(MarketingPlanCaseTypeEnum.matching_gift.getCode())) {
                dto.setAuditName(planCaseVo.getDepartmentName() + planCaseVo.getSchemeName() + "自动结案");
            } else {
                dto.setAuditName(planCaseVo.getSchemeName() + "-周边物料自动结案");
            }
            dto.setAuditStatus(AuditStatusEnum.WHOLE_AUDIT.getCode());
            dto.setStatus(ProcessStatusEnum.PASS.getDictCode());
            dto.setOrgCode(planCaseVo.getOrgCode());
            dto.setOrgName(planCaseVo.getOrgName());
            dto.setPositionCode(planCaseVo.getPositionCode());

            List<MarketingAuditDetailDto> detailDtoList = new ArrayList<>();
            for (MarketingPlanCaseVo vo : v) {
                MarketingAuditDetailDto detailDto = nebulaToolkitService.copyObjectByWhiteList(vo, MarketingAuditDetailDto.class, LinkedHashSet.class, ArrayList.class);
                detailDto.setEstimatedCost(vo.getEstimatedSalesVolume());
                if (!CollectionUtils.isEmpty(vo.getItemList())) {
                    detailDto.setItemCode(vo.getItemList().get(0).getCode());
                    detailDto.setItemName(vo.getItemList().get(0).getName());
                }
                if (!CollectionUtils.isEmpty(vo.getProductList())) {
                    detailDto.setProductCode(vo.getProductList().get(0).getCode());
                    detailDto.setProductName(vo.getProductList().get(0).getName());
                }

                BigDecimal amount;
                if (marketingPlanCaseTypeEnum.getCode().equals(MarketingPlanCaseTypeEnum.matching_gift.getCode())) {
                    amount = finalDeliveryAmountMap.get(detailDto.getSchemeDetailCode());
                    if (amount == null) {
                        continue;
                    }
                } else {
                    amount = vo.getApplyAmount();
                }
                detailDto.setAuditAmount(amount);

                detailDto.setId(null);
                detailDto.setCreateTime(null);
                detailDto.setCreateAccount(null);
                detailDto.setCreateName(null);
                detailDto.setModifyTime(null);
                detailDto.setModifyAccount(null);
                detailDto.setModifyName(null);
                detailDto.setBeFullAudit(BooleanEnum.TRUE.getCapital());
                detailDtoList.add(detailDto);
            }
            if (!CollectionUtils.isEmpty(detailDtoList)) {
                dto.setCacheList(detailDtoList);
                dto.setTotalApplyAmount(detailDtoList.stream().map(MarketingAuditDetailDto::getAuditAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                auditDtoList.add(dto);
            }
        });
        return auditDtoList;
    }

    /**
     * 获取活动明细
     *
     * @param marketingPlanCaseTypeEnum
     */
    private List<MarketingPlanCaseVo> getMarketingPlanCase(MarketingPlanCaseTypeEnum marketingPlanCaseTypeEnum) {
        //随单：活动状态为“已结束”且活动结案状态为“未结案”，且表单模板使用“随单”模板的营销方案明细
        //物资物料：活动结案状态为“未结案”、活动期间归属年月为历史月份（上月及以前月份），且表单模板使用“物料”模板的营销方案明细
        MarketingPlanCaseVo dto = new MarketingPlanCaseVo();
        dto.setEndDateLess(DateUtil.format(new Date(), "yyyy-MM-dd"));
        dto.setCaseType(marketingPlanCaseTypeEnum.getCode());
        dto.setAuditStatus(AuditStatusEnum.NOT_AUDIT.getCode());
        Page<MarketingPlanCaseVo> casePage = marketingPlanCaseService.findCaseList(PageRequest.of(0, Integer.MAX_VALUE), dto, ProcessStatusEnum.PASS.getDictCode());
        if (casePage.getTotal() > 0) {
            return casePage.getRecords();
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 核销明细生成票扣明细
     *
     * @param cacheList
     * @return
     */
    public List<MarketingAuditBillDetailVo> generateBillByAuditDetail(List<MarketingAuditDetailDto> cacheList) {
        List<MarketingAuditBillDetailVo> billDetailVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(cacheList)) {
            return billDetailVoList;
        }
        Set<String> belongDepartmentCodeList = cacheList.stream().map(e -> e.getBelongDepartmentCode()).collect(Collectors.toSet());
        Map<String, String> orgMap = orgVoService.findByOrgCodes(new ArrayList<>(belongDepartmentCodeList)).stream().collect(HashMap::new, (map, item) -> map.put(item.getOrgCode(), item.getTicketCashType()), (map1, map2) -> map1.putAll(map2));
        cacheList = cacheList.stream().filter(e -> CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(e.getCashType()) &&
                ("1".equals(e.getHzlx()) || "2".equals(e.getHzlx())) &&
                TicketCashTypeEnum.AUTO.getCode().equals(orgMap.get(e.getBelongDepartmentCode()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cacheList)) {
            return billDetailVoList;
        }
        //订单单价=当前客户、当前SKU对应费用归属年月的POD金额/当前客户对应费用归属年月的POD金额*本次结案金额
        Set<String> customerCodeSet = cacheList.stream().map(MarketingAuditDetailDto::getCustomerCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        //只有“经销商”或“直采”的客户才生成票扣明细
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodeSet)).stream().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerVos)) {
            return billDetailVoList;
        }
        Map<String, CustomerVo> customerVoMap = customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity(), (a, b) -> a));

//        List<DmsWarehouseOrderDetailVo> orderList = new ArrayList<>();
        Map<String, String> customerMap = new HashMap<>();
        List<JSONObject> customerJsonList = new ArrayList<>();
        Set<String> dtoStrSet = cacheList.stream().map(e ->
                {
                    CustomerVo customerVo = customerVoMap.get(e.getCustomerCode());
                    Validate.isTrue(null != customerVo && StringUtils.isNotBlank(customerVo.getPayerErpCode()), "客户或者SAP付款方编码不存在");
                    return e.getCompanyCode() + ":" + customerVo.getPayerErpCode() + ":" + e.getStartDate().substring(0, 7);
                }
        ).collect(Collectors.toSet());
        List<CreditOrderTicketVo> creditOrderDtoList = new ArrayList<>();
        for (String dtoStr : dtoStrSet) {
            String[] split = dtoStr.split(":");
            JSONObject data = new JSONObject();
            CreditOrderTicketVo creditOrderDto = new CreditOrderTicketVo();
            //公司代码
            data.put("BUKRS", split[0]);
            //客户
            data.put("KUNRG", split[1]);
            String customerCode = split[1] + split[0] + "0000";
            creditOrderDto.setCustomerCode(customerCode);
            //会计年度-期间  202409
            data.put("ZGJAHR", split[2].replaceAll("-", ""));
            creditOrderDto.setYears(split[2]);
            customerJsonList.add(data);
            creditOrderDtoList.add(creditOrderDto);
            customerMap.put(customerCode, split[0]);

//            TpmWarehouseDetailSearchDto searchDto = new TpmWarehouseDetailSearchDto();
//            searchDto.setCustomerCode(auditDetailDto.getCustomerCode());
//            searchDto.setSignSearchStartTime(auditDetailDto.getStartDate());
//            searchDto.setSignSearchEndTime(auditDetailDto.getEndDate());
//            List<DmsWarehouseOrderDetailVo> orderListTemp = dmsWarehouseOrderDetailVoService.findBySearchDto(searchDto);
//            if (!CollectionUtils.isEmpty(orderListTemp)) {
//                orderList.addAll(orderListTemp);
//            }
        }
        List<MarketingAuditSapInvoiceVo> invoiceVoList = marketingAuditInvoiceSapService.getSapCustomerUnInvoicedInfo(customerJsonList);
//        //只保留本品的发货数据
//        orderList = orderList.stream().filter(e -> ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(e.getItemType())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(orderList)) {
//            return billDetailVoList;
//        }
//        List<DmsWarehouseOrderDetailVo> delivery = orderList.stream().filter(e -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()) &&
//                e.getSignAmount() != null && e.getSignAmount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(delivery)) {
//            return billDetailVoList;
//        }
//        List<DmsWarehouseOrderDetailVo> stored = orderList.stream().filter(e -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(e.getWarehouseOrderType()) &&
//                e.getTotalAmount() != null && e.getTotalAmount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
//        //分别按客户、产品分组
//        Map<String, Map<String, List<DmsWarehouseOrderDetailVo>>> cusGoodsMap = delivery.stream().collect(Collectors.groupingBy(DmsWarehouseOrderDetailVo::getCustomerCode,
//                Collectors.groupingBy(DmsWarehouseOrderDetailVo::getGoodsCode)));
//        Map<String, Map<String, List<DmsWarehouseOrderDetailVo>>> cusGoodsStoredMap = stored.stream().collect(Collectors.groupingBy(DmsWarehouseOrderDetailVo::getCustomerCode,
//                Collectors.groupingBy(DmsWarehouseOrderDetailVo::getGoodsCode)));
//        //按客户汇总签收金额
//        Map<String, BigDecimal> customerSignAmount = delivery.stream().collect(Collectors.groupingBy(DmsWarehouseOrderDetailVo::getCustomerCode, Collectors.reducing(BigDecimal.ZERO, DmsWarehouseOrderDetailVo::getSignAmount, BigDecimal::add)));
//        Map<String, BigDecimal> customerStoredAmount = stored.stream().collect(Collectors.groupingBy(DmsWarehouseOrderDetailVo::getCustomerCode, Collectors.reducing(BigDecimal.ZERO, DmsWarehouseOrderDetailVo::getTotalAmount, BigDecimal::add)));

        if (CollectionUtils.isEmpty(invoiceVoList)) {
            return billDetailVoList;
        }
        invoiceVoList = invoiceVoList.stream().filter(e -> e.getUnInvoicedAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(invoiceVoList)) {
            return billDetailVoList;
        }
        Set<String> productCodes = invoiceVoList.stream().map(e -> e.getProductCode()).collect(Collectors.toSet());
        List<ProductVo> productVos = productVoService.findDetailsByIdsOrProductCodes(null, new ArrayList<>(productCodes));
        Map<String, ProductVo> productVoMap = productVos.stream().collect(Collectors.toMap(e -> e.getProductCode(), Function.identity(), (a, b) -> a));

        List<CreditOrderTicketVo> creditOrderVoList = creditOrderService.findByDtoList(creditOrderDtoList);
        //分别按年月客户、产品分组
        Map<String, Map<String, List<MarketingAuditSapInvoiceVo>>> yearCusGoodsMap = invoiceVoList.stream().collect(Collectors.groupingBy(e -> e.getYearMonthLy() + ":" + e.getCustomerCode(),
                Collectors.groupingBy(MarketingAuditSapInvoiceVo::getProductCode)));
        //累计未开票分别按年月客户、产品分组
        Map<String, Map<String, BigDecimal>> yearCusGoodsCreditMap = CollectionUtils.isEmpty(creditOrderVoList) ? new HashMap<>() : creditOrderVoList.stream().collect(Collectors.groupingBy(e -> e.getYears().replaceAll("-", "") + ":" + e.getCustomerCode(),
                Collectors.groupingBy(CreditOrderTicketVo::getProductCode, Collectors.mapping(e -> Optional.ofNullable(e.getUnTicketAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)))));
        //按客户汇总未开票金额
        Map<String, BigDecimal> customerUnInvoicedAmount = invoiceVoList.stream().collect(Collectors.groupingBy(e -> e.getYearMonthLy() + ":" + e.getCustomerCode(),
                Collectors.reducing(BigDecimal.ZERO, e -> Optional.ofNullable(e.getUnInvoicedAmount()).orElse(BigDecimal.ZERO), BigDecimal::add)));
        //按客户+年月汇总本次结案金额
        Map<String, BigDecimal> auditAmountMap = cacheList.stream().collect(Collectors.groupingBy(e -> e.getStartDate().replaceAll("-", "").substring(0, 6) + ":" + e.getCustomerCode(),
                Collectors.reducing(BigDecimal.ZERO, e -> e.getAuditAmount() == null ? BigDecimal.ZERO : e.getAuditAmount(), BigDecimal::add)));
        //获取票扣折让率上限
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(MarketingAuditConstant.PKZRLSX);
        BigDecimal limitRate = new BigDecimal(dictDataVos.get(0).getDictValue());

        List<MarketingAuditDetailDto> finalCacheList = cacheList;
        yearCusGoodsMap.forEach((customerCode, v) -> {
            AtomicInteger index = new AtomicInteger(1);
            AtomicReference<BigDecimal> calculateTotal = new AtomicReference<>(BigDecimal.ZERO);
//            Map<String, List<DmsWarehouseOrderDetailVo>> cusGoodsStoredCustomerMap = cusGoodsStoredMap.getOrDefault(customerCode, new HashMap<>());
            v.forEach((productCode, voList) -> {
                String[] split = customerCode.split(":");
//                DmsWarehouseOrderDetailVo orderDetailVo = voList.get(0);
                MarketingAuditBillDetailVo billDetailVo = new MarketingAuditBillDetailVo();
                billDetailVo.setAuditCode(finalCacheList.get(0).getAuditCode());
                billDetailVo.setAuditName(finalCacheList.get(0).getAuditName());
//                billDetailVo.setCustomerCode(orderDetailVo.getCustomerCode());
//                billDetailVo.setCustomerName(customerVoMap.getOrDefault(orderDetailVo.getCustomerCode(), new CustomerVo()).getCustomerName());
//                billDetailVo.setErpCode(customerVoMap.getOrDefault(orderDetailVo.getCustomerCode(), new CustomerVo()).getErpCode());
//                billDetailVo.setProductCode(orderDetailVo.getGoodsCode());
//                billDetailVo.setProductName(orderDetailVo.getGoodsName());
//                billDetailVo.setSaleUnit(orderDetailVo.getUnite());
                billDetailVo.setQuantity(BigDecimal.ONE);
                billDetailVo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                billDetailVo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                billDetailVo.setTenantCode(TenantUtils.getTenantCode());
                billDetailVo.setCustomerCode(split[1]);
                billDetailVo.setCustomerName(customerVoMap.getOrDefault(split[1], new CustomerVo()).getCustomerName());
                billDetailVo.setErpCode(customerVoMap.getOrDefault(split[1], new CustomerVo()).getErpCode());
                billDetailVo.setProductCode(productCode);
                billDetailVo.setProductName(productVoMap.getOrDefault(productCode, new ProductVo()).getProductName());
                billDetailVo.setSaleUnit(productVoMap.getOrDefault(productCode, new ProductVo()).getSaleUnit());

                //计算
                BigDecimal price = BigDecimal.ZERO;
                BigDecimal thisAmount = BigDecimal.ZERO;
//                BigDecimal total = customerSignAmount.getOrDefault(customerCode, BigDecimal.ZERO).subtract(customerStoredAmount.getOrDefault(customerCode, BigDecimal.ZERO));
                BigDecimal total = customerUnInvoicedAmount.getOrDefault(customerCode, BigDecimal.ZERO);
                BigDecimal auditAmount = auditAmountMap.getOrDefault(customerCode, BigDecimal.ZERO);
                if (!CollectionUtils.isEmpty(voList)) {
//                    thisAmount = voList.stream().map(e -> e.getSignAmount() != null ? e.getSignAmount() : BigDecimal.ZERO)
//                            .filter(Objects::nonNull)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    thisAmount = voList.stream().map(e -> e.getUnInvoicedAmount() != null ? e.getUnInvoicedAmount() : BigDecimal.ZERO)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//                    List<DmsWarehouseOrderDetailVo> storedList = cusGoodsStoredCustomerMap.getOrDefault(productCode, new ArrayList<>());
//                    if (!CollectionUtils.isEmpty(storedList)) {
//                        BigDecimal storedAmount = storedList.stream().map(e -> e.getTotalAmount() != null ? e.getTotalAmount() : BigDecimal.ZERO)
//                                .filter(Objects::nonNull)
//                                .reduce(BigDecimal.ZERO, BigDecimal::add);
//                        thisAmount = thisAmount.subtract(storedAmount);
//                    }
                }
                //折扣率=结案分摊金额/本次发货金额
                //折扣率不允许超数据字典维护的票扣折让率上限：pkzrlsx
                if (total.compareTo(BigDecimal.ZERO) != 0) {
                    //处理尾差
                    if (index.get() != v.size()) {
                        price = (thisAmount.divide(total, 6, BigDecimal.ROUND_HALF_UP).multiply(auditAmount)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        calculateTotal.set(calculateTotal.get().add(price));
                    } else {
                        price = auditAmount.subtract(calculateTotal.get());
                    }
                }
                billDetailVo.setPrice(price);
                billDetailVo.setAmount(price);
                billDetailVo.setYears(split[0].substring(0, 4) + "-" + split[0].substring(4, 6));
                billDetailVo.setCompanyCode(customerMap.get(split[1]));


                //未开票金额
                billDetailVo.setUnInvoicedAmount(thisAmount);

                //单次折让率
                billDetailVo.setSingleDiscountsRate(billDetailVo.getUnInvoicedAmount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : price.divide(billDetailVo.getUnInvoicedAmount(), 4, BigDecimal.ROUND_HALF_UP));

                //单次折让率(显示)
                billDetailVo.setSingleDiscountsRateStr(billDetailVo.getSingleDiscountsRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%");

                //累计未开票金额
                billDetailVo.setUnInvoicedCumulativeAmount(yearCusGoodsCreditMap.getOrDefault(customerCode, new HashMap<>()).getOrDefault(productCode, BigDecimal.ZERO));

                //累计折让率
                billDetailVo.setCumulativeDiscountsRate(billDetailVo.getUnInvoicedAmount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                        (billDetailVo.getUnInvoicedCumulativeAmount().add(price)).divide(billDetailVo.getUnInvoicedAmount(), 4, BigDecimal.ROUND_HALF_UP));

                //累计折让率(显示)
                billDetailVo.setCumulativeDiscountsRateStr(billDetailVo.getCumulativeDiscountsRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%");

                if (limitRate.compareTo(billDetailVo.getSingleDiscountsRate()) <= 0) {
                    billDetailVo.setCheckFlag(Boolean.FALSE);
                    billDetailVo.setErrMsg("单次折让率：" + billDetailVo.getSingleDiscountsRate() + "，超过票扣折让率上限：" + limitRate + "");
                }
                if (limitRate.compareTo(billDetailVo.getCumulativeDiscountsRate()) <= 0) {
                    billDetailVo.setCheckFlag(Boolean.FALSE);
                    billDetailVo.setErrMsg("累计折让率：" + billDetailVo.getCumulativeDiscountsRate() + "，超过票扣折让率上限：" + limitRate + "");
                }
                billDetailVoList.add(billDetailVo);

                index.getAndIncrement();
            });
        });
        return billDetailVoList;
    }

    @Override
    public Page<MarketingAuditDetailVo> findCachePageList(Pageable pageable, MarketingAuditDetailDto dto, String cacheKey) {
        String redisCacheIdKey = helper.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = helper.getRedisCacheDataKey(cacheKey);
        String redisCacheInitKey = helper.getRedisCacheInitKey(cacheKey);
        Page<MarketingAuditDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        page.setTotal(0);
        page.setRecords(Lists.newArrayList());

        a:
        if (redisService.hasKey(redisCacheIdKey)) {
            //redis里面有的话直接从redis里面取
            Long total = redisService.lSize(redisCacheIdKey);
            page.setTotal(total);
            List<Object> idList = redisService.lRange(redisCacheIdKey, page.offset(), page.offset() + page.getSize() - 1);
            if (!CollectionUtils.isEmpty(idList)) {
                List<MarketingAuditDetailDto> dataList = redisTemplate.opsForHash().multiGet(redisCacheDataKey, idList);
                List<MarketingAuditDetailVo> voList = helper.dtoListToVoList(dataList);
                page.setRecords(voList);
            }
        } else if (!redisService.hasKey(redisCacheInitKey) && null != dto) {
            //标记为已初始化，不重复初始化
            redisService.set(redisCacheInitKey, BooleanEnum.TRUE, helper.getExpireTime());
            //放到try catch里面 是为了以防分页从数据库查询报错了 初始化状态被确认 修正报错过后查询为空的情况
            try {
                //redis里面没有
                List<MarketingAuditDetailDto> dtoList = helper.findDtoListFromRepository(dto, cacheKey);

                if (CollectionUtils.isEmpty(dtoList)) {
                    redisService.del(redisCacheInitKey);
                    break a;
                }
                List<String> schemeDetailCodes = dtoList.stream().map(MarketingAuditDetailDto::getSchemeDetailCode).distinct().collect(Collectors.toList());
                List<MarketingPlanCaseExtend> caseExtendList = marketingPlanCaseExtendRepository.findBySchemeDetailCodes(schemeDetailCodes);
                Map<String, MarketingPlanCaseExtend> extendMap = caseExtendList.stream().collect(Collectors.toMap(MarketingPlanCaseExtend::getSchemeDetailCode, Function.identity(), (v1, v2) -> v1));
                dtoList.forEach(e -> {
                    if (extendMap.containsKey(e.getSchemeDetailCode())) {
                        MarketingPlanCaseExtend marketingPlanCaseExtend = extendMap.get(e.getSchemeDetailCode());
                        e.setConditionFormula(marketingPlanCaseExtend.getConditionFormula());
                        e.setConditionFormulaName(marketingPlanCaseExtend.getConditionFormulaName());
                        e.setConditionNum(marketingPlanCaseExtend.getConditionNum());
                        e.setGiveNum(marketingPlanCaseExtend.getGiveNum());
                        e.setDisplayCardNum(marketingPlanCaseExtend.getDisplayCardNum());
                    }
                });

                if (helper.initToCacheFromRepository()) {
                    helper.putCache(cacheKey, dtoList);
                }

                //放到缓存里面
                page.setTotal(dtoList.size());
                long start = page.offset();
                if (page.getTotal() > start) {
                    long end = page.offset() + page.getSize();
                    if (page.getTotal() < end) {
                        end = page.getTotal();
                    }
                    List<MarketingAuditDetailDto> recordDtoList = dtoList.subList((int) page.offset(), (int) end);
                    List<MarketingAuditDetailVo> voList = helper.dtoListToVoList(recordDtoList);
                    page.setRecords(voList);
                }
            } catch (Exception e) {
                redisService.del(redisCacheInitKey);
            }
        }
        //更新下VO里面的字段值
        helper.fillVoListProperties(page.getRecords());
        return page;
    }
}
