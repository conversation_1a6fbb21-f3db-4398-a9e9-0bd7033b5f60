package com.biz.crm.tpm.business.pay.local.service.internal.process;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.pay.local.service.internal.FeeCashPageCacheHelper;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTicketDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTicketAccountImportVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FeeCashTicketAccountImportProcess implements ImportProcess<FeeCashTicketAccountImportVo> {

    @Autowired
    private BudgetSubjectsVoService budgetSubjectsVoService;
    @Autowired
    private CustomerVoService customerVoServiceFeign;
    @Autowired
    private ProductVoService productVoServiceFeign;
    @Autowired
    private FeeCashPageCacheHelper feeCashPageCacheHelper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, FeeCashTicketAccountImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, FeeCashTicketAccountImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");

        for (Map.Entry<Integer, FeeCashTicketAccountImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            FeeCashTicketAccountImportVo vo = row.getValue();

//            this.validateIsTrue(StringUtils.isNotEmpty(vo.getYears()), "年月，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getBudgetSubjectsCode()), "预算科目编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCompanyCode()), "公司编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getErpCode()), "客户ERP编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getAmountStr()), "金额，不能为空！");

            try {
                new BigDecimal(vo.getAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "金额，类型转换失败！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, FeeCashTicketAccountImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();

        Set<String> erpCodeSet = new HashSet<>();
        Set<String> budgetSubjectCodeSet = new HashSet<>();

        for (Map.Entry<Integer, FeeCashTicketAccountImportVo> row : data.entrySet()) {
            FeeCashTicketAccountImportVo vo = row.getValue();
            erpCodeSet.add(vo.getErpCode());
            budgetSubjectCodeSet.add(vo.getBudgetSubjectsCode());
        }

        Map<String, Map<String, CustomerVo>> customerVoMap = customerVoServiceFeign.findByErpCodes(new ArrayList<>(erpCodeSet)).stream().collect(Collectors.groupingBy(e -> e.getErpCode(), Collectors.toMap(e -> e.getCompanyCode(), Function.identity(), (a, b) -> a)));
        Map<String, BudgetSubjectsVo> budgetSubjectsVoMap = budgetSubjectsVoService.findByCodes(budgetSubjectCodeSet).stream().collect(Collectors.toMap(e -> e.getBudgetSubjectsCode(), Function.identity(), (a, b) -> a));

        for (Map.Entry<Integer, FeeCashTicketAccountImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            FeeCashTicketAccountImportVo vo = row.getValue();
            vo.setAmount(new BigDecimal(vo.getAmountStr()));
            vo.setId(UUID.randomUUID().toString().replace("-", ""));
            if (customerVoMap.containsKey(vo.getErpCode())) {
                Map<String, CustomerVo> companyVoMap = customerVoMap.get(vo.getErpCode());
                if (companyVoMap.containsKey(vo.getCompanyCode())) {
                    vo.setCustomerCode(companyVoMap.get(vo.getCompanyCode()).getCustomerCode());
                    vo.setCustomerName(companyVoMap.get(vo.getCompanyCode()).getCustomerName());
                } else {
                    this.validateIsTrue(false, "客户，未找到！");
                }
            } else {
                this.validateIsTrue(false, "客户，未找到！");
            }
            if (budgetSubjectsVoMap.containsKey(vo.getBudgetSubjectsCode())) {
                vo.setBudgetSubjectsName(budgetSubjectsVoMap.get(vo.getBudgetSubjectsCode()).getBudgetSubjectsName());
            } else {
                this.validateIsTrue(false, "预算科目，未找到！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        if (!errMap.isEmpty()) {
            return errMap;
        }
        feeCashPageCacheHelper.importNewItem((String) params.get("cacheKey"), new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(data.values(), FeeCashTicketAccountImportVo.class, FeeCashTicketDto.class, LinkedHashSet.class, ArrayList.class)));
        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<FeeCashTicketAccountImportVo> findCrmExcelVoClass() {
        return FeeCashTicketAccountImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "FEE_CASH_TICKET_ACCOUNT_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "账扣明细导入模板";
    }
}
