package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayPayee;
import com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayPayeeMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 活动预付收款信息(ActivityPrepayPayee)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-04 20:03:02
 */
@Component
public class ActivityPrepayPayeeRepository extends ServiceImpl<ActivityPrepayPayeeMapper, ActivityPrepayPayee> {

  /**
   * 根据编码查询
   *
   * @param code
   * @return
   */
  public List<ActivityPrepayPayee> findByCode(String code) {
    return this.lambdaQuery().eq(ActivityPrepayPayee::getPrepayCode, code)
            .eq(ActivityPrepayPayee::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivityPrepayPayee::getTenantCode, TenantUtils.getTenantCode()).list();
  }

  /**
   * 根据编码查询
   *
   * @param codes
   * @return
   */
  public List<ActivityPrepayPayee> findByCodes(List<String> codes) {
    return this.lambdaQuery().in(ActivityPrepayPayee::getPrepayCode, codes)
            .eq(ActivityPrepayPayee::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivityPrepayPayee::getTenantCode, TenantUtils.getTenantCode()).list();
  }

  /**
   * 根据codeList删除
   *
   * @param codeList
   */
  public void deleteByCodeList(List<String> codeList) {
    this.lambdaUpdate().in(ActivityPrepayPayee::getPrepayCode, codeList)
            .remove();
  }

  public void hecPayStatusCallback(List<HecCallbackDto> dtoList) {
    if (CollectionUtil.isEmpty(dtoList)) {
      return;
    }
    this.baseMapper.hecPayStatusCallback(dtoList);
  }
}

