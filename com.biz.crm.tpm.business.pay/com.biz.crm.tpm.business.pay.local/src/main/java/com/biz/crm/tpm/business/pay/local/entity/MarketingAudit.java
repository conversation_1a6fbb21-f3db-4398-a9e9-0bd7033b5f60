package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 实体：方案结案;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "MarketingAudit", description = "方案结案")
@TableName("tpm_marketing_audit")
@Getter
@Setter
@Entity(name = "tpm_marketing_audit")
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_audit", comment = "方案结案")
@Table(name = "tpm_marketing_audit", indexes = {
        @Index(name = "audit_idx1", columnList = "audit_code"),
        @Index(name = "audit_idx2", columnList = "status"),
})
public class MarketingAudit extends TenantFlagOpEntity {
  /**
   * 核销申请名称
   */
  @ApiModelProperty(name = "核销申请名称", notes = "核销申请名称")
  @Column(name = "audit_name", columnDefinition = "VARCHAR(255) COMMENT '核销申请名称 '")
  private String auditName;

  /**
   * 核销申请编号
   */
  @ApiModelProperty(name = "核销申请编号", notes = "核销申请编号")
  @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
  private String auditCode;

  /**
   * 核销金额汇总
   */
  @ApiModelProperty(name = "核销金额汇总", notes = "核销金额汇总")
  @Column(name = "total_apply_amount", nullable = false, length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '核销金额汇总 '")
  private BigDecimal totalApplyAmount;

  /**
   * 结案状态
   */
  @ApiModelProperty("结案状态")
  @Column(name = "audit_status", columnDefinition = "varchar(32) comment '结案状态'")
  private String auditStatus;

  /**
   * 审批状态
   */
  @ApiModelProperty("审批状态")
  @Column(name = "status", length = 64, columnDefinition = "varchar(64) COMMENT '审批状态'")
  private String status;

  @ApiModelProperty("审批单号")
  @Column(name = "process_number", columnDefinition = "varchar(64) COMMENT '审批单号'")
  private String processNumber;

  @ApiModelProperty("推送日期")
  @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("OA人员id")
  @Column(name = "oa_id", columnDefinition = "varchar(32) COMMENT 'OA人员id'")
  private String oaId;

  @ApiModelProperty("OA人员账号")
  @Column(name = "oa_user_name", columnDefinition = "varchar(32) COMMENT 'OA人员账号'")
  private String oaUserName;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("组织编码")
  @Column(name = "org_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编码'")
  private String orgCode;

  @ApiModelProperty("组织名称")
  @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
  private String orgName;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("回传TPM接口唯一标识（接口用）")
  private String businessCode;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("url")
  private String tpmUrl;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("deptCode")
  private String deptCode;
}
