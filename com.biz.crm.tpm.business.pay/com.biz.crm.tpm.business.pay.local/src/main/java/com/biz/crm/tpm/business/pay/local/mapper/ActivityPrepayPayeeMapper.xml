<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayPayeeMapper">

    <select id="hecPayStatusCallback">
        <foreach item="dto" collection="dtoList" index="index"  open="" separator=";" close="">
            update tpm_activity_prepay_payee
            set pay_status = #{dto.orderStatus},
            pay_sucess_date = #{dto.paySucessDate}
            where prepay_code = #{dto.businessCode}
            and line_code = #{dto.businessDetailCode}
        </foreach>
    </select>
</mapper>

