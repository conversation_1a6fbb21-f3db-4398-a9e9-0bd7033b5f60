package com.biz.crm.tpm.business.pay.local.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashInvoice;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashInvoiceMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashInvoiceVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 费用兑付发票(FeeCashInvoice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
@Component
public class FeeCashInvoiceRepository extends ServiceImpl<FeeCashInvoiceMapper, FeeCashInvoice> {

  @Autowired
  private FeeCashInvoiceMapper feeCashInvoiceMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<FeeCashInvoiceVo> findByCode(String code) {
    List<FeeCashInvoice> list = this.lambdaQuery().eq(FeeCashInvoice::getCashCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashInvoice.class, FeeCashInvoiceVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param codes
   */
  public void deleteByCodes(List<String> codes) {
    this.remove(Wrappers.lambdaQuery(FeeCashInvoice.class)
            .in(FeeCashInvoice::getCashCode, codes));
  }

  /**
   * 按编码查询
   *
   * @param codes
   * @return
   */
  public List<FeeCashInvoice> findByCodes(List<String> codes) {
    return this.lambdaQuery().in(FeeCashInvoice::getCashCode, codes).list();
  }
}

