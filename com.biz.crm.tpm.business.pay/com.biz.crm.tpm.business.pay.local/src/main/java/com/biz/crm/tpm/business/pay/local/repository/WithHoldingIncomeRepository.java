package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingIncome;
import com.biz.crm.tpm.business.pay.local.mapper.WithHoldingIncomeMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class WithHoldingIncomeRepository extends ServiceImpl<WithHoldingIncomeMapper, WithHoldingIncome> {

    @Resource
    private WithHoldingIncomeMapper withHoldingIncomeMapper;

    public List<WithHoldingIncome> findByYear(List<String> year) {
        return withHoldingIncomeMapper.findByYear(year);
    }


    public List<WithHoldingIncomeVo> findListByCondition(WithholdingIncomeQueryDto dto) {
        return this.baseMapper.findListByCondition(dto);
    }

    public List<WithHoldingIncomeVo> findListByYearsAndChildrenOrgCodes(WithholdingIncomeQueryDto dto) {
        return this.baseMapper.findListByCondition(dto);
    }
}
