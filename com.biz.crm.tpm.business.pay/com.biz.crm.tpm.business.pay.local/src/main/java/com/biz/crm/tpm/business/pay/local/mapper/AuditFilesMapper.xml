<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AuditFilesMapper">
  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.AuditFiles" id="AuditFilesMap">
  </resultMap>
  
  <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.AuditFilesVo">
    select 
    t.*
    from tpm_audit_files t 
    <where>
        t.tenant_code = #{dto.tenantCode}
        and t.del_flag = '${@<EMAIL>()}'
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>