package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@TableName("tpm_invoice")
@Table(name = "tpm_invoice", indexes = {
        @Index(name = "tpm_invoice_index1", columnList = "tenant_code, type"),
        @Index(name = "tpm_invoice_index2", columnList = "invoice_no")
})
@ApiModel(value = "Invoice", description = "发票池")
@org.hibernate.annotations.Table(appliesTo = "tpm_invoice", comment = "发票池")
public class Invoice extends TenantFlagOpEntity {

    @ApiModelProperty("发票类型")
    @TableField(value = "type")
    @Column(name = "type", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '发票类型'")
    private String type;

    @ApiModelProperty("开票日期")
    @TableField("billing_date")
    @Column(name = "billing_date", length = 20, columnDefinition = "varchar(20) COMMENT '开票日期 '")
    private String billingDate;

    @ApiModelProperty("校验码(只允许填写数字和字母)")
    @TableField(value = "check_code")
    @Column(name = "check_code", columnDefinition = "varchar(64) COMMENT '校验码(只允许填写数字和字母)'")
    private String checkCode;

    @ApiModelProperty("发票代码")
    @TableField(value = "code")
    @Column(name = "code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '发票代码'")
    private String code;

    @ApiModelProperty("发票号码(只允许填写数字和字母)")
    @TableField(value = "invoice_no")
    @Column(name = "invoice_no", length = 64, nullable = false, unique = true, columnDefinition = "varchar(64) COMMENT '发票号码(只允许填写数字和字母)'")
    private String invoiceNo;

    @ApiModelProperty("不含税金额")
    @TableField(value = "amount_without_tax")
    @Column(name = "amount_without_tax", columnDefinition = "decimal(20,4) COMMENT '不含税金额'")
    private BigDecimal amountWithoutTax;

    @ApiModelProperty("税价合计")
    @TableField(value = "price_and_tax")
    @Column(name = "price_and_tax", columnDefinition = "decimal(20,4) COMMENT '税价合计'")
    private BigDecimal priceAndTax;

    @ApiModelProperty("税率")
    @TableField(value = "tax_rate")
    @Column(name = "tax_rate", columnDefinition = "decimal(20,4) COMMENT '税率'")
    private BigDecimal taxRate;

    @ApiModelProperty("税额")
    @TableField(value = "tax_amount")
    @Column(name = "tax_amount", columnDefinition = "decimal(20,4) COMMENT '税额'")
    private BigDecimal taxAmount;

    @ApiModelProperty("购买方名称")
    @TableField(value = "purchaser")
    @Column(name = "purchaser", columnDefinition = "varchar(64) COMMENT '购买方名称'")
    private String purchaser;

    @ApiModelProperty("购买方税号")
    @TableField(value = "p_no")
    @Column(name = "p_no", columnDefinition = "varchar(64) COMMENT '购买方税号'")
    private String pNo;

    @ApiModelProperty("购买方开户行及账号")
    @TableField(value = "p_bank_and_account")
    @Column(name = "p_bank_and_account", columnDefinition = "varchar(64) COMMENT '购买方开户行及账号'")
    private String pBankAndAccount;

    @ApiModelProperty("购买方地址电话")
    @TableField(value = "p_address_and_phone")
    @Column(name = "p_address_and_phone", columnDefinition = "varchar(64) COMMENT '购买方地址电话'")
    private String pAddressAndPhone;

    @ApiModelProperty("销售方名称")
    @TableField(value = "seller")
    @Column(name = "seller", columnDefinition = "varchar(64) COMMENT '销售方名称'")
    private String seller;

    @ApiModelProperty("销售方税号")
    @TableField(value = "s_no")
    @Column(name = "s_no", columnDefinition = "varchar(64) COMMENT '销售方税号'")
    private String sNo;

    @ApiModelProperty("销售方开户行及账号")
    @TableField(value = "s_bank_and_account")
    @Column(name = "s_bank_and_account", columnDefinition = "varchar(64) COMMENT '销售方开户行及账号'")
    private String sBankAndAccount;

    @ApiModelProperty("销售方地址电话")
    @TableField(value = "s_address_and_phone")
    @Column(name = "s_address_and_phone", columnDefinition = "varchar(64) COMMENT '销售方地址电话'")
    private String sAddressAndPhone;

    @ApiModelProperty("是否验证")
    @Column(name = "checked", columnDefinition = "varchar(10) comment '是否验证'")
    private String checked;

    @ApiModelProperty("是否同步")
    @Column(name = "sync_flag", columnDefinition = "varchar(10) comment '是否同步'")
    private String syncFlag;

    @ApiModelProperty("校验信息")
    @Column(name = "check_msg", columnDefinition = "text comment '校验信息'")
    private String checkMsg;

    @ApiModelProperty("文件id")
    @Column(name = "file_code", columnDefinition = "varchar(256) comment '文件Code'")
    private String fileCode;

    @ApiModelProperty("文件相对路径")
    @Column(name = "file_url", columnDefinition = "varchar(256) comment '文件相对路径'")
    private String fileUrl;

    @ApiModelProperty("文件名")
    @Column(name = "file_name", columnDefinition = "varchar(128) comment '文件名称'")
    private String fileName;

    @ApiModelProperty("核算公司代码")
    @Column(name = "acc_entity_code", columnDefinition = "varchar(32) comment '核算公司代码'")
    private String accEntityCode;

    @ApiModelProperty("公司代码-创建人所属公司的OAId")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司代码-创建人所属公司的OAId'")
    private String companyCode;

    @ApiModelProperty("关联兑付单")
    @Column(name = "cash_code", columnDefinition = "varchar(64) comment '关联兑付单'")
    private String cashCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code",columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name",columnDefinition = "varchar(32) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("职位编码")
    @Column(name = "position_code",columnDefinition = "varchar(32) comment '职位编码'")
    private String positionCode;

    @ApiModelProperty("职位名称")
    @Column(name = "position_name",columnDefinition = "varchar(32) comment '职位名称'")
    private String positionName;
}
