package com.biz.crm.tpm.business.pay.local.helper;

import cn.hutool.core.date.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.dms.business.order.common.sdk.enums.ItemTypeEnum;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialCostVoService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.regioncollect.service.strategy.DepartmentEstimationBuilder;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectDepartmentEstimationVo;
import com.biz.crm.tpm.business.budget.sdk.enums.SecondCategoryEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.FormulaGetValueComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 三级部门测算
 * <AUTHOR>
 * @Date 2024/6/25 17:12
 */
@Slf4j
public class DepartmentEstimationWithHoldingAbstractBuilder extends DepartmentEstimationBuilder<List<String>, String> {


    private static final String column = ":";

    private MarketingPlanCaseRepository caseRepository;
    private MarketingSalesPlanService marketingSalesPlanService;
    private CostBudgetIncomeService costBudgetIncomeService;
    private CostBudgetVoService costBudgetVoService;
    private CustomerVoService customerVoService;
    private MdmCostCenterVoService costCenterVoService;
    private MaterialVoService materialVoService;
    private MaterialCostVoService materialCostVoService;
    private CostTypeCategoryVoService costTypeCategoryVoService;
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;
    private FormulaGetValueComponent formulaGetValueComponent;

    private String year = null;

    private Map<String, BigDecimal> incomeDmsMap = new HashMap<>();

    private Map<String, BigDecimal> costDmsMap = new HashMap<>();

    private Map<String, BigDecimal> compensateGoodsCostMap = new HashMap<>();

    private Map<String, BigDecimal> transferMap = new HashMap<>();

    private Map<String, BigDecimal> withHoldingMap;

    private Map<String, BigDecimal> planCaseMap;

    private Map<String, List<String>> costTypeCategoryMap;

    private List<MarketingPlanCase> caseList = Lists.newArrayList();

    private List<WithHoldingVo> withHoldingList = Lists.newArrayList();

    private List<RegionCollectDepartmentEstimationVo> dataList = Lists.newArrayList();

    private List<DmsWarehouseOrderDetailVo> orderDetailList;

    public DepartmentEstimationWithHoldingAbstractBuilder() {
    }

    public static DepartmentEstimationWithHoldingAbstractBuilder builder(ApplicationContext context, List<RegionCollectDepartmentEstimationVo> dataList,
                                                                         List<MarketingPlanCase> caseList, List<WithHoldingVo> withHoldingList) {
        DepartmentEstimationWithHoldingAbstractBuilder builder = new DepartmentEstimationWithHoldingAbstractBuilder();
        builder.caseRepository = context.getBean(MarketingPlanCaseRepository.class);
        builder.marketingSalesPlanService = context.getBean(MarketingSalesPlanService.class);
        builder.costBudgetIncomeService = context.getBean(CostBudgetIncomeService.class);
        builder.costBudgetVoService = context.getBean(CostBudgetVoService.class);
        builder.customerVoService = context.getBean(CustomerVoService.class);
        builder.costCenterVoService = context.getBean(MdmCostCenterVoService.class);
        builder.materialVoService = context.getBean(MaterialVoService.class);
        builder.materialCostVoService = context.getBean(MaterialCostVoService.class);
        builder.costTypeCategoryVoService = context.getBean(CostTypeCategoryVoService.class);
        builder.dmsWarehouseOrderDetailVoService = context.getBean(DmsWarehouseOrderDetailVoService.class);
        builder.formulaGetValueComponent = context.getBean(FormulaGetValueComponent.class);
        builder.dataList = dataList;
        builder.caseList = caseList;
        builder.withHoldingList = withHoldingList;
        return builder;
    }


    @Override
    public DepartmentEstimationBuilder loadInitData() {
        List<String> orgCodes = dataList.stream().map(x -> x.getOrgCode()).distinct().collect(Collectors.toList());

        //1.获取预算收入-三级部门+成本中心+年月
        List<CostBudgetIncomeVo> incomeVos = costBudgetIncomeService.findListByOrgCodesAndYears(orgCodes, years);
        Map<String, BigDecimal> incomeMap = incomeVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDepartmentOneCode()) &&
                        ObjectUtils.isNotEmpty(x.getYearMonthLy()) &&
                        ObjectUtils.isNotEmpty(x.getIncomeAmount()))
                .collect(Collectors.groupingBy(x -> x.getDepartmentOneCode() + column + x.getYearMonthLy(),
                        Collectors.mapping(CostBudgetIncomeVo::getIncomeAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //2.获取预算费用
        List<CostBudgetVo> budgetVos = costBudgetVoService.findListByOrgCodesAndYears(orgCodes, years);
        Map<String, BigDecimal> budgetMap = budgetVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDepartmentOneCode()) &&
                        ObjectUtils.isNotEmpty(x.getYearMonthLy()) &&
                        ObjectUtils.isNotEmpty(x.getInitialAmount()))
                .collect(Collectors.groupingBy(x -> x.getDepartmentOneCode() + column + x.getYearMonthLy(),
                        Collectors.mapping(CostBudgetVo::getInitialAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //3.查询dms发货单明细
        List<CustomerVo> customerVoList = customerVoService.findByOrgCodes(orgCodes);
        Map<String, List<String>> orgCustomerMap = customerVoList.stream().collect(Collectors.groupingBy(e -> e.getOrgCode(), Collectors.mapping(CustomerVo::getCustomerCode, Collectors.toList())));
        List<String> customerCodes = customerVoList.stream().map(e -> e.getCustomerCode()).collect(Collectors.toList());
        TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
        dto.setCustomerCodes(customerCodes);
        String startDate = years + "-01";
        String endDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(startDate, "yyyy-MM-dd")), "yyyy-MM-dd");
        dto.setSearchStartTime(startDate);
        dto.setSearchEndTime(endDate);
        dto.setBeWithHolding(BooleanEnum.TRUE.getCapital());
        orderDetailList = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
        List<String> itemTypeList = Arrays.asList(ItemTypeEnum.NORMAL_GOODS.getDictCode(),
                ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
                ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
                ItemTypeEnum.COMPENSATED_GOODS.getDictCode(),
                ItemTypeEnum.TAIL_GOODS.getDictCode(),
                ItemTypeEnum.MATERIAL_GOODS.getDictCode());
        if (CollectionUtils.isNotEmpty(orderDetailList)) {
            //查询物料成本
            Set<String> materialCodes = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getGoodsCode()) && ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(x.getItemType())).filter(Objects::nonNull)
                    .map(DmsWarehouseOrderDetailVo::getGoodsCode).collect(Collectors.toSet());
            Set<String> companyCodeSet = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode()) && ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(x.getItemType())).filter(Objects::nonNull)
                    .map(DmsWarehouseOrderDetailVo::getCompanyCode).collect(Collectors.toSet());
            MaterialSearchDto searchDto = new MaterialSearchDto();
            searchDto.setMaterialCodeSet(materialCodes);
            searchDto.setCompanyCodeSet(companyCodeSet);
            List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
            Map<String, BigDecimal> materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO), (a, b) -> a));

            //POS分摊比例
            Map<String, BigDecimal> posMap = formulaGetValueComponent.splittingRateHandle(customerCodes, years, orgCodes, FormulaGetValueComponent.AMOUNT);
            orgCustomerMap.forEach((k, v) -> {
                //实际收入(发货减退货)
                BigDecimal delivery = orderDetailList.stream().filter(e -> e.getTotalAmount() != null && v.contains(e.getCustomerCode()) && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                        .filter(Objects::nonNull)
                        .map(e -> posMap.get(e.getCustomerCode()) == null ? e.getTotalAmount() : e.getTotalAmount().multiply(posMap.get(e.getCustomerCode())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal stored = orderDetailList.stream().filter(e -> e.getTotalAmount() != null && v.contains(e.getCustomerCode()) && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(e.getWarehouseOrderType()))
                        .filter(Objects::nonNull)
                        .map(e -> posMap.get(e.getCustomerCode()) == null ? e.getTotalAmount() : e.getTotalAmount().multiply(posMap.get(e.getCustomerCode())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                incomeDmsMap.put(k + column + years, (Optional.ofNullable(delivery).orElse(BigDecimal.ZERO)).subtract((Optional.ofNullable(stored).orElse(BigDecimal.ZERO))));
                //生产成本(对应物料主数据的单价*实际发货数量)
                Map<String, BigDecimal> goodsMap = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && v.contains(e.getCustomerCode()) && ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(e.getItemType())).filter(Objects::nonNull)
                        .collect(Collectors.groupingBy(e -> e.getGoodsCode(), Collectors.mapping(e -> posMap.get(e.getCustomerCode()) == null ? e.getRealOutNum() : e.getRealOutNum().multiply(posMap.get(e.getCustomerCode())), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                BigDecimal goodsCost = BigDecimal.ZERO;
                for (Map.Entry<String, BigDecimal> entry : goodsMap.entrySet()) {
                    goodsCost = goodsCost.add(entry.getValue().multiply(materialMap.getOrDefault(entry.getKey(), BigDecimal.ZERO)));
                }
                costDmsMap.put(k + column + years, goodsCost);
                //运输费用(产品运输费用+周边运输费用)
                transferMap.put(k + column + years, orderDetailList.stream().filter(e -> e.getExpressFee() != null && v.contains(e.getCustomerCode()) &&
                        itemTypeList.contains(e.getItemType())).map(e -> posMap.get(e.getCustomerCode()) == null ? e.getExpressFee() : e.getExpressFee().multiply(posMap.get(e.getCustomerCode())))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            });
        }
        //4.计提金额
        withHoldingMap = withHoldingList.stream().collect(Collectors.groupingBy(x -> x.getBelongDepartmentCode() + column + years,
                Collectors.mapping(x->Optional.ofNullable(x.getWithHoldingAmount()).orElse(BigDecimal.ZERO),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //5.兑付金额
        planCaseMap = caseList.stream().collect(Collectors.groupingBy(x -> x.getBelongDepartmentCode() + column + years,
                Collectors.mapping(x->Optional.ofNullable(x.getCashAmount()).orElse(BigDecimal.ZERO),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        //6.查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        this.costTypeCategoryMap = costTypeCategoryVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (CustomerGainsAndLossesVo data : dataList) {
            //此处的indexkey = 三级部门+年月
            String indexKey = data.getIndexKey();
            //预算收入
            data.setBudgetIncome(incomeMap.get(indexKey));
            //规划收入= 用成本中心匹配
            data.setPlanIncome(incomeDmsMap.getOrDefault(indexKey, BigDecimal.ZERO));
            //收入达成率
            data.setIncomeAchieveRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getBudgetIncome()) && ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) != 0) {
                //规划收入/预算收入
                data.setIncomeAchieveRatio(data.getPlanIncome().divide(data.getBudgetIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            //预算总额
            data.setBudgetTotalAmount(budgetMap.get(indexKey));
            //预算费率
            data.setBudgetRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getBudgetTotalAmount()) && ObjectUtils.isNotEmpty(data.getBudgetIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) != 0) {
                //预算总额/预算收入
                data.setBudgetRatio(data.getBudgetTotalAmount().divide(data.getBudgetIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            //规划总额（营销费用(计提+兑付)+运输费用+分摊费用）
            data.setPlanTotalAmount(withHoldingMap.getOrDefault(indexKey, BigDecimal.ZERO)
                    .add(planCaseMap.getOrDefault(indexKey, BigDecimal.ZERO))
                    .add(transferMap.getOrDefault(indexKey, BigDecimal.ZERO)));
            //规划费率
            data.setPlanRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && ObjectUtils.isNotEmpty(data.getPlanTotalAmount()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                //规划总额/规划收入
                data.setPlanRatio(data.getPlanTotalAmount().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            //费率偏差 = 规划费率-预算费率
            data.setRatioDeviation(data.getPlanRatio().subtract(data.getBudgetRatio()));
        }
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calProfitRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            String indexKey = data.getIndexKey();
            //生产成本
            data.setProductionCosts(costDmsMap.getOrDefault(indexKey, BigDecimal.ZERO));
            //产品运输费用
            data.setProductTransportCost(transferMap.getOrDefault(indexKey, BigDecimal.ZERO));
            //营销费用
            data.setMarketingCost(withHoldingMap.getOrDefault(indexKey, BigDecimal.ZERO)
                    .add(planCaseMap.getOrDefault(indexKey, BigDecimal.ZERO)));
            //规划利润额=规划收入-生产成本-产品运输费用-周边运输费用-营销费用
            data.setPlanProfitMargin(data.getPlanIncome().subtract(data.getProductionCosts()).subtract(ObjectUtils.defaultIfNull(data.getProductTransportCost(),BigDecimal.ZERO)).subtract(data.getMarketingCost()));
            //利润率=规划利润额/规划收入
            data.setProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setProfitRatio(data.getPlanProfitMargin().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calGrossProfitRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //规划毛利额=规划收入-生产成本
            data.setPlanGrossProfitMargin(data.getPlanIncome().subtract(data.getProductionCosts()));
            //毛利率=规划毛利额/规划收入
            data.setGrossProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setGrossProfitRatio(data.getPlanGrossProfitMargin().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calLogisticsRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //物流费率=(产品运输费用+周边运输费用)/规划收入
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setLogisticsRatio(data.getProductTransportCost().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calMarketingRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //营销费率=营销费用/规划收入
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setMarketingRatio(data.getMarketingCost().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calPublicShareRatio() {
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calSecondCategory() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //二级费用大类计算
            Map<String, BigDecimal> secondCategoryMap = Maps.newHashMap();
            for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
                BigDecimal cashAmount = caseList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()) &&
                                (x.getBelongDepartmentCode() + column + x.getYears()).equals(data.getIndexKey()))
                        .map(x -> Optional.ofNullable(x.getCashAmount()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal withHoldingAmount = withHoldingList.stream().filter(x -> entry.getValue().contains(x.getCostTypeCategoryCode()) &&
                        (x.getBelongDepartmentCode() + column + x.getYears()).equals(data.getIndexKey()))
                        .map(x -> Optional.ofNullable(x.getWithHoldingAmount()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal ratio = BigDecimal.ZERO;
                if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                    ratio = (cashAmount.add(withHoldingAmount)).divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN);
                }
                secondCategoryMap.put(entry.getKey(), ratio);

            }
            for (Map.Entry<String, BigDecimal> entry : secondCategoryMap.entrySet()) {
                BigDecimal value = entry.getValue();
                if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
                    data.setContract(value);
                } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
                    data.setGeneralization(value);
                } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
                    data.setPromotion(value);
                } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
                    data.setSalesReward(value);
                }

            }
            data.setSecondCostCategoryMap(secondCategoryMap);
        }
        return this;
    }
}
