package com.biz.crm.tpm.business.pay.local.repository;



import com.biz.crm.tpm.business.pay.local.entity.OrderAdjustProduct;
import com.biz.crm.tpm.business.pay.local.mapper.OrderAdjustProductMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustProductVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 订单调整产品(OrderAdjustProduct)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-01 20:23:43
 */
@Component
public class OrderAdjustProductRepository extends ServiceImpl<OrderAdjustProductMapper, OrderAdjustProduct> {

  @Autowired
  private OrderAdjustProductMapper orderAdjustProductMapper;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;


  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<OrderAdjustProductVo> findByCode(String code) {
    List<OrderAdjustProduct> list = this.lambdaQuery().eq(OrderAdjustProduct::getAdjustCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, OrderAdjustProduct.class, OrderAdjustProductVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码查询
   *
   * @param codes
   * @return
   */
  public List<OrderAdjustProductVo> findByCodes(List<String> codes) {
    List<OrderAdjustProduct> list = this.lambdaQuery().in(OrderAdjustProduct::getAdjustCode, codes).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, OrderAdjustProduct.class, OrderAdjustProductVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param codes
   * @return
   */
  public void deleteByCodes(List<String> codes) {
    this.lambdaUpdate().in(OrderAdjustProduct::getAdjustCode, codes).remove();
  }
}

