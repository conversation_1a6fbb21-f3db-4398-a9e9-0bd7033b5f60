package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectVo;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDto;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditService;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditActivityItemVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 费用核销;(tpm_audit)控制层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-16
 */
@Api(tags = "费用核销功能接口")
@RestController
@RequestMapping("/v1/pay/audit")
@Slf4j
public class AuditController {
  /**
   * 服务对象
   */
  @Autowired
  private AuditService auditService;

  @Autowired
  private AuditDetailService auditDetailService;

  /**
   * 由于创建或修改导致的重复提交的问题。在创建和修改前，需要使用该方法获得预授权</br></br>
   * 预授权成功后，才能通过预授权信息进行添加，
   *
   * @return
   */
  @ApiOperation(value = "由于创建或修改导致的重复提交的问题。在创建和修改前，需要使用该方法获得预授权</br>")
  @PostMapping(value = "/preSave")
  public Result<?> preSave() {
    try {
      Object prefix = this.auditService.preSave();
      return Result.ok(prefix);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页查询所有数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<AuditVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                @ApiParam(name = "audit", value = "核销采集信息") AuditDto dto) {
    try {
      Page<AuditVo> page = this.auditService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页活动明细数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页活动明细数据")
  @GetMapping("findActivitiesDetailByConditions")
  public Result<Page<AuditActivityItemVo>> findActivitiesDetailByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                            @ApiParam(name = "audit", value = "核销采集信息") ActivitiesDto dto) {
    try {
      Page<AuditActivityItemVo> page = this.auditService.findActivitiesDetailByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 查看活动明细执行采集数据
   *
   * @param activitiesDetailCode 活动明细编号
   * @return 所有数据
   */
  @ApiOperation(value = "查看活动明细执行采集数据")
  @GetMapping("findDetailCollectByActivitiesDetailCode")
  public Result<List<ActivitiesDetailCollectVo>> findDetailCollectByActivitiesDetailCode(@ApiParam(name = "activitiesDetailCode", value = "活动明细编号", required = true) String activitiesDetailCode) {
    try {
      List<ActivitiesDetailCollectVo> activitiesDetailCollectVos = this.auditDetailService.findDetailCollectByActivitiesDetailCode(activitiesDetailCode);
      return Result.ok(activitiesDetailCollectVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<AuditVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id", required = true) String id) {
    try {
      AuditVo auditVo = this.auditService.findById(id);
      return Result.ok(auditVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByCode")
  public Result<AuditVo> findByCode(@ApiParam(name = "code", value = "编号", required = true) String code) {
    try {
      AuditVo auditVo = this.auditService.findByCode(code);
      return Result.ok(auditVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param auditDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<AuditVo> create(@ApiParam(name = "auditDto", value = "费用核销") @RequestBody AuditDto auditDto) {
    try {
      AuditVo result = this.auditService.create(auditDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   *
   * @param auditDto 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<AuditVo> update( @ApiParam(name = "auditDto", value = "费用核销") @RequestBody AuditDto auditDto) {
    try {
      AuditVo result = this.auditService.update(auditDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation(value = "删除数据")
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    try {
      this.auditService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过状态编号查询单条数据
   *
   * @param enableStatus 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过状态编号查询单条数据")
  @GetMapping("findByEnableStatus")
  public Result<List<AuditVo>> findByEnableStatus(@PathVariable @ApiParam(name = "enableStatus", value = "状态编号") String enableStatus) {
    try {
      List<AuditVo> audits = this.auditService.findByEnableStatus(enableStatus);
      return Result.ok(audits);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
