package com.biz.crm.tpm.business.pay.local.service.internal.process;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.user.sdk.service.UserPositionVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserPositionVo;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingImportVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WithHoldingImportProcess implements ImportProcess<WithHoldingImportVo> {


    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private CostBudgetVoService costBudgetVoService;

    @Autowired(required = false)
    private WithHoldingService withHoldingService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Autowired(required = false)
    private ProductPhaseVoService productPhaseVoService;

    @Autowired(required = false)
    private CostTypeCategoryVoService costTypeCategoryVoService;

    @Autowired(required = false)
    private CostTypeDetailVoService costTypeDetailVoService;

    @Autowired(required = false)
    private UserPositionVoService userPositionVoService;

    /**
     * 兑付方式
     */
    private static final String TPM_SCHEME_CASH_TYPE = "tpm_scheme_cash_type";

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, WithHoldingImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, WithHoldingImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");

        //客户
        Map<String, CustomerVo> customerVoMap = this.customerVoMap(data);
        //品项
        Map<String, ProductPhaseVo> productPhaseVoMap = this.productPhaseVoMap(data);

        for (Map.Entry<Integer, WithHoldingImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            WithHoldingImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCompanyCode()), "公司编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getYearMonthLy()), "手动计提年月，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getYears()), "费用归属年月，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getBudgetSubjectsName()), "预算科目名称，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostCenterCode()), "成本中心编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getDepartmentCode()), "部门编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCustomerCode()), "客户编码，不能为空！");
            this.validateIsTrue(customerVoMap.containsKey(vo.getCustomerCode()), "客户信息未维护！");
            CustomerVo customerVo = customerVoMap.get(vo.getCustomerCode());
            if (Objects.nonNull(customerVo)){
                this.validateIsTrue(BooleanEnum.TRUE.getCapital().equals(customerVo.getContractCustomer()), "客户不是合同客户");
            }
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getItemCode()), "品项编码，不能为空！");
            this.validateIsTrue(productPhaseVoMap.containsKey(vo.getItemCode()), "品项编码未在品项管理中维护！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getWithHoldingAmountStr()), "计提金额，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getPayBy()), "兑付方式，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getRemark()), "备注，不能为空！");
            try {
                new BigDecimal(vo.getWithHoldingAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "计提金额类型转换失败！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, WithHoldingImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        // 查询数据字典
        Map<String, List<DictDataVo>> dictDataMap = dictDataVoService.findByDictTypeCodeList(Arrays.asList(TPM_SCHEME_CASH_TYPE));
        //客户
        Map<String, CustomerVo> customerVoMap = this.customerVoMap(data);
        //品项
        Map<String, ProductPhaseVo> productPhaseVoMap = this.productPhaseVoMap(data);

        for (Map.Entry<Integer, WithHoldingImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();

            WithHoldingImportVo vo = row.getValue();
            DictDataVo orgLevel = dictDataMap.get(TPM_SCHEME_CASH_TYPE).stream().filter(e -> e.getDictValue().equals(vo.getPayBy())).findFirst().orElse(null);
            vo.setPayBy(orgLevel.getDictCode());
            vo.setWithHoldingAmount(new BigDecimal(vo.getWithHoldingAmountStr()));

            CostBudgetVo budget = findBudgetCode(vo);
            if (budget != null) {
                vo.setBudgetCode(budget.getCode());
                vo.setCompanyName(budget.getCompanyName());
                vo.setBudgetSubjectsCode(budget.getBudgetSubjectCode());
                vo.setCostCenterName(budget.getCostCenterName());
                vo.setDepartmentName(budget.getDepartmentOneName());
                vo.setCustomerName(customerVoMap.get(vo.getCustomerCode()).getCustomerName());
                vo.setItemName(productPhaseVoMap.get(vo.getItemCode()).getProductPhaseName());

                vo.setErpCode(customerVoMap.get(vo.getCustomerCode()).getErpCode());
                vo.setBearDepartmentCode(vo.getDepartmentCode());
                vo.setBearDepartmentName(vo.getDepartmentName());
                vo.setBelongDepartmentCode(vo.getDepartmentCode());
                vo.setBelongDepartmentName(vo.getDepartmentName());
                vo.setWithHoldingReportAmount(vo.getWithHoldingAmount());
                vo.setActualAmount(vo.getWithHoldingAmount());
                vo.setActualReportAmount(vo.getWithHoldingAmount());
            } else {
                this.validateIsTrue(false, "预算信息不正确，未找到对应的预算！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        //根据预算科目编码找活动大类
        List<String> budgetSubjectsCodes = data.values().stream().map(WithHoldingImportVo::getBudgetSubjectsCode).distinct().collect(Collectors.toList());
        List<CostTypeCategoryVo> costTypeCategoryVos = this.costCategoryVos(budgetSubjectsCodes);
        Map<String, List<CostTypeCategoryVo>> costTypeCategoryVosMap = costTypeCategoryVos.stream().collect(Collectors.groupingBy(CostTypeCategoryVo::getBudgetSubjectsCode));

        //根据活动大类找活动细类
        List<String> categoryCodes = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getCategoryCode).distinct().collect(Collectors.toList());
        List<CostTypeDetailVo> costTypeDetailVos = this.costDetailVos(categoryCodes);
        Map<String, List<CostTypeDetailVo>> costTypeDetailVosMap = costTypeDetailVos.stream().collect(Collectors.groupingBy(CostTypeDetailVo::getCategoryCode));
        List<UserPositionVo> userPositionVoList = userPositionVoService.findByUserName(TenantUtils.getTenantCode(), paramsVo.getCreateAccount());
        if (CollectionUtil.isEmpty(userPositionVoList)) {
            errMap.put(1, errMap.getOrDefault(1, "").concat("未获取到登录人信息!"));
            return errMap;
        }
        UserPositionVo userPositionVo = userPositionVoList.stream().filter(k -> Objects.nonNull(k.getCurrentFlag()))
                .filter(UserPositionVo::getCurrentFlag)
                .findFirst().orElse(null);
        if (Objects.isNull(userPositionVo)) {
            userPositionVo = userPositionVoList.stream().filter(k -> Objects.nonNull(k.getPrimaryFlag()))
                    .filter(UserPositionVo::getPrimaryFlag)
                    .findFirst().orElse(null);
        }
        if (Objects.isNull(userPositionVo)) {
            userPositionVo = userPositionVoList.get(0);
        }
        for (Map.Entry<Integer, WithHoldingImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            WithHoldingImportVo vo = row.getValue();
            if (StringUtils.isBlank(vo.getBudgetSubjectsCode())) {
                continue;
            }
            if (!costTypeCategoryVosMap.containsKey(vo.getBudgetSubjectsCode())) {
                this.validateIsTrue(false, "预算科目未关联活动大类或配置已禁用！");
                errMap.put(rowNum, this.validateGetErrorInfo());
                continue;
            }
            List<CostTypeCategoryVo> categoryVos = costTypeCategoryVosMap.get(vo.getBudgetSubjectsCode());
            if (categoryVos.size() > 1) {
                this.validateIsTrue(false, "预算科目关联多个活动大类！");
                errMap.put(rowNum, this.validateGetErrorInfo());
                continue;
            }
            CostTypeCategoryVo categoryVo = categoryVos.get(0);
            if (!costTypeDetailVosMap.containsKey(categoryVo.getCategoryCode())) {
                this.validateIsTrue(false, "活动大类【%s】未关联活动细类或配置已禁用！");
                errMap.put(rowNum, this.validateGetErrorInfo());
                continue;
            }
            List<CostTypeDetailVo> detailVos = costTypeDetailVosMap.get(categoryVo.getCategoryCode());
            if (detailVos.size() > 1) {
                this.validateIsTrue(false, "活动大类【%s】关联多个活动细类！");
                errMap.put(rowNum, this.validateGetErrorInfo());
                continue;
            }
            CostTypeDetailVo detailVo = detailVos.get(0);
            vo.setCostTypeCategoryCode(categoryVo.getCategoryCode());
            vo.setCostTypeCategoryName(categoryVo.getCategoryName());
            vo.setCostTypeDetailCode(detailVo.getDetailCode());
            vo.setCostTypeDetailName(detailVo.getDetailName());
            vo.setPositionCode(userPositionVo.getPositionCode());
            vo.setOrgCode(userPositionVo.getOrgCode());
            vo.setOrgName(userPositionVo.getOrgName());
            String payBy = vo.getPayBy();
            // 兑付方式为账扣
            if (StringUtils.isNotEmpty(payBy)) {
                if (payBy.equals(CashMethodEnum.WIRE_TRANSFER.getDictCode()) || payBy.equals(CashMethodEnum.DEDUCTIONS.getDictCode())) {
                    vo.setNoTaxActualReportAmount(vo.getActualReportAmount().divide(BigDecimal.ONE.add(BigDecimal.valueOf(0.06)), 2, BigDecimal.ROUND_HALF_UP));
                } else if (payBy.equals(CashMethodEnum.TICKET_BUCKLE.getDictCode()) || payBy.equals(CashMethodEnum.REPLENISHMENT.getDictCode())) {
                    String itemCode = vo.getItemCode();
                    if (com.google.common.collect.Lists.newArrayList("BC1001", "BC1002").contains(itemCode)) {
                        vo.setNoTaxActualReportAmount(vo.getActualReportAmount().divide(BigDecimal.ONE.add(BigDecimal.valueOf(0.1)), 2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        ProductPhaseVo productPhaseVo = productPhaseVoMap.get(itemCode);
                        if (Objects.isNull(productPhaseVo) || Objects.isNull(productPhaseVo.getTaxRate())) {
                            this.validateIsTrue(false, itemCode + "未匹配到对应品项或对应品项未维护税率");
                            errMap.put(rowNum, this.validateGetErrorInfo());
                        } else {
                            vo.setNoTaxActualReportAmount(vo.getActualAmount().divide(BigDecimal.ONE.add(productPhaseVo.getTaxRate()), 2, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }
            }
        }
        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        withHoldingService.handleManual(new ArrayList<>(data.values()));
        return null;
    }

    /**
     * 客户信息
     *
     * @param data
     * @return
     */
    private Map<String, CustomerVo> customerVoMap(LinkedHashMap<Integer, WithHoldingImportVo> data) {
        Map<String, CustomerVo> customerVoMap = new HashMap<>();
        List<String> customerCodes = data.values().stream().filter(Objects::nonNull).map(WithHoldingImportVo::getCustomerCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(customerCodes)) {
            List<CustomerVo> customerVos = Optional.ofNullable(customerVoService.findByCustomerCodes(customerCodes)).orElse(Lists.newArrayList());
            customerVoMap.putAll(customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, v -> v, (v1, v2) -> v1)));
        }
        return customerVoMap;
    }

    /**
     * 品项信息
     *
     * @param data
     * @return
     */
    private Map<String, ProductPhaseVo> productPhaseVoMap(LinkedHashMap<Integer, WithHoldingImportVo> data) {
        Map<String, ProductPhaseVo> productPhaseVoMap = new HashMap<>();
        List<String> itemCodes = data.values().stream().filter(Objects::nonNull).map(WithHoldingImportVo::getItemCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemCodes)) {
            List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
            productPhaseVoMap.putAll(productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> v, (v1, v2) -> v1)));
        }
        return productPhaseVoMap;
    }

    /**
     * 根据预算科目编码查询活动大类
     *
     * @param budgetSubjectsCodes
     * @return
     */
    private List<CostTypeCategoryVo> costCategoryVos(List<String> budgetSubjectsCodes) {
        if (CollectionUtils.isEmpty(budgetSubjectsCodes)) {
            return Lists.newArrayList();
        }
        List<CostTypeCategoryVo> categoryVos = costTypeCategoryVoService.findByBudgetSubjectsCodes(budgetSubjectsCodes);
        return categoryVos.stream().filter(e -> StringUtils.equals(EnableStatusEnum.ENABLE.getCode(), e.getEnableStatus())).collect(Collectors.toList());
    }

    /**
     * 根据活动大类编码查询活动细类
     *
     * @param categoryCodes
     * @return
     */
    private List<CostTypeDetailVo> costDetailVos(List<String> categoryCodes) {
        if (CollectionUtils.isEmpty(categoryCodes)) {
            return Lists.newArrayList();
        }
        List<CostTypeDetailVo> detailVos = costTypeDetailVoService.findByCategoryCodes(categoryCodes);
        return detailVos.stream().filter(e -> StringUtils.equals(EnableStatusEnum.ENABLE.getCode(), e.getEnableStatus())).collect(Collectors.toList());
    }

    /**
     * 查找对应的预算编码
     *
     * @param vo
     * @return
     */
    private CostBudgetVo findBudgetCode(WithHoldingImportVo vo) {
        CostBudgetDto dto = new CostBudgetDto();
        dto.setYearMonthLy(vo.getYearMonthLy());
        dto.setDepartmentOneCode(vo.getDepartmentCode());
        dto.setCostCenterCode(vo.getCostCenterCode());
        dto.setBudgetSubjectName(vo.getBudgetSubjectsName());
//        dto.setCustomerCode(vo.getCustomerCode());
        //dto.setProductLevelCode(vo.getItemCode());
        dto.setTenantCode(TenantUtils.getTenantCode());
        List<CostBudgetVo> codeList = costBudgetVoService.findBudgetCodeByDto(dto);
        if (CollectionUtils.isEmpty(codeList)) {
            return null;
        }
        // 过滤出品项相同的
        Optional<CostBudgetVo> first = codeList.stream().filter(v -> vo.getItemCode().equals(v.getProductLevelCode())).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        // 过滤出无品项的
        Optional<CostBudgetVo> notItem = codeList.stream().filter(v -> StringUtils.isBlank(v.getItemCode())).findFirst();
        return notItem.orElse(null);
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<WithHoldingImportVo> findCrmExcelVoClass() {
        return WithHoldingImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "WITH_HOLDING_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "手动计提导入模板";
    }
}
