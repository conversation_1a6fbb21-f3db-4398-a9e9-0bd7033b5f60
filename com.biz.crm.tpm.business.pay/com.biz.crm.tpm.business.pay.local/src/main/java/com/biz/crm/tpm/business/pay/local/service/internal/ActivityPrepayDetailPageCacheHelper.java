package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail;
import com.biz.crm.tpm.business.pay.local.repository.ActivityPrepayDetailRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.ActivityPrepayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 费用兑付分页缓存
 */
@Slf4j
@Component
public class ActivityPrepayDetailPageCacheHelper extends BusinessPageCacheHelper<ActivityPrepayDetailVo, ActivityPrepayDetailDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private ActivityPrepayDetailRepository activityPrepayDetailRepository;
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return ActivityPrepayConstant.CACHE_KEY_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<ActivityPrepayDetailDto> getDtoClass() {
        return ActivityPrepayDetailDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<ActivityPrepayDetailVo> getVoClass() {
        return ActivityPrepayDetailVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param activityPrepayDetailDto
     * @param cacheKey
     */
    @Override
    public List<ActivityPrepayDetailDto> findDtoListFromRepository(ActivityPrepayDetailDto activityPrepayDetailDto, String cacheKey) {
        if (StringUtils.isBlank(activityPrepayDetailDto.getPrepayCode())) {
            return new ArrayList<>();
        }
        List<ActivityPrepayDetail> activityPrepayDetailVos = activityPrepayDetailRepository.findByCode(activityPrepayDetailDto.getPrepayCode());
        return CollectionUtils.isNotEmpty(activityPrepayDetailVos) ? (List<ActivityPrepayDetailDto>) nebulaToolkitService.copyCollectionByBlankList(activityPrepayDetailVos, ActivityPrepayDetail.class, ActivityPrepayDetailDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<ActivityPrepayDetailDto> newItem(String cacheKey, List<ActivityPrepayDetailDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<ActivityPrepayDetailDto> copyItem(String cacheKey, List<ActivityPrepayDetailDto> itemList) {
        List<ActivityPrepayDetailDto> newItemList = (List<ActivityPrepayDetailDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, ActivityPrepayDetailDto.class, ActivityPrepayDetailDto.class, HashSet.class, ArrayList.class);
        for (ActivityPrepayDetailDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 导入新增数据
     * @param cacheKey
     * @param itemList
     * @return
     */
    public void importNewItem(String cacheKey, List<ActivityPrepayDetailDto> itemList) {
        String redisCacheDataKey = this.getRedisCacheDataKey(cacheKey);

        Map<Object, ActivityPrepayDetailDto> updateMap = itemList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }

    /**
     * 获取Dto中的主键
     *
     * @param activityPrepayDetailDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(ActivityPrepayDetailDto activityPrepayDetailDto) {
        return activityPrepayDetailDto.getSchemeDetailCode();
    }

    /**
     * 获取是否选中状态
     *
     * @param activityPrepayDetailDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(ActivityPrepayDetailDto activityPrepayDetailDto) {
        return activityPrepayDetailDto.getChecked();
    }

}
