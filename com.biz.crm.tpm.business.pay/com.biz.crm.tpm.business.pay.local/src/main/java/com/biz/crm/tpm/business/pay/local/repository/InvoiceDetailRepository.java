package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.InvoiceDetail;
import com.biz.crm.tpm.business.pay.local.mapper.InvoiceDetailMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailPageVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InvoiceDetailRepository extends ServiceImpl<InvoiceDetailMapper, InvoiceDetail> {

    /**
     * 根据发票号码查询信息
     */
    public List<InvoiceDetail> findByInvoiceNoList(List<String> invoiceNoList) {
        return this.lambdaQuery()
                .in(InvoiceDetail::getInvoiceNo, invoiceNoList)
                .list();
    }


    public void deleteByInvoiceNo(String invoiceNo) {
        this.lambdaUpdate()
                .eq(InvoiceDetail::getInvoiceNo, invoiceNo).remove();
    }

    public Page<InvoiceDetailPageVo> findInvoiceDetailList(Page<InvoiceDetailPageVo> page, InvoiceDetailPageVo vo) {
        return this.baseMapper.findInvoiceDetailList(page, vo, TenantUtils.getTenantCode());
    }

    /**
     * 根据发票明细号码查询信息
     */
    public List<InvoiceDetail> findByUniqueKeyList(List<String> uniqueKeyList) {
        if (CollectionUtils.isEmpty(uniqueKeyList)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findByUniqueKeyList(uniqueKeyList);
    }

    public void updateUsedByUniqueKeyList(List<String> uniqueKeyList) {
        if (CollectionUtils.isEmpty(uniqueKeyList)) {
            return;
        }
        this.baseMapper.updateUsedByUniqueKeyList(uniqueKeyList);
    }

    public void deleteByInvoiceNoList(List<String> invoiceNoList) {
        this.lambdaUpdate()
                .in(InvoiceDetail::getInvoiceNo, invoiceNoList)
                .remove();
    }

    public InvoiceDetailPageVo findByInvoiceNoAndDetailNo(String invoiceNo, String detailNo) {
        return this.baseMapper.findByInvoiceNoAndDetailNo(invoiceNo, detailNo);
    }
}
