package com.biz.crm.tpm.business.pay.local.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/4 10:38
 **/
@ApiModel("管报收入查询dto")
@Data
public class WithholdingIncomeQueryDto {

    @ApiModelProperty("组织列表")
    private List<String> orgCodes;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("年月列表")
    private List<String> yearsList;
}
