package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@ApiModel(value = "FeeCashTicket", description = "费用票扣明细")
@TableName("tpm_fee_cash_ticket")
@Getter
@Setter
@Entity(name = "tpm_fee_cash_ticket")
@org.hibernate.annotations.Table(appliesTo = "tpm_fee_cash_ticket", comment = "费用票扣明细")
@Table(name = "tpm_fee_cash_ticket", indexes = {@Index(name = "fee_cash_idx1", columnList = "cash_code")})
public class FeeCashTicket extends TenantFlagOpEntity {

    @ApiModelProperty("兑付名称")
    @Column(name = "cash_name", columnDefinition = "VARCHAR(255) COMMENT '兑付名称 '")
    private String cashName;

    @ApiModelProperty("兑付编号")
    @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
    private String cashCode;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("预算科目编码")
    @Column(name = "budget_subjects_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
    private String budgetSubjectsCode;

    @ApiModelProperty("预算科目名称")
    @Column(name = "budget_subjects_name", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
    private String budgetSubjectsName;

    @ApiModelProperty("核销申请名称")
    @Column(name = "audit_name", columnDefinition = "VARCHAR(255) COMMENT '核销申请名称 '")
    private String auditName;

    @ApiModelProperty("核销申请编号")
    @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
    private String auditCode;

    @ApiModelProperty("批次号")
    @Column(name = "bt_no", length = 64, columnDefinition = "varchar(64) COMMENT '批次号'")
    private String btNo;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

    @ApiModelProperty("产品编码")
    @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
    private String productCode;

    @ApiModelProperty("产品名称")
    @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
    private String productName;

    @ApiModelProperty("销售单位")
    @Column(name = "sale_unit", columnDefinition = "VARCHAR(32) COMMENT '销售单位'")
    private String saleUnit;

    @ApiModelProperty("物料编码")
    @Column(name = "material_code", columnDefinition = "varchar(32) comment '物料编码'")
    private String materialCode;

    @ApiModelProperty("物料名称")
    @Column(name = "material_name", columnDefinition = "varchar(128) comment '物料名称'")
    private String materialName;

    @ApiModelProperty("活动归属年月")
    @Column(name = "year_month_ly", columnDefinition = "varchar(32) comment '活动归属年月'")
    private String yearMonthLy;

    @ApiModelProperty("未开票金额")
    @Column(name = "un_invoiced_amount", columnDefinition = "decimal(20,4) COMMENT '未开票金额'")
    private BigDecimal unInvoicedAmount;

    @ApiModelProperty("累计未开票金额")
    @Column(name = "un_invoiced_cumulative_amount", columnDefinition = "decimal(20,4) COMMENT '累计未开票金额'")
    private BigDecimal unInvoicedCumulativeAmount;

    @ApiModelProperty("单次折让率")
    @Column(name = "single_discounts_rate", columnDefinition = "decimal(20,4) COMMENT '单次折让率'")
    private BigDecimal singleDiscountsRate;

    @ApiModelProperty("单次折让率(显示)")
    @Column(name = "single_discounts_rate_str", columnDefinition = "varchar(32) comment '单次折让率(显示)'")
    private String singleDiscountsRateStr;

    @ApiModelProperty("累计折让率")
    @Column(name = "cumulative_discounts_rate", columnDefinition = "decimal(20,4) COMMENT '累计折让率'")
    private BigDecimal cumulativeDiscountsRate;

    @ApiModelProperty("累计折让率(显示)")
    @Column(name = "cumulative_discounts_rate_str", columnDefinition = "varchar(32) comment '累计折让率(显示)'")
    private String cumulativeDiscountsRateStr;

    @ApiModelProperty("数量")
    @Column(name = "quantity", columnDefinition = "decimal(20,6) COMMENT '数量'")
    private BigDecimal quantity;

    @ApiModelProperty("单价")
    @Column(name = "price", columnDefinition = "decimal(20,6) COMMENT '单价'")
    private BigDecimal price;

    @ApiModelProperty("金额")
    @Column(name = "amount", columnDefinition = "decimal(20,6) COMMENT '金额'")
    private BigDecimal amount;

    @ApiModelProperty("贷项订单编码")
    @Column(name = "credit_code", columnDefinition = "VARCHAR(32) COMMENT '贷项订单编码'")
    private String creditCode;

    @ApiModelProperty("行号")
    @Column(name = "line_number", length = 64, columnDefinition = "int(11) COMMENT '行号'")
    private Integer lineNumber;

}
