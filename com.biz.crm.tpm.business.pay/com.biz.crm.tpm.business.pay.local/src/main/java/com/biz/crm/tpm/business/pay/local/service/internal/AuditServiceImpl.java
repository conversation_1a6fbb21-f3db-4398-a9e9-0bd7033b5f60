package com.biz.crm.tpm.business.pay.local.service.internal;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.common.form.sdk.DynamicFormService;
import com.biz.crm.common.form.sdk.context.DynamicFormContextHolder;
import com.biz.crm.tpm.business.activities.dynamic.template.enums.FormTypeEnum;
import com.biz.crm.tpm.business.activities.dynamic.template.service.DynamicFormServiceResolver;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailsDto;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.pay.local.entity.Audit;
import com.biz.crm.tpm.business.pay.local.repository.AuditRepository;
import com.biz.crm.tpm.business.pay.local.service.AuditActivitiesService;
import com.biz.crm.tpm.business.pay.local.service.AuditFilesService;
import com.biz.crm.tpm.business.pay.local.service.AuditInvoiceService;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDto;
import com.biz.crm.tpm.business.pay.sdk.dto.InvoiceDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.AuditLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.event.AuditEventListener;
import com.biz.crm.tpm.business.pay.sdk.event.log.AuditLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.AuditBillService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditService;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceService;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditActivitiesVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditActivityItemVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditBillVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditFilesVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditInvoiceVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditVo;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceVo;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessMappingDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.ProcessBusinessMappingService;
import com.biz.crm.workflow.sdk.service.ProcessBusinessService;
import com.biz.crm.workflow.sdk.vo.ProcessBusinessMappingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant.DYNAMIC_FORM_FIELD_CODE;
import static com.biz.crm.tpm.business.budget.sdk.enums.RedisKeys.GENERATE_PREFIX;

/**
 * 费用核销;(tpm_audit)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Service("auditService")
public class AuditServiceImpl implements AuditService {
  @Autowired
  private AuditRepository auditRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private RedisMutexService redisMutexService;
  @Autowired(required = false)
  private List<AuditEventListener> auditEventListeners;
  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private AuditActivitiesService auditActivitiesService;
  @Autowired
  private AuditDetailService auditDetailService;
  @Autowired
  private AuditFilesService auditFilesService;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired
  private ActivitiesService activitiesService;
  @Autowired
  private AuditBillService auditBillService;
  @Autowired
  private ActivitiesDetailService activitiesDetailService;
  @Autowired
  private AuditInvoiceService auditInvoiceService;
  @Autowired
  private InvoiceService invoiceService;
  @Autowired
  private InvoiceDetailService invoiceDeailService;
  @Autowired(required = false)
  private ProcessBusinessService processBusinessService;
  @Autowired(required = false)
  private ProcessBusinessMappingService processBusinessMappingService;

  @Autowired
  private DynamicFormServiceResolver dynamicFormServiceResolver;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<AuditVo> findByConditions(Pageable pageable, AuditDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new AuditDto();
    }
    return this.auditRepository.findByConditions(pageable, dto);
  }

  @Override
  public Page<AuditActivityItemVo> findActivitiesDetailByConditions(Pageable pageable, ActivitiesDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesDto();
    }
    // 封装需要进行过滤的活动细类编号
    Set<String> excludeCostTypeDetailCodes = Sets.newHashSet();
    Set<String> excludeActivitiesDetailCodes = Sets.newHashSet();

    // 不需要核销的活动细类
    CostTypeDetailsDto notAuditDto = new CostTypeDetailsDto();
    notAuditDto.setIsAudit(BooleanEnum.FALSE.getCapital());
    Set<String> notAuditCostTypeDetailCodes = this.costTypeDetailVoService.findCodeByCondition(notAuditDto);
    // 自动核销的活动细类
    CostTypeDetailsDto autoAuditDto = new CostTypeDetailsDto();
    autoAuditDto.setIsAudit(BooleanEnum.TRUE.getCapital());
    autoAuditDto.setIsAutoAudit(BooleanEnum.TRUE.getCapital());
    Set<String> autoAuditCostTypeDetailCodes = this.costTypeDetailVoService.findCodeByCondition(autoAuditDto);
    // 单次核销的活动细类
    CostTypeDetailsDto notMultiAuditDto = new CostTypeDetailsDto();
    notMultiAuditDto.setIsAudit(BooleanEnum.TRUE.getCapital());
    notMultiAuditDto.setIsMultipleAudit(BooleanEnum.FALSE.getCapital());
    Set<String> notMultiAuditCostTypeDetailCodes = this.costTypeDetailVoService.findCodeByCondition(notMultiAuditDto);

    // 获取核销中的活动明细编号
    Set<String> auditingActivitiesDetailCodes = this.auditDetailService.findActivitiesDetailByAuditing(notMultiAuditCostTypeDetailCodes);
    // 获取核销中的活动已经完全核销的活动细类编号
    Set<String> auditFullActivitiesDetailCodes = this.auditDetailService.findActivitiesDetailByFullAudit();

    // 1、筛选不需要进行核销的活动细类
    excludeCostTypeDetailCodes.addAll(notAuditCostTypeDetailCodes);
    // 2、筛选自动核销的
    excludeCostTypeDetailCodes.addAll(autoAuditCostTypeDetailCodes);
    // 3、筛选单次核销并已经在核销中的
    excludeActivitiesDetailCodes.addAll(auditingActivitiesDetailCodes);
    // 4、已经完全核销的活动细类编号
    excludeActivitiesDetailCodes.addAll(auditFullActivitiesDetailCodes);

    ActivitiesDetailDto detailDto = new ActivitiesDetailDto();
    detailDto.setExcludeCostTypeDetailCodes(excludeCostTypeDetailCodes);
    detailDto.setExcludeItemCodes(excludeActivitiesDetailCodes);
    detailDto.setActivitiesCode(dto.getActivitiesCode());
    detailDto.setActivitiesName(dto.getActivitiesName());
    detailDto.setActivitiesCodes(dto.getActivitiesCodes());
    detailDto.setCreateName(dto.getCreateName());
    detailDto.setIsFullAudit(BooleanEnum.FALSE.getCapital());
    Page<ActivitiesDetailVo> basicPage = this.activitiesDetailService.findByConditions(pageable, detailDto);
    List<AuditActivityItemVo> result = Lists.newArrayList();
    Page<AuditActivityItemVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    if (!CollectionUtils.isEmpty(basicPage.getRecords())) {
      for (ActivitiesDetailVo basicActivityItemVo : basicPage.getRecords()) {
        AuditActivityItemVo auditActivityItemVo = this.nebulaToolkitService.copyObjectByWhiteList(basicActivityItemVo, AuditActivityItemVo.class, LinkedHashSet.class, ArrayList.class, "costTypeDetailVo");
        AuditBillVo auditBillVo = this.auditBillService.findByActivitiesDetailCode(basicActivityItemVo.getActivitiesDetailCode());
        if (auditBillVo == null) {
          auditActivityItemVo.setAuditedAmount(BigDecimal.ZERO);
        } else {
          auditActivityItemVo.setAuditedAmount(auditBillVo.getAuditedAmount());
        }
        result.add(auditActivityItemVo);
      }
    }
    page.setRecords(result);
    page.setTotal(basicPage.getTotal());
    return page;
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public AuditVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    Audit audit = this.auditRepository.findByIdAndTenantCode(id, TenantUtils.getTenantCode());
    if (audit == null) {
      return null;
    }
    AuditVo auditVo = this.nebulaToolkitService.copyObjectByWhiteList(audit, AuditVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetails(auditVo);

    return auditVo;
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 主键
   * @return 单条数据
   */
  @Override
  public AuditVo findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    Audit audit = this.auditRepository.findByCode(code);
    if (audit == null) {
      return null;
    }
    AuditVo auditVo = this.nebulaToolkitService.copyObjectByWhiteList(audit, AuditVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetails(auditVo);

    return auditVo;
  }

  @Override
  public List<AuditVo> findByActivitiesDetailCode(String activitiesDetailCode) {
    return this.auditRepository.findByActivitiesDetailCode(activitiesDetailCode);
  }

  /**
   * 填充明细数据
   *
   * @param auditVo
   */
  private void fillDetails(AuditVo auditVo) {
    /*
     * 查询关联数据信息
     */
    // 1、 查询关联活动信息
    List<AuditActivitiesVo> auditActivitiesVos = this.auditActivitiesService.findByAuditCode(auditVo.getAuditCode());
    auditVo.setAuditActivities(auditActivitiesVos);
    // 2、查询关联费用审核明细信息
    List<AuditDetailVo> auditDetailVos = this.auditDetailService.findByAuditCode(auditVo.getAuditCode());
    auditVo.setAuditDetails(auditDetailVos);
    // 3、查询关联附件
    List<AuditFilesVo> auditFilesVos = this.auditFilesService.findByAuditCode(auditVo.getAuditCode());
    auditVo.setAuditFiles(auditFilesVos);
    // 4、发票数据
    List<AuditInvoiceVo> auditInvoiceVos = this.auditInvoiceService.findByAuditCode(auditVo.getAuditCode());
    if (!CollectionUtils.isEmpty(auditInvoiceVos)) {
      auditInvoiceVos.forEach(item -> {
        String id = item.getId();
        InvoiceVo invoiceVo = this.invoiceService.findByInvoiceNo(item.getInvoiceNo());
        BeanUtils.copyProperties(invoiceVo, item);
        item.setId(id);
      });
      auditVo.setAuditInvoices(auditInvoiceVos);
    }
    //3.====查询动态模板明细
    Set<String> dynamicKeys = auditDetailVos.stream().map(AuditDetailVo::getFormMappingCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    for (String dynamicKey : dynamicKeys) {
      DynamicFormService<AuditVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, AuditVo.class, FormTypeEnum.AUDIT);
      if (dynamicFormService == null) {
        continue;
      }
      dynamicFormService.perfectDynamicDetails(auditVo, auditVo.getAuditCode());
    }
  }

  /**
   * 新增数据
   *
   * @param auditDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AuditVo create(AuditDto auditDto) {
    auditDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    auditDto.setIsAutoAudit(BooleanEnum.FALSE.getCapital());
    String code = this.generateCodeService.generateCode(PayConstant.AUDIT_LADDER_CODE, 1).get(0);
    auditDto.setAuditCode(code);
    this.createValidate(auditDto);
    Audit audit = this.nebulaToolkitService.copyObjectByWhiteList(auditDto, Audit.class, LinkedHashSet.class, ArrayList.class, "items");
    audit.setTenantCode(TenantUtils.getTenantCode());
    audit.setIsAutoAudit(BooleanEnum.FALSE.getCapital());
    this.auditRepository.saveOrUpdate(audit);
    AuditVo auditVo = this.nebulaToolkitService.copyObjectByWhiteList(audit, AuditVo.class, LinkedHashSet.class, ArrayList.class, "items");
    auditVo.setId(audit.getId());

    auditDto.getAuditActivities().forEach(item -> item.setAuditCode(auditVo.getAuditCode()));
    auditDto.getAuditDetails().forEach(item -> {
      item.setAuditCode(auditVo.getAuditCode());
      item.setAuditDto(auditDto);
    });
    auditDto.getAuditFiles().forEach(item -> item.setAuditCode(auditVo.getAuditCode()));
    if (!CollectionUtils.isEmpty(auditDto.getAuditInvoices())) {
      auditDto.getAuditInvoices().forEach(item -> {
        item.setAuditCode(auditVo.getAuditCode());
      });
    }

    String parentCode = audit.getAuditCode();
    Set<DynamicFormService<AuditVo>> dynamicFormServices = dynamicFormServiceResolver.getDynamicFormServices(JSON.parseObject(JSON.toJSONString(auditDto)), DYNAMIC_FORM_FIELD_CODE, AuditVo.class, FormTypeEnum.AUDIT);
    Validate.notEmpty(dynamicFormServices, "根据提供的信息，未能获取到匹配的动态模板服务类信息");
    AuditVo ordinaryActivityVo = nebulaToolkitService.copyObjectByWhiteList(audit, AuditVo.class, HashSet.class, ArrayList.class, "items");
    try {
      for (DynamicFormService<AuditVo> dynamicFormService : dynamicFormServices) {
        dynamicFormService.createDynamicDetails(ordinaryActivityVo, parentCode);
      }
    } finally {
      DynamicFormContextHolder.clearContext();
    }
    // 保存其他附属信息
    // 1、保存活动信息
    List<AuditActivitiesVo> auditActivitiesVos = this.auditActivitiesService.createBatch(auditDto.getAuditActivities());
    auditVo.setAuditActivities(auditActivitiesVos);
    // 2、保存费用核销明细 同时需要计算费用汇总信息 每条明细信息中的数据有效性需要在单独的明细保存方法中验证
    Map<String, ?> itemJsons = auditDto.getItems();
    List<AuditDetailDto> detailDtoList = Lists.newArrayList();
    itemJsons.forEach((key, value) -> {
      List<AuditDetailDto> details = JSON.parseArray(JSON.toJSONString(value), AuditDetailDto.class);
      details.forEach(d -> d.setFormMappingCode(key));
      detailDtoList.addAll(details);
    });
    detailDtoList.forEach(dto -> {
      dto.setAuditCode(audit.getAuditCode());
      dto.setAuditDto(auditDto);
    });
    List<AuditDetailVo> auditDetailVos = this.auditDetailService.createBatch(detailDtoList);
    auditVo.setAuditDetails(auditDetailVos);
    BigDecimal totalAmount = auditVo.getTotalApplyAmount();
    BigDecimal currentTotalAuditAmount = auditDetailVos.stream().map(AuditDetailVo::getAuditAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    Validate.isTrue(totalAmount.compareTo(currentTotalAuditAmount) == 0, "申请核销费用汇总金额与明细核销费用不一致，请检查");
    // 3、保存附件
    List<AuditFilesVo> auditFilesVos = this.auditFilesService.createBatch(auditDto.getAuditFiles());
    auditVo.setAuditFiles(auditFilesVos);
    // 4、保存发票信息
    List<AuditInvoiceVo> auditInvoiceVos = this.auditInvoiceService.createBatch(auditDto.getAuditInvoices());
    auditVo.setAuditInvoices(auditInvoiceVos);

    if (!CollectionUtils.isEmpty(auditEventListeners)) {
      for (AuditEventListener auditEventListener : auditEventListeners) {
        auditEventListener.onCreated(auditVo);
      }
    }
    //工作流
    if (auditDto.getProcessBusiness() != null) {
      auditDto.setId(audit.getId());
      auditDto.setAuditCode(audit.getAuditCode());
      //提交工作流
      this.commitProcess(auditDto);
      for (AuditDetailVo auditDetailVo : auditVo.getAuditDetails()) {
        BigDecimal excessAmount = auditDetailVo.getExcessAmount();
        if (excessAmount != null && excessAmount.compareTo(BigDecimal.ZERO) > 0) {
          ActivitiesVo activitiesVo = this.activitiesService.findByActivitiesCode(auditDetailVo.getActivitiesCode());
          ActivitiesDetailVo basicActivityItemVo = this.activitiesDetailService.findByActivitiesDetailCode(auditDetailVo.getActivitiesDetailCode());
          Validate.notNull(basicActivityItemVo, "活动明细【%s】错误，请检查！", auditDetailVo.getActivitiesDetailCode());
          this.costBudgetVoService.occupy(auditDetailVo.getAuditCode(), auditDetailVo.getAuditDetailCode(), basicActivityItemVo.getCostBudgetCode(), excessAmount, activitiesVo.getActivityMark(), CostBudgetItemSourceType.AUDIT.getDescr());
        }
      }
    }

    //新增业务日志
    AuditLogEventDto logEventDto = new AuditLogEventDto();
    logEventDto.setOriginal(null);
    logEventDto.setNewest(auditVo);
    SerializableBiConsumer<AuditLogEventListener, AuditLogEventDto> onCreate =
            AuditLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(logEventDto, AuditLogEventListener.class, onCreate);
    return auditVo;
  }

  @Override
  public AuditVo creatForAutoAudit(AuditDto auditDto) {
    auditDto.setPrefix(this.preSave());
    auditDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    auditDto.setIsAutoAudit(BooleanEnum.TRUE.getCapital());
    String code = this.generateCodeService.generateCode(PayConstant.AUDIT_LADDER_CODE, 1).get(0);
    auditDto.setAuditCode(code);
    this.createValidate(auditDto);
    Audit audit = this.nebulaToolkitService.copyObjectByWhiteList(auditDto, Audit.class, LinkedHashSet.class, ArrayList.class);
    audit.setTenantCode(TenantUtils.getTenantCode());
    audit.setIsAutoAudit(BooleanEnum.TRUE.getCapital());
    this.auditRepository.saveOrUpdate(audit);
    AuditVo auditVo = this.nebulaToolkitService.copyObjectByWhiteList(audit, AuditVo.class, LinkedHashSet.class, ArrayList.class);
    auditVo.setId(audit.getId());

    auditDto.getAuditActivities().forEach(item -> item.setAuditCode(auditVo.getAuditCode()));
    auditDto.getAuditDetails().forEach(item -> {
      item.setAuditCode(auditVo.getAuditCode());
      item.setAuditDto(auditDto);
      item.setTenantCode(TenantUtils.getTenantCode());
    });

    // 保存其他附属信息
    // 1、保存活动信息
    List<AuditActivitiesVo> auditActivitiesVos = this.auditActivitiesService.createBatch(auditDto.getAuditActivities());
    auditVo.setAuditActivities(auditActivitiesVos);
    // 2、保存费用核销明细 同时需要计算费用汇总信息 每条明细信息中的数据有效性需要在单独的明细保存方法中验证
    List<AuditDetailVo> auditDetailVos = this.auditDetailService.createBatch(auditDto.getAuditDetails());
    auditVo.setAuditDetails(auditDetailVos);
    BigDecimal totalApplyAmount = auditVo.getTotalApplyAmount();
    BigDecimal currentTotalAuditAmount = auditDetailVos.stream().map(AuditDetailVo::getAuditAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    Validate.isTrue(totalApplyAmount.compareTo(currentTotalAuditAmount) == 0, "申请费用汇总金额与明细申请费用不一致，请检查");

    if (!CollectionUtils.isEmpty(auditEventListeners)) {
      for (AuditEventListener auditEventListener : auditEventListeners) {
        auditEventListener.onCreated(auditVo);
      }
    }
    return auditVo;
  }

  /**
   * 修改新据
   *
   * @param auditDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public AuditVo update(AuditDto auditDto) {
    this.updateValidate(auditDto);
    Audit audit = this.auditRepository.findByIdAndTenantCode(auditDto.getId(), TenantUtils.getTenantCode());
    List<ProcessBusinessMappingVo> processBusinessMappingVoList = this.findProcessBusinessMappingVo(Lists.newArrayList(audit.getAuditCode()));
    if (!CollectionUtils.isEmpty(processBusinessMappingVoList)) {
      ProcessBusinessMappingVo processBusinessMappingVo = processBusinessMappingVoList.get(0);
      String processStatus = processBusinessMappingVo.getProcessStatus();
      Validate.isTrue(ProcessStatusEnum.RECOVER.getDictCode().equals(processStatus) || ProcessStatusEnum.REJECT.getDictCode().equals(processStatus), "只有状态为【流程追回、审批驳回】费用核销数据能修改，【%s】费用核销申请状态为【%s】", audit.getAuditName(), ProcessStatusEnum.getStatusNameByKey(processStatus));
    }
    // 检查修改数据的审核状态，只有待审核数据可以修改
    AuditVo oldAuditVo = this.nebulaToolkitService.copyObjectByWhiteList(audit, AuditVo.class, LinkedHashSet.class, ArrayList.class);
    Validate.notNull(audit, "修改数据不存在，请检查！");
    audit.setAuditName(auditDto.getAuditName());
    audit.setTotalApplyAmount(auditDto.getTotalApplyAmount());
    audit.setTenantCode(TenantUtils.getTenantCode());
    this.auditRepository.saveOrUpdate(audit);

    AuditVo auditVo = this.nebulaToolkitService.copyObjectByWhiteList(audit, AuditVo.class, LinkedHashSet.class, ArrayList.class);

    auditDto.getAuditActivities().forEach(item -> item.setAuditCode(auditVo.getAuditCode()));
    auditDto.getAuditDetails().forEach(item -> {
      item.setTenantCode(TenantUtils.getTenantCode());
      item.setAuditDto(auditDto);
      item.setAuditCode(auditDto.getAuditCode());
    });
    auditDto.getAuditFiles().forEach(item -> item.setAuditCode(auditVo.getAuditCode()));

    if (!CollectionUtils.isEmpty(auditDto.getAuditInvoices())) {
      auditDto.getAuditInvoices().forEach(item -> item.setAuditCode(auditVo.getAuditCode()));
    }

    String parentCode = audit.getAuditCode();
    Set<DynamicFormService<AuditVo>> dynamicFormServices = dynamicFormServiceResolver.getDynamicFormServices(JSON.parseObject(JSON.toJSONString(auditDto)), DYNAMIC_FORM_FIELD_CODE, AuditVo.class, FormTypeEnum.AUDIT);
    Validate.notEmpty(dynamicFormServices, "根据提供的信息，未能获取到匹配的动态模板服务类信息");
    AuditVo ordinaryActivityVo = nebulaToolkitService.copyObjectByWhiteList(audit, AuditVo.class, HashSet.class, ArrayList.class, "items");
    try {
      for (DynamicFormService<AuditVo> dynamicFormService : dynamicFormServices) {
        dynamicFormService.createDynamicDetails(ordinaryActivityVo, parentCode);
      }
    } finally {
      DynamicFormContextHolder.clearContext();
    }
    // 1、处理核销活动的修改
    this.auditActivitiesService.deleteByAuditCode(auditDto.getAuditCode());
    List<AuditActivitiesVo> auditActivitiesVos = this.auditActivitiesService.createBatch(auditDto.getAuditActivities());
    auditVo.setAuditActivities(auditActivitiesVos);
    // 2、处理核销明细的修改
    Map<String, ?> itemJsons = auditDto.getItems();
    List<AuditDetailDto> detailDtoList = Lists.newArrayList();
    itemJsons.forEach((key, value) -> {
      List<AuditDetailDto> details = JSON.parseArray(JSON.toJSONString(value), AuditDetailDto.class);
      details.forEach(d -> d.setFormMappingCode(key));
      detailDtoList.addAll(details);
    });
    detailDtoList.forEach(dto -> {
      dto.setAuditCode(audit.getAuditCode());
      dto.setAuditDto(auditDto);
    });
    List<AuditDetailVo> auditDetailVos = this.auditDetailService.updateBatch(detailDtoList);
    auditVo.setAuditDetails(auditDetailVos);
    // 3、处理核销附件的修改
    this.auditFilesService.deleteByAuditCode(Arrays.asList(auditDto.getAuditCode()));
    List<AuditFilesVo> auditFilesVos = this.auditFilesService.createBatch(auditDto.getAuditFiles());
    auditVo.setAuditFiles(auditFilesVos);
    // 4、核销发票数据修改
    this.auditInvoiceService.deleteByAuditCode(auditDto.getAuditCode());
    List<AuditInvoiceVo> auditInvoiceVos = this.auditInvoiceService.createBatch(auditDto.getAuditInvoices());
    auditVo.setAuditInvoices(auditInvoiceVos);

    if (!CollectionUtils.isEmpty(auditEventListeners)) {
      for (AuditEventListener auditEventListener : auditEventListeners) {
        auditEventListener.onUpdate(oldAuditVo, auditVo);
      }
    }

    //工作流
    if (auditDto.getProcessBusiness() != null) {
      auditDto.setId(audit.getId());
      auditDto.setAuditCode(audit.getAuditCode());
      //提交工作流
      this.commitProcess(auditDto);
      for (AuditDetailVo auditDetailVo : auditVo.getAuditDetails()) {
        BigDecimal excessAmount = auditDetailVo.getExcessAmount();
        if (excessAmount != null && excessAmount.compareTo(BigDecimal.ZERO) > 0) {
          ActivitiesVo activitiesVo = this.activitiesService.findByActivitiesCode(auditDetailVo.getActivitiesCode());
          ActivitiesDetailVo basicActivityItemVo = this.activitiesDetailService.findByActivitiesDetailCode(auditDetailVo.getActivitiesDetailCode());
          Validate.notNull(basicActivityItemVo, "活动明细【%s】错误，请检查！", auditDetailVo.getActivitiesDetailCode());
          this.costBudgetVoService.occupy(auditDetailVo.getAuditCode(), auditDetailVo.getAuditDetailCode(), basicActivityItemVo.getCostBudgetCode(), excessAmount, activitiesVo.getActivityMark(), CostBudgetItemSourceType.AUDIT.getDescr());
        }
      }
    }

    //编辑业务日志
    AuditLogEventDto logEventDto = new AuditLogEventDto();
    logEventDto.setOriginal(oldAuditVo);
    logEventDto.setNewest(auditVo);
    SerializableBiConsumer<AuditLogEventListener, AuditLogEventDto> onUpdate =
            AuditLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(logEventDto, AuditLogEventListener.class, onUpdate);
    return auditVo;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<Audit> audits = this.auditRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(audits)) {
      return;
    }
    List<String> auditCodes = audits.stream().map(Audit::getAuditCode).collect(Collectors.toList());
    List<ProcessBusinessMappingVo> processBusinessMappingVoList = this.findProcessBusinessMappingVo(auditCodes);
    if (CollectionUtils.isEmpty(processBusinessMappingVoList)) {
      // 检查删除数据的审核状态，只有待审核数据可以删除
      Map<String, String> map = audits.stream().collect(Collectors.toMap(Audit::getAuditCode, Audit::getAuditName));
      processBusinessMappingVoList.forEach(item -> {
        throw new RuntimeException("只有【待提交】费用核销数据能删除，【" + map.get(item.getBusinessNo()) + "】费用核销申请状态为【" + ProcessStatusEnum.getStatusNameByKey(item.getProcessStatus()) + "】");
      });
    }

    Collection<AuditVo> auditVos = this.nebulaToolkitService.copyCollectionByWhiteList(audits, Audit.class, AuditVo.class, LinkedHashSet.class, ArrayList.class);
    // 删除明细
    Set<String> delAuditCodes = audits.stream().map(Audit::getAuditCode).collect(Collectors.toSet());
    this.auditDetailService.deleteByAuditCodes(delAuditCodes);
    Set<String> delIds = audits.stream().map(Audit::getId).collect(Collectors.toSet());
    this.auditRepository.removeByIds(delIds);
    if (!CollectionUtils.isEmpty(auditEventListeners)) {
      for (AuditEventListener auditEventListener : auditEventListeners) {
        for (AuditVo auditVo : auditVos) {
          auditEventListener.onDeleted(auditVo);
        }
      }
    }
    //删除业务日志
    SerializableBiConsumer<AuditLogEventListener, AuditLogEventDto> onDelete =
            AuditLogEventListener::onDelete;
    for (AuditVo auditVo : auditVos) {
      AuditLogEventDto logEventDto = new AuditLogEventDto();
      logEventDto.setOriginal(auditVo);
      this.nebulaNetEventClient.publish(logEventDto, AuditLogEventListener.class, onDelete);
    }
  }

  /**
   * 通过启用状态查询数据
   *
   * @param enableStatus 状态
   * @return 集合数据
   */
  @Override
  public List<AuditVo> findByEnableStatus(String enableStatus) {
    if (StringUtils.isBlank(enableStatus)) {
      return Collections.emptyList();
    }
    List<Audit> audit = this.auditRepository.findByEnableStatus(enableStatus);
    if (CollectionUtils.isEmpty(audit)) {
      return Collections.emptyList();
    }
    Collection<AuditVo> audits = this.nebulaToolkitService.copyCollectionByWhiteList(audit, Audit.class, AuditVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(audits);
  }

  @Override
  @Transactional
  public void doRefund(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return;
    }
    AuditVo auditVo = this.findByCode(auditCode);
    if (auditVo != null && !CollectionUtils.isEmpty(auditVo.getAuditDetails())) {
      auditVo.getAuditDetails().forEach(item -> {
        this.auditDetailService.doRefund(item.getAuditDetailCode());
      });
    }
  }

  @Override
  @Transactional
  public void updateActivitiesAuditStatusByAuditCode(String auditCode) {
    /*
     * 检查核销的活动所有活动明细数据是否均为已核销，根据判断更新活动表的核销状态
     *
     * 1、获取有完全核销活动的编号
     * 3、计算活动已经核销完成的明细数量
     * 4、判断活动是否需要核销的活动都完全核销了，如果数量匹配则更新活动核销标识
     */
    List<AuditDetailVo> auditDetailVos = this.auditDetailService.findByAuditCode(auditCode);
    Set<String> activitiesCodes = auditDetailVos.stream().filter(item -> BooleanEnum.TRUE.getCapital().equals(item.getIsFullAudit())).map(AuditDetailVo::getActivitiesCode).collect(Collectors.toSet());
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return;
    }

    List<ActivitiesVo> activitiesVos = this.activitiesService.findDetailsByActivitiesCodes(activitiesCodes);
    for (ActivitiesVo activitiesVo : activitiesVos) {
      String activitiesCode = activitiesVo.getActivitiesCode();
      int dbWaitAuditDetailNum = activitiesVo.getWaitAuditDetailNum();
      int closedActivitiesDetailNum = this.activitiesDetailService.countByActivitiesCodeAndClosed(activitiesCode);
      int auditedDetailNum = 0;
      List<ActivitiesDetailVo> activitiesDetailVos = this.activitiesDetailService.findByActivitiesCode(activitiesCode);
      for (ActivitiesDetailVo activitiesDetailVo : activitiesDetailVos) {
        AuditBillVo auditBillVo = this.auditBillService.findByActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode());
        if (auditBillVo != null && BooleanEnum.TRUE.getCapital().equals(auditBillVo.getIsFullAudit())) {
          auditedDetailNum++;
        }
      }
      if (dbWaitAuditDetailNum == (auditedDetailNum + closedActivitiesDetailNum)) {
        // 需要更新活动为核销
        this.activitiesService.updateForAudit(activitiesCode);
      }
    }
  }

  @Override
  @Transactional
  public void updateActivitiesAuditStatusByActivitiesDetails(Collection<ActivitiesDetailVo> activitiesDetailVos) {
    if (CollectionUtils.isEmpty(activitiesDetailVos)) {
      return;
    }
    Map<String, List<ActivitiesDetailVo>> activitiesMap = activitiesDetailVos.stream().collect(Collectors.groupingBy(ActivitiesDetailVo::getActivitiesCode));
    Set<Map.Entry<String, List<ActivitiesDetailVo>>> entrySet = activitiesMap.entrySet();
    for (Map.Entry<String, List<ActivitiesDetailVo>> entry : entrySet) {
      String activitiesCode = entry.getKey();
      List<ActivitiesDetailVo> detailVos = entry.getValue();
      ActivitiesVo activitiesVo = this.activitiesService.findByActivitiesCode(activitiesCode);
      int detailNum = activitiesVo.getDetailNum();
      int dbWaitAuditDetailNum = activitiesVo.getWaitAuditDetailNum();
      int closedActivitiesDetailNum = this.activitiesDetailService.countByActivitiesCodeAndClosed(activitiesCode);
      int auditedDetailNum = 0;
      List<ActivitiesDetailVo> byActivitiesCode = this.activitiesDetailService.findByActivitiesCode(activitiesCode);
      for (ActivitiesDetailVo activitiesDetailVo : byActivitiesCode) {
        AuditBillVo auditBillVo = this.auditBillService.findByActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode());
        if (auditBillVo == null) {
          continue;
        }
        if (BooleanEnum.TRUE.getCapital().equals(auditBillVo.getIsFullAudit())) {
          ActivitiesDetailVo detailVo = this.activitiesDetailService.findByActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode());
          if (BooleanEnum.TRUE.getCapital().equals(detailVo.getIsFullAudit())) {
            this.activitiesDetailService.updateFullAuditByActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode(), BooleanEnum.TRUE.getCapital());
          }
          auditedDetailNum++;
        }
      }
      if (detailNum == closedActivitiesDetailNum) {
        this.activitiesService.closeForAudit(activitiesCode);
      }
      if (auditedDetailNum != 0 && dbWaitAuditDetailNum == (auditedDetailNum + closedActivitiesDetailNum)) {
        // 需要更新活动为核销
        this.activitiesService.updateForAudit(activitiesCode);
      }
    }
  }

  /**
   * 生成操作标记
   */
  @Override
  public String preSave() {
    String prefix = UUID.randomUUID().toString();
    // 1天后过期
    this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, TimeUnit.MILLISECONDS.convert(1, TimeUnit.DAYS));
    return prefix;
  }

  /**
   * 验证与操作标记
   */
  private void validationPrefix(AuditDto auditDto) {
    // 验证重复提交标识
    String prefix = auditDto.getPrefix();
    Validate.notBlank(prefix, "错误的预操作标记，请检查!!");
    Validate.isTrue(StringUtils.isNotBlank(this.redisMutexService.getMCode(GENERATE_PREFIX, prefix)), "没有发现预操作标记，可能是因为重复操作的原因!");
    boolean isLock = false;
    try {
      if (isLock = this.redisMutexService.tryLock(prefix, TimeUnit.MILLISECONDS, 1)) {
        this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, 1L);
      } else {
        throw new IllegalArgumentException("请不要重复操作!!");
      }
    } finally {
      if (isLock) {
        this.redisMutexService.unlock(prefix);
      }
    }
  }

  /**
   * 创建验证
   *
   * @param auditDto
   */
  private void createValidate(AuditDto auditDto) {
    Validate.notNull(auditDto, "新增时，对象信息不能为空！");
    auditDto.setId(null);
    // 验证重复操作
    this.validationPrefix(auditDto);
    Validate.notBlank(auditDto.getAuditName(), "新增数据时，核销申请名称不能为空！");
    Validate.notBlank(auditDto.getAuditCode(), "新增数据时，核销申请编号不能为空！");
    Validate.notNull(auditDto.getTotalApplyAmount(), "新增数据时，核销金额汇总不能为空！");
    Audit audit = this.auditRepository.findByCode(auditDto.getAuditCode());
    Validate.isTrue(audit == null, "新增数据时，编号重复请重试！");
    Validate.notEmpty(auditDto.getAuditActivities(), "新增数据时，活动信息不能为空！");
    Validate.notEmpty(auditDto.getAuditDetails(), "新增数据时，核销明细信息不能为空！");
    if (auditDto.getAuditFiles() == null) {
      auditDto.setAuditFiles(Lists.newArrayList());
    }
  }

  /**
   * 修改验证
   *
   * @param auditDto
   */
  private void updateValidate(AuditDto auditDto) {
    Validate.notNull(auditDto, "修改时，对象信息不能为空！");
    // 验证重复操作
    this.validationPrefix(auditDto);
    Validate.notBlank(auditDto.getId(), "修改数据时，核销主键不能为空！");
    Validate.notBlank(auditDto.getAuditName(), "修改数据时，核销申请名称不能为空！");
    Validate.notBlank(auditDto.getAuditName(), "修改数据时，核销申请名称不能为空！");
    Validate.notBlank(auditDto.getAuditCode(), "修改数据时，核销申请编号不能为空！");
    Validate.notNull(auditDto.getTotalApplyAmount(), "修改数据时，核销金额汇总不能为空！");
    Validate.notEmpty(auditDto.getAuditActivities(), "修改数据时，活动信息不能为空！");
  }

  /**
   * 提交工作流进行审批，提交成功返回流程实例ID，提交失败则抛出异常
   *
   * @param dto
   */
  private void commitProcess(AuditDto dto) {
    ProcessBusinessDto processBusiness = dto.getProcessBusiness();
    Validate.notNull(processBusiness, "提交工作流时，未传工作流对象信息!");
    // 检查核销的活动明细是否已经关闭
    List<AuditDetailVo> auditDetailVos = this.auditDetailService.findByAuditCode(dto.getAuditCode());
    auditDetailVos.forEach(item -> {
      ActivitiesDetailVo activitiesDetailVo = this.activitiesDetailService.findByActivitiesDetailCode(item.getActivitiesDetailCode());
      Validate.isTrue(!activitiesDetailVo.getIsClose().equals(BooleanEnum.TRUE.getCapital()), "活动明细[%s]已关闭，不允许进行核销，请检查", activitiesDetailVo.getActivitiesDetailCode());
    });
    processBusiness.setBusinessNo(dto.getAuditCode());
    processBusiness.setBusinessFormJson(JSON.toJSONString(dto));
    processBusiness.setBusinessCode(PayConstant.PROCESS_AUDIT_ACTIVITIES);
    this.processBusinessService.processStart(processBusiness);
  }

  /**
   * 根据业务编码查询工作流业务流程关联信息
   *
   * @param businessNos
   */
  private List<ProcessBusinessMappingVo> findProcessBusinessMappingVo(List<String> businessNos) {
    ProcessBusinessMappingDto processBusinessMappingDto = new ProcessBusinessMappingDto();
    processBusinessMappingDto.setBusinessNos(businessNos);
    processBusinessMappingDto.setBusinessCode(PayConstant.PROCESS_AUDIT_ACTIVITIES);
    return processBusinessMappingService.findMultiByByConditions(processBusinessMappingDto);
  }

  private String analysisSource(String value) {
    switch (value) {
      case "OrdinaryActivity":
        return CostBudgetItemSourceType.ORDINARY_ACTIVITY.getDescr();
      case "ProjectActivity":
        return CostBudgetItemSourceType.PROJECT_ACTIVITY.getDescr();
      case "SchemeActivity":
        return CostBudgetItemSourceType.SCHEMA_ACTIVITY.getDescr();
      default:
        throw new IllegalArgumentException("活动关闭时，未知的费用预算来源，请检查");
    }
  }
}