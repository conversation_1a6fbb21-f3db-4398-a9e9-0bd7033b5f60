package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashInvoiceRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashInvoiceVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 费用兑付明细分页缓存
 */
@Slf4j
@Component
public class FeeCashDetailPageCacheHelper extends BusinessPageCacheHelper<FeeCashDetailVo, FeeCashDetailDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private FeeCashDetailRepository feeCashDetailRepository;
    @Autowired(required = false)
    private FeeCashInvoiceRepository feeCashInvoiceRepository;
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return FeeCashConstant.CACHE_KEY_PREFIX_DETAIL;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<FeeCashDetailDto> getDtoClass() {
        return FeeCashDetailDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<FeeCashDetailVo> getVoClass() {
        return FeeCashDetailVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param feeCashDetailDto
     * @param cacheKey
     */
    @Override
    public List<FeeCashDetailDto> findDtoListFromRepository(FeeCashDetailDto feeCashDetailDto, String cacheKey) {
        if (StringUtils.isBlank(feeCashDetailDto.getCashCode())) {
            return new ArrayList<>();
        }
        List<FeeCashDetailVo> feeCashDetailVos = feeCashDetailRepository.findByCode(feeCashDetailDto.getCashCode());
        if (CollectionUtils.isEmpty(feeCashDetailVos)) {
            return new ArrayList<>();
        }
        List<FeeCashDetailDto> dtoList = (List<FeeCashDetailDto>) nebulaToolkitService.copyCollectionByBlankList(feeCashDetailVos, FeeCashDetailVo.class, FeeCashDetailDto.class, HashSet.class, ArrayList.class);
        List<FeeCashInvoiceVo> invoiceVos = feeCashInvoiceRepository.findByCode(feeCashDetailDto.getCashCode());
        if (CollectionUtils.isNotEmpty(invoiceVos)) {
            Map<String, List<FeeCashInvoiceVo>> listMap = invoiceVos.stream().collect(Collectors.groupingBy(e -> e.getAuditDetailCode()));
            dtoList.forEach(e -> {
                List<FeeCashInvoiceVo> cashInvoiceVos = listMap.get(e.getAuditDetailCode());
                if (CollectionUtils.isNotEmpty(cashInvoiceVos)) {
                    e.setInvoiceList((List<FeeCashInvoiceDto>) nebulaToolkitService.copyCollectionByBlankList(cashInvoiceVos, FeeCashInvoiceVo.class, FeeCashInvoiceDto.class, HashSet.class, ArrayList.class));
                }
            });
        }
        return dtoList;
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashDetailDto> newItem(String cacheKey, List<FeeCashDetailDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashDetailDto> copyItem(String cacheKey, List<FeeCashDetailDto> itemList) {
        List<FeeCashDetailDto> newItemList = (List<FeeCashDetailDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, FeeCashDetailDto.class, FeeCashDetailDto.class, HashSet.class, ArrayList.class);
        for (FeeCashDetailDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 导入新增数据
     * @param cacheKey
     * @param itemList
     * @return
     */
    public void importNewItem(String cacheKey, List<FeeCashDetailDto> itemList) {
        String redisCacheDataKey = this.getRedisCacheDataKey(cacheKey);

        Map<Object, FeeCashDetailDto> updateMap = itemList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }

    /**
     * 获取Dto中的主键
     *
     * @param feeCashDetailDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(FeeCashDetailDto feeCashDetailDto) {
        return feeCashDetailDto.getAuditDetailCode();
    }

    /**
     * 获取是否选中状态
     *
     * @param feeCashDetailDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(FeeCashDetailDto feeCashDetailDto) {
        return feeCashDetailDto.getChecked();
    }

}
