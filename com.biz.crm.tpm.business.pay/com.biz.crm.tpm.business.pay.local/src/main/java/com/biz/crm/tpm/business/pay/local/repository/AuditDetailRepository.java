package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.AuditDetail;
import com.biz.crm.tpm.business.pay.local.mapper.AuditDetailMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 费用核销明细;(tpm_audit_detail)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Component
public class AuditDetailRepository extends ServiceImpl<AuditDetailMapper, AuditDetail> {
  @Autowired
  private AuditDetailMapper auditDetailMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<AuditDetailVo> findByConditions(Pageable pageable, AuditDetailDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<AuditDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return auditDetailMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<AuditDetail>
   */
  public List<AuditDetail> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .in(AuditDetail::getId, ids)
        .eq(AuditDetail::getTenantCode, tenantCode)
        .eq(AuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  /**
   * 根据费用核销编号与租户编号获取对象
   *
   * @param auditCode
   * @return
   */
  public List<AuditDetail> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .eq(AuditDetail::getAuditCode, auditCode)
        .eq(AuditDetail::getTenantCode, tenantCode)
        .eq(AuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  /**
   * 根据编号获取核销明细数据
   *
   * @param auditDetailCode
   * @return
   */
  public AuditDetail findByCode(String auditDetailCode) {
    if (StringUtils.isBlank(auditDetailCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .eq(AuditDetail::getAuditDetailCode, auditDetailCode)
        .eq(AuditDetail::getTenantCode, tenantCode)
        .eq(AuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .one();
  }

  @Override
  public boolean removeByIds(Collection<? extends Serializable> idList) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
        .in(AuditDetail::getId, idList)
        .eq(AuditDetail::getTenantCode, tenantCode)
        .set(AuditDetail::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  public boolean removeByAuditCode(String auditCode) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
        .eq(AuditDetail::getTenantCode, tenantCode)
        .eq(AuditDetail::getAuditCode, auditCode)
        .set(AuditDetail::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();

  }

  public boolean removeByAuditCodes(Collection<String> auditCodes) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
        .eq(AuditDetail::getTenantCode, tenantCode)
        .in(AuditDetail::getAuditCode, auditCodes)
        .set(AuditDetail::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();

  }

  /**
   * 根据活动细类编号以及活动明细编号统计核销数量
   *
   * @param costTypeDetailCode
   * @param activitiesDetailCode
   * @return
   */
  public int countByCostTypeAndActivitiesDetail(String costTypeDetailCode, String activitiesDetailCode) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .eq(AuditDetail::getTenantCode, tenantCode)
        .eq(AuditDetail::getCostTypeDetailCode, costTypeDetailCode)
        .eq(AuditDetail::getActivitiesDetailCode, activitiesDetailCode)
        .eq(AuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .count();
  }

  /**
   * 根据排除的活动编号以及活动明细编号集合，查询是否在审核状态为待提交、审批中、审批驳回、流程追回（0,1,3,4）
   *
   * @param activitiesDetailCode
   * @return
   */
  public List<AuditDetail> findByActivitiesDetailCodes(String activitiesDetailCode) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.auditDetailMapper.findByActivitiesDetailCodes(activitiesDetailCode, tenantCode);
  }

  /**
   * 根据排除的活动编号以及活动明细编号集合，查询是否在审核状态为待提交、审批中、审批驳回、流程追回（0,1,3,4）
   *
   * @param activitiesDetailCode
   * @return
   */
  public List<AuditDetail> findByExcludeActivitiesCodeAndActivitiesDetailCodes(String activitiesCode, String activitiesDetailCode) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.auditDetailMapper.findByExcludeActivitiesCodeAndActivitiesDetailCodes(activitiesCode, activitiesDetailCode, tenantCode);
  }

  public void updateRefundAmount(String auditDetailCode, BigDecimal refundAmount) {
    if (StringUtils.isBlank(auditDetailCode) || refundAmount == null) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    this.lambdaUpdate()
        .eq(AuditDetail::getTenantCode, tenantCode)
        .eq(AuditDetail::getAuditDetailCode, auditDetailCode)
        .eq(AuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .set(AuditDetail::getRefundAmount, refundAmount).update();
  }

  public Set<String> findActivitiesDetailByAuditing(Set<String> costTypeDetailCodes) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.auditDetailMapper.findActivitiesDetailByAuditing(costTypeDetailCodes, tenantCode);
  }

  public Set<String> findActivitiesDetailByFullAudit() {
    String tenantCode = TenantUtils.getTenantCode();
    return this.auditDetailMapper.findActivitiesDetailByFullAudit(tenantCode);
  }

  public boolean updateCostTypeDetailName(String costTypeDetailCode, String costTypeDetailName) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
        .eq(AuditDetail::getTenantCode, tenantCode)
        .eq(AuditDetail::getTenantCode, tenantCode)
        .eq(AuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(AuditDetail::getCostTypeDetailCode, costTypeDetailCode)
        .set(AuditDetail::getCostTypeDetailName, costTypeDetailName).update();
  }

  public Set<String> findActivitiesDetailByAudited() {
    String tenantCode = TenantUtils.getTenantCode();
    return this.auditDetailMapper.findActivitiesDetailByAudited(tenantCode);
  }

  public AuditDetail findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(AuditDetail::getTenantCode,tenantCode)
        .in(AuditDetail::getId,id)
        .one();
  }
}