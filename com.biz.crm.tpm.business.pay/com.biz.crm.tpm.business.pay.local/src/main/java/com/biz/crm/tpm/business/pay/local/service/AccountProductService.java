package com.biz.crm.tpm.business.pay.local.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 费用上账商品表(AccountProduct)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-16 19:11:50
 */
public interface AccountProductService {

  /**
   * 分页查询数据
   *
   * @param pageable       分页对象
   * @param accountProduct 实体对象
   * @return
   */
  Page<AccountProduct> findByConditions(Pageable pageable, AccountProduct accountProduct);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AccountProduct findById(String id);

  /**
   * 通过主键查询单条数据
   *
   * @param code 主键
   * @return 单条数据
   */
  List<AccountProduct> findByCode(String code);

  /**
   * 新增数据
   *
   * @param accountProduct 实体对象
   * @return 新增结果
   */
  AccountProduct create(AccountProduct accountProduct);

  /**
   * 批量新增数据
   *
   * @param accountProducts 实体对象
   * @return 新增结果
   */
  void createBatch(List<AccountProduct> accountProducts);

  /**
   * 修改新据
   *
   * @param accountProduct 实体对象
   * @return 修改结果
   */
  AccountProduct update(AccountProduct accountProduct);

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  void delete(List<String> idList);

  /**
   * 通过上账编码逻辑删除
   */
  void deleteBatchByCodes(List<String> codes);

  /**
   * 通过主表编码集合查询商品
   * @param codes
   * @return
   */
  List<AccountProduct> findByCodes(List<String> codes);
}

