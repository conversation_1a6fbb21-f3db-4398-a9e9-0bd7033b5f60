<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.PosDataMapper">


    <select id="findList" resultType="com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo">
        select * from tpm_pos_data
        <where>
            del_flag = '${@<EMAIL>()}'
            <if test="vo.tenantCode != null and vo.tenantCode != ''">
                and tenant_code = #{vo.tenantCode}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and years = #{vo.years}
            </if>
            <if test="vo.orgCode != null and vo.orgCode != ''">
                and org_code = #{vo.orgCode}
            </if>
            <if test="vo.customerCode != null and vo.customerCode != ''">
                and customer_code = #{vo.customerCode}
            </if>
            <if test="vo.orgName != null and vo.orgName != ''">
                <bind name="orgName" value="'%' + vo.orgName + '%'"/>
                and org_name like #{orgName}
            </if>
            <if test="vo.confirmStatus != null and vo.confirmStatus != ''">
                and confirm_status = #{vo.confirmStatus}
            </if>
            <if test="vo.customerCodes != null and vo.customerCodes.size > 0">
                and customer_code in
                <foreach collection="vo.customerCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerCode != null and vo.customerCode != ''">
                <bind name="customerCode" value="'%' + vo.customerCode + '%'"/>
                 and customer_code like #{customerCode}
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                <bind name="customerName" value="'%' + vo.customerName + '%'"/>
                and customer_name like #{customerName}
            </if>
            order by code desc
        </where>
    </select>

</mapper>