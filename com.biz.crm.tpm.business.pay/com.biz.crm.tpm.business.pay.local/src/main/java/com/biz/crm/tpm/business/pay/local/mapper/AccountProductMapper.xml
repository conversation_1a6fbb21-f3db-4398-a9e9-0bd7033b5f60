<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AccountProductMapper">

  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.AccountProduct" id="TpmAccountProductMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="createAccount" column="create_account" jdbcType="VARCHAR"/>
    <result property="createName" column="create_name" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="modifyAccount" column="modify_account" jdbcType="VARCHAR"/>
    <result property="modifyName" column="modify_name" jdbcType="VARCHAR"/>
    <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    <result property="accountCode" column="account_code" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
    <result property="productName" column="product_name" jdbcType="VARCHAR"/>
  </resultMap>

  <select id="findByConditions" resultMap="TpmAccountProductMap">
    select * from tpm_account_product where tenant_code = #{entity.tenantCode}
  </select>
</mapper>

