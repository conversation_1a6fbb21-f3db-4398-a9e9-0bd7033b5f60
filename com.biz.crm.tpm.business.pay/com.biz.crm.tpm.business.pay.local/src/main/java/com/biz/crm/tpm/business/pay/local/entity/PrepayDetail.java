package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

import static org.apache.ibatis.type.JdbcType.VARCHAR;

/**
 * 实体：活动预付明细;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@ApiModel(value = "PrepayDetail", description = "活动预付明细")
@TableName("tpm_prepay_detail")
@Getter
@Setter
@Entity(name = "tpm_prepay_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_prepay_detail", comment = "活动预付明细")
@Table(name = "tpm_prepay_detail", indexes = {
        @Index(name = "tpm_prepay_detail_index1", columnList = "tenant_code, prepay_code"),
        @Index(name = "tpm_prepay_detail_index2", columnList = "tenant_code, activities_detail_code")})
public class PrepayDetail extends TenantEntity {

  /**
   * 预付编号
   */
  @ApiModelProperty(name = "预付编号", notes = "预付编号")
  @Column(name = "prepay_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付编号 '")
  private String prepayCode;

  /**
   * 预付申请明细编号
   */
  @ApiModelProperty(name = "预付申请明细编号", notes = "预付申请明细编号")
  @Column(name = "prepay_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付申请明细编号 '")
  private String prepayDetailCode;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "申请金额", notes = "申请金额")
  @Column(name = "apply_amount", nullable = false, length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '申请金额 '")
  private BigDecimal applyAmount;

  /**
   * 活动明细编码
   */
  @ApiModelProperty(name = "活动明细编码", notes = "活动明细编码")
  @Column(name = "activities_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动明细编码 '")
  private String activitiesDetailCode;

  /**
   * 预付金额
   */
  @ApiModelProperty(name = "预付金额", notes = "预付金额")
  @Column(name = "prepay_amount", nullable = false, length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '预付金额 '")
  private BigDecimal prepayAmount;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "支付方式", notes = "支付方式")
  @Column(name = "pay_by", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '支付方式 '")
  private String payBy;

  /**
   * 预付原因
   */
  @ApiModelProperty(name = "预付原因", notes = "预付原因")
  @Column(name = "reason", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '预付原因 '")
  private String reason;

  /**
   * 付款方
   */
  @ApiModelProperty(name = "付款方", notes = "付款方")
  @Column(name = "payer", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '付款方 '")
  private String payer;

  /**
   * 支付方式名称
   */
  @ApiModelProperty(name = "支付方式名称", notes = "支付方式名称")
  @Column(name = "pay_by_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT '支付方式名称 '")
  private String payByName;

  /**
   * 付款方账号
   */
  @ApiModelProperty(name = "付款方账号", notes = "付款方账号")
  @Column(name = "payer_account", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '付款方账号 '")
  private String payerAccount;

  /**
   * 收款方
   */
  @ApiModelProperty(name = "收款方", notes = "收款方")
  @Column(name = "payee", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '收款方 '")
  private String payee;

  /**
   * 收款方账号
   */
  @ApiModelProperty(name = "收款方账号", notes = "收款方账号")
  @Column(name = "payee_account", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '收款方账号 '")
  private String payeeAccount;

  /**
   * 数据状态（删除状态）
   */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY, jdbcType = VARCHAR)
  @Column(name = "del_flag", nullable = true, length = 10, columnDefinition = "varchar(10) COMMENT '数据状态（删除状态）'")
  private String delFlag;

  /** 活动细类编号 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编号细类编号", value= "活动细类编号细类编号")
  @Column(name = "cost_type_detail_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动细类编号 '")
  private String costTypeDetailCode;

  /** 活动细类名称 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value= "活动细类名称")
  @Column(name = "cost_type_detail_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '活动细类名称 '")
  private String costTypeDetailName;

  /** 客户名称 */
  @ApiModelProperty(name = "customerName",notes = "客户名称", value= "客户名称")
  @Column(name = "customer_name", nullable = true, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '客户名称 '")
  private String customerName;

  /** 客户编号 */
  @ApiModelProperty(name = "customerCode",notes = "客户编号", value= "客户编号")
  @Column(name = "customer_code", nullable = true, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '客户编号 '")
  private String customerCode;

  /** 终端名称 */
  @ApiModelProperty(name = "terminalName",notes = "终端名称", value= "终端名称")
  @Column(name = "terminal_name", nullable = true, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '终端名称 '")
  private String terminalName;

  /** 终端编号 */
  @ApiModelProperty(name = "terminalCode",notes = "终端编号", value= "终端编号")
  @Column(name = "terminal_code", nullable = true, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '终端编号 '")
  private String terminalCode;

}
