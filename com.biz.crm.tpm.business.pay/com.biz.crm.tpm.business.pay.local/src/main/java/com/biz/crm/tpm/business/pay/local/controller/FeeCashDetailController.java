package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.local.dto.ShowTocDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashDetailService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(tags = "费用兑付-兑付明细功能接口")
@RestController
@RequestMapping("/v1/pay/feeCashDetail")
@Slf4j
public class FeeCashDetailController extends BusinessPageCacheController<FeeCashDetailVo, FeeCashDetailDto> {

    @Autowired(required = false)
    private FeeCashDetailService feeCashDetailService;

    /**
     * 是否展示TOC
     *
     * @param
     * @return
     */
    @ApiOperation(value = "获取费控流程日志地址")
    @GetMapping("showToc")
    public Result<ShowTocDto> showToc(@ApiParam(name = "cacheKeyDetail", value = "明细缓存键") @RequestParam String cacheKeyDetail,
                                  @ApiParam(name = "payeeErpCode", value = "供应商SAP编码") @RequestParam String payeeErpCode) {
        try {
            String yn = this.feeCashDetailService.showToc(cacheKeyDetail, payeeErpCode);
            ShowTocDto dto = new ShowTocDto();
            dto.setBeShow(yn);
            return Result.ok(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "缓存明细导出")
    @GetMapping("findCachePageListImport")
    public Result<Page<FeeCashDetailVo>> findCachePageListImport(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                        @ApiParam(name = "dto", value = "缓存键") FeeCashDetailDto dto) {
        try {
            return this.findCachePageList(pageable,dto.getCacheKey(), dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
