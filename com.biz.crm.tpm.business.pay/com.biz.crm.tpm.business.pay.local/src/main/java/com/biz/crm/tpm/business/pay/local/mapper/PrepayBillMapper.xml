<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.PrepayBillMapper">
  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.PrepayBill" id="PrepayBillMap">
  </resultMap>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.PrepayBillVo">
    select t.*
    from tpm_prepay_bill t
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != '' ">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.activitiesDetailCode != null and dto.activitiesDetailCode != '' ">
        and t.activities_detail_code = #{dto.activitiesDetailCode}
      </if>
    </where>
    order by t.create_time desc, t.id
  </select>

  <update id="addPrepayAmount">
    update tpm_prepay_bill t
    set t.prepaid_amount = t.prepaid_amount + #{amount}
    where t.activities_detail_code = #{activitiesDetailCode}
    and t.tenant_code = #{tenantCode}
  </update>

  <update id="reducePrepayAmount">
    update tpm_prepay_bill t
    set t.prepaid_amount = t.prepaid_amount - #{amount}
    where t.activities_detail_code = #{activitiesDetailCode}
    and t.prepaid_amount >= #{amount}
    and t.tenant_code = #{tenantCode}
  </update>
</mapper>