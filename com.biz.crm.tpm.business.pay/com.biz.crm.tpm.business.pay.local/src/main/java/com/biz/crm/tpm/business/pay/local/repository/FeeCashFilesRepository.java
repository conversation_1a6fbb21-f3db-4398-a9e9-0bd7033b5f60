package com.biz.crm.tpm.business.pay.local.repository;



import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashFiles;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashFilesMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashFilesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 费用兑付附件(FeeCashFiles)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
@Component
public class FeeCashFilesRepository extends ServiceImpl<FeeCashFilesMapper, FeeCashFiles> {

  @Autowired
  private FeeCashFilesMapper feeCashFilesMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  
  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<FeeCashFilesVo> findByCode(String code) {
    List<FeeCashFiles> list = this.lambdaQuery().eq(FeeCashFiles::getCashCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashFiles.class, FeeCashFilesVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param codes
   */
  public void deleteByCodes(List<String> codes) {
    this.remove(Wrappers.lambdaQuery(FeeCashFiles.class)
            .in(FeeCashFiles::getCashCode, codes));
  }
}

