<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.FeeCashDetailMapper">

    <select id="findByAuditDetailCodesBeCommit" resultType="com.biz.crm.tpm.business.pay.local.entity.FeeCashDetail">
        select t.*
        from tpm_fee_cash_detail t
        inner join tpm_fee_cash ta on t.cash_code=ta.cash_code
        <where>
            ta.status != '3'
            AND t.audit_detail_code in
            <foreach collection="codes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="findByAuditDetailCodesBeClose" resultType="com.biz.crm.tpm.business.pay.local.entity.FeeCashDetail">
        select t.*
        from tpm_fee_cash_detail t
        inner join tpm_fee_cash ta on t.cash_code=ta.cash_code
        <where>
            ta.cash_type = 'close'
            AND t.audit_detail_code in
            <foreach collection="codes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findByAuditDetailCodesPass" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        select t.*,
               ta.pass_date, ta.pay_sucess_date, ta.voucher_callback_date, ta.pay_status, ta.cash_method, ta.cash_type
        from tpm_fee_cash_detail t
        inner join tpm_fee_cash ta on t.cash_code=ta.cash_code
        where t.del_flag = '${@<EMAIL>()}'
        <if test="status != null and status != ''">
            and ta.status = #{status}
        </if>
        <if test="cashTypeList != null and cashTypeList.size() > 0">
            and ta.cash_type in
            <foreach collection="cashTypeList" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cashDetailCodes != null and cashDetailCodes.size() > 0">
            and t.cash_detail_code not in
            <foreach collection="cashDetailCodes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        AND (
        t.audit_detail_code in
        <foreach collection="codes" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
        or
        t.scheme_detail_code in
        <foreach collection="codes" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
    </select>

    <select id="findByBillingCompareReport" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        select t.*,ta.status,ta.cash_type,ta.cash_method,SUBSTRING(ad.start_date, 1, 7) yearMonthLy
        from tpm_fee_cash_detail t
        inner join tpm_fee_cash ta on t.cash_code=ta.cash_code
        inner join tpm_marketing_audit_detail ad on t.audit_detail_code=ad.audit_detail_code
        <where>
            ta.status = '2'
            and (ta.cash_method ='ticket_buckle' or ta.cash_method='deductions')
            <if test="dto.cashType != null and dto.cashType != ''">
                and ta.cash_type = #{dto.cashType}
            </if>
            <if test="dto.years != null and dto.years != ''">
                and SUBSTRING(ad.start_date, 1, 7) = #{dto.years}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                <bind name="customerCode" value="'%' + dto.customerCode + '%'"/>
                and t.customer_code like #{customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                <bind name="customerName" value="'%' + dto.customerName + '%'"/>
                and t.customer_name like #{customerName}
            </if>
            <if test="dto.budgetSubjectName != null and dto.budgetSubjectName != ''">
                <bind name="budgetSubjectName" value="'%' + dto.budgetSubjectName + '%'"/>
                and t.budget_subject_name like #{budgetSubjectName}
            </if>
        </where>
        order by t.years,t.id
    </select>


    <select id="findAuditCashAmount" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT
        sum(b.this_cash_amount) this_cash_amount,
        b.audit_detail_code,
        c.audit_amount
        FROM
        tpm_fee_cash a
        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
        LEFT JOIN tpm_marketing_audit_detail c ON b.audit_detail_code = c.audit_detail_code
        WHERE
        a.del_flag = '009'
        and a.cash_type in ('fee','close','material')
        AND b.audit_detail_code in
        <foreach collection="auditDetailCodes" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        GROUP BY
        b.audit_detail_code,
        c.audit_amount
    </select>

    <select id="getAllDuifuJeBAuditDetailCode" resultType="decimal">
        select ifnull(sum(this_cash_amount),0) from tpm_fee_cash_detail d left  join   tpm_fee_cash a ON a.cash_code = d.cash_code
        where d. audit_detail_code =#{auditDetailCode} and (( a.cash_type not in ('wire','account')) or(a.cash_type in ('wire','account') and a.status in ('0','1','2','4','5') ))
    </select>

    <select id="getAllYuFuAvailableJe" resultType="decimal">

        SELECT

        ifnull(sum(case when t.prepay_type != 'activity' THEN
        ifnull(b.this_cash_amount,0)-ifnull(d.this_reversed_amount,0)
        else
        ifnull(c.this_pay_amount,0)-ifnull(d.this_reversed_amount,0)
        end ),0 )

        FROM
        tpm_activity_prepay_detail_record t
        LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code
        AND a.status = '3'
        LEFT JOIN tpm_activity_prepay_detail c ON t.prepay_detail_code = c.prepay_detail_code
        LEFT JOIN (
        SELECT
        b.cash_detail_code,
        b.this_cash_amount
        FROM
        tpm_fee_cash a
        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
        WHERE
        a.status = '3'
        AND cash_type in('wire', 'account')) b ON t.prepay_detail_code = b.cash_detail_code
        LEFT JOIN (
        SELECT
        a.prepay_detail_code, sum(IFNULL(a.this_reversed_amount, 0)) this_reversed_amount
        FROM
        tpm_fee_cash_prepay a left join tpm_fee_cash b on a.cash_code = b.cash_code
        where b.del_flag = '009'
        GROUP BY
        a.prepay_detail_code) d ON t.prepay_detail_code = d.prepay_detail_code
        where t.del_flag = '${@<EMAIL>()}'
        <if test="cashCode != null and cashCode != ''">
            and not exists (select * from tpm_fee_cash_prepay where cash_code=#{cashCode} and prepay_detail_code=d.prepay_detail_code)
        </if>
        <if test="auditDetailCode != null and auditDetailCode != ''">
            and t.audit_detail_code = #{auditDetailCode}
        </if>
    </select>
    <select id="findFullAuditedDetailList" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT
        t.*,
        c.status,
        c.cash_method,
        c.voucher_code,
        c.cash_type,
        c.process_key,
        c.pay_status,
        d.be_full_audit
        FROM tpm_marketing_plan p
        STRAIGHT_JOIN tpm_fee_cash_detail t ON t.scheme_code = p.scheme_code
        INNER JOIN tpm_fee_cash c ON c.cash_code = t.cash_code
        INNER JOIN tpm_marketing_audit_detail d ON t.audit_detail_code = d.audit_detail_code
        WHERE t.del_flag = '009'
        and p.years = #{dto.years}
        and t.belong_department_code in
        <foreach collection="dto.belongDepartmentCodes" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND DATE_FORMAT(c.pass_date, '%Y-%m') = #{dto.years}
        AND (c.cash_method, c.cash_type) IN (
        ('ticket_buckle', 'fee'),
        ('wire_transfer', 'material')
        )
        UNION ALL

        SELECT
        t.*,
        c.status,
        c.cash_method,
        c.voucher_code,
        c.cash_type,
        c.process_key,
        c.pay_status,
        d.be_full_audit
        FROM tpm_marketing_plan p
        STRAIGHT_JOIN tpm_fee_cash_detail t ON t.scheme_code = p.scheme_code
        INNER JOIN tpm_fee_cash c ON c.cash_code = t.cash_code
        INNER JOIN tpm_marketing_audit_detail d ON t.audit_detail_code = d.audit_detail_code
        WHERE t.del_flag = '009'
        and p.years = #{dto.years}
        and t.belong_department_code in
        <foreach collection="dto.belongDepartmentCodes" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND DATE_FORMAT(c.voucher_callback_date, '%Y-%m') = #{dto.years}
        AND (c.cash_method, c.cash_type) IN (
        ('wire_transfer', 'fee'),
        ('deductions', 'material'),
        ('wire_transfer', 'wiredf')
        )
    </select>

    <select id="findAuditedDetailList" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT
        ifnull(sum(t.this_cash_amount),0) as this_cash_amount
        FROM tpm_fee_cash_detail t
        INNER JOIN tpm_fee_cash c on c.cash_code=t.cash_code
        INNER JOIN tpm_marketing_audit_detail d on t.audit_detail_code = d.audit_detail_code
        INNER JOIN tpm_marketing_plan p on t.scheme_code = p.scheme_code
        WHERE t.del_flag = '009'
        and t.years = #{dto.years}
        and p.years = #{dto.years}
        and t.belong_department_code = #{dto.belongDepartmentCode}
        and t.customer_code = #{dto.customerCode}
        and d.be_full_audit = 'Y'
        and c.cash_type =  'fee'
    </select>

</mapper>

