package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingCollectService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: haiyang
 * @Date: 2025-03-31 17:54
 * @Desc:
 */
@RestController
@RequestMapping("/v1/pay/WithHoldingReport")
@Slf4j
@Api(tags = "费用预提报表")
public class WithHoldingReportController {

    @Autowired
    private WithHoldingCollectService withHoldingCollectService;

    @ApiOperation(value = "搭赠及周边")
    @GetMapping("findGiftAndSurroundingList")
    public Result<Page<MarketingPlanCaseVo>> findGiftAndSurroundingList(@PageableDefault(50) Pageable pageable, MarketingPlanCaseVo caseVo, String collectCode) {
        return Result.ok(withHoldingCollectService.findGiftAndSurroundingList(pageable, caseVo, collectCode));
    }

    @ApiOperation(value = "已结案兑付活动")
    @GetMapping("findAuditedAndCashedActivityList")
    public Result<Page<FeeCashDetailVo>> findAuditedAndCashedActivityList(@PageableDefault(50) Pageable pageable, MarketingPlanCaseVo caseVo, String collectCode) {
        return Result.ok(withHoldingCollectService.findAuditedAndCashedActivityList(pageable, caseVo, collectCode));
    }
}
