package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

import static org.apache.ibatis.type.JdbcType.VARCHAR;

/**
 * 实体：费用核销明细;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "AuditDetail", description = "费用核销明细")
@TableName("tpm_audit_detail")
@Getter
@Setter
@Entity(name = "tpm_audit_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_audit_detail", comment = "费用核销明细")
@Table(name = "tpm_audit_detail", indexes = {@Index(name = "tpm_audit_detail_index1", columnList = "tenant_code, audit_code"),
        @Index(name = "tpm_audit_detail_index2", columnList = "tenant_code, audit_detail_code")})
public class AuditDetail extends TenantOpEntity {
  /**
   * 核销申请编码
   */
  @ApiModelProperty(name = "核销申请编码", notes = "核销申请编码")
  @Column(name = "audit_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编码 '")
  private String auditCode;

  /**
   * 核销申请明细编号
   */
  @ApiModelProperty(name = "核销申请明细编号", notes = "核销申请编号")
  @Column(name = "audit_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请明细编号 '")
  private String auditDetailCode;

  /**
   * 活动编码
   */
  @ApiModelProperty(name = "活动编码", notes = "活动编码")
  @Column(name = "activities_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编码 '")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  @Column(name = "activities_name", nullable = false, length = 255, columnDefinition = "VARCHAR(255) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 活动明细编码
   */
  @ApiModelProperty(name = "活动细类编码", notes = "活动细类编码")
  @Column(name = "cost_type_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动细类编码 '")
  private String costTypeDetailCode;

  /**
   * 活动明细名称
   */
  @ApiModelProperty(name = "活动细类名称", notes = "活动细类名称")
  @Column(name = "cost_type_detail_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动细类名称 '")
  private String costTypeDetailName;

  /**
   * 活动大类编码
   */
  @ApiModelProperty(name = "活动大类编码", notes = "活动大类编码")
  @Column(name = "cost_type_category_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动大类编码 '")
  private String costTypeCategoryCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "活动大类名称", notes = "活动大类名称")
  @Column(name = "cost_type_category_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动大类名称 '")
  private String costTypeCategoryName;

  /**
   * 组织编号
   */
  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  @Column(name = "org_code", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编号 '")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  @Column(name = "org_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT '组织名称 '")
  private String orgName;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "支付方式", notes = "支付方式")
  @Column(name = "pay_by", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT '支付方式 '")
  private String payBy;

  /**
   * 支付方式名称
   */
  @ApiModelProperty(name = "支付方式名称", notes = "支付方式名称")
  @Column(name = "pay_by_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT '支付方式名称 '")
  private String payByName;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "申请金额", notes = "申请金额")
  @Column(name = "apply_amount", nullable = false, length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '申请金额 '")
  private BigDecimal applyAmount;

  /**
   * 核销金额
   */
  @ApiModelProperty(name = "核销金额", notes = "核销金额")
  @Column(name = "audit_amount", nullable = false, length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '核销金额 '")
  private BigDecimal auditAmount;

  /**
   * 产品层级编号
   */
  @ApiModelProperty(name = "产品层级编号", notes = "产品层级编号")
  @Column(name = "product_level_code", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '产品层级编号 '")
  private String productLevelCode;

  /**
   * 产品层级名称
   */
  @ApiModelProperty(name = "产品层级名称", notes = "产品层级名称")
  @Column(name = "product_level_name", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '产品层级名称 '")
  private String productLevelName;

  /**
   * 活动明细编码
   */
  @ApiModelProperty(name = "活动明细编码", notes = "活动明细编码")
  @Column(name = "activities_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动明细编码 '")
  private String activitiesDetailCode;

  /**
   * ERP会计科目编码
   */
  @ApiModelProperty(name = "ERP会计科目编码", notes = "")
  @Column(name = "accounting_subjects_code", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT 'ERP会计科目编码 '")
  private String accountingSubjectsCode;

  /**
   * ERP会计科目名称
   */
  @ApiModelProperty(name = "ERP会计科目名称", notes = "")
  @Column(name = "accounting_subjects_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT 'ERP会计科目名称 '")
  private String accountingSubjectsName;

  /**
   * 超额金额
   */
  @ApiModelProperty(name = "超额金额", notes = "超额金额")
  @Column(name = "excess_amount", nullable = true, length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '超额金额 '")
  private BigDecimal excessAmount;

  /**
   * 核销明细备注
   */
  @ApiModelProperty(name = "核销明细备注", notes = "")
  @Column(name = "remark", columnDefinition = "VARCHAR(255) COMMENT '核销明细备注 '")
  private String remark;

  /**
   * 数据状态（删除状态）
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY, jdbcType = VARCHAR)
  @Column(name = "del_flag", nullable = true, length = 10, columnDefinition = "varchar(10) COMMENT '数据状态（删除状态）'")
  private String delFlag;

  /**
   * 开始时间
   */
  @ApiModelProperty(name = "开始时间", notes = "开始时间")
  @Column(name = "begin_time", nullable = false, columnDefinition = "DATETIME COMMENT '开始时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @ApiModelProperty(name = "结束时间", notes = "结束时间")
  @Column(name = "end_time", nullable = false, columnDefinition = "DATETIME COMMENT '结束时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "预算科目名称", notes = "")
  @Column(name = "budget_subjects_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;

  /**
   * 预算科目编号
   */
  @ApiModelProperty(name = "预算科目编号", notes = "")
  @Column(name = "budget_subjects_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '预算科目编号 '")
  private String budgetSubjectsCode;

  /**
   * 是否多次核销(Y/N)
   */
  @ApiModelProperty(name = "是否多次核销(Y/N)", notes = "")
  @Column(name = "is_multiple_audit", nullable = true, length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否多次核销(Y/N) '")
  private String isMultipleAudit;

  /**
   * 退费金额
   */
  @ApiModelProperty(name = "退费金额", notes = "退费金额")
  @Column(name = "refund_amount", nullable = true, length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '退费金额 '")
  private BigDecimal refundAmount;

  /** 是否修改客户 */
  @ApiModelProperty(name = "是否修改客户",notes = "是否修改客户")
  @Column(name = "is_edit_customer", nullable = true, length = 10,  columnDefinition = "VARCHAR(10) COMMENT '是否修改客户 '")
  private String isEditCustomer;

  /** 费用预算编号 */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编号", value= "费用预算编号")
  @Column(name = "cost_budget_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '费用预算编号 '")
  private String costBudgetCode;

  /** 动态表单编号 */
  @ApiModelProperty(name = "formMappingCode",notes = "动态表单编号", value= "动态表单编号")
  @Column(name = "form_mapping_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '动态表单编号 '")
  private String formMappingCode;
}