package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Getter
@Setter
@Entity
@TableName("tpm_pos_data")
@Table(name = "tpm_pos_data", indexes = {@Index(name = "tpm_pos_data_index1", columnList = "tenant_code, code")})
@ApiModel(value = "PosData", description = "pos数据")
@org.hibernate.annotations.Table(appliesTo = "tpm_pos_data", comment = "pos数据")
public class PosData extends TenantFlagOpEntity {

    @ApiModelProperty("编码")
    @Column(name = "code", columnDefinition = "varchar(32) comment '编码'")
    private String code;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("末级部门编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

    @ApiModelProperty("销售数量")
    @Column(name = "quantity", columnDefinition = "decimal(18,4) comment '销售数量'")
    private BigDecimal quantity;

    @ApiModelProperty("销售金额")
    @Column(name = "amount", columnDefinition = "decimal(18,4) comment '销售金额'")
    private BigDecimal amount;

    @ApiModelProperty("确认状态")
    @Column(name = "confirm_status",columnDefinition = "varchar(10) comment '确认状态'")
    private String confirmStatus;

    @ApiModelProperty("创建人组织")
    @Column(name = "create_org_code",columnDefinition = "varchar(32) comment '创建人组织编码'")
    private String createOrgCode;

    @ApiModelProperty("创建人组织名称")
    @Column(name = "create_org_name",columnDefinition = "varchar(64) comment '创建人组织'")
    private String createOrgName;

    @ApiModelProperty("创建人职位编码")
    @Column(name = "create_post_code",columnDefinition = "varchar(32) comment '创建人职位编码'")
    private String createPostCode;

    @ApiModelProperty("创建人职位")
    @Column(name = "create_post_name",columnDefinition = "varchar(64) comment '创建人职位'")
    private String createPostName;

}
