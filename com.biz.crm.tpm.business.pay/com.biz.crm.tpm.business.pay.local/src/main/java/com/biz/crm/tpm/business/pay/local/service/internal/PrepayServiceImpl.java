package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailsDto;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.pay.local.entity.Prepay;
import com.biz.crm.tpm.business.pay.local.repository.PrepayRepository;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailService;
import com.biz.crm.tpm.business.pay.local.service.PrepayBillService;
import com.biz.crm.tpm.business.pay.local.service.PrepayDetailService;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.PrepayLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.event.PrepayEventListener;
import com.biz.crm.tpm.business.pay.sdk.event.log.PrepayLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.PrepayService;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayActivityItemVo;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayBillVo;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayVo;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessMappingDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.ProcessBusinessMappingService;
import com.biz.crm.workflow.sdk.service.ProcessBusinessService;
import com.biz.crm.workflow.sdk.vo.ProcessBusinessMappingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.enums.RedisKeys.GENERATE_PREFIX;

/**
 * 活动预付;(tpm_prepay_activities)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-22
 */
@Service("prepayService")
public class PrepayServiceImpl implements PrepayService {
  @Autowired
  private PrepayRepository prepayRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private RedisMutexService redisMutexService;
  @Autowired(required = false)
  private List<PrepayEventListener> prepayEventListeners;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired
  private PrepayBillService prepayBillService;
  @Autowired
  private PrepayDetailService prepayDetailService;
  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private ActivitiesDetailService activitiesDetailService;
  @Autowired
  private AuditDetailService auditDetailService;
  @Autowired
  private ActivitiesService activitiesService;
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired(required = false)
  private ProcessBusinessMappingService processBusinessMappingService;
  @Autowired(required = false)
  private ProcessBusinessService processBusinessService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<PrepayVo> findByConditions(Pageable pageable, PrepayDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new PrepayDto();
    }
    return this.prepayRepository.findByConditions(pageable, dto);
  }

  @Override
  public Page<ActivitiesVo> findActivitiesByConditions(Pageable pageable, ActivitiesDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesDto();
    }
    /*
     * 预付活动数据需要过滤
     * 1、未全部核销或关闭活动明细的活动
     */
    return this.activitiesService.findByConditions(pageable, dto);
  }

  @Override
  public Page<PrepayActivityItemVo> findActivitiesDetailByConditions(Pageable pageable, ActivitiesDetailDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesDetailDto();
    }
    // 封装需要进行过滤的活动细类编号
    Set<String> excludeCostTypeDetailCodes = Sets.newHashSet();
    Set<String> excludeActivitiesDetailCodes = Sets.newHashSet();

    // 不需要核销的活动细类
    CostTypeDetailsDto notAuditDto = new CostTypeDetailsDto();
    notAuditDto.setIsAudit(BooleanEnum.FALSE.getCapital());
    Set<String> notAuditCostTypeDetailCodes = this.costTypeDetailVoService.findCodeByCondition(notAuditDto);
    // 自动核销的活动细类
    CostTypeDetailsDto autoAuditDto = new CostTypeDetailsDto();
    autoAuditDto.setIsAudit(BooleanEnum.TRUE.getCapital());
    autoAuditDto.setIsAutoAudit(BooleanEnum.TRUE.getCapital());
    Set<String> autoAuditCostTypeDetailCodes = this.costTypeDetailVoService.findCodeByCondition(autoAuditDto);
    // 单次核销的活动细类
    CostTypeDetailsDto notMultiAuditDto = new CostTypeDetailsDto();
    notMultiAuditDto.setIsAudit(BooleanEnum.TRUE.getCapital());
    notMultiAuditDto.setIsMultipleAudit(BooleanEnum.FALSE.getCapital());
    Set<String> notMultiAuditCostTypeDetailCodes = this.costTypeDetailVoService.findCodeByCondition(notMultiAuditDto);

    // 获取核销中的活动明细编号
    Set<String> auditingActivitiesDetailCodes = this.auditDetailService.findActivitiesDetailByAuditing(notMultiAuditCostTypeDetailCodes);
    Set<String> auditedActivitiesDetailCodes = this.auditDetailService.findActivitiesDetailByAudited();

    // 1、筛选不需要进行核销的活动细类
    excludeCostTypeDetailCodes.addAll(notAuditCostTypeDetailCodes);
    // 2、筛选自动核销的
    excludeCostTypeDetailCodes.addAll(autoAuditCostTypeDetailCodes);
    // 3、筛选单次核销并已经在核销中的
    excludeActivitiesDetailCodes.addAll(auditingActivitiesDetailCodes);
    // 4、有和效果的明细
    excludeActivitiesDetailCodes.addAll(auditedActivitiesDetailCodes);

    dto.setExcludeCostTypeDetailCodes(excludeCostTypeDetailCodes);
    dto.setExcludeItemCodes(excludeActivitiesDetailCodes);
    Page<ActivitiesDetailVo> basicPage = this.activitiesDetailService.findByConditions(pageable, dto);
    List<PrepayActivityItemVo> result = Lists.newArrayList();
    Page<PrepayActivityItemVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    if (!CollectionUtils.isEmpty(basicPage.getRecords())) {
      for (ActivitiesDetailVo basicActivityItemVo : basicPage.getRecords()) {
        PrepayActivityItemVo prepayActivityItemVo = this.nebulaToolkitService.copyObjectByWhiteList(basicActivityItemVo, PrepayActivityItemVo.class, LinkedHashSet.class, ArrayList.class, "costTypeDetailVo");
        PrepayBillVo prepayBillVo = this.prepayBillService.findByActivitiesDetailCode(basicActivityItemVo.getActivitiesDetailCode());
        if (prepayBillVo == null) {
          prepayActivityItemVo.setPrepaidAmount(BigDecimal.ZERO);
        } else {
          prepayActivityItemVo.setPrepaidAmount(prepayBillVo.getPrepaidAmount());
        }
        result.add(prepayActivityItemVo);
      }
    }
    page.setRecords(result);
    page.setTotal(basicPage.getTotal());
    return page;
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public PrepayVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    Prepay prepay = this.prepayRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (prepay == null) {
      return null;
    }
    PrepayVo prepayVo = this.nebulaToolkitService.copyObjectByWhiteList(prepay, PrepayVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetail(prepayVo);
    return prepayVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<PrepayVo> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<Prepay> prepayActivities = this.prepayRepository.findByIds(ids);
    Collection<PrepayVo> prepayVos = this.nebulaToolkitService.copyCollectionByWhiteList(prepayActivities, Prepay.class, PrepayVo.class, LinkedHashSet.class, ArrayList.class);
    prepayVos.forEach(this::fillDetail);
    return Lists.newArrayList(prepayVos);
  }

  /**
   * 通过比编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  @Override
  public PrepayVo findByCode(String code) {
    return this.findByPrepayCode(code);
  }

  /**
   * 根绝业务编号prepayCode获取业务数据
   *
   * @param prepayCode
   * @return 单条数据
   */
  @Override
  public PrepayVo findByPrepayCode(String prepayCode) {
    if (StringUtils.isBlank(prepayCode)) {
      return null;
    }
    Prepay prepay = this.prepayRepository.findByPrepayCode(prepayCode);
    if (prepay == null) {
      return null;
    }
    PrepayVo prepayVo = this.nebulaToolkitService.copyObjectByWhiteList(prepay, PrepayVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetail(prepayVo);
    return prepayVo;
  }

  /**
   * 根绝业务编号prepayCode获取业务数据
   *
   * @param prepayCodes
   * @return 多条数据
   */
  @Override
  public List<PrepayVo> findByPrepayCodes(List<String> prepayCodes) {
    if (CollectionUtils.isEmpty(prepayCodes)) {
      return Collections.emptyList();
    }
    List<Prepay> prepayActivitiesses = this.prepayRepository.findByPrepayCodes(prepayCodes);
    if (CollectionUtils.isEmpty(prepayActivitiesses)) {
      return Collections.emptyList();
    }
    Collection<PrepayVo> prepayVos = this.nebulaToolkitService.copyCollectionByWhiteList(prepayActivitiesses, Prepay.class, PrepayVo.class, LinkedHashSet.class, ArrayList.class);
    prepayVos.forEach(this::fillDetail);
    return Lists.newArrayList(prepayVos);
  }

  /**
   * 填充明细数据
   *
   * @param prepayVo
   */
  private void fillDetail(PrepayVo prepayVo) {
    // 明细数据
    List<PrepayDetailVo> prepayDetailVos = this.prepayDetailService.findByPrepayCode(prepayVo.getPrepayCode());
    prepayVo.setPrepayDetails(prepayDetailVos);
  }

  /**
   * 新增数据
   *
   * @param prepayDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public PrepayVo create(PrepayDto prepayDto) {
    prepayDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    prepayDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    String code = this.generateCodeService.generateCode(PayConstant.PREPAY_DETAIL_LADDER_CODE, 1).get(0);
    prepayDto.setPrepayCode(code);
    this.createValidate(prepayDto);
    Prepay prepay = this.nebulaToolkitService.copyObjectByWhiteList(prepayDto, Prepay.class, LinkedHashSet.class, ArrayList.class);
    prepay.setTenantCode(TenantUtils.getTenantCode());
    this.prepayRepository.saveOrUpdate(prepay);
    PrepayVo prepayVo = this.nebulaToolkitService.copyObjectByWhiteList(prepay, PrepayVo.class, LinkedHashSet.class, ArrayList.class);
    prepayVo.setId(prepay.getId());

    // 预付明细处理
    prepayDto.getPrepayDetails().forEach(item -> {
      item.setTenantCode(TenantUtils.getTenantCode());
      item.setPrepayCode(prepayVo.getPrepayCode());
    });
    // 保存明细数据
    this.prepayDetailService.createBatch(prepayDto.getPrepayDetails());

    if (!CollectionUtils.isEmpty(prepayEventListeners)) {
      for (PrepayEventListener prepayEventListener : prepayEventListeners) {
        prepayEventListener.onCreated(prepayVo);
      }
    }

    //工作流
    if (prepayDto.getProcessBusiness() != null) {
      prepayDto.setId(prepay.getId());
      //提交工作流
      this.commitProcess(prepayDto);
    }
    //新增业务日志
    PrepayLogEventDto logEventDto = new PrepayLogEventDto();
    logEventDto.setOriginal(null);
    logEventDto.setNewest(prepayVo);
    SerializableBiConsumer<PrepayLogEventListener, PrepayLogEventDto> onCreate =
        PrepayLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(logEventDto, PrepayLogEventListener.class, onCreate);
    return prepayVo;
  }

  /**
   * 批量新增
   *
   * @param prepayDtos
   * @return
   */
  @Transactional
  @Override
  public List<PrepayVo> createBatch(List<PrepayDto> prepayDtos) {
    if (CollectionUtils.isEmpty(prepayDtos)) {
      return Lists.newArrayList();
    }
    List<PrepayVo> prepayVos = Lists.newArrayList();
    for (PrepayDto prepayDto : prepayDtos) {
      PrepayVo prepayVo = this.create(prepayDto);
      prepayVos.add(prepayVo);
    }
    return prepayVos;
  }

  /**
   * 修改新据
   *
   * @param prepayDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public PrepayVo update(PrepayDto prepayDto) {
    this.updateValidate(prepayDto);
    Prepay prepay = this.prepayRepository.findByIdAndTenantCode(prepayDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(prepay, "修改数据不存在，请检查！");
    PrepayVo oldPrepayVo = this.nebulaToolkitService.copyObjectByWhiteList(prepay, PrepayVo.class, LinkedHashSet.class, ArrayList.class);
    // 检查修改数据的审核状态，只有待审核数据可以删除
    List<ProcessBusinessMappingVo> processBusinessMappingVoList = this.findProcessBusinessMappingVo(Lists.newArrayList(prepay.getPrepayCode()));
    if (!CollectionUtils.isEmpty(processBusinessMappingVoList)) {
      ProcessBusinessMappingVo processBusinessMappingVo = processBusinessMappingVoList.get(0);
      String processStatus = processBusinessMappingVo.getProcessStatus();
      Validate.isTrue(ProcessStatusEnum.RECOVER.getDictCode().equals(processStatus) || ProcessStatusEnum.REJECT.getDictCode().equals(processStatus), "只有状态为【待提交、流程追回、审批驳回】数据能修改，【%s】申请状态为【%s】", prepay.getActivitiesName(), ProcessStatusEnum.getStatusNameByKey(processStatus));
    }
    prepay.setRemark(prepayDto.getRemark());
    prepay.setPrepayCode(prepayDto.getPrepayCode());
    prepay.setActivitiesCode(prepayDto.getActivitiesCode());
    prepay.setActivitiesName(prepayDto.getActivitiesName());
    prepay.setTotalPrepayAmount(prepayDto.getTotalPrepayAmount());
    prepay.setTenantCode(TenantUtils.getTenantCode());
    this.prepayRepository.saveOrUpdate(prepay);
    PrepayVo prepayVo = this.nebulaToolkitService.copyObjectByWhiteList(prepay, PrepayVo.class, LinkedHashSet.class, ArrayList.class);

    // 保存明细数据
    this.prepayDetailService.updateBatch(prepayDto.getPrepayDetails());

    if (!CollectionUtils.isEmpty(prepayEventListeners)) {
      for (PrepayEventListener prepayEventListener : prepayEventListeners) {
        prepayEventListener.onUpdate(oldPrepayVo, prepayVo);
      }
    }

    //工作流
    if (prepayDto.getProcessBusiness() != null) {
      prepayDto.setId(prepay.getId());
      //提交工作流
      this.commitProcess(prepayDto);
    }

    //编辑业务日志
    PrepayLogEventDto logEventDto = new PrepayLogEventDto();
    logEventDto.setOriginal(oldPrepayVo);
    logEventDto.setNewest(prepayVo);
    SerializableBiConsumer<PrepayLogEventListener, PrepayLogEventDto> onUpdate =
        PrepayLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(logEventDto, PrepayLogEventListener.class, onUpdate);
    return prepayVo;
  }

  /**
   * 批量修改据
   *
   * @param prepayDtos 实体对象
   * @return 修改结果
   */
  @Override
  public List<PrepayVo> updateBatch(List<PrepayDto> prepayDtos) {
    if (CollectionUtils.isEmpty(prepayDtos)) {
      return Collections.emptyList();
    }
    List<String> codes = prepayDtos.stream().map(PrepayDto::getPrepayCode).collect(Collectors.toList());
    List<PrepayVo> dbPrepayVos = this.findByPrepayCodes(codes);
    Set<String> dbIds = dbPrepayVos.stream().map(PrepayVo::getId).collect(Collectors.toSet());
    Set<String> currentIds = prepayDtos.stream().map(PrepayDto::getId).collect(Collectors.toSet());
    Set<String> delData = Sets.difference(dbIds, currentIds);
    if (!CollectionUtils.isEmpty(delData)) {
      this.delete(delData);
    }
    List<PrepayDto> addDtos = prepayDtos.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
    List<PrepayDto> updateDtos = prepayDtos.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
    List<PrepayVo> prepayVos = this.createBatch(addDtos);
    if (!CollectionUtils.isEmpty(updateDtos)) {
      updateDtos.forEach(item -> {
        prepayVos.add(this.update(item));
      });
    }
    return prepayVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<Prepay> prepayActivitiesses = this.prepayRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(prepayActivitiesses)) {
      return;
    }
    Collection<PrepayVo> prepayVos = this.nebulaToolkitService.copyCollectionByWhiteList(prepayActivitiesses, Prepay.class, PrepayVo.class, LinkedHashSet.class, ArrayList.class);

    // 检查删除数据的审核状态，只有待审核数据可以删除
    List<String> prepayCodes = prepayActivitiesses.stream().map(Prepay::getPrepayCode).collect(Collectors.toList());
    List<ProcessBusinessMappingVo> processBusinessMappingVoList = this.findProcessBusinessMappingVo(prepayCodes);
    if (CollectionUtils.isEmpty(processBusinessMappingVoList)) {
      processBusinessMappingVoList.forEach(item -> {
        throw new RuntimeException("只有【待提交】预付数据能删除，预付编号【" + item.getBusinessNo() + "】预付申请状态为【" + ProcessStatusEnum.getStatusNameByKey(item.getProcessStatus()) + "】");
      });
    }

    this.prepayRepository.removeByIds(ids);
    if (!CollectionUtils.isEmpty(prepayEventListeners)) {
      for (PrepayEventListener prepayEventListener : prepayEventListeners) {
        for (PrepayVo prepayVo : prepayVos) {
          prepayEventListener.onDeleted(prepayVo);
        }
      }
    }
    //删除业务日志
    SerializableBiConsumer<PrepayLogEventListener, PrepayLogEventDto> onDelete =
        PrepayLogEventListener::onDelete;
    for (PrepayVo prepayVo : prepayVos) {
      PrepayLogEventDto logEventDto = new PrepayLogEventDto();
      logEventDto.setOriginal(prepayVo);
      this.nebulaNetEventClient.publish(logEventDto, PrepayLogEventListener.class, onDelete);
    }
  }


  /**
   * 生成操作标记
   */
  @Override
  public String preSave() {
    String prefix = UUID.randomUUID().toString();
    // 1天后过期
    this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, TimeUnit.MILLISECONDS.convert(1, TimeUnit.DAYS));
    return prefix;
  }

  /**
   * 验证与操作标记
   */
  private void validationPrefix(PrepayDto prepayDto) {
    // 验证重复提交标识
    String prefix = prepayDto.getPrefix();
    Validate.notBlank(prefix, "错误的预操作标记，请检查!!");
    Validate.isTrue(StringUtils.isNotBlank(this.redisMutexService.getMCode(GENERATE_PREFIX, prefix)), "没有发现预操作标记，可能是因为重复操作的原因!");
    boolean isLock = false;
    try {
      if (isLock = this.redisMutexService.tryLock(prefix, TimeUnit.MILLISECONDS, 1)) {
        this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, 1L);
      } else {
        throw new IllegalArgumentException("请不要重复操作!!");
      }
    } finally {
      if (isLock) {
        this.redisMutexService.unlock(prefix);
      }
    }
  }

  /**
   * 创建验证
   *
   * @param prepayDto
   */
  private void createValidate(PrepayDto prepayDto) {
    Validate.notNull(prepayDto, "新增时，对象信息不能为空！");
    prepayDto.setId(null);
    // 验证重复操作
    this.validationPrefix(prepayDto);
    Validate.notBlank(prepayDto.getDelFlag(), "新增数据时，数据状态（删除状态）不能为空！");
    Validate.notBlank(prepayDto.getPrepayCode(), "新增数据时，预付编号不能为空！");
    Validate.notBlank(prepayDto.getActivitiesCode(), "新增数据时，活动编号不能为空！");
    Validate.notBlank(prepayDto.getActivitiesName(), "新增数据时，活动名称不能为空！");
    Validate.notNull(prepayDto.getTotalPrepayAmount(), "新增数据时，预付金额合计不能为空！");
    Validate.notEmpty(prepayDto.getPrepayDetails(), "新增数据时，预付明细不能为空！");

    BigDecimal totalPrepayAmount = prepayDto.getPrepayDetails().stream().map(PrepayDetailDto::getPrepayAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    Validate.isTrue(totalPrepayAmount.compareTo(prepayDto.getTotalPrepayAmount()) == 0, "新增数据时，申请预付总金额与明细不匹配，请检查！");
  }

  /**
   * 修改验证
   *
   * @param prepayDto
   */
  private void updateValidate(PrepayDto prepayDto) {
    Validate.notNull(prepayDto, "修改时，对象信息不能为空！");
    // 验证重复操作
    this.validationPrefix(prepayDto);
    Validate.notBlank(prepayDto.getId(), "修改数据时，主键不能为空！");
    Validate.notBlank(prepayDto.getPrepayCode(), "修改数据时，预付编号不能为空！");
    Validate.notBlank(prepayDto.getActivitiesCode(), "修改数据时，活动编号不能为空！");
    Validate.notBlank(prepayDto.getActivitiesName(), "修改数据时，活动名称不能为空！");
    Validate.notNull(prepayDto.getTotalPrepayAmount(), "修改数据时，预付金额合计不能为空！");
    Validate.notEmpty(prepayDto.getPrepayDetails(), "修改数据时，预付明细不能为空！");

    BigDecimal totalPrepayAmount = prepayDto.getPrepayDetails().stream().map(PrepayDetailDto::getPrepayAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    Validate.isTrue(totalPrepayAmount.compareTo(prepayDto.getTotalPrepayAmount()) == 0, "修改数据时，申请预付总金额与明细不匹配，请检查！");
  }

  /**
   * 提交工作流进行审批，提交成功返回流程实例ID，提交失败则抛出异常
   *
   * @param dto
   */
  private void commitProcess(PrepayDto dto) {
    ProcessBusinessDto processBusiness = dto.getProcessBusiness();
    Validate.notNull(processBusiness, "提交工作流时，未传工作流对象信息!");
    processBusiness.setBusinessNo(dto.getPrepayCode());
    processBusiness.setBusinessFormJson(JsonUtils.obj2JsonString(dto));
    processBusiness.setBusinessCode(PayConstant.PROCESS_PREPAY_ACTIVITIES);
    this.processBusinessService.processStart(processBusiness);
  }

  /**
   * 根据业务编码查询工作流业务流程关联信息
   *
   * @param businessNos
   */
  private List<ProcessBusinessMappingVo> findProcessBusinessMappingVo(List<String> businessNos) {
    ProcessBusinessMappingDto processBusinessMappingDto = new ProcessBusinessMappingDto();
    processBusinessMappingDto.setBusinessNos(businessNos);
    processBusinessMappingDto.setBusinessCode(PayConstant.PROCESS_PREPAY_ACTIVITIES);
    return processBusinessMappingService.findMultiByByConditions(processBusinessMappingDto);
  }

}
