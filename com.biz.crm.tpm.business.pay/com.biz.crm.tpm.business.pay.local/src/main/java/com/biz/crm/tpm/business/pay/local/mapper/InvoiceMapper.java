package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.Invoice;
import com.biz.crm.tpm.business.pay.sdk.dto.InvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceVo;
import org.apache.ibatis.annotations.Param;

/**
 * 发票池(Invoice)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
public interface InvoiceMapper extends BaseMapper<Invoice> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页对象
   * @param dto 查询实体
   * @return 所有数据
  */
  Page<InvoiceVo> findByConditions(@Param("page") Page<InvoiceVo> page, @Param("dto") InvoiceDto dto);
}

