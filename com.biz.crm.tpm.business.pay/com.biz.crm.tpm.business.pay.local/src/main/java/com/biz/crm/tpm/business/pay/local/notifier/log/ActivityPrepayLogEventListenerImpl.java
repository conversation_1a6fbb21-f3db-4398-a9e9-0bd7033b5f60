package com.biz.crm.tpm.business.pay.local.notifier.log;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.ActivityPrepayLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.event.log.ActivityPrepayLogEventListener;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ActivityPrepayLogEventListenerImpl implements ActivityPrepayLogEventListener {
    
    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 创建事件
     *
     * @param eventDto
     */
    @Override
    public void onCreate(ActivityPrepayLogEventDto eventDto) {
        ActivityPrepayDto newest = eventDto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(null);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    /**
     * 删除事件
     *
     * @param eventDto
     */
    @Override
    public void onDelete(ActivityPrepayLogEventDto eventDto) {
        List<ActivityPrepayDto> newList = Lists.newArrayList();
        if (Objects.nonNull(eventDto.getNewest())) {
            newList.add(eventDto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(eventDto.getNewestList())) {
            newList.addAll(eventDto.getNewestList());
        }
        List<ActivityPrepayDto> oldList = (List<ActivityPrepayDto>) this.nebulaToolkitService.copyCollectionByBlankList(newList, ActivityPrepayDto.class, ActivityPrepayDto.class, HashSet.class, ArrayList.class);
        Map<String, ActivityPrepayDto> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(ActivityPrepayDto::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            ActivityPrepayDto original = oldMap.getOrDefault(newest.getId(), new ActivityPrepayDto());
            original.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setDelFlag(DelFlagStatusEnum.DELETE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    /**
     * 更新日志
     *
     * @param eventDto
     */
    @Override
    public void onUpdate(ActivityPrepayLogEventDto eventDto) {
        ActivityPrepayDto original = eventDto.getOriginal();
        ActivityPrepayDto newest = eventDto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }
}
