package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayPayee;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动预付收款信息(ActivityPrepayPayee)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-04 20:03:02
 */
public interface ActivityPrepayPayeeMapper extends BaseMapper<ActivityPrepayPayee> {

    /**
     * 更新付款状态
     *
     * @param dtoList
     */
    void hecPayStatusCallback(@Param("dtoList") List<HecCallbackDto> dtoList);
}

