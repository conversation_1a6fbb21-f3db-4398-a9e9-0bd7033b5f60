package com.biz.crm.tpm.business.pay.local.repository;



import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecordItem;
import com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayDetailRecordItemMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordItemDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AccountVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordItemVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * 活动预付明细跟踪(ActivityPrepayDetailRecordItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-20 17:18:38
 */
@Component
public class ActivityPrepayDetailRecordItemRepository extends ServiceImpl<ActivityPrepayDetailRecordItemMapper, ActivityPrepayDetailRecordItem> {

  @Autowired
  private ActivityPrepayDetailRecordItemMapper activityPrepayDetailRecordItemMapper;

  public List<ActivityPrepayDetailRecordItem> findByCode(String code) {
    return lambdaQuery().eq(ActivityPrepayDetailRecordItem::getPrepayDetailCode, code).list();
  }

  public List<ActivityPrepayDetailRecordItem> findByBusinessCodes(List<String> businessCodes) {
    if (CollectionUtils.isEmpty(businessCodes)) {
      return Lists.newArrayList();
    }
    return this.lambdaQuery()
        .in(ActivityPrepayDetailRecordItem::getBusinessCode, businessCodes)
        .list();
  }

  public List<ActivityPrepayDetailRecordItem> findByBusinessCodeAndPreDetailcode(String businessCode,String preDetailCode) {
    if (StringUtils.isEmpty(businessCode)||StringUtils.isEmpty(preDetailCode)) {
      return Lists.newArrayList();
    }
    return this.lambdaQuery()
            .eq(ActivityPrepayDetailRecordItem::getBusinessCode, businessCode)
            .eq(ActivityPrepayDetailRecordItem::getPrepayDetailCode,preDetailCode)
            .list();
  }

  public Page<ActivityPrepayDetailRecordItemVo> findByConditions(Pageable pageable, ActivityPrepayDetailRecordItemDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivityPrepayDetailRecordItemVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    Page<ActivityPrepayDetailRecordItemVo> pageList = this.baseMapper.findByConditions(page, dto);
    return pageList;
  }
}

