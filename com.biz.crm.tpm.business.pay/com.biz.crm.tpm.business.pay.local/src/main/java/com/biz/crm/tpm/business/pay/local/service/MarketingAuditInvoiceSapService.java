package com.biz.crm.tpm.business.pay.local.service;


import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditSapInvoiceVo;

import java.util.List;

/**
 * 结案票扣明细  SAP数据
 *
 * <AUTHOR>
 * @version v1.0.0
 * @date 2024/9/29 15:57
 */
public interface MarketingAuditInvoiceSapService {
    /**
     * 客户+公司+活动归属年月 获取SAP数据
     *
     * @return
     * <AUTHOR>
     * @date 2024/9/29 16:03
     */
    List<MarketingAuditSapInvoiceVo> getSapCustomerUnInvoicedInfo(List<JSONObject> customerJsonList);
}
