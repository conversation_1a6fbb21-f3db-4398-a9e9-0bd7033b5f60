package com.biz.crm.tpm.business.pay.local.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.pay.sdk.service.AccountVoService;
import com.biz.crm.tpm.business.pay.sdk.vo.AccountVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 费用上账相关的http接口
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
@RestController
@RequestMapping("/v1/pay/Account")
@Slf4j
@Api(tags = "费用上账")
public class AccountVoController {
  /**
   * 服务对象
   */
  @Autowired
  private AccountVoService AccountVoService;

  /**
   * 分页查询所有数据
   *
   * @param pageable 分页对象
   * @param account  查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<AccountVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                  @ApiParam(name = "Account", value = "费用上账Dto") AccountDto account) {
    try {
      Page<AccountVo> page = this.AccountVoService.findByConditions(pageable, account);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<AccountVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id") String id) {
    try {
      AccountVo Account = this.AccountVoService.findById(id);
      return Result.ok(Account);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param account 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<AccountVo> create(@ApiParam(name = "Account", value = "费用上账主表") @RequestBody List<AccountDto> account) {
    try {
      AccountVo result = this.AccountVoService.create(account);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   *
   * @param account 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<AccountVo> update(@ApiParam(name = "Account", value = "费用上账主表") @RequestBody AccountDto account) {
    try {
      AccountVo result = this.AccountVoService.update(account);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam List<String> idList) {
    try {
      this.AccountVoService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据时，分页查询核销信息
   *
   * @param pageable 分页对象
   * @param account  查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询核销信息")
  @GetMapping("findAuditByDto")
  public Result<Page<AccountVo>> findAuditByDto(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                  @ApiParam(name = "Account", value = "费用上账Dto") AccountDto account) {
    try {
      Page<AccountVo> page = this.AccountVoService.findAuditByDto(pageable, account);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  /**
   * 启用
   *
   * @param idList
   * @return 所有数据
   */
  @ApiOperation(value = "启用")
  @PatchMapping("enable")
  public Result<?> enable(@RequestParam List<String> idList) {
    try {
       this.AccountVoService.enable(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 禁用
   *
   * @param idList
   * @return 所有数据
   */
  @ApiOperation(value = "禁用")
  @PatchMapping("disable")
  public Result<?> disable(@RequestParam List<String> idList) {
    try {
      this.AccountVoService.disable(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
