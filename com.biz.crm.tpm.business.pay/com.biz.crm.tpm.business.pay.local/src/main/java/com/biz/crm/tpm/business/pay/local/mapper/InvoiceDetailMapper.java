package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.InvoiceDetail;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailPageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票使用明细(InvoiceDetail)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
public interface InvoiceDetailMapper extends BaseMapper<InvoiceDetail> {

    Page<InvoiceDetailPageVo> findInvoiceDetailList(Page<InvoiceDetailPageVo> page, @Param("vo")InvoiceDetailPageVo vo,@Param("tenantCode")String tenantCode);

    /**
     * 根据发票号+行号查询
     * @param uniqueKeyList
     * @return
     */
    List<InvoiceDetail> findByUniqueKeyList(@Param("uniqueKeyList") List<String> uniqueKeyList);

    /**
     * 根据发票号+行号更新关联状态
     * @param uniqueKeyList
     * @return
     */
    void updateUsedByUniqueKeyList(@Param("uniqueKeyList") List<String> uniqueKeyList);


    InvoiceDetailPageVo findByInvoiceNoAndDetailNo(@Param("invoiceNo") String invoiceNo, @Param("detailNo") String detailNo);
}

