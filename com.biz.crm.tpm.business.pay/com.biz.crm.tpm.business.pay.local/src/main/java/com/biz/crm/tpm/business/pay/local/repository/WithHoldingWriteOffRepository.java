package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff;
import com.biz.crm.tpm.business.pay.local.mapper.WithHoldingWriteOffMapper;
import com.biz.crm.tpm.business.pay.sdk.enums.HecSendStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingWriteOffVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 费用冲销(WithHoldingWriteOff)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-15 15:30:18
 */
@Component
public class WithHoldingWriteOffRepository extends ServiceImpl<WithHoldingWriteOffMapper, WithHoldingWriteOff> {

    @Autowired
    private WithHoldingWriteOffMapper withHoldingWriteOffMapper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 按编码查询
     *
     * @param codes
     * @return
     */
    public List<WithHoldingWriteOffVo> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }
        List<WithHoldingWriteOff> list = this.lambdaQuery()
                .in(WithHoldingWriteOff::getWithHoldingCode, codes)
                .eq(WithHoldingWriteOff::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHoldingWriteOff::getTenantCode, TenantUtils.getTenantCode())
                .list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, WithHoldingWriteOff.class, WithHoldingWriteOffVo.class, LinkedHashSet.class, ArrayList.class));
    }

    public WithHoldingWriteOff findByBusinessCode(String businessCode) {
        if (StringUtils.isBlank(businessCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(WithHoldingWriteOff::getBusinessCode, businessCode)
                .eq(WithHoldingWriteOff::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHoldingWriteOff::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }

    public void hecVoucherCallback(List<HecCallbackDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        this.baseMapper.hecVoucherCallback(dtoList);
    }

    public void updateHecInfo(String writeOffCode, String hecReceiptNumber, String hecReceiptUrl) {
        if (StringUtil.isEmpty(writeOffCode)) {
            return;
        }
        this.lambdaUpdate()
                .eq(WithHoldingWriteOff::getWriteOffCode, writeOffCode)
                .set(WithHoldingWriteOff::getPushStatus, BooleanEnum.TRUE.getCapital())
                .set(WithHoldingWriteOff::getExternalCode, hecReceiptNumber)
                .set(WithHoldingWriteOff::getHecReceiptUrl, hecReceiptUrl)
                .set(WithHoldingWriteOff::getProcessDate, new Date())
                .set(WithHoldingWriteOff::getFailMsg, "")
                .update();
    }

    public void updateErrorMsg(String writeOffCode, String failMsg) {
        if (StringUtil.isEmpty(writeOffCode)) {
            return;
        }
        if (StringUtil.isNotEmpty(failMsg)
                && failMsg.length() > 500) {
            failMsg = failMsg.substring(0, 499);
        }
        this.lambdaUpdate()
                .eq(WithHoldingWriteOff::getWriteOffCode, writeOffCode)
                .set(WithHoldingWriteOff::getFailMsg, failMsg)
                .update();
    }

    public void updatePushStatusByCodes(List<String> writeOffCodes, HecSendStatusEnum sendStatusEnum) {
        if (CollectionUtil.isEmpty(writeOffCodes)
                || Objects.isNull(sendStatusEnum)) {
            return;
        }
        this.lambdaUpdate()
                .in(WithHoldingWriteOff::getWriteOffCode, writeOffCodes)
                .set(WithHoldingWriteOff::getPushStatus, sendStatusEnum.getCode())
                .update();
    }

    public List<WithHoldingWriteOff> findByIds(List<String> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(WithHoldingWriteOff::getId, idList)
                .eq(WithHoldingWriteOff::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHoldingWriteOff::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    public List<WithHoldingWriteOff> findByUniqueKeys(List<String> uniqueKeys) {
        return this.baseMapper.findByUniqueKeys(uniqueKeys);
    }

    public List<WithHoldingWriteOffVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        List<WithHoldingWriteOff> withHoldingWriteOffs = this.lambdaQuery()
                .in(WithHoldingWriteOff::getActivitiesDetailCode, schemeDetailCodes)
                .eq(WithHoldingWriteOff::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(WithHoldingWriteOff::getWriteOffType, Lists.newArrayList(WriteOffTypeEnum.CLOSE.getDictCode(), WriteOffTypeEnum.AUDIT.getDictCode()))
                .list();
        if (CollectionUtil.isEmpty(withHoldingWriteOffs)) {
            return Lists.newArrayList();
        }
        return (List<WithHoldingWriteOffVo>) nebulaToolkitService.copyCollectionByWhiteList(withHoldingWriteOffs, WithHoldingWriteOff.class, WithHoldingWriteOffVo.class, HashSet.class, ArrayList.class);
    }


    public List<WithHoldingWriteOffVo> findListBySourceCodes(List<String> sourceCodes) {
        List<WithHoldingWriteOff> dataList = this.lambdaQuery()
                .in(WithHoldingWriteOff::getSourceCode,sourceCodes)
                .list();
        if (CollectionUtil.isEmpty(dataList)){
            return Lists.newArrayList();
        }
        return (List<WithHoldingWriteOffVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList,WithHoldingWriteOff.class,WithHoldingWriteOffVo.class,HashSet.class,ArrayList.class);
    }

    public List<WithHoldingWriteOff> findCashAndOrderWriteOff(List<String> businessCodes, List<String> monthList) {
        return this.baseMapper.findCashAndOrderWriteOff(businessCodes, monthList);
    }

    public List<WithHoldingWriteOff> findCloseWriteOff(List<String> businessCodes, List<String> monthList) {
        return this.baseMapper.findCloseWriteOff(businessCodes, monthList);
    }

    public List<WithHoldingWriteOff> findNotWriteOffByActivitiesDetailCode(String activitiesDetailCode) {
        return this.lambdaQuery()
                .eq(WithHoldingWriteOff::getActivitiesDetailCode, activitiesDetailCode)
                .eq(WithHoldingWriteOff::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHoldingWriteOff::getTenantCode, TenantUtils.getTenantCode())
                .eq(WithHoldingWriteOff::getManageReportWriteOffStatus, WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode())
                .list();
    }



    public List<WithHoldingWriteOffVo> findBySchemeDetailCodesAndWriteOffTypes(List<String> schemeDetailCodes, List<String> writeOffTypes) {
        List<WithHoldingWriteOff> withHoldingWriteOffs = this.lambdaQuery()
                .in(WithHoldingWriteOff::getActivitiesDetailCode, schemeDetailCodes)
                .eq(WithHoldingWriteOff::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(WithHoldingWriteOff::getWriteOffType, writeOffTypes)
                .list();
        if (CollectionUtil.isEmpty(withHoldingWriteOffs)) {
            return Lists.newArrayList();
        }
        return (List<WithHoldingWriteOffVo>) nebulaToolkitService.copyCollectionByWhiteList(withHoldingWriteOffs, WithHoldingWriteOff.class, WithHoldingWriteOffVo.class, HashSet.class, ArrayList.class);
    }
}

