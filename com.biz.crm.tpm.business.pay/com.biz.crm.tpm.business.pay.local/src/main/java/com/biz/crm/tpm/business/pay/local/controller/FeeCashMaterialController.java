package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashMaterialDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashMaterialService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashMaterialVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "费用兑付-物料采购功能接口")
@RestController
@RequestMapping("/v1/pay/feeCashMaterial")
@Slf4j
public class FeeCashMaterialController extends BusinessPageCacheController<FeeCashMaterialVo, FeeCashMaterialDto> {

    @Autowired(required = false)
    private FeeCashMaterialService feeCashMaterialService;

    @ApiOperation(value = "缓存明细导出")
    @GetMapping("findCachePageListImport")
    public Result<Page<FeeCashMaterialVo>> findCachePageListImport(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                        @ApiParam(name = "dto", value = "缓存键") FeeCashMaterialDto dto) {
        try {
            return this.findCachePageList(pageable,dto.getCacheKey(), dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
