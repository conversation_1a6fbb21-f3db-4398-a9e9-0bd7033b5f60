package com.biz.crm.tpm.business.pay.local.service.internal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.eunm.ExternalSystemEnum;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.base.util.ryytn.RySignHeaderUtil;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.common.ie.sdk.excel.util.EuropaParamsTools;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.tpm.business.pay.local.entity.CreditOrder;
import com.biz.crm.tpm.business.pay.local.repository.CreditOrderRepository;
import com.biz.crm.tpm.business.pay.local.repository.CreditOrderTicketRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.CreditOrderTicketDto;
import com.biz.crm.tpm.business.pay.sdk.enums.TicketStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.service.CreditOrderService;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderSapVo;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderTicketSapVo;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderTicketVo;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderVo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CreditOrderServiceImpl implements CreditOrderService {

    @Autowired(required = false)
    private CreditOrderRepository creditOrderRepository;
    @Autowired(required = false)
    private UrlApiService urlApiService;
    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;
    @Autowired(required = false)
    private CreditOrderTicketRepository creditOrderTicketRepository;

    /**
     * 推送sap
     *
     * @param ids
     */
    @Override
    public void pushSap(List<String> ids, List<String> codes) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids) || !CollectionUtils.isEmpty(codes), "id、编码不能都为空");
        List<CreditOrder> voList;
        if (CollectionUtils.isNotEmpty(ids)) {
            voList = creditOrderRepository.listByIds(ids);
        } else {
            voList = creditOrderRepository.findByCodes(codes);
        }
        Validate.notEmpty(voList, "数据不存在");
        voList.forEach(e -> Validate.isTrue(!DmsWarehouseOrderEnum.SAP_PUSH_STATUS.SUCCESS.getCode().equals(e.getPushStatus()), "已推送SAP"));
        Map<String, List<CreditOrderTicketVo>> ticketMap = creditOrderTicketRepository.findTicketByCreditCodes(voList.stream().map(e -> e.getCreditCode()).collect(Collectors.toList())).stream()
                .collect(Collectors.groupingBy(e -> e.getCreditCode()));

        log.info("=====>   贷项订单推送SAP start    <=====");
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_SAP_ACCOUNT);
        StringBuffer error = new StringBuffer();

        voList.forEach(e -> pushSapSingle(e, error, urlAddressVo, ticketMap));

        creditOrderRepository.saveOrUpdateBatch(voList);
    }

    /**
     * 推送sap定时任务
     */
    @Override
    public void pushSapTask() {
        List<CreditOrder> creditOrders = creditOrderRepository.findByPushStatus();
        if (CollectionUtils.isEmpty(creditOrders)) {
            return;
        }
        pushSap(null, creditOrders.stream().map(e -> e.getCreditCode()).collect(Collectors.toList()));
    }

    /**
     * 按编码查询票扣详情
     *
     * @param code
     * @return
     */
    @Override
    public List<CreditOrderTicketVo> findByCode(String code) {
        List<CreditOrderTicketVo> list = creditOrderTicketRepository.findTicketByCreditCodes(Arrays.asList(code));
        list.sort(Comparator.comparing(CreditOrderTicketVo::getLineNumber));
        return list;
    }

    /**
     * 按条件查询票扣明细
     *
     * @param dtoList
     * @return
     */
    @Override
    public List<CreditOrderTicketVo> findByDtoList(List<CreditOrderTicketVo> dtoList) {
        return creditOrderTicketRepository.findByDtoList(dtoList);
    }

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    @Override
    public Page<CreditOrderTicketVo> findByConditions(Pageable pageable, CreditOrderTicketDto dto) {
        ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new CreditOrderTicketDto();
        }
        Page<CreditOrderTicketVo> page = this.creditOrderTicketRepository.findByConditions(pageable, dto);
        return page;
    }

    /**
     * 贷项订单合计
     *
     * @param params
     * @return
     */
    @Override
    public CreditOrderVo findTotalByConditions(LinkedHashMap<String, Object> params) {
        CreditOrderVo dto = new CreditOrderVo();
        if (!MapUtils.isEmpty(params)) {
            Map<String, Object> convertEuropaParam = EuropaParamsTools.convertEuropaParam(params);
            dto = JSON.parseObject(JSON.toJSONString(convertEuropaParam), CreditOrderVo.class);
            List<String> accountDate = params.keySet().stream().filter(e -> e.startsWith("account_date")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(accountDate) && accountDate.size() == 2) {
                dto.setAccountDate(null);
                dto.setAccountDateStart((String) params.get(accountDate.get(0)));
                dto.setAccountDateEnd((String) params.get(accountDate.get(1)));
            }
        }
        return creditOrderRepository.findTotalByConditions(dto);
    }

    /**
     * SAP回传贷项订单付款状态
     *
     * @param dtoList
     */
    @Override
    public void updateCreditOrderTicketStatus(List<CreditOrderSapVo> dtoList) {
        Validate.notEmpty(dtoList, "参数不能为空");
        List<CreditOrderTicketVo> list = new ArrayList<>();
        dtoList.forEach(e -> {
            Validate.notBlank(e.getCREDITCODE(), "贷项订单编码，不能为空");
            Validate.notBlank(e.getVOUCHERCODE(), "凭证号，不能为空");
            Validate.notEmpty(e.getDETAILS(), "票扣明细不能为空");
//            List<CreditOrderTicketSapVo> details = JSONArray.parseArray(JSONArray.toJSONString(e.getJSONArray("DETAILS")), CreditOrderTicketSapVo.class);
            e.getDETAILS().forEach(m -> {
                Validate.notNull(m.getLINENUMBER(), "票扣明细行号，不能为空");
                Validate.notNull(m.getAMOUNT(), "未开票金额，不能为空");
                Validate.notBlank(m.getTICKETSTATUS(), "单据付款状态，不能为空");
                Validate.isTrue(Arrays.asList(TicketStatusEnum.INVOICED.getCode(), TicketStatusEnum.UN_INVOICED.getCode(), TicketStatusEnum.PART_INVOICED.getCode())
                        .contains(m.getTICKETSTATUS()), "单据付款状态不正确，请确认");
                CreditOrderTicketVo ticketDto = new CreditOrderTicketVo() {{
                    this.setCreditCode(e.getCREDITCODE());
                    this.setLineNumber(m.getLINENUMBER());
                    this.setTicketStatus(m.getTICKETSTATUS());
                    this.setUnTicketAmount(m.getAMOUNT());
                }};
                list.add(ticketDto);
            });
        });

        creditOrderTicketRepository.updateCreditOrderTicketStatus(list);
    }

    private void pushSapSingle(CreditOrder vo, StringBuffer error, UrlAddressVo urlAddressVo, Map<String, List<CreditOrderTicketVo>> ticketMap) {
        try {
            String bodyJson = this.buildBodyJson(vo, ticketMap);

            String url = urlAddressVo.getUrl();
            String interfaceAddress = String.format(RyConstant.SAP_INTERFACE_ADDRESS, urlAddressVo.getBusinessKey(), urlAddressVo.getEnvironment());
            ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(bodyJson, urlAddressVo);
            Map<String, String> headMap = RySignHeaderUtil.getSignHeadMap(urlAddressVo.getAccessId(),
                    urlAddressVo.getSecretKey(), interfaceAddress);
            logDetailDto.setReqHead(JSON.toJSONString(headMap));
            logDetailDto.setMethod(FeeCashConstant.PUSH_SAP_INTERFACE);
            logDetailDto.setRequestUri(interfaceAddress);
            logDetailDto.setMethodMsg("贷项订单推送SAP");
            externalLogVoService.addOrUpdateLog(logDetailDto, true);

            Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, bodyJson, headMap);

            ExternalLogUtil.buildLogResult(logDetailDto, result);
            logDetailDto.setStatus(ExternalLogGlobalConstants.E);

            JSONObject jsonObject = JSON.parseObject(result.getResult());
            JSONObject head = jsonObject.getJSONObject(RyConstant.SAP_HEAD_DATA);

            if (Objects.isNull(head)) {
                error.append("sap返回头部结构为空");
                vo.setPushStatus(DmsWarehouseOrderEnum.SAP_PUSH_STATUS.ERROR.getCode());
                vo.setFailMsg("sap返回头部结构为空");
            } else {
                JSONArray jsonArray = jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA);
                AtomicInteger index = new AtomicInteger(0);
                if (StringUtils.equals(ExternalLogGlobalConstants.S, head.getString(RyConstant.SAP_HEAD_KEY))) {
                    logDetailDto.setStatus(ExternalLogGlobalConstants.S);
                    if (CollectionUtils.isEmpty(jsonArray)) {
                        error.append("sap返回明细为空");
                        vo.setPushStatus(DmsWarehouseOrderEnum.SAP_PUSH_STATUS.ERROR.getCode());
                        vo.setFailMsg("sap返回明细为空");
                    } else {
                        JSONObject resultItem = jsonArray.getJSONObject(index.getAndIncrement());
                        vo.setPushStatus(DmsWarehouseOrderEnum.SAP_PUSH_STATUS.SUCCESS.getCode());
                        vo.setFailMsg(DmsWarehouseOrderEnum.SAP_PUSH_STATUS.SUCCESS.getDesc());
                        vo.setVoucherCode(resultItem.getString("VBELN"));
                        vo.setAccountDate(DateUtil.dateStrNowYYYYMMDD());
                    }

                } else {
                    error.append("sap推送错误：" + head.getString(RyConstant.SAP_HEAD_MSG));
                    if (CollectionUtils.isNotEmpty(jsonArray)) {
                        JSONObject resultItem = jsonArray.getJSONObject(index.getAndIncrement());
                        if (Objects.nonNull(resultItem)) {
                            vo.setPushStatus(resultItem.getString(RyConstant.SAP_HEAD_KEY));
                            vo.setFailMsg(resultItem.getString(RyConstant.SAP_HEAD_MSG));
                            vo.setVoucherCode(resultItem.getString("VBELN"));
                        } else {
                            vo.setPushStatus(DmsWarehouseOrderEnum.SAP_PUSH_STATUS.ERROR.getCode());
                            vo.setFailMsg("sap返回明细为空");
                        }
                    } else {
                        vo.setPushStatus(DmsWarehouseOrderEnum.SAP_PUSH_STATUS.ERROR.getCode());
                        vo.setFailMsg(head.getString(RyConstant.SAP_HEAD_MSG));
                    }
                }

            }
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
        } catch (Exception e) {
            log.error("推送sap失败", e);
            vo.setPushStatus(DmsWarehouseOrderEnum.SAP_PUSH_STATUS.ERROR.getCode());
            vo.setFailMsg(e.getMessage());
            error.append(e.getMessage());
        }
    }

    private String buildBodyJson(CreditOrder vo, Map<String, List<CreditOrderTicketVo>> ticketMap) {
        JSONObject ctrl = new JSONObject();
        ctrl.put("SYSID", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("REVID", ExternalSystemEnum.SAP.getCode());
        ctrl.put("FUNID", FeeCashConstant.PUSH_SAP_INTERFACE);
        ctrl.put("INFID", UuidCrmUtil.general());
        ctrl.put("UNAME", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("DATUM", DateUtil.dateStrNowYYYYMMDD());
        ctrl.put("UZEIT", DateUtil.dateStrNowHms());
        ctrl.put("KEYID", vo.getCreditCode());

        JSONArray dataArray = new JSONArray();

        JSONObject data = new JSONObject();

        //AUART	订单类型
        //VKORG	销售主体
        //VTWEG	分销渠道
        //SPART	产品组
        //VKBUR	销售部门
        //VKGRP	销售组
        //BSTKD	客户参考
        //AUDAT	凭证日期
        //KUNAG	售达方
        //KUNWE	送达方
        //PRSDT	定价日期
        //VDATU	要求的交货日期
        String dateStr = DateUtil.format(vo.getCreateTime(), "yyyy-MM-dd");
        data.put("AUART", vo.getOrderType());
        data.put("VKORG", vo.getCompanyCode());
        data.put("VTWEG", "20");
        data.put("SPART", "00");
        data.put("VKBUR", vo.getDepartmentOneCode());
        data.put("VKGRP", vo.getDepartmentTwoCode());
        data.put("BSTKD", vo.getCreditCode());
        data.put("AUDAT", dateStr);
        data.put("KUNAG", vo.getErpCode());
        data.put("KUNWE", vo.getErpCode());
        data.put("PRSDT", dateStr);
        data.put("VDATU", dateStr);

        JSONArray itemArray = new JSONArray();
        ticketMap.get(vo.getCreditCode()).forEach(m -> {
            JSONObject item = new JSONObject();

            //POSNR	行项目号
            //MATNR	物料编码
            //KWMENG 数量
            //VRKME	销售单位
            //KSCHL	条件类型
            //KBETR	金额
            item.put("POSNR", m.getLineNumber());
            item.put("MATNR", m.getProductCode());
            item.put("KWMENG", m.getQuantity());
            item.put("VRKME", m.getSaleUnit());
            item.put("KSCHL", "ZP02");
            item.put("KBETR", m.getAmount());

            itemArray.add(item);
        });
        data.put("ITEM", itemArray);
        dataArray.add(data);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(RyConstant.SAP_HEAD_DATA, ctrl);
        jsonObject.put(RyConstant.SAP_DETAIL_DATA, dataArray);

        return jsonObject.toJSONString();
    }
}
