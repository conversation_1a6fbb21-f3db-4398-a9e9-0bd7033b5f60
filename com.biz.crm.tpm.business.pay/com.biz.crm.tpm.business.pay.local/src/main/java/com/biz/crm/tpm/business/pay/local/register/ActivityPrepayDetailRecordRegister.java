package com.biz.crm.tpm.business.pay.local.register;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * 活动预付明细跟踪数据视图
 */
@Component
public class ActivityPrepayDetailRecordRegister implements DataviewRegister {

    /**
     * 全系统唯一的数据视图业务编号（例如orderList）</br>
     * 如果不唯一，系统将会报错
     */
    @Override
    public String code() {
        return "tpm_activity_prepay_detail_record_data_view";
    }

    /**
     * 注册的数据视图描述信息（例如：订单业务主列表视图）
     */
    @Override
    public String desc() {
        return "活动预付明细跟踪数据视图";
    }

    /**
     * 这个数据视图所使用的SQL语句，注意这个SQL语句不需要包括任何分页查询的关键字、不需要包括和数据权限有关的任何查询条件，
     * 也不要包括和任何特定数据库有关的关键字。</p>
     * 但是可以加入和必传参数有关的参数绑定位置，例如：</p>
     * <code>
     * select * from user where user.name = :name
     * </code>
     * <p>
     * 这样的话，数据视图的正式执行就必须传入参数名为name的参数信息</p>
     */
    @Override
    public String buildSql() {
        return  "    SELECT " +
                "   t.id,   " +
                "	t.create_account, " +
                "	t.create_name, " +
                "	t.create_time, " +
                "	t.modify_account, " +
                "	t.modify_name, " +
                "	t.modify_time, " +
                "	t.del_flag, " +
                "	t.enable_status, " +
                "	t.remark, " +
                "	t.tenant_code, " +
                "case when t.prepay_type != 'activity' THEN  " +
                "ifnull(b.this_cash_amount,0)-ifnull(d.this_reversed_amount,0) " +
                "else " +
                "ifnull(c.this_pay_amount,0)-ifnull(d.this_reversed_amount,0) " +
                "end available_reversed_amount," +
                "	t.category_code, " +
                "	t.category_name, " +
                "	t.company_code, " +
                "	t.detail_code, " +
                "	t.detail_name, " +
                "	t.estimated_cost, " +
                "	t.payee_name, " +
                "	CASE WHEN t.prepay_type != 'activity' THEN " +
                "		b.this_cash_amount " +
                "	ELSE " +
                "		c.this_pay_amount " +
                "	END prepay_amount, " +
                "	t.prepay_code, " +
                "	t.prepay_detail_code, " +
                "	t.prepay_name, " +
                "	d.this_reversed_amount reversed_amount, " +
                "	t.scheme_code, " +
                "	t.scheme_detail_code, " +
                "	t.scheme_name, " +
                "	t.years, " +
                "	t.payee_code, " +
                "	t.apply_amount, " +
                "	t.audit_code, " +
                "	t.audit_detail_code, " +
                "	t.description, " +
                "	t.line_code, " +
                "	t.carried_amount, " +
                "	t.prepare_carry_amount, " +
                "	t.prepay_type, " +
                "	t.relate_carry_amount, " +
                "	CASE WHEN t.prepay_type != 'activity' THEN " +
                "		b.this_cash_amount " +
                "	ELSE " +
                "		c.this_prepay_amount " +
                "	END prepay_apply_amount, " +
                "	t.pay_status, " +
                "	t.cost_center_code, " +
                "	t.cost_center_name, " +
                "	t.item_code, " +
                "	t.item_name, " +
                "	t.pay_sucess_date " +
                "FROM " +
                "	tpm_activity_prepay_detail_record t " +
                "	LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code " +
                "		AND a.status = '3' " +
                "	LEFT JOIN tpm_activity_prepay_detail c ON t.prepay_detail_code = c.prepay_detail_code " +
                "	LEFT JOIN ( " +
                "		SELECT " +
                "			b.cash_detail_code, " +
                "			b.this_cash_amount " +
                "		FROM " +
                "			tpm_fee_cash a " +
                "			LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code " +
                "		WHERE " +
                "			a.status = '3' " +
                "			AND cash_type in('wire', 'account')) b ON t.prepay_detail_code = b.cash_detail_code " +
                "	LEFT JOIN ( " +
                "		SELECT " +
                "			a.prepay_detail_code, sum(IFNULL(a.this_reversed_amount, 0)) this_reversed_amount " +
                "		FROM " +
                "			tpm_fee_cash_prepay a left join tpm_fee_cash b on a.cash_code = b.cash_code " +
                "       where b.del_flag = '009'    " +
                "		GROUP BY " +
                "			a.prepay_detail_code) d ON t.prepay_detail_code = d.prepay_detail_code" +
                "    WHERE t.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' " +
                "    AND t.tenant_code = :tenantCode ";
    }
}
