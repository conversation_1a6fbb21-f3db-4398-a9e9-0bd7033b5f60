package com.biz.crm.tpm.business.pay.local.controller;

import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.local.service.scheduled.WithHoldingWriteOffScheduledComponent;
import com.biz.crm.tpm.business.pay.sdk.constant.WithHoldingWriteOffConstant;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffSendHecService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/v1/pay/withHoldingWriteOff")
@Slf4j
@Api(tags = "费用冲销")
public class WithHoldingWriteOffController {

    @Autowired(required = false)
    private WithHoldingWriteOffService withHoldingWriteOffService;

    @Autowired(required = false)
    private WithHoldingWriteOffSendHecService withHoldingWriteOffSendHecService;
    @Autowired(required = false)
    private WithHoldingWriteOffScheduledComponent scheduledComponent;
    @Autowired(required = false)
    private RedisLockService redisLockService;

    @ApiOperation(value = "手动冲销")
    @GetMapping("handleManual")
    public Result<?> handleManual(@ApiParam(name = "codes", value = "计提编码集合") @RequestParam List<String> codes) {
        try {
            this.withHoldingWriteOffService.handleManual(codes);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 推送
     *
     * @param idList 主键结合
     * @return
     */
    @ApiOperation(value = "推送")
    @PatchMapping("push")
    public Result<?> push(@ApiParam(name = "idList", value = "主键集合") @RequestParam List<String> idList) {
        Validate.isTrue(this.redisLockService.batchLock(WithHoldingWriteOffConstant.LOCK_PREFIX, idList,
                TimeUnit.MINUTES,
                30), "预提数据正在被操作，请稍后再试！");
        try {
            return this.withHoldingWriteOffSendHecService.push(idList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            this.redisLockService.batchUnLock(WithHoldingWriteOffConstant.LOCK_PREFIX, idList);
        }
    }

    @ApiOperation(value = "手动冲销-定时任务")
    @GetMapping("handleManualScheduled")
    public Result<?> handleManualScheduled() {
        this.scheduledComponent.handleManual();
        return Result.ok();
    }
}
