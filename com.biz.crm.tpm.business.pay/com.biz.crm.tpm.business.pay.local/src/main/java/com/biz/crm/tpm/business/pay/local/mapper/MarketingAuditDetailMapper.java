package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAuditDetail;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 方案结案明细(MarketingAuditDetail)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 16:58:19
 */
public interface MarketingAuditDetailMapper extends BaseMapper<MarketingAuditDetail> {
    
    Page<MarketingAuditDetailVo> findByConditions(@Param("page") Page<MarketingAuditDetailVo> page , @Param("dto") MarketingAuditDetailDto dto);

    List<MarketingAuditDetailVo> findByDtoList(@Param("dtoList") List<MarketingAuditDetailDto> dtoList);

    List<MarketingAuditDetail> findForReplenishmentPool();

    List<MarketingAuditDetail> findBySchemeDetailCodesBeCommit(@Param("codes") List<String> codes);

    List<MarketingAuditDetailVo> findBySchemeDetailCodesAll(@Param("codes") List<String> codes);

    void updateCashAmount(@Param("dtoList") List<MarketingAuditDetailDto> list);

    List<MarketingAuditDetailVo> findAuditAmountBySchemeDetailCodes(@Param("schemeDetailCodes")List<String> schemeDetailCodes);

    List<MarketingAuditDetailVo> findListBySchemeDetailCodes(@Param("schemeDetailCodes")List<String> schemeDetailCodes);

    List<MarketingAuditDetailVo> findListBySchemeDetailCodesApproved(@Param("schemeDetailCodes")List<String> schemeDetailCodes);

    List<MarketingAuditDetailVo> scheduleNotClosureDetailMsgPush();

    List<MarketingAuditDetailVo> scheduleNotReplenishmentMsgPush();

    List<MarketingAuditDetailVo> findCommitAndPassBySchemeDetailCodes(@Param("codes") List<String> codes);
}

