package com.biz.crm.tpm.business.pay.local.service.register;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;

/**
 * 进货返利冲销数据视图注册器
 */
@Component
@Slf4j
public class BackWithHoldingWriteOffDataViewRegister implements DataviewRegister {

    @Override
    public String code() {
        return "back_with_holding_write_off_data_view";
    }

    @Override
    public String desc() {
        return "进货返利冲销视图";
    }

    /**
     * 这个数据视图所使用的SQL语句，注意这个SQL语句不需要包括任何分页查询的关键字、不需要包括和数据权限有关的任何查询条件，
     * 也不要包括和任何特定数据库有关的关键字。</p>
     * 但是可以加入和必传参数有关的参数绑定位置，例如：</p>
     * <code>
     * select * from user where user.name = :name
     * </code>
     * <p>
     * 这样的话，数据视图的正式执行就必须传入参数名为name的参数信息</p>
     */
    @Override
    public String buildSql() {
        return "select t.*, m.case_type from tpm_back_with_holding_write_off t " +
                "left join tpm_marketing_plan_case m on t.activities_detail_code = m.scheme_detail_code " +
                "where t.tenant_code = :tenantCode " +
                "and t.del_flag = '009' ";
    }
}
