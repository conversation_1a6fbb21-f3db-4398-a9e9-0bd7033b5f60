package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 实体：方案结案明细;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "MarketingAuditDetail", description = "方案结案明细")
@TableName("tpm_marketing_audit_detail")
@Getter
@Setter
@Entity(name = "tpm_marketing_audit_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_audit_detail", comment = "方案结案明细")
@Table(name = "tpm_marketing_audit_detail", indexes = {
        @Index(name = "audit_idx1", columnList = "audit_code"),
        @Index(name = "audit_idx2", columnList = "scheme_detail_code"),
})
public class MarketingAuditDetail extends TenantFlagOpEntity {

  /**
   * 核销申请名称
   */
  @ApiModelProperty("核销申请名称")
  @Column(name = "audit_name", columnDefinition = "VARCHAR(255) COMMENT '核销申请名称 '")
  private String auditName;

  /**
   * 核销申请编号
   */
  @ApiModelProperty("核销申请编号")
  @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
  private String auditCode;

  /**
   * 核销明细编号
   */
  @ApiModelProperty("核销明细编号")
  @Column(name = "audit_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销明细编号 '")
  private String auditDetailCode;

  @ApiModelProperty("方案编码")
  @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
  private String schemeCode;

  @ApiModelProperty("方案名称")
  @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称'")
  private String schemeName;

  @ApiModelProperty("方案明细编码")
  @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
  private String schemeDetailCode;

  @ApiModelProperty("活动执行编码")
  @Column(name = "act_execute_code", columnDefinition = "varchar(32) comment '活动执行编码'")
  private String actExecuteCode;

  @ApiModelProperty("活动名称")
  @Column(name = "act_name", columnDefinition = "varchar(128) comment '活动名称'")
  private String actName;

  @ApiModelProperty("活动大类编码")
  @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
  private String categoryCode;

  @ApiModelProperty("活动大类名称")
  @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
  private String categoryName;

  @ApiModelProperty("活动细类编码")
  @Column(name = "detail_code", columnDefinition = "varchar(32) comment '活动细类编码'")
  private String detailCode;

  @ApiModelProperty("活动细类名称")
  @Column(name = "detail_name", columnDefinition = "varchar(64) comment '活动细类名称'")
  private String detailName;

  @ApiModelProperty("开始时间")
  @Column(name = "start_date", columnDefinition = "varchar(32) comment '开始时间'")
  private String startDate;

  @ApiModelProperty("结束时间")
  @Column(name = "end_date", columnDefinition = "varchar(32) comment '结束时间'")
  private String endDate;

  @ApiModelProperty("年月")
  @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
  private String years;

  @ApiModelProperty("归属部门编码")
  @Column(name = "belong_department_code", columnDefinition = "varchar(32) comment '归属部门编码'")
  private String belongDepartmentCode;

  @ApiModelProperty("归属部门名称")
  @Column(name = "belong_department_name", columnDefinition = "varchar(64) comment '归属部门名称'")
  private String belongDepartmentName;

  @ApiModelProperty("承担部门编码")
  @Column(name = "bear_department_code", columnDefinition = "varchar(32) comment '承担部门编码'")
  private String bearDepartmentCode;

  @ApiModelProperty("承担部门名称")
  @Column(name = "bear_department_name", columnDefinition = "varchar(64) comment '承担部门名称'")
  private String bearDepartmentName;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "varchar(64) comment '成本中心名称'")
  private String costCenterName;

  @ApiModelProperty("客户编码")
  @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
  private String customerCode;

  @ApiModelProperty("客户名称")
  @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
  private String customerName;

  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
  private String erpCode;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
  private String channelCode;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("组织编码")
  @Column(name = "org_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编码'")
  private String orgCode;

  @ApiModelProperty("组织名称")
  @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
  private String orgName;

  @ApiModelProperty("终端编码")
  @Column(name = "terminal_code", columnDefinition = "varchar(32) comment '终端编码'")
  private String terminalCode;

  @ApiModelProperty("终端名称")
  @Column(name = "terminal_name", columnDefinition = "varchar(64) comment '终端名称'")
  private String terminalName;

  @ApiModelProperty("终端类型")
  @Column(name = "terminal_type", columnDefinition = "varchar(20) comment '终端类型'")
  private String terminalType;

  @ApiModelProperty("兑付方式")
  @Column(name = "cash_type", columnDefinition = "varchar(20) comment '兑付方式'")
  private String cashType;

  @ApiModelProperty("执行描述")
  @Column(name = "execute_desc", columnDefinition = "varchar(500) comment '执行描述'")
  private String executeDesc;

  @ApiModelProperty("预估费用")
  @Column(name = "estimated_cost", columnDefinition = "decimal(18,4) comment '预估费用'")
  private BigDecimal estimatedCost;

  @ApiModelProperty("申请金额")
  @Column(name = "apply_amount", columnDefinition = "decimal(18,4) comment '申请金额'")
  private BigDecimal applyAmount;

  @ApiModelProperty("预估费率")
  @Column(name = "ratio", columnDefinition = "decimal(10,2) comment '预估费率'")
  private BigDecimal ratio;

  @ApiModelProperty("结案费率")
  @Column(name = "audit_ratio", columnDefinition = "decimal(10,6) comment '结案费率'")
  private BigDecimal auditRatio;

  @ApiModelProperty("费用依据")
  @Column(name = "cost_basis", columnDefinition = "varchar(20) comment '费用依据'")
  private String costBasis;

  @ApiModelProperty("本次核销金额")
  @Column(name = "audit_amount", length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '本次核销金额 '")
  private BigDecimal auditAmount;

  @ApiModelProperty("已核销金额")
  @Column(name = "audited_amount", length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '已核销金额 '")
  private BigDecimal auditedAmount;

  @ApiModelProperty("是否完全核销")
  @Column(name = "be_full_audit", length = 10, columnDefinition = "VARCHAR(10) default 'N' COMMENT '是否完全核销 '")
  private String beFullAudit;

  @ApiModelProperty("是否完全兑付")
  @Column(name = "be_full_cash", length = 10, columnDefinition = "VARCHAR(10) default 'N' COMMENT '是否完全兑付 '")
  private String beFullCash;

  @ApiModelProperty("票据类型")
  @Column(name = "bill_type", columnDefinition = "varchar(64) comment '票据类型'")
  private String billType;

  @ApiModelProperty("产品编码")
  @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
  private String productCode;

  @ApiModelProperty("产品名称")
  @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
  private String productName;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("返利品项名称")
  @Column(name = "fl_item_name_str", columnDefinition = "VARCHAR(255) COMMENT '返利品项名称'")
  private String flItemNameStr;

  @ApiModelProperty("返利产品名称")
  @Column(name = "fl_product_name_str", columnDefinition = "VARCHAR(255) COMMENT '返利产品名称'")
  private String flProductNameStr;

  @ApiModelProperty("是否预付")
  @Column(name = "be_prepay", columnDefinition = "varchar(10) default 'N' comment '是否预付'")
  private String bePrepay;

  @ApiModelProperty("预算科目编码")
  @Column(name = "budget_subject_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算科目编码'")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  @Column(name = "budget_subject_name", columnDefinition = "varchar(255) COMMENT '预算科目名称'")
  private String budgetSubjectName;

  @ApiModelProperty("物料编码")
  @Column(name = "material_code", columnDefinition = "varchar(32) comment '物料编码'")
  private String materialCode;

  @ApiModelProperty("物料名称")
  @Column(name = "material_name", columnDefinition = "varchar(128) comment '物料名称'")
  private String materialName;

  @ApiModelProperty("兑付金额")
  @Column(name = "cash_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '兑付金额 '")
  private BigDecimal cashAmount;

  @ApiModelProperty("活动可结案金额")
  @Column(name = "available_audit_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '活动可结案金额 '")
  private BigDecimal availableAuditAmount;

  @ApiModelProperty("自动兑付编码")
  @Column(name = "auto_cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '自动兑付编码'")
  private String autoCashCode;

  @ApiModelProperty("关联统筹方案编码")
  @Column(name = "release_code", columnDefinition = "varchar(32) comment '关联统筹方案编码'")
  private String releaseCode;

  @ApiModelProperty("关联统筹方案名称")
  @Column(name = "release_name", columnDefinition = "varchar(128) comment '关联统筹方案名称'")
  private String releaseName;

  @ApiModelProperty("上账状态")
  @Column(name = "account_status", length = 64, columnDefinition = "VARCHAR(64) COMMENT '上账状态 '")
  private String accountStatus;

  @ApiModelProperty("错误描述")
  @Column(name = "err_msg", columnDefinition = "varchar(500) comment '错误描述'")
  private String errMsg;

  @ApiModelProperty("是否推送SFA")
  @Column(name = "is_send_sfa", length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否推送SFA '")
  private String isSendSfa;

  @ApiModelProperty("活动描述")
  @Column(name = "act_desc", columnDefinition = "varchar(500) comment '活动描述'")
  private String actDesc;

  @ApiModelProperty("结案完结日期")
  @Column(name = "audit_date", columnDefinition = "varchar(32) comment '结案完结日期'")
  private String auditDate;

  @ApiModelProperty("兑付超时长")
  @Column(name = "over_day", columnDefinition = "varchar(32) comment '兑付超时长'")
  private String overDay;

  @ApiModelProperty("票扣兑付方式")
  @Column(name = "ticket_cash_type", columnDefinition = "VARCHAR(16) COMMENT '票扣兑付方式'")
  private String ticketCashType;

  @ApiModelProperty("合作类型")
  @Column(name = "cooperate_type",columnDefinition = "varchar(20) comment '合作类型'")
  private String cooperateType;

  @ApiModelProperty("合作类型")
  @Column(name = "hzlx",columnDefinition = "varchar(20) comment '合作类型'")
  private String hzlx;

  @ApiModelProperty("客户搜索项")
  @Column(name = "search_name",columnDefinition = "varchar(128) comment '客户搜索项'")
  private String searchName;
}
