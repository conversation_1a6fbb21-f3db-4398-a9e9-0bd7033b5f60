package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditFiles;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditFilesDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditFilesVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 费用核销附件;(tpm_audit_files)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Mapper
public interface AuditFilesMapper extends BaseMapper<AuditFiles>{
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<AuditFilesVo> findByConditions(@Param("page") Page<AuditFilesVo> page , @Param("dto") AuditFilesDto dto);
}