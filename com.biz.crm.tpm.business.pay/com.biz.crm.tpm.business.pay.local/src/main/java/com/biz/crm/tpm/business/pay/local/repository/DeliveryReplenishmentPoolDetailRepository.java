package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolOperationType;
import com.biz.crm.tpm.business.pay.local.entity.DeliveryReplenishmentPoolDetail;
import com.biz.crm.tpm.business.pay.local.mapper.DeliveryReplenishmentPoolDetailMapper;
import com.biz.crm.tpm.business.pay.sdk.enums.DeliveryDetailTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.vo.DeliveryReplenishmentPoolDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 发货费用报表(DeliveryReplenishmentPoolDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-14 16:58:57
 */
@Component
public class DeliveryReplenishmentPoolDetailRepository extends ServiceImpl<DeliveryReplenishmentPoolDetailMapper, DeliveryReplenishmentPoolDetail> {

    @Autowired
    private DeliveryReplenishmentPoolDetailMapper deliveryReplenishmentPoolDetailMapper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 按唯一码查询
     *
     * @param uniqueKeys
     * @return
     */
    public List<DeliveryReplenishmentPoolDetail> findByUniqueKeys(List<String> uniqueKeys) {
        return lambdaQuery().in(DeliveryReplenishmentPoolDetail::getUniqueKey, uniqueKeys).list();
    }

    /**
     * 查询未冲销的发货费用表
     *
     * @return
     */
    public List<DeliveryReplenishmentPoolDetail> findByUnWriteOff(List<String> businessCodes) {
        if (CollectionUtils.isEmpty(businessCodes)) {
            List<DeliveryReplenishmentPoolDetail> replenishmentPoolDetails = Lists.newArrayList();
            List<DeliveryReplenishmentPoolDetail> monthList = lambdaQuery()
                    .eq(DeliveryReplenishmentPoolDetail::getBeWriteOff, BooleanEnum.FALSE.getCapital())
                    .likeRight(DeliveryReplenishmentPoolDetail::getDeliveryTime, DateUtil.format(new Date(), "yyyy-MM"))
                    .list();
            if (CollectionUtils.isNotEmpty(monthList)) {
                replenishmentPoolDetails.addAll(monthList);
            }
            Date lastMonthDate = DateUtil.offsetMonth(new Date(), -1);
            List<DeliveryReplenishmentPoolDetail> lastMonthList = lambdaQuery()
                    .eq(DeliveryReplenishmentPoolDetail::getBeWriteOff, BooleanEnum.FALSE.getCapital())
                    .likeRight(DeliveryReplenishmentPoolDetail::getDeliveryTime, DateUtil.format(lastMonthDate, "yyyy-MM"))
                    .list();
            if (CollectionUtils.isNotEmpty(lastMonthList)) {
                replenishmentPoolDetails.addAll(lastMonthList);
            }
            return replenishmentPoolDetails;
        } else {
            return lambdaQuery().eq(DeliveryReplenishmentPoolDetail::getBeWriteOff, BooleanEnum.FALSE.getCapital())
                    .in(DeliveryReplenishmentPoolDetail::getBusinessCode, businessCodes)
                    .list();
        }
    }

    /**
     * 查询进货返利结案未冲销的发货费用表
     *
     * @return
     */
    public List<DeliveryReplenishmentPoolDetail> findBackUnWriteOff(List<String> businessCodes) {
        if (CollectionUtils.isEmpty(businessCodes)) {
            String curMonth = DateUtil.format(new Date(), "yyyy-MM");
            List<DeliveryReplenishmentPoolDetail> replenishmentPoolDetails = Lists.newArrayList();
            List<DeliveryReplenishmentPoolDetail> curMonthList = deliveryReplenishmentPoolDetailMapper.findBackUnWriteOff(curMonth);
            if (CollectionUtils.isNotEmpty(curMonthList)) {
                replenishmentPoolDetails.addAll(curMonthList);
            }
            Date lastMonthDate = DateUtil.offsetMonth(new Date(), -1);
            String lastMonth = DateUtil.format(lastMonthDate, "yyyy-MM");
            List<DeliveryReplenishmentPoolDetail> lastMonthList = deliveryReplenishmentPoolDetailMapper.findBackUnWriteOff(lastMonth);
            if (CollectionUtils.isNotEmpty(lastMonthList)) {
                replenishmentPoolDetails.addAll(lastMonthList);
            }
            return replenishmentPoolDetails;
        } else {
            return lambdaQuery().eq(DeliveryReplenishmentPoolDetail::getBeWriteOff, BooleanEnum.FALSE.getCapital())
                    .in(DeliveryReplenishmentPoolDetail::getBusinessCode, businessCodes)
                    .list();
        }
    }

    /**
     * 根据第二层费用池明细编码查询对应的第三层
     *
     * @param parentCodes
     * @param yearMonthLy
     * @return
     */
    public List<DeliveryReplenishmentPoolDetail> findByParentCodes(List<String> parentCodes, String yearMonthLy) {
        if (CollectionUtils.isEmpty(parentCodes) || StringUtils.isBlank(yearMonthLy)) {
            return new ArrayList<>();
        }
        return lambdaQuery().in(DeliveryReplenishmentPoolDetail::getParentCode, parentCodes)
                .likeRight(DeliveryReplenishmentPoolDetail::getDeliveryTime, yearMonthLy).list();
    }

    /**
     * 按活动编码加年月查询
     *
     * @param schemeDetailCodes
     * @param yearMonthLy
     * @return
     */
    public List<DeliveryReplenishmentPoolDetailVo> findBySchemeDetailCodes(List<String> schemeDetailCodes, String yearMonthLy) {
        if (CollectionUtils.isEmpty(schemeDetailCodes) || StringUtils.isBlank(yearMonthLy)) {
            return new ArrayList<>();
        }
        List<DeliveryReplenishmentPoolDetail> list = lambdaQuery().in(DeliveryReplenishmentPoolDetail::getSchemeDetailCode, schemeDetailCodes)
                .likeRight(DeliveryReplenishmentPoolDetail::getDeliveryTime, yearMonthLy)
                .in(DeliveryReplenishmentPoolDetail::getOperationType, Arrays.asList(
                        DeliveryDetailTypeEnum.MANUAL_CLEAR.getCode(),
                        DeliveryDetailTypeEnum.DELIVERY.getCode(),
                        DeliveryDetailTypeEnum.FEE_CASH.getCode(),
                        DeliveryDetailTypeEnum.FEE_CLOSE.getCode()
                )).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, DeliveryReplenishmentPoolDetail.class, DeliveryReplenishmentPoolDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 根据结案明细编码或兑付明细编码查询操作类型=清空或费用关闭的
     *
     * @param auditDetailCodes
     * @param businessCodes
     * @return
     */
    public List<DeliveryReplenishmentPoolDetail> findByAuditDetailCodes(List<String> auditDetailCodes, List<String> businessCodes) {
        return lambdaQuery()
                .and(wp -> wp.eq(DeliveryReplenishmentPoolDetail::getOperationType, DeliveryDetailTypeEnum.FEE_CLOSE.getCode())
                        .in(CollectionUtils.isNotEmpty(businessCodes), DeliveryReplenishmentPoolDetail::getBusinessCode, businessCodes))
                .or(wp -> wp.eq(DeliveryReplenishmentPoolDetail::getOperationType, DeliveryDetailTypeEnum.MANUAL_CLEAR.getCode())
                        .in(DeliveryReplenishmentPoolDetail::getParentBusinessCode, auditDetailCodes)).list();
    }

    /**
     * 根据结案明细编码查询
     *
     * @param auditDetailCodes
     * @return
     */
    public List<DeliveryReplenishmentPoolDetail> findByAuditDetailCodesAll(List<String> auditDetailCodes) {
        return lambdaQuery().in(DeliveryReplenishmentPoolDetail::getParentBusinessCode, auditDetailCodes)
                .eq(DeliveryReplenishmentPoolDetail::getOperationType, ReplenishmentPoolOperationType.DELIVERY.getCode()).list();
    }

    /**
     * 根据活动明细编码查询
     *
     * @param schemeDetailCodes
     * @return
     */
    public List<DeliveryReplenishmentPoolDetail> findBySchemeDetailCodesAll(List<String> schemeDetailCodes) {
        return lambdaQuery().in(DeliveryReplenishmentPoolDetail::getSchemeDetailCode, schemeDetailCodes)
                .in(DeliveryReplenishmentPoolDetail::getOperationType, Arrays.asList(ReplenishmentPoolOperationType.DELIVERY.getCode(), ReplenishmentPoolOperationType.MANUAL_CLEAR.getCode())).list();
    }

    public List<DeliveryReplenishmentPoolDetail> findByRuleCodes(List<String> ruleCodes) {
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        return lambdaQuery().in(DeliveryReplenishmentPoolDetail::getRuleCode, ruleCodes).list();
    }

    public  List<DeliveryReplenishmentPoolDetailVo> findManualClearAndDeliveryBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return new ArrayList<>();
        }
        List<DeliveryReplenishmentPoolDetail> list = lambdaQuery().in(DeliveryReplenishmentPoolDetail::getSchemeDetailCode, schemeDetailCodes)
                .in(DeliveryReplenishmentPoolDetail::getOperationType, Arrays.asList(
                        DeliveryDetailTypeEnum.MANUAL_CLEAR.getCode(),
                        DeliveryDetailTypeEnum.DELIVERY.getCode()
                )).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, DeliveryReplenishmentPoolDetail.class, DeliveryReplenishmentPoolDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }
}

