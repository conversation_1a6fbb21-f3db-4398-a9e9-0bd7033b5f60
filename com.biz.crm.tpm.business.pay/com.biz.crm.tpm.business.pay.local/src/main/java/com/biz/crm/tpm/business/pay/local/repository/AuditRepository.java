package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.Audit;
import com.biz.crm.tpm.business.pay.local.mapper.AuditMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 费用核销;(tpm_audit)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Component
public class AuditRepository extends ServiceImpl<AuditMapper, Audit> {
  @Autowired
  private AuditMapper auditMapper;

  /**
   * 批量根据id禁用
   *
   * @param enable
   * @param ids
   */
  public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
    UpdateWrapper<Audit> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("enable_status", enable.getCode());
    updateWrapper.eq("tenant_code",TenantUtils.getTenantCode());
    updateWrapper.in("id", ids);
    this.update(updateWrapper);
  }

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<AuditVo> findByConditions(Pageable pageable, AuditDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<AuditVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return auditMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<Audit>
   */
  public List<Audit> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .in(Audit::getId, ids)
        .eq(Audit::getTenantCode, tenantCode)
        .eq(Audit::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  /**
   * 根据编号与租户编号获取对象
   *
   * @param code
   * @return
   */
  public Audit findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .eq(Audit::getAuditCode, code)
        .eq(Audit::getTenantCode, tenantCode)
        .eq(Audit::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
  }

  /**
   * 根据编号集合获取详情集合
   *
   * @param codes 编号集合
   * @return List<Audit>
   */
  public List<Audit> findByCodes(List<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .in(Audit::getAuditCode, codes)
        .eq(Audit::getTenantCode, tenantCode)
        .eq(Audit::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  /**
   * 根据启用状态与租户编号查询对象
   *
   * @param enableStatus
   * @return
   */
  public List<Audit> findByEnableStatus(String enableStatus) {
    String tenantCode = TenantUtils.getTenantCode();
    if (StringUtils.isBlank(enableStatus)) {
      return this.lambdaQuery()
          .eq(Audit::getTenantCode, tenantCode)
          .eq(Audit::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }
    return this.lambdaQuery()
        .eq(Audit::getEnableStatus, enableStatus)
        .eq(Audit::getTenantCode, tenantCode)
        .eq(Audit::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
  }

  @Override
  public boolean removeByIds(Collection<? extends Serializable> idList) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate().in(Audit::getId, idList).
        eq(Audit::getTenantCode, tenantCode).
        set(Audit::getDelFlag, DelFlagStatusEnum.DELETE.getCode()).update();
  }

  public List<AuditVo> findByActivitiesDetailCode(String activitiesDetailCode) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.auditMapper.findByActivitiesDetailCode(tenantCode, activitiesDetailCode);
  }

  public Audit findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(Audit::getTenantCode,tenantCode)
        .in(Audit::getId,id)
        .one();
  }

}
