package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.pay.local.entity.AuditBill;
import com.biz.crm.tpm.business.pay.local.repository.AuditBillRepository;
import com.biz.crm.tpm.business.pay.local.service.AuditBillRecordService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditBillService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailService;
import com.biz.crm.tpm.business.pay.local.dto.AuditBillRecordDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditBillDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditBillVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.biz.crm.tpm.business.pay.sdk.constant.PayConstant.AUDIT_LOCK_PREFIX_FORMAT;

/**
 * 核销账单;(tpm_audit_bill)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-17
 */
@Service("auditBillService")
public class AuditBillServiceImpl implements AuditBillService {
  @Autowired
  private AuditBillRepository auditBillRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private RedisMutexService redisMutexService;
  @Autowired
  private AuditDetailService auditDetailService;
  @Autowired
  private AuditBillRecordService auditBillRecordService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private ActivitiesService activitiesService;

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public AuditBillVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    AuditBill auditBill = this.auditBillRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (auditBill == null) {
      return null;
    }
    AuditBillVo auditBillVo = this.nebulaToolkitService.copyObjectByWhiteList(auditBill, AuditBillVo.class, LinkedHashSet.class, ArrayList.class);
    return auditBillVo;
  }

  /**
   * 通过活动明细编号查询单条数据
   *
   * @param activitiesDetailCode 主键
   * @return 单条数据
   */
  @Override
  public AuditBillVo findByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return null;
    }
    AuditBill auditBill = this.auditBillRepository.findByActivitiesDetailCode(activitiesDetailCode);
    if (auditBill == null) {
      return null;
    }
    AuditBillVo auditBillVo = this.nebulaToolkitService.copyObjectByWhiteList(auditBill, AuditBillVo.class, LinkedHashSet.class, ArrayList.class);
    return auditBillVo;
  }

  /**
   * 新增数据
   *
   * @param auditBillDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AuditBillVo create(AuditBillDto auditBillDto) {
    this.createValidate(auditBillDto);
    AuditBill auditBill = this.auditBillRepository.findByActivitiesDetailCode(auditBillDto.getActivitiesDetailCode());
    if (auditBill != null) {
      AuditBillVo auditBillVo = this.nebulaToolkitService.copyObjectByWhiteList(auditBill, AuditBillVo.class, LinkedHashSet.class, ArrayList.class);
      auditBillVo.setId(auditBill.getId());
      return auditBillVo;
    }
    auditBill = this.nebulaToolkitService.copyObjectByWhiteList(auditBillDto, AuditBill.class, LinkedHashSet.class, ArrayList.class);
    auditBill.setTenantCode(TenantUtils.getTenantCode());
    this.auditBillRepository.saveOrUpdate(auditBill);
    AuditBillVo auditBillVo = this.nebulaToolkitService.copyObjectByWhiteList(auditBill, AuditBillVo.class, LinkedHashSet.class, ArrayList.class);
    auditBillVo.setId(auditBill.getId());
    // 初始化记录
    AuditBillRecordDto auditBillRecordDto = new AuditBillRecordDto();
    auditBillRecordDto.setTenantCode(TenantUtils.getTenantCode());
    auditBillRecordDto.setActivitiesDetailCode(auditBill.getActivitiesDetailCode());
    auditBillRecordDto.setChangeAmount(BigDecimal.ZERO);
    auditBillRecordDto.setType(0);
    this.auditBillRecordService.create(auditBillRecordDto);
    return auditBillVo;
  }

  @Override
  @Transactional
  public void auditAmountByActivityDetailCode(String activitiesDetailCode, String auditDetailCode, BigDecimal amount) {
    Validate.notBlank(activitiesDetailCode, "更改已核销金额，活动明细编号不能为空，请检查！");
    String tenantCode = TenantUtils.getTenantCode();
    String lockKey = String.format(AUDIT_LOCK_PREFIX_FORMAT, tenantCode, activitiesDetailCode);
    boolean isLock = false;
    try {
      isLock = this.redisMutexService.tryLock(lockKey, TimeUnit.SECONDS, 10);
      Validate.isTrue(isLock, "系统网络繁忙，请稍后重试");
      this.auditBillRepository.addAuditedAmount(activitiesDetailCode, amount);
      AuditBillRecordDto auditBillRecordDto = new AuditBillRecordDto();
      auditBillRecordDto.setTenantCode(TenantUtils.getTenantCode());
      auditBillRecordDto.setActivitiesDetailCode(activitiesDetailCode);
      auditBillRecordDto.setChangeAmount(amount);
      auditBillRecordDto.setBusinessCode(auditDetailCode);
      auditBillRecordDto.setType(1);
      this.auditBillRecordService.create(auditBillRecordDto);
      AuditBill auditBill = this.auditBillRepository.findByActivitiesDetailCode(activitiesDetailCode);
      BigDecimal waitBackAmount = auditBill.getApplyAmount().subtract(auditBill.getAuditedAmount());
      if (auditBill.getIsFullAudit().equals(BooleanEnum.TRUE.getCapital()) && waitBackAmount.compareTo(BigDecimal.ZERO) > 0) {
        AuditDetailVo auditDetailVo = this.auditDetailService.findByAuditDetailCode(auditDetailCode);
        ActivitiesVo activitiesVo = activitiesService.findByActivitiesCode(auditDetailVo.getActivitiesCode());
        Validate.notNull(activitiesVo, "该活动明细【%s】不含有主活动信息，请检查", auditDetailVo.getActivitiesDetailCode());
        this.costBudgetVoService.back(auditDetailVo.getAuditCode(), auditDetailVo.getAuditDetailCode(),auditDetailVo.getCostBudgetCode(), waitBackAmount,null, CostBudgetItemSourceType.AUDIT.getDescr());
      }
    } finally {
      if (isLock) {
        this.redisMutexService.unlock(lockKey);
      }
    }
  }

  @Override
  public void backAuditAmountByActivityDetailCode(String activitiesDetailCode, String auditDetailCode, BigDecimal amount) {
    Validate.notBlank(activitiesDetailCode, "更改已核销金额，活动明细编号不能为空，请检查！");
    String tenantCode = TenantUtils.getTenantCode();
    String lockKey = String.format(AUDIT_LOCK_PREFIX_FORMAT, tenantCode, activitiesDetailCode);
    boolean isLock = false;
    try {
      isLock = this.redisMutexService.tryLock(lockKey, TimeUnit.SECONDS, 10);
      Validate.isTrue(isLock, "系统网络繁忙，请稍后重试");
      this.auditBillRepository.reduceAuditedAmount(activitiesDetailCode, amount);
      AuditBillRecordDto auditBillRecordDto = new AuditBillRecordDto();
      auditBillRecordDto.setTenantCode(TenantUtils.getTenantCode());
      auditBillRecordDto.setActivitiesDetailCode(activitiesDetailCode);
      auditBillRecordDto.setBusinessCode(auditDetailCode);
      auditBillRecordDto.setChangeAmount(amount);
      auditBillRecordDto.setType(2);
      this.auditBillRecordService.create(auditBillRecordDto);
    } finally {
      if (isLock) {
        this.redisMutexService.unlock(lockKey);
      }
    }
  }

  @Override
  @Transactional
  public void auditAmountByAuditCode(String auditCode) {
    List<AuditDetailVo> auditDetailVos = this.auditDetailService.findByAuditCode(auditCode);
    for (AuditDetailVo auditDetailVo : auditDetailVos) {
      this.auditAmountByActivityDetailCode(auditDetailVo.getActivitiesDetailCode(), auditDetailVo.getAuditDetailCode(), auditDetailVo.getAuditAmount());
    }
  }

  @Override
  public void backAuditAmountByAuditCode(String auditCode) {
    List<AuditDetailVo> auditDetailVos = this.auditDetailService.findByAuditCode(auditCode);
    for (AuditDetailVo auditDetailVo : auditDetailVos) {
      this.backAuditAmountByActivityDetailCode(auditDetailVo.getActivitiesDetailCode(), auditDetailVo.getAuditDetailCode(), auditDetailVo.getAuditAmount());
    }
  }

  @Override
  public void updateIsFullAudit(String activitiesDetailCode, String isFullAudit) {
    if (StringUtils.isAnyBlank(activitiesDetailCode, isFullAudit)) {
      return;
    }
    AuditBill auditBill = this.auditBillRepository.findByActivitiesDetailCode(activitiesDetailCode);
    auditBill.setIsFullAudit(isFullAudit);
    auditBill.setTenantCode(TenantUtils.getTenantCode());
    this.auditBillRepository.saveOrUpdate(auditBill);
  }

  /**
   * 创建验证
   *
   * @param auditBillDto
   */
  private void createValidate(AuditBillDto auditBillDto) {
    Validate.notNull(auditBillDto, "新增时，对象信息不能为空！");
    auditBillDto.setId(null);
    // 验证重复操作
    Validate.notBlank(auditBillDto.getActivitiesDetailCode(), "新增数据时，活动明细编码不能为空！");
    Validate.notNull(auditBillDto.getApplyAmount(), "新增数据时，申请金额不能为空！");
    Validate.notNull(auditBillDto.getAuditedAmount(), "新增数据时，已核销金额不能为空！");
  }

  private String analysisSource(String value){
    switch (value){
      case "OrdinaryActivity":
        return CostBudgetItemSourceType.ORDINARY_ACTIVITY.getDescr();
      case "ProjectActivity":
        return CostBudgetItemSourceType.PROJECT_ACTIVITY.getDescr();
      case "SchemeActivity":
        return CostBudgetItemSourceType.SCHEMA_ACTIVITY.getDescr();
      default:
        throw new IllegalArgumentException("活动关闭时，未知的费用预算来源，请检查");
    }
  }
}
