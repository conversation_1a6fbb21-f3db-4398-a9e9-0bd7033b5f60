package com.biz.crm.tpm.business.pay.local.notifier.log;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.pay.sdk.dto.log.InvoiceLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.event.log.InvoiceLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 发票池 业务日志监听实现
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */
@Component
public class InvoiceLogEventListenerImpl implements InvoiceLogEventListener {
  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;
  /**
   * 创建事件
   *
   * @param eventDto
   */
  public void onCreate(InvoiceLogEventDto eventDto){
    InvoiceVo newest = eventDto.getNewest();
    InvoiceVo original = eventDto.getOriginal();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
  /**
   * 删除事件
   *
   * @param eventDto
   */
  public void onDelete(InvoiceLogEventDto eventDto){
    InvoiceVo newest = eventDto.getNewest();
    InvoiceVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
  /**
   * 更新日志
   *
   * @param eventDto
   */
  public void onUpdate(InvoiceLogEventDto eventDto){
    InvoiceVo newest = eventDto.getNewest();
    InvoiceVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
  /**
   * 启用
   *
   * @param eventDto
   */
  public void onEnable(InvoiceLogEventDto eventDto){
    InvoiceVo newest = eventDto.getNewest();
    InvoiceVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    //只传单个字段
    JSONObject oldObject = new JSONObject();
    JSONObject newObject = new JSONObject();
    oldObject.put("enableStatus", original.getEnableStatus());
    newObject.put("enableStatus", newest.getEnableStatus());
    crmBusinessLogDto.setOldObject(oldObject);
    crmBusinessLogDto.setNewObject(newObject);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
  /**
   * 禁用
   *
   * @param eventDto
   */
  public void onDisable(InvoiceLogEventDto eventDto){
    InvoiceVo newest = eventDto.getNewest();
    InvoiceVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    //只传单个字段
    JSONObject oldObject = new JSONObject();
    JSONObject newObject = new JSONObject();
    oldObject.put("enableStatus", original.getEnableStatus());
    newObject.put("enableStatus", newest.getEnableStatus());
    crmBusinessLogDto.setOldObject(oldObject);
    crmBusinessLogDto.setNewObject(newObject);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
}
