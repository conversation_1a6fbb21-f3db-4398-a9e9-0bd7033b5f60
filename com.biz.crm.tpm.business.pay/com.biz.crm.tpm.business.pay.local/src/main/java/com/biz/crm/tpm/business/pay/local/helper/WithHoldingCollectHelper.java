package com.biz.crm.tpm.business.pay.local.helper;

import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectDepartmentEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectItemEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.enums.SecondCategoryEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WithHoldingCollectHelper {

    @Resource
    private ApplicationContext context;

    @Resource
    private MarketingPlanCaseRepository caseRepository;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private MarketingPlanProductRepository productRepository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private CostTypeCategoryVoService costTypeCategoryVoService;

    @Resource
    private WithHoldingService withHoldingService;

    @Resource
    private CostBudgetIncomeService costBudgetIncomeService;

    @Resource
    private FeeCashService feeCashService;

    @Resource
    private ProductPhaseVoService productPhaseVoService;

    /**
     * 客户拆分
     */
    public static final String CUSTOMER_SPLITTING = "customer_splitting";

    private List<RegionCollectGainsAndLossesVo> gainsAndLossesVoList;

    private Map<String, List<String>> costTypeCategoryMap;

    private List<MarketingPlanCase> caseList = new ArrayList<>();

    private List<MarketingPlanCase> caseVosAll = new ArrayList<>();

    private List<String> orgCodeList;

    private List<String> customerCodes;

    /**
     * 构建客户损益
     *
     * @param schemeCodes
     * @param years
     * @return
     */
    public List<RegionCollectGainsAndLossesVo> buildCustomerGainsAndLosses(List<String> schemeCodes, Map<String, OrgVo> orgMap, String years, List<WithHoldingVo> withHoldingList, List<MarketingPlanCase> caseListAll) {

        if (CollectionUtils.isNotEmpty(caseListAll)) {
            caseVosAll = caseListAll;
            List<MarketingPlanCase> caseVos = caseListAll.stream().filter(e -> e.getCashAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caseVos)) {
                caseList = caseVos;
            }
        }

        orgCodeList = new ArrayList<>(orgMap.keySet());

        //部门下所有客户
        List<CustomerVo> customerVoList = customerVoService.findByOrgCodes(orgCodeList);
        //根据数据字典查询客户
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(CUSTOMER_SPLITTING);
        List<String> dictCustomerCodes = new ArrayList<>();
        //上月客户
        List<String> lastMonthCustomer = dictDataVos.stream().filter(e -> "last_month".equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());
        //本月客户
        List<String> currentMonthCustomer = dictDataVos.stream().filter(e -> "current_month".equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());
        dictCustomerCodes.addAll(lastMonthCustomer);
        dictCustomerCodes.addAll(currentMonthCustomer);
        List<CustomerVo> dictCustomerVos = customerVoService.findByCustomerCodes(dictCustomerCodes);

        Set<String> indexKeySet = customerVoList.stream().map(e -> e.getCustomerCode() + ":" + (StringUtils.isNotBlank(e.getOrgCode()) ? e.getOrgCode() : e.getOrgList().get(0).getOrgCode()) + ":" + years).collect(Collectors.toSet());

        Map<String, CustomerVo> customerMap = customerVoList.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, v -> v, (n, o) -> n));
        List<RegionCollectGainsAndLossesVo> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(indexKeySet)) {
            for (String s : indexKeySet) {
                String[] keys = s.split(":");
                RegionCollectGainsAndLossesVo data = new RegionCollectGainsAndLossesVo();
                data.setIndexKey(s);
                data.setIndexKeyExceptOrg(keys[0] + ":" + keys[2]);
                //基础数据设置
                data.setCustomerCode(keys[0]);
                data.setOrgCode(keys[1]);
                data.setOrgName(orgMap.getOrDefault(keys[1], new OrgVo()).getOrgName());
                data.setYears(keys[2]);
                CustomerVo customerVo = customerMap.get(data.getCustomerCode());
                Assert.notNull(customerVo, "客户[" + data.getCustomerCode() + "]为找到!");
                data.setErpCode(customerVo.getErpCode());
                data.setCustomerName(customerVo.getCustomerName());
                data.setCompanyCode(customerVo.getCompanyCode());
                data.setChannelCode(customerVo.getChannelCode());
                data.setProductGroupCode(customerVo.getProductGroupCode());
                data.setBeSplitting(Boolean.FALSE);
                dataList.add(data);
            }
            //重科客户
            for (String orgCodeData : orgCodeList) {
                for (CustomerVo dictCustomer : dictCustomerVos) {
                    RegionCollectGainsAndLossesVo dataCustomer = new RegionCollectGainsAndLossesVo();

                    dataCustomer.setIndexKey(dictCustomer.getCustomerCode() + ":" + orgCodeData + ":" + years);
                    dataCustomer.setIndexKeyExceptOrg(dictCustomer.getCustomerCode() + ":" + years);
                    dataCustomer.setCustomerCode(dictCustomer.getCustomerCode());
                    dataCustomer.setOrgCode(orgCodeData);
                    dataCustomer.setOrgName(orgMap.getOrDefault(orgCodeData, new OrgVo()).getOrgName());
                    dataCustomer.setYears(years);
                    dataCustomer.setErpCode(dictCustomer.getErpCode());
                    dataCustomer.setCustomerName(dictCustomer.getCustomerName());
                    dataCustomer.setCompanyCode(dictCustomer.getCompanyCode());
                    dataCustomer.setChannelCode(dictCustomer.getChannelCode());
                    dataCustomer.setProductGroupCode(dictCustomer.getProductGroupCode());
                    dataCustomer.setBeSplitting(Boolean.TRUE);
                    dataList.add(dataCustomer);
                }
            }
        } else {
            //重科客户
            for (String orgCodeData : orgCodeList) {
                for (CustomerVo dictCustomer : dictCustomerVos) {
                    RegionCollectGainsAndLossesVo dataCustomer = new RegionCollectGainsAndLossesVo();

                    dataCustomer.setIndexKey(dictCustomer.getCustomerCode() + ":" + orgCodeData + ":" + years);
                    dataCustomer.setIndexKeyExceptOrg(dictCustomer.getCustomerCode() + ":" + years);
                    dataCustomer.setCustomerCode(dictCustomer.getCustomerCode());
                    dataCustomer.setOrgCode(orgCodeData);
                    dataCustomer.setOrgName(orgMap.getOrDefault(orgCodeData, new OrgVo()).getOrgName());
                    dataCustomer.setYears(years);
                    dataCustomer.setErpCode(dictCustomer.getErpCode());
                    dataCustomer.setCustomerName(dictCustomer.getCustomerName());
                    dataCustomer.setCompanyCode(dictCustomer.getCompanyCode());
                    dataCustomer.setChannelCode(dictCustomer.getChannelCode());
                    dataCustomer.setProductGroupCode(dictCustomer.getProductGroupCode());
                    dataCustomer.setBeSplitting(Boolean.TRUE);
                    dataList.add(dataCustomer);
                }
            }
        }
        MarketingPlanCustomerGainsWithHoldingAbstractBuilder.builder(context, dataList, caseList, withHoldingList, orgCodeList, lastMonthCustomer, currentMonthCustomer)
                .init(schemeCodes, years)
                .loadInitData()
                .calPublicShareRatio()
                .calProfitRatio()
                .calGrossProfitRatio()
                .calLogisticsRatio()
                .calMarketingRatio();

        customerCodes = dataList.stream().map(RegionCollectGainsAndLossesVo::getCustomerCode).distinct().collect(Collectors.toList());

        gainsAndLossesVoList = dataList;

        //按客户分组
        Map<String, List<WithHoldingVo>> withHoldingMap = withHoldingList.stream().collect(Collectors.groupingBy(e -> e.getCustomerCode()));
        Map<String, List<RegionCollectGainsAndLossesVo>> map = dataList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode()));
        List<RegionCollectGainsAndLossesVo> resultDataList = Lists.newArrayList();
        for (Map.Entry<String, List<RegionCollectGainsAndLossesVo>> entry : map.entrySet()) {
            RegionCollectGainsAndLossesVo data = new RegionCollectGainsAndLossesVo();
            data.setIndexKey(entry.getValue().get(0).getCustomerCode() + ":" + years);
            data.setPublicShareRatio(entry.getValue().get(0).getPublicShareRatio());
            data.setCustomerCode(entry.getValue().get(0).getCustomerCode());
            data.setCustomerName(entry.getValue().get(0).getCustomerName());
            data.setYears(entry.getValue().get(0).getYears());
            BigDecimal budgetIncome = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getBudgetIncome()))
                    .map(RegionCollectGainsAndLossesVo::getBudgetIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal planIncome = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanIncome()))
                    .map(RegionCollectGainsAndLossesVo::getPlanIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal giftCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getGiftCost()))
                    .map(RegionCollectGainsAndLossesVo::getGiftCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal budgetTotalAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getBudgetTotalAmount()))
                    .map(RegionCollectGainsAndLossesVo::getBudgetTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal planTotalAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanTotalAmount()))
                    .map(RegionCollectGainsAndLossesVo::getPlanTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal productionCosts = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductionCosts()))
                    .map(RegionCollectGainsAndLossesVo::getProductionCosts)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal productTransportCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductTransportCost()))
                    .map(RegionCollectGainsAndLossesVo::getProductTransportCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal peripheryTransportCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPeripheryTransportCost()))
                    .map(RegionCollectGainsAndLossesVo::getPeripheryTransportCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal marketingCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getMarketingCost()))
                    .map(RegionCollectGainsAndLossesVo::getMarketingCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal planProfitMargin = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanProfitMargin()))
                    .map(RegionCollectGainsAndLossesVo::getPlanProfitMargin)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal planGrossProfitMargin = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanGrossProfitMargin()))
                    .map(RegionCollectGainsAndLossesVo::getPlanGrossProfitMargin)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal promotion = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPromotionAmount()))
                    .map(RegionCollectGainsAndLossesVo::getPromotionAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal callback = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCallbackAmount()))
                    .map(RegionCollectGainsAndLossesVo::getCallbackAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal display = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getDisplayAmount()))
                    .map(RegionCollectGainsAndLossesVo::getDisplayAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal generalization = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getGeneralizationAmount()))
                    .map(RegionCollectGainsAndLossesVo::getGeneralizationAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal disseminate = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getDisseminateAmount()))
                    .map(RegionCollectGainsAndLossesVo::getDisseminateAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal salesReward = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getSalesRewardAmount()))
                    .map(RegionCollectGainsAndLossesVo::getSalesRewardAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal contract = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getContractAmount()))
                    .map(RegionCollectGainsAndLossesVo::getContractAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            data.setBudgetIncome(budgetIncome);
            data.setPlanIncome(planIncome);
            data.setGiftCost(giftCost);
            data.setBudgetTotalAmount(budgetTotalAmount);
            data.setPlanTotalAmount(planTotalAmount);
            data.setProductionCosts(productionCosts);
            data.setProductTransportCost(productTransportCost);
            data.setPeripheryTransportCost(peripheryTransportCost);
            data.setMarketingCost(marketingCost);
            data.setPlanProfitMargin(planProfitMargin);
            data.setPlanGrossProfitMargin(planGrossProfitMargin);
            if (BigDecimal.ZERO.compareTo(planIncome) != 0) {
                data.setPromotion(promotion.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setCallback(callback.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setDisplay(display.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setGeneralization(generalization.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setDisseminate(disseminate.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setSalesReward(salesReward.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setContract(contract.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (data.getBudgetIncome().compareTo(BigDecimal.ZERO) == 1) {
                data.setIncomeAchieveRatio(data.getPlanIncome().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(data.getBudgetTotalAmount()) && ObjectUtils.isNotEmpty(data.getBudgetIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) > 0) {
                //预算总额/预算收入
                data.setBudgetRatio(data.getBudgetTotalAmount().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && ObjectUtils.isNotEmpty(data.getPlanTotalAmount()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                //规划总额/规划收入
                data.setPlanRatio(data.getPlanTotalAmount().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            //费率偏差 = 规划费率-预算费率
            if (ObjectUtils.isNotEmpty(data.getPlanRatio()) && ObjectUtils.isNotEmpty(data.getBudgetRatio())) {
                data.setRatioDeviation(data.getPlanRatio().subtract(data.getBudgetRatio()));
            }
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setProfitRatio(data.getPlanProfitMargin().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setGrossProfitRatio(data.getPlanGrossProfitMargin().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setLogisticsRatio((data.getProductTransportCost().add(data.getPeripheryTransportCost())).divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setMarketingRatio(data.getMarketingCost().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            resultDataList.add(data);

            //费率，使用管报计提实际金额/该客户发货金额
            if (withHoldingMap.containsKey(data.getCustomerCode())) {
                withHoldingMap.get(data.getCustomerCode()).forEach(e -> {
                    e.setRatio(data.getPlanIncome().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getActualReportAmount().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_UP));
                    e.setRatioStr(e.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
                });
            }
        }

        for (CustomerGainsAndLossesVo data : resultDataList) {
            List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
            costTypeCategoryMap = costTypeCategoryVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
            //二级费用大类计算
            Map<String, BigDecimal> secondCategoryMap = Maps.newHashMap();
            Map<String, BigDecimal> applyAmountMap = Maps.newHashMap();
            for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
                BigDecimal cashAmount = caseList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()) &&
                                (x.getCustomerCode() + ":" + x.getYears()).equals(data.getIndexKey()))
                        .map(x -> Optional.ofNullable(x.getCashAmount()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal withHoldingAmount = withHoldingList.stream().filter(x -> entry.getValue().contains(x.getCostTypeCategoryCode()) &&
                                (x.getCustomerCode() + ":" + x.getYears()).equals(data.getIndexKey()))
                        .map(x -> Optional.ofNullable(x.getNoTaxActualReportAmount()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal ratio = BigDecimal.ZERO;
                if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                    //促销需要加上搭赠
                    BigDecimal addAmount = cashAmount.add(withHoldingAmount);
                    if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
                        addAmount = addAmount.add(data.getGiftCost());
                    }
                    ratio = addAmount.divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
                }
                secondCategoryMap.put(entry.getKey(), ratio);

                applyAmountMap.put(entry.getKey(), cashAmount.add(withHoldingAmount));
            }
            for (Map.Entry<String, BigDecimal> entry : secondCategoryMap.entrySet()) {
                BigDecimal value = entry.getValue();
                if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
                    data.setContract(value);
                    data.setContractAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
                    data.setGeneralization(value);
                    data.setGeneralizationAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
                    data.setPromotion(value);
                    data.setPromotionAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
                    data.setSalesReward(value);
                    data.setSalesRewardAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.callback.getName().equals(entry.getKey())) {
                    data.setCallback(value);
                    data.setCallbackAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.display.getName().equals(entry.getKey())) {
                    data.setDisplay(value);
                    data.setDisplayAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.disseminateS.getName().equals(entry.getKey())) {
                    data.setDisseminate(value);
                    data.setDisseminateAmount(applyAmountMap.get(entry.getKey()));
                }

            }
            data.setSecondCostCategoryMap(secondCategoryMap);
        }
        //转换费率
        transRatio(resultDataList);

        return resultDataList;

//        return dataList;
    }


    /**
     * 构建二级部门测算
     *
     * @param schemeCodes
     * @param years
     * @return
     */
    public List<RegionCollectDepartmentEstimationVo> buildDepartmentEstimation(List<String> schemeCodes, String orgCode, String years, List<WithHoldingVo> withHoldingList) {
//        Set<String> indexKeySet = CollectionUtils.isNotEmpty(caseList) ? caseList.stream().map(x -> x.getBelongDepartmentCode() + ":" + years).collect(Collectors.toSet()) : new HashSet<>();
//        indexKeySet.addAll(CollectionUtils.isNotEmpty(withHoldingListDept) ? withHoldingListDept.stream().map(x -> x.getBelongDepartmentCode() + ":" + years).collect(Collectors.toSet()) : new HashSet<>());
//        if (CollectionUtils.isEmpty(indexKeySet)) {
//            return Lists.newArrayList();
//        }
//        Map<String, String> orgMap = orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
//        List<RegionCollectDepartmentEstimationVo> dataList = new ArrayList<>(indexKeySet.size());
//        for (String s : indexKeySet) {
//            String[] keys = s.split(":");
//            RegionCollectDepartmentEstimationVo data = new RegionCollectDepartmentEstimationVo();
//            data.setIndexKey(s);
//            //基础数据设置
//            data.setOrgCode(keys[0]);
//            data.setYears(keys[1]);
//            data.setOrgName(orgMap.get(data.getOrgCode()));
//            dataList.add(data);
//        }
//        DepartmentEstimationWithHoldingAbstractBuilder.builder(context, dataList, caseList, withHoldingListDept)
//                .init(schemeCodes, years)
//                .loadInitData()
//                .calProfitRatio()
//                .calGrossProfitRatio()
//                .calLogisticsRatio()
//                .calMarketingRatio()
//                .calPublicShareRatio()
//                .calSecondCategory();


        Map<String, List<RegionCollectGainsAndLossesVo>> map = gainsAndLossesVoList.stream().collect(Collectors.groupingBy(x -> x.getOrgCode()));
        Set<String> orgCodes =  map.keySet();
        Map<String,List<OrgVo>> orgListMap =  orgVoService.findAllChildrenByOrgCodesMap(Lists.newArrayList(orgCodes));
        Map<String,BigDecimal> orgBudgetIncomeMap = Maps.newHashMap();
        for (Map.Entry<String, List<OrgVo>> entry : orgListMap.entrySet()) {
            List<String> orgCodeList = entry.getValue().stream().map(x->x.getOrgCode()).collect(Collectors.toList());
            List<CostBudgetIncomeVo> costBudgetIncomeVos = costBudgetIncomeService.findIncomeAmountByOrgCodeAndYears(orgCodeList,years);
            List<String> itemCodes = costBudgetIncomeVos.stream().map(CostBudgetIncomeVo::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
            Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> null == v.getTaxRate() ? BigDecimal.ZERO : v.getTaxRate()));
            BigDecimal budgetIncome = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(costBudgetIncomeVos)) {
                budgetIncome = costBudgetIncomeVos.stream()
                        .filter(x->ObjectUtils.isNotEmpty(x.getIncomeAmount()) && x.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode()))
                        .map(x -> Optional.ofNullable(x.getIncomeAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(x.getItemCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP))
                        .reduce(BigDecimal.ZERO,BigDecimal::add);
            }
            orgBudgetIncomeMap.put(entry.getKey(),budgetIncome);
        }
        List<RegionCollectDepartmentEstimationVo> resultDataList = Lists.newArrayList();
        for (Map.Entry<String, List<RegionCollectGainsAndLossesVo>> entry : map.entrySet()) {
            RegionCollectDepartmentEstimationVo data = new RegionCollectDepartmentEstimationVo();
            data.setIndexKey(entry.getValue().get(0).getOrgCode() + ":" + years);
            data.setPublicShareRatio(entry.getValue().get(0).getPublicShareRatio());
            data.setOrgCode(entry.getValue().get(0).getOrgCode());
            data.setOrgName(entry.getValue().get(0).getOrgName());
            data.setYears(entry.getValue().get(0).getYears());


            BigDecimal budgetIncome = orgBudgetIncomeMap.getOrDefault(entry.getKey(),BigDecimal.ZERO);
            BigDecimal planIncome = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanIncome()))
                    .map(RegionCollectGainsAndLossesVo::getPlanIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal giftCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getGiftCost()))
                    .map(RegionCollectGainsAndLossesVo::getGiftCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal budgetTotalAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getBudgetTotalAmount()))
                    .map(RegionCollectGainsAndLossesVo::getBudgetTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal planTotalAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanTotalAmount()))
                    .map(RegionCollectGainsAndLossesVo::getPlanTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal productionCosts = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductionCosts()))
                    .map(RegionCollectGainsAndLossesVo::getProductionCosts)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal productTransportCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductTransportCost()))
                    .map(RegionCollectGainsAndLossesVo::getProductTransportCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal peripheryTransportCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPeripheryTransportCost()))
                    .map(RegionCollectGainsAndLossesVo::getPeripheryTransportCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal marketingCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getMarketingCost()))
                    .map(RegionCollectGainsAndLossesVo::getMarketingCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal planProfitMargin = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanProfitMargin()))
                    .map(RegionCollectGainsAndLossesVo::getPlanProfitMargin)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal planGrossProfitMargin = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanGrossProfitMargin()))
                    .map(RegionCollectGainsAndLossesVo::getPlanGrossProfitMargin)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal promotion = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPromotionAmount()))
                    .map(RegionCollectGainsAndLossesVo::getPromotionAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal callback = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCallbackAmount()))
                    .map(RegionCollectGainsAndLossesVo::getCallbackAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal display = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getDisplayAmount()))
                    .map(RegionCollectGainsAndLossesVo::getDisplayAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal generalization = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getGeneralizationAmount()))
                    .map(RegionCollectGainsAndLossesVo::getGeneralizationAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal disseminate = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getDisseminateAmount()))
                    .map(RegionCollectGainsAndLossesVo::getDisseminateAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal salesReward = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getSalesRewardAmount()))
                    .map(RegionCollectGainsAndLossesVo::getSalesRewardAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal contract = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getContractAmount()))
                    .map(RegionCollectGainsAndLossesVo::getContractAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //费用转移金额
            data.setCostShift(BigDecimal.ZERO);
            //考核利润
            data.setAssessProfits(BigDecimal.ZERO);

            //汇总部门下的所有组织
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(entry.getValue().get(0).getOrgCode());
            List<String> orgCodeList = orgVoList.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
            List<WithHoldingVo> withHoldingDiffList = withHoldingList.stream().filter(e -> orgCodeList.contains(e.getBelongDepartmentCode()) && !e.getBelongDepartmentCode().equals(e.getBearDepartmentCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(withHoldingDiffList)) {
                //获取计提前兑付金额
                List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesApprovedBefore(orgCodeList, years);
                BigDecimal feeCashAmount = CollectionUtils.isNotEmpty(feeCashDetailVos) ? feeCashDetailVos.stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO;
                //费用转移 统计
                BigDecimal actualReportAmount = withHoldingDiffList.stream().map(e -> e.getActualReportAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                data.setCostShift(actualReportAmount.add(feeCashAmount));
                //考核利润
                BigDecimal assesProfits = data.getCostShift().add(planProfitMargin);
                data.setAssessProfits(assesProfits);
            }
            data.setBudgetIncome(budgetIncome);
            data.setPlanIncome(planIncome);
            data.setGiftCost(giftCost);
            data.setBudgetTotalAmount(budgetTotalAmount);
            data.setPlanTotalAmount(planTotalAmount);
            data.setProductionCosts(productionCosts);
            data.setProductTransportCost(productTransportCost);
            data.setPeripheryTransportCost(peripheryTransportCost);
            data.setMarketingCost(marketingCost);
            data.setPlanProfitMargin(planProfitMargin);
            data.setPlanGrossProfitMargin(planGrossProfitMargin);
            if (BigDecimal.ZERO.compareTo(planIncome) != 0) {
                data.setPromotion(promotion.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setCallback(callback.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setDisplay(display.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setGeneralization(generalization.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setDisseminate(disseminate.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setSalesReward(salesReward.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                data.setContract(contract.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (data.getBudgetIncome().compareTo(BigDecimal.ZERO) == 1) {
                data.setIncomeAchieveRatio(data.getPlanIncome().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(data.getBudgetTotalAmount()) && ObjectUtils.isNotEmpty(data.getBudgetIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) > 0) {
                //预算总额/预算收入
                data.setBudgetRatio(data.getBudgetTotalAmount().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && ObjectUtils.isNotEmpty(data.getPlanTotalAmount()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                //规划总额/规划收入
                data.setPlanRatio(data.getPlanTotalAmount().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            //费率偏差 = 规划费率-预算费率
            if (ObjectUtils.isNotEmpty(data.getPlanRatio()) && ObjectUtils.isNotEmpty(data.getBudgetRatio())) {
                data.setRatioDeviation(data.getPlanRatio().subtract(data.getBudgetRatio()));
            }
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setProfitRatio(data.getPlanProfitMargin().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setGrossProfitRatio(data.getPlanGrossProfitMargin().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setLogisticsRatio((data.getProductTransportCost().add(data.getPeripheryTransportCost())).divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setMarketingRatio(data.getMarketingCost().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            resultDataList.add(data);
        }

        for (CustomerGainsAndLossesVo data : resultDataList) {
            //二级费用大类计算
            Map<String, BigDecimal> secondCategoryMap = Maps.newHashMap();
            Map<String, BigDecimal> applyAmountMap = Maps.newHashMap();
            for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
                BigDecimal cashAmount = caseList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()) &&
                                (x.getBelongDepartmentCode() + ":" + x.getYears()).equals(data.getIndexKey()))
                        .map(x -> Optional.ofNullable(x.getCashAmount()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal withHoldingAmount = withHoldingList.stream().filter(x -> entry.getValue().contains(x.getCostTypeCategoryCode()) &&
                                (x.getBelongDepartmentCode() + ":" + x.getYears()).equals(data.getIndexKey()))
                        .map(x -> Optional.ofNullable(x.getNoTaxActualReportAmount()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal ratio = BigDecimal.ZERO;
                if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                    //促销需要加上搭赠
                    BigDecimal addAmount = cashAmount.add(withHoldingAmount);
                    if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
                        addAmount = addAmount.add(data.getGiftCost());
                    }
                    ratio = addAmount.divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
                }
                secondCategoryMap.put(entry.getKey(), ratio);

                applyAmountMap.put(entry.getKey(), cashAmount.add(withHoldingAmount));
            }
            for (Map.Entry<String, BigDecimal> entry : secondCategoryMap.entrySet()) {
                BigDecimal value = entry.getValue();
                if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
                    data.setContract(value);
                    data.setContractAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
                    data.setGeneralization(value);
                    data.setGeneralizationAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
                    data.setPromotion(value);
                    data.setPromotionAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
                    data.setSalesReward(value);
                    data.setSalesRewardAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.callback.getName().equals(entry.getKey())) {
                    data.setCallback(value);
                    data.setCallbackAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.display.getName().equals(entry.getKey())) {
                    data.setDisplay(value);
                    data.setDisplayAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.disseminateS.getName().equals(entry.getKey())) {
                    data.setDisseminate(value);
                    data.setDisseminateAmount(applyAmountMap.get(entry.getKey()));
                }

            }
            data.setSecondCostCategoryMap(secondCategoryMap);
        }

        transRatio(resultDataList);
        return resultDataList;
    }


    /**
     * 构建品项测算数据
     *
     * @param schemeCodes
     * @param years
     * @return
     */
    public List<RegionCollectItemEstimationVo> buildItemEstimation(List<String> schemeCodes, String years, String orgCode, List<WithHoldingVo> withHoldingList) {
        List<RegionCollectGainsAndLossesVo> dataList = new ArrayList<>(gainsAndLossesVoList.size());
        for (RegionCollectGainsAndLossesVo vo : gainsAndLossesVoList) {
            RegionCollectGainsAndLossesVo data = new RegionCollectGainsAndLossesVo();
            String[] keys = vo.getIndexKey().split(":");
            data.setIndexKey(vo.getIndexKey());
            data.setCustomerCode(keys[0]);
            data.setOrgCode(keys[1]);
            data.setYears(years);
            data.setRate(vo.getRate());
            dataList.add(data);
        }
        List<RegionCollectItemEstimationVo> itemList = new ArrayList<>();
        ItemEstimationAbstractWithHoldingBuilder.builder(context, dataList, itemList, caseList, orgCodeList, customerCodes, withHoldingList)
                .init(schemeCodes, years)
                .loadInitData()
                .calProfitRatio()
                .calGrossProfitRatio()
                .calLogisticsRatio()
                .calMarketingRatio()
                .calSecondCategory();
        return itemList;
    }


    /**
     * 营销费用测算
     *
     * @param schemeCode
     * @param years
     * @param orgCode
     * @return
     */
    public List<RegionCollectMarketingEstimationVo> buildMarketingEstimation(List<String> schemeCode, String years, String orgCode, List<WithHoldingVo> withHoldingList,
                                                                             List<RegionCollectMarketingEstimationVo> planMarketingEstimationVos) {
        List<RegionCollectMarketingEstimationVo> dataList = MarketingEstimationWithHoldingAbstractBuilder.builder(context, orgCode, withHoldingList, caseList, caseVosAll, gainsAndLossesVoList, planMarketingEstimationVos)
                .init(schemeCode, years)
                .estimationRevenue()
                .productionCost()
                .grossProfitMargin()
                .productTransportCost()
                .peripheryTransport()
                .transportTotal()
                .giftCost()
                .categoryCost()
                .marketingCostTotal()
                .marginalContribution()
                .artificialTravelOnBusiness()
                .costTotal()
                .profit()
                .costShift()
                .accessProfits()
                .getList();
        return dataList;
    }

    private void transRatio(List<? extends CustomerGainsAndLossesVo> dataList) {
        for (CustomerGainsAndLossesVo vo : dataList) {

            if (ObjectUtils.isNotEmpty(vo.getPlanIncome())) {
                vo.setPlanIncome(vo.getPlanIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetIncome())) {
                vo.setBudgetIncome(vo.getBudgetIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getCostShift())){
                vo.setCostShift(vo.getCostShift().divide(BigDecimal.valueOf(10000),2,BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getAssessProfits())){
                vo.setAssessProfits(vo.getAssessProfits().divide(BigDecimal.valueOf(10000),2,BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getIncomeAchieveRatio())) {
                vo.setIncomeAchieveRatioStr(vo.getIncomeAchieveRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetRatio())) {
                vo.setBudgetRatioStr(vo.getBudgetRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanRatio())) {
                vo.setPlanRatioStr(vo.getPlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getRatioDeviation())) {
                vo.setRatioDeviationStr(vo.getRatioDeviation().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getProfitRatio())) {
                vo.setProfitRatioStr(vo.getProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGrossProfitRatio())) {
                vo.setGrossProfitRatioStr(vo.getGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getLogisticsRatio())) {
                vo.setLogisticsRatioStr(vo.getLogisticsRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getMarketingRatio())) {
                vo.setMarketingRatioStr(vo.getMarketingRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPublicShareRatio())) {
                vo.setPublicShareRatioStr(vo.getPublicShareRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPromotion())) {
                vo.setPromotionStr(vo.getPromotion().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getCallback())) {
                vo.setCallbackStr(vo.getCallback().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisplay())) {
                vo.setDisplayStr(vo.getDisplay().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGeneralization())) {
                vo.setGeneralizationStr(vo.getGeneralization().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisseminate())) {
                vo.setDisseminateStr(vo.getDisseminate().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getSalesReward())) {
                vo.setSalesRewardStr(vo.getSalesReward().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getContract())) {
                vo.setContractStr(vo.getContract().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
        }
    }
}
