package com.biz.crm.tpm.business.pay.local.service.internal;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.FeeCash;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAuditDetail;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashRepository;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashTocRepository;
import com.biz.crm.tpm.business.pay.local.repository.MarketingAuditDetailRepository;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTocDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashTocService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTocVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FeeCashTocServiceImpl extends BusinessPageCacheServiceImpl<FeeCashTocVo, FeeCashTocDto> implements FeeCashTocService {

    @Autowired(required = false)
    private FeeCashTocRepository feeCashTocRepository;
    @Autowired(required = false)
    private FeeCashDetailRepository feeCashDetailRepository;
    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;
    @Autowired(required = false)
    private FeeCashRepository feeCashRepository;

    /**
     * 获取所有缓存
     *
     * @param cacheKey
     * @return
     */
    @Override
    public List<FeeCashTocDto> findAllCacheList(String cacheKey) {
        return findCacheList(cacheKey);
    }

    @Override
    public void saveCurrentPageCache(String cacheKey, List<FeeCashTocDto> saveList) {
        List<FeeCashTocDto> dataList = checkDetails(saveList);
        log.info("tocDetails: {}", JSON.toJSONString(dataList));
        super.saveCurrentPageCache(cacheKey, dataList);
    }

    private List<FeeCashTocDto> checkDetails(List<FeeCashTocDto> saveList) {
        log.info("tocCheckDetailStart");
        saveList.forEach(vo -> {
            StringJoiner errMsg = new StringJoiner(";");
            if (StringUtils.isNotEmpty(vo.getBankNo())) {
                String bankNo = vo.getBankNo().replaceAll(" ", "");
                vo.setBankNo(bankNo);
                if (bankNo.contains(" ")) {
                    errMsg.add("银行账号不能包含空格！");
                }
                if (!bankNo.matches("^[0-9]+$")) {
                    errMsg.add("银行账号只能包含数字！");
                }
            }

            // 身份证号码格式校验：不允许空格且位数固定（18位数）
            if (StringUtils.isNotEmpty(vo.getIdCard())) {
                String idCard = vo.getIdCard().replaceAll(" ", "");
                vo.setIdCard(idCard);
                if (idCard.contains(" ")) {
                    errMsg.add("身份证号码不能包含空格！");
                }
                if (idCard.length() != 18) {
                    errMsg.add("身份证号码必须为18位！");
                }
                if (!idCard.matches("^[0-9]{17}[0-9Xx]$")) {
                    errMsg.add("身份证号码格式不正确！");
                }
            }

            // 手机号格式校验：不允许空格且位数固定（11位数）
            if (StringUtils.isNotEmpty(vo.getPhone())) {
                String phone = vo.getPhone().replaceAll(" ", "");
                vo.setPhone(phone);
                if (phone.contains(" ")) {
                    errMsg.add("手机号不能包含空格！");
                }
                if (phone.length() != 11) {
                    errMsg.add("手机号必须为11位！");
                }
                if (!phone.matches("^[0-9]{11}$")) {
                    errMsg.add("手机号只能包含数字！");
                }
            }
            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                vo.setErrMsg(errMsg.toString());
                vo.setCheckFlag(Boolean.FALSE);
            } else {
                vo.setCheckFlag(Boolean.TRUE);
            }
        });
        log.info("tocCheckDetailEnd");
        return saveList;
    }

    /**
     * 清空缓存
     *
     * @param cacheKey
     */
    @Override
    public void clearAllCache(String cacheKey) {
        clearCache(cacheKey);
    }

    /**
     * TOC汇总
     *
     * @param cacheKey
     */
    @Override
    public FeeCashTocVo findTocTotal(String cacheKey) {
        FeeCashTocVo toc = new FeeCashTocVo();
        List<FeeCashTocDto> tocList = findCacheList(cacheKey);
        if (CollectionUtils.isEmpty(tocList)) {
            return toc;
        }
        BigDecimal payeeAmountTotal = BigDecimal.ZERO;
        BigDecimal payAmountTotal = BigDecimal.ZERO;
        for (FeeCashTocDto e : tocList) {
            payeeAmountTotal = payeeAmountTotal.add(Optional.ofNullable(e.getPayeeAmount()).orElse(BigDecimal.ZERO));
            payAmountTotal = payAmountTotal.add(Optional.ofNullable(e.getPayAmount()).orElse(BigDecimal.ZERO));
        }
        toc.setPayeeAmountTotal(payeeAmountTotal);
        toc.setPayAmountTotal(payAmountTotal);
        return toc;
    }

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    @Override
    public Page<FeeCashTocVo> findByConditions(Pageable pageable, FeeCashTocVo dto) {
        ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new FeeCashTocVo();
        }
        Page<FeeCashTocVo> page = feeCashTocRepository.findByConditions(pageable, dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new Page<>();
        }
        Set<String> cashCodes = page.getRecords().stream().map(e -> e.getCashCode()).collect(Collectors.toSet());
        List<FeeCash> feeCashList = feeCashRepository.findByCodes(Lists.newArrayList(cashCodes));
        Map<String, FeeCash> feeCashMap = feeCashList.stream().collect(Collectors.toMap(FeeCash::getCashCode, Function.identity(), (e1, e2) -> e1));
        List<FeeCashDetailVo> cashDetailVos = feeCashDetailRepository.findByCodes(new ArrayList<>(cashCodes));
        Set<String> auditDetailCodes = cashDetailVos.stream().map(e -> e.getAuditDetailCode()).collect(Collectors.toSet());
        Map<String, List<String>> cashDetailMap = cashDetailVos.stream().collect(Collectors.groupingBy(FeeCashDetailVo::getCashCode, Collectors.mapping(FeeCashDetailVo::getAuditDetailCode, Collectors.toList())));
        List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findByAuditDetailCodes(new ArrayList<>(auditDetailCodes));
        page.getRecords().forEach(e -> {
            if (cashDetailMap.containsKey(e.getCashCode())) {
                List<MarketingAuditDetail> details = auditDetails.stream().filter(m -> cashDetailMap.get(e.getCashCode()).contains(m.getAuditDetailCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(details)) {
                    e.setYearMonthLy(String.join("/", details.stream().map(m -> m.getStartDate().substring(0, 7)).collect(Collectors.toSet())));
                }
            }
            if (feeCashMap.containsKey(e.getCashCode())) {
                FeeCash feeCash = feeCashMap.get(e.getCashCode());
                e.setPaySucessDate(feeCash.getPaySucessDate());
                e.setProcessKey(feeCash.getProcessKey());
            }
        });
        return page;
    }
}
