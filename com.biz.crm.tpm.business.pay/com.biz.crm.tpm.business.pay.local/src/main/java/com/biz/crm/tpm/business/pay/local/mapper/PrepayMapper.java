package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.Prepay;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDto;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 活动预付;(tpm_prepay_activities)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-22
 */
@Mapper
public interface PrepayMapper extends BaseMapper<Prepay>{
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto 动态查询条件
   * @return 分页对象列表
   */
  Page<PrepayVo> findByConditions(@Param("page") Page<PrepayVo> page , @Param("dto") PrepayDto dto);
}
