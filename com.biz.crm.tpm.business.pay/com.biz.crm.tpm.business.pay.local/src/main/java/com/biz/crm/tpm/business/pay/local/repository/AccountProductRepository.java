package com.biz.crm.tpm.business.pay.local.repository;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.mapper.AccountProductMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 费用上账商品表(TpmAccountProduct)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-16 19:11:50
 */
@Component
public class AccountProductRepository extends ServiceImpl<AccountProductMapper, AccountProduct> {

  @Autowired
  private AccountProductMapper accountProductMapper;

  /**
   * 分页查询数据
   *
   * @param pageable          分页对象
   * @param tpmAccountProduct 实体对象
   * @return
   */
  public Page<AccountProduct> findByConditions(Pageable pageable, AccountProduct tpmAccountProduct) {
    tpmAccountProduct.setTenantCode(TenantUtils.getTenantCode());
    Page<AccountProduct> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    Page<AccountProduct> pageList = accountProductMapper.findByConditions(page, tpmAccountProduct);
    return pageList;
  }

  /**
   * 通过编码查询
   * @param code
   * @return
   */
  public List<AccountProduct> findByCode(String code) {
    return this.lambdaQuery()
        .eq(AccountProduct::getAccountCode,code)
        .eq(AccountProduct::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }

  public void deleteBatchByCodes(List<String> codes) {
     this.lambdaUpdate()
         .in(AccountProduct::getAccountCode,codes)
         .eq(AccountProduct::getTenantCode,TenantUtils.getTenantCode())
         .remove();
  }

  public List<AccountProduct> findByCodes(List<String> codes) {
   return this.lambdaQuery()
        .in(AccountProduct::getAccountCode,codes)
        .eq(AccountProduct::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
        .eq(AccountProduct::getTenantCode,TenantUtils.getTenantCode())
        .list();

  }

  public AccountProduct findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(AccountProduct::getTenantCode,tenantCode)
        .in(AccountProduct::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(List<String> idList, String tenantCode) {
    this.lambdaUpdate()
        .eq(AccountProduct::getTenantCode,tenantCode)
        .in(AccountProduct::getId,idList)
        .remove();
  }
}

