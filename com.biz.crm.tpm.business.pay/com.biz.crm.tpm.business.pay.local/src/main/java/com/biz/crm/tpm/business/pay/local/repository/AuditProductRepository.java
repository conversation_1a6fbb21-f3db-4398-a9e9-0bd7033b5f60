package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.AuditProduct;
import com.biz.crm.tpm.business.pay.local.mapper.AuditProductMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditProductDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditProductVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 费用明细商品;(tpm_audit_product)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Component
public class AuditProductRepository extends ServiceImpl<AuditProductMapper, AuditProduct> {
  @Autowired
  private AuditProductMapper auditProductMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<AuditProductVo> findByConditions(Pageable pageable, AuditProductDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<AuditProductVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return auditProductMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<AuditProduct>
   */
  public List<AuditProduct> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(AuditProduct::getId, ids)
            .eq(AuditProduct::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据费用核销编号与租户编号获取对象
   *
   * @param auditCode
   * @return
   */
  public List<AuditProduct> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(AuditProduct::getAuditCode, auditCode)
            .eq(AuditProduct::getTenantCode, tenantCode).list();
  }

  /**
   * 根据费用核销明细编号与租户编号获取对象
   *
   * @param auditDetailCode
   * @return
   */
  public List<AuditProduct> findByAuditDetailCode(String auditDetailCode) {
    if (StringUtils.isBlank(auditDetailCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(AuditProduct::getAuditDetailCode, auditDetailCode)
            .eq(AuditProduct::getTenantCode, tenantCode).list();
  }

  /**
   * 根据费用核销编号删除数据
   *
   * @param auditCode
   * @return
   */
  public boolean removeByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(AuditProduct::getAuditCode, auditCode)
            .eq(AuditProduct::getTenantCode, tenantCode).remove();
  }

  /**
   * 根据费用核销编号删除数据
   *
   * @param auditDetailCode
   * @return
   */
  public boolean removeByAuditDetailCode(String auditDetailCode) {
    if (StringUtils.isBlank(auditDetailCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(AuditProduct::getAuditDetailCode, auditDetailCode)
            .eq(AuditProduct::getTenantCode, tenantCode).remove();
  }

  public AuditProduct findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(AuditProduct::getTenantCode,tenantCode)
        .in(AuditProduct::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(AuditProduct::getTenantCode,tenantCode)
        .in(AuditProduct::getId,ids)
        .remove();
  }
}