package com.biz.crm.tpm.business.pay.local.notifier;

import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.ReplenishmentPoolDetailClearEventDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.event.ReplenishmentPoolEventListener;
import com.biz.crm.tpm.business.pay.local.repository.MarketingAuditDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingWriteOffRepository;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffSendHecService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description 货补池关闭冲销
 * <AUTHOR>
 * @Date 2024/7/27 17:50
 */
@Service
public class ReplenishmentPoolEventListenerImpl implements ReplenishmentPoolEventListener {

    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;

    @Autowired(required = false)
    private WithHoldingWriteOffRepository withHoldingWriteOffRepository;

    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private WithHoldingWriteOffSendHecService withHoldingWriteOffSendHecService;

    @Override
    public void clear(ReplenishmentPoolDetailClearEventDto eventDto) {
//        Validate.notNull(eventDto, "预提冲销实体不能为空");
//        Validate.notEmpty(eventDto.getList(), "预提冲销集合不能为空");
//        List<WithHoldingWriteOffDto> writeOffdtoList = new ArrayList<>();
//        for (ReplenishmentPoolDetailClearDto dto : eventDto.getList()) {
//            Validate.notBlank(dto.getReplenishmentPoolType(), "货补费用池类型不能为空");
//            Validate.isTrue(StringUtils.equalsAny(dto.getReplenishmentPoolType(),
//                    ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode(), ReplenishmentPoolTypeEnum.TAIL_DIFF.getDictCode()), "只有货补池类型为“货补”或“尾差”才允许冲销");
//            Validate.notBlank(dto.getBusinessCode(), "预提冲销时业务编码不能为空");
//            Validate.notNull(dto.getClearAmount(), "预提冲销时冲销金额不能为空");
//            if (dto.getClearAmount().compareTo(BigDecimal.ZERO) <= 0) {
//                return;
//            }
//            String businessCode = dto.getActivityDetailCode();
//            //货补池通过结案明细上的活动，尾差通过业务编码匹配去查对应的计提
//            if (ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode().equals(dto.getReplenishmentPoolType()) &&
//                    ReplenishmentPoolOperationType.ACTIVITY_ACCOUNT.getCode().equals(dto.getOperationType())) {
//                Validate.notBlank(businessCode, "结案明细编码不能为空");
//                List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findByAuditDetailCodes(Arrays.asList(businessCode));
//                Validate.notEmpty(auditDetails, "未找到对应的结案明细");
//                businessCode = auditDetails.get(0).getSchemeDetailCode();
//            } else {
//                businessCode = dto.getParentPoolDetailCode();
//            }
//
//            WithHoldingWriteOffDto writeOffdto = new WithHoldingWriteOffDto();
//            writeOffdto.setActivitiesDetailCode(businessCode);
//            writeOffdto.setWriteOffType(WriteOffTypeEnum.CLOSE.getDictCode());
//            writeOffdto.setWriteOffAmount(dto.getClearAmount());
//            writeOffdto.setAuditCreateAccount(dto.getCreateAccount());
//            writeOffdto.setPositionCode(dto.getPositionCode());
//            writeOffdto.setSourceCode(dto.getParentPoolDetailCode());
//            writeOffdto.setBeDms(BooleanEnum.TRUE.getCapital());
//            writeOffdtoList.add(writeOffdto);
//        }
//        withHoldingWriteOffSendHecService.writeOff(writeOffdtoList);
    }
}
