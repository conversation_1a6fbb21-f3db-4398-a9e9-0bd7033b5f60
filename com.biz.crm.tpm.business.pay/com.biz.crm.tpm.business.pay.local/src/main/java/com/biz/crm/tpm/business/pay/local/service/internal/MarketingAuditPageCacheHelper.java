package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAuditDetail;
import com.biz.crm.tpm.business.pay.local.repository.MarketingAuditDetailRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.MarketingAuditConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 方案结案分页缓存
 */
@Slf4j
@Component
public class MarketingAuditPageCacheHelper extends BusinessPageCacheHelper<MarketingAuditDetailVo, MarketingAuditDetailDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return MarketingAuditConstant.CACHE_KEY_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<MarketingAuditDetailDto> getDtoClass() {
        return MarketingAuditDetailDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<MarketingAuditDetailVo> getVoClass() {
        return MarketingAuditDetailVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param marketingAuditDetailDto
     * @param cacheKey
     */
    @Override
    public List<MarketingAuditDetailDto> findDtoListFromRepository(MarketingAuditDetailDto marketingAuditDetailDto, String cacheKey) {
        if (StringUtils.isBlank(marketingAuditDetailDto.getAuditCode())) {
            return new ArrayList<>();
        }
        List<MarketingAuditDetailVo> marketingAuditDetailVos = marketingAuditDetailRepository.findByCode(marketingAuditDetailDto.getAuditCode());
        return CollectionUtils.isNotEmpty(marketingAuditDetailVos) ? (List<MarketingAuditDetailDto>) nebulaToolkitService.copyCollectionByBlankList(marketingAuditDetailVos, MarketingAuditDetailVo.class, MarketingAuditDetailDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<MarketingAuditDetailDto> newItem(String cacheKey, List<MarketingAuditDetailDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<MarketingAuditDetailDto> copyItem(String cacheKey, List<MarketingAuditDetailDto> itemList) {
        List<MarketingAuditDetailDto> newItemList = (List<MarketingAuditDetailDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, MarketingAuditDetailDto.class, MarketingAuditDetailDto.class, HashSet.class, ArrayList.class);
        for (MarketingAuditDetailDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 导入新增数据
     * @param cacheKey
     * @param itemList
     * @return
     */
    public void importNewItem(String cacheKey, List<MarketingAuditDetailDto> itemList) {
        String redisCacheDataKey = this.getRedisCacheDataKey(cacheKey);

        Map<Object, MarketingAuditDetailDto> updateMap = itemList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }

    /**
     * 获取Dto中的主键
     *
     * @param marketingAuditDetailDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(MarketingAuditDetailDto marketingAuditDetailDto) {
        return marketingAuditDetailDto.getSchemeDetailCode();
    }

    /**
     * 获取是否选中状态
     *
     * @param marketingAuditDetailDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(MarketingAuditDetailDto marketingAuditDetailDto) {
        return marketingAuditDetailDto.getChecked();
    }

}
