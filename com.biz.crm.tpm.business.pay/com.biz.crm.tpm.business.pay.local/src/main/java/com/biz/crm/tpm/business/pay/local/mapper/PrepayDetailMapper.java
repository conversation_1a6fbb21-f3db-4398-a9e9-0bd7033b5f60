package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.PrepayDetail;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 活动预付明细;(tpm_prepay_detail)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Mapper
public interface PrepayDetailMapper extends BaseMapper<PrepayDetail>{
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<PrepayDetailVo> findByConditions(@Param("page") Page<PrepayDetailVo> page , @Param("dto") PrepayDetailDto dto);
}