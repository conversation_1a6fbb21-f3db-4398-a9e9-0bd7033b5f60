package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe: 费用预提实体
 * @createTime 2022年06月25日 10:19:00
 */
@ApiModel(value = "tpm_with_holding", description = "费用预提")
@TableName("tpm_with_holding")
@Data
@Entity(name = "tpm_with_holding")
@org.hibernate.annotations.Table(appliesTo = "tpm_with_holding", comment = "费用预提")
@Table(name = "tpm_with_holding", indexes = {
        @Index(name = "tpm_with_holding_uq1", columnList = "with_holding_code",unique = true),
        @Index(name = "tpm_with_holding_idx2", columnList = "collect_code"),
        @Index(name = "tpm_with_holding_idx3", columnList = "budget_code,activities_detail_code"),
})
public class WithHolding extends TenantFlagOpEntity {

  /**
   * 推送状态 枚举
   */
  @ApiModelProperty(name = "推送费控状态", notes = "推送费控状态")
  @Column(name = "push_status",   columnDefinition = "VARCHAR(32) DEFAULT 'N' COMMENT '推送费控状态'")
  private String pushStatus;

  /**
   * 预提类型 枚举
   */
  @ApiModelProperty(name = "预提类型", notes = "预提类型")
  @Column(name = "with_holding_type",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '预提类型 '")
  private String withHoldingType;

  /**
   * 预提编号
   */
  @ApiModelProperty(name = "预提编号",notes = "预提编号")
  @Column(name = "with_holding_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预提编号 '")
  private String withHoldingCode;

  /**
   * 预提年月
   */
  @Deprecated
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM")
  @DateTimeFormat(pattern = "yyyy-MM")
  @Column(name = "with_holding_years" , length = 20, columnDefinition = "datetime COMMENT '预提年月'")
  private Date withHoldingYears;

  @ApiModelProperty("审批通过日期")
  @Column(name = "pass_date", columnDefinition = "datetime COMMENT '审批通过日期'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date passDate;

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  @Column(name = "activities_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  @Column(name = "activities_Name", length = 64, columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 活动明细编码
   */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value= "活动明细编码")
  @Column(name = "activities_detail_code", length = 64, columnDefinition = "VARCHAR(128) COMMENT '活动明细编码 '")
  private String activitiesDetailCode;

  /**
   * 预算科目编码
   */
  @ApiModelProperty(name = "预算科目编码",notes = "")
  @Column(name = "budget_subjects_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
  private String budgetSubjectsCode;

  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "预算科目名称",notes = "")
  @Column(name = "budget_subjects_name", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;

  /**
   * 活动大类编码
   */
  @ApiModelProperty(name = "活动大类编码", notes = "活动大类编码")
  @Column(name = "cost_type_category_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动大类编码 '")
  private String costTypeCategoryCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "活动大类名称", notes = "活动大类名称")
  @Column(name = "cost_type_category_name",  length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动大类名称 '")
  private String costTypeCategoryName;

  /**
   * 活动细类编码
   */
  @ApiModelProperty(name = "活动细类编码", notes = "活动细类编码")
  @Column(name = "cost_type_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动细类编码 '")
  private String costTypeDetailCode;

  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "活动细类名称", notes = "活动系类名称")
  @Column(name = "cost_type_detail_name",  length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动细类名称 '")
  private String costTypeDetailName;

  /**
   * 组织编号
   */
  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  @Column(name = "org_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编号 '")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  @Column(name = "org_name",length = 128, columnDefinition = "VARCHAR(128) COMMENT '组织名称 '")
  private String orgName;


  /**
   * 客户编号
   */
  @ApiModelProperty(name = "客户编号", notes = "客户编号")
  @Column(name = "customer_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '客户编号 '")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty(name = "客户名称", notes = "客户名称")
  @Column(name = "customer_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '客户名称 '")
  private String customerName;

  /**
   * 门店编号
   */
  @ApiModelProperty(name = "门店编号", notes = "门店编号")
  @Column(name = "terminal_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '门店编号 '")
  private String terminalCode;

  /**
   * 门店名称
   */
  @ApiModelProperty(name = "门店名称", notes = "门店名称")
  @Column(name = "terminal_name",  length = 128, columnDefinition = "VARCHAR(128) COMMENT '门店名称 '")
  private String terminalName;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "申请金额", notes = "申请金额")
  @Column(name = "apply_amount", length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '申请金额 '")
  private BigDecimal applyAmount;

  /**
   * 预提金额
   */
  @ApiModelProperty(name = "预提金额", notes = "预提金额")
  @Column(name = "with_holding_amount", length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '预提金额 '")
  private BigDecimal withHoldingAmount;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "支付方式", notes = "支付方式")
  @Column(name = "pay_by", length = 64, columnDefinition = "VARCHAR(64) COMMENT '支付方式 '")
  private String payBy;

  @ApiModelProperty("活动描述")
  @Column(name = "act_desc", columnDefinition = "varchar(500) comment '活动描述'")
  private String actDesc;



  @ApiModelProperty("费用归属年月")
  @Column(name = "years", columnDefinition = "varchar(20) comment '费用归属年月'")
  private String years;

  @ApiModelProperty("预提年月")
  @Column(name = "year_month_ly", columnDefinition = "varchar(20) comment '预提年月'")
  private String yearMonthLy;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
  private String erpCode;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
  private String channelCode;

  @ApiModelProperty("订单编码")
  @Column(name = "order_code", columnDefinition = "varchar(32) comment '订单编码'")
  private String orderCode;

  @ApiModelProperty("订单名称")
  @Column(name = "order_name", columnDefinition = "varchar(255) comment '订单名称'")
  private String orderName;

  @ApiModelProperty("归属部门编码")
  @Column(name = "belong_department_code", columnDefinition = "varchar(32) comment '归属部门编码'")
  private String belongDepartmentCode;

  @ApiModelProperty("归属部门名称")
  @Column(name = "belong_department_name", columnDefinition = "varchar(64) comment '归属部门名称'")
  private String belongDepartmentName;

  @ApiModelProperty("承担部门编码")
  @Column(name = "bear_department_code", columnDefinition = "varchar(32) comment '承担部门编码'")
  private String bearDepartmentCode;

  @ApiModelProperty("承担部门名称")
  @Column(name = "bear_department_name", columnDefinition = "varchar(64) comment '承担部门名称'")
  private String bearDepartmentName;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "varchar(64) comment '成本中心名称'")
  private String costCenterName;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("计提来源")
  @Column(name = "with_holding_source",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '计提来源 '")
  private String withHoldingSource;

  @ApiModelProperty("管报计提金额")
  @Column(name = "with_holding_report_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '管报计提金额 '")
  private BigDecimal withHoldingReportAmount;

  @ApiModelProperty("调减金额")
  @Column(name = "reduce_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '调减金额 '")
  private BigDecimal reduceAmount;

  @ApiModelProperty("实际金额")
  @Column(name = "actual_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '实际金额 '")
  private BigDecimal actualAmount;

  @ApiModelProperty("公式及取值")
  @Column(name = "formula_info", columnDefinition = "VARCHAR(255) COMMENT '公式及取值'")
  private String formulaInfo;

  @ApiModelProperty("凭证号")
  @Column(name = "voucher_code", length = 64, columnDefinition = " varchar(64) COMMENT '凭证号'")
  private String voucherCode;

  @ApiModelProperty("失败原因")
  @Column(name = "fail_msg", length = 500, columnDefinition = "varchar(500) COMMENT '失败原因'")
  private String failMsg;

  @ApiModelProperty("审批状态")
  @Column(name = "status",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '审批状态 '")
  private String status;

  @ApiModelProperty("确认状态")
  @Column(name = "confirm_status",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '确认状态 '")
  private String confirmStatus;

  @ApiModelProperty("预算编码")
  @Column(name = "budget_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '预算编码 '")
  private String budgetCode;

  @ApiModelProperty("费控单号")
  @Column(name = "external_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '费控单号 '")
  private String externalCode;

  @ApiModelProperty("业务编码")
  @Column(name = "business_code", length = 64, columnDefinition = "varchar(64) COMMENT '业务编码'")
  private String businessCode;

  @ApiModelProperty("管报计提调减金额")
  @Column(name = "with_holding_report_reduce_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '管报计提调减金额 '")
  private BigDecimal withHoldingReportReduceAmount;

  @ApiModelProperty("管报实际金额")
  @Column(name = "actual_report_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '管报实际金额 '")
  private BigDecimal actualReportAmount;

  @ApiModelProperty("管报实际金额（未税）")
  @Column(name = "no_tax_actual_report_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '管报实际金额(未税) '")
  private BigDecimal noTaxActualReportAmount;

  @ApiModelProperty("汇总单号")
  @Column(name = "collect_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '预算编码 '")
  private String collectCode;

  @ApiModelProperty("是否当期费用(Y/N)")
  @Column(name = "be_this_fee", length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否当期费用(Y/N) '")
  private String beThisFee;

  @ApiModelProperty("推送费控单号")
  @Column(name = "push_hec_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '推送费控单号 '")
  private String pushHecCode;

  @ApiModelProperty("是否进行过调整(Y/N)")
  @Column(name = "be_adjust", length = 10, columnDefinition = "VARCHAR(10) default 'N' COMMENT '是否进行过调整(Y/N) '")
  private String beAdjust;

  @ApiModelProperty("方案创建人名称")
  @Column(name = "scheme_create_name", length = 60, columnDefinition = "varchar(60) COMMENT '方案创建人名称'")
  private String schemeCreateName;

  @ApiModelProperty("费率")
  @Column(name = "ratio", columnDefinition = "decimal(20,6) comment '费率'")
  private BigDecimal ratio;

  @ApiModelProperty("费率")
  @Column(name = "ratio_str", columnDefinition = "varchar(8) COMMENT '比例'")
  private String ratioStr;

  @ApiModelProperty("计提前兑付")
  @Column(name = "cash_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '计提前兑付'")
  private BigDecimal cashAmount;
}
