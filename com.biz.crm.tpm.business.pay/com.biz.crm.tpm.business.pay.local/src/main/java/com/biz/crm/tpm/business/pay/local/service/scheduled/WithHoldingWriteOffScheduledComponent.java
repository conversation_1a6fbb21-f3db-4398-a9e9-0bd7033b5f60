package com.biz.crm.tpm.business.pay.local.service.scheduled;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingWriteOffRepository;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingDto;
import com.biz.crm.tpm.business.pay.sdk.enums.WithHoldingTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffService;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingWriteOffVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 费用冲销-手动冲销定时任务
 * <AUTHOR>
 * @Date 2024/8/3 14:19
 */
@Slf4j
@Component
public class WithHoldingWriteOffScheduledComponent {

    @Autowired(required = false)
    private WithHoldingService withHoldingService;
    @Autowired(required = false)
    private WithHoldingWriteOffRepository withHoldingWriteOffRepository;
    @Autowired(required = false)
    private WithHoldingWriteOffService withHoldingWriteOffService;

    public void handleManual() {
        WithHoldingDto dto = new WithHoldingDto();
        dto.setWithHoldingType(WithHoldingTypeEnum.HANDLE.getDictCode());
        dto.setManualWriteOff(BooleanEnum.TRUE.getCapital());
        Page<WithHoldingVo> pageOne = this.withHoldingService.findByConditions(PageRequest.of(1, 1), dto);

        Integer pageSize = CommonConstant.IE_EXPORT_MAX_PAGE_SIZE;
        int totalPage = (int) (pageOne.getSize() / pageSize + (pageOne.getSize() % pageSize == 0 ? 0 : 1));
        for (int i = 1; i <= totalPage; i++) {
            try {
                Page<WithHoldingVo> page = this.withHoldingService.findByConditions(PageRequest.of(i, pageSize), dto);
                List<WithHoldingVo> records = page.getRecords();
                List<WithHoldingWriteOffVo> uniqueList =
                        withHoldingWriteOffRepository.findByCodes(records.stream().map(WithHoldingVo::getWithHoldingCode).collect(Collectors.toList()));
                if (!CollectionUtils.isEmpty(uniqueList)) {
                    List<String> writeOffVoList = uniqueList.stream().map(WithHoldingWriteOffVo::getWithHoldingCode).collect(Collectors.toList());
                    records = records.stream().filter(v -> !writeOffVoList.contains(v.getWithHoldingCode())).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(records)) {
                    this.withHoldingWriteOffService.handleManualByEntities(records);
                }
            } catch (Exception e) {
                log.error(String.format("预提手动冲销定时任务错误：%s,", e.getMessage()), e);
            }

        }
    }

}
