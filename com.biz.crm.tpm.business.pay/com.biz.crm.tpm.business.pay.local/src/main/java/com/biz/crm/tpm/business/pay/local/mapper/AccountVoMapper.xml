<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AccountVoMapper">
  <resultMap id="AccountVoMap" type="com.biz.crm.tpm.business.pay.sdk.vo.AccountVo">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="createAccount" column="create_account" jdbcType="VARCHAR"/>
    <result property="createName" column="create_name" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="modifyAccount" column="modify_account" jdbcType="VARCHAR"/>
    <result property="modifyName" column="modify_name" jdbcType="VARCHAR"/>
    <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    <result property="enableStatus" column="enable_status" jdbcType="VARCHAR"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="accountCode" column="account_code" jdbcType="VARCHAR"/>
    <result property="activitiesCode" column="activities_code" jdbcType="VARCHAR"/>
    <result property="activitiesName" column="activities_name" jdbcType="VARCHAR"/>
    <result property="amount" column="amount" jdbcType="NUMERIC"/>
    <result property="auditCode" column="audit_code" jdbcType="VARCHAR"/>
    <result property="customerCode" column="customerCode" jdbcType="VARCHAR"/>
    <result property="customerName" column="customerName" jdbcType="VARCHAR"/>
    <result property="auditDetailCode" column="audit_detail_code" jdbcType="VARCHAR"/>
    <result property="availableAmount" column="available_amount" jdbcType="NUMERIC"/>
    <result property="beginTime" column="begin_time" jdbcType="TIMESTAMP"/>
    <result property="budgetSubjectsCode" column="budget_subjects_code" jdbcType="VARCHAR"/>
    <result property="budgetSubjectsName" column="budget_subjects_name" jdbcType="VARCHAR"/>
    <result property="costTypeCategoryCode" column="cost_type_category_code" jdbcType="VARCHAR"/>
    <result property="costTypeCategoryName" column="cost_type_category_name" jdbcType="VARCHAR"/>
    <result property="costTypeDetailCode" column="cost_type_detail_code" jdbcType="VARCHAR"/>
    <result property="costTypeDetailName" column="cost_type_detail_name" jdbcType="VARCHAR"/>
    <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
    <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
    <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
    <result property="payBy" column="pay_by" jdbcType="VARCHAR"/>
    <result property="productLevelCode" column="product_level_code" jdbcType="VARCHAR"/>
    <result property="productLevelName" column="product_level_name" jdbcType="VARCHAR"/>
    <result property="terminalCode" column="terminal_code" jdbcType="VARCHAR"/>
    <result property="terminalName" column="terminal_name" jdbcType="VARCHAR"/>
    <collection property="productList" ofType="com.biz.crm.tpm.business.pay.sdk.vo.AccountProductVo">
      <result property="id" column="productId" jdbcType="VARCHAR"/>
      <result property="createAccount" column="productCreateAccount" jdbcType="VARCHAR"/>
      <result property="createName" column="productCreateName" jdbcType="VARCHAR"/>
      <result property="createTime" column="productCreateTime" jdbcType="TIMESTAMP"/>
      <result property="modifyAccount" column="productModifyAccount" jdbcType="VARCHAR"/>
      <result property="modifyName" column="productModifyName" jdbcType="VARCHAR"/>
      <result property="modifyTime" column="productModifyTime" jdbcType="TIMESTAMP"/>
      <result property="tenantCode" column="productTenantCode" jdbcType="VARCHAR"/>
      <result property="accountCode" column="productAccountCode" jdbcType="VARCHAR"/>
      <result property="delFlag" column="productDelFlag" jdbcType="VARCHAR"/>
      <result property="enableStatus" column="productEnableStatus" jdbcType="VARCHAR"/>
      <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
      <result property="productName" column="product_name" jdbcType="VARCHAR"/>
      <result property="remark" column="productRemark" jdbcType="VARCHAR"/>
      <result property="remark" column="productRemark" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
  <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.AccountVo">
    select t.* from tpm_account t
    <where>
      t.del_flag = '${@<EMAIL>()}'
      and t.tenant_code = #{dto.tenantCode}
      <if test="dto.accountCode != null and dto.accountCode != '' ">
        <bind name="accountCode" value=" '%' + dto.accountCode + '%' "/>
        and t.account_code like #{accountCode}
      </if>
      <if test="dto.accountStatus != null and dto.accountStatus != '' ">
        and t.account_status = #{dto.accountStatus}
      </if>
      <if test="dto.activitiesDetailCode != null and dto.activitiesDetailCode != '' ">
        and t.activities_detail_code = #{dto.activitiesDetailCode}
      </if>
      <if test="dto.auditDetailCode != null and dto.auditDetailCode != '' ">
        <bind name="auditDetailCode" value=" '%' + dto.auditDetailCode + '%' "/>
        and t.audit_detail_code like #{auditDetailCode}
      </if>
    </where>
    order by t.create_time desc,t.id
  </select>
  <select id="findAllAccountAmount" resultType="com.biz.crm.tpm.business.pay.sdk.vo.AccountVo">
    select audit_detail_code, sum(amount) as amount, customer_code
    from tpm_account t
    where t.account_status = 'on'
      and t.del_flag = '${@<EMAIL>()}'
      and t.enable_status = '${@<EMAIL>()}'
      and t.tenant_code = #{dto.tenantCode}
    <if test="dto.auditCode != null and dto.auditCode != '' ">
      <bind name="likeAuditCode" value="'%' + dto.auditCode + '%'"/>
      and t.audit_code like #{likeAuditCode}
    </if>
    <if test="dto.auditDetailCode != null and dto.auditDetailCode != '' ">
      <bind name="likeAuditDetailCode" value="'%' + dto.auditDetailCode + '%'"/>
      and t.audit_detail_code like #{likeAuditDetailCode}
    </if>
    <if test="dto.activitiesCode != null and dto.activitiesCode != '' ">
      <bind name="likeActivitiesCode" value="'%' + dto.activitiesCode + '%'"/>
      and t.activities_code like #{likeActivitiesCode}
    </if>
    <if test="dto.auditDetailCodeList != null and dto.auditDetailCodeList.size > 0">
      and t.audit_detail_code in
      <foreach item="item" collection="dto.auditDetailCodeList" open="(" separator="," close=")" index="index">
        #{item}
      </foreach>
    </if>
    group by t.audit_detail_code, t.customer_code
  </select>
</mapper>

