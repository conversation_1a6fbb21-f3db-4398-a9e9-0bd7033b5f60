<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.InvoiceMapper">

    <resultMap id="invoiceVoMap" type="com.biz.crm.tpm.business.pay.sdk.vo.InvoiceVo"/>

    <select id="findByConditions" resultMap="invoiceVoMap">
        select t.* from tpm_invoice t
        where 1=1
        and t.del_flag = '${@<EMAIL>()}'
        <if test="dto.tenantCode != null and dto.tenantCode != '' ">
            and t.tenant_code = #{dto.tenantCode}
        </if>
        <if test="dto.type != null and dto.type != '' ">
            and t.type = #{dto.type}
        </if>
        <if test="dto.code != null and dto.code != '' ">
            <bind name="code" value="'%' + dto.code + '%'"/>
            and t.code like #{code}
        </if>
        <if test="dto.invoiceNo != null and dto.invoiceNo != '' ">
            <bind name="invoiceNo" value="'%' + dto.invoiceNo + '%'"/>
            and t.invoice_no like #{invoiceNo}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != '' ">
            and t.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.checked != null and dto.checked != ''">
            and t.checked = #{dto.checked}
        </if>
        <if test="dto.seller != null and dto.seller != '' ">
            <bind name="seller" value="'%' + dto.seller + '%'"/>
            and t.seller like #{seller}
        </if>
        <if test="dto.cashCode != null and dto.cashCode != '' ">
            <bind name="cashCode" value="'%' + dto.cashCode + '%'"/>
            and t.cash_code like #{cashCode}
        </if>
        <if test="dto.billingDate != null and dto.billingDate != ''">
            and t.billing_date = #{dto.billingDate}
        </if>
        <if test="dto.sNo != null and dto.sNo != '' ">
            <bind name="sNo" value="'%' + dto.sNo + '%'"/>
            and t.s_no like #{sNo}
        </if>
        <if test="dto.modifyName != null and dto.modifyName != '' ">
            <bind name="modifyName" value="'%' + dto.modifyName + '%'"/>
            and t.modify_name like #{modifyName}
        </if>
        order by t.create_time desc
    </select>
</mapper>

