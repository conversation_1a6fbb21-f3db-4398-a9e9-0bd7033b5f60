package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditCustomer;
import com.biz.crm.tpm.business.pay.local.repository.AuditCustomerRepository;
import com.biz.crm.tpm.business.pay.local.service.AuditCustomerService;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditCustomerDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditCustomerVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 费用明细客户;(tpm_audit_customer)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-27
 */
@Service("auditCustomerService")
public class AuditCustomerServiceImpl implements AuditCustomerService {
  @Autowired
  private AuditCustomerRepository auditCustomerRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<AuditCustomerVo> findByConditions(Pageable pageable, AuditCustomerDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new AuditCustomerDto();
    }
    return this.auditCustomerRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public AuditCustomerVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    AuditCustomer auditCustomer = this.auditCustomerRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (auditCustomer == null) {
      return null;
    }
    AuditCustomerVo auditCustomerVo = this.nebulaToolkitService.copyObjectByWhiteList(auditCustomer, AuditCustomerVo.class, LinkedHashSet.class, ArrayList.class);
    return auditCustomerVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<AuditCustomerVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<AuditCustomer> auditCustomers = this.auditCustomerRepository.findByIds(ids);
    Collection<AuditCustomerVo> auditCustomerVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditCustomers, AuditCustomer.class, AuditCustomerVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(auditCustomerVos);
  }

  /**
   * 根绝业务编号auditDetailCode获取业务数据
   *
   * @param auditDetailCode
   * @return 单条数据
   */
  @Override
  public List<AuditCustomerVo> findByAuditDetailCode(String auditDetailCode) {
    if (StringUtils.isBlank(auditDetailCode)) {
      return Collections.emptyList();
    }
    List<AuditCustomer> auditCustomers = this.auditCustomerRepository.findByAuditDetailCode(auditDetailCode);
    if (CollectionUtils.isEmpty(auditCustomers)) {
      return Collections.emptyList();
    }
    Collection<AuditCustomerVo> auditCustomerVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditCustomers, AuditCustomer.class, AuditCustomerVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(auditCustomerVos);
  }

  /**
   * 根绝业务编号auditDetailCode获取业务数据
   *
   * @param auditDetailCodes
   * @return 多条数据
   */
  @Override
  public List<AuditCustomerVo> findByAuditDetailCodes(Collection<String> auditDetailCodes) {
    if (CollectionUtils.isEmpty(auditDetailCodes)) {
      return Collections.emptyList();
    }
    List<AuditCustomer> auditCustomers = this.auditCustomerRepository.findByAuditDetailCodes(auditDetailCodes);
    if (CollectionUtils.isEmpty(auditCustomers)) {
      return Collections.emptyList();
    }
    Collection<AuditCustomerVo> auditCustomerVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditCustomers, AuditCustomer.class, AuditCustomerVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(auditCustomerVos);
  }

  /**
   * 新增数据
   *
   * @param auditCustomerDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AuditCustomerVo create(AuditCustomerDto auditCustomerDto) {

    this.createValidate(auditCustomerDto);
    AuditCustomer auditCustomer = this.nebulaToolkitService.copyObjectByWhiteList(auditCustomerDto, AuditCustomer.class, LinkedHashSet.class, ArrayList.class);
    auditCustomer.setTenantCode(TenantUtils.getTenantCode());
    this.auditCustomerRepository.saveOrUpdate(auditCustomer);
    AuditCustomerVo auditCustomerVo = this.nebulaToolkitService.copyObjectByWhiteList(auditCustomer, AuditCustomerVo.class, LinkedHashSet.class, ArrayList.class);

    auditCustomerVo.setId(auditCustomer.getId());
    return auditCustomerVo;
  }

  /**
   * 批量新增
   *
   * @param auditCustomerDtos
   * @return
   */
  @Transactional
  @Override
  public List<AuditCustomerVo> createBatch(Collection<AuditCustomerDto> auditCustomerDtos) {
    if (CollectionUtils.isEmpty(auditCustomerDtos)) {
      return Lists.newArrayList();
    }
    List<AuditCustomerVo> auditCustomerVos = Lists.newArrayList();
    for (AuditCustomerDto auditCustomerDto : auditCustomerDtos) {
      AuditCustomerVo auditCustomerVo = this.create(auditCustomerDto);
      auditCustomerVos.add(auditCustomerVo);
    }
    return auditCustomerVos;
  }

  /**
   * 修改新据
   *
   * @param auditCustomerDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public AuditCustomerVo update(AuditCustomerDto auditCustomerDto) {
    this.updateValidate(auditCustomerDto);
    AuditCustomer auditCustomer = this.auditCustomerRepository.findByIdAndTenantCode(auditCustomerDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(auditCustomer, "修改数据不存在，请检查！");
    auditCustomer.setCustomerCode(auditCustomerDto.getCustomerCode());
    auditCustomer.setCustomerName(auditCustomerDto.getCustomerName());
    auditCustomer.setAuditAmount(auditCustomerDto.getAuditAmount());
    auditCustomer.setTenantCode(TenantUtils.getTenantCode());
    this.auditCustomerRepository.saveOrUpdate(auditCustomer);
    AuditCustomerVo auditCustomerVo = this.nebulaToolkitService.copyObjectByWhiteList(auditCustomer, AuditCustomerVo.class, LinkedHashSet.class, ArrayList.class);

    return auditCustomerVo;
  }

  /**
   * 批量修改据
   *
   * @param auditCustomerDtos 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public List<AuditCustomerVo> updateBatch(Collection<AuditCustomerDto> auditCustomerDtos) {
    if (CollectionUtils.isEmpty(auditCustomerDtos)) {
      return Collections.emptyList();
    }
    String code = auditCustomerDtos.stream().findFirst().get().getAuditDetailCode();
    List<AuditCustomerVo> dbAuditCustomerVos = this.findByAuditDetailCode(code);
    Set<String> dbIds = dbAuditCustomerVos.stream().map(AuditCustomerVo::getId).collect(Collectors.toSet());
    Set<String> currentIds = auditCustomerDtos.stream().map(AuditCustomerDto::getId).collect(Collectors.toSet());
    Set<String> delData = Sets.difference(dbIds, currentIds);
    if (!CollectionUtils.isEmpty(delData)) {
      this.delete(delData);
    }
    List<AuditCustomerDto> addDtos = auditCustomerDtos.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
    List<AuditCustomerDto> updateDtos = auditCustomerDtos.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
    List<AuditCustomerVo> auditCustomerVos = this.createBatch(addDtos);
    if (!CollectionUtils.isEmpty(updateDtos)) {
      updateDtos.forEach(item -> {
        auditCustomerVos.add(this.update(item));
      });
    }
    return auditCustomerVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<AuditCustomer> auditCustomers = this.auditCustomerRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(auditCustomers)) {
      return;
    }
    Set<String> delIds = auditCustomers.stream().map(AuditCustomer::getId).collect(Collectors.toSet());
    this.auditCustomerRepository.removeByIdsAndTenantCode(delIds,TenantUtils.getTenantCode());
  }

  @Override
  public void deleteByAuditCode(String auditCode) {
    this.auditCustomerRepository.deleteByAuditCode(auditCode);
  }

  /**
   * 创建验证
   *
   * @param auditCustomerDto
   */
  private void createValidate(AuditCustomerDto auditCustomerDto) {
    Validate.notNull(auditCustomerDto, "新增时，对象信息不能为空！");
    Validate.isTrue(auditCustomerDto.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(auditCustomerDto.getAuditCode(), "新增数据时，费用核销编号不能为空！");
    Validate.notBlank(auditCustomerDto.getAuditDetailCode(), "新增数据时，费用核销明细编号不能为空！");
    if (StringUtils.isBlank(auditCustomerDto.getCustomerCode())) {
      auditCustomerDto.setCustomerCode("admin");
    }
    if (StringUtils.isBlank(auditCustomerDto.getCustomerName())) {
      auditCustomerDto.setCustomerName("暂时设置超级管理员，后面会排查这个bug");
    }
    Validate.notBlank(auditCustomerDto.getCustomerCode(), "新增数据时，客户编码不能为空！");
    Validate.notBlank(auditCustomerDto.getCustomerName(), "新增数据时，客户名称不能为空！");
    Validate.notNull(auditCustomerDto.getAuditAmount(), "新增数据时，核销金额不能为空！");
    Validate.isTrue(auditCustomerDto.getAuditAmount().compareTo(BigDecimal.ZERO) > 0, "新增数据时，核销金额不能为小于或等于0！");
  }

  /**
   * 修改验证
   *
   * @param auditCustomerDto
   */
  private void updateValidate(AuditCustomerDto auditCustomerDto) {
    Validate.notNull(auditCustomerDto, "修改时，对象信息不能为空！");
    Validate.notBlank(auditCustomerDto.getId(), "修改数据时，主键不能为空！");
    if (StringUtils.isBlank(auditCustomerDto.getCustomerCode())) {
      auditCustomerDto.setCustomerCode("admin");
    }
    if (StringUtils.isBlank(auditCustomerDto.getCustomerName())) {
      auditCustomerDto.setCustomerName("暂时设置超级管理员，后面会排查这个bug");
    }
    Validate.notBlank(auditCustomerDto.getCustomerCode(), "修改数据时，客户编码不能为空！");
    Validate.notBlank(auditCustomerDto.getCustomerName(), "修改数据时，客户名称不能为空！");
    Validate.notNull(auditCustomerDto.getAuditAmount(), "修改数据时，核销金额不能为空！");
    Validate.isTrue(auditCustomerDto.getAuditAmount().compareTo(BigDecimal.ZERO) > 0, "修改数据时，核销金额不能为小于或等于0！");

  }

}
