package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.tpm.business.pay.sdk.constant.WithHoldingConstant;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingLockVoService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class WithHoldingLockVoServiceImpl implements WithHoldingLockVoService {

    @Autowired(required = false)
    private RedisLockService redisLockService;

    /**
     * 根据计提编码加锁
     *
     * @param withHoldingCode
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 21:03
     **/
    @Override
    public boolean lock(String withHoldingCode, TimeUnit timeUnit, int time) {
        if (StringUtils.isEmpty(withHoldingCode)) {
            throw new RuntimeException("计提加锁失败，计提编码不能为空");
        }
        if (ObjectUtils.isEmpty(timeUnit)) {
            timeUnit = TimeUnit.MILLISECONDS;
        }
        if (time <= 0) {
            time = WithHoldingConstant.DEFAULT_LOCK_TIME;
        }
        return this.redisLockService.tryLock(WithHoldingConstant.WITH_HOLDING_LOCK + withHoldingCode, timeUnit, time);
    }

    /**
     * 根据计提编码批量加锁
     *
     * @param withHoldingCodeList
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public boolean lock(List<String> withHoldingCodeList, TimeUnit timeUnit, int time) {
        return this.lock(withHoldingCodeList, timeUnit, time, WithHoldingConstant.DEFAULT_WAITE_TIME_FIVE);
    }


    /**
     * 根据计提编码批量加锁
     *
     * @param withHoldingCodeList
     * @param timeUnit
     * @param lockTime
     * @param waiteTime
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public boolean lock(List<String> withHoldingCodeList, TimeUnit timeUnit, int lockTime, int waiteTime) {
        if (CollectionUtils.isEmpty(withHoldingCodeList)) {
            throw new RuntimeException("计提加锁失败，计提编码不能为空");
        }
        if (ObjectUtils.isEmpty(timeUnit)) {
            timeUnit = TimeUnit.MILLISECONDS;
        }
        if (lockTime <= 0) {
            lockTime = WithHoldingConstant.DEFAULT_LOCK_TIME;
        }
        if (waiteTime <= 0) {
            waiteTime = WithHoldingConstant.DEFAULT_WAITE_TIME_FIVE;
        }

        boolean isLock = true;
        List<String> successKeys = new ArrayList<>();
        try {
            // 循环加锁，并记录成功的key
            for (String withHoldingCode : withHoldingCodeList) {
                isLock = this.redisLockService.tryLock(WithHoldingConstant.WITH_HOLDING_LOCK + withHoldingCode, timeUnit, lockTime, waiteTime);
                if (!isLock) {
                    return false;
                }
                successKeys.add(withHoldingCode);
            }
        } finally {
            // 存在加锁失败的情况，则先将成功的解锁
            if (!isLock && !CollectionUtils.isEmpty(successKeys)) {
                successKeys.forEach(key -> {
                    redisLockService.unlock(WithHoldingConstant.WITH_HOLDING_LOCK + key);
                });
            }
        }
        return true;
    }

    /**
     * 根据计提编码解锁
     *
     * @param withHoldingCode
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public void unLock(String withHoldingCode) {
        if (StringUtils.isEmpty(withHoldingCode)) {
            throw new RuntimeException("计提解锁失败，计提编码不能为空");
        }
        redisLockService.unlock(WithHoldingConstant.WITH_HOLDING_LOCK + withHoldingCode);
    }

    /**
     * 根据计提编码批量解锁
     *
     * @param withHoldingCodeList
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public void unLock(List<String> withHoldingCodeList) {
        if (CollectionUtils.isEmpty(withHoldingCodeList)) {
            throw new RuntimeException("计提解锁失败，计提编码不能为空");
        }
        withHoldingCodeList.forEach(withHoldingCode -> {
            redisLockService.unlock(WithHoldingConstant.WITH_HOLDING_LOCK + withHoldingCode);
        });
    }
}
