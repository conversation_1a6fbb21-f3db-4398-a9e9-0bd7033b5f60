package com.biz.crm.tpm.business.pay.local.service;

import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayDetailVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * 活动预付明细;(tpm_prepay_detail)表服务接口
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
public interface PrepayDetailService{
  
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<PrepayDetailVo> findByConditions(Pageable pageable, PrepayDetailDto dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  PrepayDetailVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<PrepayDetailVo> findByIds(Collection<String> ids);

  /**
   * 根绝业务编号prepayCode获取业务数据
   *
   * @param prepayCode
   * @return 多条数据
   */
  List<PrepayDetailVo> findByPrepayCode(String prepayCode);
  /**
   * 新增数据
   *
   * @param prepayDetailDto 实体对象
   * @return 新增结果
   */
  PrepayDetailVo create(PrepayDetailDto prepayDetailDto);
  /**
   * 批量新增
   * @param prepayDetailDtos
   * @return
   */
  List<PrepayDetailVo> createBatch(Collection<PrepayDetailDto> prepayDetailDtos);
  /**
   * 修改数据
   *
   * @param prepayDetailDto 实体对象
   * @return 修改结果
   */
  PrepayDetailVo update(PrepayDetailDto prepayDetailDto);
  /**
   * 批量修改据
   *
   * @param prepayDetailDtos 实体对象
   * @return 修改结果
   */
  List<PrepayDetailVo> updateBatch(Collection<PrepayDetailDto> prepayDetailDtos);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
    
}