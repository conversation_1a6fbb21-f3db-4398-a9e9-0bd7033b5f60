package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.tpm.business.pay.local.repository.PrepayBillRecordRepository;
import com.biz.crm.tpm.business.pay.local.service.PrepayBillRecordService;
import com.biz.crm.tpm.business.pay.local.dto.PrepayBillRecordDto;
import com.biz.crm.tpm.business.pay.local.entity.PrepayBillRecord;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.LinkedHashSet;

/**
 * 预付账单明细记录;(tpm_prepay_bill_record)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Service("prepayBillRecordService")
public class PrepayBillRecordServiceImpl implements PrepayBillRecordService {
  @Autowired
  private PrepayBillRecordRepository prepayBillRecordRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 新增数据
   *
   * @param prepayBillRecordDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public PrepayBillRecord create(PrepayBillRecordDto prepayBillRecordDto) {
    this.createValidate(prepayBillRecordDto);
    PrepayBillRecord prepayBillRecord = this.nebulaToolkitService.copyObjectByWhiteList(prepayBillRecordDto, PrepayBillRecord.class, LinkedHashSet.class, ArrayList.class);
    prepayBillRecord.setTenantCode(TenantUtils.getTenantCode());
    this.prepayBillRecordRepository.saveOrUpdate(prepayBillRecord);
    return prepayBillRecord;
  }

  /**
   * 创建验证
   *
   * @param prepayBillRecordDto
   */
  private void createValidate(PrepayBillRecordDto prepayBillRecordDto) {
    Validate.notNull(prepayBillRecordDto, "新增时，对象信息不能为空！");
    Validate.isTrue(prepayBillRecordDto.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(prepayBillRecordDto.getActivitiesDetailCode(), "新增数据时，活动明细编码不能为空！");
    Validate.notNull(prepayBillRecordDto.getChangeAmount(), "新增数据时，申请金额不能为空！");
    Validate.notNull(prepayBillRecordDto.getType(), "新增数据时，操作类型不能为空！");
  }

}
