package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.Account;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AccountVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月18日 16:19:00
 */

public interface AccountVoMapper extends BaseMapper<AccountVo> {


  Page<AccountVo> findByConditions(Page<AccountVo> page, @Param("dto") AccountDto dto);

  List<AccountVo> findAllAccountAmount(@Param("dto")AccountDto account);
}
