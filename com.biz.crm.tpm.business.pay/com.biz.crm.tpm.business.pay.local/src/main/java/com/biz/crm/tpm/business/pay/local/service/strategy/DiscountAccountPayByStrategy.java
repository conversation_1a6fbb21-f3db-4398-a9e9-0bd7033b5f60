package com.biz.crm.tpm.business.pay.local.service.strategy;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.dms.business.costpool.discount.sdk.dto.CostPoolDiscountDto;
import com.biz.crm.dms.business.costpool.discount.sdk.enums.PoolGroupEnum;
import com.biz.crm.dms.business.costpool.discount.sdk.enums.PoolOperationTypeEnum;
import com.biz.crm.dms.business.costpool.discount.sdk.enums.PoolPayTypeEnum;
import com.biz.crm.dms.business.costpool.discount.sdk.enums.PoolUseTypeEnum;
import com.biz.crm.dms.business.costpool.discount.sdk.service.CostPoolDiscountVoService;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.budget.sdk.strategy.account.AccountPayByStrategy;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe: 折扣支付
 * @createTime 2022年06月21日 15:15:00
 */
public class DiscountAccountPayByStrategy implements AccountPayByStrategy {

  @Autowired(required = false)
  private CostPoolDiscountVoService costPoolDiscountVoService;
  /**
   * 成功代码
   */
  private final String SUCCESSFUL_CODE = "200";

  @Override
  public void execute(Object o) {
    AccountDto accountDto = (AccountDto) o;
    Validate.notNull(accountDto, "对象不能为空!");
    BigDecimal amount = accountDto.getAmount();
    Validate.notNull(amount,"上账金额不能为空");
    Validate.isTrue(amount.compareTo(BigDecimal.ZERO)>0,"上账金额必须大于0");
    String payBy = accountDto.getPayBy();
    Validate.notBlank(accountDto.getCustomerCode(), "向折扣池上账时，缺失客户编码");
    CostPoolDiscountDto dto = new CostPoolDiscountDto();
    dto.setPoolGroup(PoolGroupEnum.DEFAULT.getDictCode());
    dto.setPayType(PoolPayTypeEnum.DISCOUNT.getDictCode());
    dto.setUseType(PoolUseTypeEnum.DEFAULT.getDictCode());
    dto.setOperationType(PoolOperationTypeEnum.ACT_ACCOUNT.getDictCode());
    dto.setCustomerCode(accountDto.getCustomerCode());
    dto.setCustomerName(accountDto.getCustomerName());
    dto.setAmount(accountDto.getAmount());
    Result<?> result = costPoolDiscountVoService.handleAdjust(dto);
    Validate.notNull(result, "调用折扣池上账失败["+"无法连接到DMS]");
    Validate.isTrue(result.getCode().toString().equals(SUCCESSFUL_CODE), "折扣池上账失败["+result.getMessage()+"]");
  }
}
