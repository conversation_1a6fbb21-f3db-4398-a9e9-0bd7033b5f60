package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditInvoice;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditInvoiceVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 费用核销发票;(tpm_audit_invoice)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-7-6
 */
@Mapper
public interface AuditInvoiceMapper extends BaseMapper<AuditInvoice> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto 动态查询条件
   * @return 分页对象列表
   */
  Page<AuditInvoiceVo> findByConditions(@Param("page") Page<AuditInvoiceVo> page , @Param("dto") AuditInvoiceDto dto);

}
