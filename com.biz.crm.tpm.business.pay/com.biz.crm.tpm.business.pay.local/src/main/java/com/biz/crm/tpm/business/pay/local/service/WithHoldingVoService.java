package com.biz.crm.tpm.business.pay.local.service;

import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月27日 15:53:00
 */
public interface WithHoldingVoService {
  /**
   * 查询活动信息
   *
   * @param codes
   * @return
   */
  List<WithHoldingVo> findActivitiesByConditions(List<String> codes);


  /**
   * 通过id查询
   * @param id
   * @return
   */
  WithHoldingVo findById(String id);

  /**
   * 根据活动明细编码查询计提金额
   *
   * @param codes
   * @return
   */
  Map<String, BigDecimal> findBySchemeDetailCodes(List<String> codes);

  /**
   * 根据活动明细编码查询计提金额
   *
   * @param codes
   * @return
   */
  Map<String, BigDecimal> findByBusinessCodes(List<String> codes);

  List<WithHoldingVo> findByCollectCode(String collectCode);
}
