package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.tpm.business.pay.local.entity.FeeCash;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashRepository;
import com.biz.crm.tpm.business.pay.sdk.enums.CashTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.FeeCashFieldsEnum;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashOaService;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.FeeCashMainDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.function.BiConsumer;

@Service
@RefreshScope
public class FeeCashOaServiceImpl implements FeeCashOaService {

    @Autowired(required = false)
    private FeeCashRepository feeCashRepository;
    @Autowired(required = false)
    private PushOaService pushOaService;
    @Autowired
    private UserVoService userVoService;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Value("${domain-name:}")
    private String domainName;
    @Autowired(required = false)
    private LoginUserService loginUserService;

    /**
     * 推送OA
     *
     * @param code
     * @return
     */
    @Override
    public JSONObject pushOa(String code) {
        FeeCash order = feeCashRepository.findByCode(code);
        order.setBusinessCode(order.getCashCode());
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(order));

        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        orderJsonObject.put("title", order.getCashName()+ "-" + userVo.getFullName() + "-" + DateUtil.format(new Date(), "yyyy-MM-dd"));

        // 主表
        // 业务类型
        String businessType = MqConstant.TAG_TPM_FEE_CASH;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        String urlCode;
        //费用兑付
        if (CashTypeEnum.FEE.getDictCode().equals(order.getCashType())) {
            urlCode = TpmOaPageEnum.FEE_REDEMPTION_FORM.getUrlCode();
            //电汇预付
        } else if (CashTypeEnum.WIRE.getDictCode().equals(order.getCashType())) {
            urlCode = TpmOaPageEnum.FEE_REDEMPTION_ONLINEFORM.getUrlCode();
            //账扣预付
        } else {
            urlCode = TpmOaPageEnum.FEE_REDEMPTION_ACCOUNTFORM.getUrlCode();
        }
        orderJsonObject.put("tpmUrl", domainName + urlCode + "?code=" + order.getCashCode());
        JSONObject response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, null, workflowId, workflowName,
                mainTableMethod, null);

        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        if (response.containsKey("code")) {
            Integer resultCode = response.getInteger("code");
            if (resultCode == 100) {
                JSONObject ja = response.getJSONObject("data");
                FeeCash entity = feeCashRepository.findByCode(code);
                entity.setProcessKey(ja.getString("out"));
                entity.setProcessDate(new Date());
                entity.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
                entity.setOaId(response.getString("oaId"));
                entity.setOaUserName(response.getString("oaUserName"));
                feeCashRepository.updateById(entity);
            }else{
                throw new RuntimeException("OA流程提交失败，错误信息：" + response.getString("msg"));
            }
        } else {
            Validate.isTrue(false, "OA流程提交失败，错误信息：" + response.getString("msg"));
        }
        return response;
    }

    /**
     * 重新提交OA
     *
     * @param code@return
     */
    @Override
    public JSONObject resubmitOa(String code) {
        FeeCash order = feeCashRepository.findByCode(code);
        order.setBusinessCode(order.getCashCode());
        OaResubmitDto dto = new OaResubmitDto();

        // 获取当前登录人信息
        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);

        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType = MqConstant.TAG_TPM_FEE_CASH;
        String urlCode;
        //费用兑付
        if (CashTypeEnum.FEE.getDictCode().equals(order.getCashType())) {
            urlCode = TpmOaPageEnum.FEE_REDEMPTION_FORM.getUrlCode();
            //电汇预付
        } else if (CashTypeEnum.WIRE.getDictCode().equals(order.getCashType())) {
            urlCode = TpmOaPageEnum.FEE_REDEMPTION_ONLINEFORM.getUrlCode();
            //账扣预付
        } else {
            urlCode = TpmOaPageEnum.FEE_REDEMPTION_ACCOUNTFORM.getUrlCode();
        }
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        order.setTpmUrl(domainName + domainName + urlCode + "?code=" + order.getCashCode());

        dto.setBusinessCode(businessType);
        dto.setRequestId(order.getProcessKey());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        FeeCashMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(order, FeeCashMainDto.class, LinkedHashSet.class, ArrayList.class);
        dto.setRequestName(order.getCashName()+ "-" + userVo.getFullName() + "-" + DateUtil.format(new Date(), "yyyy-MM-dd"));
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));

        if (ryOaProcessService.resubmit(dto)) {
            FeeCash entity = feeCashRepository.findByCode(code);
            entity.setProcessDate(new Date());
            entity.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
            feeCashRepository.updateById(entity);
        }
        return null;
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public boolean oaWithdraw(String code, String remark) {
        FeeCash order = feeCashRepository.findByCode(code);
        Validate.isTrue(order.getStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(order.getProcessKey()));
        dto.setProcessCreateId(order.getOaId());
        dto.setUserName(order.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (FeeCashFieldsEnum value : FeeCashFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };
}
