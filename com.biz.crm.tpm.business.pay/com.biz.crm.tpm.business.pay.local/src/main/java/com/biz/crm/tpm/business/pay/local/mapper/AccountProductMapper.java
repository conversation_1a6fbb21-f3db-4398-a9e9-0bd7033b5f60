package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import org.apache.ibatis.annotations.Param;

/**
 * 费用上账商品表(TpmAccountProduct)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-06-16 19:11:50
 */
public interface AccountProductMapper extends BaseMapper<AccountProduct> {

  /**
   * 分页查询所有数据
   *
   * @param page              分页对象
   * @param tpmAccountProduct 查询实体
   * @return 所有数据
   */
  public Page<AccountProduct> findByConditions(@Param("page") Page<AccountProduct> page, @Param("entity") AccountProduct tpmAccountProduct);
}

