package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.ManageReport;
import com.biz.crm.tpm.business.pay.sdk.dto.ManageReportDto;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo;
import org.apache.ibatis.annotations.Param;

import java.time.YearMonth;
import java.util.List;

/**
 * 管报收入(ManageReport)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-22 14:57:23
 */
public interface ManageReportMapper extends BaseMapper<ManageReport> {

    List<ManageReportVo> findByCondition(@Param("dto") ManageReportDto dto);

    List<ManageReportVo> findListByCondition(@Param("dto") WithholdingIncomeQueryDto dto);

    List<ManageReportVo> findByYearAndCustomerCodes(@Param("yearMonth") String startEndMonth, @Param("customerCodes") List<String> customerCodes);
}

