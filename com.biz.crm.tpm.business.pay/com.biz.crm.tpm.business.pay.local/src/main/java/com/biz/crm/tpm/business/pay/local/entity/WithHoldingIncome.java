package com.biz.crm.tpm.business.pay.local.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;

@ApiModel(value = "tpm_with_holding_income", description = "管报收入")
@TableName("tpm_with_holding_income")
@Data
@Entity(name = "tpm_with_holding_income")
@org.hibernate.annotations.Table(appliesTo = "tpm_with_holding_income", comment = "管报收入")
@Table(name = "tpm_with_holding_income", indexes = {
        @Index(name = "index1", columnList = "years"),
        @Index(name = "index2", columnList = "org_code"),
        @Index(name = "index3", columnList = "customer_code"),
})
public class WithHoldingIncome {

    @Id
    @TableId(type = IdType.AUTO)
    @Column(name = "id", nullable = false, columnDefinition = "bigint(11) COMMENT 'id'")
    private Long id;

    @Column(name = "years",  length = 7, columnDefinition = "VARCHAR(7) COMMENT '年月'")
    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code",length = 100, columnDefinition = "VARCHAR(100) COMMENT '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name",length = 100, columnDefinition = "VARCHAR(100) COMMENT '客户名称'")
    private String customerName;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code",length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_name",length = 100, columnDefinition = "VARCHAR(100) COMMENT '组织名称'")
    private String orgName;

    @ApiModelProperty("产品编码")
    @Column(name = "product_code",length = 64, columnDefinition = "VARCHAR(64) COMMENT '产品编码'")
    private String productCode;

    @ApiModelProperty("产品名称")
    @Column(name = "product_name",length = 100, columnDefinition = "VARCHAR(100) COMMENT '产品名称'")
    private String productName;

    @ApiModelProperty("管报收入")
    @Column(name = "report_income",length = 10, columnDefinition = "decimal(10,2) COMMENT '管报收入'")
    private BigDecimal reportIncome;

}
