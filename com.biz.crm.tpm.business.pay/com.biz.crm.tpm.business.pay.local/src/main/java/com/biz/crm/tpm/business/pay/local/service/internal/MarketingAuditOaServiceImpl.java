package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.sdk.enums.CostCenterFieldsEnum;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAudit;
import com.biz.crm.tpm.business.pay.local.repository.MarketingAuditDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.MarketingAuditRepository;
import com.biz.crm.tpm.business.pay.sdk.enums.MarketingAuditDetailFieldsEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.MarketingAuditFieldsEnum;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditOaService;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.AuditDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.AuditMainDto;
import com.biz.crm.workflow.sdk.dto.oa.order.CostCenterDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class MarketingAuditOaServiceImpl implements MarketingAuditOaService {

    @Autowired(required = false)
    private MarketingAuditRepository marketingAuditRepository;
    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;
    @Autowired(required = false)
    private PushOaService pushOaService;
    @Autowired
    private UserVoService userVoService;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Value("${domain-name:}")
    private String domainName;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;

    /**
     * 推送OA
     *
     * @param code
     * @return
     */
    @Override
    public JSONObject pushOa(String code) {
        MarketingAudit order = marketingAuditRepository.findByCode(code);
        order.setBusinessCode(order.getAuditCode());
        List<MarketingAuditDetailVo> detailVoList = marketingAuditDetailRepository.findByCode(code);
        detailVoList.forEach(item -> {
            if (BooleanEnum.FALSE.getCapital().equals(item.getBeFullAudit())) {
                item.setBeFullAuditStr(BooleanEnum.FALSE.getSure());
            }else if (BooleanEnum.TRUE.getCapital().equals(item.getBeFullAudit())) {
                item.setBeFullAuditStr(BooleanEnum.TRUE.getSure());
            }
        });
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(order));
        Set<String> orgCodeSet = detailVoList.stream().filter(e -> StringUtils.isNotBlank(e.getBearDepartmentCode())).map(e -> e.getBearDepartmentCode()).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeSet));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            orderJsonObject.put("deptCode", oaOrgCodeSet.stream().collect(Collectors.joining(",")));
        }

        orderJsonObject.put("title", "营销方案结案：" + order.getAuditName());

        // 主表
        // 业务类型
        String businessType = MqConstant.TAG_TPM_AUDIT;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.PLAN_CLOSURE_FORM.getUrlCode() + "?code=" + order.getAuditCode());

        List<JSONArray> details = Lists.newArrayList();
        details.add(JSONArray.parseArray(JSON.toJSONString(JSONArray.parseArray(JSON.toJSONString(detailVoList)))));

        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        detailVoList.stream().filter(k-> StringUtils.isNotBlank(k.getCostCenterCode()))
                .collect(Collectors.groupingBy(MarketingAuditDetailVo::getCostCenterCode, Collectors.toList())).forEach((key, list)->{
                    CostCenterDetailDto detailDto = new CostCenterDetailDto();
                    detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
                    detailDto.setCostCenterName(list.get(0).getCostCenterName());
                    detailDto.setBearAmount(list.stream().map(MarketingAuditDetailVo::getAuditAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                    costCenterDetailDtoList.add(detailDto);
                });
        details.add(JSONArray.parseArray(JSON.toJSONString(JSONArray.parseArray(JSON.toJSONString(costCenterDetailDtoList)))));

        JSONObject response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, details, workflowId, workflowName,
                mainTableMethod, detailTableMethod,detailTableMethod1);

        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        if (response.containsKey("code")) {
            Integer resultCode = response.getInteger("code");
            if (resultCode == 100) {
                JSONObject ja = response.getJSONObject("data");
                MarketingAudit entity = marketingAuditRepository.findByCode(code);
                entity.setProcessNumber(ja.getString("out"));
                entity.setProcessDate(new Date());
                entity.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
                entity.setOaId(response.getString("oaId"));
                entity.setOaUserName(response.getString("oaUserName"));
                marketingAuditRepository.updateById(entity);
            }
        } else {
            Validate.isTrue(false, "OA流程提交失败，错误信息：" + response.getString("msg"));
        }

        return response;
    }

    /**
     * 重新提交OA
     *
     * @param code@return
     */
    @Override
    public JSONObject resubmitOa(String code) {
        MarketingAudit order = marketingAuditRepository.findByCode(code);
        order.setBusinessCode(order.getAuditCode());
        List<MarketingAuditDetailVo> detailVoList = marketingAuditDetailRepository.findByCode(code);
        detailVoList.forEach(item -> {
            if (BooleanEnum.FALSE.getCapital().equals(item.getBeFullAudit())) {
                item.setBeFullAuditStr(BooleanEnum.FALSE.getSure());
            }else if (BooleanEnum.TRUE.getCapital().equals(item.getBeFullAudit())) {
                item.setBeFullAuditStr(BooleanEnum.TRUE.getSure());
            }
        });
        OaResubmitDto dto = new OaResubmitDto();

        // 获取当前登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType = MqConstant.TAG_TPM_AUDIT;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        order.setTpmUrl(domainName + TpmOaPageEnum.PLAN_CLOSURE_FORM.getUrlCode() + "?code=" + order.getAuditCode());

        dto.setBusinessCode(businessType);
        dto.setRequestId(order.getProcessNumber());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());
        Set<String> orgCodeSet = detailVoList.stream().filter(e -> StringUtils.isNotBlank(e.getBearDepartmentCode())).map(e -> e.getBearDepartmentCode()).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeSet));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            order.setDeptCode(String.join(",", oaOrgCodeSet));
        }
        
        AuditMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(order, AuditMainDto.class, LinkedHashSet.class, ArrayList.class);
        dto.setRequestName("营销方案结案：" + order.getAuditName());
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));
        List<AuditDetailDto> detailDtoList = (List<AuditDetailDto>) nebulaToolkitService.copyCollectionByBlankList(detailVoList, MarketingAuditDetailVo.class, AuditDetailDto.class, LinkedHashSet.class, ArrayList.class);

        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        detailVoList.stream().filter(k-> StringUtils.isNotBlank(k.getCostCenterCode()))
                .collect(Collectors.groupingBy(MarketingAuditDetailVo::getCostCenterCode, Collectors.toList())).forEach((key, list)->{
                    CostCenterDetailDto detailDto = new CostCenterDetailDto();
                    detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
                    detailDto.setCostCenterName(list.get(0).getCostCenterName());
                    detailDto.setBearAmount(list.stream().map(MarketingAuditDetailVo::getAuditAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                    costCenterDetailDtoList.add(detailDto);
                });

        OaDetailDto oaDetailDto = new OaDetailDto();
        oaDetailDto.setDetailList(JSONUtil.toJsonStr(detailDtoList));
        oaDetailDto.setDetailClass(AuditDetailDto.class);

        OaDetailDto costCenterDetailDto = new OaDetailDto();
        costCenterDetailDto.setDetailList(JSONUtil.toJsonStr(costCenterDetailDtoList));
        costCenterDetailDto.setDetailClass(CostCenterDetailDto.class);

        dto.setDetailList(Arrays.asList(oaDetailDto, costCenterDetailDto));

        if (ryOaProcessService.resubmit(dto)) {
            MarketingAudit entity = marketingAuditRepository.findByCode(code);
            entity.setProcessDate(new Date());
            entity.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
            marketingAuditRepository.updateById(entity);
        }
        return null;
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public boolean oaWithdraw(String code, String remark) {
        MarketingAudit order = marketingAuditRepository.findByCode(code);
        Validate.isTrue(order.getStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(order.getProcessNumber()));
        dto.setProcessCreateId(order.getOaId());
        dto.setUserName(order.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (MarketingAuditFieldsEnum value : MarketingAuditFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod = (tableDetailFields, detailJsonObject) -> {
        for (MarketingAuditDetailFieldsEnum value : MarketingAuditDetailFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod1 = (tableDetailFields, detailJsonObject) -> {
        for (CostCenterFieldsEnum value : CostCenterFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
}
