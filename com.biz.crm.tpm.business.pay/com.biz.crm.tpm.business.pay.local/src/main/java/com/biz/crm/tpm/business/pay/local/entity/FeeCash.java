package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "FeeCash", description = "费用兑付")
@TableName("tpm_fee_cash")
@Getter
@Setter
@Entity(name = "tpm_fee_cash")
@org.hibernate.annotations.Table(appliesTo = "tpm_fee_cash", comment = "费用兑付")
@Table(name = "tpm_fee_cash", indexes = {
        @Index(name = "fee_cash_idx1", columnList = "cash_code"),
        @Index(name = "fee_cash_idx2", columnList = "cash_method"),
        @Index(name = "fee_cash_idx3", columnList = "cash_type"),
        @Index(name = "fee_cash_idx4", columnList = "status"),
})
public class FeeCash extends TenantFlagOpEntity {

    @ApiModelProperty("兑付名称")
    @Column(name = "cash_name", columnDefinition = "VARCHAR(255) COMMENT '兑付名称 '")
    private String cashName;

    @ApiModelProperty("兑付编号")
    @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
    private String cashCode;

    @ApiModelProperty("公司代码")
    @Column(name = "company_code",columnDefinition = "varchar(32) comment '公司代码'")
    private String companyCode;

    @ApiModelProperty("兑付金额汇总")
    @Column(name = "total_cash_amount", length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '兑付金额汇总 '")
    private BigDecimal totalCashAmount;

    @ApiModelProperty("申请金额汇总")
    @Column(name = "total_apply_amount", length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '申请金额汇总 '")
    private BigDecimal totalApplyAmount;

    @ApiModelProperty("兑付方式")
    @Column(name = "cash_method", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付方式 '")
    private String cashMethod;

    @ApiModelProperty("兑付类型")
    @Column(name = "cash_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付类型 '")
    private String cashType;

    @ApiModelProperty("职位编码")
    @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
    private String positionCode;

    @ApiModelProperty("组织编号")
    @Column(name = "org_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编号 '")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name",length = 128, columnDefinition = "VARCHAR(128) COMMENT '组织名称 '")
    private String orgName;

    @ApiModelProperty("审批状态")
    @Column(name = "status",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '审批状态 '")
    private String status;

    @ApiModelProperty("推送日期")
    @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("审批流程编码")
    @Column(name = "process_key",  length = 32, columnDefinition = "VARCHAR(32) COMMENT '审批流程编码 '")
    private String processKey;

    @ApiModelProperty("OA人员id")
    @Column(name = "oa_id", columnDefinition = "varchar(32) COMMENT 'OA人员id'")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    @Column(name = "oa_user_name", columnDefinition = "varchar(32) COMMENT 'OA人员账号'")
    private String oaUserName;

    @ApiModelProperty("下游单号")
    @Column(name = "external_code", columnDefinition = "VARCHAR(64) COMMENT '下游单号 '")
    private String externalCode;

    @ApiModelProperty("凭证号")
    @Column(name = "voucher_code", columnDefinition = " varchar(64) COMMENT '凭证号'")
    private String voucherCode;

    @ApiModelProperty("核销申请名称")
    @Column(name = "audit_name", columnDefinition = "VARCHAR(255) COMMENT '核销申请名称 '")
    private String auditName;

    @ApiModelProperty("核销申请编号")
    @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
    private String auditCode;

    @ApiModelProperty("供应商编码")
    @Column(name = "payee_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '供应商编码 '")
    private String payeeCode;

    @ApiModelProperty("供应商SAP编码")
    @Column(name = "payee_erp_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '供应商SAP编码 '")
    private String payeeErpCode;

    @ApiModelProperty("供应商名称")
    @Column(name = "payee_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '供应商名称 '")
    private String payeeName;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("回传TPM接口唯一标识（接口用）")
    private String businessCode;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("url")
    private String tpmUrl;

    @ApiModelProperty("查看流程日志所需地址")
    @Column(name = "hec_receipt_url", columnDefinition = "VARCHAR(1024) COMMENT '查看流程日志所需地址 '")
    private String hecReceiptUrl;

    @ApiModelProperty("付款状态")
    @Column(name = "pay_status", length = 64, columnDefinition = "VARCHAR(64) COMMENT '付款状态 '")
    private String payStatus;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
    private String customerName;

    @ApiModelProperty("客户责任人")
    @Column(name = "docking_name", columnDefinition = "varchar(255) COMMENT '客户责任人'")
    private String dockingName;

    @ApiModelProperty("是否暂存")
    @Column(name = "be_staging", columnDefinition = "varchar(32) default 'N' COMMENT 'OA人员账号'")
    private String beStaging;

    @ApiModelProperty("付款成功时间")
    @Column(name = "pay_sucess_date", columnDefinition = "datetime COMMENT '付款成功时间'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paySucessDate;

    @ApiModelProperty("审批通过日期")
    @Column(name = "pass_date", columnDefinition = "datetime COMMENT '审批通过日期'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date passDate;

    @ApiModelProperty("凭证回传日期")
    @Column(name = "voucher_callback_date", columnDefinition = "datetime COMMENT '凭证回传日期'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date voucherCallbackDate;

    @ApiModelProperty("贷项订单编号")
    @Column(name = "credit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '贷项订单编号 '")
    private String creditCode;

    @ApiModelProperty("需求类型")
    @Column(name = "demand_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '需求类型 '")
    private String demandType;

    @ApiModelProperty("需求总数量")
    @Column(name = "total_demand_quantity", length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '需求总数量 '")
    private BigDecimal totalDemandQuantity;

    @ApiModelProperty("需求总金额")
    @Column(name = "total_demand_amount", length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '需求总金额 '")
    private BigDecimal totalDemandAmount;
}
