package com.biz.crm.tpm.business.pay.local.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.WithHolding;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingBalanceDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingCollectDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingDto;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 费用预提(WithHolding)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-25 14:20:20
 */
public interface WithHoldingService extends BusinessPageCacheService<WithHoldingVo, WithHoldingDto> {

    /**
     * 分页查询数据
     *
     * @param pageable    分页对象
     * @param WithHolding 实体对象
     * @return
     */
    Page<WithHoldingVo> findByConditions(Pageable pageable, WithHoldingDto WithHolding);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    WithHolding findById(String id);

    /**
     * 新增数据
     *
     * @param WithHolding 实体对象
     * @return 新增结果
     */
    void create(List<WithHolding> WithHolding);

    /**
     * 修改新据
     *
     * @param WithHolding 实体对象
     * @return 修改结果
     */
    WithHolding update(WithHolding WithHolding);

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    void delete(List<String> idList);

    /**
     * 提交审批
     *
     * @param dto
     */
    WithHoldingCollectVo submit(String cacheKey, WithHoldingDto dto);

    /**
     * 提交审批保存
     *
     * @param dto
     * @return
     */
    String submitSave(String cacheKey, WithHoldingCollectDto dto);

    /**
     * 手动
     *
     * @param withHoldings
     * @return
     */
    void handleManual(List<WithHoldingImportVo> withHoldings);

    /**
     * 自动
     *
     * @param withHolding
     * @return
     */
    void handleAuto(WithHoldingDto withHolding);

    /**
     * 异步自动计提
     * @param withHoldingDto
     */
    void syncHandleAuto(WithHoldingDto withHoldingDto);

    /**
     * 定时任务计提
     */
    void taskAuto();

    /**
     * 根据活动明细编码查询
     *
     * @param codes
     * @return
     */
    List<WithHolding> findByActivitiesDetailCodes(List<String> codes);

    /**
     * 调整
     *
     * @param dto
     */
    void adjust(WithHoldingDto dto, ConfirmStatusEnum confirmStatusEnum);

    /**
     * 批量创建
     *
     * @param withHoldings
     */
    void importBatch(List<WithHoldingImportVo> withHoldings);

    /**
     * 批量修改
     *
     * @param withHoldings
     */
    void importUpdate(List<WithHoldingVo> withHoldings);

    /**
     * 根据编码查询
     *
     * @param codes
     * @return
     */
    List<WithHoldingVo> findByCodes(List<String> codes);

    /**
     * 根据活动明细编码查询计提金额
     *
     * @param codes
     * @return
     */
    Map<String, BigDecimal> findBySchemeDetailCodes(List<String> codes);

    List<WithHoldingVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes);

    Map<String, BigDecimal> findProcessPassBySchemeDetailCodes(List<String> codes);

    /**
     * 费控凭证回传
     *
     * @param dtoList
     */
    void hecVoucherCallback(List<HecCallbackDto> dtoList);

    /**
     * 根据活动明细编码查询审批通过的计提金额
     *
     * @param codes
     * @return
     */
    List<WithHoldingVo> findBySchemeDetailCodesPass(List<String> codes);

    /**
     * 费用计提合计
     *
     * @param params
     * @return
     */
    WithHoldingVo findTotalByConditions(LinkedHashMap<String, Object> params);

    Page<MarketingPlanCaseVo> findMarketingPlanCaseReportList(Pageable pageable, MarketingPlanCaseVo vo);

    Page<MarketingPlanCaseVo> findMarketingPlanCaseReportListV1(Pageable pageable, MarketingPlanCaseVo vo);

    /**
     * 根据计提编号确认
     *
     * @param codeList
     */
    void confirm(List<String> codeList, ConfirmStatusEnum confirmStatusEnum);

    List<WithHoldingVo> findByCollectCode(String collectCode);

    List<WithHoldingIncomeVo> findActualReportAmountByYears(List<String> years);

    BigDecimal findAmountByOrgCodesAndYears(List<String> orgCodes, String years);

    List<WithHoldingVo> findWithholdingListByYearsAndOrgCodes(WithholdingIncomeQueryDto dto);

    Map<String, BigDecimal> findByBusinessCodes(List<String> codes);

    /**
     * 分页查询计提余额数据
     *
     * @param pageable 分页对象
     * @param dto 查询条件
     * @return 计提余额分页数据
     */
    Page<WithHoldingBalanceVo> findWithHoldingBalanceByConditions(Pageable pageable, WithHoldingBalanceDto dto);
}

