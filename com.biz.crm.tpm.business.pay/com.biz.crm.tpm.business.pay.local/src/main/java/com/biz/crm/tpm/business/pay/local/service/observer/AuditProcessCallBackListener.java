package com.biz.crm.tpm.business.pay.local.service.observer;

import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditBillService;
import com.biz.crm.tpm.business.pay.local.service.AuditInvoiceService;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.InvoiceDetailDto;
import com.biz.crm.tpm.business.pay.sdk.service.AuditService;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceDetailService;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditInvoiceVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditVo;
import com.biz.crm.workflow.sdk.dto.ProcessStatusDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.listener.ProcessCompleteListener;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述：</br>费用核销审批回调方法 修改审批状态
 *
 * <AUTHOR>
 * @date 2022/6/16
 */
@Component
public class AuditProcessCallBackListener implements ProcessCompleteListener {
  @Autowired
  private AuditService auditService;
  @Autowired
  private AuditBillService auditBillService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private ActivitiesDetailService activitiesDetailService;
  @Autowired
  private AuditInvoiceService auditInvoiceService;
  @Autowired
  private InvoiceDetailService invoiceDeailService;

  @Override
  public String getBusinessCode() {
    return PayConstant.PROCESS_AUDIT_ACTIVITIES;
  }

  @Override
  @Transactional
  public void onProcessComplete(ProcessStatusDto dto) {
    //校验回调数据类型
    if (!dto.getBusinessCode().equals(PayConstant.PROCESS_AUDIT_ACTIVITIES)) {
      return;
    }
    String code = dto.getBusinessNo();
    AuditVo auditVo = this.auditService.findByCode(code);
    //校验回调实例
    Validate.notNull(auditVo, "费用核销审批流程回调失败，未查询到当前实例");
    //审批通过
    if (String.valueOf(dto.getProcessStatus()).equals(ProcessStatusEnum.PASS.getDictCode())) {
      //通过费用核销状态时，更新核销金额
      this.auditBillService.auditAmountByAuditCode(auditVo.getAuditCode());
      //更新活动核销状态
      this.auditService.updateActivitiesAuditStatusByAuditCode(auditVo.getAuditCode());
    }
    //审批驳回
    if (String.valueOf(dto.getProcessStatus()).equals(ProcessStatusEnum.REJECT.getDictCode())) {
      auditAmountBack(auditVo);
      //发票退回
      this.invoiceBack(auditVo);
    }
    //流程追回
    if (String.valueOf(dto.getProcessStatus()).equals(ProcessStatusEnum.RECOVER.getDictCode())) {
      auditAmountBack(auditVo);
      //发票退回
      this.invoiceBack(auditVo);
    }
  }

  /**
   * 退回超额核销占用金额
   *
   * @param auditVo
   */
  private void auditAmountBack(AuditVo auditVo) {
    for (AuditDetailVo auditDetailVo : auditVo.getAuditDetails()) {
      BigDecimal excessAmount = auditDetailVo.getExcessAmount();
      if (excessAmount != null && excessAmount.compareTo(BigDecimal.ZERO) > 0) {
        ActivitiesDetailVo basicActivityItemVo = this.activitiesDetailService.findByActivitiesDetailCode(auditDetailVo.getActivitiesDetailCode());
        Validate.notNull(basicActivityItemVo, "活动明细【%s】错误，请检查！", auditDetailVo.getActivitiesDetailCode());
        this.costBudgetVoService.back(auditDetailVo.getAuditCode(), auditDetailVo.getAuditDetailCode(), basicActivityItemVo.getCostBudgetCode(), excessAmount, null, CostBudgetItemSourceType.AUDIT.getDescr());
      }
    }
  }

  /**
   * 发票退回
   */
  private void invoiceBack(AuditVo auditVo) {
    List<AuditInvoiceVo> auditInvoiceVos = auditInvoiceService.findByAuditCode(auditVo.getAuditCode());
  }
}
