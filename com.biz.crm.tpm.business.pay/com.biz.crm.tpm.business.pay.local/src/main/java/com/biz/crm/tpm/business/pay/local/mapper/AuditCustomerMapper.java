package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditCustomer;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditCustomerDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditCustomerVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 费用明细客户;(tpm_audit_customer)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-27
 */
@Mapper
public interface AuditCustomerMapper extends BaseMapper<AuditCustomer> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto 动态查询条件
   * @return 分页对象列表
   */
  Page<AuditCustomerVo> findByConditions(@Param("page") Page<AuditCustomerVo> page , @Param("dto") AuditCustomerDto dto);
}
