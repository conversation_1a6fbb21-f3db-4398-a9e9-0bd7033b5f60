package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.eunm.AuditStatusEnum;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashTicketRepository;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.CashTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.BillingCompareReportService;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.vo.BillingCompareReportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTicketVo;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillingCompareReportServiceImpl implements BillingCompareReportService {

    @Autowired(required = false)
    private FeeCashTicketRepository feeCashTicketRepository;
    @Autowired(required = false)
    private FeeCashDetailRepository feeCashDetailRepository;
    @Autowired(required = false)
    private WithHoldingService withHoldingService;
    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;
    @Autowired(required = false)
    private BudgetSubjectsVoService budgetSubjectsVoService;

    /**
     * 按条件查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    @Override
    public Page<BillingCompareReportVo> findByConditions(Pageable pageable, BillingCompareReportVo dto) {
        ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new BillingCompareReportVo();
        }
        List<FeeCashTicketVo> feeCashTicketVos = feeCashTicketRepository.findByBillingCompareReport(dto);
        if (CollectionUtils.isEmpty(feeCashTicketVos)) {
            return new Page<>();
        }
        List<FeeCashDetailVo> commitList = feeCashDetailRepository.findByBillingCompareReport(new BillingCompareReportVo());
        //查询预算科目
        List<BudgetSubjectsVo> budgetSubjectsVoList = budgetSubjectsVoService.findByEnableStatus(EnableStatusEnum.ENABLE.getCode());
        Map<String, Set<String>> subjectSonMap = getSubjectMap(budgetSubjectsVoList);

        Map<String, List<FeeCashTicketVo>> listMap = feeCashTicketVos.stream().collect(
                Collectors.groupingBy(e -> e.getCashType() + ":" + StringUtils.defaultIfBlank(e.getYears(), "所有") + ":" + e.getCustomerCode() + ":" + e.getCustomerName() + ":"
                        + StringUtils.defaultIfBlank(e.getBudgetSubjectsName(), "所有") + ":" + StringUtils.defaultIfBlank(e.getBudgetSubjectsCode(), "所有")));

        //查找结案明细和活动明细
        Set<String> customerCodeSet = feeCashTicketVos.stream().map(FeeCashTicketVo::getCustomerCode).collect(Collectors.toSet());
        Set<String> subjectCodes = feeCashTicketVos.stream().filter(e -> StringUtils.isNotBlank(e.getBudgetSubjectsCode())).map(e -> e.getBudgetSubjectsCode()).collect(Collectors.toSet());
        MarketingAuditDetailDto queryDto = new MarketingAuditDetailDto();
        queryDto.setCashTypeSet(Sets.newHashSet(CashMethodEnum.TICKET_BUCKLE.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode()));
        queryDto.setCustomerCodes(customerCodeSet);
        queryDto.setStatus(ProcessStatusEnum.PASS.getDictCode());
        List<MarketingAuditDetailVo> auditDetailVoList = marketingAuditService.findDataViewByConditions(PageRequest.of(1, Integer.MAX_VALUE), queryDto).getRecords();
        MarketingPlanCaseVo planCaseDto = new MarketingPlanCaseVo();
        planCaseDto.setCashTypeSet(Sets.newHashSet(CashMethodEnum.TICKET_BUCKLE.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode()));
        planCaseDto.setChangeFlag(BooleanEnum.FALSE.getCapital());
        planCaseDto.setCustomerCodes(customerCodeSet);
        List<MarketingPlanCaseVo> planCaseList = withHoldingService.findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseDto).getRecords();
        Map<String, String> subjectMap = budgetSubjectsVoService.findByCodes(subjectCodes).stream().collect(Collectors.toMap(e -> e.getBudgetSubjectsCode(), e -> e.getLevel()));

        List<BillingCompareReportVo> records = new ArrayList<>();
        for (Map.Entry<String, List<FeeCashTicketVo>> entry : listMap.entrySet()) {
            BillingCompareReportVo data = new BillingCompareReportVo();
            String[] split = entry.getKey().split(":");
            data.setId(UUID.randomUUID().toString());
            data.setCashType(split[0]);
            data.setYears(StringUtils.isNotBlank(split[1]) && split[1].equals("所有") ? null : split[1]);
            data.setCustomerCode(split[2]);
            data.setCustomerName(split[3]);
            data.setBudgetSubjectName(StringUtils.isNotBlank(split[4]) && split[4].equals("所有") ? null : split[4]);
            data.setBudgetSubjectCode(StringUtils.isNotBlank(split[5]) && split[5].equals("所有") ? null : split[5]);
            data.setLevel(StringUtils.isNotBlank(split[5]) && split[5].equals("所有") ? null : subjectMap.get(split[5]));

            //账单导入未完结：当前维度（账扣/票扣+月份+客户+预算科目）下账扣明细/票扣明细的金额汇总
            data.setUnFinishAmount(entry.getValue().stream().map(e -> Optional.ofNullable(e.getAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add));
            //结案未兑付：当前维度（账扣/票扣+月份+客户+预算科目）下结案明细的兑付余额汇总-审批中的兑付金额汇总
            BigDecimal unCashAmount = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(auditDetailVoList)) {
                List<MarketingAuditDetailVo> auditDetailVos = auditDetailVoList.stream().filter(e ->
                        (data.getCashType().equals(CashTypeEnum.FEE.getDictCode()) ? e.getCashType().equals(CashMethodEnum.TICKET_BUCKLE.getDictCode()) : e.getCashType().equals(CashMethodEnum.DEDUCTIONS.getDictCode()))
                                && (StringUtils.isBlank(data.getYears()) ? true : e.getStartDate().substring(0, 7).equals(data.getYears()))
                                && e.getCustomerCode().equals(data.getCustomerCode())
                                && (StringUtils.isBlank(data.getBudgetSubjectCode()) ? true : subjectSonMap.get(data.getBudgetSubjectCode()).contains(e.getBudgetSubjectCode()))).collect(Collectors.toList());
                BigDecimal availableCashPrepayAmount = auditDetailVos.stream().filter(Objects::nonNull).map(e -> Optional.ofNullable(e.getAvailableCashPrepayAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                unCashAmount = Optional.ofNullable(availableCashPrepayAmount).orElse(BigDecimal.ZERO);
            }
            if (!CollectionUtils.isEmpty(commitList)) {
                List<FeeCashDetailVo> commitVoList = commitList.stream().filter(e ->
                        (data.getCashType().equals(CashTypeEnum.FEE.getDictCode()) ? e.getCashMethod().equals(CashMethodEnum.TICKET_BUCKLE.getDictCode()) : e.getCashMethod().equals(CashMethodEnum.DEDUCTIONS.getDictCode()))
                                && (StringUtils.isBlank(data.getYears()) ? true : e.getYearMonthLy().equals(data.getYears()))
                                && e.getCustomerCode().equals(data.getCustomerCode())
                                && (StringUtils.isBlank(data.getBudgetSubjectCode()) ? true : subjectSonMap.get(data.getBudgetSubjectCode()).contains(e.getBudgetSubjectCode()))).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(commitVoList)) {
                    unCashAmount = unCashAmount.subtract(commitVoList.stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }

            data.setUnCashAmount(unCashAmount);
            //方案未结案：当前维度（账扣/票扣+月份+客户+预算科目）下活动明细的“申请金额-已结案金额”汇总
            BigDecimal unAuditAmount = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(planCaseList)) {
                //要排除完全结案的
                unAuditAmount = planCaseList.stream().filter(e ->
                                        !AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) &&
                                        (data.getCashType().equals(CashTypeEnum.FEE.getDictCode()) ? e.getCashType().equals(CashMethodEnum.TICKET_BUCKLE.getDictCode()) : e.getCashType().equals(CashMethodEnum.DEDUCTIONS.getDictCode()))
                                        && (StringUtils.isBlank(data.getYears()) ? true : e.getStartDate().substring(0, 7).equals(data.getYears()))
                                        && e.getCustomerCode().equals(data.getCustomerCode())
                                        && (StringUtils.isBlank(data.getBudgetSubjectCode()) ? true : subjectSonMap.get(data.getBudgetSubjectCode()).contains(e.getBudgetSubjectCode())))
                        .filter(Objects::nonNull).map(e -> e.getApplyAmount().subtract(e.getAuditAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            data.setUnAuditAmount(unAuditAmount);
            //账单未兑付差异=账单导入未完结-结案未兑付
            data.setUnCashDiffAmount(data.getUnFinishAmount().subtract(data.getUnCashAmount()));
            //账单需计提差异=账单未兑付差异-方案未结案
            data.setUnWithHoldingDiffAmount(data.getUnCashDiffAmount().subtract(data.getUnAuditAmount()));
            if (data.getUnCashDiffAmount().compareTo(BigDecimal.ZERO) == -1) {
                data.setUnWithHoldingDiffAmount(BigDecimal.ZERO);
            }
            records.add(data);
        }

        //手动分页
        Page<BillingCompareReportVo> newPage = new Page<>();
        newPage.setTotal(records.size());
        newPage.setPages(records.size() / pageable.getPageSize() + (records.size() % pageable.getPageSize() == 0 ? 0 : 1));
        newPage.setSize(pageable.getPageSize());
        newPage.setCurrent(pageable.getPageNumber());
        List<BillingCompareReportVo> res = records.stream()
                .skip((pageable.getPageNumber() - 1) * pageable.getPageSize()).limit(pageable.getPageSize()).collect(Collectors.toList());
        newPage.setRecords(res);
        return newPage;
    }

    /**
     * 预算科目转map
     *
     * @param allList
     * @return
     */
    private Map<String, Set<String>> getSubjectMap(List<BudgetSubjectsVo> allList) {
        Map<String, Set<String>> map = new HashMap<>();
        allList.forEach(e -> map.put(e.getBudgetSubjectsCode(), getChildrenCodes(Arrays.asList(e), allList)));
        return map;
    }

    /**
     * 获取当前预算科目下所有的子级预算科目编码
     *
     * @return
     */
    private Set<String> getChildrenCodes(List<BudgetSubjectsVo> subjectList, List<BudgetSubjectsVo> allList) {
        Set<String> codes = new HashSet<>();
        for (BudgetSubjectsVo subject : subjectList) {
            codes.add(subject.getBudgetSubjectsCode());
            List<BudgetSubjectsVo> subjectChildren = getChildren(subject, allList);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(subjectChildren)) {
                codes.addAll(getChildrenCodes(subjectChildren, allList));
            }
        }
        return codes;
    }

    /**
     * 获取当前预算科目下子级预算科目
     *
     * @param
     * @return
     */
    private List<BudgetSubjectsVo> getChildren(BudgetSubjectsVo subjectChildren, List<BudgetSubjectsVo> allList) {
        return allList.stream().filter(e -> StringUtils.isNotBlank(e.getParentBudgetSubjectsCode()) && e.getParentBudgetSubjectsCode().equals(subjectChildren.getBudgetSubjectsCode()))
                .collect(Collectors.toList());
    }
}
