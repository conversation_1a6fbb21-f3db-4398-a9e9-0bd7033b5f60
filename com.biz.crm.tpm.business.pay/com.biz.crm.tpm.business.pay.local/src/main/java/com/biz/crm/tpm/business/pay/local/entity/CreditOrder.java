package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@ApiModel(value = "CreditOrder", description = "贷项订单")
@TableName("tpm_credit_order")
@Getter
@Setter
@Entity(name = "tpm_credit_order")
@org.hibernate.annotations.Table(appliesTo = "tpm_credit_order", comment = "贷项订单")
@Table(name = "tpm_credit_order", indexes = {
        @Index(name = "credit_idx1", columnList = "cash_code"),
        @Index(name = "credit_idx2", columnList = "push_status"),
        @Index(name = "credit_idx3", columnList = "credit_code"),
})
public class CreditOrder extends TenantFlagOpEntity {

    @ApiModelProperty("贷项订单编号")
    @Column(name = "credit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '贷项订单编号 '")
    private String creditCode;

    @ApiModelProperty("兑付编号")
    @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
    private String cashCode;

    @ApiModelProperty("订单类型")
    @Column(name = "order_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '订单类型 '")
    private String orderType;

    @ApiModelProperty("审批状态")
    @Column(name = "status", length = 64, columnDefinition = "varchar(64) COMMENT '审批状态'")
    private String status;

    @ApiModelProperty("批次号")
    @Column(name = "bt_no", length = 64, columnDefinition = "varchar(64) COMMENT '批次号'")
    private String btNo;

    @ApiModelProperty("一级部门编码")
    @Column(name = "department_one_code", columnDefinition = "VARCHAR(32) COMMENT '一级部门编码'")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称")
    @Column(name = "department_one_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '一级部门名称'")
    private String departmentOneName;

    @ApiModelProperty("二级部门编码")
    @Column(name = "department_two_code", columnDefinition = "VARCHAR(32) COMMENT '二级部门编码'")
    private String departmentTwoCode;

    @ApiModelProperty("二级部门名称")
    @Column(name = "department_two_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '二级部门名称'")
    private String departmentTwoName;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("凭证号")
    @Column(name = "voucher_code", length = 64, columnDefinition = " varchar(64) COMMENT '凭证号'")
    private String voucherCode;

    @ApiModelProperty("推送状态")
    @Column(name = "push_status",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '预提类型 '")
    private String pushStatus;

    @ApiModelProperty("失败原因")
    @Column(name = "fail_msg", length = 500, columnDefinition = "varchar(500) COMMENT '失败原因'")
    private String failMsg;

    @ApiModelProperty("金额")
    @Column(name = "amount", columnDefinition = "decimal(20,6) COMMENT '金额'")
    private BigDecimal amount;

    @ApiModelProperty("核销申请名称")
    @Column(name = "audit_name", columnDefinition = "VARCHAR(255) COMMENT '核销申请名称 '")
    private String auditName;

    @ApiModelProperty("核销申请编号")
    @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
    private String auditCode;

    @ApiModelProperty("上账日期")
    @Column(name = "account_date", length = 32, columnDefinition = "VARCHAR(32) COMMENT '上账状态 '")
    private String accountDate;

}
