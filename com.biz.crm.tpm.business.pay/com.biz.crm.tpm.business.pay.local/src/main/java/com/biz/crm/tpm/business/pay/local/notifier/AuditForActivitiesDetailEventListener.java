package com.biz.crm.tpm.business.pay.local.notifier;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesDetailEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditBillService;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.service.AuditService;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditBillVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditVo;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessMappingDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.ProcessBusinessMappingService;
import com.biz.crm.workflow.sdk.vo.ProcessBusinessMappingVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述：</br>实现费用核销对活动明细数据的监听
 * 实现在活动通过审核后判断活动明细绑定的活动细类是否为自动核销，如果是自动核销则自动创建核销数据
 *
 * <AUTHOR>
 * @date 2022/6/29
 */
@Component
@Slf4j
public class AuditForActivitiesDetailEventListener implements ActivitiesDetailEventListener {
  @Autowired
  private AuditBillService auditBillService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private ActivitiesDetailService activitiesDetailService;
  @Autowired
  private ActivitiesService activitiesService;
  @Autowired
  private AuditService auditService;
  @Autowired(required = false)
  private ProcessBusinessMappingService processBusinessMappingService;

  @Override
  public void onCreated(ActivitiesDetailVo activitiesDetailVo) {
  }

  @Override
  public void onUpdate(ActivitiesDetailVo oldActivitiesDetailVo, ActivitiesDetailVo activitiesDetailVo) {
  }

  @Override
  public void onDeleted(ActivitiesDetailVo activitiesDetailVo) {
  }

  @Override
  @Transactional
  public void onClosed(Collection<ActivitiesDetailVo> activitiesDetailVos) {
    if (CollectionUtils.isEmpty(activitiesDetailVos)) {
      return;
    }
    activitiesDetailVos.forEach(item -> {
      ActivitiesDetailVo activitiesDetailVo = this.activitiesDetailService.findByActivitiesDetailCode(item.getActivitiesDetailCode());
      Validate.notNull(activitiesDetailVo, "该活动尚未审批通过活动明细编号【%s】,请检查!", item.getActivitiesDetailCode());
      ActivitiesVo activitiesVo = activitiesService.findByActivitiesCode(item.getActivitiesCode());
      Validate.notNull(activitiesVo, "该活动明细【%s】不含有主活动信息，请检查", item.getActivitiesDetailCode());
      List<AuditVo> auditVos = this.auditService.findByActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode());
      if (CollectionUtils.isEmpty(auditVos)) {
        //此处是未进行过核销申请的情况
        this.costBudgetVoService.back(activitiesDetailVo.getActivitiesCode(), activitiesDetailVo.getActivitiesDetailCode(), activitiesDetailVo.getCostBudgetCode(), activitiesDetailVo.getApplyAmount(), null, this.analysisSource(activitiesVo.getActivityMark()));
        return;
      }
      //验证工作流状态
      List<String> auditCodes = auditVos.stream().map(AuditVo::getAuditCode).collect(Collectors.toList());
      ProcessBusinessMappingDto processBusinessMappingDto = new ProcessBusinessMappingDto();
      processBusinessMappingDto.setBusinessNos(auditCodes);
      processBusinessMappingDto.setBusinessCode(PayConstant.PROCESS_AUDIT_ACTIVITIES);
      List<ProcessBusinessMappingVo> processBusinessMappingVos = this.processBusinessMappingService.findMultiByByConditions(processBusinessMappingDto);
      Map<String, String> map = new HashMap<>();
      if (!CollectionUtils.isEmpty(processBusinessMappingVos)) {
        map = processBusinessMappingVos.stream().collect(Collectors.toMap(ProcessBusinessMappingVo::getBusinessNo, ProcessBusinessMappingVo::getProcessStatus));
      }
      AuditBillVo auditBillVo = this.auditBillService.findByActivitiesDetailCode(item.getActivitiesDetailCode());
      for (AuditVo auditVo : auditVos) {
        String processStatus = map.get(auditVo.getAuditCode());
        if (StringUtils.isNotBlank(processStatus)) {
          Validate.isTrue(auditVo == null || !ProcessStatusEnum.COMMIT.getDictCode().equals(processStatus), "活动明细【%s】是核销申请审批中状态，无法进行活动关闭", item.getActivitiesDetailCode());
          if (auditVo != null && auditBillVo != null && ProcessStatusEnum.PASS.getDictCode().equals(processStatus)) {
            // 验证活动状态如果是待处理的可以进行关闭活动
            Validate.isTrue(!BooleanEnum.TRUE.getCapital().equals(auditBillVo.getIsFullAudit()), "活动明细编号【%s】已完全核销，无法关闭", item.getActivitiesDetailCode());
          }
        }
      }

      // 计算是否需要退费费用预算
      BigDecimal backAmount = BigDecimal.ZERO;
      if (auditBillVo == null) {
        // 尚未和效果的活动明细
        backAmount = activitiesDetailVo.getApplyAmount();
      } else {
        backAmount = auditBillVo.getApplyAmount().subtract(auditBillVo.getAuditedAmount());
      }
      if (backAmount.compareTo(BigDecimal.ZERO) > 0) {
        this.costBudgetVoService.back(activitiesDetailVo.getActivitiesCode(), activitiesDetailVo.getActivitiesDetailCode(), activitiesDetailVo.getCostBudgetCode(), backAmount, null, this.analysisSource(activitiesVo.getActivityMark()));
      }

    });
    // 处理活动关闭问题
    this.auditService.updateActivitiesAuditStatusByActivitiesDetails(activitiesDetailVos);
  }

  private String analysisSource(String value) {
    switch (value) {
      case "OrdinaryActivity":
        return CostBudgetItemSourceType.ORDINARY_ACTIVITY.getDescr();
      case "ProjectActivity":
        return CostBudgetItemSourceType.PROJECT_ACTIVITY.getDescr();
      case "SchemeActivity":
        return CostBudgetItemSourceType.SCHEMA_ACTIVITY.getDescr();
      default:
        throw new IllegalArgumentException("活动关闭时，未知的费用预算来源，请检查");
    }
  }
}
