package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.FeeCash;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTicketDto;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.CashTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.HecPayStatusTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 费用兑付(FeeCash)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
@Component
public class FeeCashRepository extends ServiceImpl<FeeCashMapper, FeeCash> {

    @Autowired
    private FeeCashMapper feeCashMapper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 根据id集合获取详情集合
     *
     * @param ids ID集合
     * @return List<FeeCash>
     */
    public List<FeeCash> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(FeeCash::getId, ids)
                .eq(FeeCash::getTenantCode, tenantCode)
                .eq(FeeCash::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据编号与租户编号获取对象
     *
     * @param code
     * @return
     */
    public FeeCash findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .eq(FeeCash::getCashCode, code)
                .eq(FeeCash::getTenantCode, tenantCode)
                .eq(FeeCash::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
    }

    /**
     * 根据编号集合获取详情集合
     *
     * @param codes 编号集合
     * @return List<FeeCash>
     */
    public List<FeeCash> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(FeeCash::getCashCode, codes)
                .eq(FeeCash::getTenantCode, tenantCode)
                .eq(FeeCash::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }


    public List<FeeCash> findCreditOrderNull() {
        return this.lambdaQuery()
                .and(wp -> wp.isNull(FeeCash::getCreditCode).or().eq(FeeCash::getCreditCode, ""))
                .eq(FeeCash::getStatus, ProcessStatusEnum.PASS.getDictCode())
                .eq(FeeCash::getCashType, CashTypeEnum.FEE.getDictCode())
                .eq(FeeCash::getCashMethod, CashMethodEnum.TICKET_BUCKLE.getDictCode())
                .eq(FeeCash::getTenantCode, TenantUtils.getTenantCode())
                .eq(FeeCash::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }

    public void updateProcessStatus(FeeCashDto dto) {
        if (Objects.isNull(dto)
                || StringUtil.isEmpty(dto.getCashCode())
                || StringUtil.isEmpty(dto.getStatus())) {
            return;
        }
        if (ProcessStatusEnum.COMMIT.getDictCode().equals(dto.getStatus())) {
            this.lambdaUpdate()
                    .eq(FeeCash::getCashCode, dto.getCashCode())
                    .set(FeeCash::getStatus, dto.getStatus())
                    .set(Objects.nonNull(dto.getProcessDate()), FeeCash::getProcessDate, dto.getProcessDate())
                    .set(StringUtil.isNotEmpty(dto.getProcessKey()), FeeCash::getProcessKey, dto.getProcessKey())
                    .set(StringUtil.isNotBlank(dto.getHecReceiptUrl()), FeeCash::getHecReceiptUrl, dto.getHecReceiptUrl())
                    .update();
        } else {
            this.lambdaUpdate()
                    .eq(FeeCash::getCashCode, dto.getCashCode())
                    .eq(FeeCash::getProcessKey, dto.getProcessKey())
                    .set(FeeCash::getStatus, dto.getStatus())
                    .update();
        }

    }

    public void hecVoucherCallback(List<HecCallbackDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        this.baseMapper.hecVoucherCallback(dtoList);
    }

    public void hecPayStatusCallback(List<HecCallbackDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        this.baseMapper.hecPayStatusCallback(dtoList);
    }

    public void hecPayStatusSuccessAccount(Set<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return;
        }
        this.lambdaUpdate().in(FeeCash::getCashCode, codes)
                .eq(FeeCash::getCashType, CashTypeEnum.ACCOUNT.getDictCode())
                .set(FeeCash::getPayStatus, HecPayStatusTypeEnum.SUCCESS_PAY.getCode())
                .set(FeeCash::getPaySucessDate, new Date()).update();
    }

    public void hecPayStatus(Set<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return;
        }
        this.lambdaUpdate().in(FeeCash::getCashCode, codes)
                .set(FeeCash::getPayStatus, HecPayStatusTypeEnum.NOT_PAY.getCode())
                .set(FeeCash::getPaySucessDate, null).update();
    }

    public List<FeeCashVo> findByAuditCustomer(List<String> auditCodes, List<String> customerCodes) {
        if (CollectionUtil.isEmpty(auditCodes) || CollectionUtil.isEmpty(customerCodes)) {
            return new ArrayList<>();
        }
        List<FeeCash> list = lambdaQuery().in(FeeCash::getAuditCode, auditCodes)
                .in(FeeCash::getCustomerCode, customerCodes).list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, FeeCash.class, FeeCashVo.class, LinkedHashSet.class, ArrayList.class));
    }


    public List<FeeCashDetailVo> findCashAmountBySchemeDetailCodes(List<String> schemeDetailCodes) {
        List<List<String>> partitionList = Lists.partition(schemeDetailCodes, 800);
        List<FeeCashDetailVo> dataList = Lists.newArrayList();
        for (List<String> list : partitionList) {
            List<FeeCashDetailVo> data = this.baseMapper.findCashAmountBySchemeDetailCodes(list);
            if (CollectionUtils.isNotEmpty(data)) {
                dataList.addAll(data);
            }
        }
        return dataList;
    }


    public List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesPass(List<String> schemeDetailCodes) {
        List<List<String>> partitionList = Lists.partition(schemeDetailCodes, 800);
        List<FeeCashDetailVo> dataList = Lists.newArrayList();
        for (List<String> list : partitionList) {
            List<FeeCashDetailVo> data = this.baseMapper.findCashAmountBySchemeDetailCodesPass(list);
            if (CollectionUtils.isNotEmpty(data)) {
                dataList.addAll(data);
            }
        }
        return dataList;

    }


    public List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesApproved(List<String> schemeDetailCodes) {
        List<List<String>> partitionList = Lists.partition(schemeDetailCodes, 800);
        List<FeeCashDetailVo> dataList = Lists.newArrayList();
        for (List<String> list : partitionList) {
            List<FeeCashDetailVo> data = this.baseMapper.findCashAmountBySchemeDetailCodesApproved(list);
            if (CollectionUtils.isNotEmpty(data)) {
                dataList.addAll(data);
            }
        }
        return dataList;
    }


    public List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesApprovedBefore(List<String> orgCodes, String yearMonthLy) {
        return this.baseMapper.findCashAmountBySchemeDetailCodesApprovedBefore(orgCodes, yearMonthLy);
    }


    public List<FeeCashDto> findListByTicketBuckle() {
        List<FeeCash> feeCashList = this.lambdaQuery()
                .eq(FeeCash::getCashMethod, CashMethodEnum.TICKET_BUCKLE.getDictCode())
                .in(FeeCash::getStatus, Lists.newArrayList(ProcessStatusEnum.COMMIT.getDictCode(), ProcessStatusEnum.PASS.getDictCode()))
                .list();
        if (CollectionUtils.isEmpty(feeCashList)) {
            return Lists.newArrayList();
        }
        return JsonUtils.convert(feeCashList, List.class, FeeCashDto.class);
    }

    public void updateFeeCashStatus(List<String> codes) {
        this.lambdaUpdate()
                .in(FeeCash::getCashCode, codes)
                .set(FeeCash::getStatus, ProcessStatusEnum.PREPARE.getDictCode())
                .update();
    }

    public Page<TicketFeeCashDetailVo> getTicketDeductionDetail(Pageable pageable, FeeCashTicketDto dto) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 50);
        }
        Page<TicketFeeCashDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.getTicketDeductionDetail(page, dto);
    }

    // //查询费用兑付的结案金额+加上了还未通过的预付 不管是任何状态 除了删除的
    public List<FeeCashDetailVo> findFeeCashAuditDetailListByAuditDetailCodes(List<String> auditDetailCodes,String cashCode){
        return this.baseMapper.findFeeCashAuditDetailListByAuditDetailCodes(auditDetailCodes,cashCode);
    }

    public List<FeeCashDetailVo> findPrepayListByAuditDetailCodes(List<String> auditDetailCodes,String cashCode){
        return this.baseMapper.findPrepayListByAuditDetailCodes(auditDetailCodes,cashCode);
    }

    public List<FeeCashDetailVo> findNoTaxCashAmountByYearsAndOrgCodes(String years, List<String> orgCodeList) {
        return this.baseMapper.findNoTaxCashAmountByYearsAndOrgCodes(years, orgCodeList);
    }
}

