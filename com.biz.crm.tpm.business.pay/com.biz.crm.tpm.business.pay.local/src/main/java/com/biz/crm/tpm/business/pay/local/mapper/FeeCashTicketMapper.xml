<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.FeeCashTicketMapper">

    <select id="findByBillingCompareReport" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTicketVo">
        select t.*,ta.status,ta.cash_type
        from tpm_fee_cash_ticket t
        inner join tpm_fee_cash ta on t.cash_code=ta.cash_code
        <where>
            ta.status in ('1','4','5')
            and ((ta.cash_method ='ticket_buckle' and ta.cash_type='fee') or ta.cash_type='account')
            <if test="dto.cashType != null and dto.cashType != ''">
                and ta.cash_type = #{dto.cashType}
            </if>
            <if test="dto.years != null and dto.years != ''">
                and t.years = #{dto.years}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                <bind name="customerCode" value="'%' + dto.customerCode + '%'"/>
                and t.customer_code like #{customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                <bind name="customerName" value="'%' + dto.customerName + '%'"/>
                and t.customer_name like #{customerName}
            </if>
            <if test="dto.budgetSubjectName != null and dto.budgetSubjectName != ''">
                <bind name="budgetSubjectName" value="'%' + dto.budgetSubjectName + '%'"/>
                and t.budget_subjects_name like #{budgetSubjectName}
            </if>
        </where>
        order by t.years,t.id
    </select>
</mapper>

