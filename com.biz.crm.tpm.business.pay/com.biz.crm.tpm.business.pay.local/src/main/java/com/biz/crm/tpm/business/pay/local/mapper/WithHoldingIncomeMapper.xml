<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.WithHoldingIncomeMapper">

    <select id="findByYear" resultType="com.biz.crm.tpm.business.pay.local.entity.WithHoldingIncome">
        SELECT
            org_code,org_name, years,ifnull(SUM(report_income),0) as reportIncome
        FROM
            tpm_with_holding_income
        WHERE years in <foreach collection="years" item="item" open="(" separator="," close=")">
                           #{item}
                        </foreach>
        GROUP BY org_code,org_name, years
    </select>

    <select id="findListByCondition" resultType="com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo">
        select * from tpm_with_holding_income
        <where>
            1=1
            <if test="dto.years != null and dto.years != ''">
                and years = #{dto.years}
            </if>
            <if test="dto.yearsList != null and dto.yearsList.size()>0">
                and years in
                <foreach collection="dto.yearsList" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orgCodes != null and dto.orgCodes.size()>0">
                and org_code in
                <foreach collection="dto.orgCodes" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>

