package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.WithHoldingConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

/**
 * 费用兑付分页缓存
 */
@Slf4j
@Component
public class WithHoldingPageCacheHelper extends BusinessPageCacheHelper<WithHoldingVo, WithHoldingDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return WithHoldingConstant.CACHE_KEY_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<WithHoldingDto> getDtoClass() {
        return WithHoldingDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<WithHoldingVo> getVoClass() {
        return WithHoldingVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param withHoldingDto
     * @param cacheKey
     */
    @Override
    public List<WithHoldingDto> findDtoListFromRepository(WithHoldingDto withHoldingDto, String cacheKey) {
        if (StringUtils.isBlank(withHoldingDto.getCollectCode())) {
            return new ArrayList<>();
        }
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findByCollectCode(withHoldingDto.getCollectCode());
        return CollectionUtils.isNotEmpty(withHoldingVos) ? (List<WithHoldingDto>) nebulaToolkitService.copyCollectionByBlankList(withHoldingVos, WithHoldingVo.class, WithHoldingDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<WithHoldingDto> newItem(String cacheKey, List<WithHoldingDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<WithHoldingDto> copyItem(String cacheKey, List<WithHoldingDto> itemList) {
        List<WithHoldingDto> newItemList = (List<WithHoldingDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, WithHoldingDto.class, WithHoldingDto.class, HashSet.class, ArrayList.class);
        for (WithHoldingDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 获取Dto中的主键
     *
     * @param withHoldingDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(WithHoldingDto withHoldingDto) {
        return withHoldingDto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param withHoldingDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(WithHoldingDto withHoldingDto) {
        return withHoldingDto.getChecked();
    }

}
