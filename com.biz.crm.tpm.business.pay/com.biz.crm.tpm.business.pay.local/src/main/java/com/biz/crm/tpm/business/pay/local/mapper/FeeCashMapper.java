package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.FeeCash;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTicketDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.TicketFeeCashDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用兑付(FeeCash)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
public interface FeeCashMapper extends BaseMapper<FeeCash> {

    /**
     * 更新凭证信息
     *
     * @param dtoList
     */
    void hecVoucherCallback(@Param("dtoList") List<HecCallbackDto> dtoList);

    /**
     * 更新付款状态
     *
     * @param dtoList
     */
    void hecPayStatusCallback(@Param("dtoList") List<HecCallbackDto> dtoList);

    List<FeeCashDetailVo> findCashAmountBySchemeDetailCodes(@Param("schemeDetailCodes") List<String> schemeDetailCodes);


    List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesPass(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesApproved(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    List<FeeCashDetailVo> findCashAmountBySchemeDetailCodesApprovedBefore(@Param("orgCodes") List<String> orgCodes, @Param("yearMonthLy") String yearMonthLy);

    Page<TicketFeeCashDetailVo> getTicketDeductionDetail(@Param("page") Page<TicketFeeCashDetailVo> page, @Param("dto") FeeCashTicketDto dto);

    List<FeeCashDetailVo> findFeeCashAuditDetailListByAuditDetailCodes(@Param("auditDetailCodes") List<String> auditDetailCodes,@Param("cashCode") String cashCode);


    List<FeeCashDetailVo> findPrepayListByAuditDetailCodes(@Param("auditDetailCodes") List<String> auditDetailCodes, @Param("cashCode") String cashCode);

    List<FeeCashDetailVo> findNoTaxCashAmountByYearsAndOrgCodes(@Param("yearMonthLy") String years, @Param("orgCodeList") List<String> orgCodeList);
}

