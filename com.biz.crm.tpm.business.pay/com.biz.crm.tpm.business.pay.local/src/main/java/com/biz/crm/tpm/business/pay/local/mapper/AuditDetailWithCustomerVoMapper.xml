<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AuditDetailWithCustomerVoMapper">

  <resultMap id="AuditDetailVoMap" type="com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailWithCustomerVo">
    <result property="activitiesCode" column="activities_code" jdbcType="VARCHAR"/>
    <result property="activitiesName" column="activities_name" jdbcType="VARCHAR"/>
    <result property="auditCode" column="audit_code" jdbcType="VARCHAR"/>
    <result property="customerCode" column="customerCode" jdbcType="VARCHAR"/>
    <result property="customerName" column="customerName" jdbcType="VARCHAR"/>
    <result property="auditDetailCode" column="audit_detail_code" jdbcType="VARCHAR"/>
    <result property="beginTime" column="begin_time" jdbcType="TIMESTAMP"/>
    <result property="budgetSubjectsCode" column="budget_subjects_code" jdbcType="VARCHAR"/>
    <result property="budgetSubjectsName" column="budget_subjects_name" jdbcType="VARCHAR"/>
    <result property="costTypeCategoryCode" column="cost_type_category_code" jdbcType="VARCHAR"/>
    <result property="costTypeCategoryName" column="cost_type_category_name" jdbcType="VARCHAR"/>
    <result property="costTypeDetailCode" column="cost_type_detail_code" jdbcType="VARCHAR"/>
    <result property="costTypeDetailName" column="cost_type_detail_name" jdbcType="VARCHAR"/>
    <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
    <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
    <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
    <result property="payBy" column="pay_by" jdbcType="VARCHAR"/>
    <result property="productLevelCode" column="product_level_code" jdbcType="VARCHAR"/>
    <result property="productLevelName" column="product_level_name" jdbcType="VARCHAR"/>
    <result property="terminalCode" column="terminal_code" jdbcType="VARCHAR"/>
    <result property="terminalName" column="terminal_name" jdbcType="VARCHAR"/>
    <collection property="productList" ofType="com.biz.crm.tpm.business.pay.sdk.vo.AuditProductVo">
      <result property="id" column="productId" jdbcType="VARCHAR"/>
      <result property="tenantCode" column="productTenantCode" jdbcType="VARCHAR"/>
      <result property="auditCode" column="productAuditCode" jdbcType="VARCHAR"/>
      <result property="auditDetailCode" column="productAuditDetailCode" jdbcType="VARCHAR"/>
      <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
      <result property="productName" column="product_name" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
  <select id="getAuditDetailWithCustomer" resultMap="AuditDetailVoMap">
    select tad.*
    , tad.customerAuditAmount
    , tad.audit_amount
    , tap.product_code
    , tap.product_name
    , tad.customerCode
    , tad.customerName
    , ta.is_auto_audit
    from (
    select t.*, tac.customer_code as customerCode, tac.customer_name as customerName, tac.audit_amount as
    customerAuditAmount
    from tpm_audit_detail t
    left join tpm_audit_customer tac on t.audit_detail_code = tac.audit_detail_code
    ) tad
    left join tpm_audit ta on tad.audit_code = ta.audit_code
    left join tpm_audit_product tap on tad.audit_detail_code = tap.audit_detail_code
    where ta.del_flag = '${@<EMAIL>()}'
    and tad.del_flag = '${@<EMAIL>()}'
    and tad.tenant_code = #{dto.tenantCode}
    and ta.tenant_code = #{dto.tenantCode}
    <if test="dto.auditCode != null and dto.auditCode != '' ">
      <bind name="likeAuditCode" value="'%' + dto.auditCode + '%'"/>
      and tad.audit_code like #{likeAuditCode}
    </if>
    <if test="dto.auditDetailCode != null and dto.auditDetailCode != '' ">
      <bind name="likeAuditDetailCode" value="'%' + dto.auditDetailCode + '%'"/>
      and tad.audit_detail_code like #{likeAuditDetailCode}
    </if>
    <if test="dto.activitiesCode != null and dto.activitiesCode != '' ">
      <bind name="likeActivitiesCode" value="'%' + dto.activitiesCode + '%'"/>
      and tad.activities_code like #{likeActivitiesCode}
    </if>
    <if test="dto.auditDetailCodeList != null and dto.auditDetailCodeList.size > 0">
      and tad.audit_detail_code in
      <foreach item="item" collection="dto.auditDetailCodeList" open="(" separator="," close=")" index="index">
        #{item}
      </foreach>
    </if>
    order by ta.create_time desc,ta.id
  </select>

</mapper>