package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：费用核销发票;
 * <AUTHOR> Keller
 * @date : 2022-7-6
 */
@ApiModel(value = "AuditInvoice",description = "费用核销发票")
@TableName("tpm_audit_invoice")
@Getter
@Setter
@Entity(name = "tpm_audit_invoice")
@org.hibernate.annotations.Table(appliesTo = "tpm_audit_invoice", comment = "费用核销发票")
@Table(name = "tpm_audit_invoice")
public class AuditInvoice  extends TenantEntity {

  /** 费用核销编号 */
  @ApiModelProperty(name = "auditCode",notes = "方案编号", value= "方案编号")
  @Column(name = "audit_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '费用核销编号 '")
  private String auditCode;

  /** 发票编号 */
  @ApiModelProperty(name = "invoiceNo",notes = "发票编号", value= "发票编号")
  @Column(name = "invoice_no", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '发票编号 '")
  private String invoiceNo;

  /** 使用金额 */
  @ApiModelProperty(name = "useAmount",notes = "使用金额", value= "使用金额")
  @Column(name = "use_amount", nullable = false, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '使用金额 '")
  private BigDecimal useAmount;

  /** 创建时间 */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "create_time" , length = 20, columnDefinition = "datetime COMMENT '创建时间 '")
  private Date createTime;

  /** 创建人账号 */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "create_account" , length = 60, columnDefinition = "varchar(60) COMMENT '创建人账号'")
  private String createAccount;

  /** 创建人名称 */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "create_name" , length = 60, columnDefinition = "varchar(60) COMMENT '创建人名称'")
  private String createName;

  /** 修改时间 */
  @TableField(fill = FieldFill.UPDATE,updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "modify_time" , length = 20, columnDefinition = "datetime COMMENT '修改时间'")
  private Date modifyTime;

  /** 修改人账号  */
  @TableField(fill = FieldFill.UPDATE,updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "modify_account" , length = 60, columnDefinition = "varchar(40) COMMENT '修改人账号'")
  private String modifyAccount;

  /** 修改人名称 */
  @TableField(fill = FieldFill.UPDATE,updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "modify_name" , length = 60, columnDefinition = "varchar(60) COMMENT '修改人名称'")
  private String modifyName;

}