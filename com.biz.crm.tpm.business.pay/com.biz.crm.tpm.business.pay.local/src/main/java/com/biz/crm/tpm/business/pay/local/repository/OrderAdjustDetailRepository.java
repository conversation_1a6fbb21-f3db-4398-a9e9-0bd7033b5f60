package com.biz.crm.tpm.business.pay.local.repository;



import com.biz.crm.tpm.business.pay.local.entity.OrderAdjustDetail;
import com.biz.crm.tpm.business.pay.local.mapper.OrderAdjustDetailMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 订单调整明细(OrderAdjustDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-01 20:23:43
 */
@Component
public class OrderAdjustDetailRepository extends ServiceImpl<OrderAdjustDetailMapper, OrderAdjustDetail> {

  @Autowired
  private OrderAdjustDetailMapper orderAdjustDetailMapper;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<OrderAdjustDetailVo> findByCode(String code) {
    List<OrderAdjustDetail> list = this.lambdaQuery().eq(OrderAdjustDetail::getAdjustCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, OrderAdjustDetail.class, OrderAdjustDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码查询
   *
   * @param codes
   * @return
   */
  public List<OrderAdjustDetailVo> findByCodes(List<String> codes) {
    List<OrderAdjustDetail> list = this.lambdaQuery().in(OrderAdjustDetail::getAdjustCode, codes).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, OrderAdjustDetail.class, OrderAdjustDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param codes
   * @return
   */
  public void deleteByCodes(List<String> codes) {
    this.lambdaUpdate().in(OrderAdjustDetail::getAdjustCode, codes).remove();
  }
}

