package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditInvoice;
import com.biz.crm.tpm.business.pay.local.repository.AuditInvoiceRepository;
import com.biz.crm.tpm.business.pay.local.service.AuditInvoiceService;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditInvoiceVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 费用核销发票;(tpm_audit_invoice)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-7-6
 */
@Service("auditInvoiceService")
public class AuditInvoiceServiceImpl implements AuditInvoiceService {
  @Autowired
  private AuditInvoiceRepository auditInvoiceRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   * @return
   */
  @Override
  public Page<AuditInvoiceVo> findByConditions(Pageable pageable, AuditInvoiceDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new AuditInvoiceDto();
    }
    return this.auditInvoiceRepository.findByConditions(pageable, dto);
  }
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public AuditInvoiceVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    AuditInvoice auditInvoice = this.auditInvoiceRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (auditInvoice == null) {
      return null;
    }
    AuditInvoiceVo auditInvoiceVo = this.nebulaToolkitService.copyObjectByWhiteList(auditInvoice, AuditInvoiceVo.class, LinkedHashSet.class, ArrayList.class);
    return auditInvoiceVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<AuditInvoiceVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<AuditInvoice> auditInvoices = this.auditInvoiceRepository.findByIds(ids);
    Collection<AuditInvoiceVo> auditInvoiceVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditInvoices, AuditInvoice.class, AuditInvoiceVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(auditInvoiceVos);
  }

  /**
   * 根绝业务编号auditCode获取业务数据
   *
   * @param auditCode
   * @return 单条数据
   */
  @Override
  public List<AuditInvoiceVo> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return Collections.emptyList();
    }
    List<AuditInvoice> auditInvoices = this.auditInvoiceRepository.findByAuditCode(auditCode);
    if (CollectionUtils.isEmpty(auditInvoices)) {
      return Collections.emptyList();
    }
    Collection<AuditInvoiceVo> auditInvoiceVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditInvoices, AuditInvoice.class, AuditInvoiceVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(auditInvoiceVos);
  }

  /**
   * 新增数据
   *
   * @param auditInvoiceDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AuditInvoiceVo create(AuditInvoiceDto auditInvoiceDto) {

    this.createValidate(auditInvoiceDto);
    AuditInvoice auditInvoice = this.nebulaToolkitService.copyObjectByWhiteList(auditInvoiceDto, AuditInvoice.class, LinkedHashSet.class, ArrayList.class);
    auditInvoice.setTenantCode(TenantUtils.getTenantCode());
    this.auditInvoiceRepository.saveOrUpdate(auditInvoice);
    AuditInvoiceVo auditInvoiceVo = this.nebulaToolkitService.copyObjectByWhiteList(auditInvoice, AuditInvoiceVo.class, LinkedHashSet.class, ArrayList.class);

    auditInvoiceVo.setId(auditInvoice.getId());
    return auditInvoiceVo;
  }

  /**
   * 批量新增
   *
   * @param auditInvoiceDtos
   * @return
   */
  @Transactional
  @Override
  public List<AuditInvoiceVo> createBatch(Collection<AuditInvoiceDto> auditInvoiceDtos) {
    if (CollectionUtils.isEmpty(auditInvoiceDtos)) {
      return Lists.newArrayList();
    }
    List<AuditInvoiceVo> auditInvoiceVos = Lists.newArrayList();
    for (AuditInvoiceDto auditInvoiceDto : auditInvoiceDtos) {
      AuditInvoiceVo auditInvoiceVo = this.create(auditInvoiceDto);
      auditInvoiceVos.add(auditInvoiceVo);
    }
    return auditInvoiceVos;
  }

  /**
   * 修改新据
   *
   * @param auditInvoiceDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public AuditInvoiceVo update(AuditInvoiceDto auditInvoiceDto) {
    this.updateValidate(auditInvoiceDto);
    AuditInvoice auditInvoice = this.auditInvoiceRepository.findByIdAndTenantCode(auditInvoiceDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(auditInvoice, "修改数据不存在，请检查！");
    AuditInvoiceVo oldAuditInvoiceVo = this.nebulaToolkitService.copyObjectByWhiteList(auditInvoice, AuditInvoiceVo.class, LinkedHashSet.class, ArrayList.class);

    auditInvoice.setAuditCode(auditInvoiceDto.getAuditCode());
    auditInvoice.setInvoiceNo(auditInvoiceDto.getInvoiceNo());
    auditInvoice.setUseAmount(auditInvoiceDto.getUseAmount());
    auditInvoice.setTenantCode(TenantUtils.getTenantCode());
    this.auditInvoiceRepository.saveOrUpdate(auditInvoice);
    AuditInvoiceVo auditInvoiceVo = this.nebulaToolkitService.copyObjectByWhiteList(auditInvoice, AuditInvoiceVo.class, LinkedHashSet.class, ArrayList.class);

    return auditInvoiceVo;
  }

  /**
   * 批量修改据
   *
   * @param auditInvoiceDtos 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public List<AuditInvoiceVo> updateBatch(Collection<AuditInvoiceDto> auditInvoiceDtos) {
    if (CollectionUtils.isEmpty(auditInvoiceDtos)) {
      return Collections.emptyList();
    }
    String code = auditInvoiceDtos.stream().findFirst().get().getAuditCode();
    List<AuditInvoiceVo> dbAuditInvoiceVos = this.findByAuditCode(code);
    Set<String> dbIds = dbAuditInvoiceVos.stream().map(AuditInvoiceVo::getId).collect(Collectors.toSet());
    Set<String> currentIds = auditInvoiceDtos.stream().map(AuditInvoiceDto::getId).collect(Collectors.toSet());
    Set<String> delData = Sets.difference(dbIds, currentIds);
    if (!CollectionUtils.isEmpty(delData)) {
      this.delete(delData);
    }
    List<AuditInvoiceDto> addDtos = auditInvoiceDtos.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
    List<AuditInvoiceDto> updateDtos = auditInvoiceDtos.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
    List<AuditInvoiceVo> auditInvoiceVos = this.createBatch(addDtos);
    if (!CollectionUtils.isEmpty(updateDtos)) {
      updateDtos.forEach(item -> {
        auditInvoiceVos.add(this.update(item));
      });
    }
    return auditInvoiceVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<AuditInvoice> auditInvoices = this.auditInvoiceRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(auditInvoices)) {
      return;
    }
    Collection<AuditInvoiceVo> auditInvoiceVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditInvoices, AuditInvoice.class, AuditInvoiceVo.class, LinkedHashSet.class, ArrayList.class);
    this.auditInvoiceRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  @Override
  public void deleteByAuditCode(String auditCode) {
    Validate.notBlank(auditCode, "删除数据时，核销编号不能为空！");
    List<AuditInvoice> auditInvoices = this.auditInvoiceRepository.findByAuditCode(auditCode);
    if (CollectionUtils.isEmpty(auditInvoices)) {
      return;
    }
    Set<String> ids = auditInvoices.stream().map(AuditInvoice::getId).collect(Collectors.toSet());
    this.auditInvoiceRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }


  /**
   * 创建验证
   *
   * @param auditInvoiceDto
   */
  private void createValidate(AuditInvoiceDto auditInvoiceDto) {
    Validate.notNull(auditInvoiceDto, "新增时，对象信息不能为空！");
    Validate.notBlank(auditInvoiceDto.getAuditCode(), "新增数据时，费用核销编号不能为空！");
    Validate.notBlank(auditInvoiceDto.getInvoiceNo(), "新增数据时，发票编号不能为空！");
    Validate.notNull(auditInvoiceDto.getUseAmount(), "新增数据时，使用金额不能为空！");
  }

  /**
   * 修改验证
   *
   * @param auditInvoiceDto
   */
  private void updateValidate(AuditInvoiceDto auditInvoiceDto) {
    Validate.notNull(auditInvoiceDto, "修改时，对象信息不能为空！");
    Validate.notBlank(auditInvoiceDto.getId(), "修改数据时，主键不能为空！");
    Validate.notBlank(auditInvoiceDto.getAuditCode(), "修改数据时，费用核销编号不能为空！");
    Validate.notBlank(auditInvoiceDto.getInvoiceNo(), "修改数据时，发票编号不能为空！");
    Validate.notNull(auditInvoiceDto.getUseAmount(), "修改数据时，使用金额不能为空！");
  }

}
