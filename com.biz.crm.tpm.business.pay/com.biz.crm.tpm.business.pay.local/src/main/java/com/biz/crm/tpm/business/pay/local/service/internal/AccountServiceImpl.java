package com.biz.crm.tpm.business.pay.local.service.internal;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.Account;
import com.biz.crm.tpm.business.pay.local.repository.AccountRepository;
import com.biz.crm.tpm.business.pay.local.service.AccountService;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.AccountLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.enums.AccountStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.event.log.AccountLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.vo.AccountVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;

/**
 * 费用上账主表(account)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
@Service("AccountService")
public class AccountServiceImpl implements AccountService {

  @Autowired
  private AccountRepository accountRepository;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param account  实体对象
   * @return
   */
  @Override
  public Page<Account> findByConditions(Pageable pageable, AccountDto account) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(account)) {
      account = new AccountDto();
    }
    return this.accountRepository.findByConditions(pageable, account);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public Account findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    return this.accountRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
  }

  /**
   * 删除ID集合
   * @param ids 主键
   * @return
   */
  @Override
  public List<Account> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)){
      return  new ArrayList<>(0);
    }
    return this.accountRepository.findByIds(ids);
  }

  /**
   * 通过活动明细编码
   * @param codes 主键
   * @return
   */
  @Override
  public List<Account> findByActivitiesDetailCode(List<String> codes) {
    if (CollectionUtils.isEmpty(codes)){
      return  new ArrayList<>(0);
    }
    return this.accountRepository.findByActivitiesDetailCode(codes);
  }

  /**
   * 新增数据
   *
   * @param account 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public Account create(Account account) {
    this.createValidate(account);
    account.setTenantCode(TenantUtils.getTenantCode());
    this.accountRepository.saveOrUpdate(account);
    AccountVo accountVo = this.nebulaToolkitService.copyObjectByWhiteList(account, AccountVo.class, LinkedHashSet.class, ArrayList.class);
    accountVo.setId(null);
    //新增业务日志
    AccountLogEventDto logEventDto = new AccountLogEventDto();
    logEventDto.setOriginal(null);
    logEventDto.setNewest(accountVo);
    SerializableBiConsumer<AccountLogEventListener, AccountLogEventDto> onCreate =
            AccountLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(logEventDto, AccountLogEventListener.class, onCreate);
    return account;
  }

  /**
   * 批量更新
   * @param accounts
   */
  @Override
  public void createBatch(List<Account> accounts) {
    for (Account account : accounts) {
      this.createValidate(account);
      account.setTenantCode(TenantUtils.getTenantCode());
    }
    this.accountRepository.saveBatch(accounts);
  }

  /**
   * 修改新据
   *
   * @param account 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public Account update(Account account) {
    this.updateValidate(account);
    Account dbAccount = this.accountRepository.findByIdAndTenantCode(account.getId(),TenantUtils.getTenantCode());
    Validate.notNull(account,"修改数据不存在，请检查！");
    AccountVo oldAccountVo = this.nebulaToolkitService.copyObjectByWhiteList(dbAccount,AccountVo.class, LinkedHashSet.class, ArrayList.class);
    account.setTenantCode(TenantUtils.getTenantCode());
    this.accountRepository.saveOrUpdate(account);
    AccountVo accountVo = this.nebulaToolkitService.copyObjectByWhiteList(account, AccountVo.class, LinkedHashSet.class, ArrayList.class);
    //编辑业务日志
    AccountLogEventDto logEventDto = new AccountLogEventDto();
    logEventDto.setOriginal(oldAccountVo);
    logEventDto.setNewest(accountVo);
    SerializableBiConsumer<AccountLogEventListener, AccountLogEventDto> onUpdate =
            AccountLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(logEventDto, AccountLogEventListener.class, onUpdate);
    return account;
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void deleteBatch(List<String> idList) {
    Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
    List<Account> accounts = this.accountRepository.findByIds(idList);
    if(CollectionUtils.isEmpty(accounts)){
      return;
    }
    this.accountRepository.deleteBatch(idList);
    Collection<AccountVo> accountVos = this.nebulaToolkitService.copyCollectionByWhiteList(accounts, Account.class, AccountVo.class, LinkedHashSet.class, ArrayList.class);
    //删除业务日志
    SerializableBiConsumer<AccountLogEventListener, AccountLogEventDto> onDelete =
            AccountLogEventListener::onDelete;
    for (AccountVo accountVo : accountVos) {
      AccountLogEventDto logEventDto = new AccountLogEventDto();
      logEventDto.setOriginal(accountVo);
      this.nebulaNetEventClient.publish(logEventDto, AccountLogEventListener.class, onDelete);
    }
  }

  /**
   * 创建验证
   *
   * @param account
   */
  private void createValidate(Account account) {
    Validate.notNull(account, "新增时，对象信息不能为空！");
    account.setId(null);
    account.setTenantCode(TenantUtils.getTenantCode());
    account.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    account.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    if (StringUtils.isBlank(account.getAccountStatus())){
      account.setAccountStatus(AccountStatusEnum.ON.getDictCode());
      account.setAccountTime(new Date());
    }
    Validate.notNull(account.getAccountCode(), "新增数据时，费用上账编码 不能为空！");
    Validate.notNull(account.getActivitiesCode(), "新增数据时，活动编号 不能为空！");
    Validate.notNull(account.getAmount(), "新增数据时，上账金额 不能为空！");
    Validate.notNull(account.getAuditCode(), "新增数据时，核销编号 不能为空！");
    Validate.notNull(account.getAuditDetailCode(), "新增数据时，核销明细编号 不能为空！");
    Validate.notNull(account.getAvailableAmount(), "新增数据时，可上账金额 不能为空！");
    Validate.notNull(account.getBeginTime(), "新增数据时，开始时间 不能为空！");
    Validate.notNull(account.getBudgetSubjectsCode(), "新增数据时，预算科目编码 不能为空！");
    Validate.notNull(account.getCostTypeCategoryCode(), "新增数据时，活动大类编码 不能为空！");
    Validate.notNull(account.getCostTypeDetailCode(), "新增数据时，活动细类编码 不能为空！");
    Validate.notNull(account.getEndTime(), "新增数据时，结束时间 不能为空！");

  }

  /**
   * 修改验证
   *
   * @param account
   */
  private void updateValidate(Account account) {
    account.setTenantCode(TenantUtils.getTenantCode());
    account.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    account.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    if (StringUtils.isBlank(account.getAccountStatus())){
      account.setAccountStatus(AccountStatusEnum.ON.getDictCode());
      account.setAccountTime(new Date());
    }
    Validate.notNull(account, "修改时，对象信息不能为空！");
    Validate.notBlank(account.getId(), "修改数据时，ID不能为空！");
    Validate.notBlank(account.getAccountCode(), "修改数据时，费用上账编码 不能为空！");
    Validate.notBlank(account.getActivitiesCode(), "修改数据时，活动编号 不能为空！");
    Validate.notNull(account.getAmount(), "修改数据时，上账金额 不能为空！");
    Validate.notBlank(account.getAuditCode(), "修改数据时，核销编号 不能为空！");
    Validate.notBlank(account.getAuditDetailCode(), "修改数据时，核销明细编号 不能为空！");
    Validate.notNull(account.getBeginTime(), "修改数据时，开始时间 不能为空！");
    Validate.notBlank(account.getBudgetSubjectsCode(), "修改数据时，预算科目编码 不能为空！");
    Validate.notBlank(account.getCostTypeCategoryCode(), "修改数据时，活动大类编码 不能为空！");
    Validate.notBlank(account.getCostTypeDetailCode(), "修改数据时，活动细类编码 不能为空！");
    Validate.notNull(account.getEndTime(), "修改数据时，结束时间 不能为空！");

  }
}

