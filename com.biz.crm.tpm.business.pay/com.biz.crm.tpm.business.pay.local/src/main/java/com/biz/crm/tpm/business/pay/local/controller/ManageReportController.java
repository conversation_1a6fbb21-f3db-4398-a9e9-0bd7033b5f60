package com.biz.crm.tpm.business.pay.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.sdk.service.ManageReportService;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "管报收入")
@RestController
@RequestMapping("/v1/manageReport")
@Slf4j
public class ManageReportController {

    @Autowired(required = false)
    private ManageReportService manageReportService;

    /**
     * 删除数据
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据")
    @PostMapping
    public Result<?> delete(@ApiParam(name = "ids", value = "主键集合") @RequestBody List<String> ids) {
        try {
            this.manageReportService.delete(ids);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @PostMapping("findManageListByCondition")
    @ApiOperation(value = "通过条件查询")
    public Result<List<ManageReportVo>> findManageListByCondition(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(manageReportService.findListByCondition(dto));
    }
}
