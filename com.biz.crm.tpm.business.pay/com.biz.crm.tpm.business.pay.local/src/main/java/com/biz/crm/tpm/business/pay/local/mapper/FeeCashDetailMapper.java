package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashDetail;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.BillingCompareReportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 费用兑付明细(FeeCashDetail)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
public interface FeeCashDetailMapper extends BaseMapper<FeeCashDetail> {

    List<FeeCashDetail> findByAuditDetailCodesBeCommit(@Param("codes") List<String> codes);

    List<FeeCashDetail> findByAuditDetailCodesBeClose(@Param("codes") List<String> codes);

    List<FeeCashDetailVo> findByAuditDetailCodesPass(@Param("codes") List<String> codes, @Param("status") String status, @Param("cashDetailCodes") List<String> cashDetailCodes,
                                                     @Param("cashTypeList") List<String> cashTypeList);

    List<FeeCashDetailVo> findByBillingCompareReport(@Param("dto") BillingCompareReportVo dto);

    List<FeeCashDetailVo> findAuditCashAmount(@Param("auditDetailCodes") List<String> auditDetailCodes);

    BigDecimal getAllDuifuJeBAuditDetailCode(@Param("auditDetailCode") String auditDetailCode);

    BigDecimal getAllYuFuAvailableJe(@Param("auditDetailCode") String auditDetailCode,@Param("cashCode") String cashCode);

    Page<FeeCashDetailVo> findFullAuditedDetailList(@Param("page") Page<FeeCashDetailVo> page, @Param("dto") FeeCashDetailDto dto);

    List<FeeCashDetailVo> findAuditedDetailList(@Param("dto") FeeCashDetailDto dto);
}

