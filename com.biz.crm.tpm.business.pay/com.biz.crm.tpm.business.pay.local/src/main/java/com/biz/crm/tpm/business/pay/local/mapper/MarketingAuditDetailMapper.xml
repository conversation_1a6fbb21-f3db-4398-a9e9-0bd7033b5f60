<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.MarketingAuditDetailMapper">

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        select t.*,ta.status
        from tpm_marketing_audit_detail t
        inner join tpm_marketing_audit ta on t.audit_code=ta.audit_code
        <choose>
            <when test="'Y'.toString() == dto.costCloseFlag">
                left join tpm_marketing_plan_case a on t.scheme_detail_code = a.scheme_detail_code
            </when>
        </choose>
        <where>
            t.del_flag = '${@<EMAIL>()}'
            and ta.del_flag = '${@<EMAIL>()}'
            <if test="dto.status != null and dto.status != ''">
                and ta.status = #{dto.status}
            </if>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.auditName != null and dto.auditName != ''">
                <bind name="auditName" value="'%' + dto.auditName + '%'"/>
                and t.audit_name like #{auditName}
            </if>
            <if test="dto.auditCode != null and dto.auditCode != ''">
                and t.audit_code = #{dto.auditCode}
            </if>
            <if test="dto.auditDetailCode != null and dto.auditDetailCode != ''">
                and t.audit_detail_code = #{dto.auditDetailCode}
            </if>
            <if test="dto.schemeDetailCode != null and dto.schemeDetailCode != ''">
                and t.scheme_detail_code = #{dto.schemeDetailCode}
            </if>
            <if test="dto.schemeCode != null and dto.schemeCode != ''">
                and t.scheme_code = #{dto.schemeCode}
            </if>
            <if test="dto.schemeName != null and dto.schemeName != ''">
                <bind name="schemeName" value="'%' + dto.schemeName + '%'"/>
                and t.scheme_name like #{schemeName}
            </if>
            <if test="dto.actName != null and dto.actName != ''">
                <bind name="actName" value="'%' + dto.actName + '%'"/>
                and t.act_name like #{actName}
            </if>
            <if test="dto.years != null and dto.years != ''">
                and t.years = #{dto.years}
            </if>
            <if test="dto.status != null and dto.status != ''">
                and ta.status = #{dto.status}
            </if>
            <if test="dto.companyCode != null and dto.companyCode != ''">
                and t.company_code = #{dto.companyCode}
            </if>
            <if test="dto.cashType != null and dto.cashType != ''">
                and t.cash_type = #{dto.cashType}
            </if>
            <if test="dto.cashTypeExclude != null and dto.cashTypeExclude != ''">
                and t.cash_type != #{dto.cashTypeExclude}
            </if>
            <if test="dto.beFullCash != null and dto.beFullCash != ''">
                and t.be_full_cash = #{dto.beFullCash}
            </if>
            <if test="dto.beFullAudit != null and dto.beFullAudit != ''">
                and t.be_full_audit = #{dto.beFullAudit}
            </if>
            <if test="dto.bePrepay != null and dto.bePrepay != ''">
                and t.be_prepay = #{dto.bePrepay}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                <bind name="customerCode" value="'%' + dto.customerCode + '%'"/>
                and t.customer_code like #{customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                <bind name="customerName" value="'%' + dto.customerName + '%'"/>
                and t.customer_name like #{customerName}
            </if>
            <if test="dto.customerCodes != null and dto.customerCodes.size > 0">
                and t.customer_code in
                <foreach item="item" collection="dto.customerCodes" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="dto.belongDepartmentCode != null and dto.belongDepartmentCode != ''">
                and t.belong_department_code = #{dto.belongDepartmentCode}
            </if>
            <if test="dto.belongDepartmentName != null and dto.belongDepartmentName != ''">
                <bind name="belongDepartmentName" value="'%' + dto.belongDepartmentName + '%'"/>
                and t.belong_department_name like #{belongDepartmentName}
            </if>
            <if test="dto.bearDepartmentCode != null and dto.bearDepartmentCode != ''">
                and t.bear_department_code = #{dto.bearDepartmentCode}
            </if>
            <if test="dto.bearDepartmentName != null and dto.bearDepartmentName != ''">
                <bind name="bearDepartmentName" value="'%' + dto.bearDepartmentName + '%'"/>
                and t.bear_department_name like #{bearDepartmentName}
            </if>
            <if test="dto.belongDepartmentCodeList != null and dto.belongDepartmentCodeList.size > 0">
                and t.belong_department_code in
                <foreach item="item" collection="dto.belongDepartmentCodeList" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="dto.belongDepartmentNameList != null and dto.belongDepartmentNameList.size > 0">
                and t.belong_department_name in
                <foreach item="item" collection="dto.belongDepartmentNameList" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="dto.terminalName != null and dto.terminalName != ''">
                <bind name="terminalName" value="'%' + dto.terminalName + '%'"/>
                and t.terminal_name like #{terminalName}
            </if>
            <if test="dto.costCenterName != null and dto.costCenterName != ''">
                <bind name="costCenterName" value="'%' + dto.costCenterName + '%'"/>
                and t.cost_center_name like #{costCenterName}
            </if>
            <if test="dto.actDesc != null and dto.actDesc != ''">
                <bind name="actDesc" value="'%' + dto.actDesc + '%'"/>
                and t.act_desc like #{actDesc}
            </if>
            <if test="dto.detailName != null and dto.detailName != ''">
                <bind name="detailName" value="'%' + dto.detailName + '%'"/>
                and t.detail_name like #{detailName}
            </if>
            <if test="dto.detailCodeList != null and dto.detailCodeList.size > 0">
                and t.detail_code in
                <foreach item="item" collection="dto.detailCodeList" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="dto.createAccount != null and dto.createAccount != ''">
                <bind name="createAccount" value="'%' + dto.createAccount + '%'"/>
                and t.create_account like #{createAccount}
            </if>
            <if test="dto.createName != null and dto.createName != ''">
                <bind name="createName" value="'%' + dto.createName + '%'"/>
                and t.create_name like #{createName}
            </if>
            <if test="dto.overDay != null and dto.overDay != ''">
                and t.over_day &lt;= #{dto.overDay}
            </if>
            <if test="dto.auditDetailCodeList != null and dto.auditDetailCodeList.size > 0">
                and t.audit_detail_code in
                <foreach item="item" collection="dto.auditDetailCodeList" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="dto.hzlx != null and dto.hzlx != ''">
                and t.hzlx = #{dto.hzlx}
            </if>
            <if test="dto.yearsSet != null and dto.yearsSet.size > 0">
                and t.years in
                <foreach item="item" collection="dto.yearsSet" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="dto.subjectCodes != null and dto.subjectCodes.size > 0">
                and t.budget_subject_code in
                <foreach item="item" collection="dto.subjectCodes" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="dto.cashTypeSet != null and dto.cashTypeSet.size > 0">
                and t.cash_type in
                <foreach item="item" collection="dto.cashTypeSet" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="dto.dtoList != null and dto.dtoList.size > 0">
                and ta.status='3'
                and t.audit_amount > 0
                and
                <foreach collection="dto.dtoList" item="item" open="(" close=")" separator="or">
                    (
                    t.customer_code=#{item.customerCode}
                    <if test="item.budgetSubjectCode != null and item.budgetSubjectCode != ''">
                        and t.budget_subject_code=#{item.budgetSubjectCode}
                    </if>
                    <if test="item.years != null and item.years != ''">
                        and t.end_date like concat(#{item.years}, '%')
                    </if>
                    <if test="item.cashType != null and item.cashType != ''">
                        and t.cash_type=#{item.cashType}
                    </if>
                    )
                </foreach>
            </if>
            <choose>
                <when test="'Y'.toString() == dto.materialPurchaseFlag">
                    and t.audit_detail_code not in (
                        SELECT
                        b.audit_detail_code
                        FROM
                        tpm_fee_cash a
                        inner JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
                        WHERE
                        a.cash_type != 'material'
                    )
                    and t.scheme_detail_code not in (
                        SELECT
                        b.scheme_detail_code
                        FROM
                        tpm_activity_prepay a
                        INNER JOIN tpm_activity_prepay_detail b ON a.prepay_code = b.prepay_code
                        WHERE
                        a.del_flag = '009'
                    )
                </when>
                <otherwise>
                    and t.audit_detail_code not in (
                        SELECT
                        b.audit_detail_code
                        FROM
                        tpm_fee_cash a
                        inner JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
                        WHERE
                        a.cash_type = 'material'
                    )
                </otherwise>
            </choose>
            <choose>
                <when test="'Y'.toString() == dto.costCloseFlag">
                    and a.case_type not in
                    <foreach collection="dto.caseTypeList" separator="," item="item" index="index" open="(" close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
        </where>
        order by t.create_time desc, t.id desc
    </select>

    <select id="findByDtoList" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        select t.*
        from tpm_marketing_audit_detail t
        inner join tpm_marketing_audit ta on t.audit_code=ta.audit_code
        <where>
            t.del_flag = '${@<EMAIL>()}'
            and ta.del_flag = '${@<EMAIL>()}'
            and t.be_full_cash='N'
            and ta.status='3'
            and t.audit_amount > 0
            <if test="dtoList != null and dtoList.size > 0">
                and
                <foreach collection="dtoList" item="item" open="(" close=")" separator="or">
                    (
                    t.customer_code=#{item.customerCode}
                    <if test="item.budgetSubjectCode != null and item.budgetSubjectCode != ''">
                        and t.budget_subject_code=#{item.budgetSubjectCode}
                    </if>
                    <if test="item.years != null and item.years != ''">
                        and t.end_date like concat(#{item.years}, '%')
                    </if>
                    <if test="item.cashType != null and item.cashType != ''">
                        and t.cash_type=#{item.cashType}
                    </if>
                    )
                </foreach>
            </if>
        </where>
        order by t.years desc, t.id
    </select>

    <select id="findForReplenishmentPool" resultType="com.biz.crm.tpm.business.pay.local.entity.MarketingAuditDetail">
        select t.*
        from tpm_marketing_audit_detail t
        inner join tpm_marketing_audit ta on t.audit_code=ta.audit_code
        <where>
            t.del_flag = '${@<EMAIL>()}'
            and ta.del_flag = '${@<EMAIL>()}'
            and t.be_full_cash='N'
            and ta.status='3'
            and t.cash_type='replenishment'
        </where>
        order by t.years desc, t.id
    </select>

    <select id="findBySchemeDetailCodesBeCommit" resultType="com.biz.crm.tpm.business.pay.local.entity.MarketingAuditDetail">
        select t.*
        from tpm_marketing_audit_detail t
        inner join tpm_marketing_audit ta on t.audit_code=ta.audit_code
        <where>
            t.del_flag = '${@<EMAIL>()}'
            and ta.del_flag = '${@<EMAIL>()}'
            and ta.status != '3'
            AND t.scheme_detail_code in
            <foreach collection="codes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findBySchemeDetailCodesAll" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        select t.*
        from tpm_marketing_audit_detail t
        inner join tpm_marketing_audit ta on t.audit_code=ta.audit_code
        <where>
            t.del_flag = '${@<EMAIL>()}'
            and ta.del_flag = '${@<EMAIL>()}'
            and ta.status = '3'
            AND t.scheme_detail_code in
            <foreach collection="codes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findCommitAndPassBySchemeDetailCodes" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        select t.*, ta.`status`
        from tpm_marketing_audit_detail t
        inner join tpm_marketing_audit ta on t.audit_code=ta.audit_code
        <where>
            t.del_flag = '${@<EMAIL>()}'
            and ta.del_flag = '${@<EMAIL>()}'
            and ta.status in ('2', '3')
            AND t.scheme_detail_code in
            <foreach collection="codes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="updateCashAmount">
        <foreach item="dto" collection="dtoList" index="index" open="" separator=";" close="">
            update tpm_marketing_audit_detail
            set cash_amount = #{dto.cashAmount},
                be_full_cash = #{dto.beFullCash}
            where audit_detail_code = #{dto.auditDetailCode}
        </foreach>
    </select>


    <select id="findAuditAmountBySchemeDetailCodes" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        SELECT
        CASE
        WHEN SUM(CASE WHEN be_full_audit = 'Y' THEN 1 ELSE 0 END) > 0 THEN SUM(audit_amount)
        ELSE 0
        END AS audit_amount,scheme_detail_code
        FROM
        tpm_marketing_audit_detail a LEFT JOIN tpm_marketing_audit b on a.audit_code = b.audit_code
        <where>
            b.status = '3'
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                and a.scheme_detail_code in
                <foreach collection="schemeDetailCodes" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            group by a.scheme_detail_code
        </where>
    </select>

    <select id="findListBySchemeDetailCodes" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        SELECT
        sum(a.audit_amount) audit_amount ,scheme_detail_code
        FROM
        tpm_marketing_audit_detail a LEFT JOIN tpm_marketing_audit b on a.audit_code = b.audit_code
        <where>
            b.del_flag = '009'
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                and a.scheme_detail_code in
                <foreach collection="schemeDetailCodes" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            group by a.scheme_detail_code
        </where>
    </select>


    <select id="findListBySchemeDetailCodesApproved" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        SELECT
        sum(a.audit_amount) audit_amount ,scheme_detail_code
        FROM
        tpm_marketing_audit_detail a LEFT JOIN tpm_marketing_audit b on a.audit_code = b.audit_code
        <where>
            b.del_flag = '009'
            and b.status = '3'
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                and a.scheme_detail_code in
                <foreach collection="schemeDetailCodes" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            group by a.scheme_detail_code
        </where>
    </select>
    <select id="scheduleNotClosureDetailMsgPush"
            resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        select
        tmad.scheme_code,
        tmad.scheme_detail_code,
        tmpc.end_date as activityEndDate,
        tmpc.create_account as activityCreateAccount,
        tmad.detail_code
        from tpm_marketing_audit_detail tmad
        LEFT JOIN tpm_marketing_plan_case tmpc on tmad.scheme_detail_code = tmpc.scheme_detail_code
        where
            tmpc.audit_status in ('not_audit', 'part_audit')
            and now() >= DATE_ADD(tmpc.end_date,INTERVAL 80 DAY) and NOW() <![CDATA[ <= ]]> DATE_ADD(tmpc.end_date,INTERVAL 90 DAY)
    </select>
    <select id="scheduleNotReplenishmentMsgPush"
            resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        select
        tmad.scheme_code,
        tmad.scheme_detail_code,
        tmpc.end_date as activityEndDate,
        tmpc.create_account as activityCreateAccount,
        tmad.detail_code
        from tpm_marketing_audit_detail tmad
        LEFT JOIN tpm_marketing_plan_case tmpc on tmad.scheme_detail_code = tmpc.scheme_detail_code
        where
            tmpc.audit_status in ('part_audit', 'whole_audit')
            and now() >= DATE_ADD(tmpc.end_date,INTERVAL 170 DAY) and NOW() <![CDATA[ <= ]]> DATE_ADD(tmpc.end_date,INTERVAL 180 DAY)
        and not exists (
        select 1 from tpm_fee_cash_detail tfcd
        LEFT JOIN tpm_fee_cash tfc on tfcd.cash_code = tfc.cash_code
        where tmad.scheme_detail_code = tfcd.scheme_detail_code
        )
    </select>

</mapper>

