package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.service.PosDataService;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@RestController
@RequestMapping("/v1/posDataController")
@Api(tags = "POS数据")
public class PosDataController {

    @Autowired
    private PosDataService posDataService;


    @ApiOperation(value = "查询列表")
    @GetMapping("findList")
    public Result<Page<PosDataVo>> findList(@PageableDefault(50) Pageable pageable, PosDataVo vo) {
        return Result.ok(posDataService.findList(pageable, vo));
    }

    @ApiOperation(value = "确认")
    @PostMapping("confirmData")
    public Result confirmData(@RequestBody List<String> idList){
        posDataService.confirmData(idList);
        return Result.ok();
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("deleteBatchByIdList")
    public Result deleteBatchByIdList(@RequestParam List<String> ids){
        posDataService.deleteBatchByIdList(ids);
        return Result.ok();
    }
}
