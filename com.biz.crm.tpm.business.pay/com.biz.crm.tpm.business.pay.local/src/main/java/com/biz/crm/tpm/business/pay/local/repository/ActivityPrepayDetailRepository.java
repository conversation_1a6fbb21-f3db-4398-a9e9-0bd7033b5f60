package com.biz.crm.tpm.business.pay.local.repository;



import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail;
import com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayDetailMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;


/**
 * 活动预付明细(ActivityPrepayDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-04 20:03:02
 */
@Component
public class ActivityPrepayDetailRepository extends ServiceImpl<ActivityPrepayDetailMapper, ActivityPrepayDetail> {

  @Autowired
  private ActivityPrepayDetailMapper activityPrepayDetailMapper;

  /**
   * 根据编码查询
   *
   * @param code
   * @return
   */
  public List<ActivityPrepayDetail> findByCode(String code) {
    return this.lambdaQuery().eq(ActivityPrepayDetail::getPrepayCode, code)
            .eq(ActivityPrepayDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivityPrepayDetail::getTenantCode, TenantUtils.getTenantCode()).list();
  }

  /**
   * 根据编码集合查询
   *
   * @param codes
   * @return
   */
  public List<ActivityPrepayDetail> findByCodes(List<String> codes) {
    return this.lambdaQuery().in(ActivityPrepayDetail::getPrepayCode, codes)
            .eq(ActivityPrepayDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivityPrepayDetail::getTenantCode, TenantUtils.getTenantCode()).list();
  }

  /**
   * 根据codeList删除
   *
   * @param codeList
   */
  public void deleteByCodeList(List<String> codeList) {
    this.lambdaUpdate().in(ActivityPrepayDetail::getPrepayCode, codeList)
            .remove();
  }

  /**
   *
   * 活动预付——预付结转计算查询
   *
   * @param vo
   * @return
   */
  public List<ActivityPrepayDetail> findCalList(ActivityPrepayVo vo) {
    return this.baseMapper.findCalList(vo);
  }

  /**
   * 活动预付明细 - 根据活动明细编码查询已经关联的活动预付供应商编码
   * @param detailSchemeDetailCodeSet
   * @return
   */
  public List<ActivityPrepayDetail> findBySchemeDetailCodes(Set<String> detailSchemeDetailCodeSet) {
    return this.lambdaQuery().in(ActivityPrepayDetail::getSchemeDetailCode, detailSchemeDetailCodeSet)
            .eq(ActivityPrepayDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivityPrepayDetail::getTenantCode, TenantUtils.getTenantCode()).list();
  }

  /**
   * 按活动明细编码查询是否有审批中
   *
   * @param codes
   * @return
   */
  public List<ActivityPrepayDetail> findBySchemeDetailCodesBeCommit(List<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return Lists.newArrayList();
    }

    return activityPrepayDetailMapper.findBySchemeDetailCodesBeCommit(codes);
  }
}

