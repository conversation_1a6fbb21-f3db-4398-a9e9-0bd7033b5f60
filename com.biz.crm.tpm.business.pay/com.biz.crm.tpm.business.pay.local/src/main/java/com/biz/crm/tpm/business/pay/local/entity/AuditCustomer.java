package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 实体：费用明细客户;
 * <AUTHOR> Keller
 * @date : 2022-6-27
 */
@ApiModel(value = "AuditCustomer",description = "费用明细客户")
@TableName("tpm_audit_customer")
@Getter
@Setter
@Entity(name = "tpm_audit_customer")
@org.hibernate.annotations.Table(appliesTo = "tpm_audit_customer", comment = "费用明细客户")
@Table(name = "tpm_audit_customer")
public class AuditCustomer  extends TenantEntity {

  /** 费用核销编号 */
  @ApiModelProperty(name = "费用核销编号",notes = "费用核销编号")
  @Column(name = "audit_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '费用核销编号 '")
  private String auditCode;

  /** 费用核销明细编号 */
  @ApiModelProperty(name = "费用核销明细编号",notes = "费用核销明细编号")
  @Column(name = "audit_detail_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '费用核销明细编号 '")
  private String auditDetailCode;

  /** 客户编码 */
  @ApiModelProperty(name = "客户编码",notes = "客户编码编码")
  @Column(name = "customer_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '客户编码 '")
  private String customerCode;

  /** 客户名称 */
  @ApiModelProperty(name = "客户名称",notes = "客户名称")
  @Column(name = "customer_name", nullable = true, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '客户名称 '")
  private String customerName;

  /** 核销金额 */
  @ApiModelProperty(name = "核销金额",notes = "核销金额")
  @Column(name = "audit_amount", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '核销金额 '")
  private BigDecimal auditAmount;

}
