package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.rocketmq.service.RocketMqProducer;
import com.biz.crm.business.common.rocketmq.util.RocketMqUtil;
import com.biz.crm.business.common.rocketmq.vo.MqMessageVo;
import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.pay.local.entity.*;
import com.biz.crm.tpm.business.pay.local.repository.*;
import com.biz.crm.tpm.business.pay.sdk.constant.ActivityPrepayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.*;
import com.biz.crm.tpm.business.pay.sdk.dto.log.ActivityPrepayLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.enums.HecPayStatusTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.TpmPrepayTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.event.log.ActivityPrepayLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayDetailRecordService;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepaySendHecService;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayService;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 活动预付
 */
@Service
@Slf4j
public class ActivityPrepayServiceImpl extends BusinessPageCacheServiceImpl<ActivityPrepayDetailVo, ActivityPrepayDetailDto> implements ActivityPrepayService {

    @Autowired(required = false)
    private ActivityPrepayRepository activityPrepayRepository;

    @Autowired(required = false)
    private ActivityPrepayDetailRepository activityPrepayDetailRepository;

    @Autowired(required = false)
    private ActivityPrepayPayeeRepository activityPrepayPayeeRepository;

    @Autowired(required = false)
    private ActivityPrepayFilesRepository activityPrepayFilesRepository;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private ActivityPrepaySendHecService activityPrepaySendHecService;

    @Autowired(required = false)
    private ActivityPrepayDetailRecordService activityPrepayDetailRecordService;

    @Autowired(required = false)
    private ActivityPrepayDetailRecordItemRepository activityPrepayDetailRecordItemRepository;

    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;

    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;


    /**
     * 通过编码查询单条数据
     *
     * @param code 编码
     * @return 单条数据
     */
    @Override
    public ActivityPrepayVo findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        ActivityPrepay entity = activityPrepayRepository.findByCode(code);
        if (null == entity) {
            return null;
        }
        ActivityPrepayVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, ActivityPrepayVo.class, HashSet.class, ArrayList.class);
        List<ActivityPrepayDetail> list = activityPrepayDetailRepository.findByCode(vo.getPrepayCode());
        List<ActivityPrepayDetailVo> voList = (List<ActivityPrepayDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(list, ActivityPrepayDetail.class, ActivityPrepayDetailVo.class, HashSet.class, ArrayList.class);
        List<ActivityPrepayPayee> listPayee = activityPrepayPayeeRepository.findByCode(vo.getPrepayCode());
        List<ActivityPrepayPayeeVo> voListPayee = (List<ActivityPrepayPayeeVo>) nebulaToolkitService.copyCollectionByWhiteList(listPayee, ActivityPrepayPayee.class, ActivityPrepayPayeeVo.class, HashSet.class, ArrayList.class);
        List<ActivityPrepayFiles> listFiles = activityPrepayFilesRepository.findByCode(vo.getPrepayCode());
        List<ActivityPrepayFilesVo> voListFiles = (List<ActivityPrepayFilesVo>) nebulaToolkitService.copyCollectionByWhiteList(listFiles, ActivityPrepayFiles.class, ActivityPrepayFilesVo.class, HashSet.class, ArrayList.class);
        vo.setDetailList(voList);
        vo.setPayeeList(voListPayee);
        vo.setFilesList(voListFiles);
        return vo;
    }

    /**
     * 新增数据
     *
     * @param dto dto对象
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    @Override
    public void create(ActivityPrepayDto dto, String cacheKey, boolean commit) {
        ActivityPrepayVo vo = nebulaToolkitService.copyObjectByWhiteList(dto, ActivityPrepayVo.class, HashSet.class, ArrayList.class);
        vo.setCacheKey(cacheKey);
        vo.setPayeeList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(dto.getPayeeList(), ActivityPrepayPayeeDto.class, ActivityPrepayPayeeVo.class, HashSet.class, ArrayList.class)));
        ActivityPrepayVo cal = activityPrepayDetailRecordService.cal(vo);
        dto.setPayeeList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(cal.getPayeeList(), ActivityPrepayPayeeVo.class, ActivityPrepayPayeeDto.class, HashSet.class, ArrayList.class)));
        List<ActivityPrepayDetailDto> cacheList = findCacheList(cacheKey);
        dto.setDetailList(cacheList);
        createValidate(dto);
        // redis生成料编码code
        List<String> codeList = this.generateCodeService.generateCode(ActivityPrepayConstant.PREFIX_CODE, 1);
        dto.setPrepayCode(codeList.get(0));
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setStatus(ProcessStatusEnum.PREPARE.getDictCode());
        dto.setPayStatus(HecPayStatusTypeEnum.NOT_PAY.getCode());

        store(dto);

        if (!commit) {
            clearCache(cacheKey);
        }

        //新增业务日志
        ActivityPrepayLogEventDto logEventDto = new ActivityPrepayLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(dto);
        SerializableBiConsumer<ActivityPrepayLogEventListener, ActivityPrepayLogEventDto> onCreate =
                ActivityPrepayLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, ActivityPrepayLogEventListener.class, onCreate);
    }

    /**
     * 修改新据
     *
     * @param dto dto对象
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ActivityPrepayDto dto, String cacheKey, boolean commit) {
        ActivityPrepayVo vo = nebulaToolkitService.copyObjectByWhiteList(dto, ActivityPrepayVo.class, HashSet.class, ArrayList.class);
        vo.setCacheKey(cacheKey);
        vo.setPayeeList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(dto.getPayeeList(), ActivityPrepayPayeeDto.class, ActivityPrepayPayeeVo.class, HashSet.class, ArrayList.class)));
        ActivityPrepayVo cal = activityPrepayDetailRecordService.cal(vo);
        dto.setPayeeList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(cal.getPayeeList(), ActivityPrepayPayeeVo.class, ActivityPrepayPayeeDto.class, HashSet.class, ArrayList.class)));
        List<ActivityPrepayDetailDto> cacheList = findCacheList(cacheKey);
        dto.setDetailList(cacheList);
        updateValidate(dto);
        ActivityPrepay entityOld = activityPrepayRepository.findById(dto.getId());
        ActivityPrepay activityPrepay = nebulaToolkitService.copyObjectByWhiteList(dto, ActivityPrepay.class, HashSet.class, ArrayList.class);

        activityPrepayRepository.saveOrUpdate(activityPrepay);
        activityPrepayDetailRepository.deleteByCodeList(Collections.singletonList(activityPrepay.getPrepayCode()));
        activityPrepayPayeeRepository.deleteByCodeList(Collections.singletonList(activityPrepay.getPrepayCode()));
        activityPrepayFilesRepository.deleteByCodeList(Collections.singletonList(activityPrepay.getPrepayCode()));

        store(dto);

        if (!commit) {
            clearCache(cacheKey);
        }

        //编辑业务日志
        ActivityPrepayLogEventDto logEventDto = new ActivityPrepayLogEventDto();
        logEventDto.setOriginal(this.nebulaToolkitService.copyObjectByWhiteList(entityOld, ActivityPrepayDto.class, LinkedHashSet.class, ArrayList.class));
        logEventDto.setNewest(dto);
        SerializableBiConsumer<ActivityPrepayLogEventListener, ActivityPrepayLogEventDto> onUpdate =
                ActivityPrepayLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, ActivityPrepayLogEventListener.class, onUpdate);
    }

    /**
     * 保存
     *
     * @param dto
     */
    public void store(ActivityPrepayDto dto) {
        ActivityPrepay activityPrepay = nebulaToolkitService.copyObjectByWhiteList(dto, ActivityPrepay.class, HashSet.class, ArrayList.class);
        // redis生成料编码code
        List<String> codeList = this.generateCodeService.generateCode(ActivityPrepayConstant.PREFIX_CODE, dto.getDetailList().size());
        //明细
        List<ActivityPrepayDetailDto> details = dto.getDetailList();
        List<ActivityPrepayDetail> detailList = (List<ActivityPrepayDetail>) nebulaToolkitService.copyCollectionByWhiteList(details, ActivityPrepayDetailDto.class, ActivityPrepayDetail.class, HashSet.class, ArrayList.class);
        AtomicInteger index = new AtomicInteger(0);
        detailList.forEach(e -> {
            e.setId(null);
            e.setCompanyCode(dto.getCompanyCode());
            e.setPayeeCode(dto.getPayeeList().get(0).getPayeeCode());
            e.setPayeeName(dto.getPayeeList().get(0).getPayeeName());
            e.setPrepayCode(activityPrepay.getPrepayCode());
            e.setPrepayName(activityPrepay.getPrepayName());
            e.setTenantCode(TenantUtils.getTenantCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setPrepayDetailCode(codeList.get(index.get()));
            index.getAndAdd(1);
        });
        //收款
        List<ActivityPrepayPayeeDto> detailPayee = dto.getPayeeList();
        List<ActivityPrepayPayee> payeeList = (List<ActivityPrepayPayee>) nebulaToolkitService.copyCollectionByWhiteList(detailPayee, ActivityPrepayPayeeDto.class, ActivityPrepayPayee.class, HashSet.class, ArrayList.class);
        // redis生成料编码code
        List<String> payeeCodes = this.generateCodeService.generateCode(ActivityPrepayConstant.SK_DETAIL_PREFIX_CODE, payeeList.size());
        AtomicInteger indexPayee = new AtomicInteger(0);
        payeeList.forEach(e -> {
            e.setId(null);
            e.setPrepayCode(activityPrepay.getPrepayCode());
            e.setTenantCode(TenantUtils.getTenantCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setLineCode(payeeCodes.get(indexPayee.get()));
            indexPayee.getAndAdd(1);
        });
        //附件
        List<ActivityPrepayFilesDto> detailFiles = dto.getFilesList();
        if (!CollectionUtils.isEmpty(detailFiles)) {
            List<ActivityPrepayFiles> filesList = (List<ActivityPrepayFiles>) nebulaToolkitService.copyCollectionByWhiteList(detailFiles, ActivityPrepayFilesDto.class, ActivityPrepayFiles.class, HashSet.class, ArrayList.class);
            filesList.forEach(e -> {
                e.setPrepayCode(activityPrepay.getPrepayCode());
                e.setTenantCode(TenantUtils.getTenantCode());
            });
            activityPrepayFilesRepository.saveBatch(filesList);
        }
        activityPrepayRepository.saveOrUpdate(activityPrepay);
        activityPrepayDetailRepository.saveBatch(detailList);
        activityPrepayPayeeRepository.saveBatch(payeeList);
        dto.setId(activityPrepay.getId());
    }

    /**
     * 创建验证
     *
     * @param dto
     */
    private void createValidate(ActivityPrepayDto dto) {
        Validate.notNull(dto, "新增时，对象信息不能为空！");
        commonValidate(dto);
        dto.setId(null);
        FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
        Validate.notNull(loginUserDetails, "未找到当前登陆人信息！");
        dto.setOrgCode(loginUserDetails.getOrgCode());
        dto.setOrgName(loginUserDetails.getOrgName());
        dto.setPositionCode(loginUserDetails.getPostCode());
    }

    /**
     * 修改验证
     *
     * @param dto
     */
    private void updateValidate(ActivityPrepayDto dto) {
        Validate.notNull(dto, "修改时，对象信息不能为空！");
        commonValidate(dto);
        Validate.notBlank(dto.getId(), "主键id不能为空！");
    }

    /**
     * 公有校验
     *
     * @param dto
     */
    private void commonValidate(ActivityPrepayDto dto) {
        Validate.notBlank(dto.getPrepayName(), "预付名称，必填");
        Validate.notEmpty(dto.getDetailList(), "预付明细，不能为空");
        Validate.notEmpty(dto.getPayeeList(), "收款信息，不能为空");
        dto.getDetailList().forEach(e -> {
            Validate.notBlank(e.getSchemeDetailCode(), "活动编码，必填");
            Validate.notNull(e.getThisPrepayAmount(), "本次预付金额，必填");
            /*
                无可预付金额时校验：
                    1、请金额小于或等于可预付金额
                    2、可预付金额=申请金额-已兑付金额-预付可冲销金额
             */
            // BigDecimal availablePrepayAmount = e.getApplyAmount().subtract(e.getCashAmount()).subtract(e.getAvailableReversedAmount());
            Validate.isTrue(e.getThisPrepayAmount().compareTo(e.getAvailablePrepayAmount()) <= 0, "预付申请金额不能大于可预付金额");
            WithHolding byActivitiesDetailCode = withHoldingRepository.findByActivitiesDetailCode(e.getSchemeDetailCode());
            Validate.isTrue(null==byActivitiesDetailCode|| !ProcessStatusEnum.PASS.getDictCode().equals(byActivitiesDetailCode.getStatus()),"方案规划明细："+e.getSchemeDetailCode()+"已计提，不允许做活动预付！");
        });
        dto.getPayeeList().forEach(e -> {
            Validate.notBlank(e.getPayeeType(), "收款方类型，必填");
            Validate.notBlank(e.getPayeeName(), "收款方名称，必填");
            Validate.notBlank(e.getPayeeAccount(), "收款账号，必填");
            Validate.notBlank(e.getAccountName(), "户名，必填");
            Validate.notBlank(e.getInterbankNumber(), "银联行号，必填");
//            Validate.notBlank(e.getBankName(), "开户行，必填");
            Validate.notNull(e.getThisPayAmount(), "本次付款金额，必填");
            Validate.notNull(e.getPayDate(), "期望付款日期，必填");
            Validate.notBlank(e.getPayType(), "付款方式，必填");
        });

        // 供应商
        // 1044890 【活动预付】一个营销方案明细如果多次做活动预付，不允许每次活动预付的供应商不一致
        List<ActivityPrepayDetailDto> detailList = dto.getDetailList();
        Set<String> detailSchemeDetailCodeSet =
                detailList.stream().map(ActivityPrepayDetailDto::getSchemeDetailCode).filter(o -> StringUtils.isNotBlank(o))
                        .collect(Collectors.toSet());
        Validate.notEmpty(detailSchemeDetailCodeSet, "方案明细编码不能为空");
        List<MarketingAuditDetail> bySchemeDetailCodes = marketingAuditDetailRepository.findBySchemeDetailCodes(new ArrayList<>(detailSchemeDetailCodeSet));
        Validate.isTrue(CollectionUtils.isEmpty(bySchemeDetailCodes),"活动:"+bySchemeDetailCodes.stream().map(o->o.getSchemeDetailCode()).collect(Collectors.joining(","))+" 已结案，不允许做结案前预付！");
        // 通过方案明细编码查询活动预付对应供应商编码
//        List<ActivityPrepayDetail> prepayDetailsDb = activityPrepayDetailRepository.findBySchemeDetailCodes(detailSchemeDetailCodeSet);
//        if (!CollectionUtils.isEmpty(prepayDetailsDb)) {
//            // 针对更新，这里还需要排除掉当前活动预付自身
//            List<ActivityPrepayDetail> ExceptPrepayDetails = prepayDetailsDb.stream()
//                    .filter(detail -> !dto.getPayeeCode().equals(detail.getPayeeCode())
//                            && !detail.getPrepayCode().equals(dto.getPrepayCode()))
//                    .collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(ExceptPrepayDetails)) {
//                StringBuilder errorBuilder = new StringBuilder();
//                ExceptPrepayDetails.forEach(o -> {
//                    errorBuilder.append("营销方案明细【").append(o.getSchemeDetailCode()).append("】已关联供应商【").append(o.getPayeeName()).append("】\t");
//                });
//                Validate.isTrue(StringUtils.isBlank(errorBuilder.toString()), errorBuilder.toString());
//            }
//        }
        //活动预付保存的时候校验活动明细是否有待提交、审批中、驳回、撤回状态的活动预付单据
//        List<ActivityPrepayDetail> prepayDetails = activityPrepayDetailRepository.findBySchemeDetailCodesBeCommit(new ArrayList<>(detailSchemeDetailCodeSet));
//        if (CollectionUtil.isNotEmpty(prepayDetails)) {
//            //排除自己
//            List<ActivityPrepayDetail> filterPrepayDetails = prepayDetails.stream().filter(e -> StringUtils.isBlank(dto.getPrepayCode()) || !dto.getPrepayCode().equals(e.getPrepayCode())).collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty(filterPrepayDetails)) {
//                Set<String> prepayCodes = filterPrepayDetails.stream().map(e -> e.getPrepayCode()).collect(Collectors.toSet());
//                Set<String> filterSchemeDetailCodes = filterPrepayDetails.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toSet());
//                Validate.isTrue(false, "活动【%s】已在活动预付【%s】中存在，不允许保存", String.join(",", filterSchemeDetailCodes), String.join(",", prepayCodes));
//            }
//        }
    }


    /**
     * 更新流程状态
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessStatus(ActivityPrepayDto dto) {
        Assert.hasLength(dto.getPrepayCode(), "编号不能为空");
        if (StringUtil.isEmpty(dto.getStatus())) {
            return;
        }
        activityPrepayRepository.updateProcessStatus(dto);
        if (ProcessStatusEnum.PASS.getDictCode().equals(dto.getStatus())) {
            ActivityPrepay entity = activityPrepayRepository.findByCode(dto.getPrepayCode());
            if (entity == null) {
                return;
            }
            generateRecords(new HashSet<>(Arrays.asList(entity.getPrepayCode())));
        }
    }

    /**
     * 费控电汇付款状态回传
     *
     * @param dtoList
     */
    @Transactional
    @Override
    public void hecPayStatusCallback(List<HecCallbackDto> dtoList) {
        Validate.notEmpty(dtoList, "回传参数不能为空");
        Set<String> codes = new HashSet<>();
        Map<String, HecCallbackDto> dtoHeadList = Maps.newHashMap();
        dtoList.forEach(dto -> {
            Validate.notBlank(dto.getBusinessCode(), "单据编码不能为空");
            Validate.notBlank(dto.getBusinessDetailCode(), "单据明细编码不能为空");
            Validate.notBlank(dto.getOrderStatus(), "付款状态不能为空");
            dtoHeadList.put(dto.getBusinessCode(), dto);
            if (HecPayStatusTypeEnum.SUCCESS_PAY.getCode().equals(dto.getOrderStatus())) {
                codes.add(dto.getBusinessCode());
            }
            dto.setPaySucessDate(new Date());
        });
        activityPrepayPayeeRepository.hecPayStatusCallback(dtoList);
        activityPrepayRepository.hecPayStatusCallback(new ArrayList<>(dtoHeadList.values()));

        //同步更新对应预算明细跟踪付款状态
        activityPrepayDetailRecordService.updatePayStatus(dtoList);
    }

    @Override
    public List<String> rollbackModifyActivityPrepayRecordPre(ActivityPrepayDto dto) {
        List<ActivityPrepay> prepayList = this.activityPrepayRepository.findByProcessKey(dto);
        if (CollectionUtils.isEmpty(prepayList)) {
            return Lists.newArrayList();
        }
        List<String> prepayCodes = prepayList.stream().map(ActivityPrepay::getPrepayCode).collect(Collectors.toList());
        List<ActivityPrepayDetail> prepayDetailList = this.activityPrepayDetailRepository.findByCodes(prepayCodes);
        List<String> businessCodes = prepayDetailList.stream().map(e -> String.join("_", e.getPrepayDetailCode(), dto.getProcessKey())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(businessCodes)) {
            return Lists.newArrayList();
        }
        List<ActivityPrepayDetailRecordItem> recordItemList = activityPrepayDetailRecordItemRepository.findByBusinessCodes(businessCodes);
        return recordItemList.stream().map(ActivityPrepayDetailRecordItem::getPrepayCode).distinct().collect(Collectors.toList());
    }

    @Transactional
    @Override
    public void rollbackModifyActivityPrepayRecord(ActivityPrepayDto dto) {
        List<ActivityPrepay> prepayList = this.activityPrepayRepository.findByProcessKey(dto);
        if (CollectionUtils.isEmpty(prepayList)) {
            return;
        }
        List<String> prepayCodes = prepayList.stream().map(ActivityPrepay::getPrepayCode).collect(Collectors.toList());
        List<ActivityPrepayDetail> prepayDetailList = this.activityPrepayDetailRepository.findByCodes(prepayCodes);
        List<String> businessCodes = prepayDetailList.stream().map(e -> String.join("_", e.getPrepayDetailCode(), dto.getProcessKey())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(businessCodes)) {
            return;
        }
        List<ActivityPrepayDetailRecordItem> recordItemList = activityPrepayDetailRecordItemRepository.findByBusinessCodes(businessCodes);
        TenantFlagOpDto baseDto = new TenantFlagOpDto();
        baseDto.setTenantCode(TenantUtils.getTenantCode());
        baseDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        baseDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        List<ActivityPrepayDetailRecordItemDto> recordItemDtoList = new ArrayList<>();
        for (ActivityPrepayDetailRecordItem detailRecordItem : recordItemList) {
            //未审批通过，需要把预付明细操作记录还原
            if (!StringUtils.equals(ProcessStatusEnum.PASS.getDictCode(), dto.getStatus())) {
                ActivityPrepayDetailRecordItemDto itemDto = BeanUtil.copyProperties(detailRecordItem, ActivityPrepayDetailRecordItemDto.class);
                BeanUtils.copyProperties(baseDto, itemDto);
                itemDto.setAmount(itemDto.getAmount().negate());
                itemDto.setBusinessName("活动预付流程驳回还原");
                recordItemDtoList.add(itemDto);
            }
            //审批完成后把businessCode后面的流程编码去掉，防止再次提交审批时查到相同数据
            detailRecordItem.setBusinessCode(detailRecordItem.getBusinessCode().replace(StringUtils.join("_", dto.getProcessKey()), ""));
        }
        activityPrepayDetailRecordItemRepository.updateBatchById(recordItemList);
        activityPrepayDetailRecordService.operate(recordItemDtoList);
    }


    @Override
    public void compensateOaCallback(String prepayCode) {
        generateRecords(Sets.newHashSet(prepayCode));

        ActivityPrepayDto dto = new ActivityPrepayDto();
        dto.setPrepayCode(prepayCode);
        dto.setStatus(ProcessStatusEnum.PASS.getDictCode());

        sendMsg(dto);
    }


    @Autowired(required = false)
    private RocketMqProducer rocketMqProducer;

    private void sendMsg(ActivityPrepayDto dto) {
        try {
            ModifyPrepayRecordDto recordDto = new ModifyPrepayRecordDto();
            recordDto.setPrepayJson(JSON.toJSONString(dto));
            MqMessageVo mqMessageVo = new MqMessageVo();
            mqMessageVo.setTopic(MqConstant.TPM_ACTIVITY_PREPAY_RECORD + RocketMqUtil.mqEnvironment());
            mqMessageVo.setRepeatConsumer(false);
            mqMessageVo.setTag(MqConstant.TPM_ACTIVITY_PREPAY_RECORD);
            mqMessageVo.setMsgBody(JSON.toJSONString(recordDto));
            mqMessageVo.setMsgNum(1);
            String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_DATE_PATTERN));
            this.rocketMqProducer.sendMqOrderMsg(mqMessageVo, StringUtils.join(dto, date));
        } catch (Exception e) {
            log.error("topic [{}] tag [{}] 发送异常：{}", MqConstant.TPM_ACTIVITY_PREPAY_RECORD, MqConstant.TPM_ACTIVITY_PREPAY_RECORD, e.getMessage(), e);
        }
    }


    /**
     * 生成预付跟踪数据
     *
     * @param codes
     */
    public void generateRecords(Set<String> codes) {
        List<ActivityPrepayDetail> details = activityPrepayDetailRepository.findByCodes(new ArrayList<>(codes));
        Validate.notEmpty(details, "未找到对应的预付明细数据");
        List<ActivityPrepayPayee> payeeList = activityPrepayPayeeRepository.findByCodes(new ArrayList<>(codes));
        Validate.notEmpty(payeeList, "未找到对应的收款明细数据");
        Map<String, ActivityPrepayPayee> payeeMap = payeeList.stream().collect(Collectors.toMap(e -> e.getPrepayCode(), Function.identity(), (a, b) -> a));
        Map<String, ActivityPrepayDetail> map = details.stream().collect(Collectors.toMap(ActivityPrepayDetail::getPrepayDetailCode, Function.identity(), (a, b) -> a));
        Collection<ActivityPrepayDetailRecordDto> records = nebulaToolkitService.copyCollectionByWhiteList(details, ActivityPrepayDetail.class, ActivityPrepayDetailRecordDto.class, LinkedHashSet.class, ArrayList.class, "id");
        records.forEach(e -> {
            e.setPrepayType(TpmPrepayTypeEnum.activity.getCode());
            e.setPrepayAmount(map.get(e.getPrepayDetailCode()).getThisPrepayAmount());
            e.setLineCode(payeeMap.get(e.getPrepayCode()).getLineCode());
            e.setPayStatus(HecPayStatusTypeEnum.NOT_PAY.getCode());
            e.setPrepayApplyAmount(map.get(e.getPrepayDetailCode()).getApplyAmount());
        });
        activityPrepayDetailRecordService.create(new ArrayList<>(records));
    }
}
