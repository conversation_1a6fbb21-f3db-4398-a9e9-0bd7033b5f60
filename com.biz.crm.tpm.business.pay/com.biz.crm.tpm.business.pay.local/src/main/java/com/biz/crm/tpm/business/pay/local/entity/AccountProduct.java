package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.business.common.local.entity.UuidOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月16日 16:45:00
 */
@ApiModel(value = "Account", description = "费用上账商品表")
@TableName("tpm_account_product")
@Getter
@Setter
@Entity(name = "tpm_account_product")
@org.hibernate.annotations.Table(appliesTo = "tpm_account_product", comment = "费用上账商品表")
@Table(name = "tpm_account_product")
public class AccountProduct extends TenantFlagOpEntity {


  private static final long serialVersionUID = -305888574474936152L;
  /**
   * 费用上账编码
   */
  @ApiModelProperty(name = "费用上账编码", notes = "费用上账编码")
  @Column(name = "account_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '费用上账编码 '")
  private String accountCode;

  /**
   * 商品编码
   */
  @ApiModelProperty(name = "商品编码", notes = "商品编码")
  @Column(name = "product_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '商品编码 '")
  private String productCode;

  /**
   * 商品名称
   */
  @ApiModelProperty(name = "商品名称", notes = "商品名称")
  @Column(name = "product_name",  length = 128, columnDefinition = "VARCHAR(128) COMMENT '商品名称 '")
  private String productName;
}
