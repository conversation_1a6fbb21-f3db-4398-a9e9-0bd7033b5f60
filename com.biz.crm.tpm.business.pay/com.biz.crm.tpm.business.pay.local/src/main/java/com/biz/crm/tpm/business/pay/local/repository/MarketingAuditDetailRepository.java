package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAuditDetail;
import com.biz.crm.tpm.business.pay.local.mapper.MarketingAuditDetailMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 方案结案明细(MarketingAuditDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 16:58:19
 */
@Component
public class MarketingAuditDetailRepository extends ServiceImpl<MarketingAuditDetailMapper, MarketingAuditDetail> {

    @Autowired
    private MarketingAuditDetailMapper marketingAuditDetailMapper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    public List<MarketingAuditDetailVo> findByCode(String code) {
        List<MarketingAuditDetail> list = this.lambdaQuery().eq(MarketingAuditDetail::getAuditCode, code)
                .eq(MarketingAuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, MarketingAuditDetail.class, MarketingAuditDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 按编码查询
     *
     * @param codes
     * @return
     */
    public List<MarketingAuditDetailVo> findByCodes(List<String> codes) {
        List<MarketingAuditDetail> list = this.lambdaQuery().in(MarketingAuditDetail::getAuditCode, codes)
                .eq(MarketingAuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, MarketingAuditDetail.class, MarketingAuditDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }


    public List<MarketingAuditDetailVo> findDetailListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        List<MarketingAuditDetail> list = this.lambdaQuery()
                .in(MarketingAuditDetail::getSchemeDetailCode, schemeDetailCodes)
                .eq(MarketingAuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, MarketingAuditDetail.class, MarketingAuditDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 按明细编码查询
     *
     * @param codes
     * @return
     */
    public List<MarketingAuditDetail> findByAuditDetailCodes(List<String> codes) {
        List<MarketingAuditDetail> list = Lists.newArrayList();
        List<List<String>> partitionList = Lists.partition(codes, 800);
        for (List<String> stringList : partitionList) {
            List<MarketingAuditDetail> data = this.lambdaQuery().in(MarketingAuditDetail::getAuditDetailCode, stringList)
                    .eq(MarketingAuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
            if (CollectionUtils.isNotEmpty(data)) {
                list.addAll(data);
            }
        }
        return list;
    }

    /**
     * 按编码删除
     *
     * @param codes
     */
    public void deleteByCodes(List<String> codes) {
        this.remove(Wrappers.lambdaQuery(MarketingAuditDetail.class)
                .in(MarketingAuditDetail::getAuditCode, codes));
    }

    /**
     * 按编码更新上账状态
     *
     * @param codes
     */
    public void updateAccountStatus(List<String> codes, String accountStatus) {
        this.lambdaUpdate().in(MarketingAuditDetail::getAuditCode, codes)
                .eq(MarketingAuditDetail::getCashType, CashMethodEnum.REPLENISHMENT.getDictCode())
                .set(MarketingAuditDetail::getAccountStatus, accountStatus).update();
    }

    /**
     * 按编码更新结案完结日期
     *
     * @param codes
     */
    public void updateAuditDate(List<String> codes) {
        this.lambdaUpdate().in(MarketingAuditDetail::getAuditCode, codes)
                .set(MarketingAuditDetail::getAuditDate, DateUtil.format(new Date(), "yyyy-MM-dd")).update();
    }


    public void updateAuditDateBySchemeDetailCodes(List<String> schemeDetailCodes) {
        this.lambdaUpdate().in(MarketingAuditDetail::getSchemeDetailCode, schemeDetailCodes)
                .set(MarketingAuditDetail::getAuditDate, DateUtil.format(new Date(), "yyyy-MM-dd")).update();
    }

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    public Page<MarketingAuditDetailVo> findByConditions(Pageable pageable, MarketingAuditDetailDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<MarketingAuditDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return marketingAuditDetailMapper.findByConditions(page, dto);
    }

    /**
     * 批量查询
     *
     * @param dtoList
     * @return
     */
    public List<MarketingAuditDetailVo> findByDtoList(List<MarketingAuditDetailDto> dtoList) {
        return marketingAuditDetailMapper.findByDtoList(dtoList);
    }

    /**
     * 查询兑付方式=货补，且“是否完全兑付”≠“是”的审批通过的结案明细
     *
     * @param
     * @return
     */
    public List<MarketingAuditDetail> findForReplenishmentPool() {
        return marketingAuditDetailMapper.findForReplenishmentPool();
    }

    /**
     * 按活动明细编码查询
     *
     * @param codes
     * @return
     */
    public List<MarketingAuditDetail> findBySchemeDetailCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        List<MarketingAuditDetail> list = this.lambdaQuery().in(MarketingAuditDetail::getSchemeDetailCode, codes)
                .eq(MarketingAuditDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
        return list;
    }

    /**
     * 按活动明细编码查询是否有审批中
     *
     * @param codes
     * @return
     */
    public List<MarketingAuditDetail> findBySchemeDetailCodesBeCommit(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }

        return marketingAuditDetailMapper.findBySchemeDetailCodesBeCommit(codes);
    }

    public List<MarketingAuditDetailVo> findBySchemeDetailCodesAll(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }

        return marketingAuditDetailMapper.findBySchemeDetailCodesAll(codes);
    }


    public List<MarketingAuditDetailVo> findAuditAmountBySchemeDetailCodes(List<String> schemeDetailCodes) {
        List<List<String>> partitionList = Lists.partition(schemeDetailCodes, 800);
        List<MarketingAuditDetailVo> dataList = Lists.newArrayList();
        for (List<String> list : partitionList) {
            List<MarketingAuditDetailVo> data = marketingAuditDetailMapper.findAuditAmountBySchemeDetailCodes(list);
            if (CollectionUtils.isNotEmpty(data)) {
                dataList.addAll(data);
            }
        }
        return dataList;
    }


    public List<MarketingAuditDetailVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        List<List<String>> partitionList = Lists.partition(schemeDetailCodes, 800);
        List<MarketingAuditDetailVo> dataList = Lists.newArrayList();
        for (List<String> list : partitionList) {
            List<MarketingAuditDetailVo> data = marketingAuditDetailMapper.findListBySchemeDetailCodes(list);
            if (CollectionUtils.isNotEmpty(data)) {
                dataList.addAll(data);
            }
        }
        return dataList;
    }


    public List<MarketingAuditDetailVo> findListBySchemeDetailCodesApproved(List<String> schemeDetailCodes) {
        List<List<String>> partitionList = Lists.partition(schemeDetailCodes, 800);
        List<MarketingAuditDetailVo> dataList = Lists.newArrayList();
        for (List<String> list : partitionList) {
            List<MarketingAuditDetailVo> data = marketingAuditDetailMapper.findListBySchemeDetailCodesApproved(list);
            if (CollectionUtils.isNotEmpty(data)) {
                dataList.addAll(data);
            }
        }
        return dataList;
    }

    public void updateCashAmount(List<MarketingAuditDetailDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        marketingAuditDetailMapper.updateCashAmount(list);
    }

    /**
     * 修改是否预付
     *
     * @param codes
     */
    public void updateBePrepay(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        lambdaUpdate().in(MarketingAuditDetail::getAuditDetailCode, codes)
                .set(MarketingAuditDetail::getBePrepay, BooleanEnum.TRUE.getCapital()).update();
    }

    public List<MarketingAuditDetailVo> scheduleNotClosureDetailMsgPush() {
        return marketingAuditDetailMapper.scheduleNotClosureDetailMsgPush();
    }

    public List<MarketingAuditDetailVo> scheduleNotReplenishmentMsgPush() {
        return marketingAuditDetailMapper.scheduleNotReplenishmentMsgPush();
    }

    public List<MarketingAuditDetailVo> findCommitAndPassBySchemeDetailCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }

        return marketingAuditDetailMapper.findCommitAndPassBySchemeDetailCodes(codes);
    }
}

