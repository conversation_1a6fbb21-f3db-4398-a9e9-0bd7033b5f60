package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.tpm.business.pay.sdk.enums.TpmPrepayTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：活动预付明细跟踪;
 *
 * <AUTHOR> yaoyongming
 * @date : 2024-6-5
 */
@ApiModel(value = "ActivityPrepayDetailRecord", description = "活动预付明细跟踪")
@TableName("tpm_activity_prepay_detail_record")
@Getter
@Setter
@Entity(name = "tpm_activity_prepay_detail_record")
@org.hibernate.annotations.Table(appliesTo = "tpm_activity_prepay_detail_record", comment = "活动预付明细跟踪")
@Table(name = "tpm_activity_prepay_detail_record", indexes = {
        @Index(name = "activity_prepay_detail_record_idx1", columnList = "prepay_code"),
        @Index(name = "activity_prepay_detail_record_idx2", columnList = "prepay_detail_code"),
        @Index(name = "activity_prepay_detail_record_idx3", columnList = "scheme_code"),
        @Index(name = "activity_prepay_detail_record_idx4", columnList = "scheme_detail_code"),
        @Index(name = "activity_prepay_detail_record_idx5", columnList = "years"),
        @Index(name = "activity_prepay_detail_record_idx6", columnList = "audit_detail_code"),
})
public class ActivityPrepayDetailRecord extends TenantFlagOpEntity {

  @ApiModelProperty("预付编号")
  @Column(name = "prepay_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付编号 '")
  private String prepayCode;

  @ApiModelProperty("预付名称")
  @Column(name = "prepay_name", columnDefinition = "varchar(128) comment '预付名称'")
  private String prepayName;

  @ApiModelProperty("预付明细编号")
  @Column(name = "prepay_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付明细编号 '")
  private String prepayDetailCode;

  @ApiModelProperty("核销申请编号")
  @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
  private String auditCode;

  @ApiModelProperty("核销明细编号")
  @Column(name = "audit_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销明细编号 '")
  private String auditDetailCode;

  @ApiModelProperty("方案编码")
  @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
  private String schemeCode;

  @ApiModelProperty("方案名称")
  @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称'")
  private String schemeName;

  @ApiModelProperty("方案明细编码")
  @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
  private String schemeDetailCode;

  @ApiModelProperty("活动大类编码")
  @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
  private String categoryCode;

  @ApiModelProperty("活动大类名称")
  @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
  private String categoryName;

  @ApiModelProperty("活动细类编码")
  @Column(name = "detail_code", columnDefinition = "varchar(32) comment '活动细类编码'")
  private String detailCode;

  @ApiModelProperty("活动细类名称")
  @Column(name = "detail_name", columnDefinition = "varchar(64) comment '活动细类名称'")
  private String detailName;

  @ApiModelProperty("年月")
  @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
  private String years;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
  private String companyCode;

  @ApiModelProperty("供应商编码")
  @Column(name = "payee_code", columnDefinition = "VARCHAR(64) COMMENT '供应商编码 '")
  private String payeeCode;

  @ApiModelProperty("供应商名称")
  @Column(name = "payee_name", columnDefinition = "VARCHAR(255) COMMENT '供应商名称 '")
  private String payeeName;

  @ApiModelProperty("申请金额")
  @Column(name = "apply_amount", columnDefinition = "decimal(18,4) comment '申请金额'")
  private BigDecimal applyAmount;

  @ApiModelProperty("实际支付金额")
  @Column(name = "prepay_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '实际支付金额 '")
  private BigDecimal prepayAmount;

  @ApiModelProperty("申请预付金额")
  @Column(name = "prepay_apply_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '申请预付金额 '")
  private BigDecimal prepayApplyAmount;

  @ApiModelProperty("待结转金额")
  @Column(name = "prepare_carry_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '待结转金额 '")
  private BigDecimal prepareCarryAmount;

  @ApiModelProperty("关联结转金额")
  @Column(name = "relate_carry_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '关联结转金额 '")
  private BigDecimal relateCarryAmount;

  @ApiModelProperty("已结转金额")
  @Column(name = "carried_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '已结转金额 '")
  private BigDecimal carriedAmount;

  @ApiModelProperty("预付可冲销金额")
  @Column(name = "available_reversed_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '预付可冲销金额 '")
  private BigDecimal availableReversedAmount;

  @ApiModelProperty("已冲销金额")
  @Column(name = "reversed_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '已冲销金额 '")
  private BigDecimal reversedAmount;

  @ApiModelProperty("预付描述")
  @Column(name = "description", columnDefinition = "VARCHAR(255) COMMENT '预付描述 '")
  private String description;

  @ApiModelProperty("收款行编号")
  @Column(name = "line_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '收款行编号 '")
  private String lineCode;

  @ApiModelProperty("付款状态")
  @Column(name = "pay_status", length = 64, columnDefinition = "VARCHAR(64) COMMENT '付款状态 '")
  private String payStatus;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "varchar(64) comment '成本中心名称'")
  private String costCenterName;

  @ApiModelProperty("付款成功时间")
  @Column(name = "pay_sucess_date", columnDefinition = "datetime COMMENT '付款成功时间'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date paySucessDate;

  /**
   * @see TpmPrepayTypeEnum
   */
  @ApiModelProperty("预付类型")
  @Column(name = "prepay_type", length = 32, columnDefinition = "VARCHAR(32) COMMENT '预付类型 '")
  private String prepayType;
}