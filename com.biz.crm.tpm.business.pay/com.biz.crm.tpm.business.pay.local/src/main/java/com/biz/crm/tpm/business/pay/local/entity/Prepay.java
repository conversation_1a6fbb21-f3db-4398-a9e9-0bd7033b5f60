package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：活动预付;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-22
 */
@ApiModel(value = "Prepay", description = "活动预付")
@TableName("tpm_prepay")
@Getter
@Setter
@Entity(name = "tpm_prepay")
@org.hibernate.annotations.Table(appliesTo = "tpm_prepay", comment = "活动预付")
@Table(name = "tpm_prepay", indexes = {@Index(name = "tpm_prepay_index1", columnList = "tenant_code, prepay_code")})
public class Prepay extends TenantFlagOpEntity {

  private static final long serialVersionUID = -3566713877183270842L;
  /**
   * 预付编号
   */
  @ApiModelProperty(name = "预付编号", notes = "预付编号")
  @Column(name = "prepay_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付编号 '")
  private String prepayCode;

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  @Column(name = "activities_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  @Column(name = "activities_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 预付金额合计
   */
  @ApiModelProperty(name = "预付金额合计", notes = "预付金额")
  @Column(name = "total_prepay_amount", nullable = false, length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '预付金额合计 '")
  private BigDecimal totalPrepayAmount;

  /**
   * 开始时间
   */
  @ApiModelProperty(name = "开始时间", notes = "开始时间")
  @Column(name = "begin_time", nullable = false, columnDefinition = "DATETIME COMMENT '开始时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @ApiModelProperty(name = "结束时间", notes = "结束时间")
  @Column(name = "end_time", nullable = false, columnDefinition = "DATETIME COMMENT '结束时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

}