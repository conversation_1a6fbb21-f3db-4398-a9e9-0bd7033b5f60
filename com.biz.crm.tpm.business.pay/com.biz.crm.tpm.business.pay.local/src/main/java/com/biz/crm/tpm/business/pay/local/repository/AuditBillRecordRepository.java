package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.mapper.AuditBillRecordMapper;
import com.biz.crm.tpm.business.pay.local.entity.AuditBillRecord;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 核销账单明细记录;(tpm_audit_bill_record)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Component
public class AuditBillRecordRepository extends ServiceImpl<AuditBillRecordMapper,AuditBillRecord>{
  @Autowired
  private AuditBillRecordMapper auditBillRecordMapper;

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<AuditBillRecord>
   */
  public List<AuditBillRecord> findByIds(Collection<String> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(AuditBillRecord::getId, ids)
            .eq(AuditBillRecord::getTenantCode, tenantCode)
            .list();
  }


}
