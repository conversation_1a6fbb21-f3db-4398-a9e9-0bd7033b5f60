package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.AuditBill;
import com.biz.crm.tpm.business.pay.local.mapper.AuditBillMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 核销账单;(tpm_audit_bill)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-17
 */
@Component
public class AuditBillRepository extends ServiceImpl<AuditBillMapper, AuditBill> {
  @Autowired
  private AuditBillMapper auditBillMapper;

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<AuditBill>
   */
  public List<AuditBill> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(AuditBill::getId, ids)
            .eq(AuditBill::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据活动明细编号与租户编号获取对象
   *
   * @param activitiesDetailCode
   * @return
   */
  public AuditBill findByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(AuditBill::getActivitiesDetailCode, activitiesDetailCode)
            .eq(AuditBill::getTenantCode, tenantCode).one();
  }

  /**
   * 增加已核销金额
   *
   * @param activitiesDetailCode
   * @param amount
   */
  public void addAuditedAmount(String activitiesDetailCode, BigDecimal amount) {
    String tenantCode = TenantUtils.getTenantCode();
    this.auditBillMapper.addAuditAmount(activitiesDetailCode, amount, tenantCode);
  }

  /**
   * 减少已核销金额
   *
   * @param activitiesDetailCode
   * @param amount
   */
  public void reduceAuditedAmount(String activitiesDetailCode, BigDecimal amount) {
    String tenantCode = TenantUtils.getTenantCode();
    this.auditBillMapper.reduceAuditAmount(activitiesDetailCode, amount, tenantCode);
  }

  public AuditBill findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(AuditBill::getTenantCode,tenantCode)
        .in(AuditBill::getId,id)
        .one();
  }
}