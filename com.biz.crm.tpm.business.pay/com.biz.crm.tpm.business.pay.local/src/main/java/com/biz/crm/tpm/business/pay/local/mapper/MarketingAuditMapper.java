package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAudit;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 方案结案(MarketingAudit)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 16:58:19
 */
public interface MarketingAuditMapper extends BaseMapper<MarketingAudit> {

    List<MarketingAuditVo> findCommit(@Param("yearMonthLy") String yearMonthLy);

    List<MarketingAuditDetailVo> findListByApprovedAndFullAudit(@Param("code") String code);
}

