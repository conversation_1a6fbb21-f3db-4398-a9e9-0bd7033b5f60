<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.InvoiceDetailMapper">

    <select id="findInvoiceDetailList" resultType="com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailPageVo">
        SELECT
        a.type,
        a.billing_date,
        a.check_code,
        a.CODE,
        b.name,
        a.invoice_no,
        b.amount_without_tax,
        b.price_and_tax,
        b.tax_amount,
        b.invoice_detail_no,
        a.purchaser,
        a.p_no,
        a.p_bank_and_account,
        a.p_address_and_phone,
        a.seller,
        a.s_no,
        a.s_bank_and_account,
        a.s_address_and_phone,
        b.tax_rate
        FROM
        tpm_invoice a
        LEFT JOIN tpm_invoice_details b ON a.invoice_no = b.invoice_no
        <where>
            a.tenant_code = #{tenantCode}
            and a.del_flag = '${@<EMAIL>()}'
            and b.del_flag = '${@<EMAIL>()}'
            and a.checked = '${@<EMAIL>()}'
            <if test="vo.type != null and vo.type != ''">
                and a.type = #{vo.type}
            </if>
            <if test="vo.code != null and vo.code != ''">
                <bind name="code" value="'%' + vo.code + '%'"/>
                and a.code like #{code}
            </if>
            <if test="vo.invoiceNo != null and vo.invoiceNo != ''">
                <bind name="invoiceNo" value="'%' + vo.invoiceNo + '%'"/>
                and a.invoice_no like #{invoiceNo}
            </if>
            <if test="vo.purchaser != null and vo.purchaser != ''">
                <bind name="purchaser" value="'%' + vo.purchaser + '%'"/>
                and a.purchaser like #{purchaser}
            </if>
            <if test="vo.seller != null and vo.seller != ''">
                <bind name="seller" value="'%' + vo.seller + '%'"/>
                and a.seller like #{seller}
            </if>
            <if test="vo.pNo != null and vo.pNo != ''">
                and a.p_no = #{vo.pNo}
            </if>
            and (a.cash_code is null
            <if test="vo.cashCode != null and vo.cashCode != ''">
                or a.cash_code = #{vo.cashCode}
            </if>
            )
            order by a.create_time desc,a.invoice_no desc
        </where>
    </select>
    <select id="findByUniqueKeyList" resultType="com.biz.crm.tpm.business.pay.local.entity.InvoiceDetail">
        select * from tpm_invoice_details where concat(invoice_no, invoice_detail_no) in
        <foreach item="item" collection="uniqueKeyList" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="updateUsedByUniqueKeyList">
        update tpm_invoice_details set used = '${@<EMAIL>()}'
       where concat(invoice_no, invoice_detail_no) in
        <foreach item="item" collection="uniqueKeyList" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findByInvoiceNoAndDetailNo"
            resultType="com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailPageVo">
        SELECT
        a.type,
        a.billing_date,
        a.check_code,
        a.CODE,
        b.name,
        a.invoice_no,
        b.amount_without_tax,
        b.price_and_tax,
        b.tax_amount,
        b.invoice_detail_no,
        a.purchaser,
        a.p_no,
        a.p_bank_and_account,
        a.p_address_and_phone,
        a.seller,
        a.s_no,
        a.s_bank_and_account,
        a.s_address_and_phone,
        b.tax_rate
        FROM
        tpm_invoice a
        LEFT JOIN tpm_invoice_details b ON a.invoice_no = b.invoice_no
        <where>
            a.del_flag = '${@<EMAIL>()}'
        and b.del_flag = '${@<EMAIL>()}'
        and a.checked = '${@<EMAIL>()}'
        and a.invoice_no = #{invoiceNo}
        and b.invoice_detail_no = #{detailNo}
        </where>
    </select>
</mapper>

