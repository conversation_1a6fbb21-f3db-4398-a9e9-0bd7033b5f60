package com.biz.crm.tpm.business.pay.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditInvoiceVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 费用核销发票;(tpm_audit_invoice)表服务接口
 * <AUTHOR> Keller
 * @date : 2022-7-6
 */
public interface AuditInvoiceService{
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<AuditInvoiceVo> findByConditions(Pageable pageable, AuditInvoiceDto dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AuditInvoiceVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<AuditInvoiceVo> findByIds(Collection<String> ids);
  /**
   * 根绝业务编号auditCode获取业务数据
   *
   * @param auditCode
   * @return 单条数据
   */
  List<AuditInvoiceVo> findByAuditCode(String auditCode);
  /**
   * 新增数据
   *
   * @param auditInvoiceDto 实体对象
   * @return 新增结果
   */
  AuditInvoiceVo create(AuditInvoiceDto auditInvoiceDto);
  /**
   * 批量新增
   * @param auditInvoiceDtos
   * @return
   */
  List<AuditInvoiceVo> createBatch(Collection<AuditInvoiceDto> auditInvoiceDtos);
  /**
   * 修改数据
   *
   * @param auditInvoiceDto 实体对象
   * @return 修改结果
   */
  AuditInvoiceVo update(AuditInvoiceDto auditInvoiceDto);
  /**
   * 批量修改据
   *
   * @param auditInvoiceDtos 实体对象
   * @return 修改结果
   */
  List<AuditInvoiceVo> updateBatch(Collection<AuditInvoiceDto> auditInvoiceDtos);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
  /**
   * 根绝业务编号auditCode删除数据
   *
   * @param auditCode
   * @return 单条数据
   */
  void deleteByAuditCode(String auditCode);
}
