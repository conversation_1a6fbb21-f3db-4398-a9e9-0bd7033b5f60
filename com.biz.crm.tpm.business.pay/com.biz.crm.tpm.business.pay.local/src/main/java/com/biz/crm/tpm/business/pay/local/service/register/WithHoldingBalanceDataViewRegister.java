package com.biz.crm.tpm.business.pay.local.service.register;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WithHoldingBalanceDataViewRegister implements DataviewRegister {

    /**
     * 全系统唯一的数据视图业务编号（例如orderList）</br>
     * 如果不唯一，系统将会报错
     */
    @Override
    public String code() {
        return "tpm_with_holding_balance_data_view";
    }

    /**
     * 注册的数据视图描述信息（例如：订单业务主列表视图）
     */
    @Override
    public String desc() {
        return "计提余额数据视图";
    }

    /**
     * 这个数据视图所使用的SQL语句，注意这个SQL语句不需要包括任何分页查询的关键字、不需要包括和数据权限有关的任何查询条件，
     * 也不要包括和任何特定数据库有关的关键字。</p>
     * 但是可以加入和必传参数有关的参数绑定位置，例如：</p>
     * <code>
     * select * from user where user.name = :name
     * </code>
     * <p>
     * 这样的话，数据视图的正式执行就必须传入参数名为name的参数信息</p>
     */
    @Override
    public String buildSql() {
        return "SELECT " +
                " wh.with_holding_code, " +
                " wh.year_month_ly, " +
                " wh.years, " +
                " wh.activities_detail_code, " +
                " wh.activities_code, " +
                " wh.activities_name, " +
                " wh.order_code, " +
                " wh.order_name, " +
                " wh.cost_type_category_code, " +
                " wh.cost_type_category_name, " +
                " wh.cost_type_detail_code, " +
                " wh.cost_type_detail_name, " +
                " wh.budget_subjects_code, " +
                " wh.budget_subjects_name, " +
                " wh.belong_department_code, " +
                " wh.belong_department_name, " +
                " wh.bear_department_code, " +
                " wh.bear_department_name, " +
                " wh.cost_center_code, " +
                " wh.cost_center_name, " +
                " wh.customer_code, " +
                " wh.customer_name, " +
                " wh.item_name, " +
                " wh.with_holding_type, " +
                " wh.pay_by, " +
                " MAX(IFNULL(wh.apply_amount, 0)) apply_amount_total, " +
                " MAX(IFNULL(wh.actual_amount, 0)) with_holding_amount_total, " +
                " SUM(IFNULL(wo.write_off_amount, 0)) write_off_amount_total, " +
                " MAX(IFNULL(wh.actual_amount, 0)) - SUM(IFNULL(wo.write_off_amount, 0)) balance " +
                "FROM " +
                " tpm_with_holding wh " +
                " LEFT JOIN tpm_with_holding_write_off wo ON wh.with_holding_code = wo.with_holding_code and wo.del_flag ='009' " +
                " where wh.tenant_code = :tenantCode " +
                " and wh.del_flag = '009' " +
                " and wh.status = '3'"+
                "GROUP BY " +
                " wh.with_holding_code, " +
                " wh.year_month_ly, " +
                " wh.years, " +
                " wh.activities_detail_code, " +
                " wh.activities_code, " +
                " wh.activities_name, " +
                " wh.order_code, " +
                " wh.order_name, " +
                " wh.cost_type_category_code, " +
                " wh.cost_type_category_name, " +
                " wh.cost_type_detail_code, " +
                " wh.cost_type_detail_name, " +
                " wh.budget_subjects_code, " +
                " wh.budget_subjects_name, " +
                " wh.belong_department_code, " +
                " wh.belong_department_name, " +
                " wh.bear_department_code, " +
                " wh.bear_department_name, " +
                " wh.cost_center_code, " +
                " wh.cost_center_name, " +
                " wh.customer_code, " +
                " wh.customer_name, " +
                " wh.item_name, " +
                " wh.with_holding_type, " +
                " wh.pay_by ";
    }
}
