<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AuditBillMapper">
  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.AuditBill" id="AuditBillMap">
  </resultMap>

  <update id="addAuditAmount">
    update tpm_audit_bill t
    set t.audited_amount = t.audited_amount + #{amount}
    where t.activities_detail_code = #{activitiesDetailCode}
      and t.tenant_code = #{tenantCode}
  </update>

  <update id="reduceAuditAmount">
    update tpm_audit_bill t
    set t.audited_amount = t.audited_amount - #{amount}
    where t.activities_detail_code = #{activitiesDetailCode}
      and t.audited_amount >= #{amount}
      and t.tenant_code = #{tenantCode}
  </update>
</mapper>