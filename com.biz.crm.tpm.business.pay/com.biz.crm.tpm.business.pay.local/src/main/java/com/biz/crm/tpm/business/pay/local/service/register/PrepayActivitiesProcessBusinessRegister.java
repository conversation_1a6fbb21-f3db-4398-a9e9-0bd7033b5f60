package com.biz.crm.tpm.business.pay.local.service.register;

import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.workflow.sdk.register.ProcessBusinessRegister;
import org.springframework.stereotype.Component;

/**
 * 活动预付工作流注册业务编码
 *
 * <AUTHOR>
 * @date 2022/9/5
 */
@Component
public class PrepayActivitiesProcessBusinessRegister implements ProcessBusinessRegister {
  @Override
  public String getBusinessCode() {
    return PayConstant.PROCESS_PREPAY_ACTIVITIES;
  }

  @Override
  public String getBusinessName() {
    return "活动预付";
  }
}
