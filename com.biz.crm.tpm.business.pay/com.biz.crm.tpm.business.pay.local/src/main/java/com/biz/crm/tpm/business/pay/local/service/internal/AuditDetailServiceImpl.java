package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailCollectService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.PayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.BusinessStrategySettingExecutor;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.local.entity.AuditDetail;
import com.biz.crm.tpm.business.pay.local.repository.AuditDetailRepository;
import com.biz.crm.tpm.business.pay.sdk.service.AuditBillService;
import com.biz.crm.tpm.business.pay.local.service.AuditCustomerService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailService;
import com.biz.crm.tpm.business.pay.local.service.AuditProductService;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditBillDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditCustomerDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.event.AuditDetailEventListener;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditBillVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditCustomerVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditProductVo;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessMappingDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.ProcessBusinessMappingService;
import com.biz.crm.workflow.sdk.vo.ProcessBusinessMappingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType.AUTO_AUDIT;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType.EXCEEDING_AUDIT_RATE;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType.IS_AUDIT;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType.MULTIPLE_AUDIT;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType.VALIDITY_TIME_AUDIT;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType.AUDIT;

/**
 * 费用核销明细;(tpm_audit_detail)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Service("auditDetailService")
@Slf4j
public class AuditDetailServiceImpl implements AuditDetailService {
  @Autowired
  private AuditDetailRepository auditDetailRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private AuditProductService auditProductService;
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private AuditBillService auditBillService;
  @Autowired
  private ActivitiesService activitiesService;
  @Autowired
  private AuditCustomerService auditCustomerService;
  @Autowired
  private CostTypeCategoryVoService costTypeCategoryVoService;
  @Autowired(required = false)
  private List<PayByStrategy> payByStrategies;
  @Autowired(required = false)
  private List<AuditDetailEventListener> auditDetailEventListeners;
  @Autowired
  private ActivitiesDetailService activitiesDetailService;
  @Autowired
  private ActivitiesDetailCollectService activitiesDetailCollectService;
  @Autowired(required = false)
  private List<BusinessStrategySettingExecutor> businessStrategySettingExecutors;
  @Autowired(required = false)
  private ProcessBusinessMappingService processBusinessMappingService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<AuditDetailVo> findByConditions(Pageable pageable, AuditDetailDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new AuditDetailDto();
    }
    Page<AuditDetailVo> page = this.auditDetailRepository.findByConditions(pageable, dto);
    if (!CollectionUtils.isEmpty(page.getRecords())) {
      page.getRecords().forEach(item -> {
        this.fillDetail(item);
      });
    }
    return page;
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public AuditDetailVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    AuditDetail auditDetail = this.auditDetailRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (auditDetail == null) {
      return null;
    }
    AuditDetailVo auditDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(auditDetail, AuditDetailVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetail(auditDetailVo);
    return auditDetailVo;
  }

  @Override
  public AuditDetailVo findByAuditDetailCode(String auditDetailCode) {
    if (StringUtils.isBlank(auditDetailCode)) {
      return null;
    }
    AuditDetail auditDetail = this.auditDetailRepository.findByCode(auditDetailCode);
    if (auditDetail == null) {
      return null;
    }
    AuditDetailVo auditDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(auditDetail, AuditDetailVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetail(auditDetailVo);
    return auditDetailVo;
  }

  /**
   * 通过编号查询单条数据
   *
   * @param auditCode 主键
   * @return 多条数据
   */
  @Override
  public List<AuditDetailVo> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return Collections.emptyList();
    }
    List<AuditDetail> auditDetails = this.auditDetailRepository.findByAuditCode(auditCode);
    if (CollectionUtils.isEmpty(auditDetails)) {
      return Collections.emptyList();
    }
    Collection<AuditDetailVo> auditDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditDetails, AuditDetail.class, AuditDetailVo.class, LinkedHashSet.class, ArrayList.class);
    auditDetailVos.forEach(item -> {
      this.fillDetail(item);
    });
    return Lists.newArrayList(auditDetailVos);
  }

  private AuditDetailVo findByCode(String auditDetailCode) {
    if (StringUtils.isBlank(auditDetailCode)) {
      return null;
    }
    AuditDetail auditDetail = this.auditDetailRepository.findByCode(auditDetailCode);
    AuditDetailVo auditDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(auditDetail, AuditDetailVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetail(auditDetailVo);
    return auditDetailVo;
  }

  /**
   * 填充明细信息
   *
   * @param auditDetailVo
   */
  private void fillDetail(AuditDetailVo auditDetailVo) {
    if (auditDetailVo == null) {
      return;
    }
    AuditBillVo auditBillVo = this.auditBillService.findByActivitiesDetailCode(auditDetailVo.getActivitiesDetailCode());
    auditDetailVo.setAuditedAmount(auditBillVo.getAuditedAmount());
    auditDetailVo.setIsFullAudit(auditBillVo.getIsFullAudit());

    List<AuditProductVo> auditProductVos = this.auditProductService.findByAuditDetailCode(auditDetailVo.getAuditDetailCode());
    auditDetailVo.setAuditProducts(auditProductVos);

    CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(auditDetailVo.getCostTypeDetailCode());
    auditDetailVo.setCostTypeDetailVo(costTypeDetailVo);

    List<AuditCustomerVo> auditCustomerVos = this.auditCustomerService.findByAuditDetailCode(auditDetailVo.getAuditDetailCode());
    auditDetailVo.setAuditCustomers(auditCustomerVos);

    ActivitiesDetailVo activitiesDetailVo = this.activitiesDetailService.findByActivitiesDetailCode(auditDetailVo.getActivitiesDetailCode());
    auditDetailVo.setActivitiesDetailVo(activitiesDetailVo);
  }

  /**
   * 新增数据
   *
   * @param auditDetailDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AuditDetailVo create(AuditDetailDto auditDetailDto) {
    String code = this.generateCodeService.generateCode(PayConstant.AUDIT_DETAIL_LADDER_CODE, 1).get(0);
    auditDetailDto.setAuditDetailCode(code);
    this.createValidate(auditDetailDto);
    AuditDetail auditDetail = this.nebulaToolkitService.copyObjectByWhiteList(auditDetailDto, AuditDetail.class, LinkedHashSet.class, ArrayList.class);
    auditDetail.setTenantCode(TenantUtils.getTenantCode());
    auditDetail.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    this.auditDetailRepository.saveOrUpdate(auditDetail);
    AuditDetailVo auditDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(auditDetail, AuditDetailVo.class, LinkedHashSet.class, ArrayList.class);
    auditDetailVo.setId(auditDetail.getId());

    if (!CollectionUtils.isEmpty(auditDetailEventListeners)) {
      for (AuditDetailEventListener auditDetailEventListener : auditDetailEventListeners) {
        auditDetailEventListener.onCreated(auditDetailVo);
      }
    }

    // 处理子项的保存
    // 1、商品信息
    if (!CollectionUtils.isEmpty(auditDetailDto.getAuditProducts())) {
      auditDetailDto.getAuditProducts().forEach(item -> {
        item.setAuditCode(auditDetailVo.getAuditCode());
        item.setAuditDetailCode(auditDetailVo.getAuditDetailCode());
      });
      List<AuditProductVo> auditProductVos = this.auditProductService.createBatch(auditDetailDto.getAuditProducts());
      auditDetailVo.setAuditProducts(auditProductVos);
    }
    // 2、客户信息
    if (!CollectionUtils.isEmpty(auditDetailDto.getAuditCustomers())) {
      auditDetailDto.getAuditCustomers().forEach(item -> {
        item.setAuditCode(auditDetailVo.getAuditCode());
        item.setAuditDetailCode(auditDetailVo.getAuditDetailCode());
      });
      List<AuditCustomerVo> auditProductVos = this.auditCustomerService.createBatch(auditDetailDto.getAuditCustomers());
      auditDetailVo.setAuditCustomers(auditProductVos);
    }

    AuditBillVo auditBillVo = this.auditBillService.findByActivitiesDetailCode(auditDetailDto.getActivitiesDetailCode());
    if (auditBillVo == null) {
      // 3、新增活动明细账单信息
      AuditBillDto auditBillDto = new AuditBillDto();
      auditBillDto.setAuditedAmount(BigDecimal.ZERO);
      auditBillDto.setApplyAmount(auditDetailDto.getApplyAmount());
      auditBillDto.setTenantCode(TenantUtils.getTenantCode());
      auditBillDto.setActivitiesDetailCode(auditDetailDto.getActivitiesDetailCode());
      auditBillDto.setIsFullAudit(auditDetailDto.getIsFullAudit());
      this.auditBillService.create(auditBillDto);
      this.activitiesDetailService.updateFullAuditByActivitiesDetailCode(auditDetailDto.getActivitiesDetailCode(), auditDetailDto.getIsFullAudit());
    } else {
      // 如果完全核销更新完全核销信息
      this.auditBillService.updateIsFullAudit(auditBillVo.getActivitiesDetailCode(), auditDetailDto.getIsFullAudit());
      this.activitiesDetailService.updateFullAuditByActivitiesDetailCode(auditDetailDto.getActivitiesDetailCode(), auditDetailDto.getIsFullAudit());
    }
    auditDetailVo.setIsFullAudit(auditDetailDto.getIsFullAudit());
    return auditDetailVo;
  }

  @Transactional
  @Override
  public List<AuditDetailVo> createBatch(List<AuditDetailDto> auditDetailDtos) {
    if (CollectionUtils.isEmpty(auditDetailDtos)) {
      return Lists.newArrayList();
    }
    List<AuditDetailVo> auditDetailVos = Lists.newArrayList();
    for (AuditDetailDto auditDetailDto : auditDetailDtos) {
      AuditDetailVo auditDetailVo = this.create(auditDetailDto);
      auditDetailVos.add(auditDetailVo);
    }
    return auditDetailVos;
  }

  /**
   * 修改新据
   *
   * @param auditDetailDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public AuditDetailVo update(AuditDetailDto auditDetailDto) {
    this.updateValidate(auditDetailDto);
    AuditDetail auditDetail = this.auditDetailRepository.findByIdAndTenantCode(auditDetailDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(auditDetail, "修改数据不存在，请检查！");
    AuditDetailVo oldAuditDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(auditDetail, AuditDetailVo.class, LinkedHashSet.class, ArrayList.class);

    auditDetail.setPayBy(auditDetailDto.getPayBy());
    auditDetail.setPayByName(auditDetailDto.getPayByName());
    auditDetail.setAuditAmount(auditDetailDto.getAuditAmount());
    auditDetail.setProductLevelCode(auditDetailDto.getProductLevelCode());
    auditDetail.setProductLevelName(auditDetailDto.getProductLevelName());
    auditDetail.setActivitiesDetailCode(auditDetailDto.getActivitiesDetailCode());
    auditDetail.setAccountingSubjectsCode(auditDetailDto.getAccountingSubjectsCode());
    auditDetail.setAccountingSubjectsName(auditDetailDto.getAccountingSubjectsName());
    auditDetail.setApplyAmount(auditDetailDto.getApplyAmount());
    auditDetail.setRemark(auditDetailDto.getRemark());
    auditDetail.setExcessAmount(auditDetailDto.getExcessAmount());
    auditDetail.setTenantCode(TenantUtils.getTenantCode());
    this.auditDetailRepository.saveOrUpdate(auditDetail);
    AuditDetailVo auditDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(auditDetail, AuditDetailVo.class, LinkedHashSet.class, ArrayList.class);

    // 处理子项的保存
    // 1、商品信息
    if (!CollectionUtils.isEmpty(auditDetailDto.getAuditProducts())) {
      auditDetailDto.getAuditProducts().forEach(item -> {
        item.setAuditCode(auditDetailVo.getAuditCode());
        item.setAuditDetailCode(auditDetailVo.getAuditDetailCode());
      });
      List<AuditProductVo> auditProductVos = this.auditProductService.updateBatch(auditDetailDto.getAuditProducts());
      auditDetailVo.setAuditProducts(auditProductVos);
    }

    // 2、商品信息
    if (!CollectionUtils.isEmpty(auditDetailDto.getAuditCustomers())) {
      auditDetailDto.getAuditCustomers().forEach(item -> {
        item.setAuditCode(auditDetailVo.getAuditCode());
        item.setAuditDetailCode(auditDetailVo.getAuditDetailCode());
      });
      List<AuditCustomerVo> auditProductVos = this.auditCustomerService.updateBatch(auditDetailDto.getAuditCustomers());
      auditDetailVo.setAuditCustomers(auditProductVos);
    }

    //更新完全核销逻辑
    this.auditBillService.updateIsFullAudit(auditDetailVo.getActivitiesDetailCode(), auditDetailDto.getIsFullAudit());
    this.activitiesDetailService.updateFullAuditByActivitiesDetailCode(auditDetailDto.getActivitiesDetailCode(), auditDetailDto.getIsFullAudit());
    auditDetailVo.setIsFullAudit(auditDetailDto.getIsFullAudit());
    if (!CollectionUtils.isEmpty(auditDetailEventListeners)) {
      for (AuditDetailEventListener auditDetailEventListener : auditDetailEventListeners) {
        auditDetailEventListener.onUpdate(oldAuditDetailVo, auditDetailVo);
      }
    }
    return auditDetailVo;
  }

  @Override
  @Transactional
  public List<AuditDetailVo> updateBatch(List<AuditDetailDto> auditDetailDtos) {
    if (CollectionUtils.isEmpty(auditDetailDtos)) {
      return Collections.emptyList();
    }
    String auditCode = auditDetailDtos.stream().findFirst().get().getAuditCode();
    List<AuditDetailVo> dbAuditDetailVos = this.findByAuditCode(auditCode);
    Set<String> dbIds = dbAuditDetailVos.stream().map(AuditDetailVo::getId).collect(Collectors.toSet());
    Set<String> currentIds = auditDetailDtos.stream().map(AuditDetailDto::getId).collect(Collectors.toSet());

    Set<String> deleteIds = Sets.difference(dbIds, currentIds);
    if (!CollectionUtils.isEmpty(deleteIds)) {
      this.delete(Lists.newArrayList(deleteIds));
    }
    List<AuditDetailDto> addDtos = auditDetailDtos.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
    List<AuditDetailDto> updateDtos = auditDetailDtos.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
    List<AuditDetailVo> auditDetailVos = this.createBatch(addDtos);
    if (!CollectionUtils.isEmpty(updateDtos)) {
      updateDtos.forEach(item -> {
        auditDetailVos.add(this.update(item));
      });
    }
    return auditDetailVos;
  }

  @Override
  @Transactional
  public void updateCostTypeDetailName(String costTypeDetailCode, String costTypeDetailName) {
    this.auditDetailRepository.updateCostTypeDetailName(costTypeDetailCode, costTypeDetailName);
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<AuditDetail> auditDetails = this.auditDetailRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(auditDetails)) {
      return;
    }

    Collection<AuditDetailVo> auditDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditDetails, AuditDetail.class, AuditDetailVo.class, LinkedHashSet.class, ArrayList.class);
    this.auditDetailRepository.removeByIds(ids);
    if (!CollectionUtils.isEmpty(auditDetailEventListeners)) {
      for (AuditDetailEventListener auditDetailEventListener : auditDetailEventListeners) {
        for (AuditDetailVo auditDetailVo : auditDetailVos) {
          auditDetailEventListener.onDeleted(auditDetailVo);
        }
      }
    }
  }

  @Override
  public void deleteByAuditCode(String auditCode) {
    this.auditCustomerService.deleteByAuditCode(auditCode);
    this.auditDetailRepository.removeByAuditCode(auditCode);
  }

  @Override
  public void deleteByAuditCodes(Collection<String> auditCodes) {
    this.auditDetailRepository.removeByAuditCodes(auditCodes);
  }

  @Override
  @Transactional
  public void doRefund(String auditDetailCode) {
    /*
     * 进行退款前提条件检查
     * 1、是否完全核销
     * 2、待退款的金额
     */
    if (StringUtils.isBlank(auditDetailCode)) {
      return;
    }
    AuditDetailVo auditDetailVo = this.findByCode(auditDetailCode);
    AuditBillVo auditBillVo = this.auditBillService.findByActivitiesDetailCode(auditDetailVo.getActivitiesDetailCode());
    if (BooleanEnum.TRUE.getCapital().equals(auditBillVo.getIsFullAudit())) {
      return;
    }
    //待退款的金额
    BigDecimal auditedAmount = auditBillVo.getAuditedAmount();
    BigDecimal applyAmount = auditBillVo.getApplyAmount();
    BigDecimal refundAmount = applyAmount.subtract(auditedAmount);
    if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
      this.auditDetailRepository.updateRefundAmount(auditDetailVo.getAuditDetailCode(), refundAmount);
//      ActivitiesVo activitiesVo = this.activitiesService.findByCode(auditDetailVo.getActivitiesCode());
      ActivitiesDetailVo basicActivityItemVo = this.activitiesDetailService.findByActivitiesDetailCode(auditDetailVo.getActivitiesDetailCode());
      Validate.notNull(basicActivityItemVo, "活动明细【%s】错误，请检查！", auditDetailVo.getActivitiesDetailCode());
      ActivitiesVo activitiesVo = activitiesService.findByActivitiesCode(auditDetailVo.getActivitiesCode());
      Validate.notNull(activitiesVo, "该活动明细【%s】不含有主活动信息，请检查", auditDetailVo.getActivitiesDetailCode());
      this.costBudgetVoService.back(auditDetailVo.getActivitiesCode(), basicActivityItemVo.getActivitiesDetailCode(), basicActivityItemVo.getCostBudgetCode(), refundAmount, null, this.analysisSource(activitiesVo.getActivityMark()));
    } else {
      log.warn("核销明细编号【{}】数据在进行退费时候，退费金额为负数，请核对检查是否超额核销！", auditDetailVo.getAuditDetailCode());
    }

  }

  @Override
  public Set<String> findActivitiesDetailByAuditing(Set<String> costTypeDetailCodes) {
    return this.auditDetailRepository.findActivitiesDetailByAuditing(costTypeDetailCodes);
  }

  @Override
  public Set<String> findActivitiesDetailByAudited() {
    return this.auditDetailRepository.findActivitiesDetailByAudited();
  }

  @Override
  public Set<String> findActivitiesDetailByFullAudit() {
    return this.auditDetailRepository.findActivitiesDetailByFullAudit();
  }

  @Override
  public List<ActivitiesDetailCollectVo> findDetailCollectByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return Collections.emptyList();
    }
    List<ActivitiesDetailCollectVo> activitiesDetailCollectVos = this.activitiesDetailCollectService.findByActivitiesDetailCode(activitiesDetailCode);
    return activitiesDetailCollectVos;
  }

  @Override
  public Integer countByCostTypeAndActivitiesDetail(String costTypeDetailCode, String activitiesDetailCode) {
    return this.auditDetailRepository.countByCostTypeAndActivitiesDetail(costTypeDetailCode, activitiesDetailCode);
  }

  /**
   * 创建验证
   *
   * @param auditDetailDto
   */
  private void createValidate(AuditDetailDto auditDetailDto) {
    Validate.notNull(auditDetailDto, "新增时，对象信息不能为空！");
    auditDetailDto.setId(null);
    Validate.notBlank(auditDetailDto.getAuditCode(), "新增数据时，核销申请编码不能为空！");
    Validate.notBlank(auditDetailDto.getAuditDetailCode(), "新增数据时，核销申请明细编号不能为空！");
    Validate.notBlank(auditDetailDto.getActivitiesCode(), "新增数据时，活动编码不能为空！");
    Validate.notBlank(auditDetailDto.getActivitiesDetailCode(), "新增数据时，活动明细编码不能为空！");
    Validate.notBlank(auditDetailDto.getCostTypeDetailCode(), "新增数据时，活动细类编码不能为空！");
    Validate.notBlank(auditDetailDto.getCostTypeCategoryCode(), "新增数据时，活动大类编码不能为空！");
    Validate.notNull(auditDetailDto.getApplyAmount(), "新增数据时，申请金额不能为空！");
    Validate.notNull(auditDetailDto.getAuditAmount(), "新增数据时，核销金额不能为空！");
    Validate.notBlank(auditDetailDto.getPayBy(), "新增数据时，支付方式不能为空！");
    Validate.notBlank(auditDetailDto.getCostBudgetCode(), "新增数据时，费用预算编号不能为空！");
    // 申请金额是否有效
    this.validate(auditDetailDto, true);
    // 不允许同一条活动明细，在审核中
    this.validateExist(auditDetailDto, true);
    // 支付方式验证
    this.validatePayBy(auditDetailDto);
  }

  /**
   * 修改验证
   *
   * @param auditDetailDto
   */
  private void updateValidate(AuditDetailDto auditDetailDto) {
    Validate.notNull(auditDetailDto, "修改时，对象信息不能为空！");
    Validate.notNull(auditDetailDto.getId(), "修改时，数据主键不能为空！");
    Validate.notBlank(auditDetailDto.getAuditCode(), "修改时，核销申请编码不能为空！");
    Validate.notBlank(auditDetailDto.getAuditDetailCode(), "修改时，核销申请明细编号不能为空！");
    Validate.notBlank(auditDetailDto.getActivitiesCode(), "修改时，活动编码不能为空！");
    Validate.notBlank(auditDetailDto.getActivitiesName(), "修改时，活动名称不能为空！");
    Validate.notBlank(auditDetailDto.getActivitiesDetailCode(), "修改时，活动明细编码不能为空！");
    Validate.notBlank(auditDetailDto.getCostTypeDetailCode(), "修改时，活动明细编码不能为空！");
    Validate.notBlank(auditDetailDto.getCostTypeDetailName(), "修改时，活动明细名称不能为空！");
    Validate.notBlank(auditDetailDto.getCostTypeCategoryCode(), "修改时，活动大类编码不能为空！");
    Validate.notBlank(auditDetailDto.getCostTypeCategoryName(), "修改时，活动大类名称不能为空！");
    Validate.notNull(auditDetailDto.getApplyAmount(), "修改时，申请金额不能为空！");
    Validate.notNull(auditDetailDto.getAuditAmount(), "修改时，核销金额不能为空！");
    Validate.notBlank(auditDetailDto.getAuditDetailCode(), "修改时，活动细类编码不能为空！");
    Validate.notBlank(auditDetailDto.getPayBy(), "修改时，支付方式不能为空！");
    Validate.notBlank(auditDetailDto.getCostBudgetCode(), "修改时，费用预算编号不能为空！");
    //申请金额是否有效
    this.validate(auditDetailDto, false);
    // 不允许同一条活动明细，在审核中
    this.validateExist(auditDetailDto, false);
    // 支付方式验证
    this.validatePayBy(auditDetailDto);

  }

  /**
   * 通用业务验证
   *
   * @param auditDetailDto
   */
  private void validate(AuditDetailDto auditDetailDto, boolean addFlag) {
    /*
     * 核销验证
     * 1、是否开启核销
     * 2、是否开启自动核销
     * 3、核销有效期检查
     * 4、是否多次核销检查、并修正完全核销
     * 5、是否已经完全核销检查
     * 6、核销金额检查
     */
    if (!CollectionUtils.isEmpty(auditDetailDto.getAuditCustomers())) {
      BigDecimal customerAuditAmount = auditDetailDto.getAuditCustomers().stream().map(AuditCustomerDto::getAuditAmount)
              .filter(Objects::nonNull)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      Validate.isTrue(customerAuditAmount.compareTo(auditDetailDto.getAuditAmount()) == 0, "申请核销金额与客户申请核销金额不匹配，请检查");
    }
    CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(auditDetailDto.getCostTypeDetailCode());
    Validate.notNull(costTypeDetailVo, "活动细类数据错误，请检查！");
    //todo 费用核销
//    Validate.notEmpty(costTypeDetailVo.getSettingStrategies(), "活动细类编号【%s】没有策略配置信息，请检查", costTypeDetailVo.getDetailCode());
    BusinessStrategySettingExecutor executor = BusinessStrategySettingExecutor.getExecutor(businessStrategySettingExecutors, AUDIT.name());
    Validate.notNull(executor, "活动细类编号【%s】没有匹配到相应的策略执行器，请检查！", costTypeDetailVo.getDetailCode());
    CostTypeCategoryVo costTypeCategoryVo = this.costTypeCategoryVoService.findByCode(auditDetailDto.getCostTypeCategoryCode());
    Validate.notNull(costTypeCategoryVo, "活动大类数据错误，请检查！");
    auditDetailDto.setCostTypeCategoryName(costTypeCategoryVo.getCategoryName());
    auditDetailDto.setCostTypeDetailName(costTypeDetailVo.getDetailName());
    auditDetailDto.setBudgetSubjectsCode(costTypeCategoryVo.getBudgetSubjectsCode());
    auditDetailDto.setBudgetSubjectsName(costTypeCategoryVo.getBudgetSubjectsName());
    Object isMultipleAudit = executor.getValueByOprtType(costTypeDetailVo.getSettingStrategies(), MULTIPLE_AUDIT.name());
    auditDetailDto.setIsMultipleAudit(isMultipleAudit == null ? BooleanEnum.FALSE.getCapital() : isMultipleAudit.toString());
    ActivitiesVo activitiesVo = this.activitiesService.findByActivitiesCode(auditDetailDto.getActivitiesCode());
    Validate.notNull(activitiesVo, "活动编号错误，请检查！");
    auditDetailDto.setActivitiesName(activitiesVo.getActivitiesName());
    auditDetailDto.setBeginTime(activitiesVo.getBeginTime());
    auditDetailDto.setEndTime(activitiesVo.getEndTime());
    //todo 活动明细是否开启核销验证
//    boolean isAudit = executor.matchedOprtType(costTypeDetailVo.getSettingStrategies(), IS_AUDIT.name(), true);
//    Validate.isTrue(isAudit, "活动明细编号【%s】关联活动未开启核销，请检查！", auditDetailDto.getActivitiesDetailCode());
    // 核销有效期验证检查
    Object auditValidityMonth = executor.getValueByOprtType(costTypeDetailVo.getSettingStrategies(), VALIDITY_TIME_AUDIT.name());
    // TODO 整理逻辑通过表单执行器进行验证 核销有效期
    if (auditValidityMonth != null && Integer.parseInt(auditValidityMonth.toString()) > 0) {
      LocalDateTime activitiesEndTime = LocalDateTime.ofInstant(activitiesVo.getEndTime().toInstant(), ZoneId.systemDefault());
      LocalDateTime validityTime = activitiesEndTime.plusMonths(Integer.parseInt(auditValidityMonth.toString()));
      LocalDateTime now = LocalDateTime.now();
      Validate.isTrue(now.isBefore(validityTime), "活动明细编号【%s】,核销时间已经超过核销有效期", auditDetailDto.getAuditDetailCode());
    }
    // 不允许多次核销的检查
//    boolean notMultipleAudit = executor.matchedOprtType(costTypeDetailVo.getSettingStrategies(), MULTIPLE_AUDIT.name(), false);
//    if (notMultipleAudit) {
//      int detailCount = this.auditDetailRepository.countByCostTypeAndActivitiesDetail(costTypeDetailVo.getDetailCode(), auditDetailDto.getActivitiesDetailCode());
//      if (addFlag) {
//        Validate.isTrue(detailCount == 0, "新增时 活动【%s】-【%s】不允许多次核销，请检查！", costTypeDetailVo.getDetailCode(), costTypeDetailVo.getDetailName());
//      } else {
//        Validate.isTrue(detailCount == 1, "修改时 该活动【%s】-【%s】不允许多次核销，请检查！", costTypeDetailVo.getDetailCode(), costTypeDetailVo.getDetailName());
//      }
//      // 不允许多次核销，完全核销为true
//      auditDetailDto.setIsFullAudit(BooleanEnum.TRUE.getCapital());
//    }
    // 该项明细总共申请的金额
    BigDecimal applyAmount = auditDetailDto.getApplyAmount();
    // 已经核销的金额
    AuditBillVo auditBillVo = this.auditBillService.findByActivitiesDetailCode(auditDetailDto.getActivitiesDetailCode());
    BigDecimal auditedAmount = BigDecimal.ZERO;
    if (auditBillVo != null) {
      auditedAmount = auditBillVo.getAuditedAmount();
    }
    // 申请核销的金额
    BigDecimal auditAmount = auditDetailDto.getAuditAmount();
    // 手动核销检查
    boolean autoAudit = executor.matchedOprtType(costTypeDetailVo.getSettingStrategies(), AUTO_AUDIT.name(), false);
//    if (!autoAudit) {
//      // 检查超额核销数据
//      Object exceedingAuditRate = executor.getValueByOprtType(costTypeDetailVo.getSettingStrategies(), EXCEEDING_AUDIT_RATE.name());
//      if (exceedingAuditRate != null) {
//        // 允许最大超额金额
//        BigDecimal excessAmount = applyAmount.multiply(new BigDecimal(exceedingAuditRate.toString())).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
//        Integer compare = (applyAmount.add(excessAmount)).compareTo(auditedAmount.add(auditAmount));
//        Validate.isTrue(compare >= 0, "核销金额超出申请金额，申请金额【%s】, 允许超额金额【%s】,已经核销金额【%s】申请核销金额【%s】", applyAmount.toString(), excessAmount.toString(), auditedAmount.toString(), auditAmount.toString());
//        if (compare == 0) {
//          //核销金额已经满足申请金额+超额金额 则修正完成核销数据
//          auditDetailDto.setIsFullAudit(BooleanEnum.TRUE.getCapital());
//        } else if (compare < 0) {
//          Validate.isTrue(applyAmount.compareTo(auditedAmount.add(auditAmount)) >= 0, "核销金额超出申请金额，申请金额【%s】,已经核销金额【%s】申请核销金额【%s】", applyAmount.toString(), auditedAmount.toString(), auditAmount.toString());
//        } else {
//          if (auditedAmount.compareTo(applyAmount) > 0) {
//            excessAmount = auditAmount;
//          } else {
//            BigDecimal currentAmount = applyAmount.subtract(auditedAmount).subtract(auditAmount);
//            if (currentAmount.compareTo(BigDecimal.ZERO) < 0) {
//              excessAmount = currentAmount.abs();
//            } else {
//              excessAmount = BigDecimal.ZERO;
//            }
//          }
//        }
//        // 填充超额金额
//        auditDetailDto.setExcessAmount(excessAmount);
//      } else {
//        BigDecimal currentAmount = applyAmount.subtract(auditedAmount).subtract(auditAmount);
//        Validate.isTrue(currentAmount.compareTo(BigDecimal.ZERO) >= 0, "申请金额【%s】,已经核销金额【%s】,已经超出申请金额【%s】，请检查", auditAmount.toString(), auditedAmount.toString(), applyAmount.toString());
//        if (currentAmount.compareTo(BigDecimal.ZERO) == 0) {
//          auditDetailDto.setIsFullAudit(BooleanEnum.TRUE.getCapital());
//        }
//      }
//    }
  }

  /**
   * 验证一条活动明细不能在多个未完成的费用核销中
   *
   * @param auditDetailDto
   */
  private void validateExist(AuditDetailDto auditDetailDto, boolean isAdd) {
    int detailCount = 0;
    if (isAdd) {
      List<AuditDetail> list = this.auditDetailRepository.findByActivitiesDetailCodes(auditDetailDto.getActivitiesDetailCode());
      if (!CollectionUtils.isEmpty(list)) {
        List<String> auditCodes = list.stream().map(AuditDetail::getAuditCode).collect(Collectors.toList());
        List<ProcessBusinessMappingVo> processBusinessMappingVos = this.findProcessBusinessMappingVo(auditCodes);
        if (!CollectionUtils.isEmpty(processBusinessMappingVos)) {
          List<ProcessBusinessMappingVo> processVos = processBusinessMappingVos.stream().filter(item -> !ProcessStatusEnum.PASS.getDictCode().equals(item.getProcessStatus())).collect(Collectors.toList());
          detailCount = processVos.size();
        }
      }
    } else {
      List<AuditDetail> list = this.auditDetailRepository.findByExcludeActivitiesCodeAndActivitiesDetailCodes(auditDetailDto.getAuditCode(), auditDetailDto.getActivitiesDetailCode());
      if (!CollectionUtils.isEmpty(list)) {
        List<String> auditCodes = list.stream().map(AuditDetail::getAuditCode).collect(Collectors.toList());
        List<ProcessBusinessMappingVo> processBusinessMappingVos = this.findProcessBusinessMappingVo(auditCodes);
        if (!CollectionUtils.isEmpty(processBusinessMappingVos)) {
          List<ProcessBusinessMappingVo> processVos = processBusinessMappingVos.stream().filter(item -> !ProcessStatusEnum.PASS.getDictCode().equals(item.getProcessStatus())).collect(Collectors.toList());
          detailCount = processVos.size();
        }
      }
    }
    Validate.isTrue(detailCount == 0, "新增核销明细时，活动明细编号【%s】存在待审核费用核销信息，不能再次添加核销，请检查！", auditDetailDto.getActivitiesDetailCode());
  }

  /**
   * 根据支付方式检查数据
   *
   * @param auditDetailDto
   */
  private void validatePayBy(AuditDetailDto auditDetailDto) {
    Validate.notNull(auditDetailDto.getAuditDto(), "核销信息错误，请检查！");
    if (BooleanEnum.TRUE.getCapital().equals(auditDetailDto.getAuditDto().getIsAutoAudit())) {
      // 自动核销情况不进行该项检查
      return;
    }
    if (CollectionUtils.isEmpty(payByStrategies)) {
      return;
    }
    String payBy = auditDetailDto.getPayBy();
    List<PayByStrategy> currentValidators = payByStrategies.stream().filter(item -> item.getCode().equals(payBy)).collect(Collectors.toList());
    if (CollectionUtils.isEmpty(currentValidators)) {
      return;
    }
    for (PayByStrategy payByStrategy : currentValidators) {
      if (!CollectionUtils.isEmpty(payByStrategy.getValidators())) {
        payByStrategy.getValidators().forEach(item -> {
          item.handler(auditDetailDto);
        });
      }
    }
  }

  private String analysisSource(String value) {
    switch (value) {
      case "OrdinaryActivity":
        return CostBudgetItemSourceType.ORDINARY_ACTIVITY.getDescr();
      case "ProjectActivity":
        return CostBudgetItemSourceType.PROJECT_ACTIVITY.getDescr();
      case "SchemeActivity":
        return CostBudgetItemSourceType.SCHEMA_ACTIVITY.getDescr();
      default:
        throw new IllegalArgumentException("活动关闭时，未知的费用预算来源，请检查");
    }
  }

  /**
   * 根据业务编码查询工作流业务流程关联信息
   *
   * @param businessNos
   */
  private List<ProcessBusinessMappingVo> findProcessBusinessMappingVo(List<String> businessNos) {
    ProcessBusinessMappingDto processBusinessMappingDto = new ProcessBusinessMappingDto();
    processBusinessMappingDto.setBusinessNos(businessNos);
    processBusinessMappingDto.setBusinessCode(PayConstant.PROCESS_AUDIT_ACTIVITIES);
    return processBusinessMappingService.findMultiByByConditions(processBusinessMappingDto);
  }
}