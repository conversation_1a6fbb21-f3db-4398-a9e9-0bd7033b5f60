package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolOperationType;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectSchemeVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.WithHolding;
import com.biz.crm.tpm.business.pay.local.mapper.WithHoldingMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingBalanceDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingDto;
import com.biz.crm.tpm.business.pay.sdk.enums.HecSendStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WithHoldingTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingBalanceVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 费用预提(WithHolding)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-25 14:20:20
 */
@Component
public class WithHoldingRepository extends ServiceImpl<WithHoldingMapper, WithHolding> {

    @Autowired
    private WithHoldingMapper withHoldingMapper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     * @return
     */
    public Page<WithHoldingVo> findByConditions(Pageable pageable, WithHoldingDto dto) {
        Page<WithHoldingVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<WithHoldingVo> pageList = withHoldingMapper.findByConditions(page, dto);
        return pageList;
    }

    public void delete(List<String> idList) {
        this.lambdaUpdate()
                .in(WithHolding::getId, idList)
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .set(WithHolding::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
                .update();
    }

    public void remove(List<String> idList) {
        this.lambdaUpdate()
                .in(WithHolding::getId, idList)
                .remove();
    }

    public List<WithHolding> findByActivitiesDetailCodes(List<String> codes) {
        return this.lambdaQuery()
                .in(WithHolding::getActivitiesDetailCode, codes)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    public WithHolding findByActivitiesDetailCode(String activityDetailCode) {
        return this.lambdaQuery()
                .eq(WithHolding::getActivitiesDetailCode, activityDetailCode)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }

    public List<WithHolding> findByBusinessCodes(List<String> codes) {
        List<WithHolding> dataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(codes)) {
            return dataList;
        }
        List<List<String>> partitionList = Lists.partition(codes, 800);
        for (List<String> list : partitionList) {
            List<WithHolding> data = this.lambdaQuery()
                    .in(WithHolding::getBusinessCode, list)
                    .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                    .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                    .eq(WithHolding::getConfirmStatus, ConfirmStatusEnum.CONFIRMED.getCode())
                    .list();
            if (CollectionUtils.isNotEmpty(data)) {
                dataList.addAll(data);
            }
        }
        return dataList;
    }


    public List<WithHolding> findListByBusinessCodes(List<String> codes) {
        List<WithHolding> dataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(codes)) {
            return dataList;
        }
        List<List<String>> partitionList = Lists.partition(codes, 800);
        for (List<String> list : partitionList) {
            List<WithHolding> data = this.lambdaQuery()
                    .in(WithHolding::getBusinessCode, list)
                    .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                    .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                    .eq(WithHolding::getStatus, ProcessStatusEnum.PASS.getDictCode())
                    .isNotNull(WithHolding::getVoucherCode)
                    .list();
            if (CollectionUtils.isNotEmpty(data)) {
                dataList.addAll(data);
            }
        }
        return dataList;
    }


    public List<WithHoldingVo> findByBusinessCodesVo(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }
        List<WithHolding> list = this.lambdaQuery()
                .in(WithHolding::getBusinessCode, codes)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, WithHolding.class, WithHoldingVo.class, LinkedHashSet.class, ArrayList.class));
    }

    public List<WithHoldingVo> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }
        List<WithHolding> list = this.lambdaQuery()
                .in(WithHolding::getWithHoldingCode, codes)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, WithHolding.class, WithHoldingVo.class, LinkedHashSet.class, ArrayList.class));
    }

    public List<WithHoldingVo> findBySchemeDetailCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }
        List<WithHolding> list = this.lambdaQuery()
                .in(WithHolding::getActivitiesDetailCode, codes)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, WithHolding.class, WithHoldingVo.class, LinkedHashSet.class, ArrayList.class));
    }

    public List<WithHoldingVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return new ArrayList<>();
        }
        List<WithHolding> list = this.lambdaQuery()
                .in(WithHolding::getActivitiesDetailCode, schemeDetailCodes)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, WithHolding.class, WithHoldingVo.class, LinkedHashSet.class, ArrayList.class));
    }


    public List<WithHoldingVo> findProcessPassBySchemeDetailCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }
        List<WithHolding> list = this.lambdaQuery()
                .in(WithHolding::getActivitiesDetailCode, codes)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .eq(WithHolding::getStatus, ProcessStatusEnum.PASS.getDictCode())
                .list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, WithHolding.class, WithHoldingVo.class, LinkedHashSet.class, ArrayList.class));
    }


    /**
     * 查询审批通过的计提金额
     *
     * @param codes
     * @return
     */
    public List<WithHoldingVo> findBySchemeDetailCodesPass(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }
        return withHoldingMapper.findBySchemeDetailCodesPass(codes);
    }

    public WithHolding findByCode(String code) {
        return this.lambdaQuery()
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .in(WithHolding::getWithHoldingCode, code)
                .one();
    }

    public WithHolding findByIdAndTenantCode(String id, String tenantCode) {
        return this.lambdaQuery()
                .eq(WithHolding::getTenantCode, tenantCode)
                .in(WithHolding::getId, id)
                .one();
    }

    public List<WithHolding> findByIds(List<String> ids) {
        return this.lambdaQuery()
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .in(WithHolding::getId, ids)
                .list();
    }

    public void updateBatchByIdAndTenantCode(List<WithHolding> updateList, String tenantCode) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.forEach(w -> {
                LambdaUpdateWrapper<WithHolding> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
                lambdaUpdateWrapper.eq(WithHolding::getTenantCode, tenantCode);
                lambdaUpdateWrapper.eq(WithHolding::getId, w.getId());
                this.baseMapper.update(w, lambdaUpdateWrapper);
            });
        }
/*    LambdaUpdateWrapper<WithHolding>lambdaUpdateWrapper= Wrappers.lambdaUpdate();
    updateList.forEach(w->{
      lambdaUpdateWrapper.eq(WithHolding::getTenantCode,tenantCode);
      lambdaUpdateWrapper.in(WithHolding::getId);
      this.baseMapper.update(w,lambdaUpdateWrapper);
    });*/
    }

    /**
     * 按计提编码修改汇总编码
     *
     * @param codes
     * @param collectCode
     */
    public void updateCollectCodeByCodes(List<String> codes, String collectCode) {
        this.lambdaUpdate().in(WithHolding::getWithHoldingCode, codes)
                .set(WithHolding::getCollectCode, collectCode).update();
    }

    /**
     * 清空汇总编码
     *
     * @param codes
     */
    public void clearCollectCode(List<String> codes) {
        this.lambdaUpdate().in(WithHolding::getCollectCode, codes)
                .set(WithHolding::getCollectCode, null).update();
    }

    /**
     * 按汇总编码查询
     *
     * @param code
     */
    public List<WithHoldingVo> findByCollectCode(String code) {
        List<WithHolding> list = this.lambdaQuery()
                .eq(WithHolding::getCollectCode, code)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, WithHolding.class, WithHoldingVo.class, LinkedHashSet.class, ArrayList.class));
    }

    /**
     * 按汇总编码查询
     *
     * @param code
     */
    public List<WithHoldingVo> findByCollectCodeAndWithHoldingType(String code,String type) {
        List<WithHolding> list = this.lambdaQuery()
                .eq(WithHolding::getCollectCode, code)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getWithHoldingType,type)
                .list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, WithHolding.class, WithHoldingVo.class, LinkedHashSet.class, ArrayList.class));
    }

    /**
     * 按汇总编码查询
     *
     * @param codes
     */
    public List<WithHoldingVo> findByCollectCodes(List<String> codes) {
        List<WithHolding> list = this.lambdaQuery().in(WithHolding::getCollectCode, codes).list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, WithHolding.class, WithHoldingVo.class, LinkedHashSet.class, ArrayList.class));
    }

    /**
     * 按年月删除
     *
     * @param yearMonthLy
     */
    public List<String> deleteByYearMonthLy(String yearMonthLy) {
        List<String> schemeDetailCodes = new ArrayList<>();
        List<WithHolding> list = lambdaQuery().eq(WithHolding::getYearMonthLy, yearMonthLy)
                .ne(WithHolding::getWithHoldingType, WithHoldingTypeEnum.HANDLE.getDictCode())
                .in(WithHolding::getStatus, Arrays.asList(ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(), ProcessStatusEnum.RECOVER.getDictCode()))
                .notIn(WithHolding::getWithHoldingType, Lists.newArrayList(ReplenishmentPoolOperationType.POD_DIFF_ADD.getCode(),
                        ReplenishmentPoolOperationType.POD_DIFF_REDUCE.getCode(),
                        ReplenishmentPoolOperationType.ORDER_CLOSE_DETAIL.getCode()))
                .eq(WithHolding::getBeAdjust, "N")
                .eq(WithHolding::getConfirmStatus, ConfirmStatusEnum.UNCONFIRMED.getCode()).list();
        if (CollectionUtils.isNotEmpty(list)) {
            schemeDetailCodes = list.stream().filter(e -> StringUtils.isNotBlank(e.getActivitiesDetailCode()))
                    .filter(Objects::nonNull).map(e -> e.getActivitiesDetailCode()).collect(Collectors.toList());
        }
        lambdaUpdate().eq(WithHolding::getYearMonthLy, yearMonthLy)
                .ne(WithHolding::getWithHoldingType, WithHoldingTypeEnum.HANDLE.getDictCode())
                .in(WithHolding::getStatus, Arrays.asList(ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(), ProcessStatusEnum.RECOVER.getDictCode()))
                .notIn(WithHolding::getWithHoldingType, Lists.newArrayList(ReplenishmentPoolOperationType.POD_DIFF_ADD.getCode(),
                        ReplenishmentPoolOperationType.POD_DIFF_REDUCE.getCode(),
                        ReplenishmentPoolOperationType.ORDER_CLOSE_DETAIL.getCode()))
                .eq(WithHolding::getBeAdjust, "N")
                .eq(WithHolding::getConfirmStatus, ConfirmStatusEnum.UNCONFIRMED.getCode())
                .remove();

        return schemeDetailCodes;
    }


    public void deleteByYearMonthlyAndBusinessCodes(String yearMonthly, List<String> businessCodeList) {
        List<List<String>> partitionList = Lists.partition(businessCodeList, 800);
        for (List<String> list : partitionList) {
            this.lambdaUpdate().in(WithHolding::getBusinessCode, list)
                    .eq(WithHolding::getYearMonthLy, yearMonthly)
                    .remove();
        }
    }

    public List<WithHolding> findByYearMonthBelongDepartmentCode(String yearMonthLy, List<String> orgCodes) {
        return this.baseMapper.findByYearMonthBelongDepartmentCode(yearMonthLy, orgCodes, TenantUtils.getTenantCode());
    }

    public void hecVoucherCallback(List<HecCallbackDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        this.baseMapper.hecVoucherCallback(dtoList);
    }

    public List<WithHoldingVo> findSendDataByCollectIds(List<String> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findSendDataByCollectIds(idList);
    }

    public List<WithHoldingVo> findSendDataByIds(List<String> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findSendDataByIds(idList);
    }

    public void updateHecInfo(List<String> withHoldingCodeList, String hecReceiptNumber) {
        if (CollectionUtil.isEmpty(withHoldingCodeList)) {
            return;
        }
        this.lambdaUpdate()
                .in(WithHolding::getWithHoldingCode, withHoldingCodeList)
                .set(WithHolding::getPushStatus, BooleanEnum.TRUE.getCapital())
                .set(WithHolding::getExternalCode, hecReceiptNumber)
                .set(WithHolding::getFailMsg, "")
                .update();
    }

    public void updateErrorMsg(List<String> withHoldingCodeList, String failMsg) {
        if (CollectionUtil.isEmpty(withHoldingCodeList)) {
            return;
        }
        if (StringUtil.isNotEmpty(failMsg)
                && failMsg.length() > 500) {
            failMsg = failMsg.substring(0, 499);
        }
        this.lambdaUpdate()
                .in(WithHolding::getWithHoldingCode, withHoldingCodeList)
                .set(WithHolding::getFailMsg, failMsg)
                .update();
    }

    /**
     * 根据方案编码查找方案汇总创建人信息
     *
     * @param schemeCodes
     * @return
     */
    public List<RegionCollectSchemeVo> findSchemeCreateInfoList(List<String> schemeCodes) {
        if (CollectionUtil.isEmpty(schemeCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findSchemeCreateInfoList(schemeCodes);
    }

    @Transactional
    public void updateCreateInfo(List<WithHolding> entities) {
        if (CollectionUtil.isEmpty(entities)) {
            return;
        }
        this.baseMapper.updateCreateInfo(entities);
    }

    /**
     * 修改计提审批状态
     *
     * @param codes
     * @param status
     */
    public void updateStatus(List<String> codes, String collectCode, String status) {
        if (CollectionUtil.isEmpty(codes) && StringUtils.isBlank(collectCode)) {
            return;
        }
        lambdaUpdate().in(CollectionUtil.isNotEmpty(codes), WithHolding::getWithHoldingCode, codes)
                .eq(StringUtils.isNotBlank(collectCode), WithHolding::getCollectCode, collectCode)
                .set(WithHolding::getStatus, status).update();
    }

    public void updatePushStatusByWithHoldingCodes(List<String> notNeedSendCodeList, HecSendStatusEnum hecSendStatusEnum) {
        if (CollectionUtil.isEmpty(notNeedSendCodeList)
                || Objects.isNull(hecSendStatusEnum)) {
            return;
        }
        this.lambdaUpdate()
                .in(WithHolding::getWithHoldingCode, notNeedSendCodeList)
                .set(WithHolding::getPushStatus, hecSendStatusEnum.getCode())
                .update();
    }

    public WithHoldingVo findTotalByConditions(WithHoldingDto dto) {
        List<WithHoldingVo> list = baseMapper.findTotalByConditions(dto);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : new WithHoldingVo();
    }

    public List<WithHoldingIncomeVo> findActualReportAmountByYears(List<String> years) {
        return this.baseMapper.findActualReportAmountByYears(years);
    }

    public BigDecimal findAmountByOrgCodesAndYears(List<String> orgCodes, String years) {
        return this.baseMapper.findAmountByOrgCodesAndYears(orgCodes, years);
    }

    public List<WithHoldingVo> findWithholdingListByYearsAndOrgCodes(WithholdingIncomeQueryDto dto) {
        return this.baseMapper.findWithholdingListByYearsAndOrgCodes(dto);
    }


    public List<WithHolding> findWithholdingConfirmedByYearMonthly(String yearMonthly) {
        return this.lambdaQuery()
                .eq(WithHolding::getYearMonthLy, yearMonthly)
                .eq(WithHolding::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(WithHolding::getTenantCode, TenantUtils.getTenantCode())
                .eq(WithHolding::getConfirmStatus, ConfirmStatusEnum.CONFIRMED.getCode())
                .list();
    }

    /**
     * 分页查询计提余额数据
     * 实现WithHoldingBalanceDataViewRegister的SQL逻辑
     *
     * @param pageable 分页对象
     * @param dto 查询条件
     * @return 计提余额分页数据
     */
    public Page<WithHoldingBalanceVo> findWithHoldingBalanceByConditions(Pageable pageable, WithHoldingBalanceDto dto) {
        Page<WithHoldingBalanceVo> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<WithHoldingBalanceVo> pageList = withHoldingMapper.findWithHoldingBalanceByConditions(page, dto);
        return pageList;
    }
}

