package com.biz.crm.tpm.business.pay.local.service.internal.process;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.pay.sdk.service.ManageReportService;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportImportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ManageReportImportProcess implements ImportProcess<ManageReportImportVo> {


    @Autowired(required = false)
    private CustomerVoService customerVoServiceFeign;
    @Autowired(required = false)
    private ProductVoService productVoServiceFeign;
    @Autowired(required = false)
    private OrgVoService orgVoService;
    @Autowired(required = false)
    private MdmCostCenterVoService mdmCostCenterVoService;
    @Autowired(required = false)
    private ManageReportService manageReportService;


    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, ManageReportImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, ManageReportImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");


        for (Map.Entry<Integer, ManageReportImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            ManageReportImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCompanyCode()), "公司编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getYearMonthLy()), "收入年月，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getDepartmentCode()), "部门编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostCenterCode()), "成本中心编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getErpCode()), "客户ERP编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getProductCode()), "产品编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getAmountStr()), "销售金额，不能为空！");
            try {
                new BigDecimal(vo.getAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "销售金额类型转换失败！");
            }
            if (StringUtils.isNotBlank(vo.getQuantityStr())) {
                try {
                    new BigDecimal(vo.getQuantityStr().trim());
                } catch (Exception e) {
                    this.validateIsTrue(false, "销售数量类型转换失败！");
                }
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, ManageReportImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();

        Set<String> orgCodeSet = new HashSet<>();
        Set<String> productCodeSet = new HashSet<>();
        Set<String> erpCodeSet = new HashSet<>();
        Set<String> centerCodeSet = new HashSet<>();

        for (Map.Entry<Integer, ManageReportImportVo> row : data.entrySet()) {
            ManageReportImportVo vo = row.getValue();

            orgCodeSet.add(vo.getDepartmentCode());
            erpCodeSet.add(vo.getErpCode());
            productCodeSet.add(vo.getProductCode());
            centerCodeSet.add(vo.getCostCenterCode());
        }

        Map<String, OrgVo> orgVoMap = orgVoService.findByOrgCodes(new ArrayList<>(orgCodeSet)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), Function.identity(), (a, b) -> a));
        Map<String, Map<String, CustomerVo>> customerVoMap = customerVoServiceFeign.findByErpCodes(new ArrayList<>(erpCodeSet)).stream().collect(Collectors.groupingBy(e -> e.getErpCode(), Collectors.toMap(e -> e.getCompanyCode(), Function.identity(), (a, b) -> a)));
        Map<String, ProductVo> productVoMap = productVoServiceFeign.findDetailsByIdsOrProductCodes(null, new ArrayList<>(productCodeSet)).stream().collect(Collectors.toMap(e -> e.getProductCode(), Function.identity(), (a, b) -> a));
        Map<String, MdmCostCenterVo> costCenterVoMap = mdmCostCenterVoService.findByCodes(new ArrayList<>(centerCodeSet)).stream().collect(Collectors.toMap(e -> e.getCostCenterCode(), Function.identity(), (a, b) -> a));

        for (Map.Entry<Integer, ManageReportImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();

            ManageReportImportVo vo = row.getValue();
            vo.setAmount(new BigDecimal(vo.getAmountStr().trim()));
            vo.setQuantity(StringUtils.isNotEmpty(vo.getQuantityStr()) ? new BigDecimal(vo.getQuantityStr().trim()) : BigDecimal.ZERO);

            if (orgVoMap.containsKey(vo.getDepartmentCode())) {
                vo.setDepartmentName(orgVoMap.get(vo.getDepartmentCode()).getOrgName());
                vo.setLevelNum(orgVoMap.get(vo.getDepartmentCode()).getLevelNum());
            } else {
                this.validateIsTrue(false, "部门，未找到！");
            }
            if (productVoMap.containsKey(vo.getProductCode())) {
                ProductVo productVo = productVoMap.get(vo.getProductCode());
                vo.setProductName(productVo.getProductName());
                vo.setItemCode(productVo.getProductPhaseCode());
                vo.setItemName(productVo.getProductPhaseName());
            } else {
                this.validateIsTrue(false, "产品，未找到！");
            }
            if (customerVoMap.containsKey(vo.getErpCode())) {
                Map<String, CustomerVo> companyVoMap = customerVoMap.get(vo.getErpCode());
                if (companyVoMap.containsKey(vo.getCompanyCode())) {
                    CustomerVo customerVo = companyVoMap.get(vo.getCompanyCode());
                    this.validateIsTrue(BooleanEnum.TRUE.getCapital().equals(customerVo.getContractCustomer()), "客户不是合同客户");
                    vo.setCustomerCode(customerVo.getCustomerCode());
                    vo.setCustomerName(customerVo.getCustomerName());

                } else {
                    this.validateIsTrue(false, "客户，未找到！");
                }
            } else {
                this.validateIsTrue(false, "客户，未找到！");
            }
            if (costCenterVoMap.containsKey(vo.getCostCenterCode())) {
                vo.setCostCenterName(costCenterVoMap.get(vo.getCostCenterCode()).getCostCenterName());
            } else {
                this.validateIsTrue(false, "成本中心，未找到！");
            }


            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }

        manageReportService.saveBatch(new ArrayList<>(data.values()));
        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<ManageReportImportVo> findCrmExcelVoClass() {
        return ManageReportImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "MANAGE_REPORT_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "管报收入导入模板";
    }
}
