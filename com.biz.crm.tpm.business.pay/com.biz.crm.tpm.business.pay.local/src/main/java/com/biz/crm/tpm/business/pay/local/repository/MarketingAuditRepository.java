package com.biz.crm.tpm.business.pay.local.repository;



import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAudit;
import com.biz.crm.tpm.business.pay.local.mapper.MarketingAuditMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 方案结案(MarketingAudit)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 16:58:19
 */
@Component
public class MarketingAuditRepository extends ServiceImpl<MarketingAuditMapper, MarketingAudit> {

  @Autowired
  private MarketingAuditMapper marketingAuditMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;


  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<MarketingAudit>
   */
  public List<MarketingAudit> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(MarketingAudit::getId, ids)
            .eq(MarketingAudit::getTenantCode, tenantCode)
            .eq(MarketingAudit::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .list();
  }

  /**
   * 根据编号与租户编号获取对象
   *
   * @param code
   * @return
   */
  public MarketingAudit findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(MarketingAudit::getAuditCode, code)
            .eq(MarketingAudit::getTenantCode, tenantCode)
            .eq(MarketingAudit::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
  }

  /**
   * 根据编号集合获取详情集合
   *
   * @param codes 编号集合
   * @return List<MarketingAudit>
   */
  public List<MarketingAudit> findByCodes(List<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(MarketingAudit::getAuditCode, codes)
            .eq(MarketingAudit::getTenantCode, tenantCode)
            .eq(MarketingAudit::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .list();
  }

  /**
   * 查询完全结案的方案明细数据
   * @param code
   * @return
   */
  public List<MarketingAuditDetailVo> findListByApprovedAndFullAudit(String code){
    return marketingAuditMapper.findListByApprovedAndFullAudit(code);
  }

  public List<MarketingAuditVo> findCommit(String yearMonthLy) {
    return marketingAuditMapper.findCommit(yearMonthLy);
  }
}

