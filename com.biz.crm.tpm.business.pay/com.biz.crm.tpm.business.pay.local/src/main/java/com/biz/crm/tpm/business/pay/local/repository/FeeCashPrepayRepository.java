package com.biz.crm.tpm.business.pay.local.repository;



import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashPayee;
import com.biz.crm.tpm.business.pay.local.entity.FeeCash;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashPrepay;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashMapper;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashPrepayMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.enums.CashTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPrepayVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 费用预付明细(FeeCashPrepay)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-23 17:12:21
 */
@Component
public class FeeCashPrepayRepository extends ServiceImpl<FeeCashPrepayMapper, FeeCashPrepay> {

  @Autowired
  private FeeCashPrepayMapper feeCashPrepayMapper;
  @Autowired
  private FeeCashRepository feeCashRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<FeeCashPrepayVo> findByCode(String code) {
    List<FeeCashPrepay> list = this.lambdaQuery().eq(FeeCashPrepay::getCashCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashPrepay.class, FeeCashPrepayVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按活动明细编码查询
   *
   * @param code
   * @return
   */
  public List<FeeCashPrepayVo> findBySchemeDetailCode(String code) {
    return feeCashPrepayMapper.findBySchemeDetailCode(code);
  }

  /**
   * 按编码删除
   *
   * @param codes
   */
  public void deleteByCodes(List<String> codes) {
    this.remove(Wrappers.lambdaQuery(FeeCashPrepay.class)
            .in(FeeCashPrepay::getCashCode, codes));
  }

    public BigDecimal getAvailable_reversed_amount(String detailCode) {
      return feeCashPrepayMapper.getAvailableReversedAmount(detailCode);
    }

    public List<FeeCashPrepayDto> findByCodeTwo(String cashCode) {
      List<FeeCashPrepay> list = this.lambdaQuery().eq(FeeCashPrepay::getCashCode, cashCode).list();
      return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashPrepay.class, FeeCashPrepayDto.class, LinkedHashSet.class, ArrayList.class)) :
              new ArrayList<>();
    }

  public List<FeeCashPrepayVo> findByCodeAndPayeeCodeAndCacheList(String cashCode, String payeeCode, List<FeeCashDetailDto> cacheList) {
    if(!CollectionUtils.isEmpty(cacheList)){
        FeeCash code = feeCashRepository.findByCode(cashCode);
        if(code.getCashType().equals(CashTypeEnum.WIREDUIFU.getDictCode())){
          cacheList=null;
        }
    }
    List<FeeCashPrepay> list = this.lambdaQuery().eq(FeeCashPrepay::getCashCode, cashCode)
            .eq(StringUtils.isNotBlank(payeeCode),FeeCashPrepay::getPayeeCode,payeeCode)
            .in(!CollectionUtils.isEmpty(cacheList),FeeCashPrepay::getSchemeDetailCode,cacheList.stream().map(o->o.getSchemeDetailCode()).distinct().collect(Collectors.toList()))
            .list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashPrepay.class, FeeCashPrepayVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }
}

