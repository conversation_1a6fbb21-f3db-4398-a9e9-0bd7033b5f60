package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.*;
import com.biz.crm.tpm.business.pay.local.repository.*;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.constant.OrderAdjustConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.OrderAdjustDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.OrderAdjustDto;
import com.biz.crm.tpm.business.pay.sdk.dto.OrderAdjustProductDto;
import com.biz.crm.tpm.business.pay.sdk.enums.SapOrderTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.CreditOrderService;
import com.biz.crm.tpm.business.pay.sdk.service.OrderAdjustService;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustProductVo;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderAdjustServiceImpl implements OrderAdjustService {

    @Autowired(required = false)
    private OrderAdjustRepository orderAdjustRepository;
    @Autowired(required = false)
    private OrderAdjustDetailRepository orderAdjustDetailRepository;
    @Autowired(required = false)
    private OrderAdjustProductRepository orderAdjustProductRepository;
    @Autowired(required = false)
    private CreditOrderTicketRepository creditOrderTicketRepository;
    @Autowired(required = false)
    private CreditOrderRepository creditOrderRepository;
    @Autowired(required = false)
    private CreditOrderService creditOrderService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private CustomerVoService customerVoService;
    @Autowired(required = false)
    private OrgVoService orgVoService;

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    @Override
    public OrderAdjustVo findByCode(String code) {
        OrderAdjustVo vo = orderAdjustRepository.findByCode(code);
        vo.setDetailList(orderAdjustDetailRepository.findByCode(code));
        vo.setProductList(orderAdjustProductRepository.findByCode(code));
        return vo;
    }

    /**
     * 创建
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(OrderAdjustDto dto) {
        createValidate(dto);
        return store(dto);
    }

    /**
     * 编辑
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(OrderAdjustDto dto) {
        updateValidate(dto);
        store(dto);
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    public String store(OrderAdjustDto dto) {
        if (StringUtils.isBlank(dto.getId())) {
            String code = generateCodeService.generateCode(OrderAdjustConstant.PREFIX_CODE);
            dto.setAdjustCode(code);
            dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            dto.setTenantCode(TenantUtils.getTenantCode());
            dto.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
        } else {
            orderAdjustDetailRepository.deleteByCodes(Arrays.asList(dto.getAdjustCode()));
            orderAdjustProductRepository.deleteByCodes(Arrays.asList(dto.getAdjustCode()));
        }
        dto.getDetailList().forEach(e -> {
            e.setAdjustCode(dto.getAdjustCode());
            e.setAdjustName(dto.getAdjustName());
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setTenantCode(TenantUtils.getTenantCode());
        });
        dto.getProductList().forEach(e -> {
            e.setAdjustCode(dto.getAdjustCode());
            e.setAdjustName(dto.getAdjustName());
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setTenantCode(TenantUtils.getTenantCode());
        });
        OrderAdjust entity = nebulaToolkitService.copyObjectByWhiteList(dto, OrderAdjust.class, LinkedHashSet.class, ArrayList.class);
        Collection<OrderAdjustDetail> details = nebulaToolkitService.copyCollectionByWhiteList(dto.getDetailList(), OrderAdjustDetailDto.class, OrderAdjustDetail.class, LinkedHashSet.class, ArrayList.class);
        Collection<OrderAdjustProduct> products = nebulaToolkitService.copyCollectionByWhiteList(dto.getProductList(), OrderAdjustProductDto.class, OrderAdjustProduct.class, LinkedHashSet.class, ArrayList.class);

        orderAdjustRepository.saveOrUpdate(entity);
        orderAdjustDetailRepository.saveBatch(details);
        orderAdjustProductRepository.saveBatch(products);
        return dto.getAdjustCode();
    }

    /**
     * 创建并确认
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitCreate(OrderAdjustDto dto) {
        String code = create(dto);
        confirm(Arrays.asList(code));
    }

    /**
     * 编辑并确认
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitUpdate(OrderAdjustDto dto) {
        update(dto);
        confirm(Arrays.asList(dto.getAdjustCode()));
    }


    /**
     * 创建验证
     *
     * @param dto
     */
    private void createValidate(OrderAdjustDto dto) {
        Validate.notNull(dto, "新增时，对象信息不能为空！");
        commonValidate(dto);
        dto.setId(null);
    }

    /**
     * 修改验证
     *
     * @param dto
     */
    private void updateValidate(OrderAdjustDto dto) {
        Validate.notNull(dto, "修改时，对象信息不能为空！");
        commonValidate(dto);
        Validate.notBlank(dto.getId(), "主键id不能为空！");
        Validate.notBlank(dto.getAdjustCode(), "调整编码不能为空！");
    }

    /**
     * 共有校验
     *
     * @param dto
     */
    private void commonValidate(OrderAdjustDto dto) {
        Validate.notBlank(dto.getAdjustName(), "调整名称，不能为空");
        Validate.notBlank(dto.getCustomerCode(), "客户编码，不能为空");
        Validate.notBlank(dto.getErpCode(), "客户ERP编码，不能为空");
        Validate.notBlank(dto.getRemark(), "备注，不能为空");
        Validate.notEmpty(dto.getDetailList(), "关联贷项订单，不能为空");
        Validate.notEmpty(dto.getProductList(), "贷项订单，不能为空");
        AtomicReference<BigDecimal> adjustAmount = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> amount = new AtomicReference<>(BigDecimal.ZERO);
        dto.getDetailList().forEach(e -> {
            Validate.notNull(e.getAdjustAmount(), "调整金额，不能为空");
            Validate.isTrue(e.getAdjustAmount().compareTo(e.getAmount()) <= 0, "调整金额，不可大于可用余额");
            adjustAmount.set(adjustAmount.get().add(e.getAdjustAmount()));
        });
        dto.getProductList().forEach(e -> {
            Validate.notNull(e.getAmount(), "订单金额，不能为空");
            amount.set(amount.get().add(e.getAmount()));
        });
        Validate.isTrue(adjustAmount.get().compareTo(amount.get()) == 0, "订单金额之和必须等于调整金额之和");
    }

    /**
     * 根据调整编号确认
     *
     * @param codeList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirm(List<String> codeList) {
        List<OrderAdjustVo> voList = orderAdjustRepository.findByCodes(codeList);
        voList.forEach(e -> Validate.isTrue(ConfirmStatusEnum.UNCONFIRMED.getCode().equals(e.getConfirmStatus()), "已确认不允许确认"));

        Map<String, List<OrderAdjustDetailVo>> detailMap = orderAdjustDetailRepository.findByCodes(codeList).stream().collect(Collectors.groupingBy(e -> e.getAdjustCode()));
        Map<String, List<OrderAdjustProductVo>> productMap = orderAdjustProductRepository.findByCodes(codeList).stream().collect(Collectors.groupingBy(e -> e.getAdjustCode()));

        //调整之后的原贷项订单明细新增标识，不可重复确认
        Set<String> uniqueKeySet = detailMap.values().stream().flatMap(List::stream).collect(Collectors.toList()).stream().map(e -> e.getCreditCode() + "_" + e.getLineNumber()).collect(Collectors.toSet());
        creditOrderTicketRepository.updateBeAdjusted(uniqueKeySet);


        //获取客户所对应的一级、二级部门
        Set<String> customerCodeSet = voList.stream().map(e -> e.getCustomerCode()).collect(Collectors.toSet());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodeSet));
        Map<String, CustomerVo> cusOrgMap = customerVos.stream().collect(Collectors.toMap(e -> e.getCustomerCode(), Function.identity(), (a, b) -> a));
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllParentByOrgCodesMap(new ArrayList<>(cusOrgMap.values().stream().map(e -> e.getOrgList().get(0).getOrgCode()).collect(Collectors.toSet())));

        //生成贷项订单
        List<CreditOrder> orderList = new ArrayList<>();
        List<CreditOrderTicket> ticketList = new ArrayList<>();
        List<String> codes = generateCodeService.generateCode(FeeCashConstant.PREFIX_CODE_DXDD, voList.size() * 2);
        AtomicInteger index = new AtomicInteger(0);
        voList.forEach(e -> {
            //借项订单
            CreditOrder creditOrderD = new CreditOrder();
            creditOrderD.setOrderType(SapOrderTypeEnum.ZDR1.getDictCode());
            creditOrderD.setBtNo(e.getAdjustCode());
            CustomerVo customerVo = cusOrgMap.get(e.getCustomerCode());
            creditOrderD.setCompanyCode(customerVo.getCompanyCode());
            List<OrgVo> orgVos = orgMap.get(customerVo.getOrgList().get(0).getOrgCode());
            OrgVo one = orgVos.stream().filter(m -> m.getLevelNum().equals(2)).findFirst().orElse(new OrgVo());
            OrgVo two = orgVos.stream().filter(m -> m.getLevelNum().equals(3)).findFirst().orElse(new OrgVo());
            creditOrderD.setDepartmentOneCode(one.getOrgCode());
            creditOrderD.setDepartmentOneName(one.getOrgName());
            creditOrderD.setDepartmentTwoCode(two.getOrgCode());
            creditOrderD.setDepartmentTwoName(two.getOrgName());

            creditOrderD.setCustomerCode(customerVo.getCustomerCode());
            creditOrderD.setCustomerName(customerVo.getCustomerName());
            creditOrderD.setErpCode(customerVo.getErpCode());
            creditOrderD.setChannelCode(customerVo.getChannelCode());
            creditOrderD.setCreditCode(codes.get(index.get()));
            creditOrderD.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            creditOrderD.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            creditOrderD.setTenantCode(TenantUtils.getTenantCode());
            creditOrderD.setPushStatus(DmsWarehouseOrderEnum.SAP_PUSH_STATUS.NO_PUSH.getCode());

            index.getAndIncrement();
            AtomicInteger lineNumber1 = new AtomicInteger(10);
            List<OrderAdjustDetailVo> detailVos = detailMap.get(e.getAdjustCode());

            AtomicReference<BigDecimal> amountD = new AtomicReference<>(BigDecimal.ZERO);
            Iterator<OrderAdjustDetailVo> iterator = detailVos.iterator();
            while (iterator.hasNext()) {
                OrderAdjustDetailVo next = iterator.next();
                //金额＜0.01，不生成贷项订单
                if (next.getAdjustAmount().compareTo(new BigDecimal("0.01")) < 0) {
                    iterator.remove();
                } else {
                    next.setId(null);
                    next.setCreditCode(creditOrderD.getCreditCode());
                    next.setLineNumber(lineNumber1.get());
                    next.setPrice(next.getAdjustAmount());
                    next.setAmount(next.getAdjustAmount());
                    lineNumber1.getAndAdd(10);
                    amountD.set(amountD.get().add(next.getAmount()));
                }
            }
            if (amountD.get().compareTo(BigDecimal.ZERO) > 0) {
                creditOrderD.setAmount(amountD.get());
                orderList.add(creditOrderD);
                Collection<CreditOrderTicket> tickets = nebulaToolkitService.copyCollectionByWhiteList(detailVos, OrderAdjustDetailVo.class, CreditOrderTicket.class, LinkedHashSet.class, ArrayList.class);
                tickets.forEach(m -> m.setUnTicketAmount(m.getAmount()));
                ticketList.addAll(tickets);
            }


            //贷项订单
            CreditOrder creditOrderJ = new CreditOrder();
            BeanUtils.copyProperties(creditOrderD, creditOrderJ);
            creditOrderJ.setOrderType(SapOrderTypeEnum.ZCR1.getDictCode());
            creditOrderJ.setCreditCode(codes.get(index.get()));
            index.getAndIncrement();
            AtomicInteger lineNumber2 = new AtomicInteger(10);
            List<OrderAdjustProductVo> productVos = productMap.get(e.getAdjustCode());
            Collection<CreditOrderTicket> tickets = nebulaToolkitService.copyCollectionByWhiteList(productVos, OrderAdjustProductVo.class, CreditOrderTicket.class, LinkedHashSet.class, ArrayList.class);

            AtomicReference<BigDecimal> amountJ = new AtomicReference<>(BigDecimal.ZERO);
            Iterator<CreditOrderTicket> iterator2 = tickets.iterator();

            while (iterator2.hasNext()) {
                CreditOrderTicket next = iterator2.next();
                //金额＜0.01，不生成贷项订单
                if (next.getAmount().compareTo(new BigDecimal("0.01")) < 0) {
                    iterator2.remove();
                } else {
                    next.setId(null);
                    next.setCreditCode(creditOrderJ.getCreditCode());
                    next.setLineNumber(lineNumber2.get());
                    next.setQuantity(BigDecimal.ONE);
                    next.setPrice(next.getAmount());
                    next.setUnTicketAmount(next.getAmount());
                    lineNumber2.getAndAdd(10);
                    amountJ.set(amountJ.get().add(next.getAmount()));
                }
            }
            if (amountJ.get().compareTo(BigDecimal.ZERO) > 0) {
                creditOrderJ.setAmount(amountJ.get());
                orderList.add(creditOrderJ);
                ticketList.addAll(tickets);
            }
        });

        if (!CollectionUtils.isEmpty(orderList)) {
            creditOrderRepository.saveBatch(orderList);
            creditOrderTicketRepository.saveBatch(ticketList);
            orderAdjustRepository.confirm(codeList);
//            creditOrderService.pushSap(null, codes);
        }
    }

    /**
     * 删除数据
     *
     * @param idList
     * @return
     */
    @Override
    public void delete(List<String> idList) {
        Validate.notEmpty(idList, "删除时，id不能为空");
        List<OrderAdjust> list = this.orderAdjustRepository.listByIds(idList);
        Validate.notEmpty(list, "未找到对应的数据");
        list.forEach(e -> Validate.isTrue(ConfirmStatusEnum.UNCONFIRMED.getCode().equals(e.getConfirmStatus()), "已确认不允许删除"));
        List<String> codeList = list.stream().map(e -> e.getAdjustCode()).collect(Collectors.toList());
        orderAdjustDetailRepository.deleteByCodes(codeList);
        orderAdjustProductRepository.deleteByCodes(codeList);
    }
}
