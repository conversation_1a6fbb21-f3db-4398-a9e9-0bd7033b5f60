package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@ApiModel(value = "FeeCashMaterial", description = "采购需求信息")
@TableName("tpm_fee_cash_material")
@Getter
@Setter
@Entity(name = "tpm_fee_cash_material")
@org.hibernate.annotations.Table(appliesTo = "tpm_fee_cash_material", comment = "采购需求信息")
@Table(name = "tpm_fee_cash_material", indexes = {
        @Index(name = "fee_cash_idx1", columnList = "cash_code"),
})
public class FeeCashMaterial extends TenantFlagOpEntity {

    @ApiModelProperty("兑付名称")
    @Column(name = "cash_name", columnDefinition = "VARCHAR(255) COMMENT '兑付名称 '")
    private String cashName;

    @ApiModelProperty("兑付编号")
    @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
    private String cashCode;

    @ApiModelProperty("物料编码")
    @Column(name = "material_code", length = 64, columnDefinition = "varchar(64) COMMENT '物料编码'")
    private String materialCode;

    @ApiModelProperty("物料名称")
    @Column(name = "material_name", columnDefinition = "varchar(255) COMMENT '物料名称'")
    private String materialName;

    @ApiModelProperty("商品参考链接")
    @Column(name = "url", columnDefinition = "varchar(255) COMMENT '商品参考链接'")
    private String url;

    @ApiModelProperty("数量")
    @Column(name = "quantity", columnDefinition = "decimal(20,6) COMMENT '数量'")
    private BigDecimal quantity;

    @ApiModelProperty("单价")
    @Column(name = "price", columnDefinition = "decimal(20,6) COMMENT '单价'")
    private BigDecimal price;

    @ApiModelProperty("金额")
    @Column(name = "amount", columnDefinition = "decimal(20,6) COMMENT '金额'")
    private BigDecimal amount;

    @ApiModelProperty("需求到货时间")
    @Column(name = "required_delivery_time", length = 64, columnDefinition = "varchar(64) COMMENT '需求到货时间'")
    private String requiredDeliveryTime;

    @ApiModelProperty(name = "contactPerson",notes = "联系人", value= "联系人")
    @Column(name = "contact_person", columnDefinition = "VARCHAR(255) COMMENT '联系人 '")
    private String contactPerson;

    @ApiModelProperty(name = "contactPhone",notes = "联系电话", value= "联系电话")
    @Column(name = "contact_phone", columnDefinition = "VARCHAR(20) COMMENT '联系电话 '")
    private String contactPhone;

    @ApiModelProperty(name = "address",notes = "邮寄地址", value= "邮寄地址")
    @Column(name = "address", columnDefinition = "VARCHAR(255) COMMENT '邮寄地址 '")
    private String address;
}
