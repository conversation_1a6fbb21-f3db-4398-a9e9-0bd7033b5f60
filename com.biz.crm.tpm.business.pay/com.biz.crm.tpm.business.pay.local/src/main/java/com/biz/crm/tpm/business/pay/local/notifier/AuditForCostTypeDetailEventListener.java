package com.biz.crm.tpm.business.pay.local.notifier;

import com.biz.crm.tpm.business.budget.sdk.event.CostTypeDetailEventListener;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>活动明细修改核销监听，对冗余存储的活动明细名称进行更新
 *
 * <AUTHOR>
 * @date 2022/6/28
 */
@Component
public class AuditForCostTypeDetailEventListener implements CostTypeDetailEventListener {
  @Autowired
  private AuditDetailService auditDetailService;

  public void onUpdate(CostTypeDetailVo oldCostTypeDetailVo, CostTypeDetailVo costTypeDetailVo) {
    if (!costTypeDetailVo.getDetailName().equals(oldCostTypeDetailVo.getDetailName())) {
      this.auditDetailService.updateCostTypeDetailName(oldCostTypeDetailVo.getDetailCode(), costTypeDetailVo.getDetailName());
    }
  }
}
