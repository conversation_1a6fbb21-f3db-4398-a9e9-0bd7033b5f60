package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashTicket;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.BillingCompareReportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTicketVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用票扣明细(FeeCashTicket)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
public interface FeeCashTicketMapper extends BaseMapper<FeeCashTicket> {

    List<FeeCashTicketVo> findByBillingCompareReport(@Param("dto") BillingCompareReportVo dto);
}

