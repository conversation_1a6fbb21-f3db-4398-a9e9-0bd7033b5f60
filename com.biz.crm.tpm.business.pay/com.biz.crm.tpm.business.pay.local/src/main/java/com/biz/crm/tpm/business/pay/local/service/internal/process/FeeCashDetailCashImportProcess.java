package com.biz.crm.tpm.business.pay.local.service.internal.process;

import cn.hutool.core.bean.BeanUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashInvoiceRepository;
import com.biz.crm.tpm.business.pay.local.repository.InvoiceDetailRepository;
import com.biz.crm.tpm.business.pay.local.service.internal.FeeCashDetailPageCacheHelper;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.enums.BillTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashInvoiceService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailCashImportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashInvoiceVo;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailPageVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FeeCashDetailCashImportProcess implements ImportProcess<FeeCashDetailCashImportVo> {

    @Autowired(required = false)
    private FeeCashDetailService feeCashDetailService;
    @Autowired(required = false)
    private FeeCashDetailPageCacheHelper feeCashDetailPageCacheHelper;

    @Autowired
    private InvoiceDetailRepository invoiceDetailRepository;

    @Autowired
    private FeeCashInvoiceService feeCashInvoiceService;


    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, FeeCashDetailCashImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, FeeCashDetailCashImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");

        for (Map.Entry<Integer, FeeCashDetailCashImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            FeeCashDetailCashImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getAuditDetailCode()), "结案明细编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getThisCashAmountStr()), "本次兑付金额，不能为空！");

            try {
                new BigDecimal(vo.getThisCashAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "本次兑付金额类型转换失败！");
            }

            // 票据类型校验
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getBillTypeDesc()), "票据类型必选！");

            if (StringUtils.isNotEmpty(vo.getBillTypeDesc())) {
                BillTypeEnum billTypeEnum = BillTypeEnum.findByValue(vo.getBillTypeDesc());
                this.validateIsTrue(billTypeEnum != null, "票据类型填写错误！");

                if (billTypeEnum != null) {
                    if (BillTypeEnum.INVOICE.equals(billTypeEnum)) {
                        // 当票据类型为发票时，第一发票号或第二发票号至少填写一个
                        boolean hasInvoiceNo = StringUtils.isNotEmpty(vo.getFirstInvoiceNo()) || StringUtils.isNotEmpty(vo.getSecondInvoiceNo());
                        this.validateIsTrue(hasInvoiceNo, "票据类型为发票时，第一发票号或第二发票号至少填写一个！");
                    } else if (BillTypeEnum.RECEIPT.equals(billTypeEnum)) {
                        // 当票据类型为收据时，收据说明必填，发票非必填
                        this.validateIsTrue(StringUtils.isNotEmpty(vo.getReceiptDescription()), "票据类型为收据时，收据说明必填！");
                    }
                }
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, FeeCashDetailCashImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();

        String cacheKey = (String) params.get("cacheKey");

        List<FeeCashDetailDto> cacheList = feeCashDetailService.findCacheList(cacheKey);
        Validate.notEmpty(cacheList, "未获取到任何兑付明细缓存");

        Map<String, FeeCashDetailDto> cashDetailMap = cacheList.stream().collect(Collectors.toMap(FeeCashDetailDto::getAuditDetailCode, Function.identity(), (a, b) -> a));

        List<FeeCashDetailDto> updateCacheList = new ArrayList<>();
        for (Map.Entry<Integer, FeeCashDetailCashImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            FeeCashDetailCashImportVo vo = row.getValue();

            FeeCashDetailDto cashDetailDto = cashDetailMap.get(vo.getAuditDetailCode());
            if (cashDetailDto != null) {
                cashDetailDto.setThisCashAmount(new BigDecimal(vo.getThisCashAmountStr()));
                cashDetailDto.setRemark(vo.getRemark());

                // 获取票据类型信息
                BillTypeEnum currentBillType = BillTypeEnum.findByValue(vo.getBillTypeDesc());
                String originalBillType = cashDetailDto.getBillType();

                // 设置票据类型和收据说明
                if (currentBillType != null) {
                    cashDetailDto.setBillType(currentBillType.getDictCode());
                    if (BillTypeEnum.RECEIPT.equals(currentBillType)) {
                        cashDetailDto.setReceiptDescription(vo.getReceiptDescription());
                    } else {
                        cashDetailDto.setReceiptDescription(null); // 发票类型时清空收据说明
                    }
                }

                String firstInvoiceNo = vo.getFirstInvoiceNo();
                String firstInvoiceRowNo = vo.getFirstInvoiceRowNo();
                String firstInvoiceAmountStr = vo.getFirstInvoiceAmountStr();
                String secondInvoiceNo = vo.getSecondInvoiceNo();
                String secondInvoiceRowNo = vo.getSecondInvoiceRowNo();
                String secondInvoiceAmountStr = vo.getSecondInvoiceAmountStr();

                List<FeeCashInvoiceDto> invoiceList = new ArrayList<>();

                // 票据类型处理逻辑
                boolean shouldProcessInvoice = false;
                if (BillTypeEnum.INVOICE.equals(currentBillType)) {
                    // 当前票据类型为发票时，需要处理发票关联
                    shouldProcessInvoice = true;
                } else if (BillTypeEnum.RECEIPT.equals(currentBillType)) {
                    // 当前票据类型为收据时
                    if (StringUtils.isNotEmpty(originalBillType) && BillTypeEnum.INVOICE.getDictCode().equals(originalBillType)) {
                        // 之前是发票，现在改为收据，需要删除发票关联（invoiceList保持为空）
                        shouldProcessInvoice = false;
                    } else {
                        // 第一次导入或之前就是收据，不走发票关联逻辑
                        shouldProcessInvoice = false;
                    }
                }

                if (shouldProcessInvoice && StringUtils.isNotEmpty(firstInvoiceNo) && StringUtils.isNotEmpty(firstInvoiceRowNo)) {
                    InvoiceDetailPageVo invoiceDetail = invoiceDetailRepository.findByInvoiceNoAndDetailNo(firstInvoiceNo, firstInvoiceRowNo);
                    if (invoiceDetail == null) {
                        this.validateIsTrue(false, "第一发票号，行号对应发票明细不存在！");
                    } else {
                        this.validateIsTrue(invoiceDetail.getPriceAndTax().compareTo(new BigDecimal(firstInvoiceAmountStr.trim())) >= 0, "报销金额高于了发票金额，请修改报销金额或更换发票！!");
                    }
                    FeeCashInvoiceDto feeCashInvoiceDto = BeanUtil.copyProperties(invoiceDetail, FeeCashInvoiceDto.class);
                    feeCashInvoiceDto.setAvailableReimbursementAmount(feeCashInvoiceDto.getPriceAndTax().subtract(new BigDecimal(firstInvoiceAmountStr.trim())));
                    feeCashInvoiceDto.setReimbursementAmount(new BigDecimal(firstInvoiceAmountStr.trim()));
                    feeCashInvoiceDto.setInvoiceDetailNo(firstInvoiceRowNo);
                    feeCashInvoiceDto.setDetailCode(cashDetailDto.getDetailCode());
                    feeCashInvoiceDto.setDetailName(cashDetailDto.getDetailName());
                    feeCashInvoiceDto.setAuditDetailCode(cashDetailDto.getAuditDetailCode());
                    invoiceList.add(feeCashInvoiceDto);
                }
                if (shouldProcessInvoice && StringUtils.isNotEmpty(secondInvoiceNo) && StringUtils.isNotEmpty(secondInvoiceRowNo)) {
                    InvoiceDetailPageVo invoiceDetail = invoiceDetailRepository.findByInvoiceNoAndDetailNo(secondInvoiceNo, secondInvoiceRowNo);
                    if (invoiceDetail == null) {
                        this.validateIsTrue(false, "第二发票号，行号对应发票明细不存在！");
                    } else {
                        this.validateIsTrue(invoiceDetail.getPriceAndTax().compareTo(new BigDecimal(secondInvoiceAmountStr.trim())) >= 0, "报销金额高于了发票金额，请修改报销金额或更换发票！!");
                    }
                    FeeCashInvoiceDto feeCashInvoiceDto = BeanUtil.copyProperties(invoiceDetail, FeeCashInvoiceDto.class);
                    feeCashInvoiceDto.setAvailableReimbursementAmount(feeCashInvoiceDto.getPriceAndTax().subtract(new BigDecimal(secondInvoiceAmountStr.trim())));
                    feeCashInvoiceDto.setReimbursementAmount(new BigDecimal(secondInvoiceAmountStr.trim()));
                    feeCashInvoiceDto.setInvoiceDetailNo(secondInvoiceRowNo);
                    feeCashInvoiceDto.setDetailCode(cashDetailDto.getDetailCode());
                    feeCashInvoiceDto.setDetailName(cashDetailDto.getDetailName());
                    feeCashInvoiceDto.setAuditDetailCode(cashDetailDto.getAuditDetailCode());
                    invoiceList.add(feeCashInvoiceDto);
                }
                cashDetailDto.setInvoiceList(invoiceList);
                updateCacheList.add(cashDetailDto);
                feeCashInvoiceService.updateItemCache(cacheKey, Lists.newArrayList(cashDetailDto.getAuditDetailCode()), invoiceList);
            } else {
                this.validateIsTrue(false, "未找到结案明细编码对应的兑付明细！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        feeCashDetailPageCacheHelper.importNewItem(cacheKey, updateCacheList);

        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<FeeCashDetailCashImportVo> findCrmExcelVoClass() {
        return FeeCashDetailCashImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "FEE_CASH_DETAIL_CASH_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "兑付明细导入模板";
    }
}
