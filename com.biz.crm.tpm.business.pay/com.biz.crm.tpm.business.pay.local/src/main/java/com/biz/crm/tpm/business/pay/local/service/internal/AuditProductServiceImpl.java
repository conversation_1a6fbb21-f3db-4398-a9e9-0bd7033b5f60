package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditProduct;
import com.biz.crm.tpm.business.pay.local.repository.AuditProductRepository;
import com.biz.crm.tpm.business.pay.local.service.AuditProductService;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditProductDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditProductVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.enums.RedisKeys.GENERATE_PREFIX;

/**
 * 费用明细商品;(tpm_audit_product)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Service("auditProductService")
public class AuditProductServiceImpl implements AuditProductService {
  @Autowired
  private AuditProductRepository auditProductRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private RedisMutexService redisMutexService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<AuditProductVo> findByConditions(Pageable pageable, AuditProductDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new AuditProductDto();
    }
    return this.auditProductRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public AuditProductVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    AuditProduct auditProduct = this.auditProductRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (auditProduct == null) {
      return null;
    }
    AuditProductVo auditProductVo = this.nebulaToolkitService.copyObjectByWhiteList(auditProduct, AuditProductVo.class, LinkedHashSet.class, ArrayList.class);
    return auditProductVo;
  }

  /**
   * 通过费用核销编号查询单条数据
   *
   * @param auditCode 主键
   * @return 多条数据
   */
  @Override
  public List<AuditProductVo> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return Collections.emptyList();
    }
    List<AuditProduct> auditProducts = this.auditProductRepository.findByAuditCode(auditCode);
    if (CollectionUtils.isEmpty(auditProducts)) {
      return Collections.emptyList();
    }
    Collection<AuditProductVo> auditProductVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditProducts, AuditProduct.class, AuditProductVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(auditProductVos);
  }

  /**
   * 通过费用核销明细编号查询单条数据
   *
   * @param auditDetailCode 主键
   * @return 多条数据
   */
  @Override
  public List<AuditProductVo> findByAuditDetailCode(String auditDetailCode) {
    if (StringUtils.isBlank(auditDetailCode)) {
      return Collections.emptyList();
    }
    List<AuditProduct> auditProducts = this.auditProductRepository.findByAuditDetailCode(auditDetailCode);
    if (CollectionUtils.isEmpty(auditProducts)) {
      return null;
    }
    Collection<AuditProductVo> auditProductVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditProducts, AuditProduct.class, AuditProductVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(auditProductVos);
  }

  /**
   * 新增数据
   *
   * @param auditProductDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AuditProductVo create(AuditProductDto auditProductDto) {
    this.createValidate(auditProductDto);
    AuditProduct auditProduct = this.nebulaToolkitService.copyObjectByWhiteList(auditProductDto, AuditProduct.class, LinkedHashSet.class, ArrayList.class);
    auditProduct.setTenantCode(TenantUtils.getTenantCode());
    this.auditProductRepository.saveOrUpdate(auditProduct);
    AuditProductVo auditProductVo = this.nebulaToolkitService.copyObjectByWhiteList(auditProduct, AuditProductVo.class, LinkedHashSet.class, ArrayList.class);
    auditProductVo.setId(auditProduct.getId());
    return auditProductVo;
  }

  @Override
  public List<AuditProductVo> createBatch(List<AuditProductDto> auditProductDtos) {
    List<AuditProductVo> auditActivitiesVos = Lists.newArrayList();
    for (AuditProductDto auditProductDto : auditProductDtos) {
      AuditProductVo auditProductVo = this.create(auditProductDto);
      auditActivitiesVos.add(auditProductVo);
    }
    return auditActivitiesVos;
  }

  /**
   * 修改新据
   *
   * @param auditProductDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public AuditProductVo update(AuditProductDto auditProductDto) {
    this.updateValidate(auditProductDto);
    AuditProduct auditProduct = this.auditProductRepository.findByIdAndTenantCode(auditProductDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(auditProduct, "修改数据不存在，请检查！");
    auditProduct.setProductCode(auditProductDto.getProductCode());
    auditProduct.setProductName(auditProductDto.getProductName());
    auditProduct.setTenantCode(TenantUtils.getTenantCode());
    this.auditProductRepository.saveOrUpdate(auditProduct);
    AuditProductVo auditProductVo = this.nebulaToolkitService.copyObjectByWhiteList(auditProduct, AuditProductVo.class, LinkedHashSet.class, ArrayList.class);

    return auditProductVo;
  }

  @Override
  public List<AuditProductVo> updateBatch(List<AuditProductDto> auditProductDtos) {
    if (CollectionUtils.isEmpty(auditProductDtos)) {
      return Lists.newArrayList();
    }
    String code = auditProductDtos.stream().map(AuditProductDto::getAuditCode).findFirst().get();
    List<AuditProductVo> dbAuditProductVos = this.findByAuditCode(code);
    if (CollectionUtils.isEmpty(dbAuditProductVos)) {
      dbAuditProductVos = Lists.newArrayList();
    }
    Set<String> dbIds = dbAuditProductVos.stream().map(AuditProductVo::getId).collect(Collectors.toSet());
    Set<String> currentIds = auditProductDtos.stream().map(AuditProductDto::getId).collect(Collectors.toSet());

    Set<String> delData = Sets.difference(dbIds, currentIds);
    if (!CollectionUtils.isEmpty(delData)) {
      this.delete(Lists.newArrayList(delData));
    }
    List<AuditProductDto> addDtos = auditProductDtos.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
    List<AuditProductDto> updateDtos = auditProductDtos.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
    List<AuditProductVo> auditProductVos = this.createBatch(addDtos);
    if (!CollectionUtils.isEmpty(updateDtos)) {
      updateDtos.forEach(item -> {
        auditProductVos.add(this.update(item));
      });
    }
    return auditProductVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<AuditProduct> auditProducts = this.auditProductRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(auditProducts)) {
      return;
    }
    Collection<AuditProductVo> auditProductVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditProducts, AuditProduct.class, AuditProductVo.class, LinkedHashSet.class, ArrayList.class);
    this.auditProductRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  @Override
  @Transactional
  public void deleteByAuditCode(String auditCode) {
    this.auditProductRepository.removeByAuditCode(auditCode);
  }

  @Override
  @Transactional
  public void deleteByAuditDetailCode(String auditDetailCode) {
    this.auditProductRepository.removeByAuditDetailCode(auditDetailCode);
  }

  /**
   * 生成操作标记
   */
  @Override
  public String preSave() {
    String prefix = UUID.randomUUID().toString();
    // 1天后过期
    this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, TimeUnit.MILLISECONDS.convert(1, TimeUnit.DAYS));
    return prefix;
  }

  /**
   * 创建验证
   *
   * @param auditProductDto
   */
  private void createValidate(AuditProductDto auditProductDto) {
    Validate.notNull(auditProductDto, "新增时，对象信息不能为空！");
    auditProductDto.setId(null);
    Validate.notBlank(auditProductDto.getAuditCode(), "新增数据时，费用核销编号不能为空！");
    Validate.notBlank(auditProductDto.getAuditDetailCode(), "新增数据时，费用核销明细编号不能为空！");
    Validate.notBlank(auditProductDto.getProductCode(), "新增数据时，商品编码不能为空！");
    Validate.notBlank(auditProductDto.getProductName(), "新增数据时，商品名称不能为空！");
  }

  /**
   * 修改验证
   *
   * @param auditProductDto
   */
  private void updateValidate(AuditProductDto auditProductDto) {
    Validate.notNull(auditProductDto, "修改时，对象信息不能为空！");
    Validate.notBlank(auditProductDto.getId(), "修改数据时，主键不能为空！");
    Validate.notBlank(auditProductDto.getProductCode(), "修改数据时，商品编码不能为空！");
    Validate.notBlank(auditProductDto.getProductName(), "修改数据时，商品名称不能为空！");
  }


}