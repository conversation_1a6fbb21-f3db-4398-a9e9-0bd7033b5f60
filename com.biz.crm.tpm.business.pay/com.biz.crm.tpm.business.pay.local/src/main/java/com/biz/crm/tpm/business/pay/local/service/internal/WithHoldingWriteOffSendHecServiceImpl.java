package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.service.CostControlLoginService;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAuditDetail;
import com.biz.crm.tpm.business.pay.local.entity.WithHolding;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff;
import com.biz.crm.tpm.business.pay.local.repository.MarketingAuditDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingWriteOffRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.WithHoldingWriteOffConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingWriteOffDto;
import com.biz.crm.tpm.business.pay.sdk.enums.*;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffSendHecService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingWriteOffVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class WithHoldingWriteOffSendHecServiceImpl implements WithHoldingWriteOffSendHecService {

    @Autowired(required = false)
    private WithHoldingWriteOffRepository withHoldingWriteOffRepository;

    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private CostControlLoginService costControlLoginService;

    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private FeeCashService feeCashService;

    @Value("${domain-name:}")
    private String domainName;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;

    /**
     * 冲销
     *
     * @param dtoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, List<WithHoldingWriteOffVo>> writeOff(List<WithHoldingWriteOffDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return Maps.newHashMap();
        }
        validateCommon(dtoList);
        List<String> detailCodeList = dtoList.stream().map(WithHoldingWriteOffDto::getActivitiesDetailCode).collect(Collectors.toList());
        List<WithHolding> withHoldings = withHoldingRepository.findListByBusinessCodes(detailCodeList);
        if (CollectionUtil.isEmpty(withHoldings)) {
            return Maps.newHashMap();
        }
        //查询历史冲销金额
        List<String> withHoldingCodes = withHoldings.stream().map(e -> e.getWithHoldingCode()).collect(Collectors.toList());
        List<WithHoldingWriteOffVo> writeOffHistoryVos = withHoldingWriteOffRepository.findByCodes(withHoldingCodes);
        Map<String, BigDecimal> writeOffHistoryMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(writeOffHistoryVos)) {
            writeOffHistoryMap = writeOffHistoryVos.stream().collect(Collectors.groupingBy(e -> e.getWithHoldingCode(), Collectors.mapping(e -> e.getWriteOffAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        Map<String, List<WithHoldingWriteOffDto>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(WithHoldingWriteOffDto::getActivitiesDetailCode));
        if (CollectionUtil.isEmpty(dtoMap)) {
            return Maps.newHashMap();
        }
        //找结案数据的创建人职位信息
        List<MarketingAuditDetail> auditDetailList = marketingAuditDetailRepository.findBySchemeDetailCodes(detailCodeList);
        Map<String, MarketingAuditDetail> posMap = auditDetailList.stream().filter(Objects::nonNull).filter(e -> StringUtil.isNotBlank(e.getPositionCode()))
                .collect(Collectors.toMap(MarketingAuditDetail::getSchemeDetailCode, Function.identity(), (a, b) -> a));
        Map<String, List<MarketingAuditDetail>> auditSchemeDetailMap = auditDetailList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(MarketingAuditDetail::getSchemeDetailCode));

        List<WithHoldingWriteOff> entities = new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(withHoldings, WithHolding.class, WithHoldingWriteOff.class, LinkedHashSet.class, ArrayList.class));

        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesApproved(detailCodeList);
        Map<String, BigDecimal> collect=Maps.newHashMap();
        if (!CollectionUtils.isEmpty(feeCashDetailVos)){
            collect = feeCashDetailVos.stream().filter(o->null!=o&&StringUtils.isNotBlank(o.getSchemeDetailCode())).collect(Collectors.groupingBy(FeeCashDetailVo::getSchemeDetailCode, Collectors.mapping(e -> e.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }

        List<WithHoldingWriteOff> saveBatch = new ArrayList<>();
        for (WithHoldingWriteOff entity : entities) {
            //
            List<WithHoldingWriteOffDto> offDtoList = dtoMap.get(entity.getBusinessCode());
            if (CollectionUtils.isEmpty(offDtoList)) {
                continue;
            }
            //如果冲销金额＞冲销对应计提单的计提余额时，不生成冲销单据（非结案）
            List<WithHoldingWriteOffDto> othersOffDtoList = offDtoList.stream().filter(e -> !WriteOffTypeEnum.AUDIT.getDictCode().equals(e.getWriteOffType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(othersOffDtoList)) {
                BigDecimal writeOffAmount = othersOffDtoList.stream()
                        .filter(e -> StringUtils.isBlank(e.getWriteOffYears()) || (StringUtils.isNotBlank(e.getWriteOffYears()) && DateUtil.parse(e.getWriteOffYears(), DateUtil.DEFAULT_YEAR_MONTH).compareTo(DateUtil.parse(entity.getYearMonthLy(), DateUtil.DEFAULT_YEAR_MONTH)) > 0))
                        .filter(Objects::nonNull)
                        .map(e -> e.getWriteOffAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal writeOffHistoryAmount = writeOffHistoryMap.getOrDefault(entity.getWithHoldingCode(), BigDecimal.ZERO);
                if ((entity.getActualAmount().subtract(Optional.ofNullable(writeOffAmount).orElse(BigDecimal.ZERO)).subtract(writeOffHistoryAmount)).compareTo(BigDecimal.ZERO) < 0) {
                    continue;
                }
            }
            for (WithHoldingWriteOffDto dto : offDtoList) {
                //冲销的年月必须在计提入账年月之后
                if (StringUtils.isNotBlank(dto.getWriteOffYears()) && DateUtil.parse(dto.getWriteOffYears(), DateUtil.DEFAULT_YEAR_MONTH).compareTo(DateUtil.parse(entity.getYearMonthLy(), DateUtil.DEFAULT_YEAR_MONTH)) <= 0) {
                    continue;
                }
                WithHoldingWriteOff newEntity = new WithHoldingWriteOff();
                BeanUtils.copyProperties(entity, newEntity);
                newEntity.setWriteOffType(dto.getWriteOffType());
                newEntity.setWriteOffTime(new Date());
                newEntity.setWriteOffYears(StringUtils.isBlank(dto.getWriteOffYears()) ? DateUtil.format(new Date(), DateUtil.DEFAULT_YEAR_MONTH) : dto.getWriteOffYears());
                newEntity.setAuditCreateAccount(StringUtils.isBlank(dto.getAuditCreateAccount()) ? posMap.get(dto.getActivitiesDetailCode()).getCreateAccount() : dto.getAuditCreateAccount());
                newEntity.setPositionCode(StringUtils.isBlank(dto.getPositionCode()) ? posMap.get(dto.getActivitiesDetailCode()).getPositionCode() : dto.getPositionCode());
                newEntity.setSourceCode(dto.getSourceCode());
                newEntity.setRuleCode(dto.getRuleCode());
                //完全结案时冲销计提金额与结案金额的差额
                if (WriteOffTypeEnum.AUDIT.getDictCode().equals(dto.getWriteOffType()) && BooleanEnum.TRUE.getCapital().equals(dto.getBeFullAudit())) {
                    if (!(ProcessStatusEnum.PASS.getDictCode().equals(newEntity.getStatus()) && StringUtils.isNotBlank(newEntity.getVoucherCode()))) {
                        continue;
                    }
                    BigDecimal sumAuditAmount = auditSchemeDetailMap.getOrDefault(newEntity.getActivitiesDetailCode(),
                                    Lists.newArrayList()).stream().filter(k -> Objects.nonNull(k.getAuditAmount()))
                            .map(MarketingAuditDetail::getAuditAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal subtract = newEntity.getActualAmount().add(Optional.ofNullable(collect.get(dto.getActivitiesDetailCode())).orElse(BigDecimal.ZERO)).subtract(sumAuditAmount);
                    if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                        log.warn("=============================活动明细编码[{}]无需冲销", newEntity.getActivitiesDetailCode());
                        continue;
                    }
                    //结案冲销计提余额校验
                    BigDecimal writeOffHistoryAmount = writeOffHistoryMap.getOrDefault(entity.getWithHoldingCode(), BigDecimal.ZERO);
                    if ((entity.getActualAmount().subtract(Optional.ofNullable(subtract).orElse(BigDecimal.ZERO)).subtract(writeOffHistoryAmount)).compareTo(BigDecimal.ZERO) < 0) {
                        continue;
                    }
                    newEntity.setWriteOffAmount(subtract);
                    //按单次兑付的金额冲销（兑付推送费控系统成功时）；完全兑付时按兑付金额冲销，若有剩余未冲销金额则进行关闭冲销
                } else if (WriteOffTypeEnum.CASH.getDictCode().equals(dto.getWriteOffType())) {
                    if (!(ProcessStatusEnum.PASS.getDictCode().equals(newEntity.getStatus()) && StringUtils.isNotBlank(newEntity.getVoucherCode()))) {
                        continue;
                    }
                    newEntity.setWriteOffAmount(dto.getWriteOffAmount());
                    //补偿兑付，无需再走关闭冲销判断
                    if (BooleanEnum.TRUE.getCapital().equals(dto.getBeWholeCash()) && !BooleanEnum.TRUE.getCapital().equals(dto.getBeReimburse())) {
                        BigDecimal subtract = dto.getAuditAmount().subtract(ObjectUtils.defaultIfNull(dto.getHistoryAmount(),BigDecimal.ZERO));
                        if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                            newEntity.setWriteOffType(WriteOffTypeEnum.CLOSE.getDictCode());
                            newEntity.setWriteOffAmount(subtract);
                        }
                    }
                    //发货单冲销：单据业务发生日期所在年月与对应计提入账年月不一致时才冲销
                } else if (WriteOffTypeEnum.ORDER.getDictCode().equals(dto.getWriteOffType())) {
                    if (!(ProcessStatusEnum.PASS.getDictCode().equals(newEntity.getStatus()) && StringUtils.isNotBlank(newEntity.getVoucherCode())) ||
                            dto.getWriteOffYears().equals(newEntity.getYearMonthLy())) {
                        continue;
                    }
                    newEntity.setWriteOffAmount(dto.getWriteOffAmount());
                } else if (WriteOffTypeEnum.CLOSE.getDictCode().equals(dto.getWriteOffType())) {
                    //DMS清空关闭需要报错，TPM冲销只需要跳过
                    if (!(ProcessStatusEnum.PASS.getDictCode().equals(newEntity.getStatus()) && StringUtils.isNotBlank(newEntity.getVoucherCode()))) {
                        if (BooleanEnum.TRUE.getCapital().equals(dto.getBeDms())) {
                            Validate.isTrue(false, "【%s】费用计提数据尚未确认，请先处理数据", newEntity.getWithHoldingCode());
                        } else {
                            continue;
                        }
                    }
                    newEntity.setWriteOffAmount(dto.getWriteOffAmount());
                } else {
                    newEntity.setWriteOffAmount(dto.getWriteOffAmount());
                }

                newEntity.setId(null);
                newEntity.setCreateTime(null);
                newEntity.setCreateAccount(null);
                newEntity.setCreateName(null);
                newEntity.setModifyTime(null);
                newEntity.setModifyAccount(null);
                newEntity.setModifyName(null);
                newEntity.setVoucherCode(null);
                newEntity.setPushStatus(null);
                newEntity.setFailMsg(null);
                newEntity.setExternalCode(null);
                newEntity.setManageReportWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());

                saveBatch.add(newEntity);
            }
        }
        if (CollectionUtil.isEmpty(saveBatch)) {
            return Maps.newHashMap();
        }
        List<String> codes = generateCodeService.generateCode(WithHoldingWriteOffConstant.PREFIX_CODE, saveBatch.size());
        for (int i = 0; i < saveBatch.size(); i++) {
            saveBatch.get(i).setWriteOffCode(codes.get(i));
            saveBatch.get(i).setBeThisFee(BooleanEnum.FALSE.getCapital());
        }
        //冲销单生成的时候需要加唯一性校验：冲销来源单号+冲销类型
//        List<String> uniqueKeys = saveBatch.stream().map(e -> e.getRuleCode()).collect(Collectors.toList());
//        List<WithHoldingWriteOff> uniqueWriteOff = withHoldingWriteOffRepository.findByUniqueKeys(uniqueKeys);
//        if (!CollectionUtils.isEmpty(uniqueWriteOff)) {
//            List<String> uniqueWriteOffKeys = uniqueWriteOff.stream().map(e -> e.getRuleCode()).collect(Collectors.toList());
//            Iterator<WithHoldingWriteOff> iterator = saveBatch.iterator();
//            while (iterator.hasNext()) {
//                WithHoldingWriteOff next = iterator.next();
//                if (uniqueWriteOffKeys.contains(next.getRuleCode())) {
//                    iterator.remove();
//                }
//            }
//        }
        //冲销单生成的时候需要加唯一性校验：编码规则
        List<String> uniqueKeys = saveBatch.stream().map(e -> e.getRuleCode()).collect(Collectors.toList());
        List<WithHoldingWriteOff> uniqueWriteOff = withHoldingWriteOffRepository.findByUniqueKeys(uniqueKeys);
        if (!CollectionUtils.isEmpty(uniqueWriteOff)) {
            List<String> uniqueWriteOffKeys = uniqueWriteOff.stream().map(e -> e.getRuleCode()).collect(Collectors.toList());
            Iterator<WithHoldingWriteOff> iterator = saveBatch.iterator();
            while (iterator.hasNext()) {
                WithHoldingWriteOff next = iterator.next();
                if (uniqueWriteOffKeys.contains(next.getRuleCode())) {
                    iterator.remove();
                }
            }
        }
        if (CollectionUtils.isEmpty(saveBatch)) {
            return new HashMap<>();
        }
        withHoldingWriteOffRepository.saveBatch(saveBatch);
        withHoldingSendHec(saveBatch);
        Collection<WithHoldingWriteOffVo> withHoldingWriteOffVos = nebulaToolkitService.copyCollectionByWhiteList(saveBatch, WithHoldingWriteOff.class, WithHoldingWriteOffVo.class, LinkedHashSet.class, ArrayList.class);
        return withHoldingWriteOffVos.stream().collect(Collectors.groupingBy(e -> e.getBusinessCode()));
    }

    private void validateCommon(List<WithHoldingWriteOffDto> dtoList) {
        Validate.notEmpty(dtoList, "请求对象不能为空");
        dtoList.forEach(dto -> {
            Validate.notNull(dto, "请求对象不能为空");
            Validate.notBlank(dto.getActivitiesDetailCode(), "明细编码，不能为空");
            Validate.notBlank(dto.getWriteOffType(), "冲销类型，不能为空");
            Validate.notNull(dto.getWriteOffAmount(), "冲销金额，不能为空");
            WriteOffTypeEnum writeOffTypeEnum = WriteOffTypeEnum.findByCode(dto.getWriteOffType());
            Validate.notNull(writeOffTypeEnum, "冲销类型不正确");
        });
    }

    /**
     * 费用冲销 自动异步提交审批
     *
     * @param idList
     * @param crmUserIdentity
     */
    @Override
    @Async
    public void pushAutoAsync(List<String> idList, AbstractCrmUserIdentity crmUserIdentity) {
        if (CollectionUtil.isEmpty(idList)) {
            return;
        }
        loginUserService.refreshAuthentication(crmUserIdentity);
        this.push(idList);
    }

    /**
     * 提交审批
     *
     * @param idList
     * @return
     */
    @Override
    public Result<?> push(List<String> idList) {
        Assert.notEmpty(idList, "请选择数据!");
        List<WithHoldingWriteOff> holdingVoList = withHoldingWriteOffRepository.findByIds(idList);
        Assert.notEmpty(holdingVoList, "选择的数据不存在,请刷新页面重试!");
        holdingVoList.forEach(item -> {
            Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(item.getPushStatus()), "费用冲销[" + item.getWithHoldingCode() + "]已推送费控,不可重复推送");
        });
        return this.withHoldingSendHec(holdingVoList);
    }

    /**
     * 推送费控 费用冲销
     *
     * @param writeOffList
     * @return void
     * <AUTHOR>
     * @date 2024/7/9 17:04
     */
    private Result<String> withHoldingSendHec(List<WithHoldingWriteOff> writeOffList) {
        if (CollectionUtil.isEmpty(writeOffList)) {
            return Result.error("推送数据不能为空!");
        }
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findByCodes(writeOffList.stream().filter(k -> StringUtil.isNotEmpty(k.getWithHoldingCode())).map(WithHoldingWriteOff::getWithHoldingCode).distinct().collect(Collectors.toList()));
        Map<String, WithHoldingVo> withHoldingCodeMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(withHoldingVos)) {
            withHoldingCodeMap.putAll(withHoldingVos.stream().collect(Collectors.toMap(WithHoldingVo::getWithHoldingCode, v -> v, (n, o) -> n)));
        }
        List<WithHoldingWriteOff> writeOffSendList = Lists.newArrayList();
        List<String> writeOffNotNeedPushCodes = Lists.newArrayList();
        writeOffList.forEach(entity -> {
            if (Objects.isNull(entity.getWriteOffAmount())
                    || entity.getWriteOffAmount().compareTo(BigDecimal.ZERO) == 0) {
                writeOffNotNeedPushCodes.add(entity.getWriteOffCode());
            } else {
                writeOffSendList.add(entity);
            }

        });

        if (CollectionUtil.isNotEmpty(writeOffNotNeedPushCodes)) {
            withHoldingWriteOffRepository.updatePushStatusByCodes(writeOffNotNeedPushCodes, HecSendStatusEnum.NOT_NEED_PUSH);
        }
        if (CollectionUtil.isEmpty(writeOffSendList)) {
            Result<String> result = new Result<>();
            result.setMessage("推送费控成功!");
            return result;
        }
        StringBuffer errorMsg = new StringBuffer();
        writeOffSendList.forEach(writeOff -> {
            writeOff.setHecOperationType(HecOperationTypeEnum.CREATE.getCode());
            writeOff.setPushHecCode(withHoldingCodeMap.getOrDefault(writeOff.getWithHoldingCode(), new WithHoldingVo()).getPushHecCode());
            ExternalLogDetailDto logDetailDto = null;
            JSONObject jsonObject = this.buildWithHoldingSendData(writeOff);
            Assert.notNull(jsonObject, "未查询到数据,请刷新重新选择数据!");
            try {
                String token = costControlLoginService.getToken();
                UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
                Map<String, String> headMap = Maps.newHashMap();
                headMap.put(RyConstant.HEADER_AUTHORIZATION, token);
                String url = urlAddressVo.getUrl();
                String interfaceAddress = RyConstant.FK_FEE_PROVISION_INTERFACE_ADDRESS;
                JSONObject headJson = new JSONObject();
                headJson.put(RyConstant.HEADER_AUTHORIZATION, token);
                logDetailDto = ExternalLogUtil.buildLogSaveInfo(jsonObject.toJSONString(), urlAddressVo);
                logDetailDto.setReqHead(headJson.toJSONString());
                logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf(".") + 1));
                logDetailDto.setRequestUri(interfaceAddress);
                logDetailDto.setMethodMsg("费用冲销提交");
                externalLogVoService.addOrUpdateLog(logDetailDto, true);
                Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, jsonObject.toJSONString(), headMap);
                ExternalLogUtil.buildLogResult(logDetailDto, result);
                this.buildHecResultData(result, writeOff.getWriteOffCode());
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
            } catch (Exception e) {
                if (Objects.nonNull(logDetailDto)) {
                    logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                    logDetailDto.setTipMsg(e.getMessage());
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                }
                String failMsg = "提交费控异常:" + e.getMessage();
                withHoldingWriteOffRepository.updateErrorMsg(writeOff.getWriteOffCode(), failMsg);
                log.error(e.getMessage(), e);
                errorMsg.append("单据[");
                errorMsg.append(writeOff.getWriteOffCode());
                errorMsg.append("];");
                errorMsg.append(failMsg);
                errorMsg.append(";");
            }

        });
        Result<String> result = new Result<>();
        if (StringUtil.isNotEmpty(errorMsg.toString())) {
            result.error500(errorMsg.toString());
        }
        return result;

    }


    /**
     * 构建明细 推送费控
     *
     * @param writeOff
     * @return
     */
    private JSONObject buildWithHoldingSendData(WithHoldingWriteOff writeOff) {
        if (Objects.isNull(writeOff)) {
            return null;
        }
        List<String> fileUrlList = Lists.newArrayList();
        JSONObject jsonObject = new JSONObject();
        JSONObject headVo = this.buildWithHoldingHead(writeOff);
        JSONArray requisitionLines = this.buildLines(writeOff);
        headVo.put("fileUrls", fileUrlList);
        headVo.put("lines", requisitionLines);
        jsonObject.put("payload", JSONObject.toJSONString(headVo));
        return jsonObject;
    }

    /**
     * 构建推送明细  头部信息
     * 推送费控 费用冲销
     *
     * @param writeOff
     * @return
     */
    private JSONObject buildWithHoldingHead(WithHoldingWriteOff writeOff) {
        JSONObject headVo = new JSONObject();
        if (Objects.isNull(writeOff)) {
            return headVo;
        }
        Assert.hasLength(writeOff.getPositionCode(), "费用冲销[" + writeOff.getWriteOffCode() + "]职位[position_code]为空!");
        PositionVo positionVo = positionVoService.findByPositionCode(writeOff.getPositionCode());
        Assert.notNull(positionVo, "费用冲销[" + writeOff.getWriteOffCode() + "职位[" + writeOff.getPositionCode() + "]在主数据中不存在!");
        headVo.put("companyCode", positionVo.getSubCompanyId());
        headVo.put("employeeCode", writeOff.getAuditCreateAccount());
        headVo.put("accEntityCode", writeOff.getCompanyCode());
        headVo.put("unitCode", positionVo.getDepartmentId());
        headVo.put("sourceSystem", "TPM");
        headVo.put("sourceOrderNumber", writeOff.getWriteOffCode());
        headVo.put("sourceSystemOperation", writeOff.getHecOperationType());
        headVo.put("sourceOrderType", HecBusinessTypeEnum.TPM_WITH_HOLDING_WRITE_OFF_HEC.getCode());
        headVo.put("operatorCode", positionVo.getUserName());
        headVo.put("operatedDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
        headVo.put("paymentCurrencyCode", RyConstant.CNY);
        headVo.put("description", writeOff.getRemark());
        headVo.put("requisitionDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));

        headVo.put("accrualReqTypeCode", "TPMYT004");
        headVo.put("creationMethod", "MANUAL");
        //headVo.put("sourceOrderUrl", domainName + TpmOaPageEnum.WITH_HOLDING_TOTAL_APPROVEFORM.getUrlCode() + "?code=" + writeOff.getWriteOffCode());
        return headVo;
    }

    /**
     * 构建推送明细  行信息
     * 推送费控 费用冲销
     *
     * @param writeOff
     * @return
     */
    private JSONArray buildLines(WithHoldingWriteOff writeOff) {
        JSONArray jsonArray = new JSONArray();
        if (Objects.isNull(writeOff)) {
            return jsonArray;
        }
        List<String> orgCodeList = Collections.singletonList(writeOff.getBearDepartmentCode());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(orgCodeList);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("description", writeOff.getRemark());
        Assert.hasLength(writeOff.getWithHoldingType(), "费用冲销[" + writeOff.getWithHoldingCode() + "]" + "预提类型为空!");
        WithHoldingTypeEnum withHoldingTypeEnum = WithHoldingTypeEnum.findByCode(writeOff.getWithHoldingType());
        Assert.notNull(withHoldingTypeEnum, "费用冲销[" + writeOff.getWithHoldingCode() + "]" + "预提类型[" + writeOff.getWithHoldingType() + "]不合法!");
        //【费用冲销】1、预提类型为“已申请未结案”或“已结案未兑付”时传活动开始时间所属年月 2、其余取预提入账年月
        //【费用冲销】传对应费用冲销的费用归属期间
        jsonObject.put("periodName", writeOff.getYears());
        //【费用冲销】：预提入账年月
        //【费用冲销】：冲销年月
        jsonObject.put("settlementName", writeOff.getWriteOffYears());
        jsonObject.put("businessItemCode", writeOff.getCostTypeCategoryCode());
        jsonObject.put("dimension3Code", writeOff.getItemCode());
        jsonObject.put("dimension5Code", writeOff.getErpCode());
        List<OrgOaOrgVo> oaOrgVoList = orgOaOrgVoMap.get(writeOff.getBearDepartmentCode());
        Assert.notEmpty(oaOrgVoList, "费用冲销[" + writeOff.getWriteOffCode() + "]承担部门[" + writeOff.getBearDepartmentCode() + "]未找到OA组织!");
        jsonObject.put("unitCode", oaOrgVoList.get(0).getOaOrgCode());
        jsonObject.put("respCenterCode", writeOff.getCostCenterCode());
        jsonObject.put("businessAmount", writeOff.getWriteOffAmount());
        //传兑付方式（电汇：CASH；账扣：ACCOUNT；票扣：INVOICE；货补：ORDER）
        CashMethodEnum cashMethodEnum = CashMethodEnum.findByCode(writeOff.getPayBy());
        Assert.notNull(cashMethodEnum, "费用计提[" + writeOff.getWriteOffCode() + "]兑付方式[" + writeOff.getPayBy() + "]不合法!");
        jsonObject.put("paymentMethod", cashMethodEnum.getHecCode());
        jsonObject.put("sapFlag", "Y");
        jsonObject.put("autoReverseFlag", "Y");
        //当期费用时传Y，往期费用时传N
        jsonObject.put("currentFlag", writeOff.getBeThisFee());
        jsonObject.put("paymentCurrencyCode", RyConstant.CNY);
        jsonObject.put("functionalCurrencyCode", RyConstant.CNY);
        jsonObject.put("businessCurrencyCode", RyConstant.CNY);
        jsonObject.put("sourceOrderLineNumber", writeOff.getWriteOffCode());
        //【费用计提】不传
        //【费用冲销】费用冲销对应费用冲销的“推送费控单号”
        jsonObject.put("sourceAccrualReqHdNumber", writeOff.getPushHecCode());
        //【费用计提】不传
        //【费用冲销】费用冲销对应费用冲销的计提编码
        jsonObject.put("sourceAccrualReqLnNumber", writeOff.getWithHoldingCode());
        jsonArray.add(jsonObject);
        return jsonArray;
    }

    /**
     * 费用返回数据
     *
     * @param result
     */
    private void buildHecResultData(Result<String> result, String writeOffCode) {
        Assert.hasLength(result.getResult(), "费控返回信息为空1!");
        JSONObject jsonObject = JSONObject.parseObject(result.getResult());
        Assert.notNull(jsonObject, "费控返回信息为空2!");
        Assert.isTrue(jsonObject.containsKey("status"), "费控返回信息缺少[status]字段!");
        Assert.isTrue(jsonObject.containsKey("message"), "费控返回信息缺少[message]字段!");
        Assert.isTrue(String.valueOf(com.biz.crm.business.common.sdk.constant.CommonConstant.SC_OK_200).equals(jsonObject.getString("status")), "费控返回信息:" + jsonObject.get("message"));
        Assert.isTrue(jsonObject.containsKey("payload"), "费控返回信息缺少[payload]对象!");
        JSONObject payload = jsonObject.getJSONObject("payload");
        Assert.notNull(payload, "费控返回信息[payload]对象为空");
        Assert.isTrue(payload.containsKey("status"), "费控返回信息缺少[payload]对象缺少[status]字段!");
        Assert.isTrue(payload.containsKey("message"), "费控返回信息缺少[payload]对象缺少[message]字段!");
        Assert.isTrue(ExternalLogGlobalConstants.S.equals(payload.getString("status")), "费控返回信息:" + payload.get("message"));
        Assert.isTrue(payload.containsKey("result"), "费控返回信息缺少[payload]对象缺少[result]字段!");

        JSONArray resultJson = payload.getJSONArray("result");
        Assert.notNull(resultJson, "费控返回信息[result]对象为空1");
        Assert.notEmpty(resultJson, "费控返回信息[result]对象为空2");
        String hecReceiptNumber = resultJson.getJSONObject(0).getString("hecReceiptNumber");
        String hecReceiptUrl = resultJson.getJSONObject(0).getString("hecReceiptUrl");
        Assert.hasLength(hecReceiptNumber, "费控返回信息[result]内[hecReceiptNumber]为空");
        //Assert.hasLength(hecReceiptUrl, "费控返回信息[result]内[hecReceiptUrl]为空");
        withHoldingWriteOffRepository.updateHecInfo(writeOffCode, hecReceiptNumber, hecReceiptUrl);

    }

}
