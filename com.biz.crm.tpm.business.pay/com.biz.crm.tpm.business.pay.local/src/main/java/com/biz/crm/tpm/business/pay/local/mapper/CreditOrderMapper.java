package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.CreditOrder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 贷项订单(CreditOrder)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-07-16 16:18:25
 */
public interface CreditOrderMapper extends BaseMapper<CreditOrder> {

    List<CreditOrderVo> findTotalByConditions(@Param("dto") CreditOrderVo dto);
}

