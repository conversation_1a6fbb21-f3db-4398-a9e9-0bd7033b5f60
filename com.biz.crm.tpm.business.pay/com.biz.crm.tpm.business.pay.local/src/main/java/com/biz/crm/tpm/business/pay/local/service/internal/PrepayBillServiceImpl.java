package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.tpm.business.pay.local.dto.PrepayBillRecordDto;
import com.biz.crm.tpm.business.pay.local.service.PrepayBillRecordService;
import com.biz.crm.tpm.business.pay.local.service.PrepayDetailService;
import com.biz.crm.tpm.business.pay.local.entity.PrepayBill;
import com.biz.crm.tpm.business.pay.local.repository.PrepayBillRepository;
import com.biz.crm.tpm.business.pay.local.service.PrepayBillService;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayBillDto;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayBillVo;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.biz.crm.tpm.business.pay.sdk.constant.PayConstant.PREPAY_LOCK_PREFIX_FORMAT;

/**
 * 活动预付账单;(tpm_prepay_bill)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Service("prepayBillService")
public class PrepayBillServiceImpl implements PrepayBillService {
  @Autowired
  private PrepayBillRepository prepayBillRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private RedisMutexService redisMutexService;
  @Autowired
  private PrepayBillRecordService prepayBillRecordService;
  @Autowired
  private PrepayDetailService prepayDetailService;


  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public PrepayBillVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    PrepayBill prepayBill = this.prepayBillRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (prepayBill == null) {
      return null;
    }
    PrepayBillVo prepayBillVo = this.nebulaToolkitService.copyObjectByWhiteList(prepayBill, PrepayBillVo.class, LinkedHashSet.class, ArrayList.class);
    return prepayBillVo;
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCode
   * @return 单条数据
   */
  @Override
  public PrepayBillVo findByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return null;
    }
    PrepayBill prepayBill = this.prepayBillRepository.findByActivitiesDetailCode(activitiesDetailCode);
    if (prepayBill == null) {
      return null;
    }
    PrepayBillVo prepayBillVo = this.nebulaToolkitService.copyObjectByWhiteList(prepayBill, PrepayBillVo.class, LinkedHashSet.class, ArrayList.class);
    return prepayBillVo;
  }

  @Override
  @Transactional
  public PrepayBillVo init(PrepayBillDto prepayBillDto) {
    Validate.notNull(prepayBillDto.getApplyAmount(), "初始化数据,申请金额不能为空!");
    Validate.notNull(prepayBillDto.getActivitiesDetailCode(), "初始化数据,活动明细编号不能为空!");
    PrepayBillVo prepayBillVo = this.findByActivitiesDetailCode(prepayBillDto.getActivitiesDetailCode());
    if (prepayBillVo != null) {
      return prepayBillVo;
    }
    prepayBillDto.setId(null);
    prepayBillDto.setTenantCode(TenantUtils.getTenantCode());
    prepayBillDto.setPrepaidAmount(BigDecimal.ZERO);
    return this.create(prepayBillDto);
  }

  /**
   * 新增数据
   *
   * @param prepayBillDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public PrepayBillVo create(PrepayBillDto prepayBillDto) {
    this.createValidate(prepayBillDto);
    PrepayBill prepayBill = this.nebulaToolkitService.copyObjectByWhiteList(prepayBillDto, PrepayBill.class, LinkedHashSet.class, ArrayList.class);
    prepayBill.setTenantCode(TenantUtils.getTenantCode());
    this.prepayBillRepository.saveOrUpdate(prepayBill);
    PrepayBillVo prepayBillVo = this.nebulaToolkitService.copyObjectByWhiteList(prepayBill, PrepayBillVo.class, LinkedHashSet.class, ArrayList.class);

    prepayBillVo.setId(prepayBill.getId());
    return prepayBillVo;
  }

  @Override
  @Transactional
  public void prepayAmountByPrepayCode(String prepayCode) {
    List<PrepayDetailVo> prepayDetailVos = this.prepayDetailService.findByPrepayCode(prepayCode);
    for (PrepayDetailVo prepayDetailVo : prepayDetailVos) {
      this.prepayAmountByActivityDetailCode(prepayDetailVo.getActivitiesDetailCode(), prepayDetailVo.getPrepayDetailCode(), prepayDetailVo.getPrepayAmount());
    }
  }

  @Override
  @Transactional
  public void backPrepayAmountByPrepayCode(String prepayCode) {
    List<PrepayDetailVo> prepayDetailVos = this.prepayDetailService.findByPrepayCode(prepayCode);
    for (PrepayDetailVo prepayDetailVo : prepayDetailVos) {
      this.backPrepayAmountByActivityDetailCode(prepayDetailVo.getActivitiesDetailCode(), prepayDetailVo.getPrepayDetailCode(), prepayDetailVo.getPrepayAmount());
    }
  }

  @Override
  @Transactional
  public void prepayAmountByActivityDetailCode(String activitiesDetailCode, String auditDetailCode, BigDecimal amount) {
    Validate.notBlank(activitiesDetailCode, "更改已核销金额，活动明细编号不能为空，请检查！");
    String tenantCode = TenantUtils.getTenantCode();
    String lockKey = String.format(PREPAY_LOCK_PREFIX_FORMAT, tenantCode, activitiesDetailCode);
    boolean isLock = false;
    try {
      isLock = this.redisMutexService.tryLock(lockKey, TimeUnit.SECONDS, 10);
      Validate.isTrue(isLock, "系统网络繁忙，请稍后重试");
      this.prepayBillRepository.addPreparedAmount(activitiesDetailCode, amount);
      PrepayBillRecordDto prepayBillRecordDto = new PrepayBillRecordDto();
      prepayBillRecordDto.setTenantCode(TenantUtils.getTenantCode());
      prepayBillRecordDto.setActivitiesDetailCode(activitiesDetailCode);
      prepayBillRecordDto.setBusinessCode(auditDetailCode);
      prepayBillRecordDto.setChangeAmount(amount);
      prepayBillRecordDto.setType(1);
      this.prepayBillRecordService.create(prepayBillRecordDto);
    } finally {
      if (isLock) {
        this.redisMutexService.unlock(lockKey);
      }
    }
  }

  @Override
  @Transactional
  public void backPrepayAmountByActivityDetailCode(String activitiesDetailCode, String auditDetailCode, BigDecimal amount) {
    Validate.notBlank(activitiesDetailCode, "更改已核销金额，活动明细编号不能为空，请检查！");
    String tenantCode = TenantUtils.getTenantCode();
    String lockKey = String.format(PREPAY_LOCK_PREFIX_FORMAT, tenantCode, activitiesDetailCode);
    boolean isLock = false;
    try {
      isLock = this.redisMutexService.tryLock(lockKey, TimeUnit.SECONDS, 10);
      Validate.isTrue(isLock,"系统繁忙，请稍后重试");
      this.prepayBillRepository.reducePreparedAmount(activitiesDetailCode, amount);
      PrepayBillRecordDto prepayBillRecordDto = new PrepayBillRecordDto();
      prepayBillRecordDto.setTenantCode(TenantUtils.getTenantCode());
      prepayBillRecordDto.setActivitiesDetailCode(activitiesDetailCode);
      prepayBillRecordDto.setBusinessCode(auditDetailCode);
      prepayBillRecordDto.setChangeAmount(amount);
      prepayBillRecordDto.setType(2);
      this.prepayBillRecordService.create(prepayBillRecordDto);
    } finally {
      if (isLock) {
        this.redisMutexService.unlock(lockKey);
      }
    }
  }

  /**
   * 创建验证
   *
   * @param prepayBillDto
   */
  private void createValidate(PrepayBillDto prepayBillDto) {
    Validate.notNull(prepayBillDto, "新增时，对象信息不能为空！");
    Validate.isTrue(prepayBillDto.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notNull(prepayBillDto.getApplyAmount(), "新增数据时，申请金额不能为空！");
    Validate.notNull(prepayBillDto.getPrepaidAmount(), "新增数据时，已预付金额不能为空！");
  }

}