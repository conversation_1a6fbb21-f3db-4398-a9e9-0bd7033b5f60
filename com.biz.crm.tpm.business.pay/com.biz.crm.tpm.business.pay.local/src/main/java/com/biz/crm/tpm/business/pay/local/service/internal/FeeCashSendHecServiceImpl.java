package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.service.CostControlLoginService;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.supplier.sdk.service.MdmSupplierVoService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord;
import com.biz.crm.tpm.business.pay.local.entity.FeeCash;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashDetail;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashInvoice;
import com.biz.crm.tpm.business.pay.local.repository.*;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPayeeDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.FeeCashLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.enums.*;
import com.biz.crm.tpm.business.pay.sdk.event.log.FeeCashLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.*;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 费用兑付推送费控
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/10 22:33
 */
@Service
@Slf4j
@RefreshScope
public class FeeCashSendHecServiceImpl implements FeeCashSendHecService {

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private FeeCashRepository feeCashRepository;

    @Autowired(required = false)
    private FeeCashService feeCashService;

    @Autowired(required = false)
    private FeeCashDetailRepository feeCashDetailRepository;

    @Autowired(required = false)
    private FeeCashFilesRepository feeCashFilesRepository;

    @Autowired(required = false)
    private FeeCashPayeeRepository feeCashPayeeRepository;

    @Autowired(required = false)
    private FeeCashInvoiceRepository feeCashInvoiceRepository;

    @Autowired(required = false)
    private FeeCashTicketRepository feeCashTicketRepository;

    @Autowired(required = false)
    private FeeCashTocRepository feeCashTocRepository;

    @Autowired(required = false)
    private FeeCashPrepayRepository feeCashPrepayRepository;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired(required = false)
    private MdmSupplierVoService mdmSupplierVoService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private CostControlLoginService costControlLoginService;

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;

    @Autowired(required = false)
    private BudgetSubjectsVoService budgetSubjectsVoService;

    @Autowired(required = false)
    private FeeCashOaService feeCashOaService;

    @Autowired(required = false)
    private FeeCashCloseOaService feeCashCloseOaService;

    @Autowired(required = false)
    private FeeCashMaterialOaService feeCashMaterialOaService;

    @Autowired(required = false)
    private InvoiceService invoiceService;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;

    @Value("${domain-name:}")
    private String domainName;
    @Value("${domain-name-file:}")
    private String domainNameFile;
    @Autowired(required = false)
    private InvoiceDetailRepository invoiceDetailRepository;

    @Autowired(required = false)
    private FeeCashTocService feeCashTocService;
    @Autowired(required = false)
    private FeeCashDetailService feeCashDetailService;
    @Autowired(required = false)
    private FeeCashPrepayService feeCashPrepayService;
    @Autowired(required = false)
    private FeeCashInvoiceService feeCashInvoiceService;
    @Autowired(required = false)
    private FeeCashMaterialService feeCashMaterialService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private CostTypeDetailVoService costTypeDetailVoService;
    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;
    @Autowired(required = false)
    private RedisLockService redisLockService;
    @Autowired(required = false)
    private ActivityPrepayDetailRecordService activityPrepayDetailRecordService;
    @Autowired(required = false)
    private ActivityPrepayDetailRecordRepository activityPrepayDetailRecordRepository;

    /**
     * 提交审批
     *
     * @param idList
     * @return com.biz.crm.business.common.sdk.model.Result<java.lang.String>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/15 12:57
     */
    @Override
    public List<String> submitBeforeGetCodes(List<String> idList) {
        Assert.notEmpty(idList, "提交时，主键集合不能为空！");
        List<FeeCash> feeCashes = this.feeCashRepository.findByIds(idList);
        Validate.isTrue(!CollectionUtils.isEmpty(feeCashes), "未找到数据,请刷新重试!");
        feeCashes.forEach(item -> Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(item.getBeStaging()),
                "暂存的数据不能提交"));
        List<String> codes = feeCashes.stream().map(FeeCash::getCashCode).collect(Collectors.toList());
        return codes;

    }

    /**
     * @param codes
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> valAndSendList(List<String> codes, boolean beBank) {
        List<FeeCash> feeCashes = this.feeCashRepository.findByCodes(codes);
        if (CollectionUtil.isEmpty(codes)) {
            return Result.error("未找到数据,请刷新重试!");
        }
        feeCashes.forEach(item -> Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.PASS.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】、【审批通过】核销数据能提交"));


        //票扣额外校验
        List<FeeCash> feeCasheTicket = feeCashes.stream().filter(e ->
                CashTypeEnum.FEE.getDictCode().equals(e.getCashType()) && CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(e.getCashMethod())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(feeCasheTicket)) {
            feeCasheTicket.forEach(this::submitValidate);
        }

        //提交公有校验
        if (!beBank) {
            feeCashes.forEach(this::submitValidateCommon);
        }

        //票扣额外校验
        List<FeeCash> feeCashWireAndAccount = feeCashes.stream().filter(e ->
                CashTypeEnum.WIRE.getDictCode().equals(e.getCashType()) || CashTypeEnum.ACCOUNT.getDictCode().equals(e.getCashType())).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(feeCashWireAndAccount)) {
            feeCashWireAndAccount.forEach(this::submitValidateWireAndAccount);
        }

        //放开限制  很容易出问题  2024年7月10日22:27:36
        //当 提兑付类型=费用兑付 兑付方式=电汇/账扣 时 推送费控,其余推送OA
        long valDataCashType = feeCashes.stream().map(FeeCash::getCashType).distinct().count();
        Validate.isTrue(valDataCashType <= 1, "提交时，单次只能一种兑付类型！");
        long valDataCashMethod = feeCashes.stream().map(FeeCash::getCashMethod).distinct().count();
        Validate.isTrue(valDataCashMethod <= 1, "提交时，单次只能一种兑付方式！");
        Result<String> result = new Result<>();
        //提兑付类型=费用兑付 兑付方式=电汇/账扣 时 推送费控,
        List<FeeCash> feeSendHecList = feeCashes.stream()
                .filter(k -> CashTypeEnum.FEE.getDictCode().equals(k.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(k.getCashType()))
                .filter(k -> CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(k.getCashMethod())
                        || CashMethodEnum.DEDUCTIONS.getDictCode().equals(k.getCashMethod()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(feeSendHecList)) {
            List<FeeCashDto> sendHecDtoList = (List<FeeCashDto>) nebulaToolkitService.copyCollectionByWhiteList(feeSendHecList,
                    FeeCash.class, FeeCashDto.class, HashSet.class, ArrayList.class);
            sendHecDtoList.forEach(dto -> {
                if (ProcessStatusEnum.PREPARE.getDictCode().equals(dto.getStatus())) {
                    dto.setHecOperationType(HecOperationTypeEnum.CREATE.getCode());
                } else if (ProcessStatusEnum.PASS.getDictCode().equals(dto.getStatus())) {
                    dto.setHecOperationType(HecOperationTypeEnum.RESUBMIT.getCode());
                } else {
                    dto.setHecOperationType(HecOperationTypeEnum.UPDATE.getCode());
                }
            });
            result = this.feeSendHec(sendHecDtoList);
            if (null != result && result.isSuccess()) {
                feeSendHecList.forEach(dto -> {
                    if (dto.getCashType().equals(CashTypeEnum.WIREDUIFU.getDictCode())) {
                        List<FeeCashPrepayDto> prepayList = feeCashPrepayRepository.findByCodeTwo(dto.getCashCode());
                        if (!CollectionUtils.isEmpty(prepayList)) {
                            activityPrepayDetailRecordService.koujianPrepayCarreAmount(prepayList, 1);
                        }
                    }
                });
            }
        }

        //推送费控 兑付类型=账扣预付/电汇预付
        List<FeeCash> wireAndAccountHecList = feeCashes.stream()
                .filter(k -> CashTypeEnum.WIRE.getDictCode().equals(k.getCashType())
                        || CashTypeEnum.ACCOUNT.getDictCode().equals(k.getCashType()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(wireAndAccountHecList)) {
            List<FeeCashDto> sendHecDtoList = (List<FeeCashDto>) nebulaToolkitService.copyCollectionByWhiteList(wireAndAccountHecList,
                    FeeCash.class, FeeCashDto.class, HashSet.class, ArrayList.class);
            sendHecDtoList.forEach(dto -> {
                if (ProcessStatusEnum.PREPARE.getDictCode().equals(dto.getStatus())) {
                    dto.setHecOperationType(HecOperationTypeEnum.CREATE.getCode());
                } else if (ProcessStatusEnum.PASS.getDictCode().equals(dto.getStatus())) {
                    dto.setHecOperationType(HecOperationTypeEnum.RESUBMIT.getCode());
                } else {
                    dto.setHecOperationType(HecOperationTypeEnum.UPDATE.getCode());
                }
            });
            result = this.wireAndAccountSendHec(sendHecDtoList);
        }

        List<FeeCash> sendOaList = feeCashes.stream()
                .filter(k -> CashTypeEnum.FEE.getDictCode().equals(k.getCashType()))
                .filter(k -> CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(k.getCashMethod()))
                .collect(Collectors.toList());
        List<FeeCash> sendOaCloseList = feeCashes.stream()
                .filter(k -> CashTypeEnum.CLOSE.getDictCode().equals(k.getCashType()))
                .collect(Collectors.toList());
        List<FeeCash> sendOaMaterialList = feeCashes.stream()
                .filter(k -> CashTypeEnum.MATERIAL.getDictCode().equals(k.getCashType()))
                .collect(Collectors.toList());

        //OA接口
        if (CollectionUtil.isNotEmpty(sendOaList)) {
            sendOaList.forEach(e -> {
                if (ProcessStatusEnum.PREPARE.getDictCode().equals(e.getStatus())) {
                    feeCashOaService.pushOa(e.getCashCode());
                } else {
                    feeCashOaService.resubmitOa(e.getCashCode());
                }
            });
        }
        if (CollectionUtil.isNotEmpty(sendOaCloseList)) {
            sendOaCloseList.forEach(e -> {
                if (ProcessStatusEnum.PREPARE.getDictCode().equals(e.getStatus())) {
                    feeCashCloseOaService.pushOa(e.getCashCode());
                } else {
                    feeCashCloseOaService.resubmitOa(e.getCashCode());
                }
            });
        }
        if (CollectionUtil.isNotEmpty(sendOaMaterialList)) {
            sendOaMaterialList.forEach(e -> {
                if (ProcessStatusEnum.PREPARE.getDictCode().equals(e.getStatus())) {
                    feeCashMaterialOaService.pushOa(e.getCashCode());
                } else {
                    feeCashMaterialOaService.resubmitOa(e.getCashCode());
                }
            });
        }
        return result;
    }

    private void submitValidateWireAndAccount(FeeCash feeCash) {
        List<FeeCashDetailVo> detailVos = feeCashDetailRepository.findByCode(feeCash.getCashCode());
        Validate.isTrue(!CollectionUtils.isEmpty(detailVos), "预付明细不能为空");
        //结案明细编码
        List<String> auditDetailCodes = detailVos.stream().map(x -> x.getAuditDetailCode()).distinct().collect(Collectors.toList());
        //查询费用兑付的结案金额+加上了还未通过的预付 不管是任何状态 除了删除的
        List<FeeCashDetailVo> cashDetailList = feeCashRepository.findFeeCashAuditDetailListByAuditDetailCodes(auditDetailCodes, feeCash.getCashCode());
        Map<String, BigDecimal> cashDetailMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(cashDetailList)) {
            cashDetailMap = cashDetailList.stream().collect(Collectors.toMap(x -> x.getAuditDetailCode(), l -> l.getThisCashAmount()));
        }
        //查询预付金额
        List<ActivityPrepayDetailRecord> prepayDetails = activityPrepayDetailRecordRepository.findByAuditDetailCodes(auditDetailCodes);
        Map<String, BigDecimal> prepayDetailMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(prepayDetails)) {
            prepayDetailMap = prepayDetails.stream().collect(Collectors.groupingBy(x -> x.getAuditDetailCode(), Collectors.mapping(x -> x.getAvailableReversedAmount(),
                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
//        //查询预付金额 排除本次的预付金额
//        List<FeeCashDetailVo> prepayAuditDetailList = feeCashRepository.findPrepayListByAuditDetailCodes(auditDetailCodes, feeCash.getCashCode());
//        Map<String, BigDecimal> prepayAuditDetailMap = Maps.newHashMap();
//        if (!CollectionUtils.isEmpty(prepayAuditDetailList)) {
//            prepayAuditDetailMap = prepayAuditDetailList.stream().collect(Collectors.toMap(x -> x.getAuditDetailCode(), l -> l.getThisCashAmount()));
//        }
        for (FeeCashDetailVo e : detailVos) {
            Validate.notNull(e.getThisCashAmount(), "本次预付金额，不能为空！");
            BigDecimal thisCashAmount = e.getThisCashAmount();
            BigDecimal cashAmount = cashDetailMap.getOrDefault(e.getAuditDetailCode(), BigDecimal.ZERO);
            BigDecimal availableReversedAmount = prepayDetailMap.getOrDefault(e.getAuditDetailCode(), BigDecimal.ZERO);
            BigDecimal totalAmount = thisCashAmount.add(cashAmount).add(availableReversedAmount);
            Validate.isTrue(e.getAuditAmount().compareTo(totalAmount) > -1, "本次预付金额应小于或等于（结案金额-已兑付金额-预付可冲销金额）");
        }


    }

    /**
     * 提交校验
     *
     * @param feeCash
     */
    private void submitValidate(FeeCash feeCash) {
        List<FeeCashTicketVo> ticketVos = feeCashTicketRepository.findByCode(feeCash.getCashCode());
        List<FeeCashDetailVo> detailVos = feeCashDetailRepository.findByCode(feeCash.getCashCode());
        Validate.notEmpty(detailVos, "兑付明细，不能为空！");
        detailVos.forEach(e -> {
            Validate.notNull(e.getThisCashAmount(), "本次兑付金额，不能为空！");
            Validate.notBlank(e.getBeCash(), "是否完全兑付，不能为空！");
            Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(e.getBeCash()), "是否完全兑付，必须为否！");
        });
        BigDecimal amountT = ticketVos.stream().map(FeeCashTicketVo::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal amountD = detailVos.stream().map(FeeCashDetailVo::getThisCashAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        Validate.isTrue(amountD.compareTo(amountT) == 0, "票扣明细金额之和必须等于本次兑付金额之和");
        checkAndSetFeeCashBudgetSubjectCode(detailVos, ticketVos);
    }

    /**
     * 提交公有校验
     *
     * @param feeCash
     */
    private void submitValidateCommon(FeeCash feeCash) {
        List<FeeCashDetailVo> detailVos = feeCashDetailRepository.findByCode(feeCash.getCashCode());
        if (!CollectionUtils.isEmpty(detailVos)) {
            List<String> auditDetailCodes = detailVos.stream().map(FeeCashDetailVo::getAuditDetailCode).collect(Collectors.toList());
            List<String> cashDetailCodes = detailVos.stream().map(FeeCashDetailVo::getCashDetailCode).collect(Collectors.toList());

            List<FeeCashDetailVo> auditCashList = feeCashDetailRepository.findAuditCashAmount(auditDetailCodes);
            Map<String, FeeCashDetailVo> auditCashMap = auditCashList.stream().collect(Collectors.toMap(x -> x.getAuditDetailCode(), Function.identity()));

            //取消费用兑付、账扣预付、电汇预付不允许针对同一结案明细同时发起兑付申请的控制，并改为在走费用关闭流程和已关闭的结案明细不允许发起费用兑付、账扣预付、电汇预付
            List<FeeCashDetail> cashDetails = feeCash.getCashType().equals(CashTypeEnum.CLOSE.getDictCode()) ? feeCashDetailRepository.findByAuditDetailCodesBeCommit(auditDetailCodes) : feeCashDetailRepository.findByAuditDetailCodesBeClose(auditDetailCodes);
            if (CollectionUtil.isNotEmpty(cashDetails)) {
                //排除自己
                List<FeeCashDetail> filterCashDetails = cashDetails.stream().filter(e -> StringUtils.isBlank(feeCash.getCashCode()) || !feeCash.getCashCode().equals(e.getCashCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(filterCashDetails)) {
                    Set<String> cashCodes = filterCashDetails.stream().map(e -> e.getCashCode()).collect(Collectors.toSet());
                    Set<String> filterAuditDetailCodes = filterCashDetails.stream().map(e -> e.getAuditDetailCode()).collect(Collectors.toSet());
                    Validate.isTrue(false, "结案明细【%s】已在兑付单【%s】中存在，不允许提交", String.join(",", filterAuditDetailCodes), String.join(",", cashCodes));
                }
            }

            auditCashMap.entrySet().forEach(l -> {
                FeeCashDetailVo vo = l.getValue();
                Validate.isTrue(vo.getAuditAmount().compareTo(vo.getThisCashAmount()) > -1, "结案明细【%s】本次兑付金额合计（含其它在途单据）+已兑付金额＞结案金额，不允许提交！", l.getKey());
            });
//            Map<String, MarketingAuditDetailVo> auditDetailMap = auditDetailVoPage.getRecords().stream().collect(Collectors.toMap(e -> e.getAuditDetailCode(), Function.identity(), (a, b) -> a));

            Set<String> detailCodes = detailVos.stream().map(FeeCashDetailVo::getDetailCode).collect(Collectors.toSet());
            Map<String, Integer> costTypeDetailMap = costTypeDetailVoService.findByCodes(new ArrayList<>(detailCodes)).stream().collect(Collectors.toMap(e -> e.getDetailCode(), e -> e.getPayValidityTime()));
            //票扣、账扣、电汇兑付、物料采购的提交需要校验“本次兑付金额”不能大于“剩余可兑付金额”
            boolean beFee = CashTypeEnum.FEE.getDictCode().equals(feeCash.getCashType()) || CashTypeEnum.MATERIAL.getDictCode().equals(feeCash.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(feeCash.getCashType());
            boolean beAccount = CashTypeEnum.ACCOUNT.getDictCode().equals(feeCash.getCashType());
            detailVos.forEach(e -> {
//                BigDecimal availableCashAmount = Optional.ofNullable(auditDetailMap.get(e.getAuditDetailCode()).getAvailableCashPrepayAmount()).orElse(BigDecimal.ZERO).add(e.getThisCashAmount());
//                int compareTo = availableCashAmount.compareTo(e.getThisCashAmount());
//                if (compareTo < 0) {
//                    Validate.isTrue(!beFee, "“本次兑付金额”不能大于“剩余可兑付金额”");
//                }

                //账扣预付，提交时才校验预付描述
                if (beAccount) {
                    Validate.notBlank(e.getRemark(), "预付描述，不能为空！");
                }

                //校验是否超“兑付有效期”
                if (costTypeDetailMap.containsKey(e.getDetailCode()) && StringUtils.isNotBlank(e.getAuditDate()) && !CashTypeEnum.CLOSE.getDictCode().equals(feeCash.getCashType())) {
                    long betweenDay = cn.hutool.core.date.DateUtil.betweenDay(new Date(), DateUtil.parseDate(e.getAuditDate(), "yyyy-MM-dd"), true);
                    Validate.isTrue(betweenDay < costTypeDetailMap.get(e.getDetailCode()), "兑付明细【%s】超“兑付有效期”，不允许兑付！", e.getCashDetailCode());
                }
            });
        }
        if (CashTypeEnum.ACCOUNT.getDictCode().equals(feeCash.getCashType())) {
            List<FeeCashTicketVo> ticketVos = feeCashTicketRepository.findByCode(feeCash.getCashCode());
            BigDecimal amountT = ticketVos.stream().map(FeeCashTicketVo::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal amountD = detailVos.stream().map(FeeCashDetailVo::getThisCashAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            Validate.isTrue(amountD.compareTo(amountT) == 0, "账扣明细金额之和必须等于本次预付金额之和");
            this.checkAndSetFeeCashBudgetSubjectCode(detailVos, ticketVos);
        }
    }

    private void checkAndSetFeeCashBudgetSubjectCode(List<FeeCashDetailVo> detailVos, List<FeeCashTicketVo> ticketVos) {
        Validate.notEmpty(detailVos, "兑付明细，不能为空！");
        Validate.notEmpty(ticketVos, "账扣明细，不能为空！");
        List<String> subjectList = new ArrayList<>();
        ticketVos.forEach(e -> {
            Assert.hasLength(e.getBudgetSubjectsCode(), "预算科目，不能为空！");
            subjectList.add(e.getBudgetSubjectsCode());
        });
        Set<String> cashBudgetSubjectSets = detailVos.stream().map(FeeCashDetailVo::getBudgetSubjectCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        List<BudgetSubjectsVo> cashBudgetSubjectsVos = budgetSubjectsVoService.findByCodes(cashBudgetSubjectSets);
        Assert.notEmpty(cashBudgetSubjectsVos, "兑付明细的预算科目不存在！");
        List<String> cashLevelList = cashBudgetSubjectsVos.stream().map(BudgetSubjectsVo::getLevel).distinct().collect(Collectors.toList());
        Validate.isTrue(cashLevelList.size() == 1, "兑付明细的“预算科目”层级不一致，请调整兑付明细的预算科目！");
        //校验预算科目层级是否一致
        List<String> ticketLevelList = budgetSubjectsVoService.findByCodes(new HashSet<>(subjectList)).stream().map(BudgetSubjectsVo::getLevel).distinct().collect(Collectors.toList());
        Validate.isTrue(ticketLevelList.size() == 1, "账扣明细的“预算科目”层级不一致，请调整票扣明细的预算科目！");
        Integer ticketLevel = Integer.parseInt(ticketLevelList.get(0));
        Integer cashLevel = Integer.parseInt(cashLevelList.get(0));
        Validate.isTrue(ticketLevel <= cashLevel, "账扣明细的“预算科目”层级必须大于等于兑付明细“预算科目”层级！");

        if (!ticketLevel.equals(cashLevel)) {
            cashBudgetSubjectsVos.forEach(e -> {
                Validate.notEmpty(e.getParentBudgetSubjectsCode(),
                        "预算科目【%s】不存在上级，请调整！", e.getBudgetSubjectsCode());
            });
            Map<String, String> parentThreeSubjectVoMap = cashBudgetSubjectsVos.stream()
                    .collect(Collectors.toMap(BudgetSubjectsVo::getBudgetSubjectsCode, BudgetSubjectsVo::getParentBudgetSubjectsCode, (a, b) -> a));

            Set<String> parentTwoSubjectCodeSet = cashBudgetSubjectsVos.stream()
                    .map(BudgetSubjectsVo::getParentBudgetSubjectsCode).collect(Collectors.toSet());
            List<BudgetSubjectsVo> cashTwoBudgetSubjectsVos = budgetSubjectsVoService.findByCodes(parentTwoSubjectCodeSet);
            Validate.notEmpty(cashTwoBudgetSubjectsVos, "预算科目不存在上级，请调整！");
            Map<String, String> parentTwoSubjectVoMap = cashTwoBudgetSubjectsVos.stream()
                    .collect(Collectors.toMap(BudgetSubjectsVo::getBudgetSubjectsCode, BudgetSubjectsVo::getParentBudgetSubjectsCode, (a, b) -> a));
            parentTwoSubjectCodeSet.forEach(e -> {
                Validate.isTrue(parentTwoSubjectVoMap.containsKey(e), "预算科目【%s】不存在，请调整！", e);
            });
            List<String> oneCashLevelList = cashTwoBudgetSubjectsVos.stream()
                    .map(BudgetSubjectsVo::getLevel).distinct().collect(Collectors.toList());
            Assert.isTrue(oneCashLevelList.size() == 1, "兑付明细预算科目的上级层级不一致，请调整！");
            Integer oneCashLevel = Integer.parseInt(oneCashLevelList.get(0));
            if (oneCashLevel.equals(ticketLevel)) {
                detailVos.forEach(e -> {
                    e.setBudgetSubjectCodeVerify(parentThreeSubjectVoMap.get(e.getBudgetSubjectCode()));
                });
            } else {
                cashTwoBudgetSubjectsVos.forEach(e -> {
                    Validate.notEmpty(e.getParentBudgetSubjectsCode(),
                            "预算科目【%s】不存在上级，请调整！", e.getBudgetSubjectsCode());
                });
                Set<String> parentOneSubjectCodeSet = cashTwoBudgetSubjectsVos.stream()
                        .map(BudgetSubjectsVo::getParentBudgetSubjectsCode).collect(Collectors.toSet());
                List<BudgetSubjectsVo> cashOneBudgetSubjectsVos = budgetSubjectsVoService.findByCodes(parentOneSubjectCodeSet);
                Validate.notEmpty(cashOneBudgetSubjectsVos, "预算科目不存在上级，请调整！");
                Set<String> parentOneSubjectSet = cashOneBudgetSubjectsVos.stream()
                        .map(BudgetSubjectsVo::getBudgetSubjectsCode).collect(Collectors.toSet());
                parentOneSubjectCodeSet.forEach(e -> {
                    Validate.isTrue(parentOneSubjectSet.contains(e), "预算科目【%s】不存在，请调整！", e);
                });
                detailVos.forEach(e -> {
                    e.setBudgetSubjectCodeVerify(parentTwoSubjectVoMap.getOrDefault(
                            parentThreeSubjectVoMap.getOrDefault(e.getBudgetSubjectCode(), ""), ""));
                });
            }

        } else {
            detailVos.forEach(e -> {
                e.setBudgetSubjectCodeVerify(e.getBudgetSubjectCode());
            });
        }

        //校验预算科目+客户维度的票扣明细/账扣明细金额汇总与对应兑付的兑付明细的“本次兑付/预付金额”汇总是否相等，如果不相等，则报错提示
        Map<String, List<FeeCashDetailVo>> detailSubjectVos = detailVos.stream()
                .collect(Collectors.groupingBy(e -> e.getBudgetSubjectCodeVerify() + "+" + e.getCustomerCode()));
        detailSubjectVos.forEach((k, v) -> {
            BigDecimal detailAmount = v.stream().map(FeeCashDetailVo::getThisCashAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal ticketAmount = ticketVos.stream().filter(e -> k.contains(StringUtils.defaultIfBlank(e.getBudgetSubjectsCode(),
                            "") + "+" + StringUtils.defaultIfBlank(e.getCustomerCode(), "")))
                    .map(FeeCashTicketVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            ticketAmount = Optional.ofNullable(ticketAmount).orElse(BigDecimal.ZERO);
            Validate.isTrue(ticketAmount.compareTo(detailAmount) == 0,
                    "%s的票扣明细费用是%s，关联的结案费用是%s，两者金额不相等，请调整数据",
                    v.get(0).getBudgetSubjectName() + "+" + v.get(0).getCustomerName(), ticketAmount, detailAmount);
        });
    }

    /**
     * 新增保存并提交
     *
     * @param dto      实体对象
     * @param cacheKey
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> submitBeforeCreateGetCodes(FeeCashDto dto, String cacheKey, String cacheKeyToc,
                                                   String cacheKeyDetail, String cacheKeyPrepay, String cacheKeyInvoice, String cacheKeyMaterial) {
        List<String> codes = feeCashService.create(dto, false, cacheKey, cacheKeyToc, cacheKeyDetail, cacheKeyPrepay, cacheKeyInvoice, cacheKeyMaterial, true, false);
        return codes;
    }

    /**
     * 编辑保存并提交
     *
     * @param dto      实体对象
     * @param cacheKey
     * @return 修改结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> submitBeforeUpdateGetCode(FeeCashDto dto, String cacheKey, String cacheKeyToc,
                                                  String cacheKeyDetail, String cacheKeyPrepay, String cacheKeyInvoice, String cacheKeyMaterial) {
        return feeCashService.update(dto, cacheKey, cacheKeyToc, cacheKeyDetail, cacheKeyPrepay, cacheKeyInvoice, cacheKeyMaterial, true, false,false);
    }

    /**
     * 修改银行卡信息保存并提交
     *
     * @param dto      实体对象
     * @param cacheKey
     * @return 修改结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> submitUpdateBankInfo(FeeCashDto dto, String cacheKey, String cacheKeyToc) {
        feeCashService.updateBankInfo(dto, cacheKey, cacheKeyToc);
        FeeCash feeCashes = feeCashRepository.findByCode(dto.getCashCode());
        Result<String> result = this.valAndSendList(Collections.singletonList(feeCashes.getCashCode()), true);
        feeCashRepository.hecPayStatus(Collections.singleton(dto.getCashCode()));
        feeCashPayeeRepository.hecPayStatus(Collections.singleton(dto.getCashCode()));
        Assert.isTrue(result.isSuccess(), result.getMessage());
        return result;
    }


    /**
     * 删除
     *
     * @param idList
     * @return com.biz.crm.business.common.sdk.model.Result<java.lang.String>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/22 10:00
     */
    @Override
    public Result<String> delete(List<String> idList) {
        Validate.notEmpty(idList, "删除数据时，主键集合不能为空！");
        List<FeeCash> feeCashes = this.feeCashRepository.findByIds(idList);
        if (CollectionUtils.isEmpty(feeCashes)) {
            return Result.error("删除的数据不存在,清刷新重试!");
        }
        long valDataCashType = feeCashes.stream().map(FeeCash::getCashType).distinct().count();
        Validate.isTrue(valDataCashType <= 1, "删除时，单次只能一种兑付类型！");
        long valDataCashMethod = feeCashes.stream().map(FeeCash::getCashMethod).distinct().count();
        Validate.isTrue(valDataCashMethod <= 1, "删除时，单次只能一种兑付方式！");
        feeCashes.forEach(item -> Validate.isTrue(ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】数据能删除"));

        List<FeeCash> valCashes = feeCashes.stream()
                .filter(item -> ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()))
                .collect(Collectors.toList());
        Validate.isTrue(valCashes.size() <= 1, "驳回或追回的数据不支持批量删除！");

        //提兑付类型=费用兑付 兑付方式=电汇/账扣 时 推送费控,
        List<FeeCash> feeSendHecList = valCashes.stream()
                .filter(item -> ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()))
                .filter(k -> CashTypeEnum.FEE.getDictCode().equals(k.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(k.getCashType()))
                .filter(k -> CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(k.getCashMethod())
                        || CashMethodEnum.DEDUCTIONS.getDictCode().equals(k.getCashMethod()))
                .collect(Collectors.toList());
        Result<String> result = new Result<>();
        if (CollectionUtil.isNotEmpty(feeSendHecList)) {
            List<FeeCashDto> sendHecDtoList = (List<FeeCashDto>) nebulaToolkitService.copyCollectionByWhiteList(feeSendHecList,
                    FeeCash.class, FeeCashDto.class, HashSet.class, ArrayList.class);
            sendHecDtoList.forEach(dto -> {
                dto.setHecOperationType(HecOperationTypeEnum.DELETE.getCode());
            });
            result = this.feeSendHec(sendHecDtoList);
        }

        //删除OA接口
        List<FeeCash> feeSendOaList = valCashes.stream()
                .filter(item -> ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()))
                .filter(k -> CashTypeEnum.FEE.getDictCode().equals(k.getCashType()))
                .filter(k -> CashMethodEnum.REPLENISHMENT.getDictCode().equals(k.getCashMethod()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(feeSendOaList)) {
            feeSendOaList.forEach(e -> ryOaProcessService.deleteWorkflow(e.getStatus(), e.getOaId()));
        }

        //推送费控 兑付类型=账扣预付/电汇预付
        List<FeeCash> wireAndAccountHecList = valCashes.stream()
                .filter(item -> ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()))
                .filter(k -> CashTypeEnum.WIRE.getDictCode().equals(k.getCashType())
                        || CashTypeEnum.ACCOUNT.getDictCode().equals(k.getCashType()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(wireAndAccountHecList)) {
            List<FeeCashDto> sendHecDtoList = (List<FeeCashDto>) nebulaToolkitService.copyCollectionByWhiteList(wireAndAccountHecList,
                    FeeCash.class, FeeCashDto.class, HashSet.class, ArrayList.class);
            sendHecDtoList.forEach(dto -> {
                dto.setHecOperationType(HecOperationTypeEnum.DELETE.getCode());
            });
            result = this.wireAndAccountSendHec(sendHecDtoList);
        }
        if (!result.isSuccess()) {
            return result;
        }
        List<String> codes = feeCashes.stream().map(FeeCash::getCashCode).collect(Collectors.toList());
        feeCashFilesRepository.deleteByCodes(codes);
        feeCashDetailRepository.deleteByCodes(codes);
        feeCashPayeeRepository.deleteByCodes(codes);
        List<FeeCashInvoice> feeCashInvoiceList = feeCashInvoiceRepository.findByCodes(codes);
        if (!CollectionUtils.isEmpty(feeCashInvoiceList)) {
            // 释放状态
            List<String> uniqueKeyList = feeCashInvoiceList.stream().map(v -> v.getInvoiceNo() + v.getInvoiceDetailNo()).distinct().collect(Collectors.toList());
            this.invoiceDetailRepository.updateUsedByUniqueKeyList(uniqueKeyList);
        }
        feeCashInvoiceRepository.deleteByCodes(codes);
        feeCashTicketRepository.deleteByCodes(codes);
        feeCashTocRepository.deleteByCodes(codes);
        feeCashPrepayRepository.deleteByCodes(codes);
        feeCashRepository.removeByIds(idList);
        //清除兑付单关联
        invoiceService.clearCashCode(codes);
        //删除业务日志
        Collection<FeeCashDto> dtoList = nebulaToolkitService.copyCollectionByWhiteList(feeCashes,
                FeeCash.class, FeeCashDto.class, HashSet.class, ArrayList.class);
        FeeCashLogEventDto logEventDto = new FeeCashLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(new ArrayList<>(dtoList));
        SerializableBiConsumer<FeeCashLogEventListener, FeeCashLogEventDto> onDelete =
                FeeCashLogEventListener::onDelete;
        this.nebulaNetEventClient.publish(logEventDto, FeeCashLogEventListener.class, onDelete);
        result.setMessage("删除成功!");
        return result;
    }

    /**
     * 推送费控  提兑付类型=费用兑付 兑付方式=电汇/账扣 时 推送费控
     *
     * @param sendHecList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/9 17:04
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> feeSendHec(List<FeeCashDto> sendHecList) {
        if (CollectionUtil.isEmpty(sendHecList)) {
            return Result.error("推送数据不能为空!");
        }
        sendHecList.forEach(dto -> {
            Assert.hasLength(dto.getHecOperationType(), "操作类型不能为空!");
        });
        StringBuffer errorMsg = new StringBuffer();
        sendHecList.forEach(dto -> {
            ExternalLogDetailDto logDetailDto = null;
            JSONObject jsonObject = this.buildFeeSendData(dto);
            Assert.notNull(jsonObject, "未查询到数据,请刷新重新选择数据!");
            try {
                String token = costControlLoginService.getToken();
                UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
                Map<String, String> headMap = Maps.newHashMap();
                headMap.put(RyConstant.HEADER_AUTHORIZATION, token);
                String url = urlAddressVo.getUrl();
                String interfaceAddress = RyConstant.FK_COMMON_PAY_ORDER_INTERFACE_ADDRESS;
                JSONObject headJson = new JSONObject();
                headJson.put(RyConstant.HEADER_AUTHORIZATION, token);
                logDetailDto = ExternalLogUtil.buildLogSaveInfo(jsonObject.toJSONString(), urlAddressVo);
                logDetailDto.setReqHead(headJson.toJSONString());
                logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf(".") + 1));
                logDetailDto.setRequestUri(interfaceAddress);
                logDetailDto.setMethodMsg("对公付款单:费用兑付[兑付类型=费用兑付且兑付方式=电汇/账扣推送费控]");
                externalLogVoService.addOrUpdateLog(logDetailDto, true);
                Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, jsonObject.toJSONString(), headMap);
                ExternalLogUtil.buildLogResult(logDetailDto, result);
                this.buildHecResultData(result, dto);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
            } catch (Exception e) {
                if (Objects.nonNull(logDetailDto)) {
                    logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                    logDetailDto.setTipMsg(e.getMessage());
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                }
                log.error(e.getMessage(), e);
                errorMsg.append("单据[");
                errorMsg.append(dto.getCashCode());
                errorMsg.append("];提交费控异常:");
                errorMsg.append(e.getMessage());
                errorMsg.append(";");
            }

        });
        Result<String> result = new Result<>();
        if (StringUtil.isNotEmpty(errorMsg.toString())) {
            Validate.isTrue(false, "提交费控失败，错误信息：" + errorMsg.toString());
            result.error500(errorMsg.toString());
        }
        return result;

    }

    /**
     * 费用返回数据
     *
     * @param result
     */
    private void buildHecResultData(Result<String> result, FeeCashDto dto) {
        Assert.hasLength(result.getResult(), "费控返回信息为空1!");
        JSONObject jsonObject = JSONObject.parseObject(result.getResult());
        Assert.notNull(jsonObject, "费控返回信息为空2!");
        Assert.isTrue(jsonObject.containsKey("status"), "费控返回信息缺少[status]字段!");
        Assert.isTrue(jsonObject.containsKey("message"), "费控返回信息缺少[message]字段!");
        Assert.isTrue(String.valueOf(com.biz.crm.business.common.sdk.constant.CommonConstant.SC_OK_200)
                .equals(jsonObject.getString("status")), "费控返回信息:" + jsonObject.get("message"));
        Assert.isTrue(jsonObject.containsKey("payload"), "费控返回信息缺少[payload]对象!");
        JSONObject payload = jsonObject.getJSONObject("payload");
        Assert.notNull(payload, "费控返回信息[payload]对象为空");
        Assert.isTrue(payload.containsKey("status"), "费控返回信息缺少[payload]对象缺少[status]字段!");
        Assert.isTrue(payload.containsKey("message"), "费控返回信息缺少[payload]对象缺少[message]字段!");
        Assert.isTrue(ExternalLogGlobalConstants.S.equals(payload.getString("status")), "费控返回信息:" + payload.get("message"));
        Assert.isTrue(payload.containsKey("result"), "费控返回信息缺少[payload]对象缺少[result]字段!");
        if (HecOperationTypeEnum.CREATE.getCode().equals(dto.getHecOperationType())
                || HecOperationTypeEnum.UPDATE.getCode().equals(dto.getHecOperationType())) {
            JSONArray resultJson = payload.getJSONArray("result");
            Assert.notNull(resultJson, "费控返回信息[result]对象为空1");
            Assert.notEmpty(resultJson, "费控返回信息[result]对象为空2");
            String hecReceiptNumber = resultJson.getJSONObject(0).getString("hecReceiptNumber");
            String hecReceiptUrl = resultJson.getJSONObject(0).getString("hecReceiptUrl");
            Assert.hasLength(hecReceiptNumber, "费控返回信息[result]内[hecReceiptNumber]为空");
            Assert.hasLength(hecReceiptUrl, "费控返回信息[result]内[hecReceiptUrl]为空");
            FeeCash feeCash = feeCashRepository.findByCode(dto.getCashCode());
            Assert.notNull(feeCash, "更新的数据不存在!");
            feeCash.setProcessKey(hecReceiptNumber);
            feeCash.setHecReceiptUrl(hecReceiptUrl);
            feeCash.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
            feeCash.setProcessDate(new Date());
            feeCashRepository.updateById(feeCash);
        }

    }

    /**
     * 构建明细
     *
     * @param dto
     * @return
     */
    private JSONObject buildFeeSendData(FeeCashDto dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        FeeCashVo vo = feeCashService.findByCode(dto.getCashCode());
        Assert.notNull(vo, "未找到数据[" + vo.getCashCode() + "]");
        Assert.notEmpty(vo.getDetailList(), "费用兑付[" + vo.getCashCode() + "]预付/兑付明细不能为空");
        vo.setHecOperationType(dto.getHecOperationType());
        JSONObject jsonObject = new JSONObject();
        JSONObject headVo = this.buildFeeHeadVo(vo);
        JSONArray reportLines = this.buildFeeDetailVo(vo);
        JSONArray paymentScheduleLines = this.buildFeePayVo(vo);

        /**
         * 电汇兑付，收款对象是员工时，传 TPMEXP003；
         * 电汇兑付，收款对象是供应商时，传 TPMEXP004
         * 账扣兑付，传 TPMEXP012
         * 2024-07-16调整
         */
        AtomicReference<String> moExpReportTypeCode = new AtomicReference<>("");
        if (CashMethodEnum.DEDUCTIONS.getDictCode().equals(vo.getCashMethod())) {
            moExpReportTypeCode.set("TPMEXP012");
        }
        vo.getPayeeList().forEach(detailVo -> {
            PaeeTypeEnum paeeTypeEnum = PaeeTypeEnum.findByCode(detailVo.getPayeeType());
            Assert.notNull(paeeTypeEnum, "费用兑付[" + vo.getCashCode() + "]收款方类型不合法!");
            if (CashMethodEnum.WIRE_TRANSFER.getDictCode().equals(vo.getCashMethod())) {
                if (PaeeTypeEnum.PAYEE.equals(paeeTypeEnum)) {
                    moExpReportTypeCode.set("TPMEXP004");
                } else if (PaeeTypeEnum.PERSON.equals(paeeTypeEnum)) {
                    moExpReportTypeCode.set("TPMEXP003");
                }
            }
        });
        headVo.put("moExpReportTypeCode", moExpReportTypeCode.get());
        headVo.put("reportLines", reportLines);
        headVo.put("paymentScheduleLines", paymentScheduleLines);
        List<String> fileUrlList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(vo.getFilesList())) {
            vo.getFilesList().forEach(item -> {
                fileUrlList.add(String.format(domainNameFile, item.getFileCode()));
            });
        }
        headVo.put("fileUrls", fileUrlList);
        jsonObject.put("payload", JSONObject.toJSONString(headVo));
        return jsonObject;
    }

    /**
     * 构建预付明细
     *
     * @param vo
     * @return
     */
    private JSONArray buildFeePayVo(FeeCashVo vo) {
        JSONArray jsonArray = new JSONArray();
        if (Objects.isNull(vo)) {
            return jsonArray;
        }
        List<FeeCashPrepayVo> detailRecordVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(vo.getDetailList())) {
            List<FeeCashPrepayVo> detailRecordData = feeCashPrepayRepository.findByCode(vo.getCashCode());
            if (CollectionUtil.isNotEmpty(detailRecordData)) {
                // 过滤掉本次冲抵金额为空或为0的数据
                detailRecordVos.addAll(detailRecordData.stream().filter(v -> Objects.nonNull(v.getThisReversedAmount())
                        && v.getThisReversedAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()));
            }
        }
        Assert.hasLength(vo.getCashMethod(), "费用兑付[" + vo.getCashCode() + "]兑付方式不能为空");
        CashMethodEnum cashMethodEnum = CashMethodEnum.findByCode(vo.getCashMethod());
        Assert.notNull(cashMethodEnum, "费用兑付[" + vo.getCashCode() + "]兑付类型[" + vo.getCashType() + "]不合法!");
        switch (cashMethodEnum) {
            case WIRE_TRANSFER:
                Assert.notNull(vo.getPayeeList(), "费用兑付[" + vo.getCashCode() + "]无支付明细!");
                vo.getPayeeList().forEach(item -> {
                    PaeeTypeEnum paeeTypeEnum = PaeeTypeEnum.findByCode(item.getPayeeType());
                    Assert.notNull(paeeTypeEnum, "费用兑付[" + vo.getCashCode() + "]收款方类型不合法!");
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("dueAmount", item.getThisPayAmount());
                    jsonObject.put("payeeCategory", paeeTypeEnum.getHecCode());
                    jsonObject.put("payeeCode", item.getSupplierErpCode());
                    jsonObject.put("accountName", item.getAccountName());
                    jsonObject.put("bankLocationCode", item.getPayeeAccount());
                    jsonObject.put("accountNumber", item.getInterbankNumber());
                    jsonObject.put("paymentMethodCode", item.getPayType());
                    jsonObject.put("paymentUsedeCode", item.getPayPurpose());
                    jsonObject.put("scheduleDueDate", DateUtil.format(item.getPayDate(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
                    jsonObject.put("paymentCurrencyCode", RyConstant.CNY);
                    jsonObject.put("description", item.getRemark());
                    jsonObject.put("tpmPmtLineNumber", item.getLineCode());
                    if (CollectionUtil.isEmpty(detailRecordVos)) {
                        jsonObject.put("writeOffFlag", BooleanEnum.FALSE.getCapital());
                    } else {
                        jsonObject.put("writeOffFlag", BooleanEnum.TRUE.getCapital());
                        JSONArray feePayAuditArray = new JSONArray();
                        for (FeeCashPrepayVo detailRecordVo : detailRecordVos) {
                            JSONObject feePayAuditVo = new JSONObject();
                            feePayAuditVo.put("sourceOrderNumber", detailRecordVo.getPrepayCode());
                            //1046976 【费用兑付】账扣兑付传核销列表时，sourceOrderLineNumber传值同sourceOrderNumber，传兑付单号，不传兑付明细号
                            feePayAuditVo.put("sourceOrderLineNumber", detailRecordVo.getLineCode());
                            feePayAuditVo.put("cshWriteOffAmount", detailRecordVo.getThisReversedAmount());
                            feePayAuditArray.add(feePayAuditVo);
                        }

                        jsonObject.put("writeOffList", feePayAuditArray);
                    }
                    jsonArray.add(jsonObject);
                });
                break;
            case DEDUCTIONS:
                Assert.notNull(vo.getDetailList(), "费用兑付[" + vo.getCashCode() + "]无预付明细!");
                //宋佳丽:  收款信息 特殊：账扣和账扣预付 没有收款信息，取预付明细客户在供应商管理下查询银行信息，有多条时任取一条）
                //韩凯:  传固定值 ******** 供应商：乐联商贸有限公司 银行账号：*************** 银联号：************
//                List<String> erpCodeList = vo.getDetailList().stream()
//                        .filter(k -> StringUtil.isNotEmpty(k.getErpCode()))
//                        .filter(k -> StringUtil.isNotEmpty(k.getCompanyCode()))
//                        .map(v -> v.getErpCode() + v.getCompanyCode()).distinct().collect(Collectors.toList());
//                Assert.notEmpty(erpCodeList, "费用兑付[" + vo.getCashCode() + "]明细未填写客户ERP编码!");
//                List<MdmSupplierVo> supplierVoList = mdmSupplierVoService.findDetailsByCodes(erpCodeList);
//                Assert.notEmpty(supplierVoList, "费用兑付[" + vo.getCashCode() + "]客户未找到供应商信息!");
//                Map<String, MdmSupplierBankVo> bankVoMap = Maps.newHashMap();
//                supplierVoList.forEach(item -> {
//                    if (CollectionUtil.isEmpty(item.getBankList())) {
//                        return;
//                    }
//                    bankVoMap.put(item.getSupplierCode(), item.getBankList().get(0));
//                });
                //MdmSupplierBankVo bankVo = bankVoMap.get(item.getErpCode() + item.getCompanyCode());
                //Assert.notNull(bankVo, "费用兑付[" + vo.getCashCode() + "]明细[" + item.getCashDetailCode() + "]未找到供应商信息");
                //1047533 【费用兑付】对公付款单、预付申请单接口字段调整（只调整账扣）


                BigDecimal thisCashAmount = vo.getDetailList().stream().filter(Objects::nonNull)
                        .filter(k -> Objects.nonNull(k.getThisCashAmount()))
                        .map(FeeCashDetailVo::getThisCashAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("dueAmount", thisCashAmount);
                //韩凯:  传固定值 ******** 供应商：乐联商贸有限公司 银行账号：*************** 银联号：************
                jsonObject.put("payeeCategory", PaeeTypeEnum.CUSTOMER.getHecCode());
                jsonObject.put("payeeCode", vo.getDetailList().stream().filter(Objects::nonNull)
                        .filter(k -> StringUtil.isNotEmpty(k.getErpCode()))
                        .findFirst().orElse(new FeeCashDetailVo()).getErpCode());
                //1047533 【费用兑付】对公付款单、预付申请单接口字段调整（只调整账扣）
                this.setDictData(jsonObject);
                //付款方式
                jsonObject.put("paymentMethodCode", "101");
                //付款用途
                jsonObject.put("paymentUsedeCode", "030");
                jsonObject.put("scheduleDueDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
                jsonObject.put("paymentCurrencyCode", RyConstant.CNY);
                jsonObject.put("description", "");
                jsonObject.put("tpmPmtLineNumber", vo.getCashCode());
                if (CollectionUtil.isEmpty(detailRecordVos)) {
                    jsonObject.put("writeOffFlag", BooleanEnum.FALSE.getCapital());
                } else {
                    jsonObject.put("writeOffFlag", BooleanEnum.TRUE.getCapital());
                    JSONArray feePayAuditArray = new JSONArray();
                    Map<String, List<FeeCashPrepayVo>> prepayVoMap = detailRecordVos.stream().filter(k -> StringUtil.isNotEmpty(k.getPrepayCode()))
                            .collect(Collectors.groupingBy(FeeCashPrepayVo::getPrepayCode));
                    prepayVoMap.forEach((prepayCode, list) -> {
                        JSONObject feePayAuditVo = new JSONObject();
                        feePayAuditVo.put("sourceOrderNumber", prepayCode);
                        //1046976 【费用兑付】账扣兑付传核销列表时，sourceOrderLineNumber传值同sourceOrderNumber，传兑付单号，不传兑付明细号
                        feePayAuditVo.put("sourceOrderLineNumber", prepayCode);
                        feePayAuditVo.put("cshWriteOffAmount", list.stream().filter(k -> Objects.nonNull(k.getThisReversedAmount()))
                                .map(FeeCashPrepayVo::getThisReversedAmount).filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
                        feePayAuditArray.add(feePayAuditVo);
                    });


                    jsonObject.put("writeOffList", feePayAuditArray);
                }
                jsonArray.add(jsonObject);

                break;
            default:
                throw new IllegalArgumentException("不支持当前兑付类型的推送");
        }


        return jsonArray;
    }

    /**
     * 构建兑付明细
     *
     * @param vo
     * @return
     */
    private JSONArray buildFeeDetailVo(FeeCashVo vo) {
        JSONArray jsonArray = new JSONArray();

        if (Objects.isNull(vo)
                || CollectionUtil.isEmpty(vo.getDetailList())) {
            return jsonArray;
        }
        List<String> schemeDetailCodeList = Lists.newArrayList();
        vo.getDetailList().forEach(item -> {
            Assert.hasLength(item.getSchemeDetailCode(), "费用兑付[" + vo.getCashCode() + "]方案明细编码不能为空!");
            schemeDetailCodeList.add(item.getSchemeDetailCode());
        });
        List<MarketingPlanCaseVo> caseVoList = marketingPlanCaseService.findByCaseCodes(schemeDetailCodeList);
        Assert.notEmpty(caseVoList, "费用兑付[" + vo.getCashCode() + "]方案明细不存在!");
        Map<String, MarketingPlanCaseVo> caseVoMap = caseVoList.stream()
                .collect(Collectors.toMap(MarketingPlanCaseVo::getSchemeDetailCode, v -> v, (n, o) -> n));
        List<String> orgCodeList = caseVoList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getBearDepartmentCode()))
                .map(MarketingPlanCaseVo::getBearDepartmentCode)
                .distinct().collect(Collectors.toList());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(orgCodeList);

        Assert.notEmpty(caseVoList, "费用兑付[" + vo.getCashCode() + "]方案明细不存在!");
        Map<String, List<FeeCashInvoiceVo>> invoiceMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(vo.getInvoiceList())) {
            invoiceMap.putAll(vo.getInvoiceList().stream().filter(k -> StringUtil.isNotEmpty(k.getAuditDetailCode()))
                    .collect(Collectors.groupingBy(FeeCashInvoiceVo::getAuditDetailCode)));
        }
        vo.getDetailList().forEach(detailVo -> {
            MarketingPlanCaseVo caseVo = caseVoMap.getOrDefault(detailVo.getSchemeDetailCode(), new MarketingPlanCaseVo());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("respCenterCode", caseVo.getCostCenterCode());
            jsonObject.put("businessCurrencyCode", RyConstant.CNY);
            jsonObject.put("paymentCurrencyCode", RyConstant.CNY);
            jsonObject.put("managementCurrencyCode", RyConstant.CNY);
            jsonObject.put("description", detailVo.getRemark());
            // 根据票据类型设置attribute6
            if (BillTypeEnum.RECEIPT.getDictCode().equals(detailVo.getBillType())) {
                jsonObject.put("attribute6", "receipt");
            } else {
                // 票据类型为发票或其他情况时，保持现有逻辑
                jsonObject.put("attribute6", "invoice");
            }
            jsonObject.put("businessItemCode", caseVo.getCategoryCode());
            jsonObject.put("businessAmount", detailVo.getThisCashAmount());
            jsonObject.put("attribute8", caseVo.getYears());
            jsonObject.put("dimension5Code", detailVo.getErpCode());
            jsonObject.put("dimension3Code", detailVo.getItemCode());
            jsonObject.put("sourceTpmMktPool", detailVo.getCashDetailCode());
            List<OrgOaOrgVo> oaOrgVoList = orgOaOrgVoMap.get(caseVo.getBearDepartmentCode());
            Assert.notEmpty(oaOrgVoList, "费用兑付[" + vo.getCashCode() +
                    "]方案明细[" + detailVo.getSchemeDetailCode() + "]承担部门[" + caseVo.getBearDepartmentCode() + "]未找到OA组织!");
            jsonObject.put("unitCode", oaOrgVoList.get(0).getOaOrgCode());

            JSONArray invoiceArray = new JSONArray();
            List<FeeCashInvoiceVo> invoiceVoList = invoiceMap.get(detailVo.getAuditDetailCode());
            if (BillTypeEnum.INVOICE.getDictCode().equals(detailVo.getBillType()) && CollectionUtil.isNotEmpty(invoiceVoList)) {
                invoiceVoList.forEach(invoiceVo -> {
                    JSONObject invoiceObject = new JSONObject();
                    invoiceObject.put("invoiceCode", invoiceVo.getCode());
                    invoiceObject.put("invoiceNumber", invoiceVo.getInvoiceNo());
                    invoiceObject.put("lineNumber", invoiceVo.getInvoiceDetailNo());
                    invoiceObject.put("businessAmount", invoiceVo.getReimbursementAmount());
                    invoiceArray.add(invoiceObject);
                });
            }
            jsonObject.put("relationInvoiceLines", invoiceArray);

            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }

    /**
     * 构建费控请求头数据
     *
     * @param vo
     * @return
     */
    private JSONObject buildFeeHeadVo(FeeCashVo vo) {
        JSONObject headVo = new JSONObject();
        Assert.hasLength(vo.getPositionCode(), "费用兑付[" + vo.getCashCode() +
                "]职位[position_code]为空!");
        PositionVo positionVo = positionVoService.findByPositionCode(vo.getPositionCode());
        Assert.notNull(positionVo, "费用兑付[" + vo.getCashCode() +
                "职位[" + vo.getPositionCode() + "]在主数据中不存在!");
        headVo.put("companyCode", positionVo.getSubCompanyId());
        headVo.put("employeeCode", vo.getCreateAccount());
        headVo.put("accEntityCode", vo.getCompanyCode());
        headVo.put("unitCode", positionVo.getDepartmentId());
        headVo.put("sourceSystem", "TPM");
        headVo.put("sourceOrderNumber", vo.getCashCode());
        headVo.put("sourceSystemOperation", vo.getHecOperationType());
        headVo.put("sourceOrderType", HecBusinessTypeEnum.TPM_FEE_CASH_HEC.getCode());
        headVo.put("operatorCode", positionVo.getUserName());
        headVo.put("operatedDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
        headVo.put("paymentCurrencyCode", RyConstant.CNY);
        headVo.put("description", vo.getRemark());
        headVo.put("requisitionDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));

        headVo.put("accrualReqTypeCode", "TPMYT004");
        headVo.put("creationMethod", "MANUAL");
        headVo.put("sourceOrderUrl", domainName + TpmOaPageEnum.FEE_REDEMPTION_FORM.getUrlCode() + "?code=" + vo.getCashCode());
        headVo.put("reportDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));

        return headVo;
    }

    /**
     * 推送费控 兑付类型=账扣预付/电汇预付
     *
     * @param sendHecList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/9 17:04
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> wireAndAccountSendHec(List<FeeCashDto> sendHecList) {
        if (CollectionUtil.isEmpty(sendHecList)) {
            return Result.error("推送数据不能为空!");
        }
        StringBuffer errorMsg = new StringBuffer();
        sendHecList.forEach(dto -> {
            ExternalLogDetailDto logDetailDto = null;
            JSONObject jsonObject = this.buildWireAndAccountSendData(dto);
            Assert.notNull(jsonObject, "费用兑付[" + dto.getCashCode() + "]未查询到数据,请刷新重新选择数据!");
            try {
                String token = costControlLoginService.getToken();
                UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
                Map<String, String> headMap = Maps.newHashMap();
                headMap.put(RyConstant.HEADER_AUTHORIZATION, token);
                String url = urlAddressVo.getUrl();
                String interfaceAddress = RyConstant.FK_WIRE_APPLY_FORM_INTERFACE_ADDRESS;
                JSONObject headJson = new JSONObject();
                headJson.put(RyConstant.HEADER_AUTHORIZATION, token);
                logDetailDto = ExternalLogUtil.buildLogSaveInfo(jsonObject.toJSONString(), urlAddressVo);
                logDetailDto.setReqHead(headJson.toJSONString());
                logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf(".") + 1));
                logDetailDto.setRequestUri(interfaceAddress);
                logDetailDto.setMethodMsg("预付申请单:费用兑付[兑付类型=账扣预付/电汇预付]");
                externalLogVoService.addOrUpdateLog(logDetailDto, true);
                Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, jsonObject.toJSONString(), headMap);
                ExternalLogUtil.buildLogResult(logDetailDto, result);
                this.buildHecResultData(result, dto);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
            } catch (Exception e) {
                if (Objects.nonNull(logDetailDto)) {
                    logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                    logDetailDto.setTipMsg(e.getMessage());
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                }
                log.error(e.getMessage(), e);
                errorMsg.append("单据[");
                errorMsg.append(dto.getCashCode());
                errorMsg.append("];提交费控异常:");
                errorMsg.append(e.getMessage());
                errorMsg.append(";");
            }

        });
        Result<String> result = new Result<>();
        Validate.isTrue(StringUtil.isEmpty(errorMsg.toString()), "推送费控失败，错误信息：" + errorMsg.toString());
        if (StringUtil.isNotEmpty(errorMsg.toString())) {
            result.error500(errorMsg.toString());
        }
        return result;

    }


    /**
     * 构建明细 推送费控 兑付类型=账扣预付/电汇预付
     *
     * @param dto
     * @return
     */
    private JSONObject buildWireAndAccountSendData(FeeCashDto dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        FeeCashVo vo = feeCashService.findByCode(dto.getCashCode());
        Assert.notNull(vo, "未找到数据[" + vo.getCashCode() + "]");
        Assert.notEmpty(vo.getDetailList(), "费用兑付[" + vo.getCashCode() + "]预付/兑付明细不能为空");
        Assert.hasLength(vo.getCashType(), "费用兑付[" + vo.getCashCode() + "]兑付类型不能为空");
        CashTypeEnum cashTypeEnum = CashTypeEnum.findByCode(vo.getCashType());
        Assert.notNull(cashTypeEnum, "费用兑付[" + vo.getCashCode() + "]兑付类型[" + vo.getCashType() + "]不合法");
        vo.setHecOperationType(dto.getHecOperationType());
        JSONObject jsonObject = new JSONObject();
        JSONObject headVo = this.buildWireAndAccountHead(vo);
        JSONArray requisitionLines = this.buildRequisitionLines(vo);
        JSONArray paymentLines = this.buildPaymentLines(vo);
        JSONArray paymentDetails = new JSONArray();
        AtomicReference<String> moExpReportTypeCode = new AtomicReference<>("");
        //1、【活动预付】传“TPMREQ09”
        //2、【费用兑付-电汇预付】
        //①收款信息-收款方类型=供应商且“TOC支付为空”时，传“TPMREQ09”
        //②收款信息-收款方类型=供应商且“TOC支付不为空”时，传“REQ12”
        //3、【费用兑付-账扣预付】传“TPMREQ10”
        if (CashTypeEnum.ACCOUNT.equals(cashTypeEnum)) {
            moExpReportTypeCode.set("TPMREQ10");
            headVo.put("sourceOrderUrl", domainName + TpmOaPageEnum.FEE_REDEMPTION_ACCOUNTFORM.getUrlCode() + "?code=" + vo.getCashCode());
        } else if (CashTypeEnum.WIRE.equals(cashTypeEnum)) {
            headVo.put("sourceOrderUrl", domainName + TpmOaPageEnum.FEE_REDEMPTION_ONLINEFORM.getUrlCode() + "?code=" + vo.getCashCode());
            paymentDetails = this.buildPaymentDetails(vo);
            vo.getPayeeList().forEach(detailVo -> {
                PaeeTypeEnum paeeTypeEnum = PaeeTypeEnum.findByCode(detailVo.getPayeeType());
                Assert.notNull(paeeTypeEnum, "费用兑付[" + vo.getCashCode() + "]收款方类型不合法!");
                Assert.isTrue(PaeeTypeEnum.PAYEE.equals(paeeTypeEnum), CashTypeEnum.WIRE.getValue() + "只支持供应商付款类型!");
                if (CollectionUtil.isEmpty(vo.getTocList())) {
                    moExpReportTypeCode.set("TPMREQ09");
                } else {
                    moExpReportTypeCode.set("REQ12");
                }
            });
        }
        headVo.put("moExpReqTypeCode", moExpReportTypeCode.get());
        headVo.put("requisitionLines", requisitionLines);
        headVo.put("paymentLines", paymentLines);
        headVo.put("paymentDetails", paymentDetails);
        List<String> fileUrlList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(vo.getFilesList())) {
            vo.getFilesList().forEach(item -> {
                fileUrlList.add(String.format(domainNameFile, item.getFileCode()));
            });
        }
        headVo.put("fileUrls", fileUrlList);
        jsonObject.put("payload", JSONObject.toJSONString(headVo));
        return jsonObject;
    }

    /**
     * 构建推送明细  头部信息
     * 推送费控 兑付类型=账扣预付/电汇预付
     *
     * @param vo
     * @return
     */
    private JSONObject buildWireAndAccountHead(FeeCashVo vo) {
        JSONObject headVo = new JSONObject();
        if (Objects.isNull(vo)) {
            return headVo;
        }
        Assert.hasLength(vo.getPositionCode(), "费用兑付[" + vo.getCashCode() +
                "]职位[position_code]为空!");
        PositionVo positionVo = positionVoService.findByPositionCode(vo.getPositionCode());
        Assert.notNull(positionVo, "费用兑付[" + vo.getCashCode() +
                "职位[" + vo.getPositionCode() + "]在主数据中不存在!");
        headVo.put("employeeCode", vo.getCreateAccount());
        headVo.put("paymentCurrencyCode", RyConstant.CNY);
        headVo.put("description", vo.getRemark());
        headVo.put("requisitionDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
        headVo.put("advanceFlag", "Y");
        headVo.put("paymentFlag", "N");
        headVo.put("assetBusinessFlag", "N");
        headVo.put("sourceSystem", "TPM");
        headVo.put("sourceOrderType", HecBusinessTypeEnum.TPM_FEE_CASH_HEC.getCode());
        headVo.put("sourceOrderNumber", vo.getCashCode());
        headVo.put("sourceSystemOperation", vo.getHecOperationType());
        headVo.put("operatedDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
        headVo.put("operatorCode", positionVo.getUserName());
        headVo.put("accEntityCode", vo.getCompanyCode());
        headVo.put("companyCode", positionVo.getSubCompanyId());
        headVo.put("unitCode", positionVo.getDepartmentId());
        return headVo;
    }

    /**
     * 构建推送明细  行信息
     * 推送费控 兑付类型=账扣预付/电汇预付
     *
     * @param vo
     * @return
     */
    private JSONArray buildRequisitionLines(FeeCashVo vo) {
        JSONArray jsonArray = new JSONArray();
        if (Objects.isNull(vo)
                || CollectionUtil.isEmpty(vo.getDetailList())) {
            return jsonArray;
        }
        List<String> schemeDetailCodeList = Lists.newArrayList();
        vo.getDetailList().forEach(item -> {
            Assert.hasLength(item.getSchemeDetailCode(), "方案明细编码不能为空!");
            schemeDetailCodeList.add(item.getSchemeDetailCode());
        });
        List<MarketingPlanCaseVo> caseVoList = marketingPlanCaseService.findByCaseCodes(schemeDetailCodeList);
        Assert.notEmpty(caseVoList, "方案明细不存在!");
        Map<String, MarketingPlanCaseVo> caseVoMap = caseVoList.stream()
                .collect(Collectors.toMap(MarketingPlanCaseVo::getSchemeDetailCode, v -> v, (n, o) -> n));
        List<String> orgCodeList = caseVoList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getBearDepartmentCode()))
                .map(MarketingPlanCaseVo::getBearDepartmentCode)
                .distinct().collect(Collectors.toList());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(orgCodeList);
        vo.getDetailList().forEach(detailVo -> {
            JSONObject jsonObject = new JSONObject();
            MarketingPlanCaseVo caseVo = caseVoMap.getOrDefault(detailVo.getSchemeDetailCode(), new MarketingPlanCaseVo());
            jsonObject.put("primaryQuantity", 1);
            jsonObject.put("reportPageElementCode", "STANDARD");
            jsonObject.put("businessItemCode", caseVo.getCategoryCode());
            jsonObject.put("respCenterCode", caseVo.getCostCenterCode());
            jsonObject.put("attribute8", caseVo.getYears());
            jsonObject.put("dimension5Code", detailVo.getErpCode());
            jsonObject.put("dimension3Code", detailVo.getItemCode());
            List<OrgOaOrgVo> oaOrgVoList = orgOaOrgVoMap.get(caseVo.getBearDepartmentCode());
            Assert.notEmpty(oaOrgVoList, "费用兑付[" + vo.getCashCode() +
                    "]方案明细[" + detailVo.getSchemeDetailCode() + "]承担部门[" + caseVo.getBearDepartmentCode() + "]未找到OA组织!");
            jsonObject.put("unitCode", oaOrgVoList.get(0).getOaOrgCode());
            jsonObject.put("description", detailVo.getRemark());
            jsonObject.put("businessAmount", detailVo.getThisCashAmount());
            jsonObject.put("businessCurrencyCode", RyConstant.CNY);
            jsonObject.put("managementCurrencyCode", RyConstant.CNY);
            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }

    /**
     * 构建推送明细  付款行信息
     * 推送费控 兑付类型=账扣预付/电汇预付
     *
     * @param vo
     * @return
     */
    private JSONArray buildPaymentLines(FeeCashVo vo) {
        JSONArray jsonArray = new JSONArray();
        if (Objects.isNull(vo)) {
            return jsonArray;
        }
        Assert.hasLength(vo.getCashType(), "费用兑付[" + vo.getCashCode() + "]兑付类型不能为空");
        CashTypeEnum cashTypeEnum = CashTypeEnum.findByCode(vo.getCashType());
        Assert.notNull(cashTypeEnum, "费用兑付[" + vo.getCashCode() + "]兑付类型[" + vo.getCashType() + "]不合法!");
        switch (cashTypeEnum) {
            case WIRE:
                Assert.notNull(vo.getPayeeList(), "费用兑付[" + vo.getCashCode() + "]无支付明细!");
                vo.getPayeeList().forEach(item -> {
                    JSONObject jsonObject = new JSONObject();
                    PaeeTypeEnum paeeTypeEnum = PaeeTypeEnum.findByCode(item.getPayeeType());
                    Assert.notNull(paeeTypeEnum, "费用兑付[" + vo.getCashCode() + "]收款方类型不合法!");
                    //现金事务分类代码
                    jsonObject.put("moCshTrxClassCode", cashTypeEnum.getHecCode());
                    jsonObject.put("payeeCode", item.getSupplierErpCode());
                    jsonObject.put("payeeCategory", paeeTypeEnum.getHecCode());
                    jsonObject.put("paymentMethodCode", item.getPayType());
                    jsonObject.put("accountName", item.getAccountName());
                    jsonObject.put("bankLocationCode", item.getPayeeAccount());
                    jsonObject.put("accountNumber", item.getInterbankNumber());
                    jsonObject.put("paymentUsedeCode", item.getPayPurpose());
                    jsonObject.put("headDescription", vo.getRemark());
                    jsonObject.put("description", item.getRemark());
                    jsonObject.put("amount", item.getThisPayAmount());
                    jsonObject.put("documentNumber", item.getContractCode());
                    jsonObject.put("tpmPmtLineNumber", item.getLineCode());
                    jsonArray.add(jsonObject);
                });
                break;
            case ACCOUNT:
                Assert.notNull(vo.getDetailList(), "费用兑付[" + vo.getCashCode() + "]无明细!");
                //宋佳丽:  收款信息 特殊：账扣和账扣预付 没有收款信息，取预付明细客户在供应商管理下查询银行信息，有多条时任取一条）
//                List<String> erpCodeList = vo.getDetailList().stream()
//                        .filter(k -> StringUtil.isNotEmpty(k.getErpCode()))
//                        .filter(k -> StringUtil.isNotEmpty(k.getCompanyCode()))
//                        .map(v -> v.getErpCode() + v.getCompanyCode()).distinct().collect(Collectors.toList());
//                Assert.notEmpty(erpCodeList, "费用兑付[" + vo.getCashCode() + "]明细未填写客户ERP编码!");
//                List<MdmSupplierVo> supplierVoList = mdmSupplierVoService.findDetailsByCodes(erpCodeList);
//                Assert.notEmpty(supplierVoList, "费用兑付[" + vo.getCashCode() + "]客户未找到供应商信息!");
//                Map<String, MdmSupplierBankVo> bankVoMap = Maps.newHashMap();
//                supplierVoList.forEach(item -> {
//                    if (CollectionUtil.isEmpty(item.getBankList())) {
//                        return;
//                    }
//                    bankVoMap.put(item.getSupplierCode(), item.getBankList().get(0));
//                });
                //MdmSupplierBankVo bankVo = bankVoMap.get(item.getErpCode() + item.getCompanyCode());
                //Assert.notNull(bankVo, "费用兑付[" + vo.getCashCode() + "]明细[" + item.getCashDetailCode() + "]未找到供应商信息");
                BigDecimal thisCashAmount = vo.getDetailList().stream().filter(Objects::nonNull)
                        .filter(k -> Objects.nonNull(k.getThisCashAmount()))
                        .map(FeeCashDetailVo::getThisCashAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                JSONObject jsonObject = new JSONObject();
                //现金事务分类代码
                jsonObject.put("moCshTrxClassCode", cashTypeEnum.getHecCode());
                //韩凯:  传固定值 ******** 供应商：乐联商贸有限公司 银行账号：*************** 银联号：************
                //1047533 【费用兑付】对公付款单、预付申请单接口字段调整（只调整账扣）
                jsonObject.put("payeeCategory", PaeeTypeEnum.CUSTOMER.getHecCode());
                jsonObject.put("payeeCode", vo.getDetailList().stream().filter(Objects::nonNull)
                        .filter(k -> StringUtil.isNotEmpty(k.getErpCode()))
                        .findFirst().orElse(new FeeCashDetailVo()).getErpCode());
                this.setDictData(jsonObject);
                jsonObject.put("headDescription", vo.getRemark());
                jsonObject.put("description", "");
                jsonObject.put("amount", thisCashAmount);
                //付款方式固定传“101”，付款用途固定传“030”， 合同编码不传（为空） 2024年7月15日17:16:39
                //付款方式
                jsonObject.put("paymentMethodCode", "101");
                //付款用途
                jsonObject.put("paymentUsedeCode", "030");
                //合同编码
                jsonObject.put("documentNumber", "");
                jsonObject.put("tpmPmtLineNumber", vo.getCashCode());
                jsonArray.add(jsonObject);
                break;
            default:
                throw new IllegalArgumentException("不支持当前兑付类型的推送");
        }
        return jsonArray;
    }

    /**
     * 构建推送明细  打款明细行
     * 推送费控 兑付类型=账扣预付/电汇预付
     *
     * @param feeCashVo
     * @return
     */
    private JSONArray buildPaymentDetails(FeeCashVo feeCashVo) {
        JSONArray jsonArray = new JSONArray();
        if (Objects.isNull(feeCashVo)
                || CollectionUtil.isEmpty(feeCashVo.getTocList())) {
            return jsonArray;
        }
        feeCashVo.getTocList().forEach(item -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("remittancePlatform", item.getPlatform());
            jsonObject.put("payerName", item.getPayeeName());
            jsonObject.put("bankAccount", item.getBankNo());
            jsonObject.put("idCardNo", item.getIdCard());
            jsonObject.put("mobileNo", item.getPhone());
            jsonObject.put("receivedAmount", item.getPayeeAmount());
            jsonObject.put("actualRemitAmount", item.getPayAmount());
            jsonObject.put("remark", item.getRemark());
            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }


    /**
     * 查询费控电子回单
     *
     * @param
     * @param flagType
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/11 19:08
     */
    @Override
    public void queryHecElectronicReceipt(String flagType) {

        FeeCashPayeeDto dto = new FeeCashPayeeDto();
        dto.setStatus(ProcessStatusEnum.PASS.getDictCode());
        dto.setReceiptFlag(BooleanEnum.FALSE.getCapital());

        //【费用兑付】兑付类型=电汇预付
        dto.setCashType(CashTypeEnum.WIRE.getDictCode());
        dto.setCashMethod("");
        this.queryFeeCashPayeeReceipt(dto);

        //【费用兑付】兑付类型=费用兑付  兑付方式=电汇，
        dto.setCashType(CashTypeEnum.FEE.getDictCode());
        dto.setCashMethod(CashMethodEnum.WIRE_TRANSFER.getDictCode());
        this.queryFeeCashPayeeReceipt(dto);

    }

    /**
     * 更新费用兑付 付款信息 回单
     *
     * @param dto
     */
    private void queryFeeCashPayeeReceipt(FeeCashPayeeDto dto) {
        Page<FeeCashPayeeVo> payeeVoPage = null;
        Pageable pageable = PageRequest.of(1, 200);
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
        String url = urlAddressVo.getUrl();
        String interfaceAddress = RyConstant.FK_ELECTRONIC_RECEIPT_INTERFACE_ADDRESS;
        do {
            payeeVoPage = feeCashService.findByConditions(pageable, dto);
            if (Objects.isNull(payeeVoPage)
                    || CollectionUtil.isEmpty(payeeVoPage.getRecords())) {
                return;
            }
            pageable = pageable.next();
            ExternalLogDetailDto logDetailDto = null;
            try {
                String token = costControlLoginService.getToken();
                Map<String, String> headMap = Maps.newHashMap();
                headMap.put(RyConstant.HEADER_AUTHORIZATION, token);
                JSONObject headJson = new JSONObject();
                headJson.put(RyConstant.HEADER_AUTHORIZATION, token);
                JSONObject jsonObject = this.buildQueryData(HecPayBusinessTypeEnum.TPM_FEE_CASH_HEC, payeeVoPage.getRecords());
                logDetailDto = ExternalLogUtil.buildLogSaveInfo(jsonObject.toJSONString(), urlAddressVo);
                logDetailDto.setReqHead(headJson.toJSONString());
                logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf("/") + 1));
                logDetailDto.setRequestUri(interfaceAddress);
                logDetailDto.setMethodMsg("查询电子回单");
                externalLogVoService.addOrUpdateLog(logDetailDto, true);
                Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, jsonObject.toJSONString(), headMap);
                ExternalLogUtil.buildLogResult(logDetailDto, result);
                List<HecElectronicReceiptVo> receiptVoList = this.buildElectronicReceiptResult(result);
                feeCashService.updateReceipt(receiptVoList);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                if (Objects.nonNull(logDetailDto)) {
                    logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                    logDetailDto.setTipMsg(e.getMessage());
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                }
            }
        } while (payeeVoPage.hasNext() && pageable.getPageNumber() <= CommonConstant.MAX_LOOP_NUMBER);
    }

    /**
     * @param result
     */
    private List<HecElectronicReceiptVo> buildElectronicReceiptResult(Result<String> result) {
        Assert.hasLength(result.getResult(), "费控返回信息为空1!");
        JSONObject jsonObject = JSONObject.parseObject(result.getResult());
        Assert.notNull(jsonObject, "费控返回信息为空2!");
        Assert.isTrue(jsonObject.containsKey("status"), "费控返回信息缺少[status]字段!");
        Assert.isTrue(jsonObject.containsKey("message"), "费控返回信息缺少[message]字段!");
        Assert.isTrue(ExternalLogGlobalConstants.S.equals(jsonObject.getString("status")), "费控返回信息:" + jsonObject.get("message"));
        Assert.isTrue(jsonObject.containsKey("result"), "费控返回信息缺少[result]字段!");
        return JSONArray.parseArray(jsonObject.getString("result"), HecElectronicReceiptVo.class);
    }

    /**
     * 构建查询参数
     *
     * @return
     */
    private JSONObject buildQueryData(HecPayBusinessTypeEnum typeEnum, List<FeeCashPayeeVo> list) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sourceSystemCode", "TPM");
        jsonObject.put("interfaceCode", "TPM_PULL_ELECTRONIC_RECEIPT");
        JSONArray jsonArray = new JSONArray();
        JSONObject requestData = new JSONObject();
        requestData.put("electronicReceipt", jsonArray);
        jsonObject.put("requestData", requestData);
        if (CollectionUtil.isEmpty(list)
                || Objects.isNull(typeEnum)) {
            return jsonObject;
        }
        list.forEach(item -> {
            JSONObject electronicReceipt = new JSONObject();
            electronicReceipt.put("sourceOrderType", typeEnum.getCode());
            electronicReceipt.put("sourceOrderNumber", item.getCashCode());
            electronicReceipt.put("tpmPmtLineNumber", item.getLineCode());
            electronicReceipt.put("receiptCategory", typeEnum.getHecCode());
            jsonArray.add(electronicReceipt);
        });
        requestData.put("electronicReceipt", jsonArray);
        return jsonObject;
    }

    private void setDictData(JSONObject jsonObject) {
        Map<String, String> defaultMap = this.dictDataVoService.findMapByDictTypeCode("deductions_collection_default");
        Assert.notNull(defaultMap, "数据字典[deductions_collection_default]未配置!");
        jsonObject.put("accountName", defaultMap.get("accountName"));
        jsonObject.put("bankLocationCode", defaultMap.get("bankLocationCode"));
        jsonObject.put("accountNumber", defaultMap.get("accountNumber"));
    }
}
