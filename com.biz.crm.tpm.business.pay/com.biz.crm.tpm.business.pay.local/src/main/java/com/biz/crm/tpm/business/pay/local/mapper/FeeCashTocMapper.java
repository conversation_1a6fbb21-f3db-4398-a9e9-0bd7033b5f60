package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashToc;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTocVo;
import org.apache.ibatis.annotations.Param;

/**
 * TOC支付明细(FeeCashToc)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-07-01 19:20:03
 */
public interface FeeCashTocMapper extends BaseMapper<FeeCashToc> {

    /**
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<FeeCashTocVo> findByConditions(@Param("page") Page<FeeCashTocVo> page , @Param("dto") FeeCashTocVo dto);
}

