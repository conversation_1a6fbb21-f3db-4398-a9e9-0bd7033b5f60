package com.biz.crm.tpm.business.pay.local.service.strategy;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.CostPoolReplenishmentDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.CostPoolReplenishmentProductDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.PoolGroupEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.PoolOperationTypeEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.PoolPayTypeEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.PoolUseTypeEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.CostPoolReplenishmentVoService;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountProductDto;
import com.biz.crm.tpm.business.budget.sdk.strategy.account.AccountPayByStrategy;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @describe: 货补支付
 * @createTime 2022年06月21日 15:16:00
 */
public class RestockAccountPayByStrategy implements AccountPayByStrategy {

  @Autowired(required = false)
  private CostPoolReplenishmentVoService costPoolReplenishmentVoService;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  //成功代码
  private final String SUCCESSFUL_CODE = "200";


  @Override
  public void execute(Object o) {
    AccountDto accountDto = (AccountDto) o;
    Validate.notNull(accountDto, "对象不能为空!");
    String payBy = accountDto.getPayBy();
    BigDecimal amount = accountDto.getAmount();
    //校验
    Validate.notNull(amount,"上账金额不能为空");
    Validate.isTrue(amount.compareTo(BigDecimal.ZERO)>0,"上账金额必须大于0");
    Validate.notBlank(accountDto.getCustomerCode(), "向货补池上账时，缺失客户编码");
    Validate.notBlank(accountDto.getProductLevelCode(), "向货补池上账时，缺失产品层级编码");
    CostPoolReplenishmentDto dto = new CostPoolReplenishmentDto();
    dto.setPoolGroup(PoolGroupEnum.DEFAULT.getDictCode());
    dto.setUseType(PoolUseTypeEnum.DEFAULT.getDictCode());
    dto.setPayType(PoolPayTypeEnum.Replenishment.getDictCode());
    dto.setOperationType(PoolOperationTypeEnum.ACT_ACCOUNT.getDictCode());
    dto.setCustomerCode(accountDto.getCustomerCode());
    dto.setCustomerName(accountDto.getCustomerName());
    dto.setGoodsProductLevelCode(accountDto.getProductLevelCode());
    dto.setGoodsProductLevelName(accountDto.getProductLevelName());
    dto.setAmount(accountDto.getAmount());
    List<AccountProductDto> productList = accountDto.getProductList();
    if (!CollectionUtils.isEmpty(productList)) {
      List<CostPoolReplenishmentProductDto> costPoolReplenishmentProduct = new ArrayList<>();
      for (AccountProductDto accountProductDto : productList) {
        CostPoolReplenishmentProductDto productDto = new CostPoolReplenishmentProductDto();
        productDto.setGoodsProductCode(accountProductDto.getProductCode());
        productDto.setGoodsProductName(accountProductDto.getProductName());
        productDto.setProductLevelCode(accountDto.getProductLevelCode());
        costPoolReplenishmentProduct.add(productDto);
      }
      dto.setCostPoolReplenishmentProduct(costPoolReplenishmentProduct);
    }
    Result<?> result = costPoolReplenishmentVoService.handleAdjust(dto);
    Validate.notNull(result, "调用货补池上账失败["+"无法连接到DMS]");
    Validate.isTrue(result.getCode().toString().equals(SUCCESSFUL_CODE), "货补池上账失败["+result.getMessage()+"]");
  }
}
