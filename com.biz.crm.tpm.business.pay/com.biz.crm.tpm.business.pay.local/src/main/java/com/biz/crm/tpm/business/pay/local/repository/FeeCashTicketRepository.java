package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashTicket;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashTicketMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.BillingCompareReportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTicketVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 费用票扣明细(FeeCashTicket)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
@Component
public class FeeCashTicketRepository extends ServiceImpl<FeeCashTicketMapper, FeeCashTicket> {

    @Autowired
    private FeeCashTicketMapper feeCashTicketMapper;

    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    public List<FeeCashTicketVo> findByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return Lists.newArrayList();
        }
        List<FeeCashTicket> list = this.lambdaQuery().eq(FeeCashTicket::getCashCode, code).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashTicket.class, FeeCashTicketVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    public List<FeeCashTicketVo> findTicketByCreditCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return Lists.newArrayList();
        }
        List<FeeCashTicket> list = this.lambdaQuery().eq(FeeCashTicket::getCreditCode, code).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashTicket.class, FeeCashTicketVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 按编码集合查询
     *
     * @param codes
     * @return
     */
    public List<FeeCashTicketVo> findTicketByCreditCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        List<FeeCashTicket> list = this.lambdaQuery().in(FeeCashTicket::getCreditCode, codes).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashTicket.class, FeeCashTicketVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    public List<FeeCashTicketVo> findByBillingCompareReport(BillingCompareReportVo dto) {
        return baseMapper.findByBillingCompareReport(dto);
    }

    /**
     * 按编码删除
     *
     * @param codes
     */
    public void deleteByCodes(List<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return;
        }
        this.remove(Wrappers.lambdaQuery(FeeCashTicket.class)
                .in(FeeCashTicket::getCashCode, codes));
    }


    public List<FeeCashTicket> findListByCashCode(String cashCode) {
        return this.lambdaQuery()
                .eq(FeeCashTicket::getCashCode, cashCode)
                .list();
    }
}

