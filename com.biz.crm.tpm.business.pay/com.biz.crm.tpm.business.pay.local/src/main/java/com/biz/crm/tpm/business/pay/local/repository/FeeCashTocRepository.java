package com.biz.crm.tpm.business.pay.local.repository;



import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashToc;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashTocMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTocVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * TOC支付明细(FeeCashToc)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-01 19:20:03
 */
@Component
public class FeeCashTocRepository extends ServiceImpl<FeeCashTocMapper, FeeCashToc> {

  @Autowired
  private FeeCashTocMapper feeCashTocMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<FeeCashTocVo> findByCode(String code) {
    List<FeeCashToc> list = this.lambdaQuery().eq(FeeCashToc::getCashCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashToc.class, FeeCashTocVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param codes
   */
  public void deleteByCodes(List<String> codes) {
    this.remove(Wrappers.lambdaQuery(FeeCashToc.class)
            .in(FeeCashToc::getCashCode, codes));
  }
  
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  public Page<FeeCashTocVo> findByConditions(Pageable pageable, FeeCashTocVo dto) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<FeeCashTocVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return feeCashTocMapper.findByConditions(page, dto);
  }
}

