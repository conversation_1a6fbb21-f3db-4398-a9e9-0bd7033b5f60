package com.biz.crm.tpm.business.pay.local.repository;



import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.tpm.business.pay.local.entity.CreditOrder;
import com.biz.crm.tpm.business.pay.local.entity.CreditOrder;
import com.biz.crm.tpm.business.pay.local.mapper.CreditOrderMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderVo;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 贷项订单(CreditOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-16 16:18:25
 */
@Component
public class CreditOrderRepository extends ServiceImpl<CreditOrderMapper, CreditOrder> {

  @Autowired
  private CreditOrderMapper creditOrderMapper;

  /**
   * 按编码查询
   *
   * @param codes
   * @return
   */
  public List<CreditOrder> findByCodes(List<String> codes) {
    return this.lambdaQuery().in(CreditOrder::getCreditCode, codes).list();
  }

  /**
   * 按兑付单查询
   *
   * @param code
   * @return
   */
  public List<CreditOrder> findByCashCode(String code) {
    return this.lambdaQuery().eq(CreditOrder::getCashCode, code).list();
  }

  /**
   * 按兑付单推送状态
   *
   * @param
   * @return
   */
  public List<CreditOrder> findByPushStatus() {
    return this.lambdaQuery().eq(CreditOrder::getPushStatus, DmsWarehouseOrderEnum.SAP_PUSH_STATUS.NO_PUSH.getCode()).list();
  }

  public CreditOrderVo findTotalByConditions(CreditOrderVo dto) {
    List<CreditOrderVo> list = baseMapper.findTotalByConditions(dto);
    return CollectionUtils.isNotEmpty(list) ? list.get(0) : new CreditOrderVo();
  }
}

