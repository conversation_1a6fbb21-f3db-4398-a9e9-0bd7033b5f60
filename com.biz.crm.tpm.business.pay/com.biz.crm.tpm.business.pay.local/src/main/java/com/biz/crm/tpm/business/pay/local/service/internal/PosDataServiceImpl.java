package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.pay.local.entity.PosData;
import com.biz.crm.tpm.business.pay.local.repository.PosDataRepository;
import com.biz.crm.tpm.business.pay.sdk.service.PosDataService;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Service
@Slf4j
public class PosDataServiceImpl implements PosDataService {

    @Resource
    private PosDataRepository repository;

    @Resource
    private GenerateCodeService generateCodeService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private LoginUserService loginUserService;


    /**
     * 分页查询
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<PosDataVo> findList(Pageable pageable, PosDataVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<PosDataVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return repository.findList(page, vo);
    }


    /**
     * 创建数据
     *
     * @param vo
     */
    @Override
    public void create(PosDataVo vo) {
        this.validateParam(vo);
        PosData data = nebulaToolkitService.copyObjectByBlankList(vo, PosData.class, HashSet.class, ArrayList.class);
        data.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        data.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        data.setTenantCode(TenantUtils.getTenantCode());
        FacturerUserDetails user = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        data.setCreateOrgName(user.getOrgName());
        data.setCreatePostName(user.getPostName());
        data.setCreatePostCode(user.getPostCode());
        data.setConfirmStatus(BooleanEnum.FALSE.getCapital());
        repository.save(data);

    }

    private void validateParam(PosDataVo vo) {
        Validate.notNull(vo.getYears(), "POS年月不能为空");
        Validate.notNull(vo.getOrgCode(), "末级部门编码不能为空");
        Validate.notNull(vo.getCustomerCode(), "客户编码不能为空");
//        Validate.notNull(vo.getQuantity(), "销售数量不能为空");
        Validate.notNull(vo.getAmount(), "销售金额不能为空");
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(Lists.newArrayList(vo.getOrgCode()));
        Validate.isTrue(CollectionUtils.isNotEmpty(orgVoList), String.format("末级组织编码%s在主数据中不存在", vo.getOrgCode()));
        OrgVo orgVo = orgVoList.get(0);
        vo.setOrgName(orgVo.getOrgName());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(Lists.newArrayList(vo.getCustomerCode()));
        Validate.isTrue(CollectionUtils.isNotEmpty(customerVos), String.format("客户编码%s在主数据中不存在", vo.getCustomerCode()));
        CustomerVo customerVo = customerVos.get(0);
        vo.setCustomerName(customerVo.getCustomerName());
        vo.setErpCode(customerVo.getErpCode());
        vo.setCompanyCode(customerVo.getCompanyCode());
        vo.setProductGroupCode(customerVo.getProductGroupCode());
        vo.setChannelCode(customerVo.getChannelCode());
    }

    /**
     * 数据确认
     *
     * @param idList
     */
    @Override
    public void confirmData(List<String> idList) {
        List<PosData> dataList = repository.findListByIdList(idList);
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(x -> x.setConfirmStatus(BooleanEnum.TRUE.getCapital()));
            repository.updateBatchById(dataList);
        }
    }


    /**
     * 删除数据
     *
     * @param idList
     */
    @Override
    public void deleteBatchByIdList(List<String> idList) {
        List<PosData> dataList = repository.findListByIdList(idList);
        if (CollectionUtils.isNotEmpty(dataList)) {
            Set<String> codeSet = dataList.stream().filter(x -> BooleanEnum.TRUE.getCapital().equals(x.getConfirmStatus()))
                    .map(PosData::getCode).collect(Collectors.toSet());
            Validate.isTrue(CollectionUtils.isEmpty(codeSet), String.format("POS数据%s已被确认不可删除", codeSet));
            dataList.forEach(x -> x.setDelFlag(DelFlagStatusEnum.DELETE.getCode()));
            repository.updateBatchById(dataList);
        }
    }
}
