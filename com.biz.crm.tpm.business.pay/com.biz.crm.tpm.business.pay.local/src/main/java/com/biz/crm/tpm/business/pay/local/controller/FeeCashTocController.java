package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTocDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashTocService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTocVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "电汇预付-TOC功能接口")
@RestController
@RequestMapping("/v1/pay/feeCashToc")
@Slf4j
public class FeeCashTocController extends BusinessPageCacheController<FeeCashTocVo, FeeCashTocDto> {
    
    @Autowired(required = false)
    private FeeCashTocService feeCashTocService;


    /**
     * 获取所有缓存明细
     *
     * @param cacheKey
     */
    @ApiOperation(value = "获取所有缓存明细")
    @GetMapping("findAllCacheList")
    public Result<List<FeeCashTocDto>> findAllCacheList(@ApiParam(name = "cacheKey", value = "缓存键")@RequestParam String cacheKey) {
        try {
            List<FeeCashTocDto> detailVoList = this.feeCashTocService.findAllCacheList(cacheKey);
            return Result.ok(detailVoList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * TOC汇总
     *
     * @param cacheKey
     */
    @ApiOperation(value = "TOC汇总")
    @GetMapping("findTocTotal")
    public Result<FeeCashTocVo> findTocTotal(@ApiParam(name = "cacheKey", value = "TOC缓存键")@RequestParam String cacheKey) {
        try {
            FeeCashTocVo vo = this.feeCashTocService.findTocTotal(cacheKey);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分页查询所有数据
     *
     * @param pageable 分页对象
     * @param dto      查询实体
     * @return 所有数据
     */
    @ApiOperation(value = "分页查询所有数据")
    @GetMapping("findByConditions")
    public Result<Page<FeeCashTocVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                @ApiParam(name = "dto", value = "请求对象") FeeCashTocVo dto) {
        try {
            Page<FeeCashTocVo> page = this.feeCashTocService.findByConditions(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

}
