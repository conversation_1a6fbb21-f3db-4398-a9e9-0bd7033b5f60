package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.AuditFiles;
import com.biz.crm.tpm.business.pay.local.mapper.AuditFilesMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditFilesDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditFilesVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 费用核销附件;(tpm_audit_files)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Component
public class AuditFilesRepository extends ServiceImpl<AuditFilesMapper, AuditFiles> {
  @Autowired
  private AuditFilesMapper auditFilesMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<AuditFilesVo> findByConditions(Pageable pageable, AuditFilesDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<AuditFilesVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return auditFilesMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<AuditFiles>
   */
  public List<AuditFiles> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(AuditFiles::getId, ids)
            .eq(AuditFiles::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据编号与租户编号获取对象
   *
   * @param auditCode
   * @return
   */
  public List<AuditFiles> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(AuditFiles::getAuditCode, auditCode)
            .eq(AuditFiles::getTenantCode, tenantCode).list();
  }

  public boolean removeByAuditCode(List<String> auditCode) {
    if (CollectionUtils.isEmpty(auditCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(AuditFiles::getTenantCode, tenantCode)
            .in(AuditFiles::getAuditCode, auditCode).remove();
  }

  public AuditFiles findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(AuditFiles::getTenantCode,tenantCode)
        .in(AuditFiles::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(AuditFiles::getTenantCode,tenantCode)
        .in(AuditFiles::getId,ids)
        .remove();
  }
}