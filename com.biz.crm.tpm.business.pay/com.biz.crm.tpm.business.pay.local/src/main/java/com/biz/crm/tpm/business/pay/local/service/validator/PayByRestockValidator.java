package com.biz.crm.tpm.business.pay.local.service.validator;

import com.biz.crm.tpm.business.budget.sdk.validator.GenericValidator;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import org.apache.commons.lang3.Validate;

/**
 * 描述：</br>货补支付方式验证
 *
 * <AUTHOR>
 * @date 2022/6/20
 */
public class PayByRestockValidator implements GenericValidator {

  @Override
  public void handler(Object o) {
    if (!(o instanceof AuditDetailDto)) {
      return;
    }
    AuditDetailDto auditDetailDto = (AuditDetailDto) o;
    Validate.notEmpty(auditDetailDto.getAuditCustomers(), "活动细类【%s】为货补支付，需选择客户才能提交，请检查！", auditDetailDto.getCostTypeDetailName());
  }
}
