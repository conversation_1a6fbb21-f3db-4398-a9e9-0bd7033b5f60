package com.biz.crm.tpm.business.pay.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditCustomerDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditCustomerVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 费用明细客户;(tpm_audit_customer)表服务接口
 * <AUTHOR> Keller
 * @date : 2022-6-27
 */
public interface AuditCustomerService{

  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<AuditCustomerVo> findByConditions(Pageable pageable, AuditCustomerDto dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AuditCustomerVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<AuditCustomerVo> findByIds(Collection<String> ids);

  /**
   * 根绝业务编号auditDetailCode获取业务数据
   *
   * @param auditDetailCode
   * @return 单条数据
   */
  List<AuditCustomerVo> findByAuditDetailCode(String auditDetailCode);
  /**
   * 根绝业务编号auditDetailCode获取业务数据
   *
   * @param auditDetailCodes
   * @return 多条数据
   */
  List<AuditCustomerVo> findByAuditDetailCodes(Collection<String> auditDetailCodes);
  /**
   * 新增数据
   *
   * @param auditCustomerDto 实体对象
   * @return 新增结果
   */
  AuditCustomerVo create(AuditCustomerDto auditCustomerDto);
  /**
   * 批量新增
   * @param auditCustomerDtos
   * @return
   */
  List<AuditCustomerVo> createBatch(Collection<AuditCustomerDto> auditCustomerDtos);
  /**
   * 修改数据
   *
   * @param auditCustomerDto 实体对象
   * @return 修改结果
   */
  AuditCustomerVo update(AuditCustomerDto auditCustomerDto);
  /**
   * 批量修改据
   *
   * @param auditCustomerDtos 实体对象
   * @return 修改结果
   */
  List<AuditCustomerVo> updateBatch(Collection<AuditCustomerDto> auditCustomerDtos);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);

  /**
   * 根据核销编号删除h核销明细客户数据
   * @param auditCode
   */
  void deleteByAuditCode(String auditCode);

}
