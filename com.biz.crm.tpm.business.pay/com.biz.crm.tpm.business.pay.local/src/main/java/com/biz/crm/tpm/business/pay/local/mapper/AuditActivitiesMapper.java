package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditActivities;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditActivitiesDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditActivitiesVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 费用核销关联活动;(tpm_audit_activities)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Mapper
public interface AuditActivitiesMapper extends BaseMapper<AuditActivities> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto 动态查询条件
   * @return 分页对象列表
   */
  Page<AuditActivitiesVo> findByConditions(@Param("page") Page<AuditActivitiesVo> page , @Param("dto") AuditActivitiesDto dto);
}