<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.CreditOrderTicketMapper">

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderTicketVo">
        select t.*
        from tpm_credit_order_ticket t
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.productCode != null and dto.productCode != ''">
                <bind name="productCode" value="'%' + dto.productCode + '%'"/>
                and t.product_code like #{productCode}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                <bind name="productName" value="'%' + dto.productName + '%'"/>
                and t.product_name like #{productName}
            </if>
            <if test="dto.creditCode != null and dto.creditCode != ''">
                and t.credit_code = #{dto.creditCode}
            </if>
            <if test="dto.companyCode != null and dto.companyCode != ''">
                and t.company_code = #{dto.companyCode}
            </if>
            and be_adjusted = 'N'
        </where>
        order by t.create_time desc, t.id
    </select>

    <select id="findByDtoList" resultType="com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderTicketVo">
        select t.*
        from tpm_credit_order_ticket t
        inner join tpm_credit_order ta on t.credit_code=ta.credit_code
        <where>
            t.del_flag = '${@<EMAIL>()}'
            and ta.del_flag = '${@<EMAIL>()}'
            and t.ticket_status in ('un_invoiced','part_invoiced')
            <if test="dtoList != null and dtoList.size > 0">
                and
                <foreach collection="dtoList" item="item" open="(" close=")" separator="or">
                    t.customer_code=#{item.customerCode}
                    and t.years=#{item.years}
                </foreach>
            </if>
        </where>
        order by t.years desc, t.id
    </select>

    <select id="updateCreditOrderTicketStatus">
        <foreach item="dto" collection="dtoList" index="index"  open="" separator=";" close="">
            update tpm_credit_order_ticket
            set ticket_status = #{dto.ticketStatus},
                un_ticket_amount = #{dto.unTicketAmount}
            where credit_code = #{dto.creditCode}
            and line_number = #{dto.lineNumber}
        </foreach>
    </select>
</mapper>

