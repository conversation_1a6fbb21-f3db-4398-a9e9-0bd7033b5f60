package com.biz.crm.tpm.business.pay.local.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepaySendHecService;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayService;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "活动预付功能接口-新")
@RestController
@RequestMapping("/v1/pay/activityPrepay")
@Slf4j
public class ActivityPrepayController extends BusinessPageCacheController<ActivityPrepayDetailVo, ActivityPrepayDetailDto> {
    
    @Autowired(required = false)
    private ActivityPrepayService activityPrepayService;

    @Autowired(required = false)
    private ActivityPrepaySendHecService activityPrepaySendHecService;

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    @ApiOperation(value = "通过编号查询单条数据")
    @GetMapping("findByCode")
    public Result<ActivityPrepayVo> findByCode(@ApiParam(name = "code", value = "编号", required = true) String code) {
        try {
            ActivityPrepayVo activityPrepayVo = this.activityPrepayService.findByCode(code);
            return Result.ok(activityPrepayVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param activityPrepayDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @PostMapping
    public Result<?> create(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                            @ApiParam(name = "activityPrepayDto", value = "活动预付") @RequestBody ActivityPrepayDto activityPrepayDto) {
        try {
            this.activityPrepayService.create(activityPrepayDto, cacheKey, false);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改数据
     *
     * @param activityPrepayDto 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @PatchMapping
    public Result<?> update(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                            @ApiParam(name = "activityPrepayDto", value = "活动预付") @RequestBody ActivityPrepayDto activityPrepayDto) {
        try {
            this.activityPrepayService.update(activityPrepayDto, cacheKey, false);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 新增保存并提交
     *
     * @param activityPrepayDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增保存并提交")
    @PostMapping("submitCreate")
    public Result<?> submitCreate(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                  @ApiParam(name = "activityPrepayDto", value = "活动预付") @RequestBody ActivityPrepayDto activityPrepayDto) {
        try {
            this.activityPrepaySendHecService.submitCreate(activityPrepayDto, cacheKey);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑保存并提交
     *
     * @param activityPrepayDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "编辑保存并提交")
    @PostMapping("submitUpdate")
    public Result<?> submitUpdate(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                  @ApiParam(name = "activityPrepayDto", value = "活动预付") @RequestBody ActivityPrepayDto activityPrepayDto) {
        try {
            this.activityPrepaySendHecService.submitUpdate(activityPrepayDto, cacheKey);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     */
    @DeleteMapping
    @ApiOperation(value = "删除数据")
    public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
        try {
            return this.activityPrepaySendHecService.delete(idList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量提交
     *
     * @param idList 主键结合
     */
    @GetMapping("submitBatch")
    @ApiOperation(value = "批量提交")
    public Result<?> submitBatch(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
        try {
            return this.activityPrepaySendHecService.submitBatch(idList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "缓存明细导出")
    @GetMapping("findCachePageListImport")
    public Result<Page<ActivityPrepayDetailVo>> findCachePageListImport(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                 @ApiParam(name = "dto", value = "缓存键") ActivityPrepayDetailDto dto) {
        try {
            return this.findCachePageList(pageable,dto.getCacheKey(), dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @PostMapping("compensateOaCallback")
    @ApiOperation(value = "补偿OA机制")
    public Result compensateOaCallback(@RequestBody List<String> prepayCodes){
        for (String prepayCode : prepayCodes) {
            try {
                activityPrepayService.compensateOaCallback(prepayCode);
            }catch (Exception e){
                log.error(e.getMessage(),e);
            }
        }
        return Result.ok();
    }
}
