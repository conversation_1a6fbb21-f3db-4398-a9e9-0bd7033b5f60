package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.mapper.AuditCustomerMapper;
import com.biz.crm.tpm.business.pay.local.entity.AuditCustomer;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditCustomerDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditCustomerVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 费用明细客户;(tpm_audit_customer)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-27
 */
@Component
public class AuditCustomerRepository extends ServiceImpl<AuditCustomerMapper, AuditCustomer> {
  @Autowired
  private AuditCustomerMapper auditCustomerMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<AuditCustomerVo> findByConditions(Pageable pageable, AuditCustomerDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<AuditCustomerVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return auditCustomerMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<AuditCustomer>
   */
  public List<AuditCustomer> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(AuditCustomer::getId, ids)
            .eq(AuditCustomer::getTenantCode, tenantCode)
            .list();
  }


  /**
   * 根绝业务编号auditDetailCode获取业务数据
   *
   * @return 返单条数据
   */
  public List<AuditCustomer> findByAuditDetailCode(String auditDetailCode) {
    if (StringUtils.isBlank(auditDetailCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(AuditCustomer::getAuditDetailCode, auditDetailCode)
            .eq(AuditCustomer::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根绝业务编号auditDetailCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<AuditCustomer> findByAuditDetailCodes(Collection<String> auditDetailCodes) {
    if (CollectionUtils.isEmpty(auditDetailCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(AuditCustomer::getAuditDetailCode, auditDetailCodes)
            .eq(AuditCustomer::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 删除数据
   */
  public boolean deleteByAuditDetailCode(String auditDetailCode) {
    if (StringUtils.isBlank(auditDetailCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(AuditCustomer::getAuditDetailCode, auditDetailCode)
            .eq(AuditCustomer::getTenantCode, tenantCode)
            .remove();
  }

  public boolean deleteByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(AuditCustomer::getAuditCode, auditCode)
            .eq(AuditCustomer::getTenantCode, tenantCode)
            .remove();
  }

  public AuditCustomer findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(AuditCustomer::getTenantCode,tenantCode)
        .in(AuditCustomer::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Set<String> delIds, String tenantCode) {
    this.lambdaUpdate()
        .eq(AuditCustomer::getTenantCode,tenantCode)
        .in(AuditCustomer::getId,delIds)
        .remove();
  }
}
