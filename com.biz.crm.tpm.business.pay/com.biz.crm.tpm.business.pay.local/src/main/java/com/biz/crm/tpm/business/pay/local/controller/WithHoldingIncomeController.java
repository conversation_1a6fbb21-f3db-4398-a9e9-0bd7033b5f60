package com.biz.crm.tpm.business.pay.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingIncomeService;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/v1/pay/withHoldingIncome")
@Slf4j
public class WithHoldingIncomeController {

    @Autowired(required = false)
    private WithHoldingIncomeService withHoldingIncomeService;


    @PostMapping("/findByYear")
    @ApiOperation("根据年月查询管报收入")
    public Result<List<WithHoldingIncomeVo>> findByYears(@RequestBody List<String> year) {
        return Result.ok(this.withHoldingIncomeService.findByYear(year));
    }


    @ApiOperation(value = "查询管报收入列表")
    @PostMapping("findListByCondition")
    public Result<List<WithHoldingIncomeVo>> findListByCondition(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(withHoldingIncomeService.findListByCondition(dto));
    }


    @ApiOperation(value = "查询组织下所有的管报收入+年月")
    @PostMapping("findListByYearsAndChildrenOrgCodes")
    public Result<List<WithHoldingIncomeVo>> findListByYearsAndChildrenOrgCodes(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(withHoldingIncomeService.findListByYearsAndChildrenOrgCodes(dto));
    }
}
