package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.Prepay;
import com.biz.crm.tpm.business.pay.local.mapper.PrepayMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDto;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 活动预付;(tpm_prepay_activities)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-22
 */
@Component
public class PrepayRepository extends ServiceImpl<PrepayMapper, Prepay> {
  @Autowired
  private PrepayMapper prepayMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<PrepayVo> findByConditions(Pageable pageable, PrepayDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<PrepayVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return prepayMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<PrepayActivities>
   */
  public List<Prepay> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .in(Prepay::getId, ids)
        .eq(Prepay::getTenantCode, tenantCode)
        .eq(Prepay::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  @Override
  public boolean removeByIds(Collection<? extends Serializable> idList) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate().in(Prepay::getId, idList).
        eq(Prepay::getTenantCode, tenantCode).
        set(Prepay::getDelFlag, DelFlagStatusEnum.DELETE.getCode()).update();
  }

  /**
   * 根绝业务编号prepayCode获取业务数据
   *
   * @return 返单多条数据
   */
  public Prepay findByPrepayCode(String prepayCode) {
    if (StringUtils.isBlank(prepayCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .eq(Prepay::getPrepayCode, prepayCode)
        .eq(Prepay::getTenantCode, tenantCode)
        .eq(Prepay::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .one();
  }

  /**
   * 根绝业务编号prepayCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<Prepay> findByPrepayCodes(List<String> prepayCodes) {
    if (CollectionUtils.isEmpty(prepayCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .in(Prepay::getPrepayCode, prepayCodes)
        .eq(Prepay::getTenantCode, tenantCode)
        .eq(Prepay::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  /**
   * 删除数据
   *
   * @param prepayCode
   * @return
   */
  public boolean deleteByPrepayCode(String prepayCode) {
    if (StringUtils.isBlank(prepayCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
        .eq(Prepay::getPrepayCode, prepayCode)
        .eq(Prepay::getTenantCode, tenantCode)
        .set(Prepay::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  public Prepay findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(Prepay::getTenantCode,tenantCode)
        .in(Prepay::getId,id)
        .one();
  }
}
