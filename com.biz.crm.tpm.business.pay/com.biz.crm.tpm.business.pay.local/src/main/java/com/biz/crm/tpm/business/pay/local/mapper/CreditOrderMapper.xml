<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.CreditOrderMapper">
    <select id="findTotalByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderVo">
        select ifnull(sum(ifnull(t.amount, 0)), 0) amount from tpm_credit_order t
        <where>
            <if test="dto.creditCode != null and dto.creditCode != '' ">
                and t.credit_code = #{dto.creditCode}
            </if>
            <if test="dto.cashCode != null and dto.cashCode != '' ">
                and t.cash_code = #{dto.cashCode}
            </if>
            <if test="dto.orderType != null and dto.orderType != '' ">
                and t.order_type = #{dto.orderType}
            </if>

            <if test="dto.btNo != null and dto.btNo != '' ">
                and t.bt_no = #{dto.btNo}
            </if>
            <if test="dto.companyCode != null and dto.companyCode != '' ">
                and t.company_code = #{dto.companyCode}
            </if>
            <if test="dto.departmentOneCode != null and dto.departmentOneCode != '' ">
                and t.department_one_code = #{dto.departmentOneCode}
            </if>
            <if test="dto.departmentOneName != null and dto.departmentOneName != '' ">
                <bind name="departmentOneName" value=" '%' + dto.departmentOneName + '%' "/>
                and t.department_one_name like #{departmentOneName}
            </if>
            <if test="dto.departmentTwoCode != null and dto.departmentTwoCode != '' ">
                and t.department_two_code = #{dto.departmentTwoCode}
            </if>
            <if test="dto.departmentTwoName != null and dto.departmentTwoName != '' ">
                <bind name="departmentTwoName" value=" '%' + dto.departmentTwoName + '%' "/>
                and t.department_two_name like #{departmentTwoName}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != '' ">
                and t.customer_code = #{dto.customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != '' ">
                <bind name="customerName" value=" '%' + dto.customerName + '%' "/>
                and t.customer_name like #{customerName}
            </if>
            <if test="dto.auditCode != null and dto.auditCode != '' ">
                and t.audit_code = #{dto.auditCode}
            </if>
            <if test="dto.auditName != null and dto.auditName != '' ">
                <bind name="auditName" value=" '%' + dto.auditName + '%' "/>
                and t.audit_name like #{auditName}
            </if>
            <if test="dto.pushStatus != null and dto.pushStatus != '' ">
                and t.push_status = #{dto.pushStatus}
            </if>
            <if test="dto.accountDate != null and dto.accountDate != '' ">
                and t.account_date = #{dto.accountDate}
            </if>
            <if test="dto.accountDateStart != null and dto.accountDateStart != '' ">
                and t.account_date &gt;= #{dto.accountDateStart}
            </if>
            <if test="dto.accountDateEnd != null and dto.accountDateEnd != '' ">
                and t.account_date &lt;= #{dto.accountDateEnd}
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
        order by t.create_time desc,t.id desc
    </select>
</mapper>

