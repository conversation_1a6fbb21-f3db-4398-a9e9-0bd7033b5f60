package com.biz.crm.tpm.business.pay.local.helper;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.dms.business.order.common.sdk.enums.ItemTypeEnum;
import com.biz.crm.dms.business.order.common.sdk.enums.OrderTypeEnum;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialCostVoService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCustomerGainsBuilder;
import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectGainsAndLossesVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.enums.SecondCategoryEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.FormulaGetValueComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/24 19:54
 */
@Slf4j
public class MarketingPlanCustomerGainsWithHoldingAbstractBuilder extends MarketingPlanCustomerGainsBuilder<List<String>, String> {

    private static final String column = ":";

    private CostBudgetIncomeService costBudgetIncomeService;
    private CostBudgetVoService costBudgetVoService;
    private CustomerVoService customerVoService;
    private MaterialCostVoService materialCostVoService;
    private CostTypeCategoryVoService costTypeCategoryVoService;
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;
    private MaterialVoService materialVoService;
    private DictDataVoService dictDataVoService;

    private OrgVoService orgVoService;

    private PublicShareRatioService publicShareRatioService;

    private FormulaGetValueComponent formulaGetValueComponent;

    private ProductPhaseVoService productPhaseVoService;

    private String year = null;
    private List<String> orgCodes = Lists.newArrayList();

    private List<String> lastMonthCustomer = Lists.newArrayList();
    private List<String> currentMonthCustomer = Lists.newArrayList();

    private Map<String, BigDecimal> incomeDmsMap = new HashMap<>();

    private Map<String, BigDecimal> costDmsMap = new HashMap<>();

    private Map<String, BigDecimal> giftDmsMap = new HashMap<>();

    private Map<String, BigDecimal> compensateGoodsCostMap = new HashMap<>();

    private Map<String, BigDecimal> productTransferMap = new HashMap<>();

    private Map<String, BigDecimal> peripheryTransferMap = new HashMap<>();

    private Map<String, BigDecimal> withHoldingMap;

    private Map<String, BigDecimal> planCaseMap;

    private Map<String, List<String>> costTypeCategoryMap;

    private Map<String, String> dictMap;

    private List<MarketingPlanCase> caseList = Lists.newArrayList();

    private List<WithHoldingVo> withHoldingList = Lists.newArrayList();

    private List<RegionCollectGainsAndLossesVo> dataList = Lists.newArrayList();

    private List<DmsWarehouseOrderDetailVo> orderDetailList;

    public static MarketingPlanCustomerGainsWithHoldingAbstractBuilder builder(ApplicationContext context, List<RegionCollectGainsAndLossesVo> dataList,
                                                                               List<MarketingPlanCase> caseList, List<WithHoldingVo> withHoldingList, List<String> orgCodes,
                                                                               List<String> lastMonthCustomer, List<String> currentMonthCustomer) {
        MarketingPlanCustomerGainsWithHoldingAbstractBuilder builder = new MarketingPlanCustomerGainsWithHoldingAbstractBuilder();
        builder.costBudgetIncomeService = context.getBean(CostBudgetIncomeService.class);
        builder.costBudgetVoService = context.getBean(CostBudgetVoService.class);
        builder.customerVoService = context.getBean(CustomerVoService.class);
        builder.materialCostVoService = context.getBean(MaterialCostVoService.class);
        builder.materialVoService = context.getBean(MaterialVoService.class);
        builder.costTypeCategoryVoService = context.getBean(CostTypeCategoryVoService.class);
        builder.dmsWarehouseOrderDetailVoService = context.getBean(DmsWarehouseOrderDetailVoService.class);
        builder.orgVoService = context.getBean(OrgVoService.class);
        builder.dictDataVoService = context.getBean(DictDataVoService.class);
        builder.dataList = dataList;
        builder.publicShareRatioService = context.getBean(PublicShareRatioService.class);
        builder.formulaGetValueComponent = context.getBean(FormulaGetValueComponent.class);
        builder.caseList = caseList;
        builder.withHoldingList = withHoldingList;
        builder.orgCodes = orgCodes;
        builder.lastMonthCustomer = lastMonthCustomer;
        builder.currentMonthCustomer = currentMonthCustomer;
        builder.productPhaseVoService = context.getBean(ProductPhaseVoService.class);
        return builder;
    }

    @Override
    public MarketingPlanCustomerGainsBuilder loadInitData() {
        List<String> customerCodes = dataList.stream().map(RegionCollectGainsAndLossesVo::getCustomerCode).distinct().collect(Collectors.toList());
        log.info("loadInitData-customerCodes: {}", customerCodes);
        //1.获取预算收入-客户+成本中心+年月
        List<CostBudgetIncomeVo> incomeVos = costBudgetIncomeService.findListByCustomerCodesAndOrgCodesAndYears(customerCodes, null, orgCodes, years);
        incomeVos = incomeVos.stream().filter(x -> x.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode())).collect(Collectors.toList());
        List<String> itemCodes = incomeVos.stream().map(CostBudgetIncomeVo::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
        Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> null == v.getTaxRate() ? BigDecimal.ZERO : v.getTaxRate()));
        Map<String, BigDecimal> incomeMap = incomeVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()) &&
                        ObjectUtils.isNotEmpty(x.getYearMonthLy()) &&
                        ObjectUtils.isNotEmpty(x.getIncomeAmount()))
                .collect(Collectors.groupingBy(x -> x.getCustomerCode() + column + x.getYearMonthLy(),
                        Collectors.mapping(k -> Optional.ofNullable(k.getIncomeAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(k.getItemCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //2.获取预算费用
        List<CostBudgetVo> budgetVos = costBudgetVoService.findListByCustomerCodesAndCostCenterCodesAndYears(customerCodes, null, orgCodes, years);
        Map<String, BigDecimal> budgetMap = budgetVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()) &&
                        ObjectUtils.isNotEmpty(x.getYearMonthLy()) &&
                        ObjectUtils.isNotEmpty(x.getInitialAmount()))
                .collect(Collectors.groupingBy(x -> x.getCustomerCode() + column + x.getYearMonthLy(),
                        Collectors.mapping(k -> k.getAdjustBalanceAmount().divide(new BigDecimal(1).add(null == k.getTaxRate()? new BigDecimal(0) : k.getTaxRate()), 2,BigDecimal.ROUND_HALF_UP), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
        dto.setCustomerCodes(customerCodes);
        String startDate = years + "-01";
        String endDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(startDate, "yyyy-MM-dd")), "yyyy-MM-dd");
        dto.setSearchStartTime(startDate);
        dto.setSearchEndTime(endDate);
        dto.setBeWithHolding(BooleanEnum.TRUE.getCapital());
        orderDetailList = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
        List<String> productItemTypeList = Arrays.asList(ItemTypeEnum.NORMAL_GOODS.getDictCode(),
                ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
                ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
                ItemTypeEnum.COMPENSATED_GOODS.getDictCode(),
                ItemTypeEnum.TAIL_GOODS.getDictCode());
        List<String> peripheryItemTypeList = Arrays.asList(ItemTypeEnum.MATERIAL_GOODS.getDictCode());
        List<String> giftItemTypeList = Arrays.asList(ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
                ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
                ItemTypeEnum.TAIL_GOODS.getDictCode());
        if (CollectionUtils.isNotEmpty(orderDetailList)) {
            //过滤免费订单
            orderDetailList = orderDetailList.stream().filter(e -> !Lists.newArrayList(OrderTypeEnum.FREE.getDictCode(),
                    OrderTypeEnum.FREE.getReturnCode(),
                    OrderTypeEnum.CONSIGN.getDictCode(),
                    OrderTypeEnum.CONSIGN.getReturnCode()).contains(e.getOrderType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderDetailList)) {
                List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SAP_FACTORY_TYPE);
                dictMap = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
                //查询物料成本
                Set<String> materialCodes = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getGoodsCode())).filter(Objects::nonNull)
                        .map(DmsWarehouseOrderDetailVo::getGoodsCode).collect(Collectors.toSet());
                Set<String> companyCodeSet = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode())).filter(Objects::nonNull)
                        .map(DmsWarehouseOrderDetailVo::getCompanyCode).collect(Collectors.toSet());
                MaterialSearchDto searchDto = new MaterialSearchDto();
                searchDto.setMaterialCodeSet(materialCodes);
                searchDto.setCompanyCodeSet(companyCodeSet);
                List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
                Map<String, Map<String, BigDecimal>> materialMap = materialVoList.stream().collect(Collectors.groupingBy(x -> x.getMaterialCode(), Collectors.toMap(x -> x.getFactoryTypeCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO), (a, b) -> a)));
                Map<String, BigDecimal> materialTaxRateMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getTaxRate()).orElse(BigDecimal.ZERO), (a, b) -> a));

                String year = years.split("-")[0];
                //查询物流成本
                List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialCodes, new ArrayList<>(companyCodeSet), year);
                Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                        x.getCompanyCode(), Function.identity()));
                customerCodes.forEach(k -> {
                    //实际收入(发货减退货、发货金额/（1+税率）)
                    BigDecimal delivery = orderDetailList.stream().filter(e -> e.getTotalAmount() != null && k.equals(e.getCustomerCode()) && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                            .filter(Objects::nonNull)
                            .map(e -> e.getTotalAmount().divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal stored = orderDetailList.stream().filter(e -> e.getTotalAmount() != null && k.equals(e.getCustomerCode()) && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(e.getWarehouseOrderType()))
                            .filter(Objects::nonNull)
                            .map(e -> e.getTotalAmount().divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal subtract = (Optional.ofNullable(delivery).orElse(BigDecimal.ZERO)).subtract((Optional.ofNullable(stored).orElse(BigDecimal.ZERO)));
                    incomeDmsMap.put(k + column + years, subtract);
                    //生产成本(对应物料主数据的单价*(实际发货数量-退货数量))含搭赠 + 货补
                    Map<String, BigDecimal> goodsMap = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && k.equals(e.getCustomerCode()) &&
                                    (ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(e.getItemType()) || giftItemTypeList.contains(e.getItemType()) || ItemTypeEnum.COMPENSATED_GOODS.getDictCode().equals(e.getItemType()))
                            && (DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()) || DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(e.getWarehouseOrderType()))).filter(Objects::nonNull)
                            .collect(Collectors.groupingBy(e -> e.getGoodsCode() + column + e.getCompanyCode(),
                                    Collectors.mapping(e -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(e.getWarehouseOrderType()) ? e.getRealOutNum().negate() : e.getRealOutNum(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                    BigDecimal goodsCost = BigDecimal.ZERO;
                    for (Map.Entry<String, BigDecimal> entry : goodsMap.entrySet()) {
                        String[] split = entry.getKey().split(column);
                        Map<String, BigDecimal> factoryMap = materialMap.getOrDefault(split[0], new HashMap<>());
                        BigDecimal price = factoryMap.getOrDefault(dictMap.get(split[1]), BigDecimal.ZERO);
                        goodsCost = goodsCost.add(entry.getValue().multiply(price));
                    }
                    costDmsMap.put(k + column + years, goodsCost);
                    //搭赠费用(销售价*实际发货数量)
                    BigDecimal giftCost = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && k.equals(e.getCustomerCode()) && giftItemTypeList.contains(e.getItemType())
                            && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType())).filter(Objects::nonNull)
                            .map(e -> e.getPresetUnitPrice().multiply(e.getRealOutNum()).divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    giftDmsMap.put(k + column + years, giftCost);
                    // 货补费用
                    BigDecimal compensateGoodsCost = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && k.equals(e.getCustomerCode()) && ItemTypeEnum.COMPENSATED_GOODS.getDictCode().equals(e.getItemType())
                                    && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType())).filter(Objects::nonNull)
                            .map(e -> e.getPresetUnitPrice().multiply(e.getRealOutNum()).divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    compensateGoodsCostMap.put(k + column + years, compensateGoodsCost);
                    log.info("loadInitData-compensateGoodsCostMap: {}", JSON.toJSONString(compensateGoodsCostMap));
                    //运输费用(产品运输费用+周边运输费用)
                    BigDecimal productExpressFee = orderDetailList.stream().filter(e -> e.getExpressFee() != null && k.equals(e.getCustomerCode()) &&
                            productItemTypeList.contains(e.getItemType())
                            && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                            .map(e -> {
                                BigDecimal price = materialCostMap.getOrDefault(e.getMaterialCode() + column + e.getCompanyCode(), new MaterialCostVo()).getLargeLogisticsPrice();
                                return (Optional.ofNullable(price).orElse(BigDecimal.ZERO).multiply(e.getRealOutNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                            })
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal peripheryExpressFee = orderDetailList.stream().filter(e -> e.getExpressFee() != null && k.equals(e.getCustomerCode()) &&
                            peripheryItemTypeList.contains(e.getItemType())
                            && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                            .map(e -> {
                                BigDecimal price = materialCostMap.getOrDefault(e.getMaterialCode() + column + e.getCompanyCode(), new MaterialCostVo()).getLargeLogisticsPrice();
                                return (Optional.ofNullable(price).orElse(BigDecimal.ZERO).multiply(e.getRealOutNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                            })
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    productTransferMap.put(k + column + years, productExpressFee);
                    peripheryTransferMap.put(k + column + years, peripheryExpressFee);
                });
            }
        }
        //4.计提金额
        withHoldingMap = withHoldingList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode() + column + x.getBelongDepartmentCode() + column + years,
                Collectors.mapping(x->Optional.ofNullable(x.getActualReportAmount()).orElse(BigDecimal.ZERO),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //5.兑付金额
        planCaseMap = caseList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode() + column + x.getBelongDepartmentCode() + column + years,
                Collectors.mapping(x->Optional.ofNullable(x.getLineNoTaxApplyAmount()).orElse(BigDecimal.ZERO),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //6.查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        this.costTypeCategoryMap = costTypeCategoryVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (CustomerGainsAndLossesVo data : dataList) {
            //POS分摊比例
            String[] keys = data.getIndexKey().split(":");
            BigDecimal rate = null;
            if (Boolean.TRUE.equals(data.getBeSplitting())) {
                Map<String, BigDecimal> rateMap = formulaGetValueComponent.splittingRateHandleWithHolding(Collections.singletonList(keys[0]), years, Collections.singletonList(keys[1]), FormulaGetValueComponent.AMOUNT,
                        lastMonthCustomer, currentMonthCustomer);
                rate = rateMap.get(keys[0]);
            }
            data.setRate(rate);
            String indexKey = data.getIndexKeyExceptOrg();
            //预算收入
            data.setBudgetIncome(incomeMap.get(indexKey));
            //搭赠费用
            BigDecimal giftCost = giftDmsMap.getOrDefault(indexKey, BigDecimal.ZERO);
            data.setGiftCost(rate == null ? giftCost : giftCost.multiply(rate));

            // 货补费用
            BigDecimal compensateGoodsCost = compensateGoodsCostMap.getOrDefault(indexKey, BigDecimal.ZERO);

            //规划收入（加搭赠）+ 货补
            BigDecimal planIncome = incomeDmsMap.getOrDefault(indexKey, BigDecimal.ZERO);
            data.setPlanIncome(rate == null ? planIncome.add(data.getGiftCost()).add(compensateGoodsCost) : (planIncome.add(data.getGiftCost()).add(compensateGoodsCost)).multiply(rate));
            //收入达成率
            data.setIncomeAchieveRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getBudgetIncome()) && ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) != 0) {
                //规划收入/预算收入
                data.setIncomeAchieveRatio(data.getPlanIncome().divide(data.getBudgetIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            //预算总额
            data.setBudgetTotalAmount(budgetMap.get(indexKey));
            //预算费率
            data.setBudgetRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getBudgetTotalAmount()) && ObjectUtils.isNotEmpty(data.getBudgetIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) != 0) {
                //预算总额/预算收入
                data.setBudgetRatio(data.getBudgetTotalAmount().divide(data.getBudgetIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            //产品运输费用
            BigDecimal productTransfer = productTransferMap.getOrDefault(indexKey, BigDecimal.ZERO);
            BigDecimal peripheryTransfer = peripheryTransferMap.getOrDefault(indexKey, BigDecimal.ZERO);
            data.setProductTransportCost(rate == null ? productTransfer : productTransfer.multiply(rate));
            //周边运输费用
            data.setPeripheryTransportCost(rate == null ? peripheryTransfer : peripheryTransfer.multiply(rate));
            //规划总额（营销费用(计提+兑付)+运输费用+分摊费用+搭赠）
            data.setPlanTotalAmount(withHoldingMap.getOrDefault(data.getIndexKey(), BigDecimal.ZERO)
                    .add(planCaseMap.getOrDefault(data.getIndexKey(), BigDecimal.ZERO))
                    .add(data.getProductTransportCost().add(data.getPeripheryTransportCost()))
                    .add(data.getGiftCost()));
            //规划费率
            data.setPlanRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && ObjectUtils.isNotEmpty(data.getPlanTotalAmount()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                //规划总额/规划收入
                data.setPlanRatio(data.getPlanTotalAmount().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            //费率偏差 = 规划费率-预算费率
            data.setRatioDeviation(data.getPlanRatio().subtract(data.getBudgetRatio()));
        }
        log.info("loadInitData-dataList: {}", JSON.toJSONString(dataList));
        return this;
    }

    /**
     * 利润率
     *
     * @return
     */
    @Override
    public MarketingPlanCustomerGainsBuilder calProfitRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            String indexKey = data.getIndexKeyExceptOrg();
            //生产成本
            BigDecimal productionCosts = costDmsMap.getOrDefault(indexKey, BigDecimal.ZERO);
            data.setProductionCosts((data.getRate() == null ? productionCosts : productionCosts.multiply(data.getRate())));
            //运输费用
            BigDecimal transportCost = data.getPeripheryTransportCost().add(data.getProductTransportCost());
            //营销费用
            data.setMarketingCost(withHoldingMap.getOrDefault(data.getIndexKey(), BigDecimal.ZERO)
                    .add(planCaseMap.getOrDefault(data.getIndexKey(), BigDecimal.ZERO))
                    .add(data.getGiftCost()));
            //规划利润额=规划收入-生产成本-产品运输费用-周边运输费用-营销费用-公摊费用
            data.setPlanProfitMargin(data.getPlanIncome().subtract(data.getProductionCosts()).subtract(transportCost).subtract(data.getMarketingCost())
                    .subtract(data.getPublicShareRatio().multiply(data.getPlanIncome())));
            //利润率=规划利润额/规划收入
            data.setProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setProfitRatio(data.getPlanProfitMargin().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    /**
     * 毛利率
     *
     * @return
     */
    @Override
    public MarketingPlanCustomerGainsBuilder calGrossProfitRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //规划毛利额=规划收入-生产成本
            data.setPlanGrossProfitMargin(data.getPlanIncome().subtract(data.getProductionCosts()));
            //毛利率=规划毛利额/规划收入
            data.setGrossProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setGrossProfitRatio(data.getPlanGrossProfitMargin().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public MarketingPlanCustomerGainsBuilder calLogisticsRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //物流费率=(产品运输费用+周边运输费用)/规划收入
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setLogisticsRatio(data.getProductTransportCost().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    /**
     * 营销费率
     *
     * @return
     */
    @Override
    public MarketingPlanCustomerGainsBuilder calMarketingRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //营销费率=营销费用/规划收入
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setMarketingRatio(data.getMarketingCost().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    /**
     * 公摊费率
     *
     * @return
     */
    @Override
    public MarketingPlanCustomerGainsBuilder calPublicShareRatio() {
        List<String> orgCodesAll = orgCodes;
        for (CustomerGainsAndLossesVo vo : dataList) {
            String[] keys = vo.getIndexKey().split(":");
            String orgCode = keys[1];
            List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(orgCode);
            List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            BigDecimal ratio = publicShareRatioService.findRatioByCondition(orgCodes, years);
            //如果等于0  则取方案上的部门上级所有去找
            if (ratio.compareTo(BigDecimal.ZERO) == 0) {
                List<OrgVo> list = orgVoService.findAllParentByOrgCodes(Lists.newArrayList(orgCodesAll));
                List<String> list1 = list.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                ratio = publicShareRatioService.findRatioByCondition(Lists.newArrayList(list1), years);
            }
            vo.setPublicShareRatio(ratio);
            //公摊费用
            vo.setPublicShare(vo.getPlanIncome().multiply(Optional.ofNullable(ratio).orElse(BigDecimal.ZERO)));
            //加上
            vo.setPlanTotalAmount(vo.getPlanTotalAmount().add(vo.getPublicShare()));
        }
        return this;
    }

    /**
     * 二级费用大类
     *
     * @return
     */
    @Override
    public MarketingPlanCustomerGainsBuilder calSecondCategory() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //二级费用大类计算
            Map<String, BigDecimal> secondCategoryMap = Maps.newHashMap();
            Map<String, BigDecimal> applyAmountMap = Maps.newHashMap();
            for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
                BigDecimal cashAmount = caseList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()) &&
                        (x.getCustomerCode() + column + x.getYears()).equals(data.getIndexKeyExceptOrg()))
                        .map(x -> Optional.ofNullable(x.getCashAmount()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal withHoldingAmount = withHoldingList.stream().filter(x -> entry.getValue().contains(x.getCostTypeCategoryCode()) &&
                        (x.getCustomerCode() + column + x.getYears()).equals(data.getIndexKeyExceptOrg()))
                        .map(x -> Optional.ofNullable(x.getActualReportAmount()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal ratio = BigDecimal.ZERO;
                if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                    ratio = (cashAmount.add(withHoldingAmount)).divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN);
                }
                secondCategoryMap.put(entry.getKey(), ratio);

                applyAmountMap.put(entry.getKey(), cashAmount.add(withHoldingAmount));
            }
            for (Map.Entry<String, BigDecimal> entry : secondCategoryMap.entrySet()) {
                BigDecimal value = entry.getValue();
                if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
                    data.setContract(value);
                    data.setContractAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
                    data.setGeneralization(value);
                    data.setGeneralizationAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
                    data.setPromotion(value);
                    data.setPromotionAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
                    data.setSalesReward(value);
                    data.setSalesRewardAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.callback.getName().equals(entry.getKey())) {
                    data.setCallback(value);
                    data.setCallbackAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.display.getName().equals(entry.getKey())) {
                    data.setDisplay(value);
                    data.setDisplayAmount(applyAmountMap.get(entry.getKey()));
                } else if (SecondCategoryEnum.disseminateS.getName().equals(entry.getKey())) {
                    data.setDisseminate(value);
                    data.setDisseminateAmount(applyAmountMap.get(entry.getKey()));
                }

            }
            data.setSecondCostCategoryMap(secondCategoryMap);
        }
        //转换费率
        transRatio();
        return this;
    }

    private void transRatio() {
        for (CustomerGainsAndLossesVo vo : dataList) {

            if (ObjectUtils.isNotEmpty(vo.getIncomeAchieveRatio())) {
                vo.setIncomeAchieveRatioStr(vo.getIncomeAchieveRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetRatio())) {
                vo.setBudgetRatioStr(vo.getBudgetRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanRatio())) {
                vo.setPlanRatioStr(vo.getPlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getRatioDeviation())) {
                vo.setRatioDeviationStr(vo.getRatioDeviation().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getProfitRatio())) {
                vo.setProfitRatioStr(vo.getProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGrossProfitRatio())) {
                vo.setGrossProfitRatioStr(vo.getGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getLogisticsRatio())) {
                vo.setLogisticsRatioStr(vo.getLogisticsRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getMarketingRatio())) {
                vo.setMarketingRatioStr(vo.getMarketingRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPublicShareRatio())) {
                vo.setPublicShareRatioStr(vo.getPublicShareRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPromotion())) {
                vo.setPromotionStr(vo.getPromotion().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getCallback())) {
                vo.setCallbackStr(vo.getCallback().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisplay())) {
                vo.setDisplayStr(vo.getDisplay().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGeneralization())) {
                vo.setGeneralizationStr(vo.getGeneralization().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisseminate())) {
                vo.setDisseminateStr(vo.getDisseminate().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getSalesReward())) {
                vo.setSalesRewardStr(vo.getSalesReward().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getContract())) {
                vo.setContractStr(vo.getContract().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
        }
    }
}
