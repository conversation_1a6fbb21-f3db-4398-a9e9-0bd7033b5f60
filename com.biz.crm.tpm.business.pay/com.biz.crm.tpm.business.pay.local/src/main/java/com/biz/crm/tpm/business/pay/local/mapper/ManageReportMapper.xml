<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.ManageReportMapper">

    <select id="findByCondition" resultType="com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo">
        select
        t.*
        from tpm_manage_report t
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != '' ">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.customerCode !=null and dto.customerCode != '' ">
                and t.customer_code = #{dto.customerCode}
            </if>
            <if test="dto.itemName !=null and dto.itemName != '' ">
                and t.item_name = #{dto.itemName}
            </if>
            <if test="dto.productCode !=null and dto.productCode != '' ">
                and t.product_code = #{dto.productCode}
            </if>
            <if test="dto.yearMonthLy !=null and dto.yearMonthLy != '' ">
                and t.year_month_ly = #{dto.yearMonthLy}
            </if>
            <if test="dto.deptCodeSet != null and dto.deptCodeSet.size > 0">
                and t.department_code in
                <foreach item="item" collection="dto.deptCodeSet" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="dto.yearMonthLySet != null and dto.yearMonthLySet.size > 0">
                and t.year_month_ly in
                <foreach item="item" collection="dto.yearMonthLySet" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
    </select>

    <select id="findListByCondition" resultType="com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo">
        select
        t.*
        from tpm_manage_report t
        <where>
            <if test="dto.years !=null and dto.years != '' ">
                and t.year_month_ly = #{dto.years}
            </if>
            <if test="dto.yearsList !=null and dto.yearsList.size()>0 ">
                and t.year_month_ly in
                <foreach collection="dto.yearsList" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orgCodes != null and dto.orgCodes.size()>0">
                and t.department_code in
                <foreach collection="dto.orgCodes" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.customerCodes != null and dto.customerCodes.size()>0">
                and t.customer_code in
                <foreach collection="dto.customerCodes" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="findByYearAndCustomerCodes" resultType="com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo">
        select
        t.*
        from tpm_manage_report t
        <where>
            <if test="yearMonth !=null and yearMonth != '' ">
                and t.year_month_ly = #{yearMonth}
            </if>
            <if test="customerCodes !=null and customerCodes.size()>0">
                and t.customer_code in
                <foreach collection="customerCodes" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>

