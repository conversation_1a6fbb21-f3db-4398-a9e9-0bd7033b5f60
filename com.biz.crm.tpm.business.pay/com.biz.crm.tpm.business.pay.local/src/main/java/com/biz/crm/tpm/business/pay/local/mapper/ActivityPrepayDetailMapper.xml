<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayDetailMapper">

    <select id="findCalList" resultType="com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail">
        select
            t.id,
            t.relate_carry_amount
        from tpm_activity_prepay_detail t
        inner join tpm_activity_prepay t1 on t.prepay_code = t1.prepay_code
        where t.del_flag = '${@<EMAIL>()}'
          and t1.del_flag = '${@<EMAIL>()}'
          and t1.tenant_code = #{vo.tenantCode}
          and t1.status = #{vo.status}
          and t1.company_code = #{vo.companyCode}
          and t1.payee_code = #{vo.payeeCode}
          and t.relate_carry_amount is not null
          and t.relate_carry_amount > 0
          <if test="vo.prepayCode != null and vo.prepayCode != ''">
              and t1.prepay_code != #{vo.prepayCode}
          </if>
    </select>

    <select id="findBySchemeDetailCodesBeCommit" resultType="com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail">
        select t.*
        from tpm_activity_prepay_detail t
        inner join tpm_activity_prepay ta on t.prepay_code=ta.prepay_code
        <where>
            ta.status != '3'
            AND t.scheme_detail_code in
            <foreach collection="codes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </where>
    </select>

</mapper>

