package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.DeliveryReplenishmentPoolDetail;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发货费用报表(DeliveryReplenishmentPoolDetail)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-08-14 16:58:57
 */
public interface DeliveryReplenishmentPoolDetailMapper extends BaseMapper<DeliveryReplenishmentPoolDetail> {

    List<DeliveryReplenishmentPoolDetail> findBackUnWriteOff(@Param("month") String month);
}

