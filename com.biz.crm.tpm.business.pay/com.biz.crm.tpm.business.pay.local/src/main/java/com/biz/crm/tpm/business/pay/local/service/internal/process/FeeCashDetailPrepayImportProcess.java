package com.biz.crm.tpm.business.pay.local.service.internal.process;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.tpm.business.pay.local.service.internal.FeeCashDetailPageCacheHelper;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashDetailService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailPrepayImportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FeeCashDetailPrepayImportProcess implements ImportProcess<FeeCashDetailPrepayImportVo> {

    @Autowired(required = false)
    private FeeCashDetailService feeCashDetailService;
    @Autowired(required = false)
    private FeeCashDetailPageCacheHelper feeCashDetailPageCacheHelper;


    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, FeeCashDetailPrepayImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, FeeCashDetailPrepayImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");

        for (Map.Entry<Integer, FeeCashDetailPrepayImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            FeeCashDetailPrepayImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getAuditDetailCode()), "结案明细编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getThisCashAmountStr()), "本次预付金额，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getRemark()), "预付描述，不能为空！");

            try {
                new BigDecimal(vo.getThisCashAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "本次预付金额类型转换失败！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, FeeCashDetailPrepayImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();

        String cacheKey = (String) params.get("cacheKey");

        List<FeeCashDetailDto> cacheList = feeCashDetailService.findCacheList(cacheKey);
        Validate.notEmpty(cacheList, "未获取到任何预付明细缓存");

        Map<String, FeeCashDetailDto> cashDetailMap = cacheList.stream().collect(Collectors.toMap(FeeCashDetailDto::getAuditDetailCode, Function.identity(), (a, b) -> a));

        List<FeeCashDetailDto> updateCacheList = new ArrayList<>();
        for (Map.Entry<Integer, FeeCashDetailPrepayImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            FeeCashDetailPrepayImportVo vo = row.getValue();

            FeeCashDetailDto cashDetailDto = cashDetailMap.get(vo.getAuditDetailCode());
            if (cashDetailDto != null) {
                cashDetailDto.setThisCashAmount(new BigDecimal(vo.getThisCashAmountStr()));
                cashDetailDto.setRemark(vo.getRemark());
                updateCacheList.add(cashDetailDto);
            } else {
                this.validateIsTrue(false, "未找到结案明细编码对应的预付明细！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        feeCashDetailPageCacheHelper.importNewItem(cacheKey, updateCacheList);

        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<FeeCashDetailPrepayImportVo> findCrmExcelVoClass() {
        return FeeCashDetailPrepayImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "FEE_CASH_DETAIL_PREPAY_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "预付明细导入模板-费用兑付";
    }
}
