<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.MarketingAuditMapper">

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditVo">
        select distinct ta.*
        from tpm_marketing_audit_detail t
        inner join tpm_marketing_audit ta on t.audit_code=ta.audit_code
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
        </where>
        order by t.create_time, t.id
    </select>

    <select id="findCommit" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditVo">
        select distinct ta.*
        from tpm_marketing_audit_detail t
        inner join tpm_marketing_audit ta on t.audit_code=ta.audit_code
        <where>
            ta.status='2'
            and t.years=#{yearMonthLy}
        </where>
        order by ta.create_time, ta.id
    </select>

    <select id="findListByApprovedAndFullAudit" resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        SELECT CASE
                   WHEN SUM(CASE WHEN be_full_audit = 'Y' THEN 1 ELSE 0 END) > 0 THEN SUM(audit_amount)
                   ELSE 0
                   END AS audit_amount,
               scheme_detail_code
        FROM tpm_marketing_audit_detail a
                 LEFT JOIN tpm_marketing_audit b on a.audit_code = b.audit_code
        where b.status = '3'
          and a.audit_code = #{code}
        group by a.scheme_detail_code
    </select>
</mapper>

