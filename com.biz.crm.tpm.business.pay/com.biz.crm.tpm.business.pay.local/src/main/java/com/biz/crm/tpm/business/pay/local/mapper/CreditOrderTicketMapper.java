package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.CreditOrderTicket;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.CreditOrderTicketDto;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderTicketVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 贷项订单票扣明细(CreditOrderTicket)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-08-01 16:40:50
 */
public interface CreditOrderTicketMapper extends BaseMapper<CreditOrderTicket> {
    
    /**
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<CreditOrderTicketVo> findByConditions(@Param("page") Page<CreditOrderTicketVo> page , @Param("dto") CreditOrderTicketDto dto);

    /**
     * 按条件查询票扣明细
     *
     * @param dtoList
     * @return
     */
    List<CreditOrderTicketVo> findByDtoList(@Param("dtoList") List<CreditOrderTicketVo> dtoList);

    /**
     * 批量更新票扣状态
     *
     * @param dtoList
     */
    void updateCreditOrderTicketStatus(@Param("dtoList") List<CreditOrderTicketVo> dtoList);
}

