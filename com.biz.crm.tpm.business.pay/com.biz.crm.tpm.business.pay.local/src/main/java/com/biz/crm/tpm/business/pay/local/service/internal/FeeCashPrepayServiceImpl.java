package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashPrepayService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPrepayVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FeeCashPrepayServiceImpl extends BusinessPageCacheServiceImpl<FeeCashPrepayVo, FeeCashPrepayDto> implements FeeCashPrepayService {

    @Autowired(required = false)
    private FeeCashDetailService feeCashDetailService;

    /**
     * 计算预付明细
     *
     * @param cacheKeyDetail
     * @param cacheKeyPrepay
     */
    @Override
    public void calThisReversedAmount(String cacheKeyDetail, String cacheKeyPrepay, Integer copyFydf) {
        List<FeeCashDetailDto> detailDtoList = feeCashDetailService.findCacheList(cacheKeyDetail);
        List<FeeCashPrepayDto> prepayDtoList = findCacheList(cacheKeyPrepay);
        //根据兑付金额的汇总，和预付明细的可冲销金额，依次往本次冲销金额里面填充
//        if (CollectionUtils.isNotEmpty(detailDtoList) && CollectionUtils.isNotEmpty(prepayDtoList)) {
//            prepayDtoList = prepayDtoList.stream().sorted(Comparator.comparing(FeeCashPrepayDto::getCreateTime)).collect(Collectors.toList());
//            BigDecimal thisCashAmount = detailDtoList.stream().map(e -> Optional.ofNullable(e.getThisCashAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
//            if (BigDecimal.ZERO.compareTo(thisCashAmount) == 0) {
//                return;
//            }
//            for (FeeCashPrepayDto e : prepayDtoList) {
//                if (thisCashAmount.compareTo(BigDecimal.ZERO) <= 0) {
//                    e.setThisReversedAmount(BigDecimal.ZERO);
//                    continue;
//                }
//                BigDecimal availableReversedAmount = Optional.ofNullable(e.getAvailableReversedAmount()).orElse(BigDecimal.ZERO);
//                if (availableReversedAmount.compareTo(thisCashAmount) < 0) {
//                    e.setThisReversedAmount(availableReversedAmount);
//                } else {
//                    e.setThisReversedAmount(thisCashAmount);
//                }
//                thisCashAmount = thisCashAmount.subtract(availableReversedAmount);
//            }
//            addItemCache(cacheKeyPrepay, prepayDtoList);
//        }
        if (CollectionUtils.isNotEmpty(detailDtoList) && CollectionUtils.isNotEmpty(prepayDtoList)) {
            prepayDtoList = prepayDtoList.stream().sorted(Comparator.comparing(FeeCashPrepayDto::getPrepayDetailCode)).collect(Collectors.toList());
            if(0==copyFydf){
            Map<String, BigDecimal> detailDtoMap = detailDtoList.stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode(), Collectors.mapping(e -> Optional.ofNullable(e.getThisCashAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            for (Map.Entry<String, BigDecimal> entry : detailDtoMap.entrySet()) {
                BigDecimal thisCashAmount = entry.getValue();
                List<FeeCashPrepayDto> prepayDtoListFilter = prepayDtoList.stream().filter(e -> e.getSchemeDetailCode().equals(entry.getKey())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(prepayDtoListFilter)) {
                    for (FeeCashPrepayDto e : prepayDtoListFilter) {
                        if (thisCashAmount.compareTo(BigDecimal.ZERO) <= 0) {
                            e.setThisReversedAmount(BigDecimal.ZERO);
                            continue;
                        }
                        BigDecimal availableReversedAmount = Optional.ofNullable(e.getAvailableReversedAmount()).orElse(BigDecimal.ZERO);
                        if (availableReversedAmount.compareTo(thisCashAmount) < 0) {
                            e.setThisReversedAmount(availableReversedAmount);
                        } else {
                            e.setThisReversedAmount(thisCashAmount);
                        }
                        thisCashAmount = thisCashAmount.subtract(availableReversedAmount);
                    }
                }
            } } else{
                BigDecimal totalCashAmount = detailDtoList.stream().collect(Collectors.mapping(e -> Optional.ofNullable(e.getThisCashAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)));
                if (!CollectionUtils.isEmpty(prepayDtoList)) {
                    Map<String, BigDecimal> prepayDetailCodeAmountMap = new HashMap<>();
                    for (FeeCashPrepayDto dto : prepayDtoList) {
                        if (totalCashAmount.compareTo(BigDecimal.ZERO) <= 0) {
                            dto.setThisReversedAmount(BigDecimal.ZERO);
                        } else {
                            BigDecimal availableReversedAmount = Optional.ofNullable(dto.getAvailableReversedAmount()).orElse(BigDecimal.ZERO);
                            BigDecimal prepareCarryAmount = Optional.ofNullable(dto.getPrepareCarryAmount()).orElse(BigDecimal.ZERO);
                            if (prepareCarryAmount.compareTo(BigDecimal.ZERO) > 0) {
                                availableReversedAmount = availableReversedAmount.subtract(prepareCarryAmount);
                                if (availableReversedAmount.compareTo(BigDecimal.ZERO) < 0) {
                                    availableReversedAmount = BigDecimal.ZERO;
                                }
                                prepayDetailCodeAmountMap.put(dto.getPrepayDetailCode(), prepareCarryAmount);
                            }
                            if (availableReversedAmount.compareTo(totalCashAmount) < 0) {
                                dto.setThisReversedAmount(availableReversedAmount);
                            } else {
                                dto.setThisReversedAmount(totalCashAmount);
                            }
                            totalCashAmount = totalCashAmount.subtract(dto.getThisReversedAmount());
                        }
                    }
                    if (totalCashAmount.compareTo(BigDecimal.ZERO) > 0 && prepayDetailCodeAmountMap.size() > 0) {
                        AtomicReference<BigDecimal> finalTotalCashAmount = new AtomicReference<>(totalCashAmount);
                        for (FeeCashPrepayDto feeCashPrepayDto : prepayDtoList) {
                            BigDecimal bigDecimal = prepayDetailCodeAmountMap.get(feeCashPrepayDto.getPrepayDetailCode());
                            if(null!=bigDecimal && finalTotalCashAmount.get().compareTo(BigDecimal.ZERO)>0 ){
                                BigDecimal thisReversedAmount = feeCashPrepayDto.getThisReversedAmount();
                                BigDecimal availableReversedAmount = feeCashPrepayDto.getAvailableReversedAmount();
                                BigDecimal maxAddAmount=availableReversedAmount.subtract(thisReversedAmount);
                                BigDecimal addAmount= maxAddAmount.compareTo(finalTotalCashAmount.get()) > 0 ? finalTotalCashAmount.get() : maxAddAmount;
                                if(addAmount.compareTo(bigDecimal) > 0) {
                                    addAmount = bigDecimal;
                                }
                                feeCashPrepayDto.setThisReversedAmount(feeCashPrepayDto.getThisReversedAmount().add(addAmount));
                                finalTotalCashAmount.set(finalTotalCashAmount.get().subtract(addAmount));
                            }
                        }
                    }
                }
            }

            addItemCache(cacheKeyPrepay, prepayDtoList);
        }
    }

    /**
     * 删除更新预付明细
     *
     * @param cacheKeyDetail
     * @param cacheKeyPrepay
     */
    @Override
    public void updatePrepay(String cacheKeyDetail, String cacheKeyPrepay) {
        List<FeeCashDetailDto> detailDtoList = feeCashDetailService.findCacheList(cacheKeyDetail);
        List<FeeCashPrepayDto> prepayDtoList = findCacheList(cacheKeyPrepay);
        //如果活动在兑付明细中不存在，也要删除对应的预付明细
        if (CollectionUtils.isNotEmpty(detailDtoList) && CollectionUtils.isNotEmpty(prepayDtoList)) {
            List<String> schemeDetailCodes = detailDtoList.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
            prepayDtoList.forEach(e -> {
                if (!schemeDetailCodes.contains(e.getSchemeDetailCode())) {
                    e.setChecked(BooleanEnum.TRUE.getNumStr());
                }
            });
            deleteCacheList(cacheKeyPrepay, prepayDtoList);
        }
    }
}
