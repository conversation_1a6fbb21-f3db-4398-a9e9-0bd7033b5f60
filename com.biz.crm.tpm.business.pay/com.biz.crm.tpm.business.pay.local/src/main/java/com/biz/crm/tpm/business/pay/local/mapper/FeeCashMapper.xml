<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.FeeCashMapper">

    <select id="hecVoucherCallback" >
        <foreach item="dto" collection="dtoList" index="index"  open="" separator=";" close="">
            update tpm_fee_cash
            set voucher_code = #{dto.orderCode},
            voucher_callback_date = #{dto.postingDateDate}
            where cash_code = #{dto.businessCode}
        </foreach>
    </select>
    <select id="hecPayStatusCallback">
        <foreach item="dto" collection="dtoList" index="index" open="" separator=";" close="">
            update tpm_fee_cash
            set pay_status = #{dto.orderStatus},
            pay_sucess_date = #{dto.paySucessDate}
            where cash_code = #{dto.businessCode}
        </foreach>
    </select>

    <select id="findCashAmountBySchemeDetailCodes" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT CASE
        WHEN SUM(CASE WHEN be_cash = 'Y' THEN 1 ELSE 0 END) > 0 THEN SUM(this_cash_amount)
        ELSE 0
        END AS thisCashAmount,
        scheme_detail_code
        FROM tpm_fee_cash_detail a
        LEFT JOIN tpm_fee_cash b on a.cash_code = b.cash_code
        <where>
            b.status = '3'
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                and a.scheme_detail_code in
                <foreach collection="schemeDetailCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            group by a.scheme_detail_code
        </where>
    </select>


    <select id="findCashAmountBySchemeDetailCodesPass" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT
        DATE_FORMAT(a.delivery_time, '%Y-%m') delivery_time,
        a.scheme_detail_code,
        b.year_month_ly,
        b.activities_detail_code,
        CASE WHEN <![CDATA[DATE_FORMAT(a.delivery_time, '%Y-%m') <= b.year_month_ly]]> THEN
        c.this_cash_amount
        ELSE
        0
        END this_cash_amount
        FROM
        tpm_delivery_replenishment_pool_detail a
        LEFT JOIN tpm_with_holding b ON a.scheme_detail_code = b.activities_detail_code
        LEFT JOIN tpm_fee_cash_detail c ON a.scheme_detail_code = c.scheme_detail_code
        <where>
            a.operation_type = 'fee_cash'
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                and a.scheme_detail_code in
                <foreach collection="schemeDetailCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


    <select id="findCashAmountBySchemeDetailCodesApproved" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT
        DATE_FORMAT(a.delivery_time, '%Y-%m') delivery_time,
        a.scheme_detail_code,
        b.year_month_ly,
        b.activities_detail_code,
        CASE WHEN <![CDATA[DATE_FORMAT(a.delivery_time, '%Y-%m') <= b.year_month_ly]]> THEN
        a.operation_amount
        ELSE
        0
        END this_cash_amount
        FROM
        tpm_delivery_replenishment_pool_detail a
        LEFT JOIN tpm_with_holding b ON a.scheme_detail_code = b.activities_detail_code
        <where>
            a.operation_type != 'audit'
            and ( 1= 2
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                or a.scheme_detail_code in
                <foreach collection="schemeDetailCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                or a.parent_code in
                <foreach collection="schemeDetailCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            )
        </where>
    </select>

    <select id="getTicketDeductionDetail"
            resultType="com.biz.crm.tpm.business.pay.sdk.vo.TicketFeeCashDetailVo">
        SELECT
            t.create_name,
            t.create_account,
            t.create_time,
            t.cash_code,
            t.budget_subjects_name,
            t.budget_subjects_code,
            t.amount,
            t.customer_code,
            t.customer_name,
            t.cash_name,
            t.years,
            t.company_code,
            ta.status,
            ta.cash_type
        FROM
            tpm_fee_cash_ticket t
        INNER JOIN tpm_fee_cash ta
        ON t.cash_code = ta.cash_code
        WHERE
            ta.status IN ('1', '4', '5')
          AND (
            (ta.cash_method = 'ticket_buckle' AND ta.cash_type = 'fee')
                OR ta.cash_type = 'account'
            )
        <if test="dto.customerCode != null and dto.customerCode != ''">
            AND t.customer_code = #{dto.customerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            AND t.customer_name like concat('%', #{dto.customerName}, '%')
        </if>
        <if test="dto.cashCode != null and dto.cashCode != ''">
            AND t.cash_code = #{dto.cashCode}
        </if>
        <if test="dto.years != null and dto.years != ''">
            AND t.years = #{dto.years}
        </if>
        <if test="dto.budgetSubjectsCode != null and dto.budgetSubjectsCode != ''">
            AND t.budget_subjects_code = #{dto.budgetSubjectsCode}
        </if>
        <if test="dto.budgetSubjectsName != null and dto.budgetSubjectsName != ''">
            AND t.budget_subjects_name like concat('%', #{dto.budgetSubjectsName}, '%')
        </if>
        <if test="dto.companyCode != null and dto.companyCode != ''">
            AND t.company_code = #{dto.companyCode}
        </if>
    </select>

    <select id="findFeeCashAuditDetailListByAuditDetailCodes" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT
        b.audit_detail_code,
        sum(b.this_cash_amount) this_cash_amount
        FROM
        tpm_fee_cash a
        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
        WHERE
        (a.cash_type in ( 'fee','wiredf') or (a.cash_type in ('wire','account') and a.status in ('1','2','4','5')  ) )
        AND b.audit_detail_code in
        <foreach collection="auditDetailCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
        <if test="cashCode!=null and cashCode!=''">
            and a.cash_code !=#{cashCode}
        </if>
        group by b.audit_detail_code
    </select>

    <select id="findPrepayListByAuditDetailCodes" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT
        b.audit_detail_code,
        sum(b.this_cash_amount) this_cash_amount
        FROM
        tpm_fee_cash a
        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
        WHERE
        a.cash_code != #{cashCode}
        AND b.audit_detail_code in
        <foreach collection="auditDetailCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
        and a.status != '3'
        group by b.audit_detail_code
    </select>

    <select id="findCashAmountBySchemeDetailCodesApprovedBefore" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT
        DATE_FORMAT(b.delivery_time, '%Y-%m') delivery_time,
        b.scheme_detail_code,
        c.years year_month_ly,
        a.scheme_detail_code,
        CASE WHEN <![CDATA[DATE_FORMAT(b.delivery_time, '%Y-%m') <= c.years]]> THEN
        b.operation_amount
        ELSE
        0
        END this_cash_amount
        FROM tpm_fee_cash_detail a
        LEFT JOIN tpm_delivery_replenishment_pool_detail b ON a.cash_detail_code = b.cash_detail_code
        LEFT JOIN tpm_marketing_plan_case c ON a.scheme_detail_code = c.scheme_detail_code
        <where>
            b.operation_type != 'audit'
            AND a.bear_department_code not in
            <foreach collection="orgCodes" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
            and a.belong_department_code in
            <foreach collection="orgCodes" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
            and c.years = #{yearMonthLy}
        </where>
    </select>
    <select id="findNoTaxCashAmountByYearsAndOrgCodes"
            resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo">
        SELECT
        a.this_cash_no_tax_amount,
        a.category_code,
        a.category_name
        FROM tpm_fee_cash_detail a
        LEFT JOIN tpm_fee_cash b ON a.cash_code = b.cash_code
        <where>
            b.`status` = '3'
            and a.belong_department_code in
            <foreach collection="orgCodeList" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
            and a.years = #{yearMonthLy}
        </where>
    </select>
</mapper>

