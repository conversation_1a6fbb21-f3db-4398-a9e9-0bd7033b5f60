package com.biz.crm.tpm.business.pay.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingVoService;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月27日 15:51:00
 */
@RestController
@RequestMapping("/v1/pay/WithHolding")
@Slf4j
@Api(tags = "费用预提Vo")
public class WithHoldingVoController {


    @Autowired
    private WithHoldingVoService withHoldingVoService;

    /**
     * 查询活动信息
     *
     * @param codes
     * @return 查询活动信息
     */
    @ApiOperation(value = "查询活动信息")
    @GetMapping("findActivities")
    public Result<List<WithHoldingVo>> findActivitiesByConditions(@ApiParam(name = "codes", value = "活动编码集合") @RequestParam List<String> codes) {
        try {
            List<WithHoldingVo> activitiesByConditions = this.withHoldingVoService.findActivitiesByConditions(codes);
            return Result.ok(activitiesByConditions);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<WithHoldingVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id") String id) {
        try {
            WithHoldingVo WithHolding = this.withHoldingVoService.findById(id);
            return Result.ok(WithHolding);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

}
