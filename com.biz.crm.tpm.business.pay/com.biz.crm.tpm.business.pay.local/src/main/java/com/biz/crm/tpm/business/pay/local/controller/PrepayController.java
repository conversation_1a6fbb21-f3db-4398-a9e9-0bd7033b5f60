package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDto;
import com.biz.crm.tpm.business.pay.sdk.service.PrepayService;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayActivityItemVo;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 活动预付;(tpm_prepay)控制层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Api(tags = "活动预付功能接口")
@RestController
@RequestMapping("/v1/pay/prepay")
@Slf4j
@Deprecated
public class PrepayController {
  /**
   * 服务对象
   */
  @Autowired
  private PrepayService prepayService;

  /**
   * 由于创建或修改导致的重复提交的问题。在创建和修改前，需要使用该方法获得预授权</br></br>
   * 预授权成功后，才能通过预授权信息进行添加，
   *
   * @return
   */
  @ApiOperation(value = "由于创建或修改导致的重复提交的问题。在创建和修改前，需要使用该方法获得预授权</br>")
  @PostMapping(value = "/preSave")
  public Result<?> preSave() {
    try {
      Object prefix = this.prepayService.preSave();
      return Result.ok(prefix);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页查询所有数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<PrepayVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                 @ApiParam(name = "prepay", value = "核销采集信息") PrepayDto dto) {
    try {
      Page<PrepayVo> page = this.prepayService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页活动数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页活动数据")
  @GetMapping("findActivitiesByConditions")
  public Result<Page<ActivitiesVo>> findActivitiesByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                               @ApiParam(name = "audit", value = "活动信息") ActivitiesDto dto) {
    try {
      Page<ActivitiesVo> page = this.prepayService.findActivitiesByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页活动明细数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页活动明细数据")
  @GetMapping("findActivitiesDetailByConditions")
  public Result<Page<PrepayActivityItemVo>> findActivitiesDetailByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                             @ApiParam(name = "audit", value = "活动信息") ActivitiesDetailDto dto) {
    try {
      Page<PrepayActivityItemVo> page = this.prepayService.findActivitiesDetailByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<PrepayVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id", required = true) String id) {
    try {
      PrepayVo prepayVo = this.prepayService.findById(id);
      return Result.ok(prepayVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByCode")
  public Result<PrepayVo> findByCode(@ApiParam(name = "code", value = "编号", required = true) String code) {
    try {
      PrepayVo prepayVo = this.prepayService.findByCode(code);
      return Result.ok(prepayVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param prepayDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<PrepayVo> create(@ApiParam(name = "prepayDto", value = "活动预付") @RequestBody PrepayDto prepayDto) {
    try {
      PrepayVo result = this.prepayService.create(prepayDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   *
   * @param prepayDto 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<PrepayVo> update(@ApiParam(name = "prepayDto", value = "活动预付") @RequestBody PrepayDto prepayDto) {
    try {
      PrepayVo result = this.prepayService.update(prepayDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation(value = "删除数据")
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    try {
      this.prepayService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}