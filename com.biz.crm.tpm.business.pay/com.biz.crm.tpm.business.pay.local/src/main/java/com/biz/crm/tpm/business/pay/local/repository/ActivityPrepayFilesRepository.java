package com.biz.crm.tpm.business.pay.local.repository;



import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayFiles;
import com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayFilesMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.List;


/**
 * 活动预付附件(ActivityPrepayFiles)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-04 20:03:02
 */
@Component
public class ActivityPrepayFilesRepository extends ServiceImpl<ActivityPrepayFilesMapper, ActivityPrepayFiles> {

  @Autowired
  private ActivityPrepayFilesMapper activityPrepayFilesMapper;

  /**
   * 根据编码查询
   *
   * @param code
   * @return
   */
  public List<ActivityPrepayFiles> findByCode(String code) {
    return this.lambdaQuery().eq(ActivityPrepayFiles::getPrepayCode, code)
            .eq(ActivityPrepayFiles::getTenantCode, TenantUtils.getTenantCode()).list();
  }

  /**
   * 根据codeList删除
   *
   * @param codeList
   */
  public void deleteByCodeList(List<String> codeList) {
    this.lambdaUpdate().in(ActivityPrepayFiles::getPrepayCode, codeList)
            .remove();
  }
}

