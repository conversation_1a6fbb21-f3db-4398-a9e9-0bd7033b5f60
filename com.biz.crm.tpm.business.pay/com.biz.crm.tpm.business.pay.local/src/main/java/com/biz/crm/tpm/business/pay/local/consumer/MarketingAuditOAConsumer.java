package com.biz.crm.tpm.business.pay.local.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.rocketmq.service.AbstractRocketMqConsumer;
import com.biz.crm.business.common.rocketmq.service.RocketMqProducer;
import com.biz.crm.business.common.rocketmq.util.RocketMqUtil;
import com.biz.crm.business.common.rocketmq.vo.MqMessageVo;
import com.biz.crm.tpm.business.pay.sdk.dto.ModifyPrepayRecordDto;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.workflow.sdk.dto.OaCallbackDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Objects;

/**
 * @describe: mq消费示例
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2022.10.14 10:09
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MqConstant.TOPIC_OA_CALLBACK_ORDER + "${rocketmq.environment}",
        /**
         * tag
         * 可用 || 监听多个tag： "tag1 || tag2 || tag3"
         * 请把tag  定义在 *** 内 需要统一维护
        */
        selectorExpression = MqConstant.TAG_OA_CALLBACK + MqConstant.TAG_TPM_AUDIT,
        /**
         * 相同分组下 consumer 可自动负载均衡
         * 请把consumerGroup  定义在 *** 内 需要统一维护
        */
        consumerGroup = MqConstant.TAG_TPM_AUDIT_GROUP + "${rocketmq.environment}",
        /**
         * 默认集群消费
         * 可以设置 ConsumeMode.ORDERLY 使用广播消费
         * 也可使用集群模式模拟广播模式：
         * 启动多个不同 consumerGroup 的consumer实例
        */
        consumeMode = ConsumeMode.ORDERLY,
        /**
         * 集群消费or广播消费;默认是集群消费
        */
        messageModel = MessageModel.CLUSTERING)
public class MarketingAuditOAConsumer extends AbstractRocketMqConsumer {

    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;

    @Autowired(required = false)
    private RocketMqProducer rocketMqProducer;

    @Override
    protected Object handleMessage(MqMessageVo message) {
        if (Objects.isNull(message)
                || StringUtil.isEmpty(message.getMsgBody())) {
            log.error("order mq message is null  : {}", message);
            return "消费为空,消费失败!";
        }
        log.info("order mq message received  : {}", message);
        String msgBody = message.getMsgBody();
        OaCallbackDto dto = JSONObject.parseObject(msgBody, OaCallbackDto.class);
        marketingAuditService.oaCallback(dto.getBusinessCode(), dto.getProcessStatus());
        if (ProcessStatusEnum.PASS.getDictCode().equals(dto.getProcessStatus())) {
            this.sendMsg(dto);
        }
        return "顺序消息消费成功.";
    }

    private void sendMsg(OaCallbackDto dto) {
        try {
            ModifyPrepayRecordDto recordDto = new ModifyPrepayRecordDto();
            recordDto.setAllEndCaseJson(JSON.toJSONString(dto));
            MqMessageVo mqMessageVo = new MqMessageVo();
            mqMessageVo.setTopic(MqConstant.TPM_ACTIVITY_PREPAY_RECORD + RocketMqUtil.mqEnvironment());
            mqMessageVo.setRepeatConsumer(false);
            mqMessageVo.setTag(MqConstant.TPM_ACTIVITY_PREPAY_RECORD);
            mqMessageVo.setMsgBody(JSON.toJSONString(recordDto));
            mqMessageVo.setMsgNum(1);
            String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_DATE_PATTERN));
            this.rocketMqProducer.sendMqOrderMsg(mqMessageVo, sha256Hex(StringUtils.join(dto, date)));
        } catch (Exception e) {
            log.error("topic [{}] tag [{}] 发送异常：{}", MqConstant.TPM_ACTIVITY_PREPAY_RECORD, MqConstant.TPM_ACTIVITY_PREPAY_RECORD, e.getMessage(), e);
        }
    }

    public  String sha256Hex(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes("UTF-8"));
            String base64Hash = Base64.getEncoder().encodeToString(hash);
            return base64Hash;
        } catch (NoSuchAlgorithmException | java.io.UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }
}