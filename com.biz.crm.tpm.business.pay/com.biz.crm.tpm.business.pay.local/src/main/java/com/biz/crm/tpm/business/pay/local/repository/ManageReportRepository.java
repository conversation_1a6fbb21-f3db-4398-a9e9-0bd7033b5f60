package com.biz.crm.tpm.business.pay.local.repository;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.ManageReport;
import com.biz.crm.tpm.business.pay.local.mapper.ManageReportMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.ManageReportDto;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.YearMonth;
import java.util.Collections;
import java.util.List;


/**
 * 管报收入(ManageReport)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-22 14:57:23
 */
@Component
public class ManageReportRepository extends ServiceImpl<ManageReportMapper, ManageReport> {

    @Autowired
    private ManageReportMapper manageReportMapper;

    /**
     * 根据id集合获取详情集合
     *
     * @param ids ID集合
     * @return List<ManageReport>
     */
    public List<ManageReport> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(ManageReport::getId, ids)
                .eq(ManageReport::getTenantCode, tenantCode)
                .list();
    }

    /**
     * 按条件查询
     *
     * @param dto
     * @return
     */
    public List<ManageReportVo> findByCondition(ManageReportDto dto) {
        return manageReportMapper.findByCondition(dto);
    }


    public List<ManageReportVo> findListByCondition(WithholdingIncomeQueryDto dto) {
        return manageReportMapper.findListByCondition(dto);
    }

    public List<ManageReportVo> findByYearAndCustomerCodes(String startEndMonth, List<String> customerCodes) {
        return manageReportMapper.findByYearAndCustomerCodes(startEndMonth, customerCodes);
    }
}

