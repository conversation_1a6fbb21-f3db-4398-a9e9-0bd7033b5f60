package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 实体：活动预付明细;
 *
 * <AUTHOR> yaoyongming
 * @date : 2024-6-4
 */
@ApiModel(value = "ActivityPrepayDetail", description = "活动预付明细")
@TableName("tpm_activity_prepay_detail")
@Getter
@Setter
@Entity(name = "tpm_activity_prepay_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_activity_prepay_detail", comment = "活动预付明细")
@Table(name = "tpm_activity_prepay_detail", indexes = {@Index(name = "activity_prepay_detail_idx1", columnList = "prepay_code")})
public class ActivityPrepayDetail extends TenantFlagOpEntity {

  @ApiModelProperty("预付编号")
  @Column(name = "prepay_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付编号 '")
  private String prepayCode;

  @ApiModelProperty("预付名称")
  @Column(name = "prepay_name", columnDefinition = "varchar(128) comment '预付名称'")
  private String prepayName;

  @ApiModelProperty("预付明细编号")
  @Column(name = "prepay_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付明细编号 '")
  private String prepayDetailCode;

  @ApiModelProperty("方案编码")
  @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
  private String schemeCode;

  @ApiModelProperty("方案名称")
  @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称'")
  private String schemeName;

  @ApiModelProperty("方案明细编码")
  @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
  private String schemeDetailCode;

  @ApiModelProperty("活动大类编码")
  @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
  private String categoryCode;

  @ApiModelProperty("活动大类名称")
  @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
  private String categoryName;

  @ApiModelProperty("活动细类编码")
  @Column(name = "detail_code", columnDefinition = "varchar(32) comment '活动细类编码'")
  private String detailCode;

  @ApiModelProperty("活动细类名称")
  @Column(name = "detail_name", columnDefinition = "varchar(64) comment '活动细类名称'")
  private String detailName;

  @ApiModelProperty("开始时间")
  @Column(name = "start_date", columnDefinition = "varchar(32) comment '开始时间'")
  private String startDate;

  @ApiModelProperty("结束时间")
  @Column(name = "end_date", columnDefinition = "varchar(32) comment '结束时间'")
  private String endDate;

  @ApiModelProperty("年月")
  @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
  private String years;

  @ApiModelProperty("客户编码")
  @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
  private String customerCode;

  @ApiModelProperty("客户名称")
  @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
  private String customerName;

  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
  private String erpCode;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
  private String companyCode;

  @ApiModelProperty("产品组编码")
  @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
  private String channelCode;

  @ApiModelProperty("供应商编码")
  @Column(name = "payee_code", columnDefinition = "VARCHAR(64) COMMENT '供应商编码 '")
  private String payeeCode;

  @ApiModelProperty("供应商名称")
  @Column(name = "payee_name", columnDefinition = "VARCHAR(255) COMMENT '供应商名称 '")
  private String payeeName;

  @ApiModelProperty("申请金额")
  @Column(name = "apply_amount", columnDefinition = "decimal(18,4) comment '申请金额'")
  private BigDecimal applyAmount;

  @ApiModelProperty("已预付金额")
  @Column(name = "prepay_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '已预付金额 '")
  private BigDecimal prepayAmount;

  @ApiModelProperty("兑付金额")
  @Column(name = "cash_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '兑付金额 '")
  private BigDecimal cashAmount;

  @ApiModelProperty("预付可冲销金额")
  @Column(name = "available_reversed_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '预付可冲销金额 '")
  private BigDecimal availableReversedAmount;

  @ApiModelProperty("可预付金额")
  @Column(name = "available_prepay_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '可预付金额 '")
  private BigDecimal availablePrepayAmount;

  @ApiModelProperty("预付申请金额")
  @Column(name = "this_prepay_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '预付申请金额 '")
  private BigDecimal thisPrepayAmount;

  @ApiModelProperty("本次支付金额")
  @Column(name = "this_pay_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '本次支付金额 '")
  private BigDecimal thisPayAmount;

  @ApiModelProperty("关联结转金额")
  @Column(name = "relate_carry_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '关联结转金额 '")
  private BigDecimal relateCarryAmount;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "varchar(64) comment '成本中心名称'")
  private String costCenterName;
}