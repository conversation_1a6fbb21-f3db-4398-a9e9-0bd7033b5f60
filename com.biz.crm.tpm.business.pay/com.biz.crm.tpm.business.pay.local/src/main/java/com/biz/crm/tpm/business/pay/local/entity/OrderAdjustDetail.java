package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@ApiModel(value = "OrderAdjustDetail", description = "订单调整明细")
@TableName("tpm_order_adjust_detail")
@Getter
@Setter
@Entity(name = "tpm_order_adjust_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_order_adjust_detail", comment = "订单调整明细")
@Table(name = "tpm_order_adjust_detail", indexes = {@Index(name = "credit_order_idx1", columnList = "credit_code")})
public class OrderAdjustDetail extends TenantFlagOpEntity {

    @ApiModelProperty("调整编码")
    @Column(name = "adjust_code", length = 64, columnDefinition = "varchar(64) COMMENT '调整编码'")
    private String adjustCode;

    @ApiModelProperty("调整名称")
    @Column(name = "adjust_name", length = 128, columnDefinition = "varchar(128) COMMENT '调整名称'")
    private String adjustName;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("预算科目编码")
    @Column(name = "budget_subjects_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
    private String budgetSubjectsCode;

    @ApiModelProperty("预算科目名称")
    @Column(name = "budget_subjects_name", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
    private String budgetSubjectsName;

    @ApiModelProperty("核销申请名称")
    @Column(name = "audit_name", columnDefinition = "VARCHAR(255) COMMENT '核销申请名称 '")
    private String auditName;

    @ApiModelProperty("核销申请编号")
    @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
    private String auditCode;

    @ApiModelProperty("批次号")
    @Column(name = "bt_no", length = 64, columnDefinition = "varchar(64) COMMENT '批次号'")
    private String btNo;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

    @ApiModelProperty("产品编码")
    @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
    private String productCode;

    @ApiModelProperty("产品名称")
    @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
    private String productName;

    @ApiModelProperty("销售单位")
    @Column(name = "sale_unit", columnDefinition = "VARCHAR(32) COMMENT '销售单位'")
    private String saleUnit;

    @ApiModelProperty("物料编码")
    @Column(name = "material_code", columnDefinition = "varchar(32) comment '物料编码'")
    private String materialCode;

    @ApiModelProperty("物料名称")
    @Column(name = "material_name", columnDefinition = "varchar(128) comment '物料名称'")
    private String materialName;

    @ApiModelProperty("数量")
    @Column(name = "quantity", columnDefinition = "decimal(20,6) COMMENT '数量'")
    private BigDecimal quantity;

    @ApiModelProperty("单价")
    @Column(name = "price", columnDefinition = "decimal(20,6) COMMENT '单价'")
    private BigDecimal price;

    @ApiModelProperty("金额")
    @Column(name = "amount", columnDefinition = "decimal(20,6) COMMENT '金额'")
    private BigDecimal amount;

    @ApiModelProperty("贷项订单编码")
    @Column(name = "credit_code", columnDefinition = "VARCHAR(32) COMMENT '贷项订单编码'")
    private String creditCode;

    @ApiModelProperty("行号")
    @Column(name = "line_number", length = 64, columnDefinition = "int(11) COMMENT '行号'")
    private Integer lineNumber;

    @ApiModelProperty("调整金额")
    @Column(name = "adjust_amount", columnDefinition = "decimal(20,6) COMMENT '调整金额'")
    private BigDecimal adjustAmount;

}
