package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecordItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordItemDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AccountVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordItemVo;
import org.apache.ibatis.annotations.Param;

/**
 * 活动预付明细跟踪(ActivityPrepayDetailRecordItem)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-20 17:18:38
 */
public interface ActivityPrepayDetailRecordItemMapper extends BaseMapper<ActivityPrepayDetailRecordItem> {

    Page<ActivityPrepayDetailRecordItemVo> findByConditions(Page<ActivityPrepayDetailRecordItemVo> page,
                                                            @Param("dto") ActivityPrepayDetailRecordItemDto dto);

}

