<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AuditInvoiceMapper">
  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.AuditInvoice" id="AuditInvoiceMap">
  </resultMap>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.AuditInvoiceVo">
    select
    t.*
    from tpm_audit_invoice t
    <where>
        t.tenant_code = #{dto.tenantCode}
      <if test="dto.auditCode != null and dto.auditCode != '' ">
        and t.audit_code = #{dto.auditCode}
      </if>
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>