package com.biz.crm.tpm.business.pay.local.service.internal.process;

import com.aliyun.openservices.shade.com.google.common.collect.Maps;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustImportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustProductVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OrderAdjustImportProcess implements ImportProcess<OrderAdjustImportVo> {

    @Autowired(required = false)
    private ProductVoService productVoService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private RedisService redisService;

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, OrderAdjustImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, OrderAdjustImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        Validate.notNull(params.get("cacheKey"), "cacheKey不能为空！");
        Validate.notEmpty(params.get("cacheKey").toString(), "cacheKey不能为空！");
        //品项
        Map<String, ProductVo> productVoMap = this.productVoMap(data);
        Map<String, Integer> productMap = Maps.newHashMap();
        for (Map.Entry<Integer, OrderAdjustImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            OrderAdjustImportVo vo = row.getValue();
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getProductCode()), "产品编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getAmountStr()), "订单金额，不能为空！");
            if (StringUtil.isNotEmpty(vo.getProductCode())) {
                ProductVo productVo = productVoMap.get(vo.getProductCode());
                this.validateIsTrue(Objects.nonNull(productVo), "产品不存在！");
                if (Objects.nonNull(productVo)) {
                    this.validateIsTrue(EnableStatusEnum.ENABLE.getCode().equals(productVo.getEnableStatus()),
                            "产品已禁用！");
                    this.validateIsTrue(Objects.isNull(productMap.get(vo.getProductCode())),
                            "产品已和第[" + productMap.get(vo.getProductCode()) + "]行重复!");
                    if (!productMap.containsKey(vo.getProductCode())) {
                        productMap.put(vo.getProductCode(), rowNum);
                    }
                }
            }
            try {
                new BigDecimal(vo.getAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "订单金额类型转换失败！");
            }
            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, OrderAdjustImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        //品项
        Map<String, ProductVo> productVoMap = this.productVoMap(data);

        for (Map.Entry<Integer, OrderAdjustImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            OrderAdjustImportVo vo = row.getValue();
            if (StringUtil.isNotEmpty(vo.getProductCode())) {
                ProductVo productVo = productVoMap.get(vo.getProductCode());
                if (Objects.nonNull(productVo)) {
                    vo.setProductName(productVo.getProductName());
                    vo.setSaleUnit(productVo.getSaleUnit());
                }
            }
            try {
                vo.setAmount(new BigDecimal(vo.getAmountStr().trim()).setScale(2, RoundingMode.HALF_DOWN));
            } catch (Exception e) {
                this.validateIsTrue(false, "订单金额类型转换失败！");
            }
            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        List<OrderAdjustProductVo> productList = (List<OrderAdjustProductVo>) nebulaToolkitService.copyCollectionByWhiteList(data.values(), OrderAdjustImportVo.class, OrderAdjustProductVo.class, LinkedHashSet.class, ArrayList.class);
        if (CollectionUtils.isNotEmpty(productList)) {
            redisService.set(PayConstant.ORDER_ADJUST_PRODUCT_CACHE_KEY + params.get("cacheKey"), productList, 60 * 5);
        }
        return errMap;
    }


    /**
     * 品项信息
     *
     * @param data
     * @return
     */
    private Map<String, ProductVo> productVoMap(LinkedHashMap<Integer, OrderAdjustImportVo> data) {
        Map<String, ProductVo> productVoMap = new HashMap<>();
        List<String> productCodeList = data.values().stream().filter(Objects::nonNull).map(OrderAdjustImportVo::getProductCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productCodeList)) {
            productVoService.findMainDetailsByProductCodes(productCodeList)
                    .forEach(productVo -> productVoMap.put(productVo.getProductCode(), productVo));
        }
        return productVoMap;
    }


    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<OrderAdjustImportVo> findCrmExcelVoClass() {
        return OrderAdjustImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "ORDER_ADJUST_CREDIT_ORDER_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "订单调整货项订单导入模板";
    }
}
