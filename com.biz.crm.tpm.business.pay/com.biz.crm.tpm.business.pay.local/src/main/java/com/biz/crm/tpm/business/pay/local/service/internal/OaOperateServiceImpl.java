package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.date.DateUtil;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.service.OaOperateService;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class OaOperateServiceImpl implements OaOperateService {

    @Autowired(required = false)
    private MarketingPlanService marketingPlanService;
    @Autowired(required = false)
    private RegionCollectService regionCollectService;
    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;

    /**
     * 撤回变更、追加、规划及方案结案
     */
    @Override
    public void recoverAll() {
        String yearMonthLy = DateUtil.format(DateUtil.lastMonth(), "yyyy-MM");
        List<MarketingPlanVo> planVos = marketingPlanService.findMarketingPlanCommit(yearMonthLy);
        List<RegionCollectVo> regionCollectVos = regionCollectService.findCommit(yearMonthLy);
        List<MarketingAuditVo> auditVos = marketingAuditService.findCommit(yearMonthLy);

        if (CollectionUtils.isNotEmpty(planVos)) {
            planVos.forEach(l -> {
                try {
                    marketingPlanService.recover(l.getSchemeCode(), null);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(regionCollectVos)) {
            regionCollectVos.forEach(l -> {
                try {
                    regionCollectService.recover(l.getCollectCode(), null);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(auditVos)) {
            auditVos.forEach(l -> {
                try {
                    marketingAuditService.recover(l.getAuditCode(), null);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        }
    }
}
