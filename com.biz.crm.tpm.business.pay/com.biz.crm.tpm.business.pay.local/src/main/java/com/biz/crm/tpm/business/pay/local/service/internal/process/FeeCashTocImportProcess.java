package com.biz.crm.tpm.business.pay.local.service.internal.process;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.pay.local.service.internal.FeeCashPageCacheHelper;
import com.biz.crm.tpm.business.pay.local.service.internal.FeeCashTocPageCacheHelper;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTocDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTocImportVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FeeCashTocImportProcess implements ImportProcess<FeeCashTocImportVo> {

    @Autowired
    private FeeCashTocPageCacheHelper feeCashTocPageCacheHelper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, FeeCashTocImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, FeeCashTocImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");

        for (Map.Entry<Integer, FeeCashTocImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            FeeCashTocImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getPlatform()), "打款平台，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getPayeeName()), "姓名，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getBankNo()), "银行账号，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getIdCard()), "身份证号码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getPhone()), "手机号，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getPayeeAmountStr()), "收款金额，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getPayAmountStr()), "实际打款金额，不能为空！");

            // 银行账号格式校验：不允许有空格及特殊符号
            if (StringUtils.isNotEmpty(vo.getBankNo())) {
                String bankNo = vo.getBankNo().trim();
                this.validateIsTrue(!bankNo.contains(" "), "银行账号不能包含空格！");
                this.validateIsTrue(bankNo.matches("^[0-9]+$"), "银行账号只能包含数字！");
            }

            // 身份证号码格式校验：不允许空格且位数固定（18位数）
            if (StringUtils.isNotEmpty(vo.getIdCard())) {
                String idCard = vo.getIdCard().trim();
                this.validateIsTrue(!idCard.contains(" "), "身份证号码不能包含空格！");
                this.validateIsTrue(idCard.length() == 18, "身份证号码必须为18位！");
                this.validateIsTrue(idCard.matches("^[0-9]{17}[0-9Xx]$"), "身份证号码格式不正确！");
            }

            // 手机号格式校验：不允许空格且位数固定（11位数）
            if (StringUtils.isNotEmpty(vo.getPhone())) {
                String phone = vo.getPhone().trim();
                this.validateIsTrue(!phone.contains(" "), "手机号不能包含空格！");
                this.validateIsTrue(phone.length() == 11, "手机号必须为11位！");
                this.validateIsTrue(phone.matches("^[0-9]{11}$"), "手机号只能包含数字！");
            }

            try {
                new BigDecimal(vo.getPayeeAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "收款金额，类型转换失败！");
            }
            try {
                new BigDecimal(vo.getPayAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "实际打款金额，类型转换失败！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, FeeCashTocImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        for (Map.Entry<Integer, FeeCashTocImportVo> row : data.entrySet()) {
            FeeCashTocImportVo vo = row.getValue();
//            vo.setPayeeAmount(new BigDecimal(vo.getPayeeAmountStr()));
            vo.setPayAmount(new BigDecimal(vo.getPayAmountStr()));
            vo.setPayeeAmount(vo.getPayAmount().divide(new BigDecimal("1.07"), 4, RoundingMode.HALF_UP));
            vo.setId(UUID.randomUUID().toString().replace("-", ""));
        }
        feeCashTocPageCacheHelper.importNewItem((String) params.get("cacheKey"), new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(data.values(), FeeCashTocImportVo.class, FeeCashTocDto.class, LinkedHashSet.class, ArrayList.class)));
        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<FeeCashTocImportVo> findCrmExcelVoClass() {
        return FeeCashTocImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "FEE_CASH_TOC_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "TOC支付导入模板";
    }
}
