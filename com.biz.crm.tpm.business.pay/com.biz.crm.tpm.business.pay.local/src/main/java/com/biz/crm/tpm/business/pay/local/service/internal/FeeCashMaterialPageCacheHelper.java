package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashMaterialRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashMaterialDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashMaterialVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 费用兑付明细分页缓存
 */
@Slf4j
@Component
public class FeeCashMaterialPageCacheHelper extends BusinessPageCacheHelper<FeeCashMaterialVo, FeeCashMaterialDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private FeeCashMaterialRepository feeCashMaterialRepository;
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return FeeCashConstant.CACHE_KEY_PREFIX_MATERIAL;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<FeeCashMaterialDto> getDtoClass() {
        return FeeCashMaterialDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<FeeCashMaterialVo> getVoClass() {
        return FeeCashMaterialVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param feeCashDetailDto
     * @param cacheKey
     */
    @Override
    public List<FeeCashMaterialDto> findDtoListFromRepository(FeeCashMaterialDto feeCashDetailDto, String cacheKey) {
        if (StringUtils.isBlank(feeCashDetailDto.getCashCode())) {
            return new ArrayList<>();
        }
        List<FeeCashMaterialVo> feeCashDetailVos = feeCashMaterialRepository.findByCode(feeCashDetailDto.getCashCode());
        if (CollectionUtils.isEmpty(feeCashDetailVos)) {
            return new ArrayList<>();
        }
        List<FeeCashMaterialDto> dtoList = (List<FeeCashMaterialDto>) nebulaToolkitService.copyCollectionByBlankList(feeCashDetailVos, FeeCashMaterialVo.class, FeeCashMaterialDto.class, HashSet.class, ArrayList.class);
        return dtoList;
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashMaterialDto> newItem(String cacheKey, List<FeeCashMaterialDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashMaterialDto> copyItem(String cacheKey, List<FeeCashMaterialDto> itemList) {
        List<FeeCashMaterialDto> newItemList = (List<FeeCashMaterialDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, FeeCashMaterialDto.class, FeeCashMaterialDto.class, HashSet.class, ArrayList.class);
        for (FeeCashMaterialDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 导入新增数据
     * @param cacheKey
     * @param itemList
     * @return
     */
    public void importNewItem(String cacheKey, List<FeeCashMaterialDto> itemList) {
        String redisCacheDataKey = this.getRedisCacheDataKey(cacheKey);
        String redisCacheIdKey = this.getRedisCacheIdKey(cacheKey);

        for (FeeCashMaterialDto newItem : itemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }

        Object[] newIdArr = itemList.stream().map(this::getDtoKey).toArray();
        redisService.lPushAll(redisCacheIdKey, this.getExpireTime(), newIdArr);

        Map<Object, FeeCashMaterialDto> updateMap = itemList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }

    /**
     * 获取Dto中的主键
     *
     * @param feeCashDetailDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(FeeCashMaterialDto feeCashDetailDto) {
        return feeCashDetailDto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param feeCashDetailDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(FeeCashMaterialDto feeCashDetailDto) {
        return feeCashDetailDto.getChecked();
    }

}
