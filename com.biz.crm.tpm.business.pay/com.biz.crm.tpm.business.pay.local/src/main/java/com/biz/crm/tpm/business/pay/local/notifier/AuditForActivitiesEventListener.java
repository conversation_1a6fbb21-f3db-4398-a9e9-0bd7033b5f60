package com.biz.crm.tpm.business.pay.local.notifier;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.BusinessStrategySettingExecutor;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditActivitiesDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditCustomerDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDto;
import com.biz.crm.tpm.business.pay.sdk.service.AuditService;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述：</br>实现费用核销对活动数据的监听
 * 实现在活动通过审核后判断活动绑定的活动细类是否为自动核销，如果是自动核销则自动创建核销数据
 *
 * <AUTHOR>
 * @date 2022/6/16
 */
@Component
@Slf4j
public class AuditForActivitiesEventListener implements ActivitiesEventListener {
  @Autowired
  private AuditService auditService;
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired
  private CostTypeCategoryVoService costTypeCategoryVoService;
  @Autowired
  private ActivitiesService activitiesService;
  @Autowired(required = false)
  private List<BusinessStrategySettingExecutor> businessStrategySettingExecutors;

  @Override
  public void onDelete(List<ActivitiesVo> vos) {

  }

  @Override
  public void onEnable(List<ActivitiesVo> vos) {

  }

  @Override
  public void onDisable(List<ActivitiesVo> vos) {

  }

  @Override
  public void onChange(ActivitiesVo oldVo, ActivitiesVo newVo) {

  }

  @Override
  public void onUpdateProcessStatus(ActivitiesVo vo, String processStatus) {
    // FIXME 检查表单绑定策略 通过表单绑定策略执行来取代该方法
    if (ObjectUtils.isEmpty(vo)) {
      return;
    }
    // 审批通过状态
    if (ProcessStatusEnum.PASS.getDictCode().equals(processStatus)) {
      // 检查活动关联的活动细类是否为自动核销
      String activitiesCode = vo.getActivitiesCode();
      ActivitiesVo activitiesVo = this.activitiesService.findDetailsByActivitiesCode(activitiesCode);
      List<ActivitiesDetailVo> activitiesDetailVos = activitiesVo.getActivitiesDetails();
      List<AuditDetailDto> auditDetailDtos = Lists.newArrayList();
      BigDecimal totalApplyAmount = BigDecimal.ZERO;
      for (ActivitiesDetailVo activitiesDetailVo : activitiesDetailVos) {
        // 检查活动明细关联的活动细类是否为自动核销
        String costTypeDetailCode = activitiesDetailVo.getCostTypeDetailCode();
        CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(costTypeDetailCode);
        if (ObjectUtils.isEmpty(costTypeDetailVo.getExecutionFromBody()) || !costTypeDetailVo.getExecutionFromBody().containsKey(ActivitiesConstant.STRATEGY_AUTO_AUDIT)) {
          continue;
        }
        JSONObject autoAuditBody = costTypeDetailVo.getExecutionFromBody().getJSONObject(ActivitiesConstant.STRATEGY_AUTO_AUDIT);
        boolean autoAudit = autoAuditBody.getBoolean(ActivitiesConstant.FORM_PROPERTIES_ENABLED);
        if (!autoAudit) {
          continue;
        }
        CostTypeCategoryVo costTypeCategoryVo = this.costTypeCategoryVoService.findByCode(activitiesDetailVo.getCostTypeCategoryCode());
        // 核销明细数据信息
        AuditDetailDto auditDetailDto = new AuditDetailDto();
        auditDetailDto.setActivitiesCode(vo.getActivitiesCode());
        auditDetailDto.setActivitiesName(vo.getActivitiesName());
        auditDetailDto.setActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode());
        auditDetailDto.setCostTypeCategoryCode(activitiesDetailVo.getCostTypeCategoryCode());
        auditDetailDto.setCostTypeCategoryName(costTypeCategoryVo.getCategoryName());
        auditDetailDto.setCostTypeDetailCode(costTypeDetailVo.getDetailCode());
        auditDetailDto.setCostTypeDetailName(costTypeDetailVo.getDetailName());
        auditDetailDto.setBudgetSubjectsCode(activitiesDetailVo.getBudgetSubjectsCode());
        auditDetailDto.setBudgetSubjectsName(activitiesDetailVo.getBudgetSubjectsName());
        auditDetailDto.setCostBudgetCode(activitiesDetailVo.getCostBudgetCode());
        auditDetailDto.setOrgCode(activitiesDetailVo.getOrgCode());
        auditDetailDto.setOrgName(activitiesDetailVo.getOrgName());
        auditDetailDto.setPayBy(activitiesDetailVo.getPayType());
        auditDetailDto.setPayByName(activitiesDetailVo.getPayTypeName());
        auditDetailDto.setApplyAmount(activitiesDetailVo.getApplyAmount());
        auditDetailDto.setAuditAmount(activitiesDetailVo.getApplyAmount());
        auditDetailDto.setAccountingSubjectsCode(costTypeCategoryVo.getBudgetSubjectsCode());
        auditDetailDto.setAccountingSubjectsName(costTypeCategoryVo.getBudgetSubjectsName());
        auditDetailDto.setIsFullAudit(BooleanEnum.TRUE.getCapital());

        AuditCustomerDto auditCustomerDto = new AuditCustomerDto();
        auditCustomerDto.setCustomerCode(activitiesDetailVo.getCustomerCode());
        auditCustomerDto.setCustomerName(activitiesDetailVo.getCustomerName());
        auditCustomerDto.setAuditAmount(activitiesDetailVo.getApplyAmount());
        auditDetailDto.setAuditCustomers(Lists.newArrayList(auditCustomerDto));

        auditDetailDtos.add(auditDetailDto);
        totalApplyAmount = totalApplyAmount.add(activitiesDetailVo.getApplyAmount());
      }
      if (CollectionUtils.isEmpty(auditDetailDtos)) {
        return;
      }
      // 创建核销数据
      AuditDto auditDto = new AuditDto();
      // 核销活动信息
      AuditActivitiesDto auditActivitiesDto = new AuditActivitiesDto();
      auditActivitiesDto.setActivitiesCode(vo.getActivitiesCode());
      auditActivitiesDto.setActivitiesName(vo.getActivitiesName() == null ? vo.getName() : vo.getActivitiesName());
      auditActivitiesDto.setBeginTime(vo.getBeginTime() == null ? vo.getStartTime() : vo.getBeginTime());
      auditActivitiesDto.setEndTime(vo.getEndTime());
      auditActivitiesDto.setCreateAccount(vo.getCreateAccount());
      auditActivitiesDto.setCreateName(vo.getCreateName());
      auditActivitiesDto.setCreateTime(vo.getCreateTime());
      auditDto.setAuditActivities(Lists.newArrayList(auditActivitiesDto));
      auditDto.setAuditName(StringUtils.join(auditActivitiesDto.getActivitiesName(), "-自动核销"));
      auditDto.setTotalApplyAmount(totalApplyAmount);
      // 核销活动明细
      auditDto.setAuditDetails(auditDetailDtos);
      this.auditService.creatForAutoAudit(auditDto);
    }
  }
}
