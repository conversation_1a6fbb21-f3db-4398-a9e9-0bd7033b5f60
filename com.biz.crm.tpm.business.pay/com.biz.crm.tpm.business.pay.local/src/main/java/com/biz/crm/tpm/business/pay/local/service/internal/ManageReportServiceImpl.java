package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerQueryByChannelDto;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.destination.sdk.service.MdmDestinationVoService;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.ManageReport;
import com.biz.crm.tpm.business.pay.local.repository.ManageReportRepository;
import com.biz.crm.tpm.business.pay.sdk.dto.ManageReportDto;
import com.biz.crm.tpm.business.pay.sdk.service.ManageReportService;
import com.biz.crm.tpm.business.pay.sdk.service.PosDataService;
import com.biz.crm.tpm.business.pay.sdk.vo.CustomerDestinationOrgMapping;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportImportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.FormulaGetValueComponent;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContext;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContextHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ManageReportServiceImpl implements ManageReportService {

    @Autowired(required = false)
    private ManageReportRepository manageReportRepository;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Autowired(required = false)
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;

    @Autowired(required = false)
    private PosDataService posDataService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Resource
    private MdmDestinationVoService mdmDestinationVoService;

    private static final String SPECIAL_CUSTOMER_DICT_KEY = "customer_destination_org_mapping";


    /**
     * 批量保存
     *
     * @param importVoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(List<ManageReportImportVo> importVoList) {
        FacturerUserDetails login = loginUserService.getLoginDetails(FacturerUserDetails.class);
        Collection<ManageReport> manageReports = nebulaToolkitService.copyCollectionByWhiteList(importVoList, ManageReportImportVo.class, ManageReport.class, LinkedHashSet.class, ArrayList.class);
        String orgCode = login.getOrgCode();
        String orgName = login.getOrgName();
        String positionCode = login.getPostCode();
        String account = login.getAccount();
        String realName = StringUtils.isNotBlank(login.getRealName()) ? login.getRealName() : login.getUsername();

        Date nowDate = new Date();
        manageReports.forEach(e -> {
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setTenantCode(TenantUtils.getTenantCode());

            e.setCreateAccount(account);
            e.setCreateName(realName);
            e.setCreateTime(nowDate);
            e.setModifyAccount(account);
            e.setModifyName(realName);
            e.setModifyTime(nowDate);

            e.setOrgCode(orgCode);
            e.setOrgName(orgName);
            e.setPositionCode(positionCode);
            e.setCreateFullName(realName);
        });
        manageReportRepository.saveBatch(manageReports);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @Override
    public void delete(List<String> idList) {
        Validate.notEmpty(idList, "删除时，id不能为空");
        List<ManageReport> list = manageReportRepository.findByIds(idList);
        Validate.notEmpty(list, "未找到对应的数据");
        manageReportRepository.removeByIds(idList);
    }

    /**
     * 按条件查询
     *
     * @param dto
     */
    @Override
    public List<ManageReportVo> findByDto(ManageReportDto dto) {
        return manageReportRepository.findByCondition(dto);
    }


    @Override
    public List<ManageReportVo> findListByCondition(WithholdingIncomeQueryDto queryDto) {
        List<ManageReportVo> dataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(queryDto.getOrgCodes())) {
            return Lists.newArrayList();
        }
        MarsAuthorityContext marsAuthorityContext = MarsAuthorityContextHolder.getContext();
        marsAuthorityContext.setListCode(null);
        List<String> yearsList = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(queryDto.getYears())) {
            yearsList.add(queryDto.getYears());
        } else if (CollectionUtils.isNotEmpty(queryDto.getYearsList())) {
            yearsList.addAll(queryDto.getYearsList());
        }
        queryDto.setYearsList(null);
        for (String s : yearsList) {
            queryDto.setYears(s);
            List<ManageReportVo> resultList = calManageReportListV2(queryDto);
            if (CollectionUtils.isNotEmpty(resultList)) {
                dataList.addAll(resultList);
            }
        }
        return dataList;
    }

    public List<ManageReportVo> calManageReportListV2(WithholdingIncomeQueryDto queryDto) {
        //这一步主要是为了去查询当月的发货数据
        Map<String, List<CustomerVo>> customerMap = customerVoService.findCustomerListByOrgCodes(queryDto.getOrgCodes());
        Map<String, String> customerOrgMap = Maps.newHashMap();
        for (Map.Entry<String, List<CustomerVo>> entry : customerMap.entrySet()) {
            for (CustomerVo vo : entry.getValue()) {
                customerOrgMap.put(vo.getCustomerCode(), vo.getOrgCode());
            }
        }

        List<String> customerCodes = customerMap.values().stream().flatMap(List::stream).map(x -> x.getCustomerCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(queryDto.getChannelDepartmentCodeList())) {
            CustomerQueryByChannelDto dto = new CustomerQueryByChannelDto();
            dto.setOrgCodeList(queryDto.getOrgCodes());
            dto.setChannelDepartmentCodeList(queryDto.getChannelDepartmentCodeList());
            List<CustomerVo> customerVoList = customerVoService.findCustomerListByChannelAndOrg(dto);
            if (CollectionUtils.isEmpty(customerVoList)) {
                return Lists.newArrayList();
            }
            customerCodes = customerVoList.stream().map(x -> x.getCustomerCode()).distinct().collect(Collectors.toList());
        }

        if (ObjectUtils.isEmpty(queryDto.getYears()) && CollectionUtils.isEmpty(queryDto.getYearsList())) {
            return Lists.newArrayList();
        }

        List<ManageReportVo> dataList = Lists.newArrayList();

        List<List<String>> customerPartitionList = Lists.partition(customerCodes, 800);
        for (List<String> list : customerPartitionList) {
            WithholdingIncomeQueryDto dto = JsonUtils.convert(queryDto, WithholdingIncomeQueryDto.class);
            dto.setCustomerCodes(list);
            List<ManageReportVo> resultList = manageReportRepository.findListByCondition(dto);
            if (!CollectionUtils.isEmpty(resultList)) {
                dataList.addAll(resultList);
            }
        }
        return dataList;
    }

    public List<ManageReportVo> calManageReportList(WithholdingIncomeQueryDto queryDto) {
        //这一步主要是为了去查询当月的发货数据
        Map<String, List<CustomerVo>> customerMap = customerVoService.findCustomerListByOrgCodes(queryDto.getOrgCodes());
        Map<String, String> customerOrgMap = Maps.newHashMap();
        for (Map.Entry<String, List<CustomerVo>> entry : customerMap.entrySet()) {
            for (CustomerVo vo : entry.getValue()) {
                customerOrgMap.put(vo.getCustomerCode(), vo.getOrgCode());
            }
        }

        List<String> customerCodes = customerMap.values().stream().flatMap(List::stream).map(x -> x.getCustomerCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(queryDto.getChannelDepartmentCodeList())) {
            CustomerQueryByChannelDto dto = new CustomerQueryByChannelDto();
            dto.setOrgCodeList(queryDto.getOrgCodes());
            dto.setChannelDepartmentCodeList(queryDto.getChannelDepartmentCodeList());
            List<CustomerVo> customerVoList = customerVoService.findCustomerListByChannelAndOrg(dto);
            if (CollectionUtils.isEmpty(customerVoList)) {
                return Lists.newArrayList();
            }
            customerCodes = customerVoList.stream().map(x -> x.getCustomerCode()).distinct().collect(Collectors.toList());
        }

        if (ObjectUtils.isEmpty(queryDto.getYears()) && CollectionUtils.isEmpty(queryDto.getYearsList())) {
            return Lists.newArrayList();
        }

        List<ManageReportVo> dataList = Lists.newArrayList();

        List<List<String>> customerPartitionList = Lists.partition(customerCodes, 800);
        for (List<String> list : customerPartitionList) {
            WithholdingIncomeQueryDto dto = JsonUtils.convert(queryDto, WithholdingIncomeQueryDto.class);
            dto.setCustomerCodes(list);
            List<ManageReportVo> resultList = manageReportRepository.findListByCondition(dto);
            if (!CollectionUtils.isEmpty(resultList)) {
                dataList.addAll(resultList);
            }
        }
        //如果为空的情况下 再去查询实际收入
        if (CollectionUtils.isEmpty(dataList)) {
            List<DmsWarehouseOrderDetailVo> orderDetailList = Lists.newArrayList();
            Map<String, Map<String, BigDecimal>> posDataYearsMap = Maps.newHashMap();
            if (ObjectUtils.isNotEmpty(queryDto.getYears())) {
                String date = queryDto.getYears() + "-01";
                Map<String, String> map = FormulaGetValueComponent.getDateMonthly(date, DateUtil.DEFAULT_YEAR_MONTH_DAY);
                //计算pos拆分占比
                Map<String, BigDecimal> posDataMap = this.calPosData(queryDto.getYears());
                List<String> finalCustomerCodes = customerCodes;
                posDataMap = posDataMap.entrySet().stream()
                        .filter(entry -> {
                            // 分割 key 获取客户编码部分
                            String key = entry.getKey();
                            String[] parts = key.split(":", 2); // 按照冒号分割，最多分成2部分
                            String customerCode = parts[0];
                            return finalCustomerCodes.contains(customerCode);
                        })
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue
                        ));
                List<String> excludeCustomerCodes = Lists.newArrayList();
                for (Map.Entry<String, BigDecimal> entry : posDataMap.entrySet()) {
                    excludeCustomerCodes.add(entry.getKey().split(":")[0]);
                }

                posDataYearsMap.put(queryDto.getYears(), posDataMap);
                for (List<String> list : customerPartitionList) {
                    list = list.stream().filter(x -> !excludeCustomerCodes.contains(x)).collect(Collectors.toList());
                    TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
                    dto.setCustomerCodes(list);
                    dto.setSignSearchStartTime(map.get(FormulaGetValueComponent.START_DATE));
                    dto.setSignSearchEndTime(map.get(FormulaGetValueComponent.END_DATE));
                    dto.setItemTypeList(Lists.newArrayList("normalGoods"));
                    List<DmsWarehouseOrderDetailVo> orderDetailVos = dmsWarehouseOrderDetailVoService.findSapDeliveryToSfa(dto);
                    if (CollectionUtils.isNotEmpty(orderDetailVos)) {
                        orderDetailVos.forEach(x -> x.setYears(queryDto.getYears()));
                        orderDetailList.addAll(orderDetailVos);
                    }
                }
            } else if (CollectionUtils.isNotEmpty(queryDto.getYearsList())) {
                for (String s : queryDto.getYearsList()) {
                    String date = s + "-01";
                    Map<String, String> map = FormulaGetValueComponent.getDateMonthly(date, DateUtil.DEFAULT_YEAR_MONTH_DAY);

                    Map<String, BigDecimal> posDataMap = this.calPosData(s);
                    List<String> excludeCustomerCodes = Lists.newArrayList();
                    for (Map.Entry<String, BigDecimal> entry : posDataMap.entrySet()) {
                        excludeCustomerCodes.add(entry.getKey().split(":")[0]);
                    }
                    posDataYearsMap.put(s, posDataMap);
                    for (List<String> list : customerPartitionList) {
                        list = list.stream().filter(x -> !excludeCustomerCodes.contains(x)).collect(Collectors.toList());
                        TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
                        dto.setCustomerCodes(list);
                        dto.setSignSearchStartTime(map.get(FormulaGetValueComponent.START_DATE));
                        dto.setSignSearchEndTime(map.get(FormulaGetValueComponent.END_DATE));
                        dto.setItemTypeList(Lists.newArrayList("normalGoods"));
                        List<DmsWarehouseOrderDetailVo> orderDetailVos = dmsWarehouseOrderDetailVoService.findSapDeliveryToSfa(dto);
                        if (CollectionUtils.isNotEmpty(orderDetailVos)) {
                            orderDetailVos.forEach(x -> x.setYears(s));
                            orderDetailList.addAll(orderDetailVos);
                        }

                    }
                }
            }
            List<DmsWarehouseOrderDetailVo> posOrderDetailList = Lists.newArrayList();
            //pos拆分占比
            for (Map.Entry<String, Map<String, BigDecimal>> entry : posDataYearsMap.entrySet()) {
                String date = entry.getKey() + "-01";
                Map<String, String> map = FormulaGetValueComponent.getDateMonthly(date, DateUtil.DEFAULT_YEAR_MONTH_DAY);
                List<String> posCustomerCodes = Lists.newArrayList();
                for (Map.Entry<String, BigDecimal> e : entry.getValue().entrySet()) {
                    String[] cusOrg = e.getKey().split(":");
                    String customerCode = cusOrg[0];
                    posCustomerCodes.add(customerCode);
                }
                TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
                dto.setCustomerCodes(posCustomerCodes);
                dto.setSignSearchStartTime(map.get(FormulaGetValueComponent.START_DATE));
                dto.setSignSearchEndTime(map.get(FormulaGetValueComponent.END_DATE));
                dto.setItemTypeList(Lists.newArrayList("normalGoods"));
                List<DmsWarehouseOrderDetailVo> orderDetailVos = dmsWarehouseOrderDetailVoService.findSapDeliveryToSfa(dto);
                for (Map.Entry<String, BigDecimal> posEntry : entry.getValue().entrySet()) {
                    String[] cusOrg = posEntry.getKey().split(":");
                    String customerCode = cusOrg[0];
                    String orgCode = cusOrg[1];
                    for (DmsWarehouseOrderDetailVo vo : orderDetailVos) {
                        if (vo.getCustomerCode().equals(customerCode)) {
                            DmsWarehouseOrderDetailVo copyVo = JsonUtils.convert(vo, DmsWarehouseOrderDetailVo.class);
                            BigDecimal rate = posEntry.getValue();
                            BigDecimal amount = copyVo.getSignAmount().multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            copyVo.setSignAmount(amount);
                            copyVo.setOrgCode(orgCode);
                            copyVo.setYears(entry.getKey());
                            posOrderDetailList.add(copyVo);
                        }
                    }
                }

            }

            if (CollectionUtils.isNotEmpty(orderDetailList)) {
                // 查询天虹类特殊客户
                Map<String, List<CustomerDestinationOrgMapping>> destinationOrgMap = specialCustomer();
                Map<String, BigDecimal> specialCustomerAmountMap =
                        amountGroupByCustomerDirection(destinationOrgMap, orderDetailList);

                Map<String, List<DmsWarehouseOrderDetailVo>> orderDetailMap = orderDetailList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode() + ":" + x.getYears()));
                for (Map.Entry<String, List<DmsWarehouseOrderDetailVo>> entry : orderDetailMap.entrySet()) {
                    String[] keys = entry.getKey().split(":");
                    String customerCode = keys[0];
                    String years = keys[1];
                    BigDecimal deliveryData = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getSignAmount()))
                            .map(DmsWarehouseOrderDetailVo::getSignAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal amount = deliveryData;
                    String orgCode = customerOrgMap.get(customerCode);

                    ManageReportVo vo = new ManageReportVo();
                    vo.setDepartmentCode(orgCode);
                    vo.setCustomerCode(customerCode);
                    vo.setYearMonthLy(years);
                    vo.setAmount(BigDecimal.ZERO);
                    if (destinationOrgMap.containsKey(customerCode)) {
                        // 特殊客户逻辑
                        String key = StrUtil.join(StrPool.DOT, customerCode, orgCode);
                        amount = specialCustomerAmountMap.get(key);
                        if (Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) > 0) {
                            vo.setAmount(amount);
                        }
                        continue;
                    }
                    vo.setAmount(amount);
                    dataList.add(vo);
                }
            }
            if (CollectionUtils.isNotEmpty(posOrderDetailList)) {
                Map<String, List<DmsWarehouseOrderDetailVo>> orderDetailMap = posOrderDetailList.stream().collect(
                        Collectors.groupingBy(x -> x.getCustomerCode() + ":" + x.getYears() + ":" + x.getOrgCode()));
                for (Map.Entry<String, List<DmsWarehouseOrderDetailVo>> entry : orderDetailMap.entrySet()) {
                    String[] keys = entry.getKey().split(":");
                    String customerCode = keys[0];
                    String years = keys[1];
                    String orgCode = keys[2];
                    BigDecimal deliveryData = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getSignAmount()))
                            .map(DmsWarehouseOrderDetailVo::getSignAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal amount = deliveryData;

                    ManageReportVo vo = new ManageReportVo();
                    vo.setDepartmentCode(orgCode);
                    vo.setCustomerCode(customerCode);
                    vo.setYearMonthLy(years);
                    vo.setAmount(amount);
                    dataList.add(vo);
                }
            }
        }
        return dataList;
    }

    /**
     * 计算pos拆分占比
     *
     * @param years
     * @return
     */
    private Map<String, BigDecimal> calPosData(String years) {
        String date = years + "-01";
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        // 获取上个月
        LocalDate lastMonth = localDate.minusMonths(1);
        // 获取上上个月
        LocalDate monthBeforeLast = localDate.minusMonths(2);
        //根据数据字典查询客户
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(FormulaGetValueComponent.CUSTOMER_SPLITTING);
        //上月客户
        List<String> lastMonthCustomer = dictDataVos.stream().filter(e -> FormulaGetValueComponent.LAST_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());
        //本月客户
        List<String> currentMonthCustomer = dictDataVos.stream().filter(e -> FormulaGetValueComponent.CURRENT_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());

        List<PosDataVo> posDataVoList = Lists.newArrayList();
        //判断上个月的客户数据不为空
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastMonthCustomer)) {
            String dateStr = monthBeforeLast.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            List<String> lastCustomerCodes = lastMonthCustomer;
//            for (String s : lastMonthCustomer) {
//                if (!customerCodeSet.add(s)) {
//                    lastCustomerCodes.add(s);
//                }
//            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastCustomerCodes)) {
                PosDataVo dto = new PosDataVo();
                dto.setYears(dateStr);
                dto.setCustomerCodes(lastCustomerCodes);
                Page<PosDataVo> page = posDataService.findList(PageRequest.of(0, Integer.MAX_VALUE), dto);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(page.getRecords())) {
                    posDataVoList.addAll(page.getRecords());
                }
            }
        }
        //判断本月客户数据不为空
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(currentMonthCustomer)) {
            String dateStr = lastMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            List<String> customerCodes = currentMonthCustomer;
//            for (String s : currentMonthCustomer) {
//                if (!customerCodeSet.add(s)) {
//                    customerCodes.add(s);
//                }
//            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(customerCodes)) {
                PosDataVo dto = new PosDataVo();
                dto.setYears(dateStr);
                dto.setCustomerCodes(customerCodes);
                Page<PosDataVo> page = posDataService.findList(PageRequest.of(0, Integer.MAX_VALUE), dto);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(page.getRecords())) {
                    posDataVoList.addAll(page.getRecords());
                }
            }
        }

        Map<String, BigDecimal> postDataMap = Maps.newHashMap();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(posDataVoList)) {
            Map<String, BigDecimal> posCusAmountMap = posDataVoList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode(),
                    Collectors.mapping(PosDataVo::getAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            for (PosDataVo posDataVo : posDataVoList) {
                if (posCusAmountMap.containsKey(posDataVo.getCustomerCode())) {
                    BigDecimal amount = posCusAmountMap.get(posDataVo.getCustomerCode());
                    BigDecimal rate = BigDecimal.ZERO;
                    if (amount.compareTo(BigDecimal.ZERO) == 1) {
                        rate = posDataVo.getAmount().divide(amount, 8, BigDecimal.ROUND_HALF_DOWN);
                    }
                    postDataMap.put(posDataVo.getCustomerCode() + ":" + posDataVo.getOrgCode(), rate);
                }
            }
        }
        return postDataMap;
    }


    private Map<String, List<CustomerDestinationOrgMapping>> specialCustomer() {
        Map<String, List<CustomerDestinationOrgMapping>> result = new HashMap<>();
        //根据数据字典查询客户
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(SPECIAL_CUSTOMER_DICT_KEY);
        if (CollUtil.isEmpty(dictDataVos)) {
            return result;
        }
        List<String> destinationCodeList = new ArrayList<>();
        List<CustomerDestinationOrgMapping> destinationOrgMappings = new ArrayList<>();
        for (DictDataVo dataVo : dictDataVos) {
            String destinationCode = dataVo.getDictCode();
            String orgCode = dataVo.getDictValue();
            if (StrUtil.isBlank(destinationCode) || StrUtil.isBlank(orgCode)) {
                continue;
            }
            destinationCodeList.add(destinationCode);
            CustomerDestinationOrgMapping destinationOrgMapping =
                    new CustomerDestinationOrgMapping(destinationCode, orgCode);
            destinationOrgMappings.add(destinationOrgMapping);
        }
        // 根据送达方查询客户
        List<MdmDestinationVo> destinationVoList =
                mdmDestinationVoService.findByDestinationCodes(destinationCodeList);
        if (CollUtil.isEmpty(destinationVoList)) {
            return result;
        }
        // 送达方-客户
        Map<String, String> destinationCustomerMap = destinationVoList.stream().collect(
                Collectors.toMap(MdmDestinationVo::getDestinationCode,
                        MdmDestinationVo::getCustomerCode, (v1, v2) -> v1));
        destinationOrgMappings.forEach(mapping -> {
            String customerCode = destinationCustomerMap.get(mapping.getDestinationCode());
            if (StrUtil.isNotBlank(customerCode)) {
                mapping.setCustomerCode(customerCode);
            }
        });

        return destinationOrgMappings.stream()
                .filter(mapping -> StrUtil.isNotBlank(mapping.getCustomerCode()))
                .collect(Collectors.groupingBy(CustomerDestinationOrgMapping::getCustomerCode));
    }

    private Map<String, BigDecimal> amountGroupByCustomerDirection(Map<String, List<CustomerDestinationOrgMapping>> specialCustomerMap,
                                                                   List<DmsWarehouseOrderDetailVo> orderDetailVos) {
        if (MapUtil.isEmpty(specialCustomerMap)) {
            return new HashMap<>();
        }
        List<DmsWarehouseOrderDetailVo> specialCustomerOrders = orderDetailVos.stream()
                .filter(order -> Objects.nonNull(order.getTotalAmount()))
                //.filter(order -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(order.getWarehouseOrderType()))
                .filter(order -> specialCustomerMap.containsKey(order.getCustomerCode()))
                .filter(order -> StrUtil.isNotBlank(order.getDestinationCode()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(specialCustomerOrders)) {
            return new HashMap<>();
        }
        Map<String, BigDecimal> amountGroupByCustomerDirection = new HashMap<>();
        for (DmsWarehouseOrderDetailVo detailVo : specialCustomerOrders) {
            // 客户编码
            String customerCode = detailVo.getCustomerCode();
            // 送达方编码
            String destinationCode = detailVo.getDestinationCode();
            // 商品编码
            String goodsCode = detailVo.getGoodsCode();
            // 该客户关联的 送达方，以及对应的部门映射
            List<CustomerDestinationOrgMapping> destinationOrgMappings = specialCustomerMap.get(customerCode);
            if (CollUtil.isEmpty(destinationOrgMappings)) {
                continue;
            }
            // 未找到映射，直接忽略
            CustomerDestinationOrgMapping mapping = destinationOrgMappings.stream()
                    .filter(item -> StrUtil.equals(item.getDestinationCode(), destinationCode))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(mapping)) {
                continue;
            }
            // 送达方映射的组织编码
            String orgCode = mapping.getOrgCode();
            // 客户 + 组织
            String key = StrUtil.join(StrPool.DOT, customerCode, orgCode);
            // 金额汇总
            BigDecimal amount = amountGroupByCustomerDirection.get(key);
            if (Objects.isNull(amount)) {
                amountGroupByCustomerDirection.put(key, detailVo.getTotalAmount());
            } else {
                BigDecimal sum = amount.add(detailVo.getTotalAmount());
                amountGroupByCustomerDirection.put(key, sum);
            }
        }
        return amountGroupByCustomerDirection;
    }
}
