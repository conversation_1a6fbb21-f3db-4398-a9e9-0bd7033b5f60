package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingCollect;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用预提汇总(WithHoldingCollect)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-07-01 11:23:06
 */
public interface WithHoldingCollectMapper extends BaseMapper<WithHoldingCollect> {

    void updateCollectAmount(@Param("collectCode") String collectCode);
}

