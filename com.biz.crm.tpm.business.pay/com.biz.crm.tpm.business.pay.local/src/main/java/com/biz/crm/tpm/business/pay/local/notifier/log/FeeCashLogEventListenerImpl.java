package com.biz.crm.tpm.business.pay.local.notifier.log;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.FeeCashLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.event.log.FeeCashLogEventListener;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class FeeCashLogEventListenerImpl implements FeeCashLogEventListener {
    
    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 创建事件
     *
     * @param eventDto
     */
    @Override
    public void onCreate(FeeCashLogEventDto eventDto) {
        FeeCashDto newest = eventDto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(null);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    /**
     * 删除事件
     *
     * @param eventDto
     */
    @Override
    public void onDelete(FeeCashLogEventDto eventDto) {
        List<FeeCashDto> newList = Lists.newArrayList();
        if (Objects.nonNull(eventDto.getNewest())) {
            newList.add(eventDto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(eventDto.getNewestList())) {
            newList.addAll(eventDto.getNewestList());
        }
        List<FeeCashDto> oldList = (List<FeeCashDto>) this.nebulaToolkitService.copyCollectionByBlankList(newList, FeeCashDto.class, FeeCashDto.class, HashSet.class, ArrayList.class);
        Map<String, FeeCashDto> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(FeeCashDto::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            FeeCashDto original = oldMap.getOrDefault(newest.getId(), new FeeCashDto());
            original.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setDelFlag(DelFlagStatusEnum.DELETE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    /**
     * 更新日志
     *
     * @param eventDto
     */
    @Override
    public void onUpdate(FeeCashLogEventDto eventDto) {
        FeeCashDto original = eventDto.getOriginal();
        FeeCashDto newest = eventDto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }
}
