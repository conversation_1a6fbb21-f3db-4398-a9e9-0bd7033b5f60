package com.biz.crm.tpm.business.pay.local.entity;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.TenantEntity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Index;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：费用明细商品;
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "AuditProduct",description = "费用明细商品")
@TableName("tpm_audit_product")
@Getter
@Setter
@Entity(name = "tpm_audit_product")
@org.hibernate.annotations.Table(appliesTo = "tpm_audit_product", comment = "费用明细商品")
@Table(name = "tpm_audit_product")
public class AuditProduct  extends TenantEntity{
  
  /** 费用核销编号 */
  @ApiModelProperty(name = "费用核销编号",notes = "费用核销编号")
  @Column(name = "audit_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '费用核销编号 '")
  private String auditCode;
  
  /** 费用核销明细编号 */
  @ApiModelProperty(name = "费用核销明细编号",notes = "费用核销明细编号")
  @Column(name = "audit_detail_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '费用核销明细编号 '")
  private String auditDetailCode;
  
  /** 商品编码 */
  @ApiModelProperty(name = "商品编码",notes = "商品编码")
  @Column(name = "product_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '商品编码 '")
  private String productCode;
  
  /** 商品名称 */
  @ApiModelProperty(name = "商品名称",notes = "商品名称")
  @Column(name = "product_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '商品名称 '")
  private String productName;

}