package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@ApiModel(value = "OrderAdjust", description = "订单调整")
@TableName("tpm_order_adjust")
@Getter
@Setter
@Entity(name = "tpm_order_adjust")
@org.hibernate.annotations.Table(appliesTo = "tpm_order_adjust", comment = "订单调整")
@Table(name = "tpm_order_adjust", indexes = {@Index(name = "order_adjust_idx1", columnList = "adjust_code")})
public class OrderAdjust extends TenantFlagOpEntity {

    @ApiModelProperty("调整编码")
    @Column(name = "adjust_code", length = 64, columnDefinition = "varchar(64) COMMENT '调整编码'")
    private String adjustCode;

    @ApiModelProperty("调整名称")
    @Column(name = "adjust_name", length = 128, columnDefinition = "varchar(128) COMMENT '调整名称'")
    private String adjustName;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "VARCHAR(32) COMMENT '公司编码'")
    private String companyCode;

    @ApiModelProperty("公司名称")
    @Column(name = "company_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '公司名称'")
    private String companyName;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("部门编码")
    @Column(name = "department_code", columnDefinition = "varchar(32) comment '部门编码'")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    @Column(name = "department_name", columnDefinition = "varchar(128) comment '部门名称'")
    private String departmentName;

    @ApiModelProperty("确认状态")
    @Column(name = "confirm_status", columnDefinition = "varchar(20) comment '确认状态'")
    private String confirmStatus;
}
