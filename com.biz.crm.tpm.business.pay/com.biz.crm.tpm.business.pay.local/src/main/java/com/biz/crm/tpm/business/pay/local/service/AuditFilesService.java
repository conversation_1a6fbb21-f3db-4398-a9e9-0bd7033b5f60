package com.biz.crm.tpm.business.pay.local.service;

import com.biz.crm.tpm.business.pay.sdk.dto.AuditFilesDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditFilesVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**
 * 费用核销附件;(tpm_audit_files)表服务接口
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
public interface AuditFilesService{
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<AuditFilesVo> findByConditions(Pageable pageable, AuditFilesDto dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AuditFilesVo findById(String id);
    /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  List<AuditFilesVo> findByAuditCode(String code);
  /**
   * 新增数据
   *
   * @param auditFilesDto 实体对象
   * @return 新增结果
   */
  AuditFilesVo create(AuditFilesDto auditFilesDto);
  /**
   * 批量新增
   * @param auditFilesDtos
   * @return
   */
  List<AuditFilesVo> createBatch(List<AuditFilesDto> auditFilesDtos);
  /**
   * 修改新据
   *
   * @param auditFilesDto 实体对象
   * @return 修改结果
   */
  AuditFilesVo update(AuditFilesDto auditFilesDto);
  /**
   * 批量更新数据
   *
   * @param auditFilesDtos
   * @return
   */
  List<AuditFilesVo> updateBatch(List<AuditFilesDto> auditFilesDtos);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(List<String> ids);

  /**
   * 根据费用核销编号删除数据
   * @param auditCode
   */
  void deleteByAuditCode(List<String> auditCode);
}