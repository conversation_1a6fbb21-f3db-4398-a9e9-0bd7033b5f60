package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：核销账单明细记录;
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@ApiModel(value = "AuditBillRecord",description = "核销账单明细记录")
@TableName("tpm_audit_bill_record")
@Getter
@Setter
@Entity(name = "tpm_audit_bill_record")
@org.hibernate.annotations.Table(appliesTo = "tpm_audit_bill_record", comment = "核销账单明细记录")
@Table(name = "tpm_audit_bill_record")
public class AuditBillRecord  extends TenantEntity {

  /** 活动明细编码 */
  @ApiModelProperty(name = "活动明细编码",notes = "活动明细编码")
  @Column(name = "activities_detail_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动明细编码 '")
  private String activitiesDetailCode;

  /** 申请金额 */
  @ApiModelProperty(name = "申请金额",notes = "申请金额")
  @Column(name = "change_amount", nullable = false, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '申请金额 '")
  private BigDecimal changeAmount;

  /** 业务编号 */
  @ApiModelProperty(name = "业务编号",notes = "已核销金额")
  @Column(name = "business_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '业务编号 '")
  private String businessCode;

  /** 操作类型 */
  @ApiModelProperty(name = "操作类型",notes = "操作类型：0、初始化，1、新增，2、减少")
  @Column(name = "type", nullable = false,  columnDefinition = "INT COMMENT '操作类型 '")
  private Integer type;

  /**
   * 创建时间
   */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "create_time" , length = 20, columnDefinition = "datetime COMMENT '创建时间 '")
  private Date createTime;

  /**
   * 创建人账号
   */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "create_account" , length = 60, columnDefinition = "varchar(60) COMMENT '创建人账号'")
  private String createAccount;

  /**
   * 创建人名称
   */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "create_name" , length = 60, columnDefinition = "varchar(60) COMMENT '创建人名称'")
  private String createName;

  /**
   * 修改时间
   */
  @TableField(fill = FieldFill.UPDATE,updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "modify_time" , length = 20, columnDefinition = "datetime COMMENT '修改时间'")
  private Date modifyTime;

  /**
   * 修改人账号
   */
  @TableField(fill = FieldFill.UPDATE,updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "modify_account" , length = 60, columnDefinition = "varchar(40) COMMENT '修改人账号'")
  private String modifyAccount;

  /**
   * 修改人名称
   */
  @TableField(fill = FieldFill.UPDATE,updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "modify_name" , length = 60, columnDefinition = "varchar(60) COMMENT '修改人名称'")
  private String modifyName;
}
