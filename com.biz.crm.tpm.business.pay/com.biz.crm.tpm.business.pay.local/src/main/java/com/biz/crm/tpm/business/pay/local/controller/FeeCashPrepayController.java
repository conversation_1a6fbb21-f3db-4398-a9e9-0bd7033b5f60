package com.biz.crm.tpm.business.pay.local.controller;

import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashPrepayService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPrepayVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "费用兑付-预付明细功能接口")
@RestController
@RequestMapping("/v1/pay/feeCashPrepay")
@Slf4j
public class FeeCashPrepayController extends BusinessPageCacheController<FeeCashPrepayVo, FeeCashPrepayDto> {

    @Autowired(required = false)
    private FeeCashPrepayService feeCashPrepayService;

    /**
     * 计算预付明细
     *
     * @param cacheKeyDetail 明细缓存键
     * @param cacheKeyPrepay 预付明细缓存键
     * @return
     */
    @ApiOperation(value = "计算预付明细")
    @GetMapping("calThisReversedAmount")
    public Result<?> calThisReversedAmount(
            @ApiParam(name = "cacheKeyDetail", value = "明细缓存键") @RequestParam String cacheKeyDetail,
            @ApiParam(name = "cacheKeyPrepay", value = "预付明细缓存键") @RequestParam String cacheKeyPrepay,
             @ApiParam(name = "copyFydf", value = "是否复制费用兑付进来的") @RequestParam(required = false) Integer copyFydf
    ) {
        try {
            if(null==copyFydf){
                copyFydf=0;
            }
            this.feeCashPrepayService.calThisReversedAmount(cacheKeyDetail, cacheKeyPrepay,copyFydf);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除更新预付明细
     *
     * @param cacheKeyDetail 明细缓存键
     * @param cacheKeyPrepay 预付明细缓存键
     * @return
     */
    @ApiOperation(value = "删除更新预付明细")
    @GetMapping("updatePrepay")
    public Result<?> updatePrepay(
            @ApiParam(name = "cacheKeyDetail", value = "明细缓存键") @RequestParam String cacheKeyDetail,
            @ApiParam(name = "cacheKeyPrepay", value = "预付明细缓存键") @RequestParam String cacheKeyPrepay) {
        try {
            this.feeCashPrepayService.updatePrepay(cacheKeyDetail, cacheKeyPrepay);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
