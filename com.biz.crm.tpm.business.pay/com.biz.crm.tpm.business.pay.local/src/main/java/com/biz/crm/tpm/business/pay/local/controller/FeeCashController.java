package com.biz.crm.tpm.business.pay.local.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.pay.local.service.internal.process.EncryptionApiComponent;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTicketDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashSendHecService;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Api(tags = "费用兑付功能接口")
@RestController
@RequestMapping("/v1/pay/feeCash")
@Slf4j
public class FeeCashController extends BusinessPageCacheController<FeeCashTicketVo, FeeCashTicketDto> {

    @Autowired(required = false)
    private FeeCashService feeCashService;

    @Autowired(required = false)
    private FeeCashSendHecService feeCashSendHecService;

    @Autowired(required = false)
    private EncryptionApiComponent encryptionApiComponent;

    @Autowired(required = false)
    private RedisLockService redisLockService;

    @Resource
    private LoginUserService loginUserService;

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    @ApiOperation(value = "通过编号查询单条数据")
    @GetMapping("findByCode")
    public Result<FeeCashVo> findByCode(@ApiParam(name = "code", value = "编号", required = true) String code) {
        try {
            FeeCashVo cashVo = this.feeCashService.findByCode(code);
            return Result.ok(cashVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 按贷项单号查询票扣
     *
     * @param creditCode 编号
     * @return
     */
    @ApiOperation(value = "按贷项单号查询票扣")
    @GetMapping("findTicketByCreditCode")
    public Result<List<FeeCashTicketVo>> findTicketByCreditCode(@ApiParam(name = "creditCode", value = "编号", required = true) String creditCode) {
        try {
            return Result.ok(this.feeCashService.findTicketByCreditCode(creditCode));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cashDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @PostMapping
    public Result<?> create(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam(required = false) String cacheKey,
                            @ApiParam(name = "cacheKeyToc", value = "TOC缓存键") @RequestParam(required = false) String cacheKeyToc,
                            @ApiParam(name = "cacheKeyDetail", value = "明细缓存键") @RequestParam(required = false) String cacheKeyDetail,
                            @ApiParam(name = "cacheKeyPrepay", value = "预付明细缓存键") @RequestParam(required = false) String cacheKeyPrepay,
                            @ApiParam(name = "cacheKeyInvoice", value = "发票缓存键") @RequestParam(required = false) String cacheKeyInvoice,
                            @ApiParam(name = "cacheKeyMaterial", value = "采购物料缓存键") @RequestParam(required = false) String cacheKeyMaterial,
                            @ApiParam(name = "cashDto", value = "兑付") @RequestBody FeeCashDto cashDto) {
        FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
        String lockKey = FeeCashConstant.LOCK_PREFIX + loginUserDetails.getUsername();
        Boolean lockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, 20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        try {
            cashDto.setBeStaging(BooleanEnum.FALSE.getCapital());
            this.feeCashService.create(cashDto, false, cacheKey, cacheKeyToc, cacheKeyDetail, cacheKeyPrepay, cacheKeyInvoice, cacheKeyMaterial, false, false);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
        }
    }

    /**
     * 修改数据
     *
     * @param cashDto 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @PatchMapping
    public Result<?> update(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam(required = false) String cacheKey,
                            @ApiParam(name = "cacheKeyToc", value = "TOC缓存键") @RequestParam(required = false) String cacheKeyToc,
                            @ApiParam(name = "cacheKeyDetail", value = "明细缓存键") @RequestParam(required = false) String cacheKeyDetail,
                            @ApiParam(name = "cacheKeyPrepay", value = "预付明细缓存键") @RequestParam(required = false) String cacheKeyPrepay,
                            @ApiParam(name = "cacheKeyInvoice", value = "发票缓存键") @RequestParam(required = false) String cacheKeyInvoice,
                            @ApiParam(name = "cacheKeyMaterial", value = "采购物料缓存键") @RequestParam(required = false) String cacheKeyMaterial,
                            @ApiParam(name = "cashDto", value = "兑付") @RequestBody FeeCashDto cashDto) {
        String lockKey = FeeCashConstant.LOCK_PREFIX + cashDto.getCashCode();
        Boolean lockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, 20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        try {
            cashDto.setBeStaging(BooleanEnum.FALSE.getCapital());
            this.feeCashService.update(cashDto, cacheKey, cacheKeyToc, cacheKeyDetail, cacheKeyPrepay, cacheKeyInvoice, cacheKeyMaterial, false, false,true);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
        }
    }

    /**
     * 暂存新增数据
     *
     * @param cashDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "暂存新增数据")
    @PostMapping("stagingCreate")
    public Result<?> stagingCreate(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam(required = false) String cacheKey,
                                   @ApiParam(name = "cacheKeyToc", value = "TOC缓存键") @RequestParam(required = false) String cacheKeyToc,
                                   @ApiParam(name = "cacheKeyDetail", value = "明细缓存键") @RequestParam(required = false) String cacheKeyDetail,
                                   @ApiParam(name = "cacheKeyPrepay", value = "预付明细缓存键") @RequestParam(required = false) String cacheKeyPrepay,
                                   @ApiParam(name = "cacheKeyInvoice", value = "发票缓存键") @RequestParam(required = false) String cacheKeyInvoice,
                                   @ApiParam(name = "cacheKeyMaterial", value = "采购物料缓存键") @RequestParam(required = false) String cacheKeyMaterial,
                                   @ApiParam(name = "cashDto", value = "兑付") @RequestBody FeeCashDto cashDto) {
        FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
        String lockKey = FeeCashConstant.LOCK_PREFIX + loginUserDetails.getUsername();
        Boolean lockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, 20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        try {
            cashDto.setBeStaging(BooleanEnum.TRUE.getCapital());
            this.feeCashService.create(cashDto, false, cacheKey, cacheKeyToc, cacheKeyDetail, cacheKeyPrepay, cacheKeyInvoice, cacheKeyMaterial, false, true);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
        }
    }

    /**
     * 暂存修改数据
     *
     * @param cashDto 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "暂存修改数据")
    @PatchMapping("stagingUpdate")
    public Result<?> stagingUpdate(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam(required = false) String cacheKey,
                                   @ApiParam(name = "cacheKeyToc", value = "TOC缓存键") @RequestParam(required = false) String cacheKeyToc,
                                   @ApiParam(name = "cacheKeyDetail", value = "明细缓存键") @RequestParam(required = false) String cacheKeyDetail,
                                   @ApiParam(name = "cacheKeyPrepay", value = "预付明细缓存键") @RequestParam(required = false) String cacheKeyPrepay,
                                   @ApiParam(name = "cacheKeyInvoice", value = "发票缓存键") @RequestParam(required = false) String cacheKeyInvoice,
                                   @ApiParam(name = "cacheKeyMaterial", value = "采购物料缓存键") @RequestParam(required = false) String cacheKeyMaterial,
                                   @ApiParam(name = "cashDto", value = "兑付") @RequestBody FeeCashDto cashDto) {
        String lockKey = FeeCashConstant.LOCK_PREFIX + cashDto.getCashCode();
        Boolean lockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, 20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        try {
            cashDto.setBeStaging(BooleanEnum.TRUE.getCapital());
            this.feeCashService.update(cashDto, cacheKey, cacheKeyToc, cacheKeyDetail, cacheKeyPrepay, cacheKeyInvoice, cacheKeyMaterial, false, true,false);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
        }
    }

    /**
     * 新增保存并提交
     *
     * @param cashDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增保存并提交")
    @PostMapping("submitCreate")
    public Result<?> submitCreate(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam(required = false) String cacheKey,
                                  @ApiParam(name = "cacheKeyToc", value = "TOC缓存键") @RequestParam(required = false) String cacheKeyToc,
                                  @ApiParam(name = "cacheKeyDetail", value = "明细缓存键") @RequestParam(required = false) String cacheKeyDetail,
                                  @ApiParam(name = "cacheKeyPrepay", value = "预付明细缓存键") @RequestParam(required = false) String cacheKeyPrepay,
                                  @ApiParam(name = "cacheKeyInvoice", value = "发票缓存键") @RequestParam(required = false) String cacheKeyInvoice,
                                  @ApiParam(name = "cacheKeyMaterial", value = "采购物料缓存键") @RequestParam(required = false) String cacheKeyMaterial,
                                  @ApiParam(name = "cashDto", value = "兑付") @RequestBody FeeCashDto cashDto) {
        FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
        String lockKey = FeeCashConstant.LOCK_PREFIX + loginUserDetails.getUsername();
        Boolean lockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, 20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        List<String> codes = null;
        try {
            codes = this.feeCashSendHecService.submitBeforeCreateGetCodes(cashDto, cacheKey, cacheKeyToc, cacheKeyDetail, cacheKeyPrepay, cacheKeyInvoice, cacheKeyMaterial);
            lockFlag = redisLockService.batchLock(FeeCashConstant.LOCK_PREFIX, codes, TimeUnit.MINUTES, 20);
            Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
            Result<String> result = feeCashSendHecService.valAndSendList(codes, false);
            if (!result.isSuccess()) {
                feeCashService.updateFeeCashStatus(codes);
                result.setCode(com.biz.crm.business.common.sdk.constant.CommonConstant.SC_OK_200);
                result.setSuccess(true);
                result.setMessage("单据保存成功,但提交失败。" + result.getMessage());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
            if (CollectionUtil.isNotEmpty(codes)
                    && this.redisLockService.isBatchLock(FeeCashConstant.LOCK_PREFIX, codes)) {
                this.redisLockService.batchUnLock(FeeCashConstant.LOCK_PREFIX, codes);
            }
        }
        return Result.ok();
    }

    /**
     * 编辑保存并提交
     *
     * @param cashDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "编辑保存并提交")
    @PostMapping("submitUpdate")
    public Result<?> submitUpdate(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam(required = false) String cacheKey,
                                  @ApiParam(name = "cacheKeyToc", value = "TOC缓存键") @RequestParam(required = false) String cacheKeyToc,
                                  @ApiParam(name = "cacheKeyDetail", value = "明细缓存键") @RequestParam(required = false) String cacheKeyDetail,
                                  @ApiParam(name = "cacheKeyPrepay", value = "预付明细缓存键") @RequestParam(required = false) String cacheKeyPrepay,
                                  @ApiParam(name = "cacheKeyInvoice", value = "发票缓存键") @RequestParam(required = false) String cacheKeyInvoice,
                                  @ApiParam(name = "cacheKeyMaterial", value = "采购物料缓存键") @RequestParam(required = false) String cacheKeyMaterial,
                                  @ApiParam(name = "cashDto", value = "兑付") @RequestBody FeeCashDto cashDto) {
        String lockKey = FeeCashConstant.LOCK_PREFIX + cashDto.getCashCode();
        boolean lockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, 20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        List<String> codes = null;
        try {
            codes = this.feeCashSendHecService.submitBeforeUpdateGetCode(cashDto, cacheKey, cacheKeyToc, cacheKeyDetail, cacheKeyPrepay, cacheKeyInvoice, cacheKeyMaterial);
            lockFlag = redisLockService.batchLock(FeeCashConstant.LOCK_PREFIX, codes, TimeUnit.MINUTES, 20);
            Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
            Result<String> result = feeCashSendHecService.valAndSendList(codes, false);
            if (!result.isSuccess()) {
                feeCashService.updateFeeCashStatus(codes);
            }
            Assert.isTrue(result.isSuccess(), result.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
            if (CollectionUtil.isNotEmpty(codes)
                    && this.redisLockService.isBatchLock(FeeCashConstant.LOCK_PREFIX, codes)) {
                this.redisLockService.batchUnLock(FeeCashConstant.LOCK_PREFIX, codes);
            }
        }
        return Result.ok();
    }


    /**
     * 修改银行卡信息保存
     *
     * @param cashDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "修改银行卡信息保存")
    @PostMapping("updateBankInfo")
    public Result<?> updateBankInfo(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam(required = false) String cacheKey,
                                    @ApiParam(name = "cacheKeyToc", value = "TOC缓存键") @RequestParam(required = false) String cacheKeyToc,
                                    @ApiParam(name = "cashDto", value = "兑付") @RequestBody FeeCashDto cashDto) {
        String lockKey = FeeCashConstant.LOCK_PREFIX + cashDto.getCashCode();
        Boolean lockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, 20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        try {
            this.feeCashService.updateBankInfo(cashDto, cacheKey, cacheKeyToc);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
        }
    }


    /**
     * 修改银行卡信息保存并提交
     *
     * @param cashDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "修改银行卡信息保存并提交")
    @PostMapping("submitUpdateBankInfo")
    public Result<?> submitUpdateBankInfo(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam(required = false) String cacheKey,
                                          @ApiParam(name = "cacheKeyToc", value = "TOC缓存键") @RequestParam(required = false) String cacheKeyToc,
                                          @ApiParam(name = "cashDto", value = "兑付") @RequestBody FeeCashDto cashDto) {
        String lockKey = FeeCashConstant.LOCK_PREFIX + cashDto.getCashCode();
        Boolean lockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, 20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        try {
            return this.feeCashSendHecService.submitUpdateBankInfo(cashDto, cacheKey, cacheKeyToc);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
        }
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation(value = "删除数据")
    public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
        try {
            return this.feeCashSendHecService.delete(idList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取所有缓存明细
     *
     * @param cacheKey
     */
    @ApiOperation(value = "获取所有缓存明细")
    @GetMapping("findAllCacheList")
    public Result<List<FeeCashTicketDto>> findAllCacheList(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey) {
        try {
            List<FeeCashTicketDto> detailVoList = this.feeCashService.findAllCacheList(cacheKey);
            return Result.ok(detailVoList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检索活动
     *
     * @param cacheKey
     */
    @ApiOperation(value = "检索活动")
    @GetMapping("searchAuditDetail")
    public Result<List<MarketingAuditDetailVo>> searchAuditDetail(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                                                  @ApiParam(name = "cashType", value = "兑付类型") @RequestParam String cashType,
                                                                  @ApiParam(name = "cashCode", value = "兑付编码") @RequestParam(required = false) String cashCode) {
        List<MarketingAuditDetailVo> detailVoList = this.feeCashService.searchAuditDetail(cacheKey, cashType, cashCode);
        return Result.ok(detailVoList);
    }

    /**
     * 提交审批
     *
     * @param idList 主键结合
     * @return
     */
    @ApiOperation(value = "提交审批")
    @PostMapping("submit")
    public Result<String> submit(@RequestBody List<String> idList) {
        List<String> codes = this.feeCashSendHecService.submitBeforeGetCodes(idList);
        boolean lockFlag = redisLockService.batchLock(FeeCashConstant.LOCK_PREFIX, codes, TimeUnit.MINUTES, 20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        try {
            Result<String> result = feeCashSendHecService.valAndSendList(codes, false);
            if (!result.isSuccess()) {
                feeCashService.updateFeeCashStatus(codes);
            }
            return result;
        } finally {
            if (this.redisLockService.isBatchLock(FeeCashConstant.LOCK_PREFIX, codes)) {
                this.redisLockService.batchUnLock(FeeCashConstant.LOCK_PREFIX, codes);
            }
        }

    }

    /**
     * 查询费控电子回单
     *
     * @param flagType 标记类型
     * @return
     */
    @ApiOperation(value = "查询费控电子回单")
    @GetMapping("queryHecElectronicReceipt")
    public Result<?> queryHecElectronicReceipt(@RequestParam(value = "", required = false) String flagType) {
        try {
            this.feeCashSendHecService.queryHecElectronicReceipt(flagType);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 流程撤回
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "流程撤回")
    @GetMapping("recover")
    public Result<?> recover(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                             @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
        try {
            this.feeCashService.recover(code, remark);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 模拟回调
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "模拟回调")
    @GetMapping("oaCallback")
    public Result<?> oaCallback(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                                @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
        try {
            this.feeCashService.oaCallback(code, "3", false);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 模拟费控凭证回传
     *
     * @param
     * @return
     */
    @ApiOperation(value = "模拟费控凭证回传")
    @PostMapping("/hecVoucherCallback")
    public Result<?> hecVoucherCallback(@RequestBody List<HecCallbackDto> dtoList) {
        try {
            this.feeCashService.hecVoucherCallback(dtoList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 获取费控流程日志地址
     *
     * @param hecReceiptUrl 费控返回的地址
     * @return 跳转地址
     */
    @ApiOperation(value = "获取费控流程日志地址")
    @GetMapping("getProcessLogUrl")
    public Result<String> getProcessLogUrl(@RequestParam String hecReceiptUrl, String userName) {
        Result<String> result = new Result<>();
        result.setResult(this.encryptionApiComponent.getSsoUrl(hecReceiptUrl, userName));
        return result;
    }

    /**
     * 费控更新流程状态
     *
     * @param dto 主键结合
     * @return
     */
    @ApiOperation(value = "费控更新流程状态")
    @PostMapping("updateProcessStatus")
    public Result<?> updateProcessStatus(@RequestBody FeeCashDto dto) {
        try {
            feeCashService.updateProcessStatus(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "缓存明细导出")
    @GetMapping("findCachePageListImport")
    public Result<Page<FeeCashTicketVo>> findCachePageListImport(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                 @ApiParam(name = "dto", value = "缓存键") FeeCashTicketDto dto) {
        try {
            return this.findCachePageList(pageable, dto.getCacheKey(), dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "贷项订单生成定时任务")
    @GetMapping("createCreditOrderTask")
    public Result<?> createCreditOrderTask() {
        try {
            feeCashService.createCreditOrderTask();
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "刷兑付金额的税额-未税")
    @GetMapping("brushFeeCashTaxAmount")
    public Result brushFeeCashTaxAmount() {
        List<FeeCashDto> feeCashDtos = feeCashService.findListByTicketBuckle();
        if (CollectionUtils.isNotEmpty(feeCashDtos)) {
            for (FeeCashDto dto : feeCashDtos) {
                feeCashService.brushFeeCashTaxAmount(dto.getCashCode());
            }
        }
        return Result.ok();
    }

    @ApiOperation(value = "账票扣明细报表")
    @GetMapping("ticketDeductionDetailReport")
    public Result<Page<TicketFeeCashDetailVo>> ticketDeductionDetail(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable, @ApiParam(name = "dto", value = "缓存键") FeeCashTicketDto dto) {
        try {
            Page<TicketFeeCashDetailVo> page = feeCashService.getTicketDeductionDetail(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
