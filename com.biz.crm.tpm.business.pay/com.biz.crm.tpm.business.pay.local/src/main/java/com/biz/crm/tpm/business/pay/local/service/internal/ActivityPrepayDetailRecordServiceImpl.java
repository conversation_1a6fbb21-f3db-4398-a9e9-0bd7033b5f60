package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecordItem;
import com.biz.crm.tpm.business.pay.local.repository.*;
import com.biz.crm.tpm.business.pay.sdk.constant.ActivityPrepayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordItemDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.enums.ActivityPrepayDetailRecordOperateTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.HecPayStatusTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.TpmPrepayTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayDetailRecordService;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayService;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预付明细跟踪
 */
@Slf4j
@Service
public class ActivityPrepayDetailRecordServiceImpl implements ActivityPrepayDetailRecordService {

    @Autowired(required = false)
    private ActivityPrepayDetailRecordRepository activityPrepayDetailRecordRepository;
    @Autowired(required = false)
    private ActivityPrepayDetailRecordItemRepository activityPrepayDetailRecordItemRepository;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private ActivityPrepayDetailRepository activityPrepayDetailRepository;
    @Autowired(required = false)
    private ActivityPrepayService activityPrepayService;
    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;
    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;
    @Autowired(required = false)
    private FeeCashPrepayRepository feeCashPrepayRepository;
    @Resource
    private RedisLockService redisLockService;


    @Override
    public Page<ActivityPrepayDetailRecordVo> findListByCondition(Pageable pageable, ActivityPrepayDetailRecordDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<ActivityPrepayDetailRecordVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return activityPrepayDetailRecordRepository.findListByCondition(page, dto);
    }

    /**
     * 通过供应商编码查询待冲销明细
     *
     * @param
     * @return
     */
    @Override
    public Page<ActivityPrepayDetailRecordVo> findByPayeeCode(Pageable pageable, ActivityPrepayDetailRecordDto dto) {
//        dto.setPayStatus(HecPayStatusTypeEnum.SUCCESS_PAY.getCode());
        dto.setPayStatusList(Arrays.asList(HecPayStatusTypeEnum.SUCCESS_PAY.getCode(),HecPayStatusTypeEnum.RETURN_TICKETS.getCode()));
        Page<ActivityPrepayDetailRecordVo> pageData = activityPrepayDetailRecordRepository.findByPayeeCode(pageable, dto);
        return pageData;
    }

    /**
     * 通过活动明细编码查询待冲销明细
     *
     * @param dto
     * @return
     */
    @Override
    public List<ActivityPrepayDetailRecordVo> findBySchemeDetailCode(ActivityPrepayDetailRecordDto dto) {
        Validate.notEmpty(dto.getSchemeDetailCodeList(), "活动明细编码不能为空");
//        dto.setPayStatus(HecPayStatusTypeEnum.SUCCESS_PAY.getCode());
        dto.setPayStatusList(Arrays.asList(HecPayStatusTypeEnum.SUCCESS_PAY.getCode(),HecPayStatusTypeEnum.RETURN_TICKETS.getCode()));
        return activityPrepayDetailRecordRepository.findByDto(dto);
    }

    /**
     * 通过预付明细编码查询
     *
     * @param code
     * @return
     */
    @Override
    public ActivityPrepayDetailRecordVo findByCode(String code) {
        ActivityPrepayDetailRecord record = activityPrepayDetailRecordRepository.findByCode(code);
        Validate.notNull(record, "未找到对应的数据");
        List<ActivityPrepayDetailRecordItem> itemList = activityPrepayDetailRecordItemRepository.findByCode(code);
        ActivityPrepayDetailRecordVo vo = nebulaToolkitService.copyObjectByWhiteList(record, ActivityPrepayDetailRecordVo.class, LinkedHashSet.class, ArrayList.class);
        vo.setItems(CollectionUtils.isEmpty(itemList) ? null :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(itemList, ActivityPrepayDetailRecordItem.class, ActivityPrepayDetailRecordItemVo.class, LinkedHashSet.class, ArrayList.class)));
        return vo;
    }

    /**
     * 生成预付明细跟踪
     *
     * @param list
     */
    @Transactional
    @Override
    public void create(List<ActivityPrepayDetailRecordDto> list) {
        Validate.notEmpty(list, "请求数据为空");
        List<String> detailCodes = list.stream().map(e -> e.getPrepayDetailCode()).collect(Collectors.toList());
        List<ActivityPrepayDetailRecord> records = activityPrepayDetailRecordRepository.findByCodes(detailCodes);
        if (!CollectionUtils.isEmpty(records)) {
            log.warn("预付明细跟踪已存在，请检查");
            return;
        }
        list.forEach(e -> {
            e.setAvailableReversedAmount(e.getPrepayAmount());
            e.setReversedAmount(BigDecimal.ZERO);
        });
        List<ActivityPrepayDetailRecord> saveList = new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, ActivityPrepayDetailRecordDto.class, ActivityPrepayDetailRecord.class, LinkedHashSet.class, ArrayList.class));
        activityPrepayDetailRecordRepository.saveBatch(saveList);
    }

    @Override
    public ActivityPrepayVo cal(ActivityPrepayVo vo) {
        //获取预付缓存
        if (StringUtils.isNotBlank(vo.getCacheKey())) {
            List<ActivityPrepayDetailDto> cacheList = activityPrepayService.findCacheList(vo.getCacheKey());
            if (!CollectionUtils.isEmpty(cacheList)) {
                vo.setDetailList((List<ActivityPrepayDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(cacheList, ActivityPrepayDetailDto.class, ActivityPrepayDetailVo.class, LinkedHashSet.class, ArrayList.class));
            }
        }

        //1、明细金额先重置为0
        BigDecimal thisPayAmount = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(vo.getDetailList())) {
            for (ActivityPrepayDetailVo detailVo : vo.getDetailList()) {
                detailVo.setRelateCarryAmount(BigDecimal.ZERO);
                if (Objects.isNull(detailVo.getThisPrepayAmount())) {
                    detailVo.setThisPrepayAmount(BigDecimal.ZERO);
                }
                detailVo.setThisPayAmount(detailVo.getThisPrepayAmount());
                thisPayAmount = thisPayAmount.add(detailVo.getThisPayAmount());
            }
        }
        if (!CollectionUtils.isEmpty(vo.getPayeeList())) {
            for (ActivityPrepayPayeeVo payeeVo : vo.getPayeeList()) {
                payeeVo.setThisPayAmount(thisPayAmount);
            }
        }
        //2、没选公司 或 收款方
        if (StringUtils.isAnyBlank(vo.getCompanyCode(), vo.getPayeeCode())) {
            if (!CollectionUtils.isEmpty(vo.getDetailList())) {
                activityPrepayService.addItemCache(vo.getCacheKey(), (List<ActivityPrepayDetailDto>)
                        nebulaToolkitService.copyCollectionByWhiteList(vo.getDetailList(), ActivityPrepayDetailVo.class, ActivityPrepayDetailDto.class, LinkedHashSet.class, ArrayList.class));
            }
            return vo;
        }
        //3、查活动预付明细，没查到金额均返回0
        vo.setTenantCode(TenantUtils.getTenantCode());
        vo.setPrepayType(TpmPrepayTypeEnum.activity.getCode());
        vo.setStatus(ProcessStatusEnum.PREPARE.getDictCode());
        List<ActivityPrepayDetailRecord> prepayDetailRecords = activityPrepayDetailRecordRepository.findCalList(vo);
        if (CollectionUtils.isEmpty(prepayDetailRecords)) {
            if (!CollectionUtils.isEmpty(vo.getDetailList())) {
                activityPrepayService.addItemCache(vo.getCacheKey(), (List<ActivityPrepayDetailDto>)
                        nebulaToolkitService.copyCollectionByWhiteList(vo.getDetailList(), ActivityPrepayDetailVo.class, ActivityPrepayDetailDto.class, LinkedHashSet.class, ArrayList.class));
            }
            return vo;
        }
        //4、编辑时，同一公司+收款方时，其它待提交的预付数据预付明细占用的金额
        List<ActivityPrepayDetail> prepayDetails = activityPrepayDetailRepository.findCalList(vo);
        //其它待提交活动预付占用的结转金额
        BigDecimal otherOccupyAmount = prepayDetails.stream().map(ActivityPrepayDetail::getRelateCarryAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //5、先处理一遍预付明细，减掉其它待提交的占用金额
        List<ActivityPrepayDetailRecord> prepayDetailRecordsFilters = new ArrayList<>();
        for (ActivityPrepayDetailRecord prepayDetailRecord : prepayDetailRecords) {
            if (otherOccupyAmount.compareTo(BigDecimal.ZERO) == 0) {
                prepayDetailRecordsFilters.add(prepayDetailRecord);
                continue;
            }
            if (otherOccupyAmount.compareTo(prepayDetailRecord.getPrepareCarryAmount()) >= 0) {
                otherOccupyAmount = otherOccupyAmount.subtract(prepayDetailRecord.getPrepareCarryAmount());
                continue;
            }
            prepayDetailRecord.setPrepareCarryAmount(prepayDetailRecord.getPrepareCarryAmount().subtract(otherOccupyAmount));
            prepayDetailRecordsFilters.add(prepayDetailRecord);
            otherOccupyAmount = BigDecimal.ZERO;
        }

        if (!CollectionUtils.isEmpty(vo.getDetailList())) {
            //6、分配待结转金额，关联结转金额≤申请预付金额
            List<ActivityPrepayDetailVo> calList = BeanUtil.copyToList(vo.getDetailList(), ActivityPrepayDetailVo.class);
            calList.forEach(e -> {
                if (Objects.isNull(e.getThisPrepayAmount())) {
                    e.setThisPrepayAmount(BigDecimal.ZERO);
                }
            });
            calList.sort(Comparator.comparing(ActivityPrepayDetailVo::getThisPrepayAmount));
            BigDecimal prepareCarryAmount = prepayDetailRecordsFilters.stream().map(e -> Optional.ofNullable(e.getPrepareCarryAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (ActivityPrepayDetailVo detailVo : calList) {
                if (detailVo.getThisPrepayAmount().compareTo(prepareCarryAmount) <= 0) {
                    detailVo.setRelateCarryAmount(detailVo.getRelateCarryAmount().add(detailVo.getThisPrepayAmount()));
                    prepareCarryAmount = prepareCarryAmount.subtract(detailVo.getRelateCarryAmount());
                    continue;
                }
                detailVo.setRelateCarryAmount(detailVo.getRelateCarryAmount().add(prepareCarryAmount));
                detailVo.setThisPrepayAmount(detailVo.getThisPrepayAmount().subtract(prepareCarryAmount));
                prepareCarryAmount = BigDecimal.ZERO;
            }
            //7、回写关联结转金额到明细上，计算本次支付金额
            Map<String, ActivityPrepayDetailVo> calMap = calList.stream().collect(Collectors.toMap(ActivityPrepayDetailVo::getSchemeDetailCode, Function.identity()));
            for (ActivityPrepayDetailVo detailVo : vo.getDetailList()) {
                if (!calMap.containsKey(detailVo.getSchemeDetailCode())) {
                    continue;
                }
                ActivityPrepayDetailVo calVo = calMap.get(detailVo.getSchemeDetailCode());
                detailVo.setRelateCarryAmount(calVo.getRelateCarryAmount());
                detailVo.setThisPayAmount(detailVo.getThisPrepayAmount().subtract(detailVo.getRelateCarryAmount()));
            }
            BigDecimal amount = vo.getDetailList().stream().map(ActivityPrepayDetailVo::getThisPayAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (!CollectionUtils.isEmpty(vo.getPayeeList())) {
                vo.getPayeeList().get(0).setThisPayAmount(amount);
            }
        }

        if (!CollectionUtils.isEmpty(vo.getDetailList())) {
            activityPrepayService.addItemCache(vo.getCacheKey(), (List<ActivityPrepayDetailDto>)
                    nebulaToolkitService.copyCollectionByWhiteList(vo.getDetailList(), ActivityPrepayDetailVo.class, ActivityPrepayDetailDto.class, LinkedHashSet.class, ArrayList.class));
        }
        return vo;
    }

    @Override
    public void operateLock(List<String> codeList, boolean lock) {
        Validate.notEmpty(codeList, "预付明细编码为空");
        if (lock) {
            boolean locked = redisLockService.batchLock(ActivityPrepayConstant.OPERATE_LOCK_PREFIX, codeList, TimeUnit.MINUTES, ActivityPrepayConstant.lock_time_out);
            Validate.isTrue(locked, "付款明细操作加锁失败，请稍后再试...");
        } else {
            redisLockService.batchUnLock(ActivityPrepayConstant.OPERATE_LOCK_PREFIX, codeList);
        }
    }

    @Transactional
    @Override
    public void operate(List<ActivityPrepayDetailRecordItemDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //按预付明细编码分组处理
        Map<String, List<ActivityPrepayDetailRecordItemDto>> listMap =
                list.stream().collect(Collectors.groupingBy(ActivityPrepayDetailRecordItemDto::getPrepayDetailCode));
        //查询预付明细用于更新头表
        List<ActivityPrepayDetailRecord> recordList = this.activityPrepayDetailRecordRepository.findByCodes(Lists.newArrayList(listMap.keySet()));
        Map<String, ActivityPrepayDetailRecord> recordMap = recordList.stream().collect(Collectors.toMap(ActivityPrepayDetailRecord::getPrepayDetailCode, Function.identity()));

        //生成操作明细，处理头表金额
        List<ActivityPrepayDetailRecord> detailRecordList = new ArrayList<>();
        List<ActivityPrepayDetailRecordItem> detailRecordItemList = new ArrayList<>();
        for (Map.Entry<String, List<ActivityPrepayDetailRecordItemDto>> entry : listMap.entrySet()) {
            List<ActivityPrepayDetailRecordItem> detailRecordItems = BeanUtil.copyToList(entry.getValue(), ActivityPrepayDetailRecordItem.class);
            BigDecimal carriedAmount = BigDecimal.ZERO;//本次结转金额
            BigDecimal prepareCarryAmount = BigDecimal.ZERO;//待结转金额
            ActivityPrepayDetailRecord detailRecord = recordMap.get(entry.getKey());
            BigDecimal prepareCarryAmountDb = Optional.ofNullable(detailRecord.getPrepareCarryAmount()).orElse(BigDecimal.ZERO);
            for (ActivityPrepayDetailRecordItem detailRecordItem : detailRecordItems) {
                detailRecordItem.setTenantCode(TenantUtils.getTenantCode());
                detailRecordItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                detailRecordItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                ActivityPrepayDetailRecordOperateTypeEnum typeEnum = ActivityPrepayDetailRecordOperateTypeEnum.getEnumByCode(detailRecordItem.getType());
                Validate.notNull(typeEnum, "预付明细操作类型未定义");
                switch (typeEnum) {
                    case INIT:
                        break;
                    case ADD:
                        prepareCarryAmount = prepareCarryAmount.add(detailRecordItem.getAmount());
                        break;
                    case CARRY:
//                        if(prepareCarryAmountDb.compareTo(BigDecimal.ZERO)>0) {
//                            BigDecimal amount = detailRecordItem.getAmount().compareTo(prepareCarryAmountDb) > 0 ? prepareCarryAmountDb : detailRecordItem.getAmount();
//                            detailRecordItem.setAmount(amount);
//                            carriedAmount = carriedAmount.add(detailRecordItem.getAmount());
//                            prepareCarryAmount = prepareCarryAmount.subtract(detailRecordItem.getAmount());
//                            prepareCarryAmountDb=prepareCarryAmountDb.subtract(amount);
//                        }else {
//                            //已经小于0了，不能扣减了
//                            detailRecordItem.setAmount(BigDecimal.ZERO);
//                        }
                        carriedAmount = carriedAmount.add(detailRecordItem.getAmount());
                        prepareCarryAmount = prepareCarryAmount.subtract(detailRecordItem.getAmount());
                        break;
                    case WRITE_OFF:
                        break;
                    default:
                        break;
                }
            }
            detailRecordItemList.addAll(detailRecordItems);

            //更新主表金额
            BigDecimal newCarriedAmount = Optional.ofNullable(detailRecord.getCarriedAmount()).orElse(BigDecimal.ZERO).add(carriedAmount);
            BigDecimal newPrepareCarryAmount = Optional.ofNullable(detailRecord.getPrepareCarryAmount()).orElse(BigDecimal.ZERO).add(prepareCarryAmount);
            detailRecord.setCarriedAmount(newCarriedAmount);
            detailRecord.setPrepareCarryAmount(newPrepareCarryAmount);
            detailRecordList.add(detailRecord);
        }
        this.activityPrepayDetailRecordRepository.updateBatchById(detailRecordList);
        this.activityPrepayDetailRecordItemRepository.saveBatch(detailRecordItemList);
    }

    @Override
    public Page<ActivityPrepayDetailRecordItemVo> findItemByConditions(Pageable pageable, ActivityPrepayDetailRecordItemDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new ActivityPrepayDetailRecordItemDto();
        }
        return this.activityPrepayDetailRecordItemRepository.findByConditions(pageable, dto);
    }

    /**
     * 更新支付状态
     *
     * @param dtoList
     */
    @Override
    public void updatePayStatus(List<HecCallbackDto> dtoList) {
        activityPrepayDetailRecordRepository.updatePayStatus(dtoList);
    }

    @Override
    public Map<String, BigDecimal> findprepayAmount(List<String> schemeDetailCodeList) {
        // 1044715 已预付金额赋值：通过【活动方案明细编码】查询【预付明细跟踪表】，统计出【已预付金额】的和，并赋值到【活动预付明细】中【已预付金额】字段
        ActivityPrepayDetailRecordDto dto = new ActivityPrepayDetailRecordDto();
        dto.setSchemeDetailCodeList(schemeDetailCodeList);
        List<ActivityPrepayDetailRecordVo> recodes = this.findBySchemeDetailCode(dto);

        Map<String, BigDecimal> schemeDetailCodeKeyAndPrepayAmountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(recodes)) {
            recodes.stream()
                    .collect(Collectors.groupingBy(ActivityPrepayDetailRecordVo::getSchemeDetailCode))
                    .forEach((k, v) -> {
                        BigDecimal prepayAmount = v.stream().map(ActivityPrepayDetailRecordVo::getPrepayAmount)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        schemeDetailCodeKeyAndPrepayAmountMap.put(k, prepayAmount);
                    });
        }
        return schemeDetailCodeKeyAndPrepayAmountMap;
    }

    @Override
    public Page<ActivityPrepayDetailRecordVo> findByPayeeCodes(Pageable pageable, List<String> payeeCodes) {
        ActivityPrepayDetailRecordDto dto = new ActivityPrepayDetailRecordDto();
        dto.setPayeeCodes(payeeCodes);
//        dto.setPayStatus(HecPayStatusTypeEnum.SUCCESS_PAY.getCode());
        dto.setPayStatusList(Arrays.asList(HecPayStatusTypeEnum.SUCCESS_PAY.getCode(),HecPayStatusTypeEnum.RETURN_TICKETS.getCode()));
        return activityPrepayDetailRecordRepository.findByPayeeCode(pageable, dto);
    }

    /**
     * 通过活动明细编码实时查询结转、冲销明细
     *
     * @param code
     * @return
     */
    @Override
    public List<ActivityPrepayDetailRecordItemVo> findItemByPrepayDetailCode(String code) {
        ActivityPrepayDetailRecord record = activityPrepayDetailRecordRepository.findByCode(code);
        Validate.notNull(record, "未找到对应的数据");
        List<ActivityPrepayDetailRecordItemVo> list = new ArrayList<>();
        //结转先查计提、再查结案
        WithHoldingVo withHoldingVo = withHoldingRepository.findByBusinessCodesVo(Arrays.asList(record.getSchemeDetailCode()))
                .stream().filter(Objects::nonNull).filter(e -> ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus())).findFirst().orElse(null);
        if (withHoldingVo != null && record.getPrepayAmount().compareTo(withHoldingVo.getActualAmount()) > 0) {
            ActivityPrepayDetailRecordItemVo itemVo = nebulaToolkitService.copyObjectByWhiteList(record, ActivityPrepayDetailRecordItemVo.class, LinkedHashSet.class, ArrayList.class);
            itemVo.setType(ActivityPrepayDetailRecordOperateTypeEnum.CARRY.getCode());
            itemVo.setAmount(record.getPrepayAmount().subtract(withHoldingVo.getActualAmount()));
            itemVo.setBusinessCode(record.getPrepayDetailCode());
            itemVo.setBusinessName(record.getPrepayName());
            list.add(itemVo);

            List<MarketingAuditDetailVo> auditDetailVos = marketingAuditService.findBySchemeDetailCodesAll(Arrays.asList(record.getSchemeDetailCode()))
                    .stream().filter(e -> StringUtils.equals(BooleanEnum.TRUE.getCapital(), e.getBeFullAudit())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(auditDetailVos)) {
                BigDecimal auditAmount = auditDetailVos.stream().map(e -> e.getAuditAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (withHoldingVo.getActualAmount().compareTo(auditAmount) > 0) {
                    ActivityPrepayDetailRecordItemVo itemVo1 = nebulaToolkitService.copyObjectByWhiteList(record, ActivityPrepayDetailRecordItemVo.class, LinkedHashSet.class, ArrayList.class);
                    itemVo1.setType(ActivityPrepayDetailRecordOperateTypeEnum.CARRY.getCode());
                    itemVo1.setAmount(withHoldingVo.getActualAmount().subtract(auditAmount));
                    itemVo1.setBusinessCode(record.getPrepayDetailCode());
                    itemVo1.setBusinessName(record.getPrepayName());
                    list.add(itemVo1);
                }
            }
        }
        //冲销需要查询费用兑付所对应的预付明细
        List<FeeCashPrepayVo> cashPrepayVos = feeCashPrepayRepository.findBySchemeDetailCode(record.getSchemeDetailCode());
        if (!CollectionUtils.isEmpty(cashPrepayVos)) {
            cashPrepayVos.forEach(e -> {
                ActivityPrepayDetailRecordItemVo itemVo = nebulaToolkitService.copyObjectByWhiteList(e, ActivityPrepayDetailRecordItemVo.class, LinkedHashSet.class, ArrayList.class);
                itemVo.setType(ActivityPrepayDetailRecordOperateTypeEnum.WRITE_OFF.getCode());
                itemVo.setAmount(e.getThisReversedAmount());
                itemVo.setBusinessCode(e.getCashCode());
                itemVo.setBusinessName(e.getCashName());
                list.add(itemVo);
            });
        }
        return list;
    }



    /**
     * 根据活动明细编码查询已预付数据
     * @param vo
     * @return
     */
    @Override
    public Map<String, BigDecimal> findAlreadyPrepayAmount(ActivityPrepayVo vo) {
        // 1044715 已预付金额赋值：通过【活动方案明细编码】查询【预付明细跟踪表】，统计出【已预付金额】的和，并赋值到【活动预付明细】中【已预付金额】字段

        List<ActivityPrepayDetail> recodes = activityPrepayDetailRecordRepository.findAlreadySchemeDetailCodeByCondition(vo.getPrepayCode(),vo.getSchemeDetailCodeList());

        Map<String, BigDecimal> schemeDetailCodeKeyAndPrepayAmountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(recodes)) {
            recodes.stream()
                    .collect(Collectors.groupingBy(ActivityPrepayDetail::getSchemeDetailCode))
                    .forEach((k, v) -> {
                        BigDecimal prepayAmount = v.stream().map(ActivityPrepayDetail::getThisPrepayAmount)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        schemeDetailCodeKeyAndPrepayAmountMap.put(k, prepayAmount);
                    });
        }
        return schemeDetailCodeKeyAndPrepayAmountMap;
    }

    @Override
    public BigDecimal getAvailable_reversed_amount(String detailCode) {
        return feeCashPrepayRepository.getAvailable_reversed_amount(detailCode);
    }

    @Override
    public void koujianPrepayCarreAmount(List<FeeCashPrepayDto> prepayList,Integer koujianOrRecover) {
        if(CollectionUtils.isEmpty(prepayList)){
            return;
        }
        Set<String> collect = prepayList.stream().map(o -> o.getPrepayDetailCode()).collect(Collectors.toSet());
        Map<String, List<FeeCashPrepayDto>> collectMap = prepayList.stream().collect(Collectors.groupingBy(FeeCashPrepayDto::getPrepayDetailCode));
        prepayList.forEach(o->{
            o.setKouJianPrepareCarryAmount( Optional.ofNullable(o.getThisReversedAmount()).orElse(BigDecimal.ZERO).subtract ( ( Optional.ofNullable(o.getAvailableReversedAmount()).orElse(BigDecimal.ZERO)).subtract(Optional.ofNullable(o.getPrepareCarryAmount()).orElse(BigDecimal.ZERO))));
        });
        List<ActivityPrepayDetailRecord> records=activityPrepayDetailRecordRepository.getAllRecord(collect);
        List<ActivityPrepayDetailRecordItemDto> list=Lists.newArrayList();
        records.forEach(o->{
             List<FeeCashPrepayDto> feeCashPrepayDtos = collectMap.get(o.getPrepayDetailCode());
             if(!CollectionUtils.isEmpty(feeCashPrepayDtos)){
                  if(1==koujianOrRecover) {
                      BigDecimal kouJianprepareCarryAmount = feeCashPrepayDtos.get(0).getKouJianPrepareCarryAmount();
                      BigDecimal prepareCarryAmount = Optional.ofNullable(o.getPrepareCarryAmount()).orElse(BigDecimal.ZERO);
                      kouJianprepareCarryAmount = prepareCarryAmount.compareTo(kouJianprepareCarryAmount) > 0 ? kouJianprepareCarryAmount : prepareCarryAmount;
                      if(kouJianprepareCarryAmount.compareTo(BigDecimal.ZERO)>0){
                          ActivityPrepayDetailRecordItemDto dto=new ActivityPrepayDetailRecordItemDto();
                          dto.setBusinessCode(feeCashPrepayDtos.get(0).getCashCode()+"_processing");
                          dto.setBusinessName(feeCashPrepayDtos.get(0).getCashName());
                          dto.setAmount(kouJianprepareCarryAmount);
                          dto.setPrepayDetailCode(feeCashPrepayDtos.get(0).getPrepayDetailCode());
                          dto.setPrepayCode(feeCashPrepayDtos.get(0).getPrepayCode());
                          dto.setPrepayName(feeCashPrepayDtos.get(0).getPrepayName());
                          dto.setSchemeCode(feeCashPrepayDtos.get(0).getSchemeCode());
                          dto.setSchemeDetailCode(feeCashPrepayDtos.get(0).getSchemeDetailCode());
                          dto.setSchemeName(feeCashPrepayDtos.get(0).getSchemeName());
                          dto.setType(ActivityPrepayDetailRecordOperateTypeEnum.CARRY.getCode());
                          list.add(dto);
                      }

                  }else if(2==koujianOrRecover){
//                      BigDecimal kouJianprepareCarryAmount = feeCashPrepayDtos.get(0).getKouJianPrepareCarryAmount();
//                      if(null!=kouJianprepareCarryAmount&&kouJianprepareCarryAmount.compareTo(BigDecimal.ZERO)>0){
//                          o.setPrepareCarryAmount(Optional.ofNullable(o.getPrepareCarryAmount()).orElse(BigDecimal.ZERO) .add(kouJianprepareCarryAmount));
//                          o.setCarriedAmount(Optional.ofNullable(o.getCarriedAmount()).orElse(BigDecimal.ZERO).subtract(kouJianprepareCarryAmount));
//                      }
                      String cashCode = feeCashPrepayDtos.get(0).getCashCode();
                      List<ActivityPrepayDetailRecordItem> recordItemList = activityPrepayDetailRecordItemRepository.findByBusinessCodeAndPreDetailcode(cashCode+"_processing",o.getPrepayDetailCode());
                      if(!CollectionUtils.isEmpty(recordItemList)) {
                          TenantFlagOpDto baseDto = new TenantFlagOpDto();
                          baseDto.setTenantCode(TenantUtils.getTenantCode());
                          baseDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                          baseDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                          for (ActivityPrepayDetailRecordItem detailRecordItem : recordItemList) {
                              detailRecordItem.setBusinessCode(detailRecordItem.getBusinessCode().replace("_processing", ""));
                              ActivityPrepayDetailRecordItemDto itemDto = BeanUtil.copyProperties(detailRecordItem, ActivityPrepayDetailRecordItemDto.class);
                              BeanUtils.copyProperties(baseDto, itemDto);
                              itemDto.setAmount(itemDto.getAmount().negate());
                              itemDto.setBusinessName("活动预付流程驳回还原");
                              list.add(itemDto);
                              //审批完成后把businessCode后面的流程编码去掉，防止再次提交审批时查到相同数据
                          }
                          activityPrepayDetailRecordItemRepository.updateBatchById(recordItemList);
                      }
                  }
             }
        });
        this.operate(list);
    }
}
