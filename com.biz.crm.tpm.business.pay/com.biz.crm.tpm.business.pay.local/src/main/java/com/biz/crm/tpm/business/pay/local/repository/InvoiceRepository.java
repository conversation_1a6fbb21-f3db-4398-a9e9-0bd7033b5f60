package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.Invoice;
import com.biz.crm.tpm.business.pay.local.mapper.InvoiceMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.InvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class InvoiceRepository extends ServiceImpl<InvoiceMapper, Invoice> {
    @Autowired
    private InvoiceMapper invoiceMapper;

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    public Page<InvoiceVo> findByConditions(Pageable pageable, InvoiceDto dto) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 50);
        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<InvoiceVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return invoiceMapper.findByConditions(page, dto);
    }

    /**
     * 根据发票号码查询信息
     */
    public Invoice findByInvoiceNo(String invoiceNo) {
        return this.lambdaQuery()
                .eq(Invoice::getInvoiceNo, invoiceNo)
                .eq(Invoice::getTenantCode, TenantUtils.getTenantCode())
                .eq(Invoice::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
                .one();
    }

    /**
     * 根据发票号码集查询信息
     */
    public List<Invoice> findByInvoiceNos(Set<String> invoiceNos) {
        return this.lambdaQuery()
                .in(Invoice::getInvoiceNo, invoiceNos)
                .eq(Invoice::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
                .eq(Invoice::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    /**
     * 逻辑删除
     */
    public void delete(Set<String> ids) {
        this.lambdaUpdate().in(Invoice::getId, ids)
                .eq(Invoice::getTenantCode, TenantUtils.getTenantCode())
                .set(Invoice::getDelFlag, DelFlagStatusEnum.DELETE.getCode()).update();
    }

    public Invoice findByIdAndTenantCode(String id, String tenantCode) {
        return this.lambdaQuery()
                .eq(Invoice::getTenantCode, tenantCode)
                .in(Invoice::getId, id)
                .one();
    }

    public List<Invoice> listByIdsAndTenantCode(Set<String> ids, String tenantCode) {
        return this.lambdaQuery()
                .eq(Invoice::getTenantCode, tenantCode)
                .in(Invoice::getId, ids)
                .list();
    }

    public void bindCashCode(List<String> invoiceNos, String cashCode) {
        this.lambdaUpdate().in(Invoice::getInvoiceNo, invoiceNos)
                .set(Invoice::getCashCode, cashCode).update();
    }

    public void clearCashCode(List<String> cashCodes) {
        this.lambdaUpdate().in(Invoice::getCashCode, cashCodes)
                .set(Invoice::getCashCode, null).update();
    }
}
