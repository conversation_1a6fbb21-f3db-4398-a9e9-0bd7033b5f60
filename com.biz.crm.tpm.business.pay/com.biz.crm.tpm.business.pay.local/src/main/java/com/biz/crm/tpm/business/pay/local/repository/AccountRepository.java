package com.biz.crm.tpm.business.pay.local.repository;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.Account;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.mapper.AccountMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 费用上账主表(TpmAccount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
@Component
public class AccountRepository extends ServiceImpl<AccountMapper, Account> {

  @Autowired
  private AccountMapper accountMapper;

  /**
   * 分页查询数据
   *
   * @param pageable   分页对象
   * @param tpmAccount 实体对象
   * @return
   */
  public Page<Account> findByConditions(Pageable pageable, AccountDto tpmAccount) {
    tpmAccount.setTenantCode(TenantUtils.getTenantCode());
    Page<Account> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    Page<Account> pageList = accountMapper.findByConditions(page, tpmAccount);
    return pageList;
  }

  public List<Account> findByIds(List<String> ids) {
    return this.lambdaQuery()
        .eq(Account::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(Account::getTenantCode,TenantUtils.getTenantCode())
        .in(Account::getId,ids)
        .list();
  }

  /**
   * 逻辑删除
   * @param idList
   */
  public void deleteBatch(List<String> idList) {
  this.lambdaUpdate()
      .in(Account::getId,idList)
      .eq(Account::getTenantCode,TenantUtils.getTenantCode())
      .set(Account::getDelFlag,DelFlagStatusEnum.DELETE.getCode())
      .update();

  }

  /**
   * 通过活动明细编码查询
   * @param codes
   * @return
   */
  public List<Account> findByActivitiesDetailCode(List<String> codes) {
    return this.lambdaQuery()
        .in(Account::getActivitiesDetailCode,codes)
        .eq(Account::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
        .eq(Account::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }

  public Account findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(Account::getTenantCode,tenantCode)
        .in(Account::getId,id)
        .one();
  }
}

