package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：活动预付;
 *
 * <AUTHOR> yaoyongming
 * @date : 2024-6-4
 */
@ApiModel(value = "ActivityPrepay", description = "活动预付")
@TableName("tpm_activity_prepay")
@Getter
@Setter
@Entity(name = "tpm_activity_prepay")
@org.hibernate.annotations.Table(appliesTo = "tpm_activity_prepay", comment = "活动预付")
@Table(name = "tpm_activity_prepay", indexes = {@Index(name = "activity_prepay_idx1", columnList = "prepay_code")})
public class ActivityPrepay extends TenantFlagOpEntity {

  /**
   * 预付编号
   */
  @ApiModelProperty(name = "预付编号", notes = "预付编号")
  @Column(name = "prepay_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付编号 '")
  private String prepayCode;

  @ApiModelProperty("预付名称")
  @Column(name = "prepay_name", columnDefinition = "varchar(128) comment '预付名称'")
  private String prepayName;

  @ApiModelProperty("部门编码")
  @Column(name = "department_code", columnDefinition = "varchar(32) comment '部门编码'")
  private String departmentCode;

  @ApiModelProperty("部门名称")
  @Column(name = "department_name", columnDefinition = "varchar(128) comment '部门名称'")
  private String departmentName;

  @ApiModelProperty("操作人")
  @Column(name = "operate_name", columnDefinition = "varchar(32) comment '操作人'")
  private String operateName;

  @ApiModelProperty("审批状态")
  @Column(name = "status", length = 64, columnDefinition = "varchar(64) COMMENT '审批状态'")
  private String status;

  @ApiModelProperty("推送日期")
  @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("审批流程编码")
  @Column(name = "process_key",  length = 32, columnDefinition = "VARCHAR(32) COMMENT '审批流程编码 '")
  private String processKey;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("组织编码")
  @Column(name = "org_code", length = 64, columnDefinition = "varchar(64) COMMENT '组织编码'")
  private String orgCode;

  @ApiModelProperty("组织名称")
  @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
  private String orgName;

  @ApiModelProperty("公司代码")
  @Column(name = "company_code",columnDefinition = "varchar(32) comment '公司代码'")
  private String companyCode;

  @ApiModelProperty("查看流程日志所需地址")
  @Column(name = "hec_receipt_url", columnDefinition = "VARCHAR(1024) COMMENT '查看流程日志所需地址 '")
  private String hecReceiptUrl;

  @ApiModelProperty("付款状态")
  @Column(name = "pay_status", length = 64, columnDefinition = "VARCHAR(64) COMMENT '付款状态 '")
  private String payStatus;

  @ApiModelProperty("收款方编码")
  @Column(name = "payee_code", columnDefinition = "VARCHAR(64) COMMENT '收款方编码 '")
  private String payeeCode;

  @ApiModelProperty("收款方Sap编码")
  @Column(name = "supplier_erp_code", columnDefinition = "VARCHAR(64) COMMENT '收款方Sap编码 '")
  private String supplierErpCode;

  @ApiModelProperty("收款方名称")
  @Column(name = "payee_name", columnDefinition = "VARCHAR(255) COMMENT '收款方 '")
  private String payeeName;

  @ApiModelProperty("付款成功时间")
  @Column(name = "pay_sucess_date", columnDefinition = "datetime COMMENT '付款成功时间'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date paySucessDate;

}