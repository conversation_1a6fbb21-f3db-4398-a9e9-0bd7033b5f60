<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.FeeCashTocMapper">

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTocVo">
        select
        t.*,
        tc.org_code, tc.org_name, tc.create_account cash_create_account, tc.create_name cash_create_name
        from tpm_fee_cash_toc t
        INNER JOIN tpm_fee_cash tc on t.cash_code=tc.cash_code
        <where>
            t.del_flag = '009'
            AND tc.del_flag = '009'
            <if test="dto.tenantCode != null and dto.tenantCode != '' ">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.cashCode != null and dto.cashCode != '' ">
                and t.cash_code = #{dto.cashCode}
            </if>
            <if test="dto.cashCreateName != null and dto.cashCreateName != ''">
                <bind name="cashCreateName" value="'%' + dto.cashCreateName + '%'"/>
                and tc.create_name like #{cashCreateName}
            </if>
            <if test="dto.orgName != null and dto.orgName != ''">
                <bind name="orgName" value="'%' + dto.orgName + '%'"/>
                and tc.org_name like #{orgName}
            </if>
            <if test="dto.cashName != null and dto.cashName != ''">
                <bind name="cashName" value="'%' + dto.cashName + '%'"/>
                and tc.cash_name like #{cashName}
            </if>
            <if test="dto.platform != null and dto.platform != ''">
                <bind name="platform" value="'%' + dto.platform + '%'"/>
                and t.platform like #{platform}
            </if>
            <if test="dto.payeeName != null and dto.payeeName != ''">
                <bind name="payeeName" value="'%' + dto.payeeName + '%'"/>
                and t.payee_name like #{payeeName}
            </if>
        </where>
        order by t.create_time desc,t.id
    </select>
</mapper>

