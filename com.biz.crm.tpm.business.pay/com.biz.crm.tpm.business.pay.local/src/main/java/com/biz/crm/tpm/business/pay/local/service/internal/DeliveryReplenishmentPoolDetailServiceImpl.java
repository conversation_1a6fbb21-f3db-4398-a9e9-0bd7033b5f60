package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.DeliverySearchDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.ReplenishmentPoolDetailDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolOperationType;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolTypeEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.renyang.ReplenishmentPoolDetailVoService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.vo.renyang.ReplenishmentPoolDetailVo;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsDeliveryOrderService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.tpm.business.pay.local.entity.DeliveryReplenishmentPoolDetail;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAuditDetail;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff;
import com.biz.crm.tpm.business.pay.local.repository.DeliveryReplenishmentPoolDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.MarketingAuditDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingWriteOffRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.DeliveryReplenishmentConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.DeliveryReplenishmentPoolDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingWriteOffDto;
import com.biz.crm.tpm.business.pay.sdk.enums.DeliveryDetailTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.BackWithHoldingWriteOffService;
import com.biz.crm.tpm.business.pay.sdk.service.DeliveryReplenishmentPoolDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffSendHecService;
import com.biz.crm.tpm.business.pay.sdk.vo.BackWithHoldingWriteOffVo;
import com.biz.crm.tpm.business.pay.sdk.vo.DeliveryReplenishmentPoolDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingWriteOffVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DeliveryReplenishmentPoolDetailServiceImpl implements DeliveryReplenishmentPoolDetailService {

    @Autowired(required = false)
    private DeliveryReplenishmentPoolDetailRepository deliveryReplenishmentPoolDetailRepository;
    @Autowired(required = false)
    private ReplenishmentPoolDetailVoService replenishmentPoolDetailVoService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;
    @Autowired(required = false)
    private WithHoldingWriteOffSendHecService withHoldingWriteOffSendHecService;
    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;
    @Autowired(required = false)
    private FeeCashService feeCashService;
    @Autowired(required = false)
    private MaterialVoService materialVoService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired
    private BackWithHoldingWriteOffService backWithHoldingWriteOffService;

    /**
     * 获取发货费用定时任务
     */
    @Override
    public void generateDeliveryReplenishment() {
        String now = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd");
        generateDeliveryReplenishmentDate(now + " 00:00:00", now + " 23:59:59");
    }


    /**
     * 获取发货费用定时任务
     *
     * @param startDate
     * @param endDate
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generateDeliveryReplenishmentDate(String startDate, String endDate) {
        DeliverySearchDto dto = new DeliverySearchDto();
        dto.setObtain(BooleanEnum.FALSE.getCapital());
        List<ReplenishmentPoolDetailVo> detailVoList = replenishmentPoolDetailVoService.findDelivery(dto);
        if (CollectionUtils.isEmpty(detailVoList)) {
            log.warn("====================未在DMS找到对应的发货费用信息");
            return;
        }
        List<ReplenishmentPoolDetailVo> detailVoListFilter = detailVoList.stream().filter(e -> !ReplenishmentPoolTypeEnum.MATERIAL.getDictCode().equals(e.getReplenishmentPoolType())).collect(Collectors.toList());
        List<String> productCodes = detailVoListFilter.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductCode()))
                .map(x -> x.getProductCode()).distinct().collect(Collectors.toList());
        Map<String, BigDecimal> materialMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(productCodes)) {
            List<MaterialVo> materialVoList = materialVoService.findByMaterialCodes(Sets.newHashSet(productCodes));
            materialMap = materialVoList.stream().collect(Collectors.toMap(l -> l.getMaterialCode(), x -> ObjectUtils.defaultIfNull(x.getTaxRate(), BigDecimal.ZERO)));
        }
        if (CollectionUtils.isEmpty(detailVoListFilter)) {
            log.warn("====================未在DMS找到对应的发货费用信息");
            //“TPM是否获取”状态，获取后更新为是
            List<String> poolDetailCodeList = detailVoList.stream().map(e -> e.getPoolDetailCode()).collect(Collectors.toList());
            ReplenishmentPoolDetailDto poolDetailDto = new ReplenishmentPoolDetailDto();
            poolDetailDto.setPoolDetailCodeList(poolDetailCodeList);
            poolDetailDto.setObtain(BooleanEnum.TRUE.getCapital());
            replenishmentPoolDetailVoService.updateWithholdingStatusByDetailCodes(poolDetailDto);
            return;
        }
        //发货取出库单编码，清空取相反数
        detailVoListFilter.forEach(e -> {
            if (ReplenishmentPoolOperationType.DELIVERY.getCode().equals(e.getOperationType())) {
                e.setBusinessCode(e.getWarehouseOrderCode());
            } else if (ReplenishmentPoolOperationType.MANUAL_CLEAR.getCode().equals(e.getOperationType())) {
                e.setOperationAmount(e.getOperationAmount().negate());
            }
        });
        Map<String, String> replenishmentMap = detailVoListFilter.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductCode()))
                .collect(Collectors.toMap(x -> x.getBusinessCode(), l -> l.getProductCode(), (a, b) -> a));
        List<DeliveryReplenishmentPoolDetail> details = new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(detailVoListFilter, ReplenishmentPoolDetailVo.class, DeliveryReplenishmentPoolDetail.class, LinkedHashSet.class, ArrayList.class));
        List<String> auditDetailCodes = details.stream().filter(e -> ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode().equals(e.getReplenishmentPoolType())).filter(Objects::nonNull)
                .map(e -> e.getParentBusinessCode()).collect(Collectors.toList());
        Map<String, String> auditMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(auditDetailCodes)) {
            List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findByAuditDetailCodes(auditDetailCodes);
            auditMap = auditDetails.stream().collect(Collectors.toMap(e -> e.getAuditDetailCode(), e -> e.getSchemeDetailCode()));
        }
        List<String> ruleCodes = generateCodeService.generateCode(DeliveryReplenishmentConstant.PREFIX_CODE, details.size());
        Integer index = 0;
        Map<String, String> finalAuditMap = auditMap;
        for (DeliveryReplenishmentPoolDetail e : details) {
            e.setManageReportWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
            e.setRuleCode(ruleCodes.get(index++));
            e.setId(null);
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setTenantCode(TenantUtils.getTenantCode());
            e.setBeWriteOff(BooleanEnum.FALSE.getCapital());
            e.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
            //此处不需要了
//            if (ReplenishmentPoolOperationType.MANUAL_CLEAR.getCode().equals(e.getOperationType())) {
//                e.setBeWriteOff(BooleanEnum.TRUE.getCapital());
//                e.setWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
//            } else {
//                e.setBeWriteOff(BooleanEnum.FALSE.getCapital());
//                e.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
//            }
            e.setUniqueKey(e.getParentCode() + e.getBusinessCode());
            e.setOperationAmount(e.getOperationAmount());
            e.setOperationNotTaxAmount(e.getOperationAmount());
            if (replenishmentMap.containsKey(e.getBusinessCode())) {
                String productCode = replenishmentMap.get(e.getBusinessCode());
                BigDecimal taxRate = BigDecimal.ONE;
                if (materialMap.containsKey(productCode)) {
                    taxRate = taxRate.add(materialMap.get(productCode));
                    BigDecimal operationNotTaxAmount = e.getOperationAmount().divide(taxRate, 2, BigDecimal.ROUND_HALF_DOWN);
                    e.setOperationNotTaxAmount(operationNotTaxAmount);
                }
            }
            e.setSchemeDetailCode(finalAuditMap.get(e.getParentBusinessCode()));
        }
        deliveryReplenishmentPoolDetailRepository.saveBatch(details);

        //“TPM是否获取”状态，获取后更新为是
        List<String> poolDetailCodeList = details.stream().map(e -> e.getPoolDetailCode()).collect(Collectors.toList());
        ReplenishmentPoolDetailDto poolDetailDto = new ReplenishmentPoolDetailDto();
        poolDetailDto.setPoolDetailCodeList(poolDetailCodeList);
        poolDetailDto.setObtain(BooleanEnum.TRUE.getCapital());
        replenishmentPoolDetailVoService.updateWithholdingStatusByDetailCodes(poolDetailDto);
    }

    /**
     * 发货费用冲销定时任务
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deliveryReplenishmentWriteOff(List<String> businessCodes) {
        //仅冲销”业务日期“为冲销月份的数据
        List<DeliveryReplenishmentPoolDetail> detailsList = deliveryReplenishmentPoolDetailRepository.findByUnWriteOff(businessCodes);
        if (CollectionUtil.isEmpty(detailsList)) {
            log.warn("=====>    未找到可冲销的发货费用信息    <=====");
            return;
        }

        List<DeliveryReplenishmentPoolDetail> details = new ArrayList<>(detailsList.stream()
                .collect(Collectors.toMap(DeliveryReplenishmentPoolDetail::getRuleCode, v -> v, (a, b) -> a)).values());
        List<String> auditDetailCodes = details.stream().filter(e -> DeliveryDetailTypeEnum.AUDIT.getCode().equals(e.getOperationType()))
                .map(DeliveryReplenishmentPoolDetail::getBusinessCode).collect(Collectors.toList());
        Map<String, MarketingAuditDetail> auditDetailMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(auditDetailCodes)) {
            List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findByAuditDetailCodes(auditDetailCodes);
            auditDetailMap = auditDetails.stream().collect(Collectors.toMap(MarketingAuditDetail::getAuditDetailCode, v -> v, (a, b) -> a));
        }
        List<WithHoldingWriteOffDto> dtoOneList = new ArrayList<>();
        for (DeliveryReplenishmentPoolDetail e : details) {
            WithHoldingWriteOffDto dto = new WithHoldingWriteOffDto();
            if (ReplenishmentPoolOperationType.DELIVERY.getCode().equals(e.getOperationType())) {
                dto.setWriteOffType(WriteOffTypeEnum.ORDER.getDictCode());
                if (ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode().equals(e.getReplenishmentPoolType())) {
                    dto.setActivitiesDetailCode(StringUtils.isNotBlank(e.getSchemeDetailCode()) ? e.getSchemeDetailCode() : e.getParentCode());
                } else if (ReplenishmentPoolTypeEnum.TAIL_DIFF.getDictCode().equals(e.getReplenishmentPoolType())) {
                    dto.setActivitiesDetailCode(e.getParentCode());
                } else {
                    continue;
                }
            } else if (DeliveryDetailTypeEnum.FEE_CASH.getCode().equals(e.getOperationType())) {
                dto.setWriteOffType(WriteOffTypeEnum.CASH.getDictCode());
                dto.setActivitiesDetailCode(e.getSchemeDetailCode());
                dto.setBeReimburse(BooleanEnum.TRUE.getCapital());
            } else if (DeliveryDetailTypeEnum.FEE_CLOSE.getCode().equals(e.getOperationType())) {
                dto.setWriteOffType(WriteOffTypeEnum.CLOSE.getDictCode());
                dto.setActivitiesDetailCode(e.getSchemeDetailCode());
            } else if (DeliveryDetailTypeEnum.AUDIT.getCode().equals(e.getOperationType())) {
                dto.setWriteOffType(WriteOffTypeEnum.AUDIT.getDictCode());
                dto.setActivitiesDetailCode(e.getSchemeDetailCode());
                if (auditDetailMap.containsKey(e.getBusinessCode())) {
                    MarketingAuditDetail auditDetail = auditDetailMap.get(e.getBusinessCode());
                    dto.setBeFullAudit(auditDetail.getBeFullAudit());
                }
            } else if (DeliveryDetailTypeEnum.MANUAL_CLEAR.getCode().equals(e.getOperationType())) {
                dto.setWriteOffType(WriteOffTypeEnum.CLOSE.getDictCode());
                dto.setActivitiesDetailCode(StringUtils.isNotBlank(e.getSchemeDetailCode()) ? e.getSchemeDetailCode() : e.getParentCode());
            } else {
                continue;
            }

            e.setTempCode(dto.getActivitiesDetailCode());
            dto.setWriteOffAmount(e.getOperationAmount());
            dto.setAuditCreateAccount(e.getCreateAccount());
            dto.setPositionCode(e.getPositionCode());
            dto.setCreateTime(e.getCreateTime());
            dto.setWriteOffYears(DateUtil.format(DateUtil.parse(e.getDeliveryTime(), "yyyy-MM-dd"), "yyyy-MM"));
            dto.setSourceCode(e.getBusinessCode());
            dto.setRuleCode(e.getRuleCode());
            dtoOneList.add(dto);
        }

        List<WithHoldingWriteOffDto> dtoList = new ArrayList<>(dtoOneList.stream()
                .collect(Collectors.toMap(WithHoldingWriteOffDto::getRuleCode, v -> v, (a, b) -> a)).values());

        //结案冲销和其他冲销分开冲销，避免计提余额校验不通过
        Set<String> codes = Sets.newHashSet();
        Map<String, List<WithHoldingWriteOffVo>> auditMap = withHoldingWriteOffSendHecService.writeOff(dtoList.stream().filter(e -> WriteOffTypeEnum.AUDIT.getDictCode().equals(e.getWriteOffType())).collect(Collectors.toList()));
        Map<String, List<WithHoldingWriteOffVo>> othersMap = withHoldingWriteOffSendHecService.writeOff(dtoList.stream().filter(e -> !WriteOffTypeEnum.AUDIT.getDictCode().equals(e.getWriteOffType())).collect(Collectors.toList()));
        Set<String> codesAudit = MapUtils.isNotEmpty(auditMap) ? auditMap.values().stream().flatMap(List::stream).map(e -> e.getRuleCode()).collect(Collectors.toSet()) : new HashSet<>();
        Set<String> codesOthers = MapUtils.isNotEmpty(othersMap) ? othersMap.values().stream().flatMap(List::stream).map(e -> e.getRuleCode()).collect(Collectors.toSet()) : new HashSet<>();
        codes.addAll(!CollectionUtils.isEmpty(codesAudit) ? codesAudit : new ArrayList<>());
        codes.addAll(!CollectionUtils.isEmpty(codesOthers) ? codesOthers : new ArrayList<>());
        if (!CollectionUtils.isEmpty(codes)) {
            List<DeliveryReplenishmentPoolDetail> detailsSucess = details.stream().filter(e -> codes.contains(e.getRuleCode())).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(detailsSucess)) {
                detailsSucess.forEach(e -> {
                    e.setBeWriteOff(BooleanEnum.TRUE.getCapital());
                    e.setWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
                });
                deliveryReplenishmentPoolDetailRepository.updateBatchById(detailsSucess);
                details.removeAll(detailsSucess);
            }
        }
        //如果是货补的，通过活动明细编码没有冲销成功，再拿费用池明细编号去冲销
        if (!CollectionUtils.isEmpty(details)) {
            details = details.stream().filter(e -> ReplenishmentPoolOperationType.DELIVERY.getCode().equals(e.getOperationType()) && ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode().equals(e.getReplenishmentPoolType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(details)) {
                return;
            }
            dtoList.clear();
            for (DeliveryReplenishmentPoolDetail e : details) {
                WithHoldingWriteOffDto dto = new WithHoldingWriteOffDto();
                dto.setWriteOffType(WriteOffTypeEnum.ORDER.getDictCode());
                dto.setActivitiesDetailCode(e.getParentCode());

                e.setTempCode(dto.getActivitiesDetailCode());
                dto.setWriteOffAmount(e.getOperationAmount());
                dto.setAuditCreateAccount(e.getCreateAccount());
                dto.setPositionCode(e.getPositionCode());
                dto.setCreateTime(e.getCreateTime());
                dto.setWriteOffYears(DateUtil.format(DateUtil.parse(e.getDeliveryTime(), "yyyy-MM-dd"), "yyyy-MM"));
                dto.setSourceCode(e.getBusinessCode());
                dtoList.add(dto);
            }

            Set<String> codesRe = withHoldingWriteOffSendHecService.writeOff(dtoList).keySet();

            if (!CollectionUtils.isEmpty(codesRe)) {
                List<DeliveryReplenishmentPoolDetail> detailsSucessRe = details.stream().filter(e -> codesRe.contains(e.getTempCode())).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(detailsSucessRe)) {
                    detailsSucessRe.forEach(e -> {
                        e.setBeWriteOff(BooleanEnum.TRUE.getCapital());
                        e.setWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
                    });
                    deliveryReplenishmentPoolDetailRepository.updateBatchById(detailsSucessRe);
                    details.removeAll(detailsSucessRe);
                }
            }
        }
    }

    /**
     * 创建
     *
     * @param dtoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(List<DeliveryReplenishmentPoolDetailDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        Collection<DeliveryReplenishmentPoolDetail> details = nebulaToolkitService.copyCollectionByWhiteList(dtoList, DeliveryReplenishmentPoolDetailDto.class, DeliveryReplenishmentPoolDetail.class, LinkedHashSet.class, ArrayList.class);
        details.forEach(e -> {
            e.setManageReportWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
            e.setTenantCode(TenantUtils.getTenantCode());
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        deliveryReplenishmentPoolDetailRepository.saveBatch(details);
    }

    /**
     * 根据活动编码查询金额汇总
     *
     * @param schemeDetailCodes
     * @param yearMonthLy
     * @return
     */
    @Override
    public Map<String, BigDecimal> findBySchemeDetailCodes(List<String> schemeDetailCodes, String yearMonthLy) {
        Validate.notEmpty(schemeDetailCodes, "活动编码不能为空");
        Validate.notBlank(yearMonthLy, "年月不能为空");
        Map<String, BigDecimal> map = new HashMap<>();

        List<DeliveryReplenishmentPoolDetailVo> list = new ArrayList<>();
        List<List<String>> partitionList = Lists.partition(schemeDetailCodes, 800);
        partitionList.forEach(e -> list.addAll(deliveryReplenishmentPoolDetailRepository.findBySchemeDetailCodes(e, yearMonthLy)));

        if (!CollectionUtils.isEmpty(list)) {
            map = list.stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode(), Collectors.mapping(e -> e.getOperationAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        return map;
    }

    @Override
    public Map<String, BigDecimal> findManualClearAndDeliveryBySchemeDetailCodes(List<String> schemeDetailCodes, String years) {
        Validate.notEmpty(schemeDetailCodes, "活动编码不能为空");
        Map<String, BigDecimal> map = new HashMap<>();

        List<DeliveryReplenishmentPoolDetailVo> list = new ArrayList<>();
        List<List<String>> partitionList = Lists.partition(schemeDetailCodes, 500);
        partitionList.forEach(e -> list.addAll(deliveryReplenishmentPoolDetailRepository.findManualClearAndDeliveryBySchemeDetailCodes(e)));

        if (!CollectionUtils.isEmpty(list)) {
            if (StringUtils.isNotEmpty(years)) {
                List<DeliveryReplenishmentPoolDetailVo> filterList = list.stream().filter(e -> years.equals(DateUtil.format(DateUtil.parse(e.getDeliveryTime(), "yyyy-MM-dd"), "yyyy-MM"))).collect(Collectors.toList());
                map = filterList.stream().collect(Collectors.groupingBy(DeliveryReplenishmentPoolDetailVo::getSchemeDetailCode, Collectors.mapping(e -> e.getOperationAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                return map;
            }
            map = list.stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode(), Collectors.mapping(e -> e.getOperationAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        return map;
    }

    /**
     * 根据结案明细编码查询操作类型=清空或费用关闭的“费用金额”并汇总
     *
     * @param auditDetailCodes
     * @return
     */
    @Override
    public Map<String, BigDecimal> findByAuditDetailCodes(List<String> auditDetailCodes) {
        Map<String, BigDecimal> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(auditDetailCodes)) {
            return map;
        }
        List<String> businessCodes = new ArrayList<>();
        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findByAuditDetailCodes(auditDetailCodes);
        Map<String, List<String>> auditCashMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(feeCashDetailVos)) {
            auditCashMap = feeCashDetailVos.stream().collect(Collectors.groupingBy(e -> e.getAuditDetailCode(), Collectors.mapping(e -> e.getCashDetailCode(), Collectors.toList())));
            List<String> cashDetailCodes = feeCashDetailVos.stream().map(e -> e.getCashDetailCode()).collect(Collectors.toList());
            businessCodes.addAll(cashDetailCodes);
        }
        List<DeliveryReplenishmentPoolDetail> list = deliveryReplenishmentPoolDetailRepository.findByAuditDetailCodes(auditDetailCodes, businessCodes);
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        //费用关闭
        for (Map.Entry<String, List<String>> entry : auditCashMap.entrySet()) {
            List<String> cashDetailCodes = entry.getValue();
            BigDecimal amount = list.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getBusinessCode()) && DeliveryDetailTypeEnum.FEE_CLOSE.getCode().equals(e.getOperationType()) && cashDetailCodes.contains(e.getBusinessCode()))
                    .filter(Objects::nonNull).map(e -> e.getOperationAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            amount = amount == null ? BigDecimal.ZERO : amount;
            if (map.containsKey(entry.getKey())) {
                map.put(entry.getKey(), map.get(entry.getKey()).add(amount));
            } else {
                map.put(entry.getKey(), amount);
            }
        }
        //清空
        List<DeliveryReplenishmentPoolDetail> clearList = list.stream().filter(e -> StringUtils.isNotBlank(e.getParentBusinessCode()) && DeliveryDetailTypeEnum.MANUAL_CLEAR.getCode().equals(e.getOperationType()) && auditDetailCodes.contains(e.getParentBusinessCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(clearList)) {
            Map<String, BigDecimal> clearMap = clearList.stream().collect(Collectors.groupingBy(e -> e.getParentBusinessCode(), Collectors.mapping(e -> e.getOperationAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            for (String auditDetailCode : auditDetailCodes) {
                if (clearMap.containsKey(auditDetailCode)) {
                    if (map.containsKey(auditDetailCode)) {
                        map.put(auditDetailCode, map.get(auditDetailCode).add(clearMap.get(auditDetailCode)));
                    } else {
                        map.put(auditDetailCode, clearMap.get(auditDetailCode));
                    }
                }
            }
        }
        return map;
    }

    /**
     * 根据结案明细编码查询
     *
     * @param auditDetailCodes
     * @return
     */
    @Override
    public Map<String, List<DeliveryReplenishmentPoolDetailVo>> findByAuditDetailCodesAll(List<String> auditDetailCodes) {
        Map<String, List<DeliveryReplenishmentPoolDetailVo>> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(auditDetailCodes)) {
            return map;
        }
        List<DeliveryReplenishmentPoolDetail> detailList = deliveryReplenishmentPoolDetailRepository.findByAuditDetailCodesAll(auditDetailCodes);
        if (CollectionUtils.isEmpty(auditDetailCodes)) {
            return map;
        }
        Collection<DeliveryReplenishmentPoolDetailVo> detailVoList = nebulaToolkitService.copyCollectionByWhiteList(detailList, DeliveryReplenishmentPoolDetail.class, DeliveryReplenishmentPoolDetailVo.class, LinkedHashSet.class, ArrayList.class);
        map = detailVoList.stream().collect(Collectors.groupingBy(e -> e.getParentBusinessCode()));
        return map;
    }

    /**
     * 根据活动明细编码查询
     *
     * @param schemeDetailCodes
     * @return
     */
    @Override
    public Map<String, List<DeliveryReplenishmentPoolDetailVo>> findBySchemeDetailCodesAll(List<String> schemeDetailCodes) {
        Map<String, List<DeliveryReplenishmentPoolDetailVo>> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return map;
        }
        List<DeliveryReplenishmentPoolDetail> detailList = deliveryReplenishmentPoolDetailRepository.findBySchemeDetailCodesAll(schemeDetailCodes);
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return map;
        }
        Collection<DeliveryReplenishmentPoolDetailVo> detailVoList = nebulaToolkitService.copyCollectionByWhiteList(detailList, DeliveryReplenishmentPoolDetail.class, DeliveryReplenishmentPoolDetailVo.class, LinkedHashSet.class, ArrayList.class);
        map = detailVoList.stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode()));
        return map;
    }

    @Autowired
    private WithHoldingWriteOffRepository withHoldingWriteOffRepository;

    @Autowired
    private DmsDeliveryOrderService dmsDeliveryOrderService;

    @Override
    public void updateDeliveryReplenishmentPoolDetailStatus(List<String> ruleCodes) {
        dmsDeliveryOrderService.findByOrderCodes(ruleCodes);
        List<WithHoldingWriteOff> writeOffList = withHoldingWriteOffRepository.lambdaQuery()
                .in(WithHoldingWriteOff::getRuleCode, ruleCodes)
                .list();
        if (CollectionUtils.isEmpty(writeOffList)) {
            return;
        }
        List<String> ruleCodeList = writeOffList.stream().map(x -> x.getRuleCode()).collect(Collectors.toList());
        deliveryReplenishmentPoolDetailRepository.lambdaUpdate()
                .set(DeliveryReplenishmentPoolDetail::getWriteOffStatus, WriteOffStatusEnum.ON_WRITE_OFF.getDictCode())
                .set(DeliveryReplenishmentPoolDetail::getBeWriteOff, BooleanEnum.TRUE.getCapital())
                .in(DeliveryReplenishmentPoolDetail::getRuleCode, ruleCodeList)
                .update();
        //更新费用关闭的冲销状态
        List<String> sourceCodeList = writeOffList.stream().filter(e -> WriteOffTypeEnum.CLOSE.getDictCode().equals(e.getWriteOffType()))
                .map(x -> x.getSourceCode()).collect(Collectors.toList());
        deliveryReplenishmentPoolDetailRepository.lambdaUpdate()
                .set(DeliveryReplenishmentPoolDetail::getWriteOffStatus, WriteOffStatusEnum.ON_WRITE_OFF.getDictCode())
                .set(DeliveryReplenishmentPoolDetail::getBeWriteOff, BooleanEnum.TRUE.getCapital())
                .in(DeliveryReplenishmentPoolDetail::getBusinessCode, sourceCodeList)
                .eq(DeliveryReplenishmentPoolDetail::getOperationType, DeliveryDetailTypeEnum.FEE_CLOSE.getCode())
                .update();

    }

    @Override
    public void backDeliveryReplenishmentWriteOff(List<String> businessCodes) {
        //仅冲销”业务日期“为冲销月份的数据
        List<DeliveryReplenishmentPoolDetail> detailsList = deliveryReplenishmentPoolDetailRepository.findBackUnWriteOff(businessCodes);
        if (CollectionUtil.isEmpty(detailsList)) {
            log.warn("=====>    未找到进货返利结案可冲销的发货费用信息    <=====");
        } else {
            List<DeliveryReplenishmentPoolDetail> details = new ArrayList<>(detailsList.stream()
                    .collect(Collectors.toMap(DeliveryReplenishmentPoolDetail::getRuleCode, v -> v, (a, b) -> a)).values());
            List<String> auditDetailCodes = details.stream().filter(e -> DeliveryDetailTypeEnum.AUDIT.getCode().equals(e.getOperationType()))
                    .map(DeliveryReplenishmentPoolDetail::getBusinessCode).collect(Collectors.toList());
            Map<String, MarketingAuditDetail> auditDetailMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(auditDetailCodes)) {
                List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findByAuditDetailCodes(auditDetailCodes);
                auditDetailMap = auditDetails.stream().collect(Collectors.toMap(MarketingAuditDetail::getAuditDetailCode, v -> v, (a, b) -> a));
            }
            List<WithHoldingWriteOffDto> dtoOneList = new ArrayList<>();
            for (DeliveryReplenishmentPoolDetail e : details) {
                WithHoldingWriteOffDto dto = new WithHoldingWriteOffDto();
                dto.setWriteOffType(WriteOffTypeEnum.AUDIT.getDictCode());
                dto.setActivitiesDetailCode(e.getSchemeDetailCode());
                if (auditDetailMap.containsKey(e.getBusinessCode())) {
                    MarketingAuditDetail auditDetail = auditDetailMap.get(e.getBusinessCode());
                    dto.setBeFullAudit(auditDetail.getBeFullAudit());
                }
                dto.setWriteOffAmount(e.getOperationAmount());
                dto.setAuditCreateAccount(e.getCreateAccount());
                dto.setPositionCode(e.getPositionCode());
                dto.setCreateTime(e.getCreateTime());
                dto.setWriteOffYears(DateUtil.format(DateUtil.parse(e.getDeliveryTime(), "yyyy-MM-dd"), "yyyy-MM"));
                dto.setSourceCode(e.getBusinessCode());
                dto.setRuleCode(e.getRuleCode());
                dtoOneList.add(dto);
            }
            List<WithHoldingWriteOffDto> dtoList = new ArrayList<>(dtoOneList.stream()
                    .collect(Collectors.toMap(WithHoldingWriteOffDto::getRuleCode, v -> v, (a, b) -> a)).values());
            //结案冲销
            Map<String, List<BackWithHoldingWriteOffVo>> auditMap = backWithHoldingWriteOffService.writeOff(dtoList);
            List<String> codesAudit = MapUtils.isNotEmpty(auditMap) ? auditMap.values().stream().flatMap(List::stream).map(BackWithHoldingWriteOffVo::getRuleCode).distinct().collect(Collectors.toList()) : Lists.newArrayList();

            if (!CollectionUtils.isEmpty(codesAudit)) {
                List<DeliveryReplenishmentPoolDetail> detailsSuccess = details.stream().filter(e -> codesAudit.contains(e.getRuleCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(detailsSuccess)) {
                    detailsSuccess.forEach(l -> {
                        l.setManageReportWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
                    });
                    deliveryReplenishmentPoolDetailRepository.updateBatchById(detailsSuccess);
                }
            }

        }
        String curMonth = DateUtil.format(new Date(), "yyyy-MM");
        Date lastMonthDate = DateUtil.offsetMonth(new Date(), -1);
        String lastMonth = DateUtil.format(lastMonthDate, "yyyy-MM");
        // 查询费用冲销中的兑付冲销数据
        List<WithHoldingWriteOff> cashAndOrderWriteOffList = withHoldingWriteOffRepository.findCashAndOrderWriteOff(businessCodes, Lists.newArrayList(curMonth, lastMonth));
        if (CollectionUtils.isEmpty(cashAndOrderWriteOffList)) {
            log.warn("=====>    未找到进货返利兑付/发货可冲销的发货费用信息    <=====");
        } else {
            List<WithHoldingWriteOffDto> dtoList = new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(cashAndOrderWriteOffList, WithHoldingWriteOff.class, WithHoldingWriteOffDto.class, LinkedHashSet.class, ArrayList.class));
            // 兑付和发货冲销
            backWithHoldingWriteOffService.cashOrderCloseWriteOff(dtoList, "cash_order");
        }
        // 查询费用冲销关闭冲销数据
        List<WithHoldingWriteOff> closeWriteOffList = withHoldingWriteOffRepository.findCloseWriteOff(businessCodes, Lists.newArrayList(curMonth, lastMonth));
        if (CollectionUtils.isEmpty(closeWriteOffList)) {
            log.warn("=====>    未找到进货返利关闭可冲销的发货费用信息    <=====");
        } else {
            List<WithHoldingWriteOffDto> dtoList = new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(closeWriteOffList, WithHoldingWriteOff.class, WithHoldingWriteOffDto.class, LinkedHashSet.class, ArrayList.class));
            // 关闭冲销
            backWithHoldingWriteOffService.cashOrderCloseWriteOff(dtoList, "close");
        }
    }
}
