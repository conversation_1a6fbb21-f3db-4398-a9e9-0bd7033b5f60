package com.biz.crm.tpm.business.pay.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingCollectDto;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingCollectService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingSendHecService;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingCollectVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/pay/withHoldingCollect")
@Slf4j
@Api(tags = "计提汇总")
public class WithHoldingCollectController {

    /**
     * 服务对象
     */
    @Autowired(required = false)
    private WithHoldingCollectService withHoldingCollectService;
    /**
     * 服务对象
     */
    @Autowired(required = false)
    private WithHoldingSendHecService withHoldingSendHecService;

    /**
     * 推送
     *
     * @param idList 主键结合
     * @return
     */
    @ApiOperation(value = "推送")
    @PatchMapping("push")
    public Result<?> push(@ApiParam(name = "idList", value = "主键集合") @RequestParam List<String> idList) {
        try {
            this.withHoldingSendHecService.pushCollect(idList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    @ApiOperation(value = "通过编号查询单条数据")
    @GetMapping("findByCode")
    public Result<WithHoldingCollectVo> findByCode(
            @ApiParam(name = "cacheKey", value = "缓存键")@RequestParam String cacheKey,
            @ApiParam(name = "code", value = "编号", required = true) String code) {
        try {
            WithHoldingCollectVo auditVo = this.withHoldingCollectService.findByCode(cacheKey, code);
            return Result.ok(auditVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 提交审批
     *
     * @param idList 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "提交审批")
    @PostMapping("submit")
    public Result<?> submit(@RequestBody List<String> idList) {
        try {
            withHoldingCollectService.submit(idList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 流程撤回
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "流程撤回")
    @GetMapping("recover")
    public Result<?> recover(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                             @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
        try {
            this.withHoldingCollectService.recover(code, remark);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "计提汇总删除（逻辑）")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam List<String> idList) {
        try {
            this.withHoldingCollectService.delete(idList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "modifyActivityPrepayRecord")
    @PostMapping("modifyActivityPrepayRecord")
    public Result modifyActivityPrepayRecord(@RequestBody WithHoldingCollectDto dto) {
        withHoldingCollectService.modifyActivityPrepayRecord(dto);
        return Result.ok();
    }
}
