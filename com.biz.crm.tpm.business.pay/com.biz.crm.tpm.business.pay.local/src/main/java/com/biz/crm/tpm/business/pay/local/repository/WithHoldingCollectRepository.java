package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingCollect;
import com.biz.crm.tpm.business.pay.local.mapper.WithHoldingCollectMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingCollectDto;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * 费用预提汇总(WithHoldingCollect)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-01 11:23:06
 */
@Component
public class WithHoldingCollectRepository extends ServiceImpl<WithHoldingCollectMapper, WithHoldingCollect> {

    @Autowired
    private WithHoldingCollectMapper withHoldingCollectMapper;


    /**
     * 根据id集合获取详情集合
     *
     * @param ids ID集合
     * @return List<WithHoldingCollect>
     */
    public List<WithHoldingCollect> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(WithHoldingCollect::getId, ids)
                .eq(WithHoldingCollect::getTenantCode, tenantCode)
                .eq(WithHoldingCollect::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据编号与租户编号获取对象
     *
     * @param code
     * @return
     */
    public WithHoldingCollect findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .eq(WithHoldingCollect::getCollectCode, code)
                .eq(WithHoldingCollect::getTenantCode, tenantCode)
                .eq(WithHoldingCollect::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
    }

    /**
     * 根据年月加部门去查汇总数据
     *
     * @param
     * @return
     */
    public WithHoldingCollect findByUniqueKey(String deptCode, String yearMonthLy) {
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .eq(WithHoldingCollect::getDepartmentCode, deptCode)
                .eq(WithHoldingCollect::getYearMonthLy, yearMonthLy)
                .eq(WithHoldingCollect::getTenantCode, tenantCode)
                .eq(WithHoldingCollect::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
    }

    /**
     * 逻辑删除
     *
     * @param ids
     */
    public void softDelete(List<String> ids) {
        this.lambdaUpdate()
                .set(WithHoldingCollect::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
                .in(WithHoldingCollect::getId)
                .update();
    }

    /**
     * 更新计提汇总金额
     *
     * @param code
     */
    public void updateCollectAmount(String code) {
        baseMapper.updateCollectAmount(code);
    }
}

