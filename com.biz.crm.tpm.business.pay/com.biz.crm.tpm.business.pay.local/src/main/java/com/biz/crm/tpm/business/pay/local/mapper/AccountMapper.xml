<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AccountMapper">

  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.Account" id="TpmAccountMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="createAccount" column="create_account" jdbcType="VARCHAR"/>
    <result property="createName" column="create_name" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="modifyAccount" column="modify_account" jdbcType="VARCHAR"/>
    <result property="modifyName" column="modify_name" jdbcType="VARCHAR"/>
    <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    <result property="enableStatus" column="enable_status" jdbcType="VARCHAR"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="accountCode" column="account_code" jdbcType="VARCHAR"/>
    <result property="activitiesCode" column="activities_code" jdbcType="VARCHAR"/>
    <result property="activitiesName" column="activities_name" jdbcType="VARCHAR"/>
    <result property="activitiesDetailCode" column="activities_detail_code" jdbcType="VARCHAR"/>
    <result property="amount" column="amount" jdbcType="NUMERIC"/>
    <result property="auditCode" column="audit_code" jdbcType="VARCHAR"/>
    <result property="auditDetailCode" column="audit_detail_code" jdbcType="VARCHAR"/>
    <result property="availableAmount" column="available_amount" jdbcType="NUMERIC"/>
    <result property="beginTime" column="begin_time" jdbcType="TIMESTAMP"/>
    <result property="budgetSubjectsCode" column="budget_subjects_code" jdbcType="VARCHAR"/>
    <result property="budgetSubjectsName" column="budget_subjects_name" jdbcType="VARCHAR"/>
    <result property="costTypeCategoryCode" column="cost_type_category_code" jdbcType="VARCHAR"/>
    <result property="costTypeCategoryName" column="cost_type_category_name" jdbcType="VARCHAR"/>
    <result property="costTypeDetailCode" column="cost_type_detail_code" jdbcType="VARCHAR"/>
    <result property="costTypeDetailName" column="cost_type_detail_name" jdbcType="VARCHAR"/>
    <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
    <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
    <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
    <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
    <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
    <result property="payBy" column="pay_by" jdbcType="VARCHAR"/>
    <result property="productLevelCode" column="product_level_code" jdbcType="VARCHAR"/>
    <result property="productLevelName" column="product_level_name" jdbcType="VARCHAR"/>
    <result property="terminalCode" column="terminal_code" jdbcType="VARCHAR"/>
    <result property="terminalName" column="terminal_name" jdbcType="VARCHAR"/>
  </resultMap>

  <update id="enableOrDisableByIds">
    update tpm_account t set t.enable_status = #{enableCode}
    where t.id in
    <foreach collection="idList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <select id="findByConditions" resultMap="TpmAccountMap">
    select t.* from tpm_account t
    <where>
      t.del_flag = '${@<EMAIL>()}'
      and t.tenant_code = #{dto.tenantCode}
    <if test="dto.accountCode != null and dto.accountCode != '' ">
      <bind name = "accountCode" value = " '%' + dto.accountCode + '%' " />
      and t.account_code like #{accountCode}
    </if>
    <if test="dto.accountStatus != null and dto.accountStatus != '' ">
      and t.account_status = #{dto.accountStatus}
    </if>
    <if test="dto.activitiesDetailCode != null and dto.activitiesDetailCode != '' ">
      <bind name = "activitiesDetailCode" value = " '%' + dto.dto.activitiesDetailCode + '%' " />
      and t.activities_detail_code = #{activitiesDetailCode}
    </if>
    <if test="dto.auditDetailCode != null and dto.auditDetailCode != '' ">
      <bind name = "auditDetailCode" value = " '%' + dto.auditDetailCode + '%' " />
      and t.audit_detail_code like #{auditDetailCode}
    </if>
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>

