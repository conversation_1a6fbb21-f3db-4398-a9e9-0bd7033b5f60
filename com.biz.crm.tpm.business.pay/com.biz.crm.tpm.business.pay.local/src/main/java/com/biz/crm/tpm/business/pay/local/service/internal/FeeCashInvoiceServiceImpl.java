package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.lang.Assert;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashInvoiceService;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashInvoiceVo;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FeeCashInvoiceServiceImpl extends BusinessPageCacheServiceImpl<FeeCashInvoiceVo, FeeCashInvoiceDto> implements FeeCashInvoiceService {

    @Resource
    private InvoiceService invoiceService;

    /**
     * 按结案明细更新关联的发票
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public void updateItemCache(String cacheKey, List<String> auditDetailCodes, List<FeeCashInvoiceDto> itemList) {
        List<FeeCashInvoiceDto> cacheList = findCacheList(cacheKey);
        List<FeeCashInvoiceDto> deleteList = cacheList.stream().filter(e -> auditDetailCodes.contains(e.getAuditDetailCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            deleteList.forEach(e -> e.setChecked(BooleanEnum.TRUE.getNumStr()));
            deleteCacheList(cacheKey, deleteList);
        }
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList.forEach(e -> {
                Assert.notNull(e.getInvoiceNo(), "发票号码不能为空!");
                Assert.notNull(e.getReimbursementAmount(), "实际报销金额不能为空!");
                Assert.notNull(e.getPriceAndTax(), "发票价税合计金额不能为空!");
                Assert.isTrue(e.getPriceAndTax().compareTo(e.getReimbursementAmount()) >= 0,
                        "结案明细[" + e.getAuditDetailCode() + "]关联的发票[" + e.getInvoiceNo() + "]报销金额高于了发票金额，请修改报销金额或更换发票！!");

            });
            Map<String, List<FeeCashInvoiceDto>> invoceUsedMap = itemList.stream().collect(Collectors.groupingBy(FeeCashInvoiceDto::getInvoiceNo));
            List<InvoiceVo> invoiceVos = invoiceService.findByInvoiceNoList(invoceUsedMap.keySet());
            Map<String, InvoiceVo> invoiceVoMap = invoiceVos.stream().filter(x -> ObjectUtils.isNotEmpty(x) && ObjectUtils.isNotEmpty(x.getInvoiceNo()))
                    .collect(Collectors.toMap(x -> x.getInvoiceNo(), Function.identity()));
            invoceUsedMap.forEach((k, list) -> {
                FeeCashInvoiceDto e = list.get(list.size() - 1);
                InvoiceVo invoiceVo = invoiceVoMap.get(k);
                BigDecimal reimbursementAmount = list.stream().map(FeeCashInvoiceDto::getReimbursementAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                Assert.isTrue(invoiceVo.getPriceAndTax().compareTo(reimbursementAmount) >= 0,
                        "结案明细[" + e.getAuditDetailCode() + "]关联的发票[" + e.getInvoiceNo() + "]报销金额高于了发票金额，请修改报销金额或更换发票！!");
                invoiceVo.setPriceAndTax(invoiceVo.getPriceAndTax().subtract(reimbursementAmount));

            });
        }
        addItemCache(cacheKey, itemList);
    }

    /**
     * 发票金额汇总
     *
     * @param cacheKey
     * @return
     */
    @Override
    public FeeCashInvoiceVo invoiceTotal(String cacheKey) {
        FeeCashInvoiceVo vo = new FeeCashInvoiceVo();
        List<FeeCashInvoiceDto> cacheList = findCacheList(cacheKey);
        BigDecimal priceAndTaxTotal = BigDecimal.ZERO;
        BigDecimal reimbursementAmountTotal = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(cacheList)) {
            for (FeeCashInvoiceDto dto : cacheList) {
                priceAndTaxTotal = priceAndTaxTotal.add(Optional.ofNullable(dto.getPriceAndTax()).orElse(BigDecimal.ZERO));
                reimbursementAmountTotal = reimbursementAmountTotal.add(Optional.ofNullable(dto.getReimbursementAmount()).orElse(BigDecimal.ZERO));
            }
        }
        vo.setPriceAndTaxTotal(priceAndTaxTotal);
        vo.setReimbursementAmountTotal(reimbursementAmountTotal);
        return vo;
    }

    /**
     * 查询剩余可报销金额
     *
     * @param cacheKey
     * @return
     */
    @Override
    public List<FeeCashInvoiceDto> availableReimbursementAmount(String cacheKey, String auditDetailCode) {
        List<FeeCashInvoiceDto> cacheList = findCacheList(cacheKey);
        if (CollectionUtils.isEmpty(cacheList)) {
            return new ArrayList<>();
        }
        cacheList = cacheList.stream().filter(e -> !e.getAuditDetailCode().equals(auditDetailCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cacheList)) {
            return new ArrayList<>();
        }
        List<FeeCashInvoiceDto> invoiceList = new ArrayList<>();
        Map<String, List<FeeCashInvoiceDto>> invoiceMap = cacheList.stream().collect(Collectors.groupingBy(e -> e.getInvoiceNo() + ":" + e.getInvoiceDetailNo()));
        invoiceMap.forEach((k, v) -> {
            FeeCashInvoiceDto invoiceDto = new FeeCashInvoiceDto();
            String[] item = k.split(":");
            invoiceDto.setInvoiceNo(item[0]);
            invoiceDto.setInvoiceDetailNo(item[1]);
            BigDecimal reimbursementAmount = v.stream().map(e -> Optional.ofNullable(e.getReimbursementAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            invoiceDto.setAvailableReimbursementAmount(v.get(0).getPriceAndTax().subtract(reimbursementAmount));
            invoiceList.add(invoiceDto);
        });
        return invoiceList;
    }
}
