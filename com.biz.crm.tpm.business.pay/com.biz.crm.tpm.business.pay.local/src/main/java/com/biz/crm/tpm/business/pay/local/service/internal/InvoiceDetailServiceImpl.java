package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.InvoiceDetail;
import com.biz.crm.tpm.business.pay.local.repository.InvoiceDetailRepository;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceDetailService;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailPageVo;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@Service
@Transactional
public class InvoiceDetailServiceImpl implements InvoiceDetailService {

    @Autowired
    private InvoiceDetailRepository repository;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;


    /**
     * 保存数据
     *
     * @param detailList
     * @param oldInvoiceNo
     */
    @Override
    public void saveBatchList(List<InvoiceDetailVo> detailList, String oldInvoiceNo, String nowInvoiceNo) {
        if (ObjectUtils.isNotEmpty(oldInvoiceNo)) {
            repository.deleteByInvoiceNo(oldInvoiceNo);
        }
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        List<InvoiceDetail> details = (List<InvoiceDetail>) nebulaToolkitService.copyCollectionByWhiteList(detailList, InvoiceDetailVo.class, InvoiceDetail.class, HashSet.class, ArrayList.class);
        Integer count = 1;
        for (InvoiceDetail detail : details) {
            detail.setId(null);
            detail.setInvoiceNo(nowInvoiceNo);
            if (ObjectUtils.isEmpty(detail.getInvoiceDetailNo())){
                detail.setInvoiceDetailNo(String.valueOf(count*10));
            }
            count++;
        }
        repository.saveBatch(details);
    }

    /**
     * 查询数据
     *
     * @param invoiceNoList
     * @return
     */
    @Override
    public List<InvoiceDetailVo> findListByInvoiceNoList(List<String> invoiceNoList) {
        List<InvoiceDetail> detailList = repository.findByInvoiceNoList(invoiceNoList);
        if (CollectionUtils.isEmpty(detailList)) {
            return Lists.newArrayList();
        }
        return (List<InvoiceDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(detailList, InvoiceDetail.class, InvoiceDetailVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 查询发票明细分页
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<InvoiceDetailPageVo> findInvoiceDetailList(Pageable pageable, InvoiceDetailPageVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<InvoiceDetailPageVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return repository.findInvoiceDetailList(page, vo);
    }

    @Override
    public void deleteByInvoiceNoList(List<String> invoiceNoList) {
        this.repository.deleteByInvoiceNoList(invoiceNoList);
    }
}
