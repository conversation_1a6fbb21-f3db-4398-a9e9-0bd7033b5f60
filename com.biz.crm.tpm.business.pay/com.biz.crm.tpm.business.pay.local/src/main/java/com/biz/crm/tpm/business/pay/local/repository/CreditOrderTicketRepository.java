package com.biz.crm.tpm.business.pay.local.repository;



import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.pay.local.entity.CreditOrderTicket;
import com.biz.crm.tpm.business.pay.local.mapper.CreditOrderTicketMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.CreditOrderTicketDto;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderTicketVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;


/**
 * 贷项订单票扣明细(CreditOrderTicket)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-01 16:40:49
 */
@Component
public class CreditOrderTicketRepository extends ServiceImpl<CreditOrderTicketMapper, CreditOrderTicket> {

  @Autowired
  private CreditOrderTicketMapper creditOrderTicketMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<CreditOrderTicketVo> findByConditions(Pageable pageable, CreditOrderTicketDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<CreditOrderTicketVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return creditOrderTicketMapper.findByConditions(page, dto);
  }

  /**
   * 按编码集合查询
   *
   * @param codes
   * @return
   */
  public List<CreditOrderTicketVo> findTicketByCreditCodes(List<String> codes) {
    if (CollectionUtils.isEmpty(codes)){
      return Lists.newArrayList();
    }
    List<CreditOrderTicket> list = this.lambdaQuery().in(CreditOrderTicket::getCreditCode, codes).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, CreditOrderTicket.class, CreditOrderTicketVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  public List<CreditOrderTicketVo> findByDtoList(List<CreditOrderTicketVo> dtoList) {
    if (CollectionUtils.isEmpty(dtoList)){
      return Lists.newArrayList();
    }
    return creditOrderTicketMapper.findByDtoList(dtoList);
  }

  public void updateCreditOrderTicketStatus(List<CreditOrderTicketVo> dtoList) {
    if (!CollectionUtils.isEmpty(dtoList)){
      creditOrderTicketMapper.updateCreditOrderTicketStatus(dtoList);
    }
  }

  public void updateBeAdjusted(Set<String> codes) {
    if (CollectionUtils.isEmpty(codes)){
      return;
    }
    for (String code : codes) {
      String[] split = code.split("_");
      lambdaUpdate().eq(CreditOrderTicket::getCreditCode, split[0])
              .eq(CreditOrderTicket::getLineNumber, split[1])
              .set(CreditOrderTicket::getBeAdjusted, BooleanEnum.TRUE.getCapital()).update();
    }
  }
}

