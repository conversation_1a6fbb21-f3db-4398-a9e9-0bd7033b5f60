package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditFiles;
import com.biz.crm.tpm.business.pay.local.repository.AuditFilesRepository;
import com.biz.crm.tpm.business.pay.local.service.AuditFilesService;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditFilesDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditFilesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 费用核销附件;(tpm_audit_files)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Service("auditFilesService")
public class AuditFilesServiceImpl implements AuditFilesService {
  @Autowired
  private AuditFilesRepository auditFilesRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<AuditFilesVo> findByConditions(Pageable pageable, AuditFilesDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new AuditFilesDto();
    }
    return this.auditFilesRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public AuditFilesVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    AuditFiles auditFiles = this.auditFilesRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (auditFiles == null) {
      return null;
    }
    AuditFilesVo auditFilesVo = this.nebulaToolkitService.copyObjectByWhiteList(auditFiles, AuditFilesVo.class, LinkedHashSet.class, ArrayList.class);
    return auditFilesVo;
  }

  /**
   * 通过编号查询单条数据
   *
   * @param auditCode 主键
   * @return 单条数据
   */
  @Override
  public List<AuditFilesVo> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return null;
    }
    List<AuditFiles> auditFiles = this.auditFilesRepository.findByAuditCode(auditCode);
    if (auditFiles == null) {
      return null;
    }
    Collection<AuditFilesVo> auditFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditFiles, AuditFiles.class, AuditFilesVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(auditFilesVos);
  }

  /**
   * 新增数据
   *
   * @param auditFilesDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AuditFilesVo create(AuditFilesDto auditFilesDto) {
    this.createValidate(auditFilesDto);
    AuditFiles auditFiles = this.nebulaToolkitService.copyObjectByWhiteList(auditFilesDto, AuditFiles.class, LinkedHashSet.class, ArrayList.class);
    auditFiles.setTenantCode(TenantUtils.getTenantCode());
    this.auditFilesRepository.saveOrUpdate(auditFiles);
    AuditFilesVo auditFilesVo = this.nebulaToolkitService.copyObjectByWhiteList(auditFiles, AuditFilesVo.class, LinkedHashSet.class, ArrayList.class);

    auditFilesVo.setId(auditFiles.getId());
    return auditFilesVo;
  }

  @Override
  public List<AuditFilesVo> createBatch(List<AuditFilesDto> auditFilesDtos) {
    if (CollectionUtils.isEmpty(auditFilesDtos)) {
      return Lists.newArrayList();
    }
    List<AuditFilesVo> auditFilesVos = Lists.newArrayList();
    for (AuditFilesDto auditFilesDto : auditFilesDtos) {
      AuditFilesVo auditFilesVo = this.create(auditFilesDto);
      auditFilesVos.add(auditFilesVo);
    }
    return auditFilesVos;
  }

  /**
   * 修改新据
   *
   * @param auditFilesDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public AuditFilesVo update(AuditFilesDto auditFilesDto) {
    this.updateValidate(auditFilesDto);
    AuditFiles auditFiles = this.auditFilesRepository.findByIdAndTenantCode(auditFilesDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(auditFiles, "修改数据不存在，请检查！");
    auditFiles.setAuditCode(auditFilesDto.getAuditCode());
    auditFiles.setCostTypeDetailCode(auditFilesDto.getCostTypeDetailCode());
    auditFiles.setCostTypeDetailName(auditFilesDto.getCostTypeDetailName());
    auditFiles.setRequestCode(auditFilesDto.getRequestCode());
    auditFiles.setTenantCode(TenantUtils.getTenantCode());
    this.auditFilesRepository.saveOrUpdate(auditFiles);
    AuditFilesVo auditFilesVo = this.nebulaToolkitService.copyObjectByWhiteList(auditFiles, AuditFilesVo.class, LinkedHashSet.class, ArrayList.class);
    return auditFilesVo;
  }

  @Override
  public List<AuditFilesVo> updateBatch(List<AuditFilesDto> auditFilesDtos) {
    if (CollectionUtils.isEmpty(auditFilesDtos)) {
      return Collections.emptyList();
    }
    String auditCode = auditFilesDtos.stream().findFirst().get().getAuditCode();
    List<AuditFilesVo> dbAuditFilesVos = this.findByAuditCode(auditCode);
    Set<String> dbIds = dbAuditFilesVos.stream().map(AuditFilesVo::getId).collect(Collectors.toSet());
    Set<String> currentIds = auditFilesDtos.stream().map(AuditFilesDto::getId).collect(Collectors.toSet());
    Set<String> deleteIds = Sets.difference(dbIds, currentIds);
    if (!CollectionUtils.isEmpty(deleteIds)) {
      this.delete(Lists.newArrayList(deleteIds));
    }
    List<AuditFilesDto> addDtos = auditFilesDtos.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
    List<AuditFilesDto> updateDtos = auditFilesDtos.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
    List<AuditFilesVo> auditFilesVos = this.createBatch(addDtos);
    if (!CollectionUtils.isEmpty(updateDtos)) {
      updateDtos.forEach(item -> {
        auditFilesVos.add(this.update(item));
      });
    }
    return auditFilesVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<AuditFiles> auditFiless = this.auditFilesRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(auditFiless)) {
      return;
    }
    Collection<AuditFilesVo> auditFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditFiless, AuditFiles.class, AuditFilesVo.class, LinkedHashSet.class, ArrayList.class);
    this.auditFilesRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  @Override
  @Transactional
  public void deleteByAuditCode(List<String> auditCode) {
    this.auditFilesRepository.removeByAuditCode(auditCode);
  }

  /**
   * 创建验证
   *
   * @param auditFilesDto
   */
  private void createValidate(AuditFilesDto auditFilesDto) {
    Validate.notNull(auditFilesDto, "新增时，对象信息不能为空！");
    auditFilesDto.setId(null);
    Validate.notBlank(auditFilesDto.getAuditCode(), "新增数据时，费用核销编号不能为空！");
//    Validate.notBlank(auditFilesDto.getCostTypeDetailCode(), "新增数据时，活动细类编码不能为空！");
//    Validate.notBlank(auditFilesDto.getCostTypeDetailName(), "新增数据时，活动细类名称不能为空！");
//    Validate.notBlank(auditFilesDto.getRequestCode(), "新增数据时，要求核销资料编号不能为空！");
  }

  /**
   * 修改验证
   *
   * @param auditFilesDto
   */
  private void updateValidate(AuditFilesDto auditFilesDto) {
    Validate.notNull(auditFilesDto, "修改时，对象信息不能为空！");
    Validate.notBlank(auditFilesDto.getId(), "修改数据时，主键不能为空！");
    Validate.notBlank(auditFilesDto.getAuditCode(), "修改数据时，费用核销编号不能为空！");
//    Validate.notBlank(auditFilesDto.getCostTypeDetailCode(), "修改数据时，活动细类编码不能为空！");
//    Validate.notBlank(auditFilesDto.getCostTypeDetailName(), "修改数据时，活动细类名称不能为空！");
//    Validate.notBlank(auditFilesDto.getRequestCode(), "修改数据时，要求核销资料编号不能为空！");
  }
}