package com.biz.crm.tpm.business.pay.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditActivitiesDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditActivitiesVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 费用核销关联活动;(tpm_audit_activities)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
public interface AuditActivitiesService {

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<AuditActivitiesVo> findByConditions(Pageable pageable, AuditActivitiesDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AuditActivitiesVo findById(String id);

  /**
   * 通过费用核销编号查询单条数据
   *
   * @param auditCode 核销编号
   * @return 多条数据
   */
  List<AuditActivitiesVo> findByAuditCode(String auditCode);

  /**
   * 新增数据
   *
   * @param auditActivitiesDto 实体对象
   * @return 新增结果
   */
  AuditActivitiesVo create(AuditActivitiesDto auditActivitiesDto);

  /**
   * 修改新据
   *
   * @param auditActivitiesDto 实体对象
   * @return 修改结果
   */
  AuditActivitiesVo update(AuditActivitiesDto auditActivitiesDto);

  /**
   * 批量保存
   *
   * @param auditActivitiesDtos
   */
  List<AuditActivitiesVo> createBatch(List<AuditActivitiesDto> auditActivitiesDtos);

  /**
   * 批量更新
   * @param auditActivitiesDtos
   * @return
   */
  List<AuditActivitiesVo> updateBatch(List<AuditActivitiesDto> auditActivitiesDtos);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(List<String> ids);

  /**
   * 根绝费用核销编号删除关联活动信息
   *
   * @param auditCode
   */
  void deleteByAuditCode(String auditCode);
}