package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.UUID;

@ApiModel(value = "FeeCashDetail", description = "费用兑付明细")
@TableName("tpm_fee_cash_detail")
@Getter
@Setter
@Entity(name = "tpm_fee_cash_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_fee_cash_detail", comment = "费用兑付明细")
@Table(name = "tpm_fee_cash_detail", indexes = {
        @Index(name = "fee_cash_uq1", columnList = "cash_detail_code", unique = true),
        @Index(name = "fee_cash_idx1", columnList = "cash_code"),
        @Index(name = "fee_cash_idx2", columnList = "years"),
        @Index(name = "fee_cash_idx3", columnList = "customer_code"),
        @Index(name = "fee_cash_idx4", columnList = "customer_name"),
        @Index(name = "fee_cash_idx5", columnList = "budget_subject_name"),
})
public class FeeCashDetail extends TenantFlagOpEntity {

    @ApiModelProperty("兑付名称")
    @Column(name = "cash_name", columnDefinition = "VARCHAR(255) COMMENT '兑付名称 '")
    private String cashName;

    @ApiModelProperty("兑付编号")
    @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
    private String cashCode;

    @ApiModelProperty("兑付明细编号")
    @Column(name = "cash_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付明细编号 '")
    private String cashDetailCode;

    @ApiModelProperty("核销申请编号")
    @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
    private String auditCode;

    @ApiModelProperty("核销明细编号")
    @Column(name = "audit_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销明细编号 '")
    private String auditDetailCode;

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称'")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("活动名称")
    @Column(name = "act_name", columnDefinition = "varchar(128) comment '活动名称'")
    private String actName;

    @ApiModelProperty("活动大类编码")
    @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    @Column(name = "detail_code", columnDefinition = "varchar(32) comment '活动细类编码'")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    @Column(name = "detail_name", columnDefinition = "varchar(64) comment '活动细类名称'")
    private String detailName;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

    @ApiModelProperty("供应商编码")
    @Column(name = "payee_code", columnDefinition = "VARCHAR(64) COMMENT '供应商编码 '")
    private String payeeCode;

    @ApiModelProperty("供应商名称")
    @Column(name = "payee_name", columnDefinition = "VARCHAR(255) COMMENT '供应商名称 '")
    private String payeeName;

    @ApiModelProperty("供应商Erp编码")
    @Column(name = "payee_erp_code", columnDefinition = "VARCHAR(64) COMMENT '收款方Erp编码 '")
    private String payeeErpCode;

    @ApiModelProperty("申请金额")
    @Column(name = "apply_amount", columnDefinition = "decimal(18,4) comment '申请金额'")
    private BigDecimal applyAmount;

    @ApiModelProperty("核销金额")
    @Column(name = "audit_amount", length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '核销金额 '")
    private BigDecimal auditAmount;

    @ApiModelProperty("已兑付金额")
    @Column(name = "cash_amount", columnDefinition = "decimal(20,6) comment '已兑付金额'")
    private BigDecimal cashAmount;

    @ApiModelProperty("可兑付金额")
    @Column(name = "available_cash_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '可兑付金额 '")
    private BigDecimal availableCashAmount;

    @ApiModelProperty("预付可冲销金额")
    @Column(name = "available_reversed_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '预付可冲销金额 '")
    private BigDecimal availableReversedAmount;

    @ApiModelProperty("本次冲销预付金额")
    @Column(name = "this_reversed_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '本次冲销预付金额 '")
    private BigDecimal thisReversedAmount;

    @ApiModelProperty("本次兑付金额")
    @Column(name = "this_cash_amount", columnDefinition = "decimal(20,6) comment '本次兑付金额'")
    private BigDecimal thisCashAmount;

    @ApiModelProperty("是否完全兑付")
    @Column(name = "be_cash", columnDefinition = "varchar(10) default 'N' comment '是否完全兑付'")
    private String beCash;

    @ApiModelProperty("品项编码")
    @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
    private String itemCode;

    @ApiModelProperty("品项名称")
    @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
    private String itemName;

    @ApiModelProperty("预算科目编码")
    @Column(name = "budget_subject_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算科目编码'")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    @Column(name = "budget_subject_name", columnDefinition = "varchar(255) COMMENT '预算科目名称'")
    private String budgetSubjectName;

    @ApiModelProperty("归属部门编码")
    @Column(name = "belong_department_code", columnDefinition = "varchar(32) comment '归属部门编码'")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    @Column(name = "belong_department_name", columnDefinition = "varchar(64) comment '归属部门名称'")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    @Column(name = "bear_department_code", columnDefinition = "varchar(32) comment '承担部门编码'")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    @Column(name = "bear_department_name", columnDefinition = "varchar(64) comment '承担部门名称'")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @Column(name = "cost_center_name", columnDefinition = "varchar(64) comment '成本中心名称'")
    private String costCenterName;

    @ApiModelProperty("活动描述")
    @Column(name = "act_desc", columnDefinition = "varchar(500) comment '活动描述'")
    private String actDesc;

    @ApiModelProperty("结案完结日期")
    @Column(name = "audit_date", columnDefinition = "varchar(32) comment '结案完结日期'")
    private String auditDate;

    @ApiModelProperty("紧急程度")
    @Column(name = "urgency", columnDefinition = "varchar(32) comment '紧急程度'")
    private String urgency;

    @ApiModelProperty("需求紧急原因")
    @Column(name = "urgency_reason", columnDefinition = "varchar(500) comment '需求紧急原因'")
    private String urgencyReason;

    @ApiModelProperty("合作类型")
    @Column(name = "cooperate_type", columnDefinition = "varchar(20) comment '合作类型'")
    private String cooperateType;

    @ApiModelProperty("本次兑付税额")
    @Column(name = "this_cash_tax_amount", columnDefinition = "decimal(18,4) comment '本次兑付税额'")
    private BigDecimal thisCashTaxAmount;

    @ApiModelProperty("本次兑付未税金额")
    @Column(name = "this_cash_no_tax_amount", columnDefinition = "decimal(18,4) comment '本次兑付未税金额'")
    private BigDecimal thisCashNoTaxAmount;

    @ApiModelProperty("实付金额")
    @Column(name = "actual_pay_amount", columnDefinition = "decimal(18,4) comment '实付金额'")
    private BigDecimal actualPayAmount;

    @ApiModelProperty("客户搜索项")
    @Column(name = "search_name", columnDefinition = "varchar(128) comment '客户搜索项'")
    private String searchName;

    @ApiModelProperty("票据类型")
    @Column(name = "bill_type", columnDefinition = "varchar(32) comment '票据类型'")
    private String billType;

    @ApiModelProperty("收据说明")
    @Column(name = "receipt_description", columnDefinition = "varchar(500) comment '收据说明'")
    private String receiptDescription;

}
