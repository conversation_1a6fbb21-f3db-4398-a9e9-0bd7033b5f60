package com.biz.crm.tpm.business.pay.local.calcomponent;

import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @Description 计提异步计算
 * <AUTHOR>
 * @Date 2024/12/17 18:18
 **/
@Component
public class WithholdingCalComponent {

    private static final Logger log = LoggerFactory.getLogger(WithholdingCalComponent.class);
    @Autowired(required = false)
    private WithHoldingService withHoldingService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Async("tpmRegionCollectThread")
    public void calWithholdingSync(WithHoldingDto dto) {
        WithholdingCalComponent calComponent = ApplicationContextHolder.getContext().getBean(WithholdingCalComponent.class);
        calComponent.execute(dto);
    }


    public void execute(WithHoldingDto dto) {
        loginUserService.refreshAuthentication(null);
        withHoldingService.syncHandleAuto(dto);
    }
}
