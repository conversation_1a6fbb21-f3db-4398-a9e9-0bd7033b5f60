package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.Audit;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用核销;(tpm_audit)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Mapper
public interface AuditMapper extends BaseMapper<Audit> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto  动态查询条件
   * @return 分页对象列表
   */
  Page<AuditVo> findByConditions(@Param("page") Page<AuditVo> page, @Param("dto") AuditDto dto);

  /**
   * 通过活动细类编号查询涉及到的核销信息（状态为核销中）
   * @param tenantCode
   * @param activitiesDetailCode
   * @return
   */
  List<AuditVo> findByActivitiesDetailCode(@Param("tenantCode") String tenantCode, @Param("activitiesDetailCode") String activitiesDetailCode);
}
