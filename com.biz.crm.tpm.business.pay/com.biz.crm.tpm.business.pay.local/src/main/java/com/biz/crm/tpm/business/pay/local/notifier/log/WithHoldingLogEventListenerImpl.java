package com.biz.crm.tpm.business.pay.local.notifier.log;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.pay.sdk.dto.log.WithHoldingLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.event.log.WithHoldingLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年07月12日 10:25:00
 */
@Component
public class WithHoldingLogEventListenerImpl  implements WithHoldingLogEventListener {
  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;
  /**
   * 创建事件
   *
   * @param eventDto
   */
  @Override
  public void onCreate(WithHoldingLogEventDto eventDto){
    WithHoldingVo newest = eventDto.getNewest();
    WithHoldingVo original = eventDto.getOriginal();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
  /**
   * 删除事件
   *
   * @param eventDto
   */
  @Override
  public void onDelete(WithHoldingLogEventDto eventDto){
    WithHoldingVo newest = eventDto.getNewest();
    WithHoldingVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
  /**
   * 更新日志
   *
   * @param eventDto
   */
  @Override
  public void onUpdate(WithHoldingLogEventDto eventDto){
    WithHoldingVo newest = eventDto.getNewest();
    WithHoldingVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  /**
   * 更新日志
   *
   * @param eventDto
   */
  @Override
  public void onUpdateBatch(WithHoldingLogEventDto eventDto) {
    List<WithHoldingVo> newList = Lists.newArrayList();
    if (Objects.nonNull(eventDto.getNewest())) {
      newList.add(eventDto.getNewest());
    }
    if (CollectionUtil.isNotEmpty(eventDto.getNewestList())) {
      newList.addAll(eventDto.getNewestList());
    }
    List<WithHoldingVo> oldList = eventDto.getOriginalList();
    Map<String, WithHoldingVo> oldMap = oldList.stream()
            .filter(k -> StringUtil.isNotEmpty(k.getId()))
            .collect(Collectors.toMap(WithHoldingVo::getId, v -> v, (n, o) -> n));
    String tenantCode = TenantUtils.getTenantCode();
    newList.forEach(newest -> {
      String onlyKey = newest.getId();
      CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
      crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
      crmBusinessLogDto.setOnlyKey(onlyKey);
      crmBusinessLogDto.setAppCode(tenantCode);
      crmBusinessLogDto.setTenantCode(tenantCode);
      WithHoldingVo original = oldMap.getOrDefault(newest.getId(), new WithHoldingVo());
      crmBusinessLogDto.setOldObject(original);
      crmBusinessLogDto.setNewObject(newest);
      crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    });
  }

  /**
   * 启用
   *
   * @param eventDto
   */
  @Override
  public void onEnable(WithHoldingLogEventDto eventDto){
    WithHoldingVo newest = eventDto.getNewest();
    WithHoldingVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    //只传单个字段
    JSONObject oldObject = new JSONObject();
    JSONObject newObject = new JSONObject();
    oldObject.put("enableStatus", original.getEnableStatus());
    newObject.put("enableStatus", newest.getEnableStatus());
    crmBusinessLogDto.setOldObject(oldObject);
    crmBusinessLogDto.setNewObject(newObject);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
  /**
   * 禁用
   *
   * @param eventDto
   */
  @Override
  public void onDisable(WithHoldingLogEventDto eventDto){
    WithHoldingVo newest = eventDto.getNewest();
    WithHoldingVo original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    //只传单个字段
    JSONObject oldObject = new JSONObject();
    JSONObject newObject = new JSONObject();
    oldObject.put("enableStatus", original.getEnableStatus());
    newObject.put("enableStatus", newest.getEnableStatus());
    crmBusinessLogDto.setOldObject(oldObject);
    crmBusinessLogDto.setNewObject(newObject);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
}
