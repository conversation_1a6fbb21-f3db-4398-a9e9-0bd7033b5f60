<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayDetailRecordItemMapper">

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordItemVo">
        select
            t.*
        from tpm_activity_prepay_detail_record_item t
        where t.del_flag = '${@<EMAIL>()}'
        and t.tenant_code = #{dto.tenantCode}
        <if test="dto.prepayDetailCode != null and dto.prepayDetailCode != ''">
            and t.prepay_detail_code = #{dto.prepayDetailCode}
        </if>
        <if test="dto.type != null and dto.type != ''">
            and t.type = #{dto.type}
        </if>
        <if test="dto.businessCode != null and dto.businessCode != ''">
            <bind name="businessCode" value="'%'+dto.businessCode+'%'"/>
            and t.business_code like #{businessCode}
        </if>
        <if test="dto.businessName != null and dto.businessName != ''">
            <bind name="businessName" value="'%'+dto.businessName+'%'"/>
            and t.business_name like #{businessName}
        </if>
        order by t.create_time desc,t.id
    </select>

</mapper>

