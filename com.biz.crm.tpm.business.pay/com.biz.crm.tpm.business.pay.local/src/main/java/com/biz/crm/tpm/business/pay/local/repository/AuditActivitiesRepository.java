package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.AuditActivities;
import com.biz.crm.tpm.business.pay.local.mapper.AuditActivitiesMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditActivitiesDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditActivitiesVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 费用核销关联活动;(tpm_audit_activities)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Component
public class AuditActivitiesRepository extends ServiceImpl<AuditActivitiesMapper, AuditActivities> {
  @Autowired
  private AuditActivitiesMapper auditActivitiesMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<AuditActivitiesVo> findByConditions(Pageable pageable, AuditActivitiesDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<AuditActivitiesVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return auditActivitiesMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<AuditActivities>
   */
  public List<AuditActivities> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(AuditActivities::getId, ids)
            .eq(AuditActivities::getTenantCode,tenantCode)
            .list();
  }

  /**
   * 根据费用审核编号与租户编号获取对象
   *
   * @param auditCode
   * @return
   */
  public List<AuditActivities> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(AuditActivities::getTenantCode, tenantCode)
            .eq(AuditActivities::getAuditCode, auditCode).list();
  }

  /**
   * 根据费用审核编号删除与其关联的数据
   *
   * @param auditCode
   */
  public void deleteByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    this.lambdaUpdate()
            .eq(AuditActivities::getTenantCode, tenantCode)
            .eq(AuditActivities::getAuditCode, auditCode).remove();
  }

  public AuditActivities findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(AuditActivities::getTenantCode,tenantCode)
        .in(AuditActivities::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(AuditActivities::getTenantCode,tenantCode)
        .in(AuditActivities::getId,ids)
        .remove();
  }
}