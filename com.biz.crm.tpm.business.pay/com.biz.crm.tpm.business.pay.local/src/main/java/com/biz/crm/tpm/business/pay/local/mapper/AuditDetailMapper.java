package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditDetail;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 费用核销明细;(tpm_audit_detail)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Mapper
public interface AuditDetailMapper extends BaseMapper<AuditDetail> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto  动态查询条件
   * @return 分页对象列表
   */
  Page<AuditDetailVo> findByConditions(@Param("page") Page<AuditDetailVo> page, @Param("dto") AuditDetailDto dto);

  List<AuditDetail> findByActivitiesDetailCodes(@Param("activitiesDetailCode") String activitiesDetailCode, @Param("tenantCode") String tenantCode);

  List<AuditDetail> findByExcludeActivitiesCodeAndActivitiesDetailCodes(@Param("activitiesCode") String activitiesCode, @Param("activitiesDetailCode") String activitiesDetailCode, @Param("tenantCode") String tenantCode);

  Set<String> findActivitiesDetailByAuditing(@Param("costTypeDetailCodes") Set<String> costTypeDetailCodes, @Param("tenantCode") String tenantCode);

  Set<String> findActivitiesDetailByFullAudit(@Param("tenantCode") String tenantCode);

  /**
   * 查询所有已经核销的活动名明细编号
   *
   * @param tenantCode
   * @return
   */
  Set<String> findActivitiesDetailByAudited(@Param("tenantCode") String tenantCode);

}