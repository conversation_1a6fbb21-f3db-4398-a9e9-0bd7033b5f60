package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.PrepayBill;
import com.biz.crm.tpm.business.pay.local.mapper.PrepayBillMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 活动预付账单;(tpm_prepay_bill)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Component
public class PrepayBillRepository extends ServiceImpl<PrepayBillMapper, PrepayBill> {
  @Autowired
  private PrepayBillMapper prepayBillMapper;

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<PrepayBill>
   */
  public List<PrepayBill> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(PrepayBill::getId, ids)
            .eq(PrepayBill::getTenantCode, tenantCode)
            .list();
  }


  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @return 返单多条数据
   */
  public PrepayBill findByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(PrepayBill::getActivitiesDetailCode, activitiesDetailCode)
            .eq(PrepayBill::getTenantCode, tenantCode)
            .one();
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<PrepayBill> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes) {
    if (CollectionUtils.isEmpty(activitiesDetailCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(PrepayBill::getActivitiesDetailCode, activitiesDetailCodes)
            .eq(PrepayBill::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 删除数据
   */
  public boolean deleteByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(PrepayBill::getActivitiesDetailCode, activitiesDetailCode)
            .eq(PrepayBill::getTenantCode, tenantCode)
            .remove();
  }

  /**
   * 增加已预付金额
   *
   * @param activitiesDetailCode
   * @param amount
   */
  public void addPreparedAmount(String activitiesDetailCode, BigDecimal amount) {
    String tenantCode = TenantUtils.getTenantCode();
    this.prepayBillMapper.addPrepayAmount(activitiesDetailCode, amount, tenantCode);
  }

  /**
   * 减少已预付金额
   *
   * @param activitiesDetailCode
   * @param amount
   */
  public void reducePreparedAmount(String activitiesDetailCode, BigDecimal amount) {
    String tenantCode = TenantUtils.getTenantCode();
    this.prepayBillMapper.reducePrepayAmount(activitiesDetailCode, amount, tenantCode);
  }

  public PrepayBill findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(PrepayBill::getTenantCode,tenantCode)
        .in(PrepayBill::getId,id)
        .one();
  }
}