package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：费用兑付附件;
 * <AUTHOR> yaoyongming
 * @date : 2024-6-25
 */
@ApiModel(value = "FeeCashFiles",description = "费用兑付附件")
@TableName("tpm_fee_cash_files")
@Getter
@Setter
@Entity(name = "tpm_fee_cash_files")
@org.hibernate.annotations.Table(appliesTo = "tpm_fee_cash_files", comment = "费用兑付附件")
@Table(name = "tpm_fee_cash_files")
public class FeeCashFiles extends FileEntity {

  /** 兑付编号 */
  @ApiModelProperty("兑付编号")
  @Column(name = "cash_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
  private String cashCode;
}