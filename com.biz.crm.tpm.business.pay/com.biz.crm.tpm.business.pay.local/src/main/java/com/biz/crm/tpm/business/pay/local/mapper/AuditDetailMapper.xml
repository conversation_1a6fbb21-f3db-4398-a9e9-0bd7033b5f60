<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AuditDetailMapper">
  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.AuditDetail" id="AuditDetailMap">
  </resultMap>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailVo">
    select t.*,
    tab.audited_amount
    from tpm_audit_detail t
    left join tpm_audit_bill tab
    on t.activities_detail_code = tab.activities_detail_code and t.tenant_code = tab.tenant_code
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.auditDetailCode != null and dto.auditDetailCode != ''">
        <bind name="auditDetailCode" value="'%' + dto.auditDetailCode + '%'"/>
        and t.audit_detail_code like #{auditDetailCode}
      </if>
      and t.del_flag = '${@<EMAIL>()}'
    </where>
    order by t.create_time desc, t.id
  </select>

  <select id="findByActivitiesDetailCodes" resultMap="AuditDetailMap">
    select tad.*
    from tpm_audit_detail tad
    left join tpm_audit ta on ta.audit_code = tad.audit_code and ta.tenant_code = tad.tenant_code
    <where>
      tad.tenant_code = #{tenantCode}
      and tad.activities_detail_code = #{activitiesDetailCode}
      and ta.del_flag = '${@<EMAIL>()}'
      and tad.del_flag = '${@<EMAIL>()}'
    </where>
  </select>

  <select id="findByExcludeActivitiesCodeAndActivitiesDetailCodes" resultMap="AuditDetailMap">
    select tad.*
    from tpm_audit_detail tad
    left join tpm_audit ta on ta.audit_code = tad.audit_code and ta.tenant_code = tad.tenant_code
    <where>
      tad.tenant_code = #{tenantCode}
      and tad.audit_code != #{activitiesCode}
      and tad.activities_detail_code = #{activitiesDetailCode}
      and ta.del_flag = '${@<EMAIL>()}'
      and tad.del_flag = '${@<EMAIL>()}'
    </where>
  </select>

  <select id="findActivitiesDetailByAuditing" resultType="java.lang.String">
    select distinct tad.activities_detail_code
    from tpm_audit_detail tad
    left join tpm_audit ta on ta.audit_code = tad.audit_code and ta.tenant_code = tad.tenant_code
    <where>
      tad.tenant_code = #{tenantCode}
      <if test="costTypeDetailCodes != null and costTypeDetailCodes.size() > 0">
        and tad.cost_type_detail_code in
        <foreach item="item" collection="costTypeDetailCodes" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
      </if>
      and tad.del_flag = '${@<EMAIL>()}'
    </where>
  </select>

  <select id="findActivitiesDetailByFullAudit" resultType="java.lang.String">
    select distinct tad.activities_detail_code
    from tpm_audit_detail tad
    left join tpm_audit_bill tab
    on tab.activities_detail_code = tad.activities_detail_code and tab.tenant_code = tad.tenant_code
    <where>
      tad.tenant_code = #{tenantCode}
      and tad.del_flag = '${@<EMAIL>()}'
      and tab.is_full_audit = 'Y'
    </where>
  </select>

  <select id="findActivitiesDetailByAudited" resultType="java.lang.String">
    select distinct tad.activities_detail_code
    from tpm_audit_detail tad
    <where>
      tad.tenant_code = #{tenantCode}
      and tad.del_flag = '${@<EMAIL>()}'
    </where>
  </select>
</mapper>