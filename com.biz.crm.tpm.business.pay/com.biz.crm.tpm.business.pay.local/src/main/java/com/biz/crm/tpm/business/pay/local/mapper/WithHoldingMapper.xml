<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.WithHoldingMapper">

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo">
        select t.* from tpm_with_holding t
        <where>
            <if test="dto.activitiesCode != null and dto.activitiesCode != '' ">
                <bind name="activitiesCode" value=" '%' + dto.activitiesCode + '%' "/>
                and t.activities_code like #{activitiesCode}
            </if>
            <if test="dto.activitiesName != null and dto.activitiesName != '' ">
                <bind name="activitiesName" value=" '%' + dto.activitiesName + '%' "/>
                and t.activities_name like #{activitiesName}
            </if>
            <if test="dto.withHoldingCode != null and dto.withHoldingCode != '' ">
                <bind name="withHoldingCode" value=" '%' + dto.withHoldingCode + '%' "/>
                and t.with_holding_code like #{withHoldingCode}
            </if>
            <if test="dto.withHoldingType != null and dto.withHoldingType != '' ">
                and t.with_holding_type = #{dto.withHoldingType}
            </if>
            <if test="dto.yearMonthLy != null and dto.yearMonthLy != '' ">
                and t.year_month_ly = #{dto.yearMonthLy}
            </if>
            <if test="dto.years != null and dto.years != '' ">
                and t.years = #{dto.years}
            </if>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.manualWriteOff != null and dto.manualWriteOff != ''">
                and t.with_holding_code not in (select t1.with_holding_code from tpm_with_holding_write_off t1)
                and t.voucher_code is not null and t.voucher_code != ''
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
        order by t.create_time desc,t.id desc
    </select>

    <select id="findByYearMonthBelongDepartmentCode" resultType="com.biz.crm.tpm.business.pay.local.entity.WithHolding">
        select
        t.*
        from tpm_with_holding t
        where t.del_flag = '${@<EMAIL>()}'
        and t.tenant_code = #{tenantCode}
        and t.year_month_ly = #{yearMonthLy}
        and t.belong_department_code in
        <foreach collection="orgCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and (
        t.collect_code is null
        or
        t.collect_code not in
        (
        select
        t1.collect_code
        from tpm_with_holding_collect t1
        where t1.del_flag = '${@<EMAIL>()}'
            and t1.status in ('2','3')
        )
        )
    </select>

    <select id="hecVoucherCallback">
        <foreach item="dto" collection="dtoList" index="index" open="" separator=";" close="">
            update tpm_with_holding
            set voucher_code = #{dto.orderCode}
            where push_hec_code = #{dto.businessCode}
        </foreach>
    </select>
    <select id="findSendDataByCollectIds" resultType="com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo">
        select twh.*
        ,twhc.remark as collectRemark
        ,twhc.status as collectStatus
        ,twhc.position_code as collectPositionCode
        from tpm_with_holding twh
        left join tpm_with_holding_collect twhc
        on twh.collect_code = twhc.collect_code and twh.tenant_code = twhc.tenant_code
        where twhc.del_flag = '${@<EMAIL>()}'
        and twhc.id in
        <foreach item="item" collection="idList" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="findSendDataByIds" resultType="com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo">
        select twh.*
        ,twhc.remark as collectRemark
        ,twhc.status as collectStatus
        ,twhc.position_code as collectPositionCode
        from tpm_with_holding twh
        left join tpm_with_holding_collect twhc
        on twh.collect_code = twhc.collect_code and twh.tenant_code = twhc.tenant_code
        where twhc.del_flag = '${@<EMAIL>()}'
        and twh.push_hec_code in (
        select t.push_hec_code from tpm_with_holding t
        where t.id in
        <foreach item="item" collection="idList" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>)
    </select>

    <select id="findSchemeCreateInfoList" resultType="com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectSchemeVo">
        select
        distinct
        t.scheme_code,
        t.create_account,
        t.create_name
        from tpm_marketing_plan t
        where t.create_account is not null
        and t.scheme_code in
        <foreach collection="schemeCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="updateCreateInfo">
        <foreach collection="entities" item="item" separator=";">
            update tpm_with_holding
            set create_account = #{item.createAccount},
            create_name = #{item.createName},
            modify_account = #{item.createAccount},
            modify_name = #{item.createName}
            where id = #{item.id}
        </foreach>
    </select>


    <select id="findBySchemeDetailCodesPass" resultType="com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo">
        select t.*
        from tpm_with_holding t
        inner join tpm_with_holding_collect ta on t.collect_code=ta.collect_code
        <where>
            ta.status = '3'
            AND t.activities_detail_code in
            <foreach collection="codes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findTotalByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo">
        select ifnull(sum(ifnull(t.actual_report_amount, 0)), 0) actualReportAmount from tpm_with_holding t
        <where>
            <if test="dto.withHoldingCode != null and dto.withHoldingCode != '' ">
                <bind name="withHoldingCode" value=" '%' + dto.withHoldingCode + '%' "/>
                and t.with_holding_code like #{withHoldingCode}
            </if>
            <if test="dto.status != null and dto.status != '' ">
                and t.status = #{dto.status}
            </if>
            <if test="dto.pushHecCode != null and dto.pushHecCode != '' ">
                and t.push_hec_code = #{dto.pushHecCode}
            </if>
            <if test="dto.externalCode != null and dto.externalCode != '' ">
                and t.external_code = #{dto.externalCode}
            </if>
            <if test="dto.pushStatus != null and dto.pushStatus != '' ">
                and t.push_status = #{dto.pushStatus}
            </if>
            <if test="dto.yearMonthLy != null and dto.yearMonthLy != '' ">
                and t.year_month_ly = #{dto.yearMonthLy}
            </if>
            <if test="dto.years != null and dto.years != '' ">
                and t.years = #{dto.years}
            </if>
            <if test="dto.yearsStart != null and dto.yearsStart != '' ">
                and t.years &gt;= #{dto.yearsStart}
            </if>
            <if test="dto.yearsEnd != null and dto.yearsEnd != '' ">
                and t.years &lt;= #{dto.yearsEnd}
            </if>
            <if test="dto.companyCode != null and dto.companyCode != '' ">
                and t.company_code = #{dto.companyCode}
            </if>
            <if test="dto.businessCode != null and dto.businessCode != '' ">
                and t.business_code = #{dto.businessCode}
            </if>
            <if test="dto.activitiesCode != null and dto.activitiesCode != '' ">
                <bind name="activitiesCode" value=" '%' + dto.activitiesCode + '%' "/>
                and t.activities_code like #{activitiesCode}
            </if>
            <if test="dto.activitiesName != null and dto.activitiesName != '' ">
                <bind name="activitiesName" value=" '%' + dto.activitiesName + '%' "/>
                and t.activities_name like #{activitiesName}
            </if>
            <if test="dto.costTypeDetailCode != null and dto.costTypeDetailCode != '' ">
                and t.cost_type_detail_code = #{dto.costTypeDetailCode}
            </if>
            <if test="dto.budgetSubjectsName != null and dto.budgetSubjectsName != '' ">
                <bind name="budgetSubjectsName" value=" '%' + dto.budgetSubjectsName + '%' "/>
                and t.budget_subjects_name like #{budgetSubjectsName}
            </if>
            <if test="dto.costCenterName != null and dto.costCenterName != '' ">
                <bind name="costCenterName" value=" '%' + dto.costCenterName + '%' "/>
                and t.cost_center_name like #{costCenterName}
            </if>
<!--            <if test="dto.erpCode != null and dto.erpCode != '' ">-->
<!--                and t.erp_code = #{dto.pushHecCode}-->
<!--            </if>-->
            <if test="dto.itemCode != null and dto.itemCode != '' ">
                and t.item_code = #{dto.itemCode}
            </if>
            <if test="dto.itemName != null and dto.itemName != '' ">
                <bind name="itemName" value=" '%' + dto.itemName + '%' "/>
                and t.item_name like #{itemName}
            </if>
            <if test="dto.withHoldingType != null and dto.withHoldingType != '' ">
                and t.with_holding_type = #{dto.withHoldingType}
            </if>
            <if test="dto.withHoldingSource != null and dto.withHoldingSource != '' ">
                and t.with_holding_source = #{dto.withHoldingSource}
            </if>
            <if test="dto.payBy != null and dto.payBy != '' ">
                and t.pay_by = #{dto.payBy}
            </if>
            <if test="dto.voucherCode != null and dto.voucherCode != '' ">
                and t.voucher_code = #{dto.voucherCode}
            </if>
            <if test="dto.collectCode != null and dto.collectCode != '' ">
                and t.collect_code = #{dto.collectCode}
            </if>
            <if test="dto.beThisFee != null and dto.beThisFee != '' ">
                and t.be_this_fee = #{dto.beThisFee}
            </if>
            <if test="dto.beAdjust != null and dto.beAdjust != '' ">
                and t.be_adjust = #{dto.beAdjust}
            </if>
            <if test="dto.bearDepartmentCode != null and dto.bearDepartmentCode != '' ">
                and t.bear_department_code = #{dto.bearDepartmentCode}
            </if>
            <if test="dto.bearDepartmentName != null and dto.bearDepartmentName != '' ">
                <bind name="bearDepartmentName" value=" '%' + dto.bearDepartmentName + '%' "/>
                and t.bear_department_name like #{bearDepartmentName}
            </if>
            <if test="dto.belongDepartmentCode != null and dto.belongDepartmentCode != '' ">
                and t.belong_department_code = #{dto.belongDepartmentCode}
            </if>
            <if test="dto.belongDepartmentName != null and dto.belongDepartmentName != '' ">
                <bind name="belongDepartmentName" value=" '%' + dto.belongDepartmentName + '%' "/>
                and t.belong_department_name like #{belongDepartmentName}
            </if>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.createName != null and dto.createName != ''">
                and t.create_name = #{dto.createName}
            </if>
            <if test="dto.modifyName != null and dto.modifyName != ''">
                and t.modify_name = #{dto.modifyName}
            </if>
            <if test="dto.customerCode!=null and dto.customerCode!='' ">
                and t.customer_code=#{dto.customerCode}
            </if>
            <if test="dto.customerName!=null and dto.customerName!='' ">
                and t.customer_name like concat('%',#{dto.customerName},'%')
            </if>
            <if test="dto.erpCode!=null and dto.erpCode!='' ">
                and t.erp_code=#{dto.erpCode}
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
        order by t.create_time desc,t.id desc
    </select>
    <select id="findActualReportAmountByYears"
            resultType="com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo">
        SELECT
            belong_department_code AS orgCode,
            belong_department_name AS orgName,
            SUM(actual_report_amount) as actualReportAmount,
            years
        FROM
            `tpm_with_holding`
        WHERE
            del_flag = '${@<EMAIL>()}'
          AND years in <foreach collection="yearMonths" open="(" close=")" separator="," item="item">
                        #{item}
                       </foreach>
        GROUP BY  belong_department_code ,
                  belong_department_name,years


    </select>

    <select id="findAmountByOrgCodesAndYears" resultType="java.math.BigDecimal">
        SELECT
        sum(IFNULL(with_holding_amount, 0) - IFNULL(actual_report_amount, 0))
        FROM
        tpm_with_holding
        <where>
            status = '3'
            <if test="orgCodes != null and orgCodes.size()>0">
                and belong_department_code in
                <foreach collection="orgCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="years != null and years != ''">
                and years = #{years}
            </if>
        </where>
    </select>

    <select id="findWithholdingListByYearsAndOrgCodes" resultType="com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo">
        SELECT
        sum(IFNULL(with_holding_amount, 0) - IFNULL(actual_report_amount, 0)) actual_amount,
        belong_department_code,years
        FROM
        tpm_with_holding
        <where>
            status = '3'
            <if test="dto.orgCodes != null and dto.orgCodes.size()>0">
                and belong_department_code in
                <foreach collection="dto.orgCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.years != null and dto.years != ''">
                and years = #{dto.years}
            </if>
            <if test="dto.yearsList != null and dto.yearsList.size()>0">
                and years in
                <foreach collection="dto.yearsList" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by belong_department_code,years
    </select>

    <!-- 计提余额数据查询结果映射 -->
    <resultMap id="WithHoldingBalanceVoResultMap" type="com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingBalanceVo">
        <result column="with_holding_code" property="withHoldingCode"/>
        <result column="year_month_ly" property="yearMonthLy"/>
        <result column="years" property="years"/>
        <result column="business_code" property="businessCode" />
        <result column="activities_detail_code" property="activitiesDetailCode"/>
        <result column="activities_code" property="activitiesCode"/>
        <result column="activities_name" property="activitiesName"/>
        <result column="order_code" property="orderCode"/>
        <result column="order_name" property="orderName"/>
        <result column="cost_type_category_code" property="costTypeCategoryCode"/>
        <result column="cost_type_category_name" property="costTypeCategoryName"/>
        <result column="cost_type_detail_code" property="costTypeDetailCode"/>
        <result column="cost_type_detail_name" property="costTypeDetailName"/>
        <result column="budget_subjects_code" property="budgetSubjectsCode"/>
        <result column="budget_subjects_name" property="budgetSubjectsName"/>
        <result column="belong_department_code" property="belongDepartmentCode"/>
        <result column="belong_department_name" property="belongDepartmentName"/>
        <result column="bear_department_code" property="bearDepartmentCode"/>
        <result column="bear_department_name" property="bearDepartmentName"/>
        <result column="cost_center_code" property="costCenterCode"/>
        <result column="cost_center_name" property="costCenterName"/>
        <result column="customer_code" property="customerCode"/>
        <result column="customer_name" property="customerName"/>
        <result column="item_name" property="itemName"/>
        <result column="with_holding_type" property="withHoldingType"/>
        <result column="pay_by" property="payBy"/>
        <result column="apply_amount_total" property="applyAmountTotal"/>
        <result column="with_holding_amount_total" property="withHoldingAmountTotal"/>
        <result column="write_off_amount_total" property="writeOffAmountTotal"/>
        <result column="balance" property="balance"/>
        <result column="actual_report_amount" property="actualReportAmount"/>
        <result column="back_write_off_amount_total" property="backWriteOffAmountTotal"/>
        <result column="back_balance" property="backBalance"/>
    </resultMap>

    <!-- 计提余额数据查询 - 实现WithHoldingBalanceDataViewRegister的SQL逻辑 -->
    <select id="findWithHoldingBalanceByConditions" resultMap="WithHoldingBalanceVoResultMap">
        SELECT
            wh.with_holding_code,
            wh.year_month_ly,
            wh.years,
            min(wh.business_code) as business_code,
            wh.activities_detail_code,
            wh.activities_code,
            wh.activities_name,
            wh.order_code,
            wh.order_name,
            wh.cost_type_category_code,
            wh.cost_type_category_name,
            wh.cost_type_detail_code,
            wh.cost_type_detail_name,
            wh.budget_subjects_code,
            wh.budget_subjects_name,
            wh.belong_department_code,
            wh.belong_department_name,
            wh.bear_department_code,
            wh.bear_department_name,
            wh.cost_center_code,
            wh.cost_center_name,
            wh.customer_code,
            wh.customer_name,
            wh.item_name,
            wh.with_holding_type,
            wh.pay_by,
            MAX(IFNULL(wh.apply_amount, 0)) apply_amount_total,
            MAX(IFNULL(wh.actual_amount, 0)) with_holding_amount_total,
            SUM(IFNULL(wo.write_off_amount, 0)) write_off_amount_total,
            MAX(IFNULL(wh.actual_amount, 0)) - SUM(IFNULL(wo.write_off_amount, 0)) balance,
            MAX(IFNULL(wh.actual_report_amount, 0)) actual_report_amount
        FROM
            tpm_with_holding wh
            LEFT JOIN tpm_with_holding_write_off wo ON wh.with_holding_code = wo.with_holding_code and wo.del_flag ='009'
        <where>
            wh.tenant_code = #{dto.tenantCode}
            and wh.del_flag = '009'
            and wh.status = '3'
            <if test="dto.withHoldingCode != null and dto.withHoldingCode != ''">
                and wh.with_holding_code = #{dto.withHoldingCode}
            </if>
            <if test="dto.yearMonthLy != null and dto.yearMonthLy != ''">
                and wh.year_month_ly = #{dto.yearMonthLy}
            </if>
            <if test="dto.years != null and dto.years != ''">
                and wh.years = #{dto.years}
            </if>
            <if test="dto.activitiesDetailCode != null and dto.activitiesDetailCode != ''">
                and wh.activities_detail_code = #{dto.activitiesDetailCode}
            </if>
            <if test="dto.activitiesCode != null and dto.activitiesCode != ''">
                and wh.activities_code = #{dto.activitiesCode}
            </if>
            <if test="dto.activitiesName != null and dto.activitiesName != ''">
                <bind name="activitiesName" value="'%' + dto.activitiesName + '%'"/>
                and wh.activities_name like #{activitiesName}
            </if>
            <if test="dto.orderCode != null and dto.orderCode != ''">
                and wh.order_code = #{dto.orderCode}
            </if>
            <if test="dto.orderName != null and dto.orderName != ''">
                <bind name="orderName" value="'%' + dto.orderName + '%'"/>
                and wh.order_name like #{orderName}
            </if>
            <if test="dto.costTypeCategoryCode != null and dto.costTypeCategoryCode != ''">
                and wh.cost_type_category_code = #{dto.costTypeCategoryCode}
            </if>
            <if test="dto.costTypeDetailCode != null and dto.costTypeDetailCode != ''">
                and wh.cost_type_detail_code = #{dto.costTypeDetailCode}
            </if>
            <if test="dto.budgetSubjectsCode != null and dto.budgetSubjectsCode != ''">
                and wh.budget_subjects_code = #{dto.budgetSubjectsCode}
            </if>
            <if test="dto.belongDepartmentCode != null and dto.belongDepartmentCode != ''">
                and wh.belong_department_code = #{dto.belongDepartmentCode}
            </if>
            <if test="dto.bearDepartmentCode != null and dto.bearDepartmentCode != ''">
                and wh.bear_department_code = #{dto.bearDepartmentCode}
            </if>
            <if test="dto.costCenterCode != null and dto.costCenterCode != ''">
                and wh.cost_center_code = #{dto.costCenterCode}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                and wh.customer_code = #{dto.customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                <bind name="customerName" value="'%' + dto.customerName + '%'"/>
                and wh.customer_name like #{customerName}
            </if>
            <if test="dto.itemName != null and dto.itemName != ''">
                <bind name="itemName" value="'%' + dto.itemName + '%'"/>
                and wh.item_name like #{itemName}
            </if>
            <if test="dto.withHoldingType != null and dto.withHoldingType != ''">
                and wh.with_holding_type = #{dto.withHoldingType}
            </if>
            <if test="dto.payBy != null and dto.payBy != ''">
                and wh.pay_by = #{dto.payBy}
            </if>
        </where>
        GROUP BY
            wh.with_holding_code,
            wh.year_month_ly,
            wh.years,
            wh.activities_detail_code,
            wh.activities_code,
            wh.activities_name,
            wh.order_code,
            wh.order_name,
            wh.cost_type_category_code,
            wh.cost_type_category_name,
            wh.cost_type_detail_code,
            wh.cost_type_detail_name,
            wh.budget_subjects_code,
            wh.budget_subjects_name,
            wh.belong_department_code,
            wh.belong_department_name,
            wh.bear_department_code,
            wh.bear_department_name,
            wh.cost_center_code,
            wh.cost_center_name,
            wh.customer_code,
            wh.customer_name,
            wh.item_name,
            wh.with_holding_type,
            wh.pay_by
        <if test="dto.sort != null and dto.sort != ''">
            ORDER BY
            <choose>
                <when test="dto.sort.contains('year_month_ly,desc')">
                    wh.year_month_ly DESC
                </when>
                <when test="dto.sort.contains('year_month_ly,asc') or dto.sort.contains('year_month_ly,') or dto.sort == 'year_month_ly'">
                    wh.year_month_ly ASC
                </when>
                <when test="dto.sort.contains('years,desc')">
                    wh.years DESC
                </when>
                <when test="dto.sort.contains('years,asc') or dto.sort.contains('years,') or dto.sort == 'years'">
                    wh.years ASC
                </when>
                <when test="dto.sort.contains('apply_amount_total,desc')">
                    MAX(IFNULL(wh.apply_amount, 0)) DESC
                </when>
                <when test="dto.sort.contains('apply_amount_total,asc') or dto.sort.contains('apply_amount_total,') or dto.sort == 'apply_amount_total'">
                    MAX(IFNULL(wh.apply_amount, 0)) ASC
                </when>
                <when test="dto.sort.contains('with_holding_amount_total,desc')">
                    MAX(IFNULL(wh.actual_amount, 0)) DESC
                </when>
                <when test="dto.sort.contains('with_holding_amount_total,asc') or dto.sort.contains('with_holding_amount_total,') or dto.sort == 'with_holding_amount_total'">
                    MAX(IFNULL(wh.actual_amount, 0)) ASC
                </when>
                <when test="dto.sort.contains('write_off_amount_total,desc')">
                    SUM(IFNULL(wo.write_off_amount, 0)) DESC
                </when>
                <when test="dto.sort.contains('write_off_amount_total,asc') or dto.sort.contains('write_off_amount_total,') or dto.sort == 'write_off_amount_total'">
                    SUM(IFNULL(wo.write_off_amount, 0)) ASC
                </when>
                <when test="dto.sort.contains('balance,desc')">
                    (MAX(IFNULL(wh.actual_amount, 0)) - SUM(IFNULL(wo.write_off_amount, 0))) DESC
                </when>
                <when test="dto.sort.contains('balance,asc') or dto.sort.contains('balance,') or dto.sort == 'balance'">
                    (MAX(IFNULL(wh.actual_amount, 0)) - SUM(IFNULL(wo.write_off_amount, 0))) ASC
                </when>
                <otherwise>
                    wh.year_month_ly DESC
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>

