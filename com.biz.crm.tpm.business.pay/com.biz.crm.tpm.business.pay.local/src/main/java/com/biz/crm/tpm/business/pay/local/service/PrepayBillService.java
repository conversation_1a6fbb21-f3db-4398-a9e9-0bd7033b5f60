package com.biz.crm.tpm.business.pay.local.service;

import com.biz.crm.tpm.business.pay.sdk.dto.PrepayBillDto;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayBillVo;

import java.math.BigDecimal;

/**
 * 活动预付账单;(tpm_prepay_bill)表服务接口
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
public interface PrepayBillService{

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  PrepayBillVo findById(String id);

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCode
   * @return 单条数据
   */
  PrepayBillVo findByActivitiesDetailCode(String activitiesDetailCode);
  /**
   * 初始化数据
   * @param prepayBillDto
   * @return
   */
  PrepayBillVo init(PrepayBillDto prepayBillDto);
  /**
   * 新增数据
   *
   * @param prepayBillDto 实体对象
   * @return 新增结果
   */
  PrepayBillVo create(PrepayBillDto prepayBillDto);

  /**
   * 根据预付编号进行活动明细金额预付
   *
   * @param prepayCode
   */
  void prepayAmountByPrepayCode(String prepayCode);

  /**
   * 根据预付编号进行活动明细金额预付退回
   *
   * @param prepayCode
   */
  void backPrepayAmountByPrepayCode(String prepayCode);

  /**
   * 新增已经预付金额
   *
   * @param activitiesDetailCode 活动明细编码
   * @param amount               新增金额
   */
  void prepayAmountByActivityDetailCode(String activitiesDetailCode, String auditDetailCode, BigDecimal amount);

  /**
   * 退回已经预付金额
   *
   * @param activitiesDetailCode 活动明细编码
   * @param amount               新增金额
   */
  void backPrepayAmountByActivityDetailCode(String activitiesDetailCode, String auditDetailCode, BigDecimal amount);

}