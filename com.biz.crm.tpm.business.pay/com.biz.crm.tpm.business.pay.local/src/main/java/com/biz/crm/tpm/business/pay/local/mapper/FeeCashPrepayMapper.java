package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashPrepay;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPrepayVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 费用预付明细(FeeCashPrepay)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-07-23 17:12:21
 */
public interface FeeCashPrepayMapper extends BaseMapper<FeeCashPrepay> {

    List<FeeCashPrepayVo> findBySchemeDetailCode(@Param("schemeDetailCode") String schemeDetailCode);

    BigDecimal getAvailableReversedAmount(@Param("detailCode") String scheme_detail_code);
}

