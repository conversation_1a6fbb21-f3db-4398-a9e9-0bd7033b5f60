<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.FeeCashPayeeMapper">


    <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPayeeVo">
        select tfcp.*
        from tpm_fee_cash_payee tfcp
        left join tpm_fee_cash tfc on tfcp.cash_code = tfc.cash_code and tfcp.tenant_code = tfc.tenant_code
        where tfcp.tenant_code = #{dto.tenantCode}
        and tfcp.del_flag = '${@<EMAIL>()}'
        <if test="dto.receiptFlag != null and dto.receiptFlag != ''">
            and tfcp.receipt_flag = #{dto.receiptFlag}
        </if>
        <if test="dto.cashMethod != null and dto.cashMethod != ''">
            and tfc.cash_method = #{dto.cashMethod}
        </if>
        <if test="dto.cashType != null and dto.cashType != ''">
            and tfc.cash_type = #{dto.cashType}
        </if>
        <if test="dto.status != null and dto.status != ''">
            and tfc.status = #{dto.status}
        </if>
        order by tfcp.create_time asc,tfcp.id asc
    </select>
    <select id="updateReceipt">
        <foreach item="dto" collection="dtoList" index="index" separator=";">
            update tpm_fee_cash_payee
            set receipt = #{dto.ftpPath},
            receipt_flag = 'Y'
            where cash_code = #{dto.sourceOrderNumber}
            and line_code = #{dto.tpmPmtLineNumber}
        </foreach>
    </select>
    <select id="hecPayStatusCallback">
        <foreach item="dto" collection="dtoList" index="index" open="" separator=";" close="">
            update tpm_fee_cash_payee
            set pay_status = #{dto.orderStatus},
            pay_sucess_date = #{dto.paySucessDate}
            where cash_code = #{dto.businessCode}
            and line_code = #{dto.businessDetailCode}
        </foreach>
    </select>
    <select id="findPayStatus" resultType="com.biz.crm.tpm.business.pay.local.entity.FeeCashPayee">
        select distinct
        tfcp.cash_code
        ,tfcp.pay_status
        from tpm_fee_cash_payee tfcp
        left join tpm_fee_cash tfc on tfc.cash_code = tfcp.cash_code and tfc.tenant_code = tfcp.tenant_code
        where tfc.del_flag = '${@<EMAIL>()}'
        and tfc.cash_code in
        <foreach item="item" collection="cashCodes" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>

