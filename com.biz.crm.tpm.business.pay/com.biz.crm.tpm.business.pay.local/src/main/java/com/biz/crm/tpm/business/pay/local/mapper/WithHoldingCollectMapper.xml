<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.WithHoldingCollectMapper">

    <select id="updateCollectAmount">
        UPDATE tpm_with_holding_collect c
        set with_holding_amount=
                (
                    select sum(IFNULL(w.actual_report_amount, 0)) from tpm_with_holding w where w.collect_code=c.collect_code
                )
        where
            c.collect_code=#{collectCode}
    </select>
</mapper>

