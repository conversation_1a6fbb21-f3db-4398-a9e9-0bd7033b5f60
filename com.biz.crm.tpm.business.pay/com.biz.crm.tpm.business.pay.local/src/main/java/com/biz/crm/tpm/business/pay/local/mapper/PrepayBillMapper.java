package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.PrepayBill;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 活动预付账单;(tpm_prepay_bill)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Mapper
public interface PrepayBillMapper extends BaseMapper<PrepayBill>{
    boolean addPrepayAmount(@Param("activitiesDetailCode") String activitiesDetailCode, @Param("amount") BigDecimal amount, @Param("tenantCode") String tenantCode);

    boolean reducePrepayAmount(@Param("activitiesDetailCode") String activitiesDetailCode, @Param("amount") BigDecimal amount, @Param("tenantCode") String tenantCode);

}