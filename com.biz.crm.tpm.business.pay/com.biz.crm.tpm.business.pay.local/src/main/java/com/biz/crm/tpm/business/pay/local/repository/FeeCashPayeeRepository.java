package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashPayee;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashPayeeMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPayeeDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.enums.HecPayStatusTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPayeeVo;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.HecElectronicReceiptVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 费用兑付收款明细(FeeCashPayee)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
@Component
public class FeeCashPayeeRepository extends ServiceImpl<FeeCashPayeeMapper, FeeCashPayee> {

    @Autowired
    private FeeCashPayeeMapper feeCashPayeeMapper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    public List<FeeCashPayeeVo> findByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return Lists.newArrayList();
        }
        List<FeeCashPayee> list = this.lambdaQuery().eq(FeeCashPayee::getCashCode, code).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashPayee.class, FeeCashPayeeVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 按编码查询
     *
     * @param codes
     * @return
     */
    public List<FeeCashPayeeVo> findByCodes(List<String> codes) {
        List<FeeCashPayee> list = this.lambdaQuery().in(FeeCashPayee::getCashCode, codes).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashPayee.class, FeeCashPayeeVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 按编码删除
     *
     * @param codes
     */
    public void deleteByCodes(List<String> codes) {
        this.remove(Wrappers.lambdaQuery(FeeCashPayee.class)
                .in(FeeCashPayee::getCashCode, codes));
    }

    public Page<FeeCashPayeeVo> findByConditions(Pageable pageable, FeeCashPayeeDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new FeeCashPayeeDto());
        if (StringUtil.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        Page<FeeCashPayeeVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findByConditions(page, dto);
    }

    public void updateReceipt(List<HecElectronicReceiptVo> receiptVoList) {
        if (CollectionUtil.isEmpty(receiptVoList)) {
            return;
        }
        receiptVoList = receiptVoList.stream().filter(k -> StringUtil.isNotEmpty(k.getSourceOrderNumber()))
                .filter(k -> StringUtil.isNotEmpty(k.getTpmPmtLineNumber()))
                .filter(k -> StringUtil.isNotEmpty(k.getFtpPath()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(receiptVoList)) {
            return;
        }
        this.baseMapper.updateReceipt(receiptVoList);
    }

    public void hecPayStatusCallback(List<HecCallbackDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        this.baseMapper.hecPayStatusCallback(dtoList);
    }

    public void hecPayStatus(Set<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return;
        }
        this.lambdaUpdate().in(FeeCashPayee::getCashCode, codes)
                .set(FeeCashPayee::getPayStatus, HecPayStatusTypeEnum.NOT_PAY.getCode())
                .set(FeeCashPayee::getPaySucessDate, null).update();
    }

    public List<FeeCashPayee> findPayStatus(Set<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findPayStatus(codes);
    }

    public List<FeeCashPayee> findByIds(Set<String> idsSet) {
        if (CollectionUtil.isEmpty(idsSet)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery().in(FeeCashPayee::getId, idsSet).list();
    }
}

