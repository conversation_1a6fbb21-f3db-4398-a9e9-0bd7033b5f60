package com.biz.crm.tpm.business.pay.local.register;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * @className: com.biz.crm.tpm.business.pay.local.register.PlanBigDateExecutionDataViewRegister
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-08-03 10:04
 */
@Component
public class PlanBigDateExecutionDataViewRegister implements DataviewRegister {
    @Override
    public String code() {
        return "tpm_plan_big_date_data_view";
    }

    @Override
    public String desc() {
        return "客户活动执行数据视图";
    }

    @Override
    public String buildSql() {
        return "SELECT t.* " +
                "    FROM tpm_big_date_collect t " +
                "    WHERE t.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' ";
    }
}
