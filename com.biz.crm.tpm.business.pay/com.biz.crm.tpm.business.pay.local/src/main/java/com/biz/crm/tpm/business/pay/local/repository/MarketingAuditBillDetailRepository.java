package com.biz.crm.tpm.business.pay.local.repository;



import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAuditBillDetail;
import com.biz.crm.tpm.business.pay.local.mapper.MarketingAuditBillDetailMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditBillDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 方案票扣明细(MarketingAuditBillDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 16:58:19
 */
@Component
public class MarketingAuditBillDetailRepository extends ServiceImpl<MarketingAuditBillDetailMapper, MarketingAuditBillDetail> {

  @Autowired
  private MarketingAuditBillDetailMapper marketingAuditBillDetailMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<MarketingAuditBillDetailVo> findByCode(String code) {
    List<MarketingAuditBillDetail> list = this.lambdaQuery().eq(MarketingAuditBillDetail::getAuditCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, MarketingAuditBillDetail.class, MarketingAuditBillDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码集合查询
   *
   * @param codes
   * @return
   */
  public List<MarketingAuditBillDetailVo> findByCodes(List<String> codes) {
    List<MarketingAuditBillDetail> list = this.lambdaQuery().in(MarketingAuditBillDetail::getAuditCode, codes).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, MarketingAuditBillDetail.class, MarketingAuditBillDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param codes
   */
  public void deleteByCodes(List<String> codes) {
    this.remove(Wrappers.lambdaQuery(MarketingAuditBillDetail.class)
            .in(MarketingAuditBillDetail::getAuditCode, codes));
  }
}

