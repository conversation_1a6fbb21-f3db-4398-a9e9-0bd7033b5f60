package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实体：费用核销关联活动;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "AuditActivities", description = "费用核销关联活动")
@TableName("tpm_audit_activities")
@Getter
@Setter
@Entity(name = "tpm_audit_activities")
@org.hibernate.annotations.Table(appliesTo = "tpm_audit_activities", comment = "费用核销关联活动")
@Table(name = "tpm_audit_activities")
public class AuditActivities extends TenantEntity {
  private static final long serialVersionUID = 162523116441613422L;
  /**
   * 活动编号
   */
  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  @Column(name = "activities_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  @Column(name = "activities_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 开始时间
   */
  @ApiModelProperty(name = "开始时间", notes = "开始时间")
  @Column(name = "begin_time", nullable = false, columnDefinition = "DATETIME COMMENT '开始时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @ApiModelProperty(name = "结束时间", notes = "结束时间")
  @Column(name = "end_time", nullable = false, columnDefinition = "DATETIME COMMENT '结束时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  /**
   * 创建时间
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "create_time", length = 20, columnDefinition = "datetime COMMENT '创建时间 '")
  private Date createTime;

  /**
   * 创建人账号
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "create_account", length = 60, columnDefinition = "varchar(60) COMMENT '创建人账号'")
  private String createAccount;

  /**
   * 创建人名称
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "create_name", length = 60, columnDefinition = "varchar(60) COMMENT '创建人名称'")
  private String createName;

  /**
   * 核销申请编码
   */
  @ApiModelProperty(name = "核销申请编码", notes = "核销申请编码")
  @Column(name = "audit_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编码 '")
  private String auditCode;
}