package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.tpm.business.pay.local.repository.AuditDetailWithCustomerVoRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailWithCustomerVoService;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailWithCustomerVo;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessMappingDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.ProcessBusinessMappingService;
import com.biz.crm.workflow.sdk.vo.ProcessBusinessMappingVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年07月11日 16:43:00
 */
@Service
public class AuditDetailWithCustomerVoServiceImpl implements AuditDetailWithCustomerVoService {

  @Autowired
  private AuditDetailWithCustomerVoRepository auditDetailWithCustomerVoRepository;
  @Autowired(required = false)
  private ProcessBusinessMappingService processBusinessMappingService;


  /**
   * 获取客户维度核销明细
   *
   * @param dto
   * @return
   */
  @Override
  public List<AuditDetailWithCustomerVo> getAuditDetailWithCustomer(AuditDetailDto dto) {
    List<AuditDetailWithCustomerVo> auditDetailWithCustomer = auditDetailWithCustomerVoRepository.getAuditDetailWithCustomer(dto);
    if (CollectionUtils.isEmpty(auditDetailWithCustomer)) {
      return new ArrayList<>(0);
    }
    //过滤出已经走审批流的数据
    List<String> auditCodes = auditDetailWithCustomer.stream().map(AuditDetailWithCustomerVo::getAuditCode).collect(Collectors.toList());
    List<ProcessBusinessMappingVo> processBusinessMappingVo = this.findProcessBusinessMappingVo(auditCodes);
    if (CollectionUtils.isEmpty(processBusinessMappingVo)) {
      return new ArrayList<>(0);
    }
    List<String> businessNos = processBusinessMappingVo.stream().filter(item -> ProcessStatusEnum.PASS.getDictCode().equals(item.getProcessStatus())).map(ProcessBusinessMappingVo::getBusinessNo).collect(Collectors.toList());
    auditDetailWithCustomer = auditDetailWithCustomer.stream().filter(ite -> businessNos.contains(ite.getAuditCode())).collect(Collectors.toList());
    return auditDetailWithCustomer;
  }

  /**
   * 根据业务编码查询工作流业务流程关联信息
   *
   * @param businessNos
   */
  private List<ProcessBusinessMappingVo> findProcessBusinessMappingVo(List<String> businessNos) {
    ProcessBusinessMappingDto processBusinessMappingDto = new ProcessBusinessMappingDto();
    processBusinessMappingDto.setBusinessNos(businessNos);
    processBusinessMappingDto.setBusinessCode(PayConstant.PROCESS_AUDIT_ACTIVITIES);
    return processBusinessMappingService.findMultiByByConditions(processBusinessMappingDto);
  }
}
