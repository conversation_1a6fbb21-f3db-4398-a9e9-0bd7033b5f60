package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.service.CostControlLoginService;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingRepository;
import com.biz.crm.tpm.business.pay.sdk.enums.*;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingSendHecService;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class WithHoldingSendHecServiceImpl implements WithHoldingSendHecService {

    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private CostControlLoginService costControlLoginService;

    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Value("${domain-name:}")
    private String domainName;

    /**
     * 计提汇总 审批通过 自动提交费控
     *
     * @param idList
     * @param crmUserIdentity
     */
    @Override
    @Async
    public void pushCollectAutoAsync(List<String> idList, AbstractCrmUserIdentity crmUserIdentity) {
        if (CollectionUtil.isEmpty(idList)) {
            return;
        }
        loginUserService.refreshAuthentication(crmUserIdentity);
        this.pushCollect(idList);
    }

    /**
     * 计提汇总 审批通过 自动提交费控
     *
     * @param idList
     */
    @Override
    public Result<?> pushCollect(List<String> idList) {
        Assert.notEmpty(idList, "请选择数据!");
        List<WithHoldingVo> holdingVoList = withHoldingRepository.findSendDataByCollectIds(idList);
        return this.withHoldingSendHec(holdingVoList);
    }

    /**
     * 提交费控
     *
     * @param idList
     * @return
     */
    @Override
    public Result<?> push(List<String> idList) {
        Assert.notEmpty(idList, "请选择数据!");
        List<WithHoldingVo> holdingVoList = withHoldingRepository.findSendDataByIds(idList);
        return this.withHoldingSendHec(holdingVoList);
    }

    /**
     * 推送费控 费用计提
     *
     * @param sendHecList 费用计提
     * @return void
     * <AUTHOR>
     * @date 2024/7/9 17:04
     */
    private Result<String> withHoldingSendHec(List<WithHoldingVo> sendHecList) {
        Assert.notEmpty(sendHecList, "选择的数据不存在,请刷新页面重试!");
        List<WithHoldingVo> sendHecDataList = Lists.newArrayList();
        List<String> notNeedSendCodeList = Lists.newArrayList();

        sendHecList.stream()
                .filter(k -> !HecSendStatusEnum.NOT_NEED_PUSH.getCode().equals(k.getPushStatus()))
                .forEach(item -> {
                    Validate.notEmpty(item.getPushHecCode(), "费用计提[" + item.getCollectCode()
                            + "]计提明细[" + item.getWithHoldingCode() + "]" + "推送费控单号为空,不可推送");
                    Validate.isTrue(ProcessStatusEnum.PASS.getDictCode().equals(item.getCollectStatus()),
                            "费用计提[" + item.getCollectCode() + "]未审批通过,不可推送费控");
                    Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(item.getPushStatus()),
                            "费用计提[" + item.getCollectCode() + "]计提明细[" + item.getWithHoldingCode()
                                    + "]已推送费控,不可重复推送");
                    item.setHecOperationType(HecOperationTypeEnum.CREATE.getCode());
                    if (Objects.isNull(item.getActualAmount())
                            || item.getActualAmount().compareTo(BigDecimal.ZERO) == 0) {
                        notNeedSendCodeList.add(item.getWithHoldingCode());
                    } else {
                        sendHecDataList.add(item);
                    }
                });

        if (CollectionUtil.isNotEmpty(notNeedSendCodeList)) {
            withHoldingRepository.updatePushStatusByWithHoldingCodes(notNeedSendCodeList, HecSendStatusEnum.NOT_NEED_PUSH);
        }

        if (CollectionUtil.isEmpty(sendHecDataList)) {
            Result<String> result = new Result<>();
            result.setMessage("推送费控成功!");
            return result;
        }
        StringBuffer errorMsg = new StringBuffer();
        Map<String, List<WithHoldingVo>> sendHecDtoMap = sendHecDataList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getPushHecCode()))
                .collect(Collectors.groupingBy(WithHoldingVo::getPushHecCode));
        sendHecDtoMap.forEach((pushHecCode, voList) -> {
            ExternalLogDetailDto logDetailDto = null;
            JSONObject jsonObject = this.buildWithHoldingSendData(voList);
            List<String> withHoldingCodeList = voList.stream().map(WithHoldingVo::getWithHoldingCode)
                    .filter(StringUtil::isNotEmpty).collect(Collectors.toList());
            Assert.notNull(jsonObject, "未查询到数据,请刷新重新选择数据!");
            try {
                String token = costControlLoginService.getToken();
                UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
                Map<String, String> headMap = Maps.newHashMap();
                headMap.put(RyConstant.HEADER_AUTHORIZATION, token);
                String url = urlAddressVo.getUrl();
                String interfaceAddress = RyConstant.FK_FEE_PROVISION_INTERFACE_ADDRESS;
                JSONObject headJson = new JSONObject();
                headJson.put(RyConstant.HEADER_AUTHORIZATION, token);
                logDetailDto = ExternalLogUtil.buildLogSaveInfo(jsonObject.toJSONString(), urlAddressVo);
                logDetailDto.setReqHead(headJson.toJSONString());
                logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf(".") + 1));
                logDetailDto.setRequestUri(interfaceAddress);
                logDetailDto.setMethodMsg("费用计提提交");
                externalLogVoService.addOrUpdateLog(logDetailDto, true);
                Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, jsonObject.toJSONString(), headMap);
                ExternalLogUtil.buildLogResult(logDetailDto, result);
                this.buildHecResultData(result, withHoldingCodeList);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
            } catch (Exception e) {
                if (Objects.nonNull(logDetailDto)) {
                    logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                    logDetailDto.setTipMsg(e.getMessage());
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                }
                String failMsg = "提交费控异常:" + e.getMessage();
                withHoldingRepository.updateErrorMsg(withHoldingCodeList, failMsg);
                log.error(e.getMessage(), e);
                errorMsg.append("单据[");
                errorMsg.append(pushHecCode);
                errorMsg.append("];");
                errorMsg.append(failMsg);
                errorMsg.append(";");
            }

        });
        Result<String> result = new Result<>();
        if (StringUtil.isNotEmpty(errorMsg.toString())) {
            result.error500(errorMsg.toString());
        }
        return result;

    }


    /**
     * 构建明细 推送费控
     *
     * @param voList
     * @return
     */
    private JSONObject buildWithHoldingSendData(List<WithHoldingVo> voList) {
        if (CollectionUtil.isEmpty(voList)) {
            return null;
        }
        List<String> fileUrlList = Lists.newArrayList();
        JSONObject jsonObject = new JSONObject();
        JSONObject headVo = this.buildWithHoldingHead(voList);
        JSONArray requisitionLines = this.buildLines(voList);
        headVo.put("fileUrls", fileUrlList);
        headVo.put("lines", requisitionLines);
        jsonObject.put("payload", JSONObject.toJSONString(headVo));
        return jsonObject;
    }

    /**
     * 构建推送明细  头部信息
     * 推送费控 费用计提
     *
     * @param voList
     * @return
     */
    private JSONObject buildWithHoldingHead(List<WithHoldingVo> voList) {
        JSONObject headVo = new JSONObject();
        if (CollectionUtil.isEmpty(voList)) {
            return headVo;
        }
        WithHoldingVo vo = voList.get(0);
        String companyCode = vo.getCompanyCode();
        Assert.hasLength(companyCode, "费用计提[" + vo.getCollectCode() + "]公司[company_code]为空!");
        //1046540 【费用计提】推送费控的companyCode取值调整 业务反反复复
//        List<DictDataVo> dictDataVoList = dictDataVoService.findTreeByDictTypeCode("tpm_company");
//        Assert.notEmpty(dictDataVoList, "数据字典[tpm_company]未配置!");
//        DictDataVo dictDataVo = dictDataVoList.stream().filter(k -> StringUtil.isNotEmpty(k.getDictCode()))
//                .filter(k -> companyCode.equals(k.getDictCode()))
//                .findFirst().orElse(null);
//        Assert.notNull(dictDataVo, "数据字典[tpm_company]未配置公司[" + companyCode + "]信息!");
//        Assert.notEmpty(dictDataVo.getExtendMap(), "数据字典[tpm_company]公司[" + companyCode + "]信息无对应OA信息!");
//        String oaId = dictDataVo.getExtendMap().get("oa_id");
//        Assert.hasLength(oaId, "数据字典[tpm_company]公司[" + companyCode + "]信息对应OA信息为空!");

        Assert.hasLength(vo.getCollectPositionCode(), "费用计提[" + vo.getCollectCode() +
                "]职位[position_code]为空!");
        PositionVo positionVo = positionVoService.findByPositionCode(vo.getCollectPositionCode());
        Assert.notNull(positionVo, "费用计提[" + vo.getCollectCode() +
                "职位[" + vo.getCollectPositionCode() + "]在主数据中不存在!");
        headVo.put("companyCode", positionVo.getSubCompanyId());
        headVo.put("employeeCode", positionVo.getUserName());
        headVo.put("accEntityCode", companyCode);
        headVo.put("unitCode", positionVo.getDepartmentId());
        headVo.put("sourceSystem", "TPM");
        headVo.put("sourceOrderNumber", vo.getPushHecCode());
        headVo.put("sourceSystemOperation", vo.getHecOperationType());
        headVo.put("sourceOrderType", HecBusinessTypeEnum.TPM_WITH_HOLDING_HEC.getCode());
        headVo.put("operatorCode", positionVo.getUserName());
        headVo.put("operatedDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
        headVo.put("paymentCurrencyCode", RyConstant.CNY);
        headVo.put("description", vo.getCollectRemark());
        headVo.put("requisitionDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));

        headVo.put("accrualReqTypeCode", "TPMYT004");
        headVo.put("creationMethod", "MANUAL");
        //headVo.put("sourceOrderUrl", domainName + TpmOaPageEnum.WITH_HOLDING_TOTAL_APPROVEFORM.getUrlCode() + "?code=" + vo.getCollectCode());
        return headVo;
    }

    /**
     * 构建推送明细  行信息
     * 推送费控 费用计提
     *
     * @param voList
     * @return
     */
    private JSONArray buildLines(List<WithHoldingVo> voList) {
        JSONArray jsonArray = new JSONArray();
        if (CollectionUtil.isEmpty(voList)) {
            return jsonArray;
        }
        List<String> orgCodeList = voList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getBearDepartmentCode()))
                .map(WithHoldingVo::getBearDepartmentCode)
                .distinct().collect(Collectors.toList());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(orgCodeList);
        voList.forEach(detailVo -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("description", detailVo.getRemark());
            Assert.hasLength(detailVo.getWithHoldingType(), "费用计提[" + detailVo.getWithHoldingCode() + "]" +
                    "预提类型为空!");
            WithHoldingTypeEnum withHoldingTypeEnum = WithHoldingTypeEnum.findByCode(detailVo.getWithHoldingType());
            Assert.notNull(withHoldingTypeEnum, "费用计提[" + detailVo.getWithHoldingCode() + "]" +
                    "预提类型[" + detailVo.getWithHoldingType() + "]不合法!");
            //【费用计提】1、预提类型为“已申请未结案”或“已结案未兑付”时传活动开始时间所属年月 2、其余取预提入账年月
            //【费用冲销】传对应费用计提的费用归属期间
            if (WithHoldingTypeEnum.NOT_AUDIT.equals(withHoldingTypeEnum)
                    || WithHoldingTypeEnum.NOT_CASH.equals(withHoldingTypeEnum)) {
                jsonObject.put("periodName", detailVo.getYears());
            } else {
                jsonObject.put("periodName", detailVo.getYearMonthLy());
            }
            //【费用计提】：预提入账年月
            //【费用冲销】：冲销年月
            jsonObject.put("settlementName", detailVo.getYearMonthLy());
            jsonObject.put("businessItemCode", detailVo.getCostTypeCategoryCode());
            jsonObject.put("dimension3Code", detailVo.getItemCode());
            jsonObject.put("dimension5Code", detailVo.getErpCode());
            List<OrgOaOrgVo> oaOrgVoList = orgOaOrgVoMap.get(detailVo.getBearDepartmentCode());
            Assert.notEmpty(oaOrgVoList, "费用计提[" + detailVo.getCollectCode() +
                    "]预提[" + detailVo.getWithHoldingCode() + "]承担部门[" + detailVo.getBearDepartmentCode() + "]未找到OA组织!");
            jsonObject.put("unitCode", oaOrgVoList.get(0).getOaOrgCode());
            jsonObject.put("respCenterCode", detailVo.getCostCenterCode());
            jsonObject.put("businessAmount", detailVo.getActualAmount());
            //传兑付方式（电汇：CASH；账扣：ACCOUNT；票扣：INVOICE；货补：ORDER）
            CashMethodEnum cashMethodEnum = CashMethodEnum.findByCode(detailVo.getPayBy());
            Assert.notNull(cashMethodEnum, "费用计提[" + detailVo.getCollectCode() +
                    "]预提[" + detailVo.getWithHoldingCode() + "]兑付方式[" + detailVo.getPayBy() + "]不合法!");
            jsonObject.put("paymentMethod", cashMethodEnum.getHecCode());
            jsonObject.put("sapFlag", "Y");
            jsonObject.put("autoReverseFlag", "Y");
            //当期费用时传Y，往期费用时传N
            jsonObject.put("currentFlag", detailVo.getBeThisFee());
            jsonObject.put("paymentCurrencyCode", RyConstant.CNY);
            jsonObject.put("functionalCurrencyCode", RyConstant.CNY);
            jsonObject.put("businessCurrencyCode", RyConstant.CNY);
            jsonObject.put("sourceOrderLineNumber", detailVo.getWithHoldingCode());
            //【费用计提】不传
            //【费用冲销】费用冲销对应费用计提的“推送费控单号”
            jsonObject.put("sourceAccrualReqHdNumber", "");
            //【费用计提】不传
            //【费用冲销】费用冲销对应费用计提的计提编码
            jsonObject.put("sourceAccrualReqLnNumber", "");
            //活动描述
            jsonObject.put("description", detailVo.getActDesc());
            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }

    /**
     * 费用返回数据
     *
     * @param result
     */
    private void buildHecResultData(Result<String> result, List<String> withHoldingCodeList) {
        Assert.hasLength(result.getResult(), "费控返回信息为空1!");
        JSONObject jsonObject = JSONObject.parseObject(result.getResult());
        Assert.notNull(jsonObject, "费控返回信息为空2!");
        Assert.isTrue(jsonObject.containsKey("status"), "费控返回信息缺少[status]字段!");
        Assert.isTrue(jsonObject.containsKey("message"), "费控返回信息缺少[message]字段!");
        Assert.isTrue(String.valueOf(com.biz.crm.business.common.sdk.constant.CommonConstant.SC_OK_200)
                .equals(jsonObject.getString("status")), "费控返回信息:" + jsonObject.get("message"));
        Assert.isTrue(jsonObject.containsKey("payload"), "费控返回信息缺少[payload]对象!");
        JSONObject payload = jsonObject.getJSONObject("payload");
        Assert.notNull(payload, "费控返回信息[payload]对象为空");
        Assert.isTrue(payload.containsKey("status"), "费控返回信息缺少[payload]对象缺少[status]字段!");
        Assert.isTrue(payload.containsKey("message"), "费控返回信息缺少[payload]对象缺少[message]字段!");
        Assert.isTrue(ExternalLogGlobalConstants.S.equals(payload.getString("status")), "费控返回信息:" + payload.get("message"));
        Assert.isTrue(payload.containsKey("result"), "费控返回信息缺少[payload]对象缺少[result]字段!");

        JSONArray resultJson = payload.getJSONArray("result");
        Assert.notNull(resultJson, "费控返回信息[result]对象为空1");
        Assert.notEmpty(resultJson, "费控返回信息[result]对象为空2");
        String hecReceiptNumber = resultJson.getJSONObject(0).getString("hecReceiptNumber");
        //String hecReceiptUrl = resultJson.getJSONObject(0).getString("hecReceiptUrl");
        Assert.hasLength(hecReceiptNumber, "费控返回信息[result]内[hecReceiptNumber]为空");
        //Assert.hasLength(hecReceiptUrl, "费控返回信息[result]内[hecReceiptUrl]为空");
        withHoldingRepository.updateHecInfo(withHoldingCodeList, hecReceiptNumber);

    }

}
