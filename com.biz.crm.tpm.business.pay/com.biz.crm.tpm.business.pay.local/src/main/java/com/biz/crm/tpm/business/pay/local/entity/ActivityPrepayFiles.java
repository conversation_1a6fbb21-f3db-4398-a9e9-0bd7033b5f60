package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：活动预付附件;
 * <AUTHOR> yaoyongming
 * @date : 2024-6-4
 */
@ApiModel(value = "ActivityPrepayFiles",description = "活动预付附件")
@TableName("tpm_activity_prepay_files")
@Getter
@Setter
@Entity(name = "tpm_activity_prepay_files")
@org.hibernate.annotations.Table(appliesTo = "tpm_activity_prepay_files", comment = "活动预付附件")
@Table(name = "tpm_activity_prepay_files")
public class ActivityPrepayFiles extends FileEntity {

  /** 活动预付编号 */
  @ApiModelProperty("活动预付编号")
  @Column(name = "prepay_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动预付编号 '")
  private String prepayCode;
}