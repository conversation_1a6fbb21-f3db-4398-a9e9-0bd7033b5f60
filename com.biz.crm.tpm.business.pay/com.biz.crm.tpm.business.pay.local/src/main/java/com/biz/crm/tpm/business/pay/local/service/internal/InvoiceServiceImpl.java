package com.biz.crm.tpm.business.pay.local.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.service.CostControlLoginService;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.vo.OssFileVo;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.tpm.business.pay.local.entity.Invoice;
import com.biz.crm.tpm.business.pay.local.repository.InvoiceRepository;
import com.biz.crm.tpm.business.pay.sdk.dto.InvoiceDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.InvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.InvoiceLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.event.log.InvoiceLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceService;
import com.biz.crm.tpm.business.pay.sdk.util.CostControlResultUtil;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.biz.crm.workflow.sdk.service.OssVoService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {

    private static final String PATTERN = "^[A-Za-z0-9]+$";

    public static final String INVOICE_TYPE = "invoice_type";

    public static final String TPM_COMPANY = "tpm_company";

    @Resource
    private InvoiceRepository invoiceRepository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Autowired
    private InvoiceDetailService invoiceDetailService;

    @Resource
    private NebulaNetEventClient nebulaNetEventClient;

    @Resource
    private CostControlLoginService costControlLoginService;

    @Resource
    private UrlApiService urlApiService;

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private ExternalLogVoService externalLogVoService;

    @Resource
    private UserVoService userVoService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private PositionVoService positionVoService;

    @Resource
    private OssVoService ossVoService;

    @Value("${spring.profiles.active:}")
    private String env;

    @Override
    public Page<InvoiceVo> findByConditions(Pageable pageable, InvoiceDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        dto.setTenantCode(TenantUtils.getTenantCode());

        if("dev".equals(env)){
            log.info(" InvoiceDto {}",JSONObject.toJSONString(dto));
        }
        Page<InvoiceVo> page = invoiceRepository.findByConditions(pageable, dto);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            List<OssFileVo> ossFileVos = page.getRecords().stream().filter(x -> ObjectUtils.isNotEmpty(x.getFileUrl()))
                    .map(x -> {
                        OssFileVo ossFileVo = new OssFileVo();
                        ossFileVo.setFileCode(x.getFileCode());
                        ossFileVo.setFileUrl(x.getFileUrl());
                        ossFileVo.setFileName(x.getFileName());
                        return ossFileVo;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ossFileVos)) {
                List<OssFileVo> ossFileResult = ossVoService.getOssUrlList(ossFileVos);
                Map<String, String> ossFileMap = ossFileResult.stream().collect(Collectors.toMap(OssFileVo::getFileCode, OssFileVo::getFileUrl));
                for (InvoiceVo record : page.getRecords()) {
                    if (ossFileMap.containsKey(record.getFileCode())) {
                        record.setFileUrl(ossFileMap.get(record.getFileCode()));
                    }
                }
            }
        }
        return page;
    }

    @Override
    @Transactional
    public InvoiceVo create(InvoiceDto dto) {
        this.createValidate(dto);
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        if (ObjectUtils.isEmpty(dto.getChecked())) {
            dto.setChecked(BooleanEnum.FALSE.getCapital());
        }
        if (ObjectUtils.isEmpty(dto.getSyncFlag())) {
            dto.setSyncFlag(BooleanEnum.FALSE.getCapital());
        }
        //手动处理公司编码
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(TPM_COMPANY);
        if (CollectionUtils.isNotEmpty(dictDataVos)) {
            Map<String, DictDataVo> dictDataVoMap = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, Function.identity()));
            if (dictDataVoMap.containsKey(dto.getPurchaser())) {
                DictDataVo dictDataVo = dictDataVoMap.get(dto.getPurchaser());
                dto.setCompanyCode(dictDataVo.getExt2());
                dto.setAccEntityCode(dictDataVo.getDictCode());
                // 从上传创建已经赋值、未赋值也从字典获取
                if(Objects.isNull(dto.getPNo()) || StringUtils.isBlank(dto.getPNo())){
                    dto.setPNo(dictDataVo.getExt1());
                }
            }
        }
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        dto.setOrgCode(loginDetails.getOrgCode());
        dto.setOrgName(loginDetails.getOrgName());
        dto.setPositionCode(loginDetails.getPostCode());
        dto.setPositionName(loginDetails.getPostName());
        //保存主表
        Invoice invoice = nebulaToolkitService.copyObjectByWhiteList(dto, Invoice.class, HashSet.class, ArrayList.class, "items");
        invoiceRepository.save(invoice);
        InvoiceVo invoiceVo = this.nebulaToolkitService.copyObjectByWhiteList(invoice, InvoiceVo.class, LinkedHashSet.class, ArrayList.class);
        List<InvoiceDetailVo> itemList = Lists.newArrayList();
        //保存发票明细 todo 需要考虑哪些发票明细是空的情况
        if (CollectionUtils.isEmpty(dto.getItems())) {
            InvoiceDetailVo detailVo = new InvoiceDetailVo();
            detailVo.setName(dto.getPurchaser());
            detailVo.setPriceAndTax(dto.getPriceAndTax());
            detailVo.setAmountWithoutTax(dto.getAmountWithoutTax());
            detailVo.setTaxAmount(dto.getTaxAmount());
            detailVo.setTaxRate(dto.getTaxRate());
            itemList.add(detailVo);
        } else {
            List<InvoiceDetailVo> details = (List<InvoiceDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(dto.getItems(), InvoiceDetailDto.class, InvoiceDetailVo.class, HashSet.class, ArrayList.class);
            invoiceDetailService.saveBatchList(details, null, invoice.getInvoiceNo());
        }

        //新增业务日志
        InvoiceLogEventDto logEventDto = new InvoiceLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(invoiceVo);
        SerializableBiConsumer<InvoiceLogEventListener, InvoiceLogEventDto> onCreate = InvoiceLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, InvoiceLogEventListener.class, onCreate);
        return invoiceVo;
    }

    @Override
    @Transactional
    public InvoiceVo update(InvoiceDto dto) {
        this.updateValidate(dto);
        Invoice dbInvoice = invoiceRepository.findByIdAndTenantCode(dto.getId(), TenantUtils.getTenantCode());
        Validate.isTrue(dbInvoice.getSyncFlag().equals(BooleanEnum.FALSE.getCapital()), "该发票已同步费控,不可编辑");
        Validate.notNull(dbInvoice, "根据发票主键id，未能获取到相应信息");
        InvoiceVo oldInvoiceVo = this.nebulaToolkitService.copyObjectByWhiteList(dbInvoice, InvoiceVo.class, LinkedHashSet.class, ArrayList.class);
        if (!StringUtils.equals(dto.getInvoiceNo(), dbInvoice.getInvoiceNo())) {
            InvoiceVo invoiceVo = this.findByInvoiceNo(dto.getInvoiceNo());
            Validate.isTrue(invoiceVo == null, "发票号码【%s】重复，请检查", dto.getInvoiceNo());
        }
        String id = oldInvoiceVo.getId();
        //手动处理公司编码
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(TPM_COMPANY);
        if (CollectionUtils.isNotEmpty(dictDataVos)) {
            Map<String, DictDataVo> dictDataVoMap = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, Function.identity()));
            if (dictDataVoMap.containsKey(dto.getPurchaser())) {
                DictDataVo dictDataVo = dictDataVoMap.get(dto.getPurchaser());
                dto.setCompanyCode(dictDataVo.getExt2());
                dto.setAccEntityCode(dictDataVo.getDictCode());
                dto.setPNo(dictDataVo.getExt1());
            }
        }
        dbInvoice = nebulaToolkitService.copyObjectByBlankList(dto, Invoice.class, HashSet.class, ArrayList.class);
        dbInvoice.setTenantCode(TenantUtils.getTenantCode());
        dbInvoice.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dbInvoice.setId(id);
        invoiceRepository.saveOrUpdate(dbInvoice);
        InvoiceVo invoiceVo = this.nebulaToolkitService.copyObjectByWhiteList(dto, InvoiceVo.class, LinkedHashSet.class, ArrayList.class, "items");

        invoiceDetailService.saveBatchList(invoiceVo.getItems(), oldInvoiceVo.getInvoiceNo(), dto.getInvoiceNo());

        //编辑业务日志
        InvoiceLogEventDto logEventDto = new InvoiceLogEventDto();
        logEventDto.setOriginal(oldInvoiceVo);
        logEventDto.setNewest(invoiceVo);
        SerializableBiConsumer<InvoiceLogEventListener, InvoiceLogEventDto> onUpdate = InvoiceLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, InvoiceLogEventListener.class, onUpdate);
        return invoiceVo;
    }

    @Override
    public List<InvoiceVo> findByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<Invoice> invoices = invoiceRepository.listByIdsAndTenantCode(ids, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(invoices)) {
            return Lists.newArrayList();
        }
        List<InvoiceVo> invoiceVos = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(invoices, Invoice.class, InvoiceVo.class, HashSet.class, ArrayList.class));
        List<String> invoceNoList = invoices.stream().map(Invoice::getInvoiceNo).collect(Collectors.toList());
        List<InvoiceDetailVo> detailList = invoiceDetailService.findListByInvoiceNoList(invoceNoList);
        Map<String, List<InvoiceDetailVo>> detailsMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(detailList)) {
            detailsMap = detailList.stream().collect(Collectors.groupingBy(InvoiceDetailVo::getInvoiceNo));
        }
        for (InvoiceVo invoiceVo : invoiceVos) {
            invoiceVo.setItems(detailsMap.get(invoiceVo.getInvoiceNo()));
        }
        return invoiceVos;
    }

    @Override
    public InvoiceVo findByInvoiceNo(String invoiceNo) {
        if (StringUtils.isBlank(invoiceNo)) {
            return null;
        }
        Invoice invoice = invoiceRepository.findByInvoiceNo(invoiceNo);
        if (invoice == null) {
            return null;
        }
        InvoiceVo invoiceVo = nebulaToolkitService.copyObjectByWhiteList(invoice, InvoiceVo.class, HashSet.class, ArrayList.class);
        List<InvoiceDetailVo> detailVos = invoiceDetailService.findListByInvoiceNoList(Lists.newArrayList(invoiceNo));
        if (CollectionUtils.isEmpty(detailVos)) {
            return invoiceVo;
        }
        invoiceVo.setItems(detailVos);
        return invoiceVo;
    }


    @Override
    public List<InvoiceVo> findByInvoiceNoList(Set<String> invoiceNos) {
        List<Invoice> invoiceList = invoiceRepository.findByInvoiceNos(invoiceNos);
        if (CollectionUtils.isEmpty(invoiceList)) {
            return Lists.newArrayList();
        }
        return (List<InvoiceVo>) nebulaToolkitService.copyCollectionByWhiteList(invoiceList, Invoice.class, InvoiceVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "主键集合不能为空！");
        List<InvoiceVo> invoiceVos = this.findByIds(Sets.newHashSet(idList));
        Validate.notEmpty(invoiceVos, "根据提供的主键集合信息，未能获取到相应数据");
        List<InvoiceVo> beCash = invoiceVos.stream().filter(e -> StringUtils.isNotBlank(e.getCashCode())).collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isEmpty(beCash), "数据已被兑付单使用");
        //执行删除发票池（主表逻辑删除）
        this.invoiceRepository.delete(Sets.newHashSet(idList));
        List<String> invoiceNoList = invoiceVos.stream().map(x -> x.getInvoiceNo()).collect(Collectors.toList());
        invoiceDetailService.deleteByInvoiceNoList(invoiceNoList);
        for (InvoiceVo invoiceVo : invoiceVos) {
            if (BooleanEnum.FALSE.getCapital().equals(invoiceVo.getSyncFlag())) return;
            InvoiceManualInputVo vo = nebulaToolkitService.copyObjectByWhiteList(invoiceVo, InvoiceManualInputVo.class, HashSet.class, ArrayList.class, "items");
            vo.setEmployeeCode(invoiceVo.getCreateAccount());
            vo.setSourceSystem("TPM");
            JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(vo));
            //获取token
            String token = costControlLoginService.getToken();
            UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
            String url = urlAddressVo.getUrl();
            String interfaceAddress = RyConstant.FK_INVOICE_DELETE;
            Map<String, String> headMap = Maps.newHashMap();
            headMap.put(RyConstant.HEADER_AUTHORIZATION, token);
            JSONObject jsonObject = new JSONObject();

            jsonObject.put("sourceSystemCode", "TPM");
            jsonObject.put("interfaceCode", "TPM_INVOICE_DELETE");
            JSONObject invoiceObjectJson = new JSONObject();
            invoiceObjectJson.put("invoiceDelete", object);
            jsonObject.put("requestData", invoiceObjectJson);
            //组装请求参数放到日志
            JSONObject headJson = new JSONObject();
            headJson.put(RyConstant.HEADER_AUTHORIZATION, token);
            ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(jsonObject.toJSONString(), urlAddressVo);
            logDetailDto.setReqHead(headJson.toJSONString());
            logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf("/") + 1));
            logDetailDto.setRequestUri(interfaceAddress);
            logDetailDto.setMethodMsg("删除发票");
            externalLogVoService.addOrUpdateLog(logDetailDto, true);
            Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, jsonObject.toJSONString(), headMap);
            ExternalLogUtil.buildLogResult(logDetailDto, result);
            String data = null;
            Boolean flag = Boolean.FALSE;
            String errMsg = null;
            try {
                data = CostControlResultUtil.validateResult(result);
                logDetailDto.setStatus(ExternalLogGlobalConstants.S);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
                flag = Boolean.TRUE;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                errMsg = e.getMessage();
                logDetailDto.setTipMsg(e.getMessage());
                logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
                throw e;
            }
        }
        //删除业务日志
        SerializableBiConsumer<InvoiceLogEventListener, InvoiceLogEventDto> onDelete = InvoiceLogEventListener::onDelete;
        for (InvoiceVo invoiceVo : invoiceVos) {
            InvoiceLogEventDto logEventDto = new InvoiceLogEventDto();
            logEventDto.setOriginal(invoiceVo);
            this.nebulaNetEventClient.publish(logEventDto, InvoiceLogEventListener.class, onDelete);
        }
    }

    /**
     * 关联兑付单
     *
     * @param invoiceNos
     * @param cashCode
     */
    @Override
    public void bindCashCode(List<String> invoiceNos, String cashCode) {
        Validate.notEmpty(invoiceNos, "发票号码不能为空");
        Validate.notBlank(cashCode, "兑付编码不能为空");
        invoiceRepository.bindCashCode(invoiceNos, cashCode);
    }

    /**
     * 清除兑付单关联
     *
     * @param cashCodes
     */
    @Override
    public void clearCashCode(List<String> cashCodes) {
        Validate.notEmpty(cashCodes, "兑付编码不能为空");
        invoiceRepository.clearCashCode(cashCodes);
    }

    /**
     * 发票验证
     *
     * @param invoiceNo
     */
    @Override
    public void checkInvoiceList(String invoiceNo) {
        Invoice invoice = invoiceRepository.findByInvoiceNo(invoiceNo);
        Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(invoice.getChecked()), String.format("发票%s已经被识别", invoiceNo));
        InvoiceVo invoiceVo = this.findByInvoiceNo(invoiceNo);
        InvoiceManualInputVo vo = nebulaToolkitService.copyObjectByWhiteList(invoiceVo, InvoiceManualInputVo.class, HashSet.class, ArrayList.class, "items");
        //查询数据字典 控制是否需要验真
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(INVOICE_TYPE);
        List<String> dictCodes = dictDataVos.stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
        Validate.isTrue(dictCodes.contains(invoice.getType()), "未知的发票类型【%s】，请检查", invoice.getType());
        for (DictDataVo dataVo : dictDataVos) {
            //判断是不需要验真的
            if (invoice.getType().equals(dataVo.getDictCode())) {
                if (BooleanEnum.TRUE.getSure().equals(dataVo.getExt1())) {
                    vo.setNeedVerifyFlag(Boolean.TRUE);
                } else {
                    vo.setNeedVerifyFlag(Boolean.FALSE);
                }
                break;
            }
        }
        vo.setEmployeeCode(invoice.getCreateAccount());
        vo.setSourceSystem("TPM");
        JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(vo));
        //获取token
        String token = costControlLoginService.getToken();
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
        String url = urlAddressVo.getUrl();
        String interfaceAddress = RyConstant.FK_INVOICE_CHECK;
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put(RyConstant.HEADER_AUTHORIZATION, token);
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("sourceSystemCode", "TPM");
        jsonObject.put("interfaceCode", "TPM_MANUAL_INVOICE");
        JSONObject invoiceObjectJson = new JSONObject();
        invoiceObjectJson.put("manualInvoice", object);
        jsonObject.put("requestData", invoiceObjectJson);
        //组装请求参数放到日志
        JSONObject headJson = new JSONObject();
        headJson.put(RyConstant.HEADER_AUTHORIZATION, token);
        ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(jsonObject.toJSONString(), urlAddressVo);
        logDetailDto.setReqHead(headJson.toJSONString());
        logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf("/") + 1));
        logDetailDto.setRequestUri(interfaceAddress);
        logDetailDto.setMethodMsg("发票检验");
        externalLogVoService.addOrUpdateLog(logDetailDto, true);
        Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, jsonObject.toJSONString(), headMap);
        ExternalLogUtil.buildLogResult(logDetailDto, result);
        String data = null;
        Boolean flag = Boolean.FALSE;
        String errMsg = null;
        try {
            data = CostControlResultUtil.validateResult(result);
            logDetailDto.setStatus(ExternalLogGlobalConstants.S);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
            flag = Boolean.TRUE;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            errMsg = e.getMessage();
            logDetailDto.setTipMsg(e.getMessage());
            logDetailDto.setStatus(ExternalLogGlobalConstants.E);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
            throw e;
        }
        if (!flag) {
            invoice.setCheckMsg(errMsg);
            invoice.setChecked(BooleanEnum.FALSE.getCapital());
        } else {
            invoice.setChecked(BooleanEnum.TRUE.getCapital());
        }
        invoice.setSyncFlag(BooleanEnum.TRUE.getCapital());
        invoiceRepository.updateById(invoice);
    }


    /**
     * 上传发票识别
     *
     * @param fileVoList
     */
    @Override
    public String uploadInvoiceFileIdentify(List<InvoiceInputStreamFileVo> fileVoList) {
        //获取token
        String token = costControlLoginService.getToken();
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
        //获取登陆信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        String url = urlAddressVo.getUrl();
        String interfaceAddress = RyConstant.FK_INVOICE_IDENTIFY;
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put(RyConstant.HEADER_AUTHORIZATION, token);
        //组装参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sourceSystemCode", "TPM");
        jsonObject.put("interfaceCode", "TPM_INVOICE_OCR");
        List<JSONObject> lineList = Lists.newArrayList();
        for (InvoiceInputStreamFileVo fileVo : fileVoList) {
            JSONObject o = new JSONObject();
            o.put("vatAttId", fileVo.getFileCode());
            o.put("vatData", fileVo.getFileInputStream());
            lineList.add(o);
        }
        JSONObject ocr = new JSONObject();
        ocr.put("employeeNum", loginDetails.getAccount());
        ocr.put("lines", lineList);
        ocr.put("sourceSystem", "TPM");
        JSONObject requestData = new JSONObject();
        requestData.put("invoiceOcr", ocr);
        jsonObject.put("requestData", requestData);
        //组装请求参数放到日志
        JSONObject headJson = new JSONObject();
        headJson.put(RyConstant.HEADER_AUTHORIZATION, token);
        ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(jsonObject.toJSONString(), urlAddressVo);
        logDetailDto.setReqHead(headJson.toJSONString());
        logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf("/") + 1));
        logDetailDto.setRequestUri(interfaceAddress);
        logDetailDto.setMethodMsg("发票识别");
        externalLogVoService.addOrUpdateLog(logDetailDto, true);
        Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, jsonObject.toJSONString(), headMap);
        ExternalLogUtil.buildLogResult(logDetailDto, result);
        String data = null;
        String errMsg = null;
        try {
            Map<String, String> map = CostControlResultUtil.validateOcrResult(result);
            data = map.get(CostControlResultUtil.DATA);
            if (map.containsKey(CostControlResultUtil.ERR_MSG)) {
                errMsg = map.get(CostControlResultUtil.ERR_MSG);
            }
            logDetailDto.setStatus(ExternalLogGlobalConstants.S);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            logDetailDto.setTipMsg(e.getMessage());
            logDetailDto.setStatus(ExternalLogGlobalConstants.E);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
            throw e;
        }
        List<InvoiceIdentityVo> list = JSONObject.parseArray(data, InvoiceIdentityVo.class);
        Map<String, InvoiceInputStreamFileVo> fileMap = fileVoList.stream().collect(Collectors.toMap(InvoiceInputStreamFileVo::getFileCode, Function.identity()));
        List<InvoiceDto> dtoList = list.stream()
                //过滤掉识别返回信息是空的
                .filter(x -> ObjectUtils.isEmpty(x.getMessage()))
                .map(x -> {
                    InvoiceDto dto = nebulaToolkitService.copyObjectByBlankList(x, InvoiceDto.class, HashSet.class, ArrayList.class);
                    if (fileMap.containsKey(x.getFileCode())) {
                        InvoiceInputStreamFileVo fileVo = fileMap.get(x.getFileCode());
                        dto.setFileName(fileVo.getFileName());
                        dto.setFileUrl(fileVo.getFileUrl());
                    }
                    dto.setSyncFlag(BooleanEnum.TRUE.getCapital());
                    dto.setChecked(BooleanEnum.TRUE.getCapital());
                    dto.setAmountWithoutTax(dto.getPriceAndTax().subtract(dto.getTaxAmount()));
                    //  【发票池】发票“购买方纳税人识别号”取OCR识别返回的字段，不要从数据字典取
                    dto.setPNo(x.getPNo());
                    for (InvoiceDetailDto item : dto.getItems()) {
                        //计算价税合计
                        item.setPriceAndTax(item.getTaxAmount().add(item.getAmountWithoutTax()));
                        item.setTaxRate(item.getTaxRate().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_DOWN));
                    }
                    return dto;
                }).collect(Collectors.toList());
        for (InvoiceDto invoiceDto : dtoList) {
            //获取登陆信息
            PositionVo positionVo = positionVoService.findByPositionCode(loginDetails.getPostCode());
            if (ObjectUtils.isNotEmpty(positionVo)) {
                invoiceDto.setCompanyCode(ObjectUtils.isNotEmpty(positionVo.getSubCompanyId()) ? positionVo.getSubCompanyId() : null);
                invoiceDto.setAccEntityCode(ObjectUtils.isNotEmpty(positionVo.getCompanyCode()) ? positionVo.getCompanyCode() : null);
            }
            create(invoiceDto);
        }
        return errMsg;
    }


    private void createValidate(InvoiceDto dto) {
        this.validateBase(dto);
        this.validateType(dto);
        Validate.isTrue(StringUtils.isBlank(dto.getId()), "主键id不能有值");
        dto.setId(null);
        InvoiceVo dbInvoice = this.findByInvoiceNo(dto.getInvoiceNo());
        Validate.isTrue(dbInvoice == null, "发票号码重复，请检查");
    }

    private void updateValidate(InvoiceDto dto) {
        this.validateBase(dto);
        this.validateType(dto);
        Validate.notBlank(dto.getId(), "主键id必须有值");
    }

    private void validateBase(InvoiceDto dto) {
        Validate.notNull(dto, "对象信息不能为空！");
        Validate.notBlank(dto.getType(), "发票类型不能为空");
//        Validate.notBlank(dto.getCode(), "发票代码不能为空");
        Validate.notBlank(dto.getInvoiceNo(), "发票号码不能为空");
        Validate.notNull(dto.getPriceAndTax(), "税价合计不能为空");
//        this.validatePattern(dto.getCode(), "发票代码");
        this.validatePattern(dto.getInvoiceNo(), "发票号码");
    }

    private void validateForVatGeneral(InvoiceDto dto) {
        Validate.notNull(dto.getBillingDate(), "开票日期不能为空");
        Validate.notBlank(dto.getCheckCode(), "校验码不能为空");
        Validate.notNull(dto.getAmountWithoutTax(), "不含税金额不能为空");
        Validate.notNull(dto.getTaxAmount(), "税额不能为空");
        Validate.isTrue(dto.getTaxAmount().add(dto.getAmountWithoutTax()).compareTo(dto.getPriceAndTax()) == 0, "必须满足【税额 + 不含税金额 = 税价合计金额】");
        Validate.notBlank(dto.getPurchaser(), "购买方名称不能为空");
        Validate.notBlank(dto.getPNo(), "购买方税号不能为空");
        Validate.notBlank(dto.getSeller(), "销售方名称不能为空");
        Validate.notBlank(dto.getSNo(), "销售方税号不能为空");
        Validate.notBlank(dto.getSBankAndAccount(), "销售方开户行及账号不能为空");
        Validate.notBlank(dto.getSAddressAndPhone(), "销售方地址电话不能为空");
        this.validatePattern(dto.getPNo(), "购买方税号");
        this.validatePattern(dto.getSNo(), "销售方税号");
        Validate.isTrue(!StringUtils.equals(dto.getPNo(), dto.getSNo()), "购买方税号与销售方税号不能相同");
    }

    private void validatePattern(String value, String descrName) {
        Validate.matchesPattern(value, PATTERN, "%s只能是字母和数字构成", descrName);
    }

    private void validateType(InvoiceDto dto) {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(INVOICE_TYPE);
        List<String> dictCodes = dictDataVos.stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
        Validate.isTrue(dictCodes.contains(dto.getType()), "未知的发票类型【%s】，请检查", dto.getType());
//        InvoiceType invoiceType = InvoiceType.findByKey(dto.getType());
//        Validate.notNull(invoiceType, "根据提供的发票类型【%s】，未能找到相应信息", dto.getType());
//        switch (invoiceType) {
//            //定额发票(已在基础验证方法中验证)
//            case QUOTA:
//                break;
//            //增值税普通票
//            case VAT_GENERAL:
//                this.validateForVatGeneral(dto);
//                break;
//            //增值税专用票
//            case VAT_SPECIAL:
//                this.validateForVatGeneral(dto);
//                Validate.notBlank(dto.getPBankAndAccount(), "购买方开户行及账号不能为空");
//                Validate.notBlank(dto.getPAddressAndPhone(), "购买方地址电话不能为空");
//                break;
//            default:
//                throw new IllegalArgumentException("未知的发票类型【" + dto.getType() + "】，请检查");
//        }
    }
}
