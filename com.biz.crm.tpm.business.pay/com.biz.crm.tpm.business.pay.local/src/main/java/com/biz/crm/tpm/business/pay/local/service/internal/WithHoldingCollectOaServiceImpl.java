package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectProjectEnum;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.biz.crm.tpm.business.activities.sdk.enums.CostCenterFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketPlanCaseFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketingEstimationFieldsEnum;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.tpm.business.budget.sdk.enums.SecondCategoryEnum;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingCollect;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingCollectRepository;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingVoService;
import com.biz.crm.tpm.business.pay.sdk.enums.WithHoldingCollectFieldsEnum;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingCollectOaService;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingCollectVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.CostCenterDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.MarketPlanCaseDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.RegionCollectDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.WithHoldingCollectMainDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import liquibase.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class WithHoldingCollectOaServiceImpl implements WithHoldingCollectOaService {

    @Autowired(required = false)
    private WithHoldingCollectRepository withHoldingCollectRepository;

    @Autowired(required = false)
    private PushOaService pushOaService;

    @Autowired(required = false)
    private UserVoService userVoService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;

    @Value("${domain-name:}")
    private String domainName;

    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private BudgetSubjectsVoService budgetSubjectsVoService;

    @Autowired(required = false)
    private CostTypeCategoryVoService costTypeCategoryVoService;

    @Autowired(required = false)
    private WithHoldingVoService withHoldingVoService;

    /**
     * 推送OA
     *
     * @param order@return
     */
    @Override
    public JSONObject pushOa(WithHoldingCollectVo order) {
        order.setBusinessCode(order.getCollectCode());
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(order));

        List<WithHoldingVo> withHoldingList = withHoldingVoService.findByCollectCode(order.getCollectCode());
        Assert.notEmpty(withHoldingList, "计提明细不能为空");
        order.setWithHoldingList(withHoldingList);

        Set<String> orgCodeList = order.getWithHoldingList().stream().filter(e -> StringUtils.isNotBlank(e.getBearDepartmentCode())).map(e -> e.getBearDepartmentCode()).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            orderJsonObject.put("deptCode", String.join(",", oaOrgCodeSet));
        }

        List<RegionCollectMarketingEstimationVo> regionCollectMarketingEstimationVos = order.getMarketingEstimationList();
        regionCollectMarketingEstimationVos = Optional.ofNullable(regionCollectMarketingEstimationVos).orElse(Lists.newArrayList());
        Map<String, RegionCollectMarketingEstimationVo> marketingMap = regionCollectMarketingEstimationVos.stream()
                .collect(Collectors.toMap(RegionCollectMarketingEstimationVo::getProjectCode, v -> v, (a, b) -> a));
        RegionCollectMarketingEstimationVo estimationRevenue = marketingMap.get(RegionCollectProjectEnum.estimation_revenue.getCode());
        if (!CollectionUtils.isEmpty(regionCollectMarketingEstimationVos)) {

            RegionCollectMarketingEstimationVo costTotal = marketingMap.get(RegionCollectProjectEnum.marketing_cost_total.getCode());

            //计提费率=营销费用小计预计达成金额/预计收入预计达成金额
            BigDecimal withHoldingRatio = (estimationRevenue.getEstimateAmount() == null || estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO :
                    (costTotal.getEstimateAmount() == null ? BigDecimal.ZERO : costTotal.getEstimateAmount()).divide(estimationRevenue.getEstimateAmount(), 2, BigDecimal.ROUND_HALF_UP);
            orderJsonObject.put("withHoldingRatio", withHoldingRatio);
            //预算费率=营销费用小计预算达成金额/预计收入预算达成金额
            BigDecimal budgetRatio = (estimationRevenue.getBudgetAmount() == null || estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO :
                    (costTotal.getBudgetAmount() == null ? BigDecimal.ZERO : costTotal.getBudgetAmount()).divide(estimationRevenue.getBudgetAmount(), 2, BigDecimal.ROUND_HALF_UP);
            orderJsonObject.put("budgetRatio", budgetRatio);
        }

//        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
//        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        orderJsonObject.put("title", "费用计提：" + order.getYearMonthLy() + "-" + order.getOrgName() + "-营销方案计提");

        // 主表
        // 业务类型
        String businessType = MqConstant.TPM_WITH_HOLDING_COLLECT_OA;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.WITH_HOLDING_TOTAL_APPROVEFORM.getUrlCode() + "?code=" + order.getCollectCode());

        List<String> projectCodeList = regionCollectMarketingEstimationVos.stream().map(RegionCollectMarketingEstimationVo::getProjectCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListByCategoryCodes(projectCodeList);
        Map<String, String> budgetSubjectsCategoryCodeMap = costTypeCategoryVos.stream().filter(k -> StringUtil.isNotEmpty(k.getBudgetSubjectsCode()))
                .collect(Collectors.toMap(CostTypeCategoryVo::getBudgetSubjectsCode, CostTypeCategoryVo::getCategoryCode, (n, o) -> n));
        Set<String> categoryCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getCategoryCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        Set<String> budgetSubjectsCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getBudgetSubjectsCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        List<BudgetSubjectsVo> budgetSubjectsVos = budgetSubjectsVoService.findByCodes(budgetSubjectsCodeSet);
        List<RegionCollectDetailDto> detailDtoList = (List<RegionCollectDetailDto>) nebulaToolkitService.copyCollectionByBlankList(
                regionCollectMarketingEstimationVos.stream().filter(k -> StringUtil.isNotEmpty(k.getProjectCode()))
                        .filter(k -> !categoryCodeSet.contains(k.getProjectCode())
                                && !Arrays.asList(RegionCollectProjectEnum.production_cost.getCode(),
                                RegionCollectProjectEnum.product_transport_cost.getCode(),
                                RegionCollectProjectEnum.periphery_transport.getCode(),
                                RegionCollectProjectEnum.marginal_contribution.getCode(),
                                RegionCollectProjectEnum.gift_cost.getCode()).contains(k.getProjectCode())).collect(Collectors.toList()),
                RegionCollectMarketingEstimationVo.class, RegionCollectDetailDto.class, LinkedHashSet.class, ArrayList.class);
        //过滤搭赠费用，并且把搭赠费用加到“促销类”里面
        List<RegionCollectMarketingEstimationVo> giftCost = regionCollectMarketingEstimationVos.stream().filter(k -> StringUtil.isNotEmpty(k.getProjectCode()))
                .filter(k -> k.getProjectCode().equals(RegionCollectProjectEnum.gift_cost.getCode())).collect(Collectors.toList());
        AtomicInteger index = new AtomicInteger(20);
        budgetSubjectsVos.stream().filter(k -> StringUtil.isNotEmpty(k.getParentBudgetSubjectsCode()))
                .collect(Collectors.groupingBy(BudgetSubjectsVo::getParentBudgetSubjectsCode))
                .forEach((parentCode, childList) -> {
                    RegionCollectDetailDto detailDto = new RegionCollectDetailDto();
                    AtomicReference<BigDecimal> budgetAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> estimateAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> differenceAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    childList.forEach(k -> {
                        String categoryCode = budgetSubjectsCategoryCodeMap.getOrDefault(k.getBudgetSubjectsCode(), "");
                        RegionCollectMarketingEstimationVo estimationVo = marketingMap.get(categoryCode);
                        if (Objects.isNull(estimationVo)) {
                            return;
                        }
                        if (Objects.nonNull(estimationVo.getBudgetAmount())) {
                            budgetAmountTotal.set(estimationVo.getBudgetAmount().add(budgetAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getEstimateAmount())) {
                            estimateAmountTotal.set(estimationVo.getEstimateAmount().add(estimateAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getDifferenceAmount())) {
                            differenceAmountTotal.set(estimationVo.getDifferenceAmount().add(differenceAmountTotal.get()));
                        }
                    });

                    if (SecondCategoryEnum.promotion.getCode().equals(parentCode)) {
                        detailDto.setEstimateAmount(estimateAmountTotal.get().add(giftCost.get(0).getEstimateAmount()));
                    } else {
                        detailDto.setEstimateAmount(estimateAmountTotal.get());
                    }
                    detailDto.setBudgetAmount(budgetAmountTotal.get());
                    detailDto.setDifferenceAmount(differenceAmountTotal.get());
                    if (Objects.nonNull(estimationRevenue)) {
                        if (Objects.nonNull(estimationRevenue.getBudgetAmount())
                                && estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setBudgetRatio(detailDto.getBudgetAmount().divide(estimationRevenue.getBudgetAmount(), 2, RoundingMode.HALF_UP));
                        }
                        if (Objects.nonNull(estimationRevenue.getEstimateAmount())
                                && estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setEstimateRatio(detailDto.getEstimateAmount().divide(estimationRevenue.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                        }
                    }
                    if (Objects.nonNull(detailDto.getEstimateAmount())
                            && Objects.nonNull(detailDto.getDifferenceAmount())
                            && detailDto.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                        detailDto.setDifferenceRatio(detailDto.getDifferenceAmount().divide(detailDto.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                    }
                    detailDto.setProjectCode(parentCode);
                    detailDto.setProjectName(childList.get(0).getParentBudgetSubjectsName());
                    detailDto.setSort(index.getAndIncrement());
                    detailDtoList.add(detailDto);
                });
        detailDtoList.forEach(detailDto -> {
            if (Objects.isNull(detailDto.getSort())) {
                RegionCollectProjectEnum collectProjectEnum = RegionCollectProjectEnum.findByCode(detailDto.getProjectCode());
                if (Objects.nonNull(collectProjectEnum)) {
                    detailDto.setSort(collectProjectEnum.getSort());
                } else {
                    detailDto.setSort(index.getAndIncrement());
                }
            }
        });
        List<RegionCollectDetailDto> detailSendDtoList = detailDtoList.stream().sorted(Comparator.comparingInt(RegionCollectDetailDto::getSort)).collect(Collectors.toList());
        List<JSONArray> details = Lists.newArrayList();
        details.add(JSONArray.parseArray(JSON.toJSONString(detailSendDtoList)));

        //查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
            order.getWithHoldingList().stream().filter(x -> entry.getValue().contains(x.getCostTypeCategoryCode()))
                    .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
        }
        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        Set<String> bearDepartmentNameSet = new HashSet<>();
        order.getWithHoldingList().stream().filter(k -> StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                .collect(Collectors.groupingBy(e -> e.getCostCenterCode() + e.getCostTypeCategorySecondName(), Collectors.toList())).forEach((key, list) -> {
                    CostCenterDetailDto detailDto = new CostCenterDetailDto();
                    detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
                    detailDto.setCostCenterName(list.get(0).getCostCenterName());
                    List<OrgOaOrgVo> orgOaOrgVos = orgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
                    if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                        bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
                    } else {
                        detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
                    }
                    detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
                    detailDto.setBearAmount(list.stream().map(WithHoldingVo::getActualReportAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                    costCenterDetailDtoList.add(detailDto);
                });
        Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);
        details.add(JSONArray.parseArray(JSON.toJSONString(JSONArray.parseArray(JSON.toJSONString(costCenterDetailDtoList)))));

        List<MarketPlanCaseDetailDto> marketPlanCaseDetailDtoList = Lists.newArrayList();
        order.getWithHoldingList().forEach(e -> {
            MarketPlanCaseDetailDto detailDto = new MarketPlanCaseDetailDto();
            detailDto.setFysybm(e.getBelongDepartmentName());
            detailDto.setKhmc(e.getCustomerName());
            detailDto.setMdmc(e.getTerminalName());
            detailDto.setFyxm(e.getCostTypeDetailName());
            detailDto.setFygzyf(e.getYears());
            detailDto.setGbjtsjje(e.getActualReportAmount());
            detailDto.setFaghmc(e.getActivitiesName());
            detailDto.setHdms(e.getActDesc());
            detailDto.setYwbm(e.getBusinessCode());
            detailDto.setCbzx(e.getCostCenterName());
            detailDto.setXzjghtybzs(orgOaOrgVoMap.get(e.getBearDepartmentCode()).get(0).getOaOrgCode());
            marketPlanCaseDetailDtoList.add(detailDto);
        });
        details.add(JSONArray.parseArray(JSON.toJSONString(marketPlanCaseDetailDtoList)));

        JSONObject response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, details, workflowId, workflowName,
                mainTableMethod, detailTableMethod, detailTableMethod1, detailTableMethod2);

        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        if (response.containsKey("code")) {
            Integer resultCode = response.getInteger("code");
            if (resultCode == 100) {
                JSONObject ja = response.getJSONObject("data");
                WithHoldingCollect entity = withHoldingCollectRepository.findByCode(order.getCollectCode());
                entity.setProcessKey(ja.getString("out"));
                entity.setProcessDate(new Date());
                entity.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
                entity.setOaId(response.getString("oaId"));
                entity.setOaUserName(response.getString("oaUserName"));
                withHoldingCollectRepository.updateById(entity);
            } else {
                Validate.isTrue(false, "OA流程提交失败，错误信息：" + response.getString("msg"));
            }
        } else {
            Validate.isTrue(false, "OA流程提交失败，错误信息：" + response.getString("msg"));
        }

        return response;
    }

    /**
     * 重新提交OA
     *
     * @param order@return
     */
    @Override
    public JSONObject resubmitOa(WithHoldingCollectVo order) {
        order.setBusinessCode(order.getCollectCode());
        OaResubmitDto dto = new OaResubmitDto();

        // 获取当前登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType = MqConstant.TPM_WITH_HOLDING_COLLECT_OA;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        order.setTpmUrl(domainName + TpmOaPageEnum.WITH_HOLDING_TOTAL_APPROVEFORM.getUrlCode() + "?code=" + order.getCollectCode());

        List<WithHoldingVo> withHoldingList = withHoldingVoService.findByCollectCode(order.getCollectCode());
        Assert.notEmpty(withHoldingList, "计提明细不能为空");
        order.setWithHoldingList(withHoldingList);

        Set<String> orgCodeList = order.getWithHoldingList().stream().filter(e -> StringUtils.isNotBlank(e.getBearDepartmentCode())).map(e -> e.getBearDepartmentCode()).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            order.setDeptCode(String.join(",", oaOrgCodeSet));
        }
        List<RegionCollectMarketingEstimationVo> regionCollectMarketingEstimationVos = order.getMarketingEstimationList();
        regionCollectMarketingEstimationVos = Optional.ofNullable(regionCollectMarketingEstimationVos).orElse(Lists.newArrayList());
        Map<String, RegionCollectMarketingEstimationVo> marketingMap = regionCollectMarketingEstimationVos.stream()
                .collect(Collectors.toMap(RegionCollectMarketingEstimationVo::getProjectCode, v -> v, (a, b) -> a));
        RegionCollectMarketingEstimationVo estimationRevenue = marketingMap.get(RegionCollectProjectEnum.estimation_revenue.getCode());
        if (!CollectionUtils.isEmpty(regionCollectMarketingEstimationVos)) {
            RegionCollectMarketingEstimationVo costTotal = marketingMap.get(RegionCollectProjectEnum.marketing_cost_total.getCode());
            //计提费率=营销费用小计预计达成金额/预计收入预计达成金额
            BigDecimal withHoldingRatio = (estimationRevenue.getEstimateAmount() == null || estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO :
                    (costTotal.getEstimateAmount() == null ? BigDecimal.ZERO : costTotal.getEstimateAmount()).divide(estimationRevenue.getEstimateAmount(), 2, BigDecimal.ROUND_HALF_UP);
            order.setWithHoldingRatio(withHoldingRatio);
            //预算费率=营销费用小计预算达成金额/预计收入预算达成金额
            BigDecimal budgetRatio = (estimationRevenue.getBudgetAmount() == null || estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO :
                    (costTotal.getBudgetAmount() == null ? BigDecimal.ZERO : costTotal.getBudgetAmount()).divide(estimationRevenue.getBudgetAmount(), 2, BigDecimal.ROUND_HALF_UP);
            order.setBudgetRatio(budgetRatio);
        }

        dto.setBusinessCode(businessType);
        dto.setRequestId(order.getProcessKey());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        WithHoldingCollectMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(order, WithHoldingCollectMainDto.class, LinkedHashSet.class, ArrayList.class);
        dto.setRequestName("费用计提：" + order.getYearMonthLy() + "-" + order.getOrgName() + "-营销方案计提");
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));

        List<String> projectCodeList = regionCollectMarketingEstimationVos.stream().map(RegionCollectMarketingEstimationVo::getProjectCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListByCategoryCodes(projectCodeList);
        Set<String> categoryCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getCategoryCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        Map<String, String> budgetSubjectsCategoryCodeMap = costTypeCategoryVos.stream().filter(k -> StringUtil.isNotEmpty(k.getBudgetSubjectsCode()))
                .collect(Collectors.toMap(CostTypeCategoryVo::getBudgetSubjectsCode, CostTypeCategoryVo::getCategoryCode, (n, o) -> n));
        Set<String> budgetSubjectsCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getBudgetSubjectsCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        List<BudgetSubjectsVo> budgetSubjectsVos = budgetSubjectsVoService.findByCodes(budgetSubjectsCodeSet);
        List<RegionCollectDetailDto> detailDtoList = (List<RegionCollectDetailDto>) nebulaToolkitService.copyCollectionByBlankList(
                regionCollectMarketingEstimationVos.stream().filter(k -> StringUtil.isNotEmpty(k.getProjectCode()))
                        .filter(k -> !categoryCodeSet.contains(k.getProjectCode())).collect(Collectors.toList()),
                RegionCollectMarketingEstimationVo.class, RegionCollectDetailDto.class, LinkedHashSet.class, ArrayList.class);
        AtomicInteger index = new AtomicInteger(20);
        budgetSubjectsVos.stream().filter(k -> StringUtil.isNotEmpty(k.getParentBudgetSubjectsCode()))
                .collect(Collectors.groupingBy(BudgetSubjectsVo::getParentBudgetSubjectsCode))
                .forEach((parentCode, childList) -> {
                    RegionCollectDetailDto detailDto = new RegionCollectDetailDto();
                    detailDto.setProjectName(childList.get(0).getParentBudgetSubjectsName());
                    AtomicReference<BigDecimal> budgetAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> estimateAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> differenceAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    childList.forEach(k -> {
                        String categoryCode = budgetSubjectsCategoryCodeMap.getOrDefault(k.getBudgetSubjectsCode(), "");
                        RegionCollectMarketingEstimationVo estimationVo = marketingMap.get(categoryCode);
                        if (Objects.isNull(estimationVo)) {
                            return;
                        }
                        if (Objects.nonNull(estimationVo.getBudgetAmount())) {
                            budgetAmountTotal.set(estimationVo.getBudgetAmount().add(budgetAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getEstimateAmount())) {
                            estimateAmountTotal.set(estimationVo.getEstimateAmount().add(estimateAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getDifferenceAmount())) {
                            differenceAmountTotal.set(estimationVo.getDifferenceAmount().add(differenceAmountTotal.get()));
                        }
                    });

                    detailDto.setBudgetAmount(budgetAmountTotal.get());
                    detailDto.setEstimateAmount(estimateAmountTotal.get());
                    detailDto.setDifferenceAmount(differenceAmountTotal.get());
                    if (Objects.nonNull(estimationRevenue)) {
                        if (Objects.nonNull(estimationRevenue.getBudgetAmount())
                                && estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setBudgetRatio(detailDto.getBudgetAmount().divide(estimationRevenue.getBudgetAmount(), 2, RoundingMode.HALF_UP));
                        }
                        if (Objects.nonNull(estimationRevenue.getEstimateAmount())
                                && estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setEstimateRatio(detailDto.getEstimateAmount().divide(estimationRevenue.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                        }
                    }
                    if (Objects.nonNull(detailDto.getEstimateAmount())
                            && Objects.nonNull(detailDto.getDifferenceAmount())
                            && detailDto.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                        detailDto.setDifferenceRatio(detailDto.getDifferenceAmount().divide(detailDto.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                    }
                    detailDto.setProjectCode(parentCode);
                    detailDto.setProjectName(childList.get(0).getParentBudgetSubjectsName());
                    detailDto.setSort(index.getAndIncrement());
                    detailDtoList.add(detailDto);
                });
        detailDtoList.forEach(detailDto -> {
            if (Objects.isNull(detailDto.getSort())) {
                RegionCollectProjectEnum collectProjectEnum = RegionCollectProjectEnum.findByCode(detailDto.getProjectCode());
                if (Objects.nonNull(collectProjectEnum)) {
                    detailDto.setSort(collectProjectEnum.getSort());
                } else {
                    detailDto.setSort(index.getAndIncrement());
                }
            }
        });
        List<RegionCollectDetailDto> detailSendDtoList = detailDtoList.stream().sorted(Comparator.comparingInt(RegionCollectDetailDto::getSort)).collect(Collectors.toList());

        //查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
            order.getWithHoldingList().stream().filter(x -> entry.getValue().contains(x.getCostTypeCategoryCode()))
                    .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
        }
        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        Set<String> bearDepartmentNameSet = new HashSet<>();
        order.getWithHoldingList().stream().filter(k -> StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                .collect(Collectors.groupingBy(e -> e.getCostCenterCode() + e.getCostTypeCategorySecondName(), Collectors.toList())).forEach((key, list) -> {
                    CostCenterDetailDto detailDto = new CostCenterDetailDto();
                    detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
                    detailDto.setCostCenterName(list.get(0).getCostCenterName());
                    List<OrgOaOrgVo> orgOaOrgVos = orgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
                    if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                        bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
                    } else {
                        detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
                    }
                    detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
                    detailDto.setBearAmount(list.stream().map(WithHoldingVo::getActualReportAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                    costCenterDetailDtoList.add(detailDto);
                });
        Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);

        List<MarketPlanCaseDetailDto> marketPlanCaseDetailDtoList = Lists.newArrayList();
        order.getWithHoldingList().forEach(e -> {
            MarketPlanCaseDetailDto detailDto = new MarketPlanCaseDetailDto();
            detailDto.setFysybm(e.getBelongDepartmentName());
            detailDto.setKhmc(e.getCustomerName());
            detailDto.setMdmc(e.getTerminalName());
            detailDto.setFyxm(e.getCostTypeDetailName());
            detailDto.setFygzyf(e.getYears());
            detailDto.setGbjtsjje(e.getActualReportAmount());
            detailDto.setFaghmc(e.getActivitiesName());
            detailDto.setHdms(e.getActDesc());
            detailDto.setYwbm(e.getBusinessCode());
            detailDto.setCbzx(e.getCostCenterName());
            detailDto.setXzjghtybzs(orgOaOrgVoMap.get(e.getBearDepartmentCode()).get(0).getOaOrgCode());
            marketPlanCaseDetailDtoList.add(detailDto);
        });

        OaDetailDto oaDetailDto = new OaDetailDto();
        oaDetailDto.setDetailList(JSONUtil.toJsonStr(detailSendDtoList));
        oaDetailDto.setDetailClass(RegionCollectDetailDto.class);

        OaDetailDto costCenterDetailDto = new OaDetailDto();
        costCenterDetailDto.setDetailList(JSONUtil.toJsonStr(costCenterDetailDtoList));
        costCenterDetailDto.setDetailClass(CostCenterDetailDto.class);

        OaDetailDto marketPlanCaseDetailDto = new OaDetailDto();
        marketPlanCaseDetailDto.setDetailList(JSONUtil.toJsonStr(marketPlanCaseDetailDtoList));
        marketPlanCaseDetailDto.setDetailClass(MarketPlanCaseDetailDto.class);

        dto.setDetailList(Arrays.asList(oaDetailDto, costCenterDetailDto, marketPlanCaseDetailDto));

        if (ryOaProcessService.resubmit(dto)) {
            WithHoldingCollect entity = withHoldingCollectRepository.findByCode(order.getCollectCode());
            entity.setProcessDate(new Date());
            entity.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
            withHoldingCollectRepository.updateById(entity);
        } else {
            Validate.isTrue(false, "OA流程提交失败");
        }
        return null;
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public boolean oaWithdraw(String code, String remark) {
        WithHoldingCollect entity = withHoldingCollectRepository.findByCode(code);
        Validate.isTrue(entity.getStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(entity.getProcessKey()));
        dto.setProcessCreateId(entity.getOaId());
        dto.setUserName(entity.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (WithHoldingCollectFieldsEnum value : WithHoldingCollectFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod = (tableDetailFields, detailJsonObject) -> {
        for (MarketingEstimationFieldsEnum value : MarketingEstimationFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };

    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod1 = (tableDetailFields, detailJsonObject) -> {
        for (CostCenterFieldsEnum value : CostCenterFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod2 = (tableDetailFields, detailJsonObject) -> {
        for (MarketPlanCaseFieldsEnum value : MarketPlanCaseFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
}
