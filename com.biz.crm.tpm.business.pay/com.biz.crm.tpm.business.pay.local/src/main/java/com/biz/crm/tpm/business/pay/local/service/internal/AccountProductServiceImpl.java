package com.biz.crm.tpm.business.pay.local.service.internal;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.repository.AccountProductRepository;
import com.biz.crm.tpm.business.pay.local.service.AccountProductService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 费用上账商品表(TpmAccountProduct)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-16 19:11:50
 */
@Service
public class AccountProductServiceImpl implements AccountProductService {

  @Autowired
  private AccountProductRepository accountProductRepository;

  /**
   * 分页查询数据
   *
   * @param pageable          分页对象
   * @param tpmAccountProduct 实体对象
   * @return
   */
  @Override
  public Page<AccountProduct> findByConditions(Pageable pageable, AccountProduct tpmAccountProduct) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(tpmAccountProduct)) {
      tpmAccountProduct = new AccountProduct();
    }
    return this.accountProductRepository.findByConditions(pageable, tpmAccountProduct);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public AccountProduct findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    return this.accountProductRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
  }

  @Override
  public List<AccountProduct> findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    return this.accountProductRepository.findByCode(code);
  }

  /**
   * 新增数据
   *
   * @param tpmAccountProduct 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AccountProduct create(AccountProduct tpmAccountProduct) {
    this.createValidate(tpmAccountProduct);
    tpmAccountProduct.setTenantCode(TenantUtils.getTenantCode());
    this.accountProductRepository.saveOrUpdate(tpmAccountProduct);
    return tpmAccountProduct;
  }

  /**
   * 批量新增
   *
   * @param accountProducts 实体对象
   */
  @Override
  public void createBatch(List<AccountProduct> accountProducts) {
    Validate.notEmpty(accountProducts, "新增时，数据对象不能为空");
    for (AccountProduct accountProduct : accountProducts) {
      this.createValidate(accountProduct);
      accountProduct.setTenantCode(TenantUtils.getTenantCode());
    }
    this.accountProductRepository.saveBatch(accountProducts);
  }

  /**
   * 修改新据
   *
   * @param tpmAccountProduct 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public AccountProduct update(AccountProduct tpmAccountProduct) {
    this.updateValidate(tpmAccountProduct);
    tpmAccountProduct.setTenantCode(TenantUtils.getTenantCode());
    this.accountProductRepository.saveOrUpdate(tpmAccountProduct);
    return tpmAccountProduct;
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(List<String> idList) {
    Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
    this.accountProductRepository.removeByIdsAndTenantCode(idList,TenantUtils.getTenantCode());
  }

  /**
   * 通过编码进行逻辑删除
   *
   * @param codes
   */
  @Override
  public void deleteBatchByCodes(List<String> codes) {
    Validate.isTrue(!CollectionUtils.isEmpty(codes), "删除数据时，上账编码集合不能为空！");
    this.accountProductRepository.deleteBatchByCodes(codes);
  }

  @Override
  public List<AccountProduct> findByCodes(List<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return new ArrayList<>(0);
    }
    return this.accountProductRepository.findByCodes(codes);
  }

  /**
   * 创建验证
   *
   * @param tpmAccountProduct
   */
  private void createValidate(AccountProduct tpmAccountProduct) {
    tpmAccountProduct.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    tpmAccountProduct.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    tpmAccountProduct.setTenantCode(TenantUtils.getTenantCode());
    Validate.notNull(tpmAccountProduct, "新增时，对象信息不能为空！");
    tpmAccountProduct.setId(null);
    Validate.notBlank(tpmAccountProduct.getAccountCode(), "新增数据时，费用上账编码 不能为空！");
    Validate.notBlank(tpmAccountProduct.getProductCode(), "新增数据时，商品编码 不能为空！");
    Validate.notBlank(tpmAccountProduct.getProductName(), "新增数据时，商品名称 不能为空！");

  }

  /**
   * 修改验证
   *
   * @param tpmAccountProduct
   */
  private void updateValidate(AccountProduct tpmAccountProduct) {
    tpmAccountProduct.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    tpmAccountProduct.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    Validate.notNull(tpmAccountProduct, "修改时，对象信息不能为空！");
    Validate.notBlank(tpmAccountProduct.getId(), "修改数据时，ID不能为空！");
    Validate.notBlank(tpmAccountProduct.getAccountCode(), "修改数据时，费用上账编码 不能为空！");
    Validate.notBlank(tpmAccountProduct.getProductCode(), "修改数据时，商品编码 不能为空！");
    Validate.notBlank(tpmAccountProduct.getProductName(), "修改数据时，商品名称 不能为空！");
  }
}

