package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordItemDto;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayDetailRecordService;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordItemVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Api(tags = "预付明细跟踪")
@RestController
@RequestMapping("/v1/pay/activityPrepayDetailRecord")
@Slf4j
public class ActivityPrepayDetailRecordController {

    @Autowired(required = false)
    private ActivityPrepayDetailRecordService activityPrepayDetailRecordService;


    @ApiOperation(value = "查询分页列表")
    @GetMapping("findListByCondition")
    public Result<Page<ActivityPrepayDetailRecordVo>> findListByCondition(@PageableDefault(50) Pageable pageable, ActivityPrepayDetailRecordDto dto) {
        return Result.ok(activityPrepayDetailRecordService.findListByCondition(pageable, dto));
    }


    /**
     * 通过供应商编码查询待冲销明细
     *
     * @param
     * @return
     */
    @ApiOperation(value = "通过供应商编码查询待冲销明细")
    @PostMapping("findByPayeeCode")
    public Result<Page<ActivityPrepayDetailRecordVo>> findByPayeeCode(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                      @ApiParam(name = "dto", value = "对象信息") @RequestBody ActivityPrepayDetailRecordDto dto) {
        try {
            Page<ActivityPrepayDetailRecordVo> list = this.activityPrepayDetailRecordService.findByPayeeCode(pageable, dto);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过供应商编码列表查询待冲销明细
     *
     * @param payeeCodes 供应商编码
     * @return
     */
    @ApiOperation(value = "通过供应商编码列表查询待冲销明细")
    @PostMapping("findByPayeeCodes")
    public Result<Page<ActivityPrepayDetailRecordVo>> findByPayeeCodes(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                       @RequestBody List<String> payeeCodes) {
        try {
            Page<ActivityPrepayDetailRecordVo> list =
                    this.activityPrepayDetailRecordService.findByPayeeCodes(pageable, payeeCodes);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过活动明细编码查询待冲销明细
     *
     * @param
     * @return
     */
    @ApiOperation(value = "通过活动明细编码查询待冲销明细")
    @PostMapping("findBySchemeDetailCode")
    public Result<List<ActivityPrepayDetailRecordVo>> findBySchemeDetailCode(@ApiParam(name = "dto", value = "请求参数") @RequestBody ActivityPrepayDetailRecordDto dto) {
        try {
            List<ActivityPrepayDetailRecordVo> list = this.activityPrepayDetailRecordService.findBySchemeDetailCode(dto);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过活动明细编码实时查询结转、冲销明细
     *
     * @param
     * @return
     */
    @ApiOperation(value = "通过活动明细编码实时查询结转、冲销明细")
    @GetMapping("findItemByPrepayDetailCode")
    public Result<List<ActivityPrepayDetailRecordItemVo>> findItemByPrepayDetailCode(@ApiParam(name = "code", value = "预付明细编码", required = true) String code) {
        try {
            List<ActivityPrepayDetailRecordItemVo> list = this.activityPrepayDetailRecordService.findItemByPrepayDetailCode(code);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过预付明细编码查询
     *
     * @param code 供应商编码
     * @return
     */
    @ApiOperation(value = "通过预付明细编码查询")
    @GetMapping("findByCode")
    public Result<ActivityPrepayDetailRecordVo> findByCode(@ApiParam(name = "code", value = "预付明细编码", required = true) String code) {
        try {
            ActivityPrepayDetailRecordVo vo = this.activityPrepayDetailRecordService.findByCode(code);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "活动预付新增编辑计算")
    @PostMapping("cal")
    public Result<ActivityPrepayVo> cal(@RequestBody ActivityPrepayVo vo) {
        try {
            return Result.ok(this.activityPrepayDetailRecordService.cal(vo));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询所有数据")
    @GetMapping("findItemByConditions")
    public Result<Page<ActivityPrepayDetailRecordItemVo>> findItemByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                               @ApiParam(name = "Account", value = "活动预付明细跟踪明细Dto") ActivityPrepayDetailRecordItemDto dto) {
        try {
            Page<ActivityPrepayDetailRecordItemVo> page = this.activityPrepayDetailRecordService.findItemByConditions(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据活动明细编码集统计已预付金额")
    @PostMapping("findprepayAmount")
    public Result<Map<String, BigDecimal>> findprepayAmount(@RequestBody List<String> schemeDetailCodes) {
        try {
            Map<String, BigDecimal> result = this.activityPrepayDetailRecordService.findprepayAmount(schemeDetailCodes);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @PostMapping("findAlreadyPrepayAmount")
    @ApiOperation(value = "根据活动明细编码查询已预付金额")
    public Result<Map<String, BigDecimal>> findAlreadyPrepayAmount(@RequestBody ActivityPrepayVo vo) {
        return Result.ok(activityPrepayDetailRecordService.findAlreadyPrepayAmount(vo));
    }
}
