package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingWriteOffVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用冲销(WithHoldingWriteOff)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-15 15:30:18
 */
public interface WithHoldingWriteOffMapper extends BaseMapper<WithHoldingWriteOff> {

    /**
     * 更新凭证信息
     *
     * @param dtoList
     */
    void hecVoucherCallback(@Param("dtoList") List<HecCallbackDto> dtoList);

    List<WithHoldingWriteOff> findByUniqueKeys(@Param("uniqueKeys") List<String> uniqueKeys);

    List<WithHoldingWriteOff> findCashAndOrderWriteOff(@Param("businessCodes") List<String> businessCodes, @Param("monthList") List<String> monthList);

    List<WithHoldingWriteOff> findCloseWriteOff(@Param("businessCodes") List<String> businessCodes, @Param("monthList") List<String> monthList);
}

