<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.WithHoldingWriteOffMapper">

    <select id="hecVoucherCallback">
        <foreach item="dto" collection="dtoList" index="index"  open="" separator=";" close="">
            update tpm_with_holding_write_off
            set voucher_code = #{dto.orderCode}
            where write_off_code = #{dto.businessCode}
        </foreach>
    </select>

    <select id="findByUniqueKeys" resultType="com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff">
        select * from tpm_with_holding_write_off
        where rule_code in
        <foreach collection="uniqueKeys" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="findCashAndOrderWriteOff"
            resultType="com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff">
        SELECT
        t.*
        FROM
        tpm_with_holding_write_off t
        LEFT JOIN tpm_marketing_plan_case m ON t.activities_detail_code = m.scheme_detail_code
        WHERE
        t.tenant_code = 'default'
        AND t.del_flag = '009'
        and m.case_type = 'BDMB0006'
        AND t.write_off_type in ('cash','order')
        AND t.manage_report_write_off_status = 'wait_write_off'
        <if test="businessCodes != null and businessCodes.size()>0">
        AND t.business_code in
        <foreach collection="businessCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        </if>
        <if test="monthList != null and monthList.size()>0">
        AND t.write_off_years in
        <foreach collection="monthList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        </if>
    </select>
    <select id="findCloseWriteOff" resultType="com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff">
        SELECT
        t.*
        FROM
        tpm_with_holding_write_off t
        LEFT JOIN tpm_marketing_plan_case m ON t.activities_detail_code = m.scheme_detail_code
        WHERE
        t.tenant_code = 'default'
        AND t.del_flag = '009'
        and m.case_type = 'BDMB0006'
        AND t.write_off_type = 'close'
        AND t.manage_report_write_off_status = 'wait_write_off'
        <if test="businessCodes != null and businessCodes.size()>0">
        AND t.business_code in
        <foreach collection="businessCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        </if>
        <if test="monthList != null and monthList.size()>0">
        AND t.write_off_years in
        <foreach collection="monthList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        </if>
    </select>
</mapper>

