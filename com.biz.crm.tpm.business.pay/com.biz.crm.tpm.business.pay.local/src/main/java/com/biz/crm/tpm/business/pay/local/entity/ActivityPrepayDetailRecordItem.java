package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.tpm.business.pay.sdk.enums.ActivityPrepayDetailRecordOperateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 实体：活动预付明细跟踪;
 *
 * <AUTHOR> yaoyongming
 * @date : 2024-6-5
 */
@ApiModel(value = "ActivityPrepayDetailRecordItem", description = "活动预付明细跟踪明细")
@TableName("tpm_activity_prepay_detail_record_item")
@Getter
@Setter
@Entity(name = "tpm_activity_prepay_detail_record_item")
@org.hibernate.annotations.Table(appliesTo = "tpm_activity_prepay_detail_record_item", comment = "活动预付明细跟踪明细")
@Table(name = "tpm_activity_prepay_detail_record_item", indexes = {
        @Index(name = "activity_prepay_detail_record_idx1", columnList = "prepay_code"),
        @Index(name = "activity_prepay_detail_record_idx2", columnList = "prepay_detail_code"),
        @Index(name = "activity_prepay_detail_record_idx3", columnList = "scheme_code"),
        @Index(name = "activity_prepay_detail_record_idx4", columnList = "scheme_detail_code")
})
public class ActivityPrepayDetailRecordItem extends TenantFlagOpEntity {

  @ApiModelProperty("预付编号")
  @Column(name = "prepay_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付编号 '")
  private String prepayCode;

  @ApiModelProperty("预付名称")
  @Column(name = "prepay_name", columnDefinition = "varchar(128) comment '预付名称'")
  private String prepayName;

  @ApiModelProperty("预付明细编号")
  @Column(name = "prepay_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付明细编号 '")
  private String prepayDetailCode;

  @ApiModelProperty("方案编码")
  @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
  private String schemeCode;

  @ApiModelProperty("方案名称")
  @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称'")
  private String schemeName;

  @ApiModelProperty("方案明细编码")
  @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
  private String schemeDetailCode;

  /**
   * @see ActivityPrepayDetailRecordOperateTypeEnum
   */
  @ApiModelProperty("类型")
  @Column(name = "type", columnDefinition = "varchar(32) comment '类型'")
  private String type;

  @ApiModelProperty("业务编码")
  @Column(name = "business_code", length = 128, columnDefinition = "VARCHAR(128) COMMENT '业务编码 '")
  private String businessCode;

  @ApiModelProperty("业务名称")
  @Column(name = "business_name", columnDefinition = "varchar(128) comment '业务名称'")
  private String businessName;

  @ApiModelProperty("金额")
  @Column(name = "amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '金额 '")
  private BigDecimal amount;
}