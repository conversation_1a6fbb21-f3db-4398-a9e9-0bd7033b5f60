package com.biz.crm.tpm.business.pay.local.register;

import com.biz.crm.tpm.business.pay.sdk.service.DeliveryReplenishmentPoolDetailService;
import com.bizunited.nebula.europa.database.sdk.strategy.ExternalQueryInterceptor;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteContent;
import com.bizunited.nebula.europa.sdk.context.matedata.MetaData;
import com.bizunited.nebula.europa.sdk.vo.EuropaInfoVo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import java.math.BigDecimal;
import java.util.*;

@Component
public class MarketingAuditDetailQueryInterceptor implements ExternalQueryInterceptor {

    @Autowired(required = false)
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;

    @Override
    public String code() {
        return "tpm_marketing_audit_detail_query_interceptor";
    }

    @Override
    public String name() {
        return "结案明细数据视图-参数补充";
    }

    @Override
    public List<Object[]> process(EntityManager entityManager, MetaData metaData, EuropaInfoVo europaInfoVo, ExecuteContent executeContent, String... strings) {
        List<Map<String, Object>> resutls = executeContent.getResults();
        if (CollectionUtils.isEmpty(resutls)) {
            return null;
        }
        if (0 == resutls.parallelStream().filter(row -> row != null).count()) {
            return Lists.newLinkedList();
        }
        List<Object[]> contents = Lists.newArrayList();

        List<String> auditDetailCodes = new ArrayList<>();
        resutls.forEach(item -> {
            String code = (String) item.get("audit_detail_code");
            auditDetailCodes.add(code);
        });
        Map<String, BigDecimal> map = deliveryReplenishmentPoolDetailService.findByAuditDetailCodes(auditDetailCodes);

        resutls.forEach(item -> {
            List<Object> data = Lists.newArrayList();
            String code = (String) item.get("audit_detail_code");
            BigDecimal auditAmount = (BigDecimal) (item.get("audit_amount") != null ? item.get("audit_amount") : BigDecimal.ZERO);
            BigDecimal cashAmount = (BigDecimal) (item.get("cash_amount") != null ? item.get("cash_amount") : BigDecimal.ZERO);
            BigDecimal closeAmount = map.getOrDefault(code, BigDecimal.ZERO);
            for (String fieldName : strings) {
                if (StringUtils.equals("closeAmount", fieldName)) {
                    data.add(closeAmount);
                } else if (StringUtils.equals("cashBalance", fieldName)) {
                    data.add((auditAmount.subtract(cashAmount).subtract(closeAmount)));
                } else {
                    data.add(item.get(fieldName));
                }
            }
            contents.add(data.toArray(new Object[]{}));
        });
        return contents;
    }
}
