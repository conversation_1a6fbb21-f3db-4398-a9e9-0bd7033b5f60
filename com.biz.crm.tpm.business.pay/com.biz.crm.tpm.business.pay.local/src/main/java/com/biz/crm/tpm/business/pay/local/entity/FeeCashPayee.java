package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "FeeCashPayee", description = "费用兑付收款明细")
@TableName("tpm_fee_cash_payee")
@Getter
@Setter
@Entity(name = "tpm_fee_cash_payee")
@org.hibernate.annotations.Table(appliesTo = "tpm_fee_cash_payee", comment = "费用兑付收款明细")
@Table(name = "tpm_fee_cash_payee", indexes = {
        @Index(name = "fee_cash_idx1", columnList = "cash_code"),
        @Index(name = "fee_cash_idx2", columnList = "line_code", unique = true),
})
public class FeeCashPayee extends TenantFlagOpEntity {

    @ApiModelProperty("兑付名称")
    @Column(name = "cash_name", columnDefinition = "VARCHAR(255) COMMENT '兑付名称 '")
    private String cashName;

    @ApiModelProperty("兑付编号")
    @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
    private String cashCode;

    @ApiModelProperty("核销明细编号")
    @Column(name = "audit_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销明细编号 '")
    private String auditDetailCode;

    @ApiModelProperty("收款方类型")
    @Column(name = "payee_type", columnDefinition = "varchar(32) comment '收款方类型'")
    private String payeeType;

    @ApiModelProperty("收款方编码")
    @Column(name = "payee_code", columnDefinition = "VARCHAR(64) COMMENT '收款方编码 '")
    private String payeeCode;

    @ApiModelProperty("供应商编码")
    @Column(name = "supplier_code", columnDefinition = "VARCHAR(64) COMMENT '供应商编码 '")
    private String supplierCode;

    @ApiModelProperty("收款方Erp编码")
    @Column(name = "payee_erp_code", columnDefinition = "VARCHAR(64) COMMENT '收款方Erp编码 '")
    private String payeeErpCode;

    @ApiModelProperty("供应商Erp编码")
    @Column(name = "supplier_erp_code", columnDefinition = "VARCHAR(64) COMMENT '供应商Erp编码 '")
    private String supplierErpCode;

    @ApiModelProperty("收款方名称")
    @Column(name = "payee_name", columnDefinition = "VARCHAR(255) COMMENT '收款方 '")
    private String payeeName;

    @ApiModelProperty("收款方账号")
    @Column(name = "payee_account", columnDefinition = "VARCHAR(255) COMMENT '收款方账号 '")
    private String payeeAccount;

    @ApiModelProperty("户名")
    @Column(name = "account_name", columnDefinition = "VARCHAR(255) COMMENT '收款方账号 '")
    private String accountName;

    @ApiModelProperty("银联行号")
    @Column(name = "interbank_number", columnDefinition = "VARCHAR(32) COMMENT '银联行号'")
    private String interbankNumber;

    @ApiModelProperty("开户行")
    @Column(name = "bank_name", columnDefinition = "VARCHAR(128) COMMENT '开户行'")
    private String bankName;

    @ApiModelProperty("本次付款金额")
    @Column(name = "this_pay_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '本次付款金额 '")
    private BigDecimal thisPayAmount;

    @ApiModelProperty("期望付款日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Column(name = "pay_date" , length = 10, columnDefinition = "datetime COMMENT '期望付款日期 '")
    private Date payDate;

    @ApiModelProperty("付款用途")
    @Column(name = "pay_purpose", columnDefinition = "VARCHAR(255) COMMENT '付款用途 '")
    private String payPurpose;

    @ApiModelProperty("付款方式")
    @Column(name = "pay_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '付款方式 '")
    private String payType;

    @ApiModelProperty("合同编码")
    @Column(name = "contract_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '合同编码 '")
    private String contractCode;

    @ApiModelProperty("合同名称")
    @Column(name = "contract_name", columnDefinition = "VARCHAR(255) COMMENT '合同名称 '")
    private String contractName;

    @ApiModelProperty("回单标记")
    @Column(name = "receipt_flag", columnDefinition = "VARCHAR(255)  default 'N' COMMENT '回单标记 '")
    private String receiptFlag;

    @ApiModelProperty("回单")
    @Column(name = "receipt", columnDefinition = "VARCHAR(255) COMMENT '回单 '")
    private String receipt;

    @ApiModelProperty("预付描述")
    @Column(name = "description", columnDefinition = "VARCHAR(255) COMMENT '预付描述 '")
    private String description;

    @ApiModelProperty("付款状态")
    @Column(name = "pay_status", length = 64, columnDefinition = "VARCHAR(64) default 'not_pay' COMMENT '付款状态 '")
    private String payStatus;

    @ApiModelProperty("收款行编号")
    @Column(name = "line_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '收款行编号 '")
    private String lineCode;

    @ApiModelProperty("付款成功时间")
    @Column(name = "pay_sucess_date", columnDefinition = "datetime COMMENT '付款成功时间'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paySucessDate;

    @Column(name = "kou_jian_prepare_carry_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '扣减待结转金额 '")
    private BigDecimal kouJianPrepareCarryAmount;
}
