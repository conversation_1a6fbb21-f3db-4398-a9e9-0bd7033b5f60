package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe: 管报收入
 * @createTime 2022年06月25日 10:19:00
 */
@ApiModel(value = "tpm_manage_report", description = "管报收入")
@TableName("tpm_manage_report")
@Getter
@Setter
@Entity(name = "tpm_manage_report")
@org.hibernate.annotations.Table(appliesTo = "tpm_manage_report", comment = "管报收入")
@Table(name = "tpm_manage_report", indexes = {
        @Index(name = "manage_report_idx1", columnList = "year_month_ly"),
        @Index(name = "manage_report_idx2", columnList = "department_code"),
        @Index(name = "manage_report_idx3", columnList = "item_code"),
        @Index(name = "manage_report_idx4", columnList = "item_name"),
        @Index(name = "manage_report_idx5", columnList = "customer_code"),
        @Index(name = "manage_report_idx6", columnList = "product_code"),})
public class ManageReport extends TenantFlagOpEntity {


  @ApiModelProperty("收入年月")
  @Column(name = "year_month_ly", columnDefinition = "varchar(20) comment '收入年月'")
  private String yearMonthLy;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
  private String companyCode;

  @ApiModelProperty("部门编码")
  @Column(name = "department_code", columnDefinition = "varchar(32) comment '部门编码'")
  private String departmentCode;

  @ApiModelProperty("部门名称")
  @Column(name = "department_name", columnDefinition = "varchar(64) comment '部门名称'")
  private String departmentName;

  @ApiModelProperty("部门层级")
  @Column(name = "level_num", columnDefinition = "int COMMENT '部门层级'")
  private Integer levelNum;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "varchar(64) comment '成本中心名称'")
  private String costCenterName;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
  private String erpCode;

  @ApiModelProperty("产品组编码")
  @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
  private String productGroupCode;

  @ApiModelProperty("渠道编码")
  @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
  private String channelCode;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty(name = "客户编号", notes = "客户编号")
  @Column(name = "customer_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '客户编号 '")
  private String customerCode;

  @ApiModelProperty(name = "客户名称", notes = "客户名称")
  @Column(name = "customer_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '客户名称 '")
  private String customerName;

  @ApiModelProperty("产品编码")
  @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
  private String productCode;

  @ApiModelProperty("产品名称")
  @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
  private String productName;

  @ApiModelProperty("数量")
  @Column(name = "quantity", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '数量 '")
  private BigDecimal quantity;

  @ApiModelProperty("金额")
  @Column(name = "amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '金额 '")
  private BigDecimal amount;

  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  @Column(name = "org_code",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编号 '")
  private String orgCode;

  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  @Column(name = "org_name",length = 128, columnDefinition = "VARCHAR(128) COMMENT '组织名称 '")
  private String orgName;

  @ApiModelProperty(name = "创建人名称", notes = "创建人名称")
  @Column(name = "create_full_name",length = 128, columnDefinition = "VARCHAR(128) COMMENT '创建人名称 '")
  private String createFullName;
}
