package com.biz.crm.tpm.business.pay.local.service.internal;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.budget.sdk.strategy.account.AccountPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.PayByStrategy;
import com.biz.crm.tpm.business.pay.local.entity.Account;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.repository.AccountVoRepository;
import com.biz.crm.tpm.business.pay.local.service.AccountProductService;
import com.biz.crm.tpm.business.pay.local.service.AccountService;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountProductDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.enums.AccountStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.event.AccountEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.AccountVoService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailWithCustomerVoService;
import com.biz.crm.tpm.business.pay.sdk.vo.AccountProductVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AccountVo;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailWithCustomerVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 费用上账主表(account)表Vo服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
@Service
public class AccountVoServiceImpl implements AccountVoService {

  @Autowired
  private AccountService accountService;
  @Autowired
  private AccountProductService accountProductService;
  @Autowired(required = false)
  private List<AccountEventListener> accountEventListeners;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private GenerateCodeService generateCodeService;
  @Autowired
  private AccountVoRepository accountVoRepository;
  @Autowired(required = false)
  private List<PayByStrategy> payByStrategies;
  @Autowired(required = false)
  private AuditDetailWithCustomerVoService auditDetailWithCustomerVoService;


  /**
   * 列表查询
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<AccountVo> findByConditions(Pageable pageable, AccountDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new AccountDto();
    }
    Page<AccountVo> byConditions = this.accountVoRepository.findByConditions(pageable, dto);
    List<AccountVo> accountVos = byConditions.getRecords();
    //查询商品
    List<String> codes = accountVos.stream().map(AccountVo::getAccountCode).collect(Collectors.toList());
    List<AccountProduct> productList = accountProductService.findByCodes(codes);
    if (!CollectionUtils.isEmpty(productList)) {
      Collection<AccountProductVo> accountProductVos = this.nebulaToolkitService.copyCollectionByWhiteList(productList, AccountProduct.class, AccountProductVo.class, HashSet.class, ArrayList.class);
      Map<String, List<AccountProductVo>> productMap = accountProductVos.stream().collect(Collectors.groupingBy(AccountProductVo::getAccountCode));
      for (AccountVo accountVo : accountVos) {
        String accountCode = accountVo.getAccountCode();
        List<AccountProductVo> productVos = productMap.get(accountCode);
        if (!CollectionUtils.isEmpty(productVos)) {
          accountVo.setProductList(productVos);
        }
      }
    }
    byConditions.setRecords(accountVos);
    return byConditions;
  }

  /**
   * 通过ID查询
   *
   * @param id 主键
   * @return
   */
  @Override
  public AccountVo findById(String id) {
    Validate.notBlank(id, "查询ID不能为空");
    Account entity = this.accountService.findById(id);
    if (Objects.isNull(entity)) {
      return null;
    }
    AccountVo accountVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, AccountVo.class, HashSet.class, ArrayList.class);
    List<AccountProduct> byCode = this.accountProductService.findByCode(accountVo.getAccountCode());
    if (CollectionUtils.isEmpty(byCode)) {
      return accountVo;
    }
    List<AccountProductVo> accountProductVos = (List<AccountProductVo>) this.nebulaToolkitService.copyCollectionByWhiteList(byCode, AccountProduct.class, AccountProductVo.class, HashSet.class, ArrayList.class);
    accountVo.setProductList(accountProductVos);
    return accountVo;
  }

  /**
   * 创建 批量操作
   *
   * @param accountDtoList 实体对象
   * @return
   */
  @Transactional
  @Override
  public AccountVo create(List<AccountDto> accountDtoList) {
    //校验
    this.amountVerify(accountDtoList);
    //生成编码
    List<String> accountCodes = generateCodeService.generateCode(PayConstant.ACCOUNT_CODE_GENERATE_RULE, accountDtoList.size());
    for (int i = 0; i < accountDtoList.size(); i++) {
      accountDtoList.get(i).setAccountCode(accountCodes.get(i));
      BigDecimal customerAuditAmount = accountDtoList.get(i).getCustomerAuditAmount();
      if (Objects.nonNull(customerAuditAmount)) {
        accountDtoList.get(i).setAuditAmount(customerAuditAmount);
      }
    }
    List<Account> accounts = (List<Account>) this.nebulaToolkitService
        .copyCollectionByWhiteList(accountDtoList, AccountDto.class, Account.class, HashSet.class, ArrayList.class);
    this.accountService.createBatch(accounts);
    //拆分商品信息
    List<AccountProduct> productList = new ArrayList<>();
    for (AccountDto dto : accountDtoList) {
      List<AccountProduct> list = this.splitProducts(dto);
      if (!CollectionUtils.isEmpty(list)) {
        productList.addAll(list);
      }
    }
    //保存商品信息
    if (!CollectionUtils.isEmpty(productList)) {
      this.accountProductService.createBatch(productList);
    }
    //提取出需要上账的数据
    List<AccountDto> collect = accountDtoList.stream().filter(e ->
        StringUtils.isBlank(e.getAccountStatus())).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(collect)) {
      //上账
      executeAccount(collect);
    }
    //发送事件
    List<AccountVo> vos = (List<AccountVo>) this.nebulaToolkitService
        .copyCollectionByWhiteList(accountDtoList, AccountDto.class, AccountVo.class, HashSet.class, ArrayList.class, "productList");
    if (!CollectionUtils.isEmpty(accountEventListeners)) {
      for (AccountEventListener accountEventListener : accountEventListeners) {
        accountEventListener.onCreated(vos);
      }
    }
    return null;
  }

  /**
   * 向DMS费用池上账
   *
   * @param accountDtoList
   */
  private void executeAccount(List<AccountDto> accountDtoList) {
    if (CollectionUtils.isEmpty(accountDtoList) || CollectionUtils.isEmpty(payByStrategies)) {
      return;
    }
    //支付策略
    for (AccountDto dto : accountDtoList) {
      String payBy = dto.getPayBy();
      List<PayByStrategy> collect = payByStrategies.stream().filter(e -> e.getCode().equals(payBy)).collect(Collectors.toList());
      for (PayByStrategy payByStrategy : collect) {
        Collection<AccountPayByStrategy> accountPayByStrategies = payByStrategy.getAccountPayByStrategies();
        if (!CollectionUtils.isEmpty(accountPayByStrategies)) {
          accountPayByStrategies.forEach(item -> item.execute(dto));
        }
      }
    }
  }

  /**
   * 上账金额校验
   *
   * @param accountDtoList
   */
  private void amountVerify(List<AccountDto> accountDtoList) {
    Validate.notEmpty(accountDtoList, "新增时,数据对象不能为空");
    Set<String> codes = accountDtoList.stream().map(AccountDto::getAuditDetailCode).collect(Collectors.toSet());
    Map<String, AccountVo> map = this.findAmountByAuditDetailCode(codes);
    Validate.notEmpty(map, "未查询到当前相关的核销信息，无法确认当前上账金额是否正确!");
    for (AccountDto dto : accountDtoList) {
      if (Objects.nonNull(map.get(dto.getAuditDetailCode().concat(dto.getCustomerCode() != null ? dto.getCustomerCode() : "")))) {
        BigDecimal amount = dto.getAmount();
        BigDecimal availableAmount = map.get(dto.getAuditDetailCode().concat(dto.getCustomerCode() != null ? dto.getCustomerCode() : "")).getAvailableAmount();
        Validate.notNull(amount, "核销明细编码[" + dto.getAuditDetailCode() + "],未填写上账金额");
        Validate.isTrue(amount.compareTo(availableAmount) <= 0, "核销明细编码[" + dto.getAuditDetailCode() + "],上账金额超出当前可上账金额");
        dto.setAvailableAmount(availableAmount);
      }
    }
  }

  /**
   * 组装商品
   *
   * @param dto
   * @return
   */
  private List<AccountProduct> splitProducts(AccountDto dto) {
    String accountCode = dto.getAccountCode();
    List<AccountProductDto> productList = dto.getProductList();
    if (CollectionUtils.isEmpty(productList)) {
      return null;
    }
    for (AccountProductDto accountProductDto : productList) {
      accountProductDto.setAccountCode(accountCode);
    }
    return (List<AccountProduct>) this.nebulaToolkitService
        .copyCollectionByWhiteList(productList, AccountProductDto.class, AccountProduct.class, HashSet.class, ArrayList.class);
  }

  /**
   * 更新
   *
   * @param dto 实体对象
   * @return
   */
  @Override
  @Transactional
  public AccountVo update(AccountDto dto) {
    Validate.notNull(dto, "更新时，数据对象不能为空!");
    String id = dto.getId();
    AccountVo oldVo = this.findById(id);
    String accountStatus = oldVo.getAccountStatus();
    Validate.isTrue(accountStatus.equals(AccountStatusEnum.PENDING.getDictCode()), "只能编辑待上账状态的费用信息");
    Account newEntity = this.nebulaToolkitService.copyObjectByWhiteList(oldVo, Account.class, HashSet.class, ArrayList.class);
    //更新金额
    newEntity.setAmount(dto.getAmount());
    newEntity.setAccountStatus(dto.getAccountStatus());
    newEntity.setRemark(dto.getRemark());
    newEntity.setProductLevelCode(dto.getProductLevelCode());
    newEntity.setProductLevelName(dto.getProductLevelName());
    this.amountVerify(Lists.newArrayList(dto));
    this.accountService.update(newEntity);
    //更新商品
    //删除旧信息
    this.accountProductService.deleteBatchByCodes(Lists.newArrayList(dto.getAccountCode()));
    //保存新信息
    List<AccountProduct> productList = new ArrayList<>();
    List<AccountProduct> list = this.splitProducts(dto);
    if (!CollectionUtils.isEmpty(list)) {
      productList.addAll(list);
    }
    //保存商品信息
    if (!CollectionUtils.isEmpty(productList)) {
      this.accountProductService.createBatch(productList);
    }
    //上账
    if (StringUtils.isBlank(dto.getAccountStatus())) {
      this.executeAccount(Lists.newArrayList(dto));
    }
    AccountVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(newEntity, AccountVo.class, HashSet.class, ArrayList.class);
    if (!CollectionUtils.isEmpty(accountEventListeners)) {
      for (AccountEventListener accountEventListener : accountEventListeners) {
        accountEventListener.onUpdate(oldVo, newVo);
      }
    }
    return null;
  }

  /**
   * 删除
   *
   * @param idList 主键结合
   */
  @Transactional
  @Override
  public void delete(List<String> idList) {
    Validate.notEmpty(idList, "删除对象ID集合不能为空!");
    List<Account> byIds = this.accountService.findByIds(idList);
    Validate.notEmpty(idList, "未查询到相关数据!");
    Validate.isTrue(byIds.size() == idList.size(), "传入ID集合与相关数据集合长度不匹配");
    List<Account> onList = byIds.stream().filter(e -> e.getAccountStatus().equals(AccountStatusEnum.ON.getDictCode())).collect(Collectors.toList());
    Validate.isTrue(CollectionUtils.isEmpty(onList), "只能删除待上账数据");
    //逻辑删除
    this.accountService.deleteBatch(idList);
    List<String> codes = byIds.stream().map(Account::getAccountCode).collect(Collectors.toList());
    this.accountProductService.deleteBatchByCodes(codes);
    List<AccountVo> accountVos = (List<AccountVo>) this.nebulaToolkitService.copyCollectionByWhiteList(byIds, Account.class, AccountVo.class, HashSet.class, ArrayList.class);
    //TODO 发送事件
    if (!CollectionUtils.isEmpty(accountEventListeners)) {
      for (AccountEventListener accountEventListener : accountEventListeners) {
        accountEventListener.onDeleted(accountVos);
      }
    }
  }

  /**
   * 查询核销分页信息
   *
   * @param pageable
   * @param account
   * @return
   */
  @Override
  public Page<AccountVo> findAuditByDto(Pageable pageable, AccountDto account) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(account)) {
      account = new AccountDto();
    }
    AuditDetailDto dto = this.nebulaToolkitService.copyObjectByWhiteList(account, AuditDetailDto.class, HashSet.class, ArrayList.class);
    List<AuditDetailWithCustomerVo> auditDetailWithCustomer = auditDetailWithCustomerVoService.getAuditDetailWithCustomer(dto);
    if (CollectionUtils.isEmpty(auditDetailWithCustomer)) {
      return null;
    }
    List<AccountVo> accountVos = (List<AccountVo>) this.nebulaToolkitService.copyCollectionByWhiteList(auditDetailWithCustomer, AuditDetailWithCustomerVo.class, AccountVo.class, HashSet.class, ArrayList.class, "productList");
    Map<String, BigDecimal> allAccountAmount = this.accountVoRepository.findAllAccountAmount(account);
    List<AccountVo> res = this.countAvailableAmount(accountVos, allAccountAmount);
    //手动分页
    long totalSize = res.size();
    res = res.stream().sorted(Comparator.comparing(AccountVo::getAuditDetailCode).reversed())
        .skip(pageable.getPageSize() * (pageable.getPageNumber() > 0 ? pageable.getPageNumber() - 1 : 0))
        .limit(pageable.getPageSize()).collect(Collectors.toList());
    Page<AccountVo> result = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    result.setRecords(res);
    result.setTotal(totalSize);
    return result;
  }

  /**
   * 汇总核销和上账数据 计算可上账金额
   *
   * @param accountVos
   * @param allAccountAmount
   * @return
   */
  private List<AccountVo> countAvailableAmount(List<AccountVo> accountVos, Map<String, BigDecimal> allAccountAmount) {
    for (AccountVo accountVo : accountVos) {
      String customerCode = accountVo.getCustomerCode();
      String auditDetailCode = accountVo.getAuditDetailCode();
      BigDecimal auditAmount = accountVo.getAuditAmount();
      BigDecimal customerAuditAmount = accountVo.getCustomerAuditAmount();
      accountVo.setAvailableAmount(BigDecimal.ZERO);
      if (!CollectionUtils.isEmpty(allAccountAmount)) {
        if (StringUtils.isBlank(customerCode)) {
          BigDecimal accountAmount = allAccountAmount.get(auditDetailCode);
          BigDecimal subtract = auditAmount.subtract(accountAmount != null ? accountAmount : BigDecimal.ZERO);
          Validate.isTrue(subtract.compareTo(BigDecimal.ZERO) >= 0, "可上账金额超出核销金额");
          accountVo.setAvailableAmount(subtract);
        } else {
          String key = auditDetailCode.concat(customerCode);
          BigDecimal accountAmount = allAccountAmount.get(key);
          BigDecimal subtract = customerAuditAmount.subtract(accountAmount != null ? accountAmount : BigDecimal.ZERO);
          Validate.isTrue(subtract.compareTo(BigDecimal.ZERO) >= 0, "可上账金额超出核销金额");
          accountVo.setAvailableAmount(subtract);
        }
      }
    }
    return accountVos.stream().filter(e -> e.getAvailableAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
  }


  /**
   * 查询核销金额
   * K-核销明细编码+客户编码
   *
   * @param codes
   * @return
   */
  @Override
  public Map<String, AccountVo> findAmountByAuditDetailCode(Set<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return Maps.newHashMap();
    }
    AuditDetailDto dto = new AuditDetailDto();
    dto.setAuditDetailCodeList(Lists.newArrayList(codes));
    List<AuditDetailWithCustomerVo> auditDetailWithCustomer = auditDetailWithCustomerVoService.getAuditDetailWithCustomer(dto);
    if (CollectionUtils.isEmpty(auditDetailWithCustomer)) {
      return null;
    }
    List<AccountVo> accountVos = (List<AccountVo>) this.nebulaToolkitService.copyCollectionByWhiteList(auditDetailWithCustomer, AuditDetailWithCustomerVo.class, AccountVo.class, HashSet.class, ArrayList.class);
    AccountDto account = new AccountDto();
    Map<String, BigDecimal> allAccountAmount = this.accountVoRepository.findAllAccountAmount(account);
    List<AccountVo> res = this.countAvailableAmount(accountVos, allAccountAmount);
    Map<String, AccountVo> map = new HashMap<>();
    for (AccountVo vo : res) {
      String auditDetailCode = vo.getAuditDetailCode();
      String customerCode = vo.getCustomerCode();
      map.put(auditDetailCode.concat(customerCode != null ? customerCode : ""), vo);
    }
    return map;
  }

  /**
   * 启用
   *
   * @param idList
   */
  @Transactional
  @Override
  public void enable(List<String> idList) {
    Validate.notEmpty(idList, "ID集合不能为空");
    List<AccountVo> byIds = this.findByIds(idList);
    Validate.notEmpty(byIds, "未查询到相关数据");
    Validate.isTrue(byIds.size() == idList.size(), "主键集合与查询出的数据不符合!");
    this.accountVoRepository.enable(idList);
  }

  @Override
  public List<AccountVo> findByIds(List<String> idList) {
    Validate.notEmpty(idList, "ID集合不能为空");
    return this.accountVoRepository.findByIds(idList);
  }

  @Override
  public void disable(List<String> idList) {
    Validate.notEmpty(idList, "ID集合不能为空");
    List<AccountVo> byIds = this.findByIds(idList);
    Validate.notEmpty(byIds, "未查询到相关数据");
    Validate.isTrue(byIds.size() == idList.size(), "主键集合与查询出的数据不符合!");
    this.accountVoRepository.disable(idList);
  }
}

