package com.biz.crm.tpm.business.pay.local.service.internal.process;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.tpm.business.pay.local.service.internal.MarketingAuditPageCacheHelper;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.enums.BillTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailImportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MarketingAuditDetailImportProcess implements ImportProcess<MarketingAuditDetailImportVo> {

    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;
    @Autowired(required = false)
    private MarketingAuditPageCacheHelper marketingAuditPageCacheHelper;

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, MarketingAuditDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, MarketingAuditDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");

        for (Map.Entry<Integer, MarketingAuditDetailImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            MarketingAuditDetailImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getSchemeDetailCode()), "活动编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getAuditAmountStr()), "本次结案金额，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getBeFullAudit()), "是否完全结案，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCashType()), "兑付类型，不能为空！");

            if (StringUtils.isNotEmpty(vo.getCashType()) && Arrays.asList(CashMethodEnum.WIRE_TRANSFER.getValue(), CashMethodEnum.DEDUCTIONS.getValue()).contains(vo.getCashType())) {
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getBillType()), "票据类型，不能为空！");
            }
            if (StringUtils.isNotBlank(vo.getBillType())) {
                this.validateIsTrue(BillTypeEnum.findByValue(vo.getBillType()) != null, "票据类型，不正确！");
            }
            if (StringUtils.isNotBlank(vo.getBeFullAudit())) {
                this.validateIsTrue(Arrays.asList("是", "否").contains(vo.getBeFullAudit()), "是否完全结案，不正确！");
            }
            try {
                new BigDecimal(vo.getAuditAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "本次结案金额类型转换失败！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, MarketingAuditDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();

        String cacheKey = (String) params.get("cacheKey");
        List<MarketingAuditDetailDto> cacheList = marketingAuditService.findAllCacheList(cacheKey);
        Validate.notEmpty(cacheList, "未获取到任何结案明细缓存");
        Map<String, MarketingAuditDetailDto> auditDetailMap = cacheList.stream().collect(Collectors.toMap(MarketingAuditDetailDto::getSchemeDetailCode, Function.identity(), (a, b) -> a));

        List<MarketingAuditDetailDto> updateCacheList = new ArrayList<>();
        for (Map.Entry<Integer, MarketingAuditDetailImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            MarketingAuditDetailImportVo vo = row.getValue();

            MarketingAuditDetailDto auditDetailDto = auditDetailMap.get(vo.getSchemeDetailCode());
            if (auditDetailDto != null) {
                auditDetailDto.setAuditAmount(new BigDecimal(vo.getAuditAmountStr()));
                auditDetailDto.setBeFullAudit(vo.getBeFullAudit().equals(BooleanEnum.TRUE.getSure()) ? BooleanEnum.TRUE.getCapital() : BooleanEnum.FALSE.getCapital());
                if (StringUtils.isNotEmpty(vo.getCashType()) && Arrays.asList(CashMethodEnum.WIRE_TRANSFER.getValue(), CashMethodEnum.DEDUCTIONS.getValue()).contains(vo.getCashType())) {
                    auditDetailDto.setBillType(BillTypeEnum.findByValue(vo.getBillType()).getDictCode());
                }
                updateCacheList.add(auditDetailDto);
            } else {
                this.validateIsTrue(false, "未找到活动编码对应的结案明细！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        marketingAuditPageCacheHelper.importNewItem(cacheKey, updateCacheList);

        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<MarketingAuditDetailImportVo> findCrmExcelVoClass() {
        return MarketingAuditDetailImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "MARKETING_AUDIT_DETAIL_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "结案明细导入模板";
    }
}
