package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.service.CostControlLoginService;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepay;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord;
import com.biz.crm.tpm.business.pay.local.repository.*;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordItemDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.ActivityPrepayLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.enums.*;
import com.biz.crm.tpm.business.pay.sdk.event.log.ActivityPrepayLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayDetailRecordService;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepaySendHecService;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayService;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayVo;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动预付
 */
@Service
@Slf4j
@RefreshScope
public class ActivityPrepaySendHecServiceImpl implements ActivityPrepaySendHecService {

    @Autowired(required = false)
    private ActivityPrepayRepository activityPrepayRepository;

    @Autowired(required = false)
    private ActivityPrepayDetailRepository activityPrepayDetailRepository;

    @Autowired(required = false)
    private ActivityPrepayPayeeRepository activityPrepayPayeeRepository;

    @Autowired(required = false)
    private ActivityPrepayFilesRepository activityPrepayFilesRepository;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired(required = false)
    private ActivityPrepayService activityPrepayService;

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private CostControlLoginService costControlLoginService;

    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;

    @Autowired(required = false)
    private ActivityPrepayDetailRecordRepository activityPrepayDetailRecordRepository;

    @Resource
    private ActivityPrepayDetailRecordService activityPrepayDetailRecordService;

    @Value("${domain-name:}")
    private String domainName;

    @Value("${domain-name-file:}")
    private String domainNameFile;

    /**
     * 新增并提交
     *
     * @param dto dto对象
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> submitCreate(ActivityPrepayDto dto, String cacheKey) {
        activityPrepayService.create(dto, cacheKey, true);
        Assert.hasLength(dto.getId(), "提交失败,未获取ID!");
        Result<String> result = submitBatch(Collections.singletonList(dto.getId()));
        if (!result.isSuccess()) {
            result.setCode(com.biz.crm.business.common.sdk.constant.CommonConstant.SC_OK_200);
            result.setSuccess(true);
            result.setMessage("单据保存成功,但提交失败。" + result.getMessage());
        }
        activityPrepayService.clearCache(cacheKey);
        return result;
    }

    /**
     * 修改并提交
     *
     * @param dto dto对象
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> submitUpdate(ActivityPrepayDto dto, String cacheKey) {
        activityPrepayService.update(dto, cacheKey, true);
        Assert.hasLength(dto.getId(), "提交失败,未获取ID!");
        Result<String> result = submitBatch(Collections.singletonList(dto.getId()));
        Assert.isTrue(result.isSuccess(), result.getMessage());
        activityPrepayService.clearCache(cacheKey);
        return result;
    }


    /**
     * 批量提交
     *
     * @param idList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/15 20:39
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> submitBatch(List<String> idList) {
        Assert.notEmpty(idList, "提交时，主键集合不能为空！");
        List<ActivityPrepay> entityList = this.activityPrepayRepository.findByIdList(idList);
        if (CollectionUtil.isEmpty(entityList)) {
            return Result.error("未找到数据,请刷新重试!");
        }
        entityList.forEach(item -> Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.PASS.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】、【审批通过】核销数据能提交"));
        List<ActivityPrepayDto> sendHecDtoList = (List<ActivityPrepayDto>) nebulaToolkitService.copyCollectionByWhiteList(entityList,
                ActivityPrepay.class, ActivityPrepayDto.class, HashSet.class, ArrayList.class);
        sendHecDtoList.forEach(dto -> {
            if (ProcessStatusEnum.PREPARE.getDictCode().equals(dto.getStatus())) {
                dto.setHecOperationType(HecOperationTypeEnum.CREATE.getCode());
            } else if (ProcessStatusEnum.PASS.getDictCode().equals(dto.getStatus())) {
                dto.setHecOperationType(HecOperationTypeEnum.RESUBMIT.getCode());
            } else {
                dto.setHecOperationType(HecOperationTypeEnum.UPDATE.getCode());
            }
        });
        //预付明细操作
        this.modifyActivityPrepayRecord(BeanUtil.copyToList(entityList, ActivityPrepayVo.class));
        Result<String> result = prepaySendHec(sendHecDtoList);
        Assert.isTrue(result.isSuccess(), result.getMessage());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyActivityPrepayRecord(List<ActivityPrepayVo> entityList) {
        List<String> codes = entityList.stream()
                .filter(e -> StringUtils.isNoneBlank(e.getCompanyCode(), e.getPayeeCode()))
                .map(ActivityPrepayVo::getPrepayCode).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(codes)) {
            return;
        }
        List<ActivityPrepayDetail> list = activityPrepayDetailRepository.findByCodes(codes);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Map<String, String> processKeyMap = entityList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getPrepayCode()))
                .filter(k -> StringUtil.isNotEmpty(k.getProcessKey()))
                .collect(Collectors.toMap(ActivityPrepayVo::getPrepayCode, ActivityPrepayVo::getProcessKey, (n, o) -> n));
        Map<String, List<ActivityPrepayDetail>> detailListMap = list.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getCompanyCode()))
                .filter(k -> StringUtil.isNotEmpty(k.getPayeeCode()))
                .collect(Collectors.groupingBy(e -> StringUtils.join(e.getCompanyCode(), e.getPayeeCode())));
        Map<String, String> msgMap = Maps.newHashMap();
        detailListMap.forEach((code, details) -> {
            String msg = details.stream().filter(k -> StringUtil.isNotEmpty(k.getPrepayCode()))
                    .map(ActivityPrepayDetail::getPrepayCode).distinct().collect(Collectors.joining(","));
            msgMap.put(code, StringUtils.join("关联结转金额已变更，请先检查：", msg));
        });

        //预付明细
        ActivityPrepayVo prepayVo = new ActivityPrepayVo();
        prepayVo.setTenantCode(TenantUtils.getTenantCode());
        prepayVo.setPrepayType(TpmPrepayTypeEnum.activity.getCode());
        prepayVo.setCompanyCodes(list.stream().map(ActivityPrepayDetail::getCompanyCode).distinct().collect(Collectors.toList()));
        prepayVo.setPayeeCodes(list.stream().map(ActivityPrepayDetail::getPayeeCode).distinct().collect(Collectors.toList()));
        List<ActivityPrepayDetailRecord> prepayDetailRecords = activityPrepayDetailRecordRepository.findCalList(prepayVo);
        if (CollectionUtil.isEmpty(prepayDetailRecords)) {
            return;
        }
        Map<String, List<ActivityPrepayDetailRecord>> recordListMap = prepayDetailRecords.stream().collect(Collectors.groupingBy(e -> StringUtils.join(e.getCompanyCode(), e.getPayeeCode())));
        List<String> prepayDetailCodes = prepayDetailRecords.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getPrepayDetailCode()))
                .map(ActivityPrepayDetailRecord::getPrepayDetailCode).collect(Collectors.toList());

        activityPrepayDetailRecordService.operateLock(prepayDetailCodes, true);
        try {
            List<ActivityPrepayDetailRecordItemDto> itemDtoList = new ArrayList<>();
            detailListMap.forEach((detailKey, detailList) -> {
                if (!recordListMap.containsKey(detailKey)) {
                    return;
                }
                List<ActivityPrepayDetailRecord> detailRecords = recordListMap.get(detailKey);

                ActivityPrepayVo vo = new ActivityPrepayVo();
                vo.setTenantCode(TenantUtils.getTenantCode());
                vo.setPrepayType(TpmPrepayTypeEnum.activity.getCode());
                vo.setStatus(ProcessStatusEnum.PREPARE.getDictCode());
                vo.setCompanyCode(detailList.get(0).getCompanyCode());
                vo.setPayeeCode(detailList.get(0).getPayeeCode());
                List<ActivityPrepayDetail> prepayDetails = activityPrepayDetailRepository.findCalList(vo);
                List<String> ids = detailList.stream().map(ActivityPrepayDetail::getId).collect(Collectors.toList());
                //同一公司+收款方时，其它待提交的预付数据预付明细占用的金额
                BigDecimal otherOccupyAmount = prepayDetails.stream()
                        .filter(e -> !ids.contains(e.getId())).map(ActivityPrepayDetail::getRelateCarryAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                List<ActivityPrepayDetailRecord> prepayDetailRecordsFilters = new ArrayList<>();
                for (ActivityPrepayDetailRecord prepayDetailRecord : detailRecords) {
                    if (otherOccupyAmount.compareTo(BigDecimal.ZERO) == 0) {
                        prepayDetailRecordsFilters.add(prepayDetailRecord);
                        continue;
                    }
                    if (otherOccupyAmount.compareTo(prepayDetailRecord.getPrepareCarryAmount()) >= 0) {
                        otherOccupyAmount = otherOccupyAmount.subtract(prepayDetailRecord.getPrepareCarryAmount());
                        continue;
                    }
                    prepayDetailRecord.setPrepareCarryAmount(prepayDetailRecord.getPrepareCarryAmount().subtract(otherOccupyAmount));
                    otherOccupyAmount = BigDecimal.ZERO;
                }

                //分配待结转金额，关联结转金额≤申请预付金额
                List<ActivityPrepayDetailVo> calList = BeanUtil.copyToList(detailList, ActivityPrepayDetailVo.class);
                BigDecimal totalRelateCarryAmount = calList.stream().map(ActivityPrepayDetailVo::getRelateCarryAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                calList.sort(Comparator.comparing(ActivityPrepayDetailVo::getRelateCarryAmount));
                for (ActivityPrepayDetailVo detailVo : calList) {
                    for (ActivityPrepayDetailRecord prepayDetailRecordsFilter : prepayDetailRecordsFilters) {
                        if (prepayDetailRecordsFilter.getPrepareCarryAmount().compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                        BigDecimal amount;
                        if (detailVo.getRelateCarryAmount().compareTo(prepayDetailRecordsFilter.getPrepareCarryAmount()) <= 0) {
                            amount = detailVo.getRelateCarryAmount();
                        } else {
                            amount = prepayDetailRecordsFilter.getPrepareCarryAmount();
                        }
                        detailVo.setRelateCarryAmount(detailVo.getRelateCarryAmount().subtract(amount));
                        prepayDetailRecordsFilter.setPrepareCarryAmount(prepayDetailRecordsFilter.getPrepareCarryAmount().subtract(amount));
                        if (amount.compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }

                        //构建操作明细
                        ActivityPrepayDetailRecordItemDto itemDto = new ActivityPrepayDetailRecordItemDto();
                        itemDto.setPrepayCode(prepayDetailRecordsFilter.getPrepayCode());
                        itemDto.setPrepayName(prepayDetailRecordsFilter.getPrepayName());
                        itemDto.setPrepayDetailCode(prepayDetailRecordsFilter.getPrepayDetailCode());
                        itemDto.setSchemeCode(prepayDetailRecordsFilter.getSchemeCode());
                        itemDto.setSchemeName(prepayDetailRecordsFilter.getSchemeName());
                        itemDto.setSchemeDetailCode(prepayDetailRecordsFilter.getSchemeDetailCode());
                        itemDto.setType(ActivityPrepayDetailRecordOperateTypeEnum.CARRY.getCode());
                        itemDto.setBusinessCode(String.join("_", detailVo.getPrepayDetailCode(), processKeyMap.get(detailVo.getPrepayCode())));
                        itemDto.setBusinessName("活动预付提交流程");
                        itemDto.setAmount(amount);
                        itemDtoList.add(itemDto);
                    }
                }
                BigDecimal totalAmount = itemDtoList.stream().map(ActivityPrepayDetailRecordItemDto::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Validate.isTrue(totalRelateCarryAmount.compareTo(totalAmount) == 0, msgMap.get(detailKey));
            });
            activityPrepayDetailRecordService.operate(itemDtoList);
        } finally {
            activityPrepayDetailRecordService.operateLock(prepayDetailCodes, false);
        }
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> delete(List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
        List<ActivityPrepay> configList = this.activityPrepayRepository.findByIdList(idList);
        Validate.notEmpty(configList, "根据提供的主键集合信息，未能获取到相应数据");

        configList.forEach(item -> Validate.isTrue(ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】数据能删除"));

        List<ActivityPrepay> valCashes = configList.stream()
                .filter(item -> ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()))
                .collect(Collectors.toList());
        Validate.isTrue(valCashes.size() <= 1, "驳回或追回的数据不支持批量删除！");

        if (CollectionUtil.isNotEmpty(valCashes)) {
            List<ActivityPrepayDto> sendHecDtoList = (List<ActivityPrepayDto>) nebulaToolkitService.copyCollectionByWhiteList(valCashes,
                    ActivityPrepay.class, ActivityPrepayDto.class, HashSet.class, ArrayList.class);
            sendHecDtoList.forEach(dto -> {
                dto.setHecOperationType(HecOperationTypeEnum.DELETE.getCode());
            });
            Result<String> result = prepaySendHec(sendHecDtoList);
            if (!result.isSuccess()) {
                return result;
            }
        }

        List<String> codeList = configList.stream().map(ActivityPrepay::getPrepayCode).collect(Collectors.toList());
        this.activityPrepayDetailRepository.deleteByCodeList(codeList);
        this.activityPrepayPayeeRepository.deleteByCodeList(codeList);
        this.activityPrepayFilesRepository.deleteByCodeList(codeList);
        this.activityPrepayRepository.deleteByCodes(codeList);

        //删除业务日志
        Collection<ActivityPrepayDto> dtoList = nebulaToolkitService.copyCollectionByWhiteList(configList,
                ActivityPrepay.class, ActivityPrepayDto.class, HashSet.class, ArrayList.class);
        ActivityPrepayLogEventDto logEventDto = new ActivityPrepayLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(new ArrayList<>(dtoList));
        SerializableBiConsumer<ActivityPrepayLogEventListener, ActivityPrepayLogEventDto> onDelete =
                ActivityPrepayLogEventListener::onDelete;
        this.nebulaNetEventClient.publish(logEventDto, ActivityPrepayLogEventListener.class, onDelete);
        return Result.ok("删除成功!");
    }


    /**
     * 推送费控 活动预付
     *
     * @param sendHecDtoList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/9 17:04
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> prepaySendHec(List<ActivityPrepayDto> sendHecDtoList) {
        if (CollectionUtil.isEmpty(sendHecDtoList)) {
            return Result.error("推送数据不能为空!");
        }
        StringBuffer errorMsg = new StringBuffer();
        sendHecDtoList.forEach(dto -> {
            ExternalLogDetailDto logDetailDto = null;
            JSONObject jsonObject = this.buildPrepaySendData(dto);
            Assert.notNull(jsonObject, "未查询到数据,请刷新重新选择数据!");
            try {
                String token = costControlLoginService.getToken();
                UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
                Map<String, String> headMap = Maps.newHashMap();
                headMap.put(RyConstant.HEADER_AUTHORIZATION, token);
                String url = urlAddressVo.getUrl();
                String interfaceAddress = RyConstant.FK_WIRE_APPLY_FORM_INTERFACE_ADDRESS;
                JSONObject headJson = new JSONObject();
                headJson.put(RyConstant.HEADER_AUTHORIZATION, token);
                logDetailDto = ExternalLogUtil.buildLogSaveInfo(jsonObject.toJSONString(), urlAddressVo);
                logDetailDto.setReqHead(headJson.toJSONString());
                logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf(".") + 1));
                logDetailDto.setRequestUri(interfaceAddress);
                logDetailDto.setMethodMsg("预付申请单:活动预付");
                externalLogVoService.addOrUpdateLog(logDetailDto, true);
                Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, jsonObject.toJSONString(), headMap);
                ExternalLogUtil.buildLogResult(logDetailDto, result);
                this.buildHecResultData(result, dto);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
            } catch (Exception e) {
                if (Objects.nonNull(logDetailDto)) {
                    logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                    logDetailDto.setTipMsg(e.getMessage());
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                }
                log.error(e.getMessage(), e);
                errorMsg.append("单据[");
                errorMsg.append(dto.getPrepayCode());
                errorMsg.append("];提交费控异常:");
                errorMsg.append(e.getMessage());
                errorMsg.append(";");
            }

        });
        Result<String> result = new Result<>();
        if (StringUtil.isNotEmpty(errorMsg.toString())) {
            result.error500(errorMsg.toString());
        }
        return result;

    }

    /**
     * 构建明细 推送费控 活动预付
     *
     * @param dto
     * @return
     */
    private JSONObject buildPrepaySendData(ActivityPrepayDto dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        ActivityPrepayVo vo = activityPrepayService.findByCode(dto.getPrepayCode());
        Assert.notNull(vo, "未找到数据[" + vo.getPrepayCode() + "]");
        Assert.notEmpty(vo.getDetailList(), "活动预提[" + vo.getPrepayCode() + "]预付/兑付明细不能为空");
        vo.setHecOperationType(dto.getHecOperationType());
        JSONObject jsonObject = new JSONObject();
        JSONObject headVo = this.buildPrepayHead(vo);
        JSONArray requisitionLines = this.buildRequisitionLines(vo);
        JSONArray paymentLines = this.buildPaymentLines(vo);
        //1、【活动预付】传“TPMREQ09”
        //2、【费用兑付-电汇预付】
        //①收款信息-收款方类型=供应商且“TOC支付为空”时，传“TPMREQ09”
        //②收款信息-收款方类型=供应商且“TOC支付不为空”时，传“REQ12”
        //3、【费用兑付-账扣预付】传“TPMREQ10”
        headVo.put("moExpReqTypeCode", "TPMREQ09");
        headVo.put("requisitionLines", requisitionLines);
        headVo.put("paymentLines", paymentLines);
        List<String> fileUrlList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(vo.getFilesList())) {
            vo.getFilesList().forEach(item -> {
                fileUrlList.add(String.format(domainNameFile, item.getFileCode()));
            });
        }
        headVo.put("fileUrls", fileUrlList);
        jsonObject.put("payload", JSONObject.toJSONString(headVo));
        return jsonObject;
    }

    /**
     * 构建推送明细  头部信息
     * 推送费控 活动预付
     *
     * @param vo
     * @return
     */
    private JSONObject buildPrepayHead(ActivityPrepayVo vo) {
        JSONObject headVo = new JSONObject();
        if (Objects.isNull(vo)) {
            return headVo;
        }
        Assert.hasLength(vo.getPositionCode(), "活动预付[" + vo.getPrepayCode() +
                "]职位[position_code]为空!");
        PositionVo positionVo = positionVoService.findByPositionCode(vo.getPositionCode());
        Assert.notNull(positionVo, "活动预付[" + vo.getPrepayCode() +
                "职位[" + vo.getPositionCode() + "]在主数据中不存在!");
        headVo.put("employeeCode", vo.getCreateAccount());
        headVo.put("paymentCurrencyCode", RyConstant.CNY);
        headVo.put("description", vo.getRemark());
        headVo.put("requisitionDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
        headVo.put("advanceFlag", "Y");
        headVo.put("paymentFlag", "N");
        headVo.put("assetBusinessFlag", "N");
        headVo.put("sourceSystem", "TPM");
        headVo.put("accrualReqTypeCode", "TPMYT004");
        headVo.put("sourceOrderType", HecBusinessTypeEnum.TPM_ACTIVITY_PREPAY_HEC.getCode());
        headVo.put("sourceOrderNumber", vo.getPrepayCode());
        headVo.put("sourceSystemOperation", vo.getHecOperationType());
        headVo.put("sourceOrderUrl", domainName + TpmOaPageEnum.CLOSING_ACTIVITY_PAY_FORM.getUrlCode() + "?code=" + vo.getPrepayCode());
        headVo.put("operatedDate", DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
        headVo.put("operatorCode", positionVo.getUserName());
        headVo.put("accEntityCode", vo.getCompanyCode());
        headVo.put("companyCode", positionVo.getSubCompanyId());
        headVo.put("unitCode", positionVo.getDepartmentId());
        return headVo;
    }

    /**
     * 构建推送明细  行信息
     * 推送费控 活动预付
     *
     * @param vo
     * @return
     */
    private JSONArray buildRequisitionLines(ActivityPrepayVo vo) {
        JSONArray jsonArray = new JSONArray();
        if (Objects.isNull(vo)
                || CollectionUtil.isEmpty(vo.getDetailList())) {
            return jsonArray;
        }
        List<String> schemeDetailCodeList = Lists.newArrayList();
        vo.getDetailList().forEach(item -> {
            Assert.hasLength(item.getSchemeDetailCode(), "活动预付[" + vo.getPrepayCode() + "]方案明细编码不能为空!");
            schemeDetailCodeList.add(item.getSchemeDetailCode());
        });
        List<MarketingPlanCaseVo> caseVoList = marketingPlanCaseService.findByCaseCodes(schemeDetailCodeList);
        Assert.notEmpty(caseVoList, "活动预付[" + vo.getPrepayCode() + "]方案明细不存在!");
        Map<String, MarketingPlanCaseVo> caseVoMap = caseVoList.stream()
                .collect(Collectors.toMap(MarketingPlanCaseVo::getSchemeDetailCode, v -> v, (n, o) -> n));
        List<String> orgCodeList = caseVoList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getBearDepartmentCode()))
                .map(MarketingPlanCaseVo::getBearDepartmentCode)
                .distinct().collect(Collectors.toList());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(orgCodeList);
        vo.getDetailList().forEach(detailVo -> {
            JSONObject jsonObject = new JSONObject();
            MarketingPlanCaseVo caseVo = caseVoMap.getOrDefault(detailVo.getSchemeDetailCode(), new MarketingPlanCaseVo());
            jsonObject.put("primaryQuantity", 1);
            jsonObject.put("reportPageElementCode", "STANDARD");
            jsonObject.put("businessItemCode", caseVo.getCategoryCode());
            jsonObject.put("respCenterCode", caseVo.getCostCenterCode());
            jsonObject.put("dimension5Code", detailVo.getErpCode());
            jsonObject.put("dimension3Code", detailVo.getItemCode());
            List<OrgOaOrgVo> oaOrgVoList = orgOaOrgVoMap.get(caseVo.getBearDepartmentCode());
            Assert.notEmpty(oaOrgVoList, "活动预付[" + vo.getPrepayCode() +
                    "]方案明细[" + detailVo.getSchemeDetailCode() + "]承担部门[" + caseVo.getBearDepartmentCode() + "]未找到OA组织!");
            jsonObject.put("unitCode", oaOrgVoList.get(0).getOaOrgCode());
            jsonObject.put("attribute8", caseVo.getYears());
            jsonObject.put("description", detailVo.getRemark());
            jsonObject.put("businessAmount", detailVo.getThisPrepayAmount());
            jsonObject.put("businessCurrencyCode", RyConstant.CNY);
            jsonObject.put("managementCurrencyCode", RyConstant.CNY);
            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }

    /**
     * 构建推送明细  付款行信息
     * 推送费控 活动预付
     *
     * @param vo
     * @return
     */
    private JSONArray buildPaymentLines(ActivityPrepayVo vo) {
        JSONArray jsonArray = new JSONArray();
        if (Objects.isNull(vo)
                || CollectionUtil.isEmpty(vo.getPayeeList())) {
            return jsonArray;

        }
        vo.getPayeeList().forEach(item -> {
            JSONObject jsonObject = new JSONObject();
            PaeeTypeEnum paeeTypeEnum = PaeeTypeEnum.findByCode(item.getPayeeType());
            Assert.notNull(paeeTypeEnum, "活动预付[" + vo.getPrepayCode() + "]收款方类型不合法!");
            //现金事务分类代码
            jsonObject.put("moCshTrxClassCode", "CSH002");
            jsonObject.put("payeeCode", item.getSupplierErpCode());
            jsonObject.put("payeeCategory", paeeTypeEnum.getHecCode());
            jsonObject.put("paymentMethodCode", item.getPayType());
            jsonObject.put("accountName", item.getAccountName());
            jsonObject.put("bankLocationCode", item.getPayeeAccount());
            jsonObject.put("accountNumber", item.getInterbankNumber());
            jsonObject.put("paymentUsedeCode", item.getPayPurpose());
            jsonObject.put("headDescription", vo.getRemark());
            jsonObject.put("description", item.getRemark());
            jsonObject.put("amount", item.getThisPayAmount());
            jsonObject.put("documentNumber", item.getContractCode());
            jsonObject.put("tpmPmtLineNumber", item.getLineCode());
            jsonArray.add(jsonObject);
        });

        return jsonArray;
    }

    /**
     * 费用返回数据
     *
     * @param result
     */
    private void buildHecResultData(Result<String> result, ActivityPrepayDto dto) {
        Assert.hasLength(result.getResult(), "费控返回信息为空1!");
        JSONObject jsonObject = JSONObject.parseObject(result.getResult());
        Assert.notNull(jsonObject, "费控返回信息为空2!");
        Assert.isTrue(jsonObject.containsKey("status"), "费控返回信息缺少[status]字段!");
        Assert.isTrue(jsonObject.containsKey("message"), "费控返回信息缺少[message]字段!");
        Assert.isTrue(String.valueOf(com.biz.crm.business.common.sdk.constant.CommonConstant.SC_OK_200)
                .equals(jsonObject.getString("status")), "费控返回信息:" + jsonObject.get("message"));
        Assert.isTrue(jsonObject.containsKey("payload"), "费控返回信息缺少[payload]对象!");
        JSONObject payload = jsonObject.getJSONObject("payload");
        Assert.notNull(payload, "费控返回信息[payload]对象为空");
        Assert.isTrue(payload.containsKey("status"), "费控返回信息缺少[payload]对象缺少[status]字段!");
        Assert.isTrue(payload.containsKey("message"), "费控返回信息缺少[payload]对象缺少[message]字段!");
        Assert.isTrue(ExternalLogGlobalConstants.S.equals(payload.getString("status")), "费控返回信息:" + payload.get("message"));
        Assert.isTrue(payload.containsKey("result"), "费控返回信息缺少[payload]对象缺少[result]字段!");
        if (HecOperationTypeEnum.CREATE.getCode().equals(dto.getHecOperationType())
                || HecOperationTypeEnum.UPDATE.getCode().equals(dto.getHecOperationType())) {
            JSONArray resultJson = payload.getJSONArray("result");
            Assert.notNull(resultJson, "费控返回信息[result]对象为空1");
            Assert.notEmpty(resultJson, "费控返回信息[result]对象为空2");
            String hecReceiptNumber = resultJson.getJSONObject(0).getString("hecReceiptNumber");
            String hecReceiptUrl = resultJson.getJSONObject(0).getString("hecReceiptUrl");
            Assert.hasLength(hecReceiptNumber, "费控返回信息[result]内[hecReceiptNumber]为空");
            Assert.hasLength(hecReceiptUrl, "费控返回信息[result]内[hecReceiptUrl]为空");
            ActivityPrepay activityPrepay = activityPrepayRepository.findByCode(dto.getPrepayCode());
            activityPrepay.setProcessKey(hecReceiptNumber);
            activityPrepay.setHecReceiptUrl(hecReceiptUrl);
            activityPrepay.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
            activityPrepay.setProcessDate(new Date());
            activityPrepayRepository.updateById(activityPrepay);
        }

    }
}
