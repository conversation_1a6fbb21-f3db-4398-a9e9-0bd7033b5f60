package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.eunm.ExternalSystemEnum;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.base.util.ryytn.RySignHeaderUtil;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.local.service.MarketingAuditInvoiceSapService;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditSapInvoiceVo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 结案票扣明细  SAP数据
 *
 * <AUTHOR>
 * @version v1.0.0
 * @date 2024/9/29 15:57
 */
@Slf4j
@Service
public class MarketingAuditInvoiceSapServiceImpl implements MarketingAuditInvoiceSapService {

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    /**
     * 客户+公司+活动归属年月 获取SAP数据
     *
     * @return
     * <AUTHOR>
     * @date 2024/9/29 16:03
     */
    @Override
    public List<MarketingAuditSapInvoiceVo> getSapCustomerUnInvoicedInfo(List<JSONObject> customerJsonList) {
        if (CollectionUtil.isEmpty(customerJsonList)) {
            return Lists.newArrayList();
        }
        log.info("=====>   获取客户未开票信息 start    <=====");
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_SAP_ACCOUNT);
        String url = urlAddressVo.getUrl();
        String interfaceAddress = String.format(RyConstant.SAP_INTERFACE_ADDRESS, urlAddressVo.getBusinessKey(), urlAddressVo.getEnvironment());
        List<MarketingAuditSapInvoiceVo> resultList = Lists.newArrayList();
        try {
            String bodyJson = buildBodyJson(customerJsonList);
            ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(bodyJson, urlAddressVo);
            Map<String, String> headMap = RySignHeaderUtil.getSignHeadMap(urlAddressVo.getAccessId(), urlAddressVo.getSecretKey(), interfaceAddress);
            logDetailDto.setReqHead(JSON.toJSONString(headMap));
            logDetailDto.setMethod("ZSTDINF014");
            logDetailDto.setRequestUri(interfaceAddress);
            logDetailDto.setMethodMsg("获取客户未开票信息");
            externalLogVoService.addOrUpdateLog(logDetailDto, true);
            Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, bodyJson, headMap);
            ExternalLogUtil.buildLogResult(logDetailDto, result);
            logDetailDto.setStatus(ExternalLogGlobalConstants.E);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
            if (result.isSuccess() && StringUtil.isNotEmpty(result.getResult())) {
                JSONObject jsonObject = JSONObject.parseObject(result.getResult());
                if (jsonObject.containsKey(RyConstant.SAP_HEAD_DATA) && Objects.nonNull(jsonObject.getJSONObject(RyConstant.SAP_HEAD_DATA))) {
                    JSONObject head = jsonObject.getJSONObject(RyConstant.SAP_HEAD_DATA);
                    String success = head.getString(RyConstant.SAP_HEAD_KEY);
                    String msg = head.getString(RyConstant.SAP_HEAD_MSG);
                    logDetailDto.setTipMsg(msg);
                    if (!ExternalLogGlobalConstants.S.equals(success)) {
                        externalLogVoService.addOrUpdateLog(logDetailDto, false);
                        return resultList;
                    }
                } else {
                    logDetailDto.setTipMsg("SAP返回的数据结构异常!");
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                    return resultList;
                }
                logDetailDto.setStatus(ExternalLogGlobalConstants.S);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
                if (jsonObject.containsKey(RyConstant.SAP_DETAIL_DATA) && CollectionUtil.isNotEmpty(jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA))) {
                    JSONArray jsonArray = jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA);
                    jsonArray.forEach(o -> {
                        MarketingAuditSapInvoiceVo invoiceVo = new MarketingAuditSapInvoiceVo();
                        JSONObject dataJson = (JSONObject) o;
                        invoiceVo.setErpCode(dataJson.getString("KUNRG"));
                        invoiceVo.setCompanyCode(dataJson.getString("BUKRS"));
                        invoiceVo.setProductCode(dataJson.getString("MATNR"));
                        invoiceVo.setUnInvoicedAmount(dataJson.getBigDecimal("ZJSSYKPJE"));
                        invoiceVo.setYearMonthLy(dataJson.getString("ZGJAHR"));
                        invoiceVo.setCustomerCode(invoiceVo.getErpCode() + invoiceVo.getCompanyCode() + "0000");
                        resultList.add(invoiceVo);
                    });
                }
            } else {
                log.error("{}", result);
            }
            log.info("=====>   获取客户未开票信息  end    <=====");
        } catch (Exception e) {
            log.error("=====>   获取客户未开票信息  end 失败    <=====");
            log.error(e.getMessage(), e);
            throw e;
        }
        return resultList;
    }


    /**
     * 构建body参数
     *
     * @return
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/27 15:17
     */
    private String buildBodyJson(List<JSONObject> customerJsonList) {

        String dateNow = DateUtil.dateStrNowYYYYMMDD();
        JSONObject ctrl = new JSONObject();
        ctrl.put("SYSID", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("REVID", ExternalSystemEnum.SAP.getCode());
        ctrl.put("FUNID", "ZSTDINF014");
        ctrl.put("INFID", UuidCrmUtil.general());
        ctrl.put("UNAME", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("DATUM", dateNow);
        ctrl.put("UZEIT", DateUtil.dateStrNowHms());
        ctrl.put("KEYID", "");
        ctrl.put("TABIX", 0);
        ctrl.put("MSGTY", "");
        ctrl.put("MSAGE", "");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(RyConstant.SAP_HEAD_DATA, ctrl);
        jsonObject.put(RyConstant.SAP_DETAIL_DATA, customerJsonList);
        return jsonObject.toJSONString();
    }

}
