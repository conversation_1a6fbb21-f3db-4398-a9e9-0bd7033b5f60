package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord;
import com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayDetailRecordMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 活动预付明细跟踪(ActivityPrepayDetailRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-05 15:40:38
 */
@Component
public class ActivityPrepayDetailRecordRepository extends ServiceImpl<ActivityPrepayDetailRecordMapper, ActivityPrepayDetailRecord> {

    @Autowired
    private ActivityPrepayDetailRecordMapper activityPrepayDetailRecordMapper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired
    private FeeCashPrepayRepository feeCashPrepayRepository;

    public Page<ActivityPrepayDetailRecordVo> findListByCondition(Page<ActivityPrepayDetailRecordVo> page, ActivityPrepayDetailRecordDto dto) {
        return this.baseMapper.findListByCondition(page, dto);
    }

    public List<ActivityPrepayDetailRecordVo> findByDto(ActivityPrepayDetailRecordDto dto) {
        if (CollectionUtil.isEmpty(dto.getSchemeDetailCodeList())
                && StringUtil.isEmpty(dto.getPayeeCode())
                && CollectionUtil.isEmpty(dto.getPayeeCodes())) {
            return Lists.newArrayList();
        }
        List<ActivityPrepayDetailRecord> list = lambdaQuery()
                .eq(ActivityPrepayDetailRecord::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(StringUtils.isNotBlank(dto.getPayeeCode()), ActivityPrepayDetailRecord::getPayeeCode, dto.getPayeeCode())
                .in(!CollectionUtils.isEmpty(dto.getPayeeCodes()), ActivityPrepayDetailRecord::getPayeeCode, dto.getPayeeCodes())
                .in(!CollectionUtils.isEmpty(dto.getSchemeDetailCodeList()), ActivityPrepayDetailRecord::getSchemeDetailCode, dto.getSchemeDetailCodeList())
                .eq(StringUtils.isNotBlank(dto.getPayStatus()), ActivityPrepayDetailRecord::getPayStatus, dto.getPayStatus())
                .in(!CollectionUtils.isEmpty(dto.getPayStatusList()),ActivityPrepayDetailRecord::getPayStatus,dto.getPayStatusList())
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<ActivityPrepayDetailRecord> collect = list.stream().filter(e -> e.getAvailableReversedAmount() != null && e.getAvailableReversedAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(collect, ActivityPrepayDetailRecord.class, ActivityPrepayDetailRecordVo.class, LinkedHashSet.class, ArrayList.class));
    }

    public ActivityPrepayDetailRecord findByCode(String code) {
        return lambdaQuery().eq(ActivityPrepayDetailRecord::getPrepayDetailCode, code).one();
    }

    public List<ActivityPrepayDetailRecord> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        return lambdaQuery().in(ActivityPrepayDetailRecord::getPrepayDetailCode, codes).list();
    }

    public List<ActivityPrepayDetailRecord> findByAuditDetailCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        List<ActivityPrepayDetailRecord> list = Lists.newArrayList();
        List<List<String>> partitionList = Lists.partition(codes, 800);
        for (List<String> partition : partitionList) {
            List<ActivityPrepayDetailRecord> part = activityPrepayDetailRecordMapper.findListByAuditDetailCodes(partition);
            if (!CollectionUtils.isEmpty(part)) {
                list.addAll(part);
            }
        }
        return list;
    }

    public void updatePayStatus(List<HecCallbackDto> dtoList) {
        activityPrepayDetailRecordMapper.updatePayStatus(dtoList);
    }

    public List<ActivityPrepayDetailRecord> findCalList(ActivityPrepayVo vo) {
        return this.baseMapper.findCalList(vo);
    }

    public List<ActivityPrepayDetailRecord> findBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return Lists.newArrayList();
        }
        return activityPrepayDetailRecordMapper.findListBySchemeDetailCodes(schemeDetailCodes);
    }

    public List<MarketingAuditDetailVo> findAuditedAmountBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return Lists.newArrayList();
        }
        List<MarketingAuditDetailVo> dataList = this.baseMapper.findAuditedAmountBySchemeDetailCodes(schemeDetailCodes);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        Map<String, MarketingAuditDetailVo> map = dataList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBeFullAudit())
                        && BooleanEnum.TRUE.getCapital().equals(x.getBeFullAudit()))
                .collect(Collectors.toMap(x -> x.getSchemeDetailCode(), Function.identity(), (a, b) -> a));
        return map.values().stream().collect(Collectors.toList());
    }

    public Page<ActivityPrepayDetailRecordVo> findByPayeeCode(Pageable pageable, ActivityPrepayDetailRecordDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        dto = ObjectUtils.defaultIfNull(dto, new ActivityPrepayDetailRecordDto());
        Page<ActivityPrepayDetailRecordVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<ActivityPrepayDetailRecordVo> recordVoPage = this.baseMapper.findByPayeeCode(page, dto);
        if (!CollectionUtils.isEmpty(recordVoPage.getRecords()) && StringUtils.isNotBlank(dto.getCashCode())) {
            List<FeeCashPrepayVo> feeCashPrepayVos = feeCashPrepayRepository.findByCode(dto.getCashCode());
            if (!CollectionUtils.isEmpty(feeCashPrepayVos)) {
                Map<String, FeeCashPrepayVo> cashPrepayVoMap = feeCashPrepayVos.stream().collect(Collectors.toMap(FeeCashPrepayVo::getPrepayDetailCode, Function.identity(), (a, b) -> a));
                recordVoPage.getRecords().forEach(e -> {
                    if (cashPrepayVoMap.containsKey(e.getPrepayDetailCode())) {
                        e.setThisReversedAmount(cashPrepayVoMap.get(e.getPrepayDetailCode()).getThisReversedAmount());
                    }
                });
            }
        }
        return recordVoPage;
    }


    public List<ActivityPrepayDetail> findAlreadySchemeDetailCodeByCondition(String prepayCode, List<String> schemeDetailCodes) {
        return this.baseMapper.findAlreadySchemeDetailCodeByCondition(prepayCode, schemeDetailCodes);
    }

    public List<ActivityPrepayDetailRecord> getAllRecord(  Set<String> collect) {
        List<ActivityPrepayDetailRecord> list = lambdaQuery()
                .eq(ActivityPrepayDetailRecord::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(!CollectionUtils.isEmpty(collect), ActivityPrepayDetailRecord::getPrepayDetailCode, collect)
                .list();
        return list;
    }

    public List<ActivityPrepayDetailRecord> findReportBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findReportBySchemeDetailCodes(schemeDetailCodes);
    }

    public List<ActivityPrepayDetailRecord> findO2OPrepayDetailRecord(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findO2OPrepayDetailRecord(schemeDetailCodes);
    }
}

