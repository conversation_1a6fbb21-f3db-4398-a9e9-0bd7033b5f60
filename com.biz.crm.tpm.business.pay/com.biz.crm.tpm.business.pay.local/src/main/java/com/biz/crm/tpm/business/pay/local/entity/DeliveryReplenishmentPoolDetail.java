package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "tpm_delivery_replenishment_pool_detail", description = "发货费用报表")
@TableName("tpm_delivery_replenishment_pool_detail")
@Getter
@Setter
@Entity(name = "tpm_delivery_replenishment_pool_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_delivery_replenishment_pool_detail", comment = "发货费用报表")
@Table(name = "tpm_delivery_replenishment_pool_detail", indexes = {
        @Index(name = "replenishment_pool_detail_idx1", columnList = "pool_detail_code"),
        @Index(name = "replenishment_pool_detail_idx2", columnList = "business_code"),
        @Index(name = "replenishment_pool_detail_idx3", columnList = "parent_code"),
        @Index(name = "replenishment_pool_detail_idx4", columnList = "operation_type"),
        @Index(name = "replenishment_pool_detail_idx5", columnList = "scheme_detail_code"),
        @Index(name = "replenishment_pool_detail_idx6", columnList = "audit_detail_code"),
        @Index(name = "replenishment_pool_detail_idx7", columnList = "cash_detail_code"),
})
public class DeliveryReplenishmentPoolDetail extends TenantFlagOpEntity {

    @ApiModelProperty("单据编码")
    @Column(name = "rule_code", columnDefinition = "varchar(32) commnet '单据编码'")
    private String ruleCode;


    /**
     * 费用池编号
     */
    @ApiModelProperty(value = "费用池编号")
    @Column(name = "pool_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '费用池编号'")
    private String poolCode;

    /**
     * 费用池明细编号
     */
    @ApiModelProperty(value = "费用池明细编号")
    @Column(name = "pool_detail_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '费用池明细编号'")
    private String poolDetailCode;

    @ApiModelProperty(value = "费用池明细编号")
    @Column(name = "parent_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '费用池明细编号'")
    private String parentCode;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
    private String customerName;

    /**
     * @see com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolTypeEnum
     * 货补费用池类型
     */
    @Column(name = "replenishment_pool_type", length = 32, columnDefinition = "VARCHAR(32) COMMENT '货补费用池类型'")
    @ApiModelProperty(value = "货补费用池类型")
    private String replenishmentPoolType;

    @ApiModelProperty(value = "出库单")
    @Column(name = "business_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '出库单'")
    private String businessCode;

    @ApiModelProperty(value = "操作类型")
    @Column(name = "operation_type", length = 32, columnDefinition = "VARCHAR(32) COMMENT '业务编码'")
    private String operationType;

    @ApiModelProperty(value = "业务编码")
    @Column(name = "parent_business_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '业务编码'")
    private String parentBusinessCode;

    @ApiModelProperty(value = "出库带走费用")
    @Column(name = "operation_amount", columnDefinition = "decimal(20,4) COMMENT '出库带走费用'")
    private BigDecimal operationAmount;

    @ApiModelProperty("出库带走未税费用")
    @Column(name = "operation_not_tax_amount",columnDefinition = "decimal(20,4) comment '出库带走未税费用'")
    private BigDecimal operationNotTaxAmount;

    @ApiModelProperty(value = "出库时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "operation_time", columnDefinition = "datetime COMMENT '出库时间'")
    private Date operationTime;

    @ApiModelProperty("是否应冲销(Y/N)")
    @Column(name = "be_write_off", length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否应冲销(Y/N) '")
    private String beWriteOff;

    @ApiModelProperty(value = "冲销状态")
    @Column(name = "write_off_status", length = 32, columnDefinition = "VARCHAR(32) COMMENT '冲销状态'")
    private String writeOffStatus;

    @ApiModelProperty(value = "管报冲销状态")
    @Column(name = "manage_report_write_off_status", length = 32, columnDefinition = "VARCHAR(32) DEFAULT 'wait_write_off' COMMENT '管报冲销状态'")
    private String manageReportWriteOffStatus;

    @ApiModelProperty("唯一标识")
    @Column(name = "unique_key", columnDefinition = "VARCHAR(128) COMMENT '唯一标识'")
    private String uniqueKey;

    @ApiModelProperty("职位编码")
    @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
    private String positionCode;

    @ApiModelProperty("发货时间")
    @Column(name = "delivery_time", length = 32, columnDefinition = "varchar(32) COMMENT '发货时间'")
    private String deliveryTime;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", length = 32, columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("兑付编号")
    @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
    private String cashCode;

    @ApiModelProperty("兑付明细编号")
    @Column(name = "cash_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付明细编号 '")
    private String cashDetailCode;

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("核销申请编号")
    @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
    private String auditCode;

    @ApiModelProperty("核销明细编号")
    @Column(name = "audit_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销明细编号 '")
    private String auditDetailCode;

    @Transient
    @TableField(exist = false)
    private String tempCode;
}
