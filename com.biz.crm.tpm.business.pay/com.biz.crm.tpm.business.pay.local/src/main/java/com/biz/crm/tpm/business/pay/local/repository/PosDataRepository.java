package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.PosData;
import com.biz.crm.tpm.business.pay.local.mapper.PosDataMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Component
public class PosDataRepository extends ServiceImpl<PosDataMapper, PosData> {


    public Page<PosDataVo> findList(Page<PosDataVo> page,PosDataVo vo){
        vo.setTenantCode(TenantUtils.getTenantCode());
        return this.baseMapper.findList(page,vo);
    }

    public List<PosData> findListByIdList(List<String> idList){
        return this.lambdaQuery()
                .in(PosData::getId,idList)
                .eq(PosData::getTenantCode,TenantUtils.getTenantCode())
                .eq(PosData::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }
}
