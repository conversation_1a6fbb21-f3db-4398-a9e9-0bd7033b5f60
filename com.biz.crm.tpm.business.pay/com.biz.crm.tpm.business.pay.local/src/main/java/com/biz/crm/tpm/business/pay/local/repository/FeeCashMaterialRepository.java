package com.biz.crm.tpm.business.pay.local.repository;



import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashMaterial;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashMaterialMapper;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashMaterialVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 采购需求信息(TpmFeeCashMaterial)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-27 17:47:41
 */
@Component
public class FeeCashMaterialRepository extends ServiceImpl<FeeCashMaterialMapper, FeeCashMaterial> {

  @Autowired
  private FeeCashMaterialMapper feeCashMaterialMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  
  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<FeeCashMaterialVo> findByCode(String code) {
    List<FeeCashMaterial> list = this.lambdaQuery().eq(FeeCashMaterial::getCashCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashMaterial.class, FeeCashMaterialVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param codes
   */
  public void deleteByCodes(List<String> codes) {
    this.remove(Wrappers.lambdaQuery(FeeCashMaterial.class)
            .in(FeeCashMaterial::getCashCode, codes));
  }
}

