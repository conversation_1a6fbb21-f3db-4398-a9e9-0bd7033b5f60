package com.biz.crm.tpm.business.pay.local.service;

import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo;

import java.util.List;

public interface WithHoldingIncomeService {

    List<WithHoldingIncomeVo> findByYear(List<String> year);

    List<WithHoldingIncomeVo> findListByCondition(WithholdingIncomeQueryDto dto);

    List<WithHoldingIncomeVo> findListByYearsAndChildrenOrgCodes(WithholdingIncomeQueryDto dto);
}
