package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "FeeCashToc", description = "TOC支付明细")
@TableName("tpm_fee_cash_toc")
@Getter
@Setter
@Entity(name = "tpm_fee_cash_toc")
@org.hibernate.annotations.Table(appliesTo = "tpm_fee_cash_toc", comment = "TOC支付明细")
@Table(name = "tpm_fee_cash_toc", indexes = {@Index(name = "fee_cash_idx1", columnList = "cash_code")})
public class FeeCashToc extends TenantFlagOpEntity {

    @ApiModelProperty("兑付名称")
    @Column(name = "cash_name", columnDefinition = "VARCHAR(255) COMMENT '兑付名称 '")
    private String cashName;

    @ApiModelProperty("兑付编号")
    @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
    private String cashCode;

    @ApiModelProperty("打款平台")
    @Column(name = "platform", columnDefinition = "varchar(32) comment '打款平台'")
    private String platform;

    @ApiModelProperty("姓名")
    @Column(name = "payee_name", columnDefinition = "VARCHAR(255) COMMENT '姓名 '")
    private String payeeName;

    @ApiModelProperty("银行账号")
    @Column(name = "bank_no", columnDefinition = "VARCHAR(128) COMMENT '银行账号'")
    private String bankNo;

    @ApiModelProperty("身份证号码")
    @Column(name = "id_card", columnDefinition = "varchar(32) comment '身份证号码'")
    private String idCard;

    @ApiModelProperty("手机号")
    @Column(name = "phone", columnDefinition = "varchar(32) comment '手机号'")
    private String phone;

    @ApiModelProperty("收款金额")
    @Column(name = "payee_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '收款金额 '")
    private BigDecimal payeeAmount;

    @ApiModelProperty("实际打款金额")
    @Column(name = "pay_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '实际打款金额 '")
    private BigDecimal payAmount;
}
