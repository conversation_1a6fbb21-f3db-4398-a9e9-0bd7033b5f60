package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动预付明细(ActivityPrepayDetail)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-04 20:03:02
 */
public interface ActivityPrepayDetailMapper extends BaseMapper<ActivityPrepayDetail> {

    List<ActivityPrepayDetail> findCalList(@Param("vo") ActivityPrepayVo vo);

    List<ActivityPrepayDetail> findBySchemeDetailCodesBeCommit(@Param("codes") List<String> codes);
}

