package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.tpm.business.pay.local.service.WithHoldingVoService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingSdkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class WithHoldingSdkServiceImpl implements WithHoldingSdkService {

    @Autowired(required = false)
    WithHoldingVoService withHoldingVoService;

    /**
     * 根据活动明细编码查询计提金额
     *
     * @param codes
     * @return
     */
    @Override
    public Map<String, BigDecimal> findBySchemeDetailCodes(List<String> codes) {
        return withHoldingVoService.findBySchemeDetailCodes(codes);
    }


    /**
     * 根据业务明细编码查询管报实际金额
     *
     * @param codes
     * @return
     */
    @Override
    public Map<String, BigDecimal> findByBusinessCodes(List<String> codes) {
        return withHoldingVoService.findByBusinessCodes(codes);
    }
}
