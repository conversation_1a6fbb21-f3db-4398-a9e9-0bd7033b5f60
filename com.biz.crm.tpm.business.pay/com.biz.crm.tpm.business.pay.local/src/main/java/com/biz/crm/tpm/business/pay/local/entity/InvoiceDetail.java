package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@TableName("tpm_invoice_details")
@Table(name = "tpm_invoice_details", indexes = {@Index(name = "tpm_invoice_detail_index1", columnList = "invoice_no")})
@ApiModel(value = "InvoiceDetail", description = "发票使用明细")
@org.hibernate.annotations.Table(appliesTo = "tpm_invoice_details", comment = "发票使用明细")
public class InvoiceDetail extends UuidFlagOpEntity {

    @ApiModelProperty("发票明细号")
    @Column(name = "invoice_detail_no", columnDefinition = "varchar(128) comment '发票明细编码'")
    private String invoiceDetailNo;

    @ApiModelProperty("发票号码(只允许填写数字和字母)")
    @TableField(value = "invoice_no")
    @Column(name = "invoice_no", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '发票号码(只允许填写数字和字母)'")
    private String invoiceNo;

    @ApiModelProperty("货物或应税劳务名称")
    @Column(name = "name", columnDefinition = "varchar(256) comment '货物或应税劳务名称'")
    private String name;

    @ApiModelProperty("税价合计")
    @TableField(value = "price_and_tax")
    @Column(name = "price_and_tax", columnDefinition = "decimal(20,4) COMMENT '税价合计'")
    private BigDecimal priceAndTax;

    @ApiModelProperty("不含税金额")
    @TableField(value = "amount_without_tax")
    @Column(name = "amount_without_tax", columnDefinition = "decimal(20,4) COMMENT '不含税金额'")
    private BigDecimal amountWithoutTax;

    @ApiModelProperty("税额")
    @TableField(value = "tax_amount")
    @Column(name = "tax_amount", columnDefinition = "decimal(20,4) COMMENT '税额'")
    private BigDecimal taxAmount;

    @ApiModelProperty("是否被使用Y/N")
    @Column(name = "used", columnDefinition = "varchar(4) comment '是否被使用Y/N'")
    private String used;

    @ApiModelProperty("税率")
    @Column(name = "tax_rate", columnDefinition = "decimal(12,4) comment '税率'")
    private BigDecimal taxRate;

}
