package com.biz.crm.tpm.business.pay.local.register;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * @className: com.biz.crm.tpm.business.pay.local.register.PlanExecutionViewRegister
 * @description: 终端活动执行数据视图
 * @author: xiaopeng.zhang
 * @create: 2024-08-03 9:56
 */
@Component
public class PlanExecutionDataViewRegister implements DataviewRegister {
    @Override
    public String code() {
        return "tpm_plan_execution_data_view";
    }

    @Override
    public String desc() {
        return "终端活动执行数据视图";
    }

    @Override
    public String buildSql() {
        return "SELECT t.* " +
                "    FROM tpm_plan_exceution t " +
                "    WHERE t.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' ";
    }
}
