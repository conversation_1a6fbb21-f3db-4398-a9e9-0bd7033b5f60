package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepay;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动预付(ActivityPrepay)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-04 20:03:02
 */
public interface ActivityPrepayMapper extends BaseMapper<ActivityPrepay> {

    /**
     * 更新付款状态
     *
     * @param dtoList
     */
    void hecPayStatusCallback(@Param("dtoList") List<HecCallbackDto> dtoList);
}

