package com.biz.crm.tpm.business.pay.local.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashPrepayRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashDetailService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPrepayVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 费用兑付预付明细分页缓存
 */
@Slf4j
@Component
public class FeeCashPrepayPageCacheHelper extends BusinessPageCacheHelper<FeeCashPrepayVo, FeeCashPrepayDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private FeeCashPrepayRepository feeCashPrepayRepository;
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private FeeCashDetailService feeCashDetailService;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return FeeCashConstant.CACHE_KEY_PREFIX_PREPAY;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<FeeCashPrepayDto> getDtoClass() {
        return FeeCashPrepayDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<FeeCashPrepayVo> getVoClass() {
        return FeeCashPrepayVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param feeCashPrepayDto
     * @param cacheKey
     */
    @Override
    public List<FeeCashPrepayDto> findDtoListFromRepository(FeeCashPrepayDto feeCashPrepayDto, String cacheKey) {
        if (StringUtils.isBlank(feeCashPrepayDto.getCashCode())) {
            return new ArrayList<>();
        }
        String payeeCode = feeCashPrepayDto.getPayeeCode();
        List<FeeCashDetailDto> cacheList = feeCashDetailService.findCacheList(cacheKey);
        List<FeeCashPrepayVo> feeCashPrepayVos = feeCashPrepayRepository.findByCodeAndPayeeCodeAndCacheList(feeCashPrepayDto.getCashCode(),payeeCode,cacheList);
        return CollectionUtils.isNotEmpty(feeCashPrepayVos) ? (List<FeeCashPrepayDto>) nebulaToolkitService.copyCollectionByBlankList(feeCashPrepayVos, FeeCashPrepayVo.class, FeeCashPrepayDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashPrepayDto> newItem(String cacheKey, List<FeeCashPrepayDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashPrepayDto> copyItem(String cacheKey, List<FeeCashPrepayDto> itemList) {
        List<FeeCashPrepayDto> newItemList = (List<FeeCashPrepayDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, FeeCashPrepayDto.class, FeeCashPrepayDto.class, HashSet.class, ArrayList.class);
        for (FeeCashPrepayDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    @Override
    public List<FeeCashPrepayVo> dtoListToVoList(List<FeeCashPrepayDto> dtoList) {
        return JSONObject.parseArray(JSONObject.toJSONString(dtoList), this.getVoClass())
                .stream().sorted(Comparator.comparing(FeeCashPrepayVo::getPrepayDetailCode)).collect(Collectors.toList());
    }

    /**
     * 获取Dto中的主键
     *
     * @param feeCashPrepayDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(FeeCashPrepayDto feeCashPrepayDto) {
        return feeCashPrepayDto.getPrepayDetailCode();
    }

    /**
     * 获取是否选中状态
     *
     * @param feeCashPrepayDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(FeeCashPrepayDto feeCashPrepayDto) {
        return feeCashPrepayDto.getChecked();
    }

}
