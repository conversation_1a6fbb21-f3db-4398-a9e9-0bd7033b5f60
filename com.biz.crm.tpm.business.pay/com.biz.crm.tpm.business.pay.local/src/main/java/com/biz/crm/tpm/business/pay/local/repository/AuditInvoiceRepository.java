package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.AuditInvoice;
import com.biz.crm.tpm.business.pay.local.mapper.AuditInvoiceMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditInvoiceVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 费用核销发票;(tpm_audit_invoice)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-7-6
 */
@Component
public class AuditInvoiceRepository extends ServiceImpl<AuditInvoiceMapper, AuditInvoice> {
  @Autowired
  private AuditInvoiceMapper auditInvoiceMapper;

  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  public Page<AuditInvoiceVo> findByConditions(Pageable pageable, AuditInvoiceDto dto) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<AuditInvoiceVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return auditInvoiceMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<AuditInvoice>
   */
  public List<AuditInvoice> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(AuditInvoice::getId, ids)
            .eq(AuditInvoice::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根绝业务编号auditCode获取业务数据
   *
   * @return 返单条数据
   */
  public List<AuditInvoice> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(AuditInvoice::getAuditCode, auditCode)
            .eq(AuditInvoice::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 删除数据
   */
  public boolean deleteByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(AuditInvoice::getAuditCode, auditCode)
            .eq(AuditInvoice::getTenantCode, tenantCode)
            .remove();
  }

  /**
   * 删除数据
   */
  public boolean deleteByInvoiceNo(String invoiceNo) {
    if (StringUtils.isBlank(invoiceNo)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(AuditInvoice::getInvoiceNo, invoiceNo)
            .eq(AuditInvoice::getTenantCode, tenantCode)
            .remove();
  }


  public AuditInvoice findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(AuditInvoice::getTenantCode,tenantCode)
        .eq(AuditInvoice::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(AuditInvoice::getTenantCode,tenantCode)
        .in(AuditInvoice::getId,ids)
        .remove();
  }
}