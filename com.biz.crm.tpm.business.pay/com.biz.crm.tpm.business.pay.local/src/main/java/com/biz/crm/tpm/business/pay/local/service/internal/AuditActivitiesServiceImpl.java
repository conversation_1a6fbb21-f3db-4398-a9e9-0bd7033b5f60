package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditActivities;
import com.biz.crm.tpm.business.pay.local.repository.AuditActivitiesRepository;
import com.biz.crm.tpm.business.pay.local.service.AuditActivitiesService;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditActivitiesDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditActivitiesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 费用核销关联活动;(tpm_audit_activities)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Service("auditActivitiesService")
public class AuditActivitiesServiceImpl implements AuditActivitiesService {
  @Autowired
  private AuditActivitiesRepository auditActivitiesRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<AuditActivitiesVo> findByConditions(Pageable pageable, AuditActivitiesDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new AuditActivitiesDto();
    }
    return this.auditActivitiesRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public AuditActivitiesVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    AuditActivities auditActivities = this.auditActivitiesRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (auditActivities == null) {
      return null;
    }
    AuditActivitiesVo auditActivitiesVo = this.nebulaToolkitService.copyObjectByWhiteList(auditActivities, AuditActivitiesVo.class, LinkedHashSet.class, ArrayList.class);
    return auditActivitiesVo;
  }

  /**
   * 通过编号查询单条数据
   *
   * @param auditCode 主键
   * @return 单条数据
   */
  @Override
  public List<AuditActivitiesVo> findByAuditCode(String auditCode) {
    if (StringUtils.isBlank(auditCode)) {
      return Collections.emptyList();
    }
    List<AuditActivities> auditActivities = this.auditActivitiesRepository.findByAuditCode(auditCode);
    if (CollectionUtils.isEmpty(auditActivities)) {
      return Collections.emptyList();
    }
    Collection<AuditActivitiesVo> auditActivitiesVos = this.nebulaToolkitService.copyCollectionByWhiteList(auditActivities, AuditActivities.class, AuditActivitiesVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(auditActivitiesVos);
  }

  /**
   * 新增数据
   *
   * @param auditActivitiesDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public AuditActivitiesVo create(AuditActivitiesDto auditActivitiesDto) {
    this.createValidate(auditActivitiesDto);
    AuditActivities auditActivities = this.nebulaToolkitService.copyObjectByWhiteList(auditActivitiesDto, AuditActivities.class, LinkedHashSet.class, ArrayList.class);
    auditActivities.setTenantCode(TenantUtils.getTenantCode());
    this.auditActivitiesRepository.saveOrUpdate(auditActivities);
    AuditActivitiesVo auditActivitiesVo = this.nebulaToolkitService.copyObjectByWhiteList(auditActivities, AuditActivitiesVo.class, LinkedHashSet.class, ArrayList.class);

    auditActivitiesVo.setId(auditActivities.getId());
    return auditActivitiesVo;
  }

  /**
   * 修改新据
   *
   * @param auditActivitiesDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public AuditActivitiesVo update(AuditActivitiesDto auditActivitiesDto) {
    this.updateValidate(auditActivitiesDto);
    AuditActivities auditActivities = this.auditActivitiesRepository.findByIdAndTenantCode(auditActivitiesDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(auditActivities, "修改数据不存在，请检查！");
    auditActivities.setActivitiesCode(auditActivitiesDto.getActivitiesCode());
    auditActivities.setActivitiesName(auditActivitiesDto.getActivitiesName());
    auditActivities.setBeginTime(auditActivitiesDto.getBeginTime());
    auditActivities.setEndTime(auditActivitiesDto.getEndTime());
    auditActivities.setAuditCode(auditActivitiesDto.getAuditCode());
    auditActivities.setTenantCode(TenantUtils.getTenantCode());
    this.auditActivitiesRepository.saveOrUpdate(auditActivities);
    AuditActivitiesVo auditActivitiesVo = this.nebulaToolkitService.copyObjectByWhiteList(auditActivities, AuditActivitiesVo.class, LinkedHashSet.class, ArrayList.class);
    return auditActivitiesVo;
  }

  @Transactional
  @Override
  public List<AuditActivitiesVo> createBatch(List<AuditActivitiesDto> auditActivitiesDtos) {
    if (CollectionUtils.isEmpty(auditActivitiesDtos)) {
      return Lists.newArrayList();
    }
    List<AuditActivitiesVo> auditActivitiesVos = Lists.newArrayList();
    for (AuditActivitiesDto auditActivitiesDto : auditActivitiesDtos) {
      AuditActivitiesVo activitiesVo = this.create(auditActivitiesDto);
      auditActivitiesVos.add(activitiesVo);
    }
    return auditActivitiesVos;
  }

  @Override
  @Transactional
  public List<AuditActivitiesVo> updateBatch(List<AuditActivitiesDto> auditActivitiesDtos) {
    if (CollectionUtils.isEmpty(auditActivitiesDtos)) {
      return Collections.emptyList();
    }
    String auditCode = auditActivitiesDtos.stream().findFirst().get().getAuditCode();
    List<AuditActivitiesVo> dbAuditActivitiesVos = this.findByAuditCode(auditCode);
    Set<String> dbIds = dbAuditActivitiesVos.stream().map(AuditActivitiesVo::getId).collect(Collectors.toSet());
    Set<String> currentIds = auditActivitiesDtos.stream().map(AuditActivitiesDto::getId).collect(Collectors.toSet());

    // 进行新增，修改、删除数据的分析
    List<AuditActivitiesDto> addData = auditActivitiesDtos.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
    List<AuditActivitiesDto> updateData = auditActivitiesDtos.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
    Set<String> delData = Sets.difference(dbIds, currentIds);

    // 处理删除
    if (!CollectionUtils.isEmpty(delData)) {
      this.delete(Lists.newArrayList(delData));
    }
    // 处理新增
    List<AuditActivitiesVo> auditActivitiesVos = this.createBatch(addData);
    if (!CollectionUtils.isEmpty(updateData)) {
      updateData.forEach(item -> {
        auditActivitiesVos.add(this.update(item));
      });
    }
    return auditActivitiesVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<AuditActivities> auditActivitiess = this.auditActivitiesRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(auditActivitiess)) {
      return;
    }
    this.auditActivitiesRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  /**
   * 根据费用核销编号删除数据
   *
   * @param auditCode 费用核销编号
   * @return 删除结果
   */
  @Override
  @Transactional
  public void deleteByAuditCode(String auditCode) {
    this.auditActivitiesRepository.deleteByAuditCode(auditCode);
  }

  /**
   * 创建验证
   *
   * @param auditActivitiesDto
   */
  private void createValidate(AuditActivitiesDto auditActivitiesDto) {
    Validate.notNull(auditActivitiesDto, "新增时，对象信息不能为空！");
    auditActivitiesDto.setId(null);
    Validate.notBlank(auditActivitiesDto.getActivitiesCode(), "新增数据时，活动编号不能为空！");
    Validate.notBlank(auditActivitiesDto.getActivitiesName(), "新增数据时，活动名称不能为空！");
    Validate.notNull(auditActivitiesDto.getBeginTime(), "新增数据时，开始时间不能为空！");
    Validate.notNull(auditActivitiesDto.getEndTime(), "新增数据时，结束时间不能为空！");
  }

  /**
   * 修改验证
   *
   * @param auditActivitiesDto
   */
  private void updateValidate(AuditActivitiesDto auditActivitiesDto) {
    Validate.notNull(auditActivitiesDto, "修改时，对象信息不能为空！");
    Validate.notBlank(auditActivitiesDto.getId(), "修改时，主键不能为空！");
    Validate.notBlank(auditActivitiesDto.getActivitiesCode(), "修改时，活动编号不能为空！");
    Validate.notBlank(auditActivitiesDto.getActivitiesName(), "修改时，活动名称不能为空！");
    Validate.notNull(auditActivitiesDto.getBeginTime(), "修改时，开始时间不能为空！");
    Validate.notNull(auditActivitiesDto.getEndTime(), "修改时，结束时间不能为空！");
  }
}