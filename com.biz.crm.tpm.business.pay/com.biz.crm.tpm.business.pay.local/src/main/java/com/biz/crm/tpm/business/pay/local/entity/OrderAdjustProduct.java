package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@ApiModel(value = "OrderAdjustProduct", description = "订单调整产品")
@TableName("tpm_order_adjust_product")
@Getter
@Setter
@Entity(name = "tpm_order_adjust_product")
@org.hibernate.annotations.Table(appliesTo = "tpm_order_adjust_product", comment = "订单调整产品")
@Table(name = "tpm_order_adjust_product", indexes = {@Index(name = "credit_order_idx1", columnList = "adjust_code")})
public class OrderAdjustProduct extends TenantFlagOpEntity {

    @ApiModelProperty("调整编码")
    @Column(name = "adjust_code", length = 64, columnDefinition = "varchar(64) COMMENT '调整编码'")
    private String adjustCode;

    @ApiModelProperty("调整名称")
    @Column(name = "adjust_name", length = 128, columnDefinition = "varchar(128) COMMENT '调整名称'")
    private String adjustName;

    @ApiModelProperty("产品编码")
    @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
    private String productCode;

    @ApiModelProperty("产品名称")
    @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
    private String productName;

    @ApiModelProperty("销售单位")
    @Column(name = "sale_unit", columnDefinition = "VARCHAR(32) COMMENT '销售单位'")
    private String saleUnit;

    @ApiModelProperty("金额")
    @Column(name = "amount", columnDefinition = "decimal(20,6) COMMENT '金额'")
    private BigDecimal amount;

}
