package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe: 费用上账
 * @createTime 2022年06月16日 15:41:00
 */
@ApiModel(value = "Account", description = "费用上账主表")
@TableName("tpm_account")
@Getter
@Setter
@Entity(name = "tpm_account")
@org.hibernate.annotations.Table(appliesTo = "tpm_account", comment = "费用上账主表")
@Table(name = "tpm_account")
public class Account extends TenantFlagOpEntity {


  private static final long serialVersionUID = 8111076322024062900L;
  /**
   * 费用上账编码
   */
  @ApiModelProperty(name = "费用上账编码", notes = "费用上账编码")
  @Column(name = "account_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '费用上账编码 '")
  private String accountCode;

  /**
   * 上账状态 枚举
   */
  @ApiModelProperty(name = "上账状态", notes = "上账状态")
  @Column(name = "account_status", length = 64, columnDefinition = "VARCHAR(64) COMMENT '上账状态 '")
  private String accountStatus;

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  @Column(name = "activities_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  @Column(name = "activities_Name", length = 64, columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 活动明细编码
   */
  @ApiModelProperty(name = "activitiesDetailCode", notes = "活动明细编码", value = "活动明细编码")
  @Column(name = "activities_detail_code", length = 64, columnDefinition = "VARCHAR(128) COMMENT '活动明细编码 '")
  private String activitiesDetailCode;

  /**
   * 预算科目编码
   */
  @ApiModelProperty(name = "预算科目编码", notes = "")
  @Column(name = "budget_subjects_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
  private String budgetSubjectsCode;

  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "预算科目名称", notes = "")
  @Column(name = "budget_subjects_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;

  /**
   * 核销申请编码
   */
  @ApiModelProperty(name = "核销编号", notes = "核销编号")
  @Column(name = "audit_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销编号 '")
  private String auditCode;

  /**
   * 核销申请明细编号
   */
  @ApiModelProperty(name = "核销明细编号", notes = "核销明细编号")
  @Column(name = "audit_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销明细编号 '")
  private String auditDetailCode;


  /**
   * 活动大类编码
   */
  @ApiModelProperty(name = "活动大类编码", notes = "活动大类编码")
  @Column(name = "cost_type_category_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动大类编码 '")
  private String costTypeCategoryCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "活动大类名称", notes = "活动大类名称")
  @Column(name = "cost_type_category_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动大类名称 '")
  private String costTypeCategoryName;

  /**
   * 活动细类编码
   */
  @ApiModelProperty(name = "活动细类编码", notes = "活动细类编码")
  @Column(name = "cost_type_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动细类编码 '")
  private String costTypeDetailCode;

  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "活动细类名称", notes = "活动系类名称")
  @Column(name = "cost_type_detail_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动细类名称 '")
  private String costTypeDetailName;

  /**
   * 组织编号
   */
  @ApiModelProperty(name = "组织编号", notes = "组织编号")
  @Column(name = "org_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编号 '")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "组织名称", notes = "组织名称")
  @Column(name = "org_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '组织名称 '")
  private String orgName;

  /**
   * 客户编号
   */
  @ApiModelProperty(name = "客户编号", notes = "客户编号")
  @Column(name = "customer_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '客户编号 '")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty(name = "客户名称", notes = "客户名称")
  @Column(name = "customer_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '客户名称 '")
  private String customerName;

  /**
   * 门店编号
   */
  @ApiModelProperty(name = "门店编号", notes = "门店编号")
  @Column(name = "terminal_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '门店编号 '")
  private String terminalCode;

  /**
   * 门店名称
   */
  @ApiModelProperty(name = "门店名称", notes = "门店名称")
  @Column(name = "terminal_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '门店名称 '")
  private String terminalName;

  /**
   * 可上账金额
   */
  @ApiModelProperty(name = "可上账金额", notes = "可上账金额")
  @Column(name = "available_amount", nullable = false, length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '可上账金额 '")
  private BigDecimal availableAmount;

  /**
   * 核销金额
   */
  @ApiModelProperty(name = "核销金额", notes = "核销金额")
  @Column(name = "audit_amount", nullable = false, length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '核销金额 '")
  private BigDecimal auditAmount;

  /**
   * 上账金额
   */
  @ApiModelProperty(name = "上账金额", notes = "上账金额")
  @Column(name = "amount", nullable = false, length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '上账金额 '")
  private BigDecimal amount;


  /**
   * 商品层级编码
   */
  @ApiModelProperty(name = "商品层级编码", notes = "商品层级编码")
  @Column(name = "product_level_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '商品层级编码 '")
  private String productLevelCode;

  /**
   * 商品层级名称
   */
  @ApiModelProperty(name = "商品层级名称", notes = "商品层级名称")
  @Column(name = "product_level_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '商品层级名称 '")
  private String productLevelName;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "支付方式", notes = "支付方式")
  @Column(name = "pay_by", length = 64, columnDefinition = "VARCHAR(64) COMMENT '支付方式 '")
  private String payBy;

  /**
   * 开始时间
   */
  @ApiModelProperty(name = "开始时间", notes = "开始时间")
  @Column(name = "begin_time", nullable = false, columnDefinition = "DATETIME COMMENT '开始时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @ApiModelProperty(name = "结束时间", notes = "结束时间")
  @Column(name = "end_time", nullable = false, columnDefinition = "DATETIME COMMENT '结束时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  /**
   * ERP会计科目编码
   */
  @ApiModelProperty(name = "ERP会计科目编码", notes = "")
  @Column(name = "accounting_subjects_code", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT 'ERP会计科目编码 '")
  private String accountingSubjectsCode;

  /**
   * ERP会计科目名称
   */
  @ApiModelProperty(name = "ERP会计科目名称", notes = "")
  @Column(name = "accounting_subjects_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT 'ERP会计科目名称 '")
  private String accountingSubjectsName;

  /**
   * 上账时间
   */
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "account_time", length = 20, columnDefinition = "datetime COMMENT '上账时间'")
  private Date accountTime;


}
