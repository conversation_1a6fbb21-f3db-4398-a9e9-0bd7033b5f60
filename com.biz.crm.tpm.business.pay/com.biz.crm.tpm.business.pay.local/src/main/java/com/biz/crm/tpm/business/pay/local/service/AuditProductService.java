package com.biz.crm.tpm.business.pay.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditProductDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditProductVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 费用明细商品;(tpm_audit_product)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
public interface AuditProductService {

  /**
   * 生成操作标记
   */
  String preSave();

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<AuditProductVo> findByConditions(Pageable pageable, AuditProductDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  AuditProductVo findById(String id);

  /**
   * 通过编号查询单条数据
   *
   * @param auditCode 编号
   * @return 单条数据
   */
  List<AuditProductVo> findByAuditCode(String auditCode);

  /**
   * 通过编号查询单条数据
   *
   * @param auditDetailCode 编号
   * @return 单条数据
   */
  List<AuditProductVo> findByAuditDetailCode(String auditDetailCode);

  /**
   * 新增数据
   *
   * @param auditProductDto 实体对象
   * @return 新增结果
   */
  AuditProductVo create(AuditProductDto auditProductDto);
  /**
   * 新增数据
   *
   * @param auditProductDtos 实体对象
   * @return 新增结果
   */
  List<AuditProductVo> createBatch(List<AuditProductDto> auditProductDtos);
  /**
   * 修改数据
   *
   * @param auditProductDto 实体对象
   * @return 修改结果
   */
  AuditProductVo update(AuditProductDto auditProductDto);
  /**
   * 修改数据
   *
   * @param auditProductDtos 实体对象
   * @return 修改结果
   */
  List<AuditProductVo> updateBatch(List<AuditProductDto> auditProductDtos);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(List<String> ids);

  /**
   * 根据费用核销编号删除数据
   *
   * @param auditCode
   */
  void deleteByAuditCode(String auditCode);

  /**
   * 根据费用核销明细编号删除数据
   *
   * @param auditDetailCode
   */
  void deleteByAuditDetailCode(String auditDetailCode);
}