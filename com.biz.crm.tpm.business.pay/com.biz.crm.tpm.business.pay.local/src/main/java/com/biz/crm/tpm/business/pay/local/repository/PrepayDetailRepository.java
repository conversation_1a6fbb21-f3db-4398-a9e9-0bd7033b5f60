package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.entity.PrepayDetail;
import com.biz.crm.tpm.business.pay.local.mapper.PrepayDetailMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayDetailVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 活动预付明细;(tpm_prepay_detail)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Component
public class PrepayDetailRepository extends ServiceImpl<PrepayDetailMapper, PrepayDetail> {
  @Autowired
  private PrepayDetailMapper prepayDetailMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<PrepayDetailVo> findByConditions(Pageable pageable, PrepayDetailDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<PrepayDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return prepayDetailMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<PrepayDetail>
   */
  public List<PrepayDetail> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(PrepayDetail::getId, ids)
            .eq(PrepayDetail::getTenantCode, tenantCode)
            .eq(PrepayDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .list();
  }

  @Override
  public boolean removeByIds(Collection<? extends Serializable> idList) {
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate().in(PrepayDetail::getId, idList).
            eq(PrepayDetail::getTenantCode, tenantCode).
            set(PrepayDetail::getDelFlag, DelFlagStatusEnum.DELETE.getCode()).update();
  }

  /**
   * 根绝业务编号prepayCode获取业务数据
   *
   * @return 返多条数据
   */
  public List<PrepayDetail> findByPrepayCode(String prepayCode) {
    if (StringUtils.isBlank(prepayCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(PrepayDetail::getPrepayCode, prepayCode)
            .eq(PrepayDetail::getTenantCode, tenantCode)
            .eq(PrepayDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .list();
  }

  /**
   * 删除数据
   */
  public boolean deleteByPrepayCode(String prepayCode) {
    if (StringUtils.isBlank(prepayCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(PrepayDetail::getPrepayCode, prepayCode)
            .eq(PrepayDetail::getTenantCode, tenantCode)
            .set(PrepayDetail::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
            .update();
  }

  /**
   * 通过预付明细查询预付信息
   *
   * @param prepayDetailCode
   * @return
   */
  public PrepayDetail findByPrepayDetailCode(String prepayDetailCode) {
    if (StringUtils.isBlank(prepayDetailCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(PrepayDetail::getPrepayDetailCode, prepayDetailCode)
            .eq(PrepayDetail::getTenantCode, tenantCode)
            .eq(PrepayDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .one();
  }

  public PrepayDetail findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(PrepayDetail::getTenantCode,tenantCode)
        .in(PrepayDetail::getId,id)
        .one();
  }
}