<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.ActivityPrepayDetailRecordMapper">


    <select id="findListByCondition" resultType="com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordVo">
        SELECT
        t.create_account,
        t.create_name,
        t.create_time,
        t.modify_account,
        t.modify_name,
        t.modify_time,
        t.del_flag,
        t.enable_status,
        t.remark,
        t.tenant_code,
        t.available_reversed_amount,
        t.category_code,
        t.category_name,
        t.company_code,
        t.detail_code,
        t.detail_name,
        t.estimated_cost,
        t.payee_name,
        case when t.prepay_type != 'activity' then b.this_cash_amount
        else t.prepay_amount end prepay_amount,
        t.prepay_code,
        t.prepay_detail_code,
        t.prepay_name,
        t.reversed_amount,
        t.scheme_code,
        t.scheme_detail_code,
        t.scheme_name,
        t.years,
        t.payee_code,
        t.apply_amount,
        t.audit_code,
        t.audit_detail_code,
        t.description,
        t.line_code,
        t.carried_amount,
        t.prepare_carry_amount,
        t.prepay_type,
        t.relate_carry_amount,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.this_cash_amount
        ELSE
        t.prepay_apply_amount
        END prepay_apply_amount,
        t.pay_status,
        t.cost_center_code,
        t.cost_center_name,
        t.item_code,
        t.item_name,
        t.pay_sucess_date
        FROM
        tpm_activity_prepay_detail_record t
        LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code
        AND a.status = '3'
        LEFT JOIN (
        SELECT
        b.cash_detail_code,
        b.this_cash_amount
        FROM
        tpm_fee_cash a
        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
        WHERE
        a.status = '3'
        AND cash_type in('wire', 'account')) b ON t.prepay_detail_code = b.cash_detail_code
        <where>
            t.del_flag = '009'
            AND t.tenant_code = 'default'
            <if test="dto.prepayCode != null and dto.prepayCode != ''">
                <bind name="prepayCode" value="'%' + dto.prepayCode + '%'"/>
                and t.prepay_code like #{prepayCode}
            </if>
            <if test="dto.prepayDetailCode != null and dto.prepayDetailCode != ''">
                <bind name="prepayDetailCode" value="'%' + dto.prepayDetailCode + '%'"/>
                and t.prepay_detail_code like #{prepayCode}
            </if>
            <if test="dto.schemeCode != null and dto.schemeCode != ''">
                <bind name="schemeCode" value="'%' + dto.schemeCode + '%'"/>
                and t.scheme_code like #{schemeCode}
            </if>
            <if test="dto.schemeDetailCode != null and dto.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + dto.schemeDetailCode + '%'"/>
                and t.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="dto.schemeDetailCode != null and dto.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + dto.schemeDetailCode + '%'"/>
                and t.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="dto.startYears != null and dto.startYears != ''">
                <![CDATA[and t.years >= #{dto.startYears}]]>
            </if>
            <if test="dto.endYears != null and dto.endYears != ''">
                <![CDATA[and t.years <= #{dto.endYears}]]>
            </if>
            <if test="dto.prepayType != null and dto.prepayType != ''">
                and t.prepay_type = #{dto.prepayType}
            </if>
            order by t.create_time desc,t.id desc
        </where>
    </select>


    <select id="findCalList" resultType="com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord">
        select
        t.*
        from tpm_activity_prepay_detail_record t
        where t.del_flag = '${@<EMAIL>()}'
        and t.tenant_code = #{vo.tenantCode}
        and t.prepay_type = #{vo.prepayType}
        and t.prepare_carry_amount is not null
        and t.prepare_carry_amount > 0
        <if test="vo.companyCode != null and vo.companyCode != ''">
            and t.company_code = #{vo.companyCode}
        </if>
        <if test="vo.payeeCode != null and vo.payeeCode != ''">
            and t.payee_code = #{vo.payeeCode}
        </if>
        <if test="vo.companyCodes != null and vo.companyCodes.size() > 0">
            and t.company_code in
            <foreach collection="vo.companyCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.payeeCodes != null and vo.payeeCodes.size() > 0">
            and t.payee_code in
            <foreach collection="vo.payeeCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by prepay_code desc
    </select>

    <select id="updatePayStatus">
        <foreach item="dto" collection="dtoList" index="index" open="" separator=";" close="">
            update tpm_activity_prepay_detail_record
            set pay_status = #{dto.orderStatus},
            pay_sucess_date = #{dto.paySucessDate}
            where prepay_code = #{dto.businessCode}
        </foreach>
    </select>

    <select id="findAuditedAmountBySchemeDetailCodes"
            resultType="com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo">
        SELECT
        max(be_full_audit) be_full_audit,
        sum( audit_amount) audited_amount,
        scheme_detail_code
        FROM
            tpm_marketing_audit_detail
        WHERE
            scheme_detail_code in
        <foreach collection="schemeDetailCodes" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        group by  scheme_detail_code
    </select>
    <select id="findByPayeeCode" resultType="com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordVo">
    SELECT
           tt.* from
        (
        SELECT
        t.create_account,
        t.create_name,
        t.create_time,
        t.modify_account,
        t.modify_name,
        t.modify_time,
        t.del_flag,
        t.enable_status,
        t.remark,
        t.tenant_code,
        case when t.prepay_type != 'activity' THEN
        ifnull(b.this_cash_amount,0)-ifnull(d.this_reversed_amount,0)
        else
        ifnull(c.this_pay_amount,0)-ifnull(d.this_reversed_amount,0)
        end available_reversed_amount,
        t.category_code,
        t.category_name,
        t.company_code,
        t.detail_code,
        t.detail_name,
        t.estimated_cost,
        t.payee_name,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.this_cash_amount
        ELSE
        c.this_pay_amount
        END prepay_amount,
        t.prepay_code,
        t.prepay_detail_code,
        t.prepay_name,
        d.this_reversed_amount reversed_amount,
        t.scheme_code,
        t.scheme_detail_code,
        t.scheme_name,
        t.years,
        t.payee_code,
        t.apply_amount,
        t.audit_code,
        t.audit_detail_code,
        t.description,
        t.line_code,
        t.carried_amount,
        t.prepare_carry_amount,
        t.prepay_type,
        t.relate_carry_amount,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.this_cash_amount
        ELSE
        c.this_prepay_amount
        END prepay_apply_amount,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.pay_status
        ELSE
        a.pay_status
        END payStatus,
        t.cost_center_code,
        t.cost_center_name,
        t.item_code,
        t.item_name,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.pay_sucess_date
        ELSE
        a.pay_sucess_date
        END paySucessDate
        FROM
        tpm_activity_prepay_detail_record t
        LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code
        AND a.status = '3'
        LEFT JOIN tpm_activity_prepay_detail c ON t.prepay_detail_code = c.prepay_detail_code
        LEFT JOIN (
        SELECT
        b.cash_detail_code,
        b.this_cash_amount,
        a.pay_status,
        a.pay_sucess_date
        FROM
        tpm_fee_cash a
        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
        WHERE
        a.status = '3'
        AND cash_type in('wire', 'account')) b ON t.prepay_detail_code = b.cash_detail_code
        LEFT JOIN (
        SELECT
        a.prepay_detail_code, sum(IFNULL(a.this_reversed_amount, 0)) this_reversed_amount
        FROM
        tpm_fee_cash_prepay a left join tpm_fee_cash b on a.cash_code = b.cash_code
        where b.del_flag = '009'
        <if test="dto.cashCode != null and dto.cashCode != ''">
            and  a.cash_code!=#{dto.cashCode}
        </if>
        GROUP BY
        a.prepay_detail_code) d ON t.prepay_detail_code = d.prepay_detail_code
        where t.del_flag = '${@<EMAIL>()}'
        <if test="dto.payeeCode != null and dto.payeeCode != ''">
            and t.payee_code = #{dto.payeeCode}
        </if>
        <if test="dto.payeeCodes != null and dto.payeeCodes.size() > 0">
            and t.payee_code in
            <foreach collection="dto.payeeCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.schemeDetailCodeList != null and dto.schemeDetailCodeList.size() > 0">
            and t.scheme_detail_code in
            <foreach collection="dto.schemeDetailCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t.prepay_detail_code asc ,t.id desc
    ) tt
    where tt.available_reversed_amount>0
        <choose>
            <when test="dto.payStatusList != null and dto.payStatusList.size()>0">
                and payStatus in
                <foreach collection="dto.payStatusList" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="dto.payStatus != null and dto.payStatus != ''">
                    and payStatus = #{dto.payStatus}
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="findByPayeeCode2" resultType="com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailRecordVo">
        SELECT
        tt.* from
        (
        SELECT
        t.create_account,
        t.create_name,
        t.create_time,
        t.modify_account,
        t.modify_name,
        t.modify_time,
        t.del_flag,
        t.enable_status,
        t.remark,
        t.tenant_code,
        case when t.prepay_type != 'activity' THEN
        ifnull(b.this_cash_amount,0)-ifnull(d.this_reversed_amount,0)
        else
        ifnull(c.this_pay_amount,0)-ifnull(d.this_reversed_amount,0)
        end available_reversed_amount,
        t.category_code,
        t.category_name,
        t.company_code,
        t.detail_code,
        t.detail_name,
        t.estimated_cost,
        t.payee_name,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.this_cash_amount
        ELSE
        c.this_pay_amount
        END prepay_amount,
        t.prepay_code,
        t.prepay_detail_code,
        t.prepay_name,
        d.this_reversed_amount reversed_amount,
        t.scheme_code,
        t.scheme_detail_code,
        t.scheme_name,
        t.years,
        t.payee_code,
        t.apply_amount,
        t.audit_code,
        t.audit_detail_code,
        t.description,
        t.line_code,
        t.carried_amount,
        t.prepare_carry_amount,
        t.prepay_type,
        t.relate_carry_amount,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.this_cash_amount
        ELSE
        c.this_prepay_amount
        END prepay_apply_amount,
        t.pay_status,
        t.cost_center_code,
        t.cost_center_name,
        t.item_code,
        t.item_name,
        t.pay_sucess_date,
        d.id_cash_code
        FROM
        tpm_activity_prepay_detail_record t
        LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code
        AND a.status = '3'
        LEFT JOIN tpm_activity_prepay_detail c ON t.prepay_detail_code = c.prepay_detail_code
        LEFT JOIN (
        SELECT
        b.cash_detail_code,
        b.this_cash_amount
        FROM
        tpm_fee_cash a
        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
        WHERE
        a.status = '3'
        AND cash_type in('wire', 'account')) b ON t.prepay_detail_code = b.cash_detail_code
        LEFT JOIN (
        SELECT
        GROUP_CONCAT(CONCAT(b.id,':',b.cash_code)) as  idCashCode,
        a.prepay_detail_code, sum(IFNULL(a.this_reversed_amount, 0)) this_reversed_amount
        FROM
        tpm_fee_cash_prepay a left join tpm_fee_cash b on a.cash_code = b.cash_code
        where b.del_flag = '009'
        <if test="dto.cashCode != null and dto.cashCode != ''">
            and  a.cash_code=#{dto.cashCode}
        </if>
        GROUP BY
        a.prepay_detail_code) d ON t.prepay_detail_code = d.prepay_detail_code
        where t.del_flag = '${@<EMAIL>()}'
        <if test="dto.payStatus != null and dto.payStatus != ''">
            and t.pay_status = #{dto.payStatus}
        </if>
        <if test="dto.payeeCode != null and dto.payeeCode != ''">
            and t.payee_code = #{dto.payeeCode}
        </if>
        <if test="dto.payeeCodes != null and dto.payeeCodes.size() > 0">
            and t.payee_code in
            <foreach collection="dto.payeeCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.schemeDetailCodeList != null and dto.schemeDetailCodeList.size() > 0">
            and t.scheme_detail_code in
            <foreach collection="dto.schemeDetailCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t.prepay_detail_code asc ,t.id desc
        ) tt
    </select>

    <select id="findListBySchemeDetailCodes" resultType="com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord">
        SELECT
        t.id,
        t.create_account,
        t.create_name,
        t.create_time,
        t.modify_account,
        t.modify_name,
        t.modify_time,
        t.del_flag,
        t.enable_status,
        t.remark,
        t.tenant_code,
        case when t.prepay_type != 'activity' THEN
        ifnull(b.this_cash_amount,0)-ifnull(d.this_reversed_amount,0)
        else
        ifnull(c.this_pay_amount,0)-ifnull(d.this_reversed_amount,0)
        end available_reversed_amount,
        t.category_code,
        t.category_name,
        t.company_code,
        t.detail_code,
        t.detail_name,
        t.estimated_cost,
        t.payee_name,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.this_cash_amount
        ELSE
        c.this_pay_amount
        END prepay_amount,
        t.prepay_code,
        t.prepay_detail_code,
        t.prepay_name,
        d.this_reversed_amount reversed_amount,
        t.scheme_code,
        t.scheme_detail_code,
        t.scheme_name,
        t.years,
        t.payee_code,
        t.apply_amount,
        t.audit_code,
        t.audit_detail_code,
        t.description,
        t.line_code,
        t.carried_amount,
        t.prepare_carry_amount,
        t.prepay_type,
        t.relate_carry_amount,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.this_cash_amount
        ELSE
        c.this_prepay_amount
        END prepay_apply_amount,
        t.pay_status,
        t.cost_center_code,
        t.cost_center_name,
        t.item_code,
        t.item_name,
        t.pay_sucess_date
        FROM
        tpm_activity_prepay_detail_record t
        LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code
        AND a.status = '3'
        LEFT JOIN tpm_activity_prepay_detail c ON t.prepay_detail_code = c.prepay_detail_code
        LEFT JOIN (
        SELECT
        b.cash_detail_code,
        b.this_cash_amount
        FROM
        tpm_fee_cash a
        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
        WHERE
        a.status = '3'
        AND cash_type in('wire', 'account')) b ON t.prepay_detail_code = b.cash_detail_code
        LEFT JOIN (
        SELECT
        a.prepay_detail_code, sum(IFNULL(a.this_reversed_amount, 0)) this_reversed_amount
        FROM
        tpm_fee_cash_prepay a left join tpm_fee_cash b on a.cash_code = b.cash_code
        where b.del_flag = '009'
        GROUP BY
        a.prepay_detail_code) d ON t.prepay_detail_code = d.prepay_detail_code
        where t.del_flag = '${@<EMAIL>()}'
        and t.scheme_detail_code in
        <foreach collection="schemeDetailCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findListByAuditDetailCodes" resultType="com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord">
        SELECT
        t.id,
        t.create_account,
        t.create_name,
        t.create_time,
        t.modify_account,
        t.modify_name,
        t.modify_time,
        t.del_flag,
        t.enable_status,
        t.remark,
        t.tenant_code,
        case when t.prepay_type != 'activity' THEN
        ifnull(b.this_cash_amount,0)-ifnull(d.this_reversed_amount,0)
        else
        ifnull(c.this_prepay_amount,0)-ifnull(d.this_reversed_amount,0)
        end available_reversed_amount,
        t.category_code,
        t.category_name,
        t.company_code,
        t.detail_code,
        t.detail_name,
        t.estimated_cost,
        t.payee_name,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.this_cash_amount
        ELSE
        c.this_pay_amount
        END prepay_amount,
        t.prepay_code,
        t.prepay_detail_code,
        t.prepay_name,
        d.this_reversed_amount reversed_amount,
        t.scheme_code,
        t.scheme_detail_code,
        t.scheme_name,
        t.years,
        t.payee_code,
        t.apply_amount,
        t.audit_code,
        t.audit_detail_code,
        t.description,
        t.line_code,
        t.carried_amount,
        t.prepare_carry_amount,
        t.prepay_type,
        t.relate_carry_amount,
        CASE WHEN t.prepay_type != 'activity' THEN
        b.this_cash_amount
        ELSE
        c.this_prepay_amount
        END prepay_apply_amount,
        t.pay_status,
        t.cost_center_code,
        t.cost_center_name,
        t.item_code,
        t.item_name,
        t.pay_sucess_date
        FROM
        tpm_activity_prepay_detail_record t
        LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code
        AND a.status = '3'
        LEFT JOIN tpm_activity_prepay_detail c ON t.prepay_detail_code = c.prepay_detail_code
        LEFT JOIN (
        SELECT
        b.cash_detail_code,
        b.this_cash_amount
        FROM
        tpm_fee_cash a
        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
        WHERE
        a.status = '3'
        AND cash_type in('wire', 'account')) b ON t.prepay_detail_code = b.cash_detail_code
        LEFT JOIN (
        SELECT
        a.prepay_detail_code, sum(IFNULL(a.this_reversed_amount, 0)) this_reversed_amount
        FROM
        tpm_fee_cash_prepay a left join tpm_fee_cash b on a.cash_code = b.cash_code
        where b.del_flag = '009'
        GROUP BY
        a.prepay_detail_code) d ON t.prepay_detail_code = d.prepay_detail_code
        where t.del_flag = '${@<EMAIL>()}'
        and t.audit_detail_code in
        <foreach collection="auditDetailCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findAlreadySchemeDetailCodeByCondition" resultType="com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetail">
        SELECT
        sum(b.this_prepay_amount) this_prepay_amount,
        b.scheme_detail_code
        FROM
        tpm_activity_prepay a
        INNER JOIN tpm_activity_prepay_detail b ON a.prepay_code = b.prepay_code
        <where>
            a.del_flag = '009'
            <if test="prepayCode != null and prepayCode != ''">
                AND a.prepay_code != #{prepayCode}
            </if>
            AND b.scheme_detail_code in
            <foreach collection="schemeDetailCodes" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
            GROUP BY
            b.scheme_detail_code
        </where>
    </select>
    <select id="findReportBySchemeDetailCodes"
            resultType="com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord">
        SELECT
            t.id,
            t.create_account,
            t.create_name,
            t.create_time,
            t.modify_account,
            t.modify_name,
            t.modify_time,
            t.del_flag,
            t.enable_status,
            t.remark,
            t.tenant_code,
            case when t.prepay_type != 'activity' THEN
                     ifnull(b.this_cash_amount,0)-ifnull(d.this_reversed_amount,0)
                 else
                     ifnull(c.this_prepay_amount,0)-ifnull(d.this_reversed_amount,0)
                end available_reversed_amount,
            t.category_code,
            t.category_name,
            t.company_code,
            t.detail_code,
            t.detail_name,
            t.estimated_cost,
            t.payee_name,
            CASE WHEN t.prepay_type != 'activity' THEN
                     b.this_cash_amount
                 ELSE
                     c.this_pay_amount
                END prepay_amount,
            t.prepay_code,
            t.prepay_detail_code,
            t.prepay_name,
            d.this_reversed_amount reversed_amount,
            t.scheme_code,
            t.scheme_detail_code,
            t.scheme_name,
            t.years,
            t.payee_code,
            t.apply_amount,
            t.audit_code,
            t.audit_detail_code,
            t.description,
            t.line_code,
            t.carried_amount,
            t.prepare_carry_amount,
            t.prepay_type,
            t.relate_carry_amount,
            CASE WHEN t.prepay_type != 'activity' THEN
                     b.this_cash_amount
                 ELSE
                     c.this_prepay_amount
                END prepay_apply_amount,
            t.pay_status,
            t.cost_center_code,
            t.cost_center_name,
            t.item_code,
            t.item_name,
            t.pay_sucess_date
        FROM
            tpm_activity_prepay_detail_record t
                LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code AND a.status = '3'
                LEFT JOIN tpm_activity_prepay_detail c ON t.prepay_detail_code = c.prepay_detail_code
                LEFT JOIN (
                SELECT
                    b.cash_detail_code,
                    b.this_cash_amount
                FROM
                    tpm_fee_cash a
                        LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
                WHERE
                    a.status = '3' and a.pay_status in ('success_pay','return_tickets')
                  AND cash_type in('wire', 'account')) b ON t.prepay_detail_code = b.cash_detail_code
                LEFT JOIN (
                SELECT
                    a.prepay_detail_code, sum(IFNULL(a.this_reversed_amount, 0)) this_reversed_amount
                FROM
                    tpm_fee_cash_prepay a left join tpm_fee_cash b on a.cash_code = b.cash_code
                where b.del_flag = '009' and b.status = '3'
                GROUP BY
                    a.prepay_detail_code) d ON t.prepay_detail_code = d.prepay_detail_code
        where t.del_flag = '${@<EMAIL>()}'
        and t.scheme_detail_code in
        <foreach collection="schemeDetailCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="findO2OPrepayDetailRecord"
            resultType="com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord">
        select t.*
        from tpm_activity_prepay_detail_record t
        LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code
        where t.del_flag = '${@<EMAIL>()}'
        and t.prepay_type = 'activity' and a.`status` = '3'
        and a.pay_status in ('success_pay','return_tickets')
        and t.scheme_detail_code in
        <foreach collection="schemeDetailCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>

