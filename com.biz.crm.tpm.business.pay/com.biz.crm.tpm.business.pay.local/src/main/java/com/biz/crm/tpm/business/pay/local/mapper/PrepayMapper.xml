<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.PrepayMapper">
  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.Prepay" id="PrepayMap">
  </resultMap>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.PrepayVo">
    select
    distinct t.*
    from tpm_prepay t
    left join tpm_prepay_detail tpd on t.prepay_code = tpd.prepay_code and t.tenant_code = tpd.tenant_code
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != '' ">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.prepayCode != null and dto.prepayCode != '' ">
        <bind name="prepayCode" value=" '%' + dto.prepayCode + '%' "/>
        and t.prepay_code like #{prepayCode}
      </if>
      <if test="dto.activitiesCode != null and dto.activitiesCode != '' ">
        <bind name="activitiesCode" value=" '%' + dto.activitiesCode + '%' "/>
        and t.activities_code like #{activitiesCode}
      </if>
      <if test="dto.activitiesName != null and dto.activitiesName != '' ">
        <bind name="activitiesName" value=" '%' + dto.activitiesName + '%' "/>
        and t.activities_name like #{activitiesName}
      </if>
      <if test="dto.activitiesDetailCode != null and dto.activitiesDetailCode != '' ">
        <bind name="activitiesDetailCode" value=" '%' + dto.activitiesDetailCode + '%' "/>
        and tpd.activities_detail_code like #{activitiesDetailCode}
      </if>
      and t.del_flag = '${@<EMAIL>()}'
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>