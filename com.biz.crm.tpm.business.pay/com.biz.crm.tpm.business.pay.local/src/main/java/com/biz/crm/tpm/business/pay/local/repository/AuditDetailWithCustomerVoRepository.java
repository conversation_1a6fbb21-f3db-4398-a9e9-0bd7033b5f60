package com.biz.crm.tpm.business.pay.local.repository;

import com.biz.crm.tpm.business.pay.local.mapper.AuditDetailWithCustomerVoMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditDetailWithCustomerVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年07月11日 18:16:00
 */
@Component
public class AuditDetailWithCustomerVoRepository {
  @Autowired
  private AuditDetailWithCustomerVoMapper auditDetailWithCustomerVoMapper;

  public List<AuditDetailWithCustomerVo> getAuditDetailWithCustomer(AuditDetailDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    return auditDetailWithCustomerVoMapper.getAuditDetailWithCustomer(dto);
  }
}
