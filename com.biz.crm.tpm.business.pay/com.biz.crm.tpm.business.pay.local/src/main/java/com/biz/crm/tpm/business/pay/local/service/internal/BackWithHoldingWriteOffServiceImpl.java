package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.base.service.CostControlLoginService;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.pay.local.entity.*;
import com.biz.crm.tpm.business.pay.local.repository.*;
import com.biz.crm.tpm.business.pay.sdk.constant.WithHoldingWriteOffConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingWriteOffDto;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.BackWithHoldingWriteOffService;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.vo.BackWithHoldingWriteOffVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingWriteOffVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import liquibase.pro.packaged.W;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BackWithHoldingWriteOffServiceImpl implements BackWithHoldingWriteOffService {

    // 所有依赖注入和方法已移除，仅保留类结构

    @Autowired(required = false)
    private WithHoldingWriteOffRepository withHoldingWriteOffRepository;

    @Autowired
    private BackWithHoldingWriteOffRepository backWithHoldingWriteOffRepository;

    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private CostControlLoginService costControlLoginService;

    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private FeeCashService feeCashService;

    @Value("${domain-name:}")
    private String domainName;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;
    @Autowired
    private DeliveryReplenishmentPoolDetailRepository deliveryReplenishmentPoolDetailRepository;


    @Override
    public Map<String, List<BackWithHoldingWriteOffVo>> writeOff(List<WithHoldingWriteOffDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return Maps.newHashMap();
        }
        validateCommon(dtoList);
        List<String> detailCodeList = dtoList.stream().map(WithHoldingWriteOffDto::getActivitiesDetailCode).collect(Collectors.toList());
        List<WithHolding> withHoldings = withHoldingRepository.findListByBusinessCodes(detailCodeList);
        if (CollectionUtil.isEmpty(withHoldings)) {
            return Maps.newHashMap();
        }
        List<String> withHoldingCodes = withHoldings.stream().map(e -> e.getWithHoldingCode()).collect(Collectors.toList());
        List<BackWithHoldingWriteOff> writeOffHistoryVos = backWithHoldingWriteOffRepository.findByCodes(withHoldingCodes);
        Map<String, BigDecimal> writeOffHistoryMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(writeOffHistoryVos)) {
            writeOffHistoryMap = writeOffHistoryVos.stream().collect(Collectors.groupingBy(e -> e.getWithHoldingCode(), Collectors.mapping(e -> e.getWriteOffAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        Map<String, List<WithHoldingWriteOffDto>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(WithHoldingWriteOffDto::getActivitiesDetailCode));
        if (CollectionUtil.isEmpty(dtoMap)) {
            return Maps.newHashMap();
        }
        //找结案数据的创建人职位信息
        List<MarketingAuditDetail> auditDetailList = marketingAuditDetailRepository.findBySchemeDetailCodes(detailCodeList);
        Map<String, MarketingAuditDetail> posMap = auditDetailList.stream().filter(Objects::nonNull).filter(e -> StringUtil.isNotBlank(e.getPositionCode()))
                .collect(Collectors.toMap(MarketingAuditDetail::getSchemeDetailCode, Function.identity(), (a, b) -> a));
        Map<String, List<MarketingAuditDetail>> auditSchemeDetailMap = auditDetailList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(MarketingAuditDetail::getSchemeDetailCode));

        List<BackWithHoldingWriteOff> entities = new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(withHoldings, WithHolding.class, BackWithHoldingWriteOff.class, LinkedHashSet.class, ArrayList.class));

        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesApproved(detailCodeList);
        Map<String, BigDecimal> collect = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(feeCashDetailVos)){
            collect = feeCashDetailVos.stream().filter(o->null!=o&& StringUtils.isNotBlank(o.getSchemeDetailCode())).collect(Collectors.groupingBy(FeeCashDetailVo::getSchemeDetailCode, Collectors.mapping(e -> e.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }

        List<BackWithHoldingWriteOff> saveBatch = new ArrayList<>();
        for (BackWithHoldingWriteOff entity : entities) {
            List<WithHoldingWriteOffDto> offDtoList = dtoMap.get(entity.getBusinessCode());
            if (CollectionUtils.isEmpty(offDtoList)) {
                continue;
            }

            for (WithHoldingWriteOffDto dto : offDtoList) {
                //冲销的年月必须在计提入账年月之后
                if (StringUtils.isNotBlank(dto.getWriteOffYears()) && DateUtil.parse(dto.getWriteOffYears(), DateUtil.DEFAULT_YEAR_MONTH).compareTo(DateUtil.parse(entity.getYearMonthLy(), DateUtil.DEFAULT_YEAR_MONTH)) <= 0) {
                    continue;
                }
                BackWithHoldingWriteOff newEntity = new BackWithHoldingWriteOff();
                BeanUtils.copyProperties(entity, newEntity);
                newEntity.setWriteOffType(dto.getWriteOffType());
                newEntity.setWriteOffTime(new Date());
                newEntity.setWriteOffYears(StringUtils.isBlank(dto.getWriteOffYears()) ? DateUtil.format(new Date(), DateUtil.DEFAULT_YEAR_MONTH) : dto.getWriteOffYears());
                newEntity.setAuditCreateAccount(StringUtils.isBlank(dto.getAuditCreateAccount()) ? posMap.get(dto.getActivitiesDetailCode()).getCreateAccount() : dto.getAuditCreateAccount());
                newEntity.setPositionCode(StringUtils.isBlank(dto.getPositionCode()) ? posMap.get(dto.getActivitiesDetailCode()).getPositionCode() : dto.getPositionCode());
                newEntity.setSourceCode(dto.getSourceCode());
                newEntity.setRuleCode(dto.getRuleCode());
                //完全结案时冲销计提金额与结案金额的差额
                if (BooleanEnum.TRUE.getCapital().equals(dto.getBeFullAudit())) {
                    if (!(ProcessStatusEnum.PASS.getDictCode().equals(newEntity.getStatus()) && StringUtils.isNotBlank(newEntity.getVoucherCode()))) {
                        continue;
                    }

                    BigDecimal sumAuditAmount = auditSchemeDetailMap.getOrDefault(newEntity.getActivitiesDetailCode(),
                                    Lists.newArrayList()).stream().filter(k -> Objects.nonNull(k.getAuditAmount()))
                            .map(MarketingAuditDetail::getAuditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 管报计提金额 + 计提前兑付 - 结案金额
                    BigDecimal subtract = newEntity.getActualReportAmount()
                            .add(Optional.ofNullable(collect.get(dto.getActivitiesDetailCode())).orElse(BigDecimal.ZERO))
                            .subtract(sumAuditAmount);
                    if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
                        log.warn("=============================活动明细编码[{}]无需冲销", newEntity.getActivitiesDetailCode());
                        continue;
                    }
                    //结案冲销计提余额校验
                    BigDecimal writeOffHistoryAmount = writeOffHistoryMap.getOrDefault(entity.getWithHoldingCode(), BigDecimal.ZERO);
                    // 管报计提金额 - 历史冲销金额 = 计提余额
                    if ((entity.getActualReportAmount()
                            .subtract(Optional.ofNullable(subtract).orElse(BigDecimal.ZERO))
                            .subtract(writeOffHistoryAmount)).compareTo(BigDecimal.ZERO) < 0) {
                        continue;
                    }
                    newEntity.setWriteOffAmount(subtract);
                } else {
                    newEntity.setWriteOffAmount(dto.getWriteOffAmount());
                }

                newEntity.setId(null);
                newEntity.setCreateTime(null);
                newEntity.setCreateAccount(null);
                newEntity.setCreateName(null);
                newEntity.setModifyTime(null);
                newEntity.setModifyAccount(null);
                newEntity.setModifyName(null);
                newEntity.setVoucherCode(null);

                saveBatch.add(newEntity);
            }
        }
        if (CollectionUtil.isEmpty(saveBatch)) {
            return Maps.newHashMap();
        }
        List<String> codes = generateCodeService.generateCode(WithHoldingWriteOffConstant.BACK_PREFIX_CODE, saveBatch.size());
        for (int i = 0; i < saveBatch.size(); i++) {
            saveBatch.get(i).setWriteOffCode(codes.get(i));
            saveBatch.get(i).setBeThisFee(BooleanEnum.FALSE.getCapital());
        }
        //冲销单生成的时候需要加唯一性校验：编码规则
        List<String> uniqueKeys = saveBatch.stream().map(e -> e.getRuleCode()).collect(Collectors.toList());
        List<BackWithHoldingWriteOff> uniqueWriteOff = backWithHoldingWriteOffRepository.findByUniqueKeys(uniqueKeys);
        if (!CollectionUtils.isEmpty(uniqueWriteOff)) {
            List<String> uniqueWriteOffKeys = uniqueWriteOff.stream().map(e -> e.getRuleCode()).collect(Collectors.toList());
            Iterator<BackWithHoldingWriteOff> iterator = saveBatch.iterator();
            while (iterator.hasNext()) {
                BackWithHoldingWriteOff next = iterator.next();
                if (uniqueWriteOffKeys.contains(next.getRuleCode())) {
                    iterator.remove();
                }
            }
        }
        if (CollectionUtils.isEmpty(saveBatch)) {
            return new HashMap<>();
        }
        backWithHoldingWriteOffRepository.saveBatch(saveBatch);
        Collection<BackWithHoldingWriteOffVo> withHoldingWriteOffVos = nebulaToolkitService.copyCollectionByWhiteList(saveBatch, BackWithHoldingWriteOff.class, BackWithHoldingWriteOffVo.class, LinkedHashSet.class, ArrayList.class);
        return withHoldingWriteOffVos.stream().collect(Collectors.groupingBy(e -> e.getBusinessCode()));
    }

    private void validateCommon(List<WithHoldingWriteOffDto> dtoList) {
        Validate.notEmpty(dtoList, "请求对象不能为空");
        dtoList.forEach(dto -> {
            Validate.notNull(dto, "请求对象不能为空");
            Validate.notBlank(dto.getActivitiesDetailCode(), "明细编码，不能为空");
            Validate.notBlank(dto.getWriteOffType(), "冲销类型，不能为空");
            Validate.notNull(dto.getWriteOffAmount(), "冲销金额，不能为空");
            WriteOffTypeEnum writeOffTypeEnum = WriteOffTypeEnum.findByCode(dto.getWriteOffType());
            Validate.notNull(writeOffTypeEnum, "冲销类型不正确");
        });
    }

    /**
     * 兑付冲销-兑付、发货、关闭
     * @param dtoList
     * @param type cash_order  close
     * @return
     */
    @Override
    public Map<String, List<BackWithHoldingWriteOffVo>> cashOrderCloseWriteOff(List<WithHoldingWriteOffDto> dtoList, String type) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return Maps.newHashMap();
        }
        log.info("cashOrderCloseWriteOff.step1");
        validateCommon(dtoList);
        List<String> detailCodeList = dtoList.stream().map(WithHoldingWriteOffDto::getActivitiesDetailCode).collect(Collectors.toList());
        log.info("cashOrderCloseWriteOff.step2,detailCodeList.size={}",detailCodeList.size());
        // 查询是否存在计提
        List<WithHolding> withHoldings = withHoldingRepository.findListByBusinessCodes(detailCodeList);
        if (CollectionUtil.isEmpty(withHoldings)) {
            return Maps.newHashMap();
        }
        log.info("cashOrderCloseWriteOff.step3,withHoldings.size={}",withHoldings.size());
        Map<String, WithHolding> withholdingMap = withHoldings.stream().collect(Collectors.toMap(WithHolding::getBusinessCode, Function.identity(), (a, b) -> a));

        // 用于计算财报计提余额
        List<String> withholdingCodes = withHoldings.stream().map(WithHolding::getWithHoldingCode).distinct().collect(Collectors.toList());
        List<WithHoldingWriteOffVo> writeOffVoList = withHoldingWriteOffRepository.findByCodes(withholdingCodes);
        Map<String, List<WithHoldingWriteOffVo>> writeOffMap = writeOffVoList.stream().collect(Collectors.groupingBy(WithHoldingWriteOffVo::getWithHoldingCode));

        // 查询活动信息
        List<MarketingPlanCase> planCaseList = marketingPlanCaseService.findListBySchemeDetailCodes(detailCodeList);
        log.info("cashOrderCloseWriteOff.step4,planCaseList.size={}",planCaseList.size());
        Map<String, MarketingPlanCase> planCaseMap = planCaseList.stream().collect(Collectors.toMap(MarketingPlanCase::getSchemeDetailCode, v -> v, (a, b) -> a));

        // 结案数据
        List<MarketingAuditDetail> auditDetailList = marketingAuditDetailRepository.findBySchemeDetailCodes(detailCodeList);
        Map<String, MarketingAuditDetail> posMap = auditDetailList.stream().filter(Objects::nonNull).filter(e -> StringUtil.isNotBlank(e.getPositionCode()))
                .collect(Collectors.toMap(MarketingAuditDetail::getSchemeDetailCode, Function.identity(), (a, b) -> a));

        // ruleCode 集合
        List<String> ruleCodes = dtoList.stream().map(WithHoldingWriteOffDto::getRuleCode).collect(Collectors.toList());
        List<DeliveryReplenishmentPoolDetail> details = deliveryReplenishmentPoolDetailRepository.findByRuleCodes(ruleCodes);
        Map<String, DeliveryReplenishmentPoolDetail> detailMap = details.stream().collect(Collectors.toMap(DeliveryReplenishmentPoolDetail::getRuleCode, v -> v, (a, b) -> a));
        List<BackWithHoldingWriteOff> saveBatch = new ArrayList<>();
        dtoList.forEach(dto -> {
            String activitiesDetailCode = dto.getActivitiesDetailCode();
            // 查询不到活动直接return
            if (!planCaseMap.containsKey(activitiesDetailCode)) return;
            MarketingPlanCase planCaseVo = planCaseMap.get(activitiesDetailCode);
            // 当不存在管报计提时，则不生成冲销数据
            if (!withholdingMap.containsKey(activitiesDetailCode)) return;
            WithHolding withHolding = withholdingMap.get(activitiesDetailCode);
            // 判断计提前兑付
            if (!detailMap.containsKey(dto.getRuleCode())) return;
            DeliveryReplenishmentPoolDetail deliveryReplenishmentPoolDetail = detailMap.get(dto.getRuleCode());
            // 兑付冲销的业务发生日期=预算年月，则不生成冲销数据
            if (Objects.nonNull(deliveryReplenishmentPoolDetail.getDeliveryTime()) && DateUtil.format(DateUtil.parse(deliveryReplenishmentPoolDetail.getDeliveryTime(), "yyyy-MM-dd"), "yyyy-MM").compareTo(planCaseVo.getYears()) <= 0) return;
            // 财报实际计提金额
            BigDecimal actualAmount = null == withHolding.getActualAmount()? BigDecimal.ZERO : withHolding.getActualAmount();
            // 管报实际计提金额
            BigDecimal actualReportAmount = null == withHolding.getActualReportAmount()? BigDecimal.ZERO : withHolding.getActualReportAmount();
            // 实时查询历史冲销金额 - fixme
            BigDecimal writeOffHistoryAmount = new BigDecimal("0");
            if (!CollectionUtils.isEmpty(saveBatch)) {
                writeOffHistoryAmount = saveBatch.stream().filter(e -> e.getWithHoldingCode().equals(withHolding.getWithHoldingCode())).map(BackWithHoldingWriteOff::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            // 拿出结案那一步先处理的冲销金额
            List<BackWithHoldingWriteOff> auditWriteOffHistoryVos = backWithHoldingWriteOffRepository.findByCodes(Lists.newArrayList(withHolding.getWithHoldingCode()));
            BigDecimal writeOffHistoryAuditAmount = new BigDecimal("0");
            if (!CollectionUtils.isEmpty(auditWriteOffHistoryVos)) {
                writeOffHistoryAuditAmount = auditWriteOffHistoryVos.stream().map(BackWithHoldingWriteOff::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            writeOffHistoryAmount = writeOffHistoryAmount.add(writeOffHistoryAuditAmount);
//            if (type.equals("cash_order")) {
                if (actualAmount.compareTo(actualReportAmount) >= 0) {
                    // 管报计提余额 = 管报实际计提金额 - 历史冲销金额
                    BigDecimal withholdingBalanceAmount = actualReportAmount.subtract(writeOffHistoryAmount);
                    if (dto.getWriteOffAmount().compareTo(withholdingBalanceAmount) >= 0) {
                        dto.setWriteOffAmount(withholdingBalanceAmount);
                    }
                } else {
                    // 财报计提余额
                    BigDecimal actualBalanceAmount = actualAmount.subtract(writeOffMap.getOrDefault(withHolding.getWithHoldingCode(), Lists.newArrayList()).stream().map(WithHoldingWriteOffVo::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    // 管报计提余额
                    BigDecimal actualReportBalanceAmount = actualReportAmount.subtract(writeOffHistoryAmount);
                    List<WithHoldingWriteOff> notWriteOffList = withHoldingWriteOffRepository.findNotWriteOffByActivitiesDetailCode(activitiesDetailCode);
                    // 不包含本身且状态是兑付冲销 发货冲销 关闭冲销
                    List<WithHoldingWriteOff> filterList = notWriteOffList.stream().filter(e -> !e.getId().equals(dto.getId()))
                            .filter(e -> WriteOffTypeEnum.CASH.getDictCode().equals(e.getWriteOffType()) || WriteOffTypeEnum.ORDER.getDictCode().equals(e.getWriteOffType()) || WriteOffTypeEnum.CLOSE.getDictCode().equals(e.getWriteOffType()))
                            .collect(Collectors.toList());
                    // 若无且财报计提余额为0时则是最后一次兑付
                    if (CollectionUtil.isEmpty(filterList) && actualBalanceAmount.compareTo(new BigDecimal("0")) == 0) {
                        // 按管报计提余额
                        dto.setWriteOffAmount(actualReportBalanceAmount);
                    } else {
                        if (dto.getWriteOffAmount().compareTo(actualReportBalanceAmount) >= 0) {
                            dto.setWriteOffAmount(actualReportBalanceAmount);
                        }
                    }
                }
//            } else if (type.equals("close")) {
//                // 管报计提余额 = 管报实际计提金额 - 历史冲销金额
//                BigDecimal withholdingBalanceAmount = actualReportAmount.subtract(writeOffHistoryAmount);
//                dto.setWriteOffAmount(withholdingBalanceAmount);
//            } else {
//                throw new IllegalArgumentException("不支持当前冲销类型");
//            }

            BackWithHoldingWriteOff newEntity = new BackWithHoldingWriteOff();
            BeanUtils.copyProperties(dto, newEntity);
            newEntity.setActualAmount(actualAmount);
            newEntity.setActualReportAmount(actualReportAmount);
            newEntity.setWriteOffType(dto.getWriteOffType());
            newEntity.setWriteOffTime(new Date());
            newEntity.setWriteOffYears(StringUtils.isBlank(dto.getWriteOffYears()) ? DateUtil.format(new Date(), DateUtil.DEFAULT_YEAR_MONTH) : dto.getWriteOffYears());
            newEntity.setAuditCreateAccount(StringUtils.isBlank(dto.getAuditCreateAccount()) ? posMap.get(dto.getActivitiesDetailCode()).getCreateAccount() : dto.getAuditCreateAccount());
            newEntity.setPositionCode(StringUtils.isBlank(dto.getPositionCode()) ? posMap.get(dto.getActivitiesDetailCode()).getPositionCode() : dto.getPositionCode());
            newEntity.setSourceCode(dto.getSourceCode());
            newEntity.setRuleCode(dto.getRuleCode());
            newEntity.setId(null);
            newEntity.setCreateTime(null);
            newEntity.setCreateAccount(null);
            newEntity.setCreateName(null);
            newEntity.setModifyTime(null);
            newEntity.setModifyAccount(null);
            newEntity.setModifyName(null);
            newEntity.setVoucherCode(null);
            saveBatch.add(newEntity);
            // 这里需要处理一条更新一条
            WithHoldingWriteOff updateEntity = withHoldingWriteOffRepository.getById(dto.getId());
            updateEntity.setManageReportWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
            withHoldingWriteOffRepository.updateById(updateEntity);
        });
        log.info("cashOrderCloseWriteOff.step5,saveBatch.size={}",saveBatch.size());
        if (CollectionUtil.isEmpty(saveBatch)) {
            return Maps.newHashMap();
        }
        List<String> codes = generateCodeService.generateCode(WithHoldingWriteOffConstant.BACK_PREFIX_CODE, saveBatch.size());
        for (int i = 0; i < saveBatch.size(); i++) {
            saveBatch.get(i).setWriteOffCode(codes.get(i));
            saveBatch.get(i).setBeThisFee(BooleanEnum.FALSE.getCapital());
        }
        //冲销单生成的时候需要加唯一性校验：编码规则
        List<String> uniqueKeys = saveBatch.stream().map(e -> e.getRuleCode()).collect(Collectors.toList());
        List<BackWithHoldingWriteOff> uniqueWriteOff = backWithHoldingWriteOffRepository.findByUniqueKeys(uniqueKeys);
        if (!CollectionUtils.isEmpty(uniqueWriteOff)) {
            List<String> uniqueWriteOffKeys = uniqueWriteOff.stream().map(e -> e.getRuleCode()).collect(Collectors.toList());
            Iterator<BackWithHoldingWriteOff> iterator = saveBatch.iterator();
            while (iterator.hasNext()) {
                BackWithHoldingWriteOff next = iterator.next();
                if (uniqueWriteOffKeys.contains(next.getRuleCode())) {
                    iterator.remove();
                }
            }
        }
        if (CollectionUtils.isEmpty(saveBatch)) {
            return new HashMap<>();
        }
        log.info("cashOrderCloseWriteOff.step6,saveBatch.size={}",saveBatch.size());
        backWithHoldingWriteOffRepository.saveBatch(saveBatch);
        Collection<BackWithHoldingWriteOffVo> withHoldingWriteOffVos = nebulaToolkitService.copyCollectionByWhiteList(saveBatch, BackWithHoldingWriteOff.class, BackWithHoldingWriteOffVo.class, LinkedHashSet.class, ArrayList.class);
        return withHoldingWriteOffVos.stream().collect(Collectors.groupingBy(e -> e.getBusinessCode()));

    }

}
