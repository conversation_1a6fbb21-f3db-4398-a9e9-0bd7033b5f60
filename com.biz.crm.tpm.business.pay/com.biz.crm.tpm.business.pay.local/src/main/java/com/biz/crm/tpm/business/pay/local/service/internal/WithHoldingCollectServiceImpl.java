package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.eunm.AuditStatusEnum;
import com.biz.crm.business.common.base.eunm.CashStatusEnum;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.helper.RegionCollectHelper;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectMarketingEstimationService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectDepartmentEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectItemEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepayDetailRecord;
import com.biz.crm.tpm.business.pay.local.entity.WithHolding;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingCollect;
import com.biz.crm.tpm.business.pay.local.helper.WithHoldingCollectHelper;
import com.biz.crm.tpm.business.pay.local.repository.ActivityPrepayDetailRecordRepository;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingCollectRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingRepository;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.constant.WithHoldingConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailRecordItemDto;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingCollectDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingDto;
import com.biz.crm.tpm.business.pay.sdk.enums.ActivityPrepayDetailRecordOperateTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.TpmPrepayTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WithHoldingTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WithholdingReportTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayDetailRecordService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingCollectOaService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingCollectService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingSendHecService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingCollectVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WithHoldingCollectServiceImpl implements WithHoldingCollectService {

    @Autowired(required = false)
    private WithHoldingCollectRepository withHoldingCollectRepository;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;

    @Autowired(required = false)
    private RegionCollectHelper planHelper;

    @Autowired(required = false)
    private WithHoldingCollectOaService withHoldingCollectOaService;

    @Autowired(required = false)
    private RegionCollectMarketingEstimationService marketingEstimationService;

    @Autowired(required = false)
    private WithHoldingSendHecService withHoldingSendHecService;

    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private ActivityPrepayDetailRecordRepository activityPrepayDetailRecordRepository;

    @Resource
    private ActivityPrepayDetailRecordService activityPrepayDetailRecordService;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;

    @Autowired(required = false)
    private WithHoldingService withHoldingService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private MdmCostCenterVoService mdmCostCenterVoService;

    @Autowired
    private MarketingPlanCaseRepository marketingPlanCaseRepository;

    @Autowired
    private DictDataVoService dictDataVoService;

    @Autowired
    private MarketingPlanCaseService marketingPlanCaseService;
    @Qualifier("feeCashDetailRepository")
    @Autowired
    private FeeCashDetailRepository feeCashDetailRepository;

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    @Override
    public WithHoldingCollectVo findByCode(String cacheKey, String code) {
        WithHoldingCollect entity = withHoldingCollectRepository.findByCode(code);
        Validate.notNull(entity, "未找到对应的汇总数据");
        WithHoldingCollectVo vo = nebulaToolkitService.copyObjectByWhiteList(entity, WithHoldingCollectVo.class, LinkedHashSet.class, ArrayList.class);
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findByCollectCode(entity.getCollectCode());
        Validate.notEmpty(withHoldingVos, "未找到对应的计提数据");

        String orgCode = entity.getDepartmentCode();
        String yearMonthLy = entity.getYearMonthLy();
        List<OrgVo> orgChildrenVos = orgVoService.findAllChildrenByOrgCode(orgCode);
        List<String> orgCodeList = orgChildrenVos.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
        Map<String, OrgVo> orgMap = orgChildrenVos.stream().collect(Collectors.toMap(OrgVo::getOrgCode, v -> v, (n, o) -> n));
        List<String> costCenterCodes = orgChildrenVos.stream()
                .filter(x -> !CollectionUtils.isEmpty(x.getCostCenterCodeSet()))
                .flatMap(x -> x.getCostCenterCodeSet().stream()).collect(Collectors.toList());
        MarketingPlanCaseVo planCaseVo = new MarketingPlanCaseVo();
        planCaseVo.setYears(yearMonthLy);
        planCaseVo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        planCaseVo.setBelongDepartmentCodes(orgCodeList);
        Page<MarketingPlanCaseVo> casePage = withHoldingService.findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseVo);
        List<MarketingPlanCase> caseList = casePage.getTotal() > 0 ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(casePage.getRecords(), MarketingPlanCaseVo.class, MarketingPlanCase.class, LinkedHashSet.class, ArrayList.class)) : new ArrayList<>();

        Map<String, MarketingPlanCaseVo> caseVoMap = casePage.getRecords().stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));
        for (MarketingPlanCase planCase : caseList) {
            MarketingPlanCaseVo caseVo = caseVoMap.get(planCase.getSchemeDetailCode());
            if (!CollectionUtils.isEmpty(caseVo.getProductList())) {
                planCase.setProductList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(caseVo.getProductList(), MarketingPlanProductVo.class, MarketingPlanProduct.class, LinkedHashSet.class, ArrayList.class)));
            }
            if (!CollectionUtils.isEmpty(caseVo.getLevelList())) {
                planCase.setLevelList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(caseVo.getLevelList(), MarketingPlanProductVo.class, MarketingPlanProduct.class, LinkedHashSet.class, ArrayList.class)));
            }
        }
        List<String> customerCodes = caseList.stream().map(MarketingPlanCase::getCustomerCode).distinct().collect(Collectors.toList());
        List<MarketingSalesPlanVo> salePlanList = marketingSalesPlanService.findListBySalesPlanQueryVo(new SalesPlanQueryVo() {{
            this.setCustomerCodeSet(new HashSet<>(customerCodes));
            this.setYears(yearMonthLy);
        }});
        salePlanList = salePlanList.stream().filter(x -> costCenterCodes.contains(x.getCostCenterCode())).collect(Collectors.toList());
        WithHoldingCollectHelper helper = ApplicationContextHolder.getContext().getBean(WithHoldingCollectHelper.class);

        //2.客户损益预测
        List<RegionCollectGainsAndLossesVo> gainsAndLossesVoList = helper.buildCustomerGainsAndLosses(null, orgMap, yearMonthLy, withHoldingVos, caseList);
        assembleChannelType(gainsAndLossesVoList);
        //3.二级部门测算
        List<RegionCollectDepartmentEstimationVo> departmentEstimationVoList = helper.buildDepartmentEstimation(null, orgCode, yearMonthLy, withHoldingVos);
        //4.品项费用测算
        List<RegionCollectItemEstimationVo> itemEstimationVoList = helper.buildItemEstimation(null, yearMonthLy, orgCode, withHoldingVos);
        //规划营销费用测算
        List<RegionCollectMarketingEstimationVo> planMarketingEstimationVos = planHelper.buildMarketingEstimation(null, yearMonthLy, orgCode, null, null, caseList, salePlanList);
        //5.营销费用测算
        List<RegionCollectMarketingEstimationVo> marketingEstimationVos = helper.buildMarketingEstimation(null, yearMonthLy, orgCode, withHoldingVos, planMarketingEstimationVos);

        // 6.搭赠及周边
        List<DictDataVo> giftSurroundExpenseItem = dictDataVoService.findByDictTypeCode("gift_surround_expense_item");
        List<String> itemCodes = giftSurroundExpenseItem.stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
        MarketingPlanCaseVo giftAndSurroundDto = new MarketingPlanCaseVo();
        giftAndSurroundDto.setYears(yearMonthLy);
        giftAndSurroundDto.setOrgCodeList(orgCodeList);
        giftAndSurroundDto.setDetailCodeList(itemCodes);
        giftAndSurroundDto.setChangeFlag(BooleanEnum.FALSE.getCapital());
        Page<MarketingPlanCaseVo> giftPage = marketingPlanCaseService.findMarketingPlanCaseReportList(PageRequest.of(0, 50000) , giftAndSurroundDto);
        if (!CollectionUtils.isEmpty(giftPage.getRecords())) {
            vo.setGiftAndSurroundingList(giftPage.getRecords());
        }
        // 7. 已结案兑付
        FeeCashDetailDto feeCashDetailDto = new FeeCashDetailDto();
        feeCashDetailDto.setYears(yearMonthLy);
        feeCashDetailDto.setBelongDepartmentCodes(orgCodeList);
//        feeCashDetailDto.setBeFullAudit("Y");
        Page<FeeCashDetailVo> auditedPage = feeCashDetailRepository.findFullAuditedDetailList(PageRequest.of(0, 50000), feeCashDetailDto);
        if (!CollectionUtils.isEmpty(auditedPage.getRecords())) {
            vo.setAuditedAndCashedActivityList(auditedPage.getRecords());
        }

        vo.setGainsAndLossesList(gainsAndLossesVoList);
        vo.setDepartmentEstimationList(departmentEstimationVoList);
        vo.setItemEstimationList(itemEstimationVoList);
        vo.setMarketingEstimationList(marketingEstimationVos);
        vo.setWithHoldingList(withHoldingVos);

        //存放redis2小时 为了导出处理
        redisService.set(cacheKey + ":" + WithholdingReportTypeEnum.MARKETING_ESTIMATION, marketingEstimationVos, 7200);
        redisService.set(cacheKey + ":" + WithholdingReportTypeEnum.CUSTOMER_GAINS_LOSSES, gainsAndLossesVoList, 7200);
        redisService.set(cacheKey + ":" + WithholdingReportTypeEnum.DEPARTMENT_ESTIMATION, departmentEstimationVoList, 7200);
        redisService.set(cacheKey + ":" + WithholdingReportTypeEnum.PRODUCT_PHASE_ESTIMATION, itemEstimationVoList, 7200);
        redisService.set(cacheKey + ":" + WithholdingReportTypeEnum.WITHHOLDING_DETAIL, withHoldingVos, 7200);
        redisService.set(cacheKey + ":" + WithholdingReportTypeEnum.GIFT_SURROUND, giftPage.getRecords(), 7200);
        redisService.set(cacheKey + ":" + WithholdingReportTypeEnum.AUDITED_CASHED_DETAIL, auditedPage.getRecords(), 7200);


        withHoldingService.addItemCache(cacheKey, new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(withHoldingVos, WithHoldingVo.class, WithHoldingDto.class, LinkedHashSet.class, ArrayList.class)));
        return vo;
    }

    private void assembleChannelType(List<RegionCollectGainsAndLossesVo> gainsAndLossesList) {
        List<String> customerCodes = gainsAndLossesList.stream().map(RegionCollectGainsAndLossesVo::getCustomerCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, String> customerMap = customerVos.stream().filter(e -> StringUtils.isNotEmpty(e.getCustomerCode()) && Objects.nonNull(e.getChannelType()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, CustomerVo::getChannelType, (v1, v2) -> v1));
        gainsAndLossesList.forEach(e -> {
            String customerCode = e.getCustomerCode();
            String channelTypeStr = customerMap.get(customerCode);
            if (StringUtils.isNotEmpty(channelTypeStr)) {
                List<String> channleTypeList = Arrays.asList(channelTypeStr.split(","));
                e.setChannelTypeList(channleTypeList);
                e.setChannelType(channelTypeStr);
            }
        });
    }

    @Autowired(required = false)
    private RedisService redisService;

    /**
     * 生成汇总
     *
     * @param dto
     * @return
     */
    @Override
    public WithHoldingCollectVo save(WithHoldingCollectDto dto) {
        WithHoldingCollect collect = withHoldingCollectRepository.findByUniqueKey(dto.getDepartmentCode(), dto.getYearMonthLy());
        if (collect == null) {
            String code = generateCodeService.generateCodeYearMonth(WithHoldingConstant.PREFIX_CODE_HZ);
            dto.setCollectCode(code);
            WithHoldingCollect entity = nebulaToolkitService.copyObjectByWhiteList(dto, WithHoldingCollect.class, LinkedHashSet.class, ArrayList.class);
            entity.setTenantCode(TenantUtils.getTenantCode());
            entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            entity.setStatus(ProcessStatusEnum.PREPARE.getDictCode());
            FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
            entity.setPositionCode(loginDetails.getPostCode());
            entity.setOrgCode(loginDetails.getOrgCode());
            entity.setOrgName(loginDetails.getOrgName());
            withHoldingCollectRepository.saveOrUpdate(entity);
            collect = entity;
        }
        return nebulaToolkitService.copyObjectByWhiteList(collect, WithHoldingCollectVo.class, LinkedHashSet.class, ArrayList.class);
    }

    /**
     * 删除数据
     *
     * @param ids 主键结合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> ids) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
        List<WithHoldingCollect> list = this.withHoldingCollectRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> codes = list.stream().map(WithHoldingCollect::getCollectCode).collect(Collectors.toList());
        list.forEach(item -> Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) || ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus()) ||
                        ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】计提汇总数据能删除"));

        //删除OA接口
        list = list.stream().filter(x ->
                StringUtils.equalsAny(x.getStatus(), ProcessStatusEnum.REJECT.getDictCode(),
                        ProcessStatusEnum.RECOVER.getDictCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(e -> ryOaProcessService.deleteWorkflow(e.getProcessKey(), e.getOaId()));
        }

        withHoldingRepository.clearCollectCode(codes);
        withHoldingCollectRepository.removeByIds(ids);
    }

    /**
     * 提交审批
     *
     * @param idList
     */
    @Override
    public void submit(List<String> idList) {
        Assert.notEmpty(idList, "请选择数据!");
        List<WithHoldingCollect> holdingList = withHoldingCollectRepository.findByIds(idList);
        Assert.notEmpty(holdingList, "选择的数据不存在,请刷新页面重试!");
        holdingList.forEach(item -> Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】核销数据能提交"));

        List<WithHoldingCollectVo> sendVoList = (List<WithHoldingCollectVo>) nebulaToolkitService.copyCollectionByWhiteList(holdingList,
                WithHoldingCollect.class, WithHoldingCollectVo.class, HashSet.class, ArrayList.class);

        Set<String> codes = sendVoList.stream().map(WithHoldingCollectVo::getCollectCode).collect(Collectors.toSet());

        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findByCollectCodes(new ArrayList<>(codes));
        Validate.notEmpty(withHoldingVos, "未找到对应的计提数据");

        Map<String, List<WithHoldingVo>> withHoldingVoMap = withHoldingVos.stream().collect(Collectors.groupingBy(e -> e.getCollectCode()));
        Map<String, List<RegionCollectMarketingEstimationVo>> dataMap = marketingEstimationService.findByCollectCodes(new ArrayList<>(codes));
        sendVoList.forEach(vo -> {
            vo.setWithHoldingList(withHoldingVoMap.get(vo.getCollectCode()));
            vo.setMarketingEstimationList(dataMap.get(vo.getCollectCode()));
            if (ProcessStatusEnum.PREPARE.getDictCode().equals(vo.getStatus())) {
                withHoldingCollectOaService.pushOa(vo);
            } else {
                withHoldingCollectOaService.resubmitOa(vo);
            }
        });
        List<String> withHoldingCodes = withHoldingVos.stream().map(e -> e.getWithHoldingCode()).collect(Collectors.toList());
        withHoldingRepository.updateStatus(withHoldingCodes, null, ProcessStatusEnum.COMMIT.getDictCode());
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public void recover(String code, String remark) {
        WithHoldingCollect entity = withHoldingCollectRepository.findByCode(code);
        if (withHoldingCollectOaService.oaWithdraw(code, remark)) {
            entity.setStatus(ProcessStatusEnum.RECOVER.getDictCode());
            withHoldingCollectRepository.saveOrUpdate(entity);
            withHoldingRepository.updateStatus(null, code, ProcessStatusEnum.RECOVER.getDictCode());
        } else {
            Validate.isTrue(false, "流程撤回失败");
        }
    }

    /**
     * OA审批回调
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void oaCallback(WithHoldingCollectDto dto) {
        WithHoldingCollect entity = withHoldingCollectRepository.findByCode(dto.getCollectCode());
        if (entity == null || StringUtils.equalsAny(entity.getStatus(), ProcessStatusEnum.PASS.getDictCode())) {
            return;
        }
        Date passDate = new Date();
        entity.setStatus(dto.getStatus());
        if (ProcessStatusEnum.PASS.getDictCode().equals(dto.getStatus())) {
            entity.setPassDate(passDate);
        }
        withHoldingCollectRepository.saveOrUpdate(entity);
        if (ProcessStatusEnum.PASS.getDictCode().equals(dto.getStatus())) {
            List<WithHoldingVo> voList = withHoldingRepository.findByCollectCode(dto.getCollectCode());
            //按公司维度拆分
            List<List<WithHoldingVo>> partitionAll = new ArrayList<>();
            Map<String, List<WithHoldingVo>> companyMap = voList.stream().collect(Collectors.groupingBy(e -> e.getCompanyCode()));
            companyMap.forEach((k, v) -> {
                //费控单次最大接收499
                List<List<WithHoldingVo>> partition = Lists.partition(v, 400);
                partitionAll.addAll(partition);
            });

            List<String> codes = generateCodeService.generateCode(WithHoldingConstant.PREFIX_CODE_TSFK, partitionAll.size());
            for (int i = 0; i < partitionAll.size(); i++) {
                List<WithHoldingVo> withHoldingVos = partitionAll.get(i);
                for (WithHoldingVo vo : withHoldingVos) {
                    vo.setPushHecCode(codes.get(i));
                    vo.setStatus(dto.getStatus());
                    vo.setPassDate(passDate);
                }
            }
            List<WithHoldingVo> collect = partitionAll.stream().flatMap(List::stream).collect(Collectors.toList());
            withHoldingRepository.saveOrUpdateBatch(nebulaToolkitService.copyCollectionByBlankList(collect, WithHoldingVo.class, WithHolding.class, HashSet.class, ArrayList.class));
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        withHoldingSendHecService.pushCollectAutoAsync(Collections.singletonList(entity.getId()),
                                loginUserService.getAbstractLoginUser());
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            });
        } else if (ProcessStatusEnum.REJECT.getDictCode().equals(dto.getStatus())) {
            withHoldingRepository.updateStatus(null, dto.getCollectCode(), ProcessStatusEnum.REJECT.getDictCode());
        }
    }

    @Override
    public void modifyActivityPrepayRecord(WithHoldingCollectDto dto) {
        WithHoldingCollect entity = withHoldingCollectRepository.findByCode(dto.getCollectCode());
        if (entity == null || !StringUtil.equals(ProcessStatusEnum.PASS.getDictCode(), entity.getStatus())) {
            return;
        }
        List<WithHoldingVo> voList = withHoldingRepository.findByCollectCodeAndWithHoldingType(dto.getCollectCode(), WithHoldingTypeEnum.NOT_AUDIT.getDictCode());
        voList = voList.stream().filter(e -> StringUtils.isNotBlank(e.getActivitiesDetailCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        //判断活动是否发生过预付（预付类型为“活动预付）
        List<String> activitiesDetailCodes = voList.stream().map(WithHoldingVo::getActivitiesDetailCode).collect(Collectors.toList());
        List<ActivityPrepayDetailRecord> detailRecordList = activityPrepayDetailRecordRepository.findBySchemeDetailCodes(activitiesDetailCodes);
        detailRecordList = detailRecordList.stream().filter(e -> StringUtils.equals(TpmPrepayTypeEnum.activity.getCode(), e.getPrepayType())).collect(Collectors.toList());
        List<String> filterCodes = detailRecordList.stream().map(ActivityPrepayDetailRecord::getSchemeDetailCode).collect(Collectors.toList());
        List<String> prepayDetailCodes = detailRecordList.stream().map(ActivityPrepayDetailRecord::getPrepayDetailCode).collect(Collectors.toList());
        voList = voList.stream().filter(e -> filterCodes.contains(e.getActivitiesDetailCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        activityPrepayDetailRecordService.operateLock(prepayDetailCodes, true);
        try {
            Map<String, List<ActivityPrepayDetailRecord>> detailRecordListMap =
                    detailRecordList.stream().collect(Collectors.groupingBy(ActivityPrepayDetailRecord::getSchemeDetailCode));
            List<ActivityPrepayDetailRecordItemDto> list = new ArrayList<>();
            for (WithHoldingVo vo : voList) {
                List<ActivityPrepayDetailRecord> prepayDetailRecords = detailRecordListMap.get(vo.getActivitiesDetailCode());
                BigDecimal totalPrepayAmount = prepayDetailRecords.stream().map(ActivityPrepayDetailRecord::getPrepayApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                //计提金额<预付金额 才更新待结转金额
                if (vo.getActualAmount().compareTo(totalPrepayAmount) >= 0) {
                    continue;
                }
                //更新预付明细的“待结转金额”时若寻到多条预付明细，待结转金额按预付时间从当前往历史顺序更新待结转金额，待结转金额不能大于预付明细的“实际支付金额-已结转金额”
                prepayDetailRecords.sort(Comparator.comparing(ActivityPrepayDetailRecord::getPrepayDetailCode).reversed());
                BigDecimal totalAmount = totalPrepayAmount.subtract(vo.getActualAmount());

                for (ActivityPrepayDetailRecord prepayDetailRecord : prepayDetailRecords) {
                    BigDecimal maxAmount = Optional.ofNullable(prepayDetailRecord.getPrepayApplyAmount()).orElse(BigDecimal.ZERO)
                            .subtract(Optional.ofNullable(prepayDetailRecord.getCarriedAmount()).orElse(BigDecimal.ZERO))
                            .subtract(Optional.ofNullable(prepayDetailRecord.getPrepareCarryAmount()).orElse(BigDecimal.ZERO));
                    BigDecimal amount = BigDecimal.ZERO;
                    if (maxAmount.compareTo(totalAmount) >= 0) {
                        amount = totalAmount;
                    } else {
                        amount = maxAmount;
                    }
                    totalAmount = totalAmount.subtract(amount);
//                    log.info("1 {} 2 {} 3{} 4 {} 5{}",maxAmount,totalAmount,amount,dto.getBusinessCode(),voList.size());

                    if (amount.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    //构建操作明细
                    ActivityPrepayDetailRecordItemDto itemDto = new ActivityPrepayDetailRecordItemDto();
                    itemDto.setPrepayCode(prepayDetailRecord.getPrepayCode());
                    itemDto.setPrepayName(prepayDetailRecord.getPrepayName());
                    itemDto.setPrepayDetailCode(prepayDetailRecord.getPrepayDetailCode());
                    itemDto.setSchemeCode(prepayDetailRecord.getSchemeCode());
                    itemDto.setSchemeName(prepayDetailRecord.getSchemeName());
                    itemDto.setSchemeDetailCode(prepayDetailRecord.getSchemeDetailCode());
                    itemDto.setType(ActivityPrepayDetailRecordOperateTypeEnum.ADD.getCode());
                    itemDto.setBusinessCode(vo.getWithHoldingCode());
                    itemDto.setBusinessName("计提审批通过");
                    itemDto.setAmount(amount);
                    list.add(itemDto);
                }
            }
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            //更新预付明细，生成操作记录
            activityPrepayDetailRecordService.operate(list);
        } finally {
            activityPrepayDetailRecordService.operateLock(prepayDetailCodes, false);
        }
    }

    @Override
    public Page<MarketingPlanCaseVo> findGiftAndSurroundingList(Pageable pageable, MarketingPlanCaseVo caseVo, String collectCode) {
        WithHoldingCollect entity = withHoldingCollectRepository.findByCode(collectCode);
        Validate.notNull(entity, "未找到对应的汇总数据");
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findByCollectCode(entity.getCollectCode());
        Validate.notEmpty(withHoldingVos, "未找到对应的计提数据");

        String orgCode = entity.getDepartmentCode();
        String yearMonthLy = entity.getYearMonthLy();
        List<OrgVo> orgChildrenVos = orgVoService.findAllChildrenByOrgCode(orgCode);
        List<String> orgCodeList = orgChildrenVos.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());

        List<DictDataVo> giftSurroundExpenseItem = dictDataVoService.findByDictTypeCode("gift_surround_expense_item");
        List<String> itemCodes = giftSurroundExpenseItem.stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
        MarketingPlanCaseVo planCaseVo = new MarketingPlanCaseVo();
        planCaseVo.setYears(yearMonthLy);
        planCaseVo.setOrgCodeList(orgCodeList);
        planCaseVo.setDetailCodeList(itemCodes);
        planCaseVo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        Page<MarketingPlanCaseVo> marketingPlanCaseReportList = marketingPlanCaseService.findMarketingPlanCaseReportList(pageable, planCaseVo);
        return marketingPlanCaseReportList;
    }

    @Override
    public Page<FeeCashDetailVo> findAuditedAndCashedActivityList(Pageable pageable, MarketingPlanCaseVo caseVo, String collectCode) {
        WithHoldingCollect entity = withHoldingCollectRepository.findByCode(collectCode);
        Validate.notNull(entity, "未找到对应的汇总数据");
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findByCollectCode(entity.getCollectCode());
        Validate.notEmpty(withHoldingVos, "未找到对应的计提数据");

        String orgCode = entity.getDepartmentCode();
        String yearMonthLy = entity.getYearMonthLy();
        List<OrgVo> orgChildrenVos = orgVoService.findAllChildrenByOrgCode(orgCode);
        List<String> orgCodeList = orgChildrenVos.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());

        FeeCashDetailDto feeCashDetailDto = new FeeCashDetailDto();
        feeCashDetailDto.setYears(yearMonthLy);
        feeCashDetailDto.setBelongDepartmentCodes(orgCodeList);
//        feeCashDetailDto.setBeFullAudit("Y");

        Page<FeeCashDetailVo> page = feeCashDetailRepository.findFullAuditedDetailList(pageable, feeCashDetailDto);
        return page;
    }
}
