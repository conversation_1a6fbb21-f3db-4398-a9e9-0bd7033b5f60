package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@ApiModel(value = "FeeCashPrepay", description = "费用预付明细")
@TableName("tpm_fee_cash_prepay")
@Getter
@Setter
@Entity(name = "tpm_fee_cash_prepay")
@org.hibernate.annotations.Table(appliesTo = "tpm_fee_cash_prepay", comment = "费用预付明细")
@Table(name = "tpm_fee_cash_prepay", indexes = {
        @Index(name = "fee_cash_idx1", columnList = "cash_code"),
})
public class FeeCashPrepay extends TenantFlagOpEntity {

    @ApiModelProperty("兑付名称")
    @Column(name = "cash_name", columnDefinition = "VARCHAR(255) COMMENT '兑付名称 '")
    private String cashName;

    @ApiModelProperty("兑付编号")
    @Column(name = "cash_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '兑付编号 '")
    private String cashCode;

    @ApiModelProperty("预付编号")
    @Column(name = "prepay_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付编号 '")
    private String prepayCode;

    @ApiModelProperty("预付名称")
    @Column(name = "prepay_name", columnDefinition = "varchar(128) comment '预付名称'")
    private String prepayName;

    @ApiModelProperty("预付明细编号")
    @Column(name = "prepay_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '预付明细编号 '")
    private String prepayDetailCode;

    @ApiModelProperty("核销申请编号")
    @Column(name = "audit_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
    private String auditCode;

    @ApiModelProperty("核销明细编号")
    @Column(name = "audit_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销明细编号 '")
    private String auditDetailCode;

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称'")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("活动大类编码")
    @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    @Column(name = "detail_code", columnDefinition = "varchar(32) comment '活动细类编码'")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    @Column(name = "detail_name", columnDefinition = "varchar(64) comment '活动细类名称'")
    private String detailName;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("供应商编码")
    @Column(name = "payee_code", columnDefinition = "VARCHAR(64) COMMENT '供应商编码 '")
    private String payeeCode;

    @ApiModelProperty("供应商名称")
    @Column(name = "payee_name", columnDefinition = "VARCHAR(255) COMMENT '供应商名称 '")
    private String payeeName;

    @ApiModelProperty("申请金额")
    @Column(name = "apply_amount", columnDefinition = "decimal(18,4) comment '申请金额'")
    private BigDecimal applyAmount;

    @ApiModelProperty("已预付金额")
    @Column(name = "prepay_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '已预付金额 '")
    private BigDecimal prepayAmount;

    @ApiModelProperty("预付可冲销金额")
    @Column(name = "available_reversed_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '预付可冲销金额 '")
    private BigDecimal availableReversedAmount;

    @ApiModelProperty("待结转金额")
    @Column(name = "prepare_carry_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '待结转金额 '")
    private BigDecimal prepareCarryAmount;

    @ApiModelProperty("已冲销金额")
    @Column(name = "reversed_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '已冲销金额 '")
    private BigDecimal reversedAmount;

    @ApiModelProperty("本次冲抵金额")
    @Column(name = "this_reversed_amount", length = 20, columnDefinition = "DECIMAL(20,6) COMMENT '本次冲抵金额 '")
    private BigDecimal thisReversedAmount;

    @ApiModelProperty("预付描述")
    @Column(name = "description", columnDefinition = "VARCHAR(255) COMMENT '预付描述 '")
    private String description;

    @ApiModelProperty("收款行编号")
    @Column(name = "line_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '收款行编号 '")
    private String lineCode;


}
