<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.AuditActivitiesMapper">
    <resultMap type="com.biz.crm.tpm.business.pay.local.entity.AuditActivities" id="AuditActivitiesMap">
    </resultMap>

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.AuditActivitiesVo">
        select
        t.*
        from tpm_audit_activities t
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != '' ">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.createName != null and dto.createName != '' ">
                <bind name = "createName" value = " '%' + dto.createName + '%' " />
                and t.create_name like #{createName}
            </if>
            <if test="dto.activitiesCode != null and dto.activitiesCode != '' ">
                <bind name = "activitiesCode" value = " '%' + dto.activitiesCode + '%' " />
                and t.activities_code like #{activitiesCode}
            </if>
            <if test="dto.activitiesName != null and dto.activitiesName != '' ">
                <bind name = "activitiesName" value = " '%' + dto.activitiesName + '%' " />
                and t.activities_name like #{activitiesName}
            </if>
        </where>
        order by t.create_time desc,t.id
    </select>
</mapper>