package com.biz.crm.tpm.business.pay.local.repository;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.BackWithHoldingWriteOff;
import com.biz.crm.tpm.business.pay.local.entity.WithHolding;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff;
import com.biz.crm.tpm.business.pay.local.mapper.BackWithHoldingWriteOffMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * 费用反冲销(BackWithHoldingWriteOff)表数据库访问层
 */
@Component
public class BackWithHoldingWriteOffRepository extends ServiceImpl<BackWithHoldingWriteOffMapper, BackWithHoldingWriteOff> {
    public List<BackWithHoldingWriteOff> findByUniqueKeys(List<String> uniqueKeys) {
        return this.baseMapper.findByUniqueKeys(uniqueKeys);
    }

    public List<BackWithHoldingWriteOff> findByCodes(List<String> withHoldingCodes) {
        return this.lambdaQuery()
                .in(BackWithHoldingWriteOff::getWithHoldingCode, withHoldingCodes)
                .eq(BackWithHoldingWriteOff::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(BackWithHoldingWriteOff::getTenantCode, TenantUtils.getTenantCode())
                .list();

    }

    // 所有依赖注入和方法已移除，仅保留类结构
}
