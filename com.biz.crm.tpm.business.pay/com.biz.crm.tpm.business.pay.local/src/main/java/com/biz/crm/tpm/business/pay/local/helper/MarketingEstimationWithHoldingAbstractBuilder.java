package com.biz.crm.tpm.business.pay.local.helper;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.biz.crm.dms.business.order.common.sdk.enums.ItemTypeEnum;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectProjectEnum;
import com.biz.crm.tpm.business.activities.regioncollect.service.strategy.MarketingEstimationBuilder;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc 营销测算
 * <AUTHOR>
 * @date 2024/6/26
 */
@Slf4j
public class MarketingEstimationWithHoldingAbstractBuilder extends MarketingEstimationBuilder<List<String>, String> {

    private static final String column = ":";

    private List<RegionCollectMarketingEstimationVo> dataList = Lists.newArrayList();

    private String orgCode;

    private List<String> orgCodeList;

    private List<String> categoryCostCodeList = Lists.newArrayList();

    private List<CostBudgetIncomeVo> incomeVos;

    private List<MarketingPlanCase> planCaseList;

    private List<MarketingPlanCase> planCaseListAll;

    private List<WithHoldingVo> withHoldingList;

    private List<DmsWarehouseOrderDetailVo> orderDetailList;

    private List<RegionCollectGainsAndLossesVo> gainsAndLossesVoList;

    private List<RegionCollectMarketingEstimationVo> planMarketingEstimationVos;

    private CostBudgetIncomeService costBudgetIncomeService;

    private MarketingPlanCaseRepository caseRepository;

    private CostBudgetVoService costBudgetVoService;

    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;

    private CustomerVoService customerVoService;

    private OrgVoService orgVoService;

    private PublicShareRatioService publicShareRatioService;

    private MaterialVoService materialVoService;

    private FeeCashService feeCashService;

    private ProductPhaseVoService productPhaseVoService;


    //预计收入
    private RegionCollectMarketingEstimationVo estimationRevenueData = new RegionCollectMarketingEstimationVo();

    //生产成本
    private RegionCollectMarketingEstimationVo productionCostData = new RegionCollectMarketingEstimationVo();

    //产品运输费用
    private RegionCollectMarketingEstimationVo productTransportCostData = new RegionCollectMarketingEstimationVo();

    //周边运输费用
    private RegionCollectMarketingEstimationVo peripheryTransportData = new RegionCollectMarketingEstimationVo();

    //搭赠费用
    private RegionCollectMarketingEstimationVo giftCostData = new RegionCollectMarketingEstimationVo();

    //活动大类
    private List<RegionCollectMarketingEstimationVo> categoryDataList = Lists.newArrayList();

    //营销费用小计
    private RegionCollectMarketingEstimationVo marketingCostData = new RegionCollectMarketingEstimationVo();

    //人工、差旅
    private RegionCollectMarketingEstimationVo artificialTravelOnBusinessData = new RegionCollectMarketingEstimationVo();

    //利润
    private RegionCollectMarketingEstimationVo profit = new RegionCollectMarketingEstimationVo();

    //费用合计
    private RegionCollectMarketingEstimationVo costData = new RegionCollectMarketingEstimationVo();

    private RegionCollectMarketingEstimationVo costShiftData = new RegionCollectMarketingEstimationVo();


    public static MarketingEstimationWithHoldingAbstractBuilder builder(ApplicationContext context, String orgCode, List<WithHoldingVo> withHoldingList,
                                                                        List<MarketingPlanCase> planCaseList,
                                                                        List<MarketingPlanCase> planCaseListAll,
                                                                        List<RegionCollectGainsAndLossesVo> gainsAndLossesVoList,
                                                                        List<RegionCollectMarketingEstimationVo> planMarketingEstimationVos) {
        MarketingEstimationWithHoldingAbstractBuilder builder = new MarketingEstimationWithHoldingAbstractBuilder();
        builder.withHoldingList = withHoldingList;
        builder.orgCode = orgCode;
        builder.costBudgetIncomeService = context.getBean(CostBudgetIncomeService.class);
        builder.caseRepository = context.getBean(MarketingPlanCaseRepository.class);
        builder.costBudgetVoService = context.getBean(CostBudgetVoService.class);
        builder.dmsWarehouseOrderDetailVoService = context.getBean(DmsWarehouseOrderDetailVoService.class);
        builder.customerVoService = context.getBean(CustomerVoService.class);
        builder.orgVoService = context.getBean(OrgVoService.class);
        builder.publicShareRatioService = context.getBean(PublicShareRatioService.class);
        builder.feeCashService = context.getBean(FeeCashService.class);
        builder.gainsAndLossesVoList = gainsAndLossesVoList;
        builder.planMarketingEstimationVos = planMarketingEstimationVos;
        builder.orgCode = orgCode;
        builder.planCaseList = planCaseList;
        builder.planCaseListAll = planCaseListAll;
        builder.productPhaseVoService = context.getBean(ProductPhaseVoService.class);
        return builder;
    }


    /**
     * 预计收入
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder estimationRevenue() {
        estimationRevenueData.setProjectCode(RegionCollectProjectEnum.actual_revenue.getCode());
        estimationRevenueData.setProjectName(RegionCollectProjectEnum.actual_revenue.getName());
        estimationRevenueData.setSort(RegionCollectProjectEnum.actual_revenue.getSort());

        estimationRevenueData.setDifferenceRatio(BigDecimal.ZERO);
        estimationRevenueData.setDifferenceAmount(BigDecimal.ZERO);

        estimationRevenueData.setEstimateAmount(BigDecimal.ZERO);
        estimationRevenueData.setBudgetAmount(BigDecimal.ZERO);

        estimationRevenueData.setPlanRatio(BigDecimal.ZERO);
        estimationRevenueData.setPlanAmount(BigDecimal.ZERO);

        //获取该部门下所有的客户，并获取对应月份的发货单明细
        orgCodeList = orgVoService.findAllChildrenByOrgCode(orgCode).stream().map(e -> e.getOrgCode()).collect(Collectors.toList());
//        List<String> customerCodes = customerVoService.findByOrgCodes(orgCodeList).stream().map(e -> e.getCustomerCode()).collect(Collectors.toList());
//        TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
//        dto.setCustomerCodes(customerCodes);
//        String startDate = years + "-01";
//        String endDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(startDate, "yyyy-MM-dd")), "yyyy-MM-dd");
//        dto.setSearchStartTime(startDate);
//        dto.setSearchEndTime(endDate);
//        orderDetailList = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
//        BigDecimal estimationAmount = BigDecimal.ZERO;
//        if (CollectionUtils.isNotEmpty(orderDetailList)) {
//            //实际收入(发货减退货)
//            BigDecimal delivery = orderDetailList.stream().filter(e -> e.getTotalAmount() != null && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
//                    .filter(Objects::nonNull)
//                    .map(DmsWarehouseOrderDetailVo::getTotalAmount)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal stored = orderDetailList.stream().filter(e -> e.getSignAmount() != null && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(e.getWarehouseOrderType()))
//                    .filter(Objects::nonNull)
//                    .map(DmsWarehouseOrderDetailVo::getSignAmount)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            estimationAmount = (Optional.ofNullable(delivery).orElse(BigDecimal.ZERO)).subtract((Optional.ofNullable(stored).orElse(BigDecimal.ZERO)));
//        }
        log.info("estimationRevenue-gainsAndLossesVoList: {}", JSON.toJSONString(gainsAndLossesVoList));
        BigDecimal estimateAmount = this.gainsAndLossesVoList.stream()
                .map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("estimationRevenue-estimateAmount: {}", estimateAmount);
        //预计达成-转换为万元
        estimationRevenueData.setEstimateAmount(estimateAmount);
        //预算达成
        this.incomeVos = costBudgetIncomeService.findIncomeAmountByOrgCodeAndYears(orgCodeList, years);

        List<String> itemCodes = incomeVos.stream().map(CostBudgetIncomeVo::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
        Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> null == v.getTaxRate() ? BigDecimal.ZERO : v.getTaxRate()));

        // ROUND(t.income_amount / (1 + IFNULL(p.tax_rate, 0)), 4) as no_tax_income_amount
        BigDecimal budgetAmount = incomeVos.stream().filter(e -> e.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode())).map(x -> Optional.ofNullable(x.getIncomeAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(x.getItemCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP))
                .collect(Collectors.reducing(BigDecimal.ZERO, BigDecimal::add));

//        BigDecimal budgetAmount = incomeVos.stream().map(x -> Optional.ofNullable(x.getIncomeAmount()).orElse(BigDecimal.ZERO))
//                .filter(Objects::nonNull)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
        estimationRevenueData.setBudgetAmount(budgetAmount);

        //规划收入
        RegionCollectMarketingEstimationVo plan = planMarketingEstimationVos.stream()
                .filter(e -> RegionCollectProjectEnum.actual_revenue.getCode().equals(e.getProjectCode())).findAny().orElse(new RegionCollectMarketingEstimationVo());
        estimationRevenueData.setPlanAmount(plan.getEstimateAmount());

        dataList.add(estimationRevenueData);
        return this;
    }

    /**
     * 生产成本
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder productionCost() {
        productionCostData.setProjectCode(RegionCollectProjectEnum.production_cost.getCode());
        productionCostData.setProjectName(RegionCollectProjectEnum.production_cost.getName());
        productionCostData.setSort(RegionCollectProjectEnum.production_cost.getSort());

        productionCostData.setDifferenceRatio(BigDecimal.ZERO);
        productionCostData.setDifferenceAmount(BigDecimal.ZERO);
        productionCostData.setEstimateAmount(BigDecimal.ZERO);
        productionCostData.setEstimateRatio(BigDecimal.ZERO);
        productionCostData.setBudgetRatio(BigDecimal.ZERO);
        productionCostData.setBudgetAmount(BigDecimal.ZERO);
        productionCostData.setPlanRatio(BigDecimal.ZERO);
        productionCostData.setPlanAmount(BigDecimal.ZERO);

//        BigDecimal estimationAmount = BigDecimal.ZERO;

//        if (CollectionUtils.isNotEmpty(orderDetailList)) {
//            //查询物料成本
//            Set<String> materialCodes = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getGoodsCode()) && ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(x.getItemType())).filter(Objects::nonNull)
//                    .map(DmsWarehouseOrderDetailVo::getGoodsCode).collect(Collectors.toSet());
//            Set<String> companyCodeSet = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode()) && ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(x.getItemType())).filter(Objects::nonNull)
//                    .map(DmsWarehouseOrderDetailVo::getCompanyCode).collect(Collectors.toSet());
//            MaterialSearchDto searchDto = new MaterialSearchDto();
//            searchDto.setMaterialCodeSet(materialCodes);
//            searchDto.setCompanyCodeSet(companyCodeSet);
//            List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
//            Map<String, BigDecimal> materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO)));
//            //生产成本(对应物料主数据的单价*实际发货数量)
//            Map<String, BigDecimal> goodsMap = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(e.getItemType())).filter(Objects::nonNull)
//                    .collect(Collectors.groupingBy(e -> e.getGoodsCode(), Collectors.mapping(DmsWarehouseOrderDetailVo::getRealOutNum, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
//            for (Map.Entry<String, BigDecimal> entry : goodsMap.entrySet()) {
//                estimationAmount = estimationAmount.add(entry.getValue().multiply(materialMap.getOrDefault(entry.getKey(), BigDecimal.ZERO)));
//            }
//        }
//        productionCostData.setEstimateAmount(estimationAmount);

        BigDecimal estimateAmount = this.gainsAndLossesVoList.stream()
                .map(x -> Optional.ofNullable(x.getProductionCosts()).orElse(BigDecimal.ZERO))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        productionCostData.setEstimateAmount(estimateAmount);

        BigDecimal budgetAmount = incomeVos.stream().map(x -> Optional.ofNullable(x.getCostAmount()).orElse(BigDecimal.ZERO))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //生产成本
        productionCostData.setBudgetAmount(budgetAmount);

        BigDecimal budgetEstimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetEstimateRatio = productionCostData.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        productionCostData.setBudgetRatio(budgetEstimateRatio);

        //规划收入
        RegionCollectMarketingEstimationVo plan = planMarketingEstimationVos.stream()
                .filter(e -> RegionCollectProjectEnum.production_cost.getCode().equals(e.getProjectCode())).findAny().orElse(new RegionCollectMarketingEstimationVo());
        productionCostData.setPlanAmount(plan.getEstimateAmount());

        dataList.add(productionCostData);
        return this;
    }

    /**
     * 毛利率
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder grossProfitMargin() {
        RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
        data.setProjectCode(RegionCollectProjectEnum.gross_profit_margin.getCode());
        data.setProjectName(RegionCollectProjectEnum.gross_profit_margin.getName());
        data.setSort(RegionCollectProjectEnum.gross_profit_margin.getSort());

        data.setDifferenceRatio(BigDecimal.ZERO);
        data.setDifferenceAmount(BigDecimal.ZERO);
        data.setEstimateAmount(BigDecimal.ZERO);
        data.setBudgetAmount(BigDecimal.ZERO);
        data.setPlanRatio(BigDecimal.ZERO);
        data.setPlanAmount(BigDecimal.ZERO);

        if (BigDecimal.ZERO.compareTo(estimationRevenueData.getEstimateAmount()) != 0) {
            BigDecimal estimationAmount = BigDecimal.ONE.subtract(productionCostData.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
            data.setEstimateAmount(estimationAmount);
        }
        if (BigDecimal.ZERO.compareTo(estimationRevenueData.getBudgetAmount()) != 0) {
            BigDecimal budgetAmount = BigDecimal.ONE.subtract(productionCostData.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
            data.setBudgetAmount(budgetAmount);
        }
        if (BigDecimal.ZERO.compareTo(estimationRevenueData.getPlanAmount()) != 0) {
            BigDecimal planAmount = BigDecimal.ONE.subtract(productionCostData.getPlanAmount().divide(estimationRevenueData.getPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
            data.setPlanAmount(planAmount);
        }
        dataList.add(data);
        return this;
    }

    /**
     * 产品运输费用
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder productTransportCost() {
        productTransportCostData.setProjectCode(RegionCollectProjectEnum.product_transport_cost.getCode());
        productTransportCostData.setProjectName(RegionCollectProjectEnum.product_transport_cost.getName());
        productTransportCostData.setSort(RegionCollectProjectEnum.product_transport_cost.getSort());

        productTransportCostData.setDifferenceRatio(BigDecimal.ZERO);
        productTransportCostData.setDifferenceAmount(BigDecimal.ZERO);
        productTransportCostData.setEstimateAmount(BigDecimal.ZERO);
        productTransportCostData.setEstimateRatio(BigDecimal.ZERO);
        productTransportCostData.setBudgetRatio(BigDecimal.ZERO);
        productTransportCostData.setBudgetAmount(BigDecimal.ZERO);
        productTransportCostData.setPlanRatio(BigDecimal.ZERO);
        productTransportCostData.setPlanAmount(BigDecimal.ZERO);

//        //产品运输费用-预计达成金额
//        //行项目类型：本品、赠品、货补、尾差
//        BigDecimal estimateAmount = BigDecimal.ZERO;
//        List<String> itemTypeList = Arrays.asList(ItemTypeEnum.NORMAL_GOODS.getDictCode(),
//                ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
//                ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
//                ItemTypeEnum.COMPENSATED_GOODS.getDictCode(),
//                ItemTypeEnum.TAIL_GOODS.getDictCode());
//        List<DmsWarehouseOrderDetailVo> filterOrder = orderDetailList.stream().filter(e -> itemTypeList.contains(e.getItemType())).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(filterOrder)) {
//            estimateAmount = filterOrder.stream().filter(e -> e.getExpressFee() != null)
//                    .map(DmsWarehouseOrderDetailVo::getExpressFee)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//        }
//        productTransportCostData.setEstimateAmount(estimateAmount);

        BigDecimal estimateAmount = this.gainsAndLossesVoList.stream().map(x -> Optional.ofNullable(x.getProductTransportCost()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        productTransportCostData.setEstimateAmount(estimateAmount);

        //产品运输方式-预算达成金额
        BigDecimal budgetAmount = incomeVos.stream().map(x -> Optional.ofNullable(x.getLogisticsAmount()).orElse(BigDecimal.ZERO))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        productTransportCostData.setBudgetAmount(budgetAmount);

        //规划收入
        RegionCollectMarketingEstimationVo plan = planMarketingEstimationVos.stream()
                .filter(e -> RegionCollectProjectEnum.product_transport_cost.getCode().equals(e.getProjectCode())).findAny().orElse(new RegionCollectMarketingEstimationVo());
        productTransportCostData.setPlanAmount(plan.getEstimateAmount());

        dataList.add(productTransportCostData);
        return this;
    }

    /**
     * 周边运输费用
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder peripheryTransport() {
        peripheryTransportData.setProjectCode(RegionCollectProjectEnum.periphery_transport.getCode());
        peripheryTransportData.setProjectName(RegionCollectProjectEnum.periphery_transport.getName());
        peripheryTransportData.setSort(RegionCollectProjectEnum.periphery_transport.getSort());

        peripheryTransportData.setDifferenceRatio(BigDecimal.ZERO);
        peripheryTransportData.setDifferenceAmount(BigDecimal.ZERO);
        peripheryTransportData.setEstimateAmount(BigDecimal.ZERO);
        peripheryTransportData.setEstimateRatio(BigDecimal.ZERO);
        peripheryTransportData.setBudgetRatio(BigDecimal.ZERO);
        peripheryTransportData.setBudgetAmount(BigDecimal.ZERO);
        peripheryTransportData.setPlanRatio(BigDecimal.ZERO);
        peripheryTransportData.setPlanAmount(BigDecimal.ZERO);

//        BigDecimal estimateAmount = BigDecimal.ZERO;
//        List<String> itemTypeList = Collections.singletonList(ItemTypeEnum.MATERIAL_GOODS.getDictCode());
//        List<DmsWarehouseOrderDetailVo> filterOrder = orderDetailList.stream().filter(e -> itemTypeList.contains(e.getItemType())).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(filterOrder)) {
//            estimateAmount = filterOrder.stream().filter(e -> e.getExpressFee() != null)
//                    .map(DmsWarehouseOrderDetailVo::getExpressFee)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//        }
//        peripheryTransportData.setEstimateAmount(estimateAmount);

        BigDecimal estimateAmount = this.gainsAndLossesVoList.stream().map(x -> Optional.ofNullable(x.getPeripheryTransportCost()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        peripheryTransportData.setEstimateAmount(estimateAmount);

        //规划收入
        RegionCollectMarketingEstimationVo plan = planMarketingEstimationVos.stream()
                .filter(e -> RegionCollectProjectEnum.periphery_transport.getCode().equals(e.getProjectCode())).findAny().orElse(new RegionCollectMarketingEstimationVo());
        peripheryTransportData.setPlanAmount(plan.getEstimateAmount());

        dataList.add(peripheryTransportData);
        return this;
    }

    /**
     * 运输费用合计
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder transportTotal() {
        RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
        data.setProjectCode(RegionCollectProjectEnum.transport_total.getCode());
        data.setProjectName(RegionCollectProjectEnum.transport_total.getName());
        data.setSort(RegionCollectProjectEnum.transport_total.getSort());

        data.setDifferenceRatio(BigDecimal.ZERO);
        data.setDifferenceAmount(BigDecimal.ZERO);
        data.setEstimateAmount(BigDecimal.ZERO);
        data.setEstimateRatio(BigDecimal.ZERO);
        data.setBudgetRatio(BigDecimal.ZERO);
        data.setBudgetAmount(BigDecimal.ZERO);
        data.setPlanRatio(BigDecimal.ZERO);
        data.setPlanAmount(BigDecimal.ZERO);

        data.setEstimateAmount(productTransportCostData.getEstimateAmount().add(peripheryTransportData.getEstimateAmount()));
        data.setBudgetAmount(productTransportCostData.getBudgetAmount().add(peripheryTransportData.getBudgetAmount()));
        data.setPlanAmount(productTransportCostData.getPlanAmount().add(peripheryTransportData.getPlanAmount()));


        dataList.add(data);
        return this;
    }

    /**
     * 搭赠费用
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder giftCost() {
        giftCostData.setProjectCode(RegionCollectProjectEnum.gift_cost.getCode());
        giftCostData.setProjectName(RegionCollectProjectEnum.gift_cost.getName());
        giftCostData.setSort(RegionCollectProjectEnum.gift_cost.getSort());

        giftCostData.setDifferenceRatio(BigDecimal.ZERO);
        giftCostData.setDifferenceAmount(BigDecimal.ZERO);
        giftCostData.setEstimateAmount(BigDecimal.ZERO);
        giftCostData.setEstimateRatio(BigDecimal.ZERO);
        giftCostData.setBudgetRatio(BigDecimal.ZERO);
        giftCostData.setBudgetAmount(BigDecimal.ZERO);
        giftCostData.setPlanRatio(BigDecimal.ZERO);
        giftCostData.setPlanAmount(BigDecimal.ZERO);


        BigDecimal estimateAmount = this.gainsAndLossesVoList.stream().map(x -> Optional.ofNullable(x.getGiftCost()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        giftCostData.setEstimateAmount(estimateAmount);

        RegionCollectMarketingEstimationVo plan = planMarketingEstimationVos.stream()
                .filter(e -> RegionCollectProjectEnum.actual_revenue.getCode().equals(e.getProjectCode())).findAny().orElse(new RegionCollectMarketingEstimationVo());
        giftCostData.setPlanAmount(plan.getGiftCost().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));


        dataList.add(giftCostData);
        return this;
    }

    /**
     * 活动大类费用合计
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder categoryCost() {
        //计提金额+兑付金额汇总
        Map<String, BigDecimal> categoryCaseMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(planCaseList)) {
            // 查询兑付明细表中当月的已审批通过的本次兑付未税金额按活动大类维度汇总
            List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findNoTaxCashAmountByYearsAndOrgCodes(years, orgCodeList);
            categoryCaseMap = feeCashDetailVos.stream()
                .collect(Collectors.groupingBy(x -> x.getCategoryCode() + column + x.getCategoryName(),
                    Collectors.mapping(e -> e.getThisCashNoTaxAmount() == null? new BigDecimal(0) : e.getThisCashNoTaxAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
//            categoryCaseMap = planCaseList.stream()
//                .collect(Collectors.groupingBy(x -> x.getCategoryCode() + column + x.getCategoryName(),
//                    Collectors.mapping(MarketingPlanCase::getCashAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        Map<String, BigDecimal> categoryMap = withHoldingList.stream()
            .peek(e->e.setAccountAmount(Optional.ofNullable(e.getActualReportAmount()).orElse(BigDecimal.ZERO)))
            .collect(Collectors.groupingBy(x -> x.getCostTypeCategoryCode() + column + x.getCostTypeCategoryName(),
                Collectors.mapping(e -> e.getNoTaxActualReportAmount() == null? new BigDecimal(0) : e.getNoTaxActualReportAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        //规划金额
        Map<String, BigDecimal> planCaseListAllMap = planCaseListAll.stream()
                .collect(Collectors.groupingBy(x -> x.getCategoryCode() + column + x.getCategoryName(),
                        Collectors.mapping(MarketingPlanCase::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        Set<String> categoryCodesAll = new HashSet<>();
        categoryCodesAll.addAll(categoryMap.keySet());
        categoryCodesAll.addAll(categoryCaseMap.isEmpty() ? new HashSet<>() : categoryCaseMap.keySet());
        Map<String, BigDecimal> categoryMapAll = new HashMap<>();
        for (String key : categoryCodesAll) {
            categoryMapAll.put(key, categoryCaseMap.getOrDefault(key, BigDecimal.ZERO).add(categoryMap.getOrDefault(key, BigDecimal.ZERO)));
        }

        Set<String> categoryCodes = withHoldingList.stream().map(WithHoldingVo::getCostTypeCategoryCode).collect(Collectors.toSet());
        Set<String> categoryCaseCodes = planCaseList.stream().map(MarketingPlanCase::getCategoryCode).collect(Collectors.toSet());
        categoryCodes.addAll(CollectionUtils.isNotEmpty(categoryCaseCodes) ? categoryCaseCodes : new HashSet<>());
        List<CostBudgetVo> costBudgetVos = costBudgetVoService.findListByOrgCodeAndCategoryCodesAndYears(Lists.newArrayList(orgCode), new ArrayList<>(categoryCodes), years);
        Map<String, BigDecimal> costBudgetMap = costBudgetVos.stream().collect(Collectors.groupingBy(CostBudgetVo::getBudgetSubjectCode,
                Collectors.mapping(CostBudgetVo::getInitialAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        for (Map.Entry<String, BigDecimal> entry : categoryMapAll.entrySet()) {
            String[] keys = entry.getKey().split(column);
            //排除随单搭赠的
            if (Arrays.asList("随单搭赠-货补", "随单搭赠-尾差").contains(keys[1])) {
                continue;
            }
            RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
            data.setProjectCode(keys[0]);
            data.setProjectName(keys[1]);
            data.setEstimateAmount(entry.getValue());
            data.setBudgetAmount(costBudgetMap.getOrDefault(data.getProjectCode(), BigDecimal.ZERO));
            data.setPlanAmount((planCaseListAllMap.getOrDefault(entry.getKey(), BigDecimal.ZERO)).divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            categoryCostCodeList.add(keys[0]);
            categoryDataList.add(data);
        }
        dataList.addAll(categoryDataList);
        return this;
    }

    /**
     * 营销费用小计
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder marketingCostTotal() {
        marketingCostData.setProjectCode(RegionCollectProjectEnum.marketing_cost_total.getCode());
        marketingCostData.setProjectName(RegionCollectProjectEnum.marketing_cost_total.getName());
        marketingCostData.setSort(RegionCollectProjectEnum.marketing_cost_total.getSort());

        marketingCostData.setDifferenceRatio(BigDecimal.ZERO);
        marketingCostData.setDifferenceAmount(BigDecimal.ZERO);
        marketingCostData.setEstimateAmount(BigDecimal.ZERO);
        marketingCostData.setEstimateRatio(BigDecimal.ZERO);
        marketingCostData.setBudgetRatio(BigDecimal.ZERO);
        marketingCostData.setBudgetAmount(BigDecimal.ZERO);
        marketingCostData.setPlanRatio(BigDecimal.ZERO);
        marketingCostData.setPlanAmount(BigDecimal.ZERO);

        BigDecimal estimateAmount = categoryDataList.stream().map(RegionCollectMarketingEstimationVo::getEstimateAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal budgetAmount = categoryDataList.stream().map(RegionCollectMarketingEstimationVo::getBudgetAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal planAmount = categoryDataList.stream().map(RegionCollectMarketingEstimationVo::getPlanAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        marketingCostData.setEstimateAmount(estimateAmount.add(giftCostData.getEstimateAmount()));
        marketingCostData.setBudgetAmount(budgetAmount.add(giftCostData.getBudgetAmount()));
        marketingCostData.setPlanAmount(planAmount.add(giftCostData.getPlanAmount()));
        dataList.add(marketingCostData);
        return this;
    }

    /**
     * 边际贡献
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder marginalContribution() {
        RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
        data.setProjectCode(RegionCollectProjectEnum.marginal_contribution.getCode());
        data.setProjectName(RegionCollectProjectEnum.marginal_contribution.getName());
        data.setSort(RegionCollectProjectEnum.marginal_contribution.getSort());

        data.setDifferenceRatio(BigDecimal.ZERO);
        data.setDifferenceAmount(BigDecimal.ZERO);
        data.setEstimateAmount(BigDecimal.ZERO);
        data.setEstimateRatio(BigDecimal.ZERO);
        data.setBudgetRatio(BigDecimal.ZERO);
        data.setBudgetAmount(BigDecimal.ZERO);
        data.setPlanRatio(BigDecimal.ZERO);
        data.setPlanAmount(BigDecimal.ZERO);

        //毛利
        BigDecimal grossProfit = estimationRevenueData.getEstimateAmount().subtract(productionCostData.getEstimateAmount());
        //毛利-运输费用-营销费用小计
        BigDecimal estimateAmount = grossProfit.subtract(productTransportCostData.getEstimateAmount())
                .subtract(marketingCostData.getEstimateAmount());
        data.setEstimateAmount(estimateAmount);

        //毛利
        BigDecimal budgetGrossProfit = estimationRevenueData.getBudgetAmount().subtract(productionCostData.getBudgetAmount());
        //毛利-运输费用-营销费用小计
        BigDecimal budgetEstimateAmount = budgetGrossProfit.subtract(productTransportCostData.getBudgetAmount()).subtract(peripheryTransportData.getBudgetAmount())
                .subtract(marketingCostData.getBudgetAmount());
        data.setBudgetAmount(budgetEstimateAmount);

        //毛利
        BigDecimal planGrossProfit = estimationRevenueData.getPlanAmount().subtract(productionCostData.getPlanAmount());
        //毛利-运输费用-营销费用小计
        BigDecimal planAmount = planGrossProfit.subtract(productTransportCostData.getPlanAmount()).subtract(peripheryTransportData.getPlanAmount())
                .subtract(marketingCostData.getPlanAmount());
        data.setPlanAmount(planAmount);

        dataList.add(data);
        return this;
    }

    /**
     * 人工、差旅等及其他分摊费用
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder artificialTravelOnBusiness() {
        artificialTravelOnBusinessData.setProjectCode(RegionCollectProjectEnum.artificial_travel_on_business.getCode());
        artificialTravelOnBusinessData.setProjectName(RegionCollectProjectEnum.artificial_travel_on_business.getName());
        artificialTravelOnBusinessData.setSort(RegionCollectProjectEnum.artificial_travel_on_business.getSort());

        artificialTravelOnBusinessData.setDifferenceRatio(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setDifferenceAmount(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setEstimateAmount(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setEstimateFixedPoint(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setEstimateRatio(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setBudgetRatio(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setBudgetFixedPoint(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setBudgetAmount(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setPlanRatio(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setPlanAmount(BigDecimal.ZERO);

        //预计
        BigDecimal ratio = publicShareRatioService.findRatioByCondition(Lists.newArrayList(orgCode), years);
        BigDecimal estimateAmount = estimationRevenueData.getEstimateAmount().multiply(ratio);

        artificialTravelOnBusinessData.setEstimateAmount(estimateAmount);
        artificialTravelOnBusinessData.setEstimateFixedPoint(ratio);

        //预算
        BigDecimal budgetRatio = publicShareRatioService.findRatioByCondition(Lists.newArrayList(orgCode), years);
        BigDecimal budgetEstimateAmount = estimationRevenueData.getBudgetAmount().multiply(budgetRatio);

        artificialTravelOnBusinessData.setBudgetAmount(budgetEstimateAmount);
        artificialTravelOnBusinessData.setBudgetFixedPoint(budgetRatio);

        //规划
        BigDecimal planRatio = publicShareRatioService.findRatioByCondition(Lists.newArrayList(orgCode), years);
        BigDecimal planAmount = estimationRevenueData.getPlanAmount().multiply(planRatio);

        artificialTravelOnBusinessData.setPlanAmount(planAmount);

        dataList.add(artificialTravelOnBusinessData);
        return this;
    }

    /**
     * 费用合计
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder costTotal() {
        costData.setProjectCode(RegionCollectProjectEnum.cost_total.getCode());
        costData.setProjectName(RegionCollectProjectEnum.cost_total.getName());
        costData.setSort(RegionCollectProjectEnum.cost_total.getSort());

        costData.setDifferenceRatio(BigDecimal.ZERO);
        costData.setDifferenceAmount(BigDecimal.ZERO);
        costData.setEstimateAmount(BigDecimal.ZERO);
        costData.setEstimateRatio(BigDecimal.ZERO);
        costData.setBudgetRatio(BigDecimal.ZERO);
        costData.setBudgetAmount(BigDecimal.ZERO);
        costData.setPlanRatio(BigDecimal.ZERO);
        costData.setPlanAmount(BigDecimal.ZERO);

        BigDecimal estimateAmount = marketingCostData.getEstimateAmount().add(artificialTravelOnBusinessData.getEstimateAmount());
        costData.setEstimateAmount(estimateAmount);

        //预算
        BigDecimal budgetEstimateAmount = marketingCostData.getBudgetAmount().add(artificialTravelOnBusinessData.getBudgetAmount());
        costData.setBudgetAmount(budgetEstimateAmount);

        //规划
        BigDecimal planAmount = marketingCostData.getPlanAmount().add(artificialTravelOnBusinessData.getPlanAmount());
        costData.setPlanAmount(planAmount);

        dataList.add(costData);
        return this;
    }

    /**
     * 利润
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder profit() {
        profit.setProjectCode(RegionCollectProjectEnum.profit.getCode());
        profit.setProjectName(RegionCollectProjectEnum.profit.getName());
        profit.setSort(RegionCollectProjectEnum.profit.getSort());

        profit.setDifferenceRatio(BigDecimal.ZERO);
        profit.setDifferenceAmount(BigDecimal.ZERO);
        profit.setEstimateAmount(BigDecimal.ZERO);
        profit.setEstimateRatio(BigDecimal.ZERO);
        profit.setBudgetRatio(BigDecimal.ZERO);
        profit.setBudgetAmount(BigDecimal.ZERO);
        profit.setPlanRatio(BigDecimal.ZERO);
        profit.setPlanAmount(BigDecimal.ZERO);

        //毛利
        BigDecimal grossProfit = estimationRevenueData.getEstimateAmount().subtract(productionCostData.getEstimateAmount());

        BigDecimal estimateAmount = grossProfit.subtract(productTransportCostData.getEstimateAmount())
                .subtract(peripheryTransportData.getEstimateAmount())
                .subtract(costData.getEstimateAmount());
        profit.setEstimateAmount(estimateAmount);

        //毛利
        BigDecimal budgetGrossProfit = estimationRevenueData.getBudgetAmount().subtract(productionCostData.getBudgetAmount());

        BigDecimal budgetEstimateAmount = budgetGrossProfit.subtract(productTransportCostData.getBudgetAmount())
                .subtract(peripheryTransportData.getBudgetAmount())
                .subtract(costData.getBudgetAmount());
        profit.setBudgetAmount(budgetEstimateAmount);

        //毛利
        BigDecimal planGrossProfit = estimationRevenueData.getPlanAmount().subtract(productionCostData.getPlanAmount());

        BigDecimal planAmount = planGrossProfit.subtract(productTransportCostData.getPlanAmount())
                .subtract(peripheryTransportData.getPlanAmount())
                .subtract(costData.getPlanAmount());
        profit.setPlanAmount(planAmount);

        dataList.add(profit);
        return this;
    }

    /**
     * 费用转移
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder costShift() {
        costShiftData.setProjectCode(RegionCollectProjectEnum.cost_shift.getCode());
        costShiftData.setProjectName(RegionCollectProjectEnum.cost_shift.getName());
        costShiftData.setSort(RegionCollectProjectEnum.cost_shift.getSort());

        costShiftData.setDifferenceRatio(BigDecimal.ZERO);
        costShiftData.setDifferenceAmount(BigDecimal.ZERO);
        costShiftData.setEstimateAmount(BigDecimal.ZERO);
        costShiftData.setEstimateRatio(BigDecimal.ZERO);
        costShiftData.setBudgetRatio(BigDecimal.ZERO);
        costShiftData.setBudgetAmount(BigDecimal.ZERO);
        costShiftData.setPlanRatio(BigDecimal.ZERO);
        costShiftData.setPlanAmount(BigDecimal.ZERO);
        List<WithHoldingVo> withHoldingDiffList = withHoldingList.stream().filter(e -> !e.getBelongDepartmentCode().equals(e.getBearDepartmentCode())
                && !orgCodeList.contains(e.getBearDepartmentCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(withHoldingDiffList)) {
            //获取计提前兑付金额
            List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesApprovedBefore(orgCodeList, years);
            BigDecimal feeCashAmount = CollectionUtils.isNotEmpty(feeCashDetailVos) ? feeCashDetailVos.stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO;
            //费用转移 统计
            BigDecimal actualReportAmount = withHoldingDiffList.stream().map(e -> e.getActualReportAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            costShiftData.setEstimateAmount(actualReportAmount.add(feeCashAmount));
        }

        dataList.add(costShiftData);
        return this;
    }

    /**
     * 考核利润
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder accessProfits() {
        RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
        data.setProjectCode(RegionCollectProjectEnum.access_profits.getCode());
        data.setProjectName(RegionCollectProjectEnum.access_profits.getName());
        data.setSort(RegionCollectProjectEnum.access_profits.getSort());

        data.setDifferenceRatio(BigDecimal.ZERO);
        data.setDifferenceAmount(BigDecimal.ZERO);
        data.setEstimateAmount(costShiftData.getEstimateAmount().add(profit.getEstimateAmount()));
        data.setEstimateRatio(BigDecimal.ZERO);
        data.setBudgetRatio(BigDecimal.ZERO);
        data.setBudgetAmount(BigDecimal.ZERO);
        data.setPlanRatio(BigDecimal.ZERO);
        data.setPlanAmount(BigDecimal.ZERO);

        dataList.add(data);
        return this;
    }
    
    @Override
    public List<RegionCollectMarketingEstimationVo> getList() {
        for (RegionCollectMarketingEstimationVo vo : dataList) {

            if (vo.getProjectCode().equals(RegionCollectProjectEnum.gross_profit_margin.getCode())) {
                continue;
            }

            BigDecimal differenceAmount = vo.getBudgetAmount().subtract(vo.getEstimateAmount());
            BigDecimal differenceRatio = BigDecimal.ZERO;
            if (vo.getBudgetAmount().compareTo(BigDecimal.ZERO) == 1) {
                differenceRatio = differenceAmount.divide(vo.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
            }
            vo.setDifferenceAmount(differenceAmount);
            vo.setDifferenceRatio(differenceRatio);

            if (ObjectUtils.isNotEmpty(vo.getEstimateAmount())) {
                vo.setEstimateAmount(vo.getEstimateAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetAmount())) {
                vo.setBudgetAmount(vo.getBudgetAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getDifferenceAmount())) {
                vo.setDifferenceAmount(vo.getDifferenceAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }

            if (vo.getProjectCode().equals(RegionCollectProjectEnum.actual_revenue.getCode())) {
                continue;
            }

            BigDecimal estimateRatio = BigDecimal.ZERO;
            BigDecimal budgetRatio = BigDecimal.ZERO;
            BigDecimal planRatio = BigDecimal.ZERO;
            if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                    && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                estimateRatio = vo.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
            }
            if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                    && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
                budgetRatio = vo.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
            }
            if (Objects.nonNull(estimationRevenueData.getPlanAmount())
                    && estimationRevenueData.getPlanAmount().compareTo(BigDecimal.ZERO) != 0) {
                planRatio = vo.getPlanAmount().divide(estimationRevenueData.getPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
            }
            vo.setEstimateRatio(estimateRatio);
            vo.setBudgetRatio(budgetRatio);
            vo.setPlanRatio(planRatio);



            if (ObjectUtils.isNotEmpty(vo.getEstimateFixedPoint())) {
                vo.setEstimateFixedPointStr(vo.getEstimateFixedPoint().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetFixedPoint())) {
                vo.setBudgetFixedPointStr(vo.getBudgetFixedPoint().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getEstimateRatio())) {
                vo.setEstimateRatioStr(vo.getEstimateRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetRatio())) {
                vo.setBudgetRatioStr(vo.getBudgetRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanRatio())) {
                vo.setPlanRatioStr(vo.getPlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDifferenceRatio())) {
                vo.setDifferenceRatioStr(vo.getDifferenceRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
        }
        return dataList;
    }
}
