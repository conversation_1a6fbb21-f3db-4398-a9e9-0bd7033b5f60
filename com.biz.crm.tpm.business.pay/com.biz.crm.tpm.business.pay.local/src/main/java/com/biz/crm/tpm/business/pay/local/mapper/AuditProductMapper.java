package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.AuditProduct;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditProductDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditProductVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 费用明细商品;(tpm_audit_product)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@Mapper
public interface AuditProductMapper extends BaseMapper<AuditProduct>{
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<AuditProductVo> findByConditions(@Param("page") Page<AuditProductVo> page , @Param("dto") AuditProductDto dto);
}