package com.biz.crm.tpm.business.pay.local.entity;

import com.biz.crm.business.common.local.entity.FileEntity;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：费用核销附件;
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "AuditFiles",description = "费用核销附件")
@TableName("tpm_audit_files")
@Getter
@Setter
@Entity(name = "tpm_audit_files")
@org.hibernate.annotations.Table(appliesTo = "tpm_audit_files", comment = "费用核销附件")
@Table(name = "tpm_audit_files")
public class AuditFiles  extends FileEntity {
  
  /** 费用核销编号 */
  @ApiModelProperty(name = "费用核销编号",notes = "方案编号")
  @Column(name = "audit_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '费用核销编号 '")
  private String auditCode;

  /** 活动细类编码 */
  @ApiModelProperty(name = "活动细类编码",notes = "活动细类编码")
  @Column(name = "cost_type_detail_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动细类编码 '")
  private String costTypeDetailCode;

  /** 活动细类名称 */
  @ApiModelProperty(name = "活动细类名称",notes = "活动细类名称")
  @Column(name = "cost_type_detail_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '活动细类名称 '")
  private String costTypeDetailName;

  /** 要求核销资料编号 */
  @ApiModelProperty(name = "要求核销资料编号",notes = "要求核销资料编号")
  @Column(name = "request_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '要求核销资料编号 '")
  private String requestCode;
}