package com.biz.crm.tpm.business.pay.local.service.internal.process;

import cn.hutool.core.date.DateUtil;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.material.sdk.service.JdMaterialPurchaseVoService;
import com.biz.crm.mdm.business.material.sdk.vo.JdMaterialPurchaseVo;
import com.biz.crm.tpm.business.pay.local.service.internal.FeeCashMaterialPageCacheHelper;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashMaterialDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashMaterialService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashMaterialImportVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FeeCashMaterialImportProcess implements ImportProcess<FeeCashMaterialImportVo> {

    @Autowired(required = false)
    private FeeCashMaterialService feeCashMaterialService;
    @Autowired(required = false)
    private FeeCashMaterialPageCacheHelper feeCashMaterialPageCacheHelper;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private JdMaterialPurchaseVoService jdMaterialPurchaseVoService;

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, FeeCashMaterialImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, FeeCashMaterialImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");

        for (Map.Entry<Integer, FeeCashMaterialImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            FeeCashMaterialImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getMaterialCode()), "物料编码不能为空！");

//            this.validateIsTrue(StringUtils.isNotEmpty(vo.getUrl()), "商品参考链接，不能为空！");
//            this.validateIsTrue(StringUtils.isNotEmpty(vo.getPriceStr()), "单价，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getQuantityStr()), "需求数量，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getRequiredDeliveryTime()), "需求到货时间，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getContactPerson()), "联系人，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getContactPhone()), "联系电话，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getAddress()), "邮寄地址，不能为空！");

//            try {
//                new BigDecimal(vo.getPriceStr().trim());
//            } catch (Exception e) {
//                this.validateIsTrue(false, "单价类型转换失败！");
//            }
            try {
                new BigDecimal(vo.getQuantityStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "需求数量类型转换失败！");
            }
            try {
                DateUtil.parse(vo.getRequiredDeliveryTime(), "yyyy-MM-dd");
            } catch (Exception e) {
                this.validateIsTrue(false, "需求到货时间类型转换失败！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, FeeCashMaterialImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        // 收集所有物料编码，用于批量查询
        Set<String> materialCodes = new HashSet<>();
        for (FeeCashMaterialImportVo vo : data.values()) {
            if (StringUtils.isNotEmpty(vo.getMaterialCode())) {
                materialCodes.add(vo.getMaterialCode());
            }
        }

        // 批量查询物料信息
        Map<String, JdMaterialPurchaseVo> materialCodeMap = new HashMap<>();
        if (!materialCodes.isEmpty()) {
            // 使用JdMaterialPurchaseVoService查询物料信息
            List<JdMaterialPurchaseVo> materialList = jdMaterialPurchaseVoService.findByMaterialCodes(Lists.newArrayList(materialCodes));
            if (CollectionUtils.isNotEmpty(materialList)) {
                for (JdMaterialPurchaseVo material : materialList) {
                    materialCodeMap.put(material.getMaterialCode(), material);
                }
            }
        }

        String cacheKey = (String) params.get("cacheKey");

        List<FeeCashMaterialDto> updateCacheList = new ArrayList<>();
        List<FeeCashMaterialDto> cacheList = feeCashMaterialService.findCacheList(cacheKey);
        if (CollectionUtils.isNotEmpty(cacheList)){
            updateCacheList.addAll(cacheList);
        }

        for (Map.Entry<Integer, FeeCashMaterialImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            FeeCashMaterialImportVo vo = row.getValue();
            JdMaterialPurchaseVo jdMaterialPurchaseVo = materialCodeMap.get(vo.getMaterialCode());
            if (jdMaterialPurchaseVo != null) {
                // 如果查询到物料名称，则自动填充
                vo.setMaterialName(jdMaterialPurchaseVo.getMaterialName());
                vo.setPrice(jdMaterialPurchaseVo.getPurchasePrice());
                this.validateIsTrue(Objects.nonNull(jdMaterialPurchaseVo.getPurchasePrice()), "物料编码 [" + vo.getMaterialCode() + "]未维护价格");
                this.validateIsTrue(Objects.nonNull(jdMaterialPurchaseVo.getMaterialName()), "物料编码 [" + vo.getMaterialCode() + "]未维护名称信息");
            } else {
                this.validateIsTrue(false, "物料编码 [" + vo.getMaterialCode() + "] 不存在或未启用！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        for (Map.Entry<Integer, FeeCashMaterialImportVo> row : data.entrySet()) {
            FeeCashMaterialImportVo vo = row.getValue();
            vo.setQuantity(new BigDecimal(vo.getQuantityStr().trim()));
            vo.setAmount((vo.getPrice().multiply(vo.getQuantity())).setScale(2, BigDecimal.ROUND_HALF_UP));
            FeeCashMaterialDto dto = JsonUtils.convert(vo,FeeCashMaterialDto.class);
            updateCacheList.add(dto);
        }
        feeCashMaterialPageCacheHelper.importNewItem(cacheKey, updateCacheList);

        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<FeeCashMaterialImportVo> findCrmExcelVoClass() {
        return FeeCashMaterialImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "FEE_CASH_MATERIAL_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "采购需求导入模板";
    }
}
