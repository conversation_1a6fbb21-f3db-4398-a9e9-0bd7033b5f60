package com.biz.crm.tpm.business.pay.local.service.internal;

import cn.hutool.core.date.DateUtil;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingWriteOffRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.WithHoldingWriteOffConstant;
import com.biz.crm.tpm.business.pay.sdk.enums.WithHoldingTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffSendHecService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffService;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingWriteOffVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WithHoldingWriteOffServiceImpl implements WithHoldingWriteOffService {

    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;
    @Autowired(required = false)
    private WithHoldingWriteOffRepository withHoldingWriteOffRepository;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private RedisLockService redisLockService;
    @Autowired(required = false)
    private WithHoldingWriteOffSendHecService withHoldingWriteOffSendHecService;


    /**
     * 手动冲销
     *
     * @param codeList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleManual(List<String> codeList) {
        Validate.notEmpty(codeList, "编码不能为空");
        List<WithHoldingVo> withHoldingVoList = withHoldingRepository.findByCodes(codeList);
        Validate.isTrue(new HashSet<>(codeList).size() == withHoldingVoList.size(), "未找到对应的费用计提");
        withHoldingVoList.forEach(e -> Validate.isTrue(WithHoldingTypeEnum.HANDLE.getDictCode().equals(e.getWithHoldingType()), "【%s】不是手动计提的数据", e.getWithHoldingCode()));
        List<WithHoldingWriteOffVo> uniqueList = withHoldingWriteOffRepository.findByCodes(codeList);
        Validate.isTrue(CollectionUtils.isEmpty(uniqueList), "已进行过冲销，不可重复冲销");
        this.handleManualByEntities(withHoldingVoList);
    }

    /**
     * 费控凭证回传
     *
     * @param dtoList
     */
    @Override
    public void hecVoucherCallback(List<HecCallbackDto> dtoList) {
        Validate.notEmpty(dtoList, "回传参数不能为空");
        dtoList.forEach(dto -> {
            Validate.notBlank(dto.getBusinessCode(), "单据编码不能为空");
            Validate.notBlank(dto.getOrderCode(), "凭证编码不能为空");
        });

        Map<String, HecCallbackDto> dtoMap = dtoList.stream()
                .collect(Collectors.toMap(HecCallbackDto::getBusinessCode, v -> v, (n, o) -> n));
        withHoldingWriteOffRepository.hecVoucherCallback(new ArrayList<>(dtoMap.values()));
    }

    @Override
    public void handleManualByEntities(List<WithHoldingVo> withHoldingVoList) {
        if (CollectionUtils.isEmpty(withHoldingVoList)) {
            return;
        }
        withHoldingVoList.forEach(item -> {
            item.setAuditCreateAccount(item.getCreateAccount());
        });
        Collection<WithHoldingWriteOff> entities = nebulaToolkitService.copyCollectionByWhiteList(withHoldingVoList, WithHoldingVo.class, WithHoldingWriteOff.class, LinkedHashSet.class, ArrayList.class);
        List<String> lockKey = entities.stream().map(WithHoldingWriteOff::getWithHoldingCode).collect(Collectors.toList());
        Validate.isTrue(this.redisLockService.batchLock(WithHoldingWriteOffConstant.LOCK_PREFIX, lockKey,
                TimeUnit.MINUTES,
                30), "预提数据正在被操作，请稍后再试！");
        try {
            List<String> codes = generateCodeService.generateCode(WithHoldingWriteOffConstant.PREFIX_CODE, entities.size());
            int index = 0;
            for (WithHoldingWriteOff e : entities) {
                e.setWriteOffCode(codes.get(index++));
                e.setId(null);
                e.setCreateTime(null);
                //手工冲销默认使用接口用户
                e.setCreateAccount("tpm");
                e.setCreateName("TPM系统接口用户");
                e.setPositionCode("ZW202409130013");
                e.setModifyTime(null);
                e.setModifyAccount(null);
                e.setModifyName(null);
                e.setVoucherCode(null);
                e.setPushStatus(null);
                e.setFailMsg(null);
                e.setExternalCode(null);
                e.setWriteOffType(WriteOffTypeEnum.HANDLE.getDictCode());
                e.setWriteOffTime(new Date());
                e.setWriteOffYears(DateUtil.format(new Date(), "yyyy-MM"));
                e.setWriteOffAmount(e.getWithHoldingAmount());
                e.setBeThisFee(BooleanEnum.FALSE.getCapital());
                e.setManageReportWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
            }
            withHoldingWriteOffRepository.saveBatch(entities);
            withHoldingWriteOffSendHecService.push(entities.stream().map(e -> e.getId()).collect(Collectors.toList()));
        } finally {
            this.redisLockService.batchUnLock(WithHoldingWriteOffConstant.LOCK_PREFIX, lockKey);
        }

    }


    @Override
    public List<WithHoldingWriteOffVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return new ArrayList<>();
        }
        return withHoldingWriteOffRepository.findListBySchemeDetailCodes(schemeDetailCodes);
    }

    @Override
    public List<WithHoldingWriteOffVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes, List<String> writeOffTypes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes) || CollectionUtils.isEmpty(writeOffTypes)) {
            return new ArrayList<>();
        }
        return withHoldingWriteOffRepository.findBySchemeDetailCodesAndWriteOffTypes(schemeDetailCodes, writeOffTypes);
    }


    @Override
    public List<WithHoldingWriteOffVo> findListBySourceCodes(List<String> sourceCodes) {
        return withHoldingWriteOffRepository.findListBySourceCodes(sourceCodes);
    }
}
