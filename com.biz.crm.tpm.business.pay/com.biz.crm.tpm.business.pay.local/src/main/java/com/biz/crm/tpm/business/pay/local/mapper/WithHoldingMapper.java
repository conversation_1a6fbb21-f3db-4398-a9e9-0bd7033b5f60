package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectSchemeVo;
import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.WithHolding;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingBalanceDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingDto;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingBalanceVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 费用预提(WithHolding)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-06-25 14:20:21
 */
public interface WithHoldingMapper extends BaseMapper<WithHolding> {

    /**
     * 分页查询所有数据
     *
     * @param page        分页对象
     * @param withHolding 查询实体
     * @return 所有数据
     */
    Page<WithHoldingVo> findByConditions(@Param("page") Page<WithHoldingVo> page,
                                         @Param("dto") WithHoldingDto withHolding);

    /**
     * 查询未汇总的计提明细
     *
     * @param yearMonthLy
     * @param orgCodes
     * @param tenantCode
     * @return
     */
    List<WithHolding> findByYearMonthBelongDepartmentCode(@Param("yearMonthLy") String yearMonthLy,
                                                          @Param("orgCodes") List<String> orgCodes,
                                                          @Param("tenantCode") String tenantCode);

    /**
     * 更新凭证信息
     *
     * @param dtoList
     */
    void hecVoucherCallback(@Param("dtoList") List<HecCallbackDto> dtoList);

    /**
     * 查询推送费用数据
     *
     * @param idList
     * @return
     */
    List<WithHoldingVo> findSendDataByCollectIds(@Param("idList") List<String> idList);

    List<WithHoldingVo> findBySchemeDetailCodesPass(@Param("codes") List<String> codes);

    /**
     * 查询推送费用数据
     *
     * @param idList
     * @return
     */
    List<WithHoldingVo> findSendDataByIds(@Param("idList") List<String> idList);

    List<RegionCollectSchemeVo> findSchemeCreateInfoList(@Param("schemeCodes") List<String> schemeCodes);

    void updateCreateInfo(@Param("entities") List<WithHolding> entities);

    List<WithHoldingVo> findTotalByConditions(@Param("dto") WithHoldingDto dto);

    List<WithHoldingIncomeVo> findActualReportAmountByYears(@Param("yearMonths") List<String> years);

    BigDecimal findAmountByOrgCodesAndYears(@Param("orgCodes") List<String> orgCodes, @Param("years") String years);

    List<WithHoldingVo> findWithholdingListByYearsAndOrgCodes(@Param("dto") WithholdingIncomeQueryDto dto);

    /**
     * 分页查询计提余额数据
     * 实现WithHoldingBalanceDataViewRegister的SQL逻辑
     *
     * @param page 分页对象
     * @param dto 查询条件
     * @return 计提余额分页数据
     */
    Page<WithHoldingBalanceVo> findWithHoldingBalanceByConditions(@Param("page") Page<WithHoldingBalanceVo> page,
                                                                  @Param("dto") WithHoldingBalanceDto dto);
}

