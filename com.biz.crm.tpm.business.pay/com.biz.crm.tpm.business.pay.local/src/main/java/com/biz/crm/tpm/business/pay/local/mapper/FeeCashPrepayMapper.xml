<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.FeeCashPrepayMapper">

    <select id="findBySchemeDetailCode" resultType="com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPrepayVo">
        select p.* from tpm_fee_cash_prepay p
        inner join tpm_fee_cash c on c.cash_code = p.cash_code
        where p.scheme_detail_code = #{schemeDetailCode}
        and c.status = '3'
        and c.cash_type = 'fee'
    </select>

    <select id="getAvailableReversedAmount" resultType="decimal">
                SELECT sum(
                case when t.prepay_type != 'activity' THEN
                ifnull(b.this_cash_amount,0)-ifnull(d.this_reversed_amount,0)
                else
                ifnull(c.this_pay_amount,0)-ifnull(d.this_reversed_amount,0)
                end )
                FROM
                	tpm_activity_prepay_detail_record t
                	LEFT JOIN tpm_activity_prepay a ON t.prepay_code = a.prepay_code
                		AND a.status = '3'
                	LEFT JOIN tpm_activity_prepay_detail c ON t.prepay_detail_code = c.prepay_detail_code
                	LEFT JOIN (
                		SELECT
                			b.cash_detail_code,
                			b.this_cash_amount
                		FROM
                			tpm_fee_cash a
                			LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
                		WHERE
                			a.status = '3'
                			AND cash_type in('wire', 'account')) b ON t.prepay_detail_code = b.cash_detail_code
                	LEFT JOIN (
                		SELECT
                			a.prepay_detail_code, sum(IFNULL(a.this_reversed_amount, 0)) this_reversed_amount
                		FROM
                			tpm_fee_cash_prepay a left join tpm_fee_cash b on a.cash_code = b.cash_code
                       where b.del_flag = '009'
                		GROUP BY
                			a.prepay_detail_code) d ON t.prepay_detail_code = d.prepay_detail_code
                    WHERE t.del_flag = '009'  and t.scheme_detail_code=#{detailCode}
    </select>
</mapper>

