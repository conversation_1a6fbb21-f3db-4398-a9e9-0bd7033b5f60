package com.biz.crm.tpm.business.pay.local.helper;

import cn.hutool.core.date.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.dms.business.order.common.sdk.enums.ItemTypeEnum;
import com.biz.crm.dms.business.order.common.sdk.enums.OrderTypeEnum;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialCostVoService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.regioncollect.service.strategy.ItemEstimationBuilder;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectItemEstimationVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.enums.SecondCategoryEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.FormulaGetValueComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 品项测算
 * <AUTHOR>
 * @Date 2024/6/25 17:12
 */
@Slf4j
public class ItemEstimationAbstractWithHoldingBuilder extends ItemEstimationBuilder<List<String>, String> {

    private static final String column = ":";

    private CostBudgetIncomeService costBudgetIncomeService;
    private CostBudgetVoService costBudgetVoService;
    private CustomerVoService customerVoService;
    private MaterialCostVoService materialCostVoService;
    private CostTypeCategoryVoService costTypeCategoryVoService;
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;
    private ProductVoService productVoService;
    private MaterialVoService materialVoService;
    private DictDataVoService dictDataVoService;

    private OrgVoService orgVoService;

    private MarketingPlanCaseCheckHelper checkHelper;

    private PublicShareRatioService publicShareRatioService;

    private FormulaGetValueComponent formulaGetValueComponent;

    private ProductPhaseVoService productPhaseVoService;

    private String year = null;

    private Map<String, BigDecimal> incomeDmsMap = new HashMap<>();

    private Map<String, BigDecimal> costDmsMap = new HashMap<>();

    private Map<String, BigDecimal> giftDmsMap = new HashMap<>();

    private Map<String, BigDecimal> compensateGoodsCostMap = new HashMap<>();

    private Map<String, BigDecimal> productTransferMap = new HashMap<>();

    private Map<String, BigDecimal> peripheryTransferMap = new HashMap<>();

    private Map<String, List<String>> costTypeCategoryMap;

    Map<String, List<String>> productMap;

    private Map<String, String> dictMap;

    private List<MarketingPlanCase> caseList = Lists.newArrayList();

    private List<RegionCollectGainsAndLossesVo> dataList = Lists.newArrayList();

    private List<RegionCollectItemEstimationVo> itemList = Lists.newArrayList();

    private List<String> regionOrgCodes;

    private List<String> customerCodes;

    private Set<String> itemCodes = new HashSet<>();

    private List<WithHoldingVo> withHoldingList = Lists.newArrayList();

    private Map<String, ProductPhaseVo> productPhaseMap = Maps.newHashMap();

    private Map<String, List<String>> productPhaseListMap = Maps.newHashMap();

    private List<DmsWarehouseOrderDetailVo> orderDetailList = Lists.newArrayList();

    public static ItemEstimationAbstractWithHoldingBuilder builder(ApplicationContext context, List<RegionCollectGainsAndLossesVo> dataList, List<RegionCollectItemEstimationVo> itemList, List<MarketingPlanCase> caseList, List<String> regionOrgCodes, List<String> customerCodes,
                                                                   List<WithHoldingVo> withHoldingList) {
        ItemEstimationAbstractWithHoldingBuilder builder = new ItemEstimationAbstractWithHoldingBuilder();
        builder.costBudgetIncomeService = context.getBean(CostBudgetIncomeService.class);
        builder.costBudgetVoService = context.getBean(CostBudgetVoService.class);
        builder.customerVoService = context.getBean(CustomerVoService.class);
        builder.materialCostVoService = context.getBean(MaterialCostVoService.class);
        builder.costTypeCategoryVoService = context.getBean(CostTypeCategoryVoService.class);
        builder.productVoService = context.getBean(ProductVoService.class);
        builder.dmsWarehouseOrderDetailVoService = context.getBean(DmsWarehouseOrderDetailVoService.class);
        builder.orgVoService = context.getBean(OrgVoService.class);
        builder.dictDataVoService = context.getBean(DictDataVoService.class);
        builder.dataList = dataList;
        builder.itemList = itemList;
        builder.checkHelper = context.getBean(MarketingPlanCaseCheckHelper.class);
        builder.publicShareRatioService = context.getBean(PublicShareRatioService.class);
        builder.formulaGetValueComponent = context.getBean(FormulaGetValueComponent.class);
        builder.materialVoService = context.getBean(MaterialVoService.class);
        builder.caseList = caseList;
        builder.regionOrgCodes = regionOrgCodes;
        builder.withHoldingList = withHoldingList;
        builder.customerCodes = customerCodes;
        builder.productPhaseVoService = context.getBean(ProductPhaseVoService.class);
        return builder;
    }


    @Override
    public ItemEstimationBuilder loadInitData() {
        List<String> productItemTypeList = Arrays.asList(ItemTypeEnum.NORMAL_GOODS.getDictCode(),
                ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
                ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
                ItemTypeEnum.COMPENSATED_GOODS.getDictCode(),
                ItemTypeEnum.TAIL_GOODS.getDictCode());
        List<String> peripheryItemTypeList = Arrays.asList(ItemTypeEnum.MATERIAL_GOODS.getDictCode());
        List<String> giftItemTypeList = Arrays.asList(ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
                ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
                ItemTypeEnum.TAIL_GOODS.getDictCode());

        TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
        dto.setCustomerCodes(customerCodes);
        String startDate = years + "-01";
        String endDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(startDate, "yyyy-MM-dd")), "yyyy-MM-dd");
        dto.setSearchStartTime(startDate);
        dto.setSearchEndTime(endDate);
        dto.setBeWithHolding(BooleanEnum.TRUE.getCapital());
        orderDetailList = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);

        List<RegionCollectGainsAndLossesVo> dataItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderDetailList)) {

            //过滤免费订单
            orderDetailList = orderDetailList.stream().filter(e -> !Lists.newArrayList(OrderTypeEnum.FREE.getDictCode(),
                    OrderTypeEnum.FREE.getReturnCode(),
                    OrderTypeEnum.CONSIGN.getDictCode(),
                    OrderTypeEnum.CONSIGN.getReturnCode()).contains(e.getOrderType())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(orderDetailList)) {
                List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SAP_FACTORY_TYPE);
                dictMap = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
                //查询物料成本
                Set<String> materialCodes = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getGoodsCode())).filter(Objects::nonNull)
                        .map(DmsWarehouseOrderDetailVo::getGoodsCode).collect(Collectors.toSet());
                Set<String> companyCodeSet = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode())).filter(Objects::nonNull)
                        .map(DmsWarehouseOrderDetailVo::getCompanyCode).collect(Collectors.toSet());
                MaterialSearchDto searchDto = new MaterialSearchDto();
                searchDto.setMaterialCodeSet(materialCodes);
                searchDto.setCompanyCodeSet(companyCodeSet);
                List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
                Map<String, Map<String, BigDecimal>> materialMap = materialVoList.stream().collect(Collectors.groupingBy(x -> x.getMaterialCode(), Collectors.toMap(x -> x.getFactoryTypeCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO), (a, b) -> a)));
                Map<String, BigDecimal> materialTaxRateMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getTaxRate()).orElse(BigDecimal.ZERO), (a, b) -> a));

                String year = years.split("-")[0];
                //查询物流成本
                List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialCodes, new ArrayList<>(companyCodeSet), year);
                Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                        x.getCompanyCode(), Function.identity()));

                //去掉物料的
                Set<String> withoutMaterialCodes = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getGoodsCode()) && !ItemTypeEnum.MATERIAL_GOODS.getDictCode().equals(x.getItemType())).filter(Objects::nonNull)
                        .map(DmsWarehouseOrderDetailVo::getGoodsCode).collect(Collectors.toSet());
                productMap = this.productVoService.findDetailsByIdsOrProductCodes(null, new ArrayList<>(withoutMaterialCodes)).stream().filter(e -> StringUtils.isNotBlank(e.getProductPhaseCode())).collect(Collectors.groupingBy(e -> e.getProductPhaseCode(), Collectors.mapping(e -> e.getProductCode(), Collectors.toList())));

                itemCodes.addAll(productMap.keySet());

                this.dataList.forEach(data -> {
                    String customerCode = data.getCustomerCode();
                    productMap.forEach((k, v) -> {
                        RegionCollectGainsAndLossesVo itemData = new RegionCollectGainsAndLossesVo();
                        BeanUtils.copyProperties(data, itemData);
                        itemData.setIndexKey(customerCode + column + k + column + years);
                        itemData.setItemCode(k);
                        dataItemList.add(itemData);
                        //实际收入(发货减退货、发货金额/（1+税率）)
                        BigDecimal delivery = orderDetailList.stream().filter(e -> e.getTotalAmount() != null && customerCode.equals(e.getCustomerCode()) && v.contains(e.getGoodsCode()) && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                                .filter(Objects::nonNull)
                                .map(e -> e.getTotalAmount().divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal stored = orderDetailList.stream().filter(e -> e.getTotalAmount() != null && customerCode.equals(e.getCustomerCode()) && v.contains(e.getGoodsCode()) && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(e.getWarehouseOrderType()))
                                .filter(Objects::nonNull)
                                .map(e -> e.getTotalAmount().divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal subtract = (Optional.ofNullable(delivery).orElse(BigDecimal.ZERO)).subtract((Optional.ofNullable(stored).orElse(BigDecimal.ZERO)));
                        incomeDmsMap.put(customerCode + column + k + column + years, subtract);
                        //生产成本(对应物料主数据的单价*实际发货数量)含搭赠
                        Map<String, BigDecimal> goodsMap = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && customerCode.equals(e.getCustomerCode()) && v.contains(e.getGoodsCode()) && (ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(e.getItemType()) || giftItemTypeList.contains(e.getItemType()))
                                && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType())).filter(Objects::nonNull)
                                .collect(Collectors.groupingBy(e -> e.getGoodsCode() + column + e.getCompanyCode(), Collectors.mapping(DmsWarehouseOrderDetailVo::getRealOutNum, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                        BigDecimal goodsCost = BigDecimal.ZERO;
                        for (Map.Entry<String, BigDecimal> entry : goodsMap.entrySet()) {
                            String[] split = entry.getKey().split(column);
                            Map<String, BigDecimal> factoryMap = materialMap.getOrDefault(split[0], new HashMap<>());
                            BigDecimal price = factoryMap.getOrDefault(dictMap.get(split[1]), BigDecimal.ZERO);
                            goodsCost = goodsCost.add(entry.getValue().multiply(price));
                        }
                        costDmsMap.put(customerCode + column + k + column + years, goodsCost);
                        //搭赠费用(销售价*实际发货数量)
                        BigDecimal giftCost = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && customerCode.equals(e.getCustomerCode()) && v.contains(e.getGoodsCode()) && giftItemTypeList.contains(e.getItemType())
                                && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType())).filter(Objects::nonNull)
                                .map(e -> e.getPresetUnitPrice().multiply(e.getRealOutNum()).divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        giftDmsMap.put(customerCode + column + k + column + years, giftCost);

                        BigDecimal compensateGoodsCost = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && customerCode.equals(e.getCustomerCode()) && v.contains(e.getGoodsCode()) && ItemTypeEnum.COMPENSATED_GOODS.getDictCode().equals(e.getItemType())
                                        && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                                .map(e -> e.getPresetUnitPrice().multiply(e.getRealOutNum()).divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        compensateGoodsCostMap.put(customerCode + column + k + column + years, compensateGoodsCost);


                        //运输费用(产品运输费用+周边运输费用)
                        BigDecimal productExpressFee = orderDetailList.stream().filter(e -> e.getExpressFee() != null && customerCode.equals(e.getCustomerCode()) && v.contains(e.getGoodsCode()) &&
                                productItemTypeList.contains(e.getItemType())
                                && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                                .map(e -> {
                                    BigDecimal price = materialCostMap.getOrDefault(e.getMaterialCode() + column + e.getCompanyCode(), new MaterialCostVo()).getLargeLogisticsPrice();
                                    return (Optional.ofNullable(price).orElse(BigDecimal.ZERO).multiply(e.getRealOutNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                                })
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal peripheryExpressFee = orderDetailList.stream().filter(e -> e.getExpressFee() != null && customerCode.equals(e.getCustomerCode()) && v.contains(e.getGoodsCode()) &&
                                peripheryItemTypeList.contains(e.getItemType())
                                && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                                .map(e -> {
                                    BigDecimal price = materialCostMap.getOrDefault(e.getMaterialCode() + column + e.getCompanyCode(), new MaterialCostVo()).getLargeLogisticsPrice();
                                    return (Optional.ofNullable(price).orElse(BigDecimal.ZERO).multiply(e.getRealOutNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                                })
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        productTransferMap.put(customerCode + column + k + column + years, productExpressFee);
                        peripheryTransferMap.put(customerCode + column + k + column + years, peripheryExpressFee);
                    });
                });
            }
        }

        Map<String, BigDecimal> withHoldingMap = withHoldingList.stream().collect(Collectors.groupingBy(x -> x.getItemCode(), Collectors.mapping(x -> Optional.ofNullable(x.getActualReportAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, BigDecimal> caseAmountMap = caseList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(x -> x.getItemCode(), Collectors.mapping(x -> Optional.ofNullable(x.getApplyAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        if (MapUtils.isNotEmpty(withHoldingMap)) {
            itemCodes.addAll(withHoldingMap.keySet());
        }
        if (MapUtils.isNotEmpty(caseAmountMap)) {
            itemCodes.addAll(caseAmountMap.keySet());
        }
        if (MapUtils.isNotEmpty(productMap)) {
            itemCodes.addAll(productMap.keySet());
        }

        //1.获取预算收入-汇总大区编码+品项+年月
        List<CostBudgetIncomeVo> incomeVos = costBudgetIncomeService.findListByOrgCodeAndItemCodesAndYears(regionOrgCodes, new ArrayList<>(itemCodes), years);
        incomeVos = incomeVos.stream().filter(x -> x.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode())).collect(Collectors.toList());
        List<String> itemCodes = incomeVos.stream().map(CostBudgetIncomeVo::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
        Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> null == v.getTaxRate() ? BigDecimal.ZERO : v.getTaxRate()));
        Map<String, BigDecimal> incomeMap = incomeVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getIncomeAmount()))
                .collect(Collectors.groupingBy(x -> x.getItemCode() + column + x.getYearMonthLy(),
                        Collectors.mapping(k -> Optional.ofNullable(k.getIncomeAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(k.getItemCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //2.获取预算费用
        List<CostBudgetVo> budgetVos = costBudgetVoService.findListByOrgCodeAndItemCodesAndYears(regionOrgCodes, new ArrayList<>(itemCodes), years);
        Map<String, BigDecimal> budgetMap = budgetVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getInitialAmount()))
                .collect(Collectors.groupingBy(x -> x.getProductLevelCode() + column + x.getYearMonthLy(),
                        Collectors.mapping(CostBudgetVo::getInitialAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        Map<String, List<ProductVo>> itemProductMap = checkHelper.findProductByItemCodes(new ArrayList<>(itemCodes));
        this.productPhaseMap = checkHelper.findProductPhaseMap(new ArrayList<>(itemCodes)).entrySet().stream().filter(e -> StringUtils.isNotBlank(e.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        Map<String, BigDecimal> amountMap = new HashMap<>();
        itemCodes.forEach(e -> amountMap.put(e, withHoldingMap.getOrDefault(e, BigDecimal.ZERO).add(caseAmountMap.getOrDefault(e, BigDecimal.ZERO))));

        Map<List<String>, BigDecimal> planAmountMap = Maps.newHashMap();
        Map<List<String>, String> productPhaseCanShareMap = Maps.newHashMap();
        for (Map.Entry<String, BigDecimal> entry : amountMap.entrySet()) {
            if (itemProductMap.containsKey(entry.getKey())) {
                List<ProductVo> productVoList = itemProductMap.get(entry.getKey());
                List<String> phaseCodes = productVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode())).map(ProductVo::getProductPhaseCode).distinct().collect(Collectors.toList());
                planAmountMap.put(phaseCodes, entry.getValue());
                ProductPhaseVo productPhaseVo = productPhaseMap.get(entry.getKey());
                String flag = ObjectUtils.defaultIfNull(productPhaseVo.getCanShare(), BooleanEnum.FALSE.getCapital());
                productPhaseCanShareMap.put(phaseCodes, flag);
                productPhaseListMap.put(entry.getKey(), phaseCodes);
            }
        }

        //6.查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        this.costTypeCategoryMap = costTypeCategoryVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (RegionCollectGainsAndLossesVo data : dataItemList) {
            BigDecimal rate = data.getRate();
            //POS分摊比例
            String indexKey = data.getIndexKey();
            //预算收入
            data.setBudgetIncome(incomeMap.get(indexKey));
            //搭赠费用
            BigDecimal giftCost = giftDmsMap.getOrDefault(indexKey, BigDecimal.ZERO);
            data.setGiftCost(rate == null ? giftCost : giftCost.multiply(rate));
            // 货补费用
            BigDecimal compensateGoodsCost = compensateGoodsCostMap.getOrDefault(indexKey, BigDecimal.ZERO);
            //规划收入（加搭赠）+ 货补费用
            BigDecimal planIncome = incomeDmsMap.getOrDefault(indexKey, BigDecimal.ZERO);
            data.setPlanIncome((rate == null ? planIncome : planIncome.multiply(rate)).add(data.getGiftCost()).add(compensateGoodsCost));
            //预算总额
            data.setBudgetTotalAmount(budgetMap.get(indexKey));
            //产品运输费用
            BigDecimal productTransfer = productTransferMap.getOrDefault(indexKey, BigDecimal.ZERO);
            BigDecimal peripheryTransfer = peripheryTransferMap.getOrDefault(indexKey, BigDecimal.ZERO);
            data.setProductTransportCost(rate == null ? productTransfer : productTransfer.multiply(rate));
            //周边运输费用
            data.setPeripheryTransportCost(rate == null ? peripheryTransfer : peripheryTransfer.multiply(rate));
            //生产成本
            BigDecimal productionCosts = costDmsMap.getOrDefault(indexKey, BigDecimal.ZERO);
            data.setProductionCosts((data.getRate() == null ? productionCosts : productionCosts.multiply(data.getRate())));
        }

        Map<String, List<RegionCollectGainsAndLossesVo>> dataItemMap = dataItemList.stream().collect(Collectors.groupingBy(e -> e.getItemCode()));
        for (Map.Entry<String, List<RegionCollectGainsAndLossesVo>> entry : dataItemMap.entrySet()) {
            if (!productPhaseMap.containsKey(entry.getKey())) {
                continue;
            }
            BigDecimal budgetIncome = BigDecimal.ZERO;
            BigDecimal giftCost = BigDecimal.ZERO;
            BigDecimal planIncome = BigDecimal.ZERO;
            BigDecimal budgetTotalAmount = BigDecimal.ZERO;
            BigDecimal productTransportCost = BigDecimal.ZERO;
            BigDecimal peripheryTransportCost = BigDecimal.ZERO;
            BigDecimal productionCosts = BigDecimal.ZERO;
            RegionCollectItemEstimationVo item = new RegionCollectItemEstimationVo();
            for (RegionCollectGainsAndLossesVo vo : entry.getValue()) {
                budgetIncome = budgetIncome.add(Optional.ofNullable(vo.getBudgetIncome()).orElse(BigDecimal.ZERO));
                giftCost = giftCost.add(Optional.ofNullable(vo.getGiftCost()).orElse(BigDecimal.ZERO));
                planIncome = planIncome.add(Optional.ofNullable(vo.getPlanIncome()).orElse(BigDecimal.ZERO));
                budgetTotalAmount = budgetTotalAmount.add(Optional.ofNullable(vo.getBudgetTotalAmount()).orElse(BigDecimal.ZERO));
                productTransportCost = productTransportCost.add(Optional.ofNullable(vo.getProductTransportCost()).orElse(BigDecimal.ZERO));
                peripheryTransportCost = peripheryTransportCost.add(Optional.ofNullable(vo.getPeripheryTransportCost()).orElse(BigDecimal.ZERO));
                productionCosts = productionCosts.add(Optional.ofNullable(vo.getProductionCosts()).orElse(BigDecimal.ZERO));
            }
            item.setIndexKey(entry.getKey() + column + years);
            item.setItemCode(entry.getKey());
            item.setItemName(productPhaseMap.get(entry.getKey()).getProductPhaseName());
            item.setBudgetIncome(budgetIncome);
            item.setGiftCost(giftCost);
            item.setPlanIncome(planIncome);
            item.setBudgetTotalAmount(budgetTotalAmount);
            item.setProductTransportCost(productTransportCost);
            item.setPeripheryTransportCost(peripheryTransportCost);
            item.setProductionCosts(productionCosts);
            itemList.add(item);
            //预算费率
            item.setBudgetRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(item.getBudgetTotalAmount()) && ObjectUtils.isNotEmpty(item.getBudgetIncome()) && item.getBudgetIncome().compareTo(BigDecimal.ZERO) != 0) {
                //预算总额/预算收入
                item.setBudgetRatio(item.getBudgetTotalAmount().divide(item.getBudgetIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }

        this.calPublicShareRatio();

        this.calPlanTotalAmount(planAmountMap, productPhaseCanShareMap);
        return this;
    }


    /**
     * 计算规划总额
     *
     * @param planAmountMap
     */
    public void calPlanTotalAmount(Map<List<String>, BigDecimal> planAmountMap, Map<List<String>, String> productPhaseCanShareMap) {
        for (Map.Entry<List<String>, BigDecimal> entry : planAmountMap.entrySet()) {
            List<String> list = entry.getKey();
            BigDecimal totalPlanAmount = entry.getValue();
            BigDecimal totalPlanIncomeAmount = BigDecimal.ZERO;
            List<String> indexKeyList = Lists.newArrayList();
            for (CustomerGainsAndLossesVo vo : itemList) {
                String itemCode = vo.getIndexKey().split(column)[0];
                if (list.contains(itemCode)) {
                    totalPlanIncomeAmount = totalPlanIncomeAmount.add(vo.getPlanIncome());
                    indexKeyList.add(vo.getIndexKey());
                }
            }
            //判断不为空 则按照分摊占比计算
            if (CollectionUtils.isNotEmpty(indexKeyList)) {
                Integer count = 1;
                BigDecimal ratio = BigDecimal.ONE;
                for (CustomerGainsAndLossesVo vo : itemList) {
                    if (indexKeyList.contains(vo.getIndexKey())) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(totalPlanIncomeAmount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == indexKeyList.size()) {
                            vo.setPlanTotalAmount(ObjectUtils.defaultIfNull(vo.getPlanTotalAmount(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal planTotalAmount = entry.getValue().multiply(calRatio);
                            totalPlanAmount = totalPlanAmount.subtract(planTotalAmount);
                            vo.setPlanTotalAmount(ObjectUtils.defaultIfNull(vo.getPlanTotalAmount(), BigDecimal.ZERO).add(planTotalAmount));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            } else {
                //判断是需要分摊的
                if (productPhaseCanShareMap.containsKey(entry.getKey()) && BooleanEnum.TRUE.getCapital().equals(productPhaseCanShareMap.get(entry.getKey()))) {
                    Integer count = 1;
                    BigDecimal ratio = BigDecimal.ONE;
                    BigDecimal amount = itemList.stream().map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                    for (CustomerGainsAndLossesVo vo : itemList) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (amount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(amount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == itemList.size()) {
                            vo.setPlanTotalAmount(ObjectUtils.defaultIfNull(vo.getPlanTotalAmount(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal planTotalAmount = entry.getValue().multiply(ratio);
                            totalPlanAmount = totalPlanAmount.subtract(planTotalAmount);
                            vo.setPlanTotalAmount(ObjectUtils.defaultIfNull(vo.getPlanTotalAmount(), BigDecimal.ZERO).add(planTotalAmount));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            }
        }
        for (CustomerGainsAndLossesVo data : itemList) {
            data.setMarketingCost(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanTotalAmount())) {
                data.setMarketingCost(data.getPlanTotalAmount().add(data.getGiftCost()));
            }

            //规划总额（营销费用(计提+兑付)+运输费用+分摊费用+搭赠）
            data.setPlanTotalAmount(data.getProductTransportCost()
                    .add(data.getPeripheryTransportCost())
                    .add(data.getGiftCost())
                    .add(data.getPublicShare())
                    .add(Optional.ofNullable(data.getPlanTotalAmount()).orElse(BigDecimal.ZERO)));

            //规划费率
            data.setPlanRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && ObjectUtils.isNotEmpty(data.getPlanTotalAmount()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                //规划总额/规划收入
                data.setPlanRatio(data.getPlanTotalAmount().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            //费率偏差 = 规划费率-预算费率
            data.setRatioDeviation(data.getPlanRatio().subtract(data.getBudgetRatio()));
        }
    }

    /**
     * 计算利润率
     *
     * @return
     */
    @Override
    public ItemEstimationBuilder calProfitRatio() {
        for (CustomerGainsAndLossesVo data : itemList) {
            //运输费用
            BigDecimal transportCost = data.getPeripheryTransportCost().add(data.getProductTransportCost());
            //规划利润额=规划收入-生产成本-产品运输费用-周边运输费用-营销费用-公摊费用
            data.setPlanProfitMargin(data.getPlanIncome().subtract(data.getProductionCosts()).subtract(transportCost).subtract(data.getMarketingCost())
                    .subtract(data.getPublicShareRatio().multiply(data.getPlanIncome())));
            //利润率=规划利润额/规划收入
            data.setProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setProfitRatio(data.getPlanProfitMargin().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    /**
     * 计算毛利率
     *
     * @return
     */
    @Override
    public ItemEstimationBuilder calGrossProfitRatio() {
        for (CustomerGainsAndLossesVo data : itemList) {
            //规划毛利额=规划收入-生产成本
            data.setPlanGrossProfitMargin(data.getPlanIncome().subtract(data.getProductionCosts()));
            //毛利率=规划毛利额/规划收入
            data.setGrossProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setGrossProfitRatio(data.getPlanGrossProfitMargin().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public ItemEstimationBuilder calLogisticsRatio() {
        for (CustomerGainsAndLossesVo data : itemList) {
            //物流费率=(产品运输费用+周边运输费用)/规划收入
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setLogisticsRatio(data.getProductTransportCost().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public ItemEstimationBuilder calMarketingRatio() {
        for (CustomerGainsAndLossesVo data : itemList) {
            //营销费率=营销费用/规划收入
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setMarketingRatio(data.getMarketingCost().divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public ItemEstimationBuilder calPublicShareRatio() {
        List<String> orgCodesAll = regionOrgCodes;
        for (CustomerGainsAndLossesVo vo : itemList) {
            BigDecimal ratio = publicShareRatioService.findRatioByCondition(orgCodesAll, years);
            //如果等于0  则取方案上的部门上级所有去找
            if (ratio.compareTo(BigDecimal.ZERO) == 0) {
                List<OrgVo> list = orgVoService.findAllParentByOrgCodes(Lists.newArrayList(orgCodesAll));
                List<String> list1 = list.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                ratio = publicShareRatioService.findRatioByCondition(Lists.newArrayList(list1), years);
            }
            vo.setPublicShareRatio(ratio);
            //公摊费用
            vo.setPublicShare(vo.getPlanIncome().multiply(Optional.ofNullable(ratio).orElse(BigDecimal.ZERO)));
        }
        return this;
    }

    @Override
    public ItemEstimationBuilder calSecondCategory() {
//        for (CustomerGainsAndLossesVo data : dataList) {
//            //二级费用大类计算
//            Map<String, BigDecimal> secondCategoryMap = Maps.newHashMap();
//            for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
//                BigDecimal cashAmount = caseList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()) &&
//                        (x.getBelongDepartmentCode() + column + x.getYears()).equals(data.getIndexKey()))
//                        .map(x -> Optional.ofNullable(x.getCashAmount()).orElse(BigDecimal.ZERO))
//                        .filter(Objects::nonNull)
//                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                BigDecimal withHoldingAmount = withHoldingList.stream().filter(x -> entry.getValue().contains(x.getCostTypeCategoryCode()) &&
//                        (x.getBelongDepartmentCode() + column + x.getYears()).equals(data.getIndexKey()))
//                        .map(x -> Optional.ofNullable(x.getWithHoldingAmount()).orElse(BigDecimal.ZERO))
//                        .filter(Objects::nonNull)
//                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                BigDecimal ratio = BigDecimal.ZERO;
//                if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
//                    ratio = (cashAmount.add(withHoldingAmount)).divide(data.getPlanIncome(), 2, BigDecimal.ROUND_HALF_DOWN);
//                }
//                secondCategoryMap.put(entry.getKey(), ratio);
//
//            }
//            for (Map.Entry<String, BigDecimal> entry : secondCategoryMap.entrySet()) {
//                BigDecimal value = entry.getValue();
//                if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
//                    data.setContract(value);
//                } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
//                    data.setGeneralization(value);
//                } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
//                    data.setPromotion(value);
//                } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
//                    data.setSalesReward(value);
//                }
//
//            }
//            data.setSecondCostCategoryMap(secondCategoryMap);
//        }
//        return this;
        // 通过stream进行分组汇总
        Map<String, Map<String, BigDecimal>> categoryItemCaseAmount = caseList.stream().filter(Objects::nonNull).filter(x -> ObjectUtils.isNotEmpty(x.getCategoryCode()) && ObjectUtils.isNotEmpty(x.getItemCode()) && ObjectUtils.isNotEmpty(x.getCashAmount())).collect(Collectors.groupingBy(MarketingPlanCase::getCategoryCode, Collectors.toMap(MarketingPlanCase::getItemCode, MarketingPlanCase::getCashAmount, BigDecimal::add)));

        Map<String, Map<String, BigDecimal>> categoryItemWithHoldingAmount = withHoldingList.stream().filter(Objects::nonNull).filter(x -> ObjectUtils.isNotEmpty(x.getCostTypeCategoryCode())
                && ObjectUtils.isNotEmpty(x.getItemCode()) && ObjectUtils.isNotEmpty(x.getActualReportAmount()))
                .collect(Collectors.groupingBy(WithHoldingVo::getCostTypeCategoryCode, Collectors.toMap(WithHoldingVo::getItemCode, e -> Objects.nonNull(e.getNoTaxActualReportAmount())?  e.getNoTaxActualReportAmount() : BigDecimal.ZERO, BigDecimal::add)));

        Map<String, Map<String, BigDecimal>> categoryItemApplyAmount = new HashMap<>();
        Set<String> categoryCodes = new HashSet<>();
        if (MapUtils.isNotEmpty(categoryItemCaseAmount)) {
            categoryCodes.addAll(categoryItemCaseAmount.keySet());
        }
        if (MapUtils.isNotEmpty(categoryItemWithHoldingAmount)) {
            categoryCodes.addAll(categoryItemWithHoldingAmount.keySet());
        }
        for (String categoryCode : categoryCodes) {
            Map<String, BigDecimal> caseMap = categoryItemCaseAmount.get(categoryCode);
            Map<String, BigDecimal> withHoldingMap = categoryItemWithHoldingAmount.get(categoryCode);
            Map<String, BigDecimal> allMap = new HashMap<>();
            if (MapUtils.isNotEmpty(caseMap)) {
                allMap.putAll(caseMap);
            }
            if (MapUtils.isNotEmpty(withHoldingMap)) {
                withHoldingMap.forEach((k, v) -> {
                    if (allMap.containsKey(k)) {
                        allMap.put(k, allMap.get(k).add(v));
                    } else {
                        allMap.put(k, v);
                    }
                });
            }
            categoryItemApplyAmount.put(categoryCode, allMap);
        }

        // 遍历大类和项目的映射关系
        costTypeCategoryMap.forEach((secondCategory, firstCategoryList) -> {
            categoryItemApplyAmount.forEach((category, itemMap) -> {
                if (firstCategoryList.contains(category)) {
                    itemMap.forEach((itemCode, value) -> {
                        if (productPhaseMap.containsKey(itemCode)) {
                            ProductPhaseVo productPhaseVo = productPhaseMap.get(itemCode);
                            if (BooleanEnum.TRUE.getCapital().equals(productPhaseVo.getCanShare())) {
                                allocateToDataList(itemCode, value, secondCategory);
                            } else {
                                allocateDirectlyToDataList(itemCode, value, secondCategory);
                            }
                        }
                    });
                }
            });
        });
        this.transRatio();
        return this;
    }

    private void allocateToDataList(String itemCode, BigDecimal value, String secondCategory) {
        List<String> itemCodes = productPhaseListMap.get(itemCode);
        BigDecimal totalPlanIncomeAmount = itemList.stream().filter(vo -> itemCodes.contains(vo.getIndexKey().split(column)[0])).map(CustomerGainsAndLossesVo::getPlanIncome).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal remainingValue = value;
            for (int i = 0; i < itemList.size(); i++) {
                CustomerGainsAndLossesVo vo = itemList.get(i);
                if (itemCodes.contains(vo.getIndexKey().split(column)[0])) {
                    BigDecimal calRatio = BigDecimal.ZERO;
                    if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0) {
                        calRatio = vo.getPlanIncome().divide(totalPlanIncomeAmount, 4, BigDecimal.ROUND_HALF_DOWN);
                    }
                    BigDecimal allocation = i == itemList.size() - 1 ? remainingValue : value.multiply(calRatio);
                    remainingValue = remainingValue.subtract(allocation);
                    applyAllocation(vo, allocation, secondCategory);
                }
            }
        }
    }

    private void allocateDirectlyToDataList(String itemCode, BigDecimal value, String secondCategory) {
        itemList.stream().filter(vo -> vo.getIndexKey().split(column)[0].equals(itemCode)).forEach(vo -> applyAllocation(vo, value, secondCategory));
    }

    private void applyAllocation(CustomerGainsAndLossesVo vo, BigDecimal value, String secondCategory) {
        BigDecimal ratio = BigDecimal.ZERO;
        if (vo.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
            ratio = value.divide(vo.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        switch (SecondCategoryEnum.value(secondCategory)) {
            case contract:
                vo.setContract(ObjectUtils.defaultIfNull(vo.getContract(), BigDecimal.ZERO).add(ratio));
                break;
            case generalization:
                vo.setGeneralization(ObjectUtils.defaultIfNull(vo.getGeneralization(), BigDecimal.ZERO).add(ratio));
                break;
            case promotion:
                vo.setPromotion(ObjectUtils.defaultIfNull(vo.getPromotion(), BigDecimal.ZERO).add(ratio));
                break;
            case salesReward:
                vo.setSalesReward(ObjectUtils.defaultIfNull(vo.getSalesReward(), BigDecimal.ZERO).add(ratio));
                break;
            case callback:
                vo.setPromotion(ObjectUtils.defaultIfNull(vo.getPromotion(), BigDecimal.ZERO).add(ratio));
                break;
            case display:
                vo.setDisplay(ObjectUtils.defaultIfNull(vo.getDisplay(), BigDecimal.ZERO).add(ratio));
                break;
            case disseminateS:
                vo.setDisseminate(ObjectUtils.defaultIfNull(vo.getDisseminate(), BigDecimal.ZERO).add(ratio));
                break;
            default:
                break;
        }
    }

    private void transRatio() {
        for (CustomerGainsAndLossesVo vo : itemList) {
            if (ObjectUtils.isNotEmpty(vo.getBudgetIncome())) {
                vo.setBudgetIncome(vo.getBudgetIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (vo.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                vo.setPromotionStr((vo.getGiftCost().divide(vo.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN)).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanIncome())) {
                vo.setPlanIncome(vo.getPlanIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getIncomeAchieveRatio())) {
                vo.setIncomeAchieveRatioStr(vo.getIncomeAchieveRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetRatio())) {
                vo.setBudgetRatioStr(vo.getBudgetRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanRatio())) {
                vo.setPlanRatioStr(vo.getPlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getRatioDeviation())) {
                vo.setRatioDeviationStr(vo.getRatioDeviation().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getProfitRatio())) {
                vo.setProfitRatioStr(vo.getProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGrossProfitRatio())) {
                vo.setGrossProfitRatioStr(vo.getGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getLogisticsRatio())) {
                vo.setLogisticsRatioStr(vo.getLogisticsRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getMarketingRatio())) {
                vo.setMarketingRatioStr(vo.getMarketingRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPublicShareRatio())) {
                vo.setPublicShareRatioStr(vo.getPublicShareRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getCallback())) {
                vo.setCallbackStr(vo.getCallback().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisplay())) {
                vo.setDisplayStr(vo.getDisplay().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGeneralization())) {
                vo.setGeneralizationStr(vo.getGeneralization().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisseminate())) {
                vo.setDisseminateStr(vo.getDisseminate().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getSalesReward())) {
                vo.setSalesRewardStr(vo.getSalesReward().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getContract())) {
                vo.setContractStr(vo.getContract().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
        }
    }
}
