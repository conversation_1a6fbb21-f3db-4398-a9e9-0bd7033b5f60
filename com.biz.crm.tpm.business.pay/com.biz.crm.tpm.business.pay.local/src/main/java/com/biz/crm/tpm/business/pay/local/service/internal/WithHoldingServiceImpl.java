package com.biz.crm.tpm.business.pay.local.service.internal;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.eunm.AuditStatusEnum;
import com.biz.crm.business.common.base.eunm.CashStatusEnum;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.common.ie.sdk.excel.util.EuropaParamsTools;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.DeliverySearchDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.ReplenishmentPoolDetailDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.WithholdingSearchDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolOperationType;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolTypeEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.renyang.ReplenishmentPoolDetailVoService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.vo.renyang.ReplenishmentPoolDetailVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.dto.OrgQueryDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.PlanClosureEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.helper.RegionCollectHelper;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectDepartmentEstimationService;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectGainsAndLossesService;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectItemEstimationService;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectMarketingEstimationService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.*;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.local.calcomponent.WithholdingGenerateComponent;
import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.*;
import com.biz.crm.tpm.business.pay.local.helper.WithHoldingCollectHelper;
import com.biz.crm.tpm.business.pay.local.repository.*;
import com.biz.crm.tpm.business.pay.local.service.AccountService;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.constant.WithHoldingConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingBalanceDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingCollectDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingDto;
import com.biz.crm.tpm.business.pay.sdk.dto.log.WithHoldingLogEventDto;
import com.biz.crm.tpm.business.pay.sdk.enums.*;
import com.biz.crm.tpm.business.pay.sdk.event.log.WithHoldingLogEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.*;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.service.FormulaCalService;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 费用预提(WithHolding)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-25 14:20:20
 */
@Slf4j
@Service("WithHoldingService")
public class WithHoldingServiceImpl extends BusinessPageCacheServiceImpl<WithHoldingVo, WithHoldingDto> implements WithHoldingService {

    @Autowired(required = false)
    private WithHoldingRepository withHoldingRepository;

    @Autowired(required = false)
    private WithHoldingCollectRepository withHoldingCollectRepository;

    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private AccountService accountService;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired(required = false)
    private CostTypeDetailVoService costTypeDetailVoService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Autowired(required = false)
    private WithHoldingCollectService withHoldingCollectService;


    @Autowired(required = false)
    private RegionCollectHelper planHelper;

    @Autowired(required = false)
    private WithHoldingPageCacheHelper pageCacheHelper;

    @Autowired(required = false)
    private RedisTemplate redisTemplate;

    @Autowired(required = false)
    private RegionCollectDepartmentEstimationService departmentEstimationService;

    @Autowired(required = false)
    private RegionCollectGainsAndLossesService gainsAndLossesService;

    @Autowired(required = false)
    private RegionCollectItemEstimationService itemEstimationService;

    @Autowired(required = false)
    private RegionCollectMarketingEstimationService marketingEstimationService;

    @Autowired(required = false)
    private ReplenishmentPoolDetailVoService replenishmentPoolDetailVoService;

    @Autowired(required = false)
    private FormulaCalService formulaCalService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Autowired(required = false)
    private ProductVoService productVoService;

    @Autowired(required = false)
    private CostTypeCategoryVoService costTypeCategoryVoService;

    @Resource
    private LoginUserService loginUserService;
    @Resource
    private MarketingPlanProductRepository marketingPlanProductRepository;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    @Autowired(required = false)
    private DeliveryReplenishmentPoolDetailRepository deliveryReplenishmentPoolDetailRepository;

    @Autowired(required = false)
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;

    @Autowired(required = false)
    private OaOperateService oaOperateService;

    @Autowired(required = false)
    private RedisService redisService;

    @Autowired(required = false)
    private WithHoldingLockVoService withHoldingLockVoService;

    @Autowired(required = false)
    private FeeCashService feeCashService;

    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;

    @Autowired(required = false)
    private WithholdingGenerateComponent withholdingGenerateComponent;
    @Autowired
    private ActivityPrepayDetailRecordRepository activityPrepayDetailRecordRepository;

    @Autowired
    private FeeCashDetailRepository feeCashDetailRepository;

    @Autowired
    private MarketingPlanCaseRepository planCaseRepository;

    @Autowired
    private WithHoldingWriteOffService withHoldingWriteOffService;

    @Autowired
    private WithHoldingWriteOffRepository withHoldingWriteOffRepository;

    @Autowired
    private ProductPhaseVoService productPhaseVoService;


    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     * @return
     */
    @Override
    public Page<WithHoldingVo> findByConditions(Pageable pageable, WithHoldingDto dto) {
        ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new WithHoldingDto();
        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        return this.withHoldingRepository.findByConditions(pageable, dto);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @Override
    public WithHolding findById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return this.withHoldingRepository.findByIdAndTenantCode(id, TenantUtils.getTenantCode());
    }

    /**
     * 新增数据
     *
     * @param withHoldings 实体对象
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(List<WithHolding> withHoldings) {
        Validate.notEmpty(withHoldings, "新增数据集合不能为空");

        List<String> codeList = generateCodeService.generateCodeYearMonth(WithHoldingConstant.PREFIX_CODE, withHoldings.size());
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);

        for (int i = 0; i < withHoldings.size(); i++) {
            WithHolding withHolding = withHoldings.get(i);
            withHolding.setWithHoldingCode(codeList.get(i));
            withHolding.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            withHolding.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            withHolding.setTenantCode(TenantUtils.getTenantCode());
            withHolding.setStatus(ProcessStatusEnum.PREPARE.getDictCode());
            withHolding.setWithHoldingSource("TPM");
            if (StringUtil.isEmpty(withHolding.getPositionCode())) {
                withHolding.setPositionCode(loginDetails.getPostCode());
                withHolding.setOrgCode(loginDetails.getOrgCode());
                withHolding.setOrgName(loginDetails.getOrgName());
            }
            withHolding.setSchemeCreateName(loginDetails.getRealName());
            withHolding.setBeThisFee(BooleanEnum.TRUE.getCapital());
            Validate.notNull(withHolding.getWithHoldingCode(), "新增数据时，预提编号 不能为空！");
        }
        this.withHoldingRepository.saveBatch(withHoldings);

        //新增业务日志
        SerializableBiConsumer<WithHoldingLogEventListener, WithHoldingLogEventDto> onCreate =
                WithHoldingLogEventListener::onCreate;
        Collection<WithHoldingVo> withHoldingVos = this.nebulaToolkitService.copyCollectionByWhiteList(withHoldings, WithHolding.class, WithHoldingVo.class, HashSet.class, ArrayList.class);
        for (WithHoldingVo withHolding : withHoldingVos) {
            WithHoldingLogEventDto dto = new WithHoldingLogEventDto();
            dto.setOriginal(null);
            dto.setNewest(withHolding);
            this.nebulaNetEventClient.publish(dto, WithHoldingLogEventListener.class, onCreate);
        }
    }

    @Autowired
    private BackWithHoldingWriteOffRepository backWithHoldingWriteOffRepository;


    /**
     * 修改新据
     *
     * @param withHolding 实体对象
     * @return 修改结果
     */
    @Transactional
    @Override
    public WithHolding update(WithHolding withHolding) {
        this.updateValidate(withHolding);
        WithHolding entity = this.findById(withHolding.getId());
        BigDecimal oldAmount = entity.getWithHoldingAmount();
        entity.setWithHoldingAmount(withHolding.getWithHoldingAmount());
        this.amountValidate(entity);
        entity.setTenantCode(TenantUtils.getTenantCode());
        this.withHoldingRepository.saveOrUpdate(entity);
        //更新业务日志
        SerializableBiConsumer<WithHoldingLogEventListener, WithHoldingLogEventDto> onUpdate =
                WithHoldingLogEventListener::onUpdate;
        WithHoldingLogEventDto dto = new WithHoldingLogEventDto();
        WithHoldingVo newVO = new WithHoldingVo();
        WithHoldingVo oldVO = new WithHoldingVo();
        oldVO.setId(entity.getId());
        oldVO.setWithHoldingAmount(oldAmount);
        newVO.setId(entity.getId());
        newVO.setWithHoldingAmount(entity.getWithHoldingAmount());
        dto.setNewest(newVO);
        dto.setOriginal(oldVO);
        this.nebulaNetEventClient.publish(dto, WithHoldingLogEventListener.class, onUpdate);
        return withHolding;
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
        List<WithHolding> entities = withHoldingRepository.findByIds(idList);
        Validate.notEmpty(entities, "未找到对应的计提数据");
        entities.forEach(e -> Validate.isTrue(ProcessStatusEnum.PREPARE.getDictCode().equals(e.getStatus()) ||
                ProcessStatusEnum.REJECT.getDictCode().equals(e.getStatus()) ||
                ProcessStatusEnum.RECOVER.getDictCode().equals(e.getStatus()), "只能删除待提交/驳回/追回的数据"));

        List<WithHolding> poolList = entities.stream().filter(e -> Lists.newArrayList(ReplenishmentPoolOperationType.POD_DIFF_ADD.getCode(),
                ReplenishmentPoolOperationType.POD_DIFF_REDUCE.getCode(),
                ReplenishmentPoolOperationType.ORDER_CLOSE_DETAIL.getCode()).contains(e.getWithHoldingType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(poolList)) {
            List<String> poolDetailCodeList = poolList.stream().map(e -> e.getBusinessCode()).collect(Collectors.toList());
            ReplenishmentPoolDetailDto poolDetailDto = new ReplenishmentPoolDetailDto();
            poolDetailDto.setPoolDetailCodeList(poolDetailCodeList);
            poolDetailDto.setWithholdingStatus(BooleanEnum.FALSE.getCapital());
            replenishmentPoolDetailVoService.updateWithholdingStatusByDetailCodes(poolDetailDto);
        }

        //原计提活动是否计提设置为否
        List<String> schemeDetailCodes = entities.stream().filter(e -> StringUtils.isNotBlank(e.getActivitiesDetailCode()))
                .filter(Objects::nonNull).map(e -> e.getActivitiesDetailCode()).collect(Collectors.toList());
        marketingPlanCaseService.updateWithHoldingStatusBySchemeDetailCodes(schemeDetailCodes, BooleanEnum.FALSE.getCapital());

        this.withHoldingRepository.remove(idList);
    }

    /**
     * 提交审批
     *
     * @param dto
     */
    @Override
    public WithHoldingCollectVo submit(String cacheKey, WithHoldingDto dto) {
        Validate.notNull(dto, "提交时，对象不能为空！");
        Validate.notBlank(dto.getYearMonthLy(), "提交时，年月不能为空！");
        Validate.notBlank(dto.getDepartmentCode(), "提交时，所属部门不能为空！");

        List<OrgVo> orgVos = orgVoService.findByOrgCodes(Collections.singletonList(dto.getDepartmentCode()));

        Validate.notEmpty(orgVos, "部门未找到");
        OrgVo orgVo = orgVos.get(0);
        List<OrgVo> orgChildrenVos = orgVoService.findAllChildrenByOrgCode(orgVo.getOrgCode());

        List<WithHolding> withHoldings = this.withHoldingRepository.findByYearMonthBelongDepartmentCode(dto.getYearMonthLy(),
                orgChildrenVos.stream().map(OrgVo::getOrgCode).collect(Collectors.toList()));
        Validate.notEmpty(withHoldings, "未找到对应的计提数据！");

        withHoldings.forEach(e -> Validate.isTrue(ConfirmStatusEnum.CONFIRMED.getCode().equals(e.getConfirmStatus()), "存在未确认的计提数据，请确认后再提交"));

        String yearMonthLy = dto.getYearMonthLy();
        WithHoldingCollectVo collectVo = new WithHoldingCollectVo();
        collectVo.setYearMonthLy(yearMonthLy);
        collectVo.setDepartmentCode(orgVo.getOrgCode());
        collectVo.setDepartmentName(orgVo.getOrgName());
        collectVo.setWithHoldingAmount(withHoldings.stream().filter(k -> Objects.nonNull(k.getActualReportAmount()))
                .map(WithHolding::getActualReportAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

        List<WithHoldingVo> withHoldingVos = new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(withHoldings, WithHolding.class, WithHoldingVo.class, LinkedHashSet.class, ArrayList.class));

        List<String> orgCodeList = orgChildrenVos.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
        Map<String, OrgVo> orgMap = orgChildrenVos.stream().collect(Collectors.toMap(OrgVo::getOrgCode, v -> v, (n, o) -> n));
        MarketingPlanCaseVo planCaseVo = new MarketingPlanCaseVo();
        planCaseVo.setYears(yearMonthLy);
        planCaseVo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        planCaseVo.setBelongDepartmentCodes(orgCodeList);
        Page<MarketingPlanCaseVo> casePage = findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseVo);

        Map<String, MarketingPlanCaseVo> caseVoMap = casePage.getRecords().stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));
        List<MarketingPlanCase> caseList = casePage.getTotal() > 0 ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(casePage.getRecords(), MarketingPlanCaseVo.class, MarketingPlanCase.class, LinkedHashSet.class, ArrayList.class)) : new ArrayList<>();
        for (MarketingPlanCase planCase : caseList) {
            MarketingPlanCaseVo caseVo = caseVoMap.get(planCase.getSchemeDetailCode());
            if (!CollectionUtils.isEmpty(caseVo.getProductList())) {
                planCase.setProductList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(caseVo.getProductList(), MarketingPlanProductVo.class, MarketingPlanProduct.class, LinkedHashSet.class, ArrayList.class)));
            }
            if (!CollectionUtils.isEmpty(caseVo.getLevelList())) {
                planCase.setLevelList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(caseVo.getLevelList(), MarketingPlanProductVo.class, MarketingPlanProduct.class, LinkedHashSet.class, ArrayList.class)));
            }
        }
        List<String> customerCodes = caseList.stream().map(MarketingPlanCase::getCustomerCode).distinct().collect(Collectors.toList());
        List<MarketingSalesPlanVo> salePlanList = marketingSalesPlanService.findListBySalesPlanQueryVo(new SalesPlanQueryVo() {{
            this.setCustomerCodeSet(new HashSet<>(customerCodes));
            this.setYears(yearMonthLy);
        }});

        WithHoldingCollectHelper helper = ApplicationContextHolder.getContext().getBean(WithHoldingCollectHelper.class);

        //2.客户损益预测
        List<RegionCollectGainsAndLossesVo> gainsAndLossesVoList = helper.buildCustomerGainsAndLosses(null, orgMap, yearMonthLy, withHoldingVos, caseList);
        //3.二级部门测算
        List<RegionCollectDepartmentEstimationVo> departmentEstimationVoList = helper.buildDepartmentEstimation(null, orgVo.getOrgCode(), yearMonthLy, withHoldingVos);
        //4.品项费用测算
        List<RegionCollectItemEstimationVo> itemEstimationVoList = helper.buildItemEstimation(null, yearMonthLy, orgVo.getOrgCode(), withHoldingVos);
        //规划营销费用测算
        List<RegionCollectMarketingEstimationVo> planMarketingEstimationVos = planHelper.buildMarketingEstimation(null, yearMonthLy, orgVo.getOrgCode(), null, null, caseList, salePlanList);
        //5.营销费用测算
        List<RegionCollectMarketingEstimationVo> marketingEstimationVos = helper.buildMarketingEstimation(null, yearMonthLy, orgVo.getOrgCode(), withHoldingVos, planMarketingEstimationVos);
        collectVo.setGainsAndLossesList(gainsAndLossesVoList);
        collectVo.setDepartmentEstimationList(departmentEstimationVoList);
        collectVo.setItemEstimationList(itemEstimationVoList);
        collectVo.setMarketingEstimationList(marketingEstimationVos);
        collectVo.setWithHoldingList(withHoldingVos);
        // 6.搭赠及周边
        List<DictDataVo> giftSurroundExpenseItem = dictDataVoService.findByDictTypeCode("gift_surround_expense_item");
        List<String> itemCodes = giftSurroundExpenseItem.stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
        MarketingPlanCaseVo giftAndSurroundDto = new MarketingPlanCaseVo();
        giftAndSurroundDto.setYears(yearMonthLy);
        giftAndSurroundDto.setOrgCodeList(orgCodeList);
        giftAndSurroundDto.setDetailCodeList(itemCodes);
        giftAndSurroundDto.setChangeFlag(BooleanEnum.FALSE.getCapital());
        Page<MarketingPlanCaseVo> giftPage = marketingPlanCaseService.findMarketingPlanCaseReportList(PageRequest.of(0, 50000) , giftAndSurroundDto);
        if (!CollectionUtils.isEmpty(giftPage.getRecords())) {
            collectVo.setGiftAndSurroundingList(giftPage.getRecords());
        }
        // 7. 已结案兑付
        FeeCashDetailDto feeCashDetailDto = new FeeCashDetailDto();
        feeCashDetailDto.setYears(yearMonthLy);
        feeCashDetailDto.setBelongDepartmentCodes(orgCodeList);
//        feeCashDetailDto.setBeFullAudit("Y");
        Page<FeeCashDetailVo> auditedPage = feeCashDetailRepository.findFullAuditedDetailList(PageRequest.of(0, 50000), feeCashDetailDto);
        if (!CollectionUtils.isEmpty(auditedPage.getRecords())) {
            collectVo.setAuditedAndCashedActivityList(auditedPage.getRecords());
        }


        addItemCache(cacheKey, new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(withHoldingVos, WithHoldingVo.class, WithHoldingDto.class, LinkedHashSet.class, ArrayList.class)));
        return collectVo;
    }

    /**
     * 提交审批保存
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String submitSave(String cacheKey, WithHoldingCollectDto dto) {
        //生成汇总
        WithHoldingCollectVo vo = withHoldingCollectService.save(dto);
        //重新校验计提数据
        List<String> withHoldingCodes = findCacheList(cacheKey).stream().map(e -> e.getWithHoldingCode()).collect(Collectors.toList());

        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findByCodes(withHoldingCodes);
        Validate.notEmpty(withHoldingVos, "未找到对应的计提数据！");
        withHoldingVos.forEach(e -> {
            Validate.isTrue(ConfirmStatusEnum.CONFIRMED.getCode().equals(e.getConfirmStatus()), "存在未确认的计提数据，请确认后再提交");
            Validate.isTrue(!Arrays.asList(ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode()).contains(e.getStatus()), "存在审批中或审批通过的计提数据");
        });
        dto.setWithHoldingList(withHoldingVos);
        List<String> codes = dto.getWithHoldingList().stream().map(WithHoldingVo::getWithHoldingCode).collect(Collectors.toList());
        withHoldingRepository.updateCollectCodeByCodes(codes, vo.getCollectCode());
        withHoldingCollectRepository.updateCollectAmount(vo.getCollectCode());
        departmentEstimationService.saveList(dto.getDepartmentEstimationList(), vo.getCollectCode());
        gainsAndLossesService.saveList(dto.getGainsAndLossesList(), vo.getCollectCode());
        itemEstimationService.saveList(dto.getItemEstimationList(), vo.getCollectCode());
        marketingEstimationService.saveList(dto.getMarketingEstimationList(), vo.getCollectCode());

        clearCache(cacheKey);
        withHoldingCollectService.submit(Collections.singletonList(vo.getId()));
        return vo.getId();
    }

    /**
     * 手动
     *
     * @param withHoldings
     * @return
     */
    @Override
    public void handleManual(List<WithHoldingImportVo> withHoldings) {
        Validate.notEmpty(withHoldings, "新增费用预提列表不能为空");
        List<WithHolding> list = (List<WithHolding>) this.nebulaToolkitService.copyCollectionByWhiteList(withHoldings, WithHoldingImportVo.class, WithHolding.class, HashSet.class, ArrayList.class);
        for (WithHolding withHolding : list) {
            withHolding.setWithHoldingType(WithHoldingTypeEnum.HANDLE.getDictCode());
            withHolding.setConfirmStatus(ConfirmStatusEnum.CONFIRMED.getCode());
        }
        this.create(list);
    }

    /**
     * 自动
     *
     * @param withHolding
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleAuto(WithHoldingDto withHolding) {
        loginUserService.refreshAuthentication(null);
        String withHoldingYears = withHolding.getYearMonthLy();
        Validate.notBlank(withHoldingYears, "预提年月不能为空");
        List<WithHolding> entities = new ArrayList<>();
        //查找活动明细
        MarketingPlanCaseVo planCaseVo = new MarketingPlanCaseVo();
        planCaseVo.setYears(withHoldingYears);
        planCaseVo.setWithholding(BooleanEnum.TRUE.getCapital());
        planCaseVo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        planCaseVo.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());
        Page<MarketingPlanCaseVo> caseVoPage = findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseVo);
        if (caseVoPage.getTotal() > 0L) {
            List<MarketingPlanCaseVo> records = caseVoPage.getRecords();
            //方案创建人
            List<String> schemeCodes = records.stream().map(MarketingPlanCaseVo::getSchemeCode).collect(Collectors.toList());
            List<RegionCollectSchemeVo> collectSchemeVos = withHoldingRepository.findSchemeCreateInfoList(schemeCodes);
            Map<String, RegionCollectSchemeVo> collectSchemeVoMap = collectSchemeVos.stream().collect(Collectors.toMap(RegionCollectSchemeVo::getSchemeCode, Function.identity()));

            //产品范围
            List<String> schemeDetailCodes = records.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<MarketingPlanProduct> marketingPlanProductList = marketingPlanProductRepository.findListBySchemeDetailCodes(schemeDetailCodes);
            Map<String, List<MarketingPlanProduct>> planProductMap = marketingPlanProductList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
            for (MarketingPlanCaseVo planCase : records) {
                planCase.setProductAndItemList(BeanUtil.copyToList(planProductMap.getOrDefault(planCase.getSchemeDetailCode(), Lists.newArrayList()), MarketingPlanProductVo.class));
            }

            // 以下条件才允许计提：
            // 1.活动细类对应的“是否计提”维护为”是“
            // 2.结案状态为“未结案”“部分结案”的活动、
            // 或者结案状态为“完全结案”，且兑付状态为“未兑付”“部分兑付”才计提、
            // 或者结案状态为“完全结案”，且兑付状态为“完全兑付”，且完全兑付日期不等于计提年月
            Set<String> costTypeDetailCodeSet = records.stream().map(e -> e.getDetailCode()).collect(Collectors.toSet());
            List<CostTypeDetailVo> costTypeDetailList = costTypeDetailVoService.findByCodes(new ArrayList<>(costTypeDetailCodeSet));
            Validate.notEmpty(costTypeDetailList, "未找到任意活动细类");
            Set<String> detailCodeSet = costTypeDetailList.stream().filter(e -> BooleanEnum.TRUE.getCapital().equals(e.getIsWithHolding())).map(e -> e.getDetailCode()).collect(Collectors.toSet());
            List<MarketingPlanCaseVo> planCaseVoList = records.stream().filter(e -> detailCodeSet.contains(e.getDetailCode()) &&
                    ((AuditStatusEnum.NOT_AUDIT.getCode().equals(e.getAuditStatus()) || AuditStatusEnum.PART_AUDIT.getCode().equals(e.getAuditStatus())) ||
                            (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) && (CashStatusEnum.NOT_CASH.getCode().equals(e.getCashStatus()) || CashStatusEnum.PART_CASH.getCode().equals(e.getCashStatus()))) ||
                            (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) && CashStatusEnum.WHOLE_CASH.getCode().equals(e.getCashStatus()) && StringUtils.isNotBlank(e.getWholeCashDate()) && !e.getWholeCashDate().startsWith(withHoldingYears))
                    )).collect(Collectors.toList());

            //货补
            List<MarketingPlanCaseVo> replenishmentVoList = records.stream().filter(e -> detailCodeSet.contains(e.getDetailCode()) &&
                    (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) && (CashStatusEnum.NOT_CASH.getCode().equals(e.getCashStatus()) || CashStatusEnum.PART_CASH.getCode().equals(e.getCashStatus()))) &&
                    CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType())
            ).collect(Collectors.toList());
            List<String> replenishmentVoCodes = CollectionUtils.isEmpty(replenishmentVoList) ? new ArrayList<>() : replenishmentVoList.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
            Map<String, BigDecimal> deliveryAmountMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(replenishmentVoCodes)) {
                deliveryAmountMap = replenishmentPoolDetailVoService.findDeliveryAmountByActivityCodes(replenishmentVoCodes);
            }

            //活动计提
            if (!CollectionUtils.isEmpty(planCaseVoList)) {
                Map<String, List<MarketingPlanCaseVo>> caseTypeMap = planCaseVoList.stream().collect(Collectors.groupingBy(MarketingPlanCaseVo::getCaseType));
                Map<String, BigDecimal> finalDeliveryAmountMap = deliveryAmountMap;
                //在【费用兑付/关闭明细】下查询操作类型=清空/发货/费用兑付/费用关闭且业务发生日期与计提入账年月一致的明细汇总费用金额
                List<String> codes = planCaseVoList.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
                Map<String, BigDecimal> deliveryMap = deliveryReplenishmentPoolDetailService.findBySchemeDetailCodes(codes, withHoldingYears);
                caseTypeMap.forEach((k, v) -> {
                    v.forEach(e -> {
                        //
                        RegionCollectSchemeVo baseCreateInfo = new RegionCollectSchemeVo();
                        if (StringUtils.isNotBlank(e.getSchemeCode())) {
                            baseCreateInfo = collectSchemeVoMap.getOrDefault(e.getSchemeCode(), new RegionCollectSchemeVo());
                        }
                        //已申请未结案
                        //除周边物料、随单搭赠、进货返利、特价、直降外，按方案申请金额计提；返利根据返利公式测算计提金额
                        if (AuditStatusEnum.NOT_AUDIT.getCode().equals(e.getAuditStatus()) || AuditStatusEnum.PART_AUDIT.getCode().equals(e.getAuditStatus())) {
                            if (!MarketingPlanCaseTypeEnum.noWithHolding().contains(k)) {
                                WithHolding entity = new WithHolding();
                                BeanUtils.copyProperties(baseCreateInfo, entity);
                                buildWithHolding(entity, e, "TPM", WithHoldingTypeEnum.NOT_AUDIT.getDictCode(), withHoldingYears);
                                entity.setWithHoldingAmount(e.getApplyAmount().subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                                entity.setActualAmount(entity.getWithHoldingAmount());
                                entity.setWithHoldingReportAmount(entity.getWithHoldingAmount());
                                entity.setActualReportAmount(entity.getWithHoldingAmount());
                                entity.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                                entities.add(entity);
                                //返利
                            } else if (MarketingPlanCaseTypeEnum.back.getCode().equals(k)) {
                                FormulaCalBaseVo calVo = new FormulaCalBaseVo();
                                calVo.setCustomerCode(e.getCustomerCode());
                                calVo.setActDetailCode(e.getSchemeDetailCode());
                                calVo.setYears(e.getYears());
                                calVo.setRebateStartDate(e.getRebateStartDate());
                                calVo.setRebateEndDate(e.getRebateEndDate());
                                calVo.setStartDate(e.getStartDate());
                                calVo.setEndDate(e.getEndDate());
                                calVo.setConditionNum(e.getConditionNum());
                                calVo.setItemCodeSet(Optional.ofNullable(e.getItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                                calVo.setProductCodeSet(Optional.ofNullable(e.getProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                                calVo.setFeeItemCodeSet(Optional.ofNullable(e.getFeeItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                                calVo.setFeeProductCodeSet(Optional.ofNullable(e.getFeeProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                                Validate.isTrue(!(CollectionUtils.isEmpty(calVo.getItemCodeSet()) && CollectionUtils.isEmpty(calVo.getProductCodeSet())), "营销方案：%s-%s，品项编码和商品编码不能为空", e.getSchemeCode(), e.getSchemeDetailCode());
                                Validate.isTrue(!(CollectionUtils.isEmpty(calVo.getFeeItemCodeSet()) && CollectionUtils.isEmpty(calVo.getFeeProductCodeSet())), "营销方案：%s-%s，返利品项和返利产品不能为空", e.getSchemeCode(), e.getSchemeDetailCode());
                                calVo.setGiveNum(e.getGiveNum());
                                calVo.setOrgCode(e.getBelongDepartmentCode());

                                BigDecimal calAmount = formulaCalService.calResult(FormulaTypeEnum.WITHHOLDING.getCode(), e.getConditionFormula(), calVo);
                                BigDecimal reportAmount = formulaCalService.calResult(FormulaTypeEnum.MANAGEMENT_REPORT.getCode(), e.getConditionFormula(), calVo);

                                WithHolding entity = new WithHolding();
                                BeanUtils.copyProperties(baseCreateInfo, entity);
                                buildWithHolding(entity, e, "TPM", WithHoldingTypeEnum.NOT_AUDIT.getDictCode(), withHoldingYears);
                                entity.setWithHoldingAmount(calAmount.subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                                entity.setActualAmount(entity.getWithHoldingAmount());
                                entity.setWithHoldingReportAmount(reportAmount.subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                                entity.setActualReportAmount(entity.getWithHoldingReportAmount());
                                entity.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                                entities.add(entity);
                            }
                            //已结案未兑付、
                            //①电汇、账扣、票扣，按费用兑付统计已结案未上账兑付（根据TPM的未兑付状态判断）的差额计提（已结案金额-已上账金额）；
                            //②上账到客户的货补池费用，按DMS货补池的已结案未上账兑付（结案金额-该活动在DMS的已发货金额）的余额计提。
                        } else {
                            if (Arrays.asList(CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode(), CashMethodEnum.WIRE_TRANSFER.getDictCode()).contains(e.getCashType())) {
                                WithHolding entity = new WithHolding();
                                BeanUtils.copyProperties(baseCreateInfo, entity);
                                buildWithHolding(entity, e, "TPM", WithHoldingTypeEnum.NOT_CASH.getDictCode(), withHoldingYears);
                                entity.setWithHoldingAmount(e.getAuditAmount().subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                                entity.setActualAmount(entity.getWithHoldingAmount());
                                entity.setWithHoldingReportAmount(entity.getWithHoldingAmount());
                                entity.setActualReportAmount(entity.getWithHoldingAmount());
                                entity.setConfirmStatus(ConfirmStatusEnum.CONFIRMED.getCode());
                                entities.add(entity);
                            } else if (CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType())) {
                                WithHolding entity = new WithHolding();
                                BeanUtils.copyProperties(baseCreateInfo, entity);
                                buildWithHolding(entity, e, "TPM", WithHoldingTypeEnum.NOT_CASH.getDictCode(), withHoldingYears);
                                entity.setWithHoldingAmount(e.getAuditAmount().subtract(finalDeliveryAmountMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)).subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                                entity.setActualAmount(entity.getWithHoldingAmount());
                                entity.setWithHoldingReportAmount(entity.getWithHoldingAmount());
                                entity.setActualReportAmount(entity.getWithHoldingAmount());
                                entity.setConfirmStatus(ConfirmStatusEnum.CONFIRMED.getCode());
                                entities.add(entity);
                            }
                        }
                    });
                });
            }
        } else {
            log.warn("未找到【{}】，对应的活动", withHoldingYears);
        }

        //随单搭赠尾差
        //如果存在”TPM是否获取“字段为”否“的数据，不允许计提
        Validate.isTrue(Boolean.TRUE.equals(replenishmentPoolDetailVoService.checkObtainStatus(withHoldingYears)), "存在未获取的发货费用明细数据，不允许计提");
        List<ReplenishmentPoolDetailVo> replenishmentPoolDetailVos = new ArrayList<>();
        WithholdingSearchDto dto = new WithholdingSearchDto();
        dto.setOperationSearchStartTime(withHoldingYears + "-01 00:00:00");
        dto.setOperationSearchEndTime(DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(withHoldingYears + "-01", "yyyy-MM-dd")), "yyyy-MM-dd") + " 23:59:59");
        dto.setReplenishmentPoolTypes(Collections.singletonList(ReplenishmentPoolTypeEnum.TAIL_DIFF.getDictCode()));
        List<ReplenishmentPoolDetailVo> replenishmentVos1 = replenishmentPoolDetailVoService.noWithholding(dto);
        if (!CollectionUtils.isEmpty(replenishmentVos1)) {
            replenishmentPoolDetailVos.addAll(replenishmentVos1);
        }
        dto.setReplenishmentPoolTypes(Collections.singletonList(ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode()));
        dto.setOperationTypes(Lists.newArrayList(ReplenishmentPoolOperationType.POD_DIFF_ADD.getCode(),
                ReplenishmentPoolOperationType.POD_DIFF_REDUCE.getCode(),
                ReplenishmentPoolOperationType.ORDER_CLOSE_DETAIL.getCode()));
        List<ReplenishmentPoolDetailVo> replenishmentVos2 = replenishmentPoolDetailVoService.noWithholding(dto);
        if (!CollectionUtils.isEmpty(replenishmentVos2)) {
            replenishmentPoolDetailVos.addAll(replenishmentVos2);
        }
        if (!CollectionUtils.isEmpty(replenishmentPoolDetailVos)) {
            if (!CollectionUtils.isEmpty(replenishmentPoolDetailVos)) {
                Map<String, List<DictDataVo>> dictDataVoMap = dictDataVoService.findByDictTypeCodeList(Arrays.asList(WithHoldingConstant.PENNYAGE_ACTIVITY_SUBCATEGORIES, WithHoldingConstant.BUSINESS_FORMAT_FYC));
                Set<String> productCodes = replenishmentPoolDetailVos.stream().filter(e -> StringUtils.isNotBlank(e.getProductCode())).map(e -> e.getProductCode()).collect(Collectors.toSet());
                Set<String> customerCodes = replenishmentPoolDetailVos.stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode())).map(e -> e.getCustomerCode()).collect(Collectors.toSet());
                Set<String> poolDetailCodes = replenishmentPoolDetailVos.stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode())).map(e -> e.getPoolDetailCode()).collect(Collectors.toSet());
                Map<String, ProductVo> productMap = productVoService.findDetailsByIdsOrProductCodes(null, new ArrayList<>(productCodes))
                        .stream().collect(Collectors.toMap(e -> e.getProductCode(), Function.identity(), (a, b) -> a));
                Map<String, CustomerVo> customerMap = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodes))
                        .stream().collect(Collectors.toMap(e -> e.getCustomerCode(), Function.identity(), (a, b) -> a));
                List<DictDataVo> costTypeDetailDictDataVos = dictDataVoMap.get(WithHoldingConstant.PENNYAGE_ACTIVITY_SUBCATEGORIES);
                Map<String, DictDataVo> businessTypeMap = dictDataVoMap.get(WithHoldingConstant.BUSINESS_FORMAT_FYC).stream().collect(Collectors.toMap(e -> e.getDictCode(), Function.identity(), (a, b) -> a));
                CostTypeDetailVo costTypeDetail = costTypeDetailVoService.findByCode(costTypeDetailDictDataVos.get(0).getDictCode());
                CostTypeCategoryVo category = costTypeCategoryVoService.findByCode(costTypeDetail.getCategoryCode());
                Map<String, List<DeliveryReplenishmentPoolDetail>> deliverylMap = deliveryReplenishmentPoolDetailRepository.findByParentCodes(new ArrayList<>(poolDetailCodes), withHoldingYears)
                        .stream().filter(Objects::nonNull).collect(Collectors.groupingBy(e -> e.getParentCode()));
                for (ReplenishmentPoolDetailVo e : replenishmentPoolDetailVos) {
                    WithHolding entity = new WithHolding();
                    buildWithHolding(entity, "DMS", e.getOperationType(), withHoldingYears, e, costTypeDetailDictDataVos.get(0), productMap, customerMap, costTypeDetail, category, businessTypeMap);
                    //订单关闭、pod差异-增加、pod差异-减少的计提金额
                    BigDecimal price = e.getPrice() == null ? BigDecimal.ZERO : e.getPrice();
                    BigDecimal usableAmount = e.getUsableAmount() == null ? BigDecimal.ZERO : e.getUsableAmount();
                    BigDecimal usedAmount = e.getUsedAmount() == null ? BigDecimal.ZERO : e.getUsedAmount();
                    BigDecimal withholdingAmount = BigDecimal.ZERO;
                    if (ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode().equals(e.getReplenishmentPoolType())) {
                        //如果是货补类型
                        //可用金额/数量+已用金额/数量-发货费用报表只需要“业务日期”为计提年月的数据
                        withholdingAmount = usableAmount.add(usedAmount);
                    } else if (ReplenishmentPoolTypeEnum.TAIL_DIFF.getDictCode().equals(e.getReplenishmentPoolType())) {
                        //如果是尾差类型
                        //上账时单价×可用金额/数量+（上账时单价×已用金额/数量）-发货费用报表只需要“业务日期”为计提年月的数据
                        withholdingAmount = ((price.multiply(usableAmount)).add(price.multiply(usedAmount))).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    entity.setWithHoldingAmount(withholdingAmount);
                    if (deliverylMap != null && deliverylMap.containsKey(e.getPoolDetailCode())) {
                        BigDecimal reduce = deliverylMap.get(e.getPoolDetailCode()).stream().map(m -> m.getOperationAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                        entity.setWithHoldingAmount(entity.getWithHoldingAmount().subtract(reduce));
                    }
                    entity.setActualAmount(entity.getWithHoldingAmount());
                    entity.setWithHoldingReportAmount(entity.getWithHoldingAmount());
                    entity.setActualReportAmount(entity.getWithHoldingAmount());
                    entity.setConfirmStatus(ConfirmStatusEnum.CONFIRMED.getCode());
                    entities.add(entity);
                }
                List<String> poolDetailCodeList = replenishmentPoolDetailVos.stream().map(e -> e.getPoolDetailCode()).collect(Collectors.toList());
                ReplenishmentPoolDetailDto poolDetailDto = new ReplenishmentPoolDetailDto();
                poolDetailDto.setPoolDetailCodeList(poolDetailCodeList);
                poolDetailDto.setWithholdingStatus(BooleanEnum.TRUE.getCapital());
                replenishmentPoolDetailVoService.updateWithholdingStatusByDetailCodes(poolDetailDto);
            }
        }

        if (!CollectionUtils.isEmpty(entities)) {
            //原计提活动是否计提设置为否
            List<String> schemeDetailCodes = withHoldingRepository.deleteByYearMonthLy(withHoldingYears);
            marketingPlanCaseService.updateWithHoldingStatusBySchemeDetailCodes(schemeDetailCodes, BooleanEnum.FALSE.getCapital());
            List<String> codeList = generateCodeService.generateCodeYearMonth(WithHoldingConstant.PREFIX_CODE, entities.size());
            //排除重复的计提
            List<String> businessCodes = entities.stream().map(e -> e.getBusinessCode()).collect(Collectors.toList());
            Set<String> businessCodeSet = withHoldingRepository.findByBusinessCodes(businessCodes).stream().filter(Objects::nonNull).map(e -> e.getBusinessCode()).collect(Collectors.toSet());
            List<WithHolding> entitiesNew = entities.stream().filter(e -> !businessCodeSet.contains(e.getBusinessCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(entitiesNew)) {
                for (int i = 0; i < entitiesNew.size(); i++) {
                    entitiesNew.get(i).setId(UuidCrmUtil.general());
                    entitiesNew.get(i).setWithHoldingCode(codeList.get(i));
                    entitiesNew.get(i).setBeThisFee(BooleanEnum.TRUE.getCapital());
                }
                withHoldingRepository.saveBatch(entitiesNew);
                //新计提活动是否计提设置为是
                List<String> schemeDetailCodesNew = entitiesNew.stream().filter(e -> StringUtils.isNotBlank(e.getActivitiesDetailCode()))
                        .filter(Objects::nonNull).map(e -> e.getActivitiesDetailCode()).collect(Collectors.toList());
                marketingPlanCaseService.updateWithHoldingStatusBySchemeDetailCodes(schemeDetailCodesNew, BooleanEnum.TRUE.getCapital());
            }
        }
    }


    @Override
    public void syncHandleAuto(WithHoldingDto withHolding) {
        loginUserService.refreshAuthentication(null);
        String withHoldingYears = withHolding.getYearMonthLy();
        Validate.notBlank(withHoldingYears, "预提年月不能为空");
        //查找活动明细
        MarketingPlanCaseVo planCaseVo = new MarketingPlanCaseVo();
        planCaseVo.setYears(withHoldingYears);
        planCaseVo.setWithholding(BooleanEnum.TRUE.getCapital());
        planCaseVo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        planCaseVo.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());
        Page<MarketingPlanCaseVo> caseVoPage = findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseVo);
        if (caseVoPage.getTotal() > 0L) {
            withholdingGenerateComponent.runSchemeDetailGenerateWithholding(caseVoPage.getRecords(), withHoldingYears);
        }
        //随单搭赠尾差
        //如果存在”TPM是否获取“字段为”否“的数据，不允许计提
        Validate.isTrue(Boolean.TRUE.equals(replenishmentPoolDetailVoService.checkObtainStatus(withHoldingYears)), "存在未获取的发货费用明细数据，不允许计提");
        withholdingGenerateComponent.runReplenishmentGenerateWithholding(withHoldingYears);
    }

    /**
     * 定时任务计提
     */
    @Override
    public void taskAuto() {
        //先驳回，后计提
        oaOperateService.recoverAll();
        String ym = DateUtil.format(DateUtil.lastMonth(), "yyyy-MM");
        WithHoldingDto withHolding = new WithHoldingDto();
        withHolding.setYearMonthLy(ym);
        handleAuto(withHolding);
    }

    /**
     * 随单搭赠构建费用计提
     *
     * @param entity
     * @param
     */
    private void buildWithHolding(WithHolding entity, String source, String withHoldingType, String yearMonthLy, ReplenishmentPoolDetailVo vo,
                                  DictDataVo dictDataVo, Map<String, ProductVo> productMap, Map<String, CustomerVo> customerMap, CostTypeDetailVo costTypeDetail,
                                  CostTypeCategoryVo category, Map<String, DictDataVo> businessTypeMap) {

        CustomerVo customerVo = customerMap.get(vo.getCustomerCode());
        entity.setCustomerCode(vo.getCustomerCode());
        entity.setCustomerName(vo.getCustomerName());
        entity.setErpCode(customerVo.getErpCode());
        entity.setCompanyCode(customerVo.getCompanyCode());
        entity.setCostCenterCode(customerVo.getCostCenterCode());
        entity.setCostCenterName(customerVo.getCostCenterName());
        entity.setBearDepartmentCode(customerVo.getOrgList().get(0).getOrgCode());
        entity.setBearDepartmentName(customerVo.getOrgList().get(0).getOrgName());
        entity.setBelongDepartmentCode(customerVo.getOrgList().get(0).getOrgCode());
        entity.setBelongDepartmentName(customerVo.getOrgList().get(0).getOrgName());
        //如果有产品就找对应的产品品项，如果没有就通过数据字典去查业态对应的品项
        if (StringUtils.isNotBlank(vo.getProductCode())) {
            ProductVo productVo = productMap.get(vo.getProductCode());
            entity.setItemCode(productVo.getProductPhaseCode());
            entity.setItemName(productVo.getProductPhaseName());
        } else {
            DictDataVo businessType = businessTypeMap.get(vo.getBusinessType());
            entity.setItemCode(businessType.getExt1());
            entity.setItemName(businessType.getExt2());
        }
        entity.setBusinessCode(vo.getPoolDetailCode());
        entity.setYears(yearMonthLy);
        entity.setCostTypeDetailCode(dictDataVo.getDictCode());
        entity.setCostTypeDetailName(dictDataVo.getDictValue());
        entity.setPayBy(CashMethodEnum.REPLENISHMENT.getDictCode());
        entity.setCostTypeDetailCode(costTypeDetail.getDetailCode());
        entity.setCostTypeDetailName(costTypeDetail.getDetailName());
        entity.setCostTypeCategoryCode(category.getCategoryCode());
        entity.setCostTypeCategoryName(category.getCategoryName());
        entity.setBudgetSubjectsCode(category.getBudgetSubjectsCode());
        entity.setBudgetSubjectsName(category.getBudgetSubjectsName());

        buildWithHolding(entity, null, source, withHoldingType, yearMonthLy);
    }

    /**
     * 构建费用计提
     *
     * @param entity
     * @param vo
     */
    private void buildWithHolding(WithHolding entity, MarketingPlanCaseVo vo, String source, String withHoldingType, String yearMonthLy) {
        entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        entity.setTenantCode(TenantUtils.getTenantCode());
        entity.setStatus(ProcessStatusEnum.PREPARE.getDictCode());
        entity.setWithHoldingSource(source);

        entity.setYearMonthLy(yearMonthLy);
        if (vo != null) {
            entity.setId(vo.getSchemeDetailCode());
            entity.setYears(vo.getStartDate().substring(0, 7));
            entity.setCompanyCode(vo.getCompanyCode());
            entity.setActivitiesCode(vo.getSchemeCode());
            entity.setActivitiesName(vo.getSchemeName());
            entity.setActivitiesDetailCode(vo.getSchemeDetailCode());
            entity.setCostTypeCategoryCode(vo.getCategoryCode());
            entity.setCostTypeCategoryName(vo.getCategoryName());
            entity.setCostTypeDetailCode(vo.getDetailCode());
            entity.setCostTypeDetailName(vo.getDetailName());
            entity.setBearDepartmentCode(vo.getBearDepartmentCode());
            entity.setBearDepartmentName(vo.getBearDepartmentName());
            entity.setBelongDepartmentCode(vo.getBelongDepartmentCode());
            entity.setBelongDepartmentName(vo.getBelongDepartmentName());
            entity.setCostCenterCode(vo.getCostCenterCode());
            entity.setCostCenterName(vo.getCostCenterName());
            entity.setCustomerCode(vo.getCustomerCode());
            entity.setCustomerName(vo.getCustomerName());
            entity.setApplyAmount(vo.getApplyAmount());
            entity.setBudgetSubjectsCode(vo.getBudgetSubjectCode());
            entity.setBudgetSubjectsName(vo.getBudgetSubjectName());
            entity.setPayBy(vo.getCashType());
            entity.setErpCode(vo.getErpCode());
            entity.setProductGroupCode(vo.getProductGroupCode());
            entity.setChannelCode(vo.getChannelCode());
            entity.setPositionCode(vo.getPositionCode());
            entity.setBusinessCode(vo.getSchemeDetailCode());
            entity.setItemCode(vo.getItemCode());
            entity.setItemName(vo.getItemName());
            entity.setTerminalCode(vo.getTerminalCode());
            entity.setTerminalName(vo.getTerminalName());
            entity.setActDesc(vo.getActDesc());
            entity.setSchemeCreateName(vo.getCreateName());
        }

        entity.setWithHoldingType(withHoldingType);
    }

    /**
     * 通过活动编码集合查询
     *
     * @param codes
     * @return
     */
    @Override
    public List<WithHolding> findByActivitiesDetailCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>(0);
        }
        return this.withHoldingRepository.findByActivitiesDetailCodes(codes);
    }


    /**
     * 调整
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void adjust(WithHoldingDto dto, ConfirmStatusEnum confirmStatusEnum) {
        Validate.notNull(dto, "对象信息不能为空！");
        Validate.notBlank(dto.getWithHoldingCode(), "计提编码，不能为空！");
        Validate.isTrue(dto.getWithHoldingReportReduceAmount() != null, "计提实际金额，不能都为空！");
        WithHolding entity = withHoldingRepository.findByCode(dto.getWithHoldingCode());
        WithHoldingVo oldVo = nebulaToolkitService.copyObjectByWhiteList(entity, WithHoldingVo.class, HashSet.class, ArrayList.class);
        Validate.isTrue(WithHoldingTypeEnum.NOT_AUDIT.getDictCode().equals(entity.getWithHoldingType()), "仅允许调整“已申请未结案”的计提数据");
        Validate.isTrue(ConfirmStatusEnum.UNCONFIRMED.getCode().equals(entity.getConfirmStatus()), "仅允许调整“未确认”的计提数据");
        Validate.isTrue(entity.getActualReportAmount() != null && dto.getWithHoldingReportReduceAmount().compareTo(entity.getWithHoldingReportAmount()) <= 0, "计提实际金额，必须小于计提金额");
        MarketingPlanCaseVo planCaseDto = new MarketingPlanCaseVo();
        planCaseDto.setChangeFlag(BooleanEnum.FALSE.getCapital());
        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesApproved(Lists.newArrayList(entity.getBusinessCode()));

        BigDecimal withHoldingReportReduceAmount = dto.getWithHoldingReportReduceAmount();
        if (!CollectionUtils.isEmpty(feeCashDetailVos)) {
            BigDecimal thisCashAmount = feeCashDetailVos.stream().map(x -> x.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            withHoldingReportReduceAmount = withHoldingReportReduceAmount.add(thisCashAmount);
        }
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditService.findListBySchemeDetailCodesApproved(Lists.newArrayList(entity.getBusinessCode()));
        BigDecimal auditAmount = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(auditDetailVos)) {
            auditAmount = auditDetailVos.get(0).getAuditAmount();
        }
//        Page<MarketingPlanCaseVo> caseVoPage = findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseDto);
        MarketingPlanCaseVo caseVo = marketingPlanCaseService.findByCaseCode(entity.getBusinessCode());
        if (auditDetailVos.size() > 0) {
            //调整后金额不允许小于当前活动的已结案金额
            Validate.isTrue(withHoldingReportReduceAmount.compareTo(auditAmount) >= 0, "活动%s已结案%s元，计提的费用不允许小于结案的金额，请调整“计提实际金额”！", caseVo.getSchemeDetailCode(), caseVo.getAuditAmount());
        }
        if (ObjectUtils.isNotEmpty(caseVo) && MarketingPlanCaseTypeEnum.back.getCode().equals(caseVo.getCaseType())) {
            entity.setActualReportAmount(dto.getWithHoldingReportReduceAmount());
            entity.setWithHoldingReportReduceAmount(entity.getWithHoldingReportAmount().subtract(entity.getActualReportAmount()));
        } else {
            entity.setActualAmount(dto.getWithHoldingReportReduceAmount());
            entity.setReduceAmount(entity.getWithHoldingAmount().subtract(entity.getActualAmount()));
            entity.setActualReportAmount(dto.getWithHoldingReportReduceAmount());
            entity.setWithHoldingReportReduceAmount(entity.getWithHoldingReportAmount().subtract(entity.getActualReportAmount()));
        }
        entity.setBeAdjust(BooleanEnum.TRUE.getCapital());
        entity.setConfirmStatus(confirmStatusEnum.getCode());
        List<WithHoldingVo> withHoldingVos = Lists.newArrayList();
        WithHoldingVo withHoldingVo = nebulaToolkitService.copyObjectByWhiteList(entity, WithHoldingVo.class, HashSet.class, ArrayList.class);
        withHoldingVos.add(withHoldingVo);
        recaluateNoTaxActualReportAmount(withHoldingVos);
        entity.setNoTaxActualReportAmount(withHoldingVo.getNoTaxActualReportAmount());
        withHoldingRepository.saveOrUpdate(entity);

        //更新业务日志
        SerializableBiConsumer<WithHoldingLogEventListener, WithHoldingLogEventDto> onUpdate =
                WithHoldingLogEventListener::onUpdate;
        WithHoldingLogEventDto eventDto = new WithHoldingLogEventDto();
        WithHoldingVo newVo = nebulaToolkitService.copyObjectByWhiteList(entity, WithHoldingVo.class, HashSet.class, ArrayList.class);
        eventDto.setNewest(newVo);
        eventDto.setOriginal(oldVo);
        this.nebulaNetEventClient.publish(eventDto, WithHoldingLogEventListener.class, onUpdate);
    }

    /**
     * 批量创建
     *
     * @param withHoldings
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importBatch(List<WithHoldingImportVo> withHoldings) {
        Collection<WithHolding> entities = nebulaToolkitService.copyCollectionByWhiteList(withHoldings, WithHoldingImportVo.class, WithHolding.class, LinkedHashSet.class, ArrayList.class);
        List<String> codeList = this.generateCodeService.generateCodeYearMonth(WithHoldingConstant.PREFIX_CODE, entities.size());
        AtomicInteger index = new AtomicInteger(0);
        entities.forEach(e -> {
            e.setWithHoldingCode(codeList.get(index.get()));
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setTenantCode(TenantUtils.getTenantCode());
            e.setStatus(ProcessStatusEnum.PREPARE.getDictCode());
            e.setWithHoldingSource("TMP");
            e.setWithHoldingType(WithHoldingTypeEnum.HANDLE.getDictCode());
            index.getAndAdd(1);
        });

        withHoldingRepository.saveBatch(entities);
    }

    /**
     * 批量修改
     *
     * @param withHoldings
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importUpdate(List<WithHoldingVo> withHoldings) {
        List<WithHoldingVo> oldVos = withHoldingRepository.findByBusinessCodesVo(withHoldings.stream().map(WithHoldingVo::getBusinessCode).distinct().collect(Collectors.toList()));
        Collection<WithHolding> entities = nebulaToolkitService.copyCollectionByWhiteList(withHoldings, WithHoldingVo.class, WithHolding.class, LinkedHashSet.class, ArrayList.class);
        withHoldingRepository.saveOrUpdateBatch(entities);

        //更新业务日志
        SerializableBiConsumer<WithHoldingLogEventListener, WithHoldingLogEventDto> onUpdateBatch =
                WithHoldingLogEventListener::onUpdateBatch;
        WithHoldingLogEventDto eventDto = new WithHoldingLogEventDto();
        eventDto.setNewestList(withHoldings);
        eventDto.setOriginalList(oldVos);
        this.nebulaNetEventClient.publish(eventDto, WithHoldingLogEventListener.class, onUpdateBatch);
    }

    /**
     * 根据编码查询
     *
     * @param codes
     * @return
     */
    @Override
    public List<WithHoldingVo> findByCodes(List<String> codes) {
        return withHoldingRepository.findByCodes(codes);
    }

    /**
     * 根据活动明细编码查询计提金额
     *
     * @param codes
     * @return
     */
    @Override
    public Map<String, BigDecimal> findBySchemeDetailCodes(List<String> codes) {
        List<WithHoldingVo> withHoldingVoList = withHoldingRepository.findBySchemeDetailCodes(codes);
        return withHoldingVoList.stream().collect(Collectors.toMap(WithHoldingVo::getActivitiesDetailCode, WithHoldingVo::getWithHoldingAmount, (a, b) -> a));
    }

    @Override
    public List<WithHoldingVo> findListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        List<WithHoldingVo> withHoldingVoList = withHoldingRepository.findBySchemeDetailCodes(schemeDetailCodes);
        return withHoldingVoList;
    }

    @Override
    public Map<String, BigDecimal> findProcessPassBySchemeDetailCodes(List<String> codes) {
        List<WithHoldingVo> withHoldingVoList = withHoldingRepository.findProcessPassBySchemeDetailCodes(codes);
        return withHoldingVoList.stream().collect(Collectors.toMap(WithHoldingVo::getActivitiesDetailCode, WithHoldingVo::getActualAmount, (a, b) -> a));
    }

    /**
     * 创建验证
     *
     * @param withHolding
     */
    private void createValidate(WithHolding withHolding) {
        Validate.notNull(withHolding, "新增时，对象信息不能为空！");
        withHolding.setId(null);
        withHolding.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        withHolding.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        withHolding.setTenantCode(TenantUtils.getTenantCode());
        Validate.notNull(withHolding.getTenantCode(), "新增数据时，租户编号不能为空！");
        Validate.notNull(withHolding.getActivitiesCode(), "新增数据时，活动编号 不能为空！");
        Validate.notNull(withHolding.getApplyAmount(), "新增数据时，申请金额 不能为空！");
        Validate.notNull(withHolding.getBudgetSubjectsCode(), "新增数据时，预算科目编码 不能为空！");
        Validate.notNull(withHolding.getCostTypeCategoryCode(), "新增数据时，活动大类编码 不能为空！");
        Validate.notNull(withHolding.getCostTypeDetailCode(), "新增数据时，活动细类编码 不能为空！");
        Validate.notNull(withHolding.getWithHoldingAmount(), "新增数据时，预提金额 不能为空！");
    }

    /**
     * 修改验证
     *
     * @param withHolding
     */
    private void updateValidate(WithHolding withHolding) {
        Validate.notNull(withHolding, "修改时，对象信息不能为空！");
        Validate.notBlank(withHolding.getId(), "修改数据时，不能为空！");
        Validate.notBlank(withHolding.getTenantCode(), "修改数据时，租户编号不能为空！");
        Validate.notBlank(withHolding.getActivitiesCode(), "修改数据时，活动编号 不能为空！");
        Validate.notNull(withHolding.getApplyAmount(), "修改数据时，申请金额 不能为空！");
        Validate.notBlank(withHolding.getBudgetSubjectsCode(), "修改数据时，预算科目编码 不能为空！");
        Validate.notBlank(withHolding.getCostTypeCategoryCode(), "修改数据时，活动大类编码 不能为空！");
        Validate.notBlank(withHolding.getCostTypeDetailCode(), "修改数据时，活动细类编码 不能为空！");
        Validate.notNull(withHolding.getWithHoldingAmount(), "修改数据时，预提金额 不能为空！");
        Validate.notBlank(withHolding.getWithHoldingCode(), "修改数据时，预提编号 不能为空！");
        this.amountValidate(withHolding);
    }

    /**
     * 校验预提金额
     *
     * @param withHolding
     */
    private void amountValidate(WithHolding withHolding) {
        BigDecimal withHoldingAmount = withHolding.getWithHoldingAmount();
        BigDecimal applyAmount = withHolding.getApplyAmount();
        String activitiesDetailCode = withHolding.getActivitiesDetailCode();
        List<Account> byActivitiesDetailCode = accountService.findByActivitiesDetailCode(Lists.newArrayList(activitiesDetailCode));
        if (CollectionUtils.isEmpty(byActivitiesDetailCode)) {
            Validate.isTrue(applyAmount.compareTo(withHoldingAmount) >= 0, "预提金额超出申请金额");
            return;
        }
        BigDecimal amount = byActivitiesDetailCode.stream().filter(k -> Objects.nonNull(k.getAmount()))
                .map(Account::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal availableAmount = applyAmount.subtract(amount);
        Validate.isTrue(availableAmount.compareTo(withHoldingAmount) >= 0, "预提金额超出申请金额-上账金额");
    }

    /**
     * 校验预提金额 集合
     *
     * @param withHolding
     */
    private void amountValidate(List<WithHolding> withHolding) {
        List<String> codes = withHolding.stream().map(WithHolding::getActivitiesDetailCode).collect(Collectors.toList());
        List<Account> accountList = accountService.findByActivitiesDetailCode(codes);
        if (CollectionUtils.isEmpty(accountList)) {
            //比较预提金额与申请金额
            for (WithHolding entity : withHolding) {
                BigDecimal withHoldingAmount = entity.getWithHoldingAmount();
                BigDecimal applyAmount = entity.getApplyAmount();
                Validate.isTrue(applyAmount.compareTo(withHoldingAmount) >= 0, "预提金额超出申请金额");
            }
            return;
        }
        Map<String, List<Account>> map = accountList.stream().collect(Collectors.groupingBy(Account::getActivitiesDetailCode));
        for (WithHolding entity : withHolding) {
            BigDecimal withHoldingAmount = entity.getWithHoldingAmount();
            BigDecimal applyAmount = entity.getApplyAmount();
            List<Account> list = map.get(entity.getActivitiesDetailCode());
            if (CollectionUtils.isEmpty(list)) {
                //比较预提金额与申请金额
                Validate.isTrue(applyAmount.compareTo(withHoldingAmount) >= 0, "预提金额超出申请金额");
            } else {
                BigDecimal reduce = list.stream().map(Account::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal availableAmount = applyAmount.subtract(reduce);
                Validate.isTrue(availableAmount.compareTo(withHoldingAmount) >= 0, "预提金额超出申请金额-上账金额");
            }
        }
    }

    /**
     * 费控凭证回传
     *
     * @param dtoList
     */
    @Override
    public void hecVoucherCallback(List<HecCallbackDto> dtoList) {
        Validate.notEmpty(dtoList, "回传参数不能为空");
        dtoList.forEach(dto -> {
            Validate.notBlank(dto.getBusinessCode(), "单据编码不能为空");
            Validate.notBlank(dto.getOrderCode(), "凭证编码不能为空");
        });

        Map<String, HecCallbackDto> dtoMap = dtoList.stream()
                .collect(Collectors.toMap(HecCallbackDto::getBusinessCode, v -> v, (n, o) -> n));
        withHoldingRepository.hecVoucherCallback(new ArrayList<>(dtoMap.values()));
    }

    /**
     * 根据活动明细编码查询审批通过的计提金额
     *
     * @param codes
     * @return
     */
    @Override
    public List<WithHoldingVo> findBySchemeDetailCodesPass(List<String> codes) {
        List<WithHoldingVo> withHoldingVos = withHoldingRepository.findBySchemeDetailCodesPass(codes);
        if (CollectionUtils.isEmpty(withHoldingVos)) {
            return new ArrayList<>();
        }
        return withHoldingVos;
    }

    /**
     * 费用计提合计
     *
     * @param params
     * @return
     */
    @Override
    public WithHoldingVo findTotalByConditions(LinkedHashMap<String, Object> params) {
        WithHoldingDto dto = new WithHoldingDto();
        if (!CollectionUtils.isEmpty(params)) {
            Map<String, Object> convertEuropaParam = EuropaParamsTools.convertEuropaParam(params);
            dto = JSON.parseObject(JSON.toJSONString(convertEuropaParam), WithHoldingDto.class);
            List<String> years = params.keySet().stream().filter(e -> e.startsWith("years")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(years) && years.size() == 2) {
                dto.setYears(null);
                dto.setYearsStart((String) params.get(years.get(0)));
                dto.setYearsEnd((String) params.get(years.get(1)));
            }

        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        return this.withHoldingRepository.findTotalByConditions(dto);
    }

    /**
     * 分页获取缓存数据
     *
     * @param pageable 分页参数
     * @param cacheKey 缓存key
     * @return 分页缓存数据
     */
    @Override
    public Page<WithHoldingVo> findCachePageList(Pageable pageable, WithHoldingDto dto, String cacheKey) {
        String redisCacheIdKey = pageCacheHelper.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = pageCacheHelper.getRedisCacheDataKey(cacheKey);
        String redisCacheInitKey = pageCacheHelper.getRedisCacheInitKey(cacheKey);
        Page<WithHoldingVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        page.setTotal(0);
        page.setRecords(Lists.newArrayList());

        a:
        if (redisService.hasKey(redisCacheIdKey)) {
            //redis里面有的话直接从redis里面取
            Long total = redisService.lSize(redisCacheIdKey);
            page.setTotal(total);
            List<Object> idList = redisService.lRange(redisCacheIdKey, 0, -1);
            if (!CollectionUtils.isEmpty(idList)) {
                List<WithHoldingDto> dtoList = redisTemplate.opsForHash().multiGet(redisCacheDataKey, idList);

                dtoList = searchCachePageList(dto, dtoList);

                //放到缓存里面
                page.setTotal(dtoList.size());
                long start = page.offset();
                if (page.getTotal() > start) {
                    long end = page.offset() + page.getSize();
                    if (page.getTotal() < end) {
                        end = page.getTotal();
                    }
                    List<WithHoldingDto> recordDtoList = dtoList.subList((int) page.offset(), (int) end);
                    List<WithHoldingVo> voList = pageCacheHelper.dtoListToVoList(recordDtoList);
                    page.setRecords(voList);
                }
            }
        } else if (!redisService.hasKey(redisCacheInitKey) && null != dto) {
            //标记为已初始化，不重复初始化
            redisService.set(redisCacheInitKey, BooleanEnum.TRUE, pageCacheHelper.getExpireTime());
            //放到try catch里面 是为了以防分页从数据库查询报错了 初始化状态被确认 修正报错过后查询为空的情况
            try {
                //redis里面没有
                List<WithHoldingDto> dtoList = pageCacheHelper.findDtoListFromRepository(dto, cacheKey);

                if (CollectionUtils.isEmpty(dtoList)) {
                    redisService.del(redisCacheInitKey);
                    break a;
                }

                if (pageCacheHelper.initToCacheFromRepository()) {
                    pageCacheHelper.putCache(cacheKey, dtoList);
                }

                //放到缓存里面
                page.setTotal(dtoList.size());
                long start = page.offset();
                if (page.getTotal() > start) {
                    long end = page.offset() + page.getSize();
                    if (page.getTotal() < end) {
                        end = page.getTotal();
                    }
                    List<WithHoldingDto> recordDtoList = dtoList.subList((int) page.offset(), (int) end);
                    List<WithHoldingVo> voList = pageCacheHelper.dtoListToVoList(recordDtoList);
                    page.setRecords(voList);
                }
            } catch (Exception e) {
                redisService.del(redisCacheInitKey);
            }
        }
        //更新下VO里面的字段值
        pageCacheHelper.fillVoListProperties(page.getRecords());
        return page;
    }

    /**
     * 缓存条件查询
     *
     * @param dto
     * @param dataList
     * @return
     */
    List<WithHoldingDto> searchCachePageList(WithHoldingDto dto, List<WithHoldingDto> dataList) {
        List<WithHoldingDto> returnDtoList = new ArrayList<>();

        for (WithHoldingDto data : dataList) {
            if (StringUtils.isNotBlank(dto.getWithHoldingCode()) && !dto.getWithHoldingCode().equals(data.getWithHoldingCode())) {
                continue;
            }
            if (StringUtils.isNotBlank(dto.getCompanyCode()) && !dto.getCompanyCode().equals(data.getCompanyCode())) {
                continue;
            }
            if (StringUtils.isNotBlank(dto.getBusinessCode()) && !dto.getBusinessCode().equals(data.getBusinessCode())) {
                continue;
            }
            if (StringUtils.isNotBlank(dto.getActivitiesCode()) && !dto.getActivitiesCode().equals(data.getActivitiesCode())) {
                continue;
            }
            if (StringUtils.isNotBlank(dto.getCostTypeDetailName()) && (StringUtils.isBlank(data.getCostTypeDetailName()) ||
                    (StringUtils.isNotBlank(data.getCostTypeDetailName()) && !data.getCostTypeDetailName().contains(dto.getCostTypeDetailName())))) {
                continue;
            }
            if (StringUtils.isNotBlank(dto.getCostTypeCategoryName()) && (StringUtils.isBlank(data.getCostTypeCategoryName()) ||
                    (StringUtils.isNotBlank(data.getCostTypeCategoryName()) && !data.getCostTypeCategoryName().contains(dto.getCostTypeCategoryName())))) {
                continue;
            }
            if (StringUtils.isNotBlank(dto.getBelongDepartmentName()) && (StringUtils.isBlank(data.getBelongDepartmentName()) ||
                    (StringUtils.isNotBlank(data.getBelongDepartmentName()) && !data.getBelongDepartmentName().contains(dto.getBelongDepartmentName())))) {
                continue;
            }
            if (StringUtils.isNotBlank(dto.getCostCenterName()) && (StringUtils.isBlank(data.getCostCenterName()) ||
                    (StringUtils.isNotBlank(data.getCostCenterName()) && !data.getCostCenterName().contains(dto.getCostCenterName())))) {
                continue;
            }
            if (StringUtils.isNotBlank(dto.getCustomerName()) && (StringUtils.isBlank(data.getCustomerName()) ||
                    (StringUtils.isNotBlank(data.getCustomerName()) && !data.getCustomerName().contains(dto.getCustomerName())))) {
                continue;
            }
            if (StringUtils.isNotBlank(dto.getItemName()) && (StringUtils.isBlank(data.getItemName()) ||
                    (StringUtils.isNotBlank(data.getItemName()) && !data.getItemName().contains(dto.getItemName())))) {
                continue;
            }
            returnDtoList.add(data);
        }

        return returnDtoList;
    }

    /**
     * 根据计提编号确认
     *
     * @param codeList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirm(List<String> codeList, ConfirmStatusEnum confirmStatusEnum) {
        Validate.notEmpty(codeList, "计提编号不能为空");
        List<WithHoldingVo> voList = withHoldingRepository.findByCodes(codeList);
        Validate.notEmpty(voList, "计提数据未找到");
        voList.forEach(item -> Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】数据能确认或取消确认"));

        boolean lock = withHoldingLockVoService.lock(codeList, TimeUnit.SECONDS, 30);
        Validate.isTrue(lock, "操作预算失败，获取操作锁失败！");
        try {
            voList.forEach(e -> e.setConfirmStatus(confirmStatusEnum.getCode()));
            // 重新计算noTaxActualReportAmount
            recaluateNoTaxActualReportAmount(voList);
            Collection<WithHolding> withHoldings = nebulaToolkitService.copyCollectionByWhiteList(voList, WithHoldingVo.class, WithHolding.class, LinkedHashSet.class, ArrayList.class);
            withHoldingRepository.saveOrUpdateBatch(withHoldings);
        } finally {
            withHoldingLockVoService.unLock(codeList);
        }
    }

    private void recaluateNoTaxActualReportAmount(List<WithHoldingVo> voList) {
        Map<String, ProductPhaseVo> productPhaseVoMap = productPhaseVoMap(voList.stream().map(WithHoldingVo::getItemCode).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        voList.forEach(vo -> {
            String payBy = vo.getPayBy();
            // 兑付方式为账扣
            if (StringUtils.isNotEmpty(payBy)) {
                if (payBy.equals(CashMethodEnum.WIRE_TRANSFER.getDictCode()) || payBy.equals(CashMethodEnum.DEDUCTIONS.getDictCode())) {
                    vo.setNoTaxActualReportAmount(vo.getActualReportAmount().divide(BigDecimal.ONE.add(BigDecimal.valueOf(0.06)), 2, BigDecimal.ROUND_HALF_UP));
                } else if (payBy.equals(CashMethodEnum.TICKET_BUCKLE.getDictCode()) || payBy.equals(CashMethodEnum.REPLENISHMENT.getDictCode())) {
                    String itemCode = vo.getItemCode();
                    if (com.google.common.collect.Lists.newArrayList("BC1001", "BC1002").contains(itemCode)) {
                        vo.setNoTaxActualReportAmount(vo.getActualReportAmount().divide(BigDecimal.ONE.add(BigDecimal.valueOf(0.1)), 2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        ProductPhaseVo productPhaseVo = productPhaseVoMap.get(itemCode);
                        if (Objects.isNull(productPhaseVo) || Objects.isNull(productPhaseVo.getTaxRate())) {
                            throw new RuntimeException(vo.getWithHoldingCode()+ "对应" + itemCode + "未匹配到对应品项或对应品项未维护税率");
                        } else {
                            vo.setNoTaxActualReportAmount(vo.getActualAmount().divide(BigDecimal.ONE.add(productPhaseVo.getTaxRate()), 2, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }
            }
        });

    }

    /**
     * 品项信息
     *
     * @return
     */
    private Map<String, ProductPhaseVo> productPhaseVoMap(List<String> itemCodes) {
        Map<String, ProductPhaseVo> productPhaseVoMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemCodes)) {
            List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
            productPhaseVoMap.putAll(productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> v, (v1, v2) -> v1)));
        }
        return productPhaseVoMap;
    }

    /**
     * 查询活动明细报表
     *
     * @param pageable
     * @param vo
     * @return
     */
    public Page<MarketingPlanCaseVo> findMarketingPlanCaseReportList(Pageable pageable, MarketingPlanCaseVo vo) {
        //不查询变更的
        vo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getBelongDepartmentCodes())) {
            Validate.isTrue(vo.getBelongDepartmentCodes().size() < 500, "费用使用部门查询个数不能超过500");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getBearDepartmentCodes())) {
            Validate.isTrue(vo.getBearDepartmentCodes().size() < 500, "费用承担部门查询个数不能超过500");
        }

        if (ObjectUtils.isNotEmpty(vo.getRegionName())) {
            Set<String> orgCodeSet = orgVoService.findByOrgQueryDto(new OrgQueryDto() {{
                this.setOrgName(vo.getRegionName());
                this.setOrgType(OrgTypeEnum.DIVISION.getDictCode());
            }});
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(orgCodeSet)) {
                return null;
            }
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCodes(Lists.newArrayList(orgCodeSet));
            List<String> orgCodeList = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            vo.setOrgCodeList(orgCodeList);
        }
        Page<MarketingPlanCaseVo> data = marketingPlanCaseService.findMarketingPlanCaseReportList(pageable, vo);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(data.getRecords())) {
            return data;
        }
        List<MarketingPlanCaseVo> marketingPlanCaseVoList = data.getRecords();

        //查询使用部门对应的一级组织
        List<String> belongDepartmentCodes = marketingPlanCaseVoList.stream().map(MarketingPlanCaseVo::getBelongDepartmentCode).collect(Collectors.toList());
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllParentByOrgCodesMap(belongDepartmentCodes);

        List<String> marketingSchemeDetailCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        List<String> cashSchemeDetailCodes = data.getRecords().stream().filter(e -> StringUtils.equalsAny(e.getCashType(), CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode()))
                .filter(Objects::nonNull).map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        List<String> replenishmentSchemeDetailCodes = data.getRecords().stream().filter(e -> StringUtils.equalsAny(e.getCashType(), CashMethodEnum.REPLENISHMENT.getDictCode()))
                .filter(Objects::nonNull).map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());

        //1、“结案状态”取值逻辑：查询活动明细关联的结案明细（取审批通过状态的结案明细数据）
        //
        //如果关联的结案明细有完全结案的单据，则结案状态=完全结案；
        //如果关联的结案明细都未完全结案，则结案状态=部分结案；
        //如果未查询到关联的结案明细，则结案状态=未结案；
        //
        //2、“兑付状态”取值逻辑：查询活动明细关联的费用兑付明细（取审批通过状态且兑付类型=费用兑付的费用兑付明细数据）
        //（1）兑付方式=电汇、账扣、票扣的活动明细：查询活动明细关联的费用兑付明细（取审批通过状态且兑付类型=费用兑付的费用兑付明细数据）
        //如果活动明细的结案状态≠完全结案，且未查询到关联的费用兑付明细，则兑付状态=未兑付；
        //如果活动明细的结案状态≠完全结案，且可查询到关联的费用兑付明细，则兑付状态=部分兑付；
        //如果活动明细的结案状态＝完全结案，且关联的每条结案明细均有关联的完全兑付的费用兑付明细，或活动明细的结案金额≤活动明细关联的审批通过的费用兑付明细的本次兑付金额汇总，则兑付状态=完全兑付；
        //（2）兑付方式=货补的活动明细：通过“方案明细编码”和“操作类型”=“发货/清空”查询关联的费用兑付/关闭明细的费用金额（元）汇总，
        //如果活动明细的结案金额≠0，汇总的费用金额（元）=0时，则兑付状态=未兑付；
        //如果活动明细没有关联的结案明细，则兑付状态=未兑付；
        //如果活动明细的结案状态=完全结案，且结案金额＝0时，则兑付状态=完全兑付；
        //如果活动明细的结案状态＝完全结案，结案金额≠0，且汇总的费用金额（元）=当前活动的结案金额，则兑付状态=完全兑付；
        //其他情况，兑付状态=部分兑付；
        //
        //3、“结案金额”取值逻辑：查询活动明细关联的结案明细（取审批通过状态的结案明细数据）的“本次结案金额”汇总；
        //
        //4、“兑付金额”取值逻辑：
        //兑付方式=电汇、账扣、票扣的活动明细：查询活动明细关联的费用兑付明细（取审批通过状态且兑付类型=费用兑付的费用兑付明细数据）的“本次兑付金额”汇总；
        //兑付方式=货补的活动明细：通过“方案明细编码”和“操作类型”=“发货”查询关联的费用兑付/关闭明细的费用金额（元）汇总；

        //活动明细的兑付状态=完全兑付时：
        //①如果兑付方式=货补时，查询这条活动明细对应的《费用兑付/关闭明细》数据（操作类型=发货/清空），查询出来单据中“业务发生日期”最晚的日期即为“完全兑付日期”；
        //②如果兑付方式=账扣、电汇时，查询这条活动明细对应的费用兑付明细数据（兑付类型=费用兑付/费用关闭），查询出来单据中凭证回传日期最晚的日期即为“完全兑付日期”；
        //③如果兑付方式=票扣时，查询这条活动明细对应的费用兑付明细数据（兑付类型=费用兑付/费用关闭），查询出来单据中审批通过日期最晚的日期即为“完全兑付日期”。
        List<FeeCashDetailVo> cashDetailVos = feeCashService.findByAuditDetailCodesPass(cashSchemeDetailCodes, ProcessStatusEnum.PASS.getDictCode(), null,
                Arrays.asList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.CLOSE.getDictCode(), CashTypeEnum.MATERIAL.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()));
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditService.findBySchemeDetailCodesAll(marketingSchemeDetailCodes);
        //查询货补信息
        Map<String, List<DeliveryReplenishmentPoolDetailVo>> deliveryMap = deliveryReplenishmentPoolDetailService.findBySchemeDetailCodesAll(replenishmentSchemeDetailCodes);
        Map<String, List<FeeCashDetailVo>> cashDetailMap = new HashMap<>();
        Map<String, List<MarketingAuditDetailVo>> auditDetailMap = new HashMap<>();
        Map<String, BigDecimal> auditDetailCloseMap = new HashMap<>();

        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(cashDetailVos)) {
            cashDetailMap = cashDetailVos.stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode()));
        }
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(auditDetailVos)) {
            auditDetailMap = auditDetailVos.stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode()));

            //关闭金额
            Map<String, BigDecimal> closeMap = deliveryReplenishmentPoolDetailService.findByAuditDetailCodes(auditDetailVos.stream().map(e -> e.getAuditDetailCode()).collect(Collectors.toList()));
            auditDetailMap.forEach((k, v) -> {
                BigDecimal closeAmount = BigDecimal.ZERO;
                for (MarketingAuditDetailVo detailVo : v) {
                    if (closeMap.containsKey(detailVo.getAuditDetailCode())) {
                        closeAmount = closeAmount.add(closeMap.get(detailVo.getAuditDetailCode()));
                    }
                }
                auditDetailCloseMap.put(k, closeAmount);
            });
        }

        Map<String, BigDecimal> withholdingMap = findProcessPassBySchemeDetailCodes(marketingSchemeDetailCodes);

        List<ActivityPrepayDetailRecord> activityPrepayDetailRecords = activityPrepayDetailRecordRepository.findReportBySchemeDetailCodes(marketingSchemeDetailCodes);
        Map<String, List<ActivityPrepayDetailRecord>> activityPrepayMap = activityPrepayDetailRecords.stream().collect(Collectors.groupingBy(ActivityPrepayDetailRecord::getSchemeDetailCode));
        for (MarketingPlanCaseVo record : data.getRecords()) {
            List<OrgVo> orgVoList = orgMap.get(record.getBelongDepartmentCode());
            Optional<OrgVo> optionalOrgVo = orgVoList.stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType()))
                    .findFirst();
            if (optionalOrgVo.isPresent()) {
                OrgVo orgVo = optionalOrgVo.get();
                record.setRegionCode(orgVo.getOrgCode());
                record.setRegionName(orgVo.getOrgName());
            }
            //设置活动状态
            LocalDate startDate = LocalDate.parse(record.getStartDate(), DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate endDate = LocalDate.parse(record.getEndDate(), DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate now = LocalDate.now();
            if (DelFlagStatusEnum.DELETE.getCode().equals(record.getDelFlag())) {
                record.setActStatus(PlanClosureEnum.closed.getCode());
            } else {
                if (now.isBefore(startDate)) {
                    record.setActStatus(PlanClosureEnum.not_started.getCode());
                } else if (now.isAfter(endDate)) {
                    record.setActStatus(PlanClosureEnum.ended.getCode());
                } else {
                    record.setActStatus(PlanClosureEnum.in_progress.getCode());
                }
            }
            if (withholdingMap.containsKey(record.getSchemeDetailCode())) {
                record.setWithholdingAmount(withholdingMap.get(record.getSchemeDetailCode()));
            }

            String auditStatus = AuditStatusEnum.NOT_AUDIT.getCode();
            String cashStatus = CashStatusEnum.NOT_CASH.getCode();
            BigDecimal auditAmount = BigDecimal.ZERO;
            BigDecimal cashAmount = BigDecimal.ZERO;
            if (auditDetailMap.containsKey(record.getSchemeDetailCode())) {
                auditStatus = AuditStatusEnum.PART_AUDIT.getCode();
                List<MarketingAuditDetailVo> auditDetailVoList = auditDetailMap.get(record.getSchemeDetailCode());
                for (MarketingAuditDetailVo auditDetailVo : auditDetailVoList) {
                    if (BooleanEnum.TRUE.getCapital().equals(auditDetailVo.getBeFullAudit())) {
                        auditStatus = AuditStatusEnum.WHOLE_AUDIT.getCode();
                    }
                    auditAmount = auditAmount.add(auditDetailVo.getAuditAmount());
                }
            }
            record.setAuditAmount(auditAmount);
            if (cashDetailMap.containsKey(record.getSchemeDetailCode())) {
                cashStatus = CashStatusEnum.PART_CASH.getCode();
                cashAmount = cashDetailMap.get(record.getSchemeDetailCode()).stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
                    List<MarketingAuditDetailVo> auditDetailVoList = auditDetailMap.get(record.getSchemeDetailCode());
                    Map<String, List<FeeCashDetailVo>> cashAuditDetailVoMap = cashDetailMap.get(record.getSchemeDetailCode()).stream().collect(Collectors.groupingBy(e -> e.getAuditDetailCode()));

                    int i = 0;
                    for (MarketingAuditDetailVo auditDetail : auditDetailVoList) {
                        if (!cashAuditDetailVoMap.containsKey(auditDetail.getAuditDetailCode())) {
                            continue;
                        }
                        List<FeeCashDetailVo> cashAuditDetailVos = cashAuditDetailVoMap.get(auditDetail.getAuditDetailCode());
                        boolean beFullCash = false;
                        for (FeeCashDetailVo cashDetailVo : cashAuditDetailVos) {
                            if (BooleanEnum.TRUE.getCapital().equals(cashDetailVo.getBeCash())) {
                                beFullCash = true;
                            }
                        }
                        if (beFullCash) {
                            i++;
                        }
                    }
                    if (i == auditDetailVoList.size() || (cashAmount.add(auditDetailCloseMap.getOrDefault(record.getSchemeDetailCode(), BigDecimal.ZERO))).compareTo(auditAmount) >= 0) {
                        cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                        List<FeeCashDetailVo> feeCashDetailVos = cashAuditDetailVoMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
                        if (StringUtils.equalsAny(record.getCashType(), CashMethodEnum.TICKET_BUCKLE.getDictCode())) {
                            List<FeeCashDetailVo> cashDetailVo = feeCashDetailVos.stream().filter(e -> e.getPassDate() != null)
                                    .filter(Objects::nonNull).sorted(Comparator.comparing(FeeCashDetailVo::getPassDate).reversed()).collect(Collectors.toList());
                            record.setWholeCashDate(!CollectionUtils.isEmpty(cashDetailVo) ? DateUtil.formatDate(cashDetailVo.get(0).getPassDate()) : null);
                        } else {
                            List<FeeCashDetailVo> cashDetailPassVo = feeCashDetailVos.stream().filter(e -> e.getPassDate() != null)
                                    .filter(Objects::nonNull).sorted(Comparator.comparing(FeeCashDetailVo::getPassDate).reversed()).collect(Collectors.toList());
                            List<FeeCashDetailVo> cashDetailCallbackVo = feeCashDetailVos.stream().filter(e -> e.getVoucherCallbackDate() != null)
                                    .filter(Objects::nonNull).sorted(Comparator.comparing(FeeCashDetailVo::getVoucherCallbackDate).reversed()).collect(Collectors.toList());
                            String wholeCashDate = null;
                            if (!CollectionUtils.isEmpty(cashDetailPassVo) && !CollectionUtils.isEmpty(cashDetailCallbackVo)) {
                                if (cashDetailPassVo.get(0).getPassDate().compareTo(cashDetailCallbackVo.get(0).getVoucherCallbackDate()) > 0) {
                                    wholeCashDate = DateUtil.formatDate(cashDetailPassVo.get(0).getPassDate());
                                } else {
                                    wholeCashDate = DateUtil.formatDate(cashDetailCallbackVo.get(0).getVoucherCallbackDate());
                                }
                            } else {
                                if (!CollectionUtils.isEmpty(cashDetailPassVo)) {
                                    wholeCashDate = DateUtil.formatDate(cashDetailPassVo.get(0).getPassDate());
                                } else if (!CollectionUtils.isEmpty(cashDetailCallbackVo)) {
                                    wholeCashDate = DateUtil.formatDate(cashDetailCallbackVo.get(0).getVoucherCallbackDate());
                                }
                            }
                            record.setWholeCashDate(wholeCashDate);
                        }
                    }
                }
                //如果活动明细对应的表单类型为“随单搭赠”或“周边物料”，则活动明细的兑付金额=关联当前活动明细的结案明细的“本次结案金额”汇总，如果关联当前活动明细的结案明细有完全结案状态，则兑付状态=完全兑付；
            } else if (Arrays.asList(MarketingPlanCaseTypeEnum.matching_gift.getCode(), MarketingPlanCaseTypeEnum.material.getCode()).contains(record.getCaseType())) {
                if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
                    cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                }
                cashAmount = record.getAuditAmount();
            }
            if (StringUtils.equalsAny(record.getCashType(), CashMethodEnum.REPLENISHMENT.getDictCode())) {
                cashStatus = CashStatusEnum.PART_CASH.getCode();
                BigDecimal cashAmountTotal = BigDecimal.ZERO;
                if (deliveryMap.containsKey(record.getSchemeDetailCode())) {
                    cashAmountTotal = deliveryMap.get(record.getSchemeDetailCode()).stream().map(e -> e.getOperationAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    cashAmount = deliveryMap.get(record.getSchemeDetailCode()).stream()
                            .map(e -> ReplenishmentPoolOperationType.DELIVERY.getCode().equals(e.getOperationType()) ? e.getOperationAmount() : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (BigDecimal.ZERO.compareTo(record.getAuditAmount()) < 0 && BigDecimal.ZERO.compareTo(cashAmountTotal) == 0) {
                        cashStatus = CashStatusEnum.NOT_CASH.getCode();
                    }
                } else {
                    if (BigDecimal.ZERO.compareTo(record.getAuditAmount()) < 0) {
                        cashStatus = CashStatusEnum.NOT_CASH.getCode();
                    }
                }
                if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
                    if (BigDecimal.ZERO.compareTo(record.getAuditAmount()) == 0) {
                        cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                    } else {
                        if (record.getAuditAmount().compareTo(cashAmountTotal) == 0) {
                            cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                        }
                    }
                    if (deliveryMap.containsKey(record.getSchemeDetailCode()) && CashStatusEnum.WHOLE_CASH.getCode().equals(cashStatus)) {
                        List<DeliveryReplenishmentPoolDetailVo> detailVos = deliveryMap.get(record.getSchemeDetailCode()).stream().sorted(Comparator.comparing(DeliveryReplenishmentPoolDetailVo::getOperationTime).reversed()).collect(Collectors.toList());
                        record.setWholeCashDate(DateUtil.formatDate(detailVos.get(0).getOperationTime()));
                    }
                }
                if (!auditDetailMap.containsKey(record.getSchemeDetailCode())) {
                    cashStatus = CashStatusEnum.NOT_CASH.getCode();
                }
            }
            if (activityPrepayMap.containsKey(record.getSchemeDetailCode())) {
                List<ActivityPrepayDetailRecord> curPrepayDetails = activityPrepayMap.get(record.getSchemeDetailCode());
                record.setPrepayAmount(curPrepayDetails.stream().map(a -> a.getPrepayAmount() == null ? BigDecimal.ZERO : a.getPrepayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
                record.setAvailableReversedAmount(curPrepayDetails.stream().map(b -> b.getAvailableReversedAmount() == null ? BigDecimal.ZERO : b.getAvailableReversedAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
            }

            record.setCashAmount(cashAmount);

            record.setAuditStatus(auditStatus);
            record.setCashStatus(cashStatus);
            if(cashStatus.equals( CashStatusEnum.NOT_CASH.getCode())&&(auditStatus.equals(AuditStatusEnum.WHOLE_AUDIT.getCode())&&auditAmount.compareTo(BigDecimal.ZERO)==0)){
                record.setCashStatus(CashStatusEnum.WHOLE_CASH.getCode());
            }
        }
        return data;
    }

    @Override
    public Page<MarketingPlanCaseVo> findMarketingPlanCaseReportListV1(Pageable pageable, MarketingPlanCaseVo vo) {
        //不查询变更的
        vo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getBelongDepartmentCodes())) {
            Validate.isTrue(vo.getBelongDepartmentCodes().size() < 500, "费用使用部门查询个数不能超过500");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getBearDepartmentCodes())) {
            Validate.isTrue(vo.getBearDepartmentCodes().size() < 500, "费用承担部门查询个数不能超过500");
        }
        // 计算budgetYearsStart 和budgetYearsEnd 相距时间，形式为yyyy-MM
        if (StringUtils.isNotEmpty(vo.getBudgetYearsStart()) && StringUtils.isNotEmpty(vo.getBudgetYearsEnd())) {
            // 计算间隔

            Validate.isTrue(DateUtil.getMonthsBetween(vo.getBudgetYearsStart(), vo.getBudgetYearsEnd()) <= 6, "预算年份查询跨度不能超过6个月");
        }

        if (ObjectUtils.isNotEmpty(vo.getRegionName())) {
            Set<String> orgCodeSet = orgVoService.findByOrgQueryDto(new OrgQueryDto() {{
                this.setOrgName(vo.getRegionName());
                this.setOrgType(OrgTypeEnum.DIVISION.getDictCode());
            }});
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(orgCodeSet)) {
                return null;
            }
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCodes(Lists.newArrayList(orgCodeSet));
            List<String> orgCodeList = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            vo.setOrgCodeList(orgCodeList);
        }
        Page<MarketingPlanCaseVo> data = marketingPlanCaseService.findMarketingPlanCaseReportList(pageable, vo);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(data.getRecords())) {
            return data;
        }
        List<MarketingPlanCaseVo> marketingPlanCaseVoList = data.getRecords();

        //查询使用部门对应的一级组织
        List<String> belongDepartmentCodes = marketingPlanCaseVoList.stream().map(MarketingPlanCaseVo::getBelongDepartmentCode).collect(Collectors.toList());
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllParentByOrgCodesMap(belongDepartmentCodes);

        List<String> marketingSchemeDetailCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        List<String> cashSchemeDetailCodes = data.getRecords().stream().filter(e -> StringUtils.equalsAny(e.getCashType(), CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode()))
                .filter(Objects::nonNull).map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        List<String> replenishmentSchemeDetailCodes = data.getRecords().stream().filter(e -> StringUtils.equalsAny(e.getCashType(), CashMethodEnum.REPLENISHMENT.getDictCode()))
                .filter(Objects::nonNull).map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());

        //1、“结案状态”取值逻辑：查询活动明细关联的结案明细（取审批通过状态的结案明细数据）
        //
        //如果关联的结案明细有完全结案的单据，则结案状态=完全结案；
        //如果关联的结案明细都未完全结案，则结案状态=部分结案；
        //如果未查询到关联的结案明细，则结案状态=未结案；
        //
        //2、“兑付状态”取值逻辑：查询活动明细关联的费用兑付明细（取审批通过状态且兑付类型=费用兑付的费用兑付明细数据）
        //（1）兑付方式=电汇、账扣、票扣的活动明细：查询活动明细关联的费用兑付明细（取审批通过状态且兑付类型=费用兑付的费用兑付明细数据）
        //如果活动明细的结案状态≠完全结案，且未查询到关联的费用兑付明细，则兑付状态=未兑付；
        //如果活动明细的结案状态≠完全结案，且可查询到关联的费用兑付明细，则兑付状态=部分兑付；
        //如果活动明细的结案状态＝完全结案，且关联的每条结案明细均有关联的完全兑付的费用兑付明细，或活动明细的结案金额≤活动明细关联的审批通过的费用兑付明细的本次兑付金额汇总，则兑付状态=完全兑付；
        //（2）兑付方式=货补的活动明细：通过“方案明细编码”和“操作类型”=“发货/清空”查询关联的费用兑付/关闭明细的费用金额（元）汇总，
        //如果活动明细的结案金额≠0，汇总的费用金额（元）=0时，则兑付状态=未兑付；
        //如果活动明细没有关联的结案明细，则兑付状态=未兑付；
        //如果活动明细的结案状态=完全结案，且结案金额＝0时，则兑付状态=完全兑付；
        //如果活动明细的结案状态＝完全结案，结案金额≠0，且汇总的费用金额（元）=当前活动的结案金额，则兑付状态=完全兑付；
        //其他情况，兑付状态=部分兑付；
        //
        //3、“结案金额”取值逻辑：查询活动明细关联的结案明细（取审批通过状态的结案明细数据）的“本次结案金额”汇总；
        //
        //4、“兑付金额”取值逻辑：
        //兑付方式=电汇、账扣、票扣的活动明细：查询活动明细关联的费用兑付明细（取审批通过状态且兑付类型=费用兑付的费用兑付明细数据）的“本次兑付金额”汇总；
        //兑付方式=货补的活动明细：通过“方案明细编码”和“操作类型”=“发货”查询关联的费用兑付/关闭明细的费用金额（元）汇总；

        //活动明细的兑付状态=完全兑付时：
        //①如果兑付方式=货补时，查询这条活动明细对应的《费用兑付/关闭明细》数据（操作类型=发货/清空），查询出来单据中“业务发生日期”最晚的日期即为“完全兑付日期”；
        //②如果兑付方式=账扣、电汇时，查询这条活动明细对应的费用兑付明细数据（兑付类型=费用兑付/费用关闭），查询出来单据中凭证回传日期最晚的日期即为“完全兑付日期”；
        //③如果兑付方式=票扣时，查询这条活动明细对应的费用兑付明细数据（兑付类型=费用兑付/费用关闭），查询出来单据中审批通过日期最晚的日期即为“完全兑付日期”。
        List<FeeCashDetailVo> cashDetailVos = feeCashService.findByAuditDetailCodesPass(cashSchemeDetailCodes, ProcessStatusEnum.PASS.getDictCode(), null,
                Arrays.asList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.CLOSE.getDictCode(), CashTypeEnum.MATERIAL.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()));
        List<FeeCashDetailVo> commitCashDetailVos = feeCashService.findByAuditDetailCodesPass(cashSchemeDetailCodes, ProcessStatusEnum.COMMIT.getDictCode(), null,
                Arrays.asList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.CLOSE.getDictCode(), CashTypeEnum.MATERIAL.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()));
        // 预付
        List<FeeCashDetailVo> prePayDetailVos = feeCashService.findByAuditDetailCodesPass(cashSchemeDetailCodes, ProcessStatusEnum.PASS.getDictCode(), null,
                Arrays.asList(CashTypeEnum.WIRE.getDictCode(), CashTypeEnum.ACCOUNT.getDictCode()));
        // 活动预付
        List<ActivityPrepayDetailRecord> o2oPrepayDetailRecords = activityPrepayDetailRecordRepository.findO2OPrepayDetailRecord(marketingSchemeDetailCodes);
        log.info("findO2OPrepayDetailRecordParam: {}", JSON.toJSONString(marketingSchemeDetailCodes));
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditService.findBySchemeDetailCodesAll(marketingSchemeDetailCodes);
        //查询货补信息
        Map<String, List<DeliveryReplenishmentPoolDetailVo>> deliveryMap = deliveryReplenishmentPoolDetailService.findBySchemeDetailCodesAll(replenishmentSchemeDetailCodes);
        // 查询发货 + 已清空
//        Map<String, BigDecimal> clearAndDeliveryMap = deliveryReplenishmentPoolDetailService.findManualClearAndDeliveryBySchemeDetailCodes(marketingSchemeDetailCodes, null);
        DeliverySearchDto deliverySearchDto = new DeliverySearchDto();
        List<String> businessCodeList = Lists.newArrayList();
        businessCodeList.addAll(auditDetailVos.stream().map(MarketingAuditDetailVo::getAuditDetailCode).collect(Collectors.toList()));
        businessCodeList.addAll(data.getRecords().stream().filter(e -> StringUtils.isNotBlank(e.getCaseType())
                && Lists.newArrayList(MarketingPlanCaseTypeEnum.material.getCode(), MarketingPlanCaseTypeEnum.matching_gift.getCode()).contains(e.getCaseType())).map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList()));
        log.info("businessCodeList:{}", businessCodeList);
        deliverySearchDto.setBusinessCodeList(businessCodeList);
//        deliverySearchDto.setOperationTypes(Arrays.asList("delivery", "manualClear"));
        List<ReplenishmentPoolDetailVo> deliveryList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(businessCodeList)) {
            deliveryList = replenishmentPoolDetailVoService.findDelivery(deliverySearchDto);
        }
        Map<String, BigDecimal> clearAndDeliveryMap = Maps.newHashMap();
        Map<String, List<ReplenishmentPoolDetailVo>> deliveryAndClearyGroupMap = deliveryList.stream().collect(Collectors.groupingBy(ReplenishmentPoolDetailVo::getParentBusinessCode));
        // 循环deliveryAndClearyGroupMap
        deliveryAndClearyGroupMap.forEach((k, v) -> {
            BigDecimal deliveryAmount = v.stream().filter(e -> e.getOperationType().equals("delivery")).map(ReplenishmentPoolDetailVo::getOperationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal clearAmount = v.stream().filter(e -> e.getOperationType().equals("manualClear")).map(ReplenishmentPoolDetailVo::getOperationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            clearAndDeliveryMap.put(k, deliveryAmount.subtract(clearAmount));
        });

        Map<String, List<FeeCashDetailVo>> cashDetailMap = new HashMap<>();
        Map<String, List<FeeCashDetailVo>> commitCashDetailMap = new HashMap<>();
        Map<String, List<FeeCashDetailVo>> prepayDetailMap = new HashMap<>();
        Map<String, List<ActivityPrepayDetailRecord>> o2oPrepayDetailMap = new HashMap<>();
        Map<String, List<MarketingAuditDetailVo>> auditDetailMap = new HashMap<>();
        Map<String, BigDecimal> auditDetailCloseMap = new HashMap<>();

        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(cashDetailVos)) {
            cashDetailMap = cashDetailVos.stream().collect(Collectors.groupingBy(FeeCashDetailVo::getSchemeDetailCode));
        }
        if (!CollectionUtils.isEmpty(commitCashDetailVos)) {
            commitCashDetailMap = commitCashDetailVos.stream().collect(Collectors.groupingBy(FeeCashDetailVo::getSchemeDetailCode));
        }
        if (!CollectionUtils.isEmpty(prePayDetailVos)) {
            prepayDetailMap = prePayDetailVos.stream().collect(Collectors.groupingBy(FeeCashDetailVo::getSchemeDetailCode));
        }
        if (!CollectionUtils.isEmpty(o2oPrepayDetailRecords)) {
            o2oPrepayDetailMap = o2oPrepayDetailRecords.stream().collect(Collectors.groupingBy(ActivityPrepayDetailRecord::getSchemeDetailCode));
        }
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(auditDetailVos)) {
            auditDetailMap = auditDetailVos.stream().collect(Collectors.groupingBy(MarketingAuditDetailVo::getSchemeDetailCode));

            //关闭金额
            Map<String, BigDecimal> closeMap = deliveryReplenishmentPoolDetailService.findByAuditDetailCodes(auditDetailVos.stream().map(e -> e.getAuditDetailCode()).collect(Collectors.toList()));
            auditDetailMap.forEach((k, v) -> {
                BigDecimal closeAmount = BigDecimal.ZERO;
                for (MarketingAuditDetailVo detailVo : v) {
                    if (closeMap.containsKey(detailVo.getAuditDetailCode())) {
                        closeAmount = closeAmount.add(closeMap.get(detailVo.getAuditDetailCode()));
                    }
                }
                auditDetailCloseMap.put(k, closeAmount);
            });
        }

        List<WithHoldingVo> withHoldingVoList = withHoldingRepository.findProcessPassBySchemeDetailCodes(marketingSchemeDetailCodes);
        Map<String, BigDecimal> withholdingMap = withHoldingVoList.stream().collect(Collectors.toMap(WithHoldingVo::getActivitiesDetailCode, WithHoldingVo::getActualAmount, (a, b) -> a));
        // 管报计提金额
        Map<String, BigDecimal> withHoldingReportAmountMap = withHoldingVoList.stream().collect(Collectors.groupingBy(e -> e.getActivitiesDetailCode(), Collectors.mapping(e -> e.getActualReportAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        // 财报实际计提金额
        Map<String, BigDecimal> withHoldingActualAmountMap = withHoldingVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getVoucherCode())).collect(Collectors.groupingBy(e -> e.getActivitiesDetailCode(), Collectors.mapping(e -> e.getActualAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        List<ActivityPrepayDetailRecord> activityPrepayDetailRecords = activityPrepayDetailRecordRepository.findReportBySchemeDetailCodes(marketingSchemeDetailCodes);
        Map<String, List<ActivityPrepayDetailRecord>> activityPrepayMap = activityPrepayDetailRecords.stream().collect(Collectors.groupingBy(ActivityPrepayDetailRecord::getSchemeDetailCode));
        for (MarketingPlanCaseVo record : data.getRecords()) {
            String hasWithHolding = "N";
            if (withHoldingReportAmountMap.containsKey(record.getSchemeDetailCode())) {
                hasWithHolding = "Y";
            }
            List<OrgVo> orgVoList = orgMap.get(record.getBelongDepartmentCode());
            Optional<OrgVo> optionalOrgVo = orgVoList.stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType()))
                    .findFirst();
            if (optionalOrgVo.isPresent()) {
                OrgVo orgVo = optionalOrgVo.get();
                record.setRegionCode(orgVo.getOrgCode());
                record.setRegionName(orgVo.getOrgName());
            }
            //设置活动状态
            LocalDate startDate = LocalDate.parse(record.getStartDate(), DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate endDate = LocalDate.parse(record.getEndDate(), DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate now = LocalDate.now();
            if (DelFlagStatusEnum.DELETE.getCode().equals(record.getDelFlag())) {
                record.setActStatus(PlanClosureEnum.closed.getCode());
            } else {
                if (now.isBefore(startDate)) {
                    record.setActStatus(PlanClosureEnum.not_started.getCode());
                } else if (now.isAfter(endDate)) {
                    record.setActStatus(PlanClosureEnum.ended.getCode());
                } else {
                    record.setActStatus(PlanClosureEnum.in_progress.getCode());
                }
            }
            if (withholdingMap.containsKey(record.getSchemeDetailCode())) {
                record.setWithholdingAmount(withholdingMap.get(record.getSchemeDetailCode()));
            }

            String auditStatus = AuditStatusEnum.NOT_AUDIT.getCode();
            String cashStatus = CashStatusEnum.NOT_CASH.getCode();
            BigDecimal auditAmount = BigDecimal.ZERO;
            BigDecimal cashAmount = BigDecimal.ZERO;
            if (auditDetailMap.containsKey(record.getSchemeDetailCode())) {
                auditStatus = AuditStatusEnum.PART_AUDIT.getCode();
                List<MarketingAuditDetailVo> auditDetailVoList = auditDetailMap.get(record.getSchemeDetailCode());
                for (MarketingAuditDetailVo auditDetailVo : auditDetailVoList) {
                    if (BooleanEnum.TRUE.getCapital().equals(auditDetailVo.getBeFullAudit())) {
                        auditStatus = AuditStatusEnum.WHOLE_AUDIT.getCode();
                    }
                    auditAmount = auditAmount.add(auditDetailVo.getAuditAmount());
                }
            }
            record.setAuditAmount(auditAmount);
            if (cashDetailMap.containsKey(record.getSchemeDetailCode())) {
                cashStatus = CashStatusEnum.PART_CASH.getCode();
                cashAmount = cashDetailMap.get(record.getSchemeDetailCode()).stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
                    List<MarketingAuditDetailVo> auditDetailVoList = auditDetailMap.get(record.getSchemeDetailCode());
                    Map<String, List<FeeCashDetailVo>> cashAuditDetailVoMap = cashDetailMap.get(record.getSchemeDetailCode()).stream().collect(Collectors.groupingBy(e -> e.getAuditDetailCode()));

                    int i = 0;
                    for (MarketingAuditDetailVo auditDetail : auditDetailVoList) {
                        if (!cashAuditDetailVoMap.containsKey(auditDetail.getAuditDetailCode())) {
                            continue;
                        }
                        List<FeeCashDetailVo> cashAuditDetailVos = cashAuditDetailVoMap.get(auditDetail.getAuditDetailCode());
                        boolean beFullCash = false;
                        for (FeeCashDetailVo cashDetailVo : cashAuditDetailVos) {
                            if (BooleanEnum.TRUE.getCapital().equals(cashDetailVo.getBeCash())) {
                                beFullCash = true;
                            }
                        }
                        if (beFullCash) {
                            i++;
                        }
                    }
                    if (i == auditDetailVoList.size() || (cashAmount.add(auditDetailCloseMap.getOrDefault(record.getSchemeDetailCode(), BigDecimal.ZERO))).compareTo(auditAmount) >= 0) {
                        cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                        List<FeeCashDetailVo> feeCashDetailVos = cashAuditDetailVoMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
                        if (StringUtils.equalsAny(record.getCashType(), CashMethodEnum.TICKET_BUCKLE.getDictCode())) {
                            List<FeeCashDetailVo> cashDetailVo = feeCashDetailVos.stream().filter(e -> e.getPassDate() != null)
                                    .filter(Objects::nonNull).sorted(Comparator.comparing(FeeCashDetailVo::getPassDate).reversed()).collect(Collectors.toList());
                            record.setWholeCashDate(!CollectionUtils.isEmpty(cashDetailVo) ? DateUtil.formatDate(cashDetailVo.get(0).getPassDate()) : null);
                        } else {
                            List<FeeCashDetailVo> cashDetailPassVo = feeCashDetailVos.stream().filter(e -> e.getPassDate() != null)
                                    .filter(Objects::nonNull).sorted(Comparator.comparing(FeeCashDetailVo::getPassDate).reversed()).collect(Collectors.toList());
                            List<FeeCashDetailVo> cashDetailCallbackVo = feeCashDetailVos.stream().filter(e -> e.getVoucherCallbackDate() != null)
                                    .filter(Objects::nonNull).sorted(Comparator.comparing(FeeCashDetailVo::getVoucherCallbackDate).reversed()).collect(Collectors.toList());
                            String wholeCashDate = null;
                            if (!CollectionUtils.isEmpty(cashDetailPassVo) && !CollectionUtils.isEmpty(cashDetailCallbackVo)) {
                                if (cashDetailPassVo.get(0).getPassDate().compareTo(cashDetailCallbackVo.get(0).getVoucherCallbackDate()) > 0) {
                                    wholeCashDate = DateUtil.formatDate(cashDetailPassVo.get(0).getPassDate());
                                } else {
                                    wholeCashDate = DateUtil.formatDate(cashDetailCallbackVo.get(0).getVoucherCallbackDate());
                                }
                            } else {
                                if (!CollectionUtils.isEmpty(cashDetailPassVo)) {
                                    wholeCashDate = DateUtil.formatDate(cashDetailPassVo.get(0).getPassDate());
                                } else if (!CollectionUtils.isEmpty(cashDetailCallbackVo)) {
                                    wholeCashDate = DateUtil.formatDate(cashDetailCallbackVo.get(0).getVoucherCallbackDate());
                                }
                            }
                            record.setWholeCashDate(wholeCashDate);
                        }
                    }
                }
                //如果活动明细对应的表单类型为“随单搭赠”或“周边物料”，则活动明细的兑付金额=关联当前活动明细的结案明细的“本次结案金额”汇总，如果关联当前活动明细的结案明细有完全结案状态，则兑付状态=完全兑付；
            } else if (Arrays.asList(MarketingPlanCaseTypeEnum.matching_gift.getCode(), MarketingPlanCaseTypeEnum.material.getCode()).contains(record.getCaseType())) {
                if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
                    cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                }
                cashAmount = record.getAuditAmount();
            }
            if (StringUtils.equalsAny(record.getCashType(), CashMethodEnum.REPLENISHMENT.getDictCode())) {
                cashStatus = CashStatusEnum.PART_CASH.getCode();
                BigDecimal cashAmountTotal = BigDecimal.ZERO;
                if (deliveryMap.containsKey(record.getSchemeDetailCode())) {
                    cashAmountTotal = deliveryMap.get(record.getSchemeDetailCode()).stream().map(e -> e.getOperationAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    cashAmount = deliveryMap.get(record.getSchemeDetailCode()).stream()
                            .map(e -> ReplenishmentPoolOperationType.DELIVERY.getCode().equals(e.getOperationType()) ? e.getOperationAmount() : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (BigDecimal.ZERO.compareTo(record.getAuditAmount()) < 0 && BigDecimal.ZERO.compareTo(cashAmountTotal) == 0) {
                        cashStatus = CashStatusEnum.NOT_CASH.getCode();
                    }
                } else {
                    if (BigDecimal.ZERO.compareTo(record.getAuditAmount()) < 0) {
                        cashStatus = CashStatusEnum.NOT_CASH.getCode();
                    }
                }
                if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
                    if (BigDecimal.ZERO.compareTo(record.getAuditAmount()) == 0) {
                        cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                    } else {
                        if (record.getAuditAmount().compareTo(cashAmountTotal) == 0) {
                            cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                        }
                    }
                    if (deliveryMap.containsKey(record.getSchemeDetailCode()) && CashStatusEnum.WHOLE_CASH.getCode().equals(cashStatus)) {
                        List<DeliveryReplenishmentPoolDetailVo> detailVos = deliveryMap.get(record.getSchemeDetailCode()).stream().sorted(Comparator.comparing(DeliveryReplenishmentPoolDetailVo::getOperationTime).reversed()).collect(Collectors.toList());
                        record.setWholeCashDate(DateUtil.formatDate(detailVos.get(0).getOperationTime()));
                    }
                }
                if (!auditDetailMap.containsKey(record.getSchemeDetailCode())) {
                    cashStatus = CashStatusEnum.NOT_CASH.getCode();
                }
            }
            if (activityPrepayMap.containsKey(record.getSchemeDetailCode())) {
                List<ActivityPrepayDetailRecord> curPrepayDetails = activityPrepayMap.get(record.getSchemeDetailCode());
                record.setPrepayAmount(curPrepayDetails.stream().map(a -> a.getPrepayAmount() == null ? BigDecimal.ZERO : a.getPrepayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
                record.setAvailableReversedAmount(curPrepayDetails.stream().map(b -> b.getAvailableReversedAmount() == null ? BigDecimal.ZERO : b.getAvailableReversedAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
            }

            record.setCashAmount(cashAmount);

            record.setAuditStatus(auditStatus);
            record.setCashStatus(cashStatus);
            if(cashStatus.equals( CashStatusEnum.NOT_CASH.getCode())&&(auditStatus.equals(AuditStatusEnum.WHOLE_AUDIT.getCode())&&auditAmount.compareTo(BigDecimal.ZERO)==0)){
                record.setCashStatus(CashStatusEnum.WHOLE_CASH.getCode());
            }
            String caseType = record.getCaseType();
            String schemeDetailCode = record.getSchemeDetailCode();
            // 活动预算年月
            String years = record.getYears();
            if (MarketingPlanCaseTypeEnum.material.getCode().equals(caseType)) {
                BigDecimal restAvailableCashAmount = record.getAuditAmount().subtract(clearAndDeliveryMap.getOrDefault(record.getSchemeDetailCode(), BigDecimal.ZERO));
                record.setRestAvailableCashAmount(restAvailableCashAmount);
            } else if (MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(caseType)) {
                if (record.getCashStatus().equals(CashStatusEnum.WHOLE_CASH.getCode())) {
                    record.setActualReportTotalAmount(record.getCashAmount());
                }
            } else {
                // 1
                record.setWithHoldingReportAmount(withHoldingReportAmountMap.getOrDefault(record.getSchemeDetailCode(), BigDecimal.ZERO));
                // 2
                BigDecimal beforeWithHoldingAmount = BigDecimal.ZERO;
                List<FeeCashDetailVo> feeCashDetailVos = cashDetailMap.getOrDefault(schemeDetailCode, Lists.newArrayList());
                if (!CollectionUtils.isEmpty(feeCashDetailVos)) {
                    for (FeeCashDetailVo k : feeCashDetailVos) {
                        if (k.getCashMethod().equals(CashMethodEnum.TICKET_BUCKLE.getDictCode())
                                && k.getCashType().equals(CashTypeEnum.FEE.getDictCode())
                                && Objects.nonNull(k.getPassDate()) && years.substring(0, 7).equals(DateUtil.formatDate(k.getPassDate()).substring(0, 7))) {
                            beforeWithHoldingAmount = beforeWithHoldingAmount.add(k.getThisCashAmount());
                        } else if (Lists.newArrayList(CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode()).contains(k.getCashMethod())
                                && Lists.newArrayList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()).contains(k.getCashType())
                                && Objects.nonNull(k.getVoucherCallbackDate()) && years.substring(0, 7).equals(DateUtil.formatDate(k.getVoucherCallbackDate()).substring(0, 7))) {
                            beforeWithHoldingAmount = beforeWithHoldingAmount.add(k.getThisCashAmount());
                        } else if (Lists.newArrayList(CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode()).contains(k.getCashMethod())
                                && k.getCashType().equals(CashTypeEnum.CLOSE.getDictCode())
                                && Objects.nonNull(k.getPassDate()) && years.substring(0, 7).equals(DateUtil.formatDate(k.getPassDate()).substring(0, 7))
                        ) {
                            beforeWithHoldingAmount  = beforeWithHoldingAmount.add(k.getAvailableCashAmount());
                        }
                    }
                }
                BigDecimal alreadyCashPaidAmount = BigDecimal.ZERO;

                List<MarketingAuditDetailVo> marketingAuditDetailVos = auditDetailMap.get(schemeDetailCode);
                if (!CollectionUtils.isEmpty(marketingAuditDetailVos)) {
                    List<String> auditDetailCodes = marketingAuditDetailVos.stream().map(MarketingAuditDetailVo::getAuditDetailCode)
                            .filter(StringUtil::isNotEmpty).collect(Collectors.toList());
                    log.info("lineSchemeDetailCode: {}, lineAuditDetailCodes: {}", schemeDetailCode, JSON.toJSONString(auditDetailCodes));
                    BigDecimal reduce = deliveryList.stream().filter(l -> StringUtils.isNotEmpty(l.getDeliveryTime())
                            && l.getDeliveryTime().substring(0, 7).equals(years) && auditDetailCodes.contains(l.getParentBusinessCode())).map(ReplenishmentPoolDetailVo::getOperationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    beforeWithHoldingAmount = beforeWithHoldingAmount.add(reduce);

                    for (String auditDetailCode : auditDetailCodes) {
                        if (clearAndDeliveryMap.containsKey(auditDetailCode)) {
                            alreadyCashPaidAmount = alreadyCashPaidAmount.add(clearAndDeliveryMap.get(auditDetailCode));
                        }
                    }
                }

//                Map<String, BigDecimal> itemClearAndDeliveryMap = deliveryReplenishmentPoolDetailService.findManualClearAndDeliveryBySchemeDetailCodes(Lists.newArrayList(schemeDetailCode), years);
//                beforeWithHoldingAmount = beforeWithHoldingAmount.add(itemClearAndDeliveryMap.getOrDefault(schemeDetailCode, BigDecimal.ZERO));
                if (record.getAuditStatus().equals(AuditStatusEnum.WHOLE_AUDIT.getCode())) {
                    record.setRestAvailableAuditAmount(new BigDecimal(0));
                } else {
                    // 判断是否计提
                    if (hasWithHolding.equals("Y")) {
                        // 计提前兑付+财报实际计提金额-已结案金额
                        record.setRestAvailableAuditAmount(beforeWithHoldingAmount.add(withHoldingActualAmountMap.getOrDefault(schemeDetailCode, BigDecimal.ZERO)).subtract(record.getAuditAmount()));
                    } else {
                        // 活动申请金额-已结案金额
                        record.setRestAvailableAuditAmount(record.getApplyAmount().subtract(record.getAuditAmount()));
                    }
                }
                // 3
                List<FeeCashDetailVo> prepayDetails = prepayDetailMap.get(schemeDetailCode);
                if (!CollectionUtils.isEmpty(prepayDetails)) {
                    BigDecimal prepaidAmount = prepayDetails.stream()
                            .filter(e -> Lists.newArrayList(CashTypeEnum.WIRE.getDictCode(), CashTypeEnum.ACCOUNT.getDictCode()).contains(e.getCashType()) && (e.getPayStatus().equals("success_pay") || e.getPayStatus().equals("return_tickets")))
                            .map(FeeCashDetailVo::getThisCashAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    record.setAlreadyPrepaidAmount(prepaidAmount);
                }
                List<ActivityPrepayDetailRecord> o2oPrepayDetails = o2oPrepayDetailMap.get(schemeDetailCode);
                if (!CollectionUtils.isEmpty(o2oPrepayDetails)) {
                    BigDecimal o2oPrepaidAmount = o2oPrepayDetails.stream().map(ActivityPrepayDetailRecord::getPrepayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    record.setAlreadyPrepaidAmount((null == record.getAlreadyPrepaidAmount()? BigDecimal.ZERO : record.getAlreadyPrepaidAmount()).add(o2oPrepaidAmount));
                }
                // 4
                List<FeeCashDetailVo> commitFeeCashDetails = commitCashDetailMap.getOrDefault(schemeDetailCode, Lists.newArrayList());
                feeCashDetailVos.addAll(commitFeeCashDetails);
                // 2025-06-24  导出Caused by: java.lang.NullPointerException: null\n\tat java.math.BigDecimal.add(BigDecimal.java:1305)\n\tat com.biz.crm.tpm.business.pay.local.service.internal.WithHoldingServiceImpl.findMarketingPlanCaseReportListV1
                for (FeeCashDetailVo k : feeCashDetailVos) {
                    if (Lists.newArrayList(CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode()).contains(k.getCashMethod()) &&
                            Lists.newArrayList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()).contains(k.getCashType())) {
                        if(Objects.isNull(k.getActualPayAmount())){
                            k.setActualPayAmount(BigDecimal.ZERO);
                        }
                        alreadyCashPaidAmount = alreadyCashPaidAmount.add(k.getActualPayAmount());
                    } else if (CashMethodEnum.TICKET_BUCKLE.getDictCode().equals(k.getCashMethod()) && Lists.newArrayList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()).contains(k.getCashType())) {
                        if(Objects.isNull(k.getThisCashAmount())){
                            k.setThisCashAmount(BigDecimal.ZERO);
                        }
                        alreadyCashPaidAmount = alreadyCashPaidAmount.add(k.getThisCashAmount());
                    } else if (Lists.newArrayList(CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode(), CashMethodEnum.CLOSE.getDictCode()).contains(k.getCashMethod())
                            && k.getCashType().equals(CashTypeEnum.CLOSE.getDictCode())) {
                        if(Objects.isNull(k.getAvailableCashAmount())){
                            k.setAvailableCashAmount(BigDecimal.ZERO);
                        }
                        alreadyCashPaidAmount = alreadyCashPaidAmount.add(k.getAvailableCashAmount());
                    }
                }

                if (record.getCashStatus().equals(CashStatusEnum.WHOLE_CASH.getCode())) {
                    record.setRestAvailableCashAmount(new BigDecimal(0));
                } else {
                    if (record.getAuditStatus().equals(AuditStatusEnum.WHOLE_AUDIT.getCode())) {
                        // 已结案金额-已预付金额-已兑付实付金额
                        record.setRestAvailableCashAmount(record.getAuditAmount().subtract(null == record.getAlreadyPrepaidAmount()? BigDecimal.ZERO : record.getAlreadyPrepaidAmount()).subtract(alreadyCashPaidAmount));
                    } else {
                        if (hasWithHolding.equals("Y")) {
                            // 计提前兑付金额+财报实际计提金额-已预付金额-已兑付实付金额
                            BigDecimal restAvailableCashAmount = beforeWithHoldingAmount.add(withHoldingActualAmountMap.getOrDefault(schemeDetailCode, BigDecimal.ZERO)).subtract(null == record.getAlreadyPrepaidAmount()? BigDecimal.ZERO : record.getAlreadyPrepaidAmount()).subtract(alreadyCashPaidAmount);
                            record.setRestAvailableCashAmount(restAvailableCashAmount);
                        } else {
                            // 活动申请金额-已预付金额-已兑付实付金额
                            record.setRestAvailableCashAmount(record.getApplyAmount().subtract(null == record.getAlreadyPrepaidAmount()? BigDecimal.ZERO : record.getAlreadyPrepaidAmount()).subtract(alreadyCashPaidAmount));
                        }
                    }
                }
                // 5
                if (hasWithHolding.equals("Y")) {
                    // 计提前兑付金额+管报实际计提金额
                    record.setActualReportTotalAmount(beforeWithHoldingAmount.add(withHoldingReportAmountMap.getOrDefault(schemeDetailCode, BigDecimal.ZERO)));
                } else {
                    if (record.getCashStatus().equals(CashStatusEnum.WHOLE_CASH.getCode())) {
                        record.setActualReportTotalAmount(record.getCashAmount());
                    } else {
                        if (record.getAuditStatus().equals(AuditStatusEnum.WHOLE_AUDIT.getCode())) {
                            record.setActualReportTotalAmount(record.getAuditAmount());
                        } else {
                            record.setActualReportTotalAmount(record.getApplyAmount());
                        }
                    }
                }
            }
            if (record.getCaseType().equals(MarketingPlanCaseTypeEnum.matching_gift.getCode())) {
                record.setLevelOrProductName(String.join(",", Optional.ofNullable(record.getLevelList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
                if (StringUtils.isEmpty(record.getLevelOrProductName())) {
                    record.setLevelOrProductName(String.join(",", Optional.ofNullable(record.getProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
                }
            } else if (record.getCaseType().equals(MarketingPlanCaseTypeEnum.back.getCode())) {
                record.setFeeProductOrItemName(String.join(",", Optional.ofNullable(record.getFeeItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
                if (StringUtils.isEmpty(record.getFeeProductOrItemName())) {
                    record.setFeeProductOrItemName(String.join(",", Optional.ofNullable(record.getFeeProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
                }
            }
        }
        return data;
    }

    @Override
    public List<WithHoldingVo> findByCollectCode(String collectCode) {
        if (StringUtils.isBlank(collectCode)) {
            return Lists.newArrayList();
        }
        return withHoldingRepository.findByCollectCode(collectCode);
    }

    @Override
    public List<WithHoldingIncomeVo> findActualReportAmountByYears(List<String> years) {
        return this.withHoldingRepository.findActualReportAmountByYears(years);
    }


    @Override
    public BigDecimal findAmountByOrgCodesAndYears(List<String> orgCodes, String years) {
        return this.withHoldingRepository.findAmountByOrgCodesAndYears(orgCodes, years);
    }


    @Override
    public List<WithHoldingVo> findWithholdingListByYearsAndOrgCodes(WithholdingIncomeQueryDto dto) {
        List<OrgVo> orgVos = orgVoService.findAllChildrenByOrgCodes(dto.getOrgCodes());
        List<WithHoldingVo> dataList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(orgVos)) {
            List<String> orgCodes = orgVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode()))
                    .map(x -> x.getOrgCode())
                    .collect(Collectors.toList());
            dto.setOrgCodes(orgCodes);
            dataList = withHoldingRepository.findWithholdingListByYearsAndOrgCodes(dto);
        }
        return dataList;
    }

    @Override
    public Map<String, BigDecimal> findByBusinessCodes(List<String> codes) {
        if(CollectionUtils.isEmpty(codes)){
            return new HashMap<>();
        }
        List<WithHolding> list = withHoldingRepository.findByBusinessCodes(codes);
        if(CollectionUtils.isEmpty(list)){
            return new HashMap<>();
        }
        Map<String,BigDecimal>  bigDecimalMap = new HashMap<>();
        for (WithHolding mode : list) {
            if(StringUtils.isEmpty(mode.getBusinessCode())){
                continue;
            }
            bigDecimalMap.put(mode.getBusinessCode(),mode.getActualReportAmount());
        }
        return bigDecimalMap;
    }

    /**
     * 分页查询计提余额数据
     * 实现WithHoldingBalanceDataViewRegister的SQL逻辑
     * 根据TPM-计提余额报表_副本.docx文档要求处理业务逻辑
     *
     * @param pageable 分页对象
     * @param dto 查询条件
     * @return 计提余额分页数据
     */
    @Override
    public Page<WithHoldingBalanceVo> findWithHoldingBalanceByConditions(Pageable pageable, WithHoldingBalanceDto dto) {
        pageable = PageRequest.of(dto.getPageNum(), dto.getPageSize());
//        ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        dto.setTenantCode(TenantUtils.getTenantCode());

        // 查询基础数据
        Page<WithHoldingBalanceVo> page = this.withHoldingRepository.findWithHoldingBalanceByConditions(pageable, dto);

        // 根据文档要求，对查询结果进行业务逻辑处理
        if (page != null && page.getRecords() != null && !page.getRecords().isEmpty()) {
            List<String> schemeDetailCodes = page.getRecords().stream().map(WithHoldingBalanceVo::getActivitiesDetailCode).distinct().collect(Collectors.toList());
            List<String> withHoldingCodes = page.getRecords().stream().map(WithHoldingBalanceVo::getWithHoldingCode).distinct().collect(Collectors.toList());
            Page<MarketingPlanCaseVo> planCaseVoPage = new Page<>(1, schemeDetailCodes.size());
            MarketingPlanCaseVo paramVo = new MarketingPlanCaseVo();
            paramVo.setSchemeDetailCodeList(schemeDetailCodes);
            Page<MarketingPlanCaseVo> planCaseData = planCaseRepository.findMarketingPlanCaseReportList(planCaseVoPage, paramVo);
            List<MarketingPlanCaseVo> records = planCaseData.getRecords();
            Map<String, MarketingPlanCaseVo> planCaseVoMap = records.stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), e -> e, (v1, v2) -> v1));
            List<String> marketingSchemeDetailCodes = records.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<String> cashSchemeDetailCodes = records.stream().filter(e -> StringUtils.equalsAny(e.getCashType(), CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode()))
                    .filter(Objects::nonNull).map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            // 审批通过费用兑付
            List<FeeCashDetailVo> cashDetailVos = feeCashService.findByAuditDetailCodesPass(cashSchemeDetailCodes, ProcessStatusEnum.PASS.getDictCode(), null,
                    Arrays.asList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.CLOSE.getDictCode(), CashTypeEnum.MATERIAL.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()));
            Map<String, List<FeeCashDetailVo>> cashDetailMap = cashDetailVos.stream().collect(Collectors.groupingBy(FeeCashDetailVo::getSchemeDetailCode));
            // 审批中费用兑付
            List<FeeCashDetailVo> commitCashDetailVos = feeCashService.findByAuditDetailCodesPass(cashSchemeDetailCodes, ProcessStatusEnum.COMMIT.getDictCode(), null,
                    Arrays.asList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.CLOSE.getDictCode(), CashTypeEnum.MATERIAL.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()));
            Map<String, List<FeeCashDetailVo>> commitCashDetailMap = commitCashDetailVos.stream().collect(Collectors.groupingBy(FeeCashDetailVo::getSchemeDetailCode));
            //结案明细
            List<MarketingAuditDetailVo> auditDetailVos = marketingAuditService.findCommitAndPassBySchemeDetailCodes(marketingSchemeDetailCodes);
            Map<String, List<MarketingAuditDetailVo>> auditDetailMap = auditDetailVos.stream().collect(Collectors.groupingBy(MarketingAuditDetailVo::getSchemeDetailCode));
            // 货补发货和清空数据
            DeliverySearchDto deliverySearchDto = new DeliverySearchDto();
            List<String> businessCodeList = Lists.newArrayList();
            businessCodeList.addAll(auditDetailVos.stream().filter(e -> e.getStatus().equals("3")).map(MarketingAuditDetailVo::getAuditDetailCode).collect(Collectors.toList()));
            businessCodeList.addAll(records.stream().filter(e -> StringUtils.isNotBlank(e.getCaseType())
                    && Lists.newArrayList(MarketingPlanCaseTypeEnum.material.getCode(), MarketingPlanCaseTypeEnum.matching_gift.getCode()).contains(e.getCaseType()))
                    .map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList()));
            log.info("businessCodeList:{}", businessCodeList);
            deliverySearchDto.setBusinessCodeList(businessCodeList);
            Map<String, List<ReplenishmentPoolDetailVo>> clearAndDeliveryMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(businessCodeList)) {
                List<ReplenishmentPoolDetailVo> deliveryList = replenishmentPoolDetailVoService.findDelivery(deliverySearchDto);
//                clearAndDeliveryMap  = deliveryList.stream().collect(Collectors.groupingBy(ReplenishmentPoolDetailVo::getParentBusinessCode, Collectors.mapping(l -> l.getOperationAmount().abs(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                clearAndDeliveryMap  = deliveryList.stream().collect(Collectors.groupingBy(ReplenishmentPoolDetailVo::getParentBusinessCode));
            }

            // 费用冲销(非pod差异)
            List<WithHoldingWriteOffVo> writeOffVos = withHoldingWriteOffService.findListBySchemeDetailCodes(marketingSchemeDetailCodes, Lists.newArrayList(Arrays.stream(WriteOffTypeEnum.values()).map(WriteOffTypeEnum::getDictCode).collect(Collectors.toList())));
            Map<String, List<WithHoldingWriteOffVo>> writeOffMap = writeOffVos.stream().collect(Collectors.groupingBy(WithHoldingWriteOffVo::getActivitiesDetailCode));

            // 费用冲销（pod差异相关）
            List<String> podDiffWithholdingCodes = page.getRecords().stream().filter(l -> Lists.newArrayList(WithHoldingTypeEnum.ORDER_CLOSE_DETAIL.getDictCode(),
                    WithHoldingTypeEnum.POD_DIFF_ADD.getDictCode(),
                    WithHoldingTypeEnum.POD_DIFF_REDUCE.getDictCode()).contains(l.getWithHoldingType())).map(WithHoldingBalanceVo::getWithHoldingCode).collect(Collectors.toList());

            log.info("podDiffWithholdingCodes: {}", podDiffWithholdingCodes);
            List<WithHoldingWriteOffVo> podDiffWriteOffs = withHoldingWriteOffRepository.findByCodes(podDiffWithholdingCodes);
            Map<String, List<WithHoldingWriteOffVo>> podWriteOffMap = podDiffWriteOffs.stream().collect(Collectors.groupingBy(WithHoldingWriteOffVo::getWithHoldingCode));

            // 货补信息by 计提编码
            List<WithHoldingVo> podDiffWithholdingList = withHoldingRepository.findByCodes(podDiffWithholdingCodes);
            Map<String, WithHoldingVo> podDiffWithholdingMap = podDiffWithholdingList.stream().collect(Collectors.toMap(WithHoldingVo::getWithHoldingCode, Function.identity(), (a, b) -> a));


            // 预付明细
            List<ActivityPrepayDetailRecord> activityPrepayDetailRecords = activityPrepayDetailRecordRepository.findReportBySchemeDetailCodes(marketingSchemeDetailCodes);
            Map<String, List<ActivityPrepayDetailRecord>> activityPrepayMap = activityPrepayDetailRecords.stream().collect(Collectors.groupingBy(ActivityPrepayDetailRecord::getSchemeDetailCode));

            List<WithHoldingVo> withHoldingVoList = withHoldingRepository.findProcessPassBySchemeDetailCodes(marketingSchemeDetailCodes);
            // 财报实际计提金额
            Map<String, BigDecimal> withHoldingActualAmountMap = withHoldingVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getVoucherCode())).collect(Collectors.groupingBy(e -> e.getActivitiesDetailCode(), Collectors.mapping(e -> e.getActualAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

            List<BackWithHoldingWriteOff> backWithHoldingWriteOffList = backWithHoldingWriteOffRepository.findByCodes(withHoldingCodes);
            Map<String, List<BackWithHoldingWriteOff>> backWithHoldingWriteOffMap = backWithHoldingWriteOffList.stream().collect(Collectors.groupingBy(BackWithHoldingWriteOff::getWithHoldingCode));

            for (WithHoldingBalanceVo balanceVo : page.getRecords()) {
                // 根据预提类型进行不同的业务逻辑处理
                processBalanceByWithHoldingType(balanceVo, planCaseVoMap, cashDetailMap, commitCashDetailMap, clearAndDeliveryMap, writeOffMap, podWriteOffMap, activityPrepayMap, withHoldingActualAmountMap, auditDetailMap, podDiffWithholdingMap);
                List<BackWithHoldingWriteOff> backWithHoldingWriteOffs = backWithHoldingWriteOffMap.getOrDefault(balanceVo.getWithHoldingCode(), Lists.newArrayList());
                if (!CollectionUtils.isEmpty(backWithHoldingWriteOffs)) {
                    balanceVo.setBackWriteOffAmountTotal(backWithHoldingWriteOffs.stream().map(BackWithHoldingWriteOff::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    balanceVo.setBackBalance(balanceVo.getActualReportAmount().subtract(balanceVo.getBackWriteOffAmountTotal()));
                }
            }


        }

        return page;
    }

    /**
     * 根据预提类型处理计提余额业务逻辑
     * 基于TPM-计提余额报表_副本.docx文档要求
     */
    private void processBalanceByWithHoldingType(WithHoldingBalanceVo balanceVo,
                                                 Map<String, MarketingPlanCaseVo> planCaseVoMap,
                                                 Map<String, List<FeeCashDetailVo>> cashDetailMap,
                                                 Map<String, List<FeeCashDetailVo>> commitCashDetailMap,
                                                 Map<String, List<ReplenishmentPoolDetailVo>> clearAndDeliveryMap,
                                                 Map<String, List<WithHoldingWriteOffVo>> writeOffMap,
                                                 Map<String, List<WithHoldingWriteOffVo>> podWriteOffMap,
                                                 Map<String, List<ActivityPrepayDetailRecord>> activityPrepayMap,
                                                 Map<String, BigDecimal> withHoldingActualAmountMap,
                                                 Map<String, List<MarketingAuditDetailVo>> auditDetailMap, Map<String, WithHoldingVo> podDiffWithholdingMap) {
        String withHoldingType = balanceVo.getWithHoldingType();
        String withHoldingCode = balanceVo.getWithHoldingCode();
        MarketingPlanCaseVo marketingPlanCaseVo = planCaseVoMap.get(balanceVo.getActivitiesDetailCode());

        if (WithHoldingTypeEnum.HANDLE.getDictCode().equals(withHoldingType)) {
            // 预提类型=手工预提的处理逻辑
            processManualWithHolding(balanceVo, withHoldingCode);
        } else if (WithHoldingTypeEnum.NOT_AUDIT.getDictCode().equals(withHoldingType) ||
                   WithHoldingTypeEnum.NOT_CASH.getDictCode().equals(withHoldingType)) {
            if (null == marketingPlanCaseVo) {
                log.warn("脏数据，活动明细编码{}不存在，跳过处理", balanceVo.getActivitiesDetailCode());
                return;
            }
            // 预提类型=已申请未结案/已结案未兑付的处理逻辑
            processAppliedOrSettledWithHolding(balanceVo, marketingPlanCaseVo, cashDetailMap, commitCashDetailMap, clearAndDeliveryMap, writeOffMap, activityPrepayMap, withHoldingActualAmountMap, auditDetailMap);
        } else if (WithHoldingTypeEnum.ORDER_CLOSE_DETAIL.getDictCode().equals(withHoldingType) ||
                   WithHoldingTypeEnum.POD_DIFF_ADD.getDictCode().equals(withHoldingType) ||
                   WithHoldingTypeEnum.POD_DIFF_REDUCE.getDictCode().equals(withHoldingType)) {
            // 预提类型=订单关闭/pod差异-增加/pod差异-减少的处理逻辑
            processOrderOrPodDiffWithHolding(balanceVo, podWriteOffMap, podDiffWithholdingMap);
        }
    }

    /**
     * 处理手工预提类型的计提余额
     * 根据文档要求，手工预提类型的各项金额都为"/"，表示不适用
     */
    private void processManualWithHolding(WithHoldingBalanceVo balanceVo, String withHoldingCode) {
        // 根据文档，手工预提类型的所有项目都不适用，设置为null或0
        balanceVo.setPreCashAmount(null);              // 计提前兑付：/
        balanceVo.setAuditWriteOffAmount(null);        // 结案冲销：/
        balanceVo.setCashWriteOffAmount(null);         // 兑付冲销：/
        balanceVo.setCloseWriteOffAmount(null);        // 关闭冲销：/
        balanceVo.setDeliveryWriteOffAmount(null);     // 发货冲销：/
        balanceVo.setAccountUnverifiedAmount(null);    // 账扣未核销：/
        balanceVo.setWireUnverifiedAmount(null);       // 电汇未核销：/
        balanceVo.setAuditUncashedAmount(null);        // 结案未兑付：/
        balanceVo.setIncompleteAuditBalance(null);     // 未完全结案余额：/

        // FIXME: 文档中手工预提的具体处理逻辑不明确，需要进一步确认业务规则
        log.debug("处理手工预提类型，计提编码：{}，所有业务项目均不适用", withHoldingCode);
    }

    /**
     * 处理已申请未结案/已结案未兑付类型的计提余额
     * 根据文档要求实现计提余额逻辑：计提余额=计提前兑付+计提-结案差异-已兑付/已发货/已关闭
     */
    private void processAppliedOrSettledWithHolding(WithHoldingBalanceVo balanceVo, MarketingPlanCaseVo marketingPlanCaseVo, Map<String, List<FeeCashDetailVo>> cashDetailMap, Map<String, List<FeeCashDetailVo>> commitCashDetailMap, Map<String, List<ReplenishmentPoolDetailVo>> clearAndDeliveryMap, Map<String, List<WithHoldingWriteOffVo>> writeOffMap, Map<String, List<ActivityPrepayDetailRecord>> activityPrepayMap, Map<String, BigDecimal> withHoldingActualAmountMap, Map<String, List<MarketingAuditDetailVo>> auditDetailMap) {
        // TODO: 计提前兑付：兑付/关闭年月=活动预算年月的兑付金额/关闭金额
        // 1）票扣兑付年月=审批通过年月
        // 2）电汇账扣兑付年月=凭证回传年月
        // 3）电汇、账扣、票扣关闭年月=审批通过年月
        // 4）货补兑付年月= crm_dms.tpm_delivery_replenishment_pool_detail 中类型为【delivery】的delivery_time操作年月
        // 5）货补关闭年月= crm_dms.tpm_delivery_replenishment_pool_detail 中类型为【manualClear】的delivery_time操作年月
        String activitiesDetailCode = balanceVo.getActivitiesDetailCode();
        String years = marketingPlanCaseVo.getYears();
        List<MarketingAuditDetailVo> marketingAuditDetailVos = auditDetailMap.getOrDefault(activitiesDetailCode, Lists.newArrayList());
        BigDecimal preCashAmount = calculatePreCashAmount(activitiesDetailCode, years, cashDetailMap, clearAndDeliveryMap, marketingAuditDetailVos);
        balanceVo.setPreCashAmount(preCashAmount);

        List<WithHoldingWriteOffVo> withHoldingWriteOffVos = writeOffMap.get(activitiesDetailCode);
        if (!CollectionUtils.isEmpty(withHoldingWriteOffVos)) {
            List<WithHoldingWriteOffVo> auditedWriteOffs = withHoldingWriteOffVos.stream().filter(withHoldingWriteOffVo -> WriteOffTypeEnum.AUDIT.getDictCode().equals(withHoldingWriteOffVo.getWriteOffType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(auditedWriteOffs)) {
                balanceVo.setAuditWriteOffAmount(auditedWriteOffs.stream().map(WithHoldingWriteOffVo::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            // 兑付冲销
            List<WithHoldingWriteOffVo> cashWriteOffs = withHoldingWriteOffVos.stream().filter(withHoldingWriteOffVo -> WriteOffTypeEnum.CASH.getDictCode().equals(withHoldingWriteOffVo.getWriteOffType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(cashWriteOffs)) {
                balanceVo.setCashWriteOffAmount(cashWriteOffs.stream().map(WithHoldingWriteOffVo::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            // 关闭
            List<WithHoldingWriteOffVo> closeWriteOffs = withHoldingWriteOffVos.stream().filter(withHoldingWriteOffVo -> WriteOffTypeEnum.CLOSE.getDictCode().equals(withHoldingWriteOffVo.getWriteOffType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(closeWriteOffs)) {
                balanceVo.setCloseWriteOffAmount(closeWriteOffs.stream().map(WithHoldingWriteOffVo::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            // 发货
            List<WithHoldingWriteOffVo> deliveryWriteOffs = withHoldingWriteOffVos.stream().filter(withHoldingWriteOffVo -> WriteOffTypeEnum.ORDER.getDictCode().equals(withHoldingWriteOffVo.getWriteOffType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deliveryWriteOffs)) {
                balanceVo.setDeliveryWriteOffAmount(deliveryWriteOffs.stream().map(WithHoldingWriteOffVo::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }

        // TODO: 账扣未核销：取预付明细中兑付类型为【账扣预付】的方案规划明细编码对应的实付支付金额-已冲销金额
        // 1）实际支付金额：取付款状态为【付款成功】、【退票】的实际支付金额
        // 2）已冲销金额：被兑付发票核销关联的冲抵预付金额，单据状态为【审批通过】
        List<ActivityPrepayDetailRecord> activityPrepayDetailRecords = activityPrepayMap.get(activitiesDetailCode);
        if (!CollectionUtils.isEmpty(activityPrepayDetailRecords)) {
            // 获取预付明细中，类型为【账扣预付】的实付支付金额
            List<ActivityPrepayDetailRecord> accountPrepayDetailRecords = activityPrepayDetailRecords.stream().filter(activityPrepayDetailRecord -> TpmPrepayTypeEnum.deduction.getCode().equals(activityPrepayDetailRecord.getPrepayType())).collect(Collectors.toList());
            // 实际支付
            BigDecimal accountPayAmount = accountPrepayDetailRecords.stream().filter(activityPrepayDetailRecord -> "success_pay".equals(activityPrepayDetailRecord.getPayStatus()) || "return_tickets".equals(activityPrepayDetailRecord.getPayStatus())).map(ActivityPrepayDetailRecord::getPrepayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 已冲销
            BigDecimal accountWriteOffAmount = accountPrepayDetailRecords.stream().map(record -> Optional.ofNullable(record.getReversedAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            balanceVo.setAccountUnverifiedAmount(accountPayAmount.subtract(accountWriteOffAmount));

            // 电汇未核销：取预付明细中兑付类型为【电汇预付】/【活动预付】的方案规划明细编码对应的实付支付金额-已冲销金额
            List<ActivityPrepayDetailRecord> wirePrepayDetailRecords = activityPrepayDetailRecords.stream().filter(activityPrepayDetailRecord -> TpmPrepayTypeEnum.telegraphic_transfer.getCode().equals(activityPrepayDetailRecord.getPrepayType()) || TpmPrepayTypeEnum.activity.getCode().equals(activityPrepayDetailRecord.getPrepayType())).collect(Collectors.toList());
            // 实际支付
            BigDecimal wirePayAmount = wirePrepayDetailRecords.stream().filter(activityPrepayDetailRecord -> "success_pay".equals(activityPrepayDetailRecord.getPayStatus()) || "return_tickets".equals(activityPrepayDetailRecord.getPayStatus())).map(record -> Optional.ofNullable(record.getPrepayAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 已冲销
            BigDecimal wireWriteOffAmount = wirePrepayDetailRecords.stream().map(record -> Optional.ofNullable(record.getReversedAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            balanceVo.setWireUnverifiedAmount(wirePayAmount.subtract(wireWriteOffAmount));
        }

        // TODO: 结案未兑付：已结案金额--已兑付金额（含已关闭）
        // 电汇、账扣、票扣的已兑付金额取费用兑付明细中类型为【费用兑付】、【电汇兑付】的本次兑付金额，单据状态为【审批中】、【审批通过】的
        // 电汇、账扣、票扣费用关闭金额取费用兑付明细中类型为【费用关闭】的剩余可兑付金额，单据状态为【审批中】、【审批通过】的
        // 货补费用兑付金额取crm_dms.tpm_delivery_replenishment_pool_detail中类型为【delivery】的operation_amount操作金额
        // 货补费用关闭金额取crm_dms.tpm_delivery_replenishment_pool_detail中类型为【manualClear】的operation_amount操作金额

        // 已结案
        BigDecimal auditAmount = BigDecimal.ZERO;
        // 已兑付
        BigDecimal alreadyCashPaidAmount = BigDecimal.ZERO;

        if (!CollectionUtils.isEmpty(marketingAuditDetailVos)) {
           auditAmount = marketingAuditDetailVos.stream().map(MarketingAuditDetailVo::getAuditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        List<FeeCashDetailVo> feeCashDetailVos = cashDetailMap.getOrDefault(activitiesDetailCode, Lists.newArrayList());
        List<FeeCashDetailVo> commitFeeCashDetailVos = commitCashDetailMap.getOrDefault(activitiesDetailCode, Lists.newArrayList());
        feeCashDetailVos.addAll(commitFeeCashDetailVos);

        if (!CollectionUtils.isEmpty(feeCashDetailVos)) {

            for (FeeCashDetailVo k : feeCashDetailVos) {
                if (Lists.newArrayList(CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode()).contains(k.getCashMethod()) &&
                        Lists.newArrayList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode(), CashTypeEnum.MATERIAL.getDictCode()).contains(k.getCashType())) {
                    alreadyCashPaidAmount = alreadyCashPaidAmount.add(k.getThisCashAmount());
                } else if (Lists.newArrayList(CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode(), "close").contains(k.getCashMethod())
                        && k.getCashType().equals(CashTypeEnum.CLOSE.getDictCode())) {
                    alreadyCashPaidAmount = alreadyCashPaidAmount.add(k.getAvailableCashAmount());
                }
            }
        }

        BigDecimal clearAndDeliveryOperationAmount = new BigDecimal("0");
        List<ReplenishmentPoolDetailVo> replenishmentPoolDetailVos = clearAndDeliveryMap.get(activitiesDetailCode);
        if (!CollectionUtils.isEmpty(replenishmentPoolDetailVos)) {
            clearAndDeliveryOperationAmount = replenishmentPoolDetailVos.stream().map(l -> l.getOperationAmount().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        List<String> auditDetailCodes = marketingAuditDetailVos.stream().map(MarketingAuditDetailVo::getAuditDetailCode).distinct().collect(Collectors.toList());
        BigDecimal clearAndDeliveryAuditAmount = new BigDecimal("0");
        if (!CollectionUtils.isEmpty(auditDetailCodes)) {
            clearAndDeliveryAuditAmount = auditDetailCodes.stream().map(e -> clearAndDeliveryMap.getOrDefault(e, Lists.newArrayList())
                    .stream()
                    .map(k -> k.getOperationAmount().abs()).reduce(BigDecimal.ZERO, BigDecimal::add)
            ).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        balanceVo.setAuditUncashedAmount(auditAmount.subtract(alreadyCashPaidAmount.add(clearAndDeliveryOperationAmount).add(clearAndDeliveryAuditAmount)));

        // TODO: 未完全结案金额余额：计提前兑付+财报实际计提金额-已结案金额
        // 已结案金额：取活动结案金额，单据状态为【审批中】、【审批通过】的
        // 财报实际金额：取费用计提表中计提编码对应【财报实际计提金额】

        String auditStatus = AuditStatusEnum.NOT_AUDIT.getCode();
        if (auditDetailMap.containsKey(activitiesDetailCode)) {
            auditStatus = AuditStatusEnum.PART_AUDIT.getCode();
            List<MarketingAuditDetailVo> auditDetailVoList = auditDetailMap.get(activitiesDetailCode);
            for (MarketingAuditDetailVo auditDetailVo : auditDetailVoList) {
                if (BooleanEnum.TRUE.getCapital().equals(auditDetailVo.getBeFullAudit())) {
                    auditStatus = AuditStatusEnum.WHOLE_AUDIT.getCode();
                }
            }
        }
        if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
            balanceVo.setIncompleteAuditBalance(BigDecimal.ZERO);
            return;
        }
        BigDecimal withHoldingActualAmount= withHoldingActualAmountMap.getOrDefault(activitiesDetailCode, BigDecimal.ZERO);
        balanceVo.setIncompleteAuditBalance(balanceVo.getPreCashAmount().add(withHoldingActualAmount).subtract(auditAmount));

    }

    /**
     * 处理订单关闭/pod差异-增加/pod差异-减少类型的计提余额
     * 根据文档要求，将不适用的处理逻辑放在方法开头
     */
    private void processOrderOrPodDiffWithHolding(WithHoldingBalanceVo balanceVo, Map<String, List<WithHoldingWriteOffVo>> podWriteOffMap, Map<String, WithHoldingVo> podDiffWithholdingMap) {
        // 根据文档，以下项目不适用，设置为null
        balanceVo.setAuditWriteOffAmount(null);        // 2、结案冲销：/ (不适用)
        balanceVo.setCashWriteOffAmount(null);         // 3、兑付冲销：/ (不适用)
        balanceVo.setAccountUnverifiedAmount(null);    // 6、账扣未核销：/ (不适用)
        balanceVo.setWireUnverifiedAmount(null);       // 7、电汇未核销：/ (不适用)
        balanceVo.setAuditUncashedAmount(null);        // 8、结案未兑付：/ (不适用)
        balanceVo.setIncompleteAuditBalance(null);     // 9、未完全结案金额余额：/ (不适用)

        // TODO: 1、计提前兑付：兑付/关闭年月=订单关闭/pod差异-增加/pod差异-减少产生的费用年月的兑付金额/关闭金额
        // 1）兑付/关闭年月：
        // 兑付年月= crm_dms.tpm_delivery_replenishment_pool_detail 中【parent_code】=费用计提的【业务编码】且操作类型为【delivery】的delivery_time操作年月
        // 关闭年月= crm_dms.tpm_delivery_replenishment_pool_detail 中【parent_code】=费用计提的【业务编码】且操作类型为【manualClear】的delivery_time操作年月

        // TODO: 2）订单关闭产生的费用年月：
        // 订单关闭产生的费用年月= crm_dms.tpm_delivery_replenishment_pool_detail 中【pool_detail_code】=费用计提的【业务编码】且操作类型为【orderCloseDetail】的创建年月
        // pod差异-增加产生的费用年月= crm_dms.tpm_delivery_replenishment_pool_detail 中【pool_detail_code】=费用计提的【业务编码】且操作类型为【podDiffAdd】的创建年月
        // pod差异-减少产生的费用年月= crm_dms.tpm_delivery_replenishment_pool_detail 中【pool_detail_code】=费用计提的【业务编码】且操作类型为【podDiffReduce】的创建年月

        WithHoldingVo withHoldingVo = podDiffWithholdingMap.get(balanceVo.getWithHoldingCode());
        if (Objects.nonNull(withHoldingVo)) {
            BigDecimal preCashAmount = replenishmentPoolDetailVoService.orderCloseAndPodDiffPreCash(withHoldingVo.getBusinessCode());
            balanceVo.setPreCashAmount(preCashAmount);
        }



        log.info("processOrderOrPodDiffWithHolding.withholdingCode: {}", balanceVo.getWithHoldingCode());
        log.info("processOrderOrPodDiffWithHolding.podWriteOffMap: {}", JSON.toJSONString(podWriteOffMap));
        List<WithHoldingWriteOffVo> withHoldingWriteOffVos = podWriteOffMap.get(balanceVo.getWithHoldingCode());
        if (!CollectionUtils.isEmpty(withHoldingWriteOffVos)) {
            // 关闭
            List<WithHoldingWriteOffVo> closeWriteOffs = withHoldingWriteOffVos.stream().filter(withHoldingWriteOffVo -> WriteOffTypeEnum.CLOSE.getDictCode().equals(withHoldingWriteOffVo.getWriteOffType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(closeWriteOffs)) {
                balanceVo.setCloseWriteOffAmount(withHoldingWriteOffVos.stream().map(WithHoldingWriteOffVo::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            // 发货
            List<WithHoldingWriteOffVo> deliveryWriteOffs = withHoldingWriteOffVos.stream().filter(withHoldingWriteOffVo -> WriteOffTypeEnum.ORDER.getDictCode().equals(withHoldingWriteOffVo.getWriteOffType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deliveryWriteOffs)) {
                balanceVo.setDeliveryWriteOffAmount(withHoldingWriteOffVos.stream().map(WithHoldingWriteOffVo::getWriteOffAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
    }

    /**
     * 计算计提前兑付金额
     * 兑付/关闭年月=活动预算年月的兑付金额/关闭金额
     */
    private BigDecimal calculatePreCashAmount(String schemeDetailCode, String years, Map<String, List<FeeCashDetailVo>> cashDetailMap, Map<String, List<ReplenishmentPoolDetailVo>> clearAndDeliveryMap, List<MarketingAuditDetailVo> marketingAuditDetailVos) {
        // TODO: 实现计提前兑付金额计算逻辑
        // 1）票扣兑付年月=审批通过年月
        // 2）电汇账扣兑付年月=凭证回传年月
        // 3）电汇、账扣、票扣关闭年月=审批通过年月
        // 4）货补兑付年月= crm_dms.tpm_delivery_replenishment_pool_detail 中类型为【delivery】的delivery_time操作年月
        // 5）货补关闭年月= crm_dms.tpm_delivery_replenishment_pool_detail 中类型为【manualClear】的delivery_time操作年月

        BigDecimal beforeWithHoldingAmount = BigDecimal.ZERO;
        List<FeeCashDetailVo> feeCashDetailVos = cashDetailMap.getOrDefault(schemeDetailCode, Lists.newArrayList());
        if (!CollectionUtils.isEmpty(feeCashDetailVos)) {
            for (FeeCashDetailVo k : feeCashDetailVos) {
                if (k.getCashMethod().equals(CashMethodEnum.TICKET_BUCKLE.getDictCode())
                        && k.getCashType().equals(CashTypeEnum.FEE.getDictCode())
                        && Objects.nonNull(k.getPassDate()) && years.substring(0, 7).equals(DateUtil.formatDate(k.getPassDate()).substring(0, 7))) {
                    beforeWithHoldingAmount = beforeWithHoldingAmount.add(Optional.ofNullable(k.getThisCashAmount()).orElse(BigDecimal.ZERO));
                } else if (Lists.newArrayList(CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode()).contains(k.getCashMethod())
                        && Lists.newArrayList(CashTypeEnum.FEE.getDictCode(), CashTypeEnum.WIREDUIFU.getDictCode()).contains(k.getCashType())
                        && Objects.nonNull(k.getVoucherCallbackDate()) && years.substring(0, 7).equals(DateUtil.formatDate(k.getVoucherCallbackDate()).substring(0, 7))) {
                    beforeWithHoldingAmount = beforeWithHoldingAmount.add(Optional.ofNullable(k.getThisCashAmount()).orElse(BigDecimal.ZERO));
                } else if (Lists.newArrayList(CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode()).contains(k.getCashMethod())
                        && k.getCashType().equals(CashTypeEnum.CLOSE.getDictCode())
                        && Objects.nonNull(k.getPassDate()) && years.substring(0, 7).equals(DateUtil.formatDate(k.getPassDate()).substring(0, 7))
                ) {
                    beforeWithHoldingAmount = beforeWithHoldingAmount.add(Optional.ofNullable(k.getAvailableCashAmount()).orElse(BigDecimal.ZERO));
                }
            }
        }
        BigDecimal clearAndDeliveryAmount = new BigDecimal("0");
        List<ReplenishmentPoolDetailVo> replenishmentPoolDetailVos = clearAndDeliveryMap.get(schemeDetailCode);
        if (!CollectionUtils.isEmpty(replenishmentPoolDetailVos)) {
            clearAndDeliveryAmount = replenishmentPoolDetailVos.stream().filter(v -> Objects.nonNull(v.getDeliveryTime()) && v.getDeliveryTime().substring(0, 7).equals(years)).map(l -> l.getOperationAmount().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        List<String> auditDetailCodes = marketingAuditDetailVos.stream().map(MarketingAuditDetailVo::getAuditDetailCode).distinct().collect(Collectors.toList());
        BigDecimal clearAndDeliveryAuditAmount = new BigDecimal("0");
        if (!CollectionUtils.isEmpty(auditDetailCodes)) {
            clearAndDeliveryAuditAmount = auditDetailCodes.stream().map(e -> clearAndDeliveryMap.getOrDefault(e, Lists.newArrayList())
                    .stream().filter(l -> Objects.nonNull(l.getDeliveryTime()) && l.getDeliveryTime().substring(0, 7).equals(years))
                    .map(k -> k.getOperationAmount().abs()).reduce(BigDecimal.ZERO, BigDecimal::add)
            ).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        beforeWithHoldingAmount = beforeWithHoldingAmount.add(clearAndDeliveryAmount).add(clearAndDeliveryAuditAmount);
        return beforeWithHoldingAmount;
    }
}

