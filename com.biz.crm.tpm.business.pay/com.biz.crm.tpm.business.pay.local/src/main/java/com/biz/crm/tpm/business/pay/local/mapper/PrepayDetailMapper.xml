<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.business.pay.local.mapper.PrepayDetailMapper">
  <resultMap type="com.biz.crm.tpm.business.pay.local.entity.PrepayDetail" id="PrepayDetailMap">
  </resultMap>
  
  <select id="findByConditions" resultType="com.biz.crm.tpm.business.pay.sdk.vo.PrepayDetailVo">
    select 
    t.*
    from tpm_prepay_detail t 
    <where>
        <if test="dto.tenantCode != null and dto.tenantCode != '' ">
          and t.tenant_code = #{dto.tenantCode}
        </if>
        and t.del_flag = '${@<EMAIL>()}'
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>