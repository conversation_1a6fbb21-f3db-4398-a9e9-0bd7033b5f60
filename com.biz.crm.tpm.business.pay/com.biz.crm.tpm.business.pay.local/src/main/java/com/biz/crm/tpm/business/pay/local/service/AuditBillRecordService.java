package com.biz.crm.tpm.business.pay.local.service;

import com.biz.crm.tpm.business.pay.local.dto.AuditBillRecordDto;
import com.biz.crm.tpm.business.pay.local.entity.AuditBillRecord;


/**
 * 核销账单明细记录;(tpm_audit_bill_record)表服务接口
 * <AUTHOR> <PERSON>
 * @date : 2022-6-23
 */
public interface AuditBillRecordService{

  /**
   * 新增数据
   *
   * @param auditBillRecordDto 实体对象
   * @return 新增结果
   */
  AuditBillRecord create(AuditBillRecordDto auditBillRecordDto);

}
