package com.biz.crm.tpm.business.pay.local.calcomponent;

import cn.hutool.core.date.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.pay.local.entity.FeeCash;
import com.biz.crm.tpm.business.pay.local.entity.MarketingAuditDetail;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashRepository;
import com.biz.crm.tpm.business.pay.local.repository.MarketingAuditDetailRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.DeliveryReplenishmentConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.DeliveryReplenishmentPoolDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingWriteOffDto;
import com.biz.crm.tpm.business.pay.sdk.enums.CashTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.DeliveryDetailTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffStatusEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WriteOffTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.DeliveryReplenishmentPoolDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffSendHecService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 关闭、冲销明细生成
 * <AUTHOR>
 * @Date 2025/1/3 01:20
 **/
@Component
@Slf4j
public class CalCloseReversComponent {

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;

    @Autowired(required = false)
    private WithHoldingWriteOffSendHecService withHoldingWriteOffSendHecService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private FeeCashDetailRepository feeCashDetailRepository;

    @Autowired(required = false)
    private MarketingAuditDetailRepository marketingAuditDetailRepository;

    @Autowired(required = false)
    private FeeCashRepository feeCashRepository;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;


    /**
     * 生成费用兑付/关闭明细
     *
     * @param poolDetailDtos
     */
    @Async("tpmMarketingCheckCaseThread")
    public void generateDeliveryReplenishmentList(List<DeliveryReplenishmentPoolDetailDto> poolDetailDtos) {
        loginUserService.refreshAuthentication(null);
        deliveryReplenishmentPoolDetailService.create(poolDetailDtos);


    }

    @Async("tpmMarketingCheckCaseThread")
    public Future generateWriteOff(List<WithHoldingWriteOffDto> dtoList) {
        loginUserService.refreshAuthentication(null);
        withHoldingWriteOffSendHecService.writeOff(dtoList);
        return new AsyncResult<>(Boolean.TRUE);
    }


    /**
     * 费用兑付生成费用兑付/关闭明细
     *
     * @param cashDetailVos
     * @param feeMap
     */
    public List<DeliveryReplenishmentPoolDetailDto> feeCashGenerateDeliveryReplenishmentList(List<FeeCashDetailVo> cashDetailVos, Map<String, String> feeMap) {
        loginUserService.refreshAuthentication(null);
        if (CollectionUtils.isEmpty(cashDetailVos)) {
            return new ArrayList<>();
        }
        List<DeliveryReplenishmentPoolDetailDto> closeDeliveryList = new ArrayList<>();
        List<DeliveryReplenishmentPoolDetailDto> deliveryList = new ArrayList<>();
        List<String> auditDetailList = cashDetailVos.stream().map(FeeCashDetailVo::getAuditDetailCode).collect(Collectors.toList());
        Map<String, BigDecimal> historyMap = feeCashDetailRepository.findByAuditDetailCodes(auditDetailList)
                .stream().filter(x -> ObjectUtils.isNotEmpty(x.getThisCashAmount()))
                .collect(Collectors.groupingBy(e -> e.getAuditDetailCode(), Collectors.mapping(e -> e.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        List<MarketingAuditDetail> auditDetails = marketingAuditDetailRepository.findByAuditDetailCodes(auditDetailList);
        Map<String, MarketingAuditDetail> auditDetailMap = auditDetails.stream().collect(Collectors.toMap(e -> e.getAuditDetailCode(), Function.identity(), (a, b) -> a));
        for (FeeCashDetailVo detail : cashDetailVos) {
            MarketingAuditDetail auditDetail = auditDetailMap.getOrDefault(detail.getAuditDetailCode(), new MarketingAuditDetail());
            DeliveryReplenishmentPoolDetailDto delivery = new DeliveryReplenishmentPoolDetailDto();
            delivery.setRuleCode(detail.getRuleCode());
            delivery.setBusinessCode(detail.getCashDetailCode());
            delivery.setCustomerCode(detail.getCustomerCode());
            delivery.setCustomerName(detail.getCustomerName());
            delivery.setDeliveryTime(detail.getPostingDateDate() != null ? DateUtil.format(detail.getPostingDateDate(), "yyyy-MM-dd") : DateUtil.format(new Date(), "yyyy-MM-dd"));
            delivery.setOperationAmount(detail.getThisCashAmount());
            delivery.setSchemeDetailCode(detail.getSchemeDetailCode());
            delivery.setAuditCode(detail.getAuditCode());
            delivery.setAuditDetailCode(detail.getAuditDetailCode());
            delivery.setCashCode(detail.getCashCode());
            delivery.setCashDetailCode(detail.getCashDetailCode());
            delivery.setSchemeCode(detail.getSchemeCode());
            delivery.setCreateAccount(auditDetail.getCreateAccount());
            delivery.setPositionCode(auditDetail.getPositionCode());
            delivery.setBeWriteOff(BooleanEnum.FALSE.getCapital());
            delivery.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());

            String cashType = feeMap.get(detail.getCashCode());
            if (CashTypeEnum.CLOSE.getDictCode().equals(cashType)) {
                //如果是完全结案，并且结案明细结案金额-“完全兑付金额（本次兑付金额+已兑付金额）”大于0，生成费用关闭
                if (BooleanEnum.TRUE.getCapital().equals(detail.getBeCash())) {
                    BigDecimal subtract = (Optional.ofNullable(auditDetail.getAuditAmount()).orElse(BigDecimal.ZERO)).subtract(historyMap.get(detail.getAuditDetailCode()));
                    if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                        DeliveryReplenishmentPoolDetailDto closeDelivery = nebulaToolkitService.copyObjectByWhiteList(delivery, DeliveryReplenishmentPoolDetailDto.class, LinkedHashSet.class, ArrayList.class);

                        //判断是否冲销
//                    if (!CollectionUtils.isEmpty(writeOffMap) && writeOffMap.containsKey(detail.getSchemeDetailCode()) && writeOffMap.get(detail.getSchemeDetailCode()).size() > 1) {
//                        closeDelivery.setBeWriteOff(BooleanEnum.TRUE.getCapital());
//                        closeDelivery.setWriteOffStatus(WriteOffStatusEnum.ON_WRITE_OFF.getDictCode());
//                        WithHoldingWriteOffVo close = writeOffMap.get(detail.getSchemeDetailCode()).stream().filter(e -> e.getWriteOffType().equals(WriteOffTypeEnum.CLOSE.getDictCode())).collect(Collectors.toList()).get(0);
//                        closeDelivery.setOperationAmount(close.getWriteOffAmount());
//                    } else {
//                        closeDelivery.setBeWriteOff(BooleanEnum.FALSE.getCapital());
//                        closeDelivery.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
//                        closeDelivery.setOperationAmount(subtract);
//                    }
                        closeDelivery.setBeWriteOff(BooleanEnum.FALSE.getCapital());
                        closeDelivery.setWriteOffStatus(WriteOffStatusEnum.WAIT_WRITE_OFF.getDictCode());
                        closeDelivery.setOperationAmount(subtract);
                        closeDelivery.setOperationType(DeliveryDetailTypeEnum.FEE_CLOSE.getCode());

                        List<String> ruleCodes = generateCodeService.generateCode(DeliveryReplenishmentConstant.PREFIX_CODE, 1);
                        closeDelivery.setRuleCode(ruleCodes.get(0));
                        closeDeliveryList.add(closeDelivery);
                        deliveryList.add(closeDelivery);
                    }
                }
            }
            delivery.setOperationType(DeliveryDetailTypeEnum.FEE_CASH.getCode());
            deliveryList.add(delivery);
        }
        deliveryReplenishmentPoolDetailService.create(deliveryList);
        return closeDeliveryList;
    }


    /**
     * 兑付冲销
     *
     * @param cashDetailVos
     */
    @Async("tpmMarketingCheckCaseThread")
    public Future feeCashGenerateWriteOff(List<FeeCashDetailVo> cashDetailVos, List<DeliveryReplenishmentPoolDetailDto> closeDeliveryList) {
        loginUserService.refreshAuthentication(null);
        List<WithHoldingWriteOffDto> writeOffDtoList = new ArrayList<>();
        List<String> auditDetailList = cashDetailVos.stream().map(FeeCashDetailVo::getAuditDetailCode).collect(Collectors.toList());
        List<FeeCashDetailVo> historyCashDetailVos = feeCashDetailRepository.findByAuditDetailCodes(auditDetailList);
        Set<String> historyCashCodes = historyCashDetailVos.stream().map(e -> e.getCashCode()).collect(Collectors.toSet());
        //只取审批通过和兑付的历史数据
        List<FeeCash> cashVos = feeCashRepository.findByCodes(new ArrayList<>(historyCashCodes));
        cashVos = cashVos.stream().filter(e -> ProcessStatusEnum.PASS.getDictCode().equals(e.getStatus()) &&(CashTypeEnum.FEE.getDictCode().equals(e.getCashType()) || CashTypeEnum.WIREDUIFU.getDictCode().equals(e.getCashType()) )).collect(Collectors.toList());
        List<String> cashCodes = cashVos.stream().map(e -> e.getCashCode()).collect(Collectors.toList());
        Map<String, BigDecimal> historyMap = historyCashDetailVos.stream().filter(e -> cashCodes.contains(e.getCashCode())).collect(Collectors.groupingBy(e -> e.getAuditDetailCode(), Collectors.mapping(e -> e.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        cashDetailVos.forEach(e -> {
            WithHoldingWriteOffDto dto = new WithHoldingWriteOffDto();
            dto.setRuleCode(e.getRuleCode());
            dto.setActivitiesDetailCode(e.getSchemeDetailCode());
            dto.setWriteOffType(WriteOffTypeEnum.CASH.getDictCode());
            dto.setWriteOffAmount(e.getThisCashAmount());
            dto.setBeWholeCash(e.getBeCash());
            dto.setSourceCode(e.getCashDetailCode());
            dto.setAuditAmount(e.getAuditAmount());
            dto.setHistoryAmount(historyMap.getOrDefault(e.getAuditDetailCode(), BigDecimal.ZERO));
            dto.setWriteOffYears(e.getPostingDate());
            writeOffDtoList.add(dto);
        });
        withHoldingWriteOffSendHecService.writeOff(writeOffDtoList);
        return new AsyncResult<>(Boolean.TRUE);
    }
}
