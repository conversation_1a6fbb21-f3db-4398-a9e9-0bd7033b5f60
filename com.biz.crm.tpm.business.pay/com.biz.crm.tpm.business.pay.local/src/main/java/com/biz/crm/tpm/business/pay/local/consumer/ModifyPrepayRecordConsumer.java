package com.biz.crm.tpm.business.pay.local.consumer;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.rocketmq.service.AbstractRocketMqConsumer;
import com.biz.crm.business.common.rocketmq.vo.MqMessageVo;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDto;
import com.biz.crm.tpm.business.pay.sdk.dto.ModifyPrepayRecordDto;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingCollectDto;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayDetailRecordService;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayService;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingCollectService;
import com.biz.crm.workflow.sdk.dto.OaCallbackDto;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @describe: mq消费示例
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2022.10.14 10:09
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MqConstant.TPM_ACTIVITY_PREPAY_RECORD + "${rocketmq.environment}",
        /**
         * tag
         * 可用 || 监听多个tag： "tag1 || tag2 || tag3"
         * 请把tag  定义在 *** 内 需要统一维护
        */
        selectorExpression = MqConstant.TPM_ACTIVITY_PREPAY_RECORD,
        /**
         * 相同分组下 consumer 可自动负载均衡
         * 请把consumerGroup  定义在 *** 内 需要统一维护
        */
        consumerGroup = MqConstant.TPM_ACTIVITY_PREPAY_RECORD_GROUP + "${rocketmq.environment}",
        /**
         * 默认集群消费
         * 可以设置 ConsumeMode.ORDERLY 使用广播消费
         * 也可使用集群模式模拟广播模式：
         * 启动多个不同 consumerGroup 的consumer实例
        */
        consumeMode = ConsumeMode.ORDERLY,
        /**
         * 集群消费or广播消费;默认是集群消费
        */
        messageModel = MessageModel.CLUSTERING)
public class ModifyPrepayRecordConsumer extends AbstractRocketMqConsumer {

    @Autowired(required = false)
    private WithHoldingCollectService withHoldingCollectService;
    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;
    @Autowired(required = false)
    private ActivityPrepayService activityPrepayService;
    @Autowired(required = false)
    private ActivityPrepayDetailRecordService activityPrepayDetailRecordService;

    @Override
    protected Object handleMessage(MqMessageVo message) {
        if (Objects.isNull(message)
                || StringUtil.isEmpty(message.getMsgBody())) {
            log.error("order mq message is null  : {}", message);
            return "消费为空,消费失败!";
        }
        log.info("order mq message received  : {}", message);
        String msgBody = message.getMsgBody();
        ModifyPrepayRecordDto recordDto = JSONObject.parseObject(msgBody, ModifyPrepayRecordDto.class);
        if (StringUtils.isNotBlank(recordDto.getWithholdingJson())) {
            //预提
            WithHoldingCollectDto dto = JSONObject.parseObject(recordDto.getWithholdingJson(), WithHoldingCollectDto.class);
            withHoldingCollectService.modifyActivityPrepayRecord(dto);
        }
        if (StringUtils.isNotBlank(recordDto.getAllEndCaseJson())) {
            //完全结案
            OaCallbackDto dto = JSONObject.parseObject(recordDto.getAllEndCaseJson(), OaCallbackDto.class);
            marketingAuditService.modifyActivityPrepayRecord(dto);
        }
        if (StringUtils.isNotBlank(recordDto.getPrepayJson())) {
            //预付审批结束
            ActivityPrepayDto dto = JSONObject.parseObject(recordDto.getPrepayJson(), ActivityPrepayDto.class);
            List<String> codes = activityPrepayService.rollbackModifyActivityPrepayRecordPre(dto);
            if (CollectionUtils.isEmpty(codes)) {
                log.info("预付明细操作回滚-新增操作明细：无需执行");
                return "预付明细操作回滚-新增操作明细：无需执行";
            }
            activityPrepayDetailRecordService.operateLock(codes, true);
            try {
                activityPrepayService.rollbackModifyActivityPrepayRecord(dto);
            } finally {
                activityPrepayDetailRecordService.operateLock(codes, false);
            }
        }
        return "顺序消息消费成功.";
    }
}