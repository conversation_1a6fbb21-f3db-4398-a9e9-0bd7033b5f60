package com.biz.crm.tpm.business.pay.local.service.observer;


import com.biz.crm.tpm.business.pay.local.service.PrepayBillService;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.event.PrepayEventListener;
import com.biz.crm.tpm.business.pay.sdk.service.PrepayService;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayVo;
import com.biz.crm.workflow.sdk.dto.ProcessStatusDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.listener.ProcessCompleteListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.List;

/**
 * PrepayActivities;流程事件监听
 *
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Component
public class PrepayActivitiesProcessCallBackListener implements ProcessCompleteListener {

  @Autowired(required = false)
  private PrepayService prepayService;
  @Autowired
  private PrepayBillService prepayBillService;
  @Autowired(required = false)
  private List<PrepayEventListener> prepayEventListeners;

  @Override
  public String getBusinessCode() {
    return PayConstant.PROCESS_PREPAY_ACTIVITIES;
  }

  @Transactional
  @Override
  public void onProcessComplete(ProcessStatusDto dto) {
    //校验回调数据类型 是否本业务的回调
    if (!dto.getBusinessCode().equals(PayConstant.PROCESS_PREPAY_ACTIVITIES)) {
      return;
    }
    String code = dto.getBusinessNo();
    String processStatus = dto.getProcessStatus();
    PrepayVo prepayVo = this.prepayService.findByCode(code);
    //审批通过
    if (String.valueOf(processStatus).equals(ProcessStatusEnum.PASS.getDictCode())) {
      this.prepayBillService.prepayAmountByPrepayCode(prepayVo.getPrepayCode());
    }
    //审批状态更改时执行此操作
    if (!CollectionUtils.isEmpty(prepayEventListeners)) {
      this.prepayEventListeners.forEach(event -> event.onUpdateProcessStatus(prepayVo, processStatus));
    }
  }
}
