package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.local.service.AuditInvoiceService;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditInvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditInvoiceVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 费用核销发票;(tpm_audit_invoice)控制层
 * <AUTHOR> Keller
 * @date : 2022-7-6
 */
@Api(tags = "费用核销发票功能接口")
@RestController
@RequestMapping("/v1/auditInvoice")
@Slf4j
public class AuditInvoiceController{
  /**
   * 服务对象
   */
  @Autowired
  private AuditInvoiceService auditInvoiceService;

  /**
   * 分页查询所有数据
   *
   * @param pageable        分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<AuditInvoiceVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                       @ApiParam(name = "auditInvoice", value = "核销采集信息") AuditInvoiceDto dto) {
    try {
      Page<AuditInvoiceVo> page = this.auditInvoiceService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<AuditInvoiceVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id", required=true) String id) {
    try {
      AuditInvoiceVo auditInvoiceVo = this.auditInvoiceService.findById(id);
      return Result.ok(auditInvoiceVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByCode")
  public Result<List<AuditInvoiceVo>> findByCode(@PathVariable @ApiParam(name = "code", value = "编号", required=true) String code) {
    try {
      List<AuditInvoiceVo> auditInvoiceVos = this.auditInvoiceService.findByAuditCode(code);
      return Result.ok(auditInvoiceVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param auditInvoiceDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<AuditInvoiceVo> create(@ApiParam(name = "auditInvoiceDto", value = "费用核销发票") @RequestBody AuditInvoiceDto auditInvoiceDto) {
    try {
      AuditInvoiceVo result = this.auditInvoiceService.create(auditInvoiceDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   *
   * @param auditInvoiceDto 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<AuditInvoiceVo> update(@ApiParam(name = "auditInvoiceDto", value = "费用核销发票") @RequestBody AuditInvoiceDto auditInvoiceDto) {
    try {
      AuditInvoiceVo result = this.auditInvoiceService.update(auditInvoiceDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation(value = "删除数据")
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    try {
      this.auditInvoiceService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
