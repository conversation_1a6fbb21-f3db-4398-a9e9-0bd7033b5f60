package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.local.entity.Account;
import com.biz.crm.tpm.business.pay.sdk.dto.AccountDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 费用上账主表(TpmAccount)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-06-16 16:56:29
 */
public interface AccountMapper extends BaseMapper<Account> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页对象
   * @param account 查询实体
   * @return 所有数据
  */
  public Page<Account> findByConditions(@Param("page") Page<Account> page, @Param("dto") AccountDto account);

  void enableOrDisableByIds(List<String> idList, @Param("enableCode") String enableCode);
}

