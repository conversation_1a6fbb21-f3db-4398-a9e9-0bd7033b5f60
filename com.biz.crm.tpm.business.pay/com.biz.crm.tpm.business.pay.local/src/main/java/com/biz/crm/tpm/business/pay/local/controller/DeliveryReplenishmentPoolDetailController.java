package com.biz.crm.tpm.business.pay.local.controller;

import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.constant.DeliveryReplenishmentConstant;
import com.biz.crm.tpm.business.pay.sdk.service.DeliveryReplenishmentPoolDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Api(tags = "发货费用报表接口")
@RestController
@RequestMapping("/v1/deliveryReplenishment")
@Slf4j
public class DeliveryReplenishmentPoolDetailController {

    @Autowired(required = false)
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;
    @Resource
    private RedisLockService redisLockService;


    /**
     * 获取DMS发货费用定时任务
     */
    @ApiOperation(value = "获取DMS发货费用定时任务")
    @GetMapping("generateDeliveryReplenishment")
    public Result<?> generateDeliveryReplenishment() {
        this.deliveryReplenishmentPoolDetailService.generateDeliveryReplenishment();
        return Result.ok();
    }

    /**
     * 获取DMS发货费用
     */
    @ApiOperation(value = "获取DMS发货费用")
    @GetMapping("generateDeliveryReplenishmentDate")
    public Result<?> generateDeliveryReplenishmentDate(@ApiParam("开始时间") @RequestParam String startDate, @ApiParam("结束时间") @RequestParam String endDate) {
        this.deliveryReplenishmentPoolDetailService.generateDeliveryReplenishmentDate(startDate, endDate);
        return Result.ok();
    }

    /**
     * 发货费用冲销定时任务
     */
    @ApiOperation(value = "发货费用冲销定时任务")
    @GetMapping("deliveryReplenishmentWriteOff")
    public Result<?> deliveryReplenishmentWriteOff() {
        redisLockService.lock(DeliveryReplenishmentConstant.DELIVERY_REPLENISHMENT_ALL_LOCK, TimeUnit.MINUTES, 30);
        try {
            this.deliveryReplenishmentPoolDetailService.deliveryReplenishmentWriteOff(null);
        } finally {
            redisLockService.unlock(DeliveryReplenishmentConstant.DELIVERY_REPLENISHMENT_ALL_LOCK);
        }
        return Result.ok();
    }


    @ApiOperation(value = "补偿发货费用冲销")
    @PostMapping("compensateDeliveryReplenishment")
    public Result compensateDeliveryReplenishment(@RequestBody List<String> businessCodes) {
        redisLockService.lock(DeliveryReplenishmentConstant.DELIVERY_REPLENISHMENT_ALL_LOCK, TimeUnit.MINUTES, 30);
        boolean lockFlag = redisLockService.batchLock(DeliveryReplenishmentConstant.DELIVERY_REPLENISHMENT_LOCK, businessCodes, TimeUnit.MINUTES, 20);
        if (lockFlag) {
            try {
                this.deliveryReplenishmentPoolDetailService.deliveryReplenishmentWriteOff(businessCodes);
            } finally {
                redisLockService.batchUnLock(DeliveryReplenishmentConstant.DELIVERY_REPLENISHMENT_LOCK, businessCodes);
                redisLockService.unlock(DeliveryReplenishmentConstant.DELIVERY_REPLENISHMENT_ALL_LOCK);
            }
        } else {
            return Result.error("业务数据正在操作,请勿重复点击");
        }
        return Result.ok();
    }
}
