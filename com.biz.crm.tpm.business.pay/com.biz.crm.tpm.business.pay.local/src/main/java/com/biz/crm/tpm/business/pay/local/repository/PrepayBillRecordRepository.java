package com.biz.crm.tpm.business.pay.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.pay.local.entity.PrepayBillRecord;
import com.biz.crm.tpm.business.pay.local.mapper.PrepayBillRecordMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 预付账单明细记录;(tpm_prepay_bill_record)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Component
public class PrepayBillRecordRepository extends ServiceImpl<PrepayBillRecordMapper, PrepayBillRecord> {
  @Autowired
  private PrepayBillRecordMapper prepayBillRecordMapper;

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<AuditBillRecord>
   */
  public List<PrepayBillRecord> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(PrepayBillRecord::getId, ids)
            .eq(PrepayBillRecord::getTenantCode, tenantCode)
            .list();
  }


}
