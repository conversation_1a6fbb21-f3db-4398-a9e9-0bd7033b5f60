package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingIncome;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingIncomeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 管报收入mapper
 */
public interface WithHoldingIncomeMapper extends BaseMapper<WithHoldingIncome> {

    List<WithHoldingIncome> findByYear(@Param("years") List<String> year);


    List<WithHoldingIncomeVo> findListByCondition(@Param("dto") WithholdingIncomeQueryDto dto);

}
