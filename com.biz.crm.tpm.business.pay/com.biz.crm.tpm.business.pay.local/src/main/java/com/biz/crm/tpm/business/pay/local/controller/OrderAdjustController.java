package com.biz.crm.tpm.business.pay.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.OrderAdjustDto;
import com.biz.crm.tpm.business.pay.sdk.service.OrderAdjustService;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustProductVo;
import com.biz.crm.tpm.business.pay.sdk.vo.OrderAdjustVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Api(tags = "订单调整功能接口")
@RestController
@RequestMapping("/v1/orderAdjust")
@Slf4j
public class OrderAdjustController {

    @Autowired(required = false)
    private OrderAdjustService orderAdjustService;

    @Autowired(required = false)
    private RedisService redisService;

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    @ApiOperation(value = "通过编号查询单条数据")
    @GetMapping("findByCode")
    public Result<OrderAdjustVo> findByCode(@ApiParam(name = "code", value = "编号", required = true) String code) {
        try {
            OrderAdjustVo vo = this.orderAdjustService.findByCode(code);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据缓存key获取导入的数据
     *
     * @param cacheKey 编号
     * @return 单条数据
     */
    @ApiOperation(value = "根据缓存key获取导入的数据")
    @GetMapping("findOrderAdjustProductVoByCacheKey")
    public Result<List<OrderAdjustProductVo>> findOrderAdjustProductVoByCacheKey(@RequestParam("cacheKey") String cacheKey) {
        try {

            Object object = redisService.get(PayConstant.ORDER_ADJUST_PRODUCT_CACHE_KEY + cacheKey);
            if (Objects.isNull(object)) {
                return Result.ok(Lists.newArrayList());
            }
            return Result.ok((List<OrderAdjustProductVo>) object);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @PostMapping
    public Result<?> create(@ApiParam(name = "dto", value = "订单调整") @RequestBody OrderAdjustDto dto) {
        try {
            this.orderAdjustService.create(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @PatchMapping
    public Result<?> update(@ApiParam(name = "dto", value = "订单调整") @RequestBody OrderAdjustDto dto) {
        try {
            this.orderAdjustService.update(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 新增保存并提交
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增保存并提交")
    @PostMapping("submitCreate")
    public Result<?> submitCreate(@ApiParam(name = "dto", value = "订单调整") @RequestBody OrderAdjustDto dto) {
        try {
            this.orderAdjustService.submitCreate(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑保存并提交
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "编辑保存并提交")
    @PostMapping("submitUpdate")
    public Result<?> submitUpdate(@ApiParam(name = "dto", value = "订单调整") @RequestBody OrderAdjustDto dto) {
        try {
            this.orderAdjustService.submitUpdate(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 确认
     *
     * @param codes 主键结合
     * @return
     */
    @ApiOperation(value = "确认")
    @PostMapping("confirm")
    public Result<?> confirm(@RequestBody List<String> codes) {
        try {
            this.orderAdjustService.confirm(codes);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "删除操作")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "ids", value = "主键集合") @RequestParam("ids") List<String> ids) {
        try {
            this.orderAdjustService.delete(ids);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
