package com.biz.crm.tpm.business.pay.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 实体：费用核销;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-15
 */
@ApiModel(value = "Audit", description = "费用核销")
@TableName("tpm_audit")
@Getter
@Setter
@Entity(name = "tpm_audit")
@org.hibernate.annotations.Table(appliesTo = "tpm_audit", comment = "费用核销")
@Table(name = "tpm_audit", indexes = {@Index(name = "tpm_audit_index1", columnList = "tenant_code, audit_code")})
public class Audit extends TenantFlagOpEntity {
  private static final long serialVersionUID = 7338844510322194101L;
  /**
   * 核销申请名称
   */
  @ApiModelProperty(name = "核销申请名称", notes = "核销申请名称")
  @Column(name = "audit_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '核销申请名称 '")
  private String auditName;

  /**
   * 核销申请编号
   */
  @ApiModelProperty(name = "核销申请编号", notes = "核销申请编号")
  @Column(name = "audit_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '核销申请编号 '")
  private String auditCode;

  /**
   * 核销金额汇总
   */
  @ApiModelProperty(name = "核销金额汇总", notes = "核销金额汇总")
  @Column(name = "total_apply_amount", nullable = false, length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '核销金额汇总 '")
  private BigDecimal totalApplyAmount;

  /**
   * 自动核销
   */
  @ApiModelProperty(name = "自动核销", notes = "自动核销")
  @Column(name = "is_auto_audit", nullable = false, length = 10, columnDefinition = "VARCHAR(10) COMMENT '自动核销 '")
  private String isAutoAudit;

  @Transient
  @TableField(exist = false)
  private Map<String, List<?>> items;
}
