package com.biz.crm.tpm.business.pay.local.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.rocketmq.service.RocketMqProducer;
import com.biz.crm.business.common.rocketmq.vo.MqMessageVo;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerDockingVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.user.sdk.service.UserInfoVoService;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserInfoVo;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.CashTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTicketVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashVo;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.biz.crm.workflow.sdk.vo.oa.RyOaMsgVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@Service
public class FeeCashSendOaMsgService {

    @Autowired(required = false)
    private CustomerVoService customerVoService;
    @Value("${oa.msgSource:}")
    private String msgSource;
    @Autowired(required = false)
    private RocketMqProducer rocketMqProducer;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private FeeCashService feeCashService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Autowired(required = false)
    private UserVoService userVoService;
    @Autowired(required = false)
    private UserInfoVoService userInfoVoService;

    /**
     * OA钉钉消息推送
     * 账扣预付、票扣兑付数据保存时，推送钉钉消息至OA
     *
     * @param codes
     * @param userDetails
     */
    public void sendOADingTalkMsgByCodes(List<String> codes, FacturerUserDetails userDetails) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        loginUserService.refreshAuthentication(userDetails);
        List<UserInfoVo> userVos = userInfoVoService.findByUserNames(Sets.newHashSet(userDetails.getUsername()));
        if (CollectionUtils.isEmpty(userVos)) {
            log.error("当前登陆人信息未维护！");
            return;
        }
        UserInfoVo userVo = userVos.get(0);
        if (StringUtils.isBlank(userVo.getOaId())) {
            return;
        }
        for (String code : codes) {
            FeeCashVo feeCashVo = feeCashService.findByCode(code);
            if (Objects.isNull(feeCashVo)) {
                continue;
            }
            FeeCashDetailVo feeCashDetailVo = Optional.ofNullable(feeCashVo.getDetailList()).orElse(Lists.newArrayList())
                    .stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode())).findFirst().orElse(new FeeCashDetailVo());
            String customerCode = feeCashDetailVo.getCustomerCode();
            String customerName = feeCashDetailVo.getCustomerName();
            FeeCashTicketVo ticketVo = Optional.ofNullable(feeCashVo.getTicketList()).orElse(Lists.newArrayList())
                    .stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode())).findFirst().orElse(new FeeCashTicketVo());
            if (StringUtils.isEmpty(customerCode)) {
                customerCode = ticketVo.getCustomerCode();
                customerName = ticketVo.getCustomerName();
            }

            CashTypeEnum cashTypeEnum = CashTypeEnum.findByCode(feeCashVo.getCashType());
            if (Objects.isNull(cashTypeEnum)) {
                continue;
            }
            //票扣兑付、账扣预付保存的时候要推OA消息
            if (!((feeCashVo.getCashType().equals(CashTypeEnum.FEE.getDictCode()) && feeCashVo.getCashMethod().equals(CashMethodEnum.TICKET_BUCKLE.getDictCode())) || feeCashVo.getCashType().equals(CashTypeEnum.ACCOUNT.getDictCode()))
                    || StringUtils.isEmpty(customerCode)) {
                continue;
            }
            String title = "%s提醒";
            String context = "%s本月账单票扣费用财务已完成对账，请至TPM做%s[%s]处理！";
            CustomerVo customerVo = customerVoService.findDetailsByIdOrCode(null, customerCode);
            if (Objects.isNull(customerVo) || CollectionUtils.isEmpty(customerVo.getDockingList())) {
                continue;
            }
            Set<String> userNames = customerVo.getDockingList().stream()
                    .filter(k -> StringUtils.isNotEmpty(k.getUserName()))
                    .map(CustomerDockingVo::getUserName)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(userNames)) {
                continue;
            }
            //接收人
            List<UserInfoVo> userInfoVos = userInfoVoService.findByUserNames(userNames);
            String userIdList = Optional.ofNullable(userInfoVos).orElse(Lists.newArrayList()).stream().map(UserInfoVo::getOaId).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));

            RyOaMsgVo msgVo = new RyOaMsgVo();
            msgVo.setMessageId(UuidCrmUtil.randomUuid());
            msgVo.setCode(msgSource);
            msgVo.setTitle(String.format(title, cashTypeEnum.getValue()));
            msgVo.setUserIdList(userIdList);
            msgVo.setContext(String.format(context, customerName, cashTypeEnum.getValue(), code));
            msgVo.setCreater(userVo.getOaId());
            //TODO 暂时直接调，等待工作流底层引入MQ问题处理
            try {
                ryOaProcessService.sendMsg(msgVo);
            } catch (Exception e) {
                log.error("发送钉钉消息异常!");
                log.error(e.getMessage(), e);
            }
//            this.sendMsg(msgVo);
        }
    }

    /**
     * 推送OA消息到MQ
     *
     * @param msgVo
     */
    private void sendMsg(RyOaMsgVo msgVo) {
        MqMessageVo mqMessageVo = new MqMessageVo();
        mqMessageVo.setTag(MqConstant.PUSH_OA_MSG);
        mqMessageVo.setMsgBody(JSONObject.toJSONString(msgVo));
        mqMessageVo.setMsgNum(1);
        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_DATE_PATTERN));
        this.rocketMqProducer.sendMqOrderMsg(mqMessageVo, StringUtils.join(msgVo.getMessageId(), date));
    }
}
