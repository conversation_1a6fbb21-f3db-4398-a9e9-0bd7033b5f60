package com.biz.crm.tpm.business.pay.local.controller;

import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.constant.DeliveryReplenishmentConstant;
import com.biz.crm.tpm.business.pay.sdk.service.DeliveryReplenishmentPoolDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * 进货返利冲销控制器
 */
@Api(tags = "进货返利费用冲销管理")
@RestController
@RequestMapping("/v1/pay/backWithHoldingWriteOff")
@Slf4j
public class BackWithHoldingWriteOffController {

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;

    // 所有方法已移除，仅保留类结构

    /**
     * 发货费用冲销定时任务
     */
    @ApiOperation(value = "进货返利发货费用冲销定时任务")
    @GetMapping("backDeliveryReplenishmentWriteOff")
    public Result<?> deliveryReplenishmentWriteOff() {
        redisLockService.lock(DeliveryReplenishmentConstant.DELIVERY_REPLENISHMENT_BACK_LOCK, TimeUnit.MINUTES, 30);
        try {
            this.deliveryReplenishmentPoolDetailService.backDeliveryReplenishmentWriteOff(null);
        } finally {
            redisLockService.unlock(DeliveryReplenishmentConstant.DELIVERY_REPLENISHMENT_BACK_LOCK);
        }
        return Result.ok();
    }
}
