package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashTicketRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashTicketDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashTicketVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 费用兑付分页缓存
 */
@Slf4j
@Component
public class FeeCashPageCacheHelper extends BusinessPageCacheHelper<FeeCashTicketVo, FeeCashTicketDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private FeeCashTicketRepository feeCashTicketRepository;
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return FeeCashConstant.CACHE_KEY_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<FeeCashTicketDto> getDtoClass() {
        return FeeCashTicketDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<FeeCashTicketVo> getVoClass() {
        return FeeCashTicketVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param feeCashTicketDto
     * @param cacheKey
     */
    @Override
    public List<FeeCashTicketDto> findDtoListFromRepository(FeeCashTicketDto feeCashTicketDto, String cacheKey) {
        if (StringUtils.isBlank(feeCashTicketDto.getCashCode())) {
            return new ArrayList<>();
        }
        List<FeeCashTicketVo> feeCashTicketVos = feeCashTicketRepository.findByCode(feeCashTicketDto.getCashCode());
        return CollectionUtils.isNotEmpty(feeCashTicketVos) ? (List<FeeCashTicketDto>) nebulaToolkitService.copyCollectionByBlankList(feeCashTicketVos, FeeCashTicketVo.class, FeeCashTicketDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashTicketDto> newItem(String cacheKey, List<FeeCashTicketDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<FeeCashTicketDto> copyItem(String cacheKey, List<FeeCashTicketDto> itemList) {
        List<FeeCashTicketDto> newItemList = (List<FeeCashTicketDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, FeeCashTicketDto.class, FeeCashTicketDto.class, HashSet.class, ArrayList.class);
        for (FeeCashTicketDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 导入新增数据
     * @param cacheKey
     * @param itemList
     * @return
     */
    public void importNewItem(String cacheKey, List<FeeCashTicketDto> itemList) {
        String redisCacheIdKey = this.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = this.getRedisCacheDataKey(cacheKey);

        Object[] newIdArr = itemList.stream().map(this::getDtoKey).toArray();
        redisService.lPushAll(redisCacheIdKey, this.getExpireTime(),newIdArr);

        Map<Object, FeeCashTicketDto> updateMap = itemList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }

    /**
     * 获取Dto中的主键
     *
     * @param feeCashTicketDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(FeeCashTicketDto feeCashTicketDto) {
        return feeCashTicketDto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param feeCashTicketDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(FeeCashTicketDto feeCashTicketDto) {
        return feeCashTicketDto.getChecked();
    }

}
