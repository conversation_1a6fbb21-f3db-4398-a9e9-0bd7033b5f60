package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.dto.InvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceService;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailPageVo;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 发票池(Invoice)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
@RestController
@RequestMapping("/v1/pay/invoice")
@Slf4j
@Api(tags = "发票池")
public class InvoiceController {
    /**
     * 服务对象
     */
    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private InvoiceDetailService invoiceDetailService;

    /**
     * 分页查询所有数据
     *
     * @param pageable 分页对象
     * @param dto      查询实体
     * @return 所有数据
     */
    @ApiOperation(value = "分页查询所有数据")
    @GetMapping("findByConditions")
    public Result<Page<InvoiceVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                    @ApiParam(name = "dto", value = "发票池") InvoiceDto dto, HttpServletRequest request) {
        try {
            String sno = request.getParameter("sno");
            if (StringUtils.isNotBlank(sno)) {
                dto.setSNo(sno);
            }
            if(Objects.isNull(dto.getSNo())){
                sno = request.getParameter("sNo");
                if (StringUtils.isNotBlank(sno)) {
                    dto.setSNo(sno);
                }
            }
            Page<InvoiceVo> page = this.invoiceService.findByConditions(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过发票号码查询单条数据
     *
     * @param invoiceNo 发票号码
     * @return 单条数据
     */
    @ApiOperation(value = "通过发票号码查询单条数据")
    @GetMapping("findByInvoiceNo")
    public Result<InvoiceVo> findByInvoiceNo(@RequestParam("invoiceNo") @ApiParam(name = "invoiceNo", value = "发票号码") String invoiceNo) {
        try {
            InvoiceVo Invoice = this.invoiceService.findByInvoiceNo(invoiceNo);
            return Result.ok(Invoice);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param Invoice 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @PostMapping
    public Result<InvoiceVo> create(@ApiParam(name = "Invoice", value = "发票池") @RequestBody InvoiceDto Invoice) {
        try {
            InvoiceVo result = this.invoiceService.create(Invoice);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改数据
     *
     * @param Invoice 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @PatchMapping
    public Result<InvoiceVo> update(@ApiParam(name = "Invoice", value = "发票池") @RequestBody InvoiceDto Invoice) {
        try {
            InvoiceVo result = this.invoiceService.update(Invoice);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除操作")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "ids", value = "主键集合") @RequestParam("ids") List<String> ids) {
        try {
            this.invoiceService.delete(ids);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "发票验证")
    @PostMapping("checkInvoiceList")
    public Result checkInvoiceList(@RequestBody Set<String> invoiceNoList) {
        for (String s : invoiceNoList) {
            invoiceService.checkInvoiceList(s);
        }
        return Result.ok();
    }

    @ApiOperation(value = "查询发票明细列表")
    @GetMapping("findInvoiceDetailList")
    public Result<Page<InvoiceDetailPageVo>> findInvoiceDetailList(@PageableDefault(50) Pageable pageable, InvoiceDetailPageVo vo) {
        return Result.ok(invoiceDetailService.findInvoiceDetailList(pageable, vo));
    }

}
