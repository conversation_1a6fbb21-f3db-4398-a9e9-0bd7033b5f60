package com.biz.crm.tpm.business.pay.local.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 参数传递dto：预付账单明细记录;
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@ApiModel(value = "PrepayBillRecord",description = "预付账单明细记录")
@Getter
@Setter
public class PrepayBillRecordDto implements Serializable,Cloneable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value = "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 活动明细编码 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value = "活动明细编码")
  private String activitiesDetailCode;
  /** 申请金额 */
  @ApiModelProperty(name = "changeAmount",notes = "申请金额", value = "申请金额")
  private BigDecimal changeAmount;
  /** 业务编号 */
  @ApiModelProperty(name = "businessCode",notes = "已核销金额", value = "已核销金额")
  private String businessCode;
  /** 操作类型 */
  @ApiModelProperty(name = "type",notes = "操作类型：0、初始化，1、新增，2、减少", value = "操作类型：0、初始化，1、新增，2、减少")
  private Integer type;

}
