package com.biz.crm.tpm.business.pay.local.calcomponent;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.biz.crm.business.common.base.eunm.AuditStatusEnum;
import com.biz.crm.business.common.base.eunm.CashStatusEnum;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.ReplenishmentPoolDetailDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.WithholdingSearchDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolOperationType;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolTypeEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.renyang.ReplenishmentPoolDetailVoService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.vo.renyang.ReplenishmentPoolDetailVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectSchemeVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.local.entity.DeliveryReplenishmentPoolDetail;
import com.biz.crm.tpm.business.pay.local.entity.WithHolding;
import com.biz.crm.tpm.business.pay.local.repository.DeliveryReplenishmentPoolDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.WithHoldingConstant;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WithHoldingTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.DeliveryReplenishmentPoolDetailService;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.service.FormulaCalService;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/18 16:12
 **/
@Component
@Slf4j
public class WithholdingGenerateComponent {

    @Resource
    private WithHoldingRepository withHoldingRepository;

    @Resource
    private MarketingPlanProductRepository marketingPlanProductRepository;

    @Resource
    private CostTypeDetailVoService costTypeDetailVoService;

    @Resource
    private ReplenishmentPoolDetailVoService replenishmentPoolDetailVoService;

    @Resource
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;

    @Resource
    private FormulaCalService formulaCalService;

    @Resource
    private MarketingPlanCaseService marketingPlanCaseService;

    @Resource
    private GenerateCodeService generateCodeService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private ProductVoService productVoService;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private CostTypeCategoryVoService costTypeCategoryVoService;

    @Resource
    private DeliveryReplenishmentPoolDetailRepository deliveryReplenishmentPoolDetailRepository;

    @Resource
    private LoginUserService loginUserService;


    /**
     * 计提方案明细活动
     *
     * @param records
     * @param withHoldingYears
     */
    @Async("tpmOverallBudgetThread")
    public void runSchemeDetailGenerateWithholding(List<MarketingPlanCaseVo> records, String withHoldingYears) {
        loginUserService.refreshAuthentication(null);
        List<WithHolding> entities = new ArrayList<>();
        List<WithHolding> withHoldingList = withHoldingRepository.findWithholdingConfirmedByYearMonthly(withHoldingYears);

        if (!CollectionUtils.isEmpty(withHoldingList)) {
            List<String> businessCodes = withHoldingList.stream().map(x -> x.getBusinessCode()).collect(Collectors.toList());
            records = records.stream().filter(x -> !businessCodes.contains(x.getSchemeDetailCode())).collect(Collectors.toList());
        }
        //方案创建人
        List<String> schemeCodes = records.stream().map(MarketingPlanCaseVo::getSchemeCode).collect(Collectors.toList());
        Map<String, MarketingPlanCaseVo> schemeDetailVoMap = records.stream().collect(Collectors.toMap(MarketingPlanCaseVo::getSchemeDetailCode,Function.identity(), (x, y) -> x));
        List<RegionCollectSchemeVo> collectSchemeVos = withHoldingRepository.findSchemeCreateInfoList(schemeCodes);
        Map<String, RegionCollectSchemeVo> collectSchemeVoMap = collectSchemeVos.stream().collect(Collectors.toMap(RegionCollectSchemeVo::getSchemeCode, Function.identity()));

        //产品范围
        List<String> schemeDetailCodes = records.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        List<MarketingPlanProduct> marketingPlanProductList = marketingPlanProductRepository.findListBySchemeDetailCodes(schemeDetailCodes);
        Map<String, List<MarketingPlanProduct>> planProductMap = marketingPlanProductList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
        for (MarketingPlanCaseVo planCase : records) {
            planCase.setProductAndItemList(BeanUtil.copyToList(planProductMap.getOrDefault(planCase.getSchemeDetailCode(), Lists.newArrayList()), MarketingPlanProductVo.class));
        }

        // 以下条件才允许计提：
        // 1.活动细类对应的“是否计提”维护为”是“
        // 2.结案状态为“未结案”“部分结案”的活动、
        // 或者结案状态为“完全结案”，且兑付状态为“未兑付”“部分兑付”才计提、
        // 或者结案状态为“完全结案”，且兑付状态为“完全兑付”，且完全兑付日期不等于计提年月
        Set<String> costTypeDetailCodeSet = records.stream().map(e -> e.getDetailCode()).collect(Collectors.toSet());
        List<CostTypeDetailVo> costTypeDetailList = costTypeDetailVoService.findByCodes(new ArrayList<>(costTypeDetailCodeSet));
        Validate.notEmpty(costTypeDetailList, "未找到任意活动细类");
        Set<String> detailCodeSet = costTypeDetailList.stream().filter(e -> BooleanEnum.TRUE.getCapital().equals(e.getIsWithHolding())).map(e -> e.getDetailCode()).collect(Collectors.toSet());
        List<MarketingPlanCaseVo> planCaseVoList = records.stream().filter(e -> detailCodeSet.contains(e.getDetailCode()) &&
                ((AuditStatusEnum.NOT_AUDIT.getCode().equals(e.getAuditStatus()) || AuditStatusEnum.PART_AUDIT.getCode().equals(e.getAuditStatus())) ||
                        (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) && (CashStatusEnum.NOT_CASH.getCode().equals(e.getCashStatus()) || CashStatusEnum.PART_CASH.getCode().equals(e.getCashStatus()))) ||
                        (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) && CashStatusEnum.WHOLE_CASH.getCode().equals(e.getCashStatus()) && StringUtils.isNotBlank(e.getWholeCashDate()) && !e.getWholeCashDate().startsWith(withHoldingYears))
                )).collect(Collectors.toList());

        //货补
        List<MarketingPlanCaseVo> replenishmentVoList = records.stream().filter(e -> detailCodeSet.contains(e.getDetailCode()) &&
                (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) && (CashStatusEnum.NOT_CASH.getCode().equals(e.getCashStatus()) || CashStatusEnum.PART_CASH.getCode().equals(e.getCashStatus()))) &&
                CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType())
        ).collect(Collectors.toList());
        List<String> replenishmentVoCodes = CollectionUtils.isEmpty(replenishmentVoList) ? new ArrayList<>() : replenishmentVoList.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
        Map<String, BigDecimal> deliveryAmountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(replenishmentVoCodes)) {
            deliveryAmountMap = replenishmentPoolDetailVoService.findDeliveryAmountByActivityCodes(replenishmentVoCodes);
        }

        //活动计提
        if (!CollectionUtils.isEmpty(planCaseVoList)) {
            Map<String, List<MarketingPlanCaseVo>> caseTypeMap = planCaseVoList.stream().collect(Collectors.groupingBy(MarketingPlanCaseVo::getCaseType));
            Map<String, BigDecimal> finalDeliveryAmountMap = deliveryAmountMap;
            //在【费用兑付/关闭明细】下查询操作类型=清空/发货/费用兑付/费用关闭且业务发生日期与计提入账年月一致的明细汇总费用金额
            List<String> codes = planCaseVoList.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
            Map<String, BigDecimal> deliveryMap = deliveryReplenishmentPoolDetailService.findBySchemeDetailCodes(codes, withHoldingYears);
            caseTypeMap.forEach((k, v) -> {
                v.forEach(e -> {
                    //
                    RegionCollectSchemeVo baseCreateInfo = new RegionCollectSchemeVo();
                    if (StringUtils.isNotBlank(e.getSchemeCode())) {
                        baseCreateInfo = collectSchemeVoMap.getOrDefault(e.getSchemeCode(), new RegionCollectSchemeVo());
                    }
                    //已申请未结案
                    //除周边物料、随单搭赠、进货返利、特价、直降外，按方案申请金额计提；返利根据返利公式测算计提金额
                    if (AuditStatusEnum.NOT_AUDIT.getCode().equals(e.getAuditStatus()) || AuditStatusEnum.PART_AUDIT.getCode().equals(e.getAuditStatus())) {
                        if (!MarketingPlanCaseTypeEnum.noWithHolding().contains(k)) {
                            WithHolding entity = new WithHolding();
                            BeanUtils.copyProperties(baseCreateInfo, entity);
                            buildWithHolding(entity, e, "TPM", WithHoldingTypeEnum.NOT_AUDIT.getDictCode(), withHoldingYears);
                            entity.setWithHoldingAmount(e.getApplyAmount().subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                            entity.setActualAmount(entity.getWithHoldingAmount());
                            entity.setWithHoldingReportAmount(entity.getWithHoldingAmount());
                            entity.setActualReportAmount(entity.getWithHoldingAmount());
                            entity.setNoTaxActualReportAmount(new BigDecimal(0));
                            entity.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                            entities.add(entity);
                            //返利
                        } else if (MarketingPlanCaseTypeEnum.back.getCode().equals(k)) {
                            FormulaCalBaseVo calVo = new FormulaCalBaseVo();
                            calVo.setCustomerCode(e.getCustomerCode());
                            calVo.setActDetailCode(e.getSchemeDetailCode());
                            calVo.setYears(e.getYears());
                            calVo.setRebateStartDate(e.getRebateStartDate());
                            calVo.setRebateEndDate(e.getRebateEndDate());
                            calVo.setStartDate(e.getStartDate());
                            calVo.setEndDate(e.getEndDate());
                            calVo.setConditionNum(e.getConditionNum());
                            calVo.setItemCodeSet(Optional.ofNullable(e.getItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                            calVo.setProductCodeSet(Optional.ofNullable(e.getProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                            calVo.setFeeItemCodeSet(Optional.ofNullable(e.getFeeItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                            calVo.setFeeProductCodeSet(Optional.ofNullable(e.getFeeProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                            Validate.isTrue(!(CollectionUtils.isEmpty(calVo.getItemCodeSet()) && CollectionUtils.isEmpty(calVo.getProductCodeSet())), "营销方案：%s-%s，品项编码和商品编码不能为空", e.getSchemeCode(), e.getSchemeDetailCode());
                            Validate.isTrue(!(CollectionUtils.isEmpty(calVo.getFeeItemCodeSet()) && CollectionUtils.isEmpty(calVo.getFeeProductCodeSet())), "营销方案：%s-%s，返利品项和返利产品不能为空", e.getSchemeCode(), e.getSchemeDetailCode());
                            calVo.setGiveNum(e.getGiveNum());
                            calVo.setOrgCode(e.getBelongDepartmentCode());

                            BigDecimal calAmount = formulaCalService.calResult(FormulaTypeEnum.WITHHOLDING.getCode(), e.getConditionFormula(), calVo);
                            BigDecimal reportAmount = formulaCalService.calResult(FormulaTypeEnum.MANAGEMENT_REPORT.getCode(), e.getConditionFormula(), calVo);

                            WithHolding entity = new WithHolding();
                            BeanUtils.copyProperties(baseCreateInfo, entity);
                            buildWithHolding(entity, e, "TPM", WithHoldingTypeEnum.NOT_AUDIT.getDictCode(), withHoldingYears);
                            entity.setWithHoldingAmount(calAmount.subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                            entity.setActualAmount(entity.getWithHoldingAmount());
                            entity.setWithHoldingReportAmount(reportAmount.subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                            entity.setActualReportAmount(entity.getWithHoldingReportAmount());
                            entity.setNoTaxActualReportAmount(new BigDecimal(0));
                            entity.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                            entities.add(entity);
                        }
                        //已结案未兑付、
                        //①电汇、账扣、票扣，按费用兑付统计已结案未上账兑付（根据TPM的未兑付状态判断）的差额计提（已结案金额-已上账金额）；
                        //②上账到客户的货补池费用，按DMS货补池的已结案未上账兑付（结案金额-该活动在DMS的已发货金额）的余额计提。
                    } else {
                        if (Arrays.asList(CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode(), CashMethodEnum.WIRE_TRANSFER.getDictCode()).contains(e.getCashType())) {
                            WithHolding entity = new WithHolding();
                            BeanUtils.copyProperties(baseCreateInfo, entity);
                            buildWithHolding(entity, e, "TPM", WithHoldingTypeEnum.NOT_CASH.getDictCode(), withHoldingYears);
                            entity.setWithHoldingAmount(e.getAuditAmount().subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                            entity.setActualAmount(entity.getWithHoldingAmount());
                            entity.setWithHoldingReportAmount(entity.getWithHoldingAmount());
                            entity.setActualReportAmount(entity.getWithHoldingAmount());
                            entity.setNoTaxActualReportAmount(new BigDecimal(0));
                            entity.setConfirmStatus(ConfirmStatusEnum.CONFIRMED.getCode());
                            entities.add(entity);
                        } else if (CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType())) {
                            WithHolding entity = new WithHolding();
                            BeanUtils.copyProperties(baseCreateInfo, entity);
                            buildWithHolding(entity, e, "TPM", WithHoldingTypeEnum.NOT_CASH.getDictCode(), withHoldingYears);
                            entity.setWithHoldingAmount(e.getAuditAmount().subtract(finalDeliveryAmountMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)).subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                            entity.setActualAmount(entity.getWithHoldingAmount());
                            entity.setWithHoldingReportAmount(entity.getWithHoldingAmount());
                            entity.setActualReportAmount(entity.getWithHoldingAmount());
                            entity.setNoTaxActualReportAmount(new BigDecimal(0));
                            entity.setConfirmStatus(ConfirmStatusEnum.CONFIRMED.getCode());
                            entities.add(entity);
                        }
                    }
                });
            });
        }

        if (!CollectionUtils.isEmpty(entities)) {
            List<String> businessCodes = entities.stream().map(x -> x.getBusinessCode()).collect(Collectors.toList());

            withHoldingRepository.deleteByYearMonthlyAndBusinessCodes(withHoldingYears, businessCodes);
            marketingPlanCaseService.updateWithHoldingStatusBySchemeDetailCodes(schemeDetailCodes, BooleanEnum.FALSE.getCapital());
            List<String> codeList = generateCodeService.generateCodeYearMonth(WithHoldingConstant.PREFIX_CODE, entities.size());

            for (int i = 0; i < entities.size(); i++) {
                WithHolding curEntity = entities.get(i);
                curEntity.setId(UuidCrmUtil.general());
                curEntity.setWithHoldingCode(codeList.get(i));
                curEntity.setBeThisFee(BooleanEnum.TRUE.getCapital());
                MarketingPlanCaseVo marketingPlanCaseVo = schemeDetailVoMap.get(curEntity.getBusinessCode());
                if (Objects.nonNull(marketingPlanCaseVo)) {
                    curEntity.setNoTaxActualReportAmount(curEntity.getActualReportAmount().divide(BigDecimal.ONE.add(marketingPlanCaseVo.getLineTaxRate() == null ? BigDecimal.ZERO : marketingPlanCaseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_UP));
                }
            }
            withHoldingRepository.saveBatch(entities);
            //新计提活动是否计提设置为是
            List<String> schemeDetailCodesNew = entities.stream().filter(e -> StringUtils.isNotBlank(e.getActivitiesDetailCode()))
                    .filter(Objects::nonNull).map(e -> e.getActivitiesDetailCode()).collect(Collectors.toList());
            marketingPlanCaseService.updateWithHoldingStatusBySchemeDetailCodes(schemeDetailCodesNew, BooleanEnum.TRUE.getCapital());
        }
    }


    /**
     * 计提发货数据
     */
    @Async("tpmOverallBudgetThread")
    public void runReplenishmentGenerateWithholding(String withHoldingYears) {
        loginUserService.refreshAuthentication(null);
        List<WithHolding> entities = new ArrayList<>();
        //随单搭赠尾差
        List<ReplenishmentPoolDetailVo> replenishmentPoolDetailVos = new ArrayList<>();
        WithholdingSearchDto dto = new WithholdingSearchDto();
        dto.setOperationSearchStartTime(withHoldingYears + "-01 00:00:00");
        dto.setOperationSearchEndTime(DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(withHoldingYears + "-01", "yyyy-MM-dd")), "yyyy-MM-dd") + " 23:59:59");
        dto.setReplenishmentPoolTypes(Collections.singletonList(ReplenishmentPoolTypeEnum.TAIL_DIFF.getDictCode()));
        List<ReplenishmentPoolDetailVo> replenishmentVos1 = replenishmentPoolDetailVoService.noWithholding(dto);
        if (!CollectionUtils.isEmpty(replenishmentVos1)) {
            replenishmentPoolDetailVos.addAll(replenishmentVos1);
        }
        dto.setReplenishmentPoolTypes(Collections.singletonList(ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode()));
        dto.setOperationTypes(Lists.newArrayList(ReplenishmentPoolOperationType.POD_DIFF_ADD.getCode(),
                ReplenishmentPoolOperationType.POD_DIFF_REDUCE.getCode(),
                ReplenishmentPoolOperationType.ORDER_CLOSE_DETAIL.getCode()));
        List<ReplenishmentPoolDetailVo> replenishmentVos2 = replenishmentPoolDetailVoService.noWithholding(dto);
        if (!CollectionUtils.isEmpty(replenishmentVos2)) {
            replenishmentPoolDetailVos.addAll(replenishmentVos2);
        }
        if (!CollectionUtils.isEmpty(replenishmentPoolDetailVos)) {
            if (!CollectionUtils.isEmpty(replenishmentPoolDetailVos)) {
                Map<String, List<DictDataVo>> dictDataVoMap = dictDataVoService.findByDictTypeCodeList(Arrays.asList(WithHoldingConstant.PENNYAGE_ACTIVITY_SUBCATEGORIES, WithHoldingConstant.BUSINESS_FORMAT_FYC));
                Set<String> productCodes = replenishmentPoolDetailVos.stream().filter(e -> StringUtils.isNotBlank(e.getProductCode())).map(e -> e.getProductCode()).collect(Collectors.toSet());
                Set<String> customerCodes = replenishmentPoolDetailVos.stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode())).map(e -> e.getCustomerCode()).collect(Collectors.toSet());
                Set<String> poolDetailCodes = replenishmentPoolDetailVos.stream().filter(e -> StringUtils.isNotBlank(e.getCustomerCode())).map(e -> e.getPoolDetailCode()).collect(Collectors.toSet());
                Map<String, ProductVo> productMap = productVoService.findDetailsByIdsOrProductCodes(null, new ArrayList<>(productCodes))
                        .stream().collect(Collectors.toMap(e -> e.getProductCode(), Function.identity(), (a, b) -> a));
                Map<String, CustomerVo> customerMap = customerVoService.findByCustomerCodes(new ArrayList<>(customerCodes))
                        .stream().collect(Collectors.toMap(e -> e.getCustomerCode(), Function.identity(), (a, b) -> a));
                List<DictDataVo> costTypeDetailDictDataVos = dictDataVoMap.get(WithHoldingConstant.PENNYAGE_ACTIVITY_SUBCATEGORIES);
                Map<String, DictDataVo> businessTypeMap = dictDataVoMap.get(WithHoldingConstant.BUSINESS_FORMAT_FYC).stream().collect(Collectors.toMap(e -> e.getDictCode(), Function.identity(), (a, b) -> a));
                CostTypeDetailVo costTypeDetail = costTypeDetailVoService.findByCode(costTypeDetailDictDataVos.get(0).getDictCode());
                CostTypeCategoryVo category = costTypeCategoryVoService.findByCode(costTypeDetail.getCategoryCode());
                Map<String, List<DeliveryReplenishmentPoolDetail>> deliverylMap = deliveryReplenishmentPoolDetailRepository.findByParentCodes(new ArrayList<>(poolDetailCodes), withHoldingYears)
                        .stream().filter(Objects::nonNull).collect(Collectors.groupingBy(e -> e.getParentCode()));
                for (ReplenishmentPoolDetailVo e : replenishmentPoolDetailVos) {
                    WithHolding entity = new WithHolding();
                    buildWithHolding(entity, "DMS", e.getOperationType(), withHoldingYears, e, costTypeDetailDictDataVos.get(0), productMap, customerMap, costTypeDetail, category, businessTypeMap);
                    //订单关闭、pod差异-增加、pod差异-减少的计提金额
                    BigDecimal price = e.getPrice() == null ? BigDecimal.ZERO : e.getPrice();
                    BigDecimal usableAmount = e.getUsableAmount() == null ? BigDecimal.ZERO : e.getUsableAmount();
                    BigDecimal usedAmount = e.getUsedAmount() == null ? BigDecimal.ZERO : e.getUsedAmount();
                    BigDecimal withholdingAmount = BigDecimal.ZERO;
                    if (ReplenishmentPoolTypeEnum.REPLENISHMENT.getDictCode().equals(e.getReplenishmentPoolType())) {
                        //如果是货补类型
                        //可用金额/数量+已用金额/数量-发货费用报表只需要“业务日期”为计提年月的数据
                        withholdingAmount = usableAmount.add(usedAmount);
                    } else if (ReplenishmentPoolTypeEnum.TAIL_DIFF.getDictCode().equals(e.getReplenishmentPoolType())) {
                        //如果是尾差类型
                        //上账时单价×可用金额/数量+（上账时单价×已用金额/数量）-发货费用报表只需要“业务日期”为计提年月的数据
                        withholdingAmount = ((price.multiply(usableAmount)).add(price.multiply(usedAmount))).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    entity.setWithHoldingAmount(withholdingAmount);
                    if (deliverylMap != null && deliverylMap.containsKey(e.getPoolDetailCode())) {
                        BigDecimal reduce = deliverylMap.get(e.getPoolDetailCode()).stream().map(m -> m.getOperationAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                        entity.setWithHoldingAmount(entity.getWithHoldingAmount().subtract(reduce));
                    }
                    entity.setActualAmount(entity.getWithHoldingAmount());
                    entity.setWithHoldingReportAmount(entity.getWithHoldingAmount());
                    entity.setActualReportAmount(entity.getWithHoldingAmount());
                    entity.setNoTaxActualReportAmount(e.getOperationAmountExcludingTax());
                    entity.setConfirmStatus(ConfirmStatusEnum.CONFIRMED.getCode());
                    entities.add(entity);
                }
                List<String> poolDetailCodeList = replenishmentPoolDetailVos.stream().map(e -> e.getPoolDetailCode()).collect(Collectors.toList());
                ReplenishmentPoolDetailDto poolDetailDto = new ReplenishmentPoolDetailDto();
                poolDetailDto.setPoolDetailCodeList(poolDetailCodeList);
                poolDetailDto.setWithholdingStatus(BooleanEnum.TRUE.getCapital());
                replenishmentPoolDetailVoService.updateWithholdingStatusByDetailCodes(poolDetailDto);
            }
        }
        if (!CollectionUtils.isEmpty(entities)) {
            List<WithHolding> withHoldingList = withHoldingRepository.findWithholdingConfirmedByYearMonthly(withHoldingYears);

            if (!CollectionUtils.isEmpty(withHoldingList)) {
                List<String> businessCodes = withHoldingList.stream().map(x -> x.getBusinessCode()).collect(Collectors.toList());
                entities = entities.stream().filter(x -> !businessCodes.contains(x.getBusinessCode())).collect(Collectors.toList());
            }

            List<String> businessCodes = entities.stream().map(x -> x.getBusinessCode()).collect(Collectors.toList());
            ;
            withHoldingRepository.deleteByYearMonthlyAndBusinessCodes(withHoldingYears, businessCodes);
            List<String> codeList = generateCodeService.generateCodeYearMonth(WithHoldingConstant.PREFIX_CODE, entities.size());

            for (int i = 0; i < entities.size(); i++) {
                entities.get(i).setId(UuidCrmUtil.general());
                entities.get(i).setWithHoldingCode(codeList.get(i));
                entities.get(i).setBeThisFee(BooleanEnum.TRUE.getCapital());
            }
            withHoldingRepository.saveBatch(entities);
        }
    }


    /**
     * 随单搭赠构建费用计提
     *
     * @param entity
     * @param
     */
    private void buildWithHolding(WithHolding entity, String source, String withHoldingType, String yearMonthLy, ReplenishmentPoolDetailVo vo,
                                  DictDataVo dictDataVo, Map<String, ProductVo> productMap, Map<String, CustomerVo> customerMap, CostTypeDetailVo costTypeDetail,
                                  CostTypeCategoryVo category, Map<String, DictDataVo> businessTypeMap) {

        CustomerVo customerVo = customerMap.get(vo.getCustomerCode());
        entity.setCustomerCode(vo.getCustomerCode());
        entity.setCustomerName(vo.getCustomerName());
        entity.setErpCode(customerVo.getErpCode());
        entity.setCompanyCode(customerVo.getCompanyCode());
        entity.setCostCenterCode(customerVo.getCostCenterCode());
        entity.setCostCenterName(customerVo.getCostCenterName());
        entity.setBearDepartmentCode(customerVo.getOrgList().get(0).getOrgCode());
        entity.setBearDepartmentName(customerVo.getOrgList().get(0).getOrgName());
        entity.setBelongDepartmentCode(customerVo.getOrgList().get(0).getOrgCode());
        entity.setBelongDepartmentName(customerVo.getOrgList().get(0).getOrgName());
        //如果有产品就找对应的产品品项，如果没有就通过数据字典去查业态对应的品项
        if (StringUtils.isNotBlank(vo.getProductCode())) {
            ProductVo productVo = productMap.get(vo.getProductCode());
            entity.setItemCode(productVo.getProductPhaseCode());
            entity.setItemName(productVo.getProductPhaseName());
        } else {
            DictDataVo businessType = businessTypeMap.get(vo.getBusinessType());
            entity.setItemCode(businessType.getExt1());
            entity.setItemName(businessType.getExt2());
        }
        entity.setBusinessCode(vo.getPoolDetailCode());
        entity.setYears(yearMonthLy);
        entity.setCostTypeDetailCode(dictDataVo.getDictCode());
        entity.setCostTypeDetailName(dictDataVo.getDictValue());
        entity.setPayBy(CashMethodEnum.REPLENISHMENT.getDictCode());
        entity.setCostTypeDetailCode(costTypeDetail.getDetailCode());
        entity.setCostTypeDetailName(costTypeDetail.getDetailName());
        entity.setCostTypeCategoryCode(category.getCategoryCode());
        entity.setCostTypeCategoryName(category.getCategoryName());
        entity.setBudgetSubjectsCode(category.getBudgetSubjectsCode());
        entity.setBudgetSubjectsName(category.getBudgetSubjectsName());

        buildWithHolding(entity, null, source, withHoldingType, yearMonthLy);
    }


    /**
     * 构建费用计提
     *
     * @param entity
     * @param vo
     */
    private void buildWithHolding(WithHolding entity, MarketingPlanCaseVo vo, String source, String withHoldingType, String yearMonthLy) {
        entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        entity.setTenantCode(TenantUtils.getTenantCode());
        entity.setStatus(ProcessStatusEnum.PREPARE.getDictCode());
        entity.setWithHoldingSource(source);

        entity.setYearMonthLy(yearMonthLy);
        if (vo != null) {
            entity.setId(vo.getSchemeDetailCode());
            entity.setYears(vo.getStartDate().substring(0, 7));
            entity.setCompanyCode(vo.getCompanyCode());
            entity.setActivitiesCode(vo.getSchemeCode());
            entity.setActivitiesName(vo.getSchemeName());
            entity.setActivitiesDetailCode(vo.getSchemeDetailCode());
            entity.setCostTypeCategoryCode(vo.getCategoryCode());
            entity.setCostTypeCategoryName(vo.getCategoryName());
            entity.setCostTypeDetailCode(vo.getDetailCode());
            entity.setCostTypeDetailName(vo.getDetailName());
            entity.setBearDepartmentCode(vo.getBearDepartmentCode());
            entity.setBearDepartmentName(vo.getBearDepartmentName());
            entity.setBelongDepartmentCode(vo.getBelongDepartmentCode());
            entity.setBelongDepartmentName(vo.getBelongDepartmentName());
            entity.setCostCenterCode(vo.getCostCenterCode());
            entity.setCostCenterName(vo.getCostCenterName());
            entity.setCustomerCode(vo.getCustomerCode());
            entity.setCustomerName(vo.getCustomerName());
            entity.setApplyAmount(vo.getApplyAmount());
            entity.setBudgetSubjectsCode(vo.getBudgetSubjectCode());
            entity.setBudgetSubjectsName(vo.getBudgetSubjectName());
            entity.setPayBy(vo.getCashType());
            entity.setErpCode(vo.getErpCode());
            entity.setProductGroupCode(vo.getProductGroupCode());
            entity.setChannelCode(vo.getChannelCode());
            entity.setPositionCode(vo.getPositionCode());
            entity.setBusinessCode(vo.getSchemeDetailCode());
            entity.setItemCode(vo.getItemCode());
            entity.setItemName(vo.getItemName());
            entity.setTerminalCode(vo.getTerminalCode());
            entity.setTerminalName(vo.getTerminalName());
            entity.setActDesc(vo.getActDesc());
            entity.setSchemeCreateName(vo.getCreateName());
        }

        entity.setWithHoldingType(withHoldingType);
    }
}
