package com.biz.crm.tpm.business.pay.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashDetail;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashDetailMapper;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.BillingCompareReportVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 费用兑付明细(FeeCashDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
@Component
public class FeeCashDetailRepository extends ServiceImpl<FeeCashDetailMapper, FeeCashDetail> {

    @Autowired
    private FeeCashDetailMapper feeCashDetailMapper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    public List<FeeCashDetailVo> findByCode(String code) {
        List<FeeCashDetail> list = this.lambdaQuery().eq(FeeCashDetail::getCashCode, code)
                .orderByAsc(FeeCashDetail::getCashDetailCode).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashDetail.class, FeeCashDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    public List<FeeCashDetailVo> findAuditCashAmount(List<String> auditDetailCode) {
        return this.baseMapper.findAuditCashAmount(auditDetailCode);
    }

    /**
     * 按编码查询
     *
     * @param codes
     * @return
     */
    public List<FeeCashDetailVo> findByCodes(List<String> codes) {
        List<FeeCashDetail> list = this.lambdaQuery().in(FeeCashDetail::getCashCode, codes).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashDetail.class, FeeCashDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 按编码删除
     *
     * @param codes
     */
    public void deleteByCodes(List<String> codes) {
        this.remove(Wrappers.lambdaQuery(FeeCashDetail.class)
                .in(FeeCashDetail::getCashCode, codes));
    }

    /**
     * 按结案明细编码查询
     *
     * @param auditDetailCodes
     * @return
     */
    public List<FeeCashDetailVo> findByAuditDetailCodes(List<String> auditDetailCodes) {
        if (CollectionUtils.isEmpty(auditDetailCodes)) {
            return new ArrayList<>(0);
        }
        List<FeeCashDetail> list = this.lambdaQuery().in(FeeCashDetail::getAuditDetailCode, auditDetailCodes)
                .eq(FeeCashDetail::getTenantCode, TenantUtils.getTenantCode())
                .eq(FeeCashDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashDetail.class, FeeCashDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    public List<FeeCashDetailVo> findByAuditDetailCodesPass(List<String> auditDetailCodes, String status, List<String> cashDetailCodes, List<String> cashTypeList) {
        if (CollectionUtils.isEmpty(auditDetailCodes)) {
            return new ArrayList<>();
        }
        return baseMapper.findByAuditDetailCodesPass(auditDetailCodes, status, cashDetailCodes, cashTypeList);
    }

    /**
     * 按兑付明细编码查询
     *
     * @param codes
     * @return
     */
    public List<FeeCashDetailVo> findByFeeCashDetailCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>(0);
        }
        List<FeeCashDetail> list = this.lambdaQuery().in(FeeCashDetail::getCashDetailCode, codes)
                .eq(FeeCashDetail::getTenantCode, TenantUtils.getTenantCode())
                .eq(FeeCashDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, FeeCashDetail.class, FeeCashDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    public List<FeeCashDetailVo> findByAuditCustomer(List<String> auditCodes, List<String> customerCodes) {
        if (CollectionUtil.isEmpty(auditCodes) || CollectionUtil.isEmpty(customerCodes)) {
            return new ArrayList<>();
        }
        List<FeeCashDetail> list = lambdaQuery().in(FeeCashDetail::getAuditCode, auditCodes)
                .in(FeeCashDetail::getCustomerCode, customerCodes).list();
        return org.apache.commons.collections.CollectionUtils.isEmpty(list) ? new ArrayList<>() :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, FeeCashDetail.class, FeeCashDetailVo.class, LinkedHashSet.class, ArrayList.class));
    }

    public List<FeeCashDetailVo> findByBillingCompareReport(BillingCompareReportVo dto) {
        return baseMapper.findByBillingCompareReport(dto);
    }

    /**
     * 按结案明细编码查询是否有审批中
     *
     * @param codes
     * @return
     */
    public List<FeeCashDetail> findByAuditDetailCodesBeCommit(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }

        return feeCashDetailMapper.findByAuditDetailCodesBeCommit(codes);
    }

    /**
     * 按结案明细编码查询是否有关闭
     *
     * @param codes
     * @return
     */
    public List<FeeCashDetail> findByAuditDetailCodesBeClose(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }

        return feeCashDetailMapper.findByAuditDetailCodesBeClose(codes);
    }


    public List<FeeCashDetail> findListByCashCode(String cashCode){
        return this.lambdaQuery()
                .eq(FeeCashDetail::getCashCode,cashCode)
                .list();
    }

    public BigDecimal getAllDuifuJeBAuditDetailCode(String auditDetailCode) {
        if(StringUtils.isBlank(auditDetailCode)){
            return BigDecimal.ZERO;
        }
        return feeCashDetailMapper.getAllDuifuJeBAuditDetailCode(auditDetailCode);
    }

    public BigDecimal getAllYuFuAvailableJe(String auditDetailCode, String cashCode) {
        if(StringUtils.isBlank(auditDetailCode)){
            return BigDecimal.ZERO;
        }
        return feeCashDetailMapper.getAllYuFuAvailableJe(auditDetailCode,cashCode);
    }

    public Page<FeeCashDetailVo> findFullAuditedDetailList(Pageable pageable, FeeCashDetailDto dto) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 50);
        }
        Page<FeeCashDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findFullAuditedDetailList(page, dto);
    }
}

