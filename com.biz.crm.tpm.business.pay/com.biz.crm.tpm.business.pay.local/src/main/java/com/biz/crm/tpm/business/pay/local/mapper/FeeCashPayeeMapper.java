package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.FeeCashPayee;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashPayeeDto;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashPayeeVo;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.pay.sdk.vo.HecElectronicReceiptVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 费用兑付收款明细(FeeCashPayee)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-26 14:07:02
 */
public interface FeeCashPayeeMapper extends BaseMapper<FeeCashPayee> {

    /**
     * 分页查询付款信息
     *
     * @param page
     * @param dto
     * @return
     */
    Page<FeeCashPayeeVo> findByConditions(Page<FeeCashPayeeVo> page, @Param("dto") FeeCashPayeeDto dto);

    /**
     * 更新付款单回单
     *
     * @param receiptVoList
     */
    void updateReceipt(@Param("dtoList") List<HecElectronicReceiptVo> receiptVoList);

    /**
     * 更新付款状态
     *
     * @param dtoList
     */
    void hecPayStatusCallback(@Param("dtoList") List<HecCallbackDto> dtoList);

    /**
     * 明细状态 查询  主表状态
     *
     * @param cashCodes
     * @return
     */
    List<FeeCashPayee> findPayStatus(@Param("cashCodes") Set<String> cashCodes);
}

