package com.biz.crm.tpm.business.pay.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.pay.local.dto.ShowTocDto;
import com.biz.crm.tpm.business.pay.sdk.constant.MarketingAuditConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDto;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.service.OaOperateService;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditBillDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditVo;
import com.biz.crm.workflow.sdk.dto.OaCallbackDto;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 方案结案
 *
 * <AUTHOR> Keller
 * @date : 2022-6-16
 */
@Api(tags = "方案结案功能接口")
@RestController
@RequestMapping("/v1/pay/marketingAudit")
@Slf4j
public class MarketingAuditController extends BusinessPageCacheController<MarketingAuditDetailVo, MarketingAuditDetailDto> {
  /**
   * 服务对象
   */
  @Autowired(required = false)
  private MarketingAuditService marketingAuditService;

  @Autowired(required = false)
  private OaOperateService oaOperateService;

  @Autowired(required = false)
  private RedisLockService redisLockService;

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByCode")
  public Result<MarketingAuditVo> findByCode(@ApiParam(name = "code", value = "编号", required = true) String code) {
    try {
      MarketingAuditVo auditVo = this.marketingAuditService.findByCode(code);
      return Result.ok(auditVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param auditDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<?> create(@ApiParam(name = "cacheKey", value = "缓存键")@RequestParam String cacheKey,
                          @ApiParam(name = "auditDto", value = "方案结案") @RequestBody MarketingAuditDto auditDto) {
    try {
      this.marketingAuditService.create(auditDto, cacheKey, false);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   *
   * @param auditDto 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<?> update(@ApiParam(name = "cacheKey", value = "缓存键")@RequestParam String cacheKey,
                          @ApiParam(name = "auditDto", value = "方案结案") @RequestBody MarketingAuditDto auditDto) {
    try {
      this.marketingAuditService.update(auditDto, cacheKey, false);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增保存并提交
   *
   * @param auditDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增保存并提交")
  @PostMapping("submitCreate")
  public Result<?> submitCreate(@ApiParam(name = "cacheKey", value = "缓存键")@RequestParam String cacheKey,
                                @ApiParam(name = "auditDto", value = "方案结案") @RequestBody MarketingAuditDto auditDto) {
    try {
      this.marketingAuditService.submitCreate(auditDto, cacheKey);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 编辑保存并提交
   *
   * @param auditDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "编辑保存并提交")
  @PostMapping("submitUpdate")
  public Result<?> submitUpdate(@ApiParam(name = "cacheKey", value = "缓存键")@RequestParam String cacheKey,
                                @ApiParam(name = "auditDto", value = "方案结案") @RequestBody MarketingAuditDto auditDto) {
    Validate.isTrue(this.redisLockService.batchLock(MarketingAuditConstant.LOCK_PREFIX, Collections.singletonList(auditDto.getId()),
            TimeUnit.MINUTES, 30), "有数据被锁定,请稍后再试");
    try {
      this.marketingAuditService.submitUpdate(auditDto, cacheKey);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    } finally {
      if (this.redisLockService.isBatchLock(MarketingAuditConstant.LOCK_PREFIX, Collections.singletonList(auditDto.getId()))) {
        this.redisLockService.batchUnLock(MarketingAuditConstant.LOCK_PREFIX, Collections.singletonList(auditDto.getId()));
      }
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation(value = "删除数据")
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    Validate.isTrue(this.redisLockService.batchLock(MarketingAuditConstant.LOCK_PREFIX, idList,
            TimeUnit.MINUTES, 30), "有数据被锁定,请稍后再试");
    try {
      this.marketingAuditService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    } finally {
      if (this.redisLockService.isBatchLock(MarketingAuditConstant.LOCK_PREFIX, idList)) {
        this.redisLockService.batchUnLock(MarketingAuditConstant.LOCK_PREFIX, idList);
      }
    }
  }

  /**
   * 更新票扣数据
   *
   * @param pageable
   * @param dto
   */
  @ApiOperation(value = "更新票扣数据")
  @GetMapping("generateBill")
  public Result<Page<MarketingAuditBillDetailVo>> generateBill(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                               @ApiParam(name = "dto", value = "缓存键") MarketingAuditDetailDto dto) {
    try {
      List<MarketingAuditBillDetailVo> billDetailVoList = this.marketingAuditService.generateBill(dto.getCacheKey());

      Page<MarketingAuditBillDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
      page.setTotal(0);
      page.setRecords(Lists.newArrayList());
      if (CollectionUtils.isNotEmpty(billDetailVoList)) {
        page.setTotal(billDetailVoList.size());
        long start = page.offset();
        if (page.getTotal() > start) {
          long end = page.offset() + page.getSize();
          if (page.getTotal() < end) {
            end = page.getTotal();
          }
          List<MarketingAuditBillDetailVo> recordDtoList = billDetailVoList.subList((int) page.offset(), (int) end);
          page.setRecords(recordDtoList);
        }
      }
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 提交审批
   *
   * @param idList 主键结合
   * @return
   */
  @ApiOperation(value = "提交审批")
  @PostMapping("submit")
  public Result<?> submit(@RequestBody List<String> idList) {
    Validate.isTrue(this.redisLockService.batchLock(MarketingAuditConstant.LOCK_PREFIX, idList,
            TimeUnit.MINUTES, 30), "有数据被锁定,请稍后再试");
    try {
      this.marketingAuditService.submit(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    } finally {
      if (this.redisLockService.isBatchLock(MarketingAuditConstant.LOCK_PREFIX, idList)) {
        this.redisLockService.batchUnLock(MarketingAuditConstant.LOCK_PREFIX, idList);
      }
    }
  }

  @ApiOperation(value = "缓存明细导出")
  @GetMapping("findCachePageListImport")
  public Result<Page<MarketingAuditDetailVo>> findCachePageListImport(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                @ApiParam(name = "dto", value = "缓存键") MarketingAuditDetailDto dto) {
    try {
      return this.findCachePageList(pageable,dto.getCacheKey(),dto);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页查询结案明细
   *
   * @param dto
   */
  @ApiOperation(value = "分页查询结案明细")
  @GetMapping("findByConditions")
  public Result<Page<MarketingAuditDetailVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                               @ApiParam(name = "dto", value = "查询dto") MarketingAuditDetailDto dto) {
    try {
      Page<MarketingAuditDetailVo> Page = this.marketingAuditService.findByConditions(pageable, dto);
      return Result.ok(Page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   * 分页查询结案明细报表
   *
   * @param dto
   */
  @ApiOperation(value = "分页查询结案明细报表")
  @GetMapping("findDataViewByConditions")
  public Result<Page<MarketingAuditDetailVo>> findDataViewByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                               @ApiParam(name = "dto", value = "查询dto") MarketingAuditDetailDto dto) {
    try {
      Page<MarketingAuditDetailVo> Page = this.marketingAuditService.findDataViewByConditions(pageable, dto);
      return Result.ok(Page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量选择提交
   *
   * @param dto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "批量选择提交")
  @GetMapping("batchSelect")
  public Result<?> batchSelect(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                               MarketingPlanCaseVo dto) {
    try {
      dto.setChangeFlag(BooleanEnum.FALSE.getCapital());
      this.marketingAuditService.batchSelect(dto, cacheKey);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 流程撤回
   *
   * @param code 编码
   * @return 单条数据
   */
  @ApiOperation(value = "流程撤回")
  @GetMapping("recover")
  public Result<?> recover(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                           @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
    try {
      this.marketingAuditService.recover(code, remark);
      return Result.ok();
    } catch (Exception e) {
      return Result.error(e.getMessage());
    }
  }

  /**
   * 结案审批
   *
   * @param code 编码
   * @return 单条数据
   */
  @ApiOperation(value = "结案审批")
  @GetMapping("oaCallback")
  public Result<?> oaCallback(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                           @RequestParam("status") @ApiParam(name = "status", value = "状态") String status) {
    try {
      this.marketingAuditService.oaCallback(code, status);
      return Result.ok();
    } catch (Exception e) {
      return Result.error(e.getMessage());
    }
  }

  /**
   * 生成兑付单
   *
   * @param codes 编码
   * @return 单条数据
   */
  @ApiOperation(value = "生成兑付单")
  @GetMapping("createFeeCash")
  public Result<?> createFeeCash(@RequestParam("codes") @ApiParam(name = "codes", value = "编码") List<String> codes) {
    try {
      this.marketingAuditService.createFeeCash(codes);
      return Result.ok();
    } catch (Exception e) {
      return Result.error(e.getMessage());
    }
  }

  /**
   * 推送货补池
   *
   * @param codes 主键结合
   * @return
   */
  @ApiOperation(value = "推送货补池")
  @PatchMapping("push")
  public Result<?> push(@ApiParam(name = "codes", value = "编码集合") @RequestParam List<String> codes) {
    try {
      this.marketingAuditService.push(codes);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 自动兑付补偿
   *
   * @param codes 主键结合
   * @return
   */
  @ApiOperation(value = "自动兑付补偿")
  @PostMapping("autoCashReimburse")
  public Result<?> autoCashReimburse(@RequestBody List<String> codes) {
    try {
      this.marketingAuditService.autoCashReimburse(codes);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 自动结案
   */
  @ApiOperation(value = "自动结案")
  @GetMapping("autoAudit")
  public Result<?> autoAudit() {
    this.marketingAuditService.autoAudit();
    return Result.ok();
  }

  /**
   * 定时查询DMS费用池回写兑付金额
   */
  @ApiOperation(value = "定时查询DMS费用池回写兑付金额")
  @GetMapping("replenishmentPoolCashAmount")
  public Result<?> replenishmentPoolCashAmount() {
    this.marketingAuditService.replenishmentPoolCashAmount();
    return Result.ok();
  }

  /**
   * 修改兑付方式
   *
   * @param auditDetailDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "修改兑付方式")
  @PostMapping("updateCashType")
  public Result<?> updateCashType(@ApiParam(name = "auditDetailDto", value = "结案明细") @RequestBody MarketingAuditDetailDto auditDetailDto) {
    try {
      this.marketingAuditService.updateCashType(auditDetailDto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "modifyActivityPrepayRecord")
  @PostMapping("modifyActivityPrepayRecord")
  public Result modifyActivityPrepayRecord(@RequestBody OaCallbackDto dto) {
    marketingAuditService.modifyActivityPrepayRecord(dto);
    return Result.ok();
  }

  /**
   * 定时OA撤回变更、追加、规划及方案结案
   */
  @ApiOperation(value = "定时OA撤回变更、追加、规划及方案结案")
  @GetMapping("recoverAll")
  public Result<?> recoverAll() {
    this.oaOperateService.recoverAll();
    return Result.ok();
  }

  /**
   * 是否展示票扣
   *
   * @param
   * @return
   */
  @ApiOperation(value = "是否展示票扣")
  @GetMapping("showTicket")
  public Result<ShowTocDto> showTicket(@ApiParam(name = "cacheKey", value = "明细缓存键") @RequestParam String cacheKey) {
    try {
      String yn = this.marketingAuditService.showTicket(cacheKey);
      ShowTocDto dto = new ShowTocDto();
      dto.setBeShow(yn);
      return Result.ok(dto);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
