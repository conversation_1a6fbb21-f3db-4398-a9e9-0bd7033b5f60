package com.biz.crm.tpm.business.pay.local.service.internal.process;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.tpm.business.pay.local.service.internal.ActivityPrepayDetailPageCacheHelper;
import com.biz.crm.tpm.business.pay.sdk.dto.ActivityPrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.service.ActivityPrepayService;
import com.biz.crm.tpm.business.pay.sdk.vo.ActivityPrepayDetailImportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityPrepayDetailImportProcess implements ImportProcess<ActivityPrepayDetailImportVo> {

    @Autowired(required = false)
    private ActivityPrepayService activityPrepayService;
    @Autowired(required = false)
    private ActivityPrepayDetailPageCacheHelper activityPrepayDetailPageCacheHelper;


    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, ActivityPrepayDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, ActivityPrepayDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");

        for (Map.Entry<Integer, ActivityPrepayDetailImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            ActivityPrepayDetailImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getSchemeDetailCode()), "活动明细编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getThisPrepayAmountStr()), "预付申请金额，不能为空！");

            try {
                new BigDecimal(vo.getThisPrepayAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "预付申请金额类型转换失败！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, ActivityPrepayDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();

        String cacheKey = (String) params.get("cacheKey");

        List<ActivityPrepayDetailDto> cacheList = activityPrepayService.findCacheList(cacheKey);
        Validate.notEmpty(cacheList, "未获取到任何预付明细缓存");

        Map<String, ActivityPrepayDetailDto> cashDetailMap = cacheList.stream().collect(Collectors.toMap(ActivityPrepayDetailDto::getSchemeDetailCode, Function.identity(), (a, b) -> a));

        List<ActivityPrepayDetailDto> updateCacheList = new ArrayList<>();
        for (Map.Entry<Integer, ActivityPrepayDetailImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            ActivityPrepayDetailImportVo vo = row.getValue();

            ActivityPrepayDetailDto cashDetailDto = cashDetailMap.get(vo.getSchemeDetailCode());
            if (cashDetailDto != null) {
                cashDetailDto.setThisPrepayAmount(new BigDecimal(vo.getThisPrepayAmountStr().trim()));
                updateCacheList.add(cashDetailDto);
            } else {
                this.validateIsTrue(false, "未找到活动明细编码对应的预付明细！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        activityPrepayDetailPageCacheHelper.importNewItem(cacheKey, updateCacheList);

        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<ActivityPrepayDetailImportVo> findCrmExcelVoClass() {
        return ActivityPrepayDetailImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "ACTIVITY_PREPAY_DETAIL_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "预付明细导入模板-活动预付";
    }
}
