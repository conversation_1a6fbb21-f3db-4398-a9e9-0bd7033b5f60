package com.biz.crm.tpm.business.pay.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.AuditBill;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 核销账单;(tpm_audit_bill)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-17
 */
@Mapper
public interface AuditBillMapper extends BaseMapper<AuditBill> {

  boolean addAuditAmount(@Param("activitiesDetailCode") String activitiesDetailCode, @Param("amount") BigDecimal amount, @Param("tenantCode") String tenantCode);

  boolean reduceAuditAmount(@Param("activitiesDetailCode") String activitiesDetailCode, @Param("amount") BigDecimal amount, @Param("tenantCode") String tenantCode);
}
