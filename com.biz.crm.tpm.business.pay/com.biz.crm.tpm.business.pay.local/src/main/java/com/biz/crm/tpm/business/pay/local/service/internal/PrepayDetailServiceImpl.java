package com.biz.crm.tpm.business.pay.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.pay.local.service.PrepayDetailService;
import com.biz.crm.tpm.business.pay.local.entity.PrepayDetail;
import com.biz.crm.tpm.business.pay.local.repository.PrepayDetailRepository;
import com.biz.crm.tpm.business.pay.local.service.PrepayBillService;
import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayBillDto;
import com.biz.crm.tpm.business.pay.sdk.dto.PrepayDetailDto;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayBillVo;
import com.biz.crm.tpm.business.pay.sdk.vo.PrepayDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 活动预付明细;(tpm_prepay_detail)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-23
 */
@Service("prepayDetailService")
public class PrepayDetailServiceImpl implements PrepayDetailService {
  @Autowired
  private PrepayDetailRepository prepayDetailRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private PrepayBillService prepayBillService;
  @Autowired
  private GenerateCodeService generateCodeService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<PrepayDetailVo> findByConditions(Pageable pageable, PrepayDetailDto dto) {
    pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new PrepayDetailDto();
    }
    return this.prepayDetailRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public PrepayDetailVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    PrepayDetail prepayDetail = this.prepayDetailRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (prepayDetail == null) {
      return null;
    }
    PrepayDetailVo prepayDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(prepayDetail, PrepayDetailVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetail(prepayDetailVo);
    return prepayDetailVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<PrepayDetailVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<PrepayDetail> prepayDetails = this.prepayDetailRepository.findByIds(ids);
    Collection<PrepayDetailVo> prepayDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(prepayDetails, PrepayDetail.class, PrepayDetailVo.class, LinkedHashSet.class, ArrayList.class);
    prepayDetailVos.forEach(this::fillDetail);
    return Lists.newArrayList(prepayDetailVos);
  }


  /**
   * 根绝业务编号prepayCode获取业务数据
   *
   * @param prepayCode
   * @return 单条数据
   */
  @Override
  public List<PrepayDetailVo> findByPrepayCode(String prepayCode) {
    if (StringUtils.isBlank(prepayCode)) {
      return Collections.emptyList();
    }
    List<PrepayDetail> prepayDetails = this.prepayDetailRepository.findByPrepayCode(prepayCode);
    if (CollectionUtils.isEmpty(prepayDetails)) {
      return Collections.emptyList();
    }
    Collection<PrepayDetailVo> prepayDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(prepayDetails, PrepayDetail.class, PrepayDetailVo.class, LinkedHashSet.class, ArrayList.class);
    prepayDetailVos.forEach(this::fillDetail);
    return Lists.newArrayList(prepayDetailVos);
  }

  /**
   * 填充明细
   *
   * @param prepayDetailVo
   */
  private void fillDetail(PrepayDetailVo prepayDetailVo) {
    PrepayBillVo prepayBillVo = this.prepayBillService.findByActivitiesDetailCode(prepayDetailVo.getActivitiesDetailCode());
    prepayDetailVo.setPrepaidAmount(prepayBillVo.getPrepaidAmount());
  }


  /**
   * 新增数据
   *
   * @param prepayDetailDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public PrepayDetailVo create(PrepayDetailDto prepayDetailDto) {
    prepayDetailDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    String code = this.generateCodeService.generateCode(PayConstant.PREPAY_DETAIL_LADDER_CODE, 1).get(0);
    prepayDetailDto.setPrepayDetailCode(code);
    prepayDetailDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    this.createValidate(prepayDetailDto);
    PrepayDetail prepayDetail = this.nebulaToolkitService.copyObjectByWhiteList(prepayDetailDto, PrepayDetail.class, LinkedHashSet.class, ArrayList.class);
    prepayDetail.setTenantCode(TenantUtils.getTenantCode());
    this.prepayDetailRepository.saveOrUpdate(prepayDetail);
    PrepayDetailVo prepayDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(prepayDetail, PrepayDetailVo.class, LinkedHashSet.class, ArrayList.class);

    prepayDetailVo.setId(prepayDetail.getId());
    PrepayBillDto prepayBillDto = new PrepayBillDto();
    prepayBillDto.setApplyAmount(prepayDetailVo.getApplyAmount());
    prepayBillDto.setActivitiesDetailCode(prepayDetailVo.getActivitiesDetailCode());
    prepayBillDto.setPrepaidAmount(BigDecimal.ZERO);
    prepayBillService.init(prepayBillDto);
    return prepayDetailVo;
  }

  /**
   * 批量新增
   *
   * @param prepayDetailDtos
   * @return
   */
  @Transactional
  @Override
  public List<PrepayDetailVo> createBatch(Collection<PrepayDetailDto> prepayDetailDtos) {
    if (CollectionUtils.isEmpty(prepayDetailDtos)) {
      return Lists.newArrayList();
    }
    List<PrepayDetailVo> prepayDetailVos = Lists.newArrayList();
    for (PrepayDetailDto prepayDetailDto : prepayDetailDtos) {
      PrepayDetailVo prepayDetailVo = this.create(prepayDetailDto);
      prepayDetailVos.add(prepayDetailVo);
    }
    return prepayDetailVos;
  }

  /**
   * 修改新据
   *
   * @param prepayDetailDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public PrepayDetailVo update(PrepayDetailDto prepayDetailDto) {
    this.updateValidate(prepayDetailDto);
    PrepayDetail prepayDetail = this.prepayDetailRepository.findByIdAndTenantCode(prepayDetailDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(prepayDetail, "修改数据不存在，请检查！");
    prepayDetail.setPrepayAmount(prepayDetailDto.getPrepayAmount());
    prepayDetail.setPayBy(prepayDetailDto.getPayBy());
    prepayDetail.setReason(prepayDetailDto.getReason());
    prepayDetail.setPayer(prepayDetailDto.getPayer());
    prepayDetail.setPayerAccount(prepayDetailDto.getPayerAccount());
    prepayDetail.setPayee(prepayDetailDto.getPayee());
    prepayDetail.setPayeeAccount(prepayDetailDto.getPayeeAccount());
    prepayDetail.setCostTypeDetailCode(prepayDetailDto.getCostTypeDetailCode());
    prepayDetail.setCostTypeDetailName(prepayDetailDto.getCostTypeDetailName());
    prepayDetail.setCustomerName(prepayDetailDto.getCustomerName());
    prepayDetail.setCustomerCode(prepayDetailDto.getCustomerCode());
    prepayDetail.setTerminalName(prepayDetailDto.getTerminalName());
    prepayDetail.setTerminalCode(prepayDetailDto.getTerminalCode());
    prepayDetail.setTenantCode(TenantUtils.getTenantCode());
    this.prepayDetailRepository.saveOrUpdate(prepayDetail);
    PrepayDetailVo prepayDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(prepayDetail, PrepayDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return prepayDetailVo;
  }

  /**
   * 批量修改据
   *
   * @param prepayDetailDtos 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public List<PrepayDetailVo> updateBatch(Collection<PrepayDetailDto> prepayDetailDtos) {
    if (CollectionUtils.isEmpty(prepayDetailDtos)) {
      return Collections.emptyList();
    }
    String code = prepayDetailDtos.stream().findFirst().get().getPrepayCode();
    List<PrepayDetailVo> dbPrepayDetailVos = this.findByPrepayCode(code);
    Set<String> dbIds = dbPrepayDetailVos.stream().map(PrepayDetailVo::getId).collect(Collectors.toSet());
    Set<String> currentIds = prepayDetailDtos.stream().map(PrepayDetailDto::getId).collect(Collectors.toSet());
    Set<String> delData = Sets.difference(dbIds, currentIds);
    if (!CollectionUtils.isEmpty(delData)) {
      this.delete(delData);
    }
    List<PrepayDetailDto> addDtos = prepayDetailDtos.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
    List<PrepayDetailDto> updateDtos = prepayDetailDtos.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
    List<PrepayDetailVo> prepayDetailVos = this.createBatch(addDtos);
    if (!CollectionUtils.isEmpty(updateDtos)) {
      updateDtos.forEach(item -> {
        prepayDetailVos.add(this.update(item));
      });
    }
    return prepayDetailVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<PrepayDetail> prepayDetails = this.prepayDetailRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(prepayDetails)) {
      return;
    }
    Collection<PrepayDetailVo> prepayDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(prepayDetails, PrepayDetail.class, PrepayDetailVo.class, LinkedHashSet.class, ArrayList.class);
    this.prepayDetailRepository.removeByIds(ids);
  }

  /**
   * 创建验证
   *
   * @param prepayDetailDto
   */
  private void createValidate(PrepayDetailDto prepayDetailDto) {
    Validate.notNull(prepayDetailDto, "新增时，对象信息不能为空！");
    Validate.isTrue(prepayDetailDto.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(prepayDetailDto.getPrepayCode(), "新增数据时，预付编号不能为空！");
    Validate.notNull(prepayDetailDto.getApplyAmount(), "新增数据时，申请金额不能为空！");
    Validate.notBlank(prepayDetailDto.getActivitiesDetailCode(), "新增数据时，活动明细编码不能为空！");
    Validate.notNull(prepayDetailDto.getPrepayAmount(), "新增数据时，预付金额不能为空！");
    Validate.notBlank(prepayDetailDto.getPayBy(), "新增数据时，支付方式不能为空！");
    Validate.notBlank(prepayDetailDto.getCostTypeDetailCode(), "新增数据时，活动细类编号不能为空！");
    Validate.notBlank(prepayDetailDto.getCostTypeDetailName(), "新增数据时，活动细类名称不能为空！");

    PrepayBillVo prepayBillVo = this.prepayBillService.findByActivitiesDetailCode(prepayDetailDto.getActivitiesDetailCode());
    BigDecimal prepayAmount = prepayDetailDto.getPrepayAmount();
    BigDecimal applyAmount = prepayDetailDto.getApplyAmount();
    BigDecimal preparedAmount = BigDecimal.ZERO;
    if (prepayBillVo != null) {
      preparedAmount = prepayBillVo.getPrepaidAmount();
    }
    Validate.isTrue(applyAmount.compareTo(prepayAmount.add(preparedAmount)) >= 0, "预付金额与已预付金额不能超出申请金额");
  }

  /**
   * 修改验证
   *
   * @param prepayDetailDto
   */
  private void updateValidate(PrepayDetailDto prepayDetailDto) {
    Validate.notNull(prepayDetailDto, "修改时，对象信息不能为空！");
    Validate.notBlank(prepayDetailDto.getId(), "修改数据时，主键不能为空！");
    Validate.notNull(prepayDetailDto.getPrepayAmount(), "修改数据时，预付金额不能为空！");
    Validate.notBlank(prepayDetailDto.getPayBy(), "修改数据时，支付方式不能为空！");
    Validate.notBlank(prepayDetailDto.getCostTypeDetailCode(), "修改数据时，活动细类编号不能为空！");
    Validate.notBlank(prepayDetailDto.getCostTypeDetailName(), "修改数据时，活动细类名称不能为空！");

    PrepayBillVo prepayBillVo = this.prepayBillService.findByActivitiesDetailCode(prepayDetailDto.getActivitiesDetailCode());
    BigDecimal prepayAmount = prepayDetailDto.getPrepayAmount();
    BigDecimal applyAmount = prepayDetailDto.getApplyAmount();
    BigDecimal preparedAmount = BigDecimal.ZERO;
    if (prepayBillVo != null) {
      preparedAmount = prepayBillVo.getPrepaidAmount();
    }
    Validate.isTrue(applyAmount.compareTo(prepayAmount.add(preparedAmount)) >= 0, "预付金额与已预付金额不能超出申请金额");

  }

}