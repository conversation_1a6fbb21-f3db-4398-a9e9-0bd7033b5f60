<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pay</artifactId>
        <groupId>com.biz.crm.tpm.business.pay</groupId>
        <version>202405</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pay-local</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.biz.crm.tpm.business.pay</groupId>
            <artifactId>pay-sdk</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.biz.crm.dms.business</groupId>
            <artifactId>costpool-replenishment-sdk</artifactId>
            <version>${crm.dms.version}</version>
        </dependency>
        <dependency>
            <groupId>com.biz.crm.dms.business</groupId>
            <artifactId>costpool-discount-sdk</artifactId>
            <version>${crm.dms.version}</version>
        </dependency>
        <dependency>
            <groupId>com.biz.crm.common</groupId>
            <artifactId>log-sdk</artifactId>
            <version>${crm.common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.biz.crm.tpm.business.activities</groupId>
            <artifactId>activities-scheme</artifactId>
            <version>${business.version}</version>
        </dependency>
        <dependency>
            <groupId>com.biz.crm.dms.business</groupId>
            <artifactId>delivery-sdk</artifactId>
            <version>${crm.dms.version}</version>
        </dependency>
        <dependency>
            <groupId>com.biz.crm.dms.business</groupId>
            <artifactId>order-common-sdk</artifactId>
            <version>${crm.dms.version}</version>
        </dependency>
        <dependency>
            <groupId>com.biz.crm.mdm.business</groupId>
            <artifactId>org-sdk</artifactId>
            <version>${crm.mdm.version}</version>
        </dependency>
        <dependency>
            <groupId>com.biz.crm.tpm.business</groupId>
            <artifactId>rebate-local</artifactId>
            <version>${business.version}</version>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>
</project>