<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.biz.crm.tpm.boot</groupId>
    <artifactId>tpm-boot</artifactId>
    <version>202405</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>tpm-admin-web</artifactId>

  <dependencies>
    <!-- 和技术底座有关的starter start -->
    <dependency>
      <groupId>com.biz-united.nebula.venus.client</groupId>
      <artifactId>venus-client-local-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.script</groupId>
      <artifactId>script-local-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.utils</groupId>
      <artifactId>nebula-utils</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.security</groupId>
      <artifactId>security-local-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.task</groupId>
      <artifactId>task-local-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.europa</groupId>
      <artifactId>europa-local-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.europa.database</groupId>
      <artifactId>europa-database-register-local</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.mars</groupId>
      <artifactId>mars-plus-fegin-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.europa.database</groupId>
      <artifactId>europa-database-enclosure-local-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.rbac</groupId>
      <artifactId>rbac-local-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.competence</groupId>
      <artifactId>competence-local-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.competence.tacit</groupId>
      <artifactId>competence-tacit-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.gateway.websocket</groupId>
      <artifactId>gateway-websocket-client-local-starter</artifactId>
      <version>${nebula.version}</version>
    </dependency>
    <!--Lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
    </dependency>
    <!-- 以下是业务模块依赖 -->

    <!-- 和外部接口依赖有关 -->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>login-log-feign</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.common.form</groupId>
      <artifactId>form-local-starter</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz-united.nebula.event</groupId>
      <artifactId>event-local-starter</artifactId>
      <version>${nebula.version}</version>
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>-->
<!--          <groupId>com.alibaba.cloud</groupId>-->
<!--        </exclusion>-->
<!--        <exclusion>-->
<!--          <artifactId>nacos-client</artifactId>-->
<!--          <groupId>com.alibaba.nacos</groupId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
    </dependency>
    <!--导入导出-->
    <dependency>
      <groupId>com.biz.crm.common.ie</groupId>
      <artifactId>ie-local-starter</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
    <!--页面引擎-->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>table-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <!--字典-->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>dictionary-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <!--企业组织-->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>org-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <!--产品-->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>product-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <!--产品层级-->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>product-level-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <!--客户信息-->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>customer-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <!--终端信息-->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>terminal-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <!--折扣池-->
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>costpool-discount-feign-starter</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <!--货补池-->
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>costpool-replenishment-feign-starter</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <!--发货-->
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>delivery-feign-starter</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <!--订单-->
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>order-sdk</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <!--职位-->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>position-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <!--业务日志-->
    <dependency>
      <groupId>com.biz.crm.common</groupId>
      <artifactId>log-local-starter</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
    <!--错误日志-->
    <dependency>
      <groupId>com.biz.crm.common.error.logs</groupId>
      <artifactId>error-logs-local-starter</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
    <!-- 用户身份描述相关 -->
    <dependency>
      <groupId>com.biz.crm.common.identity</groupId>
      <artifactId>crm-identity-facturer</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
    <!-- BPM工作流 -->
    <dependency>
      <groupId>com.biz.crm</groupId>
      <artifactId>workflow-client-starter</artifactId>
      <version>${crm.workflow.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm</groupId>
      <artifactId>workflow-feign-starter</artifactId>
      <version>${crm.workflow.version}</version>
    </dependency>
    <!--
        注册中心 ，SpringCloud Feign在Hoxton.M2 RELEASED版本之后抛弃了Ribbon
        所以nacos的的客户端也要排除 Ribbon
    -->
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
      <version>${spring.cloud.nacos.discovery.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.cloud</groupId>
          <artifactId>spring-cloud-netflix-ribbon</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
      <version>${spring.cloud.nacos.discovery.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-bootstrap</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-loadbalancer</artifactId>
    </dependency>

    <dependency>
      <groupId>com.biz.crm.tpm.business.budget</groupId>
      <artifactId>budget-local-starter</artifactId>
      <version>${business.version}</version>
    </dependency>

    <dependency>
      <groupId>com.biz.crm.tpm.business.activities</groupId>
      <artifactId>activities-local-starter</artifactId>
      <version>${business.version}</version>
    </dependency>

    <dependency>
      <groupId>com.biz.crm.tpm.business.pay</groupId>
      <artifactId>pay-local-starter</artifactId>
      <version>${business.version}</version>
    </dependency>

    <dependency>
      <groupId>com.biz.crm.tpm.business</groupId>
      <artifactId>rebate-local-starter</artifactId>
      <version>${business.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.tpm.business.activities</groupId>
      <artifactId>activities-scheme</artifactId>
      <version>${business.version}</version>
    </dependency>
    <!-- 定额活动执行表单 -->
    <dependency>
      <groupId>com.biz.crm.tpm.business.activities</groupId>
      <artifactId>activities-quota</artifactId>
      <version>${business.version}</version>
    </dependency>
    <!-- 会议活动执行表单 -->
    <dependency>
      <groupId>com.biz.crm.tpm.business.activities</groupId>
      <artifactId>activities-meeting</artifactId>
      <version>${business.version}</version>
    </dependency>
    <!-- 市场活动执行表单 -->
    <dependency>
      <groupId>com.biz.crm.tpm.business.activities</groupId>
      <artifactId>activities-market</artifactId>
      <version>${business.version}</version>
    </dependency>
    <!-- 市场活动执行表单 -->
    <dependency>
      <groupId>com.biz.crm.tpm.business.activities</groupId>
      <artifactId>activities-simple-audit</artifactId>
      <version>${business.version}</version>
    </dependency>
    <!--测试-->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-actuator</artifactId>
      <version>${springboot.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-actuator-autoconfigure</artifactId>
      <version>${springboot.version}</version>
    </dependency>
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-spring-webmvc</artifactId>
      <version>${springfox.version}</version>
    </dependency>
    <dependency>
      <groupId>com.github.xiaoymin</groupId>
      <artifactId>knife4j-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>${snakeyaml.version}</version>
    </dependency>
    <!-- 日志接口 start -->
    <dependency>
      <groupId>com.biz.crm.business.common.log</groupId>
      <artifactId>crm-log-local-starter</artifactId>
      <version>${business.common.version}</version>
    </dependency>
    <!-- 日志接口 end -->
    <!-- 认证接口 start -->
    <dependency>
      <groupId>com.biz.crm.business.common.auth</groupId>
      <artifactId>auth-feign-starter</artifactId>
      <version>${business.common.version}</version>
    </dependency>
    <!-- 认证接口 end -->
    <!--     rocketmq start -->
    <dependency>
      <groupId>com.biz.crm.business.common</groupId>
      <artifactId>crm-common-rocketmq</artifactId>
    </dependency>
    <!-- rocketmq end -->
    <!--   自定义ie导入 -->
    <dependency>
      <groupId>com.biz.crm.business</groupId>
      <artifactId>crm-business-ie-local-starter</artifactId>
      <version>${business.common.version}</version>
    </dependency>
  </dependencies>

  <build>
    <finalName>crm-tpm</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
          <include>**/*.properties</include>
        </includes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
  </build>
</project>
