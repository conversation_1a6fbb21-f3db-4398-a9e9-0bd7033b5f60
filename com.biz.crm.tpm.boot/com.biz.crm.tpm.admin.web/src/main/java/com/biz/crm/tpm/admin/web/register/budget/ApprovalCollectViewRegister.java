package com.biz.crm.tpm.admin.web.register.budget;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>TPM-预算中心-核销采集信息策略配置数据视图注册器
 * 基于nebula的数据视图提供核销采集信息策略配置列表查询功能
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Component
public class ApprovalCollectViewRegister implements DataviewRegister {
  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-核销采集信息";
  }

  @Override
  public String buildSql() {
    return " select distinct a.* " +
            "from tpm_approval_collect a " +
            "where a.tenant_code = :tenantCode " +
            "and a.del_flag = '009' ";
  }
}
