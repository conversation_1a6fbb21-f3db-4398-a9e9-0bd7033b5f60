package com.biz.crm.tpm.admin.web.preactualdetailreport.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.admin.web.preactualdetailreport.entity.PreActualDetailReportEntity;
import com.biz.crm.tpm.admin.web.preactualdetailreport.service.strategy.PreActualDetailReportAbstractBuilder;
import com.biz.crm.tpm.admin.web.vo.PreActualDetailReportVo;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.stagingscheme.entity.TpmStagingSalesPlanEntity;
import com.biz.crm.tpm.business.activities.stagingscheme.entity.TpmStagingSchemeEntity;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.activities.stagingscheme.service.TpmStagingSalesPlanService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 22:26
 **/
@Slf4j
@Component
public class PreActualDetailReportComponent {

    @Resource
    private PreActualDetailReportService preActualDetailReportService;

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private ApplicationContext context;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    @Resource
    private ITpmStagingSchemeService tpmStagingSchemeService;

    @Resource
    private TpmStagingSalesPlanService tpmStagingSalesPlanService;


    @Async("seqReportPool")
    public void syncPreActualDetailReport(PreActualDetailReportVo detailReportVo, List<MarketingPlanCaseVo> caseVoList) {
        List<String> schemeCodeList = caseVoList.stream().map(x -> x.getSchemeCode()).distinct().collect(Collectors.toList());
        loginUserService.refreshAuthentication(null);
        //查询变更后的销售计划
        List<MarketingSalesPlanVo> changeSalesPlanList = marketingSalesPlanService.findListBySchemeCodes(schemeCodeList);
//        Map<String, List<MarketingSalesPlanVo>> changeSalesPlanMap = changeSalesPlanList.stream().collect(Collectors.groupingBy(x -> x.getSchemeCode()));
        //查询变更前的销售计划
        List<TpmStagingSalesPlanEntity> stagingSchemeEntities = tpmStagingSalesPlanService.findListByCondition(schemeCodeList);
        List<MarketingSalesPlanVo> salesPlanList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(stagingSchemeEntities)) {
            salesPlanList = new ArrayList<>();
        } else {
//            List<String> notChangeSchemeCodeList = Lists.newArrayList();
            Map<String, List<TpmStagingSalesPlanEntity>> stagingSchemeMap = stagingSchemeEntities.stream().collect(Collectors.groupingBy(x -> x.getReleaseId()));
//            for (Map.Entry<String, List<MarketingSalesPlanVo>> entry : changeSalesPlanMap.entrySet()) {
//                if (!stagingSchemeMap.containsKey(entry.getKey())) {
//                    notChangeSchemeCodeList.add(entry.getKey());
//                }
//            }
            for (Map.Entry<String, List<TpmStagingSalesPlanEntity>> entry : stagingSchemeMap.entrySet()) {
                Optional<TpmStagingSalesPlanEntity> optional = entry.getValue().stream().sorted(Comparator.comparing(TpmStagingSalesPlanEntity::getCreateTime)).findFirst();
                List<MarketingSalesPlanVo> salesPlanVoList = JSONObject.parseArray(optional.get().getJsonStr(), MarketingSalesPlanVo.class);
                salesPlanList.addAll(salesPlanVoList);
            }
//            for (String s : notChangeSchemeCodeList) {
//                if (changeSalesPlanMap.containsKey(s)) {
//                    salesPlanList.addAll(changeSalesPlanMap.get(s));
//                }
//            }
        }
        salesPlanList = salesPlanList.stream().filter(x -> detailReportVo.getYears().equals(x.getYears()) && detailReportVo.getCustomerCode().equals(x.getCustomerCode())).collect(Collectors.toList());
        changeSalesPlanList = changeSalesPlanList.stream().filter(x -> detailReportVo.getYears().equals(x.getYears()) && detailReportVo.getCustomerCode().equals(x.getCustomerCode())).collect(Collectors.toList());
        List<PreActualDetailReportVo> actualDetailReportVoList = Lists.newArrayList();
        PreActualDetailReportAbstractBuilder.builder(context, actualDetailReportVoList, salesPlanList, changeSalesPlanList)
                .init(detailReportVo, caseVoList)
                .loadDataBase()
                .income()
                .cost()
                .grossProfit()
                .logisticsCosts()
//                    .giftCost()
                .budgeSubject()
//                    .surroundingMaterials()
                .share()
                .profit();
        List<String> indexKeyList = Lists.newArrayList();
        actualDetailReportVoList.forEach(x -> {
            x.setTenantCode(TenantUtils.getTenantCode());
            x.setId(null);
            String indexKey = null;
            if (ObjectUtils.isEmpty(x.getActYears())) {
                indexKey = x.getCustomerCode() + ":" + x.getOrgCode() + ":" + x.getYears() + ":" + x.getProjectCode();
            } else {
                indexKey = x.getCustomerCode() + ":" + x.getOrgCode() + ":" + x.getYears() + ":" + x.getActYears() + ":" + x.getProjectCode();
            }
            x.setIndexKey(indexKey);
            indexKeyList.add(indexKey);
        });
        List<PreActualDetailReportEntity> entityList = JsonUtils.convert(actualDetailReportVoList, List.class, PreActualDetailReportEntity.class);
        List<PreActualDetailReportEntity> oldEntityList = preActualDetailReportService.lambdaQuery()
                .in(PreActualDetailReportEntity::getIndexKey, indexKeyList)
                .list();
        if (!CollectionUtils.isEmpty(oldEntityList)) {
            Map<String, PreActualDetailReportEntity> oldMap = oldEntityList.stream().collect(Collectors.toMap(x -> x.getIndexKey(), Function.identity()));
            for (PreActualDetailReportEntity entity : entityList) {
                if (oldMap.containsKey(entity.getIndexKey())) {
                    PreActualDetailReportEntity oldEntity = oldMap.get(entity.getIndexKey());
                    entity.setCenterCode(oldEntity.getCenterCode());
                    entity.setCenterName(oldEntity.getCenterName());
                    entity.setDepartmentOneCode(oldEntity.getDepartmentOneCode());
                    entity.setDepartmentOneName(oldEntity.getDepartmentOneName());
                    entity.setDepartmentTwoCode(oldEntity.getDepartmentTwoCode());
                    entity.setDepartmentTwoName(oldEntity.getDepartmentTwoName());
                    entity.setDepartmentThreeCode(oldEntity.getDepartmentThreeCode());
                    entity.setDepartmentThreeName(oldEntity.getDepartmentThreeName());
                    entity.setId(oldEntity.getId());
                }
            }
        }
        int oldEntitySize = entityList.size();

        List<PreActualDetailReportEntity>  newEntityList = new ArrayList<>(entityList.stream().collect(Collectors.toMap(PreActualDetailReportEntity::getIndexKey, Function.identity())).values());
        if(newEntityList.size()!=oldEntitySize){

            List<String> indexKeyLists = entityList.stream()
                    .collect(Collectors.toMap(
                            PreActualDetailReportEntity::getIndexKey,
                            Collections::singletonList,
                            (left, right) -> {
                                List<PreActualDetailReportEntity> combined = new ArrayList<>(left);
                                combined.addAll(right);
                                return combined;
                            }
                    )).entrySet().stream()
                    .filter(entry -> entry.getValue().size() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            log.info("预实明细报表生成出错了============index_key  badIndexKey {}", JSONObject.toJSONString(indexKeyLists));
        }
        entityList = newEntityList;

        List<PreActualDetailReportEntity> list = entityList.stream().filter(item -> item.getIndexKey().split("-").length > 3).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(list)){
            String s = JSONObject.toJSONString(list);
            log.error("预实明细报表生成出错了============ list {}", s.length()>1000?s.substring(0,999):s);
        }

        if(CollectionUtils.isEmpty(entityList)){
            return;
        }
        preActualDetailReportService.saveOrUpdateBatch(entityList);
    }

}
