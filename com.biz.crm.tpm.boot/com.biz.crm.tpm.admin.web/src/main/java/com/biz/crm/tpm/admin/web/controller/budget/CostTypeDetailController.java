package com.biz.crm.tpm.admin.web.controller.budget;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeDetail;
import com.biz.crm.tpm.business.budget.local.repository.CostTypeDetailRepository;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 活动细类配置
 * <AUTHOR>
 * @Date 2024/7/25 18:21
 */
@RequestMapping("/v1/budget/costTypeDetails")
@RestController
@Api(tags = "活动细类配置")
public class CostTypeDetailController {
    /**
     * 服务对象
     */
    @Autowired
    private CostTypeDetailVoService costTypeDetailVoService;
    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;
    @Autowired
    private CostTypeDetailRepository costTypeDetailRepository;

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation(value = "删除数据")
    public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
        List<CostTypeDetail> costTypeDetailsses = this.costTypeDetailRepository.findByIds(idList);
        if (CollectionUtils.isEmpty(costTypeDetailsses)) {
            return Result.ok();
        }
        List<String> detailCodes = costTypeDetailsses.stream().map(CostTypeDetail::getDetailCode).collect(Collectors.toList());
        List<MarketingPlanCaseVo> planCaseVos = marketingPlanCaseService.findListByDetailCodes(detailCodes);
        Validate.isTrue(CollectionUtils.isEmpty(planCaseVos), "在指引或营销方案中规划的细类【%s】不允许删除",
                planCaseVos.stream().map(MarketingPlanCaseVo::getDetailCode).collect(Collectors.joining(",")));
        costTypeDetailVoService.delete(idList);
        return Result.ok();
    }
}
