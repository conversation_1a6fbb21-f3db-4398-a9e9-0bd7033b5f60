package com.biz.crm.tpm.admin.web.controller.giftrebatereport.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.giftrebatereport.dto.GiftAndRebateOccupyAmountReportDto;
import com.biz.crm.tpm.business.activities.giftrebatereport.service.GiftAndRebateOccupyAmountReportService;
import com.biz.crm.tpm.business.activities.giftrebatereport.vo.GiftAndRebateOccupyAmountReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 搭赠及返利实时金额占用报表Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/v1/giftAndRebateOccupyAmountReportController")
@Api(tags = "搭赠及返利实时金额占用报表")
@Slf4j
public class GiftAndRebateOccupyAmountReportController {

    @Autowired
    private GiftAndRebateOccupyAmountReportService giftAndRebateOccupyAmountReportService;

    @ApiOperation(value = "分页查询搭赠及返利实时金额占用报表")
    @PostMapping("findByConditions")
    public Result<Page<GiftAndRebateOccupyAmountReportVo>> findByConditions(
            @PageableDefault(50) Pageable pageable,
            @RequestBody GiftAndRebateOccupyAmountReportDto dto) {
        try {
            Page<GiftAndRebateOccupyAmountReportVo> result = giftAndRebateOccupyAmountReportService.findByConditions(pageable, dto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("分页查询搭赠及返利实时金额占用报表失败", e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "查询搭赠及返利实时金额占用报表列表")
    @PostMapping("findListByConditions")
    public Result<List<GiftAndRebateOccupyAmountReportVo>> findListByConditions(
            @RequestBody GiftAndRebateOccupyAmountReportDto dto) {
        try {
            List<GiftAndRebateOccupyAmountReportVo> result = giftAndRebateOccupyAmountReportService.findListByConditions(dto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("查询搭赠及返利实时金额占用报表列表失败", e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据ID查询报表详情")
    @GetMapping("findById")
    public Result<GiftAndRebateOccupyAmountReportVo> findById(
            @RequestParam("id") @ApiParam(name = "id", value = "主键ID") String id) {
        try {
            GiftAndRebateOccupyAmountReportVo result = giftAndRebateOccupyAmountReportService.findById(id);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("根据ID查询报表详情失败", e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据方案明细编码查询报表数据")
    @GetMapping("findBySchemeDetailCode")
    public Result<List<GiftAndRebateOccupyAmountReportVo>> findBySchemeDetailCode(
            @RequestParam("schemeDetailCode") @ApiParam(name = "schemeDetailCode", value = "方案明细编码") String schemeDetailCode) {
        try {
            List<GiftAndRebateOccupyAmountReportVo> result = giftAndRebateOccupyAmountReportService.findBySchemeDetailCode(schemeDetailCode);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("根据方案明细编码查询报表数据失败", e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "刷新报表数据")
    @PostMapping("refreshReportData")
    public void refreshReportData(@RequestBody GiftAndRebateOccupyAmountReportDto dto) {
        try {
            giftAndRebateOccupyAmountReportService.refreshReportData(dto);
        } catch (Exception e) {
            log.error("刷新报表数据失败", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @ApiOperation(value = "导出报表数据")
    @PostMapping("exportData")
    public Result<List<GiftAndRebateOccupyAmountReportVo>> exportData(
            @RequestBody GiftAndRebateOccupyAmountReportDto dto) {
        try {
            List<GiftAndRebateOccupyAmountReportVo> result = giftAndRebateOccupyAmountReportService.exportData(dto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("导出报表数据失败", e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "计算占用金额")
    @GetMapping("calculateOccupyAmount")
    public void calculateOccupyAmount() {
        try {
            giftAndRebateOccupyAmountReportService.calculateOccupyAmount(null);
        } catch (Exception e) {
            log.error("计算占用金额失败", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @ApiOperation(value = "计算上月占用金额")
    @GetMapping("calculateLastMonthOccupyAmount")
    public void calculateLastMonthOccupyAmount() {
        try {
            giftAndRebateOccupyAmountReportService.calculateLastMonthOccupyAmount();
        } catch (Exception e) {
            log.error("lastMonth计算占用金额失败", e);
            throw new RuntimeException(e.getMessage());
        }
    }


}
