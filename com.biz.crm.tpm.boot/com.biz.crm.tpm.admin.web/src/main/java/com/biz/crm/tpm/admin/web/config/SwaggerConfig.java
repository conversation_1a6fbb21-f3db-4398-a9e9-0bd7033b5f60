package com.biz.crm.tpm.admin.web.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.endpoint.web.CorsEndpointProperties;
import org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties;
import org.springframework.boot.actuate.autoconfigure.web.server.ManagementPortType;
import org.springframework.boot.actuate.endpoint.ExposableEndpoint;
import org.springframework.boot.actuate.endpoint.web.*;
import org.springframework.boot.actuate.endpoint.web.annotation.ControllerEndpointsSupplier;
import org.springframework.boot.actuate.endpoint.web.annotation.ServletEndpointsSupplier;
import org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @describe: swagger 配置信息
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2022.10.12 21:20
 */
@Configuration
@EnableSwagger2WebMvc
public class SwaggerConfig {

  @Value("${knife4j.title:crm接口文档}")
  private String title;
  @Value("${knife4j.description:接口文档描述}")
  private String description;
  @Value("${knife4j.version:3.0}")
  private String version;
  @Value("${knife4j.enable:false}")
  private Boolean enabled;
  @Value("${knife4j.swagger-package:}")
  private String swaggerPackage;
  @Value("${knife4j.contact.name:crm}")
  private String contactName;
  @Value("${knife4j.contact.url:www.xxx.com}")
  private String contactUrl;
  @Value("${knife4j.contact.email:<EMAIL>}")
  private String contactEmail;


  @Bean
  public Docket createRestApi() {
    return new Docket(DocumentationType.SWAGGER_2)
            .enable(enabled)
            .apiInfo(apiInfo())
            .pathMapping("/")
            .select()
            .apis(RequestHandlerSelectors.basePackage(swaggerPackage))
            .paths(PathSelectors.any())
            .build();
  }

  private ApiInfo apiInfo() {
    return new ApiInfoBuilder()
            .title(title)
            .description(description)
            .contact(new Contact(contactName, contactUrl, contactEmail))
            .version(version)
            .build();
  }

  @Bean
  public WebMvcEndpointHandlerMapping webEndpointServletHandlerMapping(
          WebEndpointsSupplier webEndpointsSupplier, ServletEndpointsSupplier servletEndpointsSupplier,
          ControllerEndpointsSupplier controllerEndpointsSupplier, EndpointMediaTypes endpointMediaTypes,
          CorsEndpointProperties corsProperties, WebEndpointProperties webEndpointProperties, Environment environment) {
    List<ExposableEndpoint<?>> allEndpoints = new ArrayList<>();
    Collection<ExposableWebEndpoint> webEndpoints = webEndpointsSupplier.getEndpoints();
    allEndpoints.addAll(webEndpoints);
    allEndpoints.addAll(servletEndpointsSupplier.getEndpoints());
    allEndpoints.addAll(controllerEndpointsSupplier.getEndpoints());
    String basePath = webEndpointProperties.getBasePath();
    EndpointMapping endpointMapping = new EndpointMapping(basePath);
    boolean shouldRegisterLinksMapping = webEndpointProperties.getDiscovery().isEnabled() &&
            (org.springframework.util.StringUtils.hasText(basePath) || ManagementPortType.get(environment).equals(ManagementPortType.DIFFERENT));
    return new WebMvcEndpointHandlerMapping(endpointMapping, webEndpoints, endpointMediaTypes, corsProperties.toCorsConfiguration(), new EndpointLinksResolver(allEndpoints, basePath), shouldRegisterLinksMapping, null);
  }

}
