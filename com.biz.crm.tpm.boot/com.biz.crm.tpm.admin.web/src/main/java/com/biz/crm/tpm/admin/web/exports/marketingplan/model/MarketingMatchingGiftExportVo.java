package com.biz.crm.tpm.admin.web.exports.marketingplan.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description  TODO 别乱动顺序！！！
 * <AUTHOR>
 * @Date 2024/8/13 22:18
 */
@Data
@ApiModel("周边物料")
public class MarketingMatchingGiftExportVo implements Serializable {

    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("关联统筹方案名称")
    private String releaseName;

    @ApiModelProperty("客户编码")
    private String erpCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("公司代码")
    private String companyCode;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("核算品项范围-前端使用")
    private String itemCodeList;

    private String itemNameList;

    @ApiModelProperty("核算产品小类范围-前端使用")
    private String levelCodeList;

    @ApiModelProperty("核算产品小类范围-前端使用")
    private String levelNameList;

    @ApiModelProperty("核算产品范围-前端使用")
    private String productCodeList;

    private String productNameList;

    @ApiModelProperty("条件数量")
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    private String giveNum;

    @ApiModelProperty("优惠金额-上限")
    private BigDecimal discountAmount;

    @ApiModelProperty("促销描述")
    private String actDesc;

    @ApiModelProperty("费用依据集合")
    private String costBasisNameList;

    @ApiModelProperty("是否合同费用")
    private String isContractCost;

    @ApiModelProperty("合同编码")
    private String contractCode;

    private String errMsg;

}
