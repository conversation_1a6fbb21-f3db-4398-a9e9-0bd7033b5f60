package com.biz.crm.tpm.admin.web.exports.budgetsubjects.mapper;

import com.biz.crm.tpm.admin.web.exports.budgetsubjects.model.BudgetSubjectsExportsDto;
import com.biz.crm.tpm.admin.web.exports.budgetsubjects.model.BudgetSubjectsExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @describe: 预算科目信息导出
 * @createTime 2022年06月07日 15:00:00
 */
public interface BudgetSubjectsExportsMapper {


  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") BudgetSubjectsExportsDto dto);

  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<BudgetSubjectsExportsVo> findData(@Param("dto") BudgetSubjectsExportsDto dto);

}
