package com.biz.crm.tpm.admin.web.exports.ordinaryactivity.model;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年07月01日 15:17:00
 */
@ApiModel(value = "TpmOrdinaryActivity",description = "普通活动")
@Getter
@Setter
public class OrdinaryActivityExportsDto extends TenantDto implements Serializable,Cloneable {
  @ApiModelProperty("活动编码")
  private String code;

  @ApiModelProperty("活动名称")
  private String name;

  @ApiModelProperty("活动开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  @ApiModelProperty("活动结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty("费用预算编码")
  private String costBudgetCode;

  @ApiModelProperty("预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("活动大类编码")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  private String costTypeCategoryName;

  @ApiModelProperty("总申请金额")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("租户编码")
  private String tenantCode;

  @ApiModelProperty("最终可用余额")
  private BigDecimal finalBalance;

  @ApiModelProperty("流程状态")
  private String processStatus;

  @ApiModelProperty("审批单号")
  private String processNumber;

  @ApiModelProperty("活动状态")
  private String status;

  @ApiModelProperty(name = "offset",notes = "偏移量", value = "偏移量")
  private Integer offset;

  @ApiModelProperty(name = "limit",notes = "limit", value = "limit")
  private Integer limit;
}
