package com.biz.crm.tpm.admin.web.imports.invoice.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@CrmExcelImport(startRow = 1)
public class InvoiceImportsVo extends CrmExcelVo {

  @ApiModelProperty("发票类型")
  @CrmExcelColumn("*发票类型")
  private String type;

  @ApiModelProperty("开票日期")
  @CrmExcelColumn("*开票日期")
  private String billingDate;

  @ApiModelProperty("校验码")
  @CrmExcelColumn("校验码")
  private String checkCode;

  @ApiModelProperty("发票代码")
  @CrmExcelColumn("发票代码")
  private String code;

  @ApiModelProperty("发票号码")
  @CrmExcelColumn("*发票代码")
  private String invoiceNo;

  @ApiModelProperty("购买方名称")
  @CrmExcelColumn("购买方名称")
  private String purchaser;

  @ApiModelProperty("购买方税号")
  @CrmExcelColumn("购买方税号")
  private String pNo;

  @ApiModelProperty("购买方开户行及账号")
  @CrmExcelColumn("购买方开户行及账号")
  private String pBankAndAccount;

  @ApiModelProperty("购买方地址电话")
  @CrmExcelColumn("购买方地址电话")
  private String pAddressAndPhone;

  @ApiModelProperty("销售方名称")
  @CrmExcelColumn("销售方名称")
  private String seller;

  @ApiModelProperty("销售方税号")
  @CrmExcelColumn("销售方税号")
  private String sNo;

  @ApiModelProperty("销售方开户行及账号")
  @CrmExcelColumn("销售方开户行及账号")
  private String sBankAndAccount;

  @ApiModelProperty("销售方地址电话")
  @CrmExcelColumn("销售方地址电话")
  private String sAddressAndPhone;

  @ApiModelProperty("货物或应税劳务名称")
  @CrmExcelColumn("货物或应税劳务名称")
  private String name;

  @ApiModelProperty("税价合计(票价)")
  @CrmExcelColumn("*税价合计(票价)")
  private BigDecimal priceAndTax;

  @ApiModelProperty("税额")
  @CrmExcelColumn("*税额")
  private BigDecimal taxAmount;

  @ApiModelProperty("不含税金额")
  @CrmExcelColumn("不含税金额")
  private BigDecimal amountWithoutTax;

  @ExcelIgnore
  @ApiModelProperty("行数")
  private Integer count;

}
