package com.biz.crm.tpm.admin.web.exports.account.model;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;

/**
 * Vo：费用上账主表;
 * <AUTHOR> Keller
 * @date : 2022-7-2
 */
@ApiModel(value = "Account",description = "费用上账主表")
@Getter
@Setter
@CrmExcelExport
public class AccountExportsVo extends CrmExcelVo{

  /** 备注 */
  @CrmExcelColumn("备注")
  @ApiModelProperty(name = "remark",notes = "备注", value= "备注")
  private String remark;
  /** 费用上账编码 */
  @CrmExcelColumn("费用上账编码")
  @ApiModelProperty(name = "accountCode",notes = "费用上账编码", value= "费用上账编码")
  private String accountCode;
  /** 上账状态 */
  @CrmExcelColumn("上账状态")
  @ApiModelProperty(name = "accountStatus",notes = "上账状态", value= "上账状态")
  private String accountStatus;
  /** 活动编号 */
  @CrmExcelColumn("活动编号")
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value= "活动编号")
  private String activitiesCode;
  /** 活动明细编码 */
  @CrmExcelColumn("活动明细编码")
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value= "活动明细编码")
  private String activitiesDetailCode;
  /** 活动名称 */
  @CrmExcelColumn("活动名称")
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value= "活动名称")
  private String activitiesName;
  /** 上账金额 */
  @CrmExcelColumn("上账金额")
  @ApiModelProperty(name = "amount",notes = "上账金额", value= "上账金额")
  private BigDecimal amount;
  /** 核销编号 */
  @CrmExcelColumn("核销编号")
  @ApiModelProperty(name = "auditCode",notes = "核销编号", value= "核销编号")
  private String auditCode;
  /** 核销明细编号 */
  @CrmExcelColumn("核销明细编号")
  @ApiModelProperty(name = "auditDetailCode",notes = "核销明细编号", value= "核销明细编号")
  private String auditDetailCode;
  /** 可上账金额 */
  @CrmExcelColumn("可上账金额")
  @ApiModelProperty(name = "availableAmount",notes = "可上账金额", value= "可上账金额")
  private BigDecimal availableAmount;
  /** 开始时间 */
  @CrmExcelColumn("开始时间")
  @ApiModelProperty(name = "beginTime",notes = "开始时间", value= "开始时间")
  private String beginTime;
  /** 结束时间 */
  @CrmExcelColumn("结束时间")
  @ApiModelProperty(name = "endTime",notes = "结束时间", value= "结束时间")
  private String endTime;
  /** 支付方式 */
  @CrmExcelColumn("支付方式")
  @ApiModelProperty(name = "payBy",notes = "支付方式", value= "支付方式")
  private String payBy;
  /** 上账时间 */
  @CrmExcelColumn("上账时间")
  @ApiModelProperty(name = "accountTime",notes = "上账时间", value= "上账时间")
  private String accountTime;
  /** 核销金额 */
  @CrmExcelColumn("核销金额")
  @ApiModelProperty(name = "auditAmount",notes = "核销金额", value= "核销金额")
  private BigDecimal auditAmount;

}