package com.biz.crm.tpm.admin.web.exports.approvalCollect.model;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月08日 15:51:00
 */
@ApiModel(value = "ApprovalCollect", description = "核销采集信息")
@Getter
@Setter
public class ApprovalCollectExportsDto extends TenantDto implements Serializable, Cloneable {
  /**
   * 数据业务状态（启用状态）
   */
  @ApiModelProperty(name = "enableStatus", notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
  private String enableStatus;
  /**
   * 编码
   */
  @ApiModelProperty(name = "code", notes = "编码", value = "编码")
  private String code;
  /**
   * 名称
   */
  @ApiModelProperty(name = "name", notes = "名称", value = "名称")
  private String name;
  /**
   * 核销采集类型
   */
  @ApiModelProperty(name = "type", notes = "核销采集类型", value = "核销采集类型")
  private String type;

  /**
   * 租户
   */
  @ApiModelProperty(name = "tenantCode", notes = "租户", value = "租户")
  private String tenantCode;

  /**
   * 偏移量
   */
  @ApiModelProperty(name = "offset", notes = "偏移量", value = "偏移量")
  private Integer offset;

  /**
   * limit
   */
  @ApiModelProperty(name = "limit", notes = "limit", value = "limit")
  private Integer limit;
}
