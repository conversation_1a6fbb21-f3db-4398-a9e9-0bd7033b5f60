package com.biz.crm.tpm.admin.web.exports.withholdingcollect;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/26 18:09
 **/
@Data
public class WithholdingCollectMarketingEstimationExportVo {

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("预计金额(万元)")
    private BigDecimal estimateAmount;

    @ApiModelProperty("预计占比%")
    private String estimateRatioStr;

    @ApiModelProperty("预算金额(万元)")
    private BigDecimal budgetAmount;

    @ApiModelProperty("预算占比%")
    private String budgetRatioStr;

    @ApiModelProperty("差异额")
    private BigDecimal differenceAmount;

    @ApiModelProperty("差异率%")
    private String differenceRatioStr;

    @ApiModelProperty("规划收入(万元)")
    private BigDecimal planAmount;

    @ApiModelProperty("费率%")
    private String planRatioStr;


}
