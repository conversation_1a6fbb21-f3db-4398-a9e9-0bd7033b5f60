package com.biz.crm.tpm.admin.web.exports.prepay.model;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 参数传递dto：活动预付;
 * <AUTHOR> Keller
 * @date : 2022-7-2
 */
@ApiModel(value = "Prepay",description = "活动预付")
@Getter
@Setter
public class PrepayExportsDto extends TenantDto implements Serializable,Cloneable{
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 审批状态 */
  @ApiModelProperty(name = "processStatus",notes = "审批状态", value = "审批状态")
  private String processStatus;
  /** 审批流程编码 */
  @ApiModelProperty(name = "processKey",notes = "审批流程编码", value = "审批流程编码")
  private String processKey;
  /** 预付编号 */
  @ApiModelProperty(name = "prepayCode",notes = "预付编号", value = "预付编号")
  private String prepayCode;
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value = "活动编号")
  private String activitiesCode;
  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value = "活动名称")
  private String activitiesName;

  /** 偏移量 */
  @ApiModelProperty(name = "offset",notes = "偏移量", value = "偏移量")
  private Integer offset;

  /** limit */
  @ApiModelProperty(name = "limit",notes = "limit", value = "limit") 
  private Integer limit;
}