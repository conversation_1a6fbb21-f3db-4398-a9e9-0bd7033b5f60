package com.biz.crm.tpm.admin.web.register.form;

import com.biz.crm.tpm.business.activities.local.strategy.form.audit.AuditFileFormEventStrategy;
import com.biz.crm.tpm.business.activities.local.strategy.form.audit.AuditInvoiceFormEventStrategy;
import com.biz.crm.tpm.business.activities.local.strategy.form.audit.ExpirationAuditFormEventStrategy;
import com.biz.crm.tpm.business.activities.local.strategy.form.audit.MultiAuditFormEventStrategy;
import com.biz.crm.tpm.business.activities.sdk.register.AuditCenterModuleRegister;
import com.biz.crm.tpm.business.budget.sdk.register.FormDimension;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormEventStrategy;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 描述：</br>活动核销表单维度
 *
 * <AUTHOR>
 * @date 2022/11/2
 */
@Component
public class EventApprovedFormDimension implements FormDimension {

  @Autowired
  private AuditCenterModuleRegister auditCenterModuleRegister;

  @Override
  public String getCode() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String getName() {
    return "活动核销表单";
  }

  @Override
  public String getModelCode() {
    return auditCenterModuleRegister.moduleCode();
  }

  @Override
  public Collection<Class<? extends FormEventStrategy>> getFormEventStrategies() {
    return Sets.newHashSet(MultiAuditFormEventStrategy.class, ExpirationAuditFormEventStrategy.class, AuditFileFormEventStrategy.class, AuditInvoiceFormEventStrategy.class);
  }

  @Override
  public int getOrder() {
    return 20;
  }
}
