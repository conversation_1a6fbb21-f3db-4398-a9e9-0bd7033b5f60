package com.biz.crm.tpm.admin.web.controller.overallplan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.service.internal.OverallPlanReportServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.vo.HeadOverallSummaryReportVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseReportVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/8 14:48
 */
@RestController
@RequestMapping("/v1/headOverallPlanReportController")
@Api(tags = "总部指引跟踪")
public class HeadOverallPlanReportController {

    @Autowired
    private OverallPlanReportServiceImpl overallPlanCaseService;

    @ApiOperation(value = "查询分页列表")
    @GetMapping("findList")
    public Result<Page<OverallPlanCaseReportVo>> findList(@PageableDefault(50) Pageable pageable, OverallPlanCaseVo vo) {
        return Result.ok(overallPlanCaseService.findListBySchemeType(pageable, vo, OverallPlanSchemeTypeEnum.HEAD.getCode()));
    }


    @ApiOperation(value = "查询承接方案明细")
    @GetMapping("findUnderTakeSchemeDetailList")
    public Result<Page<OverallPlanCaseVo>> findUnderTakeSchemeDetailList(@PageableDefault(50) Pageable pageable, OverallPlanCaseVo vo) {
        return Result.ok(overallPlanCaseService.findUnderTakeSchemeDetailList(pageable,vo));
    }

    @ApiOperation(value = "大区指引-通过方案明细编码查询承接明细")
    @GetMapping("findRegionOverallPlanCase")
    public Result<Page<OverallPlanCaseVo>> findRegionOverallPlanCase(@PageableDefault(50) Pageable pageable, String schemeDetailCode) {
        return Result.ok(overallPlanCaseService.findRegionOverallPlanCase(pageable, schemeDetailCode));
    }

    @ApiOperation(value = "营销方案规划-通过方案明细编码查询承接明细")
    @GetMapping("findMarketingPlanCase")
    public Result<Page<MarketingPlanCaseVo>> findMarketingPlanCase(@PageableDefault(50) Pageable pageable, String schemeDetailCode) {
        return Result.ok(overallPlanCaseService.findRegionMarketingPlanCase(pageable, schemeDetailCode));
    }


    @ApiOperation(value = "总部指引追踪总览")
    @GetMapping("findSummaryList")
    public Result<Page<HeadOverallSummaryReportVo>> findSummaryList(@PageableDefault(50) Pageable pageable, HeadOverallSummaryReportVo vo) {
        return Result.ok(overallPlanCaseService.findSummaryList(pageable, vo));
    }
}
