package com.biz.crm.tpm.admin.web.register.form;

import com.biz.crm.tpm.business.activities.local.strategy.form.occupy.PushSfaFormEventStrategy;
import com.biz.crm.tpm.business.activities.sdk.register.ExecutionCenterModuleRegister;
import com.biz.crm.tpm.business.budget.sdk.register.FormDimension;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormEventStrategy;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 描述：</br>活动执行表单维度
 *
 * <AUTHOR>
 * @date 2022/11/2
 */
@Component
public class EventExecutionFormDimension implements FormDimension {

  @Autowired
  private ExecutionCenterModuleRegister executionCenterModuleRegister;

  @Override
  public String getCode() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String getName() {
    return "活动执行表单";
  }

  @Override
  public String getModelCode() {
    return executionCenterModuleRegister.moduleCode();
  }

  @Override
  public Collection<Class<? extends FormEventStrategy>> getFormEventStrategies() {
    return Lists.newArrayList(PushSfaFormEventStrategy.class);
  }

  @Override
  public int getOrder() {
    return 10;
  }
}
