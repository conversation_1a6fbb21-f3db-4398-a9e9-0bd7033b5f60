package com.biz.crm.tpm.admin.web.exports.costtypedetails.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月08日 14:27:00
 */
@CrmExcelExport
@Data
public class CostTypeDetailExportsVo extends CrmExcelVo {
  /**
   * 活动细类编号
   */
  @ApiModelProperty(name = "detailCode", notes = "活动细类编号", value = "活动细类编号")
  @CrmExcelColumn("活动细类编号")
  private String detailCode;

  /**
   * 活动细类编号
   */
  @ApiModelProperty(name = "detailName", notes = "活动细类名称", value = "活动细类名称")
  @CrmExcelColumn("活动细类名称")
  private String detailName;

  /**
   * ERP会计科目编码
   */
  @ApiModelProperty(name = "accountingSubjectsCode", notes = "ERP会计科目编码", value = "ERP会计科目编码")
  @CrmExcelColumn("ERP会计科目编码")
  private String accountingSubjectsCode;

  /**
   * ERP会计科目名称
   */
  @ApiModelProperty(name = "accountingSubjectsName", notes = "ERP会计科目名称", value = "ERP会计科目名称")
  @CrmExcelColumn("ERP会计科目名称")
  private String accountingSubjectsName;


  /**
   * 数据业务状态（启用状态）
   */
  @ApiModelProperty(name = "enableStatus", notes = "据业务状态（启用状态）", value = "据业务状态（启用状态）")
  @CrmExcelColumn("据业务状态（启用状态）")
  private String enableStatus;
  /**
   * 备注
   */
  @ApiModelProperty(name = "remark", notes = "备注", value = "备注")
  @CrmExcelColumn("备注")
  private String remark;
}
