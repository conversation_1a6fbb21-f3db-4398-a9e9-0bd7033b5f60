package com.biz.crm.tpm.admin.web.exports.activitiedetail.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@CrmExcelExport
public class MarketingPlanCaseExportsVo  extends CrmExcelVo {
    // ====


    @CrmExcelColumn("活动名称")
    @ApiModelProperty("活动名称")
    private String actName;

    @CrmExcelColumn("方案规划明细编码")
    @ApiModelProperty("方案规划明细编码")
    private String schemeDetailCode;

    @CrmExcelColumn("完全兑付日期")
    @ApiModelProperty("完全兑付时间")
    private String wholeCashDate;

    @CrmExcelColumn("方案规划编码")
    @ApiModelProperty("方案规划编码")
    private String schemeCode;

    @CrmExcelColumn("方案规划名称")
    @ApiModelProperty("方案规划名称")
    private String schemeName;

    @CrmExcelColumn("方案类型")
    @ApiModelProperty("方案类型")
    private String schemeType;


    @CrmExcelColumn("关联总部方案编码")
    @ApiModelProperty("关联总部方案编码")
    private String headSchemeCode;

    @CrmExcelColumn("关联总部方案名称")
    @ApiModelProperty("关联总部方案名称")
    private String headSchemeName;

    @CrmExcelColumn("关联指引编码")
    @ApiModelProperty("关联指引编码")
    private String releaseCode;

    @CrmExcelColumn("关联指引名称")
    @ApiModelProperty("关联指引名称")
    private String releaseName;

    @CrmExcelColumn("活动开始时间")
    @ApiModelProperty("活动开始时间")
    private String startDate;

    @ApiModelProperty("活动结束时间")
    @CrmExcelColumn("活动结束时间")
    private String endDate;


    @CrmExcelColumn("公司代码")
    @ApiModelProperty("公司代码")
    private String companyCode;

    @CrmExcelColumn("客户编码")
    @ApiModelProperty("客户编码")
    private String customerCode;

    @CrmExcelColumn("客户名称")
    @ApiModelProperty("客户名称")
    private String customerName;


    @CrmExcelColumn("合作类型")
    @ApiModelProperty("合作类型")
    private String cooperateTypeStr;

    @CrmExcelColumn("门店编码")
    @ApiModelProperty("门店编码")
    private String terminalCode;

    @CrmExcelColumn("门店名称")
    @ApiModelProperty("门店名称")
    private String terminalName;

    @CrmExcelColumn("兑付方式")
    @ApiModelProperty("兑付方式")
    private String cashType;

    @CrmExcelColumn("申请人账号")
    @ApiModelProperty("申请人账号")
    private String createAccount;

    @CrmExcelColumn("申请人姓名")
    @ApiModelProperty("申请人姓名")
    private String createName;


    @CrmExcelColumn("预算编码")
    @ApiModelProperty("预算编码")
    private String budgetCode;


    @CrmExcelColumn("预算年月")
    @ApiModelProperty("预算年月")
    private String years;

    @CrmExcelColumn("预算科目")
    @ApiModelProperty("预算科目")
    private String budgetSubjectName;

    @CrmExcelColumn("费用项目名称")
    @ApiModelProperty("费用项目名称")
    private String detailName;


    @CrmExcelColumn("费用使用部门编码")
    @ApiModelProperty("费用使用部门编码")
    private String belongDepartmentCode;

    @CrmExcelColumn("费用使用部门")
    @ApiModelProperty("费用使用部门")
    private String belongDepartmentName;

    @CrmExcelColumn("一级部门")
    @ApiModelProperty("一级部门")
    private String regionName;



    @CrmExcelColumn("费用承担部门编码")
    @ApiModelProperty("费用承担部门编码")
    private String bearDepartmentCode;

    @CrmExcelColumn("费用承担部门")
    @ApiModelProperty("费用承担部门")
    private String bearDepartmentName;


    @CrmExcelColumn("成本中心编码")
    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @CrmExcelColumn("成本中心")
    @ApiModelProperty("成本中心")
    private String costCenterName;


    @CrmExcelColumn("品项")
    @ApiModelProperty("品项")
    private String itemName;

    @CrmExcelColumn("申请金额")
    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;


    @CrmExcelColumn("计提金额")
    @ApiModelProperty("计提金额")
    private BigDecimal withholdingAmount;

    @CrmExcelColumn("结案状态")
    @ApiModelProperty("结案状态")
    private String auditStatus;

    @CrmExcelColumn("结案金额")
    @ApiModelProperty("结案金额")
    private BigDecimal auditAmount;

    @CrmExcelColumn("兑付状态")
    @ApiModelProperty("兑付状态")
    private String cashStatus;


    @CrmExcelColumn("推送状态")
    @ApiModelProperty("推送状态")
    private String pushStatus;

    @CrmExcelColumn("预付可冲销金额")
    @ApiModelProperty("预付可冲销金额")
    private BigDecimal availableReversedAmount;

    @CrmExcelColumn("兑付金额")
    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;


    @CrmExcelColumn("实际支付金额")
    @ApiModelProperty("实际支付金额")
    private BigDecimal prepayAmount;


    @CrmExcelColumn("推送错误描述")
    @ApiModelProperty("推送错误描述")
    private String pushMsg;


    @CrmExcelColumn("多部门标记")
    @ApiModelProperty("多部门标记")
    private String muchDepartmentMark;


    @CrmExcelColumn("活动执行编码")
    @ApiModelProperty("活动执行编码")
    private String actExecuteCode;

    @CrmExcelColumn("dms单据号")
    @ApiModelProperty("dms单据号")
    private String dmsCode;

    @CrmExcelColumn("是否计提")
    @ApiModelProperty("是否计提")
    private String withHoldingStatus;



    @CrmExcelColumn("活动描述")
    @ApiModelProperty("活动描述")
    private String actDesc;

    @CrmExcelColumn("执行描述")
    @ApiModelProperty("执行描述")
    private String executeDesc;

    @CrmExcelColumn("方案规划明细类型")
    @ApiModelProperty("方案规划明细类型")
    private String caseType;

    // ====



    @ApiModelProperty("大区编码")
    private String regionCode;





    @ApiModelProperty("方案明细类型名称")
    private String caseTypeName;

    private List<String> excludeCaseTypeList;








    @ApiModelProperty("原方案明细编码")
    private String originalSchemeDetailCode;





    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCodeTwo;


    @ApiModelProperty("关联总部方案编码集合")
    private List<String> headSchemeCodes;



    @ApiModelProperty("关联总部方案明细编码")
    private String headSchemeDetailCode;

    @ApiModelProperty("排序")
    private Long sort;


    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("二级费用大类名称")
    private String costTypeCategorySecondName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;








    @ApiModelProperty("活动年月")
    private String actYears;


    @ApiModelProperty("费用归属部门成本中心")
    private String belongCostCenterCodes;





    @ApiModelProperty("成本中心公司代码")
    private String costCenterCompanyCode;



    @ApiModelProperty("客户ERP编码")
    private String erpCode;



    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;





    @ApiModelProperty("终端类型")
    private String terminalType;

    @ApiModelProperty("终端渠道")
    private String terminalChannel;

    @ApiModelProperty("终端所属系统")
    private String terminalSystem;







    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("不含税金额")
    private BigDecimal noTaxApplyAmount;

    @ApiModelProperty("搭赠物料成本金额")
    private BigDecimal policyMaterialCostPrice;

    @ApiModelProperty("搭赠数量")
    private BigDecimal giftQuantity;

    @ApiModelProperty("政策商品")
    private String policyProductCode;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("预估费率")
    private BigDecimal ratio;




    @ApiModelProperty("费用依据")
    private String costBasis;

    @ApiModelProperty("费用依据集合")
    private List<String> costBasisList;

    @ApiModelProperty("执行示例")
    private String executeExample;

    @ApiModelProperty("执行示例集合")
    private List<String> executeExampleList;

    @ApiModelProperty("结案示例")
    private String closeCaseExample;

    @ApiModelProperty("结案示例集合")
    private List<String> closeCaseExampleList;



    @ApiModelProperty("合作类型标签")
    private List<String> cooperateTypeList;

    @ApiModelProperty("客户标签")
    private String customerTagStr;

    @ApiModelProperty("客户标签")
    private List<String> customerTagList;

    @ApiModelProperty("终端标签")
    private String terminalTagStr;

    @ApiModelProperty("终端标签")
    private List<String> terminalTagList;

    @ApiModelProperty("校验")
    private Boolean checkFlag;

    @ApiModelProperty("错误描述")
    private String errMsg;

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("是否合同费用")
    private String isContractCost;

    @ApiModelProperty("返利类型")
    private String rebateType;

    @ApiModelProperty("返利计算日期")
    private Integer rebateCalDay;

    @ApiModelProperty("返利开始时间")
    private String rebateStartDate;

    @ApiModelProperty("返利结束时间")
    private String rebateEndDate;

    @ApiModelProperty("政策形式编码")
    private String conditionFormula;

    @ApiModelProperty("政策形式名称")
    private String conditionFormulaName;

    @ApiModelProperty("我方承担金额")
    private BigDecimal bearAmount;

    @ApiModelProperty("客户承担金额")
    private BigDecimal cusBearAmount;

    @ApiModelProperty("行销物料类型")
    private String sellMaterialType;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料数量")
    private BigDecimal materialNum;

    @ApiModelProperty("物料成本价格")
    private BigDecimal materialCostPrice;

    @ApiModelProperty("运输方式")
    private String transportType;

    @ApiModelProperty("投放平台")
    private String platform;

    @ApiModelProperty("投放城市")
    private String placingCity;

    @ApiModelProperty("投放城市名称")
    private String placingCityName;



    @ApiModelProperty("已结案金额（前端查询用）")
    private BigDecimal auditedAmount;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmountF;




    @ApiModelProperty("是否选中")
    private String checked;

    @ApiModelProperty("条件数量")
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    private String giveNum;

    @ApiModelProperty("优惠数量-上限")
    private BigDecimal discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    private BigDecimal discountAmount;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;



    @ApiModelProperty("年月小于")
    private String yearsLess;

    @ApiModelProperty("结束时间小于")
    private String endDateLess;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("审批状态")
    private String processStatus;

    @ApiModelProperty("活动状态")
    private String actStatus;

    @ApiModelProperty("品项编码")
    private String itemCode;



    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("活动时间所在年月-OA接口用")
    private String planYears;

    @ApiModelProperty("创建人职位编码")
    private String positionCode;

    @ApiModelProperty("创建人组织编码")
    private String orgCode;

    @ApiModelProperty("创建人组织名称")
    private String orgName;


    @ApiModelProperty("是否多部门")
    private String muchDepartmentFlag;

    @ApiModelProperty("活动可结案金额")
    private BigDecimal availableAuditAmount;

    @ApiModelProperty("商品/小类编码")
    private String levelProductCode;

    private List<String> productCodeList;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("产品和品项范围-后台使用")
    private List<MarketingPlanProductVo> productAndItemList;

    @ApiModelProperty("核算产品范围-前端使用")
    private List<MarketingPlanProductVo> productList;

    @ApiModelProperty("核算品项范围-前端使用")
    private List<MarketingPlanProductVo> itemList;

    @ApiModelProperty("核算产品小类范围-前端使用")
    private List<MarketingPlanProductVo> levelList;

    @ApiModelProperty("费用产品范围-前端使用")
    private List<MarketingPlanProductVo> feeProductList;

    @ApiModelProperty("费用品项范围-前端使用")
    private List<MarketingPlanProductVo> feeItemList;

    @ApiModelProperty("费用产品小类范围-前端使用")
    private List<MarketingPlanProductVo> feeLevelList;

    @ApiModelProperty("费用归属品项-前端使用")
    private List<MarketingPlanProductVo> feeBelongItemList;

    @ApiModelProperty("是否变更查询方案明细")
    private String changeSchemeQueryFlag;

    @ApiModelProperty("原始方案编码")
    private String originalSchemeCode;

    @ApiModelProperty("排除完全结案的数据")
    private String excludeWholeAudit;

    @ApiModelProperty("是否计提查询")
    private String withholding;

    @ApiModelProperty("唯一关键字段")
    private String onlyKeys;

    @ApiModelProperty("活动材料")
    private List<CostTypeDetailCollectVo> collectList;

    @ApiModelProperty("核销材料")
    private List<CostTypeDetailCollectVo> approvalList;

    @ApiModelProperty("兑付方式")
    private Set<String> payBys;

    @ApiModelProperty("承接对象查询")
    private OverallPlanCaseVo bearCase;

    @ApiModelProperty("是否变更")
    private String changeFlag;

    @ApiModelProperty("是否可选择")
    private Boolean selectedFlag;






    @ApiModelProperty("合作类型")
    private String cooperateType;

    @ApiModelProperty("合作类型")
    private String hzlx;

    @ApiModelProperty("陈列卡板数")
    private BigDecimal displayCardNum;

    @ApiModelProperty("上上月POS")
    private BigDecimal lastUpMonthPos;

    @ApiModelProperty("上月POS")
    private BigDecimal lastMonthPos;

    @ApiModelProperty("当月pos")
    private BigDecimal monthPos;

    @ApiModelProperty("人员类型")
    private String staffType;

    @ApiModelProperty("出勤天数")
    private BigDecimal attendanceDay;

    @ApiModelProperty("底薪")
    private BigDecimal basicSalary;

    @ApiModelProperty("返利品项名称")
    private String flItemNameStr;

    @ApiModelProperty("返利产品名称")
    private String flProductNameStr;

    @ApiModelProperty("是否已读")
    private String dmsReadFlag;

    @ApiModelProperty("年月集合")
    private Set<String> yearsSet;
    @ApiModelProperty("预算科目编码集合")
    private Set<String> subjectCodes;
    @ApiModelProperty("客户编码集合")
    private Set<String> customerCodes;
    @ApiModelProperty("兑付方式集合")
    private Set<String> cashTypeSet;

    @ApiModelProperty("费用使用部门编码")
    private List<String> belongDepartmentCodes;

    @ApiModelProperty("费用承担部门编码")
    private List<String> bearDepartmentCodes;

    @ApiModelProperty("组织编码")
    private List<String> orgCodeList;

    @ApiModelProperty("活动明细编码集合")
    private List<String> schemeDetailCodeList;

    @ApiModelProperty("渠道类型")
    private List<String> channelTypeList;

    @ApiModelProperty("客户渠道类型")
    private String channelType;

    @ApiModelProperty("客户渠道类型名称")
    private String channelTypeName;

    @ApiModelProperty("费率")
    private BigDecimal rate;

    @ApiModelProperty("关闭编码")
    private String closeCode;

    @ApiModelProperty("关闭审批状态")
    private String closeProcessStatus;




    @ApiModelProperty("客户搜索项（快照）")
    private String searchName;

    @ApiModelProperty("行上税率")
    private BigDecimal lineTaxRate;

    @ApiModelProperty("行上不含税金额")
    private BigDecimal lineNoTaxApplyAmount;

    @ApiModelProperty("活动细类编码集合")
    private List<String> detailCodeList;


    public void setProductAndItemList(List<MarketingPlanProductVo> list) {
        this.productAndItemList = list;
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, List<MarketingPlanProductVo>> map = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getType()))
                    .collect(Collectors.groupingBy(MarketingPlanProductVo::getType));
            for (Map.Entry<String, List<MarketingPlanProductVo>> entry : map.entrySet()) {
                String key = entry.getKey();
                List<MarketingPlanProductVo> values = entry.getValue();
                if (OverallPlanProductEnum.cal_product.getCode().equals(key)) {
                    this.productList = values;
                    MarketingPlanProductVo itemProduct = values.get(0);
                    this.productCode = itemProduct.getCode();
                    this.productName = itemProduct.getName();
                } else if (OverallPlanProductEnum.cal_item.getCode().equals(key)) {
                    this.itemList = values;
                    MarketingPlanProductVo itemProduct = values.get(0);
                    if (!MarketingPlanCaseTypeEnum.back.getCode().equals(this.caseType)) {
                        this.itemCode = itemProduct.getCode();
                        this.itemName = itemProduct.getName();
                    }
                } else if (OverallPlanProductEnum.cal_level.getCode().equals(key)) {
                    this.levelList = values;
                } else if (OverallPlanProductEnum.fee_product.getCode().equals(key)) {
                    this.feeProductList = values;
                } else if (OverallPlanProductEnum.fee_item.getCode().equals(key)) {
                    this.feeItemList = values;
                } else if (OverallPlanProductEnum.fee_level.getCode().equals(key)) {
                    this.feeLevelList = values;
                } else if (OverallPlanProductEnum.fee_belong_item.getCode().equals(key)) {
                    this.feeBelongItemList = values;
                    MarketingPlanProductVo itemProduct = values.get(0);
                    if (MarketingPlanCaseTypeEnum.back.getCode().equals(this.caseType)) {
                        this.itemCode = itemProduct.getCode();
                        this.itemName = itemProduct.getName();
                    }
                }
            }
        }
    }

    public List<MarketingPlanProductVo> getProductAndItemList() {
        List<MarketingPlanProductVo> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(this.productList)) {
            for (MarketingPlanProductVo vo : this.productList) {
                vo.setType(OverallPlanProductEnum.cal_product.getCode());
            }
            list.addAll(this.productList);
        }
        if (CollectionUtils.isNotEmpty(this.itemList)) {
            for (MarketingPlanProductVo vo : this.itemList) {
                vo.setType(OverallPlanProductEnum.cal_item.getCode());
            }
            list.addAll(this.itemList);
        }
        if (CollectionUtils.isNotEmpty(this.levelList)) {
            for (MarketingPlanProductVo vo : this.levelList) {
                vo.setType(OverallPlanProductEnum.cal_level.getCode());
            }
            list.addAll(this.levelList);
        }
        if (CollectionUtils.isNotEmpty(this.feeProductList)) {
            for (MarketingPlanProductVo vo : this.feeProductList) {
                vo.setType(OverallPlanProductEnum.fee_product.getCode());
            }
            list.addAll(feeProductList);
        }
        if (CollectionUtils.isNotEmpty(this.feeItemList)) {
            for (MarketingPlanProductVo vo : this.feeItemList) {
                vo.setType(OverallPlanProductEnum.fee_item.getCode());
            }
            list.addAll(this.feeItemList);
        }
        if (CollectionUtils.isNotEmpty(this.feeLevelList)) {
            for (MarketingPlanProductVo vo : this.feeLevelList) {
                vo.setType(OverallPlanProductEnum.fee_level.getCode());
            }
            list.addAll(this.feeLevelList);
        }
        if (CollectionUtils.isNotEmpty(this.feeBelongItemList)) {
            for (MarketingPlanProductVo vo : this.feeBelongItemList) {
                vo.setType(OverallPlanProductEnum.fee_belong_item.getCode());
            }
            list.addAll(this.feeBelongItemList);
        }

        return list;
    }
}
