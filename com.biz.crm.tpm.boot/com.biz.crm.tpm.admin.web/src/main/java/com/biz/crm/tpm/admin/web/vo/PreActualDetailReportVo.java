package com.biz.crm.tpm.admin.web.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 20:17
 **/
@Data
@ApiModel("预实明细")
public class PreActualDetailReportVo extends TenantFlagOpVo {

    @ApiModelProperty("项目编码")
    private String projectCode;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("预算年月")
    private String years;

    @ApiModelProperty("活动年月")
    private String actYears;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("中心编码")
    private String centerCode;

    @ApiModelProperty("中心名称")
    private String centerName;

    @ApiModelProperty("一级部门编码")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称")
    private String departmentOneName;

    @ApiModelProperty("二级部门编码")
    private String departmentTwoCode;

    @ApiModelProperty("二级部门名称")
    private String departmentTwoName;

    @ApiModelProperty("三级部门编码")
    private String departmentThreeCode;

    @ApiModelProperty("三级部门名称")
    private String departmentThreeName;

    @ApiModelProperty("预算金额")
    private BigDecimal budgetCost=BigDecimal.ZERO;

    @ApiModelProperty("预算费率")
    private BigDecimal budgetCostRatio=BigDecimal.ZERO;

    @ApiModelProperty("规划金额-最初版")
    private BigDecimal planAmount=BigDecimal.ZERO;

    @ApiModelProperty("规划金额-变更后")
    private BigDecimal lastPlanAmount=BigDecimal.ZERO;

    @ApiModelProperty("规划费率-最初版")
    private BigDecimal planCostRatio=BigDecimal.ZERO;

    @ApiModelProperty("规划费率-变更后")
    private BigDecimal lastPlanCostRatio=BigDecimal.ZERO;

    @ApiModelProperty("计提金额")
    private BigDecimal withholdingCost=BigDecimal.ZERO;

    @ApiModelProperty("计提费率")
    private BigDecimal withholdingCostRatio=BigDecimal.ZERO;

    @ApiModelProperty("费率偏差")
    private BigDecimal costDifferenceRatio=BigDecimal.ZERO;

    @ApiModelProperty("结案金额")
    private BigDecimal auditCost =BigDecimal.ZERO;

    @ApiModelProperty("结案费率")
    private BigDecimal auditCostRatio=BigDecimal.ZERO;

    @ApiModelProperty("冲销金额")
    private BigDecimal writeOffCost=BigDecimal.ZERO;

    @ApiModelProperty("冲销费率")
    private BigDecimal writeOffCostRatio=BigDecimal.ZERO;

    @ApiModelProperty("关键KEY")
    private String indexKey;
}
