package com.biz.crm.tpm.admin.web.exports.costtypedetails.model;

import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月08日 14:27:00
 */
@Data
public class CostTypeDetailExportsDto extends CrmExcelVo {
  /**
   * 租户编号
   */
  private String tenantCode;
  /**
   * 数据业务状态（启用状态）
   */
  private String enableStatus;
  /**
   * 数据业务状态（删除状态）
   */
  private String delFlag;
  /**
   * 活动细类名称
   */
  private String detailName;
  /**
   * 活动细类编号
   */
  private String detailCode;
  /**
   * 偏移量
   */
  private Integer offset;
  /**
   * limit
   */
  private Integer limit;
}
