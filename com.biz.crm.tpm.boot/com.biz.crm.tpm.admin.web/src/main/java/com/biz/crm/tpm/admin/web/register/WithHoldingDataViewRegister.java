package com.biz.crm.tpm.admin.web.register;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>费用预提数据视图注册器
 * 基于nebula的数据视图提供列表查询功能
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Component
@Slf4j
public class WithHoldingDataViewRegister implements DataviewRegister {

  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-费用预提";
  }

  @Override
  public String buildSql() {
    return "select t.* from tpm_with_holding t " +
            "where t.tenant_code = :tenantCode " +
            "and t.del_flag = '009' ";
//    return "SELECT	"+
//            "	wh.id,	"+
//            "	wh.create_account,	"+
//            "	wh.create_name,	"+
//            "	wh.create_time,	"+
//            "	wh.modify_account,	"+
//            "	wh.modify_name,	"+
//            "	wh.modify_time,	"+
//            "	wh.del_flag,	"+
//            "	wh.enable_status,	"+
//            "	wh.remark,	"+
//            "	wh.tenant_code,	"+
//            "	wh.activities_code,	"+
//            "	wh.activities_detail_code,	"+
//            "	wh.activities_name,	"+
//            "	wh.apply_amount,	"+
//            "	wh.budget_subjects_code,	"+
//            "	wh.budget_subjects_name,	"+
//            "	wh.cost_type_category_code,	"+
//            "	wh.cost_type_category_name,	"+
//            "	wh.cost_type_detail_code,	"+
//            "	wh.cost_type_detail_name,	"+
//            "	wh.customer_code,	"+
//            "	wh.customer_name,	"+
//            "	wh.org_code,	"+
//            "	wh.org_name,	"+
//            "	wh.pay_by,	"+
//            "	wh.terminal_code,	"+
//            "	wh.terminal_name,	"+
//            "	wh.with_holding_amount,	"+
//            "	wh.with_holding_code,	"+
//            "	wh.with_holding_type,	"+
//            "	wh.with_holding_years,	"+
//            "	wh.actual_amount,	"+
//            "	wh.channel_code,	"+
//            "	wh.company_code,	"+
//            "	wh.cost_center_code,	"+
//            "	wh.cost_center_name,	"+
//            "	wh.department_code,	"+
//            "	wh.department_name,	"+
//            "	wh.erp_code,	"+
//            "	wh.fail_msg,	"+
//            "	wh.formula_info,	"+
//            "	wh.item_code,	"+
//            "	wh.item_name,	"+
//            "	wh.order_code,	"+
//            "	wh.order_name,	"+
//            "	wh.position_code,	"+
//            "	wh.product_group_code,	"+
//            "	wh.reduce_amount,	"+
//            "	wh.STATUS,	"+
//            "	wh.voucher_code,	"+
//            "	wh.with_holding_report_amount,	"+
//            "	wh.with_holding_source,	"+
//            "	wh.year_month_ly,	"+
//            "	wh.years,	"+
//            "	wh.budget_code,	"+
//            "	wh.bear_department_code,	"+
//            "	wh.bear_department_name,	"+
//            "	wh.belong_department_code,	"+
//            "	wh.belong_department_name,	"+
//            "	wh.business_code,	"+
//            "	wh.external_code,	"+
//            "	wh.with_holding_report_reduce_amount,	"+
//            "	wh.actual_report_amount,	"+
//            "	wh.collect_code,	"+
//            "	wh.be_this_fee,	"+
//            "	wh.push_hec_code,	"+
//            "	wh.push_status,	"+
//            "	wh.be_adjust,	"+
//            "	wh.act_desc,	"+
//            "	wh.scheme_create_name,	"+
//            "	wh.confirm_status,	"+
//            "	wh.ratio,	"+
//            "	wh.ratio_str,	"+
//            "	wh.pass_date,	"+
//            "	a.scheme_detail_code,	"+
//            "	a.this_cash_amount cash_amount	"+
//            "FROM	"+
//            "	tpm_with_holding wh	"+
//            "	LEFT JOIN (	"+
//            "		SELECT	"+
//            "			a.scheme_detail_code,	"+
//            "			sum(this_cash_amount) this_cash_amount	"+
//            "		FROM (	"+
//            "			SELECT	"+
//            "				DATE_FORMAT(a.delivery_time, '%Y-%m') delivery_time,	"+
//            "				a.scheme_detail_code,	"+
//            "				b.year_month_ly,	"+
//            "				b.activities_detail_code,	"+
//            "				CASE WHEN DATE_FORMAT(a.delivery_time, '%Y-%m') <= b.year_month_ly THEN	"+
//            "					c.this_cash_amount	"+
//            "				ELSE	"+
//            "					0	"+
//            "				END this_cash_amount,	"+
//            "				c.cash_detail_code	"+
//            "			FROM	"+
//            "				tpm_delivery_replenishment_pool_detail a	"+
//            "				LEFT JOIN tpm_with_holding b ON a.scheme_detail_code = b.activities_detail_code	"+
//            "				LEFT JOIN tpm_fee_cash_detail c ON a.scheme_detail_code = c.scheme_detail_code	"+
//            "			WHERE	"+
//            "				1 = 1	"+
//            "				AND a.scheme_detail_code in('YXMX202411011835')	"+
//            "				AND a.operation_type = 'fee_cash') a	"+
//            "		GROUP BY	"+
//            "			a.scheme_detail_code) a ON wh.activities_detail_code = a.scheme_detail_code	"+
//            "WHERE	"+
//            "	wh.del_flag = '009'	"+
//            "	and wh.tenant_code = :tenantCode	";
  }
}
