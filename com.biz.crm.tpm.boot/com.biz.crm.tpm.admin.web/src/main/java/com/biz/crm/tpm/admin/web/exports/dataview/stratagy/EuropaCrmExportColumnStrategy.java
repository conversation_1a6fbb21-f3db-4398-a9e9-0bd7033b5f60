package com.biz.crm.tpm.admin.web.exports.dataview.stratagy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.strategy.CrmExportColumnStrategy;
import com.biz.crm.common.ie.sdk.excel.vo.ColumnVo;
import com.biz.crm.common.ie.sdk.excel.vo.FunctionPermissionVo;
import com.biz.crm.common.ie.sdk.strategy.ExportColumnStrategy;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.table.sdk.service.ColumnConfigPersonalVoService;
import com.biz.crm.mdm.business.table.sdk.service.ColumnConfigVoService;
import com.biz.crm.mdm.business.table.sdk.vo.ColumnConfigVo;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContext;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContextHolder;
import com.bizunited.nebula.mars.sdk.service.MarsAuthorityExcludedFieldDetailService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述：</br>
 * mdm中通过个性化设置以及页面引擎中的导出配置信息过滤导出的数据字段
 *
 * <AUTHOR>
 * @version 2023-01-03 修正数据字典查询bug {@link #fullDictCode(List, List)}
 * @date 2022/10/12
 */
@Component
@Slf4j
public class EuropaCrmExportColumnStrategy implements CrmExportColumnStrategy {
  @Autowired
  private ColumnConfigPersonalVoService columnConfigPersonalVoService;
  @Autowired
  private ColumnConfigVoService columnConfigVoService;
  @Autowired
  private DictDataVoService dictDataVoService;
  //  @Qualifier("marsAuthorityExcludedFieldDetailServiceRemote")
  @Autowired
  private MarsAuthorityExcludedFieldDetailService marsAuthorityExcludedFieldDetailService;

  @Autowired
  private List<ExportColumnStrategy> exportColumnStrategies;


  /**
   * 导出字段查询策略默认策略编码
   */
  private final String EXPORT_COLUMN_STRATEGY = "default";
  /**
   * 导出字段查询策略参数字段 K（从前端传递的paramsJson中取值）
   */
  private final String EXPORT_COLUMN_HEAD = "columnStrategyCode";

  @Override
  public Set<String> exportColumn(ExportTaskProcessVo vo) {
    String parentCode = vo.getParentCode();
    String functionCode = vo.getFunctionCode();
    String parametersJson = vo.getParametersJson();
    List<ColumnVo> showDataviewColumn = getShowDataviewColumn(parentCode, functionCode, parametersJson);
    Set<String> collect = showDataviewColumn.parallelStream().map(ColumnVo::getField).collect(Collectors.toSet());
    return collect;
  }

  @Override
  public List<ColumnVo> getShowDataviewColumn(FunctionPermissionVo funPermVo) {
    return this.getShowDataviewColumn(funPermVo.getParentCode(), funPermVo.getFunctionCode(), funPermVo.getParamsJson());
  }

  /**
   * 获取显示的数据视图字段
   *
   * @param parentCode
   * @param functionCode
   * @return 可以显示，并可以导出的字段
   */
  @Override
  public List<ColumnVo> getShowDataviewColumn(String parentCode, String functionCode, String paramsJson) {
    if (StringUtils.isAnyBlank(parentCode, functionCode)) {
      return Collections.emptyList();
    }
    // 页面引擎配置
    List<ColumnConfigVo> columnConfigVos =
        this.columnConfigVoService.findByParentCodeAndFunctionCodeOrderByFormorder(parentCode, functionCode);
    if (columnConfigVos == null) {
      return Collections.emptyList();
    }
    // 查出所有可导出的字段
    Map<String, ColumnVo> columnConfigVosMap = columnConfigVos.parallelStream()
        // 过滤显示并且可以导出的字段
        .filter(item -> (
//            item.getVisible() && // 只保留可见
            "1".equals(item.getColumnExport()))) // 只保留允许导出的
        .map(row -> {
          ColumnVo vo = new ColumnVo();
          String dictTypeCode = row.getDictCode();
          vo.setDictTypeCode(dictTypeCode);
          vo.setField(row.getField());
          vo.setTitle(row.getTitle());
//          vo.setColumnExport("1".equals(row.getColumnExport()));
          return vo;
        })
        .collect(Collectors.toMap(ColumnVo::getField, row -> {
          return row;
        }));
    // 查找数据权限的字段
    MarsAuthorityContext marsAuthorityContext = MarsAuthorityContextHolder.getContext();
    String listCode = marsAuthorityContext.getListCode();
    Set<String> fileds = this.marsAuthorityExcludedFieldDetailService.findByListCode(listCode);
    //自定义策略找字段集合
    List<ColumnVo> strategy = this.findStrategy(fileds, parentCode, functionCode, paramsJson);
    //与允许导出字段进行比对
    List<ColumnVo> innerColumnCfg = strategy.parallelStream()
        .filter(row -> {
          String field = row.getField();
          return columnConfigVosMap.containsKey(field);
        })
        .map(row -> {
          String field = row.getField();
          return columnConfigVosMap.get(field);
        })
        .collect(Collectors.toList());
    return innerColumnCfg;
  }


  /**
   * 查找策略并产生结果
   *
   * @param fileds
   * @param parentCode
   * @param functionCode
   * @param paramsJson
   */
  private List<ColumnVo> findStrategy(Set<String> fileds, String parentCode, String functionCode, String paramsJson) {
    Validate.notEmpty(exportColumnStrategies, "导出字段获取策略不存在实现类,请检查接口com.biz.crm.common.ie.sdk.strategy.ExportColumnStrategy");
    //获取参数信息
    Map<String, Object> paramsMap = this.findParamsMap(paramsJson);
    String resultExportColumnStrategyCode = EXPORT_COLUMN_STRATEGY;
    Object exportColumnStrategyCode = paramsMap.get(EXPORT_COLUMN_HEAD);
    List<ColumnVo> result = new ArrayList<>();
    if (ObjectUtils.isNotEmpty(exportColumnStrategyCode)) {
      //不为空取前端传递的策略
      resultExportColumnStrategyCode = (String) exportColumnStrategyCode;
    }
    //构造策略接口必须参数
    JSONObject params = new JSONObject();
    params.put("parentCode", parentCode);
    params.put("functionCode", functionCode);
    for (ExportColumnStrategy exportColumnStrategy : exportColumnStrategies) {
      if (exportColumnStrategy.getCode().equals(resultExportColumnStrategyCode)) {
        //执行策略 只能执行一次！
        List<ColumnVo> column = exportColumnStrategy.getColumn(fileds, params);
        result.addAll(column);
        log.info("======'{}'数据视图导出字段策略执行成功=======",exportColumnStrategy.getName());
        return result;
      }
    }
    return result;
  }

  @Override
  public List<JSONObject> fullDictCode(List<JSONObject> excelDataList,
                                       List<ColumnVo> showDataviewColumn) {
    // 数据字典Map对象
    Map<String, Map<String, String>> allDictCodeMap = getColumnDictCodeMap(showDataviewColumn);
    // 遍历查询的数据，跟进要显示的字段进行组装数据
    List<JSONObject> result = Lists.newLinkedList();
    for (JSONObject rowData : excelDataList) {
      // {"del_flag":"009","modify_name":"超级管理员","create_time":*************,"position_code":"**********","level_num":1,"user_name":"***********","modify_account":"admin","modify_time":*************,"position_name":"职位-纳美大区销售总监","role_code":"JS01","create_account":"admin","enable_status":"009","full_name":"徐清01-纳美大区销售总监","rule_code":"000169","position_level_name":"纳美大区销售总监","parent_code":"","primary_flag":"1","position_level_code":"ZWJB001","id":"86fc0187795b50fc7fd3f0bc39e3fbbe","org_name":"市场部","org_code":"ZZ00101","create_name":"超级管理员","tenant_code":"default"}
      JSONObject jsonObj = new JSONObject();
      jsonObj.putAll(rowData);
      // 获取当前字段名称
      for (String dictTypeCode : allDictCodeMap.keySet()) {
        // 获取当前字段对应的字典值
        Object dictType = jsonObj.get(dictTypeCode);
        String dictTypeStr = "" + dictType;
        Map<String, String> map = allDictCodeMap.get(dictTypeCode);
        String dictValue = map.get(dictTypeStr);
        if (dictValue == null) {
          // 兼容前台对字典表的boolean的兼容模式
          Boolean dictValueForDb = BooleanUtil.toBooleanObject(dictTypeStr);
          if (dictValueForDb != null) {
            // 可能存在字典表写各种各样true和false的形式
            for (String key : map.keySet()) {
              Boolean dictValueForDic = BooleanUtil.toBooleanObject(key);
              if (dictValueForDb.equals(dictValueForDic)) {
                dictValue = map.get(key);
                map.put(dictTypeStr, dictValue);
                map.remove(key);
                break;
              }
            }
          }
        }
        if (dictValue != null) {
          // 替换字典
          jsonObj.set(dictTypeCode, dictValue);
        }
      }
      result.add(jsonObj);
    }
    return result;
  }

  private Map<String, Map<String, String>> getColumnDictCodeMap(List<ColumnVo> showDataviewColumn) {
    Map<String, Map<String, String>> allDictCodeMap = new HashMap<>();
    try {
      // 根据请求的数据，获取本次调用所需数据字典
      Map<String, String> dictType2FiledCodeMap = new HashMap<>(showDataviewColumn.size());
      for (ColumnVo columnVo : showDataviewColumn) {
        String dictTypeCode = columnVo.getDictTypeCode();
        if (StringUtils.isBlank(dictTypeCode)) {
          continue;
        }
        dictType2FiledCodeMap.put(dictTypeCode, columnVo.getField());
      }
      if (CollectionUtil.isEmpty(dictType2FiledCodeMap)) {
        return allDictCodeMap;
      }
      List<String> needDictTypeCodeList = new ArrayList<>(dictType2FiledCodeMap.keySet());
      Map<String, List<DictDataVo>> dictTypeCodeList = this.dictDataVoService.findByDictTypeCodeList(needDictTypeCodeList);
      for (Entry<String, List<DictDataVo>> entry : dictTypeCodeList.entrySet()) {
        String key = entry.getKey();
        Map<String, String> dictMap = entry.getValue().parallelStream()
                .filter(row -> EnableStatusEnum.ENABLE.getCode().equals(row.getEnableStatus()))
                .filter(row -> StringUtils.isNotBlank(row.getDictCode()))
                .collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
        String fieldName = dictType2FiledCodeMap.get(key);
        allDictCodeMap.put(fieldName, dictMap);
      }
    } catch (Exception e) {
        log.error(e.getMessage(), e);
    }
    return allDictCodeMap;
  }

  /**
   * 获取任务参数信息
   *
   * @param paramsJson
   * @return
   */
  private Map<String, Object> findParamsMap(String paramsJson) {
    if (StringUtils.isBlank(paramsJson)) {
      return Maps.newHashMap();
    }
    Map<String, Object> map = Maps.newHashMap();
    JSONObject jsonObject = JSONUtil.parseObj(paramsJson);
    Set<String> set = jsonObject.keySet();
    for (String item : set) {
      map.put(item, jsonObject.get(item));
    }
    return map;
  }
}
