package com.biz.crm.tpm.admin.web.preactualreport.service.strategy;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.dms.business.order.common.sdk.enums.ItemTypeEnum;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialCostVoService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.admin.web.preactualreport.eunm.PreActualWithholdingStatusEnum;
import com.biz.crm.tpm.admin.web.vo.PreActualReportCollectVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanMaterialTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingTransportEnum;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.service.PosDataService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.FormulaGetValueComponent;
import com.bizunited.nebula.common.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.PageRequest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/20 20:58
 **/
public class PreActualReportCollectAbstractBuilder extends PreActualReportCollectBuilder<Map<String, List<MarketingPlanCaseVo>>, Map<String, MarketingPlanVo>> {


    private OrgVoService orgVoService;

    private WithHoldingService withHoldingService;

    private List<PreActualReportCollectVo> reportCollectVoList;

    private MarketingSalesPlanService marketingSalesPlanService;

    private DictDataVoService dictDataVoService;

    private PosDataService posDataService;

    private MaterialVoService materialVoService;

    private MdmCostCenterVoService costCenterVoService;

    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;

    private PublicShareRatioService publicShareRatioService;

    private MaterialCostVoService materialCostVoService;

    private FeeCashService feeCashService;

    private Map<String, List<OrgVo>> orgVoListMap = Maps.newHashMap();

    private Map<String, List<OrgVo>> orgParentOrgMap = Maps.newHashMap();

    private Map<String, List<MarketingSalesPlanVo>> salesPlanMap = Maps.newHashMap();

    private Map<String, Map<String, BigDecimal>> actYearsPostDataMap = Maps.newHashMap();

    private Map<String, List<DmsWarehouseOrderDetailVo>> actYersOrderDetailsMap = Maps.newHashMap();

    private final static String column = ":";

    private final static List<String> productItemTypeList = Arrays.asList(ItemTypeEnum.NORMAL_GOODS.getDictCode(),
            ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
            ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
            ItemTypeEnum.COMPENSATED_GOODS.getDictCode(),
            ItemTypeEnum.TAIL_GOODS.getDictCode());

    private final static List<String> peripheryItemTypeList = Arrays.asList(ItemTypeEnum.MATERIAL_GOODS.getDictCode());

    private final static List<String> giftItemTypeList = Arrays.asList(ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
            ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
            ItemTypeEnum.TAIL_GOODS.getDictCode());


    public static PreActualReportCollectAbstractBuilder builder(ApplicationContext context, List<PreActualReportCollectVo> reportCollectList) {
        PreActualReportCollectAbstractBuilder builder = new PreActualReportCollectAbstractBuilder();
        builder.orgVoService = context.getBean(OrgVoService.class);
        builder.withHoldingService = context.getBean(WithHoldingService.class);
        builder.reportCollectVoList = reportCollectList;
        builder.marketingSalesPlanService = context.getBean(MarketingSalesPlanService.class);
        builder.dictDataVoService = context.getBean(DictDataVoService.class);
        builder.posDataService = context.getBean(PosDataService.class);
        builder.dmsWarehouseOrderDetailVoService = context.getBean(DmsWarehouseOrderDetailVoService.class);
        builder.materialVoService = context.getBean(MaterialVoService.class);
        builder.costCenterVoService = context.getBean(MdmCostCenterVoService.class);
        builder.publicShareRatioService = context.getBean(PublicShareRatioService.class);
        builder.materialCostVoService = context.getBean(MaterialCostVoService.class);
        builder.feeCashService = context.getBean(FeeCashService.class);
        return builder;
    }

    /**
     * 构建基础数据
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder buildBaseData() {
        List<String> orgCodes = Lists.newArrayList(regionCaseMap.keySet());
        Map<String, List<OrgVo>> orgParentMap = orgVoService.findAllParentByOrgCodesMap(orgCodes);
        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : regionCaseMap.entrySet()) {
            Map<String, OrgVo> orgVoMap = orgParentMap.get(entry.getKey()).stream().collect(Collectors.toMap(x -> x.getOrgCode(), Function.identity()));
            Optional<OrgVo> centerOptional = orgParentMap.get(entry.getKey()).stream().filter(x -> OrgTypeEnum.COMPANY.getDictCode().equals(x.getOrgType())).findFirst();
            Optional<OrgVo> divisionOptional = orgParentMap.get(entry.getKey()).stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType())).findFirst();
            Map<String, List<MarketingPlanCaseVo>> groupCaseListMap = entry.getValue().stream().collect(Collectors.groupingBy(x -> x.getSchemeCode() + x.getActYears() + x.getCustomerCode()));
            for (Map.Entry<String, List<MarketingPlanCaseVo>> listEntry : groupCaseListMap.entrySet()) {
                MarketingPlanCaseVo caseVo = listEntry.getValue().get(0);
                MarketingPlanVo planVo = planMap.get(caseVo.getSchemeCode());
                PreActualReportCollectVo reportCollectVo = JsonUtils.convert(caseVo, PreActualReportCollectVo.class);
                reportCollectVo.setApplyAccount(planVo.getCreateAccount());
                reportCollectVo.setApplyName(planVo.getCreateName());
                reportCollectVo.setProcessStatus(planVo.getProcessStatus());
                reportCollectVo.setOrgCode(entry.getKey());
                reportCollectVo.setOrgName(orgVoMap.get(entry.getKey()).getOrgName());
                if (centerOptional.isPresent()) {
                    OrgVo centerOrg = centerOptional.get();
                    reportCollectVo.setCenterCode(centerOrg.getOrgCode());
                    reportCollectVo.setCenterName(centerOrg.getOrgName());
                }
                if (divisionOptional.isPresent()) {
                    OrgVo divisionOrg = divisionOptional.get();
                    reportCollectVo.setRegionCode(divisionOrg.getOrgCode());
                    reportCollectVo.setRegionName(divisionOrg.getOrgName());
                }
                reportCollectVoList.add(reportCollectVo);
            }
        }
        return this;
    }

    /**
     * 构建计提状态
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder buildWithholdingStatus() {
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            List<MarketingPlanCaseVo> caseVoList = regionCaseMap.get(vo.getOrgCode())
                    .stream().filter(x -> x.getActYears().equals(vo.getActYears()) && x.getCustomerCode().equals(vo.getCustomerCode())
                            && vo.getSchemeCode().equals(x.getSchemeCode()))
                    .collect(Collectors.toList());
            List<String> schemeDetailCodes = caseVoList.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<WithHoldingVo> withHoldingVoList = withHoldingService.findBySchemeDetailCodesPass(schemeDetailCodes);
            List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesPass(schemeDetailCodes);
            if (CollectionUtils.isEmpty(withHoldingVoList)) {
                vo.setWithholdingStatus(PreActualWithholdingStatusEnum.not_withhold.getCode());
            } else if (withHoldingVoList.size() < schemeDetailCodes.size()) {
                vo.setWithholdingStatus(PreActualWithholdingStatusEnum.part_withhold.getCode());
            } else if (withHoldingVoList.size() == schemeDetailCodes.size()) {
                vo.setWithholdingStatus(PreActualWithholdingStatusEnum.withhold.getCode());
            }
            if (CollectionUtils.isNotEmpty(withHoldingVoList)) {
                vo.setWithHoldingVoList(withHoldingVoList);
            }
            if (CollectionUtils.isNotEmpty(feeCashDetailVos)) {
                vo.setFeeCashDetailVoList(feeCashDetailVos);
            }
        }
        return this;
    }

    /**
     * 构建规划收入
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder buildPlanIncome() {
        //查询销售计划
        List<String> schemeCodes = Lists.newArrayList(planMap.keySet());
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findListBySchemeCodes(schemeCodes);
        List<String> costCenterCodeList = salesPlanVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCostCenterCode()))
                .map(MarketingSalesPlanVo::getCostCenterCode).distinct().collect(Collectors.toList());
        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(costCenterCodeList);
        Map<String, MdmCostCenterVo> costCenterMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(costCenterVoList)) {
            costCenterMap = costCenterVoList.stream().collect(Collectors.toMap(x -> x.getCostCenterCode(), v -> v));
        }
        for (MarketingSalesPlanVo salesPlan : salesPlanVoList) {
            if (costCenterMap.containsKey(salesPlan.getCostCenterCode())) {
                MdmCostCenterVo costCenterVo1 = costCenterMap.get(salesPlan.getCostCenterCode());
                salesPlan.setCostCenterCompanyCode(costCenterVo1.getCompanyCode());
            }
        }
        this.salesPlanMap = salesPlanVoList.stream().collect(Collectors.groupingBy(x -> x.getSchemeCode() + x.getCustomerCode() + x.getYears()));
        List<String> orgCodes = reportCollectVoList.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
        this.orgVoListMap = orgVoService.findAllChildrenByOrgCodesMap(orgCodes);
        this.orgParentOrgMap = orgVoService.findAllParentByOrgCodesMap(orgCodes);
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            List<OrgVo> orgVoList = orgVoListMap.get(vo.getOrgCode());
            Set<String> costCenterCodes = orgVoList.stream()
                    .filter(x -> !CollectionUtils.isEmpty(x.getCostCenterCodeSet())) // 过滤空集合
                    .flatMap(x -> x.getCostCenterCodeSet().stream()) // 展开内部的 Set<String>
                    .collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(costCenterCodes)) {
                vo.setCostCenterCodes(Lists.newArrayList(costCenterCodes));
            }
            BigDecimal planIncome = BigDecimal.ZERO;
            //1.第一步直接把当前有搭赠费用的方案明细的费用金额拿出来
            planIncome = regionCaseMap.get(vo.getOrgCode()).stream().filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType())
                            && x.getActYears().equals(vo.getActYears()) && x.getCustomerCode().equals(vo.getCustomerCode())
                            && vo.getSchemeCode().equals(x.getSchemeCode()))
                    .map(x -> x.getNoTaxApplyAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            //2.第二步拿销售计划的预估费用(未税)
            String salesKey = vo.getSchemeCode() + vo.getCustomerCode() + vo.getActYears();
            List<MarketingSalesPlanVo> salesPlanVos = salesPlanMap.getOrDefault(salesKey, Lists.newArrayList());
            planIncome = planIncome.add(salesPlanVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getEstimatedCost()) && vo.getCostCenterCodes().contains(x.getCostCenterCode()))
                    .map(x -> x.getEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add));
            vo.setPlanIncome(planIncome);

        }
        return this;
    }

    /**
     * 构建计提收入
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder buildWithholdingIncome() {
        Map<String, List<String>> customerActYearsMap = reportCollectVoList.stream().collect(Collectors.groupingBy(PreActualReportCollectVo::getActYears,
                Collectors.mapping(PreActualReportCollectVo::getCustomerCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : customerActYearsMap.entrySet()) {
            Map<String, BigDecimal> posDataMap = this.calPosData(entry.getKey(), Sets.newHashSet(entry.getValue()));
            actYearsPostDataMap.put(entry.getKey(), posDataMap);
            String date = entry.getKey() + "-01";
            Map<String, String> dateMap = FormulaGetValueComponent.getDateMonthly(date, DateUtil.DEFAULT_YEAR_MONTH_DAY);
            //查询发货数据
            Set<String> customerCodeSet = Sets.newHashSet(entry.getValue());
//            Set<String> productCodes = dataList.stream().map(x -> x.getProductCode()).collect(Collectors.toSet());
            List<DmsWarehouseOrderDetailVo> orderDetailVos = this.findDmsOrderDelivery(customerCodeSet, Sets.newHashSet(), dateMap);
            actYersOrderDetailsMap.put(entry.getKey(), orderDetailVos);
        }
        for (PreActualReportCollectVo vo : reportCollectVoList) {

            List<DmsWarehouseOrderDetailVo> orderDetailVos = actYersOrderDetailsMap.getOrDefault(vo.getActYears(), Lists.newArrayList());
            //过滤客户
            orderDetailVos = orderDetailVos.stream().filter(x -> vo.getCustomerCode().equals(x.getCustomerCode())).collect(Collectors.toList());

            BigDecimal withholdingIncome = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getNoTaxOriginalTotalAmount()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(x -> x.getNoTaxOriginalTotalAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);

            //查询pos拆分占比数据
            Map<String, BigDecimal> posDataMap = actYearsPostDataMap.get(vo.getActYears());

            List<OrgVo> orgVoList = this.orgVoListMap.get(vo.getOrgCode());
            BigDecimal finalWithholdingIncome = null;
            BigDecimal posRate = BigDecimal.ZERO;
            for (OrgVo orgVo : orgVoList) {
                String key = vo.getCustomerCode() + orgVo.getOrgCode();
                if (posDataMap.containsKey(key)) {
                    if (ObjectUtils.isEmpty(finalWithholdingIncome)) {
                        finalWithholdingIncome = BigDecimal.ZERO;
                    }
                    BigDecimal rate = posDataMap.get(key);
                    posRate = posRate.add(rate);
                    finalWithholdingIncome = finalWithholdingIncome.add(withholdingIncome.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }
            if (ObjectUtils.isEmpty(finalWithholdingIncome)) {
                finalWithholdingIncome = withholdingIncome;
            }

            vo.setWithholdingIncome(finalWithholdingIncome);
            vo.setPosRate(posRate);
            vo.setOrderDetailVos(orderDetailVos);
            vo.setPosDataMap(posDataMap);
        }
        return this;
    }


    /**
     * 查询发货数据
     *
     * @param customerCodeSet
     * @param productCodes
     * @param dateMap
     * @return
     */
    private List<DmsWarehouseOrderDetailVo> findDmsOrderDelivery(Set<String> customerCodeSet, Set<String> productCodes, Map<String, String> dateMap) {
        List<String> customerCodeList = Lists.newArrayList(customerCodeSet);
        List<DmsWarehouseOrderDetailVo> orderDetailVos = Lists.newArrayList();
        if (customerCodeSet.size() > 1000) {
            List<List<String>> partitionCus = Lists.partition(customerCodeList, 800);
            for (List<String> cusList : partitionCus) {
                TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
                dto.setCustomerCodes(Lists.newArrayList(cusList));
                dto.setProductCodes(Lists.newArrayList(productCodes));
                dto.setSearchStartTime(dateMap.get(FormulaGetValueComponent.START_DATE));
                dto.setSearchEndTime(dateMap.get(FormulaGetValueComponent.END_DATE));
//                dto.setItemTypeList(Lists.newArrayList("normalGoods","complimentaryGoods"));
                List<DmsWarehouseOrderDetailVo> orderDetailVoList = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderDetailVoList)) {
                    orderDetailVos.addAll(orderDetailVoList);
                }
            }
        } else {
            TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
            dto.setCustomerCodes(Lists.newArrayList(customerCodeSet));
            dto.setProductCodes(Lists.newArrayList(productCodes));
            dto.setSearchStartTime(dateMap.get(FormulaGetValueComponent.START_DATE));
            dto.setSearchEndTime(dateMap.get(FormulaGetValueComponent.END_DATE));
            orderDetailVos = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
        }
        return orderDetailVos;
    }

    /**
     * 计算pos拆分占比
     *
     * @param years
     * @param customerCodeSet
     * @return
     */
    private Map<String, BigDecimal> calPosData(String years, Set<String> customerCodeSet) {
        String date = years + "-01";
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        // 获取上个月
        LocalDate lastMonth = localDate.minusMonths(1);
        // 获取上上个月
        LocalDate monthBeforeLast = localDate.minusMonths(2);
        //根据数据字典查询客户
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(FormulaGetValueComponent.CUSTOMER_SPLITTING);
        //上月客户
        List<String> lastMonthCustomer = dictDataVos.stream().filter(e -> FormulaGetValueComponent.LAST_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());
        //本月客户
        List<String> currentMonthCustomer = dictDataVos.stream().filter(e -> FormulaGetValueComponent.CURRENT_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());

        List<PosDataVo> posDataVoList = Lists.newArrayList();
        //判断上个月的客户数据不为空
        if (CollectionUtils.isNotEmpty(lastMonthCustomer)) {
            String dateStr = monthBeforeLast.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            List<String> lastCustomerCodes = Lists.newArrayList();
            for (String s : lastMonthCustomer) {
                if (!customerCodeSet.add(s)) {
                    lastCustomerCodes.add(s);
                }
            }
            if (CollectionUtils.isNotEmpty(lastCustomerCodes)) {
                PosDataVo dto = new PosDataVo();
                dto.setYears(dateStr);
                dto.setCustomerCodes(lastCustomerCodes);
                Page<PosDataVo> page = posDataService.findList(PageRequest.of(0, Integer.MAX_VALUE), dto);
                if (CollectionUtils.isNotEmpty(page.getRecords())) {
                    posDataVoList.addAll(page.getRecords());
                }
            }
        }
        //判断本月客户数据不为空
        if (CollectionUtils.isNotEmpty(currentMonthCustomer)) {
            String dateStr = lastMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            List<String> customerCodes = Lists.newArrayList();
            for (String s : currentMonthCustomer) {
                if (!customerCodeSet.add(s)) {
                    customerCodes.add(s);
                }
            }
            if (CollectionUtils.isNotEmpty(customerCodes)) {
                PosDataVo dto = new PosDataVo();
                dto.setYears(dateStr);
                dto.setCustomerCodes(customerCodes);
                Page<PosDataVo> page = posDataService.findList(PageRequest.of(0, Integer.MAX_VALUE), dto);
                if (CollectionUtils.isNotEmpty(page.getRecords())) {
                    posDataVoList.addAll(page.getRecords());
                }
            }
        }

        Map<String, BigDecimal> postDataMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(posDataVoList)) {
            Map<String, BigDecimal> posCusAmountMap = posDataVoList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode(),
                    Collectors.mapping(PosDataVo::getAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            for (PosDataVo posDataVo : posDataVoList) {
                if (posCusAmountMap.containsKey(posDataVo.getCustomerCode())) {
                    BigDecimal amount = posCusAmountMap.get(posDataVo.getCustomerCode());
                    BigDecimal rate = posDataVo.getAmount().divide(amount, 8, BigDecimal.ROUND_HALF_DOWN);
                    postDataMap.put(posDataVo.getCustomerCode() + posDataVo.getOrgCode(), rate);
                }
            }
        }
        return postDataMap;
    }


    /**
     * 构建达成率
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder buildAchieveRatio() {
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            vo.setIncomeAchieveRatio(BigDecimal.ZERO);
            vo.setIncomeAchieveRatioStr("0%");
            if (ObjectUtils.isNotEmpty(vo.getPlanIncome()) && vo.getPlanIncome().compareTo(BigDecimal.ZERO) == 1) {
                BigDecimal incomeAchieveRatio = vo.getWithholdingIncome().divide(vo.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
                vo.setIncomeAchieveRatio(incomeAchieveRatio);
                vo.setIncomeAchieveRatioStr(incomeAchieveRatio.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN).toPlainString() + "%");
            }
        }
        return this;
    }

    /**
     * 构建规划毛利率
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder buildPlanGrossProfitMargin() {
        //计算物料成本
        Set<String> companyCodes = salesPlanMap.values().stream().flatMap(List::stream).map(x -> x.getCompanyCode()).collect(Collectors.toSet());
        Set<String> materialCodes = salesPlanMap.values().stream().flatMap(List::stream).map(x -> x.getMaterialCode()).collect(Collectors.toSet());
        MaterialSearchDto searchDto = new MaterialSearchDto();
        searchDto.setMaterialCodeSet(materialCodes);
        searchDto.setCompanyCodeSet(companyCodes);
        List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
        Map<String, BigDecimal> materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO),(a,b)->a));
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            BigDecimal planCost = BigDecimal.ZERO;
            //1.第一步直接把当前有搭赠费用的成本费用金额拿出来
            planCost = regionCaseMap.get(vo.getOrgCode()).stream().filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType())
                            && x.getActYears().equals(vo.getActYears()) && x.getCustomerCode().equals(vo.getCustomerCode())
                            && x.getSchemeCode().equals(vo.getSchemeCode()))
                    .map(x -> x.getPolicyMaterialCostPrice().multiply(x.getGiftQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            //2.第二步拿销售计划的预估费用(未税)
            String salesKey = vo.getSchemeCode() + vo.getCustomerCode() + vo.getActYears();
            List<MarketingSalesPlanVo> salesPlanVos = salesPlanMap.getOrDefault(salesKey, Lists.newArrayList());
            salesPlanVos = salesPlanVos.stream().filter(x -> vo.getCostCenterCodes().contains(x.getCostCenterCode())).collect(Collectors.toList());

            Map<String, BigDecimal> materialSalesVolumeMap = salesPlanVos.stream().collect(Collectors.groupingBy(MarketingSalesPlanVo::getMaterialCode,
                    Collectors.mapping(MarketingSalesPlanVo::getEstimatedSalesVolume, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

            for (Map.Entry<String, BigDecimal> entry : materialSalesVolumeMap.entrySet()) {
                BigDecimal costPrice = materialMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
                planCost = planCost.add(costPrice.multiply(entry.getValue()));
            }
            vo.setPlanProductCost(planCost);
            //规划毛利额=规划成本/规划收入
            vo.setPlanGrossProfitMargin(BigDecimal.ZERO);
            vo.setPlanGrossProfitMarginStr("0%");
            if (vo.getPlanIncome().compareTo(BigDecimal.ZERO) == 1) {
                BigDecimal planGrossProfitMargin = vo.getPlanIncome().subtract(vo.getPlanProductCost()).divide(vo.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
                String planGrossProfitMarginStr = planGrossProfitMargin.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN).toPlainString() + "%";
                vo.setPlanGrossProfitMargin(planGrossProfitMargin);
                vo.setPlanGrossProfitMarginStr(planGrossProfitMarginStr);
            }
        }
        return this;
    }

    /**
     * 构建计提毛利率
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder buildWithholdingGrossProfitMargin() {
        List<DmsWarehouseOrderDetailVo> orderDetailVoList = reportCollectVoList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getOrderDetailVos()))
                .flatMap(x -> x.getOrderDetailVos().stream())
                .filter(e -> (ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(e.getItemType()) || giftItemTypeList.contains(e.getItemType())))
                .collect(Collectors.toList());
        Map<String, BigDecimal> materialMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orderDetailVoList)) {
            Set<String> materialCodes = orderDetailVoList.stream().map(x -> x.getMaterialCode()).collect(Collectors.toSet());
            Set<String> companyCodes = orderDetailVoList.stream().map(x -> x.getCompanyCode()).collect(Collectors.toSet());
            MaterialSearchDto searchDto = new MaterialSearchDto();
            searchDto.setMaterialCodeSet(materialCodes);
            searchDto.setCompanyCodeSet(companyCodes);
            List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
            materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO)));
        }
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            vo.setWithholdingProductCost(BigDecimal.ZERO);
            BigDecimal withholdingProductCosts = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(vo.getOrderDetailVos())) {
                for (DmsWarehouseOrderDetailVo detailVo : vo.getOrderDetailVos()) {
                    BigDecimal costPrice = materialMap.getOrDefault(detailVo.getMaterialCode(), BigDecimal.ZERO);
                    withholdingProductCosts = withholdingProductCosts.add(costPrice.multiply(detailVo.getRealOutNum()));
                }
            }
            List<OrgVo> orgVoList = this.orgVoListMap.get(vo.getOrgCode());
            BigDecimal finalWithholdingProductCosts = null;
            for (OrgVo orgVo : orgVoList) {
                String key = vo.getCustomerCode() + orgVo.getOrgCode();
                if (vo.getPosDataMap().containsKey(key)) {
                    if (ObjectUtils.isEmpty(finalWithholdingProductCosts)) {
                        finalWithholdingProductCosts = BigDecimal.ZERO;
                    }
                    BigDecimal rate = vo.getPosDataMap().get(key);
                    finalWithholdingProductCosts = finalWithholdingProductCosts.add(withholdingProductCosts.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }
            if (ObjectUtils.isEmpty(finalWithholdingProductCosts)) {
                finalWithholdingProductCosts = withholdingProductCosts;
            }
            vo.setWithholdingProductCost(finalWithholdingProductCosts);
            vo.setWithholdingGrossProfitMargin(BigDecimal.ZERO);
            BigDecimal grossProfitMargin = vo.getWithholdingIncome().subtract(vo.getWithholdingProductCost());
            if (vo.getWithholdingIncome().compareTo(BigDecimal.ZERO) == 1) {
                BigDecimal withholdingGrossProfitMargin = grossProfitMargin.divide(vo.getWithholdingIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
                String withholdingGrossProfitMarginStr = withholdingGrossProfitMargin.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN).toPlainString() + "%";
                vo.setWithholdingGrossProfitMargin(withholdingGrossProfitMargin);
                vo.setWithholdingGrossProfitMarginStr(withholdingGrossProfitMarginStr);
            }
        }
        return this;
    }

    /**
     * 构建毛利率偏差
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder grossProfitMarginDifference() {
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            BigDecimal grossProfitMarginDifference = vo.getWithholdingGrossProfitMargin().subtract(vo.getPlanGrossProfitMargin());
            String grossProfitMarginDifferenceStr = grossProfitMarginDifference.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%";
            vo.setGrossProfitMarginDifference(grossProfitMarginDifference);
            vo.setGrossProfitMarginDifferenceStr(grossProfitMarginDifferenceStr);
        }
        return this;
    }

    /**
     * 构建规划费率
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder planCostRatio() {
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            List<MarketingPlanCaseVo> caseVoList = regionCaseMap.get(vo.getOrgCode())
                    .stream().filter(x -> x.getActYears().equals(vo.getActYears()) && x.getCustomerCode().equals(vo.getCustomerCode())
                            && vo.getSchemeCode().equals(x.getSchemeCode())).collect(Collectors.toList());
            BigDecimal planCost = caseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getApplyAmount())).map(x -> x.getApplyAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setPlanCost(planCost);
            vo.setPlanCostRatio(BigDecimal.ZERO);
            vo.setPlanCostRatioStr("0%");
            if (vo.getPlanIncome().compareTo(BigDecimal.ZERO) == 1) {
                BigDecimal planCostRatio = planCost.divide(vo.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
                vo.setPlanCostRatio(planCostRatio);
                vo.setPlanCostRatioStr(planCostRatio.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN).toPlainString() + "%");
            }
        }
        return this;
    }

    /**
     * 构建计提费率
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder withholdingCostRatio() {
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            vo.setWithholdingCost(BigDecimal.ZERO);
            vo.setWithholdingCostRatio(BigDecimal.ZERO);
            vo.setWithholdingCostRatioStr("0%");
            BigDecimal withholdingCost = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(vo.getWithHoldingVoList())) {
                withholdingCost = withholdingCost.add(vo.getWithHoldingVoList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getActualReportAmount()))
                        .map(x -> x.getActualReportAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            if (CollectionUtils.isNotEmpty(vo.getFeeCashDetailVoList())) {
                withholdingCost = withholdingCost.add(vo.getFeeCashDetailVoList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getThisCashAmount()))
                        .map(x -> x.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            //发货数据
            List<DmsWarehouseOrderDetailVo> orderDetailList = vo.getOrderDetailVos();
            //查询物料成本
            Set<String> materialCodes = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getGoodsCode())).filter(Objects::nonNull)
                    .map(DmsWarehouseOrderDetailVo::getGoodsCode).collect(Collectors.toSet());
            Set<String> companyCodeSet = orderDetailList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode())).filter(Objects::nonNull)
                    .map(DmsWarehouseOrderDetailVo::getCompanyCode).collect(Collectors.toSet());
            MaterialSearchDto searchDto = new MaterialSearchDto();
            searchDto.setMaterialCodeSet(materialCodes);
            searchDto.setCompanyCodeSet(companyCodeSet);
            List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
            Map<String, Map<String, BigDecimal>> materialMap = materialVoList.stream().collect(Collectors.groupingBy(x -> x.getMaterialCode(), Collectors.toMap(x -> x.getFactoryTypeCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO), (a, b) -> a)));
            Map<String, BigDecimal> materialTaxRateMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getTaxRate()).orElse(BigDecimal.ZERO), (a, b) -> a));
            String year = vo.getActYears().split("-")[0];
            //查询物流成本
            List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialCodes, new ArrayList<>(companyCodeSet), year);
            Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                    x.getCompanyCode(), Function.identity()));
            //搭赠费用(销售价*实际发货数量)
            BigDecimal giftCost = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && giftItemTypeList.contains(e.getItemType())
                            && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType())).filter(Objects::nonNull)
                    .map(e -> e.getPresetUnitPrice().multiply(e.getRealOutNum()).divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            //查询pos拆分占比数据
            Map<String, BigDecimal> posDataMap = vo.getPosDataMap();
            List<OrgVo> orgVoList = this.orgVoListMap.get(vo.getOrgCode());
            BigDecimal finalGiftCost = null;
            BigDecimal posRate = BigDecimal.ZERO;
            for (OrgVo orgVo : orgVoList) {
                String key = vo.getCustomerCode() + orgVo.getOrgCode();
                if (posDataMap.containsKey(key)) {
                    if (ObjectUtils.isEmpty(finalGiftCost)) {
                        finalGiftCost = BigDecimal.ZERO;
                    }
                    BigDecimal rate = posDataMap.get(key);
                    posRate = posRate.add(rate);
                    finalGiftCost = finalGiftCost.add(giftCost.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }
            if (ObjectUtils.isEmpty(finalGiftCost)) {
                finalGiftCost = giftCost;
            }
            BigDecimal materialCost = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && peripheryItemTypeList.contains(e.getItemType())
                            && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType())).filter(Objects::nonNull)
                    .map(e -> e.getPresetUnitPrice().multiply(e.getRealOutNum()).divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            withholdingCost = withholdingCost.add(finalGiftCost).add(materialCost);
            vo.setMaterialMap(materialMap);
            vo.setMaterialTaxRateMap(materialTaxRateMap);
            vo.setMaterialCostMap(materialCostMap);
            vo.setWithholdingCost(withholdingCost);
            if (vo.getWithholdingIncome().compareTo(BigDecimal.ZERO) == 1) {
                BigDecimal withholdingCostRatio = withholdingCost.divide(vo.getWithholdingIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
                String withholdingCostRatioStr = withholdingCostRatio.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%";
                vo.setWithholdingCostRatio(withholdingCostRatio);
                vo.setWithholdingCostRatioStr(withholdingCostRatioStr);
            }
        }
        return this;
    }

    /**
     * 构建费率偏差
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder costRatioDifference() {
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            BigDecimal costRatioDifference = vo.getWithholdingCostRatio().subtract(vo.getPlanCostRatio());
            String costRatioDifferenceStr = costRatioDifference.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%";
            vo.setCostRatioDifference(costRatioDifference);
            vo.setCostRatioDifferenceStr(costRatioDifferenceStr);
        }
        return this;
    }

    /**
     * 构建规划利润率
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder planProfitRatio() {
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            List<OrgVo> orgVoList = orgParentOrgMap.get(vo.getOrgCode());
            List<String> orgCodes = orgVoList.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
            //查询公摊费率
            BigDecimal ratio = publicShareRatioService.findRatioByCondition(orgCodes, vo.getActYears());
            vo.setPublicShareRatio(ratio);

            //产品运输费用
            String salesKey = vo.getSchemeCode() + vo.getCustomerCode() + vo.getActYears();
            List<MarketingSalesPlanVo> salesPlanVos = salesPlanMap.getOrDefault(salesKey, Lists.newArrayList());
            List<MarketingSalesPlanVo> salesVolumeList = salesPlanVos.stream().filter(x -> vo.getCostCenterCodes().contains(x.getCostCenterCode())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(salesVolumeList)) {
                //销售计划的物料-找到物料成本价，用销售计划上的客户+成本中心+年月匹配得到物料 在用预估销售额*物料成本价
                Map<String, BigDecimal> materialSalesVolumeMap = salesVolumeList.stream().collect(Collectors.groupingBy(MarketingSalesPlanVo::getMaterialCode,
                        Collectors.mapping(MarketingSalesPlanVo::getEstimatedSalesVolume, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

                List<String> companyCodes = salesVolumeList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCostCenterCompanyCode()))
                        .map(x -> x.getCostCenterCompanyCode()).distinct().collect(Collectors.toList());
                String year = vo.getActYears().split("-")[0];
                //查询物料成本
                List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialSalesVolumeMap.keySet(), companyCodes, year);
                Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                        x.getCompanyCode() + column + x.getYearStr(), Function.identity()));
                //产品运输费用
                BigDecimal productTransport = BigDecimal.ZERO;
                for (MarketingSalesPlanVo plan : salesVolumeList) {
                    String key = plan.getMaterialCode() + column + plan.getCompanyCode() + column + year;
                    if (materialCostMap.containsKey(key)) {
                        MaterialCostVo materialCostVo = materialCostMap.get(key);
                        BigDecimal price = BigDecimal.ZERO;
                        if (MarketingTransportEnum.bulk_cargo.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getLargeLogisticsPrice();
                        } else if (MarketingTransportEnum.express.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getExpressLogisticsPrice();
                        } else if (MarketingTransportEnum.cold_chain.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getColdLogisticsPrice();
                        } else if (MarketingTransportEnum.factory.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getFactoryStraightPrice();
                        }
                        productTransport = productTransport.add(price.multiply(plan.getEstimatedSalesVolume()));

                    }
                }
                vo.setProductTransportCost(ObjectUtils.defaultIfNull(vo.getProductTransportCost(), BigDecimal.ZERO).add(productTransport));
            }
            List<MarketingPlanCaseVo> caseVoList = regionCaseMap.getOrDefault(vo.getOrgCode(), Lists.newArrayList())
                    .stream().filter(x -> x.getActYears().equals(vo.getActYears()) && x.getCustomerCode().equals(vo.getCustomerCode())
                            && vo.getSchemeCode().equals(x.getSchemeCode())).collect(Collectors.toList());
            //计算物料模板的费用运输
            BigDecimal peripheryTransportCost = BigDecimal.ZERO;
            List<MarketingPlanCaseVo> materialCaseList = caseVoList
                    .stream().filter(x -> MarketingPlanCaseTypeEnum.material.getCode().equals(x.getCaseType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(materialCaseList)) {
                //标准物料
                BigDecimal materialStandardAmount = materialCaseList.stream().filter(x -> MarketingPlanMaterialTypeEnum.standard_material.getCode().equals(x.getSellMaterialType()))
                        .map(MarketingPlanCaseVo::getApplyAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalStandardMaterialAmount = materialStandardAmount.multiply(BigDecimal.valueOf(0.15));
                peripheryTransportCost = peripheryTransportCost.add(totalStandardMaterialAmount);
                //非标准物料
                List<MarketingPlanCaseVo> notStandardMaterialList = materialCaseList.stream().filter(x -> MarketingPlanMaterialTypeEnum.not_standard_material.getCode().equals(x.getSellMaterialType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(notStandardMaterialList)) {
                    List<String> companyCodes = Lists.newArrayList();
                    Set<String> notStandardMaterialCodes = Sets.newHashSet();
                    for (MarketingPlanCaseVo planCase : notStandardMaterialList) {
                        companyCodes.add(planCase.getCompanyCode());
                        notStandardMaterialCodes.add(planCase.getMaterialCode());
                    }
                    //查询物料成本
                    String year = vo.getActYears().split("-")[0];
                    List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(notStandardMaterialCodes, companyCodes, year);
                    Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                            x.getCompanyCode() + column + x.getYearStr(), Function.identity()));
                    for (MarketingPlanCaseVo planCase : notStandardMaterialList) {
                        String key = planCase.getMaterialCode() + column + planCase.getCompanyCode() + column + year;
                        if (materialCostMap.containsKey(key)) {
                            MaterialCostVo materialCostVo = materialCostMap.get(key);
                            BigDecimal price = BigDecimal.ZERO;
                            if (MarketingTransportEnum.bulk_cargo.getCode().equals(planCase.getTransportType())) {
                                price = materialCostVo.getLargeLogisticsPrice();
                            } else if (MarketingTransportEnum.express.getCode().equals(planCase.getTransportType())) {
                                price = materialCostVo.getExpressLogisticsPrice();
                            } else if (MarketingTransportEnum.cold_chain.getCode().equals(planCase.getTransportType())) {
                                price = materialCostVo.getColdLogisticsPrice();
                            } else if (MarketingTransportEnum.factory.getCode().equals(planCase.getTransportType())) {
                                price = materialCostVo.getFactoryStraightPrice();
                            }
                            peripheryTransportCost = peripheryTransportCost.add(price.multiply(planCase.getMaterialNum()));

                        }
                    }
                }
            }
            //周边运输费用
            vo.setPeripheryTransportCost(peripheryTransportCost);
            //营销费用
            BigDecimal marketingCost = caseVoList.stream()
                    .map(x -> Optional.ofNullable(x.getApplyAmount()).orElse(BigDecimal.ZERO))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            vo.setMarketingCost(marketingCost);
            //规划利润额=规划收入-生产成本-产品运输费用-周边运输费用-营销费用-公摊费用
            vo.setPlanProfitMargin(vo.getPlanIncome().subtract(vo.getPlanProductCost()).subtract(ObjectUtils.defaultIfNull(vo.getProductTransportCost(), BigDecimal.ZERO))
                    .subtract(vo.getPeripheryTransportCost())
                    .subtract(vo.getMarketingCost()).subtract(vo.getPublicShareRatio().multiply(vo.getPlanIncome())));
            //利润率=规划利润额/规划收入
            vo.setPlanProfitRatio(BigDecimal.ZERO);
            vo.setPlanProfitRatioStr("0%");
            if (ObjectUtils.isNotEmpty(vo.getPlanIncome()) && vo.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                vo.setPlanProfitRatio(vo.getPlanProfitMargin().divide(vo.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
                vo.setPlanProfitRatioStr(vo.getPlanProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }

        }
        return this;
    }

    /**
     * 构建计提利润率
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder withholdingProfitRatio() {

        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SAP_FACTORY_TYPE);
        Map<String, String> dictMap = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));

        for (PreActualReportCollectVo vo : reportCollectVoList) {
            vo.setWithholdingProfitRatio(BigDecimal.ZERO);
            vo.setWithholdingProfitRatioStr("0%");
            if (CollectionUtils.isNotEmpty(vo.getOrderDetailVos())) {
                List<DmsWarehouseOrderDetailVo> orderDetailList = vo.getOrderDetailVos();
                Map<String, Map<String, BigDecimal>> materialMap = vo.getMaterialMap();
                Map<String, BigDecimal> materialTaxRateMap = vo.getMaterialTaxRateMap();

                Map<String, MaterialCostVo> materialCostMap = vo.getMaterialCostMap();

                //生产成本(对应物料主数据的单价*实际发货数量)含搭赠
                Map<String, BigDecimal> goodsMap = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && (ItemTypeEnum.NORMAL_GOODS.getDictCode().equals(e.getItemType()) || giftItemTypeList.contains(e.getItemType()))
                                && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType())).filter(Objects::nonNull)
                        .collect(Collectors.groupingBy(e -> e.getGoodsCode() + column + e.getCompanyCode(), Collectors.mapping(DmsWarehouseOrderDetailVo::getRealOutNum, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                BigDecimal goodsCost = BigDecimal.ZERO;
                for (Map.Entry<String, BigDecimal> entry : goodsMap.entrySet()) {
                    String[] split = entry.getKey().split(column);
                    Map<String, BigDecimal> factoryMap = materialMap.getOrDefault(split[0], new HashMap<>());
                    BigDecimal price = factoryMap.getOrDefault(dictMap.get(split[1]), BigDecimal.ZERO);
                    goodsCost = goodsCost.add(entry.getValue().multiply(price));
                }
                //搭赠费用(销售价*实际发货数量)
                BigDecimal giftCost = orderDetailList.stream().filter(e -> e.getRealOutNum() != null && giftItemTypeList.contains(e.getItemType())
                                && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType())).filter(Objects::nonNull)
                        .map(e -> e.getPresetUnitPrice().multiply(e.getRealOutNum()).divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //运输费用(产品运输费用+周边运输费用)
                BigDecimal productExpressFee = orderDetailList.stream().filter(e -> e.getExpressFee() != null &&
                                productItemTypeList.contains(e.getItemType())
                                && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                        .map(e -> {
                            BigDecimal price = materialCostMap.getOrDefault(e.getMaterialCode() + column + e.getCompanyCode(), new MaterialCostVo()).getLargeLogisticsPrice();
                            return (Optional.ofNullable(price).orElse(BigDecimal.ZERO).multiply(e.getRealOutNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                        })
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal peripheryExpressFee = orderDetailList.stream().filter(e -> e.getExpressFee() != null &&
                                peripheryItemTypeList.contains(e.getItemType())
                                && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                        .map(e -> {
                            BigDecimal price = materialCostMap.getOrDefault(e.getMaterialCode() + column + e.getCompanyCode(), new MaterialCostVo()).getLargeLogisticsPrice();
                            return (Optional.ofNullable(price).orElse(BigDecimal.ZERO).multiply(e.getRealOutNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                        })
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (vo.getPosRate().compareTo(BigDecimal.ZERO) == 1) {
                    productExpressFee = productExpressFee.multiply(vo.getPosRate());
                    peripheryExpressFee = peripheryExpressFee.multiply(vo.getPosRate());
                }
                vo.setProductExpressFee(productExpressFee);
                vo.setPeripheryExpressFee(peripheryExpressFee);
                BigDecimal withholdingMarketingCost = vo.getWithholdingCost().add(giftCost);
                vo.setWithholdingMarketingCost(withholdingMarketingCost);
                BigDecimal withholdingProfitMargin = vo.getWithholdingIncome().subtract(vo.getWithholdingProductCost()).subtract(productExpressFee)
                        .subtract(peripheryExpressFee).subtract(withholdingMarketingCost).subtract(vo.getPublicShareRatio().multiply(vo.getWithholdingIncome()));
                vo.setWithholdingProfitMargin(withholdingProfitMargin);
                if (vo.getWithholdingIncome().compareTo(BigDecimal.ZERO) == 1) {
                    BigDecimal withholdingProfitRatio = withholdingProfitMargin.divide(vo.getWithholdingIncome(), 2, BigDecimal.ROUND_HALF_DOWN);
                    String withholdingProfitRatioStr = withholdingProfitRatio.multiply(BigDecimal.valueOf(100)) + "%";
                    vo.setWithholdingProfitRatio(withholdingProfitRatio);
                    vo.setWithholdingProfitRatioStr(withholdingProfitRatioStr);
                }
            }
        }
        return this;
    }

    /**
     * 构建利润率偏差
     *
     * @return
     */
    @Override
    public PreActualReportCollectBuilder profitRatioDifference() {
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            BigDecimal profitRatioDifference = vo.getWithholdingProfitRatio().subtract(vo.getPlanProfitRatio());
            String profitRatioDifferenceStr = profitRatioDifference.multiply(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_DOWN) + "%";
            vo.setProfitRatioDifference(profitRatioDifference);
            vo.setProfitRatioDifferenceStr(profitRatioDifferenceStr);
        }
        return this;
    }
}
