package com.biz.crm.tpm.admin.web.exports.overplan.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanProductVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: haiyang
 * @Date: 2025-04-14 15:52
 * @Desc:
 */
@Data
@CrmExcelExport
public class HeadOverPlanCaseExportVo extends CrmExcelVo {

    @CrmExcelColumn("指引明细编码")
    private String schemeDetailCode;

    @CrmExcelColumn("二级费用大类")
    private String secondCostCategoryName;

    @CrmExcelColumn("费用项目")
    private String detailName;

    @CrmExcelColumn("开始时间")
    private String startDate;

    @CrmExcelColumn("结束时间")
    private String endDate;

    @CrmExcelColumn("预算使用年月")
    private String years;

    @CrmExcelColumn("承接销售部门")
    private String bearDepartmentNameStr;

    @CrmExcelColumn("承接类型")
    private String bearType;

    @CrmExcelColumn("费用承担部门")
    private String bearDepartmentName;

    @CrmExcelColumn("成本中心名称")
    private String costCenterName;


    @CrmExcelColumn("渠道类型名称")
    private String channelTypeNameStr;

    @CrmExcelColumn("客户名称")
    private String customerNameStr;

    @CrmExcelColumn("合作类型")
    private String cooperateTypeStr;


    @CrmExcelColumn("客户标签")
    private String customerTagStr;

    @ApiModelProperty("门店")
    private String terminalNameStr;

    @CrmExcelColumn("终端标签")
    private String terminalTagStr;


    @CrmExcelColumn("品项")
    private String itemName;

    @CrmExcelColumn("产品")
    private String productName;

    @CrmExcelColumn("预估费用")
    private BigDecimal applyAmount;

    @CrmExcelColumn("预估销售额")
    private BigDecimal estimatedSalesVolume;




    // Y N
    @CrmExcelColumn("大区是否拆解")
    private String bearFlag;

    @CrmExcelColumn("兑付条件及说明")
    private String cashCondition;
}
