package com.biz.crm.tpm.admin.web.preactualdetailreport.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 14:55
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_pre_actual_detail_report")
@Table(
        name = "tpm_pre_actual_detail_report",
        indexes = {
                @Index(name = "tpm_pre_actual_detail_report_index0", columnList = "act_years"),
                @Index(name = "tpm_pre_actual_detail_report_index1", columnList = "customer_code,customer_name"),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_pre_actual_detail_report", comment = "预实明细报表")
@ApiModel(value = "PreActualDetailReportEntity", description = "预实明细报表")
public class PreActualDetailReportEntity extends TenantFlagOpEntity {

    @ApiModelProperty("项目编码")
    @Column(name = "project_code", columnDefinition = "varchar(32) comment '项目编码'")
    private String projectCode;

    @ApiModelProperty("项目名称")
    @Column(name = "project_name", columnDefinition = "varchar(64) comment '项目名称'")
    private String projectName;

    @ApiModelProperty("排序")
    @Column(name = "sort", columnDefinition = "int comment '排序'")
    private Integer sort;

    @ApiModelProperty("预算年月")
    @Column(name = "years",columnDefinition = "varchar(10) comment '预算年月'")
    private String years;

    @ApiModelProperty("活动年月")
    @Column(name = "act_years", columnDefinition = "varchar(10) comment '活动年月'")
    private String actYears;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '客户编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("中心编码")
    @Column(name = "center_code", columnDefinition = "varchar(32) comment '中心编码'")
    private String centerCode;

    @ApiModelProperty("中心名称")
    @Column(name = "center_name", columnDefinition = "varchar(64) comment '中心名称'")
    private String centerName;

    @ApiModelProperty("一级部门编码")
    @Column(name = "department_one_code", columnDefinition = "varchar(32) comment '一级部门'")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称")
    @Column(name = "department_one_name", columnDefinition = "varchar(64) comment '一级部门名称'")
    private String departmentOneName;

    @ApiModelProperty("二级部门编码")
    @Column(name = "department_two_code", columnDefinition = "varchar(32) comment '二级部门编码'")
    private String departmentTwoCode;

    @ApiModelProperty("二级部门名称")
    @Column(name = "department_two_name", columnDefinition = "varchar(64) comment '二级部门名称'")
    private String departmentTwoName;

    @ApiModelProperty("三级部门编码")
    @Column(name = "department_three_code", columnDefinition = "varchar(32) comment '三级部门编码'")
    private String departmentThreeCode;

    @ApiModelProperty("三级部门名称")
    @Column(name = "department_three_name", columnDefinition = "varchar(64) comment '三级部门名称'")
    private String departmentThreeName;

    @ApiModelProperty("预算金额")
    @Column(name = "budget_cost", columnDefinition = "decimal(18,2) comment '预算金额'")
    private BigDecimal budgetCost;

    @ApiModelProperty("预算费率")
    @Column(name = "budget_cost_ratio", columnDefinition = "decimal(18,2) comment '预算费率'")
    private BigDecimal budgetCostRatio;

    @ApiModelProperty("规划金额-最初版")
    @Column(name = "plan_amount", columnDefinition = "decimal(18,2) comment '规划金额-最初版'")
    private BigDecimal planAmount;

    @ApiModelProperty("规划金额-变更后")
    @Column(name = "last_plan_amount", columnDefinition = "decimal(18,2) comment '规划金额-变更后'")
    private BigDecimal lastPlanAmount;

    @ApiModelProperty("规划费率-最初版")
    @Column(name = "plan_cost_ratio", columnDefinition = "decimal(18,2) comment '规划费率-最初版'")
    private BigDecimal planCostRatio;

    @ApiModelProperty("规划费率-变更后")
    @Column(name = "last_plan_cost_ratio", columnDefinition = "decimal(18,2) comment '规划费率-变更后'")
    private BigDecimal lastPlanCostRatio;

    @ApiModelProperty("计提金额")
    @Column(name = "withholding_cost", columnDefinition = "decimal(18,2) comment '计提金额'")
    private BigDecimal withholdingCost;

    @ApiModelProperty("计提费率")
    @Column(name = "withholding_cost_ratio", columnDefinition = "decimal(18,2) comment '计提费率'")
    private BigDecimal withholdingCostRatio;

    @ApiModelProperty("费率偏差")
    @Column(name = "cost_difference_ratio", columnDefinition = "decimal(18,2) comment '费率偏差'")
    private BigDecimal costDifferenceRatio;

    @ApiModelProperty("结案金额")
    @Column(name = "audit_cost", columnDefinition = "decimal(18,2) comment '结案金额'")
    private BigDecimal auditCost;

    @ApiModelProperty("结案费率")
    @Column(name = "audit_cost_ratio", columnDefinition = "decimal(18,2) comment '结案费率'")
    private BigDecimal auditCostRatio;

    @ApiModelProperty("冲销金额")
    @Column(name = "write_off_cost", columnDefinition = "decimal(18,2) comment '冲销金额'")
    private BigDecimal writeOffCost;

    @ApiModelProperty("冲销费率")
    @Column(name = "write_off_cost_ratio", columnDefinition = "decimal(18,2) comment '冲销费率'")
    private BigDecimal writeOffCostRatio;

    @Column(name = "index_key",columnDefinition = "varchar(255) comment '关键key(客户编码+费用使用部门+活动年月+项目)'")
    private String indexKey;

}
