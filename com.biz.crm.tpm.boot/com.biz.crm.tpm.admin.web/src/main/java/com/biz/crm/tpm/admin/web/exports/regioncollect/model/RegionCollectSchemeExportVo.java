package com.biz.crm.tpm.admin.web.exports.regioncollect.model;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "RegionCollectSchemeVo", description = "大区汇总方案明细")
public class RegionCollectSchemeExportVo {

    @ApiModelProperty("人员姓名")
    private String fullName;

    @ApiModelProperty("提报部门")
    private String orgName;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

}
