package com.biz.crm.tpm.admin.web.register;

import com.biz.crm.tpm.business.activities.ordinary.constant.ActivitiesConstant;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>活动申请数据视图注册器
 * 基于nebula的数据视图提供活动申请列表查询功能
 *
 * <AUTHOR>
 * @date 2022/8/15
 */
@Component
public class OrdinaryActivityDataViewRegister implements DataviewRegister {
  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-活动申请";
  }

  @Override
  public String buildSql() {
    return "select distinct toa.*, bpbm.process_status, bpbm.process_no " +
        "from tpm_ordinary_activity toa " +
        "left join bpm_process_business_mapping bpbm on (toa.code = bpbm.business_no and bpbm.business_code = '" + ActivitiesConstant.ORDINARY_ACTIVITY_PROCESS_NAME + "') " +
        "where toa.tenant_code = :tenantCode " +
        "and toa.del_flag = '009' ";
  }
}
