package com.biz.crm.tpm.admin.web.service.internal.strategy.impl;

import com.biz.crm.business.common.base.service.BusinessWriteMdmDataStrategy;
import com.biz.crm.business.common.base.vo.ExportWriteMdmVo;
import com.biz.crm.business.common.ie.local.BusinessExcelWriteMdmDataUtil;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductSmallClassVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo;
import com.biz.crm.tpm.admin.web.vo.*;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import liquibase.pro.packaged.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * @Description 营销方案的数据
 * <AUTHOR>
 * @Date 2024/9/24 22:56
 */
@Component
@Slf4j
public class MarketingPlanImportServiceImpl implements BusinessWriteMdmDataStrategy {


    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private TerminalVoService terminalVoService;

    @Resource
    private MdmCostCenterVoService costCenterVoService;

    @Resource
    private ProductSmallClassVoService productSmallClassVoService;

    @Resource
    private MaterialVoService materialVoService;

    @Resource
    private ProductPhaseVoService productPhaseVoService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private BusinessExcelWriteMdmDataUtil businessExcelWriteMdmDataUtil;

    @Resource
    private DictDataVoService dictDataVoService;


    @Override
    public String writeMdmDataToTemplate(String fileCode, Map<String, Integer> sheetMap) {
        //查询客户信息
        List<CustomerVo> customerVos = customerVoService.loadCacheCustomer();
        Map<String, String> cooperateTypeMap = dictDataVoService.findMapByDictTypeCode(DictConstant.MDM_COOPERATE_TYPE);
        for (CustomerVo customerVo : customerVos) {
            if (ObjectUtils.isNotEmpty(customerVo.getCooperateType())) {
                customerVo.setCooperateType(cooperateTypeMap.get(customerVo.getCooperateType()));
            }
        }
        List<CustomerWriteImportVo> customerWriteImportVos = (List<CustomerWriteImportVo>) nebulaToolkitService.copyCollectionByWhiteList(customerVos, CustomerVo.class, CustomerWriteImportVo.class, HashSet.class, ArrayList.class);
        Map<String, List<? extends ExportWriteMdmVo>> map = Maps.newHashMap();
        map.put("客户信息", customerWriteImportVos);
        //组织信息
        List<OrgVo> orgVoList = orgVoService.loadOrgListToCache();
        Map<String, String> orgDictMap = dictDataVoService.findMapByDictTypeCode(DictConstant.MDM_ORG_TYPE);
        for (OrgVo orgVo : orgVoList) {
            orgVo.setOrgType(orgDictMap.getOrDefault(orgVo.getOrgType(), null));
        }
        List<OrgWriteImportVo> orgWriteImportVos = (List<OrgWriteImportVo>) nebulaToolkitService.copyCollectionByBlankList(orgVoList, OrgVo.class, OrgWriteImportVo.class, HashSet.class, ArrayList.class);
        map.put("营销组织", orgWriteImportVos);
        //终端信息
        List<TerminalVo> terminalVos = terminalVoService.loadCacheTerminal();
        Map<String, String> terminalChanneltMap = dictDataVoService.findMapByDictTypeCode(DictConstant.TERMINAL_CHANNEL);
        for (TerminalVo terminalVo : terminalVos) {
            if (ObjectUtils.isNotEmpty(terminalVo.getChannel())) {
                terminalVo.setChannel(terminalChanneltMap.get(terminalVo.getChannel()));
            }
        }
        List<TerminalWriteImportVo> terminalWriteImportVos = (List<TerminalWriteImportVo>) nebulaToolkitService.copyCollectionByBlankList(terminalVos, TerminalVo.class, TerminalWriteImportVo.class, HashSet.class, ArrayList.class);
        map.put("终端信息", terminalWriteImportVos);
        //成本中心
        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.loadCacheCostCenter();
        List<CostCenterWriteImportVo> costCenterWriteImportVos = (List<CostCenterWriteImportVo>) nebulaToolkitService.copyCollectionByBlankList(costCenterVoList, MdmCostCenterVo.class, CostCenterWriteImportVo.class, HashSet.class, ArrayList.class);
        map.put("成本中心", costCenterWriteImportVos);
        //产品小类
        List<ProductSmallClassVo> productSmallClassVos = productSmallClassVoService.loadCacheProductSmallClass();
        List<ProductSmallClassWriteImportVo> productSmallClassWriteImportVos = (List<ProductSmallClassWriteImportVo>) nebulaToolkitService.copyCollectionByWhiteList(productSmallClassVos, ProductSmallClassVo.class, ProductSmallClassWriteImportVo.class, HashSet.class, ArrayList.class);
        map.put("产品小类", productSmallClassWriteImportVos);
        //物料管理
        List<MaterialVo> materialVoList = materialVoService.loadCacheMaterial();
        Map<String, String> materialTypeMap = dictDataVoService.findMapByDictTypeCode(MATERIAL_TYPE);
        Map<String, String> sellMaterialTypeMap = dictDataVoService.findMapByDictTypeCode(MDM_SELL_MATERIAL_TYPE);
        Map<String, String> productSaleUnitMap = dictDataVoService.findMapByDictTypeCode(DictConstant.PRODUCT_SALE_UNIT);
        for (MaterialVo materialVo : materialVoList) {
            if (ObjectUtils.isNotEmpty(materialVo.getMaterialType())) {
                materialVo.setMaterialType(materialTypeMap.get(materialVo.getMaterialType()));
            }
            if (ObjectUtils.isNotEmpty(materialVo.getSellMaterialType())) {
                materialVo.setSellMaterialType(sellMaterialTypeMap.get(materialVo.getSellMaterialType()));
            }
            if (ObjectUtils.isNotEmpty(materialVo.getStandardUnit())) {
                materialVo.setStandardUnit(productSaleUnitMap.get(materialVo.getStandardUnit()));
            }
        }
        List<MaterialWriteImportVo> materialWriteImportVos = (List<MaterialWriteImportVo>) nebulaToolkitService.copyCollectionByBlankList(materialVoList, MaterialVo.class, MaterialWriteImportVo.class, HashSet.class, ArrayList.class);
        map.put("物料管理", materialWriteImportVos);
        //品项管理
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.loadCacheProductPhase();
        List<ProductPhaseWriteImportVo> productPhaseWriteImportVos = (List<ProductPhaseWriteImportVo>) nebulaToolkitService.copyCollectionByBlankList(productPhaseVos, ProductPhaseVo.class, ProductPhaseWriteImportVo.class, HashSet.class, ArrayList.class);
        map.put("品项管理", productPhaseWriteImportVos);
        String fileId = null;
        try {
            fileId = businessExcelWriteMdmDataUtil.writeMdmDataToExcel(sheetMap, fileCode, map);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return fileId;
    }

    private static final String MATERIAL_TYPE = "material_type";

    private static final String MDM_SELL_MATERIAL_TYPE = "mdm_sell_material_type";

}
