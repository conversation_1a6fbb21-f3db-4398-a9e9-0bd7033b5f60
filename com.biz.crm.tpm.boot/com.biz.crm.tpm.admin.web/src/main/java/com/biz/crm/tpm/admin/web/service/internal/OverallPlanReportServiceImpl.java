package com.biz.crm.tpm.admin.web.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosure;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosureDetail;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.PlanClosureEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.PlanClosureDetailRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.PlanClosureRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanDepartment;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanProduct;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanScope;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.repository.OverPlanCaseRepository;
import com.biz.crm.tpm.business.activities.overallplan.repository.OverPlanDepartmentRepository;
import com.biz.crm.tpm.business.activities.overallplan.repository.OverPlanProductRepository;
import com.biz.crm.tpm.business.activities.overallplan.repository.OverPlanScopeRepository;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanCaseService;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanService;
import com.biz.crm.tpm.business.activities.overallplan.vo.*;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/4 17:45
 */
@Service
@Slf4j
public class OverallPlanReportServiceImpl {

    @Resource
    private OverPlanCaseRepository overPlanCaseRepository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private OverPlanProductRepository overPlanProductRepository;

    @Resource
    private OverPlanScopeRepository overPlanScopeRepository;

    @Resource
    private MarketingPlanCaseService marketingPlanCaseService;

    @Resource
    private WithHoldingService withHoldingService;

    @Resource
    private OverallPlanCaseService overallPlanCaseService;

    @Resource
    private OverallPlanService overallPlanService;

    @Resource
    private OverPlanDepartmentRepository overPlanDepartmentRepository;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private PlanClosureRepository planClosureRepository;

    @Resource
    private PlanClosureDetailRepository planClosureDetailRepository;

    @Resource
    private MarketingPlanCaseRepository marketingPlanCaseRepository;

    /**
     * 通过方案类型查询分页明细
     *
     * @param pageable
     * @return
     */
    public Page<OverallPlanCaseReportVo> findListBySchemeType(Pageable pageable, OverallPlanCaseVo caseVo, String schemeType) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<OverallPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<OverallPlanCaseVo> data = overPlanCaseRepository.findListBySchemeType(page, caseVo, schemeType);
        Page<OverallPlanCaseReportVo> resultData = new Page<>();
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<OverallPlanCaseReportVo> dataList = (List<OverallPlanCaseReportVo>) nebulaToolkitService.copyCollectionByWhiteList(data.getRecords(),
                    OverallPlanCaseVo.class, OverallPlanCaseReportVo.class, HashSet.class, ArrayList.class);

            List<String> schemeDetailCodes = dataList.stream().map(OverallPlanCaseReportVo::getSchemeDetailCode).collect(Collectors.toList());
            List<OverallPlanProduct> overallPlanProductList = overPlanProductRepository.findListBySchemeDetailCodeList(schemeDetailCodes);


            List<MarketingPlanCase> marketingPlanCaseVos = marketingPlanCaseRepository.findByReleaseDetailCodes(schemeDetailCodes);
            Map<String, List<MarketingPlanCase>> planCaseGuideMap = marketingPlanCaseVos.stream().collect(Collectors.groupingBy(MarketingPlanCase::getReleaseDetailCode));

            List<String> planCaseSchemeDetailCodes = marketingPlanCaseVos.stream().map(MarketingPlanCase::getSchemeDetailCode).distinct().collect(Collectors.toList());

            List<PlanClosureDetail> planClosureDetails =  planClosureDetailRepository.findBySchemeDetailCodeList(planCaseSchemeDetailCodes);
            List<String> closeCodes = planClosureDetails.stream().map(PlanClosureDetail::getCloseCode).distinct().collect(Collectors.toList());
            List<PlanClosure> planClosures = planClosureRepository.findByCloseCodeList(closeCodes);
            Map<String, PlanClosureDetail> closureDetailMap = planClosureDetails.stream().collect(Collectors.toMap(PlanClosureDetail::getSchemeDetailCode, Function.identity(), (v1, v2) -> v1));
            Map<String, PlanClosure> closureMap = planClosures.stream().collect(Collectors.toMap(PlanClosure::getCloseCode, Function.identity(), (v1, v2) -> v1));

            Map<String, List<OverallPlanProductVo>> productMap = Maps.newHashMap();
            Map<String, List<OverallPlanScopeVo>> scopeMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(overallPlanProductList)) {
                List<OverallPlanProductVo> productVoList = (List<OverallPlanProductVo>) nebulaToolkitService.copyCollectionByWhiteList(overallPlanProductList,
                        OverallPlanProduct.class, OverallPlanProductVo.class, HashSet.class, ArrayList.class);
                productMap = productVoList.stream().collect(Collectors.groupingBy(OverallPlanProductVo::getSchemeDetailCode));
            }
            List<OverallPlanScope> overallPlanScopeList = overPlanScopeRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            if (CollectionUtils.isNotEmpty(overallPlanScopeList)) {
                List<OverallPlanScopeVo> scopeVoList = (List<OverallPlanScopeVo>) nebulaToolkitService.copyCollectionByWhiteList(overallPlanScopeList,
                        OverallPlanScope.class, OverallPlanScopeVo.class, HashSet.class, ArrayList.class);
                scopeMap = scopeVoList.stream().collect(Collectors.groupingBy(OverallPlanScopeVo::getSchemeDetailCode));
            }
            List<OverallPlanDepartment> bearDepartmentList = overPlanDepartmentRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            Map<String, List<OverallPlanDepartmentVo>> bearDepartmentMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(bearDepartmentList)) {
                Collection<OverallPlanDepartmentVo> bearDepartmentVoList = nebulaToolkitService.copyCollectionByWhiteList(bearDepartmentList, OverallPlanDepartment.class,
                        OverallPlanDepartmentVo.class, HashSet.class, ArrayList.class);
                bearDepartmentMap = bearDepartmentVoList.stream().collect(Collectors.groupingBy(x -> x.getSchemeDetailCode()));
            }
            //查询结案、计提、兑付金额
            List<MarketingPlanCaseVo> planCaseVoList = Lists.newArrayList();
            if (OverallPlanSchemeTypeEnum.REGION.getCode().equals(schemeType)) {
                planCaseVoList = marketingPlanCaseService.findAuditAndWithholdingAndCashReleaseDetailCodeList(schemeDetailCodes, BooleanEnum.TRUE.getCapital());
            } else {
                //判断是可以直接承接的
                List<String> bearSchemeDetailCodes = dataList.stream().filter(x -> BooleanEnum.TRUE.getCapital().equals(x.getBearFlag()))
                        .map(OverallPlanCaseReportVo::getSchemeDetailCode).collect(Collectors.toList());
                //判断是没有直接承接的
                List<String> notBearSchemeDetailCodes = dataList.stream().filter(x -> BooleanEnum.FALSE.getCapital().equals(x.getBearFlag()) || ObjectUtils.isEmpty(x.getBearFlag()))
                        .map(OverallPlanCaseReportVo::getSchemeDetailCode).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(bearSchemeDetailCodes)) {
                    List<MarketingPlanCaseVo> bearPlanCaseVoList = marketingPlanCaseService.findAuditAndWithholdingAndCashReleaseDetailCodeList(bearSchemeDetailCodes, BooleanEnum.TRUE.getCapital());
                    if (CollectionUtils.isNotEmpty(bearPlanCaseVoList)) {
                        planCaseVoList.addAll(bearPlanCaseVoList);
                    }
                }
                if (CollectionUtils.isNotEmpty(notBearSchemeDetailCodes)) {
                    List<String> releaseDetailCodes=Lists.newArrayList();
                    List<MarketingPlanCaseVo> notBearPlanCaseVoList = marketingPlanCaseService.findAuditAndWithholdingAndCashReleaseDetailCodeList(notBearSchemeDetailCodes, BooleanEnum.FALSE.getCapital());
                    if (CollectionUtils.isNotEmpty(notBearPlanCaseVoList)) {
                        planCaseVoList.addAll(notBearPlanCaseVoList);
                        releaseDetailCodes.addAll(notBearPlanCaseVoList.stream().map(o->o.getReleaseDetailCodeTwo()).distinct().collect(Collectors.toList()));
                    }
                    List<MarketingPlanCaseVo> regionUnderTakeList = overPlanCaseRepository.findRegionUnderTakeHeadScheme(notBearSchemeDetailCodes,releaseDetailCodes);
                    if (CollectionUtils.isNotEmpty(regionUnderTakeList)) {
                        planCaseVoList.addAll(regionUnderTakeList);
                    }
                }
            }
            Map<String, MarketingPlanCaseVo> planCaseMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(planCaseVoList)) {
                List<String> marketingSchemeDetailCodes = planCaseVoList.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
                Map<String, BigDecimal> withholdingMap = withHoldingService.findBySchemeDetailCodes(marketingSchemeDetailCodes);
                for (MarketingPlanCaseVo planCaseVo : planCaseVoList) {
                    if (withholdingMap.containsKey(planCaseVo.getSchemeDetailCode())) {
                        planCaseVo.setWithholdingAmount(withholdingMap.get(planCaseVo.getSchemeDetailCode()));
                    }
                }
                for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : planCaseVoList.stream().collect(Collectors.groupingBy(MarketingPlanCaseVo::getReleaseDetailCode)).entrySet()) {
                    BigDecimal auditAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getAuditAmount()))
                            .map(MarketingPlanCaseVo::getAuditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal cashAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCashAmount()))
                            .map(MarketingPlanCaseVo::getCashAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal withholdingAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getWithholdingAmount()))
                            .map(MarketingPlanCaseVo::getWithholdingAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal applyAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getApplyAmount()))
                            .map(MarketingPlanCaseVo::getApplyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    planCaseMap.put(entry.getKey(), new MarketingPlanCaseVo() {{
                        this.setAuditAmount(auditAmount);
                        this.setWithholdingAmount(withholdingAmount);
                        this.setCashAmount(cashAmount);
                        this.setApplyAmount(applyAmount);
                    }});
                }
            }
            Map<String, String> cooperateMap = cooperateTypeMap();
            Map<String, String> customerTagMap = customerTagMap();
            Map<String, String> terminalTagMap = terminalTagMap();
            for (OverallPlanCaseReportVo vo : dataList) {
                LocalDate startDate = LocalDate.parse(vo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate endDate = LocalDate.parse(vo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate now = LocalDate.now();
                if (DelFlagStatusEnum.DELETE.getCode().equals(vo.getDelFlag())) {
                    vo.setActStatus(PlanClosureEnum.closed.getCode());
                } else {
                    if (now.isBefore(startDate)) {
                        vo.setActStatus(PlanClosureEnum.not_started.getCode());
                    } else if (now.isAfter(endDate)) {
                        vo.setActStatus(PlanClosureEnum.ended.getCode());
                    } else {
                        vo.setActStatus(PlanClosureEnum.in_progress.getCode());
                    }
                }
                if (productMap.containsKey(vo.getSchemeDetailCode())) {
                    vo.setProductAndItemList(productMap.get(vo.getSchemeDetailCode()));
                }
                if (scopeMap.containsKey(vo.getSchemeDetailCode())) {
                    vo.setScopeList(scopeMap.get(vo.getSchemeDetailCode()));
                }
                if (bearDepartmentMap.containsKey(vo.getSchemeDetailCode())) {
                    vo.setBearDepartmentList(bearDepartmentMap.get(vo.getSchemeDetailCode()));
                    vo.setBearDepartmentStr(vo.getBearDepartmentList().stream().map(OverallPlanDepartmentVo::getDepartmentName).collect(Collectors.joining("、")));
                }
                if (ObjectUtils.isNotEmpty(vo.getCooperateTypeStr())) {
                    List<String> cooperateTypeList = Arrays.asList(vo.getCooperateTypeStr().split(","));
                    vo.setCooperateTypeList(cooperateTypeList);
                    String cooperateStr = cooperateTypeList.stream().map(x -> {
                        return cooperateMap.getOrDefault(x, x);
                    }).collect(Collectors.joining("、"));
                    vo.setCooperateTypeStr(cooperateStr);
                }
                if (ObjectUtils.isNotEmpty(vo.getCustomerTagStr())) {
                    List<String> customerTagList = Arrays.asList(vo.getCustomerTagStr().split(","));
                    vo.setCustomerTagList(customerTagList);
                    String customerTagStr = customerTagList.stream().map(x -> {
                        return customerTagMap.getOrDefault(x, x);
                    }).collect(Collectors.joining("、"));
                    vo.setCustomerTagStr(customerTagStr);
                }
                if (ObjectUtils.isNotEmpty(vo.getTerminalTagStr())) {
                    List<String> terminalTagList = Arrays.asList(vo.getTerminalTagStr().split(","));
                    vo.setTerminalTagList(terminalTagList);
                    String terminalTagStr = terminalTagList.stream().map(x -> {
                        return terminalTagMap.getOrDefault(x, x);
                    }).collect(Collectors.joining("、"));
                    vo.setTerminalTagStr(terminalTagStr);
                }

                if (planCaseMap.containsKey(vo.getSchemeDetailCode())) {
                    MarketingPlanCaseVo planCaseVo = planCaseMap.get(vo.getSchemeDetailCode());
                    vo.setAuditAmount(Optional.ofNullable(planCaseVo.getAuditAmount()).orElse(BigDecimal.ZERO));
                    vo.setWithholdingAmount(Optional.ofNullable(planCaseVo.getWithholdingAmount()).orElse(BigDecimal.ZERO));
                    vo.setCashAmount(Optional.ofNullable(planCaseVo.getCashAmount()).orElse(BigDecimal.ZERO));
                    vo.setAlreadyBearAmount(Optional.ofNullable(planCaseVo.getApplyAmount()).orElse(BigDecimal.ZERO));
                    vo.setNotBearAmount(vo.getApplyAmount().subtract(vo.getAlreadyBearAmount()));
                }
                if (planCaseGuideMap.containsKey(vo.getSchemeDetailCode())) {
                    List<MarketingPlanCase> marketingPlanCases = planCaseGuideMap.get(vo.getSchemeDetailCode());
                    for (MarketingPlanCase marketingPlanCase : marketingPlanCases) {
                        PlanClosureDetail planClosureDetail = closureDetailMap.get(marketingPlanCase.getSchemeDetailCode());
                        if (null != planClosureDetail) {
                            String closeCode = planClosureDetail.getCloseCode();
                            PlanClosure planClosure = closureMap.get(closeCode);
                            if (null != planClosure) {
                                vo.setHeadCloseProcessStatus(ProcessStatusEnum.PASS.getDictCode());
                            }
                        }
                    }

                }
            }
            resultData.setRecords(dataList);
        }
        resultData.setCurrent(data.getCurrent());
        resultData.setSize(data.getSize());
        resultData.setTotal(data.getTotal());
        return resultData;
    }


    /**
     * 查询总部承接明细
     *
     * @param pageable
     * @param vo
     * @return
     */
    public Page<OverallPlanCaseVo> findUnderTakeSchemeDetailList(Pageable pageable, OverallPlanCaseVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<OverallPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        List<OverallPlanCaseVo> headCaseVoList = overPlanCaseRepository.findHeadSchemeListBySchemeType(vo);
        if (CollectionUtils.isEmpty(headCaseVoList)) {
            return null;
        }
        List<String> regionUnderTakeList = headCaseVoList.stream().filter(x -> BooleanEnum.FALSE.getCapital().equals(x.getBearFlag()))
                .map(x -> x.getSchemeDetailCode()).collect(Collectors.toList());
        List<String> marketingUnderTakeList = headCaseVoList.stream().filter(x -> BooleanEnum.TRUE.getCapital().equals(x.getBearFlag()))
                .map(x -> x.getSchemeDetailCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(regionUnderTakeList)) {
            regionUnderTakeList = Lists.newArrayList("");
        }
        if (CollectionUtils.isEmpty(marketingUnderTakeList)) {
            marketingUnderTakeList = Lists.newArrayList("");
        }
        return overPlanCaseRepository.findUnderTakeSchemeDetailList(page, regionUnderTakeList, marketingUnderTakeList);
    }


    /**
     * 查询总部的承接明细-大区指引
     *
     * @param pageable
     * @param schemeDetailCode
     * @return
     */
    public Page<OverallPlanCaseVo> findRegionOverallPlanCase(Pageable pageable, String schemeDetailCode) {
        Page<OverallPlanCaseVo> data = overallPlanCaseService.findRegionOverallPlanCase(pageable, schemeDetailCode);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            for (OverallPlanCaseVo record : data.getRecords()) {
                LocalDate startDate = LocalDate.parse(record.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate endDate = LocalDate.parse(record.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate now = LocalDate.now();
                if (DelFlagStatusEnum.DELETE.getCode().equals(record.getDelFlag())) {
                    record.setActStatus(PlanClosureEnum.closed.getCode());
                } else {
                    if (now.isBefore(startDate)) {
                        record.setActStatus(PlanClosureEnum.not_started.getCode());
                    } else if (now.isAfter(endDate)) {
                        record.setActStatus(PlanClosureEnum.ended.getCode());
                    } else {
                        record.setActStatus(PlanClosureEnum.in_progress.getCode());
                    }
                }
            }
        }
        return data;
    }

    /**
     * 查询大区承接明细
     *
     * @param pageable
     * @param schemeDetailCode
     * @return
     */
    public Page<MarketingPlanCaseVo> findRegionMarketingPlanCase(Pageable pageable, String schemeDetailCode) {
        Page<MarketingPlanCaseVo> data = marketingPlanCaseService.findRegionMarketingPlanCase(pageable, schemeDetailCode);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            for (MarketingPlanCaseVo record : data.getRecords()) {
                LocalDate startDate = LocalDate.parse(record.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate endDate = LocalDate.parse(record.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate now = LocalDate.now();
                if (DelFlagStatusEnum.DELETE.getCode().equals(record.getDelFlag())) {
                    record.setActStatus(PlanClosureEnum.closed.getCode());
                } else {
                    if (now.isBefore(startDate)) {
                        record.setActStatus(PlanClosureEnum.not_started.getCode());
                    } else if (now.isAfter(endDate)) {
                        record.setActStatus(PlanClosureEnum.ended.getCode());
                    } else {
                        record.setActStatus(PlanClosureEnum.in_progress.getCode());
                    }
                }
            }
        }
        return data;
    }


    /**
     * 总部指引追踪-总览
     *
     * @param pageable
     * @param vo
     * @return
     */
    public Page<HeadOverallSummaryReportVo> findSummaryList(@PageableDefault(50) Pageable pageable, HeadOverallSummaryReportVo vo) {
        Page<HeadOverallSummaryReportVo> data = overallPlanService.findSummaryList(pageable, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeCodes = data.getRecords().stream().map(HeadOverallSummaryReportVo::getSchemeCode)
                    .collect(Collectors.toList());
            List<OverallPlanCaseVo> caseVoList = overallPlanCaseService.findCasePartFiledListBySchemeCodes(schemeCodes);
            if (CollectionUtils.isNotEmpty(caseVoList)) {
                Map<String, List<OverallPlanCaseVo>> caseMap = caseVoList.stream().collect(Collectors.groupingBy(OverallPlanCaseVo::getSchemeCode));
                List<String> bearSchemeDetailCodeList = Lists.newArrayList();
                List<String> notBearSchemeDetailCodeList = Lists.newArrayList();
                //查询承接、结案、计提、兑付金额
                List<MarketingPlanCaseVo> planCaseVoList = Lists.newArrayList();
                for (HeadOverallSummaryReportVo record : data.getRecords()) {
                    if (caseMap.containsKey(record.getSchemeCode())) {
                        List<OverallPlanCaseVo> dataList = caseMap.get(record.getSchemeCode());
                        //申请金额
                        BigDecimal applyAmount = dataList.stream().map(x -> Optional.ofNullable(x.getApplyAmount()).orElse(BigDecimal.ZERO))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        //关闭金额
                        BigDecimal closeAmount = dataList.stream().filter(x -> DelFlagStatusEnum.DELETE.getCode().equals(x.getDelFlag()))
                                .map(x -> Optional.ofNullable(x.getApplyAmount()).orElse(BigDecimal.ZERO))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        List<String> bearSchemeDetailCodes = dataList.stream().filter(x -> BooleanEnum.TRUE.getCapital().equals(x.getBearFlag()))
                                .map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
                        List<String> notBearSchemeDetailCodes = dataList.stream().filter(x -> BooleanEnum.FALSE.getCapital().equals(x.getBearFlag()))
                                .map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(bearSchemeDetailCodes)) {
                            bearSchemeDetailCodeList.addAll(bearSchemeDetailCodes);
                        }
                        if (CollectionUtils.isNotEmpty(notBearSchemeDetailCodes)) {
                            notBearSchemeDetailCodeList.addAll(notBearSchemeDetailCodes);
                        }
                        record.setApplyAmount(applyAmount);
                        record.setCloseAmount(closeAmount);
                    }
                }
                if (CollectionUtils.isNotEmpty(bearSchemeDetailCodeList)) {
                    List<MarketingPlanCaseVo> bearPlanCaseVoList = marketingPlanCaseService.findAuditAndWithholdingAndCashReleaseDetailCodeList(bearSchemeDetailCodeList, BooleanEnum.TRUE.getCapital());
                    if (CollectionUtils.isNotEmpty(bearPlanCaseVoList)) {
                        planCaseVoList.addAll(bearPlanCaseVoList);
                    }
                }
                if (CollectionUtils.isNotEmpty(notBearSchemeDetailCodeList)) {
                    List<MarketingPlanCaseVo> notBearPlanCaseVoList = marketingPlanCaseService.findAuditAndWithholdingAndCashReleaseDetailCodeList(notBearSchemeDetailCodeList, BooleanEnum.FALSE.getCapital());
                    if (CollectionUtils.isNotEmpty(notBearPlanCaseVoList)) {
                        planCaseVoList.addAll(notBearPlanCaseVoList);
                    }
                }
                Map<String, MarketingPlanCaseVo> planCaseMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(planCaseVoList)) {
                    List<String> marketingSchemeDetailCodes = planCaseVoList.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
                    Map<String, BigDecimal> withholdingMap = withHoldingService.findBySchemeDetailCodes(marketingSchemeDetailCodes);
                    for (MarketingPlanCaseVo planCaseVo : planCaseVoList) {
                        if (withholdingMap.containsKey(planCaseVo.getSchemeDetailCode())) {
                            planCaseVo.setWithholdingAmount(withholdingMap.get(planCaseVo.getSchemeDetailCode()));
                        }
                    }
                    for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : planCaseVoList.stream().collect(Collectors.groupingBy(MarketingPlanCaseVo::getReleaseCode)).entrySet()) {
                        BigDecimal auditAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getAuditAmount()))
                                .map(MarketingPlanCaseVo::getAuditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal cashAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCashAmount()))
                                .map(MarketingPlanCaseVo::getCashAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal withholdingAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getWithholdingAmount()))
                                .map(MarketingPlanCaseVo::getWithholdingAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal applyAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getApplyAmount()))
                                .map(MarketingPlanCaseVo::getApplyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        planCaseMap.put(entry.getKey(), new MarketingPlanCaseVo() {{
                            this.setAuditAmount(auditAmount);
                            this.setWithholdingAmount(withholdingAmount);
                            this.setCashAmount(cashAmount);
                            this.setApplyAmount(applyAmount);
                        }});
                    }
                    for (HeadOverallSummaryReportVo record : data.getRecords()) {
                        if (planCaseMap.containsKey(record.getSchemeCode())) {
                            MarketingPlanCaseVo planCaseVo = planCaseMap.get(record.getSchemeCode());
                            record.setAlreadyBearAmount(Optional.ofNullable(planCaseVo.getApplyAmount()).orElse(BigDecimal.ZERO));
                            record.setNotBearAmount(record.getApplyAmount().subtract(record.getAlreadyBearAmount()));
                            record.setWithholdingAmount(planCaseVo.getWithholdingAmount());
                            record.setAuditAmount(planCaseVo.getAuditAmount());
                            record.setCashAmount(planCaseVo.getCashAmount());
                        }
                    }
                }
            }
        }
        return data;
    }


    //合作类型
    private final static ThreadLocal<Map<String, String>> cooperateTypeThreadLocal = new ThreadLocal<>();
    //客户标签
    private final static ThreadLocal<Map<String, String>> customerTagThreadLocal = new ThreadLocal<>();
    //终端标签
    private final static ThreadLocal<Map<String, String>> terminalTagThreadLocal = new ThreadLocal<>();


    /**
     * 合作类型标签
     *
     * @return
     */
    private Map<String, String> cooperateTypeMap() {
        Map<String, String> map = cooperateTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_COOPERATE_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            cooperateTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 客户标签
     *
     * @return
     */
    private Map<String, String> customerTagMap() {
        Map<String, String> map = customerTagThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.CUSTOMER_TAG);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            customerTagThreadLocal.set(map);
        }
        return map;
    }


    private Map<String, String> terminalTagMap() {
        Map<String, String> map = terminalTagThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TERMINAL_TAG);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            terminalTagThreadLocal.set(map);
        }
        return map;
    }
}
