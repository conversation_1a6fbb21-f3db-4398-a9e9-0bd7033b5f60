package com.biz.crm.tpm.admin.web.exports.costtypecategory.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.common.utils.Objects;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.costtypecategory.mapper.CostTypeCategoryExportsMapper;
import com.biz.crm.tpm.admin.web.exports.costtypecategory.model.CostTypeCategoryExportsDto;
import com.biz.crm.tpm.admin.web.exports.costtypecategory.model.CostTypeCategoryExportsVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @describe:活动大类导出
 * @createTime 2022年06月08日 11:24:00
 */
@Component
public class CostTypeCategoryExportsProcess implements ExportProcess<CostTypeCategoryExportsVo> {


  /**
   * 财务费用类型字典编码
   */
  private static final String FINANCIAL_FEE_TYPE = "financial_fee_type";
  /**
   * 业务费用分组字典编码
   */
  private static final String BUSINESS_FEE_TYPE = "business_fee_type";


  @Autowired
  private CostTypeCategoryExportsMapper costTypeCategoryExportsMapper;
  @Autowired
  private DictDataVoService dictDataVoService;


  @Override
  public Integer getTotal(Map<String, Object> params) {
    CostTypeCategoryExportsDto dto = this.findDataDto(params);
    dto.setTenantCode(TenantUtils.getTenantCode());
    return costTypeCategoryExportsMapper.getExportTotal(dto);
  }

  @Override
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    CostTypeCategoryExportsDto dto = this.findDataDto(params);
    dto.setOffset(this.getPageSize() * vo.getPageNo());
    dto.setLimit(vo.getPageSize());
    dto.setTenantCode(TenantUtils.getTenantCode());
    List<CostTypeCategoryExportsVo> data = costTypeCategoryExportsMapper.findData(dto);
    //调整
    adjustData(data);
    return toJSONArray(data);
  }

  @Override
  public String getBusinessCode() {
    return "TPM_COST_TYPE_CATEGORY_EXPORTS";
  }

  @Override
  public String getBusinessName() {
    return "TPM活动大类导出";
  }

  /**
   * 设置参数
   * @param params
   * @return
   */
  private CostTypeCategoryExportsDto findDataDto(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    
    CostTypeCategoryExportsDto dto =new CostTypeCategoryExportsDto();
    final Object categoryCode = params.get("categoryCode");
    if (Objects.nonNull(categoryCode)){
      dto.setCategoryCode(categoryCode.toString());
    }
    final Object categoryName = params.get("categoryName");
    if (Objects.nonNull(categoryName)){
      dto.setCategoryName(categoryName.toString());
    }
    final Object financialExpensesType = params.get("financialExpensesType");
    if (Objects.nonNull(financialExpensesType)){
      dto.setFinancialExpensesType(financialExpensesType.toString());
    }
    final Object businessExpensesType = params.get("businessExpensesType");
    if (Objects.nonNull(businessExpensesType)){
      dto.setBusinessExpensesType(businessExpensesType.toString());
    }
    final Object budgetSubjectsName = params.get("budgetSubjectsName");
    if (Objects.nonNull(budgetSubjectsName)){
      dto.setBudgetSubjectsName(budgetSubjectsName.toString());
    }
    final Object budgetSubjectsCode = params.get("budgetSubjectsCode");
    if (Objects.nonNull(budgetSubjectsCode)){
      dto.setBudgetSubjectsCode(budgetSubjectsCode.toString());
    }
    final Object enableStatus = params.get("enableStatus");
    if (Objects.nonNull(enableStatus)){
      dto.setEnableStatus(enableStatus.toString());
    }
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    dto.setTenantCode(TenantUtils.getTenantCode());
    return dto;
  }

  /**
   * 调整数据
   * @param data
   */
  private void adjustData(List<CostTypeCategoryExportsVo> data) {
    if (CollectionUtils.isEmpty(data)){
      return;
    }
    Map<String, List<DictDataVo>> mapDict = dictDataVoService.findByDictTypeCodeList(Lists.newArrayList(FINANCIAL_FEE_TYPE, BUSINESS_FEE_TYPE));
    for (CostTypeCategoryExportsVo vo : data) {
      vo.setFinancialExpensesType(this.findDictValue(mapDict,FINANCIAL_FEE_TYPE,vo.getFinancialExpensesType()));
      vo.setBusinessExpensesType(this.findDictValue(mapDict,BUSINESS_FEE_TYPE,vo.getBusinessExpensesType()));
      vo.setEnableStatus(EnableStatusEnum.getDesc(vo.getEnableStatus()));
    }
  }

  /**
   * 获取字典值
   *
   * @param mapDict
   * @param dictTypeCode
   * @param code
   * @return
   */
  private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
    if (java.util.Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
      return StringUtils.EMPTY;
    }
    final List<DictDataVo> vos = mapDict.get(dictTypeCode);
    if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
      return StringUtils.EMPTY;
    }
    final Optional<String> first =
        vos.stream()
            .filter(a -> a.getDictCode().equals(code))
            .map(DictDataVo::getDictValue)
            .findFirst();
    return first.orElse(StringUtils.EMPTY);
  }
}
