package com.biz.crm.tpm.admin.web.exports.ordinaryactivity.mapper;

import com.biz.crm.tpm.admin.web.exports.ordinaryactivity.model.OrdinaryActivityExportsDto;
import com.biz.crm.tpm.admin.web.exports.ordinaryactivity.model.OrdinaryActivityExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:活动申请导出mapper
 * @createTime 2022年07月01日 15:17:00
 */
public interface OrdinaryActivityExportsMapper {
  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") OrdinaryActivityExportsDto dto);
  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<OrdinaryActivityExportsVo> findData(@Param("dto") OrdinaryActivityExportsDto dto);
}
