package com.biz.crm.tpm.admin.web.exports.costbudget.model;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 参数传递dto：费用预算;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-8
 */
@ApiModel(value = "CostBudget", description = "费用预算")
@Getter
@Setter
public class CostBudgetExportsDto extends TenantDto implements Serializable, Cloneable {

  /**
   * 租户编号
   */
  @ApiModelProperty(name = "tenantCode", notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /**
   * 渠道编码
   */
  @ApiModelProperty(name = "channelCode", notes = "渠道编码", value = "渠道编码")
  private String channelCode;
  /**
   * 渠道名称
   */
  @ApiModelProperty(name = "channelName", notes = "渠道名称", value = "渠道名称")
  private String channelName;

  /**
   * 预算科目编码
   */
  @ApiModelProperty(name = "budgetSubjectCode", notes = "预算科目编码", value = "预算科目编码")
  private String budgetSubjectCode;
  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "budgetSubjectName", notes = "预算科目名称", value = "预算科目名称")
  private String budgetSubjectName;
  /**
   * 编码
   */
  @ApiModelProperty(name = "code", notes = "编码", value = "编码")
  private String code;
  /**
   * 客户名称
   */
  @ApiModelProperty(name = "customerName", notes = "客户名称", value = "客户名称")
  private String customerName;
  /**
   * 组织名称
   */
  @ApiModelProperty(name = "orgName", notes = "组织名称", value = "组织名称")
  private String orgName;
  /**
   * 费用预算类型
   */
  @ApiModelProperty(name = "type", notes = "费用预算类型", value = "费用预算类型")
  private String type;

  /**
   * 启用状态
   */
  @ApiModelProperty(name = "enableStatus", notes = "费用预算类型", value = "费用预算类型")
  private String enableStatus;

  /**
   * 偏移量
   */
  @ApiModelProperty(name = "offset", notes = "偏移量", value = "偏移量")
  private Integer offset;

  /**
   * limit
   */
  @ApiModelProperty(name = "limit", notes = "limit", value = "limit")
  private Integer limit;

}