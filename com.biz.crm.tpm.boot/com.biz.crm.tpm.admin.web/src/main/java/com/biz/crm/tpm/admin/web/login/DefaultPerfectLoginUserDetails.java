package com.biz.crm.tpm.admin.web.login;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.mdm.business.org.sdk.service.OrgPositionVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.user.sdk.service.UserPositionVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserPositionVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * CRM体系的系统中，都可以使用的用户登录策略的公共方法
 *
 * <AUTHOR>
 */
@Component
public abstract class DefaultPerfectLoginUserDetails {

  /**
   * 完善登录信息中的用户基本信息
   *
   * @param currentUser 查询出的当前用户
   * @param loginUserDetails 登录用户信息
   */
  @Autowired
  private UserPositionVoService userPositionVoService;

  @Autowired
  private OrgPositionVoService orgPositionVoService;

  @Autowired
  private PositionVoService positionVoService;

  /**
   * 完善登录表单信息中的用户基本信息
   *
   * @param currentUser      查询出的当前用户
   * @param loginFormDetails 登录表单用户信息
   */
  protected void perfectLoginFormDetails(UserVo currentUser, FacturerUserDetails loginFormDetails) {
    // u-企业用户，c-客户用户，terminal-终端用户，customer_employee-经销商员工用户
    loginFormDetails.setIdentityType(StringUtils.stripToEmpty(currentUser.getUserType()).trim());
    loginFormDetails.setTenantCode(StringUtils.stripToEmpty(currentUser.getTenantCode()).trim());
    loginFormDetails.setUsername(StringUtils.stripToEmpty(currentUser.getUserName()).trim());
    loginFormDetails.setPhone(StringUtils.stripToEmpty(currentUser.getUserPhone()).trim());
    loginFormDetails.setRealName(StringUtils.stripToEmpty(currentUser.getFullName()).trim());
    loginFormDetails.setAccount(StringUtils.stripToEmpty(currentUser.getUserName()).trim());
  }

  /**
   * 完善登录信息中的用户基本信息
   *
   * @param currentUser      查询出的当前用户
   * @param loginUserDetails 登录用户信息
   */
  protected void perfectLoginUserDetails(UserVo currentUser, FacturerUserDetails loginUserDetails) {
    loginUserDetails.setIdentityType(StringUtils.stripToEmpty(currentUser.getUserType()).trim());
    loginUserDetails.setTenantCode(StringUtils.stripToEmpty(currentUser.getTenantCode()).trim());
    loginUserDetails.setUsername(StringUtils.stripToEmpty(currentUser.getUserName()).trim());
    loginUserDetails.setPhone(StringUtils.stripToEmpty(currentUser.getUserPhone()).trim());
    loginUserDetails.setRealName(StringUtils.stripToEmpty(currentUser.getFullName()).trim());
    loginUserDetails.setAccount(StringUtils.stripToEmpty(currentUser.getUserName()).trim());
  }

  /**
   * 完善登录信息中的岗位组织等信息
   * <p>
   * 一、获取当前职位，优先级顺序：
   * 1、第一优先获取用户职位关系表“是否当前职位”为是的职位
   * 2、第二优先获取用户职位关系表“是否主职位”为是的职位
   * 二、获取当前职位对应组织
   *
   * @param loginUserDetails 登录用户信息
   */
  protected void perfectLoginPostAndOrg(FacturerUserDetails loginUserDetails) {
    List<UserPositionVo> userPositionVos = this.userPositionVoService.findByUserName(loginUserDetails.getTenantCode(), loginUserDetails.getAccount());
    if (CollectionUtils.isEmpty(userPositionVos)) {
      return;
    }
    // 一、1、
    Optional<UserPositionVo> currentFlagVo = userPositionVos.stream()
        .filter(r -> Boolean.TRUE.equals(r.getCurrentFlag()))
        .findFirst();
    if (currentFlagVo.isPresent()) {
      UserPositionVo userPositionVo = currentFlagVo.get();
      loginUserDetails.setPostCode(userPositionVo.getPositionCode());
    } else {
      // 一、2、
      Optional<UserPositionVo> primaryFlagVo = userPositionVos.stream()
          .filter(r -> Boolean.TRUE.equals(r.getPrimaryFlag()))
          .findFirst();
      if (primaryFlagVo.isPresent()) {
        UserPositionVo userPositionVo = primaryFlagVo.get();
        loginUserDetails.setPostCode(userPositionVo.getPositionCode());
      }
    }
    // 完善职位信息
    if (StringUtils.isBlank(loginUserDetails.getPostCode())) {
      return;
    }
    // 当前用户所有岗位编码集合
    loginUserDetails.setPostCodes(userPositionVos.stream().map(UserPositionVo::getPositionCode).distinct().collect(Collectors.toList()));
    // 当前用户所有下级岗位编码集合
    List<PositionVo> childrenPositionList = this.positionVoService.findAllChildrenByCode(loginUserDetails.getPostCode());
    loginUserDetails.setChildrenPostCodes(Optional.ofNullable(childrenPositionList).orElse(Lists.newLinkedList()).stream().map(PositionVo::getPositionCode).distinct().collect(Collectors.toList()));
    // 下面是查当前用户当前岗位对应角色及组织
    List<PositionVo> positionVoList = this.positionVoService.findByIdsOrCodes(Lists.newLinkedList(), Lists.newArrayList(loginUserDetails.getPostCode()));
    if (CollectionUtils.isEmpty(positionVoList)) {
      return;
    }
    PositionVo positionVo = positionVoList.get(0);
    loginUserDetails.setPostName(positionVo.getPositionName());
    if (CollectionUtils.isNotEmpty(positionVo.getRoleList())) {
      loginUserDetails.setRoleCodes(positionVo.getRoleList().toArray(new String[]{}));
    }
    loginUserDetails.setOrgCode(positionVo.getOrgCode());
    loginUserDetails.setOrgName(positionVo.getOrgName());
  }
}
