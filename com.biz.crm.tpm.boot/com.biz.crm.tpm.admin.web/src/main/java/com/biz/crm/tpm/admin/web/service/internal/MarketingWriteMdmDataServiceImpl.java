package com.biz.crm.tpm.admin.web.service.internal;

import com.biz.crm.business.common.base.service.BusinessWriteMdmDataStrategy;
import com.biz.crm.common.ie.local.entity.ImportTemplate;
import com.biz.crm.common.ie.local.entity.ImportTemplateDetail;
import com.biz.crm.common.ie.local.service.ImportTemplateService;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/24 22:37
 */
@Service
@Slf4j
public class MarketingWriteMdmDataServiceImpl {


    @Resource
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private ImportTemplateService importTemplateService;

    @Resource
    private List<BusinessWriteMdmDataStrategy> strategyList;


    public void writeMdmDataToImportTemplate() {
        List<DictDataVo> dictDataVos = dictDataVoService.findTreeByDictTypeCode(DictConstant.TPM_IMPORT_MDM_DATA_CONFIG);
        for (DictDataVo dictDataVo : dictDataVos) {
            List<DictDataVo> childrenList = dictDataVo.getChildren();
            Map<String, Integer> sheetMap = childrenList.stream().collect(Collectors.toMap(x -> x.getDictValue(), x -> Integer.valueOf(x.getDictDesc())));
            ImportTemplate importTemplate = importTemplateService.findDetailByBusinessCode(dictDataVo.getDictCode());
            for (ImportTemplateDetail detail : importTemplate.getList()) {
                for (BusinessWriteMdmDataStrategy strategy : strategyList) {
                    String fileCode = strategy.writeMdmDataToTemplate(detail.getFileCode(), sheetMap);
                    detail.setFileCode(fileCode);
                    detail.setFileCode(fileCode);
                    detail.getFile().setFileCode(fileCode);
                }
            }
            importTemplateService.update(importTemplate);
        }
    }
}
