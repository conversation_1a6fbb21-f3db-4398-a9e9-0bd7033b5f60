package com.biz.crm.tpm.admin.web.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.CryptorStrategy;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.service.CostControlLoginService;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.tpm.admin.web.vo.CostControlResult;
import com.google.common.collect.Maps;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/4 20:51
 */
@Service
@Slf4j
public class DefaultCostControlServiceImpl implements CostControlLoginService {

    @Resource
    private RedisService redisService;

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private CryptorStrategy cryptorStrategy;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    private static final String TPM_COST_CONTROL_LOGIN_TOKEN = "tpm:cost_control:log_token";

    @Override
    public String getToken() {
        Object tokenObject = redisService.get(TPM_COST_CONTROL_LOGIN_TOKEN);
        if (Objects.nonNull(tokenObject)) {
            return tokenObject.toString();
        }
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_FK_ACCOUNT);
        String url = urlAddressVo.getUrl();
        String interfaceAddress = RyConstant.FK_TOKEN_INTERFACE_ADDRESS;
        String loginName = urlAddressVo.getAccessKey();
        String password = urlAddressVo.getSecretKey();
        String grantType = urlAddressVo.getAccessId();
        Map<String, Object> map = Maps.newHashMap();
        map.put("client_id", loginName);
        map.put("client_secret", password);
        map.put("grant_type", grantType);
        ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(JSONObject.toJSONString(map), urlAddressVo);
        logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf("/") + 1));
        logDetailDto.setRequestUri(interfaceAddress);
        logDetailDto.setMethodMsg("登录费控系统");
        externalLogVoService.addOrUpdateLog(logDetailDto, true);
        Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, null, null, MediaType.MULTIPART_FORM_DATA_VALUE,
                5, 120, map);
        ExternalLogUtil.buildLogResult(logDetailDto, result);
        CostControlResult data = null;
        String token = null;
        try {
            data = this.valAndReturnJson(result);
            Assert.notNull(data, "费控返回数据结构异常");
            token = data.getAccess_token();
            Assert.hasLength(token, "费控返回数据结构异常,无[access_token]字段或未空!");
            Integer tokenExpires = data.getExpires_in();
            Assert.notNull(tokenExpires, "费控返回数据结构异常,无[expires_in]字段或未空!");
            tokenExpires = tokenExpires - 300;
            //延迟5分钟 防止各种延迟  网络延迟等
            if (tokenExpires > 0) {
                redisService.set(TPM_COST_CONTROL_LOGIN_TOKEN, token, tokenExpires);
            }
            logDetailDto.setStatus(ExternalLogGlobalConstants.S);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            logDetailDto.setTipMsg("费控返回数据结构异常:" + e.getMessage());
            logDetailDto.setStatus(ExternalLogGlobalConstants.E);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
            Assert.isTrue(false, "费控返回数据结构异常:" + e.getMessage());
        }
        Assert.hasLength(token, "登录费控时未知异常,请联系管理员!");
        return token;
    }

    /**
     * 验证构建EHC返回信息
     *
     * @param result
     * @return
     */
    private CostControlResult valAndReturnJson(Result<String> result) {
        Assert.isTrue(result.isSuccess(), "费控返回提示:" + result.getMessage());
        Assert.hasLength(result.getResult(), "费控返回信息为空:" + result.getMessage());
        CostControlResult costControlResult = JSONObject.parseObject(result.getResult(), CostControlResult.class);
        Assert.notNull(costControlResult, "费控返回明细为空:" + costControlResult);
        return costControlResult;
    }
}
