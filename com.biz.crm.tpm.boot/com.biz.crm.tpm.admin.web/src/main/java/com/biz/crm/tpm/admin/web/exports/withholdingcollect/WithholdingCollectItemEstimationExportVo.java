package com.biz.crm.tpm.admin.web.exports.withholdingcollect;

import com.biz.crm.tpm.admin.web.exports.regioncollect.model.CustomerGainsAndLossesExportBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "RegionCollectItemEstimationVo", description = "计提汇总-品项测算")
public class WithholdingCollectItemEstimationExportVo extends CustomerGainsAndLossesExportBaseVo {

    @ApiModelProperty("品项名称")
    private String itemName;

}
