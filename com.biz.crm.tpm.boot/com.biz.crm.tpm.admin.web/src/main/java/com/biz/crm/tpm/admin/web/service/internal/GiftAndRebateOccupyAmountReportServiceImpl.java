package com.biz.crm.tpm.admin.web.service.internal;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.eunm.AuditStatusEnum;
import com.biz.crm.business.common.base.eunm.CashStatusEnum;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.renyang.ReplenishmentPoolDetailVoService;
import com.biz.crm.dms.business.promotion.sdk.service.DmsPromotionSalePolicyVoService;
import com.biz.crm.tpm.business.activities.giftrebatereport.dto.GiftAndRebateOccupyAmountReportDto;
import com.biz.crm.tpm.business.activities.giftrebatereport.entity.GiftAndRebateOccupyAmountReport;
import com.biz.crm.tpm.business.activities.giftrebatereport.repository.GiftAndRebateOccupyAmountReportRepository;
import com.biz.crm.tpm.business.activities.giftrebatereport.service.GiftAndRebateOccupyAmountReportService;
import com.biz.crm.tpm.business.activities.giftrebatereport.vo.GiftAndRebateOccupyAmountReportVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectSchemeVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.local.entity.WithHolding;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.WithHoldingTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.DeliveryReplenishmentPoolDetailService;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.service.FormulaCalService;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.math3.analysis.function.Cos;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 搭赠及返利实时金额占用报表Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Service
public class GiftAndRebateOccupyAmountReportServiceImpl implements GiftAndRebateOccupyAmountReportService {

    @Autowired
    private GiftAndRebateOccupyAmountReportRepository repository;

    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    @Autowired
    private MarketingPlanCaseRepository marketingPlanCaseRepository;

    @Autowired(required = false)
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;

    @Autowired
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;

    @Autowired
    private WithHoldingService withHoldingService;

    @Autowired
    private MarketingPlanProductRepository marketingPlanProductRepository;

    @Autowired(required = false)
    private ReplenishmentPoolDetailVoService replenishmentPoolDetailVoService;

    @Autowired(required = false)
    private CostTypeDetailVoService costTypeDetailVoService;

    @Autowired
    private DmsPromotionSalePolicyVoService dmsPromotionSalePolicyVoService;

    @Autowired(required = false)
    private FormulaCalService formulaCalService;

    @Override
    public Page<GiftAndRebateOccupyAmountReportVo> findByConditions(Pageable pageable, GiftAndRebateOccupyAmountReportDto dto) {
        if (dto == null) {
            dto = new GiftAndRebateOccupyAmountReportDto();
        }
        return repository.findByConditions(pageable, dto);
    }

    @Override
    public List<GiftAndRebateOccupyAmountReportVo> findListByConditions(GiftAndRebateOccupyAmountReportDto dto) {
        if (dto == null) {
            dto = new GiftAndRebateOccupyAmountReportDto();
        }
        return repository.findListByConditions(dto);
    }

    @Override
    public GiftAndRebateOccupyAmountReportVo findById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        
        GiftAndRebateOccupyAmountReport entity = repository.findByIdAndTenantCode(id);
        if (entity == null) {
            return null;
        }
        
        return nebulaToolkitService.copyObjectByWhiteList(
                entity, GiftAndRebateOccupyAmountReportVo.class, HashSet.class, ArrayList.class);
    }


    @Override
    public List<GiftAndRebateOccupyAmountReportVo> findBySchemeDetailCode(String schemeDetailCode) {
        if (StringUtils.isBlank(schemeDetailCode)) {
            return new ArrayList<>();
        }
        
        List<GiftAndRebateOccupyAmountReport> entities = repository.findBySchemeDetailCode(schemeDetailCode);
        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        }
        
        return entities.stream()
                .map(entity -> nebulaToolkitService.copyObjectByWhiteList(
                        entity, GiftAndRebateOccupyAmountReportVo.class, HashSet.class, ArrayList.class))
                .collect(Collectors.toList());
    }



    @Override
    public GiftAndRebateOccupyAmountReportVo create(GiftAndRebateOccupyAmountReportVo vo) {
        if (vo == null) {
            throw new IllegalArgumentException("报表数据不能为空");
        }
        
        GiftAndRebateOccupyAmountReport entity = nebulaToolkitService.copyObjectByWhiteList(
                vo, GiftAndRebateOccupyAmountReport.class, HashSet.class, ArrayList.class);
        
        entity.setTenantCode(TenantUtils.getTenantCode());
        entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        
        repository.save(entity);
        
        return nebulaToolkitService.copyObjectByWhiteList(
                entity, GiftAndRebateOccupyAmountReportVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public GiftAndRebateOccupyAmountReportVo update(GiftAndRebateOccupyAmountReportVo vo) {
        if (vo == null || StringUtils.isBlank(vo.getId())) {
            throw new IllegalArgumentException("报表数据或ID不能为空");
        }
        
        GiftAndRebateOccupyAmountReport existingEntity = repository.findByIdAndTenantCode(vo.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("报表数据不存在");
        }
        
        GiftAndRebateOccupyAmountReport entity = nebulaToolkitService.copyObjectByWhiteList(
                vo, GiftAndRebateOccupyAmountReport.class, HashSet.class, ArrayList.class);
        
        entity.setTenantCode(TenantUtils.getTenantCode());
        
        repository.updateById(entity);
        
        return nebulaToolkitService.copyObjectByWhiteList(
                entity, GiftAndRebateOccupyAmountReportVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<GiftAndRebateOccupyAmountReportVo> batchCreate(List<GiftAndRebateOccupyAmountReportVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return new ArrayList<>();
        }
        
        List<GiftAndRebateOccupyAmountReport> entities = voList.stream()
                .map(vo -> {
                    GiftAndRebateOccupyAmountReport entity = nebulaToolkitService.copyObjectByWhiteList(
                            vo, GiftAndRebateOccupyAmountReport.class, HashSet.class, ArrayList.class);
                    entity.setTenantCode(TenantUtils.getTenantCode());
                    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    return entity;
                })
                .collect(Collectors.toList());
        
        repository.saveBatch(entities);
        
        return entities.stream()
                .map(entity -> nebulaToolkitService.copyObjectByWhiteList(
                        entity, GiftAndRebateOccupyAmountReportVo.class, HashSet.class, ArrayList.class))
                .collect(Collectors.toList());
    }

    @Override
    public void refreshReportData(GiftAndRebateOccupyAmountReportDto dto) {
        // TODO: 实现报表数据刷新逻辑
        // 根据业务规则重新计算和更新报表数据
        log.info("刷新搭赠及返利实时金额占用报表数据，条件：{}", dto);
    }

    @Override
    public List<GiftAndRebateOccupyAmountReportVo> exportData(GiftAndRebateOccupyAmountReportDto dto) {
        return findListByConditions(dto);
    }


    @Override
    public void calculateOccupyAmount(GiftAndRebateOccupyAmountReportDto dto) {
        log.info("开始计算占用金额");

        try {
            LocalDate today = LocalDate.now();
            // 判断当天是否为月第一天，如果是，则获取上月的开始和结束日期
            if (today.getDayOfMonth() == 1) {
                today = today.minusMonths(1);
            }
            // 获取当前年月
            String currentYearMonth = today.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            LocalDate monthStart = today.withDayOfMonth(1);
            LocalDate monthEnd = today.withDayOfMonth(today.lengthOfMonth());
            log.info("currentYearMonth-monthStart-monthEnd: {}-{}-{}", currentYearMonth, monthStart, monthEnd);
            // 第一步：查询本月审批通过的且当天与活动结束时间大于1天的tpm_marketing_plan_case营销方案明细
            List<MarketingPlanCaseVo> approvedCases = marketingPlanCaseRepository.queryCurMonthApprovedMarketingPlanCases(monthStart.toString(), monthEnd.toString());
            if (CollectionUtils.isEmpty(approvedCases)) {
                log.info("未找到符合条件的营销方案明细");
                return;
            }
            // 删除本月历史数据
            repository.deleteByYearMonth(currentYearMonth);
            // 第二步：根据第一步的结果生成基础的插入数据
            List<GiftAndRebateOccupyAmountReportVo> baseReportData = generateBaseReportData(approvedCases);
            // 第三步和第四步：处理随单搭赠类型的占用金额计算
            List<MarketingPlanCaseVo> giftPlanCaseList = approvedCases.stream().filter(k -> k.getCaseType().equals(MarketingPlanCaseTypeEnum.matching_gift.getCode())).collect(Collectors.toList());
            processGiftTypeOccupyAmount(giftPlanCaseList, baseReportData);

            // 第五步：处理返利类型的占用金额计算
            List<MarketingPlanCaseVo> rebatePlanCaseList = approvedCases.stream().filter(k -> k.getCaseType().equals(MarketingPlanCaseTypeEnum.back.getCode())).collect(Collectors.toList());
            processRebateTypeOccupyAmount(rebatePlanCaseList, baseReportData,  currentYearMonth);
            // 批量更新计算结果到数据库
            batchUpdateOccupyAmount(baseReportData);

            log.info("占用金额计算完成，共处理{}条记录", baseReportData.size());

        } catch (Exception e) {
            log.error("计算占用金额失败", e);
            throw new RuntimeException("计算占用金额失败：" + e.getMessage());
        }
    }

    @Override
    public void calculateLastMonthOccupyAmount() {
        try {
            LocalDate today = LocalDate.now();
            // 获取上个月的开始和结束时间
            today = today.minusMonths(1);
            // 获取当前年月
            String currentYearMonth = today.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            LocalDate monthStart = today.withDayOfMonth(1);
            LocalDate monthEnd = today.withDayOfMonth(today.lengthOfMonth());
            log.info("calculateLastMonthOccupyAmount-monthStart-monthEnd: {}-{}-{}", currentYearMonth, monthStart, monthEnd);
            // 第一步：查询本月审批通过的且当天与活动结束时间大于1天的tpm_marketing_plan_case营销方案明细
            List<MarketingPlanCaseVo> approvedCases = marketingPlanCaseRepository.queryCurMonthApprovedMarketingPlanCases(monthStart.toString(), monthEnd.toString());
            if (CollectionUtils.isEmpty(approvedCases)) {
                log.info("lastMonth未找到符合条件的营销方案明细");
                return;
            }
            // 删除本月历史数据
            repository.deleteByYearMonth(currentYearMonth);
            // 第二步：根据第一步的结果生成基础的插入数据
            List<GiftAndRebateOccupyAmountReportVo> baseReportData = generateBaseReportData(approvedCases);
            // 第三步和第四步：处理随单搭赠类型的占用金额计算
            List<MarketingPlanCaseVo> giftPlanCaseList = approvedCases.stream().filter(k -> k.getCaseType().equals(MarketingPlanCaseTypeEnum.matching_gift.getCode())).collect(Collectors.toList());
            processGiftTypeOccupyAmount(giftPlanCaseList, baseReportData);

            // 第五步：处理返利类型的占用金额计算
            List<MarketingPlanCaseVo> rebatePlanCaseList = approvedCases.stream().filter(k -> k.getCaseType().equals(MarketingPlanCaseTypeEnum.back.getCode())).collect(Collectors.toList());
            processRebateTypeOccupyAmount(rebatePlanCaseList, baseReportData,  currentYearMonth);
            // 批量更新计算结果到数据库
            batchUpdateOccupyAmount(baseReportData);

            log.info("占用金额计算完成，共处理{}条记录", baseReportData.size());

        } catch (Exception e) {
            log.error("lastMonth计算占用金额失败", e);
            throw new RuntimeException("lastMonth计算占用金额失败：" + e.getMessage());
        }
    }


    private void processRebateTypeOccupyAmount(List<MarketingPlanCaseVo> rebatePlanCaseList, List<GiftAndRebateOccupyAmountReportVo> baseReportDataList, String currentYearMonth) {
        if (CollectionUtils.isEmpty(rebatePlanCaseList)) return;
        MarketingPlanCaseVo planCaseVo = new MarketingPlanCaseVo();
        planCaseVo.setYears(currentYearMonth);
        planCaseVo.setWithholding(BooleanEnum.TRUE.getCapital());
        planCaseVo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        planCaseVo.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());
        planCaseVo.setSchemeDetailCodeList(rebatePlanCaseList.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList()));
        Page<MarketingPlanCaseVo> caseVoPage = withHoldingService.findMarketingPlanCaseReportList(PageRequest.of(0, Integer.MAX_VALUE), planCaseVo);
        if (caseVoPage.getTotal() > 0L) {
            log.info("caseVoPage: {}", caseVoPage.getRecords().size()>0? JSON.toJSONString(caseVoPage.getRecords()) : "null");
            List<MarketingPlanCaseVo> records = caseVoPage.getRecords();

            //产品范围
            List<String> schemeDetailCodes = records.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, List<MarketingPlanProductVo>> productMap = marketingPlanProductRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            List<MarketingPlanProduct> marketingPlanProductList = marketingPlanProductRepository.findListBySchemeDetailCodes(schemeDetailCodes);
            Map<String, List<MarketingPlanProduct>> planProductMap = marketingPlanProductList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
            for (MarketingPlanCaseVo planCase : records) {
                planCase.setProductAndItemList(BeanUtil.copyToList(planProductMap.getOrDefault(planCase.getSchemeDetailCode(), Lists.newArrayList()), MarketingPlanProductVo.class));
            }

            Set<String> costTypeDetailCodeSet = records.stream().map(e -> e.getDetailCode()).collect(Collectors.toSet());
            List<CostTypeDetailVo> costTypeDetailList = costTypeDetailVoService.findByCodes(new ArrayList<>(costTypeDetailCodeSet));
            Validate.notEmpty(costTypeDetailList, "未找到任意活动细类");
            Set<String> detailCodeSet = costTypeDetailList.stream().filter(e -> BooleanEnum.TRUE.getCapital().equals(e.getIsWithHolding())).map(e -> e.getDetailCode()).collect(Collectors.toSet());
            List<MarketingPlanCaseVo> planCaseVoList = records.stream().filter(e -> detailCodeSet.contains(e.getDetailCode()) &&
                    ((AuditStatusEnum.NOT_AUDIT.getCode().equals(e.getAuditStatus()) || AuditStatusEnum.PART_AUDIT.getCode().equals(e.getAuditStatus())) ||
                            (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) && (CashStatusEnum.NOT_CASH.getCode().equals(e.getCashStatus()) || CashStatusEnum.PART_CASH.getCode().equals(e.getCashStatus()))) ||
                            (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) && CashStatusEnum.WHOLE_CASH.getCode().equals(e.getCashStatus()) && StringUtils.isNotBlank(e.getWholeCashDate()) && !e.getWholeCashDate().startsWith(currentYearMonth))
                    )).collect(Collectors.toList());

            //货补
            List<MarketingPlanCaseVo> replenishmentVoList = records.stream().filter(e -> detailCodeSet.contains(e.getDetailCode()) &&
                    (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(e.getAuditStatus()) && (CashStatusEnum.NOT_CASH.getCode().equals(e.getCashStatus()) || CashStatusEnum.PART_CASH.getCode().equals(e.getCashStatus()))) &&
                    CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType())
            ).collect(Collectors.toList());
            List<String> replenishmentVoCodes = org.springframework.util.CollectionUtils.isEmpty(replenishmentVoList) ? new ArrayList<>() : replenishmentVoList.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
            Map<String, BigDecimal> deliveryAmountMap = new HashMap<>();
            if (!org.springframework.util.CollectionUtils.isEmpty(replenishmentVoCodes)) {
                deliveryAmountMap = replenishmentPoolDetailVoService.findDeliveryAmountByActivityCodes(replenishmentVoCodes);
            }

            if (CollectionUtils.isNotEmpty(planCaseVoList)) {
                //在【费用兑付/关闭明细】下查询操作类型=清空/发货/费用兑付/费用关闭且业务发生日期与计提入账年月一致的明细汇总费用金额
                List<String> codes = planCaseVoList.stream().map(e -> e.getSchemeDetailCode()).collect(Collectors.toList());
                Map<String, BigDecimal> deliveryMap = deliveryReplenishmentPoolDetailService.findBySchemeDetailCodes(codes, currentYearMonth);
                Map<String, BigDecimal> finalDeliveryAmountMap = deliveryAmountMap;

                Map<String, GiftAndRebateOccupyAmountReportVo> reportVoMap = baseReportDataList.stream().collect(Collectors.toMap(GiftAndRebateOccupyAmountReportVo::getSchemeDetailCode, Function.identity()));

                planCaseVoList.forEach(e -> {
                    GiftAndRebateOccupyAmountReportVo entity = reportVoMap.get(e.getSchemeDetailCode());
                    if (null == entity) {
                        return;
                    }
                    productMap.get(entity.getSchemeDetailCode()).stream()
                            .filter(k -> OverallPlanProductEnum.fee_product.getCode().equals(k.getType()) || OverallPlanProductEnum.fee_item.getCode().equals(k.getType()))
                            .findFirst().ifPresent(k -> {
                                entity.setFlItemOrProductName(k.getCode());
                                entity.setFlItemOrProductCode(k.getName());
                            });
                    //已申请未结案
                    //除周边物料、随单搭赠、进货返利、特价、直降外，按方案申请金额计提；返利根据返利公式测算计提金额
                    if (AuditStatusEnum.NOT_AUDIT.getCode().equals(e.getAuditStatus()) || AuditStatusEnum.PART_AUDIT.getCode().equals(e.getAuditStatus())) {
                        FormulaCalBaseVo calVo = new FormulaCalBaseVo();
                        calVo.setCustomerCode(e.getCustomerCode());
                        calVo.setActDetailCode(e.getSchemeDetailCode());
                        calVo.setYears(e.getYears());
                        calVo.setRebateStartDate(e.getRebateStartDate());
                        calVo.setRebateEndDate(e.getRebateEndDate());
                        calVo.setStartDate(e.getStartDate());
                        calVo.setEndDate(e.getEndDate());
                        calVo.setConditionNum(e.getConditionNum());
                        calVo.setItemCodeSet(Optional.ofNullable(e.getItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                        calVo.setProductCodeSet(Optional.ofNullable(e.getProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                        calVo.setFeeItemCodeSet(Optional.ofNullable(e.getFeeItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                        calVo.setFeeProductCodeSet(Optional.ofNullable(e.getFeeProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                        Validate.isTrue(!(org.springframework.util.CollectionUtils.isEmpty(calVo.getItemCodeSet()) && org.springframework.util.CollectionUtils.isEmpty(calVo.getProductCodeSet())), "营销方案：%s-%s，品项编码和商品编码不能为空", e.getSchemeCode(), e.getSchemeDetailCode());
                        Validate.isTrue(!(org.springframework.util.CollectionUtils.isEmpty(calVo.getFeeItemCodeSet()) && org.springframework.util.CollectionUtils.isEmpty(calVo.getFeeProductCodeSet())), "营销方案：%s-%s，返利品项和返利产品不能为空", e.getSchemeCode(), e.getSchemeDetailCode());
                        calVo.setGiveNum(e.getGiveNum());
                        calVo.setOrgCode(e.getBelongDepartmentCode());

                        BigDecimal reportAmount = formulaCalService.calResult(FormulaTypeEnum.MANAGEMENT_REPORT.getCode(), e.getConditionFormula(), calVo);

                        entity.setOccupyAmount(reportAmount.subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                        //已结案未兑付、
                        //①电汇、账扣、票扣，按费用兑付统计已结案未上账兑付（根据TPM的未兑付状态判断）的差额计提（已结案金额-已上账金额）；
                        //②上账到客户的货补池费用，按DMS货补池的已结案未上账兑付（结案金额-该活动在DMS的已发货金额）的余额计提。
                    } else {
                        if (Arrays.asList(CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode(), CashMethodEnum.WIRE_TRANSFER.getDictCode()).contains(e.getCashType())) {
                            entity.setOccupyAmount(e.getAuditAmount().subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                        } else if (CashMethodEnum.REPLENISHMENT.getDictCode().equals(e.getCashType())) {
                            entity.setOccupyAmount(e.getAuditAmount().subtract(finalDeliveryAmountMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)).subtract(deliveryMap.getOrDefault(e.getSchemeDetailCode(), BigDecimal.ZERO)));
                        }
                    }
                });
            }
        }

    }


    /**
     * 批量保存占用金额到数据库
     *
     * @param reportList 计算后的报表数据列表
     */
    private void batchUpdateOccupyAmount(List<GiftAndRebateOccupyAmountReportVo> reportList) {
        if (CollectionUtils.isEmpty(reportList)) {
            return;
        }

        try {
            // 转换为实体对象并批量保存
            List<GiftAndRebateOccupyAmountReport> entities = reportList.stream()
                    .map(vo -> nebulaToolkitService.copyObjectByWhiteList(
                            vo, GiftAndRebateOccupyAmountReport.class, HashSet.class, ArrayList.class))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(entities)) {
                repository.saveBatch(entities);
                log.info("批量保存占用金额成功，保存{}条记录", entities.size());
            }

        } catch (Exception e) {
            log.error("批量保存占用金额失败", e);
            throw new RuntimeException("批量保存占用金额失败：" + e.getMessage());
        }
    }


    /**
     * 第二步：根据营销方案明细生成基础的插入数据
     *
     * @param approvedCases 审批通过的营销方案明细
     * @return 基础报表数据
     */
    private List<GiftAndRebateOccupyAmountReportVo> generateBaseReportData(List<MarketingPlanCaseVo> approvedCases) {
        List<GiftAndRebateOccupyAmountReportVo> baseReportData = new ArrayList<>();

        for (MarketingPlanCaseVo planCase : approvedCases) {
            GiftAndRebateOccupyAmountReportVo reportVo = new GiftAndRebateOccupyAmountReportVo();

            // 基础字段映射
            reportVo.setSchemeCode(planCase.getSchemeCode());
            reportVo.setSchemeName(planCase.getSchemeName());
            reportVo.setSchemeType(planCase.getSchemeType());
            reportVo.setSchemeDetailCode(planCase.getSchemeDetailCode());
            reportVo.setStartDate(planCase.getStartDate());
            reportVo.setEndDate(planCase.getEndDate());
            reportVo.setCustomerCode(planCase.getCustomerCode());
            reportVo.setCustomerName(planCase.getCustomerName());
            reportVo.setBelongDepartmentCode(planCase.getBelongDepartmentCode());
            reportVo.setBelongDepartmentName(planCase.getBelongDepartmentName());
            reportVo.setBearDepartmentCode(planCase.getBearDepartmentCode());
            reportVo.setBearDepartmentName(planCase.getBearDepartmentName());
            reportVo.setCostCenterCode(planCase.getCostCenterCode());
            reportVo.setCostCenterName(planCase.getCostCenterName());
            reportVo.setDetailCode(planCase.getDetailCode());
            reportVo.setDetailName(planCase.getDetailName());
            reportVo.setItemCode(planCase.getItemCode());
            reportVo.setItemName(planCase.getItemName());
            reportVo.setPlanAmount(planCase.getApplyAmount()); // 规划金额使用现金金额
            reportVo.setOccupyAmount(BigDecimal.ZERO); // 初始占用金额为0
            if (planCase.getCaseType().equals(MarketingPlanCaseTypeEnum.matching_gift.getCode())) {
                reportVo.setConditionNum(planCase.getConditionNum());
                reportVo.setGiveNum(planCase.getGiveNum());
            }
            if (planCase.getCaseType().equals(MarketingPlanCaseTypeEnum.back.getCode())) {
                reportVo.setConditionFormula(planCase.getConditionNum());
                reportVo.setRebateStandard(planCase.getGiveNum());
                reportVo.setRebatePolicy(planCase.getConditionFormulaName());
            }
            reportVo.setYears(planCase.getYears());


            // 设置租户和状态信息
            reportVo.setTenantCode(TenantUtils.getTenantCode());
            reportVo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            reportVo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());

            baseReportData.add(reportVo);
        }

        log.info("生成基础报表数据{}条", baseReportData.size());
        return baseReportData;
    }




    /**
     * 第三步和第四步：处理随单搭赠类型的占用金额计算
     *
     * @param giftPlanCaseList
     * @param baseReportData   基础报表数据
     */
    private void processGiftTypeOccupyAmount(List<MarketingPlanCaseVo> giftPlanCaseList, List<GiftAndRebateOccupyAmountReportVo> baseReportData) {
        log.info("处理随单搭赠类型的占用金额计算");
        List<String> schemeDetailCodes = giftPlanCaseList.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        // <活动明细编码, 政策编码>
        Map<String, String> policyCodeMap = dmsPromotionSalePolicyVoService.findSalePolicyCodesByActivityCodes(schemeDetailCodes);
        List<DmsWarehouseOrderDetailVo> warehouseOrderDetailVos = dmsWarehouseOrderDetailVoService.findByPolicyCodes(Lists.newArrayList(policyCodeMap.values()));
        // 根据政策编码分组汇总金额
        Map<String, BigDecimal> warehouseOrderMap = warehouseOrderDetailVos.stream().collect(Collectors.groupingBy(DmsWarehouseOrderDetailVo::getRelaCode,Collectors.mapping(
                k -> Optional.ofNullable(k.getOriginalTotalAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        // 根据明细编码和type = fee_level 查询本品小类信息
        Map<String, List<MarketingPlanProductVo>> productMap = marketingPlanProductRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
        for (GiftAndRebateOccupyAmountReportVo reportVo : baseReportData) {
            if (productMap.containsKey(reportVo.getSchemeDetailCode())) {
                productMap.get(reportVo.getSchemeDetailCode()).stream()
                        .filter(e -> OverallPlanProductEnum.cal_level.getCode().equals(e.getType()))
                        .findFirst().ifPresent(e -> {
                    reportVo.setBpLevelCode(e.getCode());
                    reportVo.setBpLevelName(e.getName());
                });
                productMap.get(reportVo.getSchemeDetailCode()).stream()
                        .filter(e -> OverallPlanProductEnum.cal_item.getCode().equals(e.getType()))
                        .findFirst().ifPresent(e -> {
                            reportVo.setItemCode(e.getCode());
                            reportVo.setItemName(e.getName());
                        });
            }

            String salePolicyCode = policyCodeMap.get(reportVo.getSchemeDetailCode());
            if (StringUtils.isNotBlank(salePolicyCode)) {
                BigDecimal occupyAmount = warehouseOrderMap.get(salePolicyCode);
                reportVo.setOccupyAmount(occupyAmount);

                log.info("方案明细编码：{}，政策编码：{}，占用金额：{}",
                        reportVo.getSchemeDetailCode(), salePolicyCode, occupyAmount);
            }
        }
        log.info("处理随单搭赠类型的占用金额计算完成");
    }

}
