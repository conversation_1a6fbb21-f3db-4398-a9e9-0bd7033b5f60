<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.mapper.GiftAndRebateOccupyAmountReportExportsMapper">

    <select id="getExportTotal" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM tpm_gift_rebate_occupy_amount_report
        <where>
            <if test="dto.schemeCode != null and dto.schemeCode != ''">
                AND scheme_code = #{dto.schemeCode}
            </if>
            <if test="dto.schemeDetailCode != null and dto.schemeDetailCode != ''">
                AND scheme_detail_code = #{dto.schemeDetailCode}
            </if>
            <if test="dto.schemeName != null and dto.schemeName != ''">
                AND scheme_name LIKE CONCAT('%', #{dto.schemeName}, '%')
            </if>
            <if test="dto.schemeType != null and dto.schemeType != ''">
                AND scheme_type = #{dto.schemeType}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                AND customer_code = #{dto.customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                AND customer_name LIKE CONCAT('%', #{dto.customerName}, '%')
            </if>
            <if test="dto.belongDepartmentCode != null and dto.belongDepartmentCode != ''">
                AND belong_department_code = #{dto.belongDepartmentCode}
            </if>
            <if test="dto.belongDepartmentName != null and dto.belongDepartmentName != ''">
                AND belong_department_name LIKE CONCAT('%', #{dto.belongDepartmentName}, '%')
            </if>
            <if test="dto.bearDepartmentCode != null and dto.bearDepartmentCode != ''">
                AND bear_department_code = #{dto.bearDepartmentCode}
            </if>
            <if test="dto.bearDepartmentName != null and dto.bearDepartmentName != ''">
                AND bear_department_name LIKE CONCAT('%', #{dto.bearDepartmentName}, '%')
            </if>
            <if test="dto.costCenterCode != null and dto.costCenterCode != ''">
                AND cost_center_code = #{dto.costCenterCode}
            </if>
            <if test="dto.costCenterName != null and dto.costCenterName != ''">
                AND cost_center_name LIKE CONCAT('%', #{dto.costCenterName}, '%')
            </if>
            <if test="dto.detailCode != null and dto.detailCode != ''">
                AND detail_code = #{dto.detailCode}
            </if>
            <if test="dto.detailName != null and dto.detailName != ''">
                AND detail_name LIKE CONCAT('%', #{dto.detailName}, '%')
            </if>
            <if test="dto.itemCode != null and dto.itemCode != ''">
                AND item_code = #{dto.itemCode}
            </if>
            <if test="dto.itemName != null and dto.itemName != ''">
                AND item_name LIKE CONCAT('%', #{dto.itemName}, '%')
            </if>
            <if test="dto.bpLevelCode != null and dto.bpLevelCode != ''">
                AND bp_level_code = #{dto.bpLevelCode}
            </if>
            <if test="dto.bpLevelName != null and dto.bpLevelName != ''">
                AND bp_level_name LIKE CONCAT('%', #{dto.bpLevelName}, '%')
            </if>
            <if test="dto.flItemOrProductCode != null and dto.flItemOrProductCode != ''">
                AND fl_item_or_product_code = #{dto.flItemOrProductCode}
            </if>
            <if test="dto.flItemOrProductName != null and dto.flItemOrProductName != ''">
                AND fl_item_or_product_name LIKE CONCAT('%', #{dto.flItemOrProductName}, '%')
            </if>
            <if test="dto.rebatePolicy != null and dto.rebatePolicy != ''">
                AND rebate_policy LIKE CONCAT('%', #{dto.rebatePolicy}, '%')
            </if>
        </where>
        ORDER BY create_time DESC,ID DESC
    </select>
    <select id="findData"
            resultType="com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.model.GiftAndRebateOccupyAmountReportExportsVo">
        SELECT
        *
        FROM tpm_gift_rebate_occupy_amount_report
        <where>
            <if test="dto.schemeCode != null and dto.schemeCode != ''">
                AND scheme_code = #{dto.schemeCode}
            </if>
            <if test="dto.schemeDetailCode != null and dto.schemeDetailCode != ''">
                AND scheme_detail_code = #{dto.schemeDetailCode}
            </if>
            <if test="dto.schemeName != null and dto.schemeName != ''">
                AND scheme_name LIKE CONCAT('%', #{dto.schemeName}, '%')
            </if>
            <if test="dto.schemeType != null and dto.schemeType != ''">
                AND scheme_type = #{dto.schemeType}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                AND customer_code = #{dto.customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                AND customer_name LIKE CONCAT('%', #{dto.customerName}, '%')
            </if>
            <if test="dto.belongDepartmentCode != null and dto.belongDepartmentCode != ''">
                AND belong_department_code = #{dto.belongDepartmentCode}
            </if>
            <if test="dto.belongDepartmentName != null and dto.belongDepartmentName != ''">
                AND belong_department_name LIKE CONCAT('%', #{dto.belongDepartmentName}, '%')
            </if>
            <if test="dto.bearDepartmentCode != null and dto.bearDepartmentCode != ''">
                AND bear_department_code = #{dto.bearDepartmentCode}
            </if>
            <if test="dto.bearDepartmentName != null and dto.bearDepartmentName != ''">
                AND bear_department_name LIKE CONCAT('%', #{dto.bearDepartmentName}, '%')
            </if>
            <if test="dto.costCenterCode != null and dto.costCenterCode != ''">
                AND cost_center_code = #{dto.costCenterCode}
            </if>
            <if test="dto.costCenterName != null and dto.costCenterName != ''">
                AND cost_center_name LIKE CONCAT('%', #{dto.costCenterName}, '%')
            </if>
            <if test="dto.detailCode != null and dto.detailCode != ''">
                AND detail_code = #{dto.detailCode}
            </if>
            <if test="dto.detailName != null and dto.detailName != ''">
                AND detail_name LIKE CONCAT('%', #{dto.detailName}, '%')
            </if>
            <if test="dto.itemCode != null and dto.itemCode != ''">
                AND item_code = #{dto.itemCode}
            </if>
            <if test="dto.itemName != null and dto.itemName != ''">
                AND item_name LIKE CONCAT('%', #{dto.itemName}, '%')
            </if>
            <if test="dto.bpLevelCode != null and dto.bpLevelCode != ''">
                AND bp_level_code = #{dto.bpLevelCode}
            </if>
            <if test="dto.bpLevelName != null and dto.bpLevelName != ''">
                AND bp_level_name LIKE CONCAT('%', #{dto.bpLevelName}, '%')
            </if>
            <if test="dto.flItemOrProductCode != null and dto.flItemOrProductCode != ''">
                AND fl_item_or_product_code = #{dto.flItemOrProductCode}
            </if>
            <if test="dto.flItemOrProductName != null and dto.flItemOrProductName != ''">
                AND fl_item_or_product_name LIKE CONCAT('%', #{dto.flItemOrProductName}, '%')
            </if>
            <if test="dto.rebatePolicy != null and dto.rebatePolicy != ''">
                AND rebate_policy LIKE CONCAT('%', #{dto.rebatePolicy}, '%')
            </if>
        </where>
        ORDER BY create_time DESC,ID DESC
        limit #{dto.offset},#{dto.limit}
    </select>
</mapper>