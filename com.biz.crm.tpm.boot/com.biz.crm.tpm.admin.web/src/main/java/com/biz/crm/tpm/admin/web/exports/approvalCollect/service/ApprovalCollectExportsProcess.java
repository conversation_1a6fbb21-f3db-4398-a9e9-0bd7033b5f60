package com.biz.crm.tpm.admin.web.exports.approvalCollect.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.approvalCollect.mapper.ApprovalCollectExportsMapper;
import com.biz.crm.tpm.admin.web.exports.approvalCollect.model.ApprovalCollectExportsDto;
import com.biz.crm.tpm.admin.web.exports.approvalCollect.model.ApprovalCollectExportsVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @describe:核销采集信息导出
 * @createTime 2022年06月08日 16:56:00
 */
@Component
public class ApprovalCollectExportsProcess implements ExportProcess<ApprovalCollectExportsVo> {

  @Autowired
  private ApprovalCollectExportsMapper approvalCollectExportsMapper;
  @Autowired
  private DictDataVoService dictDataVoService;

  private final static String EXAMPLE_TYPE = "example_type";

  /**
   * 获取总条数
   *
   * @param params
   * @return
   */
  @Override
  public Integer getTotal(Map<String, Object> params) {
    ApprovalCollectExportsDto dto = this.convertParams(params);
    return approvalCollectExportsMapper.getExportTotal(dto);
  }

  @Override
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    ApprovalCollectExportsDto dto = this.convertParams(params);
    dto.setOffset(this.getPageSize() * vo.getPageNo());
    dto.setLimit(vo.getPageSize());
    List<ApprovalCollectExportsVo> data = approvalCollectExportsMapper.findData(dto);
    //调整
    adjustData(data);
    return toJSONArray(data);
  }

  /**
   * 模板编码
   *
   * @return
   */
  @Override
  public String getBusinessCode() {
    return "TPM_APPROVAL_COLLECT_EXPORT";
  }

  /**
   * 模板名称
   *
   * @return
   */
  @Override
  public String getBusinessName() {
    return "TPM核销采集信息导出";
  }

  /**
   * 获取参数
   *
   * @param params
   * @return
   */
  private ApprovalCollectExportsDto convertParams(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    // map 参数转换为对应的dto参数对象，可以手工进行修改设置
    ApprovalCollectExportsDto dto = JSON.parseObject(JSON.toJSONString(params), ApprovalCollectExportsDto.class);
    dto.setTenantCode(TenantUtils.getTenantCode());
    return dto;
  }

  /**
   * 调整数据
   *
   * @param data
   */
  private void adjustData(List<ApprovalCollectExportsVo> data) {
    if (CollectionUtils.isEmpty(data)) {
      return;
    }
    Map<String, List<DictDataVo>> mapDict = this.dictDataVoService.findByDictTypeCodeList(Lists.newArrayList(EXAMPLE_TYPE));
    for (ApprovalCollectExportsVo vo : data) {
      vo.setEnableStatus(EnableStatusEnum.getDesc(vo.getEnableStatus()));
      vo.setType(this.findDictValue(mapDict,EXAMPLE_TYPE,vo.getType()));
    }
  }

  /**
   * 获取字典值
   *
   * @param mapDict
   * @param dictTypeCode
   * @param code
   * @return
   */
  private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
    if (Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
      return StringUtils.EMPTY;
    }
    final List<DictDataVo> vos = mapDict.get(dictTypeCode);
    if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
      return StringUtils.EMPTY;
    }
    final Optional<String> first =
        vos.stream()
            .filter(a -> a.getDictCode().equals(code))
            .map(DictDataVo::getDictValue)
            .findFirst();
    return first.orElse(StringUtils.EMPTY);
  }
}
