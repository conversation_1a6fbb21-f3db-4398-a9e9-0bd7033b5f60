package com.biz.crm.tpm.admin.web.exports.marketingplan.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.StringJoiner;

/**
 * @Description  TODO 别乱动顺序！！！
 * <AUTHOR>
 * @Date 2024/8/13 21:30
 */
@ApiModel("销售计划")
@Data
public class MarketingSalesPlanExportVo implements Serializable {


    @ApiModelProperty("客户编码")
    private String erpCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty(value = "成本中心编码")
    private String costCenterCode;

    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;

    @ApiModelProperty(value = "年月")
    private String years;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("销售价格")
    private BigDecimal salesPrice;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("预估销售金额")
    private BigDecimal taxEstimatedCost;

    @ApiModelProperty("产品运输方式")
    private String transportType;

    @ApiModelProperty("错误信息")
    private String errMsg;
}
