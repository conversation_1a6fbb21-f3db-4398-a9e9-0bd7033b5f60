package com.biz.crm.tpm.admin.web.register;

import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>活动预付数据视图注册器
 * 基于nebula的数据视图提供活动预付列表查询功能
 *
 * <AUTHOR>
 * @date 2022/8/15
 */
@Component
public class PrepayActivityDataViewRegister implements DataviewRegister {
  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-活动预付";
  }

  @Override
  public String buildSql() {
    return "select distinct tp.*, tpd.activities_detail_code, bpbm.process_status, bpbm.process_no " +
        "from tpm_prepay tp " +
        "left join tpm_prepay_detail tpd on tp.prepay_code = tpd.prepay_code and tp.tenant_code = tpd.tenant_code " +
        "left join bpm_process_business_mapping bpbm on (tp.prepay_code = bpbm.business_no and bpbm.business_code = '" + PayConstant.PROCESS_PREPAY_ACTIVITIES + "') " +
        "where tp.tenant_code = :tenantCode " +
        "and tp.del_flag = '009' ";
  }
}
