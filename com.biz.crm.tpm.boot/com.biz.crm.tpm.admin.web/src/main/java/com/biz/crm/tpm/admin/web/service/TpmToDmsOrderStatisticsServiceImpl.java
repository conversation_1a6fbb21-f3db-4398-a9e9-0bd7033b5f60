package com.biz.crm.tpm.admin.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.rocketmq.service.RocketMqProducer;
import com.biz.crm.business.common.rocketmq.util.RocketMqUtil;
import com.biz.crm.business.common.rocketmq.vo.MqMessageVo;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.dms.business.order.sdk.OrderReachStatisticsReportDto;
import com.biz.crm.dms.business.order.sdk.dto.TpmToDmsOrderStatisticDto;
import com.biz.crm.dms.business.order.sdk.vo.OrderReachStatisticsReportSumVo;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.destination.sdk.service.MdmDestinationVoService;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.admin.web.dto.orderstatistics.CustomerDestinationOrgMapping;
import com.biz.crm.tpm.admin.web.mapper.TpmToDmsOrderStatisticsMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.control.sdk.enums.ControlDimensionEnum;
import com.biz.crm.tpm.business.pay.sdk.service.PosDataService;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.FormulaGetValueComponent;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/16 14:56
 */
@Service
@Slf4j
public class TpmToDmsOrderStatisticsServiceImpl {


    @Resource
    private TpmToDmsOrderStatisticsMapper tpmToDmsOrderStatisticsMapper;

    @Resource
    private OrgVoService orgVoService;

    @Autowired(required = false)
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;

    @Autowired(required = false)
    private PosDataService posDataService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private RocketMqProducer rocketMqProducer;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private MdmDestinationVoService mdmDestinationVoService;

    private static final String TPM_TO_DMS_ORDER_STATISTIC_KEY = "tpm_to_dms_order_statistic_key";

    private static final String SPECIAL_CUSTOMER_DICT_KEY = "customer_destination_org_mapping";

    /**
     * 计算当前年月的数据
     *
     * @param years
     */
    public void calOrderStatistics(String years) {
        if (ObjectUtils.isEmpty(years)) {
            years = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        }
        List<TpmToDmsOrderStatisticDto> dataList = tpmToDmsOrderStatisticsMapper.findDataListByYears(years, TenantUtils.getTenantCode());
        List<String> orgCodes = dataList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode())).map(x -> x.getOrgCode()).distinct().collect(Collectors.toList());
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllParentByOrgCodesMap(orgCodes);
        Map<String, OrgVo> regionOrgMap = Maps.newHashMap();
        orgMap.forEach((key, value) -> {
            Optional<OrgVo> optional = value.stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType())).collect(Collectors.toList()).stream().findFirst();
            if (optional.isPresent()) {
                regionOrgMap.put(key, optional.get());
            }
        });
        dataList.forEach(x -> {
            if (regionOrgMap.containsKey(x.getOrgCode())) {
                OrgVo orgVo = regionOrgMap.get(x.getOrgCode());
                x.setRegionCode(orgVo.getOrgCode());
                x.setRegionName(orgVo.getOrgName());
            }
        });
        String nowDate = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        Map<String, String> dateMap = FormulaGetValueComponent.getDateMonthly(nowDate, DateUtil.DEFAULT_YEAR_MONTH_DAY);
        //查询发货数据
        Set<String> customerCodeSet = dataList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toSet());
        Set<String> productCodes = dataList.stream().map(x -> x.getProductCode()).collect(Collectors.toSet());
        List<DmsWarehouseOrderDetailVo> orderDetailVos = this.findDmsOrderDelivery(customerCodeSet, productCodes, dateMap);
        //查询pos拆分占比数据
        Map<String, BigDecimal> posDataMap = this.calPosData(years, customerCodeSet);

        // 查询天虹类特殊客户
        Map<String, List<CustomerDestinationOrgMapping>> destinationOrgMap = specialCustomer();
        Map<String, BigDecimal> specialCustomerAmountMap =
                amountGroupByCustomerDirection(destinationOrgMap, orderDetailVos);

        Map<String, BigDecimal> cusGoodsAmountMap = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                //.filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                .collect(Collectors.groupingBy(x -> x.getCustomerCode() + x.getGoodsCode(),
                        Collectors.mapping(DmsWarehouseOrderDetailVo::getTotalAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        for (TpmToDmsOrderStatisticDto data : dataList) {
            data.setDeliveryAmount(BigDecimal.ZERO);

            String customerCode = data.getCustomerCode();
            if (destinationOrgMap.containsKey(customerCode)) {
                // 特殊客户逻辑
                String orgCode = data.getOrgCode();
                String productCode = data.getProductCode();
                String key = StrUtil.join(StrPool.DOT, customerCode, orgCode, productCode);
                BigDecimal amount = specialCustomerAmountMap.get(key);
                if (Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) > 0) {
                    data.setDeliveryAmount(amount);
                }
                continue;
            }
            // 以下是原逻辑
            String key = data.getCustomerCode() + data.getProductCode();
            if (cusGoodsAmountMap.containsKey(key)) {
                data.setDeliveryAmount(cusGoodsAmountMap.get(key));
            }
            String posKey = data.getCustomerCode() + data.getOrgCode();
            if (posDataMap.containsKey(posKey)) {
                BigDecimal rate = posDataMap.get(posKey);
                data.setDeliveryAmount(data.getDeliveryAmount().multiply(rate).setScale(2, BigDecimal.ROUND_HALF_DOWN));
            }

            if (data.getDeliveryAmount().compareTo(BigDecimal.ZERO) < 0) {
                data.setDeliveryAmount(BigDecimal.ZERO);
            }
        }

        Integer batchSize = 500;
        // 分批次处理
        for (int i = 0; i < dataList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, dataList.size());
            List<TpmToDmsOrderStatisticDto> batchList = dataList.subList(i, end);
            // 存入 Redis 不再这里进行删除 在dms消费完了在删除key
            redisTemplate.opsForList().rightPushAll(TPM_TO_DMS_ORDER_STATISTIC_KEY, batchList);
        }


        //以防数量量过大 直接mq消费
        MqMessageVo mqMessageVo = new MqMessageVo();
        mqMessageVo.setTopic(MqConstant.TOPIC_TPM_TO_DMS_ORDER_STATISTIC + RocketMqUtil.mqEnvironment());
        mqMessageVo.setOperationType(MqConstant.TOPIC_TPM_TO_DMS_ORDER_STATISTIC);
        mqMessageVo.setTag(MqConstant.TAG_TPM_TO_DMS_ORDER_STATISTIC);
        mqMessageVo.setMsgBody(TPM_TO_DMS_ORDER_STATISTIC_KEY);
        mqMessageVo.setBusinessKey(TPM_TO_DMS_ORDER_STATISTIC_KEY);
        this.rocketMqProducer.sendMqOrderMsg(mqMessageVo);

    }


    /**
     * 查询订单统计
     *
     * @param pageable
     * @param dto
     * @return
     */
    public Page<TpmToDmsOrderStatisticDto> findByCondition(Pageable pageable, OrderReachStatisticsReportDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notBlank(dto.getStatisticsDimension(), "查询维度不能为空");
        Validate.notNull(dto.getYears(), "年月不能为空");
        String date = dto.getYears() + "-01";
        List<String> orgCodes = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(dto.getRegionCode())) {
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(dto.getRegionCode());
            if (CollectionUtils.isNotEmpty(orgVoList)) {
                orgCodes = orgVoList.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
            }
        }
        Page<TpmToDmsOrderStatisticDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<TpmToDmsOrderStatisticDto> pageData = tpmToDmsOrderStatisticsMapper.findByCondition(page, dto, orgCodes, TenantUtils.getTenantCode());
        calAmountDataList(pageData, date, dto);
        return pageData;
    }


    public OrderReachStatisticsReportSumVo sumByConditions(OrderReachStatisticsReportDto dto) {
        Validate.notBlank(dto.getStatisticsDimension(), "查询维度不能为空");
        Validate.notNull(dto.getYears(), "年月不能为空");
        String date = dto.getYears() + "-01";
        List<String> orgCodes = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(dto.getRegionCode())) {
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(dto.getRegionCode());
            if (CollectionUtils.isNotEmpty(orgVoList)) {
                orgCodes = orgVoList.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
            }
        }
        Page<TpmToDmsOrderStatisticDto> page = new Page<>(0, Integer.MAX_VALUE);
        Page<TpmToDmsOrderStatisticDto> pageData = tpmToDmsOrderStatisticsMapper.findByCondition(page, dto, orgCodes, TenantUtils.getTenantCode());
        calAmountDataList(pageData, date, dto);
        OrderReachStatisticsReportSumVo vo = new OrderReachStatisticsReportSumVo();
        if (CollectionUtils.isNotEmpty(pageData.getRecords())) {
            //判断是部门
            if (ControlDimensionEnum.CUSTOMER.getCode().equals(dto.getStatisticsDimension())) {
                BigDecimal customerTotalQuantity = BigDecimal.valueOf(pageData.getRecords().stream().map(x -> x.getCustomerCode()).distinct().count());
                vo.setCustomerTotalQuantity(customerTotalQuantity);
            } else {
                BigDecimal productTotalQuantity = BigDecimal.valueOf(pageData.getRecords().stream().map(x -> x.getProductCode()).distinct().count());
                vo.setProductTotalQuantity(productTotalQuantity);
            }
            BigDecimal deliveryTotalAmount = pageData.getRecords().stream().map(x -> x.getDeliveryAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal budgetTotalAmount = pageData.getRecords().stream().map(x -> x.getBudgetReachAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal planTotalAmount = pageData.getRecords().stream().map(x -> Optional.ofNullable(x.getPlanReachAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setDeliveryTotalAmount(deliveryTotalAmount);
            vo.setBudgetTotalAmount(budgetTotalAmount);
            vo.setPlanTotalAmount(planTotalAmount);
        }
        return vo;
    }


    private void calAmountDataList(Page<TpmToDmsOrderStatisticDto> pageData, String date, OrderReachStatisticsReportDto dto) {
        if (CollectionUtils.isNotEmpty(pageData.getRecords())) {
            List<TpmToDmsOrderStatisticDto> dataList = pageData.getRecords();
            //判断是客户+部门维度
            if (ControlDimensionEnum.CUSTOMER.getCode().equals(dto.getStatisticsDimension())) {
                List<String> orgCodeList = dataList.stream().map(x -> x.getOrgCode()).distinct().collect(Collectors.toList());
                Map<String, List<OrgVo>> orgMap = orgVoService.findAllParentByOrgCodesMap(orgCodeList);
                for (TpmToDmsOrderStatisticDto l : dataList) {
                    if (orgMap.containsKey(l.getOrgCode())) {
                        Optional<OrgVo> optional = orgMap.get(l.getOrgCode()).stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType()))
                                .findFirst();
                        if (optional.isPresent()) {
                            OrgVo orgVo = optional.get();
                            l.setRegionCode(orgVo.getOrgCode());
                            l.setRegionName(orgVo.getOrgName());
                        }
                    }
                }
                Map<String, List<OrgVo>> orgVoListMap = orgVoService.findAllChildrenByOrgCodesMap(orgCodeList);
                Map<String, Set<String>> orgCostCenterMap = Maps.newHashMap();
                for (Map.Entry<String, List<OrgVo>> entry : orgVoListMap.entrySet()) {
                    orgCostCenterMap.put(entry.getKey(), entry.getValue().stream().filter(x -> CollectionUtils.isNotEmpty(x.getCostCenterCodeSet()))
                            .flatMap(x -> x.getCostCenterCodeSet().stream()).collect(Collectors.toSet()));
                }
                List<String> costCenterCodes = orgCostCenterMap.values().stream().flatMap(Set::stream).distinct().collect(Collectors.toList());
                List<String> customerCodes = dataList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toList());
                List<MarketingSalesPlanVo> salesPlanVoList = tpmToDmsOrderStatisticsMapper.findSalesPlanByConditions(customerCodes, costCenterCodes, dto.getYears(), null);
                Map<String, List<MarketingSalesPlanVo>> salesPlanMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(salesPlanVoList)) {
                    salesPlanMap = salesPlanVoList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode()));
                }
                for (TpmToDmsOrderStatisticDto l : dataList) {
                    l.setPlanReachAmount(BigDecimal.ZERO);
                    Set<String> costCenterCodeSet = orgCostCenterMap.getOrDefault(l.getOrgCode(), Sets.newHashSet());
                    if (salesPlanMap.containsKey(l.getCustomerCode())) {
                        List<MarketingSalesPlanVo> salesPlanVos = salesPlanMap.get(l.getCustomerCode());
                        BigDecimal estimatedCost = salesPlanVos.stream().filter(x -> costCenterCodeSet.contains(x.getCostCenterCode()))
                                .map(x -> x.getEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
                        l.setPlanReachAmount(estimatedCost);
                    }
                }
            } else {
                //按照客户+产品维度
                List<String> customerCodes = dataList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toList());
                List<String> productCodes = dataList.stream().map(x -> x.getProductCode()).distinct().collect(Collectors.toList());
                List<MarketingSalesPlanVo> salesPlanVoList = tpmToDmsOrderStatisticsMapper.findSalesPlanByConditions(customerCodes, null, dto.getYears(), productCodes);
                Map<String, BigDecimal> salesPlanMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(salesPlanVoList)) {
                    salesPlanMap = salesPlanVoList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode() + x.getProductCode(),
                            Collectors.mapping(x -> x.getEstimatedCost(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                }
                for (TpmToDmsOrderStatisticDto l : dataList) {
                    if (salesPlanMap.containsKey(l.getCustomerCode())) {
                        BigDecimal estimatedCost = salesPlanMap.get(l.getCustomerCode() + l.getProductCode());
                        l.setPlanReachAmount(estimatedCost);
                    }
                }
            }
            //数据组装
            buildData(date, dataList, dto.getYears(), dto.getStatisticsDimension());
            dataList.forEach(v -> {
                if (v.getBudgetReachAmount().compareTo(BigDecimal.ZERO) != 0) {
                    v.setBudgetReachRate(v.getDeliveryAmount().divide(v.getBudgetReachAmount(), 4, RoundingMode.HALF_UP));
                } else {
                    v.setBudgetReachRate(BigDecimal.ZERO);
                }
                if (null!= v.getPlanReachAmount() &&v.getPlanReachAmount().compareTo(BigDecimal.ZERO) != 0) {
                    v.setPlanReachRate(v.getDeliveryAmount().divide(v.getPlanReachAmount(), 4, RoundingMode.HALF_UP));
                } else {
                    v.setPlanReachRate(BigDecimal.ZERO);
                }
            });
        }
    }


    /**
     * 计算数据
     *
     * @param date
     * @param dataList
     * @param years
     * @param dimension
     */
    private void buildData(String date, List<TpmToDmsOrderStatisticDto> dataList, String years, String dimension) {
        Map<String, String> dateMap = FormulaGetValueComponent.getDateMonthly(date, DateUtil.DEFAULT_YEAR_MONTH_DAY);
        //查询发货数据
        Set<String> customerCodeSet = dataList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toSet());
        Set<String> productCodeSet = dataList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductCode()))
                .map(x -> x.getProductCode())
                .collect(Collectors.toSet());
//                Set<String> productCodes = dataList.stream().map(x -> x.getProductCode()).collect(Collectors.toSet());
        List<DmsWarehouseOrderDetailVo> orderDetailVos = this.findDmsOrderDelivery(customerCodeSet, productCodeSet, dateMap);
        //查询pos拆分占比数据
        Map<String, BigDecimal> posDataMap = this.calPosData(years, customerCodeSet);

        // 查询天虹类特殊客户
        Map<String, List<CustomerDestinationOrgMapping>> destinationOrgMap = specialCustomer();
        Map<String, BigDecimal> specialCustomerAmountMap =
                amountGroupByCustomerDirection(destinationOrgMap, orderDetailVos);

        Map<String, BigDecimal> cusGoodsAmountMap = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                //.filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                .collect(Collectors.groupingBy(x -> {
                            String key = x.getCustomerCode();
                            if (!ControlDimensionEnum.CUSTOMER.getCode().equals(dimension)) {
                                key = key + x.getGoodsCode();
                            }
                            return key;
                        },
                        Collectors.mapping(DmsWarehouseOrderDetailVo::getTotalAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        for (TpmToDmsOrderStatisticDto data : dataList) {
            data.setDeliveryAmount(BigDecimal.ZERO);

            String customerCode = data.getCustomerCode();
            if (destinationOrgMap.containsKey(customerCode)) {
                // 特殊客户逻辑
                String orgCode = data.getOrgCode();
                String productCode = data.getProductCode();
                String key = StrUtil.join(StrPool.DOT, customerCode, orgCode, productCode);
                BigDecimal amount = specialCustomerAmountMap.get(key);
                if (Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) > 0) {
                    data.setDeliveryAmount(amount);
                }
                continue;
            }
            // 以下是原逻辑
            String key = null;
            if (dimension.equals(ControlDimensionEnum.CUSTOMER.getCode())) {
                key = data.getCustomerCode();
            } else {
                key = data.getCustomerCode() + data.getProductCode();
            }
            if (cusGoodsAmountMap.containsKey(key)) {
                data.setDeliveryAmount(cusGoodsAmountMap.get(key));
            }
            String posKey = data.getCustomerCode() + data.getOrgCode();
            if (posDataMap.containsKey(posKey)) {
                BigDecimal rate = posDataMap.get(posKey);
                data.setDeliveryAmount(data.getDeliveryAmount().multiply(rate).setScale(2, BigDecimal.ROUND_HALF_DOWN));
            }

            if (data.getDeliveryAmount().compareTo(BigDecimal.ZERO) < 0) {
                data.setDeliveryAmount(BigDecimal.ZERO);
            }
        }
    }


    /**
     * 查询发货数据
     *
     * @param customerCodeSet
     * @param productCodes
     * @param dateMap
     * @return
     */
    public List<DmsWarehouseOrderDetailVo> findDmsOrderDelivery(Set<String> customerCodeSet, Set<String> productCodes, Map<String, String> dateMap) {
        List<String> customerCodeList = Lists.newArrayList(customerCodeSet);
        List<DmsWarehouseOrderDetailVo> orderDetailVos = Lists.newArrayList();
        List<String> itemTypeList = Lists.newArrayList(
                "normalGoods", "complimentaryGoods", "complimentaryContractGoods", "compensatedGoods");
        if (customerCodeSet.size() > 1000) {
            List<List<String>> partitionCus = Lists.partition(customerCodeList, 800);
            for (List<String> cusList : partitionCus) {
                TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
                dto.setCustomerCodes(Lists.newArrayList(cusList));
                dto.setProductCodes(Lists.newArrayList(productCodes));
                dto.setSearchStartTime(dateMap.get(FormulaGetValueComponent.START_DATE));
                dto.setSearchEndTime(dateMap.get(FormulaGetValueComponent.END_DATE));
                dto.setItemTypeList(itemTypeList);
                dto.setOrderStatistics(BooleanEnum.TRUE.getCapital());
                List<DmsWarehouseOrderDetailVo> orderDetailVoList = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
                if (CollectionUtils.isNotEmpty(orderDetailVoList)) {
                    orderDetailVos.addAll(orderDetailVoList);
                }
            }
        } else {
            TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
            dto.setCustomerCodes(Lists.newArrayList(customerCodeSet));
            if (CollectionUtils.isNotEmpty(customerCodeList)) {
                dto.setProductCodes(Lists.newArrayList(productCodes));
            }
            dto.setSearchStartTime(dateMap.get(FormulaGetValueComponent.START_DATE));
            dto.setSearchEndTime(dateMap.get(FormulaGetValueComponent.END_DATE));
            dto.setItemTypeList(itemTypeList);
            dto.setOrderStatistics(BooleanEnum.TRUE.getCapital());
            orderDetailVos = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
        }

        // 处理货补搭赠、逆向订单
        Set<String> complimentaryTypes =
                CollUtil.newHashSet("complimentaryGoods", "complimentaryContractGoods", "compensatedGoods");
        for (DmsWarehouseOrderDetailVo orderDetailVo : orderDetailVos) {
            if (StrUtil.isNotBlank(orderDetailVo.getItemType())
                    && complimentaryTypes.contains(orderDetailVo.getItemType())) {
                // 货补搭赠类型 取原始金额
                orderDetailVo.setTotalAmount(orderDetailVo.getOriginalTotalAmount());
            }
            if (Objects.isNull(orderDetailVo.getTotalAmount())) {
                orderDetailVo.setTotalAmount(BigDecimal.ZERO);
            }
            if (StrUtil.equals(orderDetailVo.getWarehouseOrderType(),
                    DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode())
                    && orderDetailVo.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal total = orderDetailVo.getTotalAmount().multiply(BigDecimal.valueOf(-1));
                orderDetailVo.setTotalAmount(total);
            }
        }
        return orderDetailVos;
    }


    /**
     * 计算pos拆分占比
     *
     * @param years
     * @param customerCodeSet
     * @return
     */
    public Map<String, BigDecimal> calPosData(String years, Set<String> customerCodeSet) {
        String date = years + "-01";
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        // 获取上个月
        LocalDate lastMonth = localDate.minusMonths(1);
        // 获取上上个月
        LocalDate monthBeforeLast = localDate.minusMonths(2);
        //根据数据字典查询客户
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(FormulaGetValueComponent.CUSTOMER_SPLITTING);
        //上月客户
        List<String> lastMonthCustomer = dictDataVos.stream().filter(e -> FormulaGetValueComponent.LAST_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());
        //本月客户
        List<String> currentMonthCustomer = dictDataVos.stream().filter(e -> FormulaGetValueComponent.CURRENT_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());

        List<PosDataVo> posDataVoList = Lists.newArrayList();
        //判断上个月的客户数据不为空
        if (CollectionUtils.isNotEmpty(lastMonthCustomer)) {
            String dateStr = monthBeforeLast.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            List<String> lastCustomerCodes = Lists.newArrayList();
            for (String s : lastMonthCustomer) {
                if (!customerCodeSet.add(s)) {
                    lastCustomerCodes.add(s);
                }
            }
            if (CollectionUtils.isNotEmpty(lastCustomerCodes)) {
                PosDataVo dto = new PosDataVo();
                dto.setYears(dateStr);
                dto.setCustomerCodes(lastCustomerCodes);
                Page<PosDataVo> page = posDataService.findList(PageRequest.of(0, Integer.MAX_VALUE), dto);
                if (CollectionUtils.isNotEmpty(page.getRecords())) {
                    posDataVoList.addAll(page.getRecords());
                }
            }
        }
        //判断本月客户数据不为空
        if (CollectionUtils.isNotEmpty(currentMonthCustomer)) {
            String dateStr = lastMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            List<String> customerCodes = Lists.newArrayList();
            for (String s : currentMonthCustomer) {
                if (!customerCodeSet.add(s)) {
                    customerCodes.add(s);
                }
            }
            if (CollectionUtils.isNotEmpty(customerCodes)) {
                PosDataVo dto = new PosDataVo();
                dto.setYears(dateStr);
                dto.setCustomerCodes(customerCodes);
                Page<PosDataVo> page = posDataService.findList(PageRequest.of(0, Integer.MAX_VALUE), dto);
                if (CollectionUtils.isNotEmpty(page.getRecords())) {
                    posDataVoList.addAll(page.getRecords());
                }
            }
        }

        Map<String, BigDecimal> postDataMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(posDataVoList)) {
            Map<String, BigDecimal> posCusAmountMap = posDataVoList.stream()
                    .filter(x -> x.getAmount().compareTo(BigDecimal.ZERO) == 1)
                    .collect(Collectors.groupingBy(x -> x.getCustomerCode(),
                            Collectors.mapping(PosDataVo::getAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            for (PosDataVo posDataVo : posDataVoList) {
                if (posCusAmountMap.containsKey(posDataVo.getCustomerCode())) {
                    BigDecimal amount = posCusAmountMap.get(posDataVo.getCustomerCode());
                    BigDecimal rate = posDataVo.getAmount().divide(amount, 8, BigDecimal.ROUND_HALF_DOWN);
                    postDataMap.put(posDataVo.getCustomerCode() + posDataVo.getOrgCode(), rate);
                }
            }
        }
        return postDataMap;
    }


    private Map<String, List<CustomerDestinationOrgMapping>> specialCustomer() {
        Map<String, List<CustomerDestinationOrgMapping>> result = new HashMap<>();
        //根据数据字典查询客户
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(SPECIAL_CUSTOMER_DICT_KEY);
        if (CollUtil.isEmpty(dictDataVos)) {
            return result;
        }
        List<String> destinationCodeList = new ArrayList<>();
        List<CustomerDestinationOrgMapping> destinationOrgMappings = new ArrayList<>();
        for (DictDataVo dataVo : dictDataVos) {
            String destinationCode = dataVo.getDictCode();
            String orgCode = dataVo.getDictValue();
            if (StrUtil.isBlank(destinationCode) || StrUtil.isBlank(orgCode)) {
                continue;
            }
            destinationCodeList.add(destinationCode);
            CustomerDestinationOrgMapping destinationOrgMapping =
                    new CustomerDestinationOrgMapping(destinationCode, orgCode);
            destinationOrgMappings.add(destinationOrgMapping);
        }
        // 根据送达方查询客户
        List<MdmDestinationVo> destinationVoList =
                mdmDestinationVoService.findByDestinationCodes(destinationCodeList);
        if (CollUtil.isEmpty(destinationVoList)) {
            return result;
        }
        // 送达方-客户
        Map<String, String> destinationCustomerMap = destinationVoList.stream().collect(
                Collectors.toMap(MdmDestinationVo::getDestinationCode,
                        MdmDestinationVo::getCustomerCode, (v1, v2) -> v1));
        destinationOrgMappings.forEach(mapping -> {
            String customerCode = destinationCustomerMap.get(mapping.getDestinationCode());
            if (StrUtil.isNotBlank(customerCode)) {
                mapping.setCustomerCode(customerCode);
            }
        });

        return destinationOrgMappings.stream()
                .filter(mapping -> StrUtil.isNotBlank(mapping.getCustomerCode()))
                .collect(Collectors.groupingBy(CustomerDestinationOrgMapping::getCustomerCode));
    }

    private Map<String, BigDecimal> amountGroupByCustomerDirection(Map<String, List<CustomerDestinationOrgMapping>> specialCustomerMap,
                                                                   List<DmsWarehouseOrderDetailVo> orderDetailVos) {
        if (MapUtil.isEmpty(specialCustomerMap)) {
            return new HashMap<>();
        }
        List<DmsWarehouseOrderDetailVo> specialCustomerOrders = orderDetailVos.stream()
                .filter(order -> Objects.nonNull(order.getTotalAmount()))
                //.filter(order -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(order.getWarehouseOrderType()))
                .filter(order -> specialCustomerMap.containsKey(order.getCustomerCode()))
                .filter(order -> StrUtil.isNotBlank(order.getDestinationCode()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(specialCustomerOrders)) {
            return new HashMap<>();
        }
        Map<String, BigDecimal> amountGroupByCustomerDirection = new HashMap<>();
        for (DmsWarehouseOrderDetailVo detailVo : specialCustomerOrders) {
            // 客户编码
            String customerCode = detailVo.getCustomerCode();
            // 送达方编码
            String destinationCode = detailVo.getDestinationCode();
            // 商品编码
            String goodsCode = detailVo.getGoodsCode();
            // 该客户关联的 送达方，以及对应的部门映射
            List<CustomerDestinationOrgMapping> destinationOrgMappings = specialCustomerMap.get(customerCode);
            if (CollUtil.isEmpty(destinationOrgMappings)) {
                continue;
            }
            // 未找到映射，直接忽略
            CustomerDestinationOrgMapping mapping = destinationOrgMappings.stream()
                    .filter(item -> StrUtil.equals(item.getDestinationCode(), destinationCode))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(mapping)) {
                continue;
            }
            // 送达方映射的组织编码
            String orgCode = mapping.getOrgCode();
            // 客户 + 组织 + 商品
            String key = StrUtil.join(StrPool.DOT, customerCode, orgCode, goodsCode);
            // 金额汇总
            BigDecimal amount = amountGroupByCustomerDirection.get(key);
            if (Objects.isNull(amount)) {
                amountGroupByCustomerDirection.put(key, detailVo.getTotalAmount());
            } else {
                BigDecimal sum = amount.add(detailVo.getTotalAmount());
                amountGroupByCustomerDirection.put(key, sum);
            }
        }
        return amountGroupByCustomerDirection;
    }


}
