package com.biz.crm.tpm.admin.web.exports.regioncollect.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/26 17:17
 */
@Data
public class RegionCollectMarketingCaseExportVo {

    private String customerCode;

    private String customerName;

    private String channelTypeName;

    private String years;

    private String startDate;

    private String endDate;

    private String terminalCode;

    private String terminalName;

    private String costCenterName;

    private String bearDepartmentName;

    private String belongDepartmentName;

    private String detailName;

    private String itemName;

    private String actDesc;

    private String cashType;

    private BigDecimal applyAmount;

    private BigDecimal lineNoTaxApplyAmount;

    private String schemeDetailCode;

    private String schemeCode;

    private String schemeName;

    private String createName;

    private String caseType;

    private String levelOrProductName;

    private String conditionFormulaName;

    private String conditionNum;

    private String giveNum;

    private String feeProductOrItemName;

    private BigDecimal displayCardNum;

    private String staffType;

    private BigDecimal basicSalary;

    private BigDecimal attendanceDay;

    private BigDecimal monthPos;
}
