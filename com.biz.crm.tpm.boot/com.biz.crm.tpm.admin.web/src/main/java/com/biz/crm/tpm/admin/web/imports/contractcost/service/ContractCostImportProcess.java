package com.biz.crm.tpm.admin.web.imports.contractcost.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.BusinessBeanUtils;
import com.biz.crm.business.common.base.util.DateStringDealUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.ie.sdk.process.BusinessImportProcess;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.imports.contractcost.model.ContractCostImportVo;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDetailDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDto;
import com.biz.crm.tpm.business.activities.contract.service.ExternalContractService;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesTemplateConfigService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/9 18:11
 */
@Component
@Slf4j
public class ContractCostImportProcess extends BusinessImportProcess<ContractCostImportVo> {


    @Autowired(required = false)
    private ExternalContractService externalContractService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private ActivitiesTemplateConfigService activitiesTemplateConfigService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, ContractCostImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<String, String> headOverallMap = getHeadFiledMap();
        //map转对象
        Map<Integer, Map<String, Object>> headFiledList = Maps.newHashMap();
        for (Map.Entry<Integer, ContractCostImportVo> entry : data.entrySet()) {
            entry.getValue().setIndex(entry.getKey());
            headFiledList.put(entry.getKey(), entry.getValue().getHeadFiledMap());
        }

        Map<Integer, String> allErrMsgMap = Maps.newHashMap();
        List<Map<String, Object>> resultList = Lists.newArrayList();
        headFiledList.forEach((index, objectMap) -> {
            Map<String, Object> map = Maps.newHashMap();
            try {
                for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
                    this.validateIsTrue(headOverallMap.containsKey(entry.getKey()), String.format("未配置字段%s", StringUtils.stripToEmpty(entry.getKey())));
                    String filed = headOverallMap.get(entry.getKey());
                    map.put(filed, entry.getValue());
                }
                map.put("index", index);
                resultList.add(map);

                String errInfo = this.validateGetErrorInfo();
                if (StringUtils.isNotBlank(errInfo)) {
                    allErrMsgMap.put(index, errInfo);
                }
            } catch (Exception e) {
                allErrMsgMap.put(index, e.getMessage());
            }
        });

        List<ContractCostImportVo> list = BusinessBeanUtils.mapListToBeanList(resultList, ContractCostImportVo.class, Boolean.TRUE);

        Boolean no = Boolean.FALSE;
        for (ContractCostImportVo importVo : list) {
            StringJoiner errMsg = new StringJoiner(";");
            this.paramNotNull("合同编码", errMsg, no, importVo.getContractCode());
            if (StringUtils.isNotEmpty(errMsg.toString())) {
                allErrMsgMap.put(importVo.getIndex(),
                        allErrMsgMap.getOrDefault(importVo.getIndex(), "").concat(errMsg.toString()));
            }
        }
        //合同导入
        this.importContractCost(list, allErrMsgMap, no);

        return allErrMsgMap;
    }

    public void importContractCost(List<ContractCostImportVo> list, Map<Integer, String> allErrMsgMap, Boolean no) {
        Map<String, List<ContractCostImportVo>> contractMap = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getContractCode()))
                .collect(Collectors.groupingBy(ContractCostImportVo::getContractCode));
        List<ExternalContractDto> dtoList = Lists.newArrayList();
        Set<String> erpCodeSet = list.stream().filter(k -> StringUtil.isNotEmpty(k.getErpCode()))
                .map(ContractCostImportVo::getErpCode).collect(Collectors.toSet());
        Map<String, Map<String, CustomerVo>> customerVoMap = customerVoService.findByErpCodes(new ArrayList<>(erpCodeSet))
                .stream().collect(Collectors.groupingBy(CustomerVo::getErpCode,
                        Collectors.toMap(CustomerVo::getCompanyCode, Function.identity(), (a, b) -> a)));
        for (Map.Entry<String, List<ContractCostImportVo>> entry : contractMap.entrySet()) {
            ContractCostImportVo costImportVo = entry.getValue().get(0);
            StringJoiner errMsg = new StringJoiner(";");
            this.paramNotNull("合同名称不能为空", errMsg, no, costImportVo.getContractName());
            this.paramNotNull("费用类型不能为空", errMsg, no, costImportVo.getCaseType());
            this.paramNotNull("公司代码不能为空", errMsg, no, costImportVo.getCompanyCode());
            this.paramNotNull("客户ERP编码不能为空", errMsg, no, costImportVo.getErpCode());
            if (StringUtil.isNotEmpty(costImportVo.getErpCode())
                    && StringUtil.isNotEmpty(costImportVo.getCompanyCode())) {
                if (customerVoMap.containsKey(costImportVo.getErpCode())) {
                    Map<String, CustomerVo> companyVoMap = customerVoMap.get(costImportVo.getErpCode());
                    if (companyVoMap.containsKey(costImportVo.getCompanyCode())) {
                        CustomerVo customerVo = companyVoMap.get(costImportVo.getCompanyCode());
                        if (!BooleanEnum.TRUE.getCapital().equals(customerVo.getContractCustomer())) {
                            errMsg.add("客户不是合同客户");
                        }
                        costImportVo.setCustomerCode(customerVo.getCustomerCode());
                        costImportVo.setCustomerName(customerVo.getCustomerName());
                    } else {
                        errMsg.add("客户，未找到！");
                    }
                } else {
                    errMsg.add("客户，未找到！");
                }
            }
            String errorMsg = DateStringDealUtil.validateDateStrAndSetErrorMsg(costImportVo.getContractStartDate(), "合同开始时间", DateUtil.DEFAULT_YEAR_MONTH_DAY, costImportVo::setContractStartTime);
            if (StringUtils.isNotEmpty(errorMsg)) {
                errMsg.add(errorMsg);
            }
            errorMsg = DateStringDealUtil.validateDateStrAndSetErrorMsg(costImportVo.getContractEndDate(), "合同结束时间", DateUtil.DEFAULT_YEAR_MONTH_DAY, costImportVo::setContractEndTime);
            if (StringUtils.isNotEmpty(errorMsg)) {
                errMsg.add(errorMsg);
            }
            if (Objects.nonNull(costImportVo.getContractStartTime())
                    && Objects.nonNull(costImportVo.getContractEndTime())) {
                if (costImportVo.getContractStartTime().compareTo(costImportVo.getContractEndTime()) > 0) {
                    errMsg.add("合同开始时间不能大于合同结束时间");
                }
            }
            this.paramNotNull("合同编码不能为空", errMsg, no, costImportVo.getContractCode());
            for (ContractCostImportVo importVo : entry.getValue()) {
                this.paramNotNull("费用类型不能为空", errMsg, no, costImportVo.getCaseType());
                String code = nameToCode(importVo.getCaseType());
                if (ObjectUtils.isEmpty(code)) {
                    errMsg.add("费用类型填写错误");
                } else {
                    importVo.setCaseType(code);
                }
                try {
                    LocalDate.parse(importVo.getContractStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                } catch (Exception e) {
                    errMsg.add("合同开始时间格式错误,正确格式：yyyy-MM-dd");
                }
                try {
                    LocalDate.parse(importVo.getContractEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                } catch (Exception e) {
                    errMsg.add("合同结束时间格式错误,正确格式：yyyy-MM-dd");
                }
                //判断是返利
                if (QUANTITY_RELATE_CODE.equals(code)) {
                    checkRebateDetail(importVo, errMsg);
                } else if (GIFT_CODE.equals(code)) {
                    //搭赠
                    checkGiftDetail(importVo, errMsg);
                } else if (FIXED_CODE.equals(code)) {
                    //固定
                    checkFixedDetail(importVo, errMsg);
                }
                if (StringUtils.isNotEmpty(errMsg.toString())) {
                    allErrMsgMap.put(importVo.getIndex(),
                            allErrMsgMap.getOrDefault(importVo.getIndex(), "").concat(errMsg.toString()));
                }
            }
            //如果不存在错误信息
            try {
                ExternalContractDto dto = buildParam(costImportVo, entry.getValue());
                dto.setExternalFlag(BooleanEnum.FALSE.getCapital());
                dtoList.add(dto);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                allErrMsgMap.put(costImportVo.getIndex(),
                        allErrMsgMap.getOrDefault(costImportVo.getIndex(), "").concat(e.getMessage()));
            }

        }
        log.error("===========================合同费用导入错误信息：" + JSONObject.toJSONString(allErrMsgMap));
        if (CollectionUtil.isEmpty(allErrMsgMap)) {
            try {
                dtoList.forEach(dto -> {
                    externalContractService.createContract(dto, BooleanEnum.FALSE.getCapital());
                });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw e;
            }
        }
    }

    /**
     * 返利校验
     *
     * @param vo
     */
    private void checkRebateDetail(ContractCostImportVo vo, StringJoiner errMsg) {
        Boolean yes = Boolean.TRUE;
        Boolean no = Boolean.FALSE;
        this.paramNotNull("活动细类", errMsg, no, vo.getDetailCode());
        this.paramNotNull("适用部门编码", errMsg, no, vo.getBelongDepartmentCode());
        this.paramNotNull("适用成本中心编码", errMsg, no, vo.getCostCenterCode());
        this.paramNotNull("政策开始时间", errMsg, no, vo.getStartDate());
        this.paramNotNull("政策结束时间", errMsg, no, vo.getEndDate());
//        this.paramNotNull("考核产品编码和考核品项编码", errMsg, no, vo.getKhProductCodeStr(), vo.getKhItemCodeStr());
        this.paramNotNull("返利产品编码和返利品项编码", errMsg, no, vo.getFlProductCodeStr(), vo.getFlItemCodeStr());
        this.paramNotNull("费用归属品项", errMsg, no, vo.getBelongItemStr());
        this.paramNotNull("返利周期", errMsg, no, vo.getFlzq());
        this.paramNotNull("返利公式", errMsg, no, vo.getFlgs());
        this.paramNotNull("达成条件", errMsg, no, vo.getDctj());
        try {
            LocalDate.parse(vo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        } catch (Exception e) {
            errMsg.add("政策开始时间格式错误,正确格式：yyyy-MM-dd");
        }
        try {
            LocalDate.parse(vo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        } catch (Exception e) {
            errMsg.add("政策结束时间格式错误,正确格式：yyyy-MM-dd");
        }
        //todo 此处的公式还需要校验
    }

    /**
     * 校验搭赠
     *
     * @param vo
     * @param errMsg
     */
    private void checkGiftDetail(ContractCostImportVo vo, StringJoiner errMsg) {
        Boolean yes = Boolean.TRUE;
        Boolean no = Boolean.FALSE;
        this.paramNotNull("活动细类", errMsg, no, vo.getDetailCode());
        this.paramNotNull("适用部门编码", errMsg, no, vo.getBelongDepartmentCode());
        this.paramNotNull("适用成本中心编码", errMsg, no, vo.getCostCenterCode());
        this.paramNotNull("政策开始时间", errMsg, no, vo.getStartDate());
        this.paramNotNull("政策结束时间", errMsg, no, vo.getEndDate());
        this.paramNotNull("本品和小类", errMsg, yes, vo.getBpProductCodeStr(), vo.getBpLevelCodeStr());
        this.paramNotNull("条件数量", errMsg, no, vo.getConditionNum());
        this.paramPositive("条件数量", errMsg, no, vo.getConditionNum());
//        this.paramNotNull("赠品和赠品小类", errMsg, yes, vo.getZpProductCodeStr(), vo.getZpLevelCodeStr());
        this.paramNotNull("赠品数量", errMsg, no, vo.getGiveNum());
        this.paramPositive("赠品数量", errMsg, no, vo.getGiveNum());
    }


    /**
     * 校验固定
     *
     * @param vo
     * @param errMsg
     */
    private void checkFixedDetail(ContractCostImportVo vo, StringJoiner errMsg) {
        Boolean yes = Boolean.TRUE;
        Boolean no = Boolean.FALSE;
        this.paramNotNull("活动细类", errMsg, no, vo.getDetailCode());
        this.paramNotNull("适用部门编码", errMsg, no, vo.getBelongDepartmentCode());
        this.paramNotNull("适用成本中心编码", errMsg, no, vo.getCostCenterCode());
        this.paramNotNull("政策开始时间", errMsg, no, vo.getStartDate());
        this.paramNotNull("政策结束时间", errMsg, no, vo.getEndDate());
        this.paramNotNull("扣费周期", errMsg, no, vo.getFlzq());
        this.paramNotNull("扣费标准", errMsg, no, vo.getKfbz());
        this.paramNotNull("品项", errMsg, no, vo.getBelongItemStr());
    }

    /**
     * 参数构建
     *
     * @param vo
     * @param detailList
     * @return
     */
    private ExternalContractDto buildParam(ContractCostImportVo vo, List<ContractCostImportVo> detailList) {
        ExternalContractDto dto = nebulaToolkitService.copyObjectByBlankList(vo, ExternalContractDto.class, HashSet.class, ArrayList.class);
        dto.setStartDate(vo.getContractStartDate());
        dto.setEndDate(vo.getContractEndDate());
        List<ExternalContractDetailDto> list = Lists.newArrayList();
        for (ContractCostImportVo importVo : detailList) {
            ExternalContractDetailDto detailDto = nebulaToolkitService.copyObjectByBlankList(importVo, ExternalContractDetailDto.class, HashSet.class, ArrayList.class);
            if (QUANTITY_RELATE_CODE.equals(importVo.getCaseType())) {
//                detailDto.setItemStr(importVo.getKhItemCodeStr());
//                detailDto.setProductStr(importVo.getKhProductCodeStr());
                detailDto.setItemStr(importVo.getFlItemCodeStr());
                detailDto.setProductStr(importVo.getFlProductCodeStr());
                detailDto.setFeeItemStr(importVo.getFlItemCodeStr());
                detailDto.setFeeProductStr(importVo.getFlProductCodeStr());
                detailDto.setFeeBelongItemStr(importVo.getBelongItemStr());
                Map<String, String> map = rebateTypeMap();
                Validate.isTrue(map.containsKey(importVo.getFlzq()), "返利类型填写错误");
                detailDto.setRebateType(map.get(importVo.getFlzq()));
//                detailDto.setRebateCalDay(Integer.valueOf(importVo.getFlsj()));
                detailDto.setRebateStandard(importVo.getFlbz());
                detailDto.setConditionFormula(importVo.getFlgs());
                detailDto.setResultFormula(importVo.getDctj());
            } else if (GIFT_CODE.equals(importVo.getCaseType())) {
                detailDto.setProductStr(importVo.getBpProductCodeStr());
                detailDto.setLevelStr(importVo.getBpLevelCodeStr());
                detailDto.setFeeProductStr(importVo.getBpProductCodeStr());
                detailDto.setFeeLevelStr(importVo.getBpLevelCodeStr());
                detailDto.setFeeBelongItemStr(importVo.getBelongItemStr());
            } else if (FIXED_CODE.equals(importVo.getCaseType())) {
                Map<String, String> map = rebateTypeMap();
                Validate.isTrue(map.containsKey(importVo.getFlzq()), "返利类型填写错误");
                detailDto.setRebateType(map.get(importVo.getFlzq()));
                detailDto.setRebateStandard(vo.getKfbz());
                detailDto.setFeeBelongItemStr(vo.getBelongItemStr());

            }
            if (ObjectUtils.isNotEmpty(vo.getCashType())) {
                Map<String, String> map = cashMap();
                Validate.isTrue(map.containsKey(vo.getCashType()), "兑付方式填写错误");
                detailDto.setCashType(map.get(vo.getCashType()));
            }
            list.add(detailDto);
        }
        dto.setDetailList(list);
        return dto;
    }

    @Override
    public Class<ContractCostImportVo> findCrmExcelVoClass() {
        return ContractCostImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "TPM_CONTRACT_COST_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "合同费用台账导入";
    }

    private static final String QUANTITY_RELATE_CODE = "quantity_relate";

    private static final String GIFT_CODE = "gift";

    private static final String FIXED_CODE = "fixed";

    private static final String QUANTITY_RELATE = "量相关";

    private static final String GIFT = "搭赠";

    private static final String FIXED = "固定";

    private String nameToCode(String name) {
        switch (name) {
            case QUANTITY_RELATE:
                return QUANTITY_RELATE_CODE;
            case GIFT:
                return GIFT_CODE;
            case FIXED:
                return FIXED_CODE;
        }
        return null;
    }


    public Boolean paramNotNull(String msg, StringJoiner errMsg, Boolean onlyOne, Object... fileds) {
        long emptyFiledCount = Stream.of(fileds)
                .filter(ObjectUtils::isEmpty)
                .count();

        if (emptyFiledCount == fileds.length) {
            errMsg.add(msg + "不能为空");
            return Boolean.FALSE;
        }
        if (ObjectUtils.isNotEmpty(onlyOne) && onlyOne && fileds.length > 1) {
            long filedNumCount = Stream.of(fileds)
                    .filter(ObjectUtils::isNotEmpty)
                    .count();
            if (filedNumCount != 1) {
                errMsg.add(msg + "有且只能一个有值");
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 数字正数校验
     *
     * @param msg
     * @param errMsg
     * @param integer
     * @param filed
     * @return
     */
    public Boolean paramPositive(String msg, StringJoiner errMsg, Boolean integer, String filed) {
        if (StringUtils.isNotBlank(filed)) {
            Boolean bePositive = Boolean.TRUE;
            String[] split = filed.split(",");
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                try {
                    if (integer) {
                        Integer number = Integer.valueOf(s.trim());
                        Validate.isTrue(number > 0);
                    } else {
                        BigDecimal number = new BigDecimal(s.trim());
                        Validate.isTrue(number.compareTo(BigDecimal.ZERO) > 0);
                    }
                } catch (Exception e) {
                    errMsg.add(msg + "数字格式错误，且必须大于0");
                    bePositive = Boolean.FALSE;
                    break;
                }
            }
            return bePositive;
        }
        return Boolean.TRUE;
    }

    //返利类型
    private final static ThreadLocal<Map<String, String>> rebateTypeThreadLocal = new ThreadLocal<>();
    //兑付方式
    private final static ThreadLocal<Map<String, String>> cashThreadLocal = new ThreadLocal<>();

    /**
     * 返利类型
     *
     * @return
     */
    private Map<String, String> rebateTypeMap() {
        Map<String, String> map = rebateTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_REBATE_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            rebateTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 兑付方式
     *
     * @return
     */
    private Map<String, String> cashMap() {
        Map<String, String> map = cashThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_SCHEME_CASH_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            cashThreadLocal.set(map);
        }
        return map;
    }

    //活动配置模板
    private final static ThreadLocal<Map<String, String>> templateThreadLocal = new ThreadLocal<>();

    public Map<String, String> getHeadFiledMap() {
        Map<String, String> headOverallMap = templateThreadLocal.get();
        if (ObjectUtils.isEmpty(headOverallMap)) {
            ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(OverallPlanConstant.CONTRACT_COST_TEMPLATE);
            Validate.notNull(config, "总部指引配置模版为空");
            List<ActivitiesTemplateConfigDetailVo> detailList = new ArrayList<>(config.getDetails());
            headOverallMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
            templateThreadLocal.set(headOverallMap);
        }
        return headOverallMap;
    }

    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    @Override
    public Integer getHeadTitleRowIndex() {
        return 0;
    }

    @Override
    public Map<Integer, String> analysisHeadFieldMap(Map<String, Object> params, Map<Integer, String> headMap) {
        return null;
    }
}
