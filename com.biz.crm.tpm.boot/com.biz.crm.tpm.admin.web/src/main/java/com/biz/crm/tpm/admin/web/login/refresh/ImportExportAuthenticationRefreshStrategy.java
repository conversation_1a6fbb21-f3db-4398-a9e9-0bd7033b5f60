package com.biz.crm.tpm.admin.web.login.refresh;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.tpm.admin.web.login.DefaultPerfectLoginUserDetails;
import com.bizunited.nebula.common.util.tenant.TenantContextHolder;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.common.vo.SimpleTenantInfo;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.bizunited.nebula.security.sdk.refresh.AuthenticationRefreshStrategy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 导入导出任务所需要的用户身份初始化动作
 *
 * <AUTHOR> yinwenjie
 * @since 2022-08-28 由银文杰进行修改
 */
@Component
public class ImportExportAuthenticationRefreshStrategy extends DefaultPerfectLoginUserDetails implements AuthenticationRefreshStrategy {

  /** nebula security模块中，关于默认用户身份信息的配置情况 */
  @Autowired
  private SimpleSecurityProperties simpleSecurityProperties;
  
  @Override
  public int getOrder() {
    return 8;
  }

  @Override
  public boolean matched(Object info) {
    if(info != null && (info instanceof TaskGlobalParamsVo)) {
      return true;
    }
    return false;
  }

  /**
   * 根据导入导出任务公共信息设置登录信息
   *
   * @deprecated 需要在导入导出方面提供相关的支持
   *
   * @param info 任务参数vo
   */
  @Override
  public UserIdentity refresh(Object info) {
    TaskGlobalParamsVo vo = (TaskGlobalParamsVo)info;
    String account = this.simpleSecurityProperties.getIndependencyUser();
    String userName = this.simpleSecurityProperties.getIndependencyUser();
    String relaName = "超级管理员";
    if (StringUtils.isNotBlank(vo.getCreateAccount())) {
      account = vo.getCreateAccount();
      userName = vo.getCreateAccount();
    }
    if (StringUtils.isNotBlank(vo.getCreateAccountName())) {
      relaName = vo.getCreateAccountName();
    }

    if (StringUtils.isNotBlank(vo.getTenantCode())) {
      SimpleTenantInfo simpleTenantInfo = new SimpleTenantInfo(vo.getTenantCode());
      TenantContextHolder.setTenantInfo(simpleTenantInfo);
    }
    String tenantCode = TenantUtils.getTenantCode();
    if (StringUtils.isBlank(tenantCode)) {
      SimpleTenantInfo simpleTenantInfo = new SimpleTenantInfo("default");
      TenantContextHolder.setTenantInfo(simpleTenantInfo);
    }
    String[] roles = this.simpleSecurityProperties.getIndependencyRoles();
    Integer type = this.simpleSecurityProperties.getDefaultLoginType();
    
    // 初始化用户
    FacturerUserDetails facturer = new FacturerUserDetails();
    facturer.setAccount(account);
    // 厂商用户（超级管理员）
    facturer.setLoginType(type);
    facturer.setIdentityType("u");
    facturer.setRoleCodes(roles);
    facturer.setUsername(userName);
    facturer.setRealName(relaName);
    facturer.setTenantCode(TenantUtils.getTenantCode());
    
    // 完善登录信息中的岗位组织等信息
    super.perfectLoginPostAndOrg(facturer);
    
    return facturer;
  }
}
