package com.biz.crm.tpm.admin.web.exports.dataview.auth;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 该数据表负责在诸如mdm-boot、DMS-boot、TPM-boot这类基于厂商用户使用的应用程序中，
 * 记录导入导出任务和创建它的厂商用户的映射关系
 * <AUTHOR>
 *
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "facturer_import_export_auth_mapping")
@TableName(value = "facturer_import_export_auth_mapping")
@org.hibernate.annotations.Table(appliesTo = "facturer_import_export_auth_mapping")
public class FacturerImportExportAuthEntity extends TenantEntity {
  private static final long serialVersionUID = -3096006459006794701L;

  /**
   * 厂商用户创建导入导出任务时使用的岗位编号信息
   */
  @TableField(value = "post_code")
  @Column(name = "post_code", length = 64, columnDefinition = "varchar(64) COMMENT '厂商用户创建导入导出任务时使用的岗位编号信息'")
  private String postCode;
  /**
   * 厂商用户创建导入导出任务时使用的顶级租户编号信息
   */
  @TableField(value = "app_code")
  @Column(name = "app_code", length = 64, columnDefinition = "varchar(64) COMMENT '厂商用户创建导入导出任务时使用的顶级租户编号信息'")
  private String appCode;
  /**
   * 厂商用户创建导入导出任务时使用的应用程序信息
   */
  @TableField(value = "application_name")
  @Column(name = "application_name", length = 64, columnDefinition = "varchar(64) COMMENT '厂商用户创建导入导出任务时使用的顶级租户编号信息'")
  private String applicationName;
  /**
   * 厂商用户创建导入导出任务时使用的账号信息
   */
  @TableField(value = "create_account")
  @Column(name = "create_account", length = 255, columnDefinition = "varchar(255) COMMENT '厂商用户创建导入导出任务时使用的账号信息'")
  private String createAccount;
  /**
   * 厂商用户创建导入导出任务时任务在系统中的唯一业务编号信息
   */
  @TableField(value = "task_code")
  @Column(name = "task_code", length = 255, columnDefinition = "varchar(255) COMMENT '该数据表负责在诸如mdm-boot、DMS-boot、TPM-boot这类基于厂商用户使用的应用程序中'")
  private String taskCode;
}
