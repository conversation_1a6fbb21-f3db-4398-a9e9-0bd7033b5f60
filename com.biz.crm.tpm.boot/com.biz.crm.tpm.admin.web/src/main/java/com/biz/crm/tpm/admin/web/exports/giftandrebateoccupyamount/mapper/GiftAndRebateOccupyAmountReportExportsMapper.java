package com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.mapper;

import com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.model.GiftAndRebateOccupyAmountReportExportsDto;
import com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.model.GiftAndRebateOccupyAmountReportExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 *
 * <AUTHOR>
 */
public interface GiftAndRebateOccupyAmountReportExportsMapper {

    Integer getExportTotal(@Param("dto") GiftAndRebateOccupyAmountReportExportsDto dto);


    List<GiftAndRebateOccupyAmountReportExportsVo> findData(@Param("dto") GiftAndRebateOccupyAmountReportExportsDto dto);
}
