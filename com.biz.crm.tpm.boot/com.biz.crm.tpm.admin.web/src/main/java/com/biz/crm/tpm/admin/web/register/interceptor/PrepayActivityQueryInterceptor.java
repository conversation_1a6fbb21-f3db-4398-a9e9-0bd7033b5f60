package com.biz.crm.tpm.admin.web.register.interceptor;

import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.europa.database.sdk.strategy.ExternalQueryInterceptor;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteContent;
import com.bizunited.nebula.europa.sdk.context.matedata.MetaData;
import com.bizunited.nebula.europa.sdk.vo.EuropaInfoVo;
import com.google.common.collect.Lists;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/16 10:19
 * @ClassName PrepayActivityQueryInterceptor
 * @Description TODO 活动预付数据视图拦截器参数补充
 */
@Component
public class PrepayActivityQueryInterceptor implements ExternalQueryInterceptor {

  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String name() {
    return "TPM活动预付数据视图拦截器";
  }

  @Override
  public List<Object[]> process(EntityManager entityManager, MetaData metaData,
      EuropaInfoVo europaInfoVo, ExecuteContent executeContent, String... strings) {
    //取出results
    //判空
    List<Map<String, Object>> results = executeContent.getResults();
    if (CollectionUtils.isEmpty(results)) {
      return null;
    }
    if (0 == results.parallelStream().filter(row -> row != null).count()) {
      return org.apache.commons.compress.utils.Lists.newArrayList();
    }
    //取出需要的数据并封装
    List<Object[]> externalContents = Lists.newArrayList();
    results.forEach(result -> {
      String processStatus = (String) result.get("process_status");
      processStatus = StringUtils.isNotEmpty(processStatus) ? processStatus
          : ProcessStatusEnum.PREPARE.getDictCode();
      List<Object> itemList = Lists.newArrayList();
      for (String externalFileName : strings) {
        if (StringUtils.equals(externalFileName, "processStatus")) {
          if (StringUtils.isNotEmpty(processStatus)) {
            itemList.add(processStatus);
          } else {
            itemList.add("");
          }
        }
      }
      externalContents.add(itemList.toArray(new Object[]{}));
    });
    return externalContents;
  }

}
