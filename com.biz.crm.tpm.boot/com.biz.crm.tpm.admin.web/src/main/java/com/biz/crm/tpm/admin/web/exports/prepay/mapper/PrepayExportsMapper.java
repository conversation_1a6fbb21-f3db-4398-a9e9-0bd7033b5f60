package com.biz.crm.tpm.admin.web.exports.prepay.mapper;

import com.biz.crm.tpm.admin.web.exports.prepay.model.PrepayExportsVo;
import com.biz.crm.tpm.admin.web.exports.prepay.model.PrepayExportsDto;

import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 活动预付;(tpm_prepay)表导出功能数据库访问层
 * <AUTHOR> <PERSON>
 * @date : 2022-7-2
 */
public interface PrepayExportsMapper{
  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") PrepayExportsDto dto);
  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<PrepayExportsVo> findData(@Param("dto") PrepayExportsDto dto);
}