////
//// Source code recreated from a .class file by IntelliJ IDEA
//// (powered by Fernflower decompiler)
////
//
//package com.biz.crm.common.error.logs.local.intercept;
//
//import com.biz.crm.business.common.sdk.model.Result;
//import com.biz.crm.common.error.logs.local.utils.ErrorLogUtil;
//import com.bizunited.nebula.common.controller.model.ResponseModel;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.core.MethodParameter;
//import org.springframework.http.MediaType;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.http.server.ServerHttpRequest;
//import org.springframework.http.server.ServerHttpResponse;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
//
//import java.util.Objects;
//
///**
// * 呵呵呵呵
// *
// * @author: huxmld
// * @version: v1.0.0
// * @date: 2024/7/25 23:43
// */
//@ControllerAdvice
//@Slf4j
//public class ErrorLogInterceptResponse implements ResponseBodyAdvice<Object> {
//
//    @Override
//    public boolean supports(MethodParameter returnType,
//                            Class<? extends HttpMessageConverter<?>> converterType) {
//        return true;
//    }
//
//    @Override
//    public Object beforeBodyWrite(Object body, MethodParameter returnType,
//                                  MediaType selectedContentType,
//                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
//                                  ServerHttpRequest request, ServerHttpResponse response) {
//        if (Objects.isNull(body)) {
//            return body;
//        }
//        if (body instanceof ResponseModel) {
//            ResponseModel responseModel = (ResponseModel) body;
//            if (responseModel.getSuccess()) {
//                return body;
//            }
//
//            responseModel.setErrorMsg(ErrorLogUtil.convertSystemMessage(responseModel.getErrorMsg()));
//            if (ErrorLogUtil.checkNeedBuildErrorCode(responseModel.getErrorMsg())
//                    && StringUtils.isNotBlank(ErrorLogUtil.getCurrentErrorCode())) {
//                responseModel.setErrorMsg(String.format("%s%s", responseModel.getErrorMsg(), ErrorLogUtil.getCurrentErrorCode()));
//            }
//        } else if (body instanceof Result) {
//            Result<?> result = (Result) body;
//            if (result.isSuccess()) {
//                return body;
//            }
//            result.setMessage(ErrorLogUtil.convertSystemMessage(result.getMessage()));
//            if (ErrorLogUtil.checkNeedBuildErrorCode(result.getMessage())
//                    && StringUtils.isNotBlank(ErrorLogUtil.getCurrentErrorCode())) {
//                result.setMessage(String.format("%s%s", result.getMessage(), ErrorLogUtil.getCurrentErrorCode()));
//            }
//        }
//        ErrorLogUtil.clearThreadLocal();
//        return body;
//    }
//}
