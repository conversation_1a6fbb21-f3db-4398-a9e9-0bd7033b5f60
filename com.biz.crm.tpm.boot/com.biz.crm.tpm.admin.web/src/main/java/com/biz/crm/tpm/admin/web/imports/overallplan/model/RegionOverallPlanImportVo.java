package com.biz.crm.tpm.admin.web.imports.overallplan.model;

import com.biz.crm.business.common.ie.sdk.vo.BusinessCrmExcelVo;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/23
 */
@Data
@CrmExcelImport(startRow = 2)
public class RegionOverallPlanImportVo extends BusinessCrmExcelVo {

    @ApiModelProperty("关联总部明细编码")
    private String headSchemeDetailCode;

    @ApiModelProperty("二级费用大类编码")
    private String secondCostCategory;

    @ApiModelProperty("二级费用大类名称")
    private String secondCostCategoryName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("销售部门编码")
    private String bearDepartmentCodeList;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("合作类型标签")
    private String cooperateTypeList;

    @ApiModelProperty("客户标签")
    private String customerTagList;

    @ApiModelProperty("客户")
    private String customerCodeList;

    @ApiModelProperty("终端标签")
    private String terminalTagList;

    @ApiModelProperty("终端编码")
    private String terminalCodeList;

    @ApiModelProperty("品项范围")
    private String itemList;

    @ApiModelProperty("品项编码范围")
    private String itemCodeList;

    @ApiModelProperty("产品范围")
    private String productList;

    @ApiModelProperty("产品编码范围")
    private String productCodeList;

    @ApiModelProperty("预估费用")
    private String applyAmount;

    @ApiModelProperty("预估销售额")
    private String estimatedSalesVolume;

    @ApiModelProperty("兑付条件及说明")
    private String cashCondition;

    @ApiModelProperty("是否可以承接")
    private String bearFlag;

    @ApiModelProperty("归属年月")
    private String years;

    @ApiModelProperty("承接类型")
    private String bearType;






}
