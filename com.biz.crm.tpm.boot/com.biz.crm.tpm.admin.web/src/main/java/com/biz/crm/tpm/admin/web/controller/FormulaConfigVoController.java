package com.biz.crm.tpm.admin.web.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.rebate.local.service.FormulaConfigService;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2024/7/19
 */
@RequestMapping("/v1/saleRebateFormulaConfig/saleRebateFormulaConfig")
@RestController
@Api(tags = "公式")
public class FormulaConfigVoController {

    @Autowired(required = false)
    private FormulaConfigService formulaConfigService;

    @Autowired
    private MarketingPlanCaseService marketingPlanCaseService;

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "删除")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "ids", value = "主键id集合") @RequestParam List<String> ids) {
        StringJoiner errMsg = new StringJoiner(";");
        for (String id : ids) {
            try {
                FormulaConfigVo vo = formulaConfigService.findById(id, null);
                if (ObjectUtils.isNotEmpty(vo)) {
                    List<MarketingPlanCase> cases = marketingPlanCaseService.findListByFormulaCode(vo.getFormulaConfigCode());
                    Validate.isTrue(CollectionUtils.isEmpty(cases), String.format("公式%s正在使用,不可删除", vo.getFormulaConfigName()));
                    this.formulaConfigService.delete(Lists.newArrayList(id));
                }
            } catch (Exception e) {
                errMsg.add(e.getMessage());
            }
        }
        if (ObjectUtils.isNotEmpty(errMsg.toString())) {
            return Result.error(errMsg.toString());
        }
        return Result.ok();
    }


    /**
     * 禁用
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "禁用", httpMethod = "PATCH")
    @PatchMapping("/disable")
    public Result<?> disable(@RequestBody List<String> ids) {
        StringJoiner errMsg = new StringJoiner(";");
        for (String id : ids) {
            try {
                FormulaConfigVo vo = formulaConfigService.findById(id, null);
                if (ObjectUtils.isNotEmpty(vo)) {
                    List<MarketingPlanCase> cases = marketingPlanCaseService.findListByFormulaCode(vo.getFormulaConfigCode());
                    Validate.isTrue(CollectionUtils.isEmpty(cases), String.format("公式%正在使用,不可禁用", vo.getFormulaConfigName()));
                    this.formulaConfigService.disableBatch(Lists.newArrayList(id));
                }
            } catch (Exception e) {
                errMsg.add(e.getMessage());
            }
        }
        if (ObjectUtils.isNotEmpty(errMsg.toString())) {
            return Result.error(errMsg.toString());
        }
        return Result.ok();

    }
}
