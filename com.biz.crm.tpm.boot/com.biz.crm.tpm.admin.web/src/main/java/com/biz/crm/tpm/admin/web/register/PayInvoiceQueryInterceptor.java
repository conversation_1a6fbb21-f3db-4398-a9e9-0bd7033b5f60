package com.biz.crm.tpm.admin.web.register;

import com.biz.crm.tpm.business.pay.sdk.service.InvoiceDetailService;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceDetailVo;
import com.bizunited.nebula.europa.database.sdk.strategy.ExternalQueryInterceptor;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteContent;
import com.bizunited.nebula.europa.sdk.context.matedata.MetaData;
import com.bizunited.nebula.europa.sdk.vo.EuropaInfoVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 费用池数据视图补充
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Component
public class PayInvoiceQueryInterceptor implements ExternalQueryInterceptor {

    private static final String INVOICE_NO = "invoice_no";

    private static final String PRICE_AND_TAX = "price_and_tax";

    @Autowired
    private InvoiceDetailService invoiceDeailService;

    @Override
    public String code() {
        return "tpm_pay_invoice_query_interceptor";
    }

    @Override
    public String name() {
        return "tpm费用池-参数补充";
    }

    @Override
    public List<Object[]> process(EntityManager entityManager, MetaData metaData, EuropaInfoVo europaInfoVo
            , ExecuteContent executeContent, String... strings) {
        List<Map<String, Object>> results = executeContent.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return Lists.newLinkedList();
        }
        if (0 == results.parallelStream().filter(row -> row != null).count()) {
            return Lists.newLinkedList();
        }
        List<Object[]> externalContents = Lists.newArrayList();
//        Set<String> invoiceNos = results.stream().filter(resultMap -> Objects.nonNull(resultMap.get(INVOICE_NO)))
//                .map(resultMap -> String.valueOf(resultMap.get(INVOICE_NO)))
//                .collect(Collectors.toSet());
//        Map<String, List<InvoiceDetailVo>> detailsMap = Maps.newHashMap();
//        List<InvoiceDetailVo> detailVos = invoiceDeailService.findListByInvoiceNoList(Lists.newArrayList(invoiceNos));
//        if (!CollectionUtils.isEmpty(detailsMap)) {
//            detailsMap = detailVos.stream().collect(Collectors.groupingBy(InvoiceDetailVo::getInvoiceNo));
//        }
//        for (Map<String, Object> result : results) {
//            buildInvoiceInfo(externalContents, detailsMap, result, strings);
//        }
        return externalContents;
    }

}
