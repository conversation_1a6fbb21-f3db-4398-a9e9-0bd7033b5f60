package com.biz.crm.tpm.admin.web.preactualdetailreport.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.admin.web.preactualdetailreport.entity.PreActualDetailReportEntity;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 15:20
 **/
public interface PreActualDetailReportMapper extends BaseMapper<PreActualDetailReportEntity> {

    List<MarketingPlanCaseVo> findCaseListByYears(@Param("years") String years);
}
