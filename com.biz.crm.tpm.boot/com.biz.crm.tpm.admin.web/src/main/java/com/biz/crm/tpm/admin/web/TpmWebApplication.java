package com.biz.crm.tpm.admin.web;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@MapperScan(value = {"com.biz.crm.**.mapper*"})
@EnableFeignClients(basePackages = {"com.biz.crm.*"})
@SpringBootApplication(scanBasePackages = {"com.biz.crm.*", "com.bizunited.nebula.*"})
@EnableTransactionManagement
@Slf4j
@EnableDiscoveryClient
@EnableElasticsearchRepositories(basePackages = {"com.biz.crm.**.repository*", "com.biz.crm.common.log.local.repository"})
public class TpmWebApplication {
    public static void main(String[] args) {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        System.setProperty("druid.mysql.usePingMethod","false");
        ConfigurableApplicationContext application = new SpringApplicationBuilder(TpmWebApplication.class).bannerMode(Banner.Mode.OFF).web(WebApplicationType.SERVLET).run(args);
        Environment env = application.getEnvironment();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        log.info("\n----------------------------------------------------------\n\t" +
                "Application tpm is running! Access URLs:\n\t" +
                "本地访问地址: \t\thttp://localhost:" + port + path + "/\n\t" +
                "swagger地址: \t\thttp://localhost:" + port + path + "/doc.html\n" +
                "----------------------------------------------------------");
    }
}
