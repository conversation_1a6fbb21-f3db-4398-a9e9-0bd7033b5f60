package com.biz.crm.tpm.admin.web.exports.creditorder.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.creditorder.mapper.CreditOrderTicketExportMapper;
import com.biz.crm.tpm.admin.web.exports.creditorder.model.CreditOrderTicketExportsDto;
import com.biz.crm.tpm.admin.web.exports.creditorder.model.CreditOrderTicketExportsVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 【贷项订单台账】导出明细
 *
 * <AUTHOR>
 */
@Component
public class CreditOrderTicketExportsProcess implements ExportProcess<CreditOrderTicketExportsVo> {

    private static final String SAP_ORDER_TYPE = "sap_order_type";

    public static final String TICKET_STATUS = "ticket_status";

    @Autowired
    private DictDataVoService dictDataVoService;
    @Autowired
    private CreditOrderTicketExportMapper creditOrderTicketExportMapper;

    @Override
    public Integer getTotal(Map<String, Object> params) {

        return creditOrderTicketExportMapper.getExportTotal(new CreditOrderTicketExportsDto(this.convertEuropaParam(params)));
    }
    @Override
    public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
        Map<String, Object> nParams = this.convertEuropaParam(params);
        nParams.putIfAbsent("offset",this.getPageSize()*vo.getPageNo());
        nParams.putIfAbsent("limit",vo.getPageSize());
        List<CreditOrderTicketExportsVo> data = creditOrderTicketExportMapper.findData(new CreditOrderTicketExportsDto(nParams));
        //调整
        adjustData(data);
        return toJSONArray(data);
    }

    /**
     * 调整数据
     *
     * @param data
     */
    private void adjustData(List<CreditOrderTicketExportsVo> data) {
        if(CollectionUtils.isEmpty(data)){
            return;
        }
        Map<String, List<DictDataVo>> mapDict = dictDataVoService.findByDictTypeCodeList(Lists.newArrayList(SAP_ORDER_TYPE,TICKET_STATUS));
        for (CreditOrderTicketExportsVo datum : data) {
            datum.setOrderType(this.findDictValue(mapDict,SAP_ORDER_TYPE,datum.getOrderType()));
            datum.setTicketStatus(this.findDictValue(mapDict,TICKET_STATUS,datum.getTicketStatus()));
        }

    }

    @Override
    public String getBusinessCode() {
        return "TPM_CREDIT_ORDER_TICKET_EXPORT";
    }

    @Override
    public String getBusinessName() {
        return "贷项订单台账明细导出";
    }

    public Map<String, Object> transferParams(Map<String, Object> params) {
        Objects.requireNonNull(params, "请求参数不能为空");
        Map<String, Object> nParams = params.entrySet().stream()
                .filter(entry -> entry.getKey() != null && !entry.getKey().isEmpty())
                .collect(Collectors.toMap(
                        entry -> entry.getKey().contains("_") ? entry.getKey().substring(0, entry.getKey().indexOf("_")) : entry.getKey(),
                        Map.Entry::getValue,
                        (existing, replacement) -> existing
                ));
        nParams.put("tenantCode", TenantUtils.getTenantCode());
        return nParams;
    }

    /**
     * 获取字典值
     *
     * @param mapDict
     * @param dictTypeCode
     * @param code
     * @return
     */
    private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
        if (Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
            return StringUtils.EMPTY;
        }
        final List<DictDataVo> vos = mapDict.get(dictTypeCode);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
            return StringUtils.EMPTY;
        }
        final Optional<String> first =
                vos.stream()
                        .filter(a -> a.getDictCode().equals(code))
                        .map(DictDataVo::getDictValue)
                        .findFirst();
        return first.orElse(StringUtils.EMPTY);
    }
}