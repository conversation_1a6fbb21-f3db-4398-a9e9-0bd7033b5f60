package com.biz.crm.tpm.admin.web.exports.activitiedetail.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.dto.OrgQueryDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.admin.web.exports.activitiedetail.mapper.MarketingPlanCaseExportsMapper;
import com.biz.crm.tpm.admin.web.exports.activitiedetail.model.MarketingPlanCaseExportsDto;
import com.biz.crm.tpm.admin.web.exports.activitiedetail.model.MarketingPlanCaseExportsVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class MarketingPlanCaseExportsProcess   implements ExportProcess<MarketingPlanCaseExportsVo> {

    @Autowired(required = false)
    OrgVoService orgVoService;

    @Autowired(required = false)
    WithHoldingService withHoldingService;

    @Autowired(required = false)
    MarketingPlanCaseExportsMapper marketingPlanCaseExportsMapper;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;


    private static final String tpm_marketing_scheme_type = "tpm_marketing_scheme_type";
    private static final String tpm_scheme_cash_type = "tpm_scheme_cash_type";
    private static final String audit_status = "audit_status";
    private static final String cash_status = "cash_status";

    private static final String cash_type = "TPM_SCHEME_CASE_TYPE";


    private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
        if (Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
            return StringUtils.EMPTY;
        }
        final List<DictDataVo> vos = mapDict.get(dictTypeCode);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
            return StringUtils.EMPTY;
        }
        final Optional<String> first =
                vos.stream()
                        .filter(a -> a.getDictCode().equals(code))
                        .map(DictDataVo::getDictValue)
                        .findFirst();
        return first.orElse(StringUtils.EMPTY);
    }
    @Override
    public Integer getTotal(Map<String, Object> params) {


        MarketingPlanCaseExportsDto marketingPlanCaseExportsDto1 = convertParams(params);
        String s = JSONObject.toJSONString(marketingPlanCaseExportsDto1);
        MarketingPlanCaseExportsVo marketingPlanCaseExportsVo = JSONObject.parseObject(s, MarketingPlanCaseExportsVo.class);

        if (ObjectUtils.isNotEmpty(marketingPlanCaseExportsVo.getRegionName())) {
            Set<String> orgCodeSet = orgVoService.findByOrgQueryDto(new OrgQueryDto() {{
                this.setOrgName(marketingPlanCaseExportsVo.getRegionName());
                this.setOrgType(OrgTypeEnum.DIVISION.getDictCode());
            }});
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(orgCodeSet)) {
                return 0;
            }
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCodes(Lists.newArrayList(orgCodeSet));
            List<String> orgCodeList = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            marketingPlanCaseExportsVo.setOrgCodeList(orgCodeList);
        }

        marketingPlanCaseExportsDto1.put("changeFlag",BooleanEnum.FALSE.getCapital());
        String tenantCode = TenantUtils.getTenantCode();
        marketingPlanCaseExportsDto1.putIfAbsent("tenantCode", tenantCode);

        if(marketingPlanCaseExportsDto1.containsKey("headSchemaName")){
            Object headSchemaName = marketingPlanCaseExportsDto1.get("headSchemaName");
            if(Objects.nonNull(headSchemaName)){
                String headSchemaNameStr = headSchemaName.toString();
                marketingPlanCaseExportsDto1.put("headSchemaName",headSchemaNameStr);
                marketingPlanCaseExportsVo.setHeadSchemeName(headSchemaNameStr);
                List<String> res = marketingPlanCaseExportsMapper.getCondition1(marketingPlanCaseExportsVo);
                if(CollectionUtils.isEmpty(res)){
                    return 0;
                }
                marketingPlanCaseExportsDto1.remove("headSchemaName");
                marketingPlanCaseExportsVo.setHeadSchemeName(null);

                if(!CollectionUtils.isEmpty(res)){
                    marketingPlanCaseExportsVo.setHeadSchemeCodes(res);
                }
            }
        }
        Integer exportTotal = marketingPlanCaseExportsMapper.getExportTotal(marketingPlanCaseExportsVo,tenantCode);
        return  exportTotal == null?0:exportTotal;
    }

    private MarketingPlanCaseExportsDto convertParams(Map<String, Object> params) {
        params.putIfAbsent("tenantCode",TenantUtils.getTenantCode());

        params = this.convertEuropaParam(params);
        return new MarketingPlanCaseExportsDto(params);
    }

        @Override
    public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
            MarketingPlanCaseExportsDto dto = convertParams(params);
            dto.putIfAbsent("offset",this.getPageSize()*vo.getPageNo());
            dto.putIfAbsent("limit",vo.getPageSize());
            MarketingPlanCaseVo vos = JSONObject.parseObject(JSONObject.toJSONString(dto), MarketingPlanCaseVo.class);

            if (ObjectUtils.isNotEmpty(vos.getRegionName())) {
                Set<String> orgCodeSet = orgVoService.findByOrgQueryDto(new OrgQueryDto() {{
                    this.setOrgName(vos.getRegionName());
                    this.setOrgType(OrgTypeEnum.DIVISION.getDictCode());
                }});
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(orgCodeSet)) {
                    return new JSONArray();
                }
                List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCodes(Lists.newArrayList(orgCodeSet));
                List<String> orgCodeList = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                vos.setOrgCodeList(orgCodeList);
            }
            Page<MarketingPlanCaseVo> marketingPlanCaseReportList = withHoldingService.findMarketingPlanCaseReportList(PageRequest.of((int)dto.get("offset"), (int)dto.get("limit")), vos);
            List<MarketingPlanCaseVo> records = marketingPlanCaseReportList.getRecords();
            if(CollectionUtils.isEmpty(records)){
                return new JSONArray();
            }
            String s = JSONArray.toJSONString(records);
            List<MarketingPlanCaseExportsVo> list = JSONArray.parseArray(s, MarketingPlanCaseExportsVo.class);
            adjustData(list);
            return toJSONArray(list);
    }

    private void adjustData(List<MarketingPlanCaseExportsVo> data) {
        if(CollectionUtils.isEmpty(data)){

            return;
        }
        Map<String, List<DictDataVo>> mapDict = dictDataVoService.findByDictTypeCodeList(Lists.newArrayList(tpm_marketing_scheme_type,tpm_scheme_cash_type,audit_status,cash_status,cash_type));
        for (MarketingPlanCaseExportsVo vo : data) {
            vo.setCashStatus(this.findDictValue(mapDict, cash_status, vo.getCashStatus()));
            vo.setSchemeType(this.findDictValue(mapDict,tpm_marketing_scheme_type,vo.getSchemeType()));
            vo.setAuditStatus(this.findDictValue(mapDict,audit_status,vo.getAuditStatus()));
            vo.setCashType(this.findDictValue(mapDict,tpm_scheme_cash_type,vo.getCashType()));
            vo.setCaseType(this.findDictValue(mapDict,cash_type,vo.getCaseType()));
        }
    }
        @Override
    public String getBusinessCode() {
        return "TPM_HDMXBB_EXPORTS";
    }

    @Override
    public String getBusinessName() {
        return "活动明细报表";
    }
}
