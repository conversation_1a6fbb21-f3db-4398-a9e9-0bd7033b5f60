package com.biz.crm.tpm.admin.web.imports.materialdemand.model;

import com.biz.crm.business.common.ie.sdk.vo.BusinessCrmExcelVo;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@CrmExcelImport
@Data
@ApiModel("物料需求提报明细表")
public class MaterialDemandDetailImportVo extends BusinessCrmExcelVo {

    @ApiModelProperty("组织编码")
    @CrmExcelColumn("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @CrmExcelColumn("组织名称")
    private String orgName;

    @ApiModelProperty("物料编码")
    @CrmExcelColumn("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    @CrmExcelColumn("物料名称")
    private String materialName;

    @ApiModelProperty("物料数量")
    @CrmExcelColumn("物料数量")
    private String materialNum;
}
