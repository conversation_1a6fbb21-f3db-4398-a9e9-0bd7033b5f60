package com.biz.crm.tpm.admin.web.exports.dataview.auth;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.common.ie.sdk.event.ImportExportTaskEventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 该监听器在导入导出任务进行创建时，记录当前创建导入导出任务的厂商用户信息。
 * 最主要是把当前厂商人员的岗位信息记录下来，以便导入导出任务正式运行时使用。</p>
 * 
 * 该监听器的实现，适合于所有基于厂商用户进行功能操作的boot。例如mdm-boot、DMS-boot、TPM-boot</p>
 * 
 * 为什么需要单独的一张数据表FacturerImportExportAuthEntity来记录创建导入导出任务是厂商操作者的岗位信息信息呢？</br>
 * 原因是在导入导出任务创建到导入导出任务正式执行的过程中，操作者可能发生了岗位信息的切换。
 * 
 * <AUTHOR>
 *
 */
@Component
public class FacturerImportExportAuthRecordListener implements ImportExportTaskEventListener {

  @Autowired
  private LoginUserService loginUserService;
  
  /**
   * 日志
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(FacturerImportExportAuthRecordListener.class);
  @Autowired
  private FacturerImportExportAuthRepository facturerImportExportAuthRepository;
  
  @Override
  public void onTaskCreate(boolean isExportTask, String taskCode, String appCode, String tenantCode,
                           String applicationName, String createAccount) {
    /*
     * 这个记录过程，只适合厂商用户作为操作主体的boot使用。
     * */
    
    AbstractCrmUserIdentity abstractLoginUser = loginUserService.getAbstractLoginUser();
    if(!(abstractLoginUser instanceof FacturerUserDetails)) {
      LOGGER.warn("创建导入导出任务时，发现当前操作者并不是一个厂商用户，请检查确认!!");
      return;
    }
    FacturerUserDetails loginUserDetails = (FacturerUserDetails)abstractLoginUser;
    String postCode = loginUserDetails.getPostCode();
    FacturerImportExportAuthEntity facturerImportExportAuth= new FacturerImportExportAuthEntity();
    facturerImportExportAuth.setAppCode(appCode);
    facturerImportExportAuth.setApplicationName(applicationName);
    facturerImportExportAuth.setCreateAccount(createAccount);
    facturerImportExportAuth.setPostCode(postCode);
    facturerImportExportAuth.setTaskCode(taskCode);
    facturerImportExportAuth.setTenantCode(tenantCode);
    
    // 进行保存
    this.facturerImportExportAuthRepository.save(facturerImportExportAuth);
  }
}
