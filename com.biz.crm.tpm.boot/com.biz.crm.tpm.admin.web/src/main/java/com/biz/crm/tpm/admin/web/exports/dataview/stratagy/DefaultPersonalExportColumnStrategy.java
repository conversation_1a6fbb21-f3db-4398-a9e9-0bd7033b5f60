package com.biz.crm.tpm.admin.web.exports.dataview.stratagy;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.biz.crm.common.ie.sdk.excel.vo.ColumnVo;
import com.biz.crm.common.ie.sdk.strategy.ExportColumnStrategy;
import com.biz.crm.mdm.business.table.sdk.service.ColumnConfigPersonalVoService;
import com.biz.crm.mdm.business.table.sdk.vo.ColumnConfigPersonalVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe: 默认的导出字段查询，从个性化设置取值
 * @createTime 2023年07月26日 14:24:00
 */
@Component
public class DefaultPersonalExportColumnStrategy implements ExportColumnStrategy {

  @Autowired
  private ColumnConfigPersonalVoService columnConfigPersonalVoService;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public String getCode() {
    return "default";
  }

  @Override
  public String getName() {
    return "个性化配置导出策略";
  }


  @Override
  public List<ColumnVo> getColumn(Set<String> marsField, JSONObject jsonObject) {
    String parentCode = jsonObject.getStr("parentCode");
    String functionCode = jsonObject.getStr("functionCode");
    //  检查个性化设置
    List<ColumnConfigPersonalVo> columnConfigPersonalVos =
        this.columnConfigPersonalVoService.findByParentCodeAndFunctionCode(parentCode, functionCode);
    if (CollectionUtils.isEmpty(columnConfigPersonalVos)){
      return new ArrayList<>();
    }
    //过滤数据权限字段,最终返回的字段必须是存在marsField中的!
    List<ColumnConfigPersonalVo> collect = columnConfigPersonalVos.parallelStream()
        .filter(ColumnConfigPersonalVo::getVisible)
        .filter(row -> {
          if (marsField == null) {
            return true;
          } else {
            String field = row.getField();
            return marsField.contains(field);
          }
        })
        .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(collect)){
      return new ArrayList<>();
    }
    return (List<ColumnVo>) nebulaToolkitService.copyCollectionByBlankList(collect, ColumnConfigPersonalVo.class, ColumnVo.class, HashSet.class, ArrayList.class);
  }
}
