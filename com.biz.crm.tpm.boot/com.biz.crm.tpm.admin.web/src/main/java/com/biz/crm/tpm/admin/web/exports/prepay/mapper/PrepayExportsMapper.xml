<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.admin.web.exports.prepay.mapper.PrepayExportsMapper">
  
  <sql id="conditions">
        <if test="dto.tenantCode != null and dto.tenantCode != '' ">
          and t.tenant_code = #{dto.tenantCode}
        </if>
        <if test="dto.processStatus != null and dto.processStatus != '' ">
          and t.process_status = #{dto.processStatus}
        </if>
        <if test="dto.processKey != null and dto.processKey != '' ">
          and t.process_key = #{dto.processKey}
        </if>
        <if test="dto.prepayCode != null and dto.prepayCode != '' ">
          <bind name = "prepayCode" value = " '%' + dto.prepayCode + '%' " />
          and t.prepay_code like #{prepayCode}
        </if>
        <if test="dto.activitiesCode != null and dto.activitiesCode != '' ">
          <bind name = "activitiesCode" value = " '%' + dto.activitiesCode + '%' " />
          and t.activities_code like #{activitiesCode}
        </if>
        <if test="dto.activitiesName != null and dto.activitiesName != '' ">
          <bind name = "activitiesName" value = " '%' + dto.activitiesName + '%' " />
          and t.activities_name like #{activitiesName}
        </if>
         and t.del_flag = '${@<EMAIL>()}'
  </sql>
  
  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from tpm_prepay t 
    <where>
       <include refid="conditions"/> 
    </where>
  </select>
  
  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.prepay.model.PrepayExportsVo">
    select *
    from tpm_prepay t 
    <where>
      <include refid="conditions" />
    </where>
    order by t.create_time desc, t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>