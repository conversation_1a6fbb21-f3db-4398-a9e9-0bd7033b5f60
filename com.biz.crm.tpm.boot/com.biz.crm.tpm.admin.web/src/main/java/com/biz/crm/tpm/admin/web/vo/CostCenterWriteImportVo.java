package com.biz.crm.tpm.admin.web.vo;

import com.biz.crm.business.common.base.vo.ExportWriteMdmVo;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterOrgVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/25 01:14
 */
@Data
public class CostCenterWriteImportVo extends ExportWriteMdmVo {

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;


}
