package com.biz.crm.tpm.admin.web.login.transform;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.mdm.business.user.sdk.service.UserValidityCheckService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.admin.web.login.DefaultPerfectLoginUserDetails;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.bizunited.nebula.security.sdk.transform.IdentityTransformStrategy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * CRM系统厂商管理平台（MDM+DMS+CPS+SFA+TPM）boot，在收到外部子系统的调用请求后（例如EMS经销商电商），将经销商用户转换为厂商用户的身份转换器
 * <AUTHOR>
 */
@Component("DistributorToFacturerTransformStrategy")
public class DistributorToFacturerTransformStrategy extends DefaultPerfectLoginUserDetails implements IdentityTransformStrategy {

  @Autowired
  private SimpleSecurityProperties simpleSecurityProperties;
  @Autowired
  private UserValidityCheckService userValidityCheckService;
  /**
   * 日志
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(DistributorToFacturerTransformStrategy.class);
  
  @Override
  public boolean matched(String sourceIdentityType) {
    // u-企业用户，c-客户用户，terminal-终端用户，customer_employee-经销商员工用户</br>
    return StringUtils.equals("c", sourceIdentityType);
  }

  @Override
  public UserIdentity transform(String sourceIdentityType, String sourceTenantCode, String sourceAccount, JSONObject userObject) {
    LOGGER.info("========= transform : " + sourceIdentityType + "  " + sourceAccount);
    FacturerUserDetails mdmUser = new FacturerUserDetails();
    // TODO 目前使用配置文件中，默认的管理员账号和管理员角色，作为调用者的身份
    String account = this.simpleSecurityProperties.getIndependencyUser();
    Integer type = this.simpleSecurityProperties.getDefaultLoginType();
    String[] independencyRoles = this.simpleSecurityProperties.getIndependencyRoles();
    
    // 转换成管理员用户
    UserVo userVo = this.userValidityCheckService.verificationManageByAccount(account);
    mdmUser.setLoginType(type);
    mdmUser.setTenantCode(sourceTenantCode);
    super.perfectLoginUserDetails(userVo, mdmUser);
    mdmUser.setRoleCodes(independencyRoles);
    return mdmUser;
  }

  @Override
  public int getOrder() {
    return 1;
  }

}
