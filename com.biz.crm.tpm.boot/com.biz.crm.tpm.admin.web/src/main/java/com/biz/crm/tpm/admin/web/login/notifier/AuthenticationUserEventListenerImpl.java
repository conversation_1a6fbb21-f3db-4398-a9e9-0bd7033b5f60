package com.biz.crm.tpm.admin.web.login.notifier;

import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.event.AuthenticationUserEventListener;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 获取当前登陆者所具备的角色（roleCode）
 *
 * <AUTHOR>
 * @date 2021/12/07
 */
@Component("AuthenticationUserEventListenerImpl")
public class AuthenticationUserEventListenerImpl implements AuthenticationUserEventListener {

  @Autowired(required = false)
  private UserVoService userVoService;

  @Autowired(required = false)
  private PositionVoService positionVoService;

  @Autowired
  private SimpleSecurityProperties simpleSecurityProperties;

  @Override
  public Set<String> onRequestRoleCodes(String tenantCode, String account) {
    if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(account)) {
      return Sets.newHashSet();
    }
    String independencyUser = this.simpleSecurityProperties.getIndependencyUser();
    if(StringUtils.equals(account,independencyUser)){
      return Arrays.stream(simpleSecurityProperties.getIndependencyRoles()).collect(Collectors.toSet());
    }
    Set<String> positionCodes = this.userVoService.findPositionCodesByUserNames(Lists.newArrayList(account));
    if (CollectionUtils.isEmpty(positionCodes)) {
      return Sets.newHashSet();
    }
    return this.positionVoService.findRolesByPositionCodes(Lists.newArrayList(positionCodes));
  }
}

