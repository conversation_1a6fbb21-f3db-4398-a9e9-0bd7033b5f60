package com.biz.crm.tpm.admin.web.exports.budgetsubjects.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月07日 14:46:00
 */
@Data
@CrmExcelExport
public class BudgetSubjectsExportsVo extends CrmExcelVo {

  /**
   * 预算科目编码
   */
  @CrmExcelColumn("预算科目编码")
  private String budgetSubjectsCode;

  /**
   * 预算科目名称
   */
  @CrmExcelColumn("预算科目名称")
  private String budgetSubjectsName;

  /**
   * 预算科目类型(数据字典)
   */
  @CrmExcelColumn("预算科目类型")
  private String budgetSubjectsType;

  /**
   * 预算科目分组(数据字典)
   */
  private String groupCode;

  @CrmExcelColumn("预算科目分组")
  private String groupName;

  /**
   * 控制类型编码
   */
  private String controlTypeCode;

  /**
   * 控制类型名称
   */
  @CrmExcelColumn("控制类型")
  private String controlTypeName;

  /**
   * 数据业务状态（启用状态）
   */
  @CrmExcelColumn("启用状态")
  private String enableStatus;

  /**
   * 备注
   */
  @CrmExcelColumn("备注")
  private String remark;

}
