package com.biz.crm.tpm.admin.web.register;

import com.biz.crm.tpm.business.activities.project.constant.ActivitiesConstant;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>项目活动数据视图注册器
 * 基于nebula的数据视图提供项目活动列表查询功能
 *
 * <AUTHOR>
 * @date 2022/8/15
 */
@Component
public class ProjectActivityDataViewRegister implements DataviewRegister {
  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-项目活动";
  }

  @Override
  public String buildSql() {
    return "select distinct tpa.*, bpbm.process_status, bpbm.process_no " +
        "from tpm_project_activity tpa " +
        "left join bpm_process_business_mapping bpbm on (tpa.code = bpbm.business_no and bpbm.business_code = '" + ActivitiesConstant.PROCESS_NAME + "') " +
        "where tpa.tenant_code = :tenantCode " +
        "and tpa.del_flag = '009' ";
  }
}
