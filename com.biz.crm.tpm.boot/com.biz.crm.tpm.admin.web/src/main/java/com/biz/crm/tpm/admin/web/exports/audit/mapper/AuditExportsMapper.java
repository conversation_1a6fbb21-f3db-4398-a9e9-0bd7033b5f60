package com.biz.crm.tpm.admin.web.exports.audit.mapper;

import com.biz.crm.tpm.admin.web.exports.audit.model.AuditExportsVo;
import com.biz.crm.tpm.admin.web.exports.audit.model.AuditExportsDto;

import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 费用核销;(tpm_audit)表导出功能数据库访问层
 * <AUTHOR> <PERSON>
 * @date : 2022-7-2
 */

public interface AuditExportsMapper{
  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") AuditExportsDto dto);
  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<AuditExportsVo> findData(@Param("dto") AuditExportsDto dto);
}