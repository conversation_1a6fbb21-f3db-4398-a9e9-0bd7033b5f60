package com.biz.crm.tpm.admin.web.exports.marketingplan.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description TODO 别乱动顺序！！！
 * <AUTHOR>
 * @Date 2024/8/13 22:18
 */
@Data
@ApiModel("周边物料")
public class MarketingBackExportVo implements Serializable {

    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("关联统筹方案名称")
    private String releaseName;

    @ApiModelProperty("客户编码")
    private String erpCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    private String companyCode;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("返利类型")
    private String rebateType;

    @ApiModelProperty("返利开始时间")
    private String rebateStartDate;

    @ApiModelProperty("返利结束时间")
    private String rebateEndDate;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("费用归属品项-前端使用")
    private String feeBelongItemCodeList;

    @ApiModelProperty("费用归属品项-前端使用")
    private String feeBelongItemNameList;

    @ApiModelProperty("费用品项范围-前端使用")
    private String feeItemCodeList;

    @ApiModelProperty("费用品项范围-前端使用")
    private String feeItemNameList;

    @ApiModelProperty("费用产品范围-前端使用")
    private String feeProductCodeList;

    @ApiModelProperty("费用产品范围-前端使用")
    private String feeProductNameList;

    @ApiModelProperty("政策形式编码")
    private String conditionFormula;

    @ApiModelProperty("政策形式编码")
    private String conditionFormulaName;

    @ApiModelProperty("条件数量")
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    private String giveNum;

    @ApiModelProperty("兑付方式")
    private String cashType;

    @ApiModelProperty("促销描述")
    private String actDesc;

    @ApiModelProperty("费用依据集合")
    private String costBasisNameList;

    @ApiModelProperty("是否合同费用")
    private String isContractCost;

    @ApiModelProperty("合同编码")
    private String contractCode;

    private String errMsg;

}
