package com.biz.crm.tpm.admin.web.exports.costbudget.mapper;

import com.biz.crm.tpm.admin.web.exports.costbudget.model.CostBudgetExportsDto;
import com.biz.crm.tpm.admin.web.exports.costbudget.model.CostBudgetExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用预算;(tpm_cost_budget)表导出功能数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-8
 */
public interface CostBudgetExportsMapper {
  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") CostBudgetExportsDto dto);

  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<CostBudgetExportsVo> findData(@Param("dto") CostBudgetExportsDto dto);
}
