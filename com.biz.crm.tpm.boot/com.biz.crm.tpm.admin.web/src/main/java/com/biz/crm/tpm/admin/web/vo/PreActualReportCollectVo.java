package com.biz.crm.tpm.admin.web.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/20 16:47
 **/
@Data
@ApiModel(value = "PreActualReportCollectVo", description = "预实报表汇总")
public class PreActualReportCollectVo extends TenantFlagOpVo {

    @ApiModelProperty("中心编码")
    private String centerCode;

    @ApiModelProperty("中心名称")
    private String centerName;

    @ApiModelProperty("一级部门编码")
    private String regionCode;

    @ApiModelProperty("一级部门名称")
    private String regionName;

    @ApiModelProperty("二级部门编码")
    private String orgCode;

    @ApiModelProperty("二级部门名称")
    private String orgName;

    @ApiModelProperty("申请人账号")
    private String applyAccount;

    @ApiModelProperty("申请人名称")
    private String applyName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("申请单号")
    private String schemeCode;

    @ApiModelProperty("申请状态")
    private String processStatus;

    @ApiModelProperty("计提状态")
    private String withholdingStatus;

    @ApiModelProperty("费用期间")
    private String actYears;

    @ApiModelProperty("规划收入")
    private BigDecimal planIncome;

    @ApiModelProperty("计提收入")
    private BigDecimal withholdingIncome;

    private BigDecimal posRate;

    @ApiModelProperty("收入达成率")
    private BigDecimal incomeAchieveRatio;

    @ApiModelProperty("收入达成率")
    private String incomeAchieveRatioStr;

    @ApiModelProperty("产品成本价")
    private BigDecimal planProductCost;

    @ApiModelProperty("规划毛利率")
    private BigDecimal planGrossProfitMargin;

    @ApiModelProperty("规划毛利率")
    private String planGrossProfitMarginStr;

    @ApiModelProperty("计提成本价")
    private BigDecimal withholdingProductCost;

    @ApiModelProperty("计提毛利率")
    private BigDecimal withholdingGrossProfitMargin;

    @ApiModelProperty("计提毛利率")
    private String withholdingGrossProfitMarginStr;

    @ApiModelProperty("毛利率偏差")
    private BigDecimal grossProfitMarginDifference;

    @ApiModelProperty("毛利率偏差")
    private String grossProfitMarginDifferenceStr;

    @ApiModelProperty("规划费用")
    private BigDecimal planCost;

    @ApiModelProperty("规划费率")
    private BigDecimal planCostRatio;

    @ApiModelProperty("规划费率")
    private String planCostRatioStr;

    @ApiModelProperty("计提费用")
    private BigDecimal withholdingCost;

    @ApiModelProperty("计提费率")
    private BigDecimal withholdingCostRatio;

    @ApiModelProperty("计提费率")
    private String withholdingCostRatioStr;

    @ApiModelProperty("费率偏差")
    private BigDecimal costRatioDifference;

    @ApiModelProperty("费率偏差")
    private String costRatioDifferenceStr;

    @ApiModelProperty("公摊费率")
    private BigDecimal publicShareRatio;

    @ApiModelProperty("产品运输费用")
    private BigDecimal productTransportCost;

    @ApiModelProperty("周边运输费用")
    private BigDecimal peripheryTransportCost;

    @ApiModelProperty("营销费用")
    private BigDecimal marketingCost;

    private BigDecimal planProfitMargin;

    @ApiModelProperty("规划利润率")
    private BigDecimal planProfitRatio;

    @ApiModelProperty("规划利润率")
    private String planProfitRatioStr;

    @ApiModelProperty("计提产品运输费用")
    private BigDecimal productExpressFee;

    @ApiModelProperty("计提周边运输费用")
    private BigDecimal peripheryExpressFee;

    @ApiModelProperty("计提营销费用")
    private BigDecimal withholdingMarketingCost;

    @ApiModelProperty("计提利润额")
    private BigDecimal withholdingProfitMargin;

    @ApiModelProperty("计提利润率")
    private BigDecimal withholdingProfitRatio;

    @ApiModelProperty("计提利润率")
    private String withholdingProfitRatioStr;

    @ApiModelProperty("利润率偏差")
    private BigDecimal profitRatioDifference;

    @ApiModelProperty("利润率偏差")
    private String profitRatioDifferenceStr;

    @ApiModelProperty("计提数据")
    private List<WithHoldingVo> withHoldingVoList;

    private List<FeeCashDetailVo> feeCashDetailVoList;

    @ApiModelProperty("成本中心编码集合")
    private List<String> costCenterCodes;

    @ApiModelProperty("发货数据")
    private List<DmsWarehouseOrderDetailVo> orderDetailVos;

    @ApiModelProperty("pos拆分数据")
    private Map<String, BigDecimal> posDataMap;

    @ApiModelProperty("物料成本")
    private Map<String, Map<String, BigDecimal>> materialMap;

    @ApiModelProperty("物料税率")
    private Map<String, BigDecimal> materialTaxRateMap;

    @ApiModelProperty("物流成本")
    private Map<String, MaterialCostVo> materialCostMap;

    @ApiModelProperty("结果值")
    private String jsonStr;

}
