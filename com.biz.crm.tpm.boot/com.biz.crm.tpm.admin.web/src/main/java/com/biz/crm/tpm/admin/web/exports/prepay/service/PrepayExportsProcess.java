package com.biz.crm.tpm.admin.web.exports.prepay.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.prepay.mapper.PrepayExportsMapper;
import com.biz.crm.tpm.admin.web.exports.prepay.model.PrepayExportsVo;
import com.biz.crm.tpm.admin.web.exports.prepay.model.PrepayExportsDto;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 活动预付;(tpm_prepay)导出服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-7-2
 */
@Component
public class PrepayExportsProcess implements ExportProcess<PrepayExportsVo> {
  @Autowired
  private PrepayExportsMapper prepayExportsMapper;
  @Autowired
  private DictDataVoService dictDataVoService;

  /**
   * 审批流状态数据字典
   */
  private final static String BPM_STATUS = "bpm_status";

  /**
   * 获取总条数
   *
   * @param params
   * @return
   */
  @Override
  public Integer getTotal(Map<String, Object> params) {
    PrepayExportsDto dto = this.convertParams(params);
    return prepayExportsMapper.getExportTotal(dto);
  }

  @Override
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    PrepayExportsDto dto = this.convertParams(params);
    dto.setOffset(this.getPageSize() * vo.getPageNo());
    dto.setLimit(vo.getPageSize());
    List<PrepayExportsVo> data = prepayExportsMapper.findData(dto);
    //调整
    adjustData(data);
    return toJSONArray(data);
  }

  /**
   * 模板编码
   *
   * @return
   */
  @Override
  public String getBusinessCode() {
    //
    return "TPM_PREPAY_EXPORT";
  }

  /**
   * 模板名称
   *
   * @return
   */
  @Override
  public String getBusinessName() {
    //
    return "活动预付导出";
  }

  /**
   * 获取参数
   *
   * @param params
   * @return
   */
  private PrepayExportsDto convertParams(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    // map 参数转换为对应的dto参数对象，可以手工进行修改设置
    PrepayExportsDto dto = JSON.parseObject(JSON.toJSONString(params), PrepayExportsDto.class);
    dto.setTenantCode(TenantUtils.getTenantCode());
    return dto;
  }

  /**
   * 调整数据
   *
   * @param data
   */
  private void adjustData(List<PrepayExportsVo> data) {
    if (CollectionUtils.isEmpty(data)) {
      return;
    }
    // 转换数据字典值
    Map<String, List<DictDataVo>> mapDict = this.dictDataVoService.findByDictTypeCodeList(Lists.newArrayList(BPM_STATUS));
    for (PrepayExportsVo vo : data) {
      vo.setProcessStatus(this.findDictValue(mapDict, BPM_STATUS, vo.getProcessStatus()));
    }
  }

  /**
   * 获取字典值
   *
   * @param mapDict
   * @param dictTypeCode
   * @param code
   * @return
   */
  private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
    if (Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
      return StringUtils.EMPTY;
    }
    final List<DictDataVo> vos = mapDict.get(dictTypeCode);
    if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
      return StringUtils.EMPTY;
    }
    final Optional<String> first =
        vos.stream()
            .filter(a -> a.getDictCode().equals(code))
            .map(DictDataVo::getDictValue)
            .findFirst();
    return first.orElse(StringUtils.EMPTY);
  }
}