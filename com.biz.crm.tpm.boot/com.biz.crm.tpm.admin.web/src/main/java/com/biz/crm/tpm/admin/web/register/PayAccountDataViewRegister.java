package com.biz.crm.tpm.admin.web.register;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>费用上账数据视图注册器
 * 基于nebula的数据视图提供列表查询功能
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Component
@Slf4j
public class PayAccountDataViewRegister implements DataviewRegister {

  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-费用上账";
  }

  @Override
  public String buildSql() {
    return "select ta.* from tpm_account ta " +
        "where ta.tenant_code = :tenantCode " +
        "and ta.del_flag = '009' ";
  }
}
