package com.biz.crm.tpm.admin.web.exports.withholdingcollect;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/30 11:05
 **/
@Data
public class WithholdingCollectDetailExportVo {


    @ApiModelProperty(name = "预提编号", notes = "预提编号")
    private String withHoldingCode;

    @ApiModelProperty("预提年月")
    private String yearMonthLy;

    @ApiModelProperty("费用归属年月")
    private String years;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty(name = "活动编号", notes = "活动编号")
    private String activitiesCode;

    @ApiModelProperty("业务编码")
    private String businessCode;

    @ApiModelProperty(name = "活动名称", notes = "活动名称")
    private String activitiesName;

    @ApiModelProperty(name = "活动大类名称", notes = "活动大类名称")
    private String costTypeCategoryName;

    @ApiModelProperty(name = "活动细类名称", notes = "活动系类名称")
    private String costTypeDetailName;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("费率")
    private String ratioStr;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty(name = "客户名称", notes = "客户名称")
    private String customerName;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty(name = "门店名称", notes = "门店名称")
    private String terminalName;

    @ApiModelProperty(name = "预提类型", notes = "预提类型(数据字典：withholding_type)")
    private String withHoldingType;

    @ApiModelProperty(name = "管报实际金额", notes = "管报实际金额")
    private BigDecimal actualReportAmount;

    @ApiModelProperty("活动描述")
    private String actDesc;


}
