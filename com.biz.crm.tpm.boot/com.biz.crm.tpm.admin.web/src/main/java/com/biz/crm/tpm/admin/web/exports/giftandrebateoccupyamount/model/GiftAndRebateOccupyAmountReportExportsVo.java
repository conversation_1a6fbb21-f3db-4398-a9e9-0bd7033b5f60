package com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
@CrmExcelExport
@Data
public class GiftAndRebateOccupyAmountReportExportsVo  extends CrmExcelVo {

    @ApiModelProperty(notes = "方案编码", value = "方案编码")
    @CrmExcelColumn("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    @CrmExcelColumn("方案名称")
    private String schemeName;

    @ApiModelProperty("方案类型")
    @CrmExcelColumn("方案类型")
    private String schemeType;

    @ApiModelProperty("方案明细编码")
    @CrmExcelColumn("方案明细编码")
    private String schemeDetailCode;


    @ApiModelProperty("开始时间")
    @CrmExcelColumn("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    @CrmExcelColumn("结束时间")
    private String endDate;

    @ApiModelProperty("客户编码")
    @CrmExcelColumn("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @CrmExcelColumn("客户名称")
    private String customerName;

    @ApiModelProperty("使用部门编码")
    @CrmExcelColumn("使用部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("使用部门名称")
    @CrmExcelColumn("使用部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    @CrmExcelColumn("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    @CrmExcelColumn("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    @CrmExcelColumn("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @CrmExcelColumn("成本中心名称")
    private String costCenterName;


    @ApiModelProperty("费用项目编码")
    @CrmExcelColumn("费用项目编码")
    private String detailCode;

    @ApiModelProperty("费用项目名称")
    @CrmExcelColumn("费用项目名称")
    private String detailName;

    @ApiModelProperty("品项编码")
    @CrmExcelColumn("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    @CrmExcelColumn("品项名称")
    private String itemName;

    @ApiModelProperty("本品数量")
    @CrmExcelColumn("本品数量")
    private String conditionNum;

    @ApiModelProperty("赠品数量")
    @CrmExcelColumn("赠品数量")
    private String giveNum;

    @ApiModelProperty("本品小类名称")
    @CrmExcelColumn("本品小类名称")
    private String bpLevelName;

    @ApiModelProperty("本品小类编码")
    @CrmExcelColumn("本品小类编码")
    private String bpLevelCode;

    @ApiModelProperty("规划金额")
    @CrmExcelColumn("规划金额")
    private String planAmount;

    @ApiModelProperty("占用金额")
    @CrmExcelColumn("占用金额")
    private String occupyAmount;

    @ApiModelProperty("返利品项或产品名称")
    @CrmExcelColumn("返利品项或产品名称")
    private String flItemOrProductName;

    @ApiModelProperty("返利品项或产品编码")
    @CrmExcelColumn("返利品项或产品编码")
    private String flItemOrProductCode;

    @ApiModelProperty("返利政策")
    @CrmExcelColumn("返利政策")
    private String rebatePolicy;

    @ApiModelProperty("达成条件")
    @CrmExcelColumn("达成条件")
    private String conditionFormula;

    @ApiModelProperty("返利标准")
    @CrmExcelColumn("返利标准")
    private String rebateStandard;

}
