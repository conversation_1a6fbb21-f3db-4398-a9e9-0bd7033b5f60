package com.biz.crm.tpm.admin.web.exports.projectactivity.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年07月01日 15:04:00
 */
@ApiModel(value = "ProjectActivityExportsVo",description = "项目活动")
@Getter
@Setter
@CrmExcelExport
public class ProjectActivityExportsVo extends CrmExcelVo {

  /**
   * 审批状态
   */
  @CrmExcelColumn("状态")
  @ApiModelProperty(name = "processStatus", notes = "审批状态", value = "审批状态")
  private String processStatus;
  /**
   * 活动编码
   */
  @CrmExcelColumn("活动编码")
  @ApiModelProperty(name = "code", notes = "活动编码", value = "活动编码")
  private String code;
  /**
   * 活动名称
   */
  @CrmExcelColumn("活动名称")
  @ApiModelProperty(name = "name", notes = "活动名称", value = "活动名称")
  private String name;
  /**
   * 审批单号
   */
  @CrmExcelColumn("审批单号")
  @ApiModelProperty(name = "processNumber", notes = "审批单号", value = "审批单号")
  private String processNumber;
  /**
   * 活动结束时间
   */
  @CrmExcelColumn("活动结束时间")
  @ApiModelProperty(name = "endTime", notes = "活动结束时间", value = "活动结束时间")
  private String endTime;
  /**
   * 开始时间
   */
  @CrmExcelColumn("开始时间")
  @ApiModelProperty(name = "startTime", notes = "开始时间", value = "开始时间")
  private String startTime;
  /**
   * 总申请金额
   */
  @CrmExcelColumn("总申请金额")
  @ApiModelProperty(name = "totalApplyAmount", notes = "总申请金额", value = "总申请金额")
  private Double totalApplyAmount;

  /**
   * 创建人名称
   */
  @CrmExcelColumn("创建人名称")
  private String createName;

  /**
   * 创建时间
   */
  @CrmExcelColumn("创建时间")
  private String createTime;
}
