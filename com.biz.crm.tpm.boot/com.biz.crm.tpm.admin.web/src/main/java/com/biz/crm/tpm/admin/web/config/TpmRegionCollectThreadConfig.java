package com.biz.crm.tpm.admin.web.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @since 2024-06-25
 */
@Configuration
@EnableAsync
@Data
public class TpmRegionCollectThreadConfig {

    /**
     * 线程池核心大小 此参数根据 ES连接池参数设定
     */
    @Value("${tpm.thread.core-pool-size:16}")
    private Integer corePoolSize;
    /**
     * 线程池最大等待数量 此参数根据 ES连接池参数设定
     */
    @Value("${tpm.thread.max-pool-size:30}")
    private Integer maxPoolSize;
    /**
     * 保持秒
     */
    @Value("${tpm.thread.keep-alive-seconds:120}")
    private Integer keepAliveSeconds;
    /**
     * 队列容量
     */
    @Value("${tpm.thread.queue-capacity:50000}")
    private Integer queueCapacity;

    @Bean(name = "tpmRegionCollectThread")
    public Executor availableThread() {
        //创建线程池对象
        //ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //这里使用我们自己创建的线程池对象类，可打印线程池使用日志信息
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //设置线程池属性
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setQueueCapacity(queueCapacity);
        //设置线程池前缀（可配置到yml文件中进行动态读取）
//        executor.setThreadNamePrefix("customAsyncPool-");
        // setRejectedExecutionHandler：当pool已经达到线程池最大线程max size的时候，如何处理新任务
        // CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "seqReportPool")
    public Executor seqReportPool() {
        //创建线程池对象
        //ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //这里使用我们自己创建的线程池对象类，可打印线程池使用日志信息
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //设置线程池属性
        executor.setCorePoolSize(1);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setQueueCapacity(queueCapacity);
        //设置线程池前缀（可配置到yml文件中进行动态读取）
//        executor.setThreadNamePrefix("customAsyncPool-");
        // setRejectedExecutionHandler：当pool已经达到线程池最大线程max size的时候，如何处理新任务
        // CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }


    @Bean(name = "tpmOverallBudgetThread")
    public Executor tpmOverallBudgetThread() {
        //创建线程池对象
        //ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //这里使用我们自己创建的线程池对象类，可打印线程池使用日志信息
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //设置线程池属性
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setQueueCapacity(queueCapacity);
        //设置线程池前缀（可配置到yml文件中进行动态读取）
//        executor.setThreadNamePrefix("customAsyncPool-");
        // setRejectedExecutionHandler：当pool已经达到线程池最大线程max size的时候，如何处理新任务
        // CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }


    @Bean(name = "tpmMarketingCheckCaseThread")
    public Executor tpmMarketingCheckCaseThread() {
        //创建线程池对象
        //ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //这里使用我们自己创建的线程池对象类，可打印线程池使用日志信息
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //设置线程池属性
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setQueueCapacity(queueCapacity);
        //设置线程池前缀（可配置到yml文件中进行动态读取）
//        executor.setThreadNamePrefix("customAsyncPool-");
        // setRejectedExecutionHandler：当pool已经达到线程池最大线程max size的时候，如何处理新任务
        // CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "tpmBudgetTrackThread")
    public Executor tpmBudgetTrackThread() {
        //创建线程池对象
        //ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //这里使用我们自己创建的线程池对象类，可打印线程池使用日志信息
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //设置线程池属性
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setQueueCapacity(queueCapacity);
        //设置线程池前缀（可配置到yml文件中进行动态读取）
//        executor.setThreadNamePrefix("customAsyncPool-");
        // setRejectedExecutionHandler：当pool已经达到线程池最大线程max size的时候，如何处理新任务
        // CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }

}
