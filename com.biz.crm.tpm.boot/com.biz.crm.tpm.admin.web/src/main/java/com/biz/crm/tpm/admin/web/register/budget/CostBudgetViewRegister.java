package com.biz.crm.tpm.admin.web.register.budget;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>TPM-预算中心-费用预算数据视图注册器
 * 基于nebula的数据视图提供费用预算列表查询功能
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Component
public class CostBudgetViewRegister implements DataviewRegister {
  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-费用预算";
  }

  @Override
  public String buildSql() {
    return "SELECT\n" +
            "IFNULL(a.used_amount, 0) + IFNULL(b.applyAmount, 0) used_amount,\n" +
            "IFNULL(a.month_balance_amount, 0) - IFNULL(b.applyAmount, 0) month_balance_amount,\n" +
            "a.*\n" +
            "FROM\n" +
            "tpm_cost_budget a\n" +
            "LEFT JOIN (\n" +
            "SELECT\n" +
            "sum(b.apply_amount) applyAmount,\n" +
            "b.budget_code\n" +
            "FROM (\n" +
            "SELECT\n" +
            "CASE d.original_scheme_detail_code IS NOT NULL\n" +
            "AND d.original_scheme_detail_code = b.scheme_detail_code\n" +
            "WHEN d.process_status = '2'\n" +
            "AND d.apply_amount < b.apply_amount THEN\n" +
            "d.apply_amount\n" +
            "WHEN d.process_status = '2'\n" +
            "AND d.apply_amount > b.apply_amount THEN\n" +
            "b.apply_amount\n" +
            "WHEN d.process_status = '3' THEN\n" +
            "d.apply_amount\n" +
            "ELSE\n" +
            "b.apply_amount\n" +
            "END apply_amount,\n" +
            "c.budget_code\n" +
            "FROM\n" +
            "tpm_marketing_plan a\n" +
            "LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code\n" +
            "LEFT JOIN tpm_marketing_plan_case_extend c ON a.scheme_code = c.scheme_code\n" +
            "AND b.scheme_detail_code = c.scheme_detail_code\n" +
            "LEFT JOIN (\n" +
            "SELECT\n" +
            "b.apply_amount,\n" +
            "c.budget_code,\n" +
            "b.original_scheme_detail_code,\n" +
            "a.process_status\n" +
            "FROM\n" +
            "tpm_marketing_plan a\n" +
            "LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code\n" +
            "LEFT JOIN tpm_marketing_plan_case_extend c ON a.scheme_code = c.scheme_code\n" +
            "AND b.scheme_detail_code = c.scheme_detail_code\n" +
            "WHERE\n" +
            "a.tenant_code = 'default'\n" +
            "AND a.del_flag = '009'\n" +
            "AND b.del_flag = '009'\n" +
            "AND a.process_status in('2', '3')\n" +
            "AND c.budget_code IS NOT NULL\n" +
            "AND c.budget_code != ''\n" +
            "AND a.scheme_type = 'change') d ON b.scheme_detail_code = d.original_scheme_detail_code\n" +
            "WHERE\n" +
            "a.tenant_code = 'default'\n" +
            "AND a.del_flag = '009'\n" +
            "AND b.del_flag = '009'\n" +
            "AND a.process_status in('2', '3')\n" +
            "AND c.budget_code IS NOT NULL\n" +
            "AND c.budget_code != ''\n" +
            "AND a.scheme_type != 'change') b\n" +
            "GROUP BY\n" +
            "b.budget_code) b ON a.code = b.budget_code\n" +
            "WHERE\n" +
            "a.tenant_code = :tenantCode\n" +
            "AND a.del_flag = '009'";
  }
}
