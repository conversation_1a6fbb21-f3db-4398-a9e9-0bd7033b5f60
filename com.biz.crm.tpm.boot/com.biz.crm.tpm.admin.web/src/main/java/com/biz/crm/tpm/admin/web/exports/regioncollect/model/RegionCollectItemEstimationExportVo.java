package com.biz.crm.tpm.admin.web.exports.regioncollect.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "RegionCollectItemEstimationVo", description = "大区汇总-品项测算")
public class RegionCollectItemEstimationExportVo extends CustomerGainsAndLossesExportBaseVo {

    @ApiModelProperty("品项名称")
    private String itemName;

}
