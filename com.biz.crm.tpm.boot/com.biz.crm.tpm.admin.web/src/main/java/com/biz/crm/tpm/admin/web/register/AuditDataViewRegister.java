package com.biz.crm.tpm.admin.web.register;

import com.biz.crm.tpm.business.pay.sdk.constant.PayConstant;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>核销申请数据视图注册器
 * 基于nebula的数据视图提供核销申请列表查询功能
 *
 * <AUTHOR>
 * @date 2022/8/15
 */
@Component
public class AuditDataViewRegister implements DataviewRegister {
  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-核销申请";
  }

  @Override
  public String buildSql() {
    return "select distinct ta.*, bpbm.process_status, bpbm.process_no " +
        "from tpm_audit ta " +
        "left join bpm_process_business_mapping bpbm on (ta.audit_code = bpbm.business_no and bpbm.business_code = '" + PayConstant.PROCESS_AUDIT_ACTIVITIES + "') " +
        "where ta.tenant_code = :tenantCode " +
        "and ta.del_flag = '009' ";
  }
}
