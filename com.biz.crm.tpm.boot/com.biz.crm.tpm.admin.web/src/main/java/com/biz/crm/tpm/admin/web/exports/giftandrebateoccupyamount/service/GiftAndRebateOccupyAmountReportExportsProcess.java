package com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.service;

import com.alibaba.fastjson.JSONArray;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.mapper.GiftAndRebateOccupyAmountReportExportsMapper;
import com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.model.GiftAndRebateOccupyAmountReportExportsDto;
import com.biz.crm.tpm.admin.web.exports.giftandrebateoccupyamount.model.GiftAndRebateOccupyAmountReportExportsVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
/**
 *
 * <AUTHOR>
 */
@Component
public class GiftAndRebateOccupyAmountReportExportsProcess implements ExportProcess<GiftAndRebateOccupyAmountReportExportsVo> {

    @Autowired
    GiftAndRebateOccupyAmountReportExportsMapper giftAndRebateOccupyAmountReportExportsMapper;


    @Override
    public Integer getTotal(Map<String, Object> params) {
        return giftAndRebateOccupyAmountReportExportsMapper.getExportTotal(new GiftAndRebateOccupyAmountReportExportsDto(this.convertEuropaParam(params)));
    }

    @Override
    public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
        Map<String, Object> nParams = this.convertEuropaParam(params);
        nParams.putIfAbsent("offset",this.getPageSize()*vo.getPageNo());
        nParams.putIfAbsent("limit",vo.getPageSize());

        List<GiftAndRebateOccupyAmountReportExportsVo> list = giftAndRebateOccupyAmountReportExportsMapper.findData(new GiftAndRebateOccupyAmountReportExportsDto(nParams));
        adjustData(list);
        if(CollectionUtils.isEmpty(list)){
            return new JSONArray();
        }
        return toJSONArray(list);
    }

    @Override
    public String getBusinessCode() {
        return "TPM_GIFT_AND_REBATE_OCCUPY_AMOUNT_REPORT_EXPORT";
    }

    private void adjustData(List<GiftAndRebateOccupyAmountReportExportsVo> data) {
        if(CollectionUtils.isEmpty(data)){
            return;
        }
    }

    @Override
    public String getBusinessName() {
        return "搭赠及返利实时费用测算报表";
    }
}
