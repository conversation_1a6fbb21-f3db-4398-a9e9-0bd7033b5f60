package com.biz.crm.tpm.admin.web.exports.projectactivity.model;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import com.biz.crm.tpm.business.activities.project.dto.ProjectActivityFilesDto;
import com.biz.crm.tpm.business.activities.project.dto.ProjectActivityRelationDto;
import com.biz.crm.workflow.sdk.vo.AttachmentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年07月01日 15:17:00
 */
@ApiModel(value = "ProjectActivityExportsDto",description = "项目活动")
@Getter
@Setter
public class ProjectActivityExportsDto extends TenantDto implements Serializable,Cloneable {
  @ApiModelProperty("活动编码")
  private String code;

  @ApiModelProperty("活动名称")
  private String name;

  @ApiModelProperty("活动开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  @ApiModelProperty("活动结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty("总申请金额")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("活动状态")
  private String status;

  @ApiModelProperty("流程状态")
  private String processStatus;

  @ApiModelProperty("审批单号")
  private String processNumber;

  /** 偏移量 */
  @ApiModelProperty(name = "offset",notes = "偏移量", value = "偏移量")
  private Integer offset;

  /** limit */
  @ApiModelProperty(name = "limit",notes = "limit", value = "limit")
  private Integer limit;
}
