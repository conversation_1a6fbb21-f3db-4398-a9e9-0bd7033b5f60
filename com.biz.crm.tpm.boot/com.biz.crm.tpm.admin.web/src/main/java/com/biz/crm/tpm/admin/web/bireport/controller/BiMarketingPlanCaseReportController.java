package com.biz.crm.tpm.admin.web.bireport.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.bireport.service.BiMarketingPlanCaseReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/biMarketingPlanCaseReportController")
@Slf4j
@Api(tags = "Bi活动明细报表")
public class BiMarketingPlanCaseReportController {

    @Autowired
    BiMarketingPlanCaseReportService biMarketingPlanCaseReportService;

    @GetMapping("runBiMarketingPlanCaseReport")
    @ApiOperation(value = "跑Bi活动明细报表")
    public Result runBiMarketingPlanCaseReport(@RequestParam(required = false) String startDate,@RequestParam(required = false)  String endDate,@RequestParam(required = false) String initFlag){
        biMarketingPlanCaseReportService.removeAll(null,null);
        biMarketingPlanCaseReportService.caliMarketingPlanCaseReport(startDate,endDate,initFlag);
        return Result.ok();
    }

    @ApiOperation(value = "定时任务执行Bi活动明细报表")
    @GetMapping("scheduleBiMarketingPlanCaseReport")
    public Result scheduleBiMarketingPlanCaseReport(){
        Map<String,String> startAndEndMap = new HashMap<>();
        LocalDate today = LocalDate.now();
        LocalDate currentMonthFirstDay = today.withDayOfMonth(1);
        LocalDate currentMonthLastDay = YearMonth.from(today).atEndOfMonth();
        LocalDate previousMonthFirstDay = today.minusMonths(1).withDayOfMonth(1);
        LocalDate previousMonthLastDay = YearMonth.from(today.minusMonths(1)).atEndOfMonth();
        startAndEndMap.put(currentMonthFirstDay.toString(),currentMonthLastDay.toString());
        startAndEndMap.put(previousMonthFirstDay.toString(),previousMonthLastDay.toString());
        for (Map.Entry<String, String> entry : startAndEndMap.entrySet()) {
            String startDate = entry.getKey();
            String endDate = entry.getValue();
            log.info("BI活动明细报表落表 startDate {}  endDate {}", startDate, endDate);
            biMarketingPlanCaseReportService.removeAll(startDate, endDate);
            biMarketingPlanCaseReportService.caliMarketingPlanCaseReport(startDate,endDate,"N");
        }
        return Result.ok();
    }


}
