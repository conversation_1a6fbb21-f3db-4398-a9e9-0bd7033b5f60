package com.biz.crm.tpm.admin.web.vo;

import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: haiyang
 * @Date: 2025-06-06 17:29
 * @Desc:
 */
@Data
public class GiftAndRebateOccupyAmountReportVo extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案类型")
    private String schemeType;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;


    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("使用部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("使用部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;


    @ApiModelProperty("费用项目编码")
    private String detailCode;

    @ApiModelProperty("费用项目名称")
    private String detailName;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("本品数量")
    private String conditionNum;

    @ApiModelProperty("赠品数量")
    private String giveNum;

    @ApiModelProperty("本品小类名称")
    private String bpLevelName;

    @ApiModelProperty("本品小类编码")
    private String bpLevelCode;

    @ApiModelProperty("规划金额")
    private BigDecimal planAmount;

    @ApiModelProperty("占用金额")
    private BigDecimal occupyAmount;

    @ApiModelProperty("返利品项或产品名称")
    private String flItemOrProductName;

    @ApiModelProperty("返利品项或产品编码")
    private String flItemOrProductCode;

    @ApiModelProperty("返利政策")
    private String rebatePolicy;

    @ApiModelProperty("达成条件")
    private String conditionFormula;

    @ApiModelProperty("返利标准")
    private String rebateStandard;


}
