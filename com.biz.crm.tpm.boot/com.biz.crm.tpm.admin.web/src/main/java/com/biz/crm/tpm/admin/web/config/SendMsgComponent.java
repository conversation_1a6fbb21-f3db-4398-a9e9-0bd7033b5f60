package com.biz.crm.tpm.admin.web.config;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.common.ie.sdk.enums.ExecStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.gateway.websocket.client.sdk.service.ChannelMsgService;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/21 00:02
 */
@Slf4j
public class SendMsgComponent {

    private ThreadLocal<Integer> websocketOrder = new ThreadLocal<>();

    private LoginUserService loginUserService;

    private ChannelMsgService channelMsgService;

    private final static String applicationName = "crm-tpm";

    public SendMsgComponent () {
        this.loginUserService = ApplicationContextHolder.getContext().getBean(LoginUserService.class);
        this.channelMsgService = ApplicationContextHolder.getContext().getBean(ChannelMsgService.class);
    }


    /**
     * 发送进度消息
     */
    public void sendMsg(String modelCode, String remark, String execStatus) {
        Integer order = websocketOrder.get();
        if (null == order) {
            order = 0;
        }
        websocketOrder.set(order + 1);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("remark", remark);
        String time = DateUtil.getDateStrByFormat(new Date(), DateUtil.DEFAULT_DATE_ALL_PATTERN);
        jsonObject.put("time", time);
        jsonObject.put("applicationName", applicationName);
        jsonObject.put("execStatus", execStatus);
        jsonObject.put("order", order);
        if (ExecStatusEnum.FINISH.getKey().equals(execStatus)) {
            //任务执行结束之后清理threadLocal
            websocketOrder.remove();
        }
        byte[] jsonBytes = JSONObject.toJSONBytes(jsonObject);
        String tenantCode = TenantUtils.getTenantCode();
        log.info(" activity_plan model send msg2: tenantCode = {} , applicationName = {} , modelCode = {} ", tenantCode, applicationName, modelCode);
        UserIdentity loginUser = loginUserService.getLoginUser();
        //公共导入通道
        this.channelMsgService.sendByTenantCodeAndApplicationNameAndModelCodeAndAccount(tenantCode, applicationName, modelCode, loginUser.getAccount(), jsonBytes);
    }
}
