package com.biz.crm.tpm.admin.web.exports.dataview.auth;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.common.ie.sdk.auth.ImportExportAuthBean;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.user.sdk.service.UserPositionVoService;
import com.biz.crm.mdm.business.user.sdk.service.UserValidityCheckService;
import com.biz.crm.mdm.business.user.sdk.vo.UserPositionVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.admin.web.login.DefaultPerfectLoginUserDetails;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 导入导出登录信息bean</br>
 * 2022-08-24 yinwenjie修改了执行任务重建security content上下文的逻辑
 *
 * <AUTHOR>
 * @date 2022/5/26
 */
public class FacturerImportExportAuthBean extends DefaultPerfectLoginUserDetails implements ImportExportAuthBean {

  /**
   * nebula security模块中，关于默认用户身份信息的配置情况
   */
  @Autowired
  private SimpleSecurityProperties simpleSecurityProperties;

  @Autowired(required = false)
  private UserPositionVoService userPositionVoService;

  @Autowired(required = false)
  private PositionVoService positionVoService;

  @Autowired(required = false)
  private UserValidityCheckService userValidityCheckService;

  @Autowired(required = false)
  private FacturerImportExportAuthRepository facturerImportExportAuthRepository;

  /**
   * 日志
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(FacturerImportExportAuthBean.class);

  /**
   * 根据导入导出任务公共信息设置登录信息
   *
   * @param vo 任务参数vo
   */
  @Override
  public void setAuthentication(TaskGlobalParamsVo vo) {
    Validate.notNull(vo, "导出任务不存在");
    /*
     * 重建账号主要依据创建导出任务的厂商用户
     * 经销商体系的导出，在10版本中不适用，原因就是这里的用户只是基于厂商用户进行的查询
     * 除非诸如EMS这样的系统，重写ImportExportAuthBean
     *
     * 但是08版本做了修改
     *
     * 1、查询当前导入导出任务用户身份关联映射表中，取出用户信息
     * 如果没有，则依据任务的创建者进行用户身份取出
     * 如果没有取到，则从配置上下文中取得管理员账号
     * 2、根据用户账号，进行用户信息的查询，并得到角色信息
     * 3、根据第1步或者第2步的内容，重建用户认证身份
     * */

    // 1、=======
    String account = null;
    String userName = "超级管理员";
    String[] roleCodes = new String[]{};
    String tenantCode = null;
    String currentPositionCode = null;
    String taskCode = vo.getTaskCode();
    String appCode = vo.getTenantCode();
    String applicationName = vo.getApplicationName();
    boolean found = false;
    // 先从导入导出任务身份映射信息中寻找
    if (this.facturerImportExportAuthRepository != null) {
      FacturerImportExportAuthEntity currentFacturerImportExportAuth = this.facturerImportExportAuthRepository.findByTaskCodeAndAppCodeAndApplicationName(taskCode, appCode, applicationName);
      if (currentFacturerImportExportAuth != null) {
        account = currentFacturerImportExportAuth.getCreateAccount();
        tenantCode = currentFacturerImportExportAuth.getTenantCode();
        currentPositionCode = currentFacturerImportExportAuth.getPostCode();
        found = true;
      }
    }
    // 如果没有找到就试图基于任务的创建者作为运行者账号
    if (!found && StringUtils.isNotBlank(vo.getCreateAccount())) {
      account = vo.getCreateAccount();
      tenantCode = vo.getTenantCode();
      found = true;
    }
    // 如果条件成立，说明已经通过以上两种方式的某一种找到了用户
    // 那么执行2、=======
    if (found) {
      Set<String> roles = this.findByAccount(tenantCode, account);
      if (!CollectionUtils.isEmpty(roles)) {
        roleCodes = roles.toArray(new String[]{});
      }
      // 设定username
      if (StringUtils.isNotBlank(vo.getCreateAccountName())) {
        userName = vo.getCreateAccountName();
      }
    }
    // 如果还没有找到，就从配置信息中以管理员账号为运行身份
    else {
      account = this.simpleSecurityProperties.getIndependencyUser();
      roleCodes = this.simpleSecurityProperties.getIndependencyRoles();
      tenantCode = TenantUtils.getTenantCode();
    }
    LOGGER.info(" ========= 导入导出过程，重建用户身份信息 ：" + account);
    FacturerUserDetails loginUserDetails = new FacturerUserDetails();
    loginUserDetails.setAccount(account);
    loginUserDetails.setAccount(account);
    // 厂商用户（超级管理员）
    Integer type = this.simpleSecurityProperties.getDefaultLoginType();
    loginUserDetails.setLoginType(type);
    loginUserDetails.setIdentityType("u");
    loginUserDetails.setRoleCodes(roleCodes);
    loginUserDetails.setUsername(userName);
    loginUserDetails.setTenantCode(tenantCode);
    UserVo userVo = this.userValidityCheckService.verificationManageByAccount(account);
    super.perfectLoginUserDetails(userVo, loginUserDetails);
    super.perfectLoginPostAndOrg(loginUserDetails);
    // 如果记录了导入导出时的岗位，则以那个岗位为准
    if (StringUtils.isNotBlank(currentPositionCode)) {
      loginUserDetails.setPostCode(currentPositionCode);
      userVo.setPositionCode(currentPositionCode);
    }

    // 3、======
    SecurityContext securityContext = SecurityContextHolder.getContext();
    List<SimpleGrantedAuthority> authorities = new ArrayList<>();
    for (String item : roleCodes) {
      SimpleGrantedAuthority author = new SimpleGrantedAuthority(StringUtils.upperCase(item));
      authorities.add(author);
    }
    // 这里的密码不重要的
    UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(account, "123xxx455", authorities);
    authentication.setDetails(loginUserDetails);
    // 设定成当前线程的鉴权信息，但由于当前线程不再web容器中，所以没有sessionId信息
    securityContext.setAuthentication(authentication);
  }

  /**
   * 按照指定的账号查询对应的角色信息
   *
   * @param tenantCode
   * @param account
   * @return
   */
  private Set<String> findByAccount(String tenantCode, String account) {
    /*
     * 由于管理关系统是客户用户登录，所以确定用户的角色需要以下步骤：
     * 1、确认当前用户和职位的关联，以及这个职位关联了的角色（注意：用户与职级没有直接关联，与职位关联）
     * 2、组装，去重后返回
     */

    // 1、
    List<UserPositionVo> userPositionVos = userPositionVoService.findByUserName(tenantCode, account);
    if (CollectionUtils.isEmpty(userPositionVos)) {
      return Sets.newHashSet();
    }
    // 默认只获取主岗位所关联的角色集合
    Set<String> positionCodes = userPositionVos.stream()
        .filter(p -> p.getPrimaryFlag())
        .map(UserPositionVo::getPositionCode).collect(Collectors.toSet());
    List<PositionVo> positionVos = positionVoService.findByIdsOrCodes(null, new ArrayList<>(positionCodes));
    if (CollectionUtils.isEmpty(positionVos)) {
      return Sets.newHashSet();
    }
    Set<String> roleCodes = Sets.newHashSet();
    positionVos.forEach(p -> {
      if (!CollectionUtils.isEmpty(p.getRoleList())) {
        roleCodes.addAll(p.getRoleList());
      }
    });
    return roleCodes;
  }


  /**
   * 通过账号查询当前职位
   *
   * @param tenantCode
   * @param account
   * @return
   */
  private List<PositionVo> findByAccountForCurrentPosition(String tenantCode, String account) {
    /*
     * 由于管理关系统是客户用户登录，所以确定用户的角色需要以下步骤：
     * 1、确认当前用户和职位的关联，以及这个职位关联了的角色（注意：用户与职级没有直接关联，与职位关联）
     * 2、组装，去重后返回
     */
    // 1、
    List<UserPositionVo> userPositionVos = userPositionVoService.findByUserName(tenantCode, account);
    if (CollectionUtils.isEmpty(userPositionVos)) {
      return Lists.newArrayList();
    }
    // 默认只获取主岗位所关联的角色集合
    Set<String> positionCodes = userPositionVos.stream()
        .filter(p -> p.getCurrentFlag())
        .map(UserPositionVo::getPositionCode).collect(Collectors.toSet());
    return positionVoService.findByIdsOrCodes(null, new ArrayList<>(positionCodes));
  }
}
