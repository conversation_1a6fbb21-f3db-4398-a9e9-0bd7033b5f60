package com.biz.crm.tpm.admin.web.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.eunm.AuditStatusEnum;
import com.biz.crm.business.common.base.eunm.CashStatusEnum;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolOperationType;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.admin.web.service.MarketingItemShareService;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingItemShareEntity;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.PlanClosureEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingItemShareMapper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingItemShareVo;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.enums.CashMethodEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.CashTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.DeliveryReplenishmentPoolDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.vo.DeliveryReplenishmentPoolDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.FormulaGetValueComponent;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/12 17:46
 **/
@Service
@Slf4j
public class MarketingItemShareServiceImpl extends ServiceImpl<MarketingItemShareMapper, MarketingItemShareEntity> implements MarketingItemShareService {

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Autowired(required = false)
    private WithHoldingService withHoldingService;

    @Resource
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;

    @Autowired(required = false)
    private MarketingSalesPlanService marketingSalesPlanService;

    @Autowired(required = false)
    private ProductVoService productVoService;

    @Autowired(required = false)
    private FeeCashService feeCashService;

    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;

    @Resource
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;


    /**
     * 查询品项分析
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MarketingItemShareVo> findListByCondition(Pageable pageable, MarketingItemShareVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MarketingItemShareVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        vo.setTenantCode(TenantUtils.getTenantCode());
        return this.baseMapper.findListByCondition(page, vo);
    }

    @Override
    public void updateItemShare(String years) {
        years = ObjectUtils.defaultIfNull(years, LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY)));
        String shareOperationYears = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        List<MarketingPlanCaseVo> caseList = marketingPlanCaseService.findCaseListByYears(years);
        if (CollectionUtils.isEmpty(caseList)) {
            return;
        }
        List<MarketingItemShareVo> itemShareList = (List<MarketingItemShareVo>) nebulaToolkitService.copyCollectionByWhiteList(caseList, MarketingPlanCaseVo.class,
                MarketingItemShareVo.class, HashSet.class, ArrayList.class);

        List<String> itemCodes = itemShareList.stream().map(x -> x.getItemCode()).distinct().collect(Collectors.toList());
        Map<String, List<ProductPhaseVo>> productPhaseMap = checkHelper.findProductPhaseMapList(itemCodes);

        List<MarketingItemShareVo> dataList = Lists.newArrayList();
        //拆分分摊品项
        for (MarketingPlanCaseVo caseVo : caseList) {
            if (productPhaseMap.containsKey(caseVo.getItemCode())) {
                List<ProductPhaseVo> productPhaseVos = productPhaseMap.get(caseVo.getItemCode());
                String shareFlag = BooleanEnum.TRUE.getCapital();
                if (productPhaseVos.size() == 1) {
                    shareFlag = BooleanEnum.FALSE.getCapital();
                }
                for (ProductPhaseVo vo : productPhaseVos) {
                    MarketingItemShareVo data = JsonUtils.convert(caseVo, MarketingItemShareVo.class);
                    data.setShareItemCode(vo.getProductPhaseCode());
                    data.setShareItemName(vo.getProductPhaseName());
                    data.setConfirmed(BooleanEnum.FALSE.getCapital());
                    data.setShareOperationYears(shareOperationYears);
                    data.setShareFlag(shareFlag);
                    data.setOrgCode(caseVo.getBelongDepartmentCode());
                    data.setOrgName(caseVo.getBelongDepartmentName());
                    dataList.add(data);
                }
            } else {
                MarketingItemShareVo data = JsonUtils.convert(caseVo, MarketingItemShareVo.class);
                data.setShareItemCode(caseVo.getItemCode());
                data.setShareItemName(caseVo.getItemName());
                data.setConfirmed(BooleanEnum.FALSE.getCapital());
                data.setShareOperationYears(shareOperationYears);
                data.setShareFlag(BooleanEnum.FALSE.getCapital());
                data.setOrgCode(caseVo.getBelongDepartmentCode());
                data.setOrgName(caseVo.getBelongDepartmentName());
                dataList.add(data);
            }
        }
        //方案活动明细
        Set<String> schemeDetailCodeSet = dataList.stream().map(x -> x.getSchemeDetailCode()).collect(Collectors.toSet());
        List<WithHoldingVo> withHoldingVoList = withHoldingService.findBySchemeDetailCodesPass(Lists.newArrayList(schemeDetailCodeSet));
        Map<String, WithHoldingVo> withHoldingVoMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(withHoldingVoList)) {
            withHoldingVoMap = withHoldingVoList.stream().collect(Collectors.toMap(x -> x.getActivitiesDetailCode(), Function.identity()));
        }
        Set<String> withholdingCodeSet = withHoldingVoMap.keySet();
        //排除
        Set<String> excludeSchemeDetailCodes = schemeDetailCodeSet.stream().filter(x -> !withholdingCodeSet.contains(x)).collect(Collectors.toSet());
        Set<String> salesCustomerCodes = Sets.newHashSet();
        Set<String> salesActYears = Sets.newHashSet();
        Set<String> salesBelongCostCenterCodes = Sets.newHashSet();

        Set<String> deliveryCustomerCodes = Sets.newHashSet();
        Set<String> deliveryActYears = Sets.newHashSet();
        Set<String> deliveryItemCodes = Sets.newHashSet();
        for (MarketingItemShareVo vo : dataList) {
            if (excludeSchemeDetailCodes.contains(vo.getSchemeDetailCode())) {
                if (ObjectUtils.isNotEmpty(vo.getBelongCostCenterCodes())) {
                    salesBelongCostCenterCodes.addAll(Arrays.asList(vo.getBelongCostCenterCodes().split(",")));
                }
                salesCustomerCodes.add(vo.getCustomerCode());
                salesActYears.add(vo.getActYears());
            } else if (withholdingCodeSet.contains(vo.getSchemeDetailCode())) {
                deliveryCustomerCodes.add(vo.getCustomerCode());
                deliveryActYears.add(vo.getActYears());
                deliveryItemCodes.add(vo.getItemCode());
            }
        }
        //查询销售计划
        List<MarketingSalesPlanVo> salesPlanVoList = this.findSalesPlanList(salesCustomerCodes, years, salesBelongCostCenterCodes);
        //查询发货数据
        List<DmsWarehouseOrderDetailVo> deliveryDataList = this.findDeliveryDataList(deliveryCustomerCodes, years, deliveryItemCodes);
        //拆分数据
        this.splitAmountAndQuantity(salesPlanVoList, deliveryDataList, dataList, withholdingCodeSet, excludeSchemeDetailCodes);
        //过滤数据
        dataList = dataList.stream().filter(x -> {
            if (BooleanEnum.TRUE.getCapital().equals(x.getShareFlag()) && x.getIncomeCost().compareTo(BigDecimal.ZERO) == 1) {
                return Boolean.TRUE;
            } else if (BooleanEnum.FALSE.getCapital().equals(x.getShareFlag())) {
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }).collect(Collectors.toList());
        //拆分分摊费用
        this.splitShareCostAmount(dataList, withHoldingVoList, caseList);
        //过滤分摊费用为0的
        dataList = dataList.stream().filter(x -> (x.getShareCost().compareTo(BigDecimal.ZERO) == 1 &&
                BooleanEnum.TRUE.getCapital().equals(x.getShareFlag()))
                || BooleanEnum.FALSE.getCapital().equals(x.getShareFlag())).collect(Collectors.toList());
        //保存
        this.saveBatchList(dataList);
    }


    @Override
    public void saveBatchList(List<MarketingItemShareVo> marketingItemShares) {
        List<MarketingItemShareEntity> entityList = JsonUtils.convert(marketingItemShares, List.class, MarketingItemShareEntity.class);
        entityList.forEach(x -> {
            x.setId(null);
            x.setTenantCode(TenantUtils.getTenantCode());
            x.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            x.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        });
        List<String> schemeDetailCodes = marketingItemShares.stream().filter(x -> BooleanEnum.TRUE.getCapital().equals(x.getSchemeDetailCode()))
                .map(x -> x.getSchemeDetailCode()).distinct().collect(Collectors.toList());
        List<String> oldSchemeDetailCodes = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(schemeDetailCodes)) {
            List<MarketingItemShareEntity> oldEntityList = this.lambdaQuery()
                    .in(MarketingItemShareEntity::getSchemeDetailCode, schemeDetailCodes)
                    .eq(MarketingItemShareEntity::getTenantCode, TenantUtils.getTenantCode())
                    .eq(MarketingItemShareEntity::getConfirmed, BooleanEnum.TRUE.getCapital())
                    .select(MarketingItemShareEntity::getSchemeDetailCode, MarketingItemShareEntity::getSchemeCode)
                    .list();
            oldSchemeDetailCodes = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(oldEntityList)) {
                oldSchemeDetailCodes = oldEntityList.stream().map(x -> x.getSchemeDetailCode()).collect(Collectors.toList());
            }
            List<String> finalOldSchemeDetailCodes = oldSchemeDetailCodes;
            entityList = entityList.stream().filter(x -> !finalOldSchemeDetailCodes.contains(x.getSchemeDetailCode())).collect(Collectors.toList());
        }
        List<String> actYears = marketingItemShares.stream().map(x -> x.getActYears()).distinct().collect(Collectors.toList());
        this.lambdaUpdate()
                .in(MarketingItemShareEntity::getActYears, actYears)
                .in(!CollectionUtils.isEmpty(oldSchemeDetailCodes), MarketingItemShareEntity::getSchemeDetailCode, oldSchemeDetailCodes)
                .remove();
        if (!CollectionUtils.isEmpty(entityList)) {
            this.saveBatch(entityList);
        }
    }

    /**
     * 查询销售计划
     *
     * @param salesCustomerCodes
     * @param salesActYear
     * @param salesBelongCostCenterCodes
     * @returno
     */
    private List<MarketingSalesPlanVo> findSalesPlanList(Set<String> salesCustomerCodes, String salesActYear, Set<String> salesBelongCostCenterCodes) {
        SalesPlanQueryVo queryVo = new SalesPlanQueryVo();
        queryVo.setYears(salesActYear);
        queryVo.setCostCenterCodeSet(salesBelongCostCenterCodes);
        queryVo.setCustomerCodeSet(salesCustomerCodes);
        List<MarketingSalesPlanVo> salesPlanList = marketingSalesPlanService.findListBySalesPlanQueryVo(queryVo);
        return salesPlanList;
    }


    /**
     * 查询发货数据
     *
     * @param deliveryCustomerCodes
     * @param deliveryActYear
     * @param deliveryItemCodes
     * @return
     */
    private List<DmsWarehouseOrderDetailVo> findDeliveryDataList(Set<String> deliveryCustomerCodes, String deliveryActYear, Set<String> deliveryItemCodes) {
        Map<String, ProductVo> productVoMap = checkHelper.findProductListByItemCodes(deliveryItemCodes);
        String date = deliveryActYear + "-01";
        Map<String, String> dateMap = FormulaGetValueComponent.getDateMonthly(date, DateUtil.DEFAULT_YEAR_MONTH_DAY);
        String startDate = dateMap.get(FormulaGetValueComponent.START_DATE);
        String endDate = dateMap.get(FormulaGetValueComponent.END_DATE);
        Set<String> productSet = Sets.newHashSet(productVoMap.keySet());
        TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
        dto.setCustomerCodes(Lists.newArrayList(deliveryCustomerCodes));
        dto.setProductCodes(Lists.newArrayList(productSet));
        dto.setSearchStartTime(startDate);
        dto.setSearchEndTime(endDate);
        dto.setItemTypeList(Lists.newArrayList("normalGoods"));
        log.error("请求dms数据:{}", JSONObject.toJSONString(dto));
//        dto.setSignStatus(DmsWarehouseOrderEnum.SIGN_STATUS.NO_SIGN.getCode());
        List<DmsWarehouseOrderDetailVo> orderDetailVos = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
        return orderDetailVos;
    }


    /**
     * 拆分数量、金额
     *
     * @param salesPlanVoList
     * @param deliveryDataList
     * @param dataList
     * @param withholdingCodeSet
     * @param excludeSchemeDetailCodes
     */
    private void splitAmountAndQuantity(List<MarketingSalesPlanVo> salesPlanVoList, List<DmsWarehouseOrderDetailVo> deliveryDataList,
                                        List<MarketingItemShareVo> dataList, Set<String> withholdingCodeSet, Set<String> excludeSchemeDetailCodes) {
        Map<String, List<MarketingSalesPlanVo>> salesPlanMap = Maps.newHashMap();
        Map<String, List<DmsWarehouseOrderDetailVo>> deliveryDataMap = Maps.newHashMap();
        Map<String, BigDecimal> productConversionMap;
        if (!CollectionUtils.isEmpty(deliveryDataList)) {
            List<String> productCodes = deliveryDataList.stream().map(x -> x.getGoodsCode()).distinct().collect(Collectors.toList());
            List<ProductVo> productVos = productVoService.findDetailsByIdsOrProductCodes(null, productCodes);
            productConversionMap = productVos.stream().collect(Collectors.toMap(ProductVo::getMaterialCode, x -> {
                if (ObjectUtils.isEmpty(x.getConversionValue())) {
                    return BigDecimal.valueOf(1);
                }
                return new BigDecimal(x.getConversionValue());
            }));
            Map<String, ProductVo> productMap = productVos.stream().collect(Collectors.toMap(x -> x.getProductCode(), Function.identity()));
            for (DmsWarehouseOrderDetailVo vo : deliveryDataList) {
                ProductVo productVo = productMap.get(vo.getGoodsCode());
                vo.setProductPhaseCode(productVo.getProductPhaseCode());
                vo.setProductPhaseName(productVo.getProductPhaseName());
            }
            deliveryDataMap = deliveryDataList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode() + x.getProductPhaseCode()));
        } else {
            productConversionMap = Maps.newHashMap();
        }
        if (!CollectionUtils.isEmpty(salesPlanVoList)) {
            salesPlanMap = salesPlanVoList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode() + x.getItemCode()));
        }
        for (MarketingItemShareVo data : dataList) {
            data.setSalesQuantity(BigDecimal.ZERO);
            data.setIncomeCost(BigDecimal.ZERO);
            String key = data.getCustomerCode() + data.getShareItemCode();
            //判断是已经产生了计提数据的
            if (withholdingCodeSet.contains(data.getSchemeDetailCode())) {
                if (deliveryDataMap.containsKey(key)) {
                    List<DmsWarehouseOrderDetailVo> orderDetailVos = deliveryDataMap.get(key);
                    BigDecimal deliveryDataQuantity = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getRealOutNum()))
                            .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                            .map(x -> x.getRealOutNum().multiply(productConversionMap.getOrDefault(x.getGoodsCode(), BigDecimal.ONE)))
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal returnDataQuantity = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getRealOutNum()))
                            .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(x.getWarehouseOrderType()))
                            .map(x -> x.getRealOutNum().multiply(productConversionMap.getOrDefault(x.getGoodsCode(), BigDecimal.ONE)))
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal salesQuantity = deliveryDataQuantity.subtract(returnDataQuantity);
                    data.setSalesQuantity(salesQuantity);

                    BigDecimal deliveryData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                            .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                            .map(DmsWarehouseOrderDetailVo::getTotalAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal returnData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                            .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(x.getWarehouseOrderType()))
                            .map(DmsWarehouseOrderDetailVo::getTotalAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal incomeCost = deliveryData.subtract(returnData);
                    data.setIncomeCost(incomeCost);
                }
            } else if (excludeSchemeDetailCodes.contains(data.getSchemeDetailCode())) {
                //判断是未产生计提数据的
                if (salesPlanMap.containsKey(key) && ObjectUtils.isNotEmpty(data.getBelongCostCenterCodes())) {
                    List<MarketingSalesPlanVo> salesPlanVos = salesPlanMap.get(key).stream().filter(x -> data.getBelongCostCenterCodes().contains(x.getCostCenterCode())).collect(Collectors.toList());
                    BigDecimal salesQuantity = salesPlanVos.stream().map(x -> x.getEstimatedSalesVolume().multiply(ObjectUtils.defaultIfNull(x.getConversionValue(), BigDecimal.ONE)))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    data.setSalesQuantity(salesQuantity);
                    BigDecimal incomeCost = salesPlanVos.stream().map(x -> x.getEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    data.setIncomeCost(incomeCost);
                }
            }
        }
    }


    /**
     * 拆分金额
     *
     * @param dataList
     * @param withHoldingVoList
     */
    private void splitShareCostAmount(List<MarketingItemShareVo> dataList, List<WithHoldingVo> withHoldingVoList, List<MarketingPlanCaseVo> caseVoList) {
        List<String> schemeDetailCodes = dataList.stream().map(x -> x.getSchemeDetailCode()).distinct().collect(Collectors.toList());
        //查询兑付金额
        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodes(schemeDetailCodes);
        //查询结案金额
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditService.findAuditAmountBySchemeDetailCodes(schemeDetailCodes);
        Map<String, BigDecimal> feeCashDetailMap = Maps.newHashMap();
        Map<String, BigDecimal> auditDetailMap = Maps.newHashMap();
        Map<String, BigDecimal> withholdingMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(withHoldingVoList)) {
            withholdingMap = withHoldingVoList.stream().collect(Collectors.toMap(x -> x.getActivitiesDetailCode(), k -> k.getActualReportAmount()));
        }
        if (!CollectionUtils.isEmpty(feeCashDetailVos)) {
            //计算费用兑付完全兑付的金额
            this.calFeeCashData(feeCashDetailMap, caseVoList);
//            feeCashDetailMap = feeCashDetailVos.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), k -> k.getThisCashAmount()));
        }
        if (!CollectionUtils.isEmpty(auditDetailVos)) {
            auditDetailMap = auditDetailVos.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), k -> k.getAuditAmount()));
        }
        Map<String, List<MarketingItemShareVo>> itemShareMap = dataList.stream().collect(Collectors.groupingBy(x -> x.getSchemeDetailCode()));

        Map<String, String> schemeConfirmMap = Maps.newHashMap();
        Map<String, BigDecimal> schemeShareItemMap = Maps.newHashMap();
        for (Map.Entry<String, List<MarketingItemShareVo>> entry : itemShareMap.entrySet()) {
            MarketingItemShareVo itemShareVo = entry.getValue().get(0);
            BigDecimal totalIncomeCost = entry.getValue().stream().map(x -> x.getIncomeCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal ratio = BigDecimal.ONE;
            Integer count = entry.getValue().size();
            Map<String, BigDecimal> shareItemRatioMap = Maps.newHashMap();
            for (MarketingItemShareVo vo : entry.getValue()) {
                if (BooleanEnum.TRUE.getCapital().equals(vo.getShareFlag())) {
                    BigDecimal calRatio = BigDecimal.ZERO;
                    if (count == 1) {
                        calRatio = ratio;
                    } else if (totalIncomeCost.compareTo(BigDecimal.ZERO) != 0) {
                        calRatio = vo.getIncomeCost().divide(totalIncomeCost, 8, BigDecimal.ROUND_HALF_DOWN);
                        ratio = ratio.subtract(calRatio);
                    }
                    shareItemRatioMap.put(vo.getShareItemCode(), calRatio);
                    count--;
                } else {
                    shareItemRatioMap.put(vo.getShareItemCode(), BigDecimal.ONE);
                }
            }
            BigDecimal cashAmount = feeCashDetailMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            BigDecimal auditAmount = auditDetailMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            BigDecimal withholdingAmount = withholdingMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            if (cashAmount.compareTo(BigDecimal.ZERO) == 1) {
                schemeConfirmMap.put(entry.getKey(), BooleanEnum.TRUE.getCapital());
            } else {
                schemeConfirmMap.put(entry.getKey(), BooleanEnum.FALSE.getCapital());
            }
            BigDecimal calAmount = itemShareVo.getApplyAmount();
            if (cashAmount.compareTo(BigDecimal.ZERO) == 1) {
                calAmount = cashAmount;
            } else if (auditAmount.compareTo(BigDecimal.ZERO) == 1) {
                calAmount = auditAmount;
            } else if (withholdingAmount.compareTo(BigDecimal.ZERO) == 1) {
                calAmount = withholdingAmount;
            }
            Integer index = entry.getValue().size();
            BigDecimal finalCalAmount = calAmount;
            for (MarketingItemShareVo vo : entry.getValue()) {
                BigDecimal shareCost = BigDecimal.ZERO;
                if (index == 1) {
                    shareCost = finalCalAmount;
                } else if (calAmount.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal calRatio = shareItemRatioMap.get(vo.getShareItemCode());
                    shareCost = calAmount.multiply(calRatio).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                    finalCalAmount = finalCalAmount.subtract(shareCost);
                }
                schemeShareItemMap.put(entry.getKey() + vo.getShareItemCode(), shareCost);
                index--;
            }
        }
        for (MarketingItemShareVo data : dataList) {
            BigDecimal cashAmount = feeCashDetailMap.getOrDefault(data.getSchemeDetailCode(), BigDecimal.ZERO);
            BigDecimal auditAmount = auditDetailMap.getOrDefault(data.getSchemeDetailCode(), BigDecimal.ZERO);
            BigDecimal withholdingAmount = withholdingMap.getOrDefault(data.getSchemeDetailCode(), BigDecimal.ZERO);
            data.setCashAmount(cashAmount);
            data.setEndCaseAmount(auditAmount);
            data.setWithholdingAmount(withholdingAmount);
            BigDecimal shareCost = schemeShareItemMap.getOrDefault(data.getSchemeDetailCode() + data.getShareItemCode(), BigDecimal.ZERO);
            data.setShareCost(shareCost);
            data.setConfirmed(schemeConfirmMap.get(data.getSchemeDetailCode()));
        }
    }


    private void calFeeCashData(Map<String, BigDecimal> feeCashDetailMap, List<MarketingPlanCaseVo> caseVoList) {
        List<String> marketingSchemeDetailCodes = caseVoList.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        List<String> cashSchemeDetailCodes = caseVoList.stream().filter(e -> StringUtils.equalsAny(e.getCashType(), CashMethodEnum.WIRE_TRANSFER.getDictCode(), CashMethodEnum.DEDUCTIONS.getDictCode(), CashMethodEnum.TICKET_BUCKLE.getDictCode()))
                .filter(Objects::nonNull).map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        List<String> replenishmentSchemeDetailCodes = caseVoList.stream().filter(e -> StringUtils.equalsAny(e.getCashType(), CashMethodEnum.REPLENISHMENT.getDictCode()))
                .filter(Objects::nonNull).map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());


        List<FeeCashDetailVo> cashDetailVos = feeCashService.findByAuditDetailCodesPass(cashSchemeDetailCodes, ProcessStatusEnum.PASS.getDictCode(), null, Collections.singletonList(CashTypeEnum.FEE.getDictCode()));
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditService.findBySchemeDetailCodesAll(marketingSchemeDetailCodes);
        //查询货补信息
        Map<String, List<DeliveryReplenishmentPoolDetailVo>> deliveryMap = deliveryReplenishmentPoolDetailService.findBySchemeDetailCodesAll(replenishmentSchemeDetailCodes);
        Map<String, List<FeeCashDetailVo>> cashDetailMap = new HashMap<>();
        Map<String, List<MarketingAuditDetailVo>> auditDetailMap = new HashMap<>();

        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(cashDetailVos)) {
            cashDetailMap = cashDetailVos.stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode()));
        }
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(auditDetailVos)) {
            auditDetailMap = auditDetailVos.stream().collect(Collectors.groupingBy(e -> e.getSchemeDetailCode()));
        }

        for (MarketingPlanCaseVo record : caseVoList) {
            //设置活动状态
            LocalDate startDate = LocalDate.parse(record.getStartDate(), DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate endDate = LocalDate.parse(record.getEndDate(), DateTimeFormatter.ofPattern(com.biz.crm.business.common.base.util.DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate now = LocalDate.now();
            if (DelFlagStatusEnum.DELETE.getCode().equals(record.getDelFlag())) {
                record.setActStatus(PlanClosureEnum.closed.getCode());
            } else {
                if (now.isBefore(startDate)) {
                    record.setActStatus(PlanClosureEnum.not_started.getCode());
                } else if (now.isAfter(endDate)) {
                    record.setActStatus(PlanClosureEnum.ended.getCode());
                } else {
                    record.setActStatus(PlanClosureEnum.in_progress.getCode());
                }
            }

            String auditStatus = AuditStatusEnum.NOT_AUDIT.getCode();
            String cashStatus = CashStatusEnum.NOT_CASH.getCode();
            BigDecimal auditAmount = BigDecimal.ZERO;
            BigDecimal cashAmount = BigDecimal.ZERO;
            if (auditDetailMap.containsKey(record.getSchemeDetailCode())) {
                auditStatus = AuditStatusEnum.PART_AUDIT.getCode();
                List<MarketingAuditDetailVo> auditDetailVoList = auditDetailMap.get(record.getSchemeDetailCode());
                for (MarketingAuditDetailVo auditDetailVo : auditDetailVoList) {
                    if (BooleanEnum.TRUE.getCapital().equals(auditDetailVo.getBeFullAudit())) {
                        auditStatus = AuditStatusEnum.WHOLE_AUDIT.getCode();
                    }
                    auditAmount = auditAmount.add(auditDetailVo.getAuditAmount());
                }
            }
            record.setAuditAmount(auditAmount);
            if (cashDetailMap.containsKey(record.getSchemeDetailCode())) {
                cashStatus = CashStatusEnum.PART_CASH.getCode();
                if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
                    List<MarketingAuditDetailVo> auditDetailVoList = auditDetailMap.get(record.getSchemeDetailCode());
                    Map<String, List<FeeCashDetailVo>> cashAuditDetailVoMap = cashDetailMap.get(record.getSchemeDetailCode()).stream().collect(Collectors.groupingBy(e -> e.getAuditDetailCode()));

                    int i = 0;
                    for (MarketingAuditDetailVo auditDetail : auditDetailVoList) {
                        if (!cashAuditDetailVoMap.containsKey(auditDetail.getAuditDetailCode())) {
                            continue;
                        }
                        List<FeeCashDetailVo> cashAuditDetailVos = cashAuditDetailVoMap.get(auditDetail.getAuditDetailCode());
                        boolean beFullCash = false;
                        for (FeeCashDetailVo cashDetailVo : cashAuditDetailVos) {
                            if (BooleanEnum.TRUE.getCapital().equals(cashDetailVo.getBeCash())) {
                                beFullCash = true;
                            }
                        }
                        if (beFullCash) {
                            i++;
                        }
                    }
                    if (i == auditDetailVoList.size()) {
                        cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                    }
                }
                cashAmount = cashDetailMap.get(record.getSchemeDetailCode()).stream().map(e -> e.getThisCashAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                //如果活动明细对应的表单类型为“随单搭赠”或“周边物料”，则活动明细的兑付金额=关联当前活动明细的结案明细的“本次结案金额”汇总，如果关联当前活动明细的结案明细有完全结案状态，则兑付状态=完全兑付；
            } else if (Arrays.asList(MarketingPlanCaseTypeEnum.matching_gift.getCode(), MarketingPlanCaseTypeEnum.material.getCode()).contains(record.getCaseType())) {
                if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
                    cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                }
                cashAmount = record.getAuditAmount();
            }
            if (StringUtils.equalsAny(record.getCashType(), CashMethodEnum.REPLENISHMENT.getDictCode())) {
                cashStatus = CashStatusEnum.PART_CASH.getCode();
                BigDecimal cashAmountTotal = BigDecimal.ZERO;
                if (deliveryMap.containsKey(record.getSchemeDetailCode())) {
                    cashAmountTotal = deliveryMap.get(record.getSchemeDetailCode()).stream().map(e -> e.getOperationAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    cashAmount = deliveryMap.get(record.getSchemeDetailCode()).stream()
                            .map(e -> ReplenishmentPoolOperationType.DELIVERY.getCode().equals(e.getOperationType()) ? e.getOperationAmount() : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (BigDecimal.ZERO.compareTo(record.getAuditAmount()) < 0 && BigDecimal.ZERO.compareTo(cashAmountTotal) == 0) {
                        cashStatus = CashStatusEnum.NOT_CASH.getCode();
                    }
                } else {
                    if (BigDecimal.ZERO.compareTo(record.getAuditAmount()) < 0) {
                        cashStatus = CashStatusEnum.NOT_CASH.getCode();
                    }
                }
                if (AuditStatusEnum.WHOLE_AUDIT.getCode().equals(auditStatus)) {
                    if (BigDecimal.ZERO.compareTo(record.getAuditAmount()) == 0) {
                        cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                    } else {
                        if (record.getAuditAmount().compareTo(cashAmountTotal) == 0) {
                            cashStatus = CashStatusEnum.WHOLE_CASH.getCode();
                        }
                    }
                }
                if (!auditDetailMap.containsKey(record.getSchemeDetailCode())) {
                    cashStatus = CashStatusEnum.NOT_CASH.getCode();
                }
            }
            if (CashStatusEnum.WHOLE_CASH.getCode().equals(cashStatus)) {
                feeCashDetailMap.put(record.getSchemeDetailCode(), cashAmount);
            }
        }
    }
}
