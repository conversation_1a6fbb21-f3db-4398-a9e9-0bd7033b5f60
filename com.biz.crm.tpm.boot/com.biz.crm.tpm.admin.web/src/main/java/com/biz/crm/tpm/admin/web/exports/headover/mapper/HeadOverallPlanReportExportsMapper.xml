<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.headover.mapper.HeadOverallPlanReportExportsMapper">

    <select id="getExportTotal" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        tpm_overall_plan_case b
        LEFT JOIN tpm_overall_plan a ON a.scheme_code = b.scheme_code
        <if test="vo.bearDepartmentStr != null and vo.bearDepartmentStr != ''">
            LEFT JOIN tpm_overall_plan_department c on b.scheme_detail_code=c.scheme_detail_code
        </if>
        <where>
            a.del_flag = '${@<EMAIL>()}'
            AND a.process_status = '3'
            AND a.scheme_type = 'head'
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="vo.schemeCode + '%'"/>
                and a.scheme_code like #{schemeCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and a.scheme_name like #{schemeName}
            </if>
            <if test="vo.costCenterName != null and vo.costCenterName != ''">
                <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
                and b.cost_center_name like #{costCenterName}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and b.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.bearDepartmentName != null and vo.bearDepartmentName != ''">
                <bind name="bearDepartmentName" value="'%' + vo.bearDepartmentName + '%'"/>
                and b.bear_department_name like #{bearDepartmentName}
            </if>
            <if test="vo.years != null and vo.years !=''">
                and b.years = #{vo.years}
            </if>
            <if test="vo.bearDepartmentStr != null and vo.bearDepartmentStr != ''">
                and c.department_name=#{vo.bearDepartmentStr}
            </if>
            <if test="vo.createName != null and vo.createName != ''">
                <bind name="createName" value="'%' + vo.createName + '%'"/>
                and b.create_name like #{createName}
            </if>
            <if test="vo.startDate != null and vo.startDate != '' and vo.endDate != null and vo.endDate != ''">
                <![CDATA[and b.start_date >= #{vo.startDate}]]>  <![CDATA[and b.end_date <= #{vo.endDate}]]>
            </if>
            order by b.create_time desc,b.scheme_detail_code desc
        </where>
    </select>
    <select id="findData"
            resultType="com.biz.crm.tpm.admin.web.exports.headover.model.HeadOverallPlanReportExportsVo">
        SELECT
        count(*)
        FROM
        tpm_overall_plan_case b
        LEFT JOIN tpm_overall_plan a ON a.scheme_code = b.scheme_code
        <if test="vo.bearDepartmentStr != null and vo.bearDepartmentStr != ''">
            LEFT JOIN tpm_overall_plan_department c on b.scheme_detail_code=c.scheme_detail_code
        </if>
        <where>
            a.del_flag = '${@<EMAIL>()}'
            AND a.process_status = '3'
            AND a.scheme_type = 'head'
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="vo.schemeCode + '%'"/>
                and a.scheme_code like #{schemeCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and a.scheme_name like #{schemeName}
            </if>
            <if test="vo.costCenterName != null and vo.costCenterName != ''">
                <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
                and b.cost_center_name like #{costCenterName}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and b.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.bearDepartmentName != null and vo.bearDepartmentName != ''">
                <bind name="bearDepartmentName" value="'%' + vo.bearDepartmentName + '%'"/>
                and b.bear_department_name like #{bearDepartmentName}
            </if>
            <if test="vo.years != null and vo.years !=''">
                and b.years = #{vo.years}
            </if>
            <if test="vo.bearDepartmentStr != null and vo.bearDepartmentStr != ''">
                and c.department_name=#{vo.bearDepartmentStr}
            </if>
            <if test="vo.createName != null and vo.createName != ''">
                <bind name="createName" value="'%' + vo.createName + '%'"/>
                and b.create_name like #{createName}
            </if>
            <if test="vo.startDate != null and vo.startDate != '' and vo.endDate != null and vo.endDate != ''">
                <![CDATA[and b.start_date >= #{vo.startDate}]]>  <![CDATA[and b.end_date <= #{vo.endDate}]]>
            </if>
            order by b.create_time desc,b.scheme_detail_code desc
        </where>
        limit #{dto.offset},#{dto.limit}
    </select>
</mapper>