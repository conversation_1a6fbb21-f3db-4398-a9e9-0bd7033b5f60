package com.biz.crm.tpm.admin.web.imports.marketingplan.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 15:57
 */
@Data
@CrmExcelImport
public class SalesPlanImportVo extends CrmExcelVo {

    @CrmExcelColumn(value = "年月")
    private String years;

    @CrmExcelColumn(value = "成本中心编码")
    private String costCenterCode;

    @CrmExcelColumn(value = "成本中心名称")
    private String costCenterName;

    @CrmExcelColumn(value = "客户编码")
    private String customerCode;

    @CrmExcelColumn(value = "客户名称")
    private String customerName;

    @CrmExcelColumn(value = "品项编码")
    private String itemCode;

    @CrmExcelColumn(value = "品项名称")
    private String itemName;

    @CrmExcelColumn(value = "产品编码")
    private String productCode;

    @CrmExcelColumn(value = "产品名称")
    private String productName;

    @CrmExcelColumn(value = "预估销售量")
    private String estimatedSalesVolumeStr;

    @CrmExcelColumn(value = "预估销售金额")
    private String estimatedCostStr;

    @CrmExcelColumn(value = "运输方式")
    private String transportType;
}
