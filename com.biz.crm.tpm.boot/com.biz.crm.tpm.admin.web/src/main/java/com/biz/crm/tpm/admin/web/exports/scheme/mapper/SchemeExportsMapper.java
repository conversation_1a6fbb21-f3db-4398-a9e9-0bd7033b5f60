package com.biz.crm.tpm.admin.web.exports.scheme.mapper;

import com.biz.crm.tpm.admin.web.exports.scheme.model.SchemeExportsDto;
import com.biz.crm.tpm.admin.web.exports.scheme.model.SchemeExportsVo;

import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 方案;(tpm_scheme)表导出功能数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */

public interface SchemeExportsMapper{
  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") SchemeExportsDto dto);
  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<SchemeExportsVo> findData(@Param("dto") SchemeExportsDto dto);
}