package com.biz.crm.tpm.admin.web.exports.costbudget.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * Vo：费用预算;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-8
 */
@ApiModel(value = "CostBudget", description = "费用预算")
@Getter
@Setter
@CrmExcelExport
public class CostBudgetExportsVo extends CrmExcelVo {


  /**
   * 编码
   */
  @CrmExcelColumn("编码")
  @ApiModelProperty(name = "code", notes = "编码", value = "编码")
  private String code;

  /**
   * 费用预算类型
   */
  @CrmExcelColumn("费用预算类型")
  @ApiModelProperty(name = "type", notes = "费用预算类型", value = "费用预算类型")
  private String type;

  /**
   * 年
   */
  @CrmExcelColumn("年")
  @ApiModelProperty(name = "year", notes = "年", value = "年")
  private Integer year;

  /**
   * 季度
   */
  @CrmExcelColumn("季度")
  @ApiModelProperty(name = "quarter", notes = "季度", value = "季度")
  private Integer quarter;

  /**
   * 月份
   */
  @CrmExcelColumn("月份")
  @ApiModelProperty(name = "month", notes = "月份", value = "月份")
  private Integer month;

  /**
   * 预算科目编码
   */
  @CrmExcelColumn("预算科目编码")
  @ApiModelProperty(name = "budgetSubjectCode", notes = "预算科目编码", value = "预算科目编码")
  private String budgetSubjectCode;
  /**
   * 预算科目名称
   */
  @CrmExcelColumn("预算科目名称")
  @ApiModelProperty(name = "budgetSubjectName", notes = "预算科目名称", value = "预算科目名称")
  private String budgetSubjectName;

  /**
   * 组织名称
   */
  @CrmExcelColumn("组织")
  @ApiModelProperty(name = "orgName", notes = "组织名称", value = "组织名称")
  private String orgName;

  /**
   * 渠道名称
   */
  @CrmExcelColumn("渠道")
  @ApiModelProperty(name = "channelName", notes = "渠道名称", value = "渠道名称")
  private String channelName;

  /**
   * 客户名称
   */
  @CrmExcelColumn("客户")
  @ApiModelProperty(name = "customerName", notes = "客户名称", value = "客户名称")
  private String customerName;

  /**
   * 门店名称
   */
  @CrmExcelColumn("门店")
  @ApiModelProperty(name = "terminalName", notes = "门店名称", value = "门店名称")
  private String terminalName;

  /**
   * 产品层级名称
   */
  @CrmExcelColumn("产品层级")
  @ApiModelProperty(name = "productLevelName", notes = "产品层级名称", value = "产品层级名称")
  private String productLevelName;

  /**
   * 产品名称
   */
  @CrmExcelColumn("产品")
  @ApiModelProperty(name = "productName", notes = "产品名称", value = "产品名称")
  private String productName;

  /**
   * 期初金额
   */
  @CrmExcelColumn("期初金额")
  @ApiModelProperty(name = "initialAmount", notes = "期初金额", value = "期初金额")
  private BigDecimal initialAmount;

  /**
   * 可用余额
   */

  @CrmExcelColumn("可用余额")
  @ApiModelProperty(name = "finalBalance", notes = "可用余额", value = "可用余额")
  private BigDecimal finalBalance;

  /**
   * 数据业务状态（启用状态）
   */
  @CrmExcelColumn("数据业务状态（启用状态）")
  @ApiModelProperty(name = "enableStatus", notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
  private String enableStatus;

  /**
   * 备注
   */
  @CrmExcelColumn("备注")
  @ApiModelProperty(name = "remark", notes = "备注", value = "备注")
  private String remark;

  /*
  非导出字段
   */

  /**
   * 渠道编码
   */
  @ApiModelProperty(name = "channelCode", notes = "渠道编码", value = "渠道编码")
  private String channelCode;

  /**
   * 客户编码
   */
  @ApiModelProperty(name = "customerCode", notes = "客户编码", value = "客户编码")
  private String customerCode;

  /**
   * 组织编码
   */
  @ApiModelProperty(name = "orgCode", notes = "组织编码", value = "组织编码")
  private String orgCode;

  /**
   * 产品编码
   */
  @ApiModelProperty(name = "productCode", notes = "产品编码", value = "产品编码")
  private String productCode;
  /**
   * 产品层级编码
   */
  @ApiModelProperty(name = "productLevelCode", notes = "产品层级编码", value = "产品层级编码")
  private String productLevelCode;

  /**
   * 门店编码
   */
  @ApiModelProperty(name = "terminalCode", notes = "门店编码", value = "门店编码")
  private String terminalCode;

}