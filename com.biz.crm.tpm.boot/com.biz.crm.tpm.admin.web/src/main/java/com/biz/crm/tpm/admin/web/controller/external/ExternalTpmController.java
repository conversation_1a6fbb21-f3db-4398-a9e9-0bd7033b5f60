package com.biz.crm.tpm.admin.web.controller.external;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.aop.SignHeaderGlobal;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.ie.local.BusinessExcelExportTemplateWriteUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.vo.CreditOrderSapVo;
import com.biz.crm.tpm.business.pay.sdk.vo.HecCallbackDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDetailDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDto;
import com.biz.crm.tpm.business.activities.contract.service.ExternalContractService;
import com.biz.crm.tpm.business.pay.sdk.enums.HecPayBusinessTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.HecPayStatusTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.enums.HecBusinessTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.*;
import com.bizunited.nebula.venus.sdk.vo.OrdinaryFileVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/18 10:43
 */
@RestController
@RequestMapping("/v1/external/tpm")
@Api(tags = "TPM数据推送接收")
@SignHeaderGlobal
@Slf4j
public class ExternalTpmController {

    @Autowired(required = false)
    private ExternalContractService externalContractService;

    @Autowired(required = false)
    private BusinessExcelExportTemplateWriteUtil businessExcelExportTemplateWriteUtil;

    @Autowired(required = false)
    private FeeCashService feeCashService;

    @Autowired(required = false)
    private WithHoldingService withHoldingService;

    @Autowired(required = false)
    private WithHoldingWriteOffService withHoldingWriteOffService;

    @Autowired(required = false)
    private ActivityPrepayService activityPrepayService;

    @Autowired(required = false)
    private CreditOrderService creditOrderService;

    @PostMapping("createBatchContract")
    @ApiOperation(value = "批量创建合同")
    public Result createBatchContract(@RequestBody ExternalContractDto contractDto) {
        try {
            Validate.notNull(contractDto.getContractCode(), "合同编码不能为空");
            List<ExternalContractDetailDto> detailList = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(contractDto.getRebateList())) {
                detailList.addAll(contractDto.getRebateList());
            }
            if (CollectionUtil.isNotEmpty(contractDto.getPromotionList())) {
                detailList.addAll(contractDto.getPromotionList());
            }
            if (CollectionUtil.isNotEmpty(contractDto.getFixedList())) {
                detailList.addAll(contractDto.getFixedList());
            }
            contractDto.setDetailList(detailList);
            contractDto.setExternalFlag(BooleanEnum.TRUE.getCapital());
            externalContractService.createContract(contractDto, BooleanEnum.TRUE.getCapital());
        } catch (Exception e) {
            log.error("错误信息:{}", e.getMessage());
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
        return Result.ok();
    }

    @ApiOperation(value = "上传合同文件")
    @PostMapping("updateContractFile")
    public Result updateContractFile(@RequestParam MultipartFile file, @RequestParam String contractCode) {
        String errMsg = null;
        try {
            OrdinaryFileVo ordinaryFileVo = businessExcelExportTemplateWriteUtil.venusFileUpload(file).get(0);
            String fileCode = ordinaryFileVo.getId();
            externalContractService.uploadContractFile(contractCode, fileCode);
        } catch (IOException e) {
            errMsg = e.getMessage();
        }
        if (ObjectUtils.isNotEmpty(errMsg)) {
            return Result.error(errMsg);
        }
        return Result.ok();
    }

    @ApiOperation(value = "SAP回传贷项订单付款状态")
    @PostMapping("updateCreditOrderTicketStatus")
    public JSONObject updateCreditOrderTicketStatus(@RequestBody JSONObject data) {
        JSONObject result = new JSONObject();
        try {
            Validate.notNull(data,"请求参数不能为空");
            Validate.notNull(data.get("DATA"),"data不能为空");
            List<CreditOrderSapVo> dtoList = data.getJSONArray("DATA").toJavaList(CreditOrderSapVo.class);
            creditOrderService.updateCreditOrderTicketStatus(dtoList);

            result.put("code", 200);
            result.put("message", "操作成功");
            result.put("success", "S");
            result.put("timestamp", new Date().getTime());
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.put("code", 500);
            result.put("message", e.getMessage());
            result.put("success", "E");
            result.put("timestamp", new Date().getTime());
            return result;
        }
    }


    @ApiOperation(value = "费控凭证回传")
    @PostMapping("/hecVoucherCallback")
    public Result<?> hecVoucherCallback(@RequestBody List<HecCallbackDto> dtoList) {
        try {
            //单据类型
            //com.biz.crm.tpm.business.pay.sdk.enums.HecBusinessTypeEnum
            //计提：计提汇总 TPM_WITH_HOLDING_COLLECT_HEC
            //冲销：费用冲销 TPM_WITH_HOLDING_WRITE_OFF_HEC
            //账扣预付：费用兑付-兑付类型=账扣预付  TPM_FEE_CASH_HEC
            //电汇兑付：费用兑付-兑付类型=费用兑付;兑付方式=电汇 TPM_FEE_CASH_HEC
            //账扣兑付：费用兑付-兑付类型=费用兑付;兑付方式=账扣 TPM_FEE_CASH_HEC
            this.voucherValidate(dtoList);
            Map<String, List<HecCallbackDto>> dtoTypeMap = dtoList.stream().collect(Collectors.groupingBy(HecCallbackDto::getBusinessType));
            dtoTypeMap.forEach((type, list) -> {
                HecBusinessTypeEnum typeEnum = HecBusinessTypeEnum.getByCode(type);
                Assert.notNull(typeEnum, "单据类型不合法!");
                Assert.notEmpty(list, "单据类型[" + typeEnum.getCode() + "]无数据!");
            });
            dtoTypeMap.forEach((type, list) -> {
                HecBusinessTypeEnum typeEnum = HecBusinessTypeEnum.getByCode(type);
                switch (typeEnum) {
                    case TPM_WITH_HOLDING_HEC:
                        withHoldingService.hecVoucherCallback(list);
                        break;
                    case TPM_WITH_HOLDING_WRITE_OFF_HEC:
                        withHoldingWriteOffService.hecVoucherCallback(list);
                        break;
                    case TPM_FEE_CASH_HEC:
                        feeCashService.hecVoucherCallback(list);
                        break;
                    default:
                        throw new IllegalArgumentException("不支持单据类型[" + typeEnum.getCode() + "]");
                }
            });
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "费控电汇付款状态回传")
    @PostMapping("/hecPayStatusCallback")
    public Result<?> hecPayStatusCallback(@RequestBody List<HecCallbackDto> dtoList) {
        try {
            //单据类型
            //com.biz.crm.tpm.business.pay.sdk.enums.HecBusinessTypeEnum
            //电汇预付：活动预付 TPM_ACTIVITY_PREPAY_HEC
            //电汇预付：费用兑付-兑付类型=电汇预付 TPM_FEE_CASH_HEC
            //电汇兑付：费用兑付-兑付类型=费用兑付;兑付方式=电汇 TPM_FEE_CASH_HEC
            this.payValidate(dtoList);
            Map<String, List<HecCallbackDto>> dtoTypeMap = dtoList.stream().collect(Collectors.groupingBy(HecCallbackDto::getBusinessType));
            dtoTypeMap.forEach((type, list) -> {
                HecPayBusinessTypeEnum typeEnum = HecPayBusinessTypeEnum.getByCode(type);
                Assert.notNull(typeEnum, "单据类型不合法!");
                Assert.notEmpty(list, "单据类型[" + typeEnum.getCode() + "]无数据!");
            });
            dtoTypeMap.forEach((type, list) -> {
                HecPayBusinessTypeEnum typeEnum = HecPayBusinessTypeEnum.getByCode(type);
                switch (typeEnum) {
                    case TPM_ACTIVITY_PREPAY_HEC:
                        activityPrepayService.hecPayStatusCallback(list);
                        break;
                    case TPM_FEE_CASH_HEC:
                        feeCashService.hecPayStatusCallback(list);
                        break;
                    default:
                        throw new IllegalArgumentException("不支持单据类型[" + typeEnum.getCode() + "]");
                }
            });
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 数据基础验证
     */
    private void baseValidate(List<HecCallbackDto> dtoList) {
        Validate.notEmpty(dtoList, "回传参数不能为空");
        Validate.isTrue(dtoList.size() <= CommonConstant.MAX_DATA_SIZE, "单次回传最大数据量[" + CommonConstant.MAX_DATA_SIZE + "]");
        dtoList.forEach(dto -> {
            Validate.notBlank(dto.getBusinessType(), "单据类型不能为空");
            Validate.notBlank(dto.getBusinessCode(), "单据编码不能为空");
        });
        Set<String> businessType = dtoList.stream().map(HecCallbackDto::getBusinessType).collect(Collectors.toSet());
        Validate.isTrue(businessType.size() == 1, "单次回传只能是一种单据类型");
    }

    /**
     * 费控凭证回传验证
     */
    private void voucherValidate(List<HecCallbackDto> dtoList) {
        this.baseValidate(dtoList);
        dtoList.forEach(dto -> {
            Validate.notBlank(dto.getOrderCode(), "凭证号不能为空");
            Validate.notNull(HecBusinessTypeEnum.getByCode(dto.getBusinessType()), "单据类型不合法");
        });
    }

    /**
     * 费控电汇付款状态回传验证
     */
    private void payValidate(List<HecCallbackDto> dtoList) {
        this.baseValidate(dtoList);
        dtoList.forEach(dto -> {
            Validate.notBlank(dto.getOrderStatus(), "付款状态不能为空");
            Validate.notBlank(dto.getBusinessDetailCode(), "单据明细编码不能为空");
            Validate.notNull(HecPayStatusTypeEnum.getByCode(dto.getOrderStatus()), "付款状态不不合法;" + JSONObject.toJSONString(HecPayStatusTypeEnum.values()));
            Validate.notNull(HecPayBusinessTypeEnum.getByCode(dto.getBusinessType()), "单据类型不合法");
        });
    }

}
