<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.admin.web.exports.account.mapper.AccountExportsMapper">
  
  <sql id="conditions">
        <if test="dto.accountCode != null and dto.accountCode != '' ">
          <bind name = "accountCode" value = " '%' + dto.accountCode + '%' " />
          and t.account_code like #{accountCode}
        </if>
        <if test="dto.accountStatus != null and dto.accountStatus != '' ">
          and t.account_status = #{dto.accountStatus}
        </if>
        <if test="dto.activitiesDetailCode != null and dto.activitiesDetailCode != '' ">
          <bind name = "activitiesDetailCode" value = " '%' + dto.dto.activitiesDetailCode + '%' " />
          and t.activities_detail_code = #{activitiesDetailCode}
        </if>
        <if test="dto.auditDetailCode != null and dto.auditDetailCode != '' ">
          <bind name = "auditDetailCode" value = " '%' + dto.auditDetailCode + '%' " />
          and t.audit_detail_code like #{auditDetailCode}
        </if>
         and t.del_flag = '${@<EMAIL>()}'
         and t.tenant_code=#{dto.tenantCode}
  </sql>
  
  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from tpm_account t 
    <where>
       <include refid="conditions"/> 
    </where>
  </select>
  
  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.account.model.AccountExportsVo">
    select *
    from tpm_account t 
    <where>
      <include refid="conditions" />
    </where>
    order by t.create_time desc, t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>