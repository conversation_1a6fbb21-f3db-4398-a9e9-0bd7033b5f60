package com.biz.crm.tpm.admin.web.preactualdetailreport.controller;

import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.preactualdetailreport.service.PreActualDetailReportService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 20:02
 **/
@RestController
@RequestMapping("/v1/preActualDetailReportController")
@Slf4j
@Api(tags = "预实明细报表")
public class PreActualDetailReportController {


    @Autowired
    private PreActualDetailReportService preActualDetailReportService;



    @GetMapping("runPreActualDetailReport")
    @ApiOperation(value = "跑预实明细报表")
    public Result runPreActualDetailReport(@RequestParam String years){
        preActualDetailReportService.calPreActualDetailReport(years);
        return Result.ok();
    }


    @ApiOperation(value = "定时任务执行预实明细报表")
    @GetMapping("schedulePreActualDetailReport")
    public Result schedulePreActualDetailReport(){
        LocalDate now = LocalDate.now();
        //获取上一个月
        LocalDate lastMonth = now.plusMonths(-1l);
        String lastYears = lastMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        String years = now.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        List<String> yearsList = Lists.newArrayList(lastYears,years);
        for (String s : yearsList) {
            preActualDetailReportService.calPreActualDetailReport(s);
        }
        return Result.ok();
    }
}
