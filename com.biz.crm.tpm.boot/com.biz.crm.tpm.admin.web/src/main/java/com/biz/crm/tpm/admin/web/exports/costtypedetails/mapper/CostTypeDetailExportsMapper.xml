<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.costtypedetails.mapper.CostTypeDetailExportsMapper">


  <select id="getExportTotal" resultType="java.lang.Integer">
    select
    count(*)
    from tpm_cost_type_detail t
    <where>
      t.del_flag = #{dto.delFlag}
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus != ''">
        and t.enable_status = #{dto.enableStatus}
      </if>
      <if test="dto.detailName != null and dto.detailName != ''">
        <bind name="detailName" value="'%' + dto.detailName + '%'"/>
        and t.detail_name like #{detailName}
      </if>
      <if test="dto.detailCode != null and dto.detailCode != ''">
        <bind name="detailCode" value="'%' + dto.detailCode + '%'"/>
        and t.detail_code like #{detailCode}
      </if>
    </where>
  </select>


  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.costtypedetails.model.CostTypeDetailExportsVo">
    select
    t.*
    from tpm_cost_type_detail t
    <where>
      t.del_flag = #{dto.delFlag}
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus != ''">
        and t.enable_status = #{dto.enableStatus}
      </if>
      <if test="dto.detailName != null and dto.detailName != ''">
        <bind name="detailName" value="'%' + dto.detailName + '%'"/>
        and t.detail_name like #{detailName}
      </if>
      <if test="dto.detailCode != null and dto.detailCode != ''">
        <bind name="detailCode" value="'%' + dto.detailCode + '%'"/>
        and t.detail_code like #{detailCode}
      </if>
    </where>
    order by t.create_time desc, t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>