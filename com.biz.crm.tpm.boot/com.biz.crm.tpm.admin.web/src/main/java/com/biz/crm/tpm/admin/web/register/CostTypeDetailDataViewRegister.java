package com.biz.crm.tpm.admin.web.register;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>预算费用规则数据视图注册器
 * 基于nebula的数据视图提供列表查询功能
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
@Component
@Slf4j
public class CostTypeDetailDataViewRegister implements DataviewRegister {

  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-预算费用规则";
  }

  @Override
  public String buildSql() {
    return "     select distinct t.* from tpm_cost_type_detail t" +
            "    inner join tpm_cost_type_detail_setting_strategy ss on t.detail_code = ss.detail_code  " +
            "     where t.tenant_code = :tenantCode " +
            "     and t.del_flag = '009' ";
  }
}
