package com.biz.crm.tpm.admin.web.exports.withholdingcollect;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: haiyang
 * @Date: 2025-04-01 18:48
 * @Desc:
 */
@Data
public class WithholdingCollectGiftAndSurroundExportVo {

    @ApiModelProperty("方案规划编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String actExecuteCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("预算项目")
    private String detailName;


    @ApiModelProperty("年度预算编码")
    private String budgetCode;

    @ApiModelProperty("合作类型")
    private String hzlx;

    @ApiModelProperty("终端编码")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("预算年月")
    private String years;

    @ApiModelProperty("预算科目")
    private String budgetSubjectName;

    @ApiModelProperty("兑付方式")
    private String cashType;

    @ApiModelProperty("费用使用部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("费用使用部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("一级部门编码")
    private String departmentOneCode;

    @ApiModelProperty("一级部门")
    private String departmentOneName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("结案金额")
    private BigDecimal auditAmount;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;

    @ApiModelProperty("结案状态")
    private String auditStatus;

    @ApiModelProperty("兑付状态")
    private String cashStatus;

    @ApiModelProperty("方案明细类型")
    private String caseType;


}
