package com.biz.crm.tpm.admin.web.register.interceptor;

import com.bizunited.nebula.europa.database.sdk.context.execute.DatabaseExecuteContent;
import com.bizunited.nebula.europa.database.sdk.strategy.ExecuteContentInterceptor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class PreActualDetailReportDataViewContentInterceptor implements ExecuteContentInterceptor {

    private final static List<String> types= Arrays.asList("成本","毛利","物流","分摊","利润","收入","物流费用");

    @Override
    public String registerCode() {
        return "tpm_pre_actual_detail_report";
    }

    @Override
    public DatabaseExecuteContent process(DatabaseExecuteContent content) {
        List<Map<String, Object>> results = content.getResults();
        if(CollectionUtils.isNotEmpty(results)){
            results.forEach(result->{
                 Object project_name = result.get("project_name");
                 if(project_name!=null){
                     final String project_name1 = (String) project_name;
                     if(types.contains(project_name1)){
                         result.put("act_years",result.get("years"));
                         result.put("years",null);
                     }
                 }
            });
        }
        return content;
    }
}
