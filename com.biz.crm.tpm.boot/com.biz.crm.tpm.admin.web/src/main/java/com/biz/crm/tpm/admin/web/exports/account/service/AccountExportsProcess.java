package com.biz.crm.tpm.admin.web.exports.account.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.account.mapper.AccountExportsMapper;
import com.biz.crm.tpm.admin.web.exports.account.model.AccountExportsDto;
import com.biz.crm.tpm.admin.web.exports.account.model.AccountExportsVo;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.PayByStrategy;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 费用上账主表;(tpm_account)导出服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-7-2
 */
@Component
public class AccountExportsProcess implements ExportProcess<AccountExportsVo> {
  @Autowired
  private AccountExportsMapper accountExportsMapper;
  @Autowired
  private DictDataVoService dictDataVoService;
  @Autowired
  private List<PayByStrategy> payByStrategies;

  /**
   * 上账状态
   */
  private final static String ACCOUNT_STATUS = "account_status";

  /**
   * 获取总条数
   *
   * @param params
   * @return
   */
  @Override
  public Integer getTotal(Map<String, Object> params) {
    AccountExportsDto dto = this.convertParams(params);
    dto.setTenantCode(TenantUtils.getTenantCode());
    return accountExportsMapper.getExportTotal(dto);
  }

  @Override
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    AccountExportsDto dto = this.convertParams(params);
    dto.setOffset(this.getPageSize() * vo.getPageNo());
    dto.setLimit(vo.getPageSize());
    dto.setTenantCode(TenantUtils.getTenantCode());
    List<AccountExportsVo> data = accountExportsMapper.findData(dto);
    //调整
    adjustData(data);
    return toJSONArray(data);
  }

  /**
   * 模板编码
   *
   * @return
   */
  @Override
  public String getBusinessCode() {
    return "TPM_ACCOUNT_EXPORT";
  }

  /**
   * 模板名称
   *
   * @return
   */
  @Override
  public String getBusinessName() {
    return "费用上账主表导出";
  }

  /**
   * 获取参数
   *
   * @param params
   * @return
   */
  private AccountExportsDto convertParams(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    // map 参数转换为对应的dto参数对象，可以手工进行修改设置
    AccountExportsDto dto = JSON.parseObject(JSON.toJSONString(params), AccountExportsDto.class);
    return dto;
  }

  /**
   * 调整数据
   *
   * @param data
   */
  private void adjustData(List<AccountExportsVo> data) {
    if (CollectionUtils.isEmpty(data)) {
      return;
    }
    //  转换数据字典值
    Map<String, List<DictDataVo>> mapDict = this.dictDataVoService.findByDictTypeCodeList(Lists.newArrayList(ACCOUNT_STATUS));
    Map<String, String> payBy = payByStrategies.stream().collect(Collectors.toMap(PayByStrategy::getCode, PayByStrategy::getName));
    for (AccountExportsVo vo : data) {
      vo.setPayBy(payBy.get(vo.getPayBy()));
      vo.setAccountStatus(this.findDictValue(mapDict, ACCOUNT_STATUS, vo.getAccountStatus()));
    }
  }

  /**
   * 获取字典值
   *
   * @param mapDict
   * @param dictTypeCode
   * @param code
   * @return
   */
  private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
    if (Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
      return StringUtils.EMPTY;
    }
    final List<DictDataVo> vos = mapDict.get(dictTypeCode);
    if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
      return StringUtils.EMPTY;
    }
    final Optional<String> first =
        vos.stream()
            .filter(a -> a.getDictCode().equals(code))
            .map(DictDataVo::getDictValue)
            .findFirst();
    return first.orElse(StringUtils.EMPTY);
  }
}