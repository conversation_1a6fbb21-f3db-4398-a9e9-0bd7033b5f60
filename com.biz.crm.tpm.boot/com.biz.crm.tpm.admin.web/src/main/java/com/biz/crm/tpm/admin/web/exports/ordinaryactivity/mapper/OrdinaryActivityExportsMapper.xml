<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.ordinaryactivity.mapper.OrdinaryActivityExportsMapper">

  <sql id="conditions">
    AND bpbm.business_code =
    '${@com.biz.crm.tpm.business.activities.ordinary.constant.ActivitiesConstant@ORDINARY_ACTIVITY_PROCESS_NAME}'
    <if test="dto.code !=null and dto.code != '' ">
      and t.code = #{dto.code}
    </if>
    <if test="dto.name !=null and dto.name != '' ">
      and t.name like concat('%',#{dto.name},'%')
    </if>
    <if test="dto.processStatus !=null and dto.processStatus != '' ">
      and bpbm.process_status = #{dto.processStatus} 
    </if>
    <if test="dto.processNumber !=null and dto.processNumber != '' ">
      and t.process_number = #{dto.processNumber}
    </if>
    <if test="dto.status !=null and dto.status != '' ">
      and t.status = #{dto.status}
    </if>
    and t.del_flag = '${@<EMAIL>()}'
    and t.tenant_code = #{dto.tenantCode}
  </sql>

  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from tpm_ordinary_activity t
    left join bpm_process_business_mapping bpbm
    on t.code = bpbm.business_no
    <where>
      <include refid="conditions"/>
    </where>
  </select>

  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.ordinaryactivity.model.OrdinaryActivityExportsVo">
    select t.*,bpbm.process_status processStatus
    from tpm_ordinary_activity t
    left join bpm_process_business_mapping bpbm
    on t.code = bpbm.business_no
    <where>
      <include refid="conditions"/>
    </where>
    order by t.create_time desc, t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>