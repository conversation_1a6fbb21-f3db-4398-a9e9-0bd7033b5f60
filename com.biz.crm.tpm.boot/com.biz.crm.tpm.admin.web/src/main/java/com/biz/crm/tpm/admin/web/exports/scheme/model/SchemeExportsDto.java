package com.biz.crm.tpm.admin.web.exports.scheme.model;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 参数传递dto：方案;
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */
@ApiModel(value = "Scheme",description = "方案")
@Getter
@Setter
public class SchemeExportsDto extends TenantDto implements Serializable,Cloneable{
  /** 方案编号 */
  @ApiModelProperty(name = "schemeCode",notes = "方案编号", value = "方案编号")
  private String schemeCode;
  /** 方案名称 */
  @ApiModelProperty(name = "schemeName",notes = "方案名称", value = "方案名称")
  private String schemeName;
  /** 方案类型 */
  @ApiModelProperty(name = "schemeType",notes = "方案类型", value = "方案类型")
  private String schemeType;
  /** 方案状态 */
  @ApiModelProperty(name = "schemeStatus",notes = "方案状态（01:待执行, 02:执行中,:03:已结束）", value = "方案状态（01:待执行, 02:执行中,:03:已结束）")
  private String schemeStatus;

  /** 偏移量 */
  @ApiModelProperty(name = "offset",notes = "偏移量", value = "偏移量")
  private Integer offset;

  /** limit */
  @ApiModelProperty(name = "limit",notes = "limit", value = "limit") 
  private Integer limit;
}