package com.biz.crm.tpm.admin.web.exports.costtypecategory.mapper;

import com.biz.crm.tpm.admin.web.exports.costtypecategory.model.CostTypeCategoryExportsDto;
import com.biz.crm.tpm.admin.web.exports.costtypecategory.model.CostTypeCategoryExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:活动大类导出mapper
 * @createTime 2022年06月07日 15:00:00
 */
public interface CostTypeCategoryExportsMapper {


  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") CostTypeCategoryExportsDto dto);

  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<CostTypeCategoryExportsVo> findData(@Param("dto") CostTypeCategoryExportsDto dto);

}
