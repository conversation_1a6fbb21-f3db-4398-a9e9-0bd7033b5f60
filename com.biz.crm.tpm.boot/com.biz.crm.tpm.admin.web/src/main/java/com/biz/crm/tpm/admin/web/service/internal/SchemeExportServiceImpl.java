package com.biz.crm.tpm.admin.web.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.ie.local.BusinessExcelExportTemplateWriteUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.marketingplan.model.*;
import com.biz.crm.tpm.admin.web.exports.regioncollect.model.*;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.*;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.*;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesTemplateConfigService;
import com.biz.crm.tpm.business.pay.sdk.enums.CashTypeEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/26 18:06
 */
@Service
public class SchemeExportServiceImpl {

    @Resource
    private BusinessExcelExportTemplateWriteUtil writeUtil;

    @Resource
    private RegionCollectService regionCollectService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;
    @Autowired(required = false)
    private MarketingPlanService marketingPlanService;
    @Autowired(required = false)
    private ActivitiesTemplateConfigService activitiesTemplateConfigService;

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private MarketingPlanCaseRepository planCaseRepository;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    /**
     * 导出大区汇总数据
     *
     * @param collectCode
     * @return
     */
    public String exportRegionCollect(String collectCode) {
        //模板文件地址
        String templateFilePath = "exceltemplate/RegionCollectTemplate.xlsx";
        RegionCollectVo vo = regionCollectService.queryByIdOrCollectCode(null, collectCode);
        Validate.isTrue(ObjectUtils.isNotEmpty(vo), "大区汇总数据不存在");
        //新文件名称
        String outputFileName = String.format("大区%s汇总", vo.getOrgName());
        Map<String, List<?>> outputMap = Maps.newHashMap();
        //营销测算
        if (CollectionUtils.isNotEmpty(vo.getMarketingEstimationList())) {
            List<RegionCollectMarketingEstimationExportVo> dataList = (List<RegionCollectMarketingEstimationExportVo>) nebulaToolkitService.copyCollectionByWhiteList(vo.getMarketingEstimationList(),
                    RegionCollectMarketingEstimationVo.class, RegionCollectMarketingEstimationExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("营销测算", dataList);
        }
        //三级部门测算
        if (CollectionUtils.isNotEmpty(vo.getDepartmentEstimationList())) {
            List<RegionCollectDepartmentEstimationExportVo> dataList = (List<RegionCollectDepartmentEstimationExportVo>) nebulaToolkitService.copyCollectionByWhiteList(vo.getDepartmentEstimationList(),
                    RegionCollectDepartmentEstimationVo.class, RegionCollectDepartmentEstimationExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("三级部门测算", dataList);
        }
        //客户损益预测
        if (CollectionUtils.isNotEmpty(vo.getGainsAndLossesList())) {
            List<RegionCollectGainsAndLossesExportVo> dataList = (List<RegionCollectGainsAndLossesExportVo>) nebulaToolkitService.copyCollectionByWhiteList(vo.getGainsAndLossesList(),
                    RegionCollectGainsAndLossesVo.class, RegionCollectGainsAndLossesExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("客户损益预测", dataList);
        }
        //品项分析
        if (CollectionUtils.isNotEmpty(vo.getItemEstimationList())) {
            List<RegionCollectItemEstimationExportVo> dataList = (List<RegionCollectItemEstimationExportVo>) nebulaToolkitService.copyCollectionByWhiteList(vo.getItemEstimationList(), RegionCollectItemEstimationVo.class,
                    RegionCollectItemEstimationExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("品项分析", dataList);
        }
        //人员提报
        if (CollectionUtils.isNotEmpty(vo.getSchemeList())) {
            List<RegionCollectSchemeExportVo> dataList = (List<RegionCollectSchemeExportVo>) nebulaToolkitService.copyCollectionByWhiteList(vo.getSchemeList(), RegionCollectSchemeVo.class,
                    RegionCollectSchemeExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("人员提报", dataList);
        }
        //方案明细
        List<MarketingPlanCaseVo> marketingPlanCaseVos = regionCollectService.findRegionMarketingCaseList(collectCode);
        if (CollectionUtils.isNotEmpty(marketingPlanCaseVos)) {
            Map<String, String> caseTypeMap = dictDataVoService.findMapByDictTypeCode("tpm_scheme_cash_type");
            Map<String, String> peopleTypeMap = dictDataVoService.findMapByDictTypeCode("people_type");

            for (MarketingPlanCaseVo caseVo : marketingPlanCaseVos) {
                if (ObjectUtils.isNotEmpty(caseVo.getCashType())) {
                    caseVo.setCashType(caseTypeMap.get(caseVo.getCashType()));
                }
                MarketingPlanCaseTypeEnum caseTypeEnum = MarketingPlanCaseTypeEnum.findByCode(caseVo.getCaseType());
                caseVo.setCaseType(caseTypeEnum.getDesc());
//                if (ObjectUtils.isNotEmpty(caseVo.getStaffType())) {
//                    caseVo.setStaffType(peopleTypeMap.get(caseVo.getStaffType()));
//                }
                if (caseTypeEnum.equals(MarketingPlanCaseTypeEnum.matching_gift)) {
                    caseVo.setLevelOrProductName(String.join(",", Optional.ofNullable(caseVo.getLevelList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
                    if (StringUtils.isEmpty(caseVo.getLevelOrProductName())) {
                        caseVo.setLevelOrProductName(String.join(",", Optional.ofNullable(caseVo.getProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
                    }
                } else if (caseTypeEnum.equals(MarketingPlanCaseTypeEnum.back)) {
                    caseVo.setFeeProductOrItemName(String.join(",", Optional.ofNullable(caseVo.getFeeItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
                    if (StringUtils.isEmpty(caseVo.getFeeProductOrItemName())) {
                        caseVo.setFeeProductOrItemName(String.join(",", Optional.ofNullable(caseVo.getFeeProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
                    }
                }
            }
            List<RegionCollectMarketingCaseExportVo> dataList = JSONObject.parseArray(JSONObject.toJSONString(marketingPlanCaseVos), RegionCollectMarketingCaseExportVo.class);
            outputMap.put("方案明细", dataList);
        }

        if (CollectionUtils.isNotEmpty(vo.getSalesPlanList())) {
            Map<String, String> caseTypeMap = dictDataVoService.findMapByDictTypeCode("tpm_transport_type");
            List<MarketingSalesPlanVo> salesPlanList = vo.getSalesPlanList();
            for (MarketingSalesPlanVo salesPlanVo : salesPlanList) {
                if (StringUtils.isNotEmpty(salesPlanVo.getTransportType())) {
                    salesPlanVo.setTransportTypeDesc(caseTypeMap.get(salesPlanVo.getTransportType()));
                }
            }
            List<RegionCollectSalesPlanExportVo> dataList = (List<RegionCollectSalesPlanExportVo>) nebulaToolkitService.copyCollectionByWhiteList(salesPlanList, MarketingSalesPlanVo.class,
                    RegionCollectSalesPlanExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("销售计划列表", dataList);
        }

        String fileCode = writeUtil.exportExcelByTemplate(templateFilePath, outputFileName, outputMap, null);

        return fileCode;
    }

    public String exportsDetail(String schemeCode, String cacheKey) {
        String templateFilePath = "exceltemplate/MarketingPlanCaseTemplate.xlsx";
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        Validate.isTrue(StringUtils.isNotBlank(schemeCode) || StringUtils.isNotBlank(cacheKey), "未查询到数据");
        String outputFileName = "";
        Map<String, List<MarketingPlanCaseVo>> map = new HashMap<>();
        MarketingPlanVo marketingPlanVo = null;
        if (StringUtils.isNotBlank(schemeCode)) {
            marketingPlanVo = marketingPlanService.queryDetails(null, schemeCode);
            Validate.notNull(marketingPlanVo, "未查询到方案数据");
            outputFileName = String.format("%s方案明细", marketingPlanVo.getSchemeName());
            Map<String, List<MarketingPlanCaseVo>> detailMap = this.marketingPlanCaseService.findListBySchemeId(marketingPlanVo.getId(), null);
            Validate.notEmpty(detailMap, "未查询到明细数据");
            map.putAll(detailMap);
        } else {
            List<MarketingPlanCaseVo> cacheList = marketingPlanService.findCacheList(cacheKey);
            Validate.notEmpty(cacheList, "未查询到明细数据");
            map.putAll(cacheList.stream().collect(Collectors.groupingBy(MarketingPlanCaseVo::getCaseType)));
            outputFileName = "营销方案明细";
        }
        Map<String, List<?>> outputMap = Maps.newHashMap();
        String salePlanCacheKey = cacheKey + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":";
        //销售计划
        List<MarketingSalesPlanVo> salesPlanVos = marketingSalesPlanService.findCacheList(salePlanCacheKey);
        if (CollectionUtils.isNotEmpty(salesPlanVos)) {
            List<MarketingSalesPlanExportVo> salesPlanExportVoList = buildSalesPlan(salesPlanVos);
            outputMap.put("销售计划", salesPlanExportVoList);
        }
        //市场推广类
        if (map.containsKey(MarketingPlanCaseTypeEnum.display.getCode())) {
            List<MarketingPlanCaseVo> caseVoList = map.get(MarketingPlanCaseTypeEnum.display.getCode());
            caseVoList = caseVoList.stream().sorted(Comparator.comparing(MarketingPlanCaseVo::getSort, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            List<MarketingDisplayExportVo> list = buildDisplayCaseList(caseVoList);
            outputMap.put("市场推广类", list);
        }
        //周边物料
        if (map.containsKey(MarketingPlanCaseTypeEnum.material.getCode())) {
            List<MarketingPlanCaseVo> caseVoList = map.get(MarketingPlanCaseTypeEnum.material.getCode());
            caseVoList = caseVoList.stream().sorted(Comparator.comparing(MarketingPlanCaseVo::getSort, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            List<MarketingMaterialExportVo> list = buildMaterialCaseList(caseVoList);
            outputMap.put("周边物料", list);
        }
        //随单
        if (map.containsKey(MarketingPlanCaseTypeEnum.matching_gift.getCode())) {
            List<MarketingPlanCaseVo> caseVoList = map.get(MarketingPlanCaseTypeEnum.matching_gift.getCode());
            caseVoList = caseVoList.stream().sorted(Comparator.comparing(MarketingPlanCaseVo::getSort, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            List<MarketingMatchingGiftExportVo> list = buildMatchingGift(caseVoList);
            outputMap.put("随单搭赠", list);
        }
        //后返
        if (map.containsKey(MarketingPlanCaseTypeEnum.back.getCode())) {
            List<MarketingPlanCaseVo> caseVoList = map.get(MarketingPlanCaseTypeEnum.back.getCode());
            caseVoList = caseVoList.stream().sorted(Comparator.comparing(MarketingPlanCaseVo::getSort, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            List<MarketingBackExportVo> list = buildBack(caseVoList);
            outputMap.put("进货返利", list);
        }
//        //固定
//        if (map.containsKey(MarketingPlanCaseTypeEnum.fixed.getCode())) {
//            List<MarketingPlanCaseVo> caseVoList = map.get(MarketingPlanCaseTypeEnum.fixed.getCode());
//            caseVoList = caseVoList.stream().sorted(Comparator.comparing(MarketingPlanCaseVo::getSort, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
//            List<MarketingFixedExportVo> list = buildFixed(caseVoList);
//            outputMap.put("其他类", list);
//        }
        //O2O
        if (map.containsKey(MarketingPlanCaseTypeEnum.o_two_o.getCode())) {
            List<MarketingPlanCaseVo> caseVoList = map.get(MarketingPlanCaseTypeEnum.o_two_o.getCode());
            caseVoList = caseVoList.stream().sorted(Comparator.comparing(MarketingPlanCaseVo::getSort, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            List<MarketingOTwoOExportVo> list = buildOTwoO(caseVoList);
            outputMap.put("O2O", list);
        }
//        //人员费用
//        if (map.containsKey(MarketingPlanCaseTypeEnum.staff_cost.getCode())) {
//            List<MarketingPlanCaseVo> caseVoList = map.get(MarketingPlanCaseTypeEnum.staff_cost.getCode());
//            caseVoList = caseVoList.stream().sorted(Comparator.comparing(MarketingPlanCaseVo::getSort, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
//            List<MarketingStaffCostExportVo> list = buildStaffCostCaseList(caseVoList);
//            outputMap.put("人员费用", list);
//        }

        //判断营销方案不为空
        if (ObjectUtils.isNotEmpty(marketingPlanVo)) {
            //营销测算
            if (CollectionUtils.isNotEmpty(marketingPlanVo.getMarketingSchemeEstimationList())) {
                List<MarketingPlanSchemeEstimationExportVo> dataList = (List<MarketingPlanSchemeEstimationExportVo>) nebulaToolkitService.copyCollectionByWhiteList(marketingPlanVo.getMarketingSchemeEstimationList(),
                        MarketingPlanSchemeEstimationVo.class, MarketingPlanSchemeEstimationExportVo.class, HashSet.class, ArrayList.class);
                outputMap.put("营销测算", dataList);
            }
            //客户损益预测
            if (CollectionUtils.isNotEmpty(marketingPlanVo.getGainsAndLossesList())) {
                List<MarketingPlanGainsAndLossesExportVo> dataList = (List<MarketingPlanGainsAndLossesExportVo>) nebulaToolkitService.copyCollectionByWhiteList(marketingPlanVo.getGainsAndLossesList(),
                        MarketingPlanGainsAndLossesVo.class, MarketingPlanGainsAndLossesExportVo.class, HashSet.class, ArrayList.class);
                outputMap.put("客户损益预测", dataList);
            }
        }


        String fileCode = writeUtil.exportExcelByTemplate(templateFilePath, outputFileName, outputMap, null);

        return fileCode;
//        List<ActivitiesTemplateConfigVo> configVoList = activitiesTemplateConfigService.findByCodes(map.keySet());
//        Map<String, DynamicHeadExportConfigVo> exportMap = new HashMap<>(configVoList.size());
//        configVoList.forEach(v -> {
//            DynamicHeadExportConfigVo vo = new DynamicHeadExportConfigVo();
//            vo.setConfigList((List<ExportConfigVo>) this.nebulaToolkitService.copyCollectionByBlankList(v.getDetails(),
//                    ActivitiesTemplateConfigDetailVo.class, ExportConfigVo.class, HashSet.class, ArrayList.class));
//            vo.setJsonArray(JSON.parseArray(JSON.toJSONString(map.get(v.getConfigCode()))));
//            exportMap.put(v.getConfigName(), vo);
//        });
//        return writeUtil.writeExcelDynamicHeadConfig(outputFileName, exportMap);
    }


    private final static ThreadLocal<Map<String, String>> transportThreadLocal = new ThreadLocal<>();

    /**
     * 构建销售计划
     *
     * @param list
     * @return
     */
    private List<MarketingSalesPlanExportVo> buildSalesPlan(List<MarketingSalesPlanVo> list) {
        Map<String, String> map = getTransport();
        List<MarketingSalesPlanExportVo> exportVos = list.stream().map(x -> {
            MarketingSalesPlanExportVo exportVo = nebulaToolkitService.copyObjectByBlankList(x, MarketingSalesPlanExportVo.class, HashSet.class, ArrayList.class);
            exportVo.setTransportType(map.get(exportVo.getTransportType()));
//            if (ObjectUtils.isNotEmpty(exportVo.getCustomerCode())){
//                exportVo.setCustomerJoiner(x.getErpCode() + "-" + exportVo.getCustomerName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getItemCode())){
//                exportVo.setItemJoiner(exportVo.getItemCode() + "-" + exportVo.getItemName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getProductCode())){
//                exportVo.setProductJoiner(exportVo.getProductCode() + "-" + exportVo.getProductName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCostCenterCode())){
//                exportVo.setCostCenterJoiner(exportVo.getCostCenterCode() + "-" + exportVo.getCostCenterName());
//            }
            return exportVo;
        }).collect(Collectors.toList());
        return exportVos;
    }

    /**
     * 构建到店费用
     *
     * @param list
     * @return
     */
    private List<MarketingDisplayExportVo> buildDisplayCaseList(List<MarketingPlanCaseVo> list) {
        List<MarketingDisplayExportVo> exportVoList = Lists.newArrayList();
        for (MarketingPlanCaseVo caseVo : list) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(caseVo));
            String itemCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String productCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_product.getCode());

            jsonObject.put("itemJoiner", itemJoinerList);
            jsonObject.put("itemCodeList", itemCodeList);
            jsonObject.put("itemNameList", itemNameList);
            jsonObject.put("productJoiner", productJoinerList);
            jsonObject.put("productCodeList", productCodeList);
            jsonObject.put("productNameList", productNameList);
            if (CollectionUtils.isNotEmpty(caseVo.getCostBasisList())) {
                StringJoiner costBasisNameList = new StringJoiner(",");
                Map<String, String> map = costBasisMap();
                for (String s : caseVo.getCostBasisList()) {
                    costBasisNameList.add(map.get(s));
                }
                jsonObject.put("costBasisNameList", costBasisNameList.toString());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCashType())) {
                Map<String, String> map = cashMap();
                String cashType = map.get(caseVo.getCashType());
                jsonObject.put("cashType", cashType);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getIsContractCost())) {
                String str = null;
                if (BooleanEnum.TRUE.getCapital().equals(caseVo.getIsContractCost())) {
                    str = BooleanEnum.TRUE.getSure();
                } else {
                    str = BooleanEnum.FALSE.getSure();
                }
                jsonObject.put("isContractCost", str);
            }
            MarketingDisplayExportVo exportVo = JSONObject.toJavaObject(jsonObject, MarketingDisplayExportVo.class);
//            if (ObjectUtils.isNotEmpty(exportVo.getDetailCode())){
//                exportVo.setDetailJoiner(exportVo.getDetailCode() + "-" + exportVo.getDetailName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBelongDepartmentCode())) {
//                exportVo.setBelongDepartmentJoiner(exportVo.getBelongDepartmentCode() + "-" + exportVo.getBelongDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBearDepartmentCode())) {
//                exportVo.setBearDepartmentJoiner(exportVo.getBearDepartmentCode() + "-" + exportVo.getBearDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCostCenterCode())) {
//                exportVo.setCostCenterJoiner(exportVo.getCostCenterCode() + "-" + exportVo.getCostCenterName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCustomerCode())) {
//                exportVo.setCustomerJoiner(caseVo.getErpCode() + "-" + exportVo.getCustomerName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getTerminalCode())) {
//                exportVo.setTerminalJoiner(exportVo.getTerminalCode() + "-" + exportVo.getTerminalName());
//            }
            exportVoList.add(exportVo);
        }
        return exportVoList;
    }


    /**
     * 构建人员费用
     *
     * @param list
     * @return
     */
    private List<MarketingStaffCostExportVo> buildStaffCostCaseList(List<MarketingPlanCaseVo> list) {
        List<MarketingStaffCostExportVo> exportVoList = Lists.newArrayList();
        for (MarketingPlanCaseVo caseVo : list) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(caseVo));
            String itemCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String productCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_product.getCode());

            jsonObject.put("itemJoiner", itemJoinerList);
            jsonObject.put("itemCodeList", itemCodeList);
            jsonObject.put("itemNameList", itemNameList);
            jsonObject.put("productJoiner", productJoinerList);
            jsonObject.put("productCodeList", productCodeList);
            jsonObject.put("productNameList", productNameList);
            if (CollectionUtils.isNotEmpty(caseVo.getCostBasisList())) {
                StringJoiner costBasisNameList = new StringJoiner(",");
                Map<String, String> map = costBasisMap();
                for (String s : caseVo.getCostBasisList()) {
                    costBasisNameList.add(map.get(s));
                }
                jsonObject.put("costBasisNameList", costBasisNameList.toString());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCashType())) {
                Map<String, String> map = cashMap();
                String cashType = map.get(caseVo.getCashType());
                jsonObject.put("cashType", cashType);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getStaffType())) {
                Map<String, String> map = staffTypeMap();
                String staffType = map.get(caseVo.getStaffType());
                jsonObject.put("staffType", staffType);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getIsContractCost())) {
                String str = null;
                if (BooleanEnum.TRUE.getCapital().equals(caseVo.getIsContractCost())) {
                    str = BooleanEnum.TRUE.getSure();
                } else {
                    str = BooleanEnum.FALSE.getSure();
                }
                jsonObject.put("isContractCost", str);
            }
            MarketingStaffCostExportVo exportVo = JSONObject.toJavaObject(jsonObject, MarketingStaffCostExportVo.class);
            exportVoList.add(exportVo);
        }
        return exportVoList;
    }


    /**
     * 周边物料
     *
     * @param list
     * @return
     */
    private List<MarketingMaterialExportVo> buildMaterialCaseList(List<MarketingPlanCaseVo> list) {
        List<MarketingMaterialExportVo> exportVos = Lists.newArrayList();
        for (MarketingPlanCaseVo caseVo : list) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(caseVo));
            String itemCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String productCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_product.getCode());

            jsonObject.put("itemJoiner", itemJoinerList);
            jsonObject.put("itemCodeList", itemCodeList);
            jsonObject.put("itemNameList", itemNameList);
            jsonObject.put("productJoiner", productJoinerList);
            jsonObject.put("productCodeList", productCodeList);
            jsonObject.put("productNameList", productNameList);
            if (CollectionUtils.isNotEmpty(caseVo.getCostBasisList())) {
                StringJoiner costBasisNameList = new StringJoiner(",");
                Map<String, String> map = costBasisMap();
                for (String s : caseVo.getCostBasisList()) {
                    costBasisNameList.add(map.get(s));
                }
                jsonObject.put("costBasisNameList", costBasisNameList.toString());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getSellMaterialType())) {
                Map<String, String> map = sellMaterialTypeMap();
                String sellMaterialType = map.get(caseVo.getSellMaterialType());
                jsonObject.put("sellMaterialType", sellMaterialType);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getTransportType())) {
                Map<String, String> map = transportTypeMap();
                String transport = map.get(caseVo.getTransportType());
                jsonObject.put("transportType", transport);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getIsContractCost())) {
                String str = null;
                if (BooleanEnum.TRUE.getCapital().equals(caseVo.getIsContractCost())) {
                    str = BooleanEnum.TRUE.getSure();
                } else {
                    str = BooleanEnum.FALSE.getSure();
                }
                jsonObject.put("isContractCost", str);
            }
            MarketingMaterialExportVo exportVo = JSONObject.toJavaObject(jsonObject, MarketingMaterialExportVo.class);
//            if (ObjectUtils.isNotEmpty(exportVo.getDetailCode())){
//                exportVo.setDetailJoiner(exportVo.getDetailCode() + "-" + exportVo.getDetailName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBelongDepartmentCode())) {
//                exportVo.setBelongDepartmentJoiner(exportVo.getBelongDepartmentCode() + "-" + exportVo.getBelongDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBearDepartmentCode())) {
//                exportVo.setBearDepartmentJoiner(exportVo.getBearDepartmentCode() + "-" + exportVo.getBearDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCostCenterCode())) {
//                exportVo.setCostCenterJoiner(exportVo.getCostCenterCode() + "-" + exportVo.getCostCenterName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCustomerCode())) {
//                exportVo.setCustomerJoiner(caseVo.getErpCode() + "-" + exportVo.getCustomerName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getMaterialCode())) {
//                exportVo.setMaterialJoiner(exportVo.getMaterialCode() + "-" + exportVo.getMaterialName());
//            }
            exportVos.add(exportVo);
        }
        return exportVos;
    }


    /**
     * 随单费用
     *
     * @param list
     * @return
     */
    public List<MarketingMatchingGiftExportVo> buildMatchingGift(List<MarketingPlanCaseVo> list) {
        List<MarketingMatchingGiftExportVo> exportVos = Lists.newArrayList();
        for (MarketingPlanCaseVo caseVo : list) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(caseVo));
            String itemJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String productJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String levelCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_level.getCode());
            String levelNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_level.getCode());
            String levelJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_level.getCode());

            jsonObject.put("itemJoiner", itemJoinerList);
            jsonObject.put("itemCodeList", itemCodeList);
            jsonObject.put("itemNameList", itemNameList);
            jsonObject.put("productJoiner", productJoinerList);
            jsonObject.put("productCodeList", productCodeList);
            jsonObject.put("productNameList", productNameList);
            jsonObject.put("levelJoiner", levelJoinerList);
            jsonObject.put("levelCodeList", levelCodeList);
            jsonObject.put("levelNameList", levelNameList);
            if (CollectionUtils.isNotEmpty(caseVo.getCostBasisList())) {
                StringJoiner costBasisNameList = new StringJoiner(",");
                Map<String, String> map = costBasisMap();
                for (String s : caseVo.getCostBasisList()) {
                    costBasisNameList.add(map.get(s));
                }
                jsonObject.put("costBasisNameList", costBasisNameList.toString());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getIsContractCost())) {
                String str = null;
                if (BooleanEnum.TRUE.getCapital().equals(caseVo.getIsContractCost())) {
                    str = BooleanEnum.TRUE.getSure();
                } else {
                    str = BooleanEnum.FALSE.getSure();
                }
                jsonObject.put("isContractCost", str);
            }
            MarketingMatchingGiftExportVo exportVo = JSONObject.toJavaObject(jsonObject, MarketingMatchingGiftExportVo.class);
//            if (ObjectUtils.isNotEmpty(exportVo.getDetailCode())){
//                exportVo.setDetailJoiner(exportVo.getDetailCode() + "-" + exportVo.getDetailName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBelongDepartmentCode())) {
//                exportVo.setBelongDepartmentJoiner(exportVo.getBelongDepartmentCode() + "-" + exportVo.getBelongDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBearDepartmentCode())) {
//                exportVo.setBearDepartmentJoiner(exportVo.getBearDepartmentCode() + "-" + exportVo.getBearDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCostCenterCode())) {
//                exportVo.setCostCenterJoiner(exportVo.getCostCenterCode() + "-" + exportVo.getCostCenterName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCustomerCode())) {
//                exportVo.setCustomerJoiner(caseVo.getErpCode() + "-" + exportVo.getCustomerName());
//            }
            exportVos.add(exportVo);
        }
        return exportVos;
    }

    /**
     * 构建后返
     *
     * @param list
     * @return
     */
    public List<MarketingBackExportVo> buildBack(List<MarketingPlanCaseVo> list) {
        List<MarketingBackExportVo> exportVos = Lists.newArrayList();
        for (MarketingPlanCaseVo caseVo : list) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(caseVo));
            String itemCodeList = getCodeStr(caseVo, OverallPlanProductEnum.fee_item.getCode());
            String itemNameList = getNameStr(caseVo, OverallPlanProductEnum.fee_item.getCode());
            String feeItemJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.fee_item.getCode());
            String productCodeList = getCodeStr(caseVo, OverallPlanProductEnum.fee_product.getCode());
            String productNameList = getNameStr(caseVo, OverallPlanProductEnum.fee_product.getCode());
            String productJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.fee_product.getCode());
            String feeBelongItemCodeList = getCodeStr(caseVo, OverallPlanProductEnum.fee_belong_item.getCode());
            String feeBelongItemNameList = getNameStr(caseVo, OverallPlanProductEnum.fee_belong_item.getCode());
            String feeBelongItemJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.fee_belong_item.getCode());
            jsonObject.put("feeItemJoiner", feeItemJoinerList);
            jsonObject.put("feeItemCodeList", itemCodeList);
            jsonObject.put("feeItemNameList", itemNameList);
            jsonObject.put("feeProductJoiner", productJoinerList);
            jsonObject.put("feeProductCodeList", productCodeList);
            jsonObject.put("feeProductNameList", productNameList);
            jsonObject.put("feeBelongItemJoiner", feeBelongItemJoinerList);
            jsonObject.put("feeBelongItemCodeList", feeBelongItemCodeList);
            jsonObject.put("feeBelongItemNameList", feeBelongItemNameList);
            if (CollectionUtils.isNotEmpty(caseVo.getCostBasisList())) {
                StringJoiner costBasisNameList = new StringJoiner(",");
                Map<String, String> map = costBasisMap();
                for (String s : caseVo.getCostBasisList()) {
                    costBasisNameList.add(map.get(s));
                }
                jsonObject.put("costBasisNameList", costBasisNameList.toString());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getRebateType())) {
                Map<String, String> map = rebateTypeMap();
                String rebateType = map.get(caseVo.getRebateType());
                jsonObject.put("rebateType", rebateType);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCashType())) {
                Map<String, String> map = cashMap();
                String cashType = map.get(caseVo.getCashType());
                jsonObject.put("cashType", cashType);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getIsContractCost())) {
                String str = null;
                if (BooleanEnum.TRUE.getCapital().equals(caseVo.getIsContractCost())) {
                    str = BooleanEnum.TRUE.getSure();
                } else {
                    str = BooleanEnum.FALSE.getSure();
                }
                jsonObject.put("isContractCost", str);
            }
            MarketingBackExportVo exportVo = JSONObject.toJavaObject(jsonObject, MarketingBackExportVo.class);
//            if (ObjectUtils.isNotEmpty(exportVo.getDetailCode())){
//                exportVo.setDetailJoiner(exportVo.getDetailCode() + "-" + exportVo.getDetailName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBelongDepartmentCode())) {
//                exportVo.setBelongDepartmentJoiner(exportVo.getBelongDepartmentCode() + "-" + exportVo.getBelongDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBearDepartmentCode())) {
//                exportVo.setBearDepartmentJoiner(exportVo.getBearDepartmentCode() + "-" + exportVo.getBearDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCostCenterCode())) {
//                exportVo.setCostCenterJoiner(exportVo.getCostCenterCode() + "-" + exportVo.getCostCenterName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCustomerCode())) {
//                exportVo.setCustomerJoiner(caseVo.getErpCode() + "-" + exportVo.getCustomerName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getConditionFormula())) {
//                exportVo.setConditionFormulaJoiner(exportVo.getConditionFormula() + "-" + exportVo.getConditionFormulaName());
//            }
            exportVos.add(exportVo);
        }
        return exportVos;
    }

    /**
     * 构建固定
     *
     * @param list
     * @return
     */
    public List<MarketingFixedExportVo> buildFixed(List<MarketingPlanCaseVo> list) {
        List<MarketingFixedExportVo> exportVos = Lists.newArrayList();
        for (MarketingPlanCaseVo caseVo : list) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(caseVo));
            String itemJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String productJoinerList = getJoinerStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            jsonObject.put("itemJoiner", itemJoinerList);
            jsonObject.put("itemCodeList", itemCodeList);
            jsonObject.put("itemNameList", itemNameList);
            jsonObject.put("productJoiner", productJoinerList);
            jsonObject.put("productCodeList", productCodeList);
            jsonObject.put("productNameList", productNameList);
            if (CollectionUtils.isNotEmpty(caseVo.getCostBasisList())) {
                StringJoiner costBasisNameList = new StringJoiner(",");
                Map<String, String> map = costBasisMap();
                for (String s : caseVo.getCostBasisList()) {
                    costBasisNameList.add(map.get(s));
                }
                jsonObject.put("costBasisNameList", costBasisNameList.toString());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCashType())) {
                Map<String, String> map = cashMap();
                String cashType = map.get(caseVo.getCashType());
                jsonObject.put("cashType", cashType);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getIsContractCost())) {
                String str = null;
                if (BooleanEnum.TRUE.getCapital().equals(caseVo.getIsContractCost())) {
                    str = BooleanEnum.TRUE.getSure();
                } else {
                    str = BooleanEnum.FALSE.getSure();
                }
                jsonObject.put("isContractCost", str);
            }
            MarketingFixedExportVo exportVo = JSONObject.toJavaObject(jsonObject, MarketingFixedExportVo.class);
//            if (ObjectUtils.isNotEmpty(exportVo.getDetailCode())){
//                exportVo.setDetailJoiner(exportVo.getDetailCode() + "-" + exportVo.getDetailName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBelongDepartmentCode())) {
//                exportVo.setBelongDepartmentJoiner(exportVo.getBelongDepartmentCode() + "-" + exportVo.getBelongDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBearDepartmentCode())) {
//                exportVo.setBearDepartmentJoiner(exportVo.getBearDepartmentCode() + "-" + exportVo.getBearDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCostCenterCode())) {
//                exportVo.setCostCenterJoiner(exportVo.getCostCenterCode() + "-" + exportVo.getCostCenterName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCustomerCode())) {
//                exportVo.setCustomerJoiner(caseVo.getErpCode() + "-" + exportVo.getCustomerName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getTerminalCode())) {
//                exportVo.setTerminalJoiner(exportVo.getTerminalCode() + "-" + exportVo.getTerminalName());
//            }
            exportVos.add(exportVo);
        }
        return exportVos;
    }

    /**
     * 构建O2O
     *
     * @param list
     * @return
     */
    public List<MarketingOTwoOExportVo> buildOTwoO(List<MarketingPlanCaseVo> list) {
        List<MarketingOTwoOExportVo> exportVos = Lists.newArrayList();
        for (MarketingPlanCaseVo caseVo : list) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(caseVo));
            String itemCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String itemJoiner = getJoinerStr(caseVo, OverallPlanProductEnum.cal_item.getCode());
            String productCodeList = getCodeStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productNameList = getNameStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            String productJoiner = getJoinerStr(caseVo, OverallPlanProductEnum.cal_product.getCode());
            jsonObject.put("itemJoiner", itemJoiner);
            jsonObject.put("itemCodeList", itemCodeList);
            jsonObject.put("itemNameList", itemNameList);
            jsonObject.put("productJoiner", productJoiner);
            jsonObject.put("productCodeList", productCodeList);
            jsonObject.put("productNameList", productNameList);
            if (CollectionUtils.isNotEmpty(caseVo.getCostBasisList())) {
                StringJoiner costBasisNameList = new StringJoiner(",");
                Map<String, String> map = costBasisMap();
                for (String s : caseVo.getCostBasisList()) {
                    costBasisNameList.add(map.get(s));
                }
                jsonObject.put("costBasisNameList", costBasisNameList.toString());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getPlatform())) {
                Map<String, String> map = platFormMap();
                String platForm = map.get(caseVo.getPlatform());
                jsonObject.put("platForm", platForm);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getIsContractCost())) {
                String str = null;
                if (BooleanEnum.TRUE.getCapital().equals(caseVo.getIsContractCost())) {
                    str = BooleanEnum.TRUE.getSure();
                } else {
                    str = BooleanEnum.FALSE.getSure();
                }
                jsonObject.put("isContractCost", str);
            }
            MarketingOTwoOExportVo exportVo = JSONObject.toJavaObject(jsonObject, MarketingOTwoOExportVo.class);
//            if (ObjectUtils.isNotEmpty(exportVo.getDetailCode())){
//                exportVo.setDetailJoiner(exportVo.getDetailCode() + "-" + exportVo.getDetailName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBelongDepartmentCode())) {
//                exportVo.setBelongDepartmentJoiner(exportVo.getBelongDepartmentCode() + "-" + exportVo.getBelongDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getBearDepartmentCode())) {
//                exportVo.setBearDepartmentJoiner(exportVo.getBearDepartmentCode() + "-" + exportVo.getBearDepartmentName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCostCenterCode())) {
//                exportVo.setCostCenterJoiner(exportVo.getCostCenterCode() + "-" + exportVo.getCostCenterName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getCustomerCode())) {
//                exportVo.setCustomerJoiner(caseVo.getErpCode() + "-" + exportVo.getCustomerName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getTerminalCode())) {
//                exportVo.setTerminalJoiner(exportVo.getTerminalCode() + "-" + exportVo.getTerminalName());
//            }
//            if (ObjectUtils.isNotEmpty(exportVo.getPlacingCity())) {
//                exportVo.setPlacingCityJoiner(exportVo.getPlacingCity() + "-" + exportVo.getPlacingCityName());
//            }
            exportVos.add(exportVo);
        }
        return exportVos;
    }

    public String getCodeStr(MarketingPlanCaseVo caseVo, String dataType) {
        String valueJoin = null;
        OverallPlanProductEnum productEnum = OverallPlanProductEnum.getEnum(dataType);
        switch (productEnum) {
            case cal_item:
                if (CollectionUtils.isNotEmpty(caseVo.getItemList())) {
                    valueJoin = caseVo.getItemList().stream().map(x -> Optional.ofNullable(x.getCode()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case cal_product:
                if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                    valueJoin = caseVo.getProductList().stream().map(x -> Optional.ofNullable(x.getCode()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case cal_level:
                if (CollectionUtils.isNotEmpty(caseVo.getLevelList())) {
                    valueJoin = caseVo.getLevelList().stream().map(x -> Optional.ofNullable(x.getCode()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_item:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeItemList())) {
                    valueJoin = caseVo.getFeeItemList().stream().map(x -> Optional.ofNullable(x.getCode()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_product:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeProductList())) {
                    valueJoin = caseVo.getFeeProductList().stream().map(x -> Optional.ofNullable(x.getCode()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_level:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeLevelList())) {
                    valueJoin = caseVo.getFeeLevelList().stream().map(x -> Optional.ofNullable(x.getCode()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_belong_item:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeBelongItemList())) {
                    valueJoin = caseVo.getFeeBelongItemList().stream().map(x -> Optional.ofNullable(x.getCode()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
        }
        return valueJoin;
    }

    public String getNameStr(MarketingPlanCaseVo caseVo, String dataType) {
        String valueJoin = null;
        OverallPlanProductEnum productEnum = OverallPlanProductEnum.getEnum(dataType);
        switch (productEnum) {
            case cal_item:
                if (CollectionUtils.isNotEmpty(caseVo.getItemList())) {
                    valueJoin = caseVo.getItemList().stream().map(x -> Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case cal_product:
                if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                    valueJoin = caseVo.getProductList().stream().map(x -> Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case cal_level:
                if (CollectionUtils.isNotEmpty(caseVo.getLevelList())) {
                    valueJoin = caseVo.getLevelList().stream().map(x -> Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_item:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeItemList())) {
                    valueJoin = caseVo.getFeeItemList().stream().map(x -> Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_product:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeProductList())) {
                    valueJoin = caseVo.getFeeProductList().stream().map(x -> Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_level:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeLevelList())) {
                    valueJoin = caseVo.getFeeLevelList().stream().map(x -> Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_belong_item:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeBelongItemList())) {
                    valueJoin = caseVo.getFeeBelongItemList().stream().map(x -> Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
        }
        return valueJoin;
    }


    public String getJoinerStr(MarketingPlanCaseVo caseVo, String dataType) {
        String valueJoin = null;
        OverallPlanProductEnum productEnum = OverallPlanProductEnum.getEnum(dataType);
        switch (productEnum) {
            case cal_item:
                if (CollectionUtils.isNotEmpty(caseVo.getItemList())) {
                    valueJoin = caseVo.getItemList().stream()
                            .map(x -> Optional.ofNullable(x.getCode()).orElse("") + "-" + Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case cal_product:
                if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                    valueJoin = caseVo.getProductList().stream()
                            .map(x -> Optional.ofNullable(x.getCode()).orElse("") + "-" + Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case cal_level:
                if (CollectionUtils.isNotEmpty(caseVo.getLevelList())) {
                    valueJoin = caseVo.getLevelList().stream()
                            .map(x -> Optional.ofNullable(x.getCode()).orElse("") + "-" + Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_item:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeItemList())) {
                    valueJoin = caseVo.getFeeItemList().stream()
                            .map(x -> Optional.ofNullable(x.getCode()).orElse("") + "-" + Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_product:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeProductList())) {
                    valueJoin = caseVo.getFeeProductList().stream()
                            .map(x -> Optional.ofNullable(x.getCode()).orElse("") + "-" + Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_level:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeLevelList())) {
                    valueJoin = caseVo.getFeeLevelList().stream()
                            .map(x -> Optional.ofNullable(x.getCode()).orElse("") + "-" + Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
            case fee_belong_item:
                if (CollectionUtils.isNotEmpty(caseVo.getFeeBelongItemList())) {
                    valueJoin = caseVo.getFeeBelongItemList().stream()
                            .map(x -> Optional.ofNullable(x.getCode()).orElse("") + "-" + Optional.ofNullable(x.getName()).orElse(""))
                            .collect(Collectors.joining(","));
                }
                break;
        }
        return valueJoin;
    }


    private static final String TPM_TRANSPORT_TYPE = "tpm_transport_type";

    //兑付方式
    private final static ThreadLocal<Map<String, String>> cashThreadLocal = new ThreadLocal<>();
    //费用依据
    private final static ThreadLocal<Map<String, String>> costBasisThreadLocal = new ThreadLocal<>();
    //执行示例
    private final static ThreadLocal<Map<String, String>> excuteExampleThreadLocal = new ThreadLocal<>();
    //结案示例
    private final static ThreadLocal<Map<String, String>> endCaseExampleThreadLocal = new ThreadLocal<>();
    //物料类型
    private final static ThreadLocal<Map<String, String>> sellMaterialTypeThreadLocal = new ThreadLocal<>();
    //周边运输方式
    private final static ThreadLocal<Map<String, String>> transprotTypeThreadLocal = new ThreadLocal<>();
    //投放平台
    private final static ThreadLocal<Map<String, String>> platFormThreadLocal = new ThreadLocal<>();
    //返利类型
    private final static ThreadLocal<Map<String, String>> rebateTypeThreadLocal = new ThreadLocal<>();

    private final static ThreadLocal<Map<String, String>> staffTypeThreadLocal = new ThreadLocal<>();

    private static final String PEOPLE_TYPE = "people_type";


    /**
     * 人员类型
     *
     * @return
     */
    private Map<String, String> staffTypeMap() {
        Map<String, String> map = staffTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(PEOPLE_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            staffTypeThreadLocal.set(map);
        }
        return map;
    }


    /**
     * 兑付方式
     *
     * @return
     */
    private Map<String, String> cashMap() {
        Map<String, String> map = cashThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_SCHEME_CASH_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            cashThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 费用依据
     *
     * @return
     */
    private Map<String, String> costBasisMap() {
        Map<String, String> map = costBasisThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_SCHEME_COST_BASIS);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            costBasisThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 执行示例
     *
     * @return
     */
    private Map<String, String> excuteExampleMap() {
        Map<String, String> map = excuteExampleThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = planCaseRepository.findCloseCaseExampleList();
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            excuteExampleThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 结案示例
     *
     * @return
     */
    private Map<String, String> endCaseExampleMap() {
        Map<String, String> map = endCaseExampleThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = planCaseRepository.findCloseCaseExampleList();
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            endCaseExampleThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 物料类型
     *
     * @return
     */
    private Map<String, String> sellMaterialTypeMap() {
        Map<String, String> map = sellMaterialTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SELL_MATERIAL_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            sellMaterialTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 运输方式
     *
     * @return
     */
    private Map<String, String> transportTypeMap() {
        Map<String, String> map = transprotTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_TRANSPORT_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            transprotTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 投放平台
     *
     * @return
     */
    private Map<String, String> platFormMap() {
        Map<String, String> map = platFormThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_PLATFORM);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            platFormThreadLocal.set(map);
        }
        return map;
    }


    /**
     * 返利类型
     *
     * @return
     */
    private Map<String, String> rebateTypeMap() {
        Map<String, String> map = rebateTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_REBATE_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            rebateTypeThreadLocal.set(map);
        }
        return map;
    }


    public Map<String, String> getTransport() {
        Map<String, String> map = transportThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(TPM_TRANSPORT_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(x -> x.getDictCode(), x -> x.getDictValue()));
            transportThreadLocal.set(map);
        }
        return map;
    }
}
