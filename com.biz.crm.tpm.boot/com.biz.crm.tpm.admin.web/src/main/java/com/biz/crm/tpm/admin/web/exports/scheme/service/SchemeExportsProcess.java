package com.biz.crm.tpm.admin.web.exports.scheme.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.scheme.mapper.SchemeExportsMapper;
import com.biz.crm.tpm.admin.web.exports.scheme.model.SchemeExportsDto;
import com.biz.crm.tpm.admin.web.exports.scheme.model.SchemeExportsVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 方案;(tpm_scheme)导出服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */
@Component
public class SchemeExportsProcess implements ExportProcess<SchemeExportsVo> {
  @Autowired
  private SchemeExportsMapper schemeExportsMapper;
  @Autowired
  private DictDataVoService dictDataVoService;

  /**
   * 方案类型
   */
  private final static String SCHEME_TYPE = "scheme_type";
  /**
   * 方案状态
   */
  private final static String SCHEME_STATUS = "schemeStatus";

  /**
   * 获取总条数
   *
   * @param params
   * @return
   */
  @Override
  public Integer getTotal(Map<String, Object> params) {
    SchemeExportsDto dto = this.convertParams(params);
    return schemeExportsMapper.getExportTotal(dto);
  }

  @Override
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    SchemeExportsDto dto = this.convertParams(params);
    dto.setOffset(this.getPageSize() * vo.getPageNo());
    dto.setLimit(vo.getPageSize());
    List<SchemeExportsVo> data = schemeExportsMapper.findData(dto);
    //调整
    adjustData(data);
    return toJSONArray(data);
  }

  /**
   * 模板编码
   *
   * @return
   */
  @Override
  public String getBusinessCode() {
    return "TPM_SCHEME_EXPORT";
  }

  /**
   * 模板名称
   *
   * @return
   */
  @Override
  public String getBusinessName() {
    return "方案导出";
  }

  /**
   * 获取参数
   *
   * @param params
   * @return
   */
  private SchemeExportsDto convertParams(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    // map 参数转换为对应的dto参数对象，可以手工进行修改设置
    SchemeExportsDto dto = JSON.parseObject(JSON.toJSONString(params), SchemeExportsDto.class);
    dto.setTenantCode(TenantUtils.getTenantCode());
    return dto;
  }

  /**
   * 调整数据
   *
   * @param data
   */
  private void adjustData(List<SchemeExportsVo> data) {
    if (CollectionUtils.isEmpty(data)) {
      return;
    }
    Map<String, List<DictDataVo>> mapDict = this.dictDataVoService.findByDictTypeCodeList(Lists.newArrayList(SCHEME_TYPE,SCHEME_STATUS));
    for (SchemeExportsVo vo : data) {
      vo.setSchemeType(this.findDictValue(mapDict,SCHEME_TYPE,vo.getSchemeType()));
      vo.setSchemeStatus(this.findDictValue(mapDict,SCHEME_STATUS,vo.getSchemeStatus()));
    }
  }

  /**
   * 获取字典值
   *
   * @param mapDict
   * @param dictTypeCode
   * @param code
   * @return
   */
  private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
    if (Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
      return StringUtils.EMPTY;
    }
    final List<DictDataVo> vos = mapDict.get(dictTypeCode);
    if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
      return StringUtils.EMPTY;
    }
    final Optional<String> first =
        vos.stream()
            .filter(a -> a.getDictCode().equals(code))
            .map(DictDataVo::getDictValue)
            .findFirst();
    return first.orElse(StringUtils.EMPTY);
  }
}