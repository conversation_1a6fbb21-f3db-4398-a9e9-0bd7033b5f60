package com.biz.crm.tpm.admin.web.register;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>发票池数据视图注册器
 * 基于nebula的数据视图提供列表查询功能
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Component
@Slf4j
public class PayInvoiceDataViewRegister implements DataviewRegister {

  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-发票池";
  }

  @Override
  public String buildSql() {
    return "select t.* from tpm_invoice t " +
        "where t.tenant_code = :tenantCode " +
        "and t.del_flag = '009' ";
  }
}
