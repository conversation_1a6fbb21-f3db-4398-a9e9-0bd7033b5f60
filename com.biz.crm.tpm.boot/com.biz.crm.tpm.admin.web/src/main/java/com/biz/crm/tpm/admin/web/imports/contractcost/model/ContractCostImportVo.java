package com.biz.crm.tpm.admin.web.imports.contractcost.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.biz.crm.business.common.ie.sdk.vo.BusinessCrmExcelVo;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/9 17:02
 */
@Data
@CrmExcelImport
@ApiModel("合同费用导入")
public class ContractCostImportVo extends BusinessCrmExcelVo {

    @ApiModelProperty("合同编码")
    @CrmExcelColumn("合同编码")
    private String contractCode;

    @ApiModelProperty("合同名称")
    @CrmExcelColumn("合同名称")
    private String contractName;

    @ApiModelProperty("合同类型")
    @CrmExcelColumn("合同类型")
    private String contractType;

    @ApiModelProperty("公司编码")
    @CrmExcelColumn("公司编码")
    private String companyCode;

    @CrmExcelColumn("客户ERP编码")
    @ApiModelProperty("客户ERP编码")
    private String erpCode;
    private String customerCode;

    @ApiModelProperty("客户名称")
    @CrmExcelColumn("客户名称")
    private String customerName;

    @ApiModelProperty("开始时间")
    @CrmExcelColumn("开始时间")
    private String contractStartDate;
    private Date contractStartTime;

    @ApiModelProperty("结束时间")
    @CrmExcelColumn("结束时间")
    private String contractEndDate;
    private Date contractEndTime;

    @ApiModelProperty("费用类型")
    @CrmExcelColumn("费用类型")
    private String caseType;

    @ApiModelProperty("活动细类编码")
    @CrmExcelColumn("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    @CrmExcelColumn("活动细类名称")
    private String detailName;

    @ApiModelProperty("归属部门编码")
    @CrmExcelColumn("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    @CrmExcelColumn("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("成本中心编码")
    @CrmExcelColumn("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @CrmExcelColumn("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("兑付方式")
    @CrmExcelColumn("兑付方式")
    private String cashType;

    @ApiModelProperty("费用归属品项")
    @CrmExcelColumn("费用归属品项")
    private String belongItemStr;

    @ApiModelProperty("费用归属品项名称")
    @CrmExcelColumn("费用归属品项名称")
    private String belongItemNameStr;

    @ApiModelProperty("考核品项编码")
//    @CrmExcelColumn("考核品项编码")
    private String khItemCodeStr;

    @ApiModelProperty("考核品项名称")
//    @CrmExcelColumn("考核品项名称")
    private String khItemNameStr;

    @ApiModelProperty("考核产品编码")
//    @CrmExcelColumn("考核产品编码")
    private String khProductCodeStr;

    @ApiModelProperty("考核产品名称")
//    @CrmExcelColumn("考核产品名称")
    private String khProductNameStr;

    @ApiModelProperty("返利品项编码")
    @CrmExcelColumn("返利品项编码")
    private String flItemCodeStr;

    @ApiModelProperty("返利品项名称")
    @CrmExcelColumn("返利品项名称")
    private String flItemNameStr;

    @ApiModelProperty("返利产品编码")
    @CrmExcelColumn("返利产品编码")
    private String flProductCodeStr;

    @ApiModelProperty("返利产品名称")
    @CrmExcelColumn("返利产品名称")
    private String flProductNameStr;

    @ApiModelProperty("开始时间")
    @CrmExcelColumn("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    @CrmExcelColumn("结束时间")
    private String endDate;

    @ApiModelProperty("返利周期")
    @CrmExcelColumn("返利周期")
    private String flzq;

    @ApiModelProperty("返利公式")
    @CrmExcelColumn("返利公式")
    private String flgs;

    @ApiModelProperty("达成条件")
    @CrmExcelColumn("达成条件")
    private String dctj;

    @ApiModelProperty("返利标准")
    @CrmExcelColumn("返利标准")
    private String flbz;

    @ApiModelProperty("本品编码")
    @CrmExcelColumn("本品编码")
    private String bpProductCodeStr;

    @ApiModelProperty("本品名称")
    @CrmExcelColumn("本品名称")
    private String bpProductNameStr;

    @ApiModelProperty("小类编码")
    @CrmExcelColumn("小类编码")
    private String bpLevelCodeStr;

    @ApiModelProperty("小类名称")
    @CrmExcelColumn("小类名称")
    private String bpLevelNameStr;

    @ApiModelProperty("满足条件数量")
    @CrmExcelColumn("满足条件数量")
    private String conditionNum;

    @ApiModelProperty("赠品编码")
//    @CrmExcelColumn("赠品编码")
    private String zpProductCodeStr;

    @ApiModelProperty("赠品名称")
//    @CrmExcelColumn("赠品名称")
    private String zpProductNameStr;

    @ApiModelProperty("赠送数量")
    @CrmExcelColumn("赠送数量")
    private String giveNum;

//    @ApiModelProperty("优惠数量-上限")
//    @CrmExcelColumn("优惠数量-上限")
//    private String discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    @CrmExcelColumn("优惠金额-上限")
    private String discountAmount;

    @ApiModelProperty("扣费标准")
    @CrmExcelColumn("扣费标准")
    private String kfbz;

    @ApiModelProperty("备注")
    @CrmExcelColumn("备注")
    private String remark;

    @ExcelIgnore
    private Integer count;

    @ExcelIgnore
    @ApiModelProperty("行")
    private Integer index;

}
