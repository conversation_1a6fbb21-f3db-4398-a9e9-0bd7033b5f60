package com.biz.crm.tpm.admin.web.exports.prepay.model;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;

/**
 * Vo：活动预付;
 * <AUTHOR> Keller
 * @date : 2022-7-2
 */
@ApiModel(value = "Prepay",description = "活动预付")
@Getter
@Setter
@CrmExcelExport
public class PrepayExportsVo extends CrmExcelVo{

  /** 审批状态 */
  @CrmExcelColumn("审批状态")
  @ApiModelProperty(name = "processStatus",notes = "审批状态", value= "审批状态")
  private String processStatus;
  /** 审批流程编码 */
  @CrmExcelColumn("审批流程编码")
  @ApiModelProperty(name = "processKey",notes = "审批流程编码", value= "审批流程编码")
  private String processKey;
  /** 预付编号 */
  @CrmExcelColumn("预付编号")
  @ApiModelProperty(name = "prepayCode",notes = "预付编号", value= "预付编号")
  private String prepayCode;
  /** 活动编号 */
  @CrmExcelColumn("活动编号")
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value= "活动编号")
  private String activitiesCode;
  /** 活动名称 */
  @CrmExcelColumn("活动名称")
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value= "活动名称")
  private String activitiesName;
  /** 预付金额合计 */
  @CrmExcelColumn("预付金额合计")
  @ApiModelProperty(name = "totalPrepayAmount",notes = "预付金额", value= "预付金额")
  private BigDecimal totalPrepayAmount;
  /** 创建人名称 */
  @CrmExcelColumn("创建人名称")
  @ApiModelProperty(name = "createName",notes = "创建人名称", value= "创建人名称")
  private String createName;
  /** 创建时间 */
  @CrmExcelColumn("创建时间")
  @ApiModelProperty(name = "createTime",notes = "创建时间", value= "创建时间")
  private String createTime;
  /** 备注 */
  @CrmExcelColumn("备注")
  @ApiModelProperty(name = "remark",notes = "备注", value= "备注")
  private String remark;

}