package com.biz.crm.tpm.admin.web.exports.costbudget.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.costbudget.mapper.CostBudgetExportsMapper;
import com.biz.crm.tpm.admin.web.exports.costbudget.model.CostBudgetExportsVo;
import com.biz.crm.tpm.admin.web.exports.costbudget.model.CostBudgetExportsDto;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetItemVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 费用预算;(tpm_cost_budget)导出服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-8
 */
@Component
public class CostBudgetExportsProcess implements ExportProcess<CostBudgetExportsVo> {
  @Autowired
  private CostBudgetExportsMapper costBudgetExportsMapper;
  @Autowired
  private DictDataVoService dictDataVoService;
  @Autowired
  private CostBudgetItemVoService costBudgetItemVoService;

  /**
   *   费用预算类型数据字典
   */
  private static final String FEE_BUDGET_TYPE = "fee_budget_type";

  /**
   * 获取总条数
   *
   * @param params
   * @return
   */
  @Override
  public Integer getTotal(Map<String, Object> params) {
    CostBudgetExportsDto dto = this.convertParams(params);
    return costBudgetExportsMapper.getExportTotal(dto);
  }

  @Override
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    CostBudgetExportsDto dto = this.convertParams(params);
    dto.setOffset(this.getPageSize() * vo.getPageNo());
    dto.setLimit(vo.getPageSize());
    List<CostBudgetExportsVo> data = costBudgetExportsMapper.findData(dto);
    //调整
    adjustData(data);
    return toJSONArray(data);
  }

  /**
   * 模板编码
   *
   * @return
   */
  @Override
  public String getBusinessCode() {
    return "TPM_COST_BUDGET_EXPORT";
  }

  /**
   * 模板名称
   *
   * @return
   */
  @Override
  public String getBusinessName() {

    return "费用预算导出";
  }

  /**
   * 获取参数
   *
   * @param params
   * @return
   */
  private CostBudgetExportsDto convertParams(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    // map 参数转换为对应的dto参数对象，可以手工进行修改设置
    CostBudgetExportsDto dto = JSON.parseObject(JSON.toJSONString(params), CostBudgetExportsDto.class);
    dto.setTenantCode(TenantUtils.getTenantCode());
    return dto;
  }

  /**
   * 调整数据
   *
   * @param data
   */
  private void adjustData(List<CostBudgetExportsVo> data) {
    if (CollectionUtils.isEmpty(data)) {
      return;
    }
    Map<String, List<DictDataVo>> mapDict = this.dictDataVoService.findByDictTypeCodeList(Lists.newArrayList(FEE_BUDGET_TYPE));
    List<String> codes = data.stream().map(CostBudgetExportsVo::getCode).collect(Collectors.toList());
    //获取明细
    Map<String, List<CostBudgetItemVo>> items = costBudgetItemVoService.findByCostBudgetCodes(codes);
    for (CostBudgetExportsVo vo : data) {
      //设置数据字典值
      vo.setEnableStatus(EnableStatusEnum.getDesc(vo.getEnableStatus()));
      vo.setType(this.findDictValue(mapDict, FEE_BUDGET_TYPE, vo.getType()));
      //获取明细余额
      List<CostBudgetItemVo> vos = items.get(vo.getCode());
      if (!CollectionUtils.isEmpty(vos)){
        //vos已经按照创建时间进行降序排序，故首位是最新的调整。
        CostBudgetItemVo costBudgetItemVo = vos.get(0);
        vo.setFinalBalance(costBudgetItemVo.getFinalBalance());
      }
    }
  }

  /**
   * 获取字典值
   *
   * @param mapDict
   * @param dictTypeCode
   * @param code
   * @return
   */
  private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
    if (Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
      return StringUtils.EMPTY;
    }
    final List<DictDataVo> vos = mapDict.get(dictTypeCode);
    if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
      return StringUtils.EMPTY;
    }
    final Optional<String> first =
        vos.stream()
            .filter(a -> a.getDictCode().equals(code))
            .map(DictDataVo::getDictValue)
            .findFirst();
    return first.orElse(StringUtils.EMPTY);
  }
}