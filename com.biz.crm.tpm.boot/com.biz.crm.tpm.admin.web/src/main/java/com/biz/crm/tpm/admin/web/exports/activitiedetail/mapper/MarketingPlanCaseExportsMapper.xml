<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.activitiedetail.mapper.MarketingPlanCaseExportsMapper">


     <select id="getCondition1" resultType="java.lang.String">
             select scheme_code from tpm_overall_plan
             <where>
                 <if test="headSchemaName != null and headSchemaName != ''">
                     and  scheme_name like concat('%',#{headSchemaName},'%')
                 </if>
                 and del_flag = '${@<EMAIL>()}'
                 and enable_status = '009'
             </where>
     </select>

    <select id="getExportTotal" resultType="java.lang.Integer">
       select  count(*)
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        left join tpm_marketing_plan_case_extend c on b.scheme_detail_code = c.scheme_detail_code and a.scheme_code =
        c.scheme_code
        where a.del_flag = '${@<EMAIL>()}'
        and b.del_flag = '${@<EMAIL>()}'
        AND a.tenant_code = #{tenantCode}
        and a.process_status = '3'
        <if test="vo.budgetSubjectCode != null and vo.budgetSubjectCode != ''">
            <bind name="budgetSubjectCode" value="'%' + vo.budgetSubjectCode + '%'"/>
            and b.budget_subject_code like #{budgetSubjectCode}
        </if>
        <if test="vo.headSchemeCode != null and vo.headSchemeCode != ''">
            <bind name="headSchemeCode" value="'%' + vo.headSchemeCode + '%'"/>
            and b.head_scheme_code like #{headSchemeCode}
        </if>
        <if test="vo.headSchemeCodes != null and vo.headSchemeCodes.size()>0">
            and b.head_scheme_code in
            <foreach collection="vo.headSchemeCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.budgetSubjectName != null and vo.budgetSubjectName != ''">
            <bind name="budgetSubjectName" value="'%' + vo.budgetSubjectName + '%'"/>
            and b.budget_subject_name like #{budgetSubjectName}
        </if>
        <if test="vo.auditStatus != null and vo.auditStatus != ''">
            and b.audit_status = #{vo.auditStatus}
        </if>
        <if test="vo.cashStatus != null and vo.cashStatus != ''">
            and b.cash_status = #{vo.cashStatus}
        </if>
        <if test="vo.pushStatus != null and vo.pushStatus != ''">
            and c.push_status = #{vo.pushStatus}
        </if>
        <if test="vo.hzlx != null and vo.hzlx != ''">
            and c.hzlx = #{vo.hzlx}
        </if>
        <if test="vo.releaseCode != null and vo.releaseCode != ''">
            <bind name="releaseCode" value="'%' + vo.releaseCode + '%'"/>
            and b.release_code like #{releaseCode}
        </if>
        <if test="vo.releaseName != null and vo.releaseName != ''">
            <bind name="releaseName" value="'%' + vo.releaseName + '%'"/>
            and b.release_name like #{releaseName}
        </if>
        <if test="vo.withHoldingStatus != null and vo.withHoldingStatus != ''">
            and c.with_holding_status = #{vo.withHoldingStatus}
        </if>
        <if test="vo.detailCode != null and vo.detailCode != ''">
            <bind name="detailCode" value="'%' + vo.detailCode + '%'"/>
            and b.detail_code like #{detailCode}
        </if>
        <if test="vo.detailName != null and vo.detailName != ''">
            <bind name="detailName" value="'%' + vo.detailName + '%'"/>
            and b.detail_name like #{detailName}
        </if>
        <if test="vo.schemeName != null and vo.schemeName != ''">
            <bind name="likeSchemeName" value="'%' + vo.schemeName + '%'"/>
            and a.scheme_name like #{likeSchemeName}
        </if>
        <if test="vo.schemeCode != null and vo.schemeCode != ''">
            <bind name="schemeCode" value="'%' + vo.schemeCode + '%'"/>
            and b.scheme_code like #{schemeCode}
        </if>
        <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
            <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
            and b.scheme_detail_code like #{schemeDetailCode}
        </if>
        <if test="vo.schemeDetailCodeList != null and vo.schemeDetailCodeList.size()>0">
            and b.scheme_detail_code in
            <foreach collection="vo.schemeDetailCodeList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.actName != null and vo.actName != ''">
            <bind name="actName" value="'%' + vo.actName + '%'"/>
            and b.act_name like #{actName}
        </if>
        <if test="vo.terminalCode != null and vo.terminalCode != ''">
            <bind name="likeTerminalCode" value="'%' + vo.terminalCode + '%'"/>
            and b.terminal_code like #{likeTerminalCode}
        </if>
        <if test="vo.terminalName != null and vo.terminalName != ''">
            <bind name="likeTerminalName" value="'%' + vo.terminalName + '%'"/>
            and b.terminal_name like #{likeTerminalName}
        </if>
        <if test="vo.customerCode != null and vo.customerCode != ''">
            <bind name="likeCustomerCode" value="'%' + vo.customerCode + '%'"/>
            and b.customer_code like #{likeCustomerCode}
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            <bind name="likeCustomerName" value="'%' + vo.customerName + '%'"/>
            and b.customer_name like #{likeCustomerName}
        </if>
        <if test="vo.years != null and vo.years != ''">
            and b.years = #{vo.years}
        </if>
        <if test="vo.startDate != null and vo.startDate != ''">
            and b.start_date <![CDATA[ >= ]]> #{vo.startDate}
        </if>
        <if test="vo.endDate != null and vo.endDate != ''">
            and b.end_date <![CDATA[ <= ]]> #{vo.endDate}
        </if>
        <if test="vo.actExecuteCode != null and vo.actExecuteCode != ''">
            and b.act_execute_code = #{vo.actExecuteCode}
        </if>
        <if test="vo.changeFlag == 'N'.toString()">
            and a.scheme_type != 'change'
        </if>
        <if test="vo.bearDepartmentName != null and vo.bearDepartmentName != ''">
            <bind name="bearDepartmentName" value="'%' + vo.bearDepartmentName + '%'"/>
            and b.bear_department_name like #{bearDepartmentName}
        </if>
        <if test="vo.bearDepartmentCode != null and vo.bearDepartmentCode != ''">
            <bind name="bearDepartmentCode" value="'%' + vo.bearDepartmentCode + '%'"/>
            and b.bear_department_code like #{bearDepartmentCode}
        </if>
        <if test="vo.belongDepartmentCode != null and vo.belongDepartmentCode != ''">
            <bind name="belongDepartmentCode" value="'%' + vo.belongDepartmentCode + '%'"/>
            and b.belong_department_code like #{belongDepartmentCode}
        </if>
        <if test="vo.belongDepartmentName != null and vo.belongDepartmentName != ''">
            <bind name="belongDepartmentName" value="'%' + vo.belongDepartmentName + '%'"/>
            and b.belong_department_name like #{belongDepartmentName}
        </if>
        <if test="vo.costCenterCode != null and vo.costCenterCode != ''">
            <bind name="costCenterCode" value="'%' + vo.costCenterCode + '%'"/>
            and b.cost_center_code like #{costCenterCode}
        </if>
        <if test="vo.costCenterName != null and vo.costCenterName != ''">
            <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
            and b.cost_center_name like #{costCenterName}
        </if>
        <if test="vo.caseType != null and vo.caseType != ''">
            and b.case_type = #{vo.caseType}
        </if>
        <if test="vo.cashType != null and vo.cashType != ''">
            and b.cash_type = #{vo.cashType}
        </if>
        <if test="vo.actStatus != null and vo.actStatus != ''">
            <choose>
                <when test=" '0'.toString() == vo.actStatus">
                    <![CDATA[and b.start_date > DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
                </when>
                <when test=" '1'.toString() == vo.actStatus">
                    <![CDATA[and b.start_date <= DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
                    <![CDATA[and b.end_date >= DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
                </when>
                <when test=" '2'.toString() == vo.actStatus">
                    <![CDATA[and b.end_date <= DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
                </when>
            </choose>
        </if>
        <if test="vo.createName != null and vo.createName != ''">
            <bind name="createName" value="'%' + vo.createName + '%'"/>
            and a.create_name like #{createName}
        </if>
        <if test="vo.createAccount != null and vo.createAccount != ''">
            <bind name="createAccount" value="'%' + vo.createAccount + '%'"/>
            and a.create_account like #{createAccount}
        </if>
        <if test="vo.belongDepartmentCodes != null and vo.belongDepartmentCodes.size()>0">
            and b.belong_department_code in
            <foreach collection="vo.belongDepartmentCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.bearDepartmentCodes != null and vo.bearDepartmentCodes.size()>0">
            and b.bear_department_code in
            <foreach collection="vo.bearDepartmentCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.actDesc != null and vo.actDesc != ''">
            <bind name="actDesc" value="'%' + vo.actDesc + '%'"/>
            and c.act_desc like #{actDesc}
        </if>
        <if test="vo.orgCodeList != null and vo.orgCodeList.size()>0">
            and b.belong_department_code in
            <foreach collection="vo.orgCodeList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.withholding != null and vo.withholding != ''">
            and b.scheme_detail_code not in
            (
            select
            IFNULL(t1.activities_detail_code,'')
            from tpm_with_holding t1
            where t1.del_flag = '${@<EMAIL>()}'
            and (t1.status in ('2','3') or t1.be_adjust='Y' or t1.confirm_status='confirmed')
            )
        </if>
        <if test="vo.processStatus != null and vo.processStatus != ''">
            and a.process_status = #{vo.processStatus}
        </if>
        <if test="vo.yearsSet != null and vo.yearsSet.size > 0">
            and b.years in
            <foreach item="item" collection="vo.yearsSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="vo.subjectCodes != null and vo.subjectCodes.size > 0">
            and b.budget_subject_code in
            <foreach item="item" collection="vo.subjectCodes" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="vo.customerCodes != null and vo.customerCodes.size > 0">
            and b.customer_code in
            <foreach item="item" collection="vo.customerCodes" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="vo.cashTypeSet != null and vo.cashTypeSet.size > 0">
            and b.cash_type in
            <foreach item="item" collection="vo.cashTypeSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="vo.detailCodeList != null and vo.detailCodeList.size() > 0">
            and b.detail_code in
            <foreach item="item" collection="vo.detailCodeList" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="findData" resultType="com.biz.crm.tpm.admin.web.exports.activitiedetail.model.MarketingPlanCaseExportsVo"  parameterType="com.biz.crm.tpm.admin.web.exports.activitiedetail.model.MarketingPlanCaseExportsDto">
        select
        a.*,c.*,
        b.scheme_name,b.department_name,b.position_code,b.org_code,b.org_name from tpm_marketing_plan_case a
        join tpm_marketing_plan_case_extend c on a.scheme_detail_code = c.scheme_detail_code
        left join tpm_marketing_plan b on a.scheme_code = b.scheme_code
        <where>
            b.del_flag = '${@<EMAIL>()}'
            and a.del_flag = '${@<EMAIL>()}'
            <if test="vo.withholding != null and vo.withholding != ''">
                and a.scheme_detail_code not in
                (
                select
                IFNULL(t1.activities_detail_code,'')
                from tpm_with_holding t1
                where t1.del_flag = '${@<EMAIL>()}'
                and (t1.status in ('2','3') or t1.be_adjust='Y')
                )
            </if>
            <if test="processStatus != null and processStatus != ''">
                and b.process_status = #{processStatus}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and a.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and b.scheme_name like #{schemeName}
            </if>
            <if test="vo.categoryName != null and vo.categoryName != ''">
                <bind name="categoryName" value="'%' + vo.categoryName + '%'"/>
                and a.category_name like #{categoryName}
            </if>
            <if test="vo.detailName != null and vo.detailName != ''">
                <bind name="detailName" value="'%' + vo.detailName + '%'"/>
                and a.detail_name like #{detailName}
            </if>
            <if test="vo.belongDepartmentName != null and vo.belongDepartmentName != ''">
                <bind name="belongDepartmentName" value="'%' + vo.belongDepartmentName + '%'"/>
                and a.belong_department_name like #{belongDepartmentName}
            </if>
            <if test="vo.costCenterName != null and vo.costCenterName != ''">
                <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
                and a.cost_center_name like #{costCenterName}
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                <bind name="customerName" value="'%' + vo.customerName + '%'"/>
                and a.customer_name like #{customerName}
            </if>
            <if test="vo.actDesc != null and vo.actDesc != ''">
                <bind name="actDesc" value="'%' + vo.actDesc + '%'"/>
                and c.act_desc like #{actDesc}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and a.years = #{vo.years}
            </if>
            <if test="vo.auditStatus != null and vo.auditStatus != ''">
                and a.audit_status = #{vo.auditStatus}
            </if>
            <if test="vo.yearsLess != null and vo.yearsLess != ''">
                and a.years &lt; #{vo.yearsLess}
            </if>
            <if test="vo.endDateLess != null and vo.endDateLess != ''">
                and a.end_date &lt; #{vo.endDateLess}
            </if>
            <if test="vo.caseType != null and vo.caseType != ''">
                and a.case_type = #{vo.caseType}
            </if>
            <if test="vo.cashType != null and vo.cashType != ''">
                and a.cash_type = #{vo.cashType}
            </if>
            <if test="vo.excludeWholeAudit == 'Y'.toString()">
                and a.audit_status != '${@com.biz.crm.business.common.base.eunm.AuditStatusEnum@WHOLE_AUDIT.getCode()}'
            </if>
            <if test="vo.changeFlag == 'N'.toString()">
                and b.scheme_type != 'change'
            </if>
            <choose>
                <when test="vo.changeSchemeQueryFlag != null and vo.changeSchemeQueryFlag != '' and 'Y'.toString() == vo.changeSchemeQueryFlag ">
                    <if test="vo.originalSchemeCode != null and vo.originalSchemeCode != ''">
                        <bind name="originalSchemeCode" value="'%' + vo.originalSchemeCode + '%'"/>
                        and b.scheme_code like #{originalSchemeCode}
                    </if>
                    <if test="vo.schemeCode != null and vo.schemeCode != ''">
                        and a.scheme_detail_code not in
                        (
                        SELECT
                        c.original_scheme_detail_code FROM tpm_marketing_plan_case c
                        LEFT JOIN tpm_marketing_plan d ON c.scheme_code = d.scheme_code
                        WHERE
                        d.scheme_code != #{vo.schemeCode}
                        AND d.scheme_type = 'change'
                        AND d.process_status != '3'
                        AND c.original_scheme_detail_code IS NOT NULL
                        GROUP BY
                        c.original_scheme_detail_code
                        )
                    </if>
                    and (
                    not exists (
                    SELECT
                    1
                    FROM
                    tpm_with_holding wh
                    LEFT JOIN tpm_with_holding_write_off wo ON wh.with_holding_code = wo.with_holding_code
                    WHERE
                    wh.del_flag = '009'
                    AND wh.business_code = a.scheme_detail_code
                    )
                    and
                    not exists (
                    SELECT
                    1
                    FROM
                    tpm_marketing_audit ma
                    LEFT JOIN tpm_marketing_audit_detail mab ON ma.audit_code = mab.audit_code
                    WHERE
                    ma.del_flag = '009'
                    AND mab.scheme_detail_code = a.scheme_detail_code
                    )
                    and
                    not exists (
                    SELECT
                    1
                    FROM
                    tpm_fee_cash fc
                    LEFT JOIN tpm_fee_cash_detail fcd ON fc.audit_code = fcd.audit_code
                    WHERE
                    fc.del_flag = '009' and fcd.scheme_detail_code = a.scheme_detail_code
                    )
                    )
                </when>
                <otherwise>
                    <if test="vo.schemeCode != null and vo.schemeCode != ''">
                        <bind name="schemeCode" value="'%' + vo.schemeCode + '%'"/>
                        and b.scheme_code like #{schemeCode}
                    </if>
                </otherwise>
            </choose>
            <if test="vo.companyCode != null and vo.companyCode != ''">
                and a.company_code = #{vo.companyCode}
            </if>
            <if test="vo.excludeCaseTypeList != null and vo.excludeCaseTypeList.size() > 0">
                and a.case_type not in
                <foreach collection="vo.excludeCaseTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="originalSchemeDetailCodes != null and originalSchemeDetailCodes.size()>0">
                and a.scheme_detail_code not in
                <foreach collection="originalSchemeDetailCodes" open="(" close=")" separator="," item="item"
                         index="index">
                    #{item}
                </foreach>
            </if>
            <if test="vo.terminalName != null and vo.terminalName != ''">
                <bind name="terminalName" value="'%' + vo.terminalName + '%'"/>
                and a.terminal_name like #{terminalName}
            </if>
            <if test="vo.actExecuteCode">
                <bind name="actExecuteCode" value="'%' + vo.actExecuteCode + '%'"/>
                and a.act_execute_code like #{actExecuteCode}
            </if>
            order by a.create_time desc,a.scheme_detail_code desc
            limit #{dto.offset},#{dto.limit}
        </where>
    </select>


</mapper>