<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.approvalCollect.mapper.ApprovalCollectExportsMapper">

  <sql id="conditions">
    <if test="dto.enableStatus != null and dto.enableStatus != '' ">
      and t.enable_status = #{dto.enableStatus}
    </if>
    <if test="dto.tenantCode != null and dto.tenantCode != '' ">
      and t.tenant_code = #{dto.tenantCode}
    </if>
    <if test="dto.code != null and dto.code != '' ">
      and t.code = #{dto.code}
    </if>
    <if test="dto.name != null and dto.name != '' ">
      <bind name = "name" value = " '%' + dto.name + '%' " />
      and t.name like #{name}
    </if>
    <if test="dto.type != null and dto.type != '' ">
      and t.type = #{dto.type}
    </if>
    and t.del_flag = '${@<EMAIL>()}'
  </sql>

  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from tpm_approval_collect t
    <where>
      <include refid="conditions"/>
    </where>
  </select>

  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.approvalCollect.model.ApprovalCollectExportsVo">
    select *
    from tpm_approval_collect t
    <where>
      <include refid="conditions" />
    </where>
    order by t.create_time desc, t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>