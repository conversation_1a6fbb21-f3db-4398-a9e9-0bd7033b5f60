<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.admin.web.exports.audit.mapper.AuditExportsMapper">
  
  <sql id="conditions">
        <if test="dto.tenantCode != null and dto.tenantCode != '' ">
          and t.tenant_code = #{dto.tenantCode}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != '' ">
          and t.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.processStatus != null and dto.processStatus != '' ">
          and bpbm.process_status = #{dto.processStatus}
        </if>
        <if test="dto.auditName != null and dto.auditName != ''">
          <bind name="auditName" value="'%' + dto.auditName + '%'"/>
          and t.audit_name like #{auditName}
        </if>
        <if test="dto.auditCode != null and dto.auditCode != ''">
          <bind name="auditCode" value="'%' + dto.auditCode + '%'"/>
          and t.audit_code like #{auditCode}
        </if>
         and t.del_flag = '${@<EMAIL>()}'
  </sql>
  
  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from tpm_audit t
    left join bpm_process_business_mapping bpbm on (bpbm.business_no=t.audit_code AND bpbm.business_code = '${@com.biz.crm.tpm.business.pay.sdk.constant.PayConstant@PROCESS_AUDIT_ACTIVITIES}')
    <where>
       <include refid="conditions"/> 
    </where>
  </select>
  
  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.audit.model.AuditExportsVo">
    select distinct t.*, bpbm.process_status, bpbm.process_no
    from tpm_audit t 
    left join bpm_process_business_mapping bpbm on (bpbm.business_no=t.audit_code AND bpbm.business_code = '${@com.biz.crm.tpm.business.pay.sdk.constant.PayConstant@PROCESS_AUDIT_ACTIVITIES}')
    <where>
      <include refid="conditions" />
    </where>
    order by t.create_time desc, t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>