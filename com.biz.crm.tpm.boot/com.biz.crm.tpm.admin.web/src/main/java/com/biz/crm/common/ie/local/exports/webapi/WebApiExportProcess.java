package com.biz.crm.common.ie.local.exports.webapi;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.common.ie.local.config.ImportExportProperties;
import com.biz.crm.common.ie.local.service.spring.SpringControllerApiService;
import com.biz.crm.common.ie.sdk.constant.ImportExportConstant;
import com.biz.crm.common.ie.sdk.dto.ExportWebApiDto;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.utils.WebApiParamsTools;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.common.ie.sdk.vo.WebApiExportVo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @title WebApiExportProcess
 * @date 2023/10/8 14:37
 * @description webApi导出执行
 */
@Slf4j
@Component
public class WebApiExportProcess implements ExportProcess<WebApiExportVo> {

    @Autowired
    private ImportExportProperties importExportProperties;

    @Autowired
    private SpringControllerApiService springControllerApiService;

    @Override
    public String getBusinessCode() {
        return ImportExportConstant.EXPORT_BIZ_CODE_EXPORT_WEB_API;
    }

    @Override
    public String getBusinessName() {
        return "webApi导出";
    }

    @Override
    public Integer getPageSize() {
        return CommonConstant.IE_EXPORT_PAGE_SIZE;
    }

    @Override
    public Integer getQueryPageSize() {
        int queryPageSize = importExportProperties.getExportProperties().getQueryPageSize();
        if (queryPageSize <= 0) {
            queryPageSize = CommonConstant.IE_EXPORT_EACH_PAGE_SIZE;
        } else if (queryPageSize > CommonConstant.IE_EXPORT_MAX_PAGE_SIZE) {
            queryPageSize = CommonConstant.IE_EXPORT_MAX_PAGE_SIZE;
        }
        return queryPageSize;
    }


    @Override
    public Class<WebApiExportVo> findCrmExcelVoClass() {
        throw new RuntimeException("webApi导出不依赖该方法返回Excel表头，注意纠正");
    }


    /**
     * 获取记录数
     *
     * @param params 自定义参数
     * @return 记录数
     */
    @Override
    public Integer getTotal(Map<String, Object> params) {
        this.validateQueryPageSize();
        //1.构建webApi请求参数
        final ExportWebApiDto exportWebApiDto = this.getWebApiParam(params);
        //2.获取请求参数的json字符串
        String requestParamJson = getRequestParamFilterWebUrl(params);
        //3.获取记录数
        Long pageTotal = springControllerApiService.getPageTotal(exportWebApiDto.getWebApiUrl(),
                exportWebApiDto.getRequestMapping(), requestParamJson);
        Validate.isTrue(pageTotal <= CommonConstant.IE_EXPORT_MAX_TOTAL, "执行接口导出时，" +
                "单次最大导出[" + CommonConstant.IE_EXPORT_MAX_TOTAL + "]条,请输入更多查询条件!!");
        return pageTotal.intValue();
    }

    /**
     * 2.获取请求参数的json字符串
     *
     * @param params
     * @return
     */
    private String getRequestParamFilterWebUrl(Map<String, Object> params) {
        Map<String, Object> requestParamMap = Maps.newHashMap(params);
        //只在构建webApi请求参数时使用
        requestParamMap.remove(WebApiParamsTools.CODE_PARAMETER_WEB_URL);
        return JSONUtil.toJsonStr(requestParamMap);
    }

    /**
     * 1.构建webApi请求参数
     *
     * @param params
     * @return
     */
    private ExportWebApiDto getWebApiParam(Map<String, Object> params) {
        Object requestMapping = params.get(WebApiParamsTools.REQUEST_MAPPING);
        Validate.notNull(requestMapping, "webApi导出时，请求方式不能为空！");
        Object url = params.get(WebApiParamsTools.CODE_PARAMETER_WEB_URL);
        Validate.notNull(url, "webApi导出时，url地址不能为空！");
        ExportWebApiDto exportWebApiDto = new ExportWebApiDto();
        exportWebApiDto.setWebApiUrl((String) url);
        String requestMappingType = (String) requestMapping;
        //方便业务流转 将请求方式类型转换为大写
        exportWebApiDto.setRequestMapping(requestMappingType.toUpperCase());
        return exportWebApiDto;
    }

    @Override
    public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
        this.validateQueryPageSize();
        // vo.getPageNo() 起始为0
        int startPageNo = (vo.getPageNo()) * (this.getPageSize() / this.getQueryPageSize());
/*        if(startPageNo == 0){
            startPageNo = 1;
        }*/
        int endPageNo = (vo.getPageNo() + 1) * (this.getPageSize() / this.getQueryPageSize()) - 1;
        JSONArray totalRecords = new JSONArray();

        for (int pageNo = startPageNo; pageNo <= endPageNo; pageNo++) {
            log.info("webApiExport---Exec pageNo {}",pageNo);
            Pageable pageable = PageRequest.of(pageNo, this.getQueryPageSize());
            params.put(WebApiParamsTools.CODE_PARAMETER_WEB_URL, vo.getWebApiUrl());
            final ExportWebApiDto webApiDto = this.getWebApiParam(params);
            String requestParamJson = this.getRequestParamFilterWebUrl(params);
            final int pageNum = pageable.getPageNumber() + webApiDto.getStartPage();
            IPage<?> page = null;
            try{
                page = this.springControllerApiService.getPage(
                        webApiDto.getWebApiUrl(),
                        webApiDto.getRequestMapping(),
                        requestParamJson,
                        pageNum,
                        pageable.getPageSize());
            }catch (Exception e){
                e.printStackTrace();
            }
            if(Objects.isNull(page)){
                log.info("webApiExport-----  requestParamJson {} webApiDto {} pageNum {} pageSize {} startPageNo {} endPage {}", JSONObject.toJSONString(requestParamJson),JSONObject.toJSONString(webApiDto),pageNum,pageable.getPageSize(),startPageNo,endPageNo);
                page.setRecords(new ArrayList<>());
            }
            params.remove(WebApiParamsTools.CODE_PARAMETER_WEB_URL);
            if (CollectionUtils.isEmpty(page.getRecords())) {
                break;
            }
            final List<?> records = page.getRecords();
            //调整参数
            this.adjustData(records);
            totalRecords.addAll(toJSONArray(records));
        }
        return totalRecords;
    }

    /**
     * 调整数据
     *
     * @param records
     */
    private void adjustData(List<?> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
    }

    /**
     * 校验查询数据分页大小的合法性
     */
    private void validateQueryPageSize() {
        Validate.isTrue(this.getPageSize() % this.getQueryPageSize() == 0, "导出子任务拆分大小必须是查询数据分页大小的整数倍!");
    }
}
