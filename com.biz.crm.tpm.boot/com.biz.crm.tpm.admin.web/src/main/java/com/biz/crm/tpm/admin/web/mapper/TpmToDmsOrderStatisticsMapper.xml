<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.admin.web.mapper.TpmToDmsOrderStatisticsMapper">


    <select id="findDataListByYears" resultType="com.biz.crm.dms.business.order.sdk.dto.TpmToDmsOrderStatisticDto">
        SELECT a.customer_code,
               a.customer_name,
               a.year_month_ly             years,
               a.item_code                 productPhaseCode,
               a.item_name                 productPhaseName,
               a.department_one_code       orgCode,
               a.department_one_name       orgName,
               a.product_code,
               a.product_name,
               a.company_code,
               a.cost_center_code,
               a.cost_center_name,
               ifnull(b.estimated_cost, 0) planReachAmount,
               a.income_amount budgetReachAmount
        FROM tpm_cost_budget_income a
                 LEFT JOIN (SELECT b.customer_code,
                                   sum(b.estimated_cost) estimated_cost,
                                   b.cost_center_code,
                                   b.item_code
                            FROM tpm_marketing_plan a
                                     LEFT JOIN tpm_marketing_sales_plan b ON a.scheme_code = b.scheme_code
                            WHERE a.process_status = '3'
                              AND a.scheme_type != 'change'
                            GROUP BY
                                b.customer_code,
                                b.cost_center_code,
                                b.item_code) b ON a.customer_code = b.customer_code
            AND a.cost_center_code = b.cost_center_code
            AND a.item_code = b.item_code
        WHERE a.customer_code IS NOT NULL
          AND a.item_code IS NOT NULL
          AND a.department_one_code IS NOT NULL
          and a.product_code is not null
          AND a.tenant_code = #{tenantCode}
          AND a.del_flag = '009'
          AND a.enable_status = '009'
          AND a.year_month_ly = #{years}
          AND a.confirm_status = 'confirmed'
    </select>


    <select id="findByCondition" resultType="com.biz.crm.dms.business.order.sdk.dto.TpmToDmsOrderStatisticDto">
        <choose>
            <when test="dto.statisticsDimension == 'customer'">
                SELECT
                a.customer_code,
                a.customer_name,
                a.year_month_ly years,
                a.department_one_code orgCode,
                a.department_one_name orgName,
                SUM(IFNULL(a.income_amount,0)) budgetReachAmount
                FROM tpm_cost_budget_income a
                <where>
                    a.customer_code IS NOT NULL
                    AND a.department_one_code IS NOT NULL
                    <if test="dto.customerCode != null and dto.customerCode != ''">
                        and a.customer_code = #{dto.customerCode}
                    </if>
                    <if test="dto.customerName != null and dto.customerName != ''">
                        <bind name="customerName" value="'%' + dto.customerName + '%'"/>
                        and a.customer_name like #{customerName}
                    </if>
                    <if test="dto.orgCode != null and dto.orgCode != ''">
                        and a.department_one_code = #{dto.orgCode}
                    </if>
                    <if test="dto.orgName != null and dto.orgName != ''">
                        <bind name="orgName" value="'%' + dto.orgName + '%'"/>
                        and a.department_one_name like #{orgName}
                    </if>
                    <if test="orgCodes != null and orgCodes.size()>0">
                        and a.department_one_code in
                        <foreach collection="orgCodes" open="(" close=")" index="index" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    AND a.tenant_code = #{tenantCode}
                    AND a.del_flag = '009'
                    AND a.enable_status = '009'
                    AND a.year_month_ly = #{dto.years}
                    AND a.confirm_status = 'confirmed'
                    GROUP BY
                    a.customer_code,
                    a.customer_name,
                    a.year_month_ly ,
                    a.department_one_code ,
                    a.department_one_name
                </where>
            </when>
            <otherwise>
                SELECT
                a.customer_code,
                a.customer_name,
                a.year_month_ly years,
                a.item_code productPhaseCode,
                a.item_name productPhaseName,
                a.product_code,
                a.product_name,
                SUM(IFNULL(a.income_amount,0)) budgetReachAmount
                FROM tpm_cost_budget_income a
                <where>
                    a.customer_code IS NOT NULL
                    AND a.item_code IS NOT NULL
                    and a.product_code is not null
                    <if test="dto.customerCode != null and dto.customerCode != ''">
                        and a.customer_code = #{dto.customerCode}
                    </if>
                    <if test="dto.customerName != null and dto.customerName != ''">
                        <bind name="customerName" value="'%' + dto.customerName + '%'"/>
                        and a.customer_name like #{customerName}
                    </if>
                    <if test="dto.orgCode != null and dto.orgCode != ''">
                        and a.department_one_code = #{dto.orgCode}
                    </if>
                    <if test="dto.orgName != null and dto.orgName != ''">
                        <bind name="orgName" value="'%' + dto.orgName + '%'"/>
                        and a.department_one_name like #{orgName}
                    </if>
                    <if test="orgCodes != null and orgCodes.size()>0">
                        and a.department_one_code in
                        <foreach collection="orgCodes" open="(" close=")" index="index" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="dto.productCode != null and dto.productCode != ''">
                        and a.product_code = #{dto.productCode}
                    </if>
                    <if test="dto.productName != null and dto.productName != ''">
                        <bind name="productName" value="'%' + dto.productName + '%'"/>
                        and a.product_name like #{productName}
                    </if>
                    <if test="dto.productPhaseCode != null and dto.productPhaseCode != ''">
                        and a.item_code = #{dto.productPhaseCode}
                    </if>
                    <if test="dto.productPhaseName != null and dto.productPhaseName != ''">
                        <bind name="productPhaseName" value="'%' + dto.productPhaseName + '%'"/>
                        and a.item_name like #{productPhaseName}
                    </if>
                    AND a.tenant_code = #{tenantCode}
                    AND a.del_flag = '009'
                    AND a.enable_status = '009'
                    AND a.year_month_ly = #{dto.years}
                    AND a.confirm_status = 'confirmed'
                    GROUP BY
                    a.customer_code,
                    a.customer_name,
                    a.year_month_ly ,
                    a.item_code ,
                    a.item_name ,
                    a.product_code,
                    a.product_name
                </where>
            </otherwise>
        </choose>
    </select>

    <select id="findSalesPlanByConditions" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo">
        SELECT b.customer_code,
        b.estimated_cost,
        b.cost_center_code,
        b.item_code,
        b.product_code
        FROM tpm_marketing_plan a
        LEFT JOIN tpm_marketing_sales_plan b ON a.scheme_code = b.scheme_code
        <where>
            a.process_status = '3'
            AND a.scheme_type != 'change'
            <if test="costCenterCodes != null and costCenterCodes.size()>0">
                and b.cost_center_code in
                <foreach collection="costCenterCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            and b.customer_code in
            <foreach collection="customerCodes" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
            <if test="productCodes != null and productCodes.size()>0">
                and b.product_code in
            <foreach collection="productCodes" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
            </if>
            and b.years = #{years}
        </where>
    </select>

</mapper>

