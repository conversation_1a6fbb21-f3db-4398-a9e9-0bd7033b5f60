package com.biz.crm.tpm.admin.web.login.validate;


import com.biz.crm.mdm.business.login.log.sdk.service.LoginLogVoService;
import com.biz.crm.mdm.business.login.log.sdk.vo.LoginLogVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.bizunited.nebula.security.sdk.transform.IdentityTransformValidate;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @describe: JWT校验 用户信息变更
 * @createTime 2023年04月19日 10:12:00
 */
@Component
public class JwtIdentityTransformValidate implements IdentityTransformValidate {

  @Autowired
  private UserVoService userVoService;

  @Autowired
  private LoginLogVoService loginLogVoService;

  @Override
  public boolean matched(UserIdentity userIdentity) {
    // u-企业用户，c-客户用户，terminal-终端用户，customer_employee-经销商员工用户</br>
    return StringUtils.equals("u", userIdentity.getIdentityType());
  }

  @Override
  public void validate(UserIdentity userIdentity) {
    /**
     * 通过最后一次登录时间 和 最后一次更新时间 进行判断
     */
//    String account = userIdentity.getAccount();
//    UserVo userDb = userVoService.findByUserName(account);
//    //校验空用户
//    if (ObjectUtils.isEmpty(userDb)) {
//      return;
//    }
//    Date modifyTime = userDb.getModifyTime();
//    if (modifyTime == null) {
//      //维护旧数据
//      return;
//    }
//    LoginLogVo loginLog = loginLogVoService.findByAccount(account);
//    Date loginTime = loginLog.getLoginTime();
//    Validate.isTrue(modifyTime.getTime() < loginTime.getTime(), "验证出错，用户信息变更");
  }
}
