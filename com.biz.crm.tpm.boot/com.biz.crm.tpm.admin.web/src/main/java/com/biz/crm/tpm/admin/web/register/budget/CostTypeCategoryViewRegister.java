package com.biz.crm.tpm.admin.web.register.budget;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>TPM-预算中心-活动大类数据视图注册器
 * 基于nebula的数据视图提供活动大类列表查询功能
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Component
public class CostTypeCategoryViewRegister implements DataviewRegister {

  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-活动大类";
  }

  @Override
  public String buildSql() {
    return "select distinct a.* " +
            "from tpm_cost_type_category a left join tpm_cost_type_category_range b on a.tenant_code = b.tenant_code and a.category_code = b.category_code " +
            "where a.tenant_code = :tenantCode and a.del_flag = '009' " ;
  }
}
