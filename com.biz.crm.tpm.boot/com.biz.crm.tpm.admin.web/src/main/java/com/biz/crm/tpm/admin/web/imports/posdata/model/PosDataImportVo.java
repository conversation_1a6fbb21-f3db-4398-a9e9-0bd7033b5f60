package com.biz.crm.tpm.admin.web.imports.posdata.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Data
@ApiModel("POS导入vo")
@CrmExcelImport(startRow = 2)
public class PosDataImportVo extends CrmExcelVo {

    @CrmExcelColumn("POS年月")
    private String years;

    @CrmExcelColumn("组织编码")
    private String orgCode;

    @CrmExcelColumn("组织名称")
    private String orgName;

    @CrmExcelColumn("公司代码")
    private String companyCode;

    @CrmExcelColumn("客户ERP编码")
    private String erpCode;
    private String customerCode;

    @CrmExcelColumn("客户名称")
    private String customerName;

    @CrmExcelColumn("销售数量")
    private String quantityStr;

    @CrmExcelColumn("销售金额")
    private String amountStr;
}
