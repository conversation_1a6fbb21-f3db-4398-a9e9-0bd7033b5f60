package com.biz.crm.tpm.admin.web.exports.approvalCollect.mapper;

import com.biz.crm.tpm.admin.web.exports.approvalCollect.model.ApprovalCollectExportsDto;
import com.biz.crm.tpm.admin.web.exports.approvalCollect.model.ApprovalCollectExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @describe: 核销采集信息导出mapper
 * @createTime 2022年06月08日 16:46:00
 */
public interface ApprovalCollectExportsMapper {
  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") ApprovalCollectExportsDto dto);

  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<ApprovalCollectExportsVo> findData(@Param("dto") ApprovalCollectExportsDto dto);
}
