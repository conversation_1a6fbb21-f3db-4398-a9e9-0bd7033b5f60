package com.biz.crm.tpm.admin.web.register.budget;

import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetItemVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import com.bizunited.nebula.europa.database.sdk.strategy.ExternalQueryInterceptor;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteContent;
import com.bizunited.nebula.europa.sdk.context.matedata.MetaData;
import com.bizunited.nebula.europa.sdk.vo.EuropaInfoVo;

import java.util.Collections;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 描述：</br>TPM-预算科目-费用预算 余额等扩展字段的扩充
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Component
public class CostBudgetQueryInterceptor implements ExternalQueryInterceptor {
  @Autowired
  private CostBudgetItemVoService costBudgetItemVoService;

  @Override
  public String code() {
    return "tpm_costBudget_queryInterceptor";
  }

  @Override
  public String name() {
    return "TPM-预算科目-费用预算-参数补充";
  }

  @Override
  public List<Object[]> process(EntityManager entityManager, MetaData metaData, EuropaInfoVo europaInfoVo, ExecuteContent executeContent, String... strings) {
    List<Map<String, Object>> resutls = executeContent.getResults();
    if (CollectionUtils.isEmpty(resutls)) {
      return null;
    }
    if (0 == resutls.parallelStream().filter(row -> row != null).count()) {
      return Lists.newLinkedList();
    }
    List<Object[]> contents = Lists.newArrayList();
    resutls.forEach(item -> {
      List<Object> data = Lists.newArrayList();
      for (String fieldName : strings) {
        String code = (String) item.get("code");
        if (StringUtils.equals("finalBalance", fieldName)) {
          List<CostBudgetItemVo> costBudgetItemVos = this.costBudgetItemVoService.findByCostBudgetCode(code);
          CostBudgetItemVo costBudgetItemVo = costBudgetItemVos.stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
          if (costBudgetItemVo != null) {
            data.add(costBudgetItemVo.getFinalBalance());
          } else {
            data.add(item.get(fieldName));
          }
        } else {
          data.add(item.get(fieldName));
        }
      }
      contents.add(data.toArray(new Object[]{}));
    });
    return contents;
  }
}
