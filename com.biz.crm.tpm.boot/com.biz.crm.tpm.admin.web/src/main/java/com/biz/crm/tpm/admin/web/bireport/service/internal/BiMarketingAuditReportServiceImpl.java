package com.biz.crm.tpm.admin.web.bireport.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.admin.web.bireport.entity.BiMarketingAuditReportEntity;
import com.biz.crm.tpm.admin.web.bireport.mapper.BiMarketingAuditReportMapper;
import com.biz.crm.tpm.admin.web.bireport.service.BiMarketingAuditReportService;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.vo.MarketingAuditDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class BiMarketingAuditReportServiceImpl extends ServiceImpl<BiMarketingAuditReportMapper, BiMarketingAuditReportEntity> implements BiMarketingAuditReportService {

    @Autowired(required = false)
    MarketingAuditService marketingAuditService;

    @Autowired
    LoginUserService loginUserService;

    @Value("${spring.profiles.active:}")
    private String env;

    @Async("tpmMarketingCheckCaseThread")
    @Override
    public void calMarketingAuditReportReport(List<String> yearList, String initFlag) {
        if ("N".equals(initFlag) && (CollectionUtils.isEmpty(yearList))) {
            log.error("BI结案明细报表落库非首次初始化，未传递年月");
            return;
        }
        loginUserService.refreshAuthentication(null);
        StopWatch st = new StopWatch();
        st.start("BI结案明细报表落库任务");
        if ("Y".equals(initFlag)) {
            writeMarketingAuditRepor(null);
        } else {
            for (String years : yearList) {
                writeMarketingAuditRepor(years);
            }
        }
        st.stop();
        log.info("BI结案明细报表落库执行耗时  totalTimeSeconds {} ", st.getTotalTimeSeconds());

    }

    private void writeMarketingAuditRepor(String years) {
        List<String> filterList = new ArrayList<>();
        filterList.add("新零售运营中心");
        filterList.add("新零售中心");
        filterList.add("新零售运营支持部");
        filterList.add("新零售市场推广部");
        log.info("BI结案明细报表落库执行 years  {}",years);
        MarketingAuditDetailDto vo = new MarketingAuditDetailDto();
        vo.setYears(years);
        int searchIndex = 1;
        int total = 0;
        long originTotal = 0;
        while (true) {
            if("dev".equals(env)){
                log.info("BI结案明细报表落库执行1 searchIndex {} ",searchIndex);
            }
            Page<MarketingAuditDetailVo> page = marketingAuditService.findDataViewByConditions(PageRequest.of(searchIndex++, 1000), vo);
            List<MarketingAuditDetailVo> records = page.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                log.info("BI结案明细报表落库查询总数 searchIndex : {}  recordsTotalSize : {} originTotal : {}  ", searchIndex, total, originTotal);
                break;
            }
            if (originTotal == 0) {
                originTotal = page.getTotal();
            }
            adjustData(records);
            total += records.size();
            List<BiMarketingAuditReportEntity> list = JSONObject.parseArray(JSONObject.toJSONString(records), BiMarketingAuditReportEntity.class);
            list = list.stream().filter(item->  filterList.contains(item.getBelongDepartmentName())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(list)){
                continue;
            }
            for (BiMarketingAuditReportEntity model : list) {
                model.setId(null);
            }
            saveBatch(list);
            list = null;
            page = null;
        }
    }

    @Override
    public void removeAll(List<String> years) {

        this.getBaseMapper().delete(new LambdaQueryWrapper<BiMarketingAuditReportEntity>()
                .in(!CollectionUtils.isEmpty(years), BiMarketingAuditReportEntity::getYears, years));
    }

    private void adjustData(List<MarketingAuditDetailVo> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
    }


}
