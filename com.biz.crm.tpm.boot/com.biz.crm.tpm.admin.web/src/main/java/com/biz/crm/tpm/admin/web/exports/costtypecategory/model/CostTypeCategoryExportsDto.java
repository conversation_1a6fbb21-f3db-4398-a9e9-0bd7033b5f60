package com.biz.crm.tpm.admin.web.exports.costtypecategory.model;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月08日 11:07:00
 */
@Data
public class CostTypeCategoryExportsDto extends TenantDto {

  /**
   * 活动大类编号
   */
  private String categoryCode;

  /**
   * 活动大类名称
   */
  private String categoryName;

  /**
   * 财务费用归类(数据字典)
   */
  private String financialExpensesType;

  /**
   * 业务费用归类(数据字典)
   */
  private String businessExpensesType;

  /**
   * 预算科目名称
   */
  private String budgetSubjectsName;

  /**
   * 预算科目编号
   */
  private String budgetSubjectsCode;

  /**
   * 备注
   */
  private String remark;

  /**
   * 数据业务状态（启用状态）
   */
  private String enableStatus;

  /**
   * 删除状态
   */
  private String delFlag;

  /**
   * 租户
   */
  private String tenantCode;


  /**
   * 偏移量
   */
  private Integer offset;

  /**
   * limit
   */
  private Integer limit;
}
