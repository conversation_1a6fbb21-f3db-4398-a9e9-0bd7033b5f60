package com.biz.crm.tpm.admin.web.exports.costtypecategory.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月08日 11:07:00
 */
@CrmExcelExport
@Data
public class CostTypeCategoryExportsVo extends CrmExcelVo {

  /**
   * 活动大类编号
   */
  @ApiModelProperty(notes = "活动大类编号", value = "活动大类编号")
  @CrmExcelColumn("活动大类编号")
  private String categoryCode;
  /**
   * 活动大类名称
   */
  @ApiModelProperty(notes = "活动大类名称", value = "活动大类名称")
  @CrmExcelColumn("活动大类名称")
  private String categoryName;

  /**
   * 财务费用归类(数据字典)
   */
  @ApiModelProperty(notes = "财务费用归类(数据字典)", value = "财务费用归类(数据字典)")
  @CrmExcelColumn("财务费用归类")
  private String financialExpensesType;

  /**
   * 业务费用归类(数据字典)
   */
  @ApiModelProperty(notes = "业务费用归类(数据字典)", value = "业务费用归类(数据字典)")
  @CrmExcelColumn("业务费用归类")
  private String businessExpensesType;

  /**
   * 预算科目名称
   */
  @ApiModelProperty(notes = "预算科目名称", value = "预算科目名称")
  @CrmExcelColumn("关联预算科目名称")
  private String budgetSubjectsName;
  /**
   * 预算科目编号
   */
  @ApiModelProperty(notes = "预算科目编号", value = "预算科目编号")
  private String budgetSubjectsCode;

  /**
   * 备注
   */
  @ApiModelProperty(name = "remark", notes = "备注", value = "备注")
  @CrmExcelColumn("备注")
  private String remark;
  /**
   * 数据业务状态（启用状态）
   */
  @CrmExcelColumn("启用状态")
  private String enableStatus;
}
