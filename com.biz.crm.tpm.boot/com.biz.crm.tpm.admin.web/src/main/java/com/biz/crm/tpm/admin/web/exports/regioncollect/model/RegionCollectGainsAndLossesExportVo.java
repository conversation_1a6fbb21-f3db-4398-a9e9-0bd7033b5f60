package com.biz.crm.tpm.admin.web.exports.regioncollect.model;

import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:24
 */
@Data
@ApiModel(value = "RegionCollectGainsAndLossesVo", description = "大区汇总-客户损益预测")
public class RegionCollectGainsAndLossesExportVo extends CustomerGainsAndLossesExportBaseVo {


    @ApiModelProperty("客户名称")
    private String customerName;
}
