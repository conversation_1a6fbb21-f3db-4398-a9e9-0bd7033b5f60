package com.biz.crm.tpm.admin.web.controller.scheme;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.service.internal.SchemeExportServiceImpl;
import com.bizunited.nebula.venus.sdk.service.file.FileHandleService;
import com.bizunited.nebula.venus.sdk.vo.OrdinaryFileVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/26 18:04
 */
@RestController
@RequestMapping("/v1/schemeExportController")
public class SchemeExportController {

    @Resource
    private FileHandleService fileHandleService;


    @Resource
    private SchemeExportServiceImpl schemeExportService;

    @ApiOperation(value = "导出大区汇总数据")
    @GetMapping("exportRegionCollect")
    public Result<OrdinaryFileVo> exportRegionCollect(@RequestParam String collectCode) {
        String fileCode = schemeExportService.exportRegionCollect(collectCode);
        OrdinaryFileVo fileVo = fileHandleService.findById(fileCode);
        return Result.ok(fileVo);
    }

    @ApiOperation(value = "方案明细导出")
    @GetMapping("exportsDetail")
    public Result<OrdinaryFileVo> exportsDetail(@RequestParam(required = false) String schemeCode, @RequestParam String cacheKey) {
        String fileCode = schemeExportService.exportsDetail(schemeCode, cacheKey);
        OrdinaryFileVo fileVo = fileHandleService.findById(fileCode);
        return Result.ok(fileVo);
    }

}
