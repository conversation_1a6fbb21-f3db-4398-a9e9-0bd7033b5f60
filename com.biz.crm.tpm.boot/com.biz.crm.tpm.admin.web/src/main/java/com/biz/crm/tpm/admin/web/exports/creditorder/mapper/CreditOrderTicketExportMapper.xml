<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.creditorder.mapper.CreditOrderTicketExportMapper">

    <select id="getExportTotal" resultType="java.lang.Integer">
        select count(0) from tpm_credit_order t
        right join  tpm_credit_order_ticket t2
        on t.credit_code=t2.credit_code
        <where>
            <if test="dto.creditCode != null and dto.creditCode != '' ">
                and t.credit_code = #{dto.creditCode}
            </if>
            <if test="dto.cashCode != null and dto.cashCode != '' ">
                and t.cash_code = #{dto.cashCode}
            </if>
            <if test="dto.orderType != null and dto.orderType != '' ">
                and t.order_type = #{dto.orderType}
            </if>
            <if test="dto.btNo != null and dto.btNo != '' ">
                and t.bt_no = #{dto.btNo}
            </if>
            <if test="dto.companyCode != null and dto.companyCode != '' ">
                and t.company_code = #{dto.companyCode}
            </if>
            <if test="dto.departmentOneCode != null and dto.departmentOneCode != '' ">
                and t.department_one_code = #{dto.departmentOneCode}
            </if>
            <if test="dto.departmentOneName != null and dto.departmentOneName != '' ">
                <bind name="departmentOneName" value=" '%' + dto.departmentOneName + '%' "/>
                and t.department_one_name like #{departmentOneName}
            </if>
            <if test="dto.departmentTwoCode != null and dto.departmentTwoCode != '' ">
                and t.department_two_code = #{dto.departmentTwoCode}
            </if>
            <if test="dto.departmentTwoName != null and dto.departmentTwoName != '' ">
                <bind name="departmentTwoName" value=" '%' + dto.departmentTwoName + '%' "/>
                and t.department_two_name like #{departmentTwoName}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != '' ">
                and t.customer_code = #{dto.customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != '' ">
                <bind name="customerName" value=" '%' + dto.customerName + '%' "/>
                and t.customer_name like #{customerName}
            </if>
            <if test="dto.auditCode != null and dto.auditCode != '' ">
                and t.audit_code = #{dto.auditCode}
            </if>
            <if test="dto.auditName != null and dto.auditName != '' ">
                <bind name="auditName" value=" '%' + dto.auditName + '%' "/>
                and t.audit_name like #{auditName}
            </if>
            <if test="dto.pushStatus != null and dto.pushStatus != '' ">
                and t.push_status = #{dto.pushStatus}
            </if>
            <if test="dto.accountDate != null and dto.accountDate != '' ">
                and t.account_date = #{dto.accountDate}
            </if>
            <if test="dto.accountDateStart != null and dto.accountDateStart != '' ">
                and t.account_date &gt;= #{dto.accountDateStart}
            </if>
            <if test="dto.accountDateEnd != null and dto.accountDateEnd != '' ">
                and t.account_date &lt;= #{dto.accountDateEnd}
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
    </select>
    <select id="findData"
            resultType="com.biz.crm.tpm.admin.web.exports.creditorder.model.CreditOrderTicketExportsVo">
        select  t.credit_code as creditCode,t.cash_code as cashCode ,t.order_type as orderType,t.bt_no as btNo,
                t.company_code as companyCode,t.enable_status as enableStatus,t.channel_code as channelCode,t.department_one_code as departmentOnCode,
                t.department_one_name as departmentOneName,t.department_two_code as departmentTwoCode,t.department_two_name as departmentTwoName,
                t.customer_name as customerName,t.voucher_code as voucherCode,t.fail_msg as failMsg,t.customer_code as customerCode,t.push_status as pushStatus,
                t.account_date as accountDate,t.amount,t.audit_code as auditCode,
                t2.line_number as lineNumber,t2.product_code as productCode,t2.product_name as productName,t2.sale_unit as saleUnit,t2.quantity,
                t2.price,t2.amount as orderAmount,t2.years,t2.single_discounts_rate_str as singleDiscountsRateStr,
                t2.cumulative_discounts_rate_str as cumulativeDiscountsRateStr,t2.ticket_status as ticketStatus,t2.un_ticket_amount as unTicketAmount,t2.un_invoiced_amount as unInvoicedAmount
                from tpm_credit_order t
        right join  tpm_credit_order_ticket t2
        on t.credit_code=t2.credit_code
        <where>
            <if test="dto.creditCode != null and dto.creditCode != '' ">
                and t.credit_code = #{dto.creditCode}
            </if>
            <if test="dto.cashCode != null and dto.cashCode != '' ">
                and t.cash_code = #{dto.cashCode}
            </if>
            <if test="dto.orderType != null and dto.orderType != '' ">
                and t.order_type = #{dto.orderType}
            </if>
            <if test="dto.btNo != null and dto.btNo != '' ">
                and t.bt_no = #{dto.btNo}
            </if>
            <if test="dto.companyCode != null and dto.companyCode != '' ">
                and t.company_code = #{dto.companyCode}
            </if>
            <if test="dto.departmentOneCode != null and dto.departmentOneCode != '' ">
                and t.department_one_code = #{dto.departmentOneCode}
            </if>
            <if test="dto.departmentOneName != null and dto.departmentOneName != '' ">
                <bind name="departmentOneName" value=" '%' + dto.departmentOneName + '%' "/>
                and t.department_one_name like #{departmentOneName}
            </if>
            <if test="dto.departmentTwoCode != null and dto.departmentTwoCode != '' ">
                and t.department_two_code = #{dto.departmentTwoCode}
            </if>
            <if test="dto.departmentTwoName != null and dto.departmentTwoName != '' ">
                <bind name="departmentTwoName" value=" '%' + dto.departmentTwoName + '%' "/>
                and t.department_two_name like #{departmentTwoName}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != '' ">
                and t.customer_code = #{dto.customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != '' ">
                <bind name="customerName" value=" '%' + dto.customerName + '%' "/>
                and t.customer_name like #{customerName}
            </if>
            <if test="dto.auditCode != null and dto.auditCode != '' ">
                and t.audit_code = #{dto.auditCode}
            </if>
            <if test="dto.auditName != null and dto.auditName != '' ">
                <bind name="auditName" value=" '%' + dto.auditName + '%' "/>
                and t.audit_name like #{auditName}
            </if>
            <if test="dto.pushStatus != null and dto.pushStatus != '' ">
                and t.push_status = #{dto.pushStatus}
            </if>
            <if test="dto.accountDate != null and dto.accountDate != '' ">
                and t.account_date = #{dto.accountDate}
            </if>
            <if test="dto.accountDateStart != null and dto.accountDateStart != '' ">
                and t.account_date &gt;= #{dto.accountDateStart}
            </if>
            <if test="dto.accountDateEnd != null and dto.accountDateEnd != '' ">
                and t.account_date &lt;= #{dto.accountDateEnd}
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
        order by t.id desc
        limit #{dto.offset},#{dto.limit}
    </select>
</mapper>