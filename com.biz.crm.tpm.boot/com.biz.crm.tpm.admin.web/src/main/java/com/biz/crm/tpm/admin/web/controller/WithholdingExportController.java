package com.biz.crm.tpm.admin.web.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.service.internal.WithholdingCollectExportServiceImpl;
import com.biz.crm.tpm.business.pay.local.consumer.WithHoldingCollectOaConsumer;
import com.biz.crm.tpm.business.pay.sdk.dto.WithHoldingCollectDto;
import com.bizunited.nebula.venus.sdk.service.file.FileHandleService;
import com.bizunited.nebula.venus.sdk.vo.OrdinaryFileVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/26 17:44
 **/
@RestController
@RequestMapping("/v1/withholdingExportController")
public class WithholdingExportController {

    @Resource
    private FileHandleService fileHandleService;

    @Autowired
    private WithholdingCollectExportServiceImpl exportRegionCollect;

    @Resource
    private WithHoldingCollectOaConsumer withHoldingCollectOaConsumer;

    @ApiOperation(value = "导出计提汇总数据")
    @GetMapping("exportWithholdingCollect")
    public Result exportWithholdingCollect(@RequestParam String code, @RequestParam String cacheKey) {
        String fileCode = exportRegionCollect.exportWithholdingCollect(code, cacheKey);
        OrdinaryFileVo fileVo = fileHandleService.findById(fileCode);
        return Result.ok(fileVo);
    }

    @ApiOperation(value = "重发WithHoldingCollectOaConsumer消息")
    @PostMapping("reSend")
    public Result reSend(@RequestBody WithHoldingCollectDto dto) {
        withHoldingCollectOaConsumer.sendMsg(dto);
        return Result.ok();
    }
}
