<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.costtypecategory.mapper.CostTypeCategoryExportsMapper">


  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from tpm_cost_type_category t
    <where>
      t.del_flag = #{dto.delFlag}
      <if test="dto.enableStatus != null and dto.enableStatus != ''">
        and t.enable_status = #{dto.enableStatus}
      </if>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.categoryCode != null and dto.categoryCode != ''">
        <bind name="categoryCode" value="'%' + dto.categoryCode + '%'"/>
        and t.category_code like #{categoryCode}
      </if>
      <if test="dto.categoryName != null and dto.categoryName != ''">
        <bind name="categoryName" value="'%' + dto.categoryName + '%'"/>
        and t.category_name like #{categoryName}
      </if>
      <if test="dto.budgetSubjectsName != null and dto.budgetSubjectsName != ''">
        <bind name="budgetSubjectsName" value="'%' + dto.budgetSubjectsName + '%'"/>
        and t.budget_subjects_name like #{budgetSubjectsName}
      </if>
      <if test="dto.businessExpensesType != null and dto.businessExpensesType != ''">
        <bind name="businessExpensesType" value="'%' + dto.businessExpensesType + '%'"/>
        and t.business_expenses_type like #{businessExpensesType}
      </if>
      <if test="dto.financialExpensesType != null and dto.financialExpensesType != ''">
        <bind name="financialExpensesType" value="'%' + dto.financialExpensesType + '%'"/>
        and t.financial_expenses_type like #{financialExpensesType}
      </if>
    </where>
  </select>


  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.costtypecategory.model.CostTypeCategoryExportsVo">
    select t.*
    from tpm_cost_type_category t
    <where>
      t.del_flag = #{dto.delFlag}
      <if test="dto.enableStatus != null and dto.enableStatus != ''">
        and t.enable_status = #{dto.enableStatus}
      </if>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.categoryCode != null and dto.categoryCode != ''">
        <bind name="categoryCode" value="'%' + dto.categoryCode + '%'"/>
        and t.category_code like #{categoryCode}
      </if>
      <if test="dto.categoryName != null and dto.categoryName != ''">
        <bind name="categoryName" value="'%' + dto.categoryName + '%'"/>
        and t.category_name like #{categoryName}
      </if>
      <if test="dto.budgetSubjectsName != null and dto.budgetSubjectsName != ''">
        <bind name="budgetSubjectsName" value="'%' + dto.budgetSubjectsName + '%'"/>
        and t.budget_subjects_name like #{budgetSubjectsName}
      </if>
      <if test="dto.businessExpensesType != null and dto.businessExpensesType != ''">
        <bind name="businessExpensesType" value="'%' + dto.businessExpensesType + '%'"/>
        and t.business_expenses_type like #{businessExpensesType}
      </if>
      <if test="dto.financialExpensesType != null and dto.financialExpensesType != ''">
        <bind name="financialExpensesType" value="'%' + dto.financialExpensesType + '%'"/>
        and t.financial_expenses_type like #{financialExpensesType}
      </if>
    </where>
    order by t.create_time desc, t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>