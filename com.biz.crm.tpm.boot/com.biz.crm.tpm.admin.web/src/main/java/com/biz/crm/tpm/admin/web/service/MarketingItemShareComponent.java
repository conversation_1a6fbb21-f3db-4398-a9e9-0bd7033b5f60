package com.biz.crm.tpm.admin.web.service;

import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/14 22:45
 **/
@Component
public class MarketingItemShareComponent {


    @Resource
    private MarketingItemShareService marketingItemShareService;


    public void syncRunItemShare(List<String> yearsList) {
        MarketingItemShareComponent bean = ApplicationContextHolder.getContext().getBean(MarketingItemShareComponent.class);
        for (String years : yearsList) {
            bean.execute(years);
        }
    }


    private void execute(String years) {
        marketingItemShareService.updateItemShare(years);
    }

}
