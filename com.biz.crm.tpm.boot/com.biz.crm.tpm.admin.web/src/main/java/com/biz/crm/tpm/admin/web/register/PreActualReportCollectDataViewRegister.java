package com.biz.crm.tpm.admin.web.register;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/21 21:59
 **/
@Component
public class PreActualReportCollectDataViewRegister implements DataviewRegister {
    @Override
    public String code() {
        return "tpm_pre_actual_report_collect";
    }

    @Override
    public String desc() {
        return "预实汇总报表视图";
    }

    @Override
    public String buildSql() {
        return "SELECT	"+
                "	id,	"+
                "	create_account,	"+
                "	create_name,	"+
                "	create_time,	"+
                "	modify_account,	"+
                "	modify_name,	"+
                "	modify_time,	"+
                "	del_flag,	"+
                "	enable_status,	"+
                "	remark,	"+
                "	tenant_code,	"+
                "	act_years,	"+
                "	apply_account,	"+
                "	apply_name,	"+
                "	center_code,	"+
                "	center_name,	"+
                "	cost_ratio_difference,	"+
                "	cost_ratio_difference_str,	"+
                "	customer_code,	"+
                "	customer_name,	"+
                "	gross_profit_margin_difference,	"+
                "	gross_profit_margin_difference_str,	"+
                "	income_achieve_ratio,	"+
                "	income_achieve_ratio_str,	"+
                "	org_code,	"+
                "	org_name,	"+
                "	plan_cost_ratio,	"+
                "	plan_cost_ratio_str,	"+
                "	plan_gross_profit_margin,	"+
                "	plan_gross_profit_margin_str,	"+
                "	plan_income,	"+
                "	plan_profit_ratio,	"+
                "	plan_profit_ratio_str,	"+
                "	process_status,	"+
                "	profit_ratio_difference,	"+
                "	profit_ratio_difference_str,	"+
                "	region_code,	"+
                "	region_name,	"+
                "	scheme_code,	"+
                "	withholding_cost_ratio,	"+
                "	withholding_cost_ratio_str,	"+
                "	withholding_gross_profit_margin,	"+
                "	withholding_gross_profit_margin_str,	"+
                "	withholding_income,	"+
                "	withholding_profit_ratio,	"+
                "	withholding_profit_ratio_str,	"+
                "	withholding_status 	"+
                "FROM	"+
                "	tpm_pre_actual_report_collect 	"+
                "WHERE	"+
                "	tenant_code = :tenantCode	";
    }
}
