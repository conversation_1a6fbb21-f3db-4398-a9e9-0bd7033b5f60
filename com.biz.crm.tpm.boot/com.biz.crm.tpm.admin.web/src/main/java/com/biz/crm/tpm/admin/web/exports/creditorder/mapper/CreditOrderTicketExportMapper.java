package com.biz.crm.tpm.admin.web.exports.creditorder.mapper;

import com.biz.crm.tpm.admin.web.exports.creditorder.model.CreditOrderTicketExportsDto;
import com.biz.crm.tpm.admin.web.exports.creditorder.model.CreditOrderTicketExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【贷项订单台账】导出明细
 *
 * <AUTHOR>
 */
public interface CreditOrderTicketExportMapper {


    /**
     * 获取匹配的数据总量
     *
     * @param dto
     * @return
     */
    Integer getExportTotal(@Param("dto") CreditOrderTicketExportsDto dto);

    /**
     * 获取导出数据
     *
     * @param dto
     * @return
     */
    List<CreditOrderTicketExportsVo> findData(@Param("dto") CreditOrderTicketExportsDto dto);
}
