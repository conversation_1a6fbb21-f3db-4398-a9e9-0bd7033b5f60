package com.biz.crm.tpm.admin.web.preactualdetailreport.service.strategy;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 21:04
 **/
public abstract class PreActualDetailReportBuild<DetailReportVo,CaseVoList> {

    protected DetailReportVo detailReportVo;

    protected CaseVoList caseVoList;

    public PreActualDetailReportBuild<DetailReportVo,CaseVoList> init(DetailReportVo detailReportVo,CaseVoList caseVoList) {
        this.detailReportVo = detailReportVo;
        this.caseVoList = caseVoList;
        return this;
    }

    public abstract PreActualDetailReportBuild loadDataBase();

    public abstract PreActualDetailReportBuild income();

    public abstract PreActualDetailReportBuild cost();

    public abstract PreActualDetailReportBuild grossProfit();

    public abstract PreActualDetailReportBuild logisticsCosts();

    public abstract PreActualDetailReportBuild giftCost();

    public abstract PreActualDetailReportBuild budgeSubject();

    public abstract PreActualDetailReportBuild surroundingMaterials();

    public abstract PreActualDetailReportBuild share();

    public abstract PreActualDetailReportBuild profit();
}
