package com.biz.crm.tpm.admin.web.preactualdetailreport.service.strategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.dms.business.order.common.sdk.enums.ItemTypeEnum;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialCostVoService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.admin.web.preactualdetailreport.eunm.PreActualProjectEnum;
import com.biz.crm.tpm.admin.web.vo.PreActualDetailReportVo;
import com.biz.crm.tpm.business.activities.marketingplan.constant.MarketingPlanConstant;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanMaterialTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingTransportEnum;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.stagingscheme.entity.TpmStagingSchemeEntity;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.local.mapper.FeeCashDetailMapper;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.MarketingAuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.service.PosDataService;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingWriteOffService;
import com.biz.crm.tpm.business.pay.sdk.vo.*;
import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.FormulaGetValueComponent;
import com.bizunited.nebula.common.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 21:09
 **/
public class PreActualDetailReportAbstractBuilder extends PreActualDetailReportBuild<PreActualDetailReportVo, List<MarketingPlanCaseVo>> {

    private final static String COLUMN = ":";
    private static final Logger log = LoggerFactory.getLogger(PreActualDetailReportAbstractBuilder.class);

    private OrgVoService orgVoService;

    private WithHoldingService withHoldingService;

    private FeeCashService feeCashService;

    private CostBudgetIncomeService costBudgetIncomeService;

    private FormulaGetValueComponent formulaGetValueComponent;

    private ITpmStagingSchemeService tpmStagingSchemeService;

    private MaterialVoService materialVoService;

    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;

    private ProductVoService productVoService;

    private MaterialCostVoService materialCostVoService;

    private MdmCostCenterVoService costCenterVoService;

    private CostTypeDetailVoService costTypeDetailVoService;

    private CostBudgetVoService costBudgetVoService;

    private MarketingAuditService marketingAuditService;

    private WithHoldingWriteOffService withHoldingWriteOffService;

    private PublicShareRatioService publicShareRatioService;

    private PosDataService posDataService;

    private DictDataVoService dictDataVoService;

    private List<PreActualDetailReportVo> actualDetailReportVoList = Lists.newArrayList();

    private List<MarketingSalesPlanVo> salesPlanList = Lists.newArrayList();

    private List<MarketingSalesPlanVo> changeSalesPlanList = Lists.newArrayList();

    private List<CostBudgetIncomeVo> budgetIncomeVoList = Lists.newArrayList();

    private PreActualDetailReportVo income;

    private PreActualDetailReportVo cost;

    private PreActualDetailReportVo grossProfit;

    private List<MarketingPlanCaseVo> originalCaseVoList = Lists.newArrayList();

    private Map<String, BigDecimal> materialMap = Maps.newHashMap();

    private List<DmsWarehouseOrderDetailVo> orderDetailVos = Lists.newArrayList();

    private Map<String, BigDecimal> productConversionMap = Maps.newHashMap();

    private Map<String, MdmCostCenterVo> costCenterMap = Maps.newHashMap();

    private Map<String, BigDecimal> materialTaxRateMap = Maps.newHashMap();

    private Map<String, BigDecimal> orderMaterialTaxRateMap = Maps.newHashMap();

    private Map<String, MaterialCostVo> materialCostMap = Maps.newHashMap();

    private Map<String, MaterialVo> materialCost2Map = Maps.newHashMap();

    private Map<String, String> costTypeDetailMap = Maps.newHashMap();

    private List<PreActualDetailReportVo> calActualDetailReportVoList = Lists.newArrayList();

    private List<MaterialVo> materialVoList = new ArrayList<>();

    List<DictDataVo> companyAndFactoryList = new ArrayList<>();

    private   String[] activeProfiles;

    private FeeCashDetailMapper feeCashDetailMapper;


    private final static List<String> productItemTypeList = Arrays.asList(ItemTypeEnum.NORMAL_GOODS.getDictCode(),
            ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
            ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
            ItemTypeEnum.COMPENSATED_GOODS.getDictCode(),
            ItemTypeEnum.TAIL_GOODS.getDictCode());

    private final static List<String> peripheryItemTypeList = Arrays.asList(ItemTypeEnum.MATERIAL_GOODS.getDictCode());

    private final static List<String> giftItemTypeList = Arrays.asList(ItemTypeEnum.COMPLIMENTARY_GOODS.getDictCode(),
            ItemTypeEnum.COMPLIMENTARY_CONTRACT_GOODS.getDictCode(),
            ItemTypeEnum.TAIL_GOODS.getDictCode());

    public static PreActualDetailReportAbstractBuilder builder(ApplicationContext context, List<PreActualDetailReportVo> actualDetailReportVoList,
                                                               List<MarketingSalesPlanVo> salesPlanvoList, List<MarketingSalesPlanVo> changeSalesPlanVoList) {
        PreActualDetailReportAbstractBuilder builder = new PreActualDetailReportAbstractBuilder();
        builder.orgVoService = context.getBean(OrgVoService.class);
        builder.withHoldingService = context.getBean(WithHoldingService.class);
        builder.actualDetailReportVoList = actualDetailReportVoList;
        builder.costBudgetIncomeService = context.getBean(CostBudgetIncomeService.class);
        builder.salesPlanList = salesPlanvoList;
        builder.changeSalesPlanList = changeSalesPlanVoList;
        builder.formulaGetValueComponent = context.getBean(FormulaGetValueComponent.class);
        builder.tpmStagingSchemeService = context.getBean(ITpmStagingSchemeService.class);
        builder.materialVoService = context.getBean(MaterialVoService.class);
        builder.dmsWarehouseOrderDetailVoService = context.getBean(DmsWarehouseOrderDetailVoService.class);
        builder.productVoService = context.getBean(ProductVoService.class);
        builder.materialCostVoService = context.getBean(MaterialCostVoService.class);
        builder.costCenterVoService = context.getBean(MdmCostCenterVoService.class);
        builder.costTypeDetailVoService = context.getBean(CostTypeDetailVoService.class);
        builder.costBudgetVoService = context.getBean(CostBudgetVoService.class);
        builder.marketingAuditService = context.getBean(MarketingAuditService.class);
        builder.withHoldingWriteOffService = context.getBean(WithHoldingWriteOffService.class);
        builder.publicShareRatioService = context.getBean(PublicShareRatioService.class);
        builder.posDataService = context.getBean(PosDataService.class);
        builder.dictDataVoService = context.getBean(DictDataVoService.class);
        builder.feeCashService = context.getBean(FeeCashService.class);
        builder.activeProfiles = context.getEnvironment().getActiveProfiles();
        builder.feeCashDetailMapper = context.getBean(FeeCashDetailMapper.class);
        return builder;
    }


    @Override
    public PreActualDetailReportBuild loadDataBase() {
        //过滤变更前的方案明细
        List<MarketingPlanCaseVo> changeLastCaseVoList = caseVoList.stream().filter(x -> !x.getSchemeDetailCode().contains(MarketingPlanConstant.CHANGE_SCHEME_DETAIL_CODE_RULE))
                .collect(Collectors.toList());
        List<String> schemeDetailCaseList = changeLastCaseVoList.stream().map(x -> x.getSchemeDetailCode()).collect(Collectors.toList());
        //查询未变更之前的数据
        List<TpmStagingSchemeEntity> stagingSchemeEntityList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(schemeDetailCaseList)) {
            stagingSchemeEntityList = tpmStagingSchemeService.findListByCondition(OverallPlanSchemeTypeEnum.PLAN_CASE.getCode(), schemeDetailCaseList);
        }
        if (CollectionUtils.isEmpty(stagingSchemeEntityList)) {
            originalCaseVoList = changeLastCaseVoList;
        } else {
            Map<String, List<TpmStagingSchemeEntity>> stagingSchemeMap = stagingSchemeEntityList.stream().collect(Collectors.groupingBy(x -> x.getReleaseId()));
            List<String> notChangeSchemeDetailCodes = Lists.newArrayList();
            for (String s : schemeDetailCaseList) {
                if (!stagingSchemeMap.containsKey(s)) {
                    notChangeSchemeDetailCodes.add(s);
                }
            }
            for (Map.Entry<String, List<TpmStagingSchemeEntity>> entry : stagingSchemeMap.entrySet()) {
                Optional<TpmStagingSchemeEntity> optional = entry.getValue().stream().sorted(Comparator.comparing(TpmStagingSchemeEntity::getCreateTime)).findFirst();
                originalCaseVoList.add(JSONObject.parseObject(optional.get().getJsonStr(), MarketingPlanCaseVo.class));
            }
            if (!CollectionUtils.isEmpty(notChangeSchemeDetailCodes)) {
                for (MarketingPlanCaseVo caseVo : changeLastCaseVoList) {
                    if (notChangeSchemeDetailCodes.contains(caseVo.getSchemeDetailCode())) {
                        originalCaseVoList.add(caseVo);
                    }
                }
            }
        }
        //查询发货数据
        Set<String> itemCodeSet = caseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getItemCode())).map(x -> x.getItemCode()).collect(Collectors.toSet());
        Set<String> productCodeSet = caseVoList.stream().filter(x -> !CollectionUtils.isEmpty(x.getProductList())).flatMap(x -> x.getProductList().stream()).map(x -> x.getCode()).collect(Collectors.toSet());
        List<String> productCodes = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(productCodeSet)) {
            productCodes.addAll(productCodeSet);
        }
        if (!CollectionUtils.isEmpty(itemCodeSet)) {
            Map<String, ProductVo> productVoMap = formulaGetValueComponent.findProductListByItemCodes(itemCodeSet);
            productCodes.addAll(productVoMap.keySet());
        }
        String date = detailReportVo.getYears() + "-01";
        Map<String, String> dateMap = FormulaGetValueComponent.getDateMonthly(date, DateUtil.DEFAULT_YEAR_MONTH_DAY);
        TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
        dto.setCustomerCode(detailReportVo.getCustomerCode());
        dto.setProductCodes(productCodes);
        dto.setSearchStartTime(dateMap.get(FormulaGetValueComponent.START_DATE));
        dto.setSearchEndTime(dateMap.get(FormulaGetValueComponent.END_DATE));
        dto.setItemTypeList(Lists.newArrayList("normalGoods","complimentaryGoods"));
        List<DmsWarehouseOrderDetailVo> orderDetailVos = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
        if (!CollectionUtils.isEmpty(orderDetailVos)) {
            this.orderDetailVos = orderDetailVos;
            Set<String> goodsCodeList = orderDetailVos.stream().map(x -> x.getGoodsCode()).collect(Collectors.toSet());
            List<MaterialVo> materialVoList = materialVoService.findByMaterialCodes(goodsCodeList);
            orderMaterialTaxRateMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), l -> ObjectUtils.defaultIfNull(l.getTaxRate(), BigDecimal.ZERO)));
        }
        List<String> productCodeList = orderDetailVos.stream().map(x -> x.getGoodsCode()).distinct().collect(Collectors.toList());
        List<ProductVo> productVos = productVoService.findDetailsByIdsOrProductCodes(null, productCodeList);
        for (ProductVo productVo : productVos) {
            if (ObjectUtils.isEmpty(productVo.getConversionValue())) {
                productVo.setConversionValue("1");
            }
        }
        this.productConversionMap = productVos.stream().collect(Collectors.toMap(ProductVo::getMaterialCode, x -> new BigDecimal(x.getConversionValue())));
        //查询物料成本
        Set<String> materialCodeSet = Sets.newHashSet();
        Set<String> companyCodeSet = Sets.newHashSet();
        Set<String> costCenterCodeSet = Sets.newHashSet();
        materialCodeSet.addAll(salesPlanList.stream().map(x -> x.getMaterialCode()).collect(Collectors.toSet()));
        materialCodeSet.addAll(changeSalesPlanList.stream().map(x -> x.getMaterialCode()).collect(Collectors.toSet()));
        companyCodeSet.addAll(salesPlanList.stream().map(x -> x.getCompanyCode()).collect(Collectors.toSet()));
        companyCodeSet.addAll(changeSalesPlanList.stream().map(x -> x.getCompanyCode()).collect(Collectors.toSet()));
        costCenterCodeSet.addAll(salesPlanList.stream().map(x -> x.getCostCenterCode()).collect(Collectors.toSet()));
        costCenterCodeSet.addAll(changeSalesPlanList.stream().map(x -> x.getCostCenterCode()).collect(Collectors.toSet()));
        if (!CollectionUtils.isEmpty(orderDetailVos)) {
            materialCodeSet.addAll(orderDetailVos.stream().map(x -> x.getMaterialCode()).collect(Collectors.toSet()));
            companyCodeSet.addAll(orderDetailVos.stream().map(x -> x.getCompanyCode()).collect(Collectors.toSet()));
        }
        MaterialSearchDto searchDto = new MaterialSearchDto();
        searchDto.setMaterialCodeSet(materialCodeSet);
        searchDto.setCompanyCodeSet(companyCodeSet);
        List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
        this.materialVoList = materialVoList;
        this.materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO)));
        this.companyAndFactoryList = dictDataVoService.findByDictTypeCode("mdm_sap_factory_type").stream().filter(item -> companyCodeSet.contains(item.getDictCode())).collect(Collectors.toList());
        String year = detailReportVo.getYears().split("-")[0];
        this.materialTaxRateMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getTaxRate()).orElse(BigDecimal.ZERO), (a, b) -> a));
        List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialCodeSet, new ArrayList<>(companyCodeSet), year);
        this.materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + COLUMN +
                x.getCompanyCode(), Function.identity()));
        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(Lists.newArrayList(costCenterCodeSet));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(costCenterVoList)) {
            costCenterMap = costCenterVoList.stream().collect(Collectors.toMap(x -> x.getCostCenterCode(), v -> v));
        }
        Set<String> detailCodeSet = Sets.newHashSet();
        detailCodeSet.addAll(caseVoList.stream().map(x -> x.getDetailCode()).collect(Collectors.toSet()));
        detailCodeSet.addAll(originalCaseVoList.stream().map(x -> x.getDetailCode()).collect(Collectors.toSet()));
        List<CostTypeDetailVo> costTypeDetailVos = costTypeDetailVoService.findByCodes(Lists.newArrayList(detailCodeSet));
        this.costTypeDetailMap = costTypeDetailVos.stream().collect(Collectors.toMap(x -> x.getDetailCode(), l -> l.getIsWithHolding()));
        // ================
        List<String> companyCodes = salesPlanList.stream().map(MarketingSalesPlanVo::getCompanyCode).distinct().collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(companyCodes)){
            Set<DictDataVo>  factoryCodes = companyAndFactoryList.stream().filter(item -> companyCodes.contains(item.getDictCode())).collect(Collectors.toSet());
            if(!CollectionUtils.isEmpty(factoryCodes)){
                MaterialSearchDto searchDto2 = new MaterialSearchDto();
                searchDto2.setMaterialCodeSet(salesPlanList.stream().map(MarketingSalesPlanVo::getMaterialCode).collect(Collectors.toSet()));
                searchDto2.setFactoryCodeSet(factoryCodes.stream().map(DictDataVo::getDictValue).collect(Collectors.toSet()));
                List<MaterialVo> materialVoList2 = materialVoService.findBySearchDto(searchDto);
                if(!CollectionUtils.isEmpty(materialVoList2)){
                    for (MaterialVo materialVo : materialVoList2) {
                        materialCost2Map.put(materialVo.getMaterialCode(),materialVo);
                    }
                }
            }
        }
        return this;
    }


    /**
     * 计算pos拆分占比
     *
     * @param years
     * @param customerCodeSet
     * @return
     */
    private Map<String, BigDecimal> calPosData(String years, Set<String> customerCodeSet) {
        String date = years + "-01";
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
/*        // 获取上个月
        LocalDate lastMonth = localDate.minusMonths(1);
        // 获取上上个月
        LocalDate monthBeforeLast = localDate.minusMonths(2);*/
        // 2025-08-05 获取上个月
        LocalDate lastMonth = localDate;
        // 获取上上个月
        LocalDate monthBeforeLast = localDate.minusMonths(1);
        //根据数据字典查询客户
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(FormulaGetValueComponent.CUSTOMER_SPLITTING);
        //上月客户
        List<String> lastMonthCustomer = dictDataVos.stream().filter(e -> FormulaGetValueComponent.LAST_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());
        //本月客户
        List<String> currentMonthCustomer = dictDataVos.stream().filter(e -> FormulaGetValueComponent.CURRENT_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());

        List<PosDataVo> posDataVoList = Lists.newArrayList();
        //判断上个月的客户数据不为空
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastMonthCustomer)) {
            String dateStr = monthBeforeLast.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            List<String> lastCustomerCodes = Lists.newArrayList();
            for (String s : lastMonthCustomer) {
                if (!customerCodeSet.add(s)) {
                    lastCustomerCodes.add(s);
                }
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastCustomerCodes)) {
                PosDataVo dto = new PosDataVo();
                dto.setYears(dateStr);
                dto.setCustomerCodes(lastCustomerCodes);
                Page<PosDataVo> page = posDataService.findList(PageRequest.of(0, Integer.MAX_VALUE), dto);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(page.getRecords())) {
                    posDataVoList.addAll(page.getRecords());
                }
            }
        }
        //判断本月客户数据不为空
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(currentMonthCustomer)) {
            String dateStr = lastMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            List<String> customerCodes = Lists.newArrayList();
            for (String s : currentMonthCustomer) {
                if (!customerCodeSet.add(s)) {
                    customerCodes.add(s);
                }
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(customerCodes)) {
                PosDataVo dto = new PosDataVo();
                dto.setYears(dateStr);
                dto.setCustomerCodes(customerCodes);
                Page<PosDataVo> page = posDataService.findList(PageRequest.of(0, Integer.MAX_VALUE), dto);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(page.getRecords())) {
                    posDataVoList.addAll(page.getRecords());
                }
            }
        }

        Map<String, BigDecimal> postDataMap = Maps.newHashMap();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(posDataVoList)) {
            Map<String, BigDecimal> posCusAmountMap = posDataVoList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode(),
                    Collectors.mapping(PosDataVo::getAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            for (PosDataVo posDataVo : posDataVoList) {
                if (posCusAmountMap.containsKey(posDataVo.getCustomerCode())) {
                    BigDecimal amount = posCusAmountMap.get(posDataVo.getCustomerCode());
                    BigDecimal rate = posDataVo.getAmount().divide(amount, 8, BigDecimal.ROUND_HALF_DOWN);
                    postDataMap.put(posDataVo.getCustomerCode() + posDataVo.getOrgCode(), rate);
                }
            }
        }
        return postDataMap;
    }

    @Override
    public PreActualDetailReportBuild income() {
        this.income = JsonUtils.convert(detailReportVo, PreActualDetailReportVo.class);
        income.setProjectCode(PreActualProjectEnum.INCOME.getCode());
        income.setProjectName(PreActualProjectEnum.INCOME.getDesc());
        income.setSort(PreActualProjectEnum.INCOME.getSort());
        income.setBudgetCost(BigDecimal.ZERO);
        income.setPlanAmount(BigDecimal.ZERO);
        income.setLastPlanAmount(BigDecimal.ZERO);
        income.setWithholdingCost(BigDecimal.ZERO);
        income.setAuditCost(BigDecimal.ZERO);
        income.setWriteOffCost(BigDecimal.ZERO);
        income.setActYears(null);
        String orgCode = income.getOrgCode();
        this.budgetIncomeVoList = costBudgetIncomeService.findListByCustomerCodesAndOrgCodesAndYears(Lists.newArrayList(income.getCustomerCode()),
                null, Lists.newArrayList(orgCode), income.getYears());
        if (!CollectionUtils.isEmpty(budgetIncomeVoList)) {
            BigDecimal incomeCost = budgetIncomeVoList.stream().map(x -> x.getIncomeAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            income.setBudgetCost(incomeCost);
        }
        //1.第一步直接把当前有搭赠费用的方案明细的费用金额拿出来
        BigDecimal  planIncome =  BigDecimal.ZERO;
        BigDecimal  originPlanIncome =  BigDecimal.ZERO;
//        Optional<String> op = Optional.empty();

        List<MarketingPlanCaseVo> machingGiftList = caseVoList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()) && (
                        (Objects.nonNull(x.getActYears()) && x.getActYears().equals(income.getYears()) )
                       || (Objects.nonNull(x.getYears()) && x.getYears().equals(income.getYears()))
                )&& x.getCustomerCode().equals(income.getCustomerCode()))
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(originalCaseVoList)){
            List<MarketingPlanCaseVo> originMachingGiftList = originalCaseVoList.stream()
                    .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()) && (
                            (Objects.nonNull(x.getActYears()) && x.getActYears().equals(income.getYears()) )
                                    || (Objects.nonNull(x.getYears()) && x.getYears().equals(income.getYears()))
                    )&& x.getCustomerCode().equals(income.getCustomerCode()))
                    .collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(originMachingGiftList)){
                originPlanIncome =  originMachingGiftList.stream().filter(x->Objects.nonNull(x.getNoTaxApplyAmount())).map(MarketingPlanCaseVo::getNoTaxApplyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        }
        if(!CollectionUtils.isEmpty(machingGiftList)){
            planIncome =  machingGiftList.stream().filter(x->Objects.nonNull(x.getNoTaxApplyAmount())).map(MarketingPlanCaseVo::getNoTaxApplyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
     /*       List<MarketingPlanCaseVo> materialCodesList = machingGiftList.stream()
                    .filter(x -> Objects.nonNull(x.getMaterialCode())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(materialCodesList)){
                op = Optional.ofNullable(materialCodesList.get(0).getMaterialCode());
            }*/
        }

/*        if(op.isPresent()){
            if(!CollectionUtils.isEmpty(materialVoList)){
                String materialCode = op.get();
                Set<MaterialVo> materialVoList2 = materialVoList.stream().filter(item -> item.getMaterialCode().equals(materialCode)).collect(Collectors.toSet());
                if(!CollectionUtils.isEmpty(materialVoList2)){
                    MaterialVo materialVo = materialVoList2.iterator().next();
                    BigDecimal taxRate =materialVo.getTaxRate();
                    if (taxRate.compareTo(BigDecimal.ZERO)>0) {
                        planIncome = planIncome.divide((taxRate.add(BigDecimal.ONE)),2, BigDecimal.ROUND_HALF_DOWN);
                    }
                }
            }
        }*/
        //2.第二步拿销售计划的预估费用(未税)
//      BigDecimal estimatedCost = salesPlanList.stream().map(x -> x.getEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal originEstimatedCost = salesPlanList.stream().filter(x->Objects.nonNull(x.getOriginEstimatedCost())).map(x -> x.getOriginEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(Objects.isNull(originEstimatedCost)){
            originEstimatedCost=BigDecimal.ZERO;
        }
        BigDecimal changeEstimatedCost = changeSalesPlanList.stream().filter(x->Objects.nonNull(x.getEstimatedCost())).map(x -> x.getEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(Objects.isNull(changeEstimatedCost)){
            changeEstimatedCost = BigDecimal.ZERO;
        }
        income.setPlanAmount(originEstimatedCost.add(Objects.isNull(originPlanIncome)?BigDecimal.ZERO:originPlanIncome));
        income.setLastPlanAmount(changeEstimatedCost.add(Objects.isNull(planIncome)?BigDecimal.ZERO:planIncome));
        //计算发货数据
        BigDecimal withholdingCost = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(orderDetailVos)) {
            BigDecimal deliveryData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(x -> {
                                BigDecimal totalAmount;
                                if(StringUtils.equals("complimentaryGoods",x.getItemType())){
                                    totalAmount = x.getGiftActualSignAmount();
                                }else {
                                    totalAmount = x.getTotalAmount();
                                }
                                if (Objects.isNull(totalAmount)) {
                                    totalAmount = BigDecimal.ZERO;
                                }
                                return  totalAmount.divide(BigDecimal.ONE.add(orderMaterialTaxRateMap.getOrDefault(x.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN);
                            }
                    )
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal returnData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(x -> {
                        BigDecimal totalAmount;
                        if(StringUtils.equals("complimentaryGoods",x.getItemType())){
                            totalAmount = x.getGiftActualSignAmount();
                        }else {
                            totalAmount = x.getTotalAmount();
                        }
                        if (Objects.isNull(totalAmount)) {
                            totalAmount = BigDecimal.ZERO;
                        }
                        return  totalAmount.divide(BigDecimal.ONE.add(orderMaterialTaxRateMap.getOrDefault(x.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN);
                       }
                    )
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            withholdingCost = deliveryData.subtract(returnData);
        }

        Map<String, BigDecimal> posDataMap = this.calPosData(detailReportVo.getYears(), Sets.newHashSet(detailReportVo.getCustomerCode()));
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(detailReportVo.getOrgCode());
        BigDecimal finalWithholdingIncome = null;
        BigDecimal posRate = BigDecimal.ZERO;
        for (OrgVo orgVo : orgVoList) {
            String key = detailReportVo.getCustomerCode() + orgVo.getOrgCode();
            if (posDataMap.containsKey(key)) {
                if (ObjectUtils.isEmpty(finalWithholdingIncome)) {
                    finalWithholdingIncome = BigDecimal.ZERO;
                }
                BigDecimal rate = posDataMap.get(key);
                posRate = posRate.add(rate);
                finalWithholdingIncome = finalWithholdingIncome.add(withholdingCost.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }
        if (ObjectUtils.isEmpty(finalWithholdingIncome)) {
            finalWithholdingIncome = withholdingCost;
        }
        income.setWithholdingCost(finalWithholdingIncome);
        income.setAuditCost(finalWithholdingIncome);
        income.setWriteOffCost(finalWithholdingIncome);
        actualDetailReportVoList.add(income);
        return this;
    }

    @Override
    public PreActualDetailReportBuild cost() {
        this.cost = JsonUtils.convert(detailReportVo, PreActualDetailReportVo.class);
        cost.setProjectCode(PreActualProjectEnum.COST.getCode());
        cost.setProjectName(PreActualProjectEnum.COST.getDesc());
        cost.setSort(PreActualProjectEnum.COST.getSort());
        cost.setBudgetCost(BigDecimal.ZERO);
        cost.setActYears(null);

        List<String> materialCode = salesPlanList.stream().map(item -> item.getMaterialCode()).collect(Collectors.toList());
        BigDecimal costPrice2 = BigDecimal.ZERO;
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialCode)){
            List<MaterialVo> list = materialCode.stream().map(materialCost2Map::get).collect(Collectors.toList());
            if (list != null && list.size()> 0 ) {
                costPrice2 =  list.stream().filter(Objects::nonNull).filter(item->Objects.nonNull(item.getCostPrice())).map(MaterialVo::getCostPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                if(Objects.isNull(costPrice2)){
                    costPrice2 = BigDecimal.ZERO;
                }
            }
        }
        if (!CollectionUtils.isEmpty(budgetIncomeVoList)) {
            BigDecimal incomeCost = budgetIncomeVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCostAmount()))
                    .map(x -> x.getCostAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            cost.setBudgetCost(incomeCost);
        }
        //预算费率
        cost.setBudgetCostRatio(BigDecimal.ZERO);
        if (income.getBudgetCost().compareTo(BigDecimal.ZERO) == 1) {
            cost.setBudgetCostRatio(cost.getBudgetCost().divide(income.getBudgetCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        //规划金额
        cost.setPlanAmount(BigDecimal.ZERO);
        cost.setLastPlanAmount(BigDecimal.ZERO);
        BigDecimal productionCosts = originalCaseVoList.stream().filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .map(x -> x.getPolicyMaterialCostPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //销售计划的物料-找到物料成本价，用销售计划上的客户+成本中心+年月匹配得到物料 在用预估销售额*物料成本价
        Map<String, BigDecimal> materialSalesVolumeMap = salesPlanList.stream()
                .collect(Collectors.groupingBy(MarketingSalesPlanVo::getMaterialCode,
                Collectors.mapping(MarketingSalesPlanVo::getEstimatedSalesVolume,
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        for (Map.Entry<String, BigDecimal> entry : materialSalesVolumeMap.entrySet()) {
            BigDecimal costPrice = materialMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            productionCosts = productionCosts.add(costPrice.multiply(entry.getValue()));
        }
        BigDecimal lastProductionCosts = caseVoList.stream().filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .map(x -> x.getPolicyMaterialCostPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //销售计划的物料-找到物料成本价，用销售计划上的客户+成本中心+年月匹配得到物料 在用预估销售额*物料成本价
        Map<String, BigDecimal> lastMaterialSalesVolumeMap = changeSalesPlanList.stream().collect(Collectors.groupingBy(MarketingSalesPlanVo::getMaterialCode,
                Collectors.mapping(MarketingSalesPlanVo::getEstimatedSalesVolume, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        for (Map.Entry<String, BigDecimal> entry : lastMaterialSalesVolumeMap.entrySet()) {
            BigDecimal costPrice = materialMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            lastProductionCosts = lastProductionCosts.add(costPrice.multiply(entry.getValue()));
        }
        cost.setPlanAmount(productionCosts.add(costPrice2));
        cost.setLastPlanAmount(lastProductionCosts);
        //规划费率
        cost.setPlanCostRatio(BigDecimal.ZERO);
        cost.setLastPlanCostRatio(BigDecimal.ZERO);
        if (income.getPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            cost.setPlanCostRatio(cost.getPlanAmount().divide(income.getPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        if (income.getLastPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            cost.setLastPlanCostRatio(cost.getLastPlanAmount().divide(income.getLastPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        //计提金额
        cost.setWithholdingCost(BigDecimal.ZERO);
        cost.setAuditCost(BigDecimal.ZERO);
        cost.setWriteOffCost(BigDecimal.ZERO);
        cost.setWithholdingCostRatio(BigDecimal.ZERO);
        if (!CollectionUtils.isEmpty(orderDetailVos)) {
            BigDecimal withholdingCost = BigDecimal.ZERO;

            // 本品金额
            Map<String, BigDecimal> orderMaterialMap = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()) && Objects.nonNull(x.getMaterialCode())
                     && StringUtils.equals(x.getItemType(),"normalGoods"))
                    .collect(Collectors.groupingBy(x -> x.getMaterialCode(), Collectors.mapping(x -> x.getRealOutNum(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            // 赠品金额
            Map<String, BigDecimal> orderMaterialGiftMap = orderDetailVos.stream().filter(x->ObjectUtils.isNotEmpty(x.getGiftActualSignAmount()) && ObjectUtils.isNotEmpty(x.getMaterialCode())
                            && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType())&&Objects.nonNull(x.getMaterialCode()) && ObjectUtils.isNotEmpty(x.getRealOutNum())
                     && StringUtils.equals(x.getItemType(),"complimentaryGoods"))
                    .collect(Collectors.groupingBy(x -> x.getMaterialCode(), Collectors.mapping(x -> x.getRealOutNum(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

            for (Map.Entry<String, BigDecimal> entry : orderMaterialMap.entrySet()) {
                BigDecimal costPrice = materialMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
                withholdingCost = withholdingCost.add(costPrice.multiply(entry.getValue()));
            }

            for (Map.Entry<String, BigDecimal> entry : orderMaterialGiftMap.entrySet()) {
                BigDecimal costPrice = materialMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
                withholdingCost = withholdingCost.add(costPrice.multiply(entry.getValue()));
            }

            Map<String, BigDecimal> posDataMap = this.calPosData(detailReportVo.getYears(), Sets.newHashSet(detailReportVo.getCustomerCode()));
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(detailReportVo.getOrgCode());
            BigDecimal finalWithholdingCost = null;
            BigDecimal posRate = BigDecimal.ZERO;
            for (OrgVo orgVo : orgVoList) {
                String key = detailReportVo.getCustomerCode() + orgVo.getOrgCode();
                if (posDataMap.containsKey(key)) {
                        if (ObjectUtils.isEmpty(finalWithholdingCost)) {
                            finalWithholdingCost = BigDecimal.ZERO;
                    }
                    BigDecimal rate = posDataMap.get(key);
                    posRate = posRate.add(rate);
                    finalWithholdingCost = finalWithholdingCost.add(withholdingCost.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }
            cost.setWithholdingCost((Objects.isNull(finalWithholdingCost) || finalWithholdingCost.intValue() == 0)?withholdingCost:finalWithholdingCost);
            cost.setAuditCost((Objects.isNull(finalWithholdingCost) ||  finalWithholdingCost.intValue() == 0)?withholdingCost:finalWithholdingCost);
            //计算计提费率
            if (income.getWithholdingCost().compareTo(BigDecimal.ZERO) == 1) {
                if(Objects.isNull(cost.getWithholdingCost()) || Objects.isNull(income.getWithholdingCost())){
                    cost.setWithholdingCostRatio(BigDecimal.ZERO);
                }else {
                    cost.setWithholdingCostRatio(cost.getWithholdingCost().divide(income.getWithholdingCost(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
            }
            //计算结案费率
            if (income.getAuditCost().compareTo(BigDecimal.ZERO) == 1) {
                if(Objects.isNull(income.getAuditCost()) || Objects.isNull(cost.getAuditCost())){
                    cost.setAuditCostRatio(BigDecimal.ZERO);
                }else {
                    cost.setAuditCostRatio(cost.getAuditCost().divide(income.getAuditCost(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
            }
            cost.setWriteOffCost(withholdingCost);
            //计算冲销费率
            if (income.getWriteOffCost().compareTo(BigDecimal.ZERO) == 1) {
                if(Objects.isNull(cost.getWriteOffCost()) || Objects.isNull(income.getWriteOffCost())){
                    cost.setWriteOffCostRatio(BigDecimal.ZERO);
                }else {
                    cost.setWriteOffCostRatio(cost.getWriteOffCost().divide(income.getWriteOffCost(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
            }
        }
        cost.setCostDifferenceRatio(cost.getWithholdingCostRatio().subtract(cost.getLastPlanCostRatio()));
        actualDetailReportVoList.add(cost);
        return this;
    }

    @Override
    public PreActualDetailReportBuild grossProfit() {
        grossProfit = JsonUtils.convert(detailReportVo, PreActualDetailReportVo.class);
        grossProfit.setProjectCode(PreActualProjectEnum.GROSS_PROFIT.getCode());
        grossProfit.setProjectName(PreActualProjectEnum.GROSS_PROFIT.getDesc());
        grossProfit.setSort(PreActualProjectEnum.GROSS_PROFIT.getSort());
        grossProfit.setBudgetCost(income.getBudgetCost().subtract(cost.getBudgetCost()));
        grossProfit.setBudgetCostRatio(BigDecimal.ZERO);
        grossProfit.setActYears(null);
        if (income.getBudgetCost().compareTo(BigDecimal.ZERO) == 1) {
            grossProfit.setBudgetCostRatio(grossProfit.getBudgetCost().divide(income.getBudgetCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        grossProfit.setPlanAmount(income.getPlanAmount().subtract(cost.getPlanAmount()));
        grossProfit.setLastPlanAmount(income.getLastPlanAmount().subtract(cost.getLastPlanAmount()));
        if (income.getPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            grossProfit.setPlanCostRatio(cost.getPlanAmount().divide(income.getPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        grossProfit.setLastPlanCostRatio(BigDecimal.ZERO);
        if (income.getLastPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            grossProfit.setLastPlanCostRatio(cost.getLastPlanAmount().divide(income.getLastPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        if(Objects.nonNull(income.getWithholdingCost()) && Objects.nonNull(cost.getWithholdingCost())){
            grossProfit.setWithholdingCost(income.getWithholdingCost().subtract(cost.getWithholdingCost()));
        }
        grossProfit.setWithholdingCostRatio(BigDecimal.ZERO);
        if (income.getWithholdingCost().compareTo(BigDecimal.ZERO) == 1) {
            grossProfit.setWithholdingCostRatio(grossProfit.getWithholdingCost().divide(income.getWithholdingCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        grossProfit.setCostDifferenceRatio(grossProfit.getWithholdingCostRatio().subtract(grossProfit.getLastPlanCostRatio()));
        if(Objects.nonNull(cost.getAuditCost())){
            grossProfit.setAuditCost(income.getAuditCost().subtract(cost.getAuditCost()));
        }else {
            grossProfit.setAuditCost(income.getAuditCost());
        }
        grossProfit.setAuditCostRatio(BigDecimal.ZERO);
        if (income.getAuditCost().compareTo(BigDecimal.ZERO) == 1) {
            grossProfit.setAuditCostRatio(grossProfit.getAuditCost().divide(income.getAuditCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        grossProfit.setWriteOffCost(income.getWriteOffCost().subtract(cost.getWriteOffCost()));
        grossProfit.setWriteOffCostRatio(BigDecimal.ZERO);
        if (income.getWriteOffCost().compareTo(BigDecimal.ZERO) == 1) {
            grossProfit.setWriteOffCostRatio(grossProfit.getWriteOffCost().divide(income.getWriteOffCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        actualDetailReportVoList.add(grossProfit);
        return this;
    }

    @Override
    public PreActualDetailReportBuild logisticsCosts() {
        PreActualDetailReportVo logisticsCosts = JsonUtils.convert(detailReportVo, PreActualDetailReportVo.class);
        logisticsCosts.setProjectCode(PreActualProjectEnum.LOGISTICS_COSTS.getCode());
        logisticsCosts.setProjectName(PreActualProjectEnum.LOGISTICS_COSTS.getDesc());
        logisticsCosts.setSort(PreActualProjectEnum.LOGISTICS_COSTS.getSort());
        logisticsCosts.setActYears(null);
        BigDecimal budgetCost = budgetIncomeVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getLogisticsAmount())).map(x -> x.getLogisticsAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        logisticsCosts.setBudgetCost(budgetCost);
        if (income.getBudgetCost().compareTo(BigDecimal.ZERO) == 1) {
            logisticsCosts.setBudgetCostRatio(logisticsCosts.getBudgetCost().divide(income.getBudgetCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        //计算原规划的物流费
        List<String> originalCostCenterCodes = originalCaseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBelongCostCenterCodes()))
                .map(x -> x.getBelongCostCenterCodes().split(",")).flatMap(Arrays::stream).collect(Collectors.toList());
        List<MarketingSalesPlanVo> originalSalesVolumeList = salesPlanList
                .stream().filter(x -> originalCostCenterCodes.contains(x.getCostCenterCode()))
                .collect(Collectors.toList());
        //计算产品运输费用
        BigDecimal originalProductTransportCost = this.calMatchingProductTransportCost(originalCaseVoList, originalSalesVolumeList);
        originalProductTransportCost = this.calProductTransportCostList(originalProductTransportCost, originalSalesVolumeList);
        //计算周边物料
        BigDecimal originalPeripheryTransportCost = this.calPeripheryTransportCost(originalCaseVoList);
        BigDecimal planAmount = originalProductTransportCost.add(originalPeripheryTransportCost);
        logisticsCosts.setPlanAmount(planAmount);
        //计算最终规划的物流费
        List<String> costCenterCodes = caseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBelongCostCenterCodes()))
                .map(x -> x.getBelongCostCenterCodes().split(",")).flatMap(Arrays::stream).collect(Collectors.toList());
        List<MarketingSalesPlanVo> salesVolumeList = salesPlanList
                .stream().filter(x -> costCenterCodes.contains(x.getCostCenterCode()))
                .collect(Collectors.toList());

        //计算产品运输费用
        BigDecimal productTransportCost = this.calMatchingProductTransportCost(caseVoList, salesVolumeList);
        productTransportCost = this.calProductTransportCostList(productTransportCost, salesVolumeList);
        //计算周边物料
        BigDecimal peripheryTransportCost = this.calPeripheryTransportCost(caseVoList);
        BigDecimal lastPlanAmount = productTransportCost.add(peripheryTransportCost);
        logisticsCosts.setLastPlanAmount(lastPlanAmount);
        logisticsCosts.setPlanCostRatio(BigDecimal.ZERO);
        if (income.getPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            logisticsCosts.setPlanCostRatio(logisticsCosts.getPlanAmount().divide(income.getPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        logisticsCosts.setLastPlanCostRatio(BigDecimal.ZERO);
        if (income.getLastPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            logisticsCosts.setLastPlanCostRatio(logisticsCosts.getLastPlanAmount().divide(income.getLastPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        //计算计提金额
        logisticsCosts.setWithholdingCost(BigDecimal.ZERO);
        if (!CollectionUtils.isEmpty(orderDetailVos)) {
            //搭赠费用(销售价*实际发货数量)
            BigDecimal giftCost = orderDetailVos.stream().filter(e -> e.getRealOutNum() != null && giftItemTypeList.contains(e.getItemType())
                            && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType())).filter(Objects::nonNull)
                    .map(e -> e.getPresetUnitPrice().multiply(e.getRealOutNum()).divide(BigDecimal.ONE.add(materialTaxRateMap.getOrDefault(e.getGoodsCode(), BigDecimal.ZERO)), 2, BigDecimal.ROUND_HALF_DOWN))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //运输费用(产品运输费用+周边运输费用)
            BigDecimal productExpressFee = orderDetailVos.stream().filter(e -> e.getExpressFee() != null && productItemTypeList.contains(e.getItemType())
                            && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                    .map(e -> {
                        BigDecimal price = materialCostMap.getOrDefault(e.getMaterialCode() + COLUMN + e.getCompanyCode(), new MaterialCostVo()).getLargeLogisticsPrice();
                        return (Optional.ofNullable(price).orElse(BigDecimal.ZERO).multiply(e.getRealOutNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                    })
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal peripheryExpressFee = orderDetailVos.stream().filter(e -> e.getExpressFee() != null &&
                            peripheryItemTypeList.contains(e.getItemType())
                            && DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(e.getWarehouseOrderType()))
                    .map(e -> {
                        BigDecimal price = materialCostMap.getOrDefault(e.getMaterialCode() + COLUMN + e.getCompanyCode(), new MaterialCostVo()).getLargeLogisticsPrice();
                        return (Optional.ofNullable(price).orElse(BigDecimal.ZERO).multiply(e.getRealOutNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                    })
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            logisticsCosts.setWithholdingCost(giftCost.add(productExpressFee).add(peripheryExpressFee));
        }
        BigDecimal finalWithholdingLogisticsCosts= null;
        if(Objects.nonNull(logisticsCosts.getWithholdingCost())){
            Map<String, BigDecimal> posDataMap = this.calPosData(detailReportVo.getYears(), Sets.newHashSet(detailReportVo.getCustomerCode()));
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(detailReportVo.getOrgCode());
            BigDecimal posRate = BigDecimal.ZERO;
            for (OrgVo orgVo : orgVoList) {
                String key = detailReportVo.getCustomerCode() + orgVo.getOrgCode();
                if (posDataMap.containsKey(key)) {
                    if (ObjectUtils.isEmpty(finalWithholdingLogisticsCosts)) {
                        finalWithholdingLogisticsCosts = BigDecimal.ZERO;
                    }
                    BigDecimal rate = posDataMap.get(key);
                    posRate = posRate.add(rate);
                    finalWithholdingLogisticsCosts = finalWithholdingLogisticsCosts.add(logisticsCosts.getWithholdingCost().multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }
            logisticsCosts.setWithholdingCost(finalWithholdingLogisticsCosts);
        }
        logisticsCosts.setWithholdingCostRatio(BigDecimal.ZERO);
        if (income.getWithholdingCost().compareTo(BigDecimal.ZERO) == 1) {
            if(Objects.nonNull(logisticsCosts.getWithholdingCost())){
                logisticsCosts.setWithholdingCostRatio(logisticsCosts.getWithholdingCost().divide(income.getWithholdingCost(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        logisticsCosts.setCostDifferenceRatio(logisticsCosts.getWithholdingCostRatio().subtract(logisticsCosts.getLastPlanCostRatio()));
        logisticsCosts.setAuditCost(logisticsCosts.getWithholdingCost());
        logisticsCosts.setAuditCostRatio(BigDecimal.ZERO);
        if (income.getAuditCost().compareTo(BigDecimal.ZERO) == 1) {
            if(Objects.nonNull(logisticsCosts.getAuditCost())){
                logisticsCosts.setAuditCostRatio(logisticsCosts.getAuditCost().divide(income.getAuditCost(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        logisticsCosts.setWriteOffCost(logisticsCosts.getWithholdingCost());
        actualDetailReportVoList.add(logisticsCosts);
        calActualDetailReportVoList.add(logisticsCosts);
        return this;
    }

    /**
     * 计算搭赠的产品运输费用
     *
     * @param caseVoList
     * @param salesPlanList
     * @return
     */
    private BigDecimal calMatchingProductTransportCost(List<MarketingPlanCaseVo> caseVoList, List<MarketingSalesPlanVo> salesPlanList) {
        List<MarketingPlanCaseVo> policyCaseList = caseVoList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()) && ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(policyCaseList)) {
            return BigDecimal.ZERO;
        }
        Map<String, String> cusProductSalesPlanMap = salesPlanList.stream().collect(Collectors.toMap(x -> x.getCustomerCode() + x.getProductCode(), l -> l.getTransportType(),(v1,v2)->v1));
        Set<String> materialCodeSet = policyCaseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getPolicyProductCode()))
                .map(MarketingPlanCaseVo::getPolicyProductCode).collect(Collectors.toSet());
        List<String> companyCodes = policyCaseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode()))
                .map(MarketingPlanCaseVo::getCompanyCode).distinct().collect(Collectors.toList());
        for (MarketingPlanCaseVo aCase : policyCaseList) {
            String key = aCase.getCustomerCode() + aCase.getPolicyProductCode();
            if (cusProductSalesPlanMap.containsKey(key)) {
                aCase.setTransportType(cusProductSalesPlanMap.get(key));
            }
        }
        String year = detailReportVo.getYears().split("-")[0];
        List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialCodeSet, companyCodes, year);
        Map<String, MaterialCostVo> materialCostVoMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + COLUMN +
                x.getCompanyCode() + COLUMN + x.getYearStr(), Function.identity()));

        Map<String, BigDecimal> materialCostMap = Maps.newHashMap();
        for (MarketingPlanCaseVo planCase : policyCaseList) {
            String key = planCase.getPolicyProductCode() + COLUMN + planCase.getCompanyCode() + COLUMN + year;
            if (materialCostVoMap.containsKey(key)) {
                MaterialCostVo materialCostVo = materialCostVoMap.get(key);
                BigDecimal price = BigDecimal.ZERO;
                if (MarketingTransportEnum.bulk_cargo.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getLargeLogisticsPrice();
                } else if (MarketingTransportEnum.express.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getExpressLogisticsPrice();
                } else if (MarketingTransportEnum.cold_chain.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getColdLogisticsPrice();
                } else if (MarketingTransportEnum.factory.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getFactoryStraightPrice();
                }
                materialCostMap.put(planCase.getPolicyProductCode(), price);

            }
        }
        BigDecimal amount = BigDecimal.ZERO;
        for (MarketingPlanCaseVo aCase : policyCaseList) {
            BigDecimal transportAmount = materialCostMap.getOrDefault(aCase.getPolicyProductCode(), BigDecimal.ZERO);
            amount = amount.add(transportAmount.multiply(ObjectUtils.defaultIfNull(aCase.getGiftQuantity(), BigDecimal.ZERO)));
        }
        return amount;
    }


    /**
     * 计算产品运输费用
     *
     * @param productTransportCost
     * @param salesPlanList
     */
    private BigDecimal calProductTransportCostList(BigDecimal productTransportCost, List<MarketingSalesPlanVo> salesPlanList) {
        //产品运输费用计算
        List<String> companyCodes = Lists.newArrayList();
        for (MarketingSalesPlanVo salesPlan : salesPlanList) {
            if (costCenterMap.containsKey(salesPlan.getCostCenterCode())) {
                MdmCostCenterVo costCenterVo1 = costCenterMap.get(salesPlan.getCostCenterCode());
                companyCodes.add(costCenterVo1.getCompanyCode());
                salesPlan.setCostCenterCompanyCode(costCenterVo1.getCompanyCode());
            }
        }
        Set<String> materialCodes = salesPlanList.stream().map(x -> x.getMaterialCode()).collect(Collectors.toSet());
        String year = detailReportVo.getYears().split("-")[0];
        //查询物料成本
        List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialCodes, companyCodes, year);
        Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + COLUMN +
                x.getCompanyCode() + COLUMN + x.getYearStr(), Function.identity()));
        //产品运输费用
        for (MarketingSalesPlanVo plan : salesPlanList) {
            String key = plan.getMaterialCode() + COLUMN + plan.getCompanyCode() + COLUMN + year;
            if (materialCostMap.containsKey(key)) {
                MaterialCostVo materialCostVo = materialCostMap.get(key);
                BigDecimal price = BigDecimal.ZERO;
                if (MarketingTransportEnum.bulk_cargo.getCode().equals(plan.getTransportType())) {
                    price = materialCostVo.getLargeLogisticsPrice();
                } else if (MarketingTransportEnum.express.getCode().equals(plan.getTransportType())) {
                    price = materialCostVo.getExpressLogisticsPrice();
                } else if (MarketingTransportEnum.cold_chain.getCode().equals(plan.getTransportType())) {
                    price = materialCostVo.getColdLogisticsPrice();
                } else if (MarketingTransportEnum.factory.getCode().equals(plan.getTransportType())) {
                    price = materialCostVo.getFactoryStraightPrice();
                }
                productTransportCost = productTransportCost.add(price.multiply(plan.getEstimatedSalesVolume()));
            }
        }
        return productTransportCost;
    }

    /**
     * 计算周边物料
     *
     * @param caseVoList
     * @return
     */
    private BigDecimal calPeripheryTransportCost(List<MarketingPlanCaseVo> caseVoList) {
        //计算物料模板的费用运输
        BigDecimal peripheryTransportCost = BigDecimal.ZERO;
        List<MarketingPlanCaseVo> materialCaseList = caseVoList.stream().filter(x -> MarketingPlanCaseTypeEnum.material.getCode().equals(x.getCaseType())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(materialCaseList)) {
            //标准物料
            BigDecimal materialStandardAmount = materialCaseList.stream().filter(x -> MarketingPlanMaterialTypeEnum.standard_material.getCode().equals(x.getSellMaterialType())).map(MarketingPlanCaseVo::getApplyAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalStandardMaterialAmount = materialStandardAmount.multiply(BigDecimal.valueOf(0.15));
            peripheryTransportCost = peripheryTransportCost.add(totalStandardMaterialAmount);
            //非标准物料
            List<MarketingPlanCaseVo> notStandardMaterialList = materialCaseList.stream().filter(x -> MarketingPlanMaterialTypeEnum.not_standard_material.getCode().equals(x.getSellMaterialType())).collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(notStandardMaterialList)) {
                List<String> companyCodes = Lists.newArrayList();
                Set<String> notStandardMaterialCodes = Sets.newHashSet();
                for (MarketingPlanCaseVo planCase : notStandardMaterialList) {
                    companyCodes.add(planCase.getCompanyCode());
                    notStandardMaterialCodes.add(planCase.getMaterialCode());
                }
                //查询物料成本
                String year = detailReportVo.getYears().split("-")[0];
                List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(notStandardMaterialCodes, companyCodes, year);
                Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + COLUMN +
                        x.getCompanyCode() + COLUMN + x.getYearStr(), Function.identity()));
                for (MarketingPlanCaseVo planCase : notStandardMaterialList) {
                    String key = planCase.getMaterialCode() + COLUMN + planCase.getCompanyCode() + COLUMN + year;
                    if (materialCostMap.containsKey(key)) {
                        MaterialCostVo materialCostVo = materialCostMap.get(key);
                        BigDecimal price = BigDecimal.ZERO;
                        if (MarketingTransportEnum.bulk_cargo.getCode().equals(planCase.getTransportType())) {
                            price = materialCostVo.getLargeLogisticsPrice();
                        } else if (MarketingTransportEnum.express.getCode().equals(planCase.getTransportType())) {
                            price = materialCostVo.getExpressLogisticsPrice();
                        } else if (MarketingTransportEnum.cold_chain.getCode().equals(planCase.getTransportType())) {
                            price = materialCostVo.getColdLogisticsPrice();
                        } else if (MarketingTransportEnum.factory.getCode().equals(planCase.getTransportType())) {
                            price = materialCostVo.getFactoryStraightPrice();
                        }
                        peripheryTransportCost = peripheryTransportCost.add(price.multiply(planCase.getMaterialNum()));

                    }
                }
            }
        }
        return peripheryTransportCost;
    }

    @Override
    public PreActualDetailReportBuild giftCost() {
        PreActualDetailReportVo giftCost = JsonUtils.convert(detailReportVo, PreActualDetailReportVo.class);
        giftCost.setProjectCode(PreActualProjectEnum.GIFT_COST.getCode());
        giftCost.setProjectName(PreActualProjectEnum.GIFT_COST.getDesc());
        giftCost.setSort(PreActualProjectEnum.GIFT_COST.getSort());
        giftCost.setBudgetCost(BigDecimal.ZERO);
        giftCost.setActYears(null);
        actualDetailReportVoList.add(giftCost);
        return this;
    }


    @Override
    public PreActualDetailReportBuild budgeSubject() {
        List<String> codes = Arrays.asList("B409500", "B406900", "B403500", "B403600", "B406300","B650100");
        Map<String, List<MarketingPlanCaseVo>> caseMap = caseVoList.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectCode() + COLUMN + x.getBudgetSubjectName() + COLUMN + x.getActYears()));
        Set<String> budgetSubjectCodes = Sets.newHashSet();
        budgetSubjectCodes.addAll(caseVoList.stream().map(x -> x.getBudgetSubjectCode()).collect(Collectors.toSet()));
        budgetSubjectCodes.addAll(originalCaseVoList.stream().map(x -> x.getBudgetSubjectCode()).collect(Collectors.toSet()));
        List<CostBudgetVo> costBudgetVoList = costBudgetVoService.findListByOrgCodesAndCustomerCodeAndBudgetSubjectCodesAndYears(Lists.newArrayList(detailReportVo.getOrgCode()), detailReportVo.getCustomerCode(), Lists.newArrayList(budgetSubjectCodes), detailReportVo.getYears());
        Map<String, BigDecimal> costBudgetMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(costBudgetVoList)) {
            costBudgetMap = costBudgetVoList.stream().collect(Collectors.toMap(x -> x.getBudgetSubjectCode(), l -> l.getInitialAmount()));
        }
        Map<String, BigDecimal> originalCaseApplyMap = originalCaseVoList.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectCode() + x.getActYears(),
                Collectors.mapping(x -> x.getApplyAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, BigDecimal> caseApplyMap = caseVoList.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectCode() + x.getActYears(),
                Collectors.mapping(x -> x.getApplyAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //查询计提
        List<String> schemeDetailCodes = caseVoList.stream().map(x -> x.getSchemeDetailCode()).collect(Collectors.toList());
        List<WithHoldingVo> withHoldingVoList = withHoldingService.findListBySchemeDetailCodes(schemeDetailCodes);
        List<FeeCashDetailVo> feeCashDetailVos = feeCashService.findCashAmountBySchemeDetailCodesPass(schemeDetailCodes);
        Map<String, BigDecimal> withholdingMap = Maps.newHashMap();
        Map<String, BigDecimal> feeCashDetailMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(withHoldingVoList)) {
            withholdingMap = withHoldingVoList.stream().collect(Collectors.toMap(x -> x.getActivitiesDetailCode(), l -> l.getActualReportAmount(), (v1, v2) -> v1 ));
        }
        if (!CollectionUtils.isEmpty(feeCashDetailVos)) {
            feeCashDetailMap = feeCashDetailVos.stream().collect(Collectors.groupingBy(x -> x.getSchemeDetailCode(),
                    Collectors.mapping(l -> l.getThisCashAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        //查询结案金额
        List<MarketingAuditDetailVo> auditDetailVos = marketingAuditService.findListBySchemeDetailCodes(schemeDetailCodes);
        Map<String, BigDecimal> auditDetailMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(auditDetailVos)) {
            auditDetailMap = auditDetailVos.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), l -> l.getAuditAmount()));
        }
        //查询冲销金额（只要结案和关闭冲销的）
        List<WithHoldingWriteOffVo> withHoldingWriteOffVoList = withHoldingWriteOffService.findListBySchemeDetailCodes(schemeDetailCodes);
        Map<String, BigDecimal> writeOffMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(withHoldingWriteOffVoList)) {
            writeOffMap = withHoldingWriteOffVoList.stream().collect(Collectors.groupingBy(x -> x.getActivitiesDetailCode(),
                    Collectors.mapping(x -> x.getWriteOffAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }

        boolean isTest = Objects.isNull(activeProfiles)?false: Arrays.stream(activeProfiles).filter(item->StringUtils.equals("dev",item)).findAny().isPresent();
        Integer count = 6;
        List<FeeCashDetailVo> thisCashDetailVo = null;
        boolean thisCashDetailVoFlag = false;
        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : caseMap.entrySet()) {
            String[] key = entry.getKey().split(COLUMN);
            MarketingPlanCaseVo caseVo = entry.getValue().get(0);
            String detailCode = caseVo.getDetailCode();
            String isWithholding = costTypeDetailMap.getOrDefault(detailCode, BooleanEnum.FALSE.getCapital());
            PreActualDetailReportVo budgetSubject = JsonUtils.convert(detailReportVo, PreActualDetailReportVo.class);
            String projectCode = key[0];
            budgetSubject.setProjectCode(projectCode);
            budgetSubject.setProjectName(key[1]);
            budgetSubject.setActYears(key[2]);
            budgetSubject.setSort(count++);
            BigDecimal budgetCost = costBudgetMap.getOrDefault(projectCode, BigDecimal.ZERO);
            budgetSubject.setBudgetCost(budgetCost);
            budgetSubject.setBudgetCostRatio(BigDecimal.ZERO);
            if (income.getBudgetCost().compareTo(BigDecimal.ZERO) == 1) {
                budgetSubject.setBudgetCostRatio(budgetSubject.getBudgetCost().divide(income.getBudgetCost(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            String caseKey = budgetSubject.getProjectCode()+budgetSubject.getActYears();
            BigDecimal planAmount = originalCaseApplyMap.getOrDefault(caseKey, BigDecimal.ZERO);
            BigDecimal lastPlanAmount = caseApplyMap.getOrDefault(caseKey, BigDecimal.ZERO);
            budgetSubject.setPlanAmount(planAmount);
            budgetSubject.setLastPlanAmount(lastPlanAmount);
            budgetSubject.setPlanCostRatio(BigDecimal.ZERO);
            budgetSubject.setLastPlanCostRatio(BigDecimal.ZERO);
            if (income.getPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
                budgetSubject.setPlanCostRatio(budgetSubject.getPlanAmount().divide(income.getPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            if (income.getLastPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
                budgetSubject.setLastPlanCostRatio(budgetSubject.getLastPlanAmount().divide(income.getLastPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            BigDecimal withholdingCost = BigDecimal.ZERO;
            for (MarketingPlanCaseVo vo : entry.getValue()) {
                if (BooleanEnum.FALSE.getCapital().equals(isWithholding)) {
                    withholdingCost = withholdingCost.add(auditDetailMap.getOrDefault(vo.getSchemeDetailCode(), BigDecimal.ZERO));
                } else {
                    withholdingCost = withholdingCost.add(withholdingMap.getOrDefault(vo.getSchemeDetailCode(), BigDecimal.ZERO))
                            .add(feeCashDetailMap.getOrDefault(vo.getSchemeDetailCode(), BigDecimal.ZERO));
                }
            }
            BigDecimal allAuditAmount = BigDecimal.ZERO;
            BigDecimal allAuditedAmount = BigDecimal.ZERO;
            if(codes.contains(projectCode)){
                MarketingAuditDetailDto dto = createAuditDetailDto(budgetSubject);
                if(Objects.nonNull(dto)){
                    if(isTest){
                        log.info("搭赠及周边== projectCode:{} dto:{}",projectCode,JSONObject.toJSONString(dto));
                    }
                    Page<MarketingAuditDetailVo> page = marketingAuditService.findDataViewByConditions(PageRequest.of(0, Integer.MAX_VALUE), dto);
                    List<MarketingAuditDetailVo> records = page.getRecords();
                    if(!CollectionUtils.isEmpty(records)){
                        records =  records.stream().filter(item->StringUtils.equals("Y",item.getBeFullAudit())).collect(Collectors.toList());
                        if(!CollectionUtils.isEmpty(records)){
                            BigDecimal bigDecimal = records.stream().filter(item -> Objects.nonNull(item.getAuditAmount())).map(MarketingAuditDetailVo::getAuditAmount).collect(Collectors.toList()).stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                            if (Objects.nonNull(bigDecimal)) {
                                allAuditAmount = bigDecimal;
                            }
                            BigDecimal bigDecimal1 = records.stream().filter(item -> Objects.nonNull(item.getAuditedAmount())).map(MarketingAuditDetailVo::getAuditedAmount).collect(Collectors.toList()).stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                            if(Objects.nonNull(bigDecimal1)){
                                allAuditedAmount = bigDecimal1;
                            }
                        }
                    }
                }
                withholdingCost =  withholdingCost.add(allAuditAmount).add(allAuditedAmount);
                // 本次兑付金额
                if(Objects.isNull(thisCashDetailVo) && !thisCashDetailVoFlag){
                    FeeCashDetailDto dto2 = new FeeCashDetailDto();
                    dto2.setBelongDepartmentCode(budgetSubject.getOrgCode());
                    dto2.setYears(budgetSubject.getYears());
                    dto2.setCustomerCode(budgetSubject.getCustomerCode());
                    thisCashDetailVo = feeCashDetailMapper.findAuditedDetailList(dto2);
                    thisCashDetailVoFlag = true;
                }
                BigDecimal thisCashAmount = BigDecimal.ZERO;
                if(!CollectionUtils.isEmpty(thisCashDetailVo)){
                    FeeCashDetailVo feeCashDetailVo = thisCashDetailVo.get(0);
                    if(Objects.nonNull(feeCashDetailVo.getThisCashAmount())){
                        thisCashAmount = feeCashDetailVo.getThisCashAmount();
                    }
                }
                withholdingCost = withholdingCost.add(thisCashAmount);
            }
            budgetSubject.setWithholdingCost(withholdingCost);
            budgetSubject.setWithholdingCostRatio(BigDecimal.ZERO);
            if (income.getWithholdingCost().compareTo(BigDecimal.ZERO) == 1) {
                budgetSubject.setWithholdingCostRatio(budgetSubject.getWithholdingCost().divide(income.getWithholdingCost(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            budgetSubject.setCostDifferenceRatio(budgetSubject.getWithholdingCostRatio().subtract(budgetSubject.getLastPlanCostRatio()));
            BigDecimal auditAmount = BigDecimal.ZERO;
            BigDecimal writeOffAmount = BigDecimal.ZERO;
            for (MarketingPlanCaseVo vo : entry.getValue()) {
                auditAmount = auditAmount.add(auditDetailMap.getOrDefault(vo.getSchemeDetailCode(), BigDecimal.ZERO));
                writeOffAmount = writeOffAmount.add(writeOffMap.getOrDefault(vo.getSchemeDetailCode(), BigDecimal.ZERO));
            }
            budgetSubject.setAuditCost(auditAmount);
            budgetSubject.setWriteOffCost(writeOffAmount);
            budgetSubject.setAuditCostRatio(BigDecimal.ZERO);


            if (income.getAuditCost().compareTo(BigDecimal.ZERO) == 1) {
                budgetSubject.setAuditCostRatio(budgetSubject.getAuditCost().divide(income.getAuditCost(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            budgetSubject.setWriteOffCostRatio(BigDecimal.ZERO);
            if (income.getWriteOffCost().compareTo(BigDecimal.ZERO) == 1) {
                budgetSubject.setWriteOffCostRatio(budgetSubject.getWriteOffCost().divide(income.getWriteOffCost(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            actualDetailReportVoList.add(budgetSubject);
            calActualDetailReportVoList.add(budgetSubject);
        }

        return this;
    }

    private MarketingAuditDetailDto createAuditDetailDto(PreActualDetailReportVo model) {
        if(Objects.isNull(model)){
            return null;
        }
        String rYears = null;
        if(StringUtils.isNotBlank(model.getYears())){
            rYears =model.getYears();
        }
        if(Objects.isNull(rYears) && StringUtils.isNotBlank( model.getActYears())){
            rYears =  model.getActYears();
        }
        if(Objects.isNull(rYears)){
            log.info("createAuditDetailDto:  model {} 年月为空 ",JSONObject.toJSONString(model));
            return null;
        }
        MarketingAuditDetailDto dto = new MarketingAuditDetailDto();
        dto.setDetailCodeList(Arrays.asList("HDXL00081", "HDXL00055", "HDXL00024", "HDXL00023", "HDXL00049", "HDXL00001"));
        dto.setBelongDepartmentCode(model.getOrgCode());
        dto.setCustomerCode(model.getCustomerCode());
        dto.setYears(rYears);
        return dto;
    }

    @Override
    public PreActualDetailReportBuild surroundingMaterials() {
        PreActualDetailReportVo surroundingMaterials = JsonUtils.convert(detailReportVo, PreActualDetailReportVo.class);
        surroundingMaterials.setProjectCode(PreActualProjectEnum.SURROUNDING_MATERIALS.getCode());
        surroundingMaterials.setProjectName(PreActualProjectEnum.SURROUNDING_MATERIALS.getDesc());
        surroundingMaterials.setSort(PreActualProjectEnum.SURROUNDING_MATERIALS.getSort());
        surroundingMaterials.setBudgetCost(BigDecimal.ZERO);
        surroundingMaterials.setActYears(null);
        actualDetailReportVoList.add(surroundingMaterials);
        return this;
    }

    @Override
    public PreActualDetailReportBuild share() {
        PreActualDetailReportVo share = JsonUtils.convert(detailReportVo, PreActualDetailReportVo.class);
        share.setProjectCode(PreActualProjectEnum.SHARE.getCode());
        share.setProjectName(PreActualProjectEnum.SHARE.getDesc());
        share.setSort(PreActualProjectEnum.SHARE.getSort());
        share.setActYears(null);
        List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(detailReportVo.getOrgCode());
        List<String> orgCodes = orgVoList.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgCodes)) {
            orgCodes = Lists.newArrayList(detailReportVo.getOrgCode());
        }
        //查询公摊费率
        BigDecimal publicShareRatio = publicShareRatioService.findRatioByCondition(orgCodes, detailReportVo.getYears());
        share.setBudgetCost(income.getBudgetCost().multiply(publicShareRatio));
        share.setBudgetCostRatio(BigDecimal.ZERO);
        if (income.getBudgetCost().compareTo(BigDecimal.ZERO) == 1) {
            share.setBudgetCostRatio(share.getBudgetCost().divide(income.getBudgetCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        share.setPlanAmount(publicShareRatio.multiply(income.getPlanAmount()));
        share.setLastPlanAmount(publicShareRatio.multiply(income.getLastPlanAmount()));
        share.setPlanCostRatio(BigDecimal.ZERO);
        share.setLastPlanCostRatio(BigDecimal.ZERO);
        if (income.getPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            share.setPlanCostRatio(share.getPlanAmount().divide(income.getPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        if (income.getLastPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            share.setLastPlanCostRatio(share.getLastPlanAmount().divide(income.getLastPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        share.setWithholdingCost(publicShareRatio.multiply(income.getWithholdingCost()));
        share.setWithholdingCostRatio(BigDecimal.ZERO);
        if (income.getWithholdingCost().compareTo(BigDecimal.ZERO) == 1) {
            share.setWithholdingCostRatio(share.getWithholdingCost().divide(income.getWithholdingCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        share.setCostDifferenceRatio(share.getWithholdingCostRatio().subtract(share.getLastPlanCostRatio()));
        share.setAuditCost(publicShareRatio.multiply(income.getAuditCost()));
        share.setAuditCostRatio(BigDecimal.ZERO);
        if (income.getAuditCost().compareTo(BigDecimal.ZERO) == 1) {
            share.setAuditCostRatio(share.getAuditCost().divide(income.getAuditCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        share.setWriteOffCost(publicShareRatio.multiply(income.getWriteOffCost()));
        share.setWriteOffCostRatio(BigDecimal.ZERO);
        if (income.getWriteOffCost().compareTo(BigDecimal.ZERO) == 1) {
            share.setWriteOffCostRatio(share.getWriteOffCost().divide(income.getWriteOffCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        actualDetailReportVoList.add(share);
        calActualDetailReportVoList.add(share);
        return this;
    }

    @Override
    public PreActualDetailReportBuild profit() {
        PreActualDetailReportVo profit = JsonUtils.convert(detailReportVo, PreActualDetailReportVo.class);
        profit.setProjectCode(PreActualProjectEnum.PROFIT.getCode());
        profit.setProjectName(PreActualProjectEnum.PROFIT.getDesc());
        profit.setSort(PreActualProjectEnum.PROFIT.getSort());
        profit.setActYears(null);
        BigDecimal totalBudgetCost = calActualDetailReportVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBudgetCost()))
                .map(x -> x.getBudgetCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal budgetCost = grossProfit.getBudgetCost().subtract(totalBudgetCost);
        profit.setBudgetCost(budgetCost);
        profit.setBudgetCostRatio(BigDecimal.ZERO);
        if (income.getBudgetCost().compareTo(BigDecimal.ZERO) == 1) {
            profit.setBudgetCostRatio(profit.getBudgetCost().divide(income.getBudgetCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        BigDecimal totalPlanAmount = calActualDetailReportVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanAmount()))
                .map(x -> x.getPlanAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal planAmount = grossProfit.getPlanAmount().subtract(totalPlanAmount);
        profit.setPlanAmount(planAmount);
        BigDecimal totalLastPlanAmount = calActualDetailReportVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getLastPlanAmount()))
                .map(x -> x.getLastPlanAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal lastPlanAmount = grossProfit.getLastPlanAmount().subtract(totalLastPlanAmount);
        profit.setLastPlanAmount(lastPlanAmount);
        profit.setPlanCostRatio(BigDecimal.ZERO);
        if (income.getPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            profit.setPlanCostRatio(profit.getPlanAmount().divide(income.getPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        profit.setLastPlanCostRatio(BigDecimal.ZERO);
        if (income.getLastPlanAmount().compareTo(BigDecimal.ZERO) == 1) {
            profit.setLastPlanCostRatio(profit.getLastPlanAmount().divide(income.getLastPlanAmount(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        BigDecimal totalWithholdingAmount = calActualDetailReportVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getWithholdingCost()))
                .map(x -> x.getWithholdingCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal withholdingAmount = grossProfit.getWithholdingCost().subtract(totalWithholdingAmount);
        profit.setWithholdingCost(withholdingAmount);
        profit.setWithholdingCostRatio(BigDecimal.ZERO);
        if (income.getWithholdingCost().compareTo(BigDecimal.ZERO) == 1) {
            profit.setWithholdingCostRatio(profit.getWithholdingCost().divide(income.getWithholdingCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        profit.setCostDifferenceRatio(profit.getWithholdingCostRatio().subtract(profit.getLastPlanCostRatio()));
        BigDecimal totalAuditAmount = calActualDetailReportVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getAuditCost()))
                .map(x -> x.getAuditCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal auditAmount = grossProfit.getAuditCost().subtract(totalAuditAmount);
        profit.setAuditCost(auditAmount);
        profit.setAuditCostRatio(BigDecimal.ZERO);
        if (income.getAuditCost().compareTo(BigDecimal.ZERO) == 1) {
            profit.setAuditCostRatio(profit.getAuditCost().divide(income.getAuditCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        BigDecimal totalWriteOffAmount = calActualDetailReportVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getWriteOffCost()))
                .map(x -> x.getWriteOffCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal writeOffAmount = grossProfit.getWriteOffCost().subtract(totalWriteOffAmount);
        profit.setWriteOffCost(writeOffAmount);
        profit.setWriteOffCostRatio(BigDecimal.ZERO);
        if (income.getWriteOffCost().compareTo(BigDecimal.ZERO) == 1) {
            profit.setWriteOffCostRatio(profit.getWriteOffCost().divide(income.getWriteOffCost(), 4, BigDecimal.ROUND_HALF_DOWN));
        }

        actualDetailReportVoList.add(profit);
        return this;
    }
}
