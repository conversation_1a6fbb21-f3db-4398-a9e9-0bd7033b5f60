<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.admin.web.exports.costbudget.mapper.CostBudgetExportsMapper">

  <sql id="conditions">
    <if test="dto.tenantCode != null and dto.tenantCode != '' ">
      and t.tenant_code = #{dto.tenantCode}
    </if>
    <if test="dto.channelCode != null and dto.channelCode != '' ">
      and t.channel_code = #{dto.channelCode}
    </if>
    <if test="dto.channelName != null and dto.channelName != '' ">
      and t.channel_name = #{dto.channelName}
    </if>
    <if test="dto.code != null and dto.code != '' ">
      <bind name = "code" value = " '%' + dto.code + '%' " />
      and t.code like #{code}
    </if>
    <if test="dto.customerName != null and dto.customerName != '' ">
      <bind name = "customerName" value = " '%' + dto.customerName + '%' " />
      and t.customer_name like #{customerName}
    </if>
    <if test="dto.orgName != null and dto.orgName != '' ">
      <bind name = "orgName" value = " '%' + dto.orgName + '%' " />
      and t.org_name like #{orgName}
    </if>
    <if test="dto.type != null and dto.type != '' ">
      and t.type = #{dto.type}
    </if>
    <if test="dto.budgetSubjectCode != null and dto.budgetSubjectCode != '' ">
      and t.budget_subject_code = #{dto.budgetSubjectCode}
    </if>
    <if test="dto.budgetSubjectName != null and dto.budgetSubjectName != '' ">
      and t.budget_subject_name = #{dto.budgetSubjectName}
    </if>
    <if test="dto.enableStatus != null and dto.enableStatus != '' ">
      and t.enable_status = #{dto.enableStatus}
    </if>
    and t.del_flag = '${@<EMAIL>()}'
  </sql>


  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from tpm_cost_budget t 
    <where>
       <include refid="conditions"/> 
    </where>
  </select>
  
  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.costbudget.model.CostBudgetExportsVo">
    select *
    from tpm_cost_budget t 
    <where>
      <include refid="conditions" />
    </where>
    order by t.create_time desc, t.id
     limit #{dto.offset},#{dto.limit}
  </select>
</mapper>