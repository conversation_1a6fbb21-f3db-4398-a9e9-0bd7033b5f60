package com.biz.crm.tpm.admin.web.preactualreport.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.admin.web.preactualreport.entity.PreActualReportCollect;
import com.biz.crm.tpm.admin.web.preactualreport.service.strategy.PreActualReportCollectAbstractBuilder;
import com.biz.crm.tpm.admin.web.vo.PreActualReportCollectVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/4 17:11
 **/
@Component
public class PreActualReportCollectComponent {

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private ApplicationContext context;

    @Resource
    private PreActualReportCollectService preActualReportCollectService;


    @Async("tpmRegionCollectThread")
    public void syncPreActualReportCollect(List<PreActualReportCollectVo> reportCollectVoList, Map<String, List<MarketingPlanCaseVo>> caseListMap,
                                           Map<String, MarketingPlanVo> planMap){
        loginUserService.refreshAuthentication(null);

        PreActualReportCollectAbstractBuilder.builder(context, reportCollectVoList)
                .init(caseListMap, planMap)
//                .buildBaseData()
                .buildWithholdingStatus()
                .buildPlanIncome()
                .buildWithholdingIncome()
                .buildAchieveRatio()
                .buildPlanGrossProfitMargin()
                .buildWithholdingGrossProfitMargin()
                .grossProfitMarginDifference()
                .planCostRatio()
                .withholdingCostRatio()
                .costRatioDifference()
                .planProfitRatio()
                .withholdingProfitRatio()
                .profitRatioDifference();
        for (PreActualReportCollectVo vo : reportCollectVoList) {
            vo.setFeeCashDetailVoList(null);
            vo.setWithHoldingVoList(null);
            vo.setOrderDetailVos(null);
            vo.setPosDataMap(null);
            vo.setMaterialMap(null);
            vo.setMaterialTaxRateMap(null);
            vo.setMaterialCostMap(null);
            String jsonStr = JSONObject.toJSONString(vo);
            vo.setJsonStr(jsonStr);
        }
        List<PreActualReportCollect> dataList = JsonUtils.convert(reportCollectVoList, List.class, PreActualReportCollect.class);
        dataList.forEach(x -> {
            x.setTenantCode(TenantUtils.getTenantCode());
            x.setId(null);
        });
        Set<String> actYears = dataList.stream().map(x->x.getActYears()).collect(Collectors.toSet());
        preActualReportCollectService.lambdaUpdate()
                .in(PreActualReportCollect::getActYears, actYears)
                .remove();
        preActualReportCollectService.saveBatch(dataList);
    }
}
