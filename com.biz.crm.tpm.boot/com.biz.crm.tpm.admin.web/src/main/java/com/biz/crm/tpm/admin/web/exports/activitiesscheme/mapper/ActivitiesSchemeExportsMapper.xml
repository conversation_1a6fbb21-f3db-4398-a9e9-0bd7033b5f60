<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.activitiesscheme.mapper.ActivitiesSchemeExportsMapper">

  <sql id="conditions">
    <if test="dto.tenantCode != null and dto.tenantCode != '' ">
      and t.tenant_code = #{dto.tenantCode}
    </if>
    <if test="dto.enableStatus != null and dto.enableStatus != '' ">
      and t.enable_status = #{dto.enableStatus}
    </if>
    <if test="dto.processStatus !=null and dto.processStatus != '' ">
      and bpbm.process_status = #{dto.processStatus}
    </if>
    <if test="dto.processNumber !=null and dto.processNumber != '' ">
      and t.process_number = #{dto.processNumber}
    </if>
    <if test="dto.activitiesCode !=null and dto.activitiesCode != '' ">
      and t.activities_code = #{dto.activitiesCode}
    </if>
    <if test="dto.activitiesName !=null and dto.activitiesName != '' ">
      and t.activities_name like concat('%',#{dto.activitiesName},'%')
    </if>
    <if test="dto.status !=null and dto.status != '' ">
      and t.status = #{dto.status}
    </if>
    and t.del_flag = '${@<EMAIL>()}'
  </sql>

  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from tpm_activities_scheme t
    left join bpm_process_business_mapping bpbm on (t.activities_code = bpbm.business_no and bpbm.business_code = '${@com.biz.crm.tpm.business.activities.scheme.constant.ActivitiesSchemeConstant@PROCESS_NAME}')
    <where>
      <include refid="conditions"/>
    </where>
  </select>

  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.activitiesscheme.model.ActivitiesSchemeExportsVo">
    select distinct t.*,bpbm.process_status processStatus
    from tpm_activities_scheme t
    left join bpm_process_business_mapping bpbm on (t.activities_code = bpbm.business_no and bpbm.business_code = '${@com.biz.crm.tpm.business.activities.scheme.constant.ActivitiesSchemeConstant@PROCESS_NAME}')
    <where>
      <include refid="conditions" />
    </where>
    order by t.create_time desc, t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>