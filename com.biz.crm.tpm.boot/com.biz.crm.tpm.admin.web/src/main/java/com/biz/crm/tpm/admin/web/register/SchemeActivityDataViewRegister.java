package com.biz.crm.tpm.admin.web.register;

import com.biz.crm.tpm.business.activities.scheme.constant.ActivitiesSchemeConstant;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>方案活动数据视图注册器
 * 基于nebula的数据视图提供方案活动列表查询功能
 *
 * <AUTHOR>
 * @date 2022/8/15
 */
@Component
@Slf4j
public class SchemeActivityDataViewRegister implements DataviewRegister {
  @Override
  public String code() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String desc() {
    return "TPM-方案活动";
  }

  @Override
  public String buildSql() {
    return "select distinct tas.*, bpbm.process_status, bpbm.process_no " +
        "from tpm_activities_scheme tas " +
        "left join bpm_process_business_mapping bpbm on (tas.activities_code = bpbm.business_no and bpbm.business_code = '" + ActivitiesSchemeConstant.PROCESS_NAME + "') " +
        "where tas.tenant_code = :tenantCode " +
        "and tas.del_flag = '009' ";
  }
}
