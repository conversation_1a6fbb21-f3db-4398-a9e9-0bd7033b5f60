package com.biz.crm.tpm.admin.web.exports.dataview.auth;

import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.bizunited.nebula.event.sdk.service.NebulaNonWebHttpHeaderStrategy;
import com.bizunited.nebula.security.local.utils.JwtUtils;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

/**
 * 描述：</br> 完美解决导入导出解决子线程中调用微服务的问题
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
@Component
public class ImportExportNebulaNonWebHttpHeaderStrategy implements NebulaNonWebHttpHeaderStrategy {
  @Autowired
  private SimpleSecurityProperties simpleSecurityProperties;
  @Autowired(required = false)
  private LoginUserService loginUserService;

  @Override
  public void fill(HttpHeaders httpHeaders) {
    UserIdentity userIdentity = loginUserService.getLoginUser();
    String jwtContent = JwtUtils.encode(userIdentity, 0, simpleSecurityProperties.getSecretKey());
    httpHeaders.add("jwt", jwtContent);
  }
}