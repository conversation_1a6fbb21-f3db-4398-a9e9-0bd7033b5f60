package com.biz.crm.tpm.admin.web.exports.headover.mapper;

import com.biz.crm.tpm.admin.web.exports.headover.model.HeadOverallPlanReportExportsDto;
import com.biz.crm.tpm.admin.web.exports.headover.model.HeadOverallPlanReportExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HeadOverallPlanReportExportsMapper {


    /**
     * 获取匹配的数据总量
     *
     * @param dto
     * @return
     */
    Integer getExportTotal(@Param("vo") HeadOverallPlanReportExportsDto dto);

    /**
     * 获取导出数据
     *
     * @param dto
     * @return
     */
    List<HeadOverallPlanReportExportsVo> findData(@Param("vo") HeadOverallPlanReportExportsDto dto);
}
