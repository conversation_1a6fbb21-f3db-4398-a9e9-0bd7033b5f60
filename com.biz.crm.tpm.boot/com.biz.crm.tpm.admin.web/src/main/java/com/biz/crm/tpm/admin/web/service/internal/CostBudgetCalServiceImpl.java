package com.biz.crm.tpm.admin.web.service.internal;

import com.biz.crm.business.common.base.service.CostBudgetCalService;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanCaseMapper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.TpmCostBudgetVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/2 15:45
 */
@Service
public class CostBudgetCalServiceImpl implements CostBudgetCalService {


    @Resource
    private MarketingPlanCaseMapper marketingPlanCaseMapper;

    @Resource
    private MarketingPlanCaseService marketingPlanCaseService;

    @Async("tpmRegionCollectThread")
    @Override
    public Future<Map<String, BigDecimal>> calCostBudgetAmount(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new AsyncResult<>(Maps.newHashMap());
        }
        List<TpmCostBudgetVo> applyAmountList = marketingPlanCaseMapper.findMarketingPlanApplyAmount(codes);
        List<TpmCostBudgetVo> changeCommitApplyAmountList = marketingPlanCaseMapper.findMarketingPlanChangeCommitApplyAmount(codes);
        if (CollectionUtils.isNotEmpty(changeCommitApplyAmountList)){
            if (CollectionUtils.isEmpty(applyAmountList)){
                applyAmountList = Lists.newArrayList();
            }
            applyAmountList.addAll(changeCommitApplyAmountList);
        }
        List<String> schemeDetailCodes = Lists.newArrayList();
        List<TpmCostBudgetVo> handleWithholdingList = marketingPlanCaseMapper.findHandleWithholdingAmount(codes);
        List<TpmCostBudgetVo> withholdingList = marketingPlanCaseMapper.findWithholdingAmount(codes);
        List<TpmCostBudgetVo> endCaseList = marketingPlanCaseMapper.findEndCaseAmount(codes);
        Map<String, TpmCostBudgetVo> schemeApplyMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(applyAmountList)) {
            //此处过滤随单搭赠重复纬度的预算费用
            schemeDetailCodes = applyAmountList.stream().filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                    .map(x -> x.getBusinessDetailCode()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(schemeDetailCodes)) {
                List<String> excludeSchemeDetailCodes = this.repeatMatchingGift(schemeDetailCodes);
                for (TpmCostBudgetVo vo : applyAmountList) {
                    if (excludeSchemeDetailCodes.contains(vo.getBusinessDetailCode())) {
                        vo.setApplyAmount(BigDecimal.ZERO);
                    }
                }
//                applyAmountList = applyAmountList.stream().filter(x -> !excludeSchemeDetailCodes.contains(x.getBusinessDetailCode())).collect(Collectors.toList());
            }
            schemeApplyMap = applyAmountList.stream().collect(Collectors.toMap(TpmCostBudgetVo::getBusinessDetailCode, v -> v));
        }
        Map<String, BigDecimal> withholdingAmountMap = withholdingList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBusinessDetailCode()))
                .collect(Collectors.groupingBy(TpmCostBudgetVo::getBusinessDetailCode,
                        Collectors.mapping(TpmCostBudgetVo::getWithholdingAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //过滤
        Map<String, BigDecimal> endCaseAmountMap = endCaseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBusinessDetailCode()) &&
                        x.getEndCaseAmount().compareTo(BigDecimal.ZERO) > -1)
                .collect(Collectors.groupingBy(TpmCostBudgetVo::getBusinessDetailCode,
                        Collectors.mapping(TpmCostBudgetVo::getEndCaseAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, BigDecimal> costAmountMap = Maps.newHashMap();
        for (Map.Entry<String, TpmCostBudgetVo> entry : schemeApplyMap.entrySet()) {
            String businessDetailCode = entry.getKey();
            TpmCostBudgetVo costBudgetVo = entry.getValue();
            BigDecimal lastAmount = costBudgetVo.getApplyAmount();
            if (withholdingAmountMap.containsKey(businessDetailCode)) {
                lastAmount = withholdingAmountMap.get(businessDetailCode);
            }
            if (endCaseAmountMap.containsKey(businessDetailCode)) {
                lastAmount = endCaseAmountMap.get(businessDetailCode);
            }
            if (costAmountMap.containsKey(costBudgetVo.getCode())) {
                lastAmount = lastAmount.add(costAmountMap.get(costBudgetVo.getCode()));
            }
            costAmountMap.put(costBudgetVo.getCode(), lastAmount);
        }
        //手动预提
        for (TpmCostBudgetVo vo : handleWithholdingList) {
            if (ObjectUtils.isEmpty(vo.getBusinessDetailCode())) {
                BigDecimal amount = costAmountMap.getOrDefault(vo.getCode(), BigDecimal.ZERO);
                amount = amount.add(vo.getWithholdingAmount());
                costAmountMap.put(vo.getCode(), amount);
            }
        }

        Map<String, BigDecimal> map = Maps.newHashMap();
        for (String code : codes) {
            BigDecimal lastAmount = costAmountMap.getOrDefault(code, BigDecimal.ZERO);
            map.put(code, lastAmount);
        }
        return new AsyncResult<>(map);
    }


    private List<String> repeatMatchingGift(List<String> schemeDetailCode) {
        List<MarketingPlanCaseVo> caseList = marketingPlanCaseService.findByCaseCodes(schemeDetailCode);
        for (MarketingPlanCaseVo caseVo : caseList) {
            if (ObjectUtils.isEmpty(caseVo.getIsContractCost())) {
                caseVo.setIsContractCost(BooleanEnum.FALSE.getCapital());
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                caseVo.setLevelProductCode(caseVo.getProductList().get(0).getCode());
            } else if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(caseVo.getLevelList())) {
                caseVo.setLevelProductCode(caseVo.getLevelList().get(0).getCode());
            }
        }
        // 过滤并按客户代码、归属部门、产品代码进行分组
        Map<String, List<MarketingPlanCaseVo>> filterNewMatchingCaseMap = caseList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()) &&
                        ObjectUtils.isNotEmpty(x.getCustomerCode()) &&
                        ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .collect(Collectors.groupingBy(x -> x.getCustomerCode() + x.getBelongDepartmentCode() + x.getLevelProductCode() + x.getIsContractCost() + x.getYears()));

        // 从每个分组中选择结束日期最大的对象
        List<MarketingPlanCaseVo> filterNewMatchingList = filterNewMatchingCaseMap.values().stream()
                .map(cases -> cases.stream().max(Comparator.comparing(MarketingPlanCaseVo::getEndDate)))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
        List<String> excludeSchemeDetailCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(filterNewMatchingList)) {
            List<String> calSchemeDetailCodes = filterNewMatchingList.stream().map(x -> x.getSchemeDetailCode()).collect(Collectors.toList());
            schemeDetailCode.removeAll(calSchemeDetailCodes);
            excludeSchemeDetailCodes = schemeDetailCode;
        }
        return excludeSchemeDetailCodes;
    }
}
