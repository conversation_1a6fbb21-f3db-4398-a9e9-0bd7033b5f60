package com.biz.crm.tpm.admin.web.controller.marketingplan;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.service.internal.MarketingPlanReportServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/5 14:31
 */
@Slf4j
@RestController
@RequestMapping("/v1/marketingPlanCaseReportController")
@Api(tags = "活动明细报表")
public class MarketingPlanCaseReportController {


    @Autowired
    private MarketingPlanReportServiceImpl marketingPlanReportService;

    @ApiOperation(value = "活动明细报表")
    @GetMapping("findMarketingPlanCaseReportList")
    public Result<Page<MarketingPlanCaseVo>> findMarketingPlanCaseReportList(@PageableDefault(50) Pageable pageable, MarketingPlanCaseVo vo) {
        return Result.ok(marketingPlanReportService.findMarketingPlanCaseReportList(pageable, vo));
    }

    @ApiOperation(value = "活动明细推送Dms")
    @PostMapping("pushDms")
    public Result<?> pushDms(@RequestBody List<String> schemeDetailCodes) {
        marketingPlanReportService.pushDms(schemeDetailCodes);
        return Result.ok();
    }


    @GetMapping("findMiniCostTotalView")
    @ApiOperation(value = "查询小程序的费用总览-客户")
    public Result<List<MarketingPlanCaseVo>> findMiniCostTotalView(MarketingPlanCaseVo vo) {
        return Result.ok(marketingPlanReportService.findMiniCostTotalView(vo));
    }

    @GetMapping("/countTerminalNum")
    @ApiOperation("统计终端数量")
    public Result<List<String>> countTerminalNum(@RequestParam("years") String years){
        return Result.ok(this.marketingPlanReportService.countTerminalNum(years));
    }

    @ApiOperation(value = "SFA移动访销费用投入情况-NEW")
    @GetMapping("findExpensesCountByConditions")
    public Result<Page<MarketingPlanCaseVo>> findExpensesCountByConditions(@PageableDefault(50) Pageable pageable, MarketingPlanCaseQueryDto vo) {
        return Result.ok(marketingPlanReportService.findExpensesCountByConditions(pageable, vo));
    }
}
