package com.biz.crm.tpm.admin.web.controller.orderstatistics;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.dms.business.order.sdk.OrderReachStatisticsReportDto;
import com.biz.crm.dms.business.order.sdk.dto.TpmToDmsOrderStatisticDto;
import com.biz.crm.dms.business.order.sdk.vo.OrderReachStatisticsReportSumVo;
import com.biz.crm.tpm.admin.web.service.TpmToDmsOrderStatisticsServiceImpl;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/16 14:55
 */
@RestController
@RequestMapping("/v1/tpmToDmsOrderStatisticsController")
@Slf4j
public class TpmToDmsOrderStatisticsController {


    @Autowired(required = false)
    private TpmToDmsOrderStatisticsServiceImpl tpmToDmsOrderStatisticsService;


    @ApiOperation(value = "计算订单统计年月")
    @GetMapping("calOrderStatisticsByYears")
    public Result calOrderStatisticsByYears(@RequestParam(required = false) String years) {
        if (ObjectUtils.isEmpty(years)) {
            years = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        }
        tpmToDmsOrderStatisticsService.calOrderStatistics(years);
        return Result.ok();
    }


    @ApiOperation(value = "定时任务计算订单达成统计年月")
    @GetMapping("scheduleOrderStatisticsByYears")
    public Result scheduleOrderStatisticsByYears() {
        String years = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        tpmToDmsOrderStatisticsService.calOrderStatistics(years);
        return Result.ok();
    }


    @ApiOperation(value = "查询订单统计报表")
    @GetMapping("findByCondition")
    public Result<Page<TpmToDmsOrderStatisticDto>> findByCondition(@PageableDefault Pageable pageable, OrderReachStatisticsReportDto dto) {
        return Result.ok(tpmToDmsOrderStatisticsService.findByCondition(pageable, dto));
    }


    /**
     * 根据条件分页汇总
     */
    @ApiOperation(value = "根据条件分页汇总")
    @GetMapping("sumByConditions")
    public Result<OrderReachStatisticsReportSumVo> sumByConditions(OrderReachStatisticsReportDto dto) {
        return Result.ok(tpmToDmsOrderStatisticsService.sumByConditions(dto));
    }
}
