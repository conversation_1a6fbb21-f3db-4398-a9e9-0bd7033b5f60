package com.biz.crm.tpm.admin.web.exports.creditorder.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 【贷项订单台账】导出明细
 *
 * <AUTHOR>
 */
@CrmExcelExport
@Data
public class CreditOrderTicketExportsVo extends CrmExcelVo {

    @ApiModelProperty(notes = "贷项订单编号", value = "贷项订单编号")
    @CrmExcelColumn("贷项订单编号")
    private String creditCode;

    @ApiModelProperty(notes = "兑付编号", value = "兑付编号")
    @CrmExcelColumn("兑付编号")
    private String cashCode;

    @ApiModelProperty(notes = "订单类型", value = "订单类型")
    @CrmExcelColumn("订单类型")
    private String orderType;

    @ApiModelProperty(notes = "批次号", value = "批次号")
    @CrmExcelColumn("批次号")
    private String btNo;

    @ApiModelProperty(notes = "公司编码", value = "公司编码")
    @CrmExcelColumn("公司编码")
    private String companyCode;

    @ApiModelProperty(notes = "一级部门编码", value = "一级部门编码")
    @CrmExcelColumn("一级部门编码")
    private String departmentOnCode;

    @ApiModelProperty(notes = "一级部门名称", value = "一级部门名称")
    @CrmExcelColumn("一级部门名称")
    private String departmentOneName;

    @ApiModelProperty(notes = "二级部门编码", value = "二级部门编码")
    @CrmExcelColumn("二级部门编码")
    private String departmentTwoCode;

    @ApiModelProperty(notes = "二级部门名称", value = "二级部门名称")
    @CrmExcelColumn("二级部门名称")
    private String departmentTwoName;

    @ApiModelProperty(notes = "客户名称", value = "客户名称")
    @CrmExcelColumn("客户名称")
    private String customerName;

    @ApiModelProperty(notes = "凭证号", value = "凭证号")
    @CrmExcelColumn("凭证号")
    private String voucherCode;

    @ApiModelProperty(notes = "接口返回信息", value = "接口返回信息")
    @CrmExcelColumn("接口返回信息")
    private String failMsg;

    @ApiModelProperty(notes = "客户编码", value = "客户编码")
    @CrmExcelColumn("客户编码")
    private String customerCode;

/*    @ApiModelProperty(notes = "预提类型", value = "预提类型")
    @CrmExcelColumn("预提类型")
    private String pushStatus;*/

    @ApiModelProperty(notes = "上账日期", value = "上账日期")
    @CrmExcelColumn("上账日期")
    private String accountDate;

    @ApiModelProperty(notes = "金额", value = "金额")
    @CrmExcelColumn("金额")
    private String amount;

    @ApiModelProperty(notes = "结案编码", value = "结案编码")
    @CrmExcelColumn("结案编码")
    private String auditCode;


    // ====
    @ApiModelProperty(notes = "行号", value = "行号")
    @CrmExcelColumn("行号")
    private String lineNumber;


    @ApiModelProperty(notes = "产品编码", value = "产品编码")
    @CrmExcelColumn("产品编码")
    private String productCode;


    @ApiModelProperty(notes = "产品名称", value = "产品名称")
    @CrmExcelColumn("产品名称")
    private String productName;


    @ApiModelProperty(notes = "单位", value = "单位")
    @CrmExcelColumn("单位")
    private String saleUnit;

    @ApiModelProperty("订单数量")
    @CrmExcelColumn("订单数量")
    private String quantity;


    @ApiModelProperty("订单单价")
    @CrmExcelColumn("订单单价")
    private String price;

    @ApiModelProperty("订单金额")
    @CrmExcelColumn("订单金额")
    private String orderAmount;

    @ApiModelProperty("活动归属年月")
    @CrmExcelColumn("活动归属年月")
    private String years;

    @ApiModelProperty("单次折让率")
    @CrmExcelColumn("单次折让率")
    private String singleDiscountsRateStr;

    @ApiModelProperty("累计折让率")
    @CrmExcelColumn("累计折让率")
    private String cumulativeDiscountsRateStr;

    @ApiModelProperty("未开票订单金额")
    @CrmExcelColumn("未开票订单金额")
    private String unInvoicedAmount;

    @ApiModelProperty("开票状态")
    @CrmExcelColumn("开票状态")
    private String ticketStatus;

    @ApiModelProperty("未开票费用金额")
    @CrmExcelColumn("未开票费用金额")
    private String unTicketAmount;

  /*

    @ApiModelProperty(notes = "创建时间", value = "创建时间")
    @CrmExcelColumn("创建时间")
    private String createTime;

    @ApiModelProperty(notes = "渠道编码", value = "渠道编码")
    @CrmExcelColumn("渠道编码")
    private String channelCode;

    @ApiModelProperty(notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
    @CrmExcelColumn("数据业务状态（启用状态）")
    private String enableStatus;

  @ApiModelProperty(notes = "数据状态（删除状态", value = "数据状态（删除状态")
    @CrmExcelColumn("数据状态（删除状态")
    private String delFlag;


    @ApiModelProperty(notes = "客户ERP编码", value = "客户ERP编码")
    @CrmExcelColumn("客户ERP编码")
    private String erpCode;

    @ApiModelProperty(notes = "创建人名称", value = "创建人名称")
    @CrmExcelColumn("创建人名称")
    private String createName;


    @ApiModelProperty(notes = "核销申请名称", value = "核销申请名称")
    @CrmExcelColumn("核销申请名称")
    private String auditName;

    @ApiModelProperty(notes = "修改人账号", value = "修改人账号")
    @CrmExcelColumn("修改人账号")
    private String modifyAccount;

    @ApiModelProperty(notes = "创建人账号", value = "创建人账号")
    @CrmExcelColumn("创建人账号")
    private String createAccount;

    @ApiModelProperty(notes = "租户编号", value = "租户编号")
    @CrmExcelColumn("租户编号")
    private String tenantCode;

    @ApiModelProperty(notes = "审批状态", value = "审批状态")
    @CrmExcelColumn("审批状态")
    private String status;

    @ApiModelProperty(notes = "修改时间", value = "修改时间")
    @CrmExcelColumn("修改时间")
    private String modifyTime;


    @ApiModelProperty(notes = "备注", value = "备注")
    @CrmExcelColumn("备注")
    private String remark;*/


}
