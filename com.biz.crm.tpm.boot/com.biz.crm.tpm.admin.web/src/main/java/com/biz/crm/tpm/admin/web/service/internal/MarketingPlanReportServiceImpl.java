package com.biz.crm.tpm.admin.web.service.internal;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.dms.business.warehouse.sdk.dto.DmsOrderStatisticsReportDto;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsOrderStatisticsReportService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsOrderStatisticsReportVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.tpm.business.activities.marketingplan.dto.CustomerMonthCategoryApplyAmountDTO;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.TpmMarketingPlanCaseComponent;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.pay.local.repository.ManageReportRepository;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.service.DeliveryReplenishmentPoolDetailService;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashService;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/5 14:32
 */
@Service
@Slf4j
public class MarketingPlanReportServiceImpl {


    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired(required = false)
    private WithHoldingService withHoldingService;

    @Autowired(required = false)
    private TpmMarketingPlanCaseComponent tpmMarketingPlanCaseComponent;

    @Autowired(required = false)
    private MarketingPlanRepository marketingPlanRepository;

    @Autowired(required = false)
    private FeeCashService feeCashService;

    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;

    @Autowired(required = false)
    private DeliveryReplenishmentPoolDetailService deliveryReplenishmentPoolDetailService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Autowired
    private MarketingPlanCaseRepository planCaseRepository;

    @Autowired
    private DmsOrderStatisticsReportService dmsOrderStatisticsReportService;

    @Autowired
    private MarketingPlanCaseRepository marketingPlanCaseRepository;

    @Autowired
    private ManageReportRepository manageReportRepository;

    /**
     * 查询活动明细报表
     *
     * @param pageable
     * @param vo
     * @return
     */
    public Page<MarketingPlanCaseVo> findMarketingPlanCaseReportList(Pageable pageable, MarketingPlanCaseVo vo) {
        return withHoldingService.findMarketingPlanCaseReportListV1(pageable, vo);
    }

    public List<MarketingPlanCaseVo> findMiniCostTotalView(MarketingPlanCaseVo vo){
        return marketingPlanCaseService.findMiniCostTotalView(vo);
    }


    @Transactional(rollbackFor = Exception.class)
    public void pushDms(List<String> schemeDetailCodes) {
        // 根据活动明细编码查询明细
        List<MarketingPlanCase> detailList =
                this.marketingPlanCaseService.findListBySchemeDetailCodes(schemeDetailCodes);
        List<MarketingPlanCase> suitableList = detailList.stream().filter(v -> StringUtils.equalsAny(v.getCaseType(),
                MarketingPlanCaseTypeEnum.matching_gift.getCode(), MarketingPlanCaseTypeEnum.material.getCode())
                && (StringUtils.equals(BooleanEnum.FALSE.getCapital(), v.getPushStatus()) || ObjectUtils.isEmpty(v.getPushStatus()))).collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isNotEmpty(suitableList), "无可推送DMS的数据");
        Map<String, List<MarketingPlanCase>> group = suitableList.stream().collect(Collectors.groupingBy(MarketingPlanCase::getSchemeCode));
        // 查询活动
        List<MarketingPlan> marketingPlans = this.marketingPlanRepository.queryBySchemeCodes(group.keySet());
        Map<String, MarketingPlan> marketingPlanMap =
                marketingPlans.stream().collect(Collectors.toMap(MarketingPlan::getSchemeCode, Function.identity()));
        group.forEach((k, v) -> this.tpmMarketingPlanCaseComponent.operate(null, marketingPlanMap.get(k), v));

    }

    public List<String> countTerminalNum(String years) {
        return this.marketingPlanRepository.countTerminalNum(years);
    }

    public Page<MarketingPlanCaseVo> findExpensesCountByConditions(Pageable pageable, MarketingPlanCaseQueryDto vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (StringUtils.isNotEmpty(vo.getStartEndDate())) {
            vo.setStartDate(vo.getStartEndDate());
            vo.setEndDate(vo.getEndDate());
        }
        if (StringUtils.isAnyBlank(vo.getStartDate(), vo.getEndDate())) {
            //日期过滤条件不满足条件时默认查询本月数据
            String years = DateUtil.dateToStr(new Date(), DateUtil.date_yyyy_MM);
            vo.setStartDate(years);
            vo.setEndDate(years);
        }
        //  费用金额取 公司 + 客户+ 年月 + 审核状态为3 ，规划类型为：规划（o-two-o） ；
        //  判断开始结束时间是否为当月，
        //  当月取实际收入：发货单-退货单 表字段 当月该客户的发货单金额 - 当月该客户的退货单金额（包含一件代发）【dms_order_statistics_report】
        //  往月总收入取管报金额 tpm_manage_report
        Page<MarketingPlanCaseVo> expensesReportPage = this.planCaseRepository.findExpensesCountByConditions(pageable, vo);
        if (expensesReportPage.getTotal() > 0) {
            List<MarketingPlanCaseVo> records = expensesReportPage.getRecords();
            List<String> customerCodes = records.stream().map(MarketingPlanCaseVo::getCustomerCode).distinct().collect(Collectors.toList());
            YearMonth currentMonth = YearMonth.now();
            YearMonth startEndMonth = YearMonth.parse(vo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            // 取费用金额
            List<CustomerMonthCategoryApplyAmountDTO> amountDTOList = marketingPlanCaseRepository.sumApplyAmountByCustomerCodesYearMonth(customerCodes, startEndMonth.toString());
            Map<String, BigDecimal> applyAmountMap = amountDTOList.stream().collect(Collectors.toMap(e -> e.getCustomerCode() + "-" + e.getCategoryCode(), CustomerMonthCategoryApplyAmountDTO::getTotalApplyAmount, (v1, v2) -> v1));
            Map<String, BigDecimal> totalIncomeMap = Maps.newHashMap();
            if (startEndMonth.equals(currentMonth)) {
                DmsOrderStatisticsReportDto reportDto = new DmsOrderStatisticsReportDto();
                reportDto.setCustomerCodes(customerCodes);
                reportDto.setStatisticsDateStart(startEndMonth.atDay(1).toString());
                reportDto.setStatisticsDateEnd(startEndMonth.atEndOfMonth().toString());
                log.info("orderStatisticsReportDto: {}", JSON.toJSONString(reportDto));
                List<DmsOrderStatisticsReportVo> orderStatisticsReportVoList = dmsOrderStatisticsReportService.findBySearchDto(reportDto);
                Map<String, List<DmsOrderStatisticsReportVo>> customerOrderStatisticsMap = orderStatisticsReportVoList.stream().collect(Collectors.groupingBy(DmsOrderStatisticsReportVo::getCustomerCode));
                for (Map.Entry<String, List<DmsOrderStatisticsReportVo>> entry : customerOrderStatisticsMap.entrySet()) {
                    BigDecimal totalIncome = entry.getValue().stream().map(DmsOrderStatisticsReportVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalIncomeMap.putIfAbsent(entry.getKey(), totalIncome);
                }
            } else {
                // 往月取管报金额
                log.info("取管报金额");
                List<ManageReportVo> manageReportVoList = manageReportRepository.findByYearAndCustomerCodes(startEndMonth.toString(), customerCodes);
                Map<String, List<ManageReportVo>> manageReportMap = manageReportVoList.stream().collect(Collectors.groupingBy(ManageReportVo::getCustomerCode));
                for (Map.Entry<String, List<ManageReportVo>> entry : manageReportMap.entrySet()) {
                    BigDecimal totalIncome = entry.getValue().stream().map(ManageReportVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalIncomeMap.putIfAbsent(entry.getKey(), totalIncome);
                }
            }
            log.info("费用金额:{}", applyAmountMap);
            log.info("收入金额:{}", totalIncomeMap);
            records.forEach(ex -> {
                String customerCode = ex.getCustomerCode();
                String categoryCode = ex.getCategoryCode();
                BigDecimal totalCategoryApplyAmount = applyAmountMap.getOrDefault(customerCode + "-" + categoryCode, BigDecimal.ZERO);
                BigDecimal totalIncome = totalIncomeMap.getOrDefault(customerCode, BigDecimal.ZERO);
                if (totalIncome.compareTo(BigDecimal.ZERO) == 0) {
                    ex.setRate(new BigDecimal(0));
                } else {
                    ex.setRate(totalCategoryApplyAmount.divide(totalIncome, 4, BigDecimal.ROUND_HALF_UP));
                }
            });
            expensesReportPage.setRecords(records);
        }
        return expensesReportPage;
    }
}
