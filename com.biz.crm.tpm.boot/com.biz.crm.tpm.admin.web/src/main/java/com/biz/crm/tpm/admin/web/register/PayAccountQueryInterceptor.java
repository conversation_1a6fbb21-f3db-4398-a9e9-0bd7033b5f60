package com.biz.crm.tpm.admin.web.register;

import com.biz.crm.tpm.business.pay.local.entity.AccountProduct;
import com.biz.crm.tpm.business.pay.local.service.AccountProductService;
import com.bizunited.nebula.europa.database.sdk.strategy.ExternalQueryInterceptor;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteContent;
import com.bizunited.nebula.europa.sdk.context.matedata.MetaData;
import com.bizunited.nebula.europa.sdk.vo.EuropaInfoVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 费用上账数据视图补充
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Component
public class PayAccountQueryInterceptor implements ExternalQueryInterceptor {

  private static final String ACCOUNT_CODE = "account_code";

  @Autowired
  private AccountProductService accountProductService;

  @Override
  public String code() {
    return "tpm_pay_account_query_interceptor";
  }

  @Override
  public String name() {
    return "tpm费用上账-参数补充";
  }

  @Override
  public List<Object[]> process(EntityManager entityManager, MetaData metaData, EuropaInfoVo europaInfoVo
      , ExecuteContent executeContent, String... strings) {
    List<Map<String, Object>> results = executeContent.getResults();
    if (CollectionUtils.isEmpty(results)) {
      return Lists.newLinkedList();
    }
    if (0 == results.parallelStream().filter(row -> row != null).count()) {
      return Lists.newLinkedList();
    }
    List<Object[]> externalContents = Lists.newArrayList();
    Set<String> accountCodes = results.stream().filter(resultMap -> Objects.nonNull(resultMap.get(ACCOUNT_CODE)))
        .map(resultMap -> String.valueOf(resultMap.get(ACCOUNT_CODE)))
        .collect(Collectors.toSet());
    List<AccountProduct> products = this.accountProductService.findByCodes(Lists.newArrayList(accountCodes));
    Map<String, List<AccountProduct>> productMap = CollectionUtils.isEmpty(products) ? Maps.newHashMap()
        : products.stream().collect(Collectors.groupingBy(AccountProduct::getAccountCode));
    for (Map<String, Object> result : results) {
      List<Object> itemList = Lists.newArrayList();
      String accountCode = (String) result.get(ACCOUNT_CODE);
      List<AccountProduct> productList = productMap.get(accountCode);
      for (String externalFieldName : strings) {
        if (StringUtils.equals(externalFieldName, "product_names")) {
          itemList.add(CollectionUtils.isEmpty(productList) ? ""
              : String.join(",", productList.stream().map(AccountProduct::getProductName).collect(Collectors.toList())));
        }
      }
      externalContents.add(itemList.toArray(new Object[]{}));
    }
    return externalContents;
  }
}
