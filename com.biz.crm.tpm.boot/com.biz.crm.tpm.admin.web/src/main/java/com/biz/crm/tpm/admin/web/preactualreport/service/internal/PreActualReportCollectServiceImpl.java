package com.biz.crm.tpm.admin.web.preactualreport.service.internal;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.admin.web.preactualreport.entity.PreActualReportCollect;
import com.biz.crm.tpm.admin.web.preactualreport.mapper.PreActualReportCollectMapper;
import com.biz.crm.tpm.admin.web.preactualreport.service.PreActualReportCollectComponent;
import com.biz.crm.tpm.admin.web.preactualreport.service.PreActualReportCollectService;
import com.biz.crm.tpm.admin.web.vo.PreActualReportCollectVo;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.bizunited.nebula.common.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/20 17:25
 **/
@Service
@Slf4j
public class PreActualReportCollectServiceImpl extends ServiceImpl<PreActualReportCollectMapper, PreActualReportCollect> implements PreActualReportCollectService {

    @Resource
    private PreActualReportCollectMapper preActualReportCollectMapper;

    @Resource
    private MarketingPlanProductRepository productRepository;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Resource
    private MarketingPlanService marketingPlanService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private ApplicationContext context;

    /**
     * 计算预实汇总报表
     *
     * @param yearsList
     */
    @Override
    public void calPreActualReportCollect(List<String> yearsList) {
        List<MarketingPlanCaseVo> caseVoList = preActualReportCollectMapper.findCaseListByYears(yearsList);
        if (CollectionUtils.isEmpty(caseVoList)) {
            return;
        }
        List<String> schemeDetailCodes = caseVoList.stream().map(x -> x.getSchemeDetailCode()).distinct().collect(Collectors.toList());
        Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
        //构建参数
        checkHelper.buildMarketingPlanCase(caseVoList, productMap);
        List<String> schemeCodes = caseVoList.stream().map(x -> x.getSchemeCode()).distinct().collect(Collectors.toList());
        List<MarketingPlanVo> planVoList = marketingPlanService.findListBySchemeCodes(schemeCodes);
        Map<String, MarketingPlanVo> planMap = planVoList.stream().collect(Collectors.toMap(x -> x.getSchemeCode(), Function.identity()));
        List<String> orgCodes = caseVoList.stream().map(x -> x.getBelongDepartmentCode()).distinct().collect(Collectors.toList());
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
        List<String> businessOrgCodes = orgVoList.stream().filter(x -> OrgTypeEnum.REGION.getDictCode().equals(x.getOrgType()))
                .map(l -> l.getOrgCode())
                .collect(Collectors.toList());
        Map<String, List<MarketingPlanCaseVo>> caseListMap = Maps.newHashMap();
        for (String orgCode : businessOrgCodes) {
            List<MarketingPlanCaseVo> caseVos = Lists.newArrayList();
            for (MarketingPlanCaseVo caseVo : caseVoList) {
                if (caseVo.getBelongDepartmentCode().equals(orgCode)) {
                    caseVos.add(caseVo);
                }
            }
            if (!CollectionUtils.isEmpty(caseVos)) {
                caseListMap.put(orgCode, caseVos);
            }
        }
        List<String> excludeBusinessOrgCodes = orgVoList.stream().filter(x -> !OrgTypeEnum.REGION.getDictCode().equals(x.getOrgType()))
                .map(l -> l.getOrgCode())
                .collect(Collectors.toList());
        Map<String, List<OrgVo>> parentOrgMap = orgVoService.findAllParentByOrgCodesMap(excludeBusinessOrgCodes);
        for (Map.Entry<String, List<OrgVo>> entry : parentOrgMap.entrySet()) {
            Optional<OrgVo> optional = entry.getValue().stream().filter(x -> OrgTypeEnum.REGION.getDictCode().equals(x.getOrgType())).findFirst();
            if (optional.isPresent()) {
                OrgVo orgVo = optional.get();
                List<MarketingPlanCaseVo> caseVos = Lists.newArrayList();
                for (MarketingPlanCaseVo caseVo : caseVoList) {
                    if (caseVo.getBelongDepartmentCode().equals(entry.getKey())) {
                        caseVos.add(caseVo);
                    }
                }
                caseListMap.put(orgVo.getOrgCode(), caseVos);
            }
        }

        List<PreActualReportCollectVo> reportCollectVoList = Lists.newArrayList();
        //构建初始化参数
        List<String> caseOrgCodes = Lists.newArrayList(caseListMap.keySet());
        Map<String, List<OrgVo>> orgParentMap = orgVoService.findAllParentByOrgCodesMap(caseOrgCodes);

        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : caseListMap.entrySet()) {
            Map<String, OrgVo> orgVoMap = orgParentMap.get(entry.getKey()).stream().collect(Collectors.toMap(x -> x.getOrgCode(), Function.identity()));
            Optional<OrgVo> centerOptional = orgParentMap.get(entry.getKey()).stream().filter(x -> OrgTypeEnum.COMPANY.getDictCode().equals(x.getOrgType())).findFirst();
            Optional<OrgVo> divisionOptional = orgParentMap.get(entry.getKey()).stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType())).findFirst();
            Map<String, List<MarketingPlanCaseVo>> groupCaseListMap = entry.getValue().stream().collect(Collectors.groupingBy(x -> x.getSchemeCode() + x.getActYears() + x.getCustomerCode()));
            for (Map.Entry<String, List<MarketingPlanCaseVo>> listEntry : groupCaseListMap.entrySet()) {
                MarketingPlanCaseVo caseVo = listEntry.getValue().get(0);
                MarketingPlanVo planVo = planMap.get(caseVo.getSchemeCode());
                PreActualReportCollectVo reportCollectVo = JsonUtils.convert(caseVo, PreActualReportCollectVo.class);
                reportCollectVo.setApplyAccount(planVo.getCreateAccount());
                reportCollectVo.setApplyName(planVo.getCreateName());
                reportCollectVo.setProcessStatus(planVo.getProcessStatus());
                reportCollectVo.setOrgCode(entry.getKey());
                reportCollectVo.setOrgName(orgVoMap.get(entry.getKey()).getOrgName());
                if (centerOptional.isPresent()) {
                    OrgVo centerOrg = centerOptional.get();
                    reportCollectVo.setCenterCode(centerOrg.getOrgCode());
                    reportCollectVo.setCenterName(centerOrg.getOrgName());
                }
                if (divisionOptional.isPresent()) {
                    OrgVo divisionOrg = divisionOptional.get();
                    reportCollectVo.setRegionCode(divisionOrg.getOrgCode());
                    reportCollectVo.setRegionName(divisionOrg.getOrgName());
                }
                reportCollectVoList.add(reportCollectVo);
            }
        }
        for (Map.Entry<String, List<PreActualReportCollectVo>> entry : reportCollectVoList.stream().collect(Collectors.groupingBy(x -> x.getActYears())).entrySet()) {
            PreActualReportCollectComponent component = ApplicationContextHolder.getContext().getBean(PreActualReportCollectComponent.class);
            component.syncPreActualReportCollect(entry.getValue(),caseListMap,planMap);
        }
    }
}
