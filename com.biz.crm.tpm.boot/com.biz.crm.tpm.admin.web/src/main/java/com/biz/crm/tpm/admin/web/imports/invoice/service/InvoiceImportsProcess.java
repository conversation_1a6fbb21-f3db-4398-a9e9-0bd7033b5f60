package com.biz.crm.tpm.admin.web.imports.invoice.service;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.imports.invoice.model.InvoiceImportsVo;
import com.biz.crm.tpm.business.pay.sdk.dto.InvoiceDto;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class InvoiceImportsProcess implements ImportProcess<InvoiceImportsVo> {

    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired
    private InvoiceService invoiceService;
    @Resource
    private DictDataVoService dictDataVoService;

    private static final String INVOICE_TYPE = "invoice_type";


    /**
     * 执行导入
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, InvoiceImportsVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(INVOICE_TYPE);
        Map<String, String> dictDataMap = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
        List<InvoiceImportsVo> importsVos = Lists.newArrayList();
        for (Map.Entry<Integer, InvoiceImportsVo> entry : data.entrySet()) {
            InvoiceImportsVo importsVo = entry.getValue();
            importsVo.setCount(entry.getKey());
            importsVos.add(importsVo);
        }
        Map<Integer, String> allErrMsg = Maps.newHashMap();
        for (InvoiceImportsVo importsVo : importsVos) {
            if (ObjectUtils.isEmpty(importsVo.getType())) {
                allErrMsg.put(importsVo.getCount(), "发票类型不能为空");
            } else {
                String invoiceType = dictDataMap.getOrDefault(importsVo.getType(), importsVo.getType());
                importsVo.setType(invoiceType);
            }
            if (ObjectUtils.isEmpty(importsVo.getInvoiceNo())) {
                allErrMsg.put(importsVo.getCount(), "发票号不能为空");
            }
            if (ObjectUtils.isEmpty(importsVo.getPriceAndTax())) {
                allErrMsg.put(importsVo.getCount(), "税价合计(票价)不能为空");
            }
            if (ObjectUtils.isEmpty(importsVo.getTaxAmount())) {
                allErrMsg.put(importsVo.getCount(), "税额不能为空");
            }
            if (ObjectUtils.isEmpty(importsVo.getAmountWithoutTax())) {
                allErrMsg.put(importsVo.getCount(), "不含税金额不能为空");
            }
        }
        Map<String, List<InvoiceImportsVo>> invoiceMap = importsVos.stream()
                .filter(x -> ObjectUtils.isNotEmpty(x.getInvoiceNo()) && ObjectUtils.isNotEmpty(x.getPriceAndTax()) &&
                        ObjectUtils.isNotEmpty(x.getTaxAmount()) && ObjectUtils.isNotEmpty(x.getAmountWithoutTax()))
                .collect(Collectors.groupingBy(InvoiceImportsVo::getInvoiceNo));
        List<InvoiceDto> dtoList = Lists.newArrayList();
        for (Map.Entry<String, List<InvoiceImportsVo>> entry : invoiceMap.entrySet()) {
            Map<Integer, String> errMsg = Maps.newHashMap();
            InvoiceImportsVo vo = entry.getValue().get(0);
            StringJoiner joiner = new StringJoiner(";");
            try {
                InvoiceDto dto = nebulaToolkitService.copyObjectByBlankList(vo, InvoiceDto.class, HashSet.class, ArrayList.class);
                BigDecimal priceAndTax = entry.getValue().stream().map(InvoiceImportsVo::getPriceAndTax)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal taxAmount = entry.getValue().stream().map(InvoiceImportsVo::getTaxAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal amountWithoutTax = entry.getValue().stream().map(InvoiceImportsVo::getAmountWithoutTax)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                dto.setPriceAndTax(priceAndTax);
                dto.setTaxAmount(taxAmount);
                dto.setAmountWithoutTax(amountWithoutTax);
                dtoList.add(dto);
                try {
                    if (ObjectUtils.isEmpty(joiner.toString())) {
                        invoiceService.create(dto);
                    } else {
                        for (InvoiceImportsVo importsVo : entry.getValue()) {
                            errMsg.put(importsVo.getCount(), joiner.toString());
                        }
                    }
                } catch (Exception e) {
                    log.error("错误信息:{}", e);
                    joiner.add(e.getMessage());
                    for (InvoiceImportsVo importsVo : entry.getValue()) {
                        errMsg.put(importsVo.getCount(), joiner.toString());
                    }
                }
            } catch (Exception e) {
                joiner.add("金额必须是数字:" + e.getMessage());
                for (InvoiceImportsVo importsVo : entry.getValue()) {
                    errMsg.put(importsVo.getCount(), joiner.toString());
                }
            }
            if (errMsg != null) {
                allErrMsg.putAll(errMsg);
            }
        }
        return allErrMsg;
    }

    @Override
    public Integer getBatchCount() {
        return 999999999;
    }

    @Override
    public Class<InvoiceImportsVo> findCrmExcelVoClass() {
        return InvoiceImportsVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "TPM_INVOICE_IMPORTS";
    }

    @Override
    public String getTemplateName() {
        return "TPM发票信息导入";
    }

}
