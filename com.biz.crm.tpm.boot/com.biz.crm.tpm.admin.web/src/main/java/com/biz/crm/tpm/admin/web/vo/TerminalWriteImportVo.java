package com.biz.crm.tpm.admin.web.vo;

import com.biz.crm.business.common.base.vo.ExportWriteMdmVo;
import com.biz.crm.mdm.business.terminal.sdk.vo.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/25 01:14
 */
@Data
public class TerminalWriteImportVo extends ExportWriteMdmVo {


    /**
     * 终端编码
     */
    @ApiModelProperty("终端编码")
    private String terminalCode;

    /**
     * 终端名称
     */
    @ApiModelProperty("终端名称")
    private String terminalName;

    /**
     * 渠道
     */
    @ApiModelProperty("渠道")
    private String channel;

    @ApiModelProperty("终端标签")
    private String tagDescription;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("所属系统")
    private String sourceSystem;

    @ApiModelProperty("系统&店名")
    private String systemAndName;

    @ApiModelProperty("业务员账号")
    private String userName;

    private String cusCode;

    private String cusName;

}
