package com.biz.crm.tpm.admin.web.exports.account.mapper;

import com.biz.crm.tpm.admin.web.exports.account.model.AccountExportsDto;
import com.biz.crm.tpm.admin.web.exports.account.model.AccountExportsVo;

import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 费用上账主表;(tpm_account)表导出功能数据库访问层
 * <AUTHOR> <PERSON>
 * @date : 2022-7-2
 */
public interface AccountExportsMapper{
  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") AccountExportsDto dto);
  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<AccountExportsVo> findData(@Param("dto") AccountExportsDto dto);
}