package com.biz.crm.tpm.admin.web.imports.marketingplan.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.ie.sdk.excel.util.BzExcelUtil;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanImportVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 15:56
 */
@Slf4j
public class TpmMarketingSalesPlanImportListener extends AnalysisEventListener {

    private List<MarketingSalesPlanImportVo> dataList = Lists.newArrayList();

    private Integer count = 2;

    @Override
    public void invoke(Object t, AnalysisContext analysisContext) {
        this.count++;
        log.info("导入第{}行数据{}", this.count, JSONObject.toJSONString(t));
        MarketingSalesPlanImportVo o = new MarketingSalesPlanImportVo();
        try {
            Map<Integer, Object> data = (Map)t;
            BzExcelUtil.setCrmExcelVoValue(o, data);
        }catch (Exception e){
            throw new RuntimeException("第" + this.count + "行数据导入失败:" + e.getMessage());
        }
        dataList.add(o);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    public List<MarketingSalesPlanImportVo> getList(){
        return this.dataList;
    }
}
