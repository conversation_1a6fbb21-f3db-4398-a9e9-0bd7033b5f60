package com.biz.crm.tpm.admin.web.controller.invoice;

import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.ie.local.BusinessExcelExportTemplateWriteUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.service.internal.DefaultFormulaServiceImpl;
import com.biz.crm.tpm.business.pay.sdk.service.InvoiceService;
import com.biz.crm.tpm.business.pay.sdk.vo.InvoiceInputStreamFileVo;
import com.bizunited.nebula.venus.sdk.vo.OrdinaryFileVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/4 21:50
 */
@RestController
@RequestMapping("/v1/pay/invoice")
public class InvoiceUploadFileController {

    @Autowired
    private InvoiceService invoiceService;

    @Resource
    private BusinessExcelExportTemplateWriteUtil writeUtil;

    @Autowired
    private DefaultFormulaServiceImpl defaultFormulaService;

    @ApiOperation(value = "发票识别")
    @PostMapping("uploadInvoiceFileIdentify")
    public Result uploadInvoiceFileIdentify(@RequestParam MultipartFile[] file) {
        List<InvoiceInputStreamFileVo> list = Lists.newArrayList();
        for (MultipartFile multipartFile : file) {
            InvoiceInputStreamFileVo fileVo = new InvoiceInputStreamFileVo();
            try {
                byte[] bytes = multipartFile.getBytes();
                String base64String = Base64.getEncoder().encodeToString(bytes);
                fileVo.setFileInputStream(base64String);
                OrdinaryFileVo ordinaryFileVo = writeUtil.venusFileUpload(multipartFile).get(0);
                String fileCode = ordinaryFileVo.getId();
                String fileUrl = ordinaryFileVo.getRelativeLocal() + "/" + ordinaryFileVo.getFileName();
                fileUrl = fileUrl.substring(1);
                String fileName = ordinaryFileVo.getOriginalFileName();
//                String fileCode = UuidCrmUtil.general();
                fileVo.setFileCode(fileCode);
                fileVo.setFileUrl(fileUrl);
                fileVo.setFileName(fileName);
                list.add(fileVo);
            } catch (Exception e) {
                throw new RuntimeException("转换base64失败");
            }
        }
        String errMsg = null;
        if (!CollectionUtils.isEmpty(list)) {
            errMsg = invoiceService.uploadInvoiceFileIdentify(list);
        }
        if (ObjectUtils.isEmpty(errMsg)){
            return Result.ok();
        }else {
            return Result.error(errMsg);
        }
    }

    @ApiOperation(value = "推送返利公式到OA")
    @GetMapping("pushFormulaToOa")
    public Result pushFormulaToOa() {
        defaultFormulaService.pushFormulaToOA();
        return Result.ok();
    }
}
