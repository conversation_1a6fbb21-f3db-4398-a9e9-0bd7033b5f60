package com.biz.crm.tpm.admin.web.exports.budgetsubjects.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.budgetsubjects.mapper.BudgetSubjectsExportsMapper;
import com.biz.crm.tpm.admin.web.exports.budgetsubjects.model.BudgetSubjectsExportsDto;
import com.biz.crm.tpm.admin.web.exports.budgetsubjects.model.BudgetSubjectsExportsVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @describe:预算科目信息导出
 * @createTime 2022年06月07日 15:02:00
 */
@Component
public class BudgetSubjectsExportsProcess implements ExportProcess<BudgetSubjectsExportsVo> {

  @Autowired
  private BudgetSubjectsExportsMapper budgetSubjectsExportsMapper;

  @Autowired
  private DictDataVoService dictDataVoService;

  /**
   * 预算科目类型字典编码
   */
  private static final String SUBJECTS_TYPE = "subjects_type";
  /**
   * 预算科目分组字典编码
   */
  private static final String SUBJECTS_GROUP = "subjects_group";

  /**
   * 获取总条数
   *
   * @param params
   * @return
   */
  @Override
  public Integer getTotal(Map<String, Object> params) {
    BudgetSubjectsExportsDto dto = this.findDataDto(params);
    dto.setTenantCode(TenantUtils.getTenantCode());
    return budgetSubjectsExportsMapper.getExportTotal(dto);
  }


  @Override
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    final BudgetSubjectsExportsDto dto = this.findDataDto(params);
    dto.setOffset(this.getPageSize() * vo.getPageNo());
    dto.setLimit(vo.getPageSize());
    dto.setTenantCode(TenantUtils.getTenantCode());
    List<BudgetSubjectsExportsVo> data = budgetSubjectsExportsMapper.findData(dto);
    //调整
    adjustData(data);
    return toJSONArray(data);
  }

  /**
   * 模板编码
   *
   * @return
   */
  @Override
  public String getBusinessCode() {
    return "TPM_BUDGET_SUBJECTS_EXPORT";
  }

  /**
   * 模板名称
   *
   * @return
   */
  @Override
  public String getBusinessName() {
    return "TPM预算科目信息导出";
  }


  /**
   * 获取参数
   *
   * @param params
   * @return
   */
  private BudgetSubjectsExportsDto findDataDto(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    
    BudgetSubjectsExportsDto dto = new BudgetSubjectsExportsDto();
    final Object budgetSubjectsCode = params.get("budgetSubjectsCode");
    if (Objects.nonNull(budgetSubjectsCode)) {
      dto.setBudgetSubjectsCode(budgetSubjectsCode.toString());
    }
    final Object budgetSubjectsName = params.get("budgetSubjectsName");
    if (Objects.nonNull(budgetSubjectsName)) {
      dto.setBudgetSubjectsName(budgetSubjectsName.toString());
    }
    final Object budgetSubjectsType = params.get("budgetSubjectsType");
    if (Objects.nonNull(budgetSubjectsType)) {
      dto.setBudgetSubjectsType(budgetSubjectsType.toString());
    }
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    dto.setTenantCode(TenantUtils.getTenantCode());
    return dto;
  }

  /**
   * 调整数据
   *
   * @param data
   */
  private void adjustData(List<BudgetSubjectsExportsVo> data) {
    if (CollectionUtils.isEmpty(data)) {
      return;
    }
    Map<String, List<DictDataVo>> mapDict = dictDataVoService.findByDictTypeCodeList(Lists.newArrayList(SUBJECTS_TYPE, SUBJECTS_GROUP));
    for (BudgetSubjectsExportsVo vo : data) {
      vo.setBudgetSubjectsType(this.findDictValue(mapDict, SUBJECTS_TYPE, vo.getBudgetSubjectsType()));
      vo.setGroupName(this.findDictValue(mapDict,SUBJECTS_GROUP,vo.getGroupCode()));
      vo.setEnableStatus(EnableStatusEnum.getDesc(vo.getEnableStatus()));
    }
  }

  /**
   * 获取字典值
   *
   * @param mapDict
   * @param dictTypeCode
   * @param code
   * @return
   */
  private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
    if (Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
      return StringUtils.EMPTY;
    }
    final List<DictDataVo> vos = mapDict.get(dictTypeCode);
    if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
      return StringUtils.EMPTY;
    }
    final Optional<String> first =
        vos.stream()
            .filter(a -> a.getDictCode().equals(code))
            .map(DictDataVo::getDictValue)
            .findFirst();
    return first.orElse(StringUtils.EMPTY);
  }
}
