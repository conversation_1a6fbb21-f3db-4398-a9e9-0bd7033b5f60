package com.biz.crm.tpm.admin.web.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingItemShareVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/12 17:50
 **/
public interface MarketingItemShareService {

    Page<MarketingItemShareVo> findListByCondition(Pageable pageable, MarketingItemShareVo vo);

    void updateItemShare(String years);

    void saveBatchList(List<MarketingItemShareVo> marketingItemShares);
}
