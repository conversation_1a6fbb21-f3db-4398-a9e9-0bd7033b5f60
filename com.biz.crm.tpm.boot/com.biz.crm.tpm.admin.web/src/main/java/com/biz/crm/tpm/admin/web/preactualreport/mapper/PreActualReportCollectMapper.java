package com.biz.crm.tpm.admin.web.preactualreport.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.admin.web.preactualreport.entity.PreActualReportCollect;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/20 17:22
 **/
public interface PreActualReportCollectMapper extends BaseMapper<PreActualReportCollect> {


    List<MarketingPlanCaseVo> findCaseListByYears(@Param("yearsList") List<String> yearsList);
}
