package com.biz.crm.tpm.admin.web.preactualdetailreport.service.internal;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.admin.web.preactualdetailreport.entity.PreActualDetailReportEntity;
import com.biz.crm.tpm.admin.web.preactualdetailreport.mapper.PreActualDetailReportMapper;
import com.biz.crm.tpm.admin.web.preactualdetailreport.service.PreActualDetailReportService;
import com.biz.crm.tpm.admin.web.preactualdetailreport.service.PreActualDetailReportComponent;
import com.biz.crm.tpm.admin.web.vo.PreActualDetailReportVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 15:19
 **/
@Service
@Slf4j
public class PreActualDetailReportServiceImpl extends ServiceImpl<PreActualDetailReportMapper, PreActualDetailReportEntity> implements PreActualDetailReportService {


    @Resource
    private ApplicationContext context;

    @Resource
    private OrgVoService orgVoService;

    /**
     * 计算预实明细报表
     *
     * @param years
     */
    @Override
    public void calPreActualDetailReport(String years) {
        //查询方案明细
        List<MarketingPlanCaseVo> caseVoList = this.baseMapper.findCaseListByYears(years);
        if (CollectionUtils.isEmpty(caseVoList)) {
            return;
        }
        Map<String, List<MarketingPlanCaseVo>> caseVoMap = caseVoList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode() + x.getYears() + x.getBelongDepartmentCode()));
        List<String> orgCodes = caseVoList.stream().map(x -> x.getBelongDepartmentCode()).distinct().collect(Collectors.toList());
        Map<String, List<OrgVo>> orgParentMap = orgVoService.findAllParentByOrgCodesMap(orgCodes);
        caseVoMap.forEach((k, v) -> {
            MarketingPlanCaseVo caseVo = v.get(0);
            PreActualDetailReportVo detailReportVo = new PreActualDetailReportVo() {{
                this.setCustomerCode(caseVo.getCustomerCode());
                this.setCustomerName(caseVo.getCustomerName());
                this.setOrgCode(caseVo.getBelongDepartmentCode());
                this.setOrgName(caseVo.getBelongDepartmentName());
                this.setYears(years);
                Optional<OrgVo> centerOptional = orgParentMap.get(caseVo.getBelongDepartmentCode()).stream().filter(x -> OrgTypeEnum.COMPANY.getDictCode().equals(x.getOrgType())).findFirst();
                Optional<OrgVo> divisionOptional = orgParentMap.get(caseVo.getBelongDepartmentCode()).stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType())).findFirst();
                Optional<OrgVo> regionOptional = orgParentMap.get(caseVo.getBelongDepartmentCode()).stream().filter(x -> OrgTypeEnum.REGION.getDictCode().equals(x.getOrgType())).findFirst();
                Optional<OrgVo> areaOptional = orgParentMap.get(caseVo.getBelongDepartmentCode()).stream().filter(x -> OrgTypeEnum.AREA.getDictCode().equals(x.getOrgType())).findFirst();
                if (centerOptional.isPresent()) {
                    OrgVo centerOrg = centerOptional.get();
                    this.setCenterCode(centerOrg.getOrgCode());
                    this.setCenterName(centerOrg.getOrgName());
                }
                if (divisionOptional.isPresent()) {
                    OrgVo divisionOrg = divisionOptional.get();
                    this.setDepartmentOneCode(divisionOrg.getOrgCode());
                    this.setDepartmentOneName(divisionOrg.getOrgName());
                }
                if (regionOptional.isPresent()) {
                    OrgVo regionOrg = regionOptional.get();
                    this.setDepartmentTwoCode(regionOrg.getOrgCode());
                    this.setDepartmentTwoName(regionOrg.getOrgName());
                }
                if (areaOptional.isPresent()) {
                    OrgVo areaOrg = areaOptional.get();
                    this.setDepartmentThreeCode(areaOrg.getOrgCode());
                    this.setDepartmentThreeName(areaOrg.getOrgName());
                }
            }};
            PreActualDetailReportComponent component = ApplicationContextHolder.getContext().getBean(PreActualDetailReportComponent.class);
            component.syncPreActualDetailReport(detailReportVo, v);
        });
    }

}
