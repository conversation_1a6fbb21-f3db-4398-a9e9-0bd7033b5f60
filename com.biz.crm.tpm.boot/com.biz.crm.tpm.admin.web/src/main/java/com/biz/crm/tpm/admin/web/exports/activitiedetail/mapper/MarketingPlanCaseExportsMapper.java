package com.biz.crm.tpm.admin.web.exports.activitiedetail.mapper;

import com.biz.crm.tpm.admin.web.exports.activitiedetail.model.MarketingPlanCaseExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MarketingPlanCaseExportsMapper {


    List<String> getCondition1(@Param("dto") MarketingPlanCaseExportsVo dto);

    Integer getExportTotal(@Param("vo") MarketingPlanCaseExportsVo  vo,@Param("tenantCode")  String tenantCode);

    List<MarketingPlanCaseExportsVo> findData(@Param("dto") MarketingPlanCaseExportsVo dto);

}
