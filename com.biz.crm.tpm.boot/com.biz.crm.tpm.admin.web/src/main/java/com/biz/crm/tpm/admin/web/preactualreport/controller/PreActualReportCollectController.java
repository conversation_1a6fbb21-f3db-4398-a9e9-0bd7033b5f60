package com.biz.crm.tpm.admin.web.preactualreport.controller;

import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.preactualreport.service.PreActualReportCollectService;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/20 17:28
 **/
@RestController
@RequestMapping("/v1/preActualReportCollectController")
@Slf4j
public class PreActualReportCollectController {


    @Autowired
    private PreActualReportCollectService preActualReportCollectService;

    @ApiOperation(value = "计算预实汇总报表")
    @PostMapping("calPreActualReportCollect")
    public Result calPreActualReportCollect(@RequestBody List<String> yearsList) {
        preActualReportCollectService.calPreActualReportCollect(yearsList);
        return Result.ok();
    }


    @GetMapping("schedulePreActualReportCollect")
    @ApiOperation(value = "定时执行预实汇总报表")
    public Result schedulePreActualReportCollect() {
        LocalDate now = LocalDate.now();
        //获取上一个月
        LocalDate lastMonth = now.plusMonths(-1l);
        String lastYears = lastMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        String years = now.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        List<String> yearsList = Lists.newArrayList(lastYears,years);
        preActualReportCollectService.calPreActualReportCollect(yearsList);
        return Result.ok();
    }

}
