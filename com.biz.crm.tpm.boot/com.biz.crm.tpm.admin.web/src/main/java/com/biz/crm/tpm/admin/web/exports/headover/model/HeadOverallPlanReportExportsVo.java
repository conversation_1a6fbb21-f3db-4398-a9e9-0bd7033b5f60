package com.biz.crm.tpm.admin.web.exports.headover.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 【总部指引追踪-分大区】导出明细
 *
 * <AUTHOR>
 */
@CrmExcelExport
@Data
public class HeadOverallPlanReportExportsVo extends CrmExcelVo {

    /**
     * 指引明细编码
     */
    @ApiModelProperty(notes = "指引明细编码", value = "指引明细编码")
    @CrmExcelColumn("指引明细编码")
    private String schemeDetailCode;

    /**
     * 总部指引编码
     */
    @ApiModelProperty(notes = "总部指引编码", value = "总部指引编码")
    @CrmExcelColumn("总部指引编码")
    private String schemeCode;

    /**
     * 总部指引名称
     */
    @ApiModelProperty(notes = "总部指引名称", value = "总部指引名称")
    @CrmExcelColumn("总部指引名称")
    private String schemeName;

    /**
     * 总部指引名称
     */
    @ApiModelProperty(notes = "二级费用大类", value = "二级费用大类")
    @CrmExcelColumn("二级费用大类")
    private String secondCostCategoryName;

    /**
     * 总部指引名称
     */
    @ApiModelProperty(notes = "费用项目", value = "费用项目")
    @CrmExcelColumn("费用项目")
    private String detailName;


    /**
     * 活动开始时间
     */
    @ApiModelProperty(notes = "活动开始时间", value = "活动开始时间")
    @CrmExcelColumn("活动开始时间")
    private String startDate;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(notes = "活动结束时间", value = "活动结束时间")
    @CrmExcelColumn("活动结束时间")
    private String endDate;

    /**
     * 费用归属年月
     */
    @ApiModelProperty(notes = "费用归属年月", value = "费用归属年月")
    @CrmExcelColumn("费用归属年月")
    private String years;

    /**
     * 承接销售部门
     */
    @ApiModelProperty(notes = "承接销售部门", value = "承接销售部门")
    @CrmExcelColumn("承接销售部门")
    private String bearDepartmentStr;


    /**
     * 承担类型
     */
    @ApiModelProperty(notes = "承担类型", value = "承担类型")
    @CrmExcelColumn("承担类型")
    private String bearType;

    /**
     * 费用承担部门
     */
    @ApiModelProperty(notes = "费用承担部门", value = "费用承担部门")
    @CrmExcelColumn("费用承担部门")
    private String bearDepartmentName;

    /**
     * 成本中心
     */
    @ApiModelProperty(notes = "成本中心", value = "成本中心")
    @CrmExcelColumn("成本中心")
    private String costCenterName;

    /**
     * 成本中心
     */
    @ApiModelProperty(notes = "合作类型", value = "合作类型")
    @CrmExcelColumn("合作类型")
    private String cooperateTypeStr;


    /**
     * 客户标签
     */
    @ApiModelProperty(notes = "客户标签", value = "客户标签")
    @CrmExcelColumn("客户标签")
    private String customerTagStr;

    /**
     * 客户
     */
    @ApiModelProperty(notes = "客户", value = "客户")
    @CrmExcelColumn("客户")
    private String customerStr;

    /**
     * 终端标签
     */
    @ApiModelProperty(notes = "终端标签", value = "终端标签")
    @CrmExcelColumn("终端标签")
    private String terminalTagStr;


    /**
     * 门店
     */
    @ApiModelProperty(notes = "门店", value = "门店")
    @CrmExcelColumn("门店")
    private String terminalStr;

    /**
     * 品项
     */
    @ApiModelProperty(notes = "品项", value = "品项")
    @CrmExcelColumn("品项")
    private String itemStr;

    /**
     * 产品
     */
    @ApiModelProperty(notes = "产品", value = "产品")
    @CrmExcelColumn("产品")
    private String productStr;

    /**
     * 预估费用
     */
    @ApiModelProperty(notes = "预估费用", value = "预估费用")
    @CrmExcelColumn("预估费用")
    private String applyAmount;

    /**
     * 区域是否可直接承接
     */
    @ApiModelProperty(notes = "区域是否可直接承接", value = "区域是否可直接承接")
    @CrmExcelColumn("区域是否可直接承接")
    private String bearFlag;

    /**
     * 兑付要求及条件
     */
    @ApiModelProperty(notes = "兑付要求及条件", value = "兑付要求及条件")
    @CrmExcelColumn("兑付要求及条件")
    private String cashCondition;

    /**
     * 已承接金额(元)
     */
    @ApiModelProperty(notes = "已承接金额(元)", value = "已承接金额(元)")
    @CrmExcelColumn("已承接金额(元)")
    private String alreadyBearAmount;

    /**
     * 未承接金额(元)
     */
    @ApiModelProperty(notes = "未承接金额(元)", value = "未承接金额(元)")
    @CrmExcelColumn("未承接金额(元)")
    private String notBearAmount;

    /**
     * 计提金额(元)
     */
    @ApiModelProperty(notes = "计提金额(元)", value = "计提金额(元)")
    @CrmExcelColumn("计提金额(元)")
    private String withholdingAmount;


    /**
     * 结案金额(元)
     */
    @ApiModelProperty(notes = "结案金额(元)", value = "结案金额(元)")
    @CrmExcelColumn("结案金额(元)")
    private String auditAmount;


    /**
     * 兑付金额(元)
     */
    @ApiModelProperty(notes = "兑付金额(元)", value = "兑付金额(元)")
    @CrmExcelColumn("兑付金额(元)")
    private String cashAmount;




    /**
     * 操作人
     */
    @ApiModelProperty(notes = "操作人", value = "操作人")
    @CrmExcelColumn("操作人")
    private String createName;
}
