package com.biz.crm.tpm.admin.web.exports.costtypedetails.mapper;

import com.biz.crm.tpm.admin.web.exports.costtypedetails.model.CostTypeDetailExportsDto;
import com.biz.crm.tpm.admin.web.exports.costtypedetails.model.CostTypeDetailExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月07日 15:00:00
 */
public interface CostTypeDetailExportsMapper {


  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") CostTypeDetailExportsDto dto);

  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<CostTypeDetailExportsVo> findData(@Param("dto") CostTypeDetailExportsDto dto);

}
