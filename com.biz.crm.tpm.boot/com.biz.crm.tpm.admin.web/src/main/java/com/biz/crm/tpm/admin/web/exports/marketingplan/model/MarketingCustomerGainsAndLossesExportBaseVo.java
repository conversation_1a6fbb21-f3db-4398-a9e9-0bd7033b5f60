package com.biz.crm.tpm.admin.web.exports.marketingplan.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/26 19:15
 */
@Data
public abstract class MarketingCustomerGainsAndLossesExportBaseVo {

    @ApiModelProperty("预算收入")
    private BigDecimal budgetIncome;

    @ApiModelProperty("规划收入")
    private BigDecimal planIncome;

    @ApiModelProperty("收入达成率")
    private BigDecimal incomeAchieveRatio;

    @ApiModelProperty("预算费率")
    private BigDecimal budgetRatio;

    @ApiModelProperty("规划费率")
    private BigDecimal planRatio;

    @ApiModelProperty("费率偏差")
    private BigDecimal ratioDeviation;

    @ApiModelProperty("利润率")
    private BigDecimal profitRatio;

    @ApiModelProperty("毛利率")
    private BigDecimal grossProfitRatio;

    @ApiModelProperty("物流费率")
    private BigDecimal logisticsRatio;

    @ApiModelProperty("营销费率")
    private BigDecimal marketingRatio;

    @ApiModelProperty("公摊费率")
    private BigDecimal publicShareRatio;

    @ApiModelProperty("促销")
    private BigDecimal promotion;

    @ApiModelProperty("回调")
    private BigDecimal callback;

    @ApiModelProperty("陈列")
    private BigDecimal display;

    @ApiModelProperty("人员推广")
    private BigDecimal generalization;

    @ApiModelProperty("品宣")
    private BigDecimal disseminate;

    @ApiModelProperty("销售奖励")
    private BigDecimal salesReward;

    @ApiModelProperty("合同")
    private BigDecimal contract;

}
