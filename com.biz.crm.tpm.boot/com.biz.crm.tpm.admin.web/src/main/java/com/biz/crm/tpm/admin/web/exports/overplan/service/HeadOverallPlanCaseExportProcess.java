package com.biz.crm.tpm.admin.web.exports.overplan.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.tpm.admin.web.exports.overplan.model.HeadOverPlanCaseExportVo;
import com.biz.crm.tpm.business.activities.overallplan.helper.OverallPlanPageCacheHelper;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanService;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanDepartmentVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanProductVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanScopeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: haiyang
 * @Date: 2025-04-14 15:52
 * @Desc:
 */
@Slf4j
@Component
public class HeadOverallPlanCaseExportProcess implements ExportProcess<HeadOverPlanCaseExportVo> {

    @Autowired
    private OverallPlanPageCacheHelper overallPlanPageCacheHelper;

    @Autowired
    private OverallPlanService overallPlanService;

    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    @Autowired
    private DictDataVoService dictDataVoService;


    @Override
    public Integer getPageSize() {
        return CommonConstant.IE_EXPORT_PAGE_SIZE;
    }

    @Override
    public Integer getTotal(Map<String, Object> params) {
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");
        Integer total = overallPlanPageCacheHelper.getTotal((String) params.get("cacheKey"));
        return total;
    }

    @Override
    public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");
        String cacheKey = (String) params.get("cacheKey");
        Map<String, String> bearTypeMap = dictDataVoService.findMapByDictTypeCode("TPM_SCHEME_BEAR_TYPE");
        Map<String, String> channelTypeMap = dictDataVoService.findMapByDictTypeCode("channel_type");
        Map<String, String> cooperateTypeMap = dictDataVoService.findMapByDictTypeCode("mdm_cooperate_type");
        Map<String, String> customerTagMap = dictDataVoService.findMapByDictTypeCode("customer_tag");
        Map<String, String> terminalTagMap = dictDataVoService.findMapByDictTypeCode("terminal_tag");
        Map<String, String> regionYesOrNoMap = dictDataVoService.findMapByDictTypeCode("region_yes_or_no");
        List<OverallPlanCaseVo> allCacheList = overallPlanService.findCacheList(cacheKey);
        List<HeadOverPlanCaseExportVo> exportList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allCacheList)) {
            allCacheList.forEach(x -> {
                HeadOverPlanCaseExportVo exportVo = BeanUtil.copyProperties(x, HeadOverPlanCaseExportVo.class);
                exportVo.setBearType(StringUtils.isEmpty(x.getBearType())? "" : bearTypeMap.get(x.getBearType()));
                if (CollectionUtils.isNotEmpty(x.getChannelTypeList())) {
                    exportVo.setChannelTypeNameStr(x.getChannelTypeList().stream().map(channelTypeMap::get).collect(Collectors.joining(",")));
                }
                if (CollectionUtils.isNotEmpty(x.getCooperateTypeList())) {
                    exportVo.setCooperateTypeStr(x.getCooperateTypeList().stream().map(cooperateTypeMap::get).collect(Collectors.joining(",")));
                }
                if (CollectionUtils.isNotEmpty(x.getCustomerTagList())) {
                    exportVo.setCustomerTagStr(x.getCustomerTagList().stream().map(customerTagMap::get).collect(Collectors.joining(",")));
                }
                if (CollectionUtils.isNotEmpty(x.getTerminalTagList())) {
                    exportVo.setTerminalTagStr(x.getTerminalTagList().stream().map(terminalTagMap::get).collect(Collectors.joining(",")));
                }
                exportVo.setBearFlag(StringUtils.isEmpty(x.getBearFlag())? "" : regionYesOrNoMap.get(x.getBearFlag()));
                if (CollectionUtils.isNotEmpty(x.getItemList())) {
                    exportVo.setItemName(x.getItemList().stream().map(OverallPlanProductVo::getName).collect(Collectors.joining(",")));
                }
                if (CollectionUtils.isNotEmpty(x.getProductList())) {
                    exportVo.setProductName(x.getProductList().stream().map(OverallPlanProductVo::getName).collect(Collectors.joining(",")));
                }
                if (CollectionUtils.isNotEmpty(x.getBearDepartmentList())){
                    exportVo.setBearDepartmentNameStr(x.getBearDepartmentList().stream().map(OverallPlanDepartmentVo::getDepartmentName).collect(Collectors.joining(",")));
                }
                if (CollectionUtils.isNotEmpty(x.getCustomerList())) {
                    exportVo.setCustomerNameStr(x.getCustomerList().stream().map(OverallPlanScopeVo::getCustomerName).collect(Collectors.joining(",")));
                }
                exportList.add(exportVo);
            });
        }
        return JSON.parseArray(JSON.toJSONString(exportList));
    }

    @Override
    public String getBusinessCode() {
        return "guide_details_list_export";
    }

    @Override
    public String getBusinessName() {
        return "总部指引明细导出";
    }
}
