package com.biz.crm.tpm.admin.web.exports.withholdingcollect;

import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashInvoiceDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: haiyang
 * @Date: 2025-04-01 19:19
 * @Desc:
 */
@Data
public class WithholdingCollectAuditedAndCashedExportVo {


    @ApiModelProperty("兑付编号")
    private String cashCode;

    @ApiModelProperty("兑付明细编号")
    private String cashDetailCode;

    @ApiModelProperty("结案申请编号")
    private String auditCode;

    @ApiModelProperty("结案明细编号")
    private String auditDetailCode;

    @ApiModelProperty("审批状态")
    private String status;

    @ApiModelProperty("方案规划编码")
    private String schemeCode;

    @ApiModelProperty("方案规划名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动描述")
    private String actDesc;

    @ApiModelProperty("费用项目名称")
    private String categoryName;

    @ApiModelProperty("兑付名称")
    private String detailName;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("结案金额")
    private BigDecimal auditAmount;

    @ApiModelProperty("已兑付金额")
    private BigDecimal cashAmount;

    @ApiModelProperty("剩余可兑付金额")
    private BigDecimal availableCashAmount;

    @ApiModelProperty("本次兑付金额")
    private BigDecimal thisCashAmount;

    @ApiModelProperty("是否完全兑付")
    private String beCash;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("使用部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("使用部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("兑付类型")
    private String cashType;

    @ApiModelProperty("兑付方式")
    private String cashMethod;

    @ApiModelProperty("付款状态")
    private String payStatus;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("供应商编码")
    private String payeeCode;

    @ApiModelProperty("供应商名称")
    private String payeeName;

    @ApiModelProperty("供应商Erp编码")
    private String payeeErpCode;

    @ApiModelProperty("合作类型")
    private String cooperateType;

    @ApiModelProperty("凭证号")
    private String voucherCode;


}
