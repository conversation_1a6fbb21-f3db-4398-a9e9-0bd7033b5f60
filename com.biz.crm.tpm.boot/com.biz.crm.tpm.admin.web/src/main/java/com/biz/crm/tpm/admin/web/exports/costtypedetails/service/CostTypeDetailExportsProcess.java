package com.biz.crm.tpm.admin.web.exports.costtypedetails.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.Objects;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.exports.costtypedetails.mapper.CostTypeDetailExportsMapper;
import com.biz.crm.tpm.admin.web.exports.costtypedetails.model.CostTypeDetailExportsDto;
import com.biz.crm.tpm.admin.web.exports.costtypedetails.model.CostTypeDetailExportsVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月08日 14:45:00
 */
@Component
public class CostTypeDetailExportsProcess implements ExportProcess<CostTypeDetailExportsVo> {

  @Autowired
  private CostTypeDetailExportsMapper costTypeDetailExportsMapper;

  @Override
  public Integer getTotal(Map<String, Object> params) {
    CostTypeDetailExportsDto dto = this.findDataDto(params);
    dto.setTenantCode(TenantUtils.getTenantCode());
    return costTypeDetailExportsMapper.getExportTotal(dto);
  }

  @Override
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    CostTypeDetailExportsDto dto = this.findDataDto(params);
    dto.setOffset(this.getPageSize() * vo.getPageNo());
    dto.setLimit(vo.getPageSize());
    dto.setTenantCode(TenantUtils.getTenantCode());
    List<CostTypeDetailExportsVo> data = costTypeDetailExportsMapper.findData(dto);
    //调整
    adjustData(data);
    return toJSONArray(data);
  }

  @Override
  public String getBusinessCode() {
    return "TPM_COST_TYPE_DETAIL_EXPORTS";
  }

  @Override
  public String getBusinessName() {
    return "TPM活动细类导出";
  }

  private CostTypeDetailExportsDto findDataDto(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    
    CostTypeDetailExportsDto dto = new CostTypeDetailExportsDto();
    final Object enableStatus = params.get("enableStatus");
    if (Objects.nonNull(enableStatus)){
      dto.setEnableStatus(enableStatus.toString());
    }
    final Object detailName = params.get("detailName");
    if (Objects.nonNull(detailName)){
      dto.setDetailName(detailName.toString());
    }
    final Object detailCode = params.get("detailCode");
    if (Objects.nonNull(detailCode)){
      dto.setDetailCode(detailCode.toString());
    }
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    dto.setTenantCode(TenantUtils.getTenantCode());
    return dto;
  }


  /**
   * 调整数据
   * @param data
   */
  private void adjustData(List<CostTypeDetailExportsVo> data) {
    if (CollectionUtils.isEmpty(data)){
      return;
    }
    for (CostTypeDetailExportsVo vo : data) {
      vo.setEnableStatus(EnableStatusEnum.getDesc(vo.getEnableStatus()));
    }
  }

  /**
   * 获取字典值
   *
   * @param mapDict
   * @param dictTypeCode
   * @param code
   * @return
   */
  private String findDictValue(Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
    if (java.util.Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
      return StringUtils.EMPTY;
    }
    final List<DictDataVo> vos = mapDict.get(dictTypeCode);
    if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
      return StringUtils.EMPTY;
    }
    final Optional<String> first =
        vos.stream()
            .filter(a -> a.getDictCode().equals(code))
            .map(DictDataVo::getDictValue)
            .findFirst();
    return first.orElse(StringUtils.EMPTY);
  }
}
