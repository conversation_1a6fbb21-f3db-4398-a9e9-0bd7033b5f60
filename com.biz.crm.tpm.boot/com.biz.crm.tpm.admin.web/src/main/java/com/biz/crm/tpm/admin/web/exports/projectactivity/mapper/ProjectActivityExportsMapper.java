package com.biz.crm.tpm.admin.web.exports.projectactivity.mapper;

import com.biz.crm.tpm.admin.web.exports.projectactivity.model.ProjectActivityExportsDto;
import com.biz.crm.tpm.admin.web.exports.projectactivity.model.ProjectActivityExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:项目活动导出 数据库访问层
 * @createTime 2022年07月01日 15:17:00
 */
public interface ProjectActivityExportsMapper {
  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") ProjectActivityExportsDto dto);
  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<ProjectActivityExportsVo> findData(@Param("dto") ProjectActivityExportsDto dto);
}
