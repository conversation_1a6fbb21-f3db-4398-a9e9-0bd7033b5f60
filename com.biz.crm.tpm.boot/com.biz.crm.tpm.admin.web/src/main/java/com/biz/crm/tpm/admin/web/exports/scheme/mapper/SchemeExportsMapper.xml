<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.admin.web.exports.scheme.mapper.SchemeExportsMapper">
  
  <sql id="conditions">
        <if test="dto.schemeCode != null and dto.schemeCode != ''">
          <bind name="schemeCode" value="'%' + dto.schemeCode + '%'"/>
          and t.scheme_code like #{schemeCode}
        </if>
        <if test="dto.schemeName != null and dto.schemeName != ''">
          <bind name="schemeName" value="'%' + dto.schemeName + '%'"/>
          and t.scheme_name like #{schemeName}
        </if>
        <if test="dto.schemeType != null and dto.schemeType != '' ">
          and t.scheme_type = #{dto.schemeType}
        </if>
        <if test="dto.schemeStatus != null and dto.schemeStatus != '' ">
          and t.scheme_status = #{dto.schemeStatus}
        </if>
         and t.del_flag = '${@<EMAIL>()}'
         and t.tenant_code = #{dto.tenantCode}
  </sql>
  
  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from tpm_scheme t 
    <where>
       <include refid="conditions"/> 
    </where>
  </select>
  
  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.scheme.model.SchemeExportsVo">
    select *
    from tpm_scheme t 
    <where>
      <include refid="conditions" />
    </where>
    order by t.create_time desc, t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>