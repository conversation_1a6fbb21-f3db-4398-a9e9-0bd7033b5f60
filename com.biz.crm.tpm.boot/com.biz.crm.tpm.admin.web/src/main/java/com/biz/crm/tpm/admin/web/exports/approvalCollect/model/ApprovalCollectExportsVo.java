package com.biz.crm.tpm.admin.web.exports.approvalCollect.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月08日 15:52:00
 */
@ApiModel(value = "ApprovalCollect", description = "核销采集信息")
@Getter
@Setter
@CrmExcelExport
public class ApprovalCollectExportsVo extends CrmExcelVo {

  /**
   * 编码
   */
  @CrmExcelColumn("示例编码")
  @ApiModelProperty(name = "code", notes = "编码", value = "编码")
  private String code;

  /**
   * 名称
   */
  @CrmExcelColumn("示例名称")
  @ApiModelProperty(name = "name", notes = "名称", value = "名称")
  private String name;

  /**
   * 描述
   */
  @CrmExcelColumn("示例描述")
  @ApiModelProperty(name = "descr", notes = "描述", value = "描述")
  private String descr;

  /**
   * 核销采集类型
   */
  @CrmExcelColumn("示例类型")
  @ApiModelProperty(name = "type", notes = "实例类型", value = "实例类型")
  private String type;

  /**
   * 数据业务状态（启用状态）
   */
  @CrmExcelColumn("启用状态")
  @ApiModelProperty(name = "enableStatus", notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
  private String enableStatus;
}
