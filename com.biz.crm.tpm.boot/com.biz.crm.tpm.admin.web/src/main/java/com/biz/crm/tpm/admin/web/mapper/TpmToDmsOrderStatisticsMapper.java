package com.biz.crm.tpm.admin.web.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.dms.business.order.sdk.OrderReachStatisticsReportDto;
import com.biz.crm.dms.business.order.sdk.dto.TpmToDmsOrderStatisticDto;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/17 11:27
 */
public interface TpmToDmsOrderStatisticsMapper {

    List<TpmToDmsOrderStatisticDto> findDataListByYears(@Param("years") String years, @Param("tenantCode") String tenantCode);

    Page<TpmToDmsOrderStatisticDto> findByCondition(Page<TpmToDmsOrderStatisticDto> page, @Param("dto")OrderReachStatisticsReportDto dto,
                                                    @Param("orgCodes")List<String> orgCodes,@Param("tenantCode")String tenantCode);

    List<MarketingSalesPlanVo> findSalesPlanByConditions(@Param("customerCodes") List<String> customerCodes, @Param("costCenterCodes") List<String> costCenterCodes,
                                                         @Param("years") String years,@Param("productCodes")List<String> productCodes);
}
