package com.biz.crm.tpm.admin.web.controller.marketingplan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.service.MarketingItemShareComponent;
import com.biz.crm.tpm.admin.web.service.MarketingItemShareService;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingItemShareVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/12 17:06
 **/
@ApiOperation(value = "品相分摊")
@RestController
@RequestMapping("/v1/marketingItemShareController")
@Slf4j
public class MarketingItemShareController {

    @Autowired
    private MarketingItemShareService marketingItemShareService;

    @Autowired
    private MarketingItemShareComponent marketingItemShareComponent;


    @ApiOperation(value = "查询品项分析列表")
    @GetMapping("findListByCondition")
    public Result<Page<MarketingItemShareVo>> findListByCondition(@PageableDefault(50) Pageable pageable, MarketingItemShareVo vo) {
        return Result.ok(marketingItemShareService.findListByCondition(pageable, vo));
    }


    @ApiOperation(value = "更新品项分析报表")
    @GetMapping("updateItemShare")
    public Result updateItemShare(@RequestParam(required = false) String years) {
        marketingItemShareService.updateItemShare(years);
        return Result.ok();
    }


    @ApiOperation(value = "定时生成品项分析报表")
    @GetMapping("scheduleGenerateItemShare")
    public Result scheduleGenerateItemShare() {
        log.error("---------------------------------品相分摊执行定时任务---------------------------------");
        String years = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        log.error("执行日期:{}", years);
        marketingItemShareService.updateItemShare(years);
        return Result.ok();
    }


    @ApiOperation(value = "异步计算")
    @PostMapping("syncRunItemShare")
    public Result syncRunItemShare(@RequestBody List<String> yearsList) {
        marketingItemShareComponent.syncRunItemShare(yearsList);
        return Result.ok();
    }

}
