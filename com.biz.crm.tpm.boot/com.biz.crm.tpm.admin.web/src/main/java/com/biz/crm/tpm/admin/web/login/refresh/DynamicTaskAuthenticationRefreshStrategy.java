package com.biz.crm.tpm.admin.web.login.refresh;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.mdm.business.user.sdk.service.UserValidityCheckService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.admin.web.login.DefaultPerfectLoginUserDetails;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.bizunited.nebula.security.sdk.refresh.AuthenticationRefreshStrategy;
import com.bizunited.nebula.task.vo.DynamicTaskOperatorVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 为了完成动态任务启动时，初始化该动态任务操作者，所设计的身份刷新策略</br>
 * 另外，请注意，该策略还要配合DefaultIndependencySecurity类中的逻辑来激活使用
 *
 * <AUTHOR>
 */
@Component
public class DynamicTaskAuthenticationRefreshStrategy extends DefaultPerfectLoginUserDetails implements AuthenticationRefreshStrategy {

  @Autowired
  private SimpleSecurityProperties simpleSecurityProperties;
  @Autowired
  @Lazy
  private UserValidityCheckService userValidityCheckService;

  @Override
  public int getOrder() {
    return 9;
  }

  @Override
  public boolean matched(Object info) {
    // 只有在DynamicTaskOperatorVo对象存在的情况下，才会调用，否则就使用DefaultAuthenticationRefreshStrategy创建操作者
    if (info != null && (info instanceof DynamicTaskOperatorVo)) {
      return true;
    }
    return false;
  }

  @Override
  public UserIdentity refresh(Object info) {
    DynamicTaskOperatorVo dynamicTaskOperatorVo = (DynamicTaskOperatorVo) info;
    // 角色信息
    String roleCodes[] = dynamicTaskOperatorVo.getRoles();
    Integer type = this.simpleSecurityProperties.getDefaultLoginType();
    String account = dynamicTaskOperatorVo.getAccount();
    String tenantCode = dynamicTaskOperatorVo.getTenantCode();

    // 生成并验证mdmUser
    FacturerUserDetails mdmUser = new FacturerUserDetails();
    UserVo userVo = this.userValidityCheckService.verificationManageByAccount(account);
    mdmUser.setRoleCodes(roleCodes);
    mdmUser.setLoginType(type);
    mdmUser.setAccount(account);
    mdmUser.setTenantCode(tenantCode);
    super.perfectLoginUserDetails(userVo, mdmUser);
    return mdmUser;
  }

}
