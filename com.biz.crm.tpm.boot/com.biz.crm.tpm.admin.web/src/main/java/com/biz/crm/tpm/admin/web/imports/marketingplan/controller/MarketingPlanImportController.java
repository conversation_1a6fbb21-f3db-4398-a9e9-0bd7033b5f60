package com.biz.crm.tpm.admin.web.imports.marketingplan.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.ie.local.BusinessExcelExportTemplateWriteUtil;
import com.biz.crm.business.common.ie.local.BusinessMoreSheetImportListener;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.common.ie.local.entity.ImportTask;
import com.biz.crm.common.ie.sdk.enums.ExecStatusEnum;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterOrgVo;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserPositionVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.admin.web.config.SendMsgComponent;
import com.biz.crm.tpm.admin.web.imports.contractcost.model.ContractCostImportVo;
import com.biz.crm.tpm.admin.web.imports.contractcost.service.ContractCostImportProcess;
import com.biz.crm.tpm.admin.web.imports.marketingplan.listener.TpmMarketingSalesPlanImportListener;
import com.biz.crm.tpm.admin.web.imports.marketingplan.service.MarketingPlanImportComponent;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanImportCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingSalesPlanImportCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseImportVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanImportVo;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesTemplateConfigService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import com.bizunited.nebula.common.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/12 15:50
 */
@RestController
@RequestMapping("/v1/marketingPlanImportController")
@Slf4j
public class MarketingPlanImportController {

    @Resource
    private BusinessExcelExportTemplateWriteUtil businessExcelExportTemplateWriteUtil;

    @Resource
    private MarketingPlanImportCheckHelper helper;

    @Resource
    private MarketingPlanImportComponent marketingPlanImportComponent;

    @Resource
    private MarketingSalesPlanImportCheckHelper salesPlanImportCheckHelper;

    @Resource
    private MarketingPlanService marketingPlanService;

    @Resource
    private ProductVoService productVoService;

    @Autowired(required = false)
    private MarketingPlanCaseCheckHelper checkHelper;

    @Autowired
    private LoginUserService loginUserService;

    @Autowired
    private UserVoService userVoService;

    @Autowired
    private OrgVoService orgVoService;


    @PostMapping("importMarketingPlanCase")
    @ApiOperation(value = "导入营销规划方案明细")
    public Result importMarketingPlanCase(@RequestParam MultipartFile file, @RequestParam String cacheKey, @RequestParam String years,
                                          @RequestParam(required = false) String schemeCode, @RequestParam(required = false) String originalSchemeCode) {
        Map<String, List<MarketingPlanCaseImportVo>> caseListMap = Maps.newHashMap();
        List<String> templateList = Lists.newArrayList();
        // 获取当前登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        log.info("importMarketingPlanCase.loginDetails: {}", null == loginDetails ? "null" : JSON.toJSONString(loginDetails));
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        log.info("importMarketingPlanCase.userVo: {}", null == userVo ? "null" : JSON.toJSONString(userVo));
        UserVo detail = userVoService.findDetailById(userVo.getId());
        log.info("importMarketingPlanCase.detail: {}", null == detail ? "null" : JSON.toJSONString(detail));
        List<String> userOrgCodes = Lists.newArrayList();
        detail.getPositionList().forEach(e -> {
            Set<String> orgCodes = e.getOrgCodes();
            if (!CollectionUtils.isEmpty(orgCodes)) {
                userOrgCodes.addAll(orgCodes);
            }
        });
        log.info("importMarketingPlanCase.userOrgCodes: {}", JSON.toJSONString(userOrgCodes));
        List<String> loginUserCodes = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(userOrgCodes)) {
            List<OrgVo> orgVos = orgVoService.findByOrgCodes(userOrgCodes);
            if (!CollectionUtils.isEmpty(orgVos)) {
                orgVos.forEach(e -> {
                    List<OrgVo> nextLevelOrgs = orgVoService.findByRuleCodeLike(e.getRuleCode());
                    if (!CollectionUtils.isEmpty(nextLevelOrgs)) {
                        loginUserCodes.addAll(nextLevelOrgs.stream().map(OrgVo::getOrgCode).collect(Collectors.toList()));
                    }
                });
            }
        }

        log.info("当前登录用户组织及下级信息：{}", loginUserCodes);
        try {
            //销售计划
            List<MarketingSalesPlanImportVo> salesPlanImportVos = marketingPlanImportComponent.salesPlanList(file);
            if (!CollectionUtils.isEmpty(salesPlanImportVos)) {
                List<String> productCodes = salesPlanImportVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getActName()) && ObjectUtils.isNotEmpty(x.getProductCode()))
                        .map(x -> x.getProductCode()).distinct().collect(Collectors.toList());
                List<String> costCenterCodes = salesPlanImportVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getActName()) && ObjectUtils.isNotEmpty(x.getCostCenterCode()))
                        .map(x -> x.getCostCenterCode()).distinct().collect(Collectors.toList());
                Map<String, MdmCostCenterVo> costCenterMap = checkHelper.findCostCenterVoMap(costCenterCodes);
                List<ProductVo> productVoList = productVoService.findMainDetailsByProductCodes(productCodes);
                Map<String, ProductVo> productMap = productVoList.stream().collect(Collectors.toMap(x -> x.getProductCode(), Function.identity()));
                //随单
                List<MarketingPlanCaseImportVo> matchingGiftList = salesPlanImportVos.stream()
                        .filter(x -> ObjectUtils.isNotEmpty(x.getActName()))
                        .map(x -> {
                            MarketingPlanCaseImportVo vo = JsonUtils.convert(x, MarketingPlanCaseImportVo.class);
                            vo.setYears(null);
                            vo.setCostCenterCode(x.getBdCostCenterCode());
                            vo.setCostCenterName(x.getBdCostCenterName());
                            if (costCenterMap.containsKey(x.getCostCenterCode())) {
                                MdmCostCenterOrgVo costCenterOrg = costCenterMap.get(x.getCostCenterCode()).getOrgList().get(0);
                                vo.setBelongDepartmentCode(costCenterOrg.getOrgCode());
                                vo.setBelongDepartmentName(costCenterOrg.getOrgName());
                            }
                            if (productMap.containsKey(x.getProductCode())) {
                                ProductVo productVo = productMap.get(x.getProductCode());
                                vo.setLevelCodeList(productVo.getProductSmallClassCode());
                            }
                            return vo;
                        }).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(matchingGiftList)) {
                    for (MarketingPlanCaseImportVo vo : matchingGiftList) {
                        vo.setSort(System.currentTimeMillis());
                    }
                    caseListMap.put(MarketingPlanCaseTypeEnum.matching_gift.getCode(), matchingGiftList);
                }
            }
            //陈列
            List<MarketingPlanCaseImportVo> displayList = marketingPlanImportComponent.displayList(file);
            if (!CollectionUtils.isEmpty(displayList)) {
                for (MarketingPlanCaseImportVo vo : displayList) {
                    vo.setSort(System.currentTimeMillis());
                }
                caseListMap.put(MarketingPlanCaseTypeEnum.display.getCode(), displayList);
            }
            //人员费用
//            List<MarketingPlanCaseImportVo> staffCostList = marketingPlanImportComponent.staffCostList(file);
//            if (!CollectionUtils.isEmpty(staffCostList)) {
//                for (MarketingPlanCaseImportVo vo : staffCostList) {
//                    vo.setSort(System.currentTimeMillis());
//                }
//                caseListMap.put(MarketingPlanCaseTypeEnum.staff_cost.getCode(), staffCostList);
//            }
            //物料
            List<MarketingPlanCaseImportVo> materialList = marketingPlanImportComponent.materialList(file);
            if (!CollectionUtils.isEmpty(materialList)) {
                for (MarketingPlanCaseImportVo vo : materialList) {
                    vo.setSort(System.currentTimeMillis());
                }
                caseListMap.put(MarketingPlanCaseTypeEnum.material.getCode(), materialList);
            }
            //后返
            List<MarketingPlanCaseImportVo> backList = marketingPlanImportComponent.backList(file);
            if (!CollectionUtils.isEmpty(backList)) {
                for (MarketingPlanCaseImportVo vo : backList) {
                    vo.setSort(System.currentTimeMillis());
                }
                for (MarketingPlanCaseImportVo marketingPlanCaseImportVo : backList) {
                    String rebateEndDate = marketingPlanCaseImportVo.getRebateEndDate();
                    if(StringUtils.isBlank(rebateEndDate)){
                        continue;
                    }
                    try{
                        if(rebateEndDate.split("-").length>2){
                            YearMonth.parse(rebateEndDate, DateTimeFormatter.ISO_LOCAL_DATE);
                        }
                    }catch (Exception e){
                        throw new ExcelAnalysisException("月末时间无效");
                    }
                }
                caseListMap.put(MarketingPlanCaseTypeEnum.back.getCode(), backList);
            }
            //随单
//            List<MarketingPlanCaseImportVo> matchingGiftList = marketingPlanImportComponent.matchingGiftList(file);
//            if (!CollectionUtils.isEmpty(matchingGiftList)) {
//                for (MarketingPlanCaseImportVo vo : matchingGiftList) {
//                    vo.setSort(System.currentTimeMillis());
//                }
//                caseListMap.put(MarketingPlanCaseTypeEnum.matching_gift.getCode(), matchingGiftList);
//            }
            //o2o
            List<MarketingPlanCaseImportVo> otoList = marketingPlanImportComponent.OTOList(file);
            if (!CollectionUtils.isEmpty(otoList)) {
                for (MarketingPlanCaseImportVo vo : otoList) {
                    vo.setSort(System.currentTimeMillis());
                }
                caseListMap.put(MarketingPlanCaseTypeEnum.o_two_o.getCode(), otoList);
            }
            //固定
//            List<MarketingPlanCaseImportVo> fixedList = marketingPlanImportComponent.fixedList(file);
//            if (!CollectionUtils.isEmpty(fixedList)) {
//                for (MarketingPlanCaseImportVo vo : fixedList) {
//                    vo.setSort(System.currentTimeMillis());
//                }
//                caseListMap.put(MarketingPlanCaseTypeEnum.fixed.getCode(), fixedList);
//            }
            //先执行销售计划
            salesPlanImportCheckHelper.checkData(salesPlanImportVos, cacheKey, years, schemeCode, originalSchemeCode, Lists.newArrayList(loginUserCodes), userVo.getUserName());
            //执行活动明细
            helper.checkData(caseListMap, cacheKey, years, schemeCode, originalSchemeCode, Lists.newArrayList(loginUserCodes), userVo.getUserName());
            templateList.addAll(caseListMap.keySet());
        } catch (ExcelAnalysisException e) {
            //直接强行清理缓存
            marketingPlanService.clearCache(cacheKey);
            return Result.error(e.getCause().getMessage());
        } finally {
            ImportTask importTask = new ImportTask();
            importTask.setTemplateCode(helper.getTemplateCode());
            importTask.setTaskSource(helper.getTemplateName());
            importTask.setTaskName(helper.getTemplateName());
            importTask.setBusinessCode(helper.getTemplateCode());
            importTask.setBusinessName(helper.getTemplateName());
            importTask.setTotal(caseListMap.values().size());
            businessExcelExportTemplateWriteUtil.saveUploadFileImport(file, importTask);
        }
        return Result.ok(templateList);
    }


    @Resource
    private ActivitiesTemplateConfigService activitiesTemplateConfigService;

    @Resource
    private ContractCostImportProcess costImportProcess;

    @PostMapping("importContractCost")
    @ApiOperation(value = "合同导入")
    public Result importContractCost(@RequestParam MultipartFile file) {
        //陈列
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(0);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(OverallPlanConstant.CONTRACT_COST_TEMPLATE);
        Validate.notNull(config, "合同配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = config.getDetails().stream().collect(Collectors.toList());
        Map<String, String> contractMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<ContractCostImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("合同").headRowNumber(1).doRead();
            list = listener.transObjectToClazz(ContractCostImportVo.class, contractMap, filedCheckMap, OverallPlanConstant.CONTRACT_COST_TEMPLATE);
            costImportProcess.importContractCost(list, Maps.newHashMap(), Boolean.FALSE);
        } catch (IOException e) {
            throw new RuntimeException("合同Excel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        return Result.ok();
    }
}
