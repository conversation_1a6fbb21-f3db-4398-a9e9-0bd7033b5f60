package com.biz.crm.tpm.admin.web.imports.overallplan.model;

import com.biz.crm.business.common.ie.sdk.vo.BusinessCrmExcelVo;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@CrmExcelImport(startRow = 2)
public class HeadOverallPlanImportVo extends BusinessCrmExcelVo {

    @CrmExcelColumn("二级费用大类编码")
    private String secondCostCategory;

    @CrmExcelColumn("二级费用大类名称")
    private String secondCostCategoryName;

    @CrmExcelColumn("活动大类编码")
    private String categoryCode;

    @CrmExcelColumn("活动大类名称")
    private String categoryName;

    @CrmExcelColumn("活动细类编码")
    private String detailCode;

    @CrmExcelColumn("活动细类名称")
    private String detailName;

    @CrmExcelColumn("是否可以承接")
    private String bearFlag;

    @CrmExcelColumn("开始时间")
    private String startDate;

    @CrmExcelColumn("结束时间")
    private String endDate;

    @CrmExcelColumn("承担部门编码")
    private String bearDepartmentCode;

    @CrmExcelColumn("承担部门名称")
    private String bearDepartmentName;

    @CrmExcelColumn("承接类型")
    private String bearType;

    @CrmExcelColumn("成本中心编码")
    private String costCenterCode;

    @CrmExcelColumn("成本中心名称")
    private String costCenterName;
    
    @CrmExcelColumn("合作类型标签")
    private String cooperateTypeList;
    
    @CrmExcelColumn("客户标签")
    private String customerTagList;

    @CrmExcelColumn("终端标签")
    private String terminalTagList;

    @CrmExcelColumn("预估费用")
    private String applyAmount;

    @CrmExcelColumn("预估销售额")
    private String estimatedSalesVolume;

    @CrmExcelColumn("兑付条件及说明")
    private String cashCondition;

    @ApiModelProperty("销售部门列表")
    private String bearDepartmentList;

    @CrmExcelColumn("承接销售部门编码")
    private String bearDepartmentCodeList;
    
    @CrmExcelColumn("品项范围")
    private String itemList;

    @CrmExcelColumn("品项范围")
    private String itemCodeList;

    @CrmExcelColumn("产品范围")
    private String productList;

    @CrmExcelColumn("产品范围")
    private String productCodeList;

    @CrmExcelColumn("客户")
    private String customerList;

    @CrmExcelColumn("客户")
    private String customerCodeList;

    @CrmExcelColumn("终端")
    private String terminalList;

    @CrmExcelColumn("终端")
    private String terminalCodeList;

    @CrmExcelColumn("行号")
    private Integer rowNum;
}
