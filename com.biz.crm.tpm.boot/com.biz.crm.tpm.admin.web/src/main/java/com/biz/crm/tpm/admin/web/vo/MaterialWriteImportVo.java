package com.biz.crm.tpm.admin.web.vo;

import com.biz.crm.business.common.base.vo.ExportWriteMdmVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/25 01:14
 */
@Data
public class MaterialWriteImportVo extends ExportWriteMdmVo {

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("标准单位/基本计量单位")
    private String standardUnit;

    @ApiModelProperty("物料类型[数据字典:material_type]")
    private String materialType;

    @ApiModelProperty("品相编码")
    private String productPhaseCode;

    @ApiModelProperty("品相名称")
    private String productPhaseName;

    @ApiModelProperty("行销物料类型[数据字典:mdm_sell_material_type]")
    private String sellMaterialType;

    @ApiModelProperty("零级名称")
    private String zeroLevelName;

    @ApiModelProperty("产品小类")
    private String productSmallClassCode;

    @ApiModelProperty("产品小类")
    private String productSmallClassName;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

}
