package com.biz.crm.tpm.admin.web.exports.regioncollect.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: haiyang
 * @Date: 2025-03-21 11:45
 * @Desc:
 */
@Data
public class RegionCollectSalesPlanExportVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("原方案编码")
    private String originalSchemeCode;

    @ApiModelProperty(value = "销售计划编码")
    private String code;

    @ApiModelProperty(value = "年月")
    private String years;

    @ApiModelProperty(value = "成本中心编码")
    private String costCenterCode;

    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("销售价格")
    private BigDecimal salesPrice;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("销售单位")
    private String saleUnit;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("成本价")
    private BigDecimal costPrice;

    @ApiModelProperty("毛利率")
    private String grossProfitRate;

    @ApiModelProperty("预估销售金额(未税)")
    private BigDecimal estimatedCost;

    @ApiModelProperty("含税预估销售金额")
    private BigDecimal taxEstimatedCost;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("产品运输方式")
    private String transportTypeDesc;

}
