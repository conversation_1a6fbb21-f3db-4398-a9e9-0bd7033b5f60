package com.biz.crm.tpm.admin.web.preactualdetailreport.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 20:18
 **/
@Getter
@AllArgsConstructor
public enum PreActualProjectEnum {

    INCOME("income", "收入", 1),
    COST("cost", "成本", 2),
    GROSS_PROFIT("grossProfit", "毛利", 3),
    LOGISTICS_COSTS("logisticsCosts", "物流费用", 4),
    GIFT_COST("giftCost","搭赠费用",5),
    SURROUNDING_MATERIALS("surroundingMaterials", "周边物料", 100),
    SHARE("share", "分摊", 101),
    PROFIT("profit", "利润", 102),

    ;
    private String code;

    private String desc;

    private Integer sort;
}
