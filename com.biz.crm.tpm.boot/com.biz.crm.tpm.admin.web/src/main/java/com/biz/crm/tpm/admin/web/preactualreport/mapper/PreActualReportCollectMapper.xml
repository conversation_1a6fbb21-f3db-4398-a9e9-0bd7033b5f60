<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.admin.web.preactualreport.mapper.PreActualReportCollectMapper">


    <select id="findCaseListByYears" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        a.*,
        b.*
        FROM
        tpm_marketing_plan p
        LEFT JOIN tpm_marketing_plan_case a ON a.scheme_code = p.scheme_code
        LEFT JOIN tpm_marketing_plan_case_extend b ON a.scheme_detail_code = b.scheme_detail_code
        <where>
            p.process_status in('2', '3')
            AND p.scheme_type != 'change'
            and a.del_flag = '009'
            <if test="yearsList != null and yearsList.size()>0">
                and a.years in
                <foreach collection="yearsList" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>

