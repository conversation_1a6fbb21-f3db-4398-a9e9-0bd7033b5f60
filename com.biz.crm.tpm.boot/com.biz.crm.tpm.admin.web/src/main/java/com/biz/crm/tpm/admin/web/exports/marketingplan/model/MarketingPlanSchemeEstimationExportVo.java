package com.biz.crm.tpm.admin.web.exports.marketingplan.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "MarketingPlanSchemeEstimationExportVo", description = "单方案-营销测算表")
public class MarketingPlanSchemeEstimationExportVo {


    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("预计固定扣点")
    private BigDecimal estimateFixedPoint;

    @ApiModelProperty("预计金额(万元)")
    private BigDecimal estimateAmount;

    @ApiModelProperty("预计占比")
    private BigDecimal estimateRatio;

    @ApiModelProperty("预算固定扣点")
    private BigDecimal budgetFixedPoint;

    @ApiModelProperty("预算金额(万元)")
    private BigDecimal budgetAmount;

    @ApiModelProperty("预算占比")
    private BigDecimal budgetRatio;

    @ApiModelProperty("差异额")
    private BigDecimal differenceAmount;

    @ApiModelProperty("差异率")
    private BigDecimal differenceRatio;
}
