package com.biz.crm.tpm.admin.web.controller.importwritedata;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.service.internal.MarketingWriteMdmDataServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/24 22:35
 */
@RestController
@RequestMapping("/v1/TpmImportTemplateWriteController")
@Slf4j
@Api(tags = "导入模板数据写入MDM主数据")
public class TpmImportTemplateWriteController {


    @Autowired
    private MarketingWriteMdmDataServiceImpl marketingWriteMdmDataService;


    @ApiOperation(value = "写入营销方案导入模板主数据")
    @PostMapping("writeMdmDataToMarketing")
    public Result writeMdmDataToMarketing() {
        marketingWriteMdmDataService.writeMdmDataToImportTemplate();
        return Result.ok();
    }
}
