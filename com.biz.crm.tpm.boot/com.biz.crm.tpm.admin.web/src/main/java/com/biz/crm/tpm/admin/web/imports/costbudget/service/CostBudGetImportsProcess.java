package com.biz.crm.tpm.admin.web.imports.costbudget.service;

import cn.hutool.json.JSONUtil;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.tpm.business.budget.local.entity.CostBudget;
import com.biz.crm.tpm.business.budget.local.repository.CostBudgetRepository;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetImportsVo;
import com.biz.crm.tpm.business.budget.sdk.enums.BudgetTypeEnum;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe: 费用预算导入
 * @createTime 2022年06月09日 14:30:00
 */
@Component
@Slf4j
public class CostBudGetImportsProcess implements ImportProcess<CostBudgetImportsVo> {

  @Autowired(required = false)
  private RedisLockService redisLockService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private CustomerVoService customerVoServiceFeign;
  @Autowired
  private ProductVoService productVoServiceFeign;
  @Autowired
  private OrgVoService orgVoService;
  @Autowired
  private DictDataVoService dictDataVoService;
  @Autowired
  private BudgetSubjectsVoService budgetSubjectsVoService;
  @Autowired(required = false)
  private CostBudgetRepository costBudgetRepository;
    private final static String TPM_COST_BUDGET_IMPORTS = "TPM_COST_BUDGET_IMPORTS";

  /**
   * 公司代码
   */
  private static final String TPM_COMPANY = "tpm_company";

  @Override
  public Integer getBatchCount() {
    return Integer.MAX_VALUE;
  }

  /**
   * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存)
   * 新逻辑需同时实现接口 tryVerify tryConfirm
   *
   * @return
   */
  @Override
  public boolean importBeforeValidationFlag() {
    return true;
  }

  @Override
  public Map<Integer, String> tryVerify(LinkedHashMap<Integer, CostBudgetImportsVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    Map<Integer, String> errMap = new HashMap<>();

    for (Map.Entry<Integer, CostBudgetImportsVo> row : data.entrySet()) {
      int rowNum = row.getKey();

      CostBudgetImportsVo vo = row.getValue();
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getYearMonthLy()), "年月，不能为空！");
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getDepartmentOneCode()), "部门编码，不能为空！");
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostCenterCode()), "成本中心编码，不能为空！");
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostCenterName()), "成本中心名称，不能为空！");
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getBudgetSubjectCode()), "预算科目编码，不能为空！");
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getInitialAmountStr()), "期初金额，不能为空");
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getCompanyCode()), "公司编码，不能为空");
      BigDecimal amount = null;
      try {
        amount = new BigDecimal(vo.getInitialAmountStr().trim());
      } catch (Exception e) {
        this.validateIsTrue(false, "期初金额类型转换失败！");
      }
      this.validateIsTrue(amount.compareTo(BigDecimal.ZERO) >= 0, "操作金额必须大于等于0！");

      String errInfo = this.validateGetErrorInfo();
      if (errInfo != null) {
        errMap.put(rowNum, errInfo);
      }
    }
    return errMap;
  }

  /**
   * 执行
   * @param data
   * @param paramsVo
   * @param params
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Map<Integer, String> execute(LinkedHashMap<Integer, CostBudgetImportsVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    Map<Integer, String> errMap = new HashMap<>();
    redisLockService.lock(TPM_COST_BUDGET_IMPORTS, TimeUnit.MINUTES,60);
    Set<String> orgCodeSet = new HashSet<>();
    Set<String> productCodeSet = new HashSet<>();
    Set<String> erpCodeSet = new HashSet<>();
    Set<String> budgetSubjectCodeSet = new HashSet<>();
    Set<String> uniqueKeyQuerySet = new HashSet<>();
    log.warn("====================开始封装Set");
    for (Map.Entry<Integer, CostBudgetImportsVo> row : data.entrySet()) {
      CostBudgetImportsVo vo = row.getValue();

      orgCodeSet.add(vo.getDepartmentOneCode());
      if (StringUtils.isNotBlank(vo.getProductCode())) {
        productCodeSet.add(vo.getProductCode());
      }
      if (StringUtils.isNotBlank(vo.getErpCode())) {
        erpCodeSet.add(vo.getErpCode());
        vo.setCustomerCode(vo.getErpCode() + vo.getCompanyCode() + "0000");
      }
      budgetSubjectCodeSet.add(vo.getBudgetSubjectCode());

      vo.setUniqueKey(vo.getYearMonthLy() + vo.getDepartmentOneCode() + vo.getCostCenterCode() + vo.getBudgetSubjectCode() +
              StringUtils.defaultIfBlank(vo.getCompanyCode(), "") +
              StringUtils.defaultIfBlank(vo.getCustomerCode(), "") +
              StringUtils.defaultIfBlank(vo.getItemCode(), "") +
              StringUtils.defaultIfBlank(vo.getProductCode(), ""));
      uniqueKeyQuerySet.add(vo.getUniqueKey());
    }
    log.warn("====================结束封装Set");

    log.warn("====================开始调用获取Map");
    Map<String, OrgVo> orgVoMap = orgVoService.findByOrgCodes(new ArrayList<>(orgCodeSet)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), Function.identity(), (a, b) -> a));
    Map<String, Map<String, CustomerVo>> customerVoMap = customerVoServiceFeign.findByErpCodes(new ArrayList<>(erpCodeSet)).stream().collect(Collectors.groupingBy(e -> e.getErpCode(), Collectors.toMap(e -> e.getCompanyCode(), Function.identity(), (a, b) -> a)));
    Map<String, BudgetSubjectsVo> budgetSubjectsVoMap = budgetSubjectsVoService.findByCodes(budgetSubjectCodeSet).stream().collect(Collectors.toMap(e -> e.getBudgetSubjectsCode(), Function.identity(), (a, b) -> a));
    Map<String, ProductVo> productVoMap = productVoServiceFeign.findDetailsByIdsOrProductCodes(null, new ArrayList<>(productCodeSet)).stream().collect(Collectors.toMap(e -> e.getProductCode(), Function.identity(), (a, b) -> a));

    CostBudgetDto costBudget = new CostBudgetDto();
    costBudget.setUniqueKeySet(uniqueKeyQuerySet);
    Set<String> uniqueKeyExistsDbSet = this.validateRepeatabilityOnCreate(costBudget);
    log.warn("====================完成调用获取Map");


    log.warn("====================开始校验");
    // 查询数据字典
    Set<String> uniqueKeySet = new HashSet<>();
    Map<String, List<DictDataVo>> dictDataMap = dictDataVoService.findByDictTypeCodeList(Collections.singletonList(TPM_COMPANY));
    for (Map.Entry<Integer, CostBudgetImportsVo> row : data.entrySet()) {
      int rowNum = row.getKey();

      CostBudgetImportsVo vo = row.getValue();
      vo.setInitialAmount(new BigDecimal(vo.getInitialAmountStr()));
      if (orgVoMap.containsKey(vo.getDepartmentOneCode())) {
        vo.setDepartmentOneName(orgVoMap.get(vo.getDepartmentOneCode()).getOrgName());
        vo.setLevelNum(orgVoMap.get(vo.getDepartmentOneCode()).getLevelNum());
      } else {
        this.validateIsTrue(false, "部门，未找到！");
      }
      if (StringUtils.isNotBlank(vo.getCompanyCode())) {
        DictDataVo company = dictDataMap.get(TPM_COMPANY).stream().filter(e -> e.getDictCode().equals(vo.getCompanyCode())).findFirst().orElse(null);
        if (company != null) {
          vo.setCompanyName(company.getDictValue());
        } else {
          this.validateIsTrue(false, "公司，未找到！");
        }
      }
      if (budgetSubjectsVoMap.containsKey(vo.getBudgetSubjectCode())) {
        vo.setBudgetSubjectName(budgetSubjectsVoMap.get(vo.getBudgetSubjectCode()).getBudgetSubjectsName());
      } else {
        this.validateIsTrue(false, "预算科目，未找到！");
      }


      if (StringUtils.isNotBlank(vo.getProductCode())) {
        if (productVoMap.containsKey(vo.getProductCode())) {
          vo.setProductName(productVoMap.get(vo.getProductCode()).getProductName());
        } else {
          this.validateIsTrue(false, "产品，未找到！");
        }
      }
      if (StringUtils.isNotBlank(vo.getErpCode())) {
        Map<String, CustomerVo> companyVoMap = customerVoMap.get(vo.getErpCode());
        if (companyVoMap.containsKey(vo.getCompanyCode())) {
          CustomerVo customerVo = companyVoMap.get(vo.getCompanyCode());
          this.validateIsTrue(BooleanEnum.TRUE.getCapital().equals(customerVo.getContractCustomer()), "客户不是合同客户");
          vo.setCustomerCode(customerVo.getCustomerCode());
          vo.setCustomerName(customerVo.getCustomerName());
        } else {
          this.validateIsTrue(false, "客户，未找到！");
        }
      }
      this.validateIsTrue(!uniqueKeySet.contains(vo.getUniqueKey()), "导入文件中存在重复的年度预算数据");
      this.validateIsTrue(!uniqueKeyExistsDbSet.contains(vo.getUniqueKey()), "已存在的年度预算数据");
      uniqueKeySet.add(vo.getUniqueKey());
      vo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
      String errInfo = this.validateGetErrorInfo();
      if (errInfo != null) {
        errMap.put(rowNum, errInfo);
      }
    }
    log.warn("====================结束校验");

    log.warn("====================errMap：" + JSONUtil.toJsonStr(errMap));
    if (!errMap.isEmpty()) {
      redisLockService.unlock(TPM_COST_BUDGET_IMPORTS);
      return errMap;
    }

    log.warn("====================开始批量保存");
    costBudgetVoService.createBatch(new ArrayList<>(data.values()));
    log.warn("====================结束批量保存");
    if (TransactionSynchronizationManager.isActualTransactionActive()) {
      TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
        @Override
        public void afterCompletion(int status) {
          if (status == STATUS_ROLLED_BACK || status == STATUS_COMMITTED) {
            // 事务提交或回滚后执行的代码
            redisLockService.unlock(TPM_COST_BUDGET_IMPORTS);
          }
        }
      });
    } else {
      redisLockService.unlock(TPM_COST_BUDGET_IMPORTS);
    }

    return null;
  }

  /**
   * 重复性校验
   * 同年度-同季度-同月份-同预算科目-同预算类型下，有且只有一条生效的费用预算
   */
  private Set<String> validateRepeatabilityOnCreate(CostBudgetDto costBudget) {
    if (CollectionUtils.isEmpty(costBudget.getUniqueKeySet())) {
      return new HashSet<>();
    }
    costBudget.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    List<CostBudget> result = costBudgetRepository.findByConditions(costBudget);
    if (CollectionUtils.isEmpty(result)) {
      return new HashSet<>();
    }
    return result.stream().map(CostBudget::getUniqueKey).collect(Collectors.toSet());
  }

  @Override
  public Class<CostBudgetImportsVo> findCrmExcelVoClass() {
    return CostBudgetImportsVo.class;
  }

  @Override
  public String getTemplateCode() {
    return TPM_COST_BUDGET_IMPORTS;
  }

  @Override
  public String getTemplateName() {
    return "TPM预算费用导入";
  }

}
