package com.biz.crm.tpm.admin.web.exports.audit.model;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;

/**
 * Vo：费用核销;
 * <AUTHOR> Keller
 * @date : 2022-7-2
 */
@ApiModel(value = "Audit",description = "费用核销")
@Getter
@Setter
@CrmExcelExport
public class AuditExportsVo extends CrmExcelVo{

  /** 审批状态 */
  @CrmExcelColumn("审批状态")
  @ApiModelProperty(name = "processStatus",notes = "审批状态", value= "审批状态")
  private String processStatus;
  /** 核销申请编号 */
  @CrmExcelColumn("核销申请编号")
  @ApiModelProperty(name = "auditCode",notes = "核销申请编号", value= "核销申请编号")
  private String auditCode;
  /** 核销申请名称 */
  @CrmExcelColumn("核销申请名称")
  @ApiModelProperty(name = "auditName",notes = "核销申请名称", value= "核销申请名称")
  private String auditName;
  /** 创建人名称 */
  @CrmExcelColumn("核销申请人")
  @ApiModelProperty(name = "createName",notes = "创建人名称", value= "创建人名称")
  private String createName;
  /** 审批流程编码 */
  @CrmExcelColumn("审批流程编码")
  @ApiModelProperty(name = "processKey",notes = "审批流程编码", value= "审批流程编码")
  private String processKey;
  /** 备注 */
  @CrmExcelColumn("备注")
  @ApiModelProperty(name = "remark",notes = "备注", value= "备注")
  private String remark;
}