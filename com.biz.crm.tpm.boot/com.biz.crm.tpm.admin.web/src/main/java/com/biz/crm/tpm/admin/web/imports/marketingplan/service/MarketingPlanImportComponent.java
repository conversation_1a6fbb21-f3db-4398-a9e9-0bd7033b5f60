package com.biz.crm.tpm.admin.web.imports.marketingplan.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.biz.crm.business.common.ie.local.BusinessExcelExportTemplateWriteUtil;
import com.biz.crm.business.common.ie.local.BusinessMoreSheetImportListener;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanImportCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseImportVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanImportVo;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesTemplateConfigService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/12 16:37
 */
@Component
public class MarketingPlanImportComponent {

    @Resource
    private BusinessExcelExportTemplateWriteUtil businessExcelExportTemplateWriteUtil;

    @Resource
    private MarketingPlanImportCheckHelper helper;

    @Autowired
    private ActivitiesTemplateConfigService activitiesTemplateConfigService;


    /**
     * 读取销售计划
     *
     * @param file
     * @return
     */
    public List<MarketingSalesPlanImportVo> salesPlanList(MultipartFile file) {
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(2);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(MarketingSalesPlanTypeEnum.sales_plan.getCode());
        Validate.notNull(config, "销售计划配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = new ArrayList<>(config.getDetails());
        Map<String, String> displayMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<MarketingSalesPlanImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("销售计划").headRowNumber(2).doRead();
            list = listener.transObjectToClazz(MarketingSalesPlanImportVo.class, displayMap, filedCheckMap, MarketingSalesPlanTypeEnum.sales_plan.getDesc());
        } catch (IOException e) {
            throw new RuntimeException("销售计划Excel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        return list;
    }

    /**
     * 读取陈列
     *
     * @param file
     * @return
     */
    public List<MarketingPlanCaseImportVo> displayList(MultipartFile file) {
        //陈列
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(2);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(MarketingPlanCaseTypeEnum.display.getCode());
        Validate.notNull(config, "陈列费配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = new ArrayList<>(config.getDetails());
        Map<String, String> displayMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<MarketingPlanCaseImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("市场推广类").headRowNumber(2).doRead();
            list = listener.transObjectToClazz(MarketingPlanCaseImportVo.class, displayMap, filedCheckMap, MarketingPlanCaseTypeEnum.display.getDesc());
        } catch (IOException e) {
            throw new RuntimeException("陈列费Excel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        return list;
    }



    /**
     * 读取人员费用
     *
     * @param file
     * @return
     */
    public List<MarketingPlanCaseImportVo> staffCostList(MultipartFile file) {
        //陈列
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(2);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(MarketingPlanCaseTypeEnum.staff_cost.getCode());
        Validate.notNull(config, "人员费用配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = new ArrayList<>(config.getDetails());
        Map<String, String> staffCostMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<MarketingPlanCaseImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("人员费用").headRowNumber(2).doRead();
            list = listener.transObjectToClazz(MarketingPlanCaseImportVo.class, staffCostMap, filedCheckMap, MarketingPlanCaseTypeEnum.staff_cost.getDesc());
        } catch (IOException e) {
            throw new RuntimeException("人员费用Excel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        return list;
    }



    /**
     * 读取物料
     *
     * @param file
     * @return
     */
    public List<MarketingPlanCaseImportVo> materialList(MultipartFile file) {
        //陈列
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(2);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(MarketingPlanCaseTypeEnum.material.getCode());
        Validate.notNull(config, "周边物料配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = new ArrayList<>(config.getDetails());
        Map<String, String> displayMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<MarketingPlanCaseImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("周边物料").headRowNumber(2).doRead();
            list = listener.transObjectToClazz(MarketingPlanCaseImportVo.class, displayMap, filedCheckMap, MarketingPlanCaseTypeEnum.material.getDesc());
        } catch (IOException e) {
            throw new RuntimeException("物料Excel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        return list;
    }

    /**
     * 读取后返
     *
     * @param file
     * @return
     */
    public List<MarketingPlanCaseImportVo> backList(MultipartFile file) {
        //陈列
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(2);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(MarketingPlanCaseTypeEnum.back.getCode());
        Validate.notNull(config, "进货返利配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = new ArrayList<>(config.getDetails());
        Map<String, String> displayMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<MarketingPlanCaseImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("进货返利").headRowNumber(2).doRead();
            list = listener.transObjectToClazz(MarketingPlanCaseImportVo.class, displayMap, filedCheckMap, MarketingPlanCaseTypeEnum.back.getDesc());
        } catch (IOException e) {
            throw new RuntimeException("进货返利Excel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        return list;
    }


    /**
     * 读取随单
     *
     * @param file
     * @return
     */
    public List<MarketingPlanCaseImportVo> matchingGiftList(MultipartFile file) {
        //陈列
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(2);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(MarketingPlanCaseTypeEnum.matching_gift.getCode());
        Validate.notNull(config, "随单搭赠配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = new ArrayList<>(config.getDetails());
        Map<String, String> displayMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<MarketingPlanCaseImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("随单搭赠").headRowNumber(2).doRead();
            list = listener.transObjectToClazz(MarketingPlanCaseImportVo.class, displayMap, filedCheckMap, MarketingPlanCaseTypeEnum.matching_gift.getDesc());
        } catch (IOException e) {
            throw new RuntimeException("随单搭赠Excel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        return list;
    }


    /**
     * 读取随单
     *
     * @param file
     * @return
     */
    public List<MarketingPlanCaseImportVo> OTOList(MultipartFile file) {
        //陈列
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(2);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(MarketingPlanCaseTypeEnum.o_two_o.getCode());
        Validate.notNull(config, "O2O配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = new ArrayList<>(config.getDetails());
        Map<String, String> displayMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<MarketingPlanCaseImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("O2O").headRowNumber(2).doRead();
            list = listener.transObjectToClazz(MarketingPlanCaseImportVo.class, displayMap, filedCheckMap, MarketingPlanCaseTypeEnum.o_two_o.getDesc());
        } catch (IOException e) {
            throw new RuntimeException("O2OExcel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        return list;
    }

    /**
     * 读取固定
     *
     * @param file
     * @return
     */
    public List<MarketingPlanCaseImportVo> fixedList(MultipartFile file) {
        //陈列
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(2);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(MarketingPlanCaseTypeEnum.fixed.getCode());
        Validate.notNull(config, "其他类配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = new ArrayList<>(config.getDetails());
        Map<String, String> displayMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<MarketingPlanCaseImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("其他类").headRowNumber(2).doRead();
            list = listener.transObjectToClazz(MarketingPlanCaseImportVo.class, displayMap, filedCheckMap, MarketingPlanCaseTypeEnum.fixed.getDesc());
        } catch (IOException e) {
            throw new RuntimeException("其他类Excel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        return list;
    }


    private void splitMarketingPlanParam(List<MarketingPlanCaseImportVo> list){
        for (MarketingPlanCaseImportVo importVo : list) {
//            importVo.setCustomerCode(splitFiled(importVo.getCustomerName()));
            importVo.setTerminalCode(splitFiled(importVo.getTerminalName()));
            importVo.setCostCenterCode(splitFiled(importVo.getCostCenterName()));
            importVo.setBelongDepartmentCode(splitFiled(importVo.getBelongDepartmentName()));
            importVo.setBearDepartmentCode(splitFiled(importVo.getBearDepartmentName()));
            importVo.setDetailCode(splitFiled(importVo.getDetailName()));
            importVo.setMaterialCode(splitFiled(importVo.getMaterialName()));
            importVo.setPlacingCity(splitFiled(importVo.getPlacingCity()));
            importVo.setConditionFormula(splitFiled(importVo.getConditionFormulaName()));
            //核算商品
            importVo.setItemCodeList(splitListFiled(importVo.getItemCodeList()));
            importVo.setProductCodeList(splitListFiled(importVo.getProductCodeList()));
            importVo.setLevelCodeList(splitListFiled(importVo.getLevelCodeList()));
            //费用商品
            importVo.setFeeItemCodeList(splitListFiled(importVo.getFeeItemCodeList()));
            importVo.setFeeProductCodeList(splitListFiled(importVo.getFeeProductCodeList()));
            importVo.setFeeLevelCodeList(splitListFiled(importVo.getFeeLevelCodeList()));
            importVo.setFeeBelongItemCodeList(splitListFiled(importVo.getFeeBelongItemCodeList()));
        }
    }


    private String splitFiled(String filed) {
        if (ObjectUtils.isEmpty(filed)) return null;
        return filed.split("-")[0];
    }

    private String splitListFiled(String filed) {
        if (ObjectUtils.isEmpty(filed)) return null;
        String[] fileds = filed.split(",");
        List<String> codeList = Lists.newArrayList();
        for (String s : fileds) {
            String[] split = s.split("-");
            if (split.length > 0){
                codeList.add(split[0]);
            }
        }
        return codeList.stream().collect(Collectors.joining(","));
    }
}
