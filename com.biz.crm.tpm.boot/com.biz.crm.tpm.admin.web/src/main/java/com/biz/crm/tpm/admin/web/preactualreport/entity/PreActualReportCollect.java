package com.biz.crm.tpm.admin.web.preactualreport.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/20 16:47
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_pre_actual_report_collect")
@Table(
        name = "tpm_pre_actual_report_collect",
        indexes = {
                @Index(name = "tpm_pre_actual_report_collect_index0", columnList = "scheme_code"),
                @Index(name = "tpm_pre_actual_report_collect_index1", columnList = "customer_code,customer_name"),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_pre_actual_report_collect", comment = "预实报表汇总")
@ApiModel(value = "PreActualReportCollect", description = "预实报表汇总")
public class PreActualReportCollect extends TenantFlagOpEntity {

    @ApiModelProperty("中心编码")
    @Column(name = "center_code", columnDefinition = "varchar(32) comment '中心编码'")
    private String centerCode;

    @ApiModelProperty("中心名称")
    @Column(name = "center_name", columnDefinition = "varchar(64) comment '中心名称'")
    private String centerName;

    @ApiModelProperty("一级部门编码")
    @Column(name = "region_code", columnDefinition = "varchar(32) comment '一级部门编码'")
    private String regionCode;

    @ApiModelProperty("一级部门名称")
    @Column(name = "region_name", columnDefinition = "varchar(64) comment '一级部门名称'")
    private String regionName;

    @ApiModelProperty("二级部门编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '二级部门编码'")
    private String orgCode;

    @ApiModelProperty("二级部门名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '二级部门名称'")
    private String orgName;

    @ApiModelProperty("申请人账号")
    @Column(name = "apply_account", columnDefinition = "varchar(32) comment '申请人账号'")
    private String applyAccount;

    @ApiModelProperty("申请人名称")
    @Column(name = "apply_name", columnDefinition = "varchar(64) comment '申请人名称'")
    private String applyName;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("申请单号")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '申请单号'")
    private String schemeCode;

    @ApiModelProperty("申请状态")
    @Column(name = "process_status", columnDefinition = "varchar(10) comment '申请状态'")
    private String processStatus;

    @ApiModelProperty("计提状态")
    @Column(name = "withholding_status", columnDefinition = "varchar(10) comment '计提状态'")
    private String withholdingStatus;

    @ApiModelProperty("费用期间")
    @Column(name = "act_years", columnDefinition = "varchar(20) comment '费用期间'")
    private String actYears;

    @ApiModelProperty("规划收入")
    @Column(name = "plan_income", columnDefinition = "decimal(18,2) comment '规划收入'")
    private BigDecimal planIncome;

    @ApiModelProperty("计提收入")
    @Column(name = "withholding_income", columnDefinition = "decimal(18,2) comment '计提收入'")
    private BigDecimal withholdingIncome;

    @ApiModelProperty("收入达成率")
    @Column(name = "income_achieve_ratio", columnDefinition = "decimal(18,4) comment '收入达成率'")
    private BigDecimal incomeAchieveRatio;

    @ApiModelProperty("收入达成率")
    @Column(name = "income_achieve_ratio_str", columnDefinition = "varchar(40) comment '收入达成率'")
    private String incomeAchieveRatioStr;

    @ApiModelProperty("规划毛利率")
    @Column(name = "plan_gross_profit_margin", columnDefinition = "decimal(18,4) comment '规划毛利率'")
    private BigDecimal planGrossProfitMargin;

    @ApiModelProperty("规划毛利率")
    @Column(name = "plan_gross_profit_margin_str", columnDefinition = "varchar(40) comment '规划毛利率'")
    private String planGrossProfitMarginStr;

    @ApiModelProperty("计提毛利率")
    @Column(name = "withholding_gross_profit_margin", columnDefinition = "decimal(18,4) comment '计提毛利率'")
    private BigDecimal withholdingGrossProfitMargin;

    @ApiModelProperty("计提毛利率")
    @Column(name = "withholding_gross_profit_margin_str", columnDefinition = "varchar(40) comment '计提毛利率'")
    private String withholdingGrossProfitMarginStr;

    @ApiModelProperty("毛利率偏差")
    @Column(name = "gross_profit_margin_difference", columnDefinition = "decimal(18,4) comment '毛利率偏差'")
    private BigDecimal grossProfitMarginDifference;

    @ApiModelProperty("毛利率偏差")
    @Column(name = "gross_profit_margin_difference_str", columnDefinition = "varchar(40) comment '毛利率偏差'")
    private String grossProfitMarginDifferenceStr;

    @ApiModelProperty("规划费率")
    @Column(name = "plan_cost_ratio", columnDefinition = "decimal(18,4) comment '规划费率'")
    private BigDecimal planCostRatio;

    @ApiModelProperty("规划费率")
    @Column(name = "plan_cost_ratio_str", columnDefinition = "varchar(40) comment '规划费率'")
    private String planCostRatioStr;

    @ApiModelProperty("计提费率")
    @Column(name = "withholding_cost_ratio", columnDefinition = "decimal(18,4) comment '计提费率'")
    private BigDecimal withholdingCostRatio;

    @ApiModelProperty("计提费率")
    @Column(name = "withholding_cost_ratio_str", columnDefinition = "varchar(40) comment '计提费率'")
    private String withholdingCostRatioStr;

    @ApiModelProperty("费率偏差")
    @Column(name = "cost_ratio_difference", columnDefinition = "decimal(18,4) comment '费率偏差'")
    private BigDecimal costRatioDifference;

    @ApiModelProperty("费率偏差")
    @Column(name = "cost_ratio_difference_str", columnDefinition = "varchar(40) comment '费率偏差'")
    private String costRatioDifferenceStr;

    @ApiModelProperty("规划利润率")
    @Column(name = "plan_profit_ratio", columnDefinition = "decimal(18,4) comment '规划利润率'")
    private BigDecimal planProfitRatio;

    @ApiModelProperty("规划利润率")
    @Column(name = "plan_profit_ratio_str", columnDefinition = "varchar(40) comment '规划利润率'")
    private String planProfitRatioStr;

    @ApiModelProperty("计提利润率")
    @Column(name = "withholding_profit_ratio", columnDefinition = "decimal(18,4) comment '计提利润率'")
    private BigDecimal withholdingProfitRatio;

    @ApiModelProperty("计提利润率")
    @Column(name = "withholding_profit_ratio_str", columnDefinition = "varchar(40) comment '计提利润率'")
    private String withholdingProfitRatioStr;

    @ApiModelProperty("利润率偏差")
    @Column(name = "profit_ratio_difference", columnDefinition = "decimal(18,4) comment '利润率偏差'")
    private BigDecimal profitRatioDifference;

    @ApiModelProperty("利润率偏差")
    @Column(name = "profit_ratio_difference_str", columnDefinition = "varchar(40) comment '利润率偏差'")
    private String profitRatioDifferenceStr;

    @ApiModelProperty("结果值")
    @Column(name = "json_str", columnDefinition = "text comment '结果值'")
    private String jsonStr;

}
