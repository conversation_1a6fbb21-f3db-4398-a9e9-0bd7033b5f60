package com.biz.crm.tpm.admin.web.exports.activitiesscheme.mapper;

import com.biz.crm.tpm.admin.web.exports.activitiesscheme.model.ActivitiesSchemeExportsDto;
import com.biz.crm.tpm.admin.web.exports.activitiesscheme.model.ActivitiesSchemeExportsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @describe: 方案活动导出mapper
 * @createTime 2022年07月01日 15:17:00
 */
public interface ActivitiesSchemeExportsMapper {
  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getExportTotal(@Param("dto") ActivitiesSchemeExportsDto dto);
  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<ActivitiesSchemeExportsVo> findData(@Param("dto") ActivitiesSchemeExportsDto dto);
}
