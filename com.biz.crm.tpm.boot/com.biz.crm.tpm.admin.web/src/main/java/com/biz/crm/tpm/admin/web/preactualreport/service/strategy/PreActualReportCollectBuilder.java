package com.biz.crm.tpm.admin.web.preactualreport.service.strategy;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/20 20:58
 **/
public abstract class PreActualReportCollectBuilder<RegionCaseMap, PlanMap> {

    protected RegionCaseMap regionCaseMap;

    protected PlanMap planMap;

    public PreActualReportCollectBuilder<RegionCaseMap, PlanMap> init(RegionCaseMap regionCaseMap, PlanMap planMap) {
        this.regionCaseMap = regionCaseMap;
        this.planMap = planMap;
        return this;
    }

    /**
     * 构建基础数据
     * @return
     */
    public abstract PreActualReportCollectBuilder buildBaseData();

    /**
     * 构建计提状态
     * @return
     */
    public abstract PreActualReportCollectBuilder buildWithholdingStatus();

    /**
     * 构建规划收入
     * @return
     */
    public abstract PreActualReportCollectBuilder buildPlanIncome();

    /**
     * 构建计提收入
     * @return
     */
    public abstract PreActualReportCollectBuilder buildWithholdingIncome();

    /**
     * 构建达成率
     * @return
     */
    public abstract PreActualReportCollectBuilder buildAchieveRatio();

    /**
     * 构建规划毛利率
     * @return
     */
    public abstract PreActualReportCollectBuilder buildPlanGrossProfitMargin();

    /**
     * 构建计提毛利率
     * @return
     */
    public abstract PreActualReportCollectBuilder buildWithholdingGrossProfitMargin();

    /**
     * 构建毛利率偏差
     * @return
     */
    public abstract PreActualReportCollectBuilder grossProfitMarginDifference();

    /**
     * 构建规划费率
     * @return
     */
    public abstract PreActualReportCollectBuilder planCostRatio();

    /**
     * 构建计提费率
     * @return
     */
    public abstract PreActualReportCollectBuilder withholdingCostRatio();

    /**
     * 构建费率偏差
     * @return
     */
    public abstract PreActualReportCollectBuilder costRatioDifference();

    /**
     * 构建规划利润率
     * @return
     */
    public abstract PreActualReportCollectBuilder planProfitRatio();

    /**
     * 构建计提利润率
     * @return
     */
    public abstract PreActualReportCollectBuilder withholdingProfitRatio();

    /**
     * 构建利润率偏差
     * @return
     */
    public abstract PreActualReportCollectBuilder profitRatioDifference();
}
