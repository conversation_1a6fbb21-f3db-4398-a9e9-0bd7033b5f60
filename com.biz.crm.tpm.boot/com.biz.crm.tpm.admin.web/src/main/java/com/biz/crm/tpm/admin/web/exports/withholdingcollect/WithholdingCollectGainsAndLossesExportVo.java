package com.biz.crm.tpm.admin.web.exports.withholdingcollect;

import com.biz.crm.tpm.admin.web.exports.regioncollect.model.CustomerGainsAndLossesExportBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:24
 */
@Data
@ApiModel(value = "RegionCollectGainsAndLossesVo", description = "计提汇总-客户损益预测")
public class WithholdingCollectGainsAndLossesExportVo extends CustomerGainsAndLossesExportBaseVo {


    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("渠道类型")
    private String channelType;
}
