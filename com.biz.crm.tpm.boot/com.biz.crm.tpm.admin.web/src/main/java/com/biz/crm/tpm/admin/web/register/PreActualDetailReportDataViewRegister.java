package com.biz.crm.tpm.admin.web.register;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22 22:50
 **/
@Component
public class PreActualDetailReportDataViewRegister implements DataviewRegister {
    @Override
    public String code() {
        return "tpm_pre_actual_detail_report";
    }

    @Override
    public String desc() {
        return "TPM预实明细报表";
    }

    @Override
    public String buildSql() {
        return "SELECT	"+
                "	*	"+
                "FROM	"+
                "	tpm_pre_actual_detail_report	"+
                "WHERE	"+
                "	tenant_code = :tenantCode	"+
                "ORDER BY	"+
                "	customer_code,	"+
                "	act_years,	"+
                "	org_code,	"+
                "	sort	";
    }
}
