package com.biz.crm.tpm.admin.web.register.form;

import com.biz.crm.tpm.business.budget.sdk.register.FormDimension;
import com.biz.crm.tpm.business.budget.sdk.register.FormEventRegister;
import com.google.common.collect.Sets;
import org.springframework.context.annotation.Configuration;

import java.util.Collection;

/**
 * 描述：</br>tpm 表单驱动配置注册器
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Configuration
public class TpmFormEventRegister implements FormEventRegister {

  /**
   * 注册支持的表单策略
   *
   * @return
   */
  @Override
  public Collection<Class<? extends FormDimension>> getFormDimensions() {
    return Sets.newHashSet(EventApplicationFormDimension.class, EventApprovedFormDimension.class, EventExecutionFormDimension.class);
  }
}
