package com.biz.crm.tpm.admin.web.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.ryytn.RySignHeaderUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/10 22:06
 */
@Service
@Slf4j
public class DefaultFormulaServiceImpl {

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private UrlApiService urlApiService;

    @Resource
    private ExternalLogVoService externalLogVoService;

    public void pushFormulaToOA() {
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_OA_ACCOUNT);
        //获取登陆信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        String url = urlAddressVo.getUrl();
        String systemId = urlAddressVo.getEnvironment();
        String password = urlAddressVo.getRefreshKey();
        String interfaceAddress = String.format(RyConstant.OA_PUSH_REBATE_FORMULA, urlAddressVo.getBusinessKey());
        String currentDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String md5 = getMD5Str(systemId + password + currentDateTime).toLowerCase();

        JSONObject headObject = new JSONObject();
        headObject.put("systemid", systemId);
        headObject.put("currentDateTime", currentDateTime);
        headObject.put("Md5", md5);
        //组装参数
        JSONObject jsonObject = new JSONObject();
        //封装mainTable参数
        JSONObject mainTable = new JSONObject();
        mainTable.put("zt", "0");
        mainTable.put("zcxsmc", "OA测试");
        mainTable.put("zcxsbm", "FLGS2024071000001");
        jsonObject.put("mainTable", mainTable);
        LocalDateTime now = LocalDateTime.now();
        //封装operationinfo参数
        JSONObject operationinfo = new JSONObject();
        operationinfo.put("operator", loginDetails.getAccount());
        operationinfo.put("operationDate", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        operationinfo.put("operationTime", now.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        jsonObject.put("operationinfo", operationinfo);

        JSONObject dataObject = new JSONObject();
        dataObject.put("header", headObject);
        dataObject.put("data", Lists.newArrayList(jsonObject));
        //请求参数
        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("datajson", JSONObject.toJSONString(dataObject));

        Map<String, String> headMap = RySignHeaderUtil.getSignHeadMap(urlAddressVo.getAccessId(), urlAddressVo.getSecretKey(), interfaceAddress);
        //组装请求参数放到日志
        ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(JSONObject.toJSONString(reqMap), urlAddressVo);
        logDetailDto.setReqHead(JSONObject.toJSONString(headMap));
        logDetailDto.setReqJson(JSONObject.toJSONString(reqMap));
        logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf("/") + 1));
        logDetailDto.setRequestUri(interfaceAddress);
        logDetailDto.setMethodMsg("推送返利公式到OA");
        externalLogVoService.addOrUpdateLog(logDetailDto, true);
        Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, null, headMap, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
                5, 120, reqMap);
        ExternalLogUtil.buildLogResult(logDetailDto, result);
        String data = null;
        try {
            data = checkResult(result);
            logDetailDto.setStatus(ExternalLogGlobalConstants.S);
            logDetailDto.setTipMsg(data);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            logDetailDto.setTipMsg(e.getMessage());
            logDetailDto.setExceptionStack(e.getMessage());
            logDetailDto.setStatus(ExternalLogGlobalConstants.E);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
            throw e;
        }
    }


    private String getMD5Str(String plainText) {
        //定义一个字节数组
        byte[] secretBytes = null;
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            //对字符串进行加密
            md.update(plainText.getBytes());
            //获得加密后的数据
            secretBytes = md.digest();
        } catch (NoSuchAlgorithmException e) {
            //throw new RuntimeException("没有md5这个算法！");
            throw new RuntimeException("没有MD5");
        }
        //将加密后的数据转换为16进制数字
        String md5code = new BigInteger(1, secretBytes).toString(16);
        // 如果生成数字未满32位，需要前面补0
        // 不能把变量放到循环条件，值改变之后会导致条件变化。如果生成30位 只能生成31位md5
        int tempIndex = 32 - md5code.length();
        for (int i = 0; i < tempIndex; i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    /**
     * 校验推送结果
     *
     * @param result
     */
    private String checkResult(Result<String> result) {
        Assert.isTrue(result.isSuccess(), "推送OA返回提示:" + result.getMessage());
        Assert.hasLength(result.getResult(), "推送OA返回信息为空:" + result.getMessage());
        JSONObject jsonObject = JSONObject.parseObject(result.getResult());
        Validate.isTrue(jsonObject.containsKey("status"), "推送OA返利公式失败");
        Validate.isTrue(jsonObject.get("status").equals(RyConstant.OS_SUCCESS_CODE), "推送OA返利公式失败");
        return jsonObject.toJSONString();
    }
}
