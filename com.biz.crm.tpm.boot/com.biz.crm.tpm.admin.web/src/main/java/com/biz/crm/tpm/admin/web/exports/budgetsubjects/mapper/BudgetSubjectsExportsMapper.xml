<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.admin.web.exports.budgetsubjects.mapper.BudgetSubjectsExportsMapper">


  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from  tpm_budget_subjects t
    <where>
      t.del_flag = #{dto.delFlag}
      <if test="dto.budgetSubjectsCode != null and dto.budgetSubjectsCode != ''">
        <bind name="budgetSubjectsCode" value="'%' + dto.budgetSubjectsCode + '%'"/>
        and t.budget_subjects_code like #{budgetSubjectsCode}
      </if>
      <if test="dto.budgetSubjectsName != null and dto.budgetSubjectsName != ''">
        <bind name="budgetSubjectsName" value="'%' + dto.budgetSubjectsName + '%'"/>
        and t.budget_subjects_name like #{budgetSubjectsName}
      </if>
      <if test="dto.budgetSubjectsType != null and dto.budgetSubjectsType != ''">
        and t.budget_subjects_type = #{dto.budgetSubjectsType}
      </if>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
    </where>
  </select>


  <select id="findData"
          resultType="com.biz.crm.tpm.admin.web.exports.budgetsubjects.model.BudgetSubjectsExportsVo">
    select *
    from tpm_budget_subjects t
    <where>
      t.del_flag = #{dto.delFlag}
      <if test="dto.budgetSubjectsCode != null and dto.budgetSubjectsCode != ''">
        <bind name="budgetSubjectsCode" value="'%' + dto.budgetSubjectsCode + '%'"/>
        and t.budget_subjects_code like #{budgetSubjectsCode}
      </if>
      <if test="dto.budgetSubjectsName != null and dto.budgetSubjectsName != ''">
        <bind name="budgetSubjectsName" value="'%' + dto.budgetSubjectsName + '%'"/>
        and t.budget_subjects_name like #{budgetSubjectsName}
      </if>
      <if test="dto.budgetSubjectsType != null and dto.budgetSubjectsType != ''">
        and t.budget_subjects_type = #{dto.budgetSubjectsType}
      </if>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
    </where>
    order by t.create_time desc,t.id
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>