package com.biz.crm.tpm.admin.web.controller.marketingplan;

import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Author: haiyang
 * @Date: 2025-03-19 15:47
 * @Desc:
 */
@RestController
@RequestMapping("/v1/nearExpiredMarketingPlanCaseWarning")
@Api(tags = "临期营销方案明细提醒")
public class NearExpiredMarketingPlanCaseWarningController {

    @Autowired
    private MarketingAuditService marketingAuditService;

    @ApiOperation(value = "定时任务-活动结束第80天推送未完全结案")
    @GetMapping("scheduleNotClosureDetailMsgPush")
    public Result scheduleNotClosureDetailMsgPush(){
        marketingAuditService.scheduleNotClosureDetailMsgPush();
        return Result.ok();
    }

    @ApiOperation(value = "定时任务-活动结束第170天推送未发起兑付")
    @GetMapping("scheduleNotReplenishmentMsgPush")
    public Result scheduleNotReplenishmentMsgPush(){
        marketingAuditService.scheduleNotReplenishmentMsgPush();
        return Result.ok();
    }
}
