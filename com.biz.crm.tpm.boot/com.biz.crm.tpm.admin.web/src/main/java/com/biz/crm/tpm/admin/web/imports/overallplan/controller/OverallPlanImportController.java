package com.biz.crm.tpm.admin.web.imports.overallplan.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.biz.crm.business.common.ie.local.BusinessMoreSheetImportListener;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.imports.overallplan.model.HeadOverallPlanImportVo;
import com.biz.crm.tpm.admin.web.imports.overallplan.service.HeadOverallPlanImportsProcess;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesTemplateConfigService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/21 03:53
 */
@RestController
@RequestMapping("/v1/overallPlanImportController")
public class OverallPlanImportController {

    @Autowired
    private ActivitiesTemplateConfigService activitiesTemplateConfigService;

    @Resource
    private HeadOverallPlanImportsProcess headOverallPlanImportsProcess;

    @PostMapping("importHeadOverallPlanCase")
    @ApiOperation(value = "导入总部指引")
    public Result importHeadOverallPlanCase(@RequestParam MultipartFile file, @RequestParam String cacheKey) {
        BusinessMoreSheetImportListener listener = new BusinessMoreSheetImportListener(0);
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(OverallPlanConstant.HEAD_OVERALL_PLAN_TEMPLATE);
        Validate.notNull(config, "总部指引配置模版为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = config.getDetails().stream().collect(Collectors.toList());
        Map<String, String> displayMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
        Map<String, Boolean> filedCheckMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getRequired));
        List<HeadOverallPlanImportVo> list = Lists.newArrayList();
        try {
            InputStream io = file.getInputStream();
            EasyExcel.read(io, listener).sheet("总部指引").headRowNumber(2).doRead();
            list = listener.transObjectToClazz(HeadOverallPlanImportVo.class, displayMap, filedCheckMap, "总部指引");
        } catch (IOException e) {
            throw new RuntimeException("总部指引Excel解析失败...");
        } catch (ExcelAnalysisException e) {
            throw new RuntimeException(e.getCause().getMessage());
        }
        headOverallPlanImportsProcess.checkData(list, cacheKey);
        return Result.ok();
    }
}
