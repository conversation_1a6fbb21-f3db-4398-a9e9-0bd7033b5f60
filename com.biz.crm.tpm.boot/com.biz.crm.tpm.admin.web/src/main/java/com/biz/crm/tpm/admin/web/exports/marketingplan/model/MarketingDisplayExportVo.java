package com.biz.crm.tpm.admin.web.exports.marketingplan.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description  TODO 别乱动顺序！！！
 * <AUTHOR>
 * @Date 2024/8/13 21:44
 */
@ApiModel("到店费用")
@Data
public class MarketingDisplayExportVo implements Serializable {

    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("关联统筹方案名称")
    private String releaseName;

    @ApiModelProperty("客户编码")
    private String erpCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    private String companyCode;

    @ApiModelProperty("终端编码")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("陈列卡板数")
    private BigDecimal displayCardNum;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("多部门承担标记")
    private String muchDepartmentMark;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("核算品项范围-前端使用")
    private String itemCodeList;

    private String itemNameList;

    @ApiModelProperty("申请费用")
    private BigDecimal applyAmount;

    @ApiModelProperty("兑付方式")
    private String cashType;

    @ApiModelProperty("执行描述")
    private String executeDesc;

    @ApiModelProperty("活动描述")
    private String actDesc;

    @ApiModelProperty("费用依据集合")
    private String costBasisNameList;

    @ApiModelProperty("错误描述")
    private String errMsg;
}
