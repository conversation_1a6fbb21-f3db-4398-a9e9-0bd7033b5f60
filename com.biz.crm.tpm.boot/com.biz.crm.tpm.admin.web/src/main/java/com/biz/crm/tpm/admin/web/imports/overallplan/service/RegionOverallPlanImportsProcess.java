package com.biz.crm.tpm.admin.web.imports.overallplan.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.BusinessBeanUtils;
import com.biz.crm.business.common.ie.sdk.process.BusinessImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.admin.web.imports.overallplan.model.RegionOverallPlanImportVo;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.helper.OverallPlanPageCacheHelper;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanDepartmentVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanProductVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanScopeVo;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesTemplateConfigService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/23
 */
@Component
@Slf4j
public class RegionOverallPlanImportsProcess extends BusinessImportProcess<RegionOverallPlanImportVo> {

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private OverallPlanPageCacheHelper pageCacheHelper;

    @Resource
    private ActivitiesTemplateConfigService activitiesTemplateConfigService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Override
    public Integer getHeadTitleRowIndex() {
        return 1;
    }

    @Override
    public Map<Integer, String> analysisHeadFieldMap(Map<String, Object> params, Map<Integer, String> headMap) {
        return null;
    }

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, RegionOverallPlanImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<String, String> headOverallMap = getHeadFiledMap();
        String cacheKey = String.valueOf(params.get("cacheKey"));

        Optional<RegionOverallPlanImportVo> optional = data.values().stream().findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        Map<String, Object> headFiledMap = optional.get().getHeadFiledMap();
        Map<String, Object> map = Maps.newHashMap();
        for (Map.Entry<String, Object> entry : headFiledMap.entrySet()) {
            Validate.isTrue(headOverallMap.containsKey(entry.getKey()), String.format("未配置字段%s", StringUtils.stripToEmpty(entry.getKey())));
            String filed = headOverallMap.get(entry.getKey());
            map.put(filed, entry.getValue());
        }

        List<RegionOverallPlanImportVo> importList = BusinessBeanUtils.mapListToBeanList(Lists.newArrayList(map), RegionOverallPlanImportVo.class, Boolean.TRUE);

        RegionOverallPlanImportVo importVo = importList.get(0);
        JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(importVo));
        //优先处理string转list的
        //承接销售部门列表
        if (ObjectUtils.isNotEmpty(importVo.getBearDepartmentCodeList())) {
            List<String> bearDepartmentCodeList = Arrays.asList(importVo.getBearDepartmentCodeList().split(","));
            List<OverallPlanDepartmentVo> bearDepartmentList = bearDepartmentCodeList.stream().map(x -> {
                OverallPlanDepartmentVo departmentVo = new OverallPlanDepartmentVo();
                departmentVo.setDepartmentCode(x);
                return departmentVo;
            }).collect(Collectors.toList());
            jsonObject.put("bearDepartmentList", bearDepartmentList);
        } else {
            jsonObject.remove("bearDepartmentList");
        }
        //客户
        if (ObjectUtils.isNotEmpty(importVo.getCustomerCodeList())) {
            List<String> list = Arrays.asList(importVo.getCustomerCodeList().split(","));
            List<OverallPlanScopeVo> customerList = list.stream().map(x -> {
                OverallPlanScopeVo departmentVo = new OverallPlanScopeVo();
                departmentVo.setCustomerCode(x);
                return departmentVo;
            }).collect(Collectors.toList());
            jsonObject.put("customerList", customerList);
        } else {
            jsonObject.remove("customerList");
        }
        //终端
        if (ObjectUtils.isNotEmpty(importVo.getTerminalCodeList())) {
            List<String> list = Arrays.asList(importVo.getTerminalCodeList().split(","));
            List<OverallPlanScopeVo> terminalList = list.stream().map(x -> {
                OverallPlanScopeVo departmentVo = new OverallPlanScopeVo();
                departmentVo.setTerminalCode(x);
                return departmentVo;
            }).collect(Collectors.toList());
            jsonObject.put("terminalList", terminalList);
        } else {
            jsonObject.remove("terminalList");
        }
        //品项
        if (ObjectUtils.isNotEmpty(importVo.getItemCodeList())) {
            List<String> list = Arrays.asList(importVo.getItemCodeList().split(","));
            List<OverallPlanProductVo> itemList = list.stream().map(x -> {
                OverallPlanProductVo departmentVo = new OverallPlanProductVo();
                departmentVo.setCode(x);
                return departmentVo;
            }).collect(Collectors.toList());
            jsonObject.put("itemList", itemList);
        } else {
            jsonObject.remove("itemList");
        }
        //商品
        if (ObjectUtils.isNotEmpty(importVo.getProductCodeList())) {
            List<String> list = Arrays.asList(importVo.getProductCodeList().split(","));
            List<OverallPlanProductVo> productList = list.stream().map(x -> {
                OverallPlanProductVo departmentVo = new OverallPlanProductVo();
                departmentVo.setCode(x);
                return departmentVo;
            }).collect(Collectors.toList());
            jsonObject.put("productList", productList);
        } else {
            jsonObject.remove("productList");
        }
        //合作类型
        if (ObjectUtils.isNotEmpty(importVo.getCooperateTypeList())) {
            List<String> list = Arrays.asList(importVo.getCooperateTypeList().split(","));
            Map<String, String> cooperateMap = cooperateTypeMap();
            List<String> cooperateTypeList = list.stream().map(x -> {
                if (cooperateMap.containsKey(x)) {
                    return cooperateMap.get(x);
                }
                return x;
            }).collect(Collectors.toList());
            jsonObject.put("cooperateTypeList", cooperateTypeList);
        } else {
            jsonObject.remove("cooperateTypeList");
        }
        //客户标签
        if (ObjectUtils.isNotEmpty(importVo.getCustomerTagList())) {
            List<String> list = Arrays.asList(importVo.getCustomerTagList().split(","));
            Map<String, String> customerTagMap = customerTagMap();
            List<String> customerTagList = list.stream().map(x -> {
                if (customerTagMap.containsKey(x)) {
                    return customerTagMap.get(x);
                }
                return x;
            }).collect(Collectors.toList());
            jsonObject.put("customerTagList", customerTagList);
        } else {
            jsonObject.remove("customerTagList");
        }
        //终端标签
        if (ObjectUtils.isNotEmpty(importVo.getTerminalTagList())) {
            List<String> list = Arrays.asList(importVo.getTerminalTagList().split(","));
            Map<String, String> terminalTagMap = customerTagMap();
            List<String> terminalTagList = list.stream().map(x -> {
                if (terminalTagMap.containsKey(x)) {
                    return terminalTagMap.get(x);
                }
                return x;
            }).collect(Collectors.toList());
            jsonObject.put("terminalTagList", terminalTagList);
        } else {
            jsonObject.remove("customerTagList");
        }
        if (ObjectUtils.isNotEmpty(importVo.getApplyAmount())) {
            try {
                jsonObject.put("applyAmount", new BigDecimal(importVo.getApplyAmount()));
            } catch (Exception e) {
                throw new IllegalArgumentException("预估费用只能是数字");
            }
        }
        if (ObjectUtils.isNotEmpty(importVo.getEstimatedSalesVolume())) {
            try {
                jsonObject.put("estimatedSalesVolume", new BigDecimal(importVo.getEstimatedSalesVolume()));
            } catch (Exception e) {
                throw new IllegalArgumentException("预估销量只能是数字");
            }
        }
        OverallPlanCaseVo vo = JSONObject.toJavaObject(jsonObject, OverallPlanCaseVo.class);
        pageCacheHelper.importNewItem(cacheKey, Lists.newArrayList(vo), OverallPlanSchemeTypeEnum.REGION.getCode());
        return null;
    }

    @Override
    public Class<RegionOverallPlanImportVo> findCrmExcelVoClass() {
        return RegionOverallPlanImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "TPM_REGION_OVERALL_PLAN_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "TPM-大区统筹方案明细导入";
    }


    //合作类型
    private final static ThreadLocal<Map<String, String>> cooperateTypeThreadLocal = new ThreadLocal<>();
    //客户标签
    private final static ThreadLocal<Map<String, String>> customerTagThreadLocal = new ThreadLocal<>();
    //终端标签
    private final static ThreadLocal<Map<String, String>> terminalTagThreadLocal = new ThreadLocal<>();
    //活动配置模板
    private final static ThreadLocal<Map<String, String>> templateThreadLocal = new ThreadLocal<>();


    /**
     * 合作类型标签
     *
     * @return
     */
    private Map<String, String> cooperateTypeMap() {
        Map<String, String> map = cooperateTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_COOPERATE_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            cooperateTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 客户标签
     *
     * @return
     */
    private Map<String, String> customerTagMap() {
        Map<String, String> map = customerTagThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.CUSTOMER_TAG);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            customerTagThreadLocal.set(map);
        }
        return map;
    }


    private Map<String, String> terminalTagMap() {
        Map<String, String> map = terminalTagThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TERMINAL_TAG);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            terminalTagThreadLocal.set(map);
        }
        return map;
    }

    public Map<String, String> getHeadFiledMap() {
        Map<String, String> headOverallMap = templateThreadLocal.get();
        if (ObjectUtils.isEmpty(headOverallMap)) {
            ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(OverallPlanConstant.REGION_OVERALL_PLAN_TEMPLATE);
            Validate.notNull(config, "大区指引配置模版为空");
            List<ActivitiesTemplateConfigDetailVo> detailList = config.getDetails().stream().collect(Collectors.toList());
            headOverallMap = detailList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getTitle, ActivitiesTemplateConfigDetailVo::getField));
            templateThreadLocal.set(headOverallMap);
        }
        return headOverallMap;
    }
}
