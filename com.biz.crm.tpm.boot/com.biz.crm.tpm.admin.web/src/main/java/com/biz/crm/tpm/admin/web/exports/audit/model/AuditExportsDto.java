package com.biz.crm.tpm.admin.web.exports.audit.model;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 参数传递dto：费用核销;
 * <AUTHOR> Keller
 * @date : 2022-7-2
 */
@ApiModel(value = "Audit",description = "费用核销")
@Getter
@Setter
public class AuditExportsDto extends TenantDto implements Serializable,Cloneable{
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 数据业务状态（启用状态） */
  @ApiModelProperty(name = "enableStatus",notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
  private String enableStatus;
  /** 审批状态 */
  @ApiModelProperty(name = "processStatus",notes = "审批状态", value = "审批状态")
  private String processStatus;
  /** 核销申请名称 */
  @ApiModelProperty(name = "auditName",notes = "核销申请名称", value = "核销申请名称")
  private String auditName;
  /** 核销申请编号 */
  @ApiModelProperty(name = "auditCode",notes = "核销申请编号", value = "核销申请编号")
  private String auditCode;

  /** 偏移量 */
  @ApiModelProperty(name = "offset",notes = "偏移量", value = "偏移量")
  private Integer offset;

  /** limit */
  @ApiModelProperty(name = "limit",notes = "limit", value = "limit") 
  private Integer limit;
}