package com.biz.crm.tpm.admin.web.vo;

import com.biz.crm.business.common.base.vo.ExportWriteMdmVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/25 01:14
 */
@Data
public class ProductPhaseWriteImportVo extends ExportWriteMdmVo {

    @ApiModelProperty("品相编码")
    private String productPhaseCode;

    @ApiModelProperty("品相名称")
    private String productPhaseName;

    @ApiModelProperty("零级编码")
    private String zeroLevelCode;

    @ApiModelProperty("零级名称")
    private String zeroLevelName;

    @ApiModelProperty("是否分摊;字典编码[yesOrNo]")
    private String canShare;

}
