package com.biz.crm.tpm.admin.web.alibaba;

import cn.hutool.core.collection.CollectionUtil;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.PropertyValueConst;
import com.aliyun.openservices.ons.api.bean.OrderConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import com.biz.crm.business.common.rocketmq.config.RocketMqConfig;
import com.biz.crm.business.common.rocketmq.config.condition.AlibabaCondition;
import com.biz.crm.business.common.rocketmq.listener.OrderMessageListener;
import com.biz.crm.business.common.rocketmq.util.AlibabaMqTopicAndTagUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.Properties;
import java.util.Set;

/**
 * 阿里 rocketMq 消费者
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/29 10:33
 */
@Conditional(AlibabaCondition.class)
@Configuration
@Slf4j
public class RocketMqOrderConsumerClient {

    @Autowired(required = false)
    private RocketMqConfig rocketMqConfig;

    @Autowired(required = false)
    private OrderMessageListener orderMessageListener;

    @Bean(name = "orderConsumerClientTpm", initMethod = "start", destroyMethod = "shutdown")
    public OrderConsumerBean consumerClient() {
        OrderConsumerBean orderConsumerBean = new OrderConsumerBean();
        //配置文件
        Properties properties = rocketMqConfig.getMqProperty();
        //集群模式  默认集群模式
        properties.setProperty(PropertyKeyConst.MessageModel, PropertyValueConst.CLUSTERING);
        Assert.hasLength(rocketMqConfig.getOrderGroupId(), "消费者GroupId不能为空!");
        properties.setProperty(PropertyKeyConst.GROUP_ID, rocketMqConfig.getOrderGroupId());
        //是否每次请求都带上最新的订阅关系
        properties.setProperty(PropertyKeyConst.PostSubscriptionWhenPull, "true");
        //将消费者线程数固定为20个 20为默认值
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "20");

        orderConsumerBean.setProperties(properties);
        //订阅关系
        Map<Subscription, MessageOrderListener> subscriptionTable = Maps.newHashMap();

        /**
         * 监听  topic => tag 方法 set  顺序
         */
        Map<String, Map<String, Set<String>>> topicMap = AlibabaMqTopicAndTagUtil.getTopicOrderMap();
        if (CollectionUtil.isNotEmpty(topicMap)) {
            topicMap.forEach((topic, tagServiceMap) -> {
                if (CollectionUtil.isNotEmpty(tagServiceMap)) {
                    //有序队列
                    Subscription orderSubscription = new Subscription();
                    orderSubscription.setTopic(topic);
                    //订阅Tag。
                    orderSubscription.setExpression(String.join("||", tagServiceMap.keySet()));
                    subscriptionTable.put(orderSubscription, orderMessageListener);
                }
            });

        }

        //订阅多个topic如上面设置
        if (CollectionUtil.isNotEmpty(subscriptionTable)) {
            log.info("=====>阿里云rocketMq启用顺序消费者{}", subscriptionTable);
            orderConsumerBean.setSubscriptionTable(subscriptionTable);
            orderConsumerBean.start();
            return orderConsumerBean;
        } else {
            log.warn("=====>阿里云rocketMq无顺序消费者");
            orderConsumerBean.shutdown();
            return null;
        }
    }

}
