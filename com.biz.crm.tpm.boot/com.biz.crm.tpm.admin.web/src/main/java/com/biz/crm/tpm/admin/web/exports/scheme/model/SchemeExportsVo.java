package com.biz.crm.tpm.admin.web.exports.scheme.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Vo：方案;
 *
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */
@ApiModel(value = "Scheme", description = "方案")
@Getter
@Setter
@CrmExcelExport
public class SchemeExportsVo extends CrmExcelVo {

  /**
   * 方案编号
   */
  @CrmExcelColumn("方案编号")
  @ApiModelProperty(name = "schemeCode", notes = "方案编号", value = "方案编号")
  private String schemeCode;
  /**
   * 方案名称
   */
  @CrmExcelColumn("方案名称")
  @ApiModelProperty(name = "schemeName", notes = "方案名称", value = "方案名称")
  private String schemeName;
  /**
   * 方案类型
   */
  @CrmExcelColumn("方案类型")
  @ApiModelProperty(name = "schemeType", notes = "方案类型", value = "方案类型")
  private String schemeType;
  /**
   * 方案开始时间
   */
  @CrmExcelColumn("方案开始时间")
  @ApiModelProperty(name = "schemeBeginTime", notes = "方案开始时间", value = "方案开始时间")
  private String schemeBeginTime;
  /**
   * 方案结束时间
   */
  @CrmExcelColumn("方案结束时间")
  @ApiModelProperty(name = "schemeEndTime", notes = "方案结束时间", value = "方案结束时间")
  private String schemeEndTime;
  /**
   * 方案状态
   */
  @CrmExcelColumn("方案状态")
  @ApiModelProperty(name = "schemeStatus", notes = "方案状态（01:待执行, 02:执行中,:03:已结束）", value = "方案状态（01:待执行, 02:执行中,:03:已结束）")
  private String schemeStatus;


  /**
   * 创建人名称
   */
  @CrmExcelColumn("创建人名称")
  private String createName;
}