package com.biz.crm.tpm.admin.web.imports.materialdemand.service;

import com.biz.crm.business.common.ie.sdk.process.BusinessImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.admin.web.imports.materialdemand.model.MaterialDemandDetailImportVo;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.materialdemand.helper.MaterialDemandPageCacheHelper;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@Component
public class MaterialDemandDetailImportsProcess extends BusinessImportProcess<MaterialDemandDetailImportVo> {

    @Resource
    private MaterialDemandPageCacheHelper helper;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private MaterialVoService materialVoService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public Integer getHeadTitleRowIndex() {
        return 2;
    }

    @Override
    public Map<Integer, String> analysisHeadFieldMap(Map<String, Object> params, Map<Integer, String> headMap) {
        return null;
    }

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, MaterialDemandDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        String cacheKey = String.valueOf(params.get("cacheKey"));
        List<MaterialDemandDetailImportVo> importVoList = data.values().stream().collect(Collectors.toList());
        List<String> orgCodes = importVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode()))
                .map(MaterialDemandDetailImportVo::getOrgCode).distinct().collect(Collectors.toList());
        Set<String> materialCodes = importVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                .map(MaterialDemandDetailImportVo::getMaterialCode).collect(Collectors.toSet());
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
        List<MaterialVo> materialVos = materialVoService.findByMaterialCodes(materialCodes);
        Map<String, String> orgMap = Maps.newHashMap();
        Map<String, MaterialVo> materialMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orgVoList)) {
            orgMap = orgVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode())).collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        }
        if (CollectionUtils.isNotEmpty(materialVos)) {
            materialMap = materialVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                    .collect(Collectors.toMap(MaterialVo::getMaterialCode, Function.identity()));
        }
        List<MaterialDemandDetailVo> dataList = Lists.newArrayList();
        Integer count = 2;
        for (MaterialDemandDetailImportVo importVo : importVoList) {
            StringJoiner errMsg = new StringJoiner(";");
            MaterialDemandDetailVo vo = nebulaToolkitService.copyObjectByWhiteList(importVo, MaterialDemandDetailVo.class, HashSet.class, ArrayList.class);
            if (MarketingPlanCaseCheckHelper.paramNotNull(importVo.getOrgCode(), "组织编码", errMsg)) {
                if (orgMap.containsKey(importVo.getOrgCode())) {
                    vo.setOrgName(orgMap.get(importVo.getOrgCode()));
                }
            }
            if (MarketingPlanCaseCheckHelper.paramNotNull(importVo.getMaterialCode(), "物料编码", errMsg)) {
                if (materialMap.containsKey(importVo.getMaterialCode())) {
                    MaterialVo materialVo = materialMap.get(importVo.getMaterialCode());
                    vo.setMaterialName(materialVo.getMaterialName());
                    vo.setMaterialPrice(ObjectUtils.defaultIfNull(materialVo.getCostPrice(), BigDecimal.ZERO));
                }
            }
            MarketingPlanCaseCheckHelper.paramNotNull(importVo.getMaterialNum(), "物料数量", errMsg);
            try {
                vo.setMaterialNum(new BigDecimal(importVo.getMaterialNum()));
            } catch (Exception e) {
                errMsg.add("物料数量只能是数字");
            }
            if (ObjectUtils.isNotEmpty(vo.getMaterialPrice()) && ObjectUtils.isNotEmpty(vo.getMaterialNum())) {
                vo.setMaterialAmount(vo.getMaterialNum().multiply(vo.getMaterialPrice()));
            }
            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                vo.setErrMsg(String.format("第%s行数据错误:", count) + errMsg);
                vo.setCheckFlag(Boolean.FALSE);
            } else {
                vo.setCheckFlag(Boolean.TRUE);
            }
            count++;
            dataList.add(vo);
        }
        dataList = dataList.stream().sorted(Comparator.comparing(MaterialDemandDetailVo::getCheckFlag)).collect(Collectors.toList());
        helper.importNewItem(cacheKey, dataList);
        return null;
    }

    @Override
    public Class<MaterialDemandDetailImportVo> findCrmExcelVoClass() {
        return MaterialDemandDetailImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "MATERIAL_DEMAND_DETAIL_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "物料需求提报明细导入";
    }

    @Override
    public Integer getBatchCount() {
        return 999999999;
    }
}
