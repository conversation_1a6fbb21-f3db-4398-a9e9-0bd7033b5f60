package com.biz.crm.tpm.admin.web.exports.headover.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.tpm.admin.web.exports.headover.mapper.HeadOverallPlanReportExportsMapper;
import com.biz.crm.tpm.admin.web.exports.headover.model.HeadOverallPlanReportExportsDto;
import com.biz.crm.tpm.admin.web.exports.headover.model.HeadOverallPlanReportExportsVo;
import com.biz.crm.tpm.admin.web.service.internal.OverallPlanReportServiceImpl;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseReportVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 【总部指引追踪-分大区】导出明细
 *
 * <AUTHOR>
 */
@Component
public class HeadOverallPlanReportExportsProcess  implements ExportProcess<HeadOverallPlanReportExportsVo> {

    @Autowired
    HeadOverallPlanReportExportsMapper headOverallPlanReportExportsMapper;

    @Autowired
    OverallPlanReportServiceImpl overallPlanReportService;

    @Override
    public Integer getTotal(Map<String, Object> params) {

        return headOverallPlanReportExportsMapper.getExportTotal(new HeadOverallPlanReportExportsDto(this.convertEuropaParam(params)));
    }

    @Override
    public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
        Map<String, Object> nParams = this.convertEuropaParam(params);
        nParams.putIfAbsent("offset",this.getPageSize()*vo.getPageNo());
        nParams.putIfAbsent("limit",vo.getPageSize());
        PageRequest pageable = PageRequest.of(this.getPageSize() * vo.getPageNo(), vo.getPageSize());
        OverallPlanCaseVo req = JSONObject.parseObject(JSONObject.toJSONString(nParams), OverallPlanCaseVo.class);
        Page<OverallPlanCaseReportVo> res = overallPlanReportService.findListBySchemeType(pageable, req, OverallPlanSchemeTypeEnum.HEAD.getCode());
        List<OverallPlanCaseReportVo> records = res.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return new JSONArray();
        }
        String s = JSONObject.toJSONString(records);
        List<HeadOverallPlanReportExportsVo> headOverallPlanReportExportsVos = JSONArray.parseArray(s, HeadOverallPlanReportExportsVo.class);
        adjustData(headOverallPlanReportExportsVos);
        return toJSONArray(headOverallPlanReportExportsVos);
    }

    private void adjustData(List<HeadOverallPlanReportExportsVo> data) {
        if(CollectionUtils.isEmpty(data)){
            return;
        }
    }

    @Override
    public String getBusinessCode() {
        return "TPM_HEAD_OVER_PLAIN_REPORT_EXPORT";
    }

    @Override
    public String getBusinessName() {
        return "总部指引追踪-分大区导出明细";
    }
}
