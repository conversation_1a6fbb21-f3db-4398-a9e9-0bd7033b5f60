package com.biz.crm.tpm.admin.web.register.form;

import com.biz.crm.tpm.business.activities.local.strategy.form.audit.AutoAuditFormEventStrategy;
import com.biz.crm.tpm.business.activities.local.strategy.form.occupy.OccupyFormEventStrategy;
import com.biz.crm.tpm.business.activities.sdk.register.ActivitiesCenterModuleRegister;
import com.biz.crm.tpm.business.budget.sdk.register.FormDimension;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormEventStrategy;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 描述：</br>活动申请表单维度
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Component
public class EventApplicationFormDimension implements FormDimension {

  @Autowired
  private ActivitiesCenterModuleRegister activitiesCenterModuleRegister;

  @Override
  public String getCode() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String getName() {
    return "活动申请表单";
  }

  @Override
  public String getModelCode() {
    return activitiesCenterModuleRegister.moduleCode();
  }

  /**
   * 注册活动申请表单能够选择的策略
   *
   * @return
   */
  @Override
  public Collection<Class<? extends FormEventStrategy>> getFormEventStrategies() {
    // 费用预占策略
    return Sets.newHashSet(OccupyFormEventStrategy.class, AutoAuditFormEventStrategy.class);
  }

  @Override
  public int getOrder() {
    return 0;
  }
}
