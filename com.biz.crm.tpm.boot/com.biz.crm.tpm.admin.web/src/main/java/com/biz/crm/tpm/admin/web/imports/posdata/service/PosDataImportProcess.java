package com.biz.crm.tpm.admin.web.imports.posdata.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.tpm.admin.web.imports.posdata.model.PosDataImportVo;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.pay.sdk.service.PosDataService;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Component
@Slf4j
public class PosDataImportProcess implements ImportProcess<PosDataImportVo> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private PosDataService posDataService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, PosDataImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Assert.notEmpty(data, "导入数据不能为空!");
        Map<Integer, String> errorMap = Maps.newHashMap();
        List<PosDataVo> dataVoList = Lists.newArrayList();
        Set<String> erpCodeSet = data.values().stream().filter(k -> StringUtil.isNotEmpty(k.getErpCode()))
                .map(PosDataImportVo::getErpCode).collect(Collectors.toSet());
        Map<String, Map<String, CustomerVo>> customerVoMap = customerVoService.findByErpCodes(new ArrayList<>(erpCodeSet))
                .stream().collect(Collectors.groupingBy(CustomerVo::getErpCode,
                        Collectors.toMap(CustomerVo::getCompanyCode, Function.identity(), (a, b) -> a)));
        data.forEach((rowNum, importVo) -> {
            StringJoiner errMsg = new StringJoiner(";");
            MarketingPlanCaseCheckHelper.paramNotNull(importVo.getYears(), "POS年月", errMsg);
            MarketingPlanCaseCheckHelper.paramNotNull(importVo.getOrgCode(), "末级组织编码", errMsg);
            MarketingPlanCaseCheckHelper.paramNotNull(importVo.getErpCode(), "客户ERP编码", errMsg);
//            MarketingPlanCaseCheckHelper.paramNotNull(importVo.getQuantityStr(), "销售数量", errMsg);
            MarketingPlanCaseCheckHelper.paramNotNull(importVo.getAmountStr(), "销售金额", errMsg);
            MarketingPlanCaseCheckHelper.paramNotNull(importVo.getCompanyCode(), "公司代码", errMsg);
            if (StringUtil.isNotEmpty(importVo.getErpCode())
                    && StringUtil.isNotEmpty(importVo.getCompanyCode())) {
                if (customerVoMap.containsKey(importVo.getErpCode())) {
                    Map<String, CustomerVo> companyVoMap = customerVoMap.get(importVo.getErpCode());
                    if (companyVoMap.containsKey(importVo.getCompanyCode())) {
                        CustomerVo customerVo = companyVoMap.get(importVo.getCompanyCode());
                        if (!BooleanEnum.TRUE.getCapital().equals(customerVo.getContractCustomer())) {
                            errMsg.add("客户不是合同客户");
                        }
                        importVo.setCustomerCode(customerVo.getCustomerCode());
                        importVo.setCustomerName(customerVo.getCustomerName());
                    } else {
                        errMsg.add("客户，未找到！");
                    }
                } else {
                    errMsg.add("客户，未找到！");
                }
            }
            PosDataVo vo = nebulaToolkitService.copyObjectByWhiteList(importVo, PosDataVo.class, HashSet.class, ArrayList.class);
            try {
                if (ObjectUtil.isNotEmpty(importVo.getQuantityStr())){
                    vo.setQuantity(new BigDecimal(importVo.getQuantityStr()));
                }
            } catch (Exception e) {
                errMsg.add("销售数量只能是数字");
            }
            try {
                vo.setAmount(new BigDecimal(importVo.getAmountStr()));
            } catch (Exception e) {
                errMsg.add("销售金额只能是数字");
            }
            if (StringUtil.isNotEmpty(errMsg.toString())) {
                errorMap.put(rowNum, errMsg.toString());
            } else {
                vo.setCustomerCode(importVo.getErpCode() + importVo.getCompanyCode() + "0000");
                dataVoList.add(vo);
            }
        });
        if (CollectionUtil.isEmpty(errorMap)) {
            dataVoList.forEach(vo -> {
                posDataService.create(vo);
            });
        }
        return errorMap;
    }

    @Override
    public Class<PosDataImportVo> findCrmExcelVoClass() {
        return PosDataImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "TPM_POS_DATA_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "POS数据导入";
    }
}
