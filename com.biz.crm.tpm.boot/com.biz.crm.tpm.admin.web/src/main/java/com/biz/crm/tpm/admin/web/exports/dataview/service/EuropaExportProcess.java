package com.biz.crm.tpm.admin.web.exports.dataview.service;

import com.alibaba.fastjson.JSONArray;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.common.ie.sdk.constant.ImportExportConstant;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.excel.util.EuropaParamsTools;
import com.biz.crm.common.ie.sdk.exports.dataview.model.EuropaExportVo;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.europa.database.sdk.context.execute.DatabaseExecuteExternalRequest;
import com.bizunited.nebula.europa.sdk.context.execute.RequestParameter;
import com.bizunited.nebula.europa.sdk.service.EuropaInfoVoService;
import com.bizunited.nebula.europa.sdk.service.ExecutionService;
import com.bizunited.nebula.europa.sdk.service.strategy.ExecutionStrategy;
import com.bizunited.nebula.europa.sdk.vo.EuropaInfoVo;
import com.bizunited.nebula.europa.sdk.vo.ExecutionInfo;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContext;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * Europa 通用导出类
 * <AUTHOR>
 * @version 1.0 Copyright 2022年11月23日 下午5:08:39
 * @version 1.1 Copyright 2023-01-28 17:21:43
 */
@Component
@Slf4j
public class EuropaExportProcess implements ExportProcess<EuropaExportVo> {
  @Autowired(required = false)
  private ExecutionService executionService;
  @Autowired(required = false)
  private EuropaInfoVoService europaInfoVoService;
  @Autowired
  private List<ExecutionStrategy> executionStrategies;

  @Autowired
  private LoginUserService loginUserService;

  @Override
  public Integer getPageSize() {
    return CommonConstant.IE_EXPORT_PAGE_SIZE;
  }


  @Override
  public String getBusinessName() {
    return "数据查询引擎（欧罗巴）导出";
  }

  @Override
  public String getBusinessCode() {
    return ImportExportConstant.EXPORT_BIZ_CODE_MDM_EUROPA_CODE;
  }

  /**
   * 自定义导出文件名称
   * @param task
   * @return
   */
  @Override
  public String getTaskFileName(ExportTaskProcessVo task) {
    /*<pre>
     示例: 数据源+创建人
     return String.format("%s_%s", task.getTaskSource(), task.getCreateAccountName());
     </pre>*/
    return null;
  }

  @Override
  public Integer getTotal(Map<String, Object> params) {
    // 获取查询数据记录条数
    String tenantCode = TenantUtils.getTenantCode();
    String europaInfoCode = "" + params.get(EuropaParamsTools.EUROPA_CODE_PARAMETER_NAME);
    Validate.notBlank(europaInfoCode, "执行数据视图时，未传入对应的欧罗巴数据视图业务编号!!");
    PageRequest pageRequest = PageRequest.of(0, 10);
    RequestParameter requestParameter = this.buildRequestParameter(pageRequest, europaInfoCode, params);
    EuropaInfoVo europaInfoVo = this.europaInfoVoService.findByTenantCodeAndCode(tenantCode, europaInfoCode);
    Validate.notNull(europaInfoVo, "执行数据视图时，未找到指定的欧罗巴数据视图基本信息，请检查!!");
    // 已正确匹配的执行策略
    ExecutionStrategy matchedExecutionStrategy = this.getMatchedExecution(europaInfoVo);
    AbstractCrmUserIdentity abstractCrmUserIdentity = loginUserService.getAbstractLoginUser();
    ExecutionInfo executionInfo =
        this.executionService.executionOnlyCount(tenantCode, europaInfoCode, requestParameter,
            matchedExecutionStrategy);
    final long maxCount = executionInfo.getExecuteContent().getTotalElements();
    Validate.isTrue(maxCount <= CommonConstant.IE_EXPORT_MAX_TOTAL, "执行数据视图导出时，" +
            "单次最大导出[" + CommonConstant.IE_EXPORT_MAX_TOTAL + "]条,请输入更多查询条件!!");
    return Long.valueOf(maxCount).intValue();
  }

  /**
   * @param pageable 分页对象
   * @param europaInfoCode
   * @param params 请求参数
   * @return
   */
  private RequestParameter buildRequestParameter(Pageable pageable, String europaInfoCode, Map<String, Object> params) {
    DatabaseExecuteExternalRequest requestParameter = new DatabaseExecuteExternalRequest();
    /*
     * 参数解析顺序
     * 1、获取传参
     * 2、组装可能的pageable分页信息
     * 3、组装排序条件
     */
    for (Entry<String, Object> entry : params.entrySet()) {
      String key = entry.getKey();
      requestParameter.setAttribute(key, entry.getValue());
      if ("sort".equalsIgnoreCase(key)) {
        String sortAtt = "" + entry.getValue();
        String[] split = sortAtt.split(",");
        if (split.length == 1) {
          requestParameter.setAttribute(StringUtils.join("sort.", split[0]), "ASC");
        } else if (split.length >= 2) {
          requestParameter.setAttribute(StringUtils.join("sort.", split[0]), split[1]);
        }
      }
    }
    // 2、=====
    requestParameter.setAttribute(EuropaParamsTools.EUROPA_CODE_PARAMETER_NAME, europaInfoCode);
    // 创建一个分页对象
    requestParameter.setPageable(pageable);
    return requestParameter;
  }

  @Override
  public Class<EuropaExportVo> findCrmExcelVoClass() {
    throw new RuntimeException("Europa导出不依赖该方法返回Excel表头，注意纠正");
  }

  @Override
  @Transactional(readOnly = true) /* 必须加上事务，否则数据视图将会失效 */
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    MarsAuthorityContext context = MarsAuthorityContextHolder.getContext();

    String europaInfoCode = "" + params.get(EuropaParamsTools.EUROPA_CODE_PARAMETER_NAME);
    Validate.notBlank(europaInfoCode, "执行数据视图时，未传入对应的欧罗巴数据视图业务编号!!");

    String tenantCode = TenantUtils.getTenantCode();
    EuropaInfoVo europaInfoVo = this.europaInfoVoService.findByTenantCodeAndCode(tenantCode, europaInfoCode);
    Validate.notNull(europaInfoVo, "执行数据视图时，未找到指定的欧罗巴数据视图基本信息，请检查!!");
    // 获取查询数据记录条数
    // 已正确匹配的执行策略
    ExecutionStrategy matchedExecutionStrategy = this.getMatchedExecution(europaInfoVo);

    Pageable pageable = PageRequest.of(vo.getPageNo(), this.getPageSize());
    RequestParameter requestParameter = this.buildRequestParameter(pageable, europaInfoCode, params);
    ExecutionInfo execution =
        this.executionService.execution(europaInfoVo, pageable, requestParameter, matchedExecutionStrategy);

    List<Map<String, Object>> results = execution.getExecuteContent().getResults();
    // 调整数据
    adjustData(results);
    return toJSONArray(results);
  }

  /**
   * 已正确匹配的执行策略
   *
   * @param europaInfoVo
   * @return
   */
  private ExecutionStrategy getMatchedExecution(EuropaInfoVo europaInfoVo) {
    // 已正确匹配的执行策略
    ExecutionStrategy matchedExecutionStrategy = null;
    for (ExecutionStrategy executionStrategy : executionStrategies) {
      if (executionStrategy.validate(europaInfoVo)) {
        matchedExecutionStrategy = executionStrategy;
        break;
      }
    }
    Validate.notNull(matchedExecutionStrategy, "执行数据视图时，未找到匹配的执行器，请检查数据视图数据");
    return matchedExecutionStrategy;
  }

  /**
   * 调整数据
   *
   * @param row
   */
  private void adjustData(List<Map<String, Object>> row) {
    if (CollectionUtils.isEmpty(row)) {
      return;
    }
  }


}
