package com.biz.crm.tpm.admin.web.exports.account.model;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 参数传递dto：费用上账主表;
 * <AUTHOR> Keller
 * @date : 2022-7-2
 */
@ApiModel(value = "Account",description = "费用上账主表")
@Getter
@Setter
public class AccountExportsDto extends TenantDto implements Serializable,Cloneable{
  /** 费用上账编码 */
  @ApiModelProperty(name = "accountCode",notes = "费用上账编码", value = "费用上账编码")
  private String accountCode;
  /** 上账状态 */
  @ApiModelProperty(name = "accountStatus",notes = "上账状态", value = "上账状态")
  private String accountStatus;
  /** 活动明细编码 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value = "活动明细编码")
  private String activitiesDetailCode;
  /** 核销明细编号 */
  @ApiModelProperty(name = "auditDetailCode",notes = "核销明细编号", value = "核销明细编号")
  private String auditDetailCode;

  /** 偏移量 */
  @ApiModelProperty(name = "offset",notes = "偏移量", value = "偏移量")
  private Integer offset;

  /** limit */
  @ApiModelProperty(name = "limit",notes = "limit", value = "limit") 
  private Integer limit;
}