package com.biz.crm.tpm.admin.web.controller.overallplan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.service.internal.OverallPlanReportServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/4 14:12
 */
@RestController
@RequestMapping("/v1/regionOverallPlanReportController")
@Api(tags = "大区指引跟踪")
public class RegionOverallPlanReportController {

    @Autowired
    private OverallPlanReportServiceImpl overallPlanCaseService;

    @ApiOperation(value = "查询分页列表")
    @GetMapping("findList")
    public Result<Page<OverallPlanCaseReportVo>> findList(@PageableDefault(50) Pageable pageable, OverallPlanCaseVo vo) {
        return Result.ok(overallPlanCaseService.findListBySchemeType(pageable, vo, OverallPlanSchemeTypeEnum.REGION.getCode()));
    }

    @ApiOperation(value = "通过方案明细编码查询承接明细")
    @GetMapping("findMarketingPlanCase")
    public Result<Page<MarketingPlanCaseVo>> findMarketingPlanCase(@PageableDefault(50) Pageable pageable, String schemeDetailCode) {
        return Result.ok(overallPlanCaseService.findRegionMarketingPlanCase(pageable, schemeDetailCode));
    }
}
