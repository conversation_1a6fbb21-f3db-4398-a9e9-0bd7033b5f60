package com.biz.crm.tpm.admin.web.register.interceptor;

import cn.hutool.core.date.DateUtil;
import com.biz.crm.tpm.business.pay.local.entity.ActivityPrepay;
import com.biz.crm.tpm.business.pay.local.entity.FeeCash;
import com.biz.crm.tpm.business.pay.local.repository.ActivityPrepayRepository;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashRepository;
import com.biz.crm.tpm.business.pay.sdk.enums.TpmPrepayTypeEnum;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.europa.database.sdk.strategy.ExternalQueryInterceptor;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteContent;
import com.bizunited.nebula.europa.sdk.context.matedata.MetaData;
import com.bizunited.nebula.europa.sdk.vo.EuropaInfoVo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/16 10:19
 * @ClassName PrepayActivityQueryInterceptor
 * @Description TODO 活动预付数据视图拦截器参数补充
 */
@Component
public class ActivityPrepayDetailRecordDataViewInterceptor implements ExternalQueryInterceptor {

  @Autowired(required = false)
  private ActivityPrepayRepository activityPrepayRepository;
  @Autowired(required = false)
  private FeeCashRepository feeCashRepository;

  @Override
  public String code() {
    return "activity_prepay_detail_record_data_view_interceptor";
  }

  @Override
  public String name() {
    return "TPM预付明细数据视图拦截器";
  }

  @Override
  public List<Object[]> process(EntityManager entityManager, MetaData metaData,
      EuropaInfoVo europaInfoVo, ExecuteContent executeContent, String... strings) {
    List<Map<String, Object>> results = executeContent.getResults();
    if (CollectionUtils.isEmpty(results)) {
      return null;
    }
    if (0 == results.parallelStream().filter(row -> row != null).count()) {
      return org.apache.commons.compress.utils.Lists.newArrayList();
    }
    Set<String> prepayCodes = results.stream().filter(e -> TpmPrepayTypeEnum.activity.getCode().equals((String) e.get("prepay_type"))).filter(Objects::nonNull)
            .map(row -> (String) row.get("prepay_code")).collect(Collectors.toSet());
    Set<String> cashCodes = results.stream().filter(e -> !TpmPrepayTypeEnum.activity.getCode().equals((String) e.get("prepay_type"))).filter(Objects::nonNull)
            .map(row -> (String) row.get("prepay_code")).collect(Collectors.toSet());
    Map<String, ActivityPrepay> prepayMap = activityPrepayRepository.findByCodes(prepayCodes).stream().collect(Collectors.toMap(ActivityPrepay::getPrepayCode, Function.identity(), (a, b) -> a));
    Map<String, FeeCash> cashMap = feeCashRepository.findByCodes(CollectionUtils.isEmpty(cashCodes) ? Lists.newArrayList() : new ArrayList<>(cashCodes))
            .stream().collect(Collectors.toMap(FeeCash::getCashCode, Function.identity(), (a, b) -> a));
    //取出需要的数据并封装
    List<Object[]> externalContents = Lists.newArrayList();
    results.forEach(result -> {
      List<Object> itemList = Lists.newArrayList();
      if (TpmPrepayTypeEnum.activity.getCode().equals((String) result.get("prepay_type"))) {
        ActivityPrepay prepay = prepayMap.getOrDefault((String) result.get("prepay_code"), new ActivityPrepay());
        for (String externalFileName : strings) {
          if (StringUtils.equals(externalFileName, "payStatus")) {
            itemList.add(prepay.getPayStatus());
          } else if (StringUtils.equals(externalFileName, "paySucessDate")) {
            itemList.add(DateUtil.format(prepay.getPaySucessDate(), "yyyy-MM-dd HH:mm:ss"));
          }
        }
      } else {
        FeeCash cash = cashMap.getOrDefault((String) result.get("prepay_code"), new FeeCash());
        for (String externalFileName : strings) {
          if (StringUtils.equals(externalFileName, "payStatus")) {
            itemList.add(cash.getPayStatus());
          } else if (StringUtils.equals(externalFileName, "paySucessDate")) {
            itemList.add(DateUtil.format(cash.getPaySucessDate(), "yyyy-MM-dd HH:mm:ss"));
          }
        }
      }
      externalContents.add(itemList.toArray(new Object[]{}));
    });
    return externalContents;
  }

}
