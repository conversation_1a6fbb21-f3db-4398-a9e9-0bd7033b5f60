package com.biz.crm.tpm.business.rebate.local.config;

import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @description: 返利模块
 * @author: lifei
 * @date: 2022/2/16 16:17
 */
@Configuration
@EntityScan(basePackages = "com.biz.crm.tpm.business.rebate.local.entity")
@ComponentScan(basePackages = {"com.biz.crm.tpm.business.rebate"})
public class RebateLocalConfig {

  /**
   * 返利变量--可配置的--销售数量
   *
   * @return {@link SaleRebatePolicyCriterionRegister}
   */
//  @Bean("SaleVolumeCriterionImpl")
//  public SaleRebatePolicyCriterionRegister saleVolumeCriterion() {
//    SaleVolumeCriterionImpl saleVolumeCriterion = new SaleVolumeCriterionImpl();
//    return saleVolumeCriterion;
//  }

  /**
   * 返利变量--可配置的--销售数量--动态表单操作策略
   *
   * @return {@link SaleRebatePolicyCriterionRegister}
   */
//  @Bean("SaleVolumeCriterionVoOperationStrategy")
//  public SaleVolumeCriterionVoOperationStrategy saleVolumeCriterionVoOperationStrategy() {
//    SaleVolumeCriterionVoOperationStrategy saleVolumeCriterionVoOperationStrategy = new SaleVolumeCriterionVoOperationStrategy();
//    return saleVolumeCriterionVoOperationStrategy;
//  }



}
