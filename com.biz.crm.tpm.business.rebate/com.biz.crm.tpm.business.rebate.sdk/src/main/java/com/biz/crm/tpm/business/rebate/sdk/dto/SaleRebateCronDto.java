package com.biz.crm.tpm.business.rebate.sdk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-07-13 14:38
 * @description：计算返利周期定时任务dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebateCronDto", description = "计算返利周期定时任务dto")
public class SaleRebateCronDto {

  /**
   * 当前选择的返利周期类型
   */
  @ApiModelProperty("当前选择的返利周期")
  private String cycleCode;
  /**
   * 返利周期推迟量
   */
  @ApiModelProperty("返利周期推迟量")
  private String delayNumber;

  /**
   * 财年周期类型
   */
  @ApiModelProperty("财年周期类型")
  private String cycleType;
  /**
   * 返利政策开始时间（包括）
   */
  @ApiModelProperty("返利政策开始时间（包括）")
  private String saleRebateStartTime;

  /**
   * 返利政策结束时间（包括）
   */
  @ApiModelProperty("返利政策结束时间（包括）")
  private String saleRebateEndTime;
}
