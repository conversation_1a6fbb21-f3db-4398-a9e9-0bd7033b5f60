package com.biz.crm.tpm.business.rebate.sdk.event;

import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * @Description 返利政策操作日志监听服务
 * <AUTHOR>
 * @Date 2024/5/29 10:04
 */
public interface SaleRebatePolicyLogEventListener extends NebulaEvent {
    
    /**
     * 创建事件
     *
     * @param dto
     */
    default void onCreate(SaleRebatePolicyLogEventDto dto) {
    }

    /**
     * 编辑事件
     *
     * @param dto
     */
    default void onUpdate(SaleRebatePolicyLogEventDto dto) {
    }

    /**
     * 删除事件
     *
     * @param dto
     */
    default void onDelete(SaleRebatePolicyLogEventDto dto) {
    }

    /**
     * 启用事件
     *
     * @param dto
     */
    default void onEnable(SaleRebatePolicyLogEventDto dto) {
    }

    /**
     * 禁用事件
     *
     * @param dto
     */
    default void onDisable(SaleRebatePolicyLogEventDto dto) {
    }
}
