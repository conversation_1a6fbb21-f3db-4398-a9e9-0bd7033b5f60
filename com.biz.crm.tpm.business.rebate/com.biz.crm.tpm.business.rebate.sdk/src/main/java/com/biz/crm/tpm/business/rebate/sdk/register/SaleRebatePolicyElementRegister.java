package com.biz.crm.tpm.business.rebate.sdk.register;

import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyElementDataVo;

/**
 * @description: 返利政策要素注册器（默认实现 考核产品，返利产品，返利范围，返利公式）
 * @author: lifei
 * @date: 2022/2/15 16:58
 */
public interface SaleRebatePolicyElementRegister<T extends SaleRebatePolicyElementDataVo> {


  /**
   * 查询返利政策要素名称
   */
  String getSaleRebatePolicyElementName();


  /**
   * 查询返利政策要素编码
   */
  String getSaleRebatePolicyElementCode();


  Integer getElementSort();

  /**
   * 查询要素bean
   */
  Class<T> getSaleRebatePolicyElementClass();

  /**
   * 返利政策编码查询查询返利政策要素内容
   *
   * @param saleRebatePolicyCode
   */
  T getBySaleRebatePolicyCode(String saleRebatePolicyCode);

  /**
   * 保存对应返利政策要素内容
   *
   * @param saleRebatePolicyCode
   * @return
   */
  T onRequestSaleRebatePolicyCreate(String saleRebatePolicyCode, T t);

  /**
   * 编辑对应返利政策要素内容
   *
   * @param saleRebatePolicyCode
   * @return
   */
  T onRequestSaleRebatePolicyUpdate(String saleRebatePolicyCode,T t);
}
