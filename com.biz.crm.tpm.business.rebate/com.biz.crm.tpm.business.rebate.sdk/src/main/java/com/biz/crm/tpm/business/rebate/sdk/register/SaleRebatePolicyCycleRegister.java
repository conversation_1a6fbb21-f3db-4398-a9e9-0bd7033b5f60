package com.biz.crm.tpm.business.rebate.sdk.register;

import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateExecutionDateRangeVo;

/**
 * @description: 返利周期注册
 * @author: lifei
 * @date: 2022/2/16 16:18
 */
public interface SaleRebatePolicyCycleRegister {

  /**
   *
   * 得到一个返利周期编码
   * <AUTHOR>
   * @date
   */
  String getSaleRebatePolicyCycleCode();

  /**
   *
   * 得到一个返利周期名称
   * <AUTHOR>
   * @date
   */
  String getSaleRebatePolicyCycleName();

  /**
   * 获取返利周期定时任务表达式
   */
  String getSaleRebateCronExpression();

  /**
   * 判断是否需要计算财年偏移量
   * @return
   */
  boolean getCheckFiscalYear();
  /**
   * 返利周期类型
   */
  int getCycleType();
  /**
   * 周期最小单位
   */
  int getCycleNum();
  /**
   * 拿到要素排序列表排序
   */
  Integer getCycleSort();

  /**
   *
   * （创建定时任务）
   * <AUTHOR>
   * @date
   */
  void onRequestcreate(SaleRebatePolicyDto saleRebatePolicyDto);



  /**
   *
   * （编辑定时任务）
   * <AUTHOR>
   * @date
   */
  void onRequestUpdate(SaleRebatePolicyDto saleRebatePolicyDto);

  /**
   *
   * 本次定时任务执行时间范围
   * 如果是企业财年（财年影响到 月 季度 年度）
   * 本次执行范围应该为财年范围 开始时间和结束时间 是通过和政策执行开始结束时间比较后结果。
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  SaleRebateExecutionDateRangeVo getExecutionDateRangeVo(SaleRebatePolicyDto saleRebatePolicyDto);

}

