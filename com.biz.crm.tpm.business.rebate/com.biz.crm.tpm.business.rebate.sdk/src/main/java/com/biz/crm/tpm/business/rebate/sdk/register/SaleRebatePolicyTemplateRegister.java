package com.biz.crm.tpm.business.rebate.sdk.register;

import com.biz.crm.tpm.business.rebate.sdk.strategy.SaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateComputeBuildParamVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateComputeParamVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyDetailVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.scope.AbstractSaleRebatePolicyCustomerInfo;

import java.util.Collection;
import java.util.List;

/**
 * @description: 返利政策模板注册（默认实现，货补，折扣）
 * @author: lifei
 * @date: 2022/2/17 17:50
 */
public interface SaleRebatePolicyTemplateRegister {

  /**
   * 模板编码  compensateRebateTemplateRegister
   *
   * <AUTHOR>
   * @date
   */
  String getSaleRebatePolicytemplateCode();

  /**
   * 模板名称
   *
   * <AUTHOR>
   * @date
   */
  String getSaleRebatePolicytemplateName();

  /**
   * 拿到要素排序列表排序
   */
  Integer getTemplateSort();

  /**
   * 返回该返利政策可以使用的客户范围选择方式(注意，是可用的，不是具体优惠实例正在使用的)
   */
  Collection<Class<? extends SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo>>> getCustomerScopeStrategyClasses();

  /**
   * 可用的要素
   *
   * <AUTHOR>
   * @date
   */
  Collection<Class<? extends SaleRebatePolicyElementRegister>> getSaleRebateElementClasses();

  /**
   * 构建最小粒度参数 返利明细
   *
   * @param saleRebateComputeBuildParamVos 构建最小粒度参数vo
   * <AUTHOR>
   * @dateSaleRebateComputeBuildParamVo
   */
  List<SaleRebateComputeParamVo> onbuildParam(List<SaleRebateComputeBuildParamVo> saleRebateComputeBuildParamVos);

  /**
   * 上账
   *
   * @param saleRebatePolicyDetailVos 返利明细
   * <AUTHOR>
   */
  void onAccount(List<SaleRebatePolicyDetailVo> saleRebatePolicyDetailVos);

}
