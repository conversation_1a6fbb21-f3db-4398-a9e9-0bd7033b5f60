package com.biz.crm.tpm.business.rebate.sdk.register;

import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @description: 返利政策基准注册(也叫返利变量 ）
 * @author: lifei
 * @date: 2022/2/16 16:42
 */
public interface SaleRebatePolicyCriterionRegister {

    /**
     * 得到一个返利政策基准策略编码 必须要保证全局唯一性
     *
     * @return {@link String}
     * <AUTHOR>
     * @date
     */
    String getSaleRebatePolicyCriterionCode();

    /**
     * 得到一个返利政策基准策略名称
     *
     * @return {@link String}
     * <AUTHOR>
     * @date
     */
    String getSaleRebatePolicyCriterionName();

    /**
     * 排序
     *
     * @return {@link Integer}
     * <AUTHOR>
     * @date
     */
    Integer getCriterionSort();

    /**
     * 查询金钱
     * key: 返利变量标识 + 客户编码
     * value： 结果值（数量，金额，目标值等）
     *
     * @param vo      计算基准公式
     * @param calType 计算类型
     * @return key：公式编码+客户编码，value：计算结果值
     * <AUTHOR>
     * @date
     */
    Map<String, BigDecimal> getAmountMapByCondition(FormulaCalBaseVo vo, String calType);


    /**
     * 公式默认金额 （只提供给验证公式用）
     *
     * @return {@link BigDecimal}
     * <AUTHOR>
     * @date
     */
    default BigDecimal getDefaultAmount() {
        return BigDecimal.ZERO;
    }

    /**
     * 是可配置的
     *
     * @return {@link Boolean}
     */
    default Boolean isConfigurable() {
        return Boolean.FALSE;
    }

    default void checkParam(FormulaCalBaseVo vo, String calType) {
        Validate.notNull(vo, "计算参数类型不能为空");
        Validate.notNull(vo.getCustomerCode(), "客户编码不能为空");
        Validate.notNull(vo.getActDetailCode(), "唯一编码不能为空");
        Validate.notNull(vo.getYears(), "年月不能为空");
        Validate.notNull(vo.getRebateStartDate(), "返利政策开始时间不能为空");
        Validate.notNull(vo.getRebateEndDate(), "返利政策结束时间不能为空");
        Validate.isTrue(!(CollectionUtils.isEmpty(vo.getItemCodeSet()) && CollectionUtils.isEmpty(vo.getProductCodeSet())), "品项编码和商品编码不能为空");
        Validate.isTrue(!(CollectionUtils.isEmpty(vo.getFeeItemCodeSet()) && CollectionUtils.isEmpty(vo.getFeeProductCodeSet())), "返利品项和返利产品不能为空");
        if (calType.equals(FormulaTypeEnum.MARKETING.getCode())) {
            Validate.notNull(vo.getOrgCode(), "费用部门不能为空");
        } else if (StringUtils.equalsAny(calType, FormulaTypeEnum.WITHHOLDING.getCode(), FormulaTypeEnum.MANAGEMENT_REPORT.getCode(),FormulaTypeEnum.END_CASE.getCode())) {
            Validate.notNull(vo.getStartDate(), "活动开始时间不能为空");
            Validate.notNull(vo.getEndDate(), "活动结束时间不能为空");
            Validate.notNull(vo.getItemCodeSet(), "品项编码不能为空");
            Validate.notNull(vo.getProductCodeSet(), "商品编码不能为空");
        } else if (FormulaTypeEnum.SPECIAL_LOGIC.getCode().equals(calType)) {

        } else {
            throw new UnsupportedOperationException("计算类型错误,参考FormulaTypeEnum");
        }
    }
}
