package com.biz.crm.tpm.business.rebate.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023-07-17 11:42
 * @description：
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebateCronVo", description = "返利周期计算结果VO")
public class SaleRebateCronVo {
  /**
   * 返利政策开始时间（包括）
   */
  @ApiModelProperty("返利政策开始时间")
  private String saleRebateStartTime;

  /**
   * 返利政策结束时间（包括）
   */
  @ApiModelProperty("返利政策结束时间")
  private String saleRebateEndTime;

  /**
   * 返利时间点
   */
  @ApiModelProperty("返利时间点")
  private String saleRebatePointTime;
}
