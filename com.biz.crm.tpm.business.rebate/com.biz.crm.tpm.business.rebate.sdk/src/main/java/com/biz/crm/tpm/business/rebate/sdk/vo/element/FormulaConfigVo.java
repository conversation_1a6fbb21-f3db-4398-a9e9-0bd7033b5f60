package com.biz.crm.tpm.business.rebate.sdk.vo.element;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description 公式配置vo
 * <AUTHOR>
 * @Date 2024/6/11 20:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FormulaConfigVo extends TenantFlagOpVo {
    
    /**
     * 公式配置编码
     */
    @ApiModelProperty("公式配置编码")
    private String formulaConfigCode;

    /**
     * 公式配置名称
     */
    @ApiModelProperty("公式配置名称")
    private String formulaConfigName;

    /**
     * 计算公式命中策略类型
     */
    @ApiModelProperty("计算公式命中策略类型")
    private String calculateType;

    @ApiModelProperty("公式明细")
    private List<FormulaSaleRebatePolicyElementVo> formulaList;
}
