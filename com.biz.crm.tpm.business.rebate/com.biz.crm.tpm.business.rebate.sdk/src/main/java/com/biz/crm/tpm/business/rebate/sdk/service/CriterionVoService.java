package com.biz.crm.tpm.business.rebate.sdk.service;

import com.biz.crm.tpm.business.rebate.sdk.vo.dynamic.AbstractCriterionVo;

import java.util.List;
import java.util.Map;

/**
 * 返利变量VO
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
public interface CriterionVoService {

  /**
   * 根据政策编码和变量编码查询和返利变量配套的动态表单的内容信息
   *
   * @param saleRebatePolicyCode          返利政策code
   * @param saleRebatePolicyCriterionCode 返利政策变量code
   * @return {@link AbstractCriterionVo}
   */
  AbstractCriterionVo findByPolicyCodeAndCriterionCode(String saleRebatePolicyCode, String saleRebatePolicyCriterionCode);

  /**
   * 查询当前政策编码下所有的自定义返利变量
   * @param saleRebatePolicyCode
   * @return
   */
  Map<String, List<AbstractCriterionVo>> findMapByPolicyCodeAndCriterionCode(String saleRebatePolicyCode, List<String> saleRebatePolicyCriterionCodes);
}
