package com.biz.crm.tpm.business.rebate.sdk.vo.element;

import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @describe:返利政策返利公式要素vo
 * @createTime 2022年02月21日 09:52:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FormulaSaleRebatePolicyElementVo", description = "返利政策返利公式要素vo")
public class FormulaSaleRebatePolicyElementVo {

  /**
   * ID
   */
  @ApiModelProperty("ID")
  private String id;
  
  /**
   * 公式配置编码
   */
  @ApiModelProperty("公式配置编码")
  private String formulaConfigCode;
  
  /**
   * 返利条件
   */
  @ApiModelProperty("返利条件")
  private String saleRebatePolicyCondition;

  /**
   * 返利条件（展示用）
   */
  @ApiModelProperty("返利条件（展示用）")
  private String saleRebatePolicyConditionName;

  /**
   * 返利公式
   */
  @ApiModelProperty("返利公式")
  private String saleRebatePolicyFormula;

  /**
   * 返利公式（展示用）
   */
  @ApiModelProperty("返利公式（展示用）")
  private String saleRebatePolicyFormulaName;


  /**
   * 公式顺序（计算策略和展示用）
   */
  @ApiModelProperty("公式顺序（计算策略和展示用）")
  private Integer formulaSort;

  /**
   * @see FormulaTypeEnum
   * 公式类型
   */
  @ApiModelProperty("公式类型,预提:withholding,返利:rebate")
  private String formulaType;
}
