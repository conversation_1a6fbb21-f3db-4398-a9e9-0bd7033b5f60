package com.biz.crm.tpm.business.rebate.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 公式类型
 * <AUTHOR>
 * @Date 2024/5/28 20:00
 */
@AllArgsConstructor
@Getter
public enum FormulaTypeEnum {

    MARKETING("marketing", "营销方案"),

    /**
     * 计提
     */
    WITHHOLDING("withholding", "计提"),

    /**
     * 管报计提
     */
    MANAGEMENT_REPORT("management_report", "管报计提"),

    /**
     * 结案
     */
    END_CASE("end_case", "结案"),

    /**
     * 特殊逻辑
     */
    SPECIAL_LOGIC("special_logic", "特殊逻辑"),
    ;

    private final String code;

    private final String desc;
}
