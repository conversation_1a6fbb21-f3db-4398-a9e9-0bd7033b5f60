package com.biz.crm.tpm.business.rebate.sdk.register;

import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateComputeParamVo;
import java.util.List;

/**
 * <AUTHOR>
 * @title SaleRebateCalculateTypeRegister
 * @date 2023/6/19 14:20
 * @description 返利计算类型注册接口
 */
public interface SaleRebateCalculateTypeRegister {

  /**
   * 获取返利计算类型注册接口实现类编码
   * @return
   */
  String getCalculateTypeRegisterCode();

  /**
   * 获取返利计算类型注册接口实现类名称
   * @return
   */
  String getCalculateTypeRegisterName();

  /**
   * 获取返利计算类型注册接口实现类顺序
   * @return
   */
  Integer getCalculateTypeRegisterSortCode();

  /**
   * 返利计算命中策略 （实现此接口，自定义本身的命中概率）
   */
  void  handleCalculate(List<SaleRebateComputeParamVo> saleRebateComputeParamVos);


}
