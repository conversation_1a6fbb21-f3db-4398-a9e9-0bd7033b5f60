package com.biz.crm.tpm.business.rebate.sdk.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @description: 返利计算对象参数
 * @author: lifei
 * @date: 2022/3/8 16:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "返利计算参数对象")
public class SaleRebateComputeParamVo{
  private static final long serialVersionUID = -8548852252283803554L;

  /**
   * 计算批次号
   */
  @ApiModelProperty("计算批次号")
  private String speedNo;
  /**
   * 客户json
   */
  @ApiModelProperty("客户json")
  private JSONObject cusJson;

  /**
   * 返利产品编码
   */
  @ApiModelProperty("返利产品/层级编码")
  private String code;

  /**
   * 返利产品
   */
  @ApiModelProperty("返利产品/层级名称")
  private String name;

  /**
   * 返利（类型）
   */
  @ApiModelProperty("返利产品/层级")
  private Integer productType;

  /**
   * 分配类型
   *
   */
  @ApiModelProperty("分配类型")
  private Integer allocationType;

  /**
   * 分配比例
   */
  @ApiModelProperty("分配比例")
  private BigDecimal rebateRatio;

  /**
   * 返利政策开始时间（包括）
   */
  @ApiModelProperty("返利政策开始时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateStartTime;

  /**
   * 返利政策结束时间（包括）
   */
  @ApiModelProperty("返利政策结束时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateEndTime;

  /**
   * 返利政策开始时间（包括）
   */
  @ApiModelProperty("返利政策计算开始时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date saleRebateComputeStartTime;

  /**
   * 返利政策结束时间（包括）
   */
  @ApiModelProperty("返利政策计算结束时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date saleRebateComputeEndTime;

  /**
   * 计算时间
   */
  @ApiModelProperty("计算时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date calculationTime;

  /**
   * 是否测试
   */
  @ApiModelProperty("是否测试")
  private String isTest = "0";

  /**
   * 公式vo
   */
  @ApiModelProperty("公式vo")
  private SaleRebatePolicyFormulaInfoVo saleRebatePolicyFormulaInfoVo;

  /**
   * 返利政策编码
   */
  @ApiModelProperty("返利政策编码")
  private SaleRebatePolicyVo saleRebatePolicyVo;

  /**
   * key 基准+客户编码
   * value
   */
  @ApiModelProperty("基准map")
  private Map<String, BigDecimal> amountMap;

  /**
   * 返利明细id
   */
  @ApiModelProperty("返利明细id")
  private String saleRebatePolicyDetailId;

  /**
   * 返利明细id
   */
  @ApiModelProperty("返利明细编码")
  private String saleRebateDetailCode;


  /**
   * 计算公式命中策略类型
   */
  @ApiModelProperty("计算公式命中策略类型")
  private String calculateType;

  /**
   * 客户编码
   */
  @ApiModelProperty("客户编码")
  private String customerCode;

  /**
   * 公式顺序
   */
  @ApiModelProperty("公式顺序")
  private Integer formulaSort;

}
