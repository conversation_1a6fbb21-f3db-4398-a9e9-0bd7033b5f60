package com.biz.crm.tpm.business.rebate.sdk.vo.scope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 以圈定渠道范围的方式，确认特定返利政策的适用客户范围
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebatePolicyScopeChannelInfoVo", description = "返利渠道范围vo")
public class SaleRebatePolicyScopeChannelInfoVo extends AbstractSaleRebatePolicyCustomerInfo {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty("被选定或者被排除的渠道业务编号")
  private String channelCode;

  @ApiModelProperty("被选定或者被排除的渠道业务名称")
  private String channelName;

}
