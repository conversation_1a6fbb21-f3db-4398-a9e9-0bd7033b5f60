package com.biz.crm.tpm.business.rebate.sdk.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description:  返利注册器返回vo
 * @author: lifei
 * @date: 2022/2/21 9:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebatePolicyRegisterVo", description = "返利注册器返回vo")
public class SaleRebatePolicyRegisterVo {

  @ApiModelProperty("编码")
  private String code;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("返利要素排序")
  private Integer indexCode;

  @ApiModelProperty("当前返利注册器定时任务基础表达式")
  private String cronExpression;

  /**
   * 当前注册周期是否需要判断使用财年
   */
  @ApiModelProperty("判断是否需要计算财年偏移量")
  private boolean checkFiscalYear = false;

  /**
   * 返利周期类型
   */
  @ApiModelProperty("返利周期类型-日期类型")
  private int cycleType;
  /**
   * 周期最小单位
   */
  @ApiModelProperty("周期最小单位")
  private int cycleNum;

}
