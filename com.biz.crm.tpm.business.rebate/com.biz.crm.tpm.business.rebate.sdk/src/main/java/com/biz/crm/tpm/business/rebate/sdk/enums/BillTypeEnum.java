package com.biz.crm.tpm.business.rebate.sdk.enums;

import lombok.Getter;

/**
 * @description: 上账方式
 * @author: lifei
 * @date: 2022/3/10 22:15
 */
@Getter
public enum BillTypeEnum {

  MANUAL_KEEP_BOOKS(1,"1", "手动上账(正)", 2),

  AUTO_KEEP_BOOKS(2,"2", "自动上账(正)", 1);


  /**
   * key
   */
  private Integer key;

  /**
   * 编码
   */
  private String dictCode;

  /**
   * 说明
   */
  private String value;
  /**
   * 排序
   */
  private Integer order;

  BillTypeEnum(Integer key, String dictCode, String value, Integer order) {
    this.key = key;
    this.dictCode = dictCode;
    this.value = value;
    this.order = order;
  }

  /**
   * 根据code转枚举
   *
   * @param dictCode
   * @return
   */
  public static BillTypeEnum codeToEnum(String dictCode) {
    BillTypeEnum result = null;
    for (BillTypeEnum billTypeEnum : BillTypeEnum.values()) {
      if (billTypeEnum.dictCode.equals(dictCode)) {
        result = billTypeEnum;
      }
    }
    return result;
  }
}
