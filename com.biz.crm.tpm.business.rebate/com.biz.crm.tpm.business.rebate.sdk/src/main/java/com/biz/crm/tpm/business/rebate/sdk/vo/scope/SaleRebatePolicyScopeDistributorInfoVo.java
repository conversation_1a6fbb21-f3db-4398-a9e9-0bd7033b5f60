package com.biz.crm.tpm.business.rebate.sdk.vo.scope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 以圈定经销商范围的方式，确认特定返利政策的适用客户范围
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebatePolicyScopeDistributorInfoVo", description = "返利客户范围vo")
public class SaleRebatePolicyScopeDistributorInfoVo extends AbstractSaleRebatePolicyCustomerInfo {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty("被选定或者被排除的经销商业务编号")
  private String distributorCode;

  @ApiModelProperty("被选定或者被排除的经销商业务名称")
  private String distributorName;

}
