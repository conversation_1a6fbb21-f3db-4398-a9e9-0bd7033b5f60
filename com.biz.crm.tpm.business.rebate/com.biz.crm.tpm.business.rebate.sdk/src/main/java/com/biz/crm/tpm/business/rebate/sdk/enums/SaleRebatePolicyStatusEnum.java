package com.biz.crm.tpm.business.rebate.sdk.enums;

import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * @description: 返利政策状态
 * @author: lifei
 * @date: 2022/4/6 17:04
 */
@Getter
public enum SaleRebatePolicyStatusEnum {

  WAIT_EXECUTION(1, "1","待执行","1"),
  ON_EXECUTION(2, "2","执行中","2"),
  COMPLETED(3, "3","已完成","3"),
  ;

  private Integer key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;

  SaleRebatePolicyStatusEnum(Integer key, String dictCode, String value, String order) {
    this.key = key;
    this.dictCode = dictCode;
    this.order = order;
    this.value = value;
  }

  /**
   * 通过key获取 SaleRebatePolicyStatusEnum
   *
   * @param key
   * @return
   */
  public static SaleRebatePolicyStatusEnum getByKey(String key) {
    return Arrays
        .stream(SaleRebatePolicyStatusEnum.values()).filter(item -> Objects.equals(item.getKey(), key)).findFirst().orElse(null);
  }
}
