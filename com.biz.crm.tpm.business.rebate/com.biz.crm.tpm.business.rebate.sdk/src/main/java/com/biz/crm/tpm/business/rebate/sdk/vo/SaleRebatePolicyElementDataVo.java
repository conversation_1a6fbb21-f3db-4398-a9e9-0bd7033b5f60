package com.biz.crm.tpm.business.rebate.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 返利要素基础类
 * @author: lifei
 * @date: 2022/2/16 16:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebatePolicyElementDataVo", description = "返利要素基础类")
public class SaleRebatePolicyElementDataVo extends TenantVo {

  /**
   * 返利编码（一旦创建，不允许修改），不输入就是系统生成(按照租户生成)
   */
  @ApiModelProperty("返利编码")
  private String saleRebatePolicyCode;
}
