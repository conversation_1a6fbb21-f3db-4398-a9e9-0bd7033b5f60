package com.biz.crm.tpm.business.rebate.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/14 02:54
 */
@Data
@ApiModel("销售计划查询vo")
public class SalesPlanQueryVo {

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("开始年月")
    private String startYears;

    @ApiModelProperty("结束年月")
    private String endYears;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户编码")
    private Set<String> customerCodeSet;

    @ApiModelProperty("品项编码")
    private Set<String> itemCodeSet;

    @ApiModelProperty("产品编码")
    private Set<String> productCodeSet;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("成本中心")
    private Set<String> costCenterCodeSet;

    @ApiModelProperty("不包含当前的schemeCode")
    private List<String> excludeSchemeCodes;

    @ApiModelProperty("变更标识")
    private String changeFlag;

    @ApiModelProperty("缓存key")
    private String cacheKey;

    @ApiModelProperty("年月列表")
    private List<String> yearsList;

}
