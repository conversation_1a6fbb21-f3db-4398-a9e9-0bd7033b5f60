package com.biz.crm.tpm.business.rebate.sdk.vo.dynamic;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 可配置的返利变量
 * - 用来对应传参里的json
 * - 传参里的json要转成这个类
 *
 * <AUTHOR>
 * @date 2022/06/22
 */
@Data
public class ConfigurableCriterionVo {

  /**
   * - 给动态表单使用的属性
   * - 每个可配置的返利变量的表单
   * - 返利变量key的值来源某一个实现类的CriterionCode方法，比如：SaleVolumeCriterionImpl#getSaleRebatePolicyCriterionCode()
   *
   * {
   * 	 "返利变量模板"：[
   * 	    {
   * 	     "instanceCode":"返利变量实例编码",
   * 	     // 表单内容的其它属性对
   * 	    }
   * 	  ],
   * 	 "CPXSSL":[]
   * }
   */
  private Map<String , List<AbstractCriterionVo>> criterionMap;


}
