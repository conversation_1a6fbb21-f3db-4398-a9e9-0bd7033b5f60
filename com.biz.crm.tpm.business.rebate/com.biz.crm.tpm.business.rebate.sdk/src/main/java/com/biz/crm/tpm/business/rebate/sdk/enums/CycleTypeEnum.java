package com.biz.crm.tpm.business.rebate.sdk.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * @description: 周期类型枚举
 * @author: lifei
 * @date: 2022/4/11 15:51
 */
public enum CycleTypeEnum {

  /**
   * 自然年
   */
  CIVIL_YEAR(1, "1", "自然年", "1"),

  /**
   * 企业财年
   */
  ENTERPRISE_FISCAL_YEAR(2, "2", "企业财年", "2"),
  ;

  private Integer key;

  /**
   * 字典编码
   */
  private String dictCode;

  /**
   * 字典值
   */
  private String value;

  /**
   * 字典排序
   */
  private String order;

  CycleTypeEnum(Integer key, String dictCode, String value, String order) {
    this.key = key;
    this.dictCode = dictCode;
    this.order = order;
    this.value = value;
  }

  public Integer getKey() {
    return key;
  }

  public String getDictCode() {
    return dictCode;
  }

  public String getValue() {
    return value;
  }

  public String getOrder() {
    return order;
  }

  /**
   * 通过key获取 CycleTypeEnum
   *
   * @param key
   * @return
   */
  public static CycleTypeEnum getByKey(Integer key) {
    return Arrays.stream(CycleTypeEnum.values()).filter(item -> Objects.equals(item.getKey(), key))
        .findFirst().orElse(null);
  }
}