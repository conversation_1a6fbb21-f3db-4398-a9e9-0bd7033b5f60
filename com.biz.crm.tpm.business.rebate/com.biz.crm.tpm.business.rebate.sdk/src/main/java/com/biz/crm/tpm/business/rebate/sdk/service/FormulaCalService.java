package com.biz.crm.tpm.business.rebate.sdk.service;

import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;

import java.math.BigDecimal;

/**
 * @Description 公式计算
 * <AUTHOR>
 * @Date 2024/7/13 17:55
 */
public interface FormulaCalService {

    /**
     *
     * @param calType {@link  com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum}
     * @param formulaConfigCode 公式编码
     * @param vo
     * @return
     */
    BigDecimal calResult(String calType, String formulaConfigCode, FormulaCalBaseVo vo);
}
