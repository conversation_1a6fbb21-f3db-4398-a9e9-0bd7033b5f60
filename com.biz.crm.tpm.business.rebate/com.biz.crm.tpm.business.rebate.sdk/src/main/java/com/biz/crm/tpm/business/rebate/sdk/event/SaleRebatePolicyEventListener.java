package com.biz.crm.tpm.business.rebate.sdk.event;

import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyVo;
import java.util.List;

/**
 * @description: 返利政策通知接口
 * @author: lifei
 * @date: 2022/2/17 11:13
 */
public interface SaleRebatePolicyEventListener {
  /**
   * 创建时触发
   *
   * @param vo
   */
  default void onCreate(SaleRebatePolicyVo vo) {
  }

  /**
   * 编辑时触发
   *
   * @param oldVo
   * @param newVo
   */
  default void onUpdate(SaleRebatePolicyVo oldVo, SaleRebatePolicyVo newVo) {
  }

  /**
   * 启用时触发
   *
   * @param list
   */
  default void onEnable(List<SaleRebatePolicyVo> list) {
  }

  /**
   * 禁用时触发
   *
   * @param list
   */
  default void onDisable(List<SaleRebatePolicyVo> list) {
  }

}
