package com.biz.crm.tpm.business.rebate.sdk.vo.scope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 以圈定组织机构范围的方式，确认特定返利政策的适用客户范围
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebatePolicyScopeOrgInfoVo", description = "返利组织范围vo")
public class SaleRebatePolicyScopeOrgInfoVo extends AbstractSaleRebatePolicyCustomerInfo {
  private static final long serialVersionUID = 1L;

  @ApiModelProperty("被选定或者被排除的组织机构业务编号")
  private String orgCode;
  
  @ApiModelProperty("被选定或者被排除的组织机构业务名称")
  private String orgName;

}
