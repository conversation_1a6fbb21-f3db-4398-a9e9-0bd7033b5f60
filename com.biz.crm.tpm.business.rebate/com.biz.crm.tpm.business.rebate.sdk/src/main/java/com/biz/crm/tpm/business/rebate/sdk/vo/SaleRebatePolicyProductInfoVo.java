package com.biz.crm.tpm.business.rebate.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 返利产品vo
 * @author: lifei
 * @date: 2022/3/13 13:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebatePolicyProductInfoVo", description = "返利产品vo")
public class SaleRebatePolicyProductInfoVo extends TenantVo {
  /**
   * 范围类型（商品或者产品层级）
   */
  @ApiModelProperty("范围类型")
  private String type;

  /**
   * 商品或产品层级编码
   */
  @ApiModelProperty("商品或产品层级编码")
  private String code;

  /**
   * 商品或产品层级名称
   */
  @ApiModelProperty("商品或产品层级名称")
  private String name;

  /**
   * 返利政策业务编号
   */
  @ApiModelProperty("返利政策业务编号")
  private String saleRebatePolicyCode;

  /**
   * 分配类型 全额 比例
   */
  @ApiModelProperty("分配类型")
  private Integer allocationType;

  /**
   * 分配比例
   */
  @ApiModelProperty("分配比例")
  private BigDecimal rebateRatio;

}
