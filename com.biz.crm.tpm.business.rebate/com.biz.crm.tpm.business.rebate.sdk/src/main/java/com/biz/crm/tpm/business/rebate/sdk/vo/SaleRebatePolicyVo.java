package com.biz.crm.tpm.business.rebate.sdk.vo;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Map;

/**
 * @description: 返利政策vo
 * @author: lifei
 * @date: 2022/2/16 17:17
 */
@Getter
@Setter
@ApiModel(value = "SaleRebatePolicyVo", description = "返利政策vo")
public class SaleRebatePolicyVo extends TenantFlagOpVo {
  /**
   * scope
   */
  private static final long serialVersionUID = 7568087552583066863L;

  /**
   * 返利编码（一旦创建，不允许修改），不输入就是系统生成(按照租户生成)
   */
  @ApiModelProperty("返利编码")
  private String saleRebatePolicyCode;

  /**
   * 返利名称
   */
  @ApiModelProperty("返利名称")
  private String saleRebatePolicyName;

  /**
   * 返利状态
   */
  @ApiModelProperty("返利状态")
  private Integer saleRebatePolicyStatus;

  /**
   * 周期类型 自然年 企业财年
   */
  @ApiModelProperty("周期类型")
  private Integer cycleType;

  /**
   * 返利周期
   */
  @ApiModelProperty("返利周期")
  private String saleRebatePolicyCycle;

  /**
   * 返利周期
   */
  @ApiModelProperty("返利周期名称")
  private String saleRebatePolicyCycleName;

  /**
   * 上账方式 手动 自动
   */
  @ApiModelProperty("上账方式")
  private Integer billType;

  /**
   * 返利政策开始时间（包括）
   */
  @ApiModelProperty("返利政策开始时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateStartTime;

  /**
   * 返利政策结束时间（包括）
   */
  @ApiModelProperty("返利政策结束时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateEndTime;

  /**
   * 返利类型 货补 折扣
   */
  @ApiModelProperty("返利类型")
  private String saleRebateType;

  /**
   * 返利类型名称 货补 折扣
   */
  @ApiModelProperty("返利类型")
  private String saleRebateTypeName;

  /**
   * 计算时间 返利政策结束后第几天
   */
  @ApiModelProperty("计算时间 返利政策结束后第几天")
  private Integer calculateDayNum;

  /**
   * 要素内容
   */
  @ApiModelProperty("要素内容")
  private Map<String, JSONObject> elementDataMap;

  /**
   * 计算公式命中策略类型
   */
  @ApiModelProperty("计算公式命中策略类型")
  private String calculateType;

  /**
   * 可配置销售变量
   */
  @ApiModelProperty("可配置销售变量")
  private JSONObject configurableCriterion;

  /**
   * 是否应用新版公式编辑器的返利政策
   */
  @ApiModelProperty("是否应用新版公式编辑器的返利政策")
  private Boolean isNewType;

  //===============================================认养新增===============================================

  /**
   * 费用使用部门编码
   */
  @ApiModelProperty("费用使用部门编码")
  private String orgCode;

  /**
   * 费用使用部门名称
   */
  @ApiModelProperty("费用使用部门名称")
  private String orgName;

  /**
   * 成本中心编码
   */
  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  /**
   * 成本中心名称
   */
  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  /**
   * 客户编码
   */
  @ApiModelProperty("客户编码")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty("客户名称")
  private String customerName;

  /**
   * 公司代码
   */
  @ApiModelProperty("公司代码")
  private String companyCode;

  /**
   * 公司名称
   */
  @ApiModelProperty("公司名称")
  private String companyName;

  /**
   * 客户ERP编码
   */
  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  /**
   * 客户产品组编码
   */
  @ApiModelProperty("客户产品组编码")
  private String productGroupCode;

  /**
   * 客户分销渠道编码
   */
  @ApiModelProperty("客户分销渠道编码")
  private String channelCode;

  /**
   * 职位编码
   */
  @ApiModelProperty(name = "position_code", value = "职位编码")
  private String positionCode;

  /**
   * 活动编码
   */
  @ApiModelProperty(name = "activity_code", value = "活动编码")
  private String activityCode;

  /**
   * 活动开始时间（包括）
   */
  @ApiModelProperty("活动开始时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date activityStartTime;

  /**
   * 活动结束时间（包括）
   */
  @ApiModelProperty("活动结束时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date activityEndTime;
}
