package com.biz.crm.tpm.business.rebate.sdk.vo.element;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe:返利政策返利产品要素vo
 * @createTime 2022年02月18日 16:29:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "RebateProductSaleRebatePolicyElementDataVo", description = "返利政策返利产品要素vo")
public class RebateProductSaleRebatePolicyElementVo {


  /**
   * ID
   */
  @ApiModelProperty("ID")
  private String id;


  /**
   * 范围类型（商品或者产品层级）
   */
  @ApiModelProperty("范围类型（商品或者产品层级）")
  private String type;

  /**
   * 商品或产品层级编码
   */
  @Column(name = "code", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '商品编码'")
  @ApiModelProperty("商品或产品层级编码")
  private String code;

  /**
   * 商品或产品层级名称
   */
  @Column(name = "name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '商品名称'")
  @ApiModelProperty("商品或产品层级名称")
  private String name;

  /**
   * 分配类型 全额 比例
   */
  @ApiModelProperty("分配类型 (全额、比例)")
  private Integer allocationType;

  /**
   * 分配比例
   */
  @ApiModelProperty("分配比例")
  private BigDecimal rebateRatio;
}
