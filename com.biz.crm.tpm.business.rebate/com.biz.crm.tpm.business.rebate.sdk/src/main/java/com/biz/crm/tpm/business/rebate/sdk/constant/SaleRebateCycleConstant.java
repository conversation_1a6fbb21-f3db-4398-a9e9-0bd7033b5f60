package com.biz.crm.tpm.business.rebate.sdk.constant;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @date 2023-07-14 9:45
 * @description：返利周期定时任务常量模板
 */
public class SaleRebateCycleConstant {

  /**
   * 时间格式化工具
   */
  public static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
  public static final SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd");
  public static final SimpleDateFormat monthFormat = new SimpleDateFormat("yyyyMM");
  public static final SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
  /**
   * 开始时间
   */
  public static final String startTime = " 00:00:01";
  /**
   * 正常的开始时间前缀
   */
  public static final String currentStartTime = " 00:00:00";
  /**
   * 当前财年起始位置
   */
  public static final String yearStartDay = "-01-01";
  /**
   * 结束时间
   */
  public static final String endTime = " 23:59:59";
  /**
   * 定时任务最大预测时间次数
   */
  public static final Integer predictionTimes = 20;

  /**
   * 返利编码前缀
   */
  public static final String REBATE_CODE_PREFIX = "FLZC";
}
