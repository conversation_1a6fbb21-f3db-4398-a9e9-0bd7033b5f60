package com.biz.crm.tpm.business.rebate.sdk.event;

import com.biz.crm.tpm.business.rebate.sdk.dto.DmsFormulaEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * @Description 公式操作事件
 * <AUTHOR>
 * @Date 2024/6/11 18:53
 */
public interface DmsFormulaEventListener extends NebulaEvent {

    /**
     * 创建事件
     *
     * @param dto
     */
    default void onCreate(DmsFormulaEventDto dto) {
    }

    /**
     * 编辑事件
     *
     * @param dto
     */
    default void onUpdate(DmsFormulaEventDto dto) {
    }

    /**
     * 删除事件
     *
     * @param dto
     */
    default void onDelete(DmsFormulaEventDto dto) {
    }

    /**
     * 启用事件
     *
     * @param dto
     */
    default void onEnable(DmsFormulaEventDto dto) {
    }

    /**
     * 禁用事件
     *
     * @param dto
     */
    default void onDisable(DmsFormulaEventDto dto) {
    }
}
