package com.biz.crm.tpm.business.rebate.sdk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @description: 定时任务执行日期范围Vo
 * @author: lifei
 * @date: 2022/2/22 16:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebatePolicyCycleVo", description = "返利周期vo类")
public class SaleRebateExecutionDateRangeVo {

  /**
   * 返利政策开始时间（包括）
   */
  @ApiModelProperty("返利政策开始时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateStartTime;

  /**
   * 返利政策结束时间（包括）
   */
  @ApiModelProperty("返利政策结束时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateEndTime;

  /**
   * 季度
   */
  @ApiModelProperty("季度")
  private String saleRebateQuarter;

  /**
   * 半年度
   */
  @ApiModelProperty("半年度")
  private String saleRebateHalfYear;
}
