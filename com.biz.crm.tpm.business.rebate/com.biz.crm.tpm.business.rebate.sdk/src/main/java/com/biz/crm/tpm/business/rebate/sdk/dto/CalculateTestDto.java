package com.biz.crm.tpm.business.rebate.sdk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @description: 计算测试返利dto
 * @author: lifei
 * @date: 2022/4/7 14:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CalculateTestDto", description = "计算测试返利dto")
public class CalculateTestDto {


  @ApiModelProperty("返利政策编码")
  private String saleRebatePolicyCode;
  /**
   * 查询期间开始时间
   */
  @ApiModelProperty("计算时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date calculateTime;
}
