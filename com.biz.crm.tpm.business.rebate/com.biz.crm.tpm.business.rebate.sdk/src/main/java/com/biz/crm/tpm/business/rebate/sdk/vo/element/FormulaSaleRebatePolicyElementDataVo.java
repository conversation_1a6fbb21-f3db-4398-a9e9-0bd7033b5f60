package com.biz.crm.tpm.business.rebate.sdk.vo.element;

import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyElementDataVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @describe: 返利政策返利要素封装vo
 * @createTime 2022年02月21日 09:51:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FormulaSaleRebatePolicyElementDataVo", description = "返利政策返利要素封装vo")
public class FormulaSaleRebatePolicyElementDataVo extends SaleRebatePolicyElementDataVo {
  
  /**
   * 返利公式 
   */
  private List<FormulaSaleRebatePolicyElementVo> formulaList;

  /**
   * 预提公式
   */
  private List<FormulaSaleRebatePolicyElementVo> withholdingFormulaList;

  /**
   * 返利公式编码
   */
  private String rebateFormulaConfigCode;

  /**
   * 预提公式编码
   */
  private String withholdingFormulaConfigCode;

  /**
   * 返利公式配置
   */
  private FormulaConfigVo rebateFormulaConfig;
  
  /**
   * 预提公式配置
   */
  private FormulaConfigVo withholdingFormulaConfig;
}
