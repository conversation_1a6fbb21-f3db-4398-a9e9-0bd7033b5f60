package com.biz.crm.tpm.business.rebate.sdk.vo.element;

import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyElementDataVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:返利政策考核产品要素封装vo
 * @createTime 2022年02月18日 11:30:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckProductSaleRebatePolicyElementDateVo", description = "返利政策考核产品要素封装vo")
public class CheckProductSaleRebatePolicyElementDataVo extends SaleRebatePolicyElementDataVo {

  private List<CheckProductSaleRebatePolicyElementVo>  checkProductList;
}
