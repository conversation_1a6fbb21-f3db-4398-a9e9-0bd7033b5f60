package com.biz.crm.tpm.business.rebate.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 返利公式计算结果VO
 * @author: lifei
 * @date: 2022/3/8 17:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebateFormulaResultVo", description = "返利公式计算结果VO")
public class SaleRebateFormulaResultVo {

  /**
   * 计算结果
   */
  @ApiModelProperty("计算结果")
  private BigDecimal value;

  /**
   * 计算取值过程（结果）
   */
  @ApiModelProperty("计算过程描述")
  private String computeProcess;

  /**
   * 计算取值过程（参数）
   */
  @ApiModelProperty("替换变量后的表达式")
  private String computeProcessResult;

  /**
   * 替换变量后的表达式
   */
  @ApiModelProperty("替换变量后的表达式")
  private String expressValue;

  /**
   * 原始表达式
   */
  @ApiModelProperty("原始表达式")
  private String expressStr;

  /**
   * 原始表达式
   */
  @ApiModelProperty("原始表达式名称")
  private String expressStrName;
}
