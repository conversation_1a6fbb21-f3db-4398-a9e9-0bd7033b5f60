package com.biz.crm.tpm.business.rebate.sdk.vo.element;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @describe:返利政策范围要素vo
 * @createTime 2022年02月18日 15:26:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ScopeSaleRebatePolicyElementVo", description = "返利政策范围要素vo")
public class ScopeSaleRebatePolicyElementVo {


  /**
   * ID
   */
  @ApiModelProperty("ID")
  private String id;

  /**
   * 类型
   */
  @ApiModelProperty("这个属性将返回这个具体客户范围控制策略的识别类型号，channelForSalePolicy：按渠道选择；distributorForSalePolicy：按经销商选择；orgForSalePolicyCustomer：按组织机构选择")
  private String customerScopeType;

  /**
   * 被选定的业务编号
   */
  @ApiModelProperty("被选定的业务编号")
  private String code;

  /**
   * 被选定的业务名称
   */
  @ApiModelProperty("被选定的业务名称")
  private String name;
}
