package com.biz.crm.tpm.business.rebate.sdk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @description: 构建最小粒度参数vo
 * @author: lifei
 * @date: 2022/3/13 16:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "构建最小粒度参数vo")
public class SaleRebateComputeBuildParamVo {

  /**
   *返利政策编码
   */
  @ApiModelProperty("返利政策编码")
  private SaleRebatePolicyVo saleRebatePolicyVo;

  /**
   *返利政策范围客户编码
   */
  @ApiModelProperty("返利政策范围客户编码")
  private Set<String> customerCodes;

  /**
   *考核商品编码
   */
  @ApiModelProperty("考核商品编码")
  private Set<String> productCodes;

  /**
   *返利商品集合
   */
  @ApiModelProperty("返利商品集合")
  private List<SaleRebatePolicyProductInfoVo> saleRebatePolicyProductInfoVos;

  /**
   *返利公式集合
   */
  @ApiModelProperty("返利公式集合")
  private List<SaleRebatePolicyFormulaInfoVo> saleRebatePolicyFormulaInfoVos;

  /**
   * 返利类型 货补 折扣
   */
  @ApiModelProperty("返利类型")
  private String saleRebateType;

  /**
   * 返利政策开始时间（本次执行 包括企业财年时间）
   */
  @ApiModelProperty("返利政策开始时间（本次执行 包括企业财年时间）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateStartTime;

  /**
   * 返利政策结束时间（本次执行 包括企业财年时间）
   */
  @ApiModelProperty("返利政策结束时间（本次执行 包括企业财年时间）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateEndTime;

  /**
   * 返利批次
   */
  @ApiModelProperty("返利批次")
  private String speedNo;

  /**
   * 计算时间
   */
  @ApiModelProperty("计算时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date calculationTime;



  /**
   * 计算公式命中策略类型
   */
  @ApiModelProperty("计算公式命中策略类型")
  private String calculateType;

}
