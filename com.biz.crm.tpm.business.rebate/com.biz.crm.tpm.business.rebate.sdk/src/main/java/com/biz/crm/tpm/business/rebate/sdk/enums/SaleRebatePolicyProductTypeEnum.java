package com.biz.crm.tpm.business.rebate.sdk.enums;

import java.util.Arrays;
import java.util.Objects;

import lombok.Getter;

/**
 * @description: 返利类型（产品，产品层级）
 * @author: lifei
 * @date: 2022/3/10 22:21
 */
@Getter
public enum SaleRebatePolicyProductTypeEnum {

  PRODUCT_LEVEL(1, "1","产品层级","1"),
  PRODUCT(2, "2","产品","2");

  private Integer key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;

  SaleRebatePolicyProductTypeEnum(Integer key, String dictCode, String value, String order) {
    this.key = key;
    this.dictCode = dictCode;
    this.order = order;
    this.value = value;
  }

  /**
   * 通过key获取 SaleRebatePolicyProductTypeEnum
   *
   * @param key
   * @return
   */
  public static SaleRebatePolicyProductTypeEnum getByKey(String key) {
    return Arrays
        .stream(SaleRebatePolicyProductTypeEnum.values()).filter(item -> Objects.equals(item.getKey(), key)).findFirst().orElse(null);
  }
}
