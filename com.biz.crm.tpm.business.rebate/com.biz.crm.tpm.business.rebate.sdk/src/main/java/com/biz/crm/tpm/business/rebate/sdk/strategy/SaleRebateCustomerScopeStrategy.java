package com.biz.crm.tpm.business.rebate.sdk.strategy;

import com.biz.crm.tpm.business.rebate.sdk.vo.scope.AbstractSaleRebatePolicyCustomerInfo;
import java.util.Set;

/**
 * @description: 可以使用的客户范围选择策略，每一个SaleRebatePolicyCustomerScopeStrategy接口的实现，就是一个具体的客户范围控制策略
 * @author: lifei
 * @date: 2022/2/16 16:27
 */
public interface SaleRebateCustomerScopeStrategy<C extends AbstractSaleRebatePolicyCustomerInfo> {

  /**
   * 这个方法将返回这个具体客户范围控制策略的全系统唯一类型编号，注意：如果系统中类型编号重复，则系统启动时会报错
   *
   * @return
   */
  String getScopeType();

  /**
   * 该方法将返回这个具体客户范围控制策略的中文说明信息
   *
   * @return
   */
  String getScopeTypeDesc();

  /**
   *
   * 排序
   * <AUTHOR>
   * @date
   */
  Integer getSort();

  /**
   * 当系统需要基于某个客户范围选择策略，寻找特定返利政策的可用客户范围（业务编号），该方法将被触发
   *
   * @param saleRebatePolicyCode 需要查询的返利政策业务编号
   * @param tenantCode     当前二级租户信息
   * @return 实现者需要将匹配的一个或者多个客户（例如经销商）业务编号进行返回
   */
   Set<String> onRequestCustomerCodes(String saleRebatePolicyCode, String tenantCode);
}
