package com.biz.crm.tpm.business.rebate.sdk.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/13 14:39
 */
@Data
@ApiModel("公式计算基础vo")
public class FormulaCalBaseVo {

    @ApiModelProperty("活动编码")
    private String actCode;

    @ApiModelProperty("唯一编码：活动明细编码")
    private String actDetailCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("活动开始时间")
    private String startDate;

    @ApiModelProperty("活动结束时间")
    private String endDate;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("达成条件")
    private String conditionNum;

    @ApiModelProperty("返利标准")
    private String giveNum;

    @ApiModelProperty("品项编码")
    private Set<String> itemCodeSet;

    @ApiModelProperty("产品编码")
    private Set<String> productCodeSet;

    @ApiModelProperty("费用品项")
    private Set<String> feeItemCodeSet;

    @ApiModelProperty("费用产品")
    private Set<String> feeProductCodeSet;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("返利开始时间")
    @Transient
    @TableField(exist = false)
    private String rebateStartDate;

    @ApiModelProperty("返利结束时间")
    @Transient
    @TableField(exist = false)
    private String rebateEndDate;

    @ApiModelProperty("缓存key")
    private String cacheKey;

    @ApiModelProperty("方案编码")
    private List<String> excludeSchemeCodes;
}
