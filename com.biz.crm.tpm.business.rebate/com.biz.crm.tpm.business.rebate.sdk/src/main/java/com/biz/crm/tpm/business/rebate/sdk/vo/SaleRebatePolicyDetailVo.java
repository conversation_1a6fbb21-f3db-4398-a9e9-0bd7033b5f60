package com.biz.crm.tpm.business.rebate.sdk.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 返利明细vo
 * @author: lifei
 * @date: 2022/3/9 17:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebatePolicyDetailVo", description = "返利明细vo")
public class SaleRebatePolicyDetailVo extends TenantFlagOpVo {
  private static final long serialVersionUID = 326961825636593003L;

  /**
   * 返利明细编码
   */
  @ApiModelProperty("返利明细编码")
  private String saleRebateDetailCode;

  /**
   * 上账状态 SaleOnAccountStatusEnums
   */
  @ApiModelProperty("上账状态")
  private Integer billStatus;

  /**
   * 返利编码
   */
  @ApiModelProperty("返利编码")
  private String saleRebatePolicyCode;

  /**
   * 返利名称
   */
  @ApiModelProperty("返利名称")
  private String saleRebatePolicyName;

  /**
   * 客户编码
   */
  @ApiModelProperty("客户编码")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty("客户名称")
  private String customerName;

  /**
   * 组织编码
   */
  @ApiModelProperty("组织编码")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty("组织名称")
  private String orgName;

  /**
   * 返利金额
   */
  @ApiModelProperty("返利金额")
  private BigDecimal rebateAmount;

  /**
   * 调整金额
   */
  @ApiModelProperty("调整金额")
  private BigDecimal adjustAmount;

  /**
   * 实际返利金额
   */
  @ApiModelProperty("实际返利金额")
  private BigDecimal actualRebateAmount;

  /**
   * 返利类型 货补 折扣
   */
  @ApiModelProperty("返利类型")
  private String saleRebateType;

  /**
   * 返利类型 货补 折扣
   */
  @ApiModelProperty("返利类型名称")
  private String saleRebateTypeName;


  /**
   * 上账状态 SaleOnAccountStatusEnums
   */
  @ApiModelProperty("返利产品类型（产品/层级）")
  private Integer productType;


  /**
   * 商品编码
   */
  @ApiModelProperty("商品编码")
  private String productCode;

  /**
   * 商品名称
   */
  @ApiModelProperty("商品名称")
  private String productName;

  /**
   * 产品层级编码
   */
  @ApiModelProperty("产品层级编码")
  private String productLevelCode;

  /**
   * 产品层级名称
   */
  @ApiModelProperty("产品层级名称")
  private String productLevelName;

  /**
   * 返利周期
   */
  @ApiModelProperty("返利周期")
  private String saleRebatePolicyCycle;

  /**
   * 返利周期
   */
  @ApiModelProperty("返利周期名称")
  private String saleRebatePolicyCycleName;

  /**
   * 返利政策开始时间（包括）
   */
  @ApiModelProperty("返利政策开始时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date saleRebateStartTime;

  /**
   * 返利政策结束时间（包括）
   */
  @ApiModelProperty("返利政策结束时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date saleRebateEndTime;

  /**
   * 返利计算时间年月
   */
  @ApiModelProperty("返利计算时间年月")
  private String saleRebateCalculationYears;

  /**
   * 返利计算时间
   */
  @ApiModelProperty("返利计算时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date calculationTime;

  /**
   * 分配类型
   *
   */
  @ApiModelProperty("分配类型")
  private Integer allocationType;

  /**
   * 分配比例
   */
  @ApiModelProperty("分配比例")
  private BigDecimal rebateRatio;

  /**
   * 批次号
   */
  @ApiModelProperty("批次号")
  private String speedNo;

  /**
   * 返利公式id
   */
  @ApiModelProperty("返利公式id")
  private String SaleRebatePolicyFormulaId;

  /**
   * 上账方式 BillTypeEnum
   */
  @ApiModelProperty("上账方式")
  private Integer billType;

  /**
   *是否测试
   */
  @ApiModelProperty("是否测试")
  private String isTest;

//===============================================认养新增===============================================

  /**
   * 职位编码
   */
  @ApiModelProperty(name = "position_code", value = "职位编码")
  private String positionCode;

  /**
   * 公司代码
   */
  @ApiModelProperty("公司代码")
  private String companyCode;

  /**
   * 客户ERP编码
   */
  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  /**
   * 客户产品组编码
   */
  @ApiModelProperty("客户产品组编码")
  private String productGroupCode;

  /**
   * 客户分销渠道编码
   */
  @ApiModelProperty("客户分销渠道编码")
  private String channelCode;

  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码")
  @TableField(value = "material_code")
  private String materialCode;

}
