package com.biz.crm.tpm.business.rebate.sdk.enums;

import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 * @describe: 上账状态枚举
 * @createTime 2022年02月25日 15:01:00
 */
@Getter
public enum SaleOnAccountStatusEnums {

  WAIT_ACCOUNT(1, "1","待上账","1"),
  ON_ACCOUNT(2, "2","已上账","2"),
  CANCELLED(3, "3","已作废","3"),
  ;

  private Integer key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;

  SaleOnAccountStatusEnums(Integer key, String dictCode, String value, String order) {
    this.key = key;
    this.dictCode = dictCode;
    this.order = order;
    this.value = value;
  }

  /**
   * 通过key获取 CapitalAdjustTypeEnum
   *
   * @param key
   * @return
   */
  public static SaleOnAccountStatusEnums getByKey(String key) {
    return Arrays
        .stream(SaleOnAccountStatusEnums.values()).filter(item -> Objects.equals(item.getKey(), key)).findFirst().orElse(null);
  }
}
