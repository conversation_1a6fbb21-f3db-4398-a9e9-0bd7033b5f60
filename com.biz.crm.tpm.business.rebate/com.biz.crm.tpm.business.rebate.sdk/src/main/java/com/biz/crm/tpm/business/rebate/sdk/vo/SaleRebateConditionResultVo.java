package com.biz.crm.tpm.business.rebate.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 返利条件计算结果VO
 * @author: lifei
 * @date: 2022/3/8 17:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebateConditionResultVo", description = "返利条件计算结果VO")
public class SaleRebateConditionResultVo {

  /**
   * 计算结果
   */
  @ApiModelProperty("计算结果")
  private boolean value;

  /**
   * 计算过程描述
   */
  @ApiModelProperty("计算过程描述")
  private String computeProcess;

  /**
   * 计算取值过程（参数）
   */
  @ApiModelProperty("计算取值过程")
  private String computeProcessResult;

  /**
   * 替换变量后的表达式
   */
  @ApiModelProperty("替换变量后的表达式")
  private String expressValue;

  /**
   * 原始表达式
   */
  @ApiModelProperty("原始表达式")
  private String expressStr;

  /**
   * 原始表达式
   */
  @ApiModelProperty("原始表达式名称")
  private String expressStrName;
}
