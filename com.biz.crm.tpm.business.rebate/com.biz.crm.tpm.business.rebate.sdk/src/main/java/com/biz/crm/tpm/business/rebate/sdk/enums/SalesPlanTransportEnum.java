package com.biz.crm.tpm.business.rebate.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/24 18:05
 */
@Getter
@AllArgsConstructor
public enum SalesPlanTransportEnum {

    bulk_cargo("bulk_cargo", "大货物流"),
    express("express", "快递物流"),
    cold_chain("cold_chain", "冷链物流"),
    factory("factory", "工厂直发"),
    ;

    private String code;

    private String desc;


    public static Boolean checkDesc(String desc) {
        for (SalesPlanTransportEnum value : SalesPlanTransportEnum.values()) {
            if (desc.equals(value.getDesc())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    public static Boolean checkCode(String code) {
        for (SalesPlanTransportEnum value : SalesPlanTransportEnum.values()) {
            if (code.equals(value.getCode())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    public static String getCode(String desc) {
        String code = null;
        for (SalesPlanTransportEnum value : SalesPlanTransportEnum.values()) {
            if (desc.equals(value.getDesc())) {
                code = value.getCode();
                break;
            }
        }
        return code;
    }
}
