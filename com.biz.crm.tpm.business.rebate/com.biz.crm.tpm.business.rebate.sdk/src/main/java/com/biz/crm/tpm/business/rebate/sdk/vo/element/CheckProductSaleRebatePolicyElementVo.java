package com.biz.crm.tpm.business.rebate.sdk.vo.element;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @describe: 返利政策考核产品要素vo
 * @createTime 2022年02月17日 18:03:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckProductSaleRebatePolicyElementVo", description = "返利政策考核产品要素vo")
public class CheckProductSaleRebatePolicyElementVo {


  /**
   * ID
   */
  @ApiModelProperty("ID")
  private String id;


  /**
   * 范围类型（商品或者产品层级）
   */
  @ApiModelProperty("范围类型（商品或者产品层级）")
  private String type;

  /**
   * 商品或产品层级编码
   */
  @ApiModelProperty("商品或产品层级编码")
  private String code;

  /**
   * 商品或产品层级名称
   */
  @ApiModelProperty("商品或产品层级名称")
  private String name;

  /**
   * 是商品的话存物料编码
   */
  @ApiModelProperty("物料编码")
  private String materialCode;
}
