package com.biz.crm.tpm.business.rebate.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 返利公式vo
 * @author: lifei
 * @date: 2022/3/8 17:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleRebatePolicyFormulaInfoVo", description = "返利公式vo")
public class SaleRebatePolicyFormulaInfoVo extends TenantFlagOpVo {
  private static final long serialVersionUID = -6838481083853694077L;

  /**
   * 公式编码
   */
  @ApiModelProperty("公式编码")
  private String formulaCode;

  /**
   * 公式名称
   */
  @ApiModelProperty("公式编码")
  private String formulaName;
  
  /**
   * 返利政策业务编号
   */
  @ApiModelProperty("返利政策业务编号")
  private String saleRebatePolicyCode;

  /**
   * 返利条件
   */
  @ApiModelProperty("返利条件")
  private String saleRebatePolicyCondition;

  /**
   * 返利条件（展示用）
   */
  @ApiModelProperty("返利条件（展示用）")
  private String saleRebatePolicyConditionName;

  /**
   * 返利公式
   */
  @ApiModelProperty("返利公式")
  private String saleRebatePolicyFormula;

  /**
   * 返利公式（展示用）
   */
  @ApiModelProperty("返利公式（展示用）")
  private String saleRebatePolicyFormulaName;


  /**
   * 公式顺序（计算策略和展示用）
   */
  @ApiModelProperty("公式顺序（计算策略和展示用）")
  private Integer formulaSort;

  /**
   * @see FormulaTypeEnum
   * 公式类型
   */
  @ApiModelProperty("公式类型,预提:withholding,返利:rebate")
  private String formulaType;
}
