package com.biz.crm.tpm.business.rebate.sdk.enums;

import lombok.Getter;

/**
 * @description: 全额 比例
 * @author: lifei
 * @date: 2022/3/10 22:35
 */
@Getter
public enum AllocationTypeEnum {

  FULL_AMOUNT(0,"0", "全额", 1),

  RATIO_AMOUNT(1,"1", "比例", 2);

  /**
   * key
   */
  private Integer key;

  /**
   * 编码
   */
  private String dictCode;

  /**
   * 说明
   */
  private String value;
  /**
   * 排序
   */
  private Integer order;

  AllocationTypeEnum(Integer key, String dictCode, String value, Integer order) {
    this.key = key;
    this.dictCode = dictCode;
    this.value = value;
    this.order = order;
  }

  /**
   * 根据code转枚举
   *
   * @param dictCode
   * @return
   */
  public static AllocationTypeEnum codeToEnum(String dictCode) {
    AllocationTypeEnum result = null;
    for (AllocationTypeEnum allocationTypeEnum : AllocationTypeEnum.values()) {
      if (allocationTypeEnum.dictCode.equals(dictCode)) {
        result = allocationTypeEnum;
      }
    }
    return result;
  }
}
