package com.biz.crm.tpm.business.rebate.sdk.vo.scope;

import com.biz.crm.business.common.sdk.vo.UuidVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 抽象的返利范围客户范围部分的信息描述
 * @author: lifei
 * @date: 2022/2/16 16:59
 */
@Getter
@Setter
@ApiModel(value = "AbstractSaleRebatePolicyCustomerInfo", description = "抽象的返利范围客户范围部分的信息描述")
public abstract class AbstractSaleRebatePolicyCustomerInfo extends UuidVo {

  private static final long serialVersionUID = -5292316124032116232L;

  @ApiModelProperty("优惠政策涉及的二级租户信息")
  private String tenantCode;

  @ApiModelProperty("优惠政策业务编号")
  private String saleRebatePolicyCode;

  @ApiModelProperty("当前优惠政策所使用的客户范围圈定方式,来自于SalePolicyCustomerScopeStrategy接口具体实现中的getScopeType()方法的返回值")
  private String customerScopeType;

}
