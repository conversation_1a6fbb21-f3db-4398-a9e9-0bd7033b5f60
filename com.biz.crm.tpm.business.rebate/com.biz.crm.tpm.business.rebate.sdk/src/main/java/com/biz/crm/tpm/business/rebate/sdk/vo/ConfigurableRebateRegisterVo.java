package com.biz.crm.tpm.business.rebate.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 可配置的返利的注册器的VO
 *
 * <AUTHOR>
 * @date 2022/06/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ConfigurableRebateRegisterVo", description = "可配置的返利的注册器的VO")
public class ConfigurableRebateRegisterVo extends SaleRebatePolicyRegisterVo{

  /**
   * 可配置
   */
  @ApiModelProperty("可配置：false，不可配")
  private boolean configurable;


}
