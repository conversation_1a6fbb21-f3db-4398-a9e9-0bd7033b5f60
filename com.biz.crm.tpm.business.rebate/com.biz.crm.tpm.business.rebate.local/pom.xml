<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>business-rebate</artifactId>
    <groupId>com.biz.crm.tpm.business</groupId>
    <version>202405</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>rebate-local</artifactId>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>com.biz.crm.common.form</groupId>
      <artifactId>form-sdk</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.tpm.business</groupId>
      <artifactId>rebate-sdk</artifactId>
      <version>${business.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>fiscal-year-sdk</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>delivery-sdk</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>sale-goal-sdk</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>customer-sdk</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>org-sdk</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>product-sdk</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>product-level-sdk</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>costpool-discount-sdk</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>costpool-replenishment-sdk</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <!--轻量级表达式计算引擎 -->
    <dependency>
      <groupId>org.eweb4j</groupId>
      <artifactId>fel</artifactId>
      <version>${fel.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.common</groupId>
      <artifactId>common-sequese-sdk</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler</groupId>
      <artifactId>quartz</artifactId>
      <version>${org.quartz.scheduler}</version>
    </dependency>
      <dependency>
          <groupId>com.biz.crm.tpm.business.pay</groupId>
          <artifactId>pay-sdk</artifactId>
        <version>${business.version}</version>
      </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
          <include>**/*.properties</include>
        </includes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
  </build>

</project>