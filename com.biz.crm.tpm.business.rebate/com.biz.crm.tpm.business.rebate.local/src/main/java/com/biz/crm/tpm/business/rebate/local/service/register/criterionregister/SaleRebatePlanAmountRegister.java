package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister;

import com.biz.crm.tpm.business.rebate.local.enums.FormulaVariableEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Map;

/**
 * @description: 费用规划金额
 * @author: lifei
 * @date: 2022/4/6 22:29
 */
@Service
@Slf4j
public class SaleRebatePlanAmountRegister implements SaleRebatePolicyCriterionRegister {

    @Resource
    private FormulaGetValueComponent formulaGetValueComponent;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public String getSaleRebatePolicyCriterionCode() {
        return FormulaVariableEnum.PLAN_AMOUNT.getCode();
    }

    @Override
    public String getSaleRebatePolicyCriterionName() {
        return FormulaVariableEnum.PLAN_AMOUNT.getName();
    }

    @Override
    public Integer getCriterionSort() {
        return FormulaVariableEnum.PLAN_AMOUNT.getSort();
    }

    /**
     * @param vo      计算基准公式
     * @param calType 计算类型
     * @return
     */
    @Override
    public Map<String, BigDecimal> getAmountMapByCondition(FormulaCalBaseVo vo, String calType) {
        Validate.notNull(vo.getApplyAmount(), "申请金额不能为空");
        Map<String, BigDecimal> map = Maps.newHashMap();
        String key = getSaleRebatePolicyCriterionCode() + vo.getCustomerCode();
        BigDecimal amount = vo.getApplyAmount();
        map.put(key, amount);
        return map;
    }


}
