package com.biz.crm.tpm.business.rebate.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateAdjustDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @description: 返利明细调整日志(SaleRebateAdjustDetail)表mybatis访问层
 * @author: lifei
 * @date: 2022/4/11 14:48
 */
public interface SaleRebateAdjustDetailMapper extends BaseMapper<SaleRebateAdjustDetail> {

  /**
   * 分页查询所有数据
   *
   * @param page                     分页对象
   * @param saleRebateAdjustDetail 查询实体
   * @return 所有数据
   */
  public Page<SaleRebateAdjustDetail> findByConditions(
      @Param("page") Page<SaleRebateAdjustDetail> page,
      @Param("dto") SaleRebateAdjustDetail saleRebateAdjustDetail);
}
