package com.biz.crm.tpm.business.rebate.local.strategy;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyScopeInfo;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyScopeInfoService;
import com.biz.crm.tpm.business.rebate.sdk.strategy.SaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.sdk.vo.scope.SaleRebatePolicyScopeDistributorInfoVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 该策略支持按照经销商信息来进行优惠政策范围圈定的处理方式
 *
 * <AUTHOR>
 */
@Component
public class DistributorForSaleRebateCustomerScopeStrategy extends
  AbstractSaleRebateCustomerScopeStrategy implements
  SaleRebateCustomerScopeStrategy<SaleRebatePolicyScopeDistributorInfoVo> {

  @Autowired(required = false)
  private SaleRebatePolicyScopeInfoService salePolicyScopeInfoService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private CustomerVoService customerVoService;

  private static final String SCOPE_TYPE = "distributorForSalePolicy";

  private static final Integer SCOPE_SORT = 1;

  @Override
  public String getScopeType() {
    return SCOPE_TYPE;
  }

  @Override
  public String getScopeTypeDesc() {
    return "客 户";
  }

  @Override
  public Integer getSort() {
    return SCOPE_SORT;
  }

  /**
   * 按照经销商信息来进行优惠政策范围圈定的客户编码集合
   *
   * @param salePolicyCode 需要查询的优惠政策业务编号
   * @param tenantCode     当前二级租户信息
   * @return
   */
  @Override
  public Set<String> onRequestCustomerCodes(String salePolicyCode, String tenantCode) {
    /**
     * 操作步骤：
     * 1、选定方式分组转成经销信息范围的客户编码map
     * 2、从包含中的客户编码过滤掉非包含客户编码
     * 3、获取经销商信息范围的客户编码
     */
    if (StringUtils.isAnyBlank(tenantCode, salePolicyCode)) {
      return null;
    }
    //查询经销商范围的客户结果
    List<SaleRebatePolicyScopeInfo> saleRebatePolicyScopeInfos = this.salePolicyScopeInfoService
        .findByTenantCodeAndSalePolicyCodeAndCustomerScopeType(tenantCode, salePolicyCode,
            SCOPE_TYPE);
    if (CollectionUtils.isEmpty(saleRebatePolicyScopeInfos)) {
      return null;
    }
    //1、======
    List<String> includeList = saleRebatePolicyScopeInfos.stream()
        .filter(a -> StringUtils.isNotBlank(a.getCode())).map(SaleRebatePolicyScopeInfo::getCode)
        .collect(
            Collectors.toList());
    //3、======
    List<CustomerVo> customerVos = this.customerVoService
        .findByCustomerCodes(includeList);
    if (CollectionUtils.isEmpty(customerVos)) {
      return null;
    }
    Set<String> customerCodes = customerVos.stream().map(CustomerVo::getCustomerCode)
        .collect(Collectors.toSet());
    return customerCodes;
  }
}
