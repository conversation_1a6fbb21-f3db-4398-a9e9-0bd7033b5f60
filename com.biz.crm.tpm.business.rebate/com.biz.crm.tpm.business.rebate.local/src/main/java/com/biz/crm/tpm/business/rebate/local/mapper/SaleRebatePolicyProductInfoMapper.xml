<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyProductInfoMapper">

  <resultMap type="com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyProductInfo" id="SaleRebatePolicyProductInfoMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="allocationType" column="allocation_type" jdbcType="INTEGER"/>
    <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
    <result property="productLevelCode" column="product_level_code" jdbcType="VARCHAR"/>
    <result property="productLevelName" column="product_level_name" jdbcType="VARCHAR"/>
    <result property="productName" column="product_name" jdbcType="VARCHAR"/>
    <result property="rebateRatio" column="rebate_ratio" jdbcType="NUMERIC"/>
    <result property="saleRebatePolicyCode" column="sale_rebate_policy_code" jdbcType="VARCHAR"/>
  </resultMap>
  
  <sql id = "saleRebatePolicyProductInfo">
    tenant_code tenantCode,
    allocation_type allocationType,
    product_code productCode,
    product_level_code productLevelCode,
    product_level_name productLevelName,
    product_name productName,
    rebate_ratio rebateRatio,
    sale_rebate_policy_code saleRebatePolicyCode,
    id id
  </sql>

  <select id="findByConditions" resultMap="SaleRebatePolicyProductInfoMap">
    select
      <include refid="saleRebatePolicyProductInfo"/>
    from tpm_sale_rebate_policy_product_info
    where
    tenant_code=#{saleRebatePolicyProductInfo.tenantCode}
  </select>
</mapper>

