package com.biz.crm.tpm.business.rebate.local.service.notifier;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyLogEventDto;
import com.biz.crm.tpm.business.rebate.sdk.event.SaleRebatePolicyLogEventListener;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 返利政策操作日志监听实现
 * <AUTHOR>
 * @Date 2024/5/29 10:05
 */
@Component
public class SaleRebatePolicyLogEventListenerImpl implements SaleRebatePolicyLogEventListener {

    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public void onCreate(SaleRebatePolicyLogEventDto dto) {
        List<SaleRebatePolicyVo> newList = this.getNewList(dto);
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            crmBusinessLogDto.setOldObject(null);
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    @Override
    public void onUpdate(SaleRebatePolicyLogEventDto dto) {
        SaleRebatePolicyVo original = dto.getOriginal();
        SaleRebatePolicyVo newest = dto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    @Override
    public void onDelete(SaleRebatePolicyLogEventDto dto) {
        List<SaleRebatePolicyVo> newList = this.getNewList(dto);
        List<SaleRebatePolicyVo> oldList = (List<SaleRebatePolicyVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, SaleRebatePolicyVo.class, SaleRebatePolicyVo.class, HashSet.class, ArrayList.class);
        Map<String, SaleRebatePolicyVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(SaleRebatePolicyVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            SaleRebatePolicyVo original = oldMap.getOrDefault(newest.getId(), new SaleRebatePolicyVo());
            original.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setDelFlag(DelFlagStatusEnum.DELETE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });

    }

    @Override
    public void onEnable(SaleRebatePolicyLogEventDto dto) {
        List<SaleRebatePolicyVo> newList = this.getNewList(dto);
        List<SaleRebatePolicyVo> oldList = (List<SaleRebatePolicyVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, SaleRebatePolicyVo.class, SaleRebatePolicyVo.class, HashSet.class, ArrayList.class);
        Map<String, SaleRebatePolicyVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(SaleRebatePolicyVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.ENABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            SaleRebatePolicyVo original = oldMap.getOrDefault(newest.getId(), new SaleRebatePolicyVo());
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    @Override
    public void onDisable(SaleRebatePolicyLogEventDto dto) {
        List<SaleRebatePolicyVo> newList = this.getNewList(dto);
        List<SaleRebatePolicyVo> oldList = (List<SaleRebatePolicyVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, SaleRebatePolicyVo.class, SaleRebatePolicyVo.class, HashSet.class, ArrayList.class);
        Map<String, SaleRebatePolicyVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(SaleRebatePolicyVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DISABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            SaleRebatePolicyVo original = oldMap.getOrDefault(newest.getId(), new SaleRebatePolicyVo());
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    private List<SaleRebatePolicyVo> getNewList(SaleRebatePolicyLogEventDto dto) {
        List<SaleRebatePolicyVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        return newList;
    }
}
