package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.biz.crm.common.form.sdk.DynamicFieldConfiguration;
import com.biz.crm.common.form.sdk.DynamicFormService;
import com.biz.crm.common.form.sdk.DynamicFormServiceBuilder;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.service.CriterionVoService;
import com.biz.crm.tpm.business.rebate.sdk.vo.dynamic.AbstractCriterionVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.dynamic.ConfigurableCriterionVo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 返利变量Vo
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
@Service
public class CriterionVoServiceImpl implements CriterionVoService {

  @Autowired(required = false)
  private ApplicationContext applicationContext;

  @Autowired(required = false)
  private List<SaleRebatePolicyCriterionRegister> registers;

  @Override
  public AbstractCriterionVo findByPolicyCodeAndCriterionCode(String saleRebatePolicyCode, String saleRebatePolicyCriterionCode) {
    /**
     * - 只查询入参返利变量的信息
     * - 按入参有限构造动态表单服务
     */
    DynamicFormServiceBuilder<ConfigurableCriterionVo> dynamicFormServiceBuilder = applicationContext.getBean(DynamicFormServiceBuilder.class, ConfigurableCriterionVo.class, this.applicationContext);
    DynamicFieldConfiguration<ConfigurableCriterionVo> dynamicFieldConfiguration = dynamicFormServiceBuilder.dynamicField("criterionMap");
//    if (StringUtils.startsWith(saleRebatePolicyCriterionCode, SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE)) {
//      dynamicFieldConfiguration.addDynamicMapping(SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE, SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE);
//    } else {
//      return null;
//    }
    DynamicFormService<ConfigurableCriterionVo> dynamicFormService = dynamicFieldConfiguration.config().build();
    // 开始做动态表单数据的详情查询
    ConfigurableCriterionVo configurableCriterionVo = new ConfigurableCriterionVo();
    dynamicFormService.perfectDynamicDetails(configurableCriterionVo, saleRebatePolicyCode);
    Map<String, List<AbstractCriterionVo>> criterionMap = configurableCriterionVo.getCriterionMap();
    List<AbstractCriterionVo> abstractCriterionVos = Lists.newArrayList();;
//    if (StringUtils.startsWith(saleRebatePolicyCriterionCode, SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE)) {
//      abstractCriterionVos = criterionMap.get(SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE);
//    } else {
//      abstractCriterionVos = Lists.newArrayList();
//    }
    Optional<AbstractCriterionVo> first = abstractCriterionVos.stream()
        .filter(vo -> vo.getInstanceCode().startsWith(saleRebatePolicyCriterionCode))
        .findFirst();
    return first.get();
  }

  /**
   * 查询当前政策编码下所有的自定义返利变量
   *
   * @param saleRebatePolicyCode
   * @param saleRebatePolicyCriterionCodes  动态表单编码，即前缀，不是实例编码
   * @return
   */
  @Override
  public Map<String, List<AbstractCriterionVo>> findMapByPolicyCodeAndCriterionCode(String saleRebatePolicyCode, List<String> saleRebatePolicyCriterionCodes) {
    /**
     * - 只查询入参返利变量的信息
     * - 按入参有限构造动态表单服务
     */
    DynamicFormServiceBuilder<ConfigurableCriterionVo> dynamicFormServiceBuilder = applicationContext.getBean(DynamicFormServiceBuilder.class, ConfigurableCriterionVo.class, this.applicationContext);
    DynamicFieldConfiguration<ConfigurableCriterionVo> dynamicFieldConfiguration = dynamicFormServiceBuilder.dynamicField("criterionMap");
    Validate.notNull(registers, "返利政策基准注册器没有实现类，请检查！");
    //只保留动态表单的注册器
    Map<String, SaleRebatePolicyCriterionRegister> registerMap = registers.stream()
        .filter(e -> e.isConfigurable())
        .collect(Collectors.toMap(e -> e.getSaleRebatePolicyCriterionCode(), k -> k));
    Map<String, List<AbstractCriterionVo>> result = new HashMap<>();
    if (CollectionUtils.isEmpty(registerMap)) {
      return result;
    }
    for (String saleRebatePolicyCriterionCode : saleRebatePolicyCriterionCodes) {
      if (registerMap.containsKey(saleRebatePolicyCriterionCode)){
        dynamicFieldConfiguration.addDynamicMapping(saleRebatePolicyCriterionCode, saleRebatePolicyCriterionCode);
        DynamicFormService<ConfigurableCriterionVo> dynamicFormService = dynamicFieldConfiguration.config().build();
        // 开始做动态表单数据的详情查询
        ConfigurableCriterionVo configurableCriterionVo = new ConfigurableCriterionVo();
        dynamicFormService.perfectDynamicDetails(configurableCriterionVo, saleRebatePolicyCode);
        Map<String, List<AbstractCriterionVo>> criterionMap = configurableCriterionVo.getCriterionMap();
        if (CollectionUtils.isNotEmpty(criterionMap)){
          result.putAll(criterionMap);
        }
      }
    }
    return result;
  }
}
