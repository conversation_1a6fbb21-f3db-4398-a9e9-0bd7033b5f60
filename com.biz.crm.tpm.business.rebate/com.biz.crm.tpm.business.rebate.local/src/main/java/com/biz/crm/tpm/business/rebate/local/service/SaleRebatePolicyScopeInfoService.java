package com.biz.crm.tpm.business.rebate.local.service;


import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyScopeInfo;

import java.util.List;
import java.util.Set;


/**
 * 以圈定某种范围类型的方式，确认特定返利政策的适用客户范围（标品中支持经销商、渠道和组织机构三种圈定方式(SaleRebatePolicyScopeInfo)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:58
 */
public interface SaleRebatePolicyScopeInfoService {

  /**
   * 批量删除
   *
   * @param idList
   * @return
   */
  void delete(List<String> idList);

  /**
   * 返利编码集合删除
   *
   * @param saleRebatePolicyCode id集合
   * @return Result
   */
  void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode);

  /**
   * 批量保存
   *
   * @param saleRebatePolicyScopeInfos
   * @return
   */
  void createBatch(Set<SaleRebatePolicyScopeInfo> saleRebatePolicyScopeInfos);

  /**
   * 范围类型查询
   *
   * @param tenantCode
   * @param salePolicyCode
   * @param customerScopeType
   * <AUTHOR>
   * @date
   */
  List<SaleRebatePolicyScopeInfo> findByTenantCodeAndSalePolicyCodeAndCustomerScopeType(
      String tenantCode, String salePolicyCode, String customerScopeType);


  /**
   * 通过返利政策编码查询
   *
   * @param saleRebatePolicyCode
   * @return
   */
  List<SaleRebatePolicyScopeInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode);

  /**
   * 批量更新
   *
   * @param saleRebatePolicyScopeInfos
   */
  void updateBatch(Set<SaleRebatePolicyScopeInfo> saleRebatePolicyScopeInfos);

}

