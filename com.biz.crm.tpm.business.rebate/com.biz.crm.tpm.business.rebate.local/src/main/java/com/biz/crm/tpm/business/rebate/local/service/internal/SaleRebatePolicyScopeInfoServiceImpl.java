package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyScopeInfo;
import com.biz.crm.tpm.business.rebate.local.repository.SaleRebatePolicyScopeInfoRepository;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyScopeInfoService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyService;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyTemplateRegister;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.ArrayList;
import java.util.Set;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 以圈定某种范围类型的方式，确认特定返利政策的适用客户范围（标品中支持经销商、渠道和组织机构三种圈定方式(SaleRebatePolicyScopeInfo)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:58
 */
@Service("saleRebatePolicyScopeInfoService")
public class SaleRebatePolicyScopeInfoServiceImpl implements SaleRebatePolicyScopeInfoService {

  @Autowired(required = false)
  private SaleRebatePolicyScopeInfoRepository saleRebatePolicyScopeInfoRepository;
  @Autowired(required = false)
  private List<SaleRebatePolicyTemplateRegister> saleRebatePolicyTemplateRegisters;
  @Autowired(required = false)
  private SaleRebatePolicyService saleRebatePolicyService;
  @Autowired(required = false)
  private ApplicationContext applicationContext;

  /**
   * id集合删除
   *
   * @param idList id集合
   * @return Result
   */
  @Transactional
  @Override
  public void delete(List<String> idList) {
    Validate.isTrue(!CollectionUtils.isEmpty(idList), "传入id集合为空");
    this.saleRebatePolicyScopeInfoRepository.removeByIdsAndTenantCode(idList,TenantUtils.getTenantCode());
  }

  /**
   * 返利编码删除
   *
   * @param saleRebatePolicyCode 返利编码
   * @return Result
   */
  @Transactional
  @Override
  public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    Validate.notBlank(saleRebatePolicyCode, "传入返利编码为空");
    this.saleRebatePolicyScopeInfoRepository.deleteBySaleRebatePolicyCode(saleRebatePolicyCode);
  }

  /**
   * 新增验证
   *
   * @param saleRebatePolicyScopeInfo
   */
  private void createValidate(SaleRebatePolicyScopeInfo saleRebatePolicyScopeInfo) {
    Validate.notNull(saleRebatePolicyScopeInfo, "新增时，对象信息不能为空！");
    saleRebatePolicyScopeInfo.setId(null);
    saleRebatePolicyScopeInfo.setTenantCode(TenantUtils.getTenantCode());
    Validate.notBlank(saleRebatePolicyScopeInfo.getTenantCode(), "新增数据时，租户编号不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getCode(), "新增数据时，被选定或者被排除的业务编号不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getCustomerScopeType(),
        "新增数据时，这个属性将返回这个具体客户范围控制策略的识别类型号不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getName(), "新增数据时，被选定或者被排除的业务名称不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getSaleRebatePolicyCode(), "新增数据时，返利政策业务编号不能为空！");

  }

  @Override
  @Transactional
  public void createBatch(Set<SaleRebatePolicyScopeInfo> saleRebatePolicyScopeInfos) {
    Validate.isTrue(!CollectionUtils.isEmpty(saleRebatePolicyScopeInfos),
        "创建返利适用范围信息时，必须至少传入一条适用范围信息，请检查！！");
    long codeCount = saleRebatePolicyScopeInfos.stream()
        .filter(item -> StringUtils.isNotBlank(item.getCode()))
        .map(SaleRebatePolicyScopeInfo::getCode).distinct().count();
    Validate.isTrue(codeCount == saleRebatePolicyScopeInfos.size(),
        "创建返利适用范围信息时，至少有两组范围信息的业务编号重复或者没有填写，请检查!!");
    for (SaleRebatePolicyScopeInfo salePolicyScopeInfo : saleRebatePolicyScopeInfos) {
      this.createHandle(salePolicyScopeInfo);
    }
  }

  /**
   * 根据租户编码、优惠政策业务编号、客户范围控制策略的识别类型号查询数据集合
   *
   * @param tenantCode        租户编码
   * @param salePolicyCode    优惠政策业务编号
   * @param customerScopeType 客户范围控制策略的识别类型号
   * @return
   */
  @Override
  public List<SaleRebatePolicyScopeInfo> findByTenantCodeAndSalePolicyCodeAndCustomerScopeType(
      String tenantCode, String salePolicyCode, String customerScopeType) {
    if (StringUtils.isAnyBlank(tenantCode, salePolicyCode, customerScopeType)) {
      return null;
    }
    return this.saleRebatePolicyScopeInfoRepository
        .findByTenantCodeAndSalePolicyCodeAndCustomerScopeType(tenantCode, salePolicyCode,
            customerScopeType);
  }

  /**
   * 根据政策编码查询
   *
   * @param saleRebatePolicyCode
   * @return
   */
  @Override
  public List<SaleRebatePolicyScopeInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    if (StringUtils.isBlank(saleRebatePolicyCode)) {
      return new ArrayList<>(0);
    }
    return this.saleRebatePolicyScopeInfoRepository
        .findBySaleRebatePolicyCode(saleRebatePolicyCode);
  }

  @Transactional
  @Override
  public void updateBatch(Set<SaleRebatePolicyScopeInfo> saleRebatePolicyScopeInfos) {
    Validate.isTrue(!CollectionUtils.isEmpty(saleRebatePolicyScopeInfos), "返利范围更新数据不存在！");
    for (SaleRebatePolicyScopeInfo saleRebatePolicyScopeInfo : saleRebatePolicyScopeInfos) {
      this.updateValidate(saleRebatePolicyScopeInfo);
      saleRebatePolicyScopeInfo.setTenantCode(TenantUtils.getTenantCode());
    }
    List<String> idList = saleRebatePolicyScopeInfos.stream().map(SaleRebatePolicyScopeInfo::getId)
        .collect(Collectors.toList());
    this.delete(idList);
    this.saleRebatePolicyScopeInfoRepository.saveBatch(saleRebatePolicyScopeInfos);
  }

  /**
   * 编辑验证
   *
   * @param saleRebatePolicyScopeInfo
   */
  private void updateValidate(SaleRebatePolicyScopeInfo saleRebatePolicyScopeInfo) {
    Validate.notNull(saleRebatePolicyScopeInfo, "修改时，对象信息不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getId(), "修改时，不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getTenantCode(), "修改时，租户编号不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getCode(), "修改时，被选定或者被排除的业务编号不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getCustomerScopeType(),
        "修改时，这个属性将返回这个具体客户范围控制策略的识别类型号不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getName(), "修改时，被选定或者被排除的业务名称不能为空！");
    Validate.notBlank(saleRebatePolicyScopeInfo.getSaleRebatePolicyCode(), "修改时，返利政策业务编号不能为空！");

  }

  /**
   * 该私有方法负责处理单条客户范围信息
   *
   * @param saleRebatePolicyScopeInfo
   */
  private void createHandle(SaleRebatePolicyScopeInfo saleRebatePolicyScopeInfo) {
    /*
     * 处理过程为：
     * 1、首先验证批量添加时，每一条数据的正确性
     * 2、依次进行每一条信息的添加
     * */
    // 1、=======
    String tenantCode = TenantUtils.getTenantCode();
    saleRebatePolicyScopeInfo.setTenantCode(tenantCode);
    saleRebatePolicyScopeInfo.setId(null);
    // 必须填写的信息
    String code = saleRebatePolicyScopeInfo.getCode();
    Validate.notBlank(code, "创建返利适用范围信息时，业务编号信息必须填写!!");
    String name = saleRebatePolicyScopeInfo.getName();
    Validate.notBlank(name, "创建返利适用范围信息时，业务名称信息必须填写!!");
    String customerScopeType = saleRebatePolicyScopeInfo.getCustomerScopeType();
    Validate.notBlank(customerScopeType, "创建返利适用范围信息时，范围类型（customerScopeType）信息必须填写!!");
    // 2、=====
    this.saleRebatePolicyScopeInfoRepository.save(saleRebatePolicyScopeInfo);
  }
}

