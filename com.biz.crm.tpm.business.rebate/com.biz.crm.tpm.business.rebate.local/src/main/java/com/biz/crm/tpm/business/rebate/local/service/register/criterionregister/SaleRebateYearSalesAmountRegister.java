package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.tpm.business.rebate.local.enums.FormulaVariableEnum;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Map;

/**
 * @description: 年度销售金额
 * @author: lifei
 * @date: 2022/4/6 22:29
 */
@Service
@Slf4j
public class SaleRebateYearSalesAmountRegister implements SaleRebatePolicyCriterionRegister {

    @Resource
    private FormulaGetValueComponent formulaGetValueComponent;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public String getSaleRebatePolicyCriterionCode() {
        return FormulaVariableEnum.NDXSJE.getCode();
    }

    @Override
    public String getSaleRebatePolicyCriterionName() {
        return FormulaVariableEnum.NDXSJE.getName();
    }

    @Override
    public Integer getCriterionSort() {
        return FormulaVariableEnum.NDXSJE.getSort();
    }

    /**
     * @param vo      计算基准公式
     * @param calType 计算类型
     * @return
     */
    @Override
    public Map<String, BigDecimal> getAmountMapByCondition(FormulaCalBaseVo vo, String calType) {
        Map<String, BigDecimal> map = Maps.newHashMap();
        String key = getSaleRebatePolicyCriterionCode() + vo.getCustomerCode();
        SalesPlanQueryVo queryVo = JSONObject.parseObject(JSONObject.toJSONString(vo), SalesPlanQueryVo.class);
        //时间转换 找到对应当前年月的开始结束时间
        Map<String, String> dateMap = FormulaGetValueComponent.getDateYears(vo.getStartDate(), DateUtil.DEFAULT_YEAR_MONTH_DAY);
        Map<String, String> yearsMap = FormulaGetValueComponent.getDateYears(vo.getStartDate(), DateUtil.DEFAULT_YEAR_MONTH);
        queryVo.setStartDate(dateMap.get(FormulaGetValueComponent.START_DATE));
        queryVo.setEndDate(dateMap.get(FormulaGetValueComponent.END_DATE));
        queryVo.setStartYears(yearsMap.get(FormulaGetValueComponent.START_DATE));
        queryVo.setEndYears(yearsMap.get(FormulaGetValueComponent.END_DATE));

        BigDecimal amount = BigDecimal.ZERO;
        if (FormulaTypeEnum.MARKETING.getCode().equals(calType)) {
            amount = formulaGetValueComponent.salesPlanCalAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
        } else if (FormulaTypeEnum.WITHHOLDING.getCode().equals(calType)) {
            amount = formulaGetValueComponent.signForAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
            //计算拆分占比
            BigDecimal rate = formulaGetValueComponent.splittingRate(vo.getCustomerCode(), vo.getYears(), vo.getOrgCode(),
                    FormulaGetValueComponent.AMOUNT);
            if (rate != null) {
                amount = amount.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
        } else if (FormulaTypeEnum.MANAGEMENT_REPORT.getCode().equals(calType)) {
            amount = formulaGetValueComponent.deliveryAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
            //计算拆分占比
            BigDecimal rate = formulaGetValueComponent.splittingRate(vo.getCustomerCode(), vo.getYears(), vo.getOrgCode(),
                    FormulaGetValueComponent.AMOUNT);
            if (rate != null) {
                amount = amount.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
        } else if (FormulaTypeEnum.END_CASE.getCode().equals(calType)) {
            amount = formulaGetValueComponent.signForAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
            //计算拆分占比
            BigDecimal rate = formulaGetValueComponent.splittingRate(vo.getCustomerCode(), vo.getYears(), vo.getOrgCode(),
                    FormulaGetValueComponent.AMOUNT);
            if (rate != null) {
                amount = amount.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
        }
        map.put(key, amount);
        return map;
    }


}
