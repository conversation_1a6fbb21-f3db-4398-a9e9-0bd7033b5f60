package com.biz.crm.tpm.business.rebate.local.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyDetail;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 返利政策明细，按照租户进行隔离(SaleRebatePolicyDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-25 14:30:52
 */
public interface SaleRebatePolicyDetailService {

  /**
   * 分页查询数据
   *
   * @param pageable               分页对象
   * @param saleRebatePolicyDetail 实体对象
   * @return
   */
  Page<SaleRebatePolicyDetail> findByConditions(Pageable pageable, SaleRebatePolicyDetail saleRebatePolicyDetail);

  /**
   *
   *  条件查询不分页
   * @param saleRebatePolicyDetail
   * <AUTHOR>
   * @date
   */
  List<SaleRebatePolicyDetail> findByConditionsList(SaleRebatePolicyDetail saleRebatePolicyDetail);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  SaleRebatePolicyDetail findById(String id);

  /**
   * 通过主键查询数据集合
   *
   * @param ids 主键
   * @return 数据集合
   */
  List<SaleRebatePolicyDetail> findByIds(List<String> ids);

  /**
   * 新增数据
   *
   * @param saleRebatePolicyDetail 实体对象
   * @return 新增结果
   */
  SaleRebatePolicyDetail createOrUpdate(SaleRebatePolicyDetail saleRebatePolicyDetail);

  /**
   * 作废
   *
   * @param saleRebatePolicyCodes
   */
  void disableBatch(List<String> saleRebatePolicyCodes);

  /**
   * 通过编码集合查询
   *
   * @param saleRebatePolicyCodes
   */
  List<SaleRebatePolicyDetail> findBySaleRebatePolicyCodes(List<String> saleRebatePolicyCodes);

  /**
   * 上账
   *
   * @param ids
   */
  void handleAccount(List<String> ids);

  /**
   * 返利计算(重新计算)
   *
   * @param ids
   */
  void handleCalculation(List<String> ids);

  /**
   * 调整
   *
   * @param saleRebatePolicyDetail
   * @return
   */
  SaleRebatePolicyDetail handleAdjust(SaleRebatePolicyDetail saleRebatePolicyDetail);

  /**
   * 根据返利政策编码和测试状态删除
   * @param saleRebatePolicyCode  返利政策编码
   * @param isTest  是否测试，0：否，1：是 BooleanEnum
   * @param speedNo 计算批次号
   */
  void deleteByRebateCodeAndTest(String saleRebatePolicyCode, String isTest, String speedNo);

}

