package com.biz.crm.tpm.business.rebate.local.constant;

/**
 * @description: 返利政策常量
 * @author: lifei
 * @date: 2022/2/17 10:52
 */
public class SalePolicyConstant {

  /**
   * 返利政策编码
   */
  public static final String SALE_REBATE_POLICY_CODE = "FLZC";

  /**
   * 返利计算批次
   */
  public static final String SALE_REBATE_SPEED_CODE = "FLPC";

  /**
   * 返利明细
   */
  public static final String SALE_REBATE_DETAIL_CODE = "FLMX";

  /**
   * 计算定时任务类名称
   */
  public static final String TASK_INVOKE_BEAN_NAME = "saleRebatePolicyTaskServiceImpl";

  /**
   * 计算定时任务的方法名
   */
  public static final String TASK_METHOD = "handleRebateTask";
  
  /**
   * 返利公式编码
   */
  public static final String SALE_REBATE_FORMULA_CODE = "FLGS";

}
