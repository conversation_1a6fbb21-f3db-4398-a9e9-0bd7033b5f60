package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.rebate.local.enums.FormulaVariableEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @description: 政策期间销售金额(客户)
 * @author: lifei
 * @date: 2022/4/6 22:29
 */
@Service
@Slf4j
public class SaleRebatePolicySalesAmountCusRegister implements SaleRebatePolicyCriterionRegister {

    @Resource
    private FormulaGetValueComponent formulaGetValueComponent;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public String getSaleRebatePolicyCriterionCode() {
        return FormulaVariableEnum.CUS_POLICY_SALES_AMOUNT.getCode();
    }

    @Override
    public String getSaleRebatePolicyCriterionName() {
        return FormulaVariableEnum.CUS_POLICY_SALES_AMOUNT.getName();
    }

    @Override
    public Integer getCriterionSort() {
        return FormulaVariableEnum.CUS_POLICY_SALES_AMOUNT.getSort();
    }

    /**
     * @param vo      计算基准公式
     * @param calType 计算类型
     * @return
     */
    @Override
    public Map<String, BigDecimal> getAmountMapByCondition(FormulaCalBaseVo vo, String calType) {
        Map<String, BigDecimal> map = Maps.newHashMap();
        String key = getSaleRebatePolicyCriterionCode() + vo.getCustomerCode();
        SalesPlanQueryVo queryVo = JSONObject.parseObject(JSONObject.toJSONString(vo),SalesPlanQueryVo.class);
        queryVo.setStartDate(vo.getRebateStartDate());
        queryVo.setEndDate(vo.getRebateEndDate());
        BigDecimal amount = formulaGetValueComponent.signForAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
        map.put(key,amount);
        return map;
    }


}
