//package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.configurable;
//
//import com.biz.crm.dms.business.delivery.sdk.dto.DeliverySalesTargetDto;
//import com.biz.crm.dms.business.delivery.sdk.service.DeliveryDetailVoService;
//import com.biz.crm.dms.business.delivery.sdk.vo.DeliveryDetailVo;
//import com.biz.crm.mdm.business.product.level.sdk.service.ProductLevelVoSdkService;
//import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
//import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
//import com.biz.crm.tpm.business.rebate.local.enums.TimeTypeEnum;
//import com.biz.crm.tpm.business.rebate.local.model.SaleVolumeCriterionVo;
//import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
//import com.biz.crm.tpm.business.rebate.sdk.service.CriterionVoService;
//import com.biz.crm.tpm.business.rebate.sdk.vo.dynamic.AbstractCriterionVo;
//import com.bizunited.nebula.common.util.tenant.TenantUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.CollectionUtils;
//
//import java.math.BigDecimal;
//import java.time.*;
//import java.time.temporal.TemporalAdjusters;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 销量
// *
// * <AUTHOR>
// * @date 2022/06/21
// */
//public class SaleVolumeCriterionImpl implements SaleRebatePolicyCriterionRegister {
//
//    @Autowired(required = false)
//    private CriterionVoService criterionVoService;
//    @Autowired(required = false)
//    private DeliveryDetailVoService deliveryDetailVoService;
//    @Autowired(required = false)
//    private ProductLevelVoSdkService productLevelVoSdkService;
//    @Autowired(required = false)
//    private ProductVoService productVoService;
//    /**
//     * 返利政策基准编码
//     */
//    public static final String REBATE_POLICY_CRITERION_CODE = "CPXSSL";
//
//    @Override
//    public String getSaleRebatePolicyCriterionCode() {
//        return REBATE_POLICY_CRITERION_CODE;
//    }
//
//    @Override
//    public String getSaleRebatePolicyCriterionName() {
//        return "可配销售数量";
//    }
//
//    @Override
//    public Integer getCriterionSort() {
//        return 1;
//    }
//
//    @Override
//    public Map<String, BigDecimal> getAmountMap(String saleRebatePolicyCriterionCode, String saleRebatePolicyCode, Set<String> cusCodes, Set<String> productCode, Date startTime, Date endTime, Date saleRebateStartTime, Date saleRebateEndTime) {
//        /**
//         * - 查询体
//         * - 查询动态表单里存的条件
//         * - 查询体，结合条件
//         */
//        // 查询动态表单信息
//        AbstractCriterionVo criterionVo = criterionVoService.findByPolicyCodeAndCriterionCode(saleRebatePolicyCode, saleRebatePolicyCriterionCode);
//        // 应该把父类转换成哪个子类
//        SaleVolumeCriterionVo saleVolumeCriterionVo = (SaleVolumeCriterionVo) criterionVo;
//        // 使用该vo去干预筛选条件
//        DeliverySalesTargetDto deliverySalesTargetDto = this.getDeliverySalesTargetDto(saleVolumeCriterionVo, productCode);
//        deliverySalesTargetDto.setCustomerCodes(cusCodes);
//        deliverySalesTargetDto.setTenantCode(TenantUtils.getTenantCode());
//        // 使用筛选条件去查询
//        List<DeliveryDetailVo> deliveryDetailVos = this.deliveryDetailVoService.findByRelateCodesInAndGoodsCodesInAndBetweenStartTimeAndEndTime(deliverySalesTargetDto);
//        // 查询结果组装
//        Map<String, BigDecimal> resultMap = this.getResultMap(saleRebatePolicyCriterionCode, cusCodes, deliveryDetailVos);
//        return resultMap;
//    }
//
//    /**
//     * 得到交付销售目标dto
//     *
//     * @param saleVolumeCriterionVo
//     * @param productCodeSet
//     * @return {@link DeliverySalesTargetDto}
//     */
//    private DeliverySalesTargetDto getDeliverySalesTargetDto(SaleVolumeCriterionVo saleVolumeCriterionVo, Set<String> productCodeSet) {
//        Date saleRebateEnd = this.getEndTime(saleVolumeCriterionVo);
//        Date saleRebateBegin = this.getStartTime(saleVolumeCriterionVo);
//        Set<String> productCodeSet1 = this.getProductCodeSet(productCodeSet, saleVolumeCriterionVo);
//        DeliverySalesTargetDto deliverySalesTargetDto = new DeliverySalesTargetDto();
//        deliverySalesTargetDto.setEndTime(saleRebateEnd);
//        deliverySalesTargetDto.setStartTime(saleRebateBegin);
//        deliverySalesTargetDto.setGoodsCodes(productCodeSet1);
//        return deliverySalesTargetDto;
//    }
//
//
//    /**
//     * 根据动态配置的数据来设定考核范围
//     *
//     * @param productCodeSet        产品代码集
//     * @param saleVolumeCriterionVo 销售额标准
//     * @return {@link Set}<{@link String}>
//     */
//    private Set<String> getProductCodeSet(Set<String> productCodeSet, SaleVolumeCriterionVo saleVolumeCriterionVo) {
//        Boolean defaultProduct = saleVolumeCriterionVo.getDefaultProduct();
//        if (defaultProduct) {
//            return productCodeSet;
//        }
//        // 根据动态配置的数据来设定考核范围
//        Set<String> newProductCodeSet = new HashSet<>();
//        // - 层级侧
//        List<String> productLevels = saleVolumeCriterionVo.getProductLevels();
//        if (!CollectionUtils.isEmpty(productLevels)) {
//            Set<String> productLevelSet = new HashSet<>();
//            for (String productLevel : productLevels) {
//                productLevelSet.add(StringUtils.substringAfterLast(productLevel, ":"));
//            }
//            //全部下级层级的编码(本级和下级所有层级)
//            List<String> curAndChildrenCodesByCodes = this.productLevelVoSdkService.findCurAndChildrenCodesByCodes(productLevelSet);
//            //层级下所有产品
//            List<ProductVo> productVos = this.productVoService.findByProductLevelCodes(curAndChildrenCodesByCodes);
//            if (!CollectionUtils.isEmpty(productVos)) {
//                for (ProductVo productVo : productVos) {
//                    newProductCodeSet.add(productVo.getProductCode());
//                }
//            }
//        }
//        // - 商品侧
//        List<String> productCodes = saleVolumeCriterionVo.getProductCodes();
//        if (!CollectionUtils.isEmpty(productCodes)) {
//            for (String productCode : productCodes) {
//                newProductCodeSet.add(StringUtils.substringAfterLast(productCode, ":"));
//            }
//        }
//        return newProductCodeSet;
//    }
//
//    /**
//     * 开始时间
//     *
//     * @param saleVolumeCriterionVo 销售额标准签证官
//     * @return {@link Date}
//     */
//    private Date getStartTime(SaleVolumeCriterionVo saleVolumeCriterionVo) {
//        String timeType = saleVolumeCriterionVo.getTimeType();
//        if (TimeTypeEnum.CUSTOM.getDictCode().equals(timeType)) {
//            return saleVolumeCriterionVo.getLeftTime();
//        } else if (TimeTypeEnum.LAST_MONTH.getDictCode().equals(timeType)) {
//            LocalDateTime now = LocalDateTime.now();
//            LocalDateTime lastMonth = now.minusMonths(1);
//            LocalDateTime firstDayOfLastMonth = lastMonth.with(TemporalAdjusters.firstDayOfMonth());
//            return Date.from(firstDayOfLastMonth.atZone(ZoneId.systemDefault()).toInstant());
//        } else if (TimeTypeEnum.LAST_QUARTER.getDictCode().equals(timeType)) {
//            LocalDate now = LocalDate.now();
//            Month firstMonthOfQuarter = Month.of(now.getMonth().firstMonthOfQuarter().getValue());
//            Month firstMonthOfLastQuarter = firstMonthOfQuarter.minus(3L);
//            int yearOfLastQuarter = firstMonthOfQuarter.getValue() < 4 ? now.getYear() - 1 : now.getYear();
//            LocalDateTime firstDayOfLastQuarter = LocalDateTime.of(LocalDate.of(yearOfLastQuarter, firstMonthOfLastQuarter, 1), LocalTime.MIN);
//            return Date.from(firstDayOfLastQuarter.atZone(ZoneId.systemDefault()).toInstant());
//        } else if (TimeTypeEnum.LAST_YEAR.getDictCode().equals(timeType)) {
//            LocalDateTime now = LocalDateTime.now();
//            LocalDateTime lastYear = now.minusYears(1);
//            LocalDateTime firstDayOfLastYear = lastYear.with(TemporalAdjusters.firstDayOfYear());
//            return Date.from(firstDayOfLastYear.atZone(ZoneId.systemDefault()).toInstant());
//        }
//        return null;
//    }
//
//    /**
//     * 得到结束时间
//     *
//     * @param saleVolumeCriterionVo 销售额标准签证官
//     * @return {@link Date}
//     */
//    private Date getEndTime(SaleVolumeCriterionVo saleVolumeCriterionVo) {
//        String timeType = saleVolumeCriterionVo.getTimeType();
//        if (TimeTypeEnum.CUSTOM.getDictCode().equals(timeType)) {
//            return saleVolumeCriterionVo.getRightTime();
//        } else if (TimeTypeEnum.LAST_MONTH.getDictCode().equals(timeType)) {
//            LocalDateTime now = LocalDateTime.now();
//            LocalDateTime lastMonth = now.minusMonths(1);
//            LocalDateTime lastDayOfLastMonth = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
//            return Date.from(lastDayOfLastMonth.atZone(ZoneId.systemDefault()).toInstant());
//        } else if (TimeTypeEnum.LAST_QUARTER.getDictCode().equals(timeType)) {
//            LocalDate now = LocalDate.now();
//            Month firstMonthOfQuarter = Month.of(now.getMonth().firstMonthOfQuarter().getValue());
//            Month firstMonthOfLastQuarter = firstMonthOfQuarter.minus(1L);
//            int yearOfLastQuarter = firstMonthOfQuarter.getValue() < 4 ? now.getYear() - 1 : now.getYear();
//            LocalDateTime lastDayOfLastQuarter = LocalDateTime.of(LocalDate.of(yearOfLastQuarter, firstMonthOfLastQuarter, firstMonthOfLastQuarter.maxLength()), LocalTime.MAX);
//            return Date.from(lastDayOfLastQuarter.atZone(ZoneId.systemDefault()).toInstant());
//        } else if (TimeTypeEnum.LAST_YEAR.getDictCode().equals(timeType)) {
//            LocalDateTime now = LocalDateTime.now();
//            LocalDateTime lastYear = now.minusYears(1);
//            LocalDateTime lastDayOfLastYear = lastYear.with(TemporalAdjusters.lastDayOfYear());
//            return Date.from(lastDayOfLastYear.atZone(ZoneId.systemDefault()).toInstant());
//        }
//        return null;
//    }
//
//    /**
//     * 得到结果图
//     *
//     * @param instanceCode
//     * @param cusCodes          客户
//     * @param deliveryDetailVos 发货信息
//     * @return {@link Map}<{@link String}, {@link BigDecimal}>
//     */
//    private Map<String, BigDecimal> getResultMap(String instanceCode, Set<String> cusCodes, List<DeliveryDetailVo> deliveryDetailVos) {
//        Map<String, BigDecimal> resultMap = new HashMap<>(cusCodes.size());
//        Map<String, List<DeliveryDetailVo>> relateToDeliveryMap = new HashMap<>(0);
//        if (!CollectionUtils.isEmpty(deliveryDetailVos)) {
//            relateToDeliveryMap = deliveryDetailVos.stream().collect(Collectors.groupingBy(DeliveryDetailVo::getRelateCode));
//        }
//        for (String cusCode : cusCodes) {
//            String key = StringUtils.join(instanceCode, cusCode);
//            List<DeliveryDetailVo> deliveryDetails = relateToDeliveryMap.get(cusCode);
//            if (CollectionUtils.isEmpty(deliveryDetails)) {
//                resultMap.put(key, BigDecimal.ZERO);
//                continue;
//            }
//            BigDecimal reduce = deliveryDetails.stream()
//                    .map(DeliveryDetailVo::getDeliveryQuantity)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            resultMap.put(key, reduce);
//        }
//        return resultMap;
//    }
//
//    @Override
//    public Boolean isConfigurable() {
//        return Boolean.TRUE;
//    }
//}
