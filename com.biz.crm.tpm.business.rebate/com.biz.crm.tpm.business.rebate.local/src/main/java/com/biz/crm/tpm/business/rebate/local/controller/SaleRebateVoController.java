package com.biz.crm.tpm.business.rebate.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyVoService;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @describe:返利政策，按照租户进行隔离(SaleRebatePolicy)
 * @createTime 2022年02月23日 10:10:00
 */
@RestController
@RequestMapping("/v1/saleRebatePolicy/saleRebatePolicy")
@Slf4j
@Api(tags = "返利模块:SaleRebatePolicyVo:返利政策")
public class SaleRebateVoController {
  @Autowired(required = false)
  private SaleRebatePolicyVoService saleRebatePolicyVoService;

  @ApiOperation(value = "查询明细")
  @GetMapping("findDetailById/{id}")
  public Result<SaleRebatePolicyVo> findDetailById(
      @PathVariable @ApiParam(name = "id", value = "id") String id) {
    try {
      SaleRebatePolicyVo vo = this.saleRebatePolicyVoService.findDetailById(id);
      return Result.ok(vo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
