package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @description: 返利政策考核商品
 * @author: lifei
 * @date: 2022/2/15 16:24
 */
@TableName("tpm_sale_rebate_policy_check_product_info")
@Entity
@Getter
@Setter
@Table(name = "tpm_sale_rebate_policy_check_product_info", indexes = {@Index(columnList = "sale_rebate_policy_code")})
@org.hibernate.annotations.Table(appliesTo = "tpm_sale_rebate_policy_check_product_info", comment = "返利政策考核商品")
public class SaleRebatePolicyCheckProductInfo extends TenantEntity {


  private static final long serialVersionUID = -3406180221268773196L;
  /**
   * 返利政策业务编号
   */
  @Column(name = "sale_rebate_policy_code", length = 128, nullable = false, columnDefinition = "VARCHAR(128) COMMENT '返利政策业务编号'")
  @ApiModelProperty("返利政策业务编号")
  private String saleRebatePolicyCode;

  /**
   * 范围类型（商品或者产品层级）
   */
  @Column(name = "type", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '范围类型（商品或者产品层级）'")
  @ApiModelProperty("范围类型（商品或者产品层级）")
  private String type;

  /**
   * 商品或产品层级编码
   */
  @Column(name = "code", nullable = false, length = 32, columnDefinition = "VARCHAR(32) COMMENT '商品编码'")
  @ApiModelProperty("商品或产品层级编码")
  private String code;

  /**
   * 商品或产品层级名称
   */
  @Column(name = "name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '商品名称'")
  @ApiModelProperty("商品或产品层级名称")
  private String name;

  /**
   * 是商品的话存物料编码
   */
  @Column(name = "material_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '物料编码'")
  @ApiModelProperty("物料编码")
  private String materialCode;


}
