package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.tpm.business.rebate.local.enums.FormulaVariableEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @description: 返利政策基准注册（月度销售金额(客户)）
 * @author: lifei
 * @date: 2022/4/6 22:23
 */
@Service
@Slf4j
public class SaleRebateMonthlySalesAmountCusRegister implements SaleRebatePolicyCriterionRegister {


    @Resource
    private FormulaGetValueComponent formulaGetValueComponent;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public String getSaleRebatePolicyCriterionCode() {
        return FormulaVariableEnum.KHYDXSJE.getCode();
    }

    @Override
    public String getSaleRebatePolicyCriterionName() {
        return FormulaVariableEnum.KHYDXSJE.getName();
    }

    @Override
    public Integer getCriterionSort() {
        return FormulaVariableEnum.KHYDXSJE.getSort();
    }


    /**
     * @param vo      计算基准公式
     * @param calType 计算类型
     * @return desc: 根据营销方案规划明细维度“活动期间+客户+品项+产品”维度取“Σ发货金额-Σ退货金额”
     * （发货时间、退货时间在活动期间内的发货数据）
     */
    @Override
    public Map<String, BigDecimal> getAmountMapByCondition(FormulaCalBaseVo vo, String calType) {
        Map<String, BigDecimal> map = Maps.newHashMap();
        String key = getSaleRebatePolicyCriterionCode() + vo.getCustomerCode();
        SalesPlanQueryVo queryVo = JSONObject.parseObject(JSONObject.toJSONString(vo), SalesPlanQueryVo.class);

        Map<String, String> dateMap = FormulaGetValueComponent.getDateMonthly(vo.getStartDate(), DateUtil.DEFAULT_YEAR_MONTH_DAY);
        String startDate = dateMap.get(FormulaGetValueComponent.START_DATE);
        String endDate = dateMap.get(FormulaGetValueComponent.END_DATE);
        queryVo.setStartDate(startDate);
        queryVo.setEndDate(endDate);

        BigDecimal amount = this.formulaGetValueComponent.signForAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
        map.put(key, amount);
        return map;
    }
}
