package com.biz.crm.tpm.business.rebate.local.service.register.elementregister;

import com.biz.crm.tpm.business.rebate.local.entity.TpmRebatePolicyRelaFormulaConfig;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyFormulaInfo;
import com.biz.crm.tpm.business.rebate.local.service.RebatePolicyRelaFormulaConfigService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyFormulaInfoService;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaSaleRebatePolicyElementDataVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaSaleRebatePolicyElementVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年02月21日 09:50:00
 */
@Slf4j
@Service("formulaSaleRebatePolicyElementRegisterImpl")
public class FormulaSaleRebatePolicyElementRegisterImpl implements
    SaleRebatePolicyElementRegister<FormulaSaleRebatePolicyElementDataVo> {

  @Autowired(required = false)
  private SaleRebatePolicyFormulaInfoService saleRebatePolicyFormulaInfoService;

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private RebatePolicyRelaFormulaConfigService relaFormulaConfigService;

  /**
   * 返利政策要素名称
   */
  private static final String REBATE_POLICY_ELEMENT_NAME = "返利政策公式";

  /**
   * 返利政策要素编码
   */
  private static final String REBATE_POLICY_ELEMENT_CODE = "formula";

  /**
   * 排序
   */
  private static final Integer REBATE_POLICY_ELEMENT_SORT = 4;

  @Override
  public String getSaleRebatePolicyElementName() {
    return REBATE_POLICY_ELEMENT_NAME;
  }

  @Override
  public String getSaleRebatePolicyElementCode() {
    return REBATE_POLICY_ELEMENT_CODE;
  }

  @Override
  public Integer getElementSort() {
    return REBATE_POLICY_ELEMENT_SORT;
  }

  @Override
  public Class<FormulaSaleRebatePolicyElementDataVo> getSaleRebatePolicyElementClass() {
    return FormulaSaleRebatePolicyElementDataVo.class;
  }

  @Override
  public FormulaSaleRebatePolicyElementDataVo getBySaleRebatePolicyCode(
      String saleRebatePolicyCode) {
    FormulaSaleRebatePolicyElementDataVo vo = new FormulaSaleRebatePolicyElementDataVo();
      Map<String, FormulaConfigVo> map = this.relaFormulaConfigService.findFormulaConfigMapBySaleRebatePolicyCode(saleRebatePolicyCode);
      this.getDataVoByList(map, vo);
      vo.setSaleRebatePolicyCode(saleRebatePolicyCode);
    return vo;
  }

  @Override
  public FormulaSaleRebatePolicyElementDataVo onRequestSaleRebatePolicyCreate(
          String saleRebatePolicyCode,
          FormulaSaleRebatePolicyElementDataVo formulaSaleRebatePolicyElementDataVo) {
      this.validate(formulaSaleRebatePolicyElementDataVo);
      // 新增公式关联配置
      List<TpmRebatePolicyRelaFormulaConfig> configList = this.getRelaFormulaConfig(saleRebatePolicyCode, formulaSaleRebatePolicyElementDataVo);
      Map<String, FormulaConfigVo> configVoMap = this.relaFormulaConfigService.saveBatch(configList);
      this.getDataVoByList(configVoMap, formulaSaleRebatePolicyElementDataVo);
      return formulaSaleRebatePolicyElementDataVo;
  }

    private List<TpmRebatePolicyRelaFormulaConfig> getRelaFormulaConfig(String saleRebatePolicyCode,
                                                                        FormulaSaleRebatePolicyElementDataVo elementDataVo) {
        List<TpmRebatePolicyRelaFormulaConfig> list = Lists.newArrayList();
        if (StringUtils.isNotBlank(elementDataVo.getRebateFormulaConfigCode())) {
            TpmRebatePolicyRelaFormulaConfig formulaConfig = new TpmRebatePolicyRelaFormulaConfig();
            formulaConfig.setSaleRebatePolicyCode(saleRebatePolicyCode);
            formulaConfig.setFormulaConfigCode(elementDataVo.getRebateFormulaConfigCode());
            formulaConfig.setFormulaType(FormulaTypeEnum.MANAGEMENT_REPORT.getCode());
            formulaConfig.setUniqueKey(DigestUtils.md5DigestAsHex(StringUtils.join(formulaConfig.getSaleRebatePolicyCode(),
                    formulaConfig.getFormulaConfigCode(), formulaConfig.getFormulaType()).getBytes(StandardCharsets.UTF_8)));
            list.add(formulaConfig);
        }
        if (StringUtils.isNotBlank(elementDataVo.getWithholdingFormulaConfigCode())) {
            TpmRebatePolicyRelaFormulaConfig formulaConfig = new TpmRebatePolicyRelaFormulaConfig();
            formulaConfig.setSaleRebatePolicyCode(saleRebatePolicyCode);
            formulaConfig.setFormulaConfigCode(elementDataVo.getWithholdingFormulaConfigCode());
            formulaConfig.setFormulaType(FormulaTypeEnum.WITHHOLDING.getCode());
            formulaConfig.setUniqueKey(DigestUtils.md5DigestAsHex(StringUtils.join(formulaConfig.getSaleRebatePolicyCode(),
                    formulaConfig.getFormulaConfigCode(), formulaConfig.getFormulaType()).getBytes(StandardCharsets.UTF_8)));
            list.add(formulaConfig);
        }
        return list;
    }

    private void validate(FormulaSaleRebatePolicyElementDataVo vo) {
      Validate.notNull(vo, "公式实体不能为空");
      //Validate.notEmpty(vo.getFormulaList(), "返利公式列表不能为空");
      //Validate.notEmpty(vo.getWithholdingFormulaList(), "预提公式列表不能为空");
      //vo.getFormulaList().forEach(v -> {
      //    this.formulaValidate(v);
      //    v.setFormulaType(FormulaTypeEnum.REBATE.getCode());
      //});
      //vo.getWithholdingFormulaList().forEach(v -> {
      //    this.formulaValidate(v);
      //    v.setFormulaType(FormulaTypeEnum.WITHHOLDING.getCode());
      //});
      Validate.notBlank(vo.getRebateFormulaConfigCode(), "返利公式配置编码不能为空");
  }

  @Override
  public FormulaSaleRebatePolicyElementDataVo onRequestSaleRebatePolicyUpdate(
          String saleRebatePolicyCode,
          FormulaSaleRebatePolicyElementDataVo formulaSaleRebatePolicyElementDataVo) {
      this.validate(formulaSaleRebatePolicyElementDataVo);
      List<TpmRebatePolicyRelaFormulaConfig> configList = this.getRelaFormulaConfig(saleRebatePolicyCode, formulaSaleRebatePolicyElementDataVo);
      // 删除管理
      this.relaFormulaConfigService.deleteBySaleRebatePolicyCode(saleRebatePolicyCode);
      // 新增关联
      Map<String, FormulaConfigVo> configVoMap = this.relaFormulaConfigService.saveBatch(configList);
      this.getDataVoByList(configVoMap, formulaSaleRebatePolicyElementDataVo);
      return formulaSaleRebatePolicyElementDataVo;
  }

    /**
     * 根据要素实体集合获得封装vo
     *
     * @param configVoMap
     * @param vo
     */
    private void getDataVoByList(Map<String, FormulaConfigVo> configVoMap,
                                 FormulaSaleRebatePolicyElementDataVo vo) {
        if (MapUtils.isEmpty(configVoMap)) {
            return;
        }
        vo.setRebateFormulaConfig(configVoMap.get(FormulaTypeEnum.MANAGEMENT_REPORT.getCode()));
        vo.setWithholdingFormulaConfig(configVoMap.get(FormulaTypeEnum.WITHHOLDING.getCode()));
        vo.setTenantCode(TenantUtils.getTenantCode());
    }

  /**
   * 根据封装vo获得实体集合
   *
   * @param formulaSaleRebatePolicyElementDataVo
   * @return
   */
  private List<SaleRebatePolicyFormulaInfo> getListByDataVo(String saleRebatePolicyCode,
                                                            FormulaSaleRebatePolicyElementDataVo formulaSaleRebatePolicyElementDataVo) {
      List<FormulaSaleRebatePolicyElementVo> list =
              Lists.newArrayList(formulaSaleRebatePolicyElementDataVo.getFormulaList());
      list.addAll(formulaSaleRebatePolicyElementDataVo.getWithholdingFormulaList());
      //copy list
      List<SaleRebatePolicyFormulaInfo> newList = (List<SaleRebatePolicyFormulaInfo>) this.nebulaToolkitService
              .copyCollectionByWhiteList(list,
                                         FormulaSaleRebatePolicyElementVo.class,
                                         SaleRebatePolicyFormulaInfo.class,
                                         LinkedHashSet.class,
                                         ArrayList.class);
      newList.forEach(e -> {
          e.setSaleRebatePolicyCode(saleRebatePolicyCode);
          e.setTenantCode(TenantUtils.getTenantCode());
      });
      return newList;

  }
  
  private void formulaValidate(FormulaSaleRebatePolicyElementVo vo) {
      Validate.notNull(vo, "公式实体不能为空");
      Validate.notBlank(vo.getSaleRebatePolicyCondition(), "返利条件不能为空");
      Validate.notBlank(vo.getSaleRebatePolicyFormula(), "返利公式不能为空");
      Validate.notBlank(vo.getSaleRebatePolicyCondition(), "返利条件不能为空");
  }
    
}
