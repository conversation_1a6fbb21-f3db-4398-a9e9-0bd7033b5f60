package com.biz.crm.tpm.business.rebate.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.rebate.local.entity.criterion.SaleVolumeCriterion;
import com.biz.crm.tpm.business.rebate.local.mapper.SaleVolumeCriterionMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 返利政策变量——销售数量——Repository
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
@Component
public class SaleVolumeCriterionRepository extends ServiceImpl<SaleVolumeCriterionMapper, SaleVolumeCriterion> {


  /**
   * 根据返利政策编码查询
   *
   * @param saleRebatePolicyCode 返利政策编码
   * @return {@link SaleVolumeCriterion}
   */
  public List<SaleVolumeCriterion> findBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    if (StringUtils.isBlank(saleRebatePolicyCode)) {
      return null;
    }
    return this.lambdaQuery()
        .eq(SaleVolumeCriterion::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .list();
  }

  /**
   * 根据返利政策编码删除
   *
   * @param saleRebatePolicyCode 返利政策编码
   */
  public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    this.lambdaUpdate()
        .eq(SaleVolumeCriterion::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .remove();
  }

  /**
   * 根据返利政策编码和表单实例删除
   *
   * @param saleRebatePolicyCode 销售折扣政策代码
   * @param instanceCode         实例代码
   */
  public void deleteBySaleRebatePolicyCodeAndInstanceCodeIn(String saleRebatePolicyCode, String[] instanceCode) {
    this.lambdaUpdate()
        .eq(SaleVolumeCriterion::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .in(SaleVolumeCriterion::getInstanceCode, instanceCode)
        .remove();
  }

  /**
   * 根据返利政策编码和变量实例集合查询
   *
   * @param saleRebatePolicyCode 返利政策编码
   * @param instanceCodes 实例代码
   */
  public List<SaleVolumeCriterion> findIdAndInstanceByPolicyAndInstanceIn(String saleRebatePolicyCode, List<String> instanceCodes) {
    return this.lambdaQuery()
        .select(SaleVolumeCriterion::getId, SaleVolumeCriterion::getInstanceCode)
        .eq(SaleVolumeCriterion::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .in(SaleVolumeCriterion::getInstanceCode, instanceCodes)
        .list();

  }
}
