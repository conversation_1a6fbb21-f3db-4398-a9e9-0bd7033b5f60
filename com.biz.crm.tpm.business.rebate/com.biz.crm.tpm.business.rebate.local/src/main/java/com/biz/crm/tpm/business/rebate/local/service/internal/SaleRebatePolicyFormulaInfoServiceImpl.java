package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyFormulaInfo;
import com.biz.crm.tpm.business.rebate.local.repository.SaleRebatePolicyFormulaInfoRepository;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyFormulaInfoService;
import com.biz.crm.tpm.business.rebate.local.utils.MathUtil;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 返利政策公式(SaleRebatePolicyFormulaInfo)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
@Service("saleRebatePolicyFormulaInfoService")
public class SaleRebatePolicyFormulaInfoServiceImpl implements SaleRebatePolicyFormulaInfoService {

  @Autowired(required = false)
  private SaleRebatePolicyFormulaInfoRepository saleRebatePolicyFormulaInfoRepository;
  @Autowired(required = false)
  private List<SaleRebatePolicyCriterionRegister> saleRebatePolicyCriterionRegisters;

  /**
   * 批量删除
   *
   * @param idList
   * @return
   */
  @Transactional
  @Override
  public void delete(List<String> idList) {
    Validate.isTrue(!CollectionUtils.isEmpty(idList), "数据操作时，主键集合不能为空！");
    this.saleRebatePolicyFormulaInfoRepository.removeByIdsAndTenantCode(idList,TenantUtils.getTenantCode());
  }

  /**
   * 返利编码删除
   *
   * @param saleRebatePolicyCode 返利编码
   * @return Result
   */
  @Transactional
  @Override
  public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    Validate.notBlank(saleRebatePolicyCode, "传入返利编码为空");
    this.saleRebatePolicyFormulaInfoRepository.deleteBySaleRebatePolicyCode(saleRebatePolicyCode);
  }

  /**
   * 根据编码查询
   *
   * @param saleRebatePolicyCode
   * @return
   */
  @Override
  public List<SaleRebatePolicyFormulaInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    if (StringUtils.isBlank(saleRebatePolicyCode)) {
      return null;
    }
    return this.saleRebatePolicyFormulaInfoRepository
        .findBySaleRebatePolicyCode(saleRebatePolicyCode);
  }

  @Override
  public List<SaleRebatePolicyFormulaInfo> findBySaleRebatePolicyCodes(
      List<String> saleRebatePolicyCodes) {
    if (CollectionUtils.isEmpty(saleRebatePolicyCodes)) {
      return null;
    }
    return this.saleRebatePolicyFormulaInfoRepository
        .findBySaleRebatePolicyCodes(saleRebatePolicyCodes, FormulaTypeEnum.MANAGEMENT_REPORT.getCode());
  }

  /**
   * 批量新增
   *
   * @param list
   */
  @Transactional
  @Override
  public void createBatch(List<SaleRebatePolicyFormulaInfo> list) {
    Validate.isTrue(!CollectionUtils.isEmpty(list), "返利公式新增数据缺失");
    for (SaleRebatePolicyFormulaInfo saleRebatePolicyFormulaInfo : list) {
      this.createValidate(saleRebatePolicyFormulaInfo);
      saleRebatePolicyFormulaInfo.setTenantCode(TenantUtils.getTenantCode());
    }
    this.saleRebatePolicyFormulaInfoRepository.saveBatch(list);
  }

  /**
   * 根据id集合查询公式
   *
   * @param ids
   * @return
   */
  @Override
  public List<SaleRebatePolicyFormulaInfo> findByIds(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "返利公式id集合缺失");
    return this.saleRebatePolicyFormulaInfoRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  /**
   * 新增验证
   *
   * @param saleRebatePolicyFormulaInfo
   */
  private void createValidate(SaleRebatePolicyFormulaInfo saleRebatePolicyFormulaInfo) {
    Validate.notNull(saleRebatePolicyFormulaInfo, "返利公式数据操作时，对象信息不能为空！");
    saleRebatePolicyFormulaInfo.setId(null);
    saleRebatePolicyFormulaInfo.setTenantCode(TenantUtils.getTenantCode());
    Validate.notBlank(saleRebatePolicyFormulaInfo.getTenantCode(), "返利公式数据操作时，租户编号不能为空！");
    Validate.notBlank(saleRebatePolicyFormulaInfo.getSaleRebatePolicyCode(), "返利公式数据操作时，返利政策业务编号不能为空！");
    Validate
        .notBlank(saleRebatePolicyFormulaInfo.getSaleRebatePolicyCondition(), "返利公式数据操作时，返利条件不能为空！");
    Validate.notBlank(saleRebatePolicyFormulaInfo.getSaleRebatePolicyConditionName(),
        "返利公式数据操作时，返利条件（展示用）不能为空！");
    Validate.notBlank(saleRebatePolicyFormulaInfo.getSaleRebatePolicyFormula(), "返利公式数据操作时，返利公式不能为空！");
    Validate.notBlank(saleRebatePolicyFormulaInfo.getSaleRebatePolicyFormulaName(),
        "返利公式数据操作时，返利公式（展示用）不能为空！");
    //验证公式正确性
    String saleRebatePolicyCondition = MathUtil.replaceDefaultVariable(saleRebatePolicyFormulaInfo.getSaleRebatePolicyCondition());
    String saleRebatePolicyFormula = MathUtil.replaceDefaultVariable(saleRebatePolicyFormulaInfo.getSaleRebatePolicyFormula());
    try {
      Validate.notNull(MathUtil.computeCondition(saleRebatePolicyCondition),"条件验证计算失败，请检查条件");
    } catch (Exception e) {
      throw new IllegalArgumentException(StringUtils.join("条件验证计算失败，请检查条件", saleRebatePolicyFormulaInfo.getSaleRebatePolicyCondition()), e);
    }
    try {
      Validate.notNull(MathUtil.computeFormula(saleRebatePolicyFormula), "公式验证计算失败，请检查公式");
    } catch (Exception e) {
      throw new IllegalArgumentException(StringUtils.join("公式验证计算失败，请检查公式", saleRebatePolicyFormulaInfo.getSaleRebatePolicyFormula()), e);
    }
  }

}

