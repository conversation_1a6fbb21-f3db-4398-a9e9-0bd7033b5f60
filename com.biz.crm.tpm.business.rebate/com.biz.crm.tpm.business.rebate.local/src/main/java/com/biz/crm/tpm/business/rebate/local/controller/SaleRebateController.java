package com.biz.crm.tpm.business.rebate.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicy;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyCalculateService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyTaskService;
import com.biz.crm.tpm.business.rebate.sdk.dto.CalculateTestDto;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebateCronDto;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateCronVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * 返利政策，按照租户进行隔离(SaleRebatePolicy)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:46
 */
@RestController
@RequestMapping("/v1/saleRebatePolicy/saleRebatePolicy")
@Slf4j
@Api(tags = "返利模块:SaleRebatePolicy:返利政策")
public class SaleRebateController {

  @Autowired(required = false)
  private SaleRebatePolicyService saleRebatePolicyService;
  @Autowired(required = false)
  private SaleRebatePolicyCalculateService saleRebatePolicyCalculateService;
  @Autowired(required = false)
  private SaleRebatePolicyTaskService saleRebatePolicyTaskService;

  /**
   * 分页查询
   *
   * @param pageable         分页对象
   * @param saleRebatePolicy 查询实体
   * @return
   */
  @ApiOperation(value = "分页查询")
  @GetMapping("findByConditions")
  public Result<Page<SaleRebatePolicy>> findByConditions(
      @ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
      @ApiParam(name = "saleRebatePolicy", value = "返利政策") SaleRebatePolicy saleRebatePolicy) {
    try {
      Page<SaleRebatePolicy> page = this.saleRebatePolicyService
          .findByConditions(pageable, saleRebatePolicy);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增
   *
   * @param saleRebatePolicyDto 返利政策Dto
   * <AUTHOR>
   * @date
   */
  @ApiOperation(value = "新增")
  @PostMapping
  public Result<SaleRebatePolicy> create(
      @ApiParam(name = "saleRebatePolicy", value = "返利政策，按照租户进行隔离") @RequestBody SaleRebatePolicyDto saleRebatePolicyDto) {
    try {
      SaleRebatePolicy result = this.saleRebatePolicyService.create(saleRebatePolicyDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 编辑
   *
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  @ApiOperation(value = "编辑")
  @PatchMapping
  public Result<SaleRebatePolicy> update(
      @ApiParam(name = "saleRebatePolicy", value = "返利政策，按照租户进行隔离") @RequestBody SaleRebatePolicyDto saleRebatePolicyDto) {
    try {
      SaleRebatePolicy result = this.saleRebatePolicyService.update(saleRebatePolicyDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除公告
   *
   * @param ids 公告id集合
   * @return 操作结果
   */
  @ApiOperation(value = "删除")
  @DeleteMapping
  public Result<?> delete(@RequestParam("ids") List<String> ids) {
    try {
      this.saleRebatePolicyService.delete(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 恢复
   *
   * @param ids
   */
  @ApiOperation(value = "恢复")
  @PatchMapping("/enableBatch")
  public Result<?> enableBatch(@RequestBody List<String> ids) {
    try {
      this.saleRebatePolicyService.enableBatch(ids);
      return Result.ok("恢复成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 作废
   *
   * @param ids
   */
  @ApiOperation(value = "作废")
  @PatchMapping("/disableBatch")
  public Result<?> disableBatch(@RequestBody List<String> ids) {
    try {
      this.saleRebatePolicyService.disableBatch(ids);
      return Result.ok("作废成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   * 测试政策接口
   * 注：此接口是测试定时任务跑出来的返利明细数据的接口
   *
   * @param calculateTestDto
   */
  @ApiOperation(value = "测试政策接口")
  @PostMapping("/onCalculateTestByCode")
  public Result<?> onCalculateTestByCode(@RequestBody CalculateTestDto calculateTestDto) {
    try {
      this.saleRebatePolicyCalculateService.onCalculateByCode(
          Collections.singletonList(calculateTestDto.getSaleRebatePolicyCode()),BooleanEnum.FALSE.getNumStr(),calculateTestDto.getCalculateTime());
      return Result.ok("操作成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 预测未来即将返利的周期时间表
   *
   * @param dto
   * @return
   */
  @ApiOperation(value = "预测未来即将返利的周期时间表")
  @PostMapping("predictionSaleRebateTimes")
  public Result<List<SaleRebateCronVo>> predictionSaleRebateTimes(@RequestBody SaleRebateCronDto dto) {
    try {
      List<SaleRebateCronVo> result = saleRebatePolicyTaskService.predictionSaleRebateTimes(dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
