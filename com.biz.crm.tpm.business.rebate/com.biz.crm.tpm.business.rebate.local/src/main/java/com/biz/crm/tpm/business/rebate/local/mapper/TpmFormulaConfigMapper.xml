<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.rebate.local.mapper.TpmFormulaConfigMapper">

    <select id="findByConditions"
            resultType="com.biz.crm.tpm.business.rebate.local.entity.FormulaConfigEntity">
        select * from tpm_formula_config
        where del_flag = '${@<EMAIL>()}'
        and tenant_code = #{dto.tenantCode}
        <if test="dto.formulaConfigCode != null and dto.formulaConfigCode != ''">
            and formula_config_code = #{dto.formulaConfigCode}
        </if>
        <if test="dto.formulaConfigName != null and dto.formulaConfigName != ''">
            and formula_config_name like concat('%', #{dto.formulaConfigName}, '%')
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and enable_status = #{dto.enableStatus}
        </if>
        order by create_time desc
    </select>
</mapper>