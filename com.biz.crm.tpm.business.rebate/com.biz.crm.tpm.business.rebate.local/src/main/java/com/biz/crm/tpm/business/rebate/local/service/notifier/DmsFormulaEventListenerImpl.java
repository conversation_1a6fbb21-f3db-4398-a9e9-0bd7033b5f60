package com.biz.crm.tpm.business.rebate.local.service.notifier;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.rebate.sdk.dto.DmsFormulaEventDto;
import com.biz.crm.tpm.business.rebate.sdk.event.DmsFormulaEventListener;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 公式操作事件实现
 * <AUTHOR>
 * @Date 2024/6/11 18:55
 */
@Component
public class DmsFormulaEventListenerImpl implements DmsFormulaEventListener {
    
    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;
    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public void onCreate(DmsFormulaEventDto dto) {
        List<FormulaConfigVo> newList = this.getNewList(dto);
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            crmBusinessLogDto.setOldObject(null);
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    @Override
    public void onUpdate(DmsFormulaEventDto dto) {
        FormulaConfigVo original = dto.getOriginal();
        FormulaConfigVo newest = dto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    @Override
    public void onDelete(DmsFormulaEventDto dto) {
        List<FormulaConfigVo> newList = this.getNewList(dto);
        List<FormulaConfigVo> oldList = (List<FormulaConfigVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, FormulaConfigVo.class, FormulaConfigVo.class, HashSet.class, ArrayList.class);
        Map<String, FormulaConfigVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(FormulaConfigVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            FormulaConfigVo original = oldMap.getOrDefault(newest.getId(), new FormulaConfigVo());
            original.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setDelFlag(DelFlagStatusEnum.DELETE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });

    }

    @Override
    public void onEnable(DmsFormulaEventDto dto) {
        List<FormulaConfigVo> newList = this.getNewList(dto);
        List<FormulaConfigVo> oldList = (List<FormulaConfigVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, FormulaConfigVo.class, FormulaConfigVo.class, HashSet.class, ArrayList.class);
        Map<String, FormulaConfigVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(FormulaConfigVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.ENABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            FormulaConfigVo original = oldMap.getOrDefault(newest.getId(), new FormulaConfigVo());
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    @Override
    public void onDisable(DmsFormulaEventDto dto) {
        List<FormulaConfigVo> newList = this.getNewList(dto);
        List<FormulaConfigVo> oldList = (List<FormulaConfigVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, FormulaConfigVo.class, FormulaConfigVo.class, HashSet.class, ArrayList.class);
        Map<String, FormulaConfigVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(FormulaConfigVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DISABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            FormulaConfigVo original = oldMap.getOrDefault(newest.getId(), new FormulaConfigVo());
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    private List<FormulaConfigVo> getNewList(DmsFormulaEventDto dto) {
        List<FormulaConfigVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        return newList;
    }
}
