package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaCalculationLog;
import com.biz.crm.tpm.business.rebate.local.mapper.FormulaCalculationLogMapper;
import com.biz.crm.tpm.business.rebate.local.service.FormulaCalculationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.ws.WebServiceRefs;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/28 18:07
 */
@Service
@Slf4j
public class FormulaCalculationLogServiceImpl extends ServiceImpl<FormulaCalculationLogMapper, FormulaCalculationLog> implements FormulaCalculationLogService {


    @Override
    public Page<FormulaCalculationLog> findPage(Pageable pageable, FormulaCalculationLog formulaCalculationLog) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<FormulaCalculationLog> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findPage(page, formulaCalculationLog);
    }

    @Override
    public List<FormulaCalculationLog> findListByDetailCodeAndCalType(String detailCode, String calType) {
        return this.lambdaQuery()
                .eq(FormulaCalculationLog::getBusinessDetailCode, detailCode)
                .eq(FormulaCalculationLog::getCalType, calType)
                .list();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatchList(List<FormulaCalculationLog> list) {
        list.forEach(x -> {
            x.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            x.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        });
        this.saveBatch(list);
    }
}
