package com.biz.crm.tpm.business.rebate.local.widget;

import com.biz.crm.common.form.sdk.widget.WidgetKey;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 复选框
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Component
public class SimpleCheckboxWidget implements WidgetKey {
  @Override
  public String widgetCode() {
    return "simpleCheckboxWidget";
  }

  @Override
  public String widgetName() {
    return "复选框";
  }

  @Override
  public Map<String, Object> widgetParam() {
    return null;
  }
}
