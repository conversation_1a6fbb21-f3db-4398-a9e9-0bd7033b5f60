package com.biz.crm.tpm.business.rebate.local.entity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;


/**
 * @description: 以圈定某种范围类型的方式，确认特定返利政策的适用客户范围（标品中支持经销商、渠道和组织机构三种圈定方式）
 * @author: lifei
 * @date: 2022/2/15 14:42
 */
@TableName("tpm_sale_rebate_policy_scope_info")
@Entity
@Getter
@Setter
@Table(name = "tpm_sale_rebate_policy_scope_info" , indexes = {@Index(columnList = "sale_rebate_policy_code")})
@org.hibernate.annotations.Table(appliesTo = "tpm_sale_rebate_policy_scope_info", comment = "以圈定某种范围类型的方式，确认特定返利政策的适用客户范围（标品中支持经销商、渠道和组织机构三种圈定方式")
public class SaleRebatePolicyScopeInfo extends TenantEntity {

  private static final long serialVersionUID = 8493459981336488546L;
  /**
   * 返利政策业务编号
   */
  @Column(name = "sale_rebate_policy_code" , nullable = false, columnDefinition = "VARCHAR(128) COMMENT '返利政策业务编号'")
  @ApiModelProperty("返利政策业务编号")
  private String saleRebatePolicyCode;

  /**
   * 类型 渠道 组织 经销商
   */
  @Column(name = "customer_scope_type" , nullable = false, columnDefinition = "VARCHAR(64) COMMENT '这个属性将返回这个具体客户范围控制策略的识别类型号'")
  @ApiModelProperty("这个属性将返回这个具体客户范围控制策略的识别类型号，channelForSalePolicy：按渠道选择；distributorForSalePolicy：按经销商选择；orgForSalePolicyCustomer：按组织机构选择")
  private String customerScopeType;

  /**
   * 被选定的业务编号
   */
  @Column(name = "code" , nullable = false, columnDefinition = "VARCHAR(255) COMMENT '被选定或者被排除的业务编号'")
  @ApiModelProperty("被选定或者被排除的业务编号")
  private String code;

  /**
   * 被选定的业务名称
   */
  @Column(name = "name" , nullable = false, columnDefinition = "VARCHAR(255) COMMENT '被选定或者被排除的业务名称'")
  @ApiModelProperty("被选定或者被排除的业务名称")
  private String name;

}
