package com.biz.crm.tpm.business.rebate.local.service;


import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateCalculationLog;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.domain.Pageable;

/**
 * 返利政策计算日志(SaleRebateCalculationLog)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-28 15:19:44
 */
public interface SaleRebateCalculationLogService{

   /**
   * 通过返利明细id查询返利计算日志数据
   * @param saleRebatePolicyDetailId 返利明细主键
   * @return 单条数据
   */
   Page<SaleRebateCalculationLog>  findByDetailId(Pageable pageable, String saleRebatePolicyDetailId);

   /**
   * 新增数据
   * @param saleRebateCalculationLog 实体对象
   * @return 新增结果
   */
  SaleRebateCalculationLog create(SaleRebateCalculationLog saleRebateCalculationLog);

  /**
   * 根据返利政策编码和测试状态删除
   * @param saleRebatePolicyCode  返利政策编码
   * @param isTest  是否测试，0：否，1：是 BooleanEnum
   * @param speedNo 计算批次号
   */
  void deleteByRebateCodeAndTest(String saleRebatePolicyCode, String isTest, String speedNo);

}

