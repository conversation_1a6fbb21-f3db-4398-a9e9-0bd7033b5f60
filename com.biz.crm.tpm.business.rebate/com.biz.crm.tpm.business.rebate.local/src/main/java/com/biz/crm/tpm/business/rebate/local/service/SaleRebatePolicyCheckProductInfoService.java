package com.biz.crm.tpm.business.rebate.local.service;


import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyCheckProductInfo;

import java.util.List;


/**
 * 返利政策考核商品(SaleRebatePolicyCheckProductInfo)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
public interface SaleRebatePolicyCheckProductInfoService {

  /**
   * id集合删除
   *
   * @param idList id集合
   * @return Result
   */
  void delete(List<String> idList);

  /**
   * 返利编码集合删除
   *
   * @param saleRebatePolicyCode id集合
   * @return Result
   */
  void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode);

  /**
   * 通过返利政策编码查询考核产品集合
   *
   * @param saleRebatePolicyCodes
   */
  List<SaleRebatePolicyCheckProductInfo> findBySaleRebatePolicyCodes(List<String> saleRebatePolicyCodes);

  /**
   * 通过返利政策编码查询考核产品集合
   *
   * @param saleRebatePolicyCode
   */
  List<SaleRebatePolicyCheckProductInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode);

  /**
   * 批量创建
   * @param saleRebatePolicyCheckProductInfos
   * @return
   */
  void createBatch(List<SaleRebatePolicyCheckProductInfo> saleRebatePolicyCheckProductInfos);


}

