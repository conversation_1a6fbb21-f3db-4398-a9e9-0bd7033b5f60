<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.rebate.local.mapper.SaleRebateAdjustDetailMapper">

  <resultMap type="com.biz.crm.tpm.business.rebate.local.entity.SaleRebateAdjustDetail" id="SaleRebateAdjustDetailMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="createAccount" column="create_account" jdbcType="VARCHAR"/>
    <result property="createName" column="create_name" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="modifyAccount" column="modify_account" jdbcType="VARCHAR"/>
    <result property="modifyName" column="modify_name" jdbcType="VARCHAR"/>
    <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    <result property="enableStatus" column="enable_status" jdbcType="VARCHAR"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
    <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
    <result property="saleRebateDetailCode" column="sale_rebate_detail_code" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyName" column="sale_rebate_policy_name" jdbcType="VARCHAR"/>
    <result property="adjustAmount" column="adjust_amount" jdbcType="NUMERIC"/>

  </resultMap>

  <select id="findByConditions" resultMap="SaleRebateAdjustDetailMap">
    select a.* from tpm_sale_rebate_adjust_detail a
    <where>
      <if test="dto.delFlag !=null and dto.delFlag !=''">
        and  a.del_flag = #{dto.delFlag}
      </if>
      <if test="dto.enableStatus !=null and dto.enableStatus !=''">
        and  a.enable_status = #{dto.enableStatus}
      </if>
      <if test="dto.tenantCode !=null and dto.tenantCode !=''">
        and a.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.saleRebateDetailCode !=null and dto.saleRebateDetailCode !=''">
        and a.sale_rebate_detail_code = #{dto.saleRebateDetailCode}
      </if>
      <if test="dto.saleRebatePolicyName !=null and dto.saleRebatePolicyName !=''">
        <bind name="likeSaleRebatePolicyName" value="'%' + dto.saleRebatePolicyName + '%'"/>
        and a.sale_rebate_policy_name = #{dto.likeSaleRebatePolicyName}
      </if>
    </where>
    order by a.create_time desc
  </select>
</mapper>

