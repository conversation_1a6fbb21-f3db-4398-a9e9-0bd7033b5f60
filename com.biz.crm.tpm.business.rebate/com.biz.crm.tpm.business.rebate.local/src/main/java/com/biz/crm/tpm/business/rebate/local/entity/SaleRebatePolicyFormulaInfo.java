package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @description: 返利政策公式
 * @author: lifei
 * @date: 2022/2/15 15:56
 */
@TableName("tpm_sale_rebate_policy_formula_info")
@Entity
@Getter
@Setter
@Table(name = "tpm_sale_rebate_policy_formula_info" , indexes = {@Index(columnList = "sale_rebate_policy_code")})
@org.hibernate.annotations.Table(appliesTo = "tpm_sale_rebate_policy_formula_info", comment = "返利政策公式")
public class SaleRebatePolicyFormulaInfo extends TenantEntity {

  private static final long serialVersionUID = -6838481083853694077L;
  /**
   * 返利政策业务编号
   */
  @Column(name = "sale_rebate_policy_code" ,  nullable = false, columnDefinition = "VARCHAR(128) COMMENT '返利政策业务编号'")
  @ApiModelProperty("返利政策业务编号")
  private String saleRebatePolicyCode;

  /**
   * 返利条件
   */
  @Column(name = "sale_rebate_policy_condition" ,  nullable = false, columnDefinition = "VARCHAR(500) COMMENT '返利条件'")
  @ApiModelProperty("返利条件")
  private String saleRebatePolicyCondition;

  /**
   * 返利条件（展示用）
   */
  @Column(name = "sale_rebate_policy_condition_name" ,  nullable = false, columnDefinition = "VARCHAR(500) COMMENT '返利条件（展示用）'")
  @ApiModelProperty("返利条件（展示用）")
  private String saleRebatePolicyConditionName;

  /**
   * 返利公式
   */
  @Column(name = "sale_rebate_policy_formula" ,  nullable = false, columnDefinition = "VARCHAR(500) COMMENT '返利公式'")
  @ApiModelProperty("返利公式")
  private String saleRebatePolicyFormula;

  /**
   * 返利公式（展示用）
   */
  @Column(name = "sale_rebate_policy_formula_name" ,  nullable = false, columnDefinition = "VARCHAR(500) COMMENT '返利公式（展示用）'")
  @ApiModelProperty("返利公式（展示用）")
  private String saleRebatePolicyFormulaName;

  /**
   * 公式顺序（计算策略和展示用）
   */
  @Column(name = "formula_sort"  , columnDefinition = "INT COMMENT '公式顺序（计算策略和展示用）'")
  @ApiModelProperty("公式顺序（计算策略和展示用）")
  private Integer formulaSort;

  /**
   * @see com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum
   * 公式类型
   */
  @Column(name = "formula_type" ,  nullable = false, columnDefinition = "VARCHAR(32) COMMENT '公式类型'")
  @ApiModelProperty("公式类型,预提:withholding,返利:rebate")
  private String formulaType;
  
  /**
   * 公式配置编码
   */
  @Column(name = "formula_config_code" ,  nullable = false, columnDefinition = "VARCHAR(32) COMMENT '公式配置编码'")
  @ApiModelProperty("公式配置编码")
  private String formulaConfigCode;
}
