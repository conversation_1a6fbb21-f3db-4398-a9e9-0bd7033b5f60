package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.biz.crm.tpm.business.rebate.local.entity.FormulaConfigEntity;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaEntity;
import com.biz.crm.tpm.business.rebate.local.entity.TpmRebatePolicyRelaFormulaConfig;
import com.biz.crm.tpm.business.rebate.local.repository.DmsFormulaConfigRepository;
import com.biz.crm.tpm.business.rebate.local.repository.DmsFormulaRepository;
import com.biz.crm.tpm.business.rebate.local.repository.DmsRebatePolicyRelaFormulaConfigRepository;
import com.biz.crm.tpm.business.rebate.local.service.RebatePolicyRelaFormulaConfigService;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaSaleRebatePolicyElementVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 返利政策关联公式配置
 * <AUTHOR>
 * @Date 2024/6/12 11:46
 */
@Service
public class RebatePolicyRelaFormulaConfigServiceImpl implements RebatePolicyRelaFormulaConfigService {

    @Autowired(required = false)
    private DmsRebatePolicyRelaFormulaConfigRepository relaConfigRepository;
    @Autowired(required = false)
    private DmsFormulaConfigRepository configRepository;
    @Autowired(required = false)
    private DmsFormulaRepository dmsFormulaRepository;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public Map<String, FormulaConfigVo> saveBatch(List<TpmRebatePolicyRelaFormulaConfig> configList) {
        this.validate(configList);
        List<TpmRebatePolicyRelaFormulaConfig> exists =
                this.relaConfigRepository.findByUniqueKeys(configList.stream().map(TpmRebatePolicyRelaFormulaConfig::getUniqueKey).collect(Collectors.toList()));
        Validate.isTrue(CollectionUtils.isEmpty(exists), "返利政策关联公式配置已存在,请检查");
        this.relaConfigRepository.saveBatch(configList);
        return this.findFormulaConfigMapBySaleRebatePolicyCode(configList.get(0).getSaleRebatePolicyCode());
    }
    
    @Override
    public Map<String, FormulaConfigVo> findFormulaConfigMapBySaleRebatePolicyCode(String saleRebatePolicyCode) {
        if (StringUtils.isBlank(saleRebatePolicyCode)) {
            return Maps.newHashMap();
        }
        List<TpmRebatePolicyRelaFormulaConfig> relaFormulaConfigs =
                this.relaConfigRepository.findByRebatePolicyCode(saleRebatePolicyCode);
        Map<String, String> relaMap = relaFormulaConfigs.stream().collect(Collectors.toMap(TpmRebatePolicyRelaFormulaConfig::getFormulaConfigCode, TpmRebatePolicyRelaFormulaConfig::getFormulaType));
        List<String> formulaConfigCodes =
                relaFormulaConfigs.stream().map(TpmRebatePolicyRelaFormulaConfig::getFormulaConfigCode).collect(Collectors.toList());
        List<FormulaConfigEntity> configEntities =
                this.configRepository.findByFormulaConfigCodes(formulaConfigCodes);
        if (CollectionUtils.isEmpty(configEntities)) {
            return Maps.newHashMap();
        }
        List<FormulaConfigVo> dmsFormulaConfigVoList =
                (List<FormulaConfigVo>) this.nebulaToolkitService.copyCollectionByWhiteList(configEntities,
                        FormulaConfigEntity.class, FormulaConfigVo.class, HashSet.class, ArrayList.class);
        List<FormulaEntity> formulaEntities = this.dmsFormulaRepository.findByFormulaConfigCodes(formulaConfigCodes);
        List<FormulaSaleRebatePolicyElementVo> formulaVoList =
                (List<FormulaSaleRebatePolicyElementVo>) this.nebulaToolkitService.copyCollectionByBlankList(formulaEntities, FormulaEntity.class, FormulaSaleRebatePolicyElementVo.class, HashSet.class, ArrayList.class);
        Map<String, List<FormulaSaleRebatePolicyElementVo>> formulaVoGroup = formulaVoList.stream().collect(Collectors.groupingBy(FormulaSaleRebatePolicyElementVo::getFormulaConfigCode));
        Map<String, FormulaConfigVo> map = Maps.newHashMap();
        dmsFormulaConfigVoList.forEach(v -> {
            v.setFormulaList(formulaVoGroup.get(v.getFormulaConfigCode()));
            map.put(relaMap.get(v.getFormulaConfigCode()), v);
        });
        
        return map;
    }

    @Override
    public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {

        if (StringUtils.isBlank(saleRebatePolicyCode)) {
            return;
        }
        this.relaConfigRepository.deleteBySaleRebatePolicyCode(saleRebatePolicyCode);
    }

    private void validate(List<TpmRebatePolicyRelaFormulaConfig> configList) {
        Validate.notEmpty(configList, "公式配置列表不能为空");
        configList.forEach(v -> {
            Validate.notBlank(v.getSaleRebatePolicyCode(), "返利政策关联公式配置保存时,返利政策编码不能为空");
            Validate.notBlank(v.getFormulaConfigCode(), "返利政策关联公式配置保存时,公式配置编码不能为空");
            Validate.notBlank(v.getFormulaType(), "返利政策关联公式配置保存时,公式类型不能为空");
            v.setUniqueKey(DigestUtils.md5DigestAsHex(StringUtils.join(v.getSaleRebatePolicyCode(),
                    v.getFormulaConfigCode(), v.getFormulaType()).getBytes(StandardCharsets.UTF_8)));
        });
    }
}
