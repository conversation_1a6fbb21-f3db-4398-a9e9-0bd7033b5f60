package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.tpm.business.rebate.local.enums.FormulaVariableEnum;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @description: 返利政策基准注册（月度销售金额）
 * @author: lifei
 * @date: 2022/2/28 10:54
 */
@Service
@Slf4j
public class SaleRebateMonthlySalesAmountRegister implements SaleRebatePolicyCriterionRegister {

    @Resource
    private FormulaGetValueComponent formulaGetValueComponent;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public String getSaleRebatePolicyCriterionCode() {
        return FormulaVariableEnum.YDXSJE.getCode();
    }

    @Override
    public String getSaleRebatePolicyCriterionName() {
        return FormulaVariableEnum.YDXSJE.getName();
    }

    @Override
    public Integer getCriterionSort() {
        return FormulaVariableEnum.YDXSJE.getSort();
    }


    @Override
    public Map<String, BigDecimal> getAmountMapByCondition(FormulaCalBaseVo vo, String calType) {
        Map<String, BigDecimal> map = Maps.newHashMap();
        String key = getSaleRebatePolicyCriterionCode() + vo.getCustomerCode();
        BigDecimal amount = BigDecimal.ZERO;
        SalesPlanQueryVo queryVo = JSONObject.parseObject(JSONObject.toJSONString(vo), SalesPlanQueryVo.class);

        Map<String, String> dateMap = FormulaGetValueComponent.getDateMonthly(vo.getStartDate(), DateUtil.DEFAULT_YEAR_MONTH_DAY);
        String startDate = dateMap.get(FormulaGetValueComponent.START_DATE);
        String endDate = dateMap.get(FormulaGetValueComponent.END_DATE);
        queryVo.setStartDate(startDate);
        queryVo.setEndDate(endDate);

        if (FormulaTypeEnum.MARKETING.getCode().equals(calType)) {
            amount = this.formulaGetValueComponent.salesPlanCalAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
        } else if (FormulaTypeEnum.WITHHOLDING.getCode().equals(calType)) {
            amount = this.formulaGetValueComponent.signForAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
            log.error("月度销售金额计算方案明细编码:{},计算金额:{}", vo.getActDetailCode(), amount);
            //计算拆分占比
            BigDecimal rate = formulaGetValueComponent.splittingRate(vo.getCustomerCode(), vo.getYears(), vo.getOrgCode(),
                    FormulaGetValueComponent.AMOUNT);
            if (rate != null) {
                amount = amount.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            log.error("月度销售金额计算方案明细编码:{},比例:{},第二次计算金额:{}", vo.getActDetailCode(), rate, amount);
        } else if (FormulaTypeEnum.MANAGEMENT_REPORT.getCode().equals(calType)) {
            amount = this.formulaGetValueComponent.deliveryAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
            //计算拆分占比
            BigDecimal rate = formulaGetValueComponent.splittingRate(vo.getCustomerCode(), vo.getYears(), vo.getOrgCode(),
                    FormulaGetValueComponent.AMOUNT);
            if (rate != null) {
                amount = amount.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
        } else if (FormulaTypeEnum.END_CASE.getCode().equals(calType)) {
            amount = this.formulaGetValueComponent.signForAmountOrQuantity(queryVo, FormulaGetValueComponent.AMOUNT);
            log.error("月度销售金额计算方案明细编码:{},计算金额:{}", vo.getActDetailCode(), amount);
            //计算拆分占比
            BigDecimal rate = formulaGetValueComponent.splittingRate(vo.getCustomerCode(), vo.getYears(), vo.getOrgCode(),
                    FormulaGetValueComponent.AMOUNT);
            if (rate != null) {
                amount = amount.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            log.error("月度销售金额计算方案明细编码:{},比例:{},第二次计算金额:{}", vo.getActDetailCode(), rate, amount);
        }
        map.put(key, amount);
        return map;
    }


}
