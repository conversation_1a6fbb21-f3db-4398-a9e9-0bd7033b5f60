package com.biz.crm.tpm.business.rebate.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaConfigEntity;
import com.biz.crm.tpm.business.rebate.local.service.FormulaConfigService;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 返利公式
 * <AUTHOR>
 * @Date 2024/6/11 17:25
 */
@RestController
@RequestMapping("/v1/saleRebateFormulaConfig/saleRebateFormulaConfig")
@Slf4j
@Api(tags = "返利公式配置：formula：返利公式配置")
public class TpmFormulaConfigController {

    @Autowired(required = false)
    private FormulaConfigService formulaConfigService;

    /**
     * 分页条件查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    @ApiOperation(value = "分页条件查询")
    @GetMapping("findByConditions")
    public Result<Page<FormulaConfigEntity>> findByConditions(@PageableDefault(50) Pageable pageable, FormulaConfigEntity dto) {
        return Result.ok(this.formulaConfigService.findByConditions(pageable, dto));
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "ͨ查询详情")
    @GetMapping("{id}")
    public Result<FormulaConfigVo> findById(@PathVariable @ApiParam(name = "id", value = "主键") String id) {
        return Result.ok(this.formulaConfigService.findById(id, null));
    }

    /**
     * 创建
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "创建")
    @PostMapping
    public Result<?> create(@ApiParam(name = "dto", value = "促销政策") @RequestBody FormulaConfigEntity dto) {
        this.formulaConfigService.create(dto);
        return Result.ok("创建成功");
    }

    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改")
    @PatchMapping
    public Result<?> update(@ApiParam(name = "dto", value = "促销政策") @RequestBody FormulaConfigEntity dto) {
        this.formulaConfigService.update(dto);
        return Result.ok("修改成功");
    }



    /**
     * 启用
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "启用", httpMethod = "PATCH")
    @PatchMapping("/enable")
    public Result<?> enable(@RequestBody List<String> ids) {
        this.formulaConfigService.enableBatch(ids);
        return Result.ok("启用成功");
    }

}
