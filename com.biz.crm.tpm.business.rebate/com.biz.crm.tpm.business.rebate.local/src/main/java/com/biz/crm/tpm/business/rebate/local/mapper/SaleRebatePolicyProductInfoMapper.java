package com.biz.crm.tpm.business.rebate.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyProductInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 返利政策分配商品(SaleRebatePolicyProductInfo)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:58
 */
public interface SaleRebatePolicyProductInfoMapper extends BaseMapper<SaleRebatePolicyProductInfo> {

  /**
   * 分页查询
   *
   * @param page                        分页
   * @param saleRebatePolicyProductInfo
   * @return
   */
  public Page<SaleRebatePolicyProductInfo> findByConditions(
      @Param("page") Page<SaleRebatePolicyProductInfo> page,
      @Param("saleRebatePolicyProductInfo") SaleRebatePolicyProductInfo saleRebatePolicyProductInfo);
}

