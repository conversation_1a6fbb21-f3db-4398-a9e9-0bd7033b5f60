package com.biz.crm.tpm.business.rebate.local.controller;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.rebate.sdk.service.CriterionVoService;
import com.biz.crm.tpm.business.rebate.sdk.vo.dynamic.AbstractCriterionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 返利政策变量（基准）
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
@RestController
@RequestMapping("/v1/rebate/criterion")
@Slf4j
@Api(tags = "返利模块:CriterionVo:返利政策变量（基准）")
public class CriterionVoController {

  @Autowired(required = false)
  private CriterionVoService criterionVoService;

  /**
   * 根据政策编码和变量编码查询和返利变量配套的动态表单的内容信息
   *
   * @param saleRebatePolicyCode          返利政策code
   * @param saleRebatePolicyCriterionCode 返利政策变量code
   * @return {@link Result}<{@link AbstractCriterionVo}>
   */
  @ApiOperation(value = "根据政策编码和变量编码查询和返利变量配套的动态表单的内容信息(单条)")
  @GetMapping("findByPolicyCodeAndCriterionCode")
  public Result<AbstractCriterionVo> findByPolicyCodeAndCriterionCode(
      @ApiParam(name = "saleRebatePolicyCode", value = "返利政策code", required = true) @RequestParam String saleRebatePolicyCode,
      @ApiParam(name = "saleRebatePolicyCriterionCode", value = "返利政策变量实例code", required = true) @RequestParam String saleRebatePolicyCriterionCode) {
    AbstractCriterionVo vo = criterionVoService.findByPolicyCodeAndCriterionCode(saleRebatePolicyCode, saleRebatePolicyCriterionCode);
    return Result.ok(vo);
  }
  /**
   * 根据政策编码查询和返利变量配套的动态表单的内容信息
   *
   * @param saleRebatePolicyCode          返利政策code
   * @param saleRebatePolicyCriterionCodes 动态表单编码，即前缀，不是实例编码
   * @return {@link Result}<{@link AbstractCriterionVo}>
   */
  @ApiOperation(value = "根据政策编码和变量编码查询和返利变量配套的动态表单的内容信息(单条)")
  @GetMapping("findMapByPolicyCodeAndCriterionCode")
  public Result< Map<String, List<AbstractCriterionVo>>> findMapByPolicyCodeAndCriterionCode(
      @ApiParam(name = "saleRebatePolicyCode", value = "返利政策code", required = true) @RequestParam String saleRebatePolicyCode,
      @ApiParam(name = "saleRebatePolicyCriterionCode", value = "动态表单编码", required = true) @RequestParam List<String> saleRebatePolicyCriterionCodes) {
    Map<String, List<AbstractCriterionVo>> vo = criterionVoService.findMapByPolicyCodeAndCriterionCode(saleRebatePolicyCode,saleRebatePolicyCriterionCodes);
    return Result.ok(vo);
  }
}
