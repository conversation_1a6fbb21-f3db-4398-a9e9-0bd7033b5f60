package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/28 17:22
 */
@Getter
@Setter
@TableName("tpm_formula_calculation_log")
@Entity
@Table(name = "tpm_formula_calculation_log", indexes = {
        @Index(name = "tpm_formula_calculation_log_index1",columnList = "business_detail_code,cal_type")
})
@org.hibernate.annotations.Table(appliesTo = "tpm_formula_calculation_log", comment = "公式计算日志")
public class FormulaCalculationLog extends UuidFlagOpEntity {

    @Column(name = "formula_code", columnDefinition = "varchar(32) COMMENT '公式编码'")
    private String formulaCode;

    @Column(name = "formula_name", columnDefinition = "varchar(256) COMMENT '公式名称'")
    private String formulaName;

    @Column(name = "customer_code", columnDefinition = "varchar(32) COMMENT '客户编码'")
    private String customerCode;

    @Column(name = "customer_name", columnDefinition = "varchar(256) COMMENT '客户名称'")
    private String customerName;

    @Column(name = "business_detail_code", columnDefinition = "varchar(200) COMMENT '业务明细编码'")
    private String businessDetailCode;

    @Column(name = "business_code", columnDefinition = "varchar(32) COMMENT '业务编码'")
    private String businessCode;

    @Column(name = "cal_type", columnDefinition = "varchar(32) COMMENT '计算类型'")
    private String calType;

    @Column(name = "cal_condition", columnDefinition = "varchar(256) COMMENT '条件'")
    private String calCondition;

    @Column(name = "cal_result", columnDefinition = "varchar(256) COMMENT '结果'")
    private String calResult;

    @Column(name = "actual_condition_amount", columnDefinition = "decimal(22,8) COMMENT '实际条件金额'")
    private BigDecimal actualConditionAmount;

    @Column(name = "actual_result_amount", columnDefinition = "decimal(22,8) COMMENT '实际结果金额'")
    private BigDecimal actualResultAmount;

    @Column(name = "result_amount", columnDefinition = "decimal(22,8) COMMENT '结果金额'")
    private BigDecimal resultAmount;


}
