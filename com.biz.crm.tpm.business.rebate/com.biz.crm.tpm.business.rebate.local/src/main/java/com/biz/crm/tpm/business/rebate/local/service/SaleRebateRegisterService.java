package com.biz.crm.tpm.business.rebate.local.service;

import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyRegisterVo;
import java.util.Collection;
import java.util.List;

/**
 * @description: 返利周期 service
 * @author: lifei
 * @date: 2022/2/21 10:44
 */
public interface SaleRebateRegisterService {

  /**
   * 查询集合（周期）
   *
   * @return
   */
  List<SaleRebatePolicyRegisterVo> findForRebateCycle();

  /**
   * 查询集合（模板）
   *
   * @return
   */
  List<SaleRebatePolicyRegisterVo> findForRebateType();


  /**
   * 查询集合（基准）
   *
   * @return
   */
  List<SaleRebatePolicyRegisterVo> findForRebateCriterion();

  /**
   *
   * 要素
   * @param saleRebateType
   * <AUTHOR>
   * @date
   */
  List<SaleRebatePolicyRegisterVo> findForRebateElement(String saleRebateType);

  /**
   *
   * 客户选择范围
   * @param saleRebateType
   * <AUTHOR>
   * @date
   */
  List<SaleRebatePolicyRegisterVo> findForRebateScope(String saleRebateType);

  /**
   *
   * 拿到要素注册器
   * @param saleRebateType
   * <AUTHOR>
   * @date
   */
  Collection<Class<? extends SaleRebatePolicyElementRegister>> findTemplateCollection(String saleRebateType);
}
