package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicy;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyVoService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateRegisterService;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyElementDataVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @describe: 返利政策VO
 * @createTime 2022年02月23日 10:17:00
 */
@Service
@Slf4j
public class SaleRebatePolicyVoServiceImpl implements SaleRebatePolicyVoService {

  @Autowired(required = false)
  private SaleRebatePolicyService saleRebatePolicyService;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private ApplicationContext applicationContext;
  @Autowired(required = false)
  private SaleRebateRegisterService saleRebateRegisterService;

  /**
   * 通过ID查询明细
   *
   * @param id
   * @return
   */
  @Override
  public SaleRebatePolicyVo findDetailById(String id) {
    SaleRebatePolicy saleRebatePolicy = this.saleRebatePolicyService.findById(id);
    String saleRebateType = saleRebatePolicy.getSaleRebateType();
    String saleRebatePolicyCode = saleRebatePolicy.getSaleRebatePolicyCode();
    //copy
    SaleRebatePolicyVo saleRebatePolicyVo = this.nebulaToolkitService
        .copyObjectByBlankList(saleRebatePolicy, SaleRebatePolicyVo.class, HashSet.class, ArrayList.class);
    Map<String, JSONObject> elementDataMap = new HashMap<>();
    //模板校验 
    Collection<Class<? extends SaleRebatePolicyElementRegister>> classes = this.saleRebateRegisterService
        .findTemplateCollection(saleRebateType);
    //查询要素信息并组装.
    for (Class<? extends SaleRebatePolicyElementRegister> aClass : classes) {
      SaleRebatePolicyElementRegister saleRebatePolicyElementRegister = applicationContext
          .getBean(aClass);
      SaleRebatePolicyElementDataVo vo = saleRebatePolicyElementRegister.getBySaleRebatePolicyCode(saleRebatePolicyCode);
      JSONObject jsonObject = JsonUtils.toJSONObject(vo);
      elementDataMap.put(saleRebatePolicyElementRegister.getSaleRebatePolicyElementCode(), jsonObject);
    }
    saleRebatePolicyVo.setElementDataMap(elementDataMap);
    //TODO  可配变量

    return saleRebatePolicyVo;

  }

  @Override
  public SaleRebatePolicyVo findDetailByCode(String saleRebatePolicyCode) {
    SaleRebatePolicy saleRebatePolicy = this.saleRebatePolicyService.findBySaleRebatePolicyCode(saleRebatePolicyCode);
    String saleRebateType = saleRebatePolicy.getSaleRebateType();
    //copy
    SaleRebatePolicyVo saleRebatePolicyVo = this.nebulaToolkitService
        .copyObjectByBlankList(saleRebatePolicy, SaleRebatePolicyVo.class, HashSet.class, ArrayList.class);
    Map<String, JSONObject> elementDataMap = new HashMap<>();
    //模板校验
    Collection<Class<? extends SaleRebatePolicyElementRegister>> classes = this.saleRebateRegisterService
        .findTemplateCollection(saleRebateType);
    //查询要素信息并组装.
    for (Class<? extends SaleRebatePolicyElementRegister> aClass : classes) {
      SaleRebatePolicyElementRegister saleRebatePolicyElementRegister = applicationContext
          .getBean(aClass);
      SaleRebatePolicyElementDataVo vo = saleRebatePolicyElementRegister.getBySaleRebatePolicyCode(saleRebatePolicyCode);
      JSONObject jsonObject = JsonUtils.toJSONObject(vo);
      elementDataMap.put(saleRebatePolicyElementRegister.getSaleRebatePolicyElementCode(), jsonObject);
    }
    saleRebatePolicyVo.setElementDataMap(elementDataMap);
    return saleRebatePolicyVo;
  }

  @Override
  public List<SaleRebatePolicyVo> findByCodes(List<String> saleRebatePolicyCodes) {
    List<SaleRebatePolicy> saleRebatePolicys = this.saleRebatePolicyService.findBySaleRebatePolicyCodes(saleRebatePolicyCodes);
    if(CollectionUtils.isEmpty(saleRebatePolicys)){
      return null;
    }
   return  (List<SaleRebatePolicyVo>)
        this.nebulaToolkitService.copyCollectionByWhiteList(
            saleRebatePolicys,
            SaleRebatePolicy.class,
            SaleRebatePolicyVo.class,
            HashSet.class,
            ArrayList.class);
  }


}
