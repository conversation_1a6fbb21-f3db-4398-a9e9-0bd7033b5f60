package com.biz.crm.tpm.business.rebate.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyFormulaInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 返利政策公式(SaleRebatePolicyFormulaInfo)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
public interface SaleRebatePolicyFormulaInfoMapper extends BaseMapper<SaleRebatePolicyFormulaInfo> {

  /**
   * 分页查询
   *
   * @param page                        分页
   * @param saleRebatePolicyFormulaInfo
   * @return
   */
  public Page<SaleRebatePolicyFormulaInfo> findByConditions(
      @Param("page") Page<SaleRebatePolicyFormulaInfo> page,
      @Param("saleRebatePolicyFormulaInfo") SaleRebatePolicyFormulaInfo saleRebatePolicyFormulaInfo);
}

