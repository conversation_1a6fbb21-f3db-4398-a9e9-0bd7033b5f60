package com.biz.crm.tpm.business.rebate.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaEntity;
import org.apache.ibatis.annotations.Param;

/**
 * @Description 返利公式
 * <AUTHOR>
 * @Date 2024/6/11 17:22
 */
public interface TpmFormulaMapper extends BaseMapper<FormulaEntity> {

    /**
     * 根据条件分页查询
     * @param page
     * @param dto
     * @return
     */
    Page<FormulaEntity> findByConditions(@Param("page") Page<FormulaEntity> page,
                                         @Param("dto") FormulaEntity dto);
}
