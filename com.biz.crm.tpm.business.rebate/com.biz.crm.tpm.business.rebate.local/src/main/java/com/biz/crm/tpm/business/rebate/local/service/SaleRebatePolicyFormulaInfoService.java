package com.biz.crm.tpm.business.rebate.local.service;


import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyFormulaInfo;

import java.util.List;

/**
 * 返利政策公式(SaleRebatePolicyFormulaInfo)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
public interface SaleRebatePolicyFormulaInfoService {

  /**
   * 批量删除
   *
   * @param idList
   * @return
   */
  void delete(List<String> idList);

  /**
   * 返利编码集合删除
   *
   * @param saleRebatePolicyCode id集合
   * @return Result
   */
  void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode);

  /**
   * 根据返利政策编码查询
   *
   * @param saleRebatePolicyCode
   * @return
   */
  List<SaleRebatePolicyFormulaInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode);

  /**
   * 根据返利政策编码查询
   *
   * @param saleRebatePolicyCodes
   * @return
   */
  List<SaleRebatePolicyFormulaInfo> findBySaleRebatePolicyCodes(List<String> saleRebatePolicyCodes);

  /**
   * 批量新增
   *
   * @param list
   */
  void createBatch(List<SaleRebatePolicyFormulaInfo> list);


  /**
   * 根据id集合查询公式
   *
   * @param ids
   * @return
   */
  List<SaleRebatePolicyFormulaInfo> findByIds(List<String> ids);
}

