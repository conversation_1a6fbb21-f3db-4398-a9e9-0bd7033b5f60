package com.biz.crm.tpm.business.rebate.local.service.register.calculatetyperegister;

import com.biz.crm.tpm.business.rebate.local.constant.SaleRebateCalculateTypeConstant;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyCalculateService;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebateCalculateTypeRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateComputeParamVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.bizunited.nebula.common.util.JsonUtils;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @title SaleRebateFirstHitRegister
 * @date 2023/6/19 14:38
 * @description 按先后顺序命中返利计算类型注册实现 (返利条件按顺序首次命中则终止匹配)
 */
@Service
public class SaleRebateFirstHitRegister  implements SaleRebateCalculateTypeRegister {

  @Autowired(required = false)
  private SaleRebatePolicyCalculateService saleRebatePolicyCalculateService;

  //返利计算类型名称
  private static final String CALCULATE_TYPE_REGISTER_NAME = "按先后顺序命中返利";

  private static final Integer SORT_CODE = 2;

  @Override
  public String getCalculateTypeRegisterCode() {
    return SaleRebateCalculateTypeConstant.FIRST_HIT_REGISTER_CODE;
  }

  @Override
  public String getCalculateTypeRegisterName() {
    return CALCULATE_TYPE_REGISTER_NAME;
  }

  @Override
  public Integer getCalculateTypeRegisterSortCode() {
    return SORT_CODE;
  }

  @Override
  @Transactional
  public void handleCalculate(List<SaleRebateComputeParamVo> saleRebateComputeParamVos) {
    saleRebateComputeParamVos.forEach(saleRebateComputeParamVo -> {
      CustomerVo customerVo = JsonUtils
          .json2Obj(saleRebateComputeParamVo.getCusJson().toJSONString(), CustomerVo.class);
      saleRebateComputeParamVo.setCustomerCode(customerVo.getCustomerCode());
      Integer formulaSort = saleRebateComputeParamVo.getSaleRebatePolicyFormulaInfoVo()
          .getFormulaSort();
      saleRebateComputeParamVo.setFormulaSort(formulaSort);
    });
    //保存
    Map<String, List<SaleRebateComputeParamVo>> saleRebateComputeParamMap = saleRebateComputeParamVos.stream()
        .collect(Collectors.groupingBy(SaleRebateComputeParamVo::getCustomerCode));
    for (String cusCode : saleRebateComputeParamMap.keySet()) {
      List<SaleRebateComputeParamVo> saleRebateComputeParamVosList = saleRebateComputeParamMap.get(
          cusCode);
      List<SaleRebateComputeParamVo> itemList = saleRebateComputeParamVosList.stream().filter(
          saleRebateComputeParamVo -> ObjectUtils.isNotEmpty(
              saleRebateComputeParamVo.getFormulaSort())).collect(
          Collectors.toList());
      if (CollectionUtils.isNotEmpty(itemList) && !saleRebateComputeParamVosList.get(0)
          .getCalculateType().equals(SaleRebateCalculateTypeConstant.RANDOM_HIT_REGISTER_CODE)) {
        List<SaleRebateComputeParamVo> results = saleRebateComputeParamVosList.stream()
            .sorted(Comparator.comparingInt(SaleRebateComputeParamVo::getFormulaSort)).collect(
                Collectors.toList());
        this.saleRebatePolicyCalculateService.onCalculate(results);
      }
    }
  }
}
