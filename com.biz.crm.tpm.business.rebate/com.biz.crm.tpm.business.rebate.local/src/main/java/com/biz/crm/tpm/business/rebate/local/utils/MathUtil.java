package com.biz.crm.tpm.business.rebate.local.utils;

import com.greenpineyu.fel.FelEngine;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: 返利公式计算工具类
 * @author: lifei
 * @date: 2022/3/9 17:53
 */
public class MathUtil {

    //表达式中的整数转化为小数
    private static final Pattern reg = Pattern.compile("-?\\d+(\\.\\d+)?");
    //提取字符串中表达式
    private static final Pattern pattern = Pattern.compile("[0-9]*[a-zA-Z]{1,}[0-9]*");
    /**
     * 防止运算符重复拼接
     */
    private static final Pattern PREVENT_REPETITION = Pattern.compile("\\+{2,}|/{2,}|-{2,}|\\*{2,}");


    /**
     * 解析数学公式 支持数学符号包括：+、-、*、/、>、<、>=、<=、&&、()、%、INT、||、==等 为了使除法计算准确，表达式中的正数替换成小数 利用{@link
     * FelEngine}解析并计算公式
     *
     * @param expressionValue 公式字符串
     * @return
     */
    public static BigDecimal computeFormula(String expressionValue) {
        //特殊处理空格 and
        String expressionStr = expressionValue.replace(" ", "").replace("and", "&&");
        Validate.notEmpty(expressionStr, "根据公式计算时，公式字符串不能为空");
        //为了使除法计算准确，表达式中的正数替换成小数
        expressionStr = formatFloat(expressionStr);
        try {
            //特殊四则符号校验
            validateSymbol(expressionStr);

            FelEngine felEngine = new FelEngineImpl();
            return calcuConResultVal(felEngine, expressionStr);
        } catch (Exception e) {
            throw new IllegalArgumentException("数学表达式解析异常，请确认表达式是否正确：".concat(expressionValue), e);
        }
    }

    /**
     * 特殊四则符号校验 -
     *
     * @param expressionStr 表达str
     */
    private static void validateSymbol(String expressionStr) {
        Matcher matcher = PREVENT_REPETITION.matcher(expressionStr);
        if (matcher.find()) {
            throw new IllegalArgumentException();
        }
    }

    /**
     * 解析数学公式 支持数学符号包括：+、-、*、/、>、<、>=、<=、&&、()、%、INT、||、==等 为了使除法计算准确，表达式中的正数替换成小数 利用{@link
     * FelEngine}解析并计算公式
     *
     * @param expressionValue 公式字符串
     * @return
     */
    public static Boolean computeCondition(String expressionValue) {
        //特殊处理空格 and
        String expressionStr = expressionValue.replace(" ", "").replace("and", "&&");
        Validate.notEmpty(expressionStr, "根据公式计算时，公式字符串不能为空");
        //为了使除法计算准确，表达式中的正数替换成小数
        expressionStr = formatFloat(expressionStr);
        try {
            FelEngine felEngine = new FelEngineImpl();
            return calconditionVal(felEngine, expressionStr);
        } catch (Exception e) {
            throw new IllegalArgumentException("数学表达式解析异常，请确认表达式是否正确：".concat(expressionValue), e);
        }
    }

    /**
     * 取整计算 并计算
     */
    private static BigDecimal calcuConResultVal(FelEngine fel, String cacuStr) {
        BigDecimal rlt = BigDecimal.ZERO;
        Object result;
        cacuStr = cacuStr.replace("INT", "$('Math').floor");//替换取整字符
        cacuStr = cacuStr.replace("-", "-1*");
        result = fel.eval(cacuStr);
        if (result != null) {
            if (!Double.isNaN(Double.parseDouble(result.toString()))) {
                rlt = new BigDecimal(result.toString());
            }
            rlt = rlt.setScale(2, BigDecimal.ROUND_HALF_UP);
            return rlt;
        } else {
            return null;
        }
    }


    /**
     * 取整计算 并计算
     */
    private static Boolean calconditionVal(FelEngine fel, String cacuStr) {
        Object result;
        cacuStr = cacuStr.replace("INT", "$('Math').floor");//替换取整字符
        cacuStr = cacuStr.replace("-", "-1*");
        result = fel.eval(cacuStr);
        if (result instanceof Boolean) {
            return (boolean) result;
        }
        return null;
    }

    /**
     * 表达式中的整数转化为小数
     *
     * @param expressionStr
     * @return
     */
    private static String formatFloat(String expressionStr) {
        StringBuilder builder = new StringBuilder(expressionStr);
        if (StringUtils.isBlank(expressionStr)) {
            return new String();
        }
        Matcher matcher = reg.matcher(expressionStr);
        int index = 0;
        while (matcher.find()) {
            String num = matcher.group();
            int start = matcher.start() + index;
            int end = matcher.end() + index;
            if (!num.contains(".")) {
                builder.replace(start, end, num.concat(".0"));
                index += 2;
            }
        }
        return builder.toString();
    }


    /**
     * 去掉公式符号
     *
     * @param formula
     * <AUTHOR>
     * @date
     */
    public static Set<String> getFormulaReplace(String formula) {
        Matcher matcher = pattern.matcher(formula);
        Set<String> variables = new HashSet<>();
        while (matcher.find()) {
            String group = matcher.group();
            if (!group.equals("INT") && !group.equals("int") && !group.equals("and") && !group.equals(
                    "MAX") && !group.equals("MIN")) {
                variables.add(group);
            }
        }
        return variables;
    }

    /**
     * 验证默认公式
     *
     * @param expressStr 表达式字符串
     * @return
     */
    public static String replaceDefaultVariable(String expressStr) {
        Validate.notEmpty(expressStr, "替换公式变量时，公式字符串不能为空");
        Matcher matcher = pattern.matcher(expressStr);
        Set<String> variables = new HashSet<>();
        while (matcher.find()) {
            variables.add(matcher.group());
        }
        String expressValue = expressStr;
        for (String entry : variables) {
            //替换变量实际值
            if ("and".equals(entry)) {
                expressValue = expressValue.replace(entry, "&&");
            } else if ("INT".equals(entry)) {

            } else if ("MAX".equals(entry)) {

            } else if ("MIN".equals(entry)) {

            } else {
                expressValue = expressValue.replace(entry, String.valueOf(BigDecimal.ONE));
            }
        }
        return expressValue;
    }

}
