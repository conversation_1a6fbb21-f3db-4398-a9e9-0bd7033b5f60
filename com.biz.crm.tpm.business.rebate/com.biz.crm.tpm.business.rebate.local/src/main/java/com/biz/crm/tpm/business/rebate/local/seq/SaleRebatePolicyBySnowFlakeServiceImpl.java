//package com.biz.crm.tpm.business.rebate.local.seq;
//
//import com.biz.crm.common.sequese.sdk.generator.service.CrmBizSequenceServiceByLong;
//import com.biz.crm.common.sequese.sdk.generator.service.aigorithm.CrmSequeseGeneratorBySnowFlake;
//import lombok.ToString;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @date 2023-06-09 16:17
// * @description：
// */
//@ToString
//@Slf4j
//@Component
//@Order(1)
//public class SaleRebatePolicyBySnowFlakeServiceImpl implements CrmBizSequenceServiceByLong<SaleRebatePolicyBySnowFlakeDto, CrmSequeseGeneratorBySnowFlake> {
//
//  /**
//   * 编码生成规则
//   */
//  private static final String SEQ_FORMAT_TEMP = "FLZC%s";
//
//  /**
//   * 当前业务模块名称: crm-mdm
//   */
//  @Value("${spring.application.name}")
//  private String subSystem;
//
//  /**
//   * 注入使用的算法
//   */
//  @Autowired
//  private CrmSequeseGeneratorBySnowFlake generator;
//  /**
//   * 策略模式：判断当前对象是否可以处理
//   *
//   * @param obj Object对象，用于判断该对象是否可以被当前实现
//   * @return
//   */
//  @Override
//  public boolean match(Object obj) {
//    return obj instanceof SaleRebatePolicyBySnowFlakeDto;
//  }
//
//
//  /**
//   * 扩展点：对序列值进行格式化
//   *
//   * @param dto
//   * @param sequese
//   * @return
//   */
//  @Override
//  public String generatorFormat(SaleRebatePolicyBySnowFlakeDto dto, Long sequese) {
//    // 进行格式化组装序列字符串
//    return String.format(SEQ_FORMAT_TEMP, sequese);
//  }
//
//  /**
//   * 扩展点：获取当前使用的算法
//   *
//   */
//  @Override
//  public CrmSequeseGeneratorBySnowFlake getGenerator() {
//    return generator;
//  }
//
//  /**
//   * 扩展点：获取当前类的日志对象
//   */
//  @Override
//  public Logger getLogger() {
//    return log;
//  }
//
//  /**
//   * 扩展点： 获取当天工作子节点，<br/>
//   * 例如 @Value("${spring.application.name:}")
//   */
//  @Override
//  public String getSubSystem() {
//    return subSystem;
//  }
//
//  /**
//   * 序列信息：获取序列名称,用于判断是否是当前策略
//   */
//  @Override
//  public String getSeqInfoByBizCode() {
//    return "DMS_SALE_REBATE_POLICY_SEQ";
//  }
//
//  /**
//   * 序列信息：获取字段描述
//   */
//  @Override
//  public String getSeqInfoByColumnDesc() {
//    return "返利政策编码规则";
//  }
//
//  /**
//   * 序列信息：获取模块名称
//   */
//  @Override
//  public String getSeqInfoByModleName() {
//    return "返利政策";
//  }
//
//}
