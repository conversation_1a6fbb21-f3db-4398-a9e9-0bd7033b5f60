package com.biz.crm.tpm.business.rebate.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.rebate.local.entity.TpmRebatePolicyRelaFormulaConfig;
import com.biz.crm.tpm.business.rebate.local.mapper.TpmRebatePolicyRelaFormulaConfigMapper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description 返利政策关联公式配置
 * <AUTHOR>
 * @Date 2024/6/12 11:44
 */
@Component
public class DmsRebatePolicyRelaFormulaConfigRepository extends ServiceImpl<TpmRebatePolicyRelaFormulaConfigMapper, TpmRebatePolicyRelaFormulaConfig> {

    /**
     * 根据唯一编码列表查询
     * @param uniqueKeys 唯一编码列表
     * @return 查询结果
     */
    public List<TpmRebatePolicyRelaFormulaConfig> findByUniqueKeys(List<String> uniqueKeys) {
        if (CollectionUtils.isEmpty(uniqueKeys)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(TpmRebatePolicyRelaFormulaConfig::getUniqueKey, uniqueKeys)
                .list();
    }

    public List<TpmRebatePolicyRelaFormulaConfig> findByRebatePolicyCode(String saleRebatePolicyCode) {
        if (StringUtils.isBlank(saleRebatePolicyCode)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(TpmRebatePolicyRelaFormulaConfig::getSaleRebatePolicyCode, saleRebatePolicyCode)
                .list();
    }

    public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
        if (StringUtils.isBlank(saleRebatePolicyCode)) {
            return;
        }
        this.lambdaUpdate()
                .eq(TpmRebatePolicyRelaFormulaConfig::getSaleRebatePolicyCode, saleRebatePolicyCode)
                .remove();
    }

    public List<TpmRebatePolicyRelaFormulaConfig> findByFormulaConfigCodes(List<String> formulaConfigCodes) {
        if (CollectionUtils.isEmpty(formulaConfigCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(TpmRebatePolicyRelaFormulaConfig::getFormulaConfigCode, formulaConfigCodes)
                .list();
    }
}
