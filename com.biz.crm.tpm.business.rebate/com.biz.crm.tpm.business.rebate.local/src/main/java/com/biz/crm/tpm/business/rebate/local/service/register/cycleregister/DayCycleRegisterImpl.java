package com.biz.crm.tpm.business.rebate.local.service.register.cycleregister;

import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyTaskService;
import com.biz.crm.tpm.business.rebate.local.utils.SaleRebateCycleUtil;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCycleRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateExecutionDateRangeVo;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: 日度返利
 * @author: lifei
 * @date: 2022/2/21 14:15
 */
@Component
@Slf4j
public class DayCycleRegisterImpl implements SaleRebatePolicyCycleRegister {

  /**
   * 返利周期名称
   */
  private static final String REBATE_POLICY_CYCLE_NAME = "日度返利";

  /**
   * 返利周期编码
   */
  private static final String REBATE_POLICY_CYCLE_CODE = "DAYCYCLE";
  /**
   * 日度返利基础定时任务表达式
   */
  public static final String DAY_CYCLE_CRON = "0 0 0 * * ?";

  /**
   * 排序
   */
  private static final Integer REBATE_POLICY_CYCLE_SORT = 5;

  @Autowired(required = false)
  private SaleRebatePolicyTaskService saleRebatePolicyTaskService;

  @Override
  public String getSaleRebatePolicyCycleCode() {
    return REBATE_POLICY_CYCLE_CODE;
  }

  @Override
  public String getSaleRebatePolicyCycleName() {
    return REBATE_POLICY_CYCLE_NAME;
  }

  /**
   * 获取返利周期定时任务表达式
   */
  @Override
  public String getSaleRebateCronExpression() {
    return DAY_CYCLE_CRON;
  }

  /**
   * 判断是否需要计算财年偏移量
   *
   * @return
   */
  @Override
  public boolean getCheckFiscalYear() {
    return false;
  }

  /**
   * 返利周期类型
   */
  @Override
  public int getCycleType() {
    return Calendar.DATE;
  }

  /**
   * 周期最小单位
   */
  @Override
  public int getCycleNum() {
    return 1;
  }

  @Override
  public Integer getCycleSort() {
    return REBATE_POLICY_CYCLE_SORT;
  }

  /**
   * （创建定时任务）
   *
   * <AUTHOR>
   * @date
   */
  @Override
  @Transactional
  public void onRequestcreate(SaleRebatePolicyDto saleRebatePolicyDto) {
    this.saleRebatePolicyTaskService.createRebatetask(saleRebatePolicyDto);
  }

  /**
   * （修改定时任务）
   *
   * <AUTHOR>
   * @date
   */
  @Override
  @Transactional
  public void onRequestUpdate(SaleRebatePolicyDto saleRebatePolicyDto) {
    this.saleRebatePolicyTaskService.updateRebatetask(saleRebatePolicyDto);
  }

  /**
   * 获取当天定时任务执行范围
   *
   * <AUTHOR>
   * @date
   */
  @Override
  public SaleRebateExecutionDateRangeVo getExecutionDateRangeVo(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
    String format = formatDate.format(saleRebatePolicyDto.getCalculateTime());
    return SaleRebateCycleUtil.getSaleRebateExecutionDayMap(
        saleRebatePolicyDto).get(format);
  }

}
