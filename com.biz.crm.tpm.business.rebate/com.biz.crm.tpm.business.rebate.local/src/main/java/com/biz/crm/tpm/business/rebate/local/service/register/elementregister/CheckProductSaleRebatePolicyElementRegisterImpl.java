package com.biz.crm.tpm.business.rebate.local.service.register.elementregister;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyCheckProductInfo;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyCheckProductInfoService;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.CheckProductSaleRebatePolicyElementDataVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.CheckProductSaleRebatePolicyElementVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @describe:返利政策考核产品要素注册器实现
 * @createTime 2022年02月17日 17:59:00
 */
@Slf4j
@Service("checkProductSaleRebatePolicyElementRegisterImpl")
public class CheckProductSaleRebatePolicyElementRegisterImpl implements
    SaleRebatePolicyElementRegister<CheckProductSaleRebatePolicyElementDataVo> {

  /**
   * 返利政策要素名称
   */
  private static final String REBATE_POLICY_ELEMENT_NAME = "考核产品";

  /**
   * 返利政策要素编码
   */
  private static final String REBATE_POLICY_ELEMENT_CODE = "product";

  /**
   * 排序
   */
  private static final Integer REBATE_POLICY_ELEMENT_SORT = 1;


  @Autowired(required = false)
  private SaleRebatePolicyCheckProductInfoService saleRebatePolicyCheckProductInfoService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;


  @Override
  public String getSaleRebatePolicyElementName() {
    return REBATE_POLICY_ELEMENT_NAME;
  }

  @Override
  public String getSaleRebatePolicyElementCode() {
    return REBATE_POLICY_ELEMENT_CODE;
  }

  @Override
  public Integer getElementSort() {
    return REBATE_POLICY_ELEMENT_SORT;
  }

  @Override
  public Class<CheckProductSaleRebatePolicyElementDataVo> getSaleRebatePolicyElementClass() {
    return CheckProductSaleRebatePolicyElementDataVo.class;
  }

  @Override
  public CheckProductSaleRebatePolicyElementDataVo getBySaleRebatePolicyCode(
      String saleRebatePolicyCode) {
    //返回vo
    CheckProductSaleRebatePolicyElementDataVo vo = new CheckProductSaleRebatePolicyElementDataVo();
    //查询考核产品
    List<SaleRebatePolicyCheckProductInfo> list = this.saleRebatePolicyCheckProductInfoService
        .findBySaleRebatePolicyCode(saleRebatePolicyCode);
    if (CollectionUtils.isEmpty(list)) {
      return vo;
    }
    this.getDataVoByList(list, vo);
    return vo;
  }


  @Override
  public CheckProductSaleRebatePolicyElementDataVo onRequestSaleRebatePolicyCreate(
      String saleRebatePolicyCode,
      CheckProductSaleRebatePolicyElementDataVo checkProductSaleRebatePolicyElementDataVo) {
    List<SaleRebatePolicyCheckProductInfo> list = this
        .getListByDataVo(saleRebatePolicyCode, checkProductSaleRebatePolicyElementDataVo);
    this.saleRebatePolicyCheckProductInfoService.createBatch(list);
    CheckProductSaleRebatePolicyElementDataVo vo = new CheckProductSaleRebatePolicyElementDataVo();
    this.getDataVoByList(list, vo);
    vo.setSaleRebatePolicyCode(saleRebatePolicyCode);
    return vo;
  }

  @Override
  public CheckProductSaleRebatePolicyElementDataVo onRequestSaleRebatePolicyUpdate(
      String saleRebatePolicyCode,
      CheckProductSaleRebatePolicyElementDataVo checkProductSaleRebatePolicyElementDataVo) {
    this.saleRebatePolicyCheckProductInfoService.deleteBySaleRebatePolicyCode(saleRebatePolicyCode);
    List<SaleRebatePolicyCheckProductInfo> list = this
        .getListByDataVo(saleRebatePolicyCode, checkProductSaleRebatePolicyElementDataVo);
    this.saleRebatePolicyCheckProductInfoService.createBatch(list);
    CheckProductSaleRebatePolicyElementDataVo vo = new CheckProductSaleRebatePolicyElementDataVo();
    this.getDataVoByList(list, vo);
    vo.setSaleRebatePolicyCode(saleRebatePolicyCode);
    return vo;
  }

  /**
   * 从要素封装vo里提取实体List
   *
   * @param checkProductSaleRebatePolicyElementDataVo
   */
  private List<SaleRebatePolicyCheckProductInfo> getListByDataVo(String saleRebatePolicyCode,
      CheckProductSaleRebatePolicyElementDataVo checkProductSaleRebatePolicyElementDataVo) {
    List<CheckProductSaleRebatePolicyElementVo> list = checkProductSaleRebatePolicyElementDataVo
        .getCheckProductList();
    //copy List
    List<SaleRebatePolicyCheckProductInfo> newList = (List<SaleRebatePolicyCheckProductInfo>) this.nebulaToolkitService
        .copyCollectionByWhiteList(list,
            CheckProductSaleRebatePolicyElementVo.class,
            SaleRebatePolicyCheckProductInfo.class,
            LinkedHashSet.class,
            ArrayList.class);
    newList.forEach(e -> {
      e.setSaleRebatePolicyCode(saleRebatePolicyCode);
      e.setTenantCode(TenantUtils.getTenantCode());
    });
    return newList;
  }

  /**
   * 将实体LIST封装成要素DataVo
   *
   * @param list
   * @param vo
   */
  private void getDataVoByList(List<SaleRebatePolicyCheckProductInfo> list,
      CheckProductSaleRebatePolicyElementDataVo vo) {
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    List<CheckProductSaleRebatePolicyElementVo> newList = (List<CheckProductSaleRebatePolicyElementVo>) this.nebulaToolkitService
        .copyCollectionByWhiteList(list, SaleRebatePolicyCheckProductInfo.class,
            CheckProductSaleRebatePolicyElementVo.class,
            LinkedHashSet.class,
            ArrayList.class);
    vo.setCheckProductList(newList);
    vo.setSaleRebatePolicyCode(list.get(0).getSaleRebatePolicyCode());
    vo.setTenantCode(TenantUtils.getTenantCode());
  }
}
