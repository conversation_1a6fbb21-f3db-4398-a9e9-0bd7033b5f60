package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe: 返利计算日志
 * @createTime 2022年02月28日 12:45:00
 */

@Getter
@Setter
@TableName("tpm_sale_rebate_calculation_log")
@Entity
@Table(name = "tpm_sale_rebate_calculation_log", indexes = {@Index(columnList = "tenant_code, sale_rebate_policy_code , customer_code,sale_rebate_calculation_years")})
@org.hibernate.annotations.Table(appliesTo = "tpm_sale_rebate_calculation_log", comment = "返利政策计算日志")
public class SaleRebateCalculationLog extends TenantFlagOpEntity {

  @Column(name = "sale_rebate_policy_code", length = 225, nullable = false, columnDefinition = "VARCHAR(225) COMMENT '促销编码'")
  @ApiModelProperty("返利编码")
  private String saleRebatePolicyCode;

  /**
   * 返利名称
   */
  @Column(name = "sale_rebate_policy_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '促销名称'")
  @ApiModelProperty("返利名称")
  private String saleRebatePolicyName;

  /**
   * 客户编码
   */
  @Column(name = "customer_code", length = 225, columnDefinition = "VARCHAR(225) COMMENT '客户编码'")
  @ApiModelProperty("客户编码")
  private String customerCode;

  /**
   * 客户名称
   */
  @Column(name = "customer_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '客户名称'")
  @ApiModelProperty("客户名称")
  private String customerName;

  /**
   * 返利计算时间年月
   */
  /**
   * 返利计算时间年月
   */
  @Column(name = "sale_rebate_calculation_years",nullable = false, columnDefinition = "varchar(20) COMMENT '返利计算时间'")
  @ApiModelProperty("返利计算时间年月")
  private String saleRebateCalculationYears;


  /**
   * 返利条件
   */
  @Column(name = "sale_rebate_policy_condition", length = 500, nullable = false, columnDefinition = "VARCHAR(500) COMMENT '返利条件'")
  @ApiModelProperty("返利条件")
  private String saleRebatePolicyCondition;

  /**
   * 返利条件取值过程
   */
  @Column(name = "sale_rebate_policy_condition_process", length = 500, columnDefinition = "VARCHAR(500) COMMENT '返利条件取值过程'")
  @ApiModelProperty("返利条件取值过程")
  private String saleRebatePolicyConditionProcess;

  /**
   * 返利公式
   */
  @Column(name = "sale_rebate_policy_formula",columnDefinition = "VARCHAR(500) COMMENT '返利公式'")
  @ApiModelProperty("返利计算公式")
  private String saleRebatePolicyFormula;

  /**
   * 返利公式计算取值过程
   */
  @Column(name = "sale_rebate_policy_formula_process", length = 500, columnDefinition = "VARCHAR(500) COMMENT '返利公式计算取值过程'")
  @ApiModelProperty("返利公式计算取值过程")
  private String saleRebatePolicyFormulaProcess;

  /**
   * 返利条件结果
   */
  @Column(name = "sale_rebate_results", nullable = false, columnDefinition = "int(1) COMMENT '返利条件结果,1=true，2=false'")
  @ApiModelProperty("返利条件结果")
  private Boolean saleRebateResults;

  /**
   * 返利计算结果
   */
  @Column(name = "sale_rebate_calculation_results", columnDefinition = "DECIMAL(20,4) COMMENT '返利计算结果'")
  @ApiModelProperty("返利计算结果")
  private BigDecimal saleRebateCalculationResults;

  /**
   * 是否测试
   */
  @Column(name = "is_test", columnDefinition = "varchar(5) COMMENT '是否测试'")
  @ApiModelProperty("是否测试")
  private String isTest;

  /**
   * 批次号
   */
  @Column(name = "speed_no", columnDefinition = "varchar(125) COMMENT '批次号'")
  @ApiModelProperty("批次号")
  private String speedNo;

  /**
   * 分配类型 全额 比例
   */
  @ApiModelProperty("分配类型")
  @Column(name = "allocation_type", columnDefinition = "int(5) COMMENT '分配类型 全额 比例'")
  private Integer allocationType;

  /**
   * 分配比例
   */
  @ApiModelProperty("分配比例")
  @Column(name = "rebate_ratio", columnDefinition = "decimal(20,4) COMMENT '分配比例'")
  private BigDecimal rebateRatio;

  /**
   * 返利明细id
   */
  @Column(name = "sale_rebate_policy_detail_id", columnDefinition = "varchar(125) COMMENT '明细id'")
  @ApiModelProperty("返利明细id")
  private String saleRebatePolicyDetailId;
}
