package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.rebate.local.constant.SalePolicyConstant;
import com.biz.crm.tpm.business.rebate.local.constant.SaleRebateCalculateTypeConstant;
import com.biz.crm.tpm.business.rebate.local.entity.*;
import com.biz.crm.tpm.business.rebate.local.enums.CalculateTypeEnum;
import com.biz.crm.tpm.business.rebate.local.service.*;
import com.biz.crm.tpm.business.rebate.local.utils.SaleRebatePolicyCalculateUtil;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleOnAccountStatusEnums;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleRebatePolicyProductTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleRebatePolicyStatusEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebateCalculateTypeRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCycleRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyTemplateRegister;
import com.biz.crm.tpm.business.rebate.sdk.strategy.SaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.sdk.vo.*;
import com.biz.crm.tpm.business.rebate.sdk.vo.scope.AbstractSaleRebatePolicyCustomerInfo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.product.level.sdk.service.ProductLevelVoSdkService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 返利计算逻辑类
 * @author: lifei
 * @date: 2022/3/7 13:46
 */
@Service
public class SaleRebatePolicyCalculateServiceImpl implements SaleRebatePolicyCalculateService {

    @Autowired(required = false)
    private SaleRebatePolicyVoService saleRebatePolicyVoService;
    @Autowired(required = false)
    private SaleRebatePolicyDetailService saleRebatePolicyDetailService;
    @Autowired(required = false)
    private List<SaleRebatePolicyCycleRegister> saleRebatePolicyCycleRegisters;
    @Autowired(required = false)
    private List<SaleRebatePolicyTemplateRegister> saleRebatePolicyTemplateRegisters;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private ApplicationContext applicationContext;
    @Autowired(required = false)
    private SaleRebatePolicyCheckProductInfoService saleRebatePolicyCheckProductInfoService;
    @Autowired(required = false)
    private SaleRebatePolicyProductInfoService saleRebatePolicyProductInfoService;
    @Autowired(required = false)
    private SaleRebatePolicyFormulaInfoService saleRebatePolicyFormulaInfoService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private SaleRebateCalculationLogService saleRebateCalculationLogService;
    @Autowired(required = false)
    private ProductLevelVoSdkService productLevelVoSdkService;
    @Autowired(required = false)
    private ProductVoService productVoService;
    @Autowired(required = false)
    private SaleRebatePolicyService saleRebatePolicyService;
    @Autowired(required = false)
    private List<SaleRebatePolicyCriterionRegister> saleRebatePolicyCriterionRegisters;
    @Autowired(required = false)
    private CustomerVoService customerVoService;
    @Autowired(required = false)
    private SaleRebateAdjustDetailService saleRebateAdjustDetailService;
    @Autowired(required = false)
    private List<SaleRebateCalculateTypeRegister> saleRebateCalculateTypeRegisters;
    @Autowired(required = false)
    private LoginUserService loginUserService;

    /**
     * 计算政策（定时任务）
     *
     * @param saleRebatePolicyCodes 政策编码
     * @param isTest                是否测试
     * <AUTHOR>
     * @date
     */
    @Override
    @Transactional
    public void onCalculateByCode(List<String> saleRebatePolicyCodes, String isTest, Date date) {
        List<SaleRebatePolicyVo> saleRebatePolicyVos = this.saleRebatePolicyVoService.findByCodes(saleRebatePolicyCodes);
        Validate.isTrue(CollectionUtils.isNotEmpty(saleRebatePolicyVos) && saleRebatePolicyVos.size() == saleRebatePolicyCodes.size(), "数据个数不匹配");
        List<SaleRebatePolicyVo> saleRebatePolicyVoList = saleRebatePolicyVos.stream().filter(
                saleRebatePolicyVo -> saleRebatePolicyVo.getEnableStatus().equals(EnableStatusEnum.DISABLE.getCode())).collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isEmpty(saleRebatePolicyVoList), "返利政策计算中存在已禁用数据！");
        //日期集合
        Map<String, SaleRebateExecutionDateRangeVo> dateRangeVoHashMap = this.getSaleRebateExecutionDateRangeVoMap(saleRebatePolicyVos, date);
        if (Objects.isNull(dateRangeVoHashMap)) {
            return;
        }
        //客户集合
        Map<String, Set<String>> customerCodeMap = this.getCustomerCodeMap(saleRebatePolicyCodes);
        //考核商品集合
        Map<String, Set<String>> checkproductCodeMap = this.getProductCodeMap(saleRebatePolicyCodes);
        //返利公式
        Map<String, List<SaleRebatePolicyFormulaInfoVo>> formulalistMap = this.getSaleRebatePolicyFormulaInfoMap(saleRebatePolicyCodes);
        //返利商品
        Map<String, List<SaleRebatePolicyProductInfoVo>> rebateproductCodeMap = this.getSaleRebatePolicyProductInfoMap(saleRebatePolicyCodes);
        //构建最细粒度保存
        this.buildAndCreateParam(saleRebatePolicyVos, customerCodeMap, checkproductCodeMap, dateRangeVoHashMap, rebateproductCodeMap, formulalistMap, isTest);
        //如果不是测试修改返利的状态
        if (BooleanEnum.FALSE.getNumStr().equals(isTest)) {
            saleRebatePolicyVos.forEach(saleRebatePolicyVo -> {
                String saleRebatePolicyCode = saleRebatePolicyVo.getSaleRebatePolicyCode();
                SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = dateRangeVoHashMap
                        .get(saleRebatePolicyCode);
                if (Objects.nonNull(saleRebateExecutionDateRangeVo)) {
                    if (saleRebateExecutionDateRangeVo.getSaleRebateEndTime()
                            .compareTo(saleRebatePolicyVo.getSaleRebateEndTime()) >= 0) {
                        this.saleRebatePolicyService.updateSaleRebatePolicyStatus(saleRebatePolicyVo.getId(),
                                SaleRebatePolicyStatusEnum.COMPLETED);
                    } else {
                        this.saleRebatePolicyService.updateSaleRebatePolicyStatus(saleRebatePolicyVo.getId(),
                                SaleRebatePolicyStatusEnum.ON_EXECUTION);
                    }
                }
            });
        }

    }


    @Override
    @Transactional
    public void onCalculateByDetailIds(List<String> detailIds) {
        Validate.isTrue(!CollectionUtils.isEmpty(detailIds), "主键集合不能为空!");
        List<SaleRebatePolicyDetail> saleRebatePolicyDetails = this.saleRebatePolicyDetailService
                .findByIds(detailIds);
        Validate.isTrue(!CollectionUtils.isEmpty(saleRebatePolicyDetails)
                && saleRebatePolicyDetails.size() == detailIds.size(), "返利计算个数不匹配");
        List<SaleRebatePolicyDetail> collect = saleRebatePolicyDetails.stream()
                .filter(saleRebatePolicyDetail -> SaleOnAccountStatusEnums.ON_ACCOUNT.getKey()
                        .equals(saleRebatePolicyDetail.getBillStatus())).collect(
                        Collectors.toList());
        Validate.isTrue(CollectionUtils.isEmpty(collect), "已上账数据不能进行返利计算");
        List<String> saleRebatePolicyCodes = saleRebatePolicyDetails.stream()
                .map(SaleRebatePolicyDetail::getSaleRebatePolicyCode).collect(
                        Collectors.toList());
        List<SaleRebatePolicy> saleRebatePolicies = this.saleRebatePolicyService
                .findBySaleRebatePolicyCodes(saleRebatePolicyCodes);
        List<SaleRebatePolicyVo> saleRebatePolicyVos =
                (List<SaleRebatePolicyVo>)
                        this.nebulaToolkitService.copyCollectionByBlankList(
                                saleRebatePolicies, SaleRebatePolicy.class, SaleRebatePolicyVo.class, HashSet.class,
                                ArrayList.class);
        Map<String, SaleRebatePolicyVo> saleRebatePolicyVoMap = saleRebatePolicyVos.stream()
                .collect(Collectors.toMap(SaleRebatePolicyVo::getSaleRebatePolicyCode, item -> item));
        //考核商品
        Map<String, Set<String>> checkproductCodeMap = this.getProductCodeMap(saleRebatePolicyCodes);
        //公式
        List<String> formulaIds = saleRebatePolicyDetails.stream()
                .map(SaleRebatePolicyDetail::getSaleRebatePolicyFormulaId).collect(
                        Collectors.toList());
        List<SaleRebatePolicyFormulaInfo> saleRebatePolicyFormulaInfos = this.saleRebatePolicyFormulaInfoService
                .findByIds(formulaIds);
        List<SaleRebatePolicyFormulaInfoVo> saleRebatePolicyFormulaInfoVos =
                (List<SaleRebatePolicyFormulaInfoVo>)
                        this.nebulaToolkitService.copyCollectionByBlankList(
                                saleRebatePolicyFormulaInfos, SaleRebatePolicyFormulaInfo.class,
                                SaleRebatePolicyFormulaInfoVo.class, HashSet.class, ArrayList.class);
        Map<String, SaleRebatePolicyFormulaInfoVo> saleRebatePolicyFormulaInfoVoMap = saleRebatePolicyFormulaInfoVos
                .stream()
                .collect(Collectors.toMap(SaleRebatePolicyFormulaInfoVo::getId, item -> item));
        //客户
        Set<String> customerCodes = saleRebatePolicyDetails.stream()
                .map(SaleRebatePolicyDetail::getCustomerCode).collect(
                        Collectors.toSet());
        List<CustomerVo> customerVos = this.customerVoService
                .findForPriceByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerVoMap = customerVos
                .stream()
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, item -> item));
        //计算金额
        Date date = new Date();
        List<SaleRebateComputeParamVo> paramVos = new ArrayList<>();
        for (SaleRebatePolicyDetail saleRebatePolicyDetail : saleRebatePolicyDetails) {
            SaleRebateComputeParamVo saleRebateComputeParamVo = new SaleRebateComputeParamVo();
            saleRebateComputeParamVo.setSpeedNo(saleRebatePolicyDetail.getSpeedNo());
            saleRebateComputeParamVo.setProductType(saleRebatePolicyDetail.getProductType());
            saleRebateComputeParamVo.setSaleRebatePolicyDetailId(saleRebatePolicyDetail.getId());
            saleRebateComputeParamVo.setSaleRebateDetailCode(saleRebatePolicyDetail.getSaleRebateDetailCode());
            if (saleRebatePolicyDetail.getProductType() != null) {
                if (SaleRebatePolicyProductTypeEnum.PRODUCT.getKey().equals(saleRebatePolicyDetail.getProductType())) {
                    saleRebateComputeParamVo.setCode(saleRebatePolicyDetail.getProductCode());
                    saleRebateComputeParamVo.setName(saleRebatePolicyDetail.getProductName());
                } else {
                    saleRebateComputeParamVo.setCode(saleRebatePolicyDetail.getProductLevelCode());
                    saleRebateComputeParamVo.setName(saleRebatePolicyDetail.getProductLevelName());
                }
            }
            saleRebateComputeParamVo.setAllocationType(saleRebatePolicyDetail.getAllocationType());
            saleRebateComputeParamVo.setRebateRatio(saleRebatePolicyDetail.getRebateRatio());
            SaleRebatePolicyVo saleRebatePolicyVo = saleRebatePolicyVoMap
                    .get(saleRebatePolicyDetail.getSaleRebatePolicyCode());
            Validate.notNull(saleRebatePolicyVo, "不存在启用得返利政策！");
            //数据从政策中取
            saleRebateComputeParamVo
                    .setSaleRebateStartTime(saleRebatePolicyVo.getSaleRebateStartTime());
            saleRebateComputeParamVo
                    .setSaleRebateEndTime(saleRebatePolicyVo.getSaleRebateEndTime());
            saleRebateComputeParamVo
                    .setSaleRebateComputeEndTime(saleRebatePolicyDetail.getSaleRebateEndTime());
            saleRebateComputeParamVo
                    .setSaleRebateComputeStartTime(saleRebatePolicyDetail.getSaleRebateStartTime());
            saleRebateComputeParamVo.setCalculationTime(date);
            saleRebateComputeParamVo.setIsTest(saleRebatePolicyDetail.getIsTest());
            //从公式中取
            SaleRebatePolicyFormulaInfoVo saleRebatePolicyFormulaInfoVo = saleRebatePolicyFormulaInfoVoMap
                    .get(saleRebatePolicyDetail.getSaleRebatePolicyFormulaId());
            saleRebateComputeParamVo.setSaleRebatePolicyFormulaInfoVo(saleRebatePolicyFormulaInfoVo);
            //方法中取
            Set<String> productCodes = checkproductCodeMap.get(saleRebatePolicyDetail.getSaleRebatePolicyCode());
            Map<String, BigDecimal> amountMap = this
                    .getAmountMap(saleRebatePolicyFormulaInfoVo, productCodes,
                            saleRebatePolicyDetail.getCustomerCode(), saleRebatePolicyDetail, saleRebateComputeParamVo);
            saleRebateComputeParamVo.setAmountMap(amountMap);
            saleRebateComputeParamVo.setSaleRebatePolicyVo(saleRebatePolicyVo);
            if (Objects.isNull(customerVoMap)) {
                return;
            }
            CustomerVo customerVo = customerVoMap.get(saleRebatePolicyDetail.getCustomerCode());
            if (Objects.isNull(customerVo)) {
                return;
            }
            saleRebateComputeParamVo.setCusJson(JsonUtils.toJSONObject(customerVo));
            saleRebateComputeParamVo.setCalculateType(saleRebatePolicyVo.getCalculateType());
            paramVos.add(saleRebateComputeParamVo);
        }
        //计算返利明细
        this.onCalculate(paramVos);
    }

    /**
     * 构建最小粒度参数 (从模板中取数)
     *
     * @param saleRebatePolicyVos 政策编码
     * @param dateRangeVoHashMap  执行日期集合
     * @param customerCodeMap     客户范围
     * @param checkproductCodeMap 考核商品
     * <AUTHOR>
     * @date
     */
    private void buildAndCreateParam(List<SaleRebatePolicyVo> saleRebatePolicyVos,
                                     Map<String, Set<String>> customerCodeMap, Map<String, Set<String>> checkproductCodeMap,
                                     Map<String, SaleRebateExecutionDateRangeVo> dateRangeVoHashMap,
                                     Map<String, List<SaleRebatePolicyProductInfoVo>> rebateproductCodeMap,
                                     Map<String, List<SaleRebatePolicyFormulaInfoVo>> formulalistMap, String isTest) {
        //如果不是执行时间内直接返回
        if (Objects.isNull(dateRangeVoHashMap) && BooleanEnum.FALSE.getNumStr()
                .equals(isTest)) {
            return;
        }
        //返利注册器
        List<SaleRebatePolicyTemplateRegister> templateRegisters = this
                .getSaleRebatePolicyTemplateRegisters(saleRebatePolicyVos);
        //构建参数
        List<SaleRebateComputeBuildParamVo> saleRebateComputeBuildParamVos = this
                .buildParamSaleRebateCodeVo(saleRebatePolicyVos, customerCodeMap, checkproductCodeMap, dateRangeVoHashMap, rebateproductCodeMap, formulalistMap, isTest);
        //通过返利类型分组进入不同的返利策略（现有：货补 折扣）
        Map<String, List<SaleRebateComputeBuildParamVo>> saleRebateComputeBuildParamMap = saleRebateComputeBuildParamVos
                .stream().collect(Collectors.groupingBy(SaleRebateComputeBuildParamVo::getSaleRebateType));
        templateRegisters.forEach(saleRebatePolicyTemplateRegister -> {
            List<SaleRebateComputeBuildParamVo> computeBuildParamVos = saleRebateComputeBuildParamMap
                    .get(saleRebatePolicyTemplateRegister.getSaleRebatePolicytemplateCode());
            //最小粒度参数
            List<SaleRebateComputeParamVo> saleRebateComputeParamVos = saleRebatePolicyTemplateRegister
                    .onbuildParam(computeBuildParamVos);
            if (CollectionUtils.isNotEmpty(saleRebateComputeParamVos)) {
                saleRebateComputeParamVos.forEach(saleRebateComputeParamVo -> {
                    saleRebateComputeParamVo.setIsTest(isTest);
                });
            }
            //执行策略返利计算
            String calculateType = saleRebatePolicyVos.get(0).getCalculateType();
            SaleRebateCalculateTypeRegister saleRebateCalculateTypeRegister = this.findCalculateType(calculateType,
                    saleRebateCalculateTypeRegisters);
            saleRebateCalculateTypeRegister.handleCalculate(saleRebateComputeParamVos);
        });
    }

    /**
     * 政策编码返利公式
     *
     * @param saleRebatePolicyCodes
     * <AUTHOR>
     * @date
     */
    private Map<String, List<SaleRebatePolicyFormulaInfoVo>> getSaleRebatePolicyFormulaInfoMap(
            List<String> saleRebatePolicyCodes) {
        List<SaleRebatePolicyFormulaInfo> saleRebatePolicyFormulaInfos = this.saleRebatePolicyFormulaInfoService
                .findBySaleRebatePolicyCodes(saleRebatePolicyCodes);
        if (CollectionUtils.isEmpty(saleRebatePolicyFormulaInfos)) {
            return null;
        }
        List<SaleRebatePolicyFormulaInfoVo> list = (List<SaleRebatePolicyFormulaInfoVo>)
                this.nebulaToolkitService.copyCollectionByBlankList(
                        saleRebatePolicyFormulaInfos,
                        SaleRebatePolicyFormulaInfo.class,
                        SaleRebatePolicyFormulaInfoVo.class,
                        HashSet.class,
                        ArrayList.class);
        return list.stream()
                .collect(Collectors.groupingBy(SaleRebatePolicyFormulaInfoVo::getSaleRebatePolicyCode));
    }

    /**
     * 政策编码返利公式
     *
     * @param saleRebatePolicyCodes
     * <AUTHOR>
     * @date
     */
    private Map<String, List<SaleRebatePolicyProductInfoVo>> getSaleRebatePolicyProductInfoMap(
            List<String> saleRebatePolicyCodes) {
        Map<String, List<SaleRebatePolicyProductInfoVo>> listMap = new HashMap<>();
        List<SaleRebatePolicyProductInfo> saleRebatePolicyProductInfos = this.saleRebatePolicyProductInfoService
                .findBySaleRebatePolicyCodes(saleRebatePolicyCodes);
        if (CollectionUtils.isEmpty(saleRebatePolicyProductInfos)) {
            return listMap;
        }
        List<SaleRebatePolicyProductInfoVo> list = (List<SaleRebatePolicyProductInfoVo>)
                this.nebulaToolkitService.copyCollectionByBlankList(
                        saleRebatePolicyProductInfos,
                        SaleRebatePolicyProductInfo.class,
                        SaleRebatePolicyProductInfoVo.class,
                        HashSet.class,
                        ArrayList.class);
        return list.stream()
                .collect(Collectors.groupingBy(SaleRebatePolicyProductInfoVo::getSaleRebatePolicyCode));
    }


    /**
     * 政策返回执行日期
     * 本次定时任务执行时间范围
     * 如果是企业财年（财年影响到 月 季度 年度）
     * 本次执行范围应该为财年范围 开始时间和结束时间 是通过和政策执行开始结束时间比较后结果。
     *
     * @param saleRebatePolicyVos
     * <AUTHOR>
     * @date
     */
    private Map<String, SaleRebateExecutionDateRangeVo> getSaleRebateExecutionDateRangeVoMap(
            List<SaleRebatePolicyVo> saleRebatePolicyVos, Date date) {
        Map<String, SaleRebateExecutionDateRangeVo> listMap = new HashMap<>();
        saleRebatePolicyVos.forEach(saleRebatePolicyVo -> {
            List<SaleRebatePolicyCycleRegister> registers = saleRebatePolicyCycleRegisters.stream().filter(
                            saleRebatePolicyCycleRegister -> saleRebatePolicyCycleRegister.getSaleRebatePolicyCycleCode().equals(saleRebatePolicyVo.getSaleRebatePolicyCycle()))
                    .collect(Collectors.toList());
            Validate.isTrue(CollectionUtils.isNotEmpty(registers), "无可用的返利周期维度注册信息");
            SaleRebatePolicyCycleRegister saleRebatePolicyCycleRegister = registers.get(0);
            //计算返利时间范围
            SaleRebatePolicyDto saleRebatePolicyDto = this.nebulaToolkitService.copyObjectByWhiteList(saleRebatePolicyVo, SaleRebatePolicyDto.class, HashSet.class, ArrayList.class);
            saleRebatePolicyDto.setCalculateTime(date);
            SaleRebateExecutionDateRangeVo executionDateRangeVo = saleRebatePolicyCycleRegister.getExecutionDateRangeVo(saleRebatePolicyDto);
            if (!Objects.isNull(executionDateRangeVo)) {
                listMap.put(saleRebatePolicyVo.getSaleRebatePolicyCode(), executionDateRangeVo);
            }
        });
        return listMap.size() > 0 ? listMap : null;
    }


    /**
     * 获取返利类型注册器
     *
     * @param saleRebatePolicyVos
     * <AUTHOR>
     * @date
     */
    private List<SaleRebatePolicyTemplateRegister> getSaleRebatePolicyTemplateRegisters(
            List<SaleRebatePolicyVo> saleRebatePolicyVos) {
        Set<String> saleRebateTypes = saleRebatePolicyVos.stream()
                .map(SaleRebatePolicyVo::getSaleRebateType)
                .collect(Collectors.toSet());
        List<SaleRebatePolicyTemplateRegister> templateRegisters = saleRebatePolicyTemplateRegisters
                .stream().filter(saleRebatePolicyTemplateRegister -> saleRebateTypes
                        .contains(saleRebatePolicyTemplateRegister
                                .getSaleRebatePolicytemplateCode()))
                .collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isNotEmpty(templateRegisters), "返利类型注册器不存在，请联系管理员！");
        return templateRegisters;
    }


    /**
     * 构建参数（政策编码构建参数）
     * 一条数据中有 政策编码 以及对应的明细数据
     *
     * @param saleRebatePolicyVos
     * <AUTHOR>
     * @date
     */
    private List<SaleRebateComputeBuildParamVo> buildParamSaleRebateCodeVo(
            List<SaleRebatePolicyVo> saleRebatePolicyVos,
            Map<String, Set<String>> customerCodeMap, Map<String, Set<String>> checkproductCodeMap,
            Map<String, SaleRebateExecutionDateRangeVo> dateRangeVoHashMap,
            Map<String, List<SaleRebatePolicyProductInfoVo>> rebateproductCodeMap,
            Map<String, List<SaleRebatePolicyFormulaInfoVo>> formulalistMap, String isTest) {
        //当前时间
        Date date = new Date();
        List<SaleRebateComputeBuildParamVo> saleRebateComputeBuildParamVos = new ArrayList<>();
        saleRebatePolicyVos.forEach(saleRebatePolicyVo -> {
            String speedNo = generateCodeService
                    .generateCode(SalePolicyConstant.SALE_REBATE_SPEED_CODE, 1)
                    .get(0);
            SaleRebateExecutionDateRangeVo saleRebateExecutionDate = dateRangeVoHashMap
                    .get(saleRebatePolicyVo.getSaleRebatePolicyCode());
            if (Objects.isNull(saleRebateExecutionDate) && BooleanEnum.FALSE.getNumStr().equals(isTest)) {
                return;
            }
            //如果是测试 并且档期执行日期为空，那么只跑当日
            if (Objects.isNull(saleRebateExecutionDate)) {
                saleRebateExecutionDate = new SaleRebateExecutionDateRangeVo();
                saleRebateExecutionDate.setSaleRebateStartTime(new Date());
                saleRebateExecutionDate.setSaleRebateEndTime(new Date());
            }
            SaleRebateComputeBuildParamVo saleRebateComputeBuildParamVo = new SaleRebateComputeBuildParamVo();
            saleRebateComputeBuildParamVo
                    .setSaleRebatePolicyVo(saleRebatePolicyVo);
            saleRebateComputeBuildParamVo
                    .setSaleRebateStartTime(saleRebateExecutionDate.getSaleRebateStartTime());
            saleRebateComputeBuildParamVo
                    .setSaleRebateEndTime(saleRebateExecutionDate.getSaleRebateEndTime());
            saleRebateComputeBuildParamVo
                    .setCustomerCodes(customerCodeMap.get(saleRebatePolicyVo.getSaleRebatePolicyCode()));
            saleRebateComputeBuildParamVo
                    .setProductCodes(checkproductCodeMap.get(saleRebatePolicyVo.getSaleRebatePolicyCode()));
            saleRebateComputeBuildParamVo.setSaleRebateType(saleRebatePolicyVo.getSaleRebateType());
            saleRebateComputeBuildParamVo
                    .setSaleRebatePolicyProductInfoVos(rebateproductCodeMap.get(saleRebatePolicyVo.getSaleRebatePolicyCode()));
            List<SaleRebatePolicyFormulaInfoVo> saleRebatePolicyFormulaInfoVos = formulalistMap.get(
                    saleRebatePolicyVo.getSaleRebatePolicyCode());
            // 兼容之前的数据
            Set<SaleRebatePolicyFormulaInfoVo> itemList = saleRebatePolicyFormulaInfoVos.stream()
                    .filter(
                            saleRebatePolicyFormulaInfoVo -> saleRebatePolicyFormulaInfoVo.getFormulaSort()
                                    != null)
                    .collect(
                            Collectors.toSet());
            if (!CollectionUtils.isEmpty(itemList) && StringUtils.isNotBlank(saleRebatePolicyVo.getCalculateType())) {
                //按照顺序字段排升序
                saleRebatePolicyFormulaInfoVos = saleRebatePolicyFormulaInfoVos.stream()
                        .sorted(Comparator.comparingInt(SaleRebatePolicyFormulaInfoVo::getFormulaSort))
                        .collect(Collectors.toList());
            }
            saleRebateComputeBuildParamVo.setSaleRebatePolicyFormulaInfoVos(saleRebatePolicyFormulaInfoVos);
            saleRebateComputeBuildParamVo.setSpeedNo(speedNo);
            saleRebateComputeBuildParamVo.setCalculationTime(date);
            saleRebateComputeBuildParamVo.setCalculateType(saleRebatePolicyVo.getCalculateType());
            saleRebateComputeBuildParamVos.add(saleRebateComputeBuildParamVo);
        });
        return saleRebateComputeBuildParamVos;
    }

    /**
     * 根据最低粒度计算价格 1、查询出来政策 2、查询出所有变量的值 3、解析表达式 4、保存结果 5、保存日志和计算过程日志
     *
     * @param params
     */
    @Override
    @Transactional
    public void onCalculate(List<SaleRebateComputeParamVo> params) {
        int count = 0;
        FacturerUserDetails loginDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
        for (SaleRebateComputeParamVo param : params) {
            SaleRebatePolicyCalculateUtil.validateCal(param);
            SaleRebatePolicyFormulaInfoVo saleRebatePolicyFormulaInfoVo = param
                    .getSaleRebatePolicyFormulaInfoVo();
            SaleRebatePolicyVo saleRebatePolicyVo = param.getSaleRebatePolicyVo();
            SaleRebateConditionResultVo conditionResult = SaleRebatePolicyCalculateUtil
                    .computeConditionExpression(saleRebatePolicyFormulaInfoVo.getSaleRebatePolicyCondition(),
                            saleRebatePolicyFormulaInfoVo.getSaleRebatePolicyConditionName(),
                            param);
            SaleRebateFormulaResultVo executeComputeResult = null;
            boolean flag = conditionResult.isValue();
            String saleRebatePolicyDetailId = null;
            boolean isTest = BooleanEnum.TRUE.getNumStr().equals(param.getIsTest());
            if (flag) {
                //满足返利条件成立时执行
                //根据命中策略执行
                String calculateType = param.getCalculateType();
                //兼容之前的业务数据 若命中策略不存在 则默认为任意命中的策略
                if (StringUtils.isBlank(calculateType)) {
                    calculateType = SaleRebateCalculateTypeConstant.RANDOM_HIT_REGISTER_CODE;
                }
                SaleRebateCalculateTypeRegister saleRebateCalculateTypeRegister = this.findCalculateType(calculateType, saleRebateCalculateTypeRegisters);
                String hitCode = saleRebateCalculateTypeRegister.getCalculateTypeRegisterCode();
                int hitCount = CalculateTypeEnum.getCalculateTypeEnum(hitCode).getHitCount();
                String calculateTypeRegisterCode = saleRebateCalculateTypeRegister.getCalculateTypeRegisterCode();
                //排除任意命中的情况
                if (!calculateTypeRegisterCode.equals(SaleRebateCalculateTypeConstant.RANDOM_HIT_REGISTER_CODE) && count == hitCount) {
                    break;
                }
                executeComputeResult = SaleRebatePolicyCalculateUtil
                        .computeRebateExpression(saleRebatePolicyFormulaInfoVo.getSaleRebatePolicyFormula(),
                                saleRebatePolicyFormulaInfoVo.getSaleRebatePolicyFormulaName(),
                                param);
                //如果是测试计算,删除老的测试明细记录
                if (isTest) {
                    this.saleRebatePolicyDetailService
                            .deleteByRebateCodeAndTest(saleRebatePolicyVo.getSaleRebatePolicyCode(),
                                    BooleanEnum.TRUE.getNumStr(), param.getSpeedNo());
                }
                CustomerVo customerVo = JsonUtils
                        .json2Obj(param.getCusJson().toJSONString(), CustomerVo.class);
                SaleRebatePolicyDetail saleRebatePolicyDetail = SaleRebatePolicyCalculateUtil
                        .buildDetail(param, executeComputeResult, customerVo, loginDetails);
                if (saleRebatePolicyDetail.getRebateRatio() != null) {
                    BigDecimal rebateAmount = executeComputeResult.getValue()
                            .multiply(saleRebatePolicyDetail.getRebateRatio())
                            .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
                    saleRebatePolicyDetail.setRebateAmount(rebateAmount);
                    saleRebatePolicyDetail.setActualRebateAmount(rebateAmount);
                } else {
                    saleRebatePolicyDetail.setRebateAmount(executeComputeResult.getValue());
                    saleRebatePolicyDetail.setActualRebateAmount(executeComputeResult.getValue());
                }
                saleRebatePolicyDetail.setAdjustAmount(BigDecimal.ZERO);
                saleRebatePolicyDetail.setId(param.getSaleRebatePolicyDetailId());
                saleRebatePolicyDetail.setSaleRebateDetailCode(param.getSaleRebateDetailCode());
                Validate.notNull(saleRebatePolicyDetail, "生成计算结果明细失败");
                SaleRebatePolicyDetail rebatePolicyDetail = this.saleRebatePolicyDetailService
                        .createOrUpdate(saleRebatePolicyDetail);
                saleRebatePolicyDetailId = rebatePolicyDetail.getId();
                //如果有调整记录做一笔冲账记录
                this.handleAdjust(rebatePolicyDetail);
                count++;
            }
            //5、如果条件公式不成立 删除之前的测试日志，保存新的
            if (isTest) {
                this.saleRebateCalculationLogService
                        .deleteByRebateCodeAndTest(saleRebatePolicyVo.getSaleRebatePolicyCode(),
                                BooleanEnum.TRUE.getNumStr(), param.getSpeedNo());
            }
            SaleRebateCalculationLog saleRebateCalculationLog = SaleRebatePolicyCalculateUtil
                    .buildComputeLog(param, conditionResult, executeComputeResult, saleRebatePolicyDetailId);
            this.saleRebateCalculationLogService.create(saleRebateCalculationLog);
        }
    }

    /**
     * 查询返利计算的命中策略
     *
     * @param saleRebateCalculateTypeRegisters
     * @return
     */
    private SaleRebateCalculateTypeRegister findCalculateType(String code, List<SaleRebateCalculateTypeRegister> saleRebateCalculateTypeRegisters) {
        if (CollectionUtils.isEmpty(saleRebateCalculateTypeRegisters)) {
            return null;
        }
        return saleRebateCalculateTypeRegisters.stream().filter(saleRebateCalculateTypeRegister -> saleRebateCalculateTypeRegister.getCalculateTypeRegisterCode().equals(code)).findFirst().orElse(null);
    }

    /**
     * 政策编码考核商品
     *
     * @param saleRebatePolicyCodes
     * <AUTHOR>
     * @date
     */
    private Map<String, Set<String>> getProductCodeMap(List<String> saleRebatePolicyCodes) {
        Map<String, Set<String>> setMap = new HashMap<>();
        List<SaleRebatePolicyCheckProductInfo> saleRebatePolicyCheckProductInfos = this.saleRebatePolicyCheckProductInfoService
                .findBySaleRebatePolicyCodes(saleRebatePolicyCodes);
        Validate.isTrue(
                !org.springframework.util.CollectionUtils.isEmpty(saleRebatePolicyCheckProductInfos),
                "考核商品不存在！");
        //考核商品
        Map<String, List<SaleRebatePolicyCheckProductInfo>> collect = saleRebatePolicyCheckProductInfos
                .stream()
                .collect(Collectors.groupingBy(SaleRebatePolicyCheckProductInfo::getSaleRebatePolicyCode));
        collect.forEach((s, checkProductInfos) -> {
            Set<String> collectProduct = new HashSet<>();
            Set<String> productLevels = checkProductInfos.stream().filter(
                            saleRebatePolicyCheckProductInfo -> saleRebatePolicyCheckProductInfo.getType()
                                    .equals(SaleRebatePolicyProductTypeEnum.PRODUCT_LEVEL.getDictCode()))
                    .map(SaleRebatePolicyCheckProductInfo::getCode).collect(
                            Collectors.toSet());
            collectProduct.addAll(checkProductInfos.stream().filter(
                            saleRebatePolicyCheckProductInfo -> saleRebatePolicyCheckProductInfo.getType()
                                    .equals(SaleRebatePolicyProductTypeEnum.PRODUCT.getDictCode()))
                    .map(SaleRebatePolicyCheckProductInfo::getCode).collect(
                            Collectors.toSet()));
            //全部下级层级的编码(本级和下级所有层级)
            List<String> curAndChildrenCodesByCodes = this.productLevelVoSdkService
                    .findCurAndChildrenCodesByCodes(productLevels);
            //层级下所有产品
            List<ProductVo> productVos = this.productVoService
                    .findByProductLevelCodes(curAndChildrenCodesByCodes);
            if (!org.springframework.util.CollectionUtils.isEmpty(productVos)) {
                collectProduct
                        .addAll(productVos.stream().map(ProductVo::getProductCode).collect(Collectors.toSet()));
            }
            Validate.isTrue(!org.springframework.util.CollectionUtils.isEmpty(collectProduct),
                    "返利政策编码%s考核商品不存在!",
                    s);
            setMap.put(s, collectProduct);
        });
        return setMap;
    }

    /**
     * 获取返利政策中客户范围集合
     *
     * @param saleRebatePolicyCodes
     * @return 拿到政策对应的客户编码集合 key 政策编码 value 客户集合
     * <AUTHOR>
     */
    private Map<String, Set<String>> getCustomerCodeMap(List<String> saleRebatePolicyCodes) {
        Validate.isTrue(!org.springframework.util.CollectionUtils.isEmpty(saleRebatePolicyCodes),
                "传入返利编码为空！");
        List<SaleRebatePolicy> saleRebatePolicys = this.saleRebatePolicyService
                .findBySaleRebatePolicyCodes(saleRebatePolicyCodes);
        Validate
                .isTrue(!org.springframework.util.CollectionUtils.isEmpty(saleRebatePolicys), "返利政策不存在！");
        Validate
                .isTrue(saleRebatePolicyCodes.size() == saleRebatePolicys.size(), "返利政策，传入返利政策编码和匹配数量不相等");
        Validate.isTrue(
                !org.springframework.util.CollectionUtils.isEmpty(saleRebatePolicyTemplateRegisters),
                "不存在返利类型注册器！");
        Map<String, Set<String>> setMap = new HashMap<>();
        saleRebatePolicys.forEach(saleRebatePolicy -> {
            Set<String> customers = new HashSet<>();
            List<SaleRebatePolicyTemplateRegister> templateRegisters = saleRebatePolicyTemplateRegisters
                    .stream()
                    .filter(saleRebatePolicyTemplateRegister -> saleRebatePolicyTemplateRegister
                            .getSaleRebatePolicytemplateCode().equals(saleRebatePolicy.getSaleRebateType()))
                    .collect(
                            Collectors.toList());
            SaleRebatePolicyTemplateRegister saleRebatePolicyTemplateRegister = templateRegisters.get(0);
            Collection<Class<? extends SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo>>> customerScopeStrategyClasses = saleRebatePolicyTemplateRegister
                    .getCustomerScopeStrategyClasses();
            for (Class<? extends SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo>> customerScopeStrategyClass : customerScopeStrategyClasses) {
                SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo> customerScopeStrategy = applicationContext
                        .getBean(customerScopeStrategyClass);
                Set<String> set = customerScopeStrategy
                        .onRequestCustomerCodes(saleRebatePolicy.getSaleRebatePolicyCode(),
                                saleRebatePolicy.getTenantCode());
                if (!org.springframework.util.CollectionUtils.isEmpty(set)) {
                    customers.addAll(set);
                }
            }
            Set<String> customerList = customers.stream().collect(Collectors.toSet());
            setMap.put(saleRebatePolicy.getSaleRebatePolicyCode(), customerList);
        });
        return setMap;
    }

    private Map<String, BigDecimal> getAmountMap(
            SaleRebatePolicyFormulaInfoVo formulaInfo,
            Set<String> productCodes,
            String customerCode,
            SaleRebatePolicyDetail saleRebatePolicyDetail,
            SaleRebateComputeParamVo saleRebateComputeParamVo
    ) {
        Set<String> collect = SaleRebatePolicyCalculateUtil.getCriterionSet(
                Collections.singletonList(formulaInfo));
        Map<String, BigDecimal> amountMap = new HashMap<>();
        collect.forEach(s -> {
            List<SaleRebatePolicyCriterionRegister> list = this.saleRebatePolicyCriterionRegisters
                    .stream()
                    .filter(saleRebatePolicyCriterionRegister -> s.startsWith(saleRebatePolicyCriterionRegister
                            .getSaleRebatePolicyCriterionCode())).collect(
                            Collectors.toList());
            Validate.isTrue(CollectionUtils.isNotEmpty(list), "基准注册器不存在！");
            SaleRebatePolicyCriterionRegister bean = list.get(0);
            String saleRebatePolicyCode = saleRebatePolicyDetail.getSaleRebatePolicyCode();
            amountMap.putAll(
//                    bean.getAmountMapByCondition(
//                            s,
//                            saleRebatePolicyCode,
//                            Collections.singleton(customerCode),
//                            productCodes,
//                            saleRebatePolicyDetail.getSaleRebateStartTime(),
//                            saleRebatePolicyDetail.getSaleRebateEndTime(),
//                            saleRebateComputeParamVo.getSaleRebateStartTime(),
//                            saleRebateComputeParamVo.getSaleRebateEndTime()
//                    )
                    Maps.newHashMap()
            );
        });
        return amountMap;
    }

    /**
     * 返利计算调整记录
     *
     * @param rebatePolicyDetail
     * <AUTHOR>
     * @date
     */
    private void handleAdjust(SaleRebatePolicyDetail rebatePolicyDetail) {
        List<SaleRebateAdjustDetail> saleRebateAdjustDetails = this.saleRebateAdjustDetailService
                .findBySaleRebateDetailCode(rebatePolicyDetail.getSaleRebateDetailCode());
        if (CollectionUtils.isNotEmpty(saleRebateAdjustDetails)) {
            BigDecimal decimal = saleRebateAdjustDetails.stream()
                    .filter(k -> Objects.nonNull(k.getAdjustAmount()))
                    .map(SaleRebateAdjustDetail::getAdjustAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            SaleRebateAdjustDetail saleRebateAdjustDetail = new SaleRebateAdjustDetail();
            saleRebateAdjustDetail.setTenantCode(TenantUtils.getTenantCode());
            saleRebateAdjustDetail.setCustomerName(rebatePolicyDetail.getCustomerName());
            saleRebateAdjustDetail.setCustomerCode(rebatePolicyDetail.getCustomerCode());
            saleRebateAdjustDetail.setSaleRebatePolicyName(rebatePolicyDetail.getSaleRebatePolicyName());
            saleRebateAdjustDetail.setSaleRebateDetailCode(rebatePolicyDetail.getSaleRebateDetailCode());
            saleRebateAdjustDetail.setAdjustAmount(BigDecimal.ZERO.subtract(decimal));
            saleRebateAdjustDetail.setRemark("返利重新计算后，冲账记录。");
            saleRebateAdjustDetailService.create(saleRebateAdjustDetail);
        }
    }
}
