package com.biz.crm.tpm.business.rebate.local.service.register.elementregister;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyScopeInfo;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyScopeInfoService;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.ScopeSaleRebatePolicyElementDataVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.ScopeSaleRebatePolicyElementVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @describe:返利政策考核范围要素注册器实现
 * @createTime 2022年02月18日 15:23:00
 */
@Slf4j
@Service("scopeSaleRebatePolicyElementRegisterImpl")
public class ScopeSaleRebatePolicyElementRegisterImpl implements
    SaleRebatePolicyElementRegister<ScopeSaleRebatePolicyElementDataVo> {


  /**
   * 返利政策要素名称
   */
  private static final String REBATE_POLICY_ELEMENT_NAME = "返利政策范围";

  /**
   * 返利政策要素编码
   */
  private static final String REBATE_POLICY_ELEMENT_CODE = "scope";

  /**
   * 排序
   */
  private static final Integer REBATE_POLICY_ELEMENT_SORT = 3;

  @Autowired(required = false)
  private SaleRebatePolicyScopeInfoService saleRebatePolicyScopeInfoService;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;


  @Override
  public String getSaleRebatePolicyElementName() {
    return REBATE_POLICY_ELEMENT_NAME;
  }

  @Override
  public String getSaleRebatePolicyElementCode() {
    return REBATE_POLICY_ELEMENT_CODE;
  }

  @Override
  public Integer getElementSort() {
    return REBATE_POLICY_ELEMENT_SORT;
  }

  @Override
  public Class<ScopeSaleRebatePolicyElementDataVo> getSaleRebatePolicyElementClass() {
    return ScopeSaleRebatePolicyElementDataVo.class;
  }

  @Override
  public ScopeSaleRebatePolicyElementDataVo getBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    ScopeSaleRebatePolicyElementDataVo vo = new ScopeSaleRebatePolicyElementDataVo();
    List<SaleRebatePolicyScopeInfo> list = this.saleRebatePolicyScopeInfoService
        .findBySaleRebatePolicyCode(saleRebatePolicyCode);
    if (CollectionUtils.isEmpty(list)) {
      return vo;
    }
    this.getDataVoByList(list, vo);
    return vo;
  }

  @Override
  public ScopeSaleRebatePolicyElementDataVo onRequestSaleRebatePolicyCreate(
      String saleRebatePolicyCode,
      ScopeSaleRebatePolicyElementDataVo scopeSaleRebatePolicyElementDataVo) {
    List<SaleRebatePolicyScopeInfo> list = getListByDataVo(saleRebatePolicyCode,
        scopeSaleRebatePolicyElementDataVo);
    Set<SaleRebatePolicyScopeInfo> set = new HashSet<>();
    set.addAll(list);
    this.saleRebatePolicyScopeInfoService.createBatch(set);
    ScopeSaleRebatePolicyElementDataVo vo = new ScopeSaleRebatePolicyElementDataVo();
    this.getDataVoByList(list, vo);
    vo.setSaleRebatePolicyCode(saleRebatePolicyCode);
    return vo;
  }


  @Override
  public ScopeSaleRebatePolicyElementDataVo onRequestSaleRebatePolicyUpdate(
      String saleRebatePolicyCode,
      ScopeSaleRebatePolicyElementDataVo scopeSaleRebatePolicyElementDataVo) {
    this.saleRebatePolicyScopeInfoService.deleteBySaleRebatePolicyCode(saleRebatePolicyCode);
    List<SaleRebatePolicyScopeInfo> list = this
        .getListByDataVo(saleRebatePolicyCode, scopeSaleRebatePolicyElementDataVo);
    Set<SaleRebatePolicyScopeInfo> set = new HashSet<>();
    set.addAll(list);
    this.saleRebatePolicyScopeInfoService.createBatch(set);
    ScopeSaleRebatePolicyElementDataVo vo = new ScopeSaleRebatePolicyElementDataVo();
    this.getDataVoByList(list, vo);
    return vo;
  }

  /**
   * 将实体LIST封装成要素DataVo
   *
   * @param list
   * @param vo
   */
  private void getDataVoByList(List<SaleRebatePolicyScopeInfo> list,
      ScopeSaleRebatePolicyElementDataVo vo) {
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    List<ScopeSaleRebatePolicyElementVo> newList = (List<ScopeSaleRebatePolicyElementVo>) this.nebulaToolkitService
        .copyCollectionByWhiteList(list, SaleRebatePolicyScopeInfo.class,
            ScopeSaleRebatePolicyElementVo.class,
            LinkedHashSet.class,
            ArrayList.class);
    vo.setScopeList(newList);
    vo.setSaleRebatePolicyCode(list.get(0).getSaleRebatePolicyCode());
    vo.setTenantCode(TenantUtils.getTenantCode());
  }

  /**
   * 从要素封装vo里提取实体List
   *
   * @param scopeSaleRebatePolicyElementDataVo
   */
  private List<SaleRebatePolicyScopeInfo> getListByDataVo(String saleRebatePolicyCode,
      ScopeSaleRebatePolicyElementDataVo scopeSaleRebatePolicyElementDataVo) {
    List<ScopeSaleRebatePolicyElementVo> list = scopeSaleRebatePolicyElementDataVo.getScopeList();
    //copy list
    List<SaleRebatePolicyScopeInfo> newList = (List<SaleRebatePolicyScopeInfo>) this.nebulaToolkitService
        .copyCollectionByWhiteList(list,
            ScopeSaleRebatePolicyElementVo.class,
            SaleRebatePolicyScopeInfo.class,
            LinkedHashSet.class,
            ArrayList.class);
    newList.forEach(e -> {
      e.setSaleRebatePolicyCode(saleRebatePolicyCode);
      e.setTenantCode(TenantUtils.getTenantCode());
    });
    return newList;
  }
}
