<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyMapper">

  <resultMap type="com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicy" id="SaleRebatePolicyMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="createAccount" column="create_account" jdbcType="VARCHAR"/>
    <result property="createName" column="create_name" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="modifyAccount" column="modify_account" jdbcType="VARCHAR"/>
    <result property="modifyName" column="modify_name" jdbcType="VARCHAR"/>
    <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    <result property="enableStatus" column="enable_status" jdbcType="VARCHAR"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="billType" column="bill_type" jdbcType="INTEGER"/>
    <result property="calculateDayNum" column="calculate_day_num" jdbcType="INTEGER"/>
    <result property="cycleType" column="cycle_type" jdbcType="INTEGER"/>
    <result property="saleRebateEndTime" column="sale_rebate_end_time" jdbcType="TIMESTAMP"/>
    <result property="saleRebatePolicyCode" column="sale_rebate_policy_code" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyCycle" column="sale_rebate_policy_cycle" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyCycleName" column="sale_rebate_policy_cycle_name" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyName" column="sale_rebate_policy_name" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyStatus" column="sale_rebate_policy_status" jdbcType="INTEGER"/>
    <result property="saleRebateStartTime" column="sale_rebate_start_time" jdbcType="TIMESTAMP"/>
    <result property="saleRebateType" column="sale_rebate_type" jdbcType="VARCHAR"/>
    <result property="saleRebateTypeName" column="sale_rebate_type_name" jdbcType="VARCHAR"/>
  </resultMap>
  
  <sql id = "saleRebatePolicy">
    create_account,
    create_name,
    create_time,
    modify_account,
    modify_name,
    modify_time,
    del_flag,
    enable_status,
    remark,
    tenant_code,
    bill_type,
    calculate_day_num,
    cycle_type,
    sale_rebate_end_time,
    sale_rebate_policy_code,
    sale_rebate_policy_cycle,
    sale_rebate_policy_cycle_name,
    sale_rebate_policy_name,
    sale_rebate_policy_status,
    sale_rebate_start_time,
    sale_rebate_type,
    sale_rebate_type_name,
    id
  </sql>

  <select id="findByConditions" resultMap="SaleRebatePolicyMap">
    select
      <include refid="saleRebatePolicy"/>
    from tpm_sale_rebate_policy cm
    where cm.del_flag = '${@<EMAIL>()}'
      <if test="saleRebatePolicy.delFlag != null and saleRebatePolicy.delFlag !=''">
        and cm.del_flag =#{saleRebatePolicy.delFlag}
      </if>
      <if test="saleRebatePolicy.enableStatus != null and saleRebatePolicy.enableStatus !=''">
        and cm.enable_status =#{saleRebatePolicy.enableStatus}
      </if>
      <if test="saleRebatePolicy.tenantCode != null and saleRebatePolicy.tenantCode !=''">
        and cm.tenant_code =#{saleRebatePolicy.tenantCode}
      </if>
      <if test="saleRebatePolicy.saleRebatePolicyCode != null and saleRebatePolicy.saleRebatePolicyCode != ''">
        and cm.sale_rebate_policy_code = #{saleRebatePolicy.saleRebatePolicyCode}
      </if>
      <if test="saleRebatePolicy.saleRebatePolicyName != null and saleRebatePolicy.saleRebatePolicyName != ''">
        <bind name="likeSaleRebatePolicyName" value="'%' + saleRebatePolicy.saleRebatePolicyName + '%'"/>
        and cm.sale_rebate_policy_name like #{likeSaleRebatePolicyName}
      </if>
      <if test="saleRebatePolicy.saleRebateStartTime != null">
        AND cm.sale_rebate_start_time <![CDATA[ >= ]]> #{saleRebatePolicy.saleRebateStartTime}
      </if>
      <if test="saleRebatePolicy.saleRebateEndTime != null">
        AND cm.sale_rebate_end_time <![CDATA[ <= ]]> #{saleRebatePolicy.saleRebateEndTime}
      </if>
    ORDER BY
    cm.create_time desc
  </select>
</mapper>

