package com.biz.crm.tpm.business.rebate.local.repository;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyProductInfo;
import com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyProductInfoMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.List;


/**
 * 返利政策分配商品(SaleRebatePolicyProductInfo)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:58
 */
@Component
public class SaleRebatePolicyProductInfoRepository extends
    ServiceImpl<SaleRebatePolicyProductInfoMapper, SaleRebatePolicyProductInfo> {

  /**
   * 分页查询
   *
   * @param pageable                    分页
   * @param saleRebatePolicyProductInfo
   * @return
   */
  public Page<SaleRebatePolicyProductInfo> findByConditions(Pageable pageable,
      SaleRebatePolicyProductInfo saleRebatePolicyProductInfo) {
    Page<SaleRebatePolicyProductInfo> page = new Page<>(pageable.getPageNumber(),
        pageable.getPageSize());
    //新增租户编号判断
    saleRebatePolicyProductInfo.setTenantCode(TenantUtils.getTenantCode());
    Page<SaleRebatePolicyProductInfo> pageList = this.baseMapper
        .findByConditions(page, saleRebatePolicyProductInfo);
    return pageList;
  }

  /**
   * 通过编码查询
   *
   * @param saleRebatePolicyCode
   * @return
   */
  public List<SaleRebatePolicyProductInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyProductInfo::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .eq(SaleRebatePolicyProductInfo::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }


  /**
   * 通过编码查询
   *
   * @param saleRebatePolicyCodes
   * @return
   */
  public List<SaleRebatePolicyProductInfo> findBySaleRebatePolicyCodes(
      List<String> saleRebatePolicyCodes) {
    return this.lambdaQuery()
        .in(SaleRebatePolicyProductInfo::getSaleRebatePolicyCode, saleRebatePolicyCodes)
        .eq(SaleRebatePolicyProductInfo::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }

  /**
   * 删除
   *
   * @param saleRebatePolicyCode
   * @return
   */
  public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    this.lambdaUpdate()
        .eq(SaleRebatePolicyProductInfo::getTenantCode, TenantUtils.getTenantCode())
        .eq(SaleRebatePolicyProductInfo::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .remove();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(SaleRebatePolicyProductInfo::getTenantCode,tenantCode)
        .in(SaleRebatePolicyProductInfo::getId,ids)
        .remove();
  }
}

