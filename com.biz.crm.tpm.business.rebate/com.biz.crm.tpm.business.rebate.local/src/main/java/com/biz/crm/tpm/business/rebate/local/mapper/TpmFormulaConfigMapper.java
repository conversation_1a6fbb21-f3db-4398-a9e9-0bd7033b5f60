package com.biz.crm.tpm.business.rebate.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaConfigEntity;
import org.apache.ibatis.annotations.Param;

/**
 * @Description 公式配置
 * <AUTHOR>
 * @Date 2024/6/11 20:14
 */
public interface TpmFormulaConfigMapper extends BaseMapper<FormulaConfigEntity> {

    /**
     * 根据条件分页查询
     * @param page
     * @param dto
     * @return
     */
    Page<FormulaConfigEntity> findByConditions(@Param("page") Page<FormulaConfigEntity> page,
                                               @Param("dto") FormulaConfigEntity dto);
}
