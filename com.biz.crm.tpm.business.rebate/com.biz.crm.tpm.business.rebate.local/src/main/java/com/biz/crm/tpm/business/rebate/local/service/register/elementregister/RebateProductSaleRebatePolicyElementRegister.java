package com.biz.crm.tpm.business.rebate.local.service.register.elementregister;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyProductInfo;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyProductInfoService;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.RebateProductSaleRebatePolicyElementDataVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.RebateProductSaleRebatePolicyElementVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @describe: 返利政策返利产品要素注册器实现
 * @createTime 2022年02月18日 16:25:00
 */
@Slf4j
@Service("rebateProductSaleRebatePolicyElementRegister")
public class RebateProductSaleRebatePolicyElementRegister implements
    SaleRebatePolicyElementRegister<RebateProductSaleRebatePolicyElementDataVo> {

  @Autowired(required = false)
  private SaleRebatePolicyProductInfoService saleRebatePolicyProductInfoService;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 返利政策要素名称
   */
  private static final String REBATE_POLICY_ELEMENT_NAME = "返利商品";

  /**
   * 返利政策要素编码
   */
  private static final String REBATE_POLICY_ELEMENT_CODE = "rebate_product";

  /**
   * 排序
   */
  private static final Integer REBATE_POLICY_ELEMENT_SORT = 2;

  @Override
  public String getSaleRebatePolicyElementName() {
    return REBATE_POLICY_ELEMENT_NAME;
  }

  @Override
  public String getSaleRebatePolicyElementCode() {
    return REBATE_POLICY_ELEMENT_CODE;
  }

  @Override
  public Integer getElementSort() {
    return REBATE_POLICY_ELEMENT_SORT;
  }

  @Override
  public Class<RebateProductSaleRebatePolicyElementDataVo> getSaleRebatePolicyElementClass() {
    return RebateProductSaleRebatePolicyElementDataVo.class;
  }

  @Override
  public RebateProductSaleRebatePolicyElementDataVo getBySaleRebatePolicyCode(
      String saleRebatePolicyCode) {
    RebateProductSaleRebatePolicyElementDataVo vo = new RebateProductSaleRebatePolicyElementDataVo();
    List<SaleRebatePolicyProductInfo> list = this.saleRebatePolicyProductInfoService
        .findBySaleRebatePolicyCode(saleRebatePolicyCode);
    if (CollectionUtils.isEmpty(list)) {
      return vo;
    }
    this.getDataVoByList(list, vo);
    return vo;
  }


  @Override
  public RebateProductSaleRebatePolicyElementDataVo onRequestSaleRebatePolicyCreate(
      String saleRebatePolicyCode,
      RebateProductSaleRebatePolicyElementDataVo rebateProductSaleRebatePolicyElementDataVo) {
    List<SaleRebatePolicyProductInfo> list = this
        .getListByDataVo(saleRebatePolicyCode, rebateProductSaleRebatePolicyElementDataVo);
    this.saleRebatePolicyProductInfoService.createBatch(list);
    RebateProductSaleRebatePolicyElementDataVo vo = new RebateProductSaleRebatePolicyElementDataVo();
    this.getDataVoByList(list, vo);
    vo.setSaleRebatePolicyCode(saleRebatePolicyCode);
    return vo;
  }


  @Override
  public RebateProductSaleRebatePolicyElementDataVo onRequestSaleRebatePolicyUpdate(
      String saleRebatePolicyCode,
      RebateProductSaleRebatePolicyElementDataVo rebateProductSaleRebatePolicyElementDataVo) {
    this.saleRebatePolicyProductInfoService.deleteBySaleRebatePolicyCode(saleRebatePolicyCode);
    List<SaleRebatePolicyProductInfo> list = this
        .getListByDataVo(saleRebatePolicyCode, rebateProductSaleRebatePolicyElementDataVo);
    this.saleRebatePolicyProductInfoService.createBatch(list);
    RebateProductSaleRebatePolicyElementDataVo vo = new RebateProductSaleRebatePolicyElementDataVo();
    this.getDataVoByList(list, vo);
    vo.setSaleRebatePolicyCode(saleRebatePolicyCode);
    return vo;
  }

  /**
   * 将实体LIST封装成要素DataVo
   *
   * @param list
   * @param vo
   */
  private void getDataVoByList(List<SaleRebatePolicyProductInfo> list,
      RebateProductSaleRebatePolicyElementDataVo vo) {
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    List<RebateProductSaleRebatePolicyElementVo> newList = (List<RebateProductSaleRebatePolicyElementVo>) this.nebulaToolkitService
        .copyCollectionByWhiteList(list, SaleRebatePolicyProductInfo.class,
            RebateProductSaleRebatePolicyElementVo.class,
            LinkedHashSet.class,
            ArrayList.class);
    vo.setRebateProductList(newList);
    vo.setSaleRebatePolicyCode(list.get(0).getSaleRebatePolicyCode());
    vo.setTenantCode(TenantUtils.getTenantCode());
  }

  /**
   * 从要素封装vo里提取实体List
   *
   * @param rebateProductSaleRebatePolicyElementDataVo
   * @return
   */
  private List<SaleRebatePolicyProductInfo> getListByDataVo(String saleRebatePolicyCode,
      RebateProductSaleRebatePolicyElementDataVo rebateProductSaleRebatePolicyElementDataVo) {
    List<RebateProductSaleRebatePolicyElementVo> list = rebateProductSaleRebatePolicyElementDataVo
        .getRebateProductList();
    //copy list
    List<SaleRebatePolicyProductInfo> newList = (List<SaleRebatePolicyProductInfo>) this.nebulaToolkitService
        .copyCollectionByWhiteList(list,
            RebateProductSaleRebatePolicyElementVo.class,
            SaleRebatePolicyProductInfo.class,
            LinkedHashSet.class,
            ArrayList.class);
    newList.forEach(e -> {
      e.setSaleRebatePolicyCode(saleRebatePolicyCode);
      e.setTenantCode(TenantUtils.getTenantCode());
    });
    return newList;


  }
}
