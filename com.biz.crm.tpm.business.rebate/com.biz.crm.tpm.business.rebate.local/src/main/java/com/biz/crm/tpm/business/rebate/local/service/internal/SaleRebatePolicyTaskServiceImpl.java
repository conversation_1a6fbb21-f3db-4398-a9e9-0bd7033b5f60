package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.rebate.local.constant.SalePolicyConstant;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyTaskService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyCalculateService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateRegisterService;
import com.biz.crm.tpm.business.rebate.local.utils.SaleRebateCycleUtil;
import com.biz.crm.tpm.business.rebate.sdk.constant.SaleRebateCycleConstant;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebateCronDto;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.enums.CycleTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateCronVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyRegisterVo;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearDetailDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.service.FiscalYearVoService;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearRebateVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.task.service.DynamicTaskSchedulerVoService;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.quartz.TriggerUtils;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: 返利定时任务 service
 * @author: lifei
 * @date: 2022/2/22 11:16
 */
@Service
@Slf4j
public class SaleRebatePolicyTaskServiceImpl implements SaleRebatePolicyTaskService {

  @Autowired(required = false)
  private DynamicTaskSchedulerVoService dynamicTaskSchedulerVoService;
  @Autowired(required = false)
  private SaleRebatePolicyCalculateService saleRebatePolicycalculateService;
  @Autowired(required = false)
  private SaleRebateRegisterService saleRebateRegisterService;
  @Autowired(required = false)
  private FiscalYearVoService fiscalYearVoService;


  /**
   * @param saleRebatePolicyCode
   * <AUTHOR>
   * @date
   */
  @Override
  @Transactional
  public void handleRebateTask(String saleRebatePolicyCode) {
    this.saleRebatePolicycalculateService.onCalculateByCode(
        Collections.singletonList(saleRebatePolicyCode), BooleanEnum.FALSE.getNumStr(),new Date());
  }

  @Override
  @Transactional
  public void createRebatetask(SaleRebatePolicyDto saleRebatePolicyDto) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
    calendar.set(Calendar.DAY_OF_YEAR,
        calendar.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum());
    Date validityTime = SaleRebateCycleUtil.getLastday(calendar.getTime());
    dynamicTaskSchedulerVoService.createIgnorePrefix(
        saleRebatePolicyDto.getSaleRebatePolicyCode(),
        SalePolicyConstant.TASK_INVOKE_BEAN_NAME,
        SalePolicyConstant.TASK_METHOD,
        2,
        SaleRebateCycleUtil.createCronExpression(),
        null,
        validityTime,
        saleRebatePolicyDto.getSaleRebatePolicyName() + "定时任务",
        saleRebatePolicyDto.getSaleRebatePolicyCode(), TenantUtils.getTenantCode(),TenantUtils.getTenantCode());
  }

  /**
   * 预测未来即将返利的周期时间表
   * 默认展示条数最大值:20
   *
   * @param dto
   * @return
   */
  @Override
  public List<SaleRebateCronVo> predictionSaleRebateTimes(SaleRebateCronDto dto) throws ParseException {
    //校验数据
    this.validateData(dto);
    //寻找当前周期的基础表达式
    SaleRebatePolicyRegisterVo vo = this.queryCronExpression(dto);
    String cronExpression = vo.getCronExpression();
    boolean checkFiscalYear = vo.isCheckFiscalYear();
    //根据获取的表达式判断 如果为空则没有周期 直接展示结束日期
    List<SaleRebateCronVo> resList = Lists.newArrayList();
    List<String> cronList = new ArrayList<>();
    if (StringUtils.isNotEmpty(cronExpression)) {
      Date now = new Date();
      int times = SaleRebateCycleConstant.predictionTimes;
      String startTimeStr = dto.getSaleRebateStartTime().concat(SaleRebateCycleConstant.startTime);
      String endTimeStr = dto.getSaleRebateEndTime().concat(SaleRebateCycleConstant.endTime);
      Date startTime = SaleRebateCycleConstant.dateFormat.parse(startTimeStr);
      Date endTime = SaleRebateCycleConstant.dateFormat.parse(endTimeStr);
      if (now.compareTo(startTime) >= 0 && now.compareTo(endTime) <= 0) {
        startTime = now;
      }

      List<String> recentExecTimeList = getRecentExecTime(cronExpression, times, startTime, endTime);

      Map<String, Long> fiscalYearMap = new HashMap<>();
      Map<String, String> fiscalYearDetailMap = new HashMap<>();
      // 区分财年 CycleTypeEnum，默认使用自然年
      if (StringUtils.equals(CycleTypeEnum.ENTERPRISE_FISCAL_YEAR.getDictCode(), dto.getCycleType())) {
        List<String> fiscalList = new ArrayList<>();
        //去MDM拉取财年信息 计算出每年的偏移量 Map<年, 偏移量>
        List<FiscalYearRebateVo> fiscalYearRebateVoList = this.fiscalYearVoService.findFiscalYearRebate();
        if (ObjectUtils.isNotEmpty(fiscalYearRebateVoList)) {
          //计算自然年与财年的起始时间的差量
          for (FiscalYearRebateVo item : fiscalYearRebateVoList) {
            String yearStartDay = item.getYear().concat(SaleRebateCycleConstant.yearStartDay);
            LocalDate naturalYearFirstDay = LocalDate.parse(yearStartDay);
            LocalDate fiscalYearFirstDay = LocalDate.parse(SaleRebateCycleConstant.dayFormat.format(item.getBeginTime()));
            long between = ChronoUnit.DAYS.between(naturalYearFirstDay, fiscalYearFirstDay);
            fiscalYearMap.put(item.getYear(), between);
            //计算出财年中每个月的日期
            for (FiscalYearDetailDto detailVo : item.getFiscalYearDetailDtoList()) {
              Calendar tempCalendar = Calendar.getInstance();
              tempCalendar.setTime(detailVo.getEndTime());
              tempCalendar.add(Calendar.DATE, 1);
              fiscalYearDetailMap.put(SaleRebateCycleConstant.monthFormat.format(detailVo.getEndTime()), SaleRebateCycleConstant.dateFormat.format(tempCalendar.getTime()));
            }
          }
          //判断当前注册器是否需要财年偏移量
          if (checkFiscalYear) {
            if (vo.getIndexCode() == 2){
              //只处理企业财年且返利周期为月度返利的情况
             if (recentExecTimeList.size() > 0){
               String lastTime = recentExecTimeList.get(recentExecTimeList.size() - 1);
               Date lastDate = SaleRebateCycleConstant.dayFormat.parse(lastTime);
               String endDate = SaleRebateCycleConstant.dayFormat.format(endTime);
               if (endDate.compareTo(lastTime) > 0){
                 //政策截止时间大于el日期表达式计算结果最大日期 自动补充一月 补充应工具造成周期表达式时间缺失
                 Calendar calendar =Calendar.getInstance();
                 calendar.setTime(lastDate);
                 calendar.add(Calendar.MONTH,1);
                 recentExecTimeList.add(SaleRebateCycleConstant.dateFormat.format(calendar.getTime()));
               }
             }
            }
            recentExecTimeList.forEach(
                item -> {
                  try {
                    Integer num = 0;
                    // 判断当前注册器是否需要财年偏移量
                    Calendar ca = Calendar.getInstance();
                    ca.setTime(SaleRebateCycleConstant.dateFormat.parse(item));
                    // 减去 周期覆盖范围
                    ca.add(vo.getCycleType(), -(vo.getCycleNum()));
                    String currentYear = String.valueOf(ca.get(Calendar.YEAR));
                    if (fiscalYearMap.containsKey(currentYear)) {
                      num = num + fiscalYearMap.get(currentYear).intValue();
                    }
                    if (vo.getIndexCode()==2 && dto.getCycleType().equals("2") && num == 0){
                      Set<String> fiscalYear = fiscalYearMap.keySet();
                      List<String> fiscalYears = new ArrayList<>(fiscalYear);
                      Collections.sort(fiscalYears);//正序
                      Collections.reverse(fiscalYears);//翻转
                      Iterator<String> iterator = fiscalYear.iterator();
                      if (iterator.hasNext()){
                        String year = iterator.next();
                        num = fiscalYearMap.get(year).intValue();
                      }
                    }
                    if (vo.getIndexCode() == 2 && dto.getCycleType().equals("2")){
                      fiscalList.add(this.plusDay(SaleRebateCycleConstant.dateFormat.format(ca.getTime()), num));
                    }else {
                      fiscalList.add(this.plusDay(item, num));
                    }
                  } catch (ParseException e) {
                    e.printStackTrace();
                  }
                });
            //清洗节点时间
            recentExecTimeList = new ArrayList<>();
            for (int i = 0; i < fiscalList.size(); i++) {
              String currentStr = SaleRebateCycleConstant.monthFormat.format(SaleRebateCycleConstant.dateFormat.parse(fiscalList.get(i)));//年月
              if (fiscalYearDetailMap.containsKey(currentStr)) {
                recentExecTimeList.add(fiscalYearDetailMap.get(currentStr));
              } else {
                if (vo.getIndexCode() == 2 && dto.getCycleType().equals("2")) {
                  // 企业财年  月度返利单独处理 当财年处理数据为空时 以所有财年中 相近年份的 同期数据为基础数据
                  int num = 1;
                  String fiscalFormat =
                      this.getLastYearMonth(fiscalYearDetailMap, fiscalList.get(i), num);
                  if (StringUtils.isNotBlank(fiscalFormat)) {
                    recentExecTimeList.add(fiscalFormat);
                  }
                } else {
                  recentExecTimeList.add(fiscalList.get(i));
                }
              }
            }
          }
        }
      }


      //正常返利周期偏移量
      recentExecTimeList.forEach(item -> {
        try {
          Integer num = Integer.parseInt(dto.getDelayNumber());
          cronList.add(this.plusDay(item, num));
        } catch (ParseException e) {
          e.printStackTrace();
        }
      });
    }
    //如果没有返回值 则表明基本的开始时间都大于结束时间 则取结束时间
    if (cronList.size() < 20){
      if (vo.getIndexCode() == 2 && dto.getCycleType().equals("2") && cronList.size() > 0){
        Collections.sort(cronList);
        String lastTime = cronList.get(cronList.size()-1);
        if (lastTime.compareTo(dto.getSaleRebateEndTime()) < 0) {
          String tempEndTime = dto.getSaleRebateEndTime().concat(SaleRebateCycleConstant.currentStartTime);
          cronList.add(this.plusDay(tempEndTime, 1 + Integer.parseInt(dto.getDelayNumber())));
        }
    }else {
        String tempEndTime = dto.getSaleRebateEndTime().concat(SaleRebateCycleConstant.currentStartTime);
        cronList.add(this.plusDay(tempEndTime, 1 + Integer.parseInt(dto.getDelayNumber())));
    }
}
    /**
     * 根据周期类型 往前推开始时间和结束时间
     * 开始时间 重新构造开始时间
     * 结束时间 返利的时间 -1
     */
    if(StringUtils.isEmpty(cronExpression)){
      //一次性返利
      for(String item : cronList){
        SaleRebateCronVo one = new SaleRebateCronVo();
        one.setSaleRebateStartTime(dto.getSaleRebateStartTime().concat(SaleRebateCycleConstant.currentStartTime));
        one.setSaleRebateEndTime(dto.getSaleRebateEndTime().concat(SaleRebateCycleConstant.endTime));
        one.setSaleRebatePointTime(item);
        resList.add(one);
      }
    }else {
      resList = rollbackDay(cronList, Integer.parseInt(dto.getDelayNumber()), vo.getCycleType(), vo.getCycleNum(), dto);
    }
    return resList;
  }

  /**
   * 递归处理数据
   * @param fiscalYearDetailMap
   * @param fiscal
   * @param num
   * @return
   * @throws ParseException
   */
  private String getLastYearMonth(Map<String, String> fiscalYearDetailMap,
      String fiscal, int num) throws ParseException {
    Date nowDate = SaleRebateCycleConstant.dayFormat.parse(fiscal);
    Calendar calendar =Calendar.getInstance();
    calendar.setTime(nowDate);
    calendar.add(Calendar.YEAR,-num);
    String format = SaleRebateCycleConstant.monthFormat.format(calendar.getTime());
    String lastYearMonth = fiscalYearDetailMap.get(format);
    if (StringUtils.isNotBlank(lastYearMonth)){
      Date parse = SaleRebateCycleConstant.dateFormat.parse(lastYearMonth);
      Calendar calendar1 = Calendar.getInstance();
      calendar1.setTime(parse);
      calendar1.add(Calendar.YEAR,num);
      return SaleRebateCycleConstant.dateFormat.format(calendar1.getTime());
    }else {
      num++;
      Set<String> keySet = fiscalYearDetailMap.keySet();
      if (num <= keySet.size()){
        return this.getLastYearMonth(fiscalYearDetailMap,fiscal,num);
      }
      return null;
    }
  }

  /**
   * 根据条件创建定时任务表达式
   *
   * @param dto
   * @return
   */
  private SaleRebatePolicyRegisterVo queryCronExpression(SaleRebateCronDto dto) {
    List<SaleRebatePolicyRegisterVo> cronList = saleRebateRegisterService.findForRebateCycle();
    List<SaleRebatePolicyRegisterVo> currentRegisterList = cronList.stream().filter(k -> StringUtils.equals(dto.getCycleCode(), k.getCode())).collect(Collectors.toList());
    Validate.isTrue(currentRegisterList.size() == 1, "当前不存在返利周期或匹配多个注册器!");
    return currentRegisterList.get(0);
  }

  /**
   * 给指定日期添加指定添加的天数 返回添加后的天数
   *
   * @param date 指定的日期
   * @param num  指定添加的天数
   * @return 添加后的天数
   */
  public String plusDay(String date, int num) throws ParseException {
    Date currentDate = SaleRebateCycleConstant.dateFormat.parse(date);
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(currentDate);
    // num为增加的天数，可以为负数
    calendar.add(Calendar.DATE, num);
    currentDate = calendar.getTime();
    String res = SaleRebateCycleConstant.dateFormat.format(currentDate);
    log.info("日期偏移后: {}", res);
    return res;
  }

  /**
   * @param cronList     计算出的周期时间
   * @param num          正常偏移量
   * @param calendarType 注册器类型
   * @param cycleNum     注册器的偏移量
   * @param dto          入参
   * @return
   * @throws ParseException
   */
  public List<SaleRebateCronVo> rollbackDay(List<String> cronList, int num, int calendarType, int cycleNum, SaleRebateCronDto dto) throws ParseException {
    List<SaleRebateCronVo> resList = Lists.newArrayList();
    Collections.sort(cronList);
    for (int i = 0; i < cronList.size(); i++) {
      if (0 == i) {
        String item = cronList.get(i);
        SaleRebateCronVo one = new SaleRebateCronVo();
        Date currentDate = SaleRebateCycleConstant.dateFormat.parse(item);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        //减去偏移量
        calendar.add(Calendar.DATE, (-num));
        //计算开始时间
        calendar.add(calendarType, -cycleNum);
        //获取计算出的偏移量时间 转换格式
        Date currentTime = SaleRebateCycleConstant.dayFormat.parse(SaleRebateCycleConstant.dayFormat.format(calendar.getTime()));
        //判断是否具备开始时间
        Date startDate = SaleRebateCycleConstant.dayFormat.parse(dto.getSaleRebateStartTime());
        if (currentTime.compareTo(startDate) <= 0) {
          currentTime = startDate;
        }
        //计算结束时间
        calendar.add(calendarType, cycleNum);
        calendar.add(Calendar.DATE, -1);
        Date endTime = SaleRebateCycleConstant.dayFormat.parse(SaleRebateCycleConstant.dayFormat.format(calendar.getTime()));
        Date endDate = SaleRebateCycleConstant.dayFormat.parse(dto.getSaleRebateEndTime());
        if (endTime.compareTo(endDate) > 0) {
          endTime = endDate;
        }
        else if (cronList.size() <= 1 && calendarType == 2){
          endTime = endDate;
        }
        one.setSaleRebateStartTime(SaleRebateCycleConstant.dayFormat.format(currentTime).concat(SaleRebateCycleConstant.currentStartTime));
        one.setSaleRebateEndTime(SaleRebateCycleConstant.dayFormat.format(endTime).concat(SaleRebateCycleConstant.endTime));
        Date parse = SaleRebateCycleConstant.dayFormat.parse(item);
        String saleRebatePointTime = SaleRebateCycleConstant.dayFormat.format(parse)
            .concat(SaleRebateCycleConstant.currentStartTime);
        //处理企业财年不足月 不足年数据
        String theOneEndTime = SaleRebateCycleConstant.dayFormat.format(endTime)
            .concat(SaleRebateCycleConstant.currentStartTime);
        Date theOneEndDate = SaleRebateCycleConstant.dayFormat.parse(theOneEndTime);
        Calendar calendars = Calendar.getInstance();
        calendars.setTime(theOneEndDate);
        calendars.add(Calendar.DATE,num+1);
        String format = SaleRebateCycleConstant.dayFormat.format(calendars.getTime()).concat(SaleRebateCycleConstant.currentStartTime);
        if (format.compareTo(saleRebatePointTime) <=0){
          saleRebatePointTime = format;
        }
        one.setSaleRebatePointTime(saleRebatePointTime);
        resList.add(one);
      } else {
        SaleRebateCronVo one = new SaleRebateCronVo();
        //获取上一个节点的信息
        SaleRebateCronVo vo = resList.get(i - 1);
        Date lastStartTime = SaleRebateCycleConstant.dayFormat.parse(vo.getSaleRebateStartTime());
        Date lastEndTime = SaleRebateCycleConstant.dayFormat.parse(vo.getSaleRebateEndTime());
        //开始时间 = 上一个结束时间 day+1 拼接 00:00:00
        Calendar ca = Calendar.getInstance();
        ca.setTime(lastEndTime);
        ca.add(Calendar.DATE, 1);
        one.setSaleRebateStartTime(SaleRebateCycleConstant.dateFormat.format(ca.getTime()));
        //结束时间 = 改节点的 day-1 拼接23:59::59
        Date endTime = SaleRebateCycleConstant.dateFormat.parse(cronList.get(i));
        ca.setTime(endTime);
        ca.add(Calendar.DATE,(-1-num));
        String format = SaleRebateCycleConstant.dateFormat.format(ca.getTime());
        //减去偏移量后再比较
        if (format.compareTo(dto.getSaleRebateEndTime()) >= 0){
          //cron表达式的结束日期大于返利结束日期的 取返利结束的日期为准
          Calendar calendar =  Calendar.getInstance();
          one.setSaleRebateEndTime(dto.getSaleRebateEndTime().concat(SaleRebateCycleConstant.endTime));
          if (dto.getSaleRebateEndTime().concat(SaleRebateCycleConstant.endTime).compareTo(vo.getSaleRebateEndTime()) == 0){
              return resList;
          }
          calendar.setTime(SaleRebateCycleConstant.dateFormat.parse(dto.getSaleRebateEndTime().concat(SaleRebateCycleConstant.currentStartTime)));
          calendar.add(Calendar.DATE,(1+num));
          one.setSaleRebatePointTime(SaleRebateCycleConstant.dayFormat.format(calendar.getTime()).concat(SaleRebateCycleConstant.currentStartTime));
          resList.add(one);
          return resList;
        }else {
          Calendar calendar = Calendar.getInstance();
          calendar.setTime(SaleRebateCycleConstant.dateFormat.parse(cronList.get(i)));
          calendar.add(Calendar.DATE, (-1-num));
          one.setSaleRebateEndTime(SaleRebateCycleConstant.dayFormat.format(calendar.getTime()).concat(SaleRebateCycleConstant.endTime));
          Date parse = SaleRebateCycleConstant.dayFormat.parse(cronList.get(i));
          String saleRebatePointTime = SaleRebateCycleConstant.dayFormat.format(parse)
              .concat(SaleRebateCycleConstant.currentStartTime);
          one.setSaleRebatePointTime(saleRebatePointTime);
          resList.add(one);
        }
      }
    }
    return resList;
  }


  /**
   * @param cronExpression cron表达式
   * @param numTimes       下一(几)次运行的时间
   * @return
   */
  public static List<String> getRecentExecTime(String cronExpression, Integer numTimes, Date startTime, Date endTime) {
    List<String> list = new ArrayList<>();
    try {
      CronTriggerImpl cronTrigger = new CronTriggerImpl();
      cronTrigger.setCronExpression(cronExpression);
      // 这个是重点，一行代码搞定
      List<Date> dates = TriggerUtils.computeFireTimesBetween(cronTrigger, null, startTime, endTime);
      for (int i = 0; i < dates.size(); i++) {
        // 这个是提示的日期个数
        if (i < numTimes) {
          list.add(SaleRebateCycleConstant.dateFormat.format(dates.get(i)));
        } else {
          break;
        }
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
    return list;
  }

  /**
   * 创建定时任务（一次性）
   *
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  @Override
  @Transactional
  public void createRebateSingletask(SaleRebatePolicyDto saleRebatePolicyDto) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
    calendar.set(Calendar.DAY_OF_YEAR,
        calendar.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum());
    //执行日期
    Date firstday = SaleRebateCycleUtil.getFirstday(calendar.getTime());
    dynamicTaskSchedulerVoService.createIgnorePrefix(
        saleRebatePolicyDto.getSaleRebatePolicyCode(),
        SalePolicyConstant.TASK_INVOKE_BEAN_NAME,
        SalePolicyConstant.TASK_METHOD,
        1,
        null,
        firstday,
        SaleRebateCycleUtil.getLastday(firstday),
        saleRebatePolicyDto.getSaleRebatePolicyName() + "一次性定时任务",
        saleRebatePolicyDto.getSaleRebatePolicyCode(), TenantUtils.getTenantCode(),TenantUtils.getTenantCode());
  }

  /**
   * 编辑定时任务
   *
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  @Override
  @Transactional
  public void updateRebatetask(SaleRebatePolicyDto saleRebatePolicyDto) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
    calendar.set(Calendar.DAY_OF_YEAR,
        calendar.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum());
    Date validityTime = SaleRebateCycleUtil.getLastday(calendar.getTime());
    dynamicTaskSchedulerVoService.updateIgnorePrefix(
        saleRebatePolicyDto.getSaleRebatePolicyCode(),
        SaleRebateCycleUtil.createCronExpression(),
        validityTime,
        saleRebatePolicyDto.getSaleRebatePolicyName() + "定时任务",
        saleRebatePolicyDto.getSaleRebatePolicyCode(),TenantUtils.getTenantCode(),TenantUtils.getTenantCode());
  }

  /**
   * 根据无效化计算定时任务
   *
   * @param taskCodes
   */
  @Override
  @Transactional
  public void invalidBatchBySaleRebatePolicyCodes(List<String> taskCodes) {
    dynamicTaskSchedulerVoService.invalid(taskCodes.stream().toArray(String[]::new));
  }

  /**
   * 根据有效化计算定时任务
   *
   * @param taskCodes
   */
  @Override
  @Transactional
  public void effectiveBatchBySaleRebatePolicyCodes(List<String> taskCodes) {
    dynamicTaskSchedulerVoService.effective(taskCodes.stream().toArray(String[]::new));
  }

  /**
   * 根据删除计算定时任务
   *
   * @param taskCodes
   */
  @Override
  @Transactional
  public void deleteBatchBySaleRebatePolicyCodes(List<String> taskCodes) {
    dynamicTaskSchedulerVoService.invalid(taskCodes.stream().toArray(String[]::new));
    dynamicTaskSchedulerVoService.deleteByTaskcodes(taskCodes.stream().toArray(String[]::new));
  }

  private void validateData(SaleRebateCronDto dto) {
    Validate.isTrue(StringUtils.isNotEmpty(dto.getCycleCode()), "请选择返利周期!");
    Validate.isTrue(StringUtils.isNotEmpty(dto.getDelayNumber()), "请选择返利周期推迟量!");
    Validate.isTrue(StringUtils.isNotEmpty(dto.getCycleType()), "请选择返利周期类型!");
    Validate.isTrue(ObjectUtils.isNotEmpty(dto.getSaleRebateStartTime()), "请选择返利开始时间!");
    Validate.isTrue(ObjectUtils.isNotEmpty(dto.getSaleRebateEndTime()), "请选择返利结束时间!");
  }
}



