package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateAdjustDetail;
import com.biz.crm.tpm.business.rebate.local.repository.SaleRebateAdjustDetailRepository;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateAdjustDetailService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * @description: 返利调整明细service
 * @author: lifei
 * @date: 2022/4/11 14:44
 */
@Service
@Slf4j
public class SaleRebateAdjustDetailServiceImpl implements SaleRebateAdjustDetailService {


  @Autowired(required = false)
  private SaleRebateAdjustDetailRepository saleRebateAdjustDetailRepository;

  @Override
  public Page<SaleRebateAdjustDetail> findByConditions(Pageable pageable,
                                                       SaleRebateAdjustDetail saleRebateAdjustDetail) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(saleRebateAdjustDetail)) {
      saleRebateAdjustDetail = new SaleRebateAdjustDetail();
    }
    return this.saleRebateAdjustDetailRepository.findByConditions(pageable, saleRebateAdjustDetail);
  }

  @Override
  public List<SaleRebateAdjustDetail> findBySaleRebateDetailCode(String saleRebateDetailCode) {
    if(StringUtils.isBlank(saleRebateDetailCode)){
      return null;
    }
    return this.saleRebateAdjustDetailRepository.findBySaleRebateDetailCode(saleRebateDetailCode);
  }

  @Override
  public SaleRebateAdjustDetail create(SaleRebateAdjustDetail saleRebateAdjustDetail) {
    Validate.notNull(saleRebateAdjustDetail,"返利调整明细为空！");
    Validate.notBlank(saleRebateAdjustDetail.getCustomerCode(),"返利客户编码为空！");
    Validate.notBlank(saleRebateAdjustDetail.getCustomerName(),"返利客户名称为空！");
    Validate.notNull(saleRebateAdjustDetail.getAdjustAmount(),"返利调整金额为空！");
    Validate.notNull(saleRebateAdjustDetail.getSaleRebateDetailCode(),"返利明细编码为空！");
    Validate.notNull(saleRebateAdjustDetail.getSaleRebatePolicyName(),"返利名称为空！");
    saleRebateAdjustDetail.setTenantCode(TenantUtils.getTenantCode());
    this.saleRebateAdjustDetailRepository.save(saleRebateAdjustDetail);
    return saleRebateAdjustDetail;
  }
}
