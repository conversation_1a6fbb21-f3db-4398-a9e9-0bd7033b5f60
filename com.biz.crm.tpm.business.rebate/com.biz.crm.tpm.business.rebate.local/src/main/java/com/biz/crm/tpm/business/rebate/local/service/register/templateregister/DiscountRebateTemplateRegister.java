package com.biz.crm.tpm.business.rebate.local.service.register.templateregister;

import com.biz.crm.dms.business.costpool.discount.sdk.dto.CostPoolDiscountDto;
import com.biz.crm.dms.business.costpool.discount.sdk.enums.*;
import com.biz.crm.dms.business.costpool.discount.sdk.strategy.OperationTypeStrategy;
import com.biz.crm.tpm.business.rebate.local.service.register.elementregister.CheckProductSaleRebatePolicyElementRegisterImpl;
import com.biz.crm.tpm.business.rebate.local.service.register.elementregister.FormulaSaleRebatePolicyElementRegisterImpl;
import com.biz.crm.tpm.business.rebate.local.service.register.elementregister.ScopeSaleRebatePolicyElementRegisterImpl;
import com.biz.crm.tpm.business.rebate.local.strategy.ChannelForSaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.local.strategy.DistributorForSaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.local.strategy.OrgForSaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.local.utils.SaleRebatePolicyCalculateUtil;
import com.biz.crm.tpm.business.rebate.sdk.enums.AllocationTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyTemplateRegister;
import com.biz.crm.tpm.business.rebate.sdk.strategy.SaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.sdk.vo.*;
import com.biz.crm.tpm.business.rebate.sdk.vo.scope.AbstractSaleRebatePolicyCustomerInfo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 折扣模板注册实现
 * @author: lifei
 * @date: 2022/2/21 10:13
 */
//@Component
//@Slf4j
//public class DiscountRebateTemplateRegister implements SaleRebatePolicyTemplateRegister {

//  @Autowired(required = false)
//  private CustomerVoService customerVoService;
//  @Autowired(required = false)
//  private NebulaToolkitService nebulaToolkitService;
//  @Autowired(required = false)
//  private List<SaleRebatePolicyCriterionRegister> saleRebatePolicyCriterionRegisters;
//  @Autowired(required = false)
//  @Qualifier("accountOperationTypeStrategyImpl")
//  private OperationTypeStrategy operationTypeStrategy;
//
//  /**
//   * 返利政策要素名称
//   */
//  private static final String REBATE_POLICY_TEMPLATE_NAME = "折 扣";
//
//  /**
//   * 返利政策要素编码
//   */
//  private static final String REBATE_POLICY_TEMPLATE_CODE = "discountRebateTemplate";
//
//  /**
//   * 排序
//   */
//  private static final Integer REBATE_POLICY_TEMPLATE_SORT = 2;
//
//
//  @Override
//  public String getSaleRebatePolicytemplateCode() {
//    return REBATE_POLICY_TEMPLATE_CODE;
//  }
//
//  @Override
//  public String getSaleRebatePolicytemplateName() {
//    return REBATE_POLICY_TEMPLATE_NAME;
//  }
//
//  /**
//   * 拿到模板排序列表排序
//   */
//  @Override
//  public Integer getTemplateSort() {
//    return REBATE_POLICY_TEMPLATE_SORT;
//  }
//
//  /**
//   * 返回该返利政策可以使用的客户范围选择方式(注意，是可用的，不是具体优惠实例正在使用的)
//   */
//  @Override
//  public Collection<Class<? extends SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo>>> getCustomerScopeStrategyClasses() {
//    return Sets.newHashSet(DistributorForSaleRebateCustomerScopeStrategy.class,
//        ChannelForSaleRebateCustomerScopeStrategy.class,
//        OrgForSaleRebateCustomerScopeStrategy.class);
//  }
//
//  /**
//   * 可用的要素
//   *
//   * <AUTHOR>
//   * @date
//   */
//  @Override
//  public Collection<Class<? extends SaleRebatePolicyElementRegister>> getSaleRebateElementClasses() {
//    return Sets.newHashSet(CheckProductSaleRebatePolicyElementRegisterImpl.class,
//        FormulaSaleRebatePolicyElementRegisterImpl.class
//            , ScopeSaleRebatePolicyElementRegisterImpl.class
//                          );
//  }
//
//  /**
//   * 构建最小粒度参数 返利明细
//   *
//   * @param saleRebateComputeBuildParamVos 构建最小粒度参数vo
//   * <AUTHOR>
//   * @date
//   */
//  @Override
//  public List<SaleRebateComputeParamVo> onbuildParam(
//      List<SaleRebateComputeBuildParamVo> saleRebateComputeBuildParamVos) {
//    if (CollectionUtils.isNotEmpty(saleRebateComputeBuildParamVos)) {
//      return this
//          .buildParam(saleRebateComputeBuildParamVos);
//    }
//    return null;
//  }
//
//  @Override
//  public void onAccount(List<SaleRebatePolicyDetailVo> saleRebatePolicyDetailVos) {
//    if (CollectionUtils.isNotEmpty(saleRebatePolicyDetailVos)) {
//      saleRebatePolicyDetailVos.forEach(saleRebatePolicyDetailVo -> {
//        CostPoolDiscountDto costPoolDiscountDto = new CostPoolDiscountDto();
//        costPoolDiscountDto.setUseType(PoolUseTypeEnum.DEFAULT.getDictCode());
//        costPoolDiscountDto.setPoolType(PoolTypeEnum.DISCOUNT.getDictCode());
//        costPoolDiscountDto.setPoolGroup(PoolGroupEnum.DEFAULT.getDictCode());
//        costPoolDiscountDto.setCustomerCode(saleRebatePolicyDetailVo.getCustomerCode());
//        costPoolDiscountDto.setCustomerName(saleRebatePolicyDetailVo.getCustomerName());
//        costPoolDiscountDto.setAmount(saleRebatePolicyDetailVo.getActualRebateAmount());
//        costPoolDiscountDto.setOperationType(PoolOperationTypeEnum.REBATE_ACCOUNT.getDictCode());
//        costPoolDiscountDto.setPayType(PoolPayTypeEnum.DISCOUNT.getDictCode());
//        costPoolDiscountDto.setFromCode(saleRebatePolicyDetailVo.getSaleRebateDetailCode());
//        costPoolDiscountDto.setFromDesc(PoolOperationTypeEnum.REBATE_ACCOUNT.getValue());
//        operationTypeStrategy.onSaveDiscountInfos(costPoolDiscountDto);
//      });
//    }
//  }
//
//
//  /**
//   * 返利政策构建最小维度明细
//   *
//   * @param saleRebateComputeParamVos
//   * <AUTHOR>
//   * @date
//   */
//  private List<SaleRebateComputeParamVo> buildParam(
//      List<SaleRebateComputeBuildParamVo> saleRebateComputeParamVos) {
//    List<SaleRebateComputeParamVo> params = Lists.newArrayList();
//    saleRebateComputeParamVos.forEach(saleRebateComputeBuildParamVo -> {
//      Set<String> cusCodes = saleRebateComputeBuildParamVo.getCustomerCodes();
//      Set<String> productCodes = saleRebateComputeBuildParamVo.getProductCodes();
//      Validate.isTrue(CollectionUtils.isNotEmpty(cusCodes), "返利政策客户范围不能为空！");
//      List<CustomerVo> customerVos = this.customerVoService.findForPriceByCustomerCodes(cusCodes);
//      Validate.isTrue(CollectionUtils.isNotEmpty(customerVos), "客户不存在");
//      List<SaleRebatePolicyFormulaInfoVo> formulaInfos = saleRebateComputeBuildParamVo
//          .getSaleRebatePolicyFormulaInfoVos();
//      Validate.isTrue(CollectionUtils.isNotEmpty(formulaInfos), "返利公式不能为空！");
//      Validate.notNull(saleRebateComputeBuildParamVo.getSaleRebateStartTime(), "返利本次执行范围开始时间为空！");
//      Validate.notNull(saleRebateComputeBuildParamVo.getSaleRebateEndTime(), "返利本次执行范围结束时间为空！");
//      SaleRebatePolicyVo saleRebatePolicyVo = saleRebateComputeBuildParamVo.getSaleRebatePolicyVo();
//      Map<String, BigDecimal> amountMap = this
//          .getAmountMap(formulaInfos, productCodes, customerVos, saleRebateComputeBuildParamVo,
//              saleRebatePolicyVo);
//      Validate.notNull(amountMap, "返利公式金额不能为空！");
//      //2、
//      customerVos.forEach(cus -> {
//        //返利公式
//        formulaInfos.forEach(express -> {
//          SaleRebateComputeParamVo param = new SaleRebateComputeParamVo();
//          SaleRebatePolicyFormulaInfoVo saleRebatePolicyFormulaInfoVo = this.nebulaToolkitService
//              .copyObjectByBlankList(
//                  express, SaleRebatePolicyFormulaInfoVo.class, HashSet.class,
//                  ArrayList.class);
//          //客户
//          param.setCusJson(JsonUtils.toJSONObject(cus));
//          //公式
//          param.setSaleRebatePolicyFormulaInfoVo(saleRebatePolicyFormulaInfoVo);
//          //公式结算结果
//          param.setAmountMap(amountMap);
//          //返利
//          param.setSaleRebateStartTime(saleRebateComputeBuildParamVo.getSaleRebateStartTime());
//          param.setSaleRebateEndTime(saleRebateComputeBuildParamVo.getSaleRebateEndTime());
//          param.setSaleRebateComputeStartTime(saleRebateComputeBuildParamVo.getSaleRebateStartTime());
//          param.setSaleRebateComputeEndTime(saleRebateComputeBuildParamVo.getSaleRebateEndTime());
//          param.setSaleRebatePolicyVo(saleRebateComputeBuildParamVo.getSaleRebatePolicyVo());
//          param.setAllocationType(AllocationTypeEnum.FULL_AMOUNT.getKey());
//          param.setSpeedNo(saleRebateComputeBuildParamVo.getSpeedNo());
//          param.setCalculationTime(saleRebateComputeBuildParamVo.getCalculationTime());
//          param.setCalculateType(saleRebateComputeBuildParamVo.getCalculateType());
//          params.add(param);
//        });
//      });
//    });
//    return params;
//  }
//
//  /**
//   * 获取对应基准金额
//   *
//   * @param formulaInfos                  公式
//   * @param productCodes                  考核商品
//   * @param customerVos                   客户
//   * @param saleRebateComputeBuildParamVo 构建最小粒度参数vo
//   * <AUTHOR>
//   * @date
//   */
//  private Map<String, BigDecimal> getAmountMap(List<SaleRebatePolicyFormulaInfoVo> formulaInfos,
//      Set<String> productCodes,
//      List<CustomerVo> customerVos,
//      SaleRebateComputeBuildParamVo saleRebateComputeBuildParamVo,
//      SaleRebatePolicyVo saleRebatePolicyVo) {
//    Set<String> collect = SaleRebatePolicyCalculateUtil.getCriterionSet(formulaInfos);
//    Map<String, BigDecimal> amountMap = new HashMap<>();
//    collect.forEach(s -> {
//      List<SaleRebatePolicyCriterionRegister> list = saleRebatePolicyCriterionRegisters.stream()
//          .filter(saleRebatePolicyCriterionRegister ->
//                  s.startsWith(saleRebatePolicyCriterionRegister.getSaleRebatePolicyCriterionCode())
//          )
//          .collect(Collectors.toList());
//      Validate.isTrue(org.apache.commons.collections.CollectionUtils.isNotEmpty(list), "基准注册器不存在！");
//      SaleRebatePolicyCriterionRegister bean = list.get(0);
//      Set<String> saleRebateCustomerSet = customerVos.stream().map(CustomerVo::getCustomerCode)
//          .collect(Collectors.toSet());
//      amountMap.putAll(
//          bean.getAmountMap(
//              s,
//              saleRebatePolicyVo.getSaleRebatePolicyCode(),
//              saleRebateCustomerSet,
//              productCodes,
//              saleRebateComputeBuildParamVo.getSaleRebateStartTime(),
//              saleRebateComputeBuildParamVo.getSaleRebateEndTime(),
//              saleRebatePolicyVo.getSaleRebateStartTime(),
//              saleRebatePolicyVo.getSaleRebateEndTime()
//          )
//      );
//    });
//    return amountMap;
//  }
//}
