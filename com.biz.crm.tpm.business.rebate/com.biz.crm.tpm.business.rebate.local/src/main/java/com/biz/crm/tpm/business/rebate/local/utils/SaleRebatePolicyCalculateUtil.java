package com.biz.crm.tpm.business.rebate.local.utils;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateCalculationLog;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyDetail;
import com.biz.crm.tpm.business.rebate.sdk.enums.BillTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleOnAccountStatusEnums;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleRebatePolicyProductTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.vo.*;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerRelateOrgVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaSaleRebatePolicyElementVo;
import com.bizunited.nebula.common.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @description: 返利计算工具类
 * @author: lifei
 * @date: 2022/3/13 12:00
 */
public class SaleRebatePolicyCalculateUtil {

    private static final SimpleDateFormat formatMonth = new SimpleDateFormat("yyyy-MM");

    /**
     * 根据表达式计算出结果，变量数值从业务方实现的接口中获取 1、校验入参，两个入参均不能为空 2、从业务方获取变量值，替换到公式里 3、计算替换真实变量后的公式，如果计算失败，抛出异常
     *
     * @param expressStr 表达式字符串（包含变量编码）
     * @param param
     * @return
     */
    public static SaleRebateFormulaResultVo computeRebateExpression(String expressStr,
                                                                    String expressStrName,
                                                                    SaleRebateComputeParamVo param) {
        //1、
        Validate.notEmpty(expressStr, "解析返利表达式时，表达式不能为空");
        //2、
        SaleRebateFormulaResultVo result = new SaleRebateFormulaResultVo();
        SaleRebateConditionResultVo conditionComputeResult = SaleRebatePolicyCalculateUtil
                .replaceVariable(expressStr, expressStrName, param);
        //3、
        String expressValue = conditionComputeResult.getExpressValue();
        BigDecimal computeResult = MathUtil.computeFormula(expressValue);
        Validate.notNull(computeResult, "返利公式【%s】计算失败，请检查公式是否正确", expressStr);
        result.setExpressStr(expressStr);
        result.setExpressStrName(expressStrName);
        result.setExpressValue(expressValue);
        result.setValue(computeResult);
        result.setComputeProcess(conditionComputeResult.getComputeProcess());
        result.setComputeProcessResult(conditionComputeResult.getComputeProcessResult());
        return result;
    }

    /**
     * 根据返利条件表达式计算结果，变量值从变量对象中（替代值）获取 1、校验入参，两个入参均不能为空 2、从业务方获取变量值，替换到公式里 3、计算替换真实变量后的公式，如果计算失败，抛出异常
     *
     * @param expressStr 表达式字符串（包含变量编码）
     * @param param
     * @return
     * @Param speedNo 当次计算批次号
     */
    public static SaleRebateConditionResultVo computeConditionExpression(String expressStr,
                                                                         String expressStrName,
                                                                         SaleRebateComputeParamVo param) {
        Validate.notEmpty(expressStr, "解析条件表达式时，表达式不能为空");
        SaleRebateConditionResultVo result = SaleRebatePolicyCalculateUtil
                .replaceVariable(expressStr, expressStrName, param);
        String expressValue = result.getExpressValue();
        Boolean computeResult = MathUtil.computeCondition(expressValue);
        Validate.notNull(computeResult, "返利条件公式【%s】计算失败，请检查公式是否正确", expressStr);
        result.setValue(computeResult);
        result.setExpressStr(expressStr);
        result.setExpressStrName(expressStrName);
        return result;
    }

    /**
     * 根据公式获取所有变量值，以键值对形式返回 1、校验入参 2、查询表达式中所有变量记录 3、遍历并检测表达式中包含哪些变量，包含的变量向业务方查询值，并替换到表达式里
     *
     * @param expressStr 表达式字符串
     * @param param
     * @return
     */
    public static SaleRebateConditionResultVo replaceVariable(String expressStr,
                                                              String expressStrName,
                                                              SaleRebateComputeParamVo param) {
        Validate.notEmpty(expressStr, "替换公式变量时，公式字符串不能为空");
        SaleRebateConditionResultVo result = new SaleRebateConditionResultVo();
        result.setExpressStr(expressStr);
        result.setExpressStrName(expressStrName);
        Set<String> variables = MathUtil.getFormulaReplace(expressStr);
        Map<String, BigDecimal> variableMap = param.getAmountMap();
        CustomerVo customerVo = JsonUtils.json2Obj(param.getCusJson().toJSONString(), CustomerVo.class);
        String expressValue = expressStr;
        //变量获取传入参数
        String expressProperties = new String();
        //变量获取结果
        String expressProcess = new String();
        for (String entry : variables) {
            String k = entry + customerVo.getCustomerCode();
            String v = variableMap.get(k).toString();
            Validate.notEmpty(v, "%s时检测到变量【%s】值为空",
                    BooleanEnum.FALSE.getNumStr().equals(param.getIsTest()) ? "计算" : "测试计算", k);
            //替换变量实际值
            expressValue = expressValue.replace(entry, v);
            //设置变量获取传入参数
            expressProperties = String.join(";", expressProperties,
                    String.join(",", String.join(":", "变量", entry),
                            String.join(":", "取值", v)
                    ));
            //设置变量获取结果
            expressProcess = String.join(";", expressProcess, String.join(":", k, v));
        }
        //截掉第一个字符‘,'
        if (StringUtils.isNotEmpty(expressProperties)) {
            expressProperties = expressProperties.substring(1);
        }
        if (StringUtils.isNotEmpty(expressProcess)) {
            expressProcess = expressProcess.substring(1);
        }
        result.setExpressValue(expressValue);
        result.setComputeProcessResult(expressProperties);
        result.setComputeProcess(expressProcess);
        return result;
    }

    /**
     * 公式中包含得基准
     *
     * @param formulaInfos
     * <AUTHOR>
     * @date
     */
    public static Set<String> getCriterionSet(List<SaleRebatePolicyFormulaInfoVo> formulaInfos) {
        Set<String> conditionAndFormula = new HashSet<>();
        for (SaleRebatePolicyFormulaInfoVo policyFormula : formulaInfos) {
            Set<String> saleRebateCondition = MathUtil
                    .getFormulaReplace(policyFormula.getSaleRebatePolicyCondition());
            Set<String> saleRebateformula = MathUtil
                    .getFormulaReplace(policyFormula.getSaleRebatePolicyFormula());
            conditionAndFormula.addAll(saleRebateformula);
            conditionAndFormula.addAll(saleRebateCondition);
        }
        return conditionAndFormula;
    }


    /**
     * 保存明细数据
     *
     * @param
     * @param
     * @param param
     * @param loginDetails
     * @return
     */
    public static SaleRebatePolicyDetail buildDetail(SaleRebateComputeParamVo param,
                                                     SaleRebateFormulaResultVo executeComputeResult,
                                                     CustomerVo customerVo, FacturerUserDetails loginDetails) {
        Validate.notNull(param, "保存明细数据时，计算参数不能为空");
        SaleRebatePolicyVo rebateVo = param.getSaleRebatePolicyVo();
        Validate.notNull(rebateVo, "保存明细数据时，返利政策对象不能为空");
        Validate.notNull(executeComputeResult, "保存明细数据时，计算结果不能为空");
        SaleRebatePolicyDetail saleRebatePolicyDetail = new SaleRebatePolicyDetail();
        saleRebatePolicyDetail.setSaleRebatePolicyCode(rebateVo.getSaleRebatePolicyCode());
        saleRebatePolicyDetail.setSaleRebatePolicyName(rebateVo.getSaleRebatePolicyName());
        saleRebatePolicyDetail.setCustomerCode(customerVo.getCustomerCode());
        saleRebatePolicyDetail.setCustomerName(customerVo.getCustomerName());
        saleRebatePolicyDetail.setErpCode(customerVo.getErpCode());
        saleRebatePolicyDetail.setProductGroupCode(customerVo.getProductGroupCode());
        saleRebatePolicyDetail.setChannelCode(customerVo.getChannelCode());
        saleRebatePolicyDetail.setPositionCode(loginDetails.getPostCode());
        List<CustomerRelateOrgVo> orgList = customerVo.getOrgList();
        if (!CollectionUtils.isEmpty(orgList)) {
            CustomerRelateOrgVo customerRelateOrgVo = orgList.get(0);
            saleRebatePolicyDetail.setOrgCode(customerRelateOrgVo.getOrgCode());
            saleRebatePolicyDetail.setOrgName(customerRelateOrgVo.getOrgName());
        } else {
            saleRebatePolicyDetail.setOrgCode(loginDetails.getOrgCode());
            saleRebatePolicyDetail.setOrgName(loginDetails.getOrgName());
        }
        saleRebatePolicyDetail.setSaleRebateType(rebateVo.getSaleRebateType());
        saleRebatePolicyDetail.setSaleRebateTypeName(rebateVo.getSaleRebateTypeName());
        saleRebatePolicyDetail.setSaleRebatePolicyCycle(rebateVo.getSaleRebatePolicyCycle());
        saleRebatePolicyDetail.setSaleRebatePolicyCycleName(rebateVo.getSaleRebatePolicyCycleName());
        saleRebatePolicyDetail.setSaleRebateStartTime(param.getSaleRebateComputeStartTime());
        saleRebatePolicyDetail.setSaleRebateEndTime(param.getSaleRebateComputeEndTime());
        saleRebatePolicyDetail
                .setSaleRebateCalculationYears(formatMonth.format(new Date()));
        saleRebatePolicyDetail.setCalculationTime(param.getCalculationTime());
        saleRebatePolicyDetail.setSpeedNo(param.getSpeedNo());
        saleRebatePolicyDetail.setBillType(rebateVo.getBillType());
        if (BillTypeEnum.AUTO_KEEP_BOOKS.getKey().equals(rebateVo.getBillType())) {
            saleRebatePolicyDetail.setBillStatus(SaleOnAccountStatusEnums.ON_ACCOUNT.getKey());
        } else {
            saleRebatePolicyDetail.setBillStatus(SaleOnAccountStatusEnums.WAIT_ACCOUNT.getKey());
        }
        saleRebatePolicyDetail.setIsTest(param.getIsTest());
        saleRebatePolicyDetail.setProductType(param.getProductType());
        if (param.getProductType() != null) {
            if (SaleRebatePolicyProductTypeEnum.PRODUCT.getKey().equals(param.getProductType())) {
                saleRebatePolicyDetail.setProductCode(param.getCode());
                saleRebatePolicyDetail.setProductName(param.getName());
            } else {
                saleRebatePolicyDetail.setProductLevelCode(param.getCode());
                saleRebatePolicyDetail.setProductLevelName(param.getName());
            }
        }
        saleRebatePolicyDetail.setAllocationType(param.getAllocationType());
        saleRebatePolicyDetail.setRebateRatio(param.getRebateRatio());
        saleRebatePolicyDetail
                .setSaleRebatePolicyFormulaId(param.getSaleRebatePolicyFormulaInfoVo().getId());
        return saleRebatePolicyDetail;
    }

    /**
     * 保存计算日志数据
     *
     * @param param
     * @param conditionResult
     * @param executeComputeResult
     */
    public static SaleRebateCalculationLog buildComputeLog(SaleRebateComputeParamVo param,
                                                           SaleRebateConditionResultVo conditionResult, SaleRebateFormulaResultVo executeComputeResult,
                                                           String detailId) {
        CustomerVo customerVo = JsonUtils.json2Obj(param.getCusJson().toJSONString(), CustomerVo.class);
        Validate.notNull(param, "保存明细数据时，计算参数不能为空");
        SaleRebatePolicyVo saleRebatePolicyVo = param.getSaleRebatePolicyVo();
        Validate.notNull(conditionResult, "保存明细数据时，计算结果不能为空");
        SaleRebateCalculationLog saleRebateCalculationLog = new SaleRebateCalculationLog();
        saleRebateCalculationLog.setSaleRebatePolicyCode(saleRebatePolicyVo.getSaleRebatePolicyCode());
        saleRebateCalculationLog.setSaleRebatePolicyName(saleRebatePolicyVo.getSaleRebatePolicyName());
        saleRebateCalculationLog.setCustomerCode(customerVo.getCustomerCode());
        saleRebateCalculationLog.setCustomerName(customerVo.getCustomerName());
        saleRebateCalculationLog
                .setSaleRebateCalculationYears(formatMonth.format(param.getCalculationTime()));
        saleRebateCalculationLog.setSaleRebatePolicyCondition(conditionResult.getExpressStrName());
        saleRebateCalculationLog
                .setSaleRebatePolicyConditionProcess(conditionResult.getComputeProcessResult());
        saleRebateCalculationLog.setSaleRebateResults(conditionResult.isValue());
        if (Objects.nonNull(executeComputeResult)) {
            saleRebateCalculationLog.setSaleRebatePolicyFormula(executeComputeResult.getExpressStrName());
            saleRebateCalculationLog
                    .setSaleRebatePolicyFormulaProcess(executeComputeResult.getComputeProcessResult());
            saleRebateCalculationLog.setSaleRebateCalculationResults(executeComputeResult.getValue());
        }
        saleRebateCalculationLog.setIsTest(param.getIsTest());
        saleRebateCalculationLog.setAllocationType(param.getAllocationType());
        saleRebateCalculationLog.setRebateRatio(param.getRebateRatio());
        saleRebateCalculationLog.setSaleRebatePolicyDetailId(detailId);
        saleRebateCalculationLog.setSpeedNo(param.getSpeedNo());
        return saleRebateCalculationLog;
    }

    /**
     * 校验最细粒度返利参数
     *
     * @param param
     * <AUTHOR>
     * @date
     */
    public static void validateCal(SaleRebateComputeParamVo param) {
        Validate.notNull(param, "计算返利时，参数不能为空");
        Validate.notNull(param.getCusJson(), "计算返利时，客户不能为空");
        Validate.notNull(param.getSaleRebatePolicyVo(), "计算返利时，返利政策不能为空");
        Validate.notEmpty(param.getSpeedNo(), "计算返利时，计算批次号不能为空");
        Validate.notNull(param.getSaleRebatePolicyFormulaInfoVo(), "计算返利时，公式不能为空");
        Validate.notNull(param.getIsTest(), "计算返利时，是否测试不能为空");
    }

}
