package com.biz.crm.tpm.business.rebate.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyCheckProductInfo;
import com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyCheckProductInfoMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 返利政策考核商品(SaleRebatePolicyCheckProductInfo)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
@Component
public class SaleRebatePolicyCheckProductInfoRepository extends
    ServiceImpl<SaleRebatePolicyCheckProductInfoMapper, SaleRebatePolicyCheckProductInfo> {

  /**
   * 分页查询
   *
   * @param pageable                         分页
   * @param saleRebatePolicyCheckProductInfo
   * @return
   */
  public Page<SaleRebatePolicyCheckProductInfo> findByConditions(Pageable pageable,
      SaleRebatePolicyCheckProductInfo saleRebatePolicyCheckProductInfo) {
    Page<SaleRebatePolicyCheckProductInfo> page = new Page<>(pageable.getPageNumber(),
        pageable.getPageSize());
    saleRebatePolicyCheckProductInfo.setTenantCode(TenantUtils.getTenantCode());
    Page<SaleRebatePolicyCheckProductInfo> pageList = this.baseMapper
        .findByConditions(page, saleRebatePolicyCheckProductInfo);
    return pageList;
  }

  /**
   * 通过返利政策编码查询考核产品
   *
   * @param saleRebatePolicyCodes
   * @return
   */
  public List<SaleRebatePolicyCheckProductInfo> findBySaleRebatePolicyCodes(
      List<String> saleRebatePolicyCodes) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyCheckProductInfo::getTenantCode, TenantUtils.getTenantCode())
        .in(SaleRebatePolicyCheckProductInfo::getSaleRebatePolicyCode, saleRebatePolicyCodes)
        .list();
  }


  /**
   * 通过返利政策编码查询考核产品
   *
   * @param saleRebatePolicyCode
   * @return
   */
  public List<SaleRebatePolicyCheckProductInfo> findBySaleRebatePolicyCode(
      String saleRebatePolicyCode) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyCheckProductInfo::getTenantCode, TenantUtils.getTenantCode())
        .eq(SaleRebatePolicyCheckProductInfo::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .list();
  }


  /**
   * 通过返利政策编码查询考核产品
   *
   * @param saleRebatePolicyCode
   * @return
   */
  public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    this.lambdaUpdate()
        .eq(SaleRebatePolicyCheckProductInfo::getTenantCode, TenantUtils.getTenantCode())
        .eq(SaleRebatePolicyCheckProductInfo::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .remove();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(SaleRebatePolicyCheckProductInfo::getTenantCode,tenantCode)
        .in(SaleRebatePolicyCheckProductInfo::getId,ids)
        .remove();
  }
}

