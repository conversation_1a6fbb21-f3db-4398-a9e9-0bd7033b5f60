package com.biz.crm.tpm.business.rebate.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateCalculationLog;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 返利政策计算日志(SaleRebateCalculationLog)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-02-28 15:19:44
 */
public interface SaleRebateCalculationLogMapper extends BaseMapper<SaleRebateCalculationLog> {

  /**
   * 分页查询所有数据
   *
   * @param page                     分页对象
   * @param saleRebateCalculationLog 查询实体
   * @return 所有数据
   */
  public Page<SaleRebateCalculationLog> findByConditions(
      @Param("page") Page<SaleRebateCalculationLog> page,
      @Param("dto") SaleRebateCalculationLog saleRebateCalculationLog);
}

