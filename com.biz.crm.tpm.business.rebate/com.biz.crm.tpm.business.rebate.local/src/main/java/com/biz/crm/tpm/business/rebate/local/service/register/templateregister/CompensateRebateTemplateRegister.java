//package com.biz.crm.tpm.business.rebate.local.service.register.templateregister;
//
//import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.CostPoolReplenishmentDto;
//import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.CostPoolReplenishmentProductDto;
//import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.*;
//import com.biz.crm.dms.business.costpool.replenishment.sdk.strategy.OperationTypeStrategy;
//import com.biz.crm.tpm.business.rebate.local.service.register.elementregister.CheckProductSaleRebatePolicyElementRegisterImpl;
//import com.biz.crm.tpm.business.rebate.local.service.register.elementregister.FormulaSaleRebatePolicyElementRegisterImpl;
//import com.biz.crm.tpm.business.rebate.local.service.register.elementregister.ScopeSaleRebatePolicyElementRegisterImpl;
//import com.biz.crm.tpm.business.rebate.local.strategy.ChannelForSaleRebateCustomerScopeStrategy;
//import com.biz.crm.tpm.business.rebate.local.strategy.DistributorForSaleRebateCustomerScopeStrategy;
//import com.biz.crm.tpm.business.rebate.local.strategy.OrgForSaleRebateCustomerScopeStrategy;
//import com.biz.crm.tpm.business.rebate.local.utils.SaleRebatePolicyCalculateUtil;
//import com.biz.crm.tpm.business.rebate.sdk.enums.SaleRebatePolicyProductTypeEnum;
//import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
//import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
//import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyTemplateRegister;
//import com.biz.crm.tpm.business.rebate.sdk.strategy.SaleRebateCustomerScopeStrategy;
//import com.biz.crm.tpm.business.rebate.sdk.vo.*;
//import com.biz.crm.tpm.business.rebate.sdk.vo.scope.AbstractSaleRebatePolicyCustomerInfo;
//import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
//import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
//import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
//import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
//import com.bizunited.nebula.common.service.NebulaToolkitService;
//import com.bizunited.nebula.common.util.JsonUtils;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Sets;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.Validate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * @description: 货补模板注册实现
// * @author: lifei
// * @date: 2022/2/21 10:05
// */
//@Component
//@Slf4j
//public class CompensateRebateTemplateRegister implements SaleRebatePolicyTemplateRegister {
//
//  @Autowired(required = false)
//  private NebulaToolkitService nebulaToolkitService;
//  @Autowired(required = false)
//  private List<SaleRebatePolicyCriterionRegister> saleRebatePolicyCriterionRegisters;
//  @Autowired(required = false)
//  private CustomerVoService customerVoService;
//  @Autowired(required = false)
//  @Qualifier("replenishmentAccountOperationTypeStrategyImpl")
//  private OperationTypeStrategy operationTypeStrategy;
//  @Autowired(required = false)
//  private ProductVoService productVoService;
//
//
//  /**
//   * 返利政策要素名称
//   */
//  private static final String REBATE_POLICY_TEMPLATE_NAME = "货 补";
//
//  /**
//   * 返利政策要素编码
//   */
//  private static final String REBATE_POLICY_TEMPLATE_CODE = "compensateRebateTemplate";
//
//  /**
//   * 排序
//   */
//  private static final Integer REBATE_POLICY_TEMPLATE_SORT = 1;
//
//  /**
//   * 模板编码
//   *
//   * <AUTHOR>
//   * @date
//   */
//  @Override
//  public String getSaleRebatePolicytemplateCode() {
//    return REBATE_POLICY_TEMPLATE_CODE;
//  }
//
//  /**
//   * 模板名称
//   *
//   * <AUTHOR>
//   * @date
//   */
//  @Override
//  public String getSaleRebatePolicytemplateName() {
//    return REBATE_POLICY_TEMPLATE_NAME;
//  }
//
//  /**
//   * 拿到模板排序列表排序
//   */
//  @Override
//  public Integer getTemplateSort() {
//    return REBATE_POLICY_TEMPLATE_SORT;
//  }
//
//  /**
//   * 返回该返利政策可以使用的客户范围选择方式(注意，是可用的，不是具体优惠实例正在使用的)
//   */
//  @Override
//  public Collection<Class<? extends SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo>>> getCustomerScopeStrategyClasses() {
//    return Sets.newHashSet(DistributorForSaleRebateCustomerScopeStrategy.class,
//        ChannelForSaleRebateCustomerScopeStrategy.class,
//        OrgForSaleRebateCustomerScopeStrategy.class);
//  }
//
//  /**
//   * 可用的要素
//   *
//   * <AUTHOR>
//   * @date
//   */
//  @Override
//  public Collection<Class<? extends SaleRebatePolicyElementRegister>> getSaleRebateElementClasses() {
//    return Sets.newHashSet(CheckProductSaleRebatePolicyElementRegisterImpl.class,
//        FormulaSaleRebatePolicyElementRegisterImpl.class
//            , ScopeSaleRebatePolicyElementRegisterImpl.class
//            //, RebateProductSaleRebatePolicyElementRegister.class
//                          );
//  }
//
//  /**
//   * 构建最小粒度参数 返利明细
//   *
//   * @param saleRebateComputeBuildParamVos 构建最小粒度参数vo
//   * <AUTHOR>
//   * @dateSaleRebateComputeBuildParamVo
//   */
//  @Override
//  public List<SaleRebateComputeParamVo> onbuildParam(
//      List<SaleRebateComputeBuildParamVo> saleRebateComputeBuildParamVos) {
//    if (CollectionUtils.isNotEmpty(saleRebateComputeBuildParamVos)) {
//      return this.buildParam(saleRebateComputeBuildParamVos);
//    }
//    return null;
//  }
//
//  /**
//   * 上账
//   *
//   * @param saleRebatePolicyDetailVos 返利明细
//   * <AUTHOR>
//   */
//  @Override
//  public void onAccount(List<SaleRebatePolicyDetailVo> saleRebatePolicyDetailVos) {
//    if (!CollectionUtils.isEmpty(saleRebatePolicyDetailVos)) {
//      Map<String, ProductVo> productVoMap = new HashMap<>();
//      List<String> productCodes = saleRebatePolicyDetailVos.stream().filter(
//          saleRebatePolicyDetailVo -> SaleRebatePolicyProductTypeEnum.PRODUCT.getKey()
//              .equals(saleRebatePolicyDetailVo.getProductType()))
//          .map(SaleRebatePolicyDetailVo::getProductCode).collect(
//              Collectors.toList());
//      if (CollectionUtils.isNotEmpty(productCodes)) {
//        List<ProductVo> productVos = this.productVoService
//            .findMainDetailsByProductCodes(productCodes);
//        if (CollectionUtils.isNotEmpty(productVos)) {
//          productVoMap.putAll(productVos.stream()
//              .collect(Collectors.toMap(ProductVo::getProductCode, t -> t, (key1, key2) -> key2)));
//        }
//      }
//      saleRebatePolicyDetailVos.forEach(saleRebatePolicyDetailVo -> {
//        CostPoolReplenishmentDto dto = new CostPoolReplenishmentDto();
//        dto.setPoolType(PoolTypeEnum.Replenishment.getKey());
//        dto.setCustomerCode(saleRebatePolicyDetailVo.getCustomerCode());
//        dto.setFromCode(saleRebatePolicyDetailVo.getSaleRebateDetailCode());
//        dto.setFromDesc(PoolOperationTypeEnum.REBATE_ACCOUNT.getValue());
//        dto.setAmount(saleRebatePolicyDetailVo.getActualRebateAmount());
//        dto.setPoolGroup(PoolGroupEnum.DEFAULT.getDictCode());
//        dto.setPayType(PoolPayTypeEnum.Replenishment.getDictCode());
//        dto.setUseType(PoolUseTypeEnum.DEFAULT.getDictCode());
//        dto.setCustomerName(saleRebatePolicyDetailVo.getCustomerName());
//        dto.setOperationType(PoolOperationTypeEnum.REBATE_ACCOUNT.getDictCode());
//        if (SaleRebatePolicyProductTypeEnum.PRODUCT.getKey()
//            .equals(saleRebatePolicyDetailVo.getProductType())) {
//          CostPoolReplenishmentProductDto costPoolReplenishmentProductDto = new CostPoolReplenishmentProductDto();
//          costPoolReplenishmentProductDto
//              .setGoodsProductCode(saleRebatePolicyDetailVo.getProductCode());
//          costPoolReplenishmentProductDto
//              .setGoodsProductName(saleRebatePolicyDetailVo.getProductName());
//          costPoolReplenishmentProductDto.setMaterialCode(saleRebatePolicyDetailVo.getMaterialCode());
//          dto.setCostPoolReplenishmentProduct(
//              Collections.singletonList(costPoolReplenishmentProductDto));
//          //拼接参数
//          ProductVo productVo = productVoMap.get(saleRebatePolicyDetailVo.getProductCode());
//          if (Objects.isNull(productVo)) {
//            return;
//          }
//          dto.setGoodsProductLevelCode(productVo.getProductLevelCode());
//          dto.setGoodsProductLevelName(productVo.getProductLevelName());
//        } else {
//          //拼接参数
//          dto.setGoodsProductLevelCode(saleRebatePolicyDetailVo.getProductLevelCode());
//          dto.setGoodsProductLevelName(saleRebatePolicyDetailVo.getProductLevelName());
//        }
//        dto.setCompanyCode(saleRebatePolicyDetailVo.getCompanyCode());
//        dto.setErpCode(saleRebatePolicyDetailVo.getErpCode());
//        dto.setProductGroupCode(saleRebatePolicyDetailVo.getProductGroupCode());
//        dto.setChannelCode(saleRebatePolicyDetailVo.getChannelCode());
//        dto.setPositionCode(saleRebatePolicyDetailVo.getPositionCode());
//        operationTypeStrategy.onSaveDiscountInfos(dto);
//      });
//    }
//  }
//
//  /**
//   * 构建最小粒度参数 返利明细
//   *
//   * @param saleRebateComputeParamVos 构建最小粒度参数vo
//   * <AUTHOR>
//   * @date
//   */
//  private List<SaleRebateComputeParamVo> buildParam(
//      List<SaleRebateComputeBuildParamVo> saleRebateComputeParamVos) {
//    List<SaleRebateComputeParamVo> params = Lists.newArrayList();
//    saleRebateComputeParamVos.forEach(saleRebateComputeBuildParamVo -> {
//      Set<String> cusCodes = saleRebateComputeBuildParamVo.getCustomerCodes();
//      Set<String> productCodes = saleRebateComputeBuildParamVo.getProductCodes();
//      Validate.isTrue(CollectionUtils.isNotEmpty(cusCodes), "返利政策客户范围不能为空！");
//      List<CustomerVo> customerVos = this.customerVoService.findForPriceByCustomerCodes(cusCodes);
//      Validate.isTrue(CollectionUtils.isNotEmpty(customerVos), "客户不存在");
//      List<SaleRebatePolicyFormulaInfoVo> formulaInfos = saleRebateComputeBuildParamVo
//          .getSaleRebatePolicyFormulaInfoVos();
//      Validate.isTrue(CollectionUtils.isNotEmpty(formulaInfos), "返利公式不能为空！");
//      Validate.notNull(saleRebateComputeBuildParamVo.getSaleRebateStartTime(), "返利本次执行范围开始时间为空！");
//      Validate.notNull(saleRebateComputeBuildParamVo.getSaleRebateEndTime(), "返利本次执行范围结束时间为空！");
//      Map<String, BigDecimal> amountMap = this
//          .getAmountMap(formulaInfos, productCodes, customerVos, saleRebateComputeBuildParamVo);
//      Validate.notNull(amountMap, "返利公式金额不能为空！");
//      List<SaleRebatePolicyProductInfoVo> saleRebatePolicyProductInfoVos = saleRebateComputeBuildParamVo
//          .getSaleRebatePolicyProductInfoVos();
//      Validate.isTrue(CollectionUtils.isNotEmpty(saleRebatePolicyProductInfoVos), "返利政策中返利商品不存在！");
//      //2、
//      customerVos.forEach(cus -> {
//        //返利商品
//        saleRebatePolicyProductInfoVos.forEach(product ->
//            //返利公式
//            formulaInfos.forEach(express -> {
//              //返利产品属性的赋值
//              SaleRebateComputeParamVo param = this.nebulaToolkitService.copyObjectByBlankList(
//                  product, SaleRebateComputeParamVo.class, HashSet.class,
//                  ArrayList.class);
//              param.setCode(product.getCode());
//              param.setName(product.getName());
//              param.setProductType(Integer.valueOf(product.getType()));
//              param.setAllocationType(product.getAllocationType());
//              param.setRebateRatio(product.getRebateRatio());
//              SaleRebatePolicyFormulaInfoVo saleRebatePolicyFormulaInfoVo = this.nebulaToolkitService
//                  .copyObjectByBlankList(
//                      express, SaleRebatePolicyFormulaInfoVo.class, HashSet.class,
//                      ArrayList.class);
//              //客户
//              param.setCusJson(JsonUtils.toJSONObject(cus));
//              //公式
//              param.setSaleRebatePolicyFormulaInfoVo(saleRebatePolicyFormulaInfoVo);
//              //公式结算结果
//              param.setAmountMap(amountMap);
//              //返利
//              param.setSaleRebateStartTime(saleRebateComputeBuildParamVo.getSaleRebateStartTime());
//              param.setSaleRebateEndTime(saleRebateComputeBuildParamVo.getSaleRebateEndTime());
//              param.setSaleRebateComputeStartTime(saleRebateComputeBuildParamVo.getSaleRebateStartTime());
//              param.setSaleRebateComputeEndTime(saleRebateComputeBuildParamVo.getSaleRebateEndTime());
//              param.setSaleRebatePolicyVo(saleRebateComputeBuildParamVo.getSaleRebatePolicyVo());
//              param.setAllocationType(product.getAllocationType());
//              param.setSpeedNo(saleRebateComputeBuildParamVo.getSpeedNo());
//              param.setCalculationTime(saleRebateComputeBuildParamVo.getCalculationTime());
//              param.setCalculateType(saleRebateComputeBuildParamVo.getCalculateType());
//              params.add(param);
//            })
//        );
//      });
//    });
//    return params;
//  }
//
//  private Map<String, BigDecimal> getAmountMap(
//      List<SaleRebatePolicyFormulaInfoVo> formulaInfos,
//      Set<String> productCodes,
//      List<CustomerVo> customerVos,
//      SaleRebateComputeBuildParamVo saleRebateComputeBuildParamVo
//  ) {
//    Set<String> collect = SaleRebatePolicyCalculateUtil.getCriterionSet(formulaInfos);
//    Map<String, BigDecimal> amountMap = new HashMap<>();
//    collect.forEach(s -> {
//      List<SaleRebatePolicyCriterionRegister> list = this.saleRebatePolicyCriterionRegisters
//          .stream()
//          .filter(saleRebatePolicyCriterionRegister ->
//              s.startsWith(saleRebatePolicyCriterionRegister.getSaleRebatePolicyCriterionCode())
//          )
//          .collect(Collectors.toList());
//      Validate.isTrue(CollectionUtils.isNotEmpty(list), "基准注册器不存在！");
//      SaleRebatePolicyCriterionRegister bean = list.get(0);
//      Set<String> saleRebateCustomerSet = customerVos.stream()
//          .map(CustomerVo::getCustomerCode)
//          .collect(Collectors.toSet());
//      SaleRebatePolicyVo saleRebatePolicyVo = saleRebateComputeBuildParamVo.getSaleRebatePolicyVo();
//      String saleRebatePolicyCode = saleRebatePolicyVo.getSaleRebatePolicyCode();
//      amountMap.putAll(
//          bean.getAmountMap(
//              s,
//              saleRebatePolicyCode,
//              saleRebateCustomerSet,
//              productCodes,
//              saleRebateComputeBuildParamVo.getSaleRebateStartTime(),
//              saleRebateComputeBuildParamVo.getSaleRebateEndTime(),
//              saleRebatePolicyVo.getSaleRebateStartTime(),
//              saleRebatePolicyVo.getSaleRebateEndTime()
//          )
//      );
//    });
//    return amountMap;
//  }
//}