package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidOpEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Description dms公式实体
 * <AUTHOR>
 * @Date 2024/6/11 17:04
 */
@Getter
@Setter
@TableName("tpm_formula")
@Entity
@Table(name = "tpm_formula")
@org.hibernate.annotations.Table(appliesTo = "tpm_formula", comment = "dms公式实体")
public class FormulaEntity extends UuidOpEntity {

    /**
     * 公式配置编码
     */
    @Column(name = "formula_config_code" ,  nullable = false, columnDefinition = "VARCHAR(32) COMMENT '公式配置编码'")
    @ApiModelProperty("公式配置编码")
    private String formulaConfigCode;

    /**
     * 返利条件
     */
    @Column(name = "sale_rebate_policy_condition" ,  nullable = false, columnDefinition = "VARCHAR(500) COMMENT '返利条件'")
    @ApiModelProperty("返利条件")
    private String saleRebatePolicyCondition;

    /**
     * 返利条件（展示用）
     */
    @Column(name = "sale_rebate_policy_condition_name" ,  nullable = false, columnDefinition = "VARCHAR(500) COMMENT '返利条件（展示用）'")
    @ApiModelProperty("返利条件（展示用）")
    private String saleRebatePolicyConditionName;

    /**
     * 返利公式
     */
    @Column(name = "sale_rebate_policy_formula" ,  nullable = false, columnDefinition = "VARCHAR(500) COMMENT '返利公式'")
    @ApiModelProperty("返利公式")
    private String saleRebatePolicyFormula;

    /**
     * 返利公式（展示用）
     */
    @Column(name = "sale_rebate_policy_formula_name" ,  nullable = false, columnDefinition = "VARCHAR(500) COMMENT '返利公式（展示用）'")
    @ApiModelProperty("返利公式（展示用）")
    private String saleRebatePolicyFormulaName;

    /**
     * 公式顺序（计算策略和展示用）
     */
    @Column(name = "formula_sort"  , columnDefinition = "INT COMMENT '公式顺序（计算策略和展示用）'")
    @ApiModelProperty("公式顺序（计算策略和展示用）")
    private Integer formulaSort;
}
