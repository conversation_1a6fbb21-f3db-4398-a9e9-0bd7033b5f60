package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister;

import com.biz.crm.tpm.business.rebate.local.enums.FormulaVariableEnum;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @description: 达成条件
 * @author: lifei
 * @date: 2022/4/6 22:29
 */
@Service
@Slf4j
public class SaleRebateConditionsMetRegister implements SaleRebatePolicyCriterionRegister {


    @Override
    public String getSaleRebatePolicyCriterionCode() {
        return FormulaVariableEnum.CONDITIONS_MET.getCode();
    }

    @Override
    public String getSaleRebatePolicyCriterionName() {
        return FormulaVariableEnum.CONDITIONS_MET.getName();
    }

    @Override
    public Integer getCriterionSort() {
        return FormulaVariableEnum.CONDITIONS_MET.getSort();
    }

    /**
     * @param vo      计算基准公式
     * @param calType 计算类型
     * @return
     */
    @Override
    public Map<String, BigDecimal> getAmountMapByCondition(FormulaCalBaseVo vo, String calType) {
        Validate.notNull(vo.getConditionNum(), "达成条件不能为空");
        String[] conditionNums = vo.getConditionNum().split(",");
        Map<String, BigDecimal> map = Maps.newHashMap();
        String key = getSaleRebatePolicyCriterionCode() + vo.getCustomerCode();
        if (FormulaTypeEnum.MARKETING.getCode().equals(calType)) {
            map.put(key, BigDecimal.ZERO);
        } else if (FormulaTypeEnum.WITHHOLDING.getCode().equals(calType)) {
            map.put(key, BigDecimal.ZERO);
        } else if (FormulaTypeEnum.MANAGEMENT_REPORT.getCode().equals(calType)) {
            map.put(key, BigDecimal.ZERO);
        } else if (FormulaTypeEnum.END_CASE.getCode().equals(calType)){
            for (String num : conditionNums) {
                try {
                    BigDecimal conditionNum = new BigDecimal(num);
                    map.put(key, conditionNum);
                } catch (Exception e) {
                    throw new UnsupportedOperationException("达成条件只能是数字");
                }
            }
        }
        return map;
    }


}
