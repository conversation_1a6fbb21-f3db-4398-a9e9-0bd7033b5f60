package com.biz.crm.tpm.business.rebate.local.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/13 15:20
 */
@Getter
@AllArgsConstructor
public enum FormulaVariableEnum {

    YDXSJE("YDXSJE", "月度销售金额", 1),

    KHYDXSJE("KHYDXSJE", "月度销售金额(客户)", 2),

    YDXSSL("YDXSSL", "月度销售数量", 3),

    KHYDXSSL("KHYDXSSL", "月度销售数量(客户)", 4),

    JDXSJE("JDXSJE", "季度销售金额", 5),

    KHJDXSJE("KHJDXSJE", "季度销售金额(客户)", 6),

    JDXSSL("JDXSSL", "季度销售数量", 7),

    KHJDXSSL("KHJDXSSL", "季度销售数量(客户)", 8),

    NDXSJE("NDXSJE", "年度销售金额", 9),

    KHNDXSJE("KHNDXSJE", "年度销售金额(客户)", 10),

    NDXSSL("NDXSSL", "年度销售数量", 11),

    KHNDXSSL("KHNDXSSL", "年度销售数量(客户)", 12),

    POLICY_SALES_AMOUNT("POLICYSALESAMOUNT", "政策期间销售金额", 13),

    CUS_POLICY_SALES_AMOUNT("CUSPOLICYSALES_AMOUNT", "政策期间销售金额(客户)", 14),

    POLICY_SALES_QUANTITY("POLICYSALESQUANTITY", "政策期间销售数量", 13),

    CUS_POLICY_SALES_QUANTITY("CUSPOLICYSALESQUANTITY", "政策期间销售数量(客户)", 14),

    CONDITIONS_MET("CONDITIONSMET", "达成条件", 15),

    REBATE_STANDARD("REBATESTANDARD", "返利标准", 16),

    PLAN_AMOUNT("PLANAMOUNT", "费用规划金额", 17);


    private String code;

    private String name;

    private Integer sort;
}
