package com.biz.crm.tpm.business.rebate.local.entity.criterion;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.util.Date;


/**
 * 销售
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
@Data
@Entity
@TableName("tpm_sale_volume_criterion")
@Table(name = "tpm_sale_volume_criterion", indexes = {
    @Index(name = "idx_rebate_policy_code", columnList = "sale_rebate_policy_code")
})
@org.hibernate.annotations.Table(appliesTo = "tpm_sale_volume_criterion", comment = "返利政策变量（基准），动态表单托管的")
@ApiModel(value = "Order", description = "订单主信息")
public class SaleVolumeCriterion extends UuidEntity {

  /**
   * 返利政策业务编号
   */
  @Column(name = "sale_rebate_policy_code", nullable = false, columnDefinition = "VARCHAR(128) COMMENT '返利政策业务编号'")
  @ApiModelProperty("返利政策业务编号")
  private String saleRebatePolicyCode;

  /**
   * 返利变量实例编码
   */
  @Column(name = "instance_code", columnDefinition = "VARCHAR(128) COMMENT '返利变量实例编码'")
  @ApiModelProperty("返利变量实例编码")
  private String instanceCode;

  /**
   * 返利变量说明
   */
  @Column(name = "description", columnDefinition = "VARCHAR(128) COMMENT '返利变量说明'")
  @ApiModelProperty("返利变量说明")
  private String description;

  /**
   * 时间类型: 自定义，上个月，上个季度，上一年
   */
  @Column(name = "time_type", length = 10, columnDefinition = "varchar(10) COMMENT '时间类型: 自定义，上个月，上个季度，上一年'")
  private String timeType;

  /**
   * 时间左
   */
  @Column(name = "left_time", columnDefinition = "date COMMENT '时间范围左'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date leftTime;

  /**
   * 时间右
   */
  @Column(name = "right_time", columnDefinition = "date COMMENT '时间范围右'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date rightTime;

  /**
   * 默认适配考核产品
   */
  @Column(name = "default_product", nullable = false, length = 1, columnDefinition = "char(1) COMMENT ' 默认适配考核产品 '")
  private Boolean defaultProduct;

  /**
   * 商品集合
   */
  @Column(name = "product_codes", columnDefinition = "VARCHAR(500) COMMENT '商品集合'")
  private String productCodes;

  /**
   * 产品层级集合
   */
  @Column(name = "product_levels", columnDefinition = "VARCHAR(500) COMMENT '产品层级集合'")
  private String productLevels;

}
