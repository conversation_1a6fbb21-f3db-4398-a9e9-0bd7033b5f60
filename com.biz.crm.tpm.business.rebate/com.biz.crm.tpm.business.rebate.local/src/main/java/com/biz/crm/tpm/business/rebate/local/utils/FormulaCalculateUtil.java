package com.biz.crm.tpm.business.rebate.local.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaCalculationLog;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateCalculationLog;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateConditionResultVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateFormulaResultVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaSaleRebatePolicyElementVo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description 公式计算工具
 * <AUTHOR>
 * @Date 2024/7/13 18:38
 */
public class FormulaCalculateUtil {


    /**
     * 根据返利条件表达式计算结果，变量值从变量对象中（替代值）获取 1、校验入参，两个入参均不能为空 2、从业务方获取变量值，替换到公式里 3、计算替换真实变量后的公式，如果计算失败，抛出异常
     *
     * @param expressStr 表达式字符串（包含变量编码）
     * @return
     * @Param speedNo 当次计算批次号
     */
    public static SaleRebateConditionResultVo computeConditionExpression(String expressStr, String expressStrName,
                                                                         Map<String, BigDecimal> amountMap, String customerCode) {
        Validate.notEmpty(expressStr, "解析条件表达式时，表达式不能为空");
        SaleRebateConditionResultVo result = replaceVariable(expressStr, expressStrName, amountMap, customerCode);
        String expressValue = result.getExpressValue();
        Boolean computeResult = MathUtil.computeCondition(expressValue);
        Validate.notNull(computeResult, "返利条件公式【%s】计算失败，请检查公式是否正确", expressStr);
        result.setValue(computeResult);
        result.setExpressStr(expressStr);
        result.setExpressStrName(expressStrName);
        return result;
    }

    /**
     * 根据表达式计算出结果，变量数值从业务方实现的接口中获取 1、校验入参，两个入参均不能为空 2、从业务方获取变量值，替换到公式里 3、计算替换真实变量后的公式，如果计算失败，抛出异常
     *
     * @param expressStr 表达式字符串（包含变量编码）
     * @return
     */
    public static SaleRebateFormulaResultVo computeRebateExpression(String expressStr, String expressStrName,
                                                                    Map<String, BigDecimal> amountMap, String customerCode) {
        //1、
        Validate.notEmpty(expressStr, "解析返利表达式时，表达式不能为空");
        //2、
        SaleRebateFormulaResultVo result = new SaleRebateFormulaResultVo();
        SaleRebateConditionResultVo conditionComputeResult = replaceVariable(expressStr, expressStrName, amountMap, customerCode);
        //3、
        String expressValue = conditionComputeResult.getExpressValue();
        BigDecimal computeResult = MathUtil.computeFormula(expressValue);
        Validate.notNull(computeResult, "返利公式【%s】计算失败，请检查公式是否正确", expressStr);
        result.setExpressStr(expressStr);
        result.setExpressStrName(expressStrName);
        result.setExpressValue(expressValue);
        result.setValue(computeResult);
        result.setComputeProcess(conditionComputeResult.getComputeProcess());
        result.setComputeProcessResult(conditionComputeResult.getComputeProcessResult());
        return result;
    }


    /**
     * 根据公式获取所有变量值，以键值对形式返回 1、校验入参 2、查询表达式中所有变量记录 3、遍历并检测表达式中包含哪些变量，包含的变量向业务方查询值，并替换到表达式里
     *
     * @param expressStr 表达式字符串
     * @return
     */
    public static SaleRebateConditionResultVo replaceVariable(String expressStr, String expressStrName,
                                                              Map<String, BigDecimal> amountMap, String customerCode) {
        Validate.notEmpty(expressStr, "替换公式变量时，公式字符串不能为空");
        SaleRebateConditionResultVo result = new SaleRebateConditionResultVo();
        result.setExpressStr(expressStr);
        result.setExpressStrName(expressStrName);
        Set<String> variables = MathUtil.getFormulaReplace(expressStr);
        Map<String, BigDecimal> variableMap = amountMap;
        String expressValue = expressStr;
        //变量获取传入参数
        String expressProperties = new String();
        //变量获取结果
        String expressProcess = new String();
        for (String entry : variables) {
            String k = entry + customerCode;
            String v = variableMap.get(k).toString();
            Validate.notEmpty(v, "%s时检测到变量【%s】值为空", k);
            //替换变量实际值
            expressValue = expressValue.replace(entry, v);
            //设置变量获取传入参数
            expressProperties = String.join(";", expressProperties,
                    String.join(",", String.join(":", "变量", entry),
                            String.join(":", "取值", v)
                    ));
            //设置变量获取结果
            expressProcess = String.join(";", expressProcess, String.join(":", k, v));
        }
        //截掉第一个字符‘,'
        if (StringUtils.isNotEmpty(expressProperties)) {
            expressProperties = expressProperties.substring(1);
        }
        if (StringUtils.isNotEmpty(expressProcess)) {
            expressProcess = expressProcess.substring(1);
        }
        result.setExpressValue(expressValue);
        result.setComputeProcessResult(expressProperties);
        result.setComputeProcess(expressProcess);
        return result;
    }


    /**
     * 公式中包含得基准
     *
     * @param formulaInfos
     * <AUTHOR>
     * @date
     */
    public static Set<String> getFormulaCriterionSet(List<FormulaSaleRebatePolicyElementVo> formulaInfos) {
        Set<String> conditionAndFormula = new HashSet<>();
        for (FormulaSaleRebatePolicyElementVo policyFormula : formulaInfos) {
            Set<String> saleRebateCondition = MathUtil
                    .getFormulaReplace(policyFormula.getSaleRebatePolicyCondition());
            Set<String> saleRebateformula = MathUtil
                    .getFormulaReplace(policyFormula.getSaleRebatePolicyFormula());
            conditionAndFormula.addAll(saleRebateformula);
            conditionAndFormula.addAll(saleRebateCondition);
        }
        return conditionAndFormula;
    }

    /**
     * 获取条件公式
     *
     * @param formulaInfos
     * @return
     */
    public static Set<String> getConditionFormulaSet(List<FormulaSaleRebatePolicyElementVo> formulaInfos) {
        Set<String> conditionAndFormula = new HashSet<>();
        for (FormulaSaleRebatePolicyElementVo policyFormula : formulaInfos) {
            Set<String> saleRebateCondition = MathUtil
                    .getFormulaReplace(policyFormula.getSaleRebatePolicyCondition());
            conditionAndFormula.addAll(saleRebateCondition);
        }
        return conditionAndFormula;
    }

    /**
     * 获取结果公式
     *
     * @param formulaInfos
     * @return
     */
    public static Set<String> getResultFormulaSet(List<FormulaSaleRebatePolicyElementVo> formulaInfos) {
        Set<String> conditionAndFormula = new HashSet<>();
        for (FormulaSaleRebatePolicyElementVo policyFormula : formulaInfos) {
            Set<String> saleRebateCondition = MathUtil
                    .getFormulaReplace(policyFormula.getSaleRebatePolicyFormula());
            conditionAndFormula.addAll(saleRebateCondition);
        }
        return conditionAndFormula;
    }


    /**
     * 保存计算日志数据
     *
     * @param conditionResult
     * @param executeComputeResult
     */
    public static SaleRebateCalculationLog buildComputeLog(FormulaCalBaseVo baseVo, SaleRebateConditionResultVo conditionResult, SaleRebateFormulaResultVo executeComputeResult) {
        Validate.notNull(conditionResult, "保存明细数据时，计算结果不能为空");
        SaleRebateCalculationLog saleRebateCalculationLog = new SaleRebateCalculationLog();
        saleRebateCalculationLog.setSaleRebatePolicyCode(baseVo.getActCode());
        saleRebateCalculationLog.setCustomerCode(baseVo.getCustomerCode());
        saleRebateCalculationLog.setCustomerName(baseVo.getCustomerName());
        saleRebateCalculationLog
                .setSaleRebateCalculationYears(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY)));
        saleRebateCalculationLog.setSaleRebatePolicyCondition(conditionResult.getExpressStrName());
        saleRebateCalculationLog
                .setSaleRebatePolicyConditionProcess(conditionResult.getComputeProcessResult());
        saleRebateCalculationLog.setSaleRebateResults(conditionResult.isValue());
        if (Objects.nonNull(executeComputeResult)) {
            saleRebateCalculationLog.setSaleRebatePolicyFormula(executeComputeResult.getExpressStrName());
            saleRebateCalculationLog
                    .setSaleRebatePolicyFormulaProcess(executeComputeResult.getComputeProcessResult());
            saleRebateCalculationLog.setSaleRebateCalculationResults(executeComputeResult.getValue());
        }
        saleRebateCalculationLog.setIsTest(BooleanEnum.FALSE.getNumStr());
        saleRebateCalculationLog.setSaleRebatePolicyDetailId(baseVo.getActDetailCode());
        return saleRebateCalculationLog;
    }


    /**
     * 构建计算日志
     *
     * @param baseVo
     * @param conditionResult
     * @param executeComputeResult
     * @return
     */
    public static FormulaCalculationLog buildFormulaLog(FormulaCalBaseVo baseVo, FormulaConfigVo formulaConfigVo, String calType,
                                                        SaleRebateConditionResultVo conditionResult, SaleRebateFormulaResultVo executeComputeResult) {
        FormulaCalculationLog log = new FormulaCalculationLog();
        log.setBusinessCode(baseVo.getActCode());
        log.setBusinessDetailCode(baseVo.getActDetailCode());
        log.setFormulaCode(formulaConfigVo.getFormulaConfigCode());
        log.setFormulaName(formulaConfigVo.getFormulaConfigName());
        log.setCustomerCode(baseVo.getCustomerCode());
        log.setCustomerName(baseVo.getCustomerName());
        log.setCalCondition(baseVo.getConditionNum());
        log.setCalResult(baseVo.getGiveNum());
        log.setCalType(calType);
        if (Objects.isNull(executeComputeResult)) {
            return log;
        }
        List<BigDecimal> bigDecimalList = extractNumbers(executeComputeResult.getExpressValue());
        if (CollectionUtil.isEmpty(bigDecimalList)) {
            return log;
        }
        if (bigDecimalList.size() > 1) {
            log.setActualConditionAmount(bigDecimalList.get(0));
            log.setActualResultAmount(bigDecimalList.get(1));
        } else {
            log.setActualResultAmount(bigDecimalList.get(0));
        }
        log.setResultAmount(executeComputeResult.getValue());
        return log;
    }


    public static List<BigDecimal> extractNumbers(String expression) {
        List<BigDecimal> numbers = new ArrayList<>();
        // 正则表达式匹配所有数字
        Pattern pattern = Pattern.compile("\\d+\\.\\d+|\\d+");
        Matcher matcher = pattern.matcher(expression);

        while (matcher.find()) {
            // 将匹配到的数字转换为整数并添加到列表中
            numbers.add(new BigDecimal(matcher.group()));
        }

        return numbers;
    }

    public static Boolean paramNotNull(Object filed, String msg, StringJoiner errMsg) {
        if (ObjectUtils.isEmpty(filed)) {
            errMsg.add(msg + "不能为空");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
