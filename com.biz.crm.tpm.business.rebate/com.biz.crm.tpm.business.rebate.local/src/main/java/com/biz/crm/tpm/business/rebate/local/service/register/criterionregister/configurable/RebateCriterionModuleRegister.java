package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.configurable;

import com.biz.crm.common.form.sdk.module.ModuleRegister;
import org.springframework.stereotype.Component;

/**
 * 返利政策变量（基准）动态表单模块
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
@Component
public class RebateCriterionModuleRegister implements ModuleRegister {

  @Override
  public String moduleCode() {
    return RebateCriterionModuleRegister.class.getName();
  }

  @Override
  public String moduleName() {
    return "返利政策变量（基准）动态表单模块";
  }

  @Override
  public int getOrder() {
    return 0;
  }
}
