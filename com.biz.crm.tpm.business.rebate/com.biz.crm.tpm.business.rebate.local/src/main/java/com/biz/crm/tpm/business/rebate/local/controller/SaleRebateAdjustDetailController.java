package com.biz.crm.tpm.business.rebate.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateAdjustDetail;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateAdjustDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 返利调整明细controller
 * @author: lifei
 * @date: 2022/4/11 14:40
 */
@RestController
@RequestMapping("/v1/saleRebateAdjustDetail/saleRebateAdjustDetail")
@Slf4j
@Api(tags = "返利模块:SaleRebateAdjustDetail:返利调整明细")
public class SaleRebateAdjustDetailController {

  @Autowired(required = false)
  private SaleRebateAdjustDetailService saleRebateAdjustDetailService;
  /**
   * 分页查询
   *
   * @param pageable         分页对象
   * @param saleRebateAdjustDetail 查询实体
   * @return
   */
  @ApiOperation(value = "分页查询")
  @GetMapping("findByConditions")
  public Result<Page<SaleRebateAdjustDetail>> findByConditions(
      @ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
      @ApiParam(name = "saleRebateAdjustDetail", value = "返利调整明细") SaleRebateAdjustDetail saleRebateAdjustDetail) {
    try {
      Page<SaleRebateAdjustDetail> page = this.saleRebateAdjustDetailService
          .findByConditions(pageable, saleRebateAdjustDetail);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
