package com.biz.crm.tpm.business.rebate.local.strategy;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyScopeInfo;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyScopeInfoService;
import com.biz.crm.tpm.business.rebate.sdk.strategy.SaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.sdk.vo.scope.SaleRebatePolicyScopeOrgInfoVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 该策略支持从企业组织和客户的关联信息中，来选定客户范围
 *
 * <AUTHOR>
 */
@Component
public class OrgForSaleRebateCustomerScopeStrategy extends
AbstractSaleRebateCustomerScopeStrategy implements
    SaleRebateCustomerScopeStrategy<SaleRebatePolicyScopeOrgInfoVo> {

  @Autowired(required = false)
  private SaleRebatePolicyScopeInfoService saleRebatePolicyScopeInfoService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private CustomerVoService customerVoService;

  private static final String SCOPE_TYPE = "orgForSalePolicyCustomer";

  private static final Integer SCOPE_SORT = 3;

  @Override
  public String getScopeType() {
    return SCOPE_TYPE;
  }

  @Override
  public String getScopeTypeDesc() {
    return "组织机构";
  }

  @Override
  public Integer getSort() {
    return SCOPE_SORT;
  }

  /**
   * 按照组织机构来进行优惠政策范围圈定的客户编码集合
   *
   * @param salePolicyCode 需要查询的优惠政策业务编号
   * @param tenantCode     当前二级租户信息
   * @return
   */
  @Override
  public Set<String> onRequestCustomerCodes(String salePolicyCode, String tenantCode) {
    if (StringUtils.isAnyBlank(tenantCode, salePolicyCode)) {
      return null;
    }
    //查询组织机构范围的客户结果
    List<SaleRebatePolicyScopeInfo> salePolicyScopeInfos = this.saleRebatePolicyScopeInfoService
        .findByTenantCodeAndSalePolicyCodeAndCustomerScopeType(tenantCode, salePolicyCode,
            SCOPE_TYPE);
    if (CollectionUtils.isEmpty(salePolicyScopeInfos)) {
      return null;
    }
    //筛选组织机构
    Set<String> customerCodes = this.buildCustomerCodes(salePolicyScopeInfos);
    if (CollectionUtils.isEmpty(customerCodes)) {
      return null;
    }
    return customerCodes;
  }

  /**
   * 筛选可以查看的组织机构
   *
   * @param salePolicyScopeInfos
   * @return
   */
  private Set<String> buildCustomerCodes(List<SaleRebatePolicyScopeInfo> salePolicyScopeInfos) {
    //选定方式分组转成组织机构范围的组织机构编码map
    //1、======
    List<String> includeList = salePolicyScopeInfos.stream()
        .filter(a -> StringUtils.isNotBlank(a.getCode())).map(SaleRebatePolicyScopeInfo::getCode)
        .collect(
            Collectors.toList());
    List<CustomerVo> includeCustomerCodeList = this.customerVoService
        .findByOrgCodes(includeList);
    if (CollectionUtils.isEmpty(includeCustomerCodeList)) {
      return null;
    }
    Set<String> includeCustomerCodes = includeCustomerCodeList.stream()
        .map(CustomerVo::getCustomerCode).collect(Collectors.toSet());
    return includeCustomerCodes;
  }
}
