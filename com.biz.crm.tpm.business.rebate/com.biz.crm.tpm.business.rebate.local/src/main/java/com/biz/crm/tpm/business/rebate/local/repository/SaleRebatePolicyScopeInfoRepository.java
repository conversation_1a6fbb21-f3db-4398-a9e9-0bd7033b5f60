package com.biz.crm.tpm.business.rebate.local.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyScopeInfo;
import com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyScopeInfoMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;


/**
 * 以圈定某种范围类型的方式，确认特定返利政策的适用客户范围（标品中支持经销商、渠道和组织机构三种圈定方式(SaleRebatePolicyScopeInfo)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:58
 */
@Component
public class SaleRebatePolicyScopeInfoRepository extends
    ServiceImpl<SaleRebatePolicyScopeInfoMapper, SaleRebatePolicyScopeInfo> {

  /**
   * 按照数据的技术编号，删除优惠政策选择范围信息
   *
   * @param ids
   */
  public void deleteByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return;
    }
    LambdaQueryWrapper<SaleRebatePolicyScopeInfo> lambdaQuery = Wrappers.<SaleRebatePolicyScopeInfo>lambdaQuery();
    lambdaQuery.in(SaleRebatePolicyScopeInfo::getId, Lists.newArrayList(ids));
    //新增租户编号判断
    lambdaQuery.eq(SaleRebatePolicyScopeInfo::getTenantCode,TenantUtils.getTenantCode());
    this.baseMapper.delete(lambdaQuery);
  }

  /**
   * 分页查询
   *
   * @param pageable                  分页
   * @param saleRebatePolicyScopeInfo
   * @return
   */
  public Page<SaleRebatePolicyScopeInfo> findByConditions(Pageable pageable,
      SaleRebatePolicyScopeInfo saleRebatePolicyScopeInfo) {
    Page<SaleRebatePolicyScopeInfo> page = new Page<>(pageable.getPageNumber(),
        pageable.getPageSize());
    //新增租户编号判断
    saleRebatePolicyScopeInfo.setTenantCode(TenantUtils.getTenantCode());
    Page<SaleRebatePolicyScopeInfo> pageList = this.baseMapper
        .findByConditions(page, saleRebatePolicyScopeInfo);
    return pageList;
  }

  /**
   * 根据租户编码、优惠政策业务编号、客户范围控制策略的识别类型号查询数据集合
   *
   * @param tenantCode           租户编码
   * @param saleRebatePolicyCode 优惠政策业务编号
   * @param customerScopeType    客户范围控制策略的识别类型号
   * @return
   */
  public List<SaleRebatePolicyScopeInfo> findByTenantCodeAndSalePolicyCodeAndCustomerScopeType(
      String tenantCode, String saleRebatePolicyCode, String customerScopeType) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyScopeInfo::getTenantCode, tenantCode)
        .eq(SaleRebatePolicyScopeInfo::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .eq(SaleRebatePolicyScopeInfo::getCustomerScopeType, customerScopeType)
        .list();
  }

  /**
   * 根据返利政策编码查询
   *
   * @param saleRebatePolicyCode
   * @return
   */
  public List<SaleRebatePolicyScopeInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyScopeInfo::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .eq(SaleRebatePolicyScopeInfo::getTenantCode, TenantUtils.getTenantCode())
        .list();
  }

  /**
   * 删除
   *
   * @param saleRebatePolicyCode
   * @return
   */
  public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    this.lambdaUpdate()
        .eq(SaleRebatePolicyScopeInfo::getTenantCode, TenantUtils.getTenantCode())
        .eq(SaleRebatePolicyScopeInfo::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .remove();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(SaleRebatePolicyScopeInfo::getTenantCode,tenantCode)
        .in(SaleRebatePolicyScopeInfo::getId,ids)
        .remove();
  }
}

