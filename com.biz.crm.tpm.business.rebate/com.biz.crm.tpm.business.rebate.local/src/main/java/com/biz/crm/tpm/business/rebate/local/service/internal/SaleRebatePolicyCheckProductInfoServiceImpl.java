package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyCheckProductInfo;
import com.biz.crm.tpm.business.rebate.local.repository.SaleRebatePolicyCheckProductInfoRepository;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyCheckProductInfoService;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleRebatePolicyProductTypeEnum;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 返利政策考核商品(SaleRebatePolicyCheckProductInfo)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
@Service("saleRebatePolicyCheckProductInfoService")
public class SaleRebatePolicyCheckProductInfoServiceImpl implements
        SaleRebatePolicyCheckProductInfoService {

  @Autowired(required = false)
  private SaleRebatePolicyCheckProductInfoRepository saleRebatePolicyCheckProductInfoRepository;
  @Autowired(required = false)
  private ProductVoService productVoService;

  /**
   * id集合删除
   *
   * @param idList id集合
   * @return Result
   */
  @Transactional
  @Override
  public void delete(List<String> idList) {
    Validate.isTrue(!CollectionUtils.isEmpty(idList), "传入id集合为空");
    this.saleRebatePolicyCheckProductInfoRepository.removeByIdsAndTenantCode(idList,TenantUtils.getTenantCode());
  }

  /**
   * 返利编码删除
   *
   * @param saleRebatePolicyCode 返利编码
   * @return Result
   */
  @Transactional
  @Override
  public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    Validate.notBlank(saleRebatePolicyCode, "传入返利编码为空");
    this.saleRebatePolicyCheckProductInfoRepository.deleteBySaleRebatePolicyCode(saleRebatePolicyCode);
  }

  /**
   * 通过返利政策编码查询考核产品
   *
   * @param saleRebatePolicyCodes
   * @return
   */
  @Override
  public List<SaleRebatePolicyCheckProductInfo> findBySaleRebatePolicyCodes(
      List<String> saleRebatePolicyCodes) {
    if (CollectionUtils.isEmpty(saleRebatePolicyCodes)) {
      return new ArrayList<>(0);
    }
    return this.saleRebatePolicyCheckProductInfoRepository
        .findBySaleRebatePolicyCodes(saleRebatePolicyCodes);
  }

  @Override
  public List<SaleRebatePolicyCheckProductInfo> findBySaleRebatePolicyCode(
      String saleRebatePolicyCode) {
    if (StringUtils.isEmpty(saleRebatePolicyCode)) {
      return new ArrayList<>(0);
    }
    return this.saleRebatePolicyCheckProductInfoRepository
        .findBySaleRebatePolicyCode(saleRebatePolicyCode);
  }

  /**
   * 批量新增
   *
   * @param saleRebatePolicyCheckProductInfos
   */
  @Transactional(rollbackFor = RuntimeException.class)
  @Override
  public void createBatch(
      List<SaleRebatePolicyCheckProductInfo> saleRebatePolicyCheckProductInfos) {
      Validate.isTrue(!CollectionUtils.isEmpty(saleRebatePolicyCheckProductInfos), "考核产品新增数据不存在");
      Validate.isTrue(saleRebatePolicyCheckProductInfos.size() == 1, "考核产品只能选择一条");
      SaleRebatePolicyCheckProductInfo info = saleRebatePolicyCheckProductInfos.get(0);
      this.createValidate(info);
      if (StringUtils.equals(SaleRebatePolicyProductTypeEnum.PRODUCT.getDictCode(), info.getType())) { 
          List<ProductVo> productVoList = this.productVoService.findDetailsByIdsOrProductCodes(null,
                                                                                               Lists.newArrayList(info.getCode()));
          Validate.notEmpty(productVoList, "产品不存在");
          Validate.isTrue(productVoList.size() == 1, "根据产品编码[%s]找到多条产品", info.getCode());
          ProductVo productVo = productVoList.get(0);
          info.setName(productVo.getProductName());
          info.setMaterialCode(productVo.getMaterialCode());
      }
      this.saleRebatePolicyCheckProductInfoRepository.saveBatch(saleRebatePolicyCheckProductInfos);
  }



  /**
   * 新增验证
   *
   * @param saleRebatePolicyCheckProductInfo
   */
  private void createValidate(SaleRebatePolicyCheckProductInfo saleRebatePolicyCheckProductInfo) {
    Validate.notNull(saleRebatePolicyCheckProductInfo, "考核商品数据操作时，对象信息不能为空！");
    saleRebatePolicyCheckProductInfo.setId(null);
    saleRebatePolicyCheckProductInfo.setTenantCode(TenantUtils.getTenantCode());
    Validate.notBlank(saleRebatePolicyCheckProductInfo.getTenantCode(), "考核商品数据操作时，租户编号不能为空！");
    Validate.notBlank(saleRebatePolicyCheckProductInfo.getSaleRebatePolicyCode(),
        "考核商品数据操作时，返利政策业务编号不能为空！");
    Validate.notBlank(saleRebatePolicyCheckProductInfo.getType(), "考核商品数据操作时，范围类型不能为空！");
    Validate.notBlank(saleRebatePolicyCheckProductInfo.getCode(), "考核商品数据操作时，商品或产品层级编码不能为空！");
    Validate.notBlank(saleRebatePolicyCheckProductInfo.getName(), "考核商品数据操作时，商品或产品层级名称不能为空！");
  }
}

