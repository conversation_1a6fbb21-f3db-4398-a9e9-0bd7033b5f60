package com.biz.crm.tpm.business.rebate.local.service;

import com.biz.crm.tpm.business.rebate.local.entity.TpmRebatePolicyRelaFormulaConfig;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;

import java.util.List;
import java.util.Map;

/**
 * @Description 返利政策关联公式配置
 * <AUTHOR>
 * @Date 2024/6/12 11:46
 */
public interface RebatePolicyRelaFormulaConfigService {

    /**
     * 批量保存返利政策与公式配置的关联关系
     * @param configList 公式配置
     * @return 键-公式类型,值-公式配置
     */
    Map<String, FormulaConfigVo> saveBatch(List<TpmRebatePolicyRelaFormulaConfig> configList);

    /**
     * 根据返利政策编码查询公式配置
     * @param saleRebatePolicyCode 返利政策编码
     * @return 键-公式类型,值-公式配置
     */
    Map<String, FormulaConfigVo> findFormulaConfigMapBySaleRebatePolicyCode(String saleRebatePolicyCode);

    void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode);
}
