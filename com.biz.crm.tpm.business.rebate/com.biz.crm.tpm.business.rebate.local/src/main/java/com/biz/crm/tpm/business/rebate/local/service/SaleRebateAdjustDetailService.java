package com.biz.crm.tpm.business.rebate.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateAdjustDetail;

import java.util.List;

import org.springframework.data.domain.Pageable;

/**
 * @description: 返利调整明细service
 * @author: lifei
 * @date: 2022/4/11 14:43
 */
public interface SaleRebateAdjustDetailService {
  /**
   * 分页查询
   *
   * @param pageable         分页参数
   * @param saleRebateAdjustDetail
   * @return
   */
  Page<SaleRebateAdjustDetail> findByConditions(Pageable pageable, SaleRebateAdjustDetail saleRebateAdjustDetail);

  /**
   * 返利明细编码查询集合
   * @param saleRebateDetailCode 返利明细编码查询集合
   * @return 返利明细编码查询集合
   */
  List<SaleRebateAdjustDetail> findBySaleRebateDetailCode(String saleRebateDetailCode);


  /**
   * 新增数据
   * @param saleRebateAdjustDetail 实体对象
   * @return 新增结果
   */
  SaleRebateAdjustDetail create(SaleRebateAdjustDetail saleRebateAdjustDetail);

}
