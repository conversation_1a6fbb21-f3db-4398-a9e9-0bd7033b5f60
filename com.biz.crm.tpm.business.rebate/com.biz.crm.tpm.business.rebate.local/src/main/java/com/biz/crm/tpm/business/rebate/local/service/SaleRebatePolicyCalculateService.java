package com.biz.crm.tpm.business.rebate.local.service;

import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateComputeParamVo;
import java.util.Date;
import java.util.List;

/**
 * @description: 返利计算servie
 * @author: lifei
 * @date: 2022/3/7 13:47
 */
public interface SaleRebatePolicyCalculateService {

  /**
   * 根据code批量计算返利  （维度 客户 + 返利产品 ），如果没有返利产品 上账类型一定是现金费用类型
   * 1、循环查询并组装出来最低粒度
   * 2、返利名称
   * <p>
   * 注意：最低粒度单独用类来表达，（最低粒度三个属性 + 政策编码 + 该次计算的批次号[随机生成 一次计算只有一个版本号，为了真正计算的时候重复查询变量值]）
   */
  void  onCalculateByCode(List<String> saleRebatePolicyCode,String isTest, Date date);

  /**
   *
   * </br>
   * @param detailIds 返利明细 重新计算
   * <AUTHOR>
   * @date
   */
  void onCalculateByDetailIds(List<String> detailIds);

  /**
   * 返利计算
   * @param saleRebateComputeParamVos
   */
  void  onCalculate(List<SaleRebateComputeParamVo> saleRebateComputeParamVos);

}
