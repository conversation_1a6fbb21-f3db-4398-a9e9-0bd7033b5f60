package com.biz.crm.tpm.business.rebate.local.service.register.cycleregister;

import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyTaskService;
import com.biz.crm.tpm.business.rebate.local.utils.SaleRebateCycleUtil;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.enums.CycleTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCycleRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateExecutionDateRangeVo;
import com.biz.crm.mdm.business.fiscal.year.sdk.service.FiscalYearVoService;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearVo;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @description: 年度返利
 * @author: lifei
 * @date: 2022/2/21 14:11
 */
@Component
@Slf4j
public class YearCycleRegisterImpl implements SaleRebatePolicyCycleRegister {


  @Autowired(required = false)
  private FiscalYearVoService fiscalYearVoService;

  /**
   * 返利周期名称
   */
  private static final String REBATE_POLICY_CYCLE_NAME = "年度返利";

  /**
   * 返利周期编码
   */
  private static final String REBATE_POLICY_CYCLE_CODE = "YEARCYCLE";

  /**
   * 年度返利基础定时任务表达式
   */
  public static final String YEAR_CYCLE_CRON = "0 0 0 1 1 ? *";

  /**
   * 排序
   */
  private static final Integer REBATE_POLICY_CYCLE_SORT = 1;

  @Autowired(required = false)
  private SaleRebatePolicyTaskService saleRebatePolicyTaskService;

  @Override
  public String getSaleRebatePolicyCycleCode() {
    return REBATE_POLICY_CYCLE_CODE;
  }

  @Override
  public String getSaleRebatePolicyCycleName() {
    return REBATE_POLICY_CYCLE_NAME;
  }

  /**
   * 获取返利周期定时任务表达式
   */
  @Override
  public String getSaleRebateCronExpression() {
    return YEAR_CYCLE_CRON;
  }

  /**
   * 判断是否需要计算财年偏移量
   *
   * @return
   */
  @Override
  public boolean getCheckFiscalYear() {
    return true;
  }

  /**
   * 返利周期类型
   */
  @Override
  public int getCycleType() {
    return Calendar.YEAR;
  }

  /**
   * 周期最小单位
   */
  @Override
  public int getCycleNum() {
    return 1;
  }

  @Override
  public Integer getCycleSort() {
    return REBATE_POLICY_CYCLE_SORT;
  }

  /**
   * （创建定时任务）
   *
   * <AUTHOR>
   * @date
   */
  @Override
  public void onRequestcreate(SaleRebatePolicyDto saleRebatePolicyDto) {
    saleRebatePolicyTaskService.createRebatetask(saleRebatePolicyDto);
  }

  /**
   * （修改定时任务）
   *
   * <AUTHOR>
   * @date
   */
  @Override
  public void onRequestUpdate(SaleRebatePolicyDto saleRebatePolicyDto) {
    saleRebatePolicyTaskService.updateRebatetask(saleRebatePolicyDto);
  }

  /**
   * 获取当天定时任务执行范围
   *
   * <AUTHOR>
   * @date
   */
  @Override
  public SaleRebateExecutionDateRangeVo getExecutionDateRangeVo(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = null;
    SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
    String format = formatDate.format(saleRebatePolicyDto.getCalculateTime());


    if (CycleTypeEnum.CIVIL_YEAR.getKey().equals(saleRebatePolicyDto.getCycleType())) {
      Map<String, SaleRebateExecutionDateRangeVo> saleRebateExecutionDateMap = SaleRebateCycleUtil
          .getSaleRebateExecutionYearMap(saleRebatePolicyDto);
      saleRebateExecutionDateRangeVo = saleRebateExecutionDateMap
          .get(format);
    } else {
      List<FiscalYearVo> fiscalYearVoListByDate = this.fiscalYearVoService
          .findByBeginTimeAndEndTime(saleRebatePolicyDto.getSaleRebateStartTime(), saleRebatePolicyDto.getSaleRebateEndTime());
      //拿到自然时间范围内的财年数据 如果有数据替换开始时间 结束时间
      if (!CollectionUtils.isEmpty(fiscalYearVoListByDate)) {
        for (FiscalYearVo fiscalYearVo : fiscalYearVoListByDate) {
          if (fiscalYearVo.getBeginTime().before(saleRebatePolicyDto.getSaleRebateStartTime())) {
            fiscalYearVo.setBeginTime(saleRebatePolicyDto.getSaleRebateStartTime());
          }
          if (fiscalYearVo.getEndTime().after(saleRebatePolicyDto.getSaleRebateEndTime())) {
            fiscalYearVo.setEndTime(saleRebatePolicyDto.getSaleRebateEndTime());
          }
        }
        //通过财年分组
        Map<String, List<FiscalYearVo>> listMap = fiscalYearVoListByDate.stream()
            .collect(Collectors.groupingBy(FiscalYearVo::getYear));
        for (String s :listMap.keySet()){
          List<FiscalYearVo> fiscalYearVos = listMap.get(s);
          Date endTime = fiscalYearVos.stream().max(Comparator.comparing(FiscalYearVo::getEndTime)).get().getEndTime();
          Date begin = fiscalYearVos.stream().min(Comparator.comparing(FiscalYearVo::getBeginTime)).get().getBeginTime();
          //执行日历
          Calendar calendarexec = Calendar.getInstance();
          calendarexec.setTime(endTime);
          calendarexec.set(Calendar.DAY_OF_YEAR,
              calendarexec.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum()+1);
          String execDate = formatDate.format(calendarexec.getTime());
          if(execDate.equals(format)){
            saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
            saleRebateExecutionDateRangeVo.setSaleRebateStartTime(begin);
            saleRebateExecutionDateRangeVo.setSaleRebateEndTime(endTime);
          }
        }
      }
    }
    return saleRebateExecutionDateRangeVo;
  }

}
