package com.biz.crm.tpm.business.rebate.local.service.internal;



import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.rebate.local.repository.SaleRebateCalculationLogRepository;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateCalculationLog;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateCalculationLogService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;

/**
 * 返利政策计算日志(SaleRebateCalculationLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-28 15:19:44
 */
@Service("saleRebateCalculationLogService")
public class SaleRebateCalculationLogServiceImpl implements SaleRebateCalculationLogService {

  @Autowired(required = false)
  private SaleRebateCalculationLogRepository saleRebateCalculationLogRepository;

  /**
   * 通过返利明细id查询返利计算日志数据
   * @param saleRebatePolicyDetailId 返利明细id
   * @return 分页数据
   */
  @Override
  public Page<SaleRebateCalculationLog> findByDetailId(Pageable pageable,
                                                       String saleRebatePolicyDetailId) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (StringUtils.isBlank(saleRebatePolicyDetailId)) {
      return null;
    }
    SaleRebateCalculationLog saleRebateCalculationLog = new SaleRebateCalculationLog();
    saleRebateCalculationLog.setSaleRebatePolicyDetailId(saleRebatePolicyDetailId);
    saleRebateCalculationLog.setTenantCode(TenantUtils.getTenantCode());
    return this.saleRebateCalculationLogRepository.findByConditions(pageable, saleRebateCalculationLog);
  }

  /**
   * 新增数据
   * @param saleRebateCalculationLog 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public SaleRebateCalculationLog create(SaleRebateCalculationLog saleRebateCalculationLog) {
    this.createValidate(saleRebateCalculationLog);
    saleRebateCalculationLog.setTenantCode(TenantUtils.getTenantCode());
    this.saleRebateCalculationLogRepository.saveOrUpdate(saleRebateCalculationLog);
    return saleRebateCalculationLog;
  }
  


  @Override
  public void deleteByRebateCodeAndTest(String saleRebatePolicyCode, String isTest, String speedNo) {
    //1、
    Validate.notEmpty(saleRebatePolicyCode, "政策编码不能为空");
    Validate.isTrue(BooleanEnum.TRUE.getNumStr().equals(isTest), "测试状态只能传是（1）");
    //2、
    this.saleRebateCalculationLogRepository.deleteByRebateCodeAndTest(saleRebatePolicyCode,isTest,speedNo);
  }

  /**
   * 创建验证
   * @param saleRebateCalculationLog
   */
  private void createValidate(SaleRebateCalculationLog saleRebateCalculationLog) {
    //补充数据
    saleRebateCalculationLog.setTenantCode(TenantUtils.getTenantCode());
    saleRebateCalculationLog.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    saleRebateCalculationLog.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    //校验
    Validate.notNull(saleRebateCalculationLog, "新增时，对象信息不能为空！");
	  saleRebateCalculationLog.setId(null);
    Validate.notNull(saleRebateCalculationLog.getTenantCode(), "新增数据时，租户编号不能为空！");
    Validate.notNull(saleRebateCalculationLog.getSaleRebateCalculationYears(), "新增数据时，返利计算时间年月不能为空！");
    saleRebateCalculationLog.setSaleRebatePolicyCode(ObjectUtils.defaultIfNull(saleRebateCalculationLog.getSaleRebatePolicyCode(),"Test-"+ UuidCrmUtil.randomUuid()));
    Validate.notNull(saleRebateCalculationLog.getSaleRebatePolicyCode(), "新增数据时，返利编码不能为空！");
    Validate.notNull(saleRebateCalculationLog.getSaleRebatePolicyCondition(), "新增数据时，返利条件不能为空！");
//    Validate.notNull(saleRebateCalculationLog.getSaleRebatePolicyName(), "新增数据时，返利名称不能为空！");
    Validate.notNull(saleRebateCalculationLog.getSaleRebateResults(), "新增数据时，返利条件结果不能为空！");
     
  }

}

