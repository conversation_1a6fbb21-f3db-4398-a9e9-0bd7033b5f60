package com.biz.crm.tpm.business.rebate.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaCalculationLog;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/28 18:07
 */
public interface FormulaCalculationLogService {

    Page<FormulaCalculationLog> findPage(Pageable pageable, FormulaCalculationLog formulaCalculationLog);

    List<FormulaCalculationLog> findListByDetailCodeAndCalType(String detailCode, String calType);

    void saveBatchList(List<FormulaCalculationLog> list);
}
