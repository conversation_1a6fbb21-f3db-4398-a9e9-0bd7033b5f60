package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description: 返利政策主表
 * @author: lifei
 * @date: 2022/2/15 14:39
 */
@Getter
@Setter
@TableName("tpm_sale_rebate_policy")
@Entity
@Table(name = "tpm_sale_rebate_policy" , indexes = {@Index(columnList = "tenant_code , sale_rebate_policy_code" , unique = true),
    @Index(columnList = "tenant_code, sale_rebate_start_time , sale_rebate_end_time")})
@org.hibernate.annotations.Table(appliesTo = "tpm_sale_rebate_policy", comment = "返利政策，按照租户进行隔离")
public class SaleRebatePolicy extends TenantFlagOpEntity {

  private static final long serialVersionUID = 4598956993151944736L;
  /**
   * 返利编码（一旦创建，不允许修改），不输入就是系统生成(按照租户生成)
   */
  @Column(name = "sale_rebate_policy_code" , length = 225 , nullable = false, columnDefinition = "VARCHAR(225) COMMENT '促销编码'")
  @ApiModelProperty("返利编码")
  private String saleRebatePolicyCode;

  /**
   * 返利名称
   */
  @Column(name = "sale_rebate_policy_name" , length = 128 , nullable = false, columnDefinition = "VARCHAR(128) COMMENT '促销名称'")
  @ApiModelProperty("返利名称")
  private String saleRebatePolicyName;

  /**
   * 返利状态
   */
  @Column(name = "sale_rebate_policy_status" ,  columnDefinition = "int(5) COMMENT '返利状态'")
  @ApiModelProperty("返利状态")
  private Integer saleRebatePolicyStatus;

  /**
   * 周期类型 自然年 企业财年
   */
  @Column(name = "cycle_type" , columnDefinition = "int(5) COMMENT '周期类型 自然年 企业财年'")
  @ApiModelProperty("周期类型")
  private Integer cycleType;

  /**
   * 返利周期
   */
  @Column(name = "sale_rebate_policy_cycle" , columnDefinition = "varchar(64) COMMENT '返利周期'")
  @ApiModelProperty("返利周期")
  private String saleRebatePolicyCycle;

  /**
   * 返利周期
   */
  @Column(name = "sale_rebate_policy_cycle_name" , columnDefinition = "varchar(64) COMMENT '返利周期名称'")
  @ApiModelProperty("返利周期名称")
  private String saleRebatePolicyCycleName;

  /**
   * 上账方式 手动 自动
   */
  @Column(name = "bill_type" , columnDefinition = "int(5) COMMENT '上账方式 手动 自动'")
  @ApiModelProperty("上账方式")
  private Integer billType;

  /**
   * 返利政策开始时间（包括）
   */
  @Column(name = "sale_rebate_start_time" , nullable = false, columnDefinition = "Datetime COMMENT '返利政策开始时间（包括）'")
  @ApiModelProperty("返利政策开始时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateStartTime;

  /**
   * 返利政策结束时间（包括）
   */
  @Column(name = "sale_rebate_end_time" , nullable = false, columnDefinition = "Datetime COMMENT '返利政策结束时间（包括）'")
  @ApiModelProperty("返利政策结束时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date saleRebateEndTime;

  /**
   * 返利类型 货补 折扣,兑付方式
   */
  @Column(name = "sale_rebate_type" , columnDefinition = "VARCHAR(128) COMMENT '返利类型 货补 折扣'")
  @ApiModelProperty("返利类型编码,兑付方式")
  private String saleRebateType;

  /**
   * 返利类型 货补 折扣
   */
  @Column(name = "sale_rebate_type_name" , columnDefinition = "VARCHAR(128) COMMENT '返利类型名称 货补 折扣'")
  @ApiModelProperty("返利类型名称")
  private String saleRebateTypeName;

  /**
   * 计算时间 返利政策结束后第几天
   */
  @Column(name = "calculate_day_num" , columnDefinition = "int(5) COMMENT '计算时间 返利政策结束后第几天'")
  @ApiModelProperty("计算时间 返利政策结束后第几天")
  private Integer calculateDayNum;

  /**
   * 计算公式命中策略类型
   */
  @Column(name = "calculate_type" , columnDefinition = "VARCHAR(64) COMMENT '计算公式命中策略类型'")
  @ApiModelProperty("计算公式命中策略类型")
  private String calculateType;

  /**
   * 是否应用新版本公式编辑器的返利政策(初始化的时候会刷数据 以0代表旧版本)
   */
  @Column(name = "is_new_type" ,nullable = false, columnDefinition = "bit(1) COMMENT '是否应用新版本公式编辑器的返利政策'")
  @ApiModelProperty("是否应用新版本公式编辑器的返利政策")
  private Boolean isNewType;
  
  //===============================================认养新增===============================================

  /**
   * 费用使用部门编码
   */
  @Column(name = "org_code" , length = 32 , columnDefinition = "VARCHAR(32) COMMENT '费用使用部门编码'")
  @ApiModelProperty("费用使用部门编码")
  private String orgCode;

  /**
   * 费用使用部门名称
   */
  @Column(name = "org_name" , length = 128 , columnDefinition = "VARCHAR(128) COMMENT '费用使用部门名称'")
  @ApiModelProperty("费用使用部门名称")
  private String orgName;

  /**
   * 成本中心编码
   */
  @Column(name = "cost_center_code" , length = 32 , columnDefinition = "VARCHAR(32) COMMENT '成本中心编码'")
  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  /**
   * 成本中心名称
   */
  @Column(name = "cost_center_name" , length = 128 , columnDefinition = "VARCHAR(128) COMMENT '成本中心名称'")
  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  /**
   * 客户编码
   */
  @Column(name = "customer_code" , length = 32 , columnDefinition = "VARCHAR(32) COMMENT '客户编码'")
  @ApiModelProperty("客户编码")
  private String customerCode;

  /**
   * 客户名称
   */
  @Column(name = "customer_name" , length = 128 , columnDefinition = "VARCHAR(128) COMMENT '客户名称'")
  @ApiModelProperty("客户名称")
  private String customerName;

  /**
   * 公司代码
   */
  @Column(name = "company_code" , length = 32 , columnDefinition = "VARCHAR(32) COMMENT '公司代码'")
  @ApiModelProperty("公司代码")
  private String companyCode;

  /**
   * 公司名称
   */
  @Column(name = "company_name" , length = 128 , columnDefinition = "VARCHAR(128) COMMENT '公司名称'")
  @ApiModelProperty("公司名称")
  private String companyName;

  /**
   * 客户ERP编码
   */
  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", length = 32 , columnDefinition = "VARCHAR(32) COMMENT '客户ERP编码'")
  private String erpCode;

  /**
   * 客户产品组编码
   */
  @ApiModelProperty("客户产品组编码")
  @Column(name = "product_group_code", length = 32 , columnDefinition = "VARCHAR(32) COMMENT '产品组编码'")
  private String productGroupCode;

  /**
   * 客户分销渠道编码
   */
  @ApiModelProperty("客户分销渠道编码")
  @Column(name = "channel_code", length = 32 , columnDefinition = "VARCHAR(32) COMMENT '分销渠道编码'")
  private String channelCode;

  /**
   * 职位编码
   */
  @ApiModelProperty(name = "position_code", value = "职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '职位编码'")
  private String positionCode;

  /**
   * 活动编码
   */
  @ApiModelProperty(name = "activity_code", value = "活动编码")
  @Column(name = "activity_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '活动编码'")
  private String activityCode;

  /**
   * 活动开始时间（包括）
   */
  @Column(name = "activity_start_time" , columnDefinition = "Datetime COMMENT '活动开始时间（包括）'")
  @ApiModelProperty("活动开始时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date activityStartTime;

  /**
   * 活动结束时间（包括）
   */
  @Column(name = "activity_end_time" , columnDefinition = "Datetime COMMENT '活动结束时间（包括）'")
  @ApiModelProperty("活动结束时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date activityEndTime;

}
