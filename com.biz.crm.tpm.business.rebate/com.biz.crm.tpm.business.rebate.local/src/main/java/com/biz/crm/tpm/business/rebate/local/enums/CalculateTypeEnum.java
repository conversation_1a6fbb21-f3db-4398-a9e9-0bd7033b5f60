package com.biz.crm.tpm.business.rebate.local.enums;

import com.biz.crm.tpm.business.rebate.local.constant.SaleRebateCalculateTypeConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @title CalculateTypeEnum
 * @date 2023/6/20 16:03
 * @description
 */
@Getter
public enum CalculateTypeEnum {

  ANY(SaleRebateCalculateTypeConstant.RANDOM_HIT_REGISTER_CODE, 0),
  FIRST(SaleRebateCalculateTypeConstant.FIRST_HIT_REGISTER_CODE, 1);

  /**
   * 命中类型
   */
  private String hitCode;
  /**
   * 命中次数
   */
  private int hitCount;

  CalculateTypeEnum(String hitCode, int hitCount) {
    this.hitCode = hitCode;
    this.hitCount = hitCount;
  }

  public static CalculateTypeEnum getCalculateTypeEnum(String hitType) {
    for (CalculateTypeEnum calculateTypeEnum : CalculateTypeEnum.values()){
      if (calculateTypeEnum.hitCode.equals(hitType)){
        return calculateTypeEnum;
      }
    }
    return null;
  }

}
