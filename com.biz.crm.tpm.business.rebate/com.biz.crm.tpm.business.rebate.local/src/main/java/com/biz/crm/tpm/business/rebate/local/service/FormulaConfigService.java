package com.biz.crm.tpm.business.rebate.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaConfigEntity;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Description 返利公式
 * <AUTHOR>
 * @Date 2024/6/11 17:23
 */
public interface FormulaConfigService {

    /**
     * 根据条件分页查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    Page<FormulaConfigEntity> findByConditions(Pageable pageable, FormulaConfigEntity dto);

    /**
     * 根据id查询详情
     *
     * @param id
     * @return
     */
    FormulaConfigVo findById(String id, String formulaConfigCode);

    /**
     * 创建
     *
     * @param dto
     */
    void create(FormulaConfigEntity dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(FormulaConfigEntity dto);

    /**
     * 批量删除
     *
     * @param ids
     */
    void delete(List<String> ids);

    /**
     * 批量启用
     *
     * @param ids
     */
    void enableBatch(List<String> ids);

    /**
     * 批量禁用
     *
     * @param ids
     */
    void disableBatch(List<String> ids);


}
