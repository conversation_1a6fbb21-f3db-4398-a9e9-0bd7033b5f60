package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @description: 返利政策返利商品
 * @author: lifei
 * @date: 2022/2/15 16:37
 */
@TableName("tpm_sale_rebate_policy_product_info")
@Entity
@Getter
@Setter
@Table(name = "tpm_sale_rebate_policy_product_info", indexes = {
    @Index(columnList = "sale_rebate_policy_code")})
@org.hibernate.annotations.Table(appliesTo = "tpm_sale_rebate_policy_product_info", comment = "返利政策分配商品")
public class SaleRebatePolicyProductInfo extends TenantEntity {

  private static final long serialVersionUID = -7008029446202716815L;
  /**
   * 范围类型（商品或者产品层级）
   */
  @Column(name = "type", nullable = false, columnDefinition = "VARCHAR(128) COMMENT '范围类型'")
  @ApiModelProperty("范围类型")
  private String type;

  /**
   * 商品或产品层级编码
   */
  @Column(name = "code", nullable = false, columnDefinition = "VARCHAR(128) COMMENT '商品编码'")
  @ApiModelProperty("商品或产品层级编码")
  private String code;

  /**
   * 商品或产品层级名称
   */
  @Column(name = "name", nullable = false, columnDefinition = "VARCHAR(128) COMMENT '商品名称'")
  @ApiModelProperty("商品或产品层级名称")
  private String name;

  /**
   * 返利政策业务编号
   */
  @Column(name = "sale_rebate_policy_code", nullable = false, columnDefinition = "VARCHAR(128) COMMENT '返利政策业务编号'")
  @ApiModelProperty("返利政策业务编号")
  private String saleRebatePolicyCode;

  /**
   * 分配类型 全额 比例
   */
  @ApiModelProperty("分配类型")
  @Column(name = "allocation_type", columnDefinition = "int(5) COMMENT '分配类型 全额 比例'")
  private Integer allocationType;

  /**
   * 分配比例
   */
  @ApiModelProperty("分配比例")
  @Column(name = "rebate_ratio", columnDefinition = "decimal(20,4) COMMENT '分配比例'")
  private BigDecimal rebateRatio;

}
