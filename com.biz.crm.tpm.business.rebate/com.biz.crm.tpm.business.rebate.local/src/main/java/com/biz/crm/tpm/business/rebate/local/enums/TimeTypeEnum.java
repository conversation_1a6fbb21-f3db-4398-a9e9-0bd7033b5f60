package com.biz.crm.tpm.business.rebate.local.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 时间类型枚举
 *
 * <AUTHOR>
 * @date 2022/06/28
 */
@Getter
@AllArgsConstructor
public enum TimeTypeEnum {

  /**
   * 自定义
   */
  CUSTOM("custom", "custom", "自定义", "1"),

  /**
   * 计算时间上月
   */
  LAST_MONTH("lastMonth", "lastMonth", "计算时间上月", "2"),
  /**
   * 计算时间上一季度
   */
  LAST_QUARTER("lastQuarter", "lastQuarter", "计算时间上一季度", "3"),
  /**
   * 计算时间上一年
   */
  LAST_YEAR("lastYear", "lastYear", "计算时间上一年", "4");

  /**
   * 系统key
   */
  private String key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;

}
