package com.biz.crm.tpm.business.rebate.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaEntity;
import com.biz.crm.tpm.business.rebate.local.mapper.TpmFormulaMapper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description 返利公式
 * <AUTHOR>
 * @Date 2024/6/11 17:22
 */
@Component
public class DmsFormulaRepository extends ServiceImpl<TpmFormulaMapper, FormulaEntity> {

    public List<FormulaEntity> findByFormulaConfigCode(String formulaConfigCode) {
        if (StringUtils.isBlank(formulaConfigCode)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(FormulaEntity::getFormulaConfigCode, formulaConfigCode).list();
    }

    public void deleteByFormulaConfigCodes(List<String> formulaConfigCodes) {
        if (CollectionUtils.isEmpty(formulaConfigCodes)) {
            return;
        }
        this.lambdaUpdate()
                .in(FormulaEntity::getFormulaConfigCode, formulaConfigCodes)
                .remove();
    }

    public List<FormulaEntity> findByFormulaConfigCodes(List<String> formulaConfigCodes) {
        if (CollectionUtils.isEmpty(formulaConfigCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(FormulaEntity::getFormulaConfigCode, formulaConfigCodes).list();
    }
}
