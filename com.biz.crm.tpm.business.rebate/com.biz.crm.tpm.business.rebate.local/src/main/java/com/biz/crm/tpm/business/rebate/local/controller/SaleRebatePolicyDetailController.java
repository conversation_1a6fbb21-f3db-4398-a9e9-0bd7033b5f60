package com.biz.crm.tpm.business.rebate.local.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyDetail;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 返利政策明细，按照租户进行隔离(SaleRebatePolicyDetail)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-02-25 14:30:52
 */
@RestController
@RequestMapping("/v1/saleRebatePolicyDetail/saleRebatePolicyDetail")
@Slf4j
@Api(tags = "返利模块:SaleRebatePolicyDetail:返利政策明细")
public class SaleRebatePolicyDetailController {
  /**
   * 服务对象
   */
  @Autowired(required = false)

  private SaleRebatePolicyDetailService saleRebatePolicyDetailService;

  /**
   * 分页查询所有数据
   *
   * @param pageable               分页对象
   * @param saleRebatePolicyDetail 查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<SaleRebatePolicyDetail>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                               @ApiParam(name = "saleRebatePolicyDetail", value = "返利政策明细，按照租户进行隔离") SaleRebatePolicyDetail saleRebatePolicyDetail) {
    try {
      Page<SaleRebatePolicyDetail> page = this.saleRebatePolicyDetailService.findByConditions(pageable, saleRebatePolicyDetail);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<SaleRebatePolicyDetail> findById(@PathVariable @ApiParam(name = "id", value = "主键id") String id) {
    try {
      SaleRebatePolicyDetail saleRebatePolicyDetail = this.saleRebatePolicyDetailService.findById(id);
      return Result.ok(saleRebatePolicyDetail);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 作废
   *
   * @param ids
   * @return
   */
  @ApiOperation(value = "作废")
  @PatchMapping("/disableBatch")
  public Result<?> disableBatch(@RequestBody List<String> ids) {
    try {
      this.saleRebatePolicyDetailService.disableBatch(ids);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量上账
   * @param ids
   * @return
   */
  @ApiOperation(value = "上账")
  @PatchMapping("/handleAccount")
  public Result<?> handleAccount(@RequestBody List<String> ids) {
    try {
      this.saleRebatePolicyDetailService.handleAccount(ids);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 返利计算
   * @param ids
   * @return
   */
  @ApiOperation(value = "返利重新计算")
  @PatchMapping("/handleCalculation")
  public Result<?> handleCalculation(@RequestBody List<String> ids) {
    try {
      this.saleRebatePolicyDetailService.handleCalculation(ids);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 调整
   * @param saleRebatePolicyDetail
   * @return
   */
  @ApiOperation(value = "返利调整")
  @PatchMapping("/handleAdjust")
  public Result<SaleRebatePolicyDetail> handleAdjust(@RequestBody SaleRebatePolicyDetail saleRebatePolicyDetail) {
    try {
      SaleRebatePolicyDetail entity = this.saleRebatePolicyDetailService.handleAdjust(saleRebatePolicyDetail);
      return Result.ok(entity);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
