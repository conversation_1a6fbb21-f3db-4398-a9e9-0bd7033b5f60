package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.rebate.local.constant.SalePolicyConstant;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateAdjustDetail;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyDetail;
import com.biz.crm.tpm.business.rebate.local.repository.SaleRebatePolicyDetailRepository;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateAdjustDetailService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyCalculateService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyDetailService;
import com.biz.crm.tpm.business.rebate.sdk.enums.BillTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleOnAccountStatusEnums;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyTemplateRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 返利政策明细，按照租户进行隔离(SaleRebatePolicyDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-25 14:30:52
 */
@Service("saleRebatePolicyDetailService")
public class SaleRebatePolicyDetailServiceImpl implements SaleRebatePolicyDetailService {

  @Autowired(required = false)
  private SaleRebatePolicyDetailRepository saleRebatePolicyDetailRepository;
  @Autowired(required = false)
  private SaleRebatePolicyCalculateService saleRebatePolicyCalculateService;
  @Autowired(required = false)
  private SaleRebateAdjustDetailService saleRebateAdjustDetailService;
  @Autowired(required = false)
  private List<SaleRebatePolicyTemplateRegister> saleRebatePolicyTemplateRegisters;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private GenerateCodeService generateCodeService;


  /**
   * 分页查询数据
   *
   * @param pageable               分页对象
   * @param saleRebatePolicyDetail 实体对象
   * @return
   */
  @Override
  public Page<SaleRebatePolicyDetail> findByConditions(Pageable pageable,
                                                       SaleRebatePolicyDetail saleRebatePolicyDetail) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(saleRebatePolicyDetail)) {
      saleRebatePolicyDetail = new SaleRebatePolicyDetail();
    }
    saleRebatePolicyDetail.setTenantCode(TenantUtils.getTenantCode());
    saleRebatePolicyDetail.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    return this.saleRebatePolicyDetailRepository.findByConditions(pageable, saleRebatePolicyDetail);
  }

  /**
   * 条件查询不分页
   *
   * @param saleRebatePolicyDetail
   * <AUTHOR>
   * @date
   */
  @Override
  public List<SaleRebatePolicyDetail> findByConditionsList(
      SaleRebatePolicyDetail saleRebatePolicyDetail) {
    return this.saleRebatePolicyDetailRepository.findByConditionsList(saleRebatePolicyDetail);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public SaleRebatePolicyDetail findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    return this.saleRebatePolicyDetailRepository.findById(id);
  }

  /**
   * 通过主键查询数据集合
   *
   * @param ids 主键
   * @return 数据集合
   */
  @Override
  public List<SaleRebatePolicyDetail> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return null;
    }
    return this.saleRebatePolicyDetailRepository.findByIds(ids);
  }

  /**
   * 新增数据
   *
   * @param saleRebatePolicyDetail 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public SaleRebatePolicyDetail createOrUpdate(SaleRebatePolicyDetail saleRebatePolicyDetail) {
    this.createValidate(saleRebatePolicyDetail);
    this.calculateValidate(saleRebatePolicyDetail);
    if(StringUtils.isBlank(saleRebatePolicyDetail.getSaleRebateDetailCode())){
      saleRebatePolicyDetail.setSaleRebateDetailCode(
          this.generateCodeService.generateCode(SalePolicyConstant.SALE_REBATE_DETAIL_CODE));
    }
    saleRebatePolicyDetail.setTenantCode(TenantUtils.getTenantCode());
    this.saleRebatePolicyDetailRepository.saveOrUpdate(saleRebatePolicyDetail);
    if (BillTypeEnum.AUTO_KEEP_BOOKS.getKey().equals(saleRebatePolicyDetail.getBillType())) {
      //上账
      this.onAccount(Collections.singletonList(saleRebatePolicyDetail));
    }
    return saleRebatePolicyDetail;
  }

  /**
   * 作废
   *
   * @param ids
   */
  @Override
  @Transactional
  public void disableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "作废数据时，参数集合不能为空！");
    List<SaleRebatePolicyDetail> list = this.findByIds(ids);
    Validate.isTrue(!CollectionUtils.isEmpty(list), "未查询到数据");
    List<SaleRebatePolicyDetail> collect = list.stream()
        .filter(e -> !e.getBillStatus().equals(SaleOnAccountStatusEnums.WAIT_ACCOUNT.getKey()))
        .collect(Collectors.toList());
    Validate.isTrue(CollectionUtils.isEmpty(collect), "仅能作废状态为待上账类型的明细");
    this.saleRebatePolicyDetailRepository.disableBatch(ids);
  }

  /**
   * 通过编码集合查询
   *
   * @param saleRebatePolicyCodes
   * @return
   */
  @Override
  public List<SaleRebatePolicyDetail> findBySaleRebatePolicyCodes(
      List<String> saleRebatePolicyCodes) {
    if (CollectionUtils.isEmpty(saleRebatePolicyCodes)) {
      return new ArrayList<>(0);
    }
    return this.saleRebatePolicyDetailRepository.findBySaleRebatePolicyCodes(saleRebatePolicyCodes);
  }

  /**
   * 上账
   *
   * @param ids
   */
  @Override
  @Transactional
  public void handleAccount(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "id集合不能为空!");
    List<SaleRebatePolicyDetail> list = this.findByIds(ids);
    this.operationValidate(list);
    this.onAccount(list);
    list.forEach(s->{
      s.setBillStatus(SaleOnAccountStatusEnums.ON_ACCOUNT.getKey());
    });
    this.saleRebatePolicyDetailRepository.updateBatchById(list);
  }

  /**
   * 返利计算
   *
   * @param ids
   */
  @Override
  @Transactional
  public void handleCalculation(List<String> ids) {
   this.saleRebatePolicyCalculateService.onCalculateByDetailIds(ids);
  }

  /**
   * 调整
   *
   * @param saleRebatePolicyDetail
   * @return
   */
  @Override
  @Transactional
  public SaleRebatePolicyDetail handleAdjust(SaleRebatePolicyDetail saleRebatePolicyDetail) {
    Validate.notNull(saleRebatePolicyDetail, "返利明细调整时传入数据为空");
    Validate.notNull(saleRebatePolicyDetail.getId(), "返利明细调整时传入主键为空");
    SaleRebatePolicyDetail entityOld = this.findById(saleRebatePolicyDetail.getId());
    Validate
        .isTrue(SaleOnAccountStatusEnums.WAIT_ACCOUNT.getKey().equals(entityOld.getBillStatus()),
            "只能调整待上账数据！");
    BigDecimal adjustAmount = saleRebatePolicyDetail.getAdjustAmount();
    Validate.notNull(adjustAmount, "调整时，调整金额不能为空");
    BigDecimal actualRebateAmountOld = entityOld.getActualRebateAmount();
    BigDecimal actualRebateAmountNew = actualRebateAmountOld.add(adjustAmount);
    BigDecimal adjustAmountOld = entityOld.getAdjustAmount();
    BigDecimal adjustAmountNew = adjustAmountOld.add(adjustAmount);
    entityOld.setActualRebateAmount(actualRebateAmountNew);
    entityOld.setAdjustAmount(adjustAmountNew);
    entityOld.setRemark(saleRebatePolicyDetail.getRemark());
    this.saleRebatePolicyDetailRepository.updateByIdAndTenantCode(entityOld,TenantUtils.getTenantCode());
    SaleRebateAdjustDetail saleRebateAdjustDetail = new SaleRebateAdjustDetail();
    saleRebateAdjustDetail.setAdjustAmount(saleRebatePolicyDetail.getAdjustAmount());
    saleRebateAdjustDetail.setCustomerCode(entityOld.getCustomerCode());
    saleRebateAdjustDetail.setCustomerName(entityOld.getCustomerName());
    saleRebateAdjustDetail.setSaleRebatePolicyName(entityOld.getSaleRebatePolicyName());
    saleRebateAdjustDetail.setSaleRebateDetailCode(entityOld.getSaleRebateDetailCode());
    this.saleRebateAdjustDetailService.create(saleRebateAdjustDetail);
    return entityOld;
  }

  /**
   * 根据返利政策编码和测试状态删除
   *
   * @param saleRebatePolicyCode 返利政策编码
   * @param isTest               是否测试，0：否，1：是 BooleanEnum
   * @param speedNo              计算批次号
   */
  @Override
  @Transactional
  public void deleteByRebateCodeAndTest(String saleRebatePolicyCode, String isTest,
      String speedNo) {
    //1、
    Validate.notEmpty(saleRebatePolicyCode, "政策编码不能为空");
    Validate.isTrue(BooleanEnum.TRUE.getNumStr().equals(isTest), "测试状态只能传是（1）");
    //2、
    this.saleRebatePolicyDetailRepository
        .deleteByRebateCodeAndTest(saleRebatePolicyCode, isTest, speedNo);
  }

  /**
   * 创建验证
   *
   * @param saleRebatePolicyDetail
   */
  private void createValidate(SaleRebatePolicyDetail saleRebatePolicyDetail) {
    // 补充数据
    saleRebatePolicyDetail.setTenantCode(TenantUtils.getTenantCode());
    saleRebatePolicyDetail.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    saleRebatePolicyDetail.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    //校验
    Validate.notNull(saleRebatePolicyDetail, "新增时，对象信息不能为空！");
    Validate.notNull(saleRebatePolicyDetail.getCalculationTime(), "新增数据时，返利计算时间不能为空！");
    Validate
        .notEmpty(saleRebatePolicyDetail.getSaleRebateCalculationYears(), "新增数据时，返利计算时间年月不能为空！");
    Validate.notNull(saleRebatePolicyDetail.getSaleRebateEndTime(), "新增数据时，返利政策结束时间（包括）不能为空！");
    Validate.notEmpty(saleRebatePolicyDetail.getSaleRebatePolicyCode(), "新增数据时，促销编码不能为空！");
    Validate.notEmpty(saleRebatePolicyDetail.getSaleRebatePolicyName(), "新增数据时，促销名称不能为空！");
    Validate.notNull(saleRebatePolicyDetail.getSaleRebateStartTime(), "新增数据时，返利政策开始时间（包括）不能为空！");
    Validate.isTrue(saleRebatePolicyDetail.getSaleRebateStartTime()
        .compareTo(saleRebatePolicyDetail.getSaleRebateEndTime()) <= 0, "返利政策额开始时间应小于结束时间");
    if (StringUtils.isNotBlank(saleRebatePolicyDetail.getRemark())) {
      Validate.isTrue(saleRebatePolicyDetail.getRemark().length() <= 100, "备注不能超过100个字");
    }
    List<SaleRebatePolicyDetail> byConditionsList = this
        .findByConditionsList(saleRebatePolicyDetail);
    if(StringUtils.isBlank(saleRebatePolicyDetail.getId())){
      if (BooleanEnum.FALSE.getNumStr().equals(saleRebatePolicyDetail.getIsTest())) {
        List<SaleRebatePolicyDetail> collect = byConditionsList.stream()
            .filter(detail -> BooleanEnum.FALSE.getNumStr().equals(detail.getIsTest())).collect(
                Collectors.toList());
        Validate.isTrue(CollectionUtils.isEmpty(collect), "已存在周期内返利明细");
      }
    }
    // 客户校验
  }

  /**
   * 操作校验
   *
   * @param list
   */
  private void operationValidate(List<SaleRebatePolicyDetail> list) {
    Validate.isTrue(!CollectionUtils.isEmpty(list), "未查询到数据");
    List<SaleRebatePolicyDetail> collect = list.stream()
        .filter(e -> !e.getBillStatus().equals(SaleOnAccountStatusEnums.WAIT_ACCOUNT.getKey()))
        .collect(Collectors.toList());
    Validate.isTrue(CollectionUtils.isEmpty(collect), "仅能上账状态为待上账类型的明细");
  }

  /**
   * 上账
   *
   * @param details 返利明细
   * <AUTHOR>
   * @date
   */
  private void onAccount(List<SaleRebatePolicyDetail> details) {
    if (CollectionUtils.isEmpty(details)) {
      return;
    }
    List<SaleRebatePolicyDetailVo> voList =
        (List<SaleRebatePolicyDetailVo>)
            this.nebulaToolkitService.copyCollectionByBlankList(
                details, SaleRebatePolicyDetail.class, SaleRebatePolicyDetailVo.class,
                HashSet.class, ArrayList.class);
    Map<String, List<SaleRebatePolicyDetailVo>> map =
        voList.stream().collect(Collectors.groupingBy(SaleRebatePolicyDetailVo::getSaleRebateType));
    map.forEach((k, v) -> {
      saleRebatePolicyTemplateRegisters.forEach(saleRebatePolicyTemplateRegister -> {
        if (saleRebatePolicyTemplateRegister.getSaleRebatePolicytemplateCode().equals(k)) {
          saleRebatePolicyTemplateRegister.onAccount(v);
        }
      });
    });
  }

  /**
   * 计算校验
   *
   * @param saleRebatePolicyDetail
   */
  private void calculateValidate(SaleRebatePolicyDetail saleRebatePolicyDetail) {
    if (saleRebatePolicyDetail.getAdjustAmount() == null) {
      saleRebatePolicyDetail.setActualRebateAmount(saleRebatePolicyDetail.getRebateAmount());
      return;
    }
    BigDecimal adjustAmount = saleRebatePolicyDetail.getAdjustAmount();
    BigDecimal rebateAmount = saleRebatePolicyDetail.getRebateAmount();
    BigDecimal actualRebateAmount = rebateAmount.add(adjustAmount);
    saleRebatePolicyDetail.setActualRebateAmount(actualRebateAmount);
  }

}

