package com.biz.crm.tpm.business.rebate.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.rebate.local.constant.SalePolicyConstant;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaConfigEntity;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaEntity;
import com.biz.crm.tpm.business.rebate.local.repository.DmsFormulaConfigRepository;
import com.biz.crm.tpm.business.rebate.local.repository.DmsFormulaRepository;
import com.biz.crm.tpm.business.rebate.local.repository.DmsRebatePolicyRelaFormulaConfigRepository;
import com.biz.crm.tpm.business.rebate.local.repository.SaleRebatePolicyFormulaInfoRepository;
import com.biz.crm.tpm.business.rebate.local.service.FormulaConfigService;
import com.biz.crm.tpm.business.rebate.local.utils.MathUtil;
import com.biz.crm.tpm.business.rebate.sdk.dto.DmsFormulaEventDto;
import com.biz.crm.tpm.business.rebate.sdk.event.DmsFormulaEventListener;
import com.biz.crm.tpm.business.rebate.sdk.service.FormulaConfigVoService;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaSaleRebatePolicyElementVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 返利公式
 * <AUTHOR>
 * @Date 2024/6/11 17:23
 */
@Service
public class FormulaConfigServiceImpl implements FormulaConfigService, FormulaConfigVoService {

    @Autowired(required = false)
    private DmsFormulaConfigRepository configRepository;
    @Autowired(required = false)
    private DmsFormulaRepository formulaRepository;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired(required = false)
    private SaleRebatePolicyFormulaInfoRepository formulaInfoRepository;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private DmsRebatePolicyRelaFormulaConfigRepository relaFormulaConfigRepository;

    @Resource
    private RebateFormulaPushOaComponent pushOaComponent;

    @Override
    public Page<FormulaConfigEntity> findByConditions(Pageable pageable, FormulaConfigEntity dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 15));
        dto = Optional.ofNullable(dto).orElse(new FormulaConfigEntity());
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        return this.configRepository.findByConditions(pageable, dto);
    }

    @Override
    public FormulaConfigVo findById(String id, String formulaConfigCode) {
        FormulaConfigEntity entity = this.configRepository.findById(id, formulaConfigCode);
        Validate.notNull(entity, "公式不存在或已被删除");
        FormulaConfigVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, FormulaConfigVo.class, HashSet.class, ArrayList.class);
        List<FormulaEntity> formulaEntityList =
                this.formulaRepository.findByFormulaConfigCode(vo.getFormulaConfigCode());
        List<FormulaSaleRebatePolicyElementVo> elementVoList =
                (List<FormulaSaleRebatePolicyElementVo>) this.nebulaToolkitService.copyCollectionByBlankList(formulaEntityList, FormulaEntity.class, FormulaSaleRebatePolicyElementVo.class, HashSet.class, ArrayList.class);
        vo.setFormulaList(elementVoList);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void create(FormulaConfigEntity dto) {
        this.validate(dto);
        dto.setId(null);
        dto.setFormulaConfigCode(this.generateCodeService.generateCode(SalePolicyConstant.SALE_REBATE_FORMULA_CODE, 1, 5).get(0));
        this.configRepository.save(dto);
        dto.getFormulaList().forEach(v -> v.setFormulaConfigCode(dto.getFormulaConfigCode()));
        this.formulaRepository.saveBatch(dto.getFormulaList());
        // 创建事件
        DmsFormulaEventDto eventDto = new DmsFormulaEventDto();
        eventDto.setOriginal(null);
        FormulaConfigVo newVo = this.findById(dto.getId(), null);
        //推送OA
        pushOaComponent.pushFormulaToOA(newVo);
        eventDto.setNewest(newVo);
        SerializableBiConsumer<DmsFormulaEventListener, DmsFormulaEventDto> consumer =
                DmsFormulaEventListener::onCreate;
        this.nebulaNetEventClient.publish(eventDto, DmsFormulaEventListener.class, consumer);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(FormulaConfigEntity dto) {
        this.validate(dto);
        Validate.notBlank(dto.getId(), "id不能为空");
        FormulaConfigVo old = this.findById(dto.getId(), null);
        Validate.notNull(old, "公式不存在或已被删除");
        Validate.isTrue(StringUtils.equals(old.getFormulaConfigCode(), dto.getFormulaConfigCode()), "公式编码不能更改");
        this.configRepository.updateById(dto);
        this.formulaRepository.deleteByFormulaConfigCodes(Lists.newArrayList(old.getFormulaConfigCode()));
        dto.getFormulaList().forEach(v -> v.setFormulaConfigCode(dto.getFormulaConfigCode()));
        this.formulaRepository.saveBatch(dto.getFormulaList());
        // 更新事件
        DmsFormulaEventDto eventDto = new DmsFormulaEventDto();
        eventDto.setOriginal(old);
        FormulaConfigVo newVo = this.findById(dto.getId(), null);
        //推送OA
        pushOaComponent.pushFormulaToOA(newVo);
        eventDto.setNewest(newVo);
        SerializableBiConsumer<DmsFormulaEventListener, DmsFormulaEventDto> consumer =
                DmsFormulaEventListener::onUpdate;
        this.nebulaNetEventClient.publish(eventDto, DmsFormulaEventListener.class, consumer);
    }

    private void validate(FormulaConfigEntity dto) {
        Validate.notNull(dto, "对象信息不能为空！");
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        Validate.notBlank(dto.getTenantCode(), "租户编号不能为空！");
        Validate.notBlank(dto.getFormulaConfigName(), "公式配置名称不能为空！");
        Validate.notEmpty(dto.getFormulaList(), "公式列表不能为空");
        dto.getFormulaList().forEach(v -> {
            Validate.notBlank(v.getSaleRebatePolicyCondition(), "条件不能为空！");
            Validate.notBlank(v.getSaleRebatePolicyConditionName(), "条件（展示用）不能为空！");
            Validate.notBlank(v.getSaleRebatePolicyFormula(), "公式不能为空！");
            Validate.notBlank(v.getSaleRebatePolicyFormulaName(), "公式（展示用）不能为空！");
            //验证公式正确性 
            String saleRebatePolicyCondition = MathUtil.replaceDefaultVariable(v.getSaleRebatePolicyCondition());
            String saleRebatePolicyFormula = MathUtil.replaceDefaultVariable(v.getSaleRebatePolicyFormula());
            try {
                Validate.notNull(MathUtil.computeCondition(saleRebatePolicyCondition), "条件验证计算失败，请检查条件");
            } catch (Exception e) {
                throw new IllegalArgumentException(StringUtils.join("条件验证计算失败，请检查条件",
                        v.getSaleRebatePolicyCondition()), e);
            }
            try {
                Validate.notNull(MathUtil.computeFormula(saleRebatePolicyFormula), "公式验证计算失败，请检查公式");
            } catch (Exception e) {
                throw new IllegalArgumentException(StringUtils.join("公式验证计算失败，请检查公式", v.getSaleRebatePolicyFormula())
                        , e);
            }
        });

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delete(List<String> ids) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除时数据不能为空");
        List<FormulaConfigEntity> formulaEntities = this.configRepository.findByIds(ids);
        Validate.notEmpty(formulaEntities, "公式不存在或已被删除");
        List<String> formulaConfigCodes =
                formulaEntities.stream().map(FormulaConfigEntity::getFormulaConfigCode).collect(Collectors.toList());
        this.configRepository.updateDelFlagByIds(ids);
        this.formulaRepository.deleteByFormulaConfigCodes(formulaConfigCodes);

        for (FormulaConfigEntity entity : formulaEntities) {
            FormulaConfigVo vo = nebulaToolkitService.copyObjectByWhiteList(entity, FormulaConfigVo.class, HashSet.class, ArrayList.class);
            vo.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            //推送OA
            pushOaComponent.pushFormulaToOA(vo);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void enableBatch(List<String> ids) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids), "请选中要操作的数据");
        List<FormulaConfigEntity> list = this.configRepository.findByIds(ids);
        Validate.notEmpty(list, "数据不存在或已被删除");
        list = list.stream().filter(k -> EnableStatusEnum.DISABLE.getCode().equals(k.getEnableStatus())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        this.configRepository.updateEnableStatusByIdIn(EnableStatusEnum.ENABLE, ids);
        //启用事件
        List<FormulaConfigVo> voList =
                (List<FormulaConfigVo>) this.nebulaToolkitService.copyCollectionByBlankList(list,
                        FormulaConfigEntity.class, FormulaConfigVo.class, HashSet.class, LinkedList.class);

        DmsFormulaEventDto eventDto = new DmsFormulaEventDto();
        eventDto.setNewestList(voList);
        SerializableBiConsumer<DmsFormulaEventListener, DmsFormulaEventDto> consumer =
                DmsFormulaEventListener::onEnable;
        this.nebulaNetEventClient.publish(eventDto, DmsFormulaEventListener.class, consumer);
        for (FormulaConfigVo vo : voList) {
            //推送OA
            pushOaComponent.pushFormulaToOA(vo);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void disableBatch(List<String> ids) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids), "请选中要操作的数据");
        List<FormulaConfigEntity> list = this.configRepository.findByIds(ids);
        Validate.notEmpty(list, "数据不存在或已被删除");
        list = list.stream().filter(k -> EnableStatusEnum.ENABLE.getCode().equals(k.getEnableStatus())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        this.configRepository.updateEnableStatusByIdIn(EnableStatusEnum.DISABLE, ids);
        //禁用事件
        List<FormulaConfigVo> voList =
                (List<FormulaConfigVo>) this.nebulaToolkitService.copyCollectionByBlankList(list,
                        FormulaConfigEntity.class, FormulaConfigVo.class, HashSet.class, LinkedList.class);

        DmsFormulaEventDto eventDto = new DmsFormulaEventDto();
        eventDto.setNewestList(voList);
        SerializableBiConsumer<DmsFormulaEventListener, DmsFormulaEventDto> consumer =
                DmsFormulaEventListener::onDisable;
        this.nebulaNetEventClient.publish(eventDto, DmsFormulaEventListener.class, consumer);
        for (FormulaConfigVo vo : voList) {
            //推送OA
            pushOaComponent.pushFormulaToOA(vo);
        }
    }

    @Override
    public FormulaConfigVo findByConfigCode(String formulaConfigCode) {
        FormulaConfigEntity entity = this.configRepository.findById(null, formulaConfigCode);
        if (ObjectUtils.isEmpty(entity)) {
            return null;
        }
        FormulaConfigVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, FormulaConfigVo.class, HashSet.class, ArrayList.class);
        List<FormulaEntity> formulaEntityList =
                this.formulaRepository.findByFormulaConfigCode(vo.getFormulaConfigCode());
        List<FormulaSaleRebatePolicyElementVo> elementVoList =
                (List<FormulaSaleRebatePolicyElementVo>) this.nebulaToolkitService.copyCollectionByBlankList(formulaEntityList, FormulaEntity.class, FormulaSaleRebatePolicyElementVo.class, HashSet.class, ArrayList.class);
        vo.setFormulaList(elementVoList);
        return vo;
    }
}
