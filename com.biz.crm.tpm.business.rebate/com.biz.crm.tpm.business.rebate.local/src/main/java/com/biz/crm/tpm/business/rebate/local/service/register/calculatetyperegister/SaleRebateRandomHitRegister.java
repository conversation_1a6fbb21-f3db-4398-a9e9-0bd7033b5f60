package com.biz.crm.tpm.business.rebate.local.service.register.calculatetyperegister;

import com.biz.crm.tpm.business.rebate.local.constant.SaleRebateCalculateTypeConstant;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyCalculateService;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebateCalculateTypeRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateComputeParamVo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @title SaleRebateRandomHitRegister
 * @date 2023/6/19 14:32
 * @description 任意命中返利计算类型注册器
 */
@Service
public class SaleRebateRandomHitRegister implements SaleRebateCalculateTypeRegister {

  @Autowired(required = false)
  private SaleRebatePolicyCalculateService  saleRebatePolicyCalculateService;

  //返利计算类型名称
  private static final String CALCULATE_TYPE_REGISTER_NAME = "任意命中返利";

  private static final Integer SORT_CODE = 1;

  @Override
  public String getCalculateTypeRegisterCode() {
    return SaleRebateCalculateTypeConstant.RANDOM_HIT_REGISTER_CODE;
  }

  @Override
  public String getCalculateTypeRegisterName() {
    return CALCULATE_TYPE_REGISTER_NAME;
  }

  @Override
  public Integer getCalculateTypeRegisterSortCode() {
    return SORT_CODE;
  }

  /**
   * 周期返利按照命中规则生成返利明细
   */
  @Override
  @Transactional
  public void handleCalculate(List<SaleRebateComputeParamVo> saleRebateComputeParamVos) {
    //周期返利计算 生成返利明细
    this.saleRebatePolicyCalculateService.onCalculate(saleRebateComputeParamVos);
  }
}
