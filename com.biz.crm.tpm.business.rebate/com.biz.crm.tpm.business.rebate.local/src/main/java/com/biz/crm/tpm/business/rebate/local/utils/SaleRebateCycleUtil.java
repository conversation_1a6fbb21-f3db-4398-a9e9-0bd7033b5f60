package com.biz.crm.tpm.business.rebate.local.utils;

import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateExecutionDateRangeVo;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 返利周期工具类
 * @author: lifei
 * @date: 2022/2/21 11:14
 */
public class SaleRebateCycleUtil {

  private static final SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
  private static final SimpleDateFormat formatMonth = new SimpleDateFormat("yyyy-MM");
  private static final SimpleDateFormat formatYear = new SimpleDateFormat("yyyy");

  /**
   * 获取输入日期当天最晚时间
   *
   * @param saleRebateTime
   * <AUTHOR>
   * @date
   */
  public static Date getMonthLastday(Date saleRebateTime) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebateTime);
    int maxDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    calendar.set(Calendar.DAY_OF_MONTH, maxDay);
    calendar.set(Calendar.HOUR_OF_DAY, 23);
    calendar.set(Calendar.MINUTE, 59);
    calendar.set(Calendar.SECOND, 59);
    return calendar.getTime();
  }

  public static Date getLastday(Date saleRebateTime) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebateTime);
    calendar.set(Calendar.HOUR_OF_DAY, 23);
    calendar.set(Calendar.MINUTE, 59);
    calendar.set(Calendar.SECOND, 59);
    return calendar.getTime();
  }

  /**
   * 获取输入日期当天最早时间
   *
   * @param saleRebateTime
   * <AUTHOR>
   * @date
   */
  public static Date getMonthFirstday(Date saleRebateTime) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebateTime);
    int minDay = calendar.getActualMinimum(Calendar.DAY_OF_MONTH);
    calendar.set(Calendar.DAY_OF_MONTH, minDay);
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    return calendar.getTime();
  }

  /**
   * 获取输入日期当天最早时间
   *
   * @param saleRebateTime
   * <AUTHOR>
   * @date
   */
  public static Date getFirstday(Date saleRebateTime) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebateTime);
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    return calendar.getTime();
  }

  /**
   * 根据月获得季度
   *
   * @param month 月
   * @return 季度
   */
  public static int getQuarter(int month) {
    if (month == 1 || month == 2 || month == 3) {
      return 1;
    } else if (month == 4 || month == 5 || month == 6) {
      return 2;
    } else if (month == 7 || month == 8 || month == 9) {
      return 3;
    } else {
      return 4;
    }
  }

  /**
   * 根据月获得半年度
   *
   * @param month 月
   * @return 半年度
   */
  public static int getHalfYear(int month) {
    if (month <= 6) {
      return 1;
    }else {
      return 2;
    }
  }


  public static String createCronExpression() {
    StringBuffer cronExp = new StringBuffer("");
    //秒
    cronExp.append(0).append(" ");
    //分
    cronExp.append(0).append(" ");
    //小时
    cronExp.append(0).append(" ");
    //日
    cronExp.append("* ");
    //月
    cronExp.append("* ");
    //周
    cronExp.append("?");
    return cronExp.toString();
  }

  /***
   * 获取两个时间段的年份/年第一天/年最后一天
   * @param
   * @return
   */
  public static Map<String, SaleRebateExecutionDateRangeVo> getSaleRebateExecutionYearMap(
      SaleRebatePolicyDto saleRebatePolicyDto) {

    String endYear = formatYear.format(saleRebatePolicyDto.getSaleRebateEndTime());
    String startYear = formatYear.format(saleRebatePolicyDto.getSaleRebateStartTime());
    //日历 日期加1(包含结束)
    Calendar calendarStart = Calendar.getInstance();
    Calendar calendarEnd = Calendar.getInstance();
    calendarStart.setTime(saleRebatePolicyDto.getSaleRebateStartTime());
    calendarEnd.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
    calendarEnd.add(Calendar.YEAR, 1);
    Map<String, SaleRebateExecutionDateRangeVo> map = new HashMap<>();
    while (formatYear.format(calendarStart.getTime()).compareTo(formatYear.format(calendarEnd.getTime())) < 0 ) {
      //开始年（循环）
      String year = formatYear.format(calendarStart.getTime());
      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      Calendar calendar = Calendar.getInstance();
      if (startYear.equals(year)) {
        saleRebateExecutionDateRangeVo
            .setSaleRebateStartTime(saleRebatePolicyDto.getSaleRebateStartTime());
      } else {
        Calendar calstart = Calendar.getInstance();
        calstart.set(Calendar.YEAR, Integer.valueOf(year));
        calstart.set(Calendar.DAY_OF_YEAR, calstart.getActualMinimum(Calendar.DAY_OF_YEAR));
        saleRebateExecutionDateRangeVo
            .setSaleRebateStartTime(getFirstday(calstart.getTime()));
      }
      if (year.equals(endYear)) {
        saleRebateExecutionDateRangeVo
            .setSaleRebateEndTime(saleRebatePolicyDto.getSaleRebateEndTime());
        calendar.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
      } else {
        Calendar calEnd = Calendar.getInstance();
        calEnd.set(Calendar.YEAR, Integer.valueOf(year));
        calEnd.set(Calendar.DAY_OF_YEAR, calEnd.getActualMaximum(Calendar.DAY_OF_YEAR));
        saleRebateExecutionDateRangeVo.setSaleRebateEndTime(getLastday(calEnd.getTime()));
        calendar.setTime(calEnd.getTime());
      }
      calendar.set(Calendar.DAY_OF_YEAR,
          calendar.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum()+1);
      map.put(formatDate.format(calendar.getTime()), saleRebateExecutionDateRangeVo);
      calendarStart.add(Calendar.YEAR, 1);
    }
    return map;
  }

  /**
   * 获取某段时间内的周日期 1－6代表周一到周六。0代表周日
   *
   * @return 返回日期List
   */
  public static Map<String, SaleRebateExecutionDateRangeVo> getSaleRebateExecutionWeekMap(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    //执行日历
    Calendar calendarexec = Calendar.getInstance();
    //开始时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebatePolicyDto.getSaleRebateStartTime());
    //key执行日期 value执行范围
    Map<String, SaleRebateExecutionDateRangeVo> map = new HashMap<>();
    //结束日期
    String endTime = formatDate.format(saleRebatePolicyDto.getSaleRebateEndTime());
    int i = 0;
    while (formatDate.format(calendar.getTime()).compareTo(endTime) <= 0) {
      //周一(次循环有两种特殊情况 开始日期小，)
      if(calendar.get(Calendar.DAY_OF_WEEK) - 1 != 1 && i == 0){
        SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
        saleRebateExecutionDateRangeVo.setSaleRebateStartTime(saleRebatePolicyDto.getSaleRebateStartTime());
        Calendar cal = Calendar.getInstance();
        cal.setTime(calendar.getTime());
        //本周日
        cal.add(Calendar.DATE,7 - cal.get(Calendar.DAY_OF_WEEK) + 1);
        if(formatDate.format(cal.getTime()).compareTo(endTime) > 0){
          saleRebateExecutionDateRangeVo.setSaleRebateEndTime(saleRebatePolicyDto.getSaleRebateEndTime());
        }else {
          saleRebateExecutionDateRangeVo.setSaleRebateEndTime(getLastday(cal.getTime()));
        }
        calendarexec.setTime(saleRebateExecutionDateRangeVo.getSaleRebateEndTime());
        calendarexec.set(Calendar.DAY_OF_YEAR, calendarexec.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum()+1);
        String execDate = formatDate.format(calendarexec.getTime());
        map.put(execDate, saleRebateExecutionDateRangeVo);
      }else if (calendar.get(Calendar.DAY_OF_WEEK) - 1 == 1) {
        SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
        Calendar cal = Calendar.getInstance();
        cal.setTime(calendar.getTime());
        cal.set(Calendar.DAY_OF_YEAR,calendar.get(Calendar.DAY_OF_YEAR) + 6);
        saleRebateExecutionDateRangeVo
            .setSaleRebateStartTime(getFirstday(calendar.getTime()));
        if(formatDate.format(cal.getTime()).compareTo(endTime) >= 0 ){
          saleRebateExecutionDateRangeVo.setSaleRebateEndTime(saleRebatePolicyDto.getSaleRebateEndTime());
          calendarexec.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
        }else {
          saleRebateExecutionDateRangeVo.setSaleRebateEndTime(getLastday(cal.getTime()));
          calendarexec.setTime(cal.getTime());
        }
        calendarexec.set(Calendar.DAY_OF_YEAR, calendarexec.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum()+1);
        String execDate = formatDate.format(calendarexec.getTime());
        map.put(execDate, saleRebateExecutionDateRangeVo);
      }

      calendar.add(Calendar.DATE, 1);
      i++;
    }
    return map;
  }

  /**
   *
   * 计算季度
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  public static Map<String, SaleRebateExecutionDateRangeVo> getSaleRebateExecutionQuarterMap(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    //开始时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebatePolicyDto.getSaleRebateStartTime());
    //结束时间加一个月
    Calendar calendarend = Calendar.getInstance();
    calendarend.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
    calendarend.add(Calendar.MONTH, 1);
    //key 执行日期 value执行范围
    Map<String, SaleRebateExecutionDateRangeVo> map = new HashMap<>();
    //按月切分
    List<SaleRebateExecutionDateRangeVo> saleRebateExecutionDateRangeVos = new ArrayList<>();
    while (formatMonth.format(calendar.getTime()).compareTo(formatMonth.format(calendarend.getTime())) < 0) {
      //循环日期年月日
      String startDate = formatMonth.format(calendar.getTime());
      //范围日期年月日（开始）
      String saleRebateStartDate = formatMonth.format(saleRebatePolicyDto.getSaleRebateStartTime());
      //范围日期年月日（结束）
      String saleRebateEndDate = formatMonth.format(saleRebatePolicyDto.getSaleRebateEndTime());
      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      //自然年算法
      if (startDate.equals(saleRebateStartDate)) {
        saleRebateExecutionDateRangeVo.setSaleRebateStartTime(
            saleRebatePolicyDto.getSaleRebateStartTime());
      } else {
        saleRebateExecutionDateRangeVo
            .setSaleRebateStartTime(getMonthFirstday(calendar.getTime()));
      }
      if (startDate.equals(saleRebateEndDate)) {
        saleRebateExecutionDateRangeVo
            .setSaleRebateEndTime(saleRebatePolicyDto.getSaleRebateEndTime());
      } else {
        saleRebateExecutionDateRangeVo.setSaleRebateEndTime(getMonthLastday(calendar.getTime()));
      }
      String[] format1 = formatMonth.format(saleRebateExecutionDateRangeVo.getSaleRebateEndTime())
          .split("-", 0);
      int quarter = SaleRebateCycleUtil.getQuarter(Integer.parseInt(format1[1]));
      saleRebateExecutionDateRangeVo.setSaleRebateQuarter(format1[0] + quarter);
      saleRebateExecutionDateRangeVos.add(saleRebateExecutionDateRangeVo);
      calendar.add(Calendar.MONTH, 1);
    }
    //key季度  value执行范围集合
    Map<String, List<SaleRebateExecutionDateRangeVo>> collect = saleRebateExecutionDateRangeVos
        .stream()
        .collect(Collectors.groupingBy(SaleRebateExecutionDateRangeVo::getSaleRebateQuarter));
    Calendar calendarexec = Calendar.getInstance();
    collect.forEach((k, v) -> {
      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      //开始结束时间最大值
      Date startTime = v.stream().map(o -> o.getSaleRebateStartTime()).distinct()
          .min(Date::compareTo).get();
      Date endTime = v.stream().map(o -> o.getSaleRebateEndTime()).distinct()
          .max(Date::compareTo).get();
      saleRebateExecutionDateRangeVo.setSaleRebateEndTime(endTime);
      saleRebateExecutionDateRangeVo.setSaleRebateStartTime(startTime);
      calendarexec.setTime(endTime);
      calendarexec.set(Calendar.DAY_OF_YEAR,
          calendarexec.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum()+1);
      String execDate = formatDate.format(calendarexec.getTime());
      map.put(execDate, saleRebateExecutionDateRangeVo);
    });
    return map;
  }

  /**
   * 拿到正向月度返利周期（key 执行日期 value 执行范围）
   *
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  public static Map<String, SaleRebateExecutionDateRangeVo> getSaleRebateExecutionMonthMap(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
    //开始时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebatePolicyDto.getSaleRebateStartTime());
    //结束时间加一个月
    Calendar calendarend = Calendar.getInstance();
    calendarend.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
    calendarend.add(Calendar.MONTH, 1);
    //执行日历
    Calendar calendarexec = Calendar.getInstance();
    //key 执行日期 value执行范围
    Map<String, SaleRebateExecutionDateRangeVo> map = new HashMap<>();
    while (format.format(calendar.getTime()).compareTo(format.format(calendarend.getTime())) < 0) {
      //循环日期年月
      String startDate = format.format(calendar.getTime());
      //范围日期年（开始）
      String saleRebateStartDate = format.format(saleRebatePolicyDto.getSaleRebateStartTime());
      //范围日期年月日（结束）
      String saleRebateEndDate = format.format(saleRebatePolicyDto.getSaleRebateEndTime());

      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      //自然年算法
      if (startDate.equals(saleRebateStartDate)) {
        saleRebateExecutionDateRangeVo.setSaleRebateStartTime(saleRebatePolicyDto.getSaleRebateStartTime());
      } else {
        saleRebateExecutionDateRangeVo
            .setSaleRebateStartTime(getMonthFirstday(calendar.getTime()));
      }
      if (startDate.equals(saleRebateEndDate)) {
        saleRebateExecutionDateRangeVo
            .setSaleRebateEndTime(saleRebatePolicyDto.getSaleRebateEndTime());
        calendarexec.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
      } else {
        saleRebateExecutionDateRangeVo.setSaleRebateEndTime(getMonthLastday(calendar.getTime()));
        calendarexec.setTime(getMonthLastday(calendar.getTime()));
      }
      calendarexec.set(Calendar.DAY_OF_YEAR,
          calendarexec.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum()+1);
      String execDate = formatDate.format(calendarexec.getTime());
      map.put(execDate, saleRebateExecutionDateRangeVo);
      calendar.add(Calendar.MONTH, 1);//进行当前日期月份加1
    }
    return map;
  }


  /**
   * 拿到正向日度返利周期（key 执行日期 value 执行范围）
   *
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  public static Map<String, SaleRebateExecutionDateRangeVo> getSaleRebateExecutionDayMap(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    //开始时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebatePolicyDto.getSaleRebateStartTime());
    Calendar calendarend = Calendar.getInstance();
    calendarend.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
    //执行日历
    Calendar calendarexec = Calendar.getInstance();
    //key 执行日期 value执行范围
    Map<String, SaleRebateExecutionDateRangeVo> map = new HashMap<>();
    while (calendar.getTime().before(calendarend.getTime())) {
      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      saleRebateExecutionDateRangeVo.setSaleRebateStartTime(calendar.getTime());
      if(formatDate.format(calendar.getTime()).equals(formatDate.format(saleRebatePolicyDto.getSaleRebateEndTime()))){
        saleRebateExecutionDateRangeVo.setSaleRebateEndTime(saleRebatePolicyDto.getSaleRebateEndTime());
      }else {
        saleRebateExecutionDateRangeVo.setSaleRebateEndTime(getLastday(calendar.getTime()));
      }
      calendarexec.setTime(calendar.getTime());
      calendarexec.set(Calendar.DAY_OF_YEAR,
          calendarexec.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum()+1);
      String execDate = formatDate.format(calendarexec.getTime());
      map.put(execDate, saleRebateExecutionDateRangeVo);
      calendar.add(Calendar.DATE, 1);
      calendar.setTime(getFirstday(calendar.getTime()));
    }
    return map;
  }



  /**
   *
   * 计算季度
   * @param saleRebateEndTime
   * @param saleRebateStartTime
   * <AUTHOR>
   * @date
   */
  public static List<SaleRebateExecutionDateRangeVo> getExecutionQuarterList(
      Date saleRebateStartTime, Date saleRebateEndTime) {
    //开始时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebateStartTime);
    //结束时间加一个月
    Calendar calendarend = Calendar.getInstance();
    calendarend.setTime(saleRebateEndTime);
    calendarend.add(Calendar.MONTH, 1);
    //key 执行日期 value执行范围
    List<SaleRebateExecutionDateRangeVo> list  = new ArrayList<>();
    //按月切分
    List<SaleRebateExecutionDateRangeVo> saleRebateExecutionDateRangeVos = new ArrayList<>();
    while (formatMonth.format(calendar.getTime()).compareTo(formatMonth.format(calendarend.getTime())) < 0) {
      //循环日期年月日
      String startDate = formatMonth.format(calendar.getTime());
      //范围日期年月日（开始）
      String saleRebateStartDate = formatMonth.format(saleRebateStartTime);
      //范围日期年月日（结束）
      String saleRebateEndDate = formatMonth.format(saleRebateEndTime);
      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      //自然年算法
      if (startDate.equals(saleRebateStartDate)) {
        saleRebateExecutionDateRangeVo.setSaleRebateStartTime(saleRebateStartTime);
      } else {
        saleRebateExecutionDateRangeVo
            .setSaleRebateStartTime(getMonthFirstday(calendar.getTime()));
      }
      if (startDate.equals(saleRebateEndDate)) {
        saleRebateExecutionDateRangeVo
            .setSaleRebateEndTime(saleRebateEndTime);
      } else {
        saleRebateExecutionDateRangeVo.setSaleRebateEndTime(getMonthLastday(calendar.getTime()));
      }
      String[] format1 = formatMonth.format(saleRebateExecutionDateRangeVo.getSaleRebateEndTime())
          .split("-", 0);
      int quarter = SaleRebateCycleUtil.getQuarter(Integer.parseInt(format1[1]));
      saleRebateExecutionDateRangeVo.setSaleRebateQuarter(format1[0] +"-"+ quarter);
      saleRebateExecutionDateRangeVos.add(saleRebateExecutionDateRangeVo);
      calendar.add(Calendar.MONTH, 1);
    }
    //key季度  value执行范围集合
    Map<String, List<SaleRebateExecutionDateRangeVo>> collect = saleRebateExecutionDateRangeVos
        .stream()
        .collect(Collectors.groupingBy(SaleRebateExecutionDateRangeVo::getSaleRebateQuarter));
    collect.forEach((k, v) -> {
      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      //开始结束时间最大值
      Date startTime = v.stream().map(o -> o.getSaleRebateStartTime()).distinct()
          .min(Date::compareTo).get();
      Date endTime = v.stream().map(o -> o.getSaleRebateEndTime()).distinct()
          .max(Date::compareTo).get();
      saleRebateExecutionDateRangeVo.setSaleRebateEndTime(endTime);
      saleRebateExecutionDateRangeVo.setSaleRebateStartTime(startTime);
      saleRebateExecutionDateRangeVo.setSaleRebateQuarter(k);
      list.add(saleRebateExecutionDateRangeVo);
    });
    return list;
  }


  /**
   *
   * 计算季度
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  public static Map<String, SaleRebateExecutionDateRangeVo> getSaleRebateExecutionHalfYearMap(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    //开始时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebatePolicyDto.getSaleRebateStartTime());
    //结束时间加一个月
    Calendar calendarend = Calendar.getInstance();
    calendarend.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
    calendarend.add(Calendar.MONTH, 1);
    //key 执行日期 value执行范围
    Map<String, SaleRebateExecutionDateRangeVo> map = new HashMap<>();
    //按月切分
    List<SaleRebateExecutionDateRangeVo> saleRebateExecutionDateRangeVos = new ArrayList<>();
    while (formatMonth.format(calendar.getTime()).compareTo(formatMonth.format(calendarend.getTime())) < 0) {
      //循环日期年月日
      String startDate = formatMonth.format(calendar.getTime());
      //范围日期年月日（开始）
      String saleRebateStartDate = formatMonth.format(saleRebatePolicyDto.getSaleRebateStartTime());
      //范围日期年月日（结束）
      String saleRebateEndDate = formatMonth.format(saleRebatePolicyDto.getSaleRebateEndTime());
      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      //自然年算法
      if (startDate.equals(saleRebateStartDate)) {
        saleRebateExecutionDateRangeVo.setSaleRebateStartTime(
            saleRebatePolicyDto.getSaleRebateStartTime());
      } else {
        saleRebateExecutionDateRangeVo
            .setSaleRebateStartTime(getMonthFirstday(calendar.getTime()));
      }
      if (startDate.equals(saleRebateEndDate)) {
        saleRebateExecutionDateRangeVo
            .setSaleRebateEndTime(saleRebatePolicyDto.getSaleRebateEndTime());
      } else {
        saleRebateExecutionDateRangeVo.setSaleRebateEndTime(getMonthLastday(calendar.getTime()));
      }
      String[] format1 = formatMonth.format(saleRebateExecutionDateRangeVo.getSaleRebateEndTime())
          .split("-", 0);
      int halfYear = SaleRebateCycleUtil.getHalfYear(Integer.parseInt(format1[1]));
      saleRebateExecutionDateRangeVo.setSaleRebateHalfYear(format1[0] + halfYear);
      saleRebateExecutionDateRangeVos.add(saleRebateExecutionDateRangeVo);
      calendar.add(Calendar.MONTH, 1);
    }
    //key 半年  1 上半年 2 下半年  value执行范围集合
    Map<String, List<SaleRebateExecutionDateRangeVo>> collect = saleRebateExecutionDateRangeVos
        .stream()
        .collect(Collectors.groupingBy(SaleRebateExecutionDateRangeVo::getSaleRebateHalfYear));
    Calendar calendarexec = Calendar.getInstance();
    collect.forEach((k, v) -> {
      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      //开始结束时间最大值
      Date startTime = v.stream().map(o -> o.getSaleRebateStartTime()).distinct()
          .min(Date::compareTo).get();
      Date endTime = v.stream().map(o -> o.getSaleRebateEndTime()).distinct()
          .max(Date::compareTo).get();
      saleRebateExecutionDateRangeVo.setSaleRebateEndTime(endTime);
      saleRebateExecutionDateRangeVo.setSaleRebateStartTime(startTime);
      calendarexec.setTime(endTime);
      calendarexec.set(Calendar.DAY_OF_YEAR,
          calendarexec.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum()+1);
      String execDate = formatDate.format(calendarexec.getTime());
      map.put(execDate, saleRebateExecutionDateRangeVo);
    });
    return map;
  }
}

