package com.biz.crm.tpm.business.rebate.local.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @title SaleRebateCalculateTypeConstant
 * @date 2023/6/27 18:14
 * @description  返利计算策略常量类-勿轻易删除
 */
public class SaleRebateCalculateTypeConstant {

  /**
   * 任意命中返利策略编码
   */
  public static  final  String RANDOM_HIT_REGISTER_CODE = "SaleRebateRandomHitRegister";

  /**
   * 按先后顺序命中返利
   */
  public static  final  String FIRST_HIT_REGISTER_CODE = "SaleRebateFirstHitRegister";
}
