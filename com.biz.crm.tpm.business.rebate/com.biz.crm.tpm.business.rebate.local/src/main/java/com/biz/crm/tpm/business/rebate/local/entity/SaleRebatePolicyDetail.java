package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe: 返利政策明细
 * @createTime 2022年02月25日 09:54:00
 */
@Getter
@Setter
@TableName("tpm_sale_rebate_policy_detail")
@Entity
@Table(name = "tpm_sale_rebate_policy_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_sale_rebate_policy_detail", comment = "返利政策明细，按照租户进行隔离")
public class SaleRebatePolicyDetail extends TenantFlagOpEntity {

  private static final long serialVersionUID = 326961825636593003L;

  /**
   * 返利明细编码
   */
  @Column(name = "sale_rebate_detail_code",  nullable = false, columnDefinition = "VARCHAR(128) COMMENT '返利明细编码'")
  @ApiModelProperty("返利明细编码")
  private String saleRebateDetailCode;

  /**
   * 上账状态 SaleOnAccountStatusEnums
   */
  @Column(name = "bill_status", nullable = false, columnDefinition = "int(5) COMMENT '上账状态 手动 自动'")
  @ApiModelProperty("上账状态")
  private Integer billStatus;

  /**
   * 返利编码
   */
  @Column(name = "sale_rebate_policy_code",  nullable = false, columnDefinition = "VARCHAR(225) COMMENT '促销编码'")
  @ApiModelProperty("返利编码")
  private String saleRebatePolicyCode;

  /**
   * 返利名称
   */
  @Column(name = "sale_rebate_policy_name",  nullable = false, columnDefinition = "VARCHAR(128) COMMENT '促销名称'")
  @ApiModelProperty("返利名称")
  private String saleRebatePolicyName;

  /**
   * 客户编码
   */
  @Column(name = "customer_code",  nullable = false, columnDefinition = "VARCHAR(225) COMMENT '客户编码'")
  @ApiModelProperty("客户编码")
  private String customerCode;

  /**
   * 客户名称
   */
  @Column(name = "customer_name", nullable = false, columnDefinition = "VARCHAR(128) COMMENT '客户名称'")
  @ApiModelProperty("客户名称")
  private String customerName;

  /**
   * 组织编码
   */
  @Column(name = "org_code",  columnDefinition = "VARCHAR(225) COMMENT '组织编码'")
  @ApiModelProperty("组织编码")
  private String orgCode;

  /**
   * 组织名称
   */
  @Column(name = "org_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '组织名称'")
  @ApiModelProperty("组织名称")
  private String orgName;

  /**
   * 返利金额
   */
  @Column(name = "rebate_amount", columnDefinition = "decimal(20,4) COMMENT '返利金额'")
  @ApiModelProperty("返利金额")
  private BigDecimal rebateAmount;

  /**
   * 调整金额
   */
  @Column(name = "adjust_amount", columnDefinition = "decimal(20,4) COMMENT '调整金额'")
  @ApiModelProperty("调整金额")
  private BigDecimal adjustAmount;

  /**
   * 实际返利金额
   */
  @Column(name = "actual_rebate_amount", columnDefinition = "decimal(20,4) COMMENT '实际返利金额'")
  @ApiModelProperty("实际返利金额")
  private BigDecimal actualRebateAmount;

  /**
   * 返利类型 货补 折扣
   */
  @Column(name = "sale_rebate_type", columnDefinition = "VARCHAR(128) COMMENT '返利类型 货补 折扣'")
  @ApiModelProperty("返利类型")
  private String saleRebateType;

  /**
   * 返利类型 货补 折扣
   */
  @Column(name = "sale_rebate_type_name", columnDefinition = "VARCHAR(128) COMMENT '返利类型 货补 折扣'")
  @ApiModelProperty("返利类型名称")
  private String saleRebateTypeName;


  /**
   * 上账状态 SaleOnAccountStatusEnums
   */
  @Column(name = "product_type",columnDefinition = "int(5) COMMENT '返利类型（2产品/1层级）'")
  @ApiModelProperty("返利产品类型（产品/层级）")
  private Integer productType;


  /**
   * 商品编码
   */
  @Column(name = "product_code", columnDefinition = "VARCHAR(225) COMMENT '商品编码'")
  @ApiModelProperty("商品编码")
  private String productCode;

  /**
   * 商品名称
   */
  @Column(name = "product_name", columnDefinition = "VARCHAR(128) COMMENT '商品名称'")
  @ApiModelProperty("商品名称")
  private String productName;

  /**
   * 产品层级编码
   */
  @Column(name = "product_level_code", columnDefinition = "VARCHAR(225) COMMENT '产品层级编码'")
  @ApiModelProperty("产品层级编码")
  private String productLevelCode;

  /**
   * 产品层级名称
   */
  @Column(name = "product_level_name",  columnDefinition = "VARCHAR(128) COMMENT '产品层级名称'")
  @ApiModelProperty("产品层级名称")
  private String productLevelName;

  /**
   * 返利周期
   */
  @Column(name = "sale_rebate_policy_cycle", columnDefinition = "varchar(64) COMMENT '返利周期'")
  @ApiModelProperty("返利周期")
  private String saleRebatePolicyCycle;

  /**
   * 返利周期
   */
  @Column(name = "sale_rebate_policy_cycle_name", columnDefinition = "varchar(64) COMMENT '返利周期名称'")
  @ApiModelProperty("返利周期名称")
  private String saleRebatePolicyCycleName;

  /**
   * 返利政策开始时间（包括）
   */
  @Column(name = "sale_rebate_start_time", nullable = false,  columnDefinition = "varchar(10) COMMENT '返利政策开始时间（包括）'")
  @ApiModelProperty("返利政策开始时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date saleRebateStartTime;

  /**
   * 返利政策结束时间（包括）
   */
  @Column(name = "sale_rebate_end_time", nullable = false, columnDefinition = "varchar(10) COMMENT '返利政策结束时间（包括）'")
  @ApiModelProperty("返利政策结束时间（包括）")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date saleRebateEndTime;

  /**
   * 返利计算时间年月
   */
  @Column(name = "sale_rebate_calculation_years",nullable = false, columnDefinition = "varchar(7) COMMENT '返利计算时间'")
  @ApiModelProperty("返利计算时间年月")
  private String saleRebateCalculationYears;

  /**
   * 返利计算时间
   */
  @Column(name = "calculation_time", nullable = false, columnDefinition = "Datetime COMMENT '返利计算时间'")
  @ApiModelProperty("返利计算时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date calculationTime;

  /**
   * 分配类型
   *
   */
  @ApiModelProperty("分配类型")
  @Column(name = "allocation_type",columnDefinition = "int(5) COMMENT '分配类型 全额 比例'")
  private Integer allocationType;

  /**
   * 分配比例
   */
  @ApiModelProperty("分配比例")
  @Column(name = "rebate_ratio", columnDefinition = "decimal(20,4) COMMENT '分配比例'")
  private BigDecimal rebateRatio;

  /**
   * 批次号
   */
  @Column(name = "speed_no", columnDefinition = "varchar(125) COMMENT '批次号'")
  @ApiModelProperty("批次号")
  private String speedNo;

  /**
   * 返利公式id
   */
  @Column(name = "sale_rebate_policy_formula_id", columnDefinition = "varchar(255) COMMENT '返利公式id'")
  @ApiModelProperty("返利公式id")
  private String SaleRebatePolicyFormulaId;

  /**
   * 上账方式 BillTypeEnum
   */
  @Column(name = "bill_type", nullable = false, columnDefinition = "int(5) COMMENT '返利类型（2产品1层级）'")
  @ApiModelProperty("上账方式")
  private Integer billType;

  /**
   *是否测试
   */
  @Column(name = "is_test", columnDefinition = "varchar(5) COMMENT '是否测试'")
  @ApiModelProperty("是否测试")
  private String isTest;

//===============================================认养新增===============================================

  /**
   * 职位编码
   */
  @ApiModelProperty(name = "position_code", value = "职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '职位编码'")
  private String positionCode;

  /**
   * 公司代码
   */
  @Column(name = "company_code" , length = 32 , columnDefinition = "VARCHAR(32) COMMENT '公司代码'")
  @ApiModelProperty("公司代码")
  private String companyCode;

  /**
   * 客户ERP编码
   */
  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", length = 32 , columnDefinition = "VARCHAR(32) COMMENT '客户ERP编码'")
  private String erpCode;

  /**
   * 客户产品组编码
   */
  @ApiModelProperty("客户产品组编码")
  @Column(name = "product_group_code", length = 32 , columnDefinition = "VARCHAR(32) COMMENT '产品组编码'")
  private String productGroupCode;

  /**
   * 客户分销渠道编码
   */
  @ApiModelProperty("客户分销渠道编码")
  @Column(name = "channel_code", length = 32 , columnDefinition = "VARCHAR(32) COMMENT '分销渠道编码'")
  private String channelCode;

  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码")
  @TableField(value = "material_code")
  @Column(name = "material_code", length = 32, columnDefinition = "varchar(32) COMMENT '物料编码'")
  private String materialCode;

}
