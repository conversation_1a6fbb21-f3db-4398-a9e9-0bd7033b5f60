<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.rebate.local.mapper.FormulaCalculationLogMapper">


  <select id="findPage" resultType="com.biz.crm.tpm.business.rebate.local.entity.FormulaCalculationLog">
      select * from tpm_formula_calculation_log
      <where>
          1=1
          <if test="vo.businessDetailCode != null and vo.businessDetailCode != ''">
              and business_detail_code = #{vo.businessDetailCode}
          </if>
          <if test="vo.calType != null and vo.calType != ''">
              and cal_type = #{vo.calType}
          </if>
          order by create_time desc
      </where>
  </select>

</mapper>

