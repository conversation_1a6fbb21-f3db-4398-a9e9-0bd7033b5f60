package com.biz.crm.tpm.business.rebate.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyCheckProductInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 返利政策考核商品(SaleRebatePolicyCheckProductInfo)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
public interface SaleRebatePolicyCheckProductInfoMapper extends
    BaseMapper<SaleRebatePolicyCheckProductInfo> {

  /**
   * 分页查询
   *
   * @param page                             分页
   * @param saleRebatePolicyCheckProductInfo
   * @return
   */
  public Page<SaleRebatePolicyCheckProductInfo> findByConditions(
      @Param("page") Page<SaleRebatePolicyCheckProductInfo> page,
      @Param("saleRebatePolicyCheckProductInfo") SaleRebatePolicyCheckProductInfo saleRebatePolicyCheckProductInfo);
}

