package com.biz.crm.tpm.business.rebate.local.strategy;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyScopeInfo;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyScopeInfoService;
import com.biz.crm.tpm.business.rebate.sdk.strategy.SaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.sdk.vo.scope.SaleRebatePolicyScopeChannelInfoVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 该策略支持按照渠道信息来进行优惠政策范围圈定的处理方式
 *
 * <AUTHOR>
 */
@Component
public class ChannelForSaleRebateCustomerScopeStrategy extends
    AbstractSaleRebateCustomerScopeStrategy implements
    SaleRebateCustomerScopeStrategy<SaleRebatePolicyScopeChannelInfoVo> {

  @Autowired(required = false)
  private SaleRebatePolicyScopeInfoService SaleRebatePolicyScopeInfoService;

  @Autowired(required = false)
  private CustomerVoService customerVoService;

  private static final String SCOPE_TYPE = "channelForSalePolicy";

  private static final Integer SCOPE_SORT = 2;

  @Override
  public String getScopeType() {
    return SCOPE_TYPE;
  }

  @Override
  public String getScopeTypeDesc() {
    return "渠 道";
  }

  @Override
  public Integer getSort() {
    return SCOPE_SORT;
  }

  /**
   * 按照渠道信息来进行优惠政策范围圈定的客户编码集合
   *
   * @param salePolicyCode 需要查询的优惠政策业务编号
   * @param tenantCode     当前二级租户信息
   * @return
   */
  @Override
  public Set<String> onRequestCustomerCodes(String salePolicyCode, String tenantCode) {
    /**
     * 操作步骤：
     * 1、选定方式分组转成渠道信息范围的渠道编码map
     * 2、从包含中的渠道编码过滤掉非包含渠道编码
     * 3、获取渠道范围的客户编码
     */
    if (StringUtils.isAnyBlank(tenantCode, salePolicyCode)) {
      return null;
    }
    //查询渠道信息范围的客户结果
    List<SaleRebatePolicyScopeInfo> saleRebatePolicyScopeInfos = this.SaleRebatePolicyScopeInfoService
        .findByTenantCodeAndSalePolicyCodeAndCustomerScopeType(tenantCode, salePolicyCode,
            SCOPE_TYPE);
    if (CollectionUtils.isEmpty(saleRebatePolicyScopeInfos)) {
      return null;
    }
    //1、======
    List<String> includeList = saleRebatePolicyScopeInfos.stream()
        .filter(a -> StringUtils.isNotBlank(a.getCode())).map(SaleRebatePolicyScopeInfo::getCode)
        .collect(
            Collectors.toList());
    //3、======
   List<CustomerVo> customerVos = this.customerVoService
        .findByChannels(includeList);
    if (CollectionUtils.isEmpty(customerVos)) {
      return null;
    }
    Set<String> customerCodes = customerVos.stream().map(CustomerVo::getCustomerCode)
        .collect(Collectors.toSet());
    return customerCodes;
  }
}
