package com.biz.crm.tpm.business.rebate.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicy;
import org.apache.ibatis.annotations.Param;

/**
 * 返利政策，按照租户进行隔离(SaleRebatePolicy)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
public interface SaleRebatePolicyMapper extends BaseMapper<SaleRebatePolicy> {


  /**
   * 分页查询
   *
   * @param page             分页
   * @param saleRebatePolicy
   * @return
   */
  public Page<SaleRebatePolicy> findByConditions(@Param("page") Page<SaleRebatePolicy> page,
      @Param("saleRebatePolicy") SaleRebatePolicy saleRebatePolicy);
}

