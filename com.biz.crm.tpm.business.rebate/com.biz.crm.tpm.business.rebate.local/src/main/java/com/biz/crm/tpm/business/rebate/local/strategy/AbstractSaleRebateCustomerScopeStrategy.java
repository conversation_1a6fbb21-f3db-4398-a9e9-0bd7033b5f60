package com.biz.crm.tpm.business.rebate.local.strategy;


import com.biz.crm.tpm.business.rebate.local.repository.SaleRebatePolicyScopeInfoRepository;
import com.biz.crm.tpm.business.rebate.sdk.vo.scope.AbstractSaleRebatePolicyCustomerInfo;
import com.google.common.collect.Lists;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

/**
 * 这个抽象类的作用只是为了帮助标品中的几个具体的客户范围选择策略，管理重复度比较高的代码</br> 如果是项目上自行实现的某种具体的客户范围选择策略，则这个抽象类没有参考价值
 *
 * <AUTHOR>
 */
public class AbstractSaleRebateCustomerScopeStrategy {

  @Autowired(required = false)
  private SaleRebatePolicyScopeInfoRepository saleRebatePolicyScopeInfoRepository;

  /**
   * 按照设定的客户适用范围信息，进行删除
   *
   * @param currentScopeType
   * @param salePolicyCustomerInfos
   */
  protected void deleteSalePolicyCustomerInfos(String currentScopeType,
      Set<? extends AbstractSaleRebatePolicyCustomerInfo> salePolicyCustomerInfos) {
    if (!CollectionUtils.isEmpty(salePolicyCustomerInfos)) {
      String[] salePolicyCustomerInfoIds = salePolicyCustomerInfos.stream()
          .filter(item -> StringUtils.equals(currentScopeType, item.getCustomerScopeType()))
          .map(AbstractSaleRebatePolicyCustomerInfo::getId).toArray(String[]::new);
      // 如果条件成立，说明要进行删除了
      if (salePolicyCustomerInfoIds != null && salePolicyCustomerInfoIds.length > 0) {
        this.saleRebatePolicyScopeInfoRepository
            .deleteByIds(Lists.newArrayList(salePolicyCustomerInfoIds));
      }
    }
  }
}
