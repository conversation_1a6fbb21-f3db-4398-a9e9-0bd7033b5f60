package com.biz.crm.tpm.business.rebate.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateRegisterService;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyRegisterVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 返利注册器相关下拉接口（返利类型，返利周期，返利基准）
 * @author: lifei
 * @date: 2022/2/21 10:37
 */
@RestController
@RequestMapping("/v1/saleRebateRegister/saleRebateRegister")
@Slf4j
@Api(tags = "返利模块:SaleRebatePolicyCycle:返利注册器相关接口（返利类型，返利周期，返利基准）")
public class SaleRebateRegisterController {

  @Autowired(required = false)
  private SaleRebateRegisterService saleRebateRegisterService;


  /**
   * 返利周期下拉接口
   *
   * @return
   */
  @ApiOperation(value = "返利周期下拉接口")
  @GetMapping("findForRebateCycle")
  public Result<List<SaleRebatePolicyRegisterVo>> findForRebateCycle() {
    try {
      return Result.ok(this.saleRebateRegisterService.findForRebateCycle());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 返利类型下拉接口
   *
   * @return
   */
  @ApiOperation(value = "返利类型下拉接口")
  @GetMapping("findForRebateType")
  public Result<List<SaleRebatePolicyRegisterVo>> findForRebateType() {
    try {
      return Result.ok(this.saleRebateRegisterService.findForRebateType());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 返利类型基准
   *
   * @return
   */
  @ApiOperation(value = "返利基准")
  @GetMapping("findForRebateCriterion")
  public Result<List<SaleRebatePolicyRegisterVo>> findForRebateCriterion() {
    try {
      return Result.ok(this.saleRebateRegisterService.findForRebateCriterion());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   *
   * 通过类型查询要素
   * @param saleRebateType 返利类型
   * <AUTHOR>
   * @date
   */
  @ApiOperation(value = "返利要素")
  @GetMapping("findForRebateElement")
  public Result<List<SaleRebatePolicyRegisterVo>> findForRebateElement(String saleRebateType) {
    try {
      return Result.ok(this.saleRebateRegisterService.findForRebateElement(saleRebateType));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   *
   * 返利客户选择范围下拉
   * @param saleRebateType 返利类型
   * <AUTHOR>
   * @date
   */
  @ApiOperation(value = "返利客户选择范围下拉")
  @GetMapping("findForRebateScope")
  public Result<List<SaleRebatePolicyRegisterVo>> findRebateScope(String saleRebateType) {
    try {
      return Result.ok(this.saleRebateRegisterService.findForRebateScope(saleRebateType));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
