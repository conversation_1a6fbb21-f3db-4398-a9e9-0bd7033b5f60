package com.biz.crm.tpm.business.rebate.local.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateCalculationLog;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateCalculationLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 返利政策计算日志(SaleRebateCalculationLog)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-02-28 15:19:44
 */
@RestController
@RequestMapping("/v1/saleRebateCalculationLog/saleRebateCalculationLog")
@Slf4j
@Api(tags = "返利模块:SaleRebateCalculationLog:返利政策计算日志")
public class SaleRebateCalculationLogController {
  /**
  * 服务对象
  */
  @Autowired(required = false)
  private SaleRebateCalculationLogService saleRebateCalculationLogService;
  
  /**
   * 通过返利明细id查询返利计算日志数据
   *
   * @param saleRebatePolicyDetailId 返利明细id
   * @return 所有数据
  */
  @ApiOperation(value = "通过返利明细id查询返利计算日志数据")
  @GetMapping("findByDetailId")
  public Result<Page<SaleRebateCalculationLog>> findByDetailId(
       @ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
       @ApiParam(name = "saleRebatePolicyDetailId", value = "返利明细id") String saleRebatePolicyDetailId) {
    try {
      Page<SaleRebateCalculationLog> page = this.saleRebateCalculationLogService.findByDetailId(pageable,saleRebatePolicyDetailId);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }




}
