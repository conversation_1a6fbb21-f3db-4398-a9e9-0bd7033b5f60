package com.biz.crm.tpm.business.rebate.local.service;

import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyVo;
import java.util.List;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年02月23日 10:15:00
 */
public interface SaleRebatePolicyVoService {

  /**
   * 通过id 查询明细
   *
   * @param id
   * @return
   */
  SaleRebatePolicyVo findDetailById(String id);

  /**
   *
   * 通过编码查询
   * @param saleRebatePolicyCode
   * <AUTHOR>
   * @date
   */
  SaleRebatePolicyVo findDetailByCode(String saleRebatePolicyCode);

  /**
   *
   * 批量查询通过编码查询
   * @param saleRebatePolicyCodes
   * <AUTHOR>
   * @date
   */
  List<SaleRebatePolicyVo> findByCodes(List<String> saleRebatePolicyCodes);
}
