package com.biz.crm.tpm.business.rebate.local.repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicy;
import com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyMapper;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleRebatePolicyStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * 返利政策，按照租户进行隔离(SaleRebatePolicy)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:54
 */
@Component
public class SaleRebatePolicyRepository extends
    ServiceImpl<SaleRebatePolicyMapper, SaleRebatePolicy> {

  /**
   * 分页查询
   *
   * @param pageable         分页
   * @param saleRebatePolicy
   * @return
   */
  public Page<SaleRebatePolicy> findByConditions(Pageable pageable,
      SaleRebatePolicy saleRebatePolicy) {
    Page<SaleRebatePolicy> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    //新增租户编号判断
    saleRebatePolicy.setTenantCode(TenantUtils.getTenantCode());
    Page<SaleRebatePolicy> pageList = this.baseMapper
        .findByConditions(page, saleRebatePolicy);
    return pageList;
  }

  /**
   * 按照优惠政策的业务编号，查询优惠政策的基本信息（只包括基本信息）。
   *
   * @param saleRebatePolicyCode 优惠政策的业务编号
   * @param tenantCode           当前二级租户的业务编号
   */
  public SaleRebatePolicy findBySalePolicyCodeAndTenantCode(String saleRebatePolicyCode,
      String tenantCode) {
    SaleRebatePolicy salePolicy = this.lambdaQuery()
        .eq(SaleRebatePolicy::getTenantCode, tenantCode)
        .eq(SaleRebatePolicy::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(SaleRebatePolicy::getSaleRebatePolicyCode, saleRebatePolicyCode).one();
    return salePolicy;
  }

  /**
   * 按照返利政策的业务编号，查询优惠政策的基本信息（只包括基本信息）。
   *
   * @param saleRebatePolicyCodes 返利的业务编号
   * @param tenantCode            当前二级租户的业务编号
   */
  public List<SaleRebatePolicy> findBySaleRebatePolicyCodes(List<String> saleRebatePolicyCodes,
      String tenantCode) {
    List<SaleRebatePolicy> salePolicys = this.lambdaQuery()
        .eq(SaleRebatePolicy::getTenantCode, tenantCode)
        .in(SaleRebatePolicy::getSaleRebatePolicyCode, saleRebatePolicyCodes)
        .eq(SaleRebatePolicy::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(SaleRebatePolicy::getTenantCode, tenantCode)
        .list();
    return salePolicys;
  }

  /**
   * 根据主键集合，修改 enable_status
   *
   * @param enable
   * @param ids
   */
  public void updateEnableStatusByIdIn(EnableStatusEnum enable, List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return;
    }
    this.lambdaUpdate()
        .set(SaleRebatePolicy::getEnableStatus, enable.getCode())
        .eq(SaleRebatePolicy::getTenantCode, TenantUtils.getTenantCode())
        .eq(SaleRebatePolicy::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
        .in(SaleRebatePolicy::getId, ids)
        .update();
  }


  /**
   * 根据主键集合，修改 SaleRebatePolicyStatus
   *
   * @param saleRebatePolicyStatusEnum
   * @param id
   */
  public void updateSaleRebatePolicyStatus(SaleRebatePolicyStatusEnum saleRebatePolicyStatusEnum,
      String id) {
    if (StringUtils.isBlank(id)) {
      return;
    }
    this.lambdaUpdate()
        .set(SaleRebatePolicy::getSaleRebatePolicyStatus, saleRebatePolicyStatusEnum.getKey())
        .eq(SaleRebatePolicy::getTenantCode, TenantUtils.getTenantCode())
        .eq(SaleRebatePolicy::getId, id)
        .eq(SaleRebatePolicy::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
        .update();
  }

  /**
   * 通过id和租户编号查询
   * @param id
   * @param tenantCode
   * @return
   */
  public SaleRebatePolicy findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicy::getTenantCode,tenantCode)
        .eq(SaleRebatePolicy::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
        .in(SaleRebatePolicy::getId,id)
        .one();
  }

  /**
   * 通过id和租户编号修改
   * @param entity
   * @param tenantCode
   */
  public void updateByIdAndTenantCode(SaleRebatePolicy entity, String tenantCode) {
    LambdaUpdateWrapper<SaleRebatePolicy>lambdaUpdateWrapper= Wrappers.lambdaUpdate();
    lambdaUpdateWrapper.eq(SaleRebatePolicy::getTenantCode,tenantCode);
    lambdaUpdateWrapper.eq(SaleRebatePolicy::getDelFlag,DelFlagStatusEnum.NORMAL.getCode());
    lambdaUpdateWrapper.in(SaleRebatePolicy::getId,entity.getId());
    this.baseMapper.update(entity,lambdaUpdateWrapper);
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void updateFlagByIdsAndTenantCode(List<String> ids, String tenantCode) {
      this.lambdaUpdate()
              .eq(SaleRebatePolicy::getTenantCode, tenantCode)
              .in(SaleRebatePolicy::getId, ids)
              .set(SaleRebatePolicy::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
              .update();
  }

  public List<SaleRebatePolicy> listByIdsAndTenantCode(List<String> idList, String tenantCode) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicy::getTenantCode,TenantUtils.getTenantCode())
        .eq(SaleRebatePolicy::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
        .in(SaleRebatePolicy::getId,idList)
        .list();
  }

    public SaleRebatePolicy findByActivityCode(String activityCode) {
        if (StringUtils.isBlank(activityCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(SaleRebatePolicy::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(SaleRebatePolicy::getTenantCode, TenantUtils.getTenantCode())
                .eq(SaleRebatePolicy::getActivityCode, activityCode)
                .one();
    }
}

