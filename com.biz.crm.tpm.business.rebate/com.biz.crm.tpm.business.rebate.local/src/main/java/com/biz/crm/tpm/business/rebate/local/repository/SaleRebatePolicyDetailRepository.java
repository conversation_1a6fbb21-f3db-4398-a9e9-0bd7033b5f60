package com.biz.crm.tpm.business.rebate.local.repository;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyDetail;
import com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyDetailMapper;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleOnAccountStatusEnums;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 返利政策明细，按照租户进行隔离(SaleRebatePolicyDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-25 14:30:52
 */
@Component
public class SaleRebatePolicyDetailRepository extends
    ServiceImpl<SaleRebatePolicyDetailMapper, SaleRebatePolicyDetail> {

  /**
   * 分页查询数据
   *
   * @param pageable               分页对象
   * @param saleRebatePolicyDetail 实体对象
   * @return
   */
  public Page<SaleRebatePolicyDetail> findByConditions(Pageable pageable,
      SaleRebatePolicyDetail saleRebatePolicyDetail) {
    Page<SaleRebatePolicyDetail> page = new Page<>(pageable.getPageNumber(),
        pageable.getPageSize());
    saleRebatePolicyDetail.setTenantCode(TenantUtils.getTenantCode());
    Page<SaleRebatePolicyDetail> pageList = this.baseMapper
        .findByConditions(page, saleRebatePolicyDetail);
    return pageList;
  }

  /**
   * 分页查询数据
   *
   * @param saleRebatePolicyDetail 实体对象
   * @return
   */
  public List<SaleRebatePolicyDetail> findByConditionsList(
      SaleRebatePolicyDetail saleRebatePolicyDetail) {
    saleRebatePolicyDetail.setTenantCode(TenantUtils.getTenantCode());
    List<SaleRebatePolicyDetail> pageList = this.baseMapper
        .findByConditionsList(saleRebatePolicyDetail);
    return pageList;
  }

  /**
   * 通过编码集合查询
   *
   * @param saleRebatePolicyCodes
   * @return
   */
  public List<SaleRebatePolicyDetail> findBySaleRebatePolicyCodes(
      List<String> saleRebatePolicyCodes) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(SaleRebatePolicyDetail::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
        .eq(SaleRebatePolicyDetail::getTenantCode, TenantUtils.getTenantCode())
        .in(SaleRebatePolicyDetail::getSaleRebatePolicyCode, saleRebatePolicyCodes)
        .list();
  }

  /**
   * 通过编码集合作废
   *
   * @param ids
   */
  public void disableBatch(List<String> ids) {
    this.lambdaUpdate()
        .in(SaleRebatePolicyDetail::getId, ids)
        .eq(SaleRebatePolicyDetail::getTenantCode,TenantUtils.getTenantCode())
        .set(SaleRebatePolicyDetail::getBillStatus, SaleOnAccountStatusEnums.CANCELLED.getKey())
        .update();
  }

  /**
   * 通过id集合查询
   *
   * @param idList
   * @return
   */
  public List<SaleRebatePolicyDetail> findByIds(List<String> idList) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(SaleRebatePolicyDetail::getTenantCode, TenantUtils.getTenantCode())
        .in(SaleRebatePolicyDetail::getId, idList)
        .list();
  }

  /**
   * 删除
   *
   * @param idList
   */
  public void delete(List<String> idList) {
    this.lambdaUpdate()
        .in(SaleRebatePolicyDetail::getId, idList)
        .eq(SaleRebatePolicyDetail::getTenantCode,TenantUtils.getTenantCode())
        .remove();
  }

  /**
   * 通过ID
   *
   * @param id
   * @return
   */
  public SaleRebatePolicyDetail findById(String id) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(SaleRebatePolicyDetail::getTenantCode, TenantUtils.getTenantCode())
        .eq(SaleRebatePolicyDetail::getId, id)
        .one();
  }

  /**
   * 物理删除批次号中测试数据
   *
   * @param saleRebatePolicyCode
   * @param isTest
   * @param speedNo
   * <AUTHOR>
   * @date
   */
  public void deleteByRebateCodeAndTest(String saleRebatePolicyCode, String isTest,
      String speedNo) {
    this.lambdaUpdate()
        .eq(SaleRebatePolicyDetail::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .eq(SaleRebatePolicyDetail::getIsTest, isTest)
        .eq(SaleRebatePolicyDetail::getTenantCode, TenantUtils.getTenantCode())
        .notIn(SaleRebatePolicyDetail::getSpeedNo, speedNo).remove();
  }

  /**
   * 通过id和租户编号修改
   * @param entityOld
   * @param tenantCode
   */
  public void updateByIdAndTenantCode(SaleRebatePolicyDetail entityOld, String tenantCode) {
    LambdaUpdateWrapper<SaleRebatePolicyDetail>lambdaUpdateWrapper= Wrappers.lambdaUpdate();
    lambdaUpdateWrapper.eq(SaleRebatePolicyDetail::getTenantCode,tenantCode);
    lambdaUpdateWrapper.in(SaleRebatePolicyDetail::getId,entityOld.getId());
    this.baseMapper.update(entityOld,lambdaUpdateWrapper);
  }
}

