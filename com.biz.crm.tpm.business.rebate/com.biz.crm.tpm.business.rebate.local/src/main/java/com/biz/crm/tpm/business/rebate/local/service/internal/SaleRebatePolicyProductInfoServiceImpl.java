package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyProductInfo;
import com.biz.crm.tpm.business.rebate.local.repository.SaleRebatePolicyProductInfoRepository;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyProductInfoService;
import com.biz.crm.tpm.business.rebate.sdk.enums.AllocationTypeEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 返利政策分配商品(SaleRebatePolicyProductInfo)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:58
 */
@Service("saleRebatePolicyProductInfoService")
public class SaleRebatePolicyProductInfoServiceImpl implements SaleRebatePolicyProductInfoService {

  @Autowired(required = false)
  private SaleRebatePolicyProductInfoRepository saleRebatePolicyProductInfoRepository;

  /**
   * id集合删除
   *
   * @param idList id集合
   * @return Result
   */
  @Transactional
  @Override
  public void delete(List<String> idList) {
    Validate.isTrue(!CollectionUtils.isEmpty(idList), "传入id集合为空");
    this.saleRebatePolicyProductInfoRepository.removeByIdsAndTenantCode(idList,TenantUtils.getTenantCode());
  }

  /**
   * 返利编码删除
   *
   * @param saleRebatePolicyCode 返利编码
   * @return Result
   */
  @Transactional
  @Override
  public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    Validate.notBlank(saleRebatePolicyCode, "传入返利编码为空");
    this.saleRebatePolicyProductInfoRepository.deleteBySaleRebatePolicyCode(saleRebatePolicyCode);
  }

  /**
   * 通过政策编码查询
   *
   * @param saleRebatePolicyCode
   * @return
   */
  @Override
  public List<SaleRebatePolicyProductInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    if (StringUtils.isBlank(saleRebatePolicyCode)) {
      return null;
    }
    return this.saleRebatePolicyProductInfoRepository
        .findBySaleRebatePolicyCode(saleRebatePolicyCode);
  }

  /**
   * 通过政策编码查询
   *
   * @param saleRebatePolicyCodes
   * @return
   */
  @Override
  public List<SaleRebatePolicyProductInfo> findBySaleRebatePolicyCodes(
      List<String> saleRebatePolicyCodes) {
    if (CollectionUtils.isEmpty(saleRebatePolicyCodes)) {
      return null;
    }
    return this.saleRebatePolicyProductInfoRepository
        .findBySaleRebatePolicyCodes(saleRebatePolicyCodes);
  }

  /**
   * 批量新增
   *
   * @param list
   */
  @Override
  @Transactional
  public void createBatch(List<SaleRebatePolicyProductInfo> list) {
    Validate.isTrue(!CollectionUtils.isEmpty(list), "创建时返利产品信息不能为空");
    //创建验证
    for (SaleRebatePolicyProductInfo saleRebatePolicyProductInfo : list) {
      this.createValidate(saleRebatePolicyProductInfo);
      saleRebatePolicyProductInfo.setTenantCode(TenantUtils.getTenantCode());
    }
    //验证分配比例
    this.rebateRatioValidate(list);
    this.saleRebatePolicyProductInfoRepository.saveBatch(list);
  }


  /**
   * 新增验证
   *
   * @param saleRebatePolicyProductInfo
   */
  private void createValidate(SaleRebatePolicyProductInfo saleRebatePolicyProductInfo) {
    Validate.notNull(saleRebatePolicyProductInfo, "返利商品数据操作时，对象信息不能为空！");
    saleRebatePolicyProductInfo.setId(null);
    saleRebatePolicyProductInfo.setTenantCode(TenantUtils.getTenantCode());
    Validate.notBlank(saleRebatePolicyProductInfo.getTenantCode(), "返利商品数据操作时，租户编号不能为空！");
    Validate.notBlank(saleRebatePolicyProductInfo.getType(), "返利商品数据操作时，范围类型不能为空！");
    Validate.notBlank(saleRebatePolicyProductInfo.getCode(), "返利商品数据操作时，商品或产品层级编码不能为空！");
    Validate.notBlank(saleRebatePolicyProductInfo.getName(), "返利商品数据操作时，商品或产品层级名称不能为空！");
    Validate.notBlank(saleRebatePolicyProductInfo.getSaleRebatePolicyCode(), "返利商品数据操作时，返利政策业务编号不能为空！");

  }


  /**
   * 分配比例验证
   *
   * @param list
   */
  private void rebateRatioValidate(List<SaleRebatePolicyProductInfo> list) {
    BigDecimal res = BigDecimal.ZERO;
    List<Integer> collect = list.stream().map(SaleRebatePolicyProductInfo::getAllocationType).distinct()
        .collect(Collectors.toList());
    Validate.isTrue((collect.size() == 1), "返利商品分配比例类型不统一！");
    if(AllocationTypeEnum.RATIO_AMOUNT.getKey().equals(collect.get(0))){
      for (SaleRebatePolicyProductInfo saleRebatePolicyProductInfo : list) {
        BigDecimal rebateRatio = saleRebatePolicyProductInfo.getRebateRatio();
        Validate.notNull(rebateRatio, "返利商品比例为空！");
        res = res.add(rebateRatio);
      }
      Validate.isTrue((res.compareTo(new BigDecimal("100")) == 0), "分配比例错误，总和应该等于100");
    }else {
      list.forEach(s->{
        s.setRebateRatio(BigDecimal.valueOf(100));
      });
    }
  }
}

