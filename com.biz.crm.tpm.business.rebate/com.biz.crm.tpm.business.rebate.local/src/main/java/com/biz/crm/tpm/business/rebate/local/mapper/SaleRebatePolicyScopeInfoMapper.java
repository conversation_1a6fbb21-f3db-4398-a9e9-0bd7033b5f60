package com.biz.crm.tpm.business.rebate.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyScopeInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 以圈定某种范围类型的方式，确认特定返利政策的适用客户范围（标品中支持经销商、渠道和组织机构三种圈定方式(SaleRebatePolicyScopeInfo)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:58
 */
public interface SaleRebatePolicyScopeInfoMapper extends BaseMapper<SaleRebatePolicyScopeInfo> {

  /**
   * 分页查询
   *
   * @param page                      分页
   * @param saleRebatePolicyScopeInfo
   * @return
   */
  public Page<SaleRebatePolicyScopeInfo> findByConditions(
      @Param("page") Page<SaleRebatePolicyScopeInfo> page,
      @Param("saleRebatePolicyScopeInfo") SaleRebatePolicyScopeInfo saleRebatePolicyScopeInfo);
}

