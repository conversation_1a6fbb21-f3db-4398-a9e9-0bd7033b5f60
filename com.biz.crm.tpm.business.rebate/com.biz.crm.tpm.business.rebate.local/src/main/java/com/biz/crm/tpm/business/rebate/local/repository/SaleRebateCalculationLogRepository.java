package com.biz.crm.tpm.business.rebate.local.repository;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateCalculationLog;
import com.biz.crm.tpm.business.rebate.local.mapper.SaleRebateCalculationLogMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;


/**
 * 返利政策计算日志(SaleRebateCalculationLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-28 15:19:44
 */
@Component
public class SaleRebateCalculationLogRepository extends
    ServiceImpl<SaleRebateCalculationLogMapper, SaleRebateCalculationLog> {

  /**
   * 物理删除批次号中测试数据
   *
   * @param saleRebatePolicyCode
   * @param isTest
   * @param speedNo
   * <AUTHOR>
   * @date
   */
  public void deleteByRebateCodeAndTest(String saleRebatePolicyCode, String isTest,
      String speedNo) {
    this.lambdaUpdate()
        .eq(SaleRebateCalculationLog::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .eq(SaleRebateCalculationLog::getIsTest, isTest)
        .eq(SaleRebateCalculationLog::getTenantCode, TenantUtils.getTenantCode())
        .notIn(SaleRebateCalculationLog::getSpeedNo, speedNo).remove();
  }

  /**
   * 通过返利明细id查询返利计算日志数据
   *
   * @param saleRebateCalculationLog 返利明细
   * @return 所有数据
   */
  public Page<SaleRebateCalculationLog> findByConditions(Pageable pageable,
      SaleRebateCalculationLog saleRebateCalculationLog) {
    Page<SaleRebateCalculationLog> page = new Page<>(pageable.getPageNumber(),
        pageable.getPageSize());
    saleRebateCalculationLog.setTenantCode(TenantUtils.getTenantCode());
    Page<SaleRebateCalculationLog> pageList = this.baseMapper
        .findByConditions(page, saleRebateCalculationLog);
    return pageList;
  }
}

