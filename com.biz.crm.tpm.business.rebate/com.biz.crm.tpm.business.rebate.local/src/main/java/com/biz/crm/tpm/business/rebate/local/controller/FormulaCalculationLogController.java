package com.biz.crm.tpm.business.rebate.local.controller;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/30 17:06
 */

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaCalculationLog;
import com.biz.crm.tpm.business.rebate.local.service.FormulaCalculationLogService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/formulaCalculationLogController")
@Slf4j
public class FormulaCalculationLogController {

    @Autowired
    private FormulaCalculationLogService formulaCalculationLogService;


    @ApiOperation(value = "查询列表")
    @GetMapping("/findList")
    public Result<Page<FormulaCalculationLog>> findList(@PageableDefault(50) Pageable pageable, @RequestParam String businessCode, @RequestParam String calType) {
        FormulaCalculationLog log = new FormulaCalculationLog();
        log.setBusinessDetailCode(businessCode);
        log.setCalType(calType);
        return Result.ok(formulaCalculationLogService.findPage(pageable, log));
    }

}
