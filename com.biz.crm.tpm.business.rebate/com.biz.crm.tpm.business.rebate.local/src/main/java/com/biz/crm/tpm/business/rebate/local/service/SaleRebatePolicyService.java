package com.biz.crm.tpm.business.rebate.local.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicy;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleRebatePolicyStatusEnum;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 返利政策，按照租户进行隔离(SaleRebatePolicy)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:46
 */
public interface SaleRebatePolicyService {

  /**
   * 分页查询
   *
   * @param pageable         分页参数
   * @param saleRebatePolicy
   * @return
   */
  Page<SaleRebatePolicy> findByConditions(Pageable pageable, SaleRebatePolicy saleRebatePolicy);

  /**
   * ͨ通过id查询
   *
   * @param id
   * @return
   */
  SaleRebatePolicy findById(String id);

  /**
   * 新增
   *
   * @param saleRebatePolicyDto
   * @return
   */
  SaleRebatePolicy create(SaleRebatePolicyDto saleRebatePolicyDto);

  /**
   * 编辑
   *
   * @param saleRebatePolicyDto
   * @return
   */
  SaleRebatePolicy update(SaleRebatePolicyDto saleRebatePolicyDto);

  /**
   * 批量删除
   *
   * @param idList
   * @return
   */
  void delete(List<String> idList);

  /**
   * 通过编码查询基础信息
   *
   * @param codes
   * @return
   */
  List<SaleRebatePolicy> findBySaleRebatePolicyCodes(List<String> codes);

  /**
   * 通过编码查询基础信息
   *
   * @param code
   * @return
   */
  SaleRebatePolicy findBySaleRebatePolicyCode(String code);

  /**
   * 批量启用模板
   *
   * @param ids
   */
  void enableBatch(List<String> ids);

  /**
   * 批量禁用模板
   *
   * @param ids
   */
  void disableBatch(List<String> ids);

  /**
   * 修改状态
   *
   * @param id
   * @return
   */
  void updateSaleRebatePolicyStatus(String id, SaleRebatePolicyStatusEnum saleRebatePolicyStatusEnum);

}

