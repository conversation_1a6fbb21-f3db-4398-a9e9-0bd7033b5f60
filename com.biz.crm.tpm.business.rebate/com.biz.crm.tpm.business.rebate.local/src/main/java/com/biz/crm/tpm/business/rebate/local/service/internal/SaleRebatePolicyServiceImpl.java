package com.biz.crm.tpm.business.rebate.local.service.internal;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.common.form.sdk.DynamicFormService;
import com.biz.crm.common.form.sdk.DynamicFormServiceBuilder;
import com.biz.crm.common.sequese.sdk.generator.service.CrmSequeseFactoryService;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicy;
import com.biz.crm.tpm.business.rebate.local.repository.SaleRebatePolicyRepository;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyTaskService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateRegisterService;
import com.biz.crm.tpm.business.rebate.sdk.constant.SaleRebateCycleConstant;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyLogEventDto;
import com.biz.crm.tpm.business.rebate.sdk.enums.SaleRebatePolicyStatusEnum;
import com.biz.crm.tpm.business.rebate.sdk.event.SaleRebatePolicyEventListener;
import com.biz.crm.tpm.business.rebate.sdk.event.SaleRebatePolicyLogEventListener;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCycleRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyElementDataVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.dynamic.ConfigurableCriterionVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 返利政策，按照租户进行隔离(SaleRebatePolicy)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:46
 */
@Service("saleRebatePolicyService")
public class SaleRebatePolicyServiceImpl<T extends SaleRebatePolicyElementDataVo> implements
        SaleRebatePolicyService {

  @Autowired(required = false)
  private SaleRebatePolicyRepository saleRebatePolicyRepository;
  @Autowired(required = false)
  private SaleRebatePolicyTaskService saleRebatePolicyTaskService;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private GenerateCodeService generateCodeService;
  @Autowired(required = false)
  private SaleRebateRegisterService saleRebateRegisterService;
  @Autowired(required = false)
  private List<SaleRebatePolicyEventListener> saleRebatePolicyEventListeners;
  @Autowired(required = false)
  private List<SaleRebatePolicyCycleRegister> saleRebatePolicyCycleRegisters;
  @Autowired(required = false)
  private ApplicationContext applicationContext;
  @Autowired(required = false)
  private List<SaleRebatePolicyCriterionRegister> saleRebatePolicyCriterionRegisters;

  @Autowired(required = false)
  private CrmSequeseFactoryService crmSequeseFactoryService;
  @Autowired(required = false)
  private LoginUserService loginUserService;
  @Autowired(required = false)
  private CustomerVoService customerVoService;
  @Autowired(required = false)
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired(required = false)
  private MdmCostCenterVoService costCenterVoService;


  /**
   * 分页查询
   *
   * @param pageable         分页参数
   * @param saleRebatePolicy
   * @return
   */
  @Override
  public Page<SaleRebatePolicy> findByConditions(Pageable pageable,
                                                 SaleRebatePolicy saleRebatePolicy) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(saleRebatePolicy)) {
      saleRebatePolicy = new SaleRebatePolicy();
    }
    return this.saleRebatePolicyRepository.findByConditions(pageable, saleRebatePolicy);
  }

  @Override
  public SaleRebatePolicy findById(String id) {
    return this.saleRebatePolicyRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
  }

  /**
   * 新增 1，基础验证 2，组装数据 3，保存要素 4，保存周期定时任务 5，保存主表信息 6，发通知
   *
   * @param saleRebatePolicyDto
   * @return
   */
  @Transactional(rollbackFor = Throwable.class)
  @Override
  public SaleRebatePolicy create(SaleRebatePolicyDto saleRebatePolicyDto) {
    // 1
    this.createValidate(saleRebatePolicyDto);
    // 2
    SaleRebatePolicy entity = this.transformCreate(saleRebatePolicyDto);
    // 3
    this.saveSaleRebatePolicyElementData(saleRebatePolicyDto);
    //4
    this.saveSaleRebatePolicyCycle(saleRebatePolicyDto);
    // 5
    entity.setSaleRebatePolicyStatus(SaleRebatePolicyStatusEnum.WAIT_EXECUTION.getKey());
    entity.setTenantCode(TenantUtils.getTenantCode());
    this.saleRebatePolicyRepository.save(entity);
    // 如果有自定义配置的返利变量也要保存
    this.saveCriterionConfig(saleRebatePolicyDto);
    // 6
    if (!CollectionUtils.isEmpty(this.saleRebatePolicyEventListeners)) {
      SaleRebatePolicyVo saleRebatePolicyVo =
          this.nebulaToolkitService.copyObjectByBlankList(
              saleRebatePolicyDto, SaleRebatePolicyVo.class, HashSet.class, ArrayList.class);
      this.saleRebatePolicyEventListeners.forEach(
          eventListener -> {
            eventListener.onCreate(saleRebatePolicyVo);
          });
    }
    // 创建事件
    SaleRebatePolicyLogEventDto eventDto = new SaleRebatePolicyLogEventDto();
    eventDto.setOriginal(null);
    SaleRebatePolicyVo newVo = this.buildVo(entity);
    eventDto.setNewest(newVo);
    SerializableBiConsumer<SaleRebatePolicyLogEventListener, SaleRebatePolicyLogEventDto> consumer =
            SaleRebatePolicyLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(eventDto, SaleRebatePolicyLogEventListener.class, consumer);
    return entity;
  }

  /**
   * 保存返利变量的配置
   * - 利用的动态表单
   *
   * @param saleRebatePolicyDto 销售返利政策dto
   */
  private void saveCriterionConfig(SaleRebatePolicyDto saleRebatePolicyDto) {

    /**
     * - 扩展信息全部都放到这个json里了。
     * (ps: demo中的入参就是json，它可以把json转成业务主表vo模型。但这里不可以，因为入参是业务主表的javaBean的Dto，接收会丢失数据，所以，这里补充了个json属性，但只用来接收可变表单数据）
     */
    JSONObject configurableCriterion = saleRebatePolicyDto.getConfigurableCriterion();
    // -把前端传过来的json转换成动态表单可是使用的结构
    Boolean reassemble = this.reassemble(configurableCriterion);
    if (!reassemble) {
      // - 没有可配置返利变量的表单信息
      return;
    }
    /**
     * - 把json里的信息转成动态表单的可用模型(ps:动态表单，还必须用map接收第一层参数）
     * - 所以必须有模型让动态表单用，模型里还必须有个map来接收每个表单
     */
    DynamicFormServiceBuilder<ConfigurableCriterionVo> dynamicFormServiceBuilder = applicationContext.getBean(DynamicFormServiceBuilder.class , ConfigurableCriterionVo.class , this.applicationContext);
    /**
     * - 构建 DynamicFormService
     * - map<返利变量code，动态表单编码>
     * - 动态表单编码，可以随意命名。
     *   - 但必须在动态表单所有业务中唯一
     *   - 操作策略实现类里的dynamicFormCode()方法返回值必须是同一个值
     *   - 此处业务开发，让返利变量code和动态表单code一致
     */
    DynamicFormService<ConfigurableCriterionVo> dynamicFormService =
            dynamicFormServiceBuilder.dynamicField("criterionMap")
                    .addDynamicMapping("", "")
                    .config()
                    .build();
//        dynamicFormServiceBuilder.dynamicField("criterionMap")
//            .addDynamicMapping(SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE, SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE)
//            .config()
//            .build();
    /**
     * 将json转成模型
     */
    ConfigurableCriterionVo configurableCriterionVo = dynamicFormService.dynamic(configurableCriterion);
    /**
     * 保存信息
     */
    dynamicFormService.createDynamicDetails(configurableCriterionVo, saleRebatePolicyDto.getSaleRebatePolicyCode());
  }

  /**
   * 重新组装
   * - 原结构：
   * {
   *   "configurableCriterion":{
   * 	    "criterionMap":{
   * 	      “返利变量实例编码”：{表单内容}
   * 	    }
   * 	 }
   * 	}
   * - 转换后的结构：
   * {
   *   "configurableCriterion":{
   * 	    "criterionMap":{
   * 	        "返利变量模板"：[
   * 	          {
   * 	            "instanceCode":"返利变量实例编码",
   * 	            // 表单内容的其它属性对
   * 	          }
   * 	        ]
   * 	    }
   * 	 }
   * 	}
   *
   * @param configurableCriterion 可配置标准
   */
  private Boolean reassemble(JSONObject configurableCriterion) {
    Validate.isTrue(!CollectionUtils.isEmpty(saleRebatePolicyCriterionRegisters), "系统中不存在返利变量！");
    if (ObjectUtils.isEmpty(configurableCriterion)) {
      return false;
    }
    JSONObject criterionMap = configurableCriterion.getJSONObject("criterionMap");
    if (ObjectUtils.isEmpty(criterionMap)) {
      return false;
    }
    Set<String> keySet = criterionMap.keySet();
    if (CollectionUtils.isEmpty(keySet)) {
      return false;
    }
    JSONObject criterionMapAfter = new JSONObject(keySet.size());
    for (String instanceCode : keySet) {
      // - 变量模板code
      Optional<SaleRebatePolicyCriterionRegister> registerOpt = saleRebatePolicyCriterionRegisters.stream()
          .filter(register ->
              StringUtils.startsWith(instanceCode, register.getSaleRebatePolicyCriterionCode())
          )
          .findFirst();
      if (!registerOpt.isPresent()) {
        continue;
      }
      SaleRebatePolicyCriterionRegister criterionRegister = registerOpt.get();
      String criterionCode = criterionRegister.getSaleRebatePolicyCriterionCode();
      // - 表单内添加属性
      JSONObject formJson = criterionMap.getJSONObject(instanceCode);
      formJson.put("instanceCode", instanceCode);
      // - 表单信息按返利变量模板分组
      JSONArray jsonArray = criterionMapAfter.getJSONArray(criterionCode);
      if (ObjectUtils.isNotEmpty(jsonArray)) {
        jsonArray.add(formJson);
      } else {
        JSONArray array = new JSONArray();
        array.add(formJson);
        criterionMapAfter.put(criterionCode, array);
      }
    }
    // - 替换原来的criterionMap
    configurableCriterion.put("criterionMap", criterionMapAfter);
    return true;
  }

  /**
   * 编辑 1，基础验证 2，组装数据 3，更新要素 4，保存周期定时任务 5，更新主表信息 6，发通知
   *
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  @Transactional
  @Override
  public SaleRebatePolicy update(SaleRebatePolicyDto saleRebatePolicyDto) {
    // 1
    this.updateValidate(saleRebatePolicyDto);
    // 2
    SaleRebatePolicy saleRebatePolicyOld = this.saleRebatePolicyRepository
        .getById(saleRebatePolicyDto.getId());
    SaleRebatePolicy entity = this.transformUpdate(saleRebatePolicyDto, saleRebatePolicyOld);
    // 3
    this.updateSaleRebatePolicyElementData(saleRebatePolicyDto);
    //4
    this.updateSaleRebatePolicyCycle(saleRebatePolicyDto);
    // 5 只做业务数据修改 不做状态修改
    this.saleRebatePolicyRepository.updateByIdAndTenantCode(entity,TenantUtils.getTenantCode());
    // 如果有自定义配置的返利变量也要修改
    this.updateCriterionConfig(saleRebatePolicyDto);
    // 6
    if (!CollectionUtils.isEmpty(this.saleRebatePolicyEventListeners)) {
      SaleRebatePolicyVo oldVo =
          this.nebulaToolkitService.copyObjectByWhiteList(
              saleRebatePolicyOld, SaleRebatePolicyVo.class, HashSet.class, ArrayList.class);
      SaleRebatePolicyVo newVo =
          this.nebulaToolkitService.copyObjectByBlankList(
              entity, SaleRebatePolicyVo.class, HashSet.class, ArrayList.class);
      this.saleRebatePolicyEventListeners.forEach(
          eventListener -> {
            eventListener.onUpdate(oldVo, newVo);
          });
    }
    // 更新事件
    SaleRebatePolicyLogEventDto eventDto = new SaleRebatePolicyLogEventDto();
    eventDto.setOriginal(this.buildVo(saleRebatePolicyOld));
    eventDto.setNewest(this.buildVo(entity));
    SerializableBiConsumer<SaleRebatePolicyLogEventListener, SaleRebatePolicyLogEventDto> consumer =
            SaleRebatePolicyLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(eventDto, SaleRebatePolicyLogEventListener.class, consumer);
    return entity;
  }

  /**
   * 修改返利变量的配置
   * - 利用的动态表单
   *
   * @param saleRebatePolicyDto 销售返利政策dto
   */
  private void updateCriterionConfig(SaleRebatePolicyDto saleRebatePolicyDto) {
    DynamicFormServiceBuilder<ConfigurableCriterionVo> dynamicFormServiceBuilder = applicationContext.getBean(DynamicFormServiceBuilder.class , ConfigurableCriterionVo.class , this.applicationContext);
    DynamicFormService<ConfigurableCriterionVo> dynamicFormService =
            dynamicFormServiceBuilder.dynamicField("criterionMap")
                    .addDynamicMapping("", "")
                    .config()
                    .build();
//        dynamicFormServiceBuilder.dynamicField("criterionMap")
//            .addDynamicMapping(SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE, SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE)
//            .config()
//            .build();
    //先清除原本数据，即便后面逻辑 新数据转换不出来结构 也应当先删除数据
    dynamicFormService.deleteDynamicDetails(saleRebatePolicyDto.getSaleRebatePolicyCode());
    JSONObject configurableCriterion = saleRebatePolicyDto.getConfigurableCriterion();
    // -把前端传过来的json转换成动态表单可是使用的结构
    Boolean reassemble = this.reassemble(configurableCriterion);
    if (!reassemble) {
      // - 没有可配置返利变量的表单信息
      return;
    }
    /**
     * 将json转成模型
     */
    ConfigurableCriterionVo configurableCriterionVo = dynamicFormService.dynamic(configurableCriterion);
    /**
     * 保存信息
     */
    dynamicFormService.modifyDynamicDetails(configurableCriterionVo, saleRebatePolicyDto.getSaleRebatePolicyCode());
  }

  /**
   * id集合删除
   *
   * @param idList id集合
   * @return Result
   */
  @Transactional
  @Override
  public void delete(List<String> idList) {
    Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除时，主键集合不能为空！");
    List<SaleRebatePolicy> entities = this.saleRebatePolicyRepository.listByIdsAndTenantCode(idList,TenantUtils.getTenantCode());
    Validate.isTrue(!CollectionUtils.isEmpty(entities) && entities.size() == idList.size(), "数据删除个数不匹配");
    List<SaleRebatePolicy> collect1 = entities.stream().filter(
        saleRebatePolicy -> !SaleRebatePolicyStatusEnum.WAIT_EXECUTION.getKey().equals(saleRebatePolicy.getSaleRebatePolicyStatus())).collect(Collectors.toList());
    Validate.isTrue(CollectionUtils.isEmpty(collect1) , "只能删除待执行状态返利政策");
    this.saleRebatePolicyRepository.updateFlagByIdsAndTenantCode(idList, TenantUtils.getTenantCode());
    //这里直接删除调用定时任务
    List<String> collect = entities.stream().map(SaleRebatePolicy::getSaleRebatePolicyCode).collect(Collectors.toList());
    saleRebatePolicyTaskService.deleteBatchBySaleRebatePolicyCodes(collect);
  }

 /**
  *
  * 批量恢复
  * @param ids
  * <AUTHOR>
  * @date
  */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void enableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "待修正的数据主键不能为空");
    List<SaleRebatePolicy> entities = this.saleRebatePolicyRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    Validate.isTrue(
        !CollectionUtils.isEmpty(entities) && entities.size() == ids.size(), "数据恢复个数不匹配");
    this.saleRebatePolicyRepository.updateEnableStatusByIdIn(EnableStatusEnum.ENABLE, ids);
    //这里调用定时（启用）
    List<String> collect = entities.stream().map(SaleRebatePolicy::getSaleRebatePolicyCode)
        .collect(Collectors.toList());
    saleRebatePolicyTaskService.effectiveBatchBySaleRebatePolicyCodes(collect);
    // 启用合同模板事件通知
    if (!CollectionUtils.isEmpty(saleRebatePolicyEventListeners)) {
      List<SaleRebatePolicyVo> voList =
          (List<SaleRebatePolicyVo>)
              this.nebulaToolkitService.copyCollectionByWhiteList(
                  entities, SaleRebatePolicy.class, SaleRebatePolicyVo.class, HashSet.class,
                  ArrayList.class);
      saleRebatePolicyEventListeners.forEach(
          listener -> {
            listener.onEnable(voList);
          });
    }
    //启用事件
    List<SaleRebatePolicyVo> voList =
            (List<SaleRebatePolicyVo>) this.nebulaToolkitService.copyCollectionByBlankList(entities,
                                                                                          SaleRebatePolicy.class
                    , SaleRebatePolicyVo.class, HashSet.class, LinkedList.class);

    SaleRebatePolicyLogEventDto eventDto = new SaleRebatePolicyLogEventDto();
    eventDto.setNewestList(voList);
    SerializableBiConsumer<SaleRebatePolicyLogEventListener, SaleRebatePolicyLogEventDto> consumer =
            SaleRebatePolicyLogEventListener::onEnable;
    this.nebulaNetEventClient.publish(eventDto, SaleRebatePolicyLogEventListener.class, consumer);
  }

  /**
   *
   * 作废
   * @param ids
   * <AUTHOR>
   * @date
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void disableBatch(List<String> ids) {
    Validate.isTrue(org.apache.commons.collections.CollectionUtils.isNotEmpty(ids), "缺失id");
    List<SaleRebatePolicy> entities = this.saleRebatePolicyRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    Validate.isTrue(
        org.apache.commons.collections.CollectionUtils.isNotEmpty(entities)
            && entities.size() == ids.size(),
        "数据作废个数不匹配");
    this.saleRebatePolicyRepository.updateEnableStatusByIdIn(EnableStatusEnum.DISABLE, ids);
    //这里调用定时（禁用）
    List<String> collect = entities.stream().map(SaleRebatePolicy::getSaleRebatePolicyCode)
        .collect(Collectors.toList());
    saleRebatePolicyTaskService.invalidBatchBySaleRebatePolicyCodes(collect);
    // 禁用合同模板事件通知
    if (!CollectionUtils.isEmpty(saleRebatePolicyEventListeners)) {
      List<SaleRebatePolicyVo> voList =
          (List<SaleRebatePolicyVo>)
              this.nebulaToolkitService.copyCollectionByWhiteList(
                  entities, SaleRebatePolicy.class, SaleRebatePolicyVo.class, HashSet.class,
                  ArrayList.class);
      saleRebatePolicyEventListeners.forEach(
          listener -> {
            listener.onDisable(voList);
          });
    }
    //禁用事件
    List<SaleRebatePolicyVo> voList =
            (List<SaleRebatePolicyVo>) this.nebulaToolkitService.copyCollectionByBlankList(entities,
                                                                                           SaleRebatePolicy.class
                    , SaleRebatePolicyVo.class, HashSet.class, LinkedList.class);

    SaleRebatePolicyLogEventDto eventDto = new SaleRebatePolicyLogEventDto();
    eventDto.setNewestList(voList);
    SerializableBiConsumer<SaleRebatePolicyLogEventListener, SaleRebatePolicyLogEventDto> consumer =
            SaleRebatePolicyLogEventListener::onDisable;
    this.nebulaNetEventClient.publish(eventDto, SaleRebatePolicyLogEventListener.class, consumer);
  }

  @Override
  @Transactional
  public void updateSaleRebatePolicyStatus(String id,
      SaleRebatePolicyStatusEnum saleRebatePolicyStatusEnum) {
    this.saleRebatePolicyRepository.updateSaleRebatePolicyStatus(saleRebatePolicyStatusEnum,id);
  }

  /**
   * 通过返利政策编码查询
   *
   * @param codes
   * @return
   */
  @Override
  public List<SaleRebatePolicy> findBySaleRebatePolicyCodes(List<String> codes) {
    return this.saleRebatePolicyRepository
        .findBySaleRebatePolicyCodes(codes, TenantUtils.getTenantCode());
  }

  @Override
  public SaleRebatePolicy findBySaleRebatePolicyCode(String code) {
    return this.saleRebatePolicyRepository
        .findBySalePolicyCodeAndTenantCode(code, TenantUtils.getTenantCode());
  }

  /**
   * 新增数据填充
   *
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  private SaleRebatePolicy transformCreate(SaleRebatePolicyDto saleRebatePolicyDto) {
    SaleRebatePolicy entity =
        this.nebulaToolkitService.copyObjectByWhiteList(
            saleRebatePolicyDto, SaleRebatePolicy.class, HashSet.class, ArrayList.class);
    if (StringUtils.isBlank(entity.getSaleRebatePolicyCode())) {
      entity.setSaleRebatePolicyCode(this.generateCodeService.generateCode(SaleRebateCycleConstant.REBATE_CODE_PREFIX));
    } else {
      Validate.isTrue(entity.getSaleRebatePolicyCode().length() < 128, "返利政策编码长度不能超过128");
    }
    entity.setTenantCode(TenantUtils.getTenantCode());
    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    saleRebatePolicyDto.setSaleRebatePolicyCode(entity.getSaleRebatePolicyCode());
    //开始时间默认是00:00:00   结束时间默认是23::59:59
    //修改结束时间
    Calendar calendar=Calendar.getInstance();
    calendar.setTime(entity.getSaleRebateEndTime());
    calendar.set(Calendar.HOUR_OF_DAY, 23);
    calendar.set(Calendar.MINUTE, 59);
    calendar.set(Calendar.SECOND, 59);
    Date endTime = calendar.getTime();
    entity.setSaleRebateEndTime(endTime);
    entity.setCalculateType(saleRebatePolicyDto.getCalculateType());
    this.buildOrgAndPosition(entity);
    return entity;
  }

  /**
   * 修改数据填充
   *
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  private SaleRebatePolicy transformUpdate(SaleRebatePolicyDto saleRebatePolicyDto,
      SaleRebatePolicy saleRebatePolicy) {
    SaleRebatePolicy entity =
        this.nebulaToolkitService.copyObjectByWhiteList(
            saleRebatePolicyDto, SaleRebatePolicy.class, HashSet.class, ArrayList.class);
    Validate.isTrue(Objects.nonNull(saleRebatePolicy), "数据不存在");
    Validate.isTrue(!SaleRebatePolicyStatusEnum.WAIT_EXECUTION.equals(saleRebatePolicy.getSaleRebatePolicyStatus()),"只能修改待执行的政策！");
    entity.setTenantCode(saleRebatePolicy.getTenantCode());
    entity.setDelFlag(saleRebatePolicy.getDelFlag());
    entity.setSaleRebatePolicyStatus(entity.getSaleRebatePolicyStatus());
    entity.setEnableStatus(saleRebatePolicy.getEnableStatus());
    //开始时间默认是00:00:00   结束时间默认是23::59:59
    //修改结束时间
    Calendar calendar=Calendar.getInstance();
    calendar.setTime(entity.getSaleRebateEndTime());
    calendar.set(Calendar.HOUR_OF_DAY, 23);
    calendar.set(Calendar.MINUTE, 59);
    calendar.set(Calendar.SECOND, 59);
    Date endTime = calendar.getTime();
    entity.setSaleRebateEndTime(endTime);
    entity.setCalculateType(saleRebatePolicyDto.getCalculateType());
    this.buildOrgAndPosition(entity);
    return entity;
  }

  private void buildOrgAndPosition(SaleRebatePolicy entity) {
    if (Objects.isNull(entity) || StringUtils.isNotBlank(entity.getOrgCode())) {
      return;
    }
    // 填充组织和职位信息
    FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
    if (Objects.nonNull(loginDetails)) {
      entity.setPositionCode(loginDetails.getPostCode());
      entity.setOrgCode(loginDetails.getOrgCode());
      entity.setOrgName(loginDetails.getOrgName());
    }
  }
  /**
   * 编辑要素内容
   *
   * @param saleRebatePolicyDto
   */
  private void saveSaleRebatePolicyElementData(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    Map<String, JSONObject> data = saleRebatePolicyDto.getElementDataMap();
    Collection<Class<? extends SaleRebatePolicyElementRegister>> classes = this.saleRebateRegisterService
        .findTemplateCollection(saleRebatePolicyDto.getSaleRebateType());

    for (Class<? extends SaleRebatePolicyElementRegister> aClass : classes) {
      SaleRebatePolicyElementRegister saleRebatePolicyElementRegister = applicationContext
          .getBean(aClass);
      JSONObject jsonObject = data
          .get(saleRebatePolicyElementRegister.getSaleRebatePolicyElementCode());
      Validate.isTrue(Objects.nonNull(jsonObject),
          "返利政策中%s数据为空！",saleRebatePolicyElementRegister.getSaleRebatePolicyElementName());
      Class<T> elementClass = saleRebatePolicyElementRegister.getSaleRebatePolicyElementClass();
      saleRebatePolicyElementRegister.onRequestSaleRebatePolicyCreate(
          saleRebatePolicyDto.getSaleRebatePolicyCode(),
          JSON.parseObject(jsonObject.toJSONString(), elementClass));
    }
  }

  /**
   * 编辑要素内容
   *
   * @param saleRebatePolicyDto
   */
  private void updateSaleRebatePolicyElementData(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    Map<String, JSONObject> data = saleRebatePolicyDto.getElementDataMap();
    Collection<Class<? extends SaleRebatePolicyElementRegister>> classes = this.saleRebateRegisterService
        .findTemplateCollection(saleRebatePolicyDto.getSaleRebateType());
    for (Class<? extends SaleRebatePolicyElementRegister> aClass : classes) {
      SaleRebatePolicyElementRegister saleRebatePolicyElementRegister = applicationContext
          .getBean(aClass);
      JSONObject jsonObject = data
          .get(saleRebatePolicyElementRegister.getSaleRebatePolicyElementCode());
      Validate.isTrue(Objects.nonNull(jsonObject),
          "返利政策中%s数据为空！",saleRebatePolicyElementRegister.getSaleRebatePolicyElementName());
      Class<T> elementClass = saleRebatePolicyElementRegister.getSaleRebatePolicyElementClass();
      saleRebatePolicyElementRegister.onRequestSaleRebatePolicyUpdate(
          saleRebatePolicyDto.getSaleRebatePolicyCode(),
          JSON.parseObject(jsonObject.toJSONString(), elementClass));
    }
  }


  /**
   * 保存政策周期(定时任务)
   *
   * @param saleRebatePolicyDto
   */
  private void saveSaleRebatePolicyCycle(SaleRebatePolicyDto saleRebatePolicyDto) {
    String saleRebatePolicyCycle = saleRebatePolicyDto.getSaleRebatePolicyCycle();
    List<SaleRebatePolicyCycleRegister> collect = saleRebatePolicyCycleRegisters.stream().filter(
        saleRebatePolicyCycleRegister -> saleRebatePolicyCycleRegister
            .getSaleRebatePolicyCycleCode().equals(saleRebatePolicyCycle)).collect(
        Collectors.toList());
    Validate.isTrue(!CollectionUtils.isEmpty(collect), "政策周期要素不存在");
    SaleRebatePolicyCycleRegister saleRebatePolicyCycleRegister = collect.get(0);
    saleRebatePolicyCycleRegister.onRequestcreate(saleRebatePolicyDto);
  }

  /**
   * 保存政策周期(定时任务)
   *
   * @param saleRebatePolicyDto
   */
  private void updateSaleRebatePolicyCycle(SaleRebatePolicyDto saleRebatePolicyDto) {
    String saleRebatePolicyCycle = saleRebatePolicyDto.getSaleRebatePolicyCycle();
    List<SaleRebatePolicyCycleRegister> collect = saleRebatePolicyCycleRegisters.stream().filter(
        saleRebatePolicyCycleRegister -> saleRebatePolicyCycleRegister
            .getSaleRebatePolicyCycleCode().equals(saleRebatePolicyCycle)).collect(
        Collectors.toList());
    Validate.isTrue(!CollectionUtils.isEmpty(collect), "政策周期要素不存在");
    SaleRebatePolicyCycleRegister saleRebatePolicyCycleRegister = collect.get(0);
    saleRebatePolicyCycleRegister.onRequestUpdate(saleRebatePolicyDto);
  }

  /**
   * 新增基础验证
   *
   * @param saleRebatePolicyDto
   */
  private void createValidate(SaleRebatePolicyDto saleRebatePolicyDto) {
    Validate.notNull(saleRebatePolicyDto, "数据操作时，操作对象不能为空！");
    //Validate.notBlank(saleRebatePolicyDto.getActivityCode(), "数据操作时，活动编码不能为空！");
    //SaleRebatePolicy saleRebatePolicy = this.saleRebatePolicyRepository.findByActivityCode(saleRebatePolicyDto.getActivityCode());
    //Validate.isTrue(Objects.isNull(saleRebatePolicy), "该活动已存在促销政策");
    this.validate(saleRebatePolicyDto);
    saleRebatePolicyDto.setId(null);
  }

  /**
   * 编辑验证
   *
   * @param saleRebatePolicyDto
   */
  private void updateValidate(SaleRebatePolicyDto saleRebatePolicyDto) {
    Validate.notNull(saleRebatePolicyDto, "数据操作时，操作对象不能为空！");
    Validate.notBlank(saleRebatePolicyDto.getId(), "数据操作时，id不能为空！");
    SaleRebatePolicy old = this.saleRebatePolicyRepository.findByIdAndTenantCode(saleRebatePolicyDto.getId(), TenantUtils.getTenantCode());
    Validate.notNull(old, "修改数据不存在或已被删除");
    //Validate.isTrue(StringUtils.equals(old.getActivityCode(), saleRebatePolicyDto.getActivityCode()), "活动编码不能修改！");
    this.validate(saleRebatePolicyDto);
  }

  private void validate(SaleRebatePolicyDto saleRebatePolicyDto) {
    Validate.notNull(saleRebatePolicyDto, "数据操作时对象不能为空");
    Validate.notNull(saleRebatePolicyDto.getSaleRebateEndTime(), "数据操作时，返利政策结束时间（包括）不能为空！");
    Validate.notBlank(saleRebatePolicyDto.getSaleRebatePolicyName(), "数据操作时，促销名称不能为空！");
    Validate.notNull(saleRebatePolicyDto.getSaleRebateStartTime(), "数据操作时，返利政策开始时间（包括）不能为空！");
    Validate
            .isTrue(saleRebatePolicyDto.getSaleRebatePolicyName().length() <= 128, "返利政策名称不能超过32个字");
    Validate.isTrue(saleRebatePolicyDto.getSaleRebateStartTime()
                            .compareTo(saleRebatePolicyDto.getSaleRebateEndTime()) <= 0, "返利政策开始时间应早于结束时间");
    //Validate.notBlank(saleRebatePolicyDto.getSaleRebateType(), "返利类型不能为空!");
    //Validate.notNull(saleRebatePolicyDto.getBillType(), "上账类型不能为空!");
    Validate.notNull(saleRebatePolicyDto.getCalculateDayNum(), "计算时间不能为空!");
    //Validate.notBlank(saleRebatePolicyDto.getRemark(), "返利政策描述不能为空!");
    if (StringUtils.isNotBlank(saleRebatePolicyDto.getRemark())) {
      Validate.isTrue(saleRebatePolicyDto.getRemark().length() <= 400, "返利政策描述不能超过100个字");
    }
    Validate.notNull(saleRebatePolicyDto.getIsNewType(),"返利政策公式编辑器新旧版本标记不能为空!");
    //if (BooleanUtils.isTrue(saleRebatePolicyDto.getIsNewType())){
    //  Validate.notNull(saleRebatePolicyDto.getCalculateType(),"返利政策新增提交时，返利公式的命中策略不能为空，请检查！");
    //}
    //Validate.notBlank(saleRebatePolicyDto.getCustomerCode(),"客户编码不能为空!");
    //Validate.notBlank(saleRebatePolicyDto.getCostCenterCode(),"成本中心编码不能为空!");
    //Validate.notBlank(saleRebatePolicyDto.getActivityCode(),"活动编码不能为空!");
    //Validate.notNull(saleRebatePolicyDto.getActivityStartTime(),"活动开始时间不能为空!");
    //Validate.notNull(saleRebatePolicyDto.getActivityEndTime(),"活动结束时间不能为空!");
    //Validate.isTrue(saleRebatePolicyDto.getActivityStartTime().compareTo(saleRebatePolicyDto.getActivityEndTime()) <= 0,
    //                "活动开始时间应早于结束时间!");

    // 检查客户信息
    //List<CustomerVo> customerVoList = this.customerVoService.findByCustomerCodes(Lists.newArrayList(saleRebatePolicyDto.getCustomerCode()));
    //Validate.notEmpty(customerVoList, "客户不存在");
    //Validate.isTrue(customerVoList.size() == 1, "找到多个客户,请检查");
    //CustomerVo customerVo = customerVoList.get(0);
    //saleRebatePolicyDto.setCustomerName(customerVo.getCustomerName());
    //saleRebatePolicyDto.setCompanyCode(customerVo.getCompanyCode());
    //saleRebatePolicyDto.setCompanyName(customerVo.getCompanyName());
    //saleRebatePolicyDto.setErpCode(customerVo.getErpCode());
    //saleRebatePolicyDto.setProductGroupCode(customerVo.getProductGroupCode());
    //saleRebatePolicyDto.setChannelCode(customerVo.getChannelCode());
    //
    ////成本中心校验
    //MdmCostCenterVo costCenterVo = this.costCenterVoService.findByCode(saleRebatePolicyDto.getCostCenterCode());
    //Validate.notNull(costCenterVo, "成本中心不存在,请检查");
    //saleRebatePolicyDto.setCostCenterName(costCenterVo.getCostCenterName());
  }
  
  private SaleRebatePolicyVo buildVo(SaleRebatePolicy saleRebatePolicy) {
    String saleRebateType = saleRebatePolicy.getSaleRebateType();
    String saleRebatePolicyCode = saleRebatePolicy.getSaleRebatePolicyCode();
    //copy
    SaleRebatePolicyVo saleRebatePolicyVo = this.nebulaToolkitService
            .copyObjectByBlankList(saleRebatePolicy, SaleRebatePolicyVo.class, HashSet.class, ArrayList.class);
    Map<String, JSONObject> elementDataMap = new HashMap<>();
    //模板校验 
    Collection<Class<? extends SaleRebatePolicyElementRegister>> classes = this.saleRebateRegisterService
            .findTemplateCollection(saleRebateType);
    //查询要素信息并组装.
    for (Class<? extends SaleRebatePolicyElementRegister> aClass : classes) {
      SaleRebatePolicyElementRegister saleRebatePolicyElementRegister = applicationContext
              .getBean(aClass);
      SaleRebatePolicyElementDataVo vo = saleRebatePolicyElementRegister.getBySaleRebatePolicyCode(saleRebatePolicyCode);
      JSONObject jsonObject = JsonUtils.toJSONObject(vo);
      elementDataMap.put(saleRebatePolicyElementRegister.getSaleRebatePolicyElementCode(), jsonObject);
    }
    saleRebatePolicyVo.setElementDataMap(elementDataMap);
    //TODO  可配变量

    return saleRebatePolicyVo;
  }

}