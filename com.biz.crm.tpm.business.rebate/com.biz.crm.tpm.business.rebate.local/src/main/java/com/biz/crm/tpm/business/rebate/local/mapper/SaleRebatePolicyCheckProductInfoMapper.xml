<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyCheckProductInfoMapper">

  <resultMap type="com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyCheckProductInfo" id="SaleRebatePolicyCheckProductInfoMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
    <result property="productLevelCode" column="product_level_code" jdbcType="VARCHAR"/>
    <result property="productLevelName" column="product_level_name" jdbcType="VARCHAR"/>
    <result property="productName" column="product_name" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyCode" column="sale_rebate_policy_code" jdbcType="VARCHAR"/>
  </resultMap>
  
  <sql id = "saleRebatePolicyCheckProductInfo">
    tenant_code tenantCode,
    product_code productCode,
    product_level_code productLevelCode,
    product_level_name productLevelName,
    product_name productName,
    sale_rebate_policy_code saleRebatePolicyCode,
    id id
  </sql>

  <select id="findByConditions" resultMap="SaleRebatePolicyCheckProductInfoMap">
    select
      <include refid="saleRebatePolicyCheckProductInfo"/>
    from tpm_sale_rebate_policy_check_product_info
    where
    tenant_code=#{saleRebatePolicyCheckProductInfo.tenantCode}
  </select>
</mapper>

