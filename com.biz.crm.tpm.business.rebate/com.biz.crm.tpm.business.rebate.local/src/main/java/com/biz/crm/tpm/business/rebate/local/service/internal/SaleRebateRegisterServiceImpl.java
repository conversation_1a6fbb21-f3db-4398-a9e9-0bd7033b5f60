package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.biz.crm.tpm.business.rebate.local.service.SaleRebateRegisterService;
import com.biz.crm.tpm.business.rebate.local.service.register.elementregister.CheckProductSaleRebatePolicyElementRegisterImpl;
import com.biz.crm.tpm.business.rebate.local.service.register.elementregister.FormulaSaleRebatePolicyElementRegisterImpl;
import com.biz.crm.tpm.business.rebate.local.service.register.elementregister.ScopeSaleRebatePolicyElementRegisterImpl;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCycleRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyElementRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyTemplateRegister;
import com.biz.crm.tpm.business.rebate.sdk.strategy.SaleRebateCustomerScopeStrategy;
import com.biz.crm.tpm.business.rebate.sdk.vo.ConfigurableRebateRegisterVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebatePolicyRegisterVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.scope.AbstractSaleRebatePolicyCustomerInfo;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 返利周期service
 * @author: lifei
 * @date: 2022/2/21 10:44
 */
@Service
@Slf4j
public class SaleRebateRegisterServiceImpl implements SaleRebateRegisterService {

    @Autowired(required = false)
    private List<SaleRebatePolicyCycleRegister> saleRebatePolicyCycleRegisters;

    @Autowired(required = false)
    private List<SaleRebatePolicyTemplateRegister> saleRebatePolicyTemplateRegisters;

    @Autowired(required = false)
    private List<SaleRebatePolicyCriterionRegister> saleRebatePolicyCriterionRegisters;

    @Autowired(required = false)
    private ApplicationContext applicationContext;


    @Override
    public List<SaleRebatePolicyRegisterVo> findForRebateCycle() {
        Validate.isTrue(CollectionUtils.isNotEmpty(saleRebatePolicyCycleRegisters), "无可用的返利周期注册信息");
        List<SaleRebatePolicyRegisterVo> list = new ArrayList<>();
        saleRebatePolicyCycleRegisters.forEach(saleRebatePolicyCycleRegister -> {
            SaleRebatePolicyRegisterVo saleRebatePolicyRegisterVo = new SaleRebatePolicyRegisterVo();
            saleRebatePolicyRegisterVo.setIndexCode(saleRebatePolicyCycleRegister.getCycleSort());
            saleRebatePolicyRegisterVo
                    .setCode(saleRebatePolicyCycleRegister.getSaleRebatePolicyCycleCode());
            saleRebatePolicyRegisterVo
                    .setName(saleRebatePolicyCycleRegister.getSaleRebatePolicyCycleName());
            saleRebatePolicyRegisterVo.setCronExpression(saleRebatePolicyCycleRegister.getSaleRebateCronExpression());
            saleRebatePolicyRegisterVo.setCheckFiscalYear(saleRebatePolicyCycleRegister.getCheckFiscalYear());
            saleRebatePolicyRegisterVo.setCycleType(saleRebatePolicyCycleRegister.getCycleType());
            saleRebatePolicyRegisterVo.setCycleNum(saleRebatePolicyCycleRegister.getCycleNum());
            list.add(saleRebatePolicyRegisterVo);
        });
        this.sortList(list);
        return list;
    }


    /**
     * 查询集合
     *
     * @return
     */
    @Override
    public List<SaleRebatePolicyRegisterVo> findForRebateType() {
        Validate.isTrue(CollectionUtils.isNotEmpty(saleRebatePolicyTemplateRegisters), "无可用的返利类型注册信息");
        List<SaleRebatePolicyRegisterVo> list = new ArrayList<>();
        saleRebatePolicyTemplateRegisters.forEach(saleRebatePolicyTemplateRegister -> {
            SaleRebatePolicyRegisterVo saleRebatePolicyRegisterVo = new SaleRebatePolicyRegisterVo();
            saleRebatePolicyRegisterVo.setIndexCode(saleRebatePolicyTemplateRegister.getTemplateSort());
            saleRebatePolicyRegisterVo
                    .setCode(saleRebatePolicyTemplateRegister.getSaleRebatePolicytemplateCode());
            saleRebatePolicyRegisterVo
                    .setName(saleRebatePolicyTemplateRegister.getSaleRebatePolicytemplateName());
            list.add(saleRebatePolicyRegisterVo);
        });
        this.sortList(list);
        return list;
    }

    @Override
    public List<SaleRebatePolicyRegisterVo> findForRebateCriterion() {
        Validate.isTrue(CollectionUtils.isNotEmpty(saleRebatePolicyCriterionRegisters), "无可用的返利基准注册信息");
        List<SaleRebatePolicyRegisterVo> list = new ArrayList<>();
        for (SaleRebatePolicyCriterionRegister register : saleRebatePolicyCriterionRegisters) {
            ConfigurableRebateRegisterVo vo = new ConfigurableRebateRegisterVo();
            vo.setIndexCode(register.getCriterionSort());
            vo.setCode(register.getSaleRebatePolicyCriterionCode());
            vo.setName(register.getSaleRebatePolicyCriterionName());
            vo.setConfigurable(register.isConfigurable());
            list.add(vo);
        }
        this.sortList(list);
        return list;
    }

    @Override
    public List<SaleRebatePolicyRegisterVo> findForRebateElement(String saleRebateType) {
        Collection<Class<? extends SaleRebatePolicyElementRegister>> classes = this
                .findTemplateCollection(saleRebateType);
        List<SaleRebatePolicyRegisterVo> list = new ArrayList<>();
        for (Class<? extends SaleRebatePolicyElementRegister> aClass : classes) {
            SaleRebatePolicyElementRegister saleRebatePolicyElementRegister = applicationContext
                    .getBean(aClass);
            SaleRebatePolicyRegisterVo saleRebatePolicyRegisterVo = new SaleRebatePolicyRegisterVo();
            saleRebatePolicyRegisterVo
                    .setCode(saleRebatePolicyElementRegister.getSaleRebatePolicyElementCode());
            saleRebatePolicyRegisterVo
                    .setName(saleRebatePolicyElementRegister.getSaleRebatePolicyElementName());
            saleRebatePolicyRegisterVo.setIndexCode(saleRebatePolicyElementRegister.getElementSort());
            list.add(saleRebatePolicyRegisterVo);
        }
        this.sortList(list);
        return list;
    }

    @Override
    public List<SaleRebatePolicyRegisterVo> findForRebateScope(String saleRebateType) {
        Collection<Class<? extends SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo>>> classes = this
                .findScopeCollection(saleRebateType);
        List<SaleRebatePolicyRegisterVo> list = new ArrayList<>();
        for (Class<? extends SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo>> aClass : classes) {
            SaleRebateCustomerScopeStrategy saleRebatePolicyElementRegister = applicationContext
                    .getBean(aClass);
            SaleRebatePolicyRegisterVo saleRebatePolicyRegisterVo = new SaleRebatePolicyRegisterVo();
            saleRebatePolicyRegisterVo
                    .setCode(saleRebatePolicyElementRegister.getScopeType());
            saleRebatePolicyRegisterVo
                    .setName(saleRebatePolicyElementRegister.getScopeTypeDesc());
            saleRebatePolicyRegisterVo.setIndexCode(saleRebatePolicyElementRegister.getSort());
            list.add(saleRebatePolicyRegisterVo);
        }
        this.sortList(list);
        return list;
    }

    /**
     * 获取模板对应要素
     *
     * @param saleRebateType
     */
    @Override
    public Collection<Class<? extends SaleRebatePolicyElementRegister>> findTemplateCollection(
            String saleRebateType) {
        //Validate.notBlank(saleRebateType, "返利类型不能为空");
        //Validate.isTrue(
        //    !org.springframework.util.CollectionUtils.isEmpty(saleRebatePolicyTemplateRegisters),
        //    "无可用的返利要素注册信息");
        //List<SaleRebatePolicyTemplateRegister> collect = saleRebatePolicyTemplateRegisters.stream()
        //    .filter(saleRebatePolicyTemplateRegister -> saleRebatePolicyTemplateRegister
        //        .getSaleRebatePolicytemplateCode().equals(saleRebateType)).collect(
        //        Collectors.toList());
        //SaleRebatePolicyTemplateRegister saleRebatePolicyTemplateRegister = collect.get(0);
        //Collection<Class<? extends SaleRebatePolicyElementRegister>> saleRebateElementClasses = saleRebatePolicyTemplateRegister
        //    .getSaleRebateElementClasses();
        //return saleRebateElementClasses;

        return Sets.newHashSet(CheckProductSaleRebatePolicyElementRegisterImpl.class,
                FormulaSaleRebatePolicyElementRegisterImpl.class
                , ScopeSaleRebatePolicyElementRegisterImpl.class);
    }

    /**
     * 获取模板对应客户范围
     *
     * @param saleRebateType
     */
    private Collection<Class<? extends SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo>>> findScopeCollection(
            String saleRebateType) {
        Validate.notBlank(saleRebateType, "返利类型不能为空");
        Validate.isTrue(
                !org.springframework.util.CollectionUtils.isEmpty(saleRebatePolicyTemplateRegisters),
                "无可用的返利要素注册信息");
        List<SaleRebatePolicyTemplateRegister> collect = saleRebatePolicyTemplateRegisters.stream()
                .filter(saleRebatePolicyTemplateRegister -> saleRebatePolicyTemplateRegister
                        .getSaleRebatePolicytemplateCode().equals(saleRebateType)).collect(
                        Collectors.toList());
        SaleRebatePolicyTemplateRegister saleRebatePolicyTemplateRegister = collect.get(0);
        Collection<Class<? extends SaleRebateCustomerScopeStrategy<? extends AbstractSaleRebatePolicyCustomerInfo>>> saleRebateElementClasses = saleRebatePolicyTemplateRegister
                .getCustomerScopeStrategyClasses();
        return saleRebateElementClasses;
    }

    /**
     * 排序
     *
     * @param list
     * <AUTHOR>
     * @date
     */
    private void sortList(List<SaleRebatePolicyRegisterVo> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            Collections.sort(
                    list,
                    new Comparator<SaleRebatePolicyRegisterVo>() {
                        @Override
                        public int compare(SaleRebatePolicyRegisterVo o1, SaleRebatePolicyRegisterVo o2) {
                            return o1.getIndexCode().compareTo(o2.getIndexCode());
                        }
                    });
        }
    }

}
