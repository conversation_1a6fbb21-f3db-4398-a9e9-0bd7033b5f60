package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.configurable;

import com.biz.crm.tpm.business.rebate.local.enums.FormulaVariableEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * @description: 返利标准
 * @author: lifei
 * @date: 2022/4/6 22:29
 */
@Service
@Slf4j
public class SaleRebateRebateStandardRegister implements SaleRebatePolicyCriterionRegister {


    @Override
    public String getSaleRebatePolicyCriterionCode() {
        return FormulaVariableEnum.REBATE_STANDARD.getCode();
    }

    @Override
    public String getSaleRebatePolicyCriterionName() {
        return FormulaVariableEnum.REBATE_STANDARD.getName();
    }

    @Override
    public Integer getCriterionSort() {
        return FormulaVariableEnum.REBATE_STANDARD.getSort();
    }


    @Override
    public Map<String, BigDecimal> getAmountMapByCondition(FormulaCalBaseVo vo, String calType) {
        Validate.notNull(vo.getGiveNum(), "返利标准不能为空");
        List<BigDecimal> giveNumList = Lists.newArrayList();
        for (String s : vo.getGiveNum().split(",")) {
            giveNumList.add(new BigDecimal(s));
        }
        BigDecimal giveNum = giveNumList.stream().max(Comparator.naturalOrder()).get();
        String key = getSaleRebatePolicyCriterionCode() + vo.getCustomerCode();
        Map<String, BigDecimal> map = Maps.newHashMap();
        map.put(key, giveNum);
        return map;
    }
}
