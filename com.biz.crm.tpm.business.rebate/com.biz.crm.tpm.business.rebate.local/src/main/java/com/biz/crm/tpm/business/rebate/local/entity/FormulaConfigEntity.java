package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.List;

/**
 * @Description 公式配置实体
 * <AUTHOR>
 * @Date 2024/6/11 20:10
 */
@Getter
@Setter
@TableName("tpm_formula_config")
@Entity
@Table(name = "tpm_formula_config", indexes = {@Index(columnList = "formula_config_code " , unique = true)})
@org.hibernate.annotations.Table(appliesTo = "tpm_formula_config", comment = "公式配置实体")
public class FormulaConfigEntity extends TenantFlagOpEntity {

    /**
     * 公式配置编码
     */
    @Column(name = "formula_config_code" ,  nullable = false, columnDefinition = "VARCHAR(32) COMMENT '公式配置编码'")
    @ApiModelProperty("公式配置编码")
    private String formulaConfigCode;

    /**
     * 公式配置名称
     */
    @Column(name = "formula_config_name" ,  nullable = false, columnDefinition = "VARCHAR(128) COMMENT '公式配置名称'")
    @ApiModelProperty("公式配置名称")
    private String formulaConfigName;

    /**
     * 计算公式命中策略类型
     */
    @Column(name = "calculate_type" , columnDefinition = "VARCHAR(64) COMMENT '计算公式命中策略类型'")
    @ApiModelProperty("计算公式命中策略类型")
    private String calculateType;
    
    @TableField(exist = false)
    @Transient
    private List<FormulaEntity> formulaList;
}
