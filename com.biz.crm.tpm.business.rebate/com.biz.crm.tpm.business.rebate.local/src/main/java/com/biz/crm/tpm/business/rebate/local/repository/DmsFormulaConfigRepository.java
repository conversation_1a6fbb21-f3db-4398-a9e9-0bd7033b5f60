package com.biz.crm.tpm.business.rebate.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaConfigEntity;
import com.biz.crm.tpm.business.rebate.local.mapper.TpmFormulaConfigMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Description 公式配置
 * <AUTHOR>
 * @Date 2024/6/11 20:15
 */
@Component
public class DmsFormulaConfigRepository extends ServiceImpl<TpmFormulaConfigMapper, FormulaConfigEntity> {
    public void updateDelFlagByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        this.lambdaUpdate()
                .in(FormulaConfigEntity::getId, ids)
                .eq(FormulaConfigEntity::getTenantCode, TenantUtils.getTenantCode())
                .set(FormulaConfigEntity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
                .update();
    }

    public List<FormulaConfigEntity> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(FormulaConfigEntity::getId, ids)
                .eq(FormulaConfigEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(FormulaConfigEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    public FormulaConfigEntity findById(String id, String formulaConfigCode) {
        if (StringUtils.isBlank(id) && StringUtils.isBlank(formulaConfigCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(id), FormulaConfigEntity::getId, id)
                .eq(ObjectUtils.isNotEmpty(formulaConfigCode), FormulaConfigEntity::getFormulaConfigCode, formulaConfigCode)
                .eq(FormulaConfigEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(FormulaConfigEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .one();
    }

    public void updateEnableStatusByIdIn(EnableStatusEnum enableStatusEnum, List<String> ids) {
        if (CollectionUtils.isEmpty(ids) || Objects.isNull(enableStatusEnum)) {
            return;
        }
        this.lambdaUpdate()
                .in(FormulaConfigEntity::getId, ids)
                .eq(FormulaConfigEntity::getTenantCode, TenantUtils.getTenantCode())
                .set(FormulaConfigEntity::getEnableStatus, enableStatusEnum.getCode())
                .update();
    }

    public Page<FormulaConfigEntity> findByConditions(Pageable pageable, FormulaConfigEntity dto) {
        return this.baseMapper.findByConditions(new Page<>(pageable.getPageNumber(), pageable.getPageSize()), dto);
    }

    public List<FormulaConfigEntity> findByFormulaConfigCodes(List<String> formulaConfigCodes) {
        if (CollectionUtils.isEmpty(formulaConfigCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(FormulaConfigEntity::getFormulaConfigCode, formulaConfigCodes)
                .eq(FormulaConfigEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(FormulaConfigEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }
}
