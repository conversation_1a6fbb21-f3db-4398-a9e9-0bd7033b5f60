package com.biz.crm.tpm.business.rebate.local.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.rebate.local.constant.SaleRebateCalculateTypeConstant;
import com.biz.crm.tpm.business.rebate.local.entity.FormulaCalculationLog;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateCalculationLog;
import com.biz.crm.tpm.business.rebate.local.service.FormulaCalculationLogService;
import com.biz.crm.tpm.business.rebate.local.service.FormulaConfigService;
import com.biz.crm.tpm.business.rebate.local.service.SaleRebateCalculationLogService;
import com.biz.crm.tpm.business.rebate.local.utils.FormulaCalculateUtil;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebateCalculateTypeRegister;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.service.FormulaCalService;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateConditionResultVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateFormulaResultVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaSaleRebatePolicyElementVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/13 18:02
 */
@Service
@Slf4j
public class FormulaCalServiceImpl implements FormulaCalService {


    @Autowired
    private FormulaConfigService formulaConfigService;

    @Autowired(required = false)
    private List<SaleRebatePolicyCriterionRegister> saleRebatePolicyCriterionRegisters;

    @Autowired(required = false)
    private List<SaleRebateCalculateTypeRegister> saleRebateCalculateTypeRegisters;

    @Autowired(required = false)
    private SaleRebateCalculationLogService saleRebateCalculationLogService;

    @Autowired
    private FormulaCalculationLogService formulaCalculationLogService;

    /**
     * 公式计算
     *
     * @param calType
     * @param formulaConfigCode
     * @param vo
     * @return
     */
    @Override
    public BigDecimal calResult(String calType, String formulaConfigCode, FormulaCalBaseVo vo) {
        Validate.notNull(calType, "计算类型不能为空");
        Validate.notNull(formulaConfigCode, "公式编码不能为空");
        Validate.notNull(vo, "公式计算参数不能为空");
        FormulaConfigVo formulaConfigVo = formulaConfigService.findById(null, formulaConfigCode);
        Validate.notNull(formulaConfigVo, String.format("查询公式%s不存在", formulaConfigCode));
        Validate.notNull(formulaConfigVo.getFormulaList(), "公式明细列表不存在");
        Set<String> conditionFormulaCodeSet = FormulaCalculateUtil.getConditionFormulaSet(formulaConfigVo.getFormulaList());
        Set<String> resultFormulaCodeSet = FormulaCalculateUtil.getResultFormulaSet(formulaConfigVo.getFormulaList());
        Validate.isTrue(CollectionUtils.isNotEmpty(conditionFormulaCodeSet), "条件公式不能为空");
        Validate.isTrue(CollectionUtils.isNotEmpty(resultFormulaCodeSet), "结果公式不能为空");
        BigDecimal amount = BigDecimal.ZERO;
        if (FormulaTypeEnum.END_CASE.getCode().equals(calType)) {
            List<String> giveNumList = Arrays.asList(vo.getGiveNum().split(","));
            List<String> conditionNumList = Arrays.asList(vo.getConditionNum().split(","));
            Integer size = giveNumList.size();
            for (int i = 0; i < size; i++) {
                String giveNum = giveNumList.get(i);
                String conditionNum = conditionNumList.get(i);
                FormulaCalBaseVo calBaseVo = JSONObject.parseObject(JSONObject.toJSONString(vo), FormulaCalBaseVo.class);
                calBaseVo.setConditionNum(conditionNum);
                calBaseVo.setGiveNum(giveNum);
                BigDecimal resultAmount = this.calAmount(calBaseVo, calType, formulaConfigVo, conditionFormulaCodeSet, resultFormulaCodeSet);
                if (amount.compareTo(resultAmount) == 1) {
                    break;
                }
                amount = resultAmount;
            }
        } else {
            amount = this.calAmount(vo, calType, formulaConfigVo, conditionFormulaCodeSet, resultFormulaCodeSet);
        }
        return amount;
    }


    private BigDecimal calAmount(FormulaCalBaseVo vo, String calType, FormulaConfigVo formulaConfigVo, Set<String> conditionFormulaCodeSet, Set<String> resultFormulaCodeSet) {
        Map<String, BigDecimal> amountMap = new HashMap<>();
        conditionFormulaCodeSet.forEach(s -> {
            List<SaleRebatePolicyCriterionRegister> list = this.saleRebatePolicyCriterionRegisters.stream().
                    filter(saleRebatePolicyCriterionRegister -> s.startsWith(saleRebatePolicyCriterionRegister.getSaleRebatePolicyCriterionCode()))
                    .collect(Collectors.toList());
            Validate.isTrue(CollectionUtils.isNotEmpty(list), "基准注册器不存在！");
            SaleRebatePolicyCriterionRegister bean = list.get(0);
            bean.checkParam(vo, calType);
            Map<String, BigDecimal> map = bean.getAmountMapByCondition(vo, calType);
            amountMap.putAll(map);
        });
        resultFormulaCodeSet.forEach(s -> {
            List<SaleRebatePolicyCriterionRegister> list = this.saleRebatePolicyCriterionRegisters.stream().
                    filter(saleRebatePolicyCriterionRegister -> s.startsWith(saleRebatePolicyCriterionRegister.getSaleRebatePolicyCriterionCode()))
                    .collect(Collectors.toList());
            Validate.isTrue(CollectionUtils.isNotEmpty(list), "基准注册器不存在！");
            SaleRebatePolicyCriterionRegister bean = list.get(0);
            FormulaCalBaseVo newVo = JSONObject.parseObject(JSONObject.toJSONString(vo), FormulaCalBaseVo.class);
            newVo.setItemCodeSet(vo.getFeeItemCodeSet());
            newVo.setProductCodeSet(vo.getFeeProductCodeSet());
            bean.checkParam(newVo, calType);
            Map<String, BigDecimal> map = bean.getAmountMapByCondition(vo, calType);
            amountMap.putAll(map);
        });
        return calculate(formulaConfigVo, vo, amountMap, calType);
    }


    private BigDecimal calculate(FormulaConfigVo formulaConfigVo, FormulaCalBaseVo vo, Map<String, BigDecimal> amountMap, String calType) {
        //根据命中策略执行
        String calculateType = formulaConfigVo.getCalculateType();
        //兼容之前的业务数据 若命中策略不存在 则默认为任意命中的策略
        if (StringUtils.isBlank(calculateType)) {
            calculateType = SaleRebateCalculateTypeConstant.RANDOM_HIT_REGISTER_CODE;
        }
        BigDecimal resultAmount = BigDecimal.ZERO;
        List<FormulaSaleRebatePolicyElementVo> formulaList = formulaConfigVo.getFormulaList();
        SaleRebateFormulaResultVo executeComputeResult = null;
        SaleRebateConditionResultVo conditionResult = null;
        for (FormulaSaleRebatePolicyElementVo elementVo : formulaList) {
            conditionResult = FormulaCalculateUtil.computeConditionExpression(elementVo.getSaleRebatePolicyCondition(), elementVo.getSaleRebatePolicyConditionName(), amountMap, vo.getCustomerCode());
            boolean flag = conditionResult.isValue();
            if (flag) {
                //满足返利条件成立时执行
                SaleRebateCalculateTypeRegister saleRebateCalculateTypeRegister = this.findCalculateType(calculateType, saleRebateCalculateTypeRegisters);
                String calculateTypeRegisterCode = saleRebateCalculateTypeRegister.getCalculateTypeRegisterCode();
                //如果是任意命中的情况 并且已经有结果金额 则结束当前公式循环
                if (calculateTypeRegisterCode.equals(SaleRebateCalculateTypeConstant.RANDOM_HIT_REGISTER_CODE) && resultAmount.compareTo(BigDecimal.ZERO) == 1) {
                    break;
                }
                executeComputeResult = FormulaCalculateUtil.computeRebateExpression(elementVo.getSaleRebatePolicyFormula(), elementVo.getSaleRebatePolicyFormulaName(), amountMap, vo.getCustomerCode());
                resultAmount = executeComputeResult.getValue();

            }
        }
        SaleRebateCalculationLog saleRebateCalculationLog = FormulaCalculateUtil.buildComputeLog(vo, conditionResult, executeComputeResult);
        FormulaCalculationLog formulaCalculationLog = FormulaCalculateUtil.buildFormulaLog(vo, formulaConfigVo, calType, conditionResult, executeComputeResult);
        this.saleRebateCalculationLogService.create(saleRebateCalculationLog);
        formulaCalculationLogService.saveBatchList(Lists.newArrayList(formulaCalculationLog));
        return resultAmount;
    }


    /**
     * 查询返利计算的命中策略
     *
     * @param saleRebateCalculateTypeRegisters
     * @return
     */
    private SaleRebateCalculateTypeRegister findCalculateType(String code, List<SaleRebateCalculateTypeRegister> saleRebateCalculateTypeRegisters) {
        if (CollectionUtils.isEmpty(saleRebateCalculateTypeRegisters)) {
            return null;
        }
        return saleRebateCalculateTypeRegisters.stream().filter(saleRebateCalculateTypeRegister -> saleRebateCalculateTypeRegister.getCalculateTypeRegisterCode().equals(code)).findFirst().orElse(null);
    }
}
