//package com.biz.crm.tpm.business.rebate.local.strategy.criterion;
//
//import com.biz.crm.common.form.sdk.model.DynamicFormsOperationStrategy;
//import com.biz.crm.tpm.business.rebate.local.entity.criterion.SaleVolumeCriterion;
//import com.biz.crm.tpm.business.rebate.local.model.SaleVolumeCriterionVo;
//import com.biz.crm.tpm.business.rebate.local.repository.SaleVolumeCriterionRepository;
//import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.configurable.RebateCriterionModuleRegister;
//import com.biz.crm.tpm.business.rebate.local.service.register.criterionregister.configurable.SaleVolumeCriterionImpl;
//import com.biz.crm.tpm.business.rebate.local.utils.SaleRebateCycleUtil;
//import com.bizunited.nebula.common.service.NebulaToolkitService;
//import com.google.common.collect.Lists;
//import java.util.Objects;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.ArrayList;
//import java.util.Collection;
//import java.util.HashMap;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// */
//public class SaleVolumeCriterionVoOperationStrategy implements DynamicFormsOperationStrategy<SaleVolumeCriterionVo> {
//
//  @Autowired(required = false)
//  private SaleVolumeCriterionRepository saleVolumeCriterionRepository;
//
//  @Autowired(required = false)
//  private NebulaToolkitService nebulaToolkitService;
//
//
//  @Override
//  public String dynamicFormCode() {
//    return SaleVolumeCriterionImpl.REBATE_POLICY_CRITERION_CODE;
//  }
//
//  @Override
//  public String dynamicFormName() {
//    return "返利变量中可配置的销售数量";
//  }
//
//  @Override
//  public Class<SaleVolumeCriterionVo> dynamicFormClass() {
//    return SaleVolumeCriterionVo.class;
//  }
//
//  @Override
//  public String moduleCode() {
//    return RebateCriterionModuleRegister.class.getName();
//  }
//
//  @Override
//  public int getOrder() {
//    return 0;
//  }
//
//  @Override
//  public void onDynamicFormsDelete(String parentCode, String dynamicKey) {
//    saleVolumeCriterionRepository.deleteBySaleRebatePolicyCode(parentCode);
//  }
//
//  @Override
//  public void onDynamicFormsDelete(String parentCode, String dynamicKey, String[] detailCodes) {
//    saleVolumeCriterionRepository.deleteBySaleRebatePolicyCodeAndInstanceCodeIn(parentCode, detailCodes);
//  }
//
//  @Override
//  public void onDynamicFormsCreate(Collection<SaleVolumeCriterionVo> dynamicForms, String dynamicKey, String parentCode, Object parent) {
//    /**
//     * 参数说明：
//     * - dynamicForm : 动态表单的一个具体表单模型 ：SaleVolumeCriterionVo
//     * - dynamicKey ：主模型（对于动态表单来说）里的那个map的key ：ConfigurableCriterionVo.criterionMap.getKey(), 此处是返利变量实现类的code，[如：CPXSSL];
//     * - parentCode ：主业务（主表）的编码 ：返利政策的code
//     * - parent ：主模型，与入参的json信息最外层对应的业务模型，动态表单会把json转成主模型 ：ConfigurableCriterionVo
//     */
//    /**
//     * 把信息保存到数据库去
//     */
//    List<SaleVolumeCriterion> list = Lists.newArrayListWithCapacity(dynamicForms.size());
//    for (SaleVolumeCriterionVo dynamicForm : dynamicForms) {
//      SaleVolumeCriterion saleVolumeCriterion = this.copyObject(parentCode, dynamicForm);
//      list.add(saleVolumeCriterion);
//    }
//    saleVolumeCriterionRepository.saveBatch(list);
//  }
//
//  @Override
//  public void onDynamicFormsModify(Collection<SaleVolumeCriterionVo> dynamicForms, String dynamicKey, String parentCode, Object parent) {
//    /**
//     * 新增的
//     * - 没有id
//     * 修改的
//     * - 有id，有内容
//     * 删除的
//     * - 有id，没有内容
//     */
//    List<String> instanceCodes = dynamicForms.stream()
//        .map(SaleVolumeCriterionVo::getInstanceCode)
//        .collect(Collectors.toList());
//    List<SaleVolumeCriterion> idAndInstanceList = saleVolumeCriterionRepository.findIdAndInstanceByPolicyAndInstanceIn(parentCode, instanceCodes);
//
//    Map<String, String> instanceToIdMap = new HashMap<>(idAndInstanceList.size());
//    for (SaleVolumeCriterion criterion : idAndInstanceList) {
//      instanceToIdMap.put(criterion.getInstanceCode(), criterion.getId());
//    }
//
//    List<SaleVolumeCriterion> list = Lists.newArrayListWithCapacity(dynamicForms.size());
//    for (SaleVolumeCriterionVo dynamicForm : dynamicForms) {
//      SaleVolumeCriterion saleVolumeCriterion = this.copyObject(parentCode, dynamicForm);
//      saleVolumeCriterion.setId(instanceToIdMap.get(dynamicForm.getInstanceCode()));
//      list.add(saleVolumeCriterion);
//    }
//    saleVolumeCriterionRepository.saveOrUpdateBatch(list);
//
//  }
//
//  /**
//   * 复制对象
//   *
//   * @param parentCode  父代码
//   * @param dynamicForm 动态表单
//   * @return {@link SaleVolumeCriterion}
//   */
//  private SaleVolumeCriterion copyObject(String parentCode, SaleVolumeCriterionVo dynamicForm) {
//    SaleVolumeCriterion saleVolumeCriterion = nebulaToolkitService.copyObjectByBlankList(dynamicForm, SaleVolumeCriterion.class, HashSet.class, ArrayList.class, "productCodes", "productLevels");
//    saleVolumeCriterion.setSaleRebatePolicyCode(parentCode);
//    List<String> productCodes = dynamicForm.getProductCodes();
//    String productCodeStr = StringUtils.join(productCodes, ",");
//    saleVolumeCriterion.setProductCodes(productCodeStr);
//    List<String> productLevels = dynamicForm.getProductLevels();
//    String productLevelStr = StringUtils.join(productLevels, ",");
//    saleVolumeCriterion.setProductLevels(productLevelStr);
//    if(Objects.nonNull(saleVolumeCriterion.getRightTime())){
//      saleVolumeCriterion.setRightTime(SaleRebateCycleUtil.getLastday(saleVolumeCriterion.getRightTime()));
//    }
//    return saleVolumeCriterion;
//  }
//
//  @Override
//  public Collection<SaleVolumeCriterionVo> findByParentCode(String dynamicKey, String parentCode) {
//    /**
//     * 返回保存的信息
//     */
//    List<SaleVolumeCriterion> saleVolumeCriterionList = saleVolumeCriterionRepository.findBySaleRebatePolicyCode(parentCode);
//    List<SaleVolumeCriterionVo> list = Lists.newArrayListWithCapacity(saleVolumeCriterionList.size());
//    for (SaleVolumeCriterion saleVolumeCriterion : saleVolumeCriterionList) {
//      SaleVolumeCriterionVo saleVolumeCriterionVo = nebulaToolkitService.copyObjectByBlankList(saleVolumeCriterion, SaleVolumeCriterionVo.class, HashSet.class, ArrayList.class, "productCodes", "productLevels");
//      saleVolumeCriterionVo.setParentCode(parentCode);
//      saleVolumeCriterionVo.setDynamicKey(dynamicKey);
//      String productCodes = saleVolumeCriterion.getProductCodes();
//      if (StringUtils.isNotBlank(productCodes)) {
//        String[] split = productCodes.split(",");
//        List<String> strings = Lists.newArrayList(split);
//        saleVolumeCriterionVo.setProductCodes(strings);
//      }
//      String productLevels = saleVolumeCriterion.getProductLevels();
//      if (StringUtils.isNotBlank(productLevels)) {
//        String[] split = productLevels.split(",");
//        List<String> strings = Lists.newArrayList(split);
//        saleVolumeCriterionVo.setProductLevels(strings);
//      }
//      list.add(saleVolumeCriterionVo);
//    }
//    return list;
//  }
//
//
//}
