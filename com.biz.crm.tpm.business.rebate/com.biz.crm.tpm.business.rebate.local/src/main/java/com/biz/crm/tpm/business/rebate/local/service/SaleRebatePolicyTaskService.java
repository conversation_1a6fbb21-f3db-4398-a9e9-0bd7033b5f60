package com.biz.crm.tpm.business.rebate.local.service;

import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebateCronDto;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateCronVo;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * @description: 返利定时任务
 * @author: lifei
 * @date: 2022/2/22 11:14
 */
public interface SaleRebatePolicyTaskService {

  /**
   * 返利定时任务
   * @param saleRebatePolicyCode
   */
  void handleRebateTask(String saleRebatePolicyCode);

  /**
   *
   * 创建定时任务
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  void createRebatetask(SaleRebatePolicyDto saleRebatePolicyDto);

  /**
   *
   * 创建定时任务（一次性）
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  void createRebateSingletask(SaleRebatePolicyDto saleRebatePolicyDto);

  /**
   *
   * 编辑定时任务
   * @param saleRebatePolicyDto
   * <AUTHOR>
   * @date
   */
  void updateRebatetask(SaleRebatePolicyDto saleRebatePolicyDto);

  /**
   * 根据无效化计算定时任务
   *
   * @param templateCodes
   */
  void invalidBatchBySaleRebatePolicyCodes(List<String> templateCodes);

  /**
   * 根据有效化计算定时任务
   *
   * @param templateCodes
   */
  void effectiveBatchBySaleRebatePolicyCodes(List<String> templateCodes);

  /**
   * 根据删除计算定时任务
   *
   * @param templateCodes
   */
  void deleteBatchBySaleRebatePolicyCodes(List<String> templateCodes);

  /**
   * 预测未来即将返利的周期时间表
   * 默认展示条数最大值:20
   * @param dto
   * @return
   */
  List<SaleRebateCronVo> predictionSaleRebateTimes(SaleRebateCronDto dto) throws ParseException;


}
