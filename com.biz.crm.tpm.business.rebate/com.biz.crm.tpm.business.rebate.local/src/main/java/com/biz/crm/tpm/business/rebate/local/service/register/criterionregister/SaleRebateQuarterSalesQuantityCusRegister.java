package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.tpm.business.rebate.local.enums.FormulaVariableEnum;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCriterionRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Map;

/**
 * @description: 季度销售数量(客户)
 * @author: lifei
 * @date: 2022/4/6 22:29
 */
@Service
@Slf4j
public class SaleRebateQuarterSalesQuantityCusRegister implements SaleRebatePolicyCriterionRegister {

    @Resource
    private FormulaGetValueComponent formulaGetValueComponent;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public String getSaleRebatePolicyCriterionCode() {
        return FormulaVariableEnum.KHJDXSSL.getCode();
    }

    @Override
    public String getSaleRebatePolicyCriterionName() {
        return FormulaVariableEnum.KHJDXSSL.getName();
    }

    @Override
    public Integer getCriterionSort() {
        return FormulaVariableEnum.KHJDXSSL.getSort();
    }

    /**
     * @param vo      计算基准公式
     * @param calType 计算类型
     * @return
     */
    @Override
    public Map<String, BigDecimal> getAmountMapByCondition(FormulaCalBaseVo vo, String calType) {
        Map<String, BigDecimal> map = Maps.newHashMap();
        String key = getSaleRebatePolicyCriterionCode() + vo.getCustomerCode();
        SalesPlanQueryVo queryVo = JSONObject.parseObject(JSONObject.toJSONString(vo),SalesPlanQueryVo.class);
        //时间转换 找到对应当前年月的季度
        Map<String, String> dateMap = FormulaGetValueComponent.getDateQuarter(vo.getStartDate(), DateUtil.DEFAULT_YEAR_MONTH_DAY);
        queryVo.setStartDate(dateMap.get(FormulaGetValueComponent.START_DATE));
        queryVo.setEndDate(dateMap.get(FormulaGetValueComponent.END_DATE));

        BigDecimal quantity = formulaGetValueComponent.signForAmountOrQuantity(queryVo, FormulaGetValueComponent.QUANTITY);
        map.put(key, quantity);
        return map;
    }


}
