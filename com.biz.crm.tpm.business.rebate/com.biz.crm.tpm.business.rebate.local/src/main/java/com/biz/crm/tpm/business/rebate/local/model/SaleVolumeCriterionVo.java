package com.biz.crm.tpm.business.rebate.local.model;

import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.field.annotation.Validate;
import com.biz.crm.common.form.sdk.field.validate.NotBlankValidateStrategy;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.SimpleDateSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.rebate.local.widget.ProductLevelNavigationBarQueryWidget;
import com.biz.crm.tpm.business.rebate.local.widget.ProductSkuNavigationBarQueryWidget;
import com.biz.crm.tpm.business.rebate.local.widget.SimpleCheckboxWidget;
import com.biz.crm.tpm.business.rebate.local.widget.SimpleSelectWidget;
import com.biz.crm.tpm.business.rebate.sdk.vo.dynamic.AbstractCriterionVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 返利变量表单
 *
 * <AUTHOR>
 * @date 2022/06/20
 */
@Data
public class SaleVolumeCriterionVo extends AbstractCriterionVo implements DynamicForm {

  /**
   * 主表的编码
   */
  private String parentCode;

  /**
   * 业务Key值
   */
  private String dynamicKey;

  /**
   * 返利变量说明
   */
  @DynamicField(fieldName = "返利变量说明", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String description;

  /**
   * 时间类型: 自定义，上个月，上个季度，上一年
   */
  @DynamicField(fieldName = "时间类型", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleSelectWidget.class)
  private String timeType;

  /**
   * 时间左
   */
  @DynamicField(fieldName = "时间左", controllKey = SimpleDateSelectWidget.class, required = false)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date leftTime;

  /**
   * 时间右
   */
  @DynamicField(fieldName = "时间右", controllKey = SimpleDateSelectWidget.class, required = false)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date rightTime;

  /**
   * 默认适配考核产品
   */
  @DynamicField(fieldName = "默认适配考核产品", controllKey = SimpleCheckboxWidget.class)
  private Boolean defaultProduct;

  /**
   * 商品集合
   */
  @DynamicField(fieldName = "商品集合", controllKey = ProductSkuNavigationBarQueryWidget.class, required = false)
  private List<String> productCodes;

  /**
   * 产品层级集合
   */
  @DynamicField(fieldName = "产品层级集合", controllKey = ProductLevelNavigationBarQueryWidget.class, required = false)
  private List<String> productLevels;
}
