package com.biz.crm.tpm.business.rebate.local.widget;

import com.biz.crm.common.form.sdk.widget.WidgetKey;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 产品sku的导航栏风格的查询用途的小部件
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Component
public class ProductSkuNavigationBarQueryWidget implements WidgetKey {
  @Override
  public String widgetCode() {
    return "productSkuNavigationBarQueryWidget";
  }

  @Override
  public String widgetName() {
    return "产品sku的导航栏风格的查询用途的小部件";
  }

  @Override
  public Map<String, Object> widgetParam() {
    return null;
  }
}
