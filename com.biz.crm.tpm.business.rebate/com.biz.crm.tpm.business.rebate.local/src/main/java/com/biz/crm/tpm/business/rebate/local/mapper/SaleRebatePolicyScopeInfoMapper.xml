<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyScopeInfoMapper">

  <resultMap type="com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyScopeInfo" id="SaleRebatePolicyScopeInfoMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="code" column="code" jdbcType="VARCHAR"/>
    <result property="customerScopeType" column="customer_scope_type" jdbcType="VARCHAR"/>
    <result property="name" column="name" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyCode" column="sale_rebate_policy_code" jdbcType="VARCHAR"/>
  </resultMap>
  
  <sql id = "saleRebatePolicyScopeInfo">
    tenant_code tenantCode,
    code code,
    customer_scope_type customerScopeType,
    name name,
    sale_rebate_policy_code saleRebatePolicyCode,
    id id
  </sql>

  <select id="findByConditions" resultMap="SaleRebatePolicyScopeInfoMap">
    select
      <include refid="saleRebatePolicyScopeInfo"/>
    from tpm_sale_rebate_policy_scope_info
    where
    tenant_code=#{saleRebatePolicyScopeInfo.tenantCode}
</select>
</mapper>

