package com.biz.crm.tpm.business.rebate.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebateAdjustDetail;
import com.biz.crm.tpm.business.rebate.local.mapper.SaleRebateAdjustDetailMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

/**
 * @description: 客户资金调整明细
 * @author: lifei
 * @date: 2022/4/11 14:45
 */
@Component
public class SaleRebateAdjustDetailRepository extends
    ServiceImpl<SaleRebateAdjustDetailMapper, SaleRebateAdjustDetail> {
  /**
   * 分页查询数据
   *
   * @param pageable               分页对象
   * @param saleRebateAdjustDetail 实体对象
   * @return
   */
  public Page<SaleRebateAdjustDetail> findByConditions(Pageable pageable,
      SaleRebateAdjustDetail saleRebateAdjustDetail) {
    Page<SaleRebateAdjustDetail> page = new Page<>(pageable.getPageNumber(),
        pageable.getPageSize());
    saleRebateAdjustDetail.setTenantCode(TenantUtils.getTenantCode());
    Page<SaleRebateAdjustDetail> pageList = this.baseMapper
        .findByConditions(page, saleRebateAdjustDetail);
    return pageList;
  }

  /**
   * 查询数据
   *
   * @param saleRebateDetailCode 明细编码
   * @return
   */
  public List<SaleRebateAdjustDetail> findBySaleRebateDetailCode(String saleRebateDetailCode){
    return this.lambdaQuery()
        .eq(SaleRebateAdjustDetail::getSaleRebateDetailCode, saleRebateDetailCode)
        .eq(SaleRebateAdjustDetail::getTenantCode, TenantUtils.getTenantCode()).list();
  }

}
