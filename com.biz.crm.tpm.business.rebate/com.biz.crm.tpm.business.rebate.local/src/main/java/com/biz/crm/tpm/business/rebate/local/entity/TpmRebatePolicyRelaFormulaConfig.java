package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidOpEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description 返利政策关联公式配置
 * <AUTHOR>
 * @Date 2024/6/11 17:04
 */
@Getter
@Setter
@TableName("tpm_rebate_policy_rela_formula_config")
@Entity
@Table(name = "tpm_rebate_policy_rela_formula_config", indexes = {
        @Index(columnList = "sale_rebate_policy_code, formula_type"),
        @Index(columnList = "unique_key", unique = true)
})
@org.hibernate.annotations.Table(appliesTo = "tpm_rebate_policy_rela_formula_config", comment = "返利政策关联公式配置")
public class TpmRebatePolicyRelaFormulaConfig extends UuidOpEntity {

    @Column(name = "sale_rebate_policy_code", nullable = false, columnDefinition = "VARCHAR(32) COMMENT '返利政策编码'")
    @ApiModelProperty("返利政策编码")
    private String saleRebatePolicyCode;
    
    @Column(name = "formula_config_code", nullable = false, columnDefinition = "VARCHAR(32) COMMENT '公式配置编码'")
    @ApiModelProperty("公式配置编码")
    private String formulaConfigCode;

    /**
     * @see com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum
     * 公式类型
     */
    @Column(name = "formula_type", nullable = false, columnDefinition = "VARCHAR(32) COMMENT '公式类型'")
    @ApiModelProperty("公式类型,预提:withholding,返利:rebate")
    private String formulaType;

    /**
     * md5(sale_rebate_policy_code+formula_config_code+formula_type)
     */
    @Column(name = "unique_key", nullable = false, columnDefinition = "VARCHAR(32) COMMENT '唯一编码'")
    @ApiModelProperty("唯一编码")
    private String uniqueKey;
}
