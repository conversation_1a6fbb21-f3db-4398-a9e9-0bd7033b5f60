package com.biz.crm.tpm.business.rebate.local.service;


import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyProductInfo;

import java.util.List;


/**
 * 返利政策分配商品(SaleRebatePolicyProductInfo)service
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
public interface SaleRebatePolicyProductInfoService {

  /**
   * 批量删除
   *
   * @param idList
   * @return
   */
  void delete(List<String> idList);

  /**
   * 返利编码集合删除
   *
   * @param saleRebatePolicyCode id集合
   * @return Result
   */
  void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode);

  /**
   * 通过政策编码查询
   *
   * @param saleRebatePolicyCode
   */
  List<SaleRebatePolicyProductInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode);

  /**
   * 通过政策编码查询
   *
   * @param saleRebatePolicyCodes
   */
  List<SaleRebatePolicyProductInfo> findBySaleRebatePolicyCodes(List<String> saleRebatePolicyCodes);

  /**
   * 批量新增
   *
   * @param list
   */
  void createBatch(List<SaleRebatePolicyProductInfo> list);

}

