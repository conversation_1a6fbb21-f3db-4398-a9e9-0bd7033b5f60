package com.biz.crm.tpm.business.rebate.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyFormulaInfo;
import com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyFormulaInfoMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * 返利政策公式(SaleRebatePolicyFormulaInfo)
 *
 * <AUTHOR>
 * @since 2022-02-16 17:55:57
 */
@Component
public class SaleRebatePolicyFormulaInfoRepository extends
    ServiceImpl<SaleRebatePolicyFormulaInfoMapper, SaleRebatePolicyFormulaInfo> {

  /**
   * 分页查询
   *
   * @param pageable                    分页
   * @param saleRebatePolicyFormulaInfo
   * @return
   */
  public Page<SaleRebatePolicyFormulaInfo> findByConditions(Pageable pageable,
      SaleRebatePolicyFormulaInfo saleRebatePolicyFormulaInfo) {
    Page<SaleRebatePolicyFormulaInfo> page = new Page<>(pageable.getPageNumber(),
        pageable.getPageSize());
    //新增租户编号判断
    saleRebatePolicyFormulaInfo.setTenantCode(TenantUtils.getTenantCode());
    Page<SaleRebatePolicyFormulaInfo> pageList = this.baseMapper
        .findByConditions(page, saleRebatePolicyFormulaInfo);
    return pageList;
  }

  /**
   * 根据编码查询
   *
   * @param saleRebatePolicyCode
   * @return
   */
  public List<SaleRebatePolicyFormulaInfo> findBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyFormulaInfo::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .eq(SaleRebatePolicyFormulaInfo::getTenantCode, TenantUtils.getTenantCode())
        .list();
  }

  /**
   * 根据编码查询
   *
   * @param saleRebatePolicyCodes
   * @return
   */
  public List<SaleRebatePolicyFormulaInfo> findBySaleRebatePolicyCodes(
      List<String> saleRebatePolicyCodes, String formulaType) {
    return this.lambdaQuery()
        .in(SaleRebatePolicyFormulaInfo::getSaleRebatePolicyCode, saleRebatePolicyCodes)
        .eq(SaleRebatePolicyFormulaInfo::getTenantCode, TenantUtils.getTenantCode())
        .eq(SaleRebatePolicyFormulaInfo::getFormulaType, formulaType)
        .list();
  }


  /**
   * 删除
   *
   * @param saleRebatePolicyCode
   * @return
   */
  public void deleteBySaleRebatePolicyCode(String saleRebatePolicyCode) {
    this.lambdaUpdate()
        .eq(SaleRebatePolicyFormulaInfo::getTenantCode, TenantUtils.getTenantCode())
        .eq(SaleRebatePolicyFormulaInfo::getSaleRebatePolicyCode, saleRebatePolicyCode)
        .remove();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(SaleRebatePolicyFormulaInfo::getTenantCode,tenantCode)
        .in(SaleRebatePolicyFormulaInfo::getId,ids)
        .remove();
  }

  public List<SaleRebatePolicyFormulaInfo> listByIdsAndTenantCode(List<String> ids, String tenantCode) {
    return this.lambdaQuery()
        .eq(SaleRebatePolicyFormulaInfo::getTenantCode,TenantUtils.getTenantCode())
        .in(SaleRebatePolicyFormulaInfo::getId,ids)
        .list();
  }

  public List<SaleRebatePolicyFormulaInfo> findByFormulaConfigCodes(List<String> formulaConfigCodes) {
    if (CollectionUtils.isEmpty(formulaConfigCodes)) {
      return Lists.newArrayList();
    }
    return this.lambdaQuery()
            .in(SaleRebatePolicyFormulaInfo::getFormulaConfigCode, formulaConfigCodes)
            .eq(SaleRebatePolicyFormulaInfo::getTenantCode, TenantUtils.getTenantCode())
            .list();
  }
}

