package com.biz.crm.tpm.business.rebate.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyDetail;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 返利政策明细，按照租户进行隔离(SaleRebatePolicyDetail)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-02-25 14:30:52
 */
public interface SaleRebatePolicyDetailMapper extends
    BaseMapper<SaleRebatePolicyDetail> {

  /**
   * 分页查询所有数据
   *
   * @param page                   分页对象
   * @param saleRebatePolicyDetail 查询实体
   * @return 所有数据
   */
  Page<SaleRebatePolicyDetail> findByConditions(@Param("page") Page<SaleRebatePolicyDetail> page,
      @Param("dto") SaleRebatePolicyDetail saleRebatePolicyDetail);

  /**
   * 分页查询所有数据
   *
   * @param saleRebatePolicyDetail 查询实体
   * @return 所有数据
   */
  List<SaleRebatePolicyDetail> findByConditionsList(
      @Param("dto") SaleRebatePolicyDetail saleRebatePolicyDetail);
}

