package com.biz.crm.tpm.business.rebate.local.service.register.criterionregister;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.dms.business.warehouse.sdk.dto.TpmWarehouseDetailSearchDto;
import com.biz.crm.dms.business.warehouse.sdk.enums.DmsWarehouseOrderEnum;
import com.biz.crm.dms.business.warehouse.sdk.service.DmsWarehouseOrderDetailVoService;
import com.biz.crm.dms.business.warehouse.sdk.vo.DmsWarehouseOrderDetailVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.dto.ProductQueryDto;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.pay.sdk.service.PosDataService;
import com.biz.crm.tpm.business.pay.sdk.vo.PosDataVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/14 03:25
 */
@Component
@Slf4j
public class FormulaGetValueComponent {

    public static final String AMOUNT = "amount";

    public static final String QUANTITY = "quantity";

    /**
     * 客户拆分
     */
    public static final String CUSTOMER_SPLITTING = "customer_splitting";

    public static final String LAST_MONTH = "last_month";

    public static final String CURRENT_MONTH = "current_month";

    @Autowired(required = false)
    private MarketingSalesPlanService marketingSalesPlanService;

    @Autowired(required = false)
    private DmsWarehouseOrderDetailVoService dmsWarehouseOrderDetailVoService;

    @Autowired(required = false)
    private ProductVoService productVoService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    public PosDataService posDataService;

    /**
     * 查询销售计划的数据
     *
     * @param vo
     * @param dataType
     * @return
     */
    public BigDecimal salesPlanCalAmountOrQuantity(SalesPlanQueryVo vo, String dataType) {
        Map<String, List<OrgVo>> orgVoListMap = orgVoService.findAllChildrenByOrgCodesMap(Lists.newArrayList(vo.getOrgCode()));
        Map<String, Set<String>> orgCostCenterMap = Maps.newHashMap();
        for (Map.Entry<String, List<OrgVo>> entry : orgVoListMap.entrySet()) {
            Set<String> costCenterCodeSet = Sets.newHashSet();
            for (OrgVo orgVo : entry.getValue()) {
                if (CollectionUtils.isNotEmpty(orgVo.getCostCenterCodeSet())) {
                    costCenterCodeSet.addAll(orgVo.getCostCenterCodeSet());
                }
            }
            orgCostCenterMap.put(entry.getKey(), costCenterCodeSet);
        }
        Set<String> costCenterCodeSet = orgCostCenterMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        vo.setCostCenterCodeSet(costCenterCodeSet);
        Set<String> productCodeSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(vo.getProductCodeSet())) {
            productCodeSet.addAll(vo.getProductCodeSet());
        }
        if (CollectionUtils.isNotEmpty(vo.getItemCodeSet())) {
            Map<String, ProductVo> productVoMap = findProductListByItemCodes(vo.getItemCodeSet());
            productCodeSet.addAll(productVoMap.keySet());
        }
        vo.setProductCodeSet(productCodeSet);
        vo.setItemCodeSet(null);
        List<MarketingSalesPlanVo> salesPlanVos = Lists.newArrayList();
        String saleCachekey = vo.getCacheKey() + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + vo.getYears();
        List<MarketingSalesPlanVo> salesPlanList1 = marketingSalesPlanService.findListBySalesPlanQueryVo(vo);
        List<MarketingSalesPlanVo> salesPlanList2 = marketingSalesPlanService.findCacheList(saleCachekey);
        if (CollectionUtils.isNotEmpty(salesPlanList1)) {
            salesPlanVos.addAll(salesPlanList1);
        }
        String startYears = LocalDate.parse(vo.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        if (CollectionUtils.isNotEmpty(salesPlanList2)) {
            salesPlanList2 = salesPlanList2.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTaxEstimatedCost())
                            && ObjectUtils.isNotEmpty(x.getCostCenterCode())
                            && ObjectUtils.isNotEmpty(x.getProductCode()) && productCodeSet.contains(x.getProductCode())
                            && costCenterCodeSet.contains(x.getCostCenterCode())
                            && startYears.equals(x.getYears()) && vo.getCustomerCode().equals(x.getCustomerCode()))
                    .collect(Collectors.toList());
            salesPlanVos.addAll(salesPlanList2);
        }
        BigDecimal data = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(salesPlanVos)) return data;
        if (AMOUNT.equals(dataType)) {
            data = salesPlanVos.stream()
                    .filter(k -> Objects.nonNull(k.getTaxEstimatedCost()))
                    .map(MarketingSalesPlanVo::getTaxEstimatedCost)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (QUANTITY.equals(dataType)) {
            data = salesPlanVos.stream()
                    .filter(k -> Objects.nonNull(k.getEstimatedSalesVolume()))
                    .map(x -> x.getEstimatedSalesVolume().multiply(ObjectUtils.defaultIfNull(x.getConversionValue(), BigDecimal.ONE)))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return data;
    }

    /**
     * 获取签收数据
     *
     * @param vo
     * @param dataType
     * @return
     */
    public BigDecimal signForAmountOrQuantity(SalesPlanQueryVo vo, String dataType) {
        List<String> productCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(vo.getProductCodeSet())) {
            productCodes.addAll(vo.getProductCodeSet());
        }
        if (CollectionUtils.isNotEmpty(vo.getItemCodeSet())) {
            Set<String> productCodeSet = Sets.newHashSet();
            Map<String, ProductVo> productVoMap = findProductListByItemCodes(vo.getItemCodeSet());
            productCodeSet.addAll(productVoMap.keySet());
            productCodes.addAll(productCodeSet);
        }
        TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
        dto.setCustomerCode(vo.getCustomerCode());
        //产品没有不用查了
        if(CollectionUtils.isEmpty(productCodes)){
            return BigDecimal.ZERO;
        }
        dto.setProductCodes(productCodes);
        dto.setSignSearchStartTime(vo.getStartDate());
        dto.setSignSearchEndTime(vo.getEndDate());
        dto.setItemTypeList(Lists.newArrayList("normalGoods"));
//        dto.setSignStatus(DmsWarehouseOrderEnum.SIGN_STATUS.SIGN.getCode());
        List<DmsWarehouseOrderDetailVo> orderDetailVos = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
        if (CollectionUtils.isEmpty(orderDetailVos)) {
            return BigDecimal.ZERO;
        }
        List<String> productCodeList = orderDetailVos.stream().map(x -> x.getGoodsCode()).distinct().collect(Collectors.toList());
        List<ProductVo> productVos = productVoService.findDetailsByIdsOrProductCodes(null, productCodeList);
        for (ProductVo productVo : productVos) {
            if (ObjectUtils.isEmpty(productVo.getConversionValue())) {
                productVo.setConversionValue("1");
            }
        }
        Map<String, BigDecimal> productConversionMap = productVos.stream().collect(Collectors.toMap(ProductVo::getMaterialCode, x -> new BigDecimal(x.getConversionValue())));
        BigDecimal data = BigDecimal.ZERO;
        if (QUANTITY.equals(dataType)) {
            BigDecimal deliveryData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getSignQuantity()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(x -> x.getSignQuantity().multiply(productConversionMap.getOrDefault(x.getGoodsCode(), BigDecimal.ONE)))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal returnData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getRealOutNum()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(x -> x.getRealOutNum().multiply(productConversionMap.getOrDefault(x.getGoodsCode(), BigDecimal.ONE)))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.error("查询dms的计算结果值，签收数量：{}，退货数量:{}", deliveryData, returnData);
            data = deliveryData.subtract(returnData);

        } else if (AMOUNT.equals(dataType)) {
            BigDecimal deliveryData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getSignAmount()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(DmsWarehouseOrderDetailVo::getSignAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal returnData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(DmsWarehouseOrderDetailVo::getTotalAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.error("查询dms的计算结果值，签收金额：{}，退货金额:{}", deliveryData, returnData);
            data = deliveryData.subtract(returnData);
        }
        return data;
    }


    /**
     * 获取收货数据
     *
     * @param vo
     * @param dataType
     * @return
     */
    public BigDecimal deliveryAmountOrQuantity(SalesPlanQueryVo vo, String dataType) {
        List<String> productCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(vo.getProductCodeSet())) {
            productCodes.addAll(vo.getProductCodeSet());
        }
        if (CollectionUtils.isNotEmpty(vo.getItemCodeSet())) {
            Set<String> productCodeSet = Sets.newHashSet();
            Map<String, ProductVo> productVoMap = findProductListByItemCodes(vo.getItemCodeSet());
            productCodeSet.addAll(productVoMap.keySet());
            productCodes.addAll(productCodeSet);
        }
        TpmWarehouseDetailSearchDto dto = new TpmWarehouseDetailSearchDto();
        dto.setCustomerCode(vo.getCustomerCode());
        dto.setProductCodes(productCodes);
        if(CollectionUtils.isEmpty(productCodes)){
            return BigDecimal.ZERO;
        }
        dto.setSearchStartTime(vo.getStartDate());
        dto.setSearchEndTime(vo.getEndDate());
        dto.setItemTypeList(Lists.newArrayList("normalGoods"));
//        dto.setSignStatus(DmsWarehouseOrderEnum.SIGN_STATUS.NO_SIGN.getCode());
        List<DmsWarehouseOrderDetailVo> orderDetailVos = dmsWarehouseOrderDetailVoService.findBySearchDto(dto);
        if (CollectionUtils.isEmpty(orderDetailVos)) {
            return BigDecimal.ZERO;
        }
        List<String> productCodeList = orderDetailVos.stream().map(x -> x.getGoodsCode()).distinct().collect(Collectors.toList());
        List<ProductVo> productVos = productVoService.findDetailsByIdsOrProductCodes(null, productCodeList);
        for (ProductVo productVo : productVos) {
            if (ObjectUtils.isEmpty(productVo.getConversionValue())) {
                productVo.setConversionValue("1");
            }
        }
        Map<String, BigDecimal> productConversionMap = productVos.stream().collect(Collectors.toMap(ProductVo::getMaterialCode, x -> new BigDecimal(x.getConversionValue())));
        BigDecimal data = BigDecimal.ZERO;
        if (QUANTITY.equals(dataType)) {
            BigDecimal deliveryData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getRealOutNum()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(x -> x.getRealOutNum().multiply(productConversionMap.getOrDefault(x.getGoodsCode(), BigDecimal.ONE)))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal returnData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getRealOutNum()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(x -> x.getRealOutNum().multiply(productConversionMap.getOrDefault(x.getGoodsCode(), BigDecimal.ONE)))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            data = deliveryData.subtract(returnData);
        } else if (AMOUNT.equals(dataType)) {

            BigDecimal deliveryData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.DELIVERY_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(DmsWarehouseOrderDetailVo::getTotalAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal returnData = orderDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTotalAmount()))
                    .filter(x -> DmsWarehouseOrderEnum.WAREHOUSE_ORDER_TYPE.STORED_ORDER.getCode().equals(x.getWarehouseOrderType()))
                    .map(DmsWarehouseOrderDetailVo::getTotalAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            data = deliveryData.subtract(returnData);
        }
        return data;
    }

    public static final String START_DATE = "startDate";

    public static final String END_DATE = "endDate";

    public static final String QUARTER = "quarter";

    public static final String YEAR = "year";


    /**
     * 获取档期啊年月的第一天和最后一天
     *
     * @param date
     * @param dateType
     * @return
     */
    public static Map<String, String> getDateMonthly(String date, String dateType) {
        LocalDate currentDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        LocalDate firstDayOfMonth = currentDate.withDayOfMonth(1);
        // 获取当前年月的最后一天
        LocalDate lastDayOfMonth = currentDate.withDayOfMonth(YearMonth.from(currentDate).lengthOfMonth());
        String startDateStr = firstDayOfMonth.format(DateTimeFormatter.ofPattern(dateType));
        String endDateStr = lastDayOfMonth.format(DateTimeFormatter.ofPattern(dateType));
        Map<String, String> map = Maps.newHashMap();
        map.put(START_DATE, startDateStr);
        map.put(END_DATE, endDateStr);
        return map;
    }

    /**
     * 获取季度的开始时间和结束时间
     *
     * @param date
     * @param dateType
     * @return
     */
    public static Map<String, String> getDateQuarter(String date, String dateType) {
        LocalDate currentDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        int month = currentDate.getMonthValue();
        int year = currentDate.getYear();

        String quarter = "";
        LocalDate startDate;
        LocalDate endDate;

        if (month >= 1 && month <= 3) {
            quarter = "Q1";
            startDate = LocalDate.of(year, Month.JANUARY, 1);
            endDate = LocalDate.of(year, Month.MARCH, 31);
        } else if (month >= 4 && month <= 6) {
            quarter = "Q2";
            startDate = LocalDate.of(year, Month.APRIL, 1);
            endDate = LocalDate.of(year, Month.JUNE, 30);
        } else if (month >= 7 && month <= 9) {
            quarter = "Q3";
            startDate = LocalDate.of(year, Month.JULY, 1);
            endDate = LocalDate.of(year, Month.SEPTEMBER, 30);
        } else {
            quarter = "Q4";
            startDate = LocalDate.of(year, Month.OCTOBER, 1);
            endDate = LocalDate.of(year, Month.DECEMBER, 31);
        }
        String startDateStr = startDate.format(DateTimeFormatter.ofPattern(dateType));
        String endDateStr = endDate.format(DateTimeFormatter.ofPattern(dateType));
        Map<String, String> map = Maps.newHashMap();
        map.put(START_DATE, startDateStr);
        map.put(END_DATE, endDateStr);
        map.put(QUARTER, quarter);
        return map;
    }

    /**
     * 获取年度的开始时间和结束时间
     *
     * @param date
     * @param dateType
     * @return
     */
    public static Map<String, String> getDateYears(String date, String dateType) {
        LocalDate currentDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        int currentYear = currentDate.getYear();

        LocalDate startDate = LocalDate.of(currentYear, 1, 1);
        LocalDate endDate = LocalDate.of(currentYear, 12, 31);
        String startDateStr = startDate.format(DateTimeFormatter.ofPattern(dateType));
        String endDateStr = endDate.format(DateTimeFormatter.ofPattern(dateType));
        Map<String, String> map = Maps.newHashMap();
        map.put(START_DATE, startDateStr);
        map.put(END_DATE, endDateStr);
        map.put(YEAR, currentDate.toString());
        return map;
    }

    /**
     * 获取pos计算拆分比例
     *
     * @return
     */
    public BigDecimal splittingRate(String customerCode, String years, String orgCode, String dataType) {
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(orgCode);
        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
        return splittingRateHandle(Collections.singletonList(customerCode), years, orgCodes, dataType).get(customerCode);
    }

    /**
     * 获取pos计算拆分比例
     *
     * @return
     */
    public Map<String, BigDecimal> splittingRateHandle(List<String> customerCodes, String years, List<String> orgCodes, String dataType) {
        Map<String, BigDecimal> rateMap = new HashMap<>();
        //根据数据字典查询客户
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(CUSTOMER_SPLITTING);
        //上月客户
        List<String> lastMonthCustomer = dictDataVos.stream().filter(e -> LAST_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());
        //本月客户
        List<String> currentMonthCustomer = dictDataVos.stream().filter(e -> CURRENT_MONTH.equals(e.getParentDictCode())).map(e -> e.getDictCode()).collect(Collectors.toList());

        customerCodes.forEach(e -> rateMap.put(e, splittingRateSingle(e, years, orgCodes, dataType, lastMonthCustomer, currentMonthCustomer)));
        return rateMap;
    }

    /**
     * 获取pos计算拆分比例
     *
     * @return
     */
    public Map<String, BigDecimal> splittingRateHandleWithHolding(List<String> customerCodes, String years, List<String> orgCodes, String dataType, List<String> lastMonthCustomer, List<String> currentMonthCustomer) {
        Map<String, BigDecimal> rateMap = new HashMap<>();

        customerCodes.forEach(e -> rateMap.put(e, splittingRateSingle(e, years, orgCodes, dataType, lastMonthCustomer, currentMonthCustomer)));
        return rateMap;
    }

    public BigDecimal splittingRateSingle(String customerCode, String years, List<String> orgCodes, String dataType, List<String> lastMonthCustomer, List<String> currentMonthCustomer) {
        //如果客户未在数据字典customer_splitting下，则无需分摊
        if (!lastMonthCustomer.contains(customerCode) && !currentMonthCustomer.contains(customerCode)) {
            return null;
        }
        if (lastMonthCustomer.contains(customerCode)) {
            Date date = DateUtil.dateAddMonth(DateUtil.parse(years + "-01", "yyyy-MM-dd"), -1);
            years = DateUtil.format(date, "yyyy-MM");
        }
        PosDataVo dto = new PosDataVo();
        dto.setYears(years);
        dto.setCustomerCode(customerCode);
        Page<PosDataVo> page = posDataService.findList(PageRequest.of(0, Integer.MAX_VALUE), dto);
        //未找到对应的POS数据，分摊为0
        if (page.getTotal() == 0) {
            return BigDecimal.ZERO;
        }
        List<PosDataVo> records = page.getRecords();
        List<PosDataVo> thisPos = records.stream().filter(e -> orgCodes.contains(e.getOrgCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(thisPos)) {
            return BigDecimal.ZERO;
        }
        BigDecimal total = records.stream().filter(k -> Objects.nonNull(k.getAmount()))
                .map(PosDataVo::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal rate = BigDecimal.ZERO;
        if (ObjectUtils.isNotEmpty(total) && total.compareTo(BigDecimal.ZERO) == 1) {
            rate = thisPos.stream().filter(k -> Objects.nonNull(k.getAmount()))
                    .map(PosDataVo::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(total, 6, BigDecimal.ROUND_HALF_UP);
        }

        return rate;
    }


    public Map<String, ProductVo> findProductListByItemCodes(Set<String> itemCodeSet) {
        if (CollectionUtils.isEmpty(itemCodeSet)) {
            return Maps.newHashMap();
        }
        ProductQueryDto dto = new ProductQueryDto() {{
            this.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            this.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            this.setTenantCode(TenantUtils.getTenantCode());
            this.setCostLabel(BooleanEnum.TRUE.getCapital());
            this.setProductType("ZERT");
            this.setItemCodeSet(itemCodeSet);
        }};
        List<ProductVo> productVoList = productVoService.findByProductQueryDto(dto);
        if (CollectionUtils.isEmpty(productVoList)) {
            return Maps.newHashMap();
        }
        return productVoList.stream().collect(Collectors.toMap(ProductVo::getProductCode, Function.identity()));
    }
}
