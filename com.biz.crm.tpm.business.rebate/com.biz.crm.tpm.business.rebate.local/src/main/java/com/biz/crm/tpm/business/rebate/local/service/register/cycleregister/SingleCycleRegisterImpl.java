package com.biz.crm.tpm.business.rebate.local.service.register.cycleregister;

import com.biz.crm.tpm.business.rebate.local.service.SaleRebatePolicyTaskService;
import com.biz.crm.tpm.business.rebate.sdk.dto.SaleRebatePolicyDto;
import com.biz.crm.tpm.business.rebate.sdk.register.SaleRebatePolicyCycleRegister;
import com.biz.crm.tpm.business.rebate.sdk.vo.SaleRebateExecutionDateRangeVo;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: 一次性
 * @author: lifei
 * @date: 2022/2/17 16:06
 */
@Component
@Slf4j
public class SingleCycleRegisterImpl implements SaleRebatePolicyCycleRegister {

  /**
   * 返利周期名称
   */
  private static final String REBATE_POLICY_CYCLE_NAME = "一次性返利";

  /**
   * 返利周期编码
   */
  private static final String REBATE_POLICY_CYCLE_CODE = "SINGLECYCLE";
  /**
   * 一次性返利基础定时任务表达式
   */
  public static final String SINGLE_CYCLE_CRON = null;

  /**
   * 排序
   */
  private static final Integer REBATE_POLICY_CYCLE_SORT = 6;

  @Autowired(required = false)
  private SaleRebatePolicyTaskService saleRebatePolicyTaskService;

  @Override
  public String getSaleRebatePolicyCycleCode() {
    return REBATE_POLICY_CYCLE_CODE;
  }

  @Override
  public String getSaleRebatePolicyCycleName() {
    return REBATE_POLICY_CYCLE_NAME;
  }

  /**
   * 获取返利周期定时任务表达式
   */
  @Override
  public String getSaleRebateCronExpression() {
    return SINGLE_CYCLE_CRON;
  }

  /**
   * 判断是否需要计算财年偏移量
   *
   * @return
   */
  @Override
  public boolean getCheckFiscalYear() {
    return false;
  }

  /**
   * 返利周期类型
   */
  @Override
  public int getCycleType() {
    return 0;
  }

  /**
   * 周期最小单位
   */
  @Override
  public int getCycleNum() {
    return 0;
  }

  @Override
  public Integer getCycleSort() {
    return REBATE_POLICY_CYCLE_SORT;
  }

  /**
   * （创建定时任务）
   *
   * <AUTHOR>
   * @date
   */
  @Override
  @Transactional
  public void onRequestcreate(SaleRebatePolicyDto saleRebatePolicyDto) {
    this.saleRebatePolicyTaskService.createRebateSingletask(saleRebatePolicyDto);
  }

  /**
   * （修改定时任务）
   *
   * <AUTHOR>
   * @date
   */
  @Override
  @Transactional
  public void onRequestUpdate(SaleRebatePolicyDto saleRebatePolicyDto) {
    this.saleRebatePolicyTaskService.updateRebatetask(saleRebatePolicyDto);
  }

  /**
   * 获取当天定时任务执行范围
   *
   * <AUTHOR>
   * @date
   */
  @Override
  public SaleRebateExecutionDateRangeVo getExecutionDateRangeVo(
      SaleRebatePolicyDto saleRebatePolicyDto) {
    SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
    String format = formatDate.format(saleRebatePolicyDto.getCalculateTime());
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(saleRebatePolicyDto.getSaleRebateEndTime());
    calendar.set(Calendar.DAY_OF_YEAR,
        calendar.get(Calendar.DAY_OF_YEAR) + saleRebatePolicyDto.getCalculateDayNum()+1);
    //
    String format1 = formatDate.format(calendar.getTime());
    if(format.equals(format1)){
      SaleRebateExecutionDateRangeVo saleRebateExecutionDateRangeVo = new SaleRebateExecutionDateRangeVo();
      saleRebateExecutionDateRangeVo.setSaleRebateEndTime(saleRebatePolicyDto.getSaleRebateEndTime());
      saleRebateExecutionDateRangeVo
          .setSaleRebateStartTime(saleRebatePolicyDto.getSaleRebateStartTime());
      return saleRebateExecutionDateRangeVo;
    }else {
     return null;
    }
  }

}
