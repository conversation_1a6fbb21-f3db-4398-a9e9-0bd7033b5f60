<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyDetailMapper">

  <resultMap type="com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyDetail" id="SaleRebatePolicyDetailMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="createAccount" column="create_account" jdbcType="VARCHAR"/>
    <result property="createName" column="create_name" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="modifyAccount" column="modify_account" jdbcType="VARCHAR"/>
    <result property="modifyName" column="modify_name" jdbcType="VARCHAR"/>
    <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    <result property="enableStatus" column="enable_status" jdbcType="VARCHAR"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="actualRebateAmount" column="actual_rebate_amount" jdbcType="NUMERIC"/>
    <result property="adjustAmount" column="adjust_amount" jdbcType="NUMERIC"/>
    <result property="rebateRatio" column="rebate_ratio" jdbcType="NUMERIC"/>
    <result property="billStatus" column="bill_status" jdbcType="INTEGER"/>
    <result property="productType" column="product_type" jdbcType="INTEGER"/>
    <result property="allocationType" column="allocation_type" jdbcType="INTEGER"/>
    <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
    <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
    <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
    <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
    <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
    <result property="productLevelCode" column="product_level_code" jdbcType="VARCHAR"/>
    <result property="productLevelName" column="product_level_name" jdbcType="VARCHAR"/>
    <result property="productName" column="product_name" jdbcType="VARCHAR"/>
    <result property="rebateAmount" column="rebate_amount" jdbcType="NUMERIC"/>
    <result property="saleRebateCalculationYears" column="sale_rebate_calculation_years" jdbcType="TIMESTAMP"/>
    <result property="saleRebateEndTime" column="sale_rebate_end_time" jdbcType="TIMESTAMP"/>
    <result property="saleRebatePolicyCode" column="sale_rebate_policy_code" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyCycle" column="sale_rebate_policy_cycle" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyCycleName" column="sale_rebate_policy_cycle_name" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyName" column="sale_rebate_policy_name" jdbcType="VARCHAR"/>
    <result property="saleRebateStartTime" column="sale_rebate_start_time" jdbcType="TIMESTAMP"/>
    <result property="saleRebateType" column="sale_rebate_type" jdbcType="VARCHAR"/>
    <result property="saleRebateTypeName" column="sale_rebate_type_name" jdbcType="VARCHAR"/>
    <result property="speedNo" column="speed_no" jdbcType="VARCHAR"/>
    <result property="saleRebateDetailCode" column="sale_rebate_detail_code" jdbcType="VARCHAR"/>
  </resultMap>

  <select id="findByConditions" resultMap="SaleRebatePolicyDetailMap">
    select a.* from tpm_sale_rebate_policy_detail a
    <where>
    a.del_flag =#{dto.delFlag} and a.tenant_code =#{dto.tenantCode}
      <if test="dto.saleRebatePolicyCode !=null and dto.saleRebatePolicyCode!=''">
        <bind name="likeSaleRebatePolicyCode" value="'%'+dto.saleRebatePolicyCode+'%'"/>
        and a.sale_rebate_policy_code like #{likeSaleRebatePolicyCode}
      </if>
      <if test="dto.saleRebateDetailCode !=null and dto.saleRebateDetailCode!=''">
        <bind name="likeSaleRebateDetailCode" value="'%'+dto.saleRebateDetailCode+'%'"/>
        and a.sale_rebate_detail_code like #{likeSaleRebateDetailCode}
      </if>
      <if test="dto.saleRebatePolicyName !=null and dto.saleRebatePolicyName!=''">
        <bind name="likeSaleRebatePolicyName" value="'%'+dto.saleRebatePolicyName+'%'"/>
        and a.sale_rebate_policy_name like #{likeSaleRebatePolicyName}
      </if>
      <if test="dto.billType !=null and dto.billType!=''">
        and a.bill_Type = #{dto.billType}
      </if>
      <if test="dto.billStatus !=null">
        and a.bill_status = #{dto.billStatus}
      </if>
      <if test="dto.saleRebateCalculationYears !=null and dto.saleRebateCalculationYears!=''">
        and a.sale_rebate_calculation_years = #{dto.saleRebateCalculationYears}
      </if>
        order by a.create_time desc
    </where>
  </select>

  <select id="findByConditionsList" resultMap="SaleRebatePolicyDetailMap">
    select a.* from tpm_sale_rebate_policy_detail a
    <where>
      a.del_flag =#{dto.delFlag} and a.tenant_code =#{dto.tenantCode}
      <if test="dto.saleRebatePolicyCode !=null and dto.saleRebatePolicyCode!=''">
        and a.sale_rebate_policy_code = #{dto.saleRebatePolicyCode}
      </if>
      <if test="dto.customerCode !=null and dto.customerCode!=''">
        and a.customer_code = #{dto.customerCode}
      </if>
      <if test="dto.saleRebateStartTime !=null">
        and a.sale_rebate_start_time = #{dto.saleRebateStartTime}
      </if>
      <if test="dto.saleRebateEndTime !=null">
        and a.sale_rebate_end_time = #{dto.saleRebateEndTime}
      </if>
      <if test="dto.isTest !=null and dto.isTest!=''">
        and a.is_test = #{dto.isTest}
      </if>
      <if test="dto.productType !=null ">
        and a.product_type = #{dto.productType}
      </if>
      <if test="dto.productCode !=null and dto.productCode!=''">
        and a.product_code = #{dto.productCode}
      </if>
      <if test="dto.productLevelCode !=null and dto.productLevelCode!=''">
        and a.product_level_code = #{dto.productLevelCode}
      </if>
      <if test="dto.SaleRebatePolicyFormulaId !=null and dto.SaleRebatePolicyFormulaId!=''">
        and a.sale_rebate_policy_formula_id = #{dto.SaleRebatePolicyFormulaId}
      </if>
      order by a.create_time desc
    </where>
  </select>
</mapper>

