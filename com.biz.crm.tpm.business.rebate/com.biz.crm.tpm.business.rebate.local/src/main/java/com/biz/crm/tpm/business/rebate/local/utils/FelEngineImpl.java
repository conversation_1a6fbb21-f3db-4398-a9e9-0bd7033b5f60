package com.biz.crm.tpm.business.rebate.local.utils;


import com.greenpineyu.fel.Expression;
import com.greenpineyu.fel.FelEngine;
import com.greenpineyu.fel.common.FelBuilder;
import com.greenpineyu.fel.compile.CompileService;
import com.greenpineyu.fel.context.ArrayCtxImpl;
import com.greenpineyu.fel.context.FelContext;
import com.greenpineyu.fel.context.Var;
import com.greenpineyu.fel.function.CommonFunction;
import com.greenpineyu.fel.function.FunMgr;
import com.greenpineyu.fel.function.Function;
import com.greenpineyu.fel.optimizer.Optimizer;
import com.greenpineyu.fel.optimizer.VarVisitOpti;
import com.greenpineyu.fel.parser.AntlrParser;
import com.greenpineyu.fel.parser.FelNode;
import com.greenpineyu.fel.parser.Parser;
import com.greenpineyu.fel.security.SecurityMgr;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title FelEngineImpl
 * @date 2023/6/26 9:49
 * @description 数据计算引擎自定义扩展
 */
public final class FelEngineImpl implements FelEngine {

  private FelContext context;

  private CompileService compileService;

  private Parser parser;

  private FunMgr funMgr;

  private SecurityMgr securityMgr;

  public FelEngineImpl(FelContext felContext) {
    this.context = felContext;
    this.securityMgr = FelBuilder.newSecurityMgr();
    this.compileService = new CompileService();
    this.parser = new AntlrParser(this);
    this.funMgr = new FunMgr();
  }

  public FelEngineImpl() {
    this(new ArrayCtxImpl());
    //取最大值
    Function MAX = new CommonFunction() {
      @Override
      public String getName() {
        return "MAX";
      }

      @Override
      public Number call(Object[] objects) {
        int length = objects.length;
        List<Integer> integerType = new ArrayList<>(length);
        List<Double> doubleType = new ArrayList<>(length);
        List<BigDecimal> bigDecimalType = new ArrayList<>(length);
        List<Float> floatType = new ArrayList<>(length);
        List<Long> longType = new ArrayList<>(length);
        for (Object o : objects) {
          if (o instanceof  Integer){
            integerType.add((Integer) o);
          }
          else if (o instanceof Double){
            doubleType.add((Double) o);
          }
          else if (o instanceof BigDecimal){
            bigDecimalType.add((BigDecimal) o);
          }
          else if (o instanceof Float){
            floatType.add((Float) o);
          }
          else if (o instanceof Long){
            longType.add((Long) o);
          }
        }
        String type = objects[0].getClass().getName();
        if (type.equals(Integer.class.getName())){
          return integerType.stream().max(Integer::compareTo).get();
        }
        else if (type.equals(Double.class.getName())){
          return doubleType.stream().max(Double::compareTo).get();
        }
        else if (type.equals(BigDecimal.class.getName())){
          return bigDecimalType.stream().max(BigDecimal::compareTo).get();
        }
        else if (type.equals(Float.class.getName())){
          return floatType.stream().max(Float::compareTo).get();
        }
        else if (type.equals(Long.class.getName())){
          return longType.stream().max(Long::compareTo).get();
        }
        else {
          return null;
        }
      }
    };
    this.addFun(MAX);

    //取最小值
    Function MIN = new CommonFunction() {

      @Override
      public String getName() {
        return "MIN";
      }

      @Override
      public Object call(Object[] objects) {
        int length = objects.length;
        List<Integer> integerType = new ArrayList<>(length);
        List<Double> doubleType = new ArrayList<>(length);
        List<BigDecimal> bigDecimalType = new ArrayList<>(length);
        List<Float> floatType = new ArrayList<>(length);
        List<Long> longType = new ArrayList<>(length);
        for (Object o : objects) {
          if (o instanceof  Integer){
            integerType.add((Integer) o);
          }
          else if (o instanceof Double){
            doubleType.add((Double) o);
          }
          else if (o instanceof BigDecimal){
            bigDecimalType.add((BigDecimal) o);
          }
          else if (o instanceof Float){
            floatType.add((Float) o);
          }
          else if (o instanceof Long){
            longType.add((Long) o);
          }
        }
        String type = objects[0].getClass().getName();
        if (type.equals(Integer.class.getName())){
          return integerType.stream().min(Integer::compareTo).get();
        }
        else if (type.equals(Double.class.getName())){
          return doubleType.stream().min(Double::compareTo).get();
        }
        else if (type.equals(BigDecimal.class.getName())){
          return bigDecimalType.stream().min(BigDecimal::compareTo).get();
        }
        else if (type.equals(Float.class.getName())){
          return floatType.stream().min(Float::compareTo).get();
        }
        else if (type.equals(Long.class.getName())){
          return longType.stream().min(Long::compareTo).get();
        }
        else {
          return null;
        }
      }
    };
    this.addFun(MIN);
  }

  @Override
  public Object eval(String s) {
    return this.eval(s, this.context);
  }

  @Override
  public Object eval(String s, FelContext felContext) {
    return this.parse(s).eval(felContext);
  }

  public Object eval(String s, Var... vars) {
    FelNode felNode = this.parse(s);
    VarVisitOpti varVisitOpti = new VarVisitOpti(vars);
    felNode = varVisitOpti.call(this.context, felNode);
    return felNode.eval(this.context);
  }


  @Override
  public FelNode parse(String s) {
    return this.parser.parse(s);
  }

  public Expression compile(String s, Var... vars) {
    return this.compile(s, (FelContext) null, new VarVisitOpti(vars));
  }

  @Override
  public Expression compile(String s, FelContext felContext, Optimizer... optimizers) {
    if (felContext == null) {
      felContext = this.context;
    }
    FelNode felNode = this.parse(s);
    if (optimizers != null) {
      Optimizer[] optimizers1 = optimizers;
      int item = optimizers.length;

      for (int i = 0; i < item; ++i) {
        Optimizer optimizer = optimizers1[i];
        if (optimizer != null) {
          felNode = optimizer.call(felContext, felNode);
        }
      }
    }
    return this.compileService.compile(felContext, felNode, s);
  }

  @Override
  public FelContext getContext() {
    return this.context;
  }

  @Override
  public void addFun(Function function) {
    this.funMgr.add(function);
  }

  @Override
  public CompileService getCompiler() {
    return this.compileService;
  }

  @Override
  public void setCompiler(CompileService compileService) {
    this.compileService = compileService;
  }

  @Override
  public Parser getParser() {
    return this.parser;
  }

  @Override
  public void setParser(Parser parser) {
    this.parser = parser;
  }

  @Override
  public FunMgr getFunMgr() {
    return this.funMgr;
  }

  @Override
  public void setFunMgr(FunMgr funMgr) {
    this.funMgr = funMgr;
  }

  @Override
  public void setContext(FelContext felContext) {
    this.context = felContext;
  }

  @Override
  public SecurityMgr getSecurityMgr() {
    return this.securityMgr;
  }

  @Override
  public void setSecurityMgr(SecurityMgr securityMgr) {
    this.securityMgr = securityMgr;
  }


  public String toString() {
    return "FelEngine";
  }
}
