package com.biz.crm.tpm.business.rebate.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @describe: 返利政策调整明细
 * @createTime 2022年02月28日 12:45:00
 */

@Getter
@Setter
@TableName("tpm_sale_rebate_adjust_detail")
@Entity
@Table(name = "tpm_sale_rebate_adjust_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_sale_rebate_adjust_detail", comment = "返利政策调整明细")
public class SaleRebateAdjustDetail extends TenantFlagOpEntity {

  /**
   * 返利明细编码
   */
  @Column(name = "sale_rebate_detail_code",  nullable = false, columnDefinition = "VARCHAR(128) COMMENT '返利明细编码'")
  @ApiModelProperty("返利明细编码")
  private String saleRebateDetailCode;

  /**
   * 返利名称
   */
  @Column(name = "sale_rebate_policy_name",  nullable = false, columnDefinition = "VARCHAR(128) COMMENT '促销名称'")
  @ApiModelProperty("返利名称")
  private String saleRebatePolicyName;

  /**
   * 客户编码
   */
  @Column(name = "customer_code",  nullable = false, columnDefinition = "VARCHAR(225) COMMENT '客户编码'")
  @ApiModelProperty("客户编码")
  private String customerCode;

  /**
   * 客户名称
   */
  @Column(name = "customer_name", nullable = false, columnDefinition = "VARCHAR(128) COMMENT '客户名称'")
  @ApiModelProperty("客户名称")
  private String customerName;

  /**
   * 调整金额
   */
  @Column(name = "adjust_amount", columnDefinition = "decimal(20,4) COMMENT '调整金额'")
  @ApiModelProperty("调整金额")
  private BigDecimal adjustAmount;
}
