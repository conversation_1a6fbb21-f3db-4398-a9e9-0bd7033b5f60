<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.rebate.local.mapper.SaleRebatePolicyFormulaInfoMapper">

  <resultMap type="com.biz.crm.tpm.business.rebate.local.entity.SaleRebatePolicyFormulaInfo" id="SaleRebatePolicyFormulaInfoMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyCode" column="sale_rebate_policy_code" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyCondition" column="sale_rebate_policy_condition" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyConditionName" column="sale_rebate_policy_condition_name" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyFormula" column="sale_rebate_policy_formula" jdbcType="VARCHAR"/>
    <result property="saleRebatePolicyFormulaName" column="sale_rebate_policy_formula_name" jdbcType="VARCHAR"/>
  </resultMap>
  
  <sql id = "saleRebatePolicyFormulaInfo">
    tenant_code tenantCode,
    sale_rebate_policy_code saleRebatePolicyCode,
    sale_rebate_policy_condition saleRebatePolicyCondition,
    sale_rebate_policy_condition_name saleRebatePolicyConditionName,
    sale_rebate_policy_formula saleRebatePolicyFormula,
    sale_rebate_policy_formula_name saleRebatePolicyFormulaName,
    id id
  </sql>

  <select id="findByConditions" resultMap="SaleRebatePolicyFormulaInfoMap">
    select
      <include refid="saleRebatePolicyFormulaInfo"/>
    from tpm_sale_rebate_policy_formula_info
    where
    tenant_code=#{saleRebatePolicyFormulaInfo.tenantCode}
  </select>
</mapper>

