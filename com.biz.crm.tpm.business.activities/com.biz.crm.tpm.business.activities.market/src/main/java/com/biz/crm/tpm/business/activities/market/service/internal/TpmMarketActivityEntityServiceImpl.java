package com.biz.crm.tpm.business.activities.market.service.internal;

import com.biz.crm.tpm.business.activities.market.entity.TpmMarketActivityModelEntity;
import com.biz.crm.tpm.business.activities.market.repository.TpmMarketActivityModelRepository;
import com.biz.crm.tpm.business.activities.market.service.TpmMarketActivityEntityService;
import com.biz.crm.tpm.business.activities.market.service.TpmMarketActivityFileService;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityFileVo;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityModelVo;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.LinkedHashSet;
import java.util.ArrayList;

import com.google.common.collect.Lists;

/**
 * tpm市场活动执行表单;(tpm_market_activity_model)表服务实现类
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-10
 */
@Service("tpmMarketActivityModelService")
public class TpmMarketActivityEntityServiceImpl implements TpmMarketActivityEntityService {
  @Autowired
  private TpmMarketActivityModelRepository tpmMarketActivityModelRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Autowired
  private TpmMarketActivityFileService tpmMarketActivityFileService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return 分页数据
   */
  @Override
  public Page<TpmMarketActivityModelVo> findByConditions(Pageable pageable, TpmMarketActivityModelVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmMarketActivityModelVo();
    }
    return this.tpmMarketActivityModelRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmMarketActivityModelVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmMarketActivityModelEntity tpmMarketActivityModel = this.tpmMarketActivityModelRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (tpmMarketActivityModel == null) {
      return null;
    }
    TpmMarketActivityModelVo tpmMarketActivityModelVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMarketActivityModel, TpmMarketActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    //获取关联文件数据
    tpmMarketActivityModelVo.setFileVos(this.tpmMarketActivityFileService.findByModelId(tpmMarketActivityModel.getId()));
    return tpmMarketActivityModelVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmMarketActivityModelVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmMarketActivityModelEntity> tpmMarketActivityModels = this.tpmMarketActivityModelRepository.findByIds(ids);
    Collection<TpmMarketActivityModelVo> tpmMarketActivityModelVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmMarketActivityModels, TpmMarketActivityModelEntity.class, TpmMarketActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmMarketActivityModelVos);
  }

  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 编号
   * @return 单条数据
   */
  @Override
  public TpmMarketActivityModelVo findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    TpmMarketActivityModelEntity tpmMarketActivityModel = this.tpmMarketActivityModelRepository.findByParentCode(parentCode);
    if (tpmMarketActivityModel == null) {
      return null;
    }
    TpmMarketActivityModelVo tpmMarketActivityModelVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMarketActivityModel, TpmMarketActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    //获取关联文件数据
    tpmMarketActivityModelVo.setFileVos(this.tpmMarketActivityFileService.findByModelId(tpmMarketActivityModel.getId()));
    return tpmMarketActivityModelVo;
  }

  /**
   * 新增数据
   *
   * @param tpmMarketActivityModelDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmMarketActivityModelVo create(TpmMarketActivityModelVo tpmMarketActivityModelDto) {
    this.createValidate(tpmMarketActivityModelDto);
    TpmMarketActivityModelEntity tpmMarketActivityModel = this.nebulaToolkitService.copyObjectByWhiteList(tpmMarketActivityModelDto, TpmMarketActivityModelEntity.class, LinkedHashSet.class, ArrayList.class);
    tpmMarketActivityModel.setTenantCode(TenantUtils.getTenantCode());
    this.tpmMarketActivityModelRepository.saveOrUpdate(tpmMarketActivityModel);
    TpmMarketActivityModelVo tpmMarketActivityModelVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMarketActivityModel, TpmMarketActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    tpmMarketActivityModelVo.setId(tpmMarketActivityModel.getId());
    //保存关联文件
    if (!CollectionUtils.isEmpty(tpmMarketActivityModelDto.getFileVos())) {
      List<TpmMarketActivityFileVo> fileVos = tpmMarketActivityModelDto.getFileVos();
      fileVos.forEach(file -> file.setActivityModelId(tpmMarketActivityModel.getId()));
      this.tpmMarketActivityFileService.createBatch(fileVos);
    }
    return tpmMarketActivityModelVo;
  }

  /**
   * 批量新增
   *
   * @param tpmMarketActivityModelVos 实体列表
   * @return 新增结果
   */
  @Transactional
  @Override
  public List<TpmMarketActivityModelVo> createBatch(Collection<TpmMarketActivityModelVo> tpmMarketActivityModelVos) {
    if (CollectionUtils.isEmpty(tpmMarketActivityModelVos)) {
      return Lists.newArrayList();
    }
    List<TpmMarketActivityModelVo> results = Lists.newArrayList();
    for (TpmMarketActivityModelVo tpmMarketActivityModelDto : tpmMarketActivityModelVos) {
      TpmMarketActivityModelVo result = this.create(tpmMarketActivityModelDto);
      results.add(result);
    }
    return results;
  }

  /**
   * 修改新据
   *
   * @param tpmMarketActivityModelDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmMarketActivityModelVo update(TpmMarketActivityModelVo tpmMarketActivityModelDto) {
    this.updateValidate(tpmMarketActivityModelDto);
    TpmMarketActivityModelEntity tpmMarketActivityModel = this.tpmMarketActivityModelRepository.findByIdAndTenantCode(tpmMarketActivityModelDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(tpmMarketActivityModel, "修改数据不存在，请检查！");
    TpmMarketActivityModelEntity tpmMarketActivityModelEntity = this.nebulaToolkitService.copyObjectByWhiteList(tpmMarketActivityModelDto, TpmMarketActivityModelEntity.class, LinkedHashSet.class, ArrayList.class);
    tpmMarketActivityModelEntity.setTenantCode(TenantUtils.getTenantCode());
    this.tpmMarketActivityModelRepository.saveOrUpdate(tpmMarketActivityModelEntity);
    return tpmMarketActivityModelDto;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmMarketActivityModelEntity> tpmMarketActivityModels = this.tpmMarketActivityModelRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(tpmMarketActivityModels)) {
      return;
    }
    this.tpmMarketActivityModelRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  /**
   * 创建验证
   *
   * @param tpmMarketActivityModelDto 创建实体
   */
  private void createValidate(TpmMarketActivityModelVo tpmMarketActivityModelDto) {
    Validate.notNull(tpmMarketActivityModelDto, "新增时，对象信息不能为空！");
    Validate.isTrue(tpmMarketActivityModelDto.getId() == null, "新增数据时,数据主键不为空!");
  }

  /**
   * 修改验证
   *
   * @param tpmMarketActivityModelDto 修改实体
   */
  private void updateValidate(TpmMarketActivityModelVo tpmMarketActivityModelDto) {
    Validate.notNull(tpmMarketActivityModelDto, "修改时，对象信息不能为空！");
    Validate.notBlank(tpmMarketActivityModelDto.getId(), "修改数据时，主键不能为空！");
  }

}