package com.biz.crm.tpm.business.activities.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.market.entity.TpmMarketActivityModelEntity;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityModelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * tpm市场活动执行表单;(tpm_market_activity_model)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-10
 */
@Mapper
public interface TpmMarketActivityModelMapper extends BaseMapper<TpmMarketActivityModelEntity>{
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<TpmMarketActivityModelVo> findByConditions(@Param("page") Page<TpmMarketActivityModelVo> page , @Param("dto") TpmMarketActivityModelVo dto);
}