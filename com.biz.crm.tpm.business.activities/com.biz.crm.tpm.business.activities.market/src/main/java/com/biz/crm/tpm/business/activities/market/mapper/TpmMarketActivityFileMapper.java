package com.biz.crm.tpm.business.activities.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.market.entity.TpmMarketActivityFileEntity;
import com.biz.crm.tpm.business.activities.market.entity.TpmMarketActivityModelEntity;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityFileVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * tpm会议执行文件;(tpm_meeting_activity_file)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Mapper
public interface TpmMarketActivityFileMapper extends BaseMapper<TpmMarketActivityFileEntity>{
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param vo 动态查询条件
     * @return 分页对象列表
     */
    Page<TpmMarketActivityFileVo> findByConditions(@Param("page") Page<TpmMarketActivityFileVo> page , @Param("dto") TpmMarketActivityFileVo vo);
}