package com.biz.crm.tpm.business.activities.market.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：tpm市场活动执行文件;
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@ApiModel(value = "MarketActivityFile",description = "tpm会议执行文件")
@TableName("tpm_market_activity_file")
@Getter
@Setter
@Entity(name = "tpm_market_activity_file")
@org.hibernate.annotations.Table(appliesTo = "tpm_market_activity_file", comment = "tpm市场活动执行文件")
@Table(name = "tpm_market_activity_file")
public class TpmMarketActivityFileEntity extends UuidEntity{
  
  /** 活动表单编码 */
  @ApiModelProperty(name = "activityModelId",notes = "活动表单编码", value= "活动表单编码")
  @Column(name = "activity_model_id", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动表单编码 '")
  private String activityModelId;
  
  /** 活动文件地址 */
  @ApiModelProperty(name = "activityFileUrl",notes = "活动文件地址", value= "活动文件地址")
  @Column(name = "activity_file_url", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动文件地址 '")
  private String activityFileUrl;
  
  /** 活动文件说明 */
  @ApiModelProperty(name = "activityFileRemark",notes = "活动文件说明", value= "活动文件说明")
  @Column(name = "activity_file_remark", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动文件说明 '")
  private String activityFileRemark;
  
  /** 活动文件类别(0-照片/1-视频) */
  @ApiModelProperty(name = "activityFileType",notes = "活动文件类别(0-照片/1-视频)", value= "活动文件类别(0-照片/1-视频)")
  @Column(name = "activity_file_type", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动文件类别(0-照片/1-视频) '")
  private String activityFileType;

}