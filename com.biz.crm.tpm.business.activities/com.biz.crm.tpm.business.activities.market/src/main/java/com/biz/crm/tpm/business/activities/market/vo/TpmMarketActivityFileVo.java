package com.biz.crm.tpm.business.activities.market.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Vo：tpm会议执行文件;
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@ApiModel(value = "MeetingActivityFile",description = "tpm会议执行文件")
@Getter
@Setter
public class TpmMarketActivityFileVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 活动表单编码 */
  @ApiModelProperty(name = "activityModelId",notes = "活动表单编码", value= "活动表单编码")
  private String activityModelId;
  /** 活动文件地址 */
  @ApiModelProperty(name = "activityFileUrl",notes = "活动文件地址", value= "活动文件地址")
  private String activityFileUrl;
  /** 活动文件说明 */
  @ApiModelProperty(name = "activityFileRemark",notes = "活动文件说明", value= "活动文件说明")
  private String activityFileRemark;
  /** 活动文件类别(0-照片/1-视频) */
  @ApiModelProperty(name = "activityFileType",notes = "活动文件类别(0-照片/1-视频)", value= "活动文件类别(0-照片/1-视频)")
  private String activityFileType;

}