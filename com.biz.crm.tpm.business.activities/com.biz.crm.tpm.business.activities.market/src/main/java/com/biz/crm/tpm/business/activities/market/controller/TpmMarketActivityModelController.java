package com.biz.crm.tpm.business.activities.market.controller;

import com.biz.crm.tpm.business.activities.market.service.TpmMarketActivityEntityService;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityModelVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.biz.crm.business.common.sdk.model.Result;

import java.util.List;

/**
 * tpm市场活动执行表单;(tpm_market_activity_model)控制层
 * <AUTHOR> jerry7
 * @date : 2022-11-10
 */
@Api(tags = "tpm市场活动执行表单功能接口")
@RestController
@RequestMapping("/v1/tpmMarketActivityModel")
@Slf4j
public class TpmMarketActivityModelController{
  /**
   * 服务对象
   */
  @Autowired
  private TpmMarketActivityEntityService tpmMarketActivityModelService;
  
  /**
   * 分页查询所有数据
   *
   * @param pageable        分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<TpmMarketActivityModelVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                 @ApiParam(name = "tpmMarketActivityModel", value = "核销采集信息") TpmMarketActivityModelVo dto) {
    try {
      Page<TpmMarketActivityModelVo> page = this.tpmMarketActivityModelService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<TpmMarketActivityModelVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id", required=true) String id) {
    try {
      TpmMarketActivityModelVo tpmMarketActivityModelVo = this.tpmMarketActivityModelService.findById(id);
      return Result.ok(tpmMarketActivityModelVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByCode")
  public Result<TpmMarketActivityModelVo> findByCode(@PathVariable @ApiParam(name = "code", value = "编号", required=true) String code) {
    try {
      TpmMarketActivityModelVo tpmMarketActivityModelVo = this.tpmMarketActivityModelService.findByParentCode(code);
      return Result.ok(tpmMarketActivityModelVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 新增数据
   *
   * @param tpmMarketActivityModelDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<TpmMarketActivityModelVo> create(@ApiParam(name = "tpmMarketActivityModelDto", value = "tpm市场活动执行表单") @RequestBody TpmMarketActivityModelVo tpmMarketActivityModelDto) {
    try {
      TpmMarketActivityModelVo result = this.tpmMarketActivityModelService.create(tpmMarketActivityModelDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 修改数据
   *
   * @param tpmMarketActivityModelDto 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<TpmMarketActivityModelVo> update(@ApiParam(name = "tpmMarketActivityModelDto", value = "tpm市场活动执行表单") @RequestBody TpmMarketActivityModelVo tpmMarketActivityModelDto) {
    try {
      TpmMarketActivityModelVo result = this.tpmMarketActivityModelService.update(tpmMarketActivityModelDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation(value = "删除数据")
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    try {
      this.tpmMarketActivityModelService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
}