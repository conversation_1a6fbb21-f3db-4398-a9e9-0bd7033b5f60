package com.biz.crm.tpm.business.activities.market.strategy;

import com.biz.crm.common.form.sdk.model.DynamicFormOperationStrategy;
import com.biz.crm.tpm.business.activities.market.model.TpmMarketActivityModel;
import com.biz.crm.tpm.business.activities.market.service.TpmMarketActivityEntityService;
import com.biz.crm.tpm.business.activities.market.service.TpmMarketModelService;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityModelVo;
import com.biz.crm.tpm.business.activities.sdk.register.ActivitiesCenterModuleRegister;
import com.biz.crm.tpm.business.activities.sdk.register.ExecutionCenterModuleRegister;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;

/**
 * tpm会议活动执行表单数据保存转换策略
 * <AUTHOR>
 */
@Component
public class DynamicFormOperationStrategyForTpmMarketActivity implements DynamicFormOperationStrategy<TpmMarketActivityModel> {

  @Autowired
  private TpmMarketActivityEntityService tpmMarketActivityEntityService;

  @Autowired
  private TpmMarketModelService tpmMarketModelService;

  @Autowired
  private ExecutionCenterModuleRegister executionCenterModuleRegister;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public void onDynamicFormCreate(TpmMarketActivityModel dynamicForm, String dynamicKey, String parentCode, Object parent) {
    dynamicForm.setParentCode(parentCode);
    dynamicForm.setDynamicKey(dynamicKey);
    TpmMarketActivityModelVo tpmMarketActivityModelVo = this.nebulaToolkitService.copyObjectByWhiteList(dynamicForm, TpmMarketActivityModelVo.class, HashSet.class, ArrayList.class,"fileVos","scanVos");
    this.tpmMarketActivityEntityService.create(tpmMarketActivityModelVo);
  }

  @Override
  public void onDynamicFormModify(TpmMarketActivityModel dynamicForm, String dynamicKey, String parentCode, Object parent) {
    dynamicForm.setParentCode(parentCode);
    dynamicForm.setDynamicKey(dynamicKey);
    TpmMarketActivityModelVo tpmMarketActivityModelVo = this.nebulaToolkitService.copyObjectByWhiteList(dynamicForm, TpmMarketActivityModelVo.class, HashSet.class, ArrayList.class,"fileVos","scanVos");
    this.tpmMarketActivityEntityService.update(tpmMarketActivityModelVo);
  }

  @Override
  public TpmMarketActivityModel findByParentCode(String dynamicKey, String parentCode) {
    if (StringUtils.isAnyBlank(dynamicKey, parentCode)) {
      return null;
    }
    return this.tpmMarketModelService.findByParentCodeAndDynamicKey(parentCode, dynamicKey);
  }

  @Override
  public String dynamicFormCode() {
    return StringUtils.uncapitalize(this.getClass().getSimpleName());
  }

  @Override
  public String dynamicFormName() {
    return "市场活动执行";
  }

  @Override
  public Class<TpmMarketActivityModel> dynamicFormClass() {
    return TpmMarketActivityModel.class;
  }

  @Override
  public String moduleCode() {
    return executionCenterModuleRegister.moduleCode();
  }

  @Override
  public void onDynamicFormsDelete(String parentCode, String dynamicKey) {

  }

  @Override
  public int getOrder() {
    return 1;
  }
}
