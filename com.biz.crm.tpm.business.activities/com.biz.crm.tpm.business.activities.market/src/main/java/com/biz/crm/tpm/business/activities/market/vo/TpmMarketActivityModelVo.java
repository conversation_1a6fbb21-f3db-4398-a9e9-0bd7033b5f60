package com.biz.crm.tpm.business.activities.market.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Vo：tpm市场活动执行表单;
 * <AUTHOR> jerry7
 * @date : 2022-11-10
 */
@ApiModel(value = "MarketActivityModel",description = "tpm市场活动执行表单")
@Getter
@Setter
public class TpmMarketActivityModelVo extends TenantFlagOpVo {
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  private String parentCode;
  /** 表单key */
  @ApiModelProperty(name = "dynamicKey",notes = "表单key", value= "表单key")
  private String dynamicKey;
  /** 活动申请编号 */
  @ApiModelProperty(name = "activityCode",notes = "活动申请编号", value= "活动申请编号")
  private String activityCode;
  /** 客户名称 */
  @ApiModelProperty(name = "customerName",notes = "客户名称", value= "客户名称")
  private String customerName;
  /** 客户地址 */
  @ApiModelProperty(name = "customerAddress",notes = "客户地址", value= "客户地址")
  private String customerAddress;
  /** 负责人 */
  @ApiModelProperty(name = "contactPerson",notes = "负责人", value= "负责人")
  private String contactPerson;
  /** 联系电话 */
  @ApiModelProperty(name = "contactPhone",notes = "联系电话", value= "联系电话")
  private String contactPhone;
  /** 是否有珍酒历史区 */
  @ApiModelProperty(name = "hasHistoricalArea",notes = "是否有珍酒历史区", value= "是否有珍酒历史区")
  private String hasHistoricalArea;
  /** 是否与评估报告相符 */
  @ApiModelProperty(name = "isQualified",notes = "是否与评估报告相符", value= "是否与评估报告相符")
  private String isQualified;
  /** 是否有产品展示区 */
  @ApiModelProperty(name = "hasProductShowArea",notes = "是否有产品展示区", value= "是否有产品展示区")
  private String hasProductShowArea;
  /** 是否有酿酒工艺区 */
  @ApiModelProperty(name = "hasBrewingTechnologyArea",notes = "是否有酿酒工艺区", value= "是否有酿酒工艺区")
  private String hasBrewingTechnologyArea;
  /** 是否仅展示和销售赠酒 */
  @ApiModelProperty(name = "isOnlyComplimentaryWine",notes = "是否仅展示和销售赠酒", value= "是否仅展示和销售赠酒")
  private String isOnlyComplimentaryWine;
  /** 是否有品鉴区 */
  @ApiModelProperty(name = "hasIdentificationArea",notes = "是否有品鉴区", value= "是否有品鉴区")
  private String hasIdentificationArea;
  /** 备注 */
  @ApiModelProperty(name = "remark",notes = "备注", value= "备注")
  private String remark;
  /**
   * 管理文件列表
   */
  private List<TpmMarketActivityFileVo> fileVos;
}