package com.biz.crm.tpm.business.activities.market.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityFileVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * tpm市场活动执行文件;(tpm_meeting_activity_file)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
public interface TpmMarketActivityFileService {
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmMarketActivityFileVo> findByConditions(Pageable pageable, TpmMarketActivityFileVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmMarketActivityFileVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmMarketActivityFileVo> findByIds(Collection<String> ids);
  /**
   * 通过表单编码查询文件数据
   *
   * @param modelId 编号
   * @return 单条数据
   */
  List<TpmMarketActivityFileVo> findByModelId(String modelId);
  
  /**
   * 新增数据
   *
   * @param tpmMarketActivityFileVo 实体对象
   * @return 新增结果
   */
  TpmMarketActivityFileVo create(TpmMarketActivityFileVo tpmMarketActivityFileVo);
  /**
   * 批量新增
   * @param tpmMarketActivityFileVos
   * @return
   */
  List<TpmMarketActivityFileVo> createBatch(Collection<TpmMarketActivityFileVo> tpmMarketActivityFileVos);
  /**
   * 修改数据
   *
   * @param tpmMarketActivityFileVo 实体对象
   * @return 修改结果
   */
  TpmMarketActivityFileVo update(TpmMarketActivityFileVo tpmMarketActivityFileVo);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
    
}