package com.biz.crm.tpm.business.activities.market.service;

import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityModelVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Collection;
import java.util.List;

/**
 * tpm市场活动执行表单;(tpm_market_activity_model)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-10
 */
public interface TpmMarketActivityEntityService {

  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmMarketActivityModelVo> findByConditions(Pageable pageable, TpmMarketActivityModelVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmMarketActivityModelVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmMarketActivityModelVo> findByIds(Collection<String> ids);
  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  TpmMarketActivityModelVo findByParentCode(String parentCode);
  
  /**
   * 新增数据
   *
   * @param tpmMarketActivityModelDto 实体对象
   * @return 新增结果
   */
  TpmMarketActivityModelVo create(TpmMarketActivityModelVo tpmMarketActivityModelDto);
  /**
   * 批量新增
   * @param tpmMarketActivityModelVos 实体对象列表
   * @return 新增结果
   */
  List<TpmMarketActivityModelVo> createBatch(Collection<TpmMarketActivityModelVo> tpmMarketActivityModelVos);
  /**
   * 修改数据
   *
   * @param tpmMarketActivityModelDto 实体对象
   * @return 修改结果
   */
  TpmMarketActivityModelVo update(TpmMarketActivityModelVo tpmMarketActivityModelDto);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
    
}