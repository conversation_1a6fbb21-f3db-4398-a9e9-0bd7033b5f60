package com.biz.crm.tpm.business.activities.market.repository;

import com.biz.crm.tpm.business.activities.market.entity.TpmMarketActivityModelEntity;
import com.biz.crm.tpm.business.activities.market.mapper.TpmMarketActivityModelMapper;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityModelVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.util.Collection;
import java.util.List;
import java.util.Collections;

/**
 * tpm市场活动执行表单;(tpm_market_activity_model)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-10
 */
@Component
public class TpmMarketActivityModelRepository extends ServiceImpl<TpmMarketActivityModelMapper, TpmMarketActivityModelEntity>{
  @Autowired
  private TpmMarketActivityModelMapper tpmMarketActivityModelMapper;
  
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  public Page<TpmMarketActivityModelVo> findByConditions(Pageable pageable, TpmMarketActivityModelVo dto) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<TpmMarketActivityModelVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmMarketActivityModelMapper.findByConditions(page, dto);
  }
  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmMarketActivityModel>
   */
  public List<TpmMarketActivityModelEntity> findByIds(Collection<String> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .in(TpmMarketActivityModelEntity::getId, ids)
        .eq(TpmMarketActivityModelEntity::getTenantCode, tenantCode)
        .list();
  }

  /**
   * 根据业务编码集合获取详情
   *
   * @param parentCode 业务编码
   * @return List<TpmMarketActivityModel>
   */
  public TpmMarketActivityModelEntity findByParentCode(String parentCode) {
    if(StringUtils.isBlank(parentCode)){
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(TpmMarketActivityModelEntity::getParentCode, parentCode)
            .eq(TpmMarketActivityModelEntity::getTenantCode, tenantCode)
            .one();
  }


  public TpmMarketActivityModelEntity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(TpmMarketActivityModelEntity::getTenantCode,tenantCode)
        .in(TpmMarketActivityModelEntity::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(TpmMarketActivityModelEntity::getTenantCode,tenantCode)
        .in(TpmMarketActivityModelEntity::getId,ids)
        .remove();
  }
}