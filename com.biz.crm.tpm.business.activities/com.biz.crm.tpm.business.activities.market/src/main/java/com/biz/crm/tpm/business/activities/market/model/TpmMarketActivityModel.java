package com.biz.crm.tpm.business.activities.market.model;


import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.BooleanSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 活动细类市场活动执行表单model
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TpmMarketActivityModel", description = "活动细类市场活动执行表单model")
public class TpmMarketActivityModel extends BaseActivityItemVo implements DynamicForm {

  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  @DynamicField(fieldName = "业务编码", required = false, controllKey = SimpleInputWidget.class)
  private String parentCode;

  /** 活动申请编号 */
  @ApiModelProperty(name = "activityCode",notes = "活动申请编号", value= "活动申请编号")
  @DynamicField(fieldName = "活动申请编号", required = false, controllKey = SimpleInputWidget.class)
  private String activityCode;

  /** 客户名称 */
  @ApiModelProperty(name = "customerName",notes = "客户名称", value= "客户名称")
  @DynamicField(fieldName = "客户名称", required = false, controllKey = SimpleInputWidget.class)
  private String customerName;

  /** 客户地址 */
  @ApiModelProperty(name = "customerAddress",notes = "客户地址", value= "客户地址")
  @DynamicField(fieldName = "客户地址", required = false, controllKey = SimpleInputWidget.class)
  private String customerAddress;

  /** 负责人 */
  @ApiModelProperty(name = "contactPerson",notes = "负责人", value= "负责人")
  @DynamicField(fieldName = "负责人", required = false, controllKey = SimpleInputWidget.class)
  private String contactPerson;

  /** 联系电话 */
  @ApiModelProperty(name = "contactPhone",notes = "联系电话", value= "联系电话")
  @DynamicField(fieldName = "联系电话", required = false, controllKey = SimpleInputWidget.class)
  private String contactPhone;

  /** 是否有珍酒历史区 */
  @ApiModelProperty(name = "hasHistoricalArea",notes = "是否有珍酒历史区", value= "是否有珍酒历史区")
  @DynamicField(fieldName = "是否有珍酒历史区", required = false, controllKey = BooleanSelectWidget.class)
  private String hasHistoricalArea;

  /** 是否与评估报告相符 */
  @ApiModelProperty(name = "isQualified",notes = "是否与评估报告相符", value= "是否与评估报告相符")
  @DynamicField(fieldName = "是否与评估报告相符", required = false, controllKey = BooleanSelectWidget.class)
  private String isQualified;

  /** 是否有产品展示区 */
  @ApiModelProperty(name = "hasProductShowArea",notes = "是否有产品展示区", value= "是否有产品展示区")
  @DynamicField(fieldName = "是否有产品展示区", required = false, controllKey = BooleanSelectWidget.class)
  private String hasProductShowArea;

  /** 是否有酿酒工艺区 */
  @ApiModelProperty(name = "hasBrewingTechnologyArea",notes = "是否有酿酒工艺区", value= "是否有酿酒工艺区")
  @DynamicField(fieldName = "是否有酿酒工艺区", required = false, controllKey = BooleanSelectWidget.class)
  private String hasBrewingTechnologyArea;

  /** 是否仅展示和销售赠酒 */
  @ApiModelProperty(name = "isOnlyComplimentaryWine",notes = "是否仅展示和销售赠酒", value= "是否仅展示和销售赠酒")
  @DynamicField(fieldName = "是否仅展示和销售赠酒", required = false, controllKey = BooleanSelectWidget.class)
  private String isOnlyComplimentaryWine;

  /** 是否有品鉴区 */
  @ApiModelProperty(name = "hasIdentificationArea",notes = "是否有品鉴区", value= "是否有品鉴区")
  @DynamicField(fieldName = "是否有品鉴区", required = false, controllKey = BooleanSelectWidget.class)
  private String hasIdentificationArea;

  /** 活动文件 */
  @DynamicField(fieldName = "活动文件" , modifiable = true , required = true , controllKey = SimpleInputWidget.class)
  @ApiModelProperty(name = "fileModels",notes = "活动文件", value= "活动文件")
  private List<TpmMarketFileModel> fileVos;

  /** 活动执行说明 */
  @ApiModelProperty(name = "remark",notes = "活动执行说明", value= "活动执行说明")
  @DynamicField(fieldName = "活动执行说明", required = false, controllKey = SimpleInputWidget.class)
  private String remark;

}
