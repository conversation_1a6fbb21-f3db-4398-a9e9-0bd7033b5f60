package com.biz.crm.tpm.business.activities.market.strategy;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.market.model.TpmMarketActivityModel;
import com.biz.crm.tpm.business.budget.sdk.model.AbstractDynamicTemplateModel;
import com.biz.crm.tpm.business.budget.sdk.strategy.DynamicTemplateOperationStrategy;
import com.bizunited.nebula.common.util.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 定额活动表单模板操作策略实现类
 *
 * <AUTHOR>
 */
@Component
public class DynamicTemplateOperationStrategyForTpmMarketActivity implements DynamicTemplateOperationStrategy {

  @Autowired
  private DynamicFormOperationStrategyForTpmMarketActivity dynamicFormOperationStrategyForTpmMarketActivity;

  @Override
  public String dynamicFormCode() {
    return this.dynamicFormOperationStrategyForTpmMarketActivity.dynamicFormCode();
  }

  @Override
  public void onDynamicTemplateCreate(JSONObject jsonObject, String dynamicKey, String parentCode) {
    TpmMarketActivityModel tpmQuotaActivityModel = JsonUtils.json2Obj(jsonObject.toJSONString(), TpmMarketActivityModel.class);
    this.dynamicFormOperationStrategyForTpmMarketActivity.onDynamicFormCreate(tpmQuotaActivityModel, dynamicKey, parentCode, null);
  }

  @Override
  public void onDynamicTemplateModify(JSONObject jsonObject, String dynamicKey, String parentCode) {
    TpmMarketActivityModel tpmQuotaActivityModel = JsonUtils.json2Obj(jsonObject.toJSONString(), TpmMarketActivityModel.class);
    this.dynamicFormOperationStrategyForTpmMarketActivity.onDynamicFormModify(tpmQuotaActivityModel, dynamicKey, parentCode, null);
  }

  @Override
  public Object findByParentCode(String dynamicKey, String parentCode) {
    return null;
  }
}
