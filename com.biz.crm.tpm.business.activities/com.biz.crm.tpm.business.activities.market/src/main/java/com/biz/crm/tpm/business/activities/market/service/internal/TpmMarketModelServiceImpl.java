package com.biz.crm.tpm.business.activities.market.service.internal;
import com.biz.crm.tpm.business.activities.market.model.TpmMarketActivityModel;
import com.biz.crm.tpm.business.activities.market.service.TpmMarketActivityEntityService;
import com.biz.crm.tpm.business.activities.market.service.TpmMarketModelService;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityModelVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;

/**
 * 市场活动model接口实现
 */
@Service
public class TpmMarketModelServiceImpl implements TpmMarketModelService {

  @Autowired
  private TpmMarketActivityEntityService tpmMarketActivityEntityService;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public TpmMarketActivityModel findByParentCodeAndDynamicKey(String parentCode, String dynamicKey) {
    if (StringUtils.isAnyEmpty(parentCode, dynamicKey)) {
      return null;
    }
    TpmMarketActivityModelVo tpmMarketActivityModelVo = this.tpmMarketActivityEntityService.findByParentCode(parentCode);
    if (ObjectUtils.isEmpty(tpmMarketActivityModelVo)) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmMarketActivityModelVo, TpmMarketActivityModel.class, HashSet.class, ArrayList.class,"fileVos");
  }
}
