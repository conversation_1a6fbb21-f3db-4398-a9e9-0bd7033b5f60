package com.biz.crm.tpm.business.activities.market.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.market.entity.TpmMarketActivityFileEntity;
import com.biz.crm.tpm.business.activities.market.repository.TpmMarketActivityFileRepository;
import com.biz.crm.tpm.business.activities.market.service.TpmMarketActivityFileService;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityFileVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;

/**
 * tpm会议执行文件;(tpm_meeting_activity_file)表服务实现类
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Service("tpmMarketActivityFileService")
public class TpmMarketActivityFileServiceImpl implements TpmMarketActivityFileService {
  @Autowired
  private TpmMarketActivityFileRepository tpmMarketActivityFileRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return 分页数据
   */
  @Override
  public Page<TpmMarketActivityFileVo> findByConditions(Pageable pageable, TpmMarketActivityFileVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmMarketActivityFileVo();
    }
    return this.tpmMarketActivityFileRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmMarketActivityFileVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmMarketActivityFileEntity tpmMarketActivityFile = this.tpmMarketActivityFileRepository.getById(id);
    if (tpmMarketActivityFile == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmMarketActivityFile, TpmMarketActivityFileVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmMarketActivityFileVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmMarketActivityFileEntity> tpmMarketActivityFiles = this.tpmMarketActivityFileRepository.findByIds(ids);
    Collection<TpmMarketActivityFileVo> tpmMarketActivityFileVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmMarketActivityFiles, TpmMarketActivityFileEntity.class, TpmMarketActivityFileVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmMarketActivityFileVos);
  }

  /**
   * 通过比编号查询单条数据
   *
   * @param modelId 编号
   * @return 单条数据
   */
  @Override
  public List<TpmMarketActivityFileVo> findByModelId(String modelId) {
    if (StringUtils.isBlank(modelId)) {
      return Lists.newArrayList();
    }
    List<TpmMarketActivityFileEntity> tpmMarketActivityFiles = this.tpmMarketActivityFileRepository.findByModelId(modelId);
    Collection<TpmMarketActivityFileVo> tpmMarketActivityFileVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmMarketActivityFiles, TpmMarketActivityFileEntity.class, TpmMarketActivityFileVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmMarketActivityFileVos);
  }

  /**
   * 新增数据
   *
   * @param tpmMarketActivityFileVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmMarketActivityFileVo create(TpmMarketActivityFileVo tpmMarketActivityFileVo) {

    this.createValidate(tpmMarketActivityFileVo);
    TpmMarketActivityFileEntity tpmMarketActivityFile = this.nebulaToolkitService.copyObjectByWhiteList(tpmMarketActivityFileVo, TpmMarketActivityFileEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmMarketActivityFileRepository.saveOrUpdate(tpmMarketActivityFile);
    tpmMarketActivityFileVo.setId(tpmMarketActivityFile.getId());
    return tpmMarketActivityFileVo;
  }

  /**
   * 批量新增
   *
   * @param tpmMarketActivityFileVos 新增实体列表
   * @return 新增结果
   */
  @Transactional
  @Override
  public List<TpmMarketActivityFileVo> createBatch(Collection<TpmMarketActivityFileVo> tpmMarketActivityFileVos) {
    if (CollectionUtils.isEmpty(tpmMarketActivityFileVos)) {
      return Lists.newArrayList();
    }
    List<TpmMarketActivityFileVo> results = Lists.newArrayList();
    for (TpmMarketActivityFileVo TpmMarketActivityFileVo : tpmMarketActivityFileVos) {
      TpmMarketActivityFileVo result = this.create(TpmMarketActivityFileVo);
      results.add(result);
    }
    return results;
  }

  /**
   * 修改新据
   *
   * @param tpmMarketActivityFileVo 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmMarketActivityFileVo update(TpmMarketActivityFileVo tpmMarketActivityFileVo) {
    this.updateValidate(tpmMarketActivityFileVo);
    TpmMarketActivityFileEntity tpmMarketActivityFile = this.tpmMarketActivityFileRepository.getById(tpmMarketActivityFileVo.getId());
    Validate.notNull(tpmMarketActivityFile, "修改数据不存在，请检查！");
    this.tpmMarketActivityFileRepository.saveOrUpdate(tpmMarketActivityFile);
    return tpmMarketActivityFileVo;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmMarketActivityFileEntity> tpmMarketActivityFiles = this.tpmMarketActivityFileRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(tpmMarketActivityFiles)) {
      return;
    }
    this.tpmMarketActivityFileRepository.removeByIds(ids);
  }

  /**
   * 创建验证
   *
   * @param tpmMarketActivityFileVo 创建实体
   */
  private void createValidate(TpmMarketActivityFileVo tpmMarketActivityFileVo) {
    Validate.notNull(tpmMarketActivityFileVo, "新增时，对象信息不能为空！");
    Validate.isTrue(tpmMarketActivityFileVo.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(tpmMarketActivityFileVo.getActivityModelId(), "新增数据时，活动表单编码不能为空！");
    Validate.notBlank(tpmMarketActivityFileVo.getActivityFileUrl(), "新增数据时，活动文件地址不能为空！");
    Validate.notBlank(tpmMarketActivityFileVo.getActivityFileRemark(), "新增数据时，活动文件说明不能为空！");
    Validate.notBlank(tpmMarketActivityFileVo.getActivityFileType(), "新增数据时，活动文件类别(0-照片/1-视频)不能为空！");
  }

  /**
   * 修改验证
   *
   * @param tpmMarketActivityFileVo 修改实体
   */
  private void updateValidate(TpmMarketActivityFileVo tpmMarketActivityFileVo) {
    Validate.notNull(tpmMarketActivityFileVo, "修改时，对象信息不能为空！");
    Validate.notBlank(tpmMarketActivityFileVo.getId(), "修改数据时，主键不能为空！");
  }

}