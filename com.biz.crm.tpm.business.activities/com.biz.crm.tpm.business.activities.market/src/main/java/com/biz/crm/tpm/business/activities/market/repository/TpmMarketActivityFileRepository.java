package com.biz.crm.tpm.business.activities.market.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.market.entity.TpmMarketActivityFileEntity;
import com.biz.crm.tpm.business.activities.market.mapper.TpmMarketActivityFileMapper;
import com.biz.crm.tpm.business.activities.market.vo.TpmMarketActivityFileVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * tpm会议执行文件;(tpm_meeting_activity_file)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Component
public class TpmMarketActivityFileRepository extends ServiceImpl<TpmMarketActivityFileMapper, TpmMarketActivityFileEntity>{
  @Autowired
  private TpmMarketActivityFileMapper tpmMarketActivityFileMapper;
  
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  public Page<TpmMarketActivityFileVo> findByConditions(Pageable pageable, TpmMarketActivityFileVo dto) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    Page<TpmMarketActivityFileVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmMarketActivityFileMapper.findByConditions(page, dto);
  }
  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmMarketActivityFile>
   */
  public List<TpmMarketActivityFileEntity> findByIds(Collection<String> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Collections.emptyList();
    }
    return this.lambdaQuery()
        .in(TpmMarketActivityFileEntity::getId, ids)
        .list();
  }

  /**
   * 根据表单id查询关联文件列表
   *
   * @param modelId 表单id
   * @return List<TpmMarketActivityFile>
   */
  public List<TpmMarketActivityFileEntity> findByModelId(String modelId) {
    if(StringUtils.isBlank(modelId)){
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .in(TpmMarketActivityFileEntity::getActivityModelId, modelId)
            .list();
  }
}