package com.biz.crm.tpm.business.activities.project.vo;

import com.biz.crm.business.common.sdk.vo.TenantOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel(value = "ProjectActivityRelationVo", description = "项目活动关联信息vo")
public class ProjectActivityRelationVo extends TenantOpVo {

  @ApiModelProperty("关联的活动编码")
  private String activityCode;

  @ApiModelProperty("费用预算编码")
  private String costBudgetCode;

  @ApiModelProperty("活动大类编码")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  private String costTypeCategoryName;

  /**
   * 参见com.biz.tpm.business.budget.sdk.enums.CostBudgetType
   */
  @ApiModelProperty("费用预算类型")
  private String type;

  @ApiModelProperty("年")
  private Integer year;

  @ApiModelProperty("季度")
  private Integer quarter;

  @ApiModelProperty("月份")
  private Integer month;

  @ApiModelProperty("预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("渠道名称")
  private String channelName;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("门店编码")
  private String terminalCode;

  @ApiModelProperty("门店名称")
  private String terminalName;

  @ApiModelProperty("产品层级编码")
  private String productLevelCode;

  @ApiModelProperty("产品层级名称")
  private String productLevelName;

  @ApiModelProperty("产品编码")
  private String productCode;

  @ApiModelProperty("产品名称")
  private String productName;

  @ApiModelProperty("费用预算可用余额")
  private BigDecimal finalBalance;
}
