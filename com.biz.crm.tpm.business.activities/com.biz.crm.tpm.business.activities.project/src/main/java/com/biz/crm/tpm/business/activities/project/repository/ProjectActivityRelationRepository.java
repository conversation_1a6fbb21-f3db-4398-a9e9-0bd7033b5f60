package com.biz.crm.tpm.business.activities.project.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityRelation;
import com.biz.crm.tpm.business.activities.project.mapper.ProjectActivityRelationMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class ProjectActivityRelationRepository extends ServiceImpl<ProjectActivityRelationMapper, ProjectActivityRelation> {
  /**
   * 根据关联的活动编码，查询信息
   */
  public List<ProjectActivityRelation> findByActivityCodeAndTenantCode(String activityCode, String tenantCode){
    return this.lambdaQuery().eq(ProjectActivityRelation::getActivityCode,activityCode).eq(ProjectActivityRelation::getTenantCode,tenantCode).list();
  }

  /**
   * 根据关联的活动编码集合，查询信息
   */
  public List<ProjectActivityRelation> findByActivityCodesAndTenantCode(Set<String> activityCodes, String tenantCode){
    return this.lambdaQuery().in(ProjectActivityRelation::getActivityCode,activityCodes).eq(ProjectActivityRelation::getTenantCode,tenantCode).list();
  }

  /**
   * 根据活动大类编码，查询信息
   */
  public List<ProjectActivityRelation> findByCostTypeCategoryCodeAndTenantCode(String costTypeCategoryCode, String tenantCode){
    return this.lambdaQuery().eq(ProjectActivityRelation::getCostTypeCategoryCode,costTypeCategoryCode).
            eq(ProjectActivityRelation::getTenantCode,tenantCode).
            list();
  }

  /**
   * 根据关联的活动编码，删除信息
   */
  public void deleteByActivityCodeAndTenantCode(String activityCode, String tenantCode){
    this.lambdaUpdate().eq(ProjectActivityRelation::getActivityCode,activityCode).eq(ProjectActivityRelation::getTenantCode,tenantCode).remove();
  }

  public void removeByIdsAndTenantCode(Set<String> needDeletes, String tenantCode) {
      this.lambdaUpdate()
          .eq(ProjectActivityRelation::getTenantCode,tenantCode)
          .in(ProjectActivityRelation::getId,needDeletes)
          .remove();
  }
}
