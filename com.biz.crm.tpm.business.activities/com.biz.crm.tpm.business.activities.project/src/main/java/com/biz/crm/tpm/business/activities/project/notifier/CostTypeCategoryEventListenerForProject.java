package com.biz.crm.tpm.business.activities.project.notifier;

import com.biz.crm.tpm.business.activities.project.service.ProjectActivityService;
import com.biz.crm.tpm.business.budget.sdk.event.CostTypeCategoryEventListener;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CostTypeCategoryEventListenerForProject implements CostTypeCategoryEventListener {

  @Autowired
  private ProjectActivityService projectActivityService;

  public void onDeleted(CostTypeCategoryVo costTypeCategoryVo){
    Validate.notNull(costTypeCategoryVo,"活动大类信息不能为空");
    Validate.notBlank(costTypeCategoryVo.getCategoryCode(),"活动大类编码不能为空");
    boolean exist = projectActivityService.existByCostTypeCategoryCode(costTypeCategoryVo.getCategoryCode());
    Validate.isTrue(!exist,"活动大类【%s】已存在活动申请数据，不能进行删除",costTypeCategoryVo.getCategoryCode());
  }
}
