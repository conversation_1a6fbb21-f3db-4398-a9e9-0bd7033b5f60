package com.biz.crm.tpm.business.activities.project.service;

import com.biz.crm.tpm.business.activities.project.dto.ProjectActivityFilesDto;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityFilesVo;

import java.util.List;

public interface ProjectActivityFilesService {

  /**
   * 保存项目活动附件
   */
  void save(List<ProjectActivityFilesDto> filesDtos, String activityCode);

  /**
   * 根据项目活动编码，查询附件
   */
  List<ProjectActivityFilesVo> findByActivityCode(String activityCode);
}
