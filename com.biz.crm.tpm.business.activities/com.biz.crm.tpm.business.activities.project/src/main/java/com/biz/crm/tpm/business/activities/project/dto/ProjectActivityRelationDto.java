package com.biz.crm.tpm.business.activities.project.dto;

import com.biz.crm.business.common.sdk.vo.TenantOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ProjectActivityRelationDto", description = "项目活动关联信息dto")
public class ProjectActivityRelationDto extends TenantOpVo {

  @ApiModelProperty("关联的活动编码")
  private String activityCode;

  @ApiModelProperty("费用预算编码")
  private String costBudgetCode;

  @ApiModelProperty("活动大类编码")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  private String costTypeCategoryName;
}
