package com.biz.crm.tpm.business.activities.project.service.internal;

import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityRelation;
import com.biz.crm.tpm.business.activities.project.repository.ProjectActivityRelationRepository;
import com.biz.crm.tpm.business.activities.project.service.ProjectActivityRelationService;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityRelationVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ProjectActivityRelationServiceImpl implements ProjectActivityRelationService {

  @Autowired
  private ProjectActivityRelationRepository projectActivityRelationRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;

  @Override
  public List<ProjectActivityRelationVo> findByActivityCode(String activityCode) {
    if(StringUtils.isBlank(activityCode)){
      return Lists.newArrayList();
    }
    List<ProjectActivityRelation> relations = projectActivityRelationRepository.findByActivityCodeAndTenantCode(activityCode, TenantUtils.getTenantCode());
    if(CollectionUtils.isEmpty(relations)){
      return Lists.newArrayList();
    }

    Collection<ProjectActivityRelationVo> relationVos = nebulaToolkitService.copyCollectionByWhiteList(relations,ProjectActivityRelation.class,ProjectActivityRelationVo.class, HashSet.class, ArrayList.class);
    Set<String> costBudgetCodes = relationVos.stream().map(ProjectActivityRelationVo::getCostBudgetCode).collect(Collectors.toSet());
    List<CostBudgetVo> costBudgetVos = costBudgetVoService.findByCodes(costBudgetCodes);
    if(!CollectionUtils.isEmpty(costBudgetVos)){
      for(ProjectActivityRelationVo relationVo : relationVos){
        CostBudgetVo costBudgetVo = costBudgetVos.stream().filter(e -> StringUtils.equals(e.getCode(),relationVo.getCostBudgetCode())).findFirst().orElse(null);
        if(costBudgetVo != null){
          relationVo.setCostBudgetCode(costBudgetVo.getCode());
          relationVo.setBudgetSubjectCode(costBudgetVo.getBudgetSubjectCode());
          relationVo.setBudgetSubjectName(costBudgetVo.getBudgetSubjectName());
          relationVo.setChannelCode(costBudgetVo.getChannelCode());
          relationVo.setChannelName(costBudgetVo.getChannelName());
          relationVo.setCustomerCode(costBudgetVo.getCustomerCode());
          relationVo.setCustomerName(costBudgetVo.getCustomerName());
          relationVo.setFinalBalance(costBudgetVo.getFinalBalance());
          relationVo.setMonth(costBudgetVo.getMonth());
          relationVo.setOrgCode(costBudgetVo.getOrgCode());
          relationVo.setOrgName(costBudgetVo.getOrgName());
          relationVo.setProductCode(costBudgetVo.getProductCode());
          relationVo.setProductLevelCode(costBudgetVo.getProductLevelCode());
          relationVo.setProductLevelName(costBudgetVo.getProductLevelName());
          relationVo.setProductName(costBudgetVo.getProductName());
          relationVo.setQuarter(costBudgetVo.getQuarter());
          relationVo.setTerminalCode(costBudgetVo.getTerminalCode());
          relationVo.setTerminalName(costBudgetVo.getTerminalName());
          relationVo.setYear(costBudgetVo.getYear());
          relationVo.setType(costBudgetVo.getType());
        }
      }
    }
    return Lists.newArrayList(relationVos);
  }

  @Override
  @Transactional
  public void deleteByActivityCode(String activityCode) {
    Validate.notBlank(activityCode,"项目活动编码不能为空");
    projectActivityRelationRepository.deleteByActivityCodeAndTenantCode(activityCode,TenantUtils.getTenantCode());
  }
}
