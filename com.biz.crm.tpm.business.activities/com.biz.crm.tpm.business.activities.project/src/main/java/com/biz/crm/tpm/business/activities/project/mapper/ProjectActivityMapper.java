package com.biz.crm.tpm.business.activities.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivity;
import com.biz.crm.tpm.business.activities.project.dto.ProjectActivityDto;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityVo;
import org.apache.ibatis.annotations.Param;

/**
 * 项目活动(ProjectActivity)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
public interface ProjectActivityMapper extends BaseMapper<ProjectActivity> {

  /**
   * 分页查询所有数据
   * @param page       分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  Page<ProjectActivityVo> findByConditions(@Param("page") Page<ProjectActivityVo> page, @Param("dto") ProjectActivityDto dto);

}

