package com.biz.crm.tpm.business.activities.project.service.register;

import com.biz.crm.tpm.business.activities.project.constant.ActivitiesConstant;
import com.biz.crm.workflow.sdk.register.ProcessBusinessRegister;
import org.springframework.stereotype.Component;

/**
 * 项目活动工作流注册业务编码
 *
 * <AUTHOR>
 * @date 2022/9/5
 */
@Component
public class ProjectActivityProcessBusinessRegister implements ProcessBusinessRegister {
  @Override
  public String getBusinessCode() {
    return ActivitiesConstant.PROCESS_NAME;
  }

  @Override
  public String getBusinessName() {
    return "项目活动";
  }
}
