package com.biz.crm.tpm.business.activities.project.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.project.dto.ProjectActivityDto;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

public interface ProjectActivityService {

  Page<ProjectActivityVo> findByConditions(Pageable pageable, ProjectActivityDto dto);

  ProjectActivityVo create(JSONObject json);

  ProjectActivityVo update(JSONObject json);

  ProjectActivityVo findByCode(String code);

  ProjectActivityVo findById(String id);

  List<ProjectActivityVo> findByIds(Set<String> ids);

  void delete(Set<String> ids);

  /**
   * 根据活动大类编码，判断是否存在活动正在使用该活动细类
   */
  boolean existByCostTypeCategoryCode(String costTypeCategoryCode);
}
