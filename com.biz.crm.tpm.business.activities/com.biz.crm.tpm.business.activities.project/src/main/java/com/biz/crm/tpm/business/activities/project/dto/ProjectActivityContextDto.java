package com.biz.crm.tpm.business.activities.project.dto;

import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "ProjectActivityContextDto", description = "专用于项目活动与动态表单间的上下文信息传参")
public class ProjectActivityContextDto {

  @ApiModelProperty("最新活动信息")
  private ProjectActivityDto targetActivity;

  @ApiModelProperty("历史活动信息")
  private ProjectActivityVo sourceActivity;

}
