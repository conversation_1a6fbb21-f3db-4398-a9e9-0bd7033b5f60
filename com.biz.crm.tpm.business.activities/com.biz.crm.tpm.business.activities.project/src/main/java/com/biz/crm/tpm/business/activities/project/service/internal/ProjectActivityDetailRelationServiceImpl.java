package com.biz.crm.tpm.business.activities.project.service.internal;


import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityDetailRelation;
import com.biz.crm.tpm.business.activities.project.repository.ProjectActivityDetailRelationRepository;
import com.biz.crm.tpm.business.activities.project.service.ProjectActivityDetailRelationService;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityDetailRelationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import liquibase.pro.packaged.P;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 方案活动关联信息(ProjectActivityDetailRelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-17 10:52:33
 */
@Service("ProjectActivityDetailRelationService")
public class ProjectActivityDetailRelationServiceImpl implements ProjectActivityDetailRelationService {

  @Autowired
  private ProjectActivityDetailRelationRepository projectActivityDetailRelationRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   * @param pageable 分页对象
   * @param ProjectActivityDetailRelation 实体对象
   * @return
   */
  @Override
  public Page<ProjectActivityDetailRelation> findByConditions(Pageable pageable, ProjectActivityDetailRelation ProjectActivityDetailRelation) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(ProjectActivityDetailRelation)) {
      ProjectActivityDetailRelation = new ProjectActivityDetailRelation();
    }
    return this.projectActivityDetailRelationRepository.findByConditions(pageable, ProjectActivityDetailRelation);
  }
  
  /**
   * 通过主键查询单条数据
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public ProjectActivityDetailRelation findById(String id) {
    if (StringUtils.isBlank(id)) {
	  return null;
	}
    return this.projectActivityDetailRelationRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
  }
  
  /**
   * 新增数据
   * @param ProjectActivityDetailRelation 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public ProjectActivityDetailRelation create(ProjectActivityDetailRelation ProjectActivityDetailRelation) {
    this.createValidate(ProjectActivityDetailRelation);
    ProjectActivityDetailRelation.setTenantCode(TenantUtils.getTenantCode());
    this.projectActivityDetailRelationRepository.saveOrUpdate(ProjectActivityDetailRelation);
    return ProjectActivityDetailRelation;
  }
  
  /**
   * 修改新据
   * @param ProjectActivityDetailRelation 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public ProjectActivityDetailRelation update(ProjectActivityDetailRelation ProjectActivityDetailRelation) {
    this.updateValidate(ProjectActivityDetailRelation);
    ProjectActivityDetailRelation.setTenantCode(TenantUtils.getTenantCode());
    this.projectActivityDetailRelationRepository.saveOrUpdate(ProjectActivityDetailRelation);
    return ProjectActivityDetailRelation;
  }
  
  /**
   * 删除数据
   * @param idList 主键结合
   * @return 删除结果
   */
  @Override
  @Transactional
  public void deleteByActivityCode(String activityCode) {
    Validate.notBlank(activityCode,"项目活动编码不能为空");
    projectActivityDetailRelationRepository.deleteByActivityCodeAndTenantCode(activityCode,TenantUtils.getTenantCode());
  }


  /**
   * 活动编码查询活动关联信息
   * @param activityCode 主键结合
   * @return
   */
  @Override
  public List<ProjectActivityDetailRelationVo> findByActivityCode(String activityCode) {
    if(StringUtils.isBlank(activityCode)){
      return Lists.newArrayList();
    }
    List<ProjectActivityDetailRelation> relations = projectActivityDetailRelationRepository.findByActivityCodeAndTenantCode(activityCode, TenantUtils.getTenantCode());
    if(CollectionUtils.isEmpty(relations)){
      return Lists.newArrayList();
    }

    Collection<ProjectActivityDetailRelationVo> relationVos = nebulaToolkitService.copyCollectionByWhiteList(relations,ProjectActivityDetailRelation.class,ProjectActivityDetailRelationVo.class, HashSet.class, ArrayList.class);
    return Lists.newArrayList(relationVos);
  }
  /**
   * 创建验证
   * @param ProjectActivityDetailRelation
   */
  private void createValidate(ProjectActivityDetailRelation ProjectActivityDetailRelation) {
    Validate.notNull(ProjectActivityDetailRelation, "新增时，对象信息不能为空！");
	ProjectActivityDetailRelation.setId(null);
      Validate.notBlank(ProjectActivityDetailRelation.getTenantCode(), "新增数据时，租户编号不能为空！");
    Validate.notBlank(ProjectActivityDetailRelation.getActivityCode(), "新增数据时，关联的活动编码不能为空！");
    Validate.notBlank(ProjectActivityDetailRelation.getCostBudgetCode(), "新增数据时，费用预算编码不能为空！");
    Validate.notBlank(ProjectActivityDetailRelation.getCostTypeDetailCode(), "新增数据时，活动细类编码不能为空！");
    Validate.notBlank(ProjectActivityDetailRelation.getCostTypeDetailName(), "新增数据时，活动细类名称不能为空！");
   
  }
  
   /**
   * 修改验证
   * @param ProjectActivityDetailRelation
   */
  private void updateValidate(ProjectActivityDetailRelation ProjectActivityDetailRelation) {
    Validate.notNull(ProjectActivityDetailRelation, "修改时，对象信息不能为空！");
      Validate.notBlank(ProjectActivityDetailRelation.getId(), "新增数据时，不能为空！");
    Validate.notBlank(ProjectActivityDetailRelation.getTenantCode(), "新增数据时，租户编号不能为空！");
    Validate.notBlank(ProjectActivityDetailRelation.getActivityCode(), "新增数据时，关联的活动编码不能为空！");
    Validate.notBlank(ProjectActivityDetailRelation.getCostBudgetCode(), "新增数据时，费用预算编码不能为空！");
    Validate.notBlank(ProjectActivityDetailRelation.getCostTypeDetailCode(), "新增数据时，活动细类编码不能为空！");
    Validate.notBlank(ProjectActivityDetailRelation.getCostTypeDetailName(), "新增数据时，活动细类名称不能为空！");
    
  }
}

