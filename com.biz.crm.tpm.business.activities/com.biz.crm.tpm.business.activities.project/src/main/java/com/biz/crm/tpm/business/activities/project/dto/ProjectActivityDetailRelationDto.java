package com.biz.crm.tpm.business.activities.project.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.business.common.sdk.vo.TenantOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> rentao
 * @date : 2022/11/17 10:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ProjectActivityDetailRelationDto", description = "方案活动关联信息Dto")
public class ProjectActivityDetailRelationDto extends TenantFlagOpDto {

  @ApiModelProperty("关联的活动编码")
  private String activityCode;

  @ApiModelProperty("费用预算编码")
  private String costBudgetCode;

  @ApiModelProperty("活动细类编码")
  private String costTypeDetailCode;

  @ApiModelProperty("活动细类名称")
  private String costTypeDetailName;

  @ApiModelProperty("申请表单编码")
  private String applyFromCode;

  @ApiModelProperty("申请动态表单关联编码")
  private String applyMappingCode;

}