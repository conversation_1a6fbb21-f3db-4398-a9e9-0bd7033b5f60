package com.biz.crm.tpm.business.activities.project.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * 项目活动附件
 */
@ApiModel(value = "ProjectActivityFiles",description = "项目活动附件")
@TableName("tpm_project_activity_files")
@Getter
@Setter
@Entity(name = "tpm_project_activity_files")
@org.hibernate.annotations.Table(appliesTo = "tpm_project_activity_files", comment = "项目活动附件")
public class ProjectActivityFiles extends FileEntity {

  /** 项目活动编号 */
  @ApiModelProperty(name = "项目活动编号",notes = "项目活动编号")
  @Column(name = "activity_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '项目活动编号 '")
  private String activityCode;
}