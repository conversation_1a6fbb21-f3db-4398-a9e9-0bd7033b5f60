package com.biz.crm.tpm.business.activities.project.repository;


import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityDetailRelation;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityRelation;
import com.biz.crm.tpm.business.activities.project.mapper.ProjectActivityDetailRelationMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;


/**
 * 方案活动关联信息(ProjectActivityDetailRelation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-17 10:52:34
 */
@Component
public class ProjectActivityDetailRelationRepository extends ServiceImpl<ProjectActivityDetailRelationMapper, ProjectActivityDetailRelation> {

  @Autowired
  private ProjectActivityDetailRelationMapper ProjectActivityDetailRelationMapper;

  /**
   * 分页查询数据
   * @param pageable 分页对象
   * @param projectActivityDetailRelation 实体对象
   * @return
   */
  public Page<ProjectActivityDetailRelation> findByConditions(Pageable pageable, ProjectActivityDetailRelation projectActivityDetailRelation) {
    Page<ProjectActivityDetailRelation> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    projectActivityDetailRelation.setTenantCode(TenantUtils.getTenantCode());
    Page<ProjectActivityDetailRelation> pageList = this.ProjectActivityDetailRelationMapper.findByConditions(page, projectActivityDetailRelation);
    return pageList;
  }

  /**
   * 根据关联的活动编码，查询信息
   */
  public List<ProjectActivityDetailRelation> findByActivityCodeAndTenantCode(String activityCode, String tenantCode) {
    return this.lambdaQuery().eq(ProjectActivityDetailRelation::getActivityCode, activityCode).eq(ProjectActivityDetailRelation::getTenantCode, tenantCode).list();
  }

  /**
   * 根据关联的活动编码，删除信息
   */
  public void deleteByActivityCodeAndTenantCode(String activityCode, String tenantCode){
    this.lambdaUpdate().eq(ProjectActivityDetailRelation::getActivityCode,activityCode).eq(ProjectActivityDetailRelation::getTenantCode,tenantCode).remove();
  }

  /**
   * 根据关联的活动编码集合，查询信息
   */
  public List<ProjectActivityDetailRelation> findByActivityCodesAndTenantCode(Set<String> activityCodes, String tenantCode){
    return this.lambdaQuery().in(ProjectActivityDetailRelation::getActivityCode,activityCodes).eq(ProjectActivityDetailRelation::getTenantCode,tenantCode).list();
  }

  public ProjectActivityDetailRelation findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ProjectActivityDetailRelation::getTenantCode,tenantCode)
        .in(ProjectActivityDetailRelation::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCodes(Set<String> needDetailDeletes, String tenantCode) {
    this.lambdaUpdate()
        .eq(ProjectActivityDetailRelation::getTenantCode,tenantCode)
        .in(ProjectActivityDetailRelation::getId,needDetailDeletes)
        .remove();
  }
}

