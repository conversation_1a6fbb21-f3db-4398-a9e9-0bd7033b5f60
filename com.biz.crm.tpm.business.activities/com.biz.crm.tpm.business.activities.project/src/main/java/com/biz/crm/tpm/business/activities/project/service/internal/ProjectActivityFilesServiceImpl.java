package com.biz.crm.tpm.business.activities.project.service.internal;

import com.biz.crm.tpm.business.activities.project.dto.ProjectActivityFilesDto;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityFiles;
import com.biz.crm.tpm.business.activities.project.repository.ProjectActivityFilesRepository;
import com.biz.crm.tpm.business.activities.project.service.ProjectActivityFilesService;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityFilesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;

@Service
public class ProjectActivityFilesServiceImpl implements ProjectActivityFilesService {
  @Autowired
  private ProjectActivityFilesRepository projectActivityFilesRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  @Transactional
  public void save(List<ProjectActivityFilesDto> filesDtos, String activityCode) {
    Validate.notBlank(activityCode,"项目活动编码不能为空");
    if(!CollectionUtils.isEmpty(filesDtos)){
      for(ProjectActivityFilesDto filesDto : filesDtos){
        Validate.notBlank(filesDto.getFileCode(),"项目活动文件唯一识别号不能为空");
      }
      filesDtos.forEach(e -> {
        e.setTenantCode(TenantUtils.getTenantCode());
        e.setId(null);
        e.setActivityCode(activityCode);
      });
    }
    //暴力删除所有数据，再保存附件信息
    projectActivityFilesRepository.deleteByActivityCodeAndTenantCode(activityCode, TenantUtils.getTenantCode());
    if(!CollectionUtils.isEmpty(filesDtos)){
      //保存文件信息
      Collection<ProjectActivityFiles> entities = nebulaToolkitService.copyCollectionByWhiteList(filesDtos,ProjectActivityFilesDto.class,ProjectActivityFiles.class, LinkedHashSet.class, ArrayList.class);
      entities.forEach(e->e.setTenantCode(TenantUtils.getTenantCode()));
      projectActivityFilesRepository.saveBatch(entities);
    }
  }

  @Override
  public List<ProjectActivityFilesVo> findByActivityCode(String activityCode) {
    if(StringUtils.isBlank(activityCode)){
      return Lists.newArrayList();
    }
    List<ProjectActivityFiles> entities = projectActivityFilesRepository.findByActivityCodeAndTenantCode(activityCode,TenantUtils.getTenantCode());
    if(CollectionUtils.isEmpty(entities)){
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities,ProjectActivityFiles.class,ProjectActivityFilesVo.class,LinkedHashSet.class,ArrayList.class));
  }

}
