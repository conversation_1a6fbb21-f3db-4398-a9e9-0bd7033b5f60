package com.biz.crm.tpm.business.activities.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityDetailRelation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 方案活动关联信息(TpmProjectActivityDetailRelation)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-11-17 10:52:34
 */
public interface ProjectActivityDetailRelationMapper extends BaseMapper<ProjectActivityDetailRelation> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页对象
   * @param projectActivityDetailRelation 查询实体
   * @return 所有数据
  */
  public Page<ProjectActivityDetailRelation> findByConditions(@Param("page") Page<ProjectActivityDetailRelation> page, @Param("projectActivityDetailRelation") ProjectActivityDetailRelation projectActivityDetailRelation);
}

