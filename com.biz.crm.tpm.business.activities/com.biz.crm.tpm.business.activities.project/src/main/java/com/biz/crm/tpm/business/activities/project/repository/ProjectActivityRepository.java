package com.biz.crm.tpm.business.activities.project.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.project.dto.ProjectActivityDto;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivity;
import com.biz.crm.tpm.business.activities.project.mapper.ProjectActivityMapper;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityVo;
import com.biz.crm.tpm.business.activities.sdk.enums.ActivityStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 项目活动(ProjectActivity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class ProjectActivityRepository extends ServiceImpl<ProjectActivityMapper, ProjectActivity> {

  @Autowired
  private ProjectActivityMapper projectActivityMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<ProjectActivityVo> findByConditions(Pageable pageable, ProjectActivityDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ProjectActivityVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return projectActivityMapper.findByConditions(page, dto);
  }

  /**
   * 根据编码查询信息
   */
  public ProjectActivity findByCodeAndTenantCode(String code, String tenantCode) {
    return this.lambdaQuery().eq(ProjectActivity::getCode, code).eq(ProjectActivity::getTenantCode, tenantCode).eq(ProjectActivity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
  }

  /**
   * 根据编码集查询信息
   */
  public List<ProjectActivity> findByCodesAndTenantCode(Set<String> codes, String tenantCode) {
    return this.lambdaQuery().in(ProjectActivity::getCode, codes).eq(ProjectActivity::getTenantCode, tenantCode).eq(ProjectActivity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
  }


  /**
   * 根据主键id集合，删除数据
   */
  public void deleteByIds(List<String> ids) {
    this.lambdaUpdate().in(ProjectActivity::getId, ids)
        .eq(ProjectActivity::getTenantCode,TenantUtils.getTenantCode())
        .set(ProjectActivity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }


  /**
   * 根据提供的活动编码，统计信息
   */
  public int countByCodesAndTenantCode(Set<String> codes, String tenantCode) {
    return this.lambdaQuery().in(ProjectActivity::getCode, codes).
        eq(ProjectActivity::getTenantCode, tenantCode).
        eq(ProjectActivity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).
        count();
  }

  /**
   * 根据提供的参数，更新status
   *
   * @param code        主表编码
   * @param isAllClosed 是否全部关闭，true表示全部关闭，false表示部分关闭
   */
  public void updateForClose(String code, Boolean isAllClosed) {
    ActivityStatusEnum status = isAllClosed ? ActivityStatusEnum.ALL_CLOSE : ActivityStatusEnum.PARTIAL_CLOSE;
    this.lambdaUpdate()
        .eq(ProjectActivity::getCode, code)
        .eq(ProjectActivity::getTenantCode,TenantUtils.getTenantCode())
        .set(ProjectActivity::getStatus, status.getCode())
        .update();
  }

  /**
   * 查询待数据刷新的数据，该方法针对定时任务的整体查询
   */
  public List<ProjectActivity> findByRefreshStatusTask() {
    return this.lambdaQuery()
        .notIn(ProjectActivity::getStatus, Sets.newHashSet(ActivityStatusEnum.ALL_CLOSE.getCode(), ActivityStatusEnum.PARTIAL_CLOSE.getCode(), ActivityStatusEnum.ENDED.getCode()))
        .eq(ProjectActivity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProjectActivity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
        .eq(ProjectActivity::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }

  public ProjectActivity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ProjectActivity::getTenantCode,tenantCode)
        .in(ProjectActivity::getId,id)
        .one();
  }

  public List<ProjectActivity> listByIdsAndTenantCode(Set<String> ids, String tenantCode) {
    return this.lambdaQuery()
        .eq(ProjectActivity::getTenantCode,tenantCode)
        .in(ProjectActivity::getId,ids)
        .list();
  }
}

