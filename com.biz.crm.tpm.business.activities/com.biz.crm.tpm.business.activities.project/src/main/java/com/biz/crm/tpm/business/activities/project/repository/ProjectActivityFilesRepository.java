package com.biz.crm.tpm.business.activities.project.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityFiles;
import com.biz.crm.tpm.business.activities.project.mapper.ProjectActivityFilesMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 项目活动附件(ProjectActivityFiles)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class ProjectActivityFilesRepository extends ServiceImpl<ProjectActivityFilesMapper, ProjectActivityFiles> {

  public List<ProjectActivityFiles> findByActivityCodeAndTenantCode(String activityCode, String tenantCode){
    return this.lambdaQuery().eq(ProjectActivityFiles::getActivityCode,activityCode).eq(ProjectActivityFiles::getTenantCode,tenantCode).list();
  }

  public void deleteByActivityCodeAndTenantCode(String activityCode, String tenantCode){
    this.lambdaUpdate().eq(ProjectActivityFiles::getActivityCode,activityCode).eq(ProjectActivityFiles::getTenantCode,tenantCode).remove();
  }
}

