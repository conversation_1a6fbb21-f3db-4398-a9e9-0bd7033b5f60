<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.project.mapper.ProjectActivityDetailRelationMapper">

  <resultMap type="com.biz.crm.tpm.business.activities.project.entity.ProjectActivityDetailRelation" id="projectActivityDetailRelationMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="createAccount" column="create_account" jdbcType="VARCHAR"/>
    <result property="createName" column="create_name" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="modifyAccount" column="modify_account" jdbcType="VARCHAR"/>
    <result property="modifyName" column="modify_name" jdbcType="VARCHAR"/>
    <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="activityCode" column="activity_code" jdbcType="VARCHAR"/>
    <result property="applyFromCode" column="apply_from_code" jdbcType="VARCHAR"/>
    <result property="applyMappingCode" column="apply_mapping_code" jdbcType="VARCHAR"/>
    <result property="costBudgetCode" column="cost_budget_code" jdbcType="VARCHAR"/>
    <result property="costTypeDetailCode" column="cost_type_detail_code" jdbcType="VARCHAR"/>
    <result property="costTypeDetailName" column="cost_type_detail_name" jdbcType="VARCHAR"/>
  </resultMap>
  
  <sql id = "projectActivityDetailRelationMap">
    create_account createAccount,
    create_name createName,
    create_time createTime,
    modify_account modifyAccount,
    modify_name modifyName,
    modify_time modifyTime,
    tenant_code tenantCode,
    activity_code activityCode,
    apply_from_code applyFromCode,
    apply_mapping_code applyMappingCode,
    cost_budget_code costBudgetCode,
    cost_type_detail_code costTypeDetailCode,
    cost_type_detail_name costTypeDetailName,
    id id
  </sql>

  <select id="findByConditions" resultMap="projectActivityDetailRelationMap">
    select
      *
    from tpm_project_activity_detail_relation
    where
    tenant_code=#{projectActivityDetailRelation.tenantCode}
  </select>
</mapper>

