package com.biz.crm.tpm.business.activities.project.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityDetailRelation;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ProjectActivityVo", description = "项目活动vo")
public class ProjectActivityVo extends UuidFlagOpVo {

  private static final long serialVersionUID = -2240712106733433253L;
  @ApiModelProperty("租户编码")
  private String tenantCode;

  @ApiModelProperty("活动编码")
  private String code;

  @ApiModelProperty("活动名称")
  private String name;

  @ApiModelProperty("活动开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  @ApiModelProperty("活动结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty("总申请金额")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("活动状态")
  private String status;

  @ApiModelProperty("关联信息")
  private List<ProjectActivityRelationVo> relations;

  @ApiModelProperty("关联信息")
  private List<ProjectActivityDetailRelationVo> detailRelations;

  @ApiModelProperty(value = "活动附件信息")
  private List<ProjectActivityFilesVo> activityFiles;

  @ApiModelProperty("动态明细信息")
  private Map<String, List<BaseActivityItemVo>> items;
}
