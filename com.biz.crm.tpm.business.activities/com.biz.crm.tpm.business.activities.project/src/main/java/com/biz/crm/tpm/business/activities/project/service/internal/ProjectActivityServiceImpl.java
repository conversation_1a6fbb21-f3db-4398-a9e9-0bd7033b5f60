package com.biz.crm.tpm.business.activities.project.service.internal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.common.form.sdk.DynamicFormService;
import com.biz.crm.common.form.sdk.context.DynamicFormContext;
import com.biz.crm.common.form.sdk.context.DynamicFormContextHolder;
import com.biz.crm.tpm.business.activities.dynamic.template.enums.FormTypeEnum;
import com.biz.crm.tpm.business.activities.dynamic.template.service.DynamicFormServiceResolver;
import com.biz.crm.tpm.business.activities.project.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.project.dto.*;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivity;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityDetailRelation;
import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityRelation;
import com.biz.crm.tpm.business.activities.project.event.ProjectActivityLogEventListener;
import com.biz.crm.tpm.business.activities.project.repository.ProjectActivityDetailRelationRepository;
import com.biz.crm.tpm.business.activities.project.repository.ProjectActivityRelationRepository;
import com.biz.crm.tpm.business.activities.project.repository.ProjectActivityRepository;
import com.biz.crm.tpm.business.activities.project.service.ProjectActivityDetailRelationService;
import com.biz.crm.tpm.business.activities.project.service.ProjectActivityFilesService;
import com.biz.crm.tpm.business.activities.project.service.ProjectActivityRelationService;
import com.biz.crm.tpm.business.activities.project.service.ProjectActivityService;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityDetailRelationVo;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityFilesVo;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityRelationVo;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityVo;
import com.biz.crm.tpm.business.activities.sdk.enums.ActivityStatusEnum;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.BasicActivitiesInfoService;
import com.biz.crm.tpm.business.activities.sdk.strategy.close.ActivityItemsClosedStrategy;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BasicActivityItemVo;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessMappingDto;
import com.biz.crm.workflow.sdk.service.ProcessBusinessMappingService;
import com.biz.crm.workflow.sdk.service.ProcessBusinessService;
import com.biz.crm.workflow.sdk.vo.ProcessBusinessMappingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant.DYNAMIC_FORM_FIELD_CODE;


@Service("_ProjectActivityServiceImpl")
public class ProjectActivityServiceImpl implements ProjectActivityService, BasicActivitiesInfoService {

  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private ProjectActivityRepository projectActivityRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private DynamicFormServiceResolver dynamicFormServiceResolver;
  @Autowired
  private ProjectActivityRelationRepository projectActivityRelationRepository;
  @Autowired
  private ProjectActivityRelationService projectActivityRelationService;
  @Autowired
  private ProjectActivityDetailRelationRepository projectActivityDetailRelationRepository;
  @Autowired
  private ProjectActivityDetailRelationService projectActivityDetailRelationService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired(required = false)
  private List<ActivitiesEventListener> activitiesEventListeners;
  @Autowired
  private ProjectActivityFilesService projectActivityFilesService;
  @Autowired(required = false)
  private List<ActivityItemsClosedStrategy> activitiesClosedStrategies;
  @Autowired(required = false)
  private ProcessBusinessService processBusinessService;
  @Autowired(required = false)
  private ProcessBusinessMappingService processBusinessMappingService;


  @Override
  public Page<ProjectActivityVo> findByConditions(Pageable pageable, ProjectActivityDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    if (dto == null) {
      dto = new ProjectActivityDto();
    }
    if (StringUtils.isBlank(dto.getTenantCode())) {
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    if (StringUtils.isBlank(dto.getDelFlag())) {
      dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    }
    return this.projectActivityRepository.findByConditions(pageable, dto);
  }

  @Override
  @Transactional
  public ProjectActivityVo create(JSONObject json) {
    ProjectActivityDto dto = this.createValidation(json);
    dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    //1. ====保存主表
    ProjectActivity entity = nebulaToolkitService.copyObjectByWhiteList(dto, ProjectActivity.class, HashSet.class, ArrayList.class, "activityFiles", "relations", "attachmentVos");
    entity.setStatus(this.analysisStatus(dto.getStartTime(), dto.getEndTime()).getCode());
    entity.setTenantCode(TenantUtils.getTenantCode());
    projectActivityRepository.save(entity);
    //2. ====保存关联信息
    entity.getRelations().forEach(e -> e.setId(null));
    entity.getRelations().forEach(e->e.setTenantCode(TenantUtils.getTenantCode()));
    projectActivityRelationRepository.saveBatch(entity.getRelations());
    //3. ====保存细类关联信息
    entity.getDetailRelations().forEach(e -> e.setId(null));
    entity.getDetailRelations().forEach(e -> e.setTenantCode(TenantUtils.getTenantCode()));
    projectActivityDetailRelationRepository.saveBatch(entity.getDetailRelations());

    //保存可能的附件信息
    if (!CollectionUtils.isEmpty(dto.getActivityFiles())) {
      projectActivityFilesService.save(dto.getActivityFiles(), entity.getCode());
    }

    //3. ====保存动态模板明细
    String parentCode = entity.getCode();
    Set<DynamicFormService<ProjectActivityVo>> dynamicFormServices = dynamicFormServiceResolver.getDynamicFormServices(json, DYNAMIC_FORM_FIELD_CODE, ProjectActivityVo.class, FormTypeEnum.APPLY);
    Validate.notEmpty(dynamicFormServices, "根据提供的信息，未能获取到匹配的动态模板服务类信息");
    ProjectActivityVo projectActivityVo = nebulaToolkitService.copyObjectByWhiteList(entity, ProjectActivityVo.class, HashSet.class, ArrayList.class, "relations", "attachmentVos", "detailRelations");
    try {
      ProjectActivityContextDto contextDto = this.buildActivityContextDto(null, dto, true);
      DynamicFormContext context = this.prepareDynamicFormContext(contextDto);
      for (DynamicFormService<ProjectActivityVo> dynamicFormService : dynamicFormServices) {
        dynamicFormService.createDynamicDetails(projectActivityVo, parentCode);
      }
      //刷新总金额
      this.validateTotalApplyAmount(context);
    } finally {
      DynamicFormContextHolder.clearContext();
    }
    //工作流
    if (dto.getProcessBusiness() != null) {
      //费用预算占用
      ProjectActivityVo activityVo = this.findByCode(entity.getCode());
      for (Map.Entry<String, List<BaseActivityItemVo>> item : activityVo.getItems().entrySet()) {
        List<BasicActivityItemVo> basicItems = JSONArray.parseArray(JSONArray.toJSONString(item.getValue()), BasicActivityItemVo.class);
        Validate.notEmpty(basicItems, "明细信息不能为空");
        for (BasicActivityItemVo basicItem : basicItems) {
          costBudgetVoService.occupy(parentCode, basicItem.getItemCode(), basicItem.getCostBudgetCode(), basicItem.getApplyAmount(), basicItem.getRemark(), CostBudgetItemSourceType.PROJECT_ACTIVITY.getDescr());
        }
      }
      dto.setCode(entity.getCode());
      dto.setId(entity.getId());
      //提交工作流
      this.commitProcess(dto);
    }
    //新增业务日志
    ProjectActivityLogEventDto logEventDto = new ProjectActivityLogEventDto();
    logEventDto.setOriginal(null);
    logEventDto.setNewest(projectActivityVo);
    SerializableBiConsumer<ProjectActivityLogEventListener, ProjectActivityLogEventDto> onCreate =
            ProjectActivityLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(logEventDto, ProjectActivityLogEventListener.class, onCreate);
    return projectActivityVo;
  }

  @Override
  @Transactional
  public ProjectActivityVo update(JSONObject json) {
    ProjectActivityDto dto = this.updateValidation(json);
    ProjectActivityVo sourceProjectActivityVo = this.findByCode(dto.getCode());
    Validate.notNull(sourceProjectActivityVo, "根据活动编码【%s】，未能获取到相应信息", dto.getCode());
    ProjectActivityContextDto contextDto = this.buildActivityContextDto(sourceProjectActivityVo, dto, false);
    ProjectActivity dbProjectActivity = nebulaToolkitService.copyObjectByWhiteList(sourceProjectActivityVo, ProjectActivity.class, HashSet.class, ArrayList.class);
    dbProjectActivity.setEndTime(dto.getEndTime());
    dbProjectActivity.setName(dto.getName());
    dbProjectActivity.setRemark(dto.getRemark());
    dbProjectActivity.setStartTime(dto.getStartTime());
    dbProjectActivity.setItems(dto.getItems());
    dbProjectActivity.setTotalApplyAmount(dto.getTotalApplyAmount());
    dbProjectActivity.setStatus(this.analysisStatus(dto.getStartTime(), dto.getEndTime()).getCode());
    dbProjectActivity.setTenantCode(TenantUtils.getTenantCode());

    //1.=====保存主表
    projectActivityRepository.saveOrUpdate(dbProjectActivity);

    //2.=====保存关联信息
    List<ProjectActivityRelation> dbProjectActivityRelations = projectActivityRelationRepository.findByActivityCodeAndTenantCode(dbProjectActivity.getCode(), dbProjectActivity.getTenantCode());
    dbProjectActivity.setRelations(dbProjectActivityRelations);
    Set<String> currentIds = dto.getRelations().stream().map(ProjectActivityRelationDto::getId).collect(Collectors.toSet());
    Set<String> dbIds = dbProjectActivityRelations.stream().map(ProjectActivityRelation::getId).collect(Collectors.toSet());
    List<ProjectActivityDetailRelation> dbProjectActivityDetailRelations = projectActivityDetailRelationRepository.findByActivityCodeAndTenantCode(dbProjectActivity.getCode(), dbProjectActivity.getTenantCode());
    dbProjectActivity.setDetailRelations(dbProjectActivityDetailRelations);
    Set<String> currentDetailIds = dto.getDetailRelations().stream().map(ProjectActivityDetailRelationDto::getId).collect(Collectors.toSet());
    Set<String> dbDetailIds = dbProjectActivityDetailRelations.stream().map(ProjectActivityDetailRelation::getId).collect(Collectors.toSet());
    //处理关联信息删除情况
    Set<String> needDeletes = Sets.difference(dbIds, currentIds);
    Set<String> needDetailDeletes = Sets.difference(dbDetailIds, currentDetailIds);
    if (!CollectionUtils.isEmpty(needDeletes)) {
      projectActivityRelationRepository.removeByIdsAndTenantCode(needDeletes,TenantUtils.getTenantCode());
      dbProjectActivity.getRelations().addAll(dbProjectActivityRelations.stream().filter(e -> !needDeletes.contains(e.getId())).collect(Collectors.toList()));
    }
    if (!CollectionUtils.isEmpty(needDetailDeletes)) {
      projectActivityDetailRelationRepository.removeByIdsAndTenantCodes(needDetailDeletes,TenantUtils.getTenantCode());
      dbProjectActivity.getDetailRelations().addAll(dbProjectActivityDetailRelations.stream().filter(e -> !needDetailDeletes.contains(e.getId())).collect(Collectors.toList()));
    }
    //处理关联信息新增情况
    List<ProjectActivityRelationDto> needAdds = dto.getRelations().stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());
    List<ProjectActivityDetailRelationDto> needDetailAdds = dto.getDetailRelations().stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(needAdds)) {
      List<ProjectActivityRelation> relations = Lists.newArrayList();
      for (ProjectActivityRelationDto e : needAdds) {
        e.setId(null);
        e.setActivityCode(dbProjectActivity.getCode());
        e.setTenantCode(dbProjectActivity.getTenantCode());
        ProjectActivityRelation relation = nebulaToolkitService.copyObjectByWhiteList(e, ProjectActivityRelation.class, HashSet.class, ArrayList.class);
        relation.setTenantCode(TenantUtils.getTenantCode());
        projectActivityRelationRepository.save(relation);
        relations.add(relation);
      }
      dbProjectActivity.getRelations().addAll(relations);
    }
    if (!CollectionUtils.isEmpty(needDetailAdds)) {
      List<ProjectActivityDetailRelation> relations = Lists.newArrayList();
      for (ProjectActivityDetailRelationDto e : needDetailAdds) {
        e.setId(null);
        e.setActivityCode(dbProjectActivity.getCode());
        e.setTenantCode(dbProjectActivity.getTenantCode());
        ProjectActivityDetailRelation relation = nebulaToolkitService.copyObjectByWhiteList(e, ProjectActivityDetailRelation.class, HashSet.class, ArrayList.class);
        relation.setTenantCode(TenantUtils.getTenantCode());
        projectActivityDetailRelationRepository.save(relation);
        relations.add(relation);
      }
      dbProjectActivity.getDetailRelations().addAll(relations);
    }
    //保存可能的附件信息
    if (!CollectionUtils.isEmpty(dto.getActivityFiles())) {
      projectActivityFilesService.save(dto.getActivityFiles(), dbProjectActivity.getCode());
    }

    //3.=====保存动态模板明细
    ProjectActivityVo projectActivityVo = this.processDynamicFormsForUpdate(dbProjectActivity, contextDto);

    //工作流
    if (dto.getProcessBusiness() != null) {
      //费用预算占用
      ProjectActivityVo activityVo = this.findByCode(dbProjectActivity.getCode());
      for (Map.Entry<String, List<BaseActivityItemVo>> item : activityVo.getItems().entrySet()) {
        List<BasicActivityItemVo> basicActivityItemVos = JSONArray.parseArray(JSONArray.toJSONString(item.getValue()), BasicActivityItemVo.class);
        Validate.notEmpty(basicActivityItemVos, "明细信息不能为空");
        for (BasicActivityItemVo basicItem : basicActivityItemVos) {
          costBudgetVoService.occupy(dbProjectActivity.getCode(), basicItem.getItemCode(), basicItem.getCostBudgetCode(), basicItem.getApplyAmount(), basicItem.getRemark(), CostBudgetItemSourceType.PROJECT_ACTIVITY.getDescr());
        }
      }
      dto.setCode(dbProjectActivity.getCode());
      dto.setId(dbProjectActivity.getId());
      //提交工作流
      this.commitProcess(dto);
    }
    //编辑业务日志
    ProjectActivityLogEventDto logEventDto = new ProjectActivityLogEventDto();
    logEventDto.setOriginal(sourceProjectActivityVo);
    logEventDto.setNewest(projectActivityVo);
    SerializableBiConsumer<ProjectActivityLogEventListener, ProjectActivityLogEventDto> onUpdate =
            ProjectActivityLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(logEventDto, ProjectActivityLogEventListener.class, onUpdate);

    return projectActivityVo;
  }

  @Override
  public ProjectActivityVo findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    //1.====查询主表
    ProjectActivity projectActivity = projectActivityRepository.findByCodeAndTenantCode(code, TenantUtils.getTenantCode());
    if (projectActivity == null || StringUtils.isBlank(projectActivity.getCode())) {
      return null;
    }
    //查询活动附件
    List<ProjectActivityFilesVo> projectActivityFilesVos = projectActivityFilesService.findByActivityCode(code);

    ProjectActivityVo projectActivityVo = nebulaToolkitService.copyObjectByWhiteList(projectActivity, ProjectActivityVo.class, HashSet.class, ArrayList.class);
    //2.====查询关联信息
    List<ProjectActivityRelationVo> projectActivityRelations = projectActivityRelationService.findByActivityCode(projectActivity.getCode());
    if (CollectionUtils.isEmpty(projectActivityRelations)) {
      return null;
    }

    List<ProjectActivityDetailRelationVo> projectDetailActivityRelations = projectActivityDetailRelationService.findByActivityCode(projectActivity.getCode());
    if (CollectionUtils.isEmpty(projectDetailActivityRelations)) {
      return null;
    }
    projectActivityVo.setRelations(projectActivityRelations);
    projectActivityVo.setDetailRelations(projectDetailActivityRelations);
    projectActivityVo.setActivityFiles(projectActivityFilesVos);


    //3.====查询动态模板明细
    Set<String> dynamicKeys = projectActivityRelations.stream().filter(e -> StringUtils.isNotBlank(e.getCostTypeCategoryCode())).map(e -> StringUtils.joinWith(":", e.getCostBudgetCode(), e.getCostTypeCategoryCode())).collect(Collectors.toSet());
    for (String dynamicKey : dynamicKeys) {
      DynamicFormService<ProjectActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ProjectActivityVo.class,FormTypeEnum.APPLY);
      if (dynamicFormService == null) {
        continue;
      }
      dynamicFormService.perfectDynamicDetails(projectActivityVo, projectActivityVo.getCode());
    }
    return projectActivityVo;
  }

  @Override
  public ProjectActivityVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    //1.====查询主表
    ProjectActivity projectActivity = projectActivityRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (projectActivity == null) {
      return null;
    }
    //查询活动附件
    List<ProjectActivityFilesVo> projectActivityFilesVos = projectActivityFilesService.findByActivityCode(projectActivity.getCode());

    ProjectActivityVo projectActivityVo = nebulaToolkitService.copyObjectByWhiteList(projectActivity, ProjectActivityVo.class, HashSet.class, ArrayList.class);
    //2.====查询关联信息
    List<ProjectActivityRelationVo> projectActivityRelations = projectActivityRelationService.findByActivityCode(projectActivity.getCode());
    if (CollectionUtils.isEmpty(projectActivityRelations)) {
      return null;
    }
    List<ProjectActivityDetailRelationVo> projectDetailActivityRelations = projectActivityDetailRelationService.findByActivityCode(projectActivity.getCode());
    if (CollectionUtils.isEmpty(projectDetailActivityRelations)) {
      return null;
    }
    projectActivityVo.setRelations(projectActivityRelations);
    projectActivityVo.setDetailRelations(projectDetailActivityRelations);
    projectActivityVo.setActivityFiles(projectActivityFilesVos);

    //3.====查询动态模板明细
    Set<String> dynamicKeys = projectActivityRelations.stream().filter(e -> StringUtils.isNotBlank(e.getCostTypeCategoryCode())).map(e -> StringUtils.joinWith(":", e.getCostBudgetCode(), e.getCostTypeCategoryCode())).collect(Collectors.toSet());
    for (String dynamicKey : dynamicKeys) {
      DynamicFormService<ProjectActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ProjectActivityVo.class,FormTypeEnum.APPLY);
      if (dynamicFormService == null) {
        continue;
      }
      dynamicFormService.perfectDynamicDetails(projectActivityVo, projectActivityVo.getCode());
    }
    return projectActivityVo;
  }


  @Override
  public List<ProjectActivityVo> findByIds(Set<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<ProjectActivity> projectActivities = projectActivityRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(projectActivities)) {
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(projectActivities, ProjectActivity.class, ProjectActivityVo.class, HashSet.class, ArrayList.class));
  }

  @Override
  @Transactional
  public void delete(Set<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "主键集合不能为空！");
    List<ProjectActivityVo> projectActivityVos = this.findByIds(ids);
    Validate.notEmpty(projectActivityVos, "根据提供的主键集合信息，未能获取到相应数据");
    //执行删除项目活动（主表逻辑删除，明细项数据真删除）
    List<String> codes = projectActivityVos.stream().map(ProjectActivityVo::getCode).collect(Collectors.toList());
    List<ProcessBusinessMappingVo> processBusinessMappingVoList = this.findProcessBusinessMappingVo(codes);
    if (CollectionUtils.isEmpty(processBusinessMappingVoList)) {
      // 检查删除数据的审核状态，只有待审核数据可以删除
      Map<String, String> map = projectActivityVos.stream().collect(Collectors.toMap(ProjectActivityVo::getCode, ProjectActivityVo::getName));
      processBusinessMappingVoList.forEach(item -> {
        throw new RuntimeException("【" + map.get(item.getBusinessNo()) + "】【" + item.getBusinessNo() + "】不是待提交状态，不能进行删除操作");
      });
    }
    for (ProjectActivityVo projectActivityVo : projectActivityVos) {
      //获取关联信息
      List<ProjectActivityRelationVo> projectActivityRelations = projectActivityRelationService.findByActivityCode(projectActivityVo.getCode());
      List<ProjectActivityDetailRelationVo> projectDetailActivityRelations = projectActivityDetailRelationService.findByActivityCode(projectActivityVo.getCode());
      Validate.notEmpty(projectActivityRelations, "【%s】【%s】数据异常，请联系管理员", projectActivityVo.getName(), projectActivityVo.getCode());
      projectActivityVo.setRelations(projectActivityRelations);
      projectActivityVo.setDetailRelations(projectDetailActivityRelations);
      //暴力删除关联信息
      projectActivityRelationService.deleteByActivityCode(projectActivityVo.getCode());
      projectActivityDetailRelationService.deleteByActivityCode(projectActivityVo.getCode());
      //暴力删除可能的附件信息
      projectActivityFilesService.save(null, projectActivityVo.getCode());
      Set<String> dynamicKeys = projectActivityRelations.stream().filter(e -> StringUtils.isNotBlank(e.getCostTypeCategoryCode())).map(e -> StringUtils.joinWith(":", e.getCostBudgetCode(), e.getCostTypeCategoryCode())).collect(Collectors.toSet());
      for (String dynamicKey : dynamicKeys) {
        DynamicFormService<ProjectActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ProjectActivityVo.class,FormTypeEnum.APPLY);
        Validate.notNull(dynamicFormService, "【%s】【%s】数据异常，请联系管理员", projectActivityVo.getName(), projectActivityVo.getCode());
        dynamicFormService.deleteDynamicDetails(projectActivityVo.getCode());
      }
    }
    projectActivityRepository.deleteByIds(Lists.newArrayList(ids));
    //删除业务日志
    SerializableBiConsumer<ProjectActivityLogEventListener, ProjectActivityLogEventDto> onDelete =
            ProjectActivityLogEventListener::onDelete;
    for (ProjectActivityVo projectActivityVo : projectActivityVos) {
      ProjectActivityLogEventDto logEventDto = new ProjectActivityLogEventDto();
      logEventDto.setOriginal(projectActivityVo);
      this.nebulaNetEventClient.publish(logEventDto, ProjectActivityLogEventListener.class, onDelete);
    }
  }

  private ProjectActivityDto createValidation(JSONObject json) {
    Validate.notNull(json, "项目活动信息不能为空");
    ProjectActivityDto dto = JSONObject.parseObject(JSONObject.toJSONString(json), ProjectActivityDto.class);
    this.createValidation(dto);
    return dto;
  }


  private void createValidation(ProjectActivityDto dto) {
    Validate.notNull(dto, "项目活动信息不能为空");
    if (StringUtils.isBlank(dto.getTenantCode())) {
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    this.validateBase(dto);
    // redis生成项目活动编码编码，编码规则为DEHD+年月日+5位顺序数。每天都从00001开始
    List<String> codeList = this.generateCodeService.generateCode(ActivitiesConstant.PROJECTACTIVITY_RULE_CODE, 1);
    Validate.notEmpty(codeList, "添加信息时，生成项目活动编码失败！");
    dto.setCode(codeList.get(0));
    String pattern = "^[A-Z]{1}[A-Z0-9]*$";
    Validate.matchesPattern(dto.getCode(), pattern, "编码只能是字母和数字构成，且首字母不能是数字，最终编码都将被大写");
    ProjectActivityVo projectActivityVo = this.findByCode(dto.getCode());
    Validate.isTrue(projectActivityVo == null, "项目活动编码重复");
    for (ProjectActivityRelationDto relation : dto.getRelations()) {
      relation.setActivityCode(dto.getCode());
      relation.setTenantCode(dto.getTenantCode());
      Validate.notBlank(relation.getCostBudgetCode(), "关联的费用预算编码不能为空");
      if (StringUtils.isNotBlank(relation.getCostTypeCategoryCode()) || StringUtils.isNotBlank(relation.getCostTypeCategoryName())) {
        Validate.notBlank(relation.getCostTypeCategoryCode(), "关联的活动大类编码不能为空");
        Validate.notBlank(relation.getCostTypeCategoryName(), "关联的活动大类名称不能为空");
      }
    }
    for (ProjectActivityDetailRelationDto relation : dto.getDetailRelations()) {
      relation.setActivityCode(dto.getCode());
      relation.setTenantCode(dto.getTenantCode());
      Validate.notBlank(relation.getCostBudgetCode(), "关联的费用预算编码不能为空");
      if (StringUtils.isNotBlank(relation.getCostTypeDetailCode()) || StringUtils.isNotBlank(relation.getCostTypeDetailName())) {
        Validate.notBlank(relation.getCostTypeDetailCode(), "关联的活动细类编码不能为空");
        Validate.notBlank(relation.getCostTypeDetailName(), "关联的活动细类名称不能为空");
        Validate.notBlank(relation.getApplyFromCode(), "申请表单编码不能为空");
        Validate.notBlank(relation.getApplyMappingCode(), "申请动态表单关联编码不能为空");
      }
    }
  }

  private void validateBase(ProjectActivityDto dto) {
    Validate.notBlank(dto.getName(), "项目活动名称不能为空");
    Validate.notNull(dto.getStartTime(), "活动开始时间不能为空");
    Validate.notNull(dto.getEndTime(), "活动结束时间不能为空");
    Validate.isTrue(NumberUtils.compare(dto.getEndTime().getTime(), dto.getStartTime().getTime()) > 0, "活动结束时间必须大于活动开始时间");
    Validate.notNull(dto.getTotalApplyAmount(), "总申请金额不能为空");
    Validate.notBlank(dto.getTenantCode(), "租户编码不能为空");
    Validate.isTrue(dto.getTotalApplyAmount().compareTo(BigDecimal.ZERO) > 0, "总申请金额必须大于0");
    Validate.notEmpty(dto.getRelations(), "项目活动关联信息不能为空");
    Validate.isTrue(dto.getRelations().stream().anyMatch(e -> StringUtils.isNotBlank(e.getCostTypeCategoryCode())), "关联的活动编码不能全部为空");
    Validate.notEmpty(dto.getItems(), "项目活动明细不能为空");
  }


  private ProjectActivityDto updateValidation(JSONObject json) {
    Validate.notNull(json, "项目活动信息不能为空");
    ProjectActivityDto dto = JSONObject.parseObject(JSONObject.toJSONString(json), ProjectActivityDto.class);
    this.updateValidation(dto);
    return dto;
  }

  private void updateValidation(ProjectActivityDto dto) {
    Validate.notNull(dto, "项目活动信息不能为空");
    Validate.notBlank(dto.getId(), "更新时，主键id必须传入");
    ProjectActivityVo projectActivityVo = this.findById(dto.getId());
    Validate.notNull(projectActivityVo, "根据提供的id主键，未能获取到相应信息");
    Validate.isTrue(StringUtils.equals(dto.getCode(), projectActivityVo.getCode()), "传入的项目活动编码与数据信息不匹配，请检查");
    this.validateBase(dto);
    for (ProjectActivityRelationDto relation : dto.getRelations()) {
      if (StringUtils.isBlank(relation.getActivityCode())) {
        relation.setActivityCode(dto.getCode());
      }
      if (StringUtils.isBlank(relation.getTenantCode())) {
        relation.setTenantCode(dto.getTenantCode());
      }
      Validate.isTrue(relation.getActivityCode().equals(dto.getCode()), "活动编码与费用预算所关联的项目活动编码不一致，请检查");
      Validate.notBlank(relation.getCostBudgetCode(), "关联的费用预算编码不能为空");
      if (StringUtils.isNotBlank(relation.getCostTypeCategoryCode()) || StringUtils.isNotBlank(relation.getCostTypeCategoryName())) {
        Validate.notBlank(relation.getCostTypeCategoryCode(), "关联的活动大类编码不能为空");
        Validate.notBlank(relation.getCostTypeCategoryName(), "关联的活动大类名称不能为空");
      }
      Validate.notBlank(relation.getTenantCode(), "租户编码不能为空");
    }
    for (ProjectActivityDetailRelationDto relation : dto.getDetailRelations()) {
      if (StringUtils.isBlank(relation.getActivityCode())) {
        relation.setActivityCode(dto.getCode());
      }
      if (StringUtils.isBlank(relation.getTenantCode())) {
        relation.setTenantCode(dto.getTenantCode());
      }
      Validate.isTrue(relation.getActivityCode().equals(dto.getCode()), "活动编码与费用预算所关联的项目活动编码不一致，请检查");
      Validate.notBlank(relation.getCostBudgetCode(), "关联的费用预算编码不能为空");
      if (StringUtils.isNotBlank(relation.getCostTypeDetailCode()) || StringUtils.isNotBlank(relation.getCostTypeDetailName())) {
        Validate.notBlank(relation.getCostTypeDetailCode(), "关联的活动细类编码不能为空");
        Validate.notBlank(relation.getCostTypeDetailName(), "关联的活动细类名称不能为空");
        Validate.notBlank(relation.getApplyFromCode(), "申请表单编码不能为空");
        Validate.notBlank(relation.getApplyMappingCode(), "申请动态表单关联编码不能为空");
      }
      Validate.notBlank(relation.getTenantCode(), "租户编码不能为空");
    }
  }

  private ProjectActivityContextDto buildActivityContextDto(ProjectActivityVo sourceActivity, ProjectActivityDto targetActivity, boolean addOperate) {
    ProjectActivityContextDto contextDto = new ProjectActivityContextDto();
    Validate.notNull(targetActivity, "最新活动信息不能为空");
    contextDto.setTargetActivity(targetActivity);
    if (!addOperate) {
      Validate.notNull(sourceActivity, "历史活动信息不能为空");
      contextDto.setSourceActivity(sourceActivity);
    }
    return contextDto;
  }

  private DynamicFormContext prepareDynamicFormContext(ProjectActivityContextDto contextDto) {
    ProjectActivityDto targetActivity = contextDto.getTargetActivity();
    ProjectActivityVo sourceActivity = contextDto.getSourceActivity();
    Validate.notNull(targetActivity.getTotalApplyAmount(), "总申请金额不能为空");
    Validate.isTrue(targetActivity.getTotalApplyAmount().compareTo(BigDecimal.ZERO) > 0, "总申请金额必须大于0");
    Validate.notEmpty(targetActivity.getRelations(), "项目活动关联信息不能为空");
    Validate.notEmpty(targetActivity.getDetailRelations(), "项目活动关联信息不能为空");
    //组装上下文
    DynamicFormContext context = DynamicFormContextHolder.getContext();
    context.put(ActivitiesConstant.TARGET_TOTAL_APPLY_AMOUNT_KEY, targetActivity.getTotalApplyAmount());
    context.put(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY, BigDecimal.ZERO);
    context.put(ActivitiesConstant.ACTIVITY_START_TIME_KEY, targetActivity.getStartTime());
    context.put(ActivitiesConstant.ACTIVITY_END_TIME_KEY, targetActivity.getEndTime());
    if (sourceActivity != null) {
      context.put(ActivitiesConstant.SOURCE_TOTAL_APPLY_AMOUNT_KEY, sourceActivity.getTotalApplyAmount());
    }
    return context;
  }

  /**
   * 该方法在com.biz.tpm.business.activities.local.service.internal.ActivitiesDetailServiceImpl#closed(java.util.Collection)中已做全局锁，
   * 此处就没有做锁了，因为这个方法一般不会被外界指定调用
   */
  @Override
  @Transactional
  public void onClosed(Map<String, Set<String>> codeMap) {
    Validate.notEmpty(codeMap, "活动关闭时，传入的活动编码信息不能为空");
    List<ActivitiesVo> activitiesVos = this.findDetailsByParentCodes(codeMap.keySet());
    if (CollectionUtils.isEmpty(activitiesVos)) {
      return;
    }
    Collection<ProjectActivityVo> projectActivityVos = nebulaToolkitService.copyCollectionByWhiteList(activitiesVos, ActivitiesVo.class, ProjectActivityVo.class, HashSet.class, ArrayList.class);
    for (ProjectActivityVo projectActivityVo : projectActivityVos) {
      Map<String, List<BaseActivityItemVo>> itemMap = projectActivityVo.getItems();
      Set<String> waiteToCloseCodes = codeMap.get(projectActivityVo.getCode());
      Map<String, Boolean> closedStatus = Maps.newHashMap();
      for (Map.Entry<String, List<BaseActivityItemVo>> entry : itemMap.entrySet()) {
        String dynamicFormCode = entry.getValue().get(0).getDynamicFormCode();
        Validate.notBlank(dynamicFormCode, "活动关闭时，项目活动【%s】活动明细对应的动态表单编码不能为空", projectActivityVo.getCode());
        Set<String> itemCodes = entry.getValue().stream().map(BaseActivityItemVo::getItemCode).collect(Collectors.toSet());
        ActivityItemsClosedStrategy strategy = null;
        for (ActivityItemsClosedStrategy activityItemsClosedStrategy : activitiesClosedStrategies) {
          if (activityItemsClosedStrategy.dynamicFormCode().equals(dynamicFormCode)) {
            strategy = activityItemsClosedStrategy;
            break;
          }
        }
        Validate.notNull(strategy, "活动关闭时，根据提供的动态表单编码【%s】没有找到对应的策略信息", dynamicFormCode);
        Set<String> intersections = Sets.intersection(waiteToCloseCodes, itemCodes);
        if (CollectionUtils.isEmpty(intersections)) {
          closedStatus.put(entry.getKey(), strategy.allClosed(projectActivityVo.getCode()));
          continue;
        }
        //明细活动关闭
        strategy.closed(intersections);
        //判断是否全部关闭或部分关闭
        boolean allClosed = strategy.allClosed(projectActivityVo.getCode());
        closedStatus.put(entry.getKey(), allClosed);
      }
      boolean isAllClosed = closedStatus.values().stream().allMatch(e -> e != null && e);
      //更新主表状态
      projectActivityRepository.updateForClose(projectActivityVo.getCode(), isAllClosed);
    }
  }

  /**
   * 该方法在com.biz.tpm.business.activities.local.task.ActivitiesTask#autoRefreshActivityStatusForActivityTime()中已做全局锁，
   * 此处就没有做锁了，因为这个方法一般不会被外界指定调用
   */
  @Override
  @Transactional
  public void refreshActivityStatusForActivityTime() {
    List<ProjectActivity> activities = projectActivityRepository.findByRefreshStatusTask();
    if (CollectionUtils.isEmpty(activities)) {
      return;
    }
    for (ProjectActivity projectActivity : activities) {
      ActivityStatusEnum activityStatusEnum = this.analysisStatus(projectActivity.getStartTime(), projectActivity.getEndTime());
      projectActivity.setStatus(activityStatusEnum.getCode());
      projectActivity.setTenantCode(TenantUtils.getTenantCode());
    }
    projectActivityRepository.saveOrUpdateBatch(activities);
  }

  @Override
  public String activityMark() {
    return ActivitiesConstant.ACTIVITY_MARK;
  }

  @Override
  public Map<String, List<BasicActivityItemVo>> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Maps.newHashMap();
    }
    ProjectActivityVo projectActivityVo = this.findByCode(parentCode);
    if (projectActivityVo == null || CollectionUtils.isEmpty(projectActivityVo.getItems())) {
      return Maps.newHashMap();
    }

    Map<String, List<BasicActivityItemVo>> result = Maps.newHashMap();
    for (Map.Entry<String, List<BaseActivityItemVo>> entry : projectActivityVo.getItems().entrySet()) {
      List<BaseActivityItemVo> itemVos = entry.getValue();
      List<BasicActivityItemVo> items = JSONArray.parseArray(JSONArray.toJSONString(itemVos), BasicActivityItemVo.class);
      result.put(entry.getKey(), items);
    }
    return result;
  }

  @Override
  public ActivitiesVo findDetailsByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    ProjectActivityVo projectActivityVo = this.findByCode(parentCode);
    if (projectActivityVo == null) {
      return null;
    }
    return nebulaToolkitService.copyObjectByWhiteList(projectActivityVo, ActivitiesVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public List<ActivitiesVo> findDetailsByParentCodes(Set<String> parentCodes) {
    if (CollectionUtils.isEmpty(parentCodes)) {
      return Lists.newArrayList();
    }
    List<ProjectActivity> projectActivities = projectActivityRepository.findByCodesAndTenantCode(parentCodes, TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(projectActivities)) {
      return Lists.newArrayList();
    }

    List<ProjectActivityRelation> relationEntities = projectActivityRelationRepository.findByActivityCodesAndTenantCode(projectActivities.stream().map(ProjectActivity::getCode).collect(Collectors.toSet()), TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(relationEntities)) {
      return Lists.newArrayList();
    }
    List<ProjectActivityDetailRelation> relationDetailEntities = projectActivityDetailRelationRepository.findByActivityCodesAndTenantCode(projectActivities.stream().map(ProjectActivity::getCode).collect(Collectors.toSet()), TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(relationDetailEntities)) {
      return Lists.newArrayList();
    }
    Collection<ProjectActivityVo> projectActivityVos = nebulaToolkitService.copyCollectionByWhiteList(projectActivities, ProjectActivity.class, ProjectActivityVo.class, HashSet.class, ArrayList.class);
    Collection<ProjectActivityRelationVo> relationVos = nebulaToolkitService.copyCollectionByWhiteList(relationEntities, ProjectActivityRelation.class, ProjectActivityRelationVo.class, HashSet.class, ArrayList.class);
    Collection<ProjectActivityDetailRelationVo> relationDetailVos = nebulaToolkitService.copyCollectionByWhiteList(relationDetailEntities, ProjectActivityDetailRelation.class, ProjectActivityDetailRelationVo.class, HashSet.class, ArrayList.class);
    for (ProjectActivityVo projectActivityVo : projectActivityVos) {
      List<ProjectActivityRelationVo> relations = relationVos.stream().filter(e -> StringUtils.equals(e.getActivityCode(), projectActivityVo.getCode())).collect(Collectors.toList());
      List<ProjectActivityDetailRelationVo> relatiDetailons = relationDetailVos.stream().filter(e -> StringUtils.equals(e.getActivityCode(), projectActivityVo.getCode())).collect(Collectors.toList());
      if (CollectionUtils.isEmpty(relations) || CollectionUtils.isEmpty(relatiDetailons)) {
        continue;
      }
      projectActivityVo.setRelations(relations);
      projectActivityVo.setDetailRelations(relatiDetailons);
      Set<String> dynamicKeys = relations.stream().filter(e -> StringUtils.isNotBlank(e.getCostTypeCategoryCode())).map(e -> StringUtils.joinWith(":", e.getCostBudgetCode(), e.getCostTypeCategoryCode())).collect(Collectors.toSet());
      for (String dynamicKey : dynamicKeys) {
        DynamicFormService<ProjectActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ProjectActivityVo.class,FormTypeEnum.APPLY);
        if (dynamicFormService == null) {
          continue;
        }
        dynamicFormService.perfectDynamicDetails(projectActivityVo, projectActivityVo.getCode());
      }
    }

    Collection<ActivitiesVo> activitiesVos = nebulaToolkitService.copyCollectionByWhiteList(projectActivityVos, ProjectActivityVo.class, ActivitiesVo.class, HashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesVos);
  }

  @Override
  public BasicActivityItemVo findByParentCodeAndItemCode(String parentCode, String itemCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }

    ProjectActivityVo projectActivityVo = this.findByCode(parentCode);
    if (projectActivityVo == null || CollectionUtils.isEmpty(projectActivityVo.getItems())) {
      return null;
    }

    for (Map.Entry<String, List<BaseActivityItemVo>> entry : projectActivityVo.getItems().entrySet()) {
      List<BaseActivityItemVo> itemVos = entry.getValue();
      List<BasicActivityItemVo> items = JSONArray.parseArray(JSONArray.toJSONString(itemVos), BasicActivityItemVo.class);
      for (BasicActivityItemVo item : items) {
        if (StringUtils.equals(item.getItemCode(), itemCode)) {
          return item;
        }
      }
    }
    return null;
  }

  @Override
  public boolean existByCostTypeCategoryCode(String costTypeCategoryCode) {
    if (StringUtils.isBlank(costTypeCategoryCode)) {
      return false;
    }
    List<ProjectActivityRelation> relations = projectActivityRelationRepository.findByCostTypeCategoryCodeAndTenantCode(costTypeCategoryCode, TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(relations)) {
      return false;
    }
    Set<String> activityCodes = relations.stream().map(ProjectActivityRelation::getActivityCode).collect(Collectors.toSet());
    int result = projectActivityRepository.countByCodesAndTenantCode(activityCodes, TenantUtils.getTenantCode());
    return result > 0;
  }

  private ProjectActivityVo processDynamicFormsForUpdate(ProjectActivity entity, ProjectActivityContextDto contextDto) {
    String parentCode = entity.getCode();
    Map<String, List<BasicActivityItemVo>> dbBasicItems = this.findByParentCode(parentCode);
    Validate.notEmpty(dbBasicItems, "根据项目活动编码【%s】，未能获取到相应明细信息", parentCode);
    Set<String> dbDynamicKeys = Sets.newHashSet(dbBasicItems.keySet());
    Set<String> dynamicKeys = Sets.newHashSet(contextDto.getTargetActivity().getItems().keySet());
    ProjectActivityVo projectActivityVo = nebulaToolkitService.copyObjectByWhiteList(contextDto.getTargetActivity(), ProjectActivityVo.class, HashSet.class, ArrayList.class, "relations", "attachmentVos", "shareInfos");
    try {
      DynamicFormContext context = this.prepareDynamicFormContext(contextDto);
      //处理可能的删除情况（对整个动态表单的明细信息进行删除）
      Set<String> needDeleteDynamicForms = Sets.difference(dbDynamicKeys, dynamicKeys);
      if (!CollectionUtils.isEmpty(needDeleteDynamicForms)) {
        for (String dynamicKey : needDeleteDynamicForms) {
          DynamicFormService<ProjectActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ProjectActivityVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息", dynamicKey);
          dynamicFormService.deleteDynamicDetails(parentCode);
        }
      }
      //处理可能的新增（对某个动态表单的明细新增）
      Set<String> needAddDynamicKeys = Sets.difference(dynamicKeys, dbDynamicKeys);
      if (!CollectionUtils.isEmpty(needAddDynamicKeys)) {
        for (String dynamicKey : needAddDynamicKeys) {
          DynamicFormService<ProjectActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ProjectActivityVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息", dynamicKey);
          dynamicFormService.createDynamicDetails(projectActivityVo, parentCode);
        }
      }

      //处理可能的更新
      Set<String> needUpdateDynamicKeys = Sets.intersection(dynamicKeys, dbDynamicKeys);
      if (!CollectionUtils.isEmpty(needUpdateDynamicKeys)) {
        for (String dynamicKey : needUpdateDynamicKeys) {
          DynamicFormService<ProjectActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ProjectActivityVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息", dynamicKey);
          dynamicFormService.modifyDynamicDetails(projectActivityVo, parentCode);
        }
      }
      //刷新总金额
      this.validateTotalApplyAmount(context);
    } finally {
      DynamicFormContextHolder.clearContext();
    }
    return projectActivityVo;
  }

  private void validateTotalApplyAmount(DynamicFormContext context) {
    //处理最终的上下文
    if (context.exist(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY) && context.exist(ActivitiesConstant.TARGET_TOTAL_APPLY_AMOUNT_KEY)) {
      BigDecimal targetTotalApplyAmount = (BigDecimal) context.get(ActivitiesConstant.TARGET_TOTAL_APPLY_AMOUNT_KEY);
      BigDecimal sumTotalApplyAmount = (BigDecimal) context.get(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY);
      Validate.isTrue(targetTotalApplyAmount.compareTo(sumTotalApplyAmount) == 0, "累计总金额与申请的总金额不一致，请检查");
      Validate.isTrue(targetTotalApplyAmount.compareTo(BigDecimal.ZERO) > 0, "总申请金额必须大于0");
    }
  }

  /**
   * 提交工作流进行审批，提交成功返回流程实例ID，提交失败则抛出异常
   */
  private void commitProcess(ProjectActivityDto dto) {
    ProcessBusinessDto processBusiness = dto.getProcessBusiness();
    Validate.notNull(processBusiness, "提交工作流时，未传工作流对象信息!");
    processBusiness.setBusinessNo(dto.getCode());
    processBusiness.setBusinessFormJson(JsonUtils.obj2JsonString(dto));
    processBusiness.setBusinessCode(ActivitiesConstant.PROCESS_NAME);
    this.processBusinessService.processStart(processBusiness);
  }

  /**
   * 根据业务编码查询工作流业务流程关联信息
   *
   * @param businessNos
   */
  private List<ProcessBusinessMappingVo> findProcessBusinessMappingVo(List<String> businessNos) {
    ProcessBusinessMappingDto processBusinessMappingDto = new ProcessBusinessMappingDto();
    processBusinessMappingDto.setBusinessNos(businessNos);
    processBusinessMappingDto.setBusinessCode(ActivitiesConstant.PROCESS_NAME);
    return processBusinessMappingService.findMultiByByConditions(processBusinessMappingDto);
  }

  private ActivityStatusEnum analysisStatus(Date startTime, Date endTime) {
    Date now = new Date();
    if (NumberUtils.compare(now.getTime(), startTime.getTime()) < 0) {
      return ActivityStatusEnum.UNEXECUTED;
    } else if (NumberUtils.compare(now.getTime(), endTime.getTime()) > 0) {
      return ActivityStatusEnum.ENDED;
    }
    return ActivityStatusEnum.EXECUTING;
  }
}
