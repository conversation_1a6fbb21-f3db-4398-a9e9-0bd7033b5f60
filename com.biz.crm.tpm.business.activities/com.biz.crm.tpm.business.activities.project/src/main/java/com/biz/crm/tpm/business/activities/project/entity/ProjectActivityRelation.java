package com.biz.crm.tpm.business.activities.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_project_activity_relation")
@Table(name = "tpm_project_activity_relation", indexes = {@Index(name = "tpm_project_activity_relation_index1", columnList = "activity_code,cost_budget_code,cost_type_category_code",unique = true)})
@ApiModel(value = "ProjectActivityRelation", description = "项目活动关联信息")
@org.hibernate.annotations.Table(appliesTo = "tpm_project_activity_relation", comment = "项目活动关联信息")
public class ProjectActivityRelation extends TenantOpEntity {

  @ApiModelProperty("关联的活动编码")
  @TableField(value = "activity_code")
  @Column(name = "activity_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '关联的活动编码'")
  private String activityCode;

  @ApiModelProperty("费用预算编码")
  @TableField(value = "cost_budget_code")
  @Column(name = "cost_budget_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '费用预算编码'")
  private String costBudgetCode;

  @ApiModelProperty("活动大类编码")
  @TableField(value = "cost_type_category_code")
  @Column(name = "cost_type_category_code", length = 64, columnDefinition = "varchar(64) COMMENT '活动大类编码'")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  @TableField(value = "cost_type_category_name")
  @Column(name = "cost_type_category_name", columnDefinition = "varchar(255) COMMENT '活动大类名称'")
  private String costTypeCategoryName;

}
