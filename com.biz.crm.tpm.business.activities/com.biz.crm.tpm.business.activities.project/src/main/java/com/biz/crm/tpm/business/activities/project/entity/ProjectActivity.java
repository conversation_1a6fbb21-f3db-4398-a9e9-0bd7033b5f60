package com.biz.crm.tpm.business.activities.project.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_project_activity")
@Table(name = "tpm_project_activity", indexes = {@Index(name = "tpm_project_activity_index1", columnList = "tenant_code, code", unique = true)})
@ApiModel(value = "ProjectActivity", description = "项目活动")
@org.hibernate.annotations.Table(appliesTo = "tpm_project_activity", comment = "项目活动")
public class ProjectActivity extends TenantFlagOpEntity {

  private static final long serialVersionUID = -2865124561019202953L;
  @ApiModelProperty("活动编码")
  @TableField(value = "code")
  @Column(name = "code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动编码'")
  private String code;

  @ApiModelProperty("活动名称")
  @TableField(value = "name")
  @Column(name = "name", nullable = false, columnDefinition = "varchar(255) COMMENT '活动名称'")
  private String name;

  @ApiModelProperty("活动开始时间")
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "start_time", length = 20, columnDefinition = "datetime COMMENT '开始时间 '")
  private Date startTime;

  @ApiModelProperty("活动结束时间")
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "end_time", length = 20, columnDefinition = "datetime COMMENT '活动结束时间 '")
  private Date endTime;

  @ApiModelProperty("总申请金额")
  @TableField(value = "total_apply_amount")
  @Column(name = "total_apply_amount", nullable = false, columnDefinition = "decimal(20,4) COMMENT '总申请金额'")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("活动状态")
  @TableField(value = "status")
  @Column(name = "status", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动状态'")
  private String status;

  @ApiModelProperty("关联信息")
  @TableField(exist = false)
  @Transient
  private List<ProjectActivityRelation> relations;

  @ApiModelProperty("关联信息")
  @TableField(exist = false)
  @Transient
  private List<ProjectActivityDetailRelation> detailRelations;

  @Transient
  @TableField(exist = false)
  private List<ProjectActivityFiles> activityFiles;

  @Transient
  @TableField(exist = false)
  private Map<String, List<?>> items;
}
