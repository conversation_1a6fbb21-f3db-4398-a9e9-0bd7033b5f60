package com.biz.crm.tpm.business.activities.project.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityDetailRelationVo;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ProjectActivityDto", description = "项目活动信息dto")
public class ProjectActivityDto extends TenantFlagOpDto {

  @ApiModelProperty("活动编码")
  private String code;

  @ApiModelProperty("活动名称")
  private String name;

  @ApiModelProperty("活动开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  @ApiModelProperty("活动结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty("总申请金额")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("活动状态")
  private String status;

  @ApiModelProperty("关联信息")
  private List<ProjectActivityRelationDto> relations;

  @ApiModelProperty("关联信息")
  private List<ProjectActivityDetailRelationDto> detailRelations;

  @ApiModelProperty("项目活动明细信息")
  @Transient
  private Map<String, List<?>> items;

  @ApiModelProperty(value = "活动附件信息")
  private List<ProjectActivityFilesDto> activityFiles;

  @ApiModelProperty("工作流对象")
  private ProcessBusinessDto processBusiness;

}
