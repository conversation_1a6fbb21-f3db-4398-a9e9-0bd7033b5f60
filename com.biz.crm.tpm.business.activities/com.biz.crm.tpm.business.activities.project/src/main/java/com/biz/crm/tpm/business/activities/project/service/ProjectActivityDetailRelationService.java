package com.biz.crm.tpm.business.activities.project.service;


import com.biz.crm.tpm.business.activities.project.entity.ProjectActivityDetailRelation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.project.vo.ProjectActivityDetailRelationVo;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 方案活动关联信息(TpmProjectActivityDetailRelation)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-17 10:52:33
 */
public interface ProjectActivityDetailRelationService {

  /**
   * 分页查询数据
   * @param pageable 分页对象
   * @param tpmProjectActivityDetailRelation 实体对象
   * @return
   */
  Page<ProjectActivityDetailRelation> findByConditions(Pageable pageable, ProjectActivityDetailRelation tpmProjectActivityDetailRelation);
  
   /**
   * 通过主键查询单条数据
   * @param id 主键
   * @return 单条数据
   */
   ProjectActivityDetailRelation findById(String id);
  
   /**
   * 新增数据
   * @param tpmProjectActivityDetailRelation 实体对象
   * @return 新增结果
   */
   ProjectActivityDetailRelation create(ProjectActivityDetailRelation tpmProjectActivityDetailRelation);
  
   /**
   * 修改新据
   * @param tpmProjectActivityDetailRelation 实体对象
   * @return 修改结果
   */
   ProjectActivityDetailRelation update(ProjectActivityDetailRelation tpmProjectActivityDetailRelation);
  
  /**
   * 删除数据
   * @param activityCode 主键结合
   * @return 删除结果
   */
  void deleteByActivityCode(String activityCode);


  /**
   * 活动编码查询活动关联信息
   * @param activityCode 主键结合
   * @return
   */
  List<ProjectActivityDetailRelationVo> findByActivityCode(String activityCode);

}

