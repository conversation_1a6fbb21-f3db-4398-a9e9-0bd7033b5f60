package com.biz.crm.tpm.business.activities.project.event;

import com.biz.crm.tpm.business.activities.project.dto.ProjectActivityLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 项目活动 业务日志监听
 * <AUTHOR> <PERSON>
 * @date : 2022-7-1
 */
public interface ProjectActivityLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(ProjectActivityLogEventDto eventDto);
  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(ProjectActivityLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(ProjectActivityLogEventDto eventDto);
  /**
   * 启用
   *
   * @param eventDto
   */
  void onEnable(ProjectActivityLogEventDto eventDto);
  /**
   * 禁用
   *
   * @param eventDto
   */
  void onDisable(ProjectActivityLogEventDto eventDto);
}
