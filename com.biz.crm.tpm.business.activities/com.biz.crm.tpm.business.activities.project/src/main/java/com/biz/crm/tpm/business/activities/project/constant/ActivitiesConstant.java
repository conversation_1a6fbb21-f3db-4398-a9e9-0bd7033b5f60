package com.biz.crm.tpm.business.activities.project.constant;

public interface ActivitiesConstant {
  /**
   * 基础活动明细-动态表单上下文-活动明细键值key
   */
  String BASIC_ITEMS_KEY = "basicItems";
  /**
   * 项目活动标识
   */
  String ACTIVITY_MARK = "ProjectActivity";
  /**
   * 项目活动编号生成前缀
   * redis生成普通活动编码编码，编码规则为XMHD+年月日+5位顺序数。每天都从00001开始
   */
  String PROJECTACTIVITY_RULE_CODE = "XMHD";

  /**
   * 项目活动-动态表单上下文-总金额键值key
   * source 原总金额
   * target 目标总金额(变更后的总金额)
   * sum 累计总金额(用于过程累计计算)
   */
  String SOURCE_TOTAL_APPLY_AMOUNT_KEY = "sourceTotalApplyAmount";
  String TARGET_TOTAL_APPLY_AMOUNT_KEY = "targetTotalApplyAmount";
  String SUM_TOTAL_APPLY_AMOUNT_KEY = "sumTotalApplyAmount";

  /**
   * 项目活动-动态表单上下文-活动开始时间键值key
   */
  String ACTIVITY_START_TIME_KEY = "startTime";

  /**
   * 项目活动-动态表单上下文-活动结束时间键值key
   */
  String ACTIVITY_END_TIME_KEY = "endTime";

  /**
   * 工作流审批类型常量
   */
  String PROCESS_NAME ="PROJECT_ACTIVITIES_PROCESS" ;
}
