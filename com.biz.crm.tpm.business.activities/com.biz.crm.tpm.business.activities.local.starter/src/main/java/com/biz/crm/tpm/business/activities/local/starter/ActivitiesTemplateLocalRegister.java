package com.biz.crm.tpm.business.activities.local.starter;

import com.biz.crm.tpm.business.activities.local.service.internal.strategy.ApplyActivityTypeStrategy;
import com.biz.crm.tpm.business.activities.local.service.internal.strategy.AuditActivityTypeStrategy;
import com.biz.crm.tpm.business.activities.local.service.internal.strategy.ExecuteActivityTypeStrategy;
import com.biz.crm.tpm.business.activities.sdk.register.ActivitiesTemplateRegister;
import com.biz.crm.tpm.business.activities.sdk.strategy.activityType.ActivityTypeStrategy;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 描述：</br>默认实现的活动配置注册信息
 *
 * <AUTHOR>
 * @date 2022/7/27
 */
@Component
@Deprecated
public class ActivitiesTemplateLocalRegister implements ActivitiesTemplateRegister {

  /**
   * 表单类型策略
   * 默认实现注册：
   * 1、ApplyActivityTypeStrategy-申请
   * 2、ExecuteActivityTypeStrategy-执行
   * 3、AuditActivityTypeStrategy-核销
   *
   *
   * @return
   */
  @Override
  public Collection<Class<? extends ActivityTypeStrategy>> getActivityTypeStrategy() {
    return Sets.newHashSet(ApplyActivityTypeStrategy.class, ExecuteActivityTypeStrategy.class, AuditActivityTypeStrategy.class);
  }

}
