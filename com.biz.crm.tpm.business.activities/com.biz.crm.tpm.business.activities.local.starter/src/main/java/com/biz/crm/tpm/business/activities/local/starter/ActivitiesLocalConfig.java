package com.biz.crm.tpm.business.activities.local.starter;

import com.biz.crm.tpm.business.activities.sdk.service.BasicActivitiesInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.Map;

@Configuration
@EntityScan(basePackages = "com.biz.crm.tpm.business.activities.*")
@ComponentScan(basePackages = {"com.biz.crm.tpm.business.activities.*","com.biz.crm.common.*"})
public class ActivitiesLocalConfig implements CommandLineRunner {

  @Autowired
  private ApplicationContext applicationContext;

  /**
   * 该spring启动任务主要是把不同的主活动表单服务(实现了BasicActivitiesInfoService)
   * 加入到"活动服务映射map"中，实现了这些不同活动到"具有一定统一性"的过渡
   *
   */
  @Override
  public void run(String... args) throws Exception {
    Map<String,BasicActivitiesInfoService> infoServices = applicationContext.getBeansOfType(BasicActivitiesInfoService.class);
    if(CollectionUtils.isEmpty(infoServices)){
      return;
    }

    //此处存入了XXXservice的具体实现类引用
    for(BasicActivitiesInfoService infoService : infoServices.values()){
      BasicActivitiesInfoService.ACTIVITY_SERVICE_MAPPING.put(infoService.activityMark(),infoService);
    }

  }
}
