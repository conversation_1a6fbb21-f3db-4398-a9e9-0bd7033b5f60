package com.biz.crm.tpm.business.activities.simple.audit.service.internal;

import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditDetailInfoEntity;
import com.biz.crm.tpm.business.activities.simple.audit.repository.TpmSimpleAuditDetailInfoRepository;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditDetailInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditDetailInfoVo;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import com.bizunited.nebula.common.service.NebulaToolkitService;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.LinkedHashSet;
import java.util.ArrayList;

import com.google.common.collect.Lists;

/**
 * tpm核销实例明细信息;(tpm_simple_audit_detail_info)表服务实现类
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Service("tpmSimpleAuditDetailInfoService")
public class TpmSimpleAuditDetailInfoServiceImpl implements TpmSimpleAuditDetailInfoService {
  @Autowired
  private TpmSimpleAuditDetailInfoRepository tpmSimpleAuditDetailInfoRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return 分页数据
   */
  @Override
  public Page<TpmSimpleAuditDetailInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditDetailInfoVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmSimpleAuditDetailInfoVo();
    }
    return this.tpmSimpleAuditDetailInfoRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmSimpleAuditDetailInfoVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmSimpleAuditDetailInfoEntity tpmSimpleAuditDetailInfo = this.tpmSimpleAuditDetailInfoRepository.getById(id);
    if (tpmSimpleAuditDetailInfo == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditDetailInfo, TpmSimpleAuditDetailInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmSimpleAuditDetailInfoVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditDetailInfoEntity> tpmSimpleAuditDetailInfos = this.tpmSimpleAuditDetailInfoRepository.findByIds(ids);
    Collection<TpmSimpleAuditDetailInfoVo> tpmSimpleAuditDetailInfoVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditDetailInfos, TpmSimpleAuditDetailInfoEntity.class, TpmSimpleAuditDetailInfoVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmSimpleAuditDetailInfoVos);
  }

  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  @Override
  public List<TpmSimpleAuditDetailInfoVo> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    List<TpmSimpleAuditDetailInfoEntity> tpmSimpleAuditDetailInfos = this.tpmSimpleAuditDetailInfoRepository.findByParentCode(parentCode);
    if (CollectionUtils.isEmpty(tpmSimpleAuditDetailInfos)) {
      return null;
    }
    return (List<TpmSimpleAuditDetailInfoVo>) this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditDetailInfos, TpmSimpleAuditDetailInfoEntity.class, TpmSimpleAuditDetailInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  @Override
  public List<TpmSimpleAuditDetailInfoVo> findByParentCodeAndDynamicKey(String parentCode, String dynamicKey) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    List<TpmSimpleAuditDetailInfoEntity> tpmSimpleAuditDetailInfos = this.tpmSimpleAuditDetailInfoRepository.findByParentCodeAndDynamicKey(parentCode,dynamicKey);
    if (CollectionUtils.isEmpty(tpmSimpleAuditDetailInfos)) {
      return null;
    }
    return (List<TpmSimpleAuditDetailInfoVo>) this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditDetailInfos, TpmSimpleAuditDetailInfoEntity.class, TpmSimpleAuditDetailInfoVo.class, LinkedHashSet.class, ArrayList.class);

  }

  /**
   * 新增数据
   *
   * @param tpmSimpleAuditDetailInfoDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditDetailInfoVo create(TpmSimpleAuditDetailInfoVo tpmSimpleAuditDetailInfoDto) {

    this.createValidate(tpmSimpleAuditDetailInfoDto);
    TpmSimpleAuditDetailInfoEntity tpmSimpleAuditDetailInfo = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditDetailInfoDto, TpmSimpleAuditDetailInfoEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmSimpleAuditDetailInfoRepository.saveOrUpdate(tpmSimpleAuditDetailInfo);
    return tpmSimpleAuditDetailInfoDto;
  }

  /**
   * 批量新增
   *
   * @param tpmSimpleAuditDetailInfoVos 新增实体集合
   * @return 新增结果
   */
  @Transactional
  @Override
  public List<TpmSimpleAuditDetailInfoVo> createBatch(Collection<TpmSimpleAuditDetailInfoVo> tpmSimpleAuditDetailInfoVos) {
    if (CollectionUtils.isEmpty(tpmSimpleAuditDetailInfoVos)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditDetailInfoVo> results = Lists.newArrayList();
    for (TpmSimpleAuditDetailInfoVo tpmSimpleAuditDetailInfoDto : tpmSimpleAuditDetailInfoVos) {
      TpmSimpleAuditDetailInfoVo result = this.create(tpmSimpleAuditDetailInfoDto);
      results.add(result);
    }
    return results;
  }

  /**
   * 修改新据
   *
   * @param tpmSimpleAuditDetailInfoDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditDetailInfoVo update(TpmSimpleAuditDetailInfoVo tpmSimpleAuditDetailInfoDto) {
    this.updateValidate(tpmSimpleAuditDetailInfoDto);
    TpmSimpleAuditDetailInfoEntity tpmSimpleAuditDetailInfo = this.tpmSimpleAuditDetailInfoRepository.getById(tpmSimpleAuditDetailInfoDto.getId());
    Validate.notNull(tpmSimpleAuditDetailInfo, "修改数据不存在，请检查！");
    TpmSimpleAuditDetailInfoEntity newEntity = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditDetailInfoDto, TpmSimpleAuditDetailInfoEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmSimpleAuditDetailInfoRepository.saveOrUpdate(newEntity);
    return tpmSimpleAuditDetailInfoDto;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmSimpleAuditDetailInfoEntity> tpmSimpleAuditDetailInfos = this.tpmSimpleAuditDetailInfoRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(tpmSimpleAuditDetailInfos)) {
      return;
    }
    this.tpmSimpleAuditDetailInfoRepository.removeByIds(ids);
  }

  @Override
  public void deleteByParentCodes(Collection<String> parentCodes) {
    if(CollectionUtils.isEmpty(parentCodes)){
      return;
    }
    this.tpmSimpleAuditDetailInfoRepository.deleteByParentCodes(parentCodes);
  }


  /**
   * 创建验证
   *
   * @param tpmSimpleAuditDetailInfoDto 创建实体
   */
  private void createValidate(TpmSimpleAuditDetailInfoVo tpmSimpleAuditDetailInfoDto) {
    Validate.notNull(tpmSimpleAuditDetailInfoDto, "新增时，对象信息不能为空！");
    Validate.isTrue(tpmSimpleAuditDetailInfoDto.getId() == null, "新增数据时,数据主键不为空!");
  }

  /**
   * 修改验证
   *
   * @param tpmSimpleAuditDetailInfoDto 修改实体
   */
  private void updateValidate(TpmSimpleAuditDetailInfoVo tpmSimpleAuditDetailInfoDto) {
    Validate.notNull(tpmSimpleAuditDetailInfoDto, "修改时，对象信息不能为空！");
    Validate.notBlank(tpmSimpleAuditDetailInfoDto.getId(), "修改数据时，主键不能为空！");
  }

}