package com.biz.crm.tpm.business.activities.simple.audit.model;


import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.field.annotation.Validate;
import com.biz.crm.common.form.sdk.field.validate.NotBlankValidateStrategy;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.BooleanSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleDateSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleDecimalInputWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 示例核销表单关联明细model
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TpmSimpleAuditDetailModel", description = "示例核销表单关联明细model")
public class TpmSimpleAuditDetailModel extends BaseActivityItemVo implements DynamicForm {

  /**
   * ------------------------------   公共字段开始 ------------------------------------
   * /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesCode", notes = "活动编号", value = "活动编号")
  @DynamicField(fieldName = "活动编号", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "activitiesName", notes = "活动名称", value = "活动名称")
  @DynamicField(fieldName = "活动名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String activitiesName;

  /**
   * 活动细类编码
   */
  @ApiModelProperty(name = "costTypeDetailCode", notes = "活动细类编码", value = "活动细类编码")
  @DynamicField(fieldName = "活动细类编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String costTypeDetailCode;

  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "costTypeDetailName", notes = "活动细类名称", value = "活动细类名称")
  @DynamicField(fieldName = "活动细类名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String costTypeDetailName;

  /**
   * 活动大类编码
   */
  @ApiModelProperty(name = "costTypeCategoryCode", notes = "活动大类编码", value = "活动大类编码")
  @DynamicField(fieldName = "活动大类编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String costTypeCategoryCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "costTypeCategoryName", notes = "活动大类名称", value = "活动大类名称")
  @DynamicField(fieldName = "活动大类名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String costTypeCategoryName;

  /**
   * 组织编码
   */
  @ApiModelProperty(name = "orgCode", notes = "组织编码", value = "组织编码")
  @DynamicField(fieldName = "组织编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "orgName", notes = "组织名称", value = "组织名称")
  @DynamicField(fieldName = "组织名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String orgName;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "payBy", notes = "支付方式", value = "支付方式")
  @DynamicField(fieldName = "支付方式", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String payBy;

  /**
   * 支付方式名称
   */
  @ApiModelProperty(name = "payByName", notes = "支付方式名称", value = "支付方式名称")
  @DynamicField(fieldName = "支付方式名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String payByName;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "applyAmount", notes = "申请金额", value = "申请金额")
  @DynamicField(fieldName = "申请金额", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleDecimalInputWidget.class)
  private BigDecimal applyAmount;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "auditedAmount", notes = "已核销金额", value = "已核销金额")
  @DynamicField(fieldName = "已核销金额", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleDecimalInputWidget.class)
  private BigDecimal auditedAmount;

  /**
   * 核销金额
   */
  @ApiModelProperty(name = "auditAmount", notes = "本次核销金额", value = "核销金额")
  @DynamicField(fieldName = "本次核销金额", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleDecimalInputWidget.class)
  private BigDecimal auditAmount;

  /**
   * 活动明细编码
   */
  @ApiModelProperty(name = "activitiesDetailCode", notes = "活动明细编码", value = "活动明细编码")
  @DynamicField(fieldName = "活动明细编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String activitiesDetailCode;

  /**
   * 核销明细编码
   */
  @ApiModelProperty(name = "auditDetailCode", notes = "核销明细编码", value = "核销明细编码")
  @DynamicField(fieldName = "核销明细编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String auditDetailCode;

  /**
   * 费用预算编码
   */
  @ApiModelProperty(name = "costBudgetCode", notes = "费用预算编码", value = "费用预算编码")
  @DynamicField(fieldName = "费用预算编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String costBudgetCode;

  /**
   * 费用预算编码
   */
  @ApiModelProperty(name = "isFullAudit", notes = "是否完全核销", value = "是否完全核销")
  @DynamicField(fieldName = "是否完全核销", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String isFullAudit;

  /**
   * 是否多次核销
   */
  @ApiModelProperty(name = "isMultipleAudit", notes = "是否多次核销", value = "是否多次核销")
  @DynamicField(fieldName = "是否多次核销", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = BooleanSelectWidget.class)
  private String isMultipleAudit;

  /**
   * ------------------------------   公共字段结束 ------------------------------------
   * <p>
   * /**
   * 开始时间
   */
  @ApiModelProperty(name = "beginTime", notes = "开始时间", value = "开始时间")
  @DynamicField(fieldName = "开始时间", controllKey = SimpleDateSelectWidget.class)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @ApiModelProperty(name = "endTime", notes = "结束时间", value = "结束时间")
  @DynamicField(fieldName = "结束时间",required = false, controllKey = SimpleDateSelectWidget.class)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  /**
   * 货补产品层级编码
   */
  @ApiModelProperty(name = "productLevelCode", notes = "货补产品层级编码", value = "货补产品层级编码")
  @DynamicField(fieldName = "货补产品层级编码", required = false,controllKey = SimpleInputWidget.class)
  private String productLevelCode;

  /**
   * 货补产品层级名称
   */
  @ApiModelProperty(name = "productLevelName", notes = "货补产品层级名称", value = "货补产品层级名称")
  @DynamicField(fieldName = "货补产品层级名称",required = false, controllKey = SimpleInputWidget.class)
  private String productLevelName;


  /**
   * 货补产品编码
   */
  @ApiModelProperty(name = "productCode", notes = "货补产品编码", value = "货补产品编码")
  @DynamicField(fieldName = "货补产品编码", required = false,controllKey = SimpleInputWidget.class)
  private String productCode;

  /**
   * 货补产品名称
   */
  @ApiModelProperty(name = "productName", notes = "货补产品名称", value = "货补产品名称")
  @DynamicField(fieldName = "货补产品名称",required = false, controllKey = SimpleInputWidget.class)
  private String productName;

  /**
   * ERP会计科目编码
   */
  @ApiModelProperty(name = "accountingSubjectsCode", notes = "ERP会计科目编码", value = "ERP会计科目编码")
  @DynamicField(fieldName = "ERP会计科目编码",required = false, controllKey = SimpleInputWidget.class)
  private String accountingSubjectsCode;
  /**
   * 预算科目编号
   */
  @ApiModelProperty(name = "budgetSubjectsCode", notes = "预算科目编号", value = "预算科目编号")
  @DynamicField(fieldName = "预算科目编号",required = false, controllKey = SimpleInputWidget.class)
  private String budgetSubjectsCode;

  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "budgetSubjectsName", notes = "预算科目名称", value = "预算科目名称")
  @DynamicField(fieldName = "预算科目名称", required = false,controllKey = SimpleInputWidget.class)
  private String budgetSubjectsName;

  /**
   * 客户编码
   */
  @ApiModelProperty(name = "customerCode", notes = "客户编码", value = "客户编码")
  @DynamicField(fieldName = "客户编码",required = false, controllKey = SimpleDecimalInputWidget.class)
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty(name = "customerCode", notes = "客户名称", value = "客户名称")
  @DynamicField(fieldName = "客户名称",required = false, controllKey = SimpleDecimalInputWidget.class)
  private String customerName;

}
