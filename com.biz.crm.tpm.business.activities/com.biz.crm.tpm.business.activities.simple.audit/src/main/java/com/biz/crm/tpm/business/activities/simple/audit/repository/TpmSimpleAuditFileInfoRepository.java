package com.biz.crm.tpm.business.activities.simple.audit.repository;

import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditFileInfoEntity;
import com.biz.crm.tpm.business.activities.simple.audit.mapper.TpmSimpleAuditFileInfoMapper;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditFileInfoVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.util.Collection;
import java.util.List;
import java.util.Collections;

/**
 * tpm核销实例文件信息;(tpm_simple_audit_file_info)表数据库访问层
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Component
public class TpmSimpleAuditFileInfoRepository extends ServiceImpl<TpmSimpleAuditFileInfoMapper, TpmSimpleAuditFileInfoEntity> {
  @Autowired
  private TpmSimpleAuditFileInfoMapper tpmSimpleAuditFileInfoMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<TpmSimpleAuditFileInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditFileInfoVo dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<TpmSimpleAuditFileInfoVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmSimpleAuditFileInfoMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmSimpleAuditFileInfo>
   */
  public List<TpmSimpleAuditFileInfoEntity> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(TpmSimpleAuditFileInfoEntity::getId, ids)
            .eq(TpmSimpleAuditFileInfoEntity::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据业务编码获取详情集合
   *
   * @param parentCode 业务编码
   * @return List<TpmSimpleAuditFileInfo>
   */
  public List<TpmSimpleAuditFileInfoEntity> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(TpmSimpleAuditFileInfoEntity::getParentCode, parentCode)
            .eq(TpmSimpleAuditFileInfoEntity::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据业务数据编码删除关联附件
   *
   * @param parentCodes 业务数据编码
   */
  public void deleteByParentCodes(Collection<String> parentCodes) {
    if (CollectionUtils.isEmpty(parentCodes)) {
      this.lambdaUpdate()
          .in(TpmSimpleAuditFileInfoEntity::getParentCode, parentCodes)
          .eq(TpmSimpleAuditFileInfoEntity::getTenantCode,TenantUtils.getTenantCode())
          .remove();
    }
  }

  public TpmSimpleAuditFileInfoEntity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(TpmSimpleAuditFileInfoEntity::getTenantCode,tenantCode)
        .in(TpmSimpleAuditFileInfoEntity::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(TpmSimpleAuditFileInfoEntity::getTenantCode,tenantCode)
        .in(TpmSimpleAuditFileInfoEntity::getId,ids)
        .remove();
  }
}