package com.biz.crm.tpm.business.activities.simple.audit.repository;

import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditModelEntity;
import com.biz.crm.tpm.business.activities.simple.audit.mapper.TpmSimpleAuditModelMapper;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditModelVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.util.Collection;
import java.util.List;
import java.util.Collections;

/**
 * tpm核销实例model;(tpm_simple_audit_model)表数据库访问层
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Component
public class TpmSimpleAuditModelRepository extends ServiceImpl<TpmSimpleAuditModelMapper, TpmSimpleAuditModelEntity> {
  @Autowired
  private TpmSimpleAuditModelMapper tpmSimpleAuditModelMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<TpmSimpleAuditModelVo> findByConditions(Pageable pageable, TpmSimpleAuditModelVo dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<TpmSimpleAuditModelVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmSimpleAuditModelMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmSimpleAuditModel>
   */
  public List<TpmSimpleAuditModelEntity> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(TpmSimpleAuditModelEntity::getId, ids)
            .eq(TpmSimpleAuditModelEntity::getTenantCode, tenantCode)
            .list();
  }
  /**
   * 根据业务编码获取详情集合
   *
   * @param parentCode 业务编码
   * @return List<TpmSimpleAuditModel>
   */
  public TpmSimpleAuditModelEntity findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(TpmSimpleAuditModelEntity::getParentCode, parentCode)
            .eq(TpmSimpleAuditModelEntity::getTenantCode, tenantCode)
            .one();
  }

  public TpmSimpleAuditModelEntity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(TpmSimpleAuditModelEntity::getTenantCode,tenantCode)
        .in(TpmSimpleAuditModelEntity::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(TpmSimpleAuditModelEntity::getTenantCode,tenantCode)
        .in(TpmSimpleAuditModelEntity::getId,ids)
        .remove();
  }
}