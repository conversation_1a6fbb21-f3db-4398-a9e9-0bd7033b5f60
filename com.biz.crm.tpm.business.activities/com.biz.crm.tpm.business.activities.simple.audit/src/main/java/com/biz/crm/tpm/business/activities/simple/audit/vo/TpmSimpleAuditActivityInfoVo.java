package com.biz.crm.tpm.business.activities.simple.audit.vo;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Vo：tpm核销实例活动信息;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "TpmSimpleAuditActivityInfo",description = "tpm核销实例活动信息")
@Getter
@Setter
public class TpmSimpleAuditActivityInfoVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  private String parentCode;
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value= "活动编号")
  private String activitiesCode;
  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value= "活动名称")
  private String activitiesName;
  /** 开始时间 */
  @ApiModelProperty(name = "beginTime",notes = "开始时间", value= "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /** 结束时间 */
  @ApiModelProperty(name = "endTime",notes = "结束时间", value= "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /** 创建人账号 */
  @ApiModelProperty(name = "createAccount",notes = "创建人账号", value= "创建人账号")
  private String createAccount;
  /** 创建人名称 */
  @ApiModelProperty(name = "createName",notes = "创建人名称", value= "创建人名称")
  private String createName;
  /** 创建时间 */
  @ApiModelProperty(name = "createTime",notes = "创建时间", value= "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

}