package com.biz.crm.tpm.business.activities.simple.audit.service;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditInvoiceInfoVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * tpm核销实例发票信息;(tpm_simple_audit_invoice_info)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
public interface TpmSimpleAuditInvoiceInfoService{
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmSimpleAuditInvoiceInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditInvoiceInfoVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmSimpleAuditInvoiceInfoVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmSimpleAuditInvoiceInfoVo> findByIds(Collection<String> ids);
  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  List<TpmSimpleAuditInvoiceInfoVo> findByParentCode(String parentCode);
  
  /**
   * 新增数据
   *
   * @param tpmSimpleAuditInvoiceInfoDto 实体对象
   * @return 新增结果
   */
  TpmSimpleAuditInvoiceInfoVo create(TpmSimpleAuditInvoiceInfoVo tpmSimpleAuditInvoiceInfoDto);
  /**
   * 批量新增
   * @param tpmSimpleAuditInvoiceInfoVos 新增实体列表
   * @return 新增结果
   */
  List<TpmSimpleAuditInvoiceInfoVo> createBatch(Collection<TpmSimpleAuditInvoiceInfoVo> tpmSimpleAuditInvoiceInfoVos);
  /**
   * 修改数据
   *
   * @param tpmSimpleAuditInvoiceInfoDto 实体对象
   * @return 修改结果
   */
  TpmSimpleAuditInvoiceInfoVo update(TpmSimpleAuditInvoiceInfoVo tpmSimpleAuditInvoiceInfoDto);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
  /**
   * 根据父级业务编码删除管理活动信息
   * @param parentCodes 父级业务编码
   */
  void deleteByParentCodes(Collection<String> parentCodes);
    
}