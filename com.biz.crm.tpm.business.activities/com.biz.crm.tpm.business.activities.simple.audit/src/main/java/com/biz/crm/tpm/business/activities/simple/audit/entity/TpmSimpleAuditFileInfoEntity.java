package com.biz.crm.tpm.business.activities.simple.audit.entity;

import com.biz.crm.business.common.local.entity.FileEntity;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：tpm核销实例文件信息;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "SimpleAuditFileInfo",description = "tpm核销实例文件信息")
@TableName("tpm_simple_audit_file_info")
@Getter
@Setter
@Entity(name = "tpm_simple_audit_file_info")
@org.hibernate.annotations.Table(appliesTo = "tpm_simple_audit_file_info", comment = "tpm核销实例文件信息")
@Table(name = "tpm_simple_audit_file_info")
public class TpmSimpleAuditFileInfoEntity  extends FileEntity {
  
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  @Column(name = "parent_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '业务编码 '")
  private String parentCode;
  
  /** 活动细类编码 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编码", value= "活动细类编码")
  @Column(name = "cost_type_detail_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动细类编码 '")
  private String costTypeDetailCode;
  
  /** 活动细类名称 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value= "活动细类名称")
  @Column(name = "cost_type_detail_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动细类名称 '")
  private String costTypeDetailName;
  
  /** 要求核销资料编号 */
  @ApiModelProperty(name = "requestCode",notes = "要求核销资料编号", value= "要求核销资料编号")
  @Column(name = "request_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '要求核销资料编号 '")
  private String requestCode;

}