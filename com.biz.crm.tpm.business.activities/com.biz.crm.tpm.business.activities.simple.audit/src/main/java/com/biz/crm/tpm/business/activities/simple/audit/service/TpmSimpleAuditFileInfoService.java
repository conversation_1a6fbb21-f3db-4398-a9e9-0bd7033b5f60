package com.biz.crm.tpm.business.activities.simple.audit.service;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditFileInfoVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * tpm核销实例文件信息;(tpm_simple_audit_file_info)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
public interface TpmSimpleAuditFileInfoService{
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmSimpleAuditFileInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditFileInfoVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmSimpleAuditFileInfoVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmSimpleAuditFileInfoVo> findByIds(Collection<String> ids);
  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  List<TpmSimpleAuditFileInfoVo> findByParentCode(String parentCode);
  
  /**
   * 新增数据
   *
   * @param tpmSimpleAuditFileInfoDto 实体对象
   * @return 新增结果
   */
  TpmSimpleAuditFileInfoVo create(TpmSimpleAuditFileInfoVo tpmSimpleAuditFileInfoDto);
  /**
   * 批量新增
   * @param tpmSimpleAuditFileInfoVos 新增实体集合
   * @return 新增结果
   */
  List<TpmSimpleAuditFileInfoVo> createBatch(Collection<TpmSimpleAuditFileInfoVo> tpmSimpleAuditFileInfoVos);
  /**
   * 修改数据
   *
   * @param tpmSimpleAuditFileInfoDto 实体对象
   * @return 修改结果
   */
  TpmSimpleAuditFileInfoVo update(TpmSimpleAuditFileInfoVo tpmSimpleAuditFileInfoDto);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
  /**
   * 根据父级业务编码删除管理活动信息
   * @param parentCodes 父级业务编码
   */
  void deleteByParentCodes(Collection<String> parentCodes);
    
}