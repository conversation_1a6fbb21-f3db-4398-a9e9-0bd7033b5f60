package com.biz.crm.tpm.business.activities.simple.audit.vo;
import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Vo：tpm核销实例model;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "TpmSimpleAuditModel",description = "tpm核销实例model")
@Getter
@Setter
public class TpmSimpleAuditModelVo extends TenantFlagOpVo {
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 步骤业务编码 */
  @ApiModelProperty(name = "dynamicKey",notes = "步骤业务编码", value= "步骤业务编码")
  private String dynamicKey;
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  private String parentCode;
  /**
   * 管理活动列表
   */
  @ApiModelProperty(name = "activityInfos",notes = "关联活动列表", value= "关联活动列表")
  private List<TpmSimpleAuditActivityInfoVo> activityInfos;
  /**
   * 关联活动细类列表
   */
  @ApiModelProperty(name = "detailInfos",notes = "关联活动细类列表", value= "关联活动细类列表")
  private List<TpmSimpleAuditDetailInfoVo> detailInfos;
  /**
   * 关联文件列表
   */
  @ApiModelProperty(name = "fileInfos",notes = "关联文件列表", value= "关联文件列表")
  private List<TpmSimpleAuditFileInfoVo> fileInfos;
  /**
   * 关联发票列表
   */
  @ApiModelProperty(name = "invoiceInfos",notes = "关联发票列表", value= "关联发票列表")
  private List<TpmSimpleAuditInvoiceInfoVo> invoiceInfos;
}