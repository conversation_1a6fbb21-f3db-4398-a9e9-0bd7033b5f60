package com.biz.crm.tpm.business.activities.simple.audit.service;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditDetailInfoVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * tpm核销实例明细信息;(tpm_simple_audit_detail_info)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
public interface TpmSimpleAuditDetailInfoService{
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmSimpleAuditDetailInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditDetailInfoVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmSimpleAuditDetailInfoVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmSimpleAuditDetailInfoVo> findByIds(Collection<String> ids);
  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  List<TpmSimpleAuditDetailInfoVo> findByParentCode(String parentCode);

  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  List<TpmSimpleAuditDetailInfoVo> findByParentCodeAndDynamicKey(String parentCode,String dynamicKey);
  
  /**
   * 新增数据
   *
   * @param tpmSimpleAuditDetailInfoDto 实体对象
   * @return 新增结果
   */
  TpmSimpleAuditDetailInfoVo create(TpmSimpleAuditDetailInfoVo tpmSimpleAuditDetailInfoDto);
  /**
   * 批量新增
   * @param tpmSimpleAuditDetailInfoVos 新增实体列表
   * @return 新增结果
   */
  List<TpmSimpleAuditDetailInfoVo> createBatch(Collection<TpmSimpleAuditDetailInfoVo> tpmSimpleAuditDetailInfoVos);
  /**
   * 修改数据
   *
   * @param tpmSimpleAuditDetailInfoDto 实体对象
   * @return 修改结果
   */
  TpmSimpleAuditDetailInfoVo update(TpmSimpleAuditDetailInfoVo tpmSimpleAuditDetailInfoDto);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
  /**
   * 根据父级业务编码删除管理活动信息
   * @param parentCodes 父级业务编码
   */
  void deleteByParentCodes(Collection<String> parentCodes);

    
}