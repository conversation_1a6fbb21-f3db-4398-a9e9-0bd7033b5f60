package com.biz.crm.tpm.business.activities.simple.audit.service;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditActivityInfoVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * tpm核销实例活动信息;(tpm_simple_audit_activity_info)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
public interface TpmSimpleAuditActivityInfoService{

  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmSimpleAuditActivityInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditActivityInfoVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmSimpleAuditActivityInfoVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmSimpleAuditActivityInfoVo> findByIds(Collection<String> ids);
  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  List<TpmSimpleAuditActivityInfoVo> findByParentCode(String parentCode);
  
  /**
   * 新增数据
   *
   * @param tpmSimpleAuditActivityInfoDto 实体对象
   * @return 新增结果
   */
  TpmSimpleAuditActivityInfoVo create(TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoDto);
  /**
   * 批量新增
   * @param tpmSimpleAuditActivityInfoVos 新增实体集合
   * @return 新增结果
   */
  List<TpmSimpleAuditActivityInfoVo> createBatch(Collection<TpmSimpleAuditActivityInfoVo> tpmSimpleAuditActivityInfoVos);
  /**
   * 修改数据
   *
   * @param tpmSimpleAuditActivityInfoDto 实体对象
   * @return 修改结果
   */
  TpmSimpleAuditActivityInfoVo update(TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoDto);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);

  /**
   * 根据父级业务编码删除管理活动信息
   * @param parentCodes 父级业务编码
   */
  void deleteByParentCodes(Collection<String> parentCodes);
    
}