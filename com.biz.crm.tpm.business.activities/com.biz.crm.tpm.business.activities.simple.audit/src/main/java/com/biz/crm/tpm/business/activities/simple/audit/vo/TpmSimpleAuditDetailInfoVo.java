package com.biz.crm.tpm.business.activities.simple.audit.vo;

import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Vo：tpm核销实例明细信息;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "TpmSimpleAuditDetailInfo",description = "tpm核销实例明细信息")
@Getter
@Setter
public class TpmSimpleAuditDetailInfoVo extends BaseActivityItemVo {
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  private String parentCode;
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value= "活动编号")
  private String activitiesCode;
  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value= "活动名称")
  private String activitiesName;
  /** 开始时间 */
  @ApiModelProperty(name = "beginTime",notes = "开始时间", value= "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /** 结束时间 */
  @ApiModelProperty(name = "endTime",notes = "结束时间", value= "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /** 创建人账号 */
  @ApiModelProperty(name = "createAccount",notes = "创建人账号", value= "创建人账号")
  private String createAccount;
  /** 创建人名称 */
  @ApiModelProperty(name = "createName",notes = "创建人名称", value= "创建人名称")
  private String createName;
  /** 创建时间 */
  @ApiModelProperty(name = "createTime",notes = "创建时间", value= "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /** 活动细类编码 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编码", value= "活动细类编码")
  private String costTypeDetailCode;
  /** 活动细类名称 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value= "活动细类名称")
  private String costTypeDetailName;
  /** 活动大类编码 */
  @ApiModelProperty(name = "costTypeCategoryCode",notes = "活动大类名称", value= "活动大类名称")
  private String costTypeCategoryCode;
  /** 活动大类名称 */
  @ApiModelProperty(name = "costTypeCategoryName",notes = "活动大类名称", value= "活动大类名称")
  private String costTypeCategoryName;
  /** 组织编码 */
  @ApiModelProperty(name = "orgCode",notes = "组织编码", value= "组织编码")
  private String orgCode;
  /** 组织名称 */
  @ApiModelProperty(name = "orgName",notes = "组织名称", value= "组织名称")
  private String orgName;
  /** 支付方式 */
  @ApiModelProperty(name = "payBy",notes = "支付方式", value= "支付方式")
  private String payBy;
  /** 支付方式名称 */
  @ApiModelProperty(name = "payByName",notes = "支付方式名称", value= "支付方式名称")
  private String payByName;
  /** 申请金额 */
  @ApiModelProperty(name = "applyAmount",notes = "申请金额", value= "申请金额")
  private BigDecimal applyAmount;
  /** 核销金额 */
  @ApiModelProperty(name = "auditAmount",notes = "核销金额", value= "核销金额")
  private BigDecimal auditAmount;
  /** 产品层级编码 */
  @ApiModelProperty(name = "productLevelCode",notes = "产品成绩编码", value= "产品成绩编码")
  private String productLevelCode;
  /** 产品层级名称 */
  @ApiModelProperty(name = "productLevelName",notes = "产品层级名称", value= "产品层级名称")
  private String productLevelName;
  /** 活动明细编码 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value= "活动明细编码")
  private String activitiesDetailCode;
  /** ERP会计科目编码 */
  @ApiModelProperty(name = "accountingSubjectsCode",notes = "ERP会计科目编码", value= "ERP会计科目编码")
  private String accountingSubjectsCode;
  /** 核销明细编码 */
  @ApiModelProperty(name = "auditDetailCode",notes = "核销明细编码", value= "核销明细编码")
  private String auditDetailCode;
  /** 费用预算编码 */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编码", value= "费用预算编码")
  private String costBudgetCode;
  /** 是否多次核销 */
  @ApiModelProperty(name = "isMultipleAudit",notes = "是否多次核销", value= "是否多次核销")
  private String isMultipleAudit;
  /** 预算科目编号 */
  @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编号", value= "预算科目编号")
  private String budgetSubjectsCode;
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value= "预算科目名称")
  private String budgetSubjectsName;

}