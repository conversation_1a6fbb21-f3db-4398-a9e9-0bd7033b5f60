package com.biz.crm.tpm.business.activities.simple.audit.service.internal;

import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditFileInfoEntity;
import com.biz.crm.tpm.business.activities.simple.audit.repository.TpmSimpleAuditFileInfoRepository;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditFileInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditFileInfoVo;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.LinkedHashSet;
import java.util.ArrayList;

import com.google.common.collect.Lists;

/**
 * tpm核销实例文件信息;(tpm_simple_audit_file_info)表服务实现类
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Service("tpmSimpleAuditFileInfoService")
public class TpmSimpleAuditFileInfoServiceImpl implements TpmSimpleAuditFileInfoService {
  @Autowired
  private TpmSimpleAuditFileInfoRepository tpmSimpleAuditFileInfoRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return 分页数据
   */
  @Override
  public Page<TpmSimpleAuditFileInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditFileInfoVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmSimpleAuditFileInfoVo();
    }
    return this.tpmSimpleAuditFileInfoRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmSimpleAuditFileInfoVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmSimpleAuditFileInfoEntity tpmSimpleAuditFileInfo = this.tpmSimpleAuditFileInfoRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (tpmSimpleAuditFileInfo == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditFileInfo, TpmSimpleAuditFileInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmSimpleAuditFileInfoVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditFileInfoEntity> tpmSimpleAuditFileInfos = this.tpmSimpleAuditFileInfoRepository.findByIds(ids);
    return (List<TpmSimpleAuditFileInfoVo>) this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditFileInfos, TpmSimpleAuditFileInfoEntity.class, TpmSimpleAuditFileInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  @Override
  public List<TpmSimpleAuditFileInfoVo> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditFileInfoEntity> tpmSimpleAuditFileInfos = this.tpmSimpleAuditFileInfoRepository.findByParentCode(parentCode);
    return (List<TpmSimpleAuditFileInfoVo>) this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditFileInfos, TpmSimpleAuditFileInfoEntity.class, TpmSimpleAuditFileInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 新增数据
   *
   * @param tpmSimpleAuditFileInfoDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditFileInfoVo create(TpmSimpleAuditFileInfoVo tpmSimpleAuditFileInfoDto) {
    this.createValidate(tpmSimpleAuditFileInfoDto);
    TpmSimpleAuditFileInfoEntity tpmSimpleAuditFileInfo = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditFileInfoDto, TpmSimpleAuditFileInfoEntity.class, LinkedHashSet.class, ArrayList.class);
    tpmSimpleAuditFileInfo.setTenantCode(TenantUtils.getTenantCode());
    this.tpmSimpleAuditFileInfoRepository.saveOrUpdate(tpmSimpleAuditFileInfo);
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditFileInfo, TpmSimpleAuditFileInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 批量新增
   *
   * @param tpmSimpleAuditFileInfoVos 新增实体集合
   * @return 新增结果
   */
  @Transactional
  @Override
  public List<TpmSimpleAuditFileInfoVo> createBatch(Collection<TpmSimpleAuditFileInfoVo> tpmSimpleAuditFileInfoVos) {
    if (CollectionUtils.isEmpty(tpmSimpleAuditFileInfoVos)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditFileInfoVo> results = Lists.newArrayList();
    for (TpmSimpleAuditFileInfoVo tpmSimpleAuditFileInfoDto : tpmSimpleAuditFileInfoVos) {
      TpmSimpleAuditFileInfoVo result = this.create(tpmSimpleAuditFileInfoDto);
      results.add(result);
    }
    return results;
  }

  /**
   * 修改新据
   *
   * @param tpmSimpleAuditFileInfoDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditFileInfoVo update(TpmSimpleAuditFileInfoVo tpmSimpleAuditFileInfoDto) {
    this.updateValidate(tpmSimpleAuditFileInfoDto);
    TpmSimpleAuditFileInfoEntity tpmSimpleAuditFileInfo = this.tpmSimpleAuditFileInfoRepository.findByIdAndTenantCode(tpmSimpleAuditFileInfoDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(tpmSimpleAuditFileInfo, "修改数据不存在，请检查！");
    TpmSimpleAuditFileInfoEntity newEntity = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditFileInfoDto, TpmSimpleAuditFileInfoEntity.class, LinkedHashSet.class, ArrayList.class);
    newEntity.setTenantCode(TenantUtils.getTenantCode());
    this.tpmSimpleAuditFileInfoRepository.saveOrUpdate(newEntity);
    return tpmSimpleAuditFileInfoDto;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmSimpleAuditFileInfoEntity> tpmSimpleAuditFileInfos = this.tpmSimpleAuditFileInfoRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(tpmSimpleAuditFileInfos)) {
      return;
    }
    this.tpmSimpleAuditFileInfoRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  @Override
  public void deleteByParentCodes(Collection<String> parentCodes) {
    if(CollectionUtils.isEmpty(parentCodes)){
      return;
    }
    this.tpmSimpleAuditFileInfoRepository.deleteByParentCodes(parentCodes);
  }

  /**
   * 创建验证
   *
   * @param tpmSimpleAuditFileInfoDto 创建实体
   */
  private void createValidate(TpmSimpleAuditFileInfoVo tpmSimpleAuditFileInfoDto) {
    Validate.notNull(tpmSimpleAuditFileInfoDto, "新增时，对象信息不能为空！");
    Validate.isTrue(tpmSimpleAuditFileInfoDto.getId() == null, "新增数据时,数据主键不为空!");
  }

  /**
   * 修改验证
   *
   * @param tpmSimpleAuditFileInfoDto 修改实体
   */
  private void updateValidate(TpmSimpleAuditFileInfoVo tpmSimpleAuditFileInfoDto) {
    Validate.notNull(tpmSimpleAuditFileInfoDto, "修改时，对象信息不能为空！");
    Validate.notBlank(tpmSimpleAuditFileInfoDto.getId(), "修改数据时，主键不能为空！");
  }

}