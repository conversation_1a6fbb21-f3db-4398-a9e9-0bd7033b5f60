package com.biz.crm.tpm.business.activities.simple.audit.entity;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.UuidOpEntity;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：tpm核销实例活动信息;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "SimpleAuditActivityInfo",description = "tpm核销实例活动信息")
@TableName("tpm_simple_audit_activity_info")
@Getter
@Setter
@Entity(name = "tpm_simple_audit_activity_info")
@org.hibernate.annotations.Table(appliesTo = "tpm_simple_audit_activity_info", comment = "tpm核销实例活动信息")
@Table(name = "tpm_simple_audit_activity_info")
public class TpmSimpleAuditActivityInfoEntity  extends UuidOpEntity{


  
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  @Column(name = "parent_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '业务编码 '")
  private String parentCode;
  
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value= "活动编号")
  @Column(name = "activities_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动编号 '")
  private String activitiesCode;
  
  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value= "活动名称")
  @Column(name = "activities_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动名称 '")
  private String activitiesName;
  
  /** 开始时间 */
  @ApiModelProperty(name = "beginTime",notes = "开始时间", value= "开始时间")
  @Column(name = "begin_time", nullable = true,  columnDefinition = "DATETIME COMMENT '开始时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  
  /** 结束时间 */
  @ApiModelProperty(name = "endTime",notes = "结束时间", value= "结束时间")
  @Column(name = "end_time", nullable = true,  columnDefinition = "DATETIME COMMENT '结束时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  
  

}