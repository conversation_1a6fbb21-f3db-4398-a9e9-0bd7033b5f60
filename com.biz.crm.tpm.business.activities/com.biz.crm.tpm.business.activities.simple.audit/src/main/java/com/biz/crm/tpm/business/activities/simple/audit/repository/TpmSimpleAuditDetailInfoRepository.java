package com.biz.crm.tpm.business.activities.simple.audit.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditDetailInfoEntity;
import com.biz.crm.tpm.business.activities.simple.audit.mapper.TpmSimpleAuditDetailInfoMapper;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditDetailInfoVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * tpm核销实例明细信息;(tpm_simple_audit_detail_info)表数据库访问层
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Component
public class TpmSimpleAuditDetailInfoRepository extends ServiceImpl<TpmSimpleAuditDetailInfoMapper, TpmSimpleAuditDetailInfoEntity> {
  @Autowired
  private TpmSimpleAuditDetailInfoMapper tpmSimpleAuditDetailInfoMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<TpmSimpleAuditDetailInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditDetailInfoVo dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    Page<TpmSimpleAuditDetailInfoVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmSimpleAuditDetailInfoMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmSimpleAuditDetailInfo>
   */
  public List<TpmSimpleAuditDetailInfoEntity> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .in(TpmSimpleAuditDetailInfoEntity::getId, ids)
            .list();
  }

  /**
   * 根据业务编号获取详情集合
   *
   * @param parentCode 业务编码
   * @return List<TpmSimpleAuditDetailInfo>
   */
  public List<TpmSimpleAuditDetailInfoEntity> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .eq(TpmSimpleAuditDetailInfoEntity::getParentCode, parentCode)
            .list();
  }


  /**
   * 根据业务数据编码删除管理细类数据
   *
   * @param parentCodes 业务数据编码集合
   */
  public void deleteByParentCodes(Collection<String> parentCodes) {
    if (CollectionUtils.isEmpty(parentCodes)) {
      return;
    }
    this.lambdaUpdate().in(TpmSimpleAuditDetailInfoEntity::getParentCode, parentCodes)
            .remove();
  }

  public List<TpmSimpleAuditDetailInfoEntity> findByParentCodeAndDynamicKey(String parentCode, String dynamicKey) {
    if (StringUtils.isBlank(parentCode)) {
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .eq(TpmSimpleAuditDetailInfoEntity::getParentCode, parentCode)
            .eq(TpmSimpleAuditDetailInfoEntity::getDynamicKey,dynamicKey)
            .list();
  }
}