package com.biz.crm.tpm.business.activities.simple.audit.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditModelEntity;
import com.biz.crm.tpm.business.activities.simple.audit.repository.TpmSimpleAuditModelRepository;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditActivityInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditDetailInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditFileInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditInvoiceInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditModelEntityService;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditModelVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;

/**
 * tpm核销实例model;(tpm_simple_audit_model)表服务实现类
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Service("tpmSimpleAuditModelService")
public class TpmSimpleAuditModelEntityServiceImpl implements TpmSimpleAuditModelEntityService {
  @Autowired
  private TpmSimpleAuditModelRepository tpmSimpleAuditModelRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Autowired
  private TpmSimpleAuditActivityInfoService tpmSimpleAuditActivityInfoService;

  @Autowired
  private TpmSimpleAuditDetailInfoService tpmSimpleAuditDetailInfoService;

  @Autowired
  private TpmSimpleAuditFileInfoService tpmSimpleAuditFileInfoService;

  @Autowired
  private TpmSimpleAuditInvoiceInfoService tpmSimpleAuditInvoiceInfoService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<TpmSimpleAuditModelVo> findByConditions(Pageable pageable, TpmSimpleAuditModelVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmSimpleAuditModelVo();
    }
    return this.tpmSimpleAuditModelRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmSimpleAuditModelVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmSimpleAuditModelEntity tpmSimpleAuditModel = this.tpmSimpleAuditModelRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (tpmSimpleAuditModel == null) {
      return null;
    }
    return this.convertSimpleAuditModelData(tpmSimpleAuditModel);
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmSimpleAuditModelVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditModelEntity> tpmSimpleAuditModels = this.tpmSimpleAuditModelRepository.findByIds(ids);
    return (List<TpmSimpleAuditModelVo>) this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditModels, TpmSimpleAuditModelEntity.class, TpmSimpleAuditModelVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  @Override
  public TpmSimpleAuditModelVo findByParentCode(String parentCode) {
    //1.获取动态表单主体数据
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    TpmSimpleAuditModelEntity tpmSimpleAuditModel = this.tpmSimpleAuditModelRepository.findByParentCode(parentCode);
    if (ObjectUtils.isEmpty(tpmSimpleAuditModel)) {
      return null;
    }
    return this.convertSimpleAuditModelData(tpmSimpleAuditModel);
  }

  /**
   * 新增数据
   *
   * @param tpmSimpleAuditModelDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditModelVo create(TpmSimpleAuditModelVo tpmSimpleAuditModelDto) {
    //1.保存动态表单信息
    this.createValidate(tpmSimpleAuditModelDto);
    TpmSimpleAuditModelEntity tpmSimpleAuditModel = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditModelDto, TpmSimpleAuditModelEntity.class, LinkedHashSet.class, ArrayList.class);
    tpmSimpleAuditModel.setTenantCode(TenantUtils.getTenantCode());
    this.tpmSimpleAuditModelRepository.saveOrUpdate(tpmSimpleAuditModel);
    //2.保存关联活动信息
    if (!CollectionUtils.isEmpty(tpmSimpleAuditModelDto.getActivityInfos())) {
      tpmSimpleAuditModelDto.getActivityInfos().forEach(activity -> activity.setParentCode(tpmSimpleAuditModel.getId()));
      this.tpmSimpleAuditActivityInfoService.createBatch(tpmSimpleAuditModelDto.getActivityInfos());
    }
    //3.保存关联活动细类信息
    if (!CollectionUtils.isEmpty(tpmSimpleAuditModelDto.getDetailInfos())) {
      tpmSimpleAuditModelDto.getDetailInfos().forEach(detail -> detail.setParentCode(tpmSimpleAuditModel.getId()));
      this.tpmSimpleAuditDetailInfoService.createBatch(tpmSimpleAuditModelDto.getDetailInfos());
    }
    //4.保存关联附件信息
    if (!CollectionUtils.isEmpty(tpmSimpleAuditModelDto.getFileInfos())) {
      tpmSimpleAuditModelDto.getFileInfos().forEach(file -> file.setParentCode(tpmSimpleAuditModel.getId()));
      this.tpmSimpleAuditFileInfoService.createBatch(tpmSimpleAuditModelDto.getFileInfos());
    }
    //5.保存关联发票信息
    if (!CollectionUtils.isEmpty(tpmSimpleAuditModelDto.getInvoiceInfos())) {
      tpmSimpleAuditModelDto.getInvoiceInfos().forEach(invoice -> invoice.setParentCode(tpmSimpleAuditModel.getId()));
      this.tpmSimpleAuditInvoiceInfoService.createBatch(tpmSimpleAuditModelDto.getInvoiceInfos());
    }
    return tpmSimpleAuditModelDto;
  }

  /**
   * 批量新增
   *
   * @param tpmSimpleAuditModelVos 新增实体列表
   * @return 新增结果
   */
  @Transactional
  @Override
  public List<TpmSimpleAuditModelVo> createBatch(Collection<TpmSimpleAuditModelVo> tpmSimpleAuditModelVos) {
    if (CollectionUtils.isEmpty(tpmSimpleAuditModelVos)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditModelVo> results = Lists.newArrayList();
    for (TpmSimpleAuditModelVo tpmSimpleAuditModelDto : tpmSimpleAuditModelVos) {
      TpmSimpleAuditModelVo result = this.create(tpmSimpleAuditModelDto);
      results.add(result);
    }
    return results;
  }

  /**
   * 修改新据
   *
   * @param tpmSimpleAuditModelDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditModelVo update(TpmSimpleAuditModelVo tpmSimpleAuditModelDto) {
    //1.保存表单信息
    this.updateValidate(tpmSimpleAuditModelDto);
    TpmSimpleAuditModelEntity tpmSimpleAuditModel = this.tpmSimpleAuditModelRepository.findByIdAndTenantCode(tpmSimpleAuditModelDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(tpmSimpleAuditModel, "修改数据不存在，请检查！");
    TpmSimpleAuditModelEntity newEntity = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditModelDto, TpmSimpleAuditModelEntity.class, LinkedHashSet.class, ArrayList.class);
    newEntity.setTenantCode(TenantUtils.getTenantCode());
    this.tpmSimpleAuditModelRepository.saveOrUpdate(newEntity);
    //2.保存关联活动信息
    this.tpmSimpleAuditActivityInfoService.deleteByParentCodes(Collections.singleton(newEntity.getId()));
    if (!CollectionUtils.isEmpty(tpmSimpleAuditModelDto.getActivityInfos())) {
      tpmSimpleAuditModelDto.getActivityInfos().forEach(activity -> activity.setParentCode(tpmSimpleAuditModel.getId()));
      this.tpmSimpleAuditActivityInfoService.createBatch(tpmSimpleAuditModelDto.getActivityInfos());
    }
    //3.保存关联活动细类信息
    this.tpmSimpleAuditDetailInfoService.deleteByParentCodes(Collections.singleton(newEntity.getId()));
    if (!CollectionUtils.isEmpty(tpmSimpleAuditModelDto.getDetailInfos())) {
      tpmSimpleAuditModelDto.getDetailInfos().forEach(detail -> detail.setParentCode(tpmSimpleAuditModel.getId()));
      this.tpmSimpleAuditDetailInfoService.createBatch(tpmSimpleAuditModelDto.getDetailInfos());
    }
    //4.保存关联附件信息
    this.tpmSimpleAuditFileInfoService.deleteByParentCodes(Collections.singleton(newEntity.getId()));
    if (!CollectionUtils.isEmpty(tpmSimpleAuditModelDto.getFileInfos())) {
      tpmSimpleAuditModelDto.getFileInfos().forEach(file -> file.setParentCode(tpmSimpleAuditModel.getId()));
      this.tpmSimpleAuditFileInfoService.createBatch(tpmSimpleAuditModelDto.getFileInfos());
    }
    //5.保存关联发票信息
    this.tpmSimpleAuditInvoiceInfoService.deleteByParentCodes(Collections.singleton(newEntity.getId()));
    if (!CollectionUtils.isEmpty(tpmSimpleAuditModelDto.getInvoiceInfos())) {
      tpmSimpleAuditModelDto.getInvoiceInfos().forEach(invoice -> invoice.setParentCode(tpmSimpleAuditModel.getId()));
      this.tpmSimpleAuditInvoiceInfoService.createBatch(tpmSimpleAuditModelDto.getInvoiceInfos());
    }
    return tpmSimpleAuditModelDto;
  }

  /**
   * 删除动态表单数据
   * 级联删除关联的活动，细类，附件，发票信息
   *
   * @param ids 主键结合
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmSimpleAuditModelEntity> tpmSimpleAuditModels = this.tpmSimpleAuditModelRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(tpmSimpleAuditModels)) {
      return;
    }
    this.tpmSimpleAuditModelRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    this.tpmSimpleAuditActivityInfoService.deleteByParentCodes(ids);
    this.tpmSimpleAuditDetailInfoService.deleteByParentCodes(ids);
    this.tpmSimpleAuditFileInfoService.deleteByParentCodes(ids);
    this.tpmSimpleAuditInvoiceInfoService.deleteByParentCodes(ids);
  }

  /**
   * 创建验证
   *
   * @param tpmSimpleAuditModelDto 创建实体
   */
  private void createValidate(TpmSimpleAuditModelVo tpmSimpleAuditModelDto) {
    Validate.notNull(tpmSimpleAuditModelDto, "新增时，对象信息不能为空！");
    Validate.isTrue(tpmSimpleAuditModelDto.getId() == null, "新增数据时,数据主键不为空!");
  }

  /**
   * 修改验证
   *
   * @param tpmSimpleAuditModelDto 编辑实体
   */
  private void updateValidate(TpmSimpleAuditModelVo tpmSimpleAuditModelDto) {
    Validate.notNull(tpmSimpleAuditModelDto, "修改时，对象信息不能为空！");
    Validate.notBlank(tpmSimpleAuditModelDto.getId(), "修改数据时，主键不能为空！");
  }

  /**
   * 组装动态表单数据
   *
   * @param tpmSimpleAuditModel 动态表单实体
   * @return 组装后数据
   */
  private TpmSimpleAuditModelVo convertSimpleAuditModelData(TpmSimpleAuditModelEntity tpmSimpleAuditModel) {
    TpmSimpleAuditModelVo tpmSimpleAuditModelVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditModel, TpmSimpleAuditModelVo.class, LinkedHashSet.class, ArrayList.class);
    //1.获取关联活动信息
    tpmSimpleAuditModelVo.setActivityInfos(this.tpmSimpleAuditActivityInfoService.findByParentCode(tpmSimpleAuditModel.getId()));
    //2.获取关联细类信息
    tpmSimpleAuditModelVo.setDetailInfos(this.tpmSimpleAuditDetailInfoService.findByParentCode(tpmSimpleAuditModel.getId()));
    //3.获取关联附件信息
    tpmSimpleAuditModelVo.setFileInfos(this.tpmSimpleAuditFileInfoService.findByParentCode(tpmSimpleAuditModel.getId()));
    //4.获取关联发票信息
    tpmSimpleAuditModelVo.setInvoiceInfos(this.tpmSimpleAuditInvoiceInfoService.findByParentCode(tpmSimpleAuditModel.getId()));
    return tpmSimpleAuditModelVo;
  }

}