package com.biz.crm.tpm.business.activities.simple.audit.vo;

import com.biz.crm.business.common.local.entity.FileEntity;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * Vo：tpm核销实例文件信息;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "TpmSimpleAuditFileInfo",description = "tpm核销实例文件信息")
@Getter
@Setter
public class TpmSimpleAuditFileInfoVo extends FileEntity {
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  private String parentCode;
  /** 活动细类编码 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编码", value= "活动细类编码")
  private String costTypeDetailCode;
  /** 活动细类名称 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value= "活动细类名称")
  private String costTypeDetailName;
  /** 要求核销资料编号 */
  @ApiModelProperty(name = "requestCode",notes = "要求核销资料编号", value= "要求核销资料编号")
  private String requestCode;

}