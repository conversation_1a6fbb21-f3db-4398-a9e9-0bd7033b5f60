package com.biz.crm.tpm.business.activities.simple.audit.entity;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.UuidEntity;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：tpm核销实例发票信息;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "SimpleAuditInvoiceInfo",description = "tpm核销实例发票信息")
@TableName("tpm_simple_audit_invoice_info")
@Getter
@Setter
@Entity(name = "tpm_simple_audit_invoice_info")
@org.hibernate.annotations.Table(appliesTo = "tpm_simple_audit_invoice_info", comment = "tpm核销实例发票信息")
@Table(name = "tpm_simple_audit_invoice_info")
public class TpmSimpleAuditInvoiceInfoEntity  extends UuidEntity{
  
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  @Column(name = "parent_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '业务编码 '")
  private String parentCode;
  
  /** 发票类型 */
  @ApiModelProperty(name = "type",notes = "发票类型", value= "发票类型")
  @Column(name = "type", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '发票类型 '")
  private String type;
  
  /** 发票号码(只允许填写数字和字母) */
  @ApiModelProperty(name = "invoiceNo",notes = "发票号码(只允许填写数字和字母)", value= "发票号码(只允许填写数字和字母)")
  @Column(name = "invoice_no", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '发票号码(只允许填写数字和字母) '")
  private String invoiceNo;
  
  /** 开票日期 */
  @ApiModelProperty(name = "billingDate",notes = "开票日期", value= "开票日期")
  @Column(name = "billing_date", nullable = true,  columnDefinition = "DATETIME COMMENT '开票日期 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date billingDate;
  
  /** 校验码 */
  @ApiModelProperty(name = "checkCode",notes = "校验码", value= "校验码")
  @Column(name = "check_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '校验码 '")
  private String checkCode;
  
  /** 税价合计 */
  @ApiModelProperty(name = "priceAndTax",notes = "税价合计", value= "税价合计")
  @Column(name = "price_and_tax", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '税价合计 '")
  private BigDecimal priceAndTax;
  
  /** 不含税金额 */
  @ApiModelProperty(name = "amountWithoutTax",notes = "不含税金额", value= "不含税金额")
  @Column(name = "amount_without_tax", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '不含税金额 '")
  private BigDecimal amountWithoutTax;
  
  /** 税率 */
  @ApiModelProperty(name = "taxRate",notes = "税率", value= "税率")
  @Column(name = "tax_rate", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '税率 '")
  private BigDecimal taxRate;
  
  /** 税额 */
  @ApiModelProperty(name = "taxAmount",notes = "税额", value= "税额")
  @Column(name = "tax_amount", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '税额 '")
  private BigDecimal taxAmount;
  
  /** 购买方名称 */
  @ApiModelProperty(name = "purchaser",notes = "购买方名称", value= "购买方名称")
  @Column(name = "purchaser", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '购买方名称 '")
  private String purchaser;
  
  /** 购买方税号 */
  @ApiModelProperty(name = "pNo",notes = "购买方税号", value= "购买方税号")
  @Column(name = "p_no", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '购买方税号 '")
  private String pNo;
  
  /** 购买方开户行及账号 */
  @ApiModelProperty(name = "pBankAndAccount",notes = "购买方开户行及账号", value= "购买方开户行及账号")
  @Column(name = "p_bank_and_account", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '购买方开户行及账号 '")
  private String pBankAndAccount;
  
  /** 购买方地址电话 */
  @ApiModelProperty(name = "pAddressAndPhone",notes = "购买方地址及电话", value= "购买方地址及电话")
  @Column(name = "p_address_and_phone", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '购买方地址电话 '")
  private String pAddressAndPhone;
  
  /** 销售方名称 */
  @ApiModelProperty(name = "seller",notes = "销售方名称", value= "销售方名称")
  @Column(name = "seller", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '销售方名称 '")
  private String seller;
  
  /** 销售方税号 */
  @ApiModelProperty(name = "sNo",notes = "销售方税号", value= "销售方税号")
  @Column(name = "s_no", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '销售方税号 '")
  private String sNo;
  
  /** 销售方开户行及账号 */
  @ApiModelProperty(name = "sBankAndAccount",notes = "销售方开户行及账号", value= "销售方开户行及账号")
  @Column(name = "s_bank_and_account", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '销售方开户行及账号 '")
  private String sBankAndAccount;
  
  /** 销售方地址电话 */
  @ApiModelProperty(name = "sAddressAndPhone",notes = "销售方地址电话", value= "销售方地址电话")
  @Column(name = "s_address_and_phone", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '销售方地址电话 '")
  private String sAddressAndPhone;
  
  /** 是否完全使用 */
  @ApiModelProperty(name = "fullUse",notes = "是否完全使用", value= "是否完全使用")
  @Column(name = "full_use", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '是否完全使用 '")
  private String fullUse;
  
  /** 是否被使用过 */
  @ApiModelProperty(name = "hasUsed",notes = "是否被使用过", value= "是否被使用过")
  @Column(name = "has_used", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '是否被使用过 '")
  private String hasUsed;
  
  /** 可用余额 */
  @ApiModelProperty(name = "availableAmount",notes = "可用余额", value= "可用余额")
  @Column(name = "available_amount", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '可用余额 '")
  private BigDecimal availableAmount;
  
  /** 已使用金额 */
  @ApiModelProperty(name = "usedAmount",notes = "已使用金额", value= "已使用金额")
  @Column(name = "used_amount", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '已使用金额 '")
  private BigDecimal usedAmount;
  
  /** 发票金额 */
  @ApiModelProperty(name = "amount",notes = "发票金额", value= "发票金额")
  @Column(name = "amount", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '发票金额 '")
  private BigDecimal amount;
  
  /** 本次操作金额 */
  @ApiModelProperty(name = "operateAmount",notes = "本次操作金额", value= "本次操作金额")
  @Column(name = "operate_amount", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '本次操作金额 '")
  private BigDecimal operateAmount;

}