<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.business.activities.simple.audit.mapper.TpmSimpleAuditInvoiceInfoMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditInvoiceInfoEntity" id="TpmSimpleAuditInvoiceInfoMap">
  </resultMap>
  
  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditInvoiceInfoVo">
    select 
    t.*
    from tpm_simple_audit_invoice_info t 
    <where>
        <if test="dto.parentCode != null and dto.parentCode != '' ">
          and t.parent_code = #{dto.parentCode}
        </if>
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>