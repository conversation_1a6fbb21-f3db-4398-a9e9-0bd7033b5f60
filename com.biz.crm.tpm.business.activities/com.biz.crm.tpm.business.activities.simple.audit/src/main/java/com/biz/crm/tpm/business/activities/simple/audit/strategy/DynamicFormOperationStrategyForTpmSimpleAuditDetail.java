package com.biz.crm.tpm.business.activities.simple.audit.strategy;

import com.alibaba.fastjson.JSON;
import com.biz.crm.common.form.sdk.model.DynamicFormsOperationStrategy;
import com.biz.crm.tpm.business.activities.sdk.register.AuditCenterModuleRegister;
import com.biz.crm.tpm.business.activities.simple.audit.model.TpmSimpleAuditDetailModel;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditDetailInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditDetailInfoVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

/**
 * tpm会议活动执行表单数据保存转换策略
 *
 * <AUTHOR>
 */
@Component
public class DynamicFormOperationStrategyForTpmSimpleAuditDetail implements DynamicFormsOperationStrategy<TpmSimpleAuditDetailModel> {


  @Autowired
  private TpmSimpleAuditDetailInfoService tpmSimpleAuditDetailInfoService;

  @Autowired
  private AuditCenterModuleRegister auditCenterModuleRegister;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public void onDynamicFormsCreate(Collection<TpmSimpleAuditDetailModel> dynamicForms, String dynamicKey, String parentCode, Object parent) {
    List<TpmSimpleAuditDetailInfoVo> tpmSimpleAuditDetailInfoVos = JSON.parseArray(JSON.toJSONString(dynamicForms), TpmSimpleAuditDetailInfoVo.class);
    tpmSimpleAuditDetailInfoVos.forEach(dynamicForm -> {
      dynamicForm.setParentCode(parentCode);
      dynamicForm.setDynamicKey(dynamicKey);
    });
    this.tpmSimpleAuditDetailInfoService.createBatch(tpmSimpleAuditDetailInfoVos);
  }

  @Override
  public void onDynamicFormsModify(Collection<TpmSimpleAuditDetailModel> dynamicForms, String dynamicKey, String parentCode, Object parent) {
    List<TpmSimpleAuditDetailInfoVo> tpmSimpleAuditDetailInfoVos = JSON.parseArray(JSON.toJSONString(dynamicForms), TpmSimpleAuditDetailInfoVo.class);
    tpmSimpleAuditDetailInfoVos.forEach(dynamicForm -> {
      dynamicForm.setParentCode(parentCode);
      dynamicForm.setDynamicKey(dynamicKey);
      this.tpmSimpleAuditDetailInfoService.update(dynamicForm);
    });
  }

  @Override
  public void onDynamicFormsDelete(String parentCode, String dynamicKey, String[] detailCodes) {

  }

  @Override
  public Collection<TpmSimpleAuditDetailModel> findByParentCode(String dynamicKey, String parentCode) {
    List<TpmSimpleAuditDetailInfoVo> tpmSimpleAuditDetailInfoVos = this.tpmSimpleAuditDetailInfoService.findByParentCodeAndDynamicKey(parentCode,dynamicKey);
    if (!CollectionUtils.isEmpty(tpmSimpleAuditDetailInfoVos)) {
      return this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditDetailInfoVos, TpmSimpleAuditDetailInfoVo.class, TpmSimpleAuditDetailModel.class, HashSet.class, ArrayList.class);
    }
    return Collections.EMPTY_LIST;
  }

  @Override
  public String dynamicFormCode() {
    return StringUtils.uncapitalize(this.getClass().getSimpleName());
  }

  @Override
  public String dynamicFormName() {
    return "示例核销明细表单";
  }

  @Override
  public Class<TpmSimpleAuditDetailModel> dynamicFormClass() {
    return TpmSimpleAuditDetailModel.class;
  }

  @Override
  public String moduleCode() {
    return auditCenterModuleRegister.moduleCode();
  }

  @Override
  public void onDynamicFormsDelete(String parentCode, String dynamicKey) {

  }

  @Override
  public int getOrder() {
    return 1;
  }
}
