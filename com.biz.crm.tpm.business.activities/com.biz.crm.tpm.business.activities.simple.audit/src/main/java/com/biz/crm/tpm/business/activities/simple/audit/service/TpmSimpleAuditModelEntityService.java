package com.biz.crm.tpm.business.activities.simple.audit.service;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditModelVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * tpm核销实例model;(tpm_simple_audit_model)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
public interface TpmSimpleAuditModelEntityService {

  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmSimpleAuditModelVo> findByConditions(Pageable pageable, TpmSimpleAuditModelVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmSimpleAuditModelVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmSimpleAuditModelVo> findByIds(Collection<String> ids);
  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  TpmSimpleAuditModelVo findByParentCode(String parentCode);
  
  /**
   * 新增数据
   *
   * @param tpmSimpleAuditModelDto 实体对象
   * @return 新增结果
   */
  TpmSimpleAuditModelVo create(TpmSimpleAuditModelVo tpmSimpleAuditModelDto);
  /**
   * 批量新增
   * @param tpmSimpleAuditModelVos 新增实体列表
   * @return 新增结果
   */
  List<TpmSimpleAuditModelVo> createBatch(Collection<TpmSimpleAuditModelVo> tpmSimpleAuditModelVos);
  /**
   * 修改数据
   *
   * @param tpmSimpleAuditModelDto 实体对象
   * @return 修改结果
   */
  TpmSimpleAuditModelVo update(TpmSimpleAuditModelVo tpmSimpleAuditModelDto);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
    
}