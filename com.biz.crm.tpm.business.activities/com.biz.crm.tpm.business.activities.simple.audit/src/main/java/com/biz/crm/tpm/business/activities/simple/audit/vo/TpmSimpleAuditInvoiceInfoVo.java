package com.biz.crm.tpm.business.activities.simple.audit.vo;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Vo：tpm核销实例发票信息;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "TpmSimpleAuditInvoiceInfo",description = "tpm核销实例发票信息")
@Getter
@Setter
public class TpmSimpleAuditInvoiceInfoVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  private String parentCode;
  /** 发票类型 */
  @ApiModelProperty(name = "type",notes = "发票类型", value= "发票类型")
  private String type;
  /** 发票代码 */
  @ApiModelProperty(name = "code",notes = "发票代码", value= "发票代码")
  private String code;
  /** 发票号码(只允许填写数字和字母) */
  @ApiModelProperty(name = "invoiceNo",notes = "发票号码(只允许填写数字和字母)", value= "发票号码(只允许填写数字和字母)")
  private String invoiceNo;
  /** 开票日期 */
  @ApiModelProperty(name = "billingDate",notes = "开票日期", value= "开票日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date billingDate;
  /** 校验码 */
  @ApiModelProperty(name = "checkCode",notes = "校验码", value= "校验码")
  private String checkCode;
  /** 税价合计 */
  @ApiModelProperty(name = "priceAndTax",notes = "税价合计", value= "税价合计")
  private BigDecimal priceAndTax;
  /** 不含税金额 */
  @ApiModelProperty(name = "amountWithoutTax",notes = "不含税金额", value= "不含税金额")
  private BigDecimal amountWithoutTax;
  /** 税率 */
  @ApiModelProperty(name = "taxRate",notes = "税率", value= "税率")
  private BigDecimal taxRate;
  /** 税额 */
  @ApiModelProperty(name = "taxAmount",notes = "税额", value= "税额")
  private BigDecimal taxAmount;
  /** 购买方名称 */
  @ApiModelProperty(name = "purchaser",notes = "购买方名称", value= "购买方名称")
  private String purchaser;
  /** 购买方税号 */
  @ApiModelProperty(name = "pNo",notes = "购买方税号", value= "购买方税号")
  private String pNo;
  /** 购买方开户行及账号 */
  @ApiModelProperty(name = "pBankAndAccount",notes = "购买方开户行及账号", value= "购买方开户行及账号")
  private String pBankAndAccount;
  /** 购买方地址电话 */
  @ApiModelProperty(name = "pAddressAndPhone",notes = "购买方地址及电话", value= "购买方地址及电话")
  private String pAddressAndPhone;
  /** 销售方名称 */
  @ApiModelProperty(name = "seller",notes = "销售方名称", value= "销售方名称")
  private String seller;
  /** 销售方税号 */
  @ApiModelProperty(name = "sNo",notes = "销售方税号", value= "销售方税号")
  private String sNo;
  /** 销售方开户行及账号 */
  @ApiModelProperty(name = "sBankAndAccount",notes = "销售方开户行及账号", value= "销售方开户行及账号")
  private String sBankAndAccount;
  /** 销售方地址电话 */
  @ApiModelProperty(name = "sAddressAndPhone",notes = "销售方地址电话", value= "销售方地址电话")
  private String sAddressAndPhone;
  /** 是否完全使用 */
  @ApiModelProperty(name = "fullUse",notes = "是否完全使用", value= "是否完全使用")
  private String fullUse;
  /** 是否被使用过 */
  @ApiModelProperty(name = "hasUsed",notes = "是否被使用过", value= "是否被使用过")
  private String hasUsed;
  /** 可用余额 */
  @ApiModelProperty(name = "availableAmount",notes = "可用余额", value= "可用余额")
  private BigDecimal availableAmount;
  /** 已使用金额 */
  @ApiModelProperty(name = "usedAmount",notes = "已使用金额", value= "已使用金额")
  private BigDecimal usedAmount;
  /** 发票金额 */
  @ApiModelProperty(name = "amount",notes = "发票金额", value= "发票金额")
  private BigDecimal amount;
  /** 本次操作金额 */
  @ApiModelProperty(name = "operateAmount",notes = "本次操作金额", value= "本次操作金额")
  private BigDecimal operateAmount;

}