package com.biz.crm.tpm.business.activities.simple.audit.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditInvoiceInfoEntity;
import com.biz.crm.tpm.business.activities.simple.audit.mapper.TpmSimpleAuditInvoiceInfoMapper;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditInvoiceInfoVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * tpm核销实例发票信息;(tpm_simple_audit_invoice_info)表数据库访问层
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Component
public class TpmSimpleAuditInvoiceInfoRepository extends ServiceImpl<TpmSimpleAuditInvoiceInfoMapper, TpmSimpleAuditInvoiceInfoEntity> {
  @Autowired
  private TpmSimpleAuditInvoiceInfoMapper tpmSimpleAuditInvoiceInfoMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<TpmSimpleAuditInvoiceInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditInvoiceInfoVo dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    Page<TpmSimpleAuditInvoiceInfoVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmSimpleAuditInvoiceInfoMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmSimpleAuditInvoiceInfo>
   */
  public List<TpmSimpleAuditInvoiceInfoEntity> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .in(TpmSimpleAuditInvoiceInfoEntity::getId, ids)
            .list();
  }

  /**
   * 根据业务编码集合获取详情集合
   *
   * @param parentCode 业务编码
   * @return List<TpmSimpleAuditInvoiceInfo>
   */
  public List<TpmSimpleAuditInvoiceInfoEntity> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .in(TpmSimpleAuditInvoiceInfoEntity::getParentCode, parentCode)
            .list();
  }

  /**
   * 根据父级业务编码删除关联发票
   *
   * @param parentCodes 父级业务编码
   */
  public void deleteByParentCodes(Collection<String> parentCodes) {
    if (CollectionUtils.isEmpty(parentCodes)) {
      return;
    }
    this.lambdaUpdate().in(TpmSimpleAuditInvoiceInfoEntity::getParentCode, parentCodes)
            .remove();
  }
}