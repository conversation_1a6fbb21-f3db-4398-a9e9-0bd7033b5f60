package com.biz.crm.tpm.business.activities.simple.audit.repository;

import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditActivityInfoEntity;
import com.biz.crm.tpm.business.activities.simple.audit.mapper.TpmSimpleAuditActivityInfoMapper;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditActivityInfoVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Collections;

/**
 * tpm核销实例活动信息;(tpm_simple_audit_activity_info)表数据库访问层
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Component
public class TpmSimpleAuditActivityInfoRepository extends ServiceImpl<TpmSimpleAuditActivityInfoMapper, TpmSimpleAuditActivityInfoEntity> {
  @Autowired
  private TpmSimpleAuditActivityInfoMapper tpmSimpleAuditActivityInfoMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<TpmSimpleAuditActivityInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditActivityInfoVo dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    Page<TpmSimpleAuditActivityInfoVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmSimpleAuditActivityInfoMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmSimpleAuditActivityInfo>
   */
  public List<TpmSimpleAuditActivityInfoEntity> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .in(TpmSimpleAuditActivityInfoEntity::getId, ids)
            .list();
  }

  /**
   * 根据业务编码获取详情集合
   *
   * @param parentCode 业务编码
   * @return List<TpmSimpleAuditActivityInfo>
   */
  public List<TpmSimpleAuditActivityInfoEntity> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .eq(TpmSimpleAuditActivityInfoEntity::getParentCode, parentCode)
            .list();
  }

  /**
   * 根据业务编码获取详情集合
   *
   * @param parentCodes 业务编码集合
   */
  public void deleteByParentCodes(Collection<String> parentCodes) {
    if (CollectionUtils.isEmpty(parentCodes)) {
      return;
    }
    this.lambdaUpdate()
            .in(TpmSimpleAuditActivityInfoEntity::getParentCode, parentCodes)
            .remove();
  }


}