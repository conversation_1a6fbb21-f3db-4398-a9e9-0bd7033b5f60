package com.biz.crm.tpm.business.activities.simple.audit.entity;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：tpm核销实例model;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "SimpleAuditModel",description = "tpm核销实例model")
@TableName("tpm_simple_audit_model")
@Getter
@Setter
@Entity(name = "tpm_simple_audit_model")
@org.hibernate.annotations.Table(appliesTo = "tpm_simple_audit_model", comment = "tpm核销实例model")
@Table(name = "tpm_simple_audit_model")
public class TpmSimpleAuditModelEntity  extends TenantFlagOpEntity {
  
  /** 步骤业务编码 */
  @ApiModelProperty(name = "dynamicKey",notes = "步骤业务编码", value= "步骤业务编码")
  @Column(name = "dynamic_key", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '步骤业务编码 '")
  private String dynamicKey;
  
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  @Column(name = "parent_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '业务编码 '")
  private String parentCode;

}