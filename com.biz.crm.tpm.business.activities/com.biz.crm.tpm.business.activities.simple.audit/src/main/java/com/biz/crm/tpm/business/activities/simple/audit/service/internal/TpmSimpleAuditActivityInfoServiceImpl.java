package com.biz.crm.tpm.business.activities.simple.audit.service.internal;
import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditActivityInfoEntity;
import com.biz.crm.tpm.business.activities.simple.audit.repository.TpmSimpleAuditActivityInfoRepository;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditActivityInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditActivityInfoVo;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.LinkedHashSet;
import java.util.ArrayList;
import com.google.common.collect.Lists;

/**
 * tpm核销实例活动信息;(tpm_simple_audit_activity_info)表服务实现类
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Service("tpmSimpleAuditActivityInfoService")
public class TpmSimpleAuditActivityInfoServiceImpl implements TpmSimpleAuditActivityInfoService {
  @Autowired
  private TpmSimpleAuditActivityInfoRepository tpmSimpleAuditActivityInfoRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return 分页数据
   */
  @Override
  public Page<TpmSimpleAuditActivityInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditActivityInfoVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmSimpleAuditActivityInfoVo();
    }
    return this.tpmSimpleAuditActivityInfoRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmSimpleAuditActivityInfoVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmSimpleAuditActivityInfoEntity tpmSimpleAuditActivityInfo = this.tpmSimpleAuditActivityInfoRepository.getById(id);
    if (tpmSimpleAuditActivityInfo == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditActivityInfo, TpmSimpleAuditActivityInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmSimpleAuditActivityInfoVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditActivityInfoEntity> tpmSimpleAuditActivityInfos = this.tpmSimpleAuditActivityInfoRepository.findByIds(ids);
    Collection<TpmSimpleAuditActivityInfoVo> tpmSimpleAuditActivityInfoVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditActivityInfos, TpmSimpleAuditActivityInfoEntity.class, TpmSimpleAuditActivityInfoVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmSimpleAuditActivityInfoVos);
  }

  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 业务编号
   * @return 单条数据
   */
  @Override
  public List<TpmSimpleAuditActivityInfoVo> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditActivityInfoEntity> tpmSimpleAuditActivityInfos = this.tpmSimpleAuditActivityInfoRepository.findByParentCode(parentCode);
    Collection<TpmSimpleAuditActivityInfoVo> tpmSimpleAuditActivityInfoVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditActivityInfos, TpmSimpleAuditActivityInfoEntity.class, TpmSimpleAuditActivityInfoVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmSimpleAuditActivityInfoVos);
  }

  /**
   * 新增数据
   *
   * @param tpmSimpleAuditActivityInfoDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditActivityInfoVo create(TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoDto) {
    this.createValidate(tpmSimpleAuditActivityInfoDto);
    TpmSimpleAuditActivityInfoEntity tpmSimpleAuditActivityInfo = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditActivityInfoDto, TpmSimpleAuditActivityInfoEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmSimpleAuditActivityInfoRepository.saveOrUpdate(tpmSimpleAuditActivityInfo);
    return tpmSimpleAuditActivityInfoDto;
  }

  /**
   * 批量新增
   *
   * @param tpmSimpleAuditActivityInfoVos 新增实体
   * @return 新增结果
   */
  @Transactional
  @Override
  public List<TpmSimpleAuditActivityInfoVo> createBatch(Collection<TpmSimpleAuditActivityInfoVo> tpmSimpleAuditActivityInfoVos) {
    if (CollectionUtils.isEmpty(tpmSimpleAuditActivityInfoVos)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditActivityInfoVo> results = Lists.newArrayList();
    for (TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoDto : tpmSimpleAuditActivityInfoVos) {
      TpmSimpleAuditActivityInfoVo result = this.create(tpmSimpleAuditActivityInfoDto);
      results.add(result);
    }
    return results;
  }

  /**
   * 修改新据
   *
   * @param tpmSimpleAuditActivityInfoDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditActivityInfoVo update(TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoDto) {
    this.updateValidate(tpmSimpleAuditActivityInfoDto);
    TpmSimpleAuditActivityInfoEntity tpmSimpleAuditActivityInfo = this.tpmSimpleAuditActivityInfoRepository.getById(tpmSimpleAuditActivityInfoDto.getId());
    Validate.notNull(tpmSimpleAuditActivityInfo, "修改数据不存在，请检查！");
    TpmSimpleAuditActivityInfoEntity newEntity = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditActivityInfoDto, TpmSimpleAuditActivityInfoEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmSimpleAuditActivityInfoRepository.saveOrUpdate(newEntity);
    return tpmSimpleAuditActivityInfoDto;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmSimpleAuditActivityInfoEntity> tpmSimpleAuditActivityInfos = this.tpmSimpleAuditActivityInfoRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(tpmSimpleAuditActivityInfos)) {
      return;
    }
    this.tpmSimpleAuditActivityInfoRepository.removeByIds(ids);
  }

  @Override
  public void deleteByParentCodes(Collection<String> parentCodes) {
    if(CollectionUtils.isEmpty(parentCodes)){
      return;
    }
    this.tpmSimpleAuditActivityInfoRepository.deleteByParentCodes(parentCodes);
  }

  /**
   * 创建验证
   *
   * @param tpmSimpleAuditActivityInfoDto 创建实体
   */
  private void createValidate(TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoDto) {
    Validate.notNull(tpmSimpleAuditActivityInfoDto, "新增时，对象信息不能为空！");
    Validate.isTrue(tpmSimpleAuditActivityInfoDto.getId() == null, "新增数据时,数据主键不为空!");
  }

  /**
   * 修改验证
   *
   * @param tpmSimpleAuditActivityInfoDto 修改实体
   */
  private void updateValidate(TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoDto) {
    Validate.notNull(tpmSimpleAuditActivityInfoDto, "修改时，对象信息不能为空！");
    Validate.notBlank(tpmSimpleAuditActivityInfoDto.getId(), "修改数据时，主键不能为空！");
  }

}