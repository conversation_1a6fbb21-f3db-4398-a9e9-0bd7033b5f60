package com.biz.crm.tpm.business.activities.simple.audit.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditInvoiceInfoEntity;
import com.biz.crm.tpm.business.activities.simple.audit.repository.TpmSimpleAuditInvoiceInfoRepository;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditInvoiceInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditInvoiceInfoVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;

/**
 * tpm核销实例发票信息;(tpm_simple_audit_invoice_info)表服务实现类
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Service("tpmSimpleAuditInvoiceInfoService")
public class TpmSimpleAuditInvoiceInfoServiceImpl implements TpmSimpleAuditInvoiceInfoService {
  @Autowired
  private TpmSimpleAuditInvoiceInfoRepository tpmSimpleAuditInvoiceInfoRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return 分页数据
   */
  @Override
  public Page<TpmSimpleAuditInvoiceInfoVo> findByConditions(Pageable pageable, TpmSimpleAuditInvoiceInfoVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmSimpleAuditInvoiceInfoVo();
    }
    return this.tpmSimpleAuditInvoiceInfoRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmSimpleAuditInvoiceInfoVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmSimpleAuditInvoiceInfoEntity tpmSimpleAuditInvoiceInfo = this.tpmSimpleAuditInvoiceInfoRepository.getById(id);
    if (tpmSimpleAuditInvoiceInfo == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditInvoiceInfo, TpmSimpleAuditInvoiceInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmSimpleAuditInvoiceInfoVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditInvoiceInfoEntity> tpmSimpleAuditInvoiceInfos = this.tpmSimpleAuditInvoiceInfoRepository.findByIds(ids);
    return (List<TpmSimpleAuditInvoiceInfoVo>) this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditInvoiceInfos, TpmSimpleAuditInvoiceInfoEntity.class, TpmSimpleAuditInvoiceInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过业务编码查询单条数据
   *
   * @param parentCode 业务编码
   * @return 单条数据
   */
  @Override
  public List<TpmSimpleAuditInvoiceInfoVo> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditInvoiceInfoEntity> tpmSimpleAuditInvoiceInfos = this.tpmSimpleAuditInvoiceInfoRepository.findByParentCode(parentCode);
    return (List<TpmSimpleAuditInvoiceInfoVo>) this.nebulaToolkitService.copyCollectionByWhiteList(tpmSimpleAuditInvoiceInfos, TpmSimpleAuditInvoiceInfoEntity.class, TpmSimpleAuditInvoiceInfoVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 新增数据
   *
   * @param tpmSimpleAuditInvoiceInfoDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditInvoiceInfoVo create(TpmSimpleAuditInvoiceInfoVo tpmSimpleAuditInvoiceInfoDto) {
    this.createValidate(tpmSimpleAuditInvoiceInfoDto);
    TpmSimpleAuditInvoiceInfoEntity tpmSimpleAuditInvoiceInfo = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditInvoiceInfoDto, TpmSimpleAuditInvoiceInfoEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmSimpleAuditInvoiceInfoRepository.saveOrUpdate(tpmSimpleAuditInvoiceInfo);
    return tpmSimpleAuditInvoiceInfoDto;
  }

  /**
   * 批量新增
   *
   * @param tpmSimpleAuditInvoiceInfoVos 新增实体集合
   * @return 新增结果
   */
  @Transactional
  @Override
  public List<TpmSimpleAuditInvoiceInfoVo> createBatch(Collection<TpmSimpleAuditInvoiceInfoVo> tpmSimpleAuditInvoiceInfoVos) {
    if (CollectionUtils.isEmpty(tpmSimpleAuditInvoiceInfoVos)) {
      return Lists.newArrayList();
    }
    List<TpmSimpleAuditInvoiceInfoVo> results = Lists.newArrayList();
    for (TpmSimpleAuditInvoiceInfoVo tpmSimpleAuditInvoiceInfoDto : tpmSimpleAuditInvoiceInfoVos) {
      TpmSimpleAuditInvoiceInfoVo result = this.create(tpmSimpleAuditInvoiceInfoDto);
      results.add(result);
    }
    return results;
  }

  /**
   * 修改新据
   *
   * @param tpmSimpleAuditInvoiceInfoDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmSimpleAuditInvoiceInfoVo update(TpmSimpleAuditInvoiceInfoVo tpmSimpleAuditInvoiceInfoDto) {
    this.updateValidate(tpmSimpleAuditInvoiceInfoDto);
    TpmSimpleAuditInvoiceInfoEntity tpmSimpleAuditInvoiceInfo = this.tpmSimpleAuditInvoiceInfoRepository.getById(tpmSimpleAuditInvoiceInfoDto.getId());
    Validate.notNull(tpmSimpleAuditInvoiceInfo, "修改数据不存在，请检查！");
    TpmSimpleAuditInvoiceInfoEntity newEntity = this.nebulaToolkitService.copyObjectByWhiteList(tpmSimpleAuditInvoiceInfoDto, TpmSimpleAuditInvoiceInfoEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmSimpleAuditInvoiceInfoRepository.saveOrUpdate(newEntity);
    return tpmSimpleAuditInvoiceInfoDto;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmSimpleAuditInvoiceInfoEntity> tpmSimpleAuditInvoiceInfos = this.tpmSimpleAuditInvoiceInfoRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(tpmSimpleAuditInvoiceInfos)) {
      return;
    }
    this.tpmSimpleAuditInvoiceInfoRepository.removeByIds(ids);
  }

  @Override
  public void deleteByParentCodes(Collection<String> parentCodes) {
    if(CollectionUtils.isEmpty(parentCodes)){
      return;
    }
    this.tpmSimpleAuditInvoiceInfoRepository.deleteByParentCodes(parentCodes);
  }

  /**
   * 创建验证
   *
   * @param tpmSimpleAuditInvoiceInfoDto 创建实体
   */
  private void createValidate(TpmSimpleAuditInvoiceInfoVo tpmSimpleAuditInvoiceInfoDto) {
    Validate.notNull(tpmSimpleAuditInvoiceInfoDto, "新增时，对象信息不能为空！");
    Validate.isTrue(tpmSimpleAuditInvoiceInfoDto.getId() == null, "新增数据时,数据主键不为空!");
  }

  /**
   * 修改验证
   *
   * @param tpmSimpleAuditInvoiceInfoDto 编辑实体
   */
  private void updateValidate(TpmSimpleAuditInvoiceInfoVo tpmSimpleAuditInvoiceInfoDto) {
    Validate.notNull(tpmSimpleAuditInvoiceInfoDto, "修改时，对象信息不能为空！");
    Validate.notBlank(tpmSimpleAuditInvoiceInfoDto.getId(), "修改数据时，主键不能为空！");
  }

}