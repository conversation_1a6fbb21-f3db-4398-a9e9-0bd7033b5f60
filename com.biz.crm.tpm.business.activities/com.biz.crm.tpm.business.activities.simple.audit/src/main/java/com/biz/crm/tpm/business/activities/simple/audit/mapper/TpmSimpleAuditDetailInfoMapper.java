package com.biz.crm.tpm.business.activities.simple.audit.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.simple.audit.entity.TpmSimpleAuditDetailInfoEntity;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditDetailInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * tpm核销实例明细信息;(tpm_simple_audit_detail_info)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Mapper
public interface TpmSimpleAuditDetailInfoMapper extends BaseMapper<TpmSimpleAuditDetailInfoEntity>{
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<TpmSimpleAuditDetailInfoVo> findByConditions(@Param("page") Page<TpmSimpleAuditDetailInfoVo> page , @Param("dto") TpmSimpleAuditDetailInfoVo dto);
}