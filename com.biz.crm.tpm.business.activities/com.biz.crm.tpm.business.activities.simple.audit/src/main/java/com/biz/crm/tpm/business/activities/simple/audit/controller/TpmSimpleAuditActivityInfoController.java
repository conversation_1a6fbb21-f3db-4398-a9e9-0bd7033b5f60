package com.biz.crm.tpm.business.activities.simple.audit.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.simple.audit.service.TpmSimpleAuditActivityInfoService;
import com.biz.crm.tpm.business.activities.simple.audit.vo.TpmSimpleAuditActivityInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * tpm核销实例活动信息;(tpm_simple_audit_activity_info)控制层
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@Api(tags = "tpm核销实例活动信息功能接口")
@RestController
@RequestMapping("/v1/tpmSimpleAuditActivityInfo")
@Slf4j
public class TpmSimpleAuditActivityInfoController{
  /**
   * 服务对象
   */
  @Autowired
  private TpmSimpleAuditActivityInfoService tpmSimpleAuditActivityInfoService;
  
  /**
   * 分页查询所有数据
   *
   * @param pageable        分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<TpmSimpleAuditActivityInfoVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                     @ApiParam(name = "tpmSimpleAuditActivityInfo", value = "核销采集信息") TpmSimpleAuditActivityInfoVo dto) {
    try {
      Page<TpmSimpleAuditActivityInfoVo> page = this.tpmSimpleAuditActivityInfoService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<TpmSimpleAuditActivityInfoVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id", required=true) String id) {
    try {
      TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoVo = this.tpmSimpleAuditActivityInfoService.findById(id);
      return Result.ok(tpmSimpleAuditActivityInfoVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 通过业务编号查询单条数据
   *
   * @param parentCode 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByParentCode")
  public Result<List<TpmSimpleAuditActivityInfoVo>> findByParentCode(@Param("parentCode") @ApiParam(name = "parentCode", value = "业务编号", required=true) String parentCode) {
    try {
      List<TpmSimpleAuditActivityInfoVo> tpmSimpleAuditActivityInfoVos = this.tpmSimpleAuditActivityInfoService.findByParentCode(parentCode);
      return Result.ok(tpmSimpleAuditActivityInfoVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 新增数据
   *
   * @param tpmSimpleAuditActivityInfoDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<TpmSimpleAuditActivityInfoVo> create(@ApiParam(name = "tpmSimpleAuditActivityInfoDto", value = "tpm核销实例活动信息") @RequestBody TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoDto) {
    try {
      TpmSimpleAuditActivityInfoVo result = this.tpmSimpleAuditActivityInfoService.create(tpmSimpleAuditActivityInfoDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 修改数据
   *
   * @param tpmSimpleAuditActivityInfoDto 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<TpmSimpleAuditActivityInfoVo> update(@ApiParam(name = "tpmSimpleAuditActivityInfoDto", value = "tpm核销实例活动信息") @RequestBody TpmSimpleAuditActivityInfoVo tpmSimpleAuditActivityInfoDto) {
    try {
      TpmSimpleAuditActivityInfoVo result = this.tpmSimpleAuditActivityInfoService.update(tpmSimpleAuditActivityInfoDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation(value = "删除数据")
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    try {
      this.tpmSimpleAuditActivityInfoService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
}