package com.biz.crm.tpm.business.activities.simple.audit.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.tpm.business.activities.sdk.entity.BaseAuditDetailEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实体：tpm核销实例明细信息;
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "SimpleAuditDetailInfo",description = "tpm核销实例明细信息")
@TableName("tpm_simple_audit_detail_info")
@Getter
@Setter
@Entity(name = "tpm_simple_audit_detail_info")
@org.hibernate.annotations.Table(appliesTo = "tpm_simple_audit_detail_info", comment = "tpm核销实例明细信息")
@Table(name = "tpm_simple_audit_detail_info")
public class TpmSimpleAuditDetailInfoEntity  extends BaseAuditDetailEntity {

  /** 开始时间 */
  @ApiModelProperty(name = "beginTime",notes = "开始时间", value= "开始时间")
  @Column(name = "begin_time", nullable = true,  columnDefinition = "DATETIME COMMENT '开始时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  
  /** 结束时间 */
  @ApiModelProperty(name = "endTime",notes = "结束时间", value= "结束时间")
  @Column(name = "end_time", nullable = true,  columnDefinition = "DATETIME COMMENT '结束时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  
  /** 货补产品层级编码 */
  @ApiModelProperty(name = "productLevelCode",notes = "货补产品层级编码", value= "货补产品层级编码")
  @Column(name = "product_level_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '货补产品层级编码 '")
  private String productLevelCode;
  
  /** 产品层级名称 */
  @ApiModelProperty(name = "productLevelName",notes = "货补产品层级名称", value= "货补产品层级名称")
  @Column(name = "product_level_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '货补产品层级名称 '")
  private String productLevelName;

  /** 货补产品编码 */
  @ApiModelProperty(name = "productCode",notes = "货补产品编码", value= "货补产品编码")
  @Column(name = "product_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '货补产品编码 '")
  private String productCode;

  /** 产品名称 */
  @ApiModelProperty(name = "productName",notes = "货补产品名称", value= "货补产品名称")
  @Column(name = "product_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '货补产品名称 '")
  private String productName;
  
  /** ERP会计科目编码 */
  @ApiModelProperty(name = "accountingSubjectsCode",notes = "ERP会计科目编码", value= "ERP会计科目编码")
  @Column(name = "accounting_subjects_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT 'ERP会计科目编码 '")
  private String accountingSubjectsCode;
  
  /** 预算科目编号 */
  @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编号", value= "预算科目编号")
  @Column(name = "budget_subjects_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '预算科目编号 '")
  private String budgetSubjectsCode;
  
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value= "预算科目名称")
  @Column(name = "budget_subjects_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;

}