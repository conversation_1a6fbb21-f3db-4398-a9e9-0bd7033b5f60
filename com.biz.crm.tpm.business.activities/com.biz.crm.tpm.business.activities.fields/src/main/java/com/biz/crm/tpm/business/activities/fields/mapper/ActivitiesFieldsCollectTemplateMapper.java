package com.biz.crm.tpm.business.activities.fields.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesFieldsCollectTemplateDto;
import com.biz.crm.tpm.business.activities.fields.entity.ActivitiesFieldsCollectTemplate;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesFieldsCollectTemplateVo;
import org.apache.ibatis.annotations.Param;

public interface ActivitiesFieldsCollectTemplateMapper extends BaseMapper<ActivitiesFieldsCollectTemplate> {
  /**
   * 分页查询所有数据
   * @param page       分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  Page<ActivitiesFieldsCollectTemplateVo> findByConditions(@Param("page") Page<ActivitiesFieldsCollectTemplateVo> page, @Param("dto") ActivitiesFieldsCollectTemplateDto dto);
}
