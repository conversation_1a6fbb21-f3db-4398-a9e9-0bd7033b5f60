package com.biz.crm.tpm.business.activities.fields.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_activities_fields_collect_template")
@Table(name = "tpm_activities_fields_collect_template", indexes = {@Index(name = "tpm_activities_fields_collect_template_index1", columnList = "tenant_code, code", unique = true)})
@ApiModel(value = "ActivitiesFieldsCollectTemplate", description = "活动采集字段模板")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_fields_collect_template", comment = "活动采集字段模板")
public class ActivitiesFieldsCollectTemplate extends TenantFlagOpEntity {

  @ApiModelProperty("活动采集字段编码")
  @TableField(value = "code")
  @Column(name = "code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动采集字段编码'")
  private String code;

  @ApiModelProperty("关联的活动表单标识")
  @TableField(value = "dynamic_form_code")
  @Column(name = "dynamic_form_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '关联的活动表单标识'")
  private String dynamicFormCode;

  @ApiModelProperty("关联的活动明细编码")
  @TableField(value = "activity_detail_code")
  @Column(name = "activity_detail_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '关联的活动明细编码'")
  private String activityDetailCode;

  @ApiModelProperty("批次号")
  @TableField(value = "bt_no")
  @Column(name = "bt_no", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '批次号'")
  private String btNo;

  @Transient
  @TableField(exist = false)
  private Map<String, ?> items;

}
