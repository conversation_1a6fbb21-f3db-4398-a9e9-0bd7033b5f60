package com.biz.crm.tpm.business.activities.fields.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesFieldsCollectTemplateDto;
import com.biz.crm.tpm.business.activities.fields.entity.ActivitiesFieldsCollectTemplate;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesFieldsCollectTemplateVo;
import com.biz.crm.tpm.business.activities.fields.mapper.ActivitiesFieldsCollectTemplateMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class ActivitiesFieldsCollectTemplateRepository extends ServiceImpl<ActivitiesFieldsCollectTemplateMapper, ActivitiesFieldsCollectTemplate> {

  @Autowired
  private ActivitiesFieldsCollectTemplateMapper activitiesFieldsCollectTemplateMapper;

  /**
   * 分页查询数据
   *
   * @param pageable   分页对象
   * @param dto 实体对象
   */
  public Page<ActivitiesFieldsCollectTemplateVo> findByConditions(Pageable pageable, ActivitiesFieldsCollectTemplateDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesFieldsCollectTemplateVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesFieldsCollectTemplateMapper.findByConditions(page, dto);
  }

  /**
   * 根据编码查询信息
   */
  public ActivitiesFieldsCollectTemplate findByCodeAndTenantCode(String code,String tenantCode){
    return this.lambdaQuery()
        .eq(ActivitiesFieldsCollectTemplate::getCode,code)
        .eq(ActivitiesFieldsCollectTemplate::getTenantCode,tenantCode)
        .eq(ActivitiesFieldsCollectTemplate::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .one();
  }

  /**
   * 根据关联的活动明细编码查询信息
   */
  public List<ActivitiesFieldsCollectTemplate> findByActivityDetailCodeAndTenantCode(String activityDetailCode,String tenantCode){
    return this.lambdaQuery()
        .eq(ActivitiesFieldsCollectTemplate::getActivityDetailCode,activityDetailCode)
        .eq(ActivitiesFieldsCollectTemplate::getTenantCode,tenantCode)
        .eq(ActivitiesFieldsCollectTemplate::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }


  /**
   * 根据批次号查询信息
   */
  public ActivitiesFieldsCollectTemplate findByBtNoAndTenantCode(String btNo,String tenantCode){
    return this.lambdaQuery()
        .eq(ActivitiesFieldsCollectTemplate::getBtNo,btNo)
        .eq(ActivitiesFieldsCollectTemplate::getTenantCode,tenantCode)
        .eq(ActivitiesFieldsCollectTemplate::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .one();
  }

  /**
   * 根据编码集合查询信息
   */
  public List<ActivitiesFieldsCollectTemplate> findByCodesAndTenantCode(Set<String> codes, String tenantCode){
    return this.lambdaQuery()
        .in(ActivitiesFieldsCollectTemplate::getCode,codes)
        .eq(ActivitiesFieldsCollectTemplate::getTenantCode,tenantCode)
        .eq(ActivitiesFieldsCollectTemplate::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }


  /**
   * 根据主键id集合，删除数据
   */
  public void deleteByIds(List<String> ids){
    this.lambdaUpdate()
        .in(ActivitiesFieldsCollectTemplate::getId,ids)
        .eq(ActivitiesFieldsCollectTemplate::getTenantCode,TenantUtils.getTenantCode())
        .set(ActivitiesFieldsCollectTemplate::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  /**
   * 通过id和租户编号查询
   * @param id
   * @param tenantCode
   * @return
   */
  public ActivitiesFieldsCollectTemplate findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesFieldsCollectTemplate::getTenantCode,tenantCode)
        .in(ActivitiesFieldsCollectTemplate::getId,id)
        .one();
  }

  /**
   * 通过id和租户编号查询
   * @param ids
   * @param tenantCode
   * @return
   */
  public List<ActivitiesFieldsCollectTemplate> listByIdsAndTenantCode(Set<String> ids, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesFieldsCollectTemplate::getTenantCode,tenantCode)
        .in(ActivitiesFieldsCollectTemplate::getId,ids)
        .list();
  }
}
