package com.biz.crm.tpm.business.activities.fields.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesFieldsCollectTemplateDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesFieldsCollectTemplateService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesFieldsCollectTemplateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * 活动采集字段模板(ActivitiesFieldsCollectTemplate)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
@RestController
@RequestMapping("/v1/activities/activitiesFieldsCollectTemplate")
@Slf4j
@Api(tags = "活动采集字段模板")
public class ActivitiesFieldsCollectTemplateController {
  /**
   * 服务对象
   */
  @Autowired
  private ActivitiesFieldsCollectTemplateService activitiesFieldsCollectTemplateService;

  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<ActivitiesFieldsCollectTemplateVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                          @ApiParam(name = "activitiesFieldsCollectTemplateDto", value = "活动采集字段模板信息") ActivitiesFieldsCollectTemplateDto dto) {
    try {
      Page<ActivitiesFieldsCollectTemplateVo> page = this.activitiesFieldsCollectTemplateService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<ActivitiesFieldsCollectTemplateVo> create(@RequestBody JSONObject json) {
    try {
      ActivitiesFieldsCollectTemplateVo result = this.activitiesFieldsCollectTemplateService.create(json);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "更新数据")
  @PatchMapping
  public Result<ActivitiesFieldsCollectTemplateVo> update(@RequestBody JSONObject json) {
    try {
      ActivitiesFieldsCollectTemplateVo result = this.activitiesFieldsCollectTemplateService.update(json);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "通过主键id查询基础数据")
  @GetMapping("findById")
  public Result<ActivitiesFieldsCollectTemplateVo> findById(@RequestParam("id") @ApiParam(name = "id", value = "主键id") String id) {
    try {
      ActivitiesFieldsCollectTemplateVo result = this.activitiesFieldsCollectTemplateService.findById(id);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "根据活动采集字段模板编码查询信息")
  @GetMapping("findByCode")
  public Result<ActivitiesFieldsCollectTemplateVo> findByCode(@RequestParam("code") @ApiParam(name = "code", value = "活动采集字段模板编码") String code) {
    try {
      ActivitiesFieldsCollectTemplateVo result = this.activitiesFieldsCollectTemplateService.findByCode(code);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "逻辑删除数据")
  @DeleteMapping
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") Set<String> idList) {
    try {
      this.activitiesFieldsCollectTemplateService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
