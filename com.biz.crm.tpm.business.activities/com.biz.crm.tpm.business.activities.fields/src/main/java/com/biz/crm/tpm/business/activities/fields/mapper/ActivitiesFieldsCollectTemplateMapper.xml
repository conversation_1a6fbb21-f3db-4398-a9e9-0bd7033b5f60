<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.fields.mapper.ActivitiesFieldsCollectTemplateMapper">
    <resultMap id="activitiesFieldsCollectTemplateVoMap" type="com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesFieldsCollectTemplateVo"/>

    <select id="findByConditions" resultMap="activitiesFieldsCollectTemplateVoMap">
        select
          t.*
        from tpm_activities_fields_collect_template t
        where t.tenant_code=#{dto.tenantCode}
        <if test="dto.code !=null and dto.code != '' ">
            and t.code = #{dto.code}
        </if>
        <if test="dto.dynamicFormCode !=null and dto.dynamicFormCode != '' ">
            and t.dynamic_form_code = #{dto.dynamicFormCode}
        </if>
        <if test="dto.delFlag !=null and dto.delFlag != '' ">
            and t.del_flag = #{dto.delFlag}
        </if>
        <if test="dto.btNo !=null and dto.btNo != '' ">
            and t.bt_no = #{dto.btNo}
        </if>
        order by t.create_time desc
    </select>
</mapper>

