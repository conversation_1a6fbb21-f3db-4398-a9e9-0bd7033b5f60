package com.biz.crm.tpm.business.activities.fields.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.common.form.sdk.DynamicFormService;
import com.biz.crm.tpm.business.activities.fields.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.fields.dto.ActivitiesFieldsCollectTemplateContextDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesFieldsCollectTemplateDto;
import com.biz.crm.tpm.business.activities.fields.entity.ActivitiesFieldsCollectTemplate;
import com.biz.crm.tpm.business.activities.fields.repository.ActivitiesFieldsCollectTemplateRepository;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesFieldsCollectTemplateService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesFieldsCollectTemplateVo;
import com.biz.crm.tpm.business.activities.dynamic.template.service.DynamicFormServiceResolver;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant.DYNAMIC_FORM_FIELD_CODE;

/**
 * 活动字段采集模板的主类处理
 */
@Service
public class ActivitiesFieldsCollectTemplateServiceImpl implements ActivitiesFieldsCollectTemplateService {

  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private ActivitiesFieldsCollectTemplateRepository activitiesFieldsCollectTemplateRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private DynamicFormServiceResolver dynamicFormServiceResolver;

  @Override
  public Page<ActivitiesFieldsCollectTemplateVo> findByConditions(Pageable pageable, ActivitiesFieldsCollectTemplateDto dto) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    if (dto == null) {
      dto = new ActivitiesFieldsCollectTemplateDto();
    }
    if(StringUtils.isBlank(dto.getTenantCode())){
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    if(StringUtils.isBlank(dto.getDelFlag())){
      dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    }
    return this.activitiesFieldsCollectTemplateRepository.findByConditions(pageable, dto);
  }

  @Override
  @Transactional
  public ActivitiesFieldsCollectTemplateVo create(JSONObject json) {
    ActivitiesFieldsCollectTemplateDto dto = this.createValidation(json);
    dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    //保存主表
    ActivitiesFieldsCollectTemplate entity = nebulaToolkitService.copyObjectByWhiteList(dto,ActivitiesFieldsCollectTemplate.class, HashSet.class, ArrayList.class);
    entity.setTenantCode(TenantUtils.getTenantCode());
    activitiesFieldsCollectTemplateRepository.save(entity);

    //保存明细
    String parentCode = entity.getCode();
    DynamicFormService<ActivitiesFieldsCollectTemplateVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(parentCode,entity.getDynamicFormCode(),DYNAMIC_FORM_FIELD_CODE,ActivitiesFieldsCollectTemplateVo.class);
    Validate.notNull(dynamicFormService,"根据提供的信息，未能获取到匹配的动态模板服务类信息");
    Class<?> calzz = dynamicFormServiceResolver.getDynamicFormClass(entity.getDynamicFormCode());
    Validate.notNull(calzz,"根据提供的动态表单编码【%s】，未能获取到动态模板类信息",entity.getDynamicFormCode());
    ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplateVo = nebulaToolkitService.copyObjectByWhiteList(entity, ActivitiesFieldsCollectTemplateVo.class,HashSet.class,ArrayList.class);

    //替换item的key为parentCode
    this.transfer(activitiesFieldsCollectTemplateVo,calzz);
    dynamicFormService.createDynamicDetails(activitiesFieldsCollectTemplateVo,parentCode);
    return activitiesFieldsCollectTemplateVo;
  }

  @Override
  public ActivitiesFieldsCollectTemplateVo update(JSONObject json) {
    ActivitiesFieldsCollectTemplateDto dto = this.updateValidation(json);
    ActivitiesFieldsCollectTemplateVo sourceActivitiesFieldsCollectTemplateVo = this.findByCode(dto.getCode());
    Validate.notNull(sourceActivitiesFieldsCollectTemplateVo,"根据活动采集字段模板编码【%s】，未能获取到相应信息",dto.getCode());
    ActivitiesFieldsCollectTemplateContextDto contextDto = this.buildActivityContextDto(sourceActivitiesFieldsCollectTemplateVo,dto,false);
    ActivitiesFieldsCollectTemplate dbActivitiesFieldsCollectTemplate =  nebulaToolkitService.copyObjectByWhiteList(sourceActivitiesFieldsCollectTemplateVo,ActivitiesFieldsCollectTemplate.class,HashSet.class,ArrayList.class);
    dbActivitiesFieldsCollectTemplate.setDynamicFormCode(dto.getDynamicFormCode());
    dbActivitiesFieldsCollectTemplate.setRemark(dto.getRemark());
    //保存明细
    return this.processDynamicFormsForUpdate(dbActivitiesFieldsCollectTemplate,contextDto);
  }

  @Override
  public ActivitiesFieldsCollectTemplateVo findByCode(String code) {
    if(StringUtils.isBlank(code)){
      return null;
    }
    //查询主表
    ActivitiesFieldsCollectTemplate activitiesFieldsCollectTemplate = activitiesFieldsCollectTemplateRepository.findByCodeAndTenantCode(code,TenantUtils.getTenantCode());
    if(activitiesFieldsCollectTemplate == null){
      return null;
    }

    DynamicFormService<ActivitiesFieldsCollectTemplateVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(activitiesFieldsCollectTemplate.getCode(),activitiesFieldsCollectTemplate.getDynamicFormCode(),DYNAMIC_FORM_FIELD_CODE,ActivitiesFieldsCollectTemplateVo.class);
    ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplateVo = nebulaToolkitService.copyObjectByWhiteList(activitiesFieldsCollectTemplate, ActivitiesFieldsCollectTemplateVo.class, HashSet.class, ArrayList.class);
    if(dynamicFormService == null){
      return activitiesFieldsCollectTemplateVo;
    }
    dynamicFormService.perfectDynamicDetails(activitiesFieldsCollectTemplateVo,activitiesFieldsCollectTemplateVo.getCode());
    return activitiesFieldsCollectTemplateVo;
  }

  @Override
  public List<ActivitiesFieldsCollectTemplateVo> findByActivityDetailCode(String activityDetailCode) {
    if(StringUtils.isBlank(activityDetailCode)){
      return Lists.newArrayList();
    }
    //查询主表
    List<ActivitiesFieldsCollectTemplate> activitiesFieldsCollectTemplates = activitiesFieldsCollectTemplateRepository.findByActivityDetailCodeAndTenantCode(activityDetailCode,TenantUtils.getTenantCode());
    if(CollectionUtils.isEmpty(activitiesFieldsCollectTemplates)){
      return Lists.newArrayList();
    }

    List<ActivitiesFieldsCollectTemplateVo> collectTemplateVos = Lists.newArrayList();
    for(ActivitiesFieldsCollectTemplate activitiesFieldsCollectTemplate : activitiesFieldsCollectTemplates){
      DynamicFormService<ActivitiesFieldsCollectTemplateVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(activitiesFieldsCollectTemplate.getCode(),activitiesFieldsCollectTemplate.getDynamicFormCode(),DYNAMIC_FORM_FIELD_CODE,ActivitiesFieldsCollectTemplateVo.class);
      ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplateVo = nebulaToolkitService.copyObjectByWhiteList(activitiesFieldsCollectTemplate, ActivitiesFieldsCollectTemplateVo.class, HashSet.class, ArrayList.class);
      if(dynamicFormService == null){
        continue;
      }
      dynamicFormService.perfectDynamicDetails(activitiesFieldsCollectTemplateVo,activitiesFieldsCollectTemplateVo.getCode());
      collectTemplateVos.add(activitiesFieldsCollectTemplateVo);
    }
    return collectTemplateVos;
  }

  @Override
  public ActivitiesFieldsCollectTemplateVo findById(String id) {
    if(StringUtils.isBlank(id)){
      return null;
    }
    //查询主表
    ActivitiesFieldsCollectTemplate activitiesFieldsCollectTemplate = activitiesFieldsCollectTemplateRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if(activitiesFieldsCollectTemplate == null){
      return null;
    }

    DynamicFormService<ActivitiesFieldsCollectTemplateVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(activitiesFieldsCollectTemplate.getCode(),activitiesFieldsCollectTemplate.getDynamicFormCode(),DYNAMIC_FORM_FIELD_CODE,ActivitiesFieldsCollectTemplateVo.class);
    ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplateVo = nebulaToolkitService.copyObjectByWhiteList(activitiesFieldsCollectTemplate, ActivitiesFieldsCollectTemplateVo.class, HashSet.class, ArrayList.class);
    if(dynamicFormService == null){
      return activitiesFieldsCollectTemplateVo;
    }
    dynamicFormService.perfectDynamicDetails(activitiesFieldsCollectTemplateVo,activitiesFieldsCollectTemplateVo.getCode());
    return activitiesFieldsCollectTemplateVo;
  }

  @Override
  public List<ActivitiesFieldsCollectTemplateVo> findByIds(Set<String> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Lists.newArrayList();
    }
    List<ActivitiesFieldsCollectTemplate> activitiesFieldsCollectTemplates = activitiesFieldsCollectTemplateRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    if(CollectionUtils.isEmpty(activitiesFieldsCollectTemplates)){
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(activitiesFieldsCollectTemplates, ActivitiesFieldsCollectTemplate.class,ActivitiesFieldsCollectTemplateVo.class, HashSet.class, ArrayList.class));
  }

  @Override
  public void delete(Set<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "主键集合不能为空！");
    List<ActivitiesFieldsCollectTemplateVo> activitiesFieldsCollectTemplateVos = this.findByIds(ids);
    Validate.notEmpty(activitiesFieldsCollectTemplateVos,"根据提供的主键集合信息，未能获取到相应数据");

    for(ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplate : activitiesFieldsCollectTemplateVos){
      String parentCode = activitiesFieldsCollectTemplate.getCode();
      DynamicFormService<ActivitiesFieldsCollectTemplateVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(activitiesFieldsCollectTemplate.getCode(),activitiesFieldsCollectTemplate.getDynamicFormCode(),DYNAMIC_FORM_FIELD_CODE,ActivitiesFieldsCollectTemplateVo.class);
      Validate.notNull(dynamicFormService,"根据提供的信息，未能获取到匹配的动态模板服务类信息");
      dynamicFormService.deleteDynamicDetails(parentCode);
    }
    activitiesFieldsCollectTemplateRepository.deleteByIds(Lists.newArrayList(ids));
  }

  private ActivitiesFieldsCollectTemplateDto createValidation(JSONObject json){
    Validate.notNull(json,"活动采集字段模板信息不能为空");
    ActivitiesFieldsCollectTemplateDto dto = JSONObject.parseObject(JSONObject.toJSONString(json),ActivitiesFieldsCollectTemplateDto.class);
    this.createValidation(dto);
    return dto;
  }


  private void createValidation(ActivitiesFieldsCollectTemplateDto dto){
    Validate.notNull(dto,"活动采集字段模板信息不能为空");
    if(StringUtils.isBlank(dto.getTenantCode())){
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    this.validateBase(dto);
    this.validateRepeatability(dto,true);
    // redis生成普通活动编码编码，编码规则为DEHD+年月日+5位顺序数。每天都从00001开始
    List<String> codeList = this.generateCodeService.generateCode(ActivitiesConstant.ACTIVITIES_FIELDS_COLLECT_TEMPLATE_RULE_CODE, 1);
    Validate.notEmpty(codeList, "添加信息时，生成活动采集字段模板编码失败！");
    dto.setCode(codeList.get(0));
    String pattern = "^[A-Z]{1}[A-Z0-9]*$";
    Validate.matchesPattern(dto.getCode(), pattern , "编码只能是字母和数字构成，且首字母不能是数字，最终编码都将被大写");
    ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplateVo = this.findByCode(dto.getCode());
    Validate.isTrue(activitiesFieldsCollectTemplateVo == null,"活动采集字段模板编码重复");
  }

  private void validateBase(ActivitiesFieldsCollectTemplateDto dto){
    Validate.notBlank(dto.getDynamicFormCode(),"关联的动态活动采集字段模板标识不能为空");
    Validate.notEmpty(dto.getItems(),"关联的动态活动采集字段模板不能为空");
    Validate.notBlank(dto.getTenantCode(),"租户编码不能为空");
    Validate.notBlank(dto.getBtNo(),"批次号不能为空");
    Validate.notBlank(dto.getActivityDetailCode(),"关联的明细活动编码不能为空");
  }

  private void validateRepeatability(ActivitiesFieldsCollectTemplateDto dto, boolean isAdd){
    ActivitiesFieldsCollectTemplate template = activitiesFieldsCollectTemplateRepository.findByBtNoAndTenantCode(dto.getBtNo(),TenantUtils.getTenantCode());
    if(isAdd){
      Validate.isTrue(template == null,"批次号【%s】重复，请检查",dto.getBtNo());
    }else{
      Validate.notNull(template,"根据提供的批次号【%s】，未能获取到相应信息",dto.getBtNo());
      Validate.isTrue(StringUtils.equals(template.getId(),dto.getId()),"根据提供的批次号【%s】，获取到数据与传入数据主键不一致，请检查",dto.getBtNo());
    }
  }

  private ActivitiesFieldsCollectTemplateDto updateValidation(JSONObject json){
    Validate.notNull(json,"活动采集字段模板信息不能为空");
    ActivitiesFieldsCollectTemplateDto dto = JSONObject.parseObject(JSONObject.toJSONString(json),ActivitiesFieldsCollectTemplateDto.class);
    this.updateValidation(dto);
    return dto;
  }

  private void updateValidation(ActivitiesFieldsCollectTemplateDto dto){
    Validate.notNull(dto,"活动采集字段模板信息不能为空");
    Validate.notBlank(dto.getId(),"更新时，主键id必须传入");
    ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplateVo = this.findById(dto.getId());
    Validate.notNull(activitiesFieldsCollectTemplateVo,"根据提供的id主键，未能获取到相应信息");
    Validate.isTrue(StringUtils.equals(dto.getCode(), activitiesFieldsCollectTemplateVo.getCode()),"传入的活动采集字段模板编码与数据信息不匹配，请检查");
    this.validateBase(dto);
    this.validateRepeatability(dto,false);
  }

  private ActivitiesFieldsCollectTemplateContextDto buildActivityContextDto(ActivitiesFieldsCollectTemplateVo sourceActivity, ActivitiesFieldsCollectTemplateDto targetActivity, boolean addOperate){
    ActivitiesFieldsCollectTemplateContextDto contextDto = new ActivitiesFieldsCollectTemplateContextDto();
    Validate.notNull(targetActivity,"最新活动信息不能为空");
    contextDto.setTargetActivity(targetActivity);
    if(!addOperate){
      Validate.notNull(sourceActivity,"历史活动信息不能为空");
      contextDto.setSourceActivity(sourceActivity);
    }
    return contextDto;
  }

  @Override
  public Map<String, BaseActivityItemVo> findByParentCode(String parentCode){
    if(StringUtils.isBlank(parentCode)){
      return Maps.newHashMap();
    }

    ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplateVo = this.findByCode(parentCode);
    if(activitiesFieldsCollectTemplateVo == null || CollectionUtils.isEmpty(activitiesFieldsCollectTemplateVo.getItems())){
      return Maps.newHashMap();
    }

    Map<String,BaseActivityItemVo> result = Maps.newHashMap();
    for(Map.Entry<String, BaseActivityItemVo> entry : activitiesFieldsCollectTemplateVo.getItems().entrySet()){
      BaseActivityItemVo itemVos = entry.getValue();
      result.put(entry.getKey(),itemVos);
    }
    return result;
  }

  private ActivitiesFieldsCollectTemplateVo processDynamicFormsForUpdate(ActivitiesFieldsCollectTemplate entity, ActivitiesFieldsCollectTemplateContextDto contextDto){
    String parentCode = entity.getCode();
    Map<String,BaseActivityItemVo> dbBasicItems = this.findByParentCode(parentCode);
    Validate.notEmpty(dbBasicItems,"根据活动字段采集编码【%s】，未能获取到相应明细信息",parentCode);
    //注意：比较是dynamicKey，是item map的key键值。。
    Set<String> dbDynamicKeys = Sets.newHashSet(dbBasicItems.keySet());
    Set<String> dynamicKeys = Sets.newHashSet(contextDto.getTargetActivity().getItems().keySet());
    ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplateVo = nebulaToolkitService.copyObjectByWhiteList(contextDto.getTargetActivity(),ActivitiesFieldsCollectTemplateVo.class,HashSet.class,ArrayList.class);
    Class<?> calzz = dynamicFormServiceResolver.getDynamicFormClass(entity.getDynamicFormCode());
    Validate.notNull(calzz,"根据提供的动态表单编码【%s】，未能获取到动态模板类信息",entity.getDynamicFormCode());
    //处理可能的删除情况（对整个动态表单的明细信息进行删除）
    Set<String> needDeleteDynamicForms = Sets.difference(dbDynamicKeys,dynamicKeys);
    if(!CollectionUtils.isEmpty(needDeleteDynamicForms)){
      for(String dynamicKey : needDeleteDynamicForms){
        DynamicFormService<ActivitiesFieldsCollectTemplateVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(entity.getCode(),entity.getDynamicFormCode(),DYNAMIC_FORM_FIELD_CODE,ActivitiesFieldsCollectTemplateVo.class);
        Validate.notNull(dynamicFormService,"根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息",dynamicKey);
        //注意此处删除就是全部删除了，因为该动态模板的明细只能一条明细记录，所以删除就一定是全部删除
        dynamicFormService.deleteDynamicDetails(parentCode);
      }
    }
    //处理可能的新增（对某个动态表单的明细新增）
    Set<String> needAddDynamicKeys = Sets.difference(dynamicKeys,dbDynamicKeys);
    if(!CollectionUtils.isEmpty(needAddDynamicKeys)){
      for(String dynamicKey : needAddDynamicKeys){
        DynamicFormService<ActivitiesFieldsCollectTemplateVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(activitiesFieldsCollectTemplateVo.getCode(),activitiesFieldsCollectTemplateVo.getDynamicFormCode(),DYNAMIC_FORM_FIELD_CODE,ActivitiesFieldsCollectTemplateVo.class);
        Validate.notNull(dynamicFormService,"根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息",dynamicKey);
        this.transfer(activitiesFieldsCollectTemplateVo,calzz);
        dynamicFormService.createDynamicDetails(activitiesFieldsCollectTemplateVo,activitiesFieldsCollectTemplateVo.getCode());
      }
    }

    //处理可能的更新
    Set<String> needUpdateDynamicKeys = Sets.intersection(dynamicKeys,dbDynamicKeys);
    if(!CollectionUtils.isEmpty(needUpdateDynamicKeys)){
      for(String dynamicKey : needUpdateDynamicKeys){
        DynamicFormService<ActivitiesFieldsCollectTemplateVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(activitiesFieldsCollectTemplateVo.getCode(),activitiesFieldsCollectTemplateVo.getDynamicFormCode(),DYNAMIC_FORM_FIELD_CODE,ActivitiesFieldsCollectTemplateVo.class);
        Validate.notNull(dynamicFormService,"根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息",dynamicKey);
        this.transfer(activitiesFieldsCollectTemplateVo,calzz);
        dynamicFormService.modifyDynamicDetails(activitiesFieldsCollectTemplateVo,parentCode);
      }
    }
    return activitiesFieldsCollectTemplateVo;
  }

  /**
   * 替换item的key为parentCode，并做数据类型转换
   * @param activitiesFieldsCollectTemplateVo 主类信息
   * @param calzz 动态采集字段模板类
   */
  private void transfer(ActivitiesFieldsCollectTemplateVo activitiesFieldsCollectTemplateVo, Class<?> calzz){
    Map<String,BaseActivityItemVo> itemMap = activitiesFieldsCollectTemplateVo.getItems();
    String key = itemMap.keySet().iterator().next();
    itemMap.put(activitiesFieldsCollectTemplateVo.getCode(), (BaseActivityItemVo)JSONObject.parseObject(JSONObject.toJSONString(itemMap.remove(key)),calzz));
  }
}
