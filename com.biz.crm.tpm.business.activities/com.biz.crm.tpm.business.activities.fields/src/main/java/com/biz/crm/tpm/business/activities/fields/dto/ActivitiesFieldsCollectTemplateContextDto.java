package com.biz.crm.tpm.business.activities.fields.dto;

import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesFieldsCollectTemplateDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesFieldsCollectTemplateVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "ActivitiesFieldsCollectTemplateContextDto", description = "专用于活动采集字段模板与动态表单间的上下文信息传参")
public class ActivitiesFieldsCollectTemplateContextDto {

  @ApiModelProperty("最新信息")
  private ActivitiesFieldsCollectTemplateDto targetActivity;

  @ApiModelProperty("历史信息")
  private ActivitiesFieldsCollectTemplateVo sourceActivity;

}
