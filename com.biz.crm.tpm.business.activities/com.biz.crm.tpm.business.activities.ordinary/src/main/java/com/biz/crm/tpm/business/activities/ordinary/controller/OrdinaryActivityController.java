package com.biz.crm.tpm.business.activities.ordinary.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityDto;
import com.biz.crm.tpm.business.activities.ordinary.service.OrdinaryActivityService;
import com.biz.crm.tpm.business.activities.ordinary.service.observer.OrdinaryActivityProcessCallBackListener;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityVo;
import com.biz.crm.workflow.sdk.dto.ProcessStatusDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * 普通活动(OrdinaryActivity)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
@RestController
@RequestMapping("/v1/activities/ordinaryActivity")
@Slf4j
@Api(tags = "普通活动")
public class OrdinaryActivityController {
  /**
   * 服务对象
   */
  @Autowired
  private OrdinaryActivityService ordinaryActivityService;
  @Autowired
  private OrdinaryActivityProcessCallBackListener ordinaryActivityProcessCallBackListener;

  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<OrdinaryActivityVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                           @ApiParam(name = "ordinaryActivityDto", value = "核销采集信息") OrdinaryActivityDto dto) {
    try {
      Page<OrdinaryActivityVo> page = this.ordinaryActivityService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<OrdinaryActivityVo> create(@RequestBody JSONObject json) {
    try {
      OrdinaryActivityVo result = this.ordinaryActivityService.create(json);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "更新数据")
  @PatchMapping
  public Result<OrdinaryActivityVo> update(@RequestBody JSONObject json) {
    try {
      OrdinaryActivityVo result = this.ordinaryActivityService.update(json);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "通过主键id查询基础数据")
  @GetMapping("findById")
  public Result<OrdinaryActivityVo> findById(@RequestParam("id") @ApiParam(name = "id", value = "主键id") String id) {
    try {
      OrdinaryActivityVo result = this.ordinaryActivityService.findById(id);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "根据普通活动编码查询信息")
  @GetMapping("findByCode")
  public Result<OrdinaryActivityVo> findByCode(@RequestParam("code") @ApiParam(name = "code", value = "普通活动编码") String code) {
    try {
      OrdinaryActivityVo result = this.ordinaryActivityService.findByCode(code);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "逻辑删除数据")
  @DeleteMapping
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") Set<String> idList) {
    try {
      this.ordinaryActivityService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "手动触发工作流回调")
  @PatchMapping("callback")
  public Result<?> callback(@RequestBody ProcessStatusDto dto) {
    try {
      this.ordinaryActivityProcessCallBackListener.onProcessComplete(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
