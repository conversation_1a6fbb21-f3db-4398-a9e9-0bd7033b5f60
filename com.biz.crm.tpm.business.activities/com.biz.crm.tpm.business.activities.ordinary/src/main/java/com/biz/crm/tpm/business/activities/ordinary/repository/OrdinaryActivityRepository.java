package com.biz.crm.tpm.business.activities.ordinary.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityDto;
import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivity;
import com.biz.crm.tpm.business.activities.ordinary.mapper.OrdinaryActivityMapper;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityVo;
import com.biz.crm.tpm.business.activities.sdk.enums.ActivityStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 普通活动(OrdinaryActivity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class OrdinaryActivityRepository extends ServiceImpl<OrdinaryActivityMapper, OrdinaryActivity> {

  @Autowired
  private OrdinaryActivityMapper ordinaryActivityMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<OrdinaryActivityVo> findByConditions(Pageable pageable, OrdinaryActivityDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<OrdinaryActivityVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return ordinaryActivityMapper.findByConditions(page, dto);
  }

  /**
   * 根据编码查询信息
   */
  public OrdinaryActivity findByCodeAndTenantCode(String code) {
    return this.lambdaQuery().eq(OrdinaryActivity::getCode, code).eq(OrdinaryActivity::getTenantCode, TenantUtils.getTenantCode()).eq(OrdinaryActivity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
  }

  /**
   * 根据编码集合查询信息
   */
  public List<OrdinaryActivity> findByCodesAndTenantCode(Set<String> codes) {
    return this.lambdaQuery().in(OrdinaryActivity::getCode, codes).eq(OrdinaryActivity::getTenantCode, TenantUtils.getTenantCode()).eq(OrdinaryActivity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
  }


  /**
   * 根据主键id集合，删除数据
   */
  public void deleteByIds(List<String> ids) {
    this.lambdaUpdate().in(OrdinaryActivity::getId, ids).
        set(OrdinaryActivity::getDelFlag, DelFlagStatusEnum.DELETE.getCode()).update();
  }

  /**
   * 根据活动大类编码，统计信息
   */
  public int countByCostTypeCategoryCodeAndTenantCode(String costTypeCategoryCode) {
    return this.lambdaQuery().eq(OrdinaryActivity::getCostTypeCategoryCode, costTypeCategoryCode).
        eq(OrdinaryActivity::getTenantCode, TenantUtils.getTenantCode()).
        eq(OrdinaryActivity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).
        count();
  }

  /**
   * 根据提供的参数，更新status
   *
   * @param code        主表编码
   * @param isAllClosed 是否全部关闭，true表示全部关闭，false表示部分关闭
   */
  public void updateForClose(String code, Boolean isAllClosed) {
    ActivityStatusEnum status = isAllClosed ? ActivityStatusEnum.ALL_CLOSE : ActivityStatusEnum.PARTIAL_CLOSE;
    this.lambdaUpdate().eq(OrdinaryActivity::getCode, code).eq(OrdinaryActivity::getTenantCode,TenantUtils.getTenantCode()).set(OrdinaryActivity::getStatus, status.getCode()).update();
  }

  /**
   * 查询待数据刷新的数据，该方法针对定时任务的整体查询
   */
  public List<OrdinaryActivity> findByRefreshStatusTask() {
    return this.lambdaQuery().notIn(OrdinaryActivity::getStatus, Sets.newHashSet(ActivityStatusEnum.ALL_CLOSE.getCode(),
        ActivityStatusEnum.PARTIAL_CLOSE.getCode()
        , ActivityStatusEnum.ENDED.getCode())).
        eq(OrdinaryActivity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).
        eq(OrdinaryActivity::getTenantCode, TenantUtils.getTenantCode()).
        eq(OrdinaryActivity::getEnableStatus, EnableStatusEnum.ENABLE.getCode()).list();
  }

  public OrdinaryActivity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(OrdinaryActivity::getTenantCode,tenantCode)
        .in(OrdinaryActivity::getId,id)
        .one();
  }

  public List<OrdinaryActivity> listByIdsAndTenantCode(Set<String> ids, String tenantCode) {
    return this.lambdaQuery()
        .eq(OrdinaryActivity::getTenantCode,tenantCode)
        .in(OrdinaryActivity::getId,ids)
        .list();
  }
}

