package com.biz.crm.tpm.business.activities.ordinary.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "OrdinaryActivityDto", description = "普通活动参数信息")
public class OrdinaryActivityDto extends TenantFlagOpDto {
  @ApiModelProperty("活动编码")
  private String code;

  @ApiModelProperty("活动名称")
  private String name;

  @ApiModelProperty("活动开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  @ApiModelProperty("活动结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty("费用预算编码")
  private String costBudgetCode;

  @ApiModelProperty("预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("活动大类编码")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  private String costTypeCategoryName;

  @ApiModelProperty("总申请金额")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("租户编码")
  private String tenantCode;

  @ApiModelProperty("最终可用余额")
  private BigDecimal finalBalance;

  @ApiModelProperty("活动状态")
  private String status;

  @ApiModelProperty(value = "活动附件信息")
  private List<OrdinaryActivityFilesDto> activityFiles;

  @ApiModelProperty("普通活动明细")
  private Map<String, List<?>> items;

  @ApiModelProperty("工作流对象")
  private ProcessBusinessDto processBusiness;

  @ApiModelProperty("关联信息")
  private List<OrdinaryActivityRelationDto> relations;
}
