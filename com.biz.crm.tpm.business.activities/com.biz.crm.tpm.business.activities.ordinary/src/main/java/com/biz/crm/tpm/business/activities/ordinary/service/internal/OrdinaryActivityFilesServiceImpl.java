package com.biz.crm.tpm.business.activities.ordinary.service.internal;

import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityFilesDto;
import com.biz.crm.tpm.business.activities.ordinary.repository.OrdinaryActivityFilesRepository;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityFilesVo;
import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivityFiles;
import com.biz.crm.tpm.business.activities.ordinary.service.OrdinaryActivityFilesService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;

@Service
public class OrdinaryActivityFilesServiceImpl implements OrdinaryActivityFilesService {

  @Autowired
  private OrdinaryActivityFilesRepository ordinaryActivityFilesRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  @Transactional
  public void save(List<OrdinaryActivityFilesDto> filesDtos, String activityCode) {
    Validate.notBlank(activityCode,"普通活动编码不能为空");
    if(!CollectionUtils.isEmpty(filesDtos)){
      for(OrdinaryActivityFilesDto filesDto : filesDtos){
        Validate.notBlank(filesDto.getFileCode(),"普通活动文件唯一识别号不能为空");
      }
      filesDtos.forEach(e -> {
        e.setTenantCode(TenantUtils.getTenantCode());
        e.setId(null);
        e.setActivityCode(activityCode);
      });
    }
    //暴力删除所有数据，再保存附件信息
    ordinaryActivityFilesRepository.deleteByActivityCodeAndTenantCode(activityCode,TenantUtils.getTenantCode());
    if(!CollectionUtils.isEmpty(filesDtos)){
      //保存文件信息
      Collection<OrdinaryActivityFiles> entities = nebulaToolkitService.copyCollectionByWhiteList(filesDtos,OrdinaryActivityFilesDto.class,OrdinaryActivityFiles.class, LinkedHashSet.class, ArrayList.class);
      entities.forEach(e->e.setTenantCode(TenantUtils.getTenantCode()));
      ordinaryActivityFilesRepository.saveBatch(entities);
    }
  }

  @Override
  public List<OrdinaryActivityFilesVo> findByActivityCode(String activityCode) {
    if(StringUtils.isBlank(activityCode)){
      return Lists.newArrayList();
    }
    List<OrdinaryActivityFiles> entities = ordinaryActivityFilesRepository.findByActivityCodeAndTenantCode(activityCode,TenantUtils.getTenantCode());
    if(CollectionUtils.isEmpty(entities)){
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities,OrdinaryActivityFiles.class,OrdinaryActivityFilesVo.class,LinkedHashSet.class,ArrayList.class));
  }

}
