package com.biz.crm.tpm.business.activities.ordinary.service.internal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.common.form.sdk.DynamicFormService;
import com.biz.crm.common.form.sdk.context.DynamicFormContext;
import com.biz.crm.common.form.sdk.context.DynamicFormContextHolder;
import com.biz.crm.tpm.business.activities.dynamic.template.enums.FormTypeEnum;
import com.biz.crm.tpm.business.activities.dynamic.template.service.DynamicFormServiceResolver;
import com.biz.crm.tpm.business.activities.ordinary.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityContextDto;
import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityDto;
import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityLogEventDto;
import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityRelationDto;
import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivity;
import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivityRelation;
import com.biz.crm.tpm.business.activities.ordinary.event.OrdinaryActivityLogEventListener;
import com.biz.crm.tpm.business.activities.ordinary.repository.OrdinaryActivityRelationRepository;
import com.biz.crm.tpm.business.activities.ordinary.repository.OrdinaryActivityRepository;
import com.biz.crm.tpm.business.activities.ordinary.service.OrdinaryActivityFilesService;
import com.biz.crm.tpm.business.activities.ordinary.service.OrdinaryActivityRelationService;
import com.biz.crm.tpm.business.activities.ordinary.service.OrdinaryActivityService;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityFilesVo;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityRelationVo;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityVo;
import com.biz.crm.tpm.business.activities.sdk.enums.ActivityStatusEnum;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.BasicActivitiesInfoService;
import com.biz.crm.tpm.business.activities.sdk.strategy.close.ActivityItemsClosedStrategy;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BasicActivityItemVo;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessMappingDto;
import com.biz.crm.workflow.sdk.service.ProcessBusinessMappingService;
import com.biz.crm.workflow.sdk.service.ProcessBusinessService;
import com.biz.crm.workflow.sdk.vo.ProcessBusinessMappingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.activities.ordinary.constant.ActivitiesConstant.ORDINARYACTIVITY_RULE_CODE;
import static com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant.DYNAMIC_FORM_FIELD_CODE;

@Service("_OrdinaryActivityServiceImpl")
public class OrdinaryActivityServiceImpl implements OrdinaryActivityService, BasicActivitiesInfoService {

  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private OrdinaryActivityRepository ordinaryActivityRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private DynamicFormServiceResolver dynamicFormServiceResolver;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired(required = false)
  private List<ActivitiesEventListener> activitiesEventListeners;
  @Autowired
  private OrdinaryActivityFilesService ordinaryActivityFilesService;

  @Autowired
  private OrdinaryActivityRelationRepository ordinaryActivityRelationRepository;
  @Autowired
  private OrdinaryActivityRelationService ordinaryActivityRelationService;
  @Autowired(required = false)
  private List<ActivityItemsClosedStrategy> activitiesClosedStrategies;
  @Autowired(required = false)
  private ProcessBusinessService processBusinessService;
  @Autowired(required = false)
  private ProcessBusinessMappingService processBusinessMappingService;


  @Override
  public Page<OrdinaryActivityVo> findByConditions(Pageable pageable, OrdinaryActivityDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    if (dto == null) {
      dto = new OrdinaryActivityDto();
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    if (StringUtils.isBlank(dto.getDelFlag())) {
      dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    }
    return this.ordinaryActivityRepository.findByConditions(pageable, dto);
  }


  @Override
  @Transactional
  public OrdinaryActivityVo create(JSONObject json) {
    OrdinaryActivityDto dto = this.createValidation(json);
    dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    //保存主表
    OrdinaryActivity entity = nebulaToolkitService.copyObjectByWhiteList(dto, OrdinaryActivity.class, HashSet.class, ArrayList.class, "activityFiles", "attachmentVos","relations");
    entity.setStatus(this.analysisStatus(dto.getStartTime(), dto.getEndTime()).getCode());
    entity.setTenantCode(TenantUtils.getTenantCode());
    ordinaryActivityRepository.save(entity);
    //2. ====保存关联信息
    entity.getRelations().forEach(e -> e.setId(null));
    entity.getRelations().forEach(e->e.setTenantCode(TenantUtils.getTenantCode()));
    ordinaryActivityRelationRepository.saveBatch(entity.getRelations());
    //保存可能的附件信息
    if (!CollectionUtils.isEmpty(dto.getActivityFiles())) {
      ordinaryActivityFilesService.save(dto.getActivityFiles(), entity.getCode());
    }
    //保存明细
    String parentCode = entity.getCode();
    Set<DynamicFormService<OrdinaryActivityVo>> dynamicFormServices = dynamicFormServiceResolver.getDynamicFormServices(json, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class, FormTypeEnum.APPLY);
    Validate.notEmpty(dynamicFormServices, "根据提供的信息，未能获取到匹配的动态模板服务类信息");
    OrdinaryActivityVo ordinaryActivityVo = nebulaToolkitService.copyObjectByWhiteList(entity, OrdinaryActivityVo.class, HashSet.class, ArrayList.class, "attachmentVos");
    try {
      OrdinaryActivityContextDto contextDto = this.buildActivityContextDto(null, dto, true);
      DynamicFormContext context = this.prepareDynamicFormContext(contextDto);
      for (DynamicFormService<OrdinaryActivityVo> dynamicFormService : dynamicFormServices) {
        dynamicFormService.createDynamicDetails(ordinaryActivityVo, parentCode);
      }      //验证总金额
      this.validateTotalApplyAmount(context);
    } finally {
      DynamicFormContextHolder.clearContext();
    }
    //工作流
    if (dto.getProcessBusiness() != null) {
      //费用预算占用
      OrdinaryActivityVo activityVo = this.findByCode(entity.getCode());
      for (Map.Entry<String, List<BaseActivityItemVo>> item : activityVo.getItems().entrySet()) {
        List<BasicActivityItemVo> basicItems = JSONArray.parseArray(JSONArray.toJSONString(item.getValue()), BasicActivityItemVo.class);
        Validate.notEmpty(basicItems, "明细信息不能为空");
        for (BasicActivityItemVo basicItem : basicItems) {
          costBudgetVoService.occupy(parentCode, basicItem.getItemCode(), basicItem.getCostBudgetCode(), basicItem.getApplyAmount(), basicItem.getRemark(), CostBudgetItemSourceType.ORDINARY_ACTIVITY.getDescr());
        }
      }
      //接入工作流
      dto.setCode(entity.getCode());
      dto.setId(entity.getId());
      dto.setTenantCode(entity.getTenantCode());
      this.commitProcess(dto);
      ordinaryActivityRepository.saveOrUpdate(entity);
    }

    //新增业务日志
    OrdinaryActivityLogEventDto logEventDto = new OrdinaryActivityLogEventDto();
    logEventDto.setOriginal(null);
    logEventDto.setNewest(ordinaryActivityVo);
    SerializableBiConsumer<OrdinaryActivityLogEventListener, OrdinaryActivityLogEventDto> onCreate =
            OrdinaryActivityLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(logEventDto, OrdinaryActivityLogEventListener.class, onCreate);
    return ordinaryActivityVo;
  }


  @Override
  @Transactional
  public OrdinaryActivityVo update(JSONObject json) {
    OrdinaryActivityDto dto = this.updateValidation(json);
    OrdinaryActivityVo sourceOrdinaryActivityVo = this.findByCode(dto.getCode());
    Validate.notNull(sourceOrdinaryActivityVo, "根据活动编码【%s】，未能获取到相应信息", dto.getCode());
    OrdinaryActivityContextDto contextDto = this.buildActivityContextDto(sourceOrdinaryActivityVo, dto, false);
    OrdinaryActivity dbOrdinaryActivity = nebulaToolkitService.copyObjectByWhiteList(sourceOrdinaryActivityVo, OrdinaryActivity.class, HashSet.class, ArrayList.class);
    //保存明细
    OrdinaryActivityVo result = this.processDynamicFormsForUpdate(dbOrdinaryActivity, contextDto);
    dbOrdinaryActivity.setBudgetSubjectCode(dto.getBudgetSubjectCode());
    dbOrdinaryActivity.setBudgetSubjectName(dto.getBudgetSubjectName());
    dbOrdinaryActivity.setCostBudgetCode(dto.getCostBudgetCode());
    dbOrdinaryActivity.setCostTypeCategoryCode(dto.getCostTypeCategoryCode());
    dbOrdinaryActivity.setCostTypeCategoryName(dto.getCostTypeCategoryName());
    dbOrdinaryActivity.setEndTime(dto.getEndTime());
    dbOrdinaryActivity.setName(dto.getName());
    dbOrdinaryActivity.setRemark(dto.getRemark());
    dbOrdinaryActivity.setStartTime(dto.getStartTime());
    dbOrdinaryActivity.setTotalApplyAmount(dto.getTotalApplyAmount());
    dbOrdinaryActivity.setStatus(this.analysisStatus(dto.getStartTime(), dto.getEndTime()).getCode());
    dbOrdinaryActivity.setTenantCode(TenantUtils.getTenantCode());
    ordinaryActivityRepository.saveOrUpdate(dbOrdinaryActivity);
    //保存可能的附件信息
    if (!CollectionUtils.isEmpty(dto.getActivityFiles())) {
      ordinaryActivityFilesService.save(dto.getActivityFiles(), dbOrdinaryActivity.getCode());
    }
    //2.=====保存关联信息
    List<OrdinaryActivityRelation> dbOrdinaryActivityRelations = ordinaryActivityRelationRepository.findByActivityCodeAndTenantCode(dbOrdinaryActivity.getCode(), dbOrdinaryActivity.getTenantCode());
    dbOrdinaryActivity.setRelations(dbOrdinaryActivityRelations);
    Set<String> currentIds = dto.getRelations().stream().map(OrdinaryActivityRelationDto::getId).collect(Collectors.toSet());
    Set<String> dbIds = dbOrdinaryActivityRelations.stream().map(OrdinaryActivityRelation::getId).collect(Collectors.toSet());
    //3.=====处理关联信息删除情况
    Set<String> needDeletes = Sets.difference(dbIds, currentIds);
    if (!CollectionUtils.isEmpty(needDeletes)) {
      ordinaryActivityRelationRepository.removeByIdsAndTenantCodes(needDeletes,TenantUtils.getTenantCode());
      dbOrdinaryActivity.getRelations().addAll(dbOrdinaryActivityRelations.stream().filter(e -> !needDeletes.contains(e.getId())).collect(Collectors.toList()));
    }
    //处理关联信息新增情况
    List<OrdinaryActivityRelationDto> needAdds = dto.getRelations().stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(needAdds)) {
      List<OrdinaryActivityRelation> relations = Lists.newArrayList();
      for (OrdinaryActivityRelationDto e : needAdds) {
        e.setId(null);
        e.setActivityCode(dbOrdinaryActivity.getCode());
        e.setTenantCode(dbOrdinaryActivity.getTenantCode());
        OrdinaryActivityRelation relation = nebulaToolkitService.copyObjectByWhiteList(e, OrdinaryActivityRelation.class, HashSet.class, ArrayList.class);
        relation.setTenantCode(TenantUtils.getTenantCode());
        ordinaryActivityRelationRepository.save(relation);
        relations.add(relation);
      }
      dbOrdinaryActivity.getRelations().addAll(relations);
    }
    OrdinaryActivityVo activityVo = this.findByCode(dbOrdinaryActivity.getCode());
    //工作流
    if (dto.getProcessBusiness() != null) {
      //费用预算占用
      for (Map.Entry<String, List<BaseActivityItemVo>> item : activityVo.getItems().entrySet()) {
        List<BasicActivityItemVo> basicItems = JSONArray.parseArray(JSONArray.toJSONString(item.getValue()), BasicActivityItemVo.class);
        Validate.notEmpty(basicItems, "明细信息不能为空");
        basicItems.forEach(e -> costBudgetVoService.occupy(dbOrdinaryActivity.getCode(), e.getItemCode(), e.getCostBudgetCode(), e.getApplyAmount(), e.getRemark(), CostBudgetItemSourceType.ORDINARY_ACTIVITY.getDescr()));
      }
      //接入工作流
      this.commitProcess(dto);
    }
    OrdinaryActivityVo ordinaryActivityVo = this.nebulaToolkitService.copyObjectByWhiteList(dbOrdinaryActivity, OrdinaryActivityVo.class, LinkedHashSet.class, ArrayList.class);
    //编辑业务日志
    OrdinaryActivityLogEventDto logEventDto = new OrdinaryActivityLogEventDto();
    logEventDto.setOriginal(sourceOrdinaryActivityVo);
    logEventDto.setNewest(ordinaryActivityVo);
    SerializableBiConsumer<OrdinaryActivityLogEventListener, OrdinaryActivityLogEventDto> onUpdate =
            OrdinaryActivityLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(logEventDto, OrdinaryActivityLogEventListener.class, onUpdate);
    return result;
  }

  @Override
  public OrdinaryActivityVo findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    //查询主表
    OrdinaryActivity ordinaryActivity = ordinaryActivityRepository.findByCodeAndTenantCode(code);
    if (ordinaryActivity == null) {
      return null;
    }
    //2.====查询关联信息 附件
    List<OrdinaryActivityFilesVo> ordinaryActivityFiles = ordinaryActivityFilesService.findByActivityCode(code);
    List<OrdinaryActivityRelationVo> projectActivityRelations = ordinaryActivityRelationService.findByActivityCode(ordinaryActivity.getCode());
    if (CollectionUtils.isEmpty(projectActivityRelations)) {
      return null;
    }
    OrdinaryActivityVo ordinaryActivityVo = nebulaToolkitService.copyObjectByWhiteList(ordinaryActivity, OrdinaryActivityVo.class, HashSet.class, ArrayList.class);
    ordinaryActivityVo.setActivityFiles(ordinaryActivityFiles);
    ordinaryActivityVo.setRelations(projectActivityRelations);
    //3.====查询动态模板明细
    Set<String> dynamicKeys = projectActivityRelations.stream().filter(e -> StringUtils.isNotBlank(e.getCostTypeDetailCode())).map(e -> StringUtils.joinWith(":", e.getCostBudgetCode(), e.getCostTypeDetailCode())).collect(Collectors.toSet());
    for (String dynamicKey : dynamicKeys) {
      DynamicFormService<OrdinaryActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class,FormTypeEnum.APPLY);
      if (dynamicFormService == null) {
        continue;
      }
      dynamicFormService.perfectDynamicDetails(ordinaryActivityVo, ordinaryActivity.getCode());
    }
    //4.====是否占用预算标记
    CostBudgetVo costBudgetVo = costBudgetVoService.findByCode(ordinaryActivity.getCostBudgetCode());
    if (costBudgetVo != null) {
      ordinaryActivityVo.setFinalBalance(costBudgetVo.getFinalBalance());
    }
    return ordinaryActivityVo;
  }

  @Override
  public List<OrdinaryActivityVo> findByCodes(Set<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return Lists.newArrayList();
    }
    List<OrdinaryActivity> ordinaryActivities = ordinaryActivityRepository.findByCodesAndTenantCode(codes);
    if (CollectionUtils.isEmpty(ordinaryActivities)) {
      return Lists.newArrayList();
    }

    List<OrdinaryActivityVo> result = Lists.newArrayList();
    //todo 需要处理
//    for (OrdinaryActivity ordinaryActivity : ordinaryActivities) {
//      String dynamicKey = StringUtils.joinWith(":", ordinaryActivity.getCostBudgetCode(), ordinaryActivity.getCostTypeCategoryCode());
//      DynamicFormService<OrdinaryActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class);
//      OrdinaryActivityVo ordinaryActivityVo = nebulaToolkitService.copyObjectByWhiteList(ordinaryActivity, OrdinaryActivityVo.class, HashSet.class, ArrayList.class);
//      if (dynamicFormService == null) {
//        continue;
//      }
//      dynamicFormService.perfectDynamicDetails(ordinaryActivityVo, ordinaryActivity.getCode());
//      result.add(ordinaryActivityVo);
//    }
    return result;
  }


  @Override
  public OrdinaryActivityVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    //查询主表
    OrdinaryActivity ordinaryActivity = ordinaryActivityRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (ordinaryActivity == null) {
      return null;
    }
    //查询活动附件 关联信息
    List<OrdinaryActivityFilesVo> ordinaryActivityFiles = ordinaryActivityFilesService.findByActivityCode(ordinaryActivity.getCode());
    List<OrdinaryActivityRelationVo> ordinaryActivityRelations = ordinaryActivityRelationService.findByActivityCode(ordinaryActivity.getCode());
    if (CollectionUtils.isEmpty(ordinaryActivityRelations)) {
      return null;
    }
    OrdinaryActivityVo ordinaryActivityVo = nebulaToolkitService.copyObjectByWhiteList(ordinaryActivity, OrdinaryActivityVo.class, HashSet.class, ArrayList.class);
    ordinaryActivityVo.setActivityFiles(ordinaryActivityFiles);
    ordinaryActivityVo.setRelations(ordinaryActivityRelations);
    //3.====查询动态模板明细
    Set<String> dynamicKeys = ordinaryActivityRelations.stream().filter(e -> StringUtils.isNotBlank(e.getCostTypeDetailCode())).map(e -> StringUtils.joinWith(":", e.getCostBudgetCode(), e.getCostTypeDetailCode())).collect(Collectors.toSet());
    for (String dynamicKey : dynamicKeys) {
      DynamicFormService<OrdinaryActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class,FormTypeEnum.APPLY);
      if (dynamicFormService == null) {
        continue;
      }
      dynamicFormService.perfectDynamicDetails(ordinaryActivityVo, ordinaryActivityVo.getCode());
    }
    CostBudgetVo costBudgetVo = costBudgetVoService.findByCode(ordinaryActivity.getCostBudgetCode());
    if (costBudgetVo != null) {
      ordinaryActivityVo.setFinalBalance(costBudgetVo.getFinalBalance());
    }
    return ordinaryActivityVo;
  }


  @Override
  public List<OrdinaryActivityVo> findByIds(Set<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<OrdinaryActivity> ordinaryActivities = ordinaryActivityRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(ordinaryActivities)) {
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(ordinaryActivities, OrdinaryActivity.class, OrdinaryActivityVo.class, HashSet.class, ArrayList.class));
  }

  @Override
  @Transactional
  public void delete(Set<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "主键集合不能为空！");
    List<OrdinaryActivityVo> ordinaryActivityVos = this.findByIds(ids);//获取普通活动
    Validate.notEmpty(ordinaryActivityVos, "根据提供的主键集合信息，未能获取到相应数据");
    //执行删除普通活动（主表逻辑删除，明细项数据真删除）
    List<String> auditCodes = ordinaryActivityVos.stream().map(OrdinaryActivityVo::getCode).collect(Collectors.toList());
    List<ProcessBusinessMappingVo> processBusinessMappingVoList = this.findProcessBusinessMappingVo(auditCodes);
    if (CollectionUtils.isEmpty(processBusinessMappingVoList)) {
      Map<String, String> map = ordinaryActivityVos.stream().collect(Collectors.toMap(OrdinaryActivityVo::getCode, OrdinaryActivityVo::getName));
      processBusinessMappingVoList.forEach(item -> {
        throw new RuntimeException("【" + map.get(item.getBusinessNo()) + "】【" + item.getBusinessNo() + "】不是待提交状态，不能进行删除操作");
      });
    }
    for (OrdinaryActivityVo ordinaryActivity : ordinaryActivityVos) {
      String parentCode = ordinaryActivity.getCode();//普通活动编码
      //获取申请活动关联信息
      List<OrdinaryActivityRelation> ordinaryActivityRelationList =  ordinaryActivityRelationRepository.findByActivityCode(ordinaryActivity.getCode(), TenantUtils.getTenantCode());
      if (!CollectionUtils.isEmpty(ordinaryActivityRelationList)) {
        ordinaryActivityRelationList.forEach(ordinaryActivityRelation -> {
          String dynamicKey = StringUtils.joinWith(":", ordinaryActivity.getCostBudgetCode(), ordinaryActivityRelation.getCostTypeDetailCode());//活动细类编码
          DynamicFormService<OrdinaryActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的信息，未能获取到匹配的动态模板服务类信息");
          dynamicFormService.deleteDynamicDetails(parentCode);
        });
      }
      /*String dynamicKey = StringUtils.joinWith(":", ordinaryActivity.getCostBudgetCode(), ordinaryActivity.getCostTypeCategoryCode());//活动大类编码
      DynamicFormService<OrdinaryActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class,FormTypeEnum.APPLY);
      Validate.notNull(dynamicFormService, "根据提供的信息，未能获取到匹配的动态模板服务类信息");
      dynamicFormService.deleteDynamicDetails(parentCode);*/
      //暴力删除可能的附件信息
      ordinaryActivityFilesService.save(null, parentCode);
    }
    ordinaryActivityRepository.deleteByIds(Lists.newArrayList(ids));
    //删除业务日志
    SerializableBiConsumer<OrdinaryActivityLogEventListener, OrdinaryActivityLogEventDto> onDelete =
            OrdinaryActivityLogEventListener::onDelete;
    for (OrdinaryActivityVo ordinaryActivityVo : ordinaryActivityVos) {
      OrdinaryActivityLogEventDto logEventDto = new OrdinaryActivityLogEventDto();
      logEventDto.setOriginal(ordinaryActivityVo);
      this.nebulaNetEventClient.publish(logEventDto, OrdinaryActivityLogEventListener.class, onDelete);
    }
  }

  private OrdinaryActivityDto createValidation(JSONObject json) {
    Validate.notNull(json, "普通活动信息不能为空");
    OrdinaryActivityDto dto = JSONObject.parseObject(JSONObject.toJSONString(json), OrdinaryActivityDto.class);
    this.createValidation(dto);
    return dto;
  }


  private void createValidation(OrdinaryActivityDto dto) {
    Validate.notNull(dto, "普通活动信息不能为空");
    if (StringUtils.isBlank(dto.getTenantCode())) {
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    this.validateBase(dto);
    // redis生成普通活动编码编码，编码规则为DEHD+年月日+5位顺序数。每天都从00001开始
    List<String> codeList = this.generateCodeService.generateCode(ORDINARYACTIVITY_RULE_CODE, 1);
    Validate.notEmpty(codeList, "添加信息时，生成普通活动编码失败！");
    dto.setCode(codeList.get(0));
    String pattern = "^[A-Z]{1}[A-Z0-9]*$";
    Validate.matchesPattern(dto.getCode(), pattern, "编码只能是字母和数字构成，且首字母不能是数字，最终编码都将被大写");
    OrdinaryActivityVo ordinaryActivityVo = this.findByCode(dto.getCode());
    Validate.isTrue(ordinaryActivityVo == null, "普通活动编码重复");
    for (OrdinaryActivityRelationDto relation : dto.getRelations()) {
      relation.setActivityCode(dto.getCode());
      relation.setTenantCode(dto.getTenantCode());
      Validate.notBlank(relation.getCostBudgetCode(), "关联的费用预算编码不能为空");
      if (StringUtils.isNotBlank(relation.getCostTypeDetailCode()) || StringUtils.isNotBlank(relation.getCostTypeDetailName())) {
        Validate.notBlank(relation.getCostTypeDetailCode(), "关联的活动细类编码不能为空");
        Validate.notBlank(relation.getCostTypeDetailName(), "关联的活动细类名称不能为空");
        Validate.notBlank(relation.getApplyFromCode(), "申请表单编码不能为空");
        Validate.notBlank(relation.getApplyMappingCode(), "申请动态表单关联编码不能为空");
      }
    }
  }

  private void validateBase(OrdinaryActivityDto dto) {
    Validate.notBlank(dto.getName(), "普通活动名称不能为空");
    Validate.notNull(dto.getStartTime(), "活动开始时间不能为空");
    Validate.notNull(dto.getEndTime(), "活动结束时间不能为空");
    Validate.isTrue(NumberUtils.compare(dto.getEndTime().getTime(), dto.getStartTime().getTime()) > 0, "活动结束时间必须大于活动开始时间");
    Validate.notBlank(dto.getCostBudgetCode(), "费用预算编码不能为空");
    Validate.notBlank(dto.getCostTypeCategoryCode(), "预算科目编码不能为空");
    Validate.notBlank(dto.getCostTypeCategoryName(), "预算科目名称不能为空");
    Validate.notBlank(dto.getCostTypeCategoryCode(), "活动大类编码不能为空");
    Validate.notBlank(dto.getCostTypeCategoryName(), "活动大类名称不能为空");
    Validate.notEmpty(dto.getItems(), "普通活动明细项不能为空");
    Validate.notBlank(dto.getTenantCode(), "租户编码不能为空");
    Validate.notNull(dto.getTotalApplyAmount(), "总申请金额不能为空");
  }


  private OrdinaryActivityDto updateValidation(JSONObject json) {
    Validate.notNull(json, "普通活动信息不能为空");
    OrdinaryActivityDto dto = JSONObject.parseObject(JSONObject.toJSONString(json), OrdinaryActivityDto.class);
    this.updateValidation(dto);
    return dto;
  }

  private void updateValidation(OrdinaryActivityDto dto) {
    Validate.notNull(dto, "普通活动信息不能为空");
    Validate.notBlank(dto.getId(), "更新时，主键id必须传入");
    OrdinaryActivityVo ordinaryActivityVo = this.findById(dto.getId());
    Validate.notNull(ordinaryActivityVo, "根据提供的id主键，未能获取到相应信息");
    Validate.isTrue(StringUtils.equals(dto.getCode(), ordinaryActivityVo.getCode()), "传入的普通活动编码与数据信息不匹配，请检查");
    this.validateBase(dto);
  }

  private OrdinaryActivityContextDto buildActivityContextDto(OrdinaryActivityVo sourceActivity, OrdinaryActivityDto targetActivity, boolean addOperate) {
    OrdinaryActivityContextDto contextDto = new OrdinaryActivityContextDto();
    Validate.notNull(targetActivity, "最新活动信息不能为空");
    contextDto.setTargetActivity(targetActivity);
    if (!addOperate) {
      Validate.notNull(sourceActivity, "历史活动信息不能为空");
      contextDto.setSourceActivity(sourceActivity);
    }
    return contextDto;
  }

  private DynamicFormContext prepareDynamicFormContext(OrdinaryActivityContextDto contextDto) {
    OrdinaryActivityDto targetActivity = contextDto.getTargetActivity();
    OrdinaryActivityVo sourceActivity = contextDto.getSourceActivity();
    Validate.notNull(targetActivity.getTotalApplyAmount(), "总申请金额不能为空");
    Validate.isTrue(targetActivity.getTotalApplyAmount().compareTo(BigDecimal.ZERO) > 0, "总申请金额必须大于0");
    Validate.notEmpty(targetActivity.getRelations(), "活动申请关联信息不能为空");
    //组装上下文
    DynamicFormContext context = DynamicFormContextHolder.getContext();
    context.put(ActivitiesConstant.TARGET_TOTAL_APPLY_AMOUNT_KEY, targetActivity.getTotalApplyAmount());
    context.put(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY, BigDecimal.ZERO);
    context.put(ActivitiesConstant.ACTIVITY_START_TIME_KEY, targetActivity.getStartTime());
    context.put(ActivitiesConstant.ACTIVITY_END_TIME_KEY, targetActivity.getEndTime());
    if (sourceActivity != null) {
      context.put(ActivitiesConstant.SOURCE_TOTAL_APPLY_AMOUNT_KEY, sourceActivity.getTotalApplyAmount());
    }
    return context;
  }

  @Override
  public String activityMark() {
    return ActivitiesConstant.ACTIVITY_MARK;
  }

  @Override
  public Map<String, List<BasicActivityItemVo>> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Maps.newHashMap();
    }

    OrdinaryActivityVo ordinaryActivityVo = this.findByCode(parentCode);
    if (ordinaryActivityVo == null || CollectionUtils.isEmpty(ordinaryActivityVo.getItems())) {
      return Maps.newHashMap();
    }

    Map<String, List<BasicActivityItemVo>> result = Maps.newHashMap();
    for (Map.Entry<String, List<BaseActivityItemVo>> entry : ordinaryActivityVo.getItems().entrySet()) {
      List<BaseActivityItemVo> itemVos = entry.getValue();
      List<BasicActivityItemVo> items = JSONArray.parseArray(JSONArray.toJSONString(itemVos), BasicActivityItemVo.class);
      result.put(entry.getKey(), items);
    }
    return result;
  }

  @Override
  public ActivitiesVo findDetailsByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    OrdinaryActivityVo ordinaryActivityVo = this.findByCode(parentCode);
    if (ordinaryActivityVo == null) {
      return null;
    }
    return nebulaToolkitService.copyObjectByWhiteList(ordinaryActivityVo, ActivitiesVo.class, HashSet.class, ArrayList.class, "items");
  }

  @Override
  public List<ActivitiesVo> findDetailsByParentCodes(Set<String> parentCodes) {
    if (CollectionUtils.isEmpty(parentCodes)) {
      return Lists.newArrayList();
    }
    List<OrdinaryActivityVo> ordinaryActivityVos = this.findByCodes(parentCodes);
    if (CollectionUtils.isEmpty(ordinaryActivityVos)) {
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(ordinaryActivityVos, OrdinaryActivityVo.class, ActivitiesVo.class, HashSet.class, ArrayList.class, "items"));
  }

  @Override
  public BasicActivityItemVo findByParentCodeAndItemCode(String parentCode, String itemCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }

    OrdinaryActivityVo ordinaryActivityVo = this.findByCode(parentCode);
    if (ordinaryActivityVo == null || CollectionUtils.isEmpty(ordinaryActivityVo.getItems())) {
      return null;
    }

    for (Map.Entry<String, List<BaseActivityItemVo>> entry : ordinaryActivityVo.getItems().entrySet()) {
      List<BaseActivityItemVo> itemVos = entry.getValue();
      List<BasicActivityItemVo> items = JSONArray.parseArray(JSONArray.toJSONString(itemVos), BasicActivityItemVo.class);
      for (BasicActivityItemVo item : items) {
        if (StringUtils.equals(item.getItemCode(), itemCode)) {
          return item;
        }
      }
    }
    return null;
  }

  /**
   * 该方法在com.biz.tpm.business.activities.local.service.internal.ActivitiesDetailServiceImpl#closed(java.util.Collection)中已做全局锁，
   * 此处就没有做锁了，因为这个方法一般不会被外界指定调用
   */
  @Override
  @Transactional
  public void onClosed(Map<String, Set<String>> codeMap) {
    Validate.notEmpty(codeMap, "活动关闭时，传入的活动编码信息不能为空");
    List<ActivitiesVo> activitiesVos = this.findDetailsByParentCodes(codeMap.keySet());
    if (CollectionUtils.isEmpty(activitiesVos)) {
      return;
    }
    Collection<OrdinaryActivityVo> ordinaryActivityVos = nebulaToolkitService.copyCollectionByWhiteList(activitiesVos, ActivitiesVo.class, OrdinaryActivityVo.class, HashSet.class, ArrayList.class);
    for (OrdinaryActivityVo ordinaryActivityVo : ordinaryActivityVos) {
      Set<String> waiteToCloseCodes = codeMap.get(ordinaryActivityVo.getCode());
      Map<String, List<BaseActivityItemVo>> itemMap = ordinaryActivityVo.getItems();
      for (Map.Entry<String, List<BaseActivityItemVo>> entry : itemMap.entrySet()) {
        String dynamicFormCode = entry.getValue().get(0).getDynamicFormCode();
        Validate.notBlank(dynamicFormCode, "活动关闭时，定额活动【%s】活动明细对应的动态表单编码不能为空", ordinaryActivityVo.getCode());
        Set<String> itemCodes = entry.getValue().stream().map(BaseActivityItemVo::getItemCode).collect(Collectors.toSet());
        ActivityItemsClosedStrategy strategy = null;
        for (ActivityItemsClosedStrategy activityItemsClosedStrategy : activitiesClosedStrategies) {
          if (activityItemsClosedStrategy.dynamicFormCode().equals(dynamicFormCode)) {
            strategy = activityItemsClosedStrategy;
            break;
          }
        }
        Validate.notNull(strategy, "活动关闭时，根据提供的动态表单编码【%s】没有找到对应的策略信息", dynamicFormCode);
        //明细活动关闭
        strategy.closed(Sets.intersection(waiteToCloseCodes, itemCodes));
        //判断是否全部关闭或部分关闭
        boolean allClosed = strategy.allClosed(ordinaryActivityVo.getCode());
        //更新主表状态
        ordinaryActivityRepository.updateForClose(ordinaryActivityVo.getCode(), allClosed);
      }
    }
  }

  /**
   * 该方法在com.biz.tpm.business.activities.local.task.ActivitiesTask#autoRefreshActivityStatusForActivityTime()中已做全局锁，
   * 此处就没有做锁了，因为这个方法一般不会被外界指定调用
   */
  @Override
  @Transactional
  public void refreshActivityStatusForActivityTime() {
    List<OrdinaryActivity> activities = ordinaryActivityRepository.findByRefreshStatusTask();
    if (CollectionUtils.isEmpty(activities)) {
      return;
    }
    for (OrdinaryActivity ordinaryActivity : activities) {
      ActivityStatusEnum activityStatusEnum = this.analysisStatus(ordinaryActivity.getStartTime(), ordinaryActivity.getEndTime());
      ordinaryActivity.setStatus(activityStatusEnum.getCode());
      ordinaryActivity.setTenantCode(TenantUtils.getTenantCode());
    }
    ordinaryActivityRepository.saveOrUpdateBatch(activities);
  }

  @Override
  public boolean existByCostTypeCategoryCode(String costTypeCategoryCode) {
    if (StringUtils.isBlank(costTypeCategoryCode)) {
      return false;
    }
    int result = ordinaryActivityRepository.countByCostTypeCategoryCodeAndTenantCode(costTypeCategoryCode);
    return result > 0;
  }

  private OrdinaryActivityVo processDynamicFormsForUpdate(OrdinaryActivity entity, OrdinaryActivityContextDto contextDto) {
    String parentCode = entity.getCode();
    Map<String, List<BasicActivityItemVo>> dbBasicItems = this.findByParentCode(parentCode);
    Validate.notEmpty(dbBasicItems, "根据活动申请编码【%s】，未能获取到相应明细信息", parentCode);
    Set<String> dbDynamicKeys = Sets.newHashSet(dbBasicItems.keySet());
    Set<String> dynamicKeys = Sets.newHashSet(contextDto.getTargetActivity().getItems().keySet());
    OrdinaryActivityVo ordinaryActivityVo = nebulaToolkitService.copyObjectByWhiteList(contextDto.getTargetActivity(), OrdinaryActivityVo.class, HashSet.class, ArrayList.class, "attachmentVos", "shareInfos");
    try {
      DynamicFormContext context = this.prepareDynamicFormContext(contextDto);
      //处理可能的删除情况（对整个动态表单的明细信息进行删除）
      Set<String> needDeleteDynamicForms = Sets.difference(dbDynamicKeys, dynamicKeys);
      if (!CollectionUtils.isEmpty(needDeleteDynamicForms)) {
        for (String dynamicKey : needDeleteDynamicForms) {
          DynamicFormService<OrdinaryActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息", dynamicKey);
          dynamicFormService.deleteDynamicDetails(parentCode);
        }
      }
      //处理可能的新增（对某个动态表单的明细新增）
      Set<String> needAddDynamicKeys = Sets.difference(dynamicKeys, dbDynamicKeys);
      if (!CollectionUtils.isEmpty(needAddDynamicKeys)) {
        for (String dynamicKey : needAddDynamicKeys) {
          DynamicFormService<OrdinaryActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息", dynamicKey);
          dynamicFormService.createDynamicDetails(ordinaryActivityVo, parentCode);
        }
      }

      //处理可能的更新
      Set<String> needUpdateDynamicKeys = Sets.intersection(dynamicKeys, dbDynamicKeys);
      if (!CollectionUtils.isEmpty(needUpdateDynamicKeys)) {
        for (String dynamicKey : needUpdateDynamicKeys) {
          DynamicFormService<OrdinaryActivityVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息", dynamicKey);
          dynamicFormService.modifyDynamicDetails(ordinaryActivityVo, parentCode);
        }
      }
      //验证总金额
      this.validateTotalApplyAmount(context);
    } finally {
      DynamicFormContextHolder.clearContext();
    }
    return ordinaryActivityVo;
  }

  private void validateTotalApplyAmount(DynamicFormContext context) {
    //处理最终的上下文
    if (context.exist(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY) && context.exist(ActivitiesConstant.TARGET_TOTAL_APPLY_AMOUNT_KEY)) {
      BigDecimal targetTotalApplyAmount = (BigDecimal) context.get(ActivitiesConstant.TARGET_TOTAL_APPLY_AMOUNT_KEY);
      BigDecimal sumTotalApplyAmount = (BigDecimal) context.get(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY);
      Validate.isTrue(targetTotalApplyAmount.compareTo(sumTotalApplyAmount) == 0, "累计总金额与申请的总金额不一致，请检查");
      Validate.isTrue(targetTotalApplyAmount.compareTo(BigDecimal.ZERO) > 0, "总申请金额必须大于0");
    }
  }

  private ActivityStatusEnum analysisStatus(Date startTime, Date endTime) {
    Date now = new Date();
    if (NumberUtils.compare(now.getTime(), startTime.getTime()) < 0) {
      return ActivityStatusEnum.UNEXECUTED;
    } else if (NumberUtils.compare(now.getTime(), endTime.getTime()) > 0) {
      return ActivityStatusEnum.ENDED;
    }
    return ActivityStatusEnum.EXECUTING;
  }

  /**
   * 提交工作流进行审批，提交成功返回流程实例ID，提交失败则抛出异常
   */
  private void commitProcess(OrdinaryActivityDto dto) {
    ProcessBusinessDto processBusiness = dto.getProcessBusiness();
    Validate.notNull(processBusiness, "提交工作流时，未传工作流对象信息!");
    processBusiness.setBusinessNo(dto.getCode());
    processBusiness.setBusinessFormJson(JsonUtils.obj2JsonString(dto));
    processBusiness.setBusinessCode(ActivitiesConstant.ORDINARY_ACTIVITY_PROCESS_NAME);
    this.processBusinessService.processStart(processBusiness);
  }

  /**
   * 根据业务编码查询工作流业务流程关联信息
   *
   * @param businessNos
   */
  private List<ProcessBusinessMappingVo> findProcessBusinessMappingVo(List<String> businessNos) {
    ProcessBusinessMappingDto processBusinessMappingDto = new ProcessBusinessMappingDto();
    processBusinessMappingDto.setBusinessNos(businessNos);
    processBusinessMappingDto.setBusinessCode(ActivitiesConstant.ORDINARY_ACTIVITY_PROCESS_NAME);
    return processBusinessMappingService.findMultiByByConditions(processBusinessMappingDto);
  }
}
