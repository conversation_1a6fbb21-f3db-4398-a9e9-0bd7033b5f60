package com.biz.crm.tpm.business.activities.ordinary.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivityRelation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 活动关联信息(TpmOrdinaryActivityRelation)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-11-11 20:19:32
 */
public interface OrdinaryActivityRelationMapper extends BaseMapper<OrdinaryActivityRelation> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页对象
   * @param ordinaryActivityRelation 查询实体
   * @return 所有数据
  */
  public Page<OrdinaryActivityRelation> findByConditions(@Param("page") Page<OrdinaryActivityRelation> page, @Param("ordinaryActivityRelation") OrdinaryActivityRelation ordinaryActivityRelation);
}

