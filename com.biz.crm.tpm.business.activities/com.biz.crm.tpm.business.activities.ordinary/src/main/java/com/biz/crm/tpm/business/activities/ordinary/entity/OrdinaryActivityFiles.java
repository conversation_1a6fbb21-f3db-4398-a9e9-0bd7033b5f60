package com.biz.crm.tpm.business.activities.ordinary.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * 普通活动附件
 */
@ApiModel(value = "OrdinaryActivityFiles",description = "普通活动附件")
@TableName("tpm_ordinary_activity_files")
@Getter
@Setter
@Entity(name = "tpm_ordinary_activity_files")
@org.hibernate.annotations.Table(appliesTo = "tpm_ordinary_activity_files", comment = "普通活动附件")
public class OrdinaryActivityFiles extends FileEntity {

  /** 普通活动编号 */
  @ApiModelProperty(name = "普通活动编号",notes = "普通活动编号")
  @Column(name = "activity_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '普通活动编号 '")
  private String activityCode;
}