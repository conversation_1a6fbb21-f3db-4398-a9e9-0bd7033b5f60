package com.biz.crm.tpm.business.activities.ordinary.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR> rentao
 * @date : 2022/11/11 19:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_ordinary_activity_relation")
@Table(name = "tpm_ordinary_activity_relation", indexes = {@Index(name = "tpm_ordinary_activity_relation_index1", columnList = "activity_code,cost_budget_code,cost_type_detail_code",unique = true)})
@ApiModel(value = "OrdinaryActivityRelation", description = "申请活动关联信息")
@org.hibernate.annotations.Table(appliesTo = "tpm_ordinary_activity_relation", comment = "活动关联信息")
public class OrdinaryActivityRelation extends TenantOpEntity {

  @ApiModelProperty("关联的活动编码")
  @TableField(value = "activity_code")
  @Column(name = "activity_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '关联的活动编码'")
  private String activityCode;

  @ApiModelProperty("费用预算编码")
  @TableField(value = "cost_budget_code")
  @Column(name = "cost_budget_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '费用预算编码'")
  private String costBudgetCode;

  @ApiModelProperty("活动细类编码")
  @TableField(value = "cost_type_detail_code")
  @Column(name = "cost_type_detail_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动细类编码'")
  private String costTypeDetailCode;

  @ApiModelProperty("活动细类名称")
  @TableField(value = "cost_type_detail_name")
  @Column(name = "cost_type_detail_name", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动细类名称'")
  private String costTypeDetailName;


  @ApiModelProperty(name = "applyFromCode", notes = "申请表单编码", value = "申请表单编码")
  @TableField(value = "apply_from_code")
  @Column(name = "apply_from_code", columnDefinition = "VARCHAR(64) COMMENT '申请表单编码 '")
  private String applyFromCode;


  @ApiModelProperty("申请动态表单关联编码")
  @TableField(value = "apply_mapping_code")
  @Column(name = "apply_mapping_code", columnDefinition = "VARCHAR(128) COMMENT '申请动态表单关联编码 '")
  private String applyMappingCode;

}
