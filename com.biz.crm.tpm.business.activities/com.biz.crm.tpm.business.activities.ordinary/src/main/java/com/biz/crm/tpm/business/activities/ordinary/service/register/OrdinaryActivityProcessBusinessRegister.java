package com.biz.crm.tpm.business.activities.ordinary.service.register;

import com.biz.crm.tpm.business.activities.ordinary.constant.ActivitiesConstant;
import com.biz.crm.workflow.sdk.register.ProcessBusinessRegister;
import org.springframework.stereotype.Component;

/**
 * 活动申请工作流注册业务编码
 *
 * <AUTHOR>
 * @date 2022/9/5
 */
@Component
public class OrdinaryActivityProcessBusinessRegister implements ProcessBusinessRegister {
  @Override
  public String getBusinessCode() {
    return ActivitiesConstant.ORDINARY_ACTIVITY_PROCESS_NAME;
  }

  @Override
  public String getBusinessName() {
    return "活动申请";
  }
}
