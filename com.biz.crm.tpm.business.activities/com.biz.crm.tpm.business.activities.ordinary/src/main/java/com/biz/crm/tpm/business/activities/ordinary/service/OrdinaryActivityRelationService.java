package com.biz.crm.tpm.business.activities.ordinary.service;


import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivityRelation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityRelationVo;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 活动关联信息(TpmOrdinaryActivityRelation)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-11 20:19:31
 */
public interface OrdinaryActivityRelationService {

  /**
   * 分页查询数据
   * @param pageable 分页对象
   * @param tpmOrdinaryActivityRelation 实体对象
   * @return
   */
  Page<OrdinaryActivityRelation> findByConditions(Pageable pageable, OrdinaryActivityRelation tpmOrdinaryActivityRelation);
  
   /**
   * 通过主键查询单条数据
   * @param id 主键
   * @return 单条数据
   */
   OrdinaryActivityRelation findById(String id);
  
   /**
   * 新增数据
   * @param tpmOrdinaryActivityRelation 实体对象
   * @return 新增结果
   */
   OrdinaryActivityRelation create(OrdinaryActivityRelation tpmOrdinaryActivityRelation);
  
   /**
   * 修改新据
   * @param tpmOrdinaryActivityRelation 实体对象
   * @return 修改结果
   */
   OrdinaryActivityRelation update(OrdinaryActivityRelation tpmOrdinaryActivityRelation);
  
  /**
   * 删除数据
   * @param idList 主键结合
   * @return 删除结果
   */
  void delete(List<String> idList);

  /**
   * 活动编码查询活动关联信息
   * @param activityCode 主键结合
   * @return
   */
  List<OrdinaryActivityRelationVo> findByActivityCode(String activityCode);

}

