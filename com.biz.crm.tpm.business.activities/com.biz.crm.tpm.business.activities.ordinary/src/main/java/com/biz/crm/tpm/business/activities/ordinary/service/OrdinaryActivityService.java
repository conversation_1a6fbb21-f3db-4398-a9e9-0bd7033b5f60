package com.biz.crm.tpm.business.activities.ordinary.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityDto;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

public interface OrdinaryActivityService {

  Page<OrdinaryActivityVo> findByConditions(Pageable pageable, OrdinaryActivityDto dto);

  OrdinaryActivityVo create(JSONObject json);

  OrdinaryActivityVo update(JSONObject json);

  OrdinaryActivityVo findByCode(String code);

  List<OrdinaryActivityVo> findByCodes(Set<String> codes);

  OrdinaryActivityVo findById(String id);

  List<OrdinaryActivityVo> findByIds(Set<String> ids);

  void delete(Set<String> ids);

  /**
   * 根据活动大类编码，判断是否存在活动正在使用该活动细类
   */
  boolean existByCostTypeCategoryCode(String costTypeCategoryCode);

}
