package com.biz.crm.tpm.business.activities.ordinary.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "OrdinaryActivityVo", description = "普通活动vo")
public class OrdinaryActivityVo extends UuidFlagOpVo {
  private static final long serialVersionUID = -993382296719224352L;
  @ApiModelProperty("活动编码")
  private String code;

  @ApiModelProperty("活动名称")
  private String name;

  @ApiModelProperty("活动开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  @ApiModelProperty("活动结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty("费用预算编码")
  private String costBudgetCode;

  @ApiModelProperty("预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("活动大类编码")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  private String costTypeCategoryName;

  @ApiModelProperty("总申请金额")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("租户编码")
  private String tenantCode;

  @ApiModelProperty("最终可用余额")
  private BigDecimal finalBalance;

  @ApiModelProperty("活动状态")
  private String status;

  @ApiModelProperty(value = "活动附件信息")
  private List<OrdinaryActivityFilesVo> activityFiles;

  @ApiModelProperty("普通活动明细")
  private Map<String, List<BaseActivityItemVo>> items;

  @ApiModelProperty("关联信息")
  private List<OrdinaryActivityRelationVo> relations;
}
