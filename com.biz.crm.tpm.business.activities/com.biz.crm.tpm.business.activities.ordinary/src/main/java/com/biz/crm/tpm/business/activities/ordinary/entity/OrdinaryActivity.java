package com.biz.crm.tpm.business.activities.ordinary.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.tpm.business.activities.sdk.template.BaseActivity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_ordinary_activity")
@Table(name = "tpm_ordinary_activity", indexes = {@Index(name = "tpm_ordinary_activity_index1", columnList = "tenant_code, code", unique = true)})
@ApiModel(value = "OrdinaryActivity", description = "普通活动")
@org.hibernate.annotations.Table(appliesTo = "tpm_ordinary_activity", comment = "普通活动")
public class OrdinaryActivity extends BaseActivity {

  @Transient
  @TableField(exist = false)
  private List<OrdinaryActivityFiles> activityFiles;

  @ApiModelProperty("关联信息")
  @TableField(exist = false)
  @Transient
  private List<OrdinaryActivityRelation> relations;

  @Transient
  @TableField(exist = false)
  private Map<String, List<?>> items;

}
