package com.biz.crm.tpm.business.activities.ordinary.dto;

import com.biz.crm.business.common.sdk.dto.FileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "OrdinaryActivityFilesDto", description = "普通活动附件信息dto")
public class OrdinaryActivityFilesDto extends FileDto {
  @ApiModelProperty(name = "普通活动编号",notes = "普通活动编号")
  private String activityCode;
}
