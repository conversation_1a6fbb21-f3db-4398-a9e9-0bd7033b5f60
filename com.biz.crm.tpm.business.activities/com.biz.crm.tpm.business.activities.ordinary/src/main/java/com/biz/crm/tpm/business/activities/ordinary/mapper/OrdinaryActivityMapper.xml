<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.ordinary.mapper.OrdinaryActivityMapper">
  <resultMap id="ordinaryActivityVoMap" type="com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityVo"/>

  <select id="findByConditions" resultMap="ordinaryActivityVoMap">
    select
    t.*
    from tpm_ordinary_activity t
    where t.tenant_code=#{dto.tenantCode}
    <if test="dto.code !=null and dto.code != '' ">
      and t.code = #{dto.code}
    </if>
    <if test="dto.name !=null and dto.name != '' ">
      and t.name like concat('%',#{dto.name},'%')
    </if>
    <if test="dto.delFlag !=null and dto.delFlag != '' ">
      and t.del_flag = #{dto.delFlag}
    </if>
    <if test="dto.status !=null and dto.status != '' ">
      and t.status = #{dto.status}
    </if>
    order by t.create_time desc
  </select>
</mapper>

