package com.biz.crm.tpm.business.activities.ordinary.service.internal;



import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivityRelation;
import com.biz.crm.tpm.business.activities.ordinary.repository.OrdinaryActivityRelationRepository;
import com.biz.crm.tpm.business.activities.ordinary.service.OrdinaryActivityRelationService;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityRelationVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动关联信息(TpmOrdinaryActivityRelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-11 20:19:31
 */
@Service("tpmOrdinaryActivityRelationService")
public class OrdinaryActivityRelationServiceImpl implements OrdinaryActivityRelationService {

  @Autowired
  private OrdinaryActivityRelationRepository tpmOrdinaryActivityRelationRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;

  /**
   * 分页查询数据
   * @param pageable 分页对象
   * @param tpmOrdinaryActivityRelation 实体对象
   * @return
   */
  @Override
  public Page<OrdinaryActivityRelation> findByConditions(Pageable pageable, OrdinaryActivityRelation tpmOrdinaryActivityRelation) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(tpmOrdinaryActivityRelation)) {
      tpmOrdinaryActivityRelation = new OrdinaryActivityRelation();
    }
    return this.tpmOrdinaryActivityRelationRepository.findByConditions(pageable, tpmOrdinaryActivityRelation);
  }
  
  /**
   * 通过主键查询单条数据
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public OrdinaryActivityRelation findById(String id) {
    if (StringUtils.isBlank(id)) {
	  return null;
	}
    return this.tpmOrdinaryActivityRelationRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
  }
  
  /**
   * 新增数据
   * @param tpmOrdinaryActivityRelation 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public OrdinaryActivityRelation create(OrdinaryActivityRelation tpmOrdinaryActivityRelation) {
    this.createValidate(tpmOrdinaryActivityRelation);
    tpmOrdinaryActivityRelation.setTenantCode(TenantUtils.getTenantCode());
    this.tpmOrdinaryActivityRelationRepository.saveOrUpdate(tpmOrdinaryActivityRelation);
    return tpmOrdinaryActivityRelation;
  }
  
  /**
   * 修改新据
   * @param tpmOrdinaryActivityRelation 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public OrdinaryActivityRelation update(OrdinaryActivityRelation tpmOrdinaryActivityRelation) {
    this.updateValidate(tpmOrdinaryActivityRelation);
    tpmOrdinaryActivityRelation.setTenantCode(TenantUtils.getTenantCode());
    this.tpmOrdinaryActivityRelationRepository.saveOrUpdate(tpmOrdinaryActivityRelation);
    return tpmOrdinaryActivityRelation;
  }
  
  /**
   * 删除数据
   * @param idList 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(List<String> idList) {
    Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
    this.tpmOrdinaryActivityRelationRepository.removeByIdsAndTenantCode(idList,TenantUtils.getTenantCode());
  }

  /**
   * 活动编码查询活动关联信息
   * @param activityCode 主键结合
   * @return
   */
  @Override
  public List<OrdinaryActivityRelationVo> findByActivityCode(String activityCode) {
    if(StringUtils.isBlank(activityCode)){
      return Lists.newArrayList();
    }
    List<OrdinaryActivityRelation> relations = tpmOrdinaryActivityRelationRepository.findByActivityCodeAndTenantCode(activityCode, TenantUtils.getTenantCode());
    if(CollectionUtils.isEmpty(relations)){
      return Lists.newArrayList();
    }

    Collection<OrdinaryActivityRelationVo> relationVos = nebulaToolkitService.copyCollectionByWhiteList(relations,OrdinaryActivityRelation.class,OrdinaryActivityRelationVo.class, HashSet.class, ArrayList.class);
    return Lists.newArrayList(relationVos);
  }

  /**
   * 创建验证
   * @param tpmOrdinaryActivityRelation
   */
  private void createValidate(OrdinaryActivityRelation tpmOrdinaryActivityRelation) {
    Validate.notNull(tpmOrdinaryActivityRelation, "新增时，对象信息不能为空！");
	tpmOrdinaryActivityRelation.setId(null);
      Validate.notBlank(tpmOrdinaryActivityRelation.getTenantCode(), "新增数据时，租户编号不能为空！");
    Validate.notBlank(tpmOrdinaryActivityRelation.getActivityCode(), "新增数据时，关联的活动编码不能为空！");
    Validate.notBlank(tpmOrdinaryActivityRelation.getCostBudgetCode(), "新增数据时，费用预算编码不能为空！");
    Validate.notBlank(tpmOrdinaryActivityRelation.getCostTypeDetailCode(), "新增数据时，活动细类编码不能为空！");
    Validate.notBlank(tpmOrdinaryActivityRelation.getCostTypeDetailName(), "新增数据时，活动细类名称不能为空！");
   
  }
  
   /**
   * 修改验证
   * @param tpmOrdinaryActivityRelation
   */
  private void updateValidate(OrdinaryActivityRelation tpmOrdinaryActivityRelation) {
    Validate.notNull(tpmOrdinaryActivityRelation, "修改时，对象信息不能为空！");
      Validate.notBlank(tpmOrdinaryActivityRelation.getId(), "新增数据时，不能为空！");
    Validate.notBlank(tpmOrdinaryActivityRelation.getTenantCode(), "新增数据时，租户编号不能为空！");
    Validate.notBlank(tpmOrdinaryActivityRelation.getActivityCode(), "新增数据时，关联的活动编码不能为空！");
    Validate.notBlank(tpmOrdinaryActivityRelation.getCostBudgetCode(), "新增数据时，费用预算编码不能为空！");
    Validate.notBlank(tpmOrdinaryActivityRelation.getCostTypeDetailCode(), "新增数据时，活动细类编码不能为空！");
    Validate.notBlank(tpmOrdinaryActivityRelation.getCostTypeDetailName(), "新增数据时，活动细类名称不能为空！");
    
  }
}

