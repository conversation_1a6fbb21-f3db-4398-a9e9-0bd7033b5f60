package com.biz.crm.tpm.business.activities.ordinary.service;

import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityFilesDto;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityFilesVo;

import java.util.List;

public interface OrdinaryActivityFilesService {

  /**
   * 保存普通活动附件
   */
  void save(List<OrdinaryActivityFilesDto> filesDtos, String activityCode);

  /**
   * 根据普通活动编码，查询附件
   */
  List<OrdinaryActivityFilesVo> findByActivityCode(String activityCode);
}
