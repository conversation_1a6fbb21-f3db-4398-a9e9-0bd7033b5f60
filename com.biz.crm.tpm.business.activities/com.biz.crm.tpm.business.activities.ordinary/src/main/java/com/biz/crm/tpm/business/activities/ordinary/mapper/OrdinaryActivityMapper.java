package com.biz.crm.tpm.business.activities.ordinary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityDto;
import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivity;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityVo;
import org.apache.ibatis.annotations.Param;

/**
 * 普通活动(OrdinaryActivity)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
public interface OrdinaryActivityMapper extends BaseMapper<OrdinaryActivity> {

  /**
   * 分页查询所有数据
   * @param page       分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  Page<OrdinaryActivityVo> findByConditions(@Param("page") Page<OrdinaryActivityVo> page, @Param("dto") OrdinaryActivityDto dto);

}

