package com.biz.crm.tpm.business.activities.ordinary.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivityFiles;
import com.biz.crm.tpm.business.activities.ordinary.mapper.OrdinaryActivityFilesMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 普通活动附件(OrdinaryActivityFiles)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class OrdinaryActivityFilesRepository extends ServiceImpl<OrdinaryActivityFilesMapper, OrdinaryActivityFiles> {

  public List<OrdinaryActivityFiles> findByActivityCodeAndTenantCode(String activityCode, String tenantCode){
    return this.lambdaQuery().eq(OrdinaryActivityFiles::getActivityCode,activityCode).eq(OrdinaryActivityFiles::getTenantCode,tenantCode).list();
  }

  public void deleteByActivityCodeAndTenantCode(String activityCode, String tenantCode){
    this.lambdaUpdate().eq(OrdinaryActivityFiles::getActivityCode,activityCode).eq(OrdinaryActivityFiles::getTenantCode,tenantCode).remove();
  }
}

