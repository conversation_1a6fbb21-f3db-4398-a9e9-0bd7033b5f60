package com.biz.crm.tpm.business.activities.ordinary.repository;

import com.biz.crm.tpm.business.activities.ordinary.entity.OrdinaryActivityRelation;
import com.biz.crm.tpm.business.activities.ordinary.mapper.OrdinaryActivityRelationMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;


/**
 * 活动关联信息(TpmOrdinaryActivityRelation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-11 20:19:31
 */
@Component
public class OrdinaryActivityRelationRepository extends ServiceImpl<OrdinaryActivityRelationMapper, OrdinaryActivityRelation> {

  @Autowired
  private OrdinaryActivityRelationMapper ordinaryActivityRelationMapper;

  /**
   * 分页查询数据
   *
   * @param pageable                 分页对象
   * @param ordinaryActivityRelation 实体对象
   * @return
   */
  public Page<OrdinaryActivityRelation> findByConditions(Pageable pageable, OrdinaryActivityRelation ordinaryActivityRelation) {
    Page<OrdinaryActivityRelation> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    ordinaryActivityRelation.setTenantCode(TenantUtils.getTenantCode());
    Page<OrdinaryActivityRelation> pageList = this.ordinaryActivityRelationMapper.findByConditions(page, ordinaryActivityRelation);
    return pageList;
  }

  /**
   * 根据关联的活动编码，查询信息
   */
  public List<OrdinaryActivityRelation> findByActivityCodeAndTenantCode(String activityCode, String tenantCode) {
    return this.lambdaQuery().eq(OrdinaryActivityRelation::getActivityCode, activityCode).eq(OrdinaryActivityRelation::getTenantCode, tenantCode).list();
  }

  public OrdinaryActivityRelation findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(OrdinaryActivityRelation::getTenantCode,tenantCode)
        .in(OrdinaryActivityRelation::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(List<String> idList, String tenantCode) {
    this.lambdaUpdate()
        .eq(OrdinaryActivityRelation::getTenantCode,tenantCode)
        .in(OrdinaryActivityRelation::getId,idList)
        .remove();
  }

  public void removeByIdsAndTenantCodes(Set<String> idList, String tenantCode) {
    this.lambdaUpdate()
        .eq(OrdinaryActivityRelation::getTenantCode,tenantCode)
        .in(OrdinaryActivityRelation::getId,idList)
        .remove();
  }

  public List<OrdinaryActivityRelation> findByActivityCode(String activityCode, String tenantCode) {
    return this.lambdaQuery().eq(OrdinaryActivityRelation::getTenantCode, tenantCode)
        .eq(OrdinaryActivityRelation::getActivityCode, activityCode)
        .list();
  }
}

