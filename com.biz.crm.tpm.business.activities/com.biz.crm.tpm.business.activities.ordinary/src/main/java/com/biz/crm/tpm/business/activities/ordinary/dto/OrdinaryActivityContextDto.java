package com.biz.crm.tpm.business.activities.ordinary.dto;

import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "OrdinaryActivityContextDto", description = "专用于普通活动与动态表单间的上下文信息传参")
public class OrdinaryActivityContextDto {

  @ApiModelProperty("最新活动信息")
  private OrdinaryActivityDto targetActivity;

  @ApiModelProperty("历史活动信息")
  private OrdinaryActivityVo sourceActivity;

}
