package com.biz.crm.tpm.business.activities.ordinary.event;

import com.biz.crm.tpm.business.activities.ordinary.dto.OrdinaryActivityLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 普通活动 业务日志监听
 * <AUTHOR> <PERSON>
 * @date : 2022-7-1
 */
public interface OrdinaryActivityLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(OrdinaryActivityLogEventDto eventDto);
  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(OrdinaryActivityLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(OrdinaryActivityLogEventDto eventDto);
  /**
   * 启用
   *
   * @param eventDto
   */
  void onEnable(OrdinaryActivityLogEventDto eventDto);
  /**
   * 禁用
   *
   * @param eventDto
   */
  void onDisable(OrdinaryActivityLogEventDto eventDto);
}
