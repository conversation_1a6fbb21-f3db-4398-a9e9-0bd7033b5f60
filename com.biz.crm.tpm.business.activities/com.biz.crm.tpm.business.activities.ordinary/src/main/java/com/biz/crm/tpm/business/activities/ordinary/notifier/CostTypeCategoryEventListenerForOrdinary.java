package com.biz.crm.tpm.business.activities.ordinary.notifier;

import com.biz.crm.tpm.business.activities.ordinary.service.OrdinaryActivityService;
import com.biz.crm.tpm.business.budget.sdk.event.CostTypeCategoryEventListener;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CostTypeCategoryEventListenerForOrdinary implements CostTypeCategoryEventListener {

  @Autowired
  private OrdinaryActivityService ordinaryActivityService;

  public void onDeleted(CostTypeCategoryVo costTypeCategoryVo){
    Validate.notNull(costTypeCategoryVo,"活动大类信息不能为空");
    Validate.notBlank(costTypeCategoryVo.getCategoryCode(),"活动大类编码不能为空");
    boolean exist = ordinaryActivityService.existByCostTypeCategoryCode(costTypeCategoryVo.getCategoryCode());
    Validate.isTrue(!exist,"活动大类【%s】已存在活动申请数据，不能进行删除",costTypeCategoryVo.getCategoryCode());
  }
}
