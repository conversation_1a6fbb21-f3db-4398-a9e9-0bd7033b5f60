package com.biz.crm.tpm.business.activities.ordinary.service.observer;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.form.sdk.DynamicFormService;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.model.OperationStrategy;
import com.biz.crm.tpm.business.activities.dynamic.template.enums.FormTypeEnum;
import com.biz.crm.tpm.business.activities.dynamic.template.service.DynamicFormServiceResolver;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.DisplayActivityItemVo;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.MaterialActivityItemVo;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.QuotaActivityItemVo;
import com.biz.crm.tpm.business.activities.ordinary.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.ordinary.service.OrdinaryActivityService;
import com.biz.crm.tpm.business.activities.ordinary.vo.OrdinaryActivityVo;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesRegisterService;
import com.biz.crm.tpm.business.activities.sdk.service.BasicActivitiesInfoService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BasicActivityItemVo;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.BusinessStrategySettingExecutor;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.workflow.sdk.dto.ProcessStatusDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.listener.ProcessCompleteListener;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant.DYNAMIC_FORM_FIELD_CODE;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType.ACTIVITY;

/**
 * <AUTHOR>
 * @describe: 活动申请工作流审批回调
 * @createTime 2022年06月13日 14:15:00
 */
@Component
public class OrdinaryActivityProcessCallBackListener implements ProcessCompleteListener {

  @Autowired(required = false)
  private OrdinaryActivityService ordinaryActivityService;
  @Autowired(required = false)
  private List<OperationStrategy> operationStraties;
  @Autowired(required = false)
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired
  private DynamicFormServiceResolver dynamicFormServiceResolver;
  @Autowired
  private ActivitiesRegisterService activitiesRegisterService;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  @Qualifier("_OrdinaryActivityServiceImpl")
  private BasicActivitiesInfoService basicActivitiesInfoService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired(required = false)
  private List<BusinessStrategySettingExecutor> businessStrategySettingExecutors;
  @Autowired(required = false)
  private List<ActivitiesEventListener> activitiesEventListeners;

  @Override
  public String getBusinessCode() {
    return ActivitiesConstant.ORDINARY_ACTIVITY_PROCESS_NAME;
  }

  /**
   * <p>
   * 1. 校验回调数据类型
   * 2. 校验回调实例
   * 3. 校验实例状态
   * </p>
   *
   * @param dto
   */
  @Override
  @Transactional
  public void onProcessComplete(ProcessStatusDto dto) {
    //校验回调数据类型
    if (!dto.getBusinessCode().equals(ActivitiesConstant.ORDINARY_ACTIVITY_PROCESS_NAME)) {
      return;
    }
    String processStatus = dto.getProcessStatus();
    OrdinaryActivityVo entity = ordinaryActivityService.findByCode(dto.getBusinessNo());
    //校验回调实例
    Validate.notNull(entity, "活动申请审批流程回调失败，未查询到当前实例");
    //审批通过
    if (String.valueOf(processStatus).equals(ProcessStatusEnum.PASS.getDictCode())) {
      Map<String, List<BaseActivityItemVo>> items = entity.getItems();
      Validate.notEmpty(items, "活动申请审批流程回调失败，当前实例缺失活动明细");
      List res = new ArrayList<>();
      items.forEach((k, v) -> {
        //
        if (!CollectionUtils.isEmpty(v)) {
          BaseActivityItemVo baseActivityItemVo = v.get(0);
          String parentCode = baseActivityItemVo.getParentCode();
          String dynamicKey = baseActivityItemVo.getDynamicKey();
          String dynamicFormCode = baseActivityItemVo.getDynamicFormCode();
          DynamicFormService<OrdinaryActivityVo> dynamicFormService = dynamicFormServiceResolver
              .getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, OrdinaryActivityVo.class, FormTypeEnum.APPLY);
          // 寻找对应的OperationStrategy
          OperationStrategy<? extends DynamicForm> selectedOperationStrategy = null;
          for (OperationStrategy<? extends DynamicForm> operationStrategy : operationStraties) {
            if (!StringUtils.equals(operationStrategy.dynamicFormCode(), dynamicFormCode)) {
              continue;
            }
            selectedOperationStrategy = operationStrategy;
          }
          BusinessStrategySettingExecutor executor = BusinessStrategySettingExecutor.getExecutor(businessStrategySettingExecutors, ACTIVITY.name());
          Validate.notNull(executor, "活动申请dynamicKey【%s】dynamicFormCode编号【%s】没有匹配到相应的策略执行器，请检查！", dynamicKey, dynamicFormCode);
          Class<? extends DynamicForm> aClass = selectedOperationStrategy.dynamicFormClass();
          if (aClass.getName() == DisplayActivityItemVo.class.getName()) {
            List<DisplayActivityItemVo> voList = (List<DisplayActivityItemVo>) dynamicFormService
                .findDetailsByParentCodeAndDynamicFieldAndDynamicKey(parentCode, DYNAMIC_FORM_FIELD_CODE, dynamicKey, DisplayActivityItemVo.class);
            if (!CollectionUtils.isEmpty(voList)) {
              List<String> codes = voList.stream().map(DisplayActivityItemVo::getCostTypeDetailCode).collect(Collectors.toList());
              List<CostTypeDetailVo> byCodes = costTypeDetailVoService.findByCodes(codes);
              if (!CollectionUtils.isEmpty(byCodes)) {
                List<String> sendSfaCodes = byCodes
                    .stream()
                    .filter(e -> executor.matchedOprtType(e.getSettingStrategies(), StrategySettingExecutorOprtType.PUSH_INFO.name(), true))
                    .map(CostTypeDetailVo::getDetailCode)
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(sendSfaCodes)) {
                  List<DisplayActivityItemVo> senSfaList = voList.stream().filter(e -> sendSfaCodes.contains(e.getCostTypeDetailCode())).collect(Collectors.toList());
                  res.addAll(senSfaList);
                }
              }
            }
          }
          if (aClass.getName() == MaterialActivityItemVo.class.getName()) {
            List<MaterialActivityItemVo> voList = (List<MaterialActivityItemVo>) dynamicFormService
                .findDetailsByParentCodeAndDynamicFieldAndDynamicKey(parentCode, DYNAMIC_FORM_FIELD_CODE, dynamicKey, MaterialActivityItemVo.class);
            if (!CollectionUtils.isEmpty(voList)) {
              List<String> codes = voList.stream().map(MaterialActivityItemVo::getCostTypeDetailCode).collect(Collectors.toList());
              List<CostTypeDetailVo> byCodes = costTypeDetailVoService.findByCodes(codes);
              if (!CollectionUtils.isEmpty(byCodes)) {
                List<String> sendSfaCodes = byCodes
                    .stream()
                    .filter(e -> executor.matchedOprtType(e.getSettingStrategies(), StrategySettingExecutorOprtType.PUSH_INFO.name(), true))
                    .map(CostTypeDetailVo::getDetailCode)
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(sendSfaCodes)) {
                  List<MaterialActivityItemVo> senSfaList = voList.stream().filter(e -> sendSfaCodes.contains(e.getCostTypeDetailCode())).collect(Collectors.toList());
                  res.addAll(senSfaList);
                }
              }
            }
          }
          if (aClass.getName() == QuotaActivityItemVo.class.getName()) {
            List<QuotaActivityItemVo> voList = (List<QuotaActivityItemVo>) dynamicFormService
                .findDetailsByParentCodeAndDynamicFieldAndDynamicKey(parentCode, DYNAMIC_FORM_FIELD_CODE, dynamicKey, QuotaActivityItemVo.class);
            if (!CollectionUtils.isEmpty(voList)) {
              List<String> codes = voList.stream().map(QuotaActivityItemVo::getCostTypeDetailCode).collect(Collectors.toList());
              List<CostTypeDetailVo> byCodes = costTypeDetailVoService.findByCodes(codes);
              if (!CollectionUtils.isEmpty(byCodes)) {
                List<String> sendSfaCodes = byCodes
                    .stream()
                    .filter(e -> executor.matchedOprtType(e.getSettingStrategies(), StrategySettingExecutorOprtType.PUSH_INFO.name(), true))
                    .map(CostTypeDetailVo::getDetailCode)
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(sendSfaCodes)) {
                  List<QuotaActivityItemVo> senSfaList = voList.stream().filter(e -> sendSfaCodes.contains(e.getCostTypeDetailCode())).collect(Collectors.toList());
                  res.addAll(senSfaList);
                }
              }
            }
          }
        }
      });
      if (!CollectionUtils.isEmpty(res)) {
        //TODO 等待推送到SFA接口
      }
      //通知活动中心
      ActivitiesDto activitiesDto = nebulaToolkitService.copyObjectByWhiteList(entity, ActivitiesDto.class, HashSet.class, ArrayList.class);
      activitiesDto.setActivitiesCode(entity.getCode());
      activitiesDto.setActivitiesName(entity.getName());
      activitiesDto.setBeginTime(entity.getStartTime());
      activitiesDto.setActivityMark(basicActivitiesInfoService.activityMark());
      this.fillDetail(activitiesDto);
      activitiesRegisterService.register(activitiesDto);
    }
    //审批驳回
    if (String.valueOf(processStatus).equals(ProcessStatusEnum.REJECT.getDictCode())) {
      //费用预算回退
      this.backCostBudget(entity);
    }
    //流程追回
    if (String.valueOf(processStatus).equals(ProcessStatusEnum.RECOVER.getDictCode())) {
      //费用预算回退
      this.backCostBudget(entity);
    }
    //审批状态变更时触发监听事件
    if (!CollectionUtils.isEmpty(activitiesEventListeners)) {
      ActivitiesVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, ActivitiesVo.class, HashSet.class, ArrayList.class);
      vo.setActivitiesCode(entity.getCode());
      this.activitiesEventListeners.forEach(event -> event.onUpdateProcessStatus(vo, processStatus));
    }
  }

  /**
   * 填充活动明细信息
   *
   * @param activitiesDto
   */
  private void fillDetail(ActivitiesDto activitiesDto) {
    OrdinaryActivityVo ordinaryActivityVo = this.ordinaryActivityService.findByCode(activitiesDto.getActivitiesCode());
    ActivitiesVo activitiesVo = JSONObject.parseObject(JSONObject.toJSONString(ordinaryActivityVo), ActivitiesVo.class);
    activitiesVo.setActivitiesCode(ordinaryActivityVo.getCode());
    activitiesVo.setActivitiesName(ordinaryActivityVo.getName());
    activitiesVo.setBeginTime(ordinaryActivityVo.getStartTime());

    Map<String, List<BasicActivityItemVo>> detailMap = activitiesVo.getItems();

    Set<Map.Entry<String, List<BasicActivityItemVo>>> entrySet = detailMap.entrySet();
    Set<BasicActivityItemVo> items = Sets.newHashSet();
    for (Map.Entry<String, List<BasicActivityItemVo>> entry : entrySet) {
      items.addAll(entry.getValue());
    }

    List<ActivitiesDetailDto> activitiesDetails = Lists.newArrayList();
    for (BasicActivityItemVo itemVo : items) {
      ActivitiesDetailDto detailDto = this.nebulaToolkitService.copyObjectByWhiteList(itemVo, ActivitiesDetailDto.class, HashSet.class, ArrayList.class);
      detailDto.setActivitiesCode(itemVo.getParentCode());
      detailDto.setActivitiesDetailCode(itemVo.getItemCode());
      detailDto.setActivitiesName(activitiesVo.getActivitiesName());
      detailDto.setCostBudgetCode(itemVo.getCostBudgetCode());
      detailDto.setId(null);
      activitiesDetails.add(detailDto);
    }
    activitiesDto.setActivitiesDetails(Sets.newLinkedHashSet(activitiesDetails));
  }


  /**
   * 费用预算回退
   */
  private void backCostBudget(OrdinaryActivityVo ordinaryActivityVo) {
    for (String key : ordinaryActivityVo.getItems().keySet()) {
      List<BasicActivityItemVo> basicItems = JSONArray.parseArray(JSONArray.toJSONString(ordinaryActivityVo.getItems().get(key)), BasicActivityItemVo.class);
      Validate.notEmpty(basicItems, "明细信息不能为空");
      for (BasicActivityItemVo basicItem : basicItems) {
        costBudgetVoService.back(ordinaryActivityVo.getCode(), basicItem.getItemCode(), basicItem.getCostBudgetCode(), basicItem.getApplyAmount(), null, CostBudgetItemSourceType.ORDINARY_ACTIVITY.getDescr());
      }
    }
  }
}
