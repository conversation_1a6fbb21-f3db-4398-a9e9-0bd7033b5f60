package com.biz.crm.tpm.business.activities.quota.service;

import com.biz.crm.tpm.business.activities.quota.model.TpmQuotaActivityModel;

import java.util.Collection;

/**
 * tpm定额活动model接口
 */
public interface TpmQuotaActivityModelService {

  /**
   * 根据上级id和表单key查询定额活动model
   * @param parentCode 上级id
   * @param dynamicKey 表单key
   * @return 库存盘点model
   */
  Collection<TpmQuotaActivityModel> findByParentCodeAndDynamicKey(String parentCode, String dynamicKey);
}
