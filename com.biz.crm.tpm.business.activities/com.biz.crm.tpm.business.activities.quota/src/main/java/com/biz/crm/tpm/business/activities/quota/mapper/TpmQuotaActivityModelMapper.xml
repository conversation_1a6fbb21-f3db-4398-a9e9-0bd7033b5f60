<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.business.activities.quota.mapper.TpmQuotaActivityModelMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.quota.entity.TpmQuotaActivityModelEntity" id="TpmQuotaActivityModelEntityMap">
  </resultMap>
  
  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.quota.vo.TpmQuotaActivityModelVo">
    select 
    t.*
    from tpm_quota_activity_model t 
    <where>
        <if test="dto.tenantCode != null and dto.tenantCode != '' ">
          and t.tenant_code = #{dto.tenantCode}
        </if>
        <if test="dto.parentCode != null and dto.parentCode != '' ">
          and t.parent_code = #{dto.parentCode}
        </if>
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>