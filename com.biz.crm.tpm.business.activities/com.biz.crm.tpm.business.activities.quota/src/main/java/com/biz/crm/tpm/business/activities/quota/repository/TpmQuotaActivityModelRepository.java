package com.biz.crm.tpm.business.activities.quota.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.quota.entity.TpmQuotaActivityModelEntity;
import com.biz.crm.tpm.business.activities.quota.mapper.TpmQuotaActivityModelMapper;
import com.biz.crm.tpm.business.activities.quota.vo.TpmQuotaActivityModelVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * tpm定额活动表单;(tpm_quota_activity_model)表数据库访问层
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-3
 */
@Component
public class TpmQuotaActivityModelRepository extends ServiceImpl<TpmQuotaActivityModelMapper, TpmQuotaActivityModelEntity> {
  @Autowired
  private TpmQuotaActivityModelMapper TpmQuotaActivityModelMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<TpmQuotaActivityModelVo> findByConditions(Pageable pageable, TpmQuotaActivityModelVo dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<TpmQuotaActivityModelVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return TpmQuotaActivityModelMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmQuotaActivityModelEntity>
   */
  public List<TpmQuotaActivityModelEntity> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(TpmQuotaActivityModelEntity::getId, ids)
            .eq(TpmQuotaActivityModelEntity::getTenantCode, tenantCode)
            .list();
  }


  /**
   * 根绝业务编号parentCode获取业务数据
   *
   * @return 返单条数据
   */
  public TpmQuotaActivityModelEntity findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(TpmQuotaActivityModelEntity::getParentCode, parentCode)
            .eq(TpmQuotaActivityModelEntity::getTenantCode, tenantCode)
            .one();
  }

  /**
   * 根绝业务编号parentCode获取业务数据
   *
   * @return 返单条数据
   */
  public Collection<TpmQuotaActivityModelEntity> findByParentCodeAndDynamicKey(String parentCode, String dynamicKey) {
    if (StringUtils.isBlank(parentCode)) {
      return Collections.EMPTY_LIST;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(TpmQuotaActivityModelEntity::getParentCode, parentCode)
            .eq(TpmQuotaActivityModelEntity::getTenantCode, tenantCode)
            .eq(TpmQuotaActivityModelEntity::getDynamicKey, dynamicKey)
            .list();
  }

  /**
   * 根绝业务编号parentCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<TpmQuotaActivityModelEntity> findByParentCodes(Collection<String> parentCodes) {
    if (CollectionUtils.isEmpty(parentCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(TpmQuotaActivityModelEntity::getParentCode, parentCodes)
            .eq(TpmQuotaActivityModelEntity::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 删除数据
   */
  public boolean deleteByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(TpmQuotaActivityModelEntity::getParentCode, parentCode)
            .eq(TpmQuotaActivityModelEntity::getTenantCode, tenantCode)
            .remove();
  }

  public TpmQuotaActivityModelEntity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
            .eq(TpmQuotaActivityModelEntity::getTenantCode, tenantCode)
            .in(TpmQuotaActivityModelEntity::getId, id)
            .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
            .eq(TpmQuotaActivityModelEntity::getTenantCode, tenantCode)
            .in(TpmQuotaActivityModelEntity::getId, ids)
            .remove();
  }
}