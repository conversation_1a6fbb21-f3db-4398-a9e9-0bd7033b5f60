package com.biz.crm.tpm.business.activities.quota.entity;

import com.biz.crm.tpm.business.activities.sdk.template.BaseActivityItem;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.TenantEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Index;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：tpm定额活动表单;
 * <AUTHOR> jerry7
 * @date : 2022-11-3
 */
@ApiModel(value = "QuotaActivityModel",description = "tpm定额活动表单")
@TableName("tpm_quota_activity_model")
@Getter
@Setter
@Entity(name = "tpm_quota_activity_model")
@org.hibernate.annotations.Table(appliesTo = "tpm_quota_activity_model", comment = "tpm定额活动表单")
@Table(name = "tpm_quota_activity_model")
public class TpmQuotaActivityModelEntity extends BaseActivityItem {

  /** 活动大类编码 */
  @ApiModelProperty(name = "costTypeCategoryCode",notes = "活动大类编码 ", value= "活动大类编码")
  @Column(name = "cost_type_category_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动大类编码 '")
  private String costTypeCategoryCode;
  
  /** 活动大类名称 */
  @ApiModelProperty(name = "costTypeCategoryName",notes = "活动大类名称", value= "活动大类名称")
  @Column(name = "cost_type_category_name", nullable = true,  columnDefinition = "INT COMMENT '活动大类名称 '")
  private String costTypeCategoryName;
  
  /** 费用预算编码 */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编码", value= "费用预算编码")
  @Column(name = "cost_budget_code", nullable = true, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '费用预算编码 '")
  private String costBudgetCode;
  
  /** 活动细类编码 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编码", value= "活动细类编码")
  @Column(name = "cost_type_detail_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动细类编码 '")
  private String costTypeDetailCode;
  
  /** 活动细类 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类", value= "活动细类")
  @Column(name = "cost_type_detail_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动细类 '")
  private String costTypeDetailName;
  
  /** 预算科目编码 */
  @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编码", value= "预算科目编码")
  @Column(name = "budget_subjects_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '预算科目编码 '")
  private String budgetSubjectsCode;
  
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value= "预算科目名称")
  @Column(name = "budget_subjects_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;
  
  /** 组织编码 */
  @ApiModelProperty(name = "orgCode",notes = "组织编码", value= "组织编码")
  @Column(name = "org_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '组织编码 '")
  private String orgCode;
  
  /** 组织名称 */
  @ApiModelProperty(name = "orgName",notes = "组织名称", value= "组织名称")
  @Column(name = "org_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '组织名称 '")
  private String orgName;
  
  /** 客户编码 */
  @ApiModelProperty(name = "customerCode",notes = "客户编码", value= "客户编码")
  @Column(name = "customer_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '客户编码 '")
  private String customerCode;
  
  /** 客户名称 */
  @ApiModelProperty(name = "customerName",notes = "客户名称", value= "客户名称")
  @Column(name = "customer_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '客户名称 '")
  private String customerName;
  
  /** 终端编码 */
  @ApiModelProperty(name = "terminalCode",notes = "终端编码", value= "终端编码")
  @Column(name = "terminal_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '终端编码 '")
  private String terminalCode;
  
  /** 终端名称 */
  @ApiModelProperty(name = "terminalName",notes = "终端名称", value= "终端名称")
  @Column(name = "terminal_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '终端名称 '")
  private String terminalName;
  
  /** 预估销售额 */
  @ApiModelProperty(name = "estimateSaleAmount",notes = "预估销售额", value= "预估销售额")
  @Column(name = "estimate_sale_amount", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '预估销售额 '")
  private String estimateSaleAmount;
  
  /** 申请金额 */
  @ApiModelProperty(name = "applyAmount",notes = "申请金额", value= "申请金额")
  @Column(name = "apply_amount", nullable = true, length = 255,  columnDefinition = "decimal(20,4) COMMENT '申请金额 '")
  private BigDecimal applyAmount;
  
  /** 支付方式 */
  @ApiModelProperty(name = "payType",notes = "支付方式", value= "支付方式")
  @Column(name = "pay_type", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '支付方式 '")
  private String payType;
  
  /** 支付方式名称 */
  @ApiModelProperty(name = "payTypeName",notes = "支付方式名称", value= "支付方式名称")
  @Column(name = "pay_type_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '支付方式名称 '")
  private String payTypeName;
  
  /** 费用日期 */
  @ApiModelProperty(name = "feeDate",notes = "费用日期", value= "费用日期")
  @Column(name = "fee_date", nullable = true,  columnDefinition = "VARCHAR(255) COMMENT '费用日期 '")
  private String feeDate;
  
  /** 是否关闭 */
  @ApiModelProperty(name = "colsed",notes = "是否关闭", value= "是否关闭")
  @Column(name = "colsed", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '是否关闭 '")
  private String colsed;

}