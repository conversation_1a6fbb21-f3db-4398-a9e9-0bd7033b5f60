package com.biz.crm.tpm.business.activities.quota.service.internal;

import com.biz.crm.tpm.business.activities.quota.entity.TpmQuotaActivityModelEntity;
import com.biz.crm.tpm.business.activities.quota.model.TpmQuotaActivityModel;
import com.biz.crm.tpm.business.activities.quota.repository.TpmQuotaActivityModelRepository;
import com.biz.crm.tpm.business.activities.quota.service.TpmQuotaActivityModelService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;

/**
 * 拜访总结model接口实现
 */
@Service
public class TpmQuotaActivityModelServiceImpl implements TpmQuotaActivityModelService {

  @Autowired
  private TpmQuotaActivityModelRepository tpmQuotaActivityModelRepository;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public Collection<TpmQuotaActivityModel> findByParentCodeAndDynamicKey(String parentCode, String dynamicKey) {
    if (StringUtils.isAnyEmpty(parentCode, dynamicKey)) {
      return Collections.emptyList();
    }
    Collection<TpmQuotaActivityModelEntity> tpmQuotaActivityModelEntities = this.tpmQuotaActivityModelRepository.findByParentCodeAndDynamicKey(parentCode, dynamicKey);
    if (ObjectUtils.isEmpty(tpmQuotaActivityModelEntities)) {
      return Collections.emptyList();
    }
    return this.nebulaToolkitService.copyCollectionByWhiteList(tpmQuotaActivityModelEntities, TpmQuotaActivityModelEntity.class, TpmQuotaActivityModel.class, HashSet.class, ArrayList.class);
  }
}
