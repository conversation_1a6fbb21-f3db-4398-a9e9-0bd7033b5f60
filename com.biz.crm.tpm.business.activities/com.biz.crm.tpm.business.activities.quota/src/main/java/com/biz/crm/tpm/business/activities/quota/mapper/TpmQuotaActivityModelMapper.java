package com.biz.crm.tpm.business.activities.quota.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.quota.entity.TpmQuotaActivityModelEntity;
import com.biz.crm.tpm.business.activities.quota.vo.TpmQuotaActivityModelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * tpm定额活动表单;(tpm_quota_activity_model)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-3
 */
@Mapper
public interface TpmQuotaActivityModelMapper extends BaseMapper<TpmQuotaActivityModelEntity>{
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<TpmQuotaActivityModelVo> findByConditions(@Param("page") Page<TpmQuotaActivityModelVo> page , @Param("dto") TpmQuotaActivityModelVo dto);
}