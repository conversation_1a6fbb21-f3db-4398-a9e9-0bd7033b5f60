package com.biz.crm.tpm.business.activities.quota.vo;

import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Vo：tpm定额活动表单;
 * <AUTHOR> jerry7
 * @date : 2022-11-3
 */
@ApiModel(value = "QuotaActivityModel",description = "tpm定额活动表单")
@Getter
@Setter
public class TpmQuotaActivityModelVo extends BaseActivityItemVo {
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
  private String tenantCode;
  /** 修改人名称 */
  @ApiModelProperty(name = "modifyName",notes = "修改人名称", value= "修改人名称")
  private String modifyName;
  /** 修改人账号 */
  @ApiModelProperty(name = "modifyAccount",notes = "修改人账号", value= "修改人账号")
  private String modifyAccount;
  /** 修改时间 */
  @ApiModelProperty(name = "modifyTime",notes = "修改时间", value= "修改时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date modifyTime;
  /** 创建人名称 */
  @ApiModelProperty(name = "createName",notes = "创建人名称", value= "创建人名称")
  private String createName;
  /** 创建时间 */
  @ApiModelProperty(name = "createTime",notes = "创建时间", value= "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /** 创建人账号 */
  @ApiModelProperty(name = "createAccount",notes = "创建人账号", value= "创建人账号")
  private String createAccount;
  /** 执行计划业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "资源编号", value= "资源编号")
  private String parentCode;
  /** 步骤业务编码stepCode */
  @ApiModelProperty(name = "dynamicKey",notes = "代理人编号", value= "代理人编号")
  private String dynamicKey;
  /** 活动大类编码 */
  @ApiModelProperty(name = "costTypeCategoryCode",notes = "代理人名称", value= "代理人名称")
  private String costTypeCategoryCode;
  /** 活动大类名称 */
  @ApiModelProperty(name = "costTypeCategoryName",notes = "排序", value= "排序")
  private String costTypeCategoryName;
  /** 费用预算编码 */
  @ApiModelProperty(name = "costBudgetCode",notes = "类型", value= "类型")
  private String costBudgetCode;
  /** 活动细类编码 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编码", value= "活动细类编码")
  private String costTypeDetailCode;
  /** 活动细类 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类", value= "活动细类")
  private String costTypeDetailName;
  /** 预算科目编码 */
  @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编码", value= "预算科目编码")
  private String budgetSubjectsCode;
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value= "预算科目名称")
  private String budgetSubjectsName;
  /** 组织编码 */
  @ApiModelProperty(name = "orgCode",notes = "组织编码", value= "组织编码")
  private String orgCode;
  /** 组织名称 */
  @ApiModelProperty(name = "orgName",notes = "组织名称", value= "组织名称")
  private String orgName;
  /** 客户编码 */
  @ApiModelProperty(name = "customerCode",notes = "客户编码", value= "客户编码")
  private String customerCode;
  /** 客户名称 */
  @ApiModelProperty(name = "customerName",notes = "客户名称", value= "客户名称")
  private String customerName;
  /** 终端编码 */
  @ApiModelProperty(name = "terminalCode",notes = "终端编码", value= "终端编码")
  private String terminalCode;
  /** 终端名称 */
  @ApiModelProperty(name = "terminalName",notes = "终端名称", value= "终端名称")
  private String terminalName;
  /** 预估销售额 */
  @ApiModelProperty(name = "estimateSaleAmount",notes = "预估销售额", value= "预估销售额")
  private String estimateSaleAmount;
  /** 申请金额 */
  @ApiModelProperty(name = "applyAmount",notes = "申请金额", value= "申请金额")
  private BigDecimal applyAmount;
  /** 支付方式 */
  @ApiModelProperty(name = "payType",notes = "支付方式", value= "支付方式")
  private String payType;
  /** 支付方式名称 */
  @ApiModelProperty(name = "payTypeName",notes = "支付方式名称", value= "支付方式名称")
  private String payTypeName;
  /** 备注 */
  @ApiModelProperty(name = "remark",notes = "备注", value= "备注")
  private String remark;
  /** 费用日期 */
  @ApiModelProperty(name = "feeDate",notes = "费用日期", value= "费用日期")
  private String feeDate;
  /** 是否关闭 */
  @ApiModelProperty(name = "colsed",notes = "是否关闭", value= "是否关闭")
  private String colsed;

}