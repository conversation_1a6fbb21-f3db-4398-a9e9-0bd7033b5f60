package com.biz.crm.tpm.business.activities.quota.service;

import com.biz.crm.tpm.business.activities.quota.vo.TpmQuotaActivityModelVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * tpm定额活动表单;(tpm_quota_activity_model)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-3
 */
public interface TpmQuotaActivityEntityService {
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmQuotaActivityModelVo> findByConditions(Pageable pageable, TpmQuotaActivityModelVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmQuotaActivityModelVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmQuotaActivityModelVo> findByIds(Collection<String> ids);
  
  /**
   * 根绝业务编号parentCode获取业务数据
   *
   * @param parentCode
   * @return 单条数据
   */
  TpmQuotaActivityModelVo findByParentCode(String parentCode);
  /**
   * 根绝业务编号parentCode获取业务数据
   *
   * @param parentCodes
   * @return 多条数据
   */
  List<TpmQuotaActivityModelVo> findByParentCodes(Collection<String> parentCodes);
  /**
   * 新增数据
   *
   * @param TpmQuotaActivityModelVo 实体对象
   * @return 新增结果
   */
  TpmQuotaActivityModelVo create(TpmQuotaActivityModelVo TpmQuotaActivityModelVo);
  /**
   * 批量新增
   * @param TpmQuotaActivityModelVos
   * @return
   */
  List<TpmQuotaActivityModelVo> createBatch(Collection<TpmQuotaActivityModelVo> TpmQuotaActivityModelVos);
  /**
   * 修改数据
   *
   * @param TpmQuotaActivityModelVo 实体对象
   * @return 修改结果
   */
  TpmQuotaActivityModelVo update(TpmQuotaActivityModelVo TpmQuotaActivityModelVo);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
    
}