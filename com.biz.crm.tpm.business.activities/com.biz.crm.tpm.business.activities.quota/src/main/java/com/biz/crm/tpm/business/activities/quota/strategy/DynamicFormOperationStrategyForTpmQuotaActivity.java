package com.biz.crm.tpm.business.activities.quota.strategy;

import com.alibaba.fastjson.JSON;
import com.biz.crm.common.form.sdk.model.DynamicFormsOperationStrategy;
import com.biz.crm.tpm.business.activities.quota.model.TpmQuotaActivityModel;
import com.biz.crm.tpm.business.activities.quota.service.TpmQuotaActivityEntityService;
import com.biz.crm.tpm.business.activities.quota.service.TpmQuotaActivityModelService;
import com.biz.crm.tpm.business.activities.quota.vo.TpmQuotaActivityModelVo;
import com.biz.crm.tpm.business.activities.sdk.register.ActivitiesCenterModuleRegister;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * tpm定额活动表单数据保存转换策略
 *
 * <AUTHOR>
 */
@Component
public class DynamicFormOperationStrategyForTpmQuotaActivity implements DynamicFormsOperationStrategy<TpmQuotaActivityModel> {

  public static final String QUOTA_ACTIVITY_ITEM_OPERATION_STRATEGY_KEY = StringUtils.uncapitalize(DynamicFormOperationStrategyForTpmQuotaActivity.class.getSimpleName());


  @Autowired
  private TpmQuotaActivityEntityService tpmQuotaActivityEntityService;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Autowired
  private ActivitiesCenterModuleRegister activitiesCenterModuleRegister;

  @Autowired
  private TpmQuotaActivityModelService tpmQuotaActivityModelService;

  @Override
  public void onDynamicFormsCreate(Collection<TpmQuotaActivityModel> dynamicForms, String dynamicKey, String parentCode, Object parent) {
    List<TpmQuotaActivityModelVo> tpmQuotaActivityModelVos = JSON.parseArray(JSON.toJSONString(dynamicForms), TpmQuotaActivityModelVo.class);
    tpmQuotaActivityModelVos.forEach(dynamicForm -> {
      dynamicForm.setParentCode(parentCode);
      dynamicForm.setDynamicKey(dynamicKey);
      dynamicForm.setDynamicFormCode(QUOTA_ACTIVITY_ITEM_OPERATION_STRATEGY_KEY);
    });
    this.tpmQuotaActivityEntityService.createBatch(tpmQuotaActivityModelVos);
  }

  @Override
  public void onDynamicFormsModify(Collection<TpmQuotaActivityModel> dynamicForms, String dynamicKey, String parentCode, Object parent) {
    List<TpmQuotaActivityModelVo> tpmQuotaActivityModelVos = JSON.parseArray(JSON.toJSONString(dynamicForms), TpmQuotaActivityModelVo.class);
    tpmQuotaActivityModelVos.forEach(dynamicForm -> {
      dynamicForm.setParentCode(parentCode);
      dynamicForm.setDynamicKey(dynamicKey);
      dynamicForm.setDynamicFormCode(QUOTA_ACTIVITY_ITEM_OPERATION_STRATEGY_KEY);
      this.tpmQuotaActivityEntityService.update(dynamicForm);
    });
  }

  @Override
  public void onDynamicFormsDelete(String parentCode, String dynamicKey, String[] detailCodes) {
  }

  @Override
  public Collection<TpmQuotaActivityModel> findByParentCode(String dynamicKey, String parentCode) {
    if (StringUtils.isAnyBlank(dynamicKey, parentCode)) {
      return Collections.emptyList();
    }
    return this.tpmQuotaActivityModelService.findByParentCodeAndDynamicKey(parentCode, dynamicKey);
  }

  @Override
  public String dynamicFormCode() {
    return StringUtils.uncapitalize(this.getClass().getSimpleName());
  }

  @Override
  public String dynamicFormName() {
    return "定额活动";
  }

  @Override
  public Class<TpmQuotaActivityModel> dynamicFormClass() {
    return TpmQuotaActivityModel.class;
  }

  @Override
  public String moduleCode() {
    return activitiesCenterModuleRegister.moduleCode();
  }

  @Override
  public void onDynamicFormsDelete(String parentCode, String dynamicKey) {

  }

  @Override
  public int getOrder() {
    return 1;
  }
}
