package com.biz.crm.tpm.business.activities.quota.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.common.form.sdk.context.DynamicFormContext;
import com.biz.crm.common.form.sdk.context.DynamicFormContextHolder;
import com.biz.crm.tpm.business.activities.dynamic.template.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.dynamic.template.strategy.DynamicFormsOperationStrategyForDisplayActivityItem;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.DisplayActivityItemVo;
import com.biz.crm.tpm.business.activities.quota.entity.TpmQuotaActivityModelEntity;
import com.biz.crm.tpm.business.activities.quota.repository.TpmQuotaActivityModelRepository;
import com.biz.crm.tpm.business.activities.quota.service.TpmQuotaActivityEntityService;
import com.biz.crm.tpm.business.activities.quota.strategy.DynamicFormOperationStrategyForTpmQuotaActivity;
import com.biz.crm.tpm.business.activities.quota.vo.TpmQuotaActivityModelVo;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesShareDto;
import com.biz.crm.tpm.business.activities.sdk.strategy.share.ActivitiesShareStrategy;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * tpm定额活动表单;(tpm_quota_activity_model)表服务实现类
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-3
 */
@Service("TpmQuotaActivityModelEntityService")
public class TpmQuotaActivityEntityServiceImpl implements TpmQuotaActivityEntityService , ActivitiesShareStrategy {
  @Autowired
  private TpmQuotaActivityModelRepository tpmQuotaActivityModelRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Autowired
  private GenerateCodeService generateCodeService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<TpmQuotaActivityModelVo> findByConditions(Pageable pageable, TpmQuotaActivityModelVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmQuotaActivityModelVo();
    }
    return this.tpmQuotaActivityModelRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmQuotaActivityModelVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmQuotaActivityModelEntity tpmQuotaActivityModelEntity = this.tpmQuotaActivityModelRepository.findByIdAndTenantCode(id, TenantUtils.getTenantCode());
    if (tpmQuotaActivityModelEntity == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmQuotaActivityModelEntity, TpmQuotaActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmQuotaActivityModelVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmQuotaActivityModelEntity> tpmQuotaActivityModelEntitys = this.tpmQuotaActivityModelRepository.findByIds(ids);
    Collection<TpmQuotaActivityModelVo> tpmQuotaActivityModelVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmQuotaActivityModelEntitys, TpmQuotaActivityModelEntity.class, TpmQuotaActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmQuotaActivityModelVos);
  }

  /**
   * 更新金额
   *
   * @param item
   */
  private void processDynamicFormContextForCreateOrUpdate(TpmQuotaActivityModelEntity item) {
    DynamicFormContext context = DynamicFormContextHolder.getContext();
    if (context.exist(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY)) {
      BigDecimal totalApplyAmount = (BigDecimal) context.get(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY);
      context.put(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY, totalApplyAmount.add(item.getApplyAmount()));
    }
  }

  /**
   * 根绝业务编号parentCode获取业务数据
   *
   * @param parentCode
   * @return 单条数据
   */
  @Override
  public TpmQuotaActivityModelVo findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    TpmQuotaActivityModelEntity tpmQuotaActivityModelEntity = this.tpmQuotaActivityModelRepository.findByParentCode(parentCode);
    if (tpmQuotaActivityModelEntity == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmQuotaActivityModelEntity, TpmQuotaActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 根绝业务编号parentCode获取业务数据
   *
   * @param parentCodes
   * @return 多条数据
   */
  @Override
  public List<TpmQuotaActivityModelVo> findByParentCodes(Collection<String> parentCodes) {
    if (CollectionUtils.isEmpty(parentCodes)) {
      return Collections.emptyList();
    }
    List<TpmQuotaActivityModelEntity> tpmQuotaActivityModelEntitys = this.tpmQuotaActivityModelRepository.findByParentCodes(parentCodes);
    if (CollectionUtils.isEmpty(tpmQuotaActivityModelEntitys)) {
      return Collections.emptyList();
    }
    Collection<TpmQuotaActivityModelVo> tpmQuotaActivityModelVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmQuotaActivityModelEntitys, TpmQuotaActivityModelEntity.class, TpmQuotaActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmQuotaActivityModelVos);
  }

  /**
   * 新增数据
   *
   * @param tpmQuotaActivityModelVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmQuotaActivityModelVo create(TpmQuotaActivityModelVo tpmQuotaActivityModelVo) {
    this.createValidate(tpmQuotaActivityModelVo);
    List<String> codeList = this.generateCodeService.generateCode(ActivitiesConstant.QUOTA_MX_CODE_KEY, 1);
    tpmQuotaActivityModelVo.setItemCode(codeList.get(0));
    TpmQuotaActivityModelEntity tpmQuotaActivityModelEntity = this.nebulaToolkitService.copyObjectByWhiteList(tpmQuotaActivityModelVo, TpmQuotaActivityModelEntity.class, LinkedHashSet.class, ArrayList.class);
    tpmQuotaActivityModelEntity.setTenantCode(TenantUtils.getTenantCode());
    this.tpmQuotaActivityModelRepository.saveOrUpdate(tpmQuotaActivityModelEntity);
    this.processDynamicFormContextForCreateOrUpdate(tpmQuotaActivityModelEntity);
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmQuotaActivityModelEntity, TpmQuotaActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 批量新增
   *
   * @param tpmQuotaActivityModelVos
   * @return
   */
  @Transactional
  @Override
  public List<TpmQuotaActivityModelVo> createBatch(Collection<TpmQuotaActivityModelVo> tpmQuotaActivityModelVos) {
    if (CollectionUtils.isEmpty(tpmQuotaActivityModelVos)) {
      return Lists.newArrayList();
    }
    List<TpmQuotaActivityModelVo> results = Lists.newArrayList();
    for (TpmQuotaActivityModelVo tpmQuotaActivityModelVo : tpmQuotaActivityModelVos) {
      TpmQuotaActivityModelVo result = this.create(tpmQuotaActivityModelVo);
      results.add(result);
    }
    return results;
  }

  /**
   * 修改新据
   *
   * @param tpmQuotaActivityModelVo 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmQuotaActivityModelVo update(TpmQuotaActivityModelVo tpmQuotaActivityModelVo) {
    this.updateValidate(tpmQuotaActivityModelVo);
    TpmQuotaActivityModelEntity tpmQuotaActivityModelEntity = this.tpmQuotaActivityModelRepository.findByIdAndTenantCode(tpmQuotaActivityModelVo.getId(), TenantUtils.getTenantCode());
    Validate.notNull(tpmQuotaActivityModelEntity, "修改数据不存在，请检查！");
    TpmQuotaActivityModelEntity newEntity = this.nebulaToolkitService.copyObjectByWhiteList(tpmQuotaActivityModelVo, TpmQuotaActivityModelEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmQuotaActivityModelRepository.saveOrUpdate(newEntity);
    this.processDynamicFormContextForCreateOrUpdate(newEntity);
    return tpmQuotaActivityModelVo;
  }


  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmQuotaActivityModelEntity> TpmQuotaActivityModelEntitys = this.tpmQuotaActivityModelRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(TpmQuotaActivityModelEntitys)) {
      return;
    }
    Collection<TpmQuotaActivityModelVo> TpmQuotaActivityModelVos = this.nebulaToolkitService.copyCollectionByWhiteList(TpmQuotaActivityModelEntitys, TpmQuotaActivityModelEntity.class, TpmQuotaActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    this.tpmQuotaActivityModelRepository.removeByIdsAndTenantCode(ids, TenantUtils.getTenantCode());
  }

  /**
   * 创建验证
   *
   * @param TpmQuotaActivityModelVo
   */
  private void createValidate(TpmQuotaActivityModelVo TpmQuotaActivityModelVo) {
    Validate.notNull(TpmQuotaActivityModelVo, "新增时，对象信息不能为空！");
    Validate.isTrue(TpmQuotaActivityModelVo.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(TpmQuotaActivityModelVo.getParentCode(), "新增数据时，执行计划业务编码不能为空！");
  }

  /**
   * 修改验证
   *
   * @param TpmQuotaActivityModelVo
   */
  private void updateValidate(TpmQuotaActivityModelVo TpmQuotaActivityModelVo) {
    Validate.notNull(TpmQuotaActivityModelVo, "修改时，对象信息不能为空！");
    Validate.notBlank(TpmQuotaActivityModelVo.getId(), "修改数据时，主键不能为空！");
  }

  @Override
  public String dynamicFormCode() {
    return DynamicFormOperationStrategyForTpmQuotaActivity.QUOTA_ACTIVITY_ITEM_OPERATION_STRATEGY_KEY;
  }

  @Override
  public List<?> share(ActivitiesShareDto dto) {
    Validate.notNull(dto,"传入的待分摊信息不能为空");
    Date startTime = dto.getStartTime();
    Date endTime = dto.getEndTime();
    String dynamicFormCode = dto.getDynamicFormCode();
    JSONObject dynamicForm = dto.getDynamicForm();
    Validate.notNull(startTime,"活动开始时间不能为空");
    Validate.notNull(endTime,"活动结束时间不能为空");
    Validate.notBlank(dynamicFormCode,"动态表单编码不能为空");
    Validate.notEmpty(dynamicForm,"动态表单信息不能为空");
    DisplayActivityItemVo itemVo = JSONObject.parseObject(JSONObject.toJSONString(dynamicForm),DisplayActivityItemVo.class);
    Validate.isTrue(StringUtils.isBlank(itemVo.getFeeDate()),"动态表单信息中，费用年月不能有值，请检查");

    //是否跨月
    String startTimeStr = DateFormatUtils.format(startTime,"yyyy-MM");
    String endTimeStr = DateFormatUtils.format(endTime,"yyyy-MM");
    boolean crossMonth = !StringUtils.equals(startTimeStr,endTimeStr);
    Validate.isTrue(crossMonth,"活动时间范围不存在跨月情况，不能进行分摊");
    //拆解时间范围，验证费用年月
    LinkedList<String> times = Lists.newLinkedList();
    LinkedList<BigDecimal> amounts = Lists.newLinkedList();
    this.dismantleTimes(startTime,endTimeStr,times);
    this.dismantleAmount(itemVo.getApplyAmount(),times.size(),amounts);
    List<DisplayActivityItemVo> result = Lists.newArrayList();
    String shareKey = String.valueOf(System.currentTimeMillis());
    for(String time : times){
      DisplayActivityItemVo copy = nebulaToolkitService.copyObjectByWhiteList(itemVo,DisplayActivityItemVo.class, HashSet.class, ArrayList.class);
      copy.setItemCode(null);
      copy.setId(null);
      copy.setShareKey(shareKey);
      copy.setFeeDate(time);
      copy.setApplyAmount(amounts.poll());
      result.add(copy);
    }
    return result;
  }

  /**
   * 按照活动开始时间和活动结束时间，拆解时间
   */
  private void dismantleTimes(Date startTime, String endTimeStr, List<String> times){
    String startTimeStr = DateFormatUtils.format(startTime,"yyyy-MM");
    if(StringUtils.compare(startTimeStr,endTimeStr) >= 0){
      times.add(endTimeStr);
      return;
    }
    times.add(DateFormatUtils.format(startTime,"yyyy-MM"));
    this.dismantleTimes(DateUtils.addMonths(startTime,1),endTimeStr,times);
  }

  /**
   * 按照指定申请金额，分摊金额
   */
  private void dismantleAmount(BigDecimal applyAmount, int size, List<BigDecimal> amounts){
    Validate.notNull(applyAmount,"指定的申请金额不能为空");
    if (size == 0) {
      return;
    }
    Validate.isTrue(applyAmount.compareTo(new BigDecimal("0.01").multiply(BigDecimal.valueOf(size))) >= 0 ,"指定的申请金额【%s】不满足最小分摊金额，请检查",applyAmount.toString());
    BigDecimal shareAmount = applyAmount.divide(BigDecimal.valueOf(size),2, RoundingMode.DOWN);
    for(int index = 0 ; index < size - 1 ; index++){
      amounts.add(shareAmount);
    }
    //组装最后一次的分摊金额
    BigDecimal shareAmounts = amounts.stream()
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO,BigDecimal::add);
    BigDecimal lastShareAmount = applyAmount.subtract(shareAmounts);
    amounts.add(lastShareAmount);
  }

}