package com.biz.crm.tpm.business.activities.meeting.vo;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

/**
 * Vo：tpm会议执行扫码数据;
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@ApiModel(value = "MeetingActivityScan",description = "tpm会议执行扫码数据")
@Getter
@Setter
public class TpmMeetingActivityScanVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 活动表单编码 */
  @ApiModelProperty(name = "activityModelId",notes = "活动表单编码", value= "活动表单编码")
  private String activityModelId;
  /** 扫码编号 */
  @ApiModelProperty(name = "scanCode",notes = "扫码编号", value= "扫码编号")
  private String scanCode;
  /** 物料编码 */
  @ApiModelProperty(name = "materialCode",notes = "物料编码", value= "物料编码")
  private String materialCode;
  /** 物料名称 */
  @ApiModelProperty(name = "materialName",notes = "物料名称", value= "物料名称")
  private String materialName;
  /** 扫码时间 */
  @ApiModelProperty(name = "scanTime",notes = "扫码时间", value= "扫码时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date scanTime;
  /** 扫码位置 */
  @ApiModelProperty(name = "scanLocation",notes = "扫码位置", value= "扫码位置")
  private String scanLocation;

}