package com.biz.crm.tpm.business.activities.meeting.strategy;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.meeting.model.TpmMeetingActivityModel;
import com.biz.crm.tpm.business.budget.sdk.strategy.DynamicTemplateOperationStrategy;
import com.bizunited.nebula.common.util.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 定额活动表单模板操作策略实现类
 *
 * <AUTHOR>
 */
@Component
public class DynamicTemplateOperationStrategyForTpmMeetingActivity implements DynamicTemplateOperationStrategy {

  @Autowired
  private DynamicFormOperationStrategyForTpmMeetingActivity dynamicFormOperationStrategyForTpmMeetingActivity;

  @Override
  public String dynamicFormCode() {
    return this.dynamicFormOperationStrategyForTpmMeetingActivity.dynamicFormCode();
  }

  @Override
  public void onDynamicTemplateCreate(JSONObject jsonObject, String dynamicKey, String parentCode) {
    TpmMeetingActivityModel tpmQuotaActivityModel = JsonUtils.json2Obj(jsonObject.toJSONString(), TpmMeetingActivityModel.class);
    this.dynamicFormOperationStrategyForTpmMeetingActivity.onDynamicFormCreate(tpmQuotaActivityModel, dynamicKey, parentCode, null);
  }

  @Override
  public void onDynamicTemplateModify(JSONObject jsonObject, String dynamicKey, String parentCode) {
    TpmMeetingActivityModel tpmQuotaActivityModel = JsonUtils.json2Obj(jsonObject.toJSONString(), TpmMeetingActivityModel.class);
    this.dynamicFormOperationStrategyForTpmMeetingActivity.onDynamicFormModify(tpmQuotaActivityModel, dynamicKey, parentCode, null);
  }

  @Override
  public Object findByParentCode(String dynamicKey, String parentCode) {
    return null;
  }
}
