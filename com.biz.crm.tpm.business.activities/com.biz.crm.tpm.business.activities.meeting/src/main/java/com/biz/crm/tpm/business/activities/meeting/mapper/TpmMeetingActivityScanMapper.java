package com.biz.crm.tpm.business.activities.meeting.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityScanEntity;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityScanVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * tpm会议执行扫码数据;(tpm_meeting_activity_scan)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Mapper
public interface TpmMeetingActivityScanMapper extends BaseMapper<TpmMeetingActivityScanEntity>{
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<TpmMeetingActivityScanVo> findByConditions(@Param("page") Page<TpmMeetingActivityScanVo> page , @Param("dto") TpmMeetingActivityScanVo vo);
}