package com.biz.crm.tpm.business.activities.meeting.model;


import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.field.annotation.Validate;
import com.biz.crm.common.form.sdk.field.validate.NotBlankValidateStrategy;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.SimpleDateSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.activities.sdk.widget.ImgSelectWidget;
import com.biz.crm.tpm.business.budget.sdk.model.AbstractDynamicTemplateModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 会议执行表单文件model
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TpmMeetingFileModel", description = "会议执行表单文件model")
public class TpmMeetingFileModel extends AbstractDynamicTemplateModel implements DynamicForm {

  /** 活动照片地址 */
  @ApiModelProperty(name = "activityFileUrl",notes = "活动照片地址", value= "活动申请编码")
  @DynamicField(fieldName = "活动照片地址", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = ImgSelectWidget.class)
  private String activityFileUrl;

  /** 活动照片备注 */
  @ApiModelProperty(name = "activityFileRemark",notes = "活动照片备注", value= "活动照片备注")
  @DynamicField(fieldName = "活动照片备注", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String activityFileRemark;

  /** 活动大类编码 */
  @ApiModelProperty(name = "activityFileType",notes = "活动文件类别(0-照片/1-视频)", value= "活动文件类别(0-照片/1-视频)")
  @DynamicField(fieldName = "活动文件类别(0-照片/1-视频)", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String activityFileType;

}
