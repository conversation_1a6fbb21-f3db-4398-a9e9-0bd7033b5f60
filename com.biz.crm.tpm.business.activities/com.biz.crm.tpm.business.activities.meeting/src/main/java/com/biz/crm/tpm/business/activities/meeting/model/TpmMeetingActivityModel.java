package com.biz.crm.tpm.business.activities.meeting.model;


import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.SimpleDateSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 活动细类会议执行表单model
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TpmMeetingActivityModel", description = "活动细类会议执行表单model")
public class TpmMeetingActivityModel extends BaseActivityItemVo implements DynamicForm {
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  @DynamicField(fieldName = "业务编码", required = false, controllKey = SimpleInputWidget.class)
  private String parentCode;

  /** 活动申请名称 */
  @ApiModelProperty(name = "activityName",notes = "活动申请名称", value= "活动申请名称")
  @DynamicField(fieldName = "活动申请名称", required = false, controllKey = SimpleInputWidget.class)
  private String activityName;

  /** 活动申请名称 */
  @ApiModelProperty(name = "activityCode",notes = "活动申请编码", value= "活动申请编码")
  @DynamicField(fieldName = "活动申请编码", required = false, controllKey = SimpleInputWidget.class)
  private String activityCode;

  /** 费用日期 */
  @ApiModelProperty(name = "startTime",notes = "申请日期", value= "申请日期")
  @DynamicField(fieldName = "费用日期", required = false, controllKey = SimpleDateSelectWidget.class)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  /** 活动执行说明 */
  @ApiModelProperty(name = "executionRemark",notes = "活动执行说明", value= "活动执行说明")
  @DynamicField(fieldName = "活动执行说明", required = false, controllKey = SimpleInputWidget.class)
  private String executionRemark;

}
