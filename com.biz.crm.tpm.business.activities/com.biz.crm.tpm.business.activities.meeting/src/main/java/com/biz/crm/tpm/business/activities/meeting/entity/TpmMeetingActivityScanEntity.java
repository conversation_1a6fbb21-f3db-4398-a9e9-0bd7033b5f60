package com.biz.crm.tpm.business.activities.meeting.entity;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.UuidEntity;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：tpm会议执行扫码数据;
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@ApiModel(value = "MeetingActivityScan",description = "tpm会议执行扫码数据")
@TableName("tpm_meeting_activity_scan")
@Getter
@Setter
@Entity(name = "tpm_meeting_activity_scan")
@org.hibernate.annotations.Table(appliesTo = "tpm_meeting_activity_scan", comment = "tpm会议执行扫码数据")
@Table(name = "tpm_meeting_activity_scan")
public class TpmMeetingActivityScanEntity  extends UuidEntity{
  
  /** 活动表单编码 */
  @ApiModelProperty(name = "activityModelId",notes = "活动表单编码", value= "活动表单编码")
  @Column(name = "activity_model_id", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动表单编码 '")
  private String activityModelId;
  
  /** 扫码编号 */
  @ApiModelProperty(name = "scanCode",notes = "扫码编号", value= "扫码编号")
  @Column(name = "scan_code", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '扫码编号 '")
  private String scanCode;
  
  /** 物料编码 */
  @ApiModelProperty(name = "materialCode",notes = "物料编码", value= "物料编码")
  @Column(name = "material_code", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '物料编码 '")
  private String materialCode;
  
  /** 物料名称 */
  @ApiModelProperty(name = "materialName",notes = "物料名称", value= "物料名称")
  @Column(name = "material_name", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '物料名称 '")
  private String materialName;
  
  /** 扫码时间 */
  @ApiModelProperty(name = "scanTime",notes = "扫码时间", value= "扫码时间")
  @Column(name = "scan_time", nullable = true,  columnDefinition = "DATETIME COMMENT '扫码时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date scanTime;
  
  /** 扫码位置 */
  @ApiModelProperty(name = "scanLocation",notes = "扫码位置", value= "扫码位置")
  @Column(name = "scan_location", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '扫码位置 '")
  private String scanLocation;

}