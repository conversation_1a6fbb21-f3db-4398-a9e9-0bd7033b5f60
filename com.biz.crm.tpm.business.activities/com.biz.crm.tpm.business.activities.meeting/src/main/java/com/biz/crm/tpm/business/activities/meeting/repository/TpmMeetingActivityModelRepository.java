package com.biz.crm.tpm.business.activities.meeting.repository;

import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityModelEntity;
import com.biz.crm.tpm.business.activities.meeting.mapper.TpmMeetingActivityModelMapper;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityModelVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.util.Collection;
import java.util.List;
import java.util.Collections;

/**
 * tpm会议执行表单;(tpm_meeting_activity_model)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Component
public class TpmMeetingActivityModelRepository extends ServiceImpl<TpmMeetingActivityModelMapper, TpmMeetingActivityModelEntity>{
  @Autowired
  private TpmMeetingActivityModelMapper tpmMeetingActivityModelMapper;
  
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  public Page<TpmMeetingActivityModelVo> findByConditions(Pageable pageable, TpmMeetingActivityModelVo dto) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<TpmMeetingActivityModelVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmMeetingActivityModelMapper.findByConditions(page, dto);
  }
  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmMeetingActivityModel>
   */
  public List<TpmMeetingActivityModelEntity> findByIds(Collection<String> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .in(TpmMeetingActivityModelEntity::getId, ids)
        .eq(TpmMeetingActivityModelEntity::getTenantCode, tenantCode)
        .list();
  }

  /**
   * 根据parentCode和dynamicKey查询表单数据
   * @param parentCode 业务编码
   * @param dynamicKey 表单编码
   * @return 表单数据
   */
  public TpmMeetingActivityModelEntity findByParentCodeAndDynamicKey(String parentCode, String dynamicKey) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(TpmMeetingActivityModelEntity::getParentCode, parentCode)
            .eq(TpmMeetingActivityModelEntity::getTenantCode, tenantCode)
            .one();
  }

  public TpmMeetingActivityModelEntity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(TpmMeetingActivityModelEntity::getTenantCode,tenantCode)
        .in(TpmMeetingActivityModelEntity::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(TpmMeetingActivityModelEntity::getTenantCode,tenantCode)
        .in(TpmMeetingActivityModelEntity::getId,ids)
        .remove();
  }
}