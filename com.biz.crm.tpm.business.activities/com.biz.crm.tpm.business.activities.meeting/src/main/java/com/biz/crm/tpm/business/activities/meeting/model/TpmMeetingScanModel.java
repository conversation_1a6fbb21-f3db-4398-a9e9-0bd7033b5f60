package com.biz.crm.tpm.business.activities.meeting.model;


import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.field.annotation.Validate;
import com.biz.crm.common.form.sdk.field.validate.NotBlankValidateStrategy;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.SimpleDateSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.activities.sdk.widget.ImgSelectWidget;
import com.biz.crm.tpm.business.budget.sdk.model.AbstractDynamicTemplateModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 会议执行表单扫码数据model
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TpmMeetingScanModel", description = "会议执行表单扫码数据model")
public class TpmMeetingScanModel extends AbstractDynamicTemplateModel implements DynamicForm {

  /** 扫码序号 */
  @ApiModelProperty(name = "scanCode",notes = "扫码序号", value= "扫码序号")
  @DynamicField(fieldName = "扫码序号", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String scanCode;

  /** 物料编码 */
  @ApiModelProperty(name = "materialCode",notes = "物料编码", value= "物料编码")
  @DynamicField(fieldName = "物料编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String materialCode;

  /** 物料名称 */
  @ApiModelProperty(name = "materialName",notes = "物料名称", value= "物料名称")
  @DynamicField(fieldName = "物料名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String materialName;

  /** 扫码日期 */
  @ApiModelProperty(name = "scanTime",notes = "扫码时间", value= "扫码时间")
  @DynamicField(fieldName = "扫码时间", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleDateSelectWidget.class)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date scanTime;

  /** 扫码地点 */
  @ApiModelProperty(name = "scanLocation",notes = "扫码地点", value= "扫码地点")
  @DynamicField(fieldName = "扫码地点", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String scanLocation;
}
