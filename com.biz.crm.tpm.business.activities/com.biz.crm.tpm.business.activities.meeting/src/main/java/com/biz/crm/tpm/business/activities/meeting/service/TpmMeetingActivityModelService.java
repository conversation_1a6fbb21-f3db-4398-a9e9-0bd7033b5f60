package com.biz.crm.tpm.business.activities.meeting.service;

import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityModelVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * tpm会议执行表单;(tpm_meeting_activity_model)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
public interface TpmMeetingActivityModelService{
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmMeetingActivityModelVo> findByConditions(Pageable pageable, TpmMeetingActivityModelVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmMeetingActivityModelVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmMeetingActivityModelVo> findByIds(Collection<String> ids);
  /**
   * 通过比编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  TpmMeetingActivityModelVo findByParentCode(String code);
  
  /**
   * 新增数据
   *
   * @param tpmMeetingActivityModelDto 实体对象
   * @return 新增结果
   */
  TpmMeetingActivityModelVo create(TpmMeetingActivityModelVo tpmMeetingActivityModelDto);
  /**
   * 批量新增
   * @param tpmMeetingActivityModelVos 实体对象
   * @return 新增结果
   */
  List<TpmMeetingActivityModelVo> createBatch(Collection<TpmMeetingActivityModelVo> tpmMeetingActivityModelVos);
  /**
   * 修改数据
   *
   * @param tpmMeetingActivityModelVo 实体对象
   * @return 修改结果
   */
  TpmMeetingActivityModelVo update(TpmMeetingActivityModelVo tpmMeetingActivityModelVo);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
    
}