package com.biz.crm.tpm.business.activities.meeting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityModelEntity;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityModelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * tpm会议执行表单;(tpm_meeting_activity_model)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Mapper
public interface TpmMeetingActivityModelMapper extends BaseMapper<TpmMeetingActivityModelEntity> {
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<TpmMeetingActivityModelVo> findByConditions(@Param("page") Page<TpmMeetingActivityModelVo> page , @Param("dto") TpmMeetingActivityModelVo dto);
}