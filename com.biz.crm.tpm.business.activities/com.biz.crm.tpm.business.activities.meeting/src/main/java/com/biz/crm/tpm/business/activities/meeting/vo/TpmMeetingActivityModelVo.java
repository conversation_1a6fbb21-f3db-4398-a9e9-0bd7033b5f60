package com.biz.crm.tpm.business.activities.meeting.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

/**
 * Vo：tpm会议执行表单;
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@ApiModel(value = "MeetingActivityModel",description = "tpm会议执行表单")
@Getter
@Setter
public class TpmMeetingActivityModelVo extends TenantFlagOpVo {

  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  private String parentCode;
  /** 表单key */
  @ApiModelProperty(name = "dynamicKey",notes = "表单key", value= "表单key")
  private String dynamicKey;
  /** 活动编码 */
  @ApiModelProperty(name = "activityCode",notes = "活动编码", value= "活动编码")
  private String activityCode;
  /** 活动名称 */
  @ApiModelProperty(name = "activityName",notes = "活动名称", value= "活动名称")
  private String activityName;
  /** 活动申请时间 */
  @ApiModelProperty(name = "startTime",notes = "活动申请时间", value= "活动申请时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;
  /** 执行说明 */
  @ApiModelProperty(name = "executionRemark",notes = "执行说明", value= "执行说明")
  private String executionRemark;
  /**
   * 活动执行关联文件
   */
  @ApiModelProperty(name = "fileVos",notes = "活动执行关联文件",value = "活动执行关联文件")
  private List<TpmMeetingActivityFileVo> fileVos;

  /**
   * 活动执行关联扫码记录
   */
  @ApiModelProperty(name = "fileVos",notes = "活动执行关联扫码记录",value = "活动执行关联扫码")
  private List<TpmMeetingActivityScanVo> scanVos;

}