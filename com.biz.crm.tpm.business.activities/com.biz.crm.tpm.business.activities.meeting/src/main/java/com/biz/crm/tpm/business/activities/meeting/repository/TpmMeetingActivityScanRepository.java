package com.biz.crm.tpm.business.activities.meeting.repository;
import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityScanEntity;
import com.biz.crm.tpm.business.activities.meeting.mapper.TpmMeetingActivityScanMapper;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityScanVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import java.util.Collection;
import java.util.List;
import java.util.Collections;

/**
 * tpm会议执行扫码数据;(tpm_meeting_activity_scan)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Component
public class TpmMeetingActivityScanRepository extends ServiceImpl<TpmMeetingActivityScanMapper, TpmMeetingActivityScanEntity>{
  @Autowired
  private TpmMeetingActivityScanMapper tpmMeetingActivityScanMapper;
  
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  public Page<TpmMeetingActivityScanVo> findByConditions(Pageable pageable, TpmMeetingActivityScanVo dto) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    Page<TpmMeetingActivityScanVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmMeetingActivityScanMapper.findByConditions(page, dto);
  }
  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmMeetingActivityScan>
   */
  public List<TpmMeetingActivityScanEntity> findByIds(Collection<String> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Collections.emptyList();
    }
    return this.lambdaQuery()
        .in(TpmMeetingActivityScanEntity::getId, ids)
        .list();
  }
  /**
   * 根据表单id集合获取详情集合
   *
   * @param modelId 表单id
   * @return List<TpmMeetingActivityScan>
   */
  public List<TpmMeetingActivityScanEntity> findByModelId(String modelId) {
    if(StringUtils.isBlank(modelId)){
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .in(TpmMeetingActivityScanEntity::getActivityModelId, modelId)
            .list();
  }
  
}