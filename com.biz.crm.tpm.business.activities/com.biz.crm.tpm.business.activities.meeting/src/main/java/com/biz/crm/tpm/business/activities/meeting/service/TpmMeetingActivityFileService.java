package com.biz.crm.tpm.business.activities.meeting.service;

import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityFileVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * tpm会议执行文件;(tpm_meeting_activity_file)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
public interface TpmMeetingActivityFileService{
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmMeetingActivityFileVo> findByConditions(Pageable pageable, TpmMeetingActivityFileVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmMeetingActivityFileVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmMeetingActivityFileVo> findByIds(Collection<String> ids);
  /**
   * 通过表单编码查询文件数据
   *
   * @param modelId 编号
   * @return 单条数据
   */
  List<TpmMeetingActivityFileVo> findByModelId(String modelId);
  
  /**
   * 新增数据
   *
   * @param tpmMeetingActivityFileVo 实体对象
   * @return 新增结果
   */
  TpmMeetingActivityFileVo create(TpmMeetingActivityFileVo tpmMeetingActivityFileVo);
  /**
   * 批量新增
   * @param tpmMeetingActivityFileVos
   * @return
   */
  List<TpmMeetingActivityFileVo> createBatch(Collection<TpmMeetingActivityFileVo> tpmMeetingActivityFileVos);
  /**
   * 修改数据
   *
   * @param tpmMeetingActivityFileVo 实体对象
   * @return 修改结果
   */
  TpmMeetingActivityFileVo update(TpmMeetingActivityFileVo tpmMeetingActivityFileVo);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
    
}