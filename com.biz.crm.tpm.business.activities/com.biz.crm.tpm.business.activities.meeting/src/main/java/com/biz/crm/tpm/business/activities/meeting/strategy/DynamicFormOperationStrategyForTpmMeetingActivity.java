package com.biz.crm.tpm.business.activities.meeting.strategy;

import com.biz.crm.common.form.sdk.model.DynamicFormOperationStrategy;
import com.biz.crm.tpm.business.activities.meeting.model.TpmMeetingActivityModel;
import com.biz.crm.tpm.business.activities.meeting.service.MeetingModelService;
import com.biz.crm.tpm.business.activities.meeting.service.TpmMeetingActivityModelService;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityModelVo;
import com.biz.crm.tpm.business.activities.sdk.register.ActivitiesCenterModuleRegister;
import com.biz.crm.tpm.business.activities.sdk.register.ExecutionCenterModuleRegister;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;

/**
 * tpm会议活动执行表单数据保存转换策略
 * <AUTHOR>
 */
@Component
public class DynamicFormOperationStrategyForTpmMeetingActivity implements DynamicFormOperationStrategy<TpmMeetingActivityModel> {


  @Autowired
  private TpmMeetingActivityModelService tpmMeetingActivityModelService;

  @Autowired
  private ExecutionCenterModuleRegister executionCenterModuleRegister;

  @Autowired
  private MeetingModelService meetingModelService;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public void onDynamicFormCreate(TpmMeetingActivityModel dynamicForm, String dynamicKey, String parentCode, Object parent) {
    dynamicForm.setParentCode(parentCode);
    dynamicForm.setDynamicKey(dynamicKey);
    TpmMeetingActivityModelVo tpmQuotaActivityModelVo = this.nebulaToolkitService.copyObjectByWhiteList(dynamicForm, TpmMeetingActivityModelVo.class, HashSet.class, ArrayList.class,"fileVos","scanVos");
    this.tpmMeetingActivityModelService.create(tpmQuotaActivityModelVo);
  }

  @Override
  public void onDynamicFormModify(TpmMeetingActivityModel dynamicForm, String dynamicKey, String parentCode, Object parent) {
    dynamicForm.setParentCode(parentCode);
    dynamicForm.setDynamicKey(dynamicKey);
    TpmMeetingActivityModelVo tpmQuotaActivityModelVo = this.nebulaToolkitService.copyObjectByWhiteList(dynamicForm, TpmMeetingActivityModelVo.class, HashSet.class, ArrayList.class,"fileVos","scanVos");
    this.tpmMeetingActivityModelService.update(tpmQuotaActivityModelVo);
  }

  @Override
  public TpmMeetingActivityModel findByParentCode(String dynamicKey, String parentCode) {
    if (StringUtils.isAnyBlank(dynamicKey, parentCode)) {
      return null;
    }
    return this.meetingModelService.findByParentCodeAndDynamicKey(parentCode, dynamicKey);
  }

  @Override
  public String dynamicFormCode() {
    return StringUtils.uncapitalize(this.getClass().getSimpleName());
  }

  @Override
  public String dynamicFormName() {
    return "会议活动执行";
  }

  @Override
  public Class<TpmMeetingActivityModel> dynamicFormClass() {
    return TpmMeetingActivityModel.class;
  }

  @Override
  public String moduleCode() {
    return executionCenterModuleRegister.moduleCode();
  }

  @Override
  public void onDynamicFormsDelete(String parentCode, String dynamicKey) {

  }

  @Override
  public int getOrder() {
    return 1;
  }
}
