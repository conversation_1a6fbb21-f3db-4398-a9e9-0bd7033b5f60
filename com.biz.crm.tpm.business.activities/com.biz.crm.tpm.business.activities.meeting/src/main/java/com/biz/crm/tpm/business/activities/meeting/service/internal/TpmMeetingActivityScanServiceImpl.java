package com.biz.crm.tpm.business.activities.meeting.service.internal;

import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityScanEntity;
import com.biz.crm.tpm.business.activities.meeting.repository.TpmMeetingActivityScanRepository;
import com.biz.crm.tpm.business.activities.meeting.service.TpmMeetingActivityScanService;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityScanVo;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.LinkedHashSet;
import java.util.ArrayList;
import com.google.common.collect.Lists;

/**
 * tpm会议执行扫码数据;(tpm_meeting_activity_scan)表服务实现类
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Service("tpmMeetingActivityScanService")
public class TpmMeetingActivityScanServiceImpl implements TpmMeetingActivityScanService {
  @Autowired
  private TpmMeetingActivityScanRepository tpmMeetingActivityScanRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   * @return
   */
  @Override
  public Page<TpmMeetingActivityScanVo> findByConditions(Pageable pageable, TpmMeetingActivityScanVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmMeetingActivityScanVo();
    }
    return this.tpmMeetingActivityScanRepository.findByConditions(pageable, dto);
  }
  
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmMeetingActivityScanVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmMeetingActivityScanEntity tpmMeetingActivityScan = this.tpmMeetingActivityScanRepository.getById(id);
    if(tpmMeetingActivityScan == null){
        return null;
    }
    TpmMeetingActivityScanVo tpmMeetingActivityScanVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMeetingActivityScan, TpmMeetingActivityScanVo.class, LinkedHashSet.class, ArrayList.class);
    return tpmMeetingActivityScanVo;
  }
  
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmMeetingActivityScanVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmMeetingActivityScanEntity> tpmMeetingActivityScans = this.tpmMeetingActivityScanRepository.findByIds(ids);
    Collection<TpmMeetingActivityScanVo> tpmMeetingActivityScanVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmMeetingActivityScans, TpmMeetingActivityScanEntity.class, TpmMeetingActivityScanVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmMeetingActivityScanVos);
  }
  
  /**
   * 通过表单编号查询单条数据
   *
   * @param modelId 表单编号
   * @return 单条数据
   */
  @Override
  public List<TpmMeetingActivityScanVo> findByModelId(String modelId){
    if (StringUtils.isBlank(modelId)) {
      return Lists.newArrayList();
    }
    List<TpmMeetingActivityScanEntity> tpmMeetingActivityScans = this.tpmMeetingActivityScanRepository.findByModelId(modelId);
    Collection<TpmMeetingActivityScanVo> tpmMeetingActivityScanVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmMeetingActivityScans, TpmMeetingActivityScanEntity.class, TpmMeetingActivityScanVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmMeetingActivityScanVos);
  }
  
  /**
   * 新增数据
   *
   * @param tpmMeetingActivityScanVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmMeetingActivityScanVo create(TpmMeetingActivityScanVo tpmMeetingActivityScanVo) {
    
    this.createValidate(tpmMeetingActivityScanVo);
    TpmMeetingActivityScanEntity tpmMeetingActivityScan = this.nebulaToolkitService.copyObjectByWhiteList(tpmMeetingActivityScanVo, TpmMeetingActivityScanEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmMeetingActivityScanRepository.saveOrUpdate(tpmMeetingActivityScan);
    return tpmMeetingActivityScanVo;
  }
  
  /**
   * 批量新增
   * @param tpmMeetingActivityScanVos
   * @return
   */
  @Transactional
  @Override
  public List<TpmMeetingActivityScanVo> createBatch(Collection<TpmMeetingActivityScanVo> tpmMeetingActivityScanVos){
    if (CollectionUtils.isEmpty(tpmMeetingActivityScanVos)) {
      return Lists.newArrayList();
    }
    List<TpmMeetingActivityScanVo> results = Lists.newArrayList();
    for (TpmMeetingActivityScanVo TpmMeetingActivityScanVo : tpmMeetingActivityScanVos) {
      TpmMeetingActivityScanVo result = this.create(TpmMeetingActivityScanVo);
      results.add(result);
    }
    return results;
  }
  
  /**
   * 修改新据
   *
   * @param tpmMeetingActivityScanVo 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmMeetingActivityScanVo update(TpmMeetingActivityScanVo tpmMeetingActivityScanVo) {
    this.updateValidate(tpmMeetingActivityScanVo);
    TpmMeetingActivityScanEntity tpmMeetingActivityScan = this.tpmMeetingActivityScanRepository.getById(tpmMeetingActivityScanVo.getId());
    Validate.notNull(tpmMeetingActivityScan,"修改数据不存在，请检查！");
    // TODO 修改内容
    this.tpmMeetingActivityScanRepository.saveOrUpdate(tpmMeetingActivityScan);
    return tpmMeetingActivityScanVo;
  }
  
  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmMeetingActivityScanEntity> tpmMeetingActivityScans = this.tpmMeetingActivityScanRepository.findByIds(ids);
    if(CollectionUtils.isEmpty(tpmMeetingActivityScans)){
        return;
    }
   this.tpmMeetingActivityScanRepository.removeByIds(ids);
  }
  
  /**
   * 创建验证
   *
   * @param TpmMeetingActivityScanVo
   */
  private void createValidate(TpmMeetingActivityScanVo TpmMeetingActivityScanVo) {
    Validate.notNull(TpmMeetingActivityScanVo, "新增时，对象信息不能为空！");
    Validate.isTrue(TpmMeetingActivityScanVo.getId() == null,"新增数据时,数据主键不为空!");
    Validate.notBlank(TpmMeetingActivityScanVo.getActivityModelId(), "新增数据时，活动表单编码不能为空！");
    Validate.notBlank(TpmMeetingActivityScanVo.getScanCode(), "新增数据时，扫码编号不能为空！");
    Validate.notBlank(TpmMeetingActivityScanVo.getMaterialCode(), "新增数据时，物料编码不能为空！");
    Validate.notBlank(TpmMeetingActivityScanVo.getMaterialName(), "新增数据时，物料名称不能为空！");
  }
  
  /**
   * 修改验证
   *
   * @param TpmMeetingActivityScanVo
   */
  private void updateValidate(TpmMeetingActivityScanVo TpmMeetingActivityScanVo) {
    Validate.notNull(TpmMeetingActivityScanVo, "修改时，对象信息不能为空！");
    Validate.notBlank(TpmMeetingActivityScanVo.getId(), "修改数据时，主键不能为空！");
  }
}