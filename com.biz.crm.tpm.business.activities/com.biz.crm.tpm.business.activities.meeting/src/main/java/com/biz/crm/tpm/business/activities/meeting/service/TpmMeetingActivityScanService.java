package com.biz.crm.tpm.business.activities.meeting.service;

import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityScanVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collection;
import java.util.List;

/**
 * tpm会议执行扫码数据;(tpm_meeting_activity_scan)表服务接口
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
public interface TpmMeetingActivityScanService{
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<TpmMeetingActivityScanVo> findByConditions(Pageable pageable, TpmMeetingActivityScanVo dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  TpmMeetingActivityScanVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<TpmMeetingActivityScanVo> findByIds(Collection<String> ids);
  /**
   * 通过表单编号查询单条数据
   *
   * @param modelId 表单id
   * @return 单条数据
   */
  List<TpmMeetingActivityScanVo> findByModelId(String modelId);
  
  /**
   * 新增数据
   *
   * @param tpmMeetingActivityScanVo 实体对象
   * @return 新增结果
   */
  TpmMeetingActivityScanVo create(TpmMeetingActivityScanVo tpmMeetingActivityScanVo);
  /**
   * 批量新增
   * @param tpmMeetingActivityScanVos
   * @return
   */
  List<TpmMeetingActivityScanVo> createBatch(Collection<TpmMeetingActivityScanVo> tpmMeetingActivityScanVos);
  /**
   * 修改数据
   *
   * @param tpmMeetingActivityScanVo 实体对象
   * @return 修改结果
   */
  TpmMeetingActivityScanVo update(TpmMeetingActivityScanVo tpmMeetingActivityScanVo);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);
    
}