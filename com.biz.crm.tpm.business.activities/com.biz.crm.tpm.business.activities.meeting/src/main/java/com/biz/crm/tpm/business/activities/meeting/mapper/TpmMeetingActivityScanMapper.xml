<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.business.activities.meeting.mapper.TpmMeetingActivityScanMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityScanEntity" id="TpmMeetingActivityScanMap">
  </resultMap>
  
  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityScanVo">
    select 
    t.*
    from tpm_meeting_activity_scan t 
    <where>
        <if test="dto.activityModelId != null and dto.activityModelId != '' ">
          and t.activity_model_id = #{dto.activityModelId}
        </if>
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>