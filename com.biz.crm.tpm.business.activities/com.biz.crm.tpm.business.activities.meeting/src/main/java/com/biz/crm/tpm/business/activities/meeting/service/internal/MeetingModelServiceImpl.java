package com.biz.crm.tpm.business.activities.meeting.service.internal;
import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityModelEntity;
import com.biz.crm.tpm.business.activities.meeting.model.TpmMeetingActivityModel;
import com.biz.crm.tpm.business.activities.meeting.repository.TpmMeetingActivityModelRepository;
import com.biz.crm.tpm.business.activities.meeting.service.MeetingModelService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;

/**
 * 拜访总结model接口实现
 */
@Service
public class MeetingModelServiceImpl implements MeetingModelService {

  @Autowired
  private TpmMeetingActivityModelRepository tpmMeetingActivityModelRepository;

  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public TpmMeetingActivityModel findByParentCodeAndDynamicKey(String parentCode, String dynamicKey) {
    if (StringUtils.isAnyEmpty(parentCode, dynamicKey)) {
      return null;
    }
    TpmMeetingActivityModelEntity tpmQuotaActivityModelEntity = this.tpmMeetingActivityModelRepository.findByParentCodeAndDynamicKey(parentCode, dynamicKey);
    if (ObjectUtils.isEmpty(tpmQuotaActivityModelEntity)) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(tpmQuotaActivityModelEntity, TpmMeetingActivityModel.class, HashSet.class, ArrayList.class,"fileVos","scanVos");
  }
}
