package com.biz.crm.tpm.business.activities.meeting.service;

import com.biz.crm.tpm.business.activities.meeting.model.TpmMeetingActivityModel;

/**
 * tpm会议活动执行model接口
 */
public interface MeetingModelService {

  /**
   * 根据上级id和表单key查询会议活动model
   * @param parentCode 上级id
   * @param dynamicKey 表单key
   * @return 会议活动model
   */
  TpmMeetingActivityModel findByParentCodeAndDynamicKey(String parentCode, String dynamicKey);
}
