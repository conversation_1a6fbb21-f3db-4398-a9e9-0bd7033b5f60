package com.biz.crm.tpm.business.activities.meeting.service.internal;

import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityFileEntity;
import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityModelEntity;
import com.biz.crm.tpm.business.activities.meeting.repository.TpmMeetingActivityModelRepository;
import com.biz.crm.tpm.business.activities.meeting.service.TpmMeetingActivityFileService;
import com.biz.crm.tpm.business.activities.meeting.service.TpmMeetingActivityModelService;
import com.biz.crm.tpm.business.activities.meeting.service.TpmMeetingActivityScanService;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityFileVo;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityModelVo;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityScanVo;
import liquibase.pro.packaged.M;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.LinkedHashSet;
import java.util.ArrayList;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;

/**
 * tpm会议执行表单;(tpm_meeting_activity_model)表服务实现类
 *
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Service("tpmMeetingActivityModelService")
public class TpmMeetingActivityModelServiceImpl implements TpmMeetingActivityModelService {
  @Autowired
  private TpmMeetingActivityModelRepository tpmMeetingActivityModelRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Autowired
  private TpmMeetingActivityFileService tpmMeetingActivityFileService;

  @Autowired
  private TpmMeetingActivityScanService tpmMeetingActivityScanService;


  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<TpmMeetingActivityModelVo> findByConditions(Pageable pageable, TpmMeetingActivityModelVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmMeetingActivityModelVo();
    }
    return this.tpmMeetingActivityModelRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmMeetingActivityModelVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmMeetingActivityModelEntity tpmMeetingActivityModel = this.tpmMeetingActivityModelRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (tpmMeetingActivityModel == null) {
      return null;
    }
    TpmMeetingActivityModelVo tpmMeetingActivityModelVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMeetingActivityModel, TpmMeetingActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    //获取关联文件数据
    tpmMeetingActivityModelVo.setFileVos(this.tpmMeetingActivityFileService.findByModelId(tpmMeetingActivityModel.getId()));
    //获取关联扫码数据
    tpmMeetingActivityModelVo.setScanVos(this.tpmMeetingActivityScanService.findByModelId(tpmMeetingActivityModel.getId()));
    return tpmMeetingActivityModelVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmMeetingActivityModelVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmMeetingActivityModelEntity> tpmMeetingActivityModels = this.tpmMeetingActivityModelRepository.findByIds(ids);
    Collection<TpmMeetingActivityModelVo> tpmMeetingActivityModelVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmMeetingActivityModels, TpmMeetingActivityModelEntity.class, TpmMeetingActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmMeetingActivityModelVos);
  }

  /**
   * 通过比编号查询单条数据
   *
   * @param parentCode 编号
   * @return 单条数据
   */
  @Override
  public TpmMeetingActivityModelVo findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    TpmMeetingActivityModelEntity tpmMeetingActivityModel = this.tpmMeetingActivityModelRepository.findByParentCodeAndDynamicKey(parentCode, null);
    if (tpmMeetingActivityModel == null) {
      return null;
    }
    TpmMeetingActivityModelVo tpmMeetingActivityModelVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMeetingActivityModel, TpmMeetingActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    //获取关联文件数据
    tpmMeetingActivityModelVo.setFileVos(this.tpmMeetingActivityFileService.findByModelId(tpmMeetingActivityModel.getId()));
    //获取关联扫码数据
    tpmMeetingActivityModelVo.setScanVos(this.tpmMeetingActivityScanService.findByModelId(tpmMeetingActivityModel.getId()));
    return tpmMeetingActivityModelVo;
  }

  /**
   * 新增数据
   *
   * @param tpmMeetingActivityModelVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmMeetingActivityModelVo create(TpmMeetingActivityModelVo tpmMeetingActivityModelVo) {
    this.createValidate(tpmMeetingActivityModelVo);
    TpmMeetingActivityModelEntity tpmMeetingActivityModel = this.nebulaToolkitService.copyObjectByWhiteList(tpmMeetingActivityModelVo, TpmMeetingActivityModelEntity.class, LinkedHashSet.class, ArrayList.class);
    tpmMeetingActivityModel.setTenantCode(TenantUtils.getTenantCode());
    this.tpmMeetingActivityModelRepository.saveOrUpdate(tpmMeetingActivityModel);
    //保存关联文件
    if (!CollectionUtils.isEmpty(tpmMeetingActivityModelVo.getFileVos())) {
      List<TpmMeetingActivityFileVo> fileVos = tpmMeetingActivityModelVo.getFileVos();
      fileVos.forEach(file -> file.setActivityModelId(tpmMeetingActivityModel.getId()));
      this.tpmMeetingActivityFileService.createBatch(fileVos);
    }
    //保存关联扫码数据
    if (!CollectionUtils.isEmpty(tpmMeetingActivityModelVo.getScanVos())) {
      List<TpmMeetingActivityScanVo> scanVos = tpmMeetingActivityModelVo.getScanVos();
      scanVos.forEach(file -> file.setActivityModelId(tpmMeetingActivityModel.getId()));
      this.tpmMeetingActivityScanService.createBatch(scanVos);
    }
    return tpmMeetingActivityModelVo;
  }

  /**
   * 批量新增
   *
   * @param tpmMeetingActivityModelVos
   * @return
   */
  @Transactional
  @Override
  public List<TpmMeetingActivityModelVo> createBatch(Collection<TpmMeetingActivityModelVo> tpmMeetingActivityModelVos) {
    if (CollectionUtils.isEmpty(tpmMeetingActivityModelVos)) {
      return Lists.newArrayList();
    }
    List<TpmMeetingActivityModelVo> results = Lists.newArrayList();
    for (TpmMeetingActivityModelVo tpmMeetingActivityModelVo : tpmMeetingActivityModelVos) {
      TpmMeetingActivityModelVo result = this.create(tpmMeetingActivityModelVo);
      results.add(tpmMeetingActivityModelVo);
    }
    return results;
  }

  /**
   * 修改新据
   *
   * @param tpmMeetingActivityModelVo 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmMeetingActivityModelVo update(TpmMeetingActivityModelVo tpmMeetingActivityModelVo) {
    this.updateValidate(tpmMeetingActivityModelVo);
    TpmMeetingActivityModelEntity tpmMeetingActivityModel = this.tpmMeetingActivityModelRepository.findByIdAndTenantCode(tpmMeetingActivityModelVo.getId(),TenantUtils.getTenantCode());
    Validate.notNull(tpmMeetingActivityModel, "修改数据不存在，请检查！");
    tpmMeetingActivityModel.setTenantCode(TenantUtils.getTenantCode());
    this.tpmMeetingActivityModelRepository.saveOrUpdate(tpmMeetingActivityModel);
    TpmMeetingActivityModelVo updateVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMeetingActivityModel, TpmMeetingActivityModelVo.class, LinkedHashSet.class, ArrayList.class);
    return updateVo;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmMeetingActivityModelEntity> tpmMeetingActivityModels = this.tpmMeetingActivityModelRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(tpmMeetingActivityModels)) {
      return;
    }
    this.tpmMeetingActivityModelRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  /**
   * 创建验证
   *
   * @param tpmMeetingActivityModelVo
   */
  private void createValidate(TpmMeetingActivityModelVo tpmMeetingActivityModelVo) {
    Validate.notNull(tpmMeetingActivityModelVo, "新增时，对象信息不能为空！");
    Validate.isTrue(tpmMeetingActivityModelVo.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(tpmMeetingActivityModelVo.getActivityCode(), "新增数据时，活动编码不能为空！");
    Validate.notBlank(tpmMeetingActivityModelVo.getActivityName(), "新增数据时，活动名称不能为空！");
    if (!CollectionUtils.isEmpty(tpmMeetingActivityModelVo.getFileVos())) {
      Map<String, List<TpmMeetingActivityFileVo>> map = tpmMeetingActivityModelVo.getFileVos().stream().collect(Collectors.groupingBy(TpmMeetingActivityFileVo::getActivityFileType));
      Validate.isTrue(!(map.containsKey("0")&&map.get("0").size()>6),"照片数量不能大于5！");
      Validate.isTrue(!(map.containsKey("1")&&map.get("1").size()>6),"视频数量不能大于5！");
    }
  }

  /**
   * 修改验证
   *
   * @param tpmMeetingActivityModelVo
   */
  private void updateValidate(TpmMeetingActivityModelVo tpmMeetingActivityModelVo) {
    Validate.notNull(tpmMeetingActivityModelVo, "修改时，对象信息不能为空！");
    Validate.notBlank(tpmMeetingActivityModelVo.getId(), "修改数据时，主键不能为空！");
  }

}