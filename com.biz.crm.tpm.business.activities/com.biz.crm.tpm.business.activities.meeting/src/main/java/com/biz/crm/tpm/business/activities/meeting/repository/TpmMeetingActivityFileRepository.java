package com.biz.crm.tpm.business.activities.meeting.repository;

import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityFileEntity;
import com.biz.crm.tpm.business.activities.meeting.mapper.TpmMeetingActivityFileMapper;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityFileVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.util.Collection;
import java.util.List;
import java.util.Collections;

/**
 * tpm会议执行文件;(tpm_meeting_activity_file)表数据库访问层
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Component
public class TpmMeetingActivityFileRepository extends ServiceImpl<TpmMeetingActivityFileMapper, TpmMeetingActivityFileEntity>{
  @Autowired
  private TpmMeetingActivityFileMapper tpmMeetingActivityFileMapper;
  
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  public Page<TpmMeetingActivityFileVo> findByConditions(Pageable pageable, TpmMeetingActivityFileVo dto) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    Page<TpmMeetingActivityFileVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return tpmMeetingActivityFileMapper.findByConditions(page, dto);
  }
  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<TpmMeetingActivityFile>
   */
  public List<TpmMeetingActivityFileEntity> findByIds(Collection<String> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Collections.emptyList();
    }
    return this.lambdaQuery()
        .in(TpmMeetingActivityFileEntity::getId, ids)
        .list();
  }

  /**
   * 根据表单id查询关联文件列表
   *
   * @param modelId 表单id
   * @return List<TpmMeetingActivityFile>
   */
  public List<TpmMeetingActivityFileEntity> findByModelId(String modelId) {
    if(StringUtils.isBlank(modelId)){
      return Collections.emptyList();
    }
    return this.lambdaQuery()
            .in(TpmMeetingActivityFileEntity::getActivityModelId, modelId)
            .list();
  }
  
}