package com.biz.crm.tpm.business.activities.meeting.entity;

import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：tpm会议执行表单;
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@ApiModel(value = "MeetingActivityModel",description = "tpm会议执行表单")
@TableName("tpm_meeting_activity_model")
@Getter
@Setter
@Entity(name = "tpm_meeting_activity_model")
@org.hibernate.annotations.Table(appliesTo = "tpm_meeting_activity_model", comment = "tpm会议执行表单")
@Table(name = "tpm_meeting_activity_model")
public class TpmMeetingActivityModelEntity  extends TenantFlagOpEntity {

  /** 执行计划业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "执行业务编码", value= "执行业务编码")
  @Column(name = "parent_code", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '执行计划业务编码 '")
  private String parentCode;

  /** 步骤业务编码stepCode */
  @ApiModelProperty(name = "dynamicKey",notes = "步骤业务编码", value= "步骤业务编码")
  @Column(name = "dynamic_key", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '步骤业务编码stepCode '")
  private String dynamicKey;
  
  /** 活动编码 */
  @ApiModelProperty(name = "activityCode",notes = "活动编码", value= "活动编码")
  @Column(name = "activity_code", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动编码 '")
  private String activityCode;
  
  /** 活动名称 */
  @ApiModelProperty(name = "activityName",notes = "活动名称", value= "活动名称")
  @Column(name = "activity_name", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动名称 '")
  private String activityName;
  
  /** 活动申请时间 */
  @ApiModelProperty(name = "startTime",notes = "活动申请时间", value= "活动申请时间")
  @Column(name = "start_time", nullable = false,  columnDefinition = "DATETIME COMMENT '活动申请时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;
  
  /** 执行说明 */
  @ApiModelProperty(name = "executionRemark",notes = "执行说明", value= "执行说明")
  @Column(name = "execution_remark", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '执行说明 '")
  private String executionRemark;

}