<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.business.activities.meeting.mapper.TpmMeetingActivityModelMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityModelEntity" id="TpmMeetingActivityModelMap">
  </resultMap>
  
  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityModelVo">
    select 
    t.*
    from tpm_meeting_activity_model t 
    <where>
        t.tenant_code=#{dto.tenantCode}
        <if test="dto.activityCode != null and dto.activityCode != '' ">
          and t.activity_code = #{dto.activityCode}
        </if>
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>