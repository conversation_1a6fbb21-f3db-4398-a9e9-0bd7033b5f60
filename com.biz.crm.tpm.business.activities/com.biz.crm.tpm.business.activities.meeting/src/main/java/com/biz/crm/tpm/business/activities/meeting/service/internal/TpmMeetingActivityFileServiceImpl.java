package com.biz.crm.tpm.business.activities.meeting.service.internal;

import com.biz.crm.tpm.business.activities.meeting.entity.TpmMeetingActivityFileEntity;
import com.biz.crm.tpm.business.activities.meeting.repository.TpmMeetingActivityFileRepository;
import com.biz.crm.tpm.business.activities.meeting.service.TpmMeetingActivityFileService;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityFileVo;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.LinkedHashSet;
import java.util.ArrayList;
import com.google.common.collect.Lists;

/**
 * tpm会议执行文件;(tpm_meeting_activity_file)表服务实现类
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Service("tpmMeetingActivityFileService")
public class TpmMeetingActivityFileServiceImpl implements TpmMeetingActivityFileService {
  @Autowired
  private TpmMeetingActivityFileRepository tpmMeetingActivityFileRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   * @return
   */
  @Override
  public Page<TpmMeetingActivityFileVo> findByConditions(Pageable pageable, TpmMeetingActivityFileVo dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new TpmMeetingActivityFileVo();
    }
    return this.tpmMeetingActivityFileRepository.findByConditions(pageable, dto);
  }
  
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public TpmMeetingActivityFileVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    TpmMeetingActivityFileEntity tpmMeetingActivityFile = this.tpmMeetingActivityFileRepository.getById(id);
    if(tpmMeetingActivityFile == null){
        return null;
    }
    TpmMeetingActivityFileVo tpmMeetingActivityFileVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMeetingActivityFile, TpmMeetingActivityFileVo.class, LinkedHashSet.class, ArrayList.class);
    return tpmMeetingActivityFileVo;
  }
  
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<TpmMeetingActivityFileVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<TpmMeetingActivityFileEntity> tpmMeetingActivityFiles = this.tpmMeetingActivityFileRepository.findByIds(ids);
    Collection<TpmMeetingActivityFileVo> tpmMeetingActivityFileVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmMeetingActivityFiles, TpmMeetingActivityFileEntity.class, TpmMeetingActivityFileVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmMeetingActivityFileVos);
  }
  
  /**
   * 通过比编号查询单条数据
   *
   * @param modelId 编号
   * @return 单条数据
   */
  @Override
  public List<TpmMeetingActivityFileVo> findByModelId(String modelId){
    if (StringUtils.isBlank(modelId)) {
      return Lists.newArrayList();
    }
    List<TpmMeetingActivityFileEntity> tpmMeetingActivityFiles = this.tpmMeetingActivityFileRepository.findByModelId(modelId);
    Collection<TpmMeetingActivityFileVo> tpmMeetingActivityFileVos = this.nebulaToolkitService.copyCollectionByWhiteList(tpmMeetingActivityFiles, TpmMeetingActivityFileEntity.class, TpmMeetingActivityFileVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(tpmMeetingActivityFileVos);
  }
  
  /**
   * 新增数据
   *
   * @param TpmMeetingActivityFileVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public TpmMeetingActivityFileVo create(TpmMeetingActivityFileVo TpmMeetingActivityFileVo) {
    
    this.createValidate(TpmMeetingActivityFileVo);
    TpmMeetingActivityFileEntity tpmMeetingActivityFile = this.nebulaToolkitService.copyObjectByWhiteList(TpmMeetingActivityFileVo, TpmMeetingActivityFileEntity.class, LinkedHashSet.class, ArrayList.class);
    this.tpmMeetingActivityFileRepository.saveOrUpdate(tpmMeetingActivityFile);
    TpmMeetingActivityFileVo tpmMeetingActivityFileVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMeetingActivityFile, TpmMeetingActivityFileVo.class, LinkedHashSet.class, ArrayList.class);
    tpmMeetingActivityFileVo.setId(tpmMeetingActivityFile.getId());
    return tpmMeetingActivityFileVo;
  }
  
  /**
   * 批量新增
   * @param tpmMeetingActivityFileVos
   * @return
   */
  @Transactional
  @Override
  public List<TpmMeetingActivityFileVo> createBatch(Collection<TpmMeetingActivityFileVo> tpmMeetingActivityFileVos){
    if (CollectionUtils.isEmpty(tpmMeetingActivityFileVos)) {
      return Lists.newArrayList();
    }
    List<TpmMeetingActivityFileVo> results = Lists.newArrayList();
    for (TpmMeetingActivityFileVo TpmMeetingActivityFileVo : tpmMeetingActivityFileVos) {
      TpmMeetingActivityFileVo result = this.create(TpmMeetingActivityFileVo);
      results.add(result);
    }
    return results;
  }
  
  /**
   * 修改新据
   *
   * @param tpmMeetingActivityFileVo 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public TpmMeetingActivityFileVo update(TpmMeetingActivityFileVo tpmMeetingActivityFileVo) {
    this.updateValidate(tpmMeetingActivityFileVo);
    TpmMeetingActivityFileEntity tpmMeetingActivityFile = this.tpmMeetingActivityFileRepository.getById(tpmMeetingActivityFileVo.getId());
    Validate.notNull(tpmMeetingActivityFile,"修改数据不存在，请检查！");
    TpmMeetingActivityFileVo oldTpmMeetingActivityFileVo = this.nebulaToolkitService.copyObjectByWhiteList(tpmMeetingActivityFile,TpmMeetingActivityFileVo.class, LinkedHashSet.class, ArrayList.class);
    // TODO 修改内容
    this.tpmMeetingActivityFileRepository.saveOrUpdate(tpmMeetingActivityFile);
   return tpmMeetingActivityFileVo;
  }
  
  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<TpmMeetingActivityFileEntity> tpmMeetingActivityFiles = this.tpmMeetingActivityFileRepository.findByIds(ids);
    if(CollectionUtils.isEmpty(tpmMeetingActivityFiles)){
        return;
    }
    this.tpmMeetingActivityFileRepository.removeByIds(ids);
  }

  /**
   * 创建验证
   *
   * @param TpmMeetingActivityFileVo
   */
  private void createValidate(TpmMeetingActivityFileVo TpmMeetingActivityFileVo) {
    Validate.notNull(TpmMeetingActivityFileVo, "新增时，对象信息不能为空！");
    Validate.isTrue(TpmMeetingActivityFileVo.getId() == null,"新增数据时,数据主键不为空!");
    Validate.notBlank(TpmMeetingActivityFileVo.getActivityModelId(), "新增数据时，活动表单编码不能为空！");
    Validate.notBlank(TpmMeetingActivityFileVo.getActivityFileUrl(), "新增数据时，活动文件地址不能为空！");
    Validate.notBlank(TpmMeetingActivityFileVo.getActivityFileRemark(), "新增数据时，活动文件说明不能为空！");
    Validate.notBlank(TpmMeetingActivityFileVo.getActivityFileType(), "新增数据时，活动文件类别(0-照片/1-视频)不能为空！");
  }
  
  /**
   * 修改验证
   *
   * @param TpmMeetingActivityFileVo
   */
  private void updateValidate(TpmMeetingActivityFileVo TpmMeetingActivityFileVo) {
    Validate.notNull(TpmMeetingActivityFileVo, "修改时，对象信息不能为空！");
    Validate.notBlank(TpmMeetingActivityFileVo.getId(), "修改数据时，主键不能为空！");
  }
  
}