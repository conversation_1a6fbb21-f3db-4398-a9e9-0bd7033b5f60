package com.biz.crm.tpm.business.activities.meeting.controller;

import com.biz.crm.tpm.business.activities.meeting.service.TpmMeetingActivityFileService;
import com.biz.crm.tpm.business.activities.meeting.vo.TpmMeetingActivityFileVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.biz.crm.business.common.sdk.model.Result;

import java.util.List;

/**
 * tpm会议执行文件;(tpm_meeting_activity_file)控制层
 * <AUTHOR> jerry7
 * @date : 2022-11-9
 */
@Api(tags = "tpm会议执行文件功能接口")
@RestController
@RequestMapping("/v1/tpmMeetingActivityFile")
@Slf4j
public class TpmMeetingActivityFileController{
  /**
   * 服务对象
   */
  @Autowired
  private TpmMeetingActivityFileService tpmMeetingActivityFileService;
  
  /**
   * 分页查询所有数据
   *
   * @param pageable        分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<TpmMeetingActivityFileVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                 @ApiParam(name = "tpmMeetingActivityFile", value = "核销采集信息") TpmMeetingActivityFileVo dto) {
    try {
      Page<TpmMeetingActivityFileVo> page = this.tpmMeetingActivityFileService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<TpmMeetingActivityFileVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id", required=true) String id) {
    try {
      TpmMeetingActivityFileVo tpmMeetingActivityFileVo = this.tpmMeetingActivityFileService.findById(id);
      return Result.ok(tpmMeetingActivityFileVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 新增数据
   *
   * @param TpmMeetingActivityFileVo 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<TpmMeetingActivityFileVo> create(@ApiParam(name = "TpmMeetingActivityFileVo", value = "tpm会议执行文件") @RequestBody TpmMeetingActivityFileVo TpmMeetingActivityFileVo) {
    try {
      TpmMeetingActivityFileVo result = this.tpmMeetingActivityFileService.create(TpmMeetingActivityFileVo);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 修改数据
   *
   * @param TpmMeetingActivityFileVo 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<TpmMeetingActivityFileVo> update(@ApiParam(name = "TpmMeetingActivityFileVo", value = "tpm会议执行文件") @RequestBody TpmMeetingActivityFileVo TpmMeetingActivityFileVo) {
    try {
      TpmMeetingActivityFileVo result = this.tpmMeetingActivityFileService.update(TpmMeetingActivityFileVo);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation(value = "删除数据")
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    try {
      this.tpmMeetingActivityFileService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
}