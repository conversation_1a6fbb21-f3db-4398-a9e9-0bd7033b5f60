<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.biz.crm.tpm</groupId>
    <artifactId>tpm</artifactId>
    <version>202405</version>
  </parent>


  <name>activities</name>
  <modules>
    <module>com.biz.crm.tpm.business.activities.sdk</module>
    <module>com.biz.crm.tpm.business.activities.local</module>
    <module>com.biz.crm.tpm.business.activities.local.starter</module>
    <module>com.biz.crm.tpm.business.activities.ordinary</module>
    <module>com.biz.crm.tpm.business.activities.project</module>
    <module>com.biz.crm.tpm.business.activities.scheme</module>
    <module>com.biz.crm.tpm.business.activities.dynamic.template</module>
    <module>com.biz.crm.tpm.business.activities.quota</module>
    <module>com.biz.crm.tpm.business.activities.fields</module>
    <module>com.biz.crm.tpm.business.activities.feign</module>
    <module>com.biz.crm.tpm.business.activities.feign.starter</module>
    <module>com.biz.crm.tpm.business.activities.meeting</module>
    <module>com.biz.crm.tpm.business.activities.market</module>
    <module>com.biz.crm.tpm.business.activities.simple.audit</module>
  </modules>

  <dependencies>
    <dependency>
      <groupId>com.biz.crm.tpm.business.budget</groupId>
      <artifactId>budget-sdk</artifactId>
      <version>${business.version}</version>
    </dependency>
    <!--CRM common -->
    <dependency>
      <groupId>com.biz.crm</groupId>
      <artifactId>crm-business-common</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.common.form</groupId>
      <artifactId>form-local</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
    <!--MDM -->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>customer-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>material-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>org-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>terminal-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>product-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>product-level-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>product-spu-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>inquiry-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <!-- 行政区域   -->
    <dependency>
      <groupId>com.biz.crm.mdm.business</groupId>
      <artifactId>region-feign-starter</artifactId>
      <version>${crm.mdm.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.tpm.business</groupId>
      <artifactId>rebate-sdk</artifactId>
      <version>${business.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>allow-sale-feign-starter</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <!--     rocketmq start -->
    <dependency>
      <groupId>com.biz.crm.business.common</groupId>
      <artifactId>crm-common-rocketmq</artifactId>
    </dependency>
    <!-- rocketmq end -->
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>policy-feign-starter</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.dms.business</groupId>
      <artifactId>costpool-replenishment-feign-starter</artifactId>
      <version>${crm.dms.version}</version>
    </dependency>
  </dependencies>

  <groupId>com.biz.crm.tpm.business.activities</groupId>
  <artifactId>activities</artifactId>
  <packaging>pom</packaging>
</project>
