package com.biz.crm.tpm.business.activities.sdk.template;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString
@NoArgsConstructor
@MappedSuperclass
public abstract class BaseActivity extends TenantFlagOpEntity {

  private static final long serialVersionUID = 6723124993231575366L;
  @ApiModelProperty("活动编码")
  @TableField(value = "code")
  @Column(name = "code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动编码'")
  private String code;

  @ApiModelProperty("活动名称")
  @TableField(value = "name")
  @Column(name = "name", nullable = false, columnDefinition = "varchar(255) COMMENT '活动名称'")
  private String name;

  @ApiModelProperty("活动开始时间")
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "start_time", length = 20, columnDefinition = "datetime COMMENT '开始时间 '")
  private Date startTime;

  @ApiModelProperty("活动结束时间")
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "end_time", length = 20, columnDefinition = "datetime COMMENT '活动结束时间 '")
  private Date endTime;

  @ApiModelProperty("费用预算编码")
  @TableField(value = "cost_budget_code")
  @Column(name = "cost_budget_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '费用预算编码'")
  private String costBudgetCode;

  @ApiModelProperty("预算科目编码")
  @TableField(value = "budget_subject_code")
  @Column(name = "budget_subject_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '预算科目编码'")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  @TableField(value = "budget_subject_name")
  @Column(name = "budget_subject_name", nullable = false, columnDefinition = "varchar(64) COMMENT '预算科目名称'")
  private String budgetSubjectName;

  @ApiModelProperty("活动大类编码")
  @TableField(value = "cost_type_category_code")
  @Column(name = "cost_type_category_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动大类编码'")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  @TableField(value = "cost_type_category_name")
  @Column(name = "cost_type_category_name", nullable = false, columnDefinition = "varchar(64) COMMENT '活动大类名称'")
  private String costTypeCategoryName;


  @ApiModelProperty("总申请金额")
  @TableField(value = "total_apply_amount")
  @Column(name = "total_apply_amount", nullable = false, columnDefinition = "decimal(20,4) COMMENT '总申请金额'")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("活动状态")
  @TableField(value = "status")
  @Column(name = "status", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动状态'")
  private String status;
}
