package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "BaseActivityItemVo", description = "基础活动动态模板vo")
public abstract class BaseActivityItemVo extends TenantOpVo {
  @ApiModelProperty("对应的活动编号")
  private String parentCode;

  @ApiModelProperty("活动明细items对应的Map key键值")
  private String dynamicKey;

  @ApiModelProperty("活动明细对应的动态模板键值")
  private String dynamicFormCode;

  @ApiModelProperty("可能的前端标签页")
  private String label;

  @ApiModelProperty("活动明细编码")
  private String itemCode;

  @ApiModelProperty("分摊标识键值")
  private String shareKey;
}
