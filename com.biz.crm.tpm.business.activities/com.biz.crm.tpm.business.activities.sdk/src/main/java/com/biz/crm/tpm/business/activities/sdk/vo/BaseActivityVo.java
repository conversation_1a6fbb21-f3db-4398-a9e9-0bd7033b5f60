package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "BaseActivityVo", description = "基础活动主模块vo")
public abstract class BaseActivityVo extends UuidFlagOpVo {

  private static final long serialVersionUID = -6641405641488717993L;
  @ApiModelProperty("活动编码")
  private String code;

  @ApiModelProperty("活动名称")
  private String name;

  @ApiModelProperty("活动开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  @ApiModelProperty("活动结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty("费用预算编码")
  private String costBudgetCode;

  @ApiModelProperty("费用预算名称")
  private String costBudgetName;

  @ApiModelProperty("预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("活动大类编码")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  private String costTypeCategoryName;

  @ApiModelProperty("活动状态")
  private String status;

  @ApiModelProperty("审批单号")
  private String processNumber;

  @ApiModelProperty("推送日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;
}
