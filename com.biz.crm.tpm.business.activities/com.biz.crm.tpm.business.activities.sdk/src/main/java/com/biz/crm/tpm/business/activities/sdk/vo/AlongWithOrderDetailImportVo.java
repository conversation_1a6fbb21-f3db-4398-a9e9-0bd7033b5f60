package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 随单比例缓存导入vo
 */
@Data
@CrmExcelImport(startRow = 4)
public class AlongWithOrderDetailImportVo extends CrmExcelVo {

    @CrmExcelColumn("部门编码")
    private String departmentCode;

    @CrmExcelColumn("部门名称")
    private String departmentName;
    private Integer levelNum;

    @CrmExcelColumn("客户编码")
    private String customerCode;

    @CrmExcelColumn("客户名称")
    private String customerName;

    @CrmExcelColumn("产品编码")
    private String productCode;

    @CrmExcelColumn("产品名称")
    private String productName;

    @CrmExcelColumn("比例")
    private String ratioStr;
    private BigDecimal ratio;
}
