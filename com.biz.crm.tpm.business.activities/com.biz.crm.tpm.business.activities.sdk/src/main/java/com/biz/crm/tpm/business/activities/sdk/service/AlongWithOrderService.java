package com.biz.crm.tpm.business.activities.sdk.service;

import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDto;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 随单比例管控
 */
public interface AlongWithOrderService {

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    void create(AlongWithOrderDto dto, String cacheKey);

    /**
     * 修改数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    void update(AlongWithOrderDto dto, String cacheKey);

    /**
     * 保存
     *
     * @param dto
     */
    void store(AlongWithOrderDto dto);

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    void delete(List<String> idList);

    /**
     * 启用
     *
     * @param idList
     */
    void enable(List<String> idList);

    /**
     * 禁用
     *
     * @param idList
     */
    void disable(List<String> idList);

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    AlongWithOrderVo findByCode(String code);

    /**
     * 获取所有缓存
     *
     * @param cacheKey
     * @return
     */
    List<AlongWithOrderDetailDto> findAllCacheList(String cacheKey);

    BigDecimal getRatioByCondition(String departmentCode, String customerCode,
                                   List<String> productCodes, String years);
}
