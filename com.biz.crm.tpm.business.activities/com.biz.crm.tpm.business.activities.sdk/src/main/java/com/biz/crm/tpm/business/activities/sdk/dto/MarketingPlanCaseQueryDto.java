package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 15:10
 */
@Data
@ApiModel("MarketingPlanCaseQueryDto")
public class MarketingPlanCaseQueryDto extends TenantFlagOpVo {

    @ApiModelProperty("方案明细类型")
    private String caseType;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("原方案明细编码")
    private String originalSchemeDetailCode;

    @ApiModelProperty("关联统筹方案编码")
    private String releaseCode;

    @ApiModelProperty("关联统筹方案名称")
    private String releaseName;

    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("小于开始时间")
    private String lessStartDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("指定时间-月份")
    private String startEndDate;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("年")
    private String year;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("费用归属-成本中心编码")
    private String belongCostCenterCode;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("终端编码")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("兑付方式")
    private String cashType;

    @ApiModelProperty("结案金额")
    private BigDecimal auditAmount;

    @ApiModelProperty("结案状态")
    private String auditStatus;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;

    @ApiModelProperty("兑付状态")
    private String cashStatus;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("公式编码")
    private String conditionFormula;

    @ApiModelProperty("方案编码列表")
    private List<String> schemeCodeList;

    @ApiModelProperty("方案明细编码列表")
    private List<String> schemeDetailCodeList;

    @ApiModelProperty("关联统筹方案编码")
    private List<String> releaseDetailCodeList;

    @ApiModelProperty("活动大类编码集合")
    private List<String> categoryCodeList;

    @ApiModelProperty("活动细类编码集合")
    private List<String> detailCodeList;

    @ApiModelProperty("不包含方案明细列表")
    private Set<String> excludeSchemeDetailCodes;

    @ApiModelProperty("采集执行状态 Y,N")
    private String executeStatus;


    /**
     * 执行类型
     */
    @ApiModelProperty("执行类型")
    private String executionType;

    @ApiModelProperty("活动执行编码")
    private String actExecuteCode;

    /**
     * 终端/客户编码
     */
    @ApiModelProperty("终端/客户编码")
    private String clientCode;
    private List<String> clientCodeList;


    /**
     * 职位编码
     */
    @ApiModelProperty("职位编码")
    private String positionCode;


    /**
     * 执行时间 yyyy-mm-dd
     */
    @ApiModelProperty("执行时间")
    private String executionTime;

    private String companyCode;
}
