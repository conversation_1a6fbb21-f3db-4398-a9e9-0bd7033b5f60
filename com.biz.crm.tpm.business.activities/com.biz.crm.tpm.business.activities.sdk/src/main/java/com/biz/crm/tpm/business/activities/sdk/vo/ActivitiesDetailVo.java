package com.biz.crm.tpm.business.activities.sdk.vo;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Vo：活动明细信息表;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-28
 */
@ApiModel(value = "ActivitiesDetail", description = "活动明细信息表")
@Getter
@Setter
public class ActivitiesDetailVo implements Serializable {
  /**
   * 主键
   */
  @ApiModelProperty(name = "id", notes = "主键", value = "主键")
  private String id;
  /**
   * 租户编号
   */
  @ApiModelProperty(name = "tenantCode", notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /**
   * 创建人账号
   */
  @ApiModelProperty(name = "createAccount", notes = "创建人账号", value = "创建人账号")
  private String createAccount;
  /**
   * 创建人名称
   */
  @ApiModelProperty(name = "createName", notes = "创建人名称", value = "创建人名称")
  private String createName;
  /**
   * 创建时间
   */
  @ApiModelProperty(name = "createTime", notes = "创建时间", value = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesCode", notes = "活动编号", value = "活动编号")
  private String activitiesCode;
  /**
   * 活动名称
   */
  @ApiModelProperty(name = "activitiesName", notes = "活动名称", value = "活动名称")
  private String activitiesName;
  /**
   * 活动明细编号
   */
  @ApiModelProperty(name = "activitiesDetailCode", notes = "活动明细编号时间", value = "活动明细编号时间")
  private String activitiesDetailCode;
  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "costTypeCategoryName", notes = "活动大类名称", value = "活动大类名称")
  private String costTypeCategoryName;
  /**
   * 活动大类编号
   */
  @ApiModelProperty(name = "costTypeCategoryCode", notes = "活动大类编号", value = "活动大类编号")
  private String costTypeCategoryCode;
  /**
   * 费用预算编码
   */
  @ApiModelProperty(name = "costBudgetCode", notes = "费用预算编码", value = "费用预算编码")
  private String costBudgetCode;
  /**
   * 活动细类编号
   */
  @ApiModelProperty(name = "costTypeDetailCode", notes = "活动细类编号", value = "活动细类编号")
  private String costTypeDetailCode;
  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "costTypeDetailName", notes = "活动细类名称", value = "活动细类名称")
  private String costTypeDetailName;
  /**
   * 组织编码
   */
  @ApiModelProperty(name = "orgCode", notes = "组织编码", value = "组织编码")
  private String orgCode;
  /**
   * 组织名称
   */
  @ApiModelProperty(name = "orgName", notes = "组织名称", value = "组织名称")
  private String orgName;
  /**
   * 客户编号
   */
  @ApiModelProperty(name = "customerCode", notes = "客户编号", value = "客户编号")
  private String customerCode;
  /**
   * 客户名称
   */
  @ApiModelProperty(name = "customerName", notes = "客户名称", value = "客户名称")
  private String customerName;
  /**
   * 终端编号
   */
  @ApiModelProperty(name = "terminalCode", notes = "终端编号", value = "终端编号")
  private String terminalCode;
  /**
   * 终端名称
   */
  @ApiModelProperty(name = "terminalName", notes = "终端名称", value = "终端名称")
  private String terminalName;
  /**
   * 申请金额
   */
  @ApiModelProperty(name = "applyAmount", notes = "申请金额", value = "申请金额")
  private BigDecimal applyAmount;
  /**
   * 支付方式
   */
  @ApiModelProperty(name = "payType", notes = "支付方式", value = "支付方式")
  private String payType;
  /**
   * 支付方式名称
   */
  @ApiModelProperty(name = "payTypeName", notes = "支付方式名称", value = "支付方式名称")
  private String payTypeName;
  /**
   * 预算科目编码
   */
  @ApiModelProperty(name = "budgetSubjectsCode", notes = "预算科目编码", value = "预算科目编码")
  private String budgetSubjectsCode;
  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "budgetSubjectsName", notes = "预算科目名称", value = "预算科目名称")
  private String budgetSubjectsName;
  /**
   * 费用日期(年月)
   */
  @ApiModelProperty(name = "feeDate", notes = "费用日期(年月)", value = "费用日期(年月)")
  private String feeDate;
  /**
   * 备注
   */
  @ApiModelProperty(name = "remark", notes = "备注", value = "备注")
  private String remark;
  /**
   * 是否关闭
   */
  @ApiModelProperty(name = "isClose", notes = "是否关闭", value = "是否关闭")
  private String isClose;

  /**
   * 已使用金额
   */
  @ApiModelProperty(name = "totalAmount", notes = "已使用金额", value = "已使用金额")
  private BigDecimal totalAmount;

  /**
   * 终端使用金额
   */
  @ApiModelProperty(name = "terminalAmount", notes = "终端使用金额", value = "终端使用金额")
  private BigDecimal terminalAmount;

  /**
   * 活动动态表单的formCode
   */
  @ApiModelProperty(name = "activitiesFormCode", notes = "活动动态表单的formCode", value = "活动动态表单的formCode")
  private String activitiesFormCode;

  /**
   * 活动字段采集的formCode
   */
  @ApiModelProperty(name = "fieldsCollectFormCode", notes = "活动字段采集的动态表单formCode", value = "活动字段采集的动态表单formCode")
  private String fieldsCollectFormCode;

  @ApiModelProperty("活动细类信息")
  private CostTypeDetailVo costTypeDetailVo;

  /**
   * 活动订单明细
   */
  @ApiModelProperty(name = "activitiesDetailSerials", notes = "活动订单明细", value = "活动订单明细")
  private List<ActivitiesDetailSerialVo> activitiesDetailSerials;

  /**
   * 活动订单图片采集要求数据
   */
  @ApiModelProperty(name = "activitiesDetailCollects", notes = "活动订单图片采集要求数据", value = "活动订单图片采集要求数据")
  private List<ActivitiesDetailCollectVo> activitiesDetailCollects;

  /**
   * 图片采集要求
   */
  @ApiModelProperty(name = "photoRequires", notes = "图片采集要求", value = "图片采集要求")
  private List<ApprovalCollectVo> photoRequires;

  /**
   * 活动字段采集模板数据(同活动 不同批次)
   */
  @ApiModelProperty(name = "fieldsCollectTemplates", notes = "活动字段采集模板数据(同活动 不同批次)", value = "活动字段采集模板数据(同活动 不同批次)")
  private List<ActivitiesFieldsCollectTemplateFeginVo> fieldsCollectTemplates;

  /**
   * 活动字段采集模板结构
   */
  @ApiModelProperty(name = "fieldsCollectTemplateStruct", notes = "活动字段采集模板结构", value = "活动字段采集模板结构")
  private JSONObject fieldsCollectTemplateStruct;

  /**
   * 采集策略结构
   */
  @ApiModelProperty(name = "collectStrategyTemplateStruct", notes = "采集策略结构", value = "采集策略结构")
  private JSONObject collectStrategyTemplateStruct;

  /**
   * 活动信息
   */
  @ApiModelProperty(name = "activities", notes = "活动信息", value = "活动信息")
  private ActivitiesVo activities;

  /**
   * 是否推送sfa
   */
  @ApiModelProperty(name = "isSendSfa", notes = "是否推送sfa", value = "是否推送sfa")
  private String isSendSfa;

  /**
   * 是否执行
   */
  @ApiModelProperty(name = "isExecute", notes = "是否执行", value = "是否执行")
  private String isExecute;

  /**
   * 是否完全核销
   */
  @ApiModelProperty(name = "isFullAudit", notes = "是否完全核销", value = "是否完全核销")
  private String isFullAudit;

}
