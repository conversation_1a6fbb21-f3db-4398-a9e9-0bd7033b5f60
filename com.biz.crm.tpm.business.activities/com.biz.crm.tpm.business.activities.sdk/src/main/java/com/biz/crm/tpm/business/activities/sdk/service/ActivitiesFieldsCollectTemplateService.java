package com.biz.crm.tpm.business.activities.sdk.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesFieldsCollectTemplateDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesFieldsCollectTemplateVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ActivitiesFieldsCollectTemplateService {

  /**
   * 分页查询
   */
  Page<ActivitiesFieldsCollectTemplateVo> findByConditions(Pageable pageable, ActivitiesFieldsCollectTemplateDto dto);

  /**
   * 新增
   */
  ActivitiesFieldsCollectTemplateVo create(JSONObject json);

  /**
   * 更新
   */
  ActivitiesFieldsCollectTemplateVo update(JSONObject json);

  /**
   * 根据编码查询数据
   */
  ActivitiesFieldsCollectTemplateVo findByCode(String code);

  /**
   * 根据关联的活动明细编码查询数据
   */
  List<ActivitiesFieldsCollectTemplateVo> findByActivityDetailCode(String activityDetailCode);

  /**
   * 根据主键id查询信息
   */
  ActivitiesFieldsCollectTemplateVo findById(String id);

  /**
   * 根据主键id集合查询信息
   */
  List<ActivitiesFieldsCollectTemplateVo> findByIds(Set<String> ids);

  /**
   * 根据编码查询数据
   */
  Map<String, BaseActivityItemVo> findByParentCode(String parentCode);

  /**
   * 删除
   */
  void delete(Set<String> ids);

}
