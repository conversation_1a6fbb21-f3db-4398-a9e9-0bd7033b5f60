package com.biz.crm.tpm.business.activities.sdk.vo;

import com.bizunited.nebula.common.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 参数传递vo：活动表单配置明细vo
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:46
 */
@ApiModel(value = "ActivitiesConfigDetailVo",description = "活动表单配置明细vo")
@Getter
@Setter
public class ActivitiesConfigDetailVo extends TenantVo {

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesConfigId", notes = "活动表单编号", value = "活动表单编号")
  private String activitiesConfigId;

  /**
   * 表单类型策略编号
   */
  @ApiModelProperty(name = "activitiesFormTypeStrategyCode", notes = "表单类型策略编号", value = "表单类型策略编号")
  private String activitiesFormTypeStrategyCode;

  /**
   * 表单类型策略名称
   */
  @ApiModelProperty(name = "activitiesFormTypeStrategyName", notes = "表单类型策略名称", value = "表单类型策略名称")
  private String activitiesFormTypeStrategyName;

  /**
   * 是否必需
   */
  @ApiModelProperty("是否必需")
  private Boolean necessary;

}
