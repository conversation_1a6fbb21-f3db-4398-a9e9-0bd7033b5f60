package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ActivitiesTemplateConfigDetailDto", description = "活动模板配置字段配置dto")
public class ActivitiesTemplateConfigDetailDto extends TenantFlagOpDto {


    /**
     * 配置编码
     */
    @ApiModelProperty("配置编码")
    private String configCode;

    /**
     * 显示属性
     */
    @ApiModelProperty("显示属性")
    private String field;

    /**
     * 字段名称
     */
    @ApiModelProperty("字段名称")
    private String title;

    /**
     * 显隐状态：1显示，0隐藏
     */
    @ApiModelProperty("显隐状态：1显示，0隐藏")
    private Boolean visible;

    /**
     * 是否必填
     */
    @ApiModelProperty("是否必填")
    private Boolean required;

    /**
     * 新增页面是否可编辑
     */
    @ApiModelProperty("新增页面是否可编辑")
    private Boolean editableInCreate;

    /**
     * 编辑页面是否可编辑
     */
    @ApiModelProperty("编辑页面是否可编辑")
    private Boolean editableInEdit;

    /**
     * 编辑时是否可显示
     */
    @ApiModelProperty("编辑时是否可显示")
    private Boolean visibleInEdit;

    /**
     * 查看时是否显示
     */
    @ApiModelProperty("查看时是否显示")
    private Boolean visibleInLook;

    /**
     * 控件类型
     */
    @ApiModelProperty("控件类型")
    private String formControl;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer formorder;

    /**
     * 排序
     */
    @ApiModelProperty("数据字典")
    private String dictCode;

    /**
     * 是否导出：1导出，0不导出
     */
    @ApiModelProperty("是否导出：1导出，0不导出")
    private Boolean exports;
}
