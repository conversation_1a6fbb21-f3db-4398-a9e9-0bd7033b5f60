package com.biz.crm.tpm.business.activities.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CostCenterFieldsEnum {
    costCenterCode("costCenterCode"),
    costCenterName("costCenterName"),
    bearAmount("bearAmount"),
    secondaryCost("secondaryCost"),
    xzjghtybzs("xzjghtybzs"),
    ;

    private String dictCode;

    public static CostCenterFieldsEnum findByCode(String code) {
        Optional<CostCenterFieldsEnum> first = Stream.of(CostCenterFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}