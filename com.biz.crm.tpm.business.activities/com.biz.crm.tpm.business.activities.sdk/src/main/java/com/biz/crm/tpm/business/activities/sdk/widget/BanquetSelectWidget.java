package com.biz.crm.tpm.business.activities.sdk.widget;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.form.sdk.widget.WidgetKey;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 宴会类型下拉选择控件
 */
@Component
public class BanquetSelectWidget implements WidgetKey {

  @Override
  public String widgetCode() {
    return "banquetSelectWidget";
  }

  @Override
  public String widgetName() {
    return "宴会类型下拉选择控件";
  }

  @SuppressWarnings("unchecked")
  @Override
  public Map<String, Object> widgetParam() {
    JSONObject banquetObject = new JSONObject();
    banquetObject.put("喜宴", "wedding");
    banquetObject.put("寿宴", "birthday");
    banquetObject.put("丧宴", "funeral");
    banquetObject.put("订婚宴", "engagement");
    banquetObject.put("家宴", "family");
    banquetObject.put("其他", "other");
    
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("valueMapping", banquetObject);

    Map<String , Object> valueMapping = (Map<String , Object>)JSON.parse(jsonObject.toJSONString());
    return valueMapping;
  }
}
