package com.biz.crm.tpm.business.activities.sdk.service;

import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BasicActivityItemVo;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 主活动信息扩展接口，需要有主活动实现类实现该接口
 * 注：只有实现了该接口的活动实现，才能收集不同类的活动信息
 */
public interface BasicActivitiesInfoService {

  /**
   * 活动注册
   * key：活动标识(标识某一类活动) 比如：普通活动、项目活动等
   * value：活动的实现类对象，主要是XXXservice
   */
  Map<String,BasicActivitiesInfoService> ACTIVITY_SERVICE_MAPPING = Maps.newHashMap();

  /**
   * 为主活动指定一个活动标识
   *
   */
  String activityMark();

  /**
   * 根据主活动编码，获取活动明细信息
   */
  Map<String, List<BasicActivityItemVo>> findByParentCode(String parentCode);

  /**
   * 根据主活动编码，获取活动详情信息
   */
  ActivitiesVo findDetailsByParentCode(String parentCode);

  /**
   * 根据主活动编码，获取活动详情信息（批量）
   */
  List<ActivitiesVo> findDetailsByParentCodes(Set<String> parentCodes);

  /**
   * 根据指定的活动编码和活动明细编码，获取指定明细信息
   */
  BasicActivityItemVo findByParentCodeAndItemCode(String parentCode, String itemCode);

  /**
   * 活动关闭
   * 注意：codeMap的key为主活动编码，value为对应的活动明细编码集合
   */
  void onClosed(Map<String, Set<String>> codeMap);

  /**
   * 定时刷新活动状态，只针对活动时间来进行
   */
  void refreshActivityStatusForActivityTime();
}
