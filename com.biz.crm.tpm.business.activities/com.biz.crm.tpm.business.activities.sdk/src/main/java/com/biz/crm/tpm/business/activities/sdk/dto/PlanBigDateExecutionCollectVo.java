package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectVo
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlanBigDateExecutionCollectVo extends UuidFlagOpVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    private String actExecuteCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("执行人")
    private String executor;

    @ApiModelProperty("执行时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeDate;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("区编码")
    private String districtCode;

    @ApiModelProperty("区名称")
    private String districtName;

//    @ApiModelProperty("经度")
//    @Column(name = "longitude", columnDefinition = "decimal(12,8) COMMENT '经度'")
//    private BigDecimal longitude;
//
//    @ApiModelProperty("纬度")
//    @Column(name = "latitude", columnDefinition = "decimal(12,8) COMMENT '纬度'")
//    private BigDecimal latitude;

    @ApiModelProperty("仓库地址")
    private String warehouseAddress;

    @ApiModelProperty("仓库id")
    private String warehouseId;

    /**
     * 执行时间 yyyy-mm-dd
     */
    @ApiModelProperty("执行时间")
    private String executionTime;

    /****************************SAF步骤所需基础字段*******************************************/

    @ApiModelProperty("执行计划业务编码")
    private String parentCode;

    @ApiModelProperty("步骤业务编码stepCode")
    private String dynamicKey;

    @ApiModelProperty("动态表单全局唯一编码formCode")
    private String dynamicFormCode;

    @ApiModelProperty("位置信息:经度")
    private BigDecimal longitude;

    @ApiModelProperty("位置信息:纬度")
    private BigDecimal latitude;

    @ApiModelProperty("创建人组织")
    private String createOrgCode;

    @ApiModelProperty("创建人组织名称")
    private String createOrgName;

    @ApiModelProperty("创建人职位编码")
    private String createPostCode;

    @ApiModelProperty("创建人职位名称")
    private String createPostName;

    @ApiModelProperty("是否展示水印(Y:是,N:否)")
    private String watermarkFlag;

    @ApiModelProperty("水印")
    private String watermarkStr;

    @ApiModelProperty("产品明细")
    private List<PlanBigDateExecutionCollectDetailVo> expirationBackProducts;
}
