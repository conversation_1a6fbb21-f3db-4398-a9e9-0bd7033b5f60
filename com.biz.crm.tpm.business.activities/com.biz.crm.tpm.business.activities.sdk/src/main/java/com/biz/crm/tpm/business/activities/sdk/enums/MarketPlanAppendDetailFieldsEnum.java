package com.biz.crm.tpm.business.activities.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MarketPlanAppendDetailFieldsEnum {
    detailName("detailName"),
    costCenterName("costCenterName"),
    bearDepartmentName("bearDepartmentName"),
    belongDepartmentName("belongDepartmentName"),
    planYears("planYears"),
    years("years"),
    applyAmount("applyAmount"),
    actDesc("actDesc"),
    ;

    private String dictCode;

    public static MarketPlanAppendDetailFieldsEnum findByCode(String code) {
        Optional<MarketPlanAppendDetailFieldsEnum> first = Stream.of(MarketPlanAppendDetailFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}