package com.biz.crm.tpm.business.activities.sdk.constant;

/**
 * 描述：</br>活动模块的常量
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
public interface ActivitiesConstant {

  /**
   * 活动订单金额锁 根据租户编号以及活动明细编号
   */
  String ACTIVITIES_ORDER_LOCK_PREFIX_FORMAT = "bz:crm:tpm:activities:order:lock:%s:%s";

  /**
   * 数据采集编号
   */
  String COLLECT_LADDER_CODE = "CO";

  /**
   * 对应动态表单主表信息中明细items的字段属性名称
   */
  String DYNAMIC_FORM_FIELD_CODE = "items";

  /**
   * 推送到sfa策略名称
   */
  String STRATEGY_PUSH_SFA = "PushSfaFormEventStrategy";

  /**
   * 自动核销策略名称
   */
  String STRATEGY_AUTO_AUDIT = "AutoAuditFormEventStrategy";

  /**
   * 表单参数：是否推送到sfa
   */
  String FORM_PROPERTIES_PUSH_SFA = "pushSfa";

  /**
   * 表单参数：是否采集活动数据
   */
  String FORM_PROPERTIES_COLLECT_ACTIVITY_DATA = "collectActivityData";

  /**
   * 表单参数：是否采集分销订单
   */
  String FORM_PROPERTIES_COLLECT_DISTRIBUTION_ORDER = "collectDistributionOrders";

  /**
   * 表单参数：是否签署陈列协议
   */
  String FORM_PROPERTIES_SIGN_DISPLAY = "signDisplay";

  /**
   * 表单参数：是否控制活动金额
   */
  String FORM_PROPERTIES_CONTROL_ACTIVITY_EXPENSES = "controlActivityExpenses";

  /**
   * 表单参数：是否启用
   */
  String FORM_PROPERTIES_ENABLED = "enabled";


}
