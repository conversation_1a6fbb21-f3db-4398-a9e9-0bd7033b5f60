package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @className: com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectDetailChildVo
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlanBigDateExecutionCollectDetailChildVo extends UuidFlagOpVo {

    @ApiModelProperty("大日期回调采集id")
    private String collectId;

    @ApiModelProperty("产品父id")
    private String detailId;

    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    private String batchCode;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal quantity;
}
