package com.biz.crm.tpm.business.activities.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@ApiModel("管报vo")
public class WithHoldingIncomeVo implements Serializable {
    private static final long serialVersionUID = 3489329483649L;


    private Long id;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织编码")
    private String orgName;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("管报收入")
    private BigDecimal reportIncome;

    @ApiModelProperty("管保实际金额")
    private BigDecimal actualReportAmount;

}
