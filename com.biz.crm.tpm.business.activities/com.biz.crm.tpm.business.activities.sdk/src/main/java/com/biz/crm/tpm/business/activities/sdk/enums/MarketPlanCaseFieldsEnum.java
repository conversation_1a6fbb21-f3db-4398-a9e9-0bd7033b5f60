package com.biz.crm.tpm.business.activities.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MarketPlanCaseFieldsEnum {
    fysybm("fysybm"),
    khmc("khmc"),
    mdmc("mdmc"),
    fyxm("fyxm"),
    fygzyf("fygzyf"),
    sqje("sqje"),
    faghmc("faghmc"),
    hdms("hdms"),
    famxbm("famxbm"),
    cbzx("cbzx"),
    xzjghtybzs("xzjghtybzs"),
    bgqje("bgqje"),
    bghje("bghje"),
    gbjtsjje("gbjtsjje"),
    ywbm("ywbm"),
    ;

    private String dictCode;

    public static MarketPlanCaseFieldsEnum findByCode(String code) {
        Optional<MarketPlanCaseFieldsEnum> first = Stream.of(MarketPlanCaseFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}