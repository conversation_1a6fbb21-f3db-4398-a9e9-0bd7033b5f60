package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectDetailVo
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlanBigDateExecutionCollectDetailVo extends UuidFlagOpVo {

    @ApiModelProperty("大日期回调采集id")
    private String collectId;

    @ApiModelProperty("产品层级编码")
    private String productLevelCode;

    @ApiModelProperty("产品层级名称")
    private String productLevelName;

    @ApiModelProperty("商品编码")
    private String productCode;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("图片URL")
    private String productImgUrl;

    @ApiModelProperty("产品单位编码")
    private String productUnit;

    @ApiModelProperty("产品单位名称")
    private String productUnitName;

    @ApiModelProperty("产品批次")
    private List<PlanBigDateExecutionCollectDetailChildVo> expirationBackBatchs;

    @ApiModelProperty("采集图片")
    private List<PlanBigDateExecutionCollectDetailPictureVo> expirationBackPictures;
}
