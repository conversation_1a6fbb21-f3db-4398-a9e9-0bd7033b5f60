package com.biz.crm.tpm.business.activities.sdk.service;

import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;

/**
 * 描述：</br>活动数据注册监听器
 * 实现活动后，在活动审批通过后需要实现该通知，将审批后的活动信息注册进入活动中心；
 * 后续其他模块对活动数据的加载均通过活动中心活动数据提供
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
public interface ActivitiesRegisterService {

  /**
   * 审核通过后通知活动中心
   * 注册活动数据
   *
   * @param dto
   */
  void register(ActivitiesDto dto);
}
