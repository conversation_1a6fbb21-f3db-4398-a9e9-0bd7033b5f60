package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * 参数传递dto：活动表单配置dto
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:46
 */
@ApiModel(value = "ActivitiesConfigDto",description = "活动表单配置dto")
@Getter
@Setter
public class ActivitiesConfigDto extends TenantFlagOpDto {

  /**
   * 活动配置名称
   */
  @ApiModelProperty(name = "activitiesConfigName", notes = "活动配置名称", value = "活动配置名称")
  private String activitiesConfigName;

  /**
   * 活动配置编号
   */
  @ApiModelProperty(name = "activitiesConfigCode", notes = "活动配置编号", value = "活动配置编号")
  private String activitiesConfigCode;

  /**
   * 活动表单类型名称
   */
  @ApiModelProperty(name = "activitiesFormTypeCode", notes = "活动表单类型编号", value = "活动表单类型编号")
  private String activitiesFormTypeCode;

  /**
   * 活动表单类型名称
   */
  @ApiModelProperty(name = "activitiesFormTypeName", notes = "活动表单类型名称", value = "活动表单类型名称")
  private String activitiesFormTypeName;

  /**
   * 动态表单编号
   */
  @ApiModelProperty(name = "dynamicFormCode", notes = "动态表单编号", value = "动态表单编号")
  private String dynamicFormCode;

  /**
   * 动态表单名称
   */
  @ApiModelProperty(name = "dynamicFormName", notes = "动态表单名称", value = "动态表单名称")
  private String dynamicFormName;

  /**
   * 表单注册器集合
   */
  @ApiModelProperty(name = "activitiesConfigDetails", notes = "表单注册器集合", value = "表单注册器集合")
  private List<ActivitiesConfigDetailDto> activitiesConfigDetails;
}
