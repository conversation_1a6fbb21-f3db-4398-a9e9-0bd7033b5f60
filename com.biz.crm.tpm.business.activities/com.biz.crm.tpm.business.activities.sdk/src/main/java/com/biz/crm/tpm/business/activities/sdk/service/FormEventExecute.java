package com.biz.crm.tpm.business.activities.sdk.service;

import com.biz.crm.common.form.sdk.model.DynamicForm;

import java.util.Map;

/**
 * 描述：</br>表单执行器执行
 * 根据
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
public interface FormEventExecute {

  /**
   * 表单执行方法
   * 根据关联业务编号获取需要执行的策略，提供执行策略上下文对每次执行的返回结果进行记录，供下一步执行策略使用
   *
   * @param businessCode 动态表单绑定编号
   * @param params
   * @param data
   */
  <T extends DynamicForm> void execute(String businessCode, T data, Map<String, ?> params);
}
