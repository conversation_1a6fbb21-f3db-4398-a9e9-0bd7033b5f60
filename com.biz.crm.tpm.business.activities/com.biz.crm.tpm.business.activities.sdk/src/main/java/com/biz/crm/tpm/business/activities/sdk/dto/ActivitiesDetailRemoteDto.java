package com.biz.crm.tpm.business.activities.sdk.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "ActivitiesDetailRemoteDto", description = "用于(sfa to tpm)活动信息远程保存接口dto")
@Getter
@Setter
public class ActivitiesDetailRemoteDto implements Serializable {
  /** 批次号 */
  @ApiModelProperty(name = "btNo", notes = "批次号", value = "批次号")
  private String btNo;

  /** 活动明细编号 */
  @ApiModelProperty(name = "activitiesDetailCode", notes = "活动明细编号", value = "活动明细编号")
  private String activitiesDetailCode;

  /** 活动订单明细 */
  @ApiModelProperty(name = "activitiesDetailSerials", notes = "活动订单明细", value = "活动订单明细")
  private List<ActivitiesDetailSerialDto> activitiesDetailSerials;

  /** 活动订单图片采集要求数据 */
  @ApiModelProperty(name = "activitiesDetailCollects", notes = "活动订单图片采集要求数据", value = "活动订单图片采集要求数据")
  private List<ActivitiesDetailCollectDto> activitiesDetailCollects;

  /** 活动字段采集模板数据(同活动 不同批次) */
  @ApiModelProperty(name = "fieldsCollectTemplate",notes = "活动字段采集模板数据(同活动 不同批次)", value= "活动字段采集模板数据(同活动 不同批次)")
  private JSONObject fieldsCollectTemplate;

}
