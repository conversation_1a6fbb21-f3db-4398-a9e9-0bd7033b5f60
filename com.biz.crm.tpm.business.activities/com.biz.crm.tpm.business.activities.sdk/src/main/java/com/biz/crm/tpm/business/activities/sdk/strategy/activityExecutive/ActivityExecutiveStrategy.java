package com.biz.crm.tpm.business.activities.sdk.strategy.activityExecutive;

import com.alibaba.fastjson.JSONObject;

/**
 * 活动执行表单绑定策略
 * <AUTHOR> rentao
 * @date : 2022/10/30 20:56
 */
public interface ActivityExecutiveStrategy {

  /**
   * 表单类型编码 在表单配置页面使用 用于选择表单类型 必须保持整个策略中编码唯一
   */
  String getActivitiesFormTypeStrategyCode();

  /**
   * 表单类型名称 在表单配置页面使用 用于选择表单类型回显汉字使用
   */
  String getActivitiesFormTypeStrategyName();

  /**
   * 策略执行方法
   */
  void execute(JSONObject jsonObject);
}
