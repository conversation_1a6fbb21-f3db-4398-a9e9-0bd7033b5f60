package com.biz.crm.tpm.business.activities.sdk.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.sdk.dto.FileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PlanExecutionPicture", description = "执行照片")
public class PlanExecutionPictureDto extends FileDto {

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

}
