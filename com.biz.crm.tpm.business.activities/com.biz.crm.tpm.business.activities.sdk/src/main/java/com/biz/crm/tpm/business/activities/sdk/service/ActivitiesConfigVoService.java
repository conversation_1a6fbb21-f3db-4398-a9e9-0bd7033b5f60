package com.biz.crm.tpm.business.activities.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 活动配置vo service
 * <AUTHOR> rentao
 * @date : 2022/11/2 16:32
 */
public interface ActivitiesConfigVoService {


  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<ActivitiesConfigVo> findByConditions(Pageable pageable, ActivitiesConfigDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  ActivitiesConfigVo findById(String id);

  /**
   * 通过编码查询单条数据
   *
   * @param activitiesConfigCode 编码
   * @return 单条数据
   */
  ActivitiesConfigVo findByActivitiesConfigCode(String activitiesConfigCode);

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<ActivitiesConfigVo> findByIds(Collection<String> ids);

  /**
   * 新增数据
   *
   * @param activitiesConfigDto 实体对象
   * @return 新增结果
   */
  ActivitiesConfigVo create(ActivitiesConfigDto activitiesConfigDto);

  /**
   * 编辑数据
   *
   * @param activitiesConfigDto 实体对象
   * @return 编辑结果
   */
  ActivitiesConfigVo update(ActivitiesConfigDto activitiesConfigDto);

  /**
   * 活动表单类型编号查询所有数据
   *
   * @param activitiesFormTypeCode 活动表单类型编号
   * @return 所有数据
   */
  List<ActivitiesConfigVo> findByActivitiesFormTypeCode(String activitiesFormTypeCode);
}
