package com.biz.crm.tpm.business.activities.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MarketingEstimationFieldsEnum {
    projectCode("projectCode"),
    sort("sort"),
    projectName("projectName"),
    estimateFixedPoint("estimateFixedPoint"),
    estimateAmount("estimateAmount"),
    estimateRatio("estimateRatio"),
    budgetFixedPoint("budgetFixedPoint"),
    budgetAmount("budgetAmount"),
    budgetRatio("budgetRatio"),
    differenceAmount("differenceAmount"),
    differenceRatio("differenceRatio"),
    ;

    private String dictCode;

    public static MarketingEstimationFieldsEnum findByCode(String code) {
        Optional<MarketingEstimationFieldsEnum> first = Stream.of(MarketingEstimationFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}