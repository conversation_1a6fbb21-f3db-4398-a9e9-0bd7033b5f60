package com.biz.crm.tpm.business.activities.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesShareDto;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * 描述：</br>
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
public interface ActivitiesService {

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<ActivitiesVo> findByConditions(Pageable pageable, ActivitiesDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  ActivitiesVo findById(String id);

  /**
   * 通过编号查询单条数据
   *
   * @param activitiesCode 编号
   * @return 单条数据
   */
  ActivitiesVo findByActivitiesCode(String activitiesCode);

  /**
   * 通过编号集合查询单条数据
   *
   * @param activitiesCodes 编号
   */
  List<ActivitiesVo> findByActivitiesCodes(Set<String> activitiesCodes);

  /**
   * 通过编号查询单条数据详情
   *
   * @param activitiesCode 编号
   * @return 单条数据
   */
  ActivitiesVo findDetailsByActivitiesCode(String activitiesCode);

  /**
   * 新增数据
   *
   * @param activitiesDto 实体对象
   * @return 新增结果
   */
  ActivitiesVo create(ActivitiesDto activitiesDto);

  /**
   * 修改数据
   *
   * @param activitiesDto 实体对象
   * @return 修改结果
   */
  ActivitiesVo update(ActivitiesDto activitiesDto);

  /**
   * 根据前端传入的动态表单信息，填充分摊信息
   */
  List<?> shareForFeeDate(ActivitiesShareDto dto);

  /**
   * 更新活动的核销状态
   *
   * @param activitiesCode
   * @return
   */
  ActivitiesVo updateForAudit(String activitiesCode);

  /**
   * 更新活动的关闭状态
   *
   * @param activitiesCode
   * @return
   */
  ActivitiesVo closeForAudit(String activitiesCode);

  /**
   * 根据主表code，查询所有可能的主表信息与明细信息
   */
  List<ActivitiesVo> findDetailsByActivitiesCodes(Set<String> parentCodes);

  /**
   * 通过时间查询活动编号（时间格式为 yyyy-mm）
   *
   * @param time
   * @return
   */
  List<String> findCodeByTime(String time);

  /**
   * 根据活动大类查询是否有活动使用
   *
   * @param costTypeCategoryCode
   * @return
   */
  boolean existsByCostCategory(String costTypeCategoryCode);
}
