package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@ApiModel(value = "AlongWithOrderDto", description = "随单比例管控")
@Data
public class AlongWithOrderDto extends TenantFlagOpDto {


    /**
     * 随单比例编号
     */
    @ApiModelProperty("随单比例编号")
    private String alongWithOrderCode;

    /**
     * 随单比例名称
     */
    @ApiModelProperty("随单比例名称")
    private String alongWithOrderName;

    /**
     * 生效开始年月
     */
    @ApiModelProperty("生效开始年月")
    private String startYearMonth;

    /**
     * 生效结束年月
     */
    @ApiModelProperty("生效结束年月")
    private String endYearMonth;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;


    List<AlongWithOrderDetailDto> cacheList;
}
