package com.biz.crm.tpm.business.activities.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.AuditExecutionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanExecutionDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionAggregateVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo;
import com.google.common.collect.Lists;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface PlanExecutionService {

    /**
     * 根据活动明细编码查询活动执行
     *
     * @param code
     * @return
     */
    List<PlanExecutionVo> findByDetailCode(String code);

    List<PlanExecutionVo> findBySchemeDetailCode(String schemeDetailCode);

    /**
     * 根据活动明细编码查询活动执行 - 聚合
     * @param code
     * @return
     */
    PlanExecutionAggregateVo findByDetailCodeV1(String code);

    /**
     * 创建活动执行
     *
     * @param dto
     */
    void createPlanExecution(PlanExecutionDto dto);

    /**
     * 修改活动执行
     *
     * @param dto
     */
    void updatePlanExecution(PlanExecutionDto dto);

    /**
     * 查询详情
     *
     * @param
     */
    PlanExecutionVo findByDynamicKey(PlanExecutionDto dto);


    default PlanExecutionVo findDetail(PlanExecutionDto dto) {
        return null;
    }

    /**
     * 活动采集数据详情查询
     *
     * @param id
     * @return
     */
    default PlanExecutionVo findDetailById(String id) {
        return null;
    }

    /**
     * 新增终端活动稽查采集
     *
     * @param vo
     */
    void createAuditExecutionCollect(AuditExecutionCollectVo vo);

    /**
     * 终端活动稽查详情查询
     *
     * @param dto
     * @return
     */
    default AuditExecutionCollectVo findAuditExecutionDetail(AuditExecutionCollectVo dto) {
        return null;
    }

    default List<MarketingPlanCaseExecuteVo> findByTerminalMarketingPlanCaseExecuteList(MarketingPlanCaseQueryDto dto) {
        return Lists.newArrayList();
    }

    /**
     * 活动执行采集分页接口
     * @param pageable
     * @param dto
     * @return
     */
    default Page<PlanExecutionVo> findByConditions(Pageable pageable,PlanExecutionDto dto){
        return new Page<>();
    }

    default void initPlanExecutionCustom() {
    }
}
