package com.biz.crm.tpm.business.activities.sdk.template;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@Getter
@Setter
@ToString
@NoArgsConstructor
@MappedSuperclass
public abstract class BaseActivityItem extends TenantOpEntity {
  @ApiModelProperty("对应的活动编号")
  @TableField(value = "parent_code")
  @Column(name = "parent_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '对应的活动编号'")
  private String parentCode;

  @ApiModelProperty("活动明细items对应的Map key键值")
  @TableField(value = "dynamic_key")
  @Column(name = "dynamic_key", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动明细items对应的Map key键值'")
  private String dynamicKey;

  @ApiModelProperty("活动明细对应的动态模板键值")
  @TableField(value = "dynamic_form_code")
  @Column(name = "dynamic_form_code", nullable = false, columnDefinition = "varchar(255) COMMENT '活动明细对应的动态模板键值'")
  private String dynamicFormCode;

  @ApiModelProperty("可能的前端标签页")
  @TableField(value = "label")
  @Column(name = "label", length = 64, columnDefinition = "varchar(64) COMMENT '可能的前端标签页'")
  private String label;

  @ApiModelProperty("活动明细编码")
  @TableField(value = "item_code")
  @Column(name = "item_code", length = 64, nullable = false, unique = true, columnDefinition = "varchar(64) COMMENT '活动明细编码'")
  private String itemCode;

  @ApiModelProperty("分摊标识键值")
  @TableField(value = "share_key")
  @Column(name = "share_key", columnDefinition = "varchar(64) COMMENT '分摊标识键值'")
  private String shareKey;
}
