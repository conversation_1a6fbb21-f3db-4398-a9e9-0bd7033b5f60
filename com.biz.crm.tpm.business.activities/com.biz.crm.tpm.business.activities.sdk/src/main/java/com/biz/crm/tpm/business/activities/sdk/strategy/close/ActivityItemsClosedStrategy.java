package com.biz.crm.tpm.business.activities.sdk.strategy.close;

import com.biz.crm.tpm.business.activities.sdk.strategy.common.DynamicFormCodeStrategy;

import java.util.Set;

/**
 * 针对活动"状态"字段status，处理活动关闭相关
 */
public interface ActivityItemsClosedStrategy extends DynamicFormCodeStrategy {

  /** 用于对活动状态字段更新，全局锁 */
  String GLOBAL_ACTIVITY_STATUS_REDIS_LOCK = "GLOBAL_ACTIVITY_STATUS_REDIS_LOCK";

  /**
   * 对传入的活动明细进行关闭操作
   */
  void closed(Set<String> itemCodes);

  /**
   * 传入主活动编码，辨别活动是否完全关闭
   */
  boolean allClosed(String parentCode);
}
