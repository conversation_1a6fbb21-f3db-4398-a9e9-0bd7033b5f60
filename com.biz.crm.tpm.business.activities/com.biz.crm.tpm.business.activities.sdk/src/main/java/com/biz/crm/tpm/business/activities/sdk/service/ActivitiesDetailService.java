package com.biz.crm.tpm.business.activities.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailRemoteDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 活动明细信息表;(tpm_activities_detail)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-28
 */
public interface ActivitiesDetailService {

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<ActivitiesDetailVo> findByConditions(Pageable pageable, ActivitiesDetailDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  ActivitiesDetailVo findById(String id);

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<ActivitiesDetailVo> findByIds(Collection<String> ids);

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCode
   * @return 单条数据
   */
  List<ActivitiesDetailVo> findByActivitiesCode(String activitiesCode);

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCodes
   * @return 多条数据
   */
  Map<String, List<ActivitiesDetailVo>> findByActivitiesCodes(Collection<String> activitiesCodes);

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCodes
   * @return 多条数据
   */
  List<ActivitiesDetailVo> findAllByActivitiesCodes(Collection<String> activitiesCodes);

  /**
   * 根绝业务编号activitiesDetailCodes获取业务数据
   *
   * @param activitiesDetailCodes
   * @return 多条数据
   */
  List<ActivitiesDetailVo> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes);

  /**
   * 根绝业务编号activitesDetailCode获取业务数据
   *
   * @param activitesDetailCode
   * @return 单条数据
   */
  ActivitiesDetailVo findByActivitiesDetailCode(String activitesDetailCode);

  /**
   * 根绝业务编号activitesDetailCode获取业务数据
   *
   * @param activitesDetailCode
   * @return 单条数据
   */
  ActivitiesDetailVo findDetailByActivitiesDetailCode(String activitesDetailCode);

  /**
   * 新增数据
   *
   * @param activitiesDetailDto 实体对象
   * @return 新增结果
   */
  ActivitiesDetailVo create(ActivitiesDetailDto activitiesDetailDto);

  /**
   * 批量新增
   *
   * @param activitiesDetailDtos
   * @return
   */
  List<ActivitiesDetailVo> createBatch(Collection<ActivitiesDetailDto> activitiesDetailDtos);

  /**
   * 关闭活动明细
   *
   * @param activitiesDetailDtos
   */
  void closed(Collection<ActivitiesDetailDto> activitiesDetailDtos);

  /**
   * 根据活动大类统计匹配该活动大类的活动明细数量
   *
   * @param costTypeCategoryCode
   * @return
   */
  int countByCostTypeCategoryCode(String costTypeCategoryCode);

  /**
   * 更新订单金额
   *
   * @param activitiesDetailCode
   * @param changeAmount
   */
  void updateOrderAmountByActivitiesCode(String activitiesDetailCode, BigDecimal changeAmount, boolean isAdd);

  /**
   * 更新活动明细完全核销状态
   * @param activitiesDetailCode
   * @param isFullAudit
   */
  void updateFullAuditByActivitiesDetailCode(String activitiesDetailCode, String isFullAudit);

  /**
   * 远程活动信息保存接口(sfa to tpm)
   */
  ActivitiesDetailVo createRemote(ActivitiesDetailRemoteDto dto);

  /**
   * 根据活动编号以及关闭状态统计活动明细数量
   *
   * @param activitiesCode
   * @return
   */
  int countByActivitiesCodeAndClosed(String activitiesCode);
}
