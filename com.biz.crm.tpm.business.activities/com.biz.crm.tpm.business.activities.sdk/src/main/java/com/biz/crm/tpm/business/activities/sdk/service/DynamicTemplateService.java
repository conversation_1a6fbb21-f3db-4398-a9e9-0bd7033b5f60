package com.biz.crm.tpm.business.activities.sdk.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.DynamicTemplateDto;
import com.biz.crm.tpm.business.activities.sdk.vo.DynamicTemplateVo;
import org.springframework.data.domain.Pageable;

public interface DynamicTemplateService {
  /**
   * 分页查询所有数据
   */
  Page<DynamicTemplateVo> findByConditions(Pageable pageable, DynamicTemplateDto dto);

  /**
   * 按照动态表单在系统中唯一的业务编码进行查询
   */
  JSONObject findByDynamicFormCode(String dynamicFormCode);

  /**
   * 按照动态表单在系统中唯一的业务编码和表单设置的字段映射编号进行查询
   *
   * @param dynamicFormCode 业务编码
   * @param mappingCode     字段映射编号
   * @return 动态表单
   */
  JSONObject findByDynamicFormCodeAndMappingCode(String dynamicFormCode, String mappingCode);
}
