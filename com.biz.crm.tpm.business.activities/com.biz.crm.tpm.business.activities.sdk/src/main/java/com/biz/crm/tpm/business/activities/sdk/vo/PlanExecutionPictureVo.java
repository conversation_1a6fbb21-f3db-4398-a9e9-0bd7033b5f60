package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.FileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PlanExecutionPicture", description = "执行照片")
public class PlanExecutionPictureVo extends FileVo {

//    @ApiModelProperty("方案明细编码")
//    private String schemeDetailCode;

    @ApiModelProperty("采集id")
    private String collectId;

}
