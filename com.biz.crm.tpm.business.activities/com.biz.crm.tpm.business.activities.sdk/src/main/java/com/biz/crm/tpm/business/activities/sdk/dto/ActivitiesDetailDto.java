package com.biz.crm.tpm.business.activities.sdk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * 参数传递dto：活动明细信息表;
 * <AUTHOR> Keller
 * @date : 2022-6-28
 */
@ApiModel(value = "ActivitiesDetail",description = "活动明细信息表")
@Getter
@Setter
public class ActivitiesDetailDto implements Serializable,Cloneable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value = "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 创建人账号 */
  @ApiModelProperty(name = "createAccount",notes = "创建人账号", value = "创建人账号")
  private String createAccount;
  /** 创建人名称 */
  @ApiModelProperty(name = "createName",notes = "创建人名称", value = "创建人名称")
  private String createName;
  /** 创建时间 */
  @ApiModelProperty(name = "createTime",notes = "创建时间", value = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value = "活动编号")
  private String activitiesCode;
  /** 活动编号集合*/
  @ApiModelProperty(name = "activitiesCodes",notes = "活动编号集合", value = "活动编号集合")
  private Set<String> activitiesCodes;
  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value = "活动名称")
  private String activitiesName;
  /** 活动明细编号 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编号", value = "活动明细编号")
  private String activitiesDetailCode;
  /** 活动明细编号集合 */
  @ApiModelProperty(name = "activitiesDetailCodes",notes = "活动明细编号集合", value = "活动明细编号集合")
  private Set<String> activitiesDetailCodes;
  /** 活动大类名称 */
  @ApiModelProperty(name = "costTypeCategoryName",notes = "活动大类名称", value = "活动大类名称")
  private String costTypeCategoryName;
  /** 活动大类编号 */
  @ApiModelProperty(name = "costTypeCategoryCode",notes = "活动大类编号", value = "活动大类编号")
  private String costTypeCategoryCode;
  /** 费用预算编码 */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编码", value = "费用预算编码")
  private String costBudgetCode;
  /** 活动细类编号 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编号", value = "活动细类编号")
  private String costTypeDetailCode;
  /** 活动细类名称 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value = "活动细类名称")
  private String costTypeDetailName;
  /** 组织编码 */
  @ApiModelProperty(name = "orgCode",notes = "组织编码", value = "组织编码")
  private String orgCode;
  /** 组织名称 */
  @ApiModelProperty(name = "orgName",notes = "组织名称", value = "组织名称")
  private String orgName;
  /** 客户编号 */
  @ApiModelProperty(name = "customerCode",notes = "客户编号", value = "客户编号")
  private String customerCode;
  /** 客户名称 */
  @ApiModelProperty(name = "customerName",notes = "客户名称", value = "客户名称")
  private String customerName;
  /** 终端编号 */
  @ApiModelProperty(name = "terminalCode",notes = "终端编号", value = "终端编号")
  private String terminalCode;
  /** 终端名称 */
  @ApiModelProperty(name = "terminalName",notes = "终端名称", value = "终端名称")
  private String terminalName;
  /** 申请金额 */
  @ApiModelProperty(name = "applyAmount",notes = "申请金额", value = "申请金额")
  private BigDecimal applyAmount;
  /** 支付方式 */
  @ApiModelProperty(name = "payType",notes = "支付方式", value = "支付方式")
  private String payType;
  /** 支付方式名称 */
  @ApiModelProperty(name = "payTypeName",notes = "支付方式名称", value = "支付方式名称")
  private String payTypeName;
  /** 预算科目编码 */
  @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编码", value = "预算科目编码")
  private String budgetSubjectsCode;
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value = "预算科目名称")
  private String budgetSubjectsName;
  /** 费用日期(年月) */
  @ApiModelProperty(name = "feeDate",notes = "费用日期(年月)", value = "费用日期(年月)")
  private String feeDate;
  /** 备注 */
  @ApiModelProperty(name = "remark",notes = "备注", value = "备注")
  private String remark;
  /** 是否关闭 */
  @ApiModelProperty(name = "isClose",notes = "是否关闭", value = "是否关闭")
  private String isClose;
  /** 客户编号集合 */
  @ApiModelProperty(name = "customerCodes",notes = "客户编号集合", value = "客户编号集合")
  private Set<String> customerCodes;
  /** 需排除的活动细类编号集合 */
  @ApiModelProperty(name = "excludeCostTypeDetailCodes", notes = "需排除的活动细类编号集合", value = "需排除的活动细类编号集合")
  private Set<String> excludeCostTypeDetailCodes;
  /** 需排除的活动明细编号集合 */
  @ApiModelProperty(name = "excludeItemCodes", notes = "需排除的活动明细编号集合", value = "需排除的活动明细编号集合")
  private Set<String> excludeItemCodes;
  /** 是否推送sfa */
  @ApiModelProperty(name = "isSendSfa",notes = "是否推送sfa", value = "是否推送sfa")
  private String isSendSfa;
  /** 组织编码集合 */
  @ApiModelProperty(name = "orgCodes",notes = "组织编码集合", value = "组织编码集合")
  private Set<String> orgCodes;
  /** 是否有效 */
  @ApiModelProperty(name = "isValid",notes = "是否有效", value = "是否有效")
  private String isValid;
  /** 是否执行 */
  @ApiModelProperty(name = "isExecute",notes = "是否执行", value= "是否执行")
  private String isExecute;
  /** 是否完全核销 */
  @ApiModelProperty(name = "isFullAudit",notes = "是否完全核销", value= "是否完全核销")
  private String isFullAudit;
  /**方案编号 冗余字段*/
  @ApiModelProperty(name = "schemeCode" ,value = "方案编号")
  private String schemeCode;
}
