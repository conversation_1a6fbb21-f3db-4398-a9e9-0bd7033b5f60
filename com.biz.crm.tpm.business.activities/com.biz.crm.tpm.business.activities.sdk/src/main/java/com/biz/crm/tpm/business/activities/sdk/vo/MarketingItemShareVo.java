package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/12 17:07
 **/
@Data
@ApiModel("品相分摊报表")
public class MarketingItemShareVo extends TenantFlagOpVo implements Serializable {

    @ApiModelProperty("费用使用部门")
    private String belongDepartmentCode;

    @ApiModelProperty("费用使用部门")
    private String belongDepartmentName;

    @ApiModelProperty("使用部门编码")
    private String orgCode;

    @ApiModelProperty("使用部门名称")
    private String orgName;

    @ApiModelProperty("客户")
    private String customerCode;

    @ApiModelProperty("客户")
    private String customerName;

    @ApiModelProperty("品相")
    private String itemCode;

    @ApiModelProperty("品相")
    private String itemName;

    @ApiModelProperty("分摊品相")
    private String shareItemCode;

    @ApiModelProperty("分摊品相")
    private String shareItemName;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("活动年月")
    private String actYears;

    @ApiModelProperty("活动开始时间")
    private String startDate;

    @ApiModelProperty("活动结束时间")
    private String endDate;

    @ApiModelProperty("预算年月")
    private String years;

    @ApiModelProperty("活动大类")
    private String categoryCode;

    @ApiModelProperty("活动大类")
    private String categoryName;

    @ApiModelProperty("费用项目")
    private String detailCode;

    @ApiModelProperty("费用项目")
    private String detailName;

    @ApiModelProperty("科目")
    private String budgetSubjectCode;

    @ApiModelProperty("科目")
    private String budgetSubjectName;

    @ApiModelProperty("分摊操作年月")
    private String shareOperationYears;

    @ApiModelProperty("是否确认")
    private String confirmed;

    @ApiModelProperty("费用归属部门成本中心")
    private String belongCostCenterCodes;

    @ApiModelProperty("销售数量")
    private BigDecimal salesQuantity;

    @ApiModelProperty("收入")
    private BigDecimal incomeCost;

    @ApiModelProperty("分摊费用")
    private BigDecimal shareCost;

    @ApiModelProperty("申请费用")
    private BigDecimal applyAmount;

    @ApiModelProperty("计提金额")
    private BigDecimal withholdingAmount;

    @ApiModelProperty("结案金额")
    private BigDecimal endCaseAmount;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;

    @ApiModelProperty("是否需要分摊")
    private String shareFlag;

}
