package com.biz.crm.tpm.business.activities.sdk.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 活动状态枚举
 */
public enum ActivityStatusEnum {

  UNEXECUTED("unexecuted","待执行"),
  EXECUTING("executing","执行中"),
  ENDED("ended","已结束"),
  PARTIAL_CLOSE("partialClose","部分关闭"),
  ALL_CLOSE("allClose","全部关闭");

  @EnumValue
  @JsonValue
  private String code;

  private String des;

  ActivityStatusEnum(String code, String des) {
    this.code = code;
    this.des = des;
  }

  public String getCode() {
    return code;
  }

  public String getDes() {
    return des;
  }

  /**
   * 根据code转枚举
   */
  public static ActivityStatusEnum codeToEnum(String code) {
    ActivityStatusEnum activityStatus = null;
    for (ActivityStatusEnum activityStatusEnum : ActivityStatusEnum.values()) {
      if (activityStatusEnum.code.equals(code)) {
        activityStatus = activityStatusEnum;
      }
    }
    return activityStatus;
  }
}
