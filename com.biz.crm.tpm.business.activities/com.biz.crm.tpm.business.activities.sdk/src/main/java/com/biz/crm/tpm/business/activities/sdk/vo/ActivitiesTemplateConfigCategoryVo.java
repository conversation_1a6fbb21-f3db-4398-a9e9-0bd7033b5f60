package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ActivitiesTemplateConfigCategory", description = "活动模板配置关联活动大类")
public class ActivitiesTemplateConfigCategoryVo extends TenantFlagOpVo {


    /**
     * 配置编码
     */
    @ApiModelProperty("配置编码")
    private String configCode;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;
}
