package com.biz.crm.tpm.business.activities.sdk.register;

import com.biz.crm.tpm.business.activities.sdk.strategy.activityType.ActivityTypeStrategy;

import java.util.Collection;

/**
 * 活动策略
 * <AUTHOR> rentao
 * @date : 2022/10/30 21:59
 */
public interface ActivitiesTemplateRegister {

  /**
   * 表单类型策略
   * 默认实现注册：
   * 1、ApplyActivityTypeStrategy-申请
   * 2、ExecuteActivityTypeStrategy-执行
   * 3、AuditActivityTypeStrategy-核销
   *
   *
   * @return
   */
  Collection<Class<? extends ActivityTypeStrategy>> getActivityTypeStrategy();
}
