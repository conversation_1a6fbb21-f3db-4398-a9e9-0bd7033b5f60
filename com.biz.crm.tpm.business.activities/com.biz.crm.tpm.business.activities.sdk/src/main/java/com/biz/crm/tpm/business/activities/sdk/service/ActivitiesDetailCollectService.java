package com.biz.crm.tpm.business.activities.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailCollectDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 活动明细采集信息表(SFA中的订单);(tpm_activities_detail_collect)表服务接口
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
public interface ActivitiesDetailCollectService{

  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  Page<ActivitiesDetailCollectVo> findByConditions(Pageable pageable, ActivitiesDetailCollectDto dto);
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  ActivitiesDetailCollectVo findById(String id);
  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<ActivitiesDetailCollectVo> findByIds(Collection<String> ids);
  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCode
   * @return 单条数据
   */
  List<ActivitiesDetailCollectVo> findByActivitiesCode(String activitiesCode);
  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCodes
   * @return 多条数据
   */
  List<ActivitiesDetailCollectVo> findByActivitiesCodes(Collection<String> activitiesCodes);
  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCode
   * @return 单条数据
   */
  List<ActivitiesDetailCollectVo> findByActivitiesDetailCode(String activitiesDetailCode);
  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCodes
   * @return 多条数据
   */
  List<ActivitiesDetailCollectVo> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes);
  /**
   * 新增数据
   *
   * @param activitiesDetailCollectDto 实体对象
   * @return 新增结果
   */
  ActivitiesDetailCollectVo create(ActivitiesDetailCollectDto activitiesDetailCollectDto);
  /**
   * 批量新增
   * @param activitiesDetailCollectDtos
   * @return
   */
  List<ActivitiesDetailCollectVo> createBatch(Collection<ActivitiesDetailCollectDto> activitiesDetailCollectDtos);
  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);

}
