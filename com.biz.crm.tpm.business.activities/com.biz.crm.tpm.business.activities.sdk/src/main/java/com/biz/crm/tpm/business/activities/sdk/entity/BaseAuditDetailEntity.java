package com.biz.crm.tpm.business.activities.sdk.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.business.common.local.entity.UuidOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：核销明细表单基础entity
 * <AUTHOR> jerry7
 * @date : 2022-11-11
 */
@ApiModel(value = "BaseAuditDetailEntity",description = "核销明细表单基础entity")
@Getter
@Setter
public abstract class BaseAuditDetailEntity extends TenantFlagOpEntity {
  
  /** 业务编码 */
  @ApiModelProperty(name = "parentCode",notes = "业务编码", value= "业务编码")
  @Column(name = "parent_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '业务编码 '")
  private String parentCode;

  @ApiModelProperty("表单编码")
  @Column(name = "dynamic_key", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '表单编码 '")
  private String dynamicKey;
  
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value= "活动编号")
  @Column(name = "activities_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动编号 '")
  private String activitiesCode;
  
  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value= "活动名称")
  @Column(name = "activities_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动名称 '")
  private String activitiesName;

  /** 活动细类编码 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编码", value= "活动细类编码")
  @Column(name = "cost_type_detail_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动细类编码 '")
  private String costTypeDetailCode;
  
  /** 活动细类名称 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value= "活动细类名称")
  @Column(name = "cost_type_detail_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动细类名称 '")
  private String costTypeDetailName;
  
  /** 活动大类编码 */
  @ApiModelProperty(name = "costTypeCategoryCode",notes = "活动大类名称", value= "活动大类名称")
  @Column(name = "cost_type_category_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动大类编码 '")
  private String costTypeCategoryCode;
  
  /** 活动大类名称 */
  @ApiModelProperty(name = "costTypeCategoryName",notes = "活动大类名称", value= "活动大类名称")
  @Column(name = "cost_type_category_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动大类名称 '")
  private String costTypeCategoryName;
  
  /** 组织编码 */
  @ApiModelProperty(name = "orgCode",notes = "组织编码", value= "组织编码")
  @Column(name = "org_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '组织编码 '")
  private String orgCode;
  
  /** 组织名称 */
  @ApiModelProperty(name = "orgName",notes = "组织名称", value= "组织名称")
  @Column(name = "org_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '组织名称 '")
  private String orgName;
  
  /** 支付方式 */
  @ApiModelProperty(name = "payBy",notes = "支付方式", value= "支付方式")
  @Column(name = "pay_by", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '支付方式 '")
  private String payBy;
  
  /** 支付方式名称 */
  @ApiModelProperty(name = "payByName",notes = "支付方式名称", value= "支付方式名称")
  @Column(name = "pay_by_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '支付方式名称 '")
  private String payByName;
  
  /** 申请金额 */
  @ApiModelProperty(name = "applyAmount",notes = "申请金额", value= "申请金额")
  @Column(name = "apply_amount", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '申请金额 '")
  private BigDecimal applyAmount;

  /** 已核销金额 */
  @ApiModelProperty(name = "auditedAmount",notes = "已核销金额", value= "已核销金额")
  @Column(name = "audited_amount", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '已核销金额 '")
  private BigDecimal auditedAmount;
  
  /** 核销金额 */
  @ApiModelProperty(name = "auditAmount",notes = "本次核销金额", value= "本次核销金额")
  @Column(name = "audit_amount", nullable = true, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '核销金额 '")
  private BigDecimal auditAmount;

  /** 活动明细编码 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编码", value= "活动明细编码")
  @Column(name = "activities_detail_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '活动明细编码 '")
  private String activitiesDetailCode;
  
  /** 核销明细编码 */
  @ApiModelProperty(name = "auditDetailCode",notes = "核销明细编码", value= "核销明细编码")
  @Column(name = "audit_detail_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '核销明细编码 '")
  private String auditDetailCode;
  
  /** 费用预算编码 */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编码", value= "费用预算编码")
  @Column(name = "cost_budget_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '费用预算编码 '")
  private String costBudgetCode;
  
  /** 是否多次核销 */
  @ApiModelProperty(name = "isMultipleAudit",notes = "是否多次核销", value= "是否多次核销")
  @Column(name = "is_multiple_audit", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '是否多次核销 '")
  private String isMultipleAudit;

}