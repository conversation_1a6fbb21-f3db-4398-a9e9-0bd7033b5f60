package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-25 14:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MarketingPlanCaseExecuteVo", description = "活动执行采集Vo")
public class MarketingPlanCaseExecuteVo extends UuidFlagOpVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    private String actExecuteCode;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("终端编码")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("终端类型")
    private String terminalType;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("执行描述")
    private String executeDesc;

    @ApiModelProperty("采集执行状态 Y,N")
    private String executeStatus;

    @ApiModelProperty("活动采集示例")
    private List<ExecuteStandVo> executeStandList;
}
