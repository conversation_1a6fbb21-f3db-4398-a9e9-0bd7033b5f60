package com.biz.crm.tpm.business.activities.sdk.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel(value = "ActivitiesShareDto", description = "动态表单分摊需要的参数dto")
public class ActivitiesShareDto {

  @ApiModelProperty(name = "startTime", notes = "活动开始时间", value = "活动开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  @ApiModelProperty(name = "endTime", notes = "活动结束时间", value = "活动结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  @ApiModelProperty(name = "dynamicFormCode", notes = "动态表单编码", value = "动态表单编码")
  private String dynamicFormCode;

  @ApiModelProperty(name = "dynamicForm", notes = "动态表单明细项数据", value = "动态表单明细项数据")
  private JSONObject dynamicForm;
}
