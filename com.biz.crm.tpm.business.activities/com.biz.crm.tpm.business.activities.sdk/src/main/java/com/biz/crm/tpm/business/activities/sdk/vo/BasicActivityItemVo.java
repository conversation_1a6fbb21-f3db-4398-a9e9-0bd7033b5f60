package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "BasicActivityItemVo", description = "基础-主活动明细vo")
public class BasicActivityItemVo extends BaseActivityItemVo{
  @ApiModelProperty("活动大类编码")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  private String costTypeCategoryName;

  @ApiModelProperty("费用预算编码")
  private String costBudgetCode;

  @ApiModelProperty("活动细类编码")
  private String costTypeDetailCode;

  @ApiModelProperty("活动细类名称")
  private String costTypeDetailName;

  @ApiModelProperty(name = "预算科目编码")
  private String budgetSubjectsCode;

  @ApiModelProperty(name = "预算科目名称")
  private String budgetSubjectsName;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("终端编码")
  private String terminalCode;

  @ApiModelProperty("终端名称")
  private String terminalName;

  @ApiModelProperty("申请金额")
  private BigDecimal applyAmount;

  @ApiModelProperty("支付方式")
  private String payType;

  @ApiModelProperty("支付方式名称")
  private String payTypeName;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("费用日期(年月)")
  private String feeDate;

  @ApiModelProperty("活动细类信息")
  private CostTypeDetailVo costTypeDetailVo;
}
