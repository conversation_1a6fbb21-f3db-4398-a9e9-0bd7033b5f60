package com.biz.crm.tpm.business.activities.sdk.dto.log;

import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesTemplateConfigDto;
import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import lombok.Data;

import java.util.List;

/**
 * 日志dto
 * 
 * @author: ya<PERSON><PERSON><PERSON>
 * @date: 2024/05/31 17:37
 */
@Data
public class ActivitiesTemplateConfigLogEventDto implements NebulaEventDto {

    /**
     * 原始
     */
    private ActivitiesTemplateConfigDto original;
    /**
     * 最新
     */
    private ActivitiesTemplateConfigDto newest;

    private List<ActivitiesTemplateConfigDto> newestList;
}
