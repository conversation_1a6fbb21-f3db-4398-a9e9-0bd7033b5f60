package com.biz.crm.tpm.business.activities.sdk.strategy.activityType;

import com.biz.crm.tpm.business.activities.sdk.strategy.activityExecutive.ActivityExecutiveStrategy;

import java.util.Collection;

/**
 * 表单类型策略 申请 执行 核销
 * <AUTHOR> rentao
 * @date : 2022/10/30 20:49
 */
public interface ActivityTypeStrategy {

  /**
   * 表单类型编码 在表单配置页面使用 用于选择表单类型 必须保持整个策略中编码唯一
   */
   String getActivitiesFormTypeCode();

  /**
   * 表单类型名称 在表单配置页面使用 用于选择表单类型回显汉字使用
   */
   String getActivitiesFormTypeName();

  /**
   * 表单类型绑定的执行策略
   * com.biz.crm.tpm.business.activities.sdk.strategy.activityExecutive.ActivityExecutiveStrategy 中的策略信息
   */
  Collection<Class<? extends ActivityExecutiveStrategy>> getBindExecutiveStrategy();

  //todo 此处扩展表单类型的各种需要的方法

}
