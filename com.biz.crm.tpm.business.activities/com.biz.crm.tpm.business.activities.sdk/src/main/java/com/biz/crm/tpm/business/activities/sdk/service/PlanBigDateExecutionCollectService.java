package com.biz.crm.tpm.business.activities.sdk.service;

import com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectVo;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.sdk.service.PlanBigDateExecutionCollectService
 * @description: 大日期回调执行采集
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:11
 */
public interface PlanBigDateExecutionCollectService {

    /**
     * 大日期回调执行采集 新增
     * @param vo
     */
    void create(PlanBigDateExecutionCollectVo vo);


    /**
     * 查询大日期采集详情数据
     * @param dto
     * @return
     */
    default PlanBigDateExecutionCollectVo findDetailByQueryDto(PlanBigDateExecutionCollectVo dto){
        return null;
    }

    /**
     * 根据执行编码查询详情数据
     * @param actExecuteCode
     * @return
     */
    default List<PlanBigDateExecutionCollectVo> findListByActCode(String actExecuteCode){
        return Lists.newArrayList();
    }

    /**
     * 大日期执行 明细查询
     * @param id
     * @return
     */
    default PlanBigDateExecutionCollectVo findDetailById(String id){
        return null;
    }

}
