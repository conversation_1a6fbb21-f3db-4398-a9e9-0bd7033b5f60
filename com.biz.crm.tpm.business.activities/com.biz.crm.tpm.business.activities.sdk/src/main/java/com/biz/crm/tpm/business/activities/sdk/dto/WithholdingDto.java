package com.biz.crm.tpm.business.activities.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/4 14:42
 **/
@Data
@ApiModel("计提dto")
public class WithholdingDto {

    @ApiModelProperty("实际费用")
    private BigDecimal actualAmount;

    @ApiModelProperty("费用归属部门")
    private String belongDepartmentCode;

    @ApiModelProperty("年月")
    private String years;
}
