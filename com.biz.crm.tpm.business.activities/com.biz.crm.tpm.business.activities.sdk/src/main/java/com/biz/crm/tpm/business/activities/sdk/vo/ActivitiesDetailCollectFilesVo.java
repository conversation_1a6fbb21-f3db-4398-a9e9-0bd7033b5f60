package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.FileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Vo：活动明细采集附件信息表(SFA中的订单);
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@ApiModel(value = "ActivitiesDetailCollectFiles",description = "活动明细采集附件信息表(SFA中的订单)")
@Getter
@Setter
public class ActivitiesDetailCollectFilesVo extends FileVo {
  /** 采集编号 */
  @ApiModelProperty(name = "collectCode",notes = "采集编号", value= "采集编号")
  private String collectCode;
  /** 采集请求编号 */
  @ApiModelProperty(name = "collectRequireCode",notes = "采集请求编号", value= "采集请求编号")
  private String collectRequireCode;
  /** 采集请求名称 */
  @ApiModelProperty(name = "collectRequireName",notes = "采集请求名称", value= "采集请求名称")
  private String collectRequireName;
}