package com.biz.crm.tpm.business.activities.sdk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Vo：活动明细订单表(SFA中的订单);
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */
@ApiModel(value = "ActivitiesDetailSerialVo",description = "活动明细流水表(SFA中的订单与协议)")
@Getter
@Setter
public class ActivitiesDetailSerialVo implements Serializable {
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
  private String tenantCode;
  /** 创建时间 */
  @ApiModelProperty(name = "createTime",notes = "创建时间", value= "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value= "活动编号")
  private String activitiesCode;
  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value= "活动名称")
  private String activitiesName;
  /** 活动明细编号 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编号时间", value= "活动明细编号时间")
  private String activitiesDetailCode;
  /** 订单编号 */
  @ApiModelProperty(name = "serialNo",notes = "订单编号", value= "订单编号")
  private String serialNo;
  /** 订单金额 */
  @ApiModelProperty(name = "serialPrice",notes = "订单金额", value= "订单金额")
  private BigDecimal serialPrice;
  /** 订单时间 */
  @ApiModelProperty(name = "serialTime",notes = "订单时间", value= "订单时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date serialTime;
  /** 终端编号 */
  @ApiModelProperty(name = "terminalCode", notes = "终端编号", value = "终端编号")
  private String terminalCode;
  /** 终端名称 */
  @ApiModelProperty(name = "terminalName", notes = "终端名称", value = "终端名称")
  private String terminalName;
  /** 订单客户编号 */
  @ApiModelProperty(name = "customerCode",notes = "订单客户编号", value= "订单客户编号")
  private String customerCode;
  /** 订单客户名称 */
  @ApiModelProperty(name = "customerName",notes = "订单客户名称", value= "订单客户名称")
  private String customerName;
  /** 提交数据批次编号 */
  @ApiModelProperty(name = "btNo",notes = "提交数据批次编号", value= "提交数据批次编号")
  private String btNo;
  /** 类型 */
  @ApiModelProperty(name = "type",notes = "类型：1、商品订单；2、协议订单", value= "类型：1、商品订单；2、协议订单")
  private Integer type;
}
