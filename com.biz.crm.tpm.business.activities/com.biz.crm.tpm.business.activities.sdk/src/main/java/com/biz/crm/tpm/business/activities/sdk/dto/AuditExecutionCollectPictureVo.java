package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.dto.FileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AuditExecutionCollectPictureVo", description = "执行照片")
public class AuditExecutionCollectPictureVo extends FileDto {

    @ApiModelProperty("终端活动执行稽查id")
    private String collectId;

}
