package com.biz.crm.tpm.business.activities.sdk.event;

import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailSerialVo;

/**
 * 活动明细订单表(SFA中的订单);(tpm_activities_detail_serial)相关的事件通知
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
public interface ActivitiesDetailSerialEventListener {

  /**
   * 当活动明细订单表(SFA中的订单)数据被创建时，该事件被触发
   * @param activitiesDetailSerialVo
   */
  void onCreated(ActivitiesDetailSerialVo activitiesDetailSerialVo);
  /**
   * 当活动明细订单表(SFA中的订单)数据被删除时（逻辑删除），该事件被触发
   * @param activitiesDetailSerialVo
   */
  void onDeleted(ActivitiesDetailSerialVo activitiesDetailSerialVo);

}
