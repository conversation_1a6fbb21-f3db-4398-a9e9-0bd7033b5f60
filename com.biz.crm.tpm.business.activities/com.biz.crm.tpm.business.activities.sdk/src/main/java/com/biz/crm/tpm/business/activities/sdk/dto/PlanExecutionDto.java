package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.dto.UuidFlagOpDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class PlanExecutionDto extends UuidFlagOpDto {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("门店编码")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    private String terminalName;

    @ApiModelProperty("终端类型")
    private String terminalType;

    @ApiModelProperty("执行结果")
    private String result;

    @ApiModelProperty("执行人")
    private String executor;

    @ApiModelProperty("执行时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeDate;

    @ApiModelProperty("执行开始时间")
    private String executeStartDate;

    @ApiModelProperty("执行结束时间")
    private String executeEndDate;


    @ApiModelProperty("执行标准")
    private String actStandards;

    @ApiModelProperty("要求执行资料")
    private String requireExecute;

    /****************************SAF步骤所需基础字段*******************************************/

    @ApiModelProperty("执行计划业务编码")
    private String parentCode;

    @ApiModelProperty("步骤业务编码stepCode")
    private String dynamicKey;

    @ApiModelProperty("动态表单全局唯一编码formCode")
    private String dynamicFormCode;

    @ApiModelProperty("位置信息:经度")
    private BigDecimal longitude;

    @ApiModelProperty("位置信息:纬度")
    private BigDecimal latitude;

    @ApiModelProperty("执行照片")
    private List<PlanExecutionPictureDto> pictureList;


    @ApiModelProperty("创建人岗位名称")
    private String createPosName;

    @ApiModelProperty("创建人岗位")
    private String createPosCode;

    @ApiModelProperty("用户账号")
    private String userName;

    @ApiModelProperty("用户名称")
    private String fullName;

    /**
     * 执行时间 yyyy-mm-dd
     */
    @ApiModelProperty("执行时间")
    private String executionTime;

    /**
     * 水印
     */
    @ApiModelProperty("水印")
    private String watermarkStr;

    /**
     * 水印开关
     */

    @ApiModelProperty("水印开关")
    private String watermarkFlag;


    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;


    @ApiModelProperty("人员在岗和考勤信息")
    private String userClockRecord;

    @ApiModelProperty("一级部门编码")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称-全匹配")
    private String departmentOneName;

    @ApiModelProperty("关联方案明细使用部门编码集合")
    private List<String> belongDepartmentCodeList;
}
