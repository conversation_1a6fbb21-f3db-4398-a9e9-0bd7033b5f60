package com.biz.crm.tpm.business.activities.sdk.widget;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.form.sdk.widget.WidgetKey;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 酒类用品下拉选择控件
 */
@Component
public class LiquorSelectWidget implements WidgetKey {

  @Override
  public String widgetCode() {
    return "liquorSelectWidget";
  }

  @Override
  public String widgetName() {
    return "酒类用品下拉选择控件";
  }

  @SuppressWarnings("unchecked")
  @Override
  public Map<String, Object> widgetParam() {
    JSONObject liquorObject = new JSONObject();
    liquorObject.put("浓香型", "highly_flavor");
    liquorObject.put("酱香型", "maotai_flavor");
    liquorObject.put("清香型", "refreshing_flavor");
    
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("valueMapping", liquorObject);

    Map<String , Object> valueMapping = (Map<String , Object>)JSON.parse(jsonObject.toJSONString());
    return valueMapping;
  }
}
