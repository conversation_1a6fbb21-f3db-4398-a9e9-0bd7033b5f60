package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.bizunited.nebula.common.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * 参数传递vo：活动表单配置vo
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:46
 */
@ApiModel(value = "ActivitiesConfigVo",description = "活动表单配置vo")
@Getter
@Setter
public class ActivitiesConfigVo extends TenantFlagOpVo {

  /**
   * 活动配置名称
   */
  @ApiModelProperty(name = "activitiesConfigName", notes = "活动配置名称", value = "活动配置名称")
  private String activitiesConfigName;

  /**
   * 活动配置编号
   */
  @ApiModelProperty(name = "activitiesConfigCode", notes = "活动配置编号", value = "活动配置编号")
  private String activitiesConfigCode;

  /**
   * 活动表单类型名称
   */
  @ApiModelProperty(name = "activitiesFormTypeCode", notes = "活动表单类型编号", value = "活动表单类型编号")
  private String activitiesFormTypeCode;

  /**
   * 活动表单类型名称
   */
  @ApiModelProperty(name = "activitiesFormTypeName", notes = "活动表单类型名称", value = "活动表单类型名称")
  private String activitiesFormTypeName;

  /**
   * 动态表单编号
   */
  @ApiModelProperty(name = "dynamicFormCode", notes = "动态表单编号", value = "动态表单编号")
  private String dynamicFormCode;

  /**
   * 动态表单名称
   */
  @ApiModelProperty(name = "dynamicFormName", notes = "动态表单名称", value = "动态表单名称")
  private String dynamicFormName;

  /**
   * 表单注册器集合
   */
  @ApiModelProperty(name = "activitiesConfigDetails", notes = "表单注册器集合", value = "表单注册器集合")
  private List<ActivitiesConfigDetailVo> activitiesConfigDetails;
}
