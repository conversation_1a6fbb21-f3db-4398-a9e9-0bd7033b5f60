package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class PlanExecutionAggregateVo extends UuidFlagOpVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("门店编码")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    private String terminalName;

    @ApiModelProperty("终端类型")
    private String terminalType;

    @ApiModelProperty("执行标准")
    private String actStandards;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("审核结果")
    private String checkResult;

    private List<ExecutionPictureVo> pictureList;

    @Data
    public static class ExecutionPictureVo {
        @ApiModelProperty("采集id")
        private String collectId;

        @ApiModelProperty("文件唯一识别号")
        private String fileCode;

        @ApiModelProperty(
                name = "originalFileName",
                value = "原始文件名"
        )
        private String originalFileName;

        @ApiModelProperty("执行结果")
        private String result;

        @ApiModelProperty("执行结果")
        private String resultDesc;

        @ApiModelProperty("执行人")
        private String executor;

        @ApiModelProperty("执行时间")
        @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date executeDate;

        @ApiModelProperty("要求执行资料")
        private String requireExecute;

        @ApiModelProperty("执行时间 yyyy-mm-dd")
        private String executionTime;

        @ApiModelProperty("水印")
        private String watermarkStr;

        @ApiModelProperty("水印开关")
        private String watermarkFlag;

    }
}
