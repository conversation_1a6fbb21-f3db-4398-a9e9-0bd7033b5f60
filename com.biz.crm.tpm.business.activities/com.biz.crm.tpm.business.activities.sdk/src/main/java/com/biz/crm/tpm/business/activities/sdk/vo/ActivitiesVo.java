package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.workflow.sdk.vo.ProcessBusinessMappingVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Vo：活动信息表;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-14
 */
@ApiModel(value = "Activities", description = "活动信息表")
@Getter
@Setter
public class ActivitiesVo extends BaseActivityVo implements Serializable {
  private static final long serialVersionUID = -3842249744226619152L;
  /**
   * 主键
   */
  @ApiModelProperty(name = "id", notes = "主键", value = "主键")
  private String id;
  /**
   * 租户编号
   */
  @ApiModelProperty(name = "tenantCode", notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /**
   * 备注
   */
  @ApiModelProperty(name = "remark", notes = "备注", value = "备注")
  private String remark;

  /**
   * 创建人账号
   */
  @ApiModelProperty(name = "createAccount", notes = "创建人账号", value = "创建人账号")
  private String createAccount;
  /**
   * 创建人名称
   */
  @ApiModelProperty(name = "createName", notes = "创建人名称", value = "创建人名称")
  private String createName;
  /**
   * 创建时间
   */
  @ApiModelProperty(name = "createTime", notes = "创建时间", value = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /**
   * 活动标识(标识某一类活动)
   */
  @ApiModelProperty(name = "activityMark", notes = "活动标识(标识某一类活动)", value = "活动标识(标识某一类活动)")
  private String activityMark;
  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesCode", notes = "活动编号", value = "活动编号")
  private String activitiesCode;
  /**
   * 活动名称
   */
  @ApiModelProperty(name = "activitiesName", notes = "活动名称", value = "活动名称")
  private String activitiesName;
  /**
   * 开始时间
   */
  @ApiModelProperty(name = "beginTime", notes = "开始时间", value = "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /**
   * 结束时间
   */
  @ApiModelProperty(name = "endTime", notes = "结束时间", value = "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /**
   * 是否核销
   */
  @ApiModelProperty(name = "isAudit", notes = "是否核销,当活动下的所有活动都核销或者无需核销后", value = "是否核销,当活动下的所有活动都核销或者无需核销后")
  private String isAudit;
  /**
   * 是否关闭
   */
  @ApiModelProperty(name = "isClose", notes = "是否关闭", value = "是否关闭")
  private String isClose;
  /**
   * 总申请金额
   */
  @ApiModelProperty(name = "totalApplyAmount", notes = "总申请金额", value = "总申请金额")
  private BigDecimal totalApplyAmount;

  /**
   * 活动明细总数
   */
  @ApiModelProperty(name = "detailNum", notes = "核销明细总数", value = "核销明细总数")
  private Integer detailNum;

  /**
   * 待核销明细数量
   */
  @ApiModelProperty(name = "waitAuditDetailNum", notes = "待核销明细数量", value = "待核销明细数量")
  private Integer waitAuditDetailNum;

  /**
   * 自动核销数量
   */
  @ApiModelProperty(name = "autoAuditDetailNum", notes = "自动核销数量", value = "自动核销数量")
  private Integer autoAuditDetailNum;

  @ApiModelProperty(name = "明细信息")
  Map<String, List<BasicActivityItemVo>> items;

  /**
   * 活动明细数据
   */
  @ApiModelProperty(name = "activitiesDetails", notes = "活动明细数据", value = "活动明细数据")
  private List<ActivitiesDetailVo> activitiesDetails;

  @ApiModelProperty("工作流对象")
  private ProcessBusinessMappingVo processBusinessMappingVo;
}
