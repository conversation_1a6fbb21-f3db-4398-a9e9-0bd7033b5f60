package com.biz.crm.tpm.business.activities.sdk.strategy.validate;

import com.biz.crm.common.form.sdk.field.ValidateStrategy;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;

@Component
public class BigDecimalGtZeroValidateStrategy implements ValidateStrategy {
  @Override
  public void validate(Object fieldValue, String fieldName, Field field, Object dynamicForm) {
    Validate.notBlank(fieldName,"BigDecimal类型的字段名称不能为空，请检查!!");
    Validate.notNull(fieldValue,"%s不能为空，请检查!!" , fieldName);
    if(!(fieldValue instanceof BigDecimal)){
      return;
    }
    BigDecimal value = (BigDecimal) fieldValue;
    Validate.isTrue(value.compareTo(BigDecimal.ZERO) > 0,"%s必须大于0，请检查!!" , fieldName);
  }
}
