package com.biz.crm.tpm.business.activities.sdk.event;

import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;

import java.util.Collection;

/**
 * 活动明细信息表;(tpm_activities_detail)相关的事件通知
 * <AUTHOR> Keller
 * @date : 2022-6-29
 */
public interface ActivitiesDetailEventListener{

  /**
   * 当活动明细信息表数据被创建时，该事件被触发
   * @param activitiesDetailVo
   */
  void onCreated(ActivitiesDetailVo activitiesDetailVo);
  /**
   * 当活动明细信息表数据被修改时，该事件被触发
   * @param oldActivitiesDetailVo 修改前数据
   * @param activitiesDetailVo  修改后数据
   */
  void onUpdate(ActivitiesDetailVo oldActivitiesDetailVo, ActivitiesDetailVo activitiesDetailVo);
  /**
   * 当活动明细信息表数据被删除时（逻辑删除），该事件被触发
   * @param activitiesDetailVo
   */
  void onDeleted(ActivitiesDetailVo activitiesDetailVo);
  /**
   * 活动明细关闭
   * @param activitiesDetailVos
   */
  void onClosed(Collection<ActivitiesDetailVo> activitiesDetailVos);

}