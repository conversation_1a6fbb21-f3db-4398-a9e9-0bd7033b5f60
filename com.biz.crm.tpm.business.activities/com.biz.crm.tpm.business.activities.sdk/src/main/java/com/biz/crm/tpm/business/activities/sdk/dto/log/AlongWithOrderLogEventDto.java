package com.biz.crm.tpm.business.activities.sdk.dto.log;

import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDto;
import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import lombok.Data;

import java.util.List;

/**
 * 日志dto
 * 
 * @author: ya<PERSON><PERSON><PERSON>
 * @date: 2024/06/03 17:57
 */
@Data
public class AlongWithOrderLogEventDto implements NebulaEventDto {

    /**
     * 原始
     */
    private AlongWithOrderDto original;
    /**
     * 最新
     */
    private AlongWithOrderDto newest;

    private List<AlongWithOrderDto> newestList;
}
