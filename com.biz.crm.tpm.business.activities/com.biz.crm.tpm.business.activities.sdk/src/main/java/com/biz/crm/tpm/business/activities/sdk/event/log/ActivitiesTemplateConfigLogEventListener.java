package com.biz.crm.tpm.business.activities.sdk.event.log;

import com.biz.crm.tpm.business.activities.sdk.dto.log.ActivitiesTemplateConfigLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

public interface ActivitiesTemplateConfigLogEventListener extends NebulaEvent {

    /**
     * 创建事件
     *
     * @param eventDto
     */
    void onCreate(ActivitiesTemplateConfigLogEventDto eventDto);
    /**
     * 删除事件
     *
     * @param eventDto
     */
    void onDelete(ActivitiesTemplateConfigLogEventDto eventDto);
    /**
     * 更新日志
     *
     * @param eventDto
     */
    void onUpdate(ActivitiesTemplateConfigLogEventDto eventDto);
}
