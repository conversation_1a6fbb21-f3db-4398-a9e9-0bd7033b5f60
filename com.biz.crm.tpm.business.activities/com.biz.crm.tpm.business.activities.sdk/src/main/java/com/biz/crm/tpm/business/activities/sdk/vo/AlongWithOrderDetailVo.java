package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "AlongWithOrderDetailVo", description = "随单比例管控明细")
@Data
public class AlongWithOrderDetailVo extends TenantFlagOpVo {


    @ApiModelProperty("随单比例编号")
    private String alongWithOrderCode;

    @ApiModelProperty("部门编码")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("部门层级")
    private Integer levelNum;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("比例")
    private String ratioStr;

    @ApiModelProperty("比例")
    private BigDecimal ratio;

    /**
     * 是否选中，0否1是
     */
    @ApiModelProperty("是否选中，0否1是")
    private String checked;

    List<AlongWithOrderDetailVo> cacheList;
}
