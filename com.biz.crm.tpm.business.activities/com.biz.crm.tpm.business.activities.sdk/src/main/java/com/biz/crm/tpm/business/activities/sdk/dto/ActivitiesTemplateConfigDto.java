package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ActivitiesTemplateConfigDto", description = "活动模板配置dto")
public class ActivitiesTemplateConfigDto extends TenantFlagOpDto {

    /**
     * 配置编码
     */
    @ApiModelProperty("配置编码")
    private String configCode;

    /**
     * 配置名称
     */
    @ApiModelProperty("配置名称")
    private String configName;

    /**
     * 配置表类型
     */
    @ApiModelProperty("配置表类型")
    private String type;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    /**
     * 明细列表
     */
    @ApiModelProperty("明细列表")
    private List<ActivitiesTemplateConfigDetailDto> details;

    /**
     * 活动大类列表
     */
    @ApiModelProperty("活动大类列表")
    private List<ActivitiesTemplateConfigCategoryDto> categoryList;
}
