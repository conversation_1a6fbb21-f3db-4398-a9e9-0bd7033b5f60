package com.biz.crm.tpm.business.activities.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/4 14:23
 **/
@Data
@ApiModel("销售计划dto")
public class MarketingSalesPlanDto {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("原方案编码")
    private String originalSchemeCode;

    @ApiModelProperty(value = "销售计划编码")
    private String code;

    @ApiModelProperty(value = "年月")
    private String years;

    @ApiModelProperty(value = "成本中心编码")
    private String costCenterCode;

    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("品项")
    private String itemCode;

    @ApiModelProperty("品项")
    private String itemName;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("销售价格")
    private BigDecimal salesPrice;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("销售单位")
    private String saleUnit;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("成本价")
    private BigDecimal costPrice;

    @ApiModelProperty("毛利率")
    private String grossProfitRate;

    @ApiModelProperty("预估销售金额(未税)")
    private BigDecimal estimatedCost;

    @ApiModelProperty("含税预估销售金额")
    private BigDecimal taxEstimatedCost;

    @ApiModelProperty("预估销售额额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("产品运输方式")
    private String transportType;

    @ApiModelProperty("关键字段,customerCode:costCenterCode:years")
    private String indexKey;

    @ApiModelProperty("唯一字段")
    private String onlyKey;

    @ApiModelProperty("成本中心对应组织编码")
    private String orgCode;

    @ApiModelProperty("成本中心对应组织名称")
    private String orgName;

    @ApiModelProperty("创建人职位编码")
    private String positionCode;

    @ApiModelProperty("创建人职位名称")
    private String positionName;

    @ApiModelProperty("错误信息")
    private String errMsg;

    @ApiModelProperty("校验结果")
    private Boolean checkFlag;

    @ApiModelProperty("转换系数")
    private BigDecimal conversionValue;

    @ApiModelProperty("排序")
    private Long sort;

    @ApiModelProperty("成本中心的公司代码")
    private String costCenterCompanyCode;

    private String checked;

    private String costCenterOrgCode;

    private String costCenterOrgName;
}
