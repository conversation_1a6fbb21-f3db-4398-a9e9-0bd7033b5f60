package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.vo.FileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @className: com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectDetailPictureVo
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PlanBigDateExecutionCollectDetailPictureVo", description = "采集照片")
public class PlanBigDateExecutionCollectDetailPictureVo extends FileVo {

    @ApiModelProperty("大日期回调采集id")
    private String collectId;

    @ApiModelProperty("产品父id")
    private String detailId;
}
