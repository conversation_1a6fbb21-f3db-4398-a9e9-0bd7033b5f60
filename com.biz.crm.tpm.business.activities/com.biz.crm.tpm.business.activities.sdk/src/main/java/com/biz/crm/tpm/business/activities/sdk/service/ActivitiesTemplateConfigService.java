package com.biz.crm.tpm.business.activities.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesTemplateConfigDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * 活动模板配置
 */
public interface ActivitiesTemplateConfigService {

    /**
     * 分页查询数据
     *
     * @param pageable        分页对象
     * @param dto 实体对象
     */
    Page<ActivitiesTemplateConfigVo> findByConditions(Pageable pageable, ActivitiesTemplateConfigDto dto);

    /**
     * 通过编码查询单条数据
     *
     * @param configCode 编码
     * @return 单条数据
     */
    ActivitiesTemplateConfigVo findByCode(String configCode);

    /**
     * 新增数据
     *
     * @param dto dto对象
     * @return 新增结果
     */
    void create(ActivitiesTemplateConfigDto dto);

    /**
     * 修改新据
     *
     * @param dto dto对象
     * @return 修改结果
     */
    void update(ActivitiesTemplateConfigDto dto);

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    void delete(List<String> idList);

    /**
     * 通过编码列表查询多条数据
     *
     * @param configCodes 编码
     * @return 多条数据
     */
    List<ActivitiesTemplateConfigVo> findByCodes(Set<String> configCodes);
}
