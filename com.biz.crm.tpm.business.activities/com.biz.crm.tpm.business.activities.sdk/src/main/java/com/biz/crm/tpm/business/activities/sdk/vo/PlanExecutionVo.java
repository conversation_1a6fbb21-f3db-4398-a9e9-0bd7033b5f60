package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class PlanExecutionVo extends UuidFlagOpVo {

    @ApiModelProperty("执行照片")
    List<PlanExecutionPictureVo> pictureList;
    @ApiModelProperty("方案编码")
    private String schemeCode;
    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;
    @ApiModelProperty("门店编码")
    private String terminalCode;
    @ApiModelProperty("门店名称")
    private String terminalName;
    @ApiModelProperty("终端类型")
    private String terminalType;
    @ApiModelProperty("执行结果")
    private String result;
    @ApiModelProperty("执行结果")
    private String resultDesc;
    @ApiModelProperty("执行人")
    private String executor;
    @ApiModelProperty("执行时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeDate;
    @ApiModelProperty("执行标准")
    private String actStandards;
    @ApiModelProperty("要求执行资料")
    private String requireExecute;
    /****************************SAF步骤所需基础字段*******************************************/

    @ApiModelProperty("执行计划业务编码")
    private String parentCode;

    @ApiModelProperty("步骤业务编码stepCode")
    private String dynamicKey;

    @ApiModelProperty("动态表单全局唯一编码formCode")
    private String dynamicFormCode;

    @ApiModelProperty("位置信息:经度")
    private BigDecimal longitude;

    @ApiModelProperty("位置信息:纬度")
    private BigDecimal latitude;

    @ApiModelProperty("创建人岗位名称")
    private String createPosName;

    @ApiModelProperty("创建人岗位")
    private String createPosCode;

    @ApiModelProperty("用户账号")
    private String userName;

    @ApiModelProperty("用户编码")
    private String userCode;

    @ApiModelProperty("用户名称")
    private String fullName;
    /**
     * 执行时间 yyyy-mm-dd
     */
    @ApiModelProperty("执行时间")
    private String executionTime;

    /**
     * 水印
     */
    @ApiModelProperty("水印")
    private String watermarkStr;

    /**
     * 水印开关
     */

    @ApiModelProperty("水印开关")
    private String watermarkFlag;

    /**
     * 图片url
     */
    @ApiModelProperty("图片url")
    private String picUrl;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("人员在岗和考勤信息")
    private String userClockRecord;

    @ApiModelProperty("一级部门编码")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称")
    private String departmentOneName;
}
