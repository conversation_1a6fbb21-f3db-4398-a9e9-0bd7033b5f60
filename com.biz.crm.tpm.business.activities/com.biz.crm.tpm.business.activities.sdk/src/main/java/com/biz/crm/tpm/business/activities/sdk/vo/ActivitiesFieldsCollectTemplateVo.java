package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ActivitiesFieldsCollectTemplateVo", description = "活动采集字段模板vo")
public class ActivitiesFieldsCollectTemplateVo extends TenantFlagOpVo {
  @ApiModelProperty("活动采集字段编码")
  private String code;

  @ApiModelProperty("关联的活动表单标识")
  private String dynamicFormCode;

  @ApiModelProperty("关联的活动明细编码")
  private String activityDetailCode;

  @ApiModelProperty("批次号")
  private String btNo;

  @ApiModelProperty("字段采集模板")
  private Map<String, BaseActivityItemVo> items;
}
