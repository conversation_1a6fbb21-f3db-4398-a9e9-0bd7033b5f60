package com.biz.crm.tpm.business.activities.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MarketPlanChangeFieldsEnum {
    customerName("customerName"),
    planIncome("planIncome"),
    planRatio("planRatio"),
    profitRatio("profitRatio"),
    grossProfitRatio("grossProfitRatio"),
    changePlanIncome("changePlanIncome"),
    changePlanRatio("changePlanRatio"),
    changeProfitRatio("changeProfitRatio"),
    changeGrossProfitRatio("changeGrossProfitRatio"),
    differencePlanIncome("differencePlanIncome"),
    differencePlanRatio("differencePlanRatio"),
    differenceProfitRatio("differenceProfitRatio"),
    differenceGrossProfitRatio("differenceGrossProfitRatio"),
    ;

    private String dictCode;

    public static MarketPlanChangeFieldsEnum findByCode(String code) {
        Optional<MarketPlanChangeFieldsEnum> first = Stream.of(MarketPlanChangeFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}