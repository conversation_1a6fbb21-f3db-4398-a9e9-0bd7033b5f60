package com.biz.crm.tpm.business.activities.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MarketPlanAppendFieldsEnum {
    schemeCode("schemeCode"),
    schemeName("schemeName"),
    schemeDesc("schemeDesc"),
    deptCode("deptCode"),
    title("title"),
    remark("remark"),
    ;

    private String dictCode;

    public static MarketPlanAppendFieldsEnum findByCode(String code) {
        Optional<MarketPlanAppendFieldsEnum> first = Stream.of(MarketPlanAppendFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}