package com.biz.crm.tpm.business.activities.sdk.vo;

import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectImageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.sdk.vo.ExecuteStandVo
 * @description: 活动采集执行示例
 * @author: xiaopeng.zhang
 * @create: 2024-08-14 17:00
 */
@Data
public class ExecuteStandVo {

    /** 采集示例名称 */
    @ApiModelProperty("采集示例名称")
    private String collectName;

    /** 采集示例编号 */
    @ApiModelProperty("采集示例编号")
    private String collectCode;

    @ApiModelProperty("核销采集图片")
    private List<ApprovalCollectImageVo> images;

}
