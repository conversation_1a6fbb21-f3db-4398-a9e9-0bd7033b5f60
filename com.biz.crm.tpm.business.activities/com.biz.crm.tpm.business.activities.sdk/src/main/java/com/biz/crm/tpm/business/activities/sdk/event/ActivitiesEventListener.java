package com.biz.crm.tpm.business.activities.sdk.event;

import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;

import java.util.List;

/**
 * <AUTHOR>
 * @describe: 活动统一事件通知
 * @createTime 2022年06月16日 09:59:00
 */
public interface ActivitiesEventListener {
  /**
   * 当删除时，触发事件
   *
   * @param vos
   * @return
   */
  void onDelete(List<ActivitiesVo> vos);

  /**
   * 当启用时，触发事件
   *
   * @param vos
   * @return
   */
  void onEnable(List<ActivitiesVo> vos);

  /**
   * 当禁用时，触发事件
   *
   * @param vos
   * @return
   */
  void onDisable(List<ActivitiesVo> vos);

  /**
   * 当修改时，触发事件
   *
   * @param oldVo
   * @param newVo
   * @return
   */
  void onChange(ActivitiesVo oldVo, ActivitiesVo newVo);

  /**
   * 审批状态变更，触发事件
   *
   * @param vo
   * @param processStatus 工作流状态
   */
  void onUpdateProcessStatus(ActivitiesVo vo, String processStatus);
}
