package com.biz.crm.tpm.business.activities.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import org.springframework.data.domain.Pageable;

/**
 * @Author: yangrui
 * @Date: 2025-05-08 17:38
 */
public interface MarkingPlanVoService {

    Page<MarketingPlanCaseExecuteVo> findMarketingPlanCaseExecuteList(Pageable pageable, MarketingPlanCaseQueryDto dto);
}
