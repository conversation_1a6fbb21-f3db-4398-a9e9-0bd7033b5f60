package com.biz.crm.tpm.business.activities.sdk.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 参数传递dto：活动明细采集信息表(SFA中的订单);
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@ApiModel(value = "ActivitiesDetailCollect",description = "活动明细采集信息表(SFA中的订单)")
@Getter
@Setter
public class ActivitiesDetailCollectDto implements Serializable,Cloneable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value = "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 创建时间 */
  @ApiModelProperty(name = "createTime",notes = "创建时间", value = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value = "活动编号")
  private String activitiesCode;
  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value = "活动名称")
  private String activitiesName;
  /** 活动明细编号 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编号时间", value = "活动明细编号时间")
  private String activitiesDetailCode;
  /** 描述 */
  @ApiModelProperty(name = "remark",notes = "描述", value = "描述")
  private String remark;
  /** 提交数据批次编号 */
  @ApiModelProperty(name = "btNo",notes = "提交数据批次编号", value= "提交数据批次编号")
  private String btNo;
  /** 采集编号 */
  @ApiModelProperty(name = "collectCode",notes = "采集编号", value= "采集编号")
  private String collectCode;
  /** 创建人名称 */
  @ApiModelProperty(name = "createName",notes = "创建人名称", value= "创建人名称")
  private String createName;
  /** 客户编号 */
  @ApiModelProperty(name = "customerCode", notes = "订单客户编号", value = "订单客户编号")
  private String customerCode;
  /** 客户名称 */
  @ApiModelProperty(name = "customerName", notes = "订单客户名称", value = "订单客户名称")
  private String customerName;
  /** 终端编号 */
  @ApiModelProperty(name = "terminalCode", notes = "终端编号", value = "终端编号")
  private String terminalCode;
  /** 终端名称 */
  @ApiModelProperty(name = "terminalName", notes = "终端名称", value = "终端名称")
  private String terminalName;
  /** 活动采集详情文件 */
  @ApiModelProperty(name = "detailCollectFiles",notes = "活动采集详情文件", value= "活动采集详情文件")
  private List<ActivitiesDetailCollectFilesDto> detailCollectFiles;

}
