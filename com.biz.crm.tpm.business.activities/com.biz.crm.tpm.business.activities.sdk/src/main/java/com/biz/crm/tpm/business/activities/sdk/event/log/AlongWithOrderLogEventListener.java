package com.biz.crm.tpm.business.activities.sdk.event.log;

import com.biz.crm.tpm.business.activities.sdk.dto.log.AlongWithOrderLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

public interface AlongWithOrderLogEventListener extends NebulaEvent {

    /**
     * 创建事件
     *
     * @param dto
     */
    void onCreate(AlongWithOrderLogEventDto dto);
    /**
     * 删除事件
     *
     * @param dto
     */
    void onDelete(AlongWithOrderLogEventDto dto);
    /**
     * 更新日志
     *
     * @param dto
     */
    void onUpdate(AlongWithOrderLogEventDto dto);

    /**
     * 启用
     *
     * @param dto
     */
    void onEnable(AlongWithOrderLogEventDto dto);

    /**
     * 禁用
     *
     * @param dto
     */
    void onDisable(AlongWithOrderLogEventDto dto);
}
