package com.biz.crm.tpm.business.activities.sdk.dto;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AuditExecutionCollectVo", description = "终端活动执行稽查")
public class AuditExecutionCollectVo extends UuidFlagOpVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    private String actExecuteCode;

    @ApiModelProperty("门店编码")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    private String terminalName;

    @ApiModelProperty("终端类型")
    private String terminalType;

    @ApiModelProperty("执行结果")
    private String result;

    @ApiModelProperty("执行人")
    private String executor;

    @ApiModelProperty("执行时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeDate;

    /**
     * 执行时间 yyyy-mm-dd
     */
    @ApiModelProperty("执行时间")
    private String executionTime;


    /****************************SAF步骤所需基础字段*******************************************/

    @ApiModelProperty("执行计划业务编码")
    private String parentCode;

    @ApiModelProperty("步骤业务编码stepCode")
    private String dynamicKey;

    @ApiModelProperty("动态表单全局唯一编码formCode")
    private String dynamicFormCode;

    @ApiModelProperty("位置信息:经度")
    private BigDecimal longitude;

    @ApiModelProperty("位置信息:纬度")
    private BigDecimal latitude;


    //权限相关字段

    @ApiModelProperty("创建人组织")
    private String createOrgCode;

    @ApiModelProperty("创建人组织名称")
    private String createOrgName;

    @ApiModelProperty("创建人职位编码")
    private String createPostCode;

    @ApiModelProperty("创建人职位名称")
    private String createPostName;

    @ApiModelProperty("执行照片")
    private List<AuditExecutionCollectPictureVo> pictureList;

}
