package com.biz.crm.tpm.business.activities.feign.feign;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.internal.ExternalContractFeignImpl;
import com.biz.crm.tpm.business.activities.sdk.vo.ExternalContractSdkVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/10 11:12
 **/
@FeignClient(
        name = "${tpm.feign-client.name:crm-tpm}",
        path = "crm-tpm",
        fallbackFactory = ExternalContractFeignImpl.class)
public interface ExternalContractFeign {

    @ApiOperation(value = "查询合同临近到期的客户信息")
    @GetMapping("/v1/contractCostController/findContractNearDate")
    Result<List<ExternalContractSdkVo>> findContractNearDate();
}
