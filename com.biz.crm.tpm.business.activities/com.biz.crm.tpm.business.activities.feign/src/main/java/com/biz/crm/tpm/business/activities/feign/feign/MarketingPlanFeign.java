package com.biz.crm.tpm.business.activities.feign.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.internal.MarketingPlanFeignImplImpl;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 活动feign
 */
@FeignClient(
        name = "${tpm.feign-client.name:crm-tpm}",
        path = "crm-tpm",
        fallbackFactory = MarketingPlanFeignImplImpl.class)
public interface MarketingPlanFeign {

    @GetMapping("/v1/marketingPlanCaseReportController/countTerminalNum")
    Result<List<String>> countTerminalNum(@RequestParam("years") String years);


    @GetMapping("/v1/marketingPlanController/findMarketingPlanCaseExecuteList")
    @ApiOperation(value = "执行活动分页查询接口")
    Result<Page<MarketingPlanCaseExecuteVo>> findMarketingPlanCaseExecuteList(
            @RequestParam("page") Integer page,
            @RequestParam("size") Integer size,
            @SpringQueryMap MarketingPlanCaseQueryDto vo);
}
