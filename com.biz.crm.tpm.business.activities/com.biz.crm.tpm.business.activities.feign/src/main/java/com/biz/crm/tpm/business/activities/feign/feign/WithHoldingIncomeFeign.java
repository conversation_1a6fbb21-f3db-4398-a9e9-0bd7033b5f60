package com.biz.crm.tpm.business.activities.feign.feign;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.internal.WithHoldingIncomeFeignImpl;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingSalesPlanDto;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingDto;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ManageReportVo;
import com.biz.crm.tpm.business.activities.sdk.vo.WithHoldingIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@FeignClient(
        name = "${tpm.feign-client.name:crm-tpm}",
        path = "crm-tpm",
        fallbackFactory = WithHoldingIncomeFeignImpl.class)
public interface WithHoldingIncomeFeign {

    @PostMapping("/v1/pay/withHoldingIncome/findByYear")
    @ApiOperation("根据年月查询管报收入")
    Result<List<WithHoldingIncomeVo>> findByYears(@RequestBody List<String> years);


    @PostMapping("/v1/pay/WithHolding/findActualReportAmountByYears")
    @ApiOperation("根据年月查询管报实际金额")
    Result<List<WithHoldingIncomeVo>> findActualReportAmountByYears(@RequestBody List<String> years);

    @ApiOperation(value = "查询管报收入列表")
    @PostMapping("/v1/pay/withHoldingIncome/findListByCondition")
    Result<List<WithHoldingIncomeVo>> findListByCondition(@RequestBody WithholdingIncomeQueryDto dto);

    @PostMapping("/v1/budget/costBudgetIncome/findListByOrgCodesAndYears")
    @ApiOperation(value = "通过组织+年月查询预算收入")
    Result<List<CostBudgetIncomeVo>> findListByOrgCodesAndYears(@RequestBody List<String> orgCodes, @RequestParam("years") String years);


    @PostMapping("/v1/budget/costBudgetIncome/findListByOrgCodesAndYearsValid")
    @ApiOperation(value = "通过组织+年月查询预算收入")
    Result<List<CostBudgetIncomeVo>> findListByOrgCodesAndYearsValid(@RequestBody List<String> orgCodes, @RequestParam("years") String years);


    @ApiOperation(value = "查询销售计划-通过组织+年月")
    @PostMapping("/v1/marketingSalesPlanController/findListByOrgCodesAndYears")
    Result<List<MarketingSalesPlanDto>> findListByOrgCodesAndYears(@RequestBody WithholdingIncomeQueryDto dto);

    @ApiOperation(value = "查询预提数据-通过年月+组织编码")
    @PostMapping("/v1/pay/WithHolding/findWithholdingListByYearsAndOrgCodes")
    Result<List<WithholdingDto>> findWithholdingListByYearsAndOrgCodes(@RequestBody WithholdingIncomeQueryDto dto);

    @ApiOperation(value = "查询组织下所有的预算收入+年月")
    @PostMapping("/v1/budget/costBudgetIncome/findListAllOrgCodeChildrenAndYears")
    Result<List<CostBudgetIncomeVo>> findListAllOrgCodeChildrenAndYears(@RequestBody WithholdingIncomeQueryDto dto);

    @ApiOperation(value = "查询组织下所有的预算收入+年月")
    @PostMapping("/v1/budget/costBudgetIncome/findListAllOrgCodeChildrenAndYearsValid")
    Result<List<CostBudgetIncomeVo>> findListAllOrgCodeChildrenAndYearsValid(@RequestBody WithholdingIncomeQueryDto dto);

    @ApiOperation(value = "查询组织下所有的管报收入+年月")
    @PostMapping("/v1/pay/withHoldingIncome/findListByYearsAndChildrenOrgCodes")
    Result<List<WithHoldingIncomeVo>> findListByYearsAndChildrenOrgCodes(@RequestBody WithholdingIncomeQueryDto dto);

    @PostMapping("/v1/marketingPlanController/findCountTerminalByTerminalCodesAndYears")
    @ApiOperation(value = "通过终端编码+年月查询费用规划的终端数(去重)")
    Result<List<String>> findCountTerminalByTerminalCodesAndYears(@RequestBody List<String> terminalCodeList, @RequestParam String years);

    @PostMapping("/v1/manageReport/findManageListByCondition")
    @ApiOperation(value = "通过条件查询")
    Result<List<ManageReportVo>> findManageListByCondition(@RequestBody WithholdingIncomeQueryDto dto);

    @ApiOperation(value = "查询列表-通过组织+年月列表")
    @PostMapping("/v1/budget/costBudget/findListByOrgCodesAndYearsList")
    Result<Map<String, BigDecimal>> findListByOrgCodesAndYearsList(@RequestBody WithholdingIncomeQueryDto dto);

    @ApiOperation(value = "查询列表-通过组织+年月列表")
    @PostMapping("/v1/budget/costBudget/findListByOrgCodesAndYearsListNoTax")
    Result<Map<String, BigDecimal>> findListByOrgCodesAndYearsListNoTax(@RequestBody WithholdingIncomeQueryDto dto);

    @PostMapping("/v1/marketingPlanController/findAmountByOrgCodesAndYearsList")
    @ApiOperation(value = "通过年月+组织查询")
    Result<Map<String, BigDecimal>> findAmountByOrgCodesAndYearsList(@RequestBody WithholdingIncomeQueryDto dto);
}
