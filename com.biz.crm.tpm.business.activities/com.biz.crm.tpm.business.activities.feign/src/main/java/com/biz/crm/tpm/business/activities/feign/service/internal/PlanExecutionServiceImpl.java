package com.biz.crm.tpm.business.activities.feign.service.internal;

import com.biz.crm.tpm.business.activities.feign.feign.PlanExecutionFeign;
import com.biz.crm.tpm.business.activities.sdk.dto.AuditExecutionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanExecutionDto;
import com.biz.crm.tpm.business.activities.sdk.service.PlanExecutionService;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionAggregateVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PlanExecutionServiceImpl implements PlanExecutionService {

    @Autowired(required = false)
    private PlanExecutionFeign planExecutionFeign;

    /**
     * 根据活动明细编码查询活动执行
     *
     * @param code
     * @return
     */
    @Override
    public List<PlanExecutionVo> findByDetailCode(String code) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<PlanExecutionVo> findBySchemeDetailCode(String code) {
        throw new UnsupportedOperationException();
    }

    @Override
    public PlanExecutionAggregateVo findByDetailCodeV1(String code) {
        throw new UnsupportedOperationException();
    }

    /**
     * 创建活动执行
     *
     * @param dto
     */
    @Override
    public void createPlanExecution(PlanExecutionDto dto) {
        planExecutionFeign.createPlanExecution(dto).checkFeignResult();
    }

    /**
     * 修改活动执行
     *
     * @param dto
     */
    @Override
    public void updatePlanExecution(PlanExecutionDto dto) {
        planExecutionFeign.updatePlanExecution(dto).checkFeignResult();
    }

    /**
     * 查询详情
     *
     * @param dto
     */
    @Override
    public PlanExecutionVo findByDynamicKey(PlanExecutionDto dto) {
        return planExecutionFeign.findByDynamicKey(dto).checkFeignResult();
    }

    @Override
    public void createAuditExecutionCollect(AuditExecutionCollectVo vo) {
        this.planExecutionFeign.createAuditExecutionCollect(vo).checkFeignResult();
    }

    @Override
    public List<MarketingPlanCaseExecuteVo> findByTerminalMarketingPlanCaseExecuteList(MarketingPlanCaseQueryDto dto) {
        return this.planExecutionFeign.findByTerminalMarketingPlanCaseExecuteList(dto).checkFeignResult();
    }
}
