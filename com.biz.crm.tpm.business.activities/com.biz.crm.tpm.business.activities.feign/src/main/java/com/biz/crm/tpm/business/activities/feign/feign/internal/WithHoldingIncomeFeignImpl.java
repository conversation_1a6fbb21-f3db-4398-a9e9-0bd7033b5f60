package com.biz.crm.tpm.business.activities.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.WithHoldingIncomeFeign;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingSalesPlanDto;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingDto;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ManageReportVo;
import com.biz.crm.tpm.business.activities.sdk.vo.WithHoldingIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class WithHoldingIncomeFeignImpl implements FallbackFactory<WithHoldingIncomeFeign> {
    @Override
    public WithHoldingIncomeFeign create(Throwable cause) {
        log.error("WithHoldingIncomeFeign", cause);
        return new WithHoldingIncomeFeign() {
            @Override
            public Result<List<WithHoldingIncomeVo>> findByYears(List<String> years) {
                throw new UnsupportedOperationException("根据年月查询管报收入进入熔断");
            }

            @Override
            public Result<List<WithHoldingIncomeVo>> findActualReportAmountByYears(List<String> years) {
                throw new UnsupportedOperationException("根据年月查询管报实际金额进入熔断");
            }

            @Override
            public Result<List<WithHoldingIncomeVo>> findListByCondition(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询管报收入列表进入熔断");
            }

            @Override
            public Result<List<CostBudgetIncomeVo>> findListByOrgCodesAndYears(List<String> orgCodes, String years) {
                throw new UnsupportedOperationException("查询预算收入进入熔断");
            }

            @Override
            public Result<List<CostBudgetIncomeVo>> findListByOrgCodesAndYearsValid(List<String> orgCodes, String years) {
                throw new UnsupportedOperationException("查询预算收入进入熔断");
            }

            @Override
            public Result<List<MarketingSalesPlanDto>> findListByOrgCodesAndYears(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询销售计划进入熔断");
            }

            @Override
            public Result<List<WithholdingDto>> findWithholdingListByYearsAndOrgCodes(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询计提数据进入熔断");
            }

            @Override
            public Result<List<CostBudgetIncomeVo>> findListAllOrgCodeChildrenAndYears(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询预算收入进入熔断!");
            }

            @Override
            public Result<List<CostBudgetIncomeVo>> findListAllOrgCodeChildrenAndYearsValid(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询预算收入进入熔断!");
            }

            @Override
            public Result<List<WithHoldingIncomeVo>> findListByYearsAndChildrenOrgCodes(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询管报收入进入熔断");
            }

            @Override
            public Result<List<String>> findCountTerminalByTerminalCodesAndYears(List<String> terminalCodeList, String years) {
                throw new UnsupportedOperationException("查询终端数进入熔断");
            }

            @Override
            public Result<List<ManageReportVo>> findManageListByCondition(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询管报收入台账进入熔断");
            }

            @Override
            public Result<Map<String, BigDecimal>> findListByOrgCodesAndYearsList(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询预算费用进入熔断");
            }

            @Override
            public Result<Map<String, BigDecimal>> findListByOrgCodesAndYearsListNoTax(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询预算费用(未税)进入熔断");
            }

            @Override
            public Result<Map<String, BigDecimal>> findAmountByOrgCodesAndYearsList(WithholdingIncomeQueryDto dto) {
                throw new UnsupportedOperationException("查询方案信息进入熔断");
            }
        };
    }
}

