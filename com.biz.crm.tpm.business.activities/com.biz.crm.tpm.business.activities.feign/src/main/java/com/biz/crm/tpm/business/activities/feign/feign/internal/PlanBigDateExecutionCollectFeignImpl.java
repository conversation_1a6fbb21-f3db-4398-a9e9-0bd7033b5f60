package com.biz.crm.tpm.business.activities.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.PlanBigDateExecutionCollectFeign;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @className: com.biz.crm.tpm.business.activities.feign.feign.internal.PlanBigDateExecutionCollectFeignImpl
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:14
 */
@Component
@Slf4j
public class PlanBigDateExecutionCollectFeignImpl implements FallbackFactory<PlanBigDateExecutionCollectFeign> {
    @Override
    public PlanBigDateExecutionCollectFeign create(Throwable cause) {
        log.error("PlanBigDateExecutionCollectFeign熔断", cause);
        return new PlanBigDateExecutionCollectFeign() {
            @Override
            public Result<?> create(PlanBigDateExecutionCollectVo dto) {
                throw new UnsupportedOperationException("创建大日期执行记录熔断");
            }
        };
    }
}
