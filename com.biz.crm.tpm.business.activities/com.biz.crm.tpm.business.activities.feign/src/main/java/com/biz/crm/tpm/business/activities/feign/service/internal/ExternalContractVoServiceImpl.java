package com.biz.crm.tpm.business.activities.feign.service.internal;

import com.biz.crm.tpm.business.activities.feign.feign.ExternalContractFeign;
import com.biz.crm.tpm.business.activities.sdk.service.ExternalContractVoService;
import com.biz.crm.tpm.business.activities.sdk.vo.ExternalContractSdkVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/10 11:29
 **/
@Service
public class ExternalContractVoServiceImpl implements ExternalContractVoService {

    @Resource
    private ExternalContractFeign externalContractFeign;

    @Override
    public List<ExternalContractSdkVo> findContractNearDate() {
        return externalContractFeign.findContractNearDate().checkFeignResult();
    }
}
