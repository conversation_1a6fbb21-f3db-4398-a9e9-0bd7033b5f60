package com.biz.crm.tpm.business.activities.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.PlanExecutionFeign;
import com.biz.crm.tpm.business.activities.sdk.dto.AuditExecutionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanExecutionDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class PlanExecutionFeignImpl implements FallbackFactory<PlanExecutionFeign> {

    /**
     * Returns an instance of the fallback appropriate for the given cause.
     *
     * @param cause cause of an exception.
     * @return fallback
     */
    @Override
    public PlanExecutionFeign create(Throwable cause) {
        log.error("PlanExecutionFeign熔断", cause);
        return new PlanExecutionFeign() {

            /**
             * 创建活动执行
             *
             * @param dto
             */
            @Override
            public Result<?> createPlanExecution(PlanExecutionDto dto) {
                throw new UnsupportedOperationException("创建活动执行熔断");
            }

            @Override
            public Result<?> createAuditExecutionCollect(AuditExecutionCollectVo dto) {
                throw new UnsupportedOperationException("创建活动稽查熔断");
            }

            /**
             * 修改活动执行
             *
             * @param dto
             */
            @Override
            public Result<?> updatePlanExecution(PlanExecutionDto dto) {
                throw new UnsupportedOperationException("修改活动执行熔断");
            }

            /**
             * 查询详情
             *
             * @param dto
             */
            @Override
            public Result<PlanExecutionVo> findByDynamicKey(PlanExecutionDto dto) {
                throw new UnsupportedOperationException("查询详情熔断");
            }

            @Override
            public Result<List<MarketingPlanCaseExecuteVo>> findByTerminalMarketingPlanCaseExecuteList(MarketingPlanCaseQueryDto dto) {
                throw new UnsupportedOperationException("执行活动查询熔断");
            }
        };
    }
}
