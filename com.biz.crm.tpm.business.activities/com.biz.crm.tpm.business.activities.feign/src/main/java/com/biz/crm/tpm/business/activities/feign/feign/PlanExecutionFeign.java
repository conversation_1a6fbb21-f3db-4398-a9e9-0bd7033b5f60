package com.biz.crm.tpm.business.activities.feign.feign;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.internal.PlanExecutionFeignImpl;
import com.biz.crm.tpm.business.activities.sdk.dto.AuditExecutionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanExecutionDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
        name = "${tpm.feign-client.name:crm-tpm}",
        path = "crm-tpm",
        fallbackFactory = PlanExecutionFeignImpl.class)
public interface PlanExecutionFeign {


    /**
     * 创建活动执行
     */
    @PostMapping("/v1/planExceution/createPlanExecution")
    Result<?> createPlanExecution(@RequestBody PlanExecutionDto dto);

    /**
     * 创建活动稽查
     *
     * @param dto
     * @return
     */
    @PostMapping("/v1/planExceution/createAuditExecutionCollect")
    Result<?> createAuditExecutionCollect(@RequestBody AuditExecutionCollectVo dto);

    /**
     * 修改活动执行
     */
    @PostMapping("/v1/planExceution/updatePlanExecution")
    Result<?> updatePlanExecution(@RequestBody PlanExecutionDto dto);

    /**
     * 查询详情
     */
    @PostMapping("/v1/planExceution/findByDynamicKey")
    Result<PlanExecutionVo> findByDynamicKey(@RequestBody PlanExecutionDto dto);

    /**
     * 执行活动查询接口
     */
    @PostMapping("/v1/marketingPlanController/findByTerminalMarketingPlanCaseExecuteList")
    Result<List<MarketingPlanCaseExecuteVo>> findByTerminalMarketingPlanCaseExecuteList(@RequestBody MarketingPlanCaseQueryDto dto);
}
