package com.biz.crm.tpm.business.activities.feign.service.internal;

import com.biz.crm.tpm.business.activities.feign.feign.PlanBigDateExecutionCollectFeign;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.service.PlanBigDateExecutionCollectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @className: com.biz.crm.tpm.business.activities.feign.service.internal.PlanBigDateExecutionCollectServiceImpl
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:12
 */
@Service
public class PlanBigDateExecutionCollectServiceImpl implements PlanBigDateExecutionCollectService {
    @Autowired(required = false)
    private PlanBigDateExecutionCollectFeign planBigDateExecutionCollectFeign;

    @Override
    public void create(PlanBigDateExecutionCollectVo vo) {
        this.planBigDateExecutionCollectFeign.create(vo).checkFeignResult();
    }
}
