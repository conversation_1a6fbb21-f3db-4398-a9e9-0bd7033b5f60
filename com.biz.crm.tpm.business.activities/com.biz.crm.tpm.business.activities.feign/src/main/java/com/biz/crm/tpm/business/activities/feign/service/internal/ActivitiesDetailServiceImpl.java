package com.biz.crm.tpm.business.activities.feign.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.ActivitiesDetailVoFeign;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailRemoteDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 描述：</br>活动明细sdk feign实现
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@Service
public class ActivitiesDetailServiceImpl implements ActivitiesDetailService {

  @Autowired(required = false)
  private ActivitiesDetailVoFeign activitiesDetailVoFeign;

  @Override
  public Page<ActivitiesDetailVo> findByConditions(Pageable pageable, ActivitiesDetailDto dto) {
    dto.setIsSendSfa(BooleanEnum.TRUE.getCapital());
    dto.setIsClose(BooleanEnum.FALSE.getCapital());
    dto.setIsFullAudit(BooleanEnum.FALSE.getCapital());
    Result<Page<ActivitiesDetailVo>> result = activitiesDetailVoFeign.findByConditions(pageable.getPageNumber(), pageable.getPageSize(), dto);
    return result.getResult();
  }

  @Override
  public ActivitiesDetailVo findById(String id) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<ActivitiesDetailVo> findByIds(Collection<String> ids) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<ActivitiesDetailVo> findByActivitiesCode(String activitiesCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Map<String, List<ActivitiesDetailVo>> findByActivitiesCodes(Collection<String> activitiesCodes) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<ActivitiesDetailVo> findAllByActivitiesCodes(Collection<String> activitiesCodes) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<ActivitiesDetailVo> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes) {
    throw new UnsupportedOperationException();
  }

  @Override
  public ActivitiesDetailVo findByActivitiesDetailCode(String activitesDetailCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public ActivitiesDetailVo findDetailByActivitiesDetailCode(String activitesDetailCode) {
    Result<ActivitiesDetailVo> result = activitiesDetailVoFeign.findDetailByActivitiesDetailCode(activitesDetailCode);
    return result.getResult();
  }

  @Override
  public ActivitiesDetailVo create(ActivitiesDetailDto activitiesDetailDto) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<ActivitiesDetailVo> createBatch(Collection<ActivitiesDetailDto> activitiesDetailDtos) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void closed(Collection<ActivitiesDetailDto> activitiesDetailDtos) {
    throw new UnsupportedOperationException();
  }

  @Override
  public int countByCostTypeCategoryCode(String costTypeCategoryCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void updateOrderAmountByActivitiesCode(String activitiesDetailCode, BigDecimal changeAmount, boolean isAdd) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void updateFullAuditByActivitiesDetailCode(String activitiesDetailCode, String isFullAudit) {
    throw new UnsupportedOperationException();
  }

  @Override
  public ActivitiesDetailVo createRemote(ActivitiesDetailRemoteDto dto) {
    Result<ActivitiesDetailVo> result = activitiesDetailVoFeign.createRemote(dto);
    Validate.isTrue(result.isSuccess(),result.getMessage());
    return result.getResult();
  }

  @Override
  public int countByActivitiesCodeAndClosed(String activitiesCode) {
    throw new UnsupportedOperationException();
  }
}
