package com.biz.crm.tpm.business.activities.feign.feign.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.ActivitiesDetailVoFeign;
import com.biz.crm.tpm.business.activities.feign.feign.MarketingPlanFeign;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class MarketingPlanFeignImplImpl implements FallbackFactory<MarketingPlanFeign> {

    @Override
    public MarketingPlanFeign create(Throwable cause) {
        log.error("ActivitiesDetailVoFeign熔断", cause);
        return new MarketingPlanFeign() {

            @Override
            public Result<List<String>> countTerminalNum(String years) {
                throw new UnsupportedOperationException("统计终端数量进入熔断");
            }

            @Override
            public Result<Page<MarketingPlanCaseExecuteVo>> findMarketingPlanCaseExecuteList(Integer page, Integer size, MarketingPlanCaseQueryDto vo) {
                throw new UnsupportedOperationException("查询终端执行结果进入熔断");
            }


        };
    }
}
