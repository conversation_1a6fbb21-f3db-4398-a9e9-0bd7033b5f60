package com.biz.crm.tpm.business.activities.feign.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.internal.ActivitiesDetailVoFeignImpl;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailRemoteDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 描述：</br>tpm活动明细feign接口类
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@FeignClient(
        name = "${tpm.feign-client.name:crm-tpm}",
        path = "crm-tpm",
        fallbackFactory = ActivitiesDetailVoFeignImpl.class)
public interface ActivitiesDetailVoFeign {

  /**
   * 根据客户编码集合获取对应的客户信息
   *
   * @param page 分页对象
   * @param size 分页对象
   * @param dto      查询实体
   * @return 分页数据
   */
  @GetMapping("/v1/activities/activitiesDetail/findByConditions")
  Result<Page<ActivitiesDetailVo>> findByConditions(@RequestParam("page") Integer page,
                                                    @RequestParam("size") Integer size,
                                                    @SpringQueryMap ActivitiesDetailDto dto);

  /**
   * 根据客户编码集合获取对应的客户信息
   *
   * @param activitiesDetailCode 编号
   * @return 单条数据
   */
  @GetMapping("/v1/activities/activitiesDetail/findDetailByActivitiesDetailCode")
  Result<ActivitiesDetailVo> findDetailByActivitiesDetailCode(@RequestParam("activitiesDetailCode") @ApiParam(name = "ActivitiesDetailCode", value = "编号", required = true) String activitiesDetailCode);

  /**
   * 远程活动信息保存接口(sfa to tpm)
   */
  @PostMapping("/v1/activities/activitiesDetail/createRemote")
  Result<ActivitiesDetailVo> createRemote(@RequestBody @ApiParam(name = "dto", value = "活动信息", required = true) ActivitiesDetailRemoteDto dto);
}
