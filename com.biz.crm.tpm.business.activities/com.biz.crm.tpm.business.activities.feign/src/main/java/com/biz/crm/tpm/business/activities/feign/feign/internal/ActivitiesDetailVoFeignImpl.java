package com.biz.crm.tpm.business.activities.feign.feign.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.ActivitiesDetailVoFeign;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailRemoteDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>tpm活动明细feign熔断实现
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@Component
@Slf4j
public class ActivitiesDetailVoFeignImpl implements FallbackFactory<ActivitiesDetailVoFeign> {
  @Override
  public ActivitiesDetailVoFeign create(Throwable cause) {
    log.error("ActivitiesDetailVoFeign熔断", cause);
    return new ActivitiesDetailVoFeign() {

      @Override
      public Result<Page<ActivitiesDetailVo>> findByConditions(Integer page, Integer size, ActivitiesDetailDto dto) {
        throw new UnsupportedOperationException("查询条件活动明细信息分页熔断");
      }

      @Override
      public Result<ActivitiesDetailVo> findDetailByActivitiesDetailCode(String activitiesDetailCode) {
        throw new UnsupportedOperationException("通过活动明细编号活动明细信息熔断");
      }

      @Override
      public Result<ActivitiesDetailVo> createRemote(ActivitiesDetailRemoteDto dto) {
        throw new UnsupportedOperationException("远程活动信息保存接口熔断");
      }
    };
  }
}
