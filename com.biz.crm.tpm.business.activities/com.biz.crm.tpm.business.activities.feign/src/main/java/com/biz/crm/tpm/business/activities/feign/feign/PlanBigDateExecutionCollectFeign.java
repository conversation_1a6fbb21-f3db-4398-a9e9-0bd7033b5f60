package com.biz.crm.tpm.business.activities.feign.feign;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.internal.PlanBigDateExecutionCollectFeignImpl;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectVo;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @className: com.biz.crm.tpm.business.activities.feign.feign.PlanBigDateExecutionCollectFeign
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:13
 */
@FeignClient(
        name = "${tpm.feign-client.name:crm-tpm}",
        path = "crm-tpm",
        fallbackFactory = PlanBigDateExecutionCollectFeignImpl.class)
public interface PlanBigDateExecutionCollectFeign {

    /**
     * 创建活动执行
     */
    @PostMapping("/v1/planBigDateExecution/create")
    Result<?> create(@RequestBody @ApiParam(name = "dto", value = "活动执行信息") PlanBigDateExecutionCollectVo dto);
}
