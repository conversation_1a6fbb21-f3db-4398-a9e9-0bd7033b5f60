package com.biz.crm.tpm.business.activities.feign.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.feign.feign.MarketingPlanFeign;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.service.MarkingPlanVoService;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: yangrui
 * @Date: 2025-05-08 17:45
 */
@Service
public class MarkingPlanVoServiceImpl implements MarkingPlanVoService {

    @Resource
    private MarketingPlanFeign marketingPlanFeign;

    @Override
    public Page<MarketingPlanCaseExecuteVo> findMarketingPlanCaseExecuteList(Pageable pageable, MarketingPlanCaseQueryDto dto) {
        return marketingPlanFeign.findMarketingPlanCaseExecuteList(pageable.getPageNumber(),
                pageable.getPageSize(), dto).checkFeignResult();
    }
}
