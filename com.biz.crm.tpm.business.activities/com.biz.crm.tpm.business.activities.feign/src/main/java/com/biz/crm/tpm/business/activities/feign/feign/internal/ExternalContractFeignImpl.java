package com.biz.crm.tpm.business.activities.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.feign.feign.ExternalContractFeign;
import com.biz.crm.tpm.business.activities.sdk.vo.ExternalContractSdkVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/10 11:12
 **/
@Component
public class ExternalContractFeignImpl implements FallbackFactory<ExternalContractFeign> {


    @Override
    public ExternalContractFeign create(Throwable cause) {
        return new ExternalContractFeign() {
            @Override
            public Result<List<ExternalContractSdkVo>> findContractNearDate() {
                throw new UnsupportedOperationException("查询外部合同信息进入熔断");
            }
        };
    }
}
