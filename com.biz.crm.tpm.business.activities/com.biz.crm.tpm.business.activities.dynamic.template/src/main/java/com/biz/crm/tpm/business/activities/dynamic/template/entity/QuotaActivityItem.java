package com.biz.crm.tpm.business.activities.dynamic.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.tpm.business.activities.sdk.template.BaseActivityItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_quota_activity_item")
@Table(name = "tpm_quota_activity_item", indexes = {@Index(name = "tpm_quota_activity_item_index1", columnList = "cost_type_detail_code, tenant_code"),
        @Index(name = "tpm_quota_activity_item_index2", columnList = "parent_code, tenant_code")})
@ApiModel(value = "QuotaActivityItem", description = "定额活动动态模板明细")
@org.hibernate.annotations.Table(appliesTo = "tpm_quota_activity_item", comment = "定额活动动态模板明细")
public class QuotaActivityItem extends BaseActivityItem {

  @ApiModelProperty("活动大类编码")
  @TableField(value = "cost_type_category_code")
  @Column(name = "cost_type_category_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动大类编码'")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  @TableField(value = "cost_type_category_name")
  @Column(name = "cost_type_category_name", nullable = false, columnDefinition = "varchar(255) COMMENT '活动大类名称'")
  private String costTypeCategoryName;

  @ApiModelProperty("费用预算编码")
  @TableField(value = "cost_budget_code")
  @Column(name = "cost_budget_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '费用预算编码'")
  private String costBudgetCode;

  @ApiModelProperty("活动细类编码")
  @TableField(value = "cost_type_detail_code")
  @Column(name = "cost_type_detail_code", length = 64, columnDefinition = "varchar(64) COMMENT '活动细类编码'")
  private String costTypeDetailCode;

  @ApiModelProperty("活动细类名称")
  @TableField(value = "cost_type_detail_name")
  @Column(name = "cost_type_detail_name", columnDefinition = "varchar(255) COMMENT '活动细类名称'")
  private String costTypeDetailName;

  @ApiModelProperty(name = "预算科目编码")
  @TableField(value = "budget_subjects_code")
  @Column(name = "budget_subjects_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
  private String budgetSubjectsCode;

  @ApiModelProperty(name = "预算科目名称")
  @TableField(value = "budget_subjects_name")
  @Column(name = "budget_subjects_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;

  @ApiModelProperty("组织编码")
  @TableField(value = "org_code")
  @Column(name = "org_code", length = 64, columnDefinition = "varchar(64) COMMENT '组织编码'")
  private String orgCode;

  @ApiModelProperty("组织名称")
  @TableField(value = "org_name")
  @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
  private String orgName;

  @ApiModelProperty("客户编码")
  @TableField(value = "customer_code")
  @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
  private String customerCode;

  @ApiModelProperty("客户名称")
  @TableField(value = "customer_name")
  @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
  private String customerName;

  @ApiModelProperty("终端编码")
  @TableField(value = "terminal_code")
  @Column(name = "terminal_code", length = 64, columnDefinition = "varchar(64) COMMENT '终端编码'")
  private String terminalCode;

  @ApiModelProperty("终端名称")
  @TableField(value = "terminal_name")
  @Column(name = "terminal_name", columnDefinition = "varchar(255) COMMENT '终端名称'")
  private String terminalName;

  @ApiModelProperty("预估销售额")
  @TableField(value = "estimate_sale_amount")
  @Column(name = "estimate_sale_amount", columnDefinition = "decimal(20,4) COMMENT '预估销售额'")
  private BigDecimal estimateSaleAmount;

  @ApiModelProperty("申请金额")
  @TableField(value = "apply_amount")
  @Column(name = "apply_amount", nullable = false, columnDefinition = "decimal(20,4) COMMENT '申请金额'")
  private BigDecimal applyAmount;

  @ApiModelProperty("支付方式")
  @TableField(value = "pay_type")
  @Column(name = "pay_type", columnDefinition = "varchar(64) COMMENT '支付方式'")
  private String payType;

  @ApiModelProperty("支付方式名称")
  @TableField(value = "pay_type_name")
  @Column(name = "pay_type_name", nullable = false, columnDefinition = "varchar(255) COMMENT '支付方式名称'")
  private String payTypeName;

  @ApiModelProperty("备注")
  @TableField(value = "remark")
  @Column(name = "remark", columnDefinition = "varchar(255) COMMENT '备注'")
  private String remark;

  @ApiModelProperty("费用日期(年月)")
  @TableField(value = "fee_date")
  @Column(name = "fee_date", columnDefinition = "varchar(32) COMMENT '费用日期(年月)'")
  private String feeDate;

  @ApiModelProperty("是否关闭")
  @TableField(value = "colsed")
  @Column(name = "colsed", columnDefinition = "bit(1) COMMENT '是否关闭'")
  private Boolean colsed;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("活动明细商品分摊")
  List<ActivityProductShareInfo> shareInfos;
}
