package com.biz.crm.tpm.business.activities.dynamic.template.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.tpm.business.activities.sdk.template.BaseActivityItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@Entity
@TableName("tpm_general_template")
@Table(name = "tpm_general_template")
@ApiModel(value = "GeneralTemplate", description = "通用采集模板")
@org.hibernate.annotations.Table(appliesTo = "tpm_general_template", comment = "通用采集模板")
public class GeneralTemplate extends BaseActivityItem {

  @ApiModelProperty("执行日期")
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "execution_date" , length = 20, columnDefinition = "datetime COMMENT '执行日期 '")
  private Date executionDate;

  @ApiModelProperty("执行记录")
  @TableField(value = "execution_record")
  @Column(name = "execution_record", nullable = false, columnDefinition = "varchar(512) COMMENT '执行记录'")
  private String executionRecord;

  @ApiModelProperty("是否正常")
  @TableField(value = "normal")
  @Column(name = "normal", columnDefinition = "bit(1) COMMENT '是否正常'")
  private Integer normal;
}
