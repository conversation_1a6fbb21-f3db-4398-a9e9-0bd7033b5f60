package com.biz.crm.tpm.business.activities.dynamic.template.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.ActivityProductShareInfo;
import com.biz.crm.tpm.business.activities.dynamic.template.mapper.ActivityProductShareInfoMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 活动明细商品分摊信息(ActivityProductShareInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class ActivityProductShareInfoRepository  extends ServiceImpl<ActivityProductShareInfoMapper, ActivityProductShareInfo> {

  public List<ActivityProductShareInfo> findByAssociateId(String associateId){
    return this.lambdaQuery()
        .eq(ActivityProductShareInfo::getAssociateId,associateId)
        .eq(ActivityProductShareInfo::getTenantCode, TenantUtils.getTenantCode())
        .orderByAsc(ActivityProductShareInfo::getRatio)
        .list();
  }

  public List<ActivityProductShareInfo> findByAssociateIds(Set<String> associateIds) {
    return this.lambdaQuery()
        .in(ActivityProductShareInfo::getAssociateId,associateIds)
        .eq(ActivityProductShareInfo::getTenantCode,TenantUtils.getTenantCode())
        .orderByAsc(ActivityProductShareInfo::getRatio)
        .list();
  }

  public void deleteByAssociateIds(Set<String> associateIds){
    this.lambdaUpdate()
        .in(ActivityProductShareInfo::getAssociateId,associateIds)
        .eq(ActivityProductShareInfo::getTenantCode,TenantUtils.getTenantCode())
        .remove();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ActivityProductShareInfo::getTenantCode,tenantCode)
        .in(ActivityProductShareInfo::getId,ids)
        .remove();
  }
}
