package com.biz.crm.tpm.business.activities.dynamic.template.strategy;

import com.alibaba.fastjson.JSONArray;
import com.biz.crm.common.form.sdk.context.DynamicFormContext;
import com.biz.crm.common.form.sdk.context.DynamicFormContextHolder;
import com.biz.crm.common.form.sdk.model.DynamicFormsOperationStrategy;
import com.biz.crm.tpm.business.activities.dynamic.template.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.dynamic.template.repository.DisplayActivityItemRepository;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.DisplayActivityItem;
import com.biz.crm.tpm.business.activities.dynamic.template.service.ActivityProductShareInfoService;
import com.biz.crm.tpm.business.activities.dynamic.template.service.DisplayActivityItemService;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.ActivityProductShareInfoVo;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.DisplayActivityItemVo;
import com.biz.crm.tpm.business.activities.sdk.register.ActivitiesCenterModuleRegister;
import com.biz.crm.tpm.business.activities.sdk.strategy.close.ActivityItemsClosedStrategy;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Component
@Slf4j
public class DynamicFormsOperationStrategyForDisplayActivityItem implements DynamicFormsOperationStrategy<DisplayActivityItemVo>, ActivityItemsClosedStrategy {

  /** 对应本类托管于Spring IOC容器中bean的ID键值，代表的动态表单编码dynamicFormCode*/
  public static final String DISPLAY_ACTIVITY_ITEM_OPERATION_STRATEGY_KEY = StringUtils.uncapitalize(DynamicFormsOperationStrategyForDisplayActivityItem.class.getSimpleName());

  @Autowired
  private ActivitiesCenterModuleRegister activitiesCenterModuleRegister;
  @Autowired
  private DisplayActivityItemService displayActivityItemService;
  @Autowired
  private DisplayActivityItemRepository displayActivityItemRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private ActivityProductShareInfoService activityProductShareInfoService;

  /**
   * 日志
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(DynamicFormsOperationStrategyForDisplayActivityItem.class);
  
  @Override
  public int getOrder() {
    return 9;
  }
  
  @Override
  public String dynamicFormCode() {
    return DISPLAY_ACTIVITY_ITEM_OPERATION_STRATEGY_KEY;
  }

  @Override
  public String dynamicFormName() {
    return "示例用-陈列活动模板";
  }

  @Override
  public Class<DisplayActivityItemVo> dynamicFormClass() {
    return DisplayActivityItemVo.class;
  }

  @Override
  public String moduleCode() {
    return activitiesCenterModuleRegister.moduleCode();
  }

  @Override
  public void onDynamicFormsDelete(String parentCode, String dynamicKey) {
    displayActivityItemService.deleteByParentCode(parentCode);
  }


  @Override
  public void onDynamicFormsCreate(Collection<DisplayActivityItemVo> dynamicForms, String dynamicKey, String parentCode, Object parent) {
    Validate.notEmpty(dynamicForms,"陈列活动明细项不能为空");
    //由于泛型信息被擦除，所以此处需要重新指定
    List<DisplayActivityItemVo> items = JSONArray.parseArray(JSONArray.toJSONString(dynamicForms),DisplayActivityItemVo.class);
    this.processDynamicFormContextForFeeDate(items);
    for(DisplayActivityItemVo item : items){
      item.setParentCode(parentCode);
      item.setDynamicKey(dynamicKey);
      item.setDynamicFormCode(DISPLAY_ACTIVITY_ITEM_OPERATION_STRATEGY_KEY);
      DisplayActivityItem entity = nebulaToolkitService.copyObjectByWhiteList(item, DisplayActivityItem.class, HashSet.class, ArrayList.class,"shareInfos");
      displayActivityItemService.create(entity);
    }
    //重复性校验
    long size = items.stream().map(e -> StringUtils.joinWith(":",
            StringUtils.isNotBlank(e.getOrgCode()) ? e.getOrgCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getCustomerCode()) ? e.getCustomerCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getTerminalCode()) ? e.getTerminalCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getCostBudgetCode()) ? e.getCostBudgetCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getCostTypeCategoryCode()) ? e.getCostTypeCategoryCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getCostTypeDetailCode()) ? e.getCostTypeDetailCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getFeeDate()) ? e.getFeeDate() : StringUtils.EMPTY)).distinct().count();
    Validate.isTrue(size == items.size(),"陈列活动明细项信息重复，请检查");
  }

  @Override
  public void onDynamicFormsModify(Collection<DisplayActivityItemVo> dynamicForms, String dynamicKey, String parentCode, Object parent) {
    Validate.notEmpty(dynamicForms,"陈列活动明细项不能为空");
    //由于泛型信息被擦除，所以此处需要重新指定
    List<DisplayActivityItemVo> items = JSONArray.parseArray(JSONArray.toJSONString(dynamicForms),DisplayActivityItemVo.class);
    this.processDynamicFormContextForFeeDate(items);
    for(DisplayActivityItemVo item : items){
      if(StringUtils.isBlank(item.getId())){
        item.setParentCode(parentCode);
        item.setDynamicKey(dynamicKey);
        item.setDynamicFormCode(DISPLAY_ACTIVITY_ITEM_OPERATION_STRATEGY_KEY);
      }
    }
    Collection<DisplayActivityItem> collection = nebulaToolkitService.copyCollectionByWhiteList(items,DisplayActivityItemVo.class,DisplayActivityItem.class,HashSet.class,ArrayList.class,"shareInfos");
    displayActivityItemService.update(collection);

    //重复性校验
    List<DisplayActivityItem> result = displayActivityItemRepository.findByDynamicKeyAndParentCodeAndTenantCode(dynamicKey,parentCode, TenantUtils.getTenantCode());
    long size = result.stream().map(e -> StringUtils.joinWith(":",
            StringUtils.isNotBlank(e.getOrgCode()) ? e.getOrgCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getCustomerCode()) ? e.getCustomerCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getTerminalCode()) ? e.getTerminalCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getCostBudgetCode()) ? e.getCostBudgetCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getCostTypeCategoryCode()) ? e.getCostTypeCategoryCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getCostTypeDetailCode()) ? e.getCostTypeDetailCode() : StringUtils.EMPTY,
            StringUtils.isNotBlank(e.getFeeDate()) ? e.getFeeDate() : StringUtils.EMPTY)).distinct().count();
    Validate.isTrue(size == result.size(),"陈列活动明细项信息重复，请检查");
  }

  @Override
  public void onDynamicFormsDelete(String parentCode, String dynamicKey, String[] detailCodes) {
    LOGGER.info("onDynamicFormsDelete(------------)");
  }

  @Override
  public Collection<DisplayActivityItemVo> findByParentCode(String dynamicKey, String parentCode) {
    if(StringUtils.isAnyBlank(dynamicKey,parentCode)){
      return Lists.newArrayList();
    }
    List<DisplayActivityItem> result = displayActivityItemRepository.findByDynamicKeyAndParentCodeAndTenantCode(dynamicKey,parentCode, TenantUtils.getTenantCode());
    if(CollectionUtils.isEmpty(result)){
      return Lists.newArrayList();
    }
    Collection<DisplayActivityItemVo> itemVos = nebulaToolkitService.copyCollectionByWhiteList(result,DisplayActivityItem.class,DisplayActivityItemVo.class,HashSet.class,ArrayList.class);
    Set<String> itemIds = itemVos.stream().map(DisplayActivityItemVo::getId).collect(Collectors.toSet());
    List<ActivityProductShareInfoVo> shareInfoVos = activityProductShareInfoService.findByAssociateIds(itemIds);
    if(!CollectionUtils.isEmpty(shareInfoVos)){
      Map<String,List<ActivityProductShareInfoVo>> shareInfoGroups = shareInfoVos.stream().collect(Collectors.groupingBy(ActivityProductShareInfoVo::getAssociateId));
      for(DisplayActivityItemVo itemVo : itemVos){
        if(shareInfoGroups.containsKey(itemVo.getId())){
          itemVo.setShareInfos(shareInfoGroups.get(itemVo.getId()));
        }
      }
    }
    return itemVos;
  }

  private void processDynamicFormContextForFeeDate(List<DisplayActivityItemVo> items){
    DynamicFormContext context = DynamicFormContextHolder.getContext();
    if(!context.exist(ActivitiesConstant.ACTIVITY_START_TIME_KEY) || !context.exist(ActivitiesConstant.ACTIVITY_END_TIME_KEY)){
      return;
    }
    Date startTime = (Date)context.get(ActivitiesConstant.ACTIVITY_START_TIME_KEY);
    Date endTime = (Date)context.get(ActivitiesConstant.ACTIVITY_END_TIME_KEY);
    String startTimeStr = DateFormatUtils.format(startTime,"yyyy-MM");
    String endTimeStr = DateFormatUtils.format(endTime,"yyyy-MM");
    //是否跨月
    boolean crossMonth = !StringUtils.equals(startTimeStr,endTimeStr);
    if(!crossMonth){
      items.forEach(e -> e.setFeeDate(startTimeStr));
      return;
    }
    //是否含有明细分摊数据
    boolean existShare = items.stream().anyMatch(e -> StringUtils.isNotBlank(e.getShareKey()));
    if(!existShare){
      items.forEach(e -> e.setFeeDate(startTimeStr));
      return;
    }
    //拆解时间范围，验证费用年月
    LinkedList<String> times = Lists.newLinkedList();
    this.dismantle(startTime,endTimeStr,times);
    Set<String> shareKeys = items.stream().filter(e -> StringUtils.isNotBlank(e.getShareKey())).map(DisplayActivityItemVo::getShareKey).collect(Collectors.toSet());
    final Map<String,LinkedList<String>> shareKeyMaps = Maps.newHashMap();
    shareKeys.forEach(e -> shareKeyMaps.put(e,Lists.newLinkedList(times)));
    for(DisplayActivityItemVo itemVo : items){
      if(StringUtils.isBlank(itemVo.getShareKey())){
        itemVo.setFeeDate(startTimeStr);
      }else{
        LinkedList<String> theTimes = shareKeyMaps.get(itemVo.getShareKey());
        Validate.notEmpty(theTimes,"明细分摊条目数与活动时间范围不一致，请检查");
        theTimes.poll();
      }
    }
    shareKeyMaps.values().forEach(e -> Validate.isTrue(e.size() == 0,"存在活动明细在【%s】未分摊的情况，请检查核对",StringUtils.join(e,",")));
  }

  private void dismantle(Date startTime, String endTimeStr, List<String> times){
    String startTimeStr = DateFormatUtils.format(startTime,"yyyy-MM");
    if(StringUtils.compare(startTimeStr,endTimeStr) >= 0){
      times.add(endTimeStr);
      return;
    }
    times.add(DateFormatUtils.format(startTime,"yyyy-MM"));
    this.dismantle(DateUtils.addMonths(startTime,1),endTimeStr,times);
  }

  @Override
  @Transactional
  public void closed(Set<String> itemCodes) {
    Validate.notEmpty(itemCodes,"陈列活动明细编码不能为空");
    displayActivityItemRepository.updateForClose(itemCodes);
  }

  @Override
  public boolean allClosed(String parentCode) {
    Validate.notBlank(parentCode,"主活动编码不能为空");
    List<DisplayActivityItem> items = displayActivityItemRepository.findByParentCodeAndTenantCode(parentCode,TenantUtils.getTenantCode());
    return !CollectionUtils.isEmpty(items) && items.stream().allMatch(e -> e.getColsed() != null && e.getColsed());
  }
}
