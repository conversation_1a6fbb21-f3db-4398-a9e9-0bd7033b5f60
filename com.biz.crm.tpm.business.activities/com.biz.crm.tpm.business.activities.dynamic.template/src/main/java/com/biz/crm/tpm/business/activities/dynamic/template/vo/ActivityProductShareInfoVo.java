package com.biz.crm.tpm.business.activities.dynamic.template.vo;

import com.bizunited.nebula.common.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ActivityProductShareInfoVo", description = "活动明细商品分摊信息vo")
public class ActivityProductShareInfoVo extends TenantVo {

  @ApiModelProperty("关联的活动明细id键值")
  private String associateId;

  @ApiModelProperty("产品编码")
  private String productCode;

  @ApiModelProperty("产品名称")
  private String productName;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("分摊比例")
  private BigDecimal ratio;
}
