package com.biz.crm.tpm.business.activities.dynamic.template.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.lang3.StringUtils;

/**
 * 酒类用品
 */
public enum LiquorTypeEnum {
  HIGHLY("浓香型", "highly_flavor"),
  MAOTAI("酱香型", "maotai_flavor"),
  REFRESHING("清香型", "refreshing_flavor");

  public static boolean contains(String code){
    if(StringUtils.isBlank(code)){
      return false;
    }
    for(LiquorTypeEnum e : values()){
      if(StringUtils.equals(code,e.getCode())){
        return true;
      }
    }
    return false;
  }

  @EnumValue
  @JsonValue
  private String code;

  private String name;

  LiquorTypeEnum(String name, String code) {
    this.code = code;
    this.name = name;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }
}
