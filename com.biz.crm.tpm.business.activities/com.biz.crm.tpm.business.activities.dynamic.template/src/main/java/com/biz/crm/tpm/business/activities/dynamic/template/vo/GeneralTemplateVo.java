package com.biz.crm.tpm.business.activities.dynamic.template.vo;

import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.field.annotation.Validate;
import com.biz.crm.common.form.sdk.field.validate.NotBlankValidateStrategy;
import com.biz.crm.common.form.sdk.field.validate.NotNullValidateStrategy;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.BooleanSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleDateTimeSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
@ApiModel(value = "GeneralTemplateVo", description = "通用采集模板vo")
public class GeneralTemplateVo extends BaseActivityItemVo implements DynamicForm {

  @ApiModelProperty("执行日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @DynamicField(fieldName = "执行日期" , validates = {@Validate(value = NotNullValidateStrategy.class)} , controllKey = SimpleDateTimeSelectWidget.class, required = false)
  private Date executionDate;

  @ApiModelProperty("执行记录")
  @DynamicField(fieldName = "执行记录" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String executionRecord;

  @ApiModelProperty("是否正常")
  @DynamicField(fieldName = "是否正常" , validates = {@Validate(value = NotNullValidateStrategy.class)} , controllKey = BooleanSelectWidget.class, required = false)
  private Integer normal;
}
