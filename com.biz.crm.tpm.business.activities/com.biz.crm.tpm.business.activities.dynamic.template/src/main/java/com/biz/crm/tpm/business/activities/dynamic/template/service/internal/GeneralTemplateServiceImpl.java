package com.biz.crm.tpm.business.activities.dynamic.template.service.internal;

import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.activities.dynamic.template.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.GeneralTemplate;
import com.biz.crm.tpm.business.activities.dynamic.template.repository.GeneralTemplateRepository;
import com.biz.crm.tpm.business.activities.dynamic.template.service.GeneralTemplateService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class GeneralTemplateServiceImpl implements GeneralTemplateService {

  @Autowired
  private GeneralTemplateRepository generalTemplateRepository;
  @Autowired
  private GenerateCodeService generateCodeService;

  @Override
  @Transactional
  public void create(GeneralTemplate item) {
    this.validate(item);
    Validate.isTrue(StringUtils.isBlank(item.getId()),"新增时，主键id不能有值");
    item.setId(null);
    List<String> codeList = this.generateCodeService.generateCode(ActivitiesConstant.GENERAL_TEMPLATE_MX_CODE_KEY, 1);
    item.setItemCode(codeList.get(0));
    item.setTenantCode(TenantUtils.getTenantCode());
    generalTemplateRepository.save(item);
  }

  @Override
  @Transactional
  public void update(GeneralTemplate generalTemplate) {
    Validate.notNull(generalTemplate,"通用采集模板信息不能为空");
    this.validate(generalTemplate);
    String parentCode = generalTemplate.getParentCode();
    GeneralTemplate dbGeneralTemplate =  generalTemplateRepository.findByParentCodeAndTenantCode(parentCode,TenantUtils.getTenantCode());
    Validate.notNull(dbGeneralTemplate,"根据提供的主表编码【%s】，未能获取到相应信息",parentCode);
    dbGeneralTemplate.setLabel(generalTemplate.getLabel());
    dbGeneralTemplate.setExecutionDate(generalTemplate.getExecutionDate());
    dbGeneralTemplate.setExecutionRecord(generalTemplate.getExecutionRecord());
    dbGeneralTemplate.setNormal(generalTemplate.getNormal());
    dbGeneralTemplate.setTenantCode(TenantUtils.getTenantCode());
    generalTemplateRepository.saveOrUpdate(dbGeneralTemplate);
  }

  @Override
  @Transactional
  public void deleteByParentCode(String parentCode) {
    Validate.notBlank(parentCode,"主活动编码信息不能为空");
    GeneralTemplate generalTemplate = generalTemplateRepository.findByParentCodeAndTenantCode(parentCode,TenantUtils.getTenantCode());
    if(generalTemplate == null){
      return;
    }
    generalTemplateRepository.removeByIdAndTenantCode(generalTemplate.getId(),TenantUtils.getTenantCode());
  }

  private void validate(GeneralTemplate item){
    Validate.notNull(item,"通用采集模板信息不能为空");
    Validate.notBlank(item.getParentCode(),"主表编码不能为空");
    Validate.notBlank(item.getDynamicKey(),"业务key键值不能为空");
    Validate.notNull(item.getExecutionDate(),"执行日期不能为空");
    Validate.notBlank(item.getExecutionRecord(),"执行记录不能为空");
    Validate.notNull(item.getNormal(),"是否正常的状态不能为空");
    if(StringUtils.isBlank(item.getTenantCode())){
      item.setTenantCode(TenantUtils.getTenantCode());
    }
  }

}
