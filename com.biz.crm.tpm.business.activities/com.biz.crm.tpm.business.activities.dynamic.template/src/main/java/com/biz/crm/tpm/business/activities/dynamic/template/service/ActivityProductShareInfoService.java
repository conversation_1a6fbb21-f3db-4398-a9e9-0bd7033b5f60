package com.biz.crm.tpm.business.activities.dynamic.template.service;

import com.biz.crm.tpm.business.activities.dynamic.template.entity.ActivityProductShareInfo;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.ActivityProductShareInfoVo;

import java.util.List;
import java.util.Set;

public interface ActivityProductShareInfoService {
  void save(List<ActivityProductShareInfo> shareInfos);

  List<ActivityProductShareInfoVo> findByAssociateIds(Set<String> associateIds);

  void deleteByAssociateIds(Set<String> associateIds);
}
