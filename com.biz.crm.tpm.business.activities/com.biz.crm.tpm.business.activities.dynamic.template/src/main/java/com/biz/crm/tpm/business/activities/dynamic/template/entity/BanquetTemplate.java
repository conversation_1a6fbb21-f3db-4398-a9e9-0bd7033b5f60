package com.biz.crm.tpm.business.activities.dynamic.template.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.tpm.business.activities.sdk.template.BaseActivityItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@Entity
@TableName("tpm_banquet_template")
@Table(name = "tpm_banquet_template")
@ApiModel(value = "BanquetTemplate", description = "宴会采集模板")
@org.hibernate.annotations.Table(appliesTo = "tpm_banquet_template", comment = "宴会采集模板")
public class BanquetTemplate extends BaseActivityItem {

  @ApiModelProperty("宴会人数")
  @TableField(value = "peoples")
  @Column(name = "peoples", nullable = false, columnDefinition = "int(11) COMMENT '宴会人数'")
  private Integer peoples;

  @ApiModelProperty("宴会日期")
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @Column(name = "banquet_date" , length = 10, nullable = false, columnDefinition = "datetime COMMENT '宴会日期 '")
  private Date banquetDate;

  @ApiModelProperty("宴会类型")
  @TableField(value = "type")
  @Column(name = "type", nullable = false, columnDefinition = "varchar(32) COMMENT '宴会类型'")
  private String type;

  @ApiModelProperty("是否正常")
  @TableField(value = "normal")
  @Column(name = "normal", columnDefinition = "bit(1) COMMENT '是否正常'")
  private Integer normal;

  @ApiModelProperty("酒类用品(逗号拼接)")
  @TableField(value = "liquor_types")
  @Column(name = "liquor_types", columnDefinition = "varchar(255) COMMENT '酒类用品(逗号拼接)'")
  private String liquorTypes;
}
