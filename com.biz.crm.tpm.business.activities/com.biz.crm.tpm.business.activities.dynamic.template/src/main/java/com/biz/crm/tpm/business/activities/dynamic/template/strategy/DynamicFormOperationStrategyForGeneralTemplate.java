package com.biz.crm.tpm.business.activities.dynamic.template.strategy;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.form.sdk.model.DynamicFormOperationStrategy;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.GeneralTemplate;
import com.biz.crm.tpm.business.activities.dynamic.template.repository.GeneralTemplateRepository;
import com.biz.crm.tpm.business.activities.dynamic.template.service.GeneralTemplateService;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.GeneralTemplateVo;
import com.biz.crm.tpm.business.activities.sdk.register.BudgetCenterModuleRegister;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;


@Component
public class DynamicFormOperationStrategyForGeneralTemplate implements DynamicFormOperationStrategy<GeneralTemplateVo> {

  /** 对应本类托管于Spring IOC容器中bean的ID键值，代表的动态表单编码dynamicFormCode*/
  public static final String GENERAL_TEMPLATE_OPERATION_STRATEGY_KEY = StringUtils.uncapitalize(DynamicFormOperationStrategyForGeneralTemplate.class.getSimpleName());

  @Autowired
  private BudgetCenterModuleRegister budgetCenterModuleRegister;
  @Autowired
  private GeneralTemplateService generalTemplateService;
  @Autowired
  private GeneralTemplateRepository generalTemplateRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;


  /**
   * 日志
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(DynamicFormOperationStrategyForGeneralTemplate.class);

  @Override
  public int getOrder() {
    return 10;
  }

  @Override
  public String dynamicFormCode() {
    return GENERAL_TEMPLATE_OPERATION_STRATEGY_KEY;
  }

  @Override
  public String dynamicFormName() {
    return "通用采集模板";
  }

  @Override
  public Class<GeneralTemplateVo> dynamicFormClass() {
    return GeneralTemplateVo.class;
  }

  @Override
  public String moduleCode() {
    return budgetCenterModuleRegister.moduleCode();
  }

  @Override
  public void onDynamicFormsDelete(String parentCode, String dynamicKey) {
    generalTemplateService.deleteByParentCode(parentCode);
  }

  @Override
  public void onDynamicFormCreate(GeneralTemplateVo dynamicForm, String dynamicKey, String parentCode, Object parent) {
    Validate.notNull(dynamicForm,"通用采集动态模板不能为空");
    Validate.isTrue(StringUtils.isBlank(dynamicForm.getId()),"通用采集动态模板id主键不能有值");
    //由于泛型信息被擦除，所以此处需要重新指定
    GeneralTemplateVo item = JSONObject.parseObject(JSONObject.toJSONString(dynamicForm), GeneralTemplateVo.class);
    item.setId(null);
    item.setParentCode(parentCode);
    item.setDynamicKey(dynamicKey);
    item.setDynamicFormCode(GENERAL_TEMPLATE_OPERATION_STRATEGY_KEY);
    GeneralTemplate entity = nebulaToolkitService.copyObjectByWhiteList(item, GeneralTemplate.class, HashSet.class, ArrayList.class);
    generalTemplateService.create(entity);
  }

  @Override
  public void onDynamicFormModify(GeneralTemplateVo dynamicForm, String dynamicKey, String parentCode, Object parent) {
    Validate.notNull(dynamicForm,"通用采集动态模板不能为空");
    Validate.notBlank(dynamicForm.getId(),"通用采集动态模板id主键不能为空");
    //由于泛型信息被擦除，所以此处需要重新指定
    GeneralTemplateVo item = JSONObject.parseObject(JSONObject.toJSONString(dynamicForm), GeneralTemplateVo.class);
    GeneralTemplate generalTemplate = nebulaToolkitService.copyObjectByWhiteList(item,GeneralTemplate.class,HashSet.class,ArrayList.class);
    generalTemplateService.update(generalTemplate);
  }

  @Override
  public GeneralTemplateVo findByParentCode(String dynamicKey, String parentCode) {
    if(StringUtils.isBlank(parentCode)){
      return null;
    }
    GeneralTemplate result = generalTemplateRepository.findByParentCodeAndTenantCode(parentCode, TenantUtils.getTenantCode());
    if(result == null){
      return null;
    }
    return nebulaToolkitService.copyObjectByWhiteList(result,GeneralTemplateVo.class,HashSet.class,ArrayList.class);
  }

}
