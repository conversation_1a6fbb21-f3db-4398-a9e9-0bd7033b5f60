package com.biz.crm.tpm.business.activities.dynamic.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_activity_product_share_info")
@Table(name = "tpm_activity_product_share_info")
@ApiModel(value = "ActivityProductShareInfo", description = "活动明细商品分摊信息")
@org.hibernate.annotations.Table(appliesTo = "tpm_activity_product_share_info", comment = "活动明细商品分摊信息")
public class ActivityProductShareInfo extends TenantEntity {

  @ApiModelProperty("关联的活动明细id键值")
  @TableField(value = "associate_id")
  @Column(name = "associate_id", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '关联的活动明细id键值'")
  private String associateId;

  @ApiModelProperty("产品编码")
  @TableField(value = "product_code")
  @Column(name = "product_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '产品编码'")
  private String productCode;

  @ApiModelProperty("产品名称")
  @TableField(value = "product_name")
  @Column(name = "product_name", nullable = false, columnDefinition = "varchar(255) COMMENT '产品名称'")
  private String productName;

  @ApiModelProperty("备注")
  @TableField(value = "remark")
  @Column(name = "remark", columnDefinition = "varchar(255) COMMENT '备注'")
  private String remark;

  @ApiModelProperty("分摊比例")
  @TableField(value = "ratio")
  @Column(name = "ratio", nullable = false, columnDefinition = "decimal(20,4) COMMENT '分摊比例'")
  private BigDecimal ratio;
}
