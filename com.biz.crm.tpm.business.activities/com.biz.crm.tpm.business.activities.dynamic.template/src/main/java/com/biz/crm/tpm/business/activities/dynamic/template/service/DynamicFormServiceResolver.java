package com.biz.crm.tpm.business.activities.dynamic.template.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.form.sdk.DynamicFormService;
import com.biz.crm.common.form.sdk.DynamicFormServiceBuilder;
import com.biz.crm.common.form.sdk.model.DynamicFormOperationStrategy;
import com.biz.crm.common.form.sdk.model.OperationStrategy;
import com.biz.crm.tpm.business.activities.dynamic.template.enums.FormTypeEnum;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesConfigVoService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.google.common.collect.Sets;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

@SuppressWarnings({"unchecked", "rawtypes"})
@Component
public class DynamicFormServiceResolver {

  @Autowired
  private ApplicationContext applicationContext;
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired
  private ActivitiesConfigVoService activitiesConfigVoService;
  @Autowired(required = false)
  private List<DynamicFormOperationStrategy> dynamicFormOperationStrategies;


  /**
   * 通过外界传入的json、以及其他相应条件，获取动态模板处理类信息DynamicFormService
   * 注：该方法适用于主页午具有未知动态模型的情况
   *
   * @param json             外界传入的json，一般是业务数据
   * @param dynamicFormField 主业务与动态明细模型的关联字段，该字段一定是个Map对应的字段
   * @param mainClass        主业务类
   */
  public <T> Set<DynamicFormService<T>> getDynamicFormServices(JSONObject json, String dynamicFormField, Class<T> mainClass, FormTypeEnum formTypeEnum) {
    //1.====基础必要条件的验证
    if (StringUtils.isBlank(dynamicFormField) || json == null || json.size() == 0 || mainClass == null) {
      return null;
    }
    JSONObject itemJsons = json.getJSONObject(dynamicFormField);
    if (itemJsons == null || itemJsons.size() == 0) {
      return null;
    }

    //2.====从json信息中提取dynamicFormCode，因实际业务形态，动态模型的确定并不固定
    Set<DynamicFormService<T>> dynamicFormServices = Sets.newHashSet();
    Set<String> dynamicKeys = itemJsons.keySet();
    for (String dynamicKey : dynamicKeys) {
      //如果传入的动态明细数据是null，则不处理
      if (itemJsons.get(dynamicKey) == null || itemJsons.getJSONArray(dynamicKey).size() == 0) {
        continue;
      }
      this.regularDynamicKey(dynamicKey);
      String costTypeCategoryCode = StringUtils.split(dynamicKey, ":")[1];
      CostTypeDetailVo costTypeDetailVo = costTypeDetailVoService.findByCode(costTypeCategoryCode);
      if (costTypeDetailVo == null) {
        return Collections.emptySet();
      }
      //根据传入的当前执行类别，获取对应的动态表单编码
      ActivitiesConfigVo activitiesConfigVo = null;
      if (formTypeEnum == FormTypeEnum.APPLY) {
        activitiesConfigVo = activitiesConfigVoService.findByActivitiesConfigCode(costTypeDetailVo.getApplyFromCode());
      } else if (formTypeEnum == FormTypeEnum.EXECUTION) {
        activitiesConfigVo = activitiesConfigVoService.findByActivitiesConfigCode(costTypeDetailVo.getExecutionFromCode());
      } else {
        activitiesConfigVo = activitiesConfigVoService.findByActivitiesConfigCode(costTypeDetailVo.getBudgetFromCode());
      }
      if (activitiesConfigVo == null) {
        return Collections.emptySet();
      }
      //3.====获取动态表单编码dynamicFormCode

      String dynamicFormCode = activitiesConfigVo.getDynamicFormCode();

      //4.====构建相应业务的动态模型服务信息，其中关键的dynamicKey信息需要从传入的json中进行提取
      DynamicFormServiceBuilder<T> builder = applicationContext.getBean(DynamicFormServiceBuilder.class, mainClass, this.applicationContext);
      DynamicFormService<T> e = builder.dynamicField(dynamicFormField).addDynamicMapping(dynamicKey, dynamicFormCode).config().build();
      dynamicFormServices.add(e);
    }

    //5.====返回的动态模型服务，该结果可能有多个值，具体看dynamicKeys的个数
    //注意：一个dynamicKey一个动态模型，不同的dynamicKey可能具有相同的OperationStrategy。。具体看实际的业务实现
    return dynamicFormServices;
  }


  /**
   * 通过外界传入的json、以及其他相应条件，获取指定的动态模板处理类信息DynamicFormService
   * 注：该方法适用于静态已知具体的业务应该使用什么样的动态模型
   *
   * @param json             外界传入的json，一般是业务数据
   * @param dynamicFormField 主业务与动态明细模型的关联字段，该字段一定是个Map对应的字段
   * @param dynamicFormCode  动态模型唯一编码(OperationStrategy的dynamicFormCode)
   * @param mainClass        主业务类
   */
  public <T> Set<DynamicFormService<T>> getDynamicFormServices(JSONObject json, String dynamicFormField, String dynamicFormCode, Class<T> mainClass) {
    //1.====基础必要条件的验证
    if (StringUtils.isAnyBlank(dynamicFormField, dynamicFormCode) || json == null || json.size() == 0 || mainClass == null) {
      return null;
    }
    JSONObject itemJsons = json.getJSONObject(dynamicFormField);
    if (itemJsons == null || itemJsons.size() == 0) {
      return null;
    }

    //2.====构建相应业务的动态模型服务信息，其中关键的dynamicKey信息需要从传入的json中进行提取
    DynamicFormServiceBuilder<T> builder = applicationContext.getBean(DynamicFormServiceBuilder.class, mainClass, this.applicationContext);
    Set<String> dynamicKeys = itemJsons.keySet();
    Set<DynamicFormService<T>> dynamicFormServices = Sets.newHashSet();
    for (String dynamicKey : dynamicKeys) {
      //如果传入的动态明细数据是null，则不处理
      if (itemJsons.get(dynamicKey) == null || itemJsons.getJSONArray(dynamicKey).size() == 0) {
        continue;
      }
      this.regularDynamicKey(dynamicKey);
      DynamicFormService<T> e = builder.dynamicField(dynamicFormField).addDynamicMapping(dynamicKey, dynamicFormCode).config().build();
      dynamicFormServices.add(e);
    }

    //3.====返回的动态模型服务，该结果可能有多个值，具体看dynamicKeys的个数
    //注意：一个dynamicKey一个动态模型，不同的dynamicKey可能具有相同的OperationStrategy。。具体看实际的业务实现
    return dynamicFormServices;
  }


  /**
   * 通过外界传入的dynamicKey、以及其他相应条件，获取指定的动态模板处理类信息DynamicFormService
   * 注：该方法适用于查询主业务时
   *
   * @param dynamicFormCode  动态表单code编码
   * @param parentCode       主业务类的编码
   * @param dynamicFormField 主业务与动态明细模型的关联字段，该字段一定是个Map对应的字段
   *                         //   * @param dynamicFormCode 动态模型唯一编码(OperationStrategy的dynamicFormCode)
   * @param mainClass        主业务类
   */
  public <T> DynamicFormService<T> getDynamicFormService(String parentCode, String dynamicFormCode, String dynamicFormField, Class<T> mainClass) {
    //1.====基础必要条件的验证
    if (StringUtils.isAnyBlank(dynamicFormField, parentCode, dynamicFormCode) || mainClass == null) {
      return null;
    }

    DynamicFormServiceBuilder<T> builder = applicationContext.getBean(DynamicFormServiceBuilder.class, mainClass, this.applicationContext);

    //3.====返回的动态模型服务
    //注意：一个dynamicKey一个动态模型，不同的dynamicKey可能具有相同的OperationStrategy。。具体看实际的业务实现
    return builder.dynamicField(dynamicFormField).addDynamicMapping(parentCode, dynamicFormCode).config().build();
  }


  /**
   * 通过外界传入的dynamicKey、以及其他相应条件，获取指定的动态模板处理类信息DynamicFormService
   * 注：该方法适用于查询主业务时
   *
   * @param dynamicKey       已知的map key键值
   * @param dynamicFormField 主业务与动态明细模型的关联字段，该字段一定是个Map对应的字段
   * @param mainClass        主业务类
   */
  public <T> DynamicFormService<T> getDynamicFormService(String dynamicKey, String dynamicFormField, Class<T> mainClass, FormTypeEnum formTypeEnum) {
    //1.====基础必要条件的验证
    if (StringUtils.isBlank(dynamicFormField) || mainClass == null) {
      return null;
    }

    //2.====获取动态表单编码dynamicFormCode
    this.regularDynamicKey(dynamicKey);
    String costTypeCategoryCode = StringUtils.split(dynamicKey, ":")[1];
    CostTypeDetailVo costTypeDetailVo = costTypeDetailVoService.findByCode(costTypeCategoryCode);
    if (costTypeDetailVo == null) {
      return null;
    }
    ActivitiesConfigVo activitiesConfigVo;
    if (formTypeEnum == FormTypeEnum.APPLY) {
      activitiesConfigVo = activitiesConfigVoService.findByActivitiesConfigCode(costTypeDetailVo.getApplyFromCode());
    } else if (formTypeEnum == FormTypeEnum.EXECUTION) {
      activitiesConfigVo = activitiesConfigVoService.findByActivitiesConfigCode(costTypeDetailVo.getExecutionFromCode());
    } else {
      activitiesConfigVo = activitiesConfigVoService.findByActivitiesConfigCode(costTypeDetailVo.getBudgetFromCode());
    }
    if (activitiesConfigVo == null) {
      return null;
    }
    //3.====获取动态表单编码dynamicFormCode

    String dynamicFormCode = activitiesConfigVo.getDynamicFormCode();

    //3.====构建相应业务的动态模型服务信息，其中关键的dynamicKey信息需要从传入的json中进行提取
    DynamicFormServiceBuilder<T> builder = applicationContext.getBean(DynamicFormServiceBuilder.class, mainClass, this.applicationContext);

    //4.====返回的动态模型服务
    //注意：一个dynamicKey一个动态模型，不同的dynamicKey可能具有相同的OperationStrategy。。具体看实际的业务实现
    return builder.dynamicField(dynamicFormField).addDynamicMapping(dynamicKey, dynamicFormCode).config().build();
  }

  public Class<? extends OperationStrategy> getDynamicFormClass(String dynamicFormCode) {
    if (StringUtils.isBlank(dynamicFormCode) || CollectionUtils.isEmpty(dynamicFormOperationStrategies)) {
      return null;
    }
    DynamicFormOperationStrategy strategy = null;
    for (DynamicFormOperationStrategy dynamicFormOperationStrategy : dynamicFormOperationStrategies) {
      if (StringUtils.equals(dynamicFormOperationStrategy.dynamicFormCode(), dynamicFormCode)) {
        strategy = dynamicFormOperationStrategy;
        break;
      }
    }
    if (strategy == null) {
      return null;
    }
    return strategy.dynamicFormClass();
  }

  /**
   * dynamicKey需满足正则条件
   * 比如合法的值：FEE001:AD0001
   */
  private void regularDynamicKey(String dynamicKey) {
    String pattern = "^[A-Z]{1}[A-Z0-9]*[:]{1}[A-Z]{1}[A-Z0-9]*$";
    Validate.matchesPattern(dynamicKey, pattern, "外界传入的dynamicKey格式非法，正确的格式形如【FEE001:AD0001】，由大写字母及数字组成，且中间由:号拼接，不能含有其它特殊字符");
  }
}
