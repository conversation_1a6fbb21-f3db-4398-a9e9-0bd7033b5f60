package com.biz.crm.tpm.business.activities.dynamic.template.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.DisplayActivityItem;
import com.biz.crm.tpm.business.activities.dynamic.template.mapper.DisplayActivityItemMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 陈列活动动态模板明细(DisplayActivityItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class DisplayActivityItemRepository extends ServiceImpl<DisplayActivityItemMapper, DisplayActivityItem> {
  /**
   * 根据主业务编码parentCode和业务dynamicKey键值，查询数据
   */
  public List<DisplayActivityItem> findByDynamicKeyAndParentCodeAndTenantCode(String dynamicKey, String parentCode, String tenantCode){
    return this.lambdaQuery().eq(DisplayActivityItem::getParentCode,parentCode).
            eq(DisplayActivityItem::getDynamicKey,dynamicKey).
            eq(DisplayActivityItem::getTenantCode,tenantCode).
            orderByAsc(DisplayActivityItem::getItemCode).orderByAsc(DisplayActivityItem::getFeeDate).
            list();
  }

  /**
   * 根据活动细类编码，统计数据
   */
  public int countByCostTypeDetailCodeAndTenantCode(String costTypeDetailCode, String tenantCode){
    return this.lambdaQuery().eq(DisplayActivityItem::getCostTypeDetailCode,costTypeDetailCode).
            eq(DisplayActivityItem::getTenantCode,tenantCode).
            count();
  }

  /**
   * 根据主活动编码，查询数据
   */
  public List<DisplayActivityItem> findByParentCodeAndTenantCode(String parentCode, String tenantCode){
    return this.lambdaQuery().eq(DisplayActivityItem::getParentCode,parentCode).
            eq(DisplayActivityItem::getTenantCode,tenantCode).
            list();
  }

  /**
   * 关闭明细活动
   */
  public void updateForClose(Set<String> itemCodes){
    this.lambdaUpdate()
        .in(DisplayActivityItem::getItemCode,itemCodes)
        .eq(DisplayActivityItem::getTenantCode, TenantUtils.getTenantCode())
        .set(DisplayActivityItem::getColsed,Boolean.TRUE)
        .update();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(Set<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(DisplayActivityItem::getTenantCode,tenantCode)
        .in(DisplayActivityItem::getId,ids)
        .remove();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(DisplayActivityItem::getTenantCode,tenantCode)
        .in(DisplayActivityItem::getId,ids)
        .remove();
  }
}

