package com.biz.crm.tpm.business.activities.dynamic.template.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.common.form.sdk.context.DynamicFormContext;
import com.biz.crm.common.form.sdk.context.DynamicFormContextHolder;
import com.biz.crm.tpm.business.activities.dynamic.template.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.dynamic.template.repository.MaterialActivityItemRepository;
import com.biz.crm.tpm.business.activities.dynamic.template.service.MaterialActivityItemService;
import com.biz.crm.tpm.business.activities.dynamic.template.strategy.DynamicFormsOperationStrategyForMaterialActivityItem;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.MaterialActivityItem;
import com.biz.crm.tpm.business.activities.dynamic.template.service.ActivityProductShareInfoService;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.MaterialActivityItemVo;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesShareDto;
import com.biz.crm.tpm.business.activities.sdk.strategy.share.ActivitiesShareStrategy;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class MaterialActivityItemServiceImpl implements MaterialActivityItemService, ActivitiesShareStrategy {

  @Autowired
  private MaterialActivityItemRepository materialActivityItemRepository;
  @Autowired
  private ActivityProductShareInfoService activityProductShareInfoService;
  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  @Transactional
  public void create(MaterialActivityItem item) {
    this.validate(item);
    Validate.isTrue(StringUtils.isBlank(item.getId()),"新增时，主键id不能有值");
    item.setId(null);
    List<String> codeList = this.generateCodeService.generateCode(ActivitiesConstant.MATERIAL_MX_CODE_KEY, 1);
    item.setItemCode(codeList.get(0));
    //处理申请金额相关逻辑（申请总金额，费用预算可用余额）
    this.processDynamicFormContextForCreateOrUpdate(item);
    item.setTenantCode(TenantUtils.getTenantCode());
    materialActivityItemRepository.save(item);
    //保存可能的明细商品分摊信息
    if(!CollectionUtils.isEmpty(item.getShareInfos())){
      item.getShareInfos().forEach(e -> e.setAssociateId(item.getId()));
      item.getShareInfos().forEach(e -> e.setTenantCode(TenantUtils.getTenantCode()));
      activityProductShareInfoService.save(item.getShareInfos());
    }
  }

  @Override
  @Transactional
  public void update(Collection<MaterialActivityItem> items) {
    Validate.notEmpty(items,"物料领用活动动态模板明细信息不能为空");
    items.forEach(this::validate);
    MaterialActivityItem materialActivityItem = items.iterator().next();
    String parentCode = materialActivityItem.getParentCode();
    String dynamicKey = materialActivityItem.getDynamicKey();
    Set<String> currentIds = items.stream().filter(e -> StringUtils.isNotBlank(e.getId())).map(MaterialActivityItem::getId).collect(Collectors.toSet());
    List<MaterialActivityItem> dbItems =  materialActivityItemRepository.findByDynamicKeyAndParentCodeAndTenantCode(dynamicKey,parentCode,TenantUtils.getTenantCode());
    Validate.notEmpty(dbItems,"根据提供的主表编码【%s】和动态模板明细键值【%s】，未能获取到相应信息",parentCode,dynamicKey);
    Set<String> dbIds = dbItems.stream().map(MaterialActivityItem::getId).collect(Collectors.toSet());
    //处理删除情况
    Set<String> needDeletes = Sets.difference(dbIds,currentIds);
    if(!CollectionUtils.isEmpty(needDeletes)){
      activityProductShareInfoService.deleteByAssociateIds(needDeletes);
      materialActivityItemRepository.removeByIdsAndTenantCode(needDeletes,TenantUtils.getTenantCode());
    }

    //处理更新情况
    Set<MaterialActivityItem> needUpdates = items.stream().filter(e -> StringUtils.isNotBlank(e.getId())).collect(Collectors.toSet());
    if(!CollectionUtils.isEmpty(needUpdates)){
      for(MaterialActivityItem item : needUpdates){
        MaterialActivityItem dbItem = dbItems.stream().filter(e -> StringUtils.equals(e.getId(),item.getId())).findFirst().orElse(null);
        Validate.notNull(dbItem,"未能匹配到相应的物料领用活动动态模板明细信息，请检查");
        this.processDynamicFormContextForCreateOrUpdate(item);
        dbItem.setApplyAmount(item.getApplyAmount());
        dbItem.setLabel(item.getLabel());
        dbItem.setCostBudgetCode(item.getCostBudgetCode());
        dbItem.setCostTypeCategoryCode(item.getCostTypeCategoryCode());
        dbItem.setCostTypeCategoryName(item.getCostTypeCategoryName());
        dbItem.setCostTypeDetailCode(item.getCostTypeDetailCode());
        dbItem.setCostTypeDetailName(item.getCostTypeDetailName());
        dbItem.setBudgetSubjectsCode(item.getBudgetSubjectsCode());
        dbItem.setBudgetSubjectsName(item.getBudgetSubjectsName());
        dbItem.setCustomerCode(item.getCustomerCode());
        dbItem.setCustomerName(item.getCustomerName());
        dbItem.setCostPrice(item.getCostPrice());
        dbItem.setMaterialCode(item.getMaterialCode());
        dbItem.setMaterialName(item.getMaterialName());
        dbItem.setQuantity(item.getQuantity());
        dbItem.setFeeDate(item.getFeeDate());
        dbItem.setOrgCode(item.getOrgCode());
        dbItem.setOrgName(item.getOrgName());
        dbItem.setPayType(item.getPayType());
        dbItem.setPayTypeName(item.getPayTypeName());
        dbItem.setRemark(item.getRemark());
        dbItem.setTerminalCode(item.getTerminalCode());
        dbItem.setTerminalName(item.getTerminalName());
        dbItem.setShareKey(item.getShareKey());
        dbItem.setTenantCode(TenantUtils.getTenantCode());
        materialActivityItemRepository.saveOrUpdate(dbItem);
        //保存可能的明细商品分摊信息
        if(!CollectionUtils.isEmpty(item.getShareInfos())){
          item.getShareInfos().forEach(e -> e.setAssociateId(item.getId()));
          item.getShareInfos().forEach(e -> e.setTenantCode(TenantUtils.getTenantCode()));
          activityProductShareInfoService.save(item.getShareInfos());
        }
      }
    }

    //处理新增情况
    List<MaterialActivityItem> needAdds = items.stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(needAdds)){
      for(MaterialActivityItem needAdd : needAdds){
        this.create(needAdd);
      }
    }
  }

  @Override
  @Transactional
  public void deleteByParentCode(String parentCode) {
    Validate.notBlank(parentCode,"主活动编码信息不能为空");
    List<MaterialActivityItem> materialActivityItems = materialActivityItemRepository.findByParentCodeAndTenantCode(parentCode,TenantUtils.getTenantCode());
    if(CollectionUtils.isEmpty(materialActivityItems)){
      return;
    }
    materialActivityItemRepository.removeByIdsAndTenantCode(materialActivityItems.stream().map(MaterialActivityItem::getId).collect(Collectors.toList()),TenantUtils.getTenantCode());
  }

  private void validate(MaterialActivityItem item){
    Validate.notNull(item,"物料领用活动动态模板明细不能为空");
    Validate.notBlank(item.getParentCode(),"主表编码不能为空");
    Validate.notBlank(item.getDynamicKey(),"业务key键值不能为空");
    Validate.notBlank(item.getCostBudgetCode(),"费用预算编码不能为空");
    Validate.notBlank(item.getCostTypeCategoryCode(),"活动大类编码不能为空");
    Validate.notBlank(item.getCostTypeCategoryName(),"活动大类名称不能为空");
    Validate.notBlank(item.getCostTypeDetailCode(),"活动细类编码不能为空");
    Validate.notBlank(item.getCostTypeDetailName(),"活动细类名称不能为空");
    Validate.notBlank(item.getBudgetSubjectsCode(),"预算科目编码不能为空");
    Validate.notBlank(item.getBudgetSubjectsName(),"预算科目名称不能为空");
    Validate.notBlank(item.getOrgCode(),"组织编码不能为空");
    Validate.notBlank(item.getOrgName(),"组织名称不能为空");
    Validate.notBlank(item.getMaterialCode(),"物料编码不能为空");
    Validate.notBlank(item.getMaterialName(),"物料名称不能为空");
    Validate.notNull(item.getQuantity(),"领用数量不能为空");
    Validate.notNull(item.getCostPrice(),"成本价格不能为空");
    this.validateCustomer(item);
    this.validateTerminal(item);
    Validate.isTrue(item.getApplyAmount().compareTo(BigDecimal.ZERO) > 0,"申请金额必须大于0");
    Validate.isTrue(item.getQuantity().compareTo(BigDecimal.ZERO) > 0,"领用数量必须大于0");
    Validate.isTrue(new BigDecimal(item.getQuantity().intValue()).compareTo(item.getQuantity()) == 0,"领用数量必须为整数");
    Validate.isTrue(item.getCostPrice().compareTo(BigDecimal.ZERO) > 0,"成本价必须大于0");
    Validate.notBlank(item.getPayType(),"支付方式不能为空");
    Validate.notBlank(item.getPayTypeName(),"支付方式名称不能为空");
    Validate.notBlank(item.getFeeDate(),"费用日期(年月)不能为空");
    if(StringUtils.isBlank(item.getTenantCode())){
      item.setTenantCode(TenantUtils.getTenantCode());
    }
  }

  private void validateCustomer(MaterialActivityItem item){
    if(StringUtils.isNotBlank(item.getCustomerCode()) || StringUtils.isNotBlank(item.getCustomerName())){
      Validate.notBlank(item.getCustomerCode(),"客户编码不能为空");
      Validate.notBlank(item.getCustomerName(),"客户名称不能为空");
    }
  }

  private void validateTerminal(MaterialActivityItem item){
    if(StringUtils.isNotBlank(item.getTerminalCode()) || StringUtils.isNotBlank(item.getTerminalName())){
      Validate.notBlank(item.getCustomerCode(),"客户编码不能为空");
      Validate.notBlank(item.getCustomerName(),"客户名称不能为空");
      Validate.notBlank(item.getTerminalCode(),"终端编码不能为空");
      Validate.notBlank(item.getTerminalName(),"终端名称不能为空");
    }
  }

  private void validateFeeDate(MaterialActivityItem item, DynamicFormContext context){
    if(context.exist(ActivitiesConstant.ACTIVITY_START_TIME_KEY) && context.exist(ActivitiesConstant.ACTIVITY_END_TIME_KEY)){
      Date startTime = (Date)context.get(ActivitiesConstant.ACTIVITY_START_TIME_KEY);
      Date endTime = (Date)context.get(ActivitiesConstant.ACTIVITY_END_TIME_KEY);
      String startTimeStr = DateFormatUtils.format(startTime,"yyyy-MM");
      String endTimeStr = DateFormatUtils.format(endTime,"yyyy-MM");
      Validate.matchesPattern(item.getFeeDate(),"^2[0-1]{1}[0-9]{1}[0-9]{1}[-]{1}[0-1]{1}[0-9]{1}$","费用年月不满足yyyy-MM格式");
      if(StringUtils.isBlank(item.getShareKey())){
        Validate.isTrue(StringUtils.compare(startTimeStr,item.getFeeDate()) == 0,"活动的开始日期与明细月份不一致，费用年月需满足yyyy-MM格式");
      }else{
        Validate.isTrue(StringUtils.compare(startTimeStr,item.getFeeDate()) <= 0 && StringUtils.compare(endTimeStr,item.getFeeDate()) >= 0,"费用年月不在活动时间范围内，请检查");
      }
    }
  }

  private void processDynamicFormContextForCreateOrUpdate(MaterialActivityItem item){
    DynamicFormContext context = DynamicFormContextHolder.getContext();
    this.validateFeeDate(item,context);
    if(context.exist(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY)){
      BigDecimal totalApplyAmount = (BigDecimal)context.get(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY);
      context.put(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY,totalApplyAmount.add(item.getApplyAmount()));
    }
  }

  @Override
  public String dynamicFormCode() {
    return DynamicFormsOperationStrategyForMaterialActivityItem.MATERIAL_ACTIVITY_ITEM_OPERATION_STRATEGY_KEY;
  }

  @Override
  public List<?> share(ActivitiesShareDto dto) {
    Validate.notNull(dto,"传入的待分摊信息不能为空");
    Date startTime = dto.getStartTime();
    Date endTime = dto.getEndTime();
    String dynamicFormCode = dto.getDynamicFormCode();
    JSONObject dynamicForm = dto.getDynamicForm();
    Validate.notNull(startTime,"活动开始时间不能为空");
    Validate.notNull(endTime,"活动结束时间不能为空");
    Validate.notBlank(dynamicFormCode,"动态表单编码不能为空");
    Validate.notEmpty(dynamicForm,"动态表单信息不能为空");
    MaterialActivityItemVo itemVo = JSONObject.parseObject(JSONObject.toJSONString(dynamicForm),MaterialActivityItemVo.class);
    Validate.isTrue(StringUtils.isBlank(itemVo.getFeeDate()),"动态表单信息中，费用年月不能有值，请检查");

    //是否跨月
    String startTimeStr = DateFormatUtils.format(startTime,"yyyy-MM");
    String endTimeStr = DateFormatUtils.format(endTime,"yyyy-MM");
    boolean crossMonth = !StringUtils.equals(startTimeStr,endTimeStr);
    Validate.isTrue(crossMonth,"活动时间范围不存在跨月情况，不能进行分摊");
    //拆解时间范围，验证费用年月
    LinkedList<String> times = Lists.newLinkedList();
    LinkedList<BigDecimal> amounts = Lists.newLinkedList();
    this.dismantleTimes(startTime,endTimeStr,times);
    this.dismantleAmount(itemVo.getApplyAmount(),times.size(),amounts);
    List<MaterialActivityItemVo> result = Lists.newArrayList();
    String shareKey = String.valueOf(System.currentTimeMillis());
    for(String time : times){
      MaterialActivityItemVo copy = nebulaToolkitService.copyObjectByWhiteList(itemVo,MaterialActivityItemVo.class, HashSet.class, ArrayList.class);
      copy.setItemCode(null);
      copy.setId(null);
      copy.setShareKey(shareKey);
      copy.setFeeDate(time);
      copy.setApplyAmount(amounts.poll());
      result.add(copy);
    }
    return result;
  }

  /**
   * 按照活动开始时间和活动结束时间，拆解时间
   */
  private void dismantleTimes(Date startTime, String endTimeStr, List<String> times){
    String startTimeStr = DateFormatUtils.format(startTime,"yyyy-MM");
    if(StringUtils.compare(startTimeStr,endTimeStr) >= 0){
      times.add(endTimeStr);
      return;
    }
    times.add(DateFormatUtils.format(startTime,"yyyy-MM"));
    this.dismantleTimes(DateUtils.addMonths(startTime,1),endTimeStr,times);
  }

  /**
   * 按照指定申请金额，分摊金额
   */
  private void dismantleAmount(BigDecimal applyAmount, int size, List<BigDecimal> amounts){
    Validate.notNull(applyAmount,"指定的申请金额不能为空");
    if (size == 0) {
      return;
    }
    Validate.isTrue(applyAmount.compareTo(new BigDecimal("0.01").multiply(BigDecimal.valueOf(size))) >= 0 ,"指定的申请金额【%s】不满足最小分摊金额，请检查",applyAmount.toString());
    BigDecimal shareAmount = applyAmount.divide(BigDecimal.valueOf(size),2, RoundingMode.DOWN);
    for(int index = 0 ; index < size - 1 ; index++){
      amounts.add(shareAmount);
    }
    //组装最后一次的分摊金额
    BigDecimal shareAmounts = amounts.stream()
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO,BigDecimal::add);
    BigDecimal lastShareAmount = applyAmount.subtract(shareAmounts);
    amounts.add(lastShareAmount);
  }
}
