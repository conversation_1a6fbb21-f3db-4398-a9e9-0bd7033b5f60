package com.biz.crm.tpm.business.activities.dynamic.template.vo;

import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.field.annotation.Validate;
import com.biz.crm.common.form.sdk.field.validate.NotBlankValidateStrategy;
import com.biz.crm.common.form.sdk.field.validate.NotNullValidateStrategy;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.BooleanSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleDateTimeSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.biz.crm.tpm.business.activities.sdk.widget.BanquetSelectWidget;
import com.biz.crm.tpm.business.activities.sdk.widget.LiquorSelectWidget;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
@ApiModel(value = "BanquetTemplate", description = "宴会采集模板")
public class BanquetTemplateVo extends BaseActivityItemVo implements DynamicForm {

  @ApiModelProperty("宴会人数")
  @DynamicField(fieldName = "宴会人数" , validates = {@Validate(value = NotNullValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private Integer peoples;

  @ApiModelProperty("宴会日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @DynamicField(fieldName = "宴会日期" , validates = {@Validate(value = NotNullValidateStrategy.class)} , controllKey = SimpleDateTimeSelectWidget.class, required = false)
  private Date banquetDate;

  @ApiModelProperty("宴会类型")
  @DynamicField(fieldName = "宴会类型" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = BanquetSelectWidget.class, required = false)
  private String type;

  @ApiModelProperty("是否正常")
  @DynamicField(fieldName = "是否正常" , validates = {@Validate(value = NotNullValidateStrategy.class)} , controllKey = BooleanSelectWidget.class, required = false)
  private Integer normal;

  @ApiModelProperty("酒类用品(逗号拼接)")
  @DynamicField(fieldName = "酒类用品(逗号拼接)" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = LiquorSelectWidget.class, required = false)
  private String liquorTypes;
}
