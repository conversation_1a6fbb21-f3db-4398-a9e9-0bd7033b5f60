package com.biz.crm.tpm.business.activities.dynamic.template.service.internal;

import com.biz.crm.tpm.business.activities.dynamic.template.repository.ActivityProductShareInfoRepository;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.ActivityProductShareInfo;
import com.biz.crm.tpm.business.activities.dynamic.template.service.ActivityProductShareInfoService;
import com.biz.crm.tpm.business.activities.dynamic.template.vo.ActivityProductShareInfoVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ActivityProductShareInfoServiceImpl implements ActivityProductShareInfoService {

  @Autowired
  private ActivityProductShareInfoRepository activityProductShareInfoRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  @Transactional
  public void save(List<ActivityProductShareInfo> shareInfos) {
    Validate.notEmpty(shareInfos,"活动明细商品分摊信息不能为空");
    for(ActivityProductShareInfo shareInfo : shareInfos){
      Validate.notBlank(shareInfo.getAssociateId(),"关联的活动明细id键值不能为空");
      Validate.notBlank(shareInfo.getProductCode(),"产品编码不能为空");
      Validate.notBlank(shareInfo.getProductName(),"产品名称不能为空");
      Validate.notNull(shareInfo.getRatio(),"比例不能为空");
      Validate.isTrue(shareInfo.getRatio().compareTo(BigDecimal.ZERO) > 0,"比例必须大于0");
      if(StringUtils.isBlank(shareInfo.getTenantCode())){
        shareInfo.setTenantCode(TenantUtils.getTenantCode());
      }
    }
    long size = shareInfos.stream().map(ActivityProductShareInfo::getAssociateId).distinct().count();
    Validate.isTrue(1 == size,"活动明细商品分摊信息含有多个不同的活动明细，请检查associateId键值");
    long productSize = shareInfos.stream().map(ActivityProductShareInfo::getProductCode).distinct().count();
    Validate.isTrue(productSize == shareInfos.size(),"分摊的产品信息重复，请检查");
    BigDecimal totalRatio = shareInfos.stream().map(ActivityProductShareInfo::getRatio)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO,BigDecimal::add);
    Validate.isTrue(totalRatio.compareTo(BigDecimal.valueOf(100)) == 0,"产品的分摊比例之和必须为100");
    List<ActivityProductShareInfo> dbShareInfos = activityProductShareInfoRepository.findByAssociateId(shareInfos.get(0).getAssociateId());
    if(!CollectionUtils.isEmpty(dbShareInfos)){
      //暴力删除历史信息
      activityProductShareInfoRepository.removeByIdsAndTenantCode(dbShareInfos.stream().map(ActivityProductShareInfo::getId).collect(Collectors.toList()),TenantUtils.getTenantCode());
    }
    //保存信息
    activityProductShareInfoRepository.saveBatch(shareInfos);
  }

  @Override
  public List<ActivityProductShareInfoVo> findByAssociateIds(Set<String> associateIds) {
    if(CollectionUtils.isEmpty(associateIds)){
      return Lists.newArrayList();
    }
    List<ActivityProductShareInfo> shareInfos = activityProductShareInfoRepository.findByAssociateIds(associateIds);
    if(CollectionUtils.isEmpty(shareInfos)){
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(shareInfos,ActivityProductShareInfo.class,ActivityProductShareInfoVo.class, HashSet.class, ArrayList.class));
  }

  @Override
  @Transactional
  public void deleteByAssociateIds(Set<String> associateIds) {
    Validate.notEmpty(associateIds,"关联的活动明细id主键不能为空");
    activityProductShareInfoRepository.deleteByAssociateIds(associateIds);
  }

}
