package com.biz.crm.tpm.business.activities.dynamic.template.service.internal;

import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.activities.dynamic.template.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.BanquetTemplate;
import com.biz.crm.tpm.business.activities.dynamic.template.enums.BanquetTypeEnum;
import com.biz.crm.tpm.business.activities.dynamic.template.enums.LiquorTypeEnum;
import com.biz.crm.tpm.business.activities.dynamic.template.service.BanquetTemplateService;
import com.biz.crm.tpm.business.activities.dynamic.template.repository.BanquetTemplateRepository;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class BanquetTemplateServiceImpl implements BanquetTemplateService {

  @Autowired
  private BanquetTemplateRepository banquetTemplateRepository;
  @Autowired
  private GenerateCodeService generateCodeService;

  @Override
  @Transactional
  public void create(BanquetTemplate item) {
    this.validate(item);
    Validate.isTrue(StringUtils.isBlank(item.getId()),"新增时，主键id不能有值");
    item.setId(null);
    List<String> codeList = this.generateCodeService.generateCode(ActivitiesConstant.BANQUET_MX_CODE_KEY, 1);
    item.setItemCode(codeList.get(0));
    item.setTenantCode(TenantUtils.getTenantCode());
    banquetTemplateRepository.save(item);
  }

  @Override
  @Transactional
  public void update(BanquetTemplate banquetTemplate) {
    Validate.notNull(banquetTemplate,"通用采集模板信息不能为空");
    this.validate(banquetTemplate);
    String parentCode = banquetTemplate.getParentCode();
    BanquetTemplate dbBanquetTemplate =  banquetTemplateRepository.findByParentCodeAndTenantCode(parentCode,TenantUtils.getTenantCode());
    Validate.notNull(dbBanquetTemplate,"根据提供的主表编码【%s】，未能获取到相应信息",parentCode);
    dbBanquetTemplate.setLabel(banquetTemplate.getLabel());
    dbBanquetTemplate.setBanquetDate(banquetTemplate.getBanquetDate());
    dbBanquetTemplate.setLiquorTypes(banquetTemplate.getLiquorTypes());
    dbBanquetTemplate.setNormal(banquetTemplate.getNormal());
    dbBanquetTemplate.setPeoples(banquetTemplate.getPeoples());
    dbBanquetTemplate.setType(banquetTemplate.getType());
    dbBanquetTemplate.setTenantCode(TenantUtils.getTenantCode());
    banquetTemplateRepository.saveOrUpdate(dbBanquetTemplate);
  }

  @Override
  @Transactional
  public void deleteByParentCode(String parentCode) {
    Validate.notBlank(parentCode,"主活动编码信息不能为空");
    BanquetTemplate banquetTemplate = banquetTemplateRepository.findByParentCodeAndTenantCode(parentCode,TenantUtils.getTenantCode());
    if(banquetTemplate == null){
      return;
    }
    banquetTemplateRepository.removeByIdAndTenantCode(banquetTemplate.getId(),TenantUtils.getTenantCode());
  }

  private void validate(BanquetTemplate item){
    Validate.notNull(item,"通用采集模板信息不能为空");
    Validate.notBlank(item.getParentCode(),"主表编码不能为空");
    Validate.notBlank(item.getDynamicKey(),"业务key键值不能为空");
    Validate.notNull(item.getBanquetDate(),"宴会日期不能为空");
    Validate.notNull(item.getPeoples(),"宴会人数不能为空");
    Validate.notBlank(item.getType(),"宴会类型不能为空");
    if(StringUtils.isNotBlank(item.getType())){
      Validate.isTrue(BanquetTypeEnum.contains(item.getType()),"宴会类型枚举值【%s】不匹配",item.getType());
    }
    if(StringUtils.isNotBlank(item.getLiquorTypes())){
      String[] liquorTypes = StringUtils.split(item.getLiquorTypes(),",");
      for(String liquorType : liquorTypes){
        Validate.isTrue(LiquorTypeEnum.contains(liquorType),"酒类用品枚举值【%s】不匹配",item.getType());
      }
    }
    if(StringUtils.isBlank(item.getTenantCode())){
      item.setTenantCode(TenantUtils.getTenantCode());
    }
  }

}
