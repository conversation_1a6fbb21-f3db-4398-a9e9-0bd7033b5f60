package com.biz.crm.tpm.business.activities.dynamic.template.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.lang3.StringUtils;

/**
 * 动态表单类型
 */
public enum FormTypeEnum {
  APPLY("申请", "apply"),
  EXECUTION("执行", "execution"),
  AUDIT("核销", "audit");

  public static boolean contains(String code){
    if(StringUtils.isBlank(code)){
      return false;
    }
    for(FormTypeEnum e : values()){
      if(StringUtils.equals(code,e.getCode())){
        return true;
      }
    }
    return false;
  }

  @EnumValue
  @JsonValue
  private String code;

  private String name;

  FormTypeEnum(String name, String code) {
    this.code = code;
    this.name = name;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }
}
