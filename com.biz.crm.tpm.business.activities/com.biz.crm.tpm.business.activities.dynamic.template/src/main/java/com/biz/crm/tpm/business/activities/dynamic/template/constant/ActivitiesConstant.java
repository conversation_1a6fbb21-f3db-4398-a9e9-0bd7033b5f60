package com.biz.crm.tpm.business.activities.dynamic.template.constant;

public interface ActivitiesConstant {
  /**
   * 动态活动明细-动态表单上下文-总金额键值key
   * source 原总金额
   * target 目标总金额(变更后的总金额)
   * sum 累计总金额(用于过程累计计算)
   */
  String SOURCE_TOTAL_APPLY_AMOUNT_KEY = "sourceTotalApplyAmount";
  String TARGET_TOTAL_APPLY_AMOUNT_KEY = "targetTotalApplyAmount";
  String SUM_TOTAL_APPLY_AMOUNT_KEY = "sumTotalApplyAmount";

  /**
   * 动态活动明细-动态表单上下文-活动开始时间键值key
   */
  String ACTIVITY_START_TIME_KEY = "startTime";

  /**
   * 动态活动明细-动态表单上下文-活动结束时间键值key
   */
  String ACTIVITY_END_TIME_KEY = "endTime";

  /**
   * 陈列活动明细编码前缀
   */
  String DISPLAY_MX_CODE_KEY = "CLMX";

  /**
   * 物料领用活动明细编码前缀
   */
  String MATERIAL_MX_CODE_KEY = "WLMX";

  /**
   * 定额活动明细编码前缀
   */
  String QUOTA_MX_CODE_KEY = "DEMX";

  /**
   * 通用活动字段采集明细编码前缀
   */
  String GENERAL_TEMPLATE_MX_CODE_KEY = "GTMX";

  /**
   * 宴席活动字段采集明细编码前缀
   */
  String BANQUET_MX_CODE_KEY = "BQMX";
}
