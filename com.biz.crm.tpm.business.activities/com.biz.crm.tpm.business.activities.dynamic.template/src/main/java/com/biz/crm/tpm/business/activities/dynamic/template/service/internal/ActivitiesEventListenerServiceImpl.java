package com.biz.crm.tpm.business.activities.dynamic.template.service.internal;

import com.biz.crm.tpm.business.activities.dynamic.template.repository.DisplayActivityItemRepository;
import com.biz.crm.tpm.business.activities.dynamic.template.repository.MaterialActivityItemRepository;
import com.biz.crm.tpm.business.activities.dynamic.template.repository.QuotaActivityItemRepository;
import com.biz.crm.tpm.business.activities.dynamic.template.service.ActivitiesEventListenerService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 目前示例有3个动态活动表单，所以就实现了3个动态活动表单的监听实现逻辑
 * 注：假如以后还加入了动态活动表单，需要根据实际情况在此进行逻辑添加
 */
@Service
public class ActivitiesEventListenerServiceImpl implements ActivitiesEventListenerService {

  @Autowired
  private QuotaActivityItemRepository quotaActivityItemRepository;
  @Autowired
  private MaterialActivityItemRepository materialActivityItemRepository;
  @Autowired
  private DisplayActivityItemRepository displayActivityItemRepository;

  @Override
  public boolean existItemsByCostTypeDetailCode(String costTypeDetailCode) {
    if(StringUtils.isBlank(costTypeDetailCode)){
      return false;
    }
    int quotaItemCount = quotaActivityItemRepository.countByCostTypeDetailCodeAndTenantCode(costTypeDetailCode, TenantUtils.getTenantCode());
    if(quotaItemCount > 0){
      return true;
    }
    int displayItemCount = displayActivityItemRepository.countByCostTypeDetailCodeAndTenantCode(costTypeDetailCode,TenantUtils.getTenantCode());
    if(displayItemCount > 0){
      return true;
    }
    int materialItemCount = materialActivityItemRepository.countByCostTypeDetailCodeAndTenantCode(costTypeDetailCode,TenantUtils.getTenantCode());
    return materialItemCount > 0;
  }
}
