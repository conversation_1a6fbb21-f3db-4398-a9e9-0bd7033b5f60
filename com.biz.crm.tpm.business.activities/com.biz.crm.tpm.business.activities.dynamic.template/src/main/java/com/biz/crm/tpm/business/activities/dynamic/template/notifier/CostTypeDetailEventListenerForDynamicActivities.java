package com.biz.crm.tpm.business.activities.dynamic.template.notifier;

import com.biz.crm.tpm.business.activities.dynamic.template.service.ActivitiesEventListenerService;
import com.biz.crm.tpm.business.budget.sdk.event.CostTypeDetailEventListener;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CostTypeDetailEventListenerForDynamicActivities implements CostTypeDetailEventListener {

  @Autowired
  private ActivitiesEventListenerService activitiesEventListenerService;

  @Override
  public void onDeleted(CostTypeDetailVo costTypeDetailVo) {
    Validate.notNull(costTypeDetailVo,"活动细类信息不能为空");
    Validate.notBlank(costTypeDetailVo.getDetailCode(),"活动细类编码不能为空");
    boolean exist = activitiesEventListenerService.existItemsByCostTypeDetailCode(costTypeDetailVo.getDetailCode());
    Validate.isTrue(!exist,"活动细类【%s】已存在活动申请数据，不能进行删除",costTypeDetailVo.getDetailCode());
  }

}
