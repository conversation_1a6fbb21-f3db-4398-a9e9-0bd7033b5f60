package com.biz.crm.tpm.business.activities.dynamic.template.vo;

import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.field.annotation.Validate;
import com.biz.crm.common.form.sdk.field.validate.NotBlankValidateStrategy;
import com.biz.crm.common.form.sdk.field.validate.NotNullValidateStrategy;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.BooleanSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.activities.sdk.strategy.validate.BigDecimalGtZeroValidateStrategy;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "DisplayActivityItemVo", description = "陈列动态模板vo")
public class DisplayActivityItemVo extends BaseActivityItemVo implements DynamicForm {

  @ApiModelProperty("活动大类编码")
  @DynamicField(fieldName = "活动大类编码" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  @DynamicField(fieldName = "活动大类名称" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String costTypeCategoryName;

  @ApiModelProperty("费用预算编码")
  @DynamicField(fieldName = "费用预算编码" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String costBudgetCode;

  @ApiModelProperty("活动细类编码")
  @DynamicField(fieldName = "活动细类编码" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String costTypeDetailCode;

  @ApiModelProperty("活动细类名称")
  @DynamicField(fieldName = "活动细类名称" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String costTypeDetailName;

  @ApiModelProperty(name = "预算科目编码",notes = "")
  @DynamicField(fieldName = "预算科目编码" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String budgetSubjectsCode;

  @ApiModelProperty(name = "预算科目名称",notes = "")
  @DynamicField(fieldName = "预算科目名称" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String budgetSubjectsName;

  @ApiModelProperty("组织编码")
  @DynamicField(fieldName = "组织编码" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String orgCode;

  @ApiModelProperty("组织名称")
  @DynamicField(fieldName = "组织名称" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String orgName;

  @ApiModelProperty("客户编码")
  @DynamicField(fieldName = "客户编码" , required = false, controllKey = SimpleInputWidget.class)
  private String customerCode;

  @ApiModelProperty("客户名称")
  @DynamicField(fieldName = "客户名称" , required = false, controllKey = SimpleInputWidget.class)
  private String customerName;

  @ApiModelProperty("终端编码")
  @DynamicField(fieldName = "终端编码" , required = false, controllKey = SimpleInputWidget.class)
  private String terminalCode;

  @ApiModelProperty("终端名称")
  @DynamicField(fieldName = "终端名称" , required = false, controllKey = SimpleInputWidget.class)
  private String terminalName;

  @ApiModelProperty("陈列方式")
  @DynamicField(fieldName = "陈列方式" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String displayType;

  @ApiModelProperty("陈列方式名称")
  @DynamicField(fieldName = "陈列方式名称" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String displayTypeName;

  @ApiModelProperty("陈列商品编码(逗号拼接)")
  @DynamicField(fieldName = "陈列商品编码(逗号拼接)" , controllKey = SimpleInputWidget.class, required = false)
  private String displayProductCodes;

  @ApiModelProperty("陈列商品名称(逗号拼接)")
  @DynamicField(fieldName = "陈列商品编码(逗号拼接)" , controllKey = SimpleInputWidget.class, required = false)
  private String displayProductNames;

  @ApiModelProperty("陈列商品层级编码(逗号拼接)")
  @DynamicField(fieldName = "陈列商品层级编码(逗号拼接)" , controllKey = SimpleInputWidget.class, required = false)
  private String displayProductLevelCodes;

  @ApiModelProperty("陈列商品层级名称(逗号拼接)")
  @DynamicField(fieldName = "陈列商品层级名称(逗号拼接)" , controllKey = SimpleInputWidget.class, required = false)
  private String displayProductLevelNames;

  @ApiModelProperty("申请金额")
  @DynamicField(fieldName = "申请金额" , validates = {@Validate(value = BigDecimalGtZeroValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private BigDecimal applyAmount;

  @ApiModelProperty("支付方式")
  @DynamicField(fieldName = "支付方式" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String payType;

  @ApiModelProperty("支付方式名称")
  @DynamicField(fieldName = "支付方式名称" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String payTypeName;

  @ApiModelProperty("备注")
  @DynamicField(fieldName = "备注" , required = false , controllKey = SimpleInputWidget.class)
  private String remark;

  @ApiModelProperty("费用日期(年月)")
  @DynamicField(fieldName = "费用日期(年月)" , validates = {@Validate(value = NotBlankValidateStrategy.class)} , controllKey = SimpleInputWidget.class, required = false)
  private String feeDate;

  @ApiModelProperty("是否关闭")
  @DynamicField(fieldName = "是否关闭)" , validates = {@Validate(value = NotNullValidateStrategy.class)} , controllKey = BooleanSelectWidget.class, required = false)
  private Boolean colsed;

  @ApiModelProperty("活动明细商品分摊")
  List<ActivityProductShareInfoVo> shareInfos;
}
