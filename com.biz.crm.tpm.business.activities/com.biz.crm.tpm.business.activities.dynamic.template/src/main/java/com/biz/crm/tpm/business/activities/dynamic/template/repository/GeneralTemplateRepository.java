package com.biz.crm.tpm.business.activities.dynamic.template.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.GeneralTemplate;
import com.biz.crm.tpm.business.activities.dynamic.template.mapper.GeneralTemplateMapper;
import org.springframework.stereotype.Component;

/**
 * 通用采集模板(GeneralTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class GeneralTemplateRepository extends ServiceImpl<GeneralTemplateMapper, GeneralTemplate> {
  /**
   * 根据主活动编码，查询数据
   */
  public GeneralTemplate findByParentCodeAndTenantCode(String parentCode, String tenantCode){
    return this.lambdaQuery().eq(GeneralTemplate::getParentCode,parentCode).
            eq(GeneralTemplate::getTenantCode,tenantCode).
            one();
  }

  /**
   * 通过id和租户编号删除
   * @param id
   * @param tenantCode
   */
  public void removeByIdAndTenantCode(String id, String tenantCode) {
    this.lambdaUpdate()
        .eq(GeneralTemplate::getTenantCode,tenantCode)
        .in(GeneralTemplate::getId,id)
        .remove();
  }
}

