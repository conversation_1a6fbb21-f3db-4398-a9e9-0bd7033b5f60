package com.biz.crm.tpm.business.activities.dynamic.template.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.MaterialActivityItem;
import com.biz.crm.tpm.business.activities.dynamic.template.mapper.MaterialActivityItemMapper;
import com.biz.crm.tpm.business.activities.sdk.enums.ActivityStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 物料领用活动动态模板明细(MaterialActivityItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class MaterialActivityItemRepository extends ServiceImpl<MaterialActivityItemMapper, MaterialActivityItem> {
  /**
   * 根据主业务编码parentCode和业务dynamicKey键值，查询数据
   */
  public List<MaterialActivityItem> findByDynamicKeyAndParentCodeAndTenantCode(String dynamicKey, String parentCode, String tenantCode){
    return this.lambdaQuery().eq(MaterialActivityItem::getParentCode,parentCode).
            eq(MaterialActivityItem::getDynamicKey,dynamicKey).
            eq(MaterialActivityItem::getTenantCode,tenantCode).
            orderByAsc(MaterialActivityItem::getItemCode).orderByAsc(MaterialActivityItem::getFeeDate).
            list();
  }

  /**
   * 根据活动细类编码，统计数据
   */
  public int countByCostTypeDetailCodeAndTenantCode(String costTypeDetailCode, String tenantCode){
    return this.lambdaQuery().eq(MaterialActivityItem::getCostTypeDetailCode,costTypeDetailCode).
            eq(MaterialActivityItem::getTenantCode,tenantCode).
            count();
  }

  /**
   * 根据主活动编码，查询数据
   */
  public List<MaterialActivityItem> findByParentCodeAndTenantCode(String parentCode, String tenantCode){
    return this.lambdaQuery().eq(MaterialActivityItem::getParentCode,parentCode).
            eq(MaterialActivityItem::getTenantCode,tenantCode).
            list();
  }

  /**
   * 关闭明细活动
   */
  public void updateForClose(Set<String> itemCodes){
    this.lambdaUpdate()
        .in(MaterialActivityItem::getItemCode,itemCodes)
        .eq(MaterialActivityItem::getTenantCode, TenantUtils.getTenantCode())
        .set(MaterialActivityItem::getColsed,Boolean.TRUE)
        .update();
  }

  /**
   * 查询所有未关闭信息
   */
  public List<MaterialActivityItem> findAllUnclosed(){
    return this.lambdaQuery()
        .eq(MaterialActivityItem::getTenantCode,TenantUtils.getTenantCode())
        .notIn(MaterialActivityItem::getColsed, Sets.newHashSet(ActivityStatusEnum.PARTIAL_CLOSE.getCode(),ActivityStatusEnum.ALL_CLOSE.getCode()))
        .list();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(Set<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(MaterialActivityItem::getTenantCode,tenantCode)
        .in(MaterialActivityItem::getId,ids)
        .remove();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(MaterialActivityItem::getTenantCode,tenantCode)
        .in(MaterialActivityItem::getId,ids)
        .remove();
  }
}

