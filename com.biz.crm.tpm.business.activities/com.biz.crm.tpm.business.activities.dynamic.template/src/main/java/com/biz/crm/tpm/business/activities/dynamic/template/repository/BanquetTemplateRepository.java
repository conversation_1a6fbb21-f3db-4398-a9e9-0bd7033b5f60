package com.biz.crm.tpm.business.activities.dynamic.template.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.BanquetTemplate;
import com.biz.crm.tpm.business.activities.dynamic.template.mapper.BanquetTemplateMapper;
import org.springframework.stereotype.Component;

/**
 * 宴会采集模板(BanquetTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class BanquetTemplateRepository extends ServiceImpl<BanquetTemplateMapper, BanquetTemplate> {
  /**
   * 根据主活动编码，查询数据
   */
  public BanquetTemplate findByParentCodeAndTenantCode(String parentCode, String tenantCode){
    return this.lambdaQuery()
        .eq(BanquetTemplate::getParentCode,parentCode)
        .eq(BanquetTemplate::getTenantCode,tenantCode)
        .one();
  }

  /**
   * 通过id和租户编号删除
   * @param id
   * @param tenantCode
   */
  public void removeByIdAndTenantCode(String id, String tenantCode) {
    this.lambdaUpdate()
        .eq(BanquetTemplate::getTenantCode,tenantCode)
        .in(BanquetTemplate::getId,id)
        .remove();
  }
}

