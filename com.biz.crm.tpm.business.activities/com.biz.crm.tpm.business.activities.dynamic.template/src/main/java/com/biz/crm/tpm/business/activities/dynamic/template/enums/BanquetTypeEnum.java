package com.biz.crm.tpm.business.activities.dynamic.template.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.lang3.StringUtils;

/**
 * 宴会类型
 */
public enum BanquetTypeEnum {
  WEDDING("喜宴", "wedding"),
  BIRTHDAY("寿宴", "birthday"),
  FUNERAL("丧宴", "funeral"),
  ENGAGEMENT("订婚宴", "engagement"),
  FAMILY("家宴", "family"),
  OTHER("其他", "other");

  public static boolean contains(String code){
    if(StringUtils.isBlank(code)){
      return false;
    }
    for(BanquetTypeEnum e : values()){
      if(StringUtils.equals(code,e.getCode())){
        return true;
      }
    }
    return false;
  }

  @EnumValue
  @JsonValue
  private String code;

  private String name;

  BanquetTypeEnum(String name, String code) {
    this.code = code;
    this.name = name;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }
}
