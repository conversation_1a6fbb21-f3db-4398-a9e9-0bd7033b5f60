package com.biz.crm.tpm.business.activities.dynamic.template.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.dynamic.template.entity.QuotaActivityItem;
import com.biz.crm.tpm.business.activities.dynamic.template.mapper.QuotaActivityItemMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 普通活动明细(OrdinaryActivityItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class QuotaActivityItemRepository extends ServiceImpl<QuotaActivityItemMapper, QuotaActivityItem> {
  /**
   * 根据主业务编码parentCode和业务dynamicKey键值，查询数据
   */
  public List<QuotaActivityItem> findByDynamicKeyAndParentCodeAndTenantCode(String dynamicKey, String parentCode, String tenantCode){
    return this.lambdaQuery().eq(QuotaActivityItem::getParentCode,parentCode).
            eq(QuotaActivityItem::getDynamicKey,dynamicKey).
            eq(QuotaActivityItem::getTenantCode,tenantCode).
            orderByAsc(QuotaActivityItem::getItemCode).orderByAsc(QuotaActivityItem::getFeeDate).
            list();
  }

  /**
   * 根据活动细类编码，统计数据
   */
  public int countByCostTypeDetailCodeAndTenantCode(String costTypeDetailCode, String tenantCode){
    return this.lambdaQuery().eq(QuotaActivityItem::getCostTypeDetailCode,costTypeDetailCode).
            eq(QuotaActivityItem::getTenantCode,tenantCode).
            count();
  }

  /**
   * 根据主活动编码，查询数据
   */
  public List<QuotaActivityItem> findByParentCodeAndTenantCode(String parentCode, String tenantCode){
    return this.lambdaQuery().eq(QuotaActivityItem::getParentCode,parentCode).
            eq(QuotaActivityItem::getTenantCode,tenantCode).
            list();
  }

  /**
   * 关闭明细活动
   */
  public void updateForClose(Set<String> itemCodes){
    this.lambdaUpdate()
        .in(QuotaActivityItem::getItemCode,itemCodes)
        .eq(QuotaActivityItem::getTenantCode, TenantUtils.getTenantCode())
        .set(QuotaActivityItem::getColsed,Boolean.TRUE)
        .update();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(Set<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(QuotaActivityItem::getTenantCode,tenantCode)
        .in(QuotaActivityItem::getId,ids)
        .remove();
  }

  /**
   * 通过id和租户编号删除
   * @param ids
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(QuotaActivityItem::getTenantCode,tenantCode)
        .in(QuotaActivityItem::getId,ids)
        .remove();
  }

}

