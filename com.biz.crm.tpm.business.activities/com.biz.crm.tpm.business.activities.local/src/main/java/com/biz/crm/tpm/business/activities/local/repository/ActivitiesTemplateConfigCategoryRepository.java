package com.biz.crm.tpm.business.activities.local.repository;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesTemplateConfigCategory;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesTemplateConfigCategoryMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;


/**
 * 活动模板配置关联活动大类(ActivitiesTemplateConfigCategory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-31 15:57:01
 */
@Component
public class ActivitiesTemplateConfigCategoryRepository extends ServiceImpl<ActivitiesTemplateConfigCategoryMapper, ActivitiesTemplateConfigCategory> {

  @Autowired
  private ActivitiesTemplateConfigCategoryMapper activitiesTemplateConfigCategoryMapper;
  
  /**
   * 根据configId查询
   *
   * @param configCode
   * @return
   */
  public List<ActivitiesTemplateConfigCategory> findByConfigCode(String configCode) {
    return this.lambdaQuery().eq(ActivitiesTemplateConfigCategory::getConfigCode, configCode)
            .eq(ActivitiesTemplateConfigCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivitiesTemplateConfigCategory::getTenantCode, TenantUtils.getTenantCode()).list();
  }

  /**
   * 根据configCode删除
   *
   * @param configCodeList
   */
  public void deleteByConfigCodeList(List<String> configCodeList) {
    this.lambdaUpdate().in(ActivitiesTemplateConfigCategory::getConfigCode, configCodeList)
            .remove();
  }

  /**
   * 根据configId查询
   *
   * @param configCodes
   * @return
   */
  public List<ActivitiesTemplateConfigCategory> findByConfigCodes(Set<String> configCodes) {
    if (CollectionUtils.isEmpty(configCodes)) {
      return new ArrayList<>(0);
    }
    return this.lambdaQuery().in(ActivitiesTemplateConfigCategory::getConfigCode, configCodes)
            .eq(ActivitiesTemplateConfigCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivitiesTemplateConfigCategory::getTenantCode, TenantUtils.getTenantCode()).list();
  }
}

