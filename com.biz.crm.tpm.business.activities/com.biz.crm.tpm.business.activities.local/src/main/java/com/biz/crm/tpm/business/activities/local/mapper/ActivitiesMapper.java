package com.biz.crm.tpm.business.activities.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.local.entity.Activities;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动信息表;(tpm_activities)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-14
 */
@Mapper
public interface ActivitiesMapper extends BaseMapper<Activities> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto  动态查询条件
   * @return 分页对象列表
   */
  Page<ActivitiesVo> findByConditions(@Param("page") Page<ActivitiesVo> page, @Param("dto") ActivitiesDto dto);

  /**
   * 通过时间查询活动编号
   *
   * @param tenantCode 租户编号
   * @param startTime  开始时间
   * @param endTime    结束时间
   * @return 活动编号
   */
  List<String> findCodeByTime(@Param("tenantCode") String tenantCode, @Param("start_time") LocalDateTime startTime, @Param("end_time") LocalDateTime endTime);

  int countByCostTypeCategoryCode(@Param("tenantCode") String tenantCode, @Param("costTypeCategoryCode") String costTypeCategoryCode);
}
