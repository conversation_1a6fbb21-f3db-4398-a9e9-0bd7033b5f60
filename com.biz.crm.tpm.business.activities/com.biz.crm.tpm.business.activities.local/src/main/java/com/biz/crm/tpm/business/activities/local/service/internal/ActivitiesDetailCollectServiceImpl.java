package com.biz.crm.tpm.business.activities.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailCollect;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesDetailCollectRepository;
import com.biz.crm.tpm.business.activities.local.service.ActivitiesDetailCollectFilesService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailCollectService;
import com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailCollectDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectFilesVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;

/**
 * 活动明细采集信息表(SFA中的订单);(tpm_activities_detail_collect)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@Service("activitiesDetailCollectService")
public class ActivitiesDetailCollectServiceImpl implements ActivitiesDetailCollectService {
  @Autowired
  private ActivitiesDetailCollectRepository activitiesDetailCollectRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private ActivitiesDetailCollectFilesService activitiesDetailCollectFilesService;
  @Autowired
  private LoginUserService loginUserService;
  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<ActivitiesDetailCollectVo> findByConditions(Pageable pageable, ActivitiesDetailCollectDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesDetailCollectDto();
    }
    return this.activitiesDetailCollectRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public ActivitiesDetailCollectVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    ActivitiesDetailCollect activitiesDetailCollect = this.activitiesDetailCollectRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (activitiesDetailCollect == null) {
      return null;
    }
    ActivitiesDetailCollectVo activitiesDetailCollectVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailCollect, ActivitiesDetailCollectVo.class, LinkedHashSet.class, ArrayList.class);
    return activitiesDetailCollectVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<ActivitiesDetailCollectVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetailCollect> activitiesDetailCollects = this.activitiesDetailCollectRepository.findByIds(ids);
    Collection<ActivitiesDetailCollectVo> activitiesDetailCollectVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailCollects, ActivitiesDetailCollect.class, ActivitiesDetailCollectVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailCollectVos);
  }

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCode
   * @return 单条数据
   */
  @Override
  public List<ActivitiesDetailCollectVo> findByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return null;
    }
    List<ActivitiesDetailCollect> activitiesDetailCollects = this.activitiesDetailCollectRepository.findByActivitiesCode(activitiesCode);
    if (CollectionUtils.isEmpty(activitiesDetailCollects)) {
      return Collections.emptyList();
    }
    Collection<ActivitiesDetailCollectVo> activitiesDetailCollectVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailCollects, ActivitiesDetailCollect.class, ActivitiesDetailCollectVo.class, LinkedHashSet.class, ArrayList.class);
    activitiesDetailCollectVos.forEach(item -> {
      this.fillDetail(item);
    });
    return Lists.newArrayList(activitiesDetailCollectVos);
  }

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCodes
   * @return 多条数据
   */
  @Override
  public List<ActivitiesDetailCollectVo> findByActivitiesCodes(Collection<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Collections.emptyList();
    }
    List<ActivitiesDetailCollect> activitiesDetailCollects = this.activitiesDetailCollectRepository.findByActivitiesCodes(activitiesCodes);
    if (CollectionUtils.isEmpty(activitiesDetailCollects)) {
      return Collections.emptyList();
    }
    Collection<ActivitiesDetailCollectVo> activitiesDetailCollectVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailCollects, ActivitiesDetailCollect.class, ActivitiesDetailCollectVo.class, LinkedHashSet.class, ArrayList.class);
    activitiesDetailCollectVos.forEach(item -> {
      this.fillDetail(item);
    });
    return Lists.newArrayList(activitiesDetailCollectVos);
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCode
   * @return 单条数据
   */
  @Override
  public List<ActivitiesDetailCollectVo> findByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return Collections.emptyList();
    }
    List<ActivitiesDetailCollect> activitiesDetailCollects = this.activitiesDetailCollectRepository.findByActivitiesDetailCode(activitiesDetailCode);
    if (CollectionUtils.isEmpty(activitiesDetailCollects)) {
      return Collections.emptyList();
    }
    Collection<ActivitiesDetailCollectVo> activitiesDetailCollectVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailCollects, ActivitiesDetailCollect.class, ActivitiesDetailCollectVo.class, LinkedHashSet.class, ArrayList.class);
    activitiesDetailCollectVos.forEach(this::fillDetail);
    return Lists.newArrayList(activitiesDetailCollectVos);
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCodes
   * @return 多条数据
   */
  @Override
  public List<ActivitiesDetailCollectVo> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes) {
    if (CollectionUtils.isEmpty(activitiesDetailCodes)) {
      return Collections.emptyList();
    }
    List<ActivitiesDetailCollect> activitiesDetailCollects = this.activitiesDetailCollectRepository.findByActivitiesDetailCodes(activitiesDetailCodes);
    if (CollectionUtils.isEmpty(activitiesDetailCollects)) {
      return Collections.emptyList();
    }
    Collection<ActivitiesDetailCollectVo> activitiesDetailCollectVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailCollects, ActivitiesDetailCollect.class, ActivitiesDetailCollectVo.class, LinkedHashSet.class, ArrayList.class);
    activitiesDetailCollectVos.forEach(item -> {
      this.fillDetail(item);
    });
    return Lists.newArrayList(activitiesDetailCollectVos);
  }

  /**
   * 新增数据
   *
   * @param activitiesDetailCollectDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public ActivitiesDetailCollectVo create(ActivitiesDetailCollectDto activitiesDetailCollectDto) {
    this.createValidate(activitiesDetailCollectDto);
    activitiesDetailCollectDto.setCreateTime(new Date());
    if(StringUtils.isBlank(activitiesDetailCollectDto.getTenantCode())){
      activitiesDetailCollectDto.setTenantCode(TenantUtils.getTenantCode());
    }
    ActivitiesDetailCollect activitiesDetailCollect = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailCollectDto, ActivitiesDetailCollect.class, LinkedHashSet.class, ArrayList.class);
    String code = this.generateCodeService.generateCode(ActivitiesConstant.COLLECT_LADDER_CODE);
    activitiesDetailCollect.setCollectCode(code);
    activitiesDetailCollect.setTenantCode(TenantUtils.getTenantCode());
    FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
    activitiesDetailCollect.setCreateName(loginDetails.getUsername());
    this.activitiesDetailCollectRepository.saveOrUpdate(activitiesDetailCollect);
    ActivitiesDetailCollectVo activitiesDetailCollectVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailCollect, ActivitiesDetailCollectVo.class, LinkedHashSet.class, ArrayList.class);

    if (!CollectionUtils.isEmpty(activitiesDetailCollectDto.getDetailCollectFiles())) {
      activitiesDetailCollectDto.getDetailCollectFiles().forEach(e -> e.setCollectCode(code));
      List<ActivitiesDetailCollectFilesVo> detailCollectFilesVos = this.activitiesDetailCollectFilesService.createBatch(activitiesDetailCollectDto.getDetailCollectFiles());
      activitiesDetailCollectVo.setDetailCollectFiles(detailCollectFilesVos);
    }

    activitiesDetailCollectVo.setId(activitiesDetailCollect.getId());
    return activitiesDetailCollectVo;
  }

  /**
   * 批量新增
   *
   * @param activitiesDetailCollectDtos
   * @return
   */
  @Transactional
  @Override
  public List<ActivitiesDetailCollectVo> createBatch(Collection<ActivitiesDetailCollectDto> activitiesDetailCollectDtos) {
    if (CollectionUtils.isEmpty(activitiesDetailCollectDtos)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetailCollectVo> activitiesDetailCollectVos = Lists.newArrayList();
    for (ActivitiesDetailCollectDto activitiesDetailCollectDto : activitiesDetailCollectDtos) {
      ActivitiesDetailCollectVo activitiesDetailCollectVo = this.create(activitiesDetailCollectDto);
      activitiesDetailCollectVos.add(activitiesDetailCollectVo);
    }
    return activitiesDetailCollectVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<ActivitiesDetailCollect> activitiesDetailCollects = this.activitiesDetailCollectRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(activitiesDetailCollects)) {
      return;
    }
    Collection<ActivitiesDetailCollectVo> activitiesDetailCollectVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailCollects, ActivitiesDetailCollect.class, ActivitiesDetailCollectVo.class, LinkedHashSet.class, ArrayList.class);
    this.activitiesDetailCollectRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  /**
   * 创建验证
   *
   * @param activitiesDetailCollectDto
   */
  private void createValidate(ActivitiesDetailCollectDto activitiesDetailCollectDto) {
    Validate.notNull(activitiesDetailCollectDto, "新增时，对象信息不能为空！");
//    Validate.isTrue(activitiesDetailCollectDto.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(activitiesDetailCollectDto.getActivitiesCode(), "新增数据时，活动编号不能为空！");
    Validate.notBlank(activitiesDetailCollectDto.getActivitiesName(), "新增数据时，活动名称不能为空！");
    Validate.notBlank(activitiesDetailCollectDto.getActivitiesDetailCode(), "新增数据时，活动明细编号不能为空！");
    Validate.notBlank(activitiesDetailCollectDto.getBtNo(), "新增数据时，提交数据批次编号不能为空！");
    Validate.notBlank(activitiesDetailCollectDto.getTerminalCode(), "新增数据时，终端编号不能为空！");
    Validate.notBlank(activitiesDetailCollectDto.getTerminalName(), "新增数据时，终端名称不能为空！");
  }

  private void fillDetail(ActivitiesDetailCollectVo activitiesDetailCollectVo) {
    List<ActivitiesDetailCollectFilesVo> detailCollectFilesVos = this.activitiesDetailCollectFilesService.findByCollectCode(activitiesDetailCollectVo.getCollectCode());
    activitiesDetailCollectVo.setDetailCollectFiles(detailCollectFilesVos);
  }

}
