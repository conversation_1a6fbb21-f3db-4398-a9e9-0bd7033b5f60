package com.biz.crm.tpm.business.activities.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailCollectFilesDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectFilesVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 活动明细采集附件信息表(SFA中的订单);(tpm_activities_detail_collect_files)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
public interface ActivitiesDetailCollectFilesService {
  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<ActivitiesDetailCollectFilesVo> findByConditions(Pageable pageable, ActivitiesDetailCollectFilesDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  ActivitiesDetailCollectFilesVo findById(String id);

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<ActivitiesDetailCollectFilesVo> findByIds(Collection<String> ids);

  /**
   * 新增数据
   *
   * @param activitiesDetailCollectFilesDto 实体对象
   * @return 新增结果
   */
  ActivitiesDetailCollectFilesVo create(ActivitiesDetailCollectFilesDto activitiesDetailCollectFilesDto);

  /**
   * 批量新增
   *
   * @param activitiesDetailCollectFilesDtos
   * @return
   */
  List<ActivitiesDetailCollectFilesVo> createBatch(Collection<ActivitiesDetailCollectFilesDto> activitiesDetailCollectFilesDtos);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);

  /**
   * 通过主键查询多条数据
   *
   * @param collectCode 活动收集编号
   * @return 多条数据
   */
  List<ActivitiesDetailCollectFilesVo> findByCollectCode(String collectCode);

}
