package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ActivitiesTemplateConfigCategory", description = "活动模板配置关联活动大类")
@Entity
@org.hibernate.annotations.Table(appliesTo = "`tpm_activities_template_config_category`", comment = "活动模板配置关联活动大类")
@TableName("tpm_activities_template_config_category")
@Table(
        name = "`tpm_activities_template_config_category`",
        indexes = {
            @Index(name = "index_config_code", columnList = "config_code"),
        }
)
public class ActivitiesTemplateConfigCategory extends TenantFlagOpEntity {


    /**
     * 配置编码
     */
    @ApiModelProperty("配置编码")
    @Column(name = "config_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '配置编码'")
    private String configCode;

    @ApiModelProperty("活动大类编码")
    @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
    private String categoryName;
}
