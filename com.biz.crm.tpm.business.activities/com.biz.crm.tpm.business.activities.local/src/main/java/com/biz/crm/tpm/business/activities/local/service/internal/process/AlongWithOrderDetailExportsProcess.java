package com.biz.crm.tpm.business.activities.local.service.internal.process;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.tpm.business.activities.local.service.internal.AlongWithOrderPageCacheHelper;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDetailDto;
import com.biz.crm.tpm.business.activities.sdk.service.AlongWithOrderService;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderDetailExportVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 随单比例缓存导出
 *
 * @author: yaoyongming
 * @date: 2024/6/3 23:14
 */
@Slf4j
@Component
public class AlongWithOrderDetailExportsProcess implements ExportProcess<AlongWithOrderDetailExportVo> {

    @Autowired(required = false)
    private AlongWithOrderPageCacheHelper alongWithOrderPageCacheHelper;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private AlongWithOrderService alongWithOrderService;


    @Override
    public Integer getPageSize() {
        return CommonConstant.IE_EXPORT_PAGE_SIZE;
    }


    /**
     * 获取当前任务需要导出数据总记录数(用于拆分子任务)
     *
     * @param params 自定义参数
     * @return
     */
    @Override
    public Integer getTotal(Map<String, Object> params) {
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");
        Integer total = alongWithOrderPageCacheHelper.getTotal((String) params.get("cacheKey"));
        return total;
    }

    /**
     * 数据处理（最终处理）
     *
     * @param vo     导出任务处理器实体信息
     * @param params 扩展参数
     * @return
     */
    @Override
    public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");
        String cacheKey = (String) params.get("cacheKey");
        List<AlongWithOrderDetailDto> allCacheList = alongWithOrderService.findAllCacheList(cacheKey);
        Collection<AlongWithOrderDetailExportVo> exportsVos = nebulaToolkitService.copyCollectionByWhiteList(allCacheList, AlongWithOrderDetailDto.class, AlongWithOrderDetailExportVo.class, LinkedHashSet.class, ArrayList.class);
        return JSON.parseArray(JSON.toJSONString(exportsVos));
    }

    /**
     * 导出业务编码
     *
     * @return
     */
    @Override
    public String getBusinessCode() {
        return "along_with_order_detail_export";
    }

    /**
     * 导出业务名称
     *
     * @return
     */
    @Override
    public String getBusinessName() {
        return "随单比例缓存导出";
    }

    /**
     * 获取对象转换实体
     *
     * @return
     */
    @Override
    public Class<AlongWithOrderDetailExportVo> findCrmExcelVoClass() {
        return AlongWithOrderDetailExportVo.class;
    }
}
