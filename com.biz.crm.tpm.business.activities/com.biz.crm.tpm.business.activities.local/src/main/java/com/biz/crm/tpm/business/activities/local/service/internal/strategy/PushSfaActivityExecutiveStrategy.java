package com.biz.crm.tpm.business.activities.local.service.internal.strategy;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.sdk.strategy.activityExecutive.ActivityExecutiveStrategy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> rentao
 * @date : 2022/10/30 21:13
 */
@Component
@Deprecated
public class PushSfaActivityExecutiveStrategy implements ActivityExecutiveStrategy {

  /**
   * 策略的中文说明信息
   */
  private static final String ACTIVITY_EXECUTIVE = "是否推送sfa";

  @Override
  public String getActivitiesFormTypeStrategyCode() {
    return StringUtils.uncapitalize(this.getClass().getSimpleName());
  }

  @Override
  public String getActivitiesFormTypeStrategyName() {
    return ACTIVITY_EXECUTIVE;
  }

  @Override
  public void execute(JSONObject jsonObject) {
    //todo 执行方法需要扩展
  }
}
