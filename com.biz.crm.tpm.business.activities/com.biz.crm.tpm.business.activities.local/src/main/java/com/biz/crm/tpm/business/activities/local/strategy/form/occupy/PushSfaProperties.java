package com.biz.crm.tpm.business.activities.local.strategy.form.occupy;

import com.biz.crm.common.form.sdk.widget.BooleanSelectWidget;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormPropertiesField;
import lombok.Data;

/**
 * 描述：</br>费用预占属性配置项目
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Data
public class PushSfaProperties implements FormProperties {
  /**
   * 是否启用
   */
  @FormPropertiesField(name = "是否推送SFA", required = true, controllKey = BooleanSelectWidget.class)
  private boolean pushSfa;

  /**
   * 是否采集活动数据
   */
  @FormPropertiesField(name = "是否采集活动数据", required = true, controllKey = BooleanSelectWidget.class)
  private boolean collectActivityData;
  /**
   * 是否采集活动数据
   */
  @FormPropertiesField(name = "是否采集分销订单", required = true, controllKey = BooleanSelectWidget.class)
  private boolean collectDistributionOrders;
  /**
   * 是否签署陈列协议
   */
  @FormPropertiesField(name = "是否签署陈列协议", required = true, controllKey = BooleanSelectWidget.class)
  private boolean signDisplay;
  /**
   * 是否控制活动金额
   */
  @FormPropertiesField(name = "是否控制活动金额", required = true, controllKey = BooleanSelectWidget.class)
  private boolean controlActivityExpenses;


}
