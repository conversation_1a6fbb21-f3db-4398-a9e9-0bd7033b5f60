<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.local.mapper.ActivitiesConfigDetailMapper">

  <resultMap type="com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfigDetail" id="TpmActivitiesConfigDetailMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="activitiesCode" column="activities_code" jdbcType="VARCHAR"/>
    <result property="activitiesFormTypeStrategyCode" column="activities_form_type_strategy_code" jdbcType="VARCHAR"/>
    <result property="activitiesFormTypeStrategyName" column="activities_form_type_strategy_name" jdbcType="VARCHAR"/>
    <result property="necessary" column="necessary" jdbcType="NUMERIC"/>
  </resultMap>
  
  <sql id = "tpmActivitiesConfigDetail">
    tenant_code tenantCode,
    necessary necessary,
    activities_code activitiesCode,
    activities_form_type_strategy_code activitiesFormTypeStrategyCode,
    activities_form_type_strategy_name activitiesFormTypeStrategyName,
    id id
  </sql>

  <select id="findByConditions" resultMap="TpmActivitiesConfigDetailMap">
    select
      *
    from tpm_activities_config_detail
    where
    tenant_code=#{dto.tenantCode}
  </select>
</mapper>

