package com.biz.crm.tpm.business.activities.local.strategy.form.audit;

import com.biz.crm.common.form.sdk.widget.BooleanSelectWidget;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormPropertiesField;
import lombok.Data;

/**
 * 描述：</br>多次核销属性配置项目
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Data
public class MultiAuditFormProperties implements FormProperties {
  /**
   * 是否多次核销
   */
  @FormPropertiesField(name = "是否多次核销", required = true, controllKey = BooleanSelectWidget.class)
  private boolean enabled;
}
