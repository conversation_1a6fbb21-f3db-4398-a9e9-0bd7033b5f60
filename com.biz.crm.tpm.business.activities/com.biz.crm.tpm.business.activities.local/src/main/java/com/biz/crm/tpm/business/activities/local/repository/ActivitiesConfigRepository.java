package com.biz.crm.tpm.business.activities.local.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesConfigMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 活动表单配置(ActivitiesConfig)表相关
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:51
 */
@Component
public class ActivitiesConfigRepository extends ServiceImpl<ActivitiesConfigMapper, ActivitiesConfig> {

  @Autowired
  private ActivitiesConfigMapper activitiesConfigMapper;


  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<ActivitiesConfigVo> findByConditions(Pageable pageable, ActivitiesConfigDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    Page<ActivitiesConfigVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesConfigMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<ActivitiesConfig>
   */
  public List<ActivitiesConfig> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesConfig::getId, ids)
            .eq(ActivitiesConfig::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 活动表单类型编号查询所有数据
   *
   * @param activitiesFormTypeCode 活动表单类型编号
   * @return 所有数据
   */
  public List<ActivitiesConfig> findByActivitiesFormTypeCode(String activitiesFormTypeCode,String enableStatus) {
    if (StringUtils.isEmpty(activitiesFormTypeCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesConfig::getActivitiesFormTypeCode, activitiesFormTypeCode)
            .eq(ActivitiesConfig::getEnableStatus, enableStatus)
            .eq(ActivitiesConfig::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivitiesConfig::getTenantCode, tenantCode)
            .list();
  }


  /**
   * 活动配置编号查询单条数据
   *
   * @param activitiesConfigCode 活动配置编号
   * @return 单条数据
   */
  public ActivitiesConfig findByActivitiesConfigCode(String activitiesConfigCode) {
    if (StringUtils.isEmpty(activitiesConfigCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesConfig::getActivitiesConfigCode, activitiesConfigCode)
            .eq(ActivitiesConfig::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivitiesConfig::getTenantCode, tenantCode)
            .one();
  }

  /**
   * 批量根据id禁用
   *
   * @param enable
   * @param ids
   */
  public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
    UpdateWrapper<ActivitiesConfig> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("enable_status", enable.getCode());
    updateWrapper.in("id", ids);
    updateWrapper.eq("tenant_code",TenantUtils.getTenantCode());
    this.update(updateWrapper);
  }

  public ActivitiesConfig findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesConfig::getTenantCode,tenantCode)
        .in(ActivitiesConfig::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ActivitiesConfig::getTenantCode,tenantCode)
        .in(ActivitiesConfig::getId,ids)
        .remove();
  }
}
