package com.biz.crm.tpm.business.activities.local.strategy.form.audit;

import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.tpm.business.activities.local.strategy.form.AbstractFormEventStrategy;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

/**
 * 描述：</br>多次核销策略
 *
 * <AUTHOR>
 * @date 2022/11/7
 */
@Component
@Slf4j
public class MultiAuditFormEventStrategy extends AbstractFormEventStrategy {
  @Autowired(required = false)
  private AuditDetailService auditDetailService;
  @Autowired(required = false)
  private ActivitiesService activitiesService;
  @Autowired(required = false)
  private CostTypeDetailVoService costTypeDetailVoService;

  @Override
  public String getCode() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String getName() {
    return "多次核销";
  }

  @Override
  public Class<? extends FormProperties> getFormPropertiesInfo() {
    return MultiAuditFormProperties.class;
  }

  @Override
  @Transactional
  public <T extends DynamicForm> Object handle(String businessCode, T data, Map<String, ?> params) {
    Validate.notBlank(businessCode, "活动编号为空，请检查");
    ActivitiesVo activitiesVo = this.activitiesService.findDetailsByActivitiesCode(businessCode);
    Validate.notNull(activitiesVo, "活动数据错误，请检查");
    List<ActivitiesDetailVo> activitiesDetailVos = activitiesVo.getActivitiesDetails();
    if (!CollectionUtils.isEmpty(activitiesDetailVos)) {
      activitiesDetailVos.forEach(activitiesDetailVo -> {
        CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(activitiesDetailVo.getCostTypeDetailCode());
        int detailCount = this.auditDetailService.countByCostTypeAndActivitiesDetail(costTypeDetailVo.getDetailCode(), activitiesDetailVo.getActivitiesDetailCode());
        Validate.isTrue(detailCount == 0, "活动【%s】-【%s】不允许多次核销，请检查！", costTypeDetailVo.getDetailCode(), costTypeDetailVo.getDetailName());
      });
    }
    return null;
  }

  @Override
  public <T extends DynamicForm> boolean match(String businessCode, T data, Map<String, ?> params) {
    MultiAuditFormProperties multiAuditFormProperties = this.findProperties(businessCode);
    if (multiAuditFormProperties == null) {
      return false;
    }
    return multiAuditFormProperties.isEnabled();
  }
}
