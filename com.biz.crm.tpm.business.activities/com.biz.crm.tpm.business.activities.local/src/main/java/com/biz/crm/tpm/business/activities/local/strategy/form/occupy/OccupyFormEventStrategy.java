package com.biz.crm.tpm.business.activities.local.strategy.form.occupy;

import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.tpm.business.activities.local.strategy.form.AbstractFormEventStrategy;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

/**
 * 描述：</br>表单费用预占策略
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Component
@Slf4j
public class OccupyFormEventStrategy extends AbstractFormEventStrategy {
  @Autowired(required = false)
  private CostBudgetVoService costBudgetVoService;
  @Autowired(required = false)
  private ActivitiesService activitiesService;

  @Override
  public String getCode() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String getName() {
    return "费用预占";
  }

  @Override
  public Class<? extends FormProperties> getFormPropertiesInfo() {
    return OccupyFormProperties.class;
  }

  @Override
  @Transactional
  public <T extends DynamicForm> Object handle(String businessCode, T data, Map<String, ?> params) {
    /*
     * 处理费用预占逻辑
     * 1、根据活动活动businessCode 获取活动大类、预算科目编号
     * 2、调用费用预占接口
     */
    // 通过细类编号查询
    Validate.notBlank(businessCode, "活动编号为空，请检查");
    ActivitiesVo activitiesVo = this.activitiesService.findDetailsByActivitiesCode(businessCode);
    Validate.notNull(activitiesVo, "预占活动数据错误，请检查");
    List<ActivitiesDetailVo> activitiesDetailVos = activitiesVo.getActivitiesDetails();
    Validate.notEmpty(activitiesDetailVos, "活动明细数据为空，请检查");
    activitiesDetailVos.forEach(item -> {
      this.costBudgetVoService.occupy(item.getActivitiesCode(), item.getActivitiesDetailCode(), item.getCostBudgetCode(), item.getApplyAmount(), item.getRemark(), CostBudgetItemSourceType.ORDINARY_ACTIVITY.getCode());
    });
    return null;
  }

  @Override
  public <T extends DynamicForm> boolean match(String businessCode, T data, Map<String, ?> params) {
    OccupyFormProperties occupyFormProperties = this.findProperties(businessCode);
    if (occupyFormProperties == null) {
      return false;
    }
    return occupyFormProperties.isEnabled();
  }
}
