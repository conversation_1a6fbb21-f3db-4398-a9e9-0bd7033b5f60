package com.biz.crm.tpm.business.activities.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailSerialDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailSerialVo;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 活动明细订单表(SFA中的订单);(tpm_activities_detail_serial)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
public interface ActivitiesDetailSerialService {

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<ActivitiesDetailSerialVo> findByConditions(Pageable pageable, ActivitiesDetailSerialDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  ActivitiesDetailSerialVo findById(String id);

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<ActivitiesDetailSerialVo> findByIds(Collection<String> ids);

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCode
   * @return 单条数据
   */
  List<ActivitiesDetailSerialVo> findByActivitiesCode(String activitiesCode);

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCodes
   * @return 多条数据
   */
  List<ActivitiesDetailSerialVo> findByActivitiesCodes(Collection<String> activitiesCodes);

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCode
   * @return 单条数据
   */
  List<ActivitiesDetailSerialVo> findByActivitiesDetailCode(String activitiesDetailCode);

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCodes
   * @return 多条数据
   */
  List<ActivitiesDetailSerialVo> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes);

  /**
   * 新增数据
   *
   * @param activitiesDetailSerialDto 实体对象
   * @return 新增结果
   */
  ActivitiesDetailSerialVo create(ActivitiesDetailSerialDto activitiesDetailSerialDto);

  /**
   * 批量新增
   *
   * @param activitiesDetailSerialDtos
   * @return
   */
  List<ActivitiesDetailSerialVo> createBatch(Collection<ActivitiesDetailSerialDto> activitiesDetailSerialDtos);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);

  /**
   * 活动编号
   *
   * @param activitiesDetailCode
   * @return
   */
  BigDecimal findAmountByActivitiesDetailCode(String activitiesDetailCode);

}