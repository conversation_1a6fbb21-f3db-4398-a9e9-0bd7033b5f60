package com.biz.crm.tpm.business.activities.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfigDetail;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDetailDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:47
 */
@Mapper
public interface ActivitiesConfigDetailMapper  extends BaseMapper<ActivitiesConfigDetail> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto 动态查询条件
   * @return 分页对象列表
   */
  Page<ActivitiesConfigDetailVo> findByConditions(@Param("page") Page<ActivitiesConfigDetailVo> page , @Param("dto") ActivitiesConfigDetailDto dto);
}
