package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：活动信息表;
 *
 * <AUTHOR> <PERSON>
 * @date : 2022-6-14
 */
@ApiModel(value = "Activities", description = "活动信息表")
@TableName("tpm_activities")
@Table(name = "tpm_activities", indexes = {@Index(name = "tpm_activities_index1", columnList = "tenant_code, activities_code", unique = true)})
@Getter
@Setter
@Entity(name = "tpm_activities")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities", comment = "活动信息表")
public class Activities extends TenantEntity {

  private static final long serialVersionUID = -3445217370889459613L;
  /**
   * 备注
   */
  @ApiModelProperty(name = "备注", notes = "备注")
  @Column(name = "remark", nullable = true, length = 400, columnDefinition = "VARCHAR(400) COMMENT '备注 '")
  private String remark;

  /**
   * 创建人账号
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @ApiModelProperty(name = "创建人账号", notes = "创建人账号")
  @Column(name = "create_account", nullable = false, length = 60, columnDefinition = "VARCHAR(60) COMMENT '创建人账号 '")
  private String createAccount;

  /**
   * 创建人名称
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @ApiModelProperty(name = "创建人名称", notes = "创建人名称")
  @Column(name = "create_name", nullable = false, length = 60, columnDefinition = "VARCHAR(60) COMMENT '创建人名称 '")
  private String createName;

  /**
   * 创建时间
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @ApiModelProperty(name = "创建时间", notes = "创建时间")
  @Column(name = "create_time", nullable = false, columnDefinition = "DATETIME COMMENT '创建时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  /**
   * 活动标识(标识某一类活动)
   */
  @ApiModelProperty(name = "活动标识(标识某一类活动)", notes = "活动标识(标识某一类活动)")
  @Column(name = "activity_mark", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动标识(标识某一类活动) '")
  private String activityMark;

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  @Column(name = "activities_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  @Column(name = "activities_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 开始时间
   */
  @ApiModelProperty(name = "开始时间", notes = "开始时间")
  @Column(name = "begin_time", nullable = false, columnDefinition = "DATETIME COMMENT '开始时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @ApiModelProperty(name = "结束时间", notes = "结束时间")
  @Column(name = "end_time", nullable = false, columnDefinition = "DATETIME COMMENT '结束时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  /**
   * 是否核销
   */
  @ApiModelProperty(name = "是否核销", notes = "是否核销,当活动下的所有活动都核销或者无需核销后")
  @Column(name = "is_audit", nullable = false, length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否核销 '")
  private String isAudit;

  /**
   * 是否关闭
   */
  @ApiModelProperty(name = "isClose", notes = "是否关闭", value = "是否关闭")
  @Column(name = "is_close", nullable = false, length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否关闭 '")
  private String isClose;

  /**
   * 总申请金额
   */
  @ApiModelProperty(name = "totalApplyAmount", notes = "总申请金额", value = "总申请金额")
  @Column(name = "total_apply_amount", nullable = false, length = 20, scale = 4, columnDefinition = "DECIMAL(20,4) COMMENT '总申请金额 '")
  private BigDecimal totalApplyAmount;

  /**
   * 活动明细总数
   */
  @ApiModelProperty(name = "detailNum", notes = "核销明细总数", value = "核销明细总数")
  @Column(name = "detail_num", nullable = false, columnDefinition = "INT COMMENT '活动明细总数 '")
  private Integer detailNum;

  /**
   * 待核销明细数量
   */
  @ApiModelProperty(name = "waitAuditDetailNum", notes = "待核销明细数量", value = "待核销明细数量")
  @Column(name = "wait_audit_detail_num", nullable = false, columnDefinition = "INT COMMENT '待核销明细数量 '")
  private Integer waitAuditDetailNum;

  /**
   * 自动核销数量
   */
  @ApiModelProperty(name = "autoAuditDetailNum", notes = "自动核销数量", value = "自动核销数量")
  @Column(name = "auto_audit_detail_num", nullable = false, columnDefinition = "INT COMMENT '自动核销数量 '")
  private Integer autoAuditDetailNum;

}
