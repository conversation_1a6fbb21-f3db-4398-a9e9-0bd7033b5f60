package com.biz.crm.tpm.business.activities.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.local.entity.AlongWithOrder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 随单比例管控(AlongWithOrder)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-03 11:55:24
 */
public interface AlongWithOrderMapper extends BaseMapper<AlongWithOrder> {

    BigDecimal getRatioByCondition(@Param("departmentCodes") List<String> departmentCodes, @Param("customerCode") String customerCode,
                                   @Param("productCodes") List<String> productCodes, @Param("years") String years, @Param("tenantCode") String tenantCode);

}

