<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.activities.local.mapper.ActivitiesMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.local.entity.Activities" id="ActivitiesMap">
  </resultMap>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo">
    select t.*
    from tpm_activities t
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.createName != null and dto.createName != ''">
        <bind name="createName" value="'%' + dto.createName + '%'"/>
        and t.create_name like #{createName}
      </if>
      <if test="dto.activitiesCode != null and dto.activitiesCode != ''">
        <bind name="activitiesCode" value="'%' + dto.activitiesCode + '%'"/>
        and t.activities_code like #{activitiesCode}
      </if>
      <if test="dto.activitiesName != null and dto.activitiesName != ''">
        <bind name="activitiesName" value="'%' + dto.activitiesName + '%'"/>
        and t.activities_name like #{activitiesName}
      </if>
      <if test="dto.isAudit != null and dto.isAudit != ''">
        and t.is_audit = #{dto.isAudit}
      </if>
      <if test="dto.isClose != null and dto.isClose != ''">
        and t.is_close = #{dto.isClose}
      </if>
      <if test="dto.excludeActivitiesCodes != null and dto.excludeActivitiesCodes.size > 0">
        and t.activities_code not in
        <foreach item="item" collection="dto.excludeActivitiesCodes" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
      </if>
    </where>
    order by t.create_time desc, t.id
  </select>

  <select id="findCodeByTime" resultType="java.lang.String">
    select t.activities_code
    from tpm_activities t
    <where>
      <if test="tenantCode != null and tenantCode != ''">
        and t.tenant_code = #{tenantCode}
      </if>
      <if test="start_time != null and end_time != null">
        and t.begin_time between #{start_time} and #{end_time}
      </if>
    </where>
    order by t.create_time desc, t.id
  </select>

  <select id="countByCostTypeCategoryCode">
    select count(distinct (ta.*))
    from tpm_activities ta
           left join tpm_activities_detail tad
                     on ta.activities_code = tad.activities_code and ta.tenant_code = tad.tenant_code
    <where>
      and ta.tenant_code = #{tenantCode}
            and tad.cost_type_category_code = #{costTypeCategoryCode}
    </where>
  </select>
</mapper>