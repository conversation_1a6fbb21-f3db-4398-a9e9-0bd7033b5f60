package com.biz.crm.tpm.business.activities.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.register.ActivitiesTemplateRegister;
import com.biz.crm.tpm.business.activities.sdk.strategy.activityExecutive.ActivityExecutiveStrategy;
import com.biz.crm.tpm.business.activities.sdk.strategy.activityType.ActivityTypeStrategy;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivityExecutiveStrategyVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivityTypeStrategyVo;
import com.biz.crm.workflow.sdk.enums.EffectiveScopeEnum;
import com.biz.crm.workflow.sdk.register.ProcessTemplateRegister;
import com.biz.crm.workflow.sdk.service.process.ProcessNodeGroup;
import com.biz.crm.workflow.sdk.strategy.tracing.TracingStrategy;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述：</br>活动模板注册信息
 *
 * <AUTHOR>
 * @date 2022/7/28
 */
@RestController
@RequestMapping("/v1/activities/register")
@Slf4j
@Api(tags = "活动模板注册信息")
@Deprecated
public class ActivitiesTemplateRegisterController {

  @Autowired
  private ActivitiesTemplateRegister activitiesTemplateRegister;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private ApplicationContext ac;

  /**
   * 获取表单类型策略
   *
   * @return
   */
  @GetMapping("/findActivityTypes")
  @ApiOperation(value = "获取表单类型策略")
  public Result<?> findActivityTypes() {
    try {
      Collection<Class<? extends ActivityTypeStrategy>> activityTypeStrategies = activitiesTemplateRegister.getActivityTypeStrategy();
      List<ActivityTypeStrategyVo> strategies = Lists.newArrayList();
      if (!CollectionUtils.isEmpty(activityTypeStrategies)) {
        activityTypeStrategies.forEach(clazz -> {
          ActivityTypeStrategy activityTypeStrategy = this.ac.getBean(clazz);
          ActivityTypeStrategyVo tracingVo = this.nebulaToolkitService.copyObjectByWhiteList(activityTypeStrategy, ActivityTypeStrategyVo.class, LinkedHashSet.class, ArrayList.class);
          strategies.add(tracingVo);
        });
      }
      return Result.ok(strategies);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   * 获取活动执行表单绑定策略信息
   *
   * @return
   */
  @GetMapping("/findActivityExecutiveStrategies")
  @ApiOperation(value = "获取活动执行表单绑定策略信息")
  public Result<?> findNodeGroups(@RequestParam("activityTypeCode") String activityTypeCode) {
    try {
      Collection<Class<? extends ActivityTypeStrategy>> activityTypeStrategies = activitiesTemplateRegister.getActivityTypeStrategy();
      List<ActivityExecutiveStrategyVo> activityExecutiveStrategyVos = Lists.newArrayList();
      if (!CollectionUtils.isEmpty(activityTypeStrategies)) {
        activityTypeStrategies.forEach(clazz -> {
          ActivityTypeStrategy activityTypeStrategy = this.ac.getBean(clazz);
          if (activityTypeStrategy.getActivitiesFormTypeCode().equals(activityTypeCode)) {
            Collection<Class<? extends ActivityExecutiveStrategy>> bindExecutiveStrategies = activityTypeStrategy.getBindExecutiveStrategy();
            if (!CollectionUtils.isEmpty(bindExecutiveStrategies)) {
              bindExecutiveStrategies.forEach(bindExecutiveStrategy -> {
                ActivityExecutiveStrategy activityExecutiveStrategy = this.ac.getBean(bindExecutiveStrategy);
                ActivityExecutiveStrategyVo activityExecutiveStrategyVo = this.nebulaToolkitService.copyObjectByWhiteList(activityExecutiveStrategy, ActivityExecutiveStrategyVo.class, LinkedHashSet.class, ArrayList.class);
                activityExecutiveStrategyVos.add(activityExecutiveStrategyVo);
              });
            }
          }
        });
      }
      return Result.ok(activityExecutiveStrategyVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
