package com.biz.crm.tpm.business.activities.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailCollect;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesDetailCollectMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailCollectDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 活动明细采集信息表(SFA中的订单);(tpm_activities_detail_collect)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@Component
public class ActivitiesDetailCollectRepository extends ServiceImpl<ActivitiesDetailCollectMapper, ActivitiesDetailCollect> {
  @Autowired
  private ActivitiesDetailCollectMapper activitiesDetailCollectMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<ActivitiesDetailCollectVo> findByConditions(Pageable pageable, ActivitiesDetailCollectDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesDetailCollectVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesDetailCollectMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<ActivitiesDetailCollect>
   */
  public List<ActivitiesDetailCollect> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetailCollect::getId, ids)
            .eq(ActivitiesDetailCollect::getTenantCode, tenantCode)
            .list();
  }


  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @return 返单条数据
   */
  public List<ActivitiesDetailCollect> findByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesDetailCollect::getActivitiesCode, activitiesCode)
            .eq(ActivitiesDetailCollect::getTenantCode, tenantCode)
            .orderByDesc(ActivitiesDetailCollect::getCreateTime)
            .list();
  }

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<ActivitiesDetailCollect> findByActivitiesCodes(Collection<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetailCollect::getActivitiesCode, activitiesCodes)
            .eq(ActivitiesDetailCollect::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 删除数据
   */
  public boolean deleteByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(ActivitiesDetailCollect::getActivitiesCode, activitiesCode)
            .eq(ActivitiesDetailCollect::getTenantCode, tenantCode)
            .remove();
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @return 返单条数据
   */
  public List<ActivitiesDetailCollect> findByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesDetailCollect::getActivitiesDetailCode, activitiesDetailCode)
            .eq(ActivitiesDetailCollect::getTenantCode, tenantCode)
            .orderByAsc(ActivitiesDetailCollect::getCreateTime)
            .list();
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<ActivitiesDetailCollect> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes) {
    if (CollectionUtils.isEmpty(activitiesDetailCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetailCollect::getActivitiesDetailCode, activitiesDetailCodes)
            .eq(ActivitiesDetailCollect::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 删除数据
   */
  public boolean deleteByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(ActivitiesDetailCollect::getActivitiesDetailCode, activitiesDetailCode)
            .eq(ActivitiesDetailCollect::getTenantCode, tenantCode)
            .remove();
  }

  public ActivitiesDetailCollect findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesDetailCollect::getTenantCode,tenantCode)
        .in(ActivitiesDetailCollect::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ActivitiesDetailCollect::getTenantCode,tenantCode)
        .in(ActivitiesDetailCollect::getId,ids)
        .remove();
  }
}
