package com.biz.crm.tpm.business.activities.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetail;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 活动明细信息表;(tpm_activities_detail)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-28
 */
@Mapper
public interface ActivitiesDetailMapper extends BaseMapper<ActivitiesDetail> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto 动态查询条件
   * @return 分页对象列表
   */
  Page<ActivitiesDetailVo> findByConditions(@Param("page") Page<ActivitiesDetailVo> page , @Param("dto") ActivitiesDetailDto dto);

  void updateStatusByActivitiesCode(@Param("activitiesCode") String activitiesCode, @Param("status") String status, @Param("tenantCode") String tenantCode);

  void updateStatusBySchemeCode(@Param("activitiesCode") String activitiesCode, @Param("schemeCode") String schemeCode, @Param("tenantCode") String tenantCode, @Param("status") String status);
}
