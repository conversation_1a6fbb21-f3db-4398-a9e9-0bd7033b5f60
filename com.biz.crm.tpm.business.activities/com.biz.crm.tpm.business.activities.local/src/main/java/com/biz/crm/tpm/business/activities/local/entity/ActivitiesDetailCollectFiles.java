package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：活动明细采集附件信息表(SFA中的订单);
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@ApiModel(value = "ActivitiesDetailCollectFiles",description = "活动明细采集附件信息表(SFA中的订单)")
@TableName("tpm_activities_detail_collect_files")
@Getter
@Setter
@Entity(name = "tpm_activities_detail_collect_files")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_detail_collect_files", comment = "活动明细采集附件信息表(SFA中的订单)")
@Table(name = "tpm_activities_detail_collect_files")
public class ActivitiesDetailCollectFiles extends FileEntity {

  /** 重命名文件名 */
  @ApiModelProperty(name = "refileName",notes = "重命名文件名", value= "重命名文件名")
  @Column(name = "refile_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '重命名文件名 '")
  private String refileName;

  /** 采集编号 */
  @ApiModelProperty(name = "collectCode",notes = "采集编号", value= "采集编号")
  @Column(name = "collect_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '采集编号 '")
  private String collectCode;
  /** 采集请求编号 */
  @ApiModelProperty(name = "collectRequireCode",notes = "采集请求编号", value= "采集请求编号")
  @Column(name = "collect_require_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '采集请求编号 '")
  private String collectRequireCode;
  /** 采集请求名称 */
  @ApiModelProperty(name = "collectRequireName",notes = "采集请求名称", value= "采集请求名称")
  @Column(name = "collect_require_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '采集请求名称 '")
  private String collectRequireName;

}
