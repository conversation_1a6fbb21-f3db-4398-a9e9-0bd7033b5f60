package com.biz.crm.tpm.business.activities.local.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetail;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesDetailRepository;
import com.biz.crm.tpm.business.activities.local.service.ActivitiesDetailSerialService;
import com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailRemoteDto;
import com.biz.crm.tpm.business.activities.sdk.enums.ActivityStatusEnum;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesDetailEventListener;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesDetailFilterEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesConfigVoService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailCollectService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesFieldsCollectTemplateService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.service.BasicActivitiesInfoService;
import com.biz.crm.tpm.business.activities.sdk.service.DynamicTemplateService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailSerialVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesFieldsCollectTemplateFeginVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesFieldsCollectTemplateVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType;
import com.biz.crm.tpm.business.budget.sdk.service.ApprovalCollectVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.BusinessStrategySettingExecutor;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import liquibase.pro.packaged.A;
import liquibase.pro.packaged.S;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant.ACTIVITIES_ORDER_LOCK_PREFIX_FORMAT;
import static com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant.DYNAMIC_FORM_FIELD_CODE;
import static com.biz.crm.tpm.business.activities.sdk.strategy.close.ActivityItemsClosedStrategy.GLOBAL_ACTIVITY_STATUS_REDIS_LOCK;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType.ACTIVITY;

/**
 * 活动明细信息表;(tpm_activities_detail)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-28
 */
@Service("activitiesDetailService")
public class ActivitiesDetailServiceImpl implements ActivitiesDetailService {
  @Autowired
  private ActivitiesDetailRepository activitiesDetailRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired(required = false)
  private List<ActivitiesDetailEventListener> activitiesDetailEventListeners;
  @Autowired
  private RedisMutexService redisMutexService;
  @Autowired
  private ActivitiesDetailSerialService activitiesDetailSerialService;
  @Autowired
  private CostTypeCategoryVoService costTypeCategoryVoService;
  @Autowired
  private ApprovalCollectVoService approvalCollectVoService;
  @Autowired
  private ActivitiesDetailCollectService activitiesDetailCollectService;
  @Autowired
  private DynamicTemplateService dynamicTemplateService;
  @Autowired
  private ActivitiesFieldsCollectTemplateService activitiesFieldsCollectTemplateService;
  @Autowired
  private ActivitiesService activitiesService;
  @Autowired(required = false)
  private List<ActivitiesDetailFilterEventListener> activitiesDetailFilterEventListeners;
  @Autowired(required = false)
  private List<BusinessStrategySettingExecutor> businessStrategySettingExecutors;

  @Autowired
  private ActivitiesConfigVoService activitiesConfigVoService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<ActivitiesDetailVo> findByConditions(Pageable pageable, ActivitiesDetailDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesDetailDto();
    }
    final String terminalCode = dto.getTerminalCode();
    Set<String> excludeActivitiesDetailCodes = Sets.newHashSet();
    if (!CollectionUtils.isEmpty(activitiesDetailFilterEventListeners)) {
      for (ActivitiesDetailFilterEventListener activitiesDetailFilterEventListener : activitiesDetailFilterEventListeners) {
        excludeActivitiesDetailCodes.addAll(activitiesDetailFilterEventListener.filterActivitiesDetailCode(dto));
      }
    }
    dto.setExcludeItemCodes(excludeActivitiesDetailCodes);
    Page<ActivitiesDetailVo> page = this.activitiesDetailRepository.findByConditions(pageable, dto);
    if (page.getSize() > 0) {
      List<ActivitiesDetailVo> result = page.getRecords();
      // 对扩展数据进行填充
      result.forEach(item -> {
        ActivitiesVo activitiesVo = this.activitiesService.findByActivitiesCode(item.getActivitiesCode());
        item.setActivities(activitiesVo);
        if (StringUtils.isNotBlank(terminalCode)) {
          List<ActivitiesDetailSerialVo> activitiesDetailSerialVos = this.activitiesDetailSerialService.findByActivitiesDetailCode(item.getActivitiesDetailCode());
          if (!CollectionUtils.isEmpty(activitiesDetailSerialVos)) {
            BigDecimal terminalAmount = activitiesDetailSerialVos.stream().filter(serial -> terminalCode.equals(serial.getTerminalCode())).map(ActivitiesDetailSerialVo::getSerialPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setTerminalAmount(terminalAmount);
          }
        }
        CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(item.getCostTypeDetailCode());
        item.setCostTypeDetailVo(costTypeDetailVo);
      });
    }
    return page;
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public ActivitiesDetailVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    ActivitiesDetail activitiesDetail = this.activitiesDetailRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (activitiesDetail == null) {
      return null;
    }
    ActivitiesDetailVo activitiesDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetail, ActivitiesDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return activitiesDetailVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<ActivitiesDetailVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetail> activitiesDetails = this.activitiesDetailRepository.findByIds(ids);
    Collection<ActivitiesDetailVo> activitiesDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetails, ActivitiesDetail.class, ActivitiesDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailVos);
  }


  @Override
  public List<ActivitiesDetailVo> findByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return Collections.emptyList();
    }
    List<ActivitiesDetail> activitiesDetails = this.activitiesDetailRepository.findByActivitiesCode(activitiesCode);
    if (CollectionUtils.isEmpty(activitiesDetails)) {
      return Collections.emptyList();
    }
    Collection<ActivitiesDetailVo> activitiesDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetails, ActivitiesDetail.class, ActivitiesDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailVos);
  }

  @Override
  public Map<String, List<ActivitiesDetailVo>> findByActivitiesCodes(Collection<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Maps.newHashMap();
    }
    List<ActivitiesDetail> activitiesDetails = this.activitiesDetailRepository.findByActivitiesCodes(activitiesCodes);
    if (CollectionUtils.isEmpty(activitiesDetails)) {
      return Maps.newHashMap();
    }
    Collection<ActivitiesDetailVo> activitiesDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetails, ActivitiesDetail.class, ActivitiesDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return activitiesDetailVos.stream().collect(Collectors.groupingBy(ActivitiesDetailVo::getActivitiesCode));
  }

  @Override
  public List<ActivitiesDetailVo> findAllByActivitiesCodes(Collection<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetail> activitiesDetails = this.activitiesDetailRepository.findByActivitiesCodes(activitiesCodes);
    if (CollectionUtils.isEmpty(activitiesDetails)) {
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(activitiesDetails, ActivitiesDetail.class, ActivitiesDetailVo.class, LinkedHashSet.class, ArrayList.class));
  }

  @Override
  public List<ActivitiesDetailVo> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes) {
    if (CollectionUtils.isEmpty(activitiesDetailCodes)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetail> activitiesDetails = this.activitiesDetailRepository.findByActivitiesDetailCodes(activitiesDetailCodes);
    if (CollectionUtils.isEmpty(activitiesDetails)) {
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(activitiesDetails, ActivitiesDetail.class, ActivitiesDetailVo.class, LinkedHashSet.class, ArrayList.class));

  }

  @Override
  public ActivitiesDetailVo findByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return null;
    }
    ActivitiesDetail activitiesDetail = this.activitiesDetailRepository.findByActivitiesDetailCode(activitiesDetailCode);
    if (activitiesDetail == null) {
      return null;
    }
    ActivitiesDetailVo activitiesDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetail, ActivitiesDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return activitiesDetailVo;
  }

  @Override
  public ActivitiesDetailVo findDetailByActivitiesDetailCode(String activitesDetailCode) {
    ActivitiesDetailVo activitiesDetailVo = this.findByActivitiesDetailCode(activitesDetailCode);
    // 填充明细信息
    this.fillDetail(activitiesDetailVo);
    return activitiesDetailVo;
  }

  /**
   * 新增数据
   *
   * @param activitiesDetailDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public ActivitiesDetailVo create(ActivitiesDetailDto activitiesDetailDto) {
    this.createValidate(activitiesDetailDto);
    ActivitiesDetail activitiesDetail = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailDto, ActivitiesDetail.class, LinkedHashSet.class, ArrayList.class);
    activitiesDetail.setTenantCode(TenantUtils.getTenantCode());
    activitiesDetail.setIsClose(BooleanEnum.FALSE.getCapital());
    activitiesDetail.setTotalAmount(BigDecimal.ZERO);
    activitiesDetail.setIsExecute(BooleanEnum.FALSE.getCapital());
    if (StringUtils.isNotBlank(activitiesDetailDto.getIsFullAudit())) {
      activitiesDetail.setIsFullAudit(activitiesDetailDto.getIsFullAudit());
    } else {
      activitiesDetail.setIsFullAudit(BooleanEnum.FALSE.getCapital());
    }
    CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(activitiesDetailDto.getCostTypeDetailCode());
    Validate.notNull(costTypeDetailVo, "活动明细编号【%s】关联活动细类编号【%s】错误，请检查！", activitiesDetailDto.getActivitiesDetailCode(), activitiesDetailDto.getCostTypeCategoryCode());
    activitiesDetail.setIsSendSfa(activitiesDetailDto.getIsSendSfa());
    this.activitiesDetailRepository.saveOrUpdate(activitiesDetail);
    ActivitiesDetailVo activitiesDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetail, ActivitiesDetailVo.class, LinkedHashSet.class, ArrayList.class);
    activitiesDetailVo.setId(activitiesDetail.getId());
    return activitiesDetailVo;
  }

  /**
   * 批量新增
   *
   * @param activitiesDetailDtos
   * @return
   */
  @Transactional
  @Override
  public List<ActivitiesDetailVo> createBatch(Collection<ActivitiesDetailDto> activitiesDetailDtos) {
    if (CollectionUtils.isEmpty(activitiesDetailDtos)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetailVo> activitiesDetailVos = Lists.newArrayList();
    for (ActivitiesDetailDto activitiesDetailDto : activitiesDetailDtos) {
      ActivitiesDetailVo activitiesDetailVo = this.create(activitiesDetailDto);
      activitiesDetailVos.add(activitiesDetailVo);
    }
    return activitiesDetailVos;
  }

  @Transactional
  @Override
  public void closed(Collection<ActivitiesDetailDto> activitiesDetailDtos) {
    if (CollectionUtils.isEmpty(activitiesDetailDtos)) {
      return;
    }
    Set<ActivitiesDetailDto> details = Sets.newHashSet();
    for (ActivitiesDetailDto activitiesDetailDto : activitiesDetailDtos) {
      ActivitiesDetail activitiesDetail = this.activitiesDetailRepository.findByActivitiesDetailCode(activitiesDetailDto.getActivitiesDetailCode());
      Validate.notNull(activitiesDetail, "活动明细数据错误，编号【%s】，请检查!", activitiesDetailDto.getActivitiesDetailCode());
      Validate.isTrue(!BooleanEnum.TRUE.getCapital().equals(activitiesDetail.getIsClose()), "该活动明细【%s】已经关闭，请检查！", activitiesDetailDto.getActivitiesDetailCode());
      activitiesDetail.setIsClose(BooleanEnum.TRUE.getCapital());
      activitiesDetail.setTenantCode(TenantUtils.getTenantCode());
      this.activitiesDetailRepository.saveOrUpdate(activitiesDetail);
      //获取活动编号
      String activitiesCode = activitiesDetailDto.getActivitiesCode();
      //获取活动申请编号
      String schemeCode = activitiesDetailDto.getSchemeCode();
      ActivityStatusEnum activityStatusEnum = this.examineActivities(activitiesCode);
      //修改活动申请状态
      this.activitiesDetailRepository.updateStatusByActivitiesCodeOrSchemeCode(activitiesCode, schemeCode, activityStatusEnum);
      ActivitiesDetailDto detail = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetail, ActivitiesDetailDto.class, HashSet.class, ArrayList.class);
      details.add(detail);
    }

    if (!CollectionUtils.isEmpty(BasicActivitiesInfoService.ACTIVITY_SERVICE_MAPPING)) {
      Map<String, Set<String>> codeMap = details.stream().collect(Collectors.groupingBy(ActivitiesDetailDto::getActivitiesCode, Collectors.mapping(ActivitiesDetailDto::getActivitiesDetailCode, Collectors.toSet())));
      Set<String> activityMarks = BasicActivitiesInfoService.ACTIVITY_SERVICE_MAPPING.keySet();
      boolean hasLock = false;
      try {
        hasLock = redisMutexService.tryLock(GLOBAL_ACTIVITY_STATUS_REDIS_LOCK, TimeUnit.SECONDS, 10);
        Validate.isTrue(hasLock, "系统网络繁忙，请稍后重试");
        //执行活动关闭
        for (String activityMark : activityMarks) {
          BasicActivitiesInfoService service = BasicActivitiesInfoService.ACTIVITY_SERVICE_MAPPING.get(activityMark);
          service.onClosed(codeMap);
        }
      } finally {
        if (hasLock) {
          redisMutexService.unlock(GLOBAL_ACTIVITY_STATUS_REDIS_LOCK);
        }
      }
    }

    Collection<ActivitiesDetailVo> activitiesDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(details, ActivitiesDetailDto.class, ActivitiesDetailVo.class, HashSet.class, ArrayList.class);
    if (!CollectionUtils.isEmpty(activitiesDetailEventListeners)) {
      this.activitiesDetailEventListeners.forEach(listener -> {
        listener.onClosed(activitiesDetailVos);
      });
    }
  }

  /**
   * 检查活动的活动明细
   * @param activitiesCode
   */
  private ActivityStatusEnum examineActivities(String activitiesCode) {
    //该活动原本有
    List<ActivitiesDetailVo> detailVoList = this.findByActivitiesCode(activitiesCode);
    //过滤出未关闭的状态的数据
    List<ActivitiesDetailVo> activitiesDetailVoFalseList = detailVoList.stream()
        .filter(detailVo -> BooleanEnum.FALSE.getCapital().equals(detailVo.getIsClose()))
        .collect(Collectors.toList());
    //如果未关闭状态的为空，说明已经全部关闭
    if (CollectionUtils.isEmpty(activitiesDetailVoFalseList)) {
      return ActivityStatusEnum.ALL_CLOSE;
    }
    return ActivityStatusEnum.PARTIAL_CLOSE;
  }

  @Override
  public int countByCostTypeCategoryCode(String costTypeCategoryCode) {
    return this.activitiesDetailRepository.countByCostTypeCategoryCode(costTypeCategoryCode);
  }

  @Override
  @Transactional
  public void updateOrderAmountByActivitiesCode(String activitiesDetailCode, BigDecimal changeAmount, boolean isAdd) {
    String key = String.format(ACTIVITIES_ORDER_LOCK_PREFIX_FORMAT, TenantUtils.getTenantCode(), activitiesDetailCode);
    boolean isLock = false;
    try {
      isLock = this.redisMutexService.tryLock(key, TimeUnit.SECONDS, 5);
      Validate.isTrue(isLock, "系统网络繁忙，请稍后重试");
      ActivitiesDetail activitiesDetail = this.activitiesDetailRepository.findByActivitiesDetailCode(activitiesDetailCode);
      BigDecimal orderedAmount = activitiesDetail.getTotalAmount();
      if (isAdd) {
        orderedAmount = orderedAmount.add(changeAmount);
      } else {
        orderedAmount = orderedAmount.subtract(changeAmount);
      }
      activitiesDetail.setTotalAmount(orderedAmount);
      activitiesDetail.setTenantCode(TenantUtils.getTenantCode());
      this.activitiesDetailRepository.saveOrUpdate(activitiesDetail);
    } finally {
      if (isLock) {
        this.redisMutexService.unlock(key);
      }
    }
  }

  @Override
  @Transactional
  public void updateFullAuditByActivitiesDetailCode(String activitiesDetailCode, String isFullAudit) {
    ActivitiesDetail activitiesDetail = this.activitiesDetailRepository.findByActivitiesDetailCode(activitiesDetailCode);
    Validate.notNull(activitiesDetail, "活动明细数据异常，明细编号【%s】", activitiesDetailCode);
    activitiesDetail.setIsFullAudit(isFullAudit);
    activitiesDetail.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesDetailRepository.saveOrUpdate(activitiesDetail);
  }

  /**
   * 创建验证
   *
   * @param activitiesDetailDto
   */
  private void createValidate(ActivitiesDetailDto activitiesDetailDto) {
    Validate.notNull(activitiesDetailDto, "新增时，对象信息不能为空！");
    Validate.isTrue(activitiesDetailDto.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(activitiesDetailDto.getActivitiesCode(), "新增数据时，活动编号不能为空！");
    Validate.notBlank(activitiesDetailDto.getActivitiesName(), "新增数据时，活动名称不能为空！");
    Validate.notBlank(activitiesDetailDto.getActivitiesDetailCode(), "新增数据时，活动明细编号不能为空！");
    Validate.notBlank(activitiesDetailDto.getCostTypeCategoryName(), "新增数据时，活动大类名称不能为空！");
    Validate.notBlank(activitiesDetailDto.getCostTypeCategoryCode(), "新增数据时，活动大类编号不能为空！");
    Validate.notBlank(activitiesDetailDto.getCostBudgetCode(), "新增数据时，费用预算编码不能为空！");
    Validate.notBlank(activitiesDetailDto.getCostTypeDetailCode(), "新增数据时，活动细类编号不能为空！");
    Validate.notBlank(activitiesDetailDto.getCostTypeDetailName(), "新增数据时，活动细类名称不能为空！");
    Validate.notBlank(activitiesDetailDto.getBudgetSubjectsCode(), "新增数据时，预算科目编号不能为空！");
    Validate.notBlank(activitiesDetailDto.getBudgetSubjectsName(), "新增数据时，预算科目名称不能为空！");
    Validate.notNull(activitiesDetailDto.getApplyAmount(), "新增数据时，申请金额不能为空！");
    Validate.notBlank(activitiesDetailDto.getPayType(), "新增数据时，支付方式不能为空！");
  }

  /**
   * 填充明细数据
   *
   * @param activitiesDetailVo
   */
  private void fillDetail(ActivitiesDetailVo activitiesDetailVo) {
    // 设置活动信息
    ActivitiesVo activitiesVo = this.activitiesService.findByActivitiesCode(activitiesDetailVo.getActivitiesCode());
    activitiesDetailVo.setActivities(activitiesVo);

    List<ActivitiesDetailSerialVo> activitiesDetailSerialVos = this.activitiesDetailSerialService.findByActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode());
    activitiesDetailVo.setActivitiesDetailSerials(activitiesDetailSerialVos);
    //根据活动细类编码获取执行活动表单编码
    CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(activitiesDetailVo.getCostTypeDetailCode());
    if (ObjectUtils.isNotEmpty(costTypeDetailVo)) {
      ActivitiesConfigVo activitiesConfigVo = this.activitiesConfigVoService.findByActivitiesConfigCode(costTypeDetailVo.getExecutionFromCode());
      activitiesDetailVo.setActivitiesFormCode(activitiesConfigVo.getDynamicFormCode());
    }
    // 采集图片要求信息
    activitiesDetailVo.setCostTypeDetailVo(costTypeDetailVo);
    BusinessStrategySettingExecutor executor = BusinessStrategySettingExecutor.getExecutor(businessStrategySettingExecutors, ACTIVITY.name());
    Validate.notNull(executor, "活动明细编号【%s】关联活动细类编号【%s】没有匹配到相应的策略执行器，请检查！", activitiesDetailVo.getActivitiesDetailCode(), activitiesDetailVo.getCostTypeCategoryCode());
    Object photoRequire = executor.getValueByOprtType(costTypeDetailVo.getSettingStrategies(), StrategySettingExecutorOprtType.COLLECT_IMAGE.name());
    if (photoRequire != null && StringUtils.isNotBlank(photoRequire.toString())) {
      String[] photoRequires = StringUtils.split(photoRequire.toString(), ",");
      Set<String> photoRequireSet = Arrays.stream(photoRequires).collect(Collectors.toSet());
      List<ApprovalCollectVo> approvalCollectVos = this.approvalCollectVoService.findDetailsByCodes(photoRequireSet);
      activitiesDetailVo.setPhotoRequires(approvalCollectVos);
    }

    // 采集数据
    List<ActivitiesDetailCollectVo> activitiesDetailCollectVos = this.activitiesDetailCollectService.findByActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode());
    activitiesDetailVo.setActivitiesDetailCollects(activitiesDetailCollectVos);

    //活动字段采集-动态表单dynamicFormCode
    if (StringUtils.isAnyBlank(activitiesDetailVo.getActivitiesFormCode(), costTypeDetailVo.getExecutionMappingCode())) {
      return;
    }
    //活动字段采集-动态表单结构
    JSONObject fieldsCollectTemplateStruct = dynamicTemplateService.findByDynamicFormCodeAndMappingCode(activitiesDetailVo.getActivitiesFormCode(), costTypeDetailVo.getExecutionMappingCode());
    if (fieldsCollectTemplateStruct != null && fieldsCollectTemplateStruct.size() > 0) {
      fieldsCollectTemplateStruct.put("dynamicFormFieldCode", DYNAMIC_FORM_FIELD_CODE);
      activitiesDetailVo.setFieldsCollectTemplateStruct(fieldsCollectTemplateStruct);
    }
    //活动字段采集-动态表单数据
    List<ActivitiesFieldsCollectTemplateVo> activitiesFieldsCollectTemplateVos = activitiesFieldsCollectTemplateService.findByActivityDetailCode(activitiesDetailVo.getActivitiesDetailCode());
    if (!CollectionUtils.isEmpty(activitiesFieldsCollectTemplateVos)) {
      Collection<ActivitiesFieldsCollectTemplateFeginVo> fieldsCollectTemplateFeginVos = nebulaToolkitService.copyCollectionByWhiteList(activitiesFieldsCollectTemplateVos, ActivitiesFieldsCollectTemplateVo.class, ActivitiesFieldsCollectTemplateFeginVo.class, HashSet.class, ArrayList.class);
      activitiesDetailVo.setFieldsCollectTemplates(Lists.newArrayList(fieldsCollectTemplateFeginVos));
    }
  }

  @Override
  @Transactional
  public ActivitiesDetailVo createRemote(ActivitiesDetailRemoteDto dto) {
    Validate.notNull(dto, "远程保存时，相关活动信息不能为空");
    Validate.notBlank(dto.getBtNo(), "批次号不能为空");
    Validate.notBlank(dto.getActivitiesDetailCode(), "活动明细编码不能为空");
    ActivitiesDetailVo activitiesDetail = this.findByActivitiesDetailCode(dto.getActivitiesDetailCode());
    Validate.notNull(activitiesDetail, "根据指定的活动明细编码【%s】，未能获取到相应信息", dto.getActivitiesDetailCode());

    CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(activitiesDetail.getCostTypeDetailCode());
    Validate.notNull(costTypeDetailVo, "保存时，数据关联活动明细错误，请检查！");
    if (ObjectUtils.isNotEmpty(costTypeDetailVo.getExecutionFromBody()) && costTypeDetailVo.getExecutionFromBody().containsKey(ActivitiesConstant.STRATEGY_PUSH_SFA)) {
      JSONObject pushSfaBody = costTypeDetailVo.getExecutionFromBody().getJSONObject(ActivitiesConstant.STRATEGY_PUSH_SFA);
      boolean isCollectDistributionOrder = pushSfaBody.getBoolean(ActivitiesConstant.FORM_PROPERTIES_COLLECT_DISTRIBUTION_ORDER);
      boolean isSignDisplayAgreement = pushSfaBody.getBoolean(ActivitiesConstant.FORM_PROPERTIES_SIGN_DISPLAY);
      boolean isCollectData = pushSfaBody.getBoolean(ActivitiesConstant.FORM_PROPERTIES_COLLECT_ACTIVITY_DATA);
      if (isCollectDistributionOrder || isSignDisplayAgreement) {
        //1.======活动订单信息处理
        Validate.notEmpty(dto.getActivitiesDetailSerials(), "活动订单或协议信息不能为空");
        dto.getActivitiesDetailSerials().forEach(e -> e.setBtNo(dto.getBtNo()));
        List<ActivitiesDetailSerialVo> activitiesDetailSerials = activitiesDetailSerialService.createBatch(dto.getActivitiesDetailSerials());
        activitiesDetail.setActivitiesDetailSerials(activitiesDetailSerials);
      }

      if (isCollectData) {
        //2.======活动订单图片采集
        Validate.notEmpty(dto.getActivitiesDetailCollects(), "活动订单图片采集信息不能为空");
        dto.getActivitiesDetailCollects().forEach(e -> {
          e.setBtNo(dto.getBtNo());
        });
        List<ActivitiesDetailCollectVo> activitiesDetailCollects = activitiesDetailCollectService.createBatch(dto.getActivitiesDetailCollects());
        activitiesDetail.setActivitiesDetailCollects(activitiesDetailCollects);
      }
    }

    //3.======活动字段采集模板数据
    if (dto.getFieldsCollectTemplate() != null && dto.getFieldsCollectTemplate().size() > 0) {
      dto.getFieldsCollectTemplate().put("btNo", dto.getBtNo());
      dto.getFieldsCollectTemplate().put("activityDetailCode", dto.getActivitiesDetailCode());
      ActivitiesFieldsCollectTemplateVo fieldsCollectTemplate = activitiesFieldsCollectTemplateService.create(dto.getFieldsCollectTemplate());
      ActivitiesFieldsCollectTemplateFeginVo fieldsCollectTemplateFeginVo = nebulaToolkitService.copyObjectByWhiteList(fieldsCollectTemplate, ActivitiesFieldsCollectTemplateFeginVo.class, HashSet.class, ArrayList.class);
      activitiesDetail.setFieldsCollectTemplates(Lists.newArrayList(fieldsCollectTemplateFeginVo));
    }

    // 更新活动明细执行状态
    if (BooleanEnum.FALSE.getCapital().equals(activitiesDetail.getIsExecute())) {
      this.activitiesDetailRepository.updateIsExecutedByActivitiesDetailCode(activitiesDetail.getActivitiesDetailCode());
      activitiesDetail.setIsExecute(BooleanEnum.TRUE.getCapital());
    }
    return activitiesDetail;
  }

  @Override
  public int countByActivitiesCodeAndClosed(String activitiesCode) {
    return this.activitiesDetailRepository.countByActivitiesCodeAndClosed(activitiesCode);
  }

}
