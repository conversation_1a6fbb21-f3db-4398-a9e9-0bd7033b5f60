package com.biz.crm.tpm.business.activities.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesConfigRepository;
import com.biz.crm.tpm.business.activities.local.service.ActivitiesConfigDetailService;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesConfigVoService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;

/**
 * 活动表单配置(ActivitiesConfig)表相关
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:57
 */
@Service
public class ActivitiesConfigVoServiceImpl implements ActivitiesConfigVoService {

  @Autowired
  private ActivitiesConfigRepository activitiesConfigRepository;
  @Autowired
  private ActivitiesConfigDetailService activitiesConfigDetailService;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private GenerateCodeService generateCodeService;

  /**
   * 活动配置编码前缀
   */
  private static final String ACTIVITY_CONFIG_CODE = "HDPZ";

  @Override
  public Page<ActivitiesConfigVo> findByConditions(Pageable pageable, ActivitiesConfigDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesConfigDto();
    }
    return this.activitiesConfigRepository.findByConditions(pageable, dto);
  }

  @Override
  public ActivitiesConfigVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    ActivitiesConfig activitiesConfig = this.activitiesConfigRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (activitiesConfig == null) {
      return null;
    }
    ActivitiesConfigVo activitiesConfigVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesConfig, ActivitiesConfigVo.class, LinkedHashSet.class, ArrayList.class);
    List<ActivitiesConfigDetailVo> activitiesConfigDetailVos = activitiesConfigDetailService.findByActivitiesConfigId(activitiesConfigVo.getId());
    activitiesConfigVo.setActivitiesConfigDetails(activitiesConfigDetailVos);
    return activitiesConfigVo;
  }

  @Override
  public ActivitiesConfigVo findByActivitiesConfigCode(String activitiesConfigCode) {
    if (StringUtils.isBlank(activitiesConfigCode)) {
      return null;
    }
    ActivitiesConfig activitiesConfig = this.activitiesConfigRepository.findByActivitiesConfigCode(activitiesConfigCode);
    if (activitiesConfig == null) {
      return null;
    }
    ActivitiesConfigVo activitiesConfigVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesConfig, ActivitiesConfigVo.class, LinkedHashSet.class, ArrayList.class);
    List<ActivitiesConfigDetailVo> activitiesConfigDetailVos = activitiesConfigDetailService.findByActivitiesConfigId(activitiesConfigVo.getId());
    activitiesConfigVo.setActivitiesConfigDetails(activitiesConfigDetailVos);
    return activitiesConfigVo;
  }

  @Override
  public List<ActivitiesConfigVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<ActivitiesConfig> activitiesConfigs = this.activitiesConfigRepository.findByIds(ids);
    Collection<ActivitiesConfigVo> activitiesDetailCollectFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigs, ActivitiesConfig.class, ActivitiesConfigVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailCollectFilesVos);
  }

  @Override
  @Transactional
  public ActivitiesConfigVo create(ActivitiesConfigDto activitiesConfigDto) {
    this.createValidate(activitiesConfigDto);
    String code = this.generateCodeService.generateCode(ACTIVITY_CONFIG_CODE);
    activitiesConfigDto.setActivitiesConfigCode(code);
    ActivitiesConfig activitiesConfig = this.nebulaToolkitService.copyObjectByWhiteList(activitiesConfigDto, ActivitiesConfig.class, LinkedHashSet.class, ArrayList.class);
    activitiesConfig.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesConfigRepository.saveOrUpdate(activitiesConfig);
    this.activitiesConfigDetailService.createBatch(activitiesConfig.getId(),activitiesConfigDto.getActivitiesConfigDetails());
    ActivitiesConfigVo activitiesConfigVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesConfig, ActivitiesConfigVo.class, LinkedHashSet.class, ArrayList.class);
    activitiesConfigVo.setId(activitiesConfig.getId());
    return activitiesConfigVo;
  }

  @Override
  public ActivitiesConfigVo update(ActivitiesConfigDto activitiesConfigDto) {
    this.updateValidate(activitiesConfigDto);
    ActivitiesConfig activitiesConfig = this.nebulaToolkitService.copyObjectByWhiteList(activitiesConfigDto, ActivitiesConfig.class, LinkedHashSet.class, ArrayList.class);
    activitiesConfig.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesConfigRepository.saveOrUpdate(activitiesConfig);
    this.activitiesConfigDetailService.updateBatch(activitiesConfig.getId(),activitiesConfigDto.getActivitiesConfigDetails());
    ActivitiesConfigVo activitiesConfigVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesConfig, ActivitiesConfigVo.class, LinkedHashSet.class, ArrayList.class);
    activitiesConfigVo.setId(activitiesConfig.getId());
    return activitiesConfigVo;
  }

  /**
   * 活动表单类型编号查询所有数据
   *
   * @param activitiesFormTypeCode 活动表单类型编号
   * @return 所有数据
   */
  @Override
  public List<ActivitiesConfigVo> findByActivitiesFormTypeCode(String activitiesFormTypeCode) {
    if(StringUtils.isBlank(activitiesFormTypeCode)){
      return null;
    }
    List<ActivitiesConfig> activitiesConfigs = this.activitiesConfigRepository.findByActivitiesFormTypeCode(activitiesFormTypeCode, EnableStatusEnum.ENABLE.getCode());
    return (List)this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigs, ActivitiesConfig.class, ActivitiesConfigVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 创建验证
   *
   * @param activitiesConfigDto
   */
  private void createValidate(ActivitiesConfigDto activitiesConfigDto) {
    Validate.notNull(activitiesConfigDto, "新增时，对象信息不能为空！");
    activitiesConfigDto.setId(null);
    activitiesConfigDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    Validate.notBlank(activitiesConfigDto.getActivitiesFormTypeCode(), "新增数据时，活动表单类型编号不能为空！");
    Validate.notBlank(activitiesConfigDto.getActivitiesFormTypeName(), "新增数据时，活动表单类型名称不能为空！");
    Validate.notBlank(activitiesConfigDto.getDynamicFormCode(), "新增数据时，动态表单编号不能为空！");
    Validate.notBlank(activitiesConfigDto.getDynamicFormName(), "新增数据时，采集请求名称不能为空！");
    Validate.notBlank(activitiesConfigDto.getActivitiesConfigName(), "新增数据时，活动表单名称不能为空！");
    Validate.notBlank(activitiesConfigDto.getEnableStatus(), "新增数据时，使用状态不能为空！");
    Validate.isTrue(!CollectionUtils.isEmpty(activitiesConfigDto.getActivitiesConfigDetails()),"新增数据时，表单注册器集合不能为空!");
  }

  /**
   * 创建验证
   *
   * @param activitiesConfigDto
   */
  private void updateValidate(ActivitiesConfigDto activitiesConfigDto) {
    Validate.notNull(activitiesConfigDto, "编辑时，对象信息不能为空！");
    Validate.notBlank(activitiesConfigDto.getActivitiesFormTypeCode(), "编辑数据时，活动表单类型编号不能为空！");
    Validate.notBlank(activitiesConfigDto.getId(), "编辑数据时，主键不能为空！");
    Validate.notBlank(activitiesConfigDto.getActivitiesFormTypeName(), "编辑数据时，活动表单类型名称不能为空！");
    Validate.notBlank(activitiesConfigDto.getDynamicFormCode(), "编辑数据时，动态表单编号不能为空！");
    Validate.notBlank(activitiesConfigDto.getDynamicFormName(), "编辑数据时，采集请求名称不能为空！");
    Validate.notBlank(activitiesConfigDto.getActivitiesConfigName(), "编辑数据时，活动表单名称不能为空！");
    Validate.notBlank(activitiesConfigDto.getActivitiesConfigCode(), "编辑数据时，活动表单编码不能为空！");
    Validate.isTrue(!CollectionUtils.isEmpty(activitiesConfigDto.getActivitiesConfigDetails()),"编辑数据时，表单注册器集合不能为空!");
  }
}
