package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ActivitiesTemplateConfig", description = "活动模板配置实体类")
@Entity
@org.hibernate.annotations.Table(appliesTo = "`tpm_activities_template_config`", comment = "活动模板配置实体类")
@TableName("tpm_activities_template_config")
@Table(
        name = "`tpm_activities_template_config`",
        indexes = @Index(name = "uk_config_code", columnList = "config_code", unique = true))
public class ActivitiesTemplateConfig extends TenantFlagOpEntity {

    /**
     * 配置编码
     */
    @ApiModelProperty("配置编码")
    @Column(name = "config_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '配置编码'")
    private String configCode;

    /**
     * 配置名称
     */
    @ApiModelProperty("配置名称")
    @Column(name = "config_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '配置名称'")
    private String configName;

    /**
     * 配置表类型
     */
    @ApiModelProperty("配置表类型")
    @Column(name = "type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '配置表类型'")
    private String type;

    @ApiModelProperty("职位编码")
    @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
    private String positionCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", length = 64, columnDefinition = "varchar(64) COMMENT '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
    private String orgName;
}
