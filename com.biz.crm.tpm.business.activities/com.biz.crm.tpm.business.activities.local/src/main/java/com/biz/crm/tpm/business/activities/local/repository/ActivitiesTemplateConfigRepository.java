package com.biz.crm.tpm.business.activities.local.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesTemplateConfig;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesTemplateConfigMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesTemplateConfigDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;


/**
 * 活动模板配置实体类(ActivitiesTemplateConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-31 15:57:01
 */
@Component
public class ActivitiesTemplateConfigRepository extends ServiceImpl<ActivitiesTemplateConfigMapper, ActivitiesTemplateConfig> {

  @Autowired
  private ActivitiesTemplateConfigMapper activitiesTemplateConfigMapper;

  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param dto 实体对象
   */
  public Page<ActivitiesTemplateConfigVo> findByConditions(Pageable pageable, ActivitiesTemplateConfigDto dto) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesTemplateConfigVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesTemplateConfigMapper.findByConditions(page, dto);
  }

  /**
   * 配置编码查询配置
   * @param configCode 配置编码
   */
  public ActivitiesTemplateConfig findByCode(String configCode){
    if (StringUtils.isBlank(configCode)) {
      return null;
    }
    return this.getOne(Wrappers.lambdaQuery(ActivitiesTemplateConfig.class)
            .eq(ActivitiesTemplateConfig::getConfigCode,configCode)
            .eq(ActivitiesTemplateConfig::getTenantCode, TenantUtils.getTenantCode())
            .eq(ActivitiesTemplateConfig::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
    );
  }

  public ActivitiesTemplateConfig findById(String id) {
    return this.lambdaQuery()
            .eq(ActivitiesTemplateConfig::getTenantCode, TenantUtils.getTenantCode())
            .eq(ActivitiesTemplateConfig::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivitiesTemplateConfig::getId, id)
            .one();
  }

  public List<ActivitiesTemplateConfig> findByIdList(List<String> idList) {
    return this.lambdaQuery()
            .eq(ActivitiesTemplateConfig::getTenantCode, TenantUtils.getTenantCode())
            .eq(ActivitiesTemplateConfig::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .in(ActivitiesTemplateConfig::getId, idList)
            .list();
  }

  /**
   * 按id批量删除
   *
   * @param ids
   */
  public void deleteConfigByCodes(List<String> ids) {
    this.lambdaUpdate().in(ActivitiesTemplateConfig::getConfigCode, ids).remove();
  }

  /**
   * 配置编码查询配置
   * @param configCodes 配置编码
   */
  public List<ActivitiesTemplateConfig> findByCodes(Set<String> configCodes){
    if (CollectionUtils.isEmpty(configCodes)) {
      return new ArrayList<>(0);
    }
    return this.lambdaQuery()
            .in(ActivitiesTemplateConfig::getConfigCode,configCodes)
            .eq(ActivitiesTemplateConfig::getTenantCode, TenantUtils.getTenantCode())
            .eq(ActivitiesTemplateConfig::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .list();
  }
  
}

