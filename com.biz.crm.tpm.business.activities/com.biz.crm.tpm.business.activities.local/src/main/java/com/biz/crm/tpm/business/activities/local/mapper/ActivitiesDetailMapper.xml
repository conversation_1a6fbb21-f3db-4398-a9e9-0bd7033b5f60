<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.activities.local.mapper.ActivitiesDetailMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetail" id="ActivitiesDetailMap">
  </resultMap>

  <update id="updateStatusByActivitiesCode">
    update tpm_ordinary_activity t1 set t1.`status` = #{status}
    WHERE t1.`code` = #{activitiesCode}
    and t1.tenant_code = #{tenantCode}
    and t1.del_flag = '${@<EMAIL>()}'
  </update>

  <update id="updateStatusBySchemeCode">
    update tpm_activities_scheme t1 set t1.`status` = #{status}
    WHERE t1.activities_code = #{activitiesCode}
    and t1.tenant_code = #{tenantCode}
    and t1.scheme_code = #{schemeCode}
    and t1.del_flag = '${@<EMAIL>()}'
  </update>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo">
    select distinct t.*
    from tpm_activities_detail t
    left join tpm_activities ta on ta.activities_code = t.activities_code and ta.tenant_code = t.tenant_code
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.createName != null and dto.createName != ''">
        <bind name="createName" value="'%' + dto.createName + '%'"/>
        and t.create_name like #{createName}
      </if>
      <if test="dto.activitiesCode != null and dto.activitiesCode != ''">
        <bind name="activitiesCode" value="'%' + dto.activitiesCode + '%'"/>
        and t.activities_code like #{activitiesCode}
      </if>
      <if test="dto.activitiesName != null and dto.activitiesName != ''">
        <bind name="activitiesName" value="'%' + dto.activitiesName + '%'"/>
        and t.activities_name like #{activitiesName}
      </if>
      <if test="dto.activitiesDetailCode != null and dto.activitiesDetailCode != ''">
        and t.activities_detail_code = #{dto.activitiesDetailCode}
      </if>
      <if test="dto.costTypeCategoryName != null and dto.costTypeCategoryName != ''">
        <bind name="costTypeCategoryName" value="'%' + dto.costTypeCategoryName + '%'"/>
        and t.cost_type_category_name like #{costTypeCategoryName}
      </if>
      <if test="dto.costTypeCategoryCode != null and dto.costTypeCategoryCode != ''">
        and t.cost_type_category_code = #{dto.costTypeCategoryCode}
      </if>
      <if test="dto.costBudgetCode != null and dto.costBudgetCode != ''">
        and t.cost_budget_code = #{dto.costBudgetCode}
      </if>
      <if test="dto.costTypeDetailCode != null and dto.costTypeDetailCode != ''">
        and t.cost_type_detail_code = #{dto.costTypeDetailCode}
      </if>
      <if test="dto.orgCode != null and dto.orgCode != ''">
        and t.org_code = #{dto.orgCode}
      </if>
      <if test="dto.orgCodes != null and dto.orgCodes.size > 0">
        and t.org_code in
        <foreach item="item" collection="dto.orgCodes" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
      </if>
      <if test="dto.isSendSfa != null and dto.isSendSfa != ''">
        and t.is_send_sfa = #{dto.isSendSfa}
      </if>
      <if test="dto.terminalCode != null and dto.terminalCode != ''">
        and ((t.terminal_code is null or t.terminal_code='') or (t.terminal_code is not null and t.terminal_code = #{dto.terminalCode}))
      </if>
      <if test="dto.customerCode != null and dto.customerCode != ''">
        and ((t.customer_code is null or t.customer_code='') or (t.customer_code is not null and t.customer_code = #{dto.customerCode}))
      </if>
      <if test="dto.customerCodes != null and dto.customerCodes.size > 0">
        and ((t.customer_code is null or t.customer_code='') or (t.customer_code is not null and t.customer_code in
        <foreach item="item" collection="dto.customerCodes" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
        ))
      </if>
      <if test="dto.activitiesCodes != null and dto.activitiesCodes.size > 0">
        and t.activities_code in
        <foreach item="item" collection="dto.activitiesCodes" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
      </if>
      <if test="dto.activitiesDetailCodes != null and dto.activitiesDetailCodes.size > 0">
        and t.activities_detail_code in
        <foreach item="item" collection="dto.activitiesDetailCodes" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
      </if>
      <if test="dto.excludeCostTypeDetailCodes != null and dto.excludeCostTypeDetailCodes.size > 0">
        and t.cost_type_detail_code not in
        <foreach item="item" collection="dto.excludeCostTypeDetailCodes" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
      </if>
      <if test="dto.excludeItemCodes != null and dto.excludeItemCodes.size > 0">
        and t.activities_detail_code not in
        <foreach item="item" collection="dto.excludeItemCodes" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
      </if>
      <if test="dto.isValid != null and dto.isValid !=''">
        and now() between ta.begin_time and ta.end_time
      </if>
      <if test="dto.isExecute !=null and dto.isExecute !=''">
        and t.is_execute = #{dto.isExecute}
      </if>
      <if test="dto.isFullAudit !=null and dto.isFullAudit !=''">
        and t.is_full_audit = #{dto.isFullAudit}
      </if>
      and t.is_close != 'Y'
    </where>
    order by t.create_time desc, t.id
  </select>
</mapper>
