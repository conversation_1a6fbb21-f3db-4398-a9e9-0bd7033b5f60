<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.local.mapper.ActivitiesConfigMapper">

  <resultMap type="com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig" id="activitiesConfigMap">
    <result property="id" column="id" jdbcType="VARCHAR"/>
    <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
    <result property="activitiesCode" column="activities_code" jdbcType="VARCHAR"/>
    <result property="activitiesFormTypeCode" column="activities_form_type_code" jdbcType="VARCHAR"/>
    <result property="activitiesFormTypeName" column="activities_form_type_name" jdbcType="VARCHAR"/>
    <result property="activitiesName" column="activities_name" jdbcType="VARCHAR"/>
    <result property="dynamicFormCode" column="dynamic_form_code" jdbcType="VARCHAR"/>
    <result property="dynamicFormName" column="dynamic_form_name" jdbcType="VARCHAR"/>
  </resultMap>


  <sql id = "activitiesConfig">
    tenant_code tenantCode,
    activities_config_code activitiesConfigCode,
    activities_form_type_code activitiesFormTypeCode,
    activities_form_type_name activitiesFormTypeName,
    activities_config_name activitiesConfigName,
    dynamic_form_code dynamicFormCode,
    dynamic_form_name dynamicFormName,
    id id
  </sql>

  <select id="findByConditions" resultMap="activitiesConfigMap">
    select
      t.*
    from tpm_activities_config t
    <where>
      and t.tenant_code = #{dto.tenantCode}
      <if test="dto.delFlag !=null and dto.delFlag != '' ">
        and t.del_flag = #{dto.delFlag}
      </if>
      <if test="dto.activitiesConfigCode != null and dto.activitiesConfigCode != ''">
        <bind name="activitiesConfigCodeLike" value="'%' + dto.activitiesConfigCode + '%'"/>
        and t.activities_config_code like #{activitiesConfigCodeLike}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus != ''">
        and t.enable_status = #{dto.enableStatus}
      </if>
      <if test="dto.activitiesConfigName != null and dto.activitiesConfigName != ''">
        <bind name="activitiesConfigNameLike" value="'%' + dto.activitiesConfigName + '%'"/>
        and t.activities_config_name like #{activitiesConfigNameLike}
      </if>
    </where>
    order by t.create_time desc, t.id
  </select>
</mapper>

