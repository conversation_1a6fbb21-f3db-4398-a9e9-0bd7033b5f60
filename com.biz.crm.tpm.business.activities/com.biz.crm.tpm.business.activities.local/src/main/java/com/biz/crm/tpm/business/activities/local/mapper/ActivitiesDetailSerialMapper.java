package com.biz.crm.tpm.business.activities.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailSerial;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailSerialDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailSerialVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 活动明细订单表(SFA中的订单);(tpm_activities_detail_serial)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */
@Mapper
public interface ActivitiesDetailSerialMapper extends BaseMapper<ActivitiesDetailSerial> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto  动态查询条件
   * @return 分页对象列表
   */
  Page<ActivitiesDetailSerialVo> findByConditions(@Param("page") Page<ActivitiesDetailSerialVo> page, @Param("dto") ActivitiesDetailSerialDto dto);

  /**
   * 根据活动明细编号统计使用金额
   *
   * @param activitiesDetailCode
   * @return
   */
  BigDecimal sumTotalAmountByActivitiesDetailCode(@Param("tenantCode") String tenantCode, @Param("activitiesDetailCode") String activitiesDetailCode);
}
