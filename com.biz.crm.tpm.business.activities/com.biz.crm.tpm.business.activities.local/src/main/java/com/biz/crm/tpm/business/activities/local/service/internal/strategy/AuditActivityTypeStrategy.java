package com.biz.crm.tpm.business.activities.local.service.internal.strategy;

import com.biz.crm.tpm.business.activities.sdk.strategy.activityExecutive.ActivityExecutiveStrategy;
import com.biz.crm.tpm.business.activities.sdk.strategy.activityType.ActivityTypeStrategy;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 表单类型策略 核销
 * <AUTHOR> rentao
 * @date : 2022/10/30 21:11
 */
@Component
@Deprecated
public class AuditActivityTypeStrategy implements ActivityTypeStrategy {

  /**
   * 策略的中文说明信息
   */
  private static final String ACTIVITY_TYPE = "核销表单";


  @Override
  public String getActivitiesFormTypeCode() {
    return StringUtils.uncapitalize(this.getClass().getSimpleName());
  }

  @Override
  public String getActivitiesFormTypeName() {
    return ACTIVITY_TYPE;
  }

  @Override
  public Collection<Class<? extends ActivityExecutiveStrategy>> getBindExecutiveStrategy() {
    return Sets.newHashSet(PushSfaActivityExecutiveStrategy.class);
  }
}
