package com.biz.crm.tpm.business.activities.local.service.internal.process;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.local.service.internal.AlongWithOrderPageCacheHelper;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDetailDto;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderDetailImportVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 随单比例缓存导入
 */
@Slf4j
@Component
public class AlongWithOrderDetailImportProcess implements ImportProcess<AlongWithOrderDetailImportVo> {

    @Autowired
    private CustomerVoService customerVoServiceFeign;
    @Autowired
    private ProductVoService productVoServiceFeign;
    @Autowired
    private OrgVoService orgVoService;
    @Autowired
    private AlongWithOrderPageCacheHelper alongWithOrderPageCacheHelper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, AlongWithOrderDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, AlongWithOrderDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        Validate.notNull(params.get("cacheKey"), "缓存键，参数未传入");

        for (Map.Entry<Integer, AlongWithOrderDetailImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            AlongWithOrderDetailImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getDepartmentCode()), "部门编码，不能为空！");
            //this.validateIsTrue(StringUtils.isNotEmpty(vo.getCustomerCode()), "客户编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getProductCode()), "产品编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getRatioStr()), "随单比例上限，不能为空！");
            if (StringUtils.isNotBlank(vo.getRatioStr())) {
                BigDecimal ratio = null;
                try {
                    ratio = new BigDecimal(vo.getRatioStr().trim());
                } catch (Exception e) {
                    this.validateIsTrue(false, "随单比例上限类型转换失败！");
                }
                this.validateIsTrue(ratio.compareTo(BigDecimal.ZERO) > 0 && ratio.compareTo(BigDecimal.ONE) <= 0, "随单比例上限必须大于0且小于1");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, AlongWithOrderDetailImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();

        Set<String> orgCodeSet = new HashSet<>();
        Set<String> productCodeSet = new HashSet<>();
        Set<String> customerCodeSet = new HashSet<>();

        for (Map.Entry<Integer, AlongWithOrderDetailImportVo> row : data.entrySet()) {
            AlongWithOrderDetailImportVo vo = row.getValue();
            orgCodeSet.add(vo.getDepartmentCode());
            productCodeSet.add(vo.getProductCode());
            if (StringUtils.isNotEmpty(vo.getCustomerCode())) {
                customerCodeSet.add(vo.getCustomerCode());
            }
        }

        Map<String, OrgVo> orgVoMap = orgVoService.findByOrgCodes(new ArrayList<>(orgCodeSet)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), Function.identity(), (a, b) -> a));
        Map<String, CustomerVo> customerVoMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(customerCodeSet)) {
            List<CustomerVo> customerVoList = customerVoServiceFeign.findByCustomerCodes(new ArrayList<>(customerCodeSet));
            if (CollectionUtil.isNotEmpty(customerVoList)) {
                customerVoMap.putAll(customerVoList.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode,
                        v -> v, (a, b) -> a)));
            }
        }

        Map<String, ProductVo> productVoMap = productVoServiceFeign.findDetailsByIdsOrProductCodes(null, new ArrayList<>(productCodeSet)).stream().collect(Collectors.toMap(e -> e.getProductCode(), Function.identity(), (a, b) -> a));

        for (Map.Entry<Integer, AlongWithOrderDetailImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            AlongWithOrderDetailImportVo vo = row.getValue();

            vo.setRatio(new BigDecimal(vo.getRatioStr()));
            if (orgVoMap.containsKey(vo.getDepartmentCode())) {
                vo.setDepartmentName(orgVoMap.get(vo.getDepartmentCode()).getOrgName());
                vo.setLevelNum(orgVoMap.get(vo.getDepartmentCode()).getLevelNum());
            } else {
                this.validateIsTrue(false, "部门，未找到！");
            }
            if (productVoMap.containsKey(vo.getProductCode())) {
                vo.setProductName(productVoMap.get(vo.getProductCode()).getProductName());
            } else {
                this.validateIsTrue(false, "产品，未找到！");
            }
            if (StringUtils.isNotEmpty(vo.getCustomerCode())) {
                if (customerVoMap.containsKey(vo.getCustomerCode())) {
                    CustomerVo customerVo = customerVoMap.get(vo.getCustomerCode());
                    this.validateIsTrue(BooleanEnum.TRUE.getCapital().equals(customerVo.getContractCustomer()), "客户不是合同客户");
                    vo.setCustomerCode(customerVo.getCustomerCode());
                    vo.setCustomerName(customerVo.getCustomerName());
                } else {
                    this.validateIsTrue(false, "客户，未找到！");
                }
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        Collection<AlongWithOrderDetailDto> alongWithOrderDetailDtos = nebulaToolkitService.copyCollectionByWhiteList(data.values(), AlongWithOrderDetailImportVo.class, AlongWithOrderDetailDto.class, LinkedHashSet.class, ArrayList.class);

        alongWithOrderPageCacheHelper.importNewItem((String) params.get("cacheKey"), (List<AlongWithOrderDetailDto>) alongWithOrderDetailDtos);
        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<AlongWithOrderDetailImportVo> findCrmExcelVoClass() {
        return AlongWithOrderDetailImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "ALONG_WITH_ORDER_DETAIL_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "随单比例缓存导入模板";
    }
}
