package com.biz.crm.tpm.business.activities.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.local.repository.AlongWithOrderDetailRepository;
import com.biz.crm.tpm.business.activities.sdk.constant.AlongWithOrderConstant;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDetailDto;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 随单比例管控分页缓存
 */
@Slf4j
@Component
public class AlongWithOrderPageCacheHelper extends BusinessPageCacheHelper<AlongWithOrderDetailVo, AlongWithOrderDetailDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private AlongWithOrderDetailRepository alongWithOrderDetailRepository;
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return AlongWithOrderConstant.CACHE_KEY_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<AlongWithOrderDetailDto> getDtoClass() {
        return AlongWithOrderDetailDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<AlongWithOrderDetailVo> getVoClass() {
        return AlongWithOrderDetailVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param alongWithOrderDetailDto
     * @param cacheKey
     */
    @Override
    public List<AlongWithOrderDetailDto> findDtoListFromRepository(AlongWithOrderDetailDto alongWithOrderDetailDto, String cacheKey) {
        if (StringUtils.isBlank(alongWithOrderDetailDto.getAlongWithOrderCode())) {
            return new ArrayList<>();
        }
        List<AlongWithOrderDetailVo> alongWithOrderDetailVos = alongWithOrderDetailRepository.findByDto(alongWithOrderDetailDto);
        return CollectionUtils.isNotEmpty(alongWithOrderDetailVos) ? (List<AlongWithOrderDetailDto>) nebulaToolkitService.copyCollectionByBlankList(alongWithOrderDetailVos, AlongWithOrderDetailVo.class, AlongWithOrderDetailDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<AlongWithOrderDetailDto> newItem(String cacheKey, List<AlongWithOrderDetailDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<AlongWithOrderDetailDto> copyItem(String cacheKey, List<AlongWithOrderDetailDto> itemList) {
        List<AlongWithOrderDetailDto> newItemList = (List<AlongWithOrderDetailDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, AlongWithOrderDetailDto.class, AlongWithOrderDetailDto.class, HashSet.class, ArrayList.class);
        for (AlongWithOrderDetailDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 导入新增数据
     * @param cacheKey
     * @param itemList
     * @return
     */
    public void importNewItem(String cacheKey, List<AlongWithOrderDetailDto> itemList) {
        String redisCacheIdKey = this.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = this.getRedisCacheDataKey(cacheKey);

        //导入新增数据
        for (AlongWithOrderDetailDto newItem : itemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }

        Object[] newIdArr = itemList.stream().map(this::getDtoKey).toArray();
        redisService.lPushAll(redisCacheIdKey, this.getExpireTime(),newIdArr);

        Map<Object, AlongWithOrderDetailDto> updateMap = itemList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }

    /**
     * 获取Dto中的主键
     *
     * @param alongWithOrderDetailDto
     * @return 主键
     */
    @Override
    public Object getDtoKey(AlongWithOrderDetailDto alongWithOrderDetailDto) {
        return alongWithOrderDetailDto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param alongWithOrderDetailDto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(AlongWithOrderDetailDto alongWithOrderDetailDto) {
        return alongWithOrderDetailDto.getChecked();
    }

}
