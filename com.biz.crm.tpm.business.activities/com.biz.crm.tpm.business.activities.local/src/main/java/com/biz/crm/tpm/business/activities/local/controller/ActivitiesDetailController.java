package com.biz.crm.tpm.business.activities.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailRemoteDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 活动明细信息表;(tpm_activities_detail)控制层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-16
 */
@Api(tags = "活动明细信息表功能接口")
@RestController
@RequestMapping("/v1/activities/activitiesDetail")
@Slf4j
public class ActivitiesDetailController {
  /**
   * 服务对象
   */
  @Autowired
  private ActivitiesDetailService activitiesDetailService;

  /**
   * 通过编号查询多条数据
   *
   * @param activitiesCode 活动编号
   * @return 多条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByCode")
  public Result<List<ActivitiesDetailVo>> findByCode(@ApiParam(name = "activitiesCode", value = "活动编号", required = true) String activitiesCode) {
    try {
      List<ActivitiesDetailVo> activitiesDetailVos = this.activitiesDetailService.findByActivitiesCode(activitiesCode);
      return Result.ok(activitiesDetailVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 活动明细关闭活动
   *
   * @param activitiesDetailDtos 分页对象
   * @return 所有数据
   */
  @ApiOperation(value = "活动明细关闭活动")
  @PostMapping("closed")
  public Result<?> closed(@RequestBody @ApiParam(name = "activitiesDetails", value = "关闭活动明细信息") List<ActivitiesDetailDto> activitiesDetailDtos) {
    try {
      this.activitiesDetailService.closed(activitiesDetailDtos);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主表编号查询数据
   *
   * @param activitiesCodes 编号
   */
  @ApiOperation(value = "通过活动编号查询数据")
  @GetMapping("findAllByActivitiesCodes")
  public Result<List<ActivitiesDetailVo>> findAllByActivitiesCodes(@RequestParam("activitiesCodes") @ApiParam(name = "activitiesCodes", value = "编号", required = true) Set<String> activitiesCodes) {
    try {
      List<ActivitiesDetailVo> detailVos = this.activitiesDetailService.findAllByActivitiesCodes(activitiesCodes);
      return Result.ok(detailVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页查询所有数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<ActivitiesDetailVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                           @ApiParam(name = "activitiesDetail", value = "核销采集信息") ActivitiesDetailDto dto) {
    try {
      Page<ActivitiesDetailVo> page = this.activitiesDetailService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主表编号查询数据(详情)
   *
   * @param activitiesDetailCode 编号
   */
  @ApiOperation(value = "通过活动编号查询数据")
  @GetMapping("findDetailByActivitiesDetailCode")
  public Result<ActivitiesDetailVo> findDetailByActivitiesDetailCode(@RequestParam("activitiesDetailCode") @ApiParam(name = "activitiesDetailCode", value = "编号", required = true) String activitiesDetailCode) {
    try {
      ActivitiesDetailVo detailVs = this.activitiesDetailService.findDetailByActivitiesDetailCode(activitiesDetailCode);
      return Result.ok(detailVs);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 相关活动明细信息保存(SFA TO TPM)
   */
  @ApiOperation(value = "相关活动明细信息保存(SFA TO TPM)")
  @PostMapping("createRemote")
  public Result<ActivitiesDetailVo> createRemote(@RequestBody @ApiParam(name = "dto", value = "相关活动明细信息") ActivitiesDetailRemoteDto dto) {
    try {
      ActivitiesDetailVo result = this.activitiesDetailService.createRemote(dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
