package com.biz.crm.tpm.business.activities.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.local.entity.*;
import com.biz.crm.tpm.business.activities.local.entity.AlongWithOrder;
import com.biz.crm.tpm.business.activities.local.entity.AlongWithOrderDetail;
import com.biz.crm.tpm.business.activities.local.repository.AlongWithOrderDetailRepository;
import com.biz.crm.tpm.business.activities.local.repository.AlongWithOrderRepository;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.sdk.constant.AlongWithOrderConstant;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDto;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDto;
import com.biz.crm.tpm.business.activities.sdk.dto.log.AlongWithOrderLogEventDto;
import com.biz.crm.tpm.business.activities.sdk.event.log.AlongWithOrderLogEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.AlongWithOrderService;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 随单比例管控
 */
@Service
@Slf4j
public class AlongWithOrderServiceImpl extends BusinessPageCacheServiceImpl<AlongWithOrderDetailVo, AlongWithOrderDetailDto> implements AlongWithOrderService {

    @Autowired(required = false)
    private AlongWithOrderRepository alongWithOrderRepository;
    @Autowired(required = false)
    private AlongWithOrderDetailRepository alongWithOrderDetailRepository;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @Override
    public void create(AlongWithOrderDto dto, String cacheKey) {
        List<AlongWithOrderDetailDto> cacheList = findCacheList(cacheKey);
        dto.setCacheList(cacheList);
        createValidate(dto);
        store(dto);

        //新增业务日志
        AlongWithOrderLogEventDto logEventDto = new AlongWithOrderLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(dto);
        SerializableBiConsumer<AlongWithOrderLogEventListener, AlongWithOrderLogEventDto> onCreate =
                AlongWithOrderLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, AlongWithOrderLogEventListener.class, onCreate);

        clearCache(cacheKey);
    }

    /**
     * 修改数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @Override
    public void update(AlongWithOrderDto dto, String cacheKey) {
        List<AlongWithOrderDetailDto> cacheList = findCacheList(cacheKey);
        dto.setCacheList(cacheList);
        updateValidate(dto);
        AlongWithOrder entityOld = alongWithOrderRepository.findByCode(dto.getAlongWithOrderCode());
        store(dto);

        //编辑业务日志
        AlongWithOrderLogEventDto logEventDto = new AlongWithOrderLogEventDto();
        logEventDto.setOriginal(this.nebulaToolkitService.copyObjectByWhiteList(entityOld, AlongWithOrderDto.class, LinkedHashSet.class, ArrayList.class));
        logEventDto.setNewest(dto);
        SerializableBiConsumer<AlongWithOrderLogEventListener, AlongWithOrderLogEventDto> onUpdate =
                AlongWithOrderLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, AlongWithOrderLogEventListener.class, onUpdate);

        clearCache(cacheKey);
    }

    /**
     * 保存
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void store(AlongWithOrderDto dto) {
        if (StringUtils.isBlank(dto.getId())) {
            FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
            Validate.notNull(loginUserDetails, "未找到当前登陆人信息！");
            dto.setOrgCode(loginUserDetails.getOrgCode());
            dto.setOrgName(loginUserDetails.getOrgName());
            dto.setPositionCode(loginUserDetails.getPostCode());
            // redis生成料编码code
            List<String> codeList = this.generateCodeService.generateCodeNotDate(AlongWithOrderConstant.PREFIX_CODE, 1);
            dto.setAlongWithOrderCode(codeList.get(0));
            dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            dto.setTenantCode(TenantUtils.getTenantCode());
        } else {
            alongWithOrderDetailRepository.deleteByCode(dto.getAlongWithOrderCode());
        }
        List<AlongWithOrderDetailDto> cacheList = dto.getCacheList();
        cacheList.forEach(e -> {
            e.setAlongWithOrderCode(dto.getAlongWithOrderCode());
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setTenantCode(TenantUtils.getTenantCode());
        });
        AlongWithOrder entity = nebulaToolkitService.copyObjectByWhiteList(dto, AlongWithOrder.class, HashSet.class, ArrayList.class);
        Collection<AlongWithOrderDetail> details = nebulaToolkitService.copyCollectionByWhiteList(cacheList, AlongWithOrderDetailDto.class, AlongWithOrderDetail.class, HashSet.class, ArrayList.class);
        alongWithOrderRepository.saveOrUpdate(entity);
        alongWithOrderDetailRepository.saveBatch(details);
    }


    /**
     * 创建验证
     *
     * @param dto
     */
    private void createValidate(AlongWithOrderDto dto) {
        Validate.notNull(dto, "新增时，对象信息不能为空！");
        commonValidate(dto);
        dto.setId(null);
    }

    /**
     * 修改验证
     *
     * @param dto
     */
    private void updateValidate(AlongWithOrderDto dto) {
        Validate.notNull(dto, "修改时，对象信息不能为空！");
        commonValidate(dto);
        Validate.notBlank(dto.getId(), "主键id不能为空！");
    }

    /**
     * 公有校验
     *
     * @param dto
     */
    private void commonValidate(AlongWithOrderDto dto) {
        Validate.notBlank(dto.getAlongWithOrderName(), "名称，不能为空");
        Validate.notBlank(dto.getStartYearMonth(), "开始年月，不能为空");
        Validate.notBlank(dto.getEndYearMonth(), "结束年月，不能为空");
        Validate.notEmpty(dto.getCacheList(), "管控范围，不能为空");

        dto.getCacheList().forEach(item -> {
            Validate.notBlank(item.getDepartmentCode(), "部门，不能为空");
            //1045909 【随单比例上限】维度“客户”可以为空
            //Validate.notBlank(item.getCustomerCode(), "客户，不能为空");
            Validate.notBlank(item.getProductCode(), "产品，不能为空");
            Validate.notBlank(item.getRatioStr(), "随单比例上限，不能为空");
            try {
                item.setRatio(new BigDecimal(item.getRatioStr()));
            } catch (Exception e) {
                throw new RuntimeException("随单比例上限必须大于0且小于1");
            }
            Validate.isTrue(item.getRatio().compareTo(BigDecimal.ZERO) > 0 && item.getRatio().compareTo(BigDecimal.ONE) <= 0, "随单比例上限必须大于0且小于1");
        });
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> idList) {
        Validate.notEmpty(idList, "删除时，id不能为空");
        List<AlongWithOrder> list = this.alongWithOrderRepository.findByIds(idList);
        Validate.notEmpty(list, "未找到对应的数据");
        List<String> codeList = list.stream().map(e -> e.getAlongWithOrderCode()).collect(Collectors.toList());
        alongWithOrderDetailRepository.deleteByCodes(codeList);
        alongWithOrderRepository.deleteByCodes(codeList);

        //删除业务日志
        Collection<AlongWithOrderDto> dtoList = nebulaToolkitService.copyCollectionByWhiteList(list,
                AlongWithOrder.class, AlongWithOrderDto.class, HashSet.class, ArrayList.class);
        AlongWithOrderLogEventDto logEventDto = new AlongWithOrderLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(new ArrayList<>(dtoList));
        SerializableBiConsumer<AlongWithOrderLogEventListener, AlongWithOrderLogEventDto> onDelete =
                AlongWithOrderLogEventListener::onDelete;
        this.nebulaNetEventClient.publish(logEventDto, AlongWithOrderLogEventListener.class, onDelete);
    }

    /**
     * 启用
     *
     * @param idList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enable(List<String> idList) {
        Validate.notEmpty(idList, "启用时，id不能为空");
        List<AlongWithOrder> list = this.alongWithOrderRepository.findByIds(idList);
        Validate.notEmpty(list, "未找到对应的数据");
        this.alongWithOrderRepository.updateEnableStatusByIds(EnableStatusEnum.ENABLE, idList);

        //启用业务日志
        Collection<AlongWithOrderDto> dtoList = nebulaToolkitService.copyCollectionByWhiteList(list,
                AlongWithOrder.class, AlongWithOrderDto.class, HashSet.class, ArrayList.class);
        AlongWithOrderLogEventDto logEventDto = new AlongWithOrderLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(new ArrayList<>(dtoList));
        SerializableBiConsumer<AlongWithOrderLogEventListener, AlongWithOrderLogEventDto> onEnable =
                AlongWithOrderLogEventListener::onEnable;
        this.nebulaNetEventClient.publish(logEventDto, AlongWithOrderLogEventListener.class, onEnable);
    }

    /**
     * 禁用
     *
     * @param idList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void disable(List<String> idList) {
        Validate.notEmpty(idList, "启用时，id不能为空");
        List<AlongWithOrder> list = this.alongWithOrderRepository.findByIds(idList);
        Validate.notEmpty(list, "未找到对应的数据");
        this.alongWithOrderRepository.updateEnableStatusByIds(EnableStatusEnum.DISABLE, idList);

        //禁用业务日志
        Collection<AlongWithOrderDto> dtoList = nebulaToolkitService.copyCollectionByWhiteList(list,
                AlongWithOrder.class, AlongWithOrderDto.class, HashSet.class, ArrayList.class);
        AlongWithOrderLogEventDto logEventDto = new AlongWithOrderLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(new ArrayList<>(dtoList));
        SerializableBiConsumer<AlongWithOrderLogEventListener, AlongWithOrderLogEventDto> onDisable =
                AlongWithOrderLogEventListener::onDisable;
        this.nebulaNetEventClient.publish(logEventDto, AlongWithOrderLogEventListener.class, onDisable);
    }

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    @Override
    public AlongWithOrderVo findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        AlongWithOrder entity = alongWithOrderRepository.findByCode(code);
        if (null == entity) {
            return null;
        }
        AlongWithOrderVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, AlongWithOrderVo.class, HashSet.class, ArrayList.class);
        return vo;
    }

    /**
     * 获取所有缓存
     *
     * @param cacheKey
     * @return
     */
    @Override
    public List<AlongWithOrderDetailDto> findAllCacheList(String cacheKey) {
        return findCacheList(cacheKey);
    }


    @Resource
    private OrgVoService orgVoService;

    @Override
    public BigDecimal getRatioByCondition(String departmentCode, String customerCode, List<String> productCodes,
                                          String years) {
        List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(departmentCode);
        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
        return alongWithOrderRepository.getRatioByCondition(orgCodes, customerCode, productCodes, years);
    }

    @Override
    public Page<AlongWithOrderDetailVo> findCachePageList(Pageable pageable, AlongWithOrderDetailDto dto, String cacheKey) {
        String redisCacheIdKey = helper.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = helper.getRedisCacheDataKey(cacheKey);
        String redisCacheInitKey = helper.getRedisCacheInitKey(cacheKey);
        Page<AlongWithOrderDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        page.setTotal(0);
        page.setRecords(Lists.newArrayList());

        a:
        if (redisService.hasKey(redisCacheIdKey)) {
            if (Objects.nonNull(dto)
                    && (StringUtils.isNotEmpty(dto.getProductCode()) || StringUtils.isNotEmpty(dto.getProductName()))) {
                List<AlongWithOrderDetailDto> dataList = findCacheList(cacheKey);
                if (!CollectionUtils.isEmpty(dataList)) {
                    // 简化过滤逻辑
                    dataList = dataList.stream()
                            .filter(x ->
                                    (ObjectUtils.isEmpty(dto.getProductCode()) || (ObjectUtils.isNotEmpty(x.getProductCode()) && x.getProductCode().contains(dto.getProductCode()))) &&
                                            (ObjectUtils.isEmpty(dto.getProductName()) || (ObjectUtils.isNotEmpty(x.getProductName()) && x.getProductName().contains(dto.getProductName())))
                            )
                            .collect(Collectors.toList());

                    // 设置总记录数
                    page.setTotal(dataList.size());

                    // 获取分页后的列表
                    int start = Math.min((int) page.offset(), dataList.size());
                    int end = Math.min(start + (int) page.offset() + (int) page.getSize(), dataList.size()); // 避免越界
                    List<AlongWithOrderDetailDto> pagedData = dataList.subList(start, end);

                    // 如果需要 `idList`
                    List<String> idList = pagedData.stream().map(AlongWithOrderDetailDto::getId).collect(Collectors.toList());

                    if (!CollectionUtils.isEmpty(idList)) {
                        List<AlongWithOrderDetailDto> resultDataList = redisTemplate.opsForHash().multiGet(redisCacheDataKey, idList);
                        List<AlongWithOrderDetailDto> dtoList = resultDataList;
                        List<AlongWithOrderDetailVo> voList = helper.dtoListToVoList(dtoList);
                        page.setRecords(voList);
                    }
                }

            } else {
                //redis里面有的话直接从redis里面取
                Long total = redisService.lSize(redisCacheIdKey);
                page.setTotal(total);
                List<Object> idList = redisService.lRange(redisCacheIdKey, page.offset(), page.offset() + page.getSize() - 1);
                if (!CollectionUtils.isEmpty(idList)) {
                    List<AlongWithOrderDetailDto> dataList = redisTemplate.opsForHash().multiGet(redisCacheDataKey, idList);
                    List<AlongWithOrderDetailDto> dtoList = (List<AlongWithOrderDetailDto>) dataList;
                    List<AlongWithOrderDetailVo> voList = helper.dtoListToVoList(dtoList);
                    page.setRecords(voList);
                }
            }

        } else if (!redisService.hasKey(redisCacheInitKey) && null != dto) {
            //标记为已初始化，不重复初始化
            redisService.set(redisCacheInitKey, BooleanEnum.TRUE, helper.getExpireTime());
            //放到try catch里面 是为了以防分页从数据库查询报错了 初始化状态被确认 修正报错过后查询为空的情况
            try {
                //redis里面没有
                List<AlongWithOrderDetailDto> dtoList = helper.findDtoListFromRepository(dto, cacheKey);

                if (CollectionUtils.isEmpty(dtoList)) {
                    redisService.del(redisCacheInitKey);
                    break a;
                }

                if (helper.initToCacheFromRepository()) {
                    helper.putCache(cacheKey, dtoList);
                }

                //放到缓存里面
                page.setTotal(dtoList.size());
                long start = page.offset();
                if (page.getTotal() > start) {
                    long end = page.offset() + page.getSize();
                    if (page.getTotal() < end) {
                        end = page.getTotal();
                    }
                    List<AlongWithOrderDetailDto> recordDtoList = dtoList.subList((int) page.offset(), (int) end);
                    List<AlongWithOrderDetailVo> voList = helper.dtoListToVoList(recordDtoList);
                    page.setRecords(voList);
                }
            } catch (Exception e) {
                redisService.del(redisCacheInitKey);
            }
        }
        //更新下VO里面的字段值
        helper.fillVoListProperties(page.getRecords());
        return page;
    }
}
