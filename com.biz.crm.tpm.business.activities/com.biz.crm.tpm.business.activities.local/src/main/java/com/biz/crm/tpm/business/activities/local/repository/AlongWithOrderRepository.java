package com.biz.crm.tpm.business.activities.local.repository;


import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.local.entity.AlongWithOrder;
import com.biz.crm.tpm.business.activities.local.mapper.AlongWithOrderMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;


/**
 * 随单比例管控(AlongWithOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-03 11:55:24
 */
@Component
public class AlongWithOrderRepository extends ServiceImpl<AlongWithOrderMapper, AlongWithOrder> {

    @Autowired
    private AlongWithOrderMapper alongWithOrderMapper;

    /**
     * 根据id集合获取详情集合
     *
     * @param ids ID集合
     * @return List<AuditActivities>
     */
    public List<AlongWithOrder> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(AlongWithOrder::getId, ids)
                .eq(AlongWithOrder::getTenantCode, tenantCode)
                .list();
    }

    /**
     * 按编码查询
     *
     * @param code 编码
     */
    public AlongWithOrder findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return this.getOne(Wrappers.lambdaQuery(AlongWithOrder.class)
                .eq(AlongWithOrder::getAlongWithOrderCode, code)
                .eq(AlongWithOrder::getTenantCode, TenantUtils.getTenantCode())
                .eq(AlongWithOrder::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        );
    }

    /**
     * 批量根据id禁用
     *
     * @param enable
     * @param ids
     */
    public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
        UpdateWrapper<AlongWithOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("enable_status", enable.getCode());
        updateWrapper.eq("tenant_code", TenantUtils.getTenantCode());
        updateWrapper.in("id", ids);
        this.update(updateWrapper);
    }

    /**
     * 按编码集合删除
     *
     * @param codes
     */
    public void deleteByCodes(List<String> codes) {
        this.lambdaUpdate().in(AlongWithOrder::getAlongWithOrderCode, codes).remove();
    }

    public BigDecimal getRatioByCondition(List<String> departmentCodes, String customerCode, List<String> productCodes,
                                          String years) {
        return this.baseMapper.getRatioByCondition(departmentCodes, customerCode, productCodes, years, TenantUtils.getTenantCode());
    }
}

