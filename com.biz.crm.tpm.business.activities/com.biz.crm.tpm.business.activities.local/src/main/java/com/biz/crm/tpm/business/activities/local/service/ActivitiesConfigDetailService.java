package com.biz.crm.tpm.business.activities.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDetailDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigDetailVo;

import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 活动表单配置(ActivitiesConfigDetail)表相关
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:54
 */
public interface ActivitiesConfigDetailService {

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<ActivitiesConfigDetailVo> findByConditions(Pageable pageable, ActivitiesConfigDetailDto dto);

  /**
   * 通过activitiesConfigId数据
   *
   * @param activitiesConfigId
   * @return 多条数据
   */
  List<ActivitiesConfigDetailVo> findByActivitiesConfigId(String activitiesConfigId);

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  List<ActivitiesConfigDetailVo> findByIds(Collection<String> ids);

  /**
   * 批量新增
   *
   * @param activitiesConfigDetailDtos
   * @return
   */
  List<ActivitiesConfigDetailVo> createBatch( String activitiesCode,Collection<ActivitiesConfigDetailDto> activitiesConfigDetailDtos);

  /**
   * 批量新增
   *
   * @param activitiesConfigDetailDtos
   * @return
   */
  List<ActivitiesConfigDetailVo> updateBatch( String activitiesCode,Collection<ActivitiesConfigDetailDto> activitiesConfigDetailDtos);

  /**
   * 删除数据
   *
   * @param activitiesCode
   * @return 删除结果
   */
  void deleteByActivitiesConfigId(String activitiesCode);
}
