package com.biz.crm.tpm.business.activities.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * 活动表单配置(ActivitiesConfig)表相关
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:53
 */
public interface ActivitiesConfigService {


  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(Collection<String> ids);

  /**
   * 批量根据id启用
   *
   * @param ids
   */
  void enable(List<String> ids);

  /**
   * 批量根据id禁用
   *
   * @param ids
   */
  void disable(List<String> ids);



}
