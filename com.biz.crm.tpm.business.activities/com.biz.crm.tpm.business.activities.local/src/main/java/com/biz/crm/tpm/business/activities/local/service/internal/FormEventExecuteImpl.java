package com.biz.crm.tpm.business.activities.local.service.internal;

import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesConfigVoService;
import com.biz.crm.tpm.business.activities.sdk.service.FormEventExecute;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import com.biz.crm.tpm.business.budget.sdk.context.FormEventContextHolder;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormEventStrategy;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述：</br>默认表单执行实现
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@Service
public class FormEventExecuteImpl implements FormEventExecute {
  @Autowired(required = false)
  private List<FormEventStrategy> formEventStrategies;
  @Autowired(required = false)
  private ActivitiesConfigVoService activitiesConfigVoService;

  @Override
  public <T extends DynamicForm> void execute(String businessCode, T data, Map<String, ?> params) {
    /*
     * 表单执行
     * 1、根据businessCode获取对应的表单执行策略
     * 2、创建执行上下文后依赖顺序调用策略
     */
    ActivitiesConfigVo activitiesConfigVo = this.activitiesConfigVoService.findByActivitiesConfigCode(businessCode);
    Validate.notNull(activitiesConfigVo, "未找到对应的动态表单策略绑定信息");
    Set<String> strategyCodes = activitiesConfigVo.getActivitiesConfigDetails().stream().map(ActivitiesConfigDetailVo::getActivitiesFormTypeStrategyCode).collect(Collectors.toSet());
    if (CollectionUtils.isEmpty(strategyCodes)) {
      return;
    }
    List<FormEventStrategy> executeStrategies = formEventStrategies.stream().filter(item -> strategyCodes.contains(item.getCode())).sorted(Comparator.comparing(FormEventStrategy::getOrder)).collect(Collectors.toList());
    for (FormEventStrategy formEventStrategy : executeStrategies) {
      Object result = formEventStrategy.handle(businessCode, data, params);
      FormEventContextHolder.put(formEventStrategy.getCode(), result);
    }
    FormEventContextHolder.clear();
  }
}
