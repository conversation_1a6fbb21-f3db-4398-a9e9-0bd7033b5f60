package com.biz.crm.tpm.business.activities.local.strategy.form;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.local.entity.FormStrategyProperties;
import com.biz.crm.tpm.business.activities.local.service.FormStrategyPropertiesService;
import com.biz.crm.tpm.business.budget.sdk.dto.FormStrategyPropertiesDto;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormEventStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 描述：</br>表单事件抽象类提供默认的一些方法
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@Slf4j
public abstract class AbstractFormEventStrategy implements FormEventStrategy {

  @Autowired(required = false)
  private FormStrategyPropertiesService formStrategyPropertiesService;

  @Override
  public <T extends FormProperties> void saveProperties(String businessCode, JSONObject data) {
    if (data == null||!data.containsKey(this.getCode())) {
      log.warn("保存参数为空！");
      return;
    }
    String json = JSON.toJSONString(data.getJSONObject(this.getCode()));
    if (StringUtils.isBlank(json)) {
      return;
    }
    T properties = (T) JSON.parseObject(json, this.getFormPropertiesInfo());
    this.validateProperties(properties);
    FormStrategyPropertiesDto formStrategyPropertiesDto = new FormStrategyPropertiesDto();
    formStrategyPropertiesDto.setCode(this.getCode());
    formStrategyPropertiesDto.setBusinessCode(businessCode);
    formStrategyPropertiesDto.setData(json);
    this.formStrategyPropertiesService.create(formStrategyPropertiesDto);
  }

  @Override
  public <T extends FormProperties> T findProperties(String businessCode) {
    FormStrategyProperties formStrategyProperties = this.formStrategyPropertiesService.findByCodeAndBusinessCode(this.getCode(), businessCode);
    if (formStrategyProperties == null) {
      return null;
    }
    String json = formStrategyProperties.getData();
    return (T) JSON.parseObject(json, this.getFormPropertiesInfo());
  }
}
