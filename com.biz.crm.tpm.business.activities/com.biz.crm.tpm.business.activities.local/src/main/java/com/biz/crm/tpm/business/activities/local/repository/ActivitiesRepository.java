package com.biz.crm.tpm.business.activities.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.local.entity.Activities;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 活动信息表;(tpm_activities)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-14
 */
@Component
public class ActivitiesRepository extends ServiceImpl<ActivitiesMapper, Activities> {
  @Autowired
  private ActivitiesMapper activitiesMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<ActivitiesVo> findByConditions(Pageable pageable, ActivitiesDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<Activities>
   */
  public List<Activities> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(Activities::getId, ids)
            .eq(Activities::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据编号与租户编号获取对象
   *
   * @param activitiesCode
   * @return
   */
  public Activities findByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(Activities::getActivitiesCode, activitiesCode)
            .eq(Activities::getTenantCode, tenantCode).one();
  }

  /**
   * 根据编号集合获取详情集合
   *
   * @param activitiesCodes 编号集合
   * @return List<Activities>
   */
  public List<Activities> findByActivitiesCodes(Set<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(Activities::getActivitiesCode, activitiesCodes)
            .eq(Activities::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 通过时间的年月查询开始时间匹配的活动编号
   *
   * @param time
   * @return
   */
  public List<String> findCodeByTime(String time) {
    if (StringUtils.isBlank(time)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    LocalDate localDate = LocalDate.parse(StringUtils.join(time,"-01"), df);
    LocalTime localTime = LocalTime.of(0, 0);
    LocalDateTime dateTime = LocalDateTime.of(localDate, localTime);
    LocalDateTime beginTime = dateTime.with(TemporalAdjusters.firstDayOfMonth());
    LocalDateTime endTime = dateTime.with(TemporalAdjusters.lastDayOfMonth());
    return this.activitiesMapper.findCodeByTime(tenantCode, beginTime, endTime);
  }

  public int countByCostTypeCategoryCode(String costTypeCategoryCode){
    if (StringUtils.isBlank(costTypeCategoryCode)) {
      return 0;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.activitiesMapper.countByCostTypeCategoryCode(tenantCode, costTypeCategoryCode);
  }

  public Activities findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(Activities::getTenantCode,tenantCode)
        .in(Activities::getId,id)
        .one();
  }
}