<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.local.mapper.AlongWithOrderMapper">


    <select id="getRatioByCondition" resultType="java.math.BigDecimal">
        SELECT
        b.ratio
        FROM
        tpm_along_with_order a
        LEFT JOIN tpm_along_with_order_detail b ON a.along_with_order_code = b.along_with_order_code
        WHERE
        a.tenant_code = #{tenantCode}
        AND a.del_flag = '${@<EMAIL>()}'
        AND a.enable_status = '${@<EMAIL>()}'
        AND b.department_code in
        <foreach collection="departmentCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
        AND (b.customer_code = #{customerCode} or b.customer_code is null  or b.customer_code='')
        AND b.product_code in
        <foreach collection="productCodes" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
        <![CDATA[AND a.start_year_month <= #{years}]]>
        <![CDATA[AND a.end_year_month >= #{years}]]>
        order by level_num desc,ratio desc limit 1
    </select>
</mapper>

