package com.biz.crm.tpm.business.activities.local.notifier;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.local.service.ActivitiesDetailSerialService;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesDetailSerialEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailSerialVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.BusinessStrategySettingExecutor;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType.CONTROL_COSTS;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType.ACTIVITY;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType.AUDIT;

/**
 * 描述：</br>活动明细订单信息新增事件监听，针对活动明细订单总金额进行维护
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@Component
@Slf4j
public class ActivitiesForActivitiesDetailSerialEventListener implements ActivitiesDetailSerialEventListener {
  @Autowired
  private ActivitiesDetailService activitiesDetailService;
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired
  private ActivitiesDetailSerialService activitiesDetailSerialService;
  @Autowired(required = false)
  private List<BusinessStrategySettingExecutor> businessStrategySettingExecutors;

  @Override
  public void onCreated(ActivitiesDetailSerialVo activitiesDetailSerialVo) {
    if (activitiesDetailSerialVo == null || StringUtils.isBlank(activitiesDetailSerialVo.getActivitiesDetailCode())) {
      return;
    }
    String activitiesDetailCode = activitiesDetailSerialVo.getActivitiesDetailCode();
    ActivitiesDetailVo activitiesDetailVo = this.activitiesDetailService.findByActivitiesDetailCode(activitiesDetailCode);
    Validate.notNull(activitiesDetailVo, "活动订单活动明细编号【%s】，无活动明细数据，请检查！", activitiesDetailCode);
    CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(activitiesDetailVo.getCostTypeDetailCode());
   //    Validate.notEmpty(costTypeDetailVo.getSettingStrategies(),"活动细类编号【%s】没有策略配置信息，请检查",costTypeDetailVo.getDetailCode());
    BusinessStrategySettingExecutor executor = BusinessStrategySettingExecutor.getExecutor(businessStrategySettingExecutors,ACTIVITY.name());
    Validate.notNull(executor,"活动细类编号【%s】没有匹配到相应的策略执行器，请检查！", costTypeDetailVo.getDetailCode());
    boolean isControlEventCosts = executor.matchedOprtType(costTypeDetailVo.getSettingStrategies(),CONTROL_COSTS.name(),true);
    if (isControlEventCosts) {
      // 控制活动费用不超出活动限制
      BigDecimal applyAmount = activitiesDetailVo.getApplyAmount();
      BigDecimal usedAmount = this.activitiesDetailSerialService.findAmountByActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode());
      Validate.isTrue(applyAmount.compareTo(usedAmount) >= 0, "活动费用使用超出控制金额【%s】", applyAmount.toString());
    }
    this.activitiesDetailService.updateOrderAmountByActivitiesCode(activitiesDetailCode, activitiesDetailSerialVo.getSerialPrice(), true);
  }

  @Override
  public void onDeleted(ActivitiesDetailSerialVo activitiesDetailSerialVo) {

  }
}
