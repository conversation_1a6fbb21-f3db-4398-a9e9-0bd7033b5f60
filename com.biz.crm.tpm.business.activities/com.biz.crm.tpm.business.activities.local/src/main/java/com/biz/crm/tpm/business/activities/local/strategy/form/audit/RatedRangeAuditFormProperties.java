package com.biz.crm.tpm.business.activities.local.strategy.form.audit;

import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormPropertiesField;
import lombok.Data;

/**
 * 描述：</br>超额核销策略
 *
 * <AUTHOR>
 * @date 2022/11/8
 */
@Data
public class RatedRangeAuditFormProperties implements FormProperties {
  /**
   * 超额核销比例
   */
  @FormPropertiesField(name = "超额核销比例", required = true, controllKey = SimpleInputWidget.class)
  private Integer rateRange;
}
