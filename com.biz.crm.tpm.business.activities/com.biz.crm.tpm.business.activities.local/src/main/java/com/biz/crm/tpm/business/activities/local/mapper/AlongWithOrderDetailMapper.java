package com.biz.crm.tpm.business.activities.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.local.entity.AlongWithOrderDetail;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDetailDto;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 随单比例管控明细(AlongWithOrderDetail)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-06-03 11:55:24
 */
public interface AlongWithOrderDetailMapper extends BaseMapper<AlongWithOrderDetail> {

    List<AlongWithOrderDetailVo> findByDto(@Param("dto") AlongWithOrderDetailDto alongWithOrderDetailDto);
}

