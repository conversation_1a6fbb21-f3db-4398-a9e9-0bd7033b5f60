package com.biz.crm.tpm.business.activities.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfigDetail;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesConfigDetailMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDetailDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigDetailVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 活动表单配置(ActivitiesConfigDetail)表相关
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:50
 */
@Component
public class ActivitiesConfigDetailRepository extends ServiceImpl<ActivitiesConfigDetailMapper, ActivitiesConfigDetail> {

  @Autowired
  private ActivitiesConfigDetailMapper activitiesConfigDetailMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<ActivitiesConfigDetailVo> findByConditions(Pageable pageable, ActivitiesConfigDetailDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesConfigDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesConfigDetailMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<ActivitiesConfigDetail>
   */
  public List<ActivitiesConfigDetail> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesConfigDetail::getId, ids)
            .eq(ActivitiesConfigDetail::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据activitiesCode获取详情集合
   *
   * @param activitiesConfigId
   * @return List<ActivitiesConfigDetail>
   */
  public List<ActivitiesConfigDetail> findByActivitiesConfigId(String activitiesConfigId) {
    if (StringUtils.isEmpty(activitiesConfigId)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesConfigDetail::getActivitiesConfigId, activitiesConfigId)
            .eq(ActivitiesConfigDetail::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 删除
   *
   * @param activitiesConfigId
   */
  public void deleteByActivitiesConfigId( String activitiesConfigId) {
    if (StringUtils.isNoneBlank(activitiesConfigId)) {
      String tenantCode = TenantUtils.getTenantCode();
      this.lambdaUpdate()
              .eq(ActivitiesConfigDetail::getActivitiesConfigId, activitiesConfigId)
              .eq(ActivitiesConfigDetail::getTenantCode, tenantCode)
              .remove();
    }
  }

}
