package com.biz.crm.tpm.business.activities.local.strategy.form.occupy;

import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.tpm.business.activities.local.strategy.form.AbstractFormEventStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.util.Map;

/**
 * 描述：</br>推送sfa策略
 * <AUTHOR>
 */
@Component
@Slf4j
public class PushSfaFormEventStrategy extends AbstractFormEventStrategy {

  @Override
  public String getCode() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String getName() {
    return "推送sfa";
  }

  @Override
  public Class<? extends FormProperties> getFormPropertiesInfo() {
    return PushSfaProperties.class;
  }

  @Override
  @Transactional
  public <T extends DynamicForm> Object handle(String businessCode, T data, Map<String, ?> params) {
    return null;
  }

  @Override
  public <T extends DynamicForm> boolean match(String businessCode, T data, Map<String, ?> params) {
    PushSfaProperties pushSfaProperties = this.findProperties(businessCode);
    if (pushSfaProperties == null) {
      return false;
    }
    return pushSfaProperties.isPushSfa();
  }
}
