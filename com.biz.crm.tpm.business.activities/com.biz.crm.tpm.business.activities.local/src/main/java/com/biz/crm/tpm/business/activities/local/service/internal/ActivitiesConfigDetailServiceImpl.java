package com.biz.crm.tpm.business.activities.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfigDetail;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesConfigDetailRepository;
import com.biz.crm.tpm.business.activities.local.service.ActivitiesConfigDetailService;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDetailDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;

/**
 * 活动表单配置(ActivitiesConfigDetail)表相关
 * <AUTHOR> rentao
 * @date : 2022/10/28 17:29
 */
@Service
public class ActivitiesConfigDetailServiceImpl implements ActivitiesConfigDetailService {

  @Autowired
  private ActivitiesConfigDetailRepository activitiesConfigDetailRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public Page<ActivitiesConfigDetailVo> findByConditions(Pageable pageable, ActivitiesConfigDetailDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesConfigDetailDto();
    }
    return this.activitiesConfigDetailRepository.findByConditions(pageable, dto);
  }

  @Override
  public List<ActivitiesConfigDetailVo> findByActivitiesConfigId(String activitiesConfigId) {
    if (StringUtils.isBlank(activitiesConfigId)) {
      return null;
    }
    List<ActivitiesConfigDetail> activitiesConfigs = this.activitiesConfigDetailRepository.findByActivitiesConfigId(activitiesConfigId);
    Collection<ActivitiesConfigDetailVo> activitiesConfigDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigs, ActivitiesConfigDetail.class, ActivitiesConfigDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesConfigDetailVos);
  }

  @Override
  public List<ActivitiesConfigDetailVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<ActivitiesConfigDetail> activitiesConfigs = this.activitiesConfigDetailRepository.findByIds(ids);
    Collection<ActivitiesConfigDetailVo> activitiesConfigDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigs, ActivitiesConfigDetail.class, ActivitiesConfigDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesConfigDetailVos);
  }


  @Override
  @Transactional
  public List<ActivitiesConfigDetailVo> createBatch(String activitiesConfigId, Collection<ActivitiesConfigDetailDto> activitiesConfigDetailDtos) {
    this.createValidate(activitiesConfigId, activitiesConfigDetailDtos);
    Collection<ActivitiesConfigDetail> activitiesConfigDetails = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigDetailDtos, ActivitiesConfigDetailDto.class, ActivitiesConfigDetail.class, LinkedHashSet.class, ArrayList.class);
    activitiesConfigDetails.forEach(activitiesConfigDetail -> activitiesConfigDetail.setTenantCode(TenantUtils.getTenantCode()));
    this.activitiesConfigDetailRepository.saveBatch(activitiesConfigDetails);
    List<ActivitiesConfigDetailVo> activitiesConfigDetailVos = (List) this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigDetails, ActivitiesConfigDetail.class, ActivitiesConfigDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return activitiesConfigDetailVos;
  }

  @Override
  @Transactional
  public List<ActivitiesConfigDetailVo> updateBatch(String activitiesConfigId, Collection<ActivitiesConfigDetailDto> activitiesConfigDetailDtos) {
    this.updateValidate(activitiesConfigId, activitiesConfigDetailDtos);
    Collection<ActivitiesConfigDetail> activitiesConfigDetails = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigDetailDtos, ActivitiesConfigDetailDto.class, ActivitiesConfigDetail.class, LinkedHashSet.class, ArrayList.class);
    this.deleteByActivitiesConfigId(activitiesConfigId);
    activitiesConfigDetails.forEach(activitiesConfigDetail -> activitiesConfigDetail.setTenantCode(TenantUtils.getTenantCode()));
    this.activitiesConfigDetailRepository.saveBatch(activitiesConfigDetails);
    List<ActivitiesConfigDetailVo> activitiesConfigDetailVos = (List) this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigDetails, ActivitiesConfigDetail.class, ActivitiesConfigDetailVo.class, LinkedHashSet.class, ArrayList.class);
    return activitiesConfigDetailVos;
  }

  /**
   * 删除数据
   *
   * @param activitiesConfigId
   * @return 删除结果
   */
  @Transactional
  @Override
  public void deleteByActivitiesConfigId(String activitiesConfigId) {
    Validate.notBlank(activitiesConfigId, "删除数据时，活动表单编号不能为空！");
    List<ActivitiesConfigDetail> activitiesConfigDetails = this.activitiesConfigDetailRepository.findByActivitiesConfigId(activitiesConfigId);
    if (CollectionUtils.isEmpty(activitiesConfigDetails)) {
      return;
    }
    this.activitiesConfigDetailRepository.deleteByActivitiesConfigId(activitiesConfigId);
  }

  /**
   * 创建验证
   *
   * @param activitiesConfigDetailDtos
   */
  private void createValidate(String activitiesConfigId, Collection<ActivitiesConfigDetailDto> activitiesConfigDetailDtos) {
    Validate.notBlank(activitiesConfigId, "新增数据时，活动表单编码不能为空！");
    Validate.isTrue(!CollectionUtils.isEmpty(activitiesConfigDetailDtos), "新增数据时，表单注册器集合不能为空!");
    String tenantCode = TenantUtils.getTenantCode();
    for (ActivitiesConfigDetailDto activitiesConfigDetailDto : activitiesConfigDetailDtos) {
      Validate.notNull(activitiesConfigDetailDto, "新增时，对象信息不能为空！");
      activitiesConfigDetailDto.setActivitiesConfigId(activitiesConfigId);
      activitiesConfigDetailDto.setId(null);
      activitiesConfigDetailDto.setTenantCode(tenantCode);
      Validate.notBlank(activitiesConfigDetailDto.getActivitiesFormTypeStrategyCode(), "新增数据时，表单类型策略编号不能为空！");
      Validate.notBlank(activitiesConfigDetailDto.getActivitiesFormTypeStrategyName(), "新增数据时，表单类型策略名称不能为空！");
      Validate.notNull(activitiesConfigDetailDto.getNecessary(), "新增数据时，是否必填不能为空！");
    }
  }

  /**
   * 创建验证
   *
   * @param activitiesConfigDetailDtos
   */
  private void updateValidate(String activitiesConfigId, Collection<ActivitiesConfigDetailDto> activitiesConfigDetailDtos) {
    Validate.notBlank(activitiesConfigId, "编辑数据时，活动表单编码不能为空！");
    Validate.isTrue(!CollectionUtils.isEmpty(activitiesConfigDetailDtos), "编辑数据时，表单注册器集合不能为空!");
    for (ActivitiesConfigDetailDto activitiesConfigDetailDto : activitiesConfigDetailDtos) {
      Validate.notNull(activitiesConfigDetailDto, "编辑时，对象信息不能为空！");
      activitiesConfigDetailDto.setActivitiesConfigId(activitiesConfigId);
      activitiesConfigDetailDto.setId(null);
      activitiesConfigDetailDto.setTenantCode(TenantUtils.getTenantCode());
      Validate.notBlank(activitiesConfigDetailDto.getActivitiesFormTypeStrategyCode(), "编辑数据时，表单类型策略编号不能为空！");
      Validate.notBlank(activitiesConfigDetailDto.getActivitiesFormTypeStrategyName(), "编辑数据时，表单类型策略名称不能为空！");
    }
  }
}
