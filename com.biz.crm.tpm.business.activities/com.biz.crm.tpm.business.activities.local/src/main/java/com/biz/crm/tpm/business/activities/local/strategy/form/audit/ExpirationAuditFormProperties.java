package com.biz.crm.tpm.business.activities.local.strategy.form.audit;

import com.biz.crm.tpm.business.activities.local.strategy.form.widget.SimpleIntegerInputWidget;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormPropertiesField;
import lombok.Data;

/**
 * 描述：</br>核销有效期属性配置项目
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Data
public class ExpirationAuditFormProperties implements FormProperties {
  /**
   * 核销有效期
   */
  @FormPropertiesField(name = "核销有效期", required = true, controllKey = SimpleIntegerInputWidget.class)
  private Integer expiration;
}
