package com.biz.crm.tpm.business.activities.local.repository;



import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.local.entity.AlongWithOrderDetail;
import com.biz.crm.tpm.business.activities.local.mapper.AlongWithOrderDetailMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDetailDto;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 随单比例管控明细(AlongWithOrderDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-03 11:55:24
 */
@Component
public class AlongWithOrderDetailRepository extends ServiceImpl<AlongWithOrderDetailMapper, AlongWithOrderDetail> {

  @Autowired
  private AlongWithOrderDetailMapper alongWithOrderDetailMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<AlongWithOrderDetailVo> findByCode(String code) {
    List<AlongWithOrderDetail> list = this.lambdaQuery().eq(AlongWithOrderDetail::getAlongWithOrderCode, code).list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByBlankList(list, AlongWithOrderDetail.class, AlongWithOrderDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param code
   * @return
   */
  public void deleteByCode(String code) {
    this.lambdaUpdate().eq(AlongWithOrderDetail::getAlongWithOrderCode, code).remove();
  }

  /**
   * 按编码删除
   *
   * @param codes
   * @return
   */
  public void deleteByCodes(List<String> codes) {
    this.lambdaUpdate().in(AlongWithOrderDetail::getAlongWithOrderCode, codes).remove();
  }

  public List<AlongWithOrderDetailVo> findByDto(AlongWithOrderDetailDto alongWithOrderDetailDto) {
    return alongWithOrderDetailMapper.findByDto(alongWithOrderDetailDto);
  }
}

