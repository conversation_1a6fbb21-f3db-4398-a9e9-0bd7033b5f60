package com.biz.crm.tpm.business.activities.local.controller;


import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDto;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDetailDto;
import com.biz.crm.tpm.business.activities.sdk.service.AlongWithOrderService;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderVo;
import com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 随单比例管控
 */
@RestController
@RequestMapping("/v1/activities/alongWithOrder")
@Slf4j
@Api(tags = "随单比例管控")
public class AlongWithOrderController extends BusinessPageCacheController<AlongWithOrderDetailVo, AlongWithOrderDetailDto> {
    
    @Autowired(required = false)
    private AlongWithOrderService alongWithOrderService;

    /**
     * 新增数据
     *
     * @param alongWithOrderDto DTO对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @PostMapping
    public Result<?> create(@ApiParam(name = "cacheKey", value = "缓存键")@RequestParam String cacheKey,
                            @ApiParam(name = "alongWithOrderDto", value = "随单比例管控DTO") @RequestBody AlongWithOrderDto alongWithOrderDto) {
        try {
            this.alongWithOrderService.create(alongWithOrderDto, cacheKey);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改数据
     *
     * @param alongWithOrderDto DTO对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @PatchMapping
    public Result<?> update(@ApiParam(name = "cacheKey", value = "缓存键")@RequestParam String cacheKey,
                            @ApiParam(name = "alongWithOrderDto", value = "随单比例管控DTO") @RequestBody AlongWithOrderDto alongWithOrderDto) {
        try {
            this.alongWithOrderService.update(alongWithOrderDto, cacheKey);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "ids", value = "主键集合") @RequestParam("ids") List<String> ids) {
        try {
            this.alongWithOrderService.delete(ids);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量根据id启用
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量根据id启用")
    @PatchMapping(value = "enable")
    public Result<?> enable(@RequestBody List<String> ids) {
        try {
            this.alongWithOrderService.enable(ids);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量根据id禁用
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量根据id禁用")
    @PatchMapping(value = "disable")
    public Result<?> disable(@RequestBody List<String> ids) {
        try {
            this.alongWithOrderService.disable(ids);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取随单比例管控
     *
     * @param code
     * @return
     */
    @ApiOperation(value = "获取随单比例管控")
    @GetMapping("findByCode")
    public Result<AlongWithOrderVo> findByCode(@ApiParam(value = "编码") @RequestParam() String code) {
        try {
            AlongWithOrderVo configVo = alongWithOrderService.findByCode(code);
            return Result.ok(configVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
