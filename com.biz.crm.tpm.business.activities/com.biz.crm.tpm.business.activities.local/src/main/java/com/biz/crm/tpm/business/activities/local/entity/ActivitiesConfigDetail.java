package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR> rentao
 * @date : 2022/10/28 11:32
 */
@ApiModel(value = "ActivitiesConfigDetail", description = "活动表单配置明细")
@TableName("tpm_activities_config_detail")
@Getter
@Setter
@Entity(name = "tpm_activities_config_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_config_detail", comment = "活动表单配置明细")
@Table(name = "tpm_activities_config_detail", indexes = {
        @Index(name = "tpm_activities_config_detail_index1", columnList = "activities_form_type_strategy_code,tenant_code"),
        @Index(name = "tpm_activities_config_detail_index2", columnList = "activities_config_id,tenant_code")})
public class ActivitiesConfigDetail extends TenantEntity {

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesConfigId", notes = "活动表单编号", value = "活动表单编号")
  @Column(name = "activities_config_id", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动表单编号 '")
  private String activitiesConfigId;

  /**
   * 表单类型策略编号
   */
  @ApiModelProperty(name = "activitiesFormTypeStrategyCode", notes = "表单类型策略编号", value = "表单类型策略编号")
  @Column(name = "activities_form_type_strategy_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '表单类型策略编号 '")
  private String activitiesFormTypeStrategyCode;

  /**
   * 表单类型策略名称
   */
  @ApiModelProperty(name = "activitiesFormTypeStrategyName", notes = "表单类型策略名称", value = "表单类型策略名称")
  @Column(name = "activities_form_type_strategy_name", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '表单类型策略名称 '")
  private String activitiesFormTypeStrategyName;

  /**
   * 是否必需
   */
  @ApiModelProperty("是否必需")
  @TableField(value = "necessary")
  @Column(name = "necessary", nullable = false, columnDefinition = "int(8) COMMENT '是否必需 '")
  private Boolean necessary;

}
