package com.biz.crm.tpm.business.activities.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesShareDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 活动信息表;(tpm_activities)控制层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-16
 */
@Api(tags = "活动信息表功能接口")
@RestController
@RequestMapping("/v1/activities/activities")
@Slf4j
public class ActivitiesController {
  /**
   * 服务对象
   */
  @Autowired
  private ActivitiesService activitiesService;

  /**
   * 分页查询所有数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<ActivitiesVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                     @ApiParam(name = "activities", value = "核销采集信息") ActivitiesDto dto) {
    try {
      Page<ActivitiesVo> page = this.activitiesService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<ActivitiesVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id", required = true) String id) {
    try {
      ActivitiesVo activitiesVo = this.activitiesService.findById(id);
      return Result.ok(activitiesVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编号查询单条数据
   *
   * @param activitiesCode 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByCode")
  public Result<ActivitiesVo> findByActivitiesCode(@RequestParam("activitiesCode") @ApiParam(name = "activitiesCode", value = "编号", required = true) String activitiesCode) {
    try {
      ActivitiesVo activitiesVo = this.activitiesService.findByActivitiesCode(activitiesCode);
      return Result.ok(activitiesVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编号查询单条数据详情
   *
   * @param activitiesCode 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据详情")
  @GetMapping("findDetailsByActivitiesCode")
  public Result<ActivitiesVo> findDetailsByActivitiesCode(@RequestParam("activitiesCode") @ApiParam(name = "activitiesCode", value = "编号", required = true) String activitiesCode) {
    try {
      ActivitiesVo activitiesVo = this.activitiesService.findDetailsByActivitiesCode(activitiesCode);
      return Result.ok(activitiesVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据前端传入的动态表单信息，填充分摊信息")
  @PostMapping(value = "shareForFeeDate")
  public Result<List<?>> shareForFeeDate(@RequestBody ActivitiesShareDto dto) {
    try {
      List<?> result = activitiesService.shareForFeeDate(dto);
      return Result.ok(result);
    } catch(Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "通过主活动编码集合，查询数据")
  @GetMapping("findDetailsByActivitiesCodes")
  public Result<List<ActivitiesVo>> findDetailsByActivitiesCodes(@RequestParam("activitiesCodes") @ApiParam(name = "activitiesCodes", value = "主活动编码") Set<String> activitiesCodes) {
    try {
      List<ActivitiesVo> result = this.activitiesService.findDetailsByActivitiesCodes(activitiesCodes);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过时间获取活动编号
   * @param time
   * @return
   */
  @ApiOperation(value = "通过时间获取活动编号")
  @GetMapping("findCodeByTime")
  public Result<List<String>> findCodeByTime(@RequestParam("time") @ApiParam(name = "time", value = "时间（格式为 1999-01）") String time) {
    try {
      List<String> result = this.activitiesService.findCodeByTime(time);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
