package com.biz.crm.tpm.business.activities.local.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailSerial;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesDetailSerialMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailSerialDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailSerialVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 活动明细订单表(SFA中的订单);(tpm_activities_detail_serial)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */
@Component
public class ActivitiesDetailSerialRepository extends ServiceImpl<ActivitiesDetailSerialMapper, ActivitiesDetailSerial> {
  @Autowired
  private ActivitiesDetailSerialMapper activitiesDetailSerialMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<ActivitiesDetailSerialVo> findByConditions(Pageable pageable, ActivitiesDetailSerialDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesDetailSerialVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesDetailSerialMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<ActivitiesDetailOrder>
   */
  public List<ActivitiesDetailSerial> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetailSerial::getId, ids)
            .eq(ActivitiesDetailSerial::getTenantCode, tenantCode)
            .list();
  }


  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @return 返单条数据
   */
  public List<ActivitiesDetailSerial> findByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesDetailSerial::getActivitiesCode, activitiesCode)
            .eq(ActivitiesDetailSerial::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<ActivitiesDetailSerial> findByActivitiesCodes(Collection<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetailSerial::getActivitiesCode, activitiesCodes)
            .eq(ActivitiesDetailSerial::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 删除数据
   */
  public boolean deleteByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(ActivitiesDetailSerial::getActivitiesCode, activitiesCode)
            .eq(ActivitiesDetailSerial::getTenantCode, tenantCode)
            .remove();
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @return 返单条数据
   */
  public List<ActivitiesDetailSerial> findByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesDetailSerial::getActivitiesDetailCode, activitiesDetailCode)
            .eq(ActivitiesDetailSerial::getTenantCode, tenantCode)
            .orderByDesc(ActivitiesDetailSerial::getCreateTime)
            .list();
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<ActivitiesDetailSerial> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes) {
    if (CollectionUtils.isEmpty(activitiesDetailCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetailSerial::getActivitiesDetailCode, activitiesDetailCodes)
            .eq(ActivitiesDetailSerial::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 删除数据
   */
  public boolean deleteByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(ActivitiesDetailSerial::getActivitiesDetailCode, activitiesDetailCode)
            .eq(ActivitiesDetailSerial::getTenantCode, tenantCode)
            .remove();
  }

  /**
   * 根据活动明细统计活动使用费用
   *
   * @param activitiesDetailCode
   * @return
   */
  public BigDecimal sumAmountByActivitiesCode(String activitiesDetailCode) {
    BigDecimal amount = BigDecimal.ZERO;
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return amount;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.activitiesDetailSerialMapper.sumTotalAmountByActivitiesDetailCode(tenantCode, activitiesDetailCode);
  }

  public ActivitiesDetailSerial findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesDetailSerial::getTenantCode,tenantCode)
        .in(ActivitiesDetailSerial::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ActivitiesDetailSerial::getTenantCode,tenantCode)
        .in(ActivitiesDetailSerial::getId,ids)
        .remove();
  }
}