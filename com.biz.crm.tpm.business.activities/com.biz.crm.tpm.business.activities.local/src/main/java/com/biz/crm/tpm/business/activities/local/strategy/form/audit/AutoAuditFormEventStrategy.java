package com.biz.crm.tpm.business.activities.local.strategy.form.audit;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.tpm.business.activities.local.strategy.form.AbstractFormEventStrategy;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.BusinessStrategySettingExecutor;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditActivitiesDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditCustomerDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDetailDto;
import com.biz.crm.tpm.business.pay.sdk.dto.AuditDto;
import com.biz.crm.tpm.business.pay.sdk.service.AuditService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType.AUTO_AUDIT;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType.AUDIT;

/**
 * 描述：</br>自动核销策略
 *
 * <AUTHOR>
 * @date 2022/11/4
 */
@Component
@Slf4j
public class AutoAuditFormEventStrategy extends AbstractFormEventStrategy {

  @Autowired(required = false)
  private AuditService auditService;
  @Autowired(required = false)
  private ActivitiesService activitiesService;
  @Autowired(required = false)
  private CostTypeCategoryVoService costTypeCategoryVoService;
  @Autowired(required = false)
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired(required = false)
  private List<BusinessStrategySettingExecutor> businessStrategySettingExecutors;

  @Override
  public String getCode() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String getName() {
    return "自动核销";
  }

  @Override
  public Class<? extends FormProperties> getFormPropertiesInfo() {
    return AutoAuditFormProperties.class;
  }

  @Override
  @Transactional
  public <T extends DynamicForm> Object handle(String businessCode, T data, Map<String, ?> params) {
    Validate.notBlank(businessCode, "活动编号为空，请检查");
    ActivitiesVo activitiesVo = this.activitiesService.findDetailsByActivitiesCode(businessCode);
    List<ActivitiesDetailVo> activitiesDetailVos = activitiesVo.getActivitiesDetails();
    List<AuditDetailDto> auditDetailDtos = Lists.newArrayList();
    BigDecimal totalApplyAmount = BigDecimal.ZERO;
    for (ActivitiesDetailVo activitiesDetailVo : activitiesDetailVos) {
      // 检查活动明细关联的活动细类是否为自动核销
      String costTypeDetailCode = activitiesDetailVo.getCostTypeDetailCode();
      CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(costTypeDetailCode);
      //todo 注释
      //Validate.notEmpty(costTypeDetailVo.getSettingStrategies(), "活动细类编号【%s】没有策略配置信息，请检查", costTypeDetailVo.getDetailCode());
      BusinessStrategySettingExecutor executor = BusinessStrategySettingExecutor.getExecutor(businessStrategySettingExecutors, AUDIT.name());
      Validate.notNull(executor, "活动细类编号【%s】没有匹配到相应的策略执行器，请检查！", costTypeDetailVo.getDetailCode());
      boolean autoAudit = executor.matchedOprtType(costTypeDetailVo.getSettingStrategies(), AUTO_AUDIT.name(), false);
      if (!autoAudit) {
        continue;
      }
      CostTypeCategoryVo costTypeCategoryVo = this.costTypeCategoryVoService.findByCode(activitiesDetailVo.getCostTypeCategoryCode());
      // 核销明细数据信息
      AuditDetailDto auditDetailDto = new AuditDetailDto();
      auditDetailDto.setActivitiesCode(activitiesVo.getActivitiesCode());
      auditDetailDto.setActivitiesName(activitiesVo.getActivitiesName());
      auditDetailDto.setActivitiesDetailCode(activitiesDetailVo.getActivitiesDetailCode());
      auditDetailDto.setCostTypeCategoryCode(activitiesDetailVo.getCostTypeCategoryCode());
      auditDetailDto.setCostTypeCategoryName(costTypeCategoryVo.getCategoryName());
      auditDetailDto.setCostTypeDetailCode(costTypeDetailVo.getDetailCode());
      auditDetailDto.setCostTypeDetailName(costTypeDetailVo.getDetailName());
      auditDetailDto.setBudgetSubjectsCode(activitiesDetailVo.getBudgetSubjectsCode());
      auditDetailDto.setBudgetSubjectsName(activitiesDetailVo.getBudgetSubjectsName());
      auditDetailDto.setCostBudgetCode(activitiesDetailVo.getCostBudgetCode());
      auditDetailDto.setOrgCode(activitiesDetailVo.getOrgCode());
      auditDetailDto.setOrgName(activitiesDetailVo.getOrgName());
      auditDetailDto.setPayBy(activitiesDetailVo.getPayType());
      auditDetailDto.setPayByName(activitiesDetailVo.getPayTypeName());
      auditDetailDto.setApplyAmount(activitiesDetailVo.getApplyAmount());
      auditDetailDto.setAuditAmount(activitiesDetailVo.getApplyAmount());
      auditDetailDto.setAccountingSubjectsCode(costTypeCategoryVo.getBudgetSubjectsCode());
      auditDetailDto.setAccountingSubjectsName(costTypeCategoryVo.getBudgetSubjectsName());
      auditDetailDto.setIsFullAudit(BooleanEnum.TRUE.getCapital());

      AuditCustomerDto auditCustomerDto = new AuditCustomerDto();
      auditCustomerDto.setCustomerCode(activitiesDetailVo.getCustomerCode());
      auditCustomerDto.setCustomerName(activitiesDetailVo.getCustomerName());
      auditCustomerDto.setAuditAmount(activitiesDetailVo.getApplyAmount());
      auditDetailDto.setAuditCustomers(Lists.newArrayList(auditCustomerDto));

      auditDetailDtos.add(auditDetailDto);
      totalApplyAmount = totalApplyAmount.add(activitiesDetailVo.getApplyAmount());
    }
    // 创建核销数据
    AuditDto auditDto = new AuditDto();
    // 核销活动信息
    AuditActivitiesDto auditActivitiesDto = new AuditActivitiesDto();
    auditActivitiesDto.setActivitiesCode(activitiesVo.getActivitiesCode());
    auditActivitiesDto.setActivitiesName(activitiesVo.getActivitiesName() == null ? activitiesVo.getName() : activitiesVo.getActivitiesName());
    auditActivitiesDto.setBeginTime(activitiesVo.getBeginTime() == null ? activitiesVo.getStartTime() : activitiesVo.getBeginTime());
    auditActivitiesDto.setEndTime(activitiesVo.getEndTime());
    auditActivitiesDto.setCreateAccount(activitiesVo.getCreateAccount());
    auditActivitiesDto.setCreateName(activitiesVo.getCreateName());
    auditActivitiesDto.setCreateTime(activitiesVo.getCreateTime());
    auditDto.setAuditActivities(Lists.newArrayList(auditActivitiesDto));
    auditDto.setAuditName(StringUtils.join(auditActivitiesDto.getActivitiesName(), "-自动核销"));
    auditDto.setTotalApplyAmount(totalApplyAmount);
    // 核销活动明细
    auditDto.setAuditDetails(auditDetailDtos);
    auditService.creatForAutoAudit(auditDto);
    return null;
  }

  @Override
  public <T extends DynamicForm> boolean match(String businessCode, T data, Map<String, ?> params) {
    AutoAuditFormProperties autoAuditFormProperties = this.findProperties(businessCode);
    if (autoAuditFormProperties == null) {
      return false;
    }
    return autoAuditFormProperties.isEnabled();
  }
}
