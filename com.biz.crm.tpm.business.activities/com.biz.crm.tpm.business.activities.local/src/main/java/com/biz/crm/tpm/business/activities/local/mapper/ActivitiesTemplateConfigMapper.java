package com.biz.crm.tpm.business.activities.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesTemplateConfig;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesTemplateConfigDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import org.apache.ibatis.annotations.Param;

/**
 * 活动模板配置实体类(ActivitiesTemplateConfig)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-05-31 15:57:01
 */
public interface ActivitiesTemplateConfigMapper extends BaseMapper<ActivitiesTemplateConfig> {
    
    /**
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<ActivitiesTemplateConfigVo> findByConditions(@Param("page") Page<ActivitiesTemplateConfigVo> page , @Param("dto") ActivitiesTemplateConfigDto dto);
}

