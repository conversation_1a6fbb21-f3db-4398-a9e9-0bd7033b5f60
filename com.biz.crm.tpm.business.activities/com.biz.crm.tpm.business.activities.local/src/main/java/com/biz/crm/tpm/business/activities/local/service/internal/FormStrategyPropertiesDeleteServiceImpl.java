package com.biz.crm.tpm.business.activities.local.service.internal;

import com.biz.crm.tpm.business.activities.local.entity.FormStrategyProperties;
import com.biz.crm.tpm.business.activities.local.repository.FormStrategyPropertiesRepository;
import com.biz.crm.tpm.business.budget.sdk.event.FormStrategyPropertiesDeleteService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 描述：</br>TPM-表单删除策略;(tpm_occupy_properties)表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FormStrategyPropertiesDeleteServiceImpl implements FormStrategyPropertiesDeleteService {
  @Autowired(required = false)
  private FormStrategyPropertiesRepository formStrategyPropertiesRepository;

  @Override
  public void deleteById(String id) {
    if (StringUtils.isBlank(id)) {
      return;
    }
    FormStrategyProperties current = this.formStrategyPropertiesRepository.findByIdAndTenantCode(id, TenantUtils.getTenantCode());
    if (current == null) {
      return;
    }
    this.formStrategyPropertiesRepository.removeById(id);
  }

  @Override
  public void deleteByBusinessId(String businessId) {
    this.formStrategyPropertiesRepository.deleteBuBusinessCode(businessId);
  }
}
