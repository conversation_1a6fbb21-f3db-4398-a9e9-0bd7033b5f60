package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 描述：</br>表单策略配置信息(默认通用实现)
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_form_strategy_properties")
@Table(name = "tpm_form_strategy_properties",indexes = {@Index(name = "tpm_form_strategy_properties_index1", columnList = "code, tenant_code, business_code", unique = true)})
@ApiModel(value = "FormStrategyProperties", description = "表单策略配置信息")
@org.hibernate.annotations.Table(appliesTo = "tpm_form_strategy_properties", comment = "表单策略配置信息")
public class FormStrategyProperties extends TenantEntity {

  @ApiModelProperty("策略编号")
  @TableField(value = "code", fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "code", length = 255, nullable = false, columnDefinition = "varchar(255) COMMENT '策略编号'")
  private String code;

  @ApiModelProperty("关联业务编号")
  @TableField(value = "business_code", fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "business_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '关联业务编号'")
  private String businessCode;

  @ApiModelProperty("json格式数据")
  @TableField(value = "data")
  @Column(name = "data", length = 2000, columnDefinition = "varchar(2000) COMMENT 'json格式数据'")
  private String data;
}
