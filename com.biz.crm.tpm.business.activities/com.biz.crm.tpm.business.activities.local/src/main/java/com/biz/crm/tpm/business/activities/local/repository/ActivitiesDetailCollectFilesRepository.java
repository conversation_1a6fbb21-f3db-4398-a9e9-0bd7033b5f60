package com.biz.crm.tpm.business.activities.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailCollectFiles;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesDetailCollectFilesMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailCollectFilesDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectFilesVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 活动明细采集附件信息表(SFA中的订单);(tpm_activities_detail_collect_files)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@Component
public class ActivitiesDetailCollectFilesRepository extends ServiceImpl<ActivitiesDetailCollectFilesMapper, ActivitiesDetailCollectFiles> {
  @Autowired
  private ActivitiesDetailCollectFilesMapper activitiesDetailCollectFilesMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<ActivitiesDetailCollectFilesVo> findByConditions(Pageable pageable, ActivitiesDetailCollectFilesDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesDetailCollectFilesVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesDetailCollectFilesMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<ActivitiesDetailCollectFiles>
   */
  public List<ActivitiesDetailCollectFiles> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetailCollectFiles::getId, ids)
            .eq(ActivitiesDetailCollectFiles::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param collectCode 采集编号
   * @return List<ActivitiesDetailCollectFiles>
   */
  public List<ActivitiesDetailCollectFiles> findByCollectCode(String collectCode) {
    if (StringUtils.isBlank(collectCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesDetailCollectFiles::getCollectCode, collectCode)
            .eq(ActivitiesDetailCollectFiles::getTenantCode, tenantCode)
            .list();
  }


  public ActivitiesDetailCollectFiles findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesDetailCollectFiles::getTenantCode,tenantCode)
        .in(ActivitiesDetailCollectFiles::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ActivitiesDetailCollectFiles::getTenantCode,tenantCode)
        .in(ActivitiesDetailCollectFiles::getId,ids)
        .remove();
  }
}
