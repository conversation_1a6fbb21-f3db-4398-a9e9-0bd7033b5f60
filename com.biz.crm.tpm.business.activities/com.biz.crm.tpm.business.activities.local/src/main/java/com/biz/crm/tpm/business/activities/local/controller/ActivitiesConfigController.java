package com.biz.crm.tpm.business.activities.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.local.service.ActivitiesConfigService;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesConfigVoService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 活动表单配置(ActivitiesConfig)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-10-28 18:18:43
 */
@RestController
@RequestMapping("/v1/activitiesConfig/activitiesConfig")
@Slf4j
@Api(tags = "活动表单配置")
public class ActivitiesConfigController {
  /**
   * 服务对象
   */
  @Autowired
  private ActivitiesConfigService activitiesConfigService;

  @Autowired
  private ActivitiesConfigVoService activitiesConfigVoService;

  /**
   * 分页查询所有数据
   *
   * @param pageable            分页对象
   * @param activitiesConfigDto 查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<ActivitiesConfigVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                           @ApiParam(name = "activitiesConfigDto", value = "活动表单配置") ActivitiesConfigDto activitiesConfigDto) {
    try {
      Page<ActivitiesConfigVo> page = this.activitiesConfigVoService.findByConditions(pageable, activitiesConfigDto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<ActivitiesConfigVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id") String id) {
    try {
      ActivitiesConfigVo activitiesConfigVo = this.activitiesConfigVoService.findById(id);
      return Result.ok(activitiesConfigVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编码查询单条数据
   *
   * @param activitiesConfigCode 编码
   * @return 单条数据
   */
  @ApiOperation(value = "通过编码查询单条数据")
  @GetMapping("findByActivitiesConfigCode")
  public Result<ActivitiesConfigVo> findByActivitiesConfigCode(@RequestParam("activitiesConfigCode") @ApiParam(name = "activitiesFormTypeCode", value = "活动表单类型编号", required = true) String activitiesConfigCode) {
    try {
      ActivitiesConfigVo activitiesConfigVo = this.activitiesConfigVoService.findByActivitiesConfigCode(activitiesConfigCode);
      return Result.ok(activitiesConfigVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param activitiesConfigDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<ActivitiesConfigVo> create(@ApiParam(name = "activitiesConfigDto", value = "活动表单配置") @RequestBody ActivitiesConfigDto activitiesConfigDto) {
    try {
      ActivitiesConfigVo result = this.activitiesConfigVoService.create(activitiesConfigDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param activitiesConfigDto 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "编辑数据")
  @PatchMapping
  public Result<ActivitiesConfigVo> update(@ApiParam(name = "activitiesConfigDto", value = "活动表单配置") @RequestBody ActivitiesConfigDto activitiesConfigDto) {
    try {
      ActivitiesConfigVo result = this.activitiesConfigVoService.update(activitiesConfigDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    try {
      this.activitiesConfigService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量根据id启用
   */
  @ApiOperation(value = "批量根据id启用")
  @PatchMapping(value = "enable")
  public Result<?> enable(@RequestBody List<String> ids) {
    try {
      this.activitiesConfigService.enable(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量根据id禁用
   */
  @ApiOperation(value = "批量根据id禁用")
  @PatchMapping(value = "disable")
  public Result<?> disable(@RequestBody List<String> ids) {
    try {
      this.activitiesConfigService.disable(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   * 活动表单类型编号查询所有数据
   *
   * @param activitiesFormTypeCode 活动表单类型编号
   * @return 所有数据
   */
  @ApiOperation(value = "活动表单类型编号查询所有数据")
  @GetMapping("findByActivitiesFormTypeCode")
  public Result<List<ActivitiesConfigVo>> findByActivitiesFormTypeCode(@RequestParam("activitiesFormTypeCode") @ApiParam(name = "activitiesFormTypeCode", value = "活动表单类型编号", required = true) String activitiesFormTypeCode) {
    try {
      List<ActivitiesConfigVo> list = this.activitiesConfigVoService.findByActivitiesFormTypeCode(activitiesFormTypeCode);
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
