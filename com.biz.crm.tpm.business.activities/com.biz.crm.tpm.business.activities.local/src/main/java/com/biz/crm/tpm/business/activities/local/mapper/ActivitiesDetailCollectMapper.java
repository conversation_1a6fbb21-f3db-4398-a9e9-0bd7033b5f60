package com.biz.crm.tpm.business.activities.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailCollect;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailCollectDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 活动明细采集信息表(SFA中的订单);(tpm_activities_detail_collect)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@Mapper
public interface ActivitiesDetailCollectMapper extends BaseMapper<ActivitiesDetailCollect> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto 动态查询条件
   * @return 分页对象列表
   */
  Page<ActivitiesDetailCollectVo> findByConditions(@Param("page") Page<ActivitiesDetailCollectVo> page , @Param("dto") ActivitiesDetailCollectDto dto);
}
