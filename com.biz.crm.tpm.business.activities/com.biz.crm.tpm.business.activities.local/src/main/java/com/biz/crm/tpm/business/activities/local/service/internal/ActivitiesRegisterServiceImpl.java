package com.biz.crm.tpm.business.activities.local.service.internal;

import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesRegisterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

/**
 * 描述：</br>提供外部进行活动信息注册时
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
@Service("activitiesRegisterService")
public class ActivitiesRegisterServiceImpl implements ActivitiesRegisterService {
  @Autowired
  private ActivitiesService activitiesService;

  @Override
  @Transactional
  public void register(ActivitiesDto dto) {
    this.activitiesService.create(dto);
  }
}
