package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：活动明细订单表(SFA中的订单);
 * <AUTHOR> Keller
 * @date : 2022-7-1
 */
@ApiModel(value = "ActivitiesDetailSerial",description = "活动明细流水表(SFA中的订单与协议信息)")
@TableName("tpm_activities_detail_serial")
@Getter
@Setter
@Entity(name = "tpm_activities_detail_serial")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_detail_serial", comment = "活动明细流水表(SFA中的订单与协议信息)")
@Table(name = "tpm_activities_detail_serial")
public class ActivitiesDetailSerial extends TenantEntity {


  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value= "活动编号")
  @Column(name = "activities_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /** 活动名称 */
  @ApiModelProperty(name = "activitiesName",notes = "活动名称", value= "活动名称")
  @Column(name = "activities_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /** 活动明细编号 */
  @ApiModelProperty(name = "activitiesDetailCode",notes = "活动明细编号时间", value= "活动明细编号时间")
  @Column(name = "activities_detail_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动明细编号 '")
  private String activitiesDetailCode;

  /** 订单编号 */
  @ApiModelProperty(name = "orderNo",notes = "订单编号", value= "订单编号")
  @Column(name = "serial_no", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '订单编号 '")
  private String serialNo;

  /** 订单金额 */
  @ApiModelProperty(name = "orderPrice",notes = "订单金额", value= "订单金额")
  @Column(name = "serial_price", nullable = false, length = 20,  columnDefinition = "DECIMAL(20,4) COMMENT '订单金额 '")
  private BigDecimal serialPrice;

  /** 订单时间 */
  @ApiModelProperty(name = "orderTime",notes = "订单时间", value= "订单时间")
  @Column(name = "serial_time", nullable = false,  columnDefinition = "DATETIME COMMENT '订单时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date serialTime;

  /** 订单客户编号 */
  @ApiModelProperty(name = "customerCode",notes = "订单客户编号", value= "订单客户编号")
  @Column(name = "customer_code", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '订单客户编号 '")
  private String customerCode;

  /** 订单客户名称 */
  @ApiModelProperty(name = "customerName",notes = "订单客户名称", value= "订单客户名称")
  @Column(name = "customer_name", nullable = true, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '订单客户名称 '")
  private String customerName;

  /** 终端编号 */
  @ApiModelProperty(name = "terminalCode", notes = "终端编号", value = "终端编号")
  @Column(name = "terminal_code", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT '终端编号 '")
  private String terminalCode;

  /** 终端名称 */
  @ApiModelProperty(name = "terminalName", notes = "终端名称", value = "终端名称")
  @Column(name = "terminal_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT '终端名称 '")
  private String terminalName;

  /** 提交数据批次编号 */
  @ApiModelProperty(name = "btNo",notes = "提交数据批次编号", value= "提交数据批次编号")
  @Column(name = "bt_no", nullable = false, length = 255,  columnDefinition = "VARCHAR(255) COMMENT '提交数据批次编号 '")
  private String btNo;

  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "create_time" , length = 20, columnDefinition = "datetime COMMENT '创建时间 '")
  private Date createTime;

  /** 类型 */
  @ApiModelProperty(name = "type",notes = "类型：1、商品订单；2、协议订单", value= "类型：1、商品订单；2、协议订单")
  @Column(name = "type", nullable = false,  columnDefinition = "INT COMMENT '类型 '")
  private Integer type;

}
