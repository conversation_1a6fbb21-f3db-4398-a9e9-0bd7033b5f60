package com.biz.crm.tpm.business.activities.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesTemplateConfig;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesTemplateConfigCategory;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesTemplateConfigDetail;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesTemplateConfigCategoryRepository;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesTemplateConfigDetailRepository;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesTemplateConfigRepository;
import com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesTemplateConfigConstant;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesTemplateConfigCategoryDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesTemplateConfigDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesTemplateConfigDto;
import com.biz.crm.tpm.business.activities.sdk.dto.log.ActivitiesTemplateConfigLogEventDto;
import com.biz.crm.tpm.business.activities.sdk.event.log.ActivitiesTemplateConfigLogEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesTemplateConfigService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigCategoryVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动模板配置
 */
@Service
public class ActivitiesTemplateConfigServiceImpl implements ActivitiesTemplateConfigService {

    @Autowired(required = false)
    private ActivitiesTemplateConfigRepository activitiesTemplateConfigRepository;
    @Autowired(required = false)
    private ActivitiesTemplateConfigDetailRepository activitiesTemplateConfigDetailRepository;
    @Autowired(required = false)
    private ActivitiesTemplateConfigCategoryRepository activitiesTemplateConfigCategoryRepository;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired(required = false)
    private LoginUserService loginUserService;

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    @Override
    public Page<ActivitiesTemplateConfigVo> findByConditions(Pageable pageable, ActivitiesTemplateConfigDto dto) {
        ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new ActivitiesTemplateConfigDto();
        }
        return activitiesTemplateConfigRepository.findByConditions(pageable, dto);
    }

    /**
     * 通过编码查询单条数据
     *
     * @param configCode 编码
     * @return 单条数据
     */
    @Override
    public ActivitiesTemplateConfigVo findByCode(String configCode) {
        if (StringUtils.isBlank(configCode)) {
            return null;
        }
        ActivitiesTemplateConfig entity = activitiesTemplateConfigRepository.findByCode(configCode);
        if (null == entity) {
            return null;
        }
        ActivitiesTemplateConfigVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, ActivitiesTemplateConfigVo.class, HashSet.class, ArrayList.class);
        List<ActivitiesTemplateConfigDetail> list = activitiesTemplateConfigDetailRepository.findByConfigCode(vo.getConfigCode());
        List<ActivitiesTemplateConfigDetailVo> dtoList = (List<ActivitiesTemplateConfigDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(list, ActivitiesTemplateConfigDetail.class, ActivitiesTemplateConfigDetailVo.class, HashSet.class, ArrayList.class);
        List<ActivitiesTemplateConfigCategory> listCategory = activitiesTemplateConfigCategoryRepository.findByConfigCode(vo.getConfigCode());
        List<ActivitiesTemplateConfigCategoryVo> dtoCategoryList = (List<ActivitiesTemplateConfigCategoryVo>) nebulaToolkitService.copyCollectionByWhiteList(listCategory, ActivitiesTemplateConfigCategory.class, ActivitiesTemplateConfigCategoryVo.class, HashSet.class, ArrayList.class);
        vo.setDetails(dtoList);
        vo.setCategoryList(dtoCategoryList);
        return vo;
    }

    /**
     * 新增数据
     *
     * @param dto dto对象
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(ActivitiesTemplateConfigDto dto) {
        createValidate(dto);
        FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
        Validate.notNull(loginUserDetails, "未找到当前登陆人信息！");
        dto.setOrgCode(loginUserDetails.getOrgCode());
        dto.setOrgName(loginUserDetails.getOrgName());
        dto.setPositionCode(loginUserDetails.getPostCode());
        // redis生成料编码code
        List<String> codeList = this.generateCodeService.generateCodeNotDate(ActivitiesTemplateConfigConstant.PREFIX_CODE, 1);
        dto.setConfigCode(codeList.get(0));
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setTenantCode(TenantUtils.getTenantCode());

        store(dto);

        //新增业务日志
        ActivitiesTemplateConfigLogEventDto logEventDto = new ActivitiesTemplateConfigLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(dto);
        SerializableBiConsumer<ActivitiesTemplateConfigLogEventListener, ActivitiesTemplateConfigLogEventDto> onCreate =
                ActivitiesTemplateConfigLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, ActivitiesTemplateConfigLogEventListener.class, onCreate);
    }

    /**
     * 修改新据
     *
     * @param dto dto对象
     * @return 修改结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ActivitiesTemplateConfigDto dto) {
        updateValidate(dto);

        ActivitiesTemplateConfig entityOld = activitiesTemplateConfigRepository.findById(dto.getId());

        activitiesTemplateConfigDetailRepository.deleteByConfigCodeList(Arrays.asList(dto.getConfigCode()));
        activitiesTemplateConfigCategoryRepository.deleteByConfigCodeList(Arrays.asList(dto.getConfigCode()));

        store(dto);

        //编辑业务日志
        ActivitiesTemplateConfigLogEventDto logEventDto = new ActivitiesTemplateConfigLogEventDto();
        logEventDto.setOriginal(this.nebulaToolkitService.copyObjectByWhiteList(entityOld, ActivitiesTemplateConfigDto.class, LinkedHashSet.class, ArrayList.class));
        logEventDto.setNewest(dto);
        SerializableBiConsumer<ActivitiesTemplateConfigLogEventListener, ActivitiesTemplateConfigLogEventDto> onUpdate =
                ActivitiesTemplateConfigLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, ActivitiesTemplateConfigLogEventListener.class, onUpdate);
    }

    /**
     * 保存
     *
     * @param dto
     */
    public void store(ActivitiesTemplateConfigDto dto) {
        ActivitiesTemplateConfig activitiesTemplateConfig = nebulaToolkitService.copyObjectByWhiteList(dto, ActivitiesTemplateConfig.class, HashSet.class, ArrayList.class);
        //明细
        List<ActivitiesTemplateConfigDetailDto> details = dto.getDetails();
        List<ActivitiesTemplateConfigDetail> detailList = (List<ActivitiesTemplateConfigDetail>) nebulaToolkitService.copyCollectionByWhiteList(details, ActivitiesTemplateConfigDetailDto.class, ActivitiesTemplateConfigDetail.class, HashSet.class, ArrayList.class);
        detailList.forEach(e -> {
            e.setConfigCode(dto.getConfigCode());
            e.setTenantCode(TenantUtils.getTenantCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        //活动大类
        List<ActivitiesTemplateConfigCategoryDto> categoryListDto = dto.getCategoryList();
        List<ActivitiesTemplateConfigCategory> categoryList = (List<ActivitiesTemplateConfigCategory>) nebulaToolkitService.copyCollectionByWhiteList(categoryListDto, ActivitiesTemplateConfigCategoryDto.class, ActivitiesTemplateConfigCategory.class, HashSet.class, ArrayList.class);
        categoryList.forEach(e -> {
            e.setConfigCode(activitiesTemplateConfig.getConfigCode());
            e.setTenantCode(TenantUtils.getTenantCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });


        this.activitiesTemplateConfigRepository.saveOrUpdate(activitiesTemplateConfig);
        this.activitiesTemplateConfigDetailRepository.saveBatch(detailList);
        this.activitiesTemplateConfigCategoryRepository.saveBatch(categoryList);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
        List<ActivitiesTemplateConfig> configList = this.activitiesTemplateConfigRepository.findByIdList(idList);
        Validate.notEmpty(configList, "根据提供的主键集合信息，未能获取到相应数据");
        List<String> codeList = configList.stream().map(e -> e.getConfigCode()).collect(Collectors.toList());
        this.activitiesTemplateConfigDetailRepository.deleteByConfigCodeList(codeList);
        this.activitiesTemplateConfigCategoryRepository.deleteByConfigCodeList(codeList);
        this.activitiesTemplateConfigRepository.deleteConfigByCodes(codeList);

        //删除业务日志
        Collection<ActivitiesTemplateConfigDto> dtoList = nebulaToolkitService.copyCollectionByWhiteList(configList,
                ActivitiesTemplateConfig.class, ActivitiesTemplateConfigDto.class, HashSet.class, ArrayList.class);
        ActivitiesTemplateConfigLogEventDto logEventDto = new ActivitiesTemplateConfigLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(new ArrayList<>(dtoList));
        SerializableBiConsumer<ActivitiesTemplateConfigLogEventListener, ActivitiesTemplateConfigLogEventDto> onDelete =
                ActivitiesTemplateConfigLogEventListener::onDelete;
        this.nebulaNetEventClient.publish(logEventDto, ActivitiesTemplateConfigLogEventListener.class, onDelete);
    }

    @Override
    public List<ActivitiesTemplateConfigVo> findByCodes(Set<String> configCodes) {

        List<ActivitiesTemplateConfig> configList = activitiesTemplateConfigRepository.findByCodes(configCodes);
        if (CollectionUtils.isEmpty(configList)) {
            return new ArrayList<>(0);
        }
        List<ActivitiesTemplateConfigVo> voList = (List<ActivitiesTemplateConfigVo>) this.nebulaToolkitService.copyCollectionByWhiteList(configList,
                ActivitiesTemplateConfig.class, ActivitiesTemplateConfigVo.class, HashSet.class, ArrayList.class);

        List<ActivitiesTemplateConfigDetail> detailList =
                activitiesTemplateConfigDetailRepository.findByConfigCodes(voList.stream().map(ActivitiesTemplateConfigVo::getConfigCode).collect(Collectors.toList()));
        List<ActivitiesTemplateConfigDetailVo> detailVoList =
                (List<ActivitiesTemplateConfigDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(detailList, ActivitiesTemplateConfigDetail.class, ActivitiesTemplateConfigDetailVo.class, HashSet.class, ArrayList.class);
        Map<String, List<ActivitiesTemplateConfigDetailVo>> detailVoMap = detailVoList.stream().collect(Collectors.groupingBy(ActivitiesTemplateConfigDetailVo::getConfigCode));

        List<ActivitiesTemplateConfigCategory> listCategory =
                activitiesTemplateConfigCategoryRepository.findByConfigCodes(configCodes);
        List<ActivitiesTemplateConfigCategoryVo> voCategoryList =
                (List<ActivitiesTemplateConfigCategoryVo>) nebulaToolkitService.copyCollectionByWhiteList(listCategory, ActivitiesTemplateConfigCategory.class, ActivitiesTemplateConfigCategoryVo.class, HashSet.class, ArrayList.class);
        Map<String, List<ActivitiesTemplateConfigCategoryVo>> voCategoryMap =
                voCategoryList.stream().collect(Collectors.groupingBy(ActivitiesTemplateConfigCategoryVo::getConfigCode));

        voList.forEach(v -> {
            v.setDetails(detailVoMap.get(v.getConfigCode()));
            v.setCategoryList(voCategoryMap.get(v.getConfigCode()));
        });
        return voList;
    }

    /**
     * 创建验证
     *
     * @param activitiesTemplateConfig
     */
    private void createValidate(ActivitiesTemplateConfigDto activitiesTemplateConfig) {
        Validate.notNull(activitiesTemplateConfig, "新增时，对象信息不能为空！");
        commonValidate(activitiesTemplateConfig);
        activitiesTemplateConfig.setId(null);
    }

    /**
     * 修改验证
     *
     * @param activitiesTemplateConfig
     */
    private void updateValidate(ActivitiesTemplateConfigDto activitiesTemplateConfig) {
        Validate.notNull(activitiesTemplateConfig, "修改时，对象信息不能为空！");
        commonValidate(activitiesTemplateConfig);
        Validate.notBlank(activitiesTemplateConfig.getId(), "主键id不能为空！");
    }

    /**
     * 公有校验
     *
     * @param activitiesTemplateConfig
     */
    private void commonValidate(ActivitiesTemplateConfigDto activitiesTemplateConfig) {
        Validate.notBlank(activitiesTemplateConfig.getConfigName(), "配置名称不能为空！");
        Validate.notBlank(activitiesTemplateConfig.getType(), "配置类型不能为空！");

        List<ActivitiesTemplateConfigDetailDto> details = activitiesTemplateConfig.getDetails();
        Validate.isTrue(!CollectionUtils.isEmpty(details), "明细列表不能为空");
        Set<String> set = new HashSet<>(details.size());
        Set<String> titleSet = new HashSet<>(details.size());
        for (ActivitiesTemplateConfigDetailDto detail : activitiesTemplateConfig.getDetails()) {
            detail.setRequired(ObjectUtils.defaultIfNull(detail.getRequired(), Boolean.FALSE));
            Validate.notBlank(detail.getField(), "显示属性不能为空");
            Validate.notBlank(detail.getTitle(), "字段名称不能为空");
            Validate.isTrue(set.add(detail.getField()), "存在相同的字段:" + detail.getField());
            Validate.isTrue(set.add(detail.getTitle()), "存在相同的字段名称:" + detail.getTitle());
            if (Objects.isNull(detail.getExports())) {
                detail.setExports(Boolean.FALSE);
            }
        }
    }
}
