package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实体：活动明细采集信息表(SFA中的订单);
 *
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@ApiModel(value = "ActivitiesDetailCollect", description = "活动明细采集信息表(SFA中的订单)")
@TableName("tpm_activities_detail_collect")
@Getter
@Setter
@Entity(name = "tpm_activities_detail_collect")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_detail_collect", comment = "活动明细采集信息表(SFA中的订单)")
@Table(name = "tpm_activities_detail_collect")
public class ActivitiesDetailCollect extends TenantEntity {


  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesCode", notes = "活动编号", value = "活动编号")
  @Column(name = "activities_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "activitiesName", notes = "活动名称", value = "活动名称")
  @Column(name = "activities_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 活动明细编号
   */
  @ApiModelProperty(name = "activitiesDetailCode", notes = "活动明细编号时间", value = "活动明细编号时间")
  @Column(name = "activities_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动明细编号 '")
  private String activitiesDetailCode;

  /**
   * 提交数据批次编号
   */
  @ApiModelProperty(name = "btNo", notes = "提交数据批次编号", value = "提交数据批次编号")
  @Column(name = "bt_no", nullable = false, length = 255, columnDefinition = "VARCHAR(255) COMMENT '提交数据批次编号 '")
  private String btNo;

  /**
   * 备注
   */
  @ApiModelProperty(name = "备注", notes = "备注")
  @Column(name = "remark", nullable = true, length = 400, columnDefinition = "VARCHAR(400) COMMENT '备注 '")
  private String remark;

  /**
   * 创建时间
   */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY)
  @ApiModelProperty(name = "创建时间", notes = "创建时间")
  @Column(name = "create_time", nullable = false, columnDefinition = "DATETIME COMMENT '创建时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  /**
   * 采集编号
   */
  @ApiModelProperty(name = "collectCode", notes = "采集编号", value = "采集编号")
  @Column(name = "collect_code", nullable = false, length = 255, columnDefinition = "VARCHAR(255) COMMENT '采集编号 '")
  private String collectCode;

  /**
   * 创建人名称
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "create_name", length = 60, columnDefinition = "varchar(60) COMMENT '创建人名称'")
  private String createName;

  /**
   * 客户编号
   */
  @ApiModelProperty(name = "customerCode", notes = "订单客户编号", value = "订单客户编号")
  @Column(name = "customer_code", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '订单客户编号 '")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty(name = "customerName", notes = "订单客户名称", value = "订单客户名称")
  @Column(name = "customer_name", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '订单客户名称 '")
  private String customerName;

  /**
   * 终端编号
   */
  @ApiModelProperty(name = "terminalCode", notes = "终端编号", value = "终端编号")
  @Column(name = "terminal_code", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT '终端编号 '")
  private String terminalCode;

  /**
   * 终端名称
   */
  @ApiModelProperty(name = "terminalName", notes = "终端名称", value = "终端名称")
  @Column(name = "terminal_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT '终端名称 '")
  private String terminalName;

}
