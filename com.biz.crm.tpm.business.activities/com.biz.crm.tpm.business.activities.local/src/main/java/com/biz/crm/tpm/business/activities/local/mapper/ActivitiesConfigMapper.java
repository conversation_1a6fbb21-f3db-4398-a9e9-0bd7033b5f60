package com.biz.crm.tpm.business.activities.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesConfigDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:39
 */
@Mapper
public interface ActivitiesConfigMapper extends BaseMapper<ActivitiesConfig> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto 动态查询条件
   * @return 分页对象列表
   */
  Page<ActivitiesConfigVo> findByConditions(@Param("page") Page<ActivitiesConfigVo> page , @Param("dto") ActivitiesConfigDto dto);

}

