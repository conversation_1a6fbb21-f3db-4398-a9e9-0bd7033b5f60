package com.biz.crm.tpm.business.activities.local.strategy.form.audit;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.tpm.business.activities.local.strategy.form.AbstractFormEventStrategy;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.pay.sdk.service.AuditBillService;
import com.biz.crm.tpm.business.pay.sdk.service.AuditDetailService;
import com.biz.crm.tpm.business.pay.sdk.vo.AuditBillVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType.AUTO_AUDIT;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType.EXCEEDING_AUDIT_RATE;

/**
 * 描述：</br>超额核销策略
 *
 * <AUTHOR>
 * @date 2022/11/8
 */
@Component
@Slf4j
public class RatedRangeAuditFormEventStrategy extends AbstractFormEventStrategy {
  @Autowired(required = false)
  private ActivitiesService activitiesService;
  @Autowired(required = false)
  private AuditDetailService auditDetailService;
  @Autowired(required = false)
  private AuditBillService auditBillService;

  @Override
  public String getCode() {
    return RatedRangeAuditFormEventStrategy.class.getSimpleName();
  }

  @Override
  public String getName() {
    return "超额核销比例";
  }

  @Override
  public Class<? extends FormProperties> getFormPropertiesInfo() {
    return RatedRangeAuditFormProperties.class;
  }

  @Override
  public <T extends DynamicForm> Object handle(String businessCode, T data, Map<String, ?> params) {
    Validate.notBlank(businessCode, "活动编号为空，请检查");
    ActivitiesVo activitiesVo = this.activitiesService.findDetailsByActivitiesCode(businessCode);
    Validate.notNull(activitiesVo, "活动数据错误，请检查");
    List<ActivitiesDetailVo> activitiesDetailVos = activitiesVo.getActivitiesDetails();
    if(!CollectionUtils.isEmpty(activitiesDetailVos)){
      activitiesDetailVos.forEach(detail->{
        // 该项明细总共申请的金额
        BigDecimal applyAmount = detail.getApplyAmount();
        // 已经核销的金额
        AuditBillVo auditBillVo = this.auditBillService.findByActivitiesDetailCode(detail.getActivitiesDetailCode());
        BigDecimal auditedAmount = BigDecimal.ZERO;
        if (auditBillVo != null) {
          auditedAmount = auditBillVo.getAuditedAmount();
        }
      });
    }
    return null;
  }

  @Override
  public <T extends DynamicForm> boolean match(String businessCode, T data, Map<String, ?> params) {
    RatedRangeAuditFormProperties ratedRangeAuditFormProperties = this.findProperties(businessCode);
    if (ratedRangeAuditFormProperties == null || ratedRangeAuditFormProperties.getRateRange() == null) {
      return false;
    }
    return true;
  }
}
