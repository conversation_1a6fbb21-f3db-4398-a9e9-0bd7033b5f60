package com.biz.crm.tpm.business.activities.local.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.DynamicTemplateDto;
import com.biz.crm.tpm.business.activities.sdk.service.DynamicTemplateService;
import com.biz.crm.tpm.business.activities.sdk.vo.DynamicTemplateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 动态模板管理(DynamicTemplateController)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
@RestController
@RequestMapping("/v1/activities/dynamicTemplate")
@Slf4j
@Api(tags = "动态模板管理")
public class DynamicTemplateController {

  @Autowired
  private DynamicTemplateService dynamicTemplateService;


  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<DynamicTemplateVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                          @ApiParam(name = "dynamicTemplateDto", value = "动态模型查询参数信息") DynamicTemplateDto dto) {
    try {
      Page<DynamicTemplateVo> page = this.dynamicTemplateService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "按照动态表单在系统中唯一的业务编码进行查询(已测试)")
  @GetMapping(value = "findByDynamicFormCode")
  public Result<?> findByDynamicFormCode(@RequestParam("dynamicFormCode") @ApiParam(name = "dynamicFormCode", value = "动态表单编码") String dynamicFormCode) {
    try {
      JSONObject result = dynamicTemplateService.findByDynamicFormCode(dynamicFormCode);
      if(result == null) {
        return Result.ok();
      }
      // 注意WidgetKey转json的问题，其中没有GET/SET方法，而是要自行构建JSON结构
      return Result.ok(result);
    } catch(Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
