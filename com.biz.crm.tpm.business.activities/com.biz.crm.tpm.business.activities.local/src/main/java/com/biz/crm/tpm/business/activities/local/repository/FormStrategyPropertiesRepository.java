package com.biz.crm.tpm.business.activities.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.local.entity.FormStrategyProperties;
import com.biz.crm.tpm.business.activities.local.mapper.FormStrategyPropertiesMapper;
import org.springframework.stereotype.Component;

/**
 * 预算策略属性(OccupyFormProperties)表数据库访问层
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Component
public class FormStrategyPropertiesRepository extends ServiceImpl<FormStrategyPropertiesMapper, FormStrategyProperties> {

  public FormStrategyProperties findByCodeAndBusinessCode(String code, String businessCode, String tenantCode) {
    return this.lambdaQuery().eq(FormStrategyProperties::getCode, code).eq(FormStrategyProperties::getBusinessCode, businessCode).eq(FormStrategyProperties::getTenantCode, tenantCode).one();
  }

  public FormStrategyProperties findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
            .eq(FormStrategyProperties::getTenantCode, tenantCode)
            .in(FormStrategyProperties::getId, id)
            .one();
  }

  /**
   * 根据业务编码删除配置
   *
   * @param businessCode 业务编码
   */
  public void deleteBuBusinessCode(String businessCode) {
    this.lambdaUpdate().eq(FormStrategyProperties::getBusinessCode, businessCode)
            .remove();
  }
}
