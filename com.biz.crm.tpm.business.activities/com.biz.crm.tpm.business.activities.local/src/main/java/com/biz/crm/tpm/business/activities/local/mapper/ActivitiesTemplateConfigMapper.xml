<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.local.mapper.ActivitiesTemplateConfigMapper">

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo">
        select
        t.*
        from tpm_activities_template_config t
        <where>
            t.tenant_code=#{dto.tenantCode} and t.del_flag = '${@<EMAIL>()}'
            <if test="dto.configCode != null and dto.configCode != '' ">
                and t.config_code = #{dto.configCode}
            </if>
            <if test="dto.configName != null and dto.configName != '' ">
                <bind name="configName" value="'%' + dto.configName + '%'"/>
                and t.config_name like #{configName}
            </if>
            <if test="dto.type != null and dto.type != ''">
                and t.type = #{dto.type}
            </if>
        </where>
        order by t.create_time desc,t.id desc
    </select>
</mapper>

