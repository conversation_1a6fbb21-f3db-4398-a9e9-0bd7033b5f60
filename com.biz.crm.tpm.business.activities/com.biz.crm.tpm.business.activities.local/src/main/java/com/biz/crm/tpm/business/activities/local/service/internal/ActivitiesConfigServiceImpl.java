package com.biz.crm.tpm.business.activities.local.service.internal;

import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesConfigRepository;
import com.biz.crm.tpm.business.activities.local.service.ActivitiesConfigService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.transaction.Transactional;
import java.util.*;

/**
 * 活动表单配置(ActivitiesConfig)表相关
 * <AUTHOR> rentao
 * @date : 2022/10/28 16:57
 */
@Service
public class ActivitiesConfigServiceImpl implements ActivitiesConfigService {

  @Autowired
  private ActivitiesConfigRepository activitiesConfigRepository;

  @Override
  @Transactional
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<ActivitiesConfig> activitiesConfigs = this.activitiesConfigRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(activitiesConfigs)) {
      return;
    }
    this.activitiesConfigRepository.removeByIdsAndTenantCode(ids, TenantUtils.getTenantCode());
  }

  /**
   * 批量根据id启用
   *
   * @param ids
   */
  @Override
  @Transactional
  public void enable(List<String> ids) {
    Validate.notEmpty(ids, "启用时，id不能为空");
    List<ActivitiesConfig> activitiesConfigs = this.activitiesConfigRepository.findByIds(ids);
    if (org.springframework.util.CollectionUtils.isEmpty(activitiesConfigs)) {
      return;
    }
//    Collection<ActivitiesConfigVo> activitiesDetailCollectFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigs, ActivitiesConfig.class, ActivitiesConfigVo.class, LinkedHashSet.class, ArrayList.class);
    this.activitiesConfigRepository.updateEnableStatusByIds(EnableStatusEnum.ENABLE, ids);
  }

  /**
   * 批量根据id禁用
   *
   * @param ids
   */
  @Override
  @Transactional
  public void disable(List<String> ids) {
    Validate.notEmpty(ids, "禁用时，id不能为空");
    List<ActivitiesConfig> activitiesConfigs = this.activitiesConfigRepository.findByIds(ids);
    if (org.springframework.util.CollectionUtils.isEmpty(activitiesConfigs)) {
      return;
    }
    // Collection<ActivitiesConfigVo> activitiesDetailCollectFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesConfigs, ActivitiesConfig.class, ActivitiesConfigVo.class, LinkedHashSet.class, ArrayList.class);
    this.activitiesConfigRepository.updateEnableStatusByIds(EnableStatusEnum.DISABLE, ids);
    //todo 通知和业务日志
  }



}
