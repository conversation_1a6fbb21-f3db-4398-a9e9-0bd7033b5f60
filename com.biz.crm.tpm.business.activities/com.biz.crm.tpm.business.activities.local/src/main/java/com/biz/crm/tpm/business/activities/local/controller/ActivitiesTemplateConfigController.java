package com.biz.crm.tpm.business.activities.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesTemplateConfigDto;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesTemplateConfigService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "表单模板配置功能接口")
@RestController
@RequestMapping("/v1/activities/activitiesTemplateConfig")
@Slf4j
public class ActivitiesTemplateConfigController {

    @Autowired(required = false)
    private ActivitiesTemplateConfigService activitiesTemplateConfigService;

    /**
     * 分页查询所有数据
     *
     * @param pageable        分页对象
     * @param dto 查询实体
     * @return 所有数据
     */
    @ApiOperation(value = "分页查询所有数据")
    @GetMapping("findByConditions")
    public Result<Page<ActivitiesTemplateConfigVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                    @ApiParam(name = "activitiesTemplateConfigDto", value = "活动模板配置DTO") ActivitiesTemplateConfigDto dto) {
        try {
            Page<ActivitiesTemplateConfigVo> page = this.activitiesTemplateConfigService.findByConditions(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param activitiesTemplateConfigDto DTO对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @PostMapping
    public Result<?> create(@ApiParam(name = "activitiesTemplateConfigDto", value = "活动模板配置DTO") @RequestBody ActivitiesTemplateConfigDto activitiesTemplateConfigDto) {
        try {
            this.activitiesTemplateConfigService.create(activitiesTemplateConfigDto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改数据
     *
     * @param activitiesTemplateConfigDto DTO对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @PatchMapping
    public Result<?> update(@ApiParam(name = "activitiesTemplateConfigDto", value = "活动模板配置DTO") @RequestBody ActivitiesTemplateConfigDto activitiesTemplateConfigDto) {
        try {
            this.activitiesTemplateConfigService.update(activitiesTemplateConfigDto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "ids", value = "主键集合") @RequestParam("ids") List<String> ids) {
        try {
            this.activitiesTemplateConfigService.delete(ids);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取活动细案明细模板配置
     *
     * @param configCode
     * @return
     */
    @ApiOperation(value = "获取活动细案明细模板配置")
    @GetMapping("findByCode")
    public Result<ActivitiesTemplateConfigVo> findByCode(@ApiParam(value = "模板编码") @RequestParam() String configCode) {
        try {
            ActivitiesTemplateConfigVo configVo = activitiesTemplateConfigService.findByCode(configCode);
            return Result.ok(configVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
