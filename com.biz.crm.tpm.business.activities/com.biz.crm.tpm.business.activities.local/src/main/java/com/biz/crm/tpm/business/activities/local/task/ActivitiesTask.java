package com.biz.crm.tpm.business.activities.local.task;

import com.biz.crm.tpm.business.activities.sdk.service.BasicActivitiesInfoService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.task.annotations.DynamicTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.biz.crm.tpm.business.activities.sdk.strategy.close.ActivityItemsClosedStrategy.GLOBAL_ACTIVITY_STATUS_REDIS_LOCK;

@Component
public class ActivitiesTask {

  @Autowired(required = false)
  private List<BasicActivitiesInfoService> basicActivitiesInfoServices;
  @Autowired
  private RedisMutexService redisMutexService;

  /**
   * 每10分钟执行：检测活动是否到期
   */
  @Transactional
  @DynamicTaskService(cornExpression = "0 */10 * * * ?", taskDesc = "每10分钟执行：检测活动是否到期")
  public void autoRefreshActivityStatusForActivityTime(){
    if(CollectionUtils.isEmpty(basicActivitiesInfoServices)){
      return;
    }
    boolean hasLock = false;
    try{
      hasLock = redisMutexService.tryLock(GLOBAL_ACTIVITY_STATUS_REDIS_LOCK, TimeUnit.SECONDS,10);
      if(hasLock){
        for(BasicActivitiesInfoService basicActivitiesInfoService : basicActivitiesInfoServices){
          basicActivitiesInfoService.refreshActivityStatusForActivityTime();
        }
      }
    }finally {
      if(hasLock){
        redisMutexService.unlock(GLOBAL_ACTIVITY_STATUS_REDIS_LOCK);
      }
    }

  }
}
