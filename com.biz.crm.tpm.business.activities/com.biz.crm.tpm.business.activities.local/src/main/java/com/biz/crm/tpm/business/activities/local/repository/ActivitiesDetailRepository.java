package com.biz.crm.tpm.business.activities.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesConfig;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetail;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesDetailMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.enums.ActivityStatusEnum;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 活动明细信息表;(tpm_activities_detail)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-28
 */
@Component
public class ActivitiesDetailRepository extends ServiceImpl<ActivitiesDetailMapper, ActivitiesDetail> {
  @Autowired
  private ActivitiesDetailMapper activitiesDetailMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<ActivitiesDetailVo> findByConditions(Pageable pageable, ActivitiesDetailDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesDetailMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<ActivitiesDetail>
   */
  public List<ActivitiesDetail> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetail::getId, ids)
            .eq(ActivitiesDetail::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @return 返单条数据
   */
  public ActivitiesDetail findByActivitiesDetailCode(String activitesDetailCode) {
    if (StringUtils.isBlank(activitesDetailCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesDetail::getActivitiesDetailCode, activitesDetailCode)
            .eq(ActivitiesDetail::getTenantCode, tenantCode)
            .one();
  }

  /**
   * 根绝业务编号activitesDetailCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<ActivitiesDetail> findByActivitiesDetailCodes(Collection<String> activitesDetailCodes) {
    if (CollectionUtils.isEmpty(activitesDetailCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetail::getActivitiesDetailCode, activitesDetailCodes)
            .eq(ActivitiesDetail::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @return 返单条数据
   */
  public List<ActivitiesDetail> findByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesDetail::getActivitiesCode, activitiesCode)
            .eq(ActivitiesDetail::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @return 返回多条数据
   */
  public List<ActivitiesDetail> findByActivitiesCodes(Collection<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesDetail::getActivitiesCode, activitiesCodes)
            .eq(ActivitiesDetail::getTenantCode, tenantCode)
            .list();
  }

  public int countByCostTypeCategoryCode(String costTypeCategoryCode) {
    if (StringUtils.isBlank(costTypeCategoryCode)) {
      return 0;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesDetail::getTenantCode, tenantCode)
            .eq(ActivitiesDetail::getCostTypeCategoryCode, costTypeCategoryCode)
            .count();
  }

  public int countByActivitiesCodeAndClosed(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return 0;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesDetail::getTenantCode, tenantCode)
            .eq(ActivitiesDetail::getActivitiesCode, activitiesCode)
            .eq(ActivitiesDetail::getIsClose, BooleanEnum.TRUE.getCapital())
            .count();
  }

  public boolean updateIsExecutedByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(ActivitiesDetail::getTenantCode, tenantCode)
            .eq(ActivitiesDetail::getActivitiesDetailCode, activitiesDetailCode)
            .set(ActivitiesDetail::getIsExecute, BooleanEnum.TRUE.getCapital())
            .update();
  }

  public ActivitiesDetail findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesDetail::getTenantCode,tenantCode)
        .in(ActivitiesDetail::getId,id)
        .one();
  }

  public void updateStatusByActivitiesCodeOrSchemeCode(String activitiesCode, String schemeCode, ActivityStatusEnum activityStatusEnum) {
    String tenantCode = TenantUtils.getTenantCode();
    String status = activityStatusEnum.getCode();
    if (StringUtils.isEmpty(schemeCode)) {
      activitiesDetailMapper.updateStatusByActivitiesCode(activitiesCode, status, tenantCode);
    }
  }
}
