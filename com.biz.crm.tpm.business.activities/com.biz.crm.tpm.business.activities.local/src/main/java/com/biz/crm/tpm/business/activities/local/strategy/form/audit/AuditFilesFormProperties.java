package com.biz.crm.tpm.business.activities.local.strategy.form.audit;

import com.biz.crm.common.form.sdk.widget.BooleanSelectWidget;
import com.biz.crm.tpm.business.activities.sdk.widget.MaintainSelectWidget;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormPropertiesField;
import lombok.Data;

/**
 * 描述：</br>核销资料属性配置项目
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Data
public class AuditFilesFormProperties implements FormProperties {
  /**
   * 是否自动核销
   */
  @FormPropertiesField(name = "维护核销资料", required = true, controllKey = MaintainSelectWidget.class)
  private String maintainFiles;
}
