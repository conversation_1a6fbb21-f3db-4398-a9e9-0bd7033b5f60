package com.biz.crm.tpm.business.activities.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.common.form.sdk.widget.WidgetKey;
import com.biz.crm.tpm.business.budget.sdk.register.FormDimension;
import com.biz.crm.tpm.business.budget.sdk.register.FormEventRegister;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormEventStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormPropertiesField;
import com.biz.crm.tpm.business.activities.sdk.vo.form.FormDimensionVo;
import com.biz.crm.tpm.business.activities.sdk.vo.form.FormEventVo;
import com.biz.crm.tpm.business.activities.sdk.vo.form.FormPropertiesItemVo;
import com.biz.crm.tpm.business.activities.sdk.vo.form.FormPropertiesWidgetVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * 描述：</br>表单事件控制器
 * 提供活动细类绑定表单策略相关的查询功能
 *
 * <AUTHOR>
 * @date 2022/10/31
 * @since 202211
 */
@RestController
@RequestMapping("/v1/formEvent")
@Slf4j
@Api(tags = "表单事件策略管理")
public class FormEventController {
  @Autowired(required = false)
  private FormEventRegister formEventRegister;
  @Autowired(required = false)
  private ApplicationContext ac;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 获取注册的表单事件策略
   *
   * @return
   */
  @GetMapping("/findAll")
  @ApiOperation(value = "获取注册的表单维度")
  public Result<?> findAll() {
    try {
      Validate.notNull(formEventRegister, "未找到表单事件注册器！");
      Collection<Class<? extends FormDimension>> formDimensions = formEventRegister.getFormDimensions();
      List<FormDimensionVo> formDimensionVos = Lists.newArrayList();
      if (!CollectionUtils.isEmpty(formDimensions)) {
        formDimensions.forEach(clazz -> {
          FormDimension formDimension = this.ac.getBean(clazz);
          FormDimensionVo formDimensionVo = this.nebulaToolkitService.copyObjectByWhiteList(formDimension, FormDimensionVo.class, HashSet.class, ArrayList.class);
          Collection<Class<? extends FormEventStrategy>> formEventStrategies = formDimension.getFormEventStrategies();
          List<FormEventVo> formEventVos = Lists.newArrayList();
          if (!CollectionUtils.isEmpty(formEventStrategies)) {
            formEventStrategies.forEach(strategy -> {
              FormEventStrategy formEventStrategy = this.ac.getBean(strategy);
              FormEventVo formEventVo = this.nebulaToolkitService.copyObjectByWhiteList(formEventStrategy, FormEventVo.class, HashSet.class, ArrayList.class);
              formEventVos.add(formEventVo);
            });
            formDimensionVo.setFormEventStrategies(formEventVos);
          }
          formDimensionVos.add(formDimensionVo);
        });
      }
      return Result.ok(formDimensionVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 获取表单维度支持的策略
   *
   * @return
   */
  @GetMapping("/findFormStrategyByCode")
  @ApiOperation(value = "获取表单维度支持的策略")
  public Result<?> findFormStrategyByCode(String code) {
    try {
      Validate.notBlank(code, "查询策略表单维度编号不能为空");
      Collection<Class<? extends FormDimension>> formDimensions = formEventRegister.getFormDimensions();
      List<FormEventVo> formEventVos = Lists.newArrayList();
      if (!CollectionUtils.isEmpty(formDimensions)) {
        formDimensions.forEach(clazz -> {
          FormDimension formDimension = this.ac.getBean(clazz);
          if (formDimension.getCode().equals(code)) {
            FormDimensionVo formDimensionVo = this.nebulaToolkitService.copyObjectByWhiteList(formDimension, FormDimensionVo.class, HashSet.class, ArrayList.class);
            Collection<Class<? extends FormEventStrategy>> formEventStrategies = formDimension.getFormEventStrategies();
            if (!CollectionUtils.isEmpty(formEventStrategies)) {
              formEventStrategies.forEach(strategy -> {
                FormEventStrategy formEventStrategy = this.ac.getBean(strategy);
                FormEventVo formEventVo = this.nebulaToolkitService.copyObjectByWhiteList(formEventStrategy, FormEventVo.class, HashSet.class, ArrayList.class);
                formEventVos.add(formEventVo);
              });
            }
          }
        });
      }
      return Result.ok(formEventVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 获取表单策略属性信息
   *
   * @return
   */
  @GetMapping("/findFormProperties")
  @ApiOperation(value = "获取表单策略属性信息")
  public Result<?> findFormProperties(@ApiParam("表单事件策略编号") String code) {
    try {
      Validate.notBlank(code, "查询表单策略属性信息，策略编号不能为空");
      Collection<Class<? extends FormDimension>> formDimensions = formEventRegister.getFormDimensions();
      Map<String, List<FormPropertiesItemVo>> formMap = Maps.newHashMap();
      if (!CollectionUtils.isEmpty(formDimensions)) {
        formDimensions.forEach(clazz -> {
          FormDimension formDimension = this.ac.getBean(clazz);
          Collection<Class<? extends FormEventStrategy>> formEventStrategies = formDimension.getFormEventStrategies();
          if (!CollectionUtils.isEmpty(formEventStrategies)) {
            formEventStrategies.forEach(strategy -> {
              FormEventStrategy formEventStrategy = this.ac.getBean(strategy);
              FormEventVo formEventVo = this.nebulaToolkitService.copyObjectByWhiteList(formEventStrategy, FormEventVo.class, HashSet.class, ArrayList.class);
              if (formEventVo.getCode().equals(code)) {
                Class<? extends FormProperties> formPropertiesClazz = formEventStrategy.getFormPropertiesInfo();
                if (formPropertiesClazz != null) {
                  List<Field> fieldList = new ArrayList<>();
                  Class fieldClazz = formPropertiesClazz;
                  while (fieldClazz != null) {
                    fieldList.addAll(new ArrayList<>(Arrays.asList(fieldClazz.getDeclaredFields())));
                    fieldClazz = fieldClazz.getSuperclass();
                  }
                  fieldList.forEach(field -> {
                    FormPropertiesField formPropertiesField = field.getAnnotation(FormPropertiesField.class);
                    if (formPropertiesField != null) {
                      FormPropertiesItemVo formPropertiesItemVo = new FormPropertiesItemVo();
                      formPropertiesItemVo.setField(field.getName());
                      formPropertiesItemVo.setName(formPropertiesField.name());
                      formPropertiesItemVo.setRequired(formPropertiesField.required());
                      if (formPropertiesField.controllKey() != null && !formPropertiesField.controllKey().equals(WidgetKey.class)) {
                        WidgetKey widgetKey = this.ac.getBean(formPropertiesField.controllKey());
                        FormPropertiesWidgetVo formPropertiesWidgetVo = new FormPropertiesWidgetVo();
                        formPropertiesWidgetVo.setWidgetCode(widgetKey.widgetCode());
                        formPropertiesWidgetVo.setWidgetName(widgetKey.widgetName());
                        formPropertiesWidgetVo.setWidgetParam(widgetKey.widgetParam());
                        formPropertiesItemVo.setControllKey(formPropertiesWidgetVo);
                      }
                      formPropertiesItemVo.setDisplay(formPropertiesField.display());
                      if (formMap.containsKey(formEventVo.getCode())) {
                        formMap.get(formEventVo.getCode()).add(formPropertiesItemVo);
                      } else {
                        formMap.put(formEventVo.getCode(), Lists.newArrayList(formPropertiesItemVo));
                      }
                    }
                  });
                }
              }
            });
          }
        });
      }
      return Result.ok(formMap.values());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
