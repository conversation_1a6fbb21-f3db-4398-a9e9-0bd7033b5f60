<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.activities.local.mapper.ActivitiesDetailSerialMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailSerial"
             id="ActivitiesDetailOrderMap">
  </resultMap>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailSerialVo">
    select t.*
    from tpm_activities_detail_serial t
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.activitiesCode != null and dto.activitiesCode != ''">
        <bind name="activitiesCode" value="'%' + dto.activitiesCode + '%'"/>
        and t.activities_code like #{activitiesCode}
      </if>
      <if test="dto.activitiesName != null and dto.activitiesName != ''">
        <bind name="activitiesName" value="'%' + dto.activitiesName + '%'"/>
        and t.activities_name like #{activitiesName}
      </if>
      <if test="dto.activitiesDetailCode != null and dto.activitiesDetailCode != ''">
        and t.activities_detail_code = #{dto.activitiesDetailCode}
      </if>
    </where>
    order by t.create_time desc, t.id
  </select>

  <select id="sumTotalAmountByActivitiesDetailCode" resultType="java.math.BigDecimal">
    select sum(t.serial_price)
    from tpm_activities_detail_serial t
    where t.tenant_code = #{tenantCode}
      and t.activities_detail_code = #{activitiesDetailCode}
  </select>
</mapper>