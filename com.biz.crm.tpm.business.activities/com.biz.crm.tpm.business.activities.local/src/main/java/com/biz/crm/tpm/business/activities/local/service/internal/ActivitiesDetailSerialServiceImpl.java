package com.biz.crm.tpm.business.activities.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailSerial;
import com.biz.crm.tpm.business.activities.local.service.ActivitiesDetailSerialService;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesDetailSerialRepository;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailSerialDto;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesDetailSerialEventListener;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailSerialVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;

/**
 * 活动明细订单表(SFA中的订单);(tpm_activities_detail_serial)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@Service("activitiesDetailSerialService")
public class ActivitiesDetailSerialServiceImpl implements ActivitiesDetailSerialService {
  @Autowired
  private ActivitiesDetailSerialRepository activitiesDetailSerialRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private List<ActivitiesDetailSerialEventListener> activitiesDetailSerialEventListeners;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<ActivitiesDetailSerialVo> findByConditions(Pageable pageable, ActivitiesDetailSerialDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesDetailSerialDto();
    }
    return this.activitiesDetailSerialRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public ActivitiesDetailSerialVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    ActivitiesDetailSerial activitiesDetailSerial = this.activitiesDetailSerialRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (activitiesDetailSerial == null) {
      return null;
    }
    ActivitiesDetailSerialVo activitiesDetailSerialVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailSerial, ActivitiesDetailSerialVo.class, LinkedHashSet.class, ArrayList.class);
    return activitiesDetailSerialVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<ActivitiesDetailSerialVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetailSerial> activitiesDetailSerials = this.activitiesDetailSerialRepository.findByIds(ids);
    Collection<ActivitiesDetailSerialVo> activitiesDetailSerialVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailSerials, ActivitiesDetailSerial.class, ActivitiesDetailSerialVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailSerialVos);
  }

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCode
   * @return 单条数据
   */
  @Override
  public List<ActivitiesDetailSerialVo> findByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return Collections.emptyList();
    }
    List<ActivitiesDetailSerial> activitiesDetailSerials = this.activitiesDetailSerialRepository.findByActivitiesCode(activitiesCode);
    if (CollectionUtils.isEmpty(activitiesDetailSerials)) {
      return Collections.emptyList();
    }
    Collection<ActivitiesDetailSerialVo> activitiesDetailSerialVo = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailSerials, ActivitiesDetailSerial.class, ActivitiesDetailSerialVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailSerialVo);
  }

  /**
   * 根绝业务编号activitiesCode获取业务数据
   *
   * @param activitiesCodes
   * @return 多条数据
   */
  @Override
  public List<ActivitiesDetailSerialVo> findByActivitiesCodes(Collection<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Collections.emptyList();
    }
    List<ActivitiesDetailSerial> activitiesDetailSerials = this.activitiesDetailSerialRepository.findByActivitiesCodes(activitiesCodes);
    if (CollectionUtils.isEmpty(activitiesDetailSerials)) {
      return Collections.emptyList();
    }
    Collection<ActivitiesDetailSerialVo> activitiesDetailSerialVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailSerials, ActivitiesDetailSerial.class, ActivitiesDetailSerialVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailSerialVos);
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCode
   * @return 单条数据
   */
  @Override
  public List<ActivitiesDetailSerialVo> findByActivitiesDetailCode(String activitiesDetailCode) {
    if (StringUtils.isBlank(activitiesDetailCode)) {
      return null;
    }
    List<ActivitiesDetailSerial> activitiesDetailSerials = this.activitiesDetailSerialRepository.findByActivitiesDetailCode(activitiesDetailCode);
    if (CollectionUtils.isEmpty(activitiesDetailSerials)) {
      return null;
    }
    Collection<ActivitiesDetailSerialVo> activitiesDetailSerialVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailSerials, ActivitiesDetailSerial.class, ActivitiesDetailSerialVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailSerialVos);
  }

  /**
   * 根绝业务编号activitiesDetailCode获取业务数据
   *
   * @param activitiesDetailCodes
   * @return 多条数据
   */
  @Override
  public List<ActivitiesDetailSerialVo> findByActivitiesDetailCodes(Collection<String> activitiesDetailCodes) {
    if (CollectionUtils.isEmpty(activitiesDetailCodes)) {
      return Collections.emptyList();
    }
    List<ActivitiesDetailSerial> activitiesDetailSerials = this.activitiesDetailSerialRepository.findByActivitiesDetailCodes(activitiesDetailCodes);
    if (CollectionUtils.isEmpty(activitiesDetailSerials)) {
      return Collections.emptyList();
    }
    Collection<ActivitiesDetailSerialVo> activitiesDetailSerialVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailSerials, ActivitiesDetailSerial.class, ActivitiesDetailSerialVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailSerialVos);
  }

  /**
   * 新增数据
   *
   * @param activitiesDetailSerialDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public ActivitiesDetailSerialVo create(ActivitiesDetailSerialDto activitiesDetailSerialDto) {

    this.createValidate(activitiesDetailSerialDto);
    ActivitiesDetailSerial activitiesDetailSerial = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailSerialDto, ActivitiesDetailSerial.class, LinkedHashSet.class, ArrayList.class);
    activitiesDetailSerial.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesDetailSerialRepository.saveOrUpdate(activitiesDetailSerial);
    ActivitiesDetailSerialVo activitiesDetailSerialVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailSerial, ActivitiesDetailSerialVo.class, LinkedHashSet.class, ArrayList.class);

    activitiesDetailSerialVo.setId(activitiesDetailSerial.getId());
    if (!CollectionUtils.isEmpty(activitiesDetailSerialEventListeners)) {
      for (ActivitiesDetailSerialEventListener activitiesDetailSerialEventListener : activitiesDetailSerialEventListeners) {
        activitiesDetailSerialEventListener.onCreated(activitiesDetailSerialVo);
      }
    }

    return activitiesDetailSerialVo;
  }

  /**
   * 批量新增
   *
   * @param activitiesDetailSerialDtos
   * @return
   */
  @Transactional
  @Override
  public List<ActivitiesDetailSerialVo> createBatch(Collection<ActivitiesDetailSerialDto> activitiesDetailSerialDtos) {
    if (CollectionUtils.isEmpty(activitiesDetailSerialDtos)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetailSerialVo> activitiesDetailSerialVos = Lists.newArrayList();
    for (ActivitiesDetailSerialDto activitiesDetailSerialDto : activitiesDetailSerialDtos) {
      ActivitiesDetailSerialVo activitiesDetailSerialVo = this.create(activitiesDetailSerialDto);
      activitiesDetailSerialVos.add(activitiesDetailSerialVo);
    }
    return activitiesDetailSerialVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<ActivitiesDetailSerial> activitiesDetailSerials = this.activitiesDetailSerialRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(activitiesDetailSerials)) {
      return;
    }
    Collection<ActivitiesDetailSerialVo> activitiesDetailSerialVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailSerials, ActivitiesDetailSerial.class, ActivitiesDetailSerialVo.class, LinkedHashSet.class, ArrayList.class);
    this.activitiesDetailSerialRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    if (!CollectionUtils.isEmpty(activitiesDetailSerialEventListeners)) {
      for (ActivitiesDetailSerialEventListener activitiesDetailSerialEventListener : activitiesDetailSerialEventListeners) {
        for (ActivitiesDetailSerialVo activitiesDetailSerialVo : activitiesDetailSerialVos) {
          activitiesDetailSerialEventListener.onDeleted(activitiesDetailSerialVo);
        }
      }
    }
  }

  @Override
  public BigDecimal findAmountByActivitiesDetailCode(String activitiesDetailCode) {
    return this.activitiesDetailSerialRepository.sumAmountByActivitiesCode(activitiesDetailCode);
  }

  /**
   * 创建验证
   *
   * @param activitiesDetailSerialDto
   */
  private void createValidate(ActivitiesDetailSerialDto activitiesDetailSerialDto) {
    Validate.notNull(activitiesDetailSerialDto, "新增时，对象信息不能为空！");
    Validate.isTrue(activitiesDetailSerialDto.getId() == null, "新增数据时,数据主键不为空!");
    Validate.notBlank(activitiesDetailSerialDto.getActivitiesCode(), "新增数据时，活动编号不能为空！");
    Validate.notBlank(activitiesDetailSerialDto.getActivitiesName(), "新增数据时，活动名称不能为空！");
    Validate.notBlank(activitiesDetailSerialDto.getActivitiesDetailCode(), "新增数据时，活动明细编号不能为空！");
    Validate.notBlank(activitiesDetailSerialDto.getSerialNo(), "新增数据时，订单编号不能为空！");
    Validate.notNull(activitiesDetailSerialDto.getSerialPrice(), "新增数据时，订单金额不能为空！");
    Validate.notNull(activitiesDetailSerialDto.getSerialTime(), "新增数据时，订单时间不能为空！");
    Validate.notBlank(activitiesDetailSerialDto.getBtNo(), "新增数据时，提交数据批次编号不能为空！");
    Validate.isTrue(activitiesDetailSerialDto.getSerialPrice().compareTo(BigDecimal.ZERO) > 0, "新增数据时，订单金额不能小于0!");
  }

}
