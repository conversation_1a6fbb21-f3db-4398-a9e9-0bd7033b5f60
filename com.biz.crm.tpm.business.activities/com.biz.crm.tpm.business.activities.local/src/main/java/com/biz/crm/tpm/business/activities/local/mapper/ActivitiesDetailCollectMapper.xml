<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.activities.local.mapper.ActivitiesDetailCollectMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailCollect" id="ActivitiesDetailCollectMap">
  </resultMap>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectVo">
    select
    t.*
    from tpm_activities_detail_collect t
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != '' ">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.activitiesCode != null and dto.activitiesCode != '' ">
        <bind name = "activitiesCode" value = " '%' + dto.activitiesCode + '%' " />
        and t.activities_code like #{activitiesCode}
      </if>
      <if test="dto.activitiesName != null and dto.activitiesName != '' ">
        <bind name = "activitiesName" value = " '%' + dto.activitiesName + '%' " />
        and t.activities_name like #{activitiesName}
      </if>
      <if test="dto.activitiesDetailCode != null and dto.activitiesDetailCode != '' ">
        and t.activities_detail_code = #{dto.activitiesDetailCode}
      </if>
      <if test="dto.btNo != null and dto.btNo != '' ">
        and t.bt_no = #{dto.btNo}
      </if>
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>