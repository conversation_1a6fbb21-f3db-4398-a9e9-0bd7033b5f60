package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR> rentao
 * @date : 2022/10/28 11:31
 */
@ApiModel(value = "ActivitiesConfig", description = "活动表单配置")
@TableName("tpm_activities_config")
@Getter
@Setter
@Entity(name = "tpm_activities_config")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_config", comment = "活动表单配置")
@Table(name = "tpm_activities_config", indexes = {
        @Index(name = "tpm_activities_config_index2", columnList = "activities_config_code,tenant_code")})
public class ActivitiesConfig extends TenantFlagOpEntity {

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiyConfigName", notes = "活动配置名称", value = "活动配置名称")
  @Column(name = "activities_config_name", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动配置名称 '")
  private String activitiesConfigName;

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiyConfigCode", notes = "活动配置编号", value = "活动配置编号")
  @Column(name = "activities_config_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动配置编号 '")
  private String activitiesConfigCode;

  /**
   * 活动表单类型名称
   */
  @ApiModelProperty(name = "activitiesFormTypeCode", notes = "活动表单类型编号", value = "活动表单类型编号")
  @Column(name = "activities_form_type_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动表单类型编号 '")
  private String activitiesFormTypeCode;

  /**
   * 活动表单类型名称
   */
  @ApiModelProperty(name = "activitiesFormTypeName", notes = "活动表单类型名称", value = "活动表单类型名称")
  @Column(name = "activities_form_type_name", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动表单类型名称 '")
  private String activitiesFormTypeName;

  /**
   * 动态表单编号
   */
  @ApiModelProperty(name = "dynamicFormCode", notes = "动态表单编号", value = "动态表单编号")
  @Column(name = "dynamic_form_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '动态表单编号 '")
  private String dynamicFormCode;

  /**
   * 动态表单名称
   */
  @ApiModelProperty(name = "dynamicFormName", notes = "动态表单名称", value = "动态表单名称")
  @Column(name = "dynamic_form_name", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '动态表单名称 '")
  private String dynamicFormName;
}
