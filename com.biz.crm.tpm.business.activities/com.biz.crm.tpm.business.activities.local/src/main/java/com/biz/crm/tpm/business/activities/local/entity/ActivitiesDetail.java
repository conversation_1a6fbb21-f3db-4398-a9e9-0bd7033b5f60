package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：活动明细信息表;
 *
 * <AUTHOR> <PERSON>
 * @date : 2022-6-28
 */
@ApiModel(value = "ActivitiesDetail", description = "活动明细信息表")
@TableName("tpm_activities_detail")
@Getter
@Setter
@Entity(name = "tpm_activities_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_detail", comment = "活动明细信息表")
@Table(name = "tpm_activities_detail", indexes = {
        @Index(name = "tpm_activities_detail_index1", columnList = "activities_detail_code,tenant_code"),
        @Index(name = "tpm_activities_detail_index2", columnList = "activities_code,tenant_code")})
public class ActivitiesDetail extends TenantEntity {

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesCode", notes = "活动编号", value = "活动编号")
  @Column(name = "activities_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /**
   * 活动名称
   */
  @ApiModelProperty(name = "activitiesName", notes = "活动名称", value = "活动名称")
  @Column(name = "activities_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 活动明细编号
   */
  @ApiModelProperty(name = "activitiesDetailCode", notes = "活动明细编号时间", value = "活动明细编号时间")
  @Column(name = "activities_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动明细编号 '")
  private String activitiesDetailCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "costTypeCategoryName", notes = "活动大类名称", value = "活动大类名称")
  @Column(name = "cost_type_category_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动大类名称 '")
  private String costTypeCategoryName;

  /**
   * 活动大类编号
   */
  @ApiModelProperty(name = "costTypeCategoryCode", notes = "活动大类编号", value = "活动大类编号")
  @Column(name = "cost_type_category_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动大类编号 '")
  private String costTypeCategoryCode;

  /**
   * 费用预算编码
   */
  @ApiModelProperty(name = "costBudgetCode", notes = "费用预算编码", value = "费用预算编码")
  @Column(name = "cost_budget_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '费用预算编码 '")
  private String costBudgetCode;

  /**
   * 活动细类编号
   */
  @ApiModelProperty(name = "costTypeDetailCode", notes = "活动细类编号", value = "活动细类编号")
  @Column(name = "cost_type_detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动细类编号 '")
  private String costTypeDetailCode;

  /**
   * 活动细类名称
   */
  @ApiModelProperty(name = "costTypeDetailName", notes = "活动细类名称", value = "活动细类名称")
  @Column(name = "cost_type_detail_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动细类名称 '")
  private String costTypeDetailName;

  /**
   * 组织编码
   */
  @ApiModelProperty(name = "orgCode", notes = "组织编码", value = "组织编码")
  @Column(name = "org_code", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编码 '")
  private String orgCode;

  /**
   * 组织名称
   */
  @ApiModelProperty(name = "orgName", notes = "组织名称", value = "组织名称")
  @Column(name = "org_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT '组织名称 '")
  private String orgName;

  /**
   * 客户编号
   */
  @ApiModelProperty(name = "customerCode", notes = "客户编号", value = "客户编号")
  @Column(name = "customer_code", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT '客户编号 '")
  private String customerCode;

  /**
   * 客户名称
   */
  @ApiModelProperty(name = "customerName", notes = "客户名称", value = "客户名称")
  @Column(name = "customer_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT '客户名称 '")
  private String customerName;

  /**
   * 终端编号
   */
  @ApiModelProperty(name = "terminalCode", notes = "终端编号", value = "终端编号")
  @Column(name = "terminal_code", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT '终端编号 '")
  private String terminalCode;

  /**
   * 终端名称
   */
  @ApiModelProperty(name = "terminalName", notes = "终端名称", value = "终端名称")
  @Column(name = "terminal_name", nullable = true, length = 128, columnDefinition = "VARCHAR(128) COMMENT '终端名称 '")
  private String terminalName;

  /**
   * 申请金额
   */
  @ApiModelProperty(name = "applyAmount", notes = "申请金额", value = "申请金额")
  @Column(name = "apply_amount", nullable = false, length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '申请金额 '")
  private BigDecimal applyAmount;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "payType", notes = "支付方式", value = "支付方式")
  @Column(name = "pay_type", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '支付方式 '")
  private String payType;

  /**
   * 支付方式名称
   */
  @ApiModelProperty(name = "payTypeName", notes = "支付方式名称", value = "支付方式名称")
  @Column(name = "pay_type_name", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '支付方式名称 '")
  private String payTypeName;

  /**
   * 预算科目编码
   */
  @ApiModelProperty(name = "budgetSubjectsCode", notes = "预算科目编码", value = "预算科目编码")
  @Column(name = "budget_subjects_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
  private String budgetSubjectsCode;

  /**
   * 预算科目名称
   */
  @ApiModelProperty(name = "budgetSubjectsName", notes = "预算科目名称", value = "预算科目名称")
  @Column(name = "budget_subjects_name", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;

  /**
   * 费用日期(年月)
   */
  @ApiModelProperty(name = "feeDate", notes = "费用日期(年月)", value = "费用日期(年月)")
  @Column(name = "fee_date", nullable = true, length = 255, columnDefinition = "VARCHAR(255) COMMENT '费用日期(年月) '")
  private String feeDate;

  /**
   * 是否关闭
   */
  @ApiModelProperty(name = "isClose", notes = "是否关闭", value = "是否关闭")
  @Column(name = "is_close", nullable = false, length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否关闭 '")
  private String isClose;

  /**
   * 是否完全核销
   */
  @ApiModelProperty(name = "isFullAudit", notes = "是否完全核销", value = "是否完全核销")
  @Column(name = "is_full_audit", nullable = false, length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否完全核销 '")
  private String isFullAudit;

  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "create_time", length = 20, columnDefinition = "datetime COMMENT '创建时间 '")
  private Date createTime;

  /**
   * 创建人账号
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "create_account", length = 60, columnDefinition = "varchar(60) COMMENT '创建人账号'")
  private String createAccount;

  /**
   * 创建人名称
   */
  @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "create_name", length = 60, columnDefinition = "varchar(60) COMMENT '创建人名称'")
  private String createName;

  /**
   * 已使用金额
   */
  @ApiModelProperty(name = "totalAmount", notes = "已使用金额", value = "已使用金额")
  @Column(name = "total_amount", nullable = false, length = 20, columnDefinition = "DECIMAL(20,4) COMMENT '已使用金额 '")
  private BigDecimal totalAmount;

  /**
   * 是否推送sfa
   */
  @ApiModelProperty(name = "isSendSfa", notes = "是否推送sfa", value = "是否推送sfa")
  @Column(name = "is_send_sfa", length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否推送sfa '")
  private String isSendSfa;

  /**
   * 是否执行
   */
  @ApiModelProperty(name = "isExecute", notes = "是否执行", value = "是否执行")
  @Column(name = "is_execute", nullable = false, length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否执行 '")
  private String isExecute;

}
