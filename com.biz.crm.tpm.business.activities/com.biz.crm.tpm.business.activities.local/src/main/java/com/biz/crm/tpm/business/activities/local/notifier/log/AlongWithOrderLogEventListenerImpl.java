package com.biz.crm.tpm.business.activities.local.notifier.log;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.activities.sdk.dto.AlongWithOrderDto;
import com.biz.crm.tpm.business.activities.sdk.dto.log.AlongWithOrderLogEventDto;
import com.biz.crm.tpm.business.activities.sdk.event.log.AlongWithOrderLogEventListener;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class AlongWithOrderLogEventListenerImpl implements AlongWithOrderLogEventListener {

    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 创建事件
     *
     * @param eventDto
     */
    @Override
    public void onCreate(AlongWithOrderLogEventDto eventDto) {
        AlongWithOrderDto newest = eventDto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(null);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    /**
     * 删除事件
     *
     * @param eventDto
     */
    @Override
    public void onDelete(AlongWithOrderLogEventDto eventDto) {
        List<AlongWithOrderDto> newList = Lists.newArrayList();
        if (Objects.nonNull(eventDto.getNewest())) {
            newList.add(eventDto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(eventDto.getNewestList())) {
            newList.addAll(eventDto.getNewestList());
        }
        List<AlongWithOrderDto> oldList = (List<AlongWithOrderDto>) this.nebulaToolkitService.copyCollectionByBlankList(newList, AlongWithOrderDto.class, AlongWithOrderDto.class, HashSet.class, ArrayList.class);
        Map<String, AlongWithOrderDto> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(AlongWithOrderDto::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            AlongWithOrderDto original = oldMap.getOrDefault(newest.getId(), new AlongWithOrderDto());
            original.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setDelFlag(DelFlagStatusEnum.DELETE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    /**
     * 更新日志
     *
     * @param eventDto
     */
    @Override
    public void onUpdate(AlongWithOrderLogEventDto eventDto) {
        AlongWithOrderDto original = eventDto.getOriginal();
        AlongWithOrderDto newest = eventDto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    /**
     * 启用事件
     *
     * @param dto
     */
    @Override
    public void onEnable(AlongWithOrderLogEventDto dto) {
        List<AlongWithOrderDto> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        List<AlongWithOrderDto> oldList = (List<AlongWithOrderDto>) this.nebulaToolkitService.copyCollectionByBlankList(newList, AlongWithOrderDto.class, AlongWithOrderDto.class, HashSet.class, ArrayList.class);
        Map<String, AlongWithOrderDto> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(AlongWithOrderDto::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.ENABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            AlongWithOrderDto original = oldMap.getOrDefault(newest.getId(), new AlongWithOrderDto());
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    /**
     * 禁用事件
     *
     * @param dto
     */
    @Override
    public void onDisable(AlongWithOrderLogEventDto dto) {
        List<AlongWithOrderDto> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        List<AlongWithOrderDto> oldList = (List<AlongWithOrderDto>) this.nebulaToolkitService.copyCollectionByBlankList(newList, AlongWithOrderDto.class, AlongWithOrderDto.class, HashSet.class, ArrayList.class);
        Map<String, AlongWithOrderDto> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(AlongWithOrderDto::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DISABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            AlongWithOrderDto original = oldMap.getOrDefault(newest.getId(), new AlongWithOrderDto());
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }
}
