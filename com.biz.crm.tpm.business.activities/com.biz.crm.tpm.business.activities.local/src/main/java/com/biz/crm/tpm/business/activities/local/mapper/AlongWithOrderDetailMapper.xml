<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.local.mapper.AlongWithOrderDetailMapper">

    <select id="findByDto" resultType="com.biz.crm.tpm.business.activities.sdk.vo.AlongWithOrderDetailVo">
        select
        t.*
        from tpm_along_with_order_detail t
        where t.del_flag = '${@<EMAIL>()}'
        and t.enable_status = '${@<EMAIL>()}'
        <if test="dto.alongWithOrderCode != null and dto.alongWithOrderCode != ''">
            and t.along_with_order_code = #{dto.alongWithOrderCode}
        </if>
        <if test="dto.customerCode != null and dto.customerCode != ''">
            and t.customer_code = #{dto.customerCode}
        </if>
        <if test="dto.productCode != null and dto.productCode != ''">
            and t.product_code = #{dto.productCode}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            and t.product_name like concat('%', #{dto.productName}, '%')
        </if>

    </select>
</mapper>

