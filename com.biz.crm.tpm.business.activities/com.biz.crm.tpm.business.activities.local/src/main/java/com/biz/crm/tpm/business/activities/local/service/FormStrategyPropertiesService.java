package com.biz.crm.tpm.business.activities.local.service;

import com.biz.crm.tpm.business.activities.local.entity.FormStrategyProperties;
import com.biz.crm.tpm.business.budget.sdk.dto.FormStrategyPropertiesDto;

/**
 * TPM-表单执行策略;(tpm_occupy_properties)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
public interface FormStrategyPropertiesService {

  /**
   * 通过业务编号获取表单事件配置信息
   *
   * @param businessCode
   * @return
   */
  FormStrategyProperties findByCodeAndBusinessCode(String code, String businessCode);

  /**
   * 新增数据
   *
   * @param dto 实体对象
   * @return 新增结果
   */
  FormStrategyProperties create(FormStrategyPropertiesDto dto);

  /**
   * 更新数据
   *
   * @param dto 实体对象
   * @return 新增结果
   */
  FormStrategyProperties update(FormStrategyPropertiesDto dto);

  /**
   * 根据id删除数据
   *
   * @param id
   */
  void deleteById(String id);
}
