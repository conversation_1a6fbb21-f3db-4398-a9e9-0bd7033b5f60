package com.biz.crm.tpm.business.activities.local.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.local.entity.Activities;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesRepository;
import com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDto;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesShareDto;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesFilterEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesConfigVoService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesDetailService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.strategy.share.ActivitiesShareStrategy;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BasicActivityItemVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.BusinessStrategySettingExecutor;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 活动信息表;(tpm_activities)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-14
 */
@Service("activitiesVoService")
public class ActivitiesServiceImpl implements ActivitiesService {
  @Autowired
  private ActivitiesRepository activitiesRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;
  @Autowired
  private ActivitiesDetailService activitiesDetailService;
  @Autowired(required = false)
  private List<ActivitiesShareStrategy> shareStrategies;
  @Autowired(required = false)
  private List<ActivitiesFilterEventListener> activitiesFilterEventListeners;
  @Autowired(required = false)
  private List<BusinessStrategySettingExecutor> businessStrategySettingExecutors;
  @Autowired
  private ActivitiesConfigVoService activitiesConfigVoService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  @Override
  public Page<ActivitiesVo> findByConditions(Pageable pageable, ActivitiesDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesDto();
    }
    Set<String> excludeActivitiesCodes = Sets.newHashSet();
    if (!CollectionUtils.isEmpty(activitiesFilterEventListeners)) {
      for (ActivitiesFilterEventListener activitiesFilterEventListener : activitiesFilterEventListeners) {
        excludeActivitiesCodes.addAll(activitiesFilterEventListener.filterActivitiesCode(dto));
      }
    }
    dto.setExcludeActivitiesCodes(excludeActivitiesCodes);
    //仅仅只是查询"已注册"的活动信息
    return this.activitiesRepository.findByConditions(pageable, dto);
  }

  /**
   * 对返回数据进行扩展填充
   */
  private void extendDataHandler(List<BasicActivityItemVo> basicActivityItemVos) {
    basicActivityItemVos.forEach(item -> {
      CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(item.getCostTypeDetailCode());
      item.setCostTypeDetailVo(costTypeDetailVo);
    });
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public ActivitiesVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    Activities activities = this.activitiesRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (activities == null) {
      return null;
    }
    return nebulaToolkitService.copyObjectByWhiteList(activities, ActivitiesVo.class, LinkedHashSet.class, ArrayList.class);
  }

  /**
   * 通过编号查询单条数据
   *
   * @param activitiesCode 编码
   * @return 单条数据
   */
  @Override
  public ActivitiesVo findByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return null;
    }
    Activities activities = this.activitiesRepository.findByActivitiesCode(activitiesCode);
    if (activities == null) {
      return null;
    }
    return nebulaToolkitService.copyObjectByWhiteList(activities, ActivitiesVo.class, LinkedHashSet.class, ArrayList.class);
  }

  @Override
  public List<ActivitiesVo> findByActivitiesCodes(Set<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Lists.newArrayList();
    }
    List<Activities> activities = this.activitiesRepository.findByActivitiesCodes(activitiesCodes);
    if (CollectionUtils.isEmpty(activities)) {
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(activities, Activities.class, ActivitiesVo.class, LinkedHashSet.class, ArrayList.class));
  }

  @Override
  public ActivitiesVo findDetailsByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return null;
    }
    Activities activities = this.activitiesRepository.findByActivitiesCode(activitiesCode);
    if (activities == null) {
      return null;
    }
    ActivitiesVo activitiesVo = nebulaToolkitService.copyObjectByWhiteList(activities, ActivitiesVo.class, LinkedHashSet.class, ArrayList.class);
    List<ActivitiesDetailVo> detailsVos = activitiesDetailService.findByActivitiesCode(activitiesVo.getActivitiesCode());
    activitiesVo.setActivitiesDetails(detailsVos);
    return activitiesVo;
  }

  /**
   * 新增数据
   *
   * @param activitiesDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public ActivitiesVo create(ActivitiesDto activitiesDto) {
    this.createValidate(activitiesDto);
    /*
     * 注册时候对活动数据进行初步处理
     * 1、计算活动明细总数
     * 2、计算活动明细中需要核销的数据(包含自动核销)
     * 3、计算活动中自动核销的数量
     */
    //活动明细记录
    Set<ActivitiesDetailDto> activitiesDetails = activitiesDto.getActivitiesDetails();

    int detailNum = activitiesDetails.size();
    int waitAuditDetailNum = 0;
    int autoAuditDetailNum = 0;
    for (ActivitiesDetailDto activitiesDetailDto : activitiesDetails) {
      String costTypeDetailCode = activitiesDetailDto.getCostTypeDetailCode();
      CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(costTypeDetailCode);
      if (ObjectUtils.isNotEmpty(costTypeDetailVo.getExecutionFromBody()) && costTypeDetailVo.getExecutionFromBody().containsKey(ActivitiesConstant.STRATEGY_AUTO_AUDIT)) {
        JSONObject pushSfaBody = costTypeDetailVo.getExecutionFromBody().getJSONObject(ActivitiesConstant.STRATEGY_AUTO_AUDIT);
        boolean isAudit = pushSfaBody.getBoolean(ActivitiesConstant.FORM_PROPERTIES_ENABLED);
        //todo 自动核销去掉
        if (!isAudit) {
          waitAuditDetailNum++;
        }
      }
    }

    Activities activities = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDto, Activities.class, LinkedHashSet.class, ArrayList.class);
    activities.setDetailNum(detailNum);
    activities.setWaitAuditDetailNum(waitAuditDetailNum);
    activities.setAutoAuditDetailNum(autoAuditDetailNum);
    activities.setTenantCode(TenantUtils.getTenantCode());
    if ((waitAuditDetailNum - autoAuditDetailNum) == 0) {
      activities.setIsAudit(BooleanEnum.TRUE.getCapital());
    } else {
      activities.setIsAudit(BooleanEnum.FALSE.getCapital());
    }
    activities.setIsClose(BooleanEnum.FALSE.getCapital());
    this.activitiesRepository.saveOrUpdate(activities);
    ActivitiesVo activitiesVo = nebulaToolkitService.copyObjectByWhiteList(activities, ActivitiesVo.class, LinkedHashSet.class, ArrayList.class);

    activitiesDetails.forEach(item -> item.setActivitiesCode(activities.getActivitiesCode()));
    List<ActivitiesDetailVo> activitiesDetailVos = this.activitiesDetailService.createBatch(activitiesDetails);
    activitiesVo.setActivitiesDetails(activitiesDetailVos);
    return activitiesVo;
  }

  @Override
  @Transactional
  public ActivitiesVo update(ActivitiesDto activitiesDto) {
    this.updateValidate(activitiesDto);
    Activities activities = this.activitiesRepository.findByActivitiesCode(activitiesDto.getActivitiesCode());
    activities.setIsClose(activitiesDto.getIsClose());
    activities.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesRepository.saveOrUpdate(activities);
    ActivitiesVo activitiesVo = this.nebulaToolkitService.copyObjectByWhiteList(activities, ActivitiesVo.class, LinkedHashSet.class, ArrayList.class);
    List<ActivitiesDetailVo> activitiesDetailVos = this.activitiesDetailService.findByActivitiesCode(activitiesVo.getActivitiesCode());
    activitiesVo.setActivitiesDetails(activitiesDetailVos);
    return activitiesVo;
  }


  @Override
  public List<?> shareForFeeDate(ActivitiesShareDto dto) {
    ActivitiesShareStrategy shareStrategy = this.getShareStrategy(dto);
    Validate.notNull(shareStrategy, "根据指定的动态表单编码【%s】，未能获取到相应分摊策略", dto.getDynamicFormCode());
    return shareStrategy.share(dto);
  }

  @Override
  @Transactional
  public ActivitiesVo updateForAudit(String activitiesCode) {
    Activities activities = this.activitiesRepository.findByActivitiesCode(activitiesCode);
    Validate.notNull(activities, "活动数据异常，请检查 ！");
    activities.setIsAudit(BooleanEnum.TRUE.getCapital());
    activities.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesRepository.saveOrUpdate(activities);
    return nebulaToolkitService.copyObjectByWhiteList(activities, ActivitiesVo.class, LinkedHashSet.class, ArrayList.class);
  }

  @Override
  @Transactional
  public ActivitiesVo closeForAudit(String activitiesCode) {
    Activities activities = this.activitiesRepository.findByActivitiesCode(activitiesCode);
    Validate.notNull(activities, "活动数据异常，请检查 ！");
    activities.setIsClose(BooleanEnum.TRUE.getCapital());
    activities.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesRepository.saveOrUpdate(activities);
    // 检查活动关闭情况
    // TODO 针对为完成核销的明细进行资金的退回
    return nebulaToolkitService.copyObjectByWhiteList(activities, ActivitiesVo.class, LinkedHashSet.class, ArrayList.class);
  }

  @Override
  public List<ActivitiesVo> findDetailsByActivitiesCodes(Set<String> activitiesCodes) {
    if (CollectionUtils.isEmpty(activitiesCodes)) {
      return Lists.newArrayList();
    }
    List<ActivitiesVo> activities = this.findByActivitiesCodes(activitiesCodes);
    if (CollectionUtils.isEmpty(activities)) {
      return Lists.newArrayList();
    }
    Map<String, List<ActivitiesDetailVo>> details = activitiesDetailService.findByActivitiesCodes(activitiesCodes);
    if (CollectionUtils.isEmpty(details)) {
      return activities;
    }
    for (Map.Entry<String, List<ActivitiesDetailVo>> entry : details.entrySet()) {
      for (ActivitiesVo activitiesVo : activities) {
        if (StringUtils.equals(activitiesVo.getActivitiesCode(), entry.getKey())) {
          activitiesVo.setActivitiesDetails(entry.getValue());
          break;
        }
      }
    }
    return activities;
  }

  @Override
  public List<String> findCodeByTime(String time) {
    if (StringUtils.isBlank(time)) {
      return null;
    }
    return this.activitiesRepository.findCodeByTime(time);
  }

  @Override
  public boolean existsByCostCategory(String costTypeCategoryCode) {
    int count = this.activitiesDetailService.countByCostTypeCategoryCode(costTypeCategoryCode);
    if (count > 0) {
      return true;
    }
    return false;
  }

  /**
   * 创建验证
   */
  private void createValidate(ActivitiesDto activitiesDto) {
    Validate.notNull(activitiesDto, "新增时，对象信息不能为空！");
    Validate.notBlank(activitiesDto.getActivitiesCode(), "新增数据时，活动编号不能为空！");
    Validate.notBlank(activitiesDto.getActivitiesName(), "新增数据时，活动名称不能为空！");
    Validate.notBlank(activitiesDto.getActivityMark(), "新增数据时，活动标识不能为空！");
    Validate.notNull(activitiesDto.getBeginTime(), "新增数据时，开始时间不能为空！");
    Validate.notNull(activitiesDto.getEndTime(), "新增数据时，结束时间不能为空！");
    Validate.notEmpty(activitiesDto.getActivitiesDetails(), "新增数据时，活动明细不能为空！");
  }

  /**
   * 更新验证
   */
  private void updateValidate(ActivitiesDto activitiesDto) {
    Validate.notNull(activitiesDto, "修改时，对象信息不能为空！");
    Validate.notBlank(activitiesDto.getId(), "修改时，主键编号不能为空！");
    Validate.notBlank(activitiesDto.getActivitiesCode(), "修改时，活动编号不能为空！");
    Validate.notBlank(activitiesDto.getActivitiesName(), "修改时，活动名称不能为空！");
    Validate.notBlank(activitiesDto.getActivityMark(), "修改时，活动标识不能为空！");
    Validate.notNull(activitiesDto.getBeginTime(), "修改时，开始时间不能为空！");
    Validate.notNull(activitiesDto.getEndTime(), "修改时，结束时间不能为空！");
    Validate.notNull(activitiesDto.getIsClose(), "修改时，活动关闭标识不能为空！");
  }


  private ActivitiesShareStrategy getShareStrategy(ActivitiesShareDto dto) {
    if (CollectionUtils.isEmpty(shareStrategies)) {
      return null;
    }
    Validate.notNull(dto, "传入的待分摊信息不能为空");
    Date startTime = dto.getStartTime();
    Date endTime = dto.getEndTime();
    String dynamicFormCode = dto.getDynamicFormCode();
    JSONObject dynamicForm = dto.getDynamicForm();
    Validate.notNull(startTime, "活动开始时间不能为空");
    Validate.notNull(endTime, "活动结束时间不能为空");
    Validate.notBlank(dynamicFormCode, "动态表单编码不能为空");
    Validate.notEmpty(dynamicForm, "动态表单信息不能为空");
    ActivitiesConfigVo activitiesConfigVo = this.activitiesConfigVoService.findByActivitiesConfigCode(dto.getDynamicFormCode());
    if(ObjectUtils.isEmpty(activitiesConfigVo)){
      return null;
    }
    for (ActivitiesShareStrategy shareStrategy : shareStrategies) {
      if (StringUtils.equals(shareStrategy.dynamicFormCode(), activitiesConfigVo.getDynamicFormCode())) {
        return shareStrategy;
      }
    }
    return null;
  }
}
