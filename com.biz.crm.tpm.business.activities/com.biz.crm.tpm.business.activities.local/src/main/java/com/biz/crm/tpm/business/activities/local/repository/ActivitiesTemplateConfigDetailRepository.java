package com.biz.crm.tpm.business.activities.local.repository;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesTemplateConfigDetail;
import com.biz.crm.tpm.business.activities.local.mapper.ActivitiesTemplateConfigDetailMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * 活动模板配置字段配置实体类(ActivitiesTemplateConfigDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-31 15:57:01
 */
@Component
public class ActivitiesTemplateConfigDetailRepository extends ServiceImpl<ActivitiesTemplateConfigDetailMapper, ActivitiesTemplateConfigDetail> {

  @Autowired
  private ActivitiesTemplateConfigDetailMapper activitiesTemplateConfigDetailMapper;

  /**
   * 根据configId查询
   *
   * @param configCode
   * @return
   */
  public List<ActivitiesTemplateConfigDetail> findByConfigCode(String configCode) {
    return this.lambdaQuery().eq(ActivitiesTemplateConfigDetail::getConfigCode, configCode)
            .eq(ActivitiesTemplateConfigDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivitiesTemplateConfigDetail::getTenantCode, TenantUtils.getTenantCode())
            .orderByAsc(ActivitiesTemplateConfigDetail::getFormorder).list();
  }

  /**
   * 根据configCode删除
   *
   * @param configCodeList
   */
  public void deleteByConfigCodeList(List<String> configCodeList) {
    this.lambdaUpdate().in(ActivitiesTemplateConfigDetail::getConfigCode, configCodeList)
            .remove();
  }

  /**
   * 根据configId查询
   *
   * @param configCodes
   * @return
   */
  public List<ActivitiesTemplateConfigDetail> findByConfigCodes(List<String> configCodes) {
    if (CollectionUtils.isEmpty(configCodes)) {
      return new ArrayList<>(0);
    }
    return this.lambdaQuery().in(ActivitiesTemplateConfigDetail::getConfigCode, configCodes)
            .eq(ActivitiesTemplateConfigDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ActivitiesTemplateConfigDetail::getTenantCode, TenantUtils.getTenantCode())
            .orderByAsc(ActivitiesTemplateConfigDetail::getFormorder).list();
  }
}

