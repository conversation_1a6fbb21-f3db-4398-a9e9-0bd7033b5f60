package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ActivitiesTemplateConfigDetail", description = "活动模板配置字段配置实体类")
@Entity
@org.hibernate.annotations.Table(appliesTo = "`tpm_activities_template_config_detail`", comment = "活动模板配置字段配置实体类")
@TableName("tpm_activities_template_config_detail")
@Table(
        name = "`tpm_activities_template_config_detail`",
        indexes = {
            @Index(name = "index_config_code", columnList = "config_code"),
        }
)
public class ActivitiesTemplateConfigDetail extends TenantFlagOpEntity {


    /**
     * 配置编码
     */
    @ApiModelProperty("配置编码")
    @Column(name = "config_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '配置编码'")
    private String configCode;

    /**
     * 显示属性
     */
    @ApiModelProperty("显示属性")
    @Column(name = "field", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '显示属性'")
    private String field;

    /**
     * 字段名称
     */
    @ApiModelProperty("字段名称")
    @Column(name = "title", length = 64, columnDefinition = "VARCHAR(64) COMMENT '字段名称'")
    private String title;

    /**
     * 显隐状态：1显示，0隐藏
     */
    @ApiModelProperty("显隐状态：1显示，0隐藏")
    @Column(name = "visible", length = 1, columnDefinition = "char(1) COMMENT '显隐状态：1显示，0隐藏'")
    private Boolean visible;

    /**
     * 是否必填
     */
    @ApiModelProperty("是否必填")
    @Column(name = "required", length = 1, columnDefinition = "char(1) COMMENT '是否必填'")
    private Boolean required;

    /**
     * 新增页面是否可编辑
     */
    @ApiModelProperty("新增页面是否可编辑")
    @Column(name = "editable_in_create", length = 1, columnDefinition = "char(1) COMMENT '新增页面是否可编辑'")
    private Boolean editableInCreate;

    /**
     * 编辑页面是否可编辑
     */
    @ApiModelProperty("编辑页面是否可编辑")
    @Column(name = "editable_in_edit", length = 1, columnDefinition = "char(1) COMMENT '编辑页面是否可编辑'")
    private Boolean editableInEdit;

    /**
     * 编辑时是否可显示
     */
    @ApiModelProperty("编辑时是否可显示")
    @Column(name = "visible_in_edit", length = 1, columnDefinition = "char(1) COMMENT '编辑时是否可显示'")
    private Boolean visibleInEdit;

    /**
     * 查看时是否显示
     */
    @ApiModelProperty("查看时是否显示")
    @Column(name = "visible_in_look", length = 1, columnDefinition = "char(1) COMMENT '查看时是否显示'")
    private Boolean visibleInLook;

    /**
     * 控件类型
     */
    @ApiModelProperty("控件类型")
    @Column(name = "form_control", length = 64, columnDefinition = "VARCHAR(64) COMMENT '控件类型'")
    private String formControl;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    @Column(name = "formorder", length = 64, columnDefinition = "int(11) COMMENT '排序'")
    private Integer formorder;

    /**
     * 排序
     */
    @ApiModelProperty("数据字典")
    @Column(name = "dict_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '数据字典'")
    private String dictCode;

    /**
     * 是否导出：1导出，0不导出
     */
    @ApiModelProperty("是否导出：1导出，0不导出")
    @Column(name = "exports", length = 1, columnDefinition = "char(1) COMMENT '是否导出：1导出，0不导出'")
    private Boolean exports;
}
