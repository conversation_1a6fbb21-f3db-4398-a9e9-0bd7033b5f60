package com.biz.crm.tpm.business.activities.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.local.entity.ActivitiesDetailCollectFiles;
import com.biz.crm.tpm.business.activities.local.repository.ActivitiesDetailCollectFilesRepository;
import com.biz.crm.tpm.business.activities.local.service.ActivitiesDetailCollectFilesService;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailCollectFilesDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesDetailCollectFilesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;

/**
 * 活动明细采集附件信息表(SFA中的订单);(tpm_activities_detail_collect_files)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-7-4
 */
@Service("activitiesDetailCollectFilesService")
public class ActivitiesDetailCollectFilesServiceImpl implements ActivitiesDetailCollectFilesService {
  @Autowired
  private ActivitiesDetailCollectFilesRepository activitiesDetailCollectFilesRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private RedisMutexService redisMutexService;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<ActivitiesDetailCollectFilesVo> findByConditions(Pageable pageable, ActivitiesDetailCollectFilesDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesDetailCollectFilesDto();
    }
    return this.activitiesDetailCollectFilesRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public ActivitiesDetailCollectFilesVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    ActivitiesDetailCollectFiles activitiesDetailCollectFiles = this.activitiesDetailCollectFilesRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (activitiesDetailCollectFiles == null) {
      return null;
    }
    ActivitiesDetailCollectFilesVo activitiesDetailCollectFilesVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailCollectFiles, ActivitiesDetailCollectFilesVo.class, LinkedHashSet.class, ArrayList.class);
    return activitiesDetailCollectFilesVo;
  }

  /**
   * 通过主键查询多条数据
   *
   * @param ids 主键集合
   * @return 多条数据
   */
  @Override
  public List<ActivitiesDetailCollectFilesVo> findByIds(Collection<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetailCollectFiles> activitiesDetailCollectFiless = this.activitiesDetailCollectFilesRepository.findByIds(ids);
    Collection<ActivitiesDetailCollectFilesVo> activitiesDetailCollectFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailCollectFiless, ActivitiesDetailCollectFiles.class, ActivitiesDetailCollectFilesVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailCollectFilesVos);
  }

  /**
   * 新增数据
   *
   * @param activitiesDetailCollectFilesDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public ActivitiesDetailCollectFilesVo create(ActivitiesDetailCollectFilesDto activitiesDetailCollectFilesDto) {
    this.createValidate(activitiesDetailCollectFilesDto);
    ActivitiesDetailCollectFiles activitiesDetailCollectFiles = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailCollectFilesDto, ActivitiesDetailCollectFiles.class, LinkedHashSet.class, ArrayList.class);
    activitiesDetailCollectFiles.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesDetailCollectFilesRepository.saveOrUpdate(activitiesDetailCollectFiles);
    ActivitiesDetailCollectFilesVo activitiesDetailCollectFilesVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesDetailCollectFiles, ActivitiesDetailCollectFilesVo.class, LinkedHashSet.class, ArrayList.class);
    activitiesDetailCollectFilesVo.setId(activitiesDetailCollectFiles.getId());
    return activitiesDetailCollectFilesVo;
  }

  /**
   * 批量新增
   *
   * @param activitiesDetailCollectFilesDtos
   * @return
   */
  @Transactional
  @Override
  public List<ActivitiesDetailCollectFilesVo> createBatch(Collection<ActivitiesDetailCollectFilesDto> activitiesDetailCollectFilesDtos) {
    if (CollectionUtils.isEmpty(activitiesDetailCollectFilesDtos)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetailCollectFilesVo> activitiesDetailCollectFilesVos = Lists.newArrayList();
    for (ActivitiesDetailCollectFilesDto activitiesDetailCollectFilesDto : activitiesDetailCollectFilesDtos) {
      ActivitiesDetailCollectFilesVo activitiesDetailCollectFilesVo = this.create(activitiesDetailCollectFilesDto);
      activitiesDetailCollectFilesVos.add(activitiesDetailCollectFilesVo);
    }
    return activitiesDetailCollectFilesVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(Collection<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<ActivitiesDetailCollectFiles> activitiesDetailCollectFiless = this.activitiesDetailCollectFilesRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(activitiesDetailCollectFiless)) {
      return;
    }
    Collection<ActivitiesDetailCollectFilesVo> activitiesDetailCollectFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailCollectFiless, ActivitiesDetailCollectFiles.class, ActivitiesDetailCollectFilesVo.class, LinkedHashSet.class, ArrayList.class);
    this.activitiesDetailCollectFilesRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  @Override
  public List<ActivitiesDetailCollectFilesVo> findByCollectCode(String collectCode) {
    if (StringUtils.isBlank(collectCode)) {
      return Lists.newArrayList();
    }
    List<ActivitiesDetailCollectFiles> activitiesDetailCollectFiless = this.activitiesDetailCollectFilesRepository.findByCollectCode(collectCode);
    Collection<ActivitiesDetailCollectFilesVo> activitiesDetailCollectFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesDetailCollectFiless, ActivitiesDetailCollectFiles.class, ActivitiesDetailCollectFilesVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesDetailCollectFilesVos);
  }

  /**
   * 创建验证
   *
   * @param activitiesDetailCollectFilesDto
   */
  private void createValidate(ActivitiesDetailCollectFilesDto activitiesDetailCollectFilesDto) {
    Validate.notNull(activitiesDetailCollectFilesDto, "新增时，对象信息不能为空！");
    activitiesDetailCollectFilesDto.setId(null);
    Validate.notBlank(activitiesDetailCollectFilesDto.getFileCode(), "新增数据时，文件唯一识别号不能为空！");
    Validate.notBlank(activitiesDetailCollectFilesDto.getCollectCode(), "新增数据时，采集编号不能为空！");
    Validate.notBlank(activitiesDetailCollectFilesDto.getCollectRequireCode(), "新增数据时，采集请求编号不能为空！");
    Validate.notBlank(activitiesDetailCollectFilesDto.getCollectRequireName(), "新增数据时，采集请求名称不能为空！");
  }

}
