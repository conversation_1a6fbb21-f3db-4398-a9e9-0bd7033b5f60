package com.biz.crm.tpm.business.activities.local.strategy.form.audit;

import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.tpm.business.activities.local.strategy.form.AbstractFormEventStrategy;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

/**
 * 描述：</br>核销有效期策略
 *
 * <AUTHOR>
 * @date 2022/11/8
 */
@Component
@Slf4j
public class ExpirationAuditFormEventStrategy extends AbstractFormEventStrategy {
  @Autowired(required = false)
  private ActivitiesService activitiesService;

  @Override
  public String getCode() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String getName() {
    return "核销有效期";
  }

  @Override
  public Class<? extends FormProperties> getFormPropertiesInfo() {
    return ExpirationAuditFormProperties.class;
  }

  @Override
  public <T extends DynamicForm> Object handle(String businessCode, T data, Map<String, ?> params) {
    Validate.notBlank(businessCode, "活动编号为空，请检查");
    ActivitiesVo activitiesVo = this.activitiesService.findDetailsByActivitiesCode(businessCode);
    Validate.notNull(activitiesVo, "活动数据错误，请检查");
    ExpirationAuditFormProperties expirationAuditFormProperties = this.findProperties(businessCode);
    Integer auditValidityMonth = expirationAuditFormProperties.getExpiration();
    LocalDateTime activitiesEndTime = LocalDateTime.ofInstant(activitiesVo.getEndTime().toInstant(), ZoneId.systemDefault());
    LocalDateTime validityTime = activitiesEndTime.plusMonths(auditValidityMonth);
    LocalDateTime now = LocalDateTime.now();
    Validate.isTrue(now.isBefore(validityTime), "活动编号【%s】,核销时间已经超过核销有效期", businessCode);
    return null;
  }

  @Override
  public <T extends DynamicForm> boolean match(String businessCode, T data, Map<String, ?> params) {
    ExpirationAuditFormProperties expirationAuditFormProperties = this.findProperties(businessCode);
    if (expirationAuditFormProperties == null || expirationAuditFormProperties.getExpiration() == null) {
      return false;
    }
    return true;
  }
}
