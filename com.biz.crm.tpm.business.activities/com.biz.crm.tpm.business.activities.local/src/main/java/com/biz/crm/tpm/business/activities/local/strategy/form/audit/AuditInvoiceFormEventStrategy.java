package com.biz.crm.tpm.business.activities.local.strategy.form.audit;

import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.tpm.business.activities.local.strategy.form.AbstractFormEventStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.util.Map;

/**
 * 描述：</br>自动核销策略
 *
 * <AUTHOR>
 * @date 2022/11/4
 */
@Component
@Slf4j
public class AuditInvoiceFormEventStrategy extends AbstractFormEventStrategy {

  @Override
  public String getCode() {
    return this.getClass().getSimpleName();
  }

  @Override
  public String getName() {
    return "核销发票";
  }

  @Override
  public Class<? extends FormProperties> getFormPropertiesInfo() {
    return AuditInvoiceFormProperties.class;
  }

  @Override
  @Transactional
  public <T extends DynamicForm> Object handle(String businessCode, T data, Map<String, ?> params) {
    return null;
  }

  @Override
  public <T extends DynamicForm> boolean match(String businessCode, T data, Map<String, ?> params) {
    AuditInvoiceFormProperties auditInvoiceFormProperties = this.findProperties(businessCode);
    if (auditInvoiceFormProperties == null) {
      return false;
    }
    return auditInvoiceFormProperties.isEnabled();
  }
}
