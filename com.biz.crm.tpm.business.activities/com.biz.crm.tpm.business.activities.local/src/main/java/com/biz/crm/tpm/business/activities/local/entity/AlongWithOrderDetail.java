package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@ApiModel(value = "AlongWithOrderDetail", description = "随单比例管控明细")
@TableName("tpm_along_with_order_detail")
@Table(name = "tpm_along_with_order_detail", indexes = {
        @Index(name = "along_with_order_detail_idx1", columnList = "along_with_order_code")})
@Data
@Entity(name = "tpm_along_with_order_detail")
@org.hibernate.annotations.Table(appliesTo = "tpm_along_with_order_detail", comment = "随单比例管控明细")
public class AlongWithOrderDetail extends TenantFlagOpEntity {


    @ApiModelProperty("随单比例编号")
    @Column(name = "along_with_order_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '随单比例编号 '")
    private String alongWithOrderCode;

    @ApiModelProperty("部门编码")
    @Column(name = "department_code", columnDefinition = "varchar(32) comment '部门编码'")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    @Column(name = "department_name", columnDefinition = "varchar(128) comment '部门名称'")
    private String departmentName;

    @ApiModelProperty("部门层级")
    @Column(name = "level_num", columnDefinition = "int COMMENT '部门层级'")
    private Integer levelNum;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
    private String customerName;

    @ApiModelProperty("产品编码")
    @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
    private String productCode;

    @ApiModelProperty("产品名称")
    @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
    private String productName;

    @ApiModelProperty("比例")
    @Column(name = "ratio_str", columnDefinition = "varchar(8) COMMENT '比例'")
    private String ratioStr;

    @ApiModelProperty("比例")
    @Column(name = "ratio", columnDefinition = "decimal(20,6) COMMENT '比例'")
    private BigDecimal ratio;
}
