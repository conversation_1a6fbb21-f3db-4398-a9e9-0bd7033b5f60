package com.biz.crm.tpm.business.activities.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@ApiModel(value = "AlongWithOrder", description = "随单比例管控")
@TableName("tpm_along_with_order")
@Table(name = "tpm_along_with_order", indexes = {
        @Index(name = "along_with_order_idx1", columnList = "along_with_order_code")})
@Data
@Entity(name = "tpm_along_with_order")
@org.hibernate.annotations.Table(appliesTo = "tpm_along_with_order", comment = "随单比例管控")
public class AlongWithOrder extends TenantFlagOpEntity {


    /**
     * 随单比例编号
     */
    @ApiModelProperty("随单比例编号")
    @Column(name = "along_with_order_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '随单比例编号 '")
    private String alongWithOrderCode;

    /**
     * 随单比例名称
     */
    @ApiModelProperty("随单比例名称")
    @Column(name = "along_with_order_name", columnDefinition = "VARCHAR(255) COMMENT '随单比例名称 '")
    private String alongWithOrderName;

    /**
     * 生效开始年月
     */
    @ApiModelProperty("生效开始年月")
    @Column(name = "start_year_month", columnDefinition = "VARCHAR(32) COMMENT '生效开始年月 '")
    private String startYearMonth;

    /**
     * 生效结束年月
     */
    @ApiModelProperty("生效结束年月")
    @Column(name = "end_year_month", columnDefinition = "VARCHAR(32) COMMENT '生效结束年月 '")
    private String endYearMonth;

    @ApiModelProperty("职位编码")
    @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
    private String positionCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", length = 64, columnDefinition = "varchar(64) COMMENT '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
    private String orgName;
}
