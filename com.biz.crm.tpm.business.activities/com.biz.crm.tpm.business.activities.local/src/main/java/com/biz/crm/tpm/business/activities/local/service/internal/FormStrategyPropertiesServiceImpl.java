package com.biz.crm.tpm.business.activities.local.service.internal;

import com.biz.crm.tpm.business.activities.local.entity.FormStrategyProperties;
import com.biz.crm.tpm.business.activities.local.repository.FormStrategyPropertiesRepository;
import com.biz.crm.tpm.business.activities.local.service.FormStrategyPropertiesService;
import com.biz.crm.tpm.business.budget.sdk.dto.FormStrategyPropertiesDto;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;

/**
 * 描述：</br>TPM-表单执行策略;(tpm_occupy_properties)表服务实现类
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@Service
@Slf4j
public class FormStrategyPropertiesServiceImpl implements FormStrategyPropertiesService {
  @Autowired(required = false)
  private FormStrategyPropertiesRepository formStrategyPropertiesRepository;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public FormStrategyProperties findByCodeAndBusinessCode(String code, String businessCode) {
    if (StringUtils.isBlank(businessCode)) {
      return null;
    }
    return this.formStrategyPropertiesRepository.findByCodeAndBusinessCode(code, businessCode, TenantUtils.getTenantCode());
  }

  @Override
  public FormStrategyProperties create(FormStrategyPropertiesDto dto) {
    this.validation(dto);
    FormStrategyProperties current = this.formStrategyPropertiesRepository.findByCodeAndBusinessCode(dto.getCode(), dto.getBusinessCode(), TenantUtils.getTenantCode());
    if (current != null) {
      current.setData(dto.getData());
      current.setTenantCode(TenantUtils.getTenantCode());
      this.formStrategyPropertiesRepository.saveOrUpdate(current);
      return current;
    }
    FormStrategyProperties formStrategyProperties = this.nebulaToolkitService.copyObjectByWhiteList(dto, FormStrategyProperties.class, HashSet.class, ArrayList.class);
    formStrategyProperties.setTenantCode(TenantUtils.getTenantCode());
    this.formStrategyPropertiesRepository.save(formStrategyProperties);
    return formStrategyProperties;
  }

  @Override
  public FormStrategyProperties update(FormStrategyPropertiesDto dto) {
    this.validation(dto);
    FormStrategyProperties current = this.formStrategyPropertiesRepository.findByCodeAndBusinessCode(dto.getCode(), dto.getBusinessCode(), TenantUtils.getTenantCode());
    Validate.notNull(current, "未找到数据，请检查");
    current.setBusinessCode(dto.getData());
    current.setTenantCode(TenantUtils.getTenantCode());
    this.formStrategyPropertiesRepository.saveOrUpdate(current);
    return current;
  }

  @Override
  public void deleteById(String id) {
    if (StringUtils.isBlank(id)) {
      return;
    }
    FormStrategyProperties current = this.formStrategyPropertiesRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (current == null) {
      return;
    }
    this.formStrategyPropertiesRepository.removeById(id);
  }

  /**
   * 保存参数验证
   *
   * @param dto
   */
  private void validation(FormStrategyPropertiesDto dto) {
    Validate.notNull(dto, "保存参数不能为空，请检查");
    Validate.notBlank(dto.getBusinessCode(), "关联业务编号为空，请检查");
    Validate.notBlank(dto.getData(), "json结构数据为空，请检查");
  }
}
