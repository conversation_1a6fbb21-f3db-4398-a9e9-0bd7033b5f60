package com.biz.crm.tpm.business.activities.local.service.internal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.common.form.sdk.DynamicFormFieldMappingService;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.model.OperationStrategy;
import com.biz.crm.common.form.sdk.model.OperationStrategyService;
import com.biz.crm.common.form.sdk.module.ModuleRegister;
import com.biz.crm.common.form.sdk.vo.DynamicFieldVo;
import com.biz.crm.common.form.sdk.vo.DynamicFormVo;
import com.biz.crm.common.form.sdk.widget.WidgetKey;
import com.biz.crm.tpm.business.activities.sdk.dto.DynamicTemplateDto;
import com.biz.crm.tpm.business.activities.sdk.service.DynamicTemplateService;
import com.biz.crm.tpm.business.activities.sdk.vo.DynamicTemplateVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant.DYNAMIC_FORM_FIELD_CODE;

@Service
public class DynamicTemplateServiceImpl implements DynamicTemplateService {

  private static final Map<String, String> CLASS_MAPPING = Maps.newConcurrentMap();

  @Order
  @Autowired(required = false)
  private List<ModuleRegister> moduleRegisters;
  @Order
  @Autowired(required = false)
  private List<OperationStrategy<? extends DynamicForm>> operationStrateis;
  @Autowired
  private OperationStrategyService operationStrategyService;
  @Autowired
  private DynamicFormFieldMappingService dynamicFormFieldMappingService;

  @Override
  public Page<DynamicTemplateVo> findByConditions(Pageable pageable, DynamicTemplateDto dto) {
    if(CollectionUtils.isEmpty(moduleRegisters) || CollectionUtils.isEmpty(operationStrateis)){
      return new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    }
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    if (dto == null) {
      dto = new DynamicTemplateDto();
    }
    //1.====获取模块分组信息
    List<DynamicTemplateVo> templates = Lists.newArrayList();
    for(ModuleRegister moduleRegister : moduleRegisters){
      String moduleCode = moduleRegister.moduleCode();
      String moduleName = moduleRegister.moduleName();
      //2.====获取动态模板策略信息
      for(OperationStrategy<? extends DynamicForm> operationStrategy : operationStrateis) {
        String moduleCodeItem = operationStrategy.moduleCode();
        if(!StringUtils.equals(moduleCode,moduleCodeItem)){
          continue;
        }
        String dynamicFormCode = operationStrategy.dynamicFormCode();
        String dynamicFormName = operationStrategy.dynamicFormName();
        Class<?> dynamicClass = operationStrategy.dynamicFormClass();
        DynamicTemplateVo template = new DynamicTemplateVo();
        template.setModuleCode(moduleCode);
        template.setModuleName(moduleName);
        template.setDynamicFormCode(dynamicFormCode);
        template.setDynamicFormName(dynamicFormName);
        template.setDynamicFormSimpleClass(dynamicClass.getSimpleName());
        templates.add(template);
      }
    }

    if(CollectionUtils.isEmpty(templates)){
      return new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    }

    //3.====根据筛选条件过滤数据
    if(StringUtils.isNotBlank(dto.getModuleCode())){
      String moduleCode = dto.getModuleCode();
      templates = templates.stream().filter(e -> StringUtils.equals(moduleCode,e.getModuleCode())).collect(Collectors.toList());
    }
    if(StringUtils.isNotBlank(dto.getModuleName())){
      String moduleName = dto.getModuleName();
      templates = templates.stream().filter(e -> StringUtils.indexOf(e.getModuleName(),moduleName) >= 0).collect(Collectors.toList());
    }
    if(StringUtils.isNotBlank(dto.getDynamicFormCode())){
      String dynamicFormCode = dto.getDynamicFormCode();
      templates = templates.stream().filter(e -> StringUtils.equals(dynamicFormCode,e.getDynamicFormCode())).collect(Collectors.toList());
    }
    if(StringUtils.isNotBlank(dto.getDynamicFormName())){
      String dynamicFormName = dto.getDynamicFormName();
      templates = templates.stream().filter(e -> StringUtils.indexOf(e.getDynamicFormName(),dynamicFormName) >= 0).collect(Collectors.toList());
    }
    long totalSize = templates.size();
    templates = templates.stream().skip(pageable.getPageSize() * (pageable.getPageNumber() > 0 ? pageable.getPageNumber() - 1 : 0))
            .limit(pageable.getPageSize()).collect(Collectors.toList());
    Page<DynamicTemplateVo> result = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    result.setRecords(templates);
    result.setTotal(totalSize);
    return result;
  }



  @Override
  public JSONObject findByDynamicFormCode(String dynamicFormCode) {
    if(StringUtils.isBlank(dynamicFormCode)){
      return null;
    }
    DynamicFormVo dynamicForm = operationStrategyService.findByDynamicFormCode(dynamicFormCode);
    if(dynamicForm == null){
      return null;
    }
    return this.buildJson(dynamicForm);
  }

  @Override
  public JSONObject findByDynamicFormCodeAndMappingCode(String dynamicFormCode, String mappingCode) {
    if(StringUtils.isAnyBlank(dynamicFormCode , mappingCode)) {
      return null;
    }
    DynamicFormVo dynamicForm = this.dynamicFormFieldMappingService.findByDynamicFormCodeAndMappingCode(dynamicFormCode, mappingCode);
    if(dynamicForm == null){
      return null;
    }
    return this.buildJson(dynamicForm);
  }

  private JSONObject buildJson(DynamicFormVo dynamicForm) {
    JSONObject result = new JSONObject();
    result.put("dynamicFormCode", dynamicForm.getDynamicFormCode());
    result.put("dynamicFormFieldCode",DYNAMIC_FORM_FIELD_CODE);

    JSONArray dynamicFieldJsons = new JSONArray();
    List<DynamicFieldVo> dynamicFields = dynamicForm.getDynamicFields();
    for (DynamicFieldVo dynamicFieldVo : dynamicFields) {
      Class<?> fieldClass = dynamicFieldVo.getFieldClass();
      Boolean array = dynamicFieldVo.getArray();
      Boolean collection = dynamicFieldVo.getCollection();
      String fieldName = dynamicFieldVo.getFieldName();
      String fieldCode = dynamicFieldVo.getFieldCode();
      boolean required = dynamicFieldVo.isRequired();
      boolean modifiable = dynamicFieldVo.isModifiable();
      // 处理controllKey
      WidgetKey controllKey = dynamicFieldVo.getControllKey();
      JSONObject widgetKeyJson = new JSONObject();
      String widgetCode = controllKey.widgetCode();
      String widgetName = controllKey.widgetName();
      Map<String , Object> widgetParam = controllKey.widgetParam();
      widgetKeyJson.put("widgetCode", widgetCode);
      widgetKeyJson.put("widgetName", widgetName);
      widgetKeyJson.put("widgetParam", widgetParam);

      // 开始生成dynamicFieldVo的json
      JSONObject dynamicFieldJson = new JSONObject();
      dynamicFieldJson.put("fieldClass", fieldClass);
      dynamicFieldJson.put("frontClass",this.transferToFrontClass(fieldClass, array, collection));
      dynamicFieldJson.put("array", array);
      dynamicFieldJson.put("fieldName", fieldName);
      dynamicFieldJson.put("fieldCode", fieldCode);
      dynamicFieldJson.put("required", required);
      dynamicFieldJson.put("modifiable", modifiable);
      dynamicFieldJson.put("collection", collection);
      dynamicFieldJson.put("controllKey", widgetKeyJson);
      dynamicFieldJsons.add(dynamicFieldJson);
    }
    result.put("dynamicFields", dynamicFieldJsons);
    return result;
  }


  private String transferToFrontClass(Class<?> fieldClass, Boolean array, Boolean collection){
    String fieldClassStr = fieldClass.getName();
    if(CLASS_MAPPING.containsKey(fieldClassStr)){
      return CLASS_MAPPING.get(fieldClassStr);
    }

    if(fieldClass.isPrimitive()){
      if(fieldClass == boolean.class){
        CLASS_MAPPING.put(fieldClassStr,"boolean");
        return CLASS_MAPPING.get(fieldClassStr);
      }
      if(fieldClass == char.class){
        CLASS_MAPPING.put(fieldClassStr,"string");
        return CLASS_MAPPING.get(fieldClassStr);
      }

      CLASS_MAPPING.put(fieldClassStr,"number");
      return CLASS_MAPPING.get(fieldClassStr);
    }

    if(fieldClass == Boolean.class){
      CLASS_MAPPING.put(fieldClassStr,"boolean");
      return CLASS_MAPPING.get(fieldClassStr);
    }
    if(fieldClass == Character.class){
      CLASS_MAPPING.put(fieldClassStr,"string");
      return CLASS_MAPPING.get(fieldClassStr);
    }

    if(array || collection){
      CLASS_MAPPING.put(fieldClassStr,"array");
      return CLASS_MAPPING.get(fieldClassStr);
    }

    if(fieldClass == String.class){
      CLASS_MAPPING.put(fieldClassStr,"string");
      return CLASS_MAPPING.get(fieldClassStr);
    }

    if(fieldClass == Date.class){
      CLASS_MAPPING.put(fieldClassStr,"date");
      return CLASS_MAPPING.get(fieldClassStr);
    }

    if(Number.class.isAssignableFrom(fieldClass)){
      CLASS_MAPPING.put(fieldClassStr,"number");
      return CLASS_MAPPING.get(fieldClassStr);
    }

    CLASS_MAPPING.put(fieldClassStr,"object");
    return CLASS_MAPPING.get(fieldClassStr);
  }
}
