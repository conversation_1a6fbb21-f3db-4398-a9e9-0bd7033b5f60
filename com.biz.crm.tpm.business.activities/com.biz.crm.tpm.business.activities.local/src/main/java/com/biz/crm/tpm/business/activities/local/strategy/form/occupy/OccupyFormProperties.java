package com.biz.crm.tpm.business.activities.local.strategy.form.occupy;

import com.biz.crm.common.form.sdk.widget.BooleanSelectWidget;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormProperties;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormPropertiesField;
import lombok.Data;

/**
 * 描述：</br>费用预占属性配置项目
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Data
public class OccupyFormProperties implements FormProperties {
  /**
   * 是否启用
   */
  @FormPropertiesField(name = "是否启用预占", required = true, controllKey = BooleanSelectWidget.class)
  private boolean enabled;
}
