package com.biz.crm.tpm.business.activities.local.notifier;

import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesService;
import com.biz.crm.tpm.business.budget.sdk.event.CostTypeCategoryEventListener;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>已审批的活动对活动大类的修改进行监听，有审批活动使用的活动大类不允许修改
 *
 * <AUTHOR>
 * @date 2022/6/30
 */
@Component
public class ActivitiesForCostTypeCategoryEventListener implements CostTypeCategoryEventListener {
  @Autowired
  private ActivitiesService activitiesService;

  @Override
  public void onUpdate(CostTypeCategoryVo oldCostTypeCategoryVo, CostTypeCategoryVo costTypeCategoryVo) {
    String costTypeCategoryCode = oldCostTypeCategoryVo.getCategoryCode();
    boolean existsCostCategory = this.activitiesService.existsByCostCategory(costTypeCategoryCode);
    Validate.isTrue(!existsCostCategory, "活动大类【%s】已被活动使用，无法修改", oldCostTypeCategoryVo.getCategoryName());
  }
}
