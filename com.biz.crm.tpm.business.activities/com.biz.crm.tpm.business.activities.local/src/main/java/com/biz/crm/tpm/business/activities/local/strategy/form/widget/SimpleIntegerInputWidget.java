package com.biz.crm.tpm.business.activities.local.strategy.form.widget;

import com.biz.crm.common.form.sdk.widget.WidgetKey;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 描述：</br>专门进行整数输入的控件
 *
 * <AUTHOR>
 * @date 2022/11/8
 */
@Component
public class SimpleIntegerInputWidget implements WidgetKey {
  @Override
  public String widgetCode() {
    return "simpleIntegerInputWidget";
  }

  @Override
  public String widgetName() {
    return "整数输入框";
  }

  @Override
  public Map<String, Object> widgetParam() {
    return null;
  }
}
