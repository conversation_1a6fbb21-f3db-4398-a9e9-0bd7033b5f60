<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.biz.crm.tpm.business.activities</groupId>
    <artifactId>activities</artifactId>
    <version>202405</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>activities-scheme</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.biz.crm.tpm.business.activities</groupId>
      <artifactId>activities-sdk</artifactId>
      <version>${project.parent.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.tpm.business.activities</groupId>
      <artifactId>activities-dynamic-template</artifactId>
      <version>${project.parent.version}</version>
    </dependency>
    <dependency>
      <groupId>com.biz.crm.common</groupId>
      <artifactId>log-sdk</artifactId>
      <version>${crm.common.version}</version>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
          <include>**/*.properties</include>
        </includes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
  </build>
</project>
