package com.biz.crm.tpm.business.activities.contract.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.base.vo.OssFileVo;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.dto.ProductQueryDto;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductSmallClassVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductItemVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.supplier.sdk.service.MdmSupplierVoService;
import com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo;
import com.biz.crm.tpm.business.activities.contract.dto.ContractCostPageDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDetailDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractProductDto;
import com.biz.crm.tpm.business.activities.contract.entity.*;
import com.biz.crm.tpm.business.activities.contract.eunm.ContractCostExecuteEnum;
import com.biz.crm.tpm.business.activities.contract.eunm.ExternalContractStatusEnum;
import com.biz.crm.tpm.business.activities.contract.repository.*;
import com.biz.crm.tpm.business.activities.contract.service.ExternalContractService;
import com.biz.crm.tpm.business.activities.contract.vo.ContractCostDetailVo;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractDetailVo;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractProductVo;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.activities.stagingscheme.vo.TpmStagingSchemeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.rebate.sdk.service.FormulaConfigVoService;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 16:15
 */
@Service
@Slf4j
public class ExternalContractServiceImpl implements ExternalContractService {

    private static final String CONTRACT_COST_DIMENSION = "contract_cost_dimension";
    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private NebulaNetEventClient nebulaNetEventClient;

    @Resource
    private ExternalContractRepository repository;

    @Resource
    private ExternalContractDetailRepository detailRepository;

    @Resource
    private ExternalContractProductRepository productRepository;

    @Resource
    private ITpmStagingSchemeService tpmStagingSchemeService;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private ProductVoService productVoService;

    @Resource
    private GenerateCodeService generateCodeService;

    @Resource
    private ProductSmallClassVoService productSmallClassVoService;

    @Resource
    private ExternalContractFileRepository fileRepository;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private MdmCostCenterVoService costCenterVoService;

    @Resource
    private ProductPhaseVoService productPhaseVoService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private MarketingPlanService marketingPlanService;

    @Resource
    private MdmSupplierVoService supplierVoService;

    @Resource
    private FormulaConfigVoService formulaConfigVoService;

    private static final String EXTERNAL_CONTRACT = "external_contract";

    private static final String RULE_CODE = "HTMX";

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Resource
    private MarketingPlanCaseService marketingPlanCaseService;


    /**
     * 分页查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    @Override
    public Page<ExternalContractVo> findList(Pageable pageable, ExternalContractDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<ExternalContractDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        dto.setTenantCode(TenantUtils.getTenantCode());
        return repository.findList(page, dto);
    }

    /**
     * 查询详情
     *
     * @param id
     * @param contractCode
     * @param contractStatus
     * @return
     */
    @Override
    public ExternalContractVo queryDetails(String id, String contractCode, String contractStatus) {
        contractStatus = ObjectUtils.defaultIfNull(contractStatus, ExternalContractStatusEnum.normal.getCode());
        ExternalContract contract = repository.queryDetails(id, contractCode, contractStatus);
        if (contract == null) {
            return null;
        }
        ExternalContractVo contractVo = nebulaToolkitService.copyObjectByWhiteList(contract, ExternalContractVo.class, HashSet.class, ArrayList.class);
        if (ExternalContractStatusEnum.stop.getCode().equals(contractVo.getContractStatus())) {
            contractVo = tpmStagingSchemeService.getJsonStr(new TpmStagingSchemeVo() {{
                this.setReleaseId(contract.getId());
                this.setFromType(EXTERNAL_CONTRACT);
            }}, ExternalContractVo.class);
        } else {
            List<ExternalContractDetail> detailList = detailRepository.findDetailListByContractCode(contractVo.getContractCode());
            List<ExternalContractDetailVo> detailVoList = (List<ExternalContractDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(detailList,
                    ExternalContractDetail.class, ExternalContractDetailVo.class, HashSet.class, ArrayList.class);
            List<ExternalContractProduct> productList = productRepository.findProductListByContractCode(contractVo.getContractCode());
            Map<String, List<ExternalContractProduct>> productMap = productList.stream().collect(Collectors.groupingBy(ExternalContractProduct::getContractDetailCode));
            for (ExternalContractDetailVo detailVo : detailVoList) {
                if (productMap.containsKey(detailVo.getContractDetailCode())) {
                    List<ExternalContractProductVo> productVoList = (List<ExternalContractProductVo>) nebulaToolkitService.copyCollectionByWhiteList(productMap.get(detailVo.getContractDetailCode()),
                            ExternalContractProduct.class, ExternalContractProductVo.class, HashSet.class, ArrayList.class);
                    Map<String, List<ExternalContractProductVo>> productTypeMap = productVoList.stream().collect(Collectors.groupingBy(ExternalContractProductVo::getType));
                    for (Map.Entry<String, List<ExternalContractProductVo>> entry : productTypeMap.entrySet()) {
                        String key = entry.getKey();
                        List<ExternalContractProductVo> productVos = entry.getValue();
                        if (OverallPlanProductEnum.cal_item.getCode().equals(key)) {
                            detailVo.setItemList(productVos);
                        } else if (OverallPlanProductEnum.cal_product.getCode().equals(key)) {
                            detailVo.setProductList(productVos);
                        } else if (OverallPlanProductEnum.cal_level.getCode().equals(key)) {
                            detailVo.setLevelList(productVos);
                        } else if (OverallPlanProductEnum.fee_item.getCode().equals(key)) {
                            detailVo.setFeeItemList(productVos);
                        } else if (OverallPlanProductEnum.fee_product.getCode().equals(key)) {
                            detailVo.setFeeProductList(productVos);
                        } else if (OverallPlanProductEnum.fee_level.getCode().equals(key)) {
                            detailVo.setFeeLevelList(productVos);
                        } else if (OverallPlanProductEnum.fee_belong_item.equals(key)) {
                            detailVo.setFeeBelongItemList(productVos);
                        }
                    }
                }
            }
            contractVo.setDetailList(detailVoList);
        }
        return contractVo;
    }


    /**
     * 新增
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createContract(ExternalContractDto dto, String externalFlag) {
        this.validateParam(dto, externalFlag);
        dto.setOnlyKey(UuidCrmUtil.general());
        ExternalContract contract = nebulaToolkitService.copyObjectByWhiteList(dto, ExternalContract.class, HashSet.class, ArrayList.class);
        //查询客户信息
        CustomerVo customerVo;
        if (BooleanEnum.TRUE.getCapital().equals(externalFlag)) {
            //外部合同销售类型的查询客户主数据 反之查询供应商
            if ("6".equals(dto.getContractType()) || "7".equals(dto.getContractType())) {
                customerVo = customerVoService.findByErpCodeAndCompanyCode(contract.getErpCode(), contract.getCompanyCode());
                Validate.notNull(customerVo, String.format("ERP客户编码%s+公司编码%s查询客户信息为空", contract.getErpCode(), contract.getCompanyCode()));
            } else {
                String supplierCode = dto.getErpCode() + dto.getCompanyCode();
                MdmSupplierVo supplierVo = supplierVoService.findDetailsByIdOrCode(null, supplierCode);
                Validate.notNull(supplierVo, String.format("ERP客户编码%s+公司编码%s查询供应商数据信息为空", contract.getErpCode(), contract.getCompanyCode()));
                customerVo = new CustomerVo();
                customerVo.setCustomerCode(supplierVo.getSupplierCode());
                customerVo.setCustomerName(supplierVo.getSupplierName());
                customerVo.setCompanyCode(ObjectUtils.isNotEmpty(supplierVo.getCompanyCode()) ? supplierVo.getCompanyCode() : "");
                customerVo.setChannelCode("");
                customerVo.setProductGroupCode("");
            }
        } else {
            if (StringUtils.isNotBlank(dto.getCustomerCode())) {
                List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(Lists.newArrayList(dto.getCustomerCode()));
                Validate.isTrue(CollectionUtils.isNotEmpty(customerVos), String.format("通过客户编码%s查询客户信息为空", dto.getCustomerName()));
                customerVo = customerVos.get(0);
            } else {
                customerVo = customerVoService.findByErpCodeAndCompanyCode(contract.getErpCode(), contract.getCompanyCode());
                Validate.notNull(customerVo, String.format("ERP客户编码%s+公司编码%s查询客户信息为空", contract.getErpCode(), contract.getCompanyCode()));
            }
        }
        contract.setCustomerCode(customerVo.getCustomerCode());
        contract.setCustomerName(customerVo.getCustomerName());
        contract.setChannelCode(customerVo.getChannelCode());
        contract.setCompanyCode(customerVo.getCompanyCode());
        contract.setProductGroupCode(customerVo.getProductGroupCode());
        contract.setTenantCode(TenantUtils.getTenantCode());
        contract.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        contract.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        contract.setContractStatus(ExternalContractStatusEnum.normal.getCode());
        contract.setExecuteStatus(ContractCostExecuteEnum.UN_EXECUTED.getCode());
        repository.saveOrUpdate(contract);
        List<String> materialOrProductCodeList = Lists.newArrayList();
        Set<String> levelList = Sets.newHashSet();
        List<String> itemList = Lists.newArrayList();
        List<String> orgCodes = Lists.newArrayList();
        List<String> costCenterCodes = Lists.newArrayList();
        for (ExternalContractDetailDto detailDto : dto.getDetailList()) {
            if (ObjectUtils.isNotEmpty(detailDto.getBelongDepartmentCode())) {
                orgCodes.add(detailDto.getBelongDepartmentCode());
            }
            if (ObjectUtils.isNotEmpty(detailDto.getCostCenterCode())) {
                costCenterCodes.add(detailDto.getCostCenterCode());
            }
            if (CollectionUtils.isNotEmpty(detailDto.getProductList())) {
                if (BooleanEnum.FALSE.getCapital().equals(dto.getExternalFlag())) {
                    List<String> productCodes = detailDto.getProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                            .map(ExternalContractProductDto::getCode).distinct().collect(Collectors.toList());
                    materialOrProductCodeList.addAll(productCodes);
                } else {
                    List<String> materialCodes = detailDto.getProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                            .map(ExternalContractProductDto::getMaterialCode).distinct().collect(Collectors.toList());
                    materialOrProductCodeList.addAll(materialCodes);
                }
            }
            if (CollectionUtils.isNotEmpty(detailDto.getFeeProductList())) {
                if (BooleanEnum.FALSE.getCapital().equals(dto.getExternalFlag())) {
                    List<String> productCodes = detailDto.getFeeProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                            .map(ExternalContractProductDto::getCode).distinct().collect(Collectors.toList());
                    materialOrProductCodeList.addAll(productCodes);
                } else {
                    List<String> materialCodes = detailDto.getFeeProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                            .map(ExternalContractProductDto::getMaterialCode).distinct().collect(Collectors.toList());
                    materialOrProductCodeList.addAll(materialCodes);
                }
            }
            if (CollectionUtils.isNotEmpty(detailDto.getLevelList())) {
                List<String> levels = detailDto.getLevelList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(ExternalContractProductDto::getCode).distinct().collect(Collectors.toList());
                levelList.addAll(levels);
            }
            if (CollectionUtils.isNotEmpty(detailDto.getFeeLevelList())) {
                List<String> levels = detailDto.getFeeLevelList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(ExternalContractProductDto::getCode).distinct().collect(Collectors.toList());
                levelList.addAll(levels);
            }
            if (CollectionUtils.isNotEmpty(detailDto.getItemList())) {
                List<String> items = detailDto.getItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(ExternalContractProductDto::getCode).distinct().collect(Collectors.toList());
                itemList.addAll(items);
            }
            if (CollectionUtils.isNotEmpty(detailDto.getFeeItemList())) {
                List<String> items = detailDto.getFeeItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(ExternalContractProductDto::getCode).distinct().collect(Collectors.toList());
                itemList.addAll(items);
            }
            if (CollectionUtils.isNotEmpty(detailDto.getFeeBelongItemList())) {
                List<String> items = detailDto.getFeeBelongItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(ExternalContractProductDto::getCode).distinct().collect(Collectors.toList());
                itemList.addAll(items);
            }
        }
        //产品产品信息
        List<ProductVo> productVoList = Lists.newArrayList();
        if (BooleanEnum.TRUE.getCapital().equals(productVoList)) {
            productVoList = productVoService.findByMaterialCodes(materialOrProductCodeList);
        } else {
            productVoList = productVoService.findByProductQueryDto(new ProductQueryDto() {{
                this.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                this.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                this.setTenantCode(TenantUtils.getTenantCode());
                this.setProductCodeList(materialOrProductCodeList);
            }});
        }
        Map<String, String> smallClassMap = productSmallClassVoService.findNameByCodes(levelList);
        List<ProductItemVo> itemCodeList = productVoService.findItemListByItemCodes(itemList);
        List<ProductPhaseVo> levelSapVoList = productPhaseVoService.findByCodes(Sets.newHashSet(itemList));
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(costCenterCodes);
        Map<String, ProductVo> productMap = Maps.newHashMap();
        if (BooleanEnum.TRUE.getCapital().equals(dto.getExternalFlag())) {
            productMap = productVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                    .collect(Collectors.toMap(ProductVo::getMaterialCode, Function.identity(), (a, b) -> a));
        } else {
            productMap = productVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductCode()))
                    .collect(Collectors.toMap(ProductVo::getProductCode, Function.identity(), (a, b) -> a));
        }
        Map<String, String> itemMap = itemCodeList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getItemCode()))
                .collect(Collectors.toMap(ProductItemVo::getItemCode, ProductItemVo::getItemName));
        Map<String, OrgVo> orgMap = Maps.newHashMap();
        Map<String, MdmCostCenterVo> costCenterVoMap = Maps.newHashMap();
        Map<String, String> sapLevelMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orgVoList)) {
            orgMap = orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, Function.identity()));
        }
        if (CollectionUtils.isNotEmpty(costCenterVoList)) {
            costCenterVoMap = costCenterVoList.stream().collect(Collectors.toMap(MdmCostCenterVo::getCostCenterCode, Function.identity()));
        }
        if (CollectionUtils.isNotEmpty(levelSapVoList)) {
            sapLevelMap = levelSapVoList.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, ProductPhaseVo::getProductPhaseName));
        }
        List<String> ruleCodes = generateCodeService.generateCode(RULE_CODE, dto.getDetailList().size());
        List<ExternalContractProduct> productList = Lists.newArrayList();
        Integer index = 0;
        for (ExternalContractDetailDto detail : dto.getDetailList()) {
            detail.setContractCode(dto.getContractCode());
            detail.setOnlyKey(dto.getOnlyKey());
            detail.setContractDetailCode(ruleCodes.get(index++));
            Validate.notNull(detail.getBelongDepartmentCode(), "费用使用部门为空");
            Validate.isTrue(orgMap.containsKey(detail.getBelongDepartmentCode()), String.format("费用使用部门编码%s查询主数据为空", detail.getBelongDepartmentCode()));
            OrgVo orgVo = orgMap.get(detail.getBelongDepartmentCode());
            detail.setBelongDepartmentCode(orgVo.getOrgCode());
            detail.setBelongDepartmentName(orgVo.getOrgName());
            if (BooleanEnum.FALSE.getCapital().equals(externalFlag)) {
                Validate.isTrue(costCenterVoMap.containsKey(detail.getCostCenterCode()), String.format("成本中心编码%s查询主数据为空", detail.getCostCenterCode()));
                MdmCostCenterVo costCenterVo = costCenterVoMap.get(detail.getCostCenterCode());
                detail.setCostCenterCode(costCenterVo.getCostCenterCode());
                detail.setCostCenterName(costCenterVo.getCostCenterName());
            }
            //核算产品
            if (CollectionUtils.isNotEmpty(detail.getProductList())) {
                for (ExternalContractProductDto p : detail.getProductList()) {
                    ProductVo productVo = null;
                    if (BooleanEnum.TRUE.getCapital().equals(dto.getExternalFlag())) {
                        Validate.isTrue(productMap.containsKey(p.getMaterialCode()), String.format("未查询到物料信息%s", p.getMaterialCode()));
                        productVo = productMap.get(p.getMaterialCode());
                    } else {
                        Validate.isTrue(productMap.containsKey(p.getCode()), String.format("未查询到商品信息%s", p.getCode()));
                        productVo = productMap.get(p.getCode());
                    }
                    ExternalContractProduct product = nebulaToolkitService.copyObjectByWhiteList(p, ExternalContractProduct.class, HashSet.class, ArrayList.class);
                    product.setCode(productVo.getProductCode());
                    product.setName(productVo.getProductName());
                    product.setType(OverallPlanProductEnum.cal_product.getCode());
                    product.setContractCode(dto.getContractCode());
                    product.setOnlyKey(dto.getOnlyKey());
                    product.setContractDetailCode(detail.getContractDetailCode());
                    productList.add(product);
                }
            }
            //费用产品
            if (CollectionUtils.isNotEmpty(detail.getFeeProductList())) {
                for (ExternalContractProductDto p : detail.getFeeProductList()) {
                    ProductVo productVo = null;
                    if (BooleanEnum.TRUE.getCapital().equals(dto.getExternalFlag())) {
                        Validate.isTrue(productMap.containsKey(p.getMaterialCode()), String.format("未查询到物料信息%s", p.getMaterialCode()));
                        productVo = productMap.get(p.getMaterialCode());
                    } else {
                        Validate.isTrue(productMap.containsKey(p.getCode()), String.format("未查询到商品信息%s", p.getCode()));
                        productVo = productMap.get(p.getCode());
                    }
                    ExternalContractProduct product = nebulaToolkitService.copyObjectByWhiteList(p, ExternalContractProduct.class, HashSet.class, ArrayList.class);
                    product.setCode(productVo.getProductCode());
                    product.setName(productVo.getProductName());
                    product.setType(OverallPlanProductEnum.fee_product.getCode());
                    product.setContractCode(dto.getContractCode());
                    product.setOnlyKey(dto.getOnlyKey());
                    product.setContractDetailCode(detail.getContractDetailCode());
                    productList.add(product);
                }
            }
            //核算产品小类
            if (CollectionUtils.isNotEmpty(detail.getLevelList())) {
                for (ExternalContractProductDto p : detail.getLevelList()) {
                    Validate.isTrue(smallClassMap.containsKey(p.getCode()), String.format("未查询到产品小类%s", p.getCode()));
                    ExternalContractProduct product = nebulaToolkitService.copyObjectByWhiteList(p, ExternalContractProduct.class, HashSet.class, ArrayList.class);
                    product.setType(OverallPlanProductEnum.cal_level.getCode());
                    product.setName(smallClassMap.get(p.getCode()));
                    product.setContractCode(dto.getContractCode());
                    product.setOnlyKey(dto.getOnlyKey());
                    product.setContractDetailCode(detail.getContractDetailCode());
                    productList.add(product);
                }
            }
            //费用产品小类
            if (CollectionUtils.isNotEmpty(detail.getFeeLevelList())) {
                for (ExternalContractProductDto p : detail.getFeeLevelList()) {
                    Validate.isTrue(smallClassMap.containsKey(p.getCode()), String.format("未查询到产品小类%s", p.getCode()));
                    ExternalContractProduct product = nebulaToolkitService.copyObjectByWhiteList(p, ExternalContractProduct.class, HashSet.class, ArrayList.class);
                    product.setType(OverallPlanProductEnum.fee_level.getCode());
                    product.setName(smallClassMap.get(p.getCode()));
                    product.setContractCode(dto.getContractCode());
                    product.setOnlyKey(dto.getOnlyKey());
                    product.setContractDetailCode(detail.getContractDetailCode());
                    productList.add(product);
                }
            }
            //核算品项
            if (CollectionUtils.isNotEmpty(detail.getItemList())) {
                for (ExternalContractProductDto p : detail.getItemList()) {
                    Validate.isTrue(sapLevelMap.containsKey(p.getCode()), String.format("未查询到品项%s", p.getCode()));
                    ExternalContractProduct product = nebulaToolkitService.copyObjectByWhiteList(p, ExternalContractProduct.class, HashSet.class, ArrayList.class);
                    product.setType(OverallPlanProductEnum.cal_item.getCode());
                    product.setName(sapLevelMap.get(p.getCode()));
                    product.setContractCode(dto.getContractCode());
                    product.setOnlyKey(dto.getOnlyKey());
                    product.setContractDetailCode(detail.getContractDetailCode());
                    productList.add(product);
                }
            }
            //费用品项
            if (CollectionUtils.isNotEmpty(detail.getFeeItemList())) {
                for (ExternalContractProductDto p : detail.getFeeItemList()) {
                    Validate.isTrue(sapLevelMap.containsKey(p.getCode()), String.format("未查询到品项%s", p.getCode()));
                    ExternalContractProduct product = nebulaToolkitService.copyObjectByWhiteList(p, ExternalContractProduct.class, HashSet.class, ArrayList.class);
                    product.setType(OverallPlanProductEnum.fee_item.getCode());
                    product.setName(sapLevelMap.get(p.getCode()));
                    product.setContractCode(dto.getContractCode());
                    product.setOnlyKey(dto.getOnlyKey());
                    product.setContractDetailCode(detail.getContractDetailCode());
                    productList.add(product);
                }
            }
            //费用归属品项
            if (CollectionUtils.isNotEmpty(detail.getFeeBelongItemList())) {
                for (ExternalContractProductDto p : detail.getFeeBelongItemList()) {
                    Validate.isTrue(sapLevelMap.containsKey(p.getCode()), String.format("未查询到品项%s", p.getCode()));
                    ExternalContractProduct product = nebulaToolkitService.copyObjectByWhiteList(p, ExternalContractProduct.class, HashSet.class, ArrayList.class);
                    product.setType(OverallPlanProductEnum.fee_belong_item.getCode());
                    product.setName(sapLevelMap.get(p.getCode()));
                    product.setContractCode(dto.getContractCode());
                    product.setOnlyKey(dto.getOnlyKey());
                    product.setContractDetailCode(detail.getContractDetailCode());
                    productList.add(product);
                }
            }
        }
        List<ExternalContractDetail> detailList = (List<ExternalContractDetail>) nebulaToolkitService.copyCollectionByWhiteList(dto.getDetailList(),
                ExternalContractDetailDto.class, ExternalContractDetail.class, HashSet.class, ArrayList.class);
        detailList.forEach(x -> x.setEnableStatus(EnableStatusEnum.ENABLE.getCode()));
        detailRepository.saveBatch(detailList);
        productRepository.saveBatch(productList);
        if (CollectionUtils.isNotEmpty(dto.getFileUrlList())) {
            fileRepository.saveBatchList(dto.getFileUrlList(), contract.getContractCode());
        }
    }

    /**
     * 停用
     *
     * @param contractCode
     */
    @Override
    public void stopContract(String contractCode) {
        ExternalContractVo contractVo = queryDetails(null, contractCode, ExternalContractStatusEnum.normal.getCode());
        if (contractVo != null) {
            String jsonStr = JSONObject.toJSONString(contractVo);
            repository.updateContractStatus(contractVo.getContractCode(), TenantUtils.getTenantCode());
            productRepository.deleteByContractCode(contractVo.getContractCode());
            detailRepository.deleteByContractCode(contractVo.getContractCode());
            fileRepository.deleteByContractCodes(Lists.newArrayList(contractCode));
            tpmStagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
                this.setReleaseId(contractVo.getId());
                this.setFromType(EXTERNAL_CONTRACT);
                this.setJsonStr(jsonStr);
            }});
        }
    }

    /**
     * 上传合同文件
     *
     * @param contractCode
     * @param fileCode
     */
    @Override
    public void uploadContractFile(String contractCode, String fileCode) {
        ExternalContractVo vo = queryDetails(null, contractCode, ExternalContractStatusEnum.normal.getCode());
        Validate.notNull(vo, "合同信息不存在");
        repository.updateContractFile(contractCode, fileCode);
    }

    /**
     * 查询合同费用列表
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<ContractCostPageDto> findList(Pageable pageable, ContractCostPageDto vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<ContractCostPageDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<ContractCostPageDto> data = repository.findContractCostList(page, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            Set<String> contractCodes = data.getRecords().stream().map(ContractCostPageDto::getContractCode).collect(Collectors.toSet());
            Map<String, List<OssFileVo>> fileMap = Maps.newHashMap();
            Map<String, List<ExternalContractProduct>> productMap = Maps.newHashMap();
            List<ExternalContractProduct> products = productRepository.findProductListByContractCodeSet(contractCodes);
            if (BooleanEnum.TRUE.getCapital().equals(vo.getExternalFlag())) {
                List<ExternalContractFile> contractFiles = fileRepository.findListByContractCodes(Lists.newArrayList(contractCodes));
                if (CollectionUtils.isNotEmpty(contractFiles)) {
                    fileMap = contractFiles.stream().collect(Collectors.groupingBy(ExternalContractFile::getContractCode,
                            Collectors.mapping(x -> new OssFileVo() {{
                                this.setFileName(x.getFileName());
                                this.setFileUrl(x.getUrl());
                            }}, Collectors.toList())));
                }
            }
            productMap = products.stream().collect(Collectors.groupingBy(ExternalContractProduct::getOnlyKey));

            for (ContractCostPageDto record : data.getRecords()) {
                if (fileMap.containsKey(record.getContractCode())) {
                    record.setFileList(fileMap.get(record.getContractCode()));
                }
                if (productMap.containsKey(record.getOnlyKey())) {
                    List<ExternalContractProduct> list = productMap.get(record.getOnlyKey());
                    Map<String, List<ExternalContractProduct>> typeMap = list.stream().collect(Collectors.groupingBy(ExternalContractProduct::getType));
                    for (Map.Entry<String, List<ExternalContractProduct>> entry : typeMap.entrySet()) {
                        OverallPlanProductEnum productEnum = OverallPlanProductEnum.getEnum(entry.getKey());
                        String codeStr = entry.getValue().stream().map(ExternalContractProduct::getCode).collect(Collectors.joining(","));
                        String nameStr = entry.getValue().stream().map(ExternalContractProduct::getName).collect(Collectors.joining(","));
                        if (ObjectUtils.isNotEmpty(productEnum)) {
                            switch (productEnum) {
                                case cal_item:
                                    record.setItemCodeStr(codeStr);
                                    record.setItemStr(nameStr);
                                    break;
                                case cal_product:
                                    record.setProductCodeStr(codeStr);
                                    record.setProductStr(nameStr);
                                    break;
                                case cal_level:
                                    record.setLevelCodeStr(codeStr);
                                    record.setLevelStr(nameStr);
                                    break;
                                case fee_item:
                                    record.setFeeItemCodeStr(codeStr);
                                    record.setFeeItemStr(nameStr);
                                    break;
                                case fee_product:
                                    record.setFeeProductCodeStr(codeStr);
                                    record.setFeeProductStr(nameStr);
                                    break;
                                case fee_level:
                                    record.setFeeLevelCodeStr(codeStr);
                                    record.setFeeLevelStr(nameStr);
                                    break;
                                case fee_belong_item:
                                    record.setFeeBelongItemCodeStr(codeStr);
                                    record.setFeeBelongItemStr(nameStr);
                                    break;
                            }
                        }
                    }
                }
            }
        }
        return data;
    }

    @Override
    public Page<ContractCostPageDto> findExternalContractList(Pageable pageable, ContractCostPageDto vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<ContractCostPageDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<ContractCostPageDto> data = repository.findExternalContractList(page, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            Set<String> contractCodes = data.getRecords().stream().map(ContractCostPageDto::getContractCode).collect(Collectors.toSet());
            Map<String, List<OssFileVo>> fileMap = Maps.newHashMap();
            if (BooleanEnum.TRUE.getCapital().equals(vo.getExternalFlag())) {
                List<ExternalContractFile> contractFiles = fileRepository.findListByContractCodes(Lists.newArrayList(contractCodes));
                if (CollectionUtils.isNotEmpty(contractFiles)) {
                    fileMap = contractFiles.stream().collect(Collectors.groupingBy(ExternalContractFile::getContractCode,
                            Collectors.mapping(x -> new OssFileVo() {{
                                this.setFileName(x.getFileName());
                                this.setFileUrl(x.getUrl());
                            }}, Collectors.toList())));
                }
            }
            for (ContractCostPageDto record : data.getRecords()) {
                if (fileMap.containsKey(record.getContractCode())) {
                    record.setFileList(fileMap.get(record.getContractCode()));
                }
            }
        }
        return data;
    }

    /**
     * 只查询不是外部合同 并且未执行状态的
     */
    @Override
    public List<ContractCostDetailVo> findUnExecutedAndNotExternalListByContractCodes(String years) {
        List<ContractCostDetailVo> contracDetailtList = repository.findUnExecutedAndNotExternal(years);
        if (CollectionUtils.isEmpty(contracDetailtList)) {
            return Lists.newArrayList();
        }
        Set<String> onlyKeys = contracDetailtList.stream().map(ContractCostDetailVo::getOnlyKey).collect(Collectors.toSet());
        List<ExternalContractProduct> productList = productRepository.findProductListByOnlyKeySet(onlyKeys);
        Map<String, List<ExternalContractProduct>> productMap = productList.stream().collect(Collectors.groupingBy(ExternalContractProduct::getContractDetailCode));
        //先组装明细
        for (ContractCostDetailVo detailVo : contracDetailtList) {
            if (productMap.containsKey(detailVo.getContractDetailCode())) {
                List<ExternalContractProductVo> productVoList = (List<ExternalContractProductVo>) nebulaToolkitService.copyCollectionByWhiteList(productMap.get(detailVo.getContractDetailCode()),
                        ExternalContractProduct.class, ExternalContractProductVo.class, HashSet.class, ArrayList.class);
                Map<String, List<ExternalContractProductVo>> productTypeMap = productVoList.stream().collect(Collectors.groupingBy(ExternalContractProductVo::getType));
                for (Map.Entry<String, List<ExternalContractProductVo>> entry : productTypeMap.entrySet()) {
                    String key = entry.getKey();
                    List<ExternalContractProductVo> productVos = entry.getValue();
                    if (OverallPlanProductEnum.cal_item.getCode().equals(key)) {
                        detailVo.setItemList(productVos);
                    } else if (OverallPlanProductEnum.cal_product.getCode().equals(key)) {
                        detailVo.setProductList(productVos);
                    } else if (OverallPlanProductEnum.cal_level.getCode().equals(key)) {
                        detailVo.setLevelList(productVos);
                    } else if (OverallPlanProductEnum.fee_item.getCode().equals(key)) {
                        detailVo.setFeeItemList(productVos);
                    } else if (OverallPlanProductEnum.fee_product.getCode().equals(key)) {
                        detailVo.setFeeProductList(productVos);
                    } else if (OverallPlanProductEnum.fee_level.getCode().equals(key)) {
                        detailVo.setFeeLevelList(productVos);
                    } else if (OverallPlanProductEnum.fee_belong_item.getCode().equals(key)) {
                        detailVo.setFeeBelongItemList(productVos);
                    }
                }
            }
        }

        return contracDetailtList;
    }

    @Resource
    private ExternalContractUsedLogRepository externalContractUsedLogRepository;

    /**
     * 生成合同营销方案
     *
     * @param years
     */
    @Override
    public void generateContractMarketing(String years) {
        LocalDate now = LocalDate.parse(years + "-01", DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        // 当月第一天
        LocalDate firstDayOfMonth = now.with(TemporalAdjusters.firstDayOfMonth());
        // 当最后一天
        LocalDate lastDayOfMonth = now.with(TemporalAdjusters.lastDayOfMonth());

        List<ContractCostDetailVo> contractDetailList = this.findUnExecutedAndNotExternalListByContractCodes(years);
        if (CollectionUtils.isEmpty(contractDetailList)) {
            return;
        }
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(CONTRACT_COST_DIMENSION);
        if (CollectionUtils.isEmpty(dictDataVos)) {
            return;
        }
        DictDataVo dictDataVo = dictDataVos.get(0);
        List<OrgVo> orgVos = orgVoService.findByOrgType(dictDataVo.getDictCode());
        if (CollectionUtils.isEmpty(orgVos)) {
            return;
        }
        Map<String, String> regionOrgMap = orgVos.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllChildrenByOrgCodesMap(Lists.newArrayList(regionOrgMap.keySet()));

        for (Map.Entry<String, List<OrgVo>> orgEntry : orgMap.entrySet()) {
            if (!CollectionUtils.isEmpty(orgEntry.getValue())) {
                List<String> orgCodes = orgEntry.getValue().stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                String orgCode = orgEntry.getKey();
                String orgName = regionOrgMap.get(orgCode);
                Map<String, List<ContractCostDetailVo>> detailListMap = Maps.newHashMap();
                for (ContractCostDetailVo contractVo : contractDetailList) {
                    if (orgCodes.contains(contractVo.getBelongDepartmentCode())) {
                        List<ContractCostDetailVo> list = detailListMap.getOrDefault(contractVo.getCustomerCode(), Lists.newArrayList());
                        list.add(contractVo);
                        detailListMap.put(contractVo.getCustomerCode(), list);
                    }
                }
                //按照区域+客户的汇总来生成营销规划方案
                for (Map.Entry<String, List<ContractCostDetailVo>> entry : detailListMap.entrySet()) {
                    List<String> contractCodes = entry.getValue().stream().map(ContractCostDetailVo::getOnlyKey).distinct().collect(Collectors.toList());
                    try {
                        MarketingPlanVo vo = this.conversionParam(entry.getValue(), firstDayOfMonth, lastDayOfMonth, orgCode, orgName, years);
                        Map<String, String> errMsg = marketingPlanService.createMarketingContract(vo, contractCodes);
                        String executeStatus = ContractCostExecuteEnum.EXECUTE_ERROR.getCode();
                        if (ObjectUtils.isEmpty(errMsg)) {
                            errMsg = Maps.newHashMap();
                            executeStatus = ContractCostExecuteEnum.EXECUTED.getCode();
                            //执行保存
                            List<ExternalContractUsedLog> usedLogList = contractCodes.stream().map(x -> {
                                ExternalContractUsedLog usedLog = new ExternalContractUsedLog();
                                usedLog.setIndexKey(x);
                                usedLog.setYears(years);
                                return usedLog;
                            }).collect(Collectors.toList());
                            externalContractUsedLogRepository.saveBatchList(usedLogList);
                        }
                        for (String s : contractCodes) {
                            //修改合同状态
                            repository.updateContractExecuteStatus(Lists.newArrayList(s), executeStatus, errMsg.getOrDefault(s, ""));
                        }
                    } catch (Exception e) {
                        log.error("创建合同营销方案失败:{}", e.getMessage());
                        repository.updateContractExecuteStatus(contractCodes, ContractCostExecuteEnum.EXECUTE_ERROR.getCode(), e.getMessage());
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> ids) {
        List<ExternalContractDetail> detailList = this.detailRepository.findByIds(ids);
        Validate.notEmpty(detailList, "数据不存在或已被删除");
        // 查询头，头和明细一一对应的
        List<String> onlyKeys = detailList.stream().map(ExternalContractDetail::getOnlyKey).collect(Collectors.toList());
        Validate.notEmpty(onlyKeys, "数据异常，请联系管理员");
        List<ExternalContract> list = this.repository.findByOnlyKeys(onlyKeys);
        list = list.stream().filter(v -> BooleanEnum.TRUE.getCapital().equals(v.getExternalFlag())
                || ContractCostExecuteEnum.EXECUTED.getCode().equals(v.getExecuteStatus())).collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isEmpty(list), "外部合同或已生成营销方案规划的合同台账不允许删除");
        Validate.isTrue(!this.marketingPlanCaseService.findExistByContractCodes(list.stream().map(ExternalContract::getContractCode)
                .collect(Collectors.toList())), "已生成营销方案规划的合同台账不允许删除!");
        this.repository.removeByOnlyKeys(onlyKeys);
        this.fileRepository.removeByOnlyKeys(onlyKeys);
        this.detailRepository.removeByOnlyKeys(onlyKeys);
        this.productRepository.removeByOnlyKeys(onlyKeys);

    }

    /**
     * 启用
     *
     * @param ids
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enable(List<String> ids) {
        List<ExternalContractDetail> detailList = this.detailRepository.findByIds(ids);
        Validate.notEmpty(detailList, "数据不存在或已被删除");
        // 查询头，头和明细一一对应的
        List<String> onlyKeys = detailList.stream().map(ExternalContractDetail::getOnlyKey).collect(Collectors.toList());
        Validate.notEmpty(onlyKeys, "数据异常，请联系管理员");
        List<ExternalContract> list = this.repository.findByOnlyKeys(onlyKeys);
        list = list.stream().filter(v -> BooleanEnum.TRUE.getCapital().equals(v.getExternalFlag())
                || ContractCostExecuteEnum.EXECUTED.getCode().equals(v.getExecuteStatus())).collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isEmpty(list), "外部合同或已生成营销方案规划的合同台账不允许修改启禁用状态");
        Validate.isTrue(!this.marketingPlanCaseService.findExistByContractCodes(list.stream().map(ExternalContract::getContractCode)
                .collect(Collectors.toList())), "已生成营销方案规划的合同台账不允许修改启禁用状态!");

        this.detailRepository.updateEnableStatusByIds(EnableStatusEnum.ENABLE, ids);
    }

    /**
     * 禁用
     *
     * @param ids
     */
    @Override
    public void disable(List<String> ids) {
        List<ExternalContractDetail> detailList = this.detailRepository.findByIds(ids);
        Validate.notEmpty(detailList, "数据不存在或已被删除");
        // 查询头，头和明细一一对应的
        List<String> onlyKeys = detailList.stream().map(ExternalContractDetail::getOnlyKey).collect(Collectors.toList());
        Validate.notEmpty(onlyKeys, "数据异常，请联系管理员");
        List<ExternalContract> list = this.repository.findByOnlyKeys(onlyKeys);
        list = list.stream().filter(v -> BooleanEnum.TRUE.getCapital().equals(v.getExternalFlag())
                || ContractCostExecuteEnum.EXECUTED.getCode().equals(v.getExecuteStatus())).collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isEmpty(list), "外部合同或已生成营销方案规划的合同台账不允许修改启禁用状态");
        Validate.isTrue(!this.marketingPlanCaseService.findExistByContractCodes(list.stream().map(ExternalContract::getContractCode)
                .collect(Collectors.toList())), "已生成营销方案规划的合同台账不允许修改启禁用状态!");

        this.detailRepository.updateEnableStatusByIds(EnableStatusEnum.DISABLE, ids);
    }

    @Override
    public Page<ExternalContractVo> findByConditions(Pageable pageable, ContractCostPageDto vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 15));
        vo = ObjectUtils.defaultIfNull(vo, new ContractCostPageDto());
        vo.setTenantCode(TenantUtils.getTenantCode());
        vo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        return this.repository.findByConditions(pageable, vo);
    }


    private static final String QUANTITY_RELATE_CODE = "quantity_relate";

    private static final String GIFT_CODE = "gift";

    private static final String FIXED_CODE = "fixed";

    /**
     * 参数转换
     *
     * @param list
     * @return
     */
    private MarketingPlanVo conversionParam(List<ContractCostDetailVo> list, LocalDate startDate, LocalDate endDate, String orgCode, String orgName, String years) {
        MarketingPlanVo vo = new MarketingPlanVo();
        vo.setDepartmentCode(orgCode);
        vo.setDepartmentName(orgName);
        vo.setYears(years);
        vo.setSchemeType(MarketingPlanSchemeTypeEnum.contract.getCode());
        String customerName = list.get(0).getCustomerName();
        String schemeName = orgName + customerName + years + "月合同费用";
        vo.setSchemeName(schemeName);
        vo.setSchemeTheme(schemeName);
        vo.setSchemeDesc(schemeName);
        List<String> detailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDetailCode()))
                .map(ContractCostDetailVo::getDetailCode).distinct().collect(Collectors.toList());
        Map<String, CostTypeDetailVo> detailVoMap = checkHelper.findDetailMap(detailCodes);
        Map<String, List<ContractCostDetailVo>> caseTypeMap = list.stream().collect(Collectors.groupingBy(ContractCostDetailVo::getCaseType));
        Map<String, List<MarketingPlanCaseVo>> detailCaseMap = Maps.newHashMap();
        for (Map.Entry<String, List<ContractCostDetailVo>> entry : caseTypeMap.entrySet()) {
            if (QUANTITY_RELATE_CODE.equals(entry.getKey())) {
                List<MarketingPlanCaseVo> caseVoList = this.conversionRebate(entry.getValue(), detailVoMap, startDate, endDate, years);
                detailCaseMap.put(MarketingPlanCaseTypeEnum.back.getCode(), caseVoList);
            } else if (GIFT_CODE.equals(entry.getKey())) {
                List<MarketingPlanCaseVo> caseVoList = this.conversionPolicy(entry.getValue(), detailVoMap, startDate, endDate, years);
                detailCaseMap.put(MarketingPlanCaseTypeEnum.matching_gift.getCode(), caseVoList);
            } else if (FIXED_CODE.equals(entry.getKey())) {
                List<MarketingPlanCaseVo> caseVoList = this.conversionFixed(entry.getValue(), detailVoMap, startDate, endDate, years);
                detailCaseMap.put(MarketingPlanCaseTypeEnum.fixed.getCode(), caseVoList);
            }
        }
        vo.setDetailCaseMap(detailCaseMap);
        return vo;
    }

    /**
     * 转换返利
     *
     * @param list
     * @return
     */
    private List<MarketingPlanCaseVo> conversionRebate(List<ContractCostDetailVo> list, Map<String, CostTypeDetailVo> detailVoMap, LocalDate actStartDate, LocalDate actEndDate, String years) {
        List<MarketingPlanCaseVo> caseVoList = list.stream().map(x -> {
            MarketingPlanCaseVo vo = nebulaToolkitService.copyObjectByWhiteList(x, MarketingPlanCaseVo.class, HashSet.class, ArrayList.class,
                    "productList", "itemList", "levelList", "feeProductList", "feeItemList", "feeLevelList", "feeBelongItemList");
            Validate.isTrue(detailVoMap.containsKey(x.getDetailCode()), "活动细类不存在");
            CostTypeDetailVo detailVo = detailVoMap.get(x.getDetailCode());
            vo.setCategoryCode(detailVo.getCategoryCode());
            vo.setCategoryName(detailVo.getCategoryName());
            vo.setCaseType(MarketingPlanCaseTypeEnum.back.getCode());
            vo.setBearDepartmentCode(x.getBelongDepartmentCode());
            vo.setBearDepartmentName(x.getBelongDepartmentName());
            vo.setRebateStartDate(x.getStartDate());
            vo.setRebateEndDate(x.getEndDate());
            vo.setActDesc(x.getRemark());
            vo.setConditionNum(x.getResultFormula());
            vo.setGiveNum(x.getRebateStandard());
            vo.setRebateCalDay(ObjectUtils.defaultIfNull(vo.getRebateCalDay(), 3));
            vo.setIsContractCost(BooleanEnum.TRUE.getCapital());
            vo.setOnlyKeys(x.getOnlyKey());
            vo.setContractCode(x.getContractCode());
            vo.setActName(x.getDetailName());
            vo.setCostBasis("purchase");
            vo.setCostBasisList(Lists.newArrayList("purchase"));
            vo.setActDesc("合同费用");
            vo.setProductList(Lists.newArrayList());
            vo.setItemList(Lists.newArrayList());
            LocalDate startDate = LocalDate.parse(x.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate endDate = LocalDate.parse(x.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            String startDateStr = actStartDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            String endDateStr = actEndDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            if (actStartDate.isBefore(startDate)) {
                startDateStr = startDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            }
            if (actEndDate.isAfter(endDate)) {
                endDateStr = endDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            }
            vo.setStartDate(startDateStr);
            vo.setEndDate(endDateStr);
            vo.setYears(years);
            return vo;
        }).collect(Collectors.toList());
        return caseVoList;
    }


    /**
     * 转换政策
     *
     * @param list
     * @return
     */
    private List<MarketingPlanCaseVo> conversionPolicy(List<ContractCostDetailVo> list, Map<String, CostTypeDetailVo> detailVoMap, LocalDate actStartDate, LocalDate actEndDate, String years) {
        List<MarketingPlanCaseVo> caseVoList = list.stream().map(x -> {
            MarketingPlanCaseVo vo = nebulaToolkitService.copyObjectByWhiteList(x, MarketingPlanCaseVo.class, HashSet.class, ArrayList.class,
                    "productList", "itemList", "levelList", "feeProductList", "feeItemList", "feeLevelList", "feeBelongItemList");
            Validate.isTrue(detailVoMap.containsKey(x.getDetailCode()), "活动细类不存在");
            CostTypeDetailVo detailVo = detailVoMap.get(x.getDetailCode());
            vo.setCategoryCode(detailVo.getCategoryCode());
            vo.setCategoryName(detailVo.getCategoryName());
            vo.setCaseType(MarketingPlanCaseTypeEnum.matching_gift.getCode());
            vo.setBearDepartmentCode(x.getBelongDepartmentCode());
            vo.setBearDepartmentName(x.getBelongDepartmentName());
            vo.setActDesc(x.getRemark());
            vo.setIsContractCost(BooleanEnum.TRUE.getCapital());
            vo.setOnlyKeys(x.getOnlyKey());
            vo.setContractCode(x.getContractCode());
            vo.setActName(x.getDetailName());
            vo.setCostBasis("purchase");
            vo.setCostBasisList(Lists.newArrayList("purchase"));
            vo.setActDesc("合同费用");
            if (CollectionUtils.isNotEmpty(vo.getFeeBelongItemList())) {
                vo.setItemList(vo.getFeeBelongItemList());
            }
            vo.setFeeProductList(null);
            vo.setFeeBelongItemList(null);
            vo.setFeeLevelList(null);
            LocalDate startDate = LocalDate.parse(x.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate endDate = LocalDate.parse(x.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            String startDateStr = actStartDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            String endDateStr = actEndDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            if (actStartDate.isBefore(startDate)) {
                startDateStr = startDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            }
            if (actEndDate.isAfter(endDate)) {
                endDateStr = endDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            }
            vo.setStartDate(startDateStr);
            vo.setEndDate(endDateStr);
            vo.setYears(years);
            return vo;
        }).collect(Collectors.toList());
        return caseVoList;
    }


    /**
     * 转化固定
     *
     * @param list
     * @return
     */
    private List<MarketingPlanCaseVo> conversionFixed(List<ContractCostDetailVo> list, Map<String, CostTypeDetailVo> detailVoMap, LocalDate actStartDate, LocalDate actEndDate, String years) {
        return list.stream().map(x -> {
            MarketingPlanCaseVo vo = nebulaToolkitService.copyObjectByWhiteList(x, MarketingPlanCaseVo.class, HashSet.class, ArrayList.class,
                    "productList", "itemList", "levelList", "feeProductList", "feeItemList", "feeLevelList", "feeBelongItemList");
            Validate.isTrue(detailVoMap.containsKey(x.getDetailCode()), "活动细类不存在");
            CostTypeDetailVo detailVo = detailVoMap.get(x.getDetailCode());
            vo.setCategoryCode(detailVo.getCategoryCode());
            vo.setCategoryName(detailVo.getCategoryName());
            vo.setCaseType(MarketingPlanCaseTypeEnum.fixed.getCode());
            vo.setBearDepartmentCode(x.getBelongDepartmentCode());
            vo.setBearDepartmentName(x.getBelongDepartmentName());
            vo.setActDesc(x.getRemark());
            vo.setGiveNum(x.getRebateStandard());
            vo.setIsContractCost(BooleanEnum.TRUE.getCapital());
            vo.setOnlyKeys(x.getOnlyKey());
            vo.setContractCode(x.getContractCode());
            vo.setActName(x.getDetailName());
            vo.setCostBasis("fixed");
            vo.setCostBasisList(Lists.newArrayList("fixed"));
            vo.setActDesc("合同费用");
            if (CollectionUtils.isNotEmpty(vo.getFeeBelongItemList())) {
                vo.setItemList(vo.getFeeBelongItemList());
                vo.setFeeBelongItemList(null);
            }
            BigDecimal amount = new BigDecimal(x.getRebateStandard());
            switch (vo.getRebateType()) {
                case "0":
                    amount = amount.divide(BigDecimal.valueOf(12), 2, BigDecimal.ROUND_HALF_DOWN);
                    break;
                case "1":
                    amount = amount.divide(BigDecimal.valueOf(3), 2, BigDecimal.ROUND_HALF_DOWN);
                    break;
                case "2":
                    amount = amount;
                    break;
                case "3":
                    List<String> yearsList = getYears(x.getContractStartDate(), x.getContractEndDate());
                    if (CollectionUtil.isNotEmpty(yearsList)) {
                        amount = amount.divide(BigDecimal.valueOf(yearsList.size()), 2, BigDecimal.ROUND_HALF_DOWN);
                    } else {
                        amount = amount;
                    }
                    break;
            }
            vo.setApplyAmount(amount);
            LocalDate startDate = LocalDate.parse(x.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate endDate = LocalDate.parse(x.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            String startDateStr = actStartDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            String endDateStr = actEndDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            if (actStartDate.isBefore(startDate)) {
                startDateStr = startDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            }
            if (actEndDate.isAfter(endDate)) {
                endDateStr = endDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            }
            vo.setStartDate(startDateStr);
            vo.setEndDate(endDateStr);
            vo.setYears(years);
            return vo;
        }).collect(Collectors.toList());
    }


    private static List<String> getYears(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 计算两个日期之间的时间段
        Period period = Period.between(start, end);
        // 获取年和月的差值
        int years = period.getYears();
        int months = period.getMonths();
        // 存储每个年月的结果
        List<String> result = Lists.newArrayList();
        // 遍历每个年月，从过去的日期开始，一直到当前日期
        LocalDate tempDate = start;
        for (int i = 0; i <= years * 12 + months; i++) {
            result.add(tempDate.getYear() + "-" + String.format("%02d", tempDate.getMonthValue()));
            tempDate = tempDate.plusMonths(1);
        }
        return result;
    }

    private void validateParam(ExternalContractDto dto, String externalFlag) {
        Validate.notNull(dto.getContractCode(), "合同编码不能为空");
        Validate.notNull(dto.getContractName(), "合同名称不能为空");
        if (BooleanEnum.TRUE.getCapital().equals(externalFlag)) {
            Validate.notNull(dto.getErpCode(), "ERP客户编码不能为空");
            Validate.notNull(dto.getCompanyCode(), "公司编码不能为空");
            String erpCode = dto.getErpCode();
            erpCode = erpCode.replaceFirst("^0+(?!$)", "");
            dto.setErpCode(erpCode);
        } else {
            if (StringUtils.isBlank(dto.getErpCode()) || StringUtils.isBlank(dto.getCompanyCode())) {
                Validate.notNull(dto.getCustomerCode(), "客户编码不能为空");
            }
        }
        Validate.notNull(dto.getStartDate(), "开始时间不能为空");
        Validate.notNull(dto.getEndDate(), "结束时间不能为空");
        Validate.notNull(dto.getExternalFlag(), "缺少来源状态");
        //不是外部系统的不需要校验
        if (BooleanEnum.FALSE.getCapital().equals(externalFlag)) {
            Validate.isTrue(CollectionUtils.isNotEmpty(dto.getDetailList()), "合同明细类型不能为空");
        }
        for (ExternalContractDetailDto detail : dto.getDetailList()) {
            Validate.notNull(detail.getCaseType(), "费用类型不能为空");
            Validate.notNull(detail.getDetailCode(), "活动细类不能为空");
            if (ObjectUtils.isNotEmpty(detail.getConditionFormula())) {
                FormulaConfigVo formulaConfigVo = formulaConfigVoService.findByConfigCode(detail.getConditionFormula());
                Validate.notNull(formulaConfigVo, "公式编码错误！");
                detail.setConditionFormulaName(formulaConfigVo.getFormulaConfigName());
            }
            if (ObjectUtils.isNotEmpty(detail.getProductStr())) {
                List<ExternalContractProductDto> list = Arrays.stream(detail.getProductStr().split(",")).map(x -> {
                    ExternalContractProductDto product = new ExternalContractProductDto();
                    if (BooleanEnum.TRUE.getCapital().equals(externalFlag)) {
                        product.setMaterialCode(x);
                    } else {
                        product.setCode(x);
                    }
                    return product;
                }).collect(Collectors.toList());
                detail.setProductList(list);
            }
            if (ObjectUtils.isNotEmpty(detail.getItemStr())) {
                List<ExternalContractProductDto> list = Arrays.stream(detail.getItemStr().split(",")).map(x -> {
                    ExternalContractProductDto product = new ExternalContractProductDto();
                    product.setCode(x);
                    return product;
                }).collect(Collectors.toList());
                detail.setItemList(list);
            }
            if (ObjectUtils.isNotEmpty(detail.getLevelStr())) {
                List<ExternalContractProductDto> list = Arrays.stream(detail.getLevelStr().split(",")).map(x -> {
                    ExternalContractProductDto product = new ExternalContractProductDto();
                    product.setCode(x);
                    return product;
                }).collect(Collectors.toList());
                detail.setLevelList(list);
            }
            if (ObjectUtils.isNotEmpty(detail.getFeeProductStr())) {
                List<ExternalContractProductDto> list = Arrays.stream(detail.getFeeProductStr().split(",")).map(x -> {
                    ExternalContractProductDto product = new ExternalContractProductDto();
                    if (BooleanEnum.TRUE.getCapital().equals(externalFlag)) {
                        product.setMaterialCode(x);
                    } else {
                        product.setCode(x);
                    }
                    return product;
                }).collect(Collectors.toList());
                detail.setFeeProductList(list);
            }
            if (ObjectUtils.isNotEmpty(detail.getFeeItemStr())) {
                List<ExternalContractProductDto> list = Arrays.stream(detail.getFeeItemStr().split(",")).map(x -> {
                    ExternalContractProductDto product = new ExternalContractProductDto();
                    product.setCode(x);
                    return product;
                }).collect(Collectors.toList());
                detail.setFeeItemList(list);
            }
            if (ObjectUtils.isNotEmpty(detail.getFeeBelongItemStr())) {
                List<ExternalContractProductDto> list = Arrays.stream(detail.getFeeBelongItemStr().split(",")).map(x -> {
                    ExternalContractProductDto product = new ExternalContractProductDto();
                    product.setCode(x);
                    return product;
                }).collect(Collectors.toList());
                detail.setFeeBelongItemList(list);
            }
            if (ObjectUtils.isNotEmpty(detail.getFeeLevelStr())) {
                List<ExternalContractProductDto> list = Arrays.stream(detail.getFeeLevelStr().split(",")).map(x -> {
                    ExternalContractProductDto product = new ExternalContractProductDto();
                    product.setCode(x);
                    return product;
                }).collect(Collectors.toList());
                detail.setFeeLevelList(list);
            }
        }
        if (BooleanEnum.TRUE.getCapital().equals(externalFlag)) {
            stopContract(dto.getContractCode());
        }
    }
}
