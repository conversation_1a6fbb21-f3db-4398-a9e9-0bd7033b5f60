package com.biz.crm.tpm.business.activities.marketingplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "MarketingPlanItemEstimationVo", description = "方案-品项测算")
public class MarketingPlanItemEstimationVo extends CustomerGainsAndLossesVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;
}
