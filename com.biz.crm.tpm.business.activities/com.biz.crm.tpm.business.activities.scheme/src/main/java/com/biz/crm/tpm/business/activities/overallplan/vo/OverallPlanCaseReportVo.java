package com.biz.crm.tpm.business.activities.overallplan.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanScopeTypeEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/4 14:43
 */
@Data
@ApiModel("大区汇总报表vo")
public class OverallPlanCaseReportVo extends UuidFlagOpVo {

    @ApiModelProperty("活动状态 枚举：PlanClosureEnum")
    private String actStatus;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("关联编码")
    private String releaseCode;

    @ApiModelProperty("关联明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("关联总部编码")
    private String headSchemeCode;

    private String headSchemeName;

    private String headSchemeDetailCode;

    @ApiModelProperty("二级费用大类编码")
    private String secondCostCategory;

    @ApiModelProperty("二级费用大类名称")
    private String secondCostCategoryName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("是否可以承接")
    private String bearFlag;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("归属年月")
    private String years;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("承接类型")
    private String bearType;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("预估费用")
    private BigDecimal applyAmount;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("兑付条件及说明")
    private String cashCondition;

    @ApiModelProperty("合作类型")
    private String cooperateTypeStr;

    @ApiModelProperty("合作类型标签")
    private List<String> cooperateTypeList;

    @ApiModelProperty("客户标签")
    private String customerTagStr;

    @ApiModelProperty("客户标签")
    private List<String> customerTagList;

    @ApiModelProperty("终端标签")
    private String terminalTagStr;

    @ApiModelProperty("终端标签")
    private List<String> terminalTagList;

    @ApiModelProperty("承接部门列表")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanDepartmentVo> bearDepartmentList;

    private String bearDepartmentStr;

    @ApiModelProperty("产品范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProductVo> productAndItemList;

    @ApiModelProperty("客户、终端")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScopeVo> scopeList;

    @ApiModelProperty("品项范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProductVo> itemList;

    private String itemStr;

    @ApiModelProperty("产品范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProductVo> productList;

    private String productStr;

    @ApiModelProperty("客户")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScopeVo> customerList;

    private String customerStr;

    @ApiModelProperty("终端")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScopeVo> terminalList;

    private String terminalStr;

    @ApiModelProperty("已承接金额")
    private BigDecimal alreadyBearAmount;

    @ApiModelProperty("本次承接金额")
    private BigDecimal bearAmount;

    @ApiModelProperty("未承接金额")
    private BigDecimal notBearAmount;

    @ApiModelProperty("计提金额")
    private BigDecimal withholdingAmount;

    @ApiModelProperty("结案金额")
    private BigDecimal auditAmount;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;

    @ApiModelProperty("关闭审批状态")
    private String headCloseProcessStatus;

    public void setProductAndItemList(List<OverallPlanProductVo> productAndItemList) {
        if (CollectionUtils.isNotEmpty(productAndItemList)) {
            this.productAndItemList = productAndItemList;
            //设置商品
            List<OverallPlanProductVo> products = productAndItemList.stream().filter(x -> OverallPlanProductEnum.cal_product.getCode().equals(x.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(products)) {
                this.productList = products;
                this.productStr = products.stream().collect(Collectors.mapping(OverallPlanProductVo::getName, Collectors.joining("、")));
            }
            //设置品项
            List<OverallPlanProductVo> itemList = productAndItemList.stream().filter(x -> OverallPlanProductEnum.cal_item.getCode().equals(x.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemList)) {
                this.itemList = itemList;
                this.itemStr = itemList.stream().collect(Collectors.mapping(OverallPlanProductVo::getName, Collectors.joining("、")));
            }
        }
    }

    public List<OverallPlanProductVo> getProductAndItemList() {
        List<OverallPlanProductVo> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(this.productList)) {
            list.addAll(this.productList);
        }
        if (CollectionUtils.isNotEmpty(this.itemList)) {
            list.addAll(this.itemList);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            this.productAndItemList = list;
        }
        return this.productAndItemList;
    }

    public void setScopeList(List<OverallPlanScopeVo> scopeList) {
        if (CollectionUtils.isNotEmpty(scopeList)) {
            this.scopeList = scopeList;
            List<OverallPlanScopeVo> customerList = scopeList.stream().filter(x -> OverallPlanScopeTypeEnum.cus.getCode().equals(x.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customerList)) {
                this.customerList = customerList;
                this.customerStr = customerList.stream().collect(Collectors.mapping(OverallPlanScopeVo::getCustomerName, Collectors.joining("、")));
            }
            List<OverallPlanScopeVo> terminalList = this.scopeList.stream().filter(x -> OverallPlanScopeTypeEnum.terminal.getCode().equals(x.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(terminalList)) {
                this.terminalList = terminalList;
                this.terminalStr = terminalList.stream().collect(Collectors.mapping(OverallPlanScopeVo::getTerminalName, Collectors.joining("、")));
            }
        }
    }

    public List<OverallPlanScopeVo> getScopeList() {
        List<OverallPlanScopeVo> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(this.customerList)) {
            list.addAll(this.customerList);
        }
        if (CollectionUtils.isNotEmpty(this.terminalList)) {
            list.addAll(this.terminalList);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            this.scopeList = list;
        }
        return this.scopeList;
    }
}
