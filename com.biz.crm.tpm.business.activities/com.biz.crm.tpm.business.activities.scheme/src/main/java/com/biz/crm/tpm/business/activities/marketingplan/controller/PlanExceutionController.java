package com.biz.crm.tpm.business.activities.marketingplan.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.AuditExecutionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanExecutionDto;
import com.biz.crm.tpm.business.activities.sdk.service.PlanExecutionService;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionAggregateVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/v1/planExceution")
@Api(tags = "活动执行")
public class PlanExceutionController {

    @Autowired(required = false)
    private PlanExecutionService planExecutionService;

    @ApiOperation(value = "根据活动明细编码查询活动执行")
    @GetMapping("findByDetailCode")
    public Result<List<PlanExecutionVo>> findByDetailCode(@ApiParam(name = "code", value = "执行编码")
                                                          @RequestParam(value = "code", required = false) String code) {
        try {
            List<PlanExecutionVo> detailVoList = this.planExecutionService.findByDetailCode(code);
            return Result.ok(detailVoList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据活动明细编码查询活动执行-聚合")
    @GetMapping("findByDetailCodeV1")
    public Result<PlanExecutionAggregateVo> findByDetailCodeV1(@ApiParam(name = "code", value = "执行编码")
                                                          @RequestParam(value = "code", required = false) String code) {
        try {
            PlanExecutionAggregateVo aggregateVo = this.planExecutionService.findByDetailCodeV1(code);
            return Result.ok(aggregateVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("createPlanExecution")
    @ApiOperation(value = "创建活动执行")
    public Result<?> createPlanExecution(@RequestBody @ApiParam(name = "dto", value = "活动执行信息") PlanExecutionDto dto) {
        try {
            planExecutionService.createPlanExecution(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("createAuditExecutionCollect")
    @ApiOperation(value = "创建活动稽查")
    public Result<?> createAuditExecutionCollect(@RequestBody @ApiParam(name = "dto", value = "活动执行信息") AuditExecutionCollectVo dto) {
        try {
            planExecutionService.createAuditExecutionCollect(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("updatePlanExecution")
    @ApiOperation(value = "修改活动执行")
    public Result<?> updatePlanExecution(@RequestBody @ApiParam(name = "dto", value = "修改活动执行") PlanExecutionDto dto) {
        try {
            planExecutionService.updatePlanExecution(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("findByDynamicKey")
    @ApiOperation(value = "活动执行信息")
    public Result<PlanExecutionVo> findByDynamicKey(@RequestBody @ApiParam(name = "dto", value = "活动执行信息") PlanExecutionDto dto) {
        try {
            PlanExecutionVo vo = this.planExecutionService.findByDynamicKey(dto);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("findDetail")
    @ApiOperation(value = "查询详情")
    public Result<PlanExecutionVo> findDetail(@RequestBody @ApiParam(name = "dto", value = "活动执行信息") PlanExecutionDto dto) {
        try {
            PlanExecutionVo vo = this.planExecutionService.findDetail(dto);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("findDetailById")
    public Result<PlanExecutionVo> findDetailById(@RequestParam("id") String id) {
        try {
            PlanExecutionVo vo = this.planExecutionService.findDetailById(id);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("findAuditExecutionDetail")
    @ApiOperation(value = "活动执稽查明细信息")
    public Result<AuditExecutionCollectVo> findAuditExecutionDetail(@RequestBody @ApiParam(name = "dto", value = "活动执行信息") AuditExecutionCollectVo dto) {
        try {
            AuditExecutionCollectVo vo = this.planExecutionService.findAuditExecutionDetail(dto);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "活动执行采集-分页接口")
    @GetMapping("/findByConditions")
    public Result<Page<PlanExecutionVo>> findByConditions(@PageableDefault(50) Pageable pageable, @ApiParam(name = "dto", value = "分页Dto") PlanExecutionDto dto) {
        try {
            return Result.ok(this.planExecutionService.findByConditions(pageable, dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/initPlanExecutionCustom")
    public Result<?> initPlanExecutionCustom() {
        planExecutionService.initPlanExecutionCustom();
        return Result.ok();
    }

}
