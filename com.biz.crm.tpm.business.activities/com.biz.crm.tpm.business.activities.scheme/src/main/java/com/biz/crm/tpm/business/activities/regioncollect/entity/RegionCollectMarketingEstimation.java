package com.biz.crm.tpm.business.activities.regioncollect.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_region_collect_marketing_estimation")
@Table(
        name = "tpm_region_collect_marketing_estimation",
        indexes = {
                @Index(name = "tpm_region_collect_marketing_estimation_index0", columnList = "collect_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_region_collect_marketing_estimation", comment = "大区汇总-营销测算表")
@ApiModel(value = "RegionCollectMarketingEstimation", description = "大区汇总-营销测算表")
public class RegionCollectMarketingEstimation extends UuidFlagOpEntity {

    @ApiModelProperty("汇总编码")
    @Column(name = "collect_code", columnDefinition = "varchar(32) comment '汇总编码'")
    private String collectCode;

    @ApiModelProperty("项目编码")
    @Column(name = "project_code", columnDefinition = "varchar(32) comment '项目编码'")
    private String projectCode;

    @ApiModelProperty("项目名称")
    @Column(name = "project_name", columnDefinition = "varchar(32) comment '项目名称'")
    private String projectName;

    @ApiModelProperty("排序")
    @Column(name = "sort", columnDefinition = "int(10) comment '排序'")
    private Integer sort;

    @ApiModelProperty("预计固定扣点")
    @Column(name = "estimate_fixed_point", columnDefinition = "decimal(18,4) comment '预计固定扣点'")
    private BigDecimal estimateFixedPoint;

    @ApiModelProperty("预计固定扣点")
    @Column(name = "estimate_fixed_point_str", columnDefinition = "varchar(32) comment '预计固定扣点'")
    private String estimateFixedPointStr;

    @ApiModelProperty("预计金额(万元)")
    @Column(name = "estimate_amount", columnDefinition = "decimal(18,4) comment '预计金额(万元)'")
    private BigDecimal estimateAmount;

    @ApiModelProperty("预计占比")
    @Column(name = "estimate_ratio", columnDefinition = "decimal(18,4) comment '预计占比'")
    private BigDecimal estimateRatio;

    @ApiModelProperty("预计占比(%)")
    @Column(name = "estimate_ratio_str", columnDefinition = "varchar(32) comment '预计占比(%)'")
    private String estimateRatioStr;

    @ApiModelProperty("预算固定扣点")
    @Column(name = "budget_fixed_point", columnDefinition = "decimal(18,4) comment '预算固定扣点'")
    private BigDecimal budgetFixedPoint;

    @ApiModelProperty("预算固定扣点")
    @Column(name = "budget_fixed_point_str", columnDefinition = "varchar(32) comment '预算固定扣点'")
    private String budgetFixedPointStr;

    @ApiModelProperty("预算金额(万元)")
    @Column(name = "budget_amount", columnDefinition = "decimal(18,4) comment '预算金额(万元)'")
    private BigDecimal budgetAmount;

    @ApiModelProperty("预算占比")
    @Column(name = "budget_ratio", columnDefinition = "decimal(10,4) comment '预算占比'")
    private BigDecimal budgetRatio;

    @ApiModelProperty("预算占比")
    @Column(name = "budget_ratio_str", columnDefinition = "varchar(32) comment '预算占比'")
    private String budgetRatioStr;

    @ApiModelProperty("差异额")
    @Column(name = "difference_amount", columnDefinition = "decimal(18,4) comment '差异额'")
    private BigDecimal differenceAmount;

    @ApiModelProperty("差异率")
    @Column(name = "difference_ratio", columnDefinition = "decimal(10,4) comment '差异率'")
    private BigDecimal differenceRatio;

    @ApiModelProperty("差异率")
    @Column(name = "difference_ratio_str", columnDefinition = "varchar(32) comment '差异率'")
    private String differenceRatioStr;
}
