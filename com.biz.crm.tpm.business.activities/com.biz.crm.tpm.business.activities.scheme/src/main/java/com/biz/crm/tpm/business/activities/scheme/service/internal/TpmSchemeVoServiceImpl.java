package com.biz.crm.tpm.business.activities.scheme.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.user.feign.feign.UserVoFeign;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.scheme.common.SchemeConstants;
import com.biz.crm.tpm.business.activities.scheme.dto.SchemeDto;
import com.biz.crm.tpm.business.activities.scheme.dto.SchemeLogEventDto;
import com.biz.crm.tpm.business.activities.scheme.entity.Scheme;
import com.biz.crm.tpm.business.activities.scheme.event.SchemeEventListener;
import com.biz.crm.tpm.business.activities.scheme.event.SchemeLogEventListener;
import com.biz.crm.tpm.business.activities.scheme.repository.TpmSchemeRepository;
import com.biz.crm.tpm.business.activities.scheme.service.SchemeCostBudgetVoService;
import com.biz.crm.tpm.business.activities.scheme.service.SchemeFilesVoService;
import com.biz.crm.tpm.business.activities.scheme.service.SchemeProductVoService;
import com.biz.crm.tpm.business.activities.scheme.service.SchemeRangeVoService;
import com.biz.crm.tpm.business.activities.scheme.service.TpmSchemeVoService;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeCostBudgetVo;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeFilesVo;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeProductVo;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeRangeVo;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.enums.RedisKeys.GENERATE_PREFIX;

/**
 * 方案;(tpm_scheme)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Service("schemeVoService")
public class TpmSchemeVoServiceImpl implements TpmSchemeVoService {
  @Autowired
  private TpmSchemeRepository schemeRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private RedisMutexService redisMutexService;
  @Autowired(required = false)
  private List<SchemeEventListener> schemeEventListeners;
  @Autowired
  private SchemeFilesVoService schemeFilesVoService;
  @Autowired
  private SchemeProductVoService schemeProductVoService;
  @Autowired
  private SchemeCostBudgetVoService schemeCostBudgetVoService;
  @Autowired
  private SchemeRangeVoService schemeRangeVoService;
  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private LoginUserService loginUserService;
  @Autowired
  private UserVoFeign userVoFeign;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<SchemeVo> findByConditions(Pageable pageable, SchemeDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new SchemeDto();
    }
    List<String> selectedCodeList =
            Optional.ofNullable(dto.getSelectedCodeList()).orElse(new ArrayList<>());
    if (StringUtils.isNotEmpty(dto.getSelectedCode())) {
      selectedCodeList.add(dto.getSelectedCode());
    }
    if (!CollectionUtils.isEmpty(selectedCodeList)) {
      dto.setSelectedCodeList(selectedCodeList);
    }
    if (dto.isSelect()) {
      Set<SchemeRangeVo> schemeRangeVos = this.schemeRangeVoService.findAll();
      List<String> schemeOrgCodes = schemeRangeVos.stream().filter(item -> item.getRangeType() == 1).map(SchemeRangeVo::getRangeCode).distinct().collect(Collectors.toList());
      List<String> schemeOrgTypes = schemeRangeVos.stream().filter(item -> item.getRangeType() == 2).map(SchemeRangeVo::getRangeCode).distinct().collect(Collectors.toList());
      UserIdentity loginDetails = loginUserService.getLoginUser();
      Result<UserVo> userVoOrgCodeResult = userVoFeign.findRelationByUserNameAndOrgCodesOrOrgTypes(loginDetails.getAccount(), schemeOrgCodes, schemeOrgTypes);
      UserVo userVo = userVoOrgCodeResult.getResult();
      if (userVo != null) {
        dto.setOrgCodes(userVo.getRelationOrgCodes());
        dto.setOrgTypes(userVo.getRelationOrgTypes());
      }
      dto.setSchemeEndTime(new Date());
    }
    return this.schemeRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public SchemeVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    Scheme scheme = this.schemeRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (scheme == null) {
      return null;
    }
    SchemeVo schemeVo = this.nebulaToolkitService.copyObjectByWhiteList(scheme, SchemeVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetails(schemeVo);
    return schemeVo;
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 主键
   * @return 单条数据
   */
  @Override
  public SchemeVo findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    Scheme scheme = this.schemeRepository.findByCode(code);
    if (scheme == null) {
      return null;
    }
    SchemeVo schemeVo = this.nebulaToolkitService.copyObjectByWhiteList(scheme, SchemeVo.class, LinkedHashSet.class, ArrayList.class);
    this.fillDetails(schemeVo);
    return schemeVo;
  }

  /**
   * 填充明细信息
   *
   * @param schemeVo
   */
  private void fillDetails(SchemeVo schemeVo) {
    if (schemeVo == null) {
      return;
    }
    // 填充附件
    Set<SchemeFilesVo> schemeFilesVos = this.schemeFilesVoService.findBySchemeCode(schemeVo.getSchemeCode());
    if (!CollectionUtils.isEmpty(schemeFilesVos)) {
      schemeVo.setSchemeFilesVos(schemeFilesVos);
    }
    // 填充商品
    Set<SchemeProductVo> schemeProductVos = this.schemeProductVoService.findBySchemeCode(schemeVo.getSchemeCode());
    schemeVo.setSchemeProductVos(schemeProductVos);
    // 填充费用预算
    Set<SchemeCostBudgetVo> schemeCostBudgetVos = this.schemeCostBudgetVoService.findBySchemeCode(schemeVo.getSchemeCode());
    Set<CostBudgetVo> costBudgetVos = Sets.newLinkedHashSet();
    if (!CollectionUtils.isEmpty(schemeCostBudgetVos)) {
      schemeCostBudgetVos.forEach(item -> {
        CostBudgetVo costBudgetVo = this.costBudgetVoService.findByCode(item.getCostBudgetCode());
        if (Objects.nonNull(costBudgetVo)) {
          costBudgetVos.add(costBudgetVo);
        }
      });
    }
    schemeVo.setSchemeCostBudgetVos(costBudgetVos);
    // 填充方案范围
    Set<SchemeRangeVo> schemeRangeVos = this.schemeRangeVoService.findBySchemeCode(schemeVo.getSchemeCode());
    schemeVo.setSchemeRangeVos(schemeRangeVos);
  }

  /**
   * 新增数据
   *
   * @param schemeVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public SchemeVo create(SchemeVo schemeVo) {
    schemeVo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    this.createValidate(schemeVo);
    String code = this.generateCodeService.generateCode(SchemeConstants.SCHEME_LADDER_CODE);
    schemeVo.setSchemeCode(code);
    Scheme scheme = this.nebulaToolkitService.copyObjectByWhiteList(schemeVo, Scheme.class, LinkedHashSet.class, ArrayList.class);
    scheme.setTenantCode(TenantUtils.getTenantCode());
    this.schemeRepository.saveOrUpdate(scheme);

    //附件的保存
    if (!CollectionUtils.isEmpty(schemeVo.getSchemeFilesVos())) {
      for (SchemeFilesVo schemeFilesVo : schemeVo.getSchemeFilesVos()) {
        schemeFilesVo.setId(null);
        schemeFilesVo.setSchemeCode(scheme.getSchemeCode());
        schemeFilesVo.setTenantCode(TenantUtils.getTenantCode());
        this.schemeFilesVoService.create(schemeFilesVo);
      }
    }
    // 方案商品
    for (SchemeProductVo schemeProductVo : schemeVo.getSchemeProductVos()) {
      schemeProductVo.setId(null);
      schemeProductVo.setSchemeCode(schemeVo.getSchemeCode());
      schemeProductVo.setTenantCode(TenantUtils.getTenantCode());
      this.schemeProductVoService.create(schemeProductVo);
    }
    // 方案预算科目
    for (CostBudgetVo costBudgetVo : schemeVo.getSchemeCostBudgetVos()) {
      SchemeCostBudgetVo schemeCostBudgetVo = new SchemeCostBudgetVo();
      schemeCostBudgetVo.setId(null);
      schemeCostBudgetVo.setCostBudgetCode(costBudgetVo.getCode());
      schemeCostBudgetVo.setSchemeCode(schemeVo.getSchemeCode());
      schemeCostBudgetVo.setTenantCode(TenantUtils.getTenantCode());
      this.schemeCostBudgetVoService.create(schemeCostBudgetVo);
    }
    // 方案范围
    for (SchemeRangeVo schemeRangeVo : schemeVo.getSchemeRangeVos()) {
      schemeRangeVo.setId(null);
      schemeRangeVo.setSchemeCode(schemeVo.getSchemeCode());
      schemeRangeVo.setTenantCode(TenantUtils.getTenantCode());
      this.schemeRangeVoService.create(schemeRangeVo);
    }

    schemeVo.setId(scheme.getId());
    if (!CollectionUtils.isEmpty(schemeEventListeners)) {
      for (SchemeEventListener schemeEventListener : schemeEventListeners) {
        schemeEventListener.onCreated(schemeVo);
      }
    }

    //新增业务日志
    SchemeLogEventDto logEventDto = new SchemeLogEventDto();
    logEventDto.setOriginal(null);
    logEventDto.setNewest(schemeVo);
    SerializableBiConsumer<SchemeLogEventListener, SchemeLogEventDto> onCreate =
            SchemeLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(logEventDto, SchemeLogEventListener.class, onCreate);
    return schemeVo;
  }

  /**
   * 修改新据
   *
   * @param schemeVo 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public SchemeVo update(SchemeVo schemeVo) {
    this.updateValidate(schemeVo);
    Scheme scheme = this.schemeRepository.findByIdAndTenantCode(schemeVo.getId(),TenantUtils.getTenantCode());
    SchemeVo oldSchemeVo = this.nebulaToolkitService.copyObjectByWhiteList(scheme, SchemeVo.class, LinkedHashSet.class, ArrayList.class);
    Validate.notNull(scheme, "修改数据不存在，请检查！");
    // TODO 修改内容
    scheme.setSchemeName(schemeVo.getSchemeName());
    scheme.setSchemeType(schemeVo.getSchemeType());
    scheme.setSchemeBeginTime(schemeVo.getSchemeBeginTime());
    scheme.setSchemeEndTime(schemeVo.getSchemeEndTime());
    scheme.setSchemeDescription(schemeVo.getSchemeDescription());
    scheme.setRemark(schemeVo.getRemark());
    scheme.setTenantCode(TenantUtils.getTenantCode());
    this.schemeRepository.saveOrUpdate(scheme);

    //附件的更新
    if (!CollectionUtils.isEmpty(schemeVo.getSchemeFilesVos())) {
      this.schemeFilesVoService.deleteBySchemeCode(scheme.getSchemeCode());
      for (SchemeFilesVo schemeFilesVo : schemeVo.getSchemeFilesVos()) {
        schemeFilesVo.setId(null);
        schemeFilesVo.setSchemeCode(scheme.getSchemeCode());
        schemeFilesVo.setTenantCode(TenantUtils.getTenantCode());
        this.schemeFilesVoService.create(schemeFilesVo);
      }
    }
    // 方案商品
    this.schemeProductVoService.deleteBySchemeCode(scheme.getSchemeCode());
    for (SchemeProductVo schemeProductVo : schemeVo.getSchemeProductVos()) {
      schemeProductVo.setId(null);
      schemeProductVo.setSchemeCode(scheme.getSchemeCode());
      schemeProductVo.setTenantCode(TenantUtils.getTenantCode());
      this.schemeProductVoService.create(schemeProductVo);
    }
    // 方案费用预算
    this.schemeCostBudgetVoService.deleteBySchemeCode(scheme.getSchemeCode());
    for (CostBudgetVo costBudgetVo : schemeVo.getSchemeCostBudgetVos()) {
      SchemeCostBudgetVo schemeCostBudgetVo = new SchemeCostBudgetVo();
      schemeCostBudgetVo.setId(null);
      schemeCostBudgetVo.setCostBudgetCode(costBudgetVo.getCode());
      schemeCostBudgetVo.setSchemeCode(schemeVo.getSchemeCode());
      schemeCostBudgetVo.setTenantCode(TenantUtils.getTenantCode());
      this.schemeCostBudgetVoService.create(schemeCostBudgetVo);
    }
    // 方案范围
    this.schemeRangeVoService.deleteBySchemeCode(scheme.getSchemeCode());
    for (SchemeRangeVo schemeRangeVo : schemeVo.getSchemeRangeVos()) {
      schemeRangeVo.setId(null);
      schemeRangeVo.setSchemeCode(schemeVo.getSchemeCode());
      schemeRangeVo.setTenantCode(TenantUtils.getTenantCode());
      this.schemeRangeVoService.create(schemeRangeVo);
    }

    if (!CollectionUtils.isEmpty(schemeEventListeners)) {
      for (SchemeEventListener schemeEventListener : schemeEventListeners) {
        schemeEventListener.onUpdate(oldSchemeVo, schemeVo);
      }
    }

    //编辑业务日志
    SchemeLogEventDto logEventDto = new SchemeLogEventDto();
    logEventDto.setOriginal(oldSchemeVo);
    logEventDto.setNewest(schemeVo);
    SerializableBiConsumer<SchemeLogEventListener, SchemeLogEventDto> onUpdate =
            SchemeLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(logEventDto, SchemeLogEventListener.class, onUpdate);
    return schemeVo;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<Scheme> schemes = this.schemeRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(schemes)) {
      return;
    }
    Collection<SchemeVo> schemeVos = this.nebulaToolkitService.copyCollectionByWhiteList(schemes, Scheme.class, SchemeVo.class, LinkedHashSet.class, ArrayList.class);
    this.schemeRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());

    if (!CollectionUtils.isEmpty(schemeEventListeners)) {
      for (SchemeEventListener schemeEventListener : schemeEventListeners) {
        for (SchemeVo schemeVo : schemeVos) {
          schemeEventListener.onDeleted(schemeVo);
        }
      }
    }

    //删除业务日志
    SerializableBiConsumer<SchemeLogEventListener, SchemeLogEventDto> onDelete =
            SchemeLogEventListener::onDelete;
    for (SchemeVo schemeVo : schemeVos) {
      SchemeLogEventDto logEventDto = new SchemeLogEventDto();
      logEventDto.setOriginal(schemeVo);
      this.nebulaNetEventClient.publish(logEventDto, SchemeLogEventListener.class, onDelete);
    }
  }

  @Override
  public boolean existsByCostBudget(String costBudgetCode) {
    return NumberUtils.compare(this.schemeRepository.countByCostBudgetCode(costBudgetCode), 0) > 0;
  }

  @Override
  public List<SchemeVo> findBySelect(String keyword) {
    UserIdentity loginDetails = this.loginUserService.getLoginUser();
    String account = loginDetails.getAccount();
    return null;
  }

  /**
   * 生成操作标记
   */
  @Override
  public String preSave() {
    String prefix = UUID.randomUUID().toString();
    // 1天后过期
    this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, TimeUnit.MILLISECONDS.convert(1, TimeUnit.DAYS));
    return prefix;
  }

  /**
   * 验证与操作标记
   */
  private void validationPrefix(SchemeVo schemeVo) {
    // 验证重复提交标识
    String prefix = schemeVo.getPrefix();
    Validate.notBlank(prefix, "错误的预操作标记，请检查!!");
    Validate.isTrue(StringUtils.isNotBlank(this.redisMutexService.getMCode(GENERATE_PREFIX, prefix)), "没有发现预操作标记，可能是因为重复操作的原因!");
    boolean isLock = false;
    try {
      if (isLock = this.redisMutexService.tryLock(prefix, TimeUnit.MILLISECONDS, 1)) {
        this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, 1L);
      } else {
        throw new IllegalArgumentException("请不要重复操作!!");
      }
    } finally {
      if (isLock) {
        this.redisMutexService.unlock(prefix);
      }
    }
  }

  /**
   * 创建验证
   *
   * @param schemeVo
   */
  private void createValidate(SchemeVo schemeVo) {
    Validate.notNull(schemeVo, "新增时，对象信息不能为空！");
    schemeVo.setId(null);
    // 验证重复操作
    this.validationPrefix(schemeVo);
    Validate.notBlank(schemeVo.getDelFlag(), "新增数据时，数据状态（删除状态）不能为空！");
    Validate.notBlank(schemeVo.getSchemeName(), "新增数据时，方案名称不能为空！");
    Validate.notBlank(schemeVo.getSchemeType(), "新增数据时，方案类型不能为空！");
    Validate.notNull(schemeVo.getSchemeBeginTime(), "新增数据时，方案开始时间不能为空！");
    Validate.notNull(schemeVo.getSchemeEndTime(), "新增数据时，方案结束时间不能为空！");
    Validate.notBlank(schemeVo.getSchemeDescription(), "新增数据时，方案说明不能为空！");
    Validate.notBlank(schemeVo.getSchemeStatus(), "新增数据时，方案状态不能为空！");
    Validate.notEmpty(schemeVo.getSchemeProductVos(), "新增数据时，方案商品不能为空！");
    Validate.notEmpty(schemeVo.getSchemeCostBudgetVos(), "新增数据时，方案预算科目不能为空！");
    Validate.notEmpty(schemeVo.getSchemeRangeVos(), "新增数据时，方案范围不能为空！");
  }

  /**
   * 修改验证
   *
   * @param schemeVo
   */
  private void updateValidate(SchemeVo schemeVo) {
    Validate.notNull(schemeVo, "修改时，对象信息不能为空！");
    // 验证重复操作
    this.validationPrefix(schemeVo);
    Validate.notBlank(schemeVo.getId(), "修改数据时，主键不能为空！");
    Validate.notBlank(schemeVo.getSchemeName(), "修改数据时，方案名称不能为空！");
    Validate.notBlank(schemeVo.getSchemeType(), "修改数据时，方案类型不能为空！");
    Validate.notNull(schemeVo.getSchemeBeginTime(), "修改数据时，方案开始时间不能为空！");
    Validate.notNull(schemeVo.getSchemeEndTime(), "修改数据时，方案结束时间不能为空！");
    Validate.notEmpty(schemeVo.getSchemeProductVos(), "修改数据时，方案商品不能为空！");
    Validate.notEmpty(schemeVo.getSchemeCostBudgetVos(), "修改数据时，方案预算科目不能为空！");
    Validate.notEmpty(schemeVo.getSchemeRangeVos(), "修改数据时，方案范围不能为空！");
  }
}