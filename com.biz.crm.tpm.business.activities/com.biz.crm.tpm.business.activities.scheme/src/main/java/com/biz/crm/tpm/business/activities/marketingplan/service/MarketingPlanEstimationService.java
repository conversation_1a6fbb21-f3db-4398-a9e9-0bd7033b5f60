package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanEstimationVo;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 00:13
 */
public interface MarketingPlanEstimationService {

    void saveBatchList(List<MarketingPlanEstimationVo> list,String schemeCode);

    List<MarketingPlanEstimationVo> findListBySchemeCode(List<String> schemCodeList);

    void deleteBySchemeCodes(List<String> schemeCodes);

    List<MarketingPlanEstimationVo> findOriginalListBySchemeCode(List<String> schemCodeList);
}
