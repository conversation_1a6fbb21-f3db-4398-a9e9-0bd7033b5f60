package com.biz.crm.tpm.business.activities.contract.service.internal;

import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.business.activities.contract.repository.ExternalContractRepository;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo;
import com.biz.crm.tpm.business.activities.sdk.service.ExternalContractVoService;
import com.biz.crm.tpm.business.activities.sdk.vo.ExternalContractSdkVo;
import com.bizunited.nebula.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/10 11:31
 **/
@Service
@Slf4j
public class ExternalContractVoServiceImpl implements ExternalContractVoService {

    @Resource
    private ExternalContractRepository repository;

    @Resource
    private DictDataVoService dictDataVoService;

    private static final String SFA_CONTRACT_NEAR_DAY = "SFA_CONTRACT_NEAR_DAY";

    @Override
    public List<ExternalContractSdkVo> findContractNearDate() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(SFA_CONTRACT_NEAR_DAY);
        Integer day = 10;
        if (!CollectionUtils.isEmpty(dictDataVos)) {
            try {
                day = Integer.valueOf(dictDataVos.get(0).getDictValue());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

        }
        List<ExternalContractVo> contractVoList = repository.findContractNearDate(day);
        contractVoList = contractVoList.stream().filter(x -> x.getDay() > 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(contractVoList)) {
            List<String> ids = contractVoList.stream().map(x -> x.getId()).collect(Collectors.toList());
            repository.updateContractPushSfaFlag(ids);
            return JsonUtils.convert(contractVoList, List.class, ExternalContractSdkVo.class);
        }
        return Collections.emptyList();
    }
}
