package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/10 22:22
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_sales_plan")
@Table(
        name = "tpm_marketing_sales_plan",
        indexes = {
                @Index(name = "tpm_marketing_sales_plan_index0", columnList = "scheme_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_sales_plan", comment = "营销方案规划-销售计划")
@ApiModel(value = "MarketingSalesPlan", description = "营销方案规划-销售计划")
public class MarketingSalesPlan extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty(value = "销售计划编码")
    @Column(name = "code", columnDefinition = "varchar(32) comment '销售计划编码'")
    private String code;

    @ApiModelProperty(value = "年月")
    @Column(name = "years", columnDefinition = "varchar(10) comment '年月'")
    private String years;

    @ApiModelProperty(value = "成本中心编码")
    @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
    private String costCenterCode;

    @ApiModelProperty(value = "成本中心名称")
    @Column(name = "cost_center_name", columnDefinition = "varchar(128) comment '成本中心名称'")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

    @ApiModelProperty("品项")
    @Column(name = "item_code", columnDefinition = "varchar(64) comment '品项'")
    private String itemCode;

    @ApiModelProperty("品项")
    @Column(name = "item_name", columnDefinition = "varchar(64) comment '品项'")
    private String itemName;

    @Column(name = "product_code", columnDefinition = "varchar(32) comment '产品编码'")
    private String productCode;

    @Column(name = "product_name", columnDefinition = "varchar(128) comment '产品名称'")
    private String productName;

    @Column(name = "sales_price", columnDefinition = "decimal(18,4) comment '销售价格'")
    private BigDecimal salesPrice;

    @ApiModelProperty("税率")
    @Column(name = "tax_rate", columnDefinition = "decimal(18,4) comment '税率'")
    private BigDecimal taxRate;

    @Column(name = "sale_unit", columnDefinition = "varchar(32) comment '销售单位'")
    private String saleUnit;

    @Column(name = "material_code", columnDefinition = "varchar(32) comment '物料编码'")
    private String materialCode;

    @ApiModelProperty("成本价")
    @Column(name = "cost_price", columnDefinition = "decimal(18,4) comment '成本价'")
    private BigDecimal costPrice;

    @ApiModelProperty("毛利率")
    @Column(name = "gross_profit_rate", columnDefinition = "varchar(20) comment '毛利率'")
    private String grossProfitRate;

    @Column(name = "estimated_cost", columnDefinition = "decimal(18,4) comment '预估销售金额(未税)'")
    private BigDecimal estimatedCost;

    @ApiModelProperty("含税预估销售金额")
    @Column(name = "tax_estimated_cost", columnDefinition = "decimal(18,4) comment '含税预估销售金额'")
    private BigDecimal taxEstimatedCost;

    @Column(name = "estimated_sales_volume", columnDefinition = "decimal(18,4) comment '预估销售额额'")
    private BigDecimal estimatedSalesVolume;

    @Column(name = "origin_estimated_cost", columnDefinition = "decimal(18,4) comment '原始预估销售金额(未税)'")
    private BigDecimal originEstimatedCost;

    @Column(name = "transport_type", columnDefinition = "varchar(10) comment '产品运输方式'")
    private String transportType;

    @ApiModelProperty("关键字段,customerCode:costCenterCode:years")
    @Column(name = "index_key", columnDefinition = "varchar(128) comment '关键字段'")
    private String indexKey;

    @ApiModelProperty("唯一字段")
    @Column(name = "only_key", columnDefinition = "varchar(128) comment '唯一字段'")
    private String onlyKey;

    @ApiModelProperty("成本中心对应组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '创建人组织编码'")
    private String orgCode;

    @ApiModelProperty("成本中心对应组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(128) comment '创建人组织名称'")
    private String orgName;

    @ApiModelProperty("创建人职位编码")
    @Column(name = "position_code", columnDefinition = "varchar(32) comment '创建人职位编码'")
    private String positionCode;

    @ApiModelProperty("创建人职位名称")
    @Column(name = "position_name", columnDefinition = "varchar(128) comment '创建人职位名称'")
    private String positionName;

    @ApiModelProperty("错误信息")
    @Column(name = "err_msg", columnDefinition = "text comment '错误信息'")
    private String errMsg;

    @ApiModelProperty("校验结果")
    @Column(name = "check_flag", columnDefinition = "tinyint comment '校验结果'")
    private Boolean checkFlag;

    @ApiModelProperty("是否变更")
    @Column(name = "change_flag", columnDefinition = "varchar(10) comment '是否变更'")
    private String changeFlag;

    @ApiModelProperty("转换系数")
    @Column(name = "conversion_value", columnDefinition = "decimal(10,2) comment '转换系数'")
    private BigDecimal conversionValue;

    @ApiModelProperty("排序")
    @Column(name = "sort", columnDefinition = "bigint(20) comment '排序'")
    private Long sort;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("成本中心的公司代码")
    private String costCenterCompanyCode;

}
