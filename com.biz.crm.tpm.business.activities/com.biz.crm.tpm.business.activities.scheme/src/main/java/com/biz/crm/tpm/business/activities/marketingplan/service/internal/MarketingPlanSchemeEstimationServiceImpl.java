package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanSchemeEstimation;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanSchemeEstimationRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanSchemeEstimationService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanSchemeEstimationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 00:14
 */
@Service
@Transactional
public class MarketingPlanSchemeEstimationServiceImpl implements MarketingPlanSchemeEstimationService {

    @Resource
    private MarketingPlanSchemeEstimationRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;


    /**
     * 保存数据
     *
     * @param list
     * @param schemeCode
     */
    @Override
    public void saveBatchList(List<MarketingPlanSchemeEstimationVo> list, String schemeCode) {
        repository.deleteBySchemeCodes(Lists.newArrayList(schemeCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<MarketingPlanSchemeEstimation> dataList = (List<MarketingPlanSchemeEstimation>) nebulaToolkitService.copyCollectionByWhiteList(list, MarketingPlanSchemeEstimationVo.class,
                MarketingPlanSchemeEstimation.class, HashSet.class, ArrayList.class);
        dataList.forEach(x -> x.setSchemeCode(schemeCode));
        repository.saveBatch(dataList);
    }

    @Override
    public List<MarketingPlanSchemeEstimationVo> findListBySchemeCode(List<String> schemCodeList) {
        List<MarketingPlanSchemeEstimation> dataList = repository.findListBySchemeCodes(schemCodeList);
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        return (List<MarketingPlanSchemeEstimationVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, MarketingPlanSchemeEstimation.class,
                MarketingPlanSchemeEstimationVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public void deleteBySchemeCodes(List<String> schemeCodes) {
        repository.deleteBySchemeCodes(schemeCodes);
    }

    @Override
    public List<MarketingPlanSchemeEstimationVo> findOriginalListBySchemeCode(List<String> schemCodeList) {
        return repository.findOriginalListBySchemeCode(schemCodeList);
    }
}
