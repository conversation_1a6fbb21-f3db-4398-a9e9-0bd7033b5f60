package com.biz.crm.tpm.business.activities.contract.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.activities.contract.dto.ContractCostPageDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDto;
import com.biz.crm.tpm.business.activities.contract.service.ContractCostComponent;
import com.biz.crm.tpm.business.activities.contract.service.ExternalContractService;
import com.biz.crm.tpm.business.activities.job.contract.ContractCostJob;
import com.biz.crm.tpm.business.activities.marketingplan.constant.MarketingPlanConstant;
import com.biz.crm.tpm.business.activities.sdk.service.ExternalContractVoService;
import com.biz.crm.tpm.business.activities.sdk.vo.ExternalContractSdkVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/8 21:43
 */
@RestController
@RequestMapping("/v1/contractCostController")
@Api(tags = "合同费用台账")
public class ContractCostController {


    @Autowired
    private ExternalContractService externalContractService;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private ContractCostComponent contractCostComponent;

    @Resource
    private ContractCostJob contractCostJob;

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private ExternalContractVoService externalContractVoService;


    @ApiOperation(value = "查询分页列表")
    @GetMapping("findList")
    public Result<Page<ContractCostPageDto>> findList(@PageableDefault(50) Pageable pageable, ContractCostPageDto vo) {
        vo.setExternalFlag(BooleanEnum.FALSE.getCapital());
        return Result.ok(externalContractService.findList(pageable, vo));
    }

    @ApiOperation(value = "创建费用合同")
    @PostMapping("createContractCost")
    public Result createContractCost(@RequestBody ExternalContractDto dto) {
        dto.setExternalFlag(BooleanEnum.FALSE.getCapital());
        externalContractService.createContract(dto, BooleanEnum.FALSE.getCapital());
        return Result.ok();
    }

    @ApiOperation(value = "生成合同")
    @GetMapping("generateContractMarketing")
    public Result generateContractMarketing(@RequestParam String years) {
        String lockKey = MarketingPlanConstant.getMarketingContractLockKey(years);
        Boolean locked = redisLockService.isLock(lockKey);
        if (locked) {
            return Result.error(String.format("当前年月%s正在生成合同方案,请勿重复操作!", years));
        }
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        contractCostComponent.generateContractMarketing(years, lockKey, loginDetails);
        return Result.ok();
    }

    @ApiOperation(value = "生成合同费用")
    @GetMapping("contractCostJob")
    public Result contractCostJob() {
        contractCostJob.generateContractMarketing();
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "删除")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "ids", value = "主键id集合") @RequestParam List<String> ids) {
        this.externalContractService.delete(ids);
        return Result.ok();
    }

    /**
     * 批量根据id启用
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量根据id启用")
    @PatchMapping(value = "enable")
    public Result<?> enable(@RequestBody List<String> ids) {
        try {
            this.externalContractService.enable(ids);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量根据id禁用
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量根据id禁用")
    @PatchMapping(value = "disable")
    public Result<?> disable(@RequestBody List<String> ids) {
        try {
            this.externalContractService.disable(ids);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "查询合同临近到期的客户信息")
    @GetMapping("findContractNearDate")
    public Result<List<ExternalContractSdkVo>> findContractNearDate() {
        return Result.ok(externalContractVoService.findContractNearDate());
    }
}
