package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectBudget;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectBudgetRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectBudgetService;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectSchemeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/30 18:04
 */
@Service("regionCollectBudgetService")
@Slf4j
public class RegionCollectBudgetServiceImpl implements RegionCollectBudgetService {

    @Resource
    private RegionCollectBudgetRepository collectBudgetRepository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;


    /**
     * 保存
     *
     * @param list
     * @param collectCode
     */
    @Override
    public void saveBatchList(List<RegionCollectBudgetVo> list, String collectCode) {
        if (CollectionUtils.isEmpty(list))return;
        List<RegionCollectBudget> regionCollectBudgets = (List<RegionCollectBudget>) nebulaToolkitService.copyCollectionByWhiteList(list, RegionCollectBudgetVo.class,
                RegionCollectBudget.class, HashSet.class, ArrayList.class);
        regionCollectBudgets.forEach(x -> x.setCollectCode(collectCode));
        collectBudgetRepository.saveBatch(regionCollectBudgets);
    }


    /**
     * 查询
     *
     * @param collectCode
     * @return
     */
    @Override
    public List<RegionCollectBudgetVo> findByCollectCode(String collectCode) {
        List<RegionCollectBudget> budgets = collectBudgetRepository.findByCollectCode(collectCode);
        if (CollectionUtils.isEmpty(budgets)) {
            return Lists.newArrayList();
        }
        return (List<RegionCollectBudgetVo>) nebulaToolkitService.copyCollectionByWhiteList(budgets, RegionCollectBudget.class,
                RegionCollectBudgetVo.class, HashSet.class, ArrayList.class);
    }


    /**
     * 查询汇总数据
     * @param collectCode
     * @return
     */
    @Override
    public List<RegionCollectBudgetVo> findCollectByCollectCode(String collectCode) {
        return collectBudgetRepository.findCollectByCollectCode(collectCode);
    }


    @Override
    public void deleteByCollectCode(String collectCode) {
        collectBudgetRepository.deleteByCollectCode(collectCode);
    }
}
