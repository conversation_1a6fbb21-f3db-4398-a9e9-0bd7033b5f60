package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureDetailVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureVo;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:23
 */
public interface PlanClosureDetailService {

    List<PlanClosureDetailVo> findListByCloseCodes(List<String> closeCodes);

    void saveBatchList(List<PlanClosureDetailVo> detailVoList,String closeCode);

    void deleteByCloseCodes(List<String> closeCodes);
}
