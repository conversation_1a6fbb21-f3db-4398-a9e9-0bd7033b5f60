<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.activities.scheme.mapper.ActivitiesSchemeMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesScheme" id="ActivitiesSchemeMap">
  </resultMap>


  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeVo">
    select
    t.*
    from tpm_activities_scheme t
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != '' ">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus != '' ">
        and t.enable_status = #{dto.enableStatus}
      </if>
      <if test="dto.activitiesCode !=null and dto.activitiesCode != '' ">
        and t.activities_code = #{dto.activitiesCode}
      </if>
      <if test="dto.activitiesName !=null and dto.activitiesName != '' ">
        and t.activities_name like concat('%',#{dto.activitiesName},'%')
      </if>
      <if test="dto.status !=null and dto.status != '' ">
        and t.status = #{dto.status}
      </if>
      and t.del_flag = '${@<EMAIL>()}'
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>