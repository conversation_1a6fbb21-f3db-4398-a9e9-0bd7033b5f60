package com.biz.crm.tpm.business.activities.marketingplan.helper;

import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.tpm.business.activities.marketingplan.constant.PlanClosureConstant;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanClosureDetailService;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanClosureService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureDetailVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/5 19:40
 */
@Component
@Slf4j
public class PlanClosurePageCacheHelper extends BusinessPageCacheHelper<PlanClosureDetailVo, PlanClosureDetailVo> {


    @Resource
    private PlanClosureService planClosureService;


    @Override
    public String getCacheKeyPrefix() {
        return PlanClosureConstant.PLAN_CLOSURE_CACHE_PAGE;
    }

    @Override
    public Class<PlanClosureDetailVo> getDtoClass() {
        return PlanClosureDetailVo.class;
    }

    @Override
    public Class<PlanClosureDetailVo> getVoClass() {
        return PlanClosureDetailVo.class;
    }

    @Override
    public List<PlanClosureDetailVo> findDtoListFromRepository(PlanClosureDetailVo vo, String cacheKey) {
        if (ObjectUtils.isEmpty(vo.get("id")) && ObjectUtils.isEmpty(vo.getId())) {
            return Lists.newArrayList();
        }
        String id = null;
        if (ObjectUtils.isNotEmpty(vo.get("id"))) {
            id = vo.getString("id");
        } else if (ObjectUtils.isNotEmpty(vo.getId())) {
            id = vo.getId();
        }
        return planClosureService.findListById(id);
    }

    @Override
    public List<PlanClosureDetailVo> newItem(String cacheKey, List<PlanClosureDetailVo> itemList) {
        return itemList;
    }

    @Override
    public List<PlanClosureDetailVo> copyItem(String cacheKey, List<PlanClosureDetailVo> itemList) {
        List<PlanClosureDetailVo> list = itemList;
        for (PlanClosureDetailVo caseVo : list) {
            caseVo.put("id", UuidCrmUtil.general());
            caseVo.setCloseDetailCode(null);
        }
        return list;
    }

    @Override
    public Object getDtoKey(PlanClosureDetailVo vo) {
        return vo.get("id");
    }

    @Override
    public String getCheckedStatus(PlanClosureDetailVo vo) {
        return vo.getString("checked");
    }
}
