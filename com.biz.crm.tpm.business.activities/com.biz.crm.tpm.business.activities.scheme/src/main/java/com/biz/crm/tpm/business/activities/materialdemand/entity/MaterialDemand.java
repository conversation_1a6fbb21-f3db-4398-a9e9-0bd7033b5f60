package com.biz.crm.tpm.business.activities.materialdemand.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.WorkflowFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_material_demand")
@Table(
        name = "tpm_material_demand",
        indexes = {
                @Index(name = "tpm_material_demand_index0", columnList = "tenant_code,demand_code", unique = true),
                @Index(name = "tpm_material_demand_index2", columnList = "del_flag")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_material_demand", comment = "物料需求提报")
@ApiModel(value = "MaterialDemandEntity", description = "物料需求提报")
public class MaterialDemand extends WorkflowFlagOpEntity {

    @ApiModelProperty("编码")
    @Column(name = "demand_code", columnDefinition = "varchar(32) comment '编码'")
    private String demandCode;

    @ApiModelProperty("名称")
    @Column(name = "demand_name", columnDefinition = "varchar(64) comment '名称'")
    private String demandName;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(10) comment '年月'")
    private String years;

    @ApiModelProperty("汇总状态")
    @Column(name = "collect_status", columnDefinition = "varchar(10) DEFAULT 'N' comment '汇总状态'")
    private String collectStatus;

}
