package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlVoService;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/6 00:19
 */
@Component
public class PlanCaseMatchControlBudgetComponent {

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private BudgetSubjectsVoService budgetSubjectsVoService;

    @Resource
    private BudgetControlVoService budgetControlVoService;

    public List<String> matchControlBudgetByPlanCase(MarketingPlanCase caseVo) {

        String years = caseVo.getYears();
        String departmentCode = caseVo.getBearDepartmentCode();
        List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(departmentCode);
        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
        String customerCode = caseVo.getCustomerCode();
        String budgetSubjectCode = caseVo.getBudgetSubjectCode();
        Set<String> budgetSubjectCodeSet = budgetSubjectsVoService.findAllParentSubjectCodesByCodes(budgetSubjectCode);
        List<BudgetControlVo> budgetControlVoList = budgetControlVoService.matchBudgetControl(years, orgCodes, customerCode, budgetSubjectCodeSet);
        Validate.isTrue(!CollectionUtils.isEmpty(budgetControlVoList), String.format("方案%s的方案明细%s未匹配到管控规则", caseVo.getSchemeCode(), caseVo.getSchemeDetailCode()));
        List<String> controlCode = budgetControlVoList.stream().map(x -> x.getControlCode()).collect(Collectors.toList());
        return controlCode;
    }
}
