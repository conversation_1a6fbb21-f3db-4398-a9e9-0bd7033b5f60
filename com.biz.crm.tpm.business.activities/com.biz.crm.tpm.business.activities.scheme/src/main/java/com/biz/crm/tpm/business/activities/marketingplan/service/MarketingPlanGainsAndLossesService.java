package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanGainsAndLossesVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/4 10:17
 */
public interface MarketingPlanGainsAndLossesService {

    Page<MarketingPlanGainsAndLosses> findList(Pageable pageable,MarketingPlanGainsAndLosses vo);

    List<MarketingPlanGainsAndLosses> findListBySchemeCodes(List<String> schemeCodes);

    /**
     * 保存明细数据
     * @param list
     * @param schemeCode
     */
    void saveBatchList(List<MarketingPlanGainsAndLossesVo> list,String schemeCode);

    void deleteBySchemeCodes(List<String> schemeCodes);
}
