<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectMapper">

    <select id="findList" resultType="com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo">
        select * from tpm_region_collect
        <where>
            del_flag = '${@<EMAIL>()}'
            <if test="vo.tenantcode != null and vo.tenantCode != ''">
                and tenant_code = #{vo.tenantCode}
            </if>
            <if test="vo.collectCode != null and vo.collectCode != ''">
                <bind name="collectCode" value="vo.collectCode + '%'"/>
                and collect_code like #{collectCode}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and years = #{vo.years}
            </if>
            <if test="vo.orgName != null and vo.orgName != ''">
                <bind name="orgName" value="'%' + vo.orgName + '%'"/>
                and org_name like #{orgName}
            </if>
            <if test="vo.orgCode != null and vo.orgCode != ''">
                <bind name="orgCode" value="'%' + vo.orgCode + '%'"/>
                and org_code like #{orgCode}
            </if>
            <if test="vo.processStatus != null and vo.processStatus != ''">
                and process_status = #{vo.processStatus}
            </if>
            <if test="vo.processKey != null and vo.processKey != ''">
                <bind name="processKey" value="'%' + vo.processKey + '%'"/>
                and process_key like #{processKey}
            </if>
            <if test="vo.processNumber != null and vo.processNumber != ''">
                <bind name="processNumber" value="'%' + vo.processNumber + '%'"/>
                and process_number like #{processNumber}
            </if>
            <if test="vo.collectStatus != null and vo.collectStatus != ''">
                and collect_status = #{vo.collectStatus}
            </if>
            <if test="vo.createName != null and vo.createName != ''">
                <bind name="createName" value="'%' + vo.createName + '%'"/>
                and create_name like #{createName}
            </if>
            order by create_time desc
        </where>
    </select>

    <select id="findDepartmentReport" resultType="com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimation">
        select * from tpm_region_collect_department_estimation
        where collect_code = #{vo.collectCode}
        order by id desc
    </select>


    <select id="findGainsAndLossesReport" resultType="com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLosses">
        select * from tpm_region_collect_gains_and_losses
        where collect_code = #{vo.collectCode}
        order by id desc
    </select>


    <select id="findItemReport" resultType="com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectItemEstimation">
        select * from tpm_region_collect_item_estimation
        where collect_code = #{vo.collectCode}
        order by id desc
    </select>

    <select id="findDepartmentReportTotal" resultType="java.util.Map">
        select sum(budget_income) budgetIncome,sum(plan_income) planIncome from tpm_region_collect_department_estimation
        where collect_code = #{vo.collectCode}
    </select>


    <select id="findGainsAndLossesReportTotal" resultType="java.util.Map">
        select sum(budget_income) budgetIncome,sum(plan_income) planIncome from tpm_region_collect_gains_and_losses
        where collect_code = #{vo.collectCode}
    </select>


    <select id="findItemReportTotal" resultType="java.util.Map">
        select sum(budget_income) budgetIncome,sum(plan_income) planIncome from tpm_region_collect_item_estimation
        where collect_code = #{vo.collectCode}
    </select>


    <select id="findSchemeReport" resultType="com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectScheme">
        select * from tpm_region_collect_scheme
        where collect_code = #{vo.collectCode}
        order by scheme_code desc,id desc
    </select>

    <select id="findRegionMarketingCaseReport" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
            c.scheme_name,
            a.*,b.*
        FROM
            tpm_marketing_plan_case a
                LEFT JOIN tpm_marketing_plan_case_extend b ON a.scheme_detail_code = b.scheme_detail_code
        left join tpm_marketing_plan c on a.scheme_code = c.scheme_code
        WHERE
            a.scheme_code in(
                SELECT
                    scheme_code FROM tpm_region_collect_scheme
                WHERE
                    scheme_code IS NOT NULL
                  AND collect_code = #{collectCode})
    </select>

    <select id="findRegionMarketingCaseList" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT c.scheme_name,
               c.create_name,
               a.*,
               b.*
        FROM tpm_marketing_plan_case a
                 LEFT JOIN tpm_marketing_plan_case_extend b ON a.scheme_detail_code = b.scheme_detail_code
                 left join tpm_marketing_plan c on a.scheme_code = c.scheme_code
        WHERE a.scheme_code in (SELECT scheme_code
                                FROM tpm_region_collect_scheme
                                WHERE scheme_code IS NOT NULL
                                  AND collect_code = #{collectCode})
    </select>

    <select id="findCommit" resultType="com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo">
        select * from tpm_region_collect
        <where>
            del_flag = '${@<EMAIL>()}'
            and years = #{yearMonthLy}
            and process_status = '2'
            order by create_time,id desc
        </where>
    </select>
    <select id="findRegionMarketingSalesPlanList"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo">
        SELECT
            a.*
        FROM
            tpm_marketing_sales_plan a
        WHERE
            a.scheme_code in (
                SELECT
                    scheme_code FROM tpm_region_collect_scheme
                WHERE
                    scheme_code IS NOT NULL
                  AND collect_code = #{collectCode})
    </select>
</mapper>

