package com.biz.crm.tpm.business.activities.materialdemand.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.materialdemand.constant.MaterialDemandConstant;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandCollect;
import com.biz.crm.tpm.business.activities.materialdemand.repository.MaterialDemandCollectRepository;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandCollectDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandCollectOaService;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandCollectService;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@Service
@Transactional
@Slf4j
public class MaterialDemandCollectServiceImpl extends BusinessPageCacheServiceImpl<MaterialDemandCollectDetailVo, MaterialDemandCollectDetailVo> implements MaterialDemandCollectService {


    @Resource
    private MaterialDemandCollectRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Autowired
    private MaterialDemandCollectDetailService collectDetailService;

    @Resource
    private OrgVoService orgVoService;

    @Autowired
    private MaterialDemandService materialDemandService;

    @Resource
    private GenerateCodeService generateCodeService;

    @Autowired
    private MaterialDemandCollectOaService materialDemandCollectOaService;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;


    /**
     * 分页查询
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MaterialDemandCollectVo> findList(Pageable pageable, MaterialDemandCollectVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MaterialDemandCollectVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return repository.findList(page, vo);
    }

    /**
     * 查询详情
     *
     * @param id
     * @param code
     * @return
     */
    @Override
    public MaterialDemandCollectVo queryByIdOrCode(String id, String code) {
        MaterialDemandCollect collect = repository.queryByIdOrCode(id, code);
        Validate.notNull(collect, "查询数据为空");
        MaterialDemandCollectVo vo = nebulaToolkitService.copyObjectByWhiteList(collect, MaterialDemandCollectVo.class, HashSet.class, ArrayList.class);
        List<MaterialDemandCollectDetailVo> collectDetailList = collectDetailService.findListByCodes(Lists.newArrayList(collect.getCollectCode()));
        vo.setDetailList(collectDetailList);
        return vo;
    }

    /**
     * 创建
     *
     * @param vo
     */
    @Override
    public void create(MaterialDemandCollectVo vo) {
        Validate.notNull(vo.getCacheKey(), "cacheKey不能为空");
        List<MaterialDemandCollectDetailVo> demandCollectDetailVos = findCacheList(vo.getCacheKey());
        vo.setDetailList(demandCollectDetailVos);
        this.validateData(vo);
        MaterialDemandCollect collect = nebulaToolkitService.copyObjectByWhiteList(vo, MaterialDemandCollect.class, HashSet.class, ArrayList.class);
        collect.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        collect.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        collect.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
        collect.setTenantCode(TenantUtils.getTenantCode());
        collect.setYears(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH)));
        collect.setCollectCode(generateCodeService.generateCode(MaterialDemandConstant.MATERIAL_DEMAND_COLLECT_RULE));
        repository.save(collect);
        collectDetailService.saveBatchList(vo.getDetailList(), collect.getCollectCode());
    }

    /**
     * 修改
     *
     * @param vo
     */
    @Override
    public void update(MaterialDemandCollectVo vo) {
        Validate.notNull(vo.getCacheKey(), "cacheKey不能为空");
        Validate.notNull(vo.getId(), "主键Id不能为空");
        MaterialDemandCollect collect = repository.queryByIdOrCode(vo.getId(), null);
        Validate.isTrue(StringUtils.equalsAny(collect.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode()), "不是待提交状态不可编辑");
        List<MaterialDemandCollectDetailVo> demandCollectDetailVos = findCacheList(vo.getCacheKey());
        vo.setDetailList(demandCollectDetailVos);
        this.validateData(vo);
        collect.setCollectName(vo.getCollectName());
        repository.updateById(collect);
        collectDetailService.saveBatchList(vo.getDetailList(), collect.getCollectCode());
    }

    /**
     * 新增提交
     *
     * @param vo
     */
    @Override
    public void submitCreate(MaterialDemandCollectVo vo) {
        Validate.notNull(vo.getCacheKey(), "cacheKey不能为空");
        List<MaterialDemandCollectDetailVo> demandCollectDetailVos = findCacheList(vo.getCacheKey());
        vo.setDetailList(demandCollectDetailVos);
        this.validateData(vo);
        MaterialDemandCollect collect = nebulaToolkitService.copyObjectByWhiteList(vo, MaterialDemandCollect.class, HashSet.class, ArrayList.class);
        collect.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        collect.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        collect.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
        collect.setTenantCode(TenantUtils.getTenantCode());
        collect.setYears(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH)));
        collect.setCollectCode(generateCodeService.generateCode(MaterialDemandConstant.MATERIAL_DEMAND_COLLECT_RULE));
        repository.save(collect);
        collectDetailService.saveBatchList(vo.getDetailList(), collect.getCollectCode());
        List<String> demandCodes = vo.getDetailList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getDemandCode()))
                .map(MaterialDemandCollectDetailVo::getDemandCode).distinct().collect(Collectors.toList());
        materialDemandService.updateProcessStatus(ProcessStatusEnum.COMMIT.getDictCode(), demandCodes);

        collect.setDemandCollectDetailVos(demandCollectDetailVos);
        materialDemandCollectOaService.pushOa(collect);
    }

    /**
     * 提交编辑
     *
     * @param vo
     */
    @Override
    public void submitUpdate(MaterialDemandCollectVo vo) {
        Validate.notNull(vo.getCacheKey(), "cacheKey不能为空");
        Validate.notNull(vo.getId(), "主键Id不能为空");
        MaterialDemandCollect collect = repository.queryByIdOrCode(vo.getId(), null);
        Validate.isTrue(StringUtils.equalsAny(collect.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode()), "不是待提交状态不可编辑");
        List<MaterialDemandCollectDetailVo> demandCollectDetailVos = findCacheList(vo.getCacheKey());
        vo.setDetailList(demandCollectDetailVos);
        this.validateData(vo);
        collect.setCollectName(vo.getCollectName());
        collect.setRemark(vo.getRemark());
        collect.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
        repository.updateById(collect);
        collectDetailService.saveBatchList(vo.getDetailList(), collect.getCollectCode());
        List<String> demandCodes = vo.getDetailList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getDemandCode()))
                .map(MaterialDemandCollectDetailVo::getDemandCode).distinct().collect(Collectors.toList());
        materialDemandService.updateProcessStatus(ProcessStatusEnum.COMMIT.getDictCode(), demandCodes);

        collect.setDemandCollectDetailVos(demandCollectDetailVos);
        materialDemandCollectOaService.pushOa(collect);
    }

    /**
     * 审批回调
     *
     * @param vo
     */
    @Override
    public void callback(MaterialDemandCollectVo vo) {
        MaterialDemandCollect collect = repository.queryByIdOrCode(null, vo.getCollectCode());
        if (ObjectUtils.isNotEmpty(collect)) {
            collect.setProcessStatus(vo.getProcessStatus());
            repository.updateById(collect);
            List<MaterialDemandCollectDetailVo> collectDetails = collectDetailService.findListByCodes(Lists.newArrayList(collect.getCollectCode()));
            List<String> demandCodes = collectDetails.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDemandCode()))
                    .map(MaterialDemandCollectDetailVo::getDemandCode).distinct().collect(Collectors.toList());
            materialDemandService.updateProcessStatus(vo.getProcessStatus(), demandCodes);
        }

    }

    /**
     * 手动审批
     *
     * @param ids
     */
    @Override
    public void manualApproval(List<String> ids) {
        List<MaterialDemandCollect> collectList = repository.findListByIdList(ids);
        collectList.forEach(x -> x.setProcessStatus(ProcessStatusEnum.PASS.getDictCode()));
        List<String> collectCodes = collectList.stream().map(MaterialDemandCollect::getCollectCode).collect(Collectors.toList());
        List<MaterialDemandCollectDetailVo> collectDetailVos = collectDetailService.findListByCodes(collectCodes);
        List<String> demandCodes = collectDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDemandCode()))
                .map(MaterialDemandCollectDetailVo::getDemandCode).distinct().collect(Collectors.toList());
        materialDemandService.updateProcessStatus(ProcessStatusEnum.PASS.getDictCode(), demandCodes);
    }

    private void validateData(MaterialDemandCollectVo vo) {
        Validate.notNull(vo.getOrgCode(), "组织编码不能为空");
        vo.setYears(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH)));
        Validate.isTrue(CollectionUtils.isNotEmpty(vo.getDetailList()), "汇总明细不能为空");
        for (MaterialDemandCollectDetailVo detailVo : vo.getDetailList()) {
            Validate.notNull(detailVo.getDemandDetailCode(), "需求提报明细编码不能为空");
        }
        //if (flag) {
        //    MaterialDemandCollect collect = repository.findByOrgCodeAndYears(vo.getOrgCode(), vo.getYears());
        //    Validate.isTrue(ObjectUtils.isEmpty(collect), String.format("当前组织%s在%s年月存在物料汇总数据", vo.getOrgCode(), vo.getYears()));
        //}
    }

    @Override
    public String deleteBatchByIds(List<String> ids) {
        List<MaterialDemandCollect> collects = repository.findListByIdList(ids);
        String codes = collects.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(),
                ProcessStatusEnum.COMMIT.getDictCode()))
                .map(MaterialDemandCollect::getCollectCode).collect(Collectors.joining("、"));
        if (CollectionUtils.isNotEmpty(collects)) {
            List<MaterialDemandCollect> deleteList = collects.stream().filter(x ->
                    StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                            ProcessStatusEnum.RECOVER.getDictCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteList)) {
                deleteList.forEach(x -> x.setDelFlag(DelFlagStatusEnum.DELETE.getCode()));
                repository.updateBatchById(deleteList);
                List<String> collectCodes = deleteList.stream().map(MaterialDemandCollect::getCollectCode).collect(Collectors.toList());
                collectDetailService.deleteByCollectCodeList(collectCodes);

                //删除OA接口
                deleteList = collects.stream().filter(x ->
                        StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.REJECT.getDictCode(),
                                ProcessStatusEnum.RECOVER.getDictCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(deleteList)) {
                    deleteList.forEach(e -> ryOaProcessService.deleteWorkflow(e.getProcessNumber(), e.getOaId()));
                }
            }
        }
        return codes;
    }

    /**
     * 获取物料信息汇总
     *
     * @param orgCode
     * @return
     */
    @Override
    public List<MaterialDemandCollectDetailVo> collectMaterial(String orgCode, String cacheKey) {
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(orgCode);
        if (CollectionUtils.isEmpty(orgVoList)) {
            return Lists.newArrayList();
        }
        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
        List<MaterialDemandDetailVo> demandDetailVos = materialDemandService.findListByOrgCodes(orgCodes, Lists.newArrayList(ProcessStatusEnum.PREPARE.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode(), ProcessStatusEnum.REJECT.getDictCode()));
        if (CollectionUtils.isEmpty(demandDetailVos)) {
            return Lists.newArrayList();
        }
        List<MaterialDemandCollectDetailVo> dataList = (List<MaterialDemandCollectDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(demandDetailVos,
                MaterialDemandDetailVo.class, MaterialDemandCollectDetailVo.class, HashSet.class, ArrayList.class);
        helper.putCache(cacheKey, dataList);
        List<MaterialDemandCollectDetailVo> resultList = Lists.newArrayList();
        for (Map.Entry<String, List<MaterialDemandCollectDetailVo>> entry : dataList.stream().collect(Collectors.groupingBy(x -> x.getMaterialCode() + ":" + x.getMaterialName()))
                .entrySet()) {
            String[] keys = entry.getKey().split(":");
            MaterialDemandCollectDetailVo vo = new MaterialDemandCollectDetailVo();
            vo.setMaterialCode(keys[0]);
            vo.setMaterialName(keys[1]);
            BigDecimal materialNum = entry.getValue().stream().map(MaterialDemandCollectDetailVo::getMaterialNum)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setMaterialNum(materialNum);
            BigDecimal materialAmount = entry.getValue().stream().map(MaterialDemandCollectDetailVo::getMaterialAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setMaterialAmount(materialAmount);
            resultList.add(vo);
        }
        return resultList;
    }


    /**
     * 获取物料分组
     *
     * @param pageable
     * @param cacheKey
     * @return
     */
    @Override
    public Page<MaterialDemandCollectDetailVo> findGroupMaterialDemand(Pageable pageable, String cacheKey) {
        if (StringUtil.isEmpty(cacheKey)) {
            return new Page<>();
        }
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        Page<MaterialDemandCollectDetailVo> detailVoPage = new Page<>();
        List<MaterialDemandCollectDetailVo> collectDetailVos = findCacheList(cacheKey);
        List<MaterialDemandCollectDetailVo> totalList = Lists.newArrayList();
        for (Map.Entry<String, List<MaterialDemandCollectDetailVo>> entry : collectDetailVos.stream().collect(Collectors.groupingBy(x -> x.getMaterialCode() + ":" + x.getMaterialName()))
                .entrySet()) {
            String[] keys = entry.getKey().split(":");
            MaterialDemandCollectDetailVo vo = new MaterialDemandCollectDetailVo();
            vo.setMaterialCode(keys[0]);
            vo.setMaterialName(keys[1]);
            BigDecimal materialNum = entry.getValue().stream().map(MaterialDemandCollectDetailVo::getMaterialNum)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setMaterialNum(materialNum);
            BigDecimal materialAmount = entry.getValue().stream().map(MaterialDemandCollectDetailVo::getMaterialAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setMaterialAmount(materialAmount);
            totalList.add(vo);
        }
        if (CollectionUtil.isEmpty(totalList)) {
            return new Page<>();
        }
        int totalSize = totalList.size();
        List<MaterialDemandCollectDetailVo> result = totalList.stream()
                .skip(pageable.getPageSize() * (pageable.getPageNumber() > 0 ? pageable.getPageNumber() - 1 : 0))
                .limit(pageable.getPageSize()).collect(Collectors.toList());
        detailVoPage.setRecords(result);
        detailVoPage.setPages(totalSize / pageable.getPageSize() + (totalSize % pageable.getPageSize() == 0 ? 0 : 1));
        detailVoPage.setCurrent(pageable.getPageNumber());
        detailVoPage.setSize(pageable.getPageSize());
        detailVoPage.setTotal(totalSize);
        return detailVoPage;
    }


    /**
     * 选择物资需求列表
     *
     * @param cacheKey
     * @param demandCodes
     */
    @Override
    public void selectMaterialDemandDetailList(String cacheKey, List<String> demandCodes) {
        List<MaterialDemandCollectDetailVo> collectDetailVos = findCacheList(cacheKey);
        List<String> demandDetailCodes = collectDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDemandDetailCode()))
                .map(MaterialDemandCollectDetailVo::getDemandDetailCode).collect(Collectors.toList());
        List<MaterialDemandDetailVo> demandDetailVos = materialDemandService.selectMaterialDemandDetailList(demandDetailCodes, demandCodes);
        if (CollectionUtils.isEmpty(demandDetailVos)) {
            return;
        }
        List<MaterialDemandCollectDetailVo> dataList = (List<MaterialDemandCollectDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(demandDetailVos,
                MaterialDemandDetailVo.class, MaterialDemandCollectDetailVo.class, HashSet.class, ArrayList.class);
        helper.putCache(cacheKey, dataList);
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public void recover(String code, String remark) {
        MaterialDemandCollect entity = repository.queryByIdOrCode(null, code);
        if (materialDemandCollectOaService.oaWithdraw(entity, remark)) {
            entity.setProcessStatus(ProcessStatusEnum.RECOVER.getDictCode());
            repository.saveOrUpdate(entity);
        }
    }
}
