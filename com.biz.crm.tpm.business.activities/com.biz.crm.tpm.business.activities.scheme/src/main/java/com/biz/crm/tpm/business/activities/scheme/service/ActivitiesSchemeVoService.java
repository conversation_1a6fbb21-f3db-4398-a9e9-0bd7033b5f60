package com.biz.crm.tpm.business.activities.scheme.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.scheme.dto.ActivitiesSchemeDto;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 方案活动;(tpm_activities_scheme)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-1
 */
public interface ActivitiesSchemeVoService {

  /**
   * 生成操作标记
   */
  String preSave();

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<ActivitiesSchemeVo> findByConditions(Pageable pageable, ActivitiesSchemeDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  ActivitiesSchemeVo findById(String id);

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  ActivitiesSchemeVo findByCode(String code);

  /**
   * 新增数据
   *
   * @param json Json对象
   * @return 新增结果
   */
  ActivitiesSchemeVo create(JSONObject json);

  /**
   * 修改新据
   *
   * @param json Json对象
   * @return 修改结果
   */
  ActivitiesSchemeVo update(JSONObject json);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(List<String> ids);

  /**
   * 通过启用状态查询数据
   *
   * @param enableStatus 状态
   * @return 集合数据
   */
  List<ActivitiesSchemeVo> findByEnableStatus(String enableStatus);

  /**
   * 批量根据id启用
   *
   * @param ids
   */
  void enable(List<String> ids);

  /**
   * 批量根据id禁用
   *
   * @param ids
   */
  void disable(List<String> ids);

  /**
   * 根据方案编号统计被活动使用的方案数量
   *
   * @param schemeCode
   * @return
   */
  int countByScheme(String schemeCode);
}
