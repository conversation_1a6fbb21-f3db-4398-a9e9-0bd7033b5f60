package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanBackGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanBackGainsAndLossesMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:42
 */
@Component
public class MarketingPlanBackGainsAndLossesRepository extends ServiceImpl<MarketingPlanBackGainsAndLossesMapper, MarketingPlanBackGainsAndLosses> {


    /**
     * 通过方案编码查询
     *
     * @param schemeCodes
     * @return
     */
    public List<MarketingPlanBackGainsAndLosses> findListBySchemeCodes(List<String> schemeCodes) {
        return this.lambdaQuery()
                .in(MarketingPlanBackGainsAndLosses::getChangeSchemeCode, schemeCodes)
                .list();
    }

}
