package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.*;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.ActExecutionCollectEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.*;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.service.PlanBigDateExecutionCollectService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.service.internal.PlanBigDateExecutionCollectServiceImpl
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:12
 */
@Service
@Slf4j
public class PlanBigDateExecutionCollectServiceImpl implements PlanBigDateExecutionCollectService {

    @Autowired(required = false)
    private PlanBigDateExecutionCollectRepository planBigDateExecutionCollectRepository;

    @Autowired(required = false)
    private PlanBigDateExecutionCollectDetailRepository planBigDateExecutionCollectDetailRepository;

    @Autowired(required = false)
    private PlanBigDateExecutionCollectDetailChildRepository planBigDateExecutionCollectDetailChildRepository;

    @Autowired(required = false)
    private PlanBigDateExecutionCollectDetailPictureRepository planBigDateExecutionCollectDetailPictureRepository;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private MarketingPlanCaseRepository marketingPlanCaseRepository;

    @Autowired(required = false)
    private MarketingPlanCaseExtendRepository marketingPlanCaseExtendRepository;

    @Autowired(required = false)
    private ActExecutionCollectLogRepository actExecutionCollectLogRepository;


    @Override
    @Transactional
    public void create(PlanBigDateExecutionCollectVo vo) {
        Validate.notNull(vo, "大日期回调执行采集数据不能为空");
        PlanBigDateExecutionCollect collect = JsonUtils.convert(vo, PlanBigDateExecutionCollect.class);
        //基础信息校验
        this.baseCheck(collect);
        AbstractCrmUserIdentity loginDetails = this.loginUserService.getAbstractLoginUser();
        Validate.notNull(loginDetails, "未查询到当前执行人信息");
        PositionVo positionVo = this.positionVoService.findByPositionCode(loginDetails.getPostCode());
        Validate.notNull(positionVo, "未查询到当前执行人职位信息");

        //执行活动明细校验
        MarketingPlanCase marketingPlanCase = this.marketingPlanCaseRepository.findBySchemeDetailCode(collect.getSchemeDetailCode());
        Validate.notNull(marketingPlanCase, "未查询到活动明细数据");

        //基础信息字段赋值
        collect.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        collect.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        collect.setExecutor(positionVo.getFullName());
        collect.setExecuteDate(new Date());
        collect.setCreatePostCode(positionVo.getPositionCode());
        collect.setCreatePostName(positionVo.getPositionName());
        collect.setCreateOrgCode(positionVo.getOrgCode());
        collect.setCreateOrgName(positionVo.getOrgName());
        collect.setExecutionTime(DateUtil.dateStrNowYYYYMMDD());
        //tpm这边的啥活动执行编码
        collect.setActExecuteCode(marketingPlanCase.getActExecuteCode());
        this.planBigDateExecutionCollectRepository.save(collect);
        String id = collect.getId();

        //活动执行记录构建
        ActExecutionCollectLog collectLog = new ActExecutionCollectLog();
        collectLog.setExecutionId(id);
        collectLog.setClientCode(collect.getCustomerCode());
        collectLog.setClientName(collect.getCustomerName());
        collectLog.setSchemeCode(collect.getSchemeCode());
        collectLog.setSchemeDetailCode(collect.getSchemeDetailCode());
        collectLog.setExecutionType(ActExecutionCollectEnum.CUSTOMER_COLLECT.getCode());
        //采集时间 当前年月日
        collectLog.setExecutionTime(DateUtil.dateStrNowYYYYMMDD());
        collectLog.setPositionCode(collect.getCreatePostCode());
        collectLog.setPositionName(collect.getCreatePostName());
        this.actExecutionCollectLogRepository.save(collectLog);


        //商品明细行
        collect.getExpirationBackProducts().forEach(x -> {
            x.setCollectId(id);
            x.setId(null);
            x.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            x.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        });
        //明细行保存
        this.planBigDateExecutionCollectDetailRepository.saveBatch(collect.getExpirationBackProducts());
        List<PlanBigDateExecutionCollectDetailChild> expirationBackBatchs = new ArrayList<>();
        List<PlanBigDateExecutionCollectDetailPicture> expirationBackPictures = new ArrayList<>();
        collect.getExpirationBackProducts().forEach(detail -> {
            if (CollectionUtils.isNotEmpty(detail.getExpirationBackBatchs())) {
                detail.getExpirationBackBatchs().forEach(child -> {
                    child.setCollectId(id);
                    child.setDetailId(detail.getId());
                    child.setId(null);
                    child.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    child.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    expirationBackBatchs.add(child);
                });
            }
            if (CollectionUtils.isNotEmpty(detail.getExpirationBackPictures())) {
                detail.getExpirationBackPictures().forEach(child -> {
                    child.setCollectId(id);
                    child.setDetailId(detail.getId());
                    child.setId(null);
                    expirationBackPictures.add(child);
                });
            }
        });
        //批次，采集图片保存
        this.planBigDateExecutionCollectDetailChildRepository.saveBatch(expirationBackBatchs);
        this.planBigDateExecutionCollectDetailPictureRepository.saveBatch(expirationBackPictures);

    }

    //基础信息校验
    private void baseCheck(PlanBigDateExecutionCollect collect) {
        Validate.notNull(collect, "大日期回调执行采集数据不能为空");
        Validate.notBlank(collect.getSchemeCode(), "方案编码不能为空");
        Validate.notBlank(collect.getSchemeDetailCode(), "方案明细编码不能为空");
        Validate.notBlank(collect.getCustomerCode(), "客户编码不能为空");

        Validate.notEmpty(collect.getExpirationBackProducts(), "商品信息不能为空");
        for (PlanBigDateExecutionCollectDetail expirationBackProduct : collect.getExpirationBackProducts()) {
            //商品基础信息验证
            Validate.notBlank(expirationBackProduct.getProductCode(), "商品编码不能为空");
            Validate.notBlank(expirationBackProduct.getProductName(), "商品名称不能为空");
            Validate.notBlank(expirationBackProduct.getBarCode(), "商品条码不能为空");
            Validate.notBlank(expirationBackProduct.getMaterialCode(), "商品物料编码不能为空");
            Validate.notEmpty(expirationBackProduct.getExpirationBackBatchs(), "商品批次信息不能为空");
            //批次信息校验
            if (CollectionUtils.isNotEmpty(expirationBackProduct.getExpirationBackBatchs())) {
                for (PlanBigDateExecutionCollectDetailChild expirationBackBatch : expirationBackProduct.getExpirationBackBatchs()) {
                    Validate.notBlank(expirationBackBatch.getBatchCode(), "批次号不能为空");
                    Validate.notNull(expirationBackBatch.getQuantity(), "数量不能为空");
                    Validate.isTrue(expirationBackBatch.getQuantity().compareTo(BigDecimal.ZERO) >= 0, "数量不能小于零");
                }
            }
        }
    }

    @Override
    public PlanBigDateExecutionCollectVo findDetailByQueryDto(PlanBigDateExecutionCollectVo dto) {
        PlanBigDateExecutionCollect collect = this.planBigDateExecutionCollectRepository.findByDto(dto);
        if (Objects.isNull(collect)) {
            return null;
        }
        //产品明细行信息
        List<PlanBigDateExecutionCollectDetail> expirationBackProducts = this.planBigDateExecutionCollectDetailRepository.findByCollectId(collect.getId());
        if (CollectionUtils.isEmpty(expirationBackProducts)) {
            return JsonUtils.convert(collect, PlanBigDateExecutionCollectVo.class);
        }
        collect.setExpirationBackProducts(expirationBackProducts);
        //关联明细行的批次，采集图片信息
        List<PlanBigDateExecutionCollectDetailChild> expirationBackBatchs = this.planBigDateExecutionCollectDetailChildRepository.findByCollectId(collect.getId());
        List<PlanBigDateExecutionCollectDetailPicture> expirationBackPictures = this.planBigDateExecutionCollectDetailPictureRepository.findByCollectId(collect.getId());
        Map<String, List<PlanBigDateExecutionCollectDetailChild>> batchMap = expirationBackBatchs.stream().collect(Collectors.groupingBy(PlanBigDateExecutionCollectDetailChild::getDetailId));
        Map<String, List<PlanBigDateExecutionCollectDetailPicture>> picMap = expirationBackPictures.stream().collect(Collectors.groupingBy(PlanBigDateExecutionCollectDetailPicture::getDetailId));
        for (PlanBigDateExecutionCollectDetail expirationBackProduct : expirationBackProducts) {
            String id = expirationBackProduct.getId();
            if (batchMap.containsKey(id)) {
                expirationBackProduct.setExpirationBackBatchs(batchMap.get(id));
            }
            if (picMap.containsKey(id)) {
                expirationBackProduct.setExpirationBackPictures(picMap.get(id));
            }
        }
        return JsonUtils.convert(collect, PlanBigDateExecutionCollectVo.class);
    }

    @Override
    public List<PlanBigDateExecutionCollectVo> findListByActCode(String actExecuteCode) {
        List<PlanBigDateExecutionCollect> list = this.planBigDateExecutionCollectRepository.findListByActCode(actExecuteCode);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        //父级编码集合
        List<String> collectIds = list.stream().map(PlanBigDateExecutionCollect::getId).collect(Collectors.toList());
        //产品明细集合
        List<PlanBigDateExecutionCollectDetail> detailList = this.planBigDateExecutionCollectDetailRepository.findListByCollectIds(collectIds);
        //产品采集图片集合
        List<PlanBigDateExecutionCollectDetailPicture> pictureList = this.planBigDateExecutionCollectDetailPictureRepository.findListByCollectIds(collectIds);
        //产品批次明细信息集合
        List<PlanBigDateExecutionCollectDetailChild> childList = this.planBigDateExecutionCollectDetailChildRepository.findListByCollectIds(collectIds);

        Map<String, List<PlanBigDateExecutionCollectDetailPicture>> picMap = pictureList.stream().collect(Collectors.groupingBy(PlanBigDateExecutionCollectDetailPicture::getDetailId));
        Map<String, List<PlanBigDateExecutionCollectDetailChild>> childMap = childList.stream().collect(Collectors.groupingBy(PlanBigDateExecutionCollectDetailChild::getDetailId));
        if (CollectionUtils.isNotEmpty(detailList)) {
            //遍历补充采集图片，稽查信息
            for (PlanBigDateExecutionCollectDetail detail : detailList) {
                if (picMap.containsKey(detail.getId())) {
                    detail.setExpirationBackPictures(picMap.get(detail.getId()));
                }
                if (childMap.containsKey(detail.getId())) {
                    detail.setExpirationBackBatchs(childMap.get(detail.getId()));
                }
            }
        }
        Map<String, List<PlanBigDateExecutionCollectDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(PlanBigDateExecutionCollectDetail::getCollectId));
        //商品采集信息补充
        for (PlanBigDateExecutionCollect executionCollect : list) {
            if (detailMap.containsKey(executionCollect.getId())) {
                executionCollect.setExpirationBackProducts(detailMap.get(executionCollect.getId()));
            }
        }
        return JsonUtils.convert(list, List.class, PlanBigDateExecutionCollectVo.class);
    }

    @Override
    public PlanBigDateExecutionCollectVo findDetailById(String id) {
        PlanBigDateExecutionCollect collect = this.planBigDateExecutionCollectRepository.findById(id);
        if (Objects.isNull(collect)) {
            return null;
        }
        ArrayList<String> collectIds = Lists.newArrayList(collect.getId());
        //产品明细集合
        List<PlanBigDateExecutionCollectDetail> detailList = this.planBigDateExecutionCollectDetailRepository.findListByCollectIds(collectIds);
        //产品采集图片集合
        List<PlanBigDateExecutionCollectDetailPicture> pictureList = this.planBigDateExecutionCollectDetailPictureRepository.findListByCollectIds(collectIds);
        //产品批次明细信息集合
        List<PlanBigDateExecutionCollectDetailChild> childList = this.planBigDateExecutionCollectDetailChildRepository.findListByCollectIds(collectIds);

        Map<String, List<PlanBigDateExecutionCollectDetailPicture>> picMap = pictureList.stream().collect(Collectors.groupingBy(PlanBigDateExecutionCollectDetailPicture::getDetailId));
        Map<String, List<PlanBigDateExecutionCollectDetailChild>> childMap = childList.stream().collect(Collectors.groupingBy(PlanBigDateExecutionCollectDetailChild::getDetailId));
        if (CollectionUtils.isNotEmpty(detailList)) {
            //遍历补充采集图片，稽查信息
            for (PlanBigDateExecutionCollectDetail detail : detailList) {
                if (picMap.containsKey(detail.getId())) {
                    detail.setExpirationBackPictures(picMap.get(detail.getId()));
                }
                if (childMap.containsKey(detail.getId())) {
                    detail.setExpirationBackBatchs(childMap.get(detail.getId()));
                }
            }
        }
        Map<String, List<PlanBigDateExecutionCollectDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(PlanBigDateExecutionCollectDetail::getCollectId));
        collect.setExpirationBackProducts(detailMap.get(collect.getId()));
        return JsonUtils.convert(collect, PlanBigDateExecutionCollectVo.class);

    }
}
