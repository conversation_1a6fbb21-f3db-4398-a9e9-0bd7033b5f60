package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosure;

public interface PlanClosureOaService {

    /**
     * 推送OA
     *
     * @param
     * @return
     */
    JSONObject pushOa(PlanClosure plan);

    /**
     * 重新提交OA
     *
     * @param
     * @return
     */
    JSONObject resubmitOa(PlanClosure plan);

    /**
     * OA撤回
     *
     * @param
     * @return
     */
    boolean oaWithdraw(PlanClosure plan, String remark);
}
