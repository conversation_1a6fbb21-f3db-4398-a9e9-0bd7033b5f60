package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLosses;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectItemEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectScheme;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectMapper;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectReportService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/16 14:20
 */
@Service
public class RegionCollectReportServiceImpl implements RegionCollectReportService {

    @Resource
    private RegionCollectMapper regionCollectMapper;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Override
    public Page<RegionCollectDepartmentEstimation> findDepartmentReport(Pageable pageable, RegionCollectDepartmentEstimation vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(vo.getCollectCode(), "汇总编码不能为空");
        Page<RegionCollectDepartmentEstimation> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return regionCollectMapper.findDepartmentReport(page, vo);
    }

    @Override
    public Page<RegionCollectGainsAndLosses> findGainsAndLossesReport(Pageable pageable, RegionCollectGainsAndLosses vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(vo.getCollectCode(), "汇总编码不能为空");
        Page<RegionCollectGainsAndLosses> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<RegionCollectGainsAndLosses> gainsAndLossesPageResult = regionCollectMapper.findGainsAndLossesReport(page, vo);
        List<RegionCollectGainsAndLosses> records = gainsAndLossesPageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<String> customerCodes = records.stream().map(RegionCollectGainsAndLosses::getCustomerCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
            Map<String, String> customerMap = customerVos.stream().filter(e -> StringUtils.isNotEmpty(e.getCustomerCode()) && Objects.nonNull(e.getChannelType()))
                    .collect(Collectors.toMap(CustomerVo::getCustomerCode, CustomerVo::getChannelType, (v1,v2) -> v1));
            records.forEach(e -> {
                String customerCode = e.getCustomerCode();
                String channelTypeStr = customerMap.get(customerCode);
                if (StringUtils.isNotEmpty(channelTypeStr)) {
                    List<String> channleTypeList = Arrays.asList(channelTypeStr.split(","));
                    e.setChannelTypeList(channleTypeList);
                    e.setChannelType(channelTypeStr);
                    e.setChannelTypeNameStr(channleTypeList.stream().map(channelType -> channelTypeMap().getOrDefault(channelType," 缺省（未配置）")).collect(Collectors.joining(",")));
                }
            });
        }
        return gainsAndLossesPageResult;
    }

    /**
     * 渠道类型标签
     *
     * @return
     */
    private Map<String, String> channelTypeMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.CHANNEL_TYPE);
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }

    @Override
    public Page<RegionCollectItemEstimation> findItemReport(Pageable pageable, RegionCollectItemEstimation vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(vo.getCollectCode(), "汇总编码不能为空");
        Page<RegionCollectItemEstimation> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return regionCollectMapper.findItemReport(page, vo);
    }

    @Override
    public Map<String, BigDecimal> findDepartmentReportTotal(RegionCollectDepartmentEstimation vo) {
        Validate.notNull(vo.getCollectCode(), "汇总编码不能为空");
        return regionCollectMapper.findDepartmentReportTotal(vo);
    }

    @Override
    public Map<String, BigDecimal> findGainsAndLossesReportTotal(RegionCollectGainsAndLosses vo) {
        Validate.notNull(vo.getCollectCode(), "汇总编码不能为空");
        return regionCollectMapper.findGainsAndLossesReportTotal(vo);
    }

    @Override
    public Map<String, BigDecimal> findItemReportTotal(RegionCollectItemEstimation vo) {
        Validate.notNull(vo.getCollectCode(), "汇总编码不能为空");
        return regionCollectMapper.findItemReportTotal(vo);
    }

    @Override
    public Page<RegionCollectScheme> findSchemeReport(Pageable pageable, RegionCollectScheme vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(vo.getCollectCode(), "汇总编码不能为空");
        Page<RegionCollectScheme> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return regionCollectMapper.findSchemeReport(page, vo);
    }

    @Resource
    private MarketingPlanProductRepository productRepository;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;


    @Override
    public Page<MarketingPlanCaseVo> findRegionMarketingCaseReport(Pageable pageable, MarketingPlanCaseVo caseVo, String collectCode) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(collectCode, "汇总编码不能为空");
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<MarketingPlanCaseVo> data = regionCollectMapper.findRegionMarketingCaseReport(page, caseVo, collectCode);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<MarketingPlanCaseVo> list = (List<MarketingPlanCaseVo>) nebulaToolkitService.copyCollectionByWhiteList(data.getRecords(), MarketingPlanCaseVo.class, MarketingPlanCaseVo.class, HashSet.class, ArrayList.class);
            List<String> schemeDetailCodes = list.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            //构建参数
            checkHelper.buildMarketingPlanCase(list, productMap);
            List<DictDataVo> cooperateTypeList = dictDataVoService.findByDictTypeCode(DictConstant.MDM_COOPERATE_TYPE);
            Map<String, String> cooperateTypeMap = cooperateTypeList.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            list.forEach(e -> {
                String cooperateType = e.getCooperateType();
                if (StringUtils.isNotEmpty(cooperateType)) {
                    e.setCooperateTypeStr(cooperateTypeMap.getOrDefault(cooperateType, ""));
                }
            });
            data.setRecords(list);
        }
        return data;
    }

    @Override
    public Page<MarketingSalesPlanVo> findRegionMarketingSalesPlanList(Pageable pageable, String collectCode) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(collectCode, "汇总编码不能为空");
        Page<MarketingSalesPlanVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<MarketingSalesPlanVo> data = regionCollectMapper.findRegionMarketingSalesPlanList(page, collectCode);
        return data;
    }
}
