package com.biz.crm.tpm.business.activities.scheme.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.UuidOpEntity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;

import static org.apache.ibatis.type.JdbcType.VARCHAR;

/**
 * 实体：方案;
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "Scheme",description = "方案")
@TableName("tpm_scheme")
@Getter
@Setter
@Entity(name = "tpm_scheme")
@org.hibernate.annotations.Table(appliesTo = "tpm_scheme", comment = "方案")
public class Scheme  extends UuidOpEntity{
  /** 方案编号 */
  @ApiModelProperty(name = "方案编号",notes = "方案编号")
  @Column(name = "scheme_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '方案编号 '")
  private String schemeCode;

  /** 方案名称 */
  @ApiModelProperty(name = "方案名称",notes = "方案名称")
  @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称 '")
  private String schemeName;

  /** 方案类型 */
  @ApiModelProperty(name = "方案类型",notes = "方案类型")
  @Column(name = "scheme_type", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '方案类型 '")
  private String schemeType;
  
  /** 方案开始时间 */
  @ApiModelProperty(name = "方案开始时间",notes = "方案开始时间")
  @Column(name = "scheme_begin_time", nullable = false,  columnDefinition = "DATETIME COMMENT '方案开始时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date schemeBeginTime;
  
  /** 方案结束时间 */
  @ApiModelProperty(name = "方案结束时间",notes = "方案结束时间")
  @Column(name = "scheme_end_time", nullable = false,  columnDefinition = "DATETIME COMMENT '方案结束时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date schemeEndTime;
  
  /** 方案说明 */
  @ApiModelProperty(name = "方案说明",notes = "方案说明")
  @Column(name = "scheme_description", nullable = false, length = 512,  columnDefinition = "VARCHAR(512) COMMENT '方案说明 '")
  private String schemeDescription;

  /** 方案状态 */
  @ApiModelProperty(name = "方案状态",notes = "方案状态（01:待执行, 02:执行中,:03:已结束）")
  @Column(name = "scheme_status", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '方案状态 '")
  private String schemeStatus;

  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode", value = "租户编号", required = true)
  @TableField(value = "tenant_code")
  @Column(name = "tenant_code", nullable = false, columnDefinition = "varchar(64) COMMENT '租户编号'")
  private String tenantCode;

  /** 数据状态（删除状态） */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY, jdbcType = VARCHAR)
  @Column(name = "del_flag", nullable = true, length = 10, columnDefinition = "varchar(10) COMMENT '数据状态（删除状态）'")
  private String delFlag;

  /** 备注 */
  @TableField(value = "remark", jdbcType = VARCHAR)
  @Column(name = "remark", nullable = true, length = 400, columnDefinition = "varchar(400) COMMENT '备注'")
  private String remark;

}