package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Author: yangrui
 * @Date: 2024-12-31 14:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_plan_execution_sum")
@Table(
        name = "tpm_plan_execution_sum",
        indexes = {
                @Index(name = "uni_scheme_detail_code", columnList = "scheme_detail_code", unique = true),
                @Index(name = "idx_scheme_code", columnList = "scheme_code"),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_plan_execution_sum", comment = "执行计划汇总")
@ApiModel(value = "PlanExecutionSum", description = "执行计划汇总")
public class PlanExecutionSum extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("门店编码")
    @Column(name = "terminal_code", length = 64, columnDefinition = "varchar(64) COMMENT '门店编码'")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    @Column(name = "terminal_name", columnDefinition = "varchar(255) COMMENT '门店名称'")
    private String terminalName;

    @ApiModelProperty("所属大区编码")
    @Column(name = "division_org_code", columnDefinition = "varchar(32) comment '所属大区编码'")
    private String divisionOrgCode;

    @ApiModelProperty("所属大区")
    @Column(name = "division_org_name", columnDefinition = "varchar(32) comment '所属大区'")
    private String divisionOrgName;

    @ApiModelProperty("归属部门编码")
    @Column(name = "belong_department_code", columnDefinition = "varchar(32) comment '归属部门编码'")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    @Column(name = "belong_department_name", columnDefinition = "varchar(64) comment '归属部门名称'")
    private String belongDepartmentName;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("指引编码")
    @Column(name = "release_code", columnDefinition = "varchar(32) comment '关联统筹方案编码'")
    private String releaseCode;

    @ApiModelProperty("指引名称")
    @Column(name = "release_name", columnDefinition = "varchar(128) comment '关联统筹方案名称'")
    private String releaseName;

    @ApiModelProperty("指引明细编码")
    @Column(name = "release_detail_code", columnDefinition = "varchar(32) comment '关联统筹方案明细编码'")
    private String releaseDetailCode;

    @ApiModelProperty("是否百万终端")
    @Column(name = "million_terminal", columnDefinition = "int(5) comment '是否百万终端'")
    private Integer millionTerminal;

    @ApiModelProperty("终端类型")
    @Column(name = "terminal_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '终端类型'")
    private String terminalType;

    @ApiModelProperty("活动执行编码")
    @Column(name = "act_execute_code", columnDefinition = "varchar(32) comment '活动执行编码'")
    private String actExecuteCode;

    @ApiModelProperty("开始时间")
    @Column(name = "start_date", columnDefinition = "varchar(32) comment '开始时间'")
    private String startDate;

    @ApiModelProperty("结束时间")
    @Column(name = "end_date", columnDefinition = "varchar(32) comment '结束时间'")
    private String endDate;



    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("执行标准")
    @Column(name = "act_standards", columnDefinition = "varchar(128) COMMENT '执行标准'")
    private String actStandards;

    @ApiModelProperty("审核结果")
    @Column(name = "audit_result", columnDefinition = "int(5) COMMENT '审核结果'")
    private Integer auditResult;
}
