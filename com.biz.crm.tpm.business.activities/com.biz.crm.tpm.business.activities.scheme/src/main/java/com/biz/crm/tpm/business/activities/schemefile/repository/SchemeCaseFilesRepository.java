package com.biz.crm.tpm.business.activities.schemefile.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.schemefile.entity.SchemeCaseFiles;
import com.biz.crm.tpm.business.activities.schemefile.mapper.SchemeCaseFilesMapper;
import com.bizunited.nebula.common.util.tenant.TenantContextHolder;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/22 18:12
 */
@Component
public class SchemeCaseFilesRepository extends ServiceImpl<SchemeCaseFilesMapper, SchemeCaseFiles> {


    public void deleteBySchemeCodeAndSchemeType(String schemeCode, String schemeType) {
        this.lambdaUpdate()
                .eq(SchemeCaseFiles::getSchemeCode, schemeCode)
                .eq(SchemeCaseFiles::getSchemeType, schemeType)
                .remove();
    }


    public void deleteBySchemeCodeAndSchemeTypeList(List<String> schemeCodes,List<String> schemeTypes) {
        this.lambdaUpdate()
                .in(SchemeCaseFiles::getSchemeCode, schemeCodes)
                .in(SchemeCaseFiles::getSchemeType, schemeTypes)
                .remove();
    }


    public List<SchemeCaseFiles> findListBySchemeCodeAndSchemeType(String schemeCode, String schemeType) {
        return this.lambdaQuery()
                .eq(SchemeCaseFiles::getSchemeCode, schemeCode)
                .eq(SchemeCaseFiles::getSchemeType, schemeType)
                .eq(SchemeCaseFiles::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }
}
