package com.biz.crm.tpm.business.activities.materialdemand.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.materialdemand.constant.MaterialDemandConstant;
import com.biz.crm.tpm.business.activities.materialdemand.dto.MaterialDemandLogEventDto;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemand;
import com.biz.crm.tpm.business.activities.materialdemand.event.MaterialDemandLogEventListener;
import com.biz.crm.tpm.business.activities.materialdemand.repository.MaterialDemandRepository;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:35
 */
@Service
@Transactional
@Slf4j
public class MaterialDemandServiceImpl extends BusinessPageCacheServiceImpl<MaterialDemandDetailVo, MaterialDemandDetailVo> implements MaterialDemandService {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private NebulaNetEventClient nebulaNetEventClient;

    @Resource
    private MaterialDemandRepository repository;

    @Autowired
    private MaterialDemandDetailService demandDetailService;

    @Resource
    private GenerateCodeService generateCodeService;

    @Resource
    private OrgVoService orgVoService;

    /**
     * 分页查询
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MaterialDemandVo> findList(Pageable pageable, MaterialDemandVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MaterialDemandVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return repository.findList(page, vo);
    }

    /**
     * 查询详情
     *
     * @param id
     * @param code
     * @return
     */
    @Override
    public MaterialDemandVo queryByIdOrCode(String id, String code) {
        MaterialDemand demand = repository.queryByIdOrCode(id, code);
        Validate.notNull(demand, "查询数据不存在");
        MaterialDemandVo vo = nebulaToolkitService.copyObjectByWhiteList(demand, MaterialDemandVo.class, HashSet.class, ArrayList.class);
        List<MaterialDemandDetailVo> demandDetailVos = demandDetailService.findListByCodes(Lists.newArrayList(demand.getDemandCode()));
        vo.setDetailList(demandDetailVos);
        return vo;
    }

    /**
     * 创建
     *
     * @param vo
     */
    @Override
    public void create(MaterialDemandVo vo) {
        Validate.notNull(vo.getCacheKey(), "cacheKey不能为空");
        List<MaterialDemandDetailVo> demandDetailVos = findCacheList(vo.getCacheKey());
        vo.setDetailList(demandDetailVos);
        this.validateData(vo, Boolean.TRUE);
        MaterialDemand demand = nebulaToolkitService.copyObjectByBlankList(vo, MaterialDemand.class, HashSet.class, ArrayList.class);
        demand.setDemandCode(generateCodeService.generateCode(MaterialDemandConstant.MATERIAL_DEMAND_RULE));
        demand.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        demand.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        demand.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
        demand.setTenantCode(TenantUtils.getTenantCode());
        demand.setRemark(vo.getRemark());
        demand.setYears(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH)));
        repository.save(demand);
        List<String> detailRuleCodeList = generateCodeService.generateCode(MaterialDemandConstant.MATERIAL_DEMAND_DETAIL_RULE, vo.getDetailList().size());
        Integer count = 0;
        for (MaterialDemandDetailVo detailVo : vo.getDetailList()) {
            detailVo.setDemandDetailCode(detailRuleCodeList.get(count++));
        }
        demandDetailService.saveBatchList(vo.getDetailList(), demand.getDemandCode());

        //日志处理
        MaterialDemandLogEventDto dto = new MaterialDemandLogEventDto();
        dto.setNewest(vo);
        SerializableBiConsumer<MaterialDemandLogEventListener, MaterialDemandLogEventDto> consumer = MaterialDemandLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, MaterialDemandLogEventListener.class, consumer);
    }

    @Override
    public void update(MaterialDemandVo vo) {
        Validate.notNull(vo.getCacheKey(), "cacheKey不能为空");
        List<MaterialDemandDetailVo> demandDetailVos = findCacheList(vo.getCacheKey());
        vo.setDetailList(demandDetailVos);
        Validate.notNull(vo.getId(), "主键ID不能为空");
        this.validateData(vo, Boolean.FALSE);
        MaterialDemand demand = repository.queryByIdOrCode(vo.getId(), null);
        Validate.notNull(demand, "查询数据不存在");
        Validate.isTrue(StringUtils.equalsAny(demand.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode()), "不是待提交状态不可编辑!");
        Validate.isTrue(!StringUtils.equals(BooleanEnum.TRUE.getCapital(), demand.getCollectStatus()), "已汇总不可编辑！");
        demand.setDemandName(vo.getDemandName());
        demand.setRemark(vo.getRemark());
        repository.updateById(demand);
        List<String> detailRuleCodeList = generateCodeService.generateCode(MaterialDemandConstant.MATERIAL_DEMAND_DETAIL_RULE, vo.getDetailList().size());
        Integer count = 0;
        for (MaterialDemandDetailVo detailVo : vo.getDetailList()) {
            detailVo.setDemandDetailCode(detailRuleCodeList.get(count++));
        }
        demandDetailService.saveBatchList(vo.getDetailList(), demand.getDemandCode());

        //日志处理
        MaterialDemandVo oldVo = queryByIdOrCode(vo.getId(), null);
        MaterialDemandLogEventDto dto = new MaterialDemandLogEventDto();
        dto.setNewest(vo);
        dto.setOriginal(oldVo);
        SerializableBiConsumer<MaterialDemandLogEventListener, MaterialDemandLogEventDto> consumer = MaterialDemandLogEventListener::onUpdate;
        nebulaNetEventClient.publish(dto, MaterialDemandLogEventListener.class, consumer);
    }


    /**
     * 参数校验
     *
     * @param vo
     */
    private void validateData(MaterialDemandVo vo, Boolean flag) {
        Validate.notNull(vo.getOrgCode(), "部门编码不能为空");
        vo.setYears(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH)));
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(vo.getOrgCode());
        List<String> orgCodeList = orgVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode()))
                .map(OrgVo::getOrgCode).collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isNotEmpty(vo.getDetailList()), "物料需求明细列表不能为空");
        for (MaterialDemandDetailVo detailVo : vo.getDetailList()) {
            Validate.isTrue(detailVo.getCheckFlag(), "未校验通过的数据不可保存");
            Validate.isTrue(orgCodeList.contains(detailVo.getOrgCode()), String.format("部门%s下级组织不包含当前使用部门%s", vo.getOrgName(), detailVo.getOrgName()));
        }
        if (flag) {
            //宋佳丽   物料提报没有这个限制  2024年7月29日14:58:24
            //MaterialDemand demand = repository.findByOrgCodeAndYears(vo.getOrgCode(), vo.getYears());
            //Validate.isTrue(ObjectUtils.isEmpty(demand), "当前组织%s在%s年月已存在数据", vo.getOrgCode(), vo.getYears());
        }
    }

    @Override
    public String deleteBatchByIds(List<String> ids) {
        List<MaterialDemand> demands = repository.findListByIdList(ids);
        String codes = demands.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(),
                        ProcessStatusEnum.COMMIT.getDictCode()))
                .map(MaterialDemand::getDemandCode)
                .collect(Collectors.joining("、"));
        demands = demands.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode()))
            .collect(Collectors.toList());
        String codes2 = demands.stream().filter(x -> StringUtils.equals(BooleanEnum.TRUE.getCapital(), x.getCollectStatus()))
            .map(MaterialDemand::getDemandCode)
            .collect(Collectors.joining("、"));
        Validate.isTrue(StringUtils.isBlank(codes2), "已汇总的需求不允许被删除：%s", codes2);
        if (CollectionUtils.isNotEmpty(demands)) {
            demands.forEach(x -> x.setDelFlag(DelFlagStatusEnum.DELETE.getCode()));
            repository.updateBatchById(demands);
            List<String> demandCodes = demands.stream().map(MaterialDemand::getDemandCode).collect(Collectors.toList());
            demandDetailService.deleteByDemandCodeList(demandCodes);
            //日志处理
            MaterialDemandLogEventDto dto = new MaterialDemandLogEventDto();
            List<MaterialDemandVo> voList = (List<MaterialDemandVo>) nebulaToolkitService.copyCollectionByWhiteList(demands, MaterialDemand.class, MaterialDemandVo.class, HashSet.class, ArrayList.class);
            dto.setNewestList(voList);
            SerializableBiConsumer<MaterialDemandLogEventListener, MaterialDemandLogEventDto> consumer = MaterialDemandLogEventListener::onDelete;
            nebulaNetEventClient.publish(dto, MaterialDemandLogEventListener.class, consumer);
        }
        return codes;
    }

    /**
     * 通过组织编码查询所有的
     *
     * @param orgCodes
     * @return
     */
    @Override
    public List<MaterialDemandDetailVo> findListByOrgCodes(List<String> orgCodes, List<String> processStatusList) {
        return repository.findListByOrgCodes(orgCodes, processStatusList);
    }


    /**
     * 查询选中的物资需求列表 并排除明细
     *
     * @param inExcludeDemandDetailCodes
     * @param demandCodes
     * @return
     */
    @Override
    public List<MaterialDemandDetailVo> selectMaterialDemandDetailList(List<String> inExcludeDemandDetailCodes, List<String> demandCodes) {
        return repository.selectMaterialDemandDetailList(inExcludeDemandDetailCodes, demandCodes);
    }

    /**
     * 修改审批状态
     *
     * @param processStatus
     * @param demandCodes
     */
    @Override
    public void updateProcessStatus(String processStatus, List<String> demandCodes) {
        repository.updateProcessStatus(processStatus, demandCodes);
    }

    @Override
    public void saveCurrentPageCache(String cacheKey, List<MaterialDemandDetailVo> saveList) {
        List<MaterialDemandDetailVo> dataList = demandDetailService.checkDetails(saveList);
        super.saveCurrentPageCache(cacheKey, dataList);
    }
}
