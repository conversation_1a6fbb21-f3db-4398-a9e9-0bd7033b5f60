package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectSchemeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
public interface RegionCollectSchemeService {

    List<RegionCollectSchemeVo> findByCollectCode(String collectCode);

    void deleteByCollectCodes(List<String> collectCodes);

    void saveList(List<RegionCollectSchemeVo> list,String collectCode);

    void deleteBySchemeCodes(List<String> schemeCodes);

    String findRegionCollectCodeBySchemeCode(String schemeCode);
}
