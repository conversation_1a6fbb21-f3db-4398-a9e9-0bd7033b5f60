package com.biz.crm.tpm.business.activities.marketingplan.helper;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.tpm.business.activities.marketingplan.constant.MarketingPlanConstant;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/11 15:52
 */
@Component
@Slf4j
public class MarketingSalesPlanPageCacheHelper extends BusinessPageCacheHelper<MarketingSalesPlanVo, MarketingSalesPlanVo> {


    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    @Resource
    private RedisTemplate redisTemplate;


    @Override
    public String getCacheKeyPrefix() {
        return MarketingPlanConstant.MARKETING_PLAN_CACHE_PAGE;
    }

    @Override
    public Class<MarketingSalesPlanVo> getDtoClass() {
        return MarketingSalesPlanVo.class;
    }

    @Override
    public Class<MarketingSalesPlanVo> getVoClass() {
        return MarketingSalesPlanVo.class;
    }

    private static final String COLUM = ":";

    @Override
    public List<MarketingSalesPlanVo> findDtoListFromRepository(MarketingSalesPlanVo vo, String cacheKey) {
        if (ObjectUtils.isEmpty(vo.getSchemeCode())) {
            return Lists.newArrayList();
        }
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findListBySchemeCode(vo.getSchemeCode());
        return salesPlanVoList.stream().sorted(Comparator.comparing(MarketingSalesPlanVo::getCheckFlag)
                .thenComparing(MarketingSalesPlanVo::getSort, Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList());
    }


    @Override
    public List<MarketingSalesPlanVo> newItem(String cacheKey, List<MarketingSalesPlanVo> itemList) {
        MarketingSalesPlanVo vo = new MarketingSalesPlanVo();
        vo.setId(UuidCrmUtil.general());
        vo.setSort(System.nanoTime());
        return Lists.newArrayList(vo);
    }

    @Override
    public List<MarketingSalesPlanVo> copyItem(String cacheKey, List<MarketingSalesPlanVo> itemList) {
//        List<MarketingSalesPlanVo> list = (List<MarketingSalesPlanVo>) nebulaToolkitService.copyCollectionByWhiteList(itemList, MarketingSalesPlanVo.class, MarketingSalesPlanVo.class,
//                HashSet.class, ArrayList.class, "productAndItemList", "productList", "itemList","executeExampleList","closeCaseExampleList",
//                "cooperateTypeList","customerTagList","terminalTagList");
//        CopyOptions copyOptions = new CopyOptions(MarketingSalesPlanVo.class, false,"productAndItemList", "productList", "itemList","executeExampleList","closeCaseExampleList",
//            "cooperateTypeList","customerTagList","terminalTagList");
        List<MarketingSalesPlanVo> list = JSONObject.parseArray(JSONObject.toJSONString(itemList), MarketingSalesPlanVo.class);
        for (MarketingSalesPlanVo caseVo : list) {
            caseVo.setId(UuidCrmUtil.general());
            caseVo.setCode(null);
            caseVo.setSort(System.nanoTime());
        }
        return list;
    }


    /**
     * 导入新增数据
     *
     * @param cacheKey
     * @param list
     * @param loginUserCodes
     * @param userName
     * @return
     */
    public void importNewItem(String cacheKey, String years, List<MarketingSalesPlanVo> list, String schemeCode, String originalSchemeCode, List<String> loginUserCodes, String userName) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String key = cacheKey + COLUM + MarketingSalesPlanTypeEnum.sales_plan.getCode() + COLUM + years;
        String redisCacheIdKey = this.getRedisCacheIdKey(key);
        String redisCacheDataKey = this.getRedisCacheDataKey(key);
        //数据校验
        List<MarketingSalesPlanVo> itemList = marketingSalesPlanService.checkSalesPlanList(list, years, schemeCode, originalSchemeCode, loginUserCodes, userName);
        for (MarketingSalesPlanVo newItem : itemList) {
            newItem.setSort(System.nanoTime());
            newItem.setId(UuidCrmUtil.general());
        }
        Object[] newIdArr = itemList.stream().map(this::getDtoKey).toArray();
        redisService.lPushAll(redisCacheIdKey, this.getExpireTime(), newIdArr);

        Map<Object, MarketingSalesPlanVo> updateMap = itemList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }


    @Override
    public List<MarketingSalesPlanVo> dtoListToVoList(List<MarketingSalesPlanVo> marketingSalesPlanVos) {
        return marketingSalesPlanVos.stream().sorted(Comparator.comparing(MarketingSalesPlanVo::getCheckFlag,
                Comparator.nullsFirst(Comparator.naturalOrder())).thenComparing(
                Comparator.comparing(MarketingSalesPlanVo::getSort,
                        Comparator.nullsLast(Comparator.naturalOrder())).reversed())).collect(Collectors.toList());
    }

    @Override
    public Object getDtoKey(MarketingSalesPlanVo vo) {
        return vo.getId();
    }

    @Override
    public String getCheckedStatus(MarketingSalesPlanVo vo) {
        return vo.getChecked();
    }
}
