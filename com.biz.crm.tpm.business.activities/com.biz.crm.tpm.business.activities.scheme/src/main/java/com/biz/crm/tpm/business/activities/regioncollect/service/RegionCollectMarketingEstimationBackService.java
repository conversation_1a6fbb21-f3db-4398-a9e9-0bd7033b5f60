package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
public interface RegionCollectMarketingEstimationBackService {

    void deleteByCollectCodes(List<String> collectCodes);

    void saveList(List<RegionCollectMarketingEstimationVo> list, String collectCode);
}
