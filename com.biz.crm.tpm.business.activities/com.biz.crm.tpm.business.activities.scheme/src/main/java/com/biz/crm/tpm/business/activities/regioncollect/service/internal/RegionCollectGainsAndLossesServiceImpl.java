package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLosses;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectGainsAndLossesRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectGainsAndLossesService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectGainsAndLossesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Service("regionCollectGainsAndLossesService")
@Slf4j
public class RegionCollectGainsAndLossesServiceImpl implements RegionCollectGainsAndLossesService {

    @Resource
    private RegionCollectGainsAndLossesRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public List<RegionCollectGainsAndLossesVo> findByCollectCode(String collectCode) {
        List<RegionCollectGainsAndLosses> list = repository.findByCollectCode(collectCode);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        for (RegionCollectGainsAndLosses data : list) {
            if (ObjectUtils.isNotEmpty(data.getSecondCostCategoryJsonStr())) {
                data.setSecondCostCategoryMap(JSONObject.parseObject(data.getSecondCostCategoryJsonStr(), LinkedHashMap.class));
            }
        }
        return (List<RegionCollectGainsAndLossesVo>) nebulaToolkitService.copyCollectionByWhiteList(list, RegionCollectGainsAndLosses.class, RegionCollectGainsAndLossesVo.class,
                HashSet.class, ArrayList.class, "secondCostCategoryMap");
    }

    @Override
    public void deleteByCollectCodes(List<String> collectCodes) {
        repository.deleteByCollectCodes(collectCodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveList(List<RegionCollectGainsAndLossesVo> list, String collectCode) {
        repository.deleteByCollectCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<RegionCollectGainsAndLosses> dataList = list.stream().map(x -> {
            RegionCollectGainsAndLosses data = nebulaToolkitService.copyObjectByWhiteList(x, RegionCollectGainsAndLosses.class, HashSet.class, ArrayList.class);
            if (!CollectionUtils.isEmpty(data.getSecondCostCategoryMap())) {
                data.setSecondCostCategoryJsonStr(JSONObject.toJSONString(data.getSecondCostCategoryMap()));
            }
            data.setId(null);
            data.setCollectCode(collectCode);
            return data;
        }).collect(Collectors.toList());
        repository.saveBatch(dataList);
    }
}
