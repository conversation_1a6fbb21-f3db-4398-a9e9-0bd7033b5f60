package com.biz.crm.tpm.business.activities.regioncollect.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.tpm.business.activities.marketingplan.entity.CustomerGainsAndLosses;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_region_collect_item_estimation_back")
@Table(
        name = "tpm_region_collect_item_estimation_back",
        indexes = {
                @Index(name = "tpm_region_collect_item_estimation_back_index0", columnList = "collect_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_region_collect_item_estimation_back", comment = "大区汇总-品项测算备份数据")
@ApiModel(value = "RegionCollectItemEstimationBack", description = "大区汇总-品项测算备份数据")
public class RegionCollectItemEstimationBack extends CustomerGainsAndLosses {

    @ApiModelProperty("大区汇总编码")
    @Column(name = "collect_code", columnDefinition = "varchar(32) comment '大区汇总编码'")
    private String collectCode;

    @ApiModelProperty("品项编码")
    @Column(name = "item_code", columnDefinition = "varchar(32) comment '品项编码'")
    private String itemCode;

    @ApiModelProperty("品项名称")
    @Column(name = "item_name", columnDefinition = "varchar(64) comment '品项名称'")
    private String itemName;
}
