package com.biz.crm.tpm.business.activities.scheme.service.internal;

import com.biz.crm.tpm.business.activities.scheme.repository.ActivitiesSchemeFilesRepository;
import com.biz.crm.tpm.business.activities.scheme.dto.ActivitiesSchemeFilesDto;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeFiles;
import com.biz.crm.tpm.business.activities.scheme.service.ActivitiesSchemeFilesService;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeFilesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 方案活动附件;(tpm_activities_scheme_files)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-20
 */
@Service("activitiesSchemeFilesService")
public class ActivitiesSchemeFilesServiceImpl implements ActivitiesSchemeFilesService {
  @Autowired
  private ActivitiesSchemeFilesRepository activitiesSchemeFilesRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public ActivitiesSchemeFilesVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    ActivitiesSchemeFiles activitiesSchemeFiles = this.activitiesSchemeFilesRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (activitiesSchemeFiles == null) {
      return null;
    }
    ActivitiesSchemeFilesVo activitiesSchemeFilesVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesSchemeFiles, ActivitiesSchemeFilesVo.class, LinkedHashSet.class, ArrayList.class);
    return activitiesSchemeFilesVo;
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 主键
   * @return 多条数据
   */
  @Override
  public List<ActivitiesSchemeFilesVo> findByActivitiesCode(String code) {
    if (StringUtils.isBlank(code)) {
      return Collections.emptyList();
    }
    List<ActivitiesSchemeFiles> activitiesSchemeFiles = this.activitiesSchemeFilesRepository.findActivitiesCode(code);
    if (activitiesSchemeFiles == null) {
      return Collections.emptyList();
    }
    Collection<ActivitiesSchemeFilesVo> activitiesSchemeFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemeFiles, ActivitiesSchemeFiles.class, ActivitiesSchemeFilesVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesSchemeFilesVos);
  }

  /**
   * 新增数据
   *
   * @param activitiesSchemeFilesDto 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public ActivitiesSchemeFilesVo create(ActivitiesSchemeFilesDto activitiesSchemeFilesDto) {
    this.createValidate(activitiesSchemeFilesDto);
    ActivitiesSchemeFiles activitiesSchemeFiles = this.nebulaToolkitService.copyObjectByWhiteList(activitiesSchemeFilesDto, ActivitiesSchemeFiles.class, LinkedHashSet.class, ArrayList.class);
    activitiesSchemeFiles.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesSchemeFilesRepository.saveOrUpdate(activitiesSchemeFiles);
    ActivitiesSchemeFilesVo activitiesSchemeFilesVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesSchemeFiles, ActivitiesSchemeFilesVo.class, LinkedHashSet.class, ArrayList.class);
    activitiesSchemeFilesVo.setId(activitiesSchemeFiles.getId());
    return activitiesSchemeFilesVo;
  }

  @Override
  @Transactional
  public List<ActivitiesSchemeFilesVo> createBatch(List<ActivitiesSchemeFilesDto> activitiesSchemeFilesDtos) {
    if (CollectionUtils.isEmpty(activitiesSchemeFilesDtos)) {
      return Collections.emptyList();
    }
    List<ActivitiesSchemeFilesVo> activitiesSchemeFilesVos = activitiesSchemeFilesDtos.stream().map(item -> this.create(item)).collect(Collectors.toList());
    return activitiesSchemeFilesVos;
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   * @return 删除结果
   */
  @Transactional
  @Override
  public void delete(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<ActivitiesSchemeFiles> activitiesSchemeFiless = this.activitiesSchemeFilesRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(activitiesSchemeFiless)) {
      return;
    }
    this.activitiesSchemeFilesRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  @Override
  @Transactional
  public void deleteByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return;
    }
    this.activitiesSchemeFilesRepository.removeByActivitiesCode(activitiesCode);
  }

  /**
   * 创建验证
   *
   * @param activitiesSchemeFilesDto
   */
  private void createValidate(ActivitiesSchemeFilesDto activitiesSchemeFilesDto) {
    Validate.notNull(activitiesSchemeFilesDto, "新增时，对象信息不能为空！");
    activitiesSchemeFilesDto.setId(null);
    // 验证重复操作
    Validate.notBlank(activitiesSchemeFilesDto.getActivitiesCode(), "新增数据时，活动编号不能为空！");
    Validate.notBlank(activitiesSchemeFilesDto.getFileCode(), "新增数据时，文件唯一识别号不能为空！");
  }
}