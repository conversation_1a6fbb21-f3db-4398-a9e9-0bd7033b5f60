package com.biz.crm.tpm.business.activities.marketingplan.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.service.PlanBigDateExecutionCollectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.controller.PlanBigDateExecutionCollectController
 * @description: 经销商活动执行
 * @author: xiaopeng.zhang
 * @create: 2024-08-01 9:08
 */
@Slf4j
@RestController
@RequestMapping("/v1/planBigDateExecution")
@Api(tags = "经销商活动执行")
public class PlanBigDateExecutionCollectController {
    @Autowired(required = false)
    private PlanBigDateExecutionCollectService planBigDateExecutionCollectService;

    /**
     * 创建活动执行
     */
    @PostMapping("create")
    Result<?> create(@RequestBody @ApiParam(name = "dto", value = "活动执行信息") PlanBigDateExecutionCollectVo dto) {
        try {
            this.planBigDateExecutionCollectService.create(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 大日期采集明细信息
     *
     * @param dto
     * @return
     */
    @PostMapping("findDetailByQueryDto")
    @ApiModelProperty("大日期采集明细信息")
    Result<PlanBigDateExecutionCollectVo> findDetailByQueryDto(@RequestBody @ApiParam(name = "dto", value = "活动执行信息") PlanBigDateExecutionCollectVo dto) {
        try {
            PlanBigDateExecutionCollectVo vo = this.planBigDateExecutionCollectService.findDetailByQueryDto(dto);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @GetMapping("findListByActCode")
    @ApiModelProperty("大日期采集信息")
    Result<List<PlanBigDateExecutionCollectVo>> findListByActCode(@RequestParam("code") String code) {
        try {
            List<PlanBigDateExecutionCollectVo> list = this.planBigDateExecutionCollectService.findListByActCode(code);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("findDetailById")
    @ApiModelProperty("大日期采集信息详情查询")
    Result<PlanBigDateExecutionCollectVo> findDetailById(@RequestParam("id") String id) {
        try {
            PlanBigDateExecutionCollectVo vo = this.planBigDateExecutionCollectService.findDetailById(id);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
