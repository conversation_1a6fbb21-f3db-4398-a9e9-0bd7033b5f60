package com.biz.crm.tpm.business.activities.materialdemand.exports.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.tpm.business.activities.materialdemand.exports.model.MaterialDemandDetailExportVo;
import com.biz.crm.tpm.business.activities.materialdemand.mapper.MaterialDemandMapper;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description 物料需求提报导出服务
 * <AUTHOR>
 * @Date 2024/7/24 13:58
 */
@Slf4j
@Component
public class MaterialDemandDetailExportProcess implements ExportProcess<MaterialDemandDetailExportVo> {

    @Autowired(required = false)
    private MaterialDemandMapper mapper;

    @Override
    public Integer getPageSize() {
        return CommonConstant.IE_EXPORT_PAGE_SIZE;
    }

    @Override
    public Integer getTotal(Map<String, Object> params) {
        MaterialDemandVo dto = this.convertParams(params);
        Page<MaterialDemandDetailExportVo> pageResult = this.mapper.findDetailExportByDto(new Page<>(1, 1), dto);
        Validate.isTrue(pageResult.getTotal() < CommonConstant.IE_EXPORT_MAX_TOTAL, "导出时，" +
                "单次最大导出[" + CommonConstant.IE_EXPORT_MAX_TOTAL + "]条,请输入更多查询条件!!");
        return ((int) (pageResult.getTotal()));
    }

    @Override
    public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
        MaterialDemandVo dto = this.convertParams(params);
        Page<MaterialDemandDetailExportVo> pageable = new Page<>(vo.getPageNo() + 1, vo.getPageSize());
        Page<MaterialDemandDetailExportVo> page = this.mapper.findDetailExportByDto(pageable, dto);
        return JSON.parseArray(JSON.toJSONString(page.getRecords()));
    }

    @Override
    public String getBusinessCode() {
        return "TPM_MATERIAL_DEMAND_DETAIL_EXPORT";
    }

    @Override
    public String getBusinessName() {
        return "TPM-物料需求提报明细导出";
    }

    @Override
    public Class<MaterialDemandDetailExportVo> findCrmExcelVoClass() {
        return MaterialDemandDetailExportVo.class;
    }

    private MaterialDemandVo convertParams(Map<String, Object> params) {
        // map 参数转换为对应的dto参数对象，可以手工进行修改设置
        MaterialDemandVo dto = JSON.parseObject(JSON.toJSONString(params), MaterialDemandVo.class);
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        return dto;
    }
}
