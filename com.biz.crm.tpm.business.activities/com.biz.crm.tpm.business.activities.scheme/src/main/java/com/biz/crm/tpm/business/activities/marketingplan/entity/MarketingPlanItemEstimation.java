package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_plan_item_estimation")
@Table(
        name = "tpm_marketing_plan_item_estimation",
        indexes = {
                @Index(name = "tpm_marketing_plan_item_estimation_index0", columnList = "scheme_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_plan_item_estimation", comment = "方案-品项测算")
@ApiModel(value = "MarketingPlanItemEstimation", description = "方案-品项测算")
public class MarketingPlanItemEstimation extends CustomerGainsAndLosses {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("品项编码")
    @Column(name = "item_code", columnDefinition = "varchar(32) comment '品项编码'")
    private String itemCode;

    @ApiModelProperty("品项名称")
    @Column(name = "item_name", columnDefinition = "varchar(64) comment '品项名称'")
    private String itemName;
}
