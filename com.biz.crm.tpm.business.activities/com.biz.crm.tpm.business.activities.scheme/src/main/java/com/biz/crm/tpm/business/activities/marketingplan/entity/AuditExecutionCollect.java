package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_audit_execution_collect")
@Table(
        name = "tpm_audit_execution_collect",
        indexes = {
                @Index(name = "tpm_audit_execution_collect", columnList = "scheme_detail_code"),
                @Index(name = "tpm_audit_execution_collect_index1", columnList = "scheme_code"),
                @Index(name = "tpm_audit_execution_collect_index2", columnList = "scheme_detail_code,create_post_code,terminal_code,dynamic_key,dynamic_form_code,parent_code,execution_time", unique = true),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_audit_execution_collect", comment = "终端活动执行稽查")
@ApiModel(value = "AuditExecutionCollect", description = "终端活动执行稽查")
public class AuditExecutionCollect extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    @Column(name = "act_execute_code", columnDefinition = "varchar(32) comment '活动执行编码'")
    private String actExecuteCode;

    @ApiModelProperty("门店编码")
    @Column(name = "terminal_code", length = 64, columnDefinition = "varchar(64) COMMENT '门店编码'")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    @Column(name = "terminal_name", columnDefinition = "varchar(255) COMMENT '门店名称'")
    private String terminalName;

    @ApiModelProperty("终端类型")
    @Column(name = "terminal_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '终端类型'")
    private String terminalType;

    @ApiModelProperty("执行结果")
    @Column(name = "result", length = 64, columnDefinition = "VARCHAR(64) COMMENT '执行结果'")
    private String result;

    @ApiModelProperty("执行人")
    @Column(name = "executor", length = 64, columnDefinition = "VARCHAR(64) COMMENT '执行人'")
    private String executor;

    @ApiModelProperty("执行时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "execute_date", columnDefinition = "DATETIME COMMENT '执行时间 '")
    private Date executeDate;

    /**
     * 执行时间 yyyy-mm-dd
     */
    @ApiModelProperty("执行时间")
    @Column(name = "execution_time", columnDefinition = "varchar(32) comment '执行时间'")
    private String executionTime;


    /****************************SAF步骤所需基础字段*******************************************/

    @ApiModelProperty("执行计划业务编码")
    @TableField(value = "parent_code")
    @Column(name = "parent_code", length = 64, columnDefinition = "varchar(64) COMMENT '执行计划业务编码'")
    private String parentCode;

    @ApiModelProperty("步骤业务编码stepCode")
    @TableField(value = "dynamic_key")
    @Column(name = "dynamic_key", length = 64, columnDefinition = "varchar(64) COMMENT '步骤业务编码stepCode'")
    private String dynamicKey;

    @ApiModelProperty("动态表单全局唯一编码formCode")
    @TableField(value = "dynamic_form_code")
    @Column(name = "dynamic_form_code", columnDefinition = "varchar(255) COMMENT '动态表单全局唯一编码formCode'")
    private String dynamicFormCode;

    @Column(name = "longitude", columnDefinition = "decimal(12,8) COMMENT '经度'")
    @ApiModelProperty("位置信息:经度")
    private BigDecimal longitude;

    @Column(name = "latitude", columnDefinition = "decimal(12,8) COMMENT '纬度'")
    @ApiModelProperty("位置信息:纬度")
    private BigDecimal latitude;


    //权限相关字段

    @ApiModelProperty("创建人组织")
    @Column(name = "create_org_code", columnDefinition = "varchar(32) comment '创建人组织'")
    private String createOrgCode;

    @ApiModelProperty("创建人组织名称")
    @Column(name = "create_org_name", columnDefinition = "varchar(64) comment '创建人组织名称'")
    private String createOrgName;

    @ApiModelProperty("创建人职位编码")
    @Column(name = "create_post_code", columnDefinition = "varchar(32) comment '创建人职位编码'")
    private String createPostCode;

    @ApiModelProperty("创建人职位名称")
    @Column(name = "create_post_name", columnDefinition = "varchar(64) comment '创建人职位名称'")
    private String createPostName;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("执行照片")
    private List<AuditExecutionCollectPicture> pictureList;

}
