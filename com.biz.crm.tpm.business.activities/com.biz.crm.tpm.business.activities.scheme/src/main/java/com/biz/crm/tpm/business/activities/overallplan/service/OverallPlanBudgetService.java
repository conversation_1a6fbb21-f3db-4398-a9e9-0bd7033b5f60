package com.biz.crm.tpm.business.activities.overallplan.service;

import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanBudget;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/8 16:18
 */
public interface OverallPlanBudgetService {

    /**
     * 查询预算使用
     *
     * @param orgCodes
     * @param budgetSubjectCodes
     * @param years
     * @param customerCodes
     * @param itemCodes
     * @param productCodes
     * @return
     */
    List<OverallPlanBudget> findOverallBudgetByCondition(List<String> orgCodes, List<String> budgetSubjectCodes, String years, List<String> customerCodes,
                                                         List<String> itemCodes, List<String> productCodes);
}
