package com.biz.crm.tpm.business.activities.contract.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.contract.dto.ContractCostPageDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDetailDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDto;
import com.biz.crm.tpm.business.activities.contract.service.ExternalContractService;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 16:16
 */
@RequestMapping("/v1/externalContractController")
@RestController
@Slf4j
public class ExternalContractController {

    @Autowired
    private ExternalContractService service;

    @ApiOperation(value = "查询分页列表")
    @GetMapping("findList")
    public Result<Page<ContractCostPageDto>> findList(@PageableDefault(50) Pageable pageable, ContractCostPageDto vo) {
        vo.setExternalFlag(BooleanEnum.TRUE.getCapital());
        return Result.ok(service.findList(pageable, vo));
    }


    @ApiOperation(value = "查询外部合同分页列表")
    @GetMapping("findExternalContractList")
    public Result<Page<ContractCostPageDto>> findExternalContractList(@PageableDefault(50) Pageable pageable, ContractCostPageDto vo) {
        vo.setExternalFlag(BooleanEnum.TRUE.getCapital());
        return Result.ok(service.findExternalContractList(pageable, vo));
    }


    @ApiOperation(value = "批量创建")
    @PostMapping("createBatch")
    public Result createBatch(@RequestBody List<ExternalContractDto> dtoList) {
        for (ExternalContractDto contractDto : dtoList) {
            List<ExternalContractDetailDto> detailList = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(contractDto.getRebateList())) {
                detailList.addAll(contractDto.getRebateList());
            }
            if (CollectionUtil.isNotEmpty(contractDto.getPromotionList())) {
                detailList.addAll(contractDto.getPromotionList());
            }
            if (CollectionUtil.isNotEmpty(contractDto.getFixedList())) {
                detailList.addAll(contractDto.getFixedList());
            }
            contractDto.setDetailList(detailList);
            contractDto.setExternalFlag(BooleanEnum.TRUE.getCapital());
            service.createContract(contractDto, BooleanEnum.TRUE.getCapital());

        }
        return Result.ok();
    }

    @ApiOperation(value = "根据条件分页查询列表")
    @GetMapping("findByConditions")
    public Result<Page<ExternalContractVo>> findByConditions(@PageableDefault(50) Pageable pageable, ContractCostPageDto vo) {
        vo.setExternalFlag(BooleanEnum.TRUE.getCapital());
        return Result.ok(service.findByConditions(pageable, vo));
    }
}
