package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MarketingPlanFieldsEnum {
    schemeCode("schemeCode"),
    deptCode("deptCode"),
    ratioBefore("ratioBefore"),
    ratioAfter("ratioAfter"),
    profitMarginBefore("profitMarginBefore"),
    profitMarginAfter("profitMarginAfter"),
    remark("remark"),
    planIncome("planIncome"),
    changePlanIncome("changePlanIncome"),
    title("title"),
    ;

    private String dictCode;

    public static MarketingPlanFieldsEnum findByCode(String code) {
        Optional<MarketingPlanFieldsEnum> first = Stream.of(MarketingPlanFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}