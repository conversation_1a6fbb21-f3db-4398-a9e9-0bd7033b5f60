package com.biz.crm.tpm.business.activities.regioncollect.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.tpm.business.activities.marketingplan.entity.CustomerGainsAndLosses;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_region_collect_department_estimation_back")
@Table(
        name = "tpm_region_collect_department_estimation_back",
        indexes = {
                @Index(name = "tpm_region_collect_department_estimation_back_index0", columnList = "collect_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_region_collect_department_estimation_back", comment = "大区汇总-部门测算备份数据")
@ApiModel(value = "RegionCollectDepartmentEstimationBack", description = "大区汇总-部门测算备份数据")
public class RegionCollectDepartmentEstimationBack extends CustomerGainsAndLosses {

    @ApiModelProperty("大区汇总编码")
    @Column(name = "collect_code", columnDefinition = "varchar(32) comment '大区汇总编码'")
    private String collectCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

}
