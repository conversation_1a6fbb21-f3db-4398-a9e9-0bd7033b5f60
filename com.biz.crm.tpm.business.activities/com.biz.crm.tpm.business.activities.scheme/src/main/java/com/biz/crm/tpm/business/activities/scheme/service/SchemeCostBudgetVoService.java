package com.biz.crm.tpm.business.activities.scheme.service;

import com.biz.crm.tpm.business.activities.scheme.vo.SchemeCostBudgetVo;

import java.util.Set;

/**
 * 方案预算成本;(tpm_scheme_cost_budget)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
public interface SchemeCostBudgetVoService {
  /**
   * 通过方案编号查询单条数据
   *
   * @param schemeCode 方案编号
   * @return 多条数据
   */
  Set<SchemeCostBudgetVo> findBySchemeCode(String schemeCode);

  /**
   * 新增数据
   *
   * @param schemeCostBudgetVo 实体对象
   * @return 新增结果
   */
  SchemeCostBudgetVo create(SchemeCostBudgetVo schemeCostBudgetVo);

  /**
   * 删除数据
   *
   * @param schemeCode 方案编号
   */
  void deleteBySchemeCode(String schemeCode);
}