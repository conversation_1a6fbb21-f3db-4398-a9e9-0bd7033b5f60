package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.AuditExecutionCollectPicture;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.AuditExecutionCollectPictureMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.repository.AuditExecutionCollectPictureRepository
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-08-02 14:11
 */
@Component
@Slf4j
public class AuditExecutionCollectPictureRepository extends ServiceImpl<AuditExecutionCollectPictureMapper, AuditExecutionCollectPicture> {
    public List<AuditExecutionCollectPicture> findByCollectId(String id) {
        return this.lambdaQuery()
                .eq(AuditExecutionCollectPicture::getCollectId, id).list();
    }

    public void deleteByCollectId(String id) {
        this.remove(Wrappers.lambdaQuery(AuditExecutionCollectPicture.class)
                .eq(AuditExecutionCollectPicture::getCollectId, id));
    }

}
