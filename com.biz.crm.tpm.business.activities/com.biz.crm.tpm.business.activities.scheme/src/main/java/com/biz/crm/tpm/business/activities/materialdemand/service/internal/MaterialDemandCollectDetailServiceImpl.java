package com.biz.crm.tpm.business.activities.materialdemand.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandCollect;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandCollectDetail;
import com.biz.crm.tpm.business.activities.materialdemand.repository.MaterialDemandCollectDetailRepository;
import com.biz.crm.tpm.business.activities.materialdemand.repository.MaterialDemandCollectRepository;
import com.biz.crm.tpm.business.activities.materialdemand.repository.MaterialDemandRepository;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandCollectDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandReportVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@Service
@Transactional
@Slf4j
public class MaterialDemandCollectDetailServiceImpl implements MaterialDemandCollectDetailService {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private MaterialDemandCollectDetailRepository repository;

    @Resource
    private MaterialDemandCollectRepository collectRepository;

    @Autowired
    private MaterialDemandDetailService demandDetailService;
    @Resource
    private MaterialDemandRepository materialDemandRepository;

    @Override
    public List<MaterialDemandCollectDetailVo> findListByCodes(List<String> codes) {
        List<MaterialDemandCollectDetail> collectDetails = repository.findListByCodes(codes);
        if (CollectionUtils.isEmpty(collectDetails)) {
            return Lists.newArrayList();
        }
        List<MaterialDemandCollectDetailVo> dataList = Lists.newArrayList();
        List<String> demandDetailCodes = collectDetails.stream().map(MaterialDemandCollectDetail::getDemandDetailCode).collect(Collectors.toList());
        List<MaterialDemandDetailVo> demandDetailVos = demandDetailService.findListByDemandDetailCodes(demandDetailCodes);
        Validate.isTrue(CollectionUtils.isNotEmpty(demandDetailVos), "物料提报数据已被删除");
        Map<String, MaterialDemandDetailVo> demandDetailVoMap = demandDetailVos.stream().collect(Collectors.toMap(MaterialDemandDetailVo::getDemandDetailCode, Function.identity()));
        for (MaterialDemandCollectDetail detail : collectDetails) {
            MaterialDemandDetailVo detailVo = demandDetailVoMap.get(detail.getDemandDetailCode());
            MaterialDemandCollectDetailVo vo = nebulaToolkitService.copyObjectByWhiteList(detailVo, MaterialDemandCollectDetailVo.class, HashSet.class, ArrayList.class);
            vo.setId(detail.getId());
            vo.setCollectCode(detail.getCollectCode());
            dataList.add(vo);
        }
        return dataList;
    }


    @Override
    public void saveBatchList(List<MaterialDemandCollectDetailVo> collectDetailVos, String collectCode) {
        repository.deleteByCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(collectDetailVos)) {
            return;
        }
        List<String> demandCodes = new ArrayList<>();
        collectDetailVos.forEach(x -> {
            x.setCollectCode(collectCode);
            x.setId(null);
            demandCodes.add(x.getDemandCode());
        });
        List<MaterialDemandCollectDetail> details = (List<MaterialDemandCollectDetail>) nebulaToolkitService.copyCollectionByWhiteList(collectDetailVos, MaterialDemandCollectDetailVo.class,
                MaterialDemandCollectDetail.class, HashSet.class, ArrayList.class);
        repository.saveBatch(details);
        materialDemandRepository.updateCollectStatus(BooleanEnum.TRUE.getCapital(), demandCodes);
    }

    @Override
    public void deleteByCollectCodeList(List<String> collectCodes) {
        List<MaterialDemandCollectDetailVo> list = this.findListByCodes(collectCodes);
        List<String> demandCodes = list.stream().map(MaterialDemandCollectDetailVo::getDemandCode).collect(Collectors.toList());
        repository.deleteByCodes(collectCodes);
        materialDemandRepository.updateCollectStatus(BooleanEnum.FALSE.getCapital(), demandCodes);
    }

    @Override
    public List<MaterialDemandCollectDetailVo> findListById(String id) {
        MaterialDemandCollect collect = collectRepository.queryByIdOrCode(id, null);
        if (ObjectUtils.isEmpty(collect)) {
            return Lists.newArrayList();
        }
        List<MaterialDemandCollectDetail> collectDetails = repository.findListByCodes(Lists.newArrayList(collect.getCollectCode()));
        List<String> demandDetailCodes = collectDetails.stream().map(MaterialDemandCollectDetail::getDemandDetailCode).collect(Collectors.toList());
        List<MaterialDemandDetailVo> demandDetailVos = demandDetailService.findListByDemandDetailCodes(demandDetailCodes);
        Map<String, MaterialDemandDetailVo> demandDetailVoMap = demandDetailVos.stream().collect(Collectors.toMap(MaterialDemandDetailVo::getDemandDetailCode, Function.identity()));
        List<MaterialDemandCollectDetailVo> dataList = Lists.newArrayList();
        for (MaterialDemandCollectDetail detail : collectDetails) {
            MaterialDemandDetailVo detailVo = demandDetailVoMap.get(detail.getDemandDetailCode());
            MaterialDemandCollectDetailVo vo = nebulaToolkitService.copyObjectByWhiteList(detailVo, MaterialDemandCollectDetailVo.class, HashSet.class, ArrayList.class);
            vo.setId(detail.getId());
            vo.setCollectCode(detail.getCollectCode());
            dataList.add(vo);
        }
        return dataList;
    }

}
