package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.entity.ActExecutionLog
 * @description: 活动执行采集记录
 * @author: xiaopeng.zhang
 * @create: 2024-08-01 10:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_act_execution_collect_log")
@Table(
        name = "tpm_act_execution_collect_log",
        indexes = {
                @Index(name = "tpm_act_execution_collect_log_index0", columnList = "scheme_detail_code"),
                @Index(name = "tpm_act_execution_collect_log_index1", columnList = "scheme_code"),
                @Index(name = "tpm_act_execution_collect_log_index2", columnList = "execution_type,client_code,position_code,execution_time", unique = false),
                @Index(name = "tpm_act_execution_collect_log_index3", columnList = "scheme_detail_code,execution_type,client_code,position_code,execution_time", unique = true),
                @Index(name = "tpm_act_execution_collect_log_index4", columnList = "act_execute_code", unique = false),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_act_execution_collect_log", comment = "活动执行采集记录表")
@ApiModel(value = "ActExecutionCollectLog", description = "活动执行采集记录")
public class ActExecutionCollectLog extends UuidOpEntity {

    /**
     * 方案编码
     */
    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", length = 32, columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", length = 32, columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    @Column(name = "act_execute_code", columnDefinition = "varchar(32) comment '活动执行编码'")
    private String actExecuteCode;

    /**
     * 执行类型
     */
    @ApiModelProperty("执行类型")
    @Column(name = "execution_type", length = 32, columnDefinition = "varchar(32) comment '方案明细编码'")
    private String executionType;

    /**
     * 终端/客户编码
     */
    @ApiModelProperty("终端/客户编码")
    @Column(name = "client_code", length = 64, columnDefinition = "varchar(64) COMMENT '终端/客户编码'")
    private String clientCode;

    /**
     * 终端/客户名称
     */
    @ApiModelProperty("终端/客户名称")
    @Column(name = "client_name", length = 64, columnDefinition = "varchar(64) COMMENT '终端/客户名称'")
    private String clientName;

    /**
     * 职位编码
     */
    @ApiModelProperty("职位编码")
    @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
    private String positionCode;

    /**
     * 职位名称
     */
    @ApiModelProperty("职位名称")
    @Column(name = "position_name", length = 64, columnDefinition = "varchar(64) COMMENT '职位名称'")
    private String positionName;

    /**
     * 执行时间 yyyy-mm-dd
     */
    @ApiModelProperty("执行时间")
    @Column(name = "execution_time", columnDefinition = "varchar(32) comment '执行时间'")
    private String executionTime;

    /**
     * 执行数据id
     */
    @ApiModelProperty("执行数据id")
    @Column(name = "execution_id", length = 32, columnDefinition = "varchar(32) comment '执行数据id'")
    private String executionId;
}
