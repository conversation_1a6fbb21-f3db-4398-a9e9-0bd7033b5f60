package com.biz.crm.tpm.business.activities.materialdemand.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemand;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandCollect;
import com.biz.crm.tpm.business.activities.materialdemand.mapper.MaterialDemandCollectMapper;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:34
 */
@Component
public class MaterialDemandCollectRepository extends ServiceImpl<MaterialDemandCollectMapper, MaterialDemandCollect> {

    public Page<MaterialDemandCollectVo> findList(Page<MaterialDemandCollectVo> page, MaterialDemandCollectVo vo) {
        vo.setTenantCode(TenantUtils.getTenantCode());
        return this.baseMapper.findList(page, vo);
    }

    public MaterialDemandCollect queryByIdOrCode(String id, String code) {
        return this.lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(id), MaterialDemandCollect::getId, id)
                .eq(ObjectUtils.isNotEmpty(code), MaterialDemandCollect::getCollectCode, code)
                .eq(MaterialDemandCollect::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MaterialDemandCollect::getTenantCode, TenantUtils.getTenantCode()).one();
    }

    public List<MaterialDemandCollect> findListByIdList(List<String> idList) {
        return this.lambdaQuery()
                .in(MaterialDemandCollect::getId, idList)
                .eq(MaterialDemandCollect::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MaterialDemandCollect::getTenantCode, TenantUtils.getTenantCode()).list();
    }


    public MaterialDemandCollect findByOrgCodeAndYears(String orgCode,String years){
        return this.lambdaQuery()
                .eq(MaterialDemandCollect::getOrgCode,orgCode)
                .eq(MaterialDemandCollect::getYears,years)
                .eq(MaterialDemandCollect::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
                .eq(MaterialDemandCollect::getTenantCode,TenantUtils.getTenantCode())
                .one();
    }
}
