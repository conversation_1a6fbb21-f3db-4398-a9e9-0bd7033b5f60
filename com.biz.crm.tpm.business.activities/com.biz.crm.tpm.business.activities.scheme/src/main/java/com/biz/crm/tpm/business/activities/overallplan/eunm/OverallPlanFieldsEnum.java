package com.biz.crm.tpm.business.activities.overallplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum OverallPlanFieldsEnum {
    schemeName("schemeName", "schemeName", "大区指引名称", "String"),
    schemeCode("schemeCode", "schemeCode", "大区指引编码", "String"),
    startDate("startDate", "startDate", "指引开始时间", "String"),
    endDate("endDate", "endDate", "指引结束时间", "String"),
    costTotal("costTotal", "costTotal", "费用合计", "BigDecimal"),
    estimateSalesVolume("estimateSalesVolume", "estimateSalesVolume", "预估销售金额", "BigDecimal"),
    productionRatio("productionRatio", "productionRatio", "指引投产比", "BigDecimal"),
    remark("remark", "remark", "备注", "String"),
    title("title", "title", "标题", "String"),
    ;

    private String key;
    private String dictCode;
    private String value;
    private String dateType;

    public static OverallPlanFieldsEnum findByCode(String code) {
        Optional<OverallPlanFieldsEnum> first = Stream.of(OverallPlanFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}