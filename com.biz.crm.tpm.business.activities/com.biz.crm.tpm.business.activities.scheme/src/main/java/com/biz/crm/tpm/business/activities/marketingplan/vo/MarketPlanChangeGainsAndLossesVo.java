package com.biz.crm.tpm.business.activities.marketingplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/5 19:46
 */
@Data
@ApiModel(value = "", description = "方案变更-客户损益对比")
public class MarketPlanChangeGainsAndLossesVo {

    @ApiModelProperty("部门编码")
    private String orgCode;

    @ApiModelProperty("部门名称")
    private String orgName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("规划收入")
    private BigDecimal planIncome;

    @ApiModelProperty("规划费率")
    private BigDecimal planRatio;

    @ApiModelProperty("利润率")
    private BigDecimal profitRatio;

    @ApiModelProperty("毛利率")
    private BigDecimal grossProfitRatio;

    @ApiModelProperty("规划申请金额")
    private BigDecimal marketingCost;

    @ApiModelProperty("规划收入-变更")
    private BigDecimal changePlanIncome;

    @ApiModelProperty("规划费率-变更")
    private BigDecimal changePlanRatio;

    @ApiModelProperty("利润率-变更")
    private BigDecimal changeProfitRatio;

    @ApiModelProperty("毛利率-变更")
    private BigDecimal changeGrossProfitRatio;

    @ApiModelProperty("规划收入-差异")
    private BigDecimal differencePlanIncome;

    @ApiModelProperty("规划费率-差异")
    private BigDecimal differencePlanRatio;

    @ApiModelProperty("利润率-差异")
    private BigDecimal differenceProfitRatio;

    @ApiModelProperty("毛利率-差异")
    private BigDecimal differenceGrossProfitRatio;

    @ApiModelProperty("规划费率")
    private String planRatioStr;

    @ApiModelProperty("利润率")
    private String profitRatioStr;

    @ApiModelProperty("毛利率")
    private String grossProfitRatioStr;

    @ApiModelProperty("规划收入-变更")
    private String changePlanIncomeStr;

    @ApiModelProperty("规划费率-变更")
    private String changePlanRatioStr;

    @ApiModelProperty("利润率-变更")
    private String changeProfitRatioStr;

    @ApiModelProperty("毛利率-变更")
    private String changeGrossProfitRatioStr;

    @ApiModelProperty("规划申请金额-变更")
    private BigDecimal changeMarketingCost;

    @ApiModelProperty("规划收入-差异")
    private String differencePlanIncomeStr;

    @ApiModelProperty("规划费率-差异")
    private String differencePlanRatioStr;

    @ApiModelProperty("利润率-差异")
    private String differenceProfitRatioStr;

    @ApiModelProperty("毛利率-差异")
    private String differenceGrossProfitRatioStr;

    @ApiModelProperty("规划申请金额-差异")
    private BigDecimal differenceMarketingCost;


}
