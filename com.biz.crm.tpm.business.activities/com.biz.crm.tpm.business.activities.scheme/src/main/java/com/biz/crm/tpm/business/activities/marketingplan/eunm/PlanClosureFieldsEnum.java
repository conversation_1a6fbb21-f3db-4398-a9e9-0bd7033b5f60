package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum PlanClosureFieldsEnum {
    closeName("closeName"),
    closeCode("closeCode"),
    closeAmount("closeAmount"),
    closeReason("closeReason"),
    title("title"),
    ;

    private String dictCode;

    public static PlanClosureFieldsEnum findByCode(String code) {
        Optional<PlanClosureFieldsEnum> first = Stream.of(PlanClosureFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}