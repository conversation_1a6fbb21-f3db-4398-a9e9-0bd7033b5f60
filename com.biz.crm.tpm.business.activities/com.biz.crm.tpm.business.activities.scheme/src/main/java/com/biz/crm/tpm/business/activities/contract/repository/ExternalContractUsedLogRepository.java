package com.biz.crm.tpm.business.activities.contract.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.contract.entity.ExternalContractUsedLog;
import com.biz.crm.tpm.business.activities.contract.mapper.ExternalContractUsedLogMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/8 21:55
 */
@Component
public class ExternalContractUsedLogRepository extends ServiceImpl<ExternalContractUsedLogMapper, ExternalContractUsedLog> {

    public void saveBatchList(List<ExternalContractUsedLog> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            this.saveBatch(list);
        }
    }
}
