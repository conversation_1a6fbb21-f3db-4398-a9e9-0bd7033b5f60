package com.biz.crm.tpm.business.activities.regioncollect.service.strategy;

import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/26
 */
public abstract class MarketingEstimationBuilder<SchemeCodes, Years> {

    protected SchemeCodes schemeCode;

    protected Years years;

    public MarketingEstimationBuilder<SchemeCodes, Years> init(SchemeCodes schemeCode, Years years) {
        this.schemeCode = schemeCode;
        this.years = years;
        return this;
    }

    /**
     * 预计收入
     *
     * @return
     */
    public abstract MarketingEstimationBuilder estimationRevenue();

    /**
     * 生产成本
     *
     * @return
     */
    public abstract MarketingEstimationBuilder productionCost();

    /**
     * 毛利率
     *
     * @return
     */
    public abstract MarketingEstimationBuilder grossProfitMargin();

    /**
     * 产品运输费用
     *
     * @return
     */
    public abstract MarketingEstimationBuilder productTransportCost();

    /**
     * 周边运输费用
     *
     * @return
     */
    public abstract MarketingEstimationBuilder peripheryTransport();

    /**
     * 运输费用合计
     *
     * @return
     */
    public abstract MarketingEstimationBuilder transportTotal();

    /**
     * 搭赠费用
     *
     * @return
     */
    public abstract MarketingEstimationBuilder giftCost();

    /**
     * 活动大类费用
     *
     * @return
     */
    public abstract MarketingEstimationBuilder categoryCost();

    /**
     * 营销费用小计
     *
     * @return
     */
    public abstract MarketingEstimationBuilder marketingCostTotal();

    /**
     * 边际贡献
     *
     * @return
     */
    public abstract MarketingEstimationBuilder marginalContribution();

    /**
     * 人工差旅费用
     *
     * @return
     */
    public abstract MarketingEstimationBuilder artificialTravelOnBusiness();

    /**
     * 费用合计
     *
     * @return
     */
    public abstract MarketingEstimationBuilder costTotal();

    /**
     * 利润
     *
     * @return
     */
    public abstract MarketingEstimationBuilder profit();

    /**
     * 费用转移
     *
     * @return
     */
    public abstract MarketingEstimationBuilder costShift();

    /**
     * 考核利润
     *
     * @return
     */
    public abstract MarketingEstimationBuilder accessProfits();

    public abstract List<RegionCollectMarketingEstimationVo> getList();
}
