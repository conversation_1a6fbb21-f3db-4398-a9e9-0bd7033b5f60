package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_plan_gains_and_losses")
@Table(
        name = "tpm_marketing_plan_gains_and_losses",
        indexes = {
                @Index(name = "tpm_marketing_plan_gains_and_losses_index1", columnList = "scheme_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_plan_gains_and_losses", comment = "营销方案-客户损益预测")
@ApiModel(value = "MarketingPlanGainsAndLosses", description = "营销方案-客户损益预测")
public class MarketingPlanGainsAndLosses extends CustomerGainsAndLosses {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

}
