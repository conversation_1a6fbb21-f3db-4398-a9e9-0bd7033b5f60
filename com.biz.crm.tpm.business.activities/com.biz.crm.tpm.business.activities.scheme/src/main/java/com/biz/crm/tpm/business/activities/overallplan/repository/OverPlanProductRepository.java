package com.biz.crm.tpm.business.activities.overallplan.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanProduct;
import com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanProductMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OverPlanProductRepository extends ServiceImpl<OverallPlanProductMapper, OverallPlanProduct> {

    public List<OverallPlanProduct> findListBySchemeCode(String schemeCode) {
        return this.lambdaQuery()
                .eq(OverallPlanProduct::getSchemeCode, schemeCode)
                .list();
    }

    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.remove(Wrappers.lambdaQuery(OverallPlanProduct.class)
                .in(OverallPlanProduct::getSchemeCode, schemeCodes));
    }

    public List<OverallPlanProduct> findListBySchemeDetailCodeList(List<String> schemeDetailCodeList) {
        return this.lambdaQuery()
                .in(OverallPlanProduct::getSchemeDetailCode, schemeDetailCodeList).list();
    }
}
