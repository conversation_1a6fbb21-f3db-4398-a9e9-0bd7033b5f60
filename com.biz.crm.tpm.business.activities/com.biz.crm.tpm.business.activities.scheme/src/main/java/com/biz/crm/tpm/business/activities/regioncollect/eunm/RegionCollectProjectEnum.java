package com.biz.crm.tpm.business.activities.regioncollect.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/6/26
 */
@Getter
@AllArgsConstructor
public enum RegionCollectProjectEnum {

    estimation_revenue("estimation_revenue", "预计收入", 1),
    actual_revenue("estimation_revenue", "实际收入", 1),
    production_cost("production_cost", "生产成本", 2),
    gross_profit_margin("gross_profit_margin", "毛利率", 3),
    product_transport_cost("product_transport_cost", "产品运输费用", 4),
    periphery_transport("periphery_transport", "周边运输费用", 5),
    transport_total("transport_total", "运输费用合计", 6),
    gift_cost("gift_cost", "搭赠费用", 7),
    marketing_cost_total("marketing_cost_total", "营销费用小计", 100),
    marginal_contribution("marginal_contribution", "边际贡献", 101),
    artificial_travel_on_business("artificial_travel_on_business", "人工、差旅等及分摊费用", 102),
    cost_total("cost_total", "费用合计", 103),
    profit("profit", "利润", 104),
    cost_shift("cost_shift", "费用转移", 105),
    access_profits("access_profits", "考核利润", 106),
    ;

    public static RegionCollectProjectEnum findByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        Optional<RegionCollectProjectEnum> first = Stream.of(RegionCollectProjectEnum.values()).filter(t -> t.getCode().equals(code)).findFirst();
        return first.orElse(null);
    }

    private String code;

    private String name;

    private Integer sort;
}
