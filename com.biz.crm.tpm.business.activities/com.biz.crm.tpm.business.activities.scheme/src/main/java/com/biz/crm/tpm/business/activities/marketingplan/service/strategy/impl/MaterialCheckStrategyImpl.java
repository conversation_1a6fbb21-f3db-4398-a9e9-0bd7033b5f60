package com.biz.crm.tpm.business.activities.marketingplan.service.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.base.util.BusinessRequiredUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.inquiry.sdk.service.InquiryVoService;
import com.biz.crm.mdm.business.inquiry.sdk.vo.InquiryVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.price.sdk.dto.FindPriceDto;
import com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum;
import com.biz.crm.mdm.business.product.sdk.dto.ProductQueryDto;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanMaterialTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCaseStrategy;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 物料模板
 * <AUTHOR>
 * @Date 2024/6/20 20:07
 */
@Slf4j
@Component
public class MaterialCheckStrategyImpl implements MarketingPlanCaseStrategy<MarketingPlanCaseVo> {


    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Resource
    private ProductVoService productVoService;

    @Resource
    private MaterialVoService materialVoService;

    @Autowired(required = false)
    private InquiryVoService priceModelVoService;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    private static final List<String> sell_material_type_list = MarketingPlanMaterialTypeEnum.getCodeList();


    @Override
    public void checkCaseList(List<MarketingPlanCaseVo> list, String years, String schemeCode, String originalSchemeCode, String cacheKey) {
        //获取配置模板信息
        List<ActivitiesTemplateConfigDetailVo> templateFiledList = checkHelper.getTemplateFiled(MarketingPlanCaseTypeEnum.material.getCode());
        Map<String, Boolean> filedMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getRequired));
        Map<String, String> chineseMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getTitle));
        //获取活动细类配置
        Set<String> detailCodeSet = checkHelper.getTemplateRelateDetailCodes(MarketingPlanCaseTypeEnum.material.getCode());
        //标记分组
        Map<String, Long> muchDepartmentMap = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMuchDepartmentMark()))
                .collect(Collectors.groupingBy(MarketingPlanCaseVo::getMuchDepartmentMark, Collectors.counting()));
        if (ObjectUtils.isNotEmpty(muchDepartmentMap)) {
            muchDepartmentMap = muchDepartmentMap.entrySet().stream()
                    .filter(entry -> entry.getValue() == 1)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        List<String> itemCodes = list.stream().filter(x -> CollectionUtils.isNotEmpty(x.getItemList())).map(k -> k.getItemList().get(0).getCode()).distinct().collect(Collectors.toList());

        //查询组织的成本中心
        Map<String, Set<String>> orgCostCenterMap = checkHelper.findOrgCostCenter(list);
        Set<String> costCenterCodeSet = orgCostCenterMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        Set<String> yearSet = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getYears()))
                .map(MarketingPlanCaseVo::getYears).collect(Collectors.toSet());
        List<String> customerCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()))
                .map(MarketingPlanCaseVo::getCustomerCode).collect(Collectors.toList());
        List<String> excludeSchemeCode = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(schemeCode)) {
            excludeSchemeCode.add(schemeCode);
        }
        if (ObjectUtils.isNotEmpty(originalSchemeCode)) {
            excludeSchemeCode.add(originalSchemeCode);
        }

        List<MarketingSalesPlanVo> salesPlanVos = Lists.newArrayList();
        String salesCacheKey = cacheKey + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + years;
        List<MarketingSalesPlanVo> salesPlanList1 = marketingSalesPlanService.findCacheList(salesCacheKey);
        List<MarketingSalesPlanVo> salesPlanList2 = marketingSalesPlanService.findListByConditionToMarketingScheme(yearSet, costCenterCodeSet, Sets.newHashSet(customerCodes),
                null, null, excludeSchemeCode, null, null);
        if (CollectionUtils.isNotEmpty(salesPlanList1)) {
            salesPlanVos.addAll(salesPlanList1);
        }
        if (CollectionUtils.isNotEmpty(salesPlanList2)) {
            salesPlanVos.addAll(salesPlanList2);
        }

        Map<String, List<MarketingSalesPlanVo>> salesPlanMap = salesPlanVos.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getCostCenterCode()))
                .collect(Collectors.groupingBy(MarketingSalesPlanVo::getCostCenterCode));
        //查询客户
        List<CustomerVo> customerVoList = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerVoMap = customerVoList.stream().filter(x -> EnableStatusEnum.ENABLE.getCode().equals(x.getEnableStatus()) &&
                        BooleanEnum.TRUE.getCapital().equals(x.getContractCustomer()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
        Set<String> companyCodeSet = Sets.newHashSet();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(customerVoList)) {
            companyCodeSet = customerVoList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getCompanyCode()))
                    .map(CustomerVo::getCompanyCode).collect(Collectors.toSet());
        }
        List<String> productCodes = Lists.newArrayList();
        List<String> items = Lists.newArrayList();
        Set<String> materialCodes = Sets.newHashSet();
        for (MarketingPlanCaseVo vo : list) {
            if (CollectionUtils.isNotEmpty(vo.getProductList())) {
                productCodes.addAll(vo.getProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(vo.getItemList())) {
                items.addAll(vo.getItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toSet()));
            }
            if (ObjectUtils.isNotEmpty(vo.getMaterialCode())) {
                materialCodes.add(vo.getMaterialCode());
            }
        }
        //品项信息
        Map<String, String> itemMap = checkHelper.findSapLevelMap(items);
        Map<String, ProductVo> productMap = checkHelper.findProductListByCodes(productCodes, companyCodeSet, items);
        Map<String, ProductPhaseVo> productPhaseMap = checkHelper.findProductPhaseMap(itemCodes);
        Map<String, MaterialVo> materialVoMap = Maps.newHashMap();
        Map<String, ProductVo> finishedMaterialMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(materialCodes)) {
            ProductQueryDto dto = new ProductQueryDto();
            dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            dto.setTenantCode(TenantUtils.getTenantCode());
            dto.setMaterialCodeSet(materialCodes);
            List<ProductVo> productVoList = productVoService.findByQueryDto(dto);
            if (CollectionUtils.isNotEmpty(productVoList)) {
                //产成品
                finishedMaterialMap = productVoList.stream().filter(x -> "ZERT".equals(x.getProductType()))
                        .collect(Collectors.toMap(ProductVo::getMaterialCode, Function.identity(), (v1, v2) -> v1));
                Set<String> materialCodeSet = productVoList.stream().filter(x -> !"ZERT".equals(x.getProductType()))
                        .map(ProductVo::getMaterialCode).collect(Collectors.toSet());
                MaterialSearchDto materialSearchDto = new MaterialSearchDto();
                materialSearchDto.setMaterialCodeSet(materialCodeSet);
                materialSearchDto.setCompanyCodeSet(companyCodeSet);
                List<MaterialVo> materialVoList = materialVoService.findBySearchDto(materialSearchDto);
                if (CollectionUtils.isNotEmpty(materialVoList)) {
                    materialVoMap = materialVoList.stream().collect(Collectors.toMap(MaterialVo::getMaterialCode, Function.identity()));
                }
            }
        }
        for (MarketingPlanCaseVo caseVo : list) {
            StringJoiner errMsg = new StringJoiner(";");
            if (ObjectUtils.isNotEmpty(caseVo.getErrMsg())) {
                errMsg.add(caseVo.getErrMsg());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getDetailCode())) {
                if (!detailCodeSet.contains(caseVo.getDetailCode())) {
                    errMsg.add("费用项目不属于当前表单模板配置");
                }
            }
            if (MarketingPlanCaseCheckHelper.paramNotNull(caseVo.getCustomerCode(), "客户编码", errMsg)) {
                if (customerVoMap.containsKey(caseVo.getCustomerCode())) {
                    CustomerVo customerVo = customerVoMap.get(caseVo.getCustomerCode());
                    caseVo.setCustomerName(customerVo.getCustomerName());
                    caseVo.setErpCode(customerVo.getErpCode());
                    caseVo.setCompanyCode(customerVo.getCompanyCode());
                    caseVo.setProductGroupCode(customerVo.getProductGroupCode());
                    caseVo.setChannelCode(customerVo.getChannelCode());
                    caseVo.setCooperateType(customerVo.getCooperateType());
                    caseVo.setHzlx(customerVo.getHzlx());
                    //校验客户公司代码与成本中心公司代码是否一致
                    if (ObjectUtils.isNotEmpty(caseVo.getCostCenterCompanyCode()) && !customerVo.getCompanyCode().equals(caseVo.getCostCenterCompanyCode())){
                        errMsg.add(String.format("客户公司代码与成本中心公司代码不一致,客户公司代码%s,成本中心公司代码%s",caseVo.getCompanyCode(),caseVo.getCostCenterCompanyCode()));
                    }
                } else {
                    errMsg.add(String.format("客户编码%s在主数据中未查询到或不是合同客户", caseVo.getCustomerCode()));
                }
            }
            if (MarketingPlanCaseCheckHelper.collectionNotNull("品项", errMsg, caseVo.getItemList())) {
                for (MarketingPlanProductVo vo : caseVo.getItemList()) {
                    if (itemMap.containsKey(vo.getCode())) {
                        vo.setName(itemMap.get(vo.getCode()));
                    } else {
                        errMsg.add(String.format("品项%s在主数据中未查询到", vo.getCode()));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                for (MarketingPlanProductVo vo : caseVo.getProductList()) {
                    if (productMap.containsKey(vo.getCode())) {
                        if (productMap.containsKey(vo.getCode())) {
                            ProductVo productVo = productMap.get(vo.getCode());
                            vo.setName(productVo.getProductName());
                            Validate.notNull(productVo.getMaterialCode(), String.format("商品%s未存在绑定的物料", productVo.getProductName()));
                            vo.setMaterialCode(productVo.getMaterialCode());
                        } else {
                            errMsg.add(String.format("商品%s不属于品项关联的商品", vo.getCode()));
                        }
                    } else {
                        errMsg.add(String.format("商品%s在主数据中未查询到", vo.getCode()));
                    }
                }
            }
            if (MarketingPlanCaseCheckHelper.paramNotNull(caseVo.getSellMaterialType(), "物料类型", errMsg)) {
                if (!sell_material_type_list.contains(caseVo.getSellMaterialType())) {
                    errMsg.add("物料类型错误");
                } else {
                    //不是标准物料类型，则需要到具体的物料
                    if (MarketingPlanMaterialTypeEnum.not_standard_material.getCode().equals(caseVo.getSellMaterialType())) {
                        if (MarketingPlanCaseCheckHelper.paramNotNull(caseVo.getMaterialCode(), "物料编码", errMsg)) {
                            if (materialVoMap.containsKey(caseVo.getMaterialCode())) {
                                MaterialVo materialVo = materialVoMap.get(caseVo.getMaterialCode());
                                caseVo.setMaterialName(materialVo.getMaterialName());
                                caseVo.setMaterialCostPrice(ObjectUtils.defaultIfNull(materialVo.getCostPrice(), BigDecimal.ZERO));
                            } else if (finishedMaterialMap.containsKey(caseVo.getMaterialCode())) {
                                ProductVo productVo = finishedMaterialMap.get(caseVo.getMaterialCode());
                                caseVo.setMaterialName(productVo.getProductName());
                                FindPriceDto findPriceDto = new FindPriceDto();
                                findPriceDto.setUserType(FindPriceUserTypeEnum.CUSTOMER.getDictCode());
                                findPriceDto.setUserCode(caseVo.getCustomerCode());
                                findPriceDto.setProductCodeSet(Sets.newHashSet(productVo.getProductCode()));
                                Map<String, InquiryVo> priceMap = this.priceModelVoService.findPrice((JSONObject) JSONObject.toJSON(findPriceDto));
                                if (ObjectUtils.isNotEmpty(priceMap)) {
                                    //随机取一个计算结束
                                    for (Map.Entry<String, InquiryVo> entry : priceMap.entrySet()) {
                                        BigDecimal price = entry.getValue().getPrice();
                                        caseVo.setMaterialCostPrice(price);
                                        break;
                                    }
                                } else {
                                    caseVo.setMaterialCostPrice(BigDecimal.ZERO);
                                    errMsg.add(String.format("物料信息%s对应的产品未找到价格", caseVo.getMaterialCode()));
                                }
                            } else {
                                errMsg.add(String.format("物料信息%s在主数据中未查询到", caseVo.getMaterialCode()));
                            }
                        }
                        caseVo.setMaterialCostPrice(ObjectUtils.defaultIfNull(caseVo.getMaterialCostPrice(), BigDecimal.ZERO));
                        if (MarketingPlanCaseCheckHelper.paramNotNull(caseVo.getMaterialNum(), "物料数量", errMsg)) {
                            caseVo.setApplyAmount(caseVo.getMaterialNum().multiply(caseVo.getMaterialCostPrice()));
                        }
                    }
                }
            }
            MarketingPlanCaseCheckHelper.paramNotNull(caseVo.getApplyAmount(), "申请金额", errMsg);
            if (Objects.nonNull(caseVo.getApplyAmount()) && caseVo.getApplyAmount().compareTo(BigDecimal.ZERO) <= 0) {
                errMsg.add("申请金额必须大于0");
            }
            MarketingPlanCaseCheckHelper.paramNotNull(caseVo.getBearAmount(), "我方承担金额", errMsg);
            //申请金额必须大于我方承担金额
            if (ObjectUtils.isNotEmpty(caseVo.getApplyAmount()) && ObjectUtils.isNotEmpty(caseVo.getBearAmount())
                    && caseVo.getApplyAmount().compareTo(caseVo.getBearAmount()) == -1) {
                errMsg.add("申请金额必须大于我方承担金额");
            }
//            checkHelper.paramNotNull(caseVo.getCusBearAmount(), "客户承担金额", errMsg);
            if (ObjectUtils.isNotEmpty(caseVo.getApplyAmount()) && ObjectUtils.isNotEmpty(caseVo.getBearAmount())) {
                caseVo.setCusBearAmount(caseVo.getApplyAmount().subtract(caseVo.getBearAmount()));
            }
//            checkHelper.paramNotNull(caseVo.getActDesc(), "活动描述", errMsg);

            caseVo.setMuchDepartmentFlag(BooleanEnum.FALSE.getCapital());
            //多部门标记
            if (ObjectUtils.isNotEmpty(caseVo.getMuchDepartmentMark())) {
                caseVo.setMuchDepartmentFlag(BooleanEnum.TRUE.getCapital());
            }
            if (ObjectUtils.isNotEmpty(muchDepartmentMap) && muchDepartmentMap.containsKey(caseVo.getMuchDepartmentMark())) {
                errMsg.add("多部门标记只找到一个,不可进行多部门标记");
            }
            //此处单独调用
            String msg = BusinessRequiredUtil.filedRequiredCheck(filedMap, chineseMap, caseVo);
            if (ObjectUtils.isNotEmpty(msg)) {
                errMsg.add(msg);
            }

            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                caseVo.setCheckFlag(Boolean.FALSE);
                caseVo.setErrMsg(errMsg.toString());
            }
            if (caseVo.getCheckFlag()) {
                //计算费率
                Set<String> costCenterCodes = orgCostCenterMap.getOrDefault(caseVo.getBelongDepartmentCode(), Sets.newHashSet());
                checkHelper.calPlanCaseRatio(salesPlanMap, costCenterCodes, caseVo);
            }
            String code = caseVo.getItemList().get(0).getCode();
            log.info("materialExpensePlanItemCode: {}", code);
            if (StringUtils.isNotEmpty(code) && Lists.newArrayList("BC1001", "BC1002").contains(code)) {
                caseVo.setLineTaxRate(new BigDecimal("0.1"));
                caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
            } else {
                ProductPhaseVo productPhaseVo = productPhaseMap.get(code);
                if (Objects.isNull(productPhaseVo) || Objects.isNull(productPhaseVo.getTaxRate())) {
                    caseVo.setErrMsg("未匹配到对应品项或对应品项未维护税率");
                    caseVo.setCheckFlag(Boolean.FALSE);
                } else {
                    caseVo.setLineTaxRate(productPhaseVo.getTaxRate());
                    caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                }
            }

            //寻年度预算
            checkHelper.matchBudget(filedMap, chineseMap, caseVo);
        }
        list.forEach(x -> x.setCaseType(getCaseType()));
        //校验多部门
        checkHelper.checkMuchDepartmentMark(list);

    }

    @Override
    public String getCaseType() {
        return MarketingPlanCaseTypeEnum.material.getCode();
    }
}
