package com.biz.crm.tpm.business.activities.scheme.service;

import com.biz.crm.tpm.business.activities.scheme.dto.SchemeDto;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 方案;(tpm_scheme)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
public interface TpmSchemeVoService {

  /**
   * 生成操作标记
   */
  String preSave();

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  Page<SchemeVo> findByConditions(Pageable pageable, SchemeDto dto);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  SchemeVo findById(String id);

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  SchemeVo findByCode(String code);

  /**
   * 新增数据
   *
   * @param schemeVo 实体对象
   * @return 新增结果
   */
  SchemeVo create(SchemeVo schemeVo);

  /**
   * 修改新据
   *
   * @param schemeVo 实体对象
   * @return 修改结果
   */
  SchemeVo update(SchemeVo schemeVo);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(List<String> ids);

  /**
   * 根据费用预算编号查询是否存在已经绑定方案
   *
   * @param costBudgetCode
   * @return
   */
  boolean existsByCostBudget(String costBudgetCode);

  /**
   * 根据keyword查询方案信息
   *
   * @param keyword
   * @return
   */
  List<SchemeVo> findBySelect(String keyword);
}