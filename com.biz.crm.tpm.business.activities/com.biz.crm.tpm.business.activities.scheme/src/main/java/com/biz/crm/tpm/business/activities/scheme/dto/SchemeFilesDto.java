package com.biz.crm.tpm.business.activities.scheme.dto;

import com.biz.crm.business.common.sdk.dto.FileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 传输传递dto：方案附件;
 * <AUTHOR> <PERSON>
 * @date : 2022-5-31
 */
@ApiModel(value = "SchemeFilesDto",description = "方案附件")
@Getter
@Setter
public class SchemeFilesDto extends FileDto {
  /** 方案编号 */
  @ApiModelProperty(name = "schemeCode",notes = "方案编号", value= "方案编号")
  private String schemeCode;
}