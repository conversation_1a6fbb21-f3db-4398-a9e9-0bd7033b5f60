package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanGainsAndLossesMapper;
import com.google.common.collect.Lists;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:42
 */
@Component
public class MarketingPlanGainsAndLossesRepository extends ServiceImpl<MarketingPlanGainsAndLossesMapper, MarketingPlanGainsAndLosses> {


    public Page<MarketingPlanGainsAndLosses> findList(Page<MarketingPlanGainsAndLosses> page, MarketingPlanGainsAndLosses vo) {
        return this.baseMapper.findList(page, vo);
    }

    /**
     * 通过方案编码查询
     *
     * @param schemeCodes
     * @return
     */
    public List<MarketingPlanGainsAndLosses> findListBySchemeCodes(List<String> schemeCodes) {
        return this.lambdaQuery()
                .in(MarketingPlanGainsAndLosses::getSchemeCode, schemeCodes)
                .list();
    }

    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.remove(Wrappers.lambdaQuery(MarketingPlanGainsAndLosses.class)
                .in(MarketingPlanGainsAndLosses::getSchemeCode, schemeCodes));
    }
}
