package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 17:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MarketingPlanProductVo", description = "营销方案规划-方案明细产品范围")
public class MarketingPlanProductVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("数量")
    private BigDecimal num;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;
}
