package com.biz.crm.tpm.business.activities.regioncollect.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_region_collect_scheme")
@Table(
        name = "tpm_region_collect_scheme",
        indexes = {
                @Index(name = "tpm_region_collect_scheme_index0", columnList = "collect_code,scheme_code", unique = true),
                @Index(name = "tpm_region_collect_scheme_index1", columnList = "del_flag"),
                @Index(name = "tpm_region_collect_scheme_index2", columnList = "collect_code"),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_region_collect_scheme", comment = "大区汇总方案明细")
@ApiModel(value = "RegionCollectScheme", description = "大区汇总方案明细")
public class RegionCollectScheme extends UuidFlagOpEntity {

    @ApiModelProperty("汇总编码")
    @Column(name = "collect_code", columnDefinition = "varchar(32) comment '汇总编码'")
    private String collectCode;

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称'")
    private String schemeName;

    @Column(name = "user_name", columnDefinition = "varchar(32) comment '人员账号'")
    private String userName;

    @Column(name = "full_name", columnDefinition = "varchar(64) comment '人员姓名'")
    private String fullName;

    @Column(name = "org_code", columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

}
