package com.biz.crm.tpm.business.activities.giftrebatereport.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.giftrebatereport.entity.GiftAndRebateOccupyAmountReport;
import com.biz.crm.tpm.business.activities.giftrebatereport.dto.GiftAndRebateOccupyAmountReportDto;
import com.biz.crm.tpm.business.activities.giftrebatereport.vo.GiftAndRebateOccupyAmountReportVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 搭赠及返利实时金额占用报表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface GiftAndRebateOccupyAmountReportMapper extends BaseMapper<GiftAndRebateOccupyAmountReport> {

    /**
     * 分页查询搭赠及返利实时金额占用报表
     *
     * @param page 分页对象
     * @param dto  查询条件
     * @return 分页结果
     */
    Page<GiftAndRebateOccupyAmountReportVo> findByConditions(Page<GiftAndRebateOccupyAmountReportVo> page, @Param("dto") GiftAndRebateOccupyAmountReportDto dto);

    /**
     * 根据条件查询报表数据
     *
     * @param dto 查询条件
     * @return 报表数据列表
     */
    java.util.List<GiftAndRebateOccupyAmountReportVo> findListByConditions(@Param("dto") GiftAndRebateOccupyAmountReportDto dto);

    /**
     * 统计报表数据总数
     *
     * @param dto 查询条件
     * @return 总数
     */
    Long countByConditions(@Param("dto") GiftAndRebateOccupyAmountReportDto dto);

    /**
     * 查询本月审批通过的且当天与活动结束时间大于1天的营销方案明细
     * left join tpm_marketing_plan 查询status = 3 的数据
     *
     * @param tenantCode 租户编码
     * @param delFlag 删除标记
     * @param monthStart 月初时间
     * @param monthEnd 月末时间
     * @return 符合条件的营销方案明细列表
     */
    List<MarketingPlanCase> queryApprovedMarketingPlanCases(
            @Param("tenantCode") String tenantCode,
            @Param("delFlag") String delFlag,
            @Param("monthStart") String monthStart,
            @Param("monthEnd") String monthEnd
    );
}
