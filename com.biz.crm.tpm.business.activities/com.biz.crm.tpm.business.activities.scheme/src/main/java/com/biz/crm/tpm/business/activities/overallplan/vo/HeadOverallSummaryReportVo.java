package com.biz.crm.tpm.business.activities.overallplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/8 17:15
 */
@Data
@ApiModel("总部指引汇总")
public class HeadOverallSummaryReportVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("创建人组织编码")
    private String orgCode;

    @ApiModelProperty("创建人组织名称")
    private String orgName;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("已承接金额")
    private BigDecimal alreadyBearAmount;

    @ApiModelProperty("未承接金额")
    private BigDecimal notBearAmount;

    @ApiModelProperty("关闭金额")
    private BigDecimal closeAmount;

    @ApiModelProperty("计提金额")
    private BigDecimal withholdingAmount;

    @ApiModelProperty("结案金额")
    private BigDecimal auditAmount;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;
}
