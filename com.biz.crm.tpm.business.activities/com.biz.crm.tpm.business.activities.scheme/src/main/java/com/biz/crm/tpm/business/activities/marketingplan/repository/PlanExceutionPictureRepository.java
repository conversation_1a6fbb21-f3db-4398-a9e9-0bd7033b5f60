package com.biz.crm.tpm.business.activities.marketingplan.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanBigDateExecutionCollectDetail;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanExecutionPicture;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanExceutionPictureMapper;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionPictureVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 执行照片(PlanExceutionPicture)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-02 20:06:33
 */
@Component
public class PlanExceutionPictureRepository extends ServiceImpl<PlanExceutionPictureMapper, PlanExecutionPicture> {

  @Autowired
  private PlanExceutionPictureMapper planExceutionPictureMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;


  //TODO 原方法弃用

  public List<PlanExecutionPictureVo> findByCode(String code) {
//    List<PlanExecutionPicture> list = lambdaQuery().eq(PlanExecutionPicture::getSchemeDetailCode, code).list();
//    return CollectionUtils.isEmpty(list) ? new ArrayList<>() : new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, PlanExecutionPicture.class, PlanExecutionPictureVo.class, LinkedHashSet.class, ArrayList.class));
    return Lists.newArrayList();
  }

  public void deleteByCode(String code) {
//    lambdaUpdate().eq(PlanExecutionPicture::getSchemeDetailCode, code).remove();
  }

  public List<PlanExecutionPicture> findByCollectId(String id) {
    return this.lambdaQuery()
            .eq(PlanExecutionPicture::getCollectId, id).list();
  }

  public List<PlanExecutionPicture> findByCollectIds(List<String> ids) {
    return this.lambdaQuery()
            .in(PlanExecutionPicture::getCollectId, ids).list();
  }

  public void deleteByCollectId(String id) {
    this.remove(Wrappers.lambdaQuery(PlanExecutionPicture.class)
            .eq(PlanExecutionPicture::getCollectId, id));
  }
}

