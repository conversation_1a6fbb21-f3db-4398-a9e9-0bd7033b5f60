package com.biz.crm.tpm.business.activities.scheme.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体：方案活动;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-2
 */
@ApiModel(value = "ActivitiesScheme", description = "方案活动")
@TableName("tpm_activities_scheme")
@Getter
@Setter
@Entity(name = "tpm_activities_scheme")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_scheme", comment = "方案活动")
public class ActivitiesScheme extends TenantFlagOpEntity {
  private static final long serialVersionUID = -1760386237091417053L;
  /**
   * 活动名称
   */
  @ApiModelProperty(name = "活动名称", notes = "活动名称")
  @Column(name = "activities_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动名称 '")
  private String activitiesName;

  /**
   * 活动编号
   */
  @ApiModelProperty(name = "活动编号", notes = "活动编号")
  @Column(name = "activities_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;

  /**
   * 开始时间
   */
  @ApiModelProperty(name = "开始时间", notes = "开始时间")
  @Column(name = "begin_time", nullable = false, columnDefinition = "DATETIME COMMENT '开始时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @ApiModelProperty(name = "结束时间", notes = "结束时间")
  @Column(name = "end_time", nullable = false, columnDefinition = "DATETIME COMMENT '结束时间 '")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  /**
   * 方案编号
   */
  @ApiModelProperty(name = "方案编号", notes = "方案编号")
  @Column(name = "scheme_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '方案编号 '")
  private String schemeCode;

  /**
   * 总申请金额
   */
  @ApiModelProperty(name = "总申请金额", notes = "总申请金额")
  @Column(name = "total_apply_amount", nullable = false, length = 24, scale = 4, columnDefinition = "DECIMAL(24,4) COMMENT '总申请金额 '")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("活动状态")
  @TableField(value = "status")
  @Column(name = "status", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '活动状态'")
  private String status;
}