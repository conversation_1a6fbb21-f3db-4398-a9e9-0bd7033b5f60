package com.biz.crm.tpm.business.activities.materialdemand.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_material_demand_collect_detail")
@Table(
        name = "tpm_material_demand_collect_detail",
        indexes = {
                @Index(name = "tpm_material_demand_collect_detail_index0", columnList = "collect_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_material_demand_collect_detail", comment = "物料需求汇总明细")
@ApiModel(value = "MaterialDemandCollectDetail", description = "物料需求汇总明细")
public class MaterialDemandCollectDetail extends UuidFlagOpEntity {

    @ApiModelProperty("编码")
    @Column(name = "collect_code", columnDefinition = "varchar(32) comment '编码'")
    private String collectCode;

    @ApiModelProperty("物料提报编码")
    @Column(name = "demand_code", columnDefinition = "varchar(32) comment '物料提报编码'")
    private String demandCode;

    @ApiModelProperty("明细编码")
    @Column(name = "demand_detail_code", columnDefinition = "varchar(32) comment '明细编码'")
    private String demandDetailCode;
}
