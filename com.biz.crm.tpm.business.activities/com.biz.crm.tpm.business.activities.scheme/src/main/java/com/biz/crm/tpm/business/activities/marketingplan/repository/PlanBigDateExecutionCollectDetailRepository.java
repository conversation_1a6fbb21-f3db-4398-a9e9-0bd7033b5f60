package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanBigDateExecutionCollectDetail;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanBigDateExecutionCollectDetailMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.repository.PlanBigDateExecutionCollectDetailRepository
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:06
 */
@Component
@Slf4j
public class PlanBigDateExecutionCollectDetailRepository extends ServiceImpl<PlanBigDateExecutionCollectDetailMapper, PlanBigDateExecutionCollectDetail> {
    public List<PlanBigDateExecutionCollectDetail> findByCollectId(String id) {
        return this.lambdaQuery()
                .eq(PlanBigDateExecutionCollectDetail::getCollectId, id).list();
    }

    public void deleteByCollectId(String id) {
        this.remove(Wrappers.lambdaQuery(PlanBigDateExecutionCollectDetail.class)
                .eq(PlanBigDateExecutionCollectDetail::getCollectId, id));
    }

    public List<PlanBigDateExecutionCollectDetail> findListByCollectIds(List<String> collectIds) {
        if (CollectionUtils.isEmpty(collectIds)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(PlanBigDateExecutionCollectDetail::getCollectId, collectIds)
                .list();
    }
}
