package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum PlanClosureDetailFieldsEnum {
    secondCostCategoryName("secondCostCategoryName"),
    bearDepartmentName("bearDepartmentName"),
    bearType("bearType"),
    estimatedCost("estimatedCost"),
    cashCondition("cashCondition"),
    ;

    private String dictCode;

    public static PlanClosureDetailFieldsEnum findByCode(String code) {
        Optional<PlanClosureDetailFieldsEnum> first = Stream.of(PlanClosureDetailFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}