package com.biz.crm.tpm.business.activities.marketingplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.business.activities.marketingplan.dto.CustomerMonthCategoryApplyAmountDTO;
import com.biz.crm.tpm.business.activities.marketingplan.dto.MarketingPlanSecondCategoryDto;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.TerminalExpenseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.TpmCostBudgetVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectImageVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:22
 */
public interface MarketingPlanCaseMapper extends BaseMapper<MarketingPlanCase> {

    Page<MarketingPlanCaseVo> findCaseList(Page<MarketingPlanCaseVo> page, @Param("vo") MarketingPlanCaseVo vo, @Param("processStatus") String processStatus);

    Page<MarketingPlanCaseVo> findCaseListByChangeScheme(Page<MarketingPlanCaseVo> page, @Param("vo") MarketingPlanCaseVo vo,
                                                         @Param("processStatus") String processStatus, @Param("originalSchemeDetailCodes") List<String> originalSchemeDetailCodes);

    /**
     * 查询结案示例
     *
     * @param tenantCode
     * @return
     */
    List<DictDataVo> findCloseCaseExampleList(@Param("tenantCode") String tenantCode);

    /**
     * 基础查询
     *
     * @param vo
     * @return
     */
    List<MarketingPlanCase> findListByBaseCondition(@Param("vo") MarketingPlanCaseQueryDto vo);

    List<MarketingPlanCaseVo> findMaterialListByMaterialCodesAndOrgCodes(@Param("materialCodes") List<String> materialCodes,
                                                                         @Param("orgCodes") List<String> orgCodes,
                                                                         @Param("tenantCode") String tenantCode);

    List<MarketingPlanCaseVo> findMaterialListByMaterialCodesAndOrgCodesAndExclusiveSchemeCode(@Param("materialCodes") List<String> materialCodes,
                                                                                               @Param("orgCodes") List<String> orgCodes, @Param("schemeCode") String schemeCOde,
                                                                                               @Param("tenantCode") String tenantCode);

    List<MarketingPlanCaseVo> findAuditAndWithholdingAndCashReleaseDetailCodeList(@Param("releaseDetailCodeList") List<String> releaseDetailCodeList, @Param("bearFlag") String bearFlag,
                                                                                  @Param("tenantCode") String tenantCode);

    Page<MarketingPlanCaseVo> findRegionMarketingPlanCase(Page<MarketingPlanCaseVo> page, @Param("releaseDetailCode") String releaseDetailCode, @Param("tenantCode") String tenantCode);

    List<MarketingPlanCaseVo> findRegionMarketingPlanCases( @Param("releaseDetailCodes") List<String> releaseDetailCode, @Param("tenantCode") String tenantCode);

    Page<MarketingPlanCaseVo> findMarketingPlanCaseReportList(Page<MarketingPlanCaseVo> page, @Param("vo") MarketingPlanCaseVo vo, @Param("tenantCode") String tenantCode);

    Page<MarketingPlanCaseVo> findNotMarketingClosedCaseList(Page<MarketingPlanCaseVo> page, @Param("vo") MarketingPlanCaseQueryDto vo, @Param("tenantCode") String tenantCode);

    Page<MarketingPlanCaseVo> findSafActPolicy(Page<MarketingPlanCaseVo> page, @Param("vo") MarketingPlanCaseQueryDto vo);


    Page<MarketingPlanCaseExecuteVo> findMarketingPlanCaseExecuteList(@Param("page") Page<MarketingPlanCaseExecuteVo> page, @Param("dto") MarketingPlanCaseQueryDto vo);

    List<MarketingPlanCaseExecuteVo> findByTerminalMarketingPlanCaseExecuteList(@Param("dto") MarketingPlanCaseQueryDto vo);

    List<String> findSendSfaCostTypeDetail(@Param("executionType") String executionType);

    List<CostTypeDetailCollectVo> findCollectCostTypeDetail(@Param("detailCodeList") List<String> detailCodeList);

    List<ApprovalCollectImageVo> findCollectImage(@Param("collectCodeList") List<String> collectCodeList);

    /**
     * 终端申请费用，兑付费用统计
     *
     * @param vo
     * @return
     */
    List<TerminalExpenseVo> findTerminalExpense(@Param("vo") MarketingPlanCaseQueryDto vo);

    List<TerminalExpenseVo> findCustomerExpense(@Param("vo") MarketingPlanCaseQueryDto vo);

    List<MarketingPlanCaseVo> findContractByDepartmentCodesAndYears(@Param("departmentCodes") List<String> departmentCodes,
                                                                    @Param("years") String years, @Param("tenantCode") String tenantCode);

    /**
     * 通过年度预算编码查询申请金额
     *
     * @param budgetCodes
     * @return
     */
    List<TpmCostBudgetVo> findMarketingPlanApplyAmount(@Param("budgetCodes") List<String> budgetCodes);


    /**
     * 通过年度预算编码查询变更中并且是新增的申请金额
     * @param budgetCodes
     * @return
     */
    List<TpmCostBudgetVo> findMarketingPlanChangeCommitApplyAmount(@Param("budgetCodes") List<String> budgetCodes);

    /**
     * 查询预提金额
     *
     * @param budgetCodes
     * @return
     */
    List<TpmCostBudgetVo> findHandleWithholdingAmount(@Param("budgetCodes") List<String> budgetCodes);


    List<TpmCostBudgetVo> findWithholdingAmount(@Param("budgetCodes")List<String> budgetCodes);

    /**
     * 查询结案金额
     *
     * @param budgetCodes
     * @return
     */
    List<TpmCostBudgetVo> findEndCaseAmount(@Param("budgetCodes") List<String> budgetCodes);


    List<String> findWithholdingBySchemeCodes(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    List<MarketingPlanCaseVo> findAlreadyUndertakeAmount(@Param("releaseDetailCodes") List<String> releaseDetailCodes);

    List<MarketingPlanCaseVo> findChanging(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    List<MarketingPlanCaseVo> findByCollectCode(@Param("collectCode") String collectCode);

    List<MarketingPlanCaseVo> findBySchemeCodes(@Param("schemeCodes") List<String> schemeCodes);

    /**
     * 查询时间段内发生过编辑的终端编码集合
     *
     * @param startDate
     * @param endDate
     * @return
     */
    List<String> findExpenseChangeTerminal(@Param("startDate") Date startDate, @Param("endDate") Date endDate);


    /**
     * SFA移动访销费用投入情况 统计分页接口
     *
     * @param page
     * @param vo
     * @return
     */
    Page<MarketingPlanCaseVo> findExpensesCountByConditions(Page<MarketingPlanCaseVo> page, @Param("vo") MarketingPlanCaseQueryDto vo);


    List<String> findCategoryCodeBySecondCategory(@Param("tenantCode") String tenantCode, @Param("secondCategoryCode") String secondCategoryCode);


    List<String> findCategoryCodeBySecondCategoryList(@Param("tenantCode") String tenantCode, @Param("secondCategoryCodes") List<String> secondCategoryCodes);


    Page<MarketingPlanCaseVo> findPlanCaseListBySecondCategory(Page<MarketingPlanCaseVo> page, @Param("vo") MarketingPlanSecondCategoryDto vo);

    List<MarketingPlanCaseVo> findPlanCaseListBySecondCategoryList(@Param("vo") MarketingPlanSecondCategoryDto vo);

    List<String> findCountTerminalByTerminalCodesAndYears(@Param("terminalCodeList") List<String> terminalCodeList, @Param("years") String years);

    List<MarketingPlanCaseVo> findAmountByOrgCodesAndYearsList(@Param("orgCodes") List<String> orgCodes, @Param("yearsList") List<String> yearsList);

    List<MarketingPlanCaseVo> findCaseListByDmsMiniHomePage(@Param("customerCode") String customerCode, @Param("yearsList") List<String> yearsList);

    List<MarketingPlanCaseVo> findCaseListByYearsAndDelFlagProcess(@Param("years") String years);

    List<CustomerMonthCategoryApplyAmountDTO> sumApplyAmountByCustomerCodesYearMonth(@Param("customerCodes") List<String> customerCodes, @Param("years") String years);

    Page<MarketingPlanCaseVo> findContractByYearsAndLogin(Page<MarketingPlanCaseVo> page, @Param("vo") MarketingPlanVo vo);

    List<MarketingPlanCaseVo> findCaseListAuditData(@Param("list") List<String> collect);

    List<MarketingPlanCaseVo> findGiftByYearOrg(@Param("years") String yearMonthLy, @Param("orgCodeList") List<String> orgCodeList, @Param("itemCodes") List<String> itemCodes);

    List<MarketingPlanCaseVo> queryCurMonthApprovedMarketingPlanCases(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<MarketingPlanCase> findByReleaseDetailCodes(@Param("releaseDetailCodes") List<String> releaseDetailCodes);
}
