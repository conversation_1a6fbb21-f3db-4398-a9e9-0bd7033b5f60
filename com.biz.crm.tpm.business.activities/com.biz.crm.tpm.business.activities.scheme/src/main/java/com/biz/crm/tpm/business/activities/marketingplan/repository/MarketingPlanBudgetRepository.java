package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanBudget;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanBudgetMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/13 20:08
 */
@Component
public class MarketingPlanBudgetRepository extends ServiceImpl<MarketingPlanBudgetMapper, MarketingPlanBudget> {

    public void deleteBySchemeCode(String schemeCode) {
        this.lambdaUpdate()
                .eq(MarketingPlanBudget::getSchemeCode, schemeCode)
                .remove();
    }


    public List<MarketingPlanBudget> findListBySchemeCode(String schemeCode){
        return this.lambdaQuery()
                .eq(MarketingPlanBudget::getSchemeCode,schemeCode)
                .list();
    }


}
