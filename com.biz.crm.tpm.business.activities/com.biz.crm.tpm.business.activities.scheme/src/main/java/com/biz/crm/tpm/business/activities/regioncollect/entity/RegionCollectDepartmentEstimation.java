package com.biz.crm.tpm.business.activities.regioncollect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import com.biz.crm.tpm.business.activities.marketingplan.entity.CustomerGainsAndLosses;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_region_collect_department_estimation")
@Table(
        name = "tpm_region_collect_department_estimation",
        indexes = {
                @Index(name = "tpm_region_collect_department_estimation_index0", columnList = "collect_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_region_collect_department_estimation", comment = "大区汇总-部门测算")
@ApiModel(value = "RegionCollectDepartmentEstimation", description = "大区汇总-部门测算")
public class RegionCollectDepartmentEstimation extends CustomerGainsAndLosses {

    @ApiModelProperty("大区汇总编码")
    @Column(name = "collect_code", columnDefinition = "varchar(32) comment '大区汇总编码'")
    private String collectCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

}
