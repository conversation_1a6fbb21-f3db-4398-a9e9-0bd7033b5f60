package com.biz.crm.tpm.business.activities.scheme.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeDetailRelation;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeFiles;
import com.biz.crm.tpm.business.activities.scheme.mapper.ActivitiesSchemeFilesMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 方案活动附件;(tpm_activities_scheme_files)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-20
 */
@Component
public class ActivitiesSchemeFilesRepository extends ServiceImpl<ActivitiesSchemeFilesMapper, ActivitiesSchemeFiles> {
  @Autowired
  private ActivitiesSchemeFilesMapper activitiesSchemeFilesMapper;

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<ActivitiesSchemeFiles>
   */
  public List<ActivitiesSchemeFiles> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(ActivitiesSchemeFiles::getId, ids)
            .eq(ActivitiesSchemeFiles::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据编号与租户编号获取对象
   *
   * @param code
   * @return
   */
  public List<ActivitiesSchemeFiles> findActivitiesCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(ActivitiesSchemeFiles::getActivitiesCode, code)
            .eq(ActivitiesSchemeFiles::getTenantCode, tenantCode).list();
  }

  /**
   * 根据活动编号删除关联数据
   *
   * @param activitiesCode
   * @return
   */
  public boolean removeByActivitiesCode(String activitiesCode) {
    if (StringUtils.isBlank(activitiesCode)) {
      return false;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaUpdate()
            .eq(ActivitiesSchemeFiles::getTenantCode, tenantCode)
            .eq(ActivitiesSchemeFiles::getActivitiesCode, activitiesCode).remove();
  }

  public ActivitiesSchemeFiles findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesSchemeFiles::getTenantCode,tenantCode)
        .in(ActivitiesSchemeFiles::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ActivitiesSchemeFiles::getTenantCode,tenantCode)
        .in(ActivitiesSchemeFiles::getId,ids)
        .remove();
  }
}
