package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectScheme;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectSchemeRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectSchemeService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectSchemeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Service
@Transactional
@Slf4j
public class RegionCollectSchemeServiceImpl implements RegionCollectSchemeService {


    @Resource
    private RegionCollectSchemeRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public List<RegionCollectSchemeVo> findByCollectCode(String collectCode) {
        List<RegionCollectScheme> list = repository.findByCollectCode(collectCode);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return (List<RegionCollectSchemeVo>) nebulaToolkitService.copyCollectionByWhiteList(list, RegionCollectScheme.class, RegionCollectSchemeVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public void deleteByCollectCodes(List<String> collectCodes) {
        repository.deleteByCollectCodes(collectCodes);
    }

    @Override
    public void saveList(List<RegionCollectSchemeVo> list, String collectCode) {
        repository.deleteByCollectCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<RegionCollectScheme> dataList = list.stream().map(x -> {
            RegionCollectScheme data = nebulaToolkitService.copyObjectByWhiteList(x, RegionCollectScheme.class, HashSet.class, ArrayList.class);
            data.setId(null);
            data.setCollectCode(collectCode);
            return data;
        }).collect(Collectors.toList());
        repository.saveBatch(dataList);
    }

    /**
     * 删除大区关联的方案
     *
     * @param schemeCodes
     */
    @Override
    public void deleteBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return;
        }
        repository.deleteBySchemeCodes(schemeCodes);
    }

    @Override
    public String findRegionCollectCodeBySchemeCode(String schemeCode) {
        RegionCollectScheme scheme = repository.findRegionCollectCodeBySchemeCode(schemeCode);
        if (ObjectUtils.isEmpty(scheme)) {
            return null;
        }
        return scheme.getCollectCode();
    }
}
