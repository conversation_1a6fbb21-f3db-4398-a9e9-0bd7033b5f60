package com.biz.crm.tpm.business.activities.marketingplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MarketingPlanBackGainsAndLossesVo", description = "营销方案-备份客户损益预测")
public class MarketingPlanBackGainsAndLossesVo extends CustomerGainsAndLossesVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("变更方案编码")
    private String changeSchemeCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

}
