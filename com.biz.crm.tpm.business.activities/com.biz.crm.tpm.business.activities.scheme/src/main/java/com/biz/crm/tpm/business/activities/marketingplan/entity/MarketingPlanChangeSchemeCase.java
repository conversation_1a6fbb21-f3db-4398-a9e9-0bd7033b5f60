package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/18 13:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_plan_change_scheme_case")
@Table(
        name = "tpm_marketing_plan_change_scheme_case",
        indexes = {
                @Index(name = "tpm_marketing_plan_change_scheme_case_index0", columnList = "original_scheme_detail_code,scheme_code", unique = true),
                @Index(name = "tpm_marketing_plan_change_scheme_case_idx1", columnList = "original_scheme_detail_code", unique = false),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_plan_change_scheme_case", comment = "营销方案规划变更日志表")
@ApiModel(value = "MarketingPlanChangeSchemeCase", description = "营销方案规划变更日志表")
public class MarketingPlanChangeSchemeCase extends UuidFlagOpEntity {

    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '变更方案编码'")
    private String schemeCode;

    @Column(name = "original_scheme_detail_code", columnDefinition = "varchar(32) comment '原始方案明细编码'")
    private String originalSchemeDetailCode;
}
