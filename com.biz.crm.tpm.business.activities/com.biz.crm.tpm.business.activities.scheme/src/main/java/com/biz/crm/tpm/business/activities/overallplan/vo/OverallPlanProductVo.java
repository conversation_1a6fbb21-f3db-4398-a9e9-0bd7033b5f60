package com.biz.crm.tpm.business.activities.overallplan.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "OverallPlanProductVo", description = "统筹方案产品范围")
public class OverallPlanProductVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("类型")
    private String type;
}
