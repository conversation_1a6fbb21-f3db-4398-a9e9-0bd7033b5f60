package com.biz.crm.tpm.business.activities.materialdemand.vo;

import com.biz.crm.business.common.sdk.vo.WorkflowFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:13
 */
@Data
@ApiModel(value = "MaterialDemandEntity", description = "物料需求提报")
public class MaterialDemandVo extends WorkflowFlagOpVo {

    @ApiModelProperty("编码")
    private String demandCode;

    @ApiModelProperty("名称")
    private String demandName;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("年月")
    private String years;

    private String tenantCode;

    private String cacheKey;

    @ApiModelProperty("汇总状态")
    private String collectStatus;

    @ApiModelProperty("明细列表")
    private List<MaterialDemandDetailVo> detailList;
}
