package com.biz.crm.tpm.business.activities.overallplan.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.dto.UuidFlagOpDto;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanDepartmentVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanProductVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanScopeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("OverallPlanCaseDto")
public class OverallPlanCaseDto extends UuidFlagOpDto {

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("关联总部编码")
    private String headSchemeCode;

    @ApiModelProperty("关联总部明细编码")
    private String headSchemeDetailCode;

    @ApiModelProperty("二级费用大类编码")
    private String secondCostCategory;

    @ApiModelProperty("二级费用大类名称")
    private String secondCostCategoryName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("是否可以承接")
    private String bearFlag;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("承接类型")
    private String bearType;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("预估费用")
    private BigDecimal applyAmount;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("兑付条件及说明")
    private String cashCondition;

    @ApiModelProperty("销售部门列表")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanDepartmentVo> bearDepartmentList;

    @ApiModelProperty("产品范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProductVo> productList;

    @ApiModelProperty("客户、终端")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScopeVo> scopeList;

    @ApiModelProperty("错误描述")
    private String errMsg;

    @ApiModelProperty("校验结果")
    private Boolean checkFlag;

    @ApiModelProperty("排序")
    private Integer sort;
}
