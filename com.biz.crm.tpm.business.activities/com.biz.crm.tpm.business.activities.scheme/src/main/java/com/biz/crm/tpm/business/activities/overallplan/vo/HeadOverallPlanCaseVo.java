package com.biz.crm.tpm.business.activities.overallplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
@ApiModel("HeadOverallPlanCaseVo")
public class HeadOverallPlanCaseVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("部门编码")
    private String departmentCode;

    @ApiModelProperty("组织编码")
    private Set<String> orgCodeSet;

    @ApiModelProperty("方案明细编码")
    private List<String> schemeDetailCodeList;

    @ApiModelProperty("方案类型")
    private String schemeType;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("组织编码")
    private String orgCode;
}
