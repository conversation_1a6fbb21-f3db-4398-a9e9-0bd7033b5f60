package com.biz.crm.tpm.business.activities.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.contract.dto.ContractCostPageDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDto;
import com.biz.crm.tpm.business.activities.contract.entity.ExternalContract;
import com.biz.crm.tpm.business.activities.contract.vo.ContractCostDetailVo;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 11:38
 */
public interface ExternalContractMapper extends BaseMapper<ExternalContract> {

    Page<ExternalContractVo> findList(Page<ExternalContractDto> page, @Param("dto") ExternalContractDto dto);

    Page<ContractCostPageDto> findContractCostList(Page<ContractCostPageDto> page, @Param("dto") ContractCostPageDto dto, @Param("tenantCode") String tenantCode);

    Page<ContractCostPageDto> findExternalContractCostList(Page<ContractCostPageDto> page, @Param("dto") ContractCostPageDto dto, @Param("tenantCode") String tenantCode);

    List<ContractCostDetailVo> findUnExecutedAndNotExternal(@Param("years") String years, @Param("tenantCode") String tenantCode);

    /**
     * 根据条件分页查询
     *
     * @param page
     * @param vo
     * @return
     */
    Page<ExternalContractVo> findByConditions(@Param("page") Page<ExternalContractVo> page,
                                              @Param("dto") ContractCostPageDto vo);

    List<ExternalContractVo> findContractNearDate(@Param("day") Integer day);
}
