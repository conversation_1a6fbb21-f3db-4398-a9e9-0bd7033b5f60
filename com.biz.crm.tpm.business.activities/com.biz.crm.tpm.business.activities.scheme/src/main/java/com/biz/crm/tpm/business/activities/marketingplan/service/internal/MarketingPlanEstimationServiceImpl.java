package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanEstimation;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanEstimationRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanEstimationService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanEstimationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 00:14
 */
@Service
@Transactional
public class MarketingPlanEstimationServiceImpl implements MarketingPlanEstimationService {

    @Resource
    private MarketingPlanEstimationRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;


    /**
     * 保存数据
     *
     * @param list
     * @param schemeCode
     */
    @Override
    public void saveBatchList(List<MarketingPlanEstimationVo> list, String schemeCode) {
        repository.deleteBySchemeCodes(Lists.newArrayList(schemeCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<MarketingPlanEstimation> dataList = (List<MarketingPlanEstimation>) nebulaToolkitService.copyCollectionByWhiteList(list, MarketingPlanEstimationVo.class,
                MarketingPlanEstimation.class, HashSet.class, ArrayList.class);
        dataList.forEach(x -> x.setSchemeCode(schemeCode));
        repository.saveBatch(dataList);
    }

    @Override
    public List<MarketingPlanEstimationVo> findListBySchemeCode(List<String> schemCodeList) {
        List<MarketingPlanEstimation> dataList = repository.findListBySchemeCodes(schemCodeList);
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        return (List<MarketingPlanEstimationVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, MarketingPlanEstimation.class,
                MarketingPlanEstimationVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public void deleteBySchemeCodes(List<String> schemeCodes) {
        repository.deleteBySchemeCodes(schemeCodes);
    }

    @Override
    public List<MarketingPlanEstimationVo> findOriginalListBySchemeCode(List<String> schemCodeList) {
        return repository.findOriginalListBySchemeCode(schemCodeList);
    }
}
