package com.biz.crm.tpm.business.activities.scheme.service.internal;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeDetailRelation;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeRelation;
import com.biz.crm.tpm.business.activities.scheme.repository.ActivitiesSchemeDetailRelationRepository;
import com.biz.crm.tpm.business.activities.scheme.service.ActivitiesSchemeDetailRelationService;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeDetailRelationVo;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeRelationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 方案活动关联信息(activitiesSchemeDetailRelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-17 10:52:33
 */
@Service("activitiesSchemeDetailRelationService")
public class ActivitiesSchemeDetailRelationServiceImpl implements ActivitiesSchemeDetailRelationService {

  @Autowired
  private ActivitiesSchemeDetailRelationRepository activitiesSchemeDetailRelationRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页查询数据
   *
   * @param pageable                       分页对象
   * @param activitiesSchemeDetailRelation 实体对象
   * @return
   */
  @Override
  public Page<ActivitiesSchemeDetailRelation> findByConditions(Pageable pageable, ActivitiesSchemeDetailRelation activitiesSchemeDetailRelation) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(activitiesSchemeDetailRelation)) {
      activitiesSchemeDetailRelation = new ActivitiesSchemeDetailRelation();
    }
    return this.activitiesSchemeDetailRelationRepository.findByConditions(pageable, activitiesSchemeDetailRelation);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public ActivitiesSchemeDetailRelation findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    return this.activitiesSchemeDetailRelationRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
  }


  /**
   * 修改新据
   *
   * @param activitiesSchemeDetailRelation 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public ActivitiesSchemeDetailRelation update(ActivitiesSchemeDetailRelation activitiesSchemeDetailRelation) {
    this.updateValidate(activitiesSchemeDetailRelation);
    activitiesSchemeDetailRelation.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesSchemeDetailRelationRepository.saveOrUpdate(activitiesSchemeDetailRelation);
    return activitiesSchemeDetailRelation;
  }

  @Transactional
  @Override

  public void removeByIds(Collection<String> ids) {
    this.activitiesSchemeDetailRelationRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }




  /**
   * 活动编码查询活动关联信息
   *
   * @param activityCode 主键结合
   * @return
   */
  @Override
  public List<ActivitiesSchemeDetailRelationVo> findByActivityCode(String activityCode) {
    if (StringUtils.isBlank(activityCode)) {
      return Lists.newArrayList();
    }
    List<ActivitiesSchemeDetailRelation> relations = activitiesSchemeDetailRelationRepository.findByActivityCodeAndTenantCode(activityCode, TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(relations)) {
      return Lists.newArrayList();
    }

    Collection<ActivitiesSchemeDetailRelationVo> relationVos = nebulaToolkitService.copyCollectionByWhiteList(relations, ActivitiesSchemeDetailRelation.class, ActivitiesSchemeDetailRelationVo.class, HashSet.class, ArrayList.class);
    return Lists.newArrayList(relationVos);
  }

  @Override
  @Transactional
  public void saveBatch(List<ActivitiesSchemeDetailRelationVo> activitiesSchemeRelationVos) {
    this.createValidate(activitiesSchemeRelationVos);
    Collection<ActivitiesSchemeDetailRelation> relations = nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemeRelationVos, ActivitiesSchemeDetailRelationVo.class, ActivitiesSchemeDetailRelation.class, HashSet.class, ArrayList.class);
    relations.forEach(relation->relation.setTenantCode(TenantUtils.getTenantCode()));
    activitiesSchemeDetailRelationRepository.saveBatch(relations);
  }

  @Override
  public List<ActivitiesSchemeDetailRelationVo> findByActivityCodes(Set<String> activityCodes) {
    String tenantCode = TenantUtils.getTenantCode();
    List<ActivitiesSchemeDetailRelation> activitiesSchemeRelations = this.activitiesSchemeDetailRelationRepository.findByActivityCodesAndTenantCode(activityCodes, tenantCode);
    Collection<ActivitiesSchemeDetailRelationVo> activitiesSchemeRelationVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemeRelations, ActivitiesSchemeDetailRelation.class, ActivitiesSchemeDetailRelationVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesSchemeRelationVos);
  }

  /**
   * 创建验证
   *
   * @param activitiesSchemeRelationVos
   */
  private void createValidate(List<ActivitiesSchemeDetailRelationVo> activitiesSchemeRelationVos) {
    Validate.isTrue(!CollectionUtils.isEmpty(activitiesSchemeRelationVos), "新增时，对象信息不能为空！");
    for (ActivitiesSchemeDetailRelationVo activitiesSchemeDetailRelation : activitiesSchemeRelationVos) {
      activitiesSchemeDetailRelation.setId(null);
      Validate.notBlank(activitiesSchemeDetailRelation.getTenantCode(), "新增数据时，租户编号不能为空！");
      Validate.notBlank(activitiesSchemeDetailRelation.getActivityCode(), "新增数据时，关联的活动编码不能为空！");
      Validate.notBlank(activitiesSchemeDetailRelation.getCostBudgetCode(), "新增数据时，费用预算编码不能为空！");
      Validate.notBlank(activitiesSchemeDetailRelation.getCostTypeDetailCode(), "新增数据时，活动细类编码不能为空！");
      Validate.notBlank(activitiesSchemeDetailRelation.getCostTypeDetailName(), "新增数据时，活动细类名称不能为空！");
    }
  }

  /**
   * 修改验证
   *
   * @param activitiesSchemeDetailRelation
   */
  private void updateValidate(ActivitiesSchemeDetailRelation activitiesSchemeDetailRelation) {
    Validate.notNull(activitiesSchemeDetailRelation, "修改时，对象信息不能为空！");
    Validate.notBlank(activitiesSchemeDetailRelation.getId(), "新增数据时，不能为空！");
    Validate.notBlank(activitiesSchemeDetailRelation.getTenantCode(), "新增数据时，租户编号不能为空！");
    Validate.notBlank(activitiesSchemeDetailRelation.getActivityCode(), "新增数据时，关联的活动编码不能为空！");
    Validate.notBlank(activitiesSchemeDetailRelation.getCostBudgetCode(), "新增数据时，费用预算编码不能为空！");
    Validate.notBlank(activitiesSchemeDetailRelation.getCostTypeDetailCode(), "新增数据时，活动细类编码不能为空！");
    Validate.notBlank(activitiesSchemeDetailRelation.getCostTypeDetailName(), "新增数据时，活动细类名称不能为空！");
  }
}

