package com.biz.crm.tpm.business.activities.marketingplan.service.strategy;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/24 19:34
 */
public abstract class MarketingPlanCustomerGainsBuilder<SchemeCodes, Years> {

    protected SchemeCodes schemeCode;

    protected Years years;

    public MarketingPlanCustomerGainsBuilder<SchemeCodes, Years> init(SchemeCodes schemeCode, Years years) {
        this.schemeCode = schemeCode;
        this.years = years;
        return this;
    }

    /**
     * 加载初始值
     *
     * @return
     */
    public abstract MarketingPlanCustomerGainsBuilder loadInitData();

    /**
     * 利润率
     *
     * @return
     */
    public abstract MarketingPlanCustomerGainsBuilder calProfitRatio();

    /**
     * 毛利率
     *
     * @return
     */
    public abstract MarketingPlanCustomerGainsBuilder calGrossProfitRatio();

    /**
     * 物流费率
     *
     * @return
     */
    public abstract MarketingPlanCustomerGainsBuilder calLogisticsRatio();

    /**
     * 营销费率
     *
     * @return
     */
    public abstract MarketingPlanCustomerGainsBuilder calMarketingRatio();

    /**
     * 公摊费率
     *
     * @return
     */
    public abstract MarketingPlanCustomerGainsBuilder calPublicShareRatio();


    /**
     * 二级费用大类
     *
     * @return
     */
    public abstract MarketingPlanCustomerGainsBuilder calSecondCategory();
}
