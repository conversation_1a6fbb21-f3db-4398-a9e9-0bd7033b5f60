package com.biz.crm.tpm.business.activities.scheme.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.scheme.entity.SchemeCostBudget;
import com.biz.crm.tpm.business.activities.scheme.mapper.SchemeCostBudgetMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 方案预算成本;(tpm_scheme_cost_budget)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Component
public class SchemeCostBudgetRepository extends ServiceImpl<SchemeCostBudgetMapper,SchemeCostBudget>{
  @Autowired
  private SchemeCostBudgetMapper schemeCostBudgetMapper;
  
  /**
   * 根据方案编号与租户编号获取对象
   *
   * @param schemeCode
   * @return
   */
  public List<SchemeCostBudget> findBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(SchemeCostBudget::getSchemeCode, schemeCode)
            .eq(SchemeCostBudget::getTenantCode, tenantCode).list();
  }

  /**
   * 根据方案编号删除附件数据
   *
   * @param schemeCode
   */
  public void deleteBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    this.lambdaUpdate().eq(SchemeCostBudget::getSchemeCode, schemeCode).eq(SchemeCostBudget::getTenantCode, tenantCode).remove();
  }

}