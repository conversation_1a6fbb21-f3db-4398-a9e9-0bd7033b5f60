<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanChangeLogMapper">


    <select id="findChangeList" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanChangeLogVo">
        select a.*,b.scheme_name,b.process_status from tpm_marketing_plan_change_log a
        left join tpm_marketing_plan b on a.scheme_code = b.scheme_code
        <where>
            1=1
            and b.del_flag = '${@<EMAIL>()}'
            and a.original_scheme_code = #{schemeCode}
        </where>
    </select>

</mapper>

