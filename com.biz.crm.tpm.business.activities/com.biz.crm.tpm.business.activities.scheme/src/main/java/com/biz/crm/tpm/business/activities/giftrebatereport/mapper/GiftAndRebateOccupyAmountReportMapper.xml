<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.giftrebatereport.mapper.GiftAndRebateOccupyAmountReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.biz.crm.tpm.business.activities.giftrebatereport.entity.GiftAndRebateOccupyAmountReport">
        <id column="id" property="id" />
        <result column="scheme_code" property="schemeCode" />
        <result column="scheme_name" property="schemeName" />
        <result column="scheme_type" property="schemeType" />
        <result column="scheme_detail_code" property="schemeDetailCode" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="customer_code" property="customerCode" />
        <result column="customer_name" property="customerName" />
        <result column="belong_department_code" property="belongDepartmentCode" />
        <result column="belong_department_name" property="belongDepartmentName" />
        <result column="bear_department_code" property="bearDepartmentCode" />
        <result column="bear_department_name" property="bearDepartmentName" />
        <result column="cost_center_code" property="costCenterCode" />
        <result column="cost_center_name" property="costCenterName" />
        <result column="detail_code" property="detailCode" />
        <result column="detail_name" property="detailName" />
        <result column="item_code" property="itemCode" />
        <result column="item_name" property="itemName" />
        <result column="condition_num" property="conditionNum" />
        <result column="give_num" property="giveNum" />
        <result column="bp_level_name" property="bpLevelName" />
        <result column="bp_level_code" property="bpLevelCode" />
        <result column="plan_amount" property="planAmount" />
        <result column="occupy_amount" property="occupyAmount" />
        <result column="fl_item_or_product_name" property="flItemOrProductName" />
        <result column="fl_item_or_product_code" property="flItemOrProductCode" />
        <result column="rebate_policy" property="rebatePolicy" />
        <result column="condition_formula" property="conditionFormula" />
        <result column="rebate_standard" property="rebateStandard" />
        <result column="tenant_code" property="tenantCode" />
        <result column="del_flag" property="delFlag" />
        <result column="enable_status" property="enableStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scheme_code, scheme_name, scheme_type, scheme_detail_code, start_date, end_date,
        customer_code, customer_name, belong_department_code, belong_department_name,
        bear_department_code, bear_department_name, cost_center_code, cost_center_name,
        detail_code, detail_name, item_code, item_name, condition_num, give_num,
        bp_level_name, bp_level_code, plan_amount, occupy_amount, fl_item_or_product_name,
        fl_item_or_product_code, rebate_policy, condition_formula, rebate_standard,
        tenant_code, del_flag
    </sql>

    <!-- 查询条件 -->
    <sql id="whereConditions">
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                AND tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.delFlag != null and dto.delFlag != ''">
                AND del_flag = #{dto.delFlag}
            </if>
            <if test="dto.schemeCode != null and dto.schemeCode != ''">
                AND scheme_code = #{dto.schemeCode}
            </if>
            <if test="dto.schemeName != null and dto.schemeName != ''">
                AND scheme_name LIKE CONCAT('%', #{dto.schemeName}, '%')
            </if>
            <if test="dto.schemeType != null and dto.schemeType != ''">
                AND scheme_type = #{dto.schemeType}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                AND customer_code = #{dto.customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                AND customer_name LIKE CONCAT('%', #{dto.customerName}, '%')
            </if>
            <if test="dto.belongDepartmentCode != null and dto.belongDepartmentCode != ''">
                AND belong_department_code = #{dto.belongDepartmentCode}
            </if>
            <if test="dto.belongDepartmentName != null and dto.belongDepartmentName != ''">
                AND belong_department_name LIKE CONCAT('%', #{dto.belongDepartmentName}, '%')
            </if>
            <if test="dto.bearDepartmentCode != null and dto.bearDepartmentCode != ''">
                AND bear_department_code = #{dto.bearDepartmentCode}
            </if>
            <if test="dto.bearDepartmentName != null and dto.bearDepartmentName != ''">
                AND bear_department_name LIKE CONCAT('%', #{dto.bearDepartmentName}, '%')
            </if>
            <if test="dto.costCenterCode != null and dto.costCenterCode != ''">
                AND cost_center_code = #{dto.costCenterCode}
            </if>
            <if test="dto.costCenterName != null and dto.costCenterName != ''">
                AND cost_center_name LIKE CONCAT('%', #{dto.costCenterName}, '%')
            </if>
            <if test="dto.detailCode != null and dto.detailCode != ''">
                AND detail_code = #{dto.detailCode}
            </if>
            <if test="dto.detailName != null and dto.detailName != ''">
                AND detail_name LIKE CONCAT('%', #{dto.detailName}, '%')
            </if>
            <if test="dto.itemCode != null and dto.itemCode != ''">
                AND item_code = #{dto.itemCode}
            </if>
            <if test="dto.itemName != null and dto.itemName != ''">
                AND item_name LIKE CONCAT('%', #{dto.itemName}, '%')
            </if>
            <if test="dto.bpLevelCode != null and dto.bpLevelCode != ''">
                AND bp_level_code = #{dto.bpLevelCode}
            </if>
            <if test="dto.bpLevelName != null and dto.bpLevelName != ''">
                AND bp_level_name LIKE CONCAT('%', #{dto.bpLevelName}, '%')
            </if>
            <if test="dto.flItemOrProductCode != null and dto.flItemOrProductCode != ''">
                AND fl_item_or_product_code = #{dto.flItemOrProductCode}
            </if>
            <if test="dto.flItemOrProductName != null and dto.flItemOrProductName != ''">
                AND fl_item_or_product_name LIKE CONCAT('%', #{dto.flItemOrProductName}, '%')
            </if>
            <if test="dto.rebatePolicy != null and dto.rebatePolicy != ''">
                AND rebate_policy LIKE CONCAT('%', #{dto.rebatePolicy}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询搭赠及返利实时金额占用报表 -->
    <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.giftrebatereport.vo.GiftAndRebateOccupyAmountReportVo">
        SELECT
         *
        FROM tpm_gift_rebate_occupy_amount_report
        <where>
            <if test="dto.schemeCode != null and dto.schemeCode != ''">
                AND scheme_code = #{dto.schemeCode}
            </if>
            <if test="dto.schemeDetailCode != null and dto.schemeDetailCode != ''">
                AND scheme_detail_code = #{dto.schemeDetailCode}
            </if>
            <if test="dto.schemeName != null and dto.schemeName != ''">
                AND scheme_name LIKE CONCAT('%', #{dto.schemeName}, '%')
            </if>
            <if test="dto.schemeType != null and dto.schemeType != ''">
                AND scheme_type = #{dto.schemeType}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                AND customer_code = #{dto.customerCode}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                AND customer_name LIKE CONCAT('%', #{dto.customerName}, '%')
            </if>
            <if test="dto.belongDepartmentCode != null and dto.belongDepartmentCode != ''">
                AND belong_department_code = #{dto.belongDepartmentCode}
            </if>
            <if test="dto.belongDepartmentName != null and dto.belongDepartmentName != ''">
                AND belong_department_name LIKE CONCAT('%', #{dto.belongDepartmentName}, '%')
            </if>
            <if test="dto.bearDepartmentCode != null and dto.bearDepartmentCode != ''">
                AND bear_department_code = #{dto.bearDepartmentCode}
            </if>
            <if test="dto.bearDepartmentName != null and dto.bearDepartmentName != ''">
                AND bear_department_name LIKE CONCAT('%', #{dto.bearDepartmentName}, '%')
            </if>
            <if test="dto.costCenterCode != null and dto.costCenterCode != ''">
                AND cost_center_code = #{dto.costCenterCode}
            </if>
            <if test="dto.costCenterName != null and dto.costCenterName != ''">
                AND cost_center_name LIKE CONCAT('%', #{dto.costCenterName}, '%')
            </if>
            <if test="dto.detailCode != null and dto.detailCode != ''">
                AND detail_code = #{dto.detailCode}
            </if>
            <if test="dto.detailName != null and dto.detailName != ''">
                AND detail_name LIKE CONCAT('%', #{dto.detailName}, '%')
            </if>
            <if test="dto.itemCode != null and dto.itemCode != ''">
                AND item_code = #{dto.itemCode}
            </if>
            <if test="dto.itemName != null and dto.itemName != ''">
                AND item_name LIKE CONCAT('%', #{dto.itemName}, '%')
            </if>
            <if test="dto.bpLevelCode != null and dto.bpLevelCode != ''">
                AND bp_level_code = #{dto.bpLevelCode}
            </if>
            <if test="dto.bpLevelName != null and dto.bpLevelName != ''">
                AND bp_level_name LIKE CONCAT('%', #{dto.bpLevelName}, '%')
            </if>
            <if test="dto.flItemOrProductCode != null and dto.flItemOrProductCode != ''">
                AND fl_item_or_product_code = #{dto.flItemOrProductCode}
            </if>
            <if test="dto.flItemOrProductName != null and dto.flItemOrProductName != ''">
                AND fl_item_or_product_name LIKE CONCAT('%', #{dto.flItemOrProductName}, '%')
            </if>
            <if test="dto.rebatePolicy != null and dto.rebatePolicy != ''">
                AND rebate_policy LIKE CONCAT('%', #{dto.rebatePolicy}, '%')
            </if>
        </where>
        ORDER BY create_time DESC,ID DESC
    </select>

    <!-- 根据条件查询报表数据 -->
    <select id="findListByConditions" resultType="com.biz.crm.tpm.business.activities.giftrebatereport.vo.GiftAndRebateOccupyAmountReportVo">
        SELECT
        <include refid="Base_Column_List" />
        FROM tpm_gift_rebate_occupy_amount_report
        <include refid="whereConditions" />
        ORDER BY create_time DESC
    </select>

    <!-- 统计报表数据总数 -->
    <select id="countByConditions" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM tpm_gift_rebate_occupy_amount_report
        <include refid="whereConditions" />
    </select>

    <!-- 查询本月审批通过的且当天与活动结束时间大于1天的营销方案明细 -->
    <select id="queryApprovedMarketingPlanCases" resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase">
        SELECT
            tpc.id,
            tpc.case_type,
            tpc.scheme_code,
            tpc.scheme_name,
            tpc.scheme_type,
            tpc.scheme_detail_code,
            tpc.start_date,
            tpc.end_date,
            tpc.customer_code,
            tpc.customer_name,
            tpc.belong_department_code,
            tpc.belong_department_name,
            tpc.bear_department_code,
            tpc.bear_department_name,
            tpc.cost_center_code,
            tpc.cost_center_name,
            tpc.detail_code,
            tpc.detail_name,
            tpc.item_code,
            tpc.item_name,
            tpc.cash_amount,
            tpc.tenant_code,
            tpc.del_flag,
            tpc.enable_status,
            tpc.create_time,
            tpc.update_time,
            tpc.create_user,
            tpc.update_user
        FROM tpm_marketing_plan_case tpc
        LEFT JOIN tpm_marketing_plan tp ON tpc.scheme_code = tp.scheme_code
        WHERE tpc.tenant_code = #{tenantCode}
          AND tpc.del_flag = #{delFlag}
          AND tp.del_flag = #{delFlag}
          AND tp.status = '3'
          AND tpc.create_time >= #{monthStart}
          AND tpc.create_time &lt;= #{monthEnd}
          AND DATEDIFF(tpc.end_date, CURDATE()) > 1
        ORDER BY tpc.create_time DESC
    </select>

</mapper>
