package com.biz.crm.tpm.business.activities.materialdemand.helper;

import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.tpm.business.activities.materialdemand.constant.MaterialDemandConstant;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandCollectDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@Component
@Slf4j
public class MaterialDemandCollectPageCacheHelper extends BusinessPageCacheHelper<MaterialDemandCollectDetailVo, MaterialDemandCollectDetailVo> {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private MaterialDemandCollectDetailService materialDemandCollectDetailService;

    @Override
    public String getCacheKeyPrefix() {
        return MaterialDemandConstant.MATERIAL_DEMAND_COLLECT_CACHE_PAGE;
    }

    @Override
    public Class<MaterialDemandCollectDetailVo> getDtoClass() {
        return MaterialDemandCollectDetailVo.class;
    }

    @Override
    public Class<MaterialDemandCollectDetailVo> getVoClass() {
        return MaterialDemandCollectDetailVo.class;
    }

    @Override
    public List<MaterialDemandCollectDetailVo> findDtoListFromRepository(MaterialDemandCollectDetailVo vo, String cacheKey) {
        if (ObjectUtils.isEmpty(vo.getId())) {
            return Lists.newArrayList();
        }
        return materialDemandCollectDetailService.findListById(vo.getId());
    }

    @Override
    public List<MaterialDemandCollectDetailVo> newItem(String cacheKey, List<MaterialDemandCollectDetailVo> itemList) {
        MaterialDemandCollectDetailVo vo = new MaterialDemandCollectDetailVo();
        vo.setId(UuidCrmUtil.general());
        return Lists.newArrayList(vo);
    }

    @Override
    public List<MaterialDemandCollectDetailVo> copyItem(String cacheKey, List<MaterialDemandCollectDetailVo> itemList) {
        List<MaterialDemandCollectDetailVo> newList = (List<MaterialDemandCollectDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(itemList, MaterialDemandCollectDetailVo.class, MaterialDemandCollectDetailVo.class,
                HashSet.class, ArrayList.class);
        for (MaterialDemandCollectDetailVo detailVo : newList) {
            detailVo.setId(UuidCrmUtil.general());
        }
        return newList;
    }

    @Override
    public Object getDtoKey(MaterialDemandCollectDetailVo vo) {
        return vo.getId();
    }

    @Override
    public String getCheckedStatus(MaterialDemandCollectDetailVo vo) {
        return vo.getChecked();
    }


}
