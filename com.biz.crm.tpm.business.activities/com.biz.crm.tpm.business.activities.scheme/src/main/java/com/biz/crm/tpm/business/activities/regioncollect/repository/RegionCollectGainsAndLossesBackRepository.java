package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLosses;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLossesBack;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectGainsAndLossesBackMapper;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectGainsAndLossesMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class RegionCollectGainsAndLossesBackRepository extends ServiceImpl<RegionCollectGainsAndLossesBackMapper, RegionCollectGainsAndLossesBack> {

    public List<RegionCollectGainsAndLossesBack> findByCollectCode(String collectCode) {
        return this.lambdaQuery()
                .eq(RegionCollectGainsAndLossesBack::getCollectCode, collectCode).list();
    }

    public void deleteByCollectCodes(List<String> collectCodes) {
        this.lambdaUpdate()
                .in(RegionCollectGainsAndLossesBack::getCollectCode, collectCodes).remove();
    }
}
