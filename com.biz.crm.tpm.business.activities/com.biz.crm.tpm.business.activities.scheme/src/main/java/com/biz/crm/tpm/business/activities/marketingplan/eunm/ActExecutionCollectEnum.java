package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.eunm.ActExecutionCollectEnum
 * @description: 活动执行采集类型
 * @author: xiaopeng.zhang
 * @create: 2024-08-01 10:45
 */
@Getter
@AllArgsConstructor
public enum ActExecutionCollectEnum {

    TERMINAL_COLLECT("terminal_collect", "终端执行采集"),
    CUSTOMER_COLLECT("customer_collect", "客户执行采集"),
    AUDIT_COLLECT("audit_collect", "稽查执行采集");

    private String code;
    private String desc;
}
