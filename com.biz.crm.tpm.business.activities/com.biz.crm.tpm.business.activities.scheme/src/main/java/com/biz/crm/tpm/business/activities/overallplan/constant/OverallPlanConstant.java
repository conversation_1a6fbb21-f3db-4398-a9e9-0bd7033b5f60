package com.biz.crm.tpm.business.activities.overallplan.constant;

public class OverallPlanConstant {

    //总部指引
    public static final String HEAD_OVERALL_PLAN_TEMPLATE = "BDMB0011";
    //大区
    public static final String REGION_OVERALL_PLAN_TEMPLATE = "BDMB0012";
    //合同
    public static final String CONTRACT_COST_TEMPLATE = "BDMB0018";

    public static final String OVERALL_PLAN_LOCK = "tpm:overall_plan:lock:";

    public static final Integer OVERALL_PLAN_LOCK_TIME_20 = 20;

    public static final String OVERALL_PLAN_STAGING_REDIS_KEY = "tpm:overall_plan:staging:id:%s";

    public static final String HEAD_SCHEME_CODE_RULE = "ZBFA";

    public static final String HEAD_SCHEME_DETAIL_CODE_RULE = "ZBMX";

    public static final String REGION_SCHEME_CODE_RULE = "DQFA";

    public static final String REGION_SCHEME_DETAIL_CODE_RULE = "DQMX";

    public static final String OVERALL_PLAN_CACHE_PAGE = "tpm:overall:plan:cache:page:";

    public static final String OVER_PLAN_PAGE_CACHE_LOCK = "tpm:overall:plan:page:cache:lockKey:id:";

    public static final String OVER_PLAN_BUDGET = "tpm:overall:plan:budget:item_cache:";

    /**
     * 营销方案规划-选择年月时是否开启校验
     */
    public static final String IS_REGIONAL_PROGRAMMES = "is_regional_programmes";
    public static final String YES = "yes";

    public static String getRedisKey(String id) {
        String redisKey = String.format(OVERALL_PLAN_STAGING_REDIS_KEY, id);
        return redisKey;
    }

    public static String getRedisLockKey(String id) {
        String redisLockKey = OVERALL_PLAN_LOCK + id;
        return redisLockKey;
    }

    public static String getRedisPageCacheLockKey(String cacheKey) {
        String pageCacheLockKey = OVER_PLAN_PAGE_CACHE_LOCK + cacheKey;
        return pageCacheLockKey;
    }

    public static String getRedisOverPlanBudgetKey(String cacheKey) {
        return OVER_PLAN_BUDGET + cacheKey;
    }
}
