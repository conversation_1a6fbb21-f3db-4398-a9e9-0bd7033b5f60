package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanBackGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanBackGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanGainsAndLossesVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/4 10:17
 */
public interface MarketingPlanBackGainsAndLossesService {


    List<MarketingPlanBackGainsAndLosses> findListBySchemeCode(String schemeCode);

    /**
     * 保存明细数据
     *
     * @param list
     * @param schemeCode
     */
    void saveBatchList(List<MarketingPlanBackGainsAndLossesVo> list, String schemeCode, String changeSchemeCode);


    void saveOriginalGainsAndLosses(String originalSchemeCode,String changeSchemeCode);
}
