package com.biz.crm.tpm.business.activities.scheme.notifier;

import com.biz.crm.tpm.business.activities.scheme.service.TpmSchemeVoService;
import com.biz.crm.tpm.business.budget.sdk.event.CostBudgetEventListener;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>方案针对已经绑定的费用预算的变动监听
 *
 * <AUTHOR>
 * @date 2022/6/9
 */
@Component
public class SchemeForCostBudgetEventListener implements CostBudgetEventListener {
  @Autowired
  private TpmSchemeVoService schemeVoService;

  @Override
  public void onDeleted(CostBudgetVo costBudgetVo) {
    String costBudgetCode = costBudgetVo.getCode();
    if(StringUtils.isBlank(costBudgetCode)){
      return;
    }
    boolean exists = this.schemeVoService.existsByCostBudget(costBudgetCode);
    Validate.isTrue(!exists,"该费用预算已经绑定方案，无法删除");
  }

}
