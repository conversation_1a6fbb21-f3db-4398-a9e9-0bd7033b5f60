package com.biz.crm.tpm.business.activities.regioncollect.service.strategy;

import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialCostVoService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanMaterialTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingTransportEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.enums.SecondCategoryEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 三级部门测算
 * <AUTHOR>
 * @Date 2024/6/25 17:12
 */
@Slf4j
public class DepartmentEstimationAbstractBuilder extends DepartmentEstimationBuilder<List<String>, String> {


    private static final String column = ":";

    private MarketingPlanCaseRepository caseRepository;
    private MarketingSalesPlanService marketingSalesPlanService;
    private CostBudgetIncomeService costBudgetIncomeService;
    private CostBudgetVoService costBudgetVoService;
    private CustomerVoService customerVoService;
    private MdmCostCenterVoService costCenterVoService;
    private MaterialVoService materialVoService;
    private MaterialCostVoService materialCostVoService;
    private CostTypeCategoryVoService costTypeCategoryVoService;

    private MarketingPlanProductRepository productRepository;

    private OrgVoService orgVoService;

    private PublicShareRatioService publicShareRatioService;

    private MarketingPlanRepository marketingPlanRepository;

    private ProductPhaseVoService productPhaseVoService;

    private String year = null;

    private Map<String, BigDecimal> materialMap;

    private Map<String, MdmCostCenterVo> costCenterMap;

    private Map<String, List<MarketingSalesPlanVo>> salesPlanMap;

    private Map<String, MarketingSalesPlanVo> cusProductSalesPlanMap;

    private Map<String, List<String>> costTypeCategoryMap;

    private Map<String, OrgVo> orgMap = Maps.newHashMap();

    private List<MarketingPlanCase> caseList = Lists.newArrayList();

    private List<? extends CustomerGainsAndLossesVo> dataList = Lists.newArrayList();

    private Set<String> orgCodeSet = Sets.newHashSet();

    public static DepartmentEstimationAbstractBuilder builder(ApplicationContext context, List<? extends CustomerGainsAndLossesVo> dataList) {
        DepartmentEstimationAbstractBuilder builder = new DepartmentEstimationAbstractBuilder();
        builder.caseRepository = context.getBean(MarketingPlanCaseRepository.class);
        builder.marketingSalesPlanService = context.getBean(MarketingSalesPlanService.class);
        builder.costBudgetIncomeService = context.getBean(CostBudgetIncomeService.class);
        builder.costBudgetVoService = context.getBean(CostBudgetVoService.class);
        builder.customerVoService = context.getBean(CustomerVoService.class);
        builder.costCenterVoService = context.getBean(MdmCostCenterVoService.class);
        builder.materialVoService = context.getBean(MaterialVoService.class);
        builder.materialCostVoService = context.getBean(MaterialCostVoService.class);
        builder.costTypeCategoryVoService = context.getBean(CostTypeCategoryVoService.class);
        builder.productRepository = context.getBean(MarketingPlanProductRepository.class);
        builder.orgVoService = context.getBean(OrgVoService.class);
        builder.dataList = dataList;
        builder.publicShareRatioService = context.getBean(PublicShareRatioService.class);
        builder.marketingPlanRepository = context.getBean(MarketingPlanRepository.class);
        builder.productPhaseVoService = context.getBean(ProductPhaseVoService.class);
        return builder;
    }


    @Override
    public DepartmentEstimationBuilder loadInitData() {
        this.year = years.split("-")[0];
        List<MarketingPlanCase> caseList = caseRepository.findListBySchemeCodes(schemeCode);
        //过滤有空值的情况
        this.caseList = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()) && ObjectUtils.isNotEmpty(x.getYears())).collect(Collectors.toList());
        List<MarketingPlanProduct> planProductList = productRepository.findListBySchemeCodes(schemeCode);
        if (CollectionUtils.isNotEmpty(planProductList)) {
            Map<String, List<MarketingPlanProduct>> planProductMap = planProductList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
            for (MarketingPlanCase planCase : caseList) {
                planCase.setProductAndItemList(planProductMap.get(planCase.getSchemeDetailCode()));
            }
        }
        //按照部门统计一下数据
        Map<String, BigDecimal> belongDepartmentNoTaxApplyAmountMap = this.caseList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .filter(x -> ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .collect(Collectors.groupingBy(MarketingPlanCase::getBelongDepartmentCode,
                        Collectors.mapping(x -> Optional.ofNullable(x.getNoTaxApplyAmount()).orElse(BigDecimal.ZERO),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        List<MarketingPlan> plans = marketingPlanRepository.queryBySchemeCodes(Sets.newHashSet(schemeCode));

        this.orgCodeSet = plans.stream().map(x -> x.getDepartmentCode()).collect(Collectors.toSet());


        List<String> orgCodes = caseList.stream().map(MarketingPlanCase::getBelongDepartmentCode).distinct().collect(Collectors.toList());

        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
        orgMap = orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, v -> v));

        //年月
        Set<String> yearsSet = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getYears()))
                .map(MarketingPlanCase::getYears).collect(Collectors.toSet());
        //成本中心编码
        Set<String> costCenterCodeSet = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCostCenterCode()))
                .map(MarketingPlanCase::getCostCenterCode).collect(Collectors.toSet());
        //公司代码
        Set<String> companyCodeSet = caseList.stream().map(MarketingPlanCase::getCompanyCode).collect(Collectors.toSet());


        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(Lists.newArrayList(costCenterCodeSet));
        if (CollectionUtils.isNotEmpty(costCenterVoList)) {
            this.costCenterMap = costCenterVoList.stream().collect(Collectors.toMap(MdmCostCenterVo::getCostCenterCode, Function.identity()));
        }
        //1.获取预算收入-三级部门+年月
        List<CostBudgetIncomeVo> incomeVos = costBudgetIncomeService.findListByOrgCodesAndYears(orgCodes, years);
        incomeVos = incomeVos.stream().filter(e -> e.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode())).collect(Collectors.toList());
        List<String> itemCodes = incomeVos.stream().map(CostBudgetIncomeVo::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
        Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> null == v.getTaxRate() ? BigDecimal.ZERO : v.getTaxRate()));

        Map<String, BigDecimal> incomeMap = incomeVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDepartmentOneCode()) &&
                        ObjectUtils.isNotEmpty(x.getCostCenterCode()) && ObjectUtils.isNotEmpty(x.getYearMonthLy()) &&
                        ObjectUtils.isNotEmpty(x.getIncomeAmount()))
                .collect(Collectors.groupingBy(x -> x.getDepartmentOneCode() + column + x.getYearMonthLy(),
                        Collectors.mapping(k -> Optional.ofNullable(k.getIncomeAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(k.getItemCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //2.获取预算费用
        List<CostBudgetVo> budgetVos = costBudgetVoService.findListByOrgCodesAndYears(orgCodes, years);
        budgetVos = budgetVos.stream().filter(e -> e.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode())).collect(Collectors.toList());
        Map<String, BigDecimal> budgetMap = budgetVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDepartmentOneCode()) &&
                        ObjectUtils.isNotEmpty(x.getCostCenterCode()) && ObjectUtils.isNotEmpty(x.getYearMonthLy()) &&
                        ObjectUtils.isNotEmpty(x.getAdjustBalanceAmount()))
                .collect(Collectors.groupingBy(x -> x.getDepartmentOneCode() + column + x.getYearMonthLy(),
                        Collectors.mapping(k -> k.getAdjustBalanceAmount().divide(new BigDecimal(1).add(null == k.getTaxRate()? new BigDecimal(0) : k.getTaxRate()), 2,BigDecimal.ROUND_HALF_UP), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //3.查询销售计划-全表检索
        List<MarketingSalesPlanVo> salePlanList = marketingSalesPlanService.findListByConditionToCalMarketing(yearsSet, null, null, null, null, null, schemeCode, null);
        Map<String, List<MarketingSalesPlanVo>> salesDepartmentPlanMap = salePlanList.stream().collect(Collectors.groupingBy(MarketingSalesPlanVo::getCostCenterCode));
        //4.规划总额
        Map<String, BigDecimal> planAmountMap = caseList.stream().collect(Collectors.groupingBy(x -> x.getBelongDepartmentCode() + column + years,
                Collectors.mapping(x -> Optional.ofNullable(x.getLineNoTaxApplyAmount()).orElse(BigDecimal.ZERO),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //5.物料成本
        Set<String> materialCodes = salePlanList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                .map(MarketingSalesPlanVo::getMaterialCode).collect(Collectors.toSet());
        MaterialSearchDto searchDto = new MaterialSearchDto();
        searchDto.setMaterialCodeSet(materialCodes);
        searchDto.setCompanyCodeSet(companyCodeSet);
        List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
        this.materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO)));
        this.salesPlanMap = salePlanList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()) &&
                        ObjectUtils.isNotEmpty(x.getEstimatedSalesVolume()) && ObjectUtils.isNotEmpty(x.getCostCenterCode()))
                .collect(Collectors.groupingBy(MarketingSalesPlanVo::getCostCenterCode));
        this.cusProductSalesPlanMap = salePlanList.stream().collect(Collectors.toMap(x -> x.getCustomerCode() + x.getProductCode(), Function.identity(), (a, b) -> a));
        //6.查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        this.costTypeCategoryMap = costTypeCategoryVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (CustomerGainsAndLossesVo data : dataList) {
            //此处的indexkey = 三级部门+年月
            String indexKey = data.getIndexKey();
            OrgVo orgVo = orgMap.get(data.getOrgCode());
            Set<String> orgCostCenterCodes = orgVo.getCostCenterCodeSet();
            Validate.isTrue(CollectionUtils.isNotEmpty(orgCostCenterCodes), String.format("当前部门下%s不存在成本中心", orgVo.getOrgName()));
            data.setCostCenterCodes(orgCostCenterCodes);
            //预算收入
            data.setBudgetIncome(incomeMap.get(indexKey));
            //规划收入= 用部门匹配
            List<MarketingSalesPlanVo> salesPlanVoList = salesDepartmentPlanMap.entrySet().stream().filter(x -> orgCostCenterCodes.contains(x.getKey()))
                    .flatMap(x -> x.getValue().stream())
                    .collect(Collectors.toList());
            BigDecimal planIncome = salesPlanVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getEstimatedCost()))
                    .map(MarketingSalesPlanVo::getEstimatedCost)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //获取随单搭赠的费用
            BigDecimal noTaxApplyAmount = belongDepartmentNoTaxApplyAmountMap.getOrDefault(data.getOrgCode(), BigDecimal.ZERO);
            planIncome = planIncome.add(noTaxApplyAmount);
            data.setPlanIncome(planIncome);
            //收入达成率
            data.setIncomeAchieveRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getBudgetIncome()) && ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) > 0) {
                //规划收入/预算收入
                data.setIncomeAchieveRatio(data.getPlanIncome().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            //预算总额
            data.setBudgetTotalAmount(budgetMap.get(indexKey));
            //预算费率
            data.setBudgetRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getBudgetTotalAmount()) && ObjectUtils.isNotEmpty(data.getBudgetIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) > 0) {
                //预算总额/预算收入
                data.setBudgetRatio(data.getBudgetTotalAmount().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            //规划总额
            data.setPlanTotalAmount(planAmountMap.get(indexKey));
            //规划费率
            data.setPlanRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && ObjectUtils.isNotEmpty(data.getPlanTotalAmount()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                //规划总额/规划收入
                data.setPlanRatio(data.getPlanTotalAmount().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            //费率偏差 = 规划费率-预算费率
            data.setRatioDeviation(data.getPlanRatio().subtract(data.getBudgetRatio()));
        }
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calProfitRatio() {
        //搭赠生产成本
        Map<String, BigDecimal> noTaxApplyAmountMap = this.caseList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .filter(x -> ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .collect(Collectors.groupingBy(x -> x.getBelongDepartmentCode() + column + years,
                        Collectors.mapping(x -> Optional.ofNullable(x.getPolicyMaterialCostPrice()).orElse(BigDecimal.ZERO)
                                .multiply(Optional.ofNullable(x.getGiftQuantity()).orElse(BigDecimal.ZERO)), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //计算搭赠的运输费用
        this.calPolicyProductTransportCost();
        for (CustomerGainsAndLossesVo data : dataList) {
            //生产成本
            data.setProductionCosts(BigDecimal.ZERO);
            List<MarketingSalesPlanVo> salesVolumeList = salesPlanMap.entrySet().stream().filter(x -> data.getCostCenterCodes().contains(x.getKey()))
                    .flatMap(x -> x.getValue().stream())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(salesVolumeList)) {
                //销售计划的物料-找到物料成本价，用销售计划上的成本中心+年月匹配得到物料 在用预估销售额*物料成本价
                Map<String, BigDecimal> materialSalesVolumeMap = salesVolumeList.stream().collect(Collectors.groupingBy(MarketingSalesPlanVo::getMaterialCode,
                        Collectors.mapping(MarketingSalesPlanVo::getEstimatedSalesVolume, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                BigDecimal productionCosts = BigDecimal.ZERO;
                for (Map.Entry<String, BigDecimal> entry : materialSalesVolumeMap.entrySet()) {
                    BigDecimal costPrice = materialMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
                    productionCosts = productionCosts.add(costPrice.multiply(entry.getValue()));
                }
                BigDecimal noTaxApplyAmount = noTaxApplyAmountMap.getOrDefault(data.getIndexKey(), BigDecimal.ZERO);
                productionCosts = productionCosts.add(noTaxApplyAmount);
                //生产成本
                data.setProductionCosts(productionCosts);
                //产品运输费用计算
                List<String> companyCodes = Lists.newArrayList();
                for (MarketingSalesPlanVo salesPlan : salesVolumeList) {
                    if (costCenterMap.containsKey(salesPlan.getCostCenterCode())) {
                        MdmCostCenterVo costCenterVo1 = costCenterMap.get(salesPlan.getCostCenterCode());
                        companyCodes.add(costCenterVo1.getCompanyCode());
                        salesPlan.setCostCenterCompanyCode(costCenterVo1.getCompanyCode());
                    }
                }
                String year = years.split("-")[0];
                //查询物料成本
                List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialSalesVolumeMap.keySet(), companyCodes, year);
                Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                        x.getCompanyCode() + column + x.getYearStr(), Function.identity()));
                //产品运输费用
                BigDecimal productTransport = BigDecimal.ZERO;
                for (MarketingSalesPlanVo plan : salesVolumeList) {
                    String key = plan.getMaterialCode() + column + plan.getCompanyCode() + column + year;
                    if (materialCostMap.containsKey(key)) {
                        MaterialCostVo materialCostVo = materialCostMap.get(key);
                        BigDecimal price = BigDecimal.ZERO;
                        if (MarketingTransportEnum.bulk_cargo.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getLargeLogisticsPrice();
                        } else if (MarketingTransportEnum.express.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getExpressLogisticsPrice();
                        } else if (MarketingTransportEnum.cold_chain.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getColdLogisticsPrice();
                        } else if (MarketingTransportEnum.factory.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getFactoryStraightPrice();
                        }
                        productTransport = productTransport.add(price.multiply(plan.getEstimatedSalesVolume()));

                    }
                }
                data.setProductTransportCost(ObjectUtils.defaultIfNull(data.getProductTransportCost(), BigDecimal.ZERO).add(productTransport));
            }
            //TODO 样品与赠品费用细类
            //计算物料模板的费用运输
            BigDecimal peripheryTransportCost = BigDecimal.ZERO;
            List<MarketingPlanCase> materialCaseList = caseList.stream().filter(x -> MarketingPlanCaseTypeEnum.material.getCode().equals(x.getCaseType()) &&
                    (x.getBelongDepartmentCode() + column + x.getYears()).equals(data.getIndexKey())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(materialCaseList)) {
                //标准物料
                BigDecimal materialStandardAmount = materialCaseList.stream().filter(x -> MarketingPlanMaterialTypeEnum.standard_material.getCode().equals(x.getSellMaterialType())).map(MarketingPlanCase::getLineNoTaxApplyAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalStandardMaterialAmount = materialStandardAmount.multiply(BigDecimal.valueOf(0.15));
                peripheryTransportCost = peripheryTransportCost.add(totalStandardMaterialAmount);
                //非标准物料
                List<MarketingPlanCase> notStandardMaterialList = materialCaseList.stream().filter(x -> MarketingPlanMaterialTypeEnum.not_standard_material.getCode().equals(x.getSellMaterialType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(notStandardMaterialList)) {
                    List<String> companyCodes = Lists.newArrayList();
                    Set<String> notStandardMaterialCodes = Sets.newHashSet();
                    for (MarketingPlanCase planCase : notStandardMaterialList) {
                        companyCodes.add(planCase.getCompanyCode());
                        notStandardMaterialCodes.add(planCase.getMaterialCode());
                    }
                    //查询物料成本
                    String year = years.split("-")[0];
                    List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(notStandardMaterialCodes, companyCodes, year);
                    Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                            x.getCompanyCode() + column + x.getYearStr(), Function.identity()));
                    for (MarketingPlanCase planCase : notStandardMaterialList) {
                        String key = planCase.getMaterialCode() + column + planCase.getCompanyCode() + column + year;
                        if (materialCostMap.containsKey(key)) {
                            MaterialCostVo materialCostVo = materialCostMap.get(key);
                            BigDecimal price = BigDecimal.ZERO;
                            if (MarketingTransportEnum.bulk_cargo.getCode().equals(planCase.getTransportType())) {
                                price = materialCostVo.getLargeLogisticsPrice();
                            } else if (MarketingTransportEnum.express.getCode().equals(planCase.getTransportType())) {
                                price = materialCostVo.getExpressLogisticsPrice();
                            } else if (MarketingTransportEnum.cold_chain.getCode().equals(planCase.getTransportType())) {
                                price = materialCostVo.getColdLogisticsPrice();
                            } else if (MarketingTransportEnum.factory.getCode().equals(planCase.getTransportType())) {
                                price = materialCostVo.getFactoryStraightPrice();
                            }
                            peripheryTransportCost = peripheryTransportCost.add(price.multiply(planCase.getMaterialNum()));

                        }
                    }
                }
            }
            //周边运输费用
            data.setPeripheryTransportCost(peripheryTransportCost);
            //营销费用
            BigDecimal marketingCost = caseList.stream().filter(x -> (x.getBelongDepartmentCode() + column + x.getYears()).equals(data.getIndexKey()))
                    .map(x -> Optional.ofNullable(x.getLineNoTaxApplyAmount()).orElse(BigDecimal.ZERO))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            data.setMarketingCost(marketingCost);
        }
        return this;
    }


    /**
     * 搭赠运输费用
     */
    public void calPolicyProductTransportCost() {
        List<MarketingPlanCase> policyCaseList = this.caseList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .filter(x -> ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .collect(Collectors.toList());
        try {
            if (CollectionUtils.isEmpty(policyCaseList)) return;
            Set<String> materialCodeSet = policyCaseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getPolicyProductCode()))
                    .map(MarketingPlanCase::getPolicyProductCode).collect(Collectors.toSet());
            List<String> companyCodes = policyCaseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode()))
                    .map(MarketingPlanCase::getCompanyCode).distinct().collect(Collectors.toList());


            for (MarketingPlanCase aCase : policyCaseList) {
                String key = aCase.getCustomerCode() + aCase.getPolicyProductCode();
                if (cusProductSalesPlanMap.containsKey(key)) {
                    aCase.setTransportType(cusProductSalesPlanMap.get(key).getTransportType());
                }
            }
            List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialCodeSet, companyCodes, year);
            Map<String, MaterialCostVo> materialCostVoMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                    x.getCompanyCode() + column + x.getYearStr(), Function.identity()));

            Map<String, BigDecimal> materialCostMap = Maps.newHashMap();
            for (MarketingPlanCase planCase : policyCaseList) {
                String key = planCase.getPolicyProductCode() + column + planCase.getCompanyCode() + column + year;
                if (materialCostVoMap.containsKey(key)) {
                    MaterialCostVo materialCostVo = materialCostVoMap.get(key);
                    BigDecimal price = BigDecimal.ZERO;
                    if (MarketingTransportEnum.bulk_cargo.getCode().equals(planCase.getTransportType())) {
                        price = materialCostVo.getLargeLogisticsPrice();
                    } else if (MarketingTransportEnum.express.getCode().equals(planCase.getTransportType())) {
                        price = materialCostVo.getExpressLogisticsPrice();
                    } else if (MarketingTransportEnum.cold_chain.getCode().equals(planCase.getTransportType())) {
                        price = materialCostVo.getColdLogisticsPrice();
                    } else if (MarketingTransportEnum.factory.getCode().equals(planCase.getTransportType())) {
                        price = materialCostVo.getFactoryStraightPrice();
                    }
                    materialCostMap.put(planCase.getPolicyProductCode(), price);

                }
            }


            Map<String, BigDecimal> materialKeyCostMap = Maps.newHashMap();
            Map<String, List<MarketingPlanCase>> policyCaseMap = policyCaseList.stream().collect(Collectors.groupingBy(x -> x.getBelongDepartmentCode() + column + years));
            for (Map.Entry<String, List<MarketingPlanCase>> entry : policyCaseMap.entrySet()) {
                BigDecimal amount = BigDecimal.ZERO;
                for (MarketingPlanCase aCase : entry.getValue()) {
                    BigDecimal transportAmount = materialCostMap.getOrDefault(aCase.getPolicyProductCode(), BigDecimal.ZERO);
                    amount = amount.add(transportAmount.multiply(ObjectUtils.defaultIfNull(aCase.getGiftQuantity(), BigDecimal.ZERO)));
                }
                materialKeyCostMap.put(entry.getKey(), amount);
            }
            for (CustomerGainsAndLossesVo data : dataList) {
                BigDecimal productTransPort = materialKeyCostMap.getOrDefault(data.getIndexKey(), BigDecimal.ZERO);
                data.setProductTransportCost(productTransPort);
            }
        } finally {
            for (CustomerGainsAndLossesVo data : dataList) {
                data.setProductTransportCost(ObjectUtils.defaultIfNull(data.getProductTransportCost(), BigDecimal.ZERO));
            }
        }
    }


    @Override
    public DepartmentEstimationBuilder calGrossProfitRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //规划毛利额=规划收入-生产成本
            data.setPlanGrossProfitMargin(data.getPlanIncome().subtract(data.getProductionCosts()));
            //毛利率=规划毛利额/规划收入
            data.setGrossProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setGrossProfitRatio(data.getPlanGrossProfitMargin().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calLogisticsRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                //物流费率=(产品运输费用+周边运输费用)/规划收入
                data.setLogisticsRatio((data.getProductTransportCost().add(data.getPeripheryTransportCost())).divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calMarketingRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                //营销费率=营销费用/规划收入
                data.setMarketingRatio(data.getMarketingCost().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }


    /**
     * 公摊费率
     *
     * @return
     */
    @Override
    public DepartmentEstimationBuilder calPublicShareRatio() {
        for (CustomerGainsAndLossesVo vo : dataList) {
            String orgCode = vo.getOrgCode();
            List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(orgCode);
            List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            BigDecimal ratio = publicShareRatioService.findRatioByCondition(orgCodes, years);
            //如果等于0  则取方案上的部门上级所有去找
            if (ratio.compareTo(BigDecimal.ZERO) == 0) {
                List<OrgVo> list = orgVoService.findAllParentByOrgCodes(Lists.newArrayList(orgCodeSet));
                List<String> list1 = list.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                ratio = publicShareRatioService.findRatioByCondition(Lists.newArrayList(list1), years);
            }
            vo.setPublicShareRatio(ratio);
            BigDecimal publicShare = vo.getPlanIncome().multiply(vo.getPublicShareRatio());
            //规划利润额=规划收入-生产成本-产品运输费用-周边运输费用-营销费用
            vo.setPlanProfitMargin(vo.getPlanIncome().subtract(vo.getProductionCosts()).subtract(vo.getProductTransportCost())
                    .subtract(vo.getPeripheryTransportCost()).subtract(vo.getMarketingCost()).subtract(publicShare));
            //利润率=规划利润额/规划收入
            vo.setProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(vo.getPlanIncome()) && vo.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                vo.setProfitRatio(vo.getPlanProfitMargin().divide(vo.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }


    /**
     * 费用转移  同一个部门下的
     *
     * @return
     */
    @Override
    public DepartmentEstimationBuilder calCostShift() {
        List<String> bearDepartmentCodeList = caseList.stream().map(x -> x.getBearDepartmentCode()).distinct().collect(Collectors.toList());
        List<OrgVo> bearDepartmentList = orgVoService.findByOrgCodes(bearDepartmentCodeList);
        List<String> belongDepartmentCodeList = caseList.stream().map(x -> x.getBelongDepartmentCode()).distinct().collect(Collectors.toList());
        List<OrgVo> belongDepartmentList = orgVoService.findByOrgCodes(belongDepartmentCodeList);

        Map<String, String> bearDepartmentMap = bearDepartmentList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getRuleCode));
        Map<String, String> belongDepartmentMap = belongDepartmentList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getRuleCode));

        for (CustomerGainsAndLossesVo vo : dataList) {
            String belongRuleCode = belongDepartmentMap.get(vo.getOrgCode());
            Map<String, BigDecimal> bearApplyAmountMap = caseList.stream().filter(x -> belongDepartmentMap.containsKey(x.getBelongDepartmentCode()))
                    .collect(Collectors.groupingBy(x -> x.getBearDepartmentCode(),
                            Collectors.mapping(MarketingPlanCase::getLineNoTaxApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            BigDecimal costShift = BigDecimal.ZERO;
            for (Map.Entry<String, BigDecimal> entry : bearApplyAmountMap.entrySet()) {
                String bearRuleCode = bearDepartmentMap.get(entry.getKey());
                if (ObjectUtils.isNotEmpty(bearRuleCode) && ObjectUtils.isNotEmpty(belongRuleCode) &&
                        bearRuleCode.length() >= 8 && belongRuleCode.length() >= 8) {
                    String bearRuleCodePrefix = bearRuleCode.substring(0, 8);
                    String belongRuleCodePrefix = belongRuleCode.substring(0, 8);
                    if (!bearRuleCodePrefix.equals(belongRuleCodePrefix)) {
                        costShift = costShift.add(entry.getValue());
                    }
                }
            }
            vo.setCostShift(costShift);
        }

        return this;
    }


    /**
     * 考核利润
     *
     * @return
     */
    @Override
    public DepartmentEstimationBuilder calAssessProfits() {
        for (CustomerGainsAndLossesVo vo : dataList) {
            BigDecimal assessProfits = vo.getPlanProfitMargin().add(vo.getCostShift());
            vo.setAssessProfits(assessProfits);
        }
        return this;
    }

    @Override
    public DepartmentEstimationBuilder calSecondCategory() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //二级费用大类计算
            Map<String, BigDecimal> secondCategoryMap = Maps.newHashMap();
            for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
                BigDecimal applyAmount = caseList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()) &&
                                (x.getBelongDepartmentCode() + column + x.getYears()).equals(data.getIndexKey()))
                        .map(x -> Optional.ofNullable(x.getLineNoTaxApplyAmount()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal ratio = BigDecimal.ZERO;
                if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                    ratio = applyAmount.divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
                }
                secondCategoryMap.put(entry.getKey(), ratio);

            }

            for (Map.Entry<String, BigDecimal> entry : secondCategoryMap.entrySet()) {
                BigDecimal value = entry.getValue();
                if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
                    data.setContract(value);
                } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
                    data.setGeneralization(value);
                } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
                    data.setPromotion(value);
                } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
                    data.setSalesReward(value);
                } else if (SecondCategoryEnum.callback.getName().equals(entry.getKey())) {
                    data.setCallback(value);
                } else if (SecondCategoryEnum.display.getName().equals(entry.getKey())) {
                    data.setDisplay(value);
                } else if (SecondCategoryEnum.disseminateS.getName().equals(entry.getKey())) {
                    data.setDisseminate(value);
                }

            }
            data.setSecondCostCategoryMap(secondCategoryMap);
        }
        this.transRatio();
        return this;
    }

    private void transRatio() {
        for (CustomerGainsAndLossesVo vo : dataList) {
            if (ObjectUtils.isNotEmpty(vo.getBudgetIncome())) {
                vo.setBudgetIncome(vo.getBudgetIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanIncome())) {
                vo.setPlanIncome(vo.getPlanIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getIncomeAchieveRatio())) {
                vo.setIncomeAchieveRatioStr(vo.getIncomeAchieveRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetRatio())) {
                vo.setBudgetRatioStr(vo.getBudgetRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanRatio())) {
                vo.setPlanRatioStr(vo.getPlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getRatioDeviation())) {
                vo.setRatioDeviationStr(vo.getRatioDeviation().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getProfitRatio())) {
                vo.setProfitRatioStr(vo.getProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGrossProfitRatio())) {
                vo.setGrossProfitRatioStr(vo.getGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getLogisticsRatio())) {
                vo.setLogisticsRatioStr(vo.getLogisticsRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getMarketingRatio())) {
                vo.setMarketingRatioStr(vo.getMarketingRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPublicShareRatio())) {
                vo.setPublicShareRatioStr(vo.getPublicShareRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPromotion())) {
                vo.setPromotionStr(vo.getPromotion().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getCallback())) {
                vo.setCallbackStr(vo.getCallback().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisplay())) {
                vo.setDisplayStr(vo.getDisplay().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGeneralization())) {
                vo.setGeneralizationStr(vo.getGeneralization().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisseminate())) {
                vo.setDisseminateStr(vo.getDisseminate().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getSalesReward())) {
                vo.setSalesRewardStr(vo.getSalesReward().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getContract())) {
                vo.setContractStr(vo.getContract().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getCostShift())){
                vo.setCostShift(vo.getCostShift().divide(BigDecimal.valueOf(10000),2,BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getAssessProfits())){
                vo.setAssessProfits(vo.getAssessProfits().divide(BigDecimal.valueOf(10000),2,BigDecimal.ROUND_HALF_DOWN));
            }
        }
    }
}
