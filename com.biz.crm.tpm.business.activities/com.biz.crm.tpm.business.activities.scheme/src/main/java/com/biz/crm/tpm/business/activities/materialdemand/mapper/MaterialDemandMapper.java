package com.biz.crm.tpm.business.activities.materialdemand.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemand;
import com.biz.crm.tpm.business.activities.materialdemand.exports.model.MaterialDemandDetailExportVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:31
 */
public interface MaterialDemandMapper extends BaseMapper<MaterialDemand> {

    Page<MaterialDemandVo> findList(Page<MaterialDemandVo> page, @Param("vo") MaterialDemandVo vo);

    List<MaterialDemandDetailVo> findListByOrgCodes(@Param("orgCodes") List<String> orgCodes, @Param("processStatusList") List<String> processStatusList);

    List<MaterialDemandDetailVo> selectMaterialDemandDetailList(@Param("inExcludeDemandDetailCodes") List<String> inExcludeDemandDetailCodes, @Param("demandCodes") List<String> demandCodes);

    /**
     * 根据条件查询导出明细
     *
     * @param page
     * @param vo
     * @return
     */
    Page<MaterialDemandDetailExportVo> findDetailExportByDto(@Param("page") Page<MaterialDemandDetailExportVo> page,
                                                             @Param("vo") MaterialDemandVo vo);
}
