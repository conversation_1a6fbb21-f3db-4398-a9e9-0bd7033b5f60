package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.WorkflowFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 14:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_plan")
@Table(
        name = "tpm_marketing_plan",
        indexes = {
                @Index(name = "tpm_marketing_plan_index0", columnList = "scheme_code", unique = true),
                @Index(name = "tpm_marketing_plan_index1", columnList = "scheme_type,process_status"),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_plan", comment = "营销方案规划")
@ApiModel(value = "MarketingPlan", description = "营销方案规划")
public class MarketingPlan extends WorkflowFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("原方案编码")
    @Column(name = "original_scheme_code", columnDefinition = "varchar(32) comment '原方案编码'")
    private String originalSchemeCode;

    @ApiModelProperty("方案名称")
    @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称'")
    private String schemeName;

    @ApiModelProperty("方案主题")
    @Column(name = "scheme_theme", columnDefinition = "varchar(512) comment '方案主题'")
    private String schemeTheme;

    @ApiModelProperty("部门编码")
    @Column(name = "department_code", columnDefinition = "varchar(32) comment '部门编码'")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    @Column(name = "department_name", columnDefinition = "varchar(128) comment '部门名称'")
    private String departmentName;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("方案说明")
    @Column(name = "scheme_desc", columnDefinition = "varchar(256) comment '方案说明'")
    private String schemeDesc;

    @ApiModelProperty("变更方案说明")
    @Column(name = "change_desc", columnDefinition = "varchar(256) comment '变更方案说明'")
    private String changeDesc;

    @ApiModelProperty("创建人组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '创建人组织编码'")
    private String orgCode;

    @ApiModelProperty("创建人组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(128) comment '创建人组织名称'")
    private String orgName;

    @ApiModelProperty("创建人职位编码")
    @Column(name = "position_code", columnDefinition = "varchar(32) comment '创建人职位编码'")
    private String positionCode;

    @ApiModelProperty("创建人职位名称")
    @Column(name = "position_name", columnDefinition = "varchar(128) comment '创建人职位名称'")
    private String positionName;

    @ApiModelProperty("确认状态")
    @Column(name = "confirm_status", columnDefinition = "varchar(10) comment '确认状态'")
    private String confirmStatus;

    @ApiModelProperty("方案状态")
    @Column(name = "scheme_status", columnDefinition = "varchar(10) comment '方案状态'")
    private String schemeStatus;

    @ApiModelProperty("方案类型")
    @Column(name = "scheme_type", columnDefinition = "varchar(20) comment '方案类型'")
    private String schemeType;

    @ApiModelProperty("是否新开客户")
    @Column(name = "new_customer_flag", columnDefinition = "varchar(10) comment '是否新开客户补录方案'")
    private String newCustomerFlag;

    @ApiModelProperty("是否变更方案,Y-是,N-否")
    @Column(name = "changed_flag", columnDefinition = "varchar(10) comment '是否变更方案,Y-是,N-否'")
    private String changedFlag;

    @ApiModelProperty("是否合同费用")
    @Column(name = "is_contract_cost", columnDefinition = "varchar(10) comment '是否合同费用'")
    private String isContractCost;

    @ApiModelProperty("合同客户")
    @Column(name = "contract_cus_code", columnDefinition = "varchar(32) comment '合同客户'")
    private String contractCusCode;

    @ApiModelProperty("申请费用合计")
    @Column(name = "apply_total_amount", columnDefinition = "decimal(18,2) comment '申请费用合计'")
    private BigDecimal applyTotalAmount;

    @ApiModelProperty("推送日期")
    @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    @Column(name = "oa_id", columnDefinition = "varchar(32) COMMENT 'OA人员id'")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    @Column(name = "oa_user_name", columnDefinition = "varchar(32) COMMENT 'OA人员账号'")
    private String oaUserName;

    @ApiModelProperty("校验预算")
    @Column(name = "check_budget_flag", columnDefinition = "varchar(10) comment '校验预算'")
    private String checkBudgetFlag;

    @ApiModelProperty("变更-客户损益对比（方案维度）")
    @Column(name = "compare_change_gains_and_losses", columnDefinition = "varchar(10) comment '变更-客户损益对比（方案维度)")
    private String compareChangeGainsAndLosses;
}
