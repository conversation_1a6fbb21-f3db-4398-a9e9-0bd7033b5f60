package com.biz.crm.tpm.business.activities.scheme.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 方案活动关联信息
 */
@Getter
@Setter
@Entity
@TableName("tpm_activities_scheme_relation")
@Table(name = "tpm_activities_scheme_relation", indexes = {@Index(name = "tpm_activities_scheme_relation_index1", columnList = "activity_code,cost_budget_code,cost_type_category_code", unique = true)})
@ApiModel(value = "ActivitiesSchemeRelation", description = "方案活动关联信息")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_scheme_relation", comment = "方案活动关联信息")
public class ActivitiesSchemeRelation extends TenantOpEntity {

  @ApiModelProperty("关联的活动编码")
  @TableField(value = "activity_code")
  @Column(name = "activity_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '关联的活动编码'")
  private String activityCode;

  @ApiModelProperty("费用预算编码")
  @TableField(value = "cost_budget_code")
  @Column(name = "cost_budget_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '费用预算编码'")
  private String costBudgetCode;

  @ApiModelProperty("活动大类编码")
  @TableField(value = "cost_type_category_code")
  @Column(name = "cost_type_category_code", length = 64, nullable = true, columnDefinition = "varchar(64) COMMENT '活动大类编码'")
  private String costTypeCategoryCode;

  @ApiModelProperty("活动大类名称")
  @TableField(value = "cost_type_category_name")
  @Column(name = "cost_type_category_name", nullable = true, columnDefinition = "varchar(255) COMMENT '活动大类名称'")
  private String costTypeCategoryName;

  @ApiModelProperty("活动细类编码")
  @TableField(value = "cost_type_detail_code")
  @Column(name = "cost_type_detail_code", length = 64, columnDefinition = "varchar(64) COMMENT '活动细类编码'")
  private String costTypeDetailCode;

  @ApiModelProperty("活动细类名称")
  @TableField(value = "cost_type_detail_name")
  @Column(name = "cost_type_detail_name", columnDefinition = "varchar(255) COMMENT '活动细类名称'")
  private String costTypeDetailName;

}
