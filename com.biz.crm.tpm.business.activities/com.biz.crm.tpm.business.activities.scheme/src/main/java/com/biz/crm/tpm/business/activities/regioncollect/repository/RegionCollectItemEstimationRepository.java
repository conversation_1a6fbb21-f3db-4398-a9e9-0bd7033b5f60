package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLosses;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectItemEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectItemEstimationMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class RegionCollectItemEstimationRepository extends ServiceImpl<RegionCollectItemEstimationMapper, RegionCollectItemEstimation> {

    public List<RegionCollectItemEstimation> findByCollectCode(String collectCode) {
        return this.lambdaQuery()
                .eq(RegionCollectItemEstimation::getCollectCode, collectCode).list();
    }

    public void deleteByCollectCodes(List<String> collectCodes) {
        this.lambdaUpdate()
                .in(RegionCollectItemEstimation::getCollectCode, collectCodes).remove();
    }
}
