package com.biz.crm.tpm.business.activities.marketingplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:21
 */
public interface MarketingPlanMapper extends BaseMapper<MarketingPlan> {


    /**
     * 查询分页列表
     *
     * @param page
     * @param vo
     * @return
     */
    Page<MarketingPlanVo> findList(Page<MarketingPlanVo> page, @Param("vo") MarketingPlanVo vo);

    List<MarketingPlan> findListByContractCodes(@Param("contractCodes") List<String> contractCodes, @Param("tenantCode") String tenantCode);

    List<MarketingPlanVo> findMarketingPlanCommit(@Param("yearMonthLy") String yearMonthLy);


    List<MarketingPlanCase> findListByCustomerCodesAndBelongDepartmentCodes(@Param("customerCodes") List<String> customerCodes,
                                                                            @Param("departmentCodes") List<String> departmentCodes,
                                                                            @Param("schemeCode") String schemeCode, @Param("years") String years);

    List<MarketingPlanCase> findChangeOccupyAmount(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    List<MarketingPlanCaseVo> findRepateCaseListByCondition(@Param("cusCodes") List<String> cusCodes, @Param("belongDepartmentCodes") List<String> belongDepartmentCodes,
                                                            @Param("levelProductCodes") List<String> levelProductCodes, @Param("isContractCost") List<String> isContractCost,
                                                            @Param("excludeSchemeDetailCodes") List<String> excludeSchemeDetailCodes,@Param("schemeCode")String schemeCode,
                                                            @Param("originalSchemeCode")String originalSchemeCode,@Param("caseType")String caseType);


    void updateCustomerCooperateType();

    void  updateCustomerCooperateTypeByAudit();

    List<MarketingPlanCaseVo> findMiniCostTotalView(@Param("vo") MarketingPlanCaseVo vo);

    List<MarketingPlanCase> findListByCondition(@Param("actYearsList") Set<String> actYearsList, @Param("customerCodes") Set<String> customerCodes,
                                                @Param("belongDepartmentCodes") Set<String> belongDepartmentCodes,@Param("schemeTypes") List<String> schemeTypes);

    List<MarketingPlanCase> findAddSchemeListByYearsAndCustomers(@Param("years") String years,@Param("customerCodes") List<String> customerCodes);

    List<String> countTerminalNum(@Param("years") String years);
}
