package com.biz.crm.tpm.business.activities.scheme.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 实体：方案活动附件;
 * <AUTHOR> <PERSON>
 * @date : 2022-6-20
 */
@ApiModel(value = "ActivitiesSchemeFiles",description = "方案活动附件")
@TableName("tpm_activities_scheme_files")
@Getter
@Setter
@Entity(name = "tpm_activities_scheme_files")
@org.hibernate.annotations.Table(appliesTo = "tpm_activities_scheme_files", comment = "方案活动附件")
@Table(name = "tpm_activities_scheme_files")
public class ActivitiesSchemeFiles extends FileEntity {

  /** 活动编号 */
  @ApiModelProperty(name = "活动编号",notes = "活动编号")
  @Column(name = "activities_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动编号 '")
  private String activitiesCode;
}