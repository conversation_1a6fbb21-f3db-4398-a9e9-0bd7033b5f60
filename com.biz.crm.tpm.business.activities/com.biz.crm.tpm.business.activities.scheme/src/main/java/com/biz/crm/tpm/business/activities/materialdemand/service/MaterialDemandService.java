package com.biz.crm.tpm.business.activities.materialdemand.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:35
 */
public interface MaterialDemandService {

    Page<MaterialDemandVo> findList(Pageable pageable, MaterialDemandVo vo);

    MaterialDemandVo queryByIdOrCode(String id, String code);

    void create(MaterialDemandVo vo);

    void update(MaterialDemandVo vo);

    String deleteBatchByIds(List<String> ids);

    List<MaterialDemandDetailVo> findListByOrgCodes(List<String> orgCodes, List<String> processStatusList);

    List<MaterialDemandDetailVo> selectMaterialDemandDetailList(List<String> inExcludeDemandDetailCodes, List<String> demandCodes);

    void updateProcessStatus(String processStatus, List<String> demandCodes);

}
