package com.biz.crm.tpm.business.activities.scheme.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.scheme.dto.ActivitiesSchemeDto;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesScheme;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeDetailRelation;
import com.biz.crm.tpm.business.activities.scheme.mapper.ActivitiesSchemeMapper;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeVo;
import com.biz.crm.tpm.business.activities.sdk.enums.ActivityStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 方案活动;(tpm_activities_scheme)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-6-1
 */
@Component
public class ActivitiesSchemeRepository extends ServiceImpl<ActivitiesSchemeMapper, ActivitiesScheme> {
  @Autowired
  private ActivitiesSchemeMapper activitiesSchemeMapper;

  /**
   * 批量根据id禁用
   *
   * @param enable
   * @param ids
   */
  public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
    UpdateWrapper<ActivitiesScheme> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("enable_status", enable.getCode());
    updateWrapper.in("id", ids);
    updateWrapper.eq("tenant_code",TenantUtils.getTenantCode());
    this.update(updateWrapper);
  }

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<ActivitiesSchemeVo> findByConditions(Pageable pageable, ActivitiesSchemeDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesSchemeVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return activitiesSchemeMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<ActivitiesScheme>
   */
  public List<ActivitiesScheme> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .in(ActivitiesScheme::getId, ids)
        .eq(ActivitiesScheme::getTenantCode, tenantCode)
        .eq(ActivitiesScheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  /**
   * 根据编号与租户编号获取对象
   *
   * @param code
   * @return
   */
  public ActivitiesScheme findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .eq(ActivitiesScheme::getActivitiesCode, code)
        .eq(ActivitiesScheme::getTenantCode, tenantCode)
        .eq(ActivitiesScheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
  }

  /**
   * 根据编号集合获取详情集合
   *
   * @param codes 编号集合
   * @return List<ActivitiesScheme>
   */
  public List<ActivitiesScheme> findByCodes(List<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .in(ActivitiesScheme::getActivitiesCode, codes)
        .eq(ActivitiesScheme::getTenantCode, tenantCode)
        .eq(ActivitiesScheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  /**
   * 根据启用状态与租户编号查询对象
   *
   * @param enableStatus
   * @return
   */
  public List<ActivitiesScheme> findByEnableStatus(String enableStatus) {
    String tenantCode = TenantUtils.getTenantCode();
    if (StringUtils.isBlank(enableStatus)) {
      return this.lambdaQuery()
          .eq(ActivitiesScheme::getTenantCode, tenantCode)
          .eq(ActivitiesScheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }
    return this.lambdaQuery()
        .eq(ActivitiesScheme::getEnableStatus, enableStatus)
        .eq(ActivitiesScheme::getTenantCode, tenantCode)
        .eq(ActivitiesScheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
  }

  @Override
  public boolean removeByIds(Collection<? extends Serializable> idList) {
    String tenantCode = TenantUtils.getTenantCode();
    UpdateWrapper<ActivitiesScheme> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("del_flag", DelFlagStatusEnum.DELETE.getCode());
    updateWrapper.eq("tenant_code", tenantCode);
    updateWrapper.in("id", idList);
    return this.update(updateWrapper);
  }

  public int countByScheme(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return 0;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
        .eq(ActivitiesScheme::getTenantCode, tenantCode)
        .eq(ActivitiesScheme::getSchemeCode, schemeCode).count();
  }

  /**
   * 根据提供的参数，更新status
   *
   * @param code        主表编码
   * @param isAllClosed 是否全部关闭，true表示全部关闭，false表示部分关闭
   */
  public void updateForClose(String code, Boolean isAllClosed) {
    ActivityStatusEnum status = isAllClosed ? ActivityStatusEnum.ALL_CLOSE : ActivityStatusEnum.PARTIAL_CLOSE;
    this.lambdaUpdate()
        .eq(ActivitiesScheme::getActivitiesCode, code)
        .eq(ActivitiesScheme::getTenantCode,TenantUtils.getTenantCode())
        .set(ActivitiesScheme::getStatus, status.getCode())
        .update();
  }

  /**
   * 查询待数据刷新的数据，该方法针对定时任务的整体查询
   */
  public List<ActivitiesScheme> findByRefreshStatusTask() {
    return this.lambdaQuery().notIn(ActivitiesScheme::getStatus, Sets.newHashSet(ActivityStatusEnum.ALL_CLOSE.getCode(),
        ActivityStatusEnum.PARTIAL_CLOSE.getCode()
        , ActivityStatusEnum.ENDED.getCode())).
        eq(ActivitiesScheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).
        eq(ActivitiesScheme::getEnableStatus, EnableStatusEnum.ENABLE.getCode()).
        eq(ActivitiesScheme::getTenantCode,TenantUtils.getTenantCode()).list();
  }

  public ActivitiesScheme findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesScheme::getTenantCode,tenantCode)
        .in(ActivitiesScheme::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ActivitiesScheme::getTenantCode,tenantCode)
        .in(ActivitiesScheme::getId,ids)
        .remove();
  }
}