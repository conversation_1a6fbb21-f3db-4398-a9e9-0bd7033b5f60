package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanChangeSchemeCase;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanChangeSchemeCaseMapper;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/18 13:47
 */
@Component
public class MarketingPlanChangeSchemeCaseRepository extends ServiceImpl<MarketingPlanChangeSchemeCaseMapper, MarketingPlanChangeSchemeCase> {


    public List<MarketingPlanChangeSchemeCase> findListByOriginalSchemeDetailCodes(List<String> schemeDetailCode) {
        if (CollectionUtils.isEmpty(schemeDetailCode)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findListByOriginalSchemeDetailCodes(schemeDetailCode);
    }

    public void saveBatchList(List<String> originalSchemeDetailCodes, String schemeCode) {
        if (CollectionUtils.isEmpty(originalSchemeDetailCodes)) return;
        this.lambdaUpdate()
                .eq(MarketingPlanChangeSchemeCase::getSchemeCode, schemeCode)
                .remove();
        List<MarketingPlanChangeSchemeCase> list = originalSchemeDetailCodes.stream().map(x -> {
            MarketingPlanChangeSchemeCase changeSchemeCase = new MarketingPlanChangeSchemeCase();
            changeSchemeCase.setSchemeCode(schemeCode);
            changeSchemeCase.setOriginalSchemeDetailCode(x);
            return changeSchemeCase;
        }).collect(Collectors.toList());
        this.saveBatch(list);
    }

    public void deleteBySchemeCode(List<String> schemeCodes) {
        this.lambdaUpdate()
                .in(MarketingPlanChangeSchemeCase::getSchemeCode, schemeCodes)
                .remove();
    }
}
