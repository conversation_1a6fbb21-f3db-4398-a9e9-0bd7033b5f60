package com.biz.crm.tpm.business.activities.overallplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/4 19:11
 */
@Getter
@AllArgsConstructor
public enum OverallPlanProductEnum {

    cal_item("cal_item", "核算品项"),
    cal_product("cal_product", "核算商品"),
    cal_level("cal_level", "核算层级"),

    fee_item("fee_item", "费用品项"),
    fee_product("fee_product", "费用商品"),
    fee_level("fee_level", "费用层级"),
    fee_belong_item("fee_belong_item", "费用归属品项"),
    ;
    private String code;

    private String desc;

    public static OverallPlanProductEnum getEnum(String code){
        for (OverallPlanProductEnum value : OverallPlanProductEnum.values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
