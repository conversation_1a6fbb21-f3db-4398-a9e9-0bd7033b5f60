package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectItemEstimationBack;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectItemEstimationBackRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectItemEstimationBackService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectItemEstimationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Slf4j
@Service
public class RegionCollectItemEstimationBackServiceImpl implements RegionCollectItemEstimationBackService {

    @Resource
    private RegionCollectItemEstimationBackRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCollectCodes(List<String> collectCodes) {
        repository.deleteByCollectCodes(collectCodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveList(List<RegionCollectItemEstimationVo> list, String collectCode) {
        repository.deleteByCollectCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<RegionCollectItemEstimationBack> dataList = list.stream().map(x -> {
            RegionCollectItemEstimationBack data = nebulaToolkitService.copyObjectByWhiteList(x, RegionCollectItemEstimationBack.class, HashSet.class, ArrayList.class);
            if (!CollectionUtils.isEmpty(data.getSecondCostCategoryMap())) {
                data.setSecondCostCategoryJsonStr(JSONObject.toJSONString(data.getSecondCostCategoryMap()));
            }
            data.setId(null);
            data.setCollectCode(collectCode);
            return data;
        }).collect(Collectors.toList());
        repository.saveBatch(dataList);
    }
}
