package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanBackGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanBackGainsAndLossesRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanBackGainsAndLossesService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanGainsAndLossesService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanBackGainsAndLossesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import liquibase.pro.packaged.A;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/4 10:17
 */
@Service
public class MarketingPlanBackGainsAndLossesServiceImpl implements MarketingPlanBackGainsAndLossesService {

    @Resource
    private MarketingPlanBackGainsAndLossesRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MarketingPlanGainsAndLossesService marketingPlanGainsAndLossesService;


    public List<MarketingPlanBackGainsAndLosses> findListBySchemeCode(String changeSchemeCode) {
        List<MarketingPlanBackGainsAndLosses> list = repository.findListBySchemeCodes(Lists.newArrayList(changeSchemeCode));
        for (MarketingPlanBackGainsAndLosses losses : list) {
            if (ObjectUtils.isNotEmpty(losses.getSecondCostCategoryJsonStr())) {
                losses.setSecondCostCategoryMap(JSONObject.parseObject(losses.getSecondCostCategoryJsonStr(), LinkedHashMap.class));
            }
        }
        return list;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchList(List<MarketingPlanBackGainsAndLossesVo> voList, String schemeCode, String changeSchemeCode) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        //删除原来的数据
        repository.remove(Wrappers.lambdaQuery(MarketingPlanBackGainsAndLosses.class)
                .eq(MarketingPlanBackGainsAndLosses::getSchemeCode, schemeCode));
        List<MarketingPlanBackGainsAndLosses> list = (List<MarketingPlanBackGainsAndLosses>) nebulaToolkitService.copyCollectionByWhiteList(voList,
                MarketingPlanBackGainsAndLossesVo.class, MarketingPlanBackGainsAndLosses.class, HashSet.class, ArrayList.class);
        list.forEach(x -> {
            x.setSchemeCode(schemeCode);
            x.setChangeSchemeCode(changeSchemeCode);
            if (!CollectionUtils.isEmpty(x.getSecondCostCategoryMap())) {
                x.setSecondCostCategoryJsonStr(JSONObject.toJSONString(x.getSecondCostCategoryMap()));
            }
            x.setId(null);
        });
        repository.saveBatch(list);
    }


    /**
     * 存放原方案的客户损益数据信息
     *
     * @param originalSchemeCode
     * @param changeSchemeCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOriginalGainsAndLosses(String originalSchemeCode, String changeSchemeCode) {
        List<MarketingPlanGainsAndLosses> gainsAndLossesList = marketingPlanGainsAndLossesService.findListBySchemeCodes(Lists.newArrayList(originalSchemeCode));
        List<MarketingPlanBackGainsAndLosses> backGainsAndLosses = (List<MarketingPlanBackGainsAndLosses>) nebulaToolkitService.copyCollectionByWhiteList(gainsAndLossesList,
                MarketingPlanGainsAndLosses.class, MarketingPlanBackGainsAndLosses.class, HashSet.class, ArrayList.class);
        for (MarketingPlanBackGainsAndLosses loss : backGainsAndLosses) {
            loss.setId(null);
            loss.setSchemeCode(originalSchemeCode);
            loss.setChangeSchemeCode(changeSchemeCode);
        }
        repository.saveBatch(backGainsAndLosses);
    }
}
