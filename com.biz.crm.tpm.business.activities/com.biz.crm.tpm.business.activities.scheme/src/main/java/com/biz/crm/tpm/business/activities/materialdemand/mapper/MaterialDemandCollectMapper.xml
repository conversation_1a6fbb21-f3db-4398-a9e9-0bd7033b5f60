<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.materialdemand.mapper.MaterialDemandCollectMapper">


    <select id="findList" resultType="com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectVo">
        select * from tpm_material_demand_collect
        <where>
            del_flag = '${@<EMAIL>()}'
            <if test="vo.tenantCode != null and vo.tenantCode != ''">
                and tenant_code = #{vo.tenantCode}
            </if>
            <if test="vo.collectCode != null and vo.collectCode !=''">
                <bind name="collectCode" value="vo.collectCode + '%'"/>
                and collect_code like #{collectCode}
            </if>
            <if test="vo.collectName != null and vo.collectName != ''">
                <bind name="collectName" value="'%' + vo.collectName + '%'"/>
                and collect_name like #{collectName}
            </if>
            <if test="vo.orgName != null and vo.orgName != ''">
                <bind name="orgName" value="'%' + vo.orgName + '%'"/>
                and org_name like #{orgName}
            </if>
            <if test="vo.processStatus != null and vo.processStatus != ''">
                and process_status = #{vo.processStatus}
            </if>
            <if test="vo.processNumber != null and vo.processNumber != ''">
                <bind name="processNumber" value="'%' + vo.processNumber + '%'"/>
                and process_number like #{processNumber}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and years = #{vo.years}
            </if>
            order by create_time desc
        </where>
    </select>


</mapper>

