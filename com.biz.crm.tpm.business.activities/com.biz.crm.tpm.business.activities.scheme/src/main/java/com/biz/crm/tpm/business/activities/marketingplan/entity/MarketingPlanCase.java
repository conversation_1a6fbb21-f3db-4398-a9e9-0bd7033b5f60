package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 15:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_plan_case")
@Table(name = "tpm_marketing_plan_case", indexes = {
        @Index(name = "tpm_marketing_plan_case_index0", columnList = "scheme_detail_code", unique = true),
        @Index(name = "tpm_marketing_plan_case_index1", columnList = "scheme_code"),
        @Index(name = "tpm_marketing_plan_case_index2", columnList = "release_code,release_detail_code"),
        @Index(name = "tpm_marketing_plan_case_index3", columnList = "head_scheme_code,Head_scheme_detail_code"),
        @Index(name = "tpm_marketing_plan_case_index4", columnList = "years,scheme_detail_code"),
        @Index(name = "tpm_marketing_plan_case_index5", columnList = "act_execute_code"),
        @Index(name = "tpm_marketing_plan_case_index6", columnList = "original_scheme_detail_code"),

})
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_plan_case", comment = "营销方案-方案明细")
@ApiModel(value = "MarketingPlanCase", description = "营销方案-方案明细")
public class MarketingPlanCase extends UuidFlagOpEntity {

    @ApiModelProperty("方案明细类型")
    @Column(name = "case_type", nullable = false, columnDefinition = "varchar(20) comment '方案明细类型'")
    private String caseType;

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    @Column(name = "act_execute_code", columnDefinition = "varchar(32) comment '活动执行编码'")
    private String actExecuteCode;

    @ApiModelProperty("原方案明细编码")
    @Column(name = "original_scheme_detail_code", columnDefinition = "varchar(32) comment '原方案明细编码'")
    private String originalSchemeDetailCode;

    @ApiModelProperty("关联统筹方案编码")
    @Column(name = "release_code", columnDefinition = "varchar(32) comment '关联统筹方案编码'")
    private String releaseCode;

    @ApiModelProperty("关联统筹方案名称")
    @Column(name = "release_name", columnDefinition = "varchar(128) comment '关联统筹方案名称'")
    private String releaseName;

    @ApiModelProperty("关联统筹方案明细编码")
    @Column(name = "release_detail_code", columnDefinition = "varchar(32) comment '关联统筹方案明细编码'")
    private String releaseDetailCode;

    @ApiModelProperty("关联总部编码")
    @Column(name = "head_scheme_code", columnDefinition = "varchar(32) comment '关联总部编码'")
    private String headSchemeCode;

    @ApiModelProperty("关联总部明细编码")
    @Column(name = "head_scheme_detail_code", columnDefinition = "varchar(32) comment '关联总部明细编码'")
    private String headSchemeDetailCode;

    @ApiModelProperty("排序")
    @Column(name = "sort", columnDefinition = "bigint(20) comment '排序'")
    private Long sort;

    @ApiModelProperty("活动名称")
    @Column(name = "act_name", columnDefinition = "varchar(128) comment '活动名称'")
    private String actName;

    @ApiModelProperty("活动大类编码")
    @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    @Column(name = "detail_code", columnDefinition = "varchar(32) comment '活动细类编码'")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    @Column(name = "detail_name", columnDefinition = "varchar(64) comment '活动细类名称'")
    private String detailName;

    @ApiModelProperty("预算科目编码")
    @Column(name = "budget_subject_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算科目编码'")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    @Column(name = "budget_subject_name", columnDefinition = "varchar(255) COMMENT '预算科目名称'")
    private String budgetSubjectName;

    @ApiModelProperty("开始时间")
    @Column(name = "start_date", columnDefinition = "varchar(32) comment '开始时间'")
    private String startDate;

    @ApiModelProperty("结束时间")
    @Column(name = "end_date", columnDefinition = "varchar(32) comment '结束时间'")
    private String endDate;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("活动年月")
    @Column(name = "act_years", columnDefinition = "varchar(20) comment '活动年月'")
    private String actYears;

    @ApiModelProperty("归属部门编码")
    @Column(name = "belong_department_code", columnDefinition = "varchar(32) comment '归属部门编码'")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    @Column(name = "belong_department_name", columnDefinition = "varchar(64) comment '归属部门名称'")
    private String belongDepartmentName;

    @ApiModelProperty("费用归属部门成本中心")
    @Column(name = "belong_cost_center_codes", columnDefinition = "varchar(512) comment '费用归属部门成本中心编码'")
    private String belongCostCenterCodes;

    @ApiModelProperty("承担部门编码")
    @Column(name = "bear_department_code", columnDefinition = "varchar(32) comment '承担部门编码'")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    @Column(name = "bear_department_name", columnDefinition = "varchar(64) comment '承担部门名称'")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @Column(name = "cost_center_name", columnDefinition = "varchar(64) comment '成本中心名称'")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

    @ApiModelProperty("终端编码")
    @Column(name = "terminal_code", columnDefinition = "varchar(32) comment '终端编码'")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    @Column(name = "terminal_name", columnDefinition = "varchar(64) comment '终端名称'")
    private String terminalName;

    @ApiModelProperty("终端类型")
    @Column(name = "terminal_type", columnDefinition = "varchar(20) comment '终端类型'")
    private String terminalType;

    @ApiModelProperty("终端渠道")
    @Column(name = "terminal_channel", columnDefinition = "varchar(32) comment '终端渠道'")
    private String terminalChannel;

    @ApiModelProperty("终端所属系统")
    @Column(name = "terminal_system", columnDefinition = "varchar(32) comment '终端所属系统'")
    private String terminalSystem;

    @ApiModelProperty("兑付方式")
    @Column(name = "cash_type", columnDefinition = "varchar(20) comment '兑付方式'")
    private String cashType;

    @ApiModelProperty("结案金额")
    @Column(name = "audit_amount", columnDefinition = "decimal(20,6) comment '结案金额'")
    private BigDecimal auditAmount;

    @ApiModelProperty("结案状态")
    @Column(name = "audit_status", columnDefinition = "varchar(32) default 'not_audit' comment '结案状态'")
    private String auditStatus;

    @ApiModelProperty("兑付金额")
    @Column(name = "cash_amount", columnDefinition = "decimal(20,6) comment '兑付金额'")
    private BigDecimal cashAmount;

    @ApiModelProperty("兑付状态")
    @Column(name = "cash_status", columnDefinition = "varchar(32) default 'not_cash' comment '兑付状态'")
    private String cashStatus;

    @ApiModelProperty("申请金额")
    @Column(name = "apply_amount", columnDefinition = "decimal(18,4) comment '申请金额'")
    private BigDecimal applyAmount;

    @ApiModelProperty("采集执行状态 Y,N")
    @Column(name = "execute_status", columnDefinition = "varchar(2) default 'N' comment '采集执行状态'")
    private String executeStatus;

    @ApiModelProperty("是否已读")
    @Column(name = "dms_read_flag", columnDefinition = "varchar(10) comment 'DMS是否已读'")
    private String dmsReadFlag;


    @ApiModelProperty("客户搜索项")
    @Column(name = "search_name", columnDefinition = "varchar(128) comment '客户搜索项'")
    private String searchName;


    /**
     * 以下字段全部放倒 marketing_plan_case_extend表里面
     * 这里存放是为了方便从mapper里面查询出来后使用
     */
    @ApiModelProperty("执行描述")
    @Transient
    @TableField(exist = false)
    private String executeDesc;

    @ApiModelProperty("预估费用")
    @Transient
    @TableField(exist = false)
    private BigDecimal estimatedCost;

    @ApiModelProperty("预估销售额")
    @Transient
    @TableField(exist = false)
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("预估费率")
    @Transient
    @TableField(exist = false)
    private BigDecimal ratio;

    @ApiModelProperty("活动描述")
    @Transient
    @TableField(exist = false)
    private String actDesc;

    @ApiModelProperty("年度预算编码")
    @Transient
    @TableField(exist = false)
    private String budgetCode;

    @ApiModelProperty("费用依据")
    @Transient
    @TableField(exist = false)
    private String costBasis;

    @ApiModelProperty("执行示例")
    @Transient
    @TableField(exist = false)
    private String executeExample;

    @ApiModelProperty("结案示例")
    @Transient
    @TableField(exist = false)
    private String closeCaseExample;

    @ApiModelProperty("错误描述")
    @Transient
    @TableField(exist = false)
    private String errMsg;

    @ApiModelProperty("校验结果")
    @Transient
    @TableField(exist = false)
    private Boolean checkFlag;

    @ApiModelProperty("合同编码")
    @Transient
    @TableField(exist = false)
    private String contractCode;

    @ApiModelProperty("合同名称")
    @Transient
    @TableField(exist = false)
    private String contractName;

    @ApiModelProperty("是否合同费用")
    @Transient
    @TableField(exist = false)
    private String isContractCost;

    @ApiModelProperty("返利类型")
    @Transient
    @TableField(exist = false)
    private String rebateType;

    @ApiModelProperty("返利计算日期")
    @Transient
    @TableField(exist = false)
    private Integer rebateCalDay;

    @ApiModelProperty("返利开始时间")
    @Transient
    @TableField(exist = false)
    private String rebateStartDate;

    @ApiModelProperty("返利结束时间")
    @Transient
    @TableField(exist = false)
    private String rebateEndDate;

    @ApiModelProperty("政策形式编码")
    @Transient
    @TableField(exist = false)
    private String conditionFormula;

    @ApiModelProperty("政策形式名称")
    @Transient
    @TableField(exist = false)
    private String conditionFormulaName;

    @ApiModelProperty("我方承担金额")
    @Transient
    @TableField(exist = false)
    private BigDecimal bearAmount;

    @ApiModelProperty("客户承担金额")
    @Transient
    @TableField(exist = false)
    private BigDecimal cusBearAmount;

    @ApiModelProperty("行销物料类型")
    @Transient
    @TableField(exist = false)
    private String sellMaterialType;

    @ApiModelProperty("物料编码")
    @Transient
    @TableField(exist = false)
    private String materialCode;

    @ApiModelProperty("物料名称")
    @Transient
    @TableField(exist = false)
    private String materialName;

    @ApiModelProperty("物料数量")
    @Transient
    @TableField(exist = false)
    private BigDecimal materialNum;

    @ApiModelProperty("物料成本价格")
    @Transient
    @TableField(exist = false)
    private BigDecimal materialCostPrice;

    @ApiModelProperty("运输方式")
    @Transient
    @TableField(exist = false)
    private String transportType;

    @ApiModelProperty("投放平台")
    @Transient
    @TableField(exist = false)
    private String platform;

    @ApiModelProperty("投放城市")
    @Transient
    @TableField(exist = false)
    private String placingCity;

    @ApiModelProperty("投放城市名称")
    @Transient
    @TableField(exist = false)
    private String placingCityName;

    @ApiModelProperty("条件数量")
    @Transient
    @TableField(exist = false)
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    @Transient
    @TableField(exist = false)
    private String giveNum;

    @ApiModelProperty("优惠数量-上限")
    @Transient
    @TableField(exist = false)
    private BigDecimal discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    @Transient
    @TableField(exist = false)
    private BigDecimal discountAmount;

    @ApiModelProperty("税率")
    @Transient
    @TableField(exist = false)
    private BigDecimal taxRate;

    @ApiModelProperty("不含税金额")
    @Transient
    @TableField(exist = false)
    private BigDecimal noTaxApplyAmount;

    @ApiModelProperty("搭赠物料成本金额")
    @Transient
    @TableField(exist = false)
    private BigDecimal policyMaterialCostPrice;

    @ApiModelProperty("搭赠数量")
    @Transient
    @TableField(exist = false)
    private BigDecimal giftQuantity;

    @ApiModelProperty("政策商品")
    @Transient
    @TableField(exist = false)
    private String policyProductCode;

    @ApiModelProperty("政策商品物流成本")
    @Transient
    @TableField(exist = false)
    private BigDecimal policyProductTransportCost;

    @ApiModelProperty("推送状态（政策/费用池）")
    @Transient
    @TableField(exist = false)
    private String pushStatus;

    @ApiModelProperty("推送描述（政策/费用池）")
    @Transient
    @TableField(exist = false)
    private String pushMsg;

    @ApiModelProperty("多部门承担标记")
    @Transient
    @TableField(exist = false)
    private String muchDepartmentMark;

    @ApiModelProperty("是否多部门")
    @Transient
    @TableField(exist = false)
    private String muchDepartmentFlag;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("产品和品项范围-后台使用")
    private List<MarketingPlanProduct> productAndItemList;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("核算产品范围-前端使用")
    private List<MarketingPlanProduct> productList;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("核算品项范围-前端使用")
    private List<MarketingPlanProduct> itemList;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("核算产品小类范围-前端使用")
    private List<MarketingPlanProduct> levelList;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("费用产品范围-前端使用")
    private List<MarketingPlanProduct> feeProductList;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("费用品项范围-前端使用")
    private List<MarketingPlanProduct> feeItemList;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("费用产品小类范围-前端使用")
    private List<MarketingPlanProduct> feeLevelList;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("费用归属品项-前端使用")
    private List<MarketingPlanProduct> feeBelongItemList;

    @ApiModelProperty("活动材料")
    @Transient
    @TableField(exist = false)
    private List<CostTypeDetailCollectVo> collectList;

    @ApiModelProperty("核销材料")
    @Transient
    @TableField(exist = false)
    private List<CostTypeDetailCollectVo> approvalList;

    @ApiModelProperty("兑付方式")
    @Transient
    @TableField(exist = false)
    private Set<String> payBys;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("品项编码")
    private String itemCode;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("品项名称")
    private String itemName;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("dms单据号（促销政策编码/货补池编码）")
    private String dmsCode;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("是否计提")
    private String withHoldingStatus;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("完全兑付时间")
    private String wholeCashDate;

    @Transient
    @TableField(exist = false)
    private String levelProductCode;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("合作类型")
    private String cooperateType;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("合作类型")
    private String hzlx;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("陈列卡板数")
    private BigDecimal displayCardNum;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("上上月POS")
    private BigDecimal lastUpMonthPos;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("上月POS")
    private BigDecimal lastMonthPos;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("当月pos")
    private BigDecimal monthPos;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("人员类型")
    private String staffType;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("出勤天数")
    private BigDecimal attendanceDay;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("底薪")
    private BigDecimal basicSalary;

    @ApiModelProperty("行上税率")
    @Column(name = "line_tax_rate")
    private BigDecimal lineTaxRate;

    @ApiModelProperty("行上不含税金额")
    @Column(name = "line_no_tax_apply_amount")
    private BigDecimal lineNoTaxApplyAmount;




    public void setProductAndItemList(List<MarketingPlanProduct> list) {
        this.productAndItemList = list;
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, List<MarketingPlanProduct>> map = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getType()))
                    .collect(Collectors.groupingBy(MarketingPlanProduct::getType));
            for (Map.Entry<String, List<MarketingPlanProduct>> entry : map.entrySet()) {
                String key = entry.getKey();
                List<MarketingPlanProduct> values = entry.getValue();
                if (OverallPlanProductEnum.cal_product.getCode().equals(key)) {
                    this.productList = values;
                } else if (OverallPlanProductEnum.cal_item.getCode().equals(key)) {
                    this.itemList = values;
                    MarketingPlanProduct itemProduct = values.get(0);
                    if (!MarketingPlanCaseTypeEnum.back.getCode().equals(this.caseType)) {
                        this.itemCode = itemProduct.getCode();
                        this.itemName = itemProduct.getName();
                    }
                } else if (OverallPlanProductEnum.cal_level.getCode().equals(key)) {
                    this.levelList = values;
                } else if (OverallPlanProductEnum.fee_product.getCode().equals(key)) {
                    this.feeProductList = values;
                } else if (OverallPlanProductEnum.fee_item.getCode().equals(key)) {
                    this.feeItemList = values;
                } else if (OverallPlanProductEnum.fee_level.getCode().equals(key)) {
                    this.feeLevelList = values;
                } else if (OverallPlanProductEnum.fee_belong_item.getCode().equals(key)) {
                    this.feeBelongItemList = values;
                    MarketingPlanProduct itemProduct = values.get(0);
                    if (MarketingPlanCaseTypeEnum.back.getCode().equals(this.caseType)) {
                        this.itemCode = itemProduct.getCode();
                        this.itemName = itemProduct.getName();
                    }
                }
            }
        }
    }

}
