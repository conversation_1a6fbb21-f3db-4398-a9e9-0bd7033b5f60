package com.biz.crm.tpm.business.activities.materialdemand.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/3 14:59
 */
@Data
@ApiModel(value = "物资需求跟踪vo")
public class MaterialDemandReportVo {

    @ApiModelProperty("主键ID(orgCode:materialCode)")
    private String id;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("部门编码")
    private String orgCode;

    @ApiModelProperty("部门名称")
    private String orgName;

    @ApiModelProperty("物料数量")
    private BigDecimal materialNum;

    @ApiModelProperty("物料金额")
    private BigDecimal materialAmount;

    @ApiModelProperty("使用数量")
    private BigDecimal usedMaterialNum;

    @ApiModelProperty("使用金额")
    private BigDecimal usedMaterialAmount;

    @ApiModelProperty("可使用数量")
    private BigDecimal availMaterialNum;

    @ApiModelProperty("可使用金额")
    private BigDecimal availMaterialAmount;
}
