package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectStatusEnum;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.analysis.function.Abs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Component
@Slf4j
public class RegionCollectCal {

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private RegionCollectService regionCollectService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Resource
    private RedisService redisService;


    @Async("tpmRegionCollectThread")
    public void modifyRegionCollect(String id, String lockKey, String userName) {
        Boolean locked = redisLockService.tryLock(lockKey, TimeUnit.HOURS, 1);
        if (!locked) {
            log.info("大区汇总加锁失败===>" + id);
            return;
        }
        try {
            RegionCollectCal bean = ApplicationContextHolder.getContext().getBean(RegionCollectCal.class);
            bean.execute(id, userName);
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
        }
    }


    public void execute(String id, String userName) {
        //更新状态
        String msg = null;
        String status = null;
        try {
            msg = "大区汇总执行完成，执行人[" + userName + "]";
            loginUserService.refreshAuthentication(null);
            regionCollectService.modifyCollect(id);
            status = RegionCollectStatusEnum.success.getCode();
        } catch (IllegalArgumentException i) {
            log.error(id + "大区汇总执行失败{}", i.getMessage(), i);
            msg = "大区汇总执行失败，执行人[" + userName + "]" + i.getMessage();
            status = RegionCollectStatusEnum.error.getCode();
        } catch (Exception e) {
            log.error(id + "大区汇总执行失败{}", e.getMessage(), e);
            msg = "大区汇总执行失败，执行人[" + userName + "],失败原因:" + e.getMessage();
            status = RegionCollectStatusEnum.error.getCode();
        }
        regionCollectService.updateExecuteMsg(id, msg, status);
    }


    @Async("tpmRegionCollectThread")
    public void modifyOTwoORegionCollect(String id, String lockKey, String userName) {
        Boolean locked = redisLockService.tryLock(lockKey, TimeUnit.HOURS, 1);
        if (!locked) {
            log.info("大区汇总加锁失败===>" + id);
            return;
        }
        try {
            RegionCollectCal bean = ApplicationContextHolder.getContext().getBean(RegionCollectCal.class);
            bean.executeOTwo(id, userName);
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
        }
    }


    public void executeOTwo(String id, String userName) {
        //更新状态
        String msg = null;
        String status = null;
        try {
            msg = "大区汇总执行完成，执行人[" + userName + "]";
            loginUserService.refreshAuthentication(null);
            regionCollectService.modifyOTwoOCollect(id);
            status = RegionCollectStatusEnum.success.getCode();
        } catch (IllegalArgumentException i) {
            log.error(id + "大区汇总执行失败{}", i.getMessage(), i);
            msg = "大区汇总执行失败，执行人[" + userName + "]" + i.getMessage();
            status = RegionCollectStatusEnum.error.getCode();
        } catch (Exception e) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            e.printStackTrace(new PrintStream(baos));
            String exception = baos.toString();
            log.error("大区汇总执行失败错误日志:{},错误堆栈:{}", e.getMessage(), exception);
            msg = "大区汇总执行失败，执行人[" + userName + "],失败原因:" + e.getMessage();
            status = RegionCollectStatusEnum.error.getCode();
        }
        regionCollectService.updateExecuteMsg(id, msg, status);
    }


    /**
     * 营销方案变更审批通过后，更新大区汇总营销测算、客户损益、品相、部门
     *
     * @param orgCode
     */
    @Async("tpmRegionCollectThread")
    public void marketingChangeUpdateRegionCollect(String orgCode, String years) {
        log.info("营销方案变更审批通过后，更新大区汇总营销测算、客户损益、品相、部门");
        regionCollectService.marketingChangeUpdateRegionCollect(orgCode, years);
    }


}
