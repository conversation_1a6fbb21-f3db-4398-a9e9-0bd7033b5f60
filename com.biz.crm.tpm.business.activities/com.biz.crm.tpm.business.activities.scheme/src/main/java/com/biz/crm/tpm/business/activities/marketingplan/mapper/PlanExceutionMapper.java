package com.biz.crm.tpm.business.activities.marketingplan.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanExecution;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanExecutionDto;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 执行计划(PlanExceution)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-07-02 20:06:33
 */
public interface PlanExceutionMapper extends BaseMapper<PlanExecution> {

    /**
     * 活动执行采集分页
     *
     * @param page
     * @param dto
     * @return
     */
    Page<PlanExecutionVo> findByConditions(@Param("page") Page<PlanExecutionVo> page, @Param("dto") PlanExecutionDto dto);
}

