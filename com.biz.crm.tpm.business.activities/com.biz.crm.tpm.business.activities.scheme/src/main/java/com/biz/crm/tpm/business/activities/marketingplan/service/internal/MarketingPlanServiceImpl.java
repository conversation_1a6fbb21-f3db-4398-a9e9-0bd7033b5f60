package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterOrgVo;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.constant.MarketingPlanConstant;
import com.biz.crm.tpm.business.activities.marketingplan.dto.MarketingPlanLogEventDto;
import com.biz.crm.tpm.business.activities.marketingplan.entity.*;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanSchemeStatusEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.event.MarketingPlanLogEventListener;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingCheckCaseComponent;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanMapper;
import com.biz.crm.tpm.business.activities.marketingplan.repository.*;
import com.biz.crm.tpm.business.activities.marketingplan.service.*;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCustomerGainsAbstractBuilder;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingSchemeEstimationAbstractBuilder;
import com.biz.crm.tpm.business.activities.marketingplan.vo.*;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.repository.OverPlanCaseRepository;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanService;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.regioncollect.helper.RegionCollectHelper;
import com.biz.crm.tpm.business.activities.regioncollect.service.*;
import com.biz.crm.tpm.business.activities.regioncollect.service.strategy.ItemEstimationAbstractBuilder;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo;
import com.biz.crm.tpm.business.activities.schemefile.service.SchemeCaseFilesService;
import com.biz.crm.tpm.business.activities.schemefile.vo.SchemeCaseFilesVo;
import com.biz.crm.tpm.business.activities.stagingscheme.entity.TpmStagingSalesPlanEntity;
import com.biz.crm.tpm.business.activities.stagingscheme.repository.TpmStagingSalesPlanRepository;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.activities.stagingscheme.vo.TpmStagingSchemeVo;
import com.biz.crm.tpm.business.budget.sdk.service.MarketingAuditComponentService;
import com.biz.crm.tpm.business.control.sdk.enums.ControlMethodEnum;
import com.biz.crm.tpm.business.track.sdk.service.BudgetTrackService;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 17:34
 */
@Service("marketingPlanServiceImpl")
@Slf4j
public class MarketingPlanServiceImpl extends BusinessPageCacheServiceImpl<MarketingPlanCaseVo, MarketingPlanCaseVo> implements MarketingPlanService {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private NebulaNetEventClient nebulaNetEventClient;

    @Resource
    private MarketingPlanRepository marketingPlanRepository;

    @Resource
    private MarketingPlanProductRepository productRepository;

    @Resource
    private LoginUserService loginUserService;

    @Autowired
    private ITpmStagingSchemeService tpmStagingSchemeService;

    @Resource
    private RedisService redisService;

    @Resource
    private GenerateCodeService generateCodeService;

    @Autowired
    private MarketingPlanCaseService planCaseService;

    @Autowired
    private MarketingPlanGainsAndLossesService gainsAndLossesService;

    @Autowired
    private MarketingPlanChangeLogService changeLogService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ApplicationContext context;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private RegionCollectHelper regionCollectHelper;

    @Autowired
    private MarketingPlanEstimationService marketingPlanEstimationService;

    @Autowired(required = false)
    private MarketingPlanSchemeEstimationService marketingPlanSchemeEstimationService;

    @Autowired(required = false)
    private MarketingPlanOaService marketingPlanOaService;

    @Autowired(required = false)
    private MarketingPlanAppendOaService marketingPlanAppendOaService;

    @Autowired(required = false)
    private MarketingPlanSupplementOaService marketingPlanSupplementOaService;

    @Resource
    private MarketingChangeSchemeComponent changeSchemeComponent;

    @Autowired
    private RegionCollectSchemeService regionCollectSchemeService;

    @Autowired(required = false)
    private TpmMarketingPlanCaseComponent tpmMarketingPlanCaseComponent;

    @Resource
    private MarketingPlanChangeSchemeCaseRepository planChangeSchemeCaseRepository;

    @Resource
    private MarketingContractCalComponent contractCalComponent;

    @Autowired(required = false)
    private MarketingAuditComponentService marketingAuditComponentService;

    @Resource
    private RegionCollectControlBudgetComponent regionCollectControlBudgetComponent;

    @Resource
    private MarketingPlanBudgetService marketingPlanBudgetService;

    @Autowired
    private MarketingSalesPlanService marketingSalesPlanService;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;

    @Autowired(required = false)
    private RegionCollectOaService regionCollectOaService;

    @Resource
    private MarketingPlanCaseRepository caseRepository;

    @Autowired
    private RegionCollectService regionCollectService;

    @Autowired
    private MarketingPlanBackGainsAndLossesService backGainsAndLossesService;

    @Autowired(required = false)
    private SchemeCaseFilesService schemeCaseFilesService;

    @Autowired
    private OverPlanCaseRepository overPlanCaseRepository;

    @Autowired
    private TpmStagingSalesPlanRepository tpmStagingSalesPlanRepository;

    @Autowired
    private MarketingPlanItemEstimationService marketingPlanItemEstimationService;

    @Autowired(required = false)
    BudgetTrackService budgetTrackService;

    @Autowired(required = false)
    MarketingPlanCaseService marketingPlanCaseService;

    @Autowired(required = false)
    OverallPlanService  overallPlanService;

    /**
     * 分页查询
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MarketingPlanVo> findList(Pageable pageable, MarketingPlanVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MarketingPlanVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return marketingPlanRepository.findList(page, vo);
    }


    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Override
    public MarketingPlanVo queryDetails(String id, String schemeCode) {
        Validate.isTrue(ObjectUtils.isNotEmpty(id) || ObjectUtils.isNotEmpty(schemeCode), "主键ID和方案编码不能都为空");
        MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(id, schemeCode);
        Validate.notNull(entity, "查询数据不存在，或已删除");
        MarketingPlanVo vo = nebulaToolkitService.copyObjectByWhiteList(entity, MarketingPlanVo.class, HashSet.class, ArrayList.class);
        List<String> schemeCodes = Lists.newArrayList(vo.getSchemeCode());
        //查询方案明细表单类型
        Set<String> caseTypeSet = caseRepository.findCaseTypeBySchemeCode(vo.getSchemeCode());
        vo.setTemplateList(caseTypeSet);
        //客户损益预测
        List<MarketingPlanGainsAndLosses> gainsAndLossesList = gainsAndLossesService.findListBySchemeCodes(schemeCodes);
        if (CollectionUtils.isNotEmpty(gainsAndLossesList)) {
            List<MarketingPlanGainsAndLossesVo> customerGainAndLossVo = (List<MarketingPlanGainsAndLossesVo>) nebulaToolkitService.copyCollectionByWhiteList(gainsAndLossesList, MarketingPlanGainsAndLosses.class, MarketingPlanGainsAndLossesVo.class, HashSet.class, ArrayList.class, "secondCostCategoryMap");
            buildChannelType(customerGainAndLossVo);
            vo.setGainsAndLossesList(customerGainAndLossVo);
        }
        //营销测算
        List<MarketingPlanEstimationVo> dataList = marketingPlanEstimationService.findListBySchemeCode(Lists.newArrayList(vo.getSchemeCode()));
        vo.setMarketingEstimationList(dataList);
        //判断是变更方案
        if (ObjectUtils.isNotEmpty(vo.getOriginalSchemeCode())) {
            //损益对比
            List<MarketPlanChangeGainsAndLossesVo> gainsAndLossesComparsionList = this.findChangeGainsAndLosses(vo.getSchemeCode());
            vo.setGainsAndLossesComparsionList(gainsAndLossesComparsionList);
            if (ObjectUtils.isNotEmpty(entity.getCompareChangeGainsAndLosses())) {
                vo.setCompareChangeGainsAndLossesVo(JSONObject.parseObject(entity.getCompareChangeGainsAndLosses(), MarketingChangeGainsAndLossesCompareVo.class));
            }
        } else {
            //单方案-营销测算
            List<MarketingPlanSchemeEstimationVo> marketingSchemeEstimationList = marketingPlanSchemeEstimationService.findListBySchemeCode(Lists.newArrayList(vo.getSchemeCode()));
            vo.setMarketingSchemeEstimationList(marketingSchemeEstimationList);
        }
        //查询预算
        List<MarketingPlanBudgetVo> planBudgetVos = marketingPlanBudgetService.findListBySchemeCode(vo.getSchemeCode());
        if (CollectionUtils.isNotEmpty(planBudgetVos)) {
            Map<String, BigDecimal> budgetMap = planBudgetVos.stream().collect(Collectors.groupingBy(x -> x.getTrackCode(), Collectors.mapping(x -> x.getApplyAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            List<MarketingPlanBudgetVo> newBudgetList = planBudgetVos.stream().collect(Collectors.toMap(x -> x.getTrackCode(), Function.identity(), (v1, v2) -> v1)).values().stream().collect(Collectors.toList());
            for (MarketingPlanBudgetVo budget : newBudgetList) {
                BigDecimal applyAmount = budgetMap.getOrDefault(budget.getTrackCode(), BigDecimal.ZERO);
                budget.setApplyAmount(applyAmount);
                budget.setUsedAmount(applyAmount);
            }
            vo.setBudgetList(newBudgetList);
        }
        //查询品相测算
        List<MarketingPlanItemEstimationVo> itemEstimationVoList = marketingPlanItemEstimationService.findListBySchemeCode(vo.getSchemeCode());
        vo.setItemEstimationList(itemEstimationVoList);
        List<SchemeCaseFilesVo> filesVoList = schemeCaseFilesService.findFilesList(vo.getSchemeCode(), OverallPlanSchemeTypeEnum.MARKETING.getCode());
        if (CollectionUtils.isNotEmpty(filesVoList)) {
            vo.setFilesList(filesVoList);
        }
        return vo;
    }

    private void buildChannelType(List<MarketingPlanGainsAndLossesVo> customerGainAndLossVo) {
        List<String> customerCodes = customerGainAndLossVo.stream().map(MarketingPlanGainsAndLossesVo::getCustomerCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, String> customerMap = customerVos.stream().filter(e -> StringUtils.isNotEmpty(e.getCustomerCode()) && Objects.nonNull(e.getChannelType())).collect(Collectors.toMap(CustomerVo::getCustomerCode, CustomerVo::getChannelType, (v1, v2) -> v1));
        customerGainAndLossVo.forEach(e -> {
            String customerCode = e.getCustomerCode();
            String channelTypeStr = customerMap.get(customerCode);
            if (StringUtils.isNotEmpty(channelTypeStr)) {
                List<String> channleTypeList = Arrays.asList(channelTypeStr.split(","));
                e.setChannelTypeList(channleTypeList);
            }
        });
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public MarketingPlanVo createPlan(MarketingPlanVo vo, Boolean checkFlag, Boolean changeFlag) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        // caseType(对应表单类型，如随单搭赠，进货返利，周边物料等), 明细
        Map<String, List<MarketingPlanCaseVo>> caseDetailMap = getCacheListToMap(vo.getCacheKey());
        log.info("createPlan.caseDetailMap: {}", JSON.toJSONString(caseDetailMap));
        vo.setDetailCaseMap(caseDetailMap);
        //拼接销售计划的cacheKey
        String salesPlanCacheKey = vo.getCacheKey() + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + vo.getYears();
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findCacheList(salesPlanCacheKey);
        vo.setSalesPlanList(salesPlanVoList);
        //参数校验
        this.validateParam(vo, checkFlag, changeFlag);
        FacturerUserDetails login = loginUserService.getLoginDetails(FacturerUserDetails.class);
        vo.setOrgCode(login.getOrgCode());
        vo.setOrgName(login.getOrgName());
        vo.setPositionCode(login.getPostCode());
        vo.setPositionName(login.getPostName());
        String schemeCode = null;
        if (changeFlag) {
            schemeCode = generateCodeService.generateCodeNotDate(vo.getOriginalSchemeCode() + "-", 1, 2).get(0);
        } else {
            schemeCode = generateCodeService.generateCode(MarketingPlanConstant.SCHEME_CODE_RULE);
        }
        vo.setSchemeCode(schemeCode);
        vo.setTenantCode(TenantUtils.getTenantCode());
        vo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        vo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        vo.setConfirmStatus(BooleanEnum.FALSE.getCapital());
        if (checkFlag) {
            vo.setConfirmStatus(BooleanEnum.TRUE.getCapital());
        }
        vo.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
        vo.setSchemeStatus(MarketingPlanSchemeStatusEnum.TO_BE_CONFIRMED.getCode());
        vo.setProcessKey(null);
        vo.setProcessNumber(null);
        vo.setProcessDate(null);
        vo.setProcessRemark(null);
        vo.setOaId(null);
        vo.setOaUserName(null);
        MarketingPlan entity = nebulaToolkitService.copyObjectByBlankList(vo, MarketingPlan.class, HashSet.class, ArrayList.class);
        entity.setCreateName(null);
        entity.setCreateTime(null);
        entity.setCreateAccount(null);
        marketingPlanRepository.save(entity);
        vo.setId(entity.getId());
        //存放明细表
        List<MarketingPlanCaseVo> planCaseVos = vo.getDetailCaseMap().values().stream().flatMap(List::stream).collect(Collectors.toList());
        String changeFlagStr = null;
        if (changeFlag) {
            changeFlagStr = BooleanEnum.TRUE.getCapital();
            planCaseService.saveBatchChangeCaseList(planCaseVos, schemeCode);
        } else {
            changeFlagStr = BooleanEnum.FALSE.getCapital();
            planCaseService.saveBatchCaseList(planCaseVos, schemeCode, changeFlag);
        }

        String schemeType = vo.getSchemeType();
        // 首次初始化原始预估销售金额(未税)
        List<MarketingSalesPlanVo> salesPlanList = vo.getSalesPlanList();
        if (StringUtils.equals("o_two_o", schemeType) || StringUtils.equals("plan", schemeType) || StringUtils.equals("append", schemeType) || StringUtils.equals("contract", schemeType) || StringUtils.equals("additional", schemeType)) {
            for (MarketingSalesPlanVo marketingSalesPlanVo : salesPlanList) {
                if (Objects.isNull(marketingSalesPlanVo.getOriginEstimatedCost())) {
                    marketingSalesPlanVo.setOriginEstimatedCost(marketingSalesPlanVo.getEstimatedCost());
                }
            }
        }
        //存放销售计划
        List<MarketingSalesPlanVo> marketingSalesPlanVos = marketingSalesPlanService.saveBatchSalesPlan(vo.getSalesPlanList(), schemeCode, checkFlag, changeFlagStr);
        //存放文件
        schemeCaseFilesService.saveOrUpdateFiles(vo.getFilesList(), entity.getSchemeCode(), OverallPlanSchemeTypeEnum.MARKETING.getCode());

        //日志处理
        MarketingPlanLogEventDto dto = new MarketingPlanLogEventDto();
        dto.setNewest(vo);
        SerializableBiConsumer<MarketingPlanLogEventListener, MarketingPlanLogEventDto> consumer = MarketingPlanLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, MarketingPlanLogEventListener.class, consumer);
        //判断如果是变更
        if (changeFlag) {
            MarketingPlanChangeLogVo logVo = nebulaToolkitService.copyObjectByWhiteList(vo, MarketingPlanChangeLogVo.class, HashSet.class, ArrayList.class);
            //查询选择需要变更的数据
            MarketingPlanVo originVo = queryDetails(null, vo.getOriginalSchemeCode());
            logVo.setOriginalVo(originVo);
            logVo.setNewestVo(vo);
            changeLogService.saveOrUpdateLog(logVo);
            //非变更，保存销售计划到销售计划暂存表
        } else {
            if (CollectionUtils.isNotEmpty(marketingSalesPlanVos)) {
                TpmStagingSalesPlanEntity stagingSalesPlanEntity = new TpmStagingSalesPlanEntity();
                stagingSalesPlanEntity.setReleaseId(schemeCode);
                stagingSalesPlanEntity.setJsonStr(JSON.toJSONString(marketingSalesPlanVos));
                tpmStagingSalesPlanRepository.save(stagingSalesPlanEntity);
            }
        }
        //如果是规划类型的 则直接进行客户损益测算
        if (StringUtils.equalsAny(entity.getSchemeType(), MarketingPlanSchemeTypeEnum.plan.getCode(), MarketingPlanSchemeTypeEnum.additional.getCode()) && checkFlag) {
            this.calCustomerGains(entity.getSchemeCode());
            if (MarketingPlanSchemeTypeEnum.plan.getCode().equals(entity.getSchemeType())) {
                //更新单方案-营销测算
                this.calMarketingPlanSchemeEstimation(entity.getSchemeCode());
            }
        }

        return buildReturnParam(entity, vo);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public MarketingPlanVo updatePlan(MarketingPlanVo vo, Boolean checkFlag, Boolean changeFlag) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        Map<String, List<MarketingPlanCaseVo>> caseDetailMap = getCacheListToMap(vo.getCacheKey());
        vo.setDetailCaseMap(caseDetailMap);
        //拼接销售计划的cacheKey
        String salesPlanCacheKey = vo.getCacheKey() + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + vo.getYears();
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findCacheList(salesPlanCacheKey);
        vo.setSalesPlanList(salesPlanVoList);
        //参数校验
        this.validateParam(vo, checkFlag, changeFlag);
        Validate.notNull(vo.getId(), "主键ID不能为空");
        MarketingPlanVo oldVo = queryDetails(vo.getId(), null);
        MarketingPlan entity = marketingPlanRepository.queryById(vo.getId());
        Validate.notNull(entity, "查询数据不存在，或已删除");
        //如果不是方案变更的就需要校验
        if (!changeFlag) {
            Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(entity.getChangedFlag()), String.format("方案%s是变更方案,不可使用此方法", entity.getSchemeCode()));
        } else {
            //是方案变更，但是此数据是规划案
            Validate.isTrue(BooleanEnum.TRUE.getCapital().equals(entity.getChangedFlag()), String.format("方案%s是规划方案,不可使用此方法", entity.getSchemeCode()));
        }
        if (MarketingPlanSchemeTypeEnum.plan.getCode().equals(entity.getSchemeType())) {
            Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(entity.getConfirmStatus()), "方案已确认不可编辑");
        }
        Validate.isTrue(!StringUtils.equalsAny(entity.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode()), "审批中/审批通过的不允许编辑");
        String schemeCode = entity.getSchemeCode();
        String id = entity.getId();
        vo.setSchemeCode(schemeCode);
        vo.setId(id);
        if (StringUtils.isEmpty(vo.getProcessStatus())) {
            vo.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
        }
        if (checkFlag) {
            vo.setConfirmStatus(BooleanEnum.TRUE.getCapital());
        } else {
            vo.setConfirmStatus(BooleanEnum.FALSE.getCapital());
        }
        entity = nebulaToolkitService.copyObjectByWhiteList(vo, MarketingPlan.class, HashSet.class, ArrayList.class);
        entity.setModifyName(null);
        entity.setModifyAccount(null);
        entity.setModifyTime(null);
        marketingPlanRepository.updateById(entity);
        //存放明细表
        List<MarketingPlanCaseVo> planCaseVos = vo.getDetailCaseMap().values().stream().flatMap(List::stream).collect(Collectors.toList());
        String changeFlagStr = null;
        if (changeFlag) {
            changeFlagStr = BooleanEnum.TRUE.getCapital();
            planCaseService.saveBatchChangeCaseList(planCaseVos, schemeCode);
        } else {
            changeFlagStr = BooleanEnum.FALSE.getCapital();
            planCaseService.saveBatchCaseList(planCaseVos, schemeCode, changeFlag);
        }
        //存放销售计划
        List<MarketingSalesPlanVo> marketingSalesPlanVos = marketingSalesPlanService.saveBatchSalesPlan(vo.getSalesPlanList(), schemeCode, checkFlag, changeFlagStr);
        //存放客户损益
//        gainsAndLossesService.saveBatchList(vo.getGainsAndLossesList(), schemeCode);

        //存放文件
        schemeCaseFilesService.saveOrUpdateFiles(vo.getFilesList(), entity.getSchemeCode(), OverallPlanSchemeTypeEnum.MARKETING.getCode());

        //日志处理
        MarketingPlanLogEventDto dto = new MarketingPlanLogEventDto();
        vo.setDetailCaseMap(null);
        vo.setTemplateList(null);
        dto.setNewest(vo);
        oldVo.setDetailCaseMap(null);
        oldVo.setTemplateList(null);
        dto.setOriginal(oldVo);
        SerializableBiConsumer<MarketingPlanLogEventListener, MarketingPlanLogEventDto> consumer = MarketingPlanLogEventListener::onUpdate;
        nebulaNetEventClient.publish(dto, MarketingPlanLogEventListener.class, consumer);
        //判断如果是变更
        if (changeFlag) {
            MarketingPlanChangeLogVo logVo = nebulaToolkitService.copyObjectByWhiteList(vo, MarketingPlanChangeLogVo.class, HashSet.class, ArrayList.class);
            //查询选择需要变更的数据
            MarketingPlanVo originVo = queryDetails(null, vo.getOriginalSchemeCode());
            logVo.setOriginalVo(originVo);
            logVo.setNewestVo(vo);
            changeLogService.saveOrUpdateLog(logVo);
            //非变更，保存销售计划到销售计划暂存表
        } else {
            tpmStagingSalesPlanRepository.removeStagingSalesPlan(schemeCode);
            if (CollectionUtils.isNotEmpty(marketingSalesPlanVos)) {
                TpmStagingSalesPlanEntity stagingSalesPlanEntity = new TpmStagingSalesPlanEntity();
                stagingSalesPlanEntity.setReleaseId(schemeCode);
                stagingSalesPlanEntity.setJsonStr(JSON.toJSONString(marketingSalesPlanVos));
                tpmStagingSalesPlanRepository.save(stagingSalesPlanEntity);
            }
        }
        //如果是规划类型的 则直接进行客户损益测算
        if (StringUtils.equalsAny(entity.getSchemeType(), MarketingPlanSchemeTypeEnum.plan.getCode(), MarketingPlanSchemeTypeEnum.additional.getCode()) && checkFlag) {
            this.calCustomerGains(entity.getSchemeCode());
            if (MarketingPlanSchemeTypeEnum.plan.getCode().equals(entity.getSchemeType())) {
                //更新单方案-营销测算
                this.calMarketingPlanSchemeEstimation(entity.getSchemeCode());
            }
        }

        return buildReturnParam(entity, vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MarketingPlanVo submitCreate(MarketingPlanVo vo, Boolean changeFlag) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        Map<String, List<MarketingPlanCaseVo>> caseDetailMap = getCacheListToMap(vo.getCacheKey());
        vo.setDetailCaseMap(caseDetailMap);
        //拼接销售计划的cacheKey
        String salesPlanCacheKey = vo.getCacheKey() + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + vo.getYears();
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findCacheList(salesPlanCacheKey);
        vo.setSalesPlanList(salesPlanVoList);
        //参数校验
        this.validateParam(vo, Boolean.TRUE, changeFlag);
        FacturerUserDetails login = loginUserService.getLoginDetails(FacturerUserDetails.class);
        vo.setOrgCode(login.getOrgCode());
        vo.setOrgName(login.getOrgName());
        vo.setPositionCode(login.getPostCode());
        vo.setPositionName(login.getPostName());
        String schemeCode = null;
        if (changeFlag) {
            schemeCode = generateCodeService.generateCodeNotDate(vo.getOriginalSchemeCode() + "-", 1, 2).get(0);
        } else {
            schemeCode = generateCodeService.generateCode(MarketingPlanConstant.SCHEME_CODE_RULE);
        }
        vo.setSchemeCode(schemeCode);
        vo.setTenantCode(TenantUtils.getTenantCode());
        vo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        vo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        vo.setConfirmStatus(BooleanEnum.TRUE.getCapital());
        vo.setSchemeStatus(MarketingPlanSchemeStatusEnum.COMMIT.getCode());
        vo.setChangedFlag(BooleanEnum.FALSE.getCapital());
        if (StringUtils.isEmpty(vo.getProcessStatus())) {
            vo.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
        }
        vo.setProcessKey(null);
        vo.setProcessNumber(null);
        vo.setProcessDate(null);
        vo.setProcessRemark(null);
        vo.setOaId(null);
        vo.setOaUserName(null);
        MarketingPlan entity = nebulaToolkitService.copyObjectByBlankList(vo, MarketingPlan.class, HashSet.class, ArrayList.class);
        entity.setCreateName(null);
        entity.setCreateTime(null);
        entity.setCreateAccount(null);
        marketingPlanRepository.save(entity);
        vo.setId(entity.getId());
        //存放明细表
        List<MarketingPlanCaseVo> planCaseVos = vo.getDetailCaseMap().values().stream().flatMap(List::stream).collect(Collectors.toList());
        String changeFlagStr = null;
        if (changeFlag) {
            changeFlagStr = BooleanEnum.TRUE.getCapital();
            planCaseService.saveBatchChangeCaseList(planCaseVos, schemeCode);
        } else {
            changeFlagStr = BooleanEnum.FALSE.getCapital();
            planCaseService.saveBatchCaseList(planCaseVos, schemeCode, changeFlag);
        }
        //存放销售计划
        marketingSalesPlanService.saveBatchSalesPlan(vo.getSalesPlanList(), schemeCode, Boolean.TRUE, changeFlagStr);

        //存放文件
        schemeCaseFilesService.saveOrUpdateFiles(vo.getFilesList(), entity.getSchemeCode(), OverallPlanSchemeTypeEnum.MARKETING.getCode());

        //如果是规划类型的 则直接进行客户损益测算
        if (StringUtils.equalsAny(entity.getSchemeType(), MarketingPlanSchemeTypeEnum.plan.getCode(), MarketingPlanSchemeTypeEnum.additional.getCode())) {
            this.calCustomerGains(entity.getSchemeCode());
            if (MarketingPlanSchemeTypeEnum.plan.getCode().equals(entity.getSchemeType())) {
                //更新单方案-营销测算
                this.calMarketingPlanSchemeEstimation(entity.getSchemeCode());
            }
        }

        //OA接口 此处会回写审批状态
        if (changeFlag) {
            marketingPlanOaService.pushOa(vo);
        } else {
            marketingPlanAppendOaService.pushOa(vo);
        }

        //日志处理
        MarketingPlanLogEventDto dto = new MarketingPlanLogEventDto();
        dto.setNewest(vo);
        SerializableBiConsumer<MarketingPlanLogEventListener, MarketingPlanLogEventDto> consumer = MarketingPlanLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, MarketingPlanLogEventListener.class, consumer);
        //判断如果是变更
        if (changeFlag) {
            MarketingPlanChangeLogVo logVo = nebulaToolkitService.copyObjectByWhiteList(vo, MarketingPlanChangeLogVo.class, HashSet.class, ArrayList.class);
            //查询选择需要变更的数据
            MarketingPlanVo originVo = queryDetails(null, vo.getOriginalSchemeCode());
            logVo.setOriginalVo(originVo);
            logVo.setNewestVo(vo);
            changeLogService.saveOrUpdateLog(logVo);
        }
        return buildReturnParam(entity, vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MarketingPlanVo submitUpdate(MarketingPlanVo vo, Boolean changeFlag) {
        Validate.notNull(vo.getId(), "主键ID不能为空");
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        Map<String, List<MarketingPlanCaseVo>> caseDetailMap = getCacheListToMap(vo.getCacheKey());
        vo.setDetailCaseMap(caseDetailMap);
        //拼接销售计划的cacheKey
        String salesPlanCacheKey = vo.getCacheKey() + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + vo.getYears();
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findCacheList(salesPlanCacheKey);
        vo.setSalesPlanList(salesPlanVoList);
        //参数校验
        this.validateParam(vo, Boolean.TRUE, changeFlag);

        MarketingPlanVo oldVo = queryDetails(vo.getId(), null);
        MarketingPlan entity = marketingPlanRepository.queryById(vo.getId());
        Validate.notNull(entity, "查询数据不存在，或已删除");
        //如果不是方案变更的就需要校验
        if (!changeFlag) {
            Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(entity.getChangedFlag()), String.format("方案%s是变更方案,不可使用此方法", entity.getSchemeCode()));
        } else {
            //是方案变更，但是此数据是规划案
            Validate.isTrue(BooleanEnum.TRUE.getCapital().equals(entity.getChangedFlag()), String.format("方案%s是规划方案,不可使用此方法", entity.getSchemeCode()));
        }
        String schemeCode = entity.getSchemeCode();
        String id = entity.getId();
        Validate.isTrue(StringUtils.equalsAny(entity.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(), ProcessStatusEnum.RECOVER.getDictCode()), "审批中/审批通过的不允许编辑");
        vo.setSchemeCode(schemeCode);
        vo.setId(id);
        vo.setConfirmStatus(BooleanEnum.TRUE.getCapital());
        //TODO 此处需要提交到OA
        entity = nebulaToolkitService.copyObjectByWhiteList(vo, MarketingPlan.class, HashSet.class, ArrayList.class);
        entity.setSchemeStatus(MarketingPlanSchemeStatusEnum.COMMIT.getCode());
        entity.setModifyName(null);
        entity.setModifyAccount(null);
        entity.setModifyTime(null);
        //判断是变更方案
        if (MarketingPlanSchemeTypeEnum.change.getCode().equals(entity.getSchemeType())) {
            String jsonStr = (String) redisService.get(MarketingPlanConstant.getMarketingChangeSchemeLock(entity.getSchemeCode()));
            entity.setCompareChangeGainsAndLosses(jsonStr);
        }
        marketingPlanRepository.updateById(entity);
        //存放明细表
        List<MarketingPlanCaseVo> planCaseVos = vo.getDetailCaseMap().values().stream().flatMap(List::stream).collect(Collectors.toList());
        String changeFlagStr = null;
        if (changeFlag) {
            changeFlagStr = BooleanEnum.TRUE.getCapital();
            planCaseService.saveBatchChangeCaseList(planCaseVos, schemeCode);
        } else {
            changeFlagStr = BooleanEnum.FALSE.getCapital();
            planCaseService.saveBatchCaseList(planCaseVos, schemeCode, changeFlag);
        }

        //存放销售计划
        marketingSalesPlanService.saveBatchSalesPlan(vo.getSalesPlanList(), schemeCode, Boolean.TRUE, changeFlagStr);

        //存放文件
        schemeCaseFilesService.saveOrUpdateFiles(vo.getFilesList(), entity.getSchemeCode(), OverallPlanSchemeTypeEnum.MARKETING.getCode());

        //如果是规划类型的 则直接进行客户损益测算
        if (StringUtils.equalsAny(entity.getSchemeType(), MarketingPlanSchemeTypeEnum.plan.getCode(), MarketingPlanSchemeTypeEnum.additional.getCode())) {
            this.calCustomerGains(entity.getSchemeCode());
            if (MarketingPlanSchemeTypeEnum.plan.getCode().equals(entity.getSchemeType())) {
                //更新单方案-营销测算
                this.calMarketingPlanSchemeEstimation(entity.getSchemeCode());
            }
        }

        //OA接口
        if (ProcessStatusEnum.PREPARE.getDictCode().equals(entity.getProcessStatus())) {
            if (changeFlag) {
                marketingPlanOaService.pushOa(vo);
            } else {
                marketingPlanAppendOaService.pushOa(vo);
            }
        } else {
            if (changeFlag) {
                marketingPlanOaService.resubmitOa(vo);
            } else {
                marketingPlanAppendOaService.resubmitOa(vo);
            }
        }

        //删除缓存
        String redisKey = MarketingPlanConstant.getRedisKey(id);
        redisService.del(redisKey);
        //放入临时表
        TpmStagingSchemeVo stagingSchemeVo = new TpmStagingSchemeVo() {{
            this.setFromType(MarketingPlanConstant.MARKETING_PLAN);
            this.setReleaseIds(Lists.newArrayList(vo.getId()));
        }};
        tpmStagingSchemeService.deleteStaging(stagingSchemeVo);
        //日志处理
        MarketingPlanLogEventDto dto = new MarketingPlanLogEventDto();
        vo.setDetailCaseMap(null);
        vo.setTemplateList(null);
        dto.setNewest(vo);
        oldVo.setDetailCaseMap(null);
        oldVo.setTemplateList(null);
        dto.setOriginal(oldVo);
        SerializableBiConsumer<MarketingPlanLogEventListener, MarketingPlanLogEventDto> consumer = MarketingPlanLogEventListener::onUpdate;
        nebulaNetEventClient.publish(dto, MarketingPlanLogEventListener.class, consumer);
        //判断如果是变更
        if (changeFlag) {
            MarketingPlanChangeLogVo logVo = nebulaToolkitService.copyObjectByWhiteList(vo, MarketingPlanChangeLogVo.class, HashSet.class, ArrayList.class);
            //查询选择需要变更的数据
            MarketingPlanVo originVo = queryDetails(null, vo.getOriginalSchemeCode());
            logVo.setOriginalVo(originVo);
            logVo.setNewestVo(vo);
            changeLogService.saveOrUpdateLog(logVo);
        }
        return buildReturnParam(entity, vo);
    }

    /**
     * 校验预算管控
     *
     * @param vo
     */
    @Override
    public Map<String, String> checkControlBudget(MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(vo.getId(), null);
            //如果不是变更、追加类的
            if (!StringUtils.equalsAny(plan.getSchemeType(), MarketingPlanSchemeTypeEnum.append.getCode(), MarketingPlanSchemeTypeEnum.change.getCode(), MarketingPlanSchemeTypeEnum.o_two_o.getCode(), MarketingPlanSchemeTypeEnum.additional.getCode())) {
                return null;
            }
//            plan.setCheckBudgetFlag(BooleanEnum.TRUE.getCapital());
//            marketingPlanRepository.updateById(plan);
        }
        Map<String, List<MarketingPlanCaseVo>> caseDetailMap = getCacheListToMap(vo.getCacheKey());
        List<MarketingPlanCase> caseList = Lists.newArrayList();
        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : caseDetailMap.entrySet()) {
            List<MarketingPlanCase> list = (List<MarketingPlanCase>) nebulaToolkitService.copyCollectionByWhiteList(entry.getValue(), MarketingPlanCaseVo.class, MarketingPlanCase.class, HashSet.class, ArrayList.class, "itemList", "productList", "levelList", "feeItemList", "feeProductList", "feeLevelList", "feeBelongItemList");
            caseList.addAll(list);
        }
        String schemeCode = ObjectUtils.defaultIfNull(vo.getSchemeCode(), UuidCrmUtil.general());
        //重新加载管控预算
        List<RegionCollectBudgetVo> budgetVoList = regionCollectControlBudgetComponent.reloadRegionCollectControlBudget(caseList, schemeCode);
        List<MarketingPlanBudget> budgetList = (List<MarketingPlanBudget>) nebulaToolkitService.copyCollectionByWhiteList(budgetVoList, RegionCollectBudgetVo.class, MarketingPlanBudget.class, HashSet.class, ArrayList.class);
        if (CollectionUtils.isNotEmpty(budgetList)) {
            marketingPlanBudgetService.saveBatchList(budgetList, schemeCode);
        }
        StringJoiner controlErrMsg = new StringJoiner(";");
        //按照预算追踪编码来进行分组
        Map<String, List<MarketingPlanBudget>> budgetControlMap = budgetList.stream().collect(Collectors.groupingBy(x -> x.getTrackCode()));
        log.info("预算分组过后的json数据:{}", JSONObject.toJSONString(budgetControlMap));
        for (Map.Entry<String, List<MarketingPlanBudget>> entry : budgetControlMap.entrySet()) {
            List<MarketingPlanBudget> list = entry.getValue();
            String controlForm = list.get(0).getControlForm();
            //根据追踪编码来合计申请金额
            BigDecimal applyAmount = list.stream().map(MarketingPlanBudget::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            Map<String, BigDecimal> tractMap = list.stream().collect(Collectors.toMap(x -> x.getTrackCode(), k -> k.getSurplusAmount(), (a, b) -> a));
            BigDecimal surplusAmount = tractMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal differenceAmount = surplusAmount.subtract(applyAmount);
            log.error("方案{}金额{}与预算金额{}差异{},控制类型{}", vo.getSchemeCode(), applyAmount, surplusAmount, differenceAmount, controlForm);
            if (differenceAmount.compareTo(BigDecimal.ZERO) < 0) {
                String key = entry.getKey();
                BigDecimal negate = differenceAmount.negate();
                BudgetTrackVo trackVo = budgetTrackService.findByCode(key);
                //判断是预警
                if (ControlMethodEnum.waring.getCode().equals(controlForm)) {
                    if (trackVo == null) {
                        controlErrMsg.add(String.format("预算管控规则%s超额使用金额%s", key, negate));
                    } else {
                        controlErrMsg.add(String.format("预算管控规则%s-%s超额使用金额%s", key, trackVo.getDepartmentOneName(), negate));
                    }

                } else if (ControlMethodEnum.force.getCode().equals(controlForm)) {
                    if (trackVo == null) {
                        Validate.isTrue(Boolean.FALSE, String.format("预算管控规则%s超额使用金额%s", key, negate));
                    } else {
                        Validate.isTrue(Boolean.FALSE, String.format("预算管控规则%s-%s超额使用金额%s", key, trackVo.getDepartmentOneName(), negate));
                    }

                }
            }
        }
        if (!BooleanEnum.TRUE.getCapital().equals(vo.getControlFlag()) && ObjectUtils.isNotEmpty(controlErrMsg.toString())) {
            return new HashMap<String, String>() {{
                this.put("msg", controlErrMsg.toString());
            }};
        }
        return null;
    }


    /**
     * 变更的校验预算管控
     *
     * @param vo
     * @return
     */
    @Override
    public Map<String, String> checkChangeControlBudget(MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        Validate.notNull(vo.getSchemeCode(), "方案编码不能为空");
        MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(vo.getId(), null);
        //如果不是变更、追加类的
        if (!StringUtils.equalsAny(plan.getSchemeType(), MarketingPlanSchemeTypeEnum.append.getCode(), MarketingPlanSchemeTypeEnum.change.getCode(), MarketingPlanSchemeTypeEnum.o_two_o.getCode(), MarketingPlanSchemeTypeEnum.additional.getCode())) {
            return null;
        }

        List<MarketingPlanCase> caseList = planCaseService.findListBySchemeCodes(Lists.newArrayList(vo.getSchemeCode()));
//        Map<String, List<MarketingPlanCaseVo>> caseDetailMap = getCacheListToMap(vo.getCacheKey());
//        List<MarketingPlanCase> caseList = Lists.newArrayList();
//        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : caseDetailMap.entrySet()) {
//            List<MarketingPlanCase> list = (List<MarketingPlanCase>) nebulaToolkitService.copyCollectionByWhiteList(entry.getValue(), MarketingPlanCaseVo.class,
//                    MarketingPlanCase.class, HashSet.class, ArrayList.class,
//                    "itemList", "productList", "levelList", "feeItemList", "feeProductList", "feeLevelList", "feeBelongItemList");
//            caseList.addAll(list);
//        }
        String schemeCode = vo.getSchemeCode();
        //重新加载管控预算
        List<RegionCollectBudgetVo> budgetVoList = regionCollectControlBudgetComponent.reloadRegionCollectControlBudget(caseList, schemeCode);
        List<MarketingPlanBudget> budgetList = (List<MarketingPlanBudget>) nebulaToolkitService.copyCollectionByWhiteList(budgetVoList, RegionCollectBudgetVo.class, MarketingPlanBudget.class, HashSet.class, ArrayList.class);
        if (CollectionUtils.isNotEmpty(budgetList)) {
            marketingPlanBudgetService.saveBatchList(budgetList, schemeCode);
        }
        log.error("变更方案:{}匹配预算:{}", vo.getSchemeCode(), JSONObject.toJSONString(budgetList));
        StringJoiner controlErrMsg = new StringJoiner(";");
        Map<String, List<MarketingPlanBudget>> budgetControlMap = budgetList.stream().collect(Collectors.groupingBy(x -> x.getTrackCode()));
        for (Map.Entry<String, List<MarketingPlanBudget>> entry : budgetControlMap.entrySet()) {
            List<MarketingPlanBudget> list = entry.getValue();
            //此处需要判断一下变更的
            List<String> schemeDetailCodes = list.stream().map(x -> x.getSchemeDetailCode()).distinct().collect(Collectors.toList());
            List<MarketingPlanCase> marketingPlanCases = marketingPlanRepository.findChangeOccupyAmount(schemeDetailCodes);
            if (CollectionUtils.isNotEmpty(marketingPlanCases)) {
                Map<String, BigDecimal> applyAmountMap = marketingPlanCases.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), x -> x.getApplyAmount()));
                for (MarketingPlanBudget budget : list) {
                    if (applyAmountMap.containsKey(budget.getSchemeDetailCode())) {
                        budget.setApplyAmount(applyAmountMap.get(budget.getSchemeDetailCode()).negate());
                    }
                }
            }
            String controlForm = list.get(0).getControlForm();
            BigDecimal applyAmount = list.stream().map(MarketingPlanBudget::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            Map<String, BigDecimal> tractMap = list.stream().collect(Collectors.toMap(x -> x.getTrackCode(), k -> k.getSurplusAmount(), (a, b) -> a));
            BigDecimal surplusAmount = tractMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal differenceAmount = surplusAmount.subtract(applyAmount);
            if (differenceAmount.compareTo(BigDecimal.ZERO) < 0) {
                String key = entry.getKey();
                BigDecimal negate = differenceAmount.negate();
                BudgetTrackVo trackVo = budgetTrackService.findByCode(key);
                //判断是预警
                if (ControlMethodEnum.waring.getCode().equals(controlForm)) {
                    if (trackVo == null) {
                        controlErrMsg.add(String.format("预算管控规则%s超额使用金额%s", key, negate));
                    } else {
                        controlErrMsg.add(String.format("预算管控规则%s-%s超额使用金额%s", key, trackVo.getDepartmentOneName(), negate));
                    }

                } else if (ControlMethodEnum.force.getCode().equals(controlForm)) {
                    if (trackVo == null) {
                        Validate.isTrue(Boolean.FALSE, String.format("预算管控规则%s超额使用金额%s", key, negate));
                    } else {
                        Validate.isTrue(Boolean.FALSE, String.format("预算管控规则%s-%s超额使用金额%s", key, trackVo.getDepartmentOneName(), negate));
                    }

                }
            }
        }
        if (!BooleanEnum.TRUE.getCapital().equals(vo.getControlFlag()) && ObjectUtils.isNotEmpty(controlErrMsg.toString())) {
            return new HashMap<String, String>() {{
                this.put("msg", controlErrMsg.toString());
            }};
        }
        return null;
    }

    /**
     * 匹配预算管控
     *
     * @param schemeCode
     */
    @Override
    public void matchControlBudget(String schemeCode) {
        List<MarketingPlanCase> caseList = planCaseService.findListBySchemeCodes(Lists.newArrayList(schemeCode));
        //重新加载管控预算
        List<RegionCollectBudgetVo> budgetVoList = regionCollectControlBudgetComponent.reloadRegionCollectControlBudget(caseList, schemeCode);
        List<MarketingPlanBudget> budgetList = (List<MarketingPlanBudget>) nebulaToolkitService.copyCollectionByWhiteList(budgetVoList, RegionCollectBudgetVo.class, MarketingPlanBudget.class, HashSet.class, ArrayList.class);
        if (CollectionUtils.isNotEmpty(budgetList)) {
            marketingPlanBudgetService.saveBatchList(budgetList, schemeCode);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MarketingPlanVo submitOTwoOCreate(MarketingPlanVo vo, Boolean changeFlag) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        Map<String, List<MarketingPlanCaseVo>> caseDetailMap = getCacheListToMap(vo.getCacheKey());
        vo.setDetailCaseMap(caseDetailMap);
        //拼接销售计划的cacheKey
        String salesPlanCacheKey = vo.getCacheKey() + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + vo.getYears();
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findCacheList(salesPlanCacheKey);
        vo.setSalesPlanList(salesPlanVoList);
        //参数校验
        this.validateParam(vo, Boolean.TRUE, changeFlag);
        FacturerUserDetails login = loginUserService.getLoginDetails(FacturerUserDetails.class);
        vo.setOrgCode(login.getOrgCode());
        vo.setOrgName(login.getOrgName());
        vo.setPositionCode(login.getPostCode());
        vo.setPositionName(login.getPostName());
        String schemeCode = null;
        if (changeFlag) {
            schemeCode = generateCodeService.generateCodeNotDate(vo.getOriginalSchemeCode() + "-", 1, 2).get(0);
        } else {
            schemeCode = generateCodeService.generateCode(MarketingPlanConstant.SCHEME_CODE_RULE);
        }
        vo.setSchemeCode(schemeCode);
        vo.setTenantCode(TenantUtils.getTenantCode());
        vo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        vo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        vo.setConfirmStatus(BooleanEnum.TRUE.getCapital());
        if (StringUtils.isEmpty(vo.getProcessStatus())) {
            vo.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
        }
        vo.setSchemeStatus(MarketingPlanSchemeStatusEnum.COLLECTED_TO_PREPARE.getCode());
        vo.setProcessKey(null);
        vo.setProcessNumber(null);
        vo.setProcessDate(null);
        vo.setProcessRemark(null);
        vo.setOaId(null);
        vo.setOaUserName(null);
        MarketingPlan entity = nebulaToolkitService.copyObjectByBlankList(vo, MarketingPlan.class, HashSet.class, ArrayList.class);
        marketingPlanRepository.save(entity);
        vo.setId(entity.getId());
        //存放明细表
        List<MarketingPlanCaseVo> planCaseVos = vo.getDetailCaseMap().values().stream().flatMap(List::stream).collect(Collectors.toList());
        planCaseService.saveBatchCaseList(planCaseVos, schemeCode, changeFlag);
        String schemeType = vo.getSchemeType();
        // 首次初始化原始预估销售金额(未税)
        List<MarketingSalesPlanVo> salesPlanList = vo.getSalesPlanList();
        if (StringUtils.equals("o_two_o", schemeType) || StringUtils.equals("plan", schemeType) || StringUtils.equals("append", schemeType) || StringUtils.equals("contract", schemeType) || StringUtils.equals("additional", schemeType)) {
            for (MarketingSalesPlanVo marketingSalesPlanVo : salesPlanList) {
                if (Objects.isNull(marketingSalesPlanVo.getOriginEstimatedCost())) {
                    marketingSalesPlanVo.setOriginEstimatedCost(marketingSalesPlanVo.getEstimatedCost());
                }
            }
        }
        //存放销售计划
        marketingSalesPlanService.saveBatchSalesPlan(salesPlanList, schemeCode, Boolean.TRUE, BooleanEnum.FALSE.getCapital());

        //存放文件
        schemeCaseFilesService.saveOrUpdateFiles(vo.getFilesList(), entity.getSchemeCode(), OverallPlanSchemeTypeEnum.MARKETING.getCode());

        //存放客户损益
//        gainsAndLossesService.saveBatchList(vo.getGainsAndLossesList(), schemeCode);
        if (MarketingPlanSchemeTypeEnum.o_two_o.getCode().equals(vo.getSchemeType())) {
            MarketingPlanVo marketingPlanVo = queryDetails(vo.getId(), null);
            RegionCollectVo regionCollectVo = nebulaToolkitService.copyObjectByWhiteList(vo, RegionCollectVo.class, LinkedHashSet.class, ArrayList.class);
            regionCollectVo.setMarketingEstimationList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(marketingPlanVo.getMarketingEstimationList(), MarketingPlanEstimationVo.class, RegionCollectMarketingEstimationVo.class, LinkedHashSet.class, ArrayList.class)));
            regionCollectVo.setSchemeCode(schemeCode);
            regionCollectVo.setSchemeName(marketingPlanVo.getSchemeName());
            regionCollectOaService.pushOa(regionCollectVo);
        } else {
            marketingPlanSupplementOaService.pushOa(vo);
        }

        //日志处理
        MarketingPlanLogEventDto dto = new MarketingPlanLogEventDto();
        dto.setNewest(vo);
        SerializableBiConsumer<MarketingPlanLogEventListener, MarketingPlanLogEventDto> consumer = MarketingPlanLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, MarketingPlanLogEventListener.class, consumer);
        return buildReturnParam(entity, vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MarketingPlanVo submitOTwoOupdate(MarketingPlanVo vo, Boolean changeFlag) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        Map<String, List<MarketingPlanCaseVo>> caseDetailMap = getCacheListToMap(vo.getCacheKey());
        vo.setDetailCaseMap(caseDetailMap);
        //拼接销售计划的cacheKey
        String salesPlanCacheKey = vo.getCacheKey() + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + vo.getYears();
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findCacheList(salesPlanCacheKey);
        vo.setSalesPlanList(salesPlanVoList);
        //参数校验
        this.validateParam(vo, Boolean.TRUE, changeFlag);
        Validate.notNull(vo.getId(), "主键ID不能为空");
        MarketingPlanVo oldVo = queryDetails(vo.getId(), null);
        MarketingPlan entity = marketingPlanRepository.queryById(vo.getId());
        Validate.notNull(entity, "查询数据不存在，或已删除");
        //如果不是方案变更的就需要校验
        if (!changeFlag) {
            Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(entity.getChangedFlag()), String.format("方案%s是变更方案,不可使用此方法", entity.getSchemeCode()));
        } else {
            //是方案变更，但是此数据是规划案
            Validate.isTrue(BooleanEnum.TRUE.getCapital().equals(entity.getChangedFlag()), String.format("方案%s是规划方案,不可使用此方法", entity.getSchemeCode()));
        }
//        Validate.isTrue(BooleanEnum.FALSE.getCapital().equals(entity.getConfirmStatus()), "方案已确认不可编辑");
        Validate.isTrue(!StringUtils.equalsAny(entity.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode()), "审批中/审批通过的不允许编辑");
        String schemeCode = entity.getSchemeCode();
        String id = entity.getId();
        vo.setSchemeCode(schemeCode);
        vo.setId(id);
        vo.setConfirmStatus(BooleanEnum.TRUE.getCapital());
        vo.setSchemeStatus(MarketingPlanSchemeStatusEnum.COLLECTED_TO_PREPARE.getCode());
        entity = nebulaToolkitService.copyObjectByWhiteList(vo, MarketingPlan.class, HashSet.class, ArrayList.class);
        marketingPlanRepository.updateById(entity);
        //存放明细表
        List<MarketingPlanCaseVo> planCaseVos = vo.getDetailCaseMap().values().stream().flatMap(List::stream).collect(Collectors.toList());
        if (changeFlag) {
            planCaseService.saveBatchChangeCaseList(planCaseVos, schemeCode);
        } else {
            planCaseService.saveBatchCaseList(planCaseVos, schemeCode, changeFlag);
        }
        //存放销售计划
        marketingSalesPlanService.saveBatchSalesPlan(vo.getSalesPlanList(), schemeCode, Boolean.TRUE, BooleanEnum.FALSE.getCapital());

        //存放文件
        schemeCaseFilesService.saveOrUpdateFiles(vo.getFilesList(), entity.getSchemeCode(), OverallPlanSchemeTypeEnum.MARKETING.getCode());
        //存放客户损益
//        gainsAndLossesService.saveBatchList(vo.getGainsAndLossesList(), schemeCode);

        MarketingPlanVo marketingPlanVo = queryDetails(vo.getId(), null);
        RegionCollectVo regionCollectVo = nebulaToolkitService.copyObjectByWhiteList(vo, RegionCollectVo.class, LinkedHashSet.class, ArrayList.class);
        regionCollectVo.setMarketingEstimationList(new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(marketingPlanVo.getMarketingEstimationList(), MarketingPlanEstimationVo.class, RegionCollectMarketingEstimationVo.class, LinkedHashSet.class, ArrayList.class)));
        regionCollectVo.setSchemeCode(schemeCode);
        regionCollectVo.setSchemeName(marketingPlanVo.getSchemeName());
        if (ProcessStatusEnum.PREPARE.getDictCode().equals(vo.getProcessStatus())) {
            if (MarketingPlanSchemeTypeEnum.o_two_o.getCode().equals(vo.getSchemeType())) {
                regionCollectOaService.pushOa(regionCollectVo);
            } else {
                marketingPlanSupplementOaService.pushOa(vo);
            }
        } else {
            if (MarketingPlanSchemeTypeEnum.o_two_o.getCode().equals(vo.getSchemeType())) {
                regionCollectOaService.resubmitOa(regionCollectVo);
            } else {
                marketingPlanSupplementOaService.resubmitOa(vo);
            }
        }

        //日志处理
        MarketingPlanLogEventDto dto = new MarketingPlanLogEventDto();
        vo.setDetailCaseMap(null);
        vo.setTemplateList(null);
        dto.setNewest(vo);
        oldVo.setDetailCaseMap(null);
        oldVo.setTemplateList(null);
        dto.setOriginal(oldVo);
        SerializableBiConsumer<MarketingPlanLogEventListener, MarketingPlanLogEventDto> consumer = MarketingPlanLogEventListener::onUpdate;
        nebulaNetEventClient.publish(dto, MarketingPlanLogEventListener.class, consumer);
        return buildReturnParam(entity, vo);
    }

    /**
     * 创建合同营销方案
     *
     * @param vo
     */
    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> createMarketingContract(MarketingPlanVo vo, List<String> contractCodes) {
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        //验证合同方案是否有存在审批中、审批通过的
        this.validateMarketingContract(contractCodes);
        FacturerUserDetails login = loginUserService.getLoginDetails(FacturerUserDetails.class);
        vo.setOrgCode(login.getOrgCode());
        vo.setOrgName(login.getOrgName());
        vo.setPositionCode(login.getPostCode());
        vo.setPositionName(login.getPostName());
        vo.setCreateName("系统自动生成-合同类");
        String schemeCode = generateCodeService.generateCode(MarketingPlanConstant.SCHEME_CODE_RULE);
        vo.setSchemeCode(schemeCode);
        vo.setTenantCode(TenantUtils.getTenantCode());
        vo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        vo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        vo.setConfirmStatus(BooleanEnum.TRUE.getCapital());
        vo.setSchemeStatus(MarketingPlanSchemeStatusEnum.TO_BE_CONFIRMED.getCode());
        if (StringUtils.isEmpty(vo.getProcessStatus())) {
            vo.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
        }
        vo.setSchemeType(MarketingPlanSchemeTypeEnum.contract.getCode());
        MarketingPlan entity = nebulaToolkitService.copyObjectByBlankList(vo, MarketingPlan.class, HashSet.class, ArrayList.class);

        //存放明细表
        List<MarketingPlanCaseVo> planCaseVos = Lists.newArrayList();
        String cacheKey = UuidCrmUtil.general();
        List<Future<List<MarketingPlanCaseVo>>> futureList = Lists.newArrayList();
        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : vo.getDetailCaseMap().entrySet()) {
            MarketingCheckCaseComponent bean = ApplicationContextHolder.getContext().getBean(MarketingCheckCaseComponent.class);
            Future<List<MarketingPlanCaseVo>> future = bean.checkCaseList(entry.getValue(), entry.getKey(), vo.getYears(), null, null, cacheKey, loginDetails, Collections.emptyList(), null);
            futureList.add(future);
//            //大方向校验
//            List<MarketingPlanCaseVo> caseVoList = planCaseService.checkPlanCaseList(entry.getValue(), vo.getYears());
//            //分模板校验
//            for (MarketingPlanCaseStrategy strategy : strategyList) {
//                if (strategy.getCaseType().equals(entry.getKey())) {
//                    strategy.checkCaseList(caseVoList, vo.getYears(), null, null, UuidCrmUtil.general());
//                }
//            }
        }
        for (Future<List<MarketingPlanCaseVo>> future : futureList) {
            planCaseVos.addAll(future.get());
        }
        Validate.isTrue(CollectionUtils.isNotEmpty(planCaseVos), "方案明细为空");

        Map<String, String> errMap = Maps.newHashMap();
        for (MarketingPlanCaseVo planCaseVo : planCaseVos) {
            if (!planCaseVo.getCheckFlag()) {
                errMap.put(planCaseVo.getOnlyKeys(), planCaseVo.getErrMsg());
            }
        }
        if (ObjectUtils.isNotEmpty(errMap)) {
            return errMap;
        }
        entity.setCreateTime(null);
        entity.setCreateAccount(null);
        marketingPlanRepository.save(entity);
        vo.setId(entity.getId());
        planCaseService.saveBatchCaseList(planCaseVos, schemeCode, Boolean.FALSE);

        //日志处理
        MarketingPlanLogEventDto dto = new MarketingPlanLogEventDto();
        dto.setNewest(vo);
        SerializableBiConsumer<MarketingPlanLogEventListener, MarketingPlanLogEventDto> consumer = MarketingPlanLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, MarketingPlanLogEventListener.class, consumer);
        return null;
    }

    /**
     * 校验合同数据
     *
     * @param contractCodes
     */
    private void validateMarketingContract(List<String> contractCodes) {
//        List<MarketingPlan> plans = marketingPlanRepository.findListByContractCodes(contractCodes);
//        if (CollectionUtils.isNotEmpty(plans)) {
//            List<String> processStatusList = plans.stream().map(MarketingPlan::getProcessStatus).collect(Collectors.toList());
//            Validate.isTrue(processStatusList.contains(ProcessStatusEnum.PASS.getDictCode()) ||
//                    processStatusList.contains(ProcessStatusEnum.COMMIT.getDictCode()), "存在审批中/审批通过的合同方案");
//            this.deleteBatch(plans.stream().map(MarketingPlan::getId).collect(Collectors.toList()));
//        }
    }

    public MarketingPlanVo buildReturnParam(MarketingPlan plan, MarketingPlanVo vo) {
        MarketingPlanVo result = nebulaToolkitService.copyObjectByWhiteList(plan, MarketingPlanVo.class, HashSet.class, ArrayList.class);
        result.setTemplateList(vo.getTemplateList());
        result.setGainsAndLossesList(vo.getGainsAndLossesList());
        return result;
    }


    /**
     * 校验参数信息
     *
     * @param vo
     * @param submitFlag
     */
    private void validateParam(MarketingPlanVo vo, Boolean submitFlag, Boolean changeFlag) {
        Validate.notNull(vo.getSchemeName(), "方案名称不能为空");
        Validate.isTrue(vo.getSchemeName().length() < 100, "方案名称不能超过100字符");
        Validate.notNull(vo.getSchemeType(), "方案类型不能为空");
        if (ObjectUtils.isNotEmpty(vo.getSchemeTheme())) {
            Validate.isTrue(vo.getSchemeName().length() <= 300, "方案主题不能超过300字符");
        }
        if (ObjectUtils.isNotEmpty(vo.getSchemeDesc())) {
            Validate.isTrue(vo.getSchemeDesc().length() <= 1000, "方案说明不能超过1000字符");
        }
        vo.setChangedFlag(BooleanEnum.FALSE.getCapital());
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(vo.getId(), null);
            Validate.isTrue(!MarketingPlanSchemeTypeEnum.contract.getCode().equals(plan.getSchemeType()), "合同营销方案不允许编辑");
        }
        //如果是变更方案
        if (changeFlag) {
            String nowYears = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            String years = vo.getYears();
            Validate.isTrue(nowYears.compareTo(years) < 1, "不允许变更历史月份方案！");
            vo.setChangedFlag(BooleanEnum.TRUE.getCapital());
            if (ObjectUtils.isNotEmpty(vo.getChangeDesc())) {
                Validate.isTrue(vo.getChangeDesc().length() <= 1000, "变更方案说明不能超过1000字符");
            }
            Validate.notNull(vo.getOriginalSchemeCode(), "变更方案原方案编码不能为空");
            MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(null, vo.getOriginalSchemeCode());
            //判断方案类型是规划
            if (MarketingPlanSchemeTypeEnum.plan.getCode().equals(plan.getSchemeType())) {
                //判断方案明细不为空
                if (!vo.getDetailCaseMap().isEmpty() && ObjectUtils.isNotEmpty(vo.getDetailCaseMap())) {
                    List<String> actYearsList = vo.getDetailCaseMap().values().stream().flatMap(List::stream).map(x -> x.getActYears()).distinct().collect(Collectors.toList());
                    if (actYearsList.size() > 1 || !actYearsList.contains(years)) {
                        Validate.isTrue(Boolean.FALSE, "原方案类型是规划的，进行变更时不能变更历史月份数据");
                    }
                }
            }
            Validate.notNull(plan, String.format("方案编码%s查询不存在", vo.getOriginalSchemeCode()));
            Validate.isTrue(!MarketingPlanSchemeTypeEnum.change.getCode().equals(plan.getSchemeType()), "原方案是变更方案不允许在变更");
            Validate.isTrue(!StringUtils.equalsAny(plan.getSchemeType(), MarketingPlanSchemeTypeEnum.contract.getCode()), "合同营销方案不允许变更");
            Validate.isTrue(ProcessStatusEnum.PASS.getDictCode().equals(plan.getProcessStatus()), String.format("方案%s未审核通过不可进行变更", plan.getSchemeName()));
            String errMsg = checkUnderTakeAmount(vo.getDetailCaseMap().values().stream().flatMap(List::stream).collect(Collectors.toList()));
            Validate.isTrue(ObjectUtils.isEmpty(errMsg), errMsg);

        }
        if (submitFlag) {
            Validate.notNull(vo.getYears(), "年月不能为空");
            Validate.notNull(vo.getDepartmentCode(), "部门编码不能为空");
            Validate.notNull(vo.getDepartmentName(), "部门名称不能为空");
            Validate.isTrue(!vo.getDetailCaseMap().isEmpty(), "方案明细列表不能为空");
            LocalDate yearsDate = LocalDate.now();
            List<String> customerCodes = Lists.newArrayList();
            List<String> belongDepartmentCodes = Lists.newArrayList();
            BigDecimal applyTotalAmount = BigDecimal.ZERO;
            List<MarketingPlanCaseVo> planCaseVos = vo.getDetailCaseMap().values().stream().flatMap(List::stream)
                    .collect(Collectors.toList());
            Map<String, String> channelTypeMap = buildOverallPlanCaseMap(planCaseVos);
            for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : vo.getDetailCaseMap().entrySet()) {
                Validate.isTrue(MarketingPlanCaseTypeEnum.checkCode(entry.getKey()), "类型错误");
                for (MarketingPlanCaseVo caseVo : entry.getValue()) {
                    Validate.isTrue(caseVo.getCheckFlag(), "数据校验不通过，请在活动明细行右下角查看错误信息");
                    LocalDate caseEndDate = LocalDate.parse(caseVo.getYears() + "-01", DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                    Validate.isTrue(caseEndDate.getMonthValue() >= yearsDate.getMonthValue(), "活动明细的预算年月不能是往月");
//                    Validate.isTrue(yearsDate.getYear() == caseEndDate.getYear() && yearsDate.getMonthValue() >= caseEndDate.getMonthValue(), "方案明细的活动时间不允许跨年或活动结束时间不能大于预算归属年月");
                    caseVo.setCaseType(entry.getKey());
                    caseVo.setYears(vo.getYears());

                    customerCodes.add(caseVo.getCustomerCode());
                    belongDepartmentCodes.add(caseVo.getBelongDepartmentCode());
                    applyTotalAmount = applyTotalAmount.add(caseVo.getApplyAmount());
                }
            }
            vo.setApplyTotalAmount(applyTotalAmount);
            // 校验指引中指定的渠道类型 与 明细行所选的客户渠道类型 是否一致
            List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
            Map<String, String> customerChannelTypeMap = customerVos.stream().filter(e -> StringUtils.isNotEmpty(e.getCustomerCode()) && Objects.nonNull(e.getChannelType()))
                    .collect(Collectors.toMap(CustomerVo::getCustomerCode, CustomerVo::getChannelType, (v1, v2) -> v1));
            planCaseVos.forEach(item -> {
                String customerCode = item.getCustomerCode();
                String customerChannelTypeStr = customerChannelTypeMap.get(customerCode);
                if (StringUtils.isEmpty(customerChannelTypeStr)) return;
                List<String> channelTypeList = Arrays.asList(customerChannelTypeStr.split(","));
                String headSchemeCode = item.getHeadSchemeCode();
                String headSchemeDetailCode = item.getHeadSchemeDetailCode();
                if (StringUtils.isNotEmpty(headSchemeCode) && StringUtils.isNotEmpty(headSchemeDetailCode)) {
                    String key = headSchemeCode + "_" + headSchemeDetailCode;
                    String channelTypeStr = channelTypeMap.get(key);
                    if (StringUtils.isNotEmpty(channelTypeStr)) {
                        List<String> list = Arrays.asList(channelTypeStr.split(","));
                        list.retainAll(channelTypeList);
                        Validate.isTrue(CollectionUtils.isNotEmpty(list), String.format("客户%s的渠道类型与关联指引中的渠道类型不一致，请修改", customerCode));
                    }
                }
            });
//            if (MarketingPlanSchemeTypeEnum.additional.getCode().equals(vo.getSchemeType())) {
//                List<MarketingPlanCase> caseList = planCaseService.findListByCustomerCodesAndBelongDepartmentCodes(customerCodes, belongDepartmentCodes, vo.getSchemeCode(), vo.getYears());
//                if (CollectionUtils.isNotEmpty(caseList)) {
//                    StringJoiner errMsg = new StringJoiner(";");
//                    for (MarketingPlanCase planCase : caseList) {
//                        errMsg.add(String.format("客户%s和使用部门%s存在审批通过的数据，不可进行补录", planCase.getCustomerName(), planCase.getBelongDepartmentName()));
//                    }
//                    Validate.isTrue(ObjectUtils.isEmpty(errMsg.toString()), errMsg.toString());
//                }
//            }
            //校验方案类型对应的控制
            this.validateSchemeType(vo);
        }
    }

    private String checkUnderTakeAmount(List<MarketingPlanCaseVo>  marketingPlanCaseVos) {
        if(CollectionUtils.isEmpty(marketingPlanCaseVos)){
            return null;
        }
        List<MarketingPlanCase> list = marketingPlanCaseVos.stream().filter(item -> Objects.nonNull(item.getApplyAmount()) && ObjectUtils.isNotEmpty(item.getReleaseDetailCode())).map(item -> new MarketingPlanCase() {{
            setApplyAmount(item.getApplyAmount());
            setReleaseDetailCode(item.getReleaseDetailCode());
        }}).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        Map<String, BigDecimal> caseMap = list.stream().map(item -> new MarketingPlanCase() {{
            setApplyAmount(item.getApplyAmount());
            setReleaseDetailCode(item.getReleaseDetailCode());
        }}).collect(Collectors.toList()).stream().collect(Collectors.groupingBy(MarketingPlanCase::getReleaseDetailCode,
                Collectors.mapping(MarketingPlanCase::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Set<String> releaseDetailCodes = caseMap.keySet();
        log.info("营销方案变更-明细编码  releaseDetailCodes {} caseMap {}",JSONObject.toJSONString(releaseDetailCodes),JSONObject.toJSONString(caseMap));
        // 总金额
        List<OverallPlanCaseVo> overallPlanCaseVoList = overallPlanService.findOverallPlanCaseListByReleaseDetailCode(Lists.newArrayList(releaseDetailCodes));
        // 明细
        List<MarketingPlanCaseVo> regionMarketingPlanCase = marketingPlanCaseService.findRegionMarketingPlanCase(Lists.newArrayList(releaseDetailCodes));
        Map<String, BigDecimal> existDetail = new HashMap<>();
        if(CollectionUtils.isNotEmpty(regionMarketingPlanCase)){
            existDetail = regionMarketingPlanCase.stream().map(item -> new MarketingPlanCase() {{
                setApplyAmount(item.getApplyAmount());
                setReleaseDetailCode(item.getReleaseDetailCode());
            }}).collect(Collectors.toList()).stream().collect(Collectors.groupingBy(MarketingPlanCase::getReleaseDetailCode, Collectors.mapping(MarketingPlanCase::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }


        StringJoiner errMsg = new StringJoiner(";");
        for (OverallPlanCaseVo caseVo : overallPlanCaseVoList) {
            BigDecimal applyAmount = caseMap.get(caseVo.getSchemeDetailCode());
            //承接明细金额-营销方案承接金额-历史金额
            BigDecimal existstDetailAmount =Objects.isNull(existDetail)?BigDecimal.ZERO:existDetail.getOrDefault(caseVo.getSchemeDetailCode(),BigDecimal.ZERO);
            BigDecimal differenceAmount = caseVo.getApplyAmount().subtract(existstDetailAmount).subtract(applyAmount);
            //未承接完成 剩余金额
            if (differenceAmount.compareTo(BigDecimal.ZERO) == 1) {
                if (caseVo.getSchemeDetailCode().contains(OverallPlanConstant.HEAD_SCHEME_DETAIL_CODE_RULE)) {
                    errMsg.add(String.format("总部指引明细%s未承接完成,剩余可承接金额%s", caseVo.getSchemeDetailCode(), differenceAmount));
                } else {
                    errMsg.add(String.format("大区指引明细%s未承接完成,剩余可承接金额%s", caseVo.getSchemeDetailCode(), differenceAmount));
                }
            }
            //承接超额
            if (differenceAmount.compareTo(BigDecimal.ZERO) == -1) {
                if (caseVo.getSchemeDetailCode().contains(OverallPlanConstant.HEAD_SCHEME_DETAIL_CODE_RULE)) {
                    errMsg.add(String.format("总部指引明细%s承接超额,超额%s", caseVo.getSchemeDetailCode(), differenceAmount));
                } else {
                    errMsg.add(String.format("大区指引明细%s承接超额,超额%s", caseVo.getSchemeDetailCode(), differenceAmount));
                }
            }
        }
        String errMsgStr = errMsg.toString();
        if("".equals(errMsgStr) || ";".equals(errMsgStr)){

            return null;
        }
        return errMsgStr;
    }

    private Map<String, String> buildOverallPlanCaseMap(List<MarketingPlanCaseVo> planCaseVos) {
        List<OverallPlanCase> caseQueryList = Lists.newArrayList();
        Map<String, Integer> planCaseExistedMap = Maps.newHashMap();
        planCaseVos.forEach(e -> {
            String headSchemeCode = e.getHeadSchemeCode();
            String headSchemeDetailCode = e.getHeadSchemeDetailCode();
            if (StringUtils.isNotEmpty(headSchemeCode) && StringUtils.isNotEmpty(headSchemeDetailCode)) {
                String key = headSchemeCode + "_" + headSchemeDetailCode;
                Integer existedFlag = planCaseExistedMap.get(key);
                if (null == existedFlag) {
                    OverallPlanCase planCase = new OverallPlanCase();
                    planCase.setSchemeCode(headSchemeCode);
                    planCase.setSchemeDetailCode(headSchemeDetailCode);
                    caseQueryList.add(planCase);
                    planCaseExistedMap.put(key, 1);
                }
            }
        });
        List<OverallPlanCase> overallPlanCases = overPlanCaseRepository.findListBySchemeCodeAndSchemeDetailCodePair(caseQueryList);
        Map<String, String> channelTypeMap = overallPlanCases.stream().filter(el -> StringUtils.isNotEmpty(el.getChannelTypeStr()))
                .collect(Collectors.toMap(x -> x.getSchemeCode() + "_" + x.getSchemeDetailCode(), OverallPlanCase::getChannelTypeStr));
        return channelTypeMap;
    }


    /**
     * 校验方案类型对应的判断
     *
     * @param vo
     */
    private void validateSchemeType(MarketingPlanVo vo) {
        //判断是客户
        if (MarketingPlanSchemeTypeEnum.plan.getCode().equals(vo.getSchemeType())) {
            AbstractCrmUserIdentity userIdentity = loginUserService.getAbstractLoginUser();
            List<CustomerVo> customerVoList = customerVoService.findCustomerByUserName(userIdentity.getAccount());
            Set<String> customerCodes = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(customerVoList)) {
                Map<String, String> customerMap = customerVoList.stream().collect(Collectors.toMap(x -> x.getCustomerCode(), l -> l.getCustomerCode() + "/" + l.getCustomerName()));
                //只需要启用并且是合同客户的
                customerCodes = customerVoList.stream().filter(x -> EnableStatusEnum.ENABLE.getCode().equals(x.getEnableStatus()) &&
                                BooleanEnum.TRUE.getCapital().equals(x.getContractCustomer()))
                        .map(x -> x.getCustomerCode()).collect(Collectors.toSet());
                Validate.isTrue(CollectionUtils.isNotEmpty(vo.getSalesPlanList()), "销售计划不能为空");
                Set<String> salesPlanCustomerCodes = vo.getSalesPlanList().stream().map(x -> x.getCustomerCode()).collect(Collectors.toSet());

                Set<String> salesPlanDifferenceSet = Sets.difference(customerCodes, salesPlanCustomerCodes);
                if (CollectionUtils.isNotEmpty(salesPlanDifferenceSet)) {
                    StringJoiner errMsg = new StringJoiner(";");
                    for (String s : salesPlanDifferenceSet) {
                        String unionCodeName = customerMap.get(s);
                        errMsg.add(unionCodeName);
                    }
                    Validate.isTrue(Boolean.FALSE, String.format("当前用户关联的客户%s未规划销售计划", errMsg));
                }
            }
        } else if (MarketingPlanSchemeTypeEnum.change.getCode().equals(vo.getSchemeType())) {
            String originalSchemeCode = vo.getOriginalSchemeCode();
//            Set<String> originalCustomerCodes = planCaseService.findCustomerCodeListBySchemeCode(originalSchemeCode);
            MarketingPlan originalPlan = marketingPlanRepository.queryByIdOrSchemeCode(null, originalSchemeCode);
            //如果原方案不是规划类型的 则不校验
            if (!MarketingPlanSchemeTypeEnum.plan.getCode().equals(originalPlan.getSchemeType())) {
                return;
            }
            List<MarketingSalesPlanVo> originalSalesPlanList = marketingSalesPlanService.findListBySchemeCode(originalSchemeCode);
            Set<String> originalSalesPlanCustomerCodes = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(originalSalesPlanList)) {
                originalSalesPlanCustomerCodes = originalSalesPlanList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toSet());
            }

            Set<String> salesPlanCustomerCodes = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(vo.getSalesPlanList())) {
                salesPlanCustomerCodes = vo.getSalesPlanList().stream().map(x -> x.getCustomerCode()).collect(Collectors.toSet());
            }
//            Set<String> customerCodes = vo.getDetailCaseMap().values().stream().flatMap(List::stream).map(x -> x.getCustomerCode()).collect(Collectors.toSet());

//            Set<String> caseDifferenceSet = Sets.difference(customerCodes, originalCustomerCodes);
//            Validate.isTrue(CollectionUtils.isEmpty(caseDifferenceSet), "变更方案只允许做原方案里面的客户费用明细");
            if (CollectionUtils.isNotEmpty(salesPlanCustomerCodes)) {
                Set<String> salesPlanDifferenceSet = Sets.difference(salesPlanCustomerCodes, originalSalesPlanCustomerCodes);
                Validate.isTrue(CollectionUtils.isEmpty(salesPlanDifferenceSet), "变更方案只允许做原方案里面的客户销售计划");
            }
        } else if (MarketingPlanSchemeTypeEnum.additional.getCode().equals(vo.getSchemeType())) {
            String nowYears = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
//            Set<String> customerCodes = vo.getDetailCaseMap().values().stream().flatMap(List::stream).map(x -> x.getCustomerCode()).collect(Collectors.toSet());
//            Set<String> belongDepartmentCodes = vo.getDetailCaseMap().values().stream().flatMap(List::stream).map(x -> x.getBelongDepartmentCode()).collect(Collectors.toSet());
//            Set<String> actYearsList = vo.getDetailCaseMap().values().stream().flatMap(List::stream).map(x -> x.getActYears()).collect(Collectors.toSet());
//            String years = vo.getYears();
//            List<String> schemeTypes = Lists.newArrayList(MarketingPlanSchemeTypeEnum.plan.getCode(), MarketingPlanSchemeTypeEnum.o_two_o.getCode(),
//                    MarketingPlanSchemeTypeEnum.contract.getCode(), MarketingPlanSchemeTypeEnum.append.getCode());

//            Validate.isTrue(nowYears.compareTo(years) > -1, "补录方案只能是历史月份或当前月");
//            List<MarketingPlanCase> caseList = marketingPlanMapper.findListByCondition(actYearsList, customerCodes, belongDepartmentCodes, schemeTypes);
//            if (CollectionUtils.isNotEmpty(caseList)) {
//                StringJoiner errMsg = new StringJoiner(";");
//                caseList.forEach(x -> {
//                    String msg = String.format("客户%s加使用部门%s加年月%s存在重复的费用明细数据", x.getCustomerName(), x.getBelongDepartmentName(), x.getYears());
//                    errMsg.add(msg);
//                });
//                Validate.isTrue(Boolean.FALSE, errMsg.toString());
//            }
            Set<String> actYearsSet = vo.getDetailCaseMap().values().stream().flatMap(List::stream)
                    .map(x -> x.getActYears()).collect(Collectors.toSet());
            //判断大于1 并且包含了当前月 则直接提示
            if (actYearsSet.size() > 1 && actYearsSet.contains(nowYears)) {
                Validate.isTrue(Boolean.FALSE, "补录方案不能既有新开客户方案又有历史客户方案明细");
            }
            //如果包含当前年月 表示是新开客户的补录方案，反之历史补录
            if (actYearsSet.contains(nowYears)) {
                vo.setNewCustomerFlag(BooleanEnum.TRUE.getCapital());
            } else {
                vo.setNewCustomerFlag(BooleanEnum.FALSE.getCapital());
            }
            //验证是当月的客户的时候 校验新开客户
            List<String> newCustomerCodes = vo.getDetailCaseMap().values().stream().flatMap(List::stream)
                    .filter(x -> x.getActYears().equals(nowYears))
                    .map(x -> x.getCustomerCode()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(newCustomerCodes)) {
                List<MarketingPlanCase> planCaseList = marketingPlanMapper.findAddSchemeListByYearsAndCustomers(nowYears, newCustomerCodes);
                if (CollectionUtils.isNotEmpty(planCaseList)) {
                    StringJoiner errMsg = new StringJoiner(";");
                    planCaseList.forEach(x -> {
                        String msg = String.format("客户%s已有规划方案,不可进行补录", x.getCustomerName());
                        errMsg.add(msg);
                    });
                    Validate.isTrue(Boolean.FALSE, errMsg.toString());
                }
            }
            //验证不准承接
            List<String> releaseDetailCodes = vo.getDetailCaseMap().values().stream().flatMap(List::stream).filter(x -> ObjectUtils.isNotEmpty(x.getReleaseDetailCode()))
                    .map(x -> x.getReleaseDetailCode()).collect(Collectors.toList());
            Validate.isTrue(CollectionUtils.isEmpty(releaseDetailCodes), "补录方案不允许承接");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteBatch(List<String> idList) {
        Assert.notEmpty(idList, "请选择数据!");
        //对方案类型为“O2O/追加/变更/合同”四种类型的，编辑和删除时，
        //只判断审批状态是否可以编辑和删除（审批中和审批通过不允许，其他状态允许），不判定确认状态。
        //1.对于“规划”类型的需要同时判断审批状态和确认状态。
        List<MarketingPlan> planList = marketingPlanRepository.findListByIdList(idList);
        String schemeCodeStr = planList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode()) || (MarketingPlanSchemeTypeEnum.plan.getCode().equals(x.getSchemeType()) && BooleanEnum.TRUE.getCapital().equals(x.getConfirmStatus()))).map(MarketingPlan::getSchemeCode).collect(Collectors.joining("、"));
        StringJoiner errMsg = new StringJoiner(";");
        if (ObjectUtils.isNotEmpty(schemeCodeStr)) {
            errMsg.add("方案编码:" + schemeCodeStr + "状态为审批中/审批通过或已确认不允许删除");
        }
        String contractCodesStr = planList.stream().filter(x -> StringUtils.equalsAny(x.getSchemeType(), MarketingPlanSchemeTypeEnum.contract.getCode())).map(MarketingPlan::getSchemeCode).collect(Collectors.joining(","));
        if (ObjectUtils.isNotEmpty(contractCodesStr)) {
            errMsg.add("方案编码:" + contractCodesStr + "是合同方案不允许删除");
        }
        planList = planList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(), ProcessStatusEnum.RECOVER.getDictCode())).filter(x -> BooleanEnum.FALSE.getCapital().equals(x.getConfirmStatus()) || !MarketingPlanSchemeTypeEnum.contract.getCode().equals(x.getSchemeType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(planList) && StringUtils.isBlank(errMsg.toString())) {
            planList.forEach(x -> x.setDelFlag(DelFlagStatusEnum.DELETE.getCode()));
            marketingPlanRepository.updateBatchById(planList);
            List<String> schemeCodes = planList.stream().map(MarketingPlan::getSchemeCode).collect(Collectors.toList());
            planCaseService.deleteBySchemeCodes(schemeCodes);
            gainsAndLossesService.deleteBySchemeCodes(schemeCodes);
            marketingSalesPlanService.deleteBySchemeCodes(schemeCodes);
            productRepository.deleteBySchemeCodes(schemeCodes);
            marketingPlanEstimationService.deleteBySchemeCodes(schemeCodes);
            marketingPlanSchemeEstimationService.deleteBySchemeCodes(schemeCodes);
            planChangeSchemeCaseRepository.deleteBySchemeCode(schemeCodes);

            //存放文件
            schemeCaseFilesService.deleteFiles(schemeCodes, Lists.newArrayList(OverallPlanSchemeTypeEnum.MARKETING.getCode()));

            //删除OA接口
            planList = planList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.REJECT.getDictCode(), ProcessStatusEnum.RECOVER.getDictCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(planList)) {
                planList.forEach(e -> ryOaProcessService.deleteWorkflow(e.getProcessNumber(), e.getOaId()));
            }

            //日志处理
            MarketingPlanLogEventDto dto = new MarketingPlanLogEventDto();
            List<MarketingPlanVo> planVoList = (List<MarketingPlanVo>) nebulaToolkitService.copyCollectionByWhiteList(planList, MarketingPlan.class, MarketingPlanVo.class, HashSet.class, ArrayList.class);
            dto.setNewestList(planVoList);
            SerializableBiConsumer<MarketingPlanLogEventListener, MarketingPlanLogEventDto> consumer = MarketingPlanLogEventListener::onDelete;
            nebulaNetEventClient.publish(dto, MarketingPlanLogEventListener.class, consumer);
        }
        return errMsg.toString();
    }

    /**
     * 确认数据
     *
     * @param idList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String confirmSure(List<String> idList) {
        List<MarketingPlan> entityList = marketingPlanRepository.findListByIdList(idList);
        if (CollectionUtils.isEmpty(entityList)) {
            return null;
        }
        String errMsg = null;
        String schemeCodes = entityList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.COMMIT.getDictCode(), ProcessStatusEnum.PASS.getDictCode())).map(MarketingPlan::getSchemeCode).collect(Collectors.joining("、"));
        if (ObjectUtils.isNotEmpty(schemeCodes)) {
            errMsg = "方案编码" + schemeCodes + "是审批中/审批通过状态不允许确认";
        }
        List<MarketingPlan> filterEntityList = entityList.stream().filter(x -> !StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterEntityList)) {
            filterEntityList.forEach(x -> {
                x.setSchemeStatus(MarketingPlanSchemeStatusEnum.CONFIRMED_TO_COLLECT.getCode());
                x.setConfirmStatus(BooleanEnum.TRUE.getCapital());
                //更新客户损益表
                this.calCustomerGains(x.getSchemeCode());
                if (MarketingPlanSchemeTypeEnum.plan.getCode().equals(x.getSchemeType())) {
                    //更新单方案-营销测算
                    this.calMarketingPlanSchemeEstimation(x.getSchemeCode());
                }
            });
            marketingPlanRepository.updateBatchById(entityList);
        }
        return errMsg;
    }


    /**
     * 取消确认
     *
     * @param idList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String cancelConfirm(List<String> idList) {
        List<MarketingPlan> entityList = marketingPlanRepository.findListByIdList(idList);
        if (CollectionUtils.isEmpty(entityList)) {
            return null;
        }
        String errMsg = null;
        String schemeCodes = entityList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.COMMIT.getDictCode(), ProcessStatusEnum.PASS.getDictCode())).map(MarketingPlan::getSchemeCode).collect(Collectors.joining("、"));
        if (ObjectUtils.isNotEmpty(schemeCodes)) {
            errMsg = "方案编码" + schemeCodes + "是审批中/审批通过状态不允许取消确认";
        }
        List<MarketingPlan> filterEntityList = entityList.stream().filter(x -> !StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterEntityList)) {
            filterEntityList.forEach(x -> {
                x.setSchemeStatus(MarketingPlanSchemeStatusEnum.TO_BE_CONFIRMED.getCode());
                x.setConfirmStatus(BooleanEnum.FALSE.getCapital());
            });
            marketingPlanRepository.updateBatchById(entityList);
            List<String> filterSchemeCodes = filterEntityList.stream().map(MarketingPlan::getSchemeCode).collect(Collectors.toList());
            //删除大区关联的营销方案
            regionCollectSchemeService.deleteBySchemeCodes(filterSchemeCodes);
        }
        return errMsg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void callback(MarketingPlanVo callbackVo) {
        //TODO 此处还不知道是通过什么来查询业务主数据
        MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, callbackVo.getBusinessCode());
        if (ObjectUtils.isNotEmpty(entity)) {
            if (StringUtils.equalsAny(entity.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode())) return;
            entity.setProcessStatus(callbackVo.getProcessStatus());
            entity.setSchemeStatus(callbackVo.getProcessStatus());
            marketingPlanRepository.updateById(entity);
            //如果是审批通过则调用推送方法
            if (ProcessStatusEnum.PASS.getDictCode().equals(entity.getProcessStatus())) {
                //判断是否是变更方案
                List<String> schemeDetailCodes = Lists.newArrayList();
                if (ObjectUtils.isNotEmpty(entity.getOriginalSchemeCode()) && MarketingPlanSchemeTypeEnum.change.getCode().equals(entity.getSchemeType())) {
                    schemeDetailCodes = changeSchemeComponent.changeSchemeSyncOriginScheme(entity.getSchemeCode());
                } else {
                    List<MarketingPlanCase> caseList = planCaseService.findListBySchemeCodes(Lists.newArrayList(entity.getSchemeCode()));
                    schemeDetailCodes = caseList.stream().map(MarketingPlanCase::getSchemeDetailCode).collect(Collectors.toList());
                }

                //推送活动到SFA
                planCaseService.pushCaseListToSfa(schemeDetailCodes);
                //TODO 还有一个物料推送DMS费用池上账
                this.tpmMarketingPlanCaseComponent.operate(schemeDetailCodes, entity, null);
                //自动结案
                marketingAuditComponentService.autoAuditPlanCasePass(schemeDetailCodes);

                //变更判断更新
                if (MarketingPlanSchemeTypeEnum.change.getCode().equals(entity.getSchemeType())) {
                    //存放原方案的客户损益
                    backGainsAndLossesService.saveOriginalGainsAndLosses(entity.getOriginalSchemeCode(), entity.getSchemeCode());
                    //更新客户损益表
                    this.changeCalCustomerGains(entity.getOriginalSchemeCode());
                    List<OrgVo> orgVos = orgVoService.findAllParentByOrgCode(entity.getDepartmentCode());
                    Validate.isTrue(CollectionUtils.isNotEmpty(orgVos), "查询上级组织信息为空");
                    Optional<OrgVo> optional = orgVos.stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType())).collect(Collectors.toList()).stream().findFirst();
                    if (optional.isPresent()) {
                        String orgCode = optional.get().getOrgCode();
                        RegionCollectCal bean = ApplicationContextHolder.getContext().getBean(RegionCollectCal.class);
                        bean.marketingChangeUpdateRegionCollect(orgCode, entity.getYears());
                    }
                }
            }
        }
    }


    /**
     * 变更方案对比客户损益
     *
     * @param schemeCode
     * @return
     */
    @Override
    public List<MarketPlanChangeGainsAndLossesVo> findChangeGainsAndLosses(String schemeCode) {
        MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(null, schemeCode);
        Validate.notNull(plan.getOriginalSchemeCode(), "不是变更方案");
        List<MarketingPlanGainsAndLosses> gainsAndLossesList = Lists.newArrayList();
        //判断如果是审批通过
        if (ProcessStatusEnum.PASS.getDictCode().equals(plan.getProcessStatus())) {
            //查询变更方案的客户损益
            List<MarketingPlanGainsAndLosses> losses = gainsAndLossesService.findListBySchemeCodes(Lists.newArrayList(schemeCode));
            if (CollectionUtils.isNotEmpty(losses)) {
                gainsAndLossesList.addAll(losses);
            }
            //查询原方案的客户损益-绑定的是变更方案编码
            List<MarketingPlanBackGainsAndLosses> backGainsAndLosses = backGainsAndLossesService.findListBySchemeCode(schemeCode);
            if (CollectionUtils.isNotEmpty(backGainsAndLosses)) {
                List<MarketingPlanGainsAndLosses> backLosses = (List<MarketingPlanGainsAndLosses>) nebulaToolkitService.copyCollectionByWhiteList(backGainsAndLosses, MarketingPlanBackGainsAndLosses.class, MarketingPlanGainsAndLosses.class, HashSet.class, ArrayList.class);
                gainsAndLossesList.addAll(backLosses);
            }
        } else {
            List<String> schemeCodes = Lists.newArrayList(schemeCode, plan.getOriginalSchemeCode());
            gainsAndLossesList = gainsAndLossesService.findListBySchemeCodes(schemeCodes);
        }
        if (CollectionUtils.isEmpty(gainsAndLossesList)) {
            return Lists.newArrayList();
        }
        List<MarketPlanChangeGainsAndLossesVo> dataList = Lists.newArrayList();
        //以变更表作为基础表
        Map<String, List<MarketingPlanGainsAndLosses>> listMap = gainsAndLossesList.stream().filter(x -> schemeCode.equals(x.getSchemeCode())).collect(Collectors.groupingBy(x -> x.getOrgCode() + ":" + x.getOrgName() + ":" + x.getCustomerCode() + ":" + x.getCustomerName()));
        for (Map.Entry<String, List<MarketingPlanGainsAndLosses>> entry : listMap.entrySet()) {
            String[] str = entry.getKey().split(":");
            MarketPlanChangeGainsAndLossesVo vo = new MarketPlanChangeGainsAndLossesVo();
            vo.setOrgCode(str[0]);
            vo.setOrgName(str[1]);
            vo.setCustomerCode(str[2]);
            vo.setCustomerName(str[3]);
            dataList.add(vo);
        }
        Map<String, List<MarketingPlanGainsAndLosses>> map = gainsAndLossesList.stream().filter(k -> StringUtils.isNotEmpty(k.getSchemeCode())).collect(Collectors.groupingBy(MarketingPlanGainsAndLosses::getSchemeCode));
        List<MarketingPlanGainsAndLosses> list = map.getOrDefault(plan.getOriginalSchemeCode(), Lists.newArrayList());
        Map<String, MarketingPlanGainsAndLosses> originalMap = list.stream().collect(Collectors.toMap(x -> x.getCustomerCode() + x.getOrgCode(), v -> v, (n, o) -> n));
        for (MarketPlanChangeGainsAndLossesVo data : dataList) {
            String key = data.getCustomerCode() + data.getOrgCode();
            if (originalMap.containsKey(key)) {
                MarketingPlanGainsAndLosses entity = originalMap.get(key);
                data.setProfitRatio(entity.getProfitRatio());
                data.setPlanIncome(entity.getPlanIncome());
                data.setPlanRatio(entity.getPlanRatio());
                data.setMarketingCost(entity.getMarketingCost());
                data.setGrossProfitRatio(entity.getGrossProfitRatio());
            }
        }


        //变更数据
        List<MarketingPlanGainsAndLosses> changeList = map.getOrDefault(plan.getSchemeCode(), Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(changeList)) {
            Map<String, MarketingPlanGainsAndLosses> changeMap = changeList.stream().collect(Collectors.toMap(x -> x.getCustomerCode() + x.getOrgCode(), v -> v, (n, o) -> n));
            for (MarketPlanChangeGainsAndLossesVo data : dataList) {
                String key = data.getCustomerCode() + data.getOrgCode();
                if (changeMap.containsKey(key)) {
                    MarketingPlanGainsAndLosses entity = changeMap.get(key);
                    data.setChangeProfitRatio(entity.getProfitRatio());
                    data.setChangePlanIncome(entity.getPlanIncome());
                    data.setChangePlanRatio(entity.getPlanRatio());
                    data.setChangeMarketingCost(entity.getMarketingCost());
                    data.setChangeGrossProfitRatio(entity.getGrossProfitRatio());
                }
            }
        }

        for (MarketPlanChangeGainsAndLossesVo data : dataList) {
            data.setPlanIncome(ObjectUtils.defaultIfNull(data.getPlanIncome(), BigDecimal.ZERO));
            data.setChangePlanIncome(ObjectUtils.defaultIfNull(data.getChangePlanIncome(), BigDecimal.ZERO));
            data.setPlanRatio(ObjectUtils.defaultIfNull(data.getPlanRatio(), BigDecimal.ZERO));
            data.setProfitRatio(ObjectUtils.defaultIfNull(data.getProfitRatio(), BigDecimal.ZERO));
            data.setGrossProfitRatio(ObjectUtils.defaultIfNull(data.getGrossProfitRatio(), BigDecimal.ZERO));
            data.setMarketingCost(ObjectUtils.defaultIfNull(data.getMarketingCost(), BigDecimal.ZERO));
            data.setChangePlanRatio(ObjectUtils.defaultIfNull(data.getChangePlanRatio(), BigDecimal.ZERO));
            data.setChangeProfitRatio(ObjectUtils.defaultIfNull(data.getChangeProfitRatio(), BigDecimal.ZERO));
            data.setChangeGrossProfitRatio(ObjectUtils.defaultIfNull(data.getChangeGrossProfitRatio(), BigDecimal.ZERO));
            data.setChangeMarketingCost(ObjectUtils.defaultIfNull(data.getChangeMarketingCost(), BigDecimal.ZERO));

            data.setDifferencePlanIncome(data.getChangePlanIncome().subtract(data.getPlanIncome()));
            data.setDifferencePlanRatio(data.getChangePlanRatio().subtract(data.getPlanRatio()));
            data.setDifferenceProfitRatio(data.getChangeProfitRatio().subtract(data.getProfitRatio()));
            data.setDifferenceGrossProfitRatio(data.getChangeGrossProfitRatio().subtract(data.getGrossProfitRatio()));
            data.setDifferenceMarketingCost(data.getChangeMarketingCost().subtract(data.getMarketingCost()));

            data.setPlanRatioStr(data.getPlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
            data.setProfitRatioStr(data.getProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
            data.setGrossProfitRatioStr(data.getGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
            data.setChangePlanRatioStr(data.getChangePlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
            data.setChangeProfitRatioStr(data.getChangeProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
            data.setChangeGrossProfitRatioStr(data.getChangeGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
//            if (data.getDifferencePlanRatio().compareTo(BigDecimal.ZERO) != 0) {
//                data.setDifferencePlanRatioStr(data.getDifferencePlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
//            }
//            if (data.getDifferenceProfitRatio().compareTo(BigDecimal.ZERO) != 0) {
//                data.setDifferenceProfitRatioStr(data.getDifferenceProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
//            }
//            if (data.getDifferenceGrossProfitRatio().compareTo(BigDecimal.ZERO) != 0) {
//                data.setDifferenceGrossProfitRatioStr(data.getDifferenceGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
//            }
            data.setDifferencePlanRatioStr(data.getDifferencePlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
            data.setDifferenceProfitRatioStr(data.getDifferenceProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
            data.setDifferenceGrossProfitRatioStr(data.getDifferenceGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
        }
        List<MarketPlanChangeGainsAndLossesVo> removeList = dataList.stream().filter(data -> data.getDifferencePlanRatio().compareTo(BigDecimal.ZERO) == 0
                && data.getDifferenceProfitRatio().compareTo(BigDecimal.ZERO) == 0
                && data.getDifferenceGrossProfitRatio().compareTo(BigDecimal.ZERO) == 0
                && data.getDifferencePlanIncome().compareTo(BigDecimal.ZERO) == 0
                && data.getDifferenceMarketingCost().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(removeList)) {
            dataList.removeAll(removeList);
        }
        return dataList;
    }


    @Resource
    private MdmCostCenterVoService costCenterVoService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeCalCustomerGains(String schemeCode) {
        this.calCustomerGainsAndLosses(schemeCode, Boolean.FALSE);
    }

    /**
     * 计算客户损益预测
     *
     * @param schemeCode
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MarketingPlanGainsAndLossesVo> calCustomerGains(String schemeCode) {
        return calCustomerGainsAndLosses(schemeCode, Boolean.TRUE);
    }


    protected List<MarketingPlanGainsAndLossesVo> calCustomerGainsAndLosses(String schemeCode, Boolean isCalContract) {
        MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(null, schemeCode);
        Validate.isTrue(ObjectUtils.isNotEmpty(plan), "当前方案不存在");
//        if (StringUtils.equalsAny(plan.getProcessStatus(), ProcessStatusEnum.COMMIT.getDictCode(), ProcessStatusEnum.PASS.getDictCode())) {
//            return Lists.newArrayList();
//        }
        String years = plan.getYears();
        List<MarketingPlanCase> caseList = caseRepository.findListBySchemeCodes(Lists.newArrayList(schemeCode));
        //过滤有空值的情况
        caseList = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()) && ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()) && ObjectUtils.isNotEmpty(x.getYears()) && ObjectUtils.isNotEmpty(x.getApplyAmount())).collect(Collectors.toList());
        List<String> indexKeyList = caseList.stream().map(x -> x.getCustomerCode() + ":" + x.getBelongDepartmentCode() + ":" + years).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(indexKeyList)) {
            return Lists.newArrayList();
        }
        if (isCalContract) {
            //计算合同的申请费用
            contractCalComponent.calContractApplyAmountByCaseList(caseList, plan.getYears());
        }
        List<String> customerCodes = caseList.stream().map(MarketingPlanCase::getCustomerCode).distinct().collect(Collectors.toList());

        List<String> cusOrgCodes = caseList.stream().map(x -> x.getCustomerCode() + x.getBelongDepartmentCode()).distinct().collect(Collectors.toList());

        //销售计划
        List<MarketingSalesPlanVo> salePlanList = marketingSalesPlanService.findListBySchemeCodes(Lists.newArrayList(schemeCode));

        List<String> differenceList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(salePlanList)) {
            //查询成本中心
            List<String> costCenterCodes = salePlanList.stream().map(MarketingSalesPlanVo::getCostCenterCode).distinct().collect(Collectors.toList());
            List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(costCenterCodes);
            Map<String, MdmCostCenterOrgVo> costCenterMap = costCenterVoList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getOrgList())).collect(Collectors.toMap(x -> x.getCostCenterCode(), y -> y.getOrgList().get(0)));
            for (MarketingSalesPlanVo vo : salePlanList) {
                if (costCenterMap.containsKey(vo.getCostCenterCode())) {
                    MdmCostCenterOrgVo costCenterOrgVo = costCenterMap.get(vo.getCostCenterCode());
                    vo.setCostCenterOrgCode(costCenterOrgVo.getOrgCode());
                    vo.setCostCenterOrgName(costCenterOrgVo.getOrgName());
                }
            }
            List<String> salesCustomerOrgList = salePlanList.stream().map(x -> x.getCustomerCode() + x.getCostCenterOrgCode()).distinct().collect(Collectors.toList());
            differenceList = salesCustomerOrgList.stream().filter(x -> !cusOrgCodes.contains(x)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(differenceList)) {
                customerCodes.addAll(salePlanList.stream().map(x -> x.getCustomerCode()).distinct().collect(Collectors.toList()));
            }
        }


        List<CustomerVo> customerVoList = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerMap = customerVoList.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
        List<MarketingPlanGainsAndLossesVo> dataList = new ArrayList<>();
        for (String s : indexKeyList) {
            String[] keys = s.split(":");
            MarketingPlanGainsAndLossesVo data = new MarketingPlanGainsAndLossesVo();
            data.setCalFlag(Boolean.TRUE);
            data.setIndexKey(s);
            //基础数据设置
            data.setCustomerCode(keys[0]);
            data.setOrgCode(keys[1]);
            data.setYears(keys[2]);
            CustomerVo customerVo = customerMap.get(data.getCustomerCode());
            data.setCustomerName(customerVo.getCustomerName());
            data.setErpCode(customerVo.getErpCode());
            data.setCompanyCode(customerVo.getCompanyCode());
            data.setChannelCode(customerVo.getChannelCode());
            data.setProductGroupCode(customerVo.getProductGroupCode());
            dataList.add(data);
        }


        if (CollectionUtils.isNotEmpty(differenceList)) {
            List<String> differenceCusOrgList = differenceList;
            Map<String, Set<String>> cusCostCenterMap = salePlanList.stream().filter(x -> differenceCusOrgList.contains(x.getCustomerCode() + x.getCostCenterOrgCode())).collect(Collectors.groupingBy(x -> x.getCustomerCode() + x.getCostCenterOrgCode(), Collectors.mapping(MarketingSalesPlanVo::getCostCenterCode, Collectors.toSet())));

            Map<String, MarketingSalesPlanVo> salesCusCostCenterMap = salePlanList.stream().filter(x -> differenceCusOrgList.contains(x.getCustomerCode() + x.getCostCenterOrgCode())).collect(Collectors.toMap(x -> x.getCustomerCode() + ":" + x.getCostCenterCode(), Function.identity(), (a, b) -> a));

            for (Map.Entry<String, MarketingSalesPlanVo> entry : salesCusCostCenterMap.entrySet()) {
                String[] key = entry.getKey().split(":");
                String customerCode = key[0];
                String costCenterCdoe = key[1];

                MarketingPlanGainsAndLossesVo data = new MarketingPlanGainsAndLossesVo();
                data.setIndexKey(customerCode + ":" + entry.getValue().getCostCenterOrgCode() + ":" + years);
                data.setCustomerCode(customerCode);
                data.setOrgCode(entry.getValue().getCostCenterOrgCode());
                data.setOrgName(entry.getValue().getCostCenterOrgName());
                data.setYears(years);
                CustomerVo customerVo = customerMap.get(data.getCustomerCode());
                data.setCustomerName(customerVo.getCustomerName());
                data.setErpCode(customerVo.getErpCode());
                data.setCompanyCode(customerVo.getCompanyCode());
                data.setChannelCode(customerVo.getChannelCode());
                data.setProductGroupCode(customerVo.getProductGroupCode());
                data.setCalFlag(Boolean.FALSE);
                data.setCostCenterCodes(cusCostCenterMap.get(customerCode + data.getOrgCode()));
                dataList.add(data);
            }
        }

        MarketingPlanCustomerGainsAbstractBuilder.builder(context, dataList, plan.getOriginalSchemeCode(), Sets.newHashSet(customerCodes))
                .init(Lists.newArrayList(schemeCode), years)
                .loadInitData().calPublicShareRatio()
                .calProfitRatio()
                .calGrossProfitRatio()
                .calLogisticsRatio()
                .calMarketingRatio()
                .calSecondCategory();
        dataList.forEach(vo -> {
            if (ObjectUtils.isNotEmpty(vo.getPlanIncome())) {
                vo.setPlanIncome(vo.getPlanIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetIncome())) {
                vo.setBudgetIncome(vo.getBudgetIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanTotalAmount())) {
                vo.setPlanTotalAmount(vo.getPlanTotalAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetTotalAmount())) {
                vo.setBudgetTotalAmount(vo.getBudgetTotalAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanProfitMargin())) {
                vo.setPlanProfitMargin(vo.getPlanProfitMargin().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanGrossProfitMargin())) {
                vo.setPlanGrossProfitMargin(vo.getPlanGrossProfitMargin().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
        });
        gainsAndLossesService.saveBatchList(dataList, schemeCode);
        //保存变更对比
        compareChangeGainsAndLosses(dataList, plan.getOriginalSchemeCode(), plan);
        return dataList;
    }


    /**
     * 获取单方案数据
     *
     * @param schemeCode
     * @return
     */
    @Override
    public List<MarketingPlanSchemeEstimationVo> getMarketingPlanSchemeEstimation(String schemeCode) {
        String key = MarketingPlanConstant.getTpmMarketingSchemeEstimateKey(schemeCode);
        if (redisTemplate.opsForHash().hasKey(key, MarketingPlanConstant.TPM_MARKETING_SCHEME_ESTIMATE)) {
            List<MarketingPlanSchemeEstimationVo> dataList = (List<MarketingPlanSchemeEstimationVo>) redisTemplate.opsForHash().get(key, MarketingPlanConstant.TPM_MARKETING_SCHEME_ESTIMATE);
            redisTemplate.opsForHash().delete(key, MarketingPlanConstant.TPM_MARKETING_SCHEME_ESTIMATE);
            return dataList;
        }
        return null;
    }

    /**
     * 营销方案变更对比
     *
     * @param dataList
     * @param originalSchemeCode
     */
    protected void compareChangeGainsAndLosses(List<MarketingPlanGainsAndLossesVo> dataList, String originalSchemeCode, MarketingPlan plan) {
        if (!MarketingPlanSchemeTypeEnum.change.getCode().equals(plan.getSchemeType())) return;
        MarketingChangeGainsAndLossesCompareVo vo = new MarketingChangeGainsAndLossesCompareVo();
        //查询原方案的客户损益
        List<MarketingPlanGainsAndLosses> gainsAndLossesList = gainsAndLossesService.findListBySchemeCodes(Lists.newArrayList(originalSchemeCode));
        //转换原方案的金额信息
//        gainsAndLossesList.forEach(k -> {
//            if (ObjectUtils.isNotEmpty(k.getPlanTotalAmount())) {
//                k.setPlanTotalAmount(k.getPlanTotalAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
//            }
//            if (ObjectUtils.isNotEmpty(k.getPlanProfitMargin())) {
//                k.setPlanProfitMargin(k.getPlanProfitMargin().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
//            }
//            if (ObjectUtils.isNotEmpty(k.getPlanGrossProfitMargin())) {
//                k.setPlanGrossProfitMargin(k.getPlanGrossProfitMargin().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
//            }
//        });
        //规划收入
        BigDecimal planIncome = gainsAndLossesList.stream().map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //规划总额
        BigDecimal planTotalAmount = gainsAndLossesList.stream().map(x -> Optional.ofNullable(x.getPlanTotalAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //规划利润额
        BigDecimal planProfitMargin = gainsAndLossesList.stream().map(x -> Optional.ofNullable(x.getPlanProfitMargin()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //规划毛利额
        BigDecimal planGrossProfitMargin = gainsAndLossesList.stream().map(x -> Optional.ofNullable(x.getPlanGrossProfitMargin()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //规划费率
        BigDecimal planCostRatio = BigDecimal.ZERO;
        //利润率
        BigDecimal profitMargin = BigDecimal.ZERO;
        //毛利率
        BigDecimal grossMargin = BigDecimal.ZERO;
        if (planIncome.compareTo(BigDecimal.ZERO) == 1) {
            planCostRatio = planTotalAmount.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN);
            profitMargin = planProfitMargin.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN);
            grossMargin = planGrossProfitMargin.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN);
        }
        vo.setPlanIncome(planIncome);
        vo.setPlanCostRatio(planCostRatio);
        vo.setProfitMargin(profitMargin);
        vo.setGrossMargin(grossMargin);
        vo.setPlanCostRatioStr(planCostRatio.multiply(BigDecimal.valueOf(100)) + "%");
        vo.setProfitMarginStr(profitMargin.multiply(BigDecimal.valueOf(100)) + "%");
        vo.setGrossMarginStr(grossMargin.multiply(BigDecimal.valueOf(100)) + "%");

        //变更客户损益
        //规划收入
        BigDecimal changePlanIncome = dataList.stream().map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //规划总额
        BigDecimal changePlanTotalAmount = dataList.stream().map(x -> Optional.ofNullable(x.getPlanTotalAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //规划利润额
        BigDecimal changePlanProfitMargin = dataList.stream().map(x -> Optional.ofNullable(x.getPlanProfitMargin()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //规划毛利额
        BigDecimal changePlanGrossProfitMargin = dataList.stream().map(x -> Optional.ofNullable(x.getPlanGrossProfitMargin()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //规划费率
        BigDecimal changePlanCostRatio = BigDecimal.ZERO;
        //利润率
        BigDecimal changeProfitMargin = BigDecimal.ZERO;
        //毛利率
        BigDecimal changeGrossMargin = BigDecimal.ZERO;
        if (changePlanIncome.compareTo(BigDecimal.ZERO) == 1) {
            changePlanCostRatio = changePlanTotalAmount.divide(changePlanIncome, 4, BigDecimal.ROUND_HALF_DOWN);
            changeProfitMargin = changePlanProfitMargin.divide(changePlanIncome, 4, BigDecimal.ROUND_HALF_DOWN);
            changeGrossMargin = changePlanGrossProfitMargin.divide(changePlanIncome, 4, BigDecimal.ROUND_HALF_DOWN);
        }
        vo.setChangePlanIncome(changePlanIncome);
        vo.setChangePlanCostRatio(changePlanCostRatio);
        vo.setChangeProfitMargin(changeProfitMargin);
        vo.setChangeGrossMargin(changeGrossMargin);
        vo.setChangePlanCostRatioStr(changePlanCostRatio.multiply(BigDecimal.valueOf(100)) + "%");
        vo.setChangeProfitMarginStr(changeProfitMargin.multiply(BigDecimal.valueOf(100)) + "%");
        vo.setChangeGrossMarginStr(changeGrossMargin.multiply(BigDecimal.valueOf(100)) + "%");

        //差异
        BigDecimal differencePlanIncome = changePlanIncome.subtract(planIncome);
        BigDecimal differencePlanCostRatio = changePlanCostRatio.subtract(planCostRatio);
        BigDecimal differenceProfitMargin = changeProfitMargin.subtract(profitMargin);
        BigDecimal differenceGrossMargin = changeGrossMargin.subtract(grossMargin);
        vo.setDifferencePlanIncome(differencePlanIncome);
        vo.setDifferencePlanCostRatio(differencePlanCostRatio);
        vo.setDifferenceProfitMargin(differenceProfitMargin);
        vo.setDifferenceGrossMargin(differenceGrossMargin);
        vo.setDifferencePlanCostRatioStr(differencePlanCostRatio.multiply(BigDecimal.valueOf(100)) + "%");
        vo.setDifferenceProfitMarginStr(differenceProfitMargin.multiply(BigDecimal.valueOf(100)) + "%");
        vo.setDifferenceGrossMarginStr(differenceGrossMargin.multiply(BigDecimal.valueOf(100)) + "%");

        plan.setCompareChangeGainsAndLosses(JSONObject.toJSONString(vo));
        redisService.set(MarketingPlanConstant.getMarketingChangeSchemeLock(plan.getSchemeCode()), JSONObject.toJSONString(vo), 18000);
        marketingPlanRepository.updateById(plan);
    }


    /**
     * 计算单方案的营销测算数据
     *
     * @param schemeCode
     * @return
     */
    public List<MarketingPlanSchemeEstimationVo> calMarketingPlanSchemeEstimation(String schemeCode) {
        MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(null, schemeCode);
        Validate.isTrue(ObjectUtils.isNotEmpty(plan), "当前方案不存在");
        List<RegionCollectMarketingEstimationVo> marketingSchemeEstimationVos = MarketingSchemeEstimationAbstractBuilder.builder(context, plan.getDepartmentCode())
                .init(Lists.newArrayList(schemeCode), plan.getYears())
                .estimationRevenue().productionCost().grossProfitMargin()
                .productTransportCost()
                .peripheryTransport()
                .transportTotal()
                .categoryCost()
                .marketingCostTotal()
                .marginalContribution()
                .artificialTravelOnBusiness()
                .costTotal()
                .profit()
                .costShift()
                .accessProfits()
                .getList();
        if (CollectionUtils.isEmpty(marketingSchemeEstimationVos)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanSchemeEstimationVo> dataList = (List<MarketingPlanSchemeEstimationVo>) nebulaToolkitService.copyCollectionByWhiteList(marketingSchemeEstimationVos, RegionCollectMarketingEstimationVo.class, MarketingPlanSchemeEstimationVo.class, HashSet.class, ArrayList.class);
        marketingPlanSchemeEstimationService.saveBatchList(dataList, plan.getSchemeCode());
        String key = MarketingPlanConstant.getTpmMarketingSchemeEstimateKey(schemeCode);
        redisTemplate.opsForHash().put(key, MarketingPlanConstant.TPM_MARKETING_SCHEME_ESTIMATE, dataList);
        return dataList;
    }


    /**
     * 查询变更方案-损益预测对比
     *
     * @param schemeCode
     * @return
     */
    @Override
    public MarketingChangeGainsAndLossesCompareVo findChangeGainsAndLossesCompare(String schemeCode) {
        MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(null, schemeCode);
        if (ObjectUtils.isNotEmpty(plan) && MarketingPlanSchemeTypeEnum.change.getCode().equals(plan.getSchemeType())) {
            MarketingChangeGainsAndLossesCompareVo vo = JSONObject.parseObject(plan.getCompareChangeGainsAndLosses(), MarketingChangeGainsAndLossesCompareVo.class);
            return vo;
        }
        return null;
    }

    /**
     * 通过组织查询营销方案 不包含变更
     *
     * @param orgCodes
     * @return
     */
    @Override
    public List<MarketingPlanVo> findMarketingPlanByOrgCodes(List<String> orgCodes, String years, List<String> schemeTypes, String confirmStatus) {
        List<MarketingPlan> dataList = marketingPlanRepository.findMarketingPlanByOrgCodes(orgCodes, years, schemeTypes, confirmStatus);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return (List<MarketingPlanVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, MarketingPlan.class, MarketingPlanVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 更新营销规划方案状态-只需要更新为审批通过
     *
     * @param schemeCodes
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMarketingPlanPassStatus(List<String> schemeCodes, String processStatus, String processNumber) {
        if (CollectionUtils.isNotEmpty(schemeCodes)) {
            marketingPlanRepository.updateMarketingPlanPass(schemeCodes, processStatus, processNumber);
            if (ProcessStatusEnum.PASS.getDictCode().equals(processStatus)) {
                for (String schemeCode : schemeCodes) {
                    MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, schemeCode);
                    //推送活动细案推送SFA
                    List<String> schemeDetailCodes = Lists.newArrayList();
                    if (entity == null) {
                        continue;
                    }
                    if (ObjectUtils.isNotEmpty(entity.getOriginalSchemeCode())) {
                        schemeDetailCodes = changeSchemeComponent.changeSchemeSyncOriginScheme(entity.getSchemeCode());
                    } else {
                        List<MarketingPlanCase> caseList = planCaseService.findListBySchemeCodes(Lists.newArrayList(entity.getSchemeCode()));
                        schemeDetailCodes = caseList.stream().map(MarketingPlanCase::getSchemeDetailCode).collect(Collectors.toList());
                    }

                    //推送活动到SFA
                    planCaseService.pushCaseListToSfa(schemeDetailCodes);
                    //TODO 还有一个物料推送DMS费用池上账
                    this.tpmMarketingPlanCaseComponent.operate(schemeDetailCodes, entity, null);
                    //自动结案
                    marketingAuditComponentService.autoAuditPlanCasePass(schemeDetailCodes);
                }
            }
        }
    }

    /**
     * 方案变更营销测算
     *
     * @param schemeCode
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MarketingPlanEstimationVo> calMarketingPlanEstimation(String schemeCode) {
        MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(null, schemeCode);
        Validate.notNull(plan.getOriginalSchemeCode(), "不是变更方案不可进行营销测算");
        MarketingPlan originalPlan = marketingPlanRepository.queryByIdOrSchemeCode(null, plan.getOriginalSchemeCode());
        String orgCode = plan.getDepartmentCode();
        List<String> orgCodeList = Lists.newArrayList();
        //判断如果是方案的 则通过当前组织找上级
        //通过原方案类型判断需要测算的维度
        String calOrgCode = null;
        if (MarketingPlanSchemeTypeEnum.plan.getCode().equals(originalPlan.getSchemeType())) {
            //查询变更方案的上级组织-大区
            List<OrgVo> orgVos = orgVoService.findAllParentByOrgCode(orgCode);
            Validate.isTrue(CollectionUtils.isNotEmpty(orgVos), "查询上级组织信息为空");
            Optional<OrgVo> optional = orgVos.stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType())).collect(Collectors.toList()).stream().findFirst();
            Validate.isTrue(optional.isPresent(), String.format("当前变更方案%s不存在上级大区组织", plan.getDepartmentName()));
            OrgVo orgVo = optional.get();
            //查询大区下面的所有区域
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(orgVo.getOrgCode());
            Validate.isTrue(CollectionUtils.isNotEmpty(orgVoList), String.format("查询当前组织%s下的组织信息为空", orgVo.getOrgCode()));
            orgCodeList = orgVoList.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
            calOrgCode = orgVo.getOrgCode();
        } else {
            calOrgCode = plan.getDepartmentCode();
            orgCodeList = Lists.newArrayList(plan.getDepartmentCode());
        }

        List<MarketingPlanVo> marketingPlanVoList = this.findMarketingPlanByOrgCodes(orgCodeList, plan.getYears(), null, BooleanEnum.TRUE.getCapital());
        if (CollectionUtils.isEmpty(marketingPlanVoList)) {
            return Lists.newArrayList();
        }
        List<String> schemeCodes = marketingPlanVoList.stream().filter(x -> !MarketingPlanSchemeTypeEnum.change.getCode().equals(x.getSchemeType()) && ProcessStatusEnum.PASS.getDictCode().equals(x.getProcessStatus())).map(MarketingPlanVo::getSchemeCode).collect(Collectors.toList());
        schemeCodes.add(plan.getSchemeCode());
        schemeCodes.remove(plan.getOriginalSchemeCode());
        log.error("变更方案对应查询的方案编码:{}", schemeCodes);
        //营销测算
        List<RegionCollectMarketingEstimationVo> marketingEstimationVos = regionCollectHelper.buildMarketingEstimation(schemeCodes, plan.getYears(), calOrgCode, plan.getOriginalSchemeCode(), plan.getSchemeCode(), null, null);
        if (CollectionUtils.isEmpty(marketingEstimationVos)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanEstimationVo> dataList = (List<MarketingPlanEstimationVo>) nebulaToolkitService.copyCollectionByWhiteList(marketingEstimationVos, RegionCollectMarketingEstimationVo.class, MarketingPlanEstimationVo.class, HashSet.class, ArrayList.class);
        marketingPlanEstimationService.saveBatchList(dataList, plan.getSchemeCode());
        return dataList;
    }

    /**
     * OTWO营销测算
     *
     * @param schemeCode
     * @return
     */
    @Override
    public List<MarketingPlanEstimationVo> calMarketingPlanEstimationOTwoO(String schemeCode) {
        MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(null, schemeCode);
        Validate.isTrue(StringUtils.equalsAny(plan.getSchemeType(), MarketingPlanSchemeTypeEnum.o_two_o.getCode()), "不是O2O方案不可进行营销测算");
//        String orgCode = plan.getDepartmentCode();
//        //查询变更方案的上级组织-大区
//        List<OrgVo> orgVos = orgVoService.findAllParentByOrgCode(orgCode);
//        Validate.isTrue(CollectionUtils.isNotEmpty(orgVos), "查询上级组织信息为空");
//        Optional<OrgVo> optional = orgVos.stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType()))
//                .collect(Collectors.toList()).stream().findFirst();
//        Validate.isTrue(optional.isPresent(), String.format("当前O2O方案%s不存在上级大区组织", plan.getDepartmentName()));
//        OrgVo orgVo = optional.get();
//        //查询大区下面的所有区域
//        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(orgVo.getOrgCode());
//        Validate.isTrue(CollectionUtils.isNotEmpty(orgVoList), String.format("查询当前组织%s下的组织信息为空", orgVo.getOrgCode()));
//        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
        List<MarketingPlanVo> marketingPlanVoList = this.findMarketingPlanByOrgCodes(Lists.newArrayList(plan.getDepartmentCode()), plan.getYears(), Lists.newArrayList(MarketingPlanSchemeTypeEnum.o_two_o.getCode()), BooleanEnum.TRUE.getCapital());
        if (CollectionUtils.isEmpty(marketingPlanVoList)) {
            return Lists.newArrayList();
        }
        List<String> schemeCodes = marketingPlanVoList.stream().map(MarketingPlanVo::getSchemeCode).collect(Collectors.toList());
        schemeCodes.add(plan.getSchemeCode());
        //营销测算
        List<RegionCollectMarketingEstimationVo> marketingEstimationVos = regionCollectHelper.buildMarketingEstimation(schemeCodes, plan.getYears(), plan.getDepartmentCode(), null, null, null, null);
        if (CollectionUtils.isEmpty(marketingEstimationVos)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanEstimationVo> dataList = (List<MarketingPlanEstimationVo>) nebulaToolkitService.copyCollectionByWhiteList(marketingEstimationVos, RegionCollectMarketingEstimationVo.class, MarketingPlanEstimationVo.class, HashSet.class, ArrayList.class);
        marketingPlanEstimationService.saveBatchList(dataList, plan.getSchemeCode());
        return dataList;
    }


    /**
     * OTWO营销测算
     *
     * @param schemeCode
     * @return
     */
    @Override
    public List<MarketingPlanEstimationVo> calMarketingPlanEstimationAdditional(String schemeCode) {
        MarketingPlan plan = marketingPlanRepository.queryByIdOrSchemeCode(null, schemeCode);
        Validate.isTrue(StringUtils.equalsAny(plan.getSchemeType(), MarketingPlanSchemeTypeEnum.additional.getCode()), "不是补录方案不可进行营销测算");
//        String orgCode = plan.getDepartmentCode();
//        //查询变更方案的上级组织-大区
//        List<OrgVo> orgVos = orgVoService.findAllParentByOrgCode(orgCode);
//        Validate.isTrue(CollectionUtils.isNotEmpty(orgVos), "查询上级组织信息为空");
//        Optional<OrgVo> optional = orgVos.stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType()))
//                .collect(Collectors.toList()).stream().findFirst();
//        Validate.isTrue(optional.isPresent(), String.format("当前O2O方案%s不存在上级大区组织", plan.getDepartmentName()));
//        OrgVo orgVo = optional.get();
//        //查询大区下面的所有区域
//        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(orgVo.getOrgCode());
//        Validate.isTrue(CollectionUtils.isNotEmpty(orgVoList), String.format("查询当前组织%s下的组织信息为空", orgVo.getOrgCode()));
//        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
        List<MarketingPlanVo> marketingPlanVoList = this.findMarketingPlanByOrgCodes(Lists.newArrayList(plan.getDepartmentCode()), plan.getYears(), Lists.newArrayList(MarketingPlanSchemeTypeEnum.additional.getCode()), BooleanEnum.TRUE.getCapital());
        if (CollectionUtils.isEmpty(marketingPlanVoList)) {
            return Lists.newArrayList();
        }
        List<String> schemeCodes = marketingPlanVoList.stream().map(MarketingPlanVo::getSchemeCode).collect(Collectors.toList());
        schemeCodes.add(plan.getSchemeCode());
        //营销测算
        List<RegionCollectMarketingEstimationVo> marketingEstimationVos = regionCollectHelper.buildMarketingEstimation(schemeCodes, plan.getYears(), plan.getDepartmentCode(), null, null, null, null);
        if (CollectionUtils.isEmpty(marketingEstimationVos)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanEstimationVo> dataList = (List<MarketingPlanEstimationVo>) nebulaToolkitService.copyCollectionByWhiteList(marketingEstimationVos, RegionCollectMarketingEstimationVo.class, MarketingPlanEstimationVo.class, HashSet.class, ArrayList.class);
        marketingPlanEstimationService.saveBatchList(dataList, plan.getSchemeCode());
        return dataList;
    }


    /**
     * 计算品项
     *
     * @param schemeCode
     * @return
     */
    @Override
    public List<MarketingPlanItemEstimationVo> calMarketingPlanItemEstimation(String schemeCode) {
        List<String> schemeCodes = Lists.newArrayList(schemeCode);
        MarketingPlan marketingPlan = marketingPlanRepository.queryByIdOrSchemeCode(null, schemeCode);
        String years = marketingPlan.getYears();
        String orgCode = marketingPlan.getOrgCode();
        List<MarketingPlanCase> caseList = caseRepository.findListBySchemeCodes(schemeCodes);
        List<MarketingPlanProduct> planProductList = productRepository.findListBySchemeCodes(schemeCodes);
        if (CollectionUtils.isEmpty(planProductList)) {
            return Lists.newArrayList();
        }
        Map<String, List<MarketingPlanProduct>> planProductMap = planProductList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
        for (MarketingPlanCase planCase : caseList) {
            planCase.setProductAndItemList(planProductMap.get(planCase.getSchemeDetailCode()));
        }
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findListBySchemeCodes(schemeCodes);
        //过滤有空值的情况
        caseList = caseList.stream().filter(x -> {
            Boolean flag = Boolean.FALSE;
            if (ObjectUtils.isNotEmpty(x.getYears()) && years.equals(x.getYears())) {
                if (MarketingPlanCaseTypeEnum.back.getCode().equals(x.getCaseType())) {
                    if (CollectionUtils.isNotEmpty(x.getFeeBelongItemList())) {
                        flag = Boolean.TRUE;
                    }
                } else if (CollectionUtils.isNotEmpty(x.getItemList())) {
                    flag = Boolean.TRUE;
                }
            }
            return flag;
        }).collect(Collectors.toList());
        List<String> indexKeyList = salesPlanVoList.stream().map(x -> x.getItemCode() + ":" + x.getItemName() + ":" + years)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(indexKeyList)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanItemEstimationVo> dataList = new ArrayList<>(indexKeyList.size());
        for (String s : indexKeyList) {
            String[] keys = s.split(":");
            MarketingPlanItemEstimationVo data = new MarketingPlanItemEstimationVo();
            String key = keys[0] + ":" + keys[2];
            data.setIndexKey(key);
            data.setOrgCode(orgCode);
            //基础数据设置
            data.setItemCode(keys[0]);
            data.setItemName(keys[1]);
            data.setYears(keys[2]);
            dataList.add(data);
        }
        ItemEstimationAbstractBuilder.builder(context, dataList, caseList, orgCode)
                .init(schemeCodes, years)
                .loadInitData()
                .calProfitRatio()
                .calGrossProfitRatio()
                .calLogisticsRatio()
                .calMarketingRatio()
                .calPublicShareRatio()
                .calSecondCategory();
        marketingPlanItemEstimationService.saveBatchList(dataList, schemeCode);
        return dataList;
    }

    /**
     * 更新方案变更预算信息
     *
     * @param schemeCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateChangeSchemeBudget(String schemeCode) {
        marketingPlanBudgetService.deleteBySchemeCode(schemeCode);
        List<MarketingPlanCase> caseList = planCaseService.findListBySchemeCodes(Lists.newArrayList(schemeCode));
        //重新加载管控预算
        List<RegionCollectBudgetVo> budgetVoList = regionCollectControlBudgetComponent.reloadRegionCollectControlBudget(caseList, schemeCode);
        List<MarketingPlanBudget> planBudgets = budgetVoList.stream().map(x -> {
            MarketingPlanBudget budget = nebulaToolkitService.copyObjectByBlankList(x, MarketingPlanBudget.class, HashSet.class, ArrayList.class);
            budget.setSchemeCode(schemeCode);
            return budget;
        }).collect(Collectors.toList());
        marketingPlanBudgetService.saveBatchList(planBudgets, schemeCode);
    }

    /**
     * 流程撤回
     *
     * @param code
     * @param remark
     */
    @Override
    public void recover(String code, String remark) {
        MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, code);
        boolean beSuccess;
        if (MarketingPlanSchemeTypeEnum.append.getCode().equals(entity.getSchemeType())) {
            beSuccess = marketingPlanAppendOaService.oaWithdraw(code, remark);
        } else {
            beSuccess = marketingPlanOaService.oaWithdraw(code, remark);
        }
        if (beSuccess) {
            entity.setProcessStatus(ProcessStatusEnum.RECOVER.getDictCode());
            entity.setSchemeStatus(ProcessStatusEnum.RECOVER.getDictCode());
            marketingPlanRepository.saveOrUpdate(entity);
        } else {
            Validate.isTrue(false, "流程撤回失败");
        }
    }

    @Override
    public List<MarketingPlanVo> findMarketingPlanCommit(String yearMonthLy) {
        return marketingPlanRepository.findMarketingPlanCommit(yearMonthLy);
    }

    private Map<String, List<MarketingPlanCaseVo>> getCacheListToMap(String cacheKey) {
        Map<String, List<MarketingPlanCaseVo>> map = Maps.newHashMap();
        for (MarketingPlanCaseTypeEnum value : MarketingPlanCaseTypeEnum.values()) {
            String lastCacheKey = cacheKey + ":" + value.getCode();
            List<MarketingPlanCaseVo> data = super.findCacheList(lastCacheKey);
            if (CollectionUtils.isNotEmpty(data)) {
                for (MarketingPlanCaseVo x : data) {
                    x.setCaseType(value.getCode());
                }
                map.put(value.getCode(), data);
            }
        }
        return map;
    }


    @Override
    public void addItemCache(String cacheKey, List<MarketingPlanCaseVo> itemList) {
        String[] splits = cacheKey.split(":");
        Validate.isTrue(splits.length == 2, "没有方案明细类型");
        super.addItemCache(cacheKey, itemList);
    }


    @Override
    public void copyItemListCache(String cacheKey, List<MarketingPlanCaseVo> itemList) {
        String[] splits = cacheKey.split(":");
        Validate.isTrue(splits.length == 2, "没有方案明细类型");
        super.copyItemListCache(cacheKey, itemList);
    }

    @Override
    public Page<MarketingPlanCaseVo> findCachePageList(Pageable pageable, MarketingPlanCaseVo dto, String cacheKey) {
        String[] splits = cacheKey.split(":");
        Validate.isTrue(splits.length == 2, "没有方案明细类型");

        String redisCacheIdKey = helper.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = helper.getRedisCacheDataKey(cacheKey);
        String redisCacheInitKey = helper.getRedisCacheInitKey(cacheKey);
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        page.setTotal(0);
        page.setRecords(Lists.newArrayList());
        //判断是否已经重新载入缓存
        String allRedisCacheIdKey = helper.getRedisCacheIdKey(splits[0]);
        Set<String> keys = redisTemplate.keys(allRedisCacheIdKey + "*");
        Boolean flag = Boolean.FALSE;
        if (CollectionUtils.isNotEmpty(keys)) {
            flag = Boolean.TRUE;
        }

        a:
        if (redisService.hasKey(redisCacheIdKey)) {
            //redis里面有的话直接从redis里面取
            Long total = redisService.lSize(redisCacheIdKey);
            page.setTotal(total);
            List<Object> idList = redisService.lRange(redisCacheIdKey, page.offset(), page.offset() + page.getSize() - 1);
            if (!org.springframework.util.CollectionUtils.isEmpty(idList)) {
                List<MarketingPlanCaseVo> dataList = redisTemplate.opsForHash().multiGet(redisCacheDataKey, idList);
                List<MarketingPlanCaseVo> dtoList = dataList;
                if (ObjectUtils.isNotEmpty(dto.getCustomerName())) {
                    dtoList = dataList.stream().filter(x -> x.getCustomerName().contains(dto.getCustomerName())).collect(Collectors.toList());
                }
                List<MarketingPlanCaseVo> voList = helper.dtoListToVoList(dtoList);
                page.setRecords(voList);
            }
        } else if (!redisService.hasKey(redisCacheInitKey) && null != dto && !flag) {
            //标记为已初始化，不重复初始化
            redisService.set(redisCacheInitKey, BooleanEnum.TRUE, helper.getExpireTime());
            //放到try catch里面 是为了以防分页从数据库查询报错了 初始化状态被确认 修正报错过后查询为空的情况
            try {
                //redis里面没有
                List<MarketingPlanCaseVo> dtoList = helper.findDtoListFromRepository(dto, cacheKey);

                if (org.springframework.util.CollectionUtils.isEmpty(dtoList)) {
                    redisService.del(redisCacheInitKey);
                    break a;
                }
                assembleChannelType(dtoList);

                if (helper.initToCacheFromRepository()) {
                    helper.putCache(cacheKey, dtoList);
                }

                //放到缓存里面
                page.setTotal(dtoList.size());
                long start = page.offset();
                if (page.getTotal() > start) {
                    long end = page.offset() + page.getSize();
                    if (page.getTotal() < end) {
                        end = page.getTotal();
                    }
                    List<MarketingPlanCaseVo> recordDtoList = dtoList.subList((int) page.offset(), (int) end);
                    List<MarketingPlanCaseVo> voList = helper.dtoListToVoList(recordDtoList);
                    page.setRecords(voList);
                }
            } catch (Exception e) {
                redisService.del(redisCacheInitKey);
            }
        }
        //更新下VO里面的字段值
        helper.fillVoListProperties(page.getRecords());
        return page;
    }

    private void assembleChannelType(List<MarketingPlanCaseVo> dtoList) {
        List<String> customerCodes = dtoList.stream().map(MarketingPlanCaseVo::getCustomerCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, String> customerMap = customerVos.stream().filter(e -> StringUtils.isNotEmpty(e.getCustomerCode()) && Objects.nonNull(e.getChannelType()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, CustomerVo::getChannelType, (v1, v2) -> v1));
        dtoList.forEach(e -> {
            String customerCode = e.getCustomerCode();
            String channelTypeStr = customerMap.get(customerCode);
            if (StringUtils.isNotEmpty(channelTypeStr)) {
                List<String> channleTypeList = Arrays.asList(channelTypeStr.split(","));
                e.setChannelTypeList(channleTypeList);
                e.setChannelType(channelTypeStr);
            }
        });
    }


    /**
     * 保存缓存分页
     *
     * @param cacheKey       缓存key
     * @param saveList
     * @param loginUserCodes
     * @param userName
     */
    @SneakyThrows
    @Override
    public void planCaseSaveCurrentPageCache(String cacheKey, List<MarketingPlanCaseVo> saveList, String years, String schemeCode, String originalSchemeCode, List<String> loginUserCodes, String userName) {
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        String[] splits = cacheKey.split(":");
        Validate.isTrue(splits.length == 2, "没有方案明细类型");
        for (MarketingPlanCaseVo x : saveList) {
            x.setCaseType(splits[1]);
            //判断是后返
            if (MarketingPlanCaseTypeEnum.back.getCode().equals(x.getCaseType())) {
                x.setItemList(null);
                x.setProductList(null);
            } else {
                x.setFeeLevelList(null);
                x.setFeeProductList(null);
            }
        }
        MarketingCheckCaseComponent bean = ApplicationContextHolder.getContext().getBean(MarketingCheckCaseComponent.class);
        Future<List<MarketingPlanCaseVo>> future = bean.checkCaseList(saveList, splits[1], years, schemeCode, originalSchemeCode, splits[0], loginDetails, loginUserCodes, userName);

        List<MarketingPlanCaseVo> caseVoList = future.get();
//        //大方向校验
//        List<MarketingPlanCaseVo> caseVoList = planCaseService.checkPlanCaseList(saveList, years);
//        //分模板校验
//        for (MarketingPlanCaseStrategy strategy : strategyList) {
//            if (strategy.getCaseType().equals(splits[1])) {
//                strategy.checkCaseList(caseVoList, years, schemeCode, originalSchemeCode, splits[0]);
//            }
//        }
        String redisCacheDataKey = helper.getRedisCacheDataKey(cacheKey);
        String redisCacheIdKey = helper.getRedisCacheIdKey(cacheKey);
        if (org.springframework.util.CollectionUtils.isEmpty(caseVoList)) {
            return;
        }
        for (MarketingPlanCaseVo x : caseVoList) {
            if (ObjectUtils.isEmpty(x.getId())) {
                x.setId(UuidCrmUtil.general());
            }
        }
        List<Object> idList = redisService.lRange(redisCacheIdKey, 0, -1);
        Object[] newIdArr = caseVoList.stream().filter(x -> !idList.contains(x.getId())).map(helper::getDtoKey).toArray();
        if (newIdArr.length > 0) {
            redisService.lPushAll(redisCacheIdKey, helper.getExpireTime(), newIdArr);
            helper.updateItem(cacheKey, caseVoList);
        }
        Map<Object, MarketingPlanCaseVo> updateMap = caseVoList.stream().collect(Collectors.toMap(helper::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, helper.getExpireTime());
    }


    @Override
    public List<MarketingPlanCaseVo> findCacheList(String cacheKey) {
        List<MarketingPlanCaseVo> dataList = Lists.newArrayList();
        for (MarketingPlanCaseTypeEnum value : MarketingPlanCaseTypeEnum.values()) {
            String lastCacheKey = cacheKey + ":" + value.getCode();
            List<MarketingPlanCaseVo> data = super.findCacheList(lastCacheKey);
            if (CollectionUtils.isNotEmpty(data)) {
                for (MarketingPlanCaseVo x : data) {
                    x.setCaseType(value.getCode());
                }
                dataList.addAll(data);
            }
        }
        return dataList;
    }


    @Override
    public void deleteCacheList(String cacheKey, List<MarketingPlanCaseVo> itemList) {
        String[] splits = cacheKey.split(":");
        Validate.isTrue(splits.length == 2, "没有方案明细类型");
        super.deleteCacheList(cacheKey, itemList);
    }


    @Resource
    private MarketingPlanMapper marketingPlanMapper;

    @Override
    public void updateCustomerCooperateType() {
        marketingPlanMapper.updateCustomerCooperateType();
        marketingPlanMapper.updateCustomerCooperateTypeByAudit();
    }


    @Override
    public List<MarketingPlanVo> findListBySchemeCodes(List<String> schemeCodes) {
        List<MarketingPlan> marketingPlans = marketingPlanRepository.queryBySchemeCodes(Sets.newHashSet(schemeCodes));
        return JsonUtils.convert(marketingPlans, List.class, MarketingPlanVo.class);
    }

    @Override
    public void calMarketingPlanCaseList(String cacheKey, String years, String schemeCode, String originalSchemeCode) {
        Map<String, List<MarketingPlanCaseVo>> caseDetailMap = getCacheListToMap(cacheKey);
        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : caseDetailMap.entrySet()) {
            final String lastCacheKey = cacheKey + ":" + entry.getKey();
            this.planCaseSaveCurrentPageCache(lastCacheKey, entry.getValue(), years, schemeCode, originalSchemeCode);
        }
    }
}
