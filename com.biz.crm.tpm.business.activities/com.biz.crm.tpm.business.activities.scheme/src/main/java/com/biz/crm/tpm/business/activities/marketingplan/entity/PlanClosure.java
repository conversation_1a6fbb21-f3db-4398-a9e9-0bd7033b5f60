package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.WorkflowFlagOpEntity;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureDetailVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_plan_closure")
@Table(
        name = "tpm_plan_closure",
        indexes = {
                @Index(name = "tpm_plan_closure_index0", columnList = "tenant_code,close_code", unique = true),
                @Index(name = "tpm_plan_closure_index2", columnList = "del_flag")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_plan_closure", comment = "方案规划关闭")
@ApiModel(value = "PlanClosure", description = "方案规划关闭")
public class PlanClosure extends WorkflowFlagOpEntity {

    @ApiModelProperty("关闭编码")
    @Column(name = "close_code", columnDefinition = "varchar(32) comment '关闭编码'")
    private String closeCode;

    @ApiModelProperty("关闭名称")
    @Column(name = "close_name", columnDefinition = "varchar(64) comment '关闭名称'")
    private String closeName;

    @ApiModelProperty("关闭原因")
    @Column(name = "close_reason", columnDefinition = "varchar(500) comment '关闭原因'")
    private String closeReason;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("来源类型")
    @Column(name = "source_type", columnDefinition = "varchar(20) comment '来源类型'")
    private String sourceType;

    @ApiModelProperty("审批单号")
    @Column(name = "process_number", columnDefinition = "varchar(64) COMMENT '审批单号'")
    private String processNumber;

    @ApiModelProperty("推送日期")
    @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    @Column(name = "oa_id", columnDefinition = "varchar(32) COMMENT 'OA人员id'")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    @Column(name = "oa_user_name", columnDefinition = "varchar(32) COMMENT 'OA人员账号'")
    private String oaUserName;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("回传TPM接口唯一标识（接口用）")
    private String businessCode;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("url")
    private String tpmUrl;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("closeAmount")
    private BigDecimal closeAmount;

    @Transient
    @TableField(exist = false)
    List<PlanClosureDetailVo> detailVos;
}
