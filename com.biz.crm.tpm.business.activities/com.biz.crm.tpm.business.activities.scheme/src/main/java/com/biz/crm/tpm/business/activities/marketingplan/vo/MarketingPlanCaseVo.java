package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 15:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MarketingPlanCase", description = "营销方案-销售计划")
public class MarketingPlanCaseVo extends UuidFlagOpVo {

    private static final long serialVersionUID = 3773874470817795546L;

    @ApiModelProperty("大区编码")
    private String regionCode;

    @ApiModelProperty("大区名称")
    private String regionName;

    @ApiModelProperty("方案明细类型")
    private String caseType;

    @ApiModelProperty("方案明细类型名称")
    private String caseTypeName;

    private List<String> excludeCaseTypeList;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案类型")
    private String schemeType;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    private String actExecuteCode;

    @ApiModelProperty("原方案明细编码")
    private String originalSchemeDetailCode;

    @ApiModelProperty("关联统筹方案编码")
    private String releaseCode;

    @ApiModelProperty("关联统筹方案名称")
    private String releaseName;

    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCodeTwo;

    @ApiModelProperty("关联总部方案编码")
    private String headSchemeCode;

    @ApiModelProperty("关联总部方案编码集合")
    private List<String> headSchemeCodes;

    @ApiModelProperty("关联总部方案名称")
    private String headSchemeName;

    @ApiModelProperty("关联总部方案明细编码")
    private String headSchemeDetailCode;

    @ApiModelProperty("排序")
    private Long sort;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("二级费用大类名称")
    private String costTypeCategorySecondName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("活动年月")
    private String actYears;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("费用归属部门成本中心")
    private String belongCostCenterCodes;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("成本中心公司代码")
    private String costCenterCompanyCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("终端编码")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("终端类型")
    private String terminalType;

    @ApiModelProperty("终端渠道")
    private String terminalChannel;

    @ApiModelProperty("终端所属系统")
    private String terminalSystem;

    @ApiModelProperty("兑付方式")
    private String cashType;

    @ApiModelProperty("执行描述")
    private String executeDesc;

    @ApiModelProperty("申请费用")
    private BigDecimal applyAmount;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("不含税金额")
    private BigDecimal noTaxApplyAmount;

    @ApiModelProperty("搭赠物料成本金额")
    private BigDecimal policyMaterialCostPrice;

    @ApiModelProperty("搭赠数量")
    private BigDecimal giftQuantity;

    @ApiModelProperty("政策商品")
    private String policyProductCode;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("预估费率")
    private BigDecimal ratio;

    @ApiModelProperty("活动描述")
    private String actDesc;

    @ApiModelProperty("年度预算编码")
    private String budgetCode;

    @ApiModelProperty("费用依据")
    private String costBasis;

    @ApiModelProperty("费用依据集合")
    private List<String> costBasisList;

    @ApiModelProperty("执行示例")
    private String executeExample;

    @ApiModelProperty("执行示例集合")
    private List<String> executeExampleList;

    @ApiModelProperty("结案示例")
    private String closeCaseExample;

    @ApiModelProperty("结案示例集合")
    private List<String> closeCaseExampleList;

    @ApiModelProperty("合作类型")
    private String cooperateTypeStr;

    @ApiModelProperty("合作类型标签")
    private List<String> cooperateTypeList;

    @ApiModelProperty("客户标签")
    private String customerTagStr;

    @ApiModelProperty("客户标签")
    private List<String> customerTagList;

    @ApiModelProperty("终端标签")
    private String terminalTagStr;

    @ApiModelProperty("终端标签")
    private List<String> terminalTagList;

    @ApiModelProperty("校验")
    private Boolean checkFlag;

    @ApiModelProperty("错误描述")
    private String errMsg;

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("是否合同费用")
    private String isContractCost;

    @ApiModelProperty("返利类型")
    private String rebateType;

    @ApiModelProperty("返利计算日期")
    private Integer rebateCalDay;

    @ApiModelProperty("返利开始时间")
    private String rebateStartDate;

    @ApiModelProperty("返利结束时间")
    private String rebateEndDate;

    @ApiModelProperty("政策形式编码")
    private String conditionFormula;

    @ApiModelProperty("政策形式名称")
    private String conditionFormulaName;

    @ApiModelProperty("我方承担金额")
    private BigDecimal bearAmount;

    @ApiModelProperty("客户承担金额")
    private BigDecimal cusBearAmount;

    @ApiModelProperty("行销物料类型")
    private String sellMaterialType;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料数量")
    private BigDecimal materialNum;

    @ApiModelProperty("物料成本价格")
    private BigDecimal materialCostPrice;

    @ApiModelProperty("运输方式")
    private String transportType;

    @ApiModelProperty("投放平台")
    private String platform;

    @ApiModelProperty("投放城市")
    private String placingCity;

    @ApiModelProperty("投放城市名称")
    private String placingCityName;

    @ApiModelProperty("结案金额")
    private BigDecimal auditAmount;

    @ApiModelProperty("已结案金额（前端查询用）")
    private BigDecimal auditedAmount;

    @ApiModelProperty("结案状态")
    private String auditStatus;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmountF;

    @ApiModelProperty("兑付状态")
    private String cashStatus;

    @ApiModelProperty("计提金额")
    private BigDecimal withholdingAmount;

    @ApiModelProperty("是否选中")
    private String checked;

    @ApiModelProperty("条件数量")
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    private String giveNum;

    @ApiModelProperty("优惠数量-上限")
    private BigDecimal discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    private BigDecimal discountAmount;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("年月小于")
    private String yearsLess;

    @ApiModelProperty("结束时间小于")
    private String endDateLess;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("审批状态")
    private String processStatus;

    @ApiModelProperty("活动状态")
    private String actStatus;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("活动时间所在年月-OA接口用")
    private String planYears;

    @ApiModelProperty("创建人职位编码")
    private String positionCode;

    @ApiModelProperty("创建人组织编码")
    private String orgCode;

    @ApiModelProperty("创建人组织名称")
    private String orgName;

    @ApiModelProperty("多部门承担标记")
    private String muchDepartmentMark;

    @ApiModelProperty("是否多部门")
    private String muchDepartmentFlag;

    @ApiModelProperty("活动可结案金额")
    private BigDecimal availableAuditAmount;

    @ApiModelProperty("商品/小类编码")
    private String levelProductCode;

    private List<String> productCodeList;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("产品和品项范围-后台使用")
    private List<MarketingPlanProductVo> productAndItemList;

    @ApiModelProperty("核算产品范围-前端使用")
    private List<MarketingPlanProductVo> productList;

    @ApiModelProperty("核算品项范围-前端使用")
    private List<MarketingPlanProductVo> itemList;

    @ApiModelProperty("核算产品小类范围-前端使用")
    private List<MarketingPlanProductVo> levelList;

    @ApiModelProperty("费用产品范围-前端使用")
    private List<MarketingPlanProductVo> feeProductList;

    @ApiModelProperty("费用品项范围-前端使用")
    private List<MarketingPlanProductVo> feeItemList;

    @ApiModelProperty("费用产品小类范围-前端使用")
    private List<MarketingPlanProductVo> feeLevelList;

    @ApiModelProperty("费用归属品项-前端使用")
    private List<MarketingPlanProductVo> feeBelongItemList;

    @ApiModelProperty("是否变更查询方案明细")
    private String changeSchemeQueryFlag;

    @ApiModelProperty("原始方案编码")
    private String originalSchemeCode;

    @ApiModelProperty("排除完全结案的数据")
    private String excludeWholeAudit;

    @ApiModelProperty("推送状态（政策/费用池）")
    private String pushStatus;

    @ApiModelProperty("推送描述（政策/费用池）")
    private String pushMsg;

    @ApiModelProperty("是否计提查询")
    private String withholding;

    @ApiModelProperty("唯一关键字段")
    private String onlyKeys;

    @ApiModelProperty("活动材料")
    private List<CostTypeDetailCollectVo> collectList;

    @ApiModelProperty("核销材料")
    private List<CostTypeDetailCollectVo> approvalList;

    @ApiModelProperty("兑付方式")
    private Set<String> payBys;

    @ApiModelProperty("承接对象查询")
    private OverallPlanCaseVo bearCase;

    @ApiModelProperty("是否变更")
    private String changeFlag;

    @ApiModelProperty("是否可选择")
    private Boolean selectedFlag;

    @ApiModelProperty("dms单据号（促销政策编码/货补池编码）")
    private String dmsCode;

    @ApiModelProperty("是否计提")
    private String withHoldingStatus;

    @ApiModelProperty("完全兑付时间")
    private String wholeCashDate;

    @ApiModelProperty("合作类型")
    private String cooperateType;

    @ApiModelProperty("合作类型")
    private String hzlx;

    @ApiModelProperty("陈列卡板数")
    private BigDecimal displayCardNum;

    @ApiModelProperty("上上月POS")
    private BigDecimal lastUpMonthPos;

    @ApiModelProperty("上月POS")
    private BigDecimal lastMonthPos;

    @ApiModelProperty("当月pos")
    private BigDecimal monthPos;

    @ApiModelProperty("人员类型")
    private String staffType;

    @ApiModelProperty("出勤天数")
    private BigDecimal attendanceDay;

    @ApiModelProperty("底薪")
    private BigDecimal basicSalary;

    @ApiModelProperty("返利品项名称")
    private String flItemNameStr;

    @ApiModelProperty("返利产品名称")
    private String flProductNameStr;

    @ApiModelProperty("是否已读")
    private String dmsReadFlag;

    @ApiModelProperty("年月集合")
    private Set<String> yearsSet;
    @ApiModelProperty("预算科目编码集合")
    private Set<String> subjectCodes;
    @ApiModelProperty("客户编码集合")
    private Set<String> customerCodes;
    @ApiModelProperty("兑付方式集合")
    private Set<String> cashTypeSet;

    @ApiModelProperty("费用使用部门编码")
    private List<String> belongDepartmentCodes;

    @ApiModelProperty("费用承担部门编码")
    private List<String> bearDepartmentCodes;

    @ApiModelProperty("组织编码")
    private List<String> orgCodeList;

    @ApiModelProperty("活动明细编码集合")
    private List<String> schemeDetailCodeList;

    @ApiModelProperty("渠道类型")
    private List<String> channelTypeList;

    @ApiModelProperty("客户渠道类型")
    private String channelType;

    @ApiModelProperty("客户渠道类型名称")
    private String channelTypeName;

    @ApiModelProperty("费率")
    private BigDecimal rate;

    @ApiModelProperty("关闭编码")
    private String closeCode;

    @ApiModelProperty("关闭审批状态")
    private String closeProcessStatus;

    @ApiModelProperty("实际支付金额")
    private BigDecimal prepayAmount;

    @ApiModelProperty("预付可冲销金额")
    private BigDecimal availableReversedAmount;
    @ApiModelProperty("客户搜索项（快照）")
    private String searchName;

    @ApiModelProperty("行上税率")
    private BigDecimal lineTaxRate;

    @ApiModelProperty("行上不含税金额")
    private BigDecimal lineNoTaxApplyAmount;

    @ApiModelProperty("活动细类编码集合")
    private List<String> detailCodeList;

    @ApiModelProperty("管报计提金额-活动维度")
    private BigDecimal withHoldingReportAmount;

    @ApiModelProperty("剩余可结案金额-活动维度")
    private BigDecimal restAvailableAuditAmount;

    @ApiModelProperty("已预付金额-活动维度")
    private BigDecimal alreadyPrepaidAmount;

    @ApiModelProperty("剩余可兑付金额-活动维度")
    private BigDecimal restAvailableCashAmount;

    @ApiModelProperty("管报实际总金额-活动维度")
    private BigDecimal actualReportTotalAmount;

    @ApiModelProperty("本品小类/名称-大区方案管理方案明细导出用")
    private String levelOrProductName;

    @ApiModelProperty("返利品项/产品名称-大区方案管理方案明细导出用")
    private String feeProductOrItemName;

    @ApiModelProperty("方案明细编码集合,逗号分割")
    private String schemeDetailCodeListStr;

    @ApiModelProperty("预算时间区间查询开始")
    private String budgetYearsStart;

    @ApiModelProperty("预算时间区间查询结束")
    private String budgetYearsEnd;


    public void setProductAndItemList(List<MarketingPlanProductVo> list) {
        this.productAndItemList = list;
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, List<MarketingPlanProductVo>> map = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getType()))
                    .collect(Collectors.groupingBy(MarketingPlanProductVo::getType));
            for (Map.Entry<String, List<MarketingPlanProductVo>> entry : map.entrySet()) {
                String key = entry.getKey();
                List<MarketingPlanProductVo> values = entry.getValue();
                if (OverallPlanProductEnum.cal_product.getCode().equals(key)) {
                    this.productList = values;
                    MarketingPlanProductVo itemProduct = values.get(0);
                    this.productCode = itemProduct.getCode();
                    this.productName = itemProduct.getName();
                } else if (OverallPlanProductEnum.cal_item.getCode().equals(key)) {
                    this.itemList = values;
                    MarketingPlanProductVo itemProduct = values.get(0);
                    if (!MarketingPlanCaseTypeEnum.back.getCode().equals(this.caseType)) {
                        this.itemCode = itemProduct.getCode();
                        this.itemName = itemProduct.getName();
                    }
                } else if (OverallPlanProductEnum.cal_level.getCode().equals(key)) {
                    this.levelList = values;
                } else if (OverallPlanProductEnum.fee_product.getCode().equals(key)) {
                    this.feeProductList = values;
                } else if (OverallPlanProductEnum.fee_item.getCode().equals(key)) {
                    this.feeItemList = values;
                } else if (OverallPlanProductEnum.fee_level.getCode().equals(key)) {
                    this.feeLevelList = values;
                } else if (OverallPlanProductEnum.fee_belong_item.getCode().equals(key)) {
                    this.feeBelongItemList = values;
                    MarketingPlanProductVo itemProduct = values.get(0);
                    if (MarketingPlanCaseTypeEnum.back.getCode().equals(this.caseType)) {
                        this.itemCode = itemProduct.getCode();
                        this.itemName = itemProduct.getName();
                    }
                }
            }
        }
    }

    public List<MarketingPlanProductVo> getProductAndItemList() {
        List<MarketingPlanProductVo> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(this.productList)) {
            for (MarketingPlanProductVo vo : this.productList) {
                vo.setType(OverallPlanProductEnum.cal_product.getCode());
            }
            list.addAll(this.productList);
        }
        if (CollectionUtils.isNotEmpty(this.itemList)) {
            for (MarketingPlanProductVo vo : this.itemList) {
                vo.setType(OverallPlanProductEnum.cal_item.getCode());
            }
            list.addAll(this.itemList);
        }
        if (CollectionUtils.isNotEmpty(this.levelList)) {
            for (MarketingPlanProductVo vo : this.levelList) {
                vo.setType(OverallPlanProductEnum.cal_level.getCode());
            }
            list.addAll(this.levelList);
        }
        if (CollectionUtils.isNotEmpty(this.feeProductList)) {
            for (MarketingPlanProductVo vo : this.feeProductList) {
                vo.setType(OverallPlanProductEnum.fee_product.getCode());
            }
            list.addAll(feeProductList);
        }
        if (CollectionUtils.isNotEmpty(this.feeItemList)) {
            for (MarketingPlanProductVo vo : this.feeItemList) {
                vo.setType(OverallPlanProductEnum.fee_item.getCode());
            }
            list.addAll(this.feeItemList);
        }
        if (CollectionUtils.isNotEmpty(this.feeLevelList)) {
            for (MarketingPlanProductVo vo : this.feeLevelList) {
                vo.setType(OverallPlanProductEnum.fee_level.getCode());
            }
            list.addAll(this.feeLevelList);
        }
        if (CollectionUtils.isNotEmpty(this.feeBelongItemList)) {
            for (MarketingPlanProductVo vo : this.feeBelongItemList) {
                vo.setType(OverallPlanProductEnum.fee_belong_item.getCode());
            }
            list.addAll(this.feeBelongItemList);
        }

        return list;
    }
}
