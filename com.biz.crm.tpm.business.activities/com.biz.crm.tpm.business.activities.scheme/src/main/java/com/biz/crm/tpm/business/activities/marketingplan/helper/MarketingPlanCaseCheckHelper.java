package com.biz.crm.tpm.business.activities.marketingplan.helper;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerSelectDto;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.dto.ProductQueryDto;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductSmallClassVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductItemVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.region.sdk.service.RegionVoService;
import com.biz.crm.mdm.business.region.sdk.vo.RegionVo;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalPaginationDto;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanDepartment;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanProduct;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanScope;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanDepartmentVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanProductVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanScopeVo;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesTemplateConfigService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigCategoryVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigVo;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailsDto;
import com.biz.crm.tpm.business.budget.sdk.enums.ApprovalCollectType;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.service.FormulaCalService;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterOrgVo;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/20 21:01
 */
@Component
public class MarketingPlanCaseCheckHelper {

    @Resource
    private CostTypeCategoryVoService costTypeCategoryVoService;

    @Resource
    private CostTypeDetailVoService costTypeDetailVoService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private MdmCostCenterVoService costCenterVoService;

    @Resource
    private BudgetSubjectsVoService budgetSubjectsVoService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private ProductPhaseVoService productPhaseVoService;

    @Resource
    private ProductVoService productVoService;

    @Resource
    private ProductSmallClassVoService smallClassVoService;

    @Resource
    private CostBudgetVoService costBudgetVoService;

    @Resource
    private ActivitiesTemplateConfigService activitiesTemplateConfigService;

    @Resource
    private RegionVoService regionVoService;

    @Resource
    private FormulaCalService formulaCalService;

    @Resource
    private TerminalVoService terminalVoService;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private MarketingPlanCaseService planCaseService;

    @Resource
    private DictDataVoService dictDataVoService;

    private final static String regex = "^[0-9]+(\\.[0-9]+)?(,[0-9]+(\\.[0-9]+)?)*$";

    private final static String regex2 = "\\d+";

    private final static String regex3 = "^\\d+(\\.\\d{1,2})?$";

    private final static String regex4 = "^\\d+(\\.\\d{1})?$";


    public static Boolean matchRules(String value, String msg, StringJoiner errMsg) {
        if (ObjectUtils.isEmpty(value)) {
            errMsg.add(msg + "不能为空");
            return Boolean.FALSE;
        }
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        if (!matcher.matches()) {
            errMsg.add(msg + "为非法字符,只能是数字加英文逗号,例如:1,2");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    public static Boolean isMatch(String value, String msg, StringJoiner errMsg) {
        if (ObjectUtils.isEmpty(value)) {
            errMsg.add(msg + "不能为空");
            return Boolean.FALSE;
        }
        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex2);
        String[] values = value.split(",");
        for (String s : values) {
            // 创建 Matcher 对象
            Matcher matcher = pattern.matcher(s);
            if (!matcher.matches()) {
                errMsg.add(msg + "为非法字符,只能是正整数");
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 匹配位数
     *
     * @param value
     * @param msg
     * @param errMsg
     * @param places
     * @return
     */
    public static Boolean isMatchNum(BigDecimal value, String msg, StringJoiner errMsg, Integer places) {
        if (ObjectUtils.isEmpty(value)) {
            errMsg.add(msg + "不能为空");
            return Boolean.FALSE;
        }
        // 创建 Pattern 对象
        Pattern pattern = null;
        if (places == 2) {
            pattern = Pattern.compile(regex3);
        } else if (places == 1) {
            pattern = Pattern.compile(regex4);
        }
        String data = value.toString();
        Matcher matcher = pattern.matcher(data);
        if (!matcher.matches()) {
            errMsg.add(msg + "为非法字符,只能是正整数或最多只能保留" + places + "小数");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    public static Boolean paramNotNull(Object filed, String msg, StringJoiner errMsg) {
        if (ObjectUtils.isEmpty(filed)) {
            errMsg.add(msg + "不能为空");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public static boolean containsAll(List<?> list1, List<?> list2) {
        // 如果list2的大小大于list1的大小，则list1不可能完全包含list2
        if (list2.size() > list1.size()) {
            return false;
        }
        return list1.containsAll(list2);
    }

    public static Boolean collectionNotNull(String msg, StringJoiner errMsg, List<?>... lists) {
        long emptyListsCount = Stream.of(lists)
                .filter(CollectionUtils::isEmpty)
                .count();

        if (emptyListsCount == lists.length) {
            errMsg.add(msg + "不能为空");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public static Boolean collectionNotNullAndOnlyOne(String msg, StringJoiner errMsg, List<?>... lists) {
        long emptyListsCount = Stream.of(lists)
                .filter(CollectionUtils::isEmpty)
                .count();

        if (emptyListsCount == lists.length) {
            errMsg.add(msg + "不能都为空");
            return Boolean.FALSE;
        }
        long listsNunCount = Stream.of(lists)
                .filter(CollectionUtils::isNotEmpty)
                .count();
        if (listsNunCount != 1) {
            errMsg.add(msg + "有且只能有一个有值");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public static String getOnlyKey(MarketingPlanCaseVo vo, String caseType) {
        String key = null;
        MarketingPlanCaseTypeEnum caseTypeEnum = MarketingPlanCaseTypeEnum.findByCode(caseType);
        String baseKey = vo.getReleaseDetailCode() + vo.getActName() + vo.getDetailCode() + vo.getStartDate() + vo.getEndDate()
                + vo.getCustomerCode() + vo.getBelongDepartmentCode();
        String itemCodes = null;
        String productCodes = null;
        String feeBelongItemCodes = null;
        String levels = null;
        String feeItemCodes = null;
        String feeProductCodes = null;
        String feeLevels = null;
        String costBasis = null;
        String executeExample = null;
        String closeCaseExample = null;
        String contractCode = vo.getContractCode();
        String cashType = vo.getCashType();
        String actDesc = vo.getActDesc();
        if (CollectionUtils.isNotEmpty(vo.getItemList())) {
            itemCodes = vo.getItemList().stream().map(MarketingPlanProductVo::getCode).sorted().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(vo.getProductList())) {
            productCodes = vo.getProductList().stream().map(MarketingPlanProductVo::getCode).sorted().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(vo.getFeeBelongItemList())) {
            feeBelongItemCodes = vo.getFeeBelongItemList().stream().map(MarketingPlanProductVo::getCode).sorted().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(vo.getLevelList())) {
            levels = vo.getLevelList().stream().map(MarketingPlanProductVo::getCode).sorted().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(vo.getFeeItemList())) {
            feeItemCodes = vo.getFeeItemList().stream().map(MarketingPlanProductVo::getCode).sorted().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(vo.getFeeProductList())) {
            feeProductCodes = vo.getFeeProductList().stream().map(MarketingPlanProductVo::getCode).sorted().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(vo.getFeeLevelList())) {
            feeLevels = vo.getFeeLevelList().stream().map(MarketingPlanProductVo::getCode).sorted().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(vo.getCostBasisList())) {
            costBasis = vo.getCostBasisList().stream().sorted().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(vo.getExecuteExampleList())) {
            executeExample = vo.getExecuteExampleList().stream().sorted().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(vo.getCloseCaseExampleList())) {
            closeCaseExample = vo.getCloseCaseExampleList().stream().sorted().collect(Collectors.joining(","));
        }

        switch (caseTypeEnum) {
            case display:
                key = baseKey + vo.getTerminalCode() + itemCodes + productCodes + cashType + vo.getExecuteDesc() + actDesc +
                        executeExample + closeCaseExample + costBasis;
                break;
            case staff_cost:
                key = baseKey + vo.getTerminalCode() + itemCodes + productCodes + cashType + vo.getExecuteDesc() + actDesc +
                        executeExample + closeCaseExample + costBasis;
                break;
            case material:
                key = baseKey + itemCodes + productCodes + vo.getSellMaterialType() + vo.getMaterialCode() + vo.getTransportType() +
                        vo.getBearAmount() + vo.getCusBearAmount() + actDesc + closeCaseExample;
                break;
            case matching_gift:
                key = baseKey + itemCodes + levels + vo.getConditionNum() + vo.getGiveNum() + vo.getDiscountAmount() + vo.getContractCode() + costBasis +
                        actDesc;
                break;
            case back:
                key = baseKey + vo.getRebateType() + vo.getRebateStartDate() + vo.getRebateEndDate() + feeProductCodes + feeItemCodes + feeBelongItemCodes + vo.getConditionFormula()
                        + vo.getConditionNum() + vo.getGiveNum() + cashType + vo.getContractCode() + costBasis + actDesc;
                break;
            case o_two_o:
                key = baseKey + itemCodes + productCodes + vo.getPlatform() + vo.getPlacingCity() + actDesc;
                break;
            case fixed:
                key = baseKey + vo.getTerminalCode() + itemCodes + productCodes + caseType + costBasis + actDesc;
                break;
            case direct_descent:
                break;
            case special_price:
                break;
        }
        return key;
    }


    /**
     * 校验多部门标记
     *
     * @param list
     */
    public static void checkMuchDepartmentMark(List<MarketingPlanCaseVo> list) {
        Map<String, String> map = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMuchDepartmentMark()))
                .collect(Collectors.toMap(MarketingPlanCaseVo::getMuchDepartmentMark, x -> MarketingPlanCaseCheckHelper.getOnlyKey(x, x.getCaseType()),
                        (v1, v2) -> v1));
        for (MarketingPlanCaseVo caseVo : list) {
            if (map.containsKey(caseVo.getMuchDepartmentMark())) {
                String onlyKey = map.get(caseVo.getMuchDepartmentMark());
                String key = MarketingPlanCaseCheckHelper.getOnlyKey(caseVo, caseVo.getCaseType());
                String msg = null;
                if (!onlyKey.equals(key)) {
                    msg = "多部门标记除了成本中心、费用承担部门、费用金额不同之外，其他必须相同";
                }
                if (ObjectUtils.isNotEmpty(caseVo.getErrMsg())) {
                    msg = caseVo.getErrMsg() + ";" + msg;
                }
                if (ObjectUtils.isNotEmpty(msg)) {
                    caseVo.setCheckFlag(Boolean.FALSE);
                    caseVo.setErrMsg(msg);
                }
            }
        }
    }

    /**
     * 通过组织查询成本中心
     *
     * @param list
     * @return
     */
    public Map<String, Set<String>> findOrgCostCenter(List<MarketingPlanCaseVo> list) {
        Set<String> orgCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .map(MarketingPlanCaseVo::getBelongDepartmentCode).collect(Collectors.toSet());
        Map<String, List<OrgVo>> orgVoListMap = orgVoService.findAllChildrenByOrgCodesMap(Lists.newArrayList(orgCodes));
        Map<String, Set<String>> orgCostCenterMap = Maps.newHashMap();
        for (Map.Entry<String, List<OrgVo>> entry : orgVoListMap.entrySet()) {
            Set<String> costCenterCodeSet = Sets.newHashSet();
            for (OrgVo orgVo : entry.getValue()) {
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orgVo.getCostCenterCodeSet())) {
                    costCenterCodeSet.addAll(orgVo.getCostCenterCodeSet());
                }
            }
            orgCostCenterMap.put(entry.getKey(), costCenterCodeSet);
        }
        return orgCostCenterMap;
    }


    /**
     * 通过组织查询成本中心
     *
     * @param orgCodes
     * @return
     */
    public Map<String, Set<String>> findCostCenterByOrgCodes(List<String> orgCodes) {
        Map<String, List<OrgVo>> orgVoListMap = orgVoService.findAllChildrenByOrgCodesMap(Lists.newArrayList(orgCodes));
        Map<String, Set<String>> orgCostCenterMap = Maps.newHashMap();
        for (Map.Entry<String, List<OrgVo>> entry : orgVoListMap.entrySet()) {
            Set<String> costCenterCodeSet = Sets.newHashSet();
            for (OrgVo orgVo : entry.getValue()) {
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orgVo.getCostCenterCodeSet())) {
                    costCenterCodeSet.addAll(orgVo.getCostCenterCodeSet());
                }
            }
            orgCostCenterMap.put(entry.getKey(), costCenterCodeSet);
        }
        return orgCostCenterMap;
    }


    /**
     * 计算费率
     *
     * @param salesPlanMap
     * @param costCenterCodes
     * @param caseVo
     */
    public void calPlanCaseRatio(Map<String, List<MarketingSalesPlanVo>> salesPlanMap, Set<String> costCenterCodes, MarketingPlanCaseVo caseVo) {
        //计算费率
        List<MarketingSalesPlanVo> ratioSalesPlanVoList = salesPlanMap.entrySet().stream()
                .filter(x -> costCenterCodes.contains(x.getKey()))
                .flatMap(x -> x.getValue().stream())
                .filter(k -> StringUtil.isNotEmpty(k.getCustomerCode()))
                .filter(k -> StringUtil.isNotEmpty(k.getYears()))
                .filter(x -> x.getCustomerCode().equals(caseVo.getCustomerCode())
                        && x.getYears().equals(caseVo.getYears()))
                .collect(Collectors.toList());
        BigDecimal ratioSalesVolume = ratioSalesPlanVoList.stream().map(x -> Optional.ofNullable(x.getTaxEstimatedCost()).orElse(BigDecimal.ZERO))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (ratioSalesVolume.compareTo(BigDecimal.ZERO) == 1 && ObjectUtils.isNotEmpty(caseVo.getApplyAmount())) {
            caseVo.setRatio(caseVo.getApplyAmount().divide(ratioSalesVolume, 4, BigDecimal.ROUND_HALF_UP));
        }
    }

    /**
     * 查询活动大类
     *
     * @param categoryCodes
     * @return
     */
    public Map<String, CostTypeCategoryVo> findCategoryMap(List<String> categoryCodes) {
        if (CollectionUtils.isEmpty(categoryCodes)) {
            return Maps.newHashMap();
        }
        List<CostTypeCategoryVo> categoryVoList = costTypeCategoryVoService.findListByCategoryCodes(categoryCodes);
        if (CollectionUtils.isEmpty(categoryVoList)) {
            return Maps.newHashMap();
        }
        return categoryVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCategoryCode()) && ObjectUtils.isNotEmpty(x.getCategoryName()))
                .collect(Collectors.toMap(CostTypeCategoryVo::getCategoryCode, Function.identity()));
    }


    /**
     * 查询活动细类
     *
     * @param detailCodes
     * @return
     */
    public Map<String, CostTypeDetailVo> findDetailMap(List<String> detailCodes) {
        if (CollectionUtils.isEmpty(detailCodes)) {
            return Maps.newHashMap();
        }
        List<CostTypeDetailVo> costTypeDetailVos = costTypeDetailVoService.findByCodes(detailCodes);
        if (CollectionUtils.isEmpty(costTypeDetailVos)) {
            return Maps.newHashMap();
        }
        return costTypeDetailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDetailCode()) && ObjectUtils.isNotEmpty(x.getDetailName()))
                .collect(Collectors.toMap(CostTypeDetailVo::getDetailCode, Function.identity()));
    }

    /**
     * 二级费用科目
     *
     * @return
     */
    public Map<String, String> findSecondBudgetSubject() {
        List<BudgetSubjectsVo> budgetSubjectsVos = budgetSubjectsVoService.findSecondBudgetSubject();
        if (CollectionUtils.isEmpty(budgetSubjectsVos)) {
            return Maps.newHashMap();
        }
        return budgetSubjectsVos.stream().collect(Collectors.toMap(BudgetSubjectsVo::getBudgetSubjectsCode, BudgetSubjectsVo::getBudgetSubjectsName));
    }

    /**
     * 查询组织信息
     *
     * @param orgCodes
     * @return
     */
    public Map<String, OrgVo> findOrgMap(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Maps.newHashMap();
        }
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
        if (CollectionUtils.isEmpty(orgVoList)) {
            return Maps.newHashMap();
        }
        return orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, Function.identity()));
    }


    public Map<String, Boolean> findOrgChildrenOrg(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Maps.newHashMap();
        }
        Map<String, List<OrgVo>> orgChildrenMap = orgVoService.findAllChildrenByOrgCodesMap(orgCodes);
        if (ObjectUtils.isEmpty(orgChildrenMap)) {
            return Maps.newHashMap();
        }
        Map<String, Boolean> map = Maps.newHashMap();
        orgChildrenMap.forEach((key, value) -> {
            Boolean flag = Boolean.TRUE;
            if (value.size() > 1) {
                flag = Boolean.FALSE;
            }
            map.put(key, flag);
        });
        return map;
    }

    /**
     * 查询成本中心
     *
     * @param costCenterCodes
     * @return
     */
    public Map<String, String> findCostCenterMap(List<String> costCenterCodes) {
        if (CollectionUtils.isEmpty(costCenterCodes)) {
            return Maps.newHashMap();
        }
        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(costCenterCodes);
        if (CollectionUtils.isEmpty(costCenterVoList)) {
            return Maps.newHashMap();
        }
        return costCenterVoList.stream().filter(x -> EnableStatusEnum.ENABLE.getCode().equals(x.getEnableStatus()))
                .collect(Collectors.toMap(MdmCostCenterVo::getCostCenterCode, MdmCostCenterVo::getCostCenterName));
    }

    public Map<String, MdmCostCenterVo> findCostCenterVoMap(List<String> costCenterCodes) {
        if (CollectionUtils.isEmpty(costCenterCodes)) {
            return Maps.newHashMap();
        }
        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(costCenterCodes);
        if (CollectionUtils.isEmpty(costCenterVoList)) {
            return Maps.newHashMap();
        }
        return costCenterVoList.stream().filter(x -> EnableStatusEnum.ENABLE.getCode().equals(x.getEnableStatus()))
                .collect(Collectors.toMap(MdmCostCenterVo::getCostCenterCode, Function.identity()));
    }

    /**
     * 查询小类
     *
     * @param codes
     * @return
     */
    public Map<String, String> findSmallLevel(Set<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Maps.newHashMap();
        }
        return smallClassVoService.findNameByCodes(codes);
    }

    /**
     * 查询SAP产品层级-品项
     *
     * @param codes
     * @return
     */
    public Map<String, String> findSapLevelMap(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Maps.newHashMap();
        }
        List<ProductPhaseVo> productPhaseVoList = productPhaseVoService.findByCodes(Sets.newHashSet(codes));
        if (CollectionUtils.isNotEmpty(productPhaseVoList)) {
            return productPhaseVoList.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, ProductPhaseVo::getProductPhaseName));
        }
        return Maps.newHashMap();
    }

    public Map<String, ProductPhaseVo> findProductPhaseMap(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Maps.newHashMap();
        }
        List<ProductPhaseVo> productPhaseVoList = productPhaseVoService.findByCodes(Sets.newHashSet(codes));
        if (CollectionUtils.isNotEmpty(productPhaseVoList)) {
            return productPhaseVoList.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, Function.identity()));
        }
        return Maps.newHashMap();
    }

    public Map<String, List<ProductPhaseVo>> findProductPhaseMapList(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Maps.newHashMap();
        }
        Map<String, List<ProductPhaseVo>> map = productPhaseVoService.findListByPhaseCodes(codes);
        return map;
    }


    /**
     * 通过品项查询商品信息
     *
     * @param codes
     * @return
     */
    public Map<String, List<ProductVo>> findProductByItemCodes(List<String> codes) {
        List<ProductItemVo> itemVos = productVoService.findItemListByItemCodes(codes);
        if (CollectionUtils.isEmpty(itemVos)) {
            return Maps.newHashMap();
        }
        return itemVos.stream().collect(Collectors.toMap(x -> x.getItemCode(), x -> x.getProductList()));
    }


    /**
     * 查询商品
     *
     * @param codes
     * @return
     */
    public Map<String, ProductVo> findProductListByCodes(List<String> codes, Set<String> companyCodeSet, List<String> itemCodes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Maps.newHashMap();
        }
        List<ProductVo> productVoList = productVoService.findByProductQueryDto(new ProductQueryDto() {{
            this.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            this.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            this.setTenantCode(TenantUtils.getTenantCode());
            this.setProductCodeList(codes);
            this.setCostLabel(BooleanEnum.TRUE.getCapital());
            this.setCompanyCodeSet(companyCodeSet);
            this.setProductType("ZERT");
            if (CollectionUtils.isNotEmpty(itemCodes)) {
                this.setItemCodeSet(Sets.newHashSet(itemCodes));
            }
        }});
        if (CollectionUtils.isEmpty(productVoList)) {
            return Maps.newHashMap();
        }
        return productVoList.stream().collect(Collectors.toMap(ProductVo::getProductCode, Function.identity()));
    }

    public Map<String, ProductVo> findProductListByItemCodes(Set<String> itemCodeSet) {
        if (CollectionUtils.isEmpty(itemCodeSet)) {
            return Maps.newHashMap();
        }
        List<ProductVo> productVoList = productVoService.findByProductQueryDto(new ProductQueryDto() {{
            this.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            this.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            this.setTenantCode(TenantUtils.getTenantCode());
            this.setCostLabel(BooleanEnum.TRUE.getCapital());
            this.setProductType("ZERT");
            this.setItemCodeSet(itemCodeSet);
        }});
        if (CollectionUtils.isEmpty(productVoList)) {
            return Maps.newHashMap();
        }
        return productVoList.stream().collect(Collectors.toMap(ProductVo::getProductCode, Function.identity()));
    }


    public Map<String, ProductVo> findProductListBySmallLevelCodes(Set<String> smallLevelCodes) {
        if (CollectionUtils.isEmpty(smallLevelCodes)) {
            return Maps.newHashMap();
        }
        List<ProductVo> productVoList = productVoService.findByProductQueryDto(new ProductQueryDto() {{
            this.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            this.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            this.setTenantCode(TenantUtils.getTenantCode());
            this.setSmallClassCodeSet(smallLevelCodes);
        }});
        if (CollectionUtils.isEmpty(productVoList)) {
            return Maps.newHashMap();
        }
        return productVoList.stream().collect(Collectors.toMap(ProductVo::getProductCode, Function.identity()));
    }


    /**
     * 获取行政区域
     *
     * @param placingCitys
     * @return
     */
    public Map<String, String> getAdministrativeArea(List<String> placingCitys) {
        List<RegionVo> regionVos = regionVoService.findListByRegionCodes(placingCitys);
        if (CollectionUtils.isEmpty(regionVos)) {
            return Maps.newHashMap();
        }
        return regionVos.stream().collect(Collectors.toMap(RegionVo::getRegionCode, RegionVo::getRegionName));
    }


    /**
     * 预算匹配
     *
     * @param vo
     */
    public void matchBudget(Map<String, Boolean> filedMap, Map<String, String> chineseMap, MarketingPlanCaseVo vo) {
        if (vo.getCheckFlag()) {
            CostBudgetDto dto = new CostBudgetDto();
            dto.setYearMonthLy(vo.getYears());
            dto.setCompanyCode(vo.getCompanyCode());
            dto.setDepartmentOneCode(vo.getBearDepartmentCode());
            dto.setCostCenterCode(vo.getCostCenterCode());
            dto.setBudgetSubjectCode(vo.getBudgetSubjectCode());
            dto.setCustomerCode(vo.getCustomerCode());
            List<String> itemCodes = Lists.newArrayList();
            List<String> productCodes = Lists.newArrayList();
            //后返的需要单独处理
            if (MarketingPlanCaseTypeEnum.back.getCode().equals(vo.getCaseType())) {
                if (CollectionUtils.isNotEmpty(vo.getFeeBelongItemList())) {
                    itemCodes = vo.getFeeBelongItemList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toList());
                    dto.setItemCodes(itemCodes);
                }
            } else {
                if (CollectionUtils.isNotEmpty(vo.getItemList())) {
                    itemCodes = vo.getItemList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toList());
                    dto.setItemCodes(itemCodes);
                }
            }
            if (CollectionUtils.isNotEmpty(vo.getProductList())) {
                productCodes = vo.getProductList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toList());
                dto.setProductCodes(productCodes);
            }
            String code = costBudgetVoService.findCodeByConditions(dto);
            if (ObjectUtils.isEmpty(code)) {
                vo.setErrMsg("年度预算未找到");
                vo.setCheckFlag(Boolean.FALSE);
            } else {
                vo.setBudgetCode(code);
            }
//            String msg = BusinessRequiredUtil.filedRequiredCheck(filedMap, chineseMap, vo);
//            if (ObjectUtils.isNotEmpty(msg)) {
//                vo.setErrMsg(msg);
//                vo.setCheckFlag(Boolean.FALSE);
//            }
        }
    }

    /**
     * 获取配置模板
     *
     * @param configCode
     * @return
     */
    public List<ActivitiesTemplateConfigDetailVo> getTemplateFiled(String configCode) {
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(configCode);
        Validate.notNull(config, "表单模版配置为空");
        List<ActivitiesTemplateConfigDetailVo> detailList = config.getDetails().stream().collect(Collectors.toList());
        return detailList;
    }


    /**
     * 通过活动配置编码查询细类编码
     *
     * @param configCode
     * @return
     */
    public Set<String> getTemplateRelateDetailCodes(String configCode) {
        ActivitiesTemplateConfigVo config = activitiesTemplateConfigService.findByCode(configCode);
        Validate.notNull(config, "表单模版配置为空");
        List<ActivitiesTemplateConfigCategoryVo> categoryVos = config.getCategoryList();

        List<String> categoryCodes = categoryVos.stream().map(ActivitiesTemplateConfigCategoryVo::getCategoryCode).collect(Collectors.toList());
        List<CostTypeDetailVo> detailVoList = costTypeDetailVoService.findByCategoryCodes(categoryCodes);
        if (CollectionUtils.isEmpty(detailVoList)) {
            return Sets.newHashSet();
        }
        return detailVoList.stream().map(CostTypeDetailVo::getDetailCode).collect(Collectors.toSet());
    }

    /**
     * 获取活动细类采集
     *
     * @param codes
     * @return
     */
    public Map<String, List<CostTypeDetailCollectVo>> getDetailCollectByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Maps.newHashMap();
        }
        return costTypeDetailVoService.findCollectByCodes(codes);
    }

    public Map<String, Set<String>> findCostTypeDetailByDetailCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Maps.newHashMap();
        }
        return costTypeDetailVoService.findPayBysByDetailCodes(codes);
    }


    /**
     * 查询活动细类
     *
     * @param vo
     * @return
     */
    public List<String> findDetailCodesByCondition(OverallPlanCaseVo vo) {
        CostTypeDetailsDto dto = new CostTypeDetailsDto();
        dto.setSecondCostCategory(vo.getSecondCostCategory());
        dto.setCategoryCode(vo.getCategoryCode());
        dto.setDetailCode(vo.getDetailCode());
        return costTypeDetailVoService.findListByCondition(dto);
    }


    /**
     * 查询客户编码
     *
     * @param vo
     * @return
     */
    public Set<String> findCustomerCodesByCondition(OverallPlanCaseVo vo) {
        CustomerSelectDto dto = new CustomerSelectDto();
        if (CollectionUtils.isNotEmpty(vo.getCustomerTagList())) {
            dto.setCustomerTagSet(Sets.newHashSet(vo.getCustomerTagList()));
        }
        if (CollectionUtils.isNotEmpty(vo.getCooperateTypeList())) {
            dto.setCooperateTypeSet(Sets.newHashSet(vo.getCooperateTypeList()));
        }
        if (CollectionUtils.isNotEmpty(vo.getCustomerList())) {
            Set<String> customerCodeSet = vo.getCustomerList().stream().map(OverallPlanScopeVo::getCustomerCode).collect(Collectors.toSet());
            dto.setCustomerCodes(customerCodeSet);

        }
        if (ObjectUtils.isEmpty(dto)) {
            return Sets.newHashSet();
        }
        return customerVoService.findCustomerCodesByCondition(dto);
    }

    /**
     * 查询终端编码
     *
     * @param vo
     * @return
     */
    public List<String> findTerminalCodesByCondition(OverallPlanCaseVo vo) {
        TerminalPaginationDto dto = new TerminalPaginationDto();
        if (CollectionUtils.isNotEmpty(vo.getTerminalTagList())) {
            dto.setTerminalTagSet(Sets.newHashSet(vo.getTerminalTagList()));
        }
        if (CollectionUtils.isNotEmpty(vo.getTerminalList())) {
            Set<String> terminalCodes = vo.getTerminalList().stream().map(OverallPlanScopeVo::getTerminalCode).collect(Collectors.toSet());
            dto.setTerminalCodeSet(terminalCodes);
        }
        if (ObjectUtils.isEmpty(dto)) {
            return Lists.newArrayList();
        }
        return terminalVoService.findTerminalCodesByCondition(dto);
    }


    /**
     * 查询 营销方案明细
     *
     * @param schemeDetailCodes
     * @return
     */
    public List<MarketingPlanCaseVo> findCaseListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        List<MarketingPlanCaseVo> caseVoList = this.planCaseService.findByCaseCodes(schemeDetailCodes);
        return caseVoList;
    }


    /**
     * 参数组装
     *
     * @param caseVos
     * @param departmentList
     * @param productList
     * @param scopeList
     */
    public void buildOverallPlanCase(List<OverallPlanCaseVo> caseVos, List<OverallPlanDepartment> departmentList, List<OverallPlanProduct> productList,
                                     List<OverallPlanScope> scopeList) {
        Map<String, List<OverallPlanDepartment>> departmentMap = Maps.newHashMap();
        Map<String, List<OverallPlanProduct>> productMap = Maps.newHashMap();
        Map<String, List<OverallPlanScope>> scopeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(departmentList)) {
            departmentMap = departmentList.stream().collect(Collectors.groupingBy(OverallPlanDepartment::getSchemeDetailCode));
        }
        if (CollectionUtils.isNotEmpty(productList)) {
            productMap = productList.stream().collect(Collectors.groupingBy(OverallPlanProduct::getSchemeDetailCode));
        }
        if (CollectionUtils.isNotEmpty(scopeList)) {
            scopeMap = scopeList.stream().collect(Collectors.groupingBy(OverallPlanScope::getSchemeDetailCode));
        }
        for (OverallPlanCaseVo record : caseVos) {
            if (departmentMap.containsKey(record.getSchemeDetailCode())) {
                List<OverallPlanDepartmentVo> departmentVoList = (List<OverallPlanDepartmentVo>) nebulaToolkitService.copyCollectionByWhiteList(
                        departmentMap.get(record.getSchemeDetailCode()), OverallPlanDepartment.class, OverallPlanDepartmentVo.class, HashSet.class, ArrayList.class);
                String bearDepartmentNameStr = departmentVoList.stream().map(x -> x.getDepartmentName()).collect(Collectors.joining(","));
                record.setBearDepartmentList(departmentVoList);
                record.setBearDepartmentNameStr(bearDepartmentNameStr);
            }
            if (productMap.containsKey(record.getSchemeDetailCode())) {
                List<OverallPlanProductVo> productVoList = (List<OverallPlanProductVo>) nebulaToolkitService.copyCollectionByWhiteList(
                        productMap.get(record.getSchemeDetailCode()), OverallPlanProduct.class, OverallPlanProductVo.class, HashSet.class, ArrayList.class);
                record.setProductAndItemList(productVoList);
            }
            if (scopeMap.containsKey(record.getSchemeDetailCode())) {
                List<OverallPlanScopeVo> scopeVoList = (List<OverallPlanScopeVo>) nebulaToolkitService.copyCollectionByWhiteList(
                        scopeMap.get(record.getSchemeDetailCode()), OverallPlanScope.class, OverallPlanScopeVo.class, HashSet.class, ArrayList.class);
                record.setScopeList(scopeVoList);
            }
            if (ObjectUtils.isNotEmpty(record.getCooperateTypeStr())) {
                List<String> cooperateTypeList = Arrays.asList(record.getCooperateTypeStr().split(","));
                record.setCooperateTypeList(cooperateTypeList);
            }
            if (ObjectUtils.isNotEmpty(record.getCustomerTagStr())) {
                List<String> customerTagList = Arrays.asList(record.getCustomerTagStr().split(","));
                record.setCustomerTagList(customerTagList);
            }
            if (ObjectUtils.isNotEmpty(record.getTerminalTagStr())) {
                List<String> terminalTagList = Arrays.asList(record.getTerminalTagStr().split(","));
                record.setTerminalTagList(terminalTagList);
            }
            if (ObjectUtils.isNotEmpty(record.getChannelTypeStr())) {
                List<String> channelTypeList = Arrays.asList(record.getChannelTypeStr().split(","));
                record.setChannelTypeList(channelTypeList);
                Map<String, String> channelTypeMap = channelTypeMap();
                record.setChannelTypeNameStr(channelTypeList.stream().map(e -> channelTypeMap.getOrDefault(e, "缺省(未配置)")).collect(Collectors.joining(",")));
            }
        }
    }

    public void buildMarketingPlanCase(List<MarketingPlanCaseVo> caseVos, Map<String, List<MarketingPlanProductVo>> productMap) {
        List<String> detailCodes = caseVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDetailCode()))
                .map(v -> v.getDetailCode()).collect(Collectors.toList());
        Map<String, List<CostTypeDetailCollectVo>> detailCollectMap = this.getDetailCollectByCodes(detailCodes);
        Map<String, Set<String>> payBysMap = this.findCostTypeDetailByDetailCodes(detailCodes);

        for (MarketingPlanCaseVo caseVo : caseVos) {
            caseVo.setCashAmount(caseVo.getCashAmountF());
            if (detailCollectMap.containsKey(caseVo.getDetailCode())) {
                Map<String, List<CostTypeDetailCollectVo>> collectMap = detailCollectMap.get(caseVo.getDetailCode()).stream()
                        .collect(Collectors.groupingBy(CostTypeDetailCollectVo::getType));
                caseVo.setCollectList(collectMap.getOrDefault(ApprovalCollectType.COLLECT.getCode(), Lists.newArrayList()));
                caseVo.setApprovalList(collectMap.getOrDefault(ApprovalCollectType.APPROVAL.getCode(), Lists.newArrayList()));
            }
            if (payBysMap.containsKey(caseVo.getDetailCode())) {
                caseVo.setPayBys(payBysMap.get(caseVo.getDetailCode()));
            }
            if (productMap.containsKey(caseVo.getSchemeDetailCode())) {
                caseVo.setProductAndItemList(productMap.get(caseVo.getSchemeDetailCode()));
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCostBasis())) {
                List<String> costBasisList = Arrays.asList(caseVo.getCostBasis().split(","));
                caseVo.setCostBasisList(costBasisList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getExecuteExample())) {
                List<String> executeExampleList = Arrays.asList(caseVo.getExecuteExample().split(","));
                caseVo.setExecuteExampleList(executeExampleList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCloseCaseExample())) {
                List<String> closeCaseExampleList = Arrays.asList(caseVo.getCloseCaseExample().split(","));
                caseVo.setCloseCaseExampleList(closeCaseExampleList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCooperateTypeStr())) {
                List<String> cooperateTypeList = Arrays.asList(caseVo.getCooperateTypeStr().split(","));
                caseVo.setCooperateTypeList(cooperateTypeList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCustomerTagStr())) {
                List<String> customerTagList = Arrays.asList(caseVo.getCustomerTagStr().split(","));
                caseVo.setCustomerTagList(customerTagList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getTerminalTagStr())) {
                List<String> terminalTagList = Arrays.asList(caseVo.getTerminalTagStr().split(","));
                caseVo.setTerminalTagList(terminalTagList);
            }
        }
        assembleChannelType(caseVos);
    }

    private void assembleChannelType(List<MarketingPlanCaseVo> dtoList) {
        List<String> customerCodes = dtoList.stream().map(MarketingPlanCaseVo::getCustomerCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, String> customerMap = customerVos.stream().filter(e -> StringUtils.isNotEmpty(e.getCustomerCode()) && Objects.nonNull(e.getChannelType()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, CustomerVo::getChannelType, (v1, v2) -> v1));
        Map<String, String> channelTypeMap = channelTypeMap();
        dtoList.forEach(e -> {
            String customerCode = e.getCustomerCode();
            String channelTypeStr = customerMap.get(customerCode);
            if (StringUtils.isNotEmpty(channelTypeStr)) {
                List<String> channleTypeList = Arrays.asList(channelTypeStr.split(","));
                e.setChannelTypeList(channleTypeList);
                e.setChannelType(channelTypeStr);
                e.setChannelTypeName(channleTypeList.stream().map(k -> channelTypeMap.getOrDefault(k, "缺省(未配置)")).collect(Collectors.joining(",")));
            }
        });
    }


    public void buildMarketingPlanCaseAndCalRebate(List<MarketingPlanCaseVo> caseVos, Map<String, List<MarketingPlanProductVo>> productMap) {
        for (MarketingPlanCaseVo caseVo : caseVos) {
            if (productMap.containsKey(caseVo.getSchemeDetailCode())) {
                caseVo.setProductAndItemList(productMap.get(caseVo.getSchemeDetailCode()));
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCostBasis())) {
                List<String> costBasisList = Arrays.asList(caseVo.getCostBasis().split(","));
                caseVo.setCostBasisList(costBasisList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getExecuteExample())) {
                List<String> executeExampleList = Arrays.asList(caseVo.getExecuteExample().split(","));
                caseVo.setExecuteExampleList(executeExampleList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCloseCaseExample())) {
                List<String> closeCaseExampleList = Arrays.asList(caseVo.getCloseCaseExample().split(","));
                caseVo.setCloseCaseExampleList(closeCaseExampleList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCooperateTypeStr())) {
                List<String> cooperateTypeList = Arrays.asList(caseVo.getCooperateTypeStr().split(","));
                caseVo.setCooperateTypeList(cooperateTypeList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCustomerTagStr())) {
                List<String> customerTagList = Arrays.asList(caseVo.getCustomerTagStr().split(","));
                caseVo.setCustomerTagList(customerTagList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getTerminalTagStr())) {
                List<String> terminalTagList = Arrays.asList(caseVo.getTerminalTagStr().split(","));
                caseVo.setTerminalTagList(terminalTagList);
            }
            caseVo.setAuditedAmount(caseVo.getAuditAmount());
            if (MarketingPlanCaseTypeEnum.back.getCode().equals(caseVo.getCaseType())) {
                FormulaCalBaseVo calVo = new FormulaCalBaseVo();
                calVo.setCustomerCode(caseVo.getCustomerCode());
                calVo.setActDetailCode(caseVo.getSchemeDetailCode());
                calVo.setYears(caseVo.getYears());
                calVo.setRebateStartDate(caseVo.getRebateStartDate());
                calVo.setRebateEndDate(caseVo.getRebateEndDate());
                calVo.setStartDate(caseVo.getStartDate());
                calVo.setEndDate(caseVo.getEndDate());
                calVo.setConditionNum(caseVo.getConditionNum());
                calVo.setItemCodeSet(Optional.ofNullable(caseVo.getItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                calVo.setProductCodeSet(Optional.ofNullable(caseVo.getProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                calVo.setFeeItemCodeSet(Optional.ofNullable(caseVo.getFeeItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                calVo.setFeeProductCodeSet(Optional.ofNullable(caseVo.getFeeProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getCode()).collect(Collectors.toSet()));
                Validate.isTrue(!(org.springframework.util.CollectionUtils.isEmpty(calVo.getItemCodeSet()) && org.springframework.util.CollectionUtils.isEmpty(calVo.getProductCodeSet())), "营销方案：%s-%s，品项编码和商品编码不能为空", caseVo.getSchemeCode(), caseVo.getSchemeDetailCode());
                Validate.isTrue(!(org.springframework.util.CollectionUtils.isEmpty(calVo.getFeeItemCodeSet()) && org.springframework.util.CollectionUtils.isEmpty(calVo.getFeeProductCodeSet())), "营销方案：%s-%s，返利品项和返利产品不能为空", caseVo.getSchemeCode(), caseVo.getSchemeDetailCode());
                calVo.setGiveNum(caseVo.getGiveNum());
                calVo.setOrgCode(caseVo.getBelongDepartmentCode());

                BigDecimal calAmount = formulaCalService.calResult(FormulaTypeEnum.END_CASE.getCode(), caseVo.getConditionFormula(), calVo);
                caseVo.setAvailableAuditAmount(ObjectUtils.defaultIfNull(calAmount, BigDecimal.ZERO));
                caseVo.setFlProductNameStr(String.join(",", Optional.ofNullable(caseVo.getFeeProductList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
                caseVo.setFlItemNameStr(String.join(",", Optional.ofNullable(caseVo.getFeeItemList()).orElse(Lists.newArrayList()).stream().map(m -> m.getName()).collect(Collectors.toSet())));
            }
        }
    }

    /**
     * 渠道类型标签
     *
     * @return
     */
    private Map<String, String> channelTypeMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.CHANNEL_TYPE);
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }

    public List<MdmCostCenterOrgVo> findCostCenterOrgVoMap(List<String> costCenterCodes) {
        if (CollectionUtils.isEmpty(costCenterCodes)) {
            return Lists.newArrayList();
        }
        List<MdmCostCenterOrgVo> orgVoList = costCenterVoService.findCostOrgByCodes(costCenterCodes);
        if (CollectionUtils.isEmpty(orgVoList)) {
            return Lists.newArrayList();
        }
        return orgVoList;
    }

    public Map<String, String> cashTypeMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_SCHEME_CASH_TYPE);
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }

}
