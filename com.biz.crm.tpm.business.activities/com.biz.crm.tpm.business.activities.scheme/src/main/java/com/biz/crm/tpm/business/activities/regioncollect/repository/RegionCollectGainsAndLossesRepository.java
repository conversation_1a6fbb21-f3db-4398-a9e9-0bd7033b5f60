package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLosses;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectGainsAndLossesMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class RegionCollectGainsAndLossesRepository extends ServiceImpl<RegionCollectGainsAndLossesMapper, RegionCollectGainsAndLosses> {

    public List<RegionCollectGainsAndLosses> findByCollectCode(String collectCode) {
        return this.lambdaQuery()
                .eq(RegionCollectGainsAndLosses::getCollectCode, collectCode).list();
    }

    public void deleteByCollectCodes(List<String> collectCodes) {
        this.lambdaUpdate()
                .in(RegionCollectGainsAndLosses::getCollectCode, collectCodes).remove();
    }
}
