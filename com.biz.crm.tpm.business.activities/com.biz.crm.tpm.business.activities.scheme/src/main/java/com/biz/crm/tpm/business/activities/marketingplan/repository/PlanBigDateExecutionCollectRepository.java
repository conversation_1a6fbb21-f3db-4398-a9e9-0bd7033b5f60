package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanBigDateExecutionCollect;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanBigDateExecutionCollectMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanBigDateExecutionCollectVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.repository.PlanBigDateExecutionCollectRepository
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:06
 */
@Component
@Slf4j
public class PlanBigDateExecutionCollectRepository extends ServiceImpl<PlanBigDateExecutionCollectMapper, PlanBigDateExecutionCollect> {

    /**
     * 供步骤组件查询执行历史记录使用，因业务侧修改，该方法现在仅查询并至多返回一条数据，仅用于SFA步骤组件在查看历史数据时有返回数据，正常执行（该返回数据无实际意义）
     *
     * @param parentCode
     * @param dynamicKey
     * @return
     */
    public List<PlanBigDateExecutionCollect> findByParentCodeAndDynamicKey(String parentCode, String dynamicKey) {
        return this.lambdaQuery().eq(PlanBigDateExecutionCollect::getParentCode, parentCode)
                .eq(PlanBigDateExecutionCollect::getDynamicKey, dynamicKey)
                .list();
    }

    public PlanBigDateExecutionCollect findById(String id) {
        return this.lambdaQuery()
                .eq(PlanBigDateExecutionCollect::getId, id)
                .one();
    }

    public PlanBigDateExecutionCollect findByDto(PlanBigDateExecutionCollectVo dto) {
        return this.lambdaQuery()
                .eq(PlanBigDateExecutionCollect::getSchemeDetailCode, dto.getSchemeDetailCode())
                .eq(PlanBigDateExecutionCollect::getCustomerCode, dto.getCustomerCode())
                .eq(PlanBigDateExecutionCollect::getCreatePostCode, dto.getCreatePostCode())
                .eq(PlanBigDateExecutionCollect::getExecutionTime, dto.getExecutionTime())
                .eq(PlanBigDateExecutionCollect::getDynamicKey, dto.getDynamicKey())
                .eq(PlanBigDateExecutionCollect::getDynamicFormCode, dto.getDynamicFormCode())
                .eq(PlanBigDateExecutionCollect::getParentCode, dto.getParentCode())
                .one();
    }

    /**
     * 经销商活动执行记录查询 by actExecuteCode
     *
     * @param actExecuteCode
     * @return
     */
    public List<PlanBigDateExecutionCollect> findListByActCode(String actExecuteCode) {
        if (StringUtils.isBlank(actExecuteCode)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(PlanBigDateExecutionCollect::getActExecuteCode, actExecuteCode)
                .list();
    }
}
