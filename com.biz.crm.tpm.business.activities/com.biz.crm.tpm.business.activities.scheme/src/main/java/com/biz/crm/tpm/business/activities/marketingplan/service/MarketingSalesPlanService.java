package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;

import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/12 09:39
 */
public interface MarketingSalesPlanService extends BusinessPageCacheService<MarketingSalesPlanVo, MarketingSalesPlanVo> {

    Page<MarketingSalesPlanVo> findCollectList(@PageableDefault(50) Pageable pageable, MarketingSalesPlanVo vo);

    List<MarketingSalesPlanVo> saveBatchSalesPlan(List<MarketingSalesPlanVo> list, String schemeCode, Boolean checkFlag, String changeFlag);

    List<MarketingSalesPlanVo> findListBySchemeCode(String schemeCode);

    List<MarketingSalesPlanVo> findListBySchemeCodes(List<String> schemeCodes);


    List<MarketingSalesPlanVo> checkSalesPlanList(List<MarketingSalesPlanVo> voList, String years, String schemeCode, String originalSchemeCode, List<String> loginUserCodes, String userName);

    List<MarketingSalesPlanVo> findListBySalesPlanQueryVo(SalesPlanQueryVo vo);

    List<MarketingSalesPlanVo> findListByConditionToMarketingScheme(Set<String> yearsSet, Set<String> costCenterCodes, Set<String> customerCodes, Set<String> itemCodes,
                                                                    Set<String> productCodes, List<String> excludeSchemeCodes, List<String> includeSchemeCodes, String changeFlag);


    List<MarketingSalesPlanVo> findListByConditionToCalMarketing(Set<String> yearsSet, Set<String> costCenterCodes, Set<String> customerCodes, Set<String> itemCodes,
                                                                    Set<String> productCodes, List<String> excludeSchemeCodes, List<String> includeSchemeCodes, String changeFlag);

    void deleteBySchemeCodes(List<String> schemeCodes);

    void salesPlanSaveCurrentCachePage(String cacheKey, List<MarketingSalesPlanVo> saveList, String schemeCode, String originalSchemeCode, List<String> loginUserCodes, String userName);

    List<MarketingSalesPlanVo> findListByOrgCodesAndYears(WithholdingIncomeQueryDto dto);

    List<MarketingSalesPlanVo> findByCollectCode(String collectCode);
}
