package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanEstimation;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanSchemeEstimation;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanEstimationMapper;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanSchemeEstimationMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanEstimationVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanSchemeEstimationVo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class MarketingPlanSchemeEstimationRepository extends ServiceImpl<MarketingPlanSchemeEstimationMapper, MarketingPlanSchemeEstimation> {


    public List<MarketingPlanSchemeEstimation> findListBySchemeCodes(List<String> schemeCodes) {
        return this.lambdaQuery()
                .in(MarketingPlanSchemeEstimation::getSchemeCode, schemeCodes)
                .orderByAsc(MarketingPlanSchemeEstimation::getSort)
                .list();
    }


    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.lambdaUpdate()
                .in(MarketingPlanSchemeEstimation::getSchemeCode, schemeCodes)
                .remove();
    }

    public List<MarketingPlanSchemeEstimationVo> findOriginalListBySchemeCode(List<String> schemCodeList) {
        return baseMapper.findOriginalListBySchemeCode(schemCodeList);
    }
}
