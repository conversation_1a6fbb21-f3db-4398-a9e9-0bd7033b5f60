package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingContractCalComponent;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanService;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.regioncollect.constant.RegionCollectConstant;
import com.biz.crm.tpm.business.activities.regioncollect.dto.RegionCollectLogEventDto;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollect;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectProjectEnum;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectStatusEnum;
import com.biz.crm.tpm.business.activities.regioncollect.event.RegionCollectLogEventListener;
import com.biz.crm.tpm.business.activities.regioncollect.helper.RegionCollectHelper;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.*;
import com.biz.crm.tpm.business.activities.regioncollect.vo.*;
import com.biz.crm.tpm.business.control.sdk.enums.ControlMethodEnum;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Service
@Slf4j
public class RegionCollectServiceImpl implements RegionCollectService {

    @Resource
    private RegionCollectRepository collectRepository;

    @Autowired
    private RegionCollectDepartmentEstimationService regionCollectDepartmentEstimationService;

    @Autowired
    private RegionCollectGainsAndLossesService regionCollectGainsAndLossesService;

    @Autowired
    private RegionCollectItemEstimationService regionCollectItemEstimationService;

    @Autowired
    private RegionCollectMarketingEstimationService regionCollectMarketingEstimationService;


    /**
     * 备份相关
     */
    @Autowired
    private RegionCollectDepartmentEstimationBackService regionCollectDepartmentEstimationBackService;

    @Autowired
    private RegionCollectGainsAndLossesBackService regionCollectGainsAndLossesBackService;

    @Autowired
    private RegionCollectItemEstimationBackService regionCollectItemEstimationBackService;

    @Autowired
    private RegionCollectMarketingEstimationBackService regionCollectMarketingEstimationBackService;

    @Autowired
    private RegionCollectSchemeService schemeService;

    @Resource
    private OrgVoService orgVoService;

    @Autowired
    private MarketingPlanService marketingPlanService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private NebulaNetEventClient nebulaNetEventClient;

    @Resource
    private GenerateCodeService generateCodeService;

    @Resource
    private RegionCollectHelper helper;

    @Autowired(required = false)
    private RegionCollectOaService regionCollectOaService;

    @Autowired
    private MarketingPlanCaseService planCaseService;

    @Autowired
    private RegionCollectBudgetService regionCollectBudgetService;

    @Autowired
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired
    private OverallPlanService overallPlanService;

    @Resource
    private MarketingContractCalComponent marketingContractCalComponent;

    @Resource
    private RegionCollectControlBudgetComponent regionCollectControlBudgetComponent;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;

    @Resource
    private CustomerVoService customerVoService;

    @Autowired
    private RegionCollectReportService regionCollectReportService;

    @Autowired
    private MarketingSalesPlanService marketingSalesPlanService;

    /***
     * 分页查询
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<RegionCollectVo> findList(Pageable pageable, RegionCollectVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        vo.setTenantCode(TenantUtils.getTenantCode());
        Page<RegionCollectVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return collectRepository.findList(page, vo);
    }

    @Override
    public RegionCollectVo queryByIdOrCollectCode(String id, String collectCode) {
        Validate.isTrue(ObjectUtils.isNotEmpty(id) || ObjectUtils.isNotEmpty(collectCode), "编码和ID不能都为空");
        RegionCollect collect = collectRepository.queryByIdOrCollectCode(id, collectCode);
        if (Objects.isNull(collect)) {
            return null;
        }
        RegionCollectVo collectVo = nebulaToolkitService.copyObjectByWhiteList(collect, RegionCollectVo.class, HashSet.class, ArrayList.class);
        List<RegionCollectDepartmentEstimationVo> departmentEstimationList = regionCollectDepartmentEstimationService.findByCollectCode(collectVo.getCollectCode());
        List<RegionCollectGainsAndLossesVo> gainsAndLossesList = regionCollectGainsAndLossesService.findByCollectCode(collectVo.getCollectCode());
        List<RegionCollectItemEstimationVo> itemEstimationList = regionCollectItemEstimationService.findByCollectCode(collectVo.getCollectCode());
        List<RegionCollectMarketingEstimationVo> marketingEstimationList = regionCollectMarketingEstimationService.findByCollectCode(collectVo.getCollectCode());
        List<RegionCollectSchemeVo> schemeList = schemeService.findByCollectCode(collectVo.getCollectCode());
        if (CollectionUtils.isNotEmpty(schemeList)) {
            schemeList = schemeList.stream().sorted(Comparator.comparing(RegionCollectSchemeVo::getSchemeCode, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
        }

        assembleChannelType(gainsAndLossesList);
        List<RegionCollectBudgetVo> budgetList = regionCollectBudgetService.findCollectByCollectCode(collectVo.getCollectCode());
        List<MarketingSalesPlanVo> salesPlanVos = marketingSalesPlanService.findByCollectCode(collectCode);
        collectVo.setBudgetList(budgetList);
        collectVo.setDepartmentEstimationList(departmentEstimationList);
        collectVo.setGainsAndLossesList(gainsAndLossesList);
        collectVo.setItemEstimationList(itemEstimationList);
        collectVo.setMarketingEstimationList(marketingEstimationList);
        collectVo.setSchemeList(schemeList);
        collectVo.setSalesPlanList(salesPlanVos);
        return collectVo;
    }

    private void assembleChannelType(List<RegionCollectGainsAndLossesVo> gainsAndLossesList) {
        List<String> customerCodes = gainsAndLossesList.stream().map(RegionCollectGainsAndLossesVo::getCustomerCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, String> customerMap = customerVos.stream().filter(e -> StringUtils.isNotEmpty(e.getCustomerCode()) && Objects.nonNull(e.getChannelType()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, CustomerVo::getChannelType, (v1,v2) -> v1));
        gainsAndLossesList.forEach(e -> {
            String customerCode = e.getCustomerCode();
            String channelTypeStr = customerMap.get(customerCode);
            if (StringUtils.isNotEmpty(channelTypeStr)) {
                List<String> channleTypeList = Arrays.asList(channelTypeStr.split(","));
                e.setChannelTypeList(channleTypeList);
                e.setChannelType(channelTypeStr);
            }
        });
    }

    /**
     * 创建一个临时的数据
     *
     * @param orgCode
     * @param years
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRegionCollect(String orgCode, String years) {
        RegionCollect collect = collectRepository.findByOrgCodeAndYears(orgCode, years);
        if (ObjectUtils.isNotEmpty(collect)) {
            return collect.getId();
        }
        collect = new RegionCollect();
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(Lists.newArrayList(orgCode));
        Validate.isTrue(CollectionUtils.isNotEmpty(orgVoList), "查询组织信息为空");
        OrgVo orgVo = orgVoList.get(0);
        collect.setOrgCode(orgCode);
        collect.setOrgName(orgVo.getOrgName());
        collect.setYears(years);
        String collectCode = generateCodeService.generateCode(RegionCollectConstant.REGION_COLLECT_RULE_CODE);
        collect.setCollectCode(collectCode);
        collect.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        collect.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        collect.setTenantCode(TenantUtils.getTenantCode());
        collect.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
        collectRepository.save(collect);
        return collect.getId();
    }

    /**
     * 重新测算
     *
     * @param id
     */
    @Override
    public void modifyCollect(String id) {
        List<RegionCollect> collectList = collectRepository.findListByIds(Lists.newArrayList(id));
        Validate.isTrue(CollectionUtils.isNotEmpty(collectList), "未查询到已创建的大区汇总数据");
        RegionCollect collect = collectList.get(0);
        //如果是审批中/审批通过则不允许在创建新的
        Validate.isTrue(!StringUtils.equalsAny(collect.getProcessStatus(), ProcessStatusEnum.COMMIT.getDictCode(),
                ProcessStatusEnum.PASS.getDictCode()), "大区汇总方案已提交审批/审批通过，无法更新汇总");
        String regionOrgCode = collect.getOrgCode();
        String years = collect.getYears();
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(regionOrgCode);
        List<String> orgCodes = Lists.newArrayList(regionOrgCode);
        if (CollectionUtils.isNotEmpty(orgVoList)) {
            orgCodes.addAll(orgVoList.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList()));
        }
        //重新计算合同费用
        marketingContractCalComponent.calContractApplyAmountByOrgCodes(orgCodes, years);

        List<MarketingPlanVo> marketingPlanVoList = marketingPlanService.findMarketingPlanByOrgCodes(orgCodes, years,
                Lists.newArrayList(MarketingPlanSchemeTypeEnum.plan.getCode(), MarketingPlanSchemeTypeEnum.contract.getCode()), BooleanEnum.TRUE.getCapital());
        Validate.isTrue(CollectionUtils.isNotEmpty(marketingPlanVoList), String.format("当前组织下%s查询营销方案规划为空", collect.getOrgName()));

        //校验承接金额是否完全承接或者部分承接
        String errMsg = this.checkUnderTakeAmount(marketingPlanVoList);
        Validate.isTrue(ObjectUtils.isEmpty(errMsg), errMsg);

        //更新计算汇总
        this.calRegionCollect(marketingPlanVoList, collect, Boolean.TRUE);

    }


    @Override
    public void modifyOTwoOCollect(String id) {
        List<RegionCollect> collectList = collectRepository.findListByIds(Lists.newArrayList(id));
        Validate.isTrue(CollectionUtils.isNotEmpty(collectList), "未查询到已创建的大区汇总数据");
        RegionCollect collect = collectList.get(0);
        //如果是审批中/审批通过则不允许在创建新的
        Validate.isTrue(!StringUtils.equalsAny(collect.getProcessStatus(), ProcessStatusEnum.COMMIT.getDictCode(),
                ProcessStatusEnum.PASS.getDictCode()), "大区汇总方案已提交审批/审批通过，无法更新汇总");
        String regionOrgCode = collect.getOrgCode();
        String years = collect.getYears();
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(regionOrgCode);
        List<String> orgCodes = Lists.newArrayList(regionOrgCode);
        if (CollectionUtils.isNotEmpty(orgVoList)) {
            orgCodes.addAll(orgVoList.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList()));
        }

        List<MarketingPlanVo> marketingPlanVoList = marketingPlanService.findMarketingPlanByOrgCodes(orgCodes, years,
                Lists.newArrayList(MarketingPlanSchemeTypeEnum.o_two_o.getCode()), BooleanEnum.TRUE.getCapital());
        Validate.isTrue(CollectionUtils.isNotEmpty(marketingPlanVoList), String.format("当前组织下%s查询营销方案规划为空", collect.getOrgName()));

        //更新计算汇总
        this.calRegionCollect(marketingPlanVoList, collect, Boolean.FALSE);
    }


    /**
     * 大区汇总更新数据-营销方案变更进行更新汇总数据
     *
     * @param orgCode
     * @param years
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void marketingChangeUpdateRegionCollect(String orgCode, String years) {
        RegionCollect collect = collectRepository.findByYearsAndOrgCode(years, orgCode);
        if (ObjectUtils.isEmpty(collect)) return;
        List<RegionCollectSchemeVo> schemeList = schemeService.findByCollectCode(collect.getCollectCode());
        List<String> schemeCodes = schemeList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getSchemeCode()))
                .map(RegionCollectSchemeVo::getSchemeCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(schemeCodes)) return;

        //2.客户损益预测
        List<RegionCollectGainsAndLossesVo> gainsAndLossesVoList = helper.buildCustomerGainsAndLosses(schemeCodes, collect.getYears());
        //3.三级部门测算
        List<RegionCollectDepartmentEstimationVo> departmentEstimationVoList = helper.buildDepartmentEstimation(schemeCodes, collect.getYears());
        //4.品项费用测算
        List<RegionCollectItemEstimationVo> itemEstimationVoList = helper.buildItemEstimation(schemeCodes, collect.getYears(), collect.getOrgCode());
        //5.营销费用测算
        List<RegionCollectMarketingEstimationVo> marketingEstimationVos = helper.buildMarketingEstimation(schemeCodes, collect.getYears(), collect.getOrgCode(), null, null, null, null);

        regionCollectDepartmentEstimationService.saveList(departmentEstimationVoList, collect.getCollectCode());
        regionCollectGainsAndLossesService.saveList(gainsAndLossesVoList, collect.getCollectCode());
        regionCollectItemEstimationService.saveList(itemEstimationVoList, collect.getCollectCode());
        regionCollectMarketingEstimationService.saveList(marketingEstimationVos, collect.getCollectCode());
        //回写预估费用、预估销售额、费率
        if (CollectionUtils.isNotEmpty(marketingEstimationVos)) {
            Map<String, BigDecimal> map = marketingEstimationVos.stream().collect(Collectors.toMap(x -> x.getProjectCode(), x -> Optional.ofNullable(x.getEstimateAmount()).orElse(BigDecimal.ZERO)));
            BigDecimal applyAmount = map.getOrDefault(RegionCollectProjectEnum.marketing_cost_total.getCode(), BigDecimal.ZERO);
            BigDecimal estimatedSalesVolume = map.getOrDefault(RegionCollectProjectEnum.estimation_revenue.getCode(), BigDecimal.ZERO);
            collect.setApplyAmount(applyAmount);
            collect.setEstimatedSalesVolume(estimatedSalesVolume);
            collect.setRatio(BigDecimal.ZERO);
            collect.setRatioStr(BigDecimal.ZERO + "%");
            if (estimatedSalesVolume.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = applyAmount.divide(estimatedSalesVolume, 4, BigDecimal.ROUND_HALF_DOWN);
                collect.setRatioStr(ratio.multiply(BigDecimal.valueOf(100)) + "%");
                collect.setRatio(ratio);
            }
            collectRepository.updateById(collect);
        }

        String originalCollectCode = collect.getCollectCode();
        String collectCode = generateCodeService.generateCodeNotDate(originalCollectCode + "-", 1, 3).get(0);
        //数据备份-原数据进行备份处理
        regionCollectDepartmentEstimationBackService.saveList(departmentEstimationVoList, collectCode);
        regionCollectGainsAndLossesBackService.saveList(gainsAndLossesVoList, collectCode);
        regionCollectItemEstimationBackService.saveList(itemEstimationVoList, collectCode);
        regionCollectMarketingEstimationBackService.saveList(marketingEstimationVos, collectCode);


    }

    @Resource
    private UserVoService userVoService;

    public void calRegionCollect(List<MarketingPlanVo> marketingPlanVoList, RegionCollect collect, Boolean underTakeFlag) {
        List<String> schemeCodes = marketingPlanVoList.stream()
                .filter(x -> ObjectUtils.isNotEmpty(x.getSchemeCode()))
                .map(MarketingPlanVo::getSchemeCode).collect(Collectors.toList());
        List<MarketingPlanCase> caseList = planCaseService.findListBySchemeCodesAndDelFlag(schemeCodes, DelFlagStatusEnum.NORMAL.getCode());
        //判断是需要
        if (underTakeFlag) {
            List<String> regionSchemeDetailCodes = overallPlanService.findAllUndertakeOverall(collect.getYears(), collect.getOrgCode());
            if (CollectionUtils.isNotEmpty(regionSchemeDetailCodes)) {
                StringJoiner msg = new StringJoiner(";");
                Set<String> underTakeSchemeDetailCodeSet = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getReleaseDetailCode()))
                        .map(MarketingPlanCase::getReleaseDetailCode).collect(Collectors.toSet());
                for (String code : regionSchemeDetailCodes) {
                    if (!underTakeSchemeDetailCodeSet.contains(code)) {
                        msg.add(String.format("方案明细%s未承接", code));
                    }
                }
                Validate.isTrue(ObjectUtils.isEmpty(msg.toString()), msg.toString());
            }
        }
        //1.大区汇总收集方案
        List<RegionCollectSchemeVo> allCollectSchemeList = Lists.newArrayList();
        List<RegionCollectSchemeVo> collectSchemeList = RegionCollectHelper.buildCollectScheme(marketingPlanVoList, collect.getCollectCode());
        if (CollectionUtils.isNotEmpty(collectSchemeList)) {
            allCollectSchemeList.addAll(collectSchemeList);
        }
        if (underTakeFlag) {
            Map<String, String> collectSchemeMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(collectSchemeList)) {
                collectSchemeMap = collectSchemeList.stream().collect(Collectors.toMap(x -> x.getUserName() + x.getOrgCode(), x -> x.getUserName(), (v1, v2) -> v1));
            }
            List<UserVo> userVos = userVoService.findOrgAllChildrenUser(collect.getOrgCode());
            for (UserVo userVo : userVos) {
                String key = userVo.getUserName() + userVo.getOrgCode();
                if (!collectSchemeMap.containsKey(key)) {
                    RegionCollectSchemeVo schemeVo = new RegionCollectSchemeVo();
                    schemeVo.setFullName(userVo.getFullName());
                    schemeVo.setUserName(userVo.getUserName());
                    schemeVo.setOrgCode(userVo.getOrgCode());
                    schemeVo.setOrgName(userVo.getOrgName());
                    schemeVo.setCollectCode(collect.getCollectCode());
                    allCollectSchemeList.add(schemeVo);
                }
            }
        }
        //2.客户损益预测
        List<RegionCollectGainsAndLossesVo> gainsAndLossesVoList = helper.buildCustomerGainsAndLosses(schemeCodes, collect.getYears());
        //3.三级部门测算
        List<RegionCollectDepartmentEstimationVo> departmentEstimationVoList = helper.buildDepartmentEstimation(schemeCodes, collect.getYears());
        //4.品项费用测算
        List<RegionCollectItemEstimationVo> itemEstimationVoList = helper.buildItemEstimation(schemeCodes, collect.getYears(), collect.getOrgCode());
        //5.营销费用测算
        List<RegionCollectMarketingEstimationVo> marketingEstimationVos = helper.buildMarketingEstimation(schemeCodes, collect.getYears(), collect.getOrgCode(), null, null, null, null);

        regionCollectDepartmentEstimationService.saveList(departmentEstimationVoList, collect.getCollectCode());
        regionCollectGainsAndLossesService.saveList(gainsAndLossesVoList, collect.getCollectCode());
        regionCollectItemEstimationService.saveList(itemEstimationVoList, collect.getCollectCode());
        regionCollectMarketingEstimationService.saveList(marketingEstimationVos, collect.getCollectCode());
        schemeService.saveList(allCollectSchemeList, collect.getCollectCode());
        //回写预估费用、预估销售额、费率
        if (CollectionUtils.isNotEmpty(marketingEstimationVos)) {
            Map<String, BigDecimal> map = marketingEstimationVos.stream().collect(Collectors.toMap(x -> x.getProjectCode(), x -> Optional.ofNullable(x.getEstimateAmount()).orElse(BigDecimal.ZERO)));
            BigDecimal applyAmount = map.getOrDefault(RegionCollectProjectEnum.marketing_cost_total.getCode(), BigDecimal.ZERO);
            BigDecimal estimatedSalesVolume = map.getOrDefault(RegionCollectProjectEnum.estimation_revenue.getCode(), BigDecimal.ZERO);
            collect.setApplyAmount(applyAmount);
            collect.setEstimatedSalesVolume(estimatedSalesVolume);
            collect.setRatio(BigDecimal.ZERO);
            collect.setRatioStr(BigDecimal.ZERO + "%");
            if (estimatedSalesVolume.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = applyAmount.divide(estimatedSalesVolume, 4, BigDecimal.ROUND_HALF_DOWN);
                collect.setRatioStr(ratio.multiply(BigDecimal.valueOf(100)) + "%");
                collect.setRatio(ratio);
            }
            collectRepository.updateById(collect);
        }

        //更新营销方案规划状态
        if (CollectionUtils.isNotEmpty(schemeCodes)) {
            marketingPlanService.updateMarketingPlanPassStatus(schemeCodes, ProcessStatusEnum.PREPARE.getDictCode(), null);
        }

        //重新加载管控预算
        regionCollectControlBudgetComponent.reloadRegionCollectControlBudget(caseList, collect.getCollectCode());
    }


    /**
     * 批量删除
     *
     * @param idList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteBatch(List<String> idList) {
        List<RegionCollect> collectList = collectRepository.findListByIds(idList);
        String collectCodeList = collectList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode()))
                .map(RegionCollect::getCollectCode).collect(Collectors.joining("、"));
        collectList = collectList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                        ProcessStatusEnum.RECOVER.getDictCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collectList)) {
            collectList.forEach(x -> x.setDelFlag(DelFlagStatusEnum.DELETE.getCode()));
            collectRepository.updateBatchById(collectList);
            List<String> collectCodes = collectList.stream().map(RegionCollect::getCollectCode).collect(Collectors.toList());
            regionCollectDepartmentEstimationService.deleteByCollectCodes(collectCodes);
            regionCollectGainsAndLossesService.deleteByCollectCodes(collectCodes);
            regionCollectItemEstimationService.deleteByCollectCodes(collectCodes);
            regionCollectMarketingEstimationService.deleteByCollectCodes(collectCodes);
            schemeService.deleteByCollectCodes(collectCodes);

            //删除OA接口
            collectList = collectList.stream().filter(x ->
                    StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.REJECT.getDictCode(),
                            ProcessStatusEnum.RECOVER.getDictCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collectList)) {
                collectList.forEach(e -> ryOaProcessService.deleteWorkflow(e.getProcessNumber(), e.getOaId()));
            }

            //日志处理
            RegionCollectLogEventDto dto = new RegionCollectLogEventDto();
            List<RegionCollectVo> planVoList = (List<RegionCollectVo>) nebulaToolkitService.copyCollectionByWhiteList(collectList, RegionCollect.class,
                    RegionCollectVo.class, HashSet.class, ArrayList.class);
            dto.setNewestList(planVoList);
            SerializableBiConsumer<RegionCollectLogEventListener, RegionCollectLogEventDto> consumer = RegionCollectLogEventListener::onDelete;
            nebulaNetEventClient.publish(dto, RegionCollectLogEventListener.class, consumer);
        }
        return collectCodeList;
    }


    /**
     * 校验预算管控
     *
     * @param controlFlag
     * @param collectCode
     * @return
     */
    @SneakyThrows
    @Override
    public Map<String, String> checkControlBudget(String controlFlag, String collectCode) {
        if (StringUtils.isEmpty(collectCode)) {
            return Maps.newHashMap();
        }
        RegionCollect collect = collectRepository.queryByIdOrCollectCode(null, collectCode);
        List<RegionCollectSchemeVo> schemeVoList = schemeService.findByCollectCode(collectCode);
        List<String> schemeCodeList = schemeVoList.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getSchemeCode()))
                .map(RegionCollectSchemeVo::getSchemeCode).distinct().collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isNotEmpty(schemeCodeList), "大区汇总方案明细不存在,请检查数据！");
        List<MarketingPlanCase> caseList = planCaseService.findListBySchemeCodes(schemeCodeList);
        //重新加载管控预算
        List<RegionCollectBudgetVo> budgetVoList = regionCollectControlBudgetComponent.reloadRegionCollectControlBudget(caseList, collect.getCollectCode());

        Map<String, String> controlMap = budgetVoList.stream().collect(Collectors.toMap(x -> x.getTrackCode(), x -> x.getControlCode(), (a, b) -> a));

        //强制管控
        List<RegionCollectBudgetVo> forceBudgetList = budgetVoList.stream().filter(x -> ControlMethodEnum.force.getCode().equals(x.getControlForm()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(forceBudgetList)) {
            Map<String, BigDecimal> amountMap = forceBudgetList.stream().collect(Collectors.toMap(RegionCollectBudgetVo::getTrackCode,
                    RegionCollectBudgetVo::getSurplusAmount, (v1, v2) -> v1));
            Map<String, BigDecimal> applyAmount = forceBudgetList.stream().collect(Collectors.groupingBy(RegionCollectBudgetVo::getTrackCode,
                    Collectors.mapping(RegionCollectBudgetVo::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            for (Map.Entry<String, BigDecimal> entry : amountMap.entrySet()) {
                BigDecimal differenceAmount = entry.getValue().subtract(applyAmount.get(entry.getKey()));
                Validate.isTrue(differenceAmount.compareTo(BigDecimal.ZERO) > -1, "预算管控规则%s属于强制管控,超金额使用%s", controlMap.get(entry.getKey()), differenceAmount.negate());
            }
        }
        //预警
        List<RegionCollectBudgetVo> waringBudgetList = budgetVoList.stream().filter(x -> ControlMethodEnum.waring.getCode().equals(x.getControlForm()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(waringBudgetList) && BooleanEnum.FALSE.getCapital().equals(controlFlag)) {
            Map<String, BigDecimal> amountMap = waringBudgetList.stream().collect(Collectors.toMap(RegionCollectBudgetVo::getTrackCode,
                    RegionCollectBudgetVo::getSurplusAmount, (v1, v2) -> v1));
            Map<String, BigDecimal> applyAmount = waringBudgetList.stream().collect(Collectors.groupingBy(RegionCollectBudgetVo::getTrackCode,
                    Collectors.mapping(RegionCollectBudgetVo::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            StringJoiner errMsg = new StringJoiner(";");
            for (Map.Entry<String, BigDecimal> entry : amountMap.entrySet()) {
                BigDecimal differenceAmount = entry.getValue().subtract(applyAmount.get(entry.getKey()));
                if (differenceAmount.compareTo(BigDecimal.ZERO) == -1) {
                    errMsg.add(String.format("预算管控规则%s超金额使用%s", controlMap.get(entry.getKey()), differenceAmount.negate()));
                }
            }
            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                return new HashMap<String, String>() {{
                    this.put("msg", errMsg.toString());
                }};
            }
        }


        collect.setCheckBudgetFlag(BooleanEnum.TRUE.getCapital());
        collectRepository.updateById(collect);
        return Maps.newHashMap();
    }

    /**
     * 提交
     *
     * @param collectCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> submitByCollectCode(String collectCode, String submitFlag, String remark) {
        //是否直接提交 如果没有值强制复制
        submitFlag = ObjectUtils.defaultIfNull(submitFlag, BooleanEnum.FALSE.getCapital());
        RegionCollect collect = collectRepository.queryByIdOrCollectCode(null, collectCode);
        Validate.notNull(collect, "方案不存在");
        Validate.isTrue(RegionCollectStatusEnum.success.getCode().equals(collect.getCollectStatus()), "方案未计算完成");
        Validate.isTrue(StringUtils.equalsAny(collect.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(),
                ProcessStatusEnum.REJECT.getDictCode(), ProcessStatusEnum.RECOVER.getDictCode()), "方案不是待提交状态");
        Validate.isTrue(BooleanEnum.TRUE.getCapital().equals(collect.getCheckBudgetFlag()), "大区汇总未进行前置校验:预算管控校验");
        collect.setRemark(remark);
        String regionOrgCode = collect.getOrgCode();
        String years = collect.getYears();
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(regionOrgCode);
        Validate.isTrue(CollectionUtils.isNotEmpty(orgVoList), String.format("查询当前组织%s下的组织信息为空", regionOrgCode));
        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
        List<RegionCollectSchemeVo> collectSchemeVoList = schemeService.findByCollectCode(collectCode);
        Validate.isTrue(CollectionUtils.isNotEmpty(collectSchemeVoList), "大区汇总方案明细不存在,请检查数据!");
        Set<String> schemeCodeSet = collectSchemeVoList.stream().map(RegionCollectSchemeVo::getSchemeCode).collect(Collectors.toSet());
        //查询这个大区下面所有的营销方案
        List<MarketingPlanVo> marketingPlanVoList = marketingPlanService.findMarketingPlanByOrgCodes(orgCodes, years,
                Lists.newArrayList(MarketingPlanSchemeTypeEnum.plan.getCode(), MarketingPlanSchemeTypeEnum.contract.getCode()), null);
        Validate.isTrue(CollectionUtils.isNotEmpty(marketingPlanVoList), String.format("当前大区[%s]下未查询到关联的营销规划方案", regionOrgCode));
        StringJoiner confirmSchemeStr = new StringJoiner(";");
        StringJoiner noConfirmSchemeCodes = new StringJoiner("、");
        for (MarketingPlanVo vo : marketingPlanVoList) {
            if (!schemeCodeSet.contains(vo.getSchemeCode())) {
                noConfirmSchemeCodes.add(vo.getSchemeCode());
            }
            if (!schemeCodeSet.contains(vo.getSchemeCode())
                    && BooleanEnum.TRUE.getCapital().equals(vo.getConfirmStatus())) {
                confirmSchemeStr.add(vo.getSchemeCode());
            }
        }
        Validate.isTrue(ObjectUtils.isEmpty(confirmSchemeStr.toString()), String.format("方案编码:%s已确认但未汇总,请重新汇总提交", confirmSchemeStr));


        //如果不是强制提交 则进行返回参数 让前端进行提示
        if (BooleanEnum.FALSE.getCapital().equals(submitFlag)) {
            if (ObjectUtils.isNotEmpty(noConfirmSchemeCodes.toString())) {
                return new HashMap<String, String>(4) {{
                    this.put("msg", String.format("方案编码%s存在未确认的,是否继续提交审批", noConfirmSchemeCodes));
                }};
            }
        }

        collectRepository.updateById(collect);

        String processNumber = collect.getProcessNumber();
        //OA接口
        RegionCollectVo collectVo = nebulaToolkitService.copyObjectByWhiteList(collect, RegionCollectVo.class, LinkedHashSet.class, ArrayList.class);
        RegionCollectVo regionCollectVoTemp = queryByIdOrCollectCode(null, collectVo.getCollectCode());
        collectVo.setMarketingEstimationList(regionCollectVoTemp.getMarketingEstimationList());
        collectVo.setSchemeList(regionCollectVoTemp.getSchemeList());
        if (ProcessStatusEnum.PREPARE.getDictCode().equals(collect.getProcessStatus())) {
            processNumber = regionCollectOaService.pushOa(collectVo);
        } else {
            regionCollectOaService.resubmitOa(collectVo);
        }
        List<RegionCollectSchemeVo> schemeVoList = schemeService.findByCollectCode(collectCode);
        if (CollectionUtils.isNotEmpty(schemeVoList)) {
            List<String> schemeCodes = schemeVoList.stream()
                    .filter(x -> ObjectUtils.isNotEmpty(x.getSchemeCode()))
                    .map(RegionCollectSchemeVo::getSchemeCode).collect(Collectors.toList());
            marketingPlanService.updateMarketingPlanPassStatus(schemeCodes, ProcessStatusEnum.COMMIT.getDictCode(), processNumber);
        }
        return Maps.newHashMap();
    }


    /**
     * 校验承接金额
     *
     * @param marketingPlanVoList
     */
    private String checkUnderTakeAmount(List<MarketingPlanVo> marketingPlanVoList) {
        List<String> schemeCodes = marketingPlanVoList.stream().map(MarketingPlanVo::getSchemeCode).collect(Collectors.toList());
        List<MarketingPlanCase> caseList = marketingPlanCaseService.findListBySchemeCodes(schemeCodes);
        caseList = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getReleaseDetailCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(caseList)) return null;
        Map<String, BigDecimal> caseMap = caseList.stream().collect(Collectors.groupingBy(MarketingPlanCase::getReleaseDetailCode,
                Collectors.mapping(MarketingPlanCase::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Set<String> releaseDetailCodes = caseMap.keySet();
        List<OverallPlanCaseVo> overallPlanCaseVoList = overallPlanService.findOverallPlanCaseListBySchemeDetailCodes(Lists.newArrayList(releaseDetailCodes));
        StringJoiner errMsg = new StringJoiner(";");
        for (OverallPlanCaseVo caseVo : overallPlanCaseVoList) {
            BigDecimal applyAmount = caseMap.get(caseVo.getSchemeDetailCode());
            //承接明细金额-营销方案承接金额
            BigDecimal differenceAmount = caseVo.getApplyAmount().subtract(applyAmount);
            //未承接完成 剩余金额
            if (differenceAmount.compareTo(BigDecimal.ZERO) == 1) {
                if (caseVo.getSchemeDetailCode().contains(OverallPlanConstant.HEAD_SCHEME_DETAIL_CODE_RULE)) {
                    errMsg.add(String.format("总部指引明细%s未承接完成,剩余可承接金额%s", caseVo.getSchemeDetailCode(), differenceAmount));
                } else {
                    errMsg.add(String.format("大区指引明细%s未承接完成,剩余可承接金额%s", caseVo.getSchemeDetailCode(), differenceAmount));
                }
            }
            //承接超额
            if (differenceAmount.compareTo(BigDecimal.ZERO) == -1) {
                if (caseVo.getSchemeDetailCode().contains(OverallPlanConstant.HEAD_SCHEME_DETAIL_CODE_RULE)) {
                    errMsg.add(String.format("总部指引明细%s承接超额,超额%s", caseVo.getSchemeDetailCode(), differenceAmount));
                } else {
                    errMsg.add(String.format("大区指引明细%s承接超额,超额%s", caseVo.getSchemeDetailCode(), differenceAmount));
                }
            }
        }
        return errMsg.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExecuteMsg(String id, String msg, String status) {
        collectRepository.updateExecuteMsg(id, msg, status);
    }

    /**
     * OA审批回调
     *
     * @param vo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void callback(RegionCollectVo vo) {
        //TODO 此处还不知道具体的操作 直接实例化一个对象
        RegionCollect collect = collectRepository.queryByIdOrCollectCode(null, vo.getCollectCode());
        if (collect != null) {
            if (StringUtils.equalsAny(collect.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode())) return;
            collect.setProcessStatus(vo.getProcessStatus());
            collectRepository.updateById(collect);
            List<RegionCollectSchemeVo> schemeVoList = schemeService.findByCollectCode(collect.getCollectCode());
            if (CollectionUtils.isNotEmpty(schemeVoList)) {
                List<String> schemeCodes = schemeVoList.stream()
                        .filter(x -> ObjectUtils.isNotEmpty(x.getSchemeCode()))
                        .map(RegionCollectSchemeVo::getSchemeCode).collect(Collectors.toList());
                marketingPlanService.updateMarketingPlanPassStatus(schemeCodes, vo.getProcessStatus(), null);
            }
        } else {
            MarketingPlanVo planVo = new MarketingPlanVo();
            planVo.setBusinessCode(vo.getCollectCode());
            planVo.setProcessStatus(vo.getProcessStatus());
            marketingPlanService.callback(planVo);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manualApproval(String collectCode) {
        RegionCollect collect = collectRepository.queryByIdOrCollectCode(null, collectCode);
        Validate.isTrue(ObjectUtils.isNotEmpty(collect), "大区汇总不存在");
        collect.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());
        collectRepository.updateById(collect);
        List<RegionCollectSchemeVo> schemeVoList = schemeService.findByCollectCode(collect.getCollectCode());
        if (CollectionUtils.isNotEmpty(schemeVoList)) {
            List<String> schemeCodes = schemeVoList.stream()
                    .filter(x -> ObjectUtils.isNotEmpty(x.getSchemeCode()))
                    .map(RegionCollectSchemeVo::getSchemeCode).collect(Collectors.toList());
            marketingPlanService.updateMarketingPlanPassStatus(schemeCodes, ProcessStatusEnum.PASS.getDictCode(), null);
        }
    }

    /**
     * 流程撤回
     *
     * @param code
     * @param remark
     */
    @Override
    public void recover(String code, String remark) {
        if (regionCollectOaService.oaWithdraw(code, remark)) {
            RegionCollect entity = collectRepository.queryByIdOrCollectCode(null, code);
            entity.setProcessStatus(ProcessStatusEnum.RECOVER.getDictCode());
            collectRepository.saveOrUpdate(entity);
            List<RegionCollectSchemeVo> schemeVoList = schemeService.findByCollectCode(entity.getCollectCode());
            if (CollectionUtils.isNotEmpty(schemeVoList)) {
                List<String> schemeCodes = schemeVoList.stream()
                        .filter(x -> ObjectUtils.isNotEmpty(x.getSchemeCode()))
                        .map(RegionCollectSchemeVo::getSchemeCode).collect(Collectors.toList());
                marketingPlanService.updateMarketingPlanPassStatus(schemeCodes, ProcessStatusEnum.RECOVER.getDictCode(), null);
            }
        } else {
            Validate.isTrue(false, "流程撤回失败");
        }
    }

    @Override
    public List<RegionCollectVo> findCommit(String yearMonthLy) {
        return collectRepository.findCommit(yearMonthLy);
    }


    /**
     * 通过方案编码查询大区汇总数据
     *
     * @param schemeCode
     * @return
     */
    @Override
    public RegionCollectVo findBySchemeCode(String schemeCode) {
        String collectCode = schemeService.findRegionCollectCodeBySchemeCode(schemeCode);
        if (ObjectUtils.isNotEmpty(collectCode)) {
            return queryByIdOrCollectCode(null, collectCode);
        }
        return null;
    }

    @Resource
    private MarketingPlanProductRepository productRepository;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Override
    public List<MarketingPlanCaseVo> findRegionMarketingCaseList(String collectCode) {
        List<MarketingPlanCaseVo> list = collectRepository.findRegionMarketingCaseList(collectCode);
        List<String> schemeDetailCodes = list.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
        //构建参数
        checkHelper.buildMarketingPlanCase(list, productMap);
        return list;
    }
}
