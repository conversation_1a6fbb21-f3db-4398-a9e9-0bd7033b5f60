package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectItemEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectItemEstimationRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectItemEstimationService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectItemEstimationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Slf4j
@Service("regionCollectItemEstimationService")
public class RegionCollectItemEstimationServiceImpl implements RegionCollectItemEstimationService {

    @Resource
    private RegionCollectItemEstimationRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public List<RegionCollectItemEstimationVo> findByCollectCode(String collectCode) {
        List<RegionCollectItemEstimation> list = repository.findByCollectCode(collectCode);
        for (RegionCollectItemEstimation data : list) {
            if (ObjectUtils.isNotEmpty(data.getSecondCostCategoryJsonStr())) {
                data.setSecondCostCategoryMap(JSONObject.parseObject(data.getSecondCostCategoryJsonStr(), LinkedHashMap.class));
            }
        }
        return (List<RegionCollectItemEstimationVo>) nebulaToolkitService.copyCollectionByWhiteList(list, RegionCollectItemEstimation.class, RegionCollectItemEstimationVo.class,
                HashSet.class, ArrayList.class, "secondCostCategoryMap");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCollectCodes(List<String> collectCodes) {
        repository.deleteByCollectCodes(collectCodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveList(List<RegionCollectItemEstimationVo> list, String collectCode) {
        repository.deleteByCollectCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<RegionCollectItemEstimation> dataList = list.stream().map(x -> {
            RegionCollectItemEstimation data = nebulaToolkitService.copyObjectByWhiteList(x, RegionCollectItemEstimation.class, HashSet.class, ArrayList.class);
            if (!CollectionUtils.isEmpty(data.getSecondCostCategoryMap())) {
                data.setSecondCostCategoryJsonStr(JSONObject.toJSONString(data.getSecondCostCategoryMap()));
            }
            data.setId(null);
            data.setCollectCode(collectCode);
            return data;
        }).collect(Collectors.toList());
        repository.saveBatch(dataList);
    }
}
