package com.biz.crm.tpm.business.activities.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/8 21:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_external_contract_used_log")
@Table(
        name = "tpm_external_contract_used_log",
        indexes = {
                @Index(name = "tpm_external_contract_used_log_index0", columnList = "index_key,years"),
                @Index(name = "tpm_external_contract_used_log_index2", columnList = "del_flag")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_external_contract_used_log", comment = "合同使用日志")
@ApiModel(value = "ExternalContractUsedLog", description = "合同使用日志")
public class ExternalContractUsedLog extends UuidFlagOpEntity {

    @ApiModelProperty("唯一标识")
    @Column(name = "index_key", columnDefinition = "varchar(32) comment '唯一标识'")
    private String indexKey;

    @ApiModelProperty("years")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;
}
