package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectMarketingEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectMarketingEstimationMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class RegionCollectMarketingEstimationRepository extends ServiceImpl<RegionCollectMarketingEstimationMapper, RegionCollectMarketingEstimation> {

    public List<RegionCollectMarketingEstimation> findByCollectCode(String collectCode) {
        return this.lambdaQuery()
                .eq(RegionCollectMarketingEstimation::getCollectCode, collectCode)
                .orderByAsc(RegionCollectMarketingEstimation::getSort)
                .list();
    }

    public List<RegionCollectMarketingEstimation> findByCollectCodes(List<String> collectCodes) {
        return this.lambdaQuery()
                .in(RegionCollectMarketingEstimation::getCollectCode, collectCodes)
                .orderByAsc(RegionCollectMarketingEstimation::getSort)
                .list();
    }

    public void deleteByCollectCodes(List<String> collectCodes) {
        this.lambdaUpdate()
                .in(RegionCollectMarketingEstimation::getCollectCode, collectCodes).remove();
    }
}
