package com.biz.crm.tpm.business.activities.overallplan.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanCaseService;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/8 21:02
 */
@RestController
@RequestMapping("/v1/overallPlanController")
@Api(tags = "指引方案")
public class OverallPlanController {

    @Autowired
    private OverallPlanCaseService service;

    @ApiOperation(value = "查询可承接的方案明细列表-总部、大区")
    @GetMapping("findBearPlanCaseList")
    public Result<Page<OverallPlanCaseVo>> findBearPlanCaseList(@PageableDefault(50) Pageable pageable, OverallPlanCaseVo vo) {
        return Result.ok(service.findBearPlanCaseList(pageable, vo));
    }
}
