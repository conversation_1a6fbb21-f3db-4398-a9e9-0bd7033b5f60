package com.biz.crm.tpm.business.activities.marketingplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanChangeLog;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanChangeLogVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/5 16:35
 */
public interface MarketingPlanChangeLogMapper extends BaseMapper<MarketingPlanChangeLog> {


    /**
     * 查询变更列表
     *
     * @param schemeCode
     * @return
     */
    List<MarketingPlanChangeLogVo> findChangeList(@Param("schemeCode") String schemeCode);
}
