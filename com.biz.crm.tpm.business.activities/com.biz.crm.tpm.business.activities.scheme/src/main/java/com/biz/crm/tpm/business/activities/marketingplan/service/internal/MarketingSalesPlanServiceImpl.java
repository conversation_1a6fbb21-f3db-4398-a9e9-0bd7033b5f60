package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.dms.business.allow.sale.sdk.list.service.AllowSaleListVoService;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerDockingVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerRelateOrgVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.inquiry.sdk.service.InquiryVoService;
import com.biz.crm.mdm.business.inquiry.sdk.vo.InquiryVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.price.sdk.dto.FindPriceDto;
import com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.constant.DictDataConstant;
import com.biz.crm.tpm.business.activities.marketingplan.constant.MarketingPlanConstant;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingSalesPlan;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingSalesPlanRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.rebate.sdk.enums.SalesPlanTransportEnum;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContext;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContextHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/12 09:39
 */
@Service
@Slf4j
public class MarketingSalesPlanServiceImpl extends BusinessPageCacheServiceImpl<MarketingSalesPlanVo, MarketingSalesPlanVo> implements MarketingSalesPlanService {

    @Resource
    private MarketingSalesPlanRepository marketingSalesPlanRepository;

    @Resource
    private MdmCostCenterVoService costCenterVoService;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private ProductVoService productVoService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private InquiryVoService priceModelVoService;

    @Resource
    private MaterialVoService materialVoService;

    @Resource
    private AllowSaleListVoService allowSaleListVoService;

    @Resource
    private OrgVoService orgVoService;

    public static final String column = ":";

    @Autowired
    private DictDataVoService dictDataVoService;


    /**
     * 查询分页
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MarketingSalesPlanVo> findCollectList(Pageable pageable, MarketingSalesPlanVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MarketingSalesPlanVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return marketingSalesPlanRepository.findCollectList(page, vo);
    }

    /**
     * 保存数据
     *
     * @param list
     * @param schemeCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MarketingSalesPlanVo> saveBatchSalesPlan(List<MarketingSalesPlanVo> list, String schemeCode, Boolean checkFlag, String changeFlag) {
        marketingSalesPlanRepository.deleteBySchemeCode(schemeCode);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        if (checkFlag) {
            for (MarketingSalesPlanVo planVo : list) {
                Validate.isTrue(planVo.getCheckFlag(), "校验不通过的数据不允许保存，请修改!");
            }
        }
        List<String> ruleCodes = generateCodeService.generateCode(MarketingPlanConstant.SALES_PLAN_RULE_CODE, list.size());
        int count = 0;
        List<MarketingSalesPlan> salesPlans = Lists.newArrayList();
        for (MarketingSalesPlanVo x : list) {
            x.setCode(ruleCodes.get(count++));
            MarketingSalesPlan salesPlan = nebulaToolkitService.copyObjectByBlankList(x, MarketingSalesPlan.class, HashSet.class, ArrayList.class);
            salesPlan.setSchemeCode(schemeCode);
            salesPlan.setId(null);
            salesPlan.setChangeFlag(changeFlag);
            salesPlans.add(salesPlan);
        }
        marketingSalesPlanRepository.saveBatch(salesPlans);
        return new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(salesPlans, MarketingSalesPlan.class, MarketingSalesPlanVo.class, HashSet.class, ArrayList.class));
    }


    /**
     * 查询列表
     *
     * @param schemeCode
     * @return
     */
    @Override
    public List<MarketingSalesPlanVo> findListBySchemeCode(String schemeCode) {
        List<MarketingSalesPlan> salesPlans = marketingSalesPlanRepository.findListBySchemeCode(schemeCode);
        if (CollectionUtils.isEmpty(salesPlans)) {
            return Lists.newArrayList();
        }
        return (List<MarketingSalesPlanVo>) nebulaToolkitService.copyCollectionByWhiteList(salesPlans, MarketingSalesPlan.class,
                MarketingSalesPlanVo.class, HashSet.class, ArrayList.class);
    }


    /**
     * 查询列表
     *
     * @param schemeCodes
     * @return
     */
    @Override
    public List<MarketingSalesPlanVo> findListBySchemeCodes(List<String> schemeCodes) {
        List<MarketingSalesPlan> salesPlans = marketingSalesPlanRepository.findListBySchemeCodes(schemeCodes);
        if (CollectionUtils.isEmpty(salesPlans)) {
            return Lists.newArrayList();
        }
        return (List<MarketingSalesPlanVo>) nebulaToolkitService.copyCollectionByWhiteList(salesPlans, MarketingSalesPlan.class,
                MarketingSalesPlanVo.class, HashSet.class, ArrayList.class);
    }

    private static final String CUSTOMER_TYPE = "Z009";

    private static final String TEN_CHANNEL = "10";

    private static final String TWENTY_CHANNEL = "20";

    /**
     * 校验销售计划
     *
     * @param voList
     * @param loginUserCodes
     * @param userName
     * @return
     */
    @Override
    public List<MarketingSalesPlanVo> checkSalesPlanList(List<MarketingSalesPlanVo> voList, String years, String schemeCode, String originalSchemeCode, List<String> loginUserCodes, String userName) {
        List<String> costCenterCodes = voList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCostCenterCode()))
                .map(MarketingSalesPlanVo::getCostCenterCode).collect(Collectors.toList());
        List<String> customerCodes = voList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()))
                .map(MarketingSalesPlanVo::getCustomerCode).collect(Collectors.toList());
        List<String> itemCodes = voList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getItemCode()))
                .map(MarketingSalesPlanVo::getItemCode).collect(Collectors.toList());
        List<String> productCodes = voList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductCode()))
                .map(MarketingSalesPlanVo::getProductCode).collect(Collectors.toList());

        Map<String, MdmCostCenterVo> costCenterMap = Maps.newHashMap();
        Map<String, CustomerVo> customerMap = Maps.newHashMap();
        Map<String, ProductVo> productMap = Maps.newHashMap();
//        Map<String, String> productItemMap = Maps.newHashMap();
        Map<String, Set<String>> allowProductMap = allowSaleListVoService.findAllowProductByCustomerCodes(customerCodes);
        //成本中心
        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(costCenterCodes);
        if (CollectionUtils.isNotEmpty(costCenterVoList)) {
            costCenterMap = costCenterVoList.stream().collect(Collectors.toMap(MdmCostCenterVo::getCostCenterCode, Function.identity()));
        }
        //客户
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
        if (CollectionUtils.isNotEmpty(customerVos)) {
            customerMap = customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
        }
        //产品
        List<ProductVo> productVoList = productVoService.findMainDetailsByProductCodes(productCodes);
        productMap = productVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductCode()))
                .collect(Collectors.toMap(ProductVo::getProductCode, Function.identity()));
        //品项
//        List<ProductItemVo> productItemVoList = productVoService.findItemListByItemCodes(itemCodes);
//        productItemMap = productItemVoList.stream().collect(Collectors.toMap(ProductItemVo::getItemCode, ProductItemVo::getItemName));
        for (MarketingSalesPlanVo vo : voList) {
            StringJoiner errMsg = new StringJoiner(";");
            if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getYears(), "年月", errMsg)) {
                try {
                    String date = vo.getYears() + "-01";
                    LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                } catch (Exception e) {
                    errMsg.add("年月格式错误,例如:yyyy-MM");
                }
                if (!vo.getYears().equals(years)) {
                    errMsg.add("销售计划年月必须等于方案年月");
                }
            }
            if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getCustomerCode(), "客户编码", errMsg)) {
                if (customerMap.containsKey(vo.getCustomerCode())) {
                    CustomerVo customerVo = customerMap.get(vo.getCustomerCode());
                    vo.setCustomerName(customerVo.getCustomerName());
                    vo.setErpCode(customerVo.getErpCode());
                    vo.setCompanyCode(customerVo.getCompanyCode());
                    vo.setProductGroupCode(customerVo.getProductGroupCode());
                    vo.setChannelCode(customerVo.getChannelCode());
                } else {
                    errMsg.add(String.format("客户编码%s在主数据中未查询到", vo.getCustomerCode()));
                }
            }
            if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getCostCenterCode(), "成本中心编码", errMsg)) {
                if (costCenterMap.containsKey(vo.getCostCenterCode())) {
                    MdmCostCenterVo costCenterVo = costCenterMap.get(vo.getCostCenterCode());
                    String companyCode = costCenterVo.getCompanyCode();
                    vo.setCostCenterName(costCenterMap.get(vo.getCostCenterCode()).getCostCenterName());
                    if (!vo.getCompanyCode().equals(companyCode)) {
                        errMsg.add("公司代码与对应成本中心的公司代码不匹配");
                    }
                    if (CollectionUtils.isNotEmpty(costCenterVo.getOrgList())) {
                        String orgCodes = costCenterVo.getOrgList().stream().map(x -> x.getOrgCode()).collect(Collectors.joining(","));
                        vo.setOrgCode(orgCodes);
                    } else {
                        errMsg.add(String.format("成本中心%s未绑定组织信息", vo.getCostCenterCode()));
                    }
                } else {
                    errMsg.add(String.format("成本中心%s在主数据中未查询到", vo.getCostCenterCode()));
                }
            }
//            if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getItemCode(), "品项编码", errMsg)) {
//                if (productItemMap.containsKey(vo.getItemCode())) {
//                    vo.setItemName(productItemMap.get(vo.getItemCode()));
//                }
//            }
            if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getProductCode(), "商品编码", errMsg)) {
                if (allowProductMap.containsKey(vo.getCustomerCode())) {
                    if (!allowProductMap.get(vo.getCustomerCode()).contains(vo.getProductCode())) {
                        errMsg.add("客户" + vo.getCustomerCode() + "维护的可购清单不包含商品" + vo.getProductCode());
                    } else {
                        if (productMap.containsKey(vo.getProductCode())) {
                            ProductVo productVo = productMap.get(vo.getProductCode());
                            vo.setProductName(productVo.getProductName());
                            vo.setSaleUnit(productVo.getSaleUnit());
                            vo.setTaxRate(ObjectUtils.defaultIfNull(productVo.getTaxRate(), BigDecimal.ZERO));
                            vo.setItemName(productVo.getProductPhaseName());
                            vo.setItemCode(productVo.getProductPhaseCode());
                            try {
                                vo.setConversionValue(BigDecimal.ONE);
                                if (ObjectUtils.isNotEmpty(productVo.getConversionValue())) {
                                    vo.setConversionValue(new BigDecimal(productVo.getConversionValue()));
                                }
                            } catch (Exception e) {
                                errMsg.add(String.format("产品%s转换系数不是数字", vo.getProductCode()));
                            }

                            if (MarketingPlanCaseCheckHelper.paramNotNull(productVo.getMaterialCode(), String.format("产品%s未绑定物料信息", vo.getProductCode()), errMsg)) {
                                vo.setMaterialCode(productVo.getMaterialCode());
                            }
                        }
                    }
                } else {
                    errMsg.add(String.format("客户%s未维护可购清单", vo.getCustomerCode()));
                }
            }
            MarketingPlanCaseCheckHelper.paramNotNull(vo.getEstimatedSalesVolume(), "预估销售量", errMsg);
            if (ObjectUtils.isNotEmpty(vo.getEstimatedCost()) && vo.getEstimatedCost().compareTo(BigDecimal.ZERO) == -1) {
                errMsg.add("预估销售金额不能为负数");
            }
            if (ObjectUtils.isNotEmpty(vo.getEstimatedSalesVolume()) && vo.getEstimatedSalesVolume().compareTo(BigDecimal.ZERO) == -1) {
                errMsg.add("预估销量不能为负数");
            }
            if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getTransportType(), "运输方式", errMsg)) {
                if (!SalesPlanTransportEnum.checkCode(vo.getTransportType())) {
                    errMsg.add("运输方式填写错误");
                }
            }
            if (CollectionUtils.isNotEmpty(loginUserCodes) && null != userName) {
                List<String> cpLoginUserOneLevelOrgCodes = Lists.newArrayList(loginUserCodes);
                List<DictDataVo> collaborationUserList = dictDataVoService.findTreeByDictTypeCode(DictDataConstant.TPM_COLLABORATION_CUSTOMER);
                List<String> collaborationCustomerCodes = collaborationUserList.stream().map(DictDataVo::getDictValue).collect(Collectors.toList());
                String curCustomerCode = vo.getCustomerCode();
                if (!collaborationCustomerCodes.contains(curCustomerCode)) {
                    log.info("checkSalesPlanList.customerVos: {}", CollectionUtils.isEmpty(customerVos)? "null" : JSON.toJSONString(customerVos));
                    Map<String, List<String>> dockingUserNameMap = customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, e -> CollectionUtils.isEmpty(e.getDockingList())? Lists.newArrayList() : e.getDockingList().stream().map(CustomerDockingVo::getUserName).filter(StringUtils::isNotEmpty).collect(Collectors.toList())));

                    Map<String, List<String>> customerOrgMap = customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, e -> e.getOrgList().stream().map(CustomerRelateOrgVo::getOrgCode).collect(Collectors.toList())));

                    log.info("checkSalesPlanList.customerDockingMap: {}", JSON.toJSONString(dockingUserNameMap));
                    log.info("checkSalesPlanList.oneLevelOrgMap: {}", JSON.toJSONString(customerOrgMap));
                    this.checkCustomerDockingAndOrg(vo, errMsg, dockingUserNameMap, customerOrgMap, userName, cpLoginUserOneLevelOrgCodes);
                }
            }


            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                vo.setCheckFlag(Boolean.FALSE);
                vo.setErrMsg(errMsg.toString());
            } else {
                vo.setCheckFlag(Boolean.TRUE);
                vo.setErrMsg(null);
                String onlyKey = vo.getYears() + ":" + vo.getCostCenterCode() + ":" + vo.getCustomerCode() + ":" + vo.getItemCode() + ":"
                        + vo.getProductCode();
                vo.setOnlyKey(onlyKey);
            }
        }

        Map<String, Long> map = voList.stream().filter(x -> x.getCheckFlag())
                .collect(Collectors.groupingBy(x -> x.getOnlyKey(), Collectors.counting()));
        if (ObjectUtils.isNotEmpty(map)) {
            map = map.entrySet().stream().filter(x -> x.getValue() > 1).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        List<String> customerCodeList = voList.stream().filter(x -> x.getCheckFlag())
                .map(x -> x.getCustomerCode()).collect(Collectors.toList());
        List<String> costCenterCodeList = voList.stream().filter(x -> x.getCheckFlag())
                .map(x -> x.getCostCenterCode()).collect(Collectors.toList());
        List<String> excludeSchemeCodes = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(schemeCode)) {
            excludeSchemeCodes.add(schemeCode);
        }
        if (ObjectUtils.isNotEmpty(originalSchemeCode)) {
            excludeSchemeCodes.add(originalSchemeCode);
        }
        List<MarketingSalesPlan> salesPlans = marketingSalesPlanRepository.findListByYearsAndCustomerAndCostCenter(customerCodeList, costCenterCodeList, years, excludeSchemeCodes);
        Map<String, String> salesPlanMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(salesPlans)) {
            salesPlanMap = salesPlans.stream().collect(Collectors.groupingBy(x -> x.getYears() + x.getCustomerCode() + x.getCostCenterCode(),
                    Collectors.mapping(MarketingSalesPlan::getSchemeCode, Collectors.collectingAndThen(
                            Collectors.toSet(), // 对 schemeCode 进行去重
                            set -> String.join(",", set) // 将去重后的 schemeCode 用逗号连接
                    ))));
        }
        List<MarketingSalesPlanVo> salesPlanVoList = voList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()) && ObjectUtils.isNotEmpty(x.getProductCode()) && ObjectUtils.isNotEmpty(x.getEstimatedSalesVolume()))
                .collect(Collectors.toList());
        Map<String, Map<String, InquiryVo>> cusPriceMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(salesPlanVoList)) {
            Map<String, Set<String>> listMap = salesPlanVoList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode(),
                    Collectors.mapping(MarketingSalesPlanVo::getProductCode, Collectors.toSet())));
            for (Map.Entry<String, Set<String>> entry : listMap.entrySet()) {
                FindPriceDto findPriceDto = new FindPriceDto();
                findPriceDto.setUserType(FindPriceUserTypeEnum.CUSTOMER.getDictCode());
                findPriceDto.setUserCode(entry.getKey());
                findPriceDto.setProductCodeSet(entry.getValue());
                String customerType = null;
                if (customerMap.containsKey(entry.getKey())) {
                    customerType = ObjectUtils.defaultIfNull(customerMap.get(entry.getKey()).getCustomerType(), null);
                }
                if (CUSTOMER_TYPE.equals(customerType)) {
                    findPriceDto.setChannelCode(TEN_CHANNEL);
                } else {
                    findPriceDto.setChannelCode(TWENTY_CHANNEL);
                }
                Map<String, InquiryVo> priceMap = this.priceModelVoService.findPrice((JSONObject) JSONObject.toJSON(findPriceDto));
                if (ObjectUtils.isEmpty(priceMap)) {
                    priceMap = Maps.newHashMap();
                }
                cusPriceMap.put(entry.getKey(), priceMap);
            }
        }
        for (MarketingSalesPlanVo vo : voList) {
            //价格
            if (ObjectUtils.isNotEmpty(vo.getEstimatedSalesVolume()) && cusPriceMap.containsKey(vo.getCustomerCode())) {
                Map<String, InquiryVo> inquiryVoMap = cusPriceMap.get(vo.getCustomerCode());
                if (inquiryVoMap.containsKey(vo.getProductCode())) {
                    InquiryVo inquiryVo = inquiryVoMap.get(vo.getProductCode());
                    vo.setSalesPrice(inquiryVo.getPrice());
                    //含税金额
                    vo.setTaxEstimatedCost(vo.getEstimatedSalesVolume().multiply(inquiryVo.getPrice()));
                    //未税金额
                    vo.setEstimatedCost(vo.getTaxEstimatedCost().divide(BigDecimal.ONE.add(ObjectUtils.defaultIfNull(vo.getTaxRate(), BigDecimal.ZERO)), 4, BigDecimal.ROUND_HALF_DOWN));
                } else {
                    StringJoiner errMsg = new StringJoiner(";");
                    errMsg.add(vo.getErrMsg());
                    errMsg.add("产品价格未找到");
                    vo.setErrMsg(errMsg.toString());
                    vo.setCheckFlag(Boolean.FALSE);
                }
            }
            if (vo.getCheckFlag()) {
                if (map.containsKey(vo.getOnlyKey())) {
                    vo.setErrMsg("销售计划维护重复，请调整！（维度：年月+成本中心+客户+品项+产品）");
                    vo.setCheckFlag(Boolean.FALSE);
                }
                String key = vo.getYears() + vo.getCustomerCode() + vo.getCostCenterCode();
                if (salesPlanMap.containsKey(key)) {
                    String schemeCodeJoiner = salesPlanMap.get(key);
                    vo.setErrMsg(String.format("同一部门下客户的销售计划不允许在多个方案中维护（维度：年月+成本中心+客户）！重复方案编码%s", schemeCodeJoiner));
                    vo.setCheckFlag(Boolean.FALSE);
                }
            }
        }
        List<MarketingSalesPlanVo> list = voList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCheckFlag()) && x.getCheckFlag()
                && ObjectUtils.isNotEmpty(x.getMaterialCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            Set<String> materialCodes = list.stream().map(x -> x.getMaterialCode()).collect(Collectors.toSet());
            Set<String> companyCodeSet = list.stream().map(x -> x.getCompanyCode()).collect(Collectors.toSet());

            MaterialSearchDto searchDto = new MaterialSearchDto();
            searchDto.setMaterialCodeSet(materialCodes);
            searchDto.setCompanyCodeSet(companyCodeSet);
            List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
            Map<String, BigDecimal> materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO)));

            for (MarketingSalesPlanVo planVo : list) {
                if (ObjectUtils.isNotEmpty(planVo.getSalesPrice()) && planVo.getSalesPrice().compareTo(BigDecimal.ZERO) == 1) {
                    if (materialMap.containsKey(planVo.getMaterialCode())) {
                        BigDecimal costPrice = materialMap.get(planVo.getMaterialCode());
                        planVo.setCostPrice(costPrice);
                        if (ObjectUtils.isNotEmpty(planVo.getSalesPrice()) && planVo.getSalesPrice().compareTo(BigDecimal.ZERO) == 1) {
                            BigDecimal ratio = (planVo.getSalesPrice().subtract(costPrice)).divide(planVo.getSalesPrice(), 4, BigDecimal.ROUND_HALF_DOWN);
                            String grossProfitRate = ratio.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%";
                            planVo.setGrossProfitRate(grossProfitRate);
                        }
                    } else {
                        planVo.setErrMsg("物料成本未找到");
                        planVo.setCheckFlag(Boolean.FALSE);
                    }
                }
            }
        }
        return voList;
    }

    private void checkCustomerDockingAndOrg(MarketingSalesPlanVo vo, StringJoiner errMsg, Map<String, List<String>> customerDockingMap,
                                            Map<String, List<String>> customerOrgMap, String loginUserName, List<String> loginUserOneLevelOrgCodes) {
        String customerCode = vo.getCustomerCode();
        log.info("planCheckCustomerDockingAndOrg.customerCode: {}, loginUserName: {}, loginUserOneLevelOrgCodes: {}", customerCode, loginUserName, loginUserOneLevelOrgCodes);
        boolean dockingFlag = false;
        boolean orgFlag = false;
        List<String> dockingUserNames = customerDockingMap.get(customerCode);
        if (CollectionUtils.isNotEmpty(dockingUserNames) && !dockingUserNames.contains(loginUserName)) {
            dockingFlag = true;
        }
        List<String> curOrgCodes = customerOrgMap.getOrDefault(customerCode, Lists.newArrayList());
        loginUserOneLevelOrgCodes.retainAll(curOrgCodes);
        if (CollectionUtils.isEmpty(loginUserOneLevelOrgCodes)) {
            orgFlag = true;
        }
        log.info("planCheckCustomerDockingAndOrg.dockingUserNames: {}, oneLevelOrgCode: {}", dockingUserNames, loginUserOneLevelOrgCodes);
        if (dockingFlag && orgFlag) {
            errMsg.add("客户绑定的业务员不包含当前操作者，或客户不属于当前登录者组织及下级组织");
        }
    }


    @Override
    public List<MarketingSalesPlanVo> findListBySalesPlanQueryVo(SalesPlanQueryVo vo) {
        return marketingSalesPlanRepository.findListBySalesPlanQueryVo(vo);
    }

    @Override
    public List<MarketingSalesPlanVo> findListByConditionToMarketingScheme(Set<String> yearsSet, Set<String> costCenterCodes, Set<String> customerCodes, Set<String> itemCodes,
                                                                           Set<String> productCodes, List<String> excludeSchemeCodes, List<String> includeSchemeCodes, String changeFlag) {
        return marketingSalesPlanRepository.findListByCondition(yearsSet, costCenterCodes, customerCodes, itemCodes, productCodes, excludeSchemeCodes, includeSchemeCodes, changeFlag, null);
    }


    /**
     * 营销方案计算专用
     *
     * @param yearsSet
     * @param costCenterCodes
     * @param customerCodes
     * @param itemCodes
     * @param productCodes
     * @param excludeSchemeCodes
     * @param includeSchemeCodes
     * @param changeFlag
     * @return
     */
    @Override
    public List<MarketingSalesPlanVo> findListByConditionToCalMarketing(Set<String> yearsSet, Set<String> costCenterCodes, Set<String> customerCodes, Set<String> itemCodes, Set<String> productCodes, List<String> excludeSchemeCodes, List<String> includeSchemeCodes, String changeFlag) {
        return marketingSalesPlanRepository.findListByCondition(yearsSet, costCenterCodes, customerCodes, itemCodes,
                productCodes, excludeSchemeCodes, includeSchemeCodes, changeFlag, BooleanEnum.TRUE.getCapital());
    }

    @Override
    public void deleteBySchemeCodes(List<String> schemeCodes) {
        marketingSalesPlanRepository.deleteBySchemeCodes(schemeCodes);
    }


    @Override
    public void salesPlanSaveCurrentCachePage(String cacheKey, List<MarketingSalesPlanVo> saveList, String schemeCode, String originalSchemeCode, List<String> loginUserCodes, String userName) {
        String[] cacheKeys = cacheKey.split(":");
        Validate.isTrue(cacheKeys.length == 3, "缺少关键数据");
        String configCode = cacheKeys[1];
        Validate.isTrue(MarketingSalesPlanTypeEnum.sales_plan.getCode().equals(configCode), "销售计划表单类型编码错误");
        String years = cacheKeys[2];
        //校验数据
        List<MarketingSalesPlanVo> salesPlanVoList = this.checkSalesPlanList(saveList, years, schemeCode, originalSchemeCode, loginUserCodes, userName);
        for (MarketingSalesPlanVo marketingSalesPlanVo : salesPlanVoList) {
            String schemeType = marketingSalesPlanVo.getSchemeType();
            if(StringUtils.equals("o_two_o",schemeType)
                    || StringUtils.equals("plan",schemeType)
                    || StringUtils.equals("append",schemeType)
                    || StringUtils.equals("contract",schemeType)
                    || StringUtils.equals("additional",schemeType)){
                // 首次初始化原始预估销售金额(未税)
                if(Objects.isNull(marketingSalesPlanVo.getOriginEstimatedCost())){
                    marketingSalesPlanVo.setOriginEstimatedCost(marketingSalesPlanVo.getEstimatedCost());
                }
            }
        }
        super.saveCurrentPageCache(cacheKey, salesPlanVoList);
    }


    /**
     * 经销商渠道
     */
    private final static String DEALER_CHANNEL = "1";


    @Override
    public List<MarketingSalesPlanVo> findListByOrgCodesAndYears(WithholdingIncomeQueryDto dto) {
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllChildrenByOrgCodesMap(dto.getOrgCodes());

        MarsAuthorityContext marsAuthorityContext = MarsAuthorityContextHolder.getContext();
        marsAuthorityContext.setListCode(null);

        List<MarketingSalesPlanVo> dataList = Lists.newArrayList();
        if (orgMap != null && !orgMap.isEmpty()) {
            Map<String, Set<String>> orgOostCenterMap = orgMap.values().stream().flatMap(List::stream)
                    .filter(x -> CollectionUtils.isNotEmpty(x.getCostCenterCodeSet()))
                    .collect(Collectors.toMap(x -> x.getOrgCode(), OrgVo::getCostCenterCodeSet));
            Set<String> costCenterCodes = orgOostCenterMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
            SalesPlanQueryVo queryVo = new SalesPlanQueryVo() {{
                this.setYears(dto.getYears());
                this.setYearsList(dto.getYearsList());
                this.setCostCenterCodeSet(costCenterCodes);
            }};

            List<String> customerCodes = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(dto.getChannelDepartmentCodeList())) {
                List<CustomerVo> customerVoList = customerVoService.findCustomerListByChannelDepartmentCodeList(dto.getChannelDepartmentCodeList());
                if (CollectionUtils.isEmpty(customerVoList)) {
                    return Lists.newArrayList();
                }
                customerCodes = customerVoList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toList());
            }

            List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanRepository.findListBySalesPlanQueryVo(queryVo);
            List<MarketingSalesPlanVo> resultList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(dto.getChannelDepartmentCodeList())) {
                List<String> finalCustomerCodes = customerCodes;
                resultList = salesPlanVoList.stream().filter(x-> finalCustomerCodes.contains(x.getCustomerCode())).collect(Collectors.toList());
            } else {
                resultList = salesPlanVoList;
            }

            if (CollectionUtils.isNotEmpty(resultList)) {
                for (Map.Entry<String, Set<String>> entry : orgOostCenterMap.entrySet()) {
                    Set<String> costCenterCodeSet = entry.getValue();
                    for (MarketingSalesPlanVo planVo : resultList) {
                        //判断是否包含
                        if (costCenterCodeSet.contains(planVo.getCostCenterCode())) {
                            planVo.setOrgCode(entry.getKey());
                        }
                    }
                }
                Map<String, List<MarketingSalesPlanVo>> resultDataList = resultList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode()))
                        .collect(Collectors.groupingBy(x -> x.getOrgCode() + ":" + x.getYears()));
                for (Map.Entry<String, List<MarketingSalesPlanVo>> entry : resultDataList.entrySet()) {
                    BigDecimal estimatedCost = entry.getValue().stream().map(x -> x.getEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal taxEstimatedCost = entry.getValue().stream().map(x -> x.getTaxEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    String[] keys = entry.getKey().split(":");
                    MarketingSalesPlanVo vo = new MarketingSalesPlanVo();
                    vo.setOrgCode(keys[0]);
                    vo.setYears(keys[1]);
                    vo.setEstimatedCost(estimatedCost);
                    vo.setTaxEstimatedCost(taxEstimatedCost);
                    dataList.add(vo);
                }
            }
        }
        return dataList;
    }

    @Override
    public List<MarketingSalesPlanVo> findByCollectCode(String collectCode) {
        return marketingSalesPlanRepository.findByCollectCode(collectCode);
    }

    @Autowired(required = false)
    private RedisService redisService;

    @Autowired(required = false)
    private RedisTemplate redisTemplate;


    @Override
    public Page<MarketingSalesPlanVo> findCachePageList(Pageable pageable, MarketingSalesPlanVo vo, String cacheKey) {
        String redisCacheIdKey = helper.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = helper.getRedisCacheDataKey(cacheKey);
        String redisCacheInitKey = helper.getRedisCacheInitKey(cacheKey);
        Page<MarketingSalesPlanVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        page.setTotal(0);
        page.setRecords(Lists.newArrayList());

        a:
        if (redisService.hasKey(redisCacheIdKey)) {
            //判断查询条件不为空
            if (ObjectUtils.isNotEmpty(vo) && (ObjectUtils.isNotEmpty(vo.getCustomerCode()) || ObjectUtils.isNotEmpty(vo.getCustomerName()) ||
                    ObjectUtils.isNotEmpty(vo.getCostCenterName()) || ObjectUtils.isNotEmpty(vo.getProductName()))) {
                List<MarketingSalesPlanVo> dataList = findCacheList(cacheKey);
                if (CollectionUtils.isNotEmpty(dataList)) {
                    // 简化过滤逻辑
                    dataList = dataList.stream()
                            .filter(x ->
                                    (ObjectUtils.isEmpty(vo.getCustomerCode()) || (ObjectUtils.isNotEmpty(x.getCustomerCode()) && x.getCustomerCode().contains(vo.getCustomerCode()))) &&
                                            (ObjectUtils.isEmpty(vo.getCustomerName()) || (ObjectUtils.isNotEmpty(x.getCustomerName()) && x.getCustomerName().contains(vo.getCustomerName()))) &&
                                            (ObjectUtils.isEmpty(vo.getCostCenterName()) || (ObjectUtils.isNotEmpty(x.getCostCenterName()) && x.getCostCenterName().contains(vo.getCostCenterName()))) &&
                                            (ObjectUtils.isEmpty(vo.getProductName()) || (ObjectUtils.isNotEmpty(x.getProductName()) && x.getProductName().contains(vo.getProductName())))
                            )
                            .collect(Collectors.toList());

                    // 设置总记录数
                    page.setTotal(dataList.size());

                    // 获取分页后的列表
                    int start = Math.min((int) page.offset(), dataList.size());
                    int end = Math.min(start + (int) page.offset() + (int) page.getSize(), dataList.size()); // 避免越界
                    List<MarketingSalesPlanVo> pagedData = dataList.subList(start, end);

                    // 如果需要 `idList`
                    List<String> idList = pagedData.stream().map(MarketingSalesPlanVo::getId).collect(Collectors.toList());

                    if (!org.springframework.util.CollectionUtils.isEmpty(idList)) {
                        List<MarketingSalesPlanVo> resultDataList = redisTemplate.opsForHash().multiGet(redisCacheDataKey, idList);
                        List<MarketingSalesPlanVo> dtoList = resultDataList;
                        List<MarketingSalesPlanVo> voList = helper.dtoListToVoList(dtoList);
                        page.setRecords(voList);
                    }
                }
            } else {
                //redis里面有的话直接从redis里面取
                Long total = redisService.lSize(redisCacheIdKey);
                page.setTotal(total);
                List<Object> idList = redisService.lRange(redisCacheIdKey, page.offset(), page.offset() + page.getSize() - 1);
                if (!org.springframework.util.CollectionUtils.isEmpty(idList)) {
                    List<MarketingSalesPlanVo> dataList = redisTemplate.opsForHash().multiGet(redisCacheDataKey, idList);
                    List<MarketingSalesPlanVo> dtoList = dataList;
                    List<MarketingSalesPlanVo> voList = helper.dtoListToVoList(dtoList);
                    page.setRecords(voList);
                }
            }
        } else if (!redisService.hasKey(redisCacheInitKey) && null != vo) {
            //标记为已初始化，不重复初始化
            redisService.set(redisCacheInitKey, BooleanEnum.TRUE, helper.getExpireTime());
            //放到try catch里面 是为了以防分页从数据库查询报错了 初始化状态被确认 修正报错过后查询为空的情况
            try {
                //redis里面没有
                List<MarketingSalesPlanVo> dtoList = helper.findDtoListFromRepository(vo, cacheKey);

                if (org.springframework.util.CollectionUtils.isEmpty(dtoList)) {
                    redisService.del(redisCacheInitKey);
                    break a;
                }

                if (helper.initToCacheFromRepository()) {
                    helper.putCache(cacheKey, dtoList);
                }

                //放到缓存里面
                page.setTotal(dtoList.size());
                long start = page.offset();
                if (page.getTotal() > start) {
                    long end = page.offset() + page.getSize();
                    if (page.getTotal() < end) {
                        end = page.getTotal();
                    }
                    List<MarketingSalesPlanVo> recordDtoList = dtoList.subList((int) page.offset(), (int) end);
                    List<MarketingSalesPlanVo> voList = helper.dtoListToVoList(recordDtoList);
                    page.setRecords(voList);
                }
            } catch (Exception e) {
                redisService.del(redisCacheInitKey);
            }
        }
        //更新下VO里面的字段值
        helper.fillVoListProperties(page.getRecords());
        return page;
    }
}
