package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.entity.PlanBigDateExcution
 * @description: 大日期回调执行计划
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 13:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_big_date_collect")
@Table(
        name = "tpm_big_date_collect",
        indexes = {
                @Index(name = "tpm_plan_big_date_execution_collect_index0", columnList = "scheme_detail_code"),
                @Index(name = "tpm_plan_big_date_execution_collect_index1", columnList = "scheme_code"),
                @Index(name = "tpm_plan_big_date_execution_collect_index3", columnList = "scheme_detail_code,create_post_code,customer_code,dynamic_key,dynamic_form_code,parent_code,execution_time", unique = true),
                @Index(name = "tpm_plan_big_date_execution_collect_index4", columnList = "act_execute_code"),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_big_date_collect", comment = "大日期回调执行计划")
@ApiModel(value = "PlanBigDateExecutionCollect", description = "大日期回调执行计划")
public class PlanBigDateExecutionCollect extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    @Column(name = "act_execute_code", columnDefinition = "varchar(32) comment '活动执行编码'")
    private String actExecuteCode;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("执行人")
    @Column(name = "executor", length = 64, columnDefinition = "VARCHAR(64) COMMENT '执行人'")
    private String executor;

    @ApiModelProperty("执行时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "execute_date", columnDefinition = "DATETIME COMMENT '执行时间 '")
    private Date executeDate;

    //仓库信息
    @ApiModelProperty("省编码")
    @Column(name = "province_code", columnDefinition = "VARCHAR(32) COMMENT '省编码'")
    private String provinceCode;

    @ApiModelProperty("省名称")
    @Column(name = "province_name", columnDefinition = "VARCHAR(128) COMMENT '省名称'")
    private String provinceName;

    @ApiModelProperty("市编码")
    @Column(name = "city_code", columnDefinition = "VARCHAR(32) COMMENT '市编码'")
    private String cityCode;

    @ApiModelProperty("市名称")
    @Column(name = "city_name", columnDefinition = "VARCHAR(128) COMMENT '市名称'")
    private String cityName;

    @ApiModelProperty("区编码")
    @Column(name = "district_code", columnDefinition = "VARCHAR(32) COMMENT '区编码'")
    private String districtCode;

    @ApiModelProperty("区名称")
    @Column(name = "district_name", columnDefinition = "VARCHAR(128) COMMENT '区名称'")
    private String districtName;

//    @ApiModelProperty("经度")
//    @Column(name = "longitude", columnDefinition = "decimal(12,8) COMMENT '经度'")
//    private BigDecimal longitude;
//
//    @ApiModelProperty("纬度")
//    @Column(name = "latitude", columnDefinition = "decimal(12,8) COMMENT '纬度'")
//    private BigDecimal latitude;

    @ApiModelProperty("仓库地址")
    @Column(name = "warehouse_address", columnDefinition = "VARCHAR(256) COMMENT '仓库地址'")
    private String warehouseAddress;

    @ApiModelProperty("仓库id")
    @Column(name = "warehouse_id", columnDefinition = "VARCHAR(32) COMMENT '仓库id'")
    private String warehouseId;

    /**
     * 执行时间 yyyy-mm-dd
     */
    @ApiModelProperty("执行时间")
    @Column(name = "execution_time", columnDefinition = "varchar(32) comment '执行时间'")
    private String executionTime;

    /****************************SAF步骤所需基础字段*******************************************/

    @ApiModelProperty("执行计划业务编码")
    @TableField(value = "parent_code")
    @Column(name = "parent_code", length = 64, columnDefinition = "varchar(64) COMMENT '执行计划业务编码'")
    private String parentCode;

    @ApiModelProperty("步骤业务编码stepCode")
    @TableField(value = "dynamic_key")
    @Column(name = "dynamic_key", length = 64, columnDefinition = "varchar(64) COMMENT '步骤业务编码stepCode'")
    private String dynamicKey;

    @ApiModelProperty("动态表单全局唯一编码formCode")
    @TableField(value = "dynamic_form_code")
    @Column(name = "dynamic_form_code", columnDefinition = "varchar(255) COMMENT '动态表单全局唯一编码formCode'")
    private String dynamicFormCode;

    @Column(name = "longitude", columnDefinition = "decimal(12,8) COMMENT '经度'")
    @ApiModelProperty("位置信息:经度")
    private BigDecimal longitude;

    @Column(name = "latitude", columnDefinition = "decimal(12,8) COMMENT '纬度'")
    @ApiModelProperty("位置信息:纬度")
    private BigDecimal latitude;

    @ApiModelProperty("创建人组织")
    @Column(name = "create_org_code", columnDefinition = "varchar(32) comment '创建人组织'")
    private String createOrgCode;

    @ApiModelProperty("创建人组织名称")
    @Column(name = "create_org_name", columnDefinition = "varchar(64) comment '创建人组织名称'")
    private String createOrgName;

    @ApiModelProperty("创建人职位编码")
    @Column(name = "create_post_code", columnDefinition = "varchar(32) comment '创建人职位编码'")
    private String createPostCode;

    @ApiModelProperty("创建人职位名称")
    @Column(name = "create_post_name", columnDefinition = "varchar(64) comment '创建人职位名称'")
    private String createPostName;

    @ApiModelProperty("是否展示水印(Y:是,N:否)")
    @Column(name = "watermark_flag", columnDefinition = "varchar(10) default 'Y' COMMENT '是否展示水印，Y是N否'")
    private String watermarkFlag;


    @ApiModelProperty("水印")
    @Column(name = "watermark_str", columnDefinition = "varchar(512) comment '水印'")
    private String watermarkStr;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("产品明细")
    private List<PlanBigDateExecutionCollectDetail> expirationBackProducts;
}
