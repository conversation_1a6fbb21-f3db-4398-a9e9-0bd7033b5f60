package com.biz.crm.tpm.business.activities.marketingplan.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/25 15:26
 */
@Data
public class CustomerGainsAndLossesVo {

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("预算收入")
    private BigDecimal budgetIncome;

    @ApiModelProperty("规划收入")
    private BigDecimal planIncome;

    @ApiModelProperty("收入达成率")
    private BigDecimal incomeAchieveRatio;

    private String incomeAchieveRatioStr;

    @ApiModelProperty("预算费率")
    private BigDecimal budgetRatio;

    private String budgetRatioStr;

    @ApiModelProperty("规划费率")
    private BigDecimal planRatio;

    @ApiModelProperty("规划费率")
    private String planRatioStr;

    @ApiModelProperty("费率偏差")
    private BigDecimal ratioDeviation;

    @ApiModelProperty("费率偏差")
    private String ratioDeviationStr;

    @ApiModelProperty("利润率")
    private BigDecimal profitRatio;

    @ApiModelProperty("利润率")
    private String profitRatioStr;

    @ApiModelProperty("毛利率")
    private BigDecimal grossProfitRatio;

    @ApiModelProperty("毛利率")
    private String grossProfitRatioStr;

    @ApiModelProperty("物流费率")
    private BigDecimal logisticsRatio;

    @ApiModelProperty("物流费率")
    private String logisticsRatioStr;

    @ApiModelProperty("营销费率")
    private BigDecimal marketingRatio;

    @ApiModelProperty("营销费率")
    private String marketingRatioStr;

    @ApiModelProperty("公摊费率")
    private BigDecimal publicShareRatio;

    @ApiModelProperty("公摊费用")
    private BigDecimal publicShare;

    private String publicShareRatioStr;

    @ApiModelProperty("二级费用大类")
    private Map<String, BigDecimal> secondCostCategoryMap;

    @ApiModelProperty("二级费用大类-json串")
    private String secondCostCategoryJsonStr;

    @ApiModelProperty("促销")
    private BigDecimal promotion;

    private BigDecimal promotionAmount;

    @ApiModelProperty("促销")
    private String promotionStr;

    @ApiModelProperty("回调")
    private BigDecimal callback;

    private BigDecimal callbackAmount;

    @ApiModelProperty("回调")
    private String callbackStr;

    @ApiModelProperty("陈列")
    private BigDecimal display;

    private BigDecimal displayAmount;

    @ApiModelProperty("陈列")
    private String displayStr;

    @ApiModelProperty("人员推广")
    private BigDecimal generalization;

    private BigDecimal generalizationAmount;

    @ApiModelProperty("人员推广")
    private String generalizationStr;

    @ApiModelProperty("品宣")
    private BigDecimal disseminate;

    private BigDecimal disseminateAmount;

    @ApiModelProperty("品宣")
    private String disseminateStr;

    @ApiModelProperty("销售奖励")
    private BigDecimal salesReward;

    private BigDecimal salesRewardAmount;

    @ApiModelProperty("销售奖励")
    private String salesRewardStr;

    @ApiModelProperty("合同")
    private BigDecimal contract;

    private BigDecimal contractAmount;

    @ApiModelProperty("合同")
    private String contractStr;


    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("预算总额")
    private BigDecimal budgetTotalAmount;

    @ApiModelProperty("规划总额")
    private BigDecimal planTotalAmount;

    @ApiModelProperty("生产成本")
    private BigDecimal productionCosts;

    @ApiModelProperty("产品运输费用")
    private BigDecimal productTransportCost;

    @ApiModelProperty("周边运输费用")
    private BigDecimal peripheryTransportCost;

    @ApiModelProperty("搭赠费用")
    private BigDecimal giftCost;

    @ApiModelProperty("营销费用")
    private BigDecimal marketingCost;

    @ApiModelProperty("规划利润额")
    private BigDecimal planProfitMargin;

    @ApiModelProperty("规划毛利额")
    private BigDecimal planGrossProfitMargin;

    @ApiModelProperty("关键唯一字段")
    private String indexKey;

    @ApiModelProperty("关键唯一字段")
    private String indexKeyExceptOrg;

    @ApiModelProperty("是否需要分摊")
    private Boolean beSplitting;

    @ApiModelProperty("分摊费率")
    private BigDecimal rate;

    private Set<String> costCenterCodes;

    @ApiModelProperty("是否参与计算")
    private Boolean calFlag;

    @ApiModelProperty("费用转移")
    private BigDecimal costShift;

    @ApiModelProperty("考核利润")
    private BigDecimal assessProfits;
}
