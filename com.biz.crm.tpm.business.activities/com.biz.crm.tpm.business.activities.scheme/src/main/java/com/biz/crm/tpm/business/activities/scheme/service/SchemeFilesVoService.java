package com.biz.crm.tpm.business.activities.scheme.service;

import com.biz.crm.tpm.business.activities.scheme.vo.SchemeFilesVo;

import java.util.Set;

/**
 * 方案附件;(tpm_scheme_files)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
public interface SchemeFilesVoService {

  /**
   * 通过方案编号查询附件数据
   *
   * @param schemeCode 方案编号
   * @return 多条数据
   */
  Set<SchemeFilesVo> findBySchemeCode(String schemeCode);

  /**
   * 新增数据
   *
   * @param schemeFilesVo 实体对象
   * @return 新增结果
   */
  SchemeFilesVo create(SchemeFilesVo schemeFilesVo);

  /**
   * 批量新增
   *
   * @param schemeFilesVos
   * @return
   */
  Set<SchemeFilesVo> createBatch(Set<SchemeFilesVo> schemeFilesVos);

  /**
   * 删除数据
   *
   * @param schemeCode 方案编号
   */
  void deleteBySchemeCode(String schemeCode);
}