package com.biz.crm.tpm.business.activities.overallplan.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanService;
import com.biz.crm.tpm.business.activities.overallplan.vo.HeadOverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/v1/regionOverallPlanController")
@Api(tags = "大区方案统筹")
public class RegionOverallPlanController extends BusinessPageCacheController<OverallPlanCaseVo, OverallPlanCaseVo> {

    @Autowired
    private OverallPlanService overallPlanService;

    @Resource
    private RedisLockService redisLockService;

    @ApiOperation(value = "分页查询")
    @GetMapping("findList")
    public Result<Page<OverallPlanVo>> findList(@PageableDefault(50) Pageable pageable, OverallPlanVo vo) {
        vo.setSchemeType(OverallPlanSchemeTypeEnum.REGION.getCode());
        return Result.ok(overallPlanService.findList(pageable, vo));
    }


    @ApiOperation(value = "查询详情")
    @GetMapping("findById")
    public Result<OverallPlanVo> findById(@RequestParam String id) {
        return Result.ok(overallPlanService.findById(id, OverallPlanSchemeTypeEnum.REGION.getCode()));
    }

    @ApiOperation(value = "暂存-新增/编辑")
    @PostMapping("stagingSaveOrUpdateHead")
    public Result stagingSaveOrUpdateHead(@RequestBody OverallPlanVo vo) {
        vo.setSchemeType(OverallPlanSchemeTypeEnum.REGION.getCode());
        String id = null;
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            id = overallPlanService.updateOverallPlan(vo, Boolean.FALSE);
        } else {
            id = overallPlanService.createOverallPlan(vo, Boolean.FALSE);
        }
        OverallPlanVo dataVo = overallPlanService.findById(id, OverallPlanSchemeTypeEnum.REGION.getCode());
        return Result.ok(dataVo);
    }

    @ApiOperation(value = "新增/编辑")
    @PostMapping("saveOrUpdateRegion")
    public Result saveOrUpdateRegion(@RequestBody OverallPlanVo vo) {
        vo.setSchemeType(OverallPlanSchemeTypeEnum.REGION.getCode());
        String id = null;
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            id = overallPlanService.updateOverallPlan(vo, Boolean.FALSE);
        } else {
            id = overallPlanService.createOverallPlan(vo, Boolean.FALSE);
        }
        OverallPlanVo dataVo = overallPlanService.findById(id, OverallPlanSchemeTypeEnum.REGION.getCode());
        return Result.ok(dataVo);
    }

    @ApiOperation(value = "提交新增/编辑")
    @PostMapping("submitSaveOrUpdateRegion")
    public Result submitSaveOrUpdateRegion(@RequestBody OverallPlanVo vo) {
        vo.setSchemeType(OverallPlanSchemeTypeEnum.REGION.getCode());
        if (ObjectUtils.isEmpty(vo.getId())) {
            overallPlanService.submitCreate(vo);
        } else {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            String redisLockKey = OverallPlanConstant.getRedisLockKey(vo.getId());
            Boolean lockFlag = redisLockService.tryLock(redisLockKey, TimeUnit.MINUTES, OverallPlanConstant.OVERALL_PLAN_LOCK_TIME_20);
            Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
            try {
                overallPlanService.submitUpdate(vo);
            } finally {
                redisLockService.unlock(redisLockKey);
            }


        }
        return Result.ok();
    }

    @ApiOperation(value = "提交审批")
    @PostMapping("submitByIdList")
    public Result submitByIdList(@RequestBody List<String> idList) {
        Boolean lockFlag = redisLockService.batchLock(OverallPlanConstant.OVERALL_PLAN_LOCK, idList, TimeUnit.MINUTES, OverallPlanConstant.OVERALL_PLAN_LOCK_TIME_20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        try {
            for (String id : idList) {
                overallPlanService.submitById(id, OverallPlanSchemeTypeEnum.REGION.getCode());
            }
        } finally {
            redisLockService.batchUnLock(OverallPlanConstant.OVERALL_PLAN_LOCK, idList);
        }
        return Result.ok();
    }

    @ApiOperation(value = "手动审批")
    @PostMapping("manualApproval")
    public Result manualApproval(@RequestBody List<String> idList) {
        for (String id : idList) {
            overallPlanService.manualApproval(id, OverallPlanSchemeTypeEnum.REGION.getCode());
        }
        return Result.ok();
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("deleteByIds")
    public Result deleteByIds(@RequestParam("idList") List<String> idList) {
        Boolean lockFlag = redisLockService.batchLock(OverallPlanConstant.OVERALL_PLAN_LOCK, idList, TimeUnit.MINUTES, OverallPlanConstant.OVERALL_PLAN_LOCK_TIME_20);
        Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
        String schemeName = null;
        try {
            schemeName = overallPlanService.deleteByIdList(idList);
        } finally {
            redisLockService.batchUnLock(OverallPlanConstant.OVERALL_PLAN_LOCK, idList);
        }
        if (ObjectUtils.isNotEmpty(schemeName)) {
            return Result.error(String.format("方案%s状态是审批中/审批通过，不允许删除", schemeName));
        }
        return Result.ok();
    }

    /**
     * 流程撤回
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "流程撤回")
    @GetMapping("recover")
    public Result<?> recover(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                             @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
        try {
            this.overallPlanService.recover(code, remark);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "查询总部方案明细列表")
    @GetMapping("findHeadOverallPlanCaseList")
    public Result<Page<OverallPlanCaseVo>> findHeadOverallPlanCaseList(@PageableDefault(50) Pageable pageable, HeadOverallPlanCaseVo vo) {
        return Result.ok(overallPlanService.findHeadOverallPlanCaseList(pageable, vo));
    }


    @ApiOperation(value = "查询总部方案明细-区域直接承接的")
    @GetMapping("findHeadOverallPlanCaseToMarketing")
    public Result<Page<OverallPlanCaseVo>> findHeadOverallPlanCaseToMarketing(@PageableDefault(50)Pageable pageable,HeadOverallPlanCaseVo vo){
        return Result.ok(overallPlanService.findHeadOverallPlanCaseToMarketing(pageable, vo));
    }

    @ApiOperation(value = "通过方案明细编码查询方案明细列表")
    @PostMapping("findOverallPlanCaseListBySchemeDetailCodes")
    public Result findOverallPlanCaseListBySchemeDetailCodes(@RequestBody List<String> schemeDetailCodeList) {
        return Result.ok(overallPlanService.findOverallPlanCaseListBySchemeDetailCodes(schemeDetailCodeList));
    }


    @ApiOperation(value = "查询关联的总部方案明细")
    @GetMapping("findReleaseHeadCaseList")
    public Result findReleaseHeadCaseList(@RequestBody List<String> schemeDetailCodes) {
        return Result.ok(overallPlanService.findReleaseHeadCaseList(schemeDetailCodes));
    }

    @GetMapping("getTotalAmount")
    @ApiOperation(value = "获取统计值")
    public Result getTotalAmount(@RequestParam String cacheKey) {
        return Result.ok(overallPlanService.getTotalAmount(cacheKey));
    }

    @Override
    @ApiOperation(value = "保存当前页数据到缓存并返回指定页数据接口")
    @PostMapping("saveCurrentPageCache")
    public Result<Page<OverallPlanCaseVo>> saveCurrentPageCache(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                @ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                                                OverallPlanCaseVo caseVo,
                                                                @RequestBody List<OverallPlanCaseVo> saveList) {
        String regionCacheKey = cacheKey + ":" + OverallPlanSchemeTypeEnum.REGION.getCode();
        overallPlanService.saveCurrentPageCache(regionCacheKey, saveList);
        Page<OverallPlanCaseVo> page = this.overallPlanService.findCachePageList(pageable, caseVo, cacheKey);
        return Result.ok(page);
    }


    @GetMapping("checkRegionOverall")
    @ApiOperation(value = "验证是否有大区指引案")
    public Result checkRegionOverall(@Param("years") String years) {
        overallPlanService.checkRegionOverall(years);
        return Result.ok();
    }
}
