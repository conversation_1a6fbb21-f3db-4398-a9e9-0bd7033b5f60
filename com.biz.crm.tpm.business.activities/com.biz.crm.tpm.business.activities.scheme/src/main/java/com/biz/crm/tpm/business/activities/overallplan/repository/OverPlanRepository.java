package com.biz.crm.tpm.business.activities.overallplan.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlan;
import com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanMapper;
import com.biz.crm.tpm.business.activities.overallplan.vo.HeadOverallSummaryReportVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OverPlanRepository extends ServiceImpl<OverallPlanMapper, OverallPlan> {


    public Page<OverallPlanVo> findPageList(Page<OverallPlanVo> page, OverallPlanVo vo) {
        vo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        vo.setTenantCode(TenantUtils.getTenantCode());
        return this.baseMapper.findList(page, vo);
    }

    public OverallPlan findById(String id) {
        return this.lambdaQuery()
                .eq(OverallPlan::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(OverallPlan::getTenantCode, TenantUtils.getTenantCode())
                .eq(OverallPlan::getId, id).one();
    }


    public List<OverallPlan> findListByIdList(List<String> idList) {
        return this.lambdaQuery()
                .in(OverallPlan::getId, idList)
                .eq(OverallPlan::getTenantCode, TenantUtils.getTenantCode())
                .eq(OverallPlan::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    public OverallPlan findBySchemeCode(String schemeCode) {
        return this.lambdaQuery()
                .eq(OverallPlan::getSchemeCode, schemeCode)
                .eq(OverallPlan::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(OverallPlan::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }

    public Page<HeadOverallSummaryReportVo> findSummaryList(Page<HeadOverallSummaryReportVo> page, HeadOverallSummaryReportVo vo) {
        return this.baseMapper.findSummaryList(page, vo, TenantUtils.getTenantCode());
    }
}
