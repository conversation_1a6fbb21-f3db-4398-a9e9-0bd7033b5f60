package com.biz.crm.tpm.business.activities.marketingplan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: haiyang
 * @Date: 2024-12-09 16:14
 * @Desc:
 */
@Data
public class CustomerMonthCategoryApplyAmountDTO {

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("总申请金额")
    private BigDecimal totalApplyAmount;

    @ApiModelProperty("年月")
    private String years;

}
