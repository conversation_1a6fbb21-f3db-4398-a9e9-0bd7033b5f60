<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanClosureMapper">


    <select id="findList" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureVo">
        select * from tpm_plan_closure
        <where>
            del_flag = '${@<EMAIL>()}'
            and tenant_code = #{vo.tenantCode}
            <if test="vo.closeCode != null and vo.closeCode != ''">
                <bind name="closeCode" value="vo.closeCode + '%'"/>
                and close_code like #{closeCode}
            </if>
            <if test="vo.closeName != null and vo.closeName != ''">
                <bind name="closeName" value="'%' + vo.closeName + '%'"/>
                and close_name like #{closeName}
            </if>
            <if test="vo.processStatus != null and vo.processStatus != ''">
                and process_status = #{vo.processStatus}
            </if>
            <if test="vo.sourceType != null and vo.sourceType != ''">
                and source_type = #{vo.sourceType}
            </if>
            order by create_time desc
        </where>
    </select>

</mapper>

