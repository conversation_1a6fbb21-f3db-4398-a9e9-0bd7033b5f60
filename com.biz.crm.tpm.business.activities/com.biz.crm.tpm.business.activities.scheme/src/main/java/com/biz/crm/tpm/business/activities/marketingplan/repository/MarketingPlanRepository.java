package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:41
 */
@Component
public class MarketingPlanRepository extends ServiceImpl<MarketingPlanMapper, MarketingPlan> {

    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    public Page<MarketingPlanVo> findList(Page<MarketingPlanVo> page, MarketingPlanVo vo) {
        page = Optional.ofNullable(page).orElse(new Page(1, 50));
        vo = Optional.ofNullable(vo).orElse(new MarketingPlanVo());
        if (StringUtils.isEmpty(vo.getTenantCode())) {
            vo.setTenantCode(TenantUtils.getTenantCode());
        }
        return baseMapper.findList(page, vo);
    }

    /**
     * 调用此方法前必须校验其中一个参数不能为空
     *
     * @param id
     * @param schemeCode
     * @return
     */
    public MarketingPlan queryByIdOrSchemeCode(String id, String schemeCode) {
        if (ObjectUtils.isEmpty(id) && ObjectUtils.isEmpty(schemeCode)){
            return null;
        }
        return this.lambdaQuery()
                .eq(MarketingPlan::getTenantCode, TenantUtils.getTenantCode())
                .eq(MarketingPlan::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(StringUtils.isNotBlank(id), MarketingPlan::getId, id)
                .eq(StringUtils.isNotBlank(schemeCode), MarketingPlan::getSchemeCode, schemeCode)
                .one();
    }

    /**
     * @param schemeCodes
     * @return
     */
    public List<MarketingPlan> queryBySchemeCodes(Set<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(MarketingPlan::getTenantCode, TenantUtils.getTenantCode())
                .eq(MarketingPlan::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(MarketingPlan::getSchemeCode, schemeCodes)
                .list();
    }

    public MarketingPlan queryById(String id) {
        return this.lambdaQuery()
                .eq(MarketingPlan::getTenantCode, TenantUtils.getTenantCode())
                .eq(MarketingPlan::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MarketingPlan::getId, id).one();
    }


    public List<MarketingPlan> findListByIdList(List<String> idList) {
        return this.lambdaQuery()
                .eq(MarketingPlan::getTenantCode, TenantUtils.getTenantCode())
                .eq(MarketingPlan::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(MarketingPlan::getId, idList).list();
    }

    public List<MarketingPlan> findMarketingPlanByOrgCodes(List<String> orgCodes, String years, List<String> schemeTypes, String confirmStatus) {
        return this.lambdaQuery()
                .in(MarketingPlan::getDepartmentCode, orgCodes)
                .eq(MarketingPlan::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MarketingPlan::getTenantCode, TenantUtils.getTenantCode())
                .eq(MarketingPlan::getYears, years)
                .isNull(MarketingPlan::getOriginalSchemeCode)
                .eq(ObjectUtils.isNotEmpty(confirmStatus), MarketingPlan::getConfirmStatus, confirmStatus)
                .in(CollectionUtils.isNotEmpty(schemeTypes), MarketingPlan::getSchemeType, schemeTypes)
                .list();
    }

    public void updateMarketingPlanPass(List<String> schemeCodes, String processStatus,String processNumber) {
        this.lambdaUpdate()
                .set(MarketingPlan::getProcessStatus, processStatus)
                .set(MarketingPlan::getSchemeStatus, processStatus)
                .set(ObjectUtils.isNotEmpty(processNumber),MarketingPlan::getProcessNumber,processNumber)
                .in(MarketingPlan::getSchemeCode, schemeCodes).update();
    }


    public List<MarketingPlan> findListByContractCodes(List<String> contractCodes) {
        return this.baseMapper.findListByContractCodes(contractCodes, TenantUtils.getTenantCode());
    }

    public MarketingPlan findOTwoOPlan(String orgCode, String years, String schemeCode) {
        return this.lambdaQuery()
                .eq(MarketingPlan::getDepartmentCode, orgCode)
                .eq(MarketingPlan::getYears, years)
                .ne(StringUtils.isNotBlank(schemeCode), MarketingPlan::getSchemeCode, schemeCode)
                .eq(MarketingPlan::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MarketingPlan::getTenantCode, TenantUtils.getTenantCode())
                .eq(MarketingPlan::getSchemeType, MarketingPlanSchemeTypeEnum.o_two_o.getCode())
                .one();
    }

    public List<MarketingPlanVo> findMarketingPlanCommit(String yearMonthLy) {
        return baseMapper.findMarketingPlanCommit(yearMonthLy);
    }


    public List<MarketingPlanCase> findListByCustomerCodesAndBelongDepartmentCodes(List<String> customerCodes, List<String> departmentCodes,String schemeCode,String years){
        return baseMapper.findListByCustomerCodesAndBelongDepartmentCodes(customerCodes,departmentCodes,schemeCode,years);
    }


    public List<MarketingPlanCase> findChangeOccupyAmount(List<String> schemeDetailCodes){
        return this.baseMapper.findChangeOccupyAmount(schemeDetailCodes);
    }

    public List<MarketingPlanCaseVo> findMiniCostTotalView(MarketingPlanCaseVo vo){
        return this.baseMapper.findMiniCostTotalView(vo);
    }


    public List<String> countTerminalNum(String years) {
        return this.baseMapper.countTerminalNum(years);
    }
}
