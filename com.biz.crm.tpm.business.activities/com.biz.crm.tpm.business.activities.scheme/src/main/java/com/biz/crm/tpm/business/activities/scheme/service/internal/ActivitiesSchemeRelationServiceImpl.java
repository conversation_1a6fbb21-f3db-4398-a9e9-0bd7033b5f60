package com.biz.crm.tpm.business.activities.scheme.service.internal;

import com.biz.crm.tpm.business.activities.scheme.repository.ActivitiesSchemeRelationRepository;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeRelation;
import com.biz.crm.tpm.business.activities.scheme.service.ActivitiesSchemeRelationService;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeRelationVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述：</br>
 *
 * <AUTHOR>
 * @date 2022/6/10
 */
@Service
public class ActivitiesSchemeRelationServiceImpl implements ActivitiesSchemeRelationService {
  @Autowired
  private ActivitiesSchemeRelationRepository activitiesSchemeRelationRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;

  @Override
  @Transactional
  public ActivitiesSchemeRelation save(ActivitiesSchemeRelationVo activitiesSchemeRelationVo) {
    this.createValidate(activitiesSchemeRelationVo);
    ActivitiesSchemeRelation activitiesSchemeRelation = this.nebulaToolkitService.copyObjectByWhiteList(activitiesSchemeRelationVo, ActivitiesSchemeRelation.class, HashSet.class, ArrayList.class);
    activitiesSchemeRelation.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesSchemeRelationRepository.save(activitiesSchemeRelation);
    return activitiesSchemeRelation;
  }

  @Override
  @Transactional
  public void saveBatch(List<ActivitiesSchemeRelationVo> activitiesSchemeRelationVos) {
    Validate.notEmpty(activitiesSchemeRelationVos, "方案活动关联信息数据为空，请检查！");
    // 1、验证数据
    activitiesSchemeRelationVos.forEach(this::createValidate);

    // 2、批量保存数据
    Collection<ActivitiesSchemeRelation> activitiesSchemeRelations = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemeRelationVos, ActivitiesSchemeRelationVo.class, ActivitiesSchemeRelation.class, HashSet.class, ArrayList.class);
    activitiesSchemeRelations.forEach(a->a.setTenantCode(TenantUtils.getTenantCode()));
    this.activitiesSchemeRelationRepository.saveBatch(activitiesSchemeRelations);
  }

  @Override
  public List<ActivitiesSchemeRelationVo> findByActivityCode(String activityCode) {
    String tenantCode = TenantUtils.getTenantCode();
    List<ActivitiesSchemeRelation> activitiesSchemeRelations = this.activitiesSchemeRelationRepository.findByActivityCodeAndTenantCode(activityCode, tenantCode);
    Collection<ActivitiesSchemeRelationVo> activitiesSchemeRelationVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemeRelations, ActivitiesSchemeRelation.class, ActivitiesSchemeRelationVo.class, LinkedHashSet.class, ArrayList.class);
    Set<String> costBudgetCodes = activitiesSchemeRelationVos.stream().map(ActivitiesSchemeRelationVo::getCostBudgetCode).collect(Collectors.toSet());
    List<CostBudgetVo> costBudgetVos = costBudgetVoService.findByCodes(costBudgetCodes);
    for (ActivitiesSchemeRelationVo relationVo : activitiesSchemeRelationVos) {
      CostBudgetVo costBudgetVo = costBudgetVos.stream().filter(e -> StringUtils.equals(e.getCode(), relationVo.getCostBudgetCode())).findFirst().orElse(null);
      if (costBudgetVo != null) {
        relationVo.setBudgetSubjectCode(costBudgetVo.getBudgetSubjectCode());
        relationVo.setBudgetSubjectName(costBudgetVo.getBudgetSubjectName());
        relationVo.setChannelCode(costBudgetVo.getChannelCode());
        relationVo.setChannelName(costBudgetVo.getChannelName());
        relationVo.setCustomerCode(costBudgetVo.getCustomerCode());
        relationVo.setCustomerName(costBudgetVo.getCustomerName());
        relationVo.setFinalBalance(costBudgetVo.getFinalBalance());
        relationVo.setMonth(costBudgetVo.getMonth());
        relationVo.setOrgCode(costBudgetVo.getOrgCode());
        relationVo.setOrgName(costBudgetVo.getOrgName());
        relationVo.setProductCode(costBudgetVo.getProductCode());
        relationVo.setProductLevelCode(costBudgetVo.getProductLevelCode());
        relationVo.setProductLevelName(costBudgetVo.getProductLevelName());
        relationVo.setProductName(costBudgetVo.getProductName());
        relationVo.setQuarter(costBudgetVo.getQuarter());
        relationVo.setTerminalCode(costBudgetVo.getTerminalCode());
        relationVo.setTerminalName(costBudgetVo.getTerminalName());
        relationVo.setYear(costBudgetVo.getYear());
        relationVo.setType(costBudgetVo.getType());
      }
    }
    return Lists.newArrayList(activitiesSchemeRelationVos);
  }

  @Override
  public List<ActivitiesSchemeRelationVo> findByActivityCodes(Set<String> activityCodes) {
    String tenantCode = TenantUtils.getTenantCode();
    List<ActivitiesSchemeRelation> activitiesSchemeRelations = this.activitiesSchemeRelationRepository.findByActivityCodesAndTenantCode(activityCodes, tenantCode);
    Collection<ActivitiesSchemeRelationVo> activitiesSchemeRelationVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemeRelations, ActivitiesSchemeRelation.class, ActivitiesSchemeRelationVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesSchemeRelationVos);
  }

  @Override
  @Transactional
  public void removeByIds(Collection<String> ids) {
    this.activitiesSchemeRelationRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  /**
   * 保存数据验证
   *
   * @param activitiesSchemeRelationVo
   */
  private void createValidate(ActivitiesSchemeRelationVo activitiesSchemeRelationVo) {
    Validate.notNull(activitiesSchemeRelationVo, "方案活动关联数据为空，请检查！");
    Validate.notBlank(activitiesSchemeRelationVo.getActivityCode(), "方案活动关联数据活动编号为空，请检查！");
    Validate.notBlank(activitiesSchemeRelationVo.getCostBudgetCode(), "方案活动关联数据预算编号为空，请检查！");
  }
}
