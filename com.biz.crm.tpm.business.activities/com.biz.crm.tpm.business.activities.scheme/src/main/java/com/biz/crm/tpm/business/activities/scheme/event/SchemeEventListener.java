package com.biz.crm.tpm.business.activities.scheme.event;

import com.biz.crm.tpm.business.activities.scheme.vo.SchemeVo;

/**
 * 方案;(tpm_scheme)相关的事件通知
 * <AUTHOR> <PERSON>
 * @date : 2022-5-31
 */
public interface SchemeEventListener{
  
  /**
   * 当方案数据被创建时，该事件被触发
   * @param schemeVo
   */
  void onCreated(SchemeVo schemeVo);
  /**
   * 当方案数据被修改时，该事件被触发
   * @param oldSchemeVo 修改前数据
   * @param schemeVo  修改后数据
   */
  void onUpdate(SchemeVo oldSchemeVo, SchemeVo schemeVo);
  /**
   * 当方案数据被删除时（逻辑删除），该事件被触发
   * @param schemeVo
   */
  void onDeleted(SchemeVo schemeVo);
}