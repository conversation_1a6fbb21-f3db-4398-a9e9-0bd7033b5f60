package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.biz.crm.tpm.business.activities.marketingplan.dto.MarketingPlanLogEventDto;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanChangeLog;
import com.biz.crm.tpm.business.activities.marketingplan.event.MarketingPlanLogEventListener;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanChangeLogRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanChangeLogService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanChangeLogVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/5 16:57
 */
@Service
@Transactional
public class MarketingPlanChangeLogServiceImpl implements MarketingPlanChangeLogService {

    @Resource
    private MarketingPlanChangeLogRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private NebulaNetEventClient nebulaNetEventClient;

    @Override
    public void saveOrUpdateLog(MarketingPlanChangeLogVo logVo) {
        MarketingPlanChangeLog log = repository.findBySchemeCode(logVo.getSchemeCode());
        if (log == null) {
            log = nebulaToolkitService.copyObjectByWhiteList(logVo, MarketingPlanChangeLog.class, HashSet.class, ArrayList.class);
        }
        repository.saveOrUpdate(log);

        MarketingPlanLogEventDto dto = new MarketingPlanLogEventDto();
        dto.setOriginal(logVo.getOriginalVo());
        dto.setNewest(logVo.getNewestVo());
        dto.setId(log.getId());
        SerializableBiConsumer<MarketingPlanLogEventListener, MarketingPlanLogEventDto> consumer = MarketingPlanLogEventListener::onChangeLogUpdate;
        nebulaNetEventClient.publish(dto, MarketingPlanLogEventListener.class, consumer);
    }

    /**
     * 查询变更列表
     *
     * @param schemeCode
     * @return
     */
    @Override
    public List<MarketingPlanChangeLogVo> findChangeList(String schemeCode) {
        return repository.findChangeList(schemeCode);
    }
}
