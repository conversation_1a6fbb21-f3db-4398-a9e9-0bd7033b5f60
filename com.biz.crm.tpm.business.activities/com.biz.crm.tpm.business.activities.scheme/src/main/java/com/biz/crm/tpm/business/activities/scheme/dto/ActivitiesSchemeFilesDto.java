package com.biz.crm.tpm.business.activities.scheme.dto;

import com.biz.crm.business.common.sdk.dto.FileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 参数传递dto：方案活动附件;
 * <AUTHOR> Keller
 * @date : 2022-6-20
 */
@ApiModel(value = "ActivitiesSchemeFiles",description = "方案活动附件")
@Getter
@Setter
public class ActivitiesSchemeFilesDto extends FileDto implements Serializable,Cloneable{
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value = "活动编号")
  private String activitiesCode;
}