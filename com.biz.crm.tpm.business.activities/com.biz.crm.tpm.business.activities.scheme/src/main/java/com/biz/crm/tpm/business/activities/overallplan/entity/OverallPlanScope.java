package com.biz.crm.tpm.business.activities.overallplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_overall_plan_scope")
@Table(
        name = "tpm_overall_plan_scope",
        indexes = {
                @Index(name = "tpm_overall_plan_scope_index0", columnList = "scheme_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_overall_plan_scope", comment = "统筹方案范围")
@ApiModel(value = "OverallPlanScope", description = "统筹方案范围")
public class OverallPlanScope extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("客户销量体量")
    @Column(name = "customer_type", columnDefinition = "varchar(20) comment '客户销量体量'")
    private String customerType;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

    @ApiModelProperty("类型(区分客户/终端:客户:dealer,终端:terminal)")
    @Column(name = "type", columnDefinition = "varchar(10) comment '类型(区分客户/终端:客户:dealer,终端:terminal)'")
    private String type;

    @ApiModelProperty("终端编码")
    @Column(name = "terminal_code", columnDefinition = "varchar(32) comment '终端编码'")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    @Column(name = "terminal_name", columnDefinition = "varchar(64) comment '终端名称'")
    private String terminalName;

    @ApiModelProperty("终端类型")
    @Column(name = "terminal_type", columnDefinition = "varchar(20) comment '终端类型'")
    private String terminalType;
}
