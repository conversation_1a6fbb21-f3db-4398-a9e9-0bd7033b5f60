package com.biz.crm.tpm.business.activities.overallplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.WorkflowFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_overall_plan")
@Table(
        name = "tpm_overall_plan",
        indexes = {
                @Index(name = "tpm_overall_plan_index0", columnList = "tenant_code,scheme_code", unique = true),
                @Index(name = "tpm_overall_plan_index1", columnList = "scheme_type"),
                @Index(name = "tpm_overall_plan_index2", columnList = "del_flag")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_overall_plan", comment = "统筹方案")
@ApiModel(value = "OverallPlan", description = "统筹方案")
public class OverallPlan extends WorkflowFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    @Column(name = "scheme_name", columnDefinition = "varchar(128) comment '方案名称'")
    private String schemeName;

    @ApiModelProperty("方案主题")
    @Column(name = "scheme_theme", columnDefinition = "varchar(512) comment '方案主题'")
    private String schemeTheme;

    @ApiModelProperty("方案类型:数据字典(scheme_type)")
    @Column(name = "scheme_type", columnDefinition = "varchar(20) comment '方案类型'")
    private String schemeType;

    @ApiModelProperty("公司代码")
    @Column(name = "company_code",columnDefinition = "varchar(32) comment '公司代码'")
    private String companyCode;

    @ApiModelProperty("开始时间")
    @Column(name = "start_date", columnDefinition = "varchar(20) comment '开始时间'")
    private String startDate;

    @ApiModelProperty("结束时间")
    @Column(name = "end_date", columnDefinition = "varchar(20) comment '结束时间'")
    private String endDate;

    @ApiModelProperty("年月")
    @Column(name = "years",columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("方案说明")
    @Column(name = "scheme_desc", columnDefinition = "varchar(256) comment '方案说明'")
    private String schemeDesc;

    @ApiModelProperty("费用合计")
    @Column(name = "cost_total", columnDefinition = "decimal(18,4) comment '方案合计'")
    private BigDecimal costTotal;

    @ApiModelProperty("预估销售额")
    @Column(name = "estimate_sales_volume", columnDefinition = "decimal(18,4) comment '预估销售额'")
    private BigDecimal estimateSalesVolume;

    @ApiModelProperty("方案投产比")
    @Column(name = "production_ratio", columnDefinition = "decimal(10,4) comment '方案投产比'")
    private BigDecimal productionRatio;

    @ApiModelProperty("创建人组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '创建人组织编码'")
    private String orgCode;

    @ApiModelProperty("创建人组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(128) comment '创建人组织名称'")
    private String orgName;

    @ApiModelProperty("创建人职位编码")
    @Column(name = "position_code", columnDefinition = "varchar(32) comment '创建人职位编码'")
    private String positionCode;

    @ApiModelProperty("创建人职位名称")
    @Column(name = "position_name", columnDefinition = "varchar(128) comment '创建人职位名称'")
    private String positionName;

    @ApiModelProperty("审批单号")
    @Column(name = "process_number", columnDefinition = "varchar(64) COMMENT '审批单号'")
    private String processNumber;

    @ApiModelProperty("推送日期")
    @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    @Column(name = "oa_id", columnDefinition = "varchar(32) COMMENT 'OA人员id'")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    @Column(name = "oa_user_name", columnDefinition = "varchar(32) COMMENT 'OA人员账号'")
    private String oaUserName;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("回传TPM接口唯一标识（接口用）")
    private String businessCode;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("url")
    private String tpmUrl;

    @ApiModelProperty("所属部门")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanDepartment> departmentList;

    @ApiModelProperty("方案明细")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanCase> caseList;

    @ApiModelProperty("费用部门")
    @Transient
    @TableField(exist = false)
    private String departmentNameStr;
}
