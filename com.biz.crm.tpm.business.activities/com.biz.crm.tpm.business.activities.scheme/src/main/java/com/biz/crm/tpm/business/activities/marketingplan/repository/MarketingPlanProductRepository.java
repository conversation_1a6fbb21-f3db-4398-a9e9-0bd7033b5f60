package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanProductMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 18:51
 */
@Component
public class MarketingPlanProductRepository extends ServiceImpl<MarketingPlanProductMapper, MarketingPlanProduct> {


    @Resource
    private NebulaToolkitService nebulaToolkitService;

    public List<MarketingPlanProduct> findListBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(MarketingPlanProduct::getSchemeCode, schemeCodes)
                .list();
    }

    public List<MarketingPlanProduct> findListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(MarketingPlanProduct::getSchemeDetailCode, schemeDetailCodes)
                .list();
    }

    public void deleteBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return;
        }
        this.remove(Wrappers.lambdaQuery(MarketingPlanProduct.class)
                .in(MarketingPlanProduct::getSchemeCode, schemeCodes));
    }


    public void deleteBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return;
        }
        this.remove(Wrappers.lambdaQuery(MarketingPlanProduct.class)
                .in(MarketingPlanProduct::getSchemeDetailCode, schemeDetailCodes));
    }

    public Map<String, List<MarketingPlanProductVo>> findProductListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return Maps.newHashMap();
        }
        List<MarketingPlanProduct> list = this.lambdaQuery()
                .in(MarketingPlanProduct::getSchemeDetailCode, schemeDetailCodes)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        List<MarketingPlanProductVo> dataList = (List<MarketingPlanProductVo>) nebulaToolkitService.copyCollectionByWhiteList(list, MarketingPlanProduct.class,
                MarketingPlanProductVo.class, HashSet.class, ArrayList.class);
        return dataList.stream().collect(Collectors.groupingBy(MarketingPlanProductVo::getSchemeDetailCode));
    }
}
