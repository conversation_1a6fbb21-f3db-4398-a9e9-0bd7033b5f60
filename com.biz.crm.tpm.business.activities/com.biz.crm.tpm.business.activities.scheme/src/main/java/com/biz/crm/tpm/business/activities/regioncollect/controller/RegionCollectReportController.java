package com.biz.crm.tpm.business.activities.regioncollect.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLosses;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectItemEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectScheme;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/16 14:17
 */
@Api("大区汇总报表分页")
@RequestMapping("/v1/regionCollectReportController")
@RestController
public class RegionCollectReportController {


    @Autowired
    private RegionCollectReportService regionCollectReportService;

    @GetMapping("findDepartmentReport")
    @ApiOperation(value = "三级部门")
    public Result<Page<RegionCollectDepartmentEstimation>> findDepartmentReport(@PageableDefault(50) Pageable pageable, RegionCollectDepartmentEstimation vo) {
        return Result.ok(regionCollectReportService.findDepartmentReport(pageable, vo));
    }

    @GetMapping("findGainsAndLossesReport")
    @ApiOperation(value = "客户损益")
    public Result<Page<RegionCollectGainsAndLosses>> findGainsAndLossesReport(@PageableDefault(50) Pageable pageable, RegionCollectGainsAndLosses vo) {
        return Result.ok(regionCollectReportService.findGainsAndLossesReport(pageable, vo));
    }

    @GetMapping("findItemReport")
    @ApiOperation(value = "品项分析")
    public Result<Page<RegionCollectItemEstimation>> findItemReport(@PageableDefault(50) Pageable pageable, RegionCollectItemEstimation vo) {
        return Result.ok(regionCollectReportService.findItemReport(pageable, vo));
    }


    @GetMapping("findDepartmentReportTotal")
    @ApiOperation(value = "三级部门-金额合计")
    public Result<Map<String, BigDecimal>> findDepartmentReportTotal(RegionCollectDepartmentEstimation vo) {
        return Result.ok(regionCollectReportService.findDepartmentReportTotal(vo));
    }

    @GetMapping("findGainsAndLossesReportTotal")
    @ApiOperation(value = "客户损益-金额合计")
    public Result<Map<String, BigDecimal>> findGainsAndLossesReportTotal(RegionCollectGainsAndLosses vo) {
        return Result.ok(regionCollectReportService.findGainsAndLossesReportTotal(vo));
    }

    @GetMapping("findItemReportTotal")
    @ApiOperation(value = "品项分析-金额合计")
    public Result<Map<String, BigDecimal>> findItemReportTotal(RegionCollectItemEstimation vo) {
        return Result.ok(regionCollectReportService.findItemReportTotal(vo));
    }

    @GetMapping("findSchemeReport")
    @ApiOperation(value = "人员提报明细")
    public Result<Page<RegionCollectScheme>> findSchemeReport(@PageableDefault(50) Pageable pageable, RegionCollectScheme vo) {
        return Result.ok(regionCollectReportService.findSchemeReport(pageable, vo));
    }


    @ApiOperation(value = "大区下方案明细分页")
    @GetMapping("findRegionMarketingCaseReport")
    public Result<Page<MarketingPlanCaseVo>> findRegionMarketingCaseReport(@PageableDefault(50) Pageable pageable, MarketingPlanCaseVo caseVo, String collectCode) {
        return Result.ok(regionCollectReportService.findRegionMarketingCaseReport(pageable, caseVo, collectCode));
    }

    @ApiOperation(value = "大区下销售计划列表")
    @GetMapping("findRegionMarketingSalesPlanList")
    public Result<Page<MarketingSalesPlanVo>> findRegionMarketingSalesPlanList(@PageableDefault(50) Pageable pageable, String collectCode) {
        return Result.ok(regionCollectReportService.findRegionMarketingSalesPlanList(pageable, collectCode));
    }
}
