package com.biz.crm.tpm.business.activities.scheme.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.scheme.entity.SchemeRange;
import com.biz.crm.tpm.business.activities.scheme.mapper.SchemeRangeMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 方案范围;(tpm_scheme_range)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Component
public class SchemeRangeRepository extends ServiceImpl<SchemeRangeMapper, SchemeRange> {
  @Autowired
  private SchemeRangeMapper schemeRangeMapper;

  /**
   * 批量根据id禁用
   * @param enable
   * @param ids
   */
  public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
    UpdateWrapper<SchemeRange> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("enable_status", enable.getCode());
    updateWrapper.in("id", ids);
    updateWrapper.eq("tenant_code",TenantUtils.getTenantCode());
    this.update(updateWrapper);
  }

  /**
   * 批量根据id禁用
   *
   * @param enable
   * @param codes
   */
  public void updateEnableStatusByCodes(EnableStatusEnum enable, Set<String> codes) {
    UpdateWrapper<SchemeRange> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("enable_status", enable.getCode());
    updateWrapper.in("range_code", codes);
    updateWrapper.eq("tenant_code",TenantUtils.getTenantCode());
    this.update(updateWrapper);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<SchemeRange>
   */
  public List<SchemeRange> findByIds(Collection<String> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(SchemeRange::getId, ids)
            .eq(SchemeRange::getTenantCode, tenantCode)
            .eq(SchemeRange::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .list();
  }

  /**
   * 根据编号与租户编号获取对象
   *
   * @param codes
   * @return
   */
  public List<SchemeRange> findByCodes(Set<String> codes) {
    if (org.springframework.util.CollectionUtils.isEmpty(codes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(SchemeRange::getRangeCode, codes)
            .eq(SchemeRange::getTenantCode, tenantCode).list();
  }

  /**
   * 根据方案编号获取详情集合
   *
   * @param schemeCode 方案编号
   * @return List<SchemeRange>
   */
  public List<SchemeRange> findBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(SchemeRange::getSchemeCode, schemeCode)
            .eq(SchemeRange::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据方案编号删除附件数据
   *
   * @param schemeCode
   */
  public void deleteBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    this.lambdaUpdate().eq(SchemeRange::getSchemeCode, schemeCode).eq(SchemeRange::getTenantCode, tenantCode).remove();
  }

  public List<SchemeRange> findAll() {
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(SchemeRange::getTenantCode, tenantCode)
            .list();
  }

  public void deleteByRangeCodes(Collection<String> codes) {
    String tenantCode = TenantUtils.getTenantCode();
    this.lambdaUpdate()
            .eq(SchemeRange::getTenantCode, tenantCode)
            .in(SchemeRange::getRangeCode, codes)
            .set(SchemeRange::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
            .update();
  }

  public SchemeRange findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(SchemeRange::getTenantCode,tenantCode)
        .in(SchemeRange::getId,id)
        .one();
  }
}