package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.*;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:44
 */
public interface MarketingPlanService extends BusinessPageCacheService<MarketingPlanCaseVo, MarketingPlanCaseVo> {

    /**
     * 分页查询
     *
     * @param pageable
     * @param vo
     * @return
     */
    Page<MarketingPlanVo> findList(Pageable pageable, MarketingPlanVo vo);

    MarketingPlanVo queryDetails(String id, String schemeCode);

    MarketingPlanVo createPlan(MarketingPlanVo vo, Boolean checkFlag, Boolean changeFlag);

    MarketingPlanVo updatePlan(MarketingPlanVo vo, Boolean checkFlag, Boolean changeFlag);

    MarketingPlanVo submitCreate(MarketingPlanVo vo, Boolean changeFlag);

    MarketingPlanVo submitUpdate(MarketingPlanVo vo, Boolean changeFlag);

    Map<String, String> checkControlBudget(MarketingPlanVo vo);

    Map<String, String> checkChangeControlBudget(MarketingPlanVo vo);

    void matchControlBudget(String schemeCode);

    MarketingPlanVo submitOTwoOCreate(MarketingPlanVo vo, Boolean changeFlag);

    MarketingPlanVo submitOTwoOupdate(MarketingPlanVo vo, Boolean changeFlag);

    Map<String, String> createMarketingContract(MarketingPlanVo vo, List<String> contractCodes);

    String deleteBatch(List<String> idList);

    String confirmSure(List<String> idList);

    String cancelConfirm(List<String> idList);

    void callback(MarketingPlanVo vo);

    List<MarketPlanChangeGainsAndLossesVo> findChangeGainsAndLosses(String schemeCode);

    void changeCalCustomerGains(String schemeCode);

    List<MarketingPlanGainsAndLossesVo> calCustomerGains(String schemeCode);

    List<MarketingPlanSchemeEstimationVo> getMarketingPlanSchemeEstimation(String schemeCode);

    List<MarketingPlanSchemeEstimationVo> calMarketingPlanSchemeEstimation(String schemeCode);

    MarketingChangeGainsAndLossesCompareVo findChangeGainsAndLossesCompare(String schemeCode);

    List<MarketingPlanVo> findMarketingPlanByOrgCodes(List<String> orgCodes, String years, List<String> schemeTypes, String confirmStatus);

    void updateMarketingPlanPassStatus(List<String> schemeCodes, String processStatus, String processNumber);

    List<MarketingPlanEstimationVo> calMarketingPlanEstimation(String schemeCode);

    List<MarketingPlanEstimationVo> calMarketingPlanEstimationOTwoO(String schemeCode);

    List<MarketingPlanEstimationVo> calMarketingPlanEstimationAdditional(String schemeCode);

    List<MarketingPlanItemEstimationVo> calMarketingPlanItemEstimation(String schemeCode);

    void updateChangeSchemeBudget(String schemeCode);

    /**
     * 流程撤回
     *
     * @param code
     */
    void recover(String code, String remark);

    void planCaseSaveCurrentPageCache(String cacheKey, List<MarketingPlanCaseVo> saveList, String years, String schemeCode, String originalSchemeCode, List<String> loginUserCodes, String userName);

    List<MarketingPlanVo> findMarketingPlanCommit(String yearMonthLy);

    void updateCustomerCooperateType();

    List<MarketingPlanVo> findListBySchemeCodes(List<String> schemeCodes);

    void calMarketingPlanCaseList(String cacheKey, String years, String schemeCode, String originalSchemeCode);

}
