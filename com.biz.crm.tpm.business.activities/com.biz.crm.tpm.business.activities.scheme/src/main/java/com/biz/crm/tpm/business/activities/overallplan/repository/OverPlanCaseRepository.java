package com.biz.crm.tpm.business.activities.overallplan.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanCaseMapper;
import com.biz.crm.tpm.business.activities.overallplan.vo.BearOverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.HeadOverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Component
public class OverPlanCaseRepository extends ServiceImpl<OverallPlanCaseMapper, OverallPlanCase> {

    public List<OverallPlanCase> findListBySchemeCode(String schemeCode) {
        return this.lambdaQuery()
                .eq(OverallPlanCase::getSchemeCode, schemeCode)
                .list();
    }

    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.remove(Wrappers.lambdaQuery(OverallPlanCase.class)
                .in(OverallPlanCase::getSchemeCode, schemeCodes));
    }


    /**
     * 查询总部方案明细编码 (大区使用)
     *
     * @param vo
     * @return
     */
    public Page<OverallPlanCaseVo> findHeadOverallPlanCaseList(Page<HeadOverallPlanCaseVo> page, HeadOverallPlanCaseVo vo) {
        return this.baseMapper.findHeadOverallPlanCaseList(page, vo);
    }


    public Page<OverallPlanCaseVo> findHeadOverallPlanCaseToMarketing(Page<HeadOverallPlanCaseVo> page, HeadOverallPlanCaseVo vo){
        return this.baseMapper.findHeadOverallPlanCaseToMarketing(page, vo);
    }

    public List<OverallPlanCaseVo> findAlreadyUndertakeAmount(List<String> schemeDetailCodes) {
        return this.baseMapper.findAlreadyUndertakeAmount(schemeDetailCodes);
    }

    /**
     * 查询方案明细列表通过方案明细编码
     *
     * @param schemeDetailCodes
     * @return
     */
    public List<OverallPlanCase> findCaseListBySchemeDetailCodes(List<String> schemeDetailCodes, String processStatus) {
        return this.baseMapper.findCaseListBySchemeDetailCodes(schemeDetailCodes, TenantUtils.getTenantCode(), processStatus);
    }

    /**
     * 查询总部关联方案明细（大区使用）
     *
     * @param schemeDetailCodes
     * @return
     */
    public List<BearOverallPlanCaseVo> findReleaseHeadCaseList(List<String> schemeDetailCodes) {
        return this.baseMapper.findReleaseHeadCaseList(schemeDetailCodes);
    }


    /**
     * 查询总部方案明细(总部，大区)
     *
     * @param schemeDetailCodes
     * @return
     */
    public List<BearOverallPlanCaseVo> findReleaseCaseByMarketingPlan(List<String> schemeDetailCodes) {
        return this.baseMapper.findReleaseCaseByMarketingPlan(schemeDetailCodes);
    }

    /**
     * 查询已经承接的方案明细
     *
     * @param schemeDetailCodeList
     * @return
     */
    public List<OverallPlanCase> findAlreadyBearCase(List<String> schemeDetailCodeList) {
        return this.baseMapper.findAlreadyBearCase(schemeDetailCodeList);
    }


    public List<OverallPlanCase> findListByReleaseDetailNotNull(String schemeCode) {
        return this.lambdaQuery()
                .eq(OverallPlanCase::getSchemeCode, schemeCode)
                .isNotNull(OverallPlanCase::getHeadSchemeDetailCode)
                .list();
    }

    public Page<OverallPlanCaseVo> findListBySchemeType(Page<OverallPlanCaseVo> page, OverallPlanCaseVo vo, String schemeType) {
        return this.baseMapper.findListBySchemeType(page, vo, schemeType, TenantUtils.getTenantCode());
    }

    public List<OverallPlanCaseVo> findHeadSchemeListBySchemeType(OverallPlanCaseVo vo) {
        return this.baseMapper.findHeadSchemeListBySchemeType(vo, OverallPlanSchemeTypeEnum.HEAD.getCode(),TenantUtils.getTenantCode());
    }

    public Page<OverallPlanCaseVo> findNotCaseBySchemeTypeList(Page<OverallPlanCaseVo> page, OverallPlanCaseVo vo, String schemeType) {
        return this.baseMapper.findNotCaseBySchemeTypeList(page, vo, schemeType, TenantUtils.getTenantCode());
    }

    public Set<String> findAlreadyUnderTakeCaseList(String schemeType) {
        return this.baseMapper.findAlreadyUnderTakeCaseList(schemeType);
    }

    public void updateCaseStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String delFlag) {
        this.lambdaUpdate()
                .in(OverallPlanCase::getSchemeDetailCode, schemeDetailCodes)
                .set(OverallPlanCase::getDelFlag, delFlag)
                .update();
    }

    public Page<OverallPlanCaseVo> findRegionOverallPlanCase(Page<OverallPlanCaseVo> page, String headSchemeDetailCode) {
        return this.baseMapper.findRegionOverallPlanCase(page, headSchemeDetailCode, TenantUtils.getTenantCode());
    }

    public List<OverallPlanCase> findPartFiledListBySchemeCodes(List<String> schemeCodes) {
        return this.lambdaQuery()
                .in(OverallPlanCase::getSchemeCode, schemeCodes)
                .select(OverallPlanCase::getSchemeCode, OverallPlanCase::getSchemeDetailCode, OverallPlanCase::getDelFlag,
                        OverallPlanCase::getBearFlag, OverallPlanCase::getApplyAmount)
                .list();
    }

    public Page<OverallPlanCaseVo> findBearPlanCaseList(Page<OverallPlanCaseVo> page, OverallPlanCaseVo vo, Set<String> orgCodes) {
        return this.baseMapper.findBearPlanCaseList(page, vo, orgCodes, TenantUtils.getTenantCode());
    }


    public List<OverallPlanCase> findCaseListBySchemeCode(String schemeCode) {
        return this.lambdaQuery()
                .eq(OverallPlanCase::getSchemeCode, schemeCode)
                .list();
    }

    public List<OverallPlanCaseVo> checkRegionOverall(Set<String> orgCodeSet, String schemeType, String years, String detailFlag) {
        return this.baseMapper.checkRegionOverall(orgCodeSet, schemeType, TenantUtils.getTenantCode(), years, detailFlag);
    }


    public List<OverallPlanCase> findHeadPlanCaseListByOrgCodes(List<String> orgCodes, String years, String bearFlag) {
        return this.baseMapper.findHeadPlanCaseListByOrgCodes(orgCodes, years, bearFlag, TenantUtils.getTenantCode());
    }

    public List<OverallPlanCase> findUnderTakeCaseList(List<String> headSchemeDetailCodes,String excludeSchemeCode){
        return this.baseMapper.findUnderTakeCaseList(headSchemeDetailCodes,excludeSchemeCode,TenantUtils.getTenantCode());
    }

    public List<OverallPlanCase> findListBySchemeCodeAndSchemeDetailCodePair(List<OverallPlanCase> caseQueryList) {
        if (CollectionUtils.isEmpty(caseQueryList)) return Collections.emptyList();
        return this.baseMapper.findListBySchemeCodeAndSchemeDetailCodePair(caseQueryList);
    }


    public List<MarketingPlanCaseVo> findRegionUnderTakeHeadScheme(List<String> releaseDetailCodes,List<String> notCodes){
        return this.baseMapper.findRegionUnderTakeHeadScheme(releaseDetailCodes,notCodes);
    }


    public Page<OverallPlanCaseVo> findUnderTakeSchemeDetailList(Page<OverallPlanCaseVo> page,List<String> regionUnderTakeList,List<String> marketingUnderTakeList) {
        return this.baseMapper.findUnderTakeSchemeDetailList(page,regionUnderTakeList,marketingUnderTakeList);
    }

    public List<OverallPlanCase> findCaseListByReleaseDetailCodes(List<String> releaseDetailCode, String processStatus) {
        return this.baseMapper.findCaseListByReleaseDetailCodes(releaseDetailCode, TenantUtils.getTenantCode(), processStatus);
    }
}
