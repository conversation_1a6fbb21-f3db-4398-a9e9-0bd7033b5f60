package com.biz.crm.tpm.business.activities.marketingplan.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanChangeLogService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanChangeLogVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/5 20:27
 */
@RestController
@RequestMapping("/v1/marketingPlanChangeLogController")
@Api(tags = "营销规划方案变更日志")
public class MarketingPlanChangeLogController {


    @Autowired
    private MarketingPlanChangeLogService changeLogService;

    @Autowired
    private MarketingPlanService marketingPlanService;

    @ApiOperation(value = "查询变更列表")
    @GetMapping("findChangeList")
    public Result<Page<MarketingPlanVo>> findChangeList(@PageableDefault(50) Pageable pageable, MarketingPlanVo vo) {
        Validate.notNull(vo.getOriginalSchemeCode(), "原方案编码不能为空");
        return Result.ok(marketingPlanService.findList(pageable, vo));
    }
}
