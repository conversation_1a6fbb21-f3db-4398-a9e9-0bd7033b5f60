package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectMarketingEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectMarketingEstimationBack;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectMarketingEstimationBackMapper;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectMarketingEstimationMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class RegionCollectMarketingEstimationBackRepository extends ServiceImpl<RegionCollectMarketingEstimationBackMapper, RegionCollectMarketingEstimationBack> {



    public void deleteByCollectCodes(List<String> collectCodes) {
        this.lambdaUpdate()
                .in(RegionCollectMarketingEstimationBack::getCollectCode, collectCodes).remove();
    }
}
