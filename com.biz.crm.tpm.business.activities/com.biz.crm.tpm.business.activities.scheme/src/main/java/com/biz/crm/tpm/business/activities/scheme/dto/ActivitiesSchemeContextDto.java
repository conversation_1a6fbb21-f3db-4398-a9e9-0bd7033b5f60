package com.biz.crm.tpm.business.activities.scheme.dto;

import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "ActivitiesSchemeContextDto", description = "专用于方案活动与动态表单间的上下文信息传参")
public class ActivitiesSchemeContextDto {
  @ApiModelProperty("最新活动信息")
  private ActivitiesSchemeDto targetActivity;

  @ApiModelProperty("历史活动信息")
  private ActivitiesSchemeVo sourceActivity;
}
