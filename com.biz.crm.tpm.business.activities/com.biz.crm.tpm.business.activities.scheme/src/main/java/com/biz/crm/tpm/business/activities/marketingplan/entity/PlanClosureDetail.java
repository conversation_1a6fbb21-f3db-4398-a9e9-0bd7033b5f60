package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Component;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_plan_closure_detail")
@Table(
        name = "tpm_plan_closure_detail",
        indexes = {
                @Index(name = "tpm_plan_closure_detail_index0", columnList = "close_code,close_detail_code", unique = true)
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_plan_closure_detail", comment = "方案规划关闭明细")
@ApiModel(value = "PlanClosureDetail", description = "方案规划关闭明细")
public class PlanClosureDetail extends UuidFlagOpEntity {

    @ApiModelProperty("关闭编码")
    @Column(name = "close_code", columnDefinition = "varchar(32) comment '关闭编码'")
    private String closeCode;

    @ApiModelProperty("关闭明细编码")
    @Column(name = "close_detail_code", columnDefinition = "varchar(32) comment '关闭明细编码'")
    private String closeDetailCode;

    @ApiModelProperty("关联方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '关联方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("json字符串")
    @Column(name = "json_str", columnDefinition = "text comment 'JSON字符串'")
    private String jsonStr;
}
