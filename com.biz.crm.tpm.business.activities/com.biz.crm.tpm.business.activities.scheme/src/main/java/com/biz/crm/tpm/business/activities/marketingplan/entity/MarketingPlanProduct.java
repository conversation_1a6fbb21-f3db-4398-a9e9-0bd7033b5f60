package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 17:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_plan_product")
@Table(
        name = "tpm_marketing_plan_product",
        indexes = {
                @Index(name = "tpm_marketing_plan_product_index0", columnList = "scheme_code"),
                @Index(name = "tpm_marketing_plan_product_index1", columnList = "scheme_detail_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_plan_product", comment = "营销方案规划-方案明细产品范围")
@ApiModel(value = "MarketingPlanProduct", description = "营销方案规划-方案明细产品范围")
public class MarketingPlanProduct extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("编码")
    @Column(name = "code", columnDefinition = "varchar(32) comment '编码'")
    private String code;

    @ApiModelProperty("名称")
    @Column(name = "name", columnDefinition = "varchar(64) comment '名称'")
    private String name;

    @ApiModelProperty("物料编码")
    @Column(name = "material_code", columnDefinition = "varchar(32) comment '物料编码'")
    private String materialCode;

    @ApiModelProperty("数量")
    @Column(name = "num", columnDefinition = "decimal(10) comment '数量'")
    private BigDecimal num;

    @ApiModelProperty("类型")
    @Column(name = "type", columnDefinition = "varchar(20) comment '类型'")
    private String type;
}
