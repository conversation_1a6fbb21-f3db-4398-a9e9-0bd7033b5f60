package com.biz.crm.tpm.business.activities.giftrebatereport.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.giftrebatereport.entity.GiftAndRebateOccupyAmountReport;
import com.biz.crm.tpm.business.activities.giftrebatereport.mapper.GiftAndRebateOccupyAmountReportMapper;
import com.biz.crm.tpm.business.activities.giftrebatereport.dto.GiftAndRebateOccupyAmountReportDto;
import com.biz.crm.tpm.business.activities.giftrebatereport.vo.GiftAndRebateOccupyAmountReportVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 搭赠及返利实时金额占用报表Repository
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Component
public class GiftAndRebateOccupyAmountReportRepository extends ServiceImpl<GiftAndRebateOccupyAmountReportMapper, GiftAndRebateOccupyAmountReport> {

    @Autowired
    private GiftAndRebateOccupyAmountReportMapper giftAndRebateOccupyAmountReportMapper;

    /**
     * 分页查询搭赠及返利实时金额占用报表
     *
     * @param pageable 分页对象
     * @param dto      查询条件
     * @return 分页结果
     */
    public Page<GiftAndRebateOccupyAmountReportVo> findByConditions(Pageable pageable, GiftAndRebateOccupyAmountReportDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        Page<GiftAndRebateOccupyAmountReportVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return giftAndRebateOccupyAmountReportMapper.findByConditions(page, dto);
    }

    /**
     * 根据条件查询报表数据列表
     *
     * @param dto 查询条件
     * @return 报表数据列表
     */
    public List<GiftAndRebateOccupyAmountReportVo> findListByConditions(GiftAndRebateOccupyAmountReportDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        return giftAndRebateOccupyAmountReportMapper.findListByConditions(dto);
    }

    /**
     * 根据方案编码查询报表数据
     *
     * @param schemeCode 方案编码
     * @return 报表数据列表
     */
    public List<GiftAndRebateOccupyAmountReport> findBySchemeCode(String schemeCode) {
        return this.lambdaQuery()
                .eq(GiftAndRebateOccupyAmountReport::getSchemeCode, schemeCode)
                .eq(GiftAndRebateOccupyAmountReport::getTenantCode, TenantUtils.getTenantCode())
                .eq(GiftAndRebateOccupyAmountReport::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据方案明细编码查询报表数据
     *
     * @param schemeDetailCode 方案明细编码
     * @return 报表数据列表
     */
    public List<GiftAndRebateOccupyAmountReport> findBySchemeDetailCode(String schemeDetailCode) {
        return this.lambdaQuery()
                .eq(GiftAndRebateOccupyAmountReport::getSchemeDetailCode, schemeDetailCode)
                .eq(GiftAndRebateOccupyAmountReport::getTenantCode, TenantUtils.getTenantCode())
                .eq(GiftAndRebateOccupyAmountReport::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据客户编码查询报表数据
     *
     * @param customerCode 客户编码
     * @return 报表数据列表
     */
    public List<GiftAndRebateOccupyAmountReport> findByCustomerCode(String customerCode) {
        return this.lambdaQuery()
                .eq(GiftAndRebateOccupyAmountReport::getCustomerCode, customerCode)
                .eq(GiftAndRebateOccupyAmountReport::getTenantCode, TenantUtils.getTenantCode())
                .eq(GiftAndRebateOccupyAmountReport::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据使用部门编码查询报表数据
     *
     * @param belongDepartmentCode 使用部门编码
     * @return 报表数据列表
     */
    public List<GiftAndRebateOccupyAmountReport> findByBelongDepartmentCode(String belongDepartmentCode) {
        return this.lambdaQuery()
                .eq(GiftAndRebateOccupyAmountReport::getBelongDepartmentCode, belongDepartmentCode)
                .eq(GiftAndRebateOccupyAmountReport::getTenantCode, TenantUtils.getTenantCode())
                .eq(GiftAndRebateOccupyAmountReport::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据ID查询报表数据
     *
     * @param id 主键ID
     * @return 报表数据
     */
    public GiftAndRebateOccupyAmountReport findByIdAndTenantCode(String id) {
        return this.lambdaQuery()
                .eq(GiftAndRebateOccupyAmountReport::getId, id)
                .eq(GiftAndRebateOccupyAmountReport::getTenantCode, TenantUtils.getTenantCode())
                .eq(GiftAndRebateOccupyAmountReport::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .one();
    }

    /**
     * 统计报表数据总数
     *
     * @param dto 查询条件
     * @return 总数
     */
    public Long countByConditions(GiftAndRebateOccupyAmountReportDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        return giftAndRebateOccupyAmountReportMapper.countByConditions(dto);
    }

    /**
     * 查询本月审批通过的且当天与活动结束时间大于1天的营销方案明细
     *
     * @param monthStart 月初时间
     * @param monthEnd 月末时间
     * @return 符合条件的营销方案明细列表
     */
    public List<com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase> queryApprovedMarketingPlanCases(
            String monthStart, String monthEnd) {
        return giftAndRebateOccupyAmountReportMapper.queryApprovedMarketingPlanCases(
                TenantUtils.getTenantCode(),
                DelFlagStatusEnum.NORMAL.getCode(),
                monthStart,
                monthEnd
        );
    }

    public void deleteByYearMonth(String currentYearMonth) {
        this.lambdaUpdate()
                .likeRight(GiftAndRebateOccupyAmountReport::getStartDate, currentYearMonth)
                .remove();
    }
}
