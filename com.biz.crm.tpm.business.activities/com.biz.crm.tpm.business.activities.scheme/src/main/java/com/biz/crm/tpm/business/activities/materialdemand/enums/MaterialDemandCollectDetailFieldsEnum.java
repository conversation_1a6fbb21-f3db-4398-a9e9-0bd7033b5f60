package com.biz.crm.tpm.business.activities.materialdemand.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MaterialDemandCollectDetailFieldsEnum {
    materialCode("materialCode"),
    materialName("materialName"),
    materialNum("materialNum"),
    materialAmount("materialAmount"),
    ;

    private String dictCode;

    public static MaterialDemandCollectDetailFieldsEnum findByCode(String code) {
        Optional<MaterialDemandCollectDetailFieldsEnum> first = Stream.of(MaterialDemandCollectDetailFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}