package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectGainsAndLossesVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
public interface RegionCollectGainsAndLossesBackService {

    void deleteByCollectCodes(List<String> collectCodes);

    void saveList(List<RegionCollectGainsAndLossesVo> list,String collectCode);
}
