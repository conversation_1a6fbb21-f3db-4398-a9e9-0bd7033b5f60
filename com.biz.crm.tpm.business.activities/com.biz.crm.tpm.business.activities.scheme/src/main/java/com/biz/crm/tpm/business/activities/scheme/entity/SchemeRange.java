package com.biz.crm.tpm.business.activities.scheme.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.TenantEntity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;

import static org.apache.ibatis.type.JdbcType.VARCHAR;

/**
 * 实体：方案范围;
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "SchemeRange",description = "方案范围")
@TableName("tpm_scheme_range")
@Getter
@Setter
@Entity(name = "tpm_scheme_range")
@org.hibernate.annotations.Table(appliesTo = "tpm_scheme_range", comment = "方案范围")
public class SchemeRange  extends TenantEntity{

  /** 方案编号 */
  @ApiModelProperty(name = "方案编号",notes = "方案编号")
  @Column(name = "scheme_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '方案编号 '")
  private String schemeCode;

  /** 范围类型(1,组织,2,组织类型) */
  @ApiModelProperty(name = "范围类型(1,组织,2,组织类型)",notes = "范围类型(1,组织,2,组织类型)")
  @Column(name = "range_type", nullable = false,  columnDefinition = "INT COMMENT '范围类型(1,组织,2,组织类型) '")
  private Integer rangeType;

  /** 范围编号 */
  @ApiModelProperty(name = "范围编号",notes = "范围编号")
  @Column(name = "range_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '范围编号 '")
  private String rangeCode;

  /** 范围名称 */
  @ApiModelProperty(name = "范围名称",notes = "范围名称")
  @Column(name = "range_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '范围名称 '")
  private String rangeName;

  /**
   * 数据业务状态（启用状态）
   */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY, jdbcType = VARCHAR)
  @Column(name = "enable_status", nullable = true, length = 10, columnDefinition = "varchar(10) COMMENT '数据业务状态（启用状态）'")
  private String enableStatus;

  /**
   * 数据状态（删除状态）
   */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY, jdbcType = VARCHAR)
  @Column(name = "del_flag", nullable = true, length = 10, columnDefinition = "varchar(10) COMMENT '数据状态（删除状态）'")
  private String delFlag;

}