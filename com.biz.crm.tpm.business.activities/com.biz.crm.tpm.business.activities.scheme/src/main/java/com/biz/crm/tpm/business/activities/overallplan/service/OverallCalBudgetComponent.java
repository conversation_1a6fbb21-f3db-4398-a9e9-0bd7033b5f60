package com.biz.crm.tpm.business.activities.overallplan.service;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanBudget;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanProductVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlVoService;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import com.biz.crm.tpm.business.track.sdk.service.BudgetTrackService;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Description 不允许注入
 * <AUTHOR>
 * @Date 2024/8/8 16:21
 */
@Component
public class OverallCalBudgetComponent {

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private BudgetTrackService budgetTrackService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private BudgetControlVoService budgetControlVoService;

    @Resource
    private BudgetSubjectsVoService budgetSubjectsVoService;

    @Resource
    private OverallPlanBudgetService overallPlanBudgetService;


    /**
     * 计算总部预算
     *
     * @param code                管控规则编码
     * @param controlBudgetOrgMap
     * @return
     */
    @Async("tpmOverallBudgetThread")
    public Future<List<BudgetTrackVo>> syncCalOverallBudget(String code, Map<String, Map<String, List<OrgVo>>> controlBudgetOrgMap,
                                                            FacturerUserDetails loginDetails) {
        loginUserService.refreshAuthentication(loginDetails);
        List<String> codes = Lists.newArrayList(code);
        budgetTrackService.generateBudgetTrackByCodes(codes);
        List<BudgetTrackVo> budgetTrackVos = budgetTrackService.findByBudgetControlCodes(codes);
        List<String> orgCodes = budgetTrackVos.stream().map(BudgetTrackVo::getDepartmentOneCode).collect(Collectors.toList());
        Set<String> orgCodeSet = Sets.newHashSet();
        for (String orgCode : orgCodes) {
            orgCodeSet.addAll(Arrays.asList(orgCode.split(",")));
        }
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllChildrenByOrgCodesMap(Lists.newArrayList(orgCodeSet));
        controlBudgetOrgMap.put(code, orgMap);
        return new AsyncResult<>(budgetTrackVos);
    }


    /**
     * 一个方案一个方案的匹配管控规则
     *
     * @param budgetTrackVos
     * @param caseVo
     * @param controlBudgetOrgMap
     * @param loginDetails
     * @return
     */
    @Async("tpmOverallBudgetThread")
    public Future<List<OverallPlanBudget>> syncMatchBudgetByOverallPlan(List<BudgetTrackVo> budgetTrackVos, OverallPlanCaseVo caseVo,
                                                                  Map<String, Map<String, List<OrgVo>>> controlBudgetOrgMap, FacturerUserDetails loginDetails) {
        loginUserService.refreshAuthentication(loginDetails);
        //查询管控规则
        List<String> controlCodes = budgetTrackVos.stream().map(BudgetTrackVo::getBudgetControlCode).collect(Collectors.toList());
        List<BudgetControlVo> controlVos = budgetControlVoService.findBudgetControlByCodes(controlCodes);
        Map<String, String> budgetControlMap = controlVos.stream().collect(Collectors.toMap(x -> x.getControlCode(), x -> x.getControlForm()));
        Map<String, List<OrgVo>> orgMap = Maps.newHashMap();
        for (String controlCode : controlCodes) {
            orgMap.putAll(controlBudgetOrgMap.get(controlCode));
        }
        String budgetSubjectCode = null;
        if (ObjectUtils.isNotEmpty(caseVo.getDetailCode())) {
            budgetSubjectCode = budgetSubjectsVoService.findBudgetSubjectByDetailCode(caseVo.getDetailCode());
        } else {
            budgetSubjectCode = caseVo.getSecondCostCategory();
        }
        Set<String> budgetSubjectCodeSet = budgetSubjectsVoService.findAllParentSubjectCodesByCodes(budgetSubjectCode);
        String productCode = null;
        String itemCode = null;
        String customerCode = null;
        if (!CollectionUtils.isEmpty(caseVo.getProductList()) && caseVo.getProductList().size() == 1) {
            productCode = caseVo.getProductList().get(0).getCode();
        }
        if (!CollectionUtils.isEmpty(caseVo.getItemList()) && caseVo.getItemList().size() == 1) {
            itemCode = caseVo.getItemList().get(0).getCode();
        }
        if (!CollectionUtils.isEmpty(caseVo.getCustomerList()) && caseVo.getCustomerList().size() == 1) {
            customerCode = caseVo.getCustomerList().get(0).getCustomerCode();
        }

        List<BudgetTrackVo> filterList = Lists.newArrayList();
        for (BudgetTrackVo x : budgetTrackVos) {
            List<String> orgCodes = Arrays.asList(x.getDepartmentOneCode().split(","));
            List<OrgVo> orgVoList = Lists.newArrayList();
            for (String s : orgCodes) {
                if (orgMap.containsKey(s)) {
                    orgVoList.addAll(orgMap.get(s));
                }
            }
            List<String> commonElements = Lists.newArrayList();
            //找出重复的数据
            if (ObjectUtils.isNotEmpty(x.getBudgetSubjectCode())) {
                List<String> budgetSubjectCodeList = Arrays.asList(x.getBudgetSubjectCode().split(","));
                for (String s : budgetSubjectCodeList) {
                    if (budgetSubjectCodeSet.contains(s)) {
                        commonElements.add(s);
                    }
                }
            }

            Set<String> orgCodeSet = orgVoList.stream().map(l -> l.getOrgCode()).collect(Collectors.toSet());
            if (orgCodeSet.contains(caseVo.getBearDepartmentCode()) && caseVo.getYears().equals(x.getYearMonthLy()) &&
                    //判断科目 可以为空 但是如果有值 则必须匹配上
                    (ObjectUtils.isEmpty(x.getBudgetSubjectCode()) || (commonElements.size() > 0))
                    //判断客户 可以为空 如果有值 则必须匹配上
                    && (ObjectUtils.isEmpty(x.getCustomerCode()) || customerCode.equals(x.getCustomerCode())) &&
                    //判断产品 可以为空 如果有值 则必须匹配上
                    (ObjectUtils.isEmpty(x.getProductCode()) || (ObjectUtils.isNotEmpty(productCode) && productCode.equals(x.getProductCode()))) &&
                    //判断品项 可以为空 如果有值 则必须匹配上
                    (ObjectUtils.isEmpty(x.getItemCode()) || (ObjectUtils.isNotEmpty(itemCode) && itemCode.equals(x.getItemCode())))) {
                filterList.add(x);
            }
        }

        Validate.isTrue(!CollectionUtils.isEmpty(filterList), String.format("明细编码%s年月+部门+科目未找到对应的预算管控规则", caseVo.getSchemeDetailCode()));
        List<String> orgCodes = filterList.stream().filter(x->ObjectUtils.isNotEmpty(x.getDepartmentOneCode()))
                .map(x->x.getDepartmentOneCode()).collect(Collectors.toList());
        List<String> budgetSubjectCodes = filterList.stream().filter(x->ObjectUtils.isNotEmpty(x.getBudgetSubjectCode()))
                .map(x->x.getBudgetSubjectCode()).collect(Collectors.toList());
        List<String> customerCodes = filterList.stream().filter(x->ObjectUtils.isNotEmpty(x.getCustomerCode()))
                .map(x->x.getCustomerCode()).collect(Collectors.toList());
        List<String> itemCodes = filterList.stream().filter(x->ObjectUtils.isNotEmpty(x.getItemCode()))
                .map(x->x.getItemCode()).collect(Collectors.toList());
        List<String> productCodes = filterList.stream().filter(x->ObjectUtils.isNotEmpty(x.getProductCode()))
                .map(x->x.getProductCode()).collect(Collectors.toList());
        List<OverallPlanBudget> planBudgetList = overallPlanBudgetService.findOverallBudgetByCondition(orgCodes, budgetSubjectCodes, caseVo.getYears(), customerCodes, itemCodes, productCodes);
        Map<String, BigDecimal> planBudgetMap = planBudgetList.stream().collect(Collectors.groupingBy(x -> {
            String key = x.getControlCode() + x.getYears() + x.getDepartmentCode() + x.getSubjectCode()
                    + x.getCustomerCode() + x.getItemCode() + x.getProductCode();
            return key;
        }, Collectors.mapping(OverallPlanBudget::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        List<OverallPlanBudget> overallPlanBudgets = Lists.newArrayList();
        for (BudgetTrackVo budgetTrackVo : filterList) {
            String key = budgetTrackVo.getBudgetControlCode() + budgetTrackVo.getYearMonthLy() + budgetTrackVo.getDepartmentOneCode() + budgetTrackVo.getBudgetSubjectCode()
                    + budgetTrackVo.getCustomerCode() + budgetTrackVo.getItemCode() + budgetTrackVo.getProductCode();
            BigDecimal usedAmount = planBudgetMap.getOrDefault(key, BigDecimal.ZERO);
            OverallPlanBudget vo = new OverallPlanBudget();
            vo.setApplyAmount(caseVo.getApplyAmount());
            vo.setSchemeCode(caseVo.getSchemeCode());
            vo.setSchemeDetailCode(caseVo.getSchemeDetailCode());
            vo.setYears(budgetTrackVo.getYearMonthLy());
            vo.setDepartmentCode(budgetTrackVo.getDepartmentOneCode());
            vo.setDepartmentName(budgetTrackVo.getDepartmentOneName());
            vo.setCustomerCode(budgetTrackVo.getCustomerCode());
            vo.setCustomerName(budgetTrackVo.getCustomerName());
            vo.setItemCode(budgetTrackVo.getItemCode());
            vo.setItemName(budgetTrackVo.getItemName());
            vo.setProductCode(budgetTrackVo.getProductCode());
            vo.setProductName(budgetTrackVo.getProductName());
            vo.setSubjectCode(budgetTrackVo.getBudgetSubjectCode());
            vo.setSubjectName(budgetTrackVo.getBudgetSubjectName());
            vo.setSurplusAmount(budgetTrackVo.getMonthBalanceAmount().subtract(usedAmount));
            vo.setUsedAmount(caseVo.getApplyAmount());
            vo.setControlCode(budgetTrackVo.getBudgetControlCode());
            vo.setTrackCode(budgetTrackVo.getTrackCode());
            vo.setControlForm(budgetControlMap.get(budgetTrackVo.getBudgetControlCode()));
            overallPlanBudgets.add(vo);
        }

        return new AsyncResult<>(overallPlanBudgets);
    }
}
