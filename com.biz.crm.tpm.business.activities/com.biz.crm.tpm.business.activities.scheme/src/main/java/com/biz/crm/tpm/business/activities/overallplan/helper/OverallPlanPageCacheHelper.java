package com.biz.crm.tpm.business.activities.overallplan.helper;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanCaseService;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/30 18:08
 */
@Component
@Slf4j
public class OverallPlanPageCacheHelper extends BusinessPageCacheHelper<OverallPlanCaseVo, OverallPlanCaseVo> {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private OverallPlanCaseService overallPlanCaseService;

    @Resource
    private RedisTemplate redisTemplate;

    @Override
    public String getCacheKeyPrefix() {
        return OverallPlanConstant.OVERALL_PLAN_CACHE_PAGE;
    }

    @Override
    public Class<OverallPlanCaseVo> getDtoClass() {
        return OverallPlanCaseVo.class;
    }

    @Override
    public Class<OverallPlanCaseVo> getVoClass() {
        return OverallPlanCaseVo.class;
    }

    @Override
    public List<OverallPlanCaseVo> findDtoListFromRepository(OverallPlanCaseVo vo, String cacheKey) {
        if (ObjectUtils.isEmpty(vo.getId())) {
            return Lists.newArrayList();
        }
        return overallPlanCaseService.findListBySchemeId(vo.getId());
    }

    @Override
    public List<OverallPlanCaseVo> newItem(String cacheKey, List<OverallPlanCaseVo> itemList) {
        OverallPlanCaseVo vo = new OverallPlanCaseVo();
        vo.setId(UuidCrmUtil.general());
        vo.setSchemeDetailCode(null);
        vo.setSchemeCode(null);
        vo.setSort(System.currentTimeMillis());
        return Lists.newArrayList(vo);
    }

    @Override
    public List<OverallPlanCaseVo> copyItem(String cacheKey, List<OverallPlanCaseVo> itemList) {
        List<OverallPlanCaseVo> newList = JSONObject.parseArray(JSONObject.toJSONString(itemList),OverallPlanCaseVo.class);
        for (OverallPlanCaseVo caseVo : newList) {
            caseVo.setId(UuidCrmUtil.general());
            caseVo.setSchemeDetailCode(null);
            caseVo.setSort(System.currentTimeMillis());
        }
        return newList;
    }

    /**
     * 导入新增数据
     *
     * @param cacheKey
     * @param itemList
     * @return
     */
    public void importNewItem(String cacheKey, List<OverallPlanCaseVo> itemList,String schemeType) {
        String redisCacheIdKey = this.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = this.getRedisCacheDataKey(cacheKey);

        //导入新增数据
        for (OverallPlanCaseVo newItem : itemList) {
            newItem.setId(UuidCrmUtil.general());
        }

        List<OverallPlanCaseVo> caseVoList = overallPlanCaseService.checkCaseList(itemList, schemeType);


        Object[] newIdArr = caseVoList.stream().map(this::getDtoKey).toArray();
        redisService.lPushAll(redisCacheIdKey, this.getExpireTime(), newIdArr);

        Map<Object, OverallPlanCaseVo> updateMap = caseVoList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }


    @Override
    public List<OverallPlanCaseVo> dtoListToVoList(List<OverallPlanCaseVo> overallPlanCaseVos) {
        return overallPlanCaseVos;
    }

    @Override
    public Object getDtoKey(OverallPlanCaseVo vo) {
        return vo.getId();
    }

    @Override
    public String getCheckedStatus(OverallPlanCaseVo vo) {
        return vo.getChecked();
    }
}
