package com.biz.crm.tpm.business.activities.marketingplan.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.eunm.CashStatusEnum;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCaseExtend;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanCaseExtendMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:42
 */
@Component
public class MarketingPlanCaseExtendRepository extends ServiceImpl<MarketingPlanCaseExtendMapper, MarketingPlanCaseExtend> {


    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.remove(Wrappers.lambdaQuery(MarketingPlanCaseExtend.class)
                .in(MarketingPlanCaseExtend::getSchemeCode, schemeCodes));
    }

    public void deleteBySchemeDetailCodes(List<String> schemeDetailCodes) {
        this.remove(Wrappers.lambdaQuery(MarketingPlanCaseExtend.class)
                .in(MarketingPlanCaseExtend::getSchemeDetailCode, schemeDetailCodes));
    }

    public MarketingPlanCaseExtend findBySchemeDetailCode(String schemeDetailCode) {
        if (StringUtils.isBlank(schemeDetailCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(MarketingPlanCaseExtend::getSchemeDetailCode, schemeDetailCode)
                .one();
    }


    public List<MarketingPlanCaseExtend> findBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtil.isEmpty(schemeDetailCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(MarketingPlanCaseExtend::getSchemeDetailCode, schemeDetailCodes)
                .list();
    }

    public void updateWithHoldingStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String withHoldingStatus) {
        if (CollectionUtil.isEmpty(schemeDetailCodes)) {
            return;
        }
        this.lambdaUpdate()
                .in(MarketingPlanCaseExtend::getSchemeDetailCode, schemeDetailCodes)
                .set(MarketingPlanCaseExtend::getWithHoldingStatus, withHoldingStatus).update();
    }

    public void rewriteEndCaseOrWithholding(List<MarketingPlanCaseVo> caseVos) {
        for (MarketingPlanCaseVo caseVo : caseVos) {
            if (CashStatusEnum.WHOLE_CASH.getCode().equals(caseVo.getCashStatus())) {
                this.lambdaUpdate()
                        .set(MarketingPlanCaseExtend::getWholeCashDate, DateUtil.format(new Date(), "yyyy-MM-dd"))
                        .eq(MarketingPlanCaseExtend::getSchemeDetailCode, caseVo.getSchemeDetailCode()).update();
            }
        }
    }

}
