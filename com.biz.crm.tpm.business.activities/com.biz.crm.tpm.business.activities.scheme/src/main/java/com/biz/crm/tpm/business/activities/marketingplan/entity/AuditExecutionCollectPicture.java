package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_audit_execution_collect_pic")
@Table(
        name = "tpm_audit_execution_collect_pic",
        indexes = {
                @Index(name = "tpm_audit_execution_collect_pic_index1", columnList = "collect_id")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_audit_execution_collect_pic", comment = "终端活动执行稽查照片")
@ApiModel(value = "AuditExecutionCollectPicture", description = "终端活动执行稽查照片")
public class AuditExecutionCollectPicture extends FileEntity {

    @ApiModelProperty("终端活动执行稽查id")
    @Column(name = "collect_id", columnDefinition = "varchar(32) comment '终端活动执行稽查id'")
    private String collectId;
}
