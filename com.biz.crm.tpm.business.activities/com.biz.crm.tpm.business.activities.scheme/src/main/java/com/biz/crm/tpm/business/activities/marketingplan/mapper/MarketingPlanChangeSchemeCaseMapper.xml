<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanChangeSchemeCaseMapper">

    <select id="findListByOriginalSchemeDetailCodes" resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanChangeSchemeCase">
        select a.* from tpm_marketing_plan_change_scheme_case a
        left join tpm_marketing_plan b on a.scheme_code = b.scheme_code
        where b.process_status != '3'
        and a.original_scheme_detail_code in
        <foreach collection="schemeDetailCode" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>

