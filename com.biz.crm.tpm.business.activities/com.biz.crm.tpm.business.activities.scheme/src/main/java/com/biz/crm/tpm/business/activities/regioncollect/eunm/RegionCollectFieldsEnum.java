package com.biz.crm.tpm.business.activities.regioncollect.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum RegionCollectFieldsEnum {
    collectCode("collectCode"),
    auditCode("deptCode"),
    years("years"),
    estimatedSalesVolume("estimatedSalesVolume"),
    applyAmount("applyAmount"),
    ratio("ratio"),
    ratioBudget("ratioBudget"),
    achieveRatio("achieveRatio"),
    profitMargin("profitMargin"),
    profitMarginBudget("profitMarginBudget"),
    remark("remark"),
    estimateAmount("estimateAmount"),
    budgetAmount("budgetAmount"),
    marketingBudgetAmount("marketingBudgetAmount"),
    lrl("lrl"),
    yslrl("yslrl"),
    cflghflysfl("cflghflysfl"),
    cklrelreyslre("cklrelreyslre"),
    title("title"),
    falx("falx"),
    ;

    private String dictCode;

    public static RegionCollectFieldsEnum findByCode(String code) {
        Optional<RegionCollectFieldsEnum> first = Stream.of(RegionCollectFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}