package com.biz.crm.tpm.business.activities.overallplan.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlan;

public interface OverallPlanOaService {

    /**
     * 推送OA
     *
     * @param plan
     * @return
     */
    JSONObject pushOa(OverallPlan plan);

    /**
     * 重新提交OA
     *
     * @param plan
     * @return
     */
    JSONObject resubmitOa(OverallPlan plan);

    /**
     * OA撤回
     *
     * @param plan
     * @return
     */
    boolean oaWithdraw(OverallPlan plan, String remark);
}
