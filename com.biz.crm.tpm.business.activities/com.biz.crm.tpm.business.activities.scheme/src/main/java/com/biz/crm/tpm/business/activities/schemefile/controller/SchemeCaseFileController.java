package com.biz.crm.tpm.business.activities.schemefile.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.schemefile.service.SchemeCaseFilesService;
import com.biz.crm.tpm.business.activities.schemefile.vo.SchemeCaseFilesVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/22 18:51
 */
@RestController
@RequestMapping("/v1/schemeCaseFileController")
public class SchemeCaseFileController {

    @Autowired
    private SchemeCaseFilesService schemeCaseFilesService;


    @ApiOperation(value = "查询营销方案附件")
    @GetMapping("findMarketingFileListBySchemeCode")
    public Result<List<SchemeCaseFilesVo>> findMarketingFileListBySchemeCode(@RequestParam String schemeCode){
        return Result.ok(schemeCaseFilesService.findFilesList(schemeCode, OverallPlanSchemeTypeEnum.MARKETING.getCode()));
    }
}
