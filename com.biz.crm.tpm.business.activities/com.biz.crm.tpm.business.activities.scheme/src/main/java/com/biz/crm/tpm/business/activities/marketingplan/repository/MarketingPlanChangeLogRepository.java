package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanChangeLog;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanChangeLogMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanChangeLogVo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/5 16:58
 */
@Component
public class MarketingPlanChangeLogRepository extends ServiceImpl<MarketingPlanChangeLogMapper, MarketingPlanChangeLog> {


    public MarketingPlanChangeLog findBySchemeCode(String schemeCode) {
        return this.lambdaQuery()
                .eq(MarketingPlanChangeLog::getSchemeCode, schemeCode).one();
    }


    public List<MarketingPlanChangeLogVo> findChangeList(String schemeCode) {
        return this.baseMapper.findChangeList(schemeCode);
    }
}
