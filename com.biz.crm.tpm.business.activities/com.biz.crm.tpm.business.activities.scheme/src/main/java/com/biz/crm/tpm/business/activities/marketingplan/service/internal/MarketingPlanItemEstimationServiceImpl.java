package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanItemEstimation;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanItemEstimationRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanItemEstimationService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanItemEstimationVo;
import com.bizunited.nebula.common.util.JsonUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/27 14:30
 **/
@Service
public class MarketingPlanItemEstimationServiceImpl implements MarketingPlanItemEstimationService {

    @Resource
    private MarketingPlanItemEstimationRepository marketingPlanItemEstimationRepository;

    @Override
    public void saveBatchList(List<MarketingPlanItemEstimationVo> itemEstimations, String schemeCode) {
        marketingPlanItemEstimationRepository.lambdaUpdate()
                .eq(MarketingPlanItemEstimation::getSchemeCode, schemeCode)
                .remove();
        List<MarketingPlanItemEstimation> dataList = JsonUtils.convert(itemEstimations, List.class, MarketingPlanItemEstimation.class);
        dataList.forEach(x -> {
            x.setSchemeCode(schemeCode);
            x.setId(null);
        });
        marketingPlanItemEstimationRepository.saveBatch(dataList);
    }

    @Override
    public List<MarketingPlanItemEstimationVo> findListBySchemeCode(String schemeCode) {
        List<MarketingPlanItemEstimation> list = marketingPlanItemEstimationRepository.lambdaQuery()
                .eq(MarketingPlanItemEstimation::getSchemeCode, schemeCode)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return JsonUtils.convert(list, List.class, MarketingPlanItemEstimationVo.class);
    }
}
