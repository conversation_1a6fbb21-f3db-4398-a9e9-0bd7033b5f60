package com.biz.crm.tpm.business.activities.scheme.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Vo：方案活动;
 *
 * <AUTHOR> Keller
 * @date : 2022-6-1
 */
@ApiModel(value = "ActivitiesScheme", description = "方案活动")
@Getter
@Setter
public class ActivitiesSchemeVo extends UuidFlagOpVo {
  private static final long serialVersionUID = -5412325494436543595L;
  /**
   * 操作的预授权标记
   */
  @ApiModelProperty(name = "操作的预授权标记", notes = "操作的预授权标记", value = "操作的预授权标记", required = true)
  private String prefix;
  /**
   * 主键
   */
  @ApiModelProperty(name = "id", notes = "主键", value = "主键")
  private String id;
  /**
   * 租户编号
   */
  @ApiModelProperty(name = "tenantCode", notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /**
   * 备注
   */
  @ApiModelProperty(name = "remark", notes = "备注", value = "备注")
  private String remark;
  /**
   * 数据业务状态（启用状态）
   */
  @ApiModelProperty(name = "enableStatus", notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
  private String enableStatus;
  /**
   * 数据状态（删除状态）
   */
  @ApiModelProperty(name = "delFlag", notes = "数据状态（删除状态）", value = "数据状态（删除状态）")
  private String delFlag;
  /**
   * 修改人名称
   */
  @ApiModelProperty(name = "modifyName", notes = "修改人名称", value = "修改人名称")
  private String modifyName;
  /**
   * 修改人账号
   */
  @ApiModelProperty(name = "modifyAccount", notes = "修改人账号", value = "修改人账号")
  private String modifyAccount;
  /**
   * 修改时间
   */
  @ApiModelProperty(name = "modifyTime", notes = "修改时间", value = "修改时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date modifyTime;
  /**
   * 创建人名称
   */
  @ApiModelProperty(name = "createName", notes = "创建人名称", value = "创建人名称")
  private String createName;
  /**
   * 创建人账号
   */
  @ApiModelProperty(name = "createAccount", notes = "创建人账号", value = "创建人账号")
  private String createAccount;
  /**
   * 创建时间
   */
  @ApiModelProperty(name = "createTime", notes = "创建时间", value = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /**
   * 活动名称
   */
  @ApiModelProperty(name = "activitiesName", notes = "活动名称", value = "活动名称")
  private String activitiesName;
  /**
   * 活动编号
   */
  @ApiModelProperty(name = "activitiesCode", notes = "活动编号", value = "活动编号")
  private String activitiesCode;
  /**
   * 开始时间
   */
  @ApiModelProperty(name = "beginTime", notes = "开始时间", value = "开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /**
   * 结束时间
   */
  @ApiModelProperty(name = "endTime", notes = "结束时间", value = "结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /**
   * 方案编号
   */
  @ApiModelProperty(name = "schemeCode", notes = "方案编号", value = "方案编号")
  private String schemeCode;
  /**
   * 总申请金额
   */
  @ApiModelProperty(name = "totalApplyAmount", notes = "总申请金额", value = "总申请金额")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("活动状态")
  private String status;

  /**
   * 关联信息
   */
  @ApiModelProperty("关联信息")
  private List<ActivitiesSchemeRelationVo> relations;

  /**
   * 关联信息
   */
  @ApiModelProperty("关联信息")
  private List<ActivitiesSchemeDetailRelationVo> detailRelations;

  /**
   * 动态明细信息
   */
  @ApiModelProperty("动态明细信息")
  private Map<String, List<BaseActivityItemVo>> items;

  @ApiModelProperty(value = "附件")
  private List<ActivitiesSchemeFilesVo> files;

}
