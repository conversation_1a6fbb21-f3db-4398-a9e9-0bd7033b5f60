package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_plan_exceution")
@Table(
        name = "tpm_plan_exceution",
        indexes = {
                @Index(name = "plan_exceution_index0", columnList = "scheme_detail_code"),
                @Index(name = "plan_exceution_index1", columnList = "scheme_code"),
                @Index(name = "plan_exceution_index3", columnList = "scheme_detail_code,create_pos_code,terminal_code,dynamic_key,dynamic_form_code,parent_code,execution_time",unique = true),
                @Index(name = "plan_exceution_index4", columnList = "act_execute_code"),
                @Index(name = "idx_customer_code", columnList = "customer_code"),
                @Index(name = "idx_customer_name", columnList = "customer_name"),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_plan_exceution", comment = "执行计划")
@ApiModel(value = "PlanExecution", description = "执行计划")
public class PlanExecution extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    @Column(name = "act_execute_code", columnDefinition = "varchar(32) comment '活动执行编码'")
    private String actExecuteCode;

    @ApiModelProperty("门店编码")
    @Column(name = "terminal_code", length = 64, columnDefinition = "varchar(64) COMMENT '门店编码'")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    @Column(name = "terminal_name", columnDefinition = "varchar(255) COMMENT '门店名称'")
    private String terminalName;

    @ApiModelProperty("终端类型")
    @Column(name = "terminal_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '终端类型'")
    private String terminalType;

    @ApiModelProperty("执行结果")
    @Column(name = "result", length = 64, columnDefinition = "VARCHAR(64) COMMENT '执行结果'")
    private String result;

    @ApiModelProperty("执行人")
    @Column(name = "executor", length = 64, columnDefinition = "VARCHAR(64) COMMENT '执行人'")
    private String executor;

    @ApiModelProperty("执行时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "execute_date", columnDefinition = "DATETIME COMMENT '执行时间 '")
    private Date executeDate;

    /**
     * 执行时间 yyyy-mm-dd
     */
    @ApiModelProperty("执行时间")
    @Column(name = "execution_time", columnDefinition = "varchar(32) comment '执行时间'")
    private String executionTime;

    @ApiModelProperty("执行标准")
    @Column(name = "act_standards", columnDefinition = "varchar(128) COMMENT '执行标准'")
    private String actStandards;

    @ApiModelProperty("要求执行资料")
    @Column(name = "require_execute", length = 64, columnDefinition = "VARCHAR(64) COMMENT '要求执行资料'")
    private String requireExecute;




    /****************************SAF步骤所需基础字段*******************************************/

    @ApiModelProperty("执行计划业务编码")
    @TableField(value = "parent_code")
    @Column(name = "parent_code", length = 64, columnDefinition = "varchar(64) COMMENT '执行计划业务编码'")
    private String parentCode;

    @ApiModelProperty("步骤业务编码stepCode")
    @TableField(value = "dynamic_key")
    @Column(name = "dynamic_key", length = 64, columnDefinition = "varchar(64) COMMENT '步骤业务编码stepCode'")
    private String dynamicKey;

    @ApiModelProperty("动态表单全局唯一编码formCode")
    @TableField(value = "dynamic_form_code")
    @Column(name = "dynamic_form_code", columnDefinition = "varchar(255) COMMENT '动态表单全局唯一编码formCode'")
    private String dynamicFormCode;

    @Column(name = "longitude", columnDefinition = "decimal(12,8) COMMENT '经度'")
    @ApiModelProperty("位置信息:经度")
    private BigDecimal longitude;

    @Column(name = "latitude", columnDefinition = "decimal(12,8) COMMENT '纬度'")
    @ApiModelProperty("位置信息:纬度")
    private BigDecimal latitude;


    //权限相关字段

    @ApiModelProperty("创建人岗位名称")
    @Column(name = "create_pos_name", length = 128, columnDefinition = "varchar(128) COMMENT '创建人岗位名称'")
    private String createPosName;

    @ApiModelProperty("创建人岗位")
    @Column(name = "create_pos_code", length = 32, columnDefinition = "varchar(32) COMMENT '创建人岗位'")
    private String createPosCode;

    @ApiModelProperty("用户账号")
    @Column(name = "user_name", length = 128, columnDefinition = "varchar(128) COMMENT '用户姓名'")
    private String userName;

    @ApiModelProperty("用户编码")
    @Column(name = "user_code", length = 32, columnDefinition = "varchar(32) COMMENT '用户编码'")
    private String userCode;

    @ApiModelProperty("用户名称")
    @Column(name = "full_name", columnDefinition = "varchar(128) comment '人员姓名'")
    private String fullName;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("执行照片")
    private List<PlanExecutionPicture> pictureList;

    @ApiModelProperty("是否展示水印(true:是,false:否)")
    @Column(name = "watermark_flag", columnDefinition = "varchar(10) default 'Y' COMMENT '是否展示水印，Y是N否'")
    private String watermarkFlag;


    @ApiModelProperty("水印")
    @Column(name = "watermark_str", columnDefinition = "varchar(512) comment '水印'")
    private String watermarkStr;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;


    @ApiModelProperty("人员在岗和考勤信息")
    @Column(name = "user_clock_record", columnDefinition = "text comment '人员在岗和考勤信息'")
    private String userClockRecord;
}
