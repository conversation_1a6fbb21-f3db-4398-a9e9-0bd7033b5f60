package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingSalesPlan;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingSalesPlanMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/12 09:38
 */
@Component
public class MarketingSalesPlanRepository extends ServiceImpl<MarketingSalesPlanMapper, MarketingSalesPlan> {

    public Page<MarketingSalesPlanVo> findCollectList(Page<MarketingSalesPlanVo> page, MarketingSalesPlanVo vo) {
        return this.baseMapper.findCollectList(page, vo, TenantUtils.getTenantCode());
    }

    /**
     * 查询销售计划列表
     *
     * @param onlyKeys
     * @return
     */
    public List<MarketingSalesPlan> findListByOnlyKeys(List<String> onlyKeys) {
        return this.lambdaQuery()
                .in(MarketingSalesPlan::getOnlyKey, onlyKeys)
                .list();
    }

    public List<MarketingSalesPlan> findListByYearsAndCustomerAndCostCenter(List<String> customerCodes, List<String> costCenterCodes,
                                                                            String years, List<String> excludeSchemeCodes) {
        if (CollectionUtils.isEmpty(customerCodes) || CollectionUtils.isEmpty(costCenterCodes) || ObjectUtils.isEmpty(years)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(MarketingSalesPlan::getCustomerCode, customerCodes)
                .in(MarketingSalesPlan::getCostCenterCode, costCenterCodes)
                .eq(MarketingSalesPlan::getYears, years)
                .notIn(CollectionUtils.isNotEmpty(excludeSchemeCodes), MarketingSalesPlan::getSchemeCode, excludeSchemeCodes)
                .eq(MarketingSalesPlan::getChangeFlag, BooleanEnum.FALSE.getCapital())
                .list();
    }

    public void deleteBySchemeCode(String schemeCode) {
        this.lambdaUpdate()
                .eq(MarketingSalesPlan::getSchemeCode, schemeCode)
                .remove();
    }

    public List<MarketingSalesPlan> findListBySchemeCode(String schemeCode) {
        if (ObjectUtils.isEmpty(schemeCode)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(MarketingSalesPlan::getSchemeCode, schemeCode).list();
    }

    public List<MarketingSalesPlan> findListBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(MarketingSalesPlan::getSchemeCode, schemeCodes).list();
    }

    public List<MarketingSalesPlanVo> findListBySalesPlanQueryVo(SalesPlanQueryVo vo) {
        return this.baseMapper.findListBySalesPlanQueryVo(vo, TenantUtils.getTenantCode());
    }

    public List<MarketingSalesPlanVo> findListByCondition(Set<String> yearsSet, Set<String> costCenterCodes, Set<String> customerCodes, Set<String> itemCodes,
                                                          Set<String> productCodes, List<String> excludeSchemeCodes, List<String> includeSchemeCodes, String changeFlag, String confirmStatus) {
        return this.baseMapper.findListByCondition(yearsSet, costCenterCodes, customerCodes, itemCodes,
                productCodes, excludeSchemeCodes, includeSchemeCodes, changeFlag, confirmStatus, TenantUtils.getTenantCode());
    }

    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.lambdaUpdate()
                .in(MarketingSalesPlan::getSchemeCode, schemeCodes)
                .remove();
    }

    public List<MarketingSalesPlanVo> findByCollectCode(String collectCode) {
        return this.baseMapper.findByCollectCode(collectCode);
    }
}
