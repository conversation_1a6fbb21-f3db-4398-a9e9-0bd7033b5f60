package com.biz.crm.tpm.business.activities.schemefile.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/22 18:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_scheme_case_files")
@Table(
        name = "tpm_scheme_case_files",
        indexes = {
                @Index(name = "tpm_scheme_case_files_index0", columnList = "scheme_code,scheme_type", unique = true)
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_scheme_case_files", comment = "方案附件")
@ApiModel(value = "SchemeCaseFiles", description = "方案附件")
public class SchemeCaseFiles extends FileEntity {

    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @Column(name = "scheme_type", columnDefinition = "varchar(32) comment '方案类型'")
    private String schemeType;
}
