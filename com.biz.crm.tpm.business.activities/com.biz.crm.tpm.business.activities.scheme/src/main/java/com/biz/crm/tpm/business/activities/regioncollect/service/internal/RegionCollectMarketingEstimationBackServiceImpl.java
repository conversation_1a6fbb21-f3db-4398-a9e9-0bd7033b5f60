package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectMarketingEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectMarketingEstimationBack;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectMarketingEstimationBackRepository;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectMarketingEstimationRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectMarketingEstimationBackService;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectMarketingEstimationService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Transactional
@Slf4j
@Service
public class RegionCollectMarketingEstimationBackServiceImpl implements RegionCollectMarketingEstimationBackService {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private RegionCollectMarketingEstimationBackRepository repository;


    @Override
    public void deleteByCollectCodes(List<String> collectCodes) {
        repository.deleteByCollectCodes(collectCodes);
    }

    @Override
    public void saveList(List<RegionCollectMarketingEstimationVo> list, String collectCode) {
        repository.deleteByCollectCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<RegionCollectMarketingEstimationBack> dataList = list.stream().map(x -> {
            RegionCollectMarketingEstimationBack data = nebulaToolkitService.copyObjectByWhiteList(x, RegionCollectMarketingEstimationBack.class, HashSet.class, ArrayList.class);
            data.setId(null);
            data.setCollectCode(collectCode);
            return data;
        }).collect(Collectors.toList());
        repository.saveBatch(dataList);
    }
}
