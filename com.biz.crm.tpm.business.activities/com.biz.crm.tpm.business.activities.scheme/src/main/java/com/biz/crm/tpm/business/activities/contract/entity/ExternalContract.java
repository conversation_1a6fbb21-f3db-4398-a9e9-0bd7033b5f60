package com.biz.crm.tpm.business.activities.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 09:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_external_contract")
@Table(
        name = "tpm_external_contract",
        indexes = {
                @Index(name = "tpm_external_contract_index0", columnList = "tenant_code,contract_code,external_flag"),
                @Index(name = "tpm_external_contract_index2", columnList = "del_flag")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_external_contract", comment = "外部合同")
@ApiModel(value = "ExternalContract", description = "外部合同")
public class ExternalContract extends TenantFlagOpEntity {

    @ApiModelProperty("流程ID")
    @Column(name = "process_id", columnDefinition = "varchar(32) comment '流程ID'")
    private String processId;

    @ApiModelProperty("合同编码")
    @Column(name = "contract_code", columnDefinition = "varchar(32) comment '合同编码'")
    private String contractCode;

    @ApiModelProperty("合同名称")
    @Column(name = "contract_name", columnDefinition = "varchar(200) comment '合同名称'")
    private String contractName;

    @ApiModelProperty("合同状态")
    @Column(name = "contract_status", columnDefinition = "varchar(20) comment '合同状态'")
    private String contractStatus;

    @ApiModelProperty("辅助列")
    @Column(name = "auxiliary", columnDefinition = "varchar(20) comment '辅助列'")
    private String auxiliary;

    @ApiModelProperty("合同类型")
    @Column(name = "contract_type", columnDefinition = "varchar(32) comment '合同类型'")
    private String contractType;

    @ApiModelProperty("是否补充")
    @Column(name = "is_replenish", columnDefinition = "varchar(10) comment '是否补充'")
    private String isReplenish;

    @ApiModelProperty("原始合同")
    @Column(name = "original_contract_code", columnDefinition = "varchar(32) comment '原始合同编码'")
    private String originalContractCode;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

    @ApiModelProperty("开始时间")
    @Column(name = "start_date", columnDefinition = "varchar(32) comment '开始时间'")
    private String startDate;

    @ApiModelProperty("结束时间")
    @Column(name = "end_date", columnDefinition = "varchar(32) comment '结束时间'")
    private String endDate;

    @ApiModelProperty("合同附件")
    @Column(name = "file_url", columnDefinition = "varchar(200) comment '合同附件'")
    private String fileUrl;

    @ApiModelProperty("是否外部合同")
    @Column(name = "external_flag", columnDefinition = "varchar(10) comment '是否外部合同'")
    private String externalFlag;

    @ApiModelProperty("执行状态")
    @Column(name = "execute_status", columnDefinition = "varchar(10) comment '执行状态'")
    private String executeStatus;

    @ApiModelProperty("唯一编码")
    @Column(name = "only_key", columnDefinition = "varchar(64) comment '唯一编码'")
    private String onlyKey;

    @ApiModelProperty("日志")
    @Column(name = "log", columnDefinition = "text comment '日志'")
    private String log;

    @ApiModelProperty("是否推送SFA公告")
    @Column(name = "push_sfa_flag", columnDefinition = "varchar(10) comment '是否推送SFA公告'")
    private String pushSfaFlag;
}
