package com.biz.crm.tpm.business.activities.marketingplan.service.strategy.impl;

import com.biz.crm.business.common.base.util.BusinessRequiredUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaCusVo;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCaseStrategy;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 固定模板
 * <AUTHOR>
 * @Date 2024/6/20 20:09
 */
@Component
public class FixedCheckStrategyImpl implements MarketingPlanCaseStrategy<MarketingPlanCaseVo> {

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private TerminalVoService terminalVoService;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    @Override
    public void checkCaseList(List<MarketingPlanCaseVo> list, String years, String schemeCode,String originalSchemeCode, String cacheKey) {
        //获取配置模板信息
        List<ActivitiesTemplateConfigDetailVo> templateFiledList = checkHelper.getTemplateFiled(MarketingPlanCaseTypeEnum.fixed.getCode());
        Map<String, Boolean> filedMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getRequired));
        Map<String, String> chineseMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getTitle));
        //获取活动细类配置
        Set<String> detailCodeSet = checkHelper.getTemplateRelateDetailCodes(MarketingPlanCaseTypeEnum.fixed.getCode());
        List<String> originalSchemeDetailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOriginalSchemeDetailCode()))
                .map(MarketingPlanCaseVo::getOriginalSchemeDetailCode).collect(Collectors.toList());
        Map<String, MarketingPlanCaseVo> originCaseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(originalSchemeDetailCodes)) {
            List<MarketingPlanCaseVo> marketingPlanCaseVos = checkHelper.findCaseListBySchemeDetailCodes(originalSchemeDetailCodes);
            originCaseMap = marketingPlanCaseVos.stream().collect(Collectors.toMap(MarketingPlanCaseVo::getSchemeDetailCode, Function.identity()));
        }

        //标记分组
        Map<String, Long> muchDepartmentMap = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMuchDepartmentMark()))
                .collect(Collectors.groupingBy(MarketingPlanCaseVo::getMuchDepartmentMark, Collectors.counting()));
        if (ObjectUtils.isNotEmpty(muchDepartmentMap)) {
            muchDepartmentMap = muchDepartmentMap.entrySet().stream()
                    .filter(entry -> entry.getValue() == 1)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        //查询组织的成本中心
        Map<String, Set<String>> orgCostCenterMap = checkHelper.findOrgCostCenter(list);
        Set<String> costCenterCodeSet = orgCostCenterMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        Set<String> yearSet = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getYears()))
                .map(MarketingPlanCaseVo::getYears).collect(Collectors.toSet());
        List<String> customerCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()))
                .map(MarketingPlanCaseVo::getCustomerCode).collect(Collectors.toList());

        List<String> excludeSchemeCode = com.google.common.collect.Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(schemeCode)) {
            excludeSchemeCode.add(schemeCode);
        }
        if (ObjectUtils.isNotEmpty(originalSchemeCode)) {
            excludeSchemeCode.add(originalSchemeCode);
        }

        List<MarketingSalesPlanVo> salesPlanVos = Lists.newArrayList();
        String salesCacheKey = cacheKey + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + years;
        List<MarketingSalesPlanVo> salesPlanList1 = marketingSalesPlanService.findCacheList(salesCacheKey);
        List<MarketingSalesPlanVo> salesPlanList2 = marketingSalesPlanService.findListByConditionToMarketingScheme(yearSet, costCenterCodeSet, Sets.newHashSet(customerCodes),
                null, null, excludeSchemeCode, null,null);
        if (CollectionUtils.isNotEmpty(salesPlanList1)) {
            salesPlanVos.addAll(salesPlanList1);
        }
        if (CollectionUtils.isNotEmpty(salesPlanList2)) {
            salesPlanVos.addAll(salesPlanList2);
        }

        Map<String, List<MarketingSalesPlanVo>> salesPlanMap = salesPlanVos.stream()
                .filter(k-> StringUtil.isNotEmpty(k.getCostCenterCode()))
                .collect(Collectors.groupingBy(MarketingSalesPlanVo::getCostCenterCode));

        List<CustomerVo> customerVoList = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerVoMap = customerVoList.stream().filter(x -> EnableStatusEnum.ENABLE.getCode().equals(x.getEnableStatus()) &&
                        BooleanEnum.TRUE.getCapital().equals(x.getContractCustomer()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
        Set<String> companyCodeSet = Sets.newHashSet();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(customerVoList)) {
            companyCodeSet = customerVoList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getCompanyCode()))
                    .map(CustomerVo::getCompanyCode).collect(Collectors.toSet());
        }
        //查询终端
        List<String> terminalCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTerminalCode()))
                .map(MarketingPlanCaseVo::getTerminalCode).collect(Collectors.toList());
        List<TerminalVo> terminalVoList = terminalVoService.findTerminalAndRelaCusByTerminalCodeList(terminalCodes);
        Map<String, TerminalVo> terminalMap = terminalVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTerminalCode()))
                .collect(Collectors.toMap(TerminalVo::getTerminalCode, Function.identity()));

        List<String> productCodes = Lists.newArrayList();
        List<String> items = Lists.newArrayList();
        for (MarketingPlanCaseVo vo : list) {
            if (CollectionUtils.isNotEmpty(vo.getProductList())) {
                productCodes.addAll(vo.getProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(vo.getItemList())) {
                items.addAll(vo.getItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toSet()));
            }
        }

        //品项信息
        Map<String, String> itemMap = checkHelper.findSapLevelMap(items);
        Map<String, ProductVo> productMap = checkHelper.findProductListByCodes(productCodes, companyCodeSet,items);

        Map<String, MarketingPlanCaseVo> replaceMap = Maps.newHashMap();
        for (MarketingPlanCaseVo caseVo : list) {
            StringJoiner errMsg = new StringJoiner(";");

            //判断是变更的
            if (ObjectUtils.isNotEmpty(caseVo.getOriginalSchemeDetailCode())) {
                MarketingPlanCaseVo originCaseVo = originCaseMap.get(caseVo.getOriginalSchemeDetailCode());
                originCaseVo.setId(caseVo.getId());
                originCaseVo.setSchemeCode(caseVo.getSchemeCode());
                originCaseVo.setSchemeDetailCode(caseVo.getSchemeDetailCode());
                originCaseVo.setOriginalSchemeDetailCode(caseVo.getOriginalSchemeDetailCode());
                originCaseVo.setOriginalSchemeCode(caseVo.getOriginalSchemeCode());

                LocalDate now = LocalDate.now();
                LocalDate startDate = LocalDate.parse(originCaseVo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate endDate = LocalDate.parse(caseVo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                //如果开始时间晚于当前时间
                if (now.isBefore(startDate)) {
                    originCaseVo.setStartDate(caseVo.getStartDate());
                }
                //如果结束时间早于当前时间
                if (now.isAfter(endDate)) {
                    errMsg.add("结束时间不能早于当前时间");
                } else {
                    originCaseVo.setEndDate(caseVo.getEndDate());
                }
                //费用使用部门
                originCaseVo.setBelongDepartmentCode(caseVo.getBelongDepartmentCode());
                originCaseVo.setBelongDepartmentName(caseVo.getBelongDepartmentName());
                //门店
                originCaseVo.setTerminalCode(caseVo.getTerminalCode());
                originCaseVo.setTerminalName(caseVo.getTerminalName());
                //兑付方式
                originCaseVo.setCashType(caseVo.getCashType());
                //费用金额
                if (checkHelper.paramNotNull(caseVo.getApplyAmount(), "费用金额", errMsg)) {
                    originCaseVo.setApplyAmount(caseVo.getApplyAmount());
                }
                if (Objects.nonNull(originCaseVo.getApplyAmount()) && originCaseVo.getApplyAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    errMsg.add("费用金额必须大于0");
                }
                //费用依据
                originCaseVo.setCostBasisList(caseVo.getCostBasisList());
                originCaseVo.setCostBasis(caseVo.getCostBasis());
                //活动描述
                originCaseVo.setActDesc(caseVo.getActDesc());
                if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                    originCaseVo.setCheckFlag(Boolean.FALSE);
                    originCaseVo.setErrMsg(errMsg.toString());
                } else {
                    originCaseVo.setCheckFlag(Boolean.TRUE);
                }
                //计算费率
                Set<String> costCenterCodes = orgCostCenterMap.getOrDefault(originCaseVo.getBelongDepartmentCode(), Sets.newHashSet());
                checkHelper.calPlanCaseRatio(salesPlanMap,costCenterCodes,originCaseVo);

                replaceMap.put(caseVo.getOriginalSchemeDetailCode(), originCaseVo);
                continue;
            }

            if (ObjectUtils.isNotEmpty(caseVo.getErrMsg())) {
                errMsg.add(caseVo.getErrMsg());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getDetailCode())) {
                if (!detailCodeSet.contains(caseVo.getDetailCode())) {
                    errMsg.add("费用项目不属于当前表单模板配置");
                }
            }
            if (checkHelper.paramNotNull(caseVo.getCustomerCode(), "客户编码", errMsg)) {
                if (customerVoMap.containsKey(caseVo.getCustomerCode())) {
                    CustomerVo customerVo = customerVoMap.get(caseVo.getCustomerCode());
                    caseVo.setCustomerName(customerVo.getCustomerName());
                    caseVo.setErpCode(customerVo.getErpCode());
                    caseVo.setCompanyCode(customerVo.getCompanyCode());
                    caseVo.setProductGroupCode(customerVo.getProductGroupCode());
                    caseVo.setChannelCode(customerVo.getChannelCode());
                    caseVo.setCooperateType(customerVo.getCooperateType());
                    caseVo.setHzlx(customerVo.getHzlx());
                    //校验客户公司代码与成本中心公司代码是否一致
                    if (ObjectUtils.isNotEmpty(caseVo.getCostCenterCompanyCode()) && !customerVo.getCompanyCode().equals(caseVo.getCostCenterCompanyCode())){
                        errMsg.add(String.format("客户公司代码与成本中心公司代码不一致,客户公司代码%s,成本中心公司代码%s",caseVo.getCompanyCode(),caseVo.getCostCenterCompanyCode()));
                    }
                } else {
                    errMsg.add(String.format("客户编码%s在主数据中未查询到或不是合同客户", caseVo.getCustomerCode()));
                }
            }
            if (ObjectUtils.isNotEmpty(caseVo.getTerminalCode())) {
                if (terminalMap.containsKey(caseVo.getTerminalCode())) {
                    TerminalVo terminalVo = terminalMap.get(caseVo.getTerminalCode());
                    caseVo.setTerminalName(terminalVo.getTerminalName());
                    caseVo.setTerminalType(terminalVo.getTerminalType());
                    caseVo.setTerminalChannel(terminalVo.getChannel());
                    caseVo.setTerminalSystem(terminalVo.getSourceSystem());
                    if (checkHelper.collectionNotNull("终端关联客户", errMsg, terminalVo.getRelaCusVos())) {
                        List<String> relaCusList = terminalVo.getRelaCusVos().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCusCode()))
                                .map(TerminalRelaCusVo::getCusCode).collect(Collectors.toList());
                        if (!relaCusList.contains(caseVo.getCustomerCode())) {
                            errMsg.add(String.format("终端关联客户信息不存在当前客户%s", caseVo.getCustomerCode()));
                        }
                    }
                }
            }
            if (checkHelper.collectionNotNull("品项",errMsg,caseVo.getItemList())) {
                for (MarketingPlanProductVo vo : caseVo.getItemList()) {
                    if (itemMap.containsKey(vo.getCode())) {
                        vo.setName(itemMap.get(vo.getCode()));
                    } else {
                        errMsg.add(String.format("品项%s在主数据中未查询到", vo.getCode()));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                for (MarketingPlanProductVo vo : caseVo.getProductList()) {
                    if (productMap.containsKey(vo.getCode())) {
                        if (productMap.containsKey(vo.getCode())) {
                            ProductVo productVo = productMap.get(vo.getCode());
                            vo.setName(productVo.getProductName());
                            Validate.notNull(productVo.getMaterialCode(), String.format("商品%s未存在绑定的物料", productVo.getProductName()));
                            vo.setMaterialCode(productVo.getMaterialCode());
                        } else {
                            errMsg.add(String.format("商品%s不属于品项关联的商品", vo.getCode()));
                        }
                    } else {
                        errMsg.add(String.format("商品%s在主数据中未查询到", vo.getCode()));
                    }
                }
            }
            checkHelper.paramNotNull(caseVo.getApplyAmount(), "申请金额", errMsg);

            if (Objects.nonNull(caseVo.getApplyAmount()) && caseVo.getApplyAmount().compareTo(BigDecimal.ZERO) <= 0) {
                errMsg.add("申请金额必须大于0");
            }
            caseVo.setMuchDepartmentFlag(BooleanEnum.FALSE.getCapital());
            //多部门标记
            if (ObjectUtils.isNotEmpty(caseVo.getMuchDepartmentMark())) {
                caseVo.setMuchDepartmentFlag(BooleanEnum.TRUE.getCapital());
            }
            if (ObjectUtils.isNotEmpty(muchDepartmentMap) && muchDepartmentMap.containsKey(caseVo.getMuchDepartmentMark())) {
                errMsg.add("多部门标记只找到一个,不可进行多部门标记");
            }
            //此处单独调用
            String msg = BusinessRequiredUtil.filedRequiredCheck(filedMap, chineseMap, caseVo);
            if (ObjectUtils.isNotEmpty(msg)) {
                errMsg.add(msg);
            }

            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                caseVo.setCheckFlag(Boolean.FALSE);
                caseVo.setErrMsg(errMsg.toString());
            }
            if (caseVo.getCheckFlag()) {
                //计算费率
                Set<String> costCenterCodes = orgCostCenterMap.getOrDefault(caseVo.getBelongDepartmentCode(), Sets.newHashSet());
                checkHelper.calPlanCaseRatio(salesPlanMap,costCenterCodes,caseVo);
            }
            //寻年度预算
            checkHelper.matchBudget(filedMap, chineseMap, caseVo);
        }

        //进行替换操作
        if (ObjectUtils.isNotEmpty(replaceMap)){
            list.replaceAll(x->{
                String originalCode = x.getOriginalSchemeDetailCode();
                return (originalCode != null && !originalCode.isEmpty() && replaceMap.containsKey(originalCode))
                        ? replaceMap.get(originalCode)
                        : x;
            });
        }
        list.forEach(x->x.setCaseType(getCaseType()));
        //校验多部门
        checkHelper.checkMuchDepartmentMark(list);
    }

    @Override
    public String getCaseType() {
        return MarketingPlanCaseTypeEnum.fixed.getCode();
    }
}
