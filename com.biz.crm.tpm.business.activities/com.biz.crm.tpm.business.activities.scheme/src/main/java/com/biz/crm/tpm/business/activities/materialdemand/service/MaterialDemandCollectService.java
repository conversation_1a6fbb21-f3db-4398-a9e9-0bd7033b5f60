package com.biz.crm.tpm.business.activities.materialdemand.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
public interface MaterialDemandCollectService {

    Page<MaterialDemandCollectVo> findList(Pageable pageable, MaterialDemandCollectVo vo);

    MaterialDemandCollectVo queryByIdOrCode(String id, String code);

    void create(MaterialDemandCollectVo vo);

    void update(MaterialDemandCollectVo vo);

    void submitCreate(MaterialDemandCollectVo vo);

    void submitUpdate(MaterialDemandCollectVo vo);

    void callback(MaterialDemandCollectVo vo);

    void manualApproval(List<String> ids);

    String deleteBatchByIds(List<String> ids);

    List<MaterialDemandCollectDetailVo> collectMaterial(String orgCode,String cacheKey);

    Page<MaterialDemandCollectDetailVo> findGroupMaterialDemand(Pageable pageable, String cacheKey);

    void selectMaterialDemandDetailList(String cacheKey,List<String> demandCodes);

    /**
     * OA撤回
     *
     * @param code
     * @return
     */
    void recover(String code, String remark);


}
