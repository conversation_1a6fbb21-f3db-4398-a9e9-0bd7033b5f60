package com.biz.crm.tpm.business.activities.marketingplan.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.constant.PlanClosureConstant;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanClosureService;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureDetailVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:28
 */
@RestController
@RequestMapping("/v1/planClosureController")
@Api(tags = "方案规划关闭")
public class PlanClosureController extends BusinessPageCacheController<PlanClosureDetailVo, PlanClosureDetailVo> {

    @Autowired
    private PlanClosureService service;

    @Resource
    private RedisLockService redisLockService;

    @ApiOperation(value = "分页查询")
    @GetMapping("findList")
    public Result<Page<PlanClosureVo>> findList(@PageableDefault(50) Pageable pageable, PlanClosureVo vo) {
        return Result.ok(service.findList(pageable, vo));
    }


    @ApiOperation(value = "查询详情")
    @GetMapping("queryByIdOrCode")
    public Result queryByIdOrCode(@RequestParam(value = "id", required = false) String id, @RequestParam(value = "closeCode", required = false) String closeCdoe) {
        return Result.ok(service.queryByIdOrCode(id, closeCdoe));
    }


    @PostMapping("createOrUpdate")
    @ApiOperation(value = "新增/编辑")
    public Result createOrUpdate(@RequestBody PlanClosureVo vo) {
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            Validate.notNull(vo.getCacheKey(), "cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            service.update(vo);
        } else {
            service.create(vo);
        }
        return Result.ok();
    }

    @ApiOperation(value = "新增/编辑 提交")
    @PostMapping("createOrUpdateSubmit")
    public Result createOrUpdateSubmit(@RequestBody PlanClosureVo vo) {
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            Validate.notNull(vo.getCacheKey(), "cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            service.submitUpdate(vo);
        } else {
            service.submitCreate(vo);
        }
        return Result.ok();
    }

    @ApiOperation(value = "提交审批")
    @PostMapping("submitByIdList")
    public Result submitByIdList(@RequestBody List<String> idList) {
        Boolean lockFlag = redisLockService.batchLock(PlanClosureConstant.PLAN_CLOSURE_LOCK, idList, TimeUnit.MINUTES, 20);
        if (lockFlag) {
            try {
                for (String id : idList) {
                    service.submitById(id);
                }
            } finally {
                redisLockService.batchUnLock(PlanClosureConstant.PLAN_CLOSURE_LOCK, idList);
            }
        } else {
            return Result.error("业务数据正在操作,请勿重复点击");
        }
        return Result.ok();
    }

    /**
     * 流程撤回
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "流程撤回")
    @GetMapping("recover")
    public Result<?> recover(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                             @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
        try {
            this.service.recover(code, remark);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "删除")
    @DeleteMapping("deleteByIds")
    public Result deleteByIds(@RequestParam("idList") List<String> idList) {
        Boolean lockFlag = redisLockService.batchLock(PlanClosureConstant.PLAN_CLOSURE_LOCK, idList, TimeUnit.MINUTES, 20);
        if (lockFlag) {
            try {
                service.deleteBatchByIds(idList);
            } finally {
                redisLockService.batchUnLock(PlanClosureConstant.PLAN_CLOSURE_LOCK, idList);
            }
        } else {
            return Result.error("业务数据正在操作,请勿重复点击");
        }
        return Result.ok();
    }

    @ApiOperation(value = "查询营销方案规划未关闭方案明细")
    @GetMapping("findNotMarketingClosedCaseList")
    public Result<Page<MarketingPlanCaseVo>> findNotMarketingClosedCaseList(@PageableDefault(50) Pageable pageable, MarketingPlanCaseQueryDto vo) {
        return Result.ok(service.findNotMarketingClosedCaseList(pageable, vo));
    }

    @ApiOperation(value = "查询大区指引未关闭方案明细")
    @GetMapping("findNotRegionCaseList")
    public Result<Page<OverallPlanCaseVo>> findNotRegionCaseList(@PageableDefault(50) Pageable pageable, OverallPlanCaseVo vo) {
        return Result.ok(service.findNotRegionCaseList(pageable, vo));
    }

    @ApiOperation(value = "查询总部指引未关闭方案明细")
    @GetMapping("findNotHeadCaseList")
    public Result<Page<OverallPlanCaseVo>> findNotHeadCaseList(@PageableDefault(50) Pageable pageable, OverallPlanCaseVo vo) {
        return Result.ok(service.findNotHeadCaseList(pageable, vo));
    }

}
