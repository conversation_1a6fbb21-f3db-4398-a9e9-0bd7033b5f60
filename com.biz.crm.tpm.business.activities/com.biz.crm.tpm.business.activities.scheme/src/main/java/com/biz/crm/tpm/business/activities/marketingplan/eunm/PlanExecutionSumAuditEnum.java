package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Author: yangrui
 * @Date: 2025-01-02 15:00
 */
@Getter
@RequiredArgsConstructor
public enum PlanExecutionSumAuditEnum {
    NOT_PASS(0, "不合格"),
    PASS(1, "合格"),
    ;
    private final Integer code;
    private final String desc;

    public static String getDesc(Integer code) {
        PlanExecutionSumAuditEnum auditEnum = Arrays.stream(PlanExecutionSumAuditEnum.values())
                .filter(e -> Objects.equals(e.getCode(), code)).findAny().orElse(null);
        if (Objects.isNull(auditEnum)) {
            return StrUtil.EMPTY;
        }
        return auditEnum.getDesc();
    }
}
