package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.constant.PoolReplenishmentConstant;
import com.biz.crm.dms.business.costpool.replenishment.sdk.dto.renyang.ReplenishmentPoolDto;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolOperationType;
import com.biz.crm.dms.business.costpool.replenishment.sdk.enums.ReplenishmentPoolTypeEnum;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.renyang.ReplenishmentPoolLockService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.service.renyang.ReplenishmentPoolVoService;
import com.biz.crm.dms.business.costpool.replenishment.sdk.vo.renyang.ReplenishmentPoolVo;
import com.biz.crm.dms.business.promotion.sdk.dto.DmsPromotionSaleLadderDto;
import com.biz.crm.dms.business.promotion.sdk.dto.DmsPromotionSalePolicyDto;
import com.biz.crm.dms.business.promotion.sdk.dto.DmsPromotionSaleProductDto;
import com.biz.crm.dms.business.promotion.sdk.dto.DmsPromotionSaleScopeDto;
import com.biz.crm.dms.business.promotion.sdk.enums.SalePolicyScopeEnum;
import com.biz.crm.dms.business.promotion.sdk.service.DmsPromotionSalePolicyVoService;
import com.biz.crm.dms.business.promotion.sdk.vo.DmsPromotionSalePolicyVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCaseExtend;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseExtendRepository;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description tpm方案明细操作组件
 * <AUTHOR>
 * @Date 2024/7/22 14:27
 */
@Slf4j
@Component
public class TpmMarketingPlanCaseComponent {

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;
    @Autowired(required = false)
    private MarketingPlanCaseExtendRepository marketingPlanCaseExtendRepository;
    @Autowired(required = false)
    private DmsPromotionSalePolicyVoService promotionSalePolicyVoService;
    @Autowired(required = false)
    private ReplenishmentPoolVoService replenishmentPoolVoService;
    @Autowired(required = false)
    private ReplenishmentPoolLockService replenishmentPoolLockService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private ProductVoService productVoService;
    @Resource
    private ProductPhaseVoService productPhaseVoService;

    /**
     * 默认零级编码
     */
    private final static String DEFAULT_ZERO_LEVEL_CODE = "QPX";

    /**
     * 活动明细操作---随单：推送政策，物料：货补池上账
     *
     * @param schemeDetailCodes 活动明细编码
     */
    public void operate(List<String> schemeDetailCodes, MarketingPlan entity, List<MarketingPlanCase> detailList) {
        log.info("---------活动明细操作开始---------\n活动明细编码：{}", JSON.toJSONString(schemeDetailCodes));
        if (CollectionUtils.isEmpty(schemeDetailCodes) && CollectionUtils.isEmpty(detailList)) {
            return;
        }
        // 根据活动明细编码查询明细
        if (CollectionUtils.isEmpty(detailList)) {
            detailList = this.marketingPlanCaseService.findListBySchemeDetailCodes(schemeDetailCodes);
        }
        // 根据方案明细类型分组
        Map<String, List<MarketingPlanCase>> group =
                detailList.stream().collect(Collectors.groupingBy(MarketingPlanCase::getCaseType));

        group.forEach((k, v) -> {
            MarketingPlanCaseTypeEnum typeEnum = MarketingPlanCaseTypeEnum.findByCode(k);
            if (Objects.isNull(typeEnum)) {
                log.info("活动明细操作时方案明细类型不存在：{}", k);
                return;
            } else {
                log.info("活动明细操作时方案明细类型：{}", typeEnum.getCode());
            }
            switch (typeEnum) {
                case matching_gift:
                    // 推送DMS政策
                    this.pushDmsPolicy(v, entity);
                    break;
                case material:
                    // 推送DMS货补池
                    this.pushDmsReplenishmentPool(v, entity);
                    break;
                default:
                    break;
            }
        });
        log.info("---------活动明细操作结束---------");
    }

    /**
     * 方案明细推送政策
     *
     * @param detailList 政策
     */
    private void pushDmsPolicy(List<MarketingPlanCase> detailList, MarketingPlan entity) {
        try {

            List<DmsPromotionSalePolicyDto> policyDtoList =
                    detailList.stream().map(v -> {
                        try {
                            DmsPromotionSalePolicyDto dto = this.buildPolicyDto(v);
                            dto.setOrgCode(entity.getOrgCode());
                            dto.setOrgName(entity.getOrgName());
                            dto.setPositionCode(entity.getPositionCode());
                            return dto;
                        } catch (Exception e) {
                            v.setPushStatus(BooleanEnum.FALSE.getCapital());
                            v.setPushMsg(e.getMessage());
                            log.error("构建促销政策失败：" + e.getMessage(), e);
                            return null;
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(policyDtoList)) {
                List<DmsPromotionSalePolicyVo> voList = this.promotionSalePolicyVoService.saveBatch(policyDtoList);
                Map<String, DmsPromotionSalePolicyVo> voMap = voList.stream().collect(Collectors.toMap(DmsPromotionSalePolicyVo::getActivityCode,
                        Function.identity(), (a, b) -> b));

                detailList.forEach(v -> {
                    DmsPromotionSalePolicyVo policyVo = voMap.get(v.getSchemeDetailCode());
                    if (Objects.isNull(policyVo)) {
                        v.setPushStatus(BooleanEnum.FALSE.getCapital());
                        v.setPushMsg("dms未返回数据，请联系管理员");
                    } else {
                        if (StringUtils.isNotBlank(policyVo.getErrorMsg())) {
                            v.setPushStatus(BooleanEnum.FALSE.getCapital());
                            v.setPushMsg(policyVo.getErrorMsg());
                        } else {
                            v.setDmsCode(policyVo.getSalePolicyCode());
                            v.setPushStatus(BooleanEnum.TRUE.getCapital());
                            v.setPushMsg("");
                        }
                    }
                });
            }

        } catch (Exception e) {
            log.error("方案明细推送政策错误：" + e.getMessage(), e);
            this.recordError(detailList, e.getMessage());
        }
        this.updateDetailList(detailList);
    }

    private DmsPromotionSalePolicyDto buildPolicyDto(MarketingPlanCase planCase) {
        DmsPromotionSalePolicyDto policyDto = new DmsPromotionSalePolicyDto();
        policyDto.setActivityCode(planCase.getSchemeDetailCode());
        policyDto.setSalePolicyName(planCase.getActName());
        policyDto.setSalePolicyType(SalePolicyScopeEnum.PROMOTION_TYPE_SCOPE.FULL_GIFT.getCode());
        if (BooleanEnum.TRUE.getCapital().equals(planCase.getIsContractCost())) {
            policyDto.setSourceType(SalePolicyScopeEnum.SOURCE_TYPE.CONTRACT.getCode());
        } else {
            policyDto.setSourceType(SalePolicyScopeEnum.SOURCE_TYPE.ACTIVITY.getCode());
        }
        policyDto.setValidStartTime(DateUtil.getDateByFormat(planCase.getStartDate(), DateUtil.DEFAULT_YEAR_MONTH_DAY));
        policyDto.setValidEndTime(DateUtil.getDateByFormat(planCase.getEndDate(), DateUtil.DEFAULT_YEAR_MONTH_DAY));
        policyDto.setRemark(planCase.getActDesc());
        if (Objects.nonNull(planCase.getDiscountAmount()) && planCase.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            policyDto.setIsDiscountLimit(BooleanEnum.TRUE.getCapital());
            policyDto.setDiscountLimitAmount(planCase.getDiscountAmount());
        }

        // 本品
        DmsPromotionSaleProductDto productDto = new DmsPromotionSaleProductDto();
        productDto.setSalePolicyLadderId(SalePolicyScopeEnum.DEFAULT_POLICY_LADDER_ID);
        Validate.isTrue(!CollectionUtils.isEmpty(planCase.getProductList()) || !CollectionUtils.isEmpty(planCase.getLevelList()), "本品和小类不能同时为空");
        MarketingPlanProduct marketingPlanProduct = null;
        if (!CollectionUtils.isEmpty(planCase.getLevelList())) {
            marketingPlanProduct = planCase.getLevelList().get(0);
            productDto.setProductType(SalePolicyScopeEnum.PRODUCT_TYPE_SCOPE.SMALL_CLASS.getCode());
        }
        if (!CollectionUtils.isEmpty(planCase.getProductList())) {
            marketingPlanProduct = planCase.getProductList().get(0);
            productDto.setProductType(SalePolicyScopeEnum.PRODUCT_TYPE_SCOPE.PRODUCT.getCode());
        }
        productDto.setProductCode(marketingPlanProduct.getCode());
        productDto.setMaterialCode(marketingPlanProduct.getMaterialCode());
        productDto.setProductName(marketingPlanProduct.getName());
        productDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        productDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        policyDto.setSaleProduct(productDto);

        // 阶梯
        DmsPromotionSaleLadderDto ladderDto = new DmsPromotionSaleLadderDto();
        ladderDto.setId(UuidCrmUtil.general());
        ladderDto.setIndexs(1);
        ladderDto.setSatisfyQuantity(new BigDecimal(planCase.getConditionNum()));
        ladderDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        ladderDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());

        // 赠品

        DmsPromotionSaleProductDto giftDto = new DmsPromotionSaleProductDto();
        MarketingPlanProduct giftProduct = null;
        Validate.isTrue(!CollectionUtils.isEmpty(planCase.getFeeLevelList()) || !CollectionUtils.isEmpty(planCase.getFeeProductList()), "赠品和赠品小类不能同时为空");
        if (!CollectionUtils.isEmpty(planCase.getFeeLevelList())) {
            giftProduct = planCase.getFeeLevelList().get(0);
            giftDto.setProductType(SalePolicyScopeEnum.PRODUCT_TYPE_SCOPE.GIFT_SMALL.getCode());
        }
        if (!CollectionUtils.isEmpty(planCase.getFeeProductList())) {
            giftProduct = planCase.getFeeProductList().get(0);
            giftDto.setProductType(SalePolicyScopeEnum.PRODUCT_TYPE_SCOPE.GIFT.getCode());
        }
        giftDto.setSalePolicyLadderId(ladderDto.getId());
        giftDto.setProductCode(giftProduct.getCode());
        giftDto.setMaterialCode(giftProduct.getMaterialCode());
        giftDto.setProductName(giftProduct.getName());
        giftDto.setGiftQuantity(new BigDecimal(planCase.getGiveNum()));
        giftDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        giftDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        ladderDto.setGiftProduct(giftDto);
        policyDto.setLadderList(Lists.newArrayList(ladderDto));

        // 范围
        DmsPromotionSaleScopeDto scopeDto = new DmsPromotionSaleScopeDto();
        scopeDto.setScopeName(planCase.getCustomerName());
        scopeDto.setScopeCode(planCase.getCustomerCode());
        scopeDto.setScopeType(SalePolicyScopeEnum.PROMOTION_SALE_SCOPE_TYPE.CUSTOMER.getCode());
        scopeDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        scopeDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        policyDto.setPromotionScopeList(Lists.newArrayList(scopeDto));

        policyDto.setPromotionScopeType(SalePolicyScopeEnum.PROMOTION_SALE_SCOPE_TYPE.CUSTOMER.getCode());
        policyDto.setDescription(String.format("买%s赠%s", ladderDto.getSatisfyQuantity(), giftDto.getGiftQuantity()));
        policyDto.setTenantCode(TenantUtils.getTenantCode());
        policyDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        policyDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        return policyDto;
    }

    /**
     * 方案明细推送货补池
     *
     * @param detailList 方案明细
     */
    private void pushDmsReplenishmentPool(List<MarketingPlanCase> detailList, MarketingPlan entity) {
        if (CollectionUtils.isEmpty(detailList) || Objects.isNull(entity)) {
            return;
        }
        // 过滤出已推送的
        List<MarketingPlanCase> filter =
                detailList.stream().filter(v -> !BooleanEnum.TRUE.getCapital().equals(v.getPushStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        boolean lock = false;
        // 锁的客户
        List<String> customerCodes = filter.stream().map(MarketingPlanCase::getCustomerCode).distinct().collect(Collectors.toList());
        try {
            lock = this.replenishmentPoolLockService.lock(customerCodes);
            Validate.isTrue(lock, "货补池正在被操作，请稍后再试");//根据品项查业态
            Set<String> itemCodes = filter.stream().map(MarketingPlanCase::getItemCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            Map<String, String> businessTypeMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(itemCodes)) {
                List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(itemCodes);
                businessTypeMap.putAll(productPhaseVos.stream().filter(e -> StringUtils.isNotBlank(e.getZeroLevelCode()))
                        .collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, ProductPhaseVo::getZeroLevelCode, (v1, v2) -> v1)));
            }
            for (MarketingPlanCase planCase : filter) {
                ReplenishmentPoolDto dto = this.buildReplenishmentPoolDto(planCase);
                if (StringUtils.isBlank(planCase.getItemCode())) {
                    dto.setBusinessType(DEFAULT_ZERO_LEVEL_CODE);
                } else {
                    dto.setBusinessType(businessTypeMap.getOrDefault(planCase.getItemCode(), DEFAULT_ZERO_LEVEL_CODE));
                }
                dto.setOrgCode(planCase.getBelongDepartmentCode());
                dto.setOrgName(planCase.getBelongDepartmentName());
                dto.setPositionCode(entity.getPositionCode());
                ReplenishmentPoolVo poolVo = this.replenishmentPoolVoService.handelAccount(dto);
                planCase.setPushStatus(BooleanEnum.TRUE.getCapital());
                planCase.setDmsCode(poolVo.getPoolCode());
            }
        } catch (Exception e) {
            log.error("方案明细推送货补池错误：" + e.getMessage(), e);
            this.recordError(filter, e.getMessage());
        } finally {
            if (lock && this.replenishmentPoolLockService.isLock(customerCodes)) {
                this.replenishmentPoolLockService.unLock(customerCodes);
            }
        }
        this.updateDetailList(filter);
    }

    private ReplenishmentPoolDto buildReplenishmentPoolDto(MarketingPlanCase planCase) {
        ReplenishmentPoolDto poolDto = new ReplenishmentPoolDto();
        poolDto.setReplenishmentPoolType(ReplenishmentPoolTypeEnum.MATERIAL.getDictCode());
        poolDto.setCustomerCode(planCase.getCustomerCode());
        poolDto.setCustomerName(planCase.getCustomerName());
        poolDto.setCompanyCode(planCase.getCompanyCode());
        poolDto.setProductGroupCode(planCase.getProductGroupCode());
        poolDto.setErpCode(planCase.getErpCode());
        poolDto.setChannelCode(planCase.getChannelCode());
        poolDto.setOperationType(ReplenishmentPoolOperationType.ACTIVITY_ACCOUNT.getCode());
        poolDto.setSellMaterialType(planCase.getSellMaterialType());
        poolDto.setFinanceIndexCode(planCase.getItemCode());
        poolDto.setFinanceIndexName(planCase.getItemName());
        poolDto.setTotalAmount(planCase.getApplyAmount());
        poolDto.setCusUndertakeAmount(planCase.getCusBearAmount());
        poolDto.setCostCenterCode(planCase.getCostCenterCode());
        poolDto.setCostCenterName(planCase.getCostCenterName());
        poolDto.setBusinessCode(planCase.getSchemeDetailCode());
        poolDto.setActivityType(planCase.getDetailName());
        // 标准物料
        if (StringUtils.equals(PoolReplenishmentConstant.STANDARD_SELL_MATERIAL_TYPE, planCase.getSellMaterialType())) {
            poolDto.setAccountAmount(planCase.getApplyAmount());
        } else {
            // 非标准物料
            poolDto.setAccountAmount(planCase.getMaterialNum());
            poolDto.setQuantity(planCase.getMaterialNum());
            poolDto.setProductCode(planCase.getMaterialCode());
            poolDto.setMaterialCode(planCase.getMaterialCode());
            poolDto.setProductName(planCase.getMaterialName());
        }

        return poolDto;
    }

    private void recordError(List<MarketingPlanCase> detailList, String message) {
        detailList.forEach(v -> {
            v.setPushStatus(BooleanEnum.FALSE.getCapital());
            v.setPushMsg(message);
        });
    }

    private void updateDetailList(List<MarketingPlanCase> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        Collection<MarketingPlanCaseExtend> marketingPlanCaseExtends =
                this.nebulaToolkitService.copyCollectionByBlankList(detailList, MarketingPlanCase.class,
                MarketingPlanCaseExtend.class, HashSet.class, ArrayList.class);
        this.marketingPlanCaseExtendRepository.updateBatchById(marketingPlanCaseExtends);
    }
}
