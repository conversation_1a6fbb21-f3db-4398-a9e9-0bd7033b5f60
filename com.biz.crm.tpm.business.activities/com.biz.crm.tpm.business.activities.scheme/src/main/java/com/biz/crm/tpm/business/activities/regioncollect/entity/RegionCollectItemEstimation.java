package com.biz.crm.tpm.business.activities.regioncollect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import com.biz.crm.tpm.business.activities.marketingplan.entity.CustomerGainsAndLosses;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_region_collect_item_estimation")
@Table(
        name = "tpm_region_collect_item_estimation",
        indexes = {
                @Index(name = "tpm_region_collect_item_estimation_index0", columnList = "collect_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_region_collect_item_estimation", comment = "大区汇总-品项测算")
@ApiModel(value = "RegionCollectItemEstimation", description = "大区汇总-品项测算")
public class RegionCollectItemEstimation extends CustomerGainsAndLosses {

    @ApiModelProperty("大区汇总编码")
    @Column(name = "collect_code", columnDefinition = "varchar(32) comment '大区汇总编码'")
    private String collectCode;

    @ApiModelProperty("品项编码")
    @Column(name = "item_code", columnDefinition = "varchar(32) comment '品项编码'")
    private String itemCode;

    @ApiModelProperty("品项名称")
    @Column(name = "item_name", columnDefinition = "varchar(64) comment '品项名称'")
    private String itemName;
}
