package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanBudget;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanBudgetRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanBudgetService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanBudgetVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/13 20:08
 */
@Service
@Slf4j
public class MarketingPlanBudgetServiceImpl implements MarketingPlanBudgetService {

    @Resource
    private MarketingPlanBudgetRepository planBudgetRepository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public void deleteBySchemeCode(String schemeCode) {
        planBudgetRepository.deleteBySchemeCode(schemeCode);
    }

    @Override
    public void saveBatchList(List<MarketingPlanBudget> list, String schemeCode) {
        planBudgetRepository.deleteBySchemeCode(schemeCode);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (MarketingPlanBudget budget : list) {
            budget.setSchemeCode(schemeCode);
            budget.setId(null);
        }
        planBudgetRepository.saveBatch(list);
    }


    @Override
    public List<MarketingPlanBudgetVo> findListBySchemeCode(String schemeCode) {
        List<MarketingPlanBudget> budgets = planBudgetRepository.findListBySchemeCode(schemeCode);
        if (CollectionUtils.isEmpty(budgets)) {
            return Lists.newArrayList();
        }
        return (List<MarketingPlanBudgetVo>) nebulaToolkitService.copyCollectionByWhiteList(budgets, MarketingPlanBudget.class, MarketingPlanBudgetVo.class, HashSet.class, ArrayList.class);
    }
}
