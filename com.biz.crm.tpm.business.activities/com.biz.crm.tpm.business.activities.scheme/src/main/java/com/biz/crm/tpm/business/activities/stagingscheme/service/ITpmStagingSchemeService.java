package com.biz.crm.tpm.business.activities.stagingscheme.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.biz.crm.tpm.business.activities.stagingscheme.entity.TpmStagingSchemeEntity;
import com.biz.crm.tpm.business.activities.stagingscheme.vo.TpmStagingSchemeVo;

import java.util.List;

/**
 * 方案相关暂存表接口
 *
 * <AUTHOR>
 * @date 2023-12-14 10:50:31
 */
public interface ITpmStagingSchemeService extends IService<TpmStagingSchemeEntity> {

    /**
     * 新增或者保存
     * @param reqVo
     */
   void saveOrUpdateStaging(TpmStagingSchemeVo reqVo);

    /**
     * 获取数据
     * @param <T>
     * @return
     */
   default <T> T getJsonStr(TpmStagingSchemeVo reqVo, Class<T> clazz){
       return null;
   }

    /**
     * 删除暂存数据
     * @param reqVo
     */
   void deleteStaging(TpmStagingSchemeVo reqVo);

    /**
     * 获取批量的数据
     * @param reqVo
     * @param clazz
     * @param <T>
     * @return
     */
   default <T> List<T> getJSONStrList(TpmStagingSchemeVo reqVo, Class<T> clazz){
       return null;
   }

   List<TpmStagingSchemeEntity> findListByCondition(String fromType,List<String> releaseIds);
}

