package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCaseExtend;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingCheckCaseComponent;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseExtendRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCaseStrategy;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/31 21:03
 */
@Component
@Slf4j
public class MarketingContractCalComponent {


    @Resource
    private MarketingPlanCaseService marketingPlanCaseService;

    @Resource
    private List<MarketingPlanCaseStrategy> strategyList;

    @Resource
    private MarketingPlanCaseRepository planCaseRepository;

    @Resource
    private MarketingPlanCaseExtendRepository extendRepository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;


    /**
     * 计算合同费用
     *
     * @param dataList
     * @param years
     */
    public void calContractApplyAmountByCaseList(List<MarketingPlanCase> dataList, String years) {
        List<String> departmentCodes = dataList.stream().map(MarketingPlanCase::getBelongDepartmentCode).distinct().collect(Collectors.toList());
        this.calContractApplyAmountByOrgCodes(departmentCodes, years);

    }


    @Resource
    private LoginUserService loginUserService;

    /**
     * 计算合同费用
     *
     * @param orgCodes
     * @param years
     */
    public void calContractApplyAmountByOrgCodes(List<String> orgCodes, String years) {
        UserIdentity loginDetails = this.loginUserService.getLoginDetails(UserIdentity.class);
        List<MarketingPlanCaseVo> caseVoList = marketingPlanCaseService.findContractByDepartmentCodesAndYears(orgCodes, years);
        if (CollectionUtils.isEmpty(caseVoList)) {
            return;
        }

        Map<String, List<MarketingPlanCaseVo>> caseMap = caseVoList.stream().collect(Collectors.groupingBy(MarketingPlanCaseVo::getCaseType));
        String cacheKey = UuidCrmUtil.general();
        List<Future<List<MarketingPlanCaseVo>>> futureList = Lists.newArrayList();
        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : caseMap.entrySet()) {
            MarketingCheckCaseComponent bean = ApplicationContextHolder.getContext().getBean(MarketingCheckCaseComponent.class);
            Future<List<MarketingPlanCaseVo>> future = bean.checkCaseList(entry.getValue(), entry.getKey(),
                    years, null, null, cacheKey, loginDetails, Collections.emptyList(), null);
            futureList.add(future);
//            //大方向校验
//            List<MarketingPlanCaseVo> caseVoList = planCaseService.checkPlanCaseList(entry.getValue(), vo.getYears());
//            //分模板校验
//            for (MarketingPlanCaseStrategy strategy : strategyList) {
//                if (strategy.getCaseType().equals(entry.getKey())) {
//                    strategy.checkCaseList(caseVoList, vo.getYears(), null, null, UuidCrmUtil.general());
//                }
//            }
        }
        List<MarketingPlanCaseVo> contractVoList = Lists.newArrayList();
        for (Future<List<MarketingPlanCaseVo>> future : futureList) {
            try {
                contractVoList.addAll(future.get());
            } catch (Exception e) {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                e.printStackTrace(new PrintStream(baos));
                String exception = baos.toString();
                log.error("计算合同数据失败:{}，错误堆栈:{}", e.getMessage(), exception);
                //计算失败
//                throw new RuntimeException("计算合同数据失败");
            }

        }


//        //大方向校验
//        List<MarketingPlanCaseVo> caseList = marketingPlanCaseService.checkPlanCaseList(caseVoList, years);
//        //明细校验
//        Map<String, List<MarketingPlanCaseVo>> map = caseList.stream().collect(Collectors.groupingBy(MarketingPlanCaseVo::getCaseType));
//        List<MarketingPlanCaseVo> contractVoList = Lists.newArrayList();
//        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : map.entrySet()) {
//            List<MarketingPlanCaseVo> list = entry.getValue();
//            for (MarketingPlanCaseStrategy strategy : strategyList) {
//                if (strategy.getCaseType().equals(entry.getKey())) {
//                    strategy.checkCaseList(list, years, null, null, UuidCrmUtil.general());
//                }
//            }
//            contractVoList.addAll(list);
//        }

        List<MarketingPlanCase> contractList = (List<MarketingPlanCase>) nebulaToolkitService.copyCollectionByWhiteList(contractVoList, MarketingPlanCaseVo.class,
                MarketingPlanCase.class, HashSet.class, ArrayList.class);
        planCaseRepository.updateBatchById(contractList);
        //保存扩展
        List<MarketingPlanCaseExtend> extendList = (List<MarketingPlanCaseExtend>) nebulaToolkitService.copyCollectionByWhiteList(contractList, MarketingPlanCase.class,
                MarketingPlanCaseExtend.class, HashSet.class, ArrayList.class);
        extendRepository.updateBatchById(extendList);
    }

}
