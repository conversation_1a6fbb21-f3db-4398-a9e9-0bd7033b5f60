package com.biz.crm.tpm.business.activities.marketingplan.service.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.BusinessRequiredUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.inquiry.sdk.service.InquiryVoService;
import com.biz.crm.mdm.business.inquiry.sdk.vo.InquiryVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.price.sdk.dto.FindPriceDto;
import com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanMapper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCaseStrategy;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.sdk.service.AlongWithOrderService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 搭赠模板
 * <AUTHOR>
 * @Date 2024/6/20 20:08
 */
@Component
@Slf4j
public class MatchingGiftCheckStrategyImpl implements MarketingPlanCaseStrategy<MarketingPlanCaseVo> {


    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Resource
    private ProductVoService productVoService;

    @Autowired(required = false)
    private InquiryVoService priceModelVoService;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private AlongWithOrderService alongWithOrderService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private MaterialVoService materialVoService;

    @Resource
    private MarketingPlanMapper marketingPlanMapper;

    private static final String CUSTOMER_TYPE = "Z009";

    private static final String TEN_CHANNEL = "10";

    private static final String TWENTY_CHANNEL = "20";

    //固定费用依据是进货
    private static final String FIX_COST_BASIS = "purchase";


    @Override
    public void checkCaseList(List<MarketingPlanCaseVo> list, String years, String schemeCode, String originalSchemeCode, String cacheKey) {
        //获取配置模板信息
        List<ActivitiesTemplateConfigDetailVo> templateFiledList = checkHelper.getTemplateFiled(MarketingPlanCaseTypeEnum.matching_gift.getCode());
        Map<String, Boolean> filedMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getRequired));
        Map<String, String> chineseMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getTitle));
        //获取活动细类配置
        Set<String> detailCodeSet = checkHelper.getTemplateRelateDetailCodes(MarketingPlanCaseTypeEnum.matching_gift.getCode());
        List<String> originalSchemeDetailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOriginalSchemeDetailCode()))
                .map(x -> x.getOriginalSchemeDetailCode()).collect(Collectors.toList());
        Map<String, MarketingPlanCaseVo> originCaseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(originalSchemeDetailCodes)) {
            List<MarketingPlanCaseVo> marketingPlanCaseVos = checkHelper.findCaseListBySchemeDetailCodes(originalSchemeDetailCodes);
            originCaseMap = marketingPlanCaseVos.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), Function.identity()));
        }

        //标记分组
        Map<String, Long> muchDepartmentMap = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMuchDepartmentMark()))
                .collect(Collectors.groupingBy(MarketingPlanCaseVo::getMuchDepartmentMark, Collectors.counting()));
        if (ObjectUtils.isNotEmpty(muchDepartmentMap)) {
            muchDepartmentMap = muchDepartmentMap.entrySet().stream()
                    .filter(entry -> entry.getValue() == 1)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        //查询组织的成本中心
        Map<String, Set<String>> orgCostCenterMap = checkHelper.findOrgCostCenter(list);
        Set<String> costCenterCodeSet = orgCostCenterMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        Set<String> yearSet = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getYears()))
                .map(MarketingPlanCaseVo::getYears).collect(Collectors.toSet());
        List<String> customerCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()))
                .map(MarketingPlanCaseVo::getCustomerCode).collect(Collectors.toList());
        // itemList 中itemCodes
        List<String> itemCodes = list.stream().filter(x -> CollectionUtils.isNotEmpty(x.getItemList())).map(k -> k.getItemList().get(0).getCode()).distinct().collect(Collectors.toList());
        List<MarketingSalesPlanVo> salesPlanVos = Lists.newArrayList();
        List<String> excludeSchemeCodes = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(schemeCode)) {
            excludeSchemeCodes.add(schemeCode);
        }
        if (ObjectUtils.isNotEmpty(originalSchemeCode)) {
            excludeSchemeCodes.add(originalSchemeCode);
        }
        String saleCacheKey = cacheKey + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + years;
        List<MarketingSalesPlanVo> salesPlanList1 = marketingSalesPlanService.findListByConditionToMarketingScheme(yearSet, costCenterCodeSet, Sets.newHashSet(customerCodes),
                null, null, excludeSchemeCodes, null, null);
        List<MarketingSalesPlanVo> salesPlanList2 = marketingSalesPlanService.findCacheList(saleCacheKey);
        if (CollectionUtils.isNotEmpty(salesPlanList1)) {
            salesPlanVos.addAll(salesPlanList1);
        }
        if (CollectionUtils.isNotEmpty(salesPlanList2)) {
            salesPlanVos.addAll(salesPlanList2);
        }
        Map<String, List<MarketingSalesPlanVo>> salesPlanMap = salesPlanVos.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getCostCenterCode()))
                .collect(Collectors.groupingBy(MarketingSalesPlanVo::getCostCenterCode));
        //查询客户
        List<CustomerVo> customerVoList = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerVoMap = customerVoList.stream().filter(x -> EnableStatusEnum.ENABLE.getCode().equals(x.getEnableStatus()) &&
                        BooleanEnum.TRUE.getCapital().equals(x.getContractCustomer()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
        Set<String> companyCodeSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(customerVoList)) {
            companyCodeSet = customerVoList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getCompanyCode()))
                    .map(CustomerVo::getCompanyCode).collect(Collectors.toSet());
        }
        List<String> productCodes = Lists.newArrayList();
        Set<String> levels = Sets.newHashSet();
        for (MarketingPlanCaseVo vo : list) {
            if (CollectionUtils.isNotEmpty(vo.getProductList())) {
                productCodes.addAll(vo.getProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));

                List<MarketingPlanProductVo> feeProductList = vo.getProductList().stream().map(x -> {
                    MarketingPlanProductVo productVo = nebulaToolkitService.copyObjectByWhiteList(x, MarketingPlanProductVo.class, HashSet.class, ArrayList.class);
                    productVo.setType(OverallPlanProductEnum.fee_product.getCode());
                    return productVo;
                }).collect(Collectors.toList());
                vo.setFeeProductList(feeProductList);
            }
            if (CollectionUtils.isNotEmpty(vo.getLevelList())) {
                levels.addAll(vo.getLevelList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toSet()));

                List<MarketingPlanProductVo> feeLevelList = vo.getLevelList().stream().map(x -> {
                    MarketingPlanProductVo productVo = nebulaToolkitService.copyObjectByWhiteList(x, MarketingPlanProductVo.class, HashSet.class, ArrayList.class);
                    productVo.setType(OverallPlanProductEnum.fee_level.getCode());
                    return productVo;
                }).collect(Collectors.toList());
                vo.setFeeLevelList(feeLevelList);
            }
//            if (CollectionUtils.isNotEmpty(vo.getFeeProductList())) {
//                productCodes.addAll(vo.getFeeProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
//                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));
//            }
//            if (CollectionUtils.isNotEmpty(vo.getFeeLevelList())) {
//                levels.addAll(vo.getFeeLevelList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
//                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toSet()));
//            }
        }
        //查询品项
        List<String> items = list.stream().filter(x -> CollectionUtils.isNotEmpty(x.getItemList()))
                .map(x -> x.getItemList()).flatMap(List::stream).filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                .map(x -> x.getCode()).collect(Collectors.toList());
        // 税率
        Map<String, ProductPhaseVo> productPhaseMap = checkHelper.findProductPhaseMap(itemCodes);
        Map<String, String> itemMap = checkHelper.findSapLevelMap(items);
        //查询商品
        Map<String, ProductVo> productMap = checkHelper.findProductListByCodes(productCodes, companyCodeSet, items);
        //查询小类
        Map<String, String> levelMap = checkHelper.findSmallLevel(levels);
        //查询小类对应的商品
        List<ProductVo> productVoList = productVoService.findByProductSmallClassCodes(Lists.newArrayList(levels));
        Map<String, List<ProductVo>> levelProductMap = productVoList.stream().collect(Collectors.groupingBy(ProductVo::getProductSmallClassCode));
        //查询物料成本
        Set<String> productCodeSet = Sets.newHashSet();
        productCodeSet.addAll(productMap.keySet());
        productCodeSet.addAll(levelProductMap.values().stream().flatMap(List::stream).map(ProductVo::getProductCode).collect(Collectors.toSet()));
        MaterialSearchDto searchDto = new MaterialSearchDto();
        searchDto.setMaterialCodeSet(productCodeSet);
        searchDto.setCompanyCodeSet(companyCodeSet);
        List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
        Map<String, BigDecimal> materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO)));

        Map<String, MarketingPlanCaseVo> replaceMap = Maps.newHashMap();
        for (MarketingPlanCaseVo caseVo : list) {
            caseVo.setCheckFlag(Boolean.TRUE);
            StringJoiner errMsg = new StringJoiner(";");
            //判断是变更的
            if (ObjectUtils.isNotEmpty(caseVo.getOriginalSchemeDetailCode())) {
                MarketingPlanCaseVo originCaseVo = originCaseMap.get(caseVo.getOriginalSchemeDetailCode());
                originCaseVo.setId(caseVo.getId());
                originCaseVo.setSchemeCode(caseVo.getSchemeCode());
                originCaseVo.setSchemeDetailCode(caseVo.getSchemeDetailCode());
                originCaseVo.setOriginalSchemeDetailCode(caseVo.getOriginalSchemeDetailCode());
                originCaseVo.setOriginalSchemeCode(caseVo.getOriginalSchemeCode());

                LocalDate now = LocalDate.now();
                LocalDate startDate = LocalDate.parse(originCaseVo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate endDate = LocalDate.parse(caseVo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                //如果开始时间晚于当前时间
                if (now.isBefore(startDate)) {
                    originCaseVo.setStartDate(caseVo.getStartDate());
                }
                //如果结束时间早于当前时间
                if (now.isAfter(endDate)) {
                    errMsg.add("结束时间不能早于当前时间");
                } else {
                    originCaseVo.setEndDate(caseVo.getEndDate());
                }
                List<String> codeList = Lists.newArrayList();
                //费用使用部门
                originCaseVo.setBelongDepartmentCode(caseVo.getBelongDepartmentCode());
                originCaseVo.setBelongDepartmentName(caseVo.getBelongDepartmentName());
                //优惠金额上限
                originCaseVo.setDiscountAmount(caseVo.getDiscountAmount());
                //费用依据
                originCaseVo.setCostBasisList(caseVo.getCostBasisList());
                originCaseVo.setCostBasis(caseVo.getCostBasis());
                //产品校验
                getCodeList(originCaseVo, codeList, errMsg, productMap, levelMap);
                String customerType = null;
                if (customerVoMap.containsKey(caseVo.getCustomerCode())) {
                    CustomerVo customerVo = customerVoMap.get(caseVo.getCustomerCode());
                    customerType = ObjectUtils.defaultIfNull(customerVo.getCustomerType(), null);
                }
                //费用计算
                this.calApplyAmount(originCaseVo, productMap, levelProductMap, orgCostCenterMap, salesPlanMap, materialMap, codeList, errMsg, customerType, productPhaseMap);
                //促销描述
                originCaseVo.setActDesc(caseVo.getActDesc());
                if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                    originCaseVo.setCheckFlag(Boolean.FALSE);
                    originCaseVo.setErrMsg(errMsg.toString());
                } else {
                    originCaseVo.setCheckFlag(Boolean.TRUE);
                }
                replaceMap.put(caseVo.getOriginalSchemeDetailCode(), originCaseVo);
                continue;
            }
            if (ObjectUtils.isNotEmpty(caseVo.getErrMsg())) {
                errMsg.add(caseVo.getErrMsg());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getDetailCode())) {
                if (!detailCodeSet.contains(caseVo.getDetailCode())) {
                    errMsg.add("费用项目不属于当前表单模板配置");
                }
            }
            String customerType = null;
            if (checkHelper.paramNotNull(caseVo.getCustomerCode(), "客户编码", errMsg)) {
                if (customerVoMap.containsKey(caseVo.getCustomerCode())) {
                    CustomerVo customerVo = customerVoMap.get(caseVo.getCustomerCode());
                    caseVo.setCustomerName(customerVo.getCustomerName());
                    caseVo.setErpCode(customerVo.getErpCode());
                    caseVo.setCompanyCode(customerVo.getCompanyCode());
                    caseVo.setProductGroupCode(customerVo.getProductGroupCode());
                    caseVo.setChannelCode(customerVo.getChannelCode());
                    caseVo.setCooperateType(customerVo.getCooperateType());
                    caseVo.setHzlx(customerVo.getHzlx());
                    customerType = ObjectUtils.defaultIfNull(customerVo.getCustomerType(), null);
                    //校验客户公司代码与成本中心公司代码是否一致
                    if (ObjectUtils.isNotEmpty(caseVo.getCostCenterCompanyCode()) && !customerVo.getCompanyCode().equals(caseVo.getCostCenterCompanyCode())) {
                        errMsg.add(String.format("客户公司代码与成本中心公司代码不一致,客户公司代码%s,成本中心公司代码%s", caseVo.getCompanyCode(), caseVo.getCostCenterCompanyCode()));
                    }
                } else {
                    errMsg.add(String.format("客户编码%s在主数据中未查询到或不是合同客户", caseVo.getCustomerCode()));
                }
            }
            if (checkHelper.collectionNotNull("品项", errMsg, caseVo.getItemList())) {
                for (MarketingPlanProductVo vo : caseVo.getItemList()) {
                    if (itemMap.containsKey(vo.getCode())) {
                        vo.setName(itemMap.get(vo.getCode()));
                    } else {
                        errMsg.add(String.format("品项%s在主数据中未查询到", vo.getCode()));
                    }
                }
            }
            List<String> codeList = Lists.newArrayList();
            //产品校验
            getCodeList(caseVo, codeList, errMsg, productMap, levelMap);
            if (MarketingPlanCaseCheckHelper.matchRules(caseVo.getConditionNum(), "本品数量", errMsg)) {
                MarketingPlanCaseCheckHelper.isMatch(caseVo.getConditionNum(), "本品数量", errMsg);
            }
            if (MarketingPlanCaseCheckHelper.matchRules(caseVo.getGiveNum(), "赠品数量", errMsg)) {
                MarketingPlanCaseCheckHelper.isMatch(caseVo.getGiveNum(), "赠品数量", errMsg);
            }
            //费用计算
            this.calApplyAmount(caseVo, productMap, levelProductMap, orgCostCenterMap, salesPlanMap, materialMap, codeList, errMsg, customerType, productPhaseMap);

            //设置固定的费用依据
            caseVo.setCostBasisList(Lists.newArrayList(FIX_COST_BASIS));
            caseVo.setCostBasis(FIX_COST_BASIS);
            caseVo.setMuchDepartmentFlag(BooleanEnum.FALSE.getCapital());
            //多部门标记
            if (ObjectUtils.isNotEmpty(caseVo.getMuchDepartmentMark())) {
                caseVo.setMuchDepartmentFlag(BooleanEnum.TRUE.getCapital());
            }
            if (ObjectUtils.isNotEmpty(muchDepartmentMap) && muchDepartmentMap.containsKey(caseVo.getMuchDepartmentMark())) {
                errMsg.add("多部门标记只找到一个,不可进行多部门标记");
            }
            //此处单独调用
            String msg = BusinessRequiredUtil.filedRequiredCheck(filedMap, chineseMap, caseVo);
            if (ObjectUtils.isNotEmpty(msg)) {
                errMsg.add(msg);
            }

            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                caseVo.setCheckFlag(Boolean.FALSE);
                caseVo.setErrMsg(errMsg.toString());
            }
            //寻年度预算
            checkHelper.matchBudget(filedMap, chineseMap, caseVo);
            if (caseVo.getCheckFlag()) {
                if (ObjectUtils.isEmpty(caseVo.getContractCode())) {
                    //计算是否满足随单比例
                    calAlongWithOrderRatio(caseVo, productMap, levelProductMap);
                }
            }
        }

        //进行替换操作
        if (ObjectUtils.isNotEmpty(replaceMap)) {
            list.replaceAll(x -> {
                String originalCode = x.getOriginalSchemeDetailCode();
                return (originalCode != null && !originalCode.isEmpty() && replaceMap.containsKey(originalCode))
                        ? replaceMap.get(originalCode)
                        : x;
            });
        }
        list.forEach(x -> x.setCaseType(getCaseType()));
        //校验多部门
        checkHelper.checkMuchDepartmentMark(list);
        //校验是否有重复维度的数据
        for (MarketingPlanCaseVo caseVo : list) {
//            caseVo.setIsContractCost(ObjectUtils.defaultIfNull(caseVo.getIsContractCost(), BooleanEnum.FALSE.getCapital()));
            if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                caseVo.setLevelProductCode(caseVo.getProductList().get(0).getCode());
            } else if (CollectionUtils.isNotEmpty(caseVo.getLevelList())) {
                caseVo.setLevelProductCode(caseVo.getLevelList().get(0).getCode());
            }
        }
        //校验是否有重复
        Map<String, List<MarketingPlanCaseVo>> cusOrgLevelMap = list.stream().filter(x -> x.getCheckFlag())
                .collect(Collectors.groupingBy(x -> x.getCustomerCode() +
                        x.getBelongDepartmentCode() + x.getLevelProductCode() + x.getIsContractCost()));
        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : cusOrgLevelMap.entrySet()) {
            List<MarketingPlanCaseVo> caseVoList = entry.getValue();
            if (hasDateOverlap(caseVoList)) {
                for (MarketingPlanCaseVo caseVo : caseVoList) {
                    StringJoiner errMsg = new StringJoiner(";");
                    if (ObjectUtils.isNotEmpty(caseVo.getErrMsg())) {
                        errMsg.add(caseVo.getErrMsg());
                    }
                    errMsg.add("客户+使用部门+产品/小类相同的情况下,活动时间不能交叉");
                    caseVo.setCheckFlag(Boolean.FALSE);
                    caseVo.setErrMsg(errMsg.toString());
                }
            } else {
                //这里再去数据库匹配一下
                List<String> cusCodes = caseVoList.stream().map(x -> x.getCustomerCode()).distinct().collect(Collectors.toList());
                List<String> belongDepartmentCodes = caseVoList.stream().map(x -> x.getBelongDepartmentCode()).distinct().collect(Collectors.toList());
                List<String> levelProductCodes = caseVoList.stream().map(x -> x.getLevelProductCode()).distinct().collect(Collectors.toList());
                List<String> isContractCosts = caseVoList.stream().map(x -> x.getIsContractCost()).distinct().collect(Collectors.toList());
                List<String> excelSchemeDetailCodes = Lists.newArrayList();

                excelSchemeDetailCodes.addAll(caseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getSchemeDetailCode()))
                        .map(x -> x.getSchemeDetailCode()).collect(Collectors.toList()));
                excelSchemeDetailCodes.addAll(caseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOriginalSchemeDetailCode()))
                        .map(x -> x.getOriginalSchemeDetailCode()).collect(Collectors.toList()));

                List<MarketingPlanCaseVo> caseVoList1 = marketingPlanMapper.findRepateCaseListByCondition(cusCodes, belongDepartmentCodes, levelProductCodes,
                        isContractCosts, excelSchemeDetailCodes,schemeCode,originalSchemeCode,getCaseType());
                if (CollectionUtils.isNotEmpty(caseVoList1)) {
                    for (MarketingPlanCaseVo a : caseVoList) {
                        List<MarketingPlanCaseVo> allList = Lists.newArrayList();
                        allList.addAll(caseVoList1);
                        if (!hasDateOverlapToDb(allList,a)) {
                            a.setCheckFlag(Boolean.TRUE);
                            a.setErrMsg(null);
                        }
                    }
                }
            }
        }
    }


    // 检查给定的计划列表是否有时间交叉
    private boolean hasDateOverlap(List<MarketingPlanCaseVo> plans) {
        // 按 startDate 排序
        plans.sort(Comparator.comparing(MarketingPlanCaseVo::getStartDate));

        // 检查相邻两个时间段是否有交叉
        for (int i = 0; i < plans.size() - 1; i++) {
            MarketingPlanCaseVo current = plans.get(i);
            MarketingPlanCaseVo next = plans.get(i + 1);
            LocalDate currentEndDate = LocalDate.parse(current.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate nextStartDate = LocalDate.parse(next.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            if (currentEndDate.isAfter(nextStartDate) || currentEndDate.isEqual(nextStartDate)) {
                return true; // 如果有交叉，则返回 true
            }
        }

        return false; // 没有交叉返回 false
    }


    // 检查给定的计划列表是否有时间交叉
    private boolean hasDateOverlapToDb(List<MarketingPlanCaseVo> plans, MarketingPlanCaseVo caseVo) {
        // 按 startDate 排序
        plans.sort(Comparator.comparing(MarketingPlanCaseVo::getStartDate));
        LocalDate currentStartDate = LocalDate.parse(caseVo.getStartDate(),DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        LocalDate currentEndDate = LocalDate.parse(caseVo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        for (MarketingPlanCaseVo next : plans) {
            LocalDate nextStartDate = LocalDate.parse(next.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate nextEndDate = LocalDate.parse(next.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            if ((currentEndDate.isAfter(nextStartDate) && currentStartDate.isBefore(nextStartDate)) || (currentEndDate.isAfter(nextEndDate) && currentStartDate.isBefore(nextEndDate))) {

                StringJoiner errMsg = new StringJoiner(";");
                if (ObjectUtils.isNotEmpty(caseVo.getErrMsg())) {
                    errMsg.add(caseVo.getErrMsg());
                }
                errMsg.add(String.format("客户+使用部门+产品/小类相同的情况下,活动时间不能交叉,重复活动方案编码%s",next.getSchemeCode()));
                caseVo.setCheckFlag(Boolean.FALSE);
                caseVo.setErrMsg(errMsg.toString());

                return true; // 如果有交叉，则返回 true
            }
        }
        return false; // 没有交叉返回 false
    }

    /**
     * 获取产品编码
     *
     * @param caseVo
     * @param codeList
     * @param errMsg
     * @param productMap
     * @param levelMap
     */
    public void getCodeList(MarketingPlanCaseVo caseVo, List<String> codeList, StringJoiner errMsg, Map<String, ProductVo> productMap, Map<String, String> levelMap) {
        checkHelper.collectionNotNullAndOnlyOne("促销本品和小类", errMsg, caseVo.getProductList(), caseVo.getLevelList());
        if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
            for (MarketingPlanProductVo vo : caseVo.getProductList()) {
                if (productMap.containsKey(vo.getCode())) {
                    ProductVo productVo = productMap.get(vo.getCode());
                    vo.setName(productVo.getProductName());
                    Validate.notNull(productVo.getMaterialCode(), String.format("商品%s未存在绑定的物料", productVo.getProductName()));
                    vo.setMaterialCode(productVo.getMaterialCode());
                    codeList.add(productVo.getProductCode());
                } else {
                    errMsg.add(String.format("促销商品%s在主数据中未查询到", vo.getCode()));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(caseVo.getLevelList())) {
            Set<String> smallLevelList = caseVo.getLevelList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toSet());
            Map<String, ProductVo> productVoMap = checkHelper.findProductListBySmallLevelCodes(smallLevelList);
            codeList.addAll(productVoMap.keySet());
            for (MarketingPlanProductVo vo : caseVo.getLevelList()) {
                if (levelMap.containsKey(vo.getCode())) {
                    vo.setName(levelMap.get(vo.getCode()));
                } else {
                    errMsg.add(String.format("促销小类%s在主数据中未查询到", vo.getCode()));
                }
            }
        }
        checkHelper.collectionNotNullAndOnlyOne("促销赠品和小类", errMsg, caseVo.getFeeProductList(), caseVo.getFeeLevelList());
        if (CollectionUtils.isNotEmpty(caseVo.getFeeProductList())) {
            for (MarketingPlanProductVo vo : caseVo.getFeeProductList()) {
                if (productMap.containsKey(vo.getCode())) {
                    ProductVo productVo = productMap.get(vo.getCode());
                    vo.setName(productVo.getProductName());
                    Validate.notNull(productVo.getMaterialCode(), String.format("商品%s未存在绑定的物料", productVo.getProductName()));
                    vo.setMaterialCode(productVo.getMaterialCode());
                } else {
                    errMsg.add(String.format("促销商品%s在主数据中未查询到", vo.getCode()));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(caseVo.getFeeLevelList())) {
            for (MarketingPlanProductVo vo : caseVo.getFeeLevelList()) {
                if (levelMap.containsKey(vo.getCode())) {
                    vo.setName(levelMap.get(vo.getCode()));
                } else {
                    errMsg.add(String.format("促销小类%s在主数据中未查询到", vo.getCode()));
                }
            }
        }

    }


    /**
     * 费用计算
     *
     * @param caseVo
     * @param orgCostCenterMap
     * @param salesPlanMap
     * @param codeList
     * @param errMsg
     * @param productPhaseMap
     */
    public void calApplyAmount(MarketingPlanCaseVo caseVo, Map<String, ProductVo> productVoMap, Map<String, List<ProductVo>> levelProductMap,
                               Map<String, Set<String>> orgCostCenterMap, Map<String, List<MarketingSalesPlanVo>> salesPlanMap, Map<String, BigDecimal> materialCostMap,
                               List<String> codeList, StringJoiner errMsg, String customerType, Map<String, ProductPhaseVo> productPhaseMap) {
        BigDecimal salesVolumeMinUnit = BigDecimal.ZERO;
        BigDecimal salesVolumeMaxUnit = BigDecimal.ZERO;
        caseVo.setEstimatedSalesVolume(null);
        String belongDepartmentCode = ObjectUtils.isNotEmpty(caseVo.getBelongDepartmentCode()) ? caseVo.getBelongDepartmentCode() : "";
        Set<String> costCenterCodes = orgCostCenterMap.getOrDefault(belongDepartmentCode, Sets.newHashSet());
        List<MarketingSalesPlanVo> salesPlanVoList = salesPlanMap.entrySet().stream().filter(x -> costCenterCodes.contains(x.getKey()))
                .flatMap(x -> x.getValue().stream())
                .filter(x -> StringUtil.isNotEmpty(x.getCustomerCode()))
                .filter(x -> StringUtil.isNotEmpty(x.getYears()))
                .filter(x -> StringUtil.isNotEmpty(x.getProductCode()))
                .filter(x -> x.getCustomerCode().equals(caseVo.getCustomerCode())
                        && x.getYears().equals(caseVo.getYears())
                        && codeList.contains(x.getProductCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(salesPlanVoList)) {
            salesVolumeMinUnit = salesPlanVoList.stream()
                    .map(x -> Optional.ofNullable(x.getEstimatedSalesVolume()).orElse(BigDecimal.ZERO)
                            .multiply(ObjectUtils.defaultIfNull(x.getConversionValue(), BigDecimal.ONE)))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            salesVolumeMaxUnit = salesPlanVoList.stream()
                    .map(x -> Optional.ofNullable(x.getEstimatedSalesVolume()).orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (salesVolumeMinUnit.compareTo(BigDecimal.ZERO) == 1) {
                caseVo.setEstimatedSalesVolume(salesVolumeMinUnit);
            }
        }
        if (ObjectUtils.isEmpty(caseVo.getContractCode()) &&
                !checkHelper.paramNotNull(caseVo.getEstimatedSalesVolume(), "预估销量(销售计划统计取值)", errMsg)) {
            caseVo.setEstimatedSalesVolume(BigDecimal.ZERO);
        }

        caseVo.setApplyAmount(BigDecimal.ZERO);
        //计算申请金额
        try {
            if ((CollectionUtils.isNotEmpty(caseVo.getFeeProductList()) || CollectionUtils.isNotEmpty(caseVo.getFeeLevelList()))
                    && ObjectUtils.isNotEmpty(caseVo.getConditionNum()) && ObjectUtils.isNotEmpty(caseVo.getGiveNum())
                    && ObjectUtils.isNotEmpty(caseVo.getCustomerCode())) {
                BigDecimal estimatedSalesVolume = ObjectUtils.defaultIfNull(caseVo.getEstimatedSalesVolume(), BigDecimal.ZERO);
                List<BigDecimal> conditionNumList = Lists.newArrayList();
                for (String s : caseVo.getConditionNum().split(",")) {
                    conditionNumList.add(new BigDecimal(s));
                }
                BigDecimal conditionNum = conditionNumList.stream().max(Comparator.naturalOrder()).get();
                List<BigDecimal> giveNumList = Lists.newArrayList();
                for (String s : caseVo.getGiveNum().split(",")) {
                    giveNumList.add(new BigDecimal(s));
                }
                BigDecimal giveNum = giveNumList.stream().max(Comparator.naturalOrder()).get();
                BigDecimal quantity = BigDecimal.ZERO;
                BigDecimal giftQuantity = BigDecimal.ZERO;
                if (Objects.nonNull(conditionNum)
                        && conditionNum.compareTo(BigDecimal.ZERO) != 0) {
                    quantity = estimatedSalesVolume.divide(conditionNum, 0, BigDecimal.ROUND_DOWN).multiply(giveNum);
                    giftQuantity = salesVolumeMaxUnit.divide(conditionNum, 0, BigDecimal.ROUND_DOWN).multiply(giveNum);
                }
                caseVo.setGiftQuantity(giftQuantity);
                //判断是费用赠品商品不为空
                if (CollectionUtils.isNotEmpty(caseVo.getFeeProductList())) {
                    List<String> productCodeList = caseVo.getFeeProductList().stream().map(MarketingPlanProductVo::getCode).distinct().collect(Collectors.toList());
                    FindPriceDto findPriceDto = new FindPriceDto();
                    findPriceDto.setUserType(FindPriceUserTypeEnum.CUSTOMER.getDictCode());
                    findPriceDto.setUserCode(caseVo.getCustomerCode());
                    findPriceDto.setProductCodeSet(Sets.newHashSet(productCodeList));
                    if (CUSTOMER_TYPE.equals(customerType)) {
                        findPriceDto.setChannelCode(TEN_CHANNEL);
                    } else {
                        findPriceDto.setChannelCode(TWENTY_CHANNEL);
                    }
                    Map<String, InquiryVo> priceMap = this.priceModelVoService.findPrice((JSONObject) JSONObject.toJSON(findPriceDto));
                    BigDecimal applyAmount = BigDecimal.ZERO;
                    BigDecimal noTaxApplyAmount = BigDecimal.ZERO;
                    BigDecimal taxRate = BigDecimal.ZERO;
                    BigDecimal materialCostPrice = BigDecimal.ZERO;
                    String policyProductCode = null;
                    if (ObjectUtils.isEmpty(priceMap)) {
                        errMsg.add("赠品未找到销售价格");
                    }
                    Map<String, String> productMap = productVoMap.values().stream()
                            .collect(Collectors.toMap(x -> x.getProductCode(),
                                    x -> Optional.ofNullable(x.getConversionValue()).orElse("1")));
                    Map<String, BigDecimal> productTaxMap = productVoMap.values().stream().collect(Collectors.toMap(x -> x.getProductCode(),
                            x -> Optional.ofNullable(x.getTaxRate()).orElse(BigDecimal.ZERO)));
                    if (ObjectUtils.isNotEmpty(priceMap)) {
                        Map<BigDecimal, String> priceMap1 = Maps.newHashMap();
                        for (Map.Entry<String, InquiryVo> entry : priceMap.entrySet()) {
//                            String conversionValueStr = productMap.get(entry.getKey());
//                            BigDecimal conversionValue = BigDecimal.ONE;
//                            if (ObjectUtils.isNotEmpty(conversionValueStr)) {
//                                conversionValue = new BigDecimal(conversionValueStr);
//                            }
//                            priceMap1.put(entry.getValue().getPrice().divide(conversionValue, 4, BigDecimal.ROUND_HALF_DOWN), entry.getKey());
                            priceMap1.put(entry.getValue().getPrice(), entry.getKey());
                        }
                        BigDecimal price = priceMap1.keySet().stream().max(BigDecimal::compareTo).get();
                        policyProductCode = priceMap1.get(price);
                        materialCostPrice = materialCostMap.getOrDefault(policyProductCode, BigDecimal.ZERO);
                        taxRate = productTaxMap.getOrDefault(policyProductCode, BigDecimal.ZERO);
//                        applyAmount = applyAmount.add(quantity.multiply(price));
                        applyAmount = applyAmount.add(giftQuantity.multiply(price));
                        noTaxApplyAmount = applyAmount.divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_DOWN);
                    }
                    caseVo.setPolicyProductCode(policyProductCode);
                    caseVo.setPolicyMaterialCostPrice(materialCostPrice);
                    caseVo.setApplyAmount(applyAmount);
                    caseVo.setNoTaxApplyAmount(noTaxApplyAmount);
                    caseVo.setTaxRate(taxRate);

                } else if (CollectionUtils.isNotEmpty(caseVo.getFeeLevelList())) {
                    List<String> levelCodes = caseVo.getFeeLevelList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toList());
                    List<ProductVo> productVoList = Lists.newArrayList();
                    for (String levelCode : levelCodes) {
                        if (levelProductMap.containsKey(levelCode)) {
                            productVoList.addAll(levelProductMap.get(levelCode));
                        }
                    }
                    Map<String, String> productConversionMap = productVoList.stream().collect(Collectors.toMap(ProductVo::getProductCode,
                            x -> Optional.ofNullable(x.getConversionValue()).orElse("1")));
                    Map<String, BigDecimal> productTaxMap = productVoList.stream().collect(Collectors.toMap(ProductVo::getProductCode,
                            x -> Optional.ofNullable(x.getTaxRate()).orElse(BigDecimal.ZERO)));
                    if (CollectionUtils.isNotEmpty(productVoList)) {
                        Set<String> productCodeSet = productVoList.stream().map(ProductVo::getProductCode).collect(Collectors.toSet());
                        FindPriceDto findPriceDto = new FindPriceDto();
                        findPriceDto.setUserType(FindPriceUserTypeEnum.CUSTOMER.getDictCode());
                        findPriceDto.setUserCode(caseVo.getCustomerCode());
                        findPriceDto.setProductCodeSet(productCodeSet);
                        Map<String, InquiryVo> priceMap = this.priceModelVoService.findPrice((JSONObject) JSONObject.toJSON(findPriceDto));
                        if (ObjectUtils.isEmpty(priceMap)) {
                            errMsg.add("赠品小类未找到销售价格");
                        }
                        BigDecimal applyAmount = BigDecimal.ZERO;
                        BigDecimal noTaxApplyAmount = BigDecimal.ZERO;
                        BigDecimal taxRate = BigDecimal.ZERO;
                        BigDecimal materialCostPrice = BigDecimal.ZERO;
                        String policyProductCode = null;
                        if (ObjectUtils.isNotEmpty(priceMap)) {
                            Map<BigDecimal, String> priceMap1 = Maps.newHashMap();
                            for (Map.Entry<String, InquiryVo> entry : priceMap.entrySet()) {
                                String conversionValueStr = productConversionMap.get(entry.getKey());
                                BigDecimal conversionValue = BigDecimal.ONE;
                                if (ObjectUtils.isNotEmpty(conversionValueStr)) {
                                    conversionValue = new BigDecimal(conversionValueStr);
                                }
                                priceMap1.put(entry.getValue().getPrice().divide(conversionValue, 4, BigDecimal.ROUND_HALF_DOWN), entry.getKey());
                            }
                            BigDecimal price = priceMap1.keySet().stream().max(BigDecimal::compareTo).get();
                            policyProductCode = priceMap1.get(price);
                            materialCostPrice = materialCostMap.getOrDefault(policyProductCode, BigDecimal.ZERO);
                            taxRate = productTaxMap.getOrDefault(policyProductCode, BigDecimal.ZERO);
                            applyAmount = applyAmount.add(quantity.multiply(price));
                            noTaxApplyAmount = applyAmount.divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_DOWN);
                        }
                        caseVo.setPolicyMaterialCostPrice(materialCostPrice);
                        caseVo.setPolicyProductCode(policyProductCode);
                        caseVo.setApplyAmount(applyAmount);
                        caseVo.setTaxRate(taxRate);
                        caseVo.setNoTaxApplyAmount(noTaxApplyAmount);
                    }
                }
                if (Objects.nonNull(caseVo.getApplyAmount()) && caseVo.getApplyAmount().compareTo(BigDecimal.ZERO) <= 0
                        && ObjectUtils.isEmpty(caseVo.getContractCode())) {
                    errMsg.add("申请金额必须大于0");
                }

                String code = caseVo.getItemList().get(0).getCode();
                log.info("expensePlanItemCode: {}", code);
                if (StringUtils.isNotEmpty(code) && Lists.newArrayList("BC1001", "BC1002").contains(code)) {
                    caseVo.setLineTaxRate(new BigDecimal("0.1"));
                    caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                } else {
                    ProductPhaseVo productPhaseVo = productPhaseMap.get(code);
                    if (Objects.isNull(productPhaseVo) || Objects.isNull(productPhaseVo.getTaxRate())) {
                        caseVo.setErrMsg("未匹配到对应品项或对应品项未维护税率");
                        caseVo.setCheckFlag(Boolean.FALSE);
                    } else {
                        caseVo.setLineTaxRate(productPhaseVo.getTaxRate());
                        caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                    }
                }

            }
            //计算费率
            checkHelper.calPlanCaseRatio(salesPlanMap, costCenterCodes, caseVo);
        } catch (NumberFormatException e) {
            log.error(e.getMessage(), e);
            errMsg.add("本品数量或特价金额不是数字");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            errMsg.add("计算费用合计失败");
        }
    }

    @Override
    public String getCaseType() {
        return MarketingPlanCaseTypeEnum.matching_gift.getCode();
    }


    /**
     * 计算随单比例
     *
     * @param vo
     */
    private void calAlongWithOrderRatio(MarketingPlanCaseVo vo, Map<String, ProductVo> productVoMap, Map<String, List<ProductVo>> levelProductMap) {
        StringJoiner errMsg = new StringJoiner(";");
        List<String> bpCodes = Lists.newArrayList();
        List<String> zpCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(vo.getProductList())) {
            bpCodes = vo.getProductList().stream().map(MarketingPlanProductVo::getCode)
                    .distinct().collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(vo.getLevelList())) {
            Set<String> levels = vo.getLevelList().stream().map(MarketingPlanProductVo::getCode)
                    .collect(Collectors.toSet());
            List<ProductVo> productVoList = Lists.newArrayList();
            for (String level : levels) {
                if (levelProductMap.containsKey(level)) {
                    productVoList.addAll(levelProductMap.get(level));
                }
            }
            bpCodes = productVoList.stream().map(x -> x.getProductCode()).distinct().collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(bpCodes)) {
            errMsg.add(String.format("本品在主数据中未找到"));
        }
        if (ObjectUtils.isNotEmpty(errMsg.toString())) {
            vo.setErrMsg(errMsg.toString());
            vo.setCheckFlag(Boolean.FALSE);
            return;
        }
        List<BigDecimal> conditionNumList = Lists.newArrayList();
        for (String s : vo.getConditionNum().split(",")) {
            conditionNumList.add(new BigDecimal(s));
        }
        BigDecimal conditionNum = conditionNumList.stream().max(Comparator.naturalOrder()).get();
        List<BigDecimal> giveNumList = Lists.newArrayList();
        for (String s : vo.getGiveNum().split(",")) {
            giveNumList.add(new BigDecimal(s));
        }
        BigDecimal giveNum = giveNumList.stream().max(Comparator.naturalOrder()).get();


        BigDecimal ratio = alongWithOrderService.getRatioByCondition(vo.getBelongDepartmentCode(), vo.getCustomerCode(), bpCodes, vo.getYears());
        if (ObjectUtils.isNotEmpty(ratio) && conditionNum.compareTo(BigDecimal.ZERO) == 1) {
            BigDecimal result = giveNum.divide(conditionNum, 6, BigDecimal.ROUND_HALF_DOWN);
            if (ratio.compareTo(result) < 0) {
                vo.setErrMsg("超出随单比例上限");
                vo.setCheckFlag(Boolean.FALSE);
            }
        }
    }
}
