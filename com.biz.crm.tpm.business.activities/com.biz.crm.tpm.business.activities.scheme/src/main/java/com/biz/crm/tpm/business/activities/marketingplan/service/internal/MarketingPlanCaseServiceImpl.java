package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.rocketmq.service.RocketMqProducer;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterOrgVo;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerDockingVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerRelateOrgVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalCostRecordLogDto;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.tpm.business.activities.marketingplan.constant.DictDataConstant;
import com.biz.crm.tpm.business.activities.marketingplan.constant.MarketingPlanConstant;
import com.biz.crm.tpm.business.activities.marketingplan.dto.MarketingPlanSecondCategoryDto;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.PlanClosureEnum;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlan;
import com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.marketingplan.entity.*;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanMaterialTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.repository.*;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.*;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanService;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.ExecuteStandVo;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ApprovalCollectType;
import com.biz.crm.tpm.business.budget.sdk.enums.SecondCategoryEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectImageVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/4 09:53
 */
@Service
@Slf4j
public class MarketingPlanCaseServiceImpl implements MarketingPlanCaseService {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MarketingPlanCaseRepository planCaseRepository;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private MarketingPlanProductRepository productRepository;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private MarketingPlanRepository marketingPlanRepository;

    @Autowired(required = false)
    private RedisService redisService;

    @Autowired(required = false)
    private MarketingPlanCaseCheckHelper checkHelper;

    @Autowired(required = false)
    private CostTypeDetailVoService costTypeDetailVoService;

    @Autowired(required = false)
    private RocketMqProducer rocketMqProducer;

    @Autowired(required = false)
    private MaterialDemandDetailService materialDemandDetailService;

    @Autowired(required = false)
    private MarketingPlanChangeSchemeCaseRepository changeSchemeCaseRepository;

    @Autowired(required = false)
    private OverallPlanService overallPlanService;

    @Autowired(required = false)
    private MarketingPlanCaseExtendRepository marketingPlanCaseExtendRepository;

    @Autowired(required = false)
    private TerminalVoService terminalVoService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired
    private OverallPlanMapper overallPlanMapper;


    @Autowired
    private PlanClosureRepository planClosureRepository;
    @Autowired
    private PlanClosureDetailRepository planClosureDetailRepository;

    @Autowired
    private ProductPhaseVoService productPhaseVoService;


    @Override
    public List<MarketingPlanCase> findListBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanCase> caseList = planCaseRepository.findListBySchemeCodes(schemeCodes);
        if (CollectionUtils.isNotEmpty(caseList)) {
            List<MarketingPlanProduct> planProductList = productRepository.findListBySchemeCodes(schemeCodes);
            if (CollectionUtils.isNotEmpty(planProductList)) {
                Map<String, List<MarketingPlanProduct>> planProductMap = planProductList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
                for (MarketingPlanCase planCase : caseList) {
                    planCase.setProductAndItemList(planProductMap.get(planCase.getSchemeDetailCode()));
                }
            }
        }
        return caseList;
    }


    @Override
    public List<MarketingPlanCase> findListBySchemeCodesAndDelFlag(List<String> schemeCodes, String delFlag) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanCase> caseList = planCaseRepository.findListBySchemeCodesAndDelFlag(schemeCodes, delFlag);
        if (CollectionUtils.isNotEmpty(caseList)) {
            List<MarketingPlanProduct> planProductList = productRepository.findListBySchemeCodes(schemeCodes);
            if (CollectionUtils.isNotEmpty(planProductList)) {
                Map<String, List<MarketingPlanProduct>> planProductMap = planProductList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
                for (MarketingPlanCase planCase : caseList) {
                    planCase.setProductAndItemList(planProductMap.get(planCase.getSchemeDetailCode()));
                }
            }
        }
        return caseList;
    }

    /**
     * 通过方案明细编码查询
     *
     * @param schemeDetailCodes
     * @return
     */
    @Override
    public List<MarketingPlanCase> findListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanCase> caseList = planCaseRepository.findListBySchemeDetailCode(schemeDetailCodes);
        if (CollectionUtils.isNotEmpty(caseList)) {
            List<MarketingPlanProduct> planProductList = productRepository.findListBySchemeDetailCodes(schemeDetailCodes);
            if (CollectionUtils.isNotEmpty(planProductList)) {
                Map<String, List<MarketingPlanProduct>> planProductMap = planProductList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
                for (MarketingPlanCase planCase : caseList) {
                    planCase.setProductAndItemList(planProductMap.get(planCase.getSchemeDetailCode()));
                }
            }
        }
        return caseList;
    }

    @Override
    public void saveBatchCaseList(List<MarketingPlanCaseVo> caseList, String schemeCode, Boolean changeFlag) {
        if (CollectionUtils.isEmpty(caseList)) {
            return;
        }
        //删除原数据
        planCaseRepository.deleteBySchemeCodes(Lists.newArrayList(schemeCode));
        productRepository.remove(Wrappers.lambdaQuery(MarketingPlanProduct.class)
                .eq(MarketingPlanProduct::getSchemeCode, schemeCode));

        List<String> ruleCodeList = Lists.newArrayList();
        if (changeFlag) {
            ruleCodeList = generateCodeService.generateCode(MarketingPlanConstant.CHANGE_SCHEME_DETAIL_CODE_RULE, caseList.size());
        } else {
            ruleCodeList = generateCodeService.generateCode(MarketingPlanConstant.SCHEME_DETAIL_CODE_RULE, caseList.size());
        }
        Map<String, List<MarketingPlanCaseVo>> muchDepartmentMap = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMuchDepartmentMark()))
                .collect(Collectors.groupingBy(x -> x.getCaseType() + x.getMuchDepartmentMark()));
        List<String> actExecuteCodeList = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(muchDepartmentMap)) {
            actExecuteCodeList = generateCodeService.generateCode(MarketingPlanConstant.SCHEME_ACT_EXECUTE_CODE_RULE, muchDepartmentMap.keySet().size());
        }
        Integer count = 0;
        Map<String, String> actExecuteCodeMap = Maps.newHashMap();
        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : muchDepartmentMap.entrySet()) {
            actExecuteCodeMap.put(entry.getKey(), actExecuteCodeList.get(count++));
        }
        Integer index = 0;
        List<MarketingPlanCase> entityList = Lists.newArrayList();
        List<MarketingPlanProduct> productList = Lists.newArrayList();

        //判断是物料类型 并且是非标准物料
        List<MaterialDemandDetailVo> detailVos = caseList.stream().filter(x -> MarketingPlanCaseTypeEnum.material.getCode().equals(x.getCaseType()) &&
                MarketingPlanMaterialTypeEnum.not_standard_material.getCode().equals(x.getSellMaterialType())).map(caseVo -> {
            MaterialDemandDetailVo detailVo = new MaterialDemandDetailVo();
            detailVo.setMaterialCode(caseVo.getMaterialCode());
            detailVo.setMaterialName(caseVo.getMaterialName());
            detailVo.setMaterialNum(caseVo.getMaterialNum());
            detailVo.setOrgCode(caseVo.getBelongDepartmentCode());
            detailVo.setOrgName(caseVo.getBelongDepartmentName());
            return detailVo;
        }).collect(Collectors.toList());
        //校验是否超数量
        materialDemandDetailService.checkMaterialDemandNum(detailVos, schemeCode);

        for (MarketingPlanCaseVo caseVo : caseList) {
            String schemeDetailCode = ruleCodeList.get(index++);
            String actExecuteCode = null;
            //判断是否多部门标记
            if (ObjectUtils.isNotEmpty(caseVo.getMuchDepartmentMark())) {
                String key = caseVo.getCaseType() + caseVo.getMuchDepartmentMark();
                actExecuteCode = actExecuteCodeMap.get(key);
            } else {
                actExecuteCode = schemeDetailCode;
            }

            if (CollectionUtils.isNotEmpty(caseVo.getCostBasisList())) {
                String costBasis = caseVo.getCostBasisList().stream().collect(Collectors.joining(","));
                caseVo.setCostBasis(costBasis);
            }
            if (CollectionUtils.isNotEmpty(caseVo.getExecuteExampleList())) {
                String executeExample = caseVo.getExecuteExampleList().stream().collect(Collectors.joining(","));
                caseVo.setExecuteExample(executeExample);
            }
            if (CollectionUtils.isNotEmpty(caseVo.getCloseCaseExampleList())) {
                String closeCaseExample = caseVo.getCloseCaseExampleList().stream().collect(Collectors.joining(","));
                caseVo.setCloseCaseExample(closeCaseExample);
            }
            MarketingPlanCase entity = nebulaToolkitService.copyObjectByWhiteList(caseVo, MarketingPlanCase.class, HashSet.class, ArrayList.class);
            entity.setSchemeDetailCode(schemeDetailCode);
            entity.setActExecuteCode(actExecuteCode);
            entity.setSchemeCode(schemeCode);
            entity.setId(null);
            entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            entityList.add(entity);
            if (CollectionUtils.isNotEmpty(caseVo.getProductAndItemList())) {
                List<MarketingPlanProduct> products = (List<MarketingPlanProduct>) nebulaToolkitService.copyCollectionByWhiteList(caseVo.getProductAndItemList(),
                        MarketingPlanProductVo.class, MarketingPlanProduct.class, HashSet.class, ArrayList.class);
                products.forEach(x -> {
                    x.setId(null);
                    x.setSchemeCode(schemeCode);
                    x.setSchemeDetailCode(schemeDetailCode);
                });
                productList.addAll(products);
            }

        }
        //保存方案明细
        planCaseRepository.saveBatchList(entityList);
        //保存产品
        if (CollectionUtils.isNotEmpty(productList)) {
            productRepository.saveBatch(productList);
        }
    }

    /**
     * 存放变更方案的方案明细
     *
     * @param cases
     * @param schemeCode
     */
    @Override
    public void saveBatchChangeCaseList(List<MarketingPlanCaseVo> cases, String schemeCode) {
        List<String> originalSchemeDetailCodes = cases.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOriginalSchemeDetailCode()))
                .map(MarketingPlanCaseVo::getOriginalSchemeDetailCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(originalSchemeDetailCodes)) {
            List<MarketingPlanChangeSchemeCase> changeSchemeCases = changeSchemeCaseRepository.findListByOriginalSchemeDetailCodes(originalSchemeDetailCodes);
            if (CollectionUtils.isNotEmpty(changeSchemeCases)) {
                List<String> changeSchemeDetailCodes = changeSchemeCases.stream().filter(x -> !schemeCode.equals(x.getSchemeCode()))
                        .map(MarketingPlanChangeSchemeCase::getOriginalSchemeDetailCode).collect(Collectors.toList());
                Validate.isTrue(CollectionUtils.isEmpty(changeSchemeDetailCodes), String.format("方案明细编码%s已被其他变更方案绑定,不可再进行变更", changeSchemeDetailCodes));
            }
            //保存到中间表
            changeSchemeCaseRepository.saveBatchList(originalSchemeDetailCodes, schemeCode);
        }
        this.saveBatchCaseList(cases, schemeCode, Boolean.TRUE);
    }

    @Override
    public void deleteBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return;
        }
        planCaseRepository.deleteBySchemeCodes(schemeCodes);
        productRepository.deleteBySchemeCodes(schemeCodes);
    }


    /**
     * 通过方案明细编码删除
     *
     * @param schemeDetailCodes
     */
    @Override
    public void deleteBySchemeDetailCodes(List<String> schemeDetailCodes) {
        planCaseRepository.deleteBySchemeDetailCodes(schemeDetailCodes);
        productRepository.deleteBySchemeDetailCodes(schemeDetailCodes);
    }

    /**
     * 查询已经承接的方案明细
     *
     * @param releaseDetailCodes
     * @return
     */
    @Override
    public List<MarketingPlanCase> findAlreadyBearCase(List<String> releaseDetailCodes) {
        if (CollectionUtils.isEmpty(releaseDetailCodes)) {
            return Lists.newArrayList();
        }
        return planCaseRepository.findAlreadyBearCase(releaseDetailCodes);
    }

    /**
     * 查询方案明细列表
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MarketingPlanCaseVo> findCaseList(Pageable pageable, MarketingPlanCaseVo vo, String processStatus) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<MarketingPlanCaseVo> data = planCaseRepository.findCaseList(page, vo, processStatus);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeDetailCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            //构建参数
            checkHelper.buildMarketingPlanCase(data.getRecords(), productMap);
        }
        return data;
    }


    /**
     * 查询变更方案明细
     *
     * @param pageable
     * @param vo
     * @param processStatus
     * @return
     */
    @Override
    public Page<MarketingPlanCaseVo> findCaseListByChangeScheme(Pageable pageable, MarketingPlanCaseVo vo, String processStatus,
                                                                List<String> originalSchemeDetailCodes) {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_SCHEME_CHANGE_TEMPLATE);
        List<String> schemeCaseList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dictDataVos)) {
            schemeCaseList = dictDataVos.stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
        }
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());

        Page<MarketingPlanCaseVo> data = planCaseRepository.findCaseListByChangeScheme(page, vo, processStatus, originalSchemeDetailCodes);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeDetailCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<String> withholdingCodes = planCaseRepository.findWithholdingBySchemeCodes(schemeDetailCodes);
            LocalDate now = LocalDate.now();
            for (MarketingPlanCaseVo record : data.getRecords()) {
                Boolean selectedFlag = Boolean.TRUE;
                //已做预提
                if (withholdingCodes.contains(record.getSchemeDetailCode())) {
                    selectedFlag = Boolean.FALSE;
                }
                //时间判断
                LocalDate endDate = LocalDate.parse(record.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                if (endDate.isBefore(now)) {
                    selectedFlag = Boolean.FALSE;
                }
                if (!schemeCaseList.contains(record.getCaseType())) {
                    selectedFlag = Boolean.FALSE;
                }
                record.setSelectedFlag(selectedFlag);
            }

            Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            //构建参数
            checkHelper.buildMarketingPlanCase(data.getRecords(), productMap);
        }
        return data;
    }

    /**
     * 查询预提结案
     *
     * @param pageable
     * @param vo
     * @param processStatus
     * @return
     */
    @Override
    public Page<MarketingPlanCaseVo> findCaseListByWithholdingEndCase(Pageable pageable, MarketingPlanCaseVo vo, String processStatus) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        StopWatch stopWatch=new StopWatch("查询预提结案");
        stopWatch.start("分页接口");
        if (StringUtils.isNotEmpty(vo.getSchemeDetailCodeListStr())) {
            List<String> schemeDetailCodes = Arrays.asList(vo.getSchemeDetailCodeListStr().split(","));
            if (CollectionUtils.isNotEmpty(schemeDetailCodes) && schemeDetailCodes.size() > 200) {
                throw new IllegalArgumentException("方案明细编码数量不能超过200个");
            }
            vo.setSchemeDetailCodeList(schemeDetailCodes);
//            page = new Page<>(0, schemeDetailCodes.size());
        }
        Page<MarketingPlanCaseVo> data = planCaseRepository.findCaseList(page, vo, processStatus);
        stopWatch.stop();
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeDetailCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<MarketingPlanCaseVo> auditData = planCaseRepository.findCaseListAuditData(schemeDetailCodes);
            data.getRecords().forEach(o->{
                 Optional<MarketingPlanCaseVo> first = auditData.stream().filter(e -> o.getSchemeDetailCode().equals(e.getSchemeDetailCode())).findFirst();
                 if(first.isPresent()){
                     o.setAuditAmount(first.get().getAuditAmount());
                 }
            });
            stopWatch.start("批量查询");
            Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            stopWatch.stop();
            //构建参数
            stopWatch.start("构建参数");
            checkHelper.buildMarketingPlanCaseAndCalRebate(data.getRecords(), productMap);
            stopWatch.stop();
        }
        log.info("查询预提结案耗时 {}",stopWatch.prettyPrint(TimeUnit.SECONDS));
        return data;
    }

    @Resource
    private PlanCaseUnderTakeComponent planCaseUnderTakeComponent;

    @Override
    public List<MarketingPlanCaseVo> checkPlanCaseList(List<MarketingPlanCaseVo> list, String years,  List<String> loginUserOneLevelOrgCodes, String loginUserName) {
//        List<String> categoryCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCategoryCode()))
//                .map(MarketingPlanCaseVo::getCategoryCode).distinct().collect(Collectors.toList());
        List<String> detailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDetailCode()))
                .map(MarketingPlanCaseVo::getDetailCode).distinct().collect(Collectors.toList());
        List<String> belongDepartmentCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .map(MarketingPlanCaseVo::getBelongDepartmentCode).distinct().collect(Collectors.toList());
        List<String> bearDepartmentCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBearDepartmentCode()))
                .map(MarketingPlanCaseVo::getBearDepartmentCode).distinct().collect(Collectors.toList());
        List<String> costCenterCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCostCenterCode()))
                .map(MarketingPlanCaseVo::getCostCenterCode).distinct().collect(Collectors.toList());
        List<String> regionCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlacingCity()))
                .map(MarketingPlanCaseVo::getPlacingCity).distinct().collect(Collectors.toList());
        List<String> customerCodes = list.stream().map(MarketingPlanCaseVo::getCustomerCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, CostTypeDetailVo> detailMap = checkHelper.findDetailMap(detailCodes);
        Map<String, CostTypeCategoryVo> categoryMap = Maps.newHashMap();
        if (ObjectUtils.isNotEmpty(detailMap)) {
            List<String> categoryCodes = detailMap.values().stream().map(x -> x.getCategoryCode()).collect(Collectors.toList());
            categoryMap = checkHelper.findCategoryMap(categoryCodes);
        }

        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);

        List<String> orgCodes = Lists.newArrayList();
        orgCodes.addAll(belongDepartmentCodes);
        orgCodes.addAll(bearDepartmentCodes);
        Map<String, Set<String>> orgCostCenterMap = checkHelper.findCostCenterByOrgCodes(bearDepartmentCodes);
        Map<String, OrgVo> orgMap = checkHelper.findOrgMap(orgCodes);
        Map<String, MdmCostCenterVo> costCenterMap = checkHelper.findCostCenterVoMap(costCenterCodes);
        if (costCenterMap != null) {
            orgCodes.addAll(costCenterMap.entrySet().stream()
                    .filter(x -> CollectionUtils.isNotEmpty(x.getValue().getOrgList()))
                    .flatMap(x -> x.getValue().getOrgList().stream())
                    .map(MdmCostCenterOrgVo::getOrgCode).distinct().collect(Collectors.toList()));
        }
        Map<String, Boolean> orgChildrenMap = checkHelper.findOrgChildrenOrg(orgCodes);
        Map<String, Set<String>> belongOrgCostCenterMap = checkHelper.findCostCenterByOrgCodes(belongDepartmentCodes);

        Map<String, String> regionMap = checkHelper.getAdministrativeArea(regionCodes);
        Map<String, List<CostTypeDetailCollectVo>> detailCollectMap = checkHelper.getDetailCollectByCodes(detailCodes);
        Map<String, Set<String>> payBysMap = checkHelper.findCostTypeDetailByDetailCodes(detailCodes);
        //承接数据
        List<String> releaseDetailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getReleaseDetailCode()))
                .map(MarketingPlanCaseVo::getReleaseDetailCode).distinct().collect(Collectors.toList());
        Map<String, OverallPlanCaseVo> bearCaseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(releaseDetailCodes)) {
            List<OverallPlanCaseVo> bearCaseList = overallPlanService.findOverallPlanCaseListBySchemeDetailCodes(releaseDetailCodes);
            bearCaseMap = bearCaseList.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), Function.identity()));
        }
        Map<String, BigDecimal> underTakeAmountMap = Maps.newHashMap();
        for (MarketingPlanCaseVo caseVo : list) {
            StringJoiner errMsg = new StringJoiner(";");
            caseVo.setYears(years);
            if (bearCaseMap.containsKey(caseVo.getReleaseDetailCode())) {
                OverallPlanCaseVo overallPlanCaseVo = bearCaseMap.get(caseVo.getReleaseDetailCode());
                caseVo.setBearCase(overallPlanCaseVo);
                caseVo.setReleaseName(overallPlanCaseVo.getSchemeName());
            }
            if (detailCollectMap.containsKey(caseVo.getDetailCode())) {
                Map<String, List<CostTypeDetailCollectVo>> collectMap = detailCollectMap.get(caseVo.getDetailCode()).stream()
                        .collect(Collectors.groupingBy(CostTypeDetailCollectVo::getType));
                caseVo.setCollectList(collectMap.getOrDefault(ApprovalCollectType.COLLECT.getCode(), Lists.newArrayList()));
                caseVo.setApprovalList(collectMap.getOrDefault(ApprovalCollectType.APPROVAL.getCode(), Lists.newArrayList()));
            }
            if (payBysMap.containsKey(caseVo.getDetailCode())) {
                caseVo.setPayBys(payBysMap.get(caseVo.getDetailCode()));
            }
            //校验必填字段
            this.checkMustNeedFiled(caseVo, errMsg, categoryMap, detailMap, orgMap, orgChildrenMap, costCenterMap, orgCostCenterMap, belongOrgCostCenterMap, years);
            //校验数据字典关系
            this.checkDictData(caseVo, errMsg, detailMap);

            log.info("checkPlanCaseList.loginUserOneLevelOrgCodes:{}-{}", loginUserOneLevelOrgCodes, loginUserName);
            if (CollectionUtils.isNotEmpty(loginUserOneLevelOrgCodes) && null != loginUserName) {
                List<String> cpLoginUserOneLevelOrgCodes = Lists.newArrayList(loginUserOneLevelOrgCodes);
                List<DictDataVo> collaborationUserList = dictDataVoService.findTreeByDictTypeCode(DictDataConstant.TPM_COLLABORATION_CUSTOMER);
                List<String> collaborationCustomerCodes = collaborationUserList.stream().map(DictDataVo::getDictValue).collect(Collectors.toList());
                if (!collaborationCustomerCodes.contains(caseVo.getCustomerCode())) {
                    Map<String, List<String>> dockingUserNameMap = customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, e -> CollectionUtils.isEmpty(e.getDockingList())? Lists.newArrayList(): e.getDockingList().stream().map(CustomerDockingVo::getUserName).collect(Collectors.toList())));

                    Map<String, List<String>> customerOrgMap = customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, e -> CollectionUtils.isEmpty(e.getOrgList())? Lists.newArrayList() : e.getOrgList().stream().map(CustomerRelateOrgVo::getOrgCode).collect(Collectors.toList())));

                    log.info("checkPlanCaseList.customerDockingMap: {}", JSON.toJSONString(dockingUserNameMap));
                    log.info("checkPlanCaseList.oneLevelOrgMap: {}", JSON.toJSONString(customerOrgMap));
                    this.checkCustomerDockingAndOrg(caseVo, errMsg, dockingUserNameMap, customerOrgMap, loginUserName, cpLoginUserOneLevelOrgCodes);
                }
            }
            //校验投放城市
            if (ObjectUtils.isNotEmpty(caseVo.getPlacingCity())) {
                if (regionMap.containsKey(caseVo.getPlacingCity())) {
                    caseVo.setPlacingCityName(regionMap.get(caseVo.getPlacingCity()));
                } else {
                    errMsg.add(String.format("投放城市编码%s错误", caseVo.getPlacingCity()));
                }
            }
            if (ObjectUtils.isEmpty(errMsg.toString())) {
                caseVo.setCheckFlag(Boolean.TRUE);
                caseVo.setErrMsg(null);
            } else {
                caseVo.setCheckFlag(Boolean.FALSE);
                caseVo.setErrMsg(errMsg.toString());
            }
            //判断是承接的 并且是没有错误数据的 再重新走一次承接验证
            if (ObjectUtils.isNotEmpty(caseVo.getReleaseDetailCode()) && caseVo.getCheckFlag()) {
                planCaseUnderTakeComponent.checkMarketingUnderTakeCase(caseVo, underTakeAmountMap);
            }
        }
        return list;
    }

    private void checkCustomerDockingAndOrg(MarketingPlanCaseVo caseVo, StringJoiner errMsg, Map<String, List<String>> customerDockingMap,
                                            Map<String, List<String>> customerOrgMap, String loginUserName, List<String> loginUserOneLevelOrgCodes) {
        String customerCode = caseVo.getCustomerCode();
        log.info("checkCustomerDockingAndOrg1.customerCode: {}, loginUserName: {}, loginUserOneLevelOrgCodes: {}", customerCode, loginUserName, loginUserOneLevelOrgCodes);
        boolean dockingFlag = false;
        boolean orgFlag = false;
        List<String> dockingUserNames = customerDockingMap.get(customerCode);
        if (CollectionUtils.isNotEmpty(dockingUserNames) && !dockingUserNames.contains(loginUserName)) {
            dockingFlag = true;
        }
        List<String> curOrgCodes = customerOrgMap.getOrDefault(customerCode, Lists.newArrayList());
        loginUserOneLevelOrgCodes.retainAll(curOrgCodes);
        if (CollectionUtils.isEmpty(loginUserOneLevelOrgCodes)) {
            orgFlag = true;
        }
        log.info("checkCustomerDockingAndOrg1.dockingUserNames: {}, oneLevelOrgCode: {}", dockingUserNames, loginUserOneLevelOrgCodes);
        log.info("checkCustomerDockingAndOrg1.flag: {}-{}", dockingFlag, orgFlag);
        if (dockingFlag && orgFlag) {
            errMsg.add("客户绑定的业务员不包含当前操作者，或客户不属于当前登录者组织及下级组织");
        }
    }


    /**
     * 通过id查询方案明细
     *
     * @param schemeId
     * @return
     */
    @Override
    public Map<String, List<MarketingPlanCaseVo>> findListBySchemeId(String schemeId, String customerName) {
        Map<String, List<MarketingPlanCaseVo>> map = null;
        MarketingPlan plan = marketingPlanRepository.queryById(schemeId);

        List<String> schemeCodes = Lists.newArrayList(plan.getSchemeCode());
        //方案明细
        List<MarketingPlanCase> caseList = planCaseRepository.findListBySchemeCodesAndCustomerName(schemeCodes, customerName);
        //产品明细
        List<MarketingPlanProduct> productAndItemList = productRepository.findListBySchemeCodes(schemeCodes);
        Map<String, List<MarketingPlanProduct>> productAndItemMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(productAndItemList)) {
            productAndItemMap = productAndItemList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
        }

        //查询承接的方案
        List<String> releaseDetailCodes = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getReleaseDetailCode()))
                .map(MarketingPlanCase::getReleaseDetailCode).distinct().collect(Collectors.toList());
        List<String> customerCodes = caseList.stream().map(MarketingPlanCase::getCustomerCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerMap = customerVos.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity(), (a, b) -> a));
        Map<String, OverallPlanCaseVo> bearCaseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(releaseDetailCodes)) {
            List<OverallPlanCaseVo> bearCaseList = overallPlanService.findOverallPlanCaseListBySchemeDetailCodes(releaseDetailCodes);
            bearCaseMap = bearCaseList.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), Function.identity()));
        }
        List<String> detailCodes = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDetailCode()))
                .map(MarketingPlanCase::getDetailCode).distinct().collect(Collectors.toList());
        Map<String, List<CostTypeDetailCollectVo>> detailCollectMap = checkHelper.getDetailCollectByCodes(detailCodes);
        Map<String, Set<String>> detailPayBysMap = checkHelper.findCostTypeDetailByDetailCodes(detailCodes);
        List<MarketingPlanCaseVo> caseVoList = Lists.newArrayList();
        for (MarketingPlanCase planCase : caseList) {
            MarketingPlanCaseVo caseVo = nebulaToolkitService.copyObjectByWhiteList(planCase, MarketingPlanCaseVo.class, HashSet.class, ArrayList.class);
            if (productAndItemMap.containsKey(caseVo.getSchemeDetailCode())) {
                List<MarketingPlanProductVo> productAndItemVos = (List<MarketingPlanProductVo>) nebulaToolkitService.copyCollectionByWhiteList(
                        productAndItemMap.get(caseVo.getSchemeDetailCode()), MarketingPlanProduct.class, MarketingPlanProductVo.class, HashSet.class, ArrayList.class);
                caseVo.setProductAndItemList(productAndItemVos);
            }
            if (bearCaseMap.containsKey(planCase.getReleaseDetailCode())) {
                caseVo.setBearCase(bearCaseMap.get(planCase.getReleaseDetailCode()));
            }
            if (detailCollectMap.containsKey(planCase.getDetailCode())) {
                Map<String, List<CostTypeDetailCollectVo>> collectMap = detailCollectMap.get(planCase.getDetailCode()).stream()
                        .collect(Collectors.groupingBy(CostTypeDetailCollectVo::getType));
                caseVo.setCollectList(collectMap.getOrDefault(ApprovalCollectType.COLLECT.getCode(), Lists.newArrayList()));
                caseVo.setApprovalList(collectMap.getOrDefault(ApprovalCollectType.APPROVAL.getCode(), Lists.newArrayList()));
            }
            if (detailPayBysMap.containsKey(planCase.getDetailCode())) {
                caseVo.setPayBys(detailPayBysMap.get(planCase.getDetailCode()));
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCostBasis())) {
                List<String> list = Arrays.asList(caseVo.getCostBasis().split(","));
                caseVo.setCostBasisList(list);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getExecuteExample())) {
                List<String> list = Arrays.asList(caseVo.getExecuteExample().split(","));
                caseVo.setExecuteExampleList(list);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCloseCaseExample())) {
                List<String> list = Arrays.asList(caseVo.getCloseCaseExample().split(","));
                caseVo.setCloseCaseExampleList(list);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCooperateTypeStr())) {
                List<String> cooperateTypeList = Arrays.asList(caseVo.getCooperateTypeStr().split(","));
                caseVo.setCooperateTypeList(cooperateTypeList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCustomerTagStr())) {
                List<String> customerTagList = Arrays.asList(caseVo.getCustomerTagStr().split(","));
                caseVo.setCustomerTagList(customerTagList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getTerminalTagStr())) {
                List<String> terminalTagList = Arrays.asList(caseVo.getTerminalTagStr().split(","));
                caseVo.setTerminalTagList(terminalTagList);
            }
            if (ObjectUtils.isNotEmpty(caseVo.getCustomerCode())) {
                CustomerVo customerVo = customerMap.get(caseVo.getCustomerCode());
                if (Objects.nonNull(customerVo)) {
                    caseVo.setSearchName(customerVo.getSearchName());
                }
            }
            caseVoList.add(caseVo);
        }
        map = caseVoList.stream().collect(Collectors.groupingBy(MarketingPlanCaseVo::getCaseType, Collectors.collectingAndThen(
                Collectors.toList(), list -> {
                    list.sort(
                            Comparator.comparing(MarketingPlanCaseVo::getSort,
                                            Comparator.nullsLast(Comparator.naturalOrder())).
                                    thenComparing(MarketingPlanCaseVo::getCheckFlag, Comparator.nullsLast(Comparator.naturalOrder()))
                    );
                    return list;
                }
        )));
        return map;
    }


    @Override
    public List<String> findReleaseDetailCodeBySchemeCodeAndProcessStatus(String schemeCode, String schemeId, String processStatus) {
        List<String> releaseDetailCodes = Lists.newArrayList();
        List<MarketingPlanCase> caseList = planCaseRepository.findReleaseDetailCaseList(schemeCode);
        if (CollectionUtils.isNotEmpty(caseList)) {
            releaseDetailCodes = caseList.stream().map(MarketingPlanCase::getReleaseDetailCode).distinct().collect(Collectors.toList());
        }
        return releaseDetailCodes;
    }

    @Override
    public MarketingPlanCaseVo findByCaseCode(String caseCode) {
        MarketingPlanCase planCase = planCaseRepository.findByCaseCode(caseCode);
        if (ObjectUtils.isEmpty(planCase)) {
            return null;
        }
        MarketingPlanCaseVo caseVo = nebulaToolkitService.copyObjectByWhiteList(planCase, MarketingPlanCaseVo.class, HashSet.class, ArrayList.class);
        List<String> schemeDetailCodes = Lists.newArrayList(caseVo.getSchemeDetailCode());
        Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
        List<MarketingPlanCaseVo> caseVoList = Lists.newArrayList(caseVo);
        //构建参数
        checkHelper.buildMarketingPlanCase(caseVoList, productMap);
        return caseVoList.get(0);
    }

    @Override
    public List<MarketingPlanCaseVo> findByCaseCodes(List<String> caseCodes) {
        List<MarketingPlanCase> list = planCaseRepository.findByCaseCodes(caseCodes);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanCaseVo> caseVoList = (List<MarketingPlanCaseVo>) nebulaToolkitService.copyCollectionByWhiteList(list, MarketingPlanCase.class, MarketingPlanCaseVo.class, LinkedHashSet.class, ArrayList.class);
        List<String> schemeDetailCodes = caseVoList.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
        //构建参数
        checkHelper.buildMarketingPlanCase(caseVoList, productMap);
        return caseVoList;
    }

    /**
     * 推送活动明细到SFA
     *
     * @param schemeDetailCodeList
     */
    @Override
    public void pushCaseListToSfa(List<String> schemeDetailCodeList) {
//        List<CostTypeDetailVo> detailVoList = costTypeDetailVoService.findListByPushSfa();
//        if (CollectionUtils.isEmpty(detailVoList)) {
//            return;
//        }
//        List<String> detailCodes = detailVoList.stream().map(CostTypeDetailVo::getDetailCode).distinct().collect(Collectors.toList());
//        //查询需要推送SFA活动的方案明细
//        List<MarketingPlanCase> caseList = planCaseRepository.findPushActListBySchemeCodeAndDetailCodes(schemeDetailCodeList, detailCodes);
//        if (CollectionUtils.isEmpty(caseList)) {
//            return;
//        }
//        //此处的作用时方案变更
//        for (MarketingPlanCase planCase : caseList) {
//            if (ObjectUtils.isNotEmpty(planCase.getOriginalSchemeDetailCode())) {
//                planCase.setSchemeDetailCode(planCase.getOriginalSchemeDetailCode());
//            }
//        }
//        //MQ发送消息
//        MqMessageVo mqMessageVo = new MqMessageVo();
//        mqMessageVo.setTopic(MqConstant.TPM_ACT_TOPIC_ORDER);
//        mqMessageVo.setRepeatConsumer(true);
//        mqMessageVo.setTag(MqConstant.TPM_ACT_PUSH_SFA_TAG);
//        mqMessageVo.setMsgBody(JSON.toJSONString(caseList));
//        mqMessageVo.setMsgNum(caseList.size());
//        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_DATE_PATTERN));
//        this.rocketMqProducer.sendMqOrderMsg(mqMessageVo, StringUtils.join(schemeDetailCodeList, date));

    }

    /**
     * 回写结案状态或者计提
     *
     * @param caseVos
     */
    @Override
    public void rewriteEndCaseOrWithholding(List<MarketingPlanCaseVo> caseVos) {
        for (MarketingPlanCaseVo caseVo : caseVos) {
            Validate.notNull(caseVo.getSchemeDetailCode(), "方案明细编码不能为空");
            Validate.isTrue(ObjectUtils.isNotEmpty(caseVo.getAuditStatus()) || ObjectUtils.isNotEmpty(caseVo.getAuditAmount()) ||
                    ObjectUtils.isNotEmpty(caseVo.getCashStatus()) || ObjectUtils.isNotEmpty(caseVo.getCashAmount()), "结案或兑付信息不能都为空");
        }
        planCaseRepository.rewriteEndCaseOrWithholding(caseVos);
        marketingPlanCaseExtendRepository.rewriteEndCaseOrWithholding(caseVos);
    }


    /**
     * 查询物料模板的数据-通过物料编码+部门+非标准物料
     *
     * @param materialCodes
     * @param orgCodes
     * @return
     */
    @Override
    public List<MarketingPlanCaseVo> findMaterialListByMaterialCodesAndOrgCodes(List<String> materialCodes, List<String> orgCodes) {
        return planCaseRepository.findMaterialListByMaterialCodesAndOrgCodes(materialCodes, orgCodes);
    }

    /**
     * 通过物料+组织+排除方案编码查询使用
     *
     * @param materialCodes
     * @param orgCodes
     * @param schemeCode
     * @return
     */
    @Override
    public List<MarketingPlanCaseVo> findMaterialListByMaterialCodesAndOrgCodesAndExclusiveSchemeCode(List<String> materialCodes, List<String> orgCodes, String schemeCode) {
        return planCaseRepository.findMaterialListByMaterialCodesAndOrgCodesAndExclusiveSchemeCode(materialCodes, orgCodes, schemeCode);
    }

    /**
     * 查询关联的统筹方案明细的计提、结案、兑付金额
     *
     * @param releaseDetailCodes
     * @return
     */
    @Override
    public List<MarketingPlanCaseVo> findAuditAndWithholdingAndCashReleaseDetailCodeList(List<String> releaseDetailCodes, String bearFlag) {
        if (CollectionUtils.isEmpty(releaseDetailCodes)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanCaseVo> dataList = planCaseRepository.findAuditAndWithholdingAndCashReleaseDetailCodeList(releaseDetailCodes, bearFlag);
        return dataList;
    }

    /**
     * 大区查询方案承接明细
     *
     * @param pageable
     * @param releaseDetailCode
     * @return
     */
    @Override
    public Page<MarketingPlanCaseVo> findRegionMarketingPlanCase(Pageable pageable, String releaseDetailCode) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<MarketingPlanCaseVo> data = planCaseRepository.findRegionMarketingPlanCase(page, releaseDetailCode);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeDetailCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            List<PlanClosureDetail> planClosureDetails =  planClosureDetailRepository.findBySchemeDetailCodeList(schemeDetailCodes);
            List<String> closeCodes = planClosureDetails.stream().map(PlanClosureDetail::getCloseCode).distinct().collect(Collectors.toList());
            List<PlanClosure> planClosures = planClosureRepository.findByCloseCodeList(closeCodes);
            Map<String, PlanClosureDetail> closureDetailMap = planClosureDetails.stream().collect(Collectors.toMap(PlanClosureDetail::getSchemeDetailCode, Function.identity(), (v1, v2) -> v1));
            Map<String, PlanClosure> closureMap = planClosures.stream().collect(Collectors.toMap(PlanClosure::getCloseCode, Function.identity(), (v1, v2) -> v1));
            //构建参数
            checkHelper.buildMarketingPlanCase(data.getRecords(), productMap);
            data.getRecords().forEach(e -> {
                PlanClosureDetail planClosureDetail = closureDetailMap.get(e.getSchemeDetailCode());
                if (null != planClosureDetail) {
                    String closeCode = planClosureDetail.getCloseCode();
                    e.setCloseCode(closeCode);
                    PlanClosure planClosure = closureMap.get(closeCode);
                    if (null != planClosure) {
                        e.setCloseProcessStatus(planClosure.getProcessStatus());
                    }
                }
            });
        }
        return data;
    }

    @Override
    public List<MarketingPlanCaseVo> findRegionMarketingPlanCase(List<String> releaseDetailCodes) {
        if(CollectionUtils.isEmpty(releaseDetailCodes)){
            return Lists.newArrayList();
        }
        return planCaseRepository.findRegionMarketingPlanCases(releaseDetailCodes);
    }

    /**
     * 查询活动明细报表
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MarketingPlanCaseVo> findMarketingPlanCaseReportList(Pageable pageable, MarketingPlanCaseVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        // 根据关联总部方案名称获取关联总部方案编码集合
        if(StringUtils.isNotBlank(vo.getHeadSchemeName())) {
            List<String> headSchemaCodes  = overallPlanMapper.findBySchemeCodesByName(vo.getHeadSchemeName());
            if (CollectionUtils.isEmpty(headSchemaCodes)) {
                page.setRecords(new ArrayList<>());
                return   page;
            }
            vo.setHeadSchemeCodes(headSchemaCodes.stream().distinct().collect(Collectors.toList()));
        }
        Page<MarketingPlanCaseVo> data = planCaseRepository.findMarketingPlanCaseReportList(page, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeDetailCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            List<String> headSchemeCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getHeadSchemeCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(headSchemeCodes)) {
                List<OverallPlan> overallPlans =  overallPlanMapper.findBySchemeCodes(headSchemeCodes);
                Map<String, OverallPlan> overallPlanMap = overallPlans.stream().collect(Collectors.toMap(OverallPlan::getSchemeCode, Function.identity(), (v1, v2) -> v1));
                data.getRecords().forEach(e -> {
                    String headSchemeCode = e.getHeadSchemeCode();
                    if (StringUtils.isNotEmpty(headSchemeCode)) {
                        OverallPlan overallPlan = overallPlanMap.get(headSchemeCode);
                        if (null != overallPlan) {
                            e.setHeadSchemeName(overallPlan.getSchemeName());
                        }
                    }
                });
            }
            //构建参数
            checkHelper.buildMarketingPlanCase(data.getRecords(), productMap);
        }
        return data;
    }


    /**
     * 查询未关闭的方案明细列表
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MarketingPlanCaseVo> findNotMarketingClosedCaseList(Pageable pageable, MarketingPlanCaseQueryDto vo) {
        vo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        vo.setLessStartDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY)));
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<MarketingPlanCaseVo> data = planCaseRepository.findNotMarketingClosedCaseList(page, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeDetailCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            //构建参数
            checkHelper.buildMarketingPlanCase(data.getRecords(), productMap);
        }
        return data;
    }

    @Override
    public Page<MarketingPlanCaseVo> findSafActPolicy(Pageable pageable, MarketingPlanCaseQueryDto vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.planCaseRepository.findSafActPolicy(page, vo);
    }

    /**
     * 修改方案明细状态
     *
     * @param schemeDetailCodes
     * @param delFlag
     */
    @Override
    public void updateCaseStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String delFlag) {
        planCaseRepository.updateCaseStatusBySchemeDetailCodes(schemeDetailCodes, delFlag);
    }


    /**
     * 通过公式查询
     *
     * @param formulaCode
     * @return
     */
    @Override
    public List<MarketingPlanCase> findListByFormulaCode(String formulaCode) {
        return planCaseRepository.findListByFormulaCode(formulaCode);
    }


    @Override
    public List<MarketingPlanCaseVo> findListByDetailCodes(List<String> detailCodes) {
        List<MarketingPlanCase> caseList = planCaseRepository.findListByDetailCodes(detailCodes);
        if (CollectionUtils.isEmpty(caseList)) {
            return Lists.newArrayList();
        }
        return (List<MarketingPlanCaseVo>) nebulaToolkitService.copyCollectionByWhiteList(caseList, MarketingPlanCase.class, MarketingPlanCaseVo.class,
                HashSet.class, ArrayList.class);
    }

    @Override
    public Page<MarketingPlanCaseExecuteVo> findMarketingPlanCaseExecuteList(Pageable pageable, MarketingPlanCaseQueryDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        dto = ObjectUtils.defaultIfNull(dto, new MarketingPlanCaseQueryDto());
        if (StringUtil.isEmpty(dto.getExecutionType())) {
            return new Page<>();
        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<MarketingPlanCaseExecuteVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        List<String> sendSfaCodeList = this.planCaseRepository.findSendSfaCostTypeDetail(dto.getExecutionType().substring(0, dto.getExecutionType().indexOf("_")));
        if (CollectionUtil.isEmpty(sendSfaCodeList)) {
            return new Page<>();
        }
        dto.setDetailCodeList(sendSfaCodeList);
        Page<MarketingPlanCaseExecuteVo> voPage = this.planCaseRepository.findMarketingPlanCaseExecuteList(page, dto);
        if (CollectionUtil.isEmpty(voPage.getRecords())) {
            return voPage;
        }
        List<String> schemeDetailCodeList = voPage.getRecords().stream()
                .filter(k -> StringUtil.isNotEmpty(k.getSchemeDetailCode()))
                .map(MarketingPlanCaseExecuteVo::getSchemeDetailCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(schemeDetailCodeList)) {
            return voPage;
        }
        List<MarketingPlanCaseExtend> planCaseExtendList = marketingPlanCaseExtendRepository.findBySchemeDetailCodes(schemeDetailCodeList);
        if (CollectionUtil.isEmpty(planCaseExtendList)) {
            return voPage;
        }
        Map<String, MarketingPlanCaseExtend> planCaseExtendMap = planCaseExtendList
                .stream().filter(k -> StringUtil.isNotEmpty(k.getSchemeDetailCode()))
                .collect(Collectors.toMap(MarketingPlanCaseExtend::getSchemeDetailCode, v -> v, (n, o) -> n));
        //最终活动细类编码集合
        List<String> detailCodeList = voPage.getRecords().stream().map(MarketingPlanCaseExecuteVo::getDetailCode).filter(code -> StringUtils.isNotBlank(code)).distinct().collect(Collectors.toList());
        List<CostTypeDetailCollectVo> collectCostTypeDetail = this.planCaseRepository.findCollectCostTypeDetail(detailCodeList);
        Map<String, List<CostTypeDetailCollectVo>> collectVoMap = collectCostTypeDetail.stream().collect(Collectors.groupingBy(CostTypeDetailCollectVo::getDetailCode));
        //采集示例编码集合
        List<String> collectCodeList = collectCostTypeDetail.stream().map(CostTypeDetailCollectVo::getCollectCode).filter(code -> StringUtils.isNotBlank(code)).distinct().collect(Collectors.toList());
        //执行图片集合
        List<ApprovalCollectImageVo> collectImage = this.planCaseRepository.findCollectImage(collectCodeList);
        Map<String, List<ApprovalCollectImageVo>> imageMap = collectImage.stream().collect(Collectors.groupingBy(ApprovalCollectImageVo::getApprovalCollectCode));

        voPage.getRecords().forEach(item -> {
            MarketingPlanCaseExtend planCaseExtend = planCaseExtendMap.get(item.getSchemeDetailCode());
            if (Objects.isNull(planCaseExtend)) {
                return;
            }
            item.setExecuteDesc(planCaseExtend.getExecuteDesc());
            item.setEstimatedSalesVolume(planCaseExtend.getEstimatedSalesVolume());

            //示例照片
            if (collectVoMap.containsKey(item.getDetailCode())) {
                List<CostTypeDetailCollectVo> detailCollectVos = collectVoMap.get(item.getDetailCode());
                List<ExecuteStandVo> executeStandVos = new ArrayList<>();
                for (CostTypeDetailCollectVo detailCollectVo : detailCollectVos) {
                    if (imageMap.containsKey(detailCollectVo.getCollectCode())) {
                        ExecuteStandVo executeStandVo = new ExecuteStandVo();
                        executeStandVo.setCollectCode(detailCollectVo.getCollectCode());
                        executeStandVo.setCollectName(detailCollectVo.getCollectName());
                        executeStandVo.setImages(imageMap.get(detailCollectVo.getCollectCode()));
                        executeStandVos.add(executeStandVo);
                    }
                }
                item.setExecuteStandList(executeStandVos);
            }
        });
        return voPage;
    }

    @Override
    public List<MarketingPlanCaseExecuteVo> findByTerminalMarketingPlanCaseExecuteList(MarketingPlanCaseQueryDto dto) {
        dto = ObjectUtils.defaultIfNull(dto, new MarketingPlanCaseQueryDto());
        if (StringUtil.isEmpty(dto.getExecutionType())) {
            return Lists.newArrayList();
        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        List<String> sendSfaCodeList = this.planCaseRepository.findSendSfaCostTypeDetail(dto.getExecutionType().substring(0, dto.getExecutionType().indexOf("_")));
        if (CollectionUtil.isEmpty(sendSfaCodeList)) {
            return Lists.newArrayList();
        }
        dto.setDetailCodeList(sendSfaCodeList);
        List<MarketingPlanCaseExecuteVo> executeList = this.planCaseRepository.findByTerminalMarketingPlanCaseExecuteList(dto);
        if (CollectionUtil.isEmpty(executeList)) {
            return Lists.newArrayList();
        }
        return executeList;
    }

    @Override
    public List<TerminalExpenseVo> findTerminalExpense(MarketingPlanCaseQueryDto vo) {
        Validate.notBlank(vo.getTerminalCode(), "终端编码不能为空");
        if (StringUtils.isEmpty(vo.getStartDate())
                && StringUtils.isEmpty(vo.getEndDate())) {
            Validate.notBlank(vo.getYears(), "查询年度不能为空");
        }
        return this.planCaseRepository.findTerminalExpense(vo);
    }


    @Override
    public List<TerminalExpenseVo> findCustomerExpense(MarketingPlanCaseQueryDto vo) {
        Validate.notBlank(vo.getCustomerCode(), "客户编码不能为空");
        Validate.isTrue(!StringUtils.isAllBlank(vo.getYears(), vo.getYear()), "查询月度和年度不能同时为空");
        return this.planCaseRepository.findCustomerExpense(vo);
    }


    /**
     * 查询通过部门+年月查询合同的方案明细
     *
     * @param departmentCodes
     * @param years
     * @return
     */
    @Override
    public List<MarketingPlanCaseVo> findContractByDepartmentCodesAndYears(List<String> departmentCodes, String years) {
        List<MarketingPlanCaseVo> caseVoList = planCaseRepository.findContractByDepartmentCodesAndYears(departmentCodes, years);
        if (CollectionUtils.isEmpty(caseVoList)) {
            return Lists.newArrayList();
        }
        List<String> schemeDetailCodes = caseVoList.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
        //构建参数
        checkHelper.buildMarketingPlanCase(caseVoList, productMap);
        return caseVoList;
    }

    @Override
    public boolean findExistByContractCodes(List<String> contractCodes) {
        Integer count = this.planCaseRepository.findExistByContractCodes(contractCodes);
        return count > 0;
    }


    /**
     * 查询已承接金额
     *
     * @param releaseDetailCodes
     * @return
     */
    @Override
    public Map<String, BigDecimal> findAlreadyUndertakeAmount(List<String> releaseDetailCodes) {
        if (CollectionUtils.isEmpty(releaseDetailCodes)) {
            return Maps.newHashMap();
        }
        List<MarketingPlanCaseVo> list = planCaseRepository.findAlreadyUndertakeAmount(releaseDetailCodes);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.groupingBy(MarketingPlanCaseVo::getReleaseDetailCode,
                    Collectors.mapping(MarketingPlanCaseVo::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        return Maps.newHashMap();
    }

    @Override
    public List<MarketingPlanCaseVo> findChanging(List<String> schemeDetailCodes) {
        return planCaseRepository.findChanging(schemeDetailCodes);
    }

    @Override
    public List<MarketingPlanCaseVo> findBySchemeCode(String schemeCode) {
        if (StringUtils.isBlank(schemeCode)) {
            return Lists.newArrayList();
        }
        return this.findBySchemeCodes(Collections.singletonList(schemeCode));
    }

    @Override
    public List<MarketingPlanCaseVo> findByCollectCode(String collectCode) {
        if (StringUtils.isBlank(collectCode)) {
            return Lists.newArrayList();
        }
        return this.planCaseRepository.findByCollectCode(collectCode);
    }

    @Override
    public List<MarketingPlanCaseVo> findBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return Lists.newArrayList();
        }
        return this.planCaseRepository.findBySchemeCodes(schemeCodes);
    }

    @Override
    public Page<MarketingPlanCaseVo> findExpensesCountByConditions(Pageable pageable, MarketingPlanCaseQueryDto vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (StringUtils.isAnyBlank(vo.getStartDate(), vo.getEndDate())) {
            //日期过滤条件不满足条件时默认查询本月数据
            String years = DateUtil.dateToStr(new Date(), DateUtil.date_yyyy_MM);
            vo.setStartDate(years);
            vo.setEndDate(years);
        }
        return this.planCaseRepository.findExpensesCountByConditions(pageable, vo);
    }

    @Override
    @Transactional
    public void createOrUpdateTerminalCostRecordLog(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            endDate = new Date();
            startDate = DateUtil.ReturnTheDay(endDate, -2);
        }
        List<String> terminalCodes = this.planCaseRepository.findExpenseChangeTerminal(startDate, endDate);
        if (CollectionUtils.isEmpty(terminalCodes)) {
            return;
        }
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(startDate);
        String year = String.valueOf(calendar.get(Calendar.YEAR));
        List<MarketingPlanCase> marketingPlanCaseList = this.planCaseRepository.findListByTerminalCodes(terminalCodes, year);
        //含有费用的终端map
        Map<String, List<MarketingPlanCase>> planCaseMap = marketingPlanCaseList.stream().collect(Collectors.groupingBy(MarketingPlanCase::getTerminalCode));
        //TODO 本次更新的终端费用投入记录表的终端：terminalCodes，含有费用的将更新具体月份的情况，无费用的将默认全设置为该年度无费用N
        List<TerminalCostRecordLogDto> logList = new ArrayList<>();
        for (String terminalCode : terminalCodes) {
            TerminalCostRecordLogDto dto = new TerminalCostRecordLogDto();
            dto.setTerminalCode(terminalCode);
            dto.setYears(year);
            if (planCaseMap.containsKey(terminalCode)) {
                List<MarketingPlanCase> planCaseList = planCaseMap.get(terminalCode);
                //包含年月集合 01-12
                List<String> yearsList = planCaseList.stream().map(MarketingPlanCase::getYears).filter(StringUtil::isNotBlank).map(years -> {
                    return years.substring(years.length() - 2);
                }).distinct().collect(Collectors.toList());

                //各年月判断
                if (yearsList.contains("01")) {
                    dto.setJanuary("Y");
                } else {
                    dto.setJanuary("N");

                }
                if (yearsList.contains("02")) {
                    dto.setFebruary("Y");
                } else {
                    dto.setFebruary("N");

                }
                if (yearsList.contains("03")) {
                    dto.setMarch("Y");
                } else {
                    dto.setMarch("N");

                }
                if (yearsList.contains("04")) {
                    dto.setApril("Y");
                } else {
                    dto.setApril("N");

                }
                if (yearsList.contains("05")) {
                    dto.setMay("Y");
                } else {
                    dto.setMay("N");

                }
                if (yearsList.contains("06")) {
                    dto.setJune("Y");
                } else {
                    dto.setJune("N");

                }
                if (yearsList.contains("07")) {
                    dto.setJuly("Y");
                } else {
                    dto.setJuly("N");

                }
                if (yearsList.contains("08")) {
                    dto.setAugust("Y");
                } else {
                    dto.setAugust("N");

                }
                if (yearsList.contains("09")) {
                    dto.setSeptember("Y");
                } else {
                    dto.setSeptember("N");

                }
                if (yearsList.contains("10")) {
                    dto.setOctober("Y");
                } else {
                    dto.setOctober("N");

                }
                if (yearsList.contains("11")) {
                    dto.setNovember("Y");
                } else {
                    dto.setNovember("N");

                }
                if (yearsList.contains("12")) {
                    dto.setDecember("Y");
                } else {
                    dto.setDecember("N");

                }

                //季度判断
                if ("Y".equals(dto.getJanuary()) || "Y".equals(dto.getFebruary()) || "Y".equals(dto.getMarch())) {
                    dto.setQuarterOne("Y");
                } else {
                    dto.setQuarterOne("N");
                }
                if ("Y".equals(dto.getApril()) || "Y".equals(dto.getMay()) || "Y".equals(dto.getJune())) {
                    dto.setQuarterTwo("Y");
                } else {
                    dto.setQuarterTwo("N");
                }
                if ("Y".equals(dto.getJuly()) || "Y".equals(dto.getAugust()) || "Y".equals(dto.getSeptember())) {
                    dto.setQuarterThree("Y");
                } else {
                    dto.setQuarterThree("N");
                }
                if ("Y".equals(dto.getOctober()) || "Y".equals(dto.getNovember()) || "Y".equals(dto.getDecember())) {
                    dto.setQuarterFour("Y");
                } else {
                    dto.setQuarterFour("N");
                }

                //年度判断
                if ("Y".equals(dto.getQuarterOne()) || "Y".equals(dto.getQuarterTwo()) || "Y".equals(dto.getQuarterThree()) || "Y".equals(dto.getQuarterFour())) {
                    dto.setTotalYear("Y");
                } else {
                    dto.setTotalYear("N");
                }

            } else {
                //各个月
                dto.setJanuary("N");
                dto.setFebruary("N");
                dto.setMarch("N");

                dto.setApril("N");
                dto.setMay("N");
                dto.setJune("N");

                dto.setJuly("N");
                dto.setAugust("N");
                dto.setSeptember("N");

                dto.setOctober("N");
                dto.setNovember("N");
                dto.setDecember("N");

                //季度
                dto.setQuarterOne("N");
                dto.setQuarterTwo("N");
                dto.setQuarterThree("N");
                dto.setQuarterFour("N");

                //年
                dto.setTotalYear("N");
            }
            logList.add(dto);
        }
        this.terminalVoService.createOrUpdateTerminalCostRecordLog(logList);
    }

    @Override
    public void updateWithHoldingStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String withHoldingStatus) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return;
        }
        List<List<String>> partitionList = Lists.partition(schemeDetailCodes, 800);
        partitionList.forEach(e -> marketingPlanCaseExtendRepository.updateWithHoldingStatusBySchemeDetailCodes(e, withHoldingStatus));
    }


    /**
     * 通过客户编码+费用使用部门
     *
     * @param customerCodes
     * @param departmentCodes
     * @return
     */
    @Override
    public List<MarketingPlanCase> findListByCustomerCodesAndBelongDepartmentCodes(List<String> customerCodes, List<String> departmentCodes, String schemeCode, String years) {
        return marketingPlanRepository.findListByCustomerCodesAndBelongDepartmentCodes(customerCodes, departmentCodes, schemeCode, years);
    }

    /**
     * 小程序查询客户的费用总览
     *
     * @param vo
     * @return
     */
    @Override
    public List<MarketingPlanCaseVo> findMiniCostTotalView(MarketingPlanCaseVo vo) {
        return marketingPlanRepository.findMiniCostTotalView(vo);
    }


    @Override
    public Set<String> findCustomerCodeListBySchemeCode(String schemeCode) {
        List<MarketingPlanCase> caseList = planCaseRepository.findCustomerCodeListBySchemeCode(schemeCode);
        return caseList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toSet());
    }


    @Override
    public Page<MarketingPlanCaseVo> findPlanCaseListBySecondCategory(Pageable pageable, MarketingPlanSecondCategoryDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(dto.getSecondCategory(), "二级费用大类编码不能为空");
        List<String> categoryCodes = planCaseRepository.findCategoryCodeBySecondCategory(dto.getSecondCategory());
        dto.setCategoryCodeList(categoryCodes);
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return planCaseRepository.findPlanCaseListBySecondCategory(page, dto);
    }


    @Override
    public List<String> findCountTerminalByTerminalCodesAndYears(List<String> terminalCodeList, String years) {
        List<List<String>> partitionLsit = Lists.partition(terminalCodeList, 800);
        Set<String> terminalCodeSet = Sets.newHashSet();
        for (List<String> list : partitionLsit) {
            List<String> terminalCodes = planCaseRepository.findCountTerminalByTerminalCodesAndYears(list, years);
            if (CollectionUtils.isNotEmpty(terminalCodes)) {
                terminalCodeSet.addAll(terminalCodes);
            }
        }

        return Lists.newArrayList(terminalCodeSet);
    }

    @Resource
    private OrgVoService orgVoService;

    @Override
    public Map<String, BigDecimal> findAmountByOrgCodesAndYearsList(WithholdingIncomeQueryDto dto) {
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCodes(dto.getOrgCodes());
        if (CollectionUtils.isEmpty(orgVoList)) {
            return Collections.emptyMap();
        }
        List<String> orgCodes = orgVoList.stream().map(x -> x.getOrgCode()).distinct().collect(Collectors.toList());
        List<List<String>> partitionList = Lists.partition(orgCodes, 800);
        List<MarketingPlanCaseVo> caseList = Lists.newArrayList();
        for (List<String> s : partitionList) {
            List<MarketingPlanCaseVo> caseVos = planCaseRepository.findAmountByOrgCodesAndYearsList(s, dto.getYearsList());
            if (CollectionUtils.isNotEmpty(caseVos)) {
                caseList.addAll(caseVos);
            }
        }
        if (CollectionUtils.isNotEmpty(caseList)) {
            return caseList.stream().collect(Collectors.groupingBy(x -> x.getYears(),
                    Collectors.mapping(x -> ObjectUtils.defaultIfNull(x.getNoTaxApplyAmount(), x.getApplyAmount()),
                            Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        return Collections.emptyMap();
    }


    @Override
    public List<MarketingPlanCaseVo> findCaseListByDmsMiniHomePage(String customerCode) {
        List<String> yearsList = Lists.newArrayList();
        LocalDate now = LocalDate.now();
        LocalDate nextMonth = now.plusMonths(1);
        String years = now.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        String nextYears = nextMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        yearsList.add(years);
        yearsList.add(nextYears);
        List<MarketingPlanCaseVo> dataList = planCaseRepository.findCaseListByDmsMiniHomePage(customerCode, yearsList);
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (MarketingPlanCaseVo vo : dataList) {
                vo.setCaseTypeName(MarketingPlanCaseTypeEnum.findByCode(vo.getCaseType()).getDesc());
            }
        }
        return dataList;
    }


    @Override
    public MarketingPlanCaseVo dmsQueryCaseDetailsBySchemeDetailCode(String schemeDetailCode) {
        List<MarketingPlanCase> caseList = planCaseRepository.findListBySchemeDetailCode(Lists.newArrayList(schemeDetailCode));
        if (CollectionUtils.isNotEmpty(caseList)) {
            MarketingPlanCaseVo caseVo = nebulaToolkitService.copyObjectByBlankList(caseList.get(0), MarketingPlanCaseVo.class, HashSet.class, ArrayList.class);
            LocalDate currentDate = LocalDate.now();
            LocalDate startDate = LocalDate.parse(caseVo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate endDate = LocalDate.parse(caseVo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));

            boolean isWithinRange = (currentDate.isEqual(startDate) || currentDate.isAfter(startDate))
                    && (currentDate.isEqual(endDate) || currentDate.isBefore(endDate));
            if (isWithinRange) {
                caseVo.setActStatus(PlanClosureEnum.in_progress.getCode());
            } else if (currentDate.isBefore(startDate)) {
                caseVo.setActStatus(PlanClosureEnum.not_started.getCode());
            } else if (currentDate.isAfter(endDate)) {
                caseVo.setActStatus(PlanClosureEnum.ended.getCode());
            }
            List<String> schemeDetailCodes = Lists.newArrayList(schemeDetailCode);
            Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            //构建参数
            caseVo.setProductAndItemList(productMap.getOrDefault(schemeDetailCode, Lists.newArrayList()));
            //修改是否已读状态
            planCaseRepository.updateCaseDmsReadFlag(caseVo.getSchemeDetailCode(), BooleanEnum.TRUE.getCapital());
            return caseVo;

        }
        return null;
    }


    @Override
    public Page<MarketingPlanCaseVo> dmsQueryCaseListByCondition(Pageable pageable, MarketingPlanSecondCategoryDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(dto.getSecondCategory(), "二级费用大类编码不能为空");
        Validate.notNull(dto.getCustomerCode(), "客户编码不能为空");
        List<String> categoryCodes = planCaseRepository.findCategoryCodeBySecondCategory(dto.getSecondCategory());
        dto.setCategoryCodeList(categoryCodes);
        dto.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());

        List<String> yearsList = Lists.newArrayList();
        LocalDate now = LocalDate.now();
        LocalDate nextMonth = now.plusMonths(1);
        String years = now.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        String nextYears = nextMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        yearsList.add(years);
        yearsList.add(nextYears);
        dto.setYearsList(yearsList);
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<MarketingPlanCaseVo> dataResult = planCaseRepository.findPlanCaseListBySecondCategory(page, dto);
        if (CollectionUtils.isNotEmpty(dataResult.getRecords())) {
            List<String> schemeDetailCodes = dataResult.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            //构建参数
            checkHelper.buildMarketingPlanCase(dataResult.getRecords(), productMap);
        }
        return dataResult;
    }


    /**
     * 查询未读数据
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, Long> dmsQueryCaseNotRead(MarketingPlanSecondCategoryDto dto) {
        List<String> secondCategoryCodes = SecondCategoryEnum.getCodes();
        Map<String, List<String>> map = Maps.newHashMap();
        List<String> categoryCodeList = Lists.newArrayList();
        Map<String, Long> dataMap = Maps.newHashMap();
        for (String s : secondCategoryCodes) {
            map.put(s, Lists.newArrayList());
            List<String> categoryCodes = planCaseRepository.findCategoryCodeBySecondCategory(s);
            if (CollectionUtils.isNotEmpty(categoryCodes)) {
                categoryCodeList.addAll(categoryCodes);
                map.put(s, categoryCodes);
            }
            dataMap.put(s, 0l);

        }
        dto.setCategoryCodeList(categoryCodeList);
        dto.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());

        List<String> yearsList = Lists.newArrayList();
        LocalDate now = LocalDate.now();
        LocalDate nextMonth = now.plusMonths(1);
        String years = now.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        String nextYears = nextMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        yearsList.add(years);
        yearsList.add(nextYears);
        dto.setYearsList(yearsList);
        List<MarketingPlanCaseVo> caseVoList = planCaseRepository.findPlanCaseListBySecondCategoryList(dto);

        if (CollectionUtils.isNotEmpty(caseVoList)) {
            Map<String, Long> categoryCountMap = caseVoList.stream().filter(x -> ObjectUtils.isEmpty(x.getDmsReadFlag()) ||
                            BooleanEnum.FALSE.getCapital().equals(x.getDmsReadFlag()))
                    .collect(Collectors.groupingBy(x -> x.getCategoryCode(), Collectors.counting()));
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                Long result = 0l;
                for (String s : entry.getValue()) {
                    if (categoryCountMap.containsKey(s)) {
                        result = result + categoryCountMap.get(s);
                    }
                }
                dataMap.put(entry.getKey(), result);
            }
        }
        return dataMap;
    }


    @Override
    public List<MarketingPlanCaseVo> findCaseListByYears(String years) {

        List<MarketingPlanCaseVo> caseVoList = planCaseRepository.findCaseListByYearsAndDelFlagProcess(years);
        if (CollectionUtils.isEmpty(caseVoList)) {
            return Lists.newArrayList();
        }
        List<String> schemeDetailCodes = caseVoList.stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
        //构建参数
        checkHelper.buildMarketingPlanCase(caseVoList, productMap);
        return caseVoList;
    }


    /**
     * 查询合同类费用列表
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MarketingPlanCaseVo> findContractByYearsAndLogin(Pageable pageable, MarketingPlanVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(vo.getYears(), "年月不能为空");
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(),pageable.getPageSize());
        AbstractCrmUserIdentity userIdentity = loginUserService.getAbstractLoginUser();
        List<CustomerVo> customerVoList = customerVoService.findCustomerByUserName(userIdentity.getAccount());
        if (CollectionUtils.isNotEmpty(customerVoList)) {
            List<String> customerCodeList = customerVoList.stream().map(x->x.getCustomerCode()).collect(Collectors.toList());
            vo.setCustomerCodeList(customerCodeList);
            Page<MarketingPlanCaseVo> pageData = planCaseRepository.findContractByYearsAndLogin(page,vo);
            if (CollectionUtils.isNotEmpty(pageData.getRecords())) {
                List<String> schemeDetailCodes = pageData.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
                Map<String, List<MarketingPlanProductVo>> productMap = productRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
                //构建参数
                checkHelper.buildMarketingPlanCase(pageData.getRecords(), productMap);
            }
            return pageData;
        }
        return null;
    }

    /**
     * 验证必填字段
     *
     * @param vo
     * @param errMsg
     */
    private void checkMustNeedFiled(MarketingPlanCaseVo vo, StringJoiner errMsg, Map<String, CostTypeCategoryVo> categoryMap,
                                    Map<String, CostTypeDetailVo> detailMap, Map<String, OrgVo> orgMap, Map<String, Boolean> orgChildrenMap,
                                    Map<String, MdmCostCenterVo> costCenterMap,
                                    Map<String, Set<String>> bearOrgCostCenterMap, Map<String, Set<String>> belongOrgCostCenterMap, String years) {
        vo.setIsContractCost(ObjectUtils.defaultIfNull(vo.getIsContractCost(), BooleanEnum.FALSE.getCapital()));
        if (checkHelper.paramNotNull(vo.getDetailCode(), "活动细类编码", errMsg)) {
            if (detailMap.containsKey(vo.getDetailCode())) {
                CostTypeDetailVo costTypeDetailVo = detailMap.get(vo.getDetailCode());
                vo.setDetailName(costTypeDetailVo.getDetailName());
                vo.setCategoryCode(costTypeDetailVo.getCategoryCode());
                vo.setCategoryName(costTypeDetailVo.getCategoryName());
                if (categoryMap.containsKey(vo.getCategoryCode())) {
                    CostTypeCategoryVo categoryVo = categoryMap.get(vo.getCategoryCode());
                    vo.setCategoryName(categoryVo.getCategoryName());
                    Validate.notNull(categoryVo.getBudgetSubjectsCode(), String.format("活动大类%s未绑定预算科目", categoryVo.getCategoryName()));
                    vo.setBudgetSubjectCode(categoryVo.getBudgetSubjectsCode());
                    vo.setBudgetSubjectName(categoryVo.getBudgetSubjectsName());
                } else {
                    errMsg.add(String.format("活动大类编码%s未查询到", vo.getCategoryCode()));
                }

            } else {
                errMsg.add(String.format("活动细类编码%s未查询到", vo.getDetailCode()));
            }
        }
        LocalDate startDate = null;
        LocalDate endDate = null;
        if (checkHelper.paramNotNull(vo.getStartDate(), "活动开始时间", errMsg)) {
            try {
                startDate = LocalDate.parse(vo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            } catch (Exception e) {
                errMsg.add("活动开始时间格式错误,正确格式:yyyy-MM-dd");
            }
        }
        if (checkHelper.paramNotNull(vo.getEndDate(), "活动结束时间", errMsg)) {
            try {
                endDate = LocalDate.parse(vo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            } catch (Exception e) {
                errMsg.add("活动结束时间格式错误,正确格式:yyyy-MM-dd");
            }
        }
        if (ObjectUtils.isNotEmpty(startDate) && ObjectUtils.isNotEmpty(endDate)) {
            if (startDate.isAfter(endDate)) {
                errMsg.add("活动开始时间不能晚于活动结束时间");
            }
            String startYm = startDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            String endYm = endDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            if (!startYm.equals(endYm)) {
                errMsg.add("活动开始、结束时间不在同一个月份");
            }
            //设置活动年月
            vo.setActYears(startYm);
            LocalDate currentDate = LocalDate.now();
            String currentYears = currentDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
            //判断当前时间的年月<方案的年月的时候 判断活动年月必须跟方案年月一致
            if (currentYears.compareTo(years) == -1) {
                if (endYm.compareTo(years) != 0) {
                    errMsg.add("活动时间必须跟方案年月保持一致");
                }
            } else {
                if (endYm.compareTo(years) == 1) {
                    errMsg.add("活动时间不能大于方案年月时间");
                }
            }
        }
        if (checkHelper.paramNotNull(vo.getYears(), "费用归属年月", errMsg)) {
            try {
                LocalDate localDate = LocalDate.parse(vo.getYears() + "-01", DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            } catch (Exception e) {
                errMsg.add("费用归属年月格式错误,正确格式:yyyy-MM");
            }
        }
        if (checkHelper.paramNotNull(vo.getBelongDepartmentCode(), "费用使用部门编码", errMsg)) {
            if (orgMap.containsKey(vo.getBelongDepartmentCode())) {
                OrgVo orgVo = orgMap.get(vo.getBelongDepartmentCode());
                vo.setBelongDepartmentName(orgVo.getOrgName());
                if (!orgChildrenMap.get(vo.getBelongDepartmentCode())) {
                    errMsg.add(String.format("费用使用部门编码%s不是末级部门", vo.getBelongDepartmentCode()));
                }
                if (belongOrgCostCenterMap.containsKey(vo.getBelongDepartmentCode())) {
                    String belongDepartmentCodes = belongOrgCostCenterMap.get(vo.getBelongDepartmentCode()).stream().collect(Collectors.joining(","));
                    vo.setBelongCostCenterCodes(belongDepartmentCodes);
                } else {
                    errMsg.add(String.format("费用使用部门编码%s未绑定成本中心", vo.getBelongDepartmentCode()));
                }
            } else {
                errMsg.add(String.format("费用使用部门编码%s不存在", vo.getBelongDepartmentCode()));
            }
        }
        if (checkHelper.paramNotNull(vo.getCostCenterCode(), "成本中心编码", errMsg)) {
            if (costCenterMap.containsKey(vo.getCostCenterCode())) {
                MdmCostCenterVo costCenterVo = costCenterMap.get(vo.getCostCenterCode());
                vo.setCostCenterName(costCenterVo.getCostCenterName());
                vo.setCostCenterCompanyCode(costCenterVo.getCompanyCode());
                if (CollectionUtils.isNotEmpty(costCenterVo.getOrgList())) {
                    MdmCostCenterOrgVo costCenterOrg = costCenterVo.getOrgList().get(0);
                    vo.setBearDepartmentCode(costCenterOrg.getOrgCode());
                    vo.setBearDepartmentName(costCenterOrg.getOrgName());
                    if (!orgChildrenMap.get(vo.getBearDepartmentCode())) {
                        errMsg.add(String.format("费用承担部门编码%s不是末级部门", vo.getBearDepartmentCode()));
                    }
                } else {
                    errMsg.add(String.format("成本中心%s未绑定组织", vo.getCostCenterCode()));
                }
            } else {
                errMsg.add(String.format("成本中心编码%s不存在", vo.getCostCenterCode()));
            }
        }
    }

    /**
     * 数据字典校验
     *
     * @param vo
     * @param errMsg
     */
    private void checkDictData(MarketingPlanCaseVo vo, StringJoiner errMsg, Map<String, CostTypeDetailVo> detailMap) {
        //兑付方式
        if (ObjectUtils.isNotEmpty(vo.getCashType())) {
            if (ObjectUtils.isNotEmpty(vo.getDetailCode()) && detailMap.containsKey(vo.getDetailCode())) {
                CostTypeDetailVo costTypeDetailVo = detailMap.get(vo.getDetailCode());
                if (costTypeDetailVo.getPayBy().contains(vo.getCashType())) {
                    Map<String, String> map = cashMap();
                    if (!map.containsKey(vo.getCashType())) {
                        errMsg.add("兑付方式填写错误");
                    }
                } else {
                    errMsg.add("兑付方式不在费用项目的兑付方式范围内");
                }
            } else {
                errMsg.add("兑付方式不属于费用项目");
            }

        }
        //费用依据
        if (CollectionUtils.isNotEmpty(vo.getCostBasisList())) {
            Map<String, String> map = costBasisMap();
            for (String s : vo.getCostBasisList()) {
                if (!map.containsKey(s)) {
                    errMsg.add(String.format("费用依据%s错误", s));
                }
            }
            vo.setCostBasis(vo.getCostBasisList().stream().collect(Collectors.joining(",")));
        }
        //物料类型
        if (ObjectUtils.isNotEmpty(vo.getSellMaterialType())) {
            Map<String, String> map = sellMaterialTypeMap();
            if (!map.containsKey(vo.getSellMaterialType())) {
                errMsg.add(String.format("物料类型%s错误", vo.getSellMaterialType()));
            }
        }
        //运输方式
        if (ObjectUtils.isNotEmpty(vo.getTransportType())) {
            Map<String, String> map = transportTypeMap();
            if (!map.containsKey(vo.getTransportType())) {
                errMsg.add(String.format("运输方式%s错误", vo.getTransportType()));
            }
        }
        //投放平台
        if (ObjectUtils.isNotEmpty(vo.getPlatform())) {
            Map<String, String> map = platFormMap();
            if (!map.containsKey(vo.getPlatform())) {
                errMsg.add(String.format("投放平台%s错误", vo.getPlatform()));
            }
        }
        //返利类型
        if (ObjectUtils.isNotEmpty(vo.getRebateType())) {
            Map<String, String> map = rebateTypeMap();
            if (!map.containsKey(vo.getRebateType())) {
                errMsg.add(String.format("返利类型%s错误", vo.getRebateType()));
            }
        }
        //执行示例
        if (CollectionUtils.isNotEmpty(vo.getExecuteExampleList())) {
            Map<String, String> map = excuteExampleMap();
            for (String s : vo.getExecuteExampleList()) {
                if (!map.containsKey(s)) {
                    errMsg.add(String.format("执行示例%s错误", s));
                }
            }
            vo.setExecuteExample(vo.getExecuteExampleList().stream().collect(Collectors.joining(",")));
        }
        //结案示例
        if (CollectionUtils.isNotEmpty(vo.getCloseCaseExampleList())) {
            Map<String, String> map = endCaseExampleMap();
            for (String s : vo.getCloseCaseExampleList()) {
                if (!map.containsKey(s)) {
                    errMsg.add(String.format("结案示例%s错误", s));
                }
            }
            vo.setCloseCaseExample(vo.getCloseCaseExampleList().stream().collect(Collectors.joining(",")));
        }
    }

    //兑付方式
    private final static ThreadLocal<Map<String, String>> cashThreadLocal = new ThreadLocal<>();
    //费用依据
    private final static ThreadLocal<Map<String, String>> costBasisThreadLocal = new ThreadLocal<>();
    //执行示例
    private final static ThreadLocal<Map<String, String>> excuteExampleThreadLocal = new ThreadLocal<>();
    //结案示例
    private final static ThreadLocal<Map<String, String>> endCaseExampleThreadLocal = new ThreadLocal<>();
    //物料类型
    private final static ThreadLocal<Map<String, String>> sellMaterialTypeThreadLocal = new ThreadLocal<>();
    //周边运输方式
    private final static ThreadLocal<Map<String, String>> transprotTypeThreadLocal = new ThreadLocal<>();
    //投放平台
    private final static ThreadLocal<Map<String, String>> platFormThreadLocal = new ThreadLocal<>();
    //返利类型
    private final static ThreadLocal<Map<String, String>> rebateTypeThreadLocal = new ThreadLocal<>();

    /**
     * 兑付方式
     *
     * @return
     */
    private Map<String, String> cashMap() {
        Map<String, String> map = cashThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_SCHEME_CASH_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            cashThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 费用依据
     *
     * @return
     */
    private Map<String, String> costBasisMap() {
        Map<String, String> map = costBasisThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_SCHEME_COST_BASIS);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            costBasisThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 执行示例
     *
     * @return
     */
    private Map<String, String> excuteExampleMap() {
        Map<String, String> map = excuteExampleThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = planCaseRepository.findCloseCaseExampleList();
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            excuteExampleThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 结案示例
     *
     * @return
     */
    private Map<String, String> endCaseExampleMap() {
        Map<String, String> map = endCaseExampleThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = planCaseRepository.findCloseCaseExampleList();
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            endCaseExampleThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 物料类型
     *
     * @return
     */
    private Map<String, String> sellMaterialTypeMap() {
        Map<String, String> map = sellMaterialTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SELL_MATERIAL_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            sellMaterialTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 运输方式
     *
     * @return
     */
    private Map<String, String> transportTypeMap() {
        Map<String, String> map = transprotTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_TRANSPORT_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            transprotTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 投放平台
     *
     * @return
     */
    private Map<String, String> platFormMap() {
        Map<String, String> map = platFormThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_PLATFORM);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            platFormThreadLocal.set(map);
        }
        return map;
    }


    /**
     * 返利类型
     *
     * @return
     */
    private Map<String, String> rebateTypeMap() {
        Map<String, String> map = rebateTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_REBATE_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            rebateTypeThreadLocal.set(map);
        }
        return map;
    }

}
