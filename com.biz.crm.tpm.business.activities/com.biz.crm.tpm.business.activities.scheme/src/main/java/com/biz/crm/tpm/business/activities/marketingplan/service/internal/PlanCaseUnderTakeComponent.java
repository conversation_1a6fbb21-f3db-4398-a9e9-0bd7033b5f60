package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanDepartmentVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanProductVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanScopeVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/3 18:41
 */
@Component
@Slf4j
public class PlanCaseUnderTakeComponent {

    @Resource
    private MarketingPlanCaseCheckHelper caseCheckHelper;

    @Resource
    private OrgVoService orgVoService;


    /**
     * 校验承接总部、大区方案的数据
     *
     * @param caseVo
     */
    public void checkMarketingUnderTakeCase(MarketingPlanCaseVo caseVo, Map<String, BigDecimal> underTakeAmountMap) {
        Validate.notNull(caseVo.getBearCase(), String.format("承接明细%s不存在", caseVo.getReleaseDetailCode()));
        OverallPlanCaseVo overallPlanCaseVo = caseVo.getBearCase();
        caseVo.setHeadSchemeDetailCode(overallPlanCaseVo.getHeadSchemeDetailCode());
        caseVo.setHeadSchemeCode(overallPlanCaseVo.getHeadSchemeCode());
        StringJoiner errMsg = new StringJoiner(";");
        //判断是总部明细
        if (overallPlanCaseVo.getSchemeDetailCode().contains(OverallPlanConstant.HEAD_SCHEME_DETAIL_CODE_RULE)) {
            if (BooleanEnum.FALSE.getCapital().equals(overallPlanCaseVo.getBearFlag())) {
                errMsg.add("总部承接指引明细不可区域直接承接");
            }
        }
        //1.校验活动细类控制
        List<String> detailCodes = caseCheckHelper.findDetailCodesByCondition(overallPlanCaseVo);
        if (StringUtils.isNotEmpty(caseVo.getDetailCode())
                && ObjectUtils.isNotEmpty(detailCodes)
                && !detailCodes.contains(caseVo.getDetailCode())) {
            errMsg.add("活动细类不在承接范围内");
        }
        //2.时间范围校验
        LocalDate startDate = LocalDate.parse(overallPlanCaseVo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        LocalDate endDate = LocalDate.parse(overallPlanCaseVo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        LocalDate caseStartDate = LocalDate.parse(caseVo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        LocalDate caseEndDate = LocalDate.parse(caseVo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        if (caseStartDate.isBefore(startDate) || caseEndDate.isAfter(endDate)) {
            errMsg.add("方案明细的活动时间必须在承接方案活动时间内");
        }
        //3.费用承担部门
        if (ObjectUtils.isNotEmpty(overallPlanCaseVo.getBearDepartmentCode())) {
            if (!overallPlanCaseVo.getBearDepartmentCode().equals(caseVo.getBearDepartmentCode())) {
                errMsg.add("费用承担部门必须等于承接方案的费用承担部门");
            }
        }
        //4.成本中心
        if (ObjectUtils.isNotEmpty(overallPlanCaseVo.getCostCenterCode())) {
            if (!overallPlanCaseVo.getCostCenterCode().equals(caseVo.getCostCenterCode())) {
                errMsg.add("成本中心必须等于承接方案的成本中心");
            }
        }
        //5.承接销售部门
        if (CollectionUtils.isNotEmpty(overallPlanCaseVo.getBearDepartmentList())) {
            List<String> orgCodes = overallPlanCaseVo.getBearDepartmentList().stream().map(OverallPlanDepartmentVo::getDepartmentCode)
                    .collect(Collectors.toList());
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCodes(orgCodes);
            List<String> allOrgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            if (!allOrgCodes.contains(caseVo.getBelongDepartmentCode())) {
                errMsg.add("费用使用部门必须在承接销售部门下");
            }
        }
        //6.费用承担部门
        if (ObjectUtils.isNotEmpty(overallPlanCaseVo.getBearDepartmentCode())) {
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(overallPlanCaseVo.getBearDepartmentCode());
            List<String> allOrgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            if (!allOrgCodes.contains(caseVo.getBearDepartmentCode())) {
                errMsg.add("费用使用部门必须在关联方案的承接销售部门当前及下级");
            }
        }
        //7.客户
        Set<String> customerCodeSet = caseCheckHelper.findCustomerCodesByCondition(overallPlanCaseVo);
        if (CollectionUtils.isNotEmpty(customerCodeSet)) {
            if (ObjectUtils.isNotEmpty(caseVo.getCustomerCode()) && !customerCodeSet.contains(caseVo.getCustomerCode())) {
                errMsg.add("客户必须在承接客户条件范围内");
            }
        }
        //8.终端
        List<String> terminalCodeSet = caseCheckHelper.findTerminalCodesByCondition(overallPlanCaseVo);
        if (CollectionUtils.isNotEmpty(terminalCodeSet)) {
            if (ObjectUtils.isNotEmpty(caseVo.getTerminalCode()) && !terminalCodeSet.contains(caseVo.getTerminalCode())) {
                errMsg.add("门店必须在承接门店条件范围内");
            }
        }
        //9.品项
        if (CollectionUtils.isNotEmpty(overallPlanCaseVo.getItemList())) {
            List<String> itemCodeList = overallPlanCaseVo.getItemList().stream().map(OverallPlanProductVo::getCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caseVo.getItemList())) {
                List<String> codes = caseVo.getItemList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toList());
                if (!caseCheckHelper.containsAll(itemCodeList, codes)) {
                    errMsg.add("品项必须在承接品项范围内");
                }
            }
            if (CollectionUtils.isNotEmpty(caseVo.getFeeItemList())) {
                List<String> codes = caseVo.getFeeItemList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toList());
                if (!caseCheckHelper.containsAll(itemCodeList, codes)) {
                    errMsg.add("费用品项必须在承接品项范围内");
                }
            }
            if (CollectionUtils.isNotEmpty(caseVo.getFeeBelongItemList())) {
                List<String> codes = caseVo.getFeeBelongItemList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toList());
                if (!caseCheckHelper.containsAll(itemCodeList, codes)) {
                    errMsg.add("费用归属品项必须在承接品项范围内");
                }
            }
        }
        //9.产品
        if (CollectionUtils.isNotEmpty(overallPlanCaseVo.getProductList())) {
            List<String> productList = overallPlanCaseVo.getProductList().stream().map(OverallPlanProductVo::getCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                List<String> codes = caseVo.getProductList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toList());
                if (!caseCheckHelper.containsAll(productList, codes)) {
                    errMsg.add("产品必须在承接产品范围内");
                }
            }
            if (CollectionUtils.isNotEmpty(caseVo.getFeeProductList())) {
                List<String> codes = caseVo.getFeeProductList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toList());
                if (!caseCheckHelper.containsAll(productList, codes)) {
                    errMsg.add("费用产品必须在承接产品范围内");
                }
            }
        }
        overallPlanCaseVo.setApplyAmount(Optional.ofNullable(overallPlanCaseVo.getApplyAmount()).orElse(BigDecimal.ZERO));
        caseVo.setApplyAmount(Optional.ofNullable(caseVo.getApplyAmount()).orElse(BigDecimal.ZERO));
        //10.费用
        if (overallPlanCaseVo.getApplyAmount().compareTo(caseVo.getApplyAmount()) < 0) {
            errMsg.add("申请费用金额必须小于承接申请费用金额");
        } else if (underTakeAmountMap.containsKey(caseVo.getReleaseDetailCode())) {
            BigDecimal applyAmount = underTakeAmountMap.getOrDefault(caseVo.getReleaseDetailCode(), BigDecimal.ZERO);
            applyAmount = applyAmount.add(caseVo.getApplyAmount());
            //指引承接金额小于汇总承接的金额
            BigDecimal differenceAmount = overallPlanCaseVo.getApplyAmount().subtract(applyAmount);
            if (differenceAmount.compareTo(BigDecimal.ZERO) < 0) {
                errMsg.add(String.format("承接金额超过承接指引明细的金额,超额%s", differenceAmount.negate()));
            }
            underTakeAmountMap.put(caseVo.getReleaseDetailCode(), applyAmount);
        } else {
            underTakeAmountMap.put(caseVo.getReleaseDetailCode(), caseVo.getApplyAmount());
        }
        if (ObjectUtils.isNotEmpty(errMsg.toString())) {
            caseVo.setErrMsg(errMsg.toString());
            caseVo.setCheckFlag(Boolean.FALSE);
        }
    }


    /**
     * 区域承接校验
     *
     * @param caseVo
     */
    public void checkRegionUnderTakeCase(OverallPlanCaseVo caseVo, Map<String, BigDecimal> underTakeAmountMap) {
        OverallPlanCaseVo overallPlanCaseVo = caseVo.getBearCase();
        StringJoiner errMsg = new StringJoiner(";");
        //判断是总部明细
        if (overallPlanCaseVo.getSchemeDetailCode().contains(OverallPlanConstant.HEAD_SCHEME_DETAIL_CODE_RULE)) {
            if (BooleanEnum.TRUE.getCapital().equals(overallPlanCaseVo.getBearFlag())) {
                errMsg.add("总部承接指引明细到区域的大区不可直接承接");
            }
        }
        //1.活动大类
        if (!caseVo.getSecondCostCategory().equals(overallPlanCaseVo.getSecondCostCategory())) {
            errMsg.add("二级费用大类必须等于承接二级费用大类");
        }
        //1.校验活动细类控制
        List<String> detailCodes = caseCheckHelper.findDetailCodesByCondition(overallPlanCaseVo);
        if (StringUtils.isNotEmpty(caseVo.getDetailCode())
                && ObjectUtils.isNotEmpty(detailCodes)
                && !detailCodes.contains(caseVo.getDetailCode())) {
            errMsg.add("活动细类不在承接范围内");
        }
        //2.时间范围校验
        LocalDate startDate = LocalDate.parse(overallPlanCaseVo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        LocalDate endDate = LocalDate.parse(overallPlanCaseVo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        LocalDate caseStartDate = LocalDate.parse(caseVo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        LocalDate caseEndDate = LocalDate.parse(caseVo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
        if (caseStartDate.isBefore(startDate) || caseEndDate.isAfter(endDate)) {
            errMsg.add("方案明细的活动时间必须在承接方案活动时间内");
        }
        //3.费用承担部门
        if (ObjectUtils.isNotEmpty(overallPlanCaseVo.getBearDepartmentCode())) {
            if (!overallPlanCaseVo.getBearDepartmentCode().equals(caseVo.getBearDepartmentCode())) {
                errMsg.add("费用承担部门必须等于承接方案的费用承担部门");
            }
        }
        //4.成本中心
        if (ObjectUtils.isNotEmpty(overallPlanCaseVo.getCostCenterCode())) {
            if (!overallPlanCaseVo.getCostCenterCode().equals(caseVo.getCostCenterCode())) {
                errMsg.add("成本中心必须等于承接方案的成本中心");
            }
        }
        //5.承接销售部门
        if (CollectionUtils.isNotEmpty(overallPlanCaseVo.getBearDepartmentList()) && CollectionUtils.isNotEmpty(caseVo.getBearDepartmentList())) {
            List<String> bearDepartmentCodes = caseVo.getBearDepartmentList().stream().map(OverallPlanDepartmentVo::getDepartmentCode).collect(Collectors.toList());
            List<String> orgCodes = overallPlanCaseVo.getBearDepartmentList().stream().map(OverallPlanDepartmentVo::getDepartmentCode)
                    .collect(Collectors.toList());
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCodes(orgCodes);
            List<String> allOrgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            if (!caseCheckHelper.containsAll(allOrgCodes, bearDepartmentCodes)) {
                errMsg.add("承接销售部门必须在承接销售部门下");
            }
        }
        //6.费用承担部门
        if (ObjectUtils.isNotEmpty(overallPlanCaseVo.getBearDepartmentCode())) {
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(overallPlanCaseVo.getBearDepartmentCode());
            List<String> allOrgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            if (!allOrgCodes.contains(caseVo.getBearDepartmentCode())) {
                errMsg.add("费用使用部门必须在关联方案的承接销售部门当前及下级");
            }
        }
        //7.客户
        //客户标签
        if (CollectionUtils.isNotEmpty(overallPlanCaseVo.getCustomerTagList())) {
            caseVo.setCustomerTagList(overallPlanCaseVo.getCustomerTagList());
        }
        //合作类型
        if (CollectionUtils.isNotEmpty(overallPlanCaseVo.getCooperateTypeList())) {
            caseVo.setCooperateTypeList(overallPlanCaseVo.getCooperateTypeList());
        }
        Set<String> customerCodeSet = caseCheckHelper.findCustomerCodesByCondition(overallPlanCaseVo);
        if (CollectionUtils.isNotEmpty(customerCodeSet)) {
            if (CollectionUtils.isNotEmpty(caseVo.getCustomerList())) {
                List<String> customerCodes = caseVo.getCustomerList().stream().map(OverallPlanScopeVo::getCustomerCode).collect(Collectors.toList());
                if (!caseCheckHelper.containsAll(Lists.newArrayList(customerCodeSet), customerCodes)) {
                    errMsg.add("客户必须在承接客户条件范围内");
                }
            }
        }
        //8.终端
        //终端标签
        if (CollectionUtils.isNotEmpty(overallPlanCaseVo.getTerminalTagList())) {
            caseVo.setTerminalTagList(overallPlanCaseVo.getTerminalTagList());
        }
        List<String> terminalCodeSet = caseCheckHelper.findTerminalCodesByCondition(overallPlanCaseVo);
        if (CollectionUtils.isNotEmpty(terminalCodeSet)) {
            if (CollectionUtils.isNotEmpty(caseVo.getTerminalList())) {
                List<String> terminalCodes = caseVo.getTerminalList().stream().map(OverallPlanScopeVo::getTerminalCode).collect(Collectors.toList());
                if (!caseCheckHelper.containsAll(terminalCodeSet, terminalCodes)) {
                    errMsg.add("门店必须在承接门店条件范围内");
                }
            }
        }
        //9.品项
        if (CollectionUtils.isNotEmpty(overallPlanCaseVo.getItemList())) {
            List<String> itemCodeList = overallPlanCaseVo.getItemList().stream().map(OverallPlanProductVo::getCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caseVo.getItemList())) {
                List<String> codes = caseVo.getItemList().stream().map(OverallPlanProductVo::getCode).collect(Collectors.toList());
                if (!caseCheckHelper.containsAll(itemCodeList, codes)) {
                    errMsg.add("品项必须在承接品项范围内");
                }
            }
        }
        //9.产品
        if (CollectionUtils.isNotEmpty(overallPlanCaseVo.getProductList())) {
            List<String> productList = overallPlanCaseVo.getProductList().stream().map(OverallPlanProductVo::getCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                List<String> codes = caseVo.getProductList().stream().map(OverallPlanProductVo::getCode).collect(Collectors.toList());
                if (!caseCheckHelper.containsAll(productList, codes)) {
                    errMsg.add("产品必须在承接产品范围内");
                }
            }
        }
        //10.费用
        if (overallPlanCaseVo.getApplyAmount().compareTo(caseVo.getApplyAmount()) == -1) {
            errMsg.add("申请费用金额必须小于承接申请费用金额");
        } else if (underTakeAmountMap.containsKey(caseVo.getHeadSchemeDetailCode())) {
            BigDecimal applyAmount = underTakeAmountMap.get(caseVo.getHeadSchemeDetailCode());
            applyAmount = applyAmount.add(caseVo.getApplyAmount());
            //指引承接金额小于汇总承接的金额
            BigDecimal differenceAmount = overallPlanCaseVo.getApplyAmount().subtract(applyAmount);
            if (differenceAmount.compareTo(BigDecimal.ZERO) == -1) {
                errMsg.add(String.format("承接金额超过承接指引明细的金额,超额%s", differenceAmount.negate()));
            }
            underTakeAmountMap.put(caseVo.getHeadSchemeDetailCode(), applyAmount);
        } else {
            underTakeAmountMap.put(caseVo.getHeadSchemeDetailCode(), caseVo.getApplyAmount());
        }
        if (ObjectUtils.isNotEmpty(errMsg.toString())) {
            caseVo.setErrMsg(errMsg.toString());
            caseVo.setCheckFlag(Boolean.FALSE);
        }
    }
}
