package com.biz.crm.tpm.business.activities.materialdemand.exports.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description 物料需求提报导出vo
 * <AUTHOR>
 * @Date 2024/7/24 13:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@CrmExcelExport
public class MaterialDemandDetailExportVo extends CrmExcelVo {

    @CrmExcelColumn("物资需求名称")
    private String demandName;

    @CrmExcelColumn("部门")
    private String mainOrgName;

    @CrmExcelColumn("备注说明")
    private String mainRemark;

    @CrmExcelColumn("使用部门")
    private String orgName;

    @CrmExcelColumn("物料编码")
    private String materialCode;

    @CrmExcelColumn("物料名称")
    private String materialName;

    @CrmExcelColumn("物料数量")
    private BigDecimal materialNum;

    @CrmExcelColumn("物料金额（元）")
    private BigDecimal materialAmount;

    @CrmExcelColumn("备注")
    private String remark;
}
