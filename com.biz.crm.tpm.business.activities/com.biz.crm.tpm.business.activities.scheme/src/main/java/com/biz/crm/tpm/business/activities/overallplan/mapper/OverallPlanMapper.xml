<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanMapper">

    <select id="findList" resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanVo">
        select * from tpm_overall_plan a
        left join (
        SELECT
        GROUP_CONCAT(department_name) departmentNameStr,
        scheme_code
        FROM
        tpm_overall_plan_department
        WHERE
        scheme_detail_code IS NULL
        GROUP BY
        scheme_code
        ) b on a.scheme_code = b.scheme_code
        <where>
            a.del_flag = #{vo.delFlag}
            <if test="vo.tenantCode != null and vo.tenantCode != ''">
                and a.tenant_code = #{vo.tenantCode}
            </if>
            <if test="vo.schemeType != null and vo.schemeType != ''">
                and a.scheme_type = #{vo.schemeType}
            </if>
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="vo.schemeCode+ '%'"/>
                and a.scheme_code like #{schemeCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and a.scheme_name like #{schemeName}
            </if>
            <if test="vo.processStatus != null and vo.processStatus != ''">
                and a.process_status = #{vo.processStatus}
            </if>
            <if test="vo.startDate != null and vo.startDate != '' and vo.endDate != null and vo.endDate != ''">
                <![CDATA[and a.start_date >= #{vo.startDate}]]>  <![CDATA[and a.end_date <= #{vo.endDate}]]>
            </if>
            <if test="vo.createName != null and vo.createName != ''">
                <bind name="createName" value="'%' + vo.createName + '%'"/>
                and a.create_name like #{createName}
            </if>
            order by a.create_time desc
        </where>
    </select>

    <select id="findSummaryList" resultType="com.biz.crm.tpm.business.activities.overallplan.vo.HeadOverallSummaryReportVo">
        select
            a.scheme_code,
            a.scheme_name,
            a.org_code,
            a.org_name,
            sum(ifnull(b.apply_amount,0)) applyAmount
        from tpm_overall_plan a
        left join tpm_overall_plan_case b on a.scheme_code = b.scheme_code
        <where>
            a.process_status = '3'
            and a.scheme_type = 'head'
            and a.del_flag = '${@<EMAIL>()}'
            and a.tenant_code = #{tenantCode}
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="vo.schemeCode + '%'"/>
                and a.scheme_code like #{schemeCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and a.scheme_name like #{schemeName}
            </if>
            <if test="vo.orgCode != null and vo.orgCode != ''">
                <bind name="orgCode" value="'%' + vo.orgCode + '%'"/>
                and a.org_code like #{orgCode}
            </if>
            <if test="vo.orgName != null and vo.orgName != ''">
                <bind name="orgName" value="'%' + vo.orgName + '%'"/>
                and a.org_name like #{orgName}
            </if>
            group by
            a.scheme_code,a.scheme_name,a.org_code,a.org_name
            order by a.scheme_code desc
        </where>
    </select>
    <select id="findBySchemeCodes"
            resultType="com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlan">
        select * from tpm_overall_plan where scheme_code in
        <foreach collection="schemeCodes" item="schemeCode" open="(" separator="," close=")">
            #{schemeCode}
        </foreach>
        and del_flag = '${@<EMAIL>()}'
        and enable_status = '009'
    </select>

    <select id="findBySchemeCodesByName" resultType="java.lang.String" parameterType="java.lang.String">
        select  scheme_code from tpm_overall_plan
        <where>
            <if test="headSchemaName != null and headSchemaName != ''">
                and  scheme_name like concat('%',#{headSchemaName},'%')
            </if>
            and del_flag = '${@<EMAIL>()}'
            and enable_status = '009'
        </where>
    </select>

</mapper>

