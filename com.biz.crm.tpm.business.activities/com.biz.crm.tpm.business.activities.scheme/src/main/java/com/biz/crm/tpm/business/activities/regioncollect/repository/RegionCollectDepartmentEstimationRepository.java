package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectDepartmentEstimationMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class RegionCollectDepartmentEstimationRepository extends ServiceImpl<RegionCollectDepartmentEstimationMapper, RegionCollectDepartmentEstimation> {


    public List<RegionCollectDepartmentEstimation> findByCollectCode(String collectCode) {
        return this.lambdaQuery()
                .eq(RegionCollectDepartmentEstimation::getCollectCode, collectCode).list();
    }


    public void deleteByCollectCodes(List<String> collectCodes) {
        this.lambdaUpdate()
                .in(RegionCollectDepartmentEstimation::getCollectCode, collectCodes).remove();
    }
}
