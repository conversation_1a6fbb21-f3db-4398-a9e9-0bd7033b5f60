<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingItemShareMapper">

    <select id="findListByCondition" resultType="com.biz.crm.tpm.business.activities.sdk.vo.MarketingItemShareVo">
        select * from tpm_marketing_item_share
        <where>
            1=1
            <if test="vo.belongDepartmentCode != null and vo.belongDepartmentCode != ''">
                and belong_department_code = #{vo.belongDepartmentCode}
            </if>
            <if test="vo.belongDepartmentName != null and vo.belongDepartmentName != ''">
                <bind name="belongDepartmentName" value="'%' + vo.belongDepartmentName + '%'"/>
                and belong_department_name like #{belongDepartmentName}
            </if>
            <if test="vo.shareOperationYears != null and vo.shareOperationYears != ''">
                and share_operation_years = #{vo.shareOperationYears}
            </if>
            <if test="vo.actName != null and vo.actName != ''">
                <bind name="actName" value="'%' + vo.actName + '%'"/>
                and act_name like #{actName}
            </if>
            <if test="vo.customerCode != null and vo.customerCode != ''">
                <bind name="customerCode" value="'%' + vo.customerCode + '%'"/>
                and customer_code like #{customerCode}
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                <bind name="customerName" value="'%' + vo.customerName + '%'"/>
                and customer_name like #{customerName}
            </if>
            <if test="vo.shareItemCode != null and vo.shareItemCode != ''">
                <bind name="shareItemCode" value="'%' + vo.shareItemCode + '%'"/>
                and share_item_code like #{shareItemCode}
            </if>
            <if test="vo.shareItemName != null and vo.shareItemName != ''">
                <bind name="shareItemName" value="'%' + vo.shareItemName + '%'"/>
                and share_item_name like #{shareItemName}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="'%' + vo.schemeCode + '%'"/>
                and scheme_code like #{schemeCode}
            </if>
            <if test="vo.actYears != null and vo.actYears != ''">
                and act_years = #{vo.actYears}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and years = #{vo.years}
            </if>
            <if test="vo.detailName != null and vo.detailName != ''">
                <bind name="detailName" value="'%' + vo.detailName + '%'"/>
                and detail_name like #{detailName}
            </if>
            order by create_time desc,scheme_detail_code desc,id desc
        </where>
    </select>
</mapper>

