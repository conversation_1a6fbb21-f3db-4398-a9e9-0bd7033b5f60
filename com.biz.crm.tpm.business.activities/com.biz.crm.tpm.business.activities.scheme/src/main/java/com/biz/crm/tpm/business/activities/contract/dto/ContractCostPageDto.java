package com.biz.crm.tpm.business.activities.contract.dto;

import com.biz.crm.business.common.base.vo.OssFileVo;
import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/8 21:45
 */
@Data
@ApiModel("合同费用台账分页vo")
public class ContractCostPageDto extends TenantFlagOpDto {

    @ApiModelProperty("是否外部合同")
    private String externalFlag;

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("唯一编码")
    private String onlyKey;


    @ApiModelProperty("执行状态")
    private String executeStatus;

    @ApiModelProperty("日志")
    private String log;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同状态")
    private String contractStatus;

    @ApiModelProperty("合同类型")
    private String contractType;

    @ApiModelProperty("是否补充")
    private String isReplenish;

    @ApiModelProperty("原始合同")
    private String originalContractCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("开始时间")
    private String contractStartDate;

    @ApiModelProperty("结束时间")
    private String contractEndDate;

    @ApiModelProperty("合同明细编码")
    private String contractDetailCode;

    @ApiModelProperty("合同明细类型")
    private String caseType;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("预估费用")
    private BigDecimal estimatedCost;

    @ApiModelProperty("兑付方式")
    private String cashType;

    @ApiModelProperty("返利类型")
    private String rebateType;

    @ApiModelProperty("返利计算日期")
    private Integer rebateCalDay;

    @ApiModelProperty("条件公式")
    private String conditionFormula;

    @ApiModelProperty("结果公式")
    private String resultFormula;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("返利标准")
    private String rebateStandard;

    @ApiModelProperty("条件数量")
    private String conditionNum;

    @ApiModelProperty("搭赠数量")
    private String giveNum;

    @ApiModelProperty("优惠数量-上限")
    private BigDecimal discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    private BigDecimal discountAmount;

    @ApiModelProperty("商品")
    private String productStr;

    @ApiModelProperty("品项")
    private String itemStr;

    @ApiModelProperty("小类")
    private String levelStr;

    @ApiModelProperty("费用商品")
    private String feeProductStr;

    @ApiModelProperty("费用品项")
    private String feeItemStr;

    @ApiModelProperty("费用小类")
    private String feeLevelStr;

    @ApiModelProperty("商品编码")
    private String productCodeStr;

    @ApiModelProperty("品项编码")
    private String itemCodeStr;

    @ApiModelProperty("小类编码")
    private String levelCodeStr;

    @ApiModelProperty("费用商品编码")
    private String feeProductCodeStr;

    @ApiModelProperty("费用品项编码")
    private String feeItemCodeStr;

    @ApiModelProperty("费用小类编码")
    private String feeLevelCodeStr;

    @ApiModelProperty("费用归属品项编码")
    private String feeBelongItemCodeStr;

    @ApiModelProperty("费用归属品项名称")
    private String feeBelongItemStr;

    @ApiModelProperty("合同类型")
    private List<String> contractTypeList;

    @ApiModelProperty("附件地址")
    private List<OssFileVo> fileList;
}
