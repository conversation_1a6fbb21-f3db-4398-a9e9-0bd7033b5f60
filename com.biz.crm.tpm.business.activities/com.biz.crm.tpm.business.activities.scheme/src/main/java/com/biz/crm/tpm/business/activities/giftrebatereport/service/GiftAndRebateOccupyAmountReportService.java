package com.biz.crm.tpm.business.activities.giftrebatereport.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.giftrebatereport.dto.GiftAndRebateOccupyAmountReportDto;
import com.biz.crm.tpm.business.activities.giftrebatereport.vo.GiftAndRebateOccupyAmountReportVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 搭赠及返利实时金额占用报表Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface GiftAndRebateOccupyAmountReportService {

    /**
     * 分页查询搭赠及返利实时金额占用报表
     *
     * @param pageable 分页对象
     * @param dto      查询条件
     * @return 分页结果
     */
    Page<GiftAndRebateOccupyAmountReportVo> findByConditions(Pageable pageable, GiftAndRebateOccupyAmountReportDto dto);

    /**
     * 根据条件查询报表数据列表
     *
     * @param dto 查询条件
     * @return 报表数据列表
     */
    List<GiftAndRebateOccupyAmountReportVo> findListByConditions(GiftAndRebateOccupyAmountReportDto dto);

    /**
     * 根据ID查询报表详情
     *
     * @param id 主键ID
     * @return 报表详情
     */
    GiftAndRebateOccupyAmountReportVo findById(String id);


    /**
     * 根据方案明细编码查询报表数据
     *
     * @param schemeDetailCode 方案明细编码
     * @return 报表数据列表
     */
    List<GiftAndRebateOccupyAmountReportVo> findBySchemeDetailCode(String schemeDetailCode);


    /**
     * 新增报表数据
     *
     * @param vo 报表数据
     * @return 新增结果
     */
    GiftAndRebateOccupyAmountReportVo create(GiftAndRebateOccupyAmountReportVo vo);


    List<GiftAndRebateOccupyAmountReportVo> batchCreate(List<GiftAndRebateOccupyAmountReportVo> voList);

    /**
     * 修改报表数据
     *
     * @param vo 报表数据
     * @return 修改结果
     */
    GiftAndRebateOccupyAmountReportVo update(GiftAndRebateOccupyAmountReportVo vo);



    /**
     * 刷新报表数据
     * 根据业务规则重新计算和更新报表数据
     *
     * @param dto 刷新条件
     */
    void refreshReportData(GiftAndRebateOccupyAmountReportDto dto);

    /**
     * 导出报表数据
     *
     * @param dto 查询条件
     * @return 导出数据列表
     */
    List<GiftAndRebateOccupyAmountReportVo> exportData(GiftAndRebateOccupyAmountReportDto dto);

    /**
     * 计算占用金额
     * 根据业务规则实时计算各方案的占用金额
     *
     * @param dto 计算条件（可为null，表示计算所有数据）
     */
    void calculateOccupyAmount(GiftAndRebateOccupyAmountReportDto dto);


    void calculateLastMonthOccupyAmount();
}
