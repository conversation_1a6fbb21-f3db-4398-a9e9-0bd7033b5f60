package com.biz.crm.tpm.business.activities.regioncollect.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.CustomerGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "RegionCollectDepartmentEstimationVo", description = "大区汇总-部门测算")
public class RegionCollectDepartmentEstimationVo extends CustomerGainsAndLossesVo {

    @ApiModelProperty("大区汇总编码")
    private String collectCode;

}
