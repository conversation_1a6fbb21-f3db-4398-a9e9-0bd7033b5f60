package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.biz.crm.business.common.sdk.vo.FileVo;
import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Author: yangrui
 * @Date: 2025-01-02 10:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanExecutionSumDetailVo extends UuidFlagOpVo {

    @ApiModelProperty("门店编码")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    private String terminalName;

    @ApiModelProperty("执行标准")
    private String actStandards;

    @ApiModelProperty("审核结果")
    private Integer auditResult;

    @ApiModelProperty("审核结果")
    private String auditResultDesc;

    @ApiModelProperty("执行结果")
    private List<PlanExecutionSumDetailResultVo> exeResultList;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class PlanExecutionSumDetailResultVo extends FileVo {
        @ApiModelProperty("执行人")
        private String executor;
        @ApiModelProperty("执行时间")
        @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        private Date executeDate;
        @ApiModelProperty("执行结果")
        private String result;
        @ApiModelProperty("执行结果")
        private String resultDesc;

        /**
         * 水印
         */
        @ApiModelProperty("水印")
        private String watermarkStr;

        /**
         * 水印开关
         */

        @ApiModelProperty("水印开关")
        private String watermarkFlag;
    }
}
