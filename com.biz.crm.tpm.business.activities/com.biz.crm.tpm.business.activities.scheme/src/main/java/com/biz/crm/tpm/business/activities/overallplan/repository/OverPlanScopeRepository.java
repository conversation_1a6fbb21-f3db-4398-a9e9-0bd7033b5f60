package com.biz.crm.tpm.business.activities.overallplan.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanScope;
import com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanScopeMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OverPlanScopeRepository extends ServiceImpl<OverallPlanScopeMapper, OverallPlanScope> {

    public List<OverallPlanScope> findListBySchemeCode(String schemeCode) {
        return this.lambdaQuery()
                .eq(OverallPlanScope::getSchemeCode, schemeCode).list();
    }

    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.remove(Wrappers.lambdaQuery(OverallPlanScope.class)
                .in(OverallPlanScope::getSchemeCode, schemeCodes));
    }

    public List<OverallPlanScope> findListBySchemeDetailCodeList(List<String> schemeDetailCodeList) {
        return this.lambdaQuery()
                .in(OverallPlanScope::getSchemeDetailCode, schemeDetailCodeList).list();
    }
}
