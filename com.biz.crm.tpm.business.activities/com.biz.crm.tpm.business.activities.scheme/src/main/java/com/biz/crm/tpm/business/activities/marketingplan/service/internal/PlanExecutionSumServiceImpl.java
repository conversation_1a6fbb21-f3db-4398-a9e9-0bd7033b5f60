package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalTagVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalTagVo;
import com.biz.crm.tpm.business.activities.marketingplan.dto.PlanExecutionSumEditDto;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanExecutionSum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.PlanExecutionSumAuditEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.PlanExecutionSumRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanExecutionSumService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanExecutionSumDetailVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanExecutionSumVo;
import com.biz.crm.tpm.business.activities.sdk.service.PlanExecutionService;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionPictureVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * @Author: yangrui
 * @Date: 2024-12-31 20:17
 */
@Service
@Slf4j
public class PlanExecutionSumServiceImpl implements PlanExecutionSumService {
    @Resource
    private PlanExecutionSumRepository planExecutionSumRepository;

    @Resource
    private TerminalTagVoService terminalTagVoService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private OrgVoService orgVoService;

    @Autowired(required = false)
    private PlanExecutionService planExecutionService;

    @Override
    public void savePlanExecutionSum(MarketingPlanCase marketingPlanCase) {
        try {
            save(marketingPlanCase);
        } catch (Exception e) {
            log.error("PlanExecutionSumServiceImpl.savePlanExecutionSum error msg{}",
                    e.getMessage(), e);
        }
    }

    private void save(MarketingPlanCase marketingPlanCase) {
        String schemeDetailCode = marketingPlanCase.getSchemeDetailCode();
        Assert.notBlank(schemeDetailCode, "方案明细编码不能为空");
        if (StrUtil.isBlank(marketingPlanCase.getTerminalCode())) {
            return;
        }
        // 判断汇总是否存在
        PlanExecutionSum pre = planExecutionSumRepository.queryBySchemeDetailCode(schemeDetailCode);
        if (Objects.nonNull(pre)) {
            // 已经生成过 不需要重复生成
            return;
        }
        PlanExecutionSum planExecutionSum = this.nebulaToolkitService
                .copyObjectByWhiteList(marketingPlanCase, PlanExecutionSum.class, HashSet.class, ArrayList.class);
        planExecutionSum.setId(null);
        planExecutionSum.setCreateTime(null);
        planExecutionSum.setCreateAccount(null);
        planExecutionSum.setCreateName(null);
        planExecutionSum.setModifyTime(null);
        planExecutionSum.setModifyAccount(null);
        planExecutionSum.setModifyName(null);
        planExecutionSum.setRemark(null);

        // 所属大区
        // 费用使用部门
        String departmentCode = marketingPlanCase.getBelongDepartmentCode();
        if (StrUtil.isNotBlank(departmentCode)) {
            String divisionOrg = "";
            String divisionOrgName = "";
            List<OrgVo> org = orgVoService.findByOrgCodes(CollUtil.newArrayList(departmentCode));
            if (CollUtil.isNotEmpty(org) && Objects.nonNull(org.get(0))
                    && StrUtil.equals(OrgTypeEnum.DIVISION.getKey(), org.get(0).getOrgType())) {
                divisionOrg = org.get(0).getOrgCode();
                divisionOrgName = org.get(0).getOrgName();
            } else {
                List<OrgVo> orgVos = orgVoService.findAllParentByOrgCode(departmentCode);
                // 找到大区那一层
                OrgVo division = CollUtil.isNotEmpty(orgVos) ?
                        orgVos.stream().filter(o ->
                                StrUtil.equals(OrgTypeEnum.DIVISION.getKey(), o.getOrgType()))
                                .findAny().orElse(null)
                        : null;
                if (Objects.nonNull(division)) {
                    divisionOrg = division.getOrgCode();
                    divisionOrgName = division.getOrgName();
                }
            }
            planExecutionSum.setDivisionOrgCode(divisionOrg);
            planExecutionSum.setDivisionOrgName(divisionOrgName);
        }

        // 是否百万终端
        String million = "百万终端";
        List<TerminalTagVo> terminalTagVos = terminalTagVoService.findByTerminalCodes(
                CollUtil.newHashSet(planExecutionSum.getTerminalCode()));
        boolean isMillion = CollUtil.isNotEmpty(terminalTagVos)
                && Objects.nonNull(
                terminalTagVos.stream().map(TerminalTagVo::getTagDescription).filter(
                        tag -> tag.contains(million)
                ).findAny().orElse(null));
        planExecutionSum.setMillionTerminal(isMillion ? 1 : 0);

        planExecutionSumRepository.save(planExecutionSum);
    }

    // 查询列表
    public Page<PlanExecutionSumVo> queryPage(Pageable pageable, PlanExecutionSumVo param) {
        Page<PlanExecutionSum> page = planExecutionSumRepository.queryPage(pageable, param);
        if (Objects.isNull(page) || CollUtil.isEmpty(page.getRecords())) {
            return new Page<>();
        }
        List<PlanExecutionSumVo> voList = (List<PlanExecutionSumVo>) nebulaToolkitService.copyCollectionByWhiteList(
                page.getRecords(), PlanExecutionSum.class, PlanExecutionSumVo.class, HashSet.class, ArrayList.class);
        voList.forEach(item -> item.setAuditResultDesc(PlanExecutionSumAuditEnum.getDesc(item.getAuditResult())));
        Page<PlanExecutionSumVo> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(voList);
        return result;
    }

    // 根据方案明细编码查询
    public PlanExecutionSumVo queryBySchemeDetailCode(String schemeDetailCode) {
        if (StrUtil.isBlank(schemeDetailCode)) {
            return null;
        }
        PlanExecutionSum planExecutionSum = planExecutionSumRepository.queryBySchemeDetailCode(schemeDetailCode);
        if (Objects.isNull(planExecutionSum)) {
            return null;
        }
        PlanExecutionSumVo planExecutionSumVo =
                nebulaToolkitService.copyObjectByWhiteList(planExecutionSum, PlanExecutionSumVo.class, HashSet.class, ArrayList.class);
        planExecutionSumVo.setAuditResultDesc(PlanExecutionSumAuditEnum.getDesc(planExecutionSumVo.getAuditResult()));
        return planExecutionSumVo;
    }

    public PlanExecutionSumDetailVo queryDetail(String id) {
        if (StrUtil.isBlank(id)) {
            return null;
        }
        PlanExecutionSum planExecutionSum = planExecutionSumRepository.getById(id);
        if (Objects.isNull(planExecutionSum)) {
            return null;
        }
        PlanExecutionSumDetailVo detailVo = new PlanExecutionSumDetailVo();
        detailVo.setTerminalCode(planExecutionSum.getTerminalCode());
        detailVo.setTerminalName(planExecutionSum.getTerminalName());
        detailVo.setActStandards(planExecutionSum.getActStandards());
        detailVo.setAuditResult(planExecutionSum.getAuditResult());
        detailVo.setAuditResultDesc(PlanExecutionSumAuditEnum.getDesc(planExecutionSum.getAuditResult()));

        // 执行结果
        List<PlanExecutionVo> planExecutionVos =
                planExecutionService.findBySchemeDetailCode(planExecutionSum.getSchemeDetailCode());
        if (CollUtil.isEmpty(planExecutionVos)) {
            return detailVo;
        }
        List<PlanExecutionSumDetailVo.PlanExecutionSumDetailResultVo> resultVos = new ArrayList<>();
        for (PlanExecutionVo planExecutionVo : planExecutionVos) {
            List<PlanExecutionPictureVo> planExecutionPictureVos = planExecutionVo.getPictureList();
            if (CollUtil.isEmpty(planExecutionPictureVos)) {
                continue;
            }
            List<PlanExecutionSumDetailVo.PlanExecutionSumDetailResultVo> tempResultVos =
                    (List<PlanExecutionSumDetailVo.PlanExecutionSumDetailResultVo>) nebulaToolkitService.copyCollectionByWhiteList(
                            planExecutionPictureVos, PlanExecutionPictureVo.class, PlanExecutionSumDetailVo.PlanExecutionSumDetailResultVo.class, HashSet.class, ArrayList.class);
            for (PlanExecutionSumDetailVo.PlanExecutionSumDetailResultVo tempResultVo : tempResultVos) {
                tempResultVo.setExecutor(planExecutionVo.getExecutor());
                tempResultVo.setExecuteDate(planExecutionVo.getExecuteDate());
                tempResultVo.setResult(planExecutionVo.getResult());
                tempResultVo.setResultDesc(planExecutionVo.getResultDesc());
                tempResultVo.setWatermarkStr(planExecutionVo.getWatermarkStr());
                tempResultVo.setWatermarkFlag(planExecutionVo.getWatermarkFlag());
            }
            resultVos.addAll(tempResultVos);
        }
        detailVo.setExeResultList(resultVos);
        return detailVo;
    }

    // 编辑
    public void edit(PlanExecutionSumEditDto dto) {
        Assert.notBlank(dto.getId(), "主键不能为空");
        PlanExecutionSum planExecutionSum = planExecutionSumRepository.getById(dto.getId());
        Assert.notNull(planExecutionSum, "主键id有误");
        Assert.isNull(planExecutionSum.getAuditResult(), "不能重复审核");
        planExecutionSum.setAuditResult(dto.getAuditResult());
        planExecutionSum.setRemark(dto.getRemark());
        planExecutionSumRepository.updateById(planExecutionSum);
    }
}
