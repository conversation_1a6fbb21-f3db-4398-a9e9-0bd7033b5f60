package com.biz.crm.tpm.business.activities.materialdemand.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MaterialDemandCollectFieldsEnum {
    demandCode("demandCode"),
    demandName("demandName"),
    remark("remark"),
    title("title"),
    ;

    private String dictCode;

    public static MaterialDemandCollectFieldsEnum findByCode(String code) {
        Optional<MaterialDemandCollectFieldsEnum> first = Stream.of(MaterialDemandCollectFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}