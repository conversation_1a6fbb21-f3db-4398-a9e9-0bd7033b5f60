package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.entity.PlanBigDateExcution
 * @description: 大日期回调执行计划 商品明细采集图片
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 13:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_plan_big_date_execution_collect_detail_picture")
@Table(
        name = "tpm_plan_big_date_execution_collect_detail_picture",
        indexes = {
                @Index(name = "tpm_plan_big_date_execution_collect_detail_picture_index0", columnList = "collect_id"),
                @Index(name = "tpm_plan_big_date_execution_collect_detail_picture_index1", columnList = "detail_id")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_plan_big_date_execution_collect_detail_picture", comment = "大日期回调执行计划商品明细采集图片")
@ApiModel(value = "PlanBigDateExecutionCollectDetailPicture", description = "大日期回调执行计划商品明细采集图片")
public class PlanBigDateExecutionCollectDetailPicture extends FileEntity {

    @ApiModelProperty("大日期回调采集id")
    @Column(name = "collect_id", columnDefinition = "varchar(32) comment '效期采集ID'")
    private String collectId;

    @ApiModelProperty("产品父id")
    @Column(name = "detail_id", columnDefinition = "varchar(32) comment '产品父id'")
    private String detailId;

}
