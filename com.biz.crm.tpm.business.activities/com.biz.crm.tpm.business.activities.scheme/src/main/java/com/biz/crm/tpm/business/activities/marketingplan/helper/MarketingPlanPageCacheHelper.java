package com.biz.crm.tpm.business.activities.marketingplan.helper;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.tpm.business.activities.marketingplan.constant.MarketingPlanConstant;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCaseStrategy;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/11 15:52
 */
@Component
@Slf4j
public class MarketingPlanPageCacheHelper extends BusinessPageCacheHelper<MarketingPlanCaseVo, MarketingPlanCaseVo> {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private MarketingPlanCaseService marketingPlanCaseService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private List<MarketingPlanCaseStrategy> strategyList;


    @Override
    public String getCacheKeyPrefix() {
        return MarketingPlanConstant.MARKETING_PLAN_CACHE_PAGE;
    }

    @Override
    public Class<MarketingPlanCaseVo> getDtoClass() {
        return MarketingPlanCaseVo.class;
    }

    @Override
    public Class<MarketingPlanCaseVo> getVoClass() {
        return MarketingPlanCaseVo.class;
    }

    private static final String COLUM = ":";

    @Override
    public List<MarketingPlanCaseVo> findDtoListFromRepository(MarketingPlanCaseVo vo, String cacheKey) {
        if (ObjectUtils.isEmpty(vo.getId())) {
            return Lists.newArrayList();
        }
        String[] splits = cacheKey.split(COLUM);
        String caseType = splits[1];
        String unionKey = splits[0];
        Map<String, List<MarketingPlanCaseVo>> map = this.findListByRepository(vo, cacheKey);
        for (MarketingPlanCaseTypeEnum value : MarketingPlanCaseTypeEnum.values()) {
            //除了当前类型的 全部载入缓存
            if (!value.getCode().equals(caseType)) {
                if (map.containsKey(value.getCode())) {
                    String key = unionKey + COLUM + value.getCode();
                    putCache(key, map.getOrDefault(value.getCode(), Lists.newArrayList()));
                }
            }
        }
        return map.get(caseType);
    }


    public Map<String, List<MarketingPlanCaseVo>> findListByRepository(MarketingPlanCaseVo vo, String cacheKey) {
        if (ObjectUtils.isEmpty(vo.getId())) {
            return Maps.newHashMap();
        }
        return marketingPlanCaseService.findListBySchemeId(vo.getId(),vo.getCustomerName());
    }

    @Override
    public List<MarketingPlanCaseVo> newItem(String cacheKey, List<MarketingPlanCaseVo> itemList) {
        MarketingPlanCaseVo vo = new MarketingPlanCaseVo();
        vo.setId(UuidCrmUtil.general());
        vo.setSort(System.currentTimeMillis());
        return Lists.newArrayList(vo);
    }

    @Override
    public List<MarketingPlanCaseVo> copyItem(String cacheKey, List<MarketingPlanCaseVo> itemList) {
//        List<MarketingPlanCaseVo> list = (List<MarketingPlanCaseVo>) nebulaToolkitService.copyCollectionByWhiteList(itemList, MarketingPlanCaseVo.class, MarketingPlanCaseVo.class,
//                HashSet.class, ArrayList.class, "productAndItemList", "productList", "itemList","executeExampleList","closeCaseExampleList",
//                "cooperateTypeList","customerTagList","terminalTagList");
//        CopyOptions copyOptions = new CopyOptions(MarketingPlanCaseVo.class, false,"productAndItemList", "productList", "itemList","executeExampleList","closeCaseExampleList",
//            "cooperateTypeList","customerTagList","terminalTagList");
        List<MarketingPlanCaseVo> list = JSONObject.parseArray(JSONObject.toJSONString(itemList), MarketingPlanCaseVo.class);
        for (MarketingPlanCaseVo caseVo : list) {
            caseVo.setId(UuidCrmUtil.general());
            caseVo.setSchemeDetailCode(null);
            caseVo.setOriginalSchemeDetailCode(null);
            caseVo.setOriginalSchemeCode(null);
            caseVo.setSort(System.currentTimeMillis());
        }
        return list;
    }


    /**
     * 导入新增数据
     *
     * @param cacheKey
     * @param map
     * @return
     */
    @SneakyThrows
    public void importNewItem(String cacheKey, Map<String, List<MarketingPlanCaseVo>> map, String years, String schemeCode, String originalSchemeCode, List<String> loginUserOneLevelOrgCodes, String loginUserName) {
        for (Map.Entry<String, List<MarketingPlanCaseVo>> entry : map.entrySet()) {
            String key = cacheKey + COLUM + entry.getKey();

            String redisCacheIdKey = this.getRedisCacheIdKey(key);
            String redisCacheDataKey = this.getRedisCacheDataKey(key);

            List<MarketingPlanCaseVo> itemList = entry.getValue();
            //导入新增数据
            for (MarketingPlanCaseVo newItem : itemList) {
                newItem.setId(UuidCrmUtil.general());
            }

//            MarketingCheckCaseComponent bean = ApplicationContextHolder.getContext().getBean(MarketingCheckCaseComponent.class);
//            Future<List<MarketingPlanCaseVo>> future = bean.checkCaseList(itemList, entry.getKey(),
//                    years, schemeCode, originalSchemeCode, key);
//
//            List<MarketingPlanCaseVo> caseVoList = future.get();

            //大方向校验
            List<MarketingPlanCaseVo> caseVoList = marketingPlanCaseService.checkPlanCaseList(itemList, years, loginUserOneLevelOrgCodes, loginUserName);
            //分模板校验
            for (MarketingPlanCaseStrategy strategy : strategyList) {
                if (strategy.getCaseType().equals(entry.getKey())) {
                    strategy.checkCaseList(caseVoList, years, schemeCode, originalSchemeCode, cacheKey);
                }
            }

            //先排序处理
            caseVoList = caseVoList.stream().sorted(Comparator.comparing(MarketingPlanCaseVo::getCheckFlag)
                    .thenComparing(Comparator.comparing(MarketingPlanCaseVo::getSort).reversed())).collect(Collectors.toList());

            Object[] newIdArr = caseVoList.stream().map(this::getDtoKey).toArray();
            redisService.lPushAll(redisCacheIdKey, this.getExpireTime(), newIdArr);

            Map<Object, MarketingPlanCaseVo> updateMap = caseVoList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
            redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
            redisService.expire(redisCacheDataKey, this.getExpireTime());
        }

    }


    @Override
    public List<MarketingPlanCaseVo> dtoListToVoList(List<MarketingPlanCaseVo> marketingPlanCaseVos) {
        return marketingPlanCaseVos.stream().sorted(Comparator.comparing(MarketingPlanCaseVo::getCheckFlag,
                Comparator.nullsFirst(Comparator.naturalOrder())).thenComparing(
                Comparator.comparing(MarketingPlanCaseVo::getSort,
                        Comparator.nullsFirst(Comparator.naturalOrder())).reversed())).collect(Collectors.toList());
    }

    @Override
    public Object getDtoKey(MarketingPlanCaseVo vo) {
        return vo.getId();
    }

    @Override
    public String getCheckedStatus(MarketingPlanCaseVo vo) {
        return vo.getChecked();
    }
}
