package com.biz.crm.tpm.business.activities.marketingplan.service.strategy;

import com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialCostVoService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingTransportEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanGainsAndLossesService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectProjectEnum;
import com.biz.crm.tpm.business.activities.regioncollect.service.strategy.MarketingEstimationBuilder;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/21 13:53
 */
@Slf4j
public class MarketingSchemeEstimationAbstractBuilder extends MarketingEstimationBuilder<List<String>, String> {


    private static final String column = ":";

    private List<RegionCollectMarketingEstimationVo> dataList = Lists.newArrayList();

    private List<MarketingSalesPlanVo> salesPlans;

    private String orgCode;

    private List<CostBudgetIncomeVo> incomeVos;

    private List<MarketingPlanCase> planCaseList;

    private MarketingSalesPlanService marketingSalesPlanService;

    private MaterialVoService materialVoService;

    private CostBudgetIncomeService costBudgetIncomeService;

    private MaterialCostVoService materialCostVoService;

    private MarketingPlanCaseRepository caseRepository;

    private CostBudgetVoService costBudgetVoService;

    private MarketingPlanCaseService marketingPlanCaseService;

    private OrgVoService orgVoService;

    private MarketingPlanGainsAndLossesService marketingPlanGainsAndLossesService;

    private PublicShareRatioService publicShareRatioService;

    private ProductPhaseVoService productPhaseVoService;

    private BudgetSubjectsVoService budgetSubjectsVoService;

    //预计收入
    private RegionCollectMarketingEstimationVo estimationRevenueData = new RegionCollectMarketingEstimationVo();

    //生产成本
    private RegionCollectMarketingEstimationVo productionCostData = new RegionCollectMarketingEstimationVo();

    //产品运输费用
    private RegionCollectMarketingEstimationVo productTransportCostData = new RegionCollectMarketingEstimationVo();

    //周边运输费用
    private RegionCollectMarketingEstimationVo peripheryTransportData = new RegionCollectMarketingEstimationVo();

    //活动大类
    private List<RegionCollectMarketingEstimationVo> categoryDataList = Lists.newArrayList();

    //营销费用小计
    private RegionCollectMarketingEstimationVo marketingCostData = new RegionCollectMarketingEstimationVo();

    //人工、差旅
    private RegionCollectMarketingEstimationVo artificialTravelOnBusinessData = new RegionCollectMarketingEstimationVo();

    //费用合计
    private RegionCollectMarketingEstimationVo costData = new RegionCollectMarketingEstimationVo();

    private RegionCollectMarketingEstimationVo profit = new RegionCollectMarketingEstimationVo();

    private RegionCollectMarketingEstimationVo costShiftData = new RegionCollectMarketingEstimationVo();

    private Set<String> orgCodes = Sets.newHashSet();

    private List<MarketingPlanGainsAndLosses> gainsAndLossesList = Lists.newArrayList();


    private Map<String, BigDecimal> materialCostMap = Maps.newHashMap();

    private MdmCostCenterVoService costCenterVoService;

    private List<MarketingPlanCase> caseList = Lists.newArrayList();


    /**
     * 过滤重复的随单政策，只取时间最大的一个
     */
    private List<MarketingPlanCase> filterNewMatchingList = Lists.newArrayList();


    public static MarketingSchemeEstimationAbstractBuilder builder(ApplicationContext context, String orgCode) {
        MarketingSchemeEstimationAbstractBuilder builder = new MarketingSchemeEstimationAbstractBuilder();
        builder.orgCode = orgCode;
        builder.marketingSalesPlanService = context.getBean(MarketingSalesPlanService.class);
        builder.materialVoService = context.getBean(MaterialVoService.class);
        builder.costBudgetIncomeService = context.getBean(CostBudgetIncomeService.class);
        builder.materialCostVoService = context.getBean(MaterialCostVoService.class);
        builder.caseRepository = context.getBean(MarketingPlanCaseRepository.class);
        builder.costBudgetVoService = context.getBean(CostBudgetVoService.class);
        builder.marketingPlanCaseService = context.getBean(MarketingPlanCaseService.class);
        builder.orgVoService = context.getBean(OrgVoService.class);
        builder.marketingPlanGainsAndLossesService = context.getBean(MarketingPlanGainsAndLossesService.class);
        builder.publicShareRatioService = context.getBean(PublicShareRatioService.class);
        builder.costCenterVoService = context.getBean(MdmCostCenterVoService.class);
        builder.productPhaseVoService = context.getBean(ProductPhaseVoService.class);
        builder.budgetSubjectsVoService = context.getBean(BudgetSubjectsVoService.class);
        return builder;
    }


    /**
     * 预计收入
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder estimationRevenue() {
        estimationRevenueData.setProjectCode(RegionCollectProjectEnum.estimation_revenue.getCode());
        estimationRevenueData.setProjectName(RegionCollectProjectEnum.estimation_revenue.getName());
        estimationRevenueData.setSort(RegionCollectProjectEnum.estimation_revenue.getSort());

        estimationRevenueData.setDifferenceRatio(BigDecimal.ZERO);
        estimationRevenueData.setDifferenceAmount(BigDecimal.ZERO);

        estimationRevenueData.setEstimateAmount(BigDecimal.ZERO);
        estimationRevenueData.setBudgetAmount(BigDecimal.ZERO);

        //查询客户损益
        this.gainsAndLossesList = marketingPlanGainsAndLossesService.findListBySchemeCodes(schemeCode);

        this.caseList = marketingPlanCaseService.findListBySchemeCodes(schemeCode);

        for (MarketingPlanCase planCase : caseList) {
            //设置一个默认值，为空的情况下
            planCase.setIsContractCost(ObjectUtils.defaultIfNull(planCase.getIsContractCost(), BooleanEnum.FALSE.getCapital()));
            if (MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(planCase.getCaseType())) {
                if (CollectionUtils.isNotEmpty(planCase.getProductList())) {
                    planCase.setLevelProductCode(planCase.getProductList().get(0).getCode());
                } else {
                    planCase.setLevelProductCode(planCase.getLevelList().get(0).getCode());
                }
            }
        }


        salesPlans = marketingSalesPlanService.findListBySchemeCodes(schemeCode);

        List<String> costCenterCodes = salesPlans.stream().map(MarketingSalesPlanVo::getCostCenterCode).distinct().collect(Collectors.toList());

        List<String> costCenterOrgCodes = costCenterVoService.findOrgCodesByCostCenterCodes(costCenterCodes);
        if (CollectionUtils.isNotEmpty(costCenterOrgCodes)) {
            orgCodes.addAll(costCenterOrgCodes);
        }
        List<String> bearDepartmentCodes = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBearDepartmentCode()))
                .map(MarketingPlanCase::getBearDepartmentCode).distinct().collect(Collectors.toList());
        orgCodes.addAll(bearDepartmentCodes);

        BigDecimal estimationAmount = salesPlans.stream()
                .map(x -> Optional.ofNullable(x.getEstimatedCost()).orElse(BigDecimal.ZERO))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 过滤并按客户代码、归属部门、产品代码进行分组
        Map<String, List<MarketingPlanCase>> filterNewMatchingCaseMap = this.caseList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()) &&
                        ObjectUtils.isNotEmpty(x.getCustomerCode()) &&
                        ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .collect(Collectors.groupingBy(x -> x.getCustomerCode() + x.getBelongDepartmentCode() + x.getLevelProductCode() + x.getIsContractCost()));

        // 从每个分组中选择结束日期最大的对象
        this.filterNewMatchingList = filterNewMatchingCaseMap.values().stream()
                .map(cases -> cases.stream().max(Comparator.comparing(MarketingPlanCase::getEndDate)))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        //删除随单搭赠的数据，重新放入过滤好的
        List<MarketingPlanCase> excludeMatchGiftCaseList = caseList.stream().filter(x -> !MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(excludeMatchGiftCaseList)) {
            excludeMatchGiftCaseList = Lists.newArrayList();
        }
        excludeMatchGiftCaseList.addAll(filterNewMatchingList);
        this.caseList = excludeMatchGiftCaseList;

        //此处还需要单独处理一下随单搭赠的数据
        BigDecimal policyNoTaxApplyAmount = filterNewMatchingList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .map(x -> Optional.ofNullable(x.getNoTaxApplyAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        estimationAmount = estimationAmount.add(policyNoTaxApplyAmount);

        //预计达成-转换为万元
        estimationRevenueData.setEstimateAmount(estimationAmount);
        //预算达成
        this.incomeVos = costBudgetIncomeService.findIncomeAmountByOrgCodeAndYears(Lists.newArrayList(orgCodes), years);

        List<String> itemCodes = incomeVos.stream().map(CostBudgetIncomeVo::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
        Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> null == v.getTaxRate() ? BigDecimal.ZERO : v.getTaxRate()));
        BigDecimal budgetAmount = incomeVos.stream().filter(e -> e.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode()))
                .map(x -> Optional.ofNullable(x.getIncomeAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(x.getItemCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP))
                .collect(Collectors.reducing(BigDecimal.ZERO, BigDecimal::add));

        estimationRevenueData.setBudgetAmount(budgetAmount);

        dataList.add(estimationRevenueData);
        return this;
    }

    /**
     * 生产成本
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder productionCost() {
        productionCostData.setProjectCode(RegionCollectProjectEnum.production_cost.getCode());
        productionCostData.setProjectName(RegionCollectProjectEnum.production_cost.getName());
        productionCostData.setSort(RegionCollectProjectEnum.production_cost.getSort());

//        productionCostData.setDifferenceRatio(BigDecimal.ZERO);
        productionCostData.setDifferenceAmount(BigDecimal.ZERO);
        productionCostData.setEstimateAmount(BigDecimal.ZERO);
//        productionCostData.setEstimateRatio(BigDecimal.ZERO);
//        productionCostData.setBudgetRatio(BigDecimal.ZERO);
        productionCostData.setBudgetAmount(BigDecimal.ZERO);

        Set<String> companyCodeSet = salesPlans.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode()))
                .map(x -> x.getCompanyCode())
                .collect(Collectors.toSet());
        Set<String> materialCodes = salesPlans.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                .map(MarketingSalesPlanVo::getMaterialCode).collect(Collectors.toSet());
        MaterialSearchDto searchDto = new MaterialSearchDto();
        searchDto.setMaterialCodeSet(materialCodes);
        searchDto.setCompanyCodeSet(companyCodeSet);
        List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
        this.materialCostMap = materialVoList.stream()
                .collect(Collectors.toMap(
                        x -> x.getMaterialCode(),
                        x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO),
                        (existingValue, newValue) -> existingValue
                ));
        Map<String, BigDecimal> materialSalesVolumeMap = salesPlans.stream().collect(Collectors.groupingBy(MarketingSalesPlanVo::getMaterialCode,
                Collectors.mapping(MarketingSalesPlanVo::getEstimatedSalesVolume, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        BigDecimal productionCosts = BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> entry : materialSalesVolumeMap.entrySet()) {
            BigDecimal costPrice = materialCostMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            productionCosts = productionCosts.add(costPrice.multiply(entry.getValue()));
        }
        //搭赠生产成本
        BigDecimal policyProductCosts = this.filterNewMatchingList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .map(x -> Optional.ofNullable(x.getPolicyMaterialCostPrice()).orElse(BigDecimal.ZERO)
                        .multiply(Optional.ofNullable(x.getGiftQuantity()).orElse(BigDecimal.ZERO)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal estimateAmount = productionCosts.add(policyProductCosts);
        productionCostData.setEstimateAmount(estimateAmount);

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = productionCostData.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
//        productionCostData.setEstimateRatio(estimateRatio);


        BigDecimal budgetAmount = incomeVos.stream().map(x -> Optional.ofNullable(x.getCostAmount()).orElse(BigDecimal.ZERO))
                .collect(Collectors.reducing(BigDecimal.ZERO, BigDecimal::add));
        //生产成本
        productionCostData.setBudgetAmount(budgetAmount);

        BigDecimal budgetEstimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetEstimateRatio = productionCostData.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
//        productionCostData.setBudgetRatio(budgetEstimateRatio);

        dataList.add(productionCostData);
        return this;
    }

    /**
     * 毛利率
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder grossProfitMargin() {
        RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
        data.setProjectCode(RegionCollectProjectEnum.gross_profit_margin.getCode());
        data.setProjectName(RegionCollectProjectEnum.gross_profit_margin.getName());
        data.setSort(RegionCollectProjectEnum.gross_profit_margin.getSort());

        data.setDifferenceRatio(BigDecimal.ZERO);
        data.setDifferenceAmount(BigDecimal.ZERO);
        data.setEstimateAmount(BigDecimal.ZERO);
        data.setBudgetAmount(BigDecimal.ZERO);

        if (estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal estimationAmount = BigDecimal.ONE.subtract(productionCostData.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 2, BigDecimal.ROUND_HALF_DOWN));
            data.setEstimateAmount(estimationAmount.multiply(BigDecimal.valueOf(10000)));
        }
        if (estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal budgetAmount = BigDecimal.ONE.subtract(productionCostData.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 2, BigDecimal.ROUND_HALF_DOWN));
            data.setBudgetAmount(budgetAmount.multiply(BigDecimal.valueOf(10000)));
        }
        dataList.add(data);
        return this;
    }

    /**
     * 产品运输费用
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder productTransportCost() {
        productTransportCostData.setProjectCode(RegionCollectProjectEnum.product_transport_cost.getCode());
        productTransportCostData.setProjectName(RegionCollectProjectEnum.product_transport_cost.getName());
        productTransportCostData.setSort(RegionCollectProjectEnum.product_transport_cost.getSort());

        productTransportCostData.setDifferenceRatio(BigDecimal.ZERO);
        productTransportCostData.setDifferenceAmount(BigDecimal.ZERO);
        productTransportCostData.setEstimateAmount(BigDecimal.ZERO);
        productTransportCostData.setEstimateRatio(BigDecimal.ZERO);
        productTransportCostData.setBudgetRatio(BigDecimal.ZERO);
        productTransportCostData.setBudgetAmount(BigDecimal.ZERO);


        //销售计划的物料-找到物料成本价，用销售计划上的客户+成本中心+年月匹配得到物料 在用预估销售额*物料成本价
        Map<String, BigDecimal> materialSalesVolumeMap = salesPlans.stream().collect(Collectors.groupingBy(MarketingSalesPlanVo::getMaterialCode,
                Collectors.mapping(MarketingSalesPlanVo::getEstimatedSalesVolume, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        BigDecimal productionCosts = BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> entry : materialSalesVolumeMap.entrySet()) {
            BigDecimal costPrice = materialCostMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            productionCosts = productionCosts.add(costPrice.multiply(entry.getValue()));
        }
        //生产成本
        List<String> costCenterCodes = salesPlans.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCostCenterCode()))
                .map(MarketingSalesPlanVo::getCostCenterCode).distinct().collect(Collectors.toList());
        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(costCenterCodes);
        Map<String, MdmCostCenterVo> costCenterMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(costCenterVoList)) {
            costCenterMap = costCenterVoList.stream().collect(Collectors.toMap(x -> x.getCostCenterCode(), v -> v));
        }

        //产品运输费用计算
        List<String> companyCodes = Lists.newArrayList();
        for (MarketingSalesPlanVo salesPlan : salesPlans) {
            if (costCenterMap.containsKey(salesPlan.getCostCenterCode())) {
                MdmCostCenterVo costCenterVo1 = costCenterMap.get(salesPlan.getCostCenterCode());
                companyCodes.add(costCenterVo1.getCompanyCode());
                salesPlan.setCostCenterCompanyCode(costCenterVo1.getCompanyCode());
            }
        }
        String year = years.split("-")[0];
        //查询物料成本
        List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialSalesVolumeMap.keySet(), companyCodes, year);
        Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                x.getCompanyCode() + column + x.getYearStr(), Function.identity()));
        //产品运输费用 先计算搭赠的
        BigDecimal productTransport = calPolicyProductTransportCost();
        for (MarketingSalesPlanVo plan : salesPlans) {
            String key = plan.getMaterialCode() + column + plan.getCompanyCode() + column + year;
            if (materialCostMap.containsKey(key)) {
                MaterialCostVo materialCostVo = materialCostMap.get(key);
                BigDecimal price = BigDecimal.ZERO;
                if (MarketingTransportEnum.bulk_cargo.getCode().equals(plan.getTransportType())) {
                    price = materialCostVo.getLargeLogisticsPrice();
                } else if (MarketingTransportEnum.express.getCode().equals(plan.getTransportType())) {
                    price = materialCostVo.getExpressLogisticsPrice();
                } else if (MarketingTransportEnum.cold_chain.getCode().equals(plan.getTransportType())) {
                    price = materialCostVo.getColdLogisticsPrice();
                } else if (MarketingTransportEnum.factory.getCode().equals(plan.getTransportType())) {
                    price = materialCostVo.getFactoryStraightPrice();
                }
                productTransport = productTransport.add(price.multiply(plan.getEstimatedSalesVolume()));

            }
        }
        productTransportCostData.setEstimateAmount(productTransport);
        //产品运输方式-预算达成金额
        BigDecimal budgetAmount = incomeVos.stream().map(x -> Optional.ofNullable(x.getLogisticsAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        productTransportCostData.setBudgetAmount(budgetAmount);

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(productTransportCostData.getEstimateAmount())
                && Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = productTransportCostData.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        productTransportCostData.setEstimateRatio(estimateRatio);


        BigDecimal budgetRatio = BigDecimal.ZERO;
        if (Objects.nonNull(productTransportCostData.getBudgetAmount())
                && Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetRatio = productTransportCostData.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        productTransportCostData.setBudgetRatio(budgetRatio);

        dataList.add(productTransportCostData);
        return this;
    }


    /**
     * 搭赠运输费用
     */
    public BigDecimal calPolicyProductTransportCost() {
        String year = years.split("-")[0];
        List<MarketingPlanCase> policyCaseList = this.filterNewMatchingList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()) && ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(policyCaseList)) return BigDecimal.ZERO;
        Set<String> materialCodeSet = policyCaseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getPolicyProductCode()))
                .map(MarketingPlanCase::getPolicyProductCode).collect(Collectors.toSet());
        List<String> companyCodes = policyCaseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode()))
                .map(MarketingPlanCase::getCompanyCode).distinct().collect(Collectors.toList());

        Map<String, MarketingSalesPlanVo> cusProductSalesPlanMap = salesPlans.stream().collect(Collectors.toMap(x -> x.getCustomerCode() + x.getProductCode(), Function.identity(), (a, b) -> a));
        for (MarketingPlanCase aCase : policyCaseList) {
            String key = aCase.getCustomerCode() + aCase.getPolicyProductCode();
            if (cusProductSalesPlanMap.containsKey(key)) {
                aCase.setTransportType(cusProductSalesPlanMap.get(key).getTransportType());
            }
        }
        List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialCodeSet, companyCodes, year);
        Map<String, MaterialCostVo> materialCostVoMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                x.getCompanyCode() + column + x.getYearStr(), Function.identity()));

        Map<String, BigDecimal> materialCostMap = Maps.newHashMap();
        for (MarketingPlanCase planCase : policyCaseList) {
            String key = planCase.getPolicyProductCode() + column + planCase.getCompanyCode() + column + year;
            if (materialCostVoMap.containsKey(key)) {
                MaterialCostVo materialCostVo = materialCostVoMap.get(key);
                BigDecimal price = BigDecimal.ZERO;
                if (MarketingTransportEnum.bulk_cargo.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getLargeLogisticsPrice();
                } else if (MarketingTransportEnum.express.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getExpressLogisticsPrice();
                } else if (MarketingTransportEnum.cold_chain.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getColdLogisticsPrice();
                } else if (MarketingTransportEnum.factory.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getFactoryStraightPrice();
                }
                materialCostMap.put(planCase.getPolicyProductCode(), price);

            }
        }

        BigDecimal amount = BigDecimal.ZERO;
        for (MarketingPlanCase aCase : policyCaseList) {
            BigDecimal transportAmount = materialCostMap.getOrDefault(aCase.getPolicyProductCode(), BigDecimal.ZERO);
            amount = amount.add(transportAmount.multiply(ObjectUtils.defaultIfNull(aCase.getGiftQuantity(), BigDecimal.ZERO)));
        }
        return amount;
    }


    /**
     * 周边运输费用
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder peripheryTransport() {
        peripheryTransportData.setProjectCode(RegionCollectProjectEnum.periphery_transport.getCode());
        peripheryTransportData.setProjectName(RegionCollectProjectEnum.periphery_transport.getName());
        peripheryTransportData.setSort(RegionCollectProjectEnum.periphery_transport.getSort());

        peripheryTransportData.setDifferenceRatio(BigDecimal.ZERO);
        peripheryTransportData.setDifferenceAmount(BigDecimal.ZERO);
        peripheryTransportData.setEstimateAmount(BigDecimal.ZERO);
        peripheryTransportData.setEstimateRatio(BigDecimal.ZERO);
        peripheryTransportData.setBudgetRatio(BigDecimal.ZERO);
        peripheryTransportData.setBudgetAmount(BigDecimal.ZERO);

        BigDecimal estimateAmount = this.gainsAndLossesList.stream().map(x -> Optional.ofNullable(x.getPeripheryTransportCost()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        peripheryTransportData.setEstimateAmount(estimateAmount);

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = peripheryTransportData.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        peripheryTransportData.setEstimateRatio(estimateRatio);

        BigDecimal budgetRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetRatio = peripheryTransportData.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        peripheryTransportData.setBudgetRatio(budgetRatio);


        List<MarketingPlanCase> caseList = caseRepository.findListBySchemeCodes(schemeCode);
        //获取原方案
        this.planCaseList = caseList;
        //TODO 预计达成另算
        dataList.add(peripheryTransportData);
        return this;
    }

    /**
     * 运输费用合计
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder transportTotal() {
        RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
        data.setProjectCode(RegionCollectProjectEnum.transport_total.getCode());
        data.setProjectName(RegionCollectProjectEnum.transport_total.getName());
        data.setSort(RegionCollectProjectEnum.transport_total.getSort());

        data.setDifferenceRatio(BigDecimal.ZERO);
        data.setDifferenceAmount(BigDecimal.ZERO);
        data.setEstimateAmount(BigDecimal.ZERO);
        data.setEstimateRatio(BigDecimal.ZERO);
        data.setBudgetRatio(BigDecimal.ZERO);
        data.setBudgetAmount(BigDecimal.ZERO);

        data.setEstimateAmount(productTransportCostData.getEstimateAmount().add(peripheryTransportData.getEstimateAmount()));
        data.setBudgetAmount(productTransportCostData.getBudgetAmount().add(peripheryTransportData.getBudgetAmount()));

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = data.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        data.setEstimateRatio(estimateRatio);

        BigDecimal budgetRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetRatio = data.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        data.setBudgetRatio(budgetRatio);
        dataList.add(data);
        return this;
    }

    /**
     * 搭赠费用
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder giftCost() {
        return null;
    }

    /**
     * 活动大类费用合计
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder categoryCost() {
        Map<String, BigDecimal> categoryMap = planCaseList.stream().collect(Collectors.groupingBy(x -> x.getCategoryCode() + column + x.getCategoryName(),
                Collectors.mapping(e -> e.getLineNoTaxApplyAmount() == null? new BigDecimal(0) : e.getLineNoTaxApplyAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        List<String> categoryCodes = planCaseList.stream().map(MarketingPlanCase::getCategoryCode).distinct().collect(Collectors.toList());
        List<CostBudgetVo> costBudgetVos = costBudgetVoService.findListByOrgCodeAndCategoryCodesAndYears(Lists.newArrayList(orgCodes), categoryCodes, years);
        costBudgetVos = costBudgetVos.stream().filter(e -> e.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode())).collect(Collectors.toList());
        List<String> subjectCodes = costBudgetVos.stream().map(CostBudgetVo::getBudgetSubjectCode).collect(Collectors.toList());
        List<BudgetSubjectsVo> subjectVos = budgetSubjectsVoService.findByCodeList(subjectCodes);
        Map<String, BigDecimal> taxMap = subjectVos.stream().filter(e -> Objects.nonNull(e.getTaxRate())).collect(Collectors.toMap(BudgetSubjectsVo::getBudgetSubjectsCode, BudgetSubjectsVo::getTaxRate));
        Map<String, BigDecimal> costBudgetMap = costBudgetVos.stream().collect(Collectors.groupingBy(CostBudgetVo::getCategoryCode,
                Collectors.mapping(k -> k.getAdjustBalanceAmount().divide(new BigDecimal(1).add(taxMap.getOrDefault(k.getBudgetSubjectCode(), BigDecimal.ZERO)), 2,BigDecimal.ROUND_HALF_UP), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        Integer sort = 7;
        for (Map.Entry<String, BigDecimal> entry : categoryMap.entrySet()) {
            String[] keys = entry.getKey().split(column);
            RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
            data.setProjectCode(keys[0]);
            data.setProjectName(keys[1]);
            data.setEstimateAmount(entry.getValue());
            data.setBudgetAmount(costBudgetMap.getOrDefault(data.getProjectCode(), BigDecimal.ZERO));
            data.setSort(sort++);
            BigDecimal estimateRatio = BigDecimal.ZERO;
            if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                    && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                estimateRatio = data.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
            }
            data.setEstimateRatio(estimateRatio);

            BigDecimal budgetRatio = BigDecimal.ZERO;
            if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                    && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
                budgetRatio = data.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
            }
            data.setBudgetRatio(budgetRatio);

            categoryDataList.add(data);
        }
        dataList.addAll(categoryDataList);
        return this;
    }

    /**
     * 营销费用小计
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder marketingCostTotal() {
        marketingCostData.setProjectCode(RegionCollectProjectEnum.marketing_cost_total.getCode());
        marketingCostData.setProjectName(RegionCollectProjectEnum.marketing_cost_total.getName());
        marketingCostData.setSort(RegionCollectProjectEnum.marketing_cost_total.getSort());

        marketingCostData.setDifferenceRatio(BigDecimal.ZERO);
        marketingCostData.setDifferenceAmount(BigDecimal.ZERO);
        marketingCostData.setEstimateAmount(BigDecimal.ZERO);
        marketingCostData.setEstimateRatio(BigDecimal.ZERO);
        marketingCostData.setBudgetRatio(BigDecimal.ZERO);
        marketingCostData.setBudgetAmount(BigDecimal.ZERO);

        BigDecimal estimateAmount = categoryDataList.stream().map(RegionCollectMarketingEstimationVo::getEstimateAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal budgetAmount = categoryDataList.stream().map(RegionCollectMarketingEstimationVo::getBudgetAmount)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 预算费用管理当月已确认的未税金额
        List<String> orgCodes = caseList.stream().map(MarketingPlanCase::getBelongDepartmentCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CostBudgetVo> costBudgetVos = costBudgetVoService.findListByOrgCodesAndYears(orgCodes, years);
        List<CostBudgetVo> confirmedCostBudgetVos = costBudgetVos.stream().filter(e -> e.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode())).collect(Collectors.toList());
        List<String> subjectCodes = confirmedCostBudgetVos.stream().map(CostBudgetVo::getBudgetSubjectCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<BudgetSubjectsVo> subjectVos = budgetSubjectsVoService.findByCodeList(subjectCodes);
        Map<String, BigDecimal> taxRateMap = subjectVos.stream().filter(e -> Objects.nonNull(e.getTaxRate())).collect(Collectors.toMap(BudgetSubjectsVo::getBudgetSubjectsCode, BudgetSubjectsVo::getTaxRate));
        BigDecimal budgetAmount = confirmedCostBudgetVos.stream().map(x -> Optional.ofNullable(x.getAdjustBalanceAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(taxRateMap.get(x.getBudgetSubjectCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        marketingCostData.setEstimateAmount(estimateAmount);
        marketingCostData.setBudgetAmount(budgetAmount);

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = marketingCostData.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        marketingCostData.setEstimateRatio(estimateRatio);

        BigDecimal budgetRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetRatio = marketingCostData.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        marketingCostData.setBudgetRatio(budgetRatio);

        dataList.add(marketingCostData);
        return this;
    }

    /**
     * 边际贡献
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder marginalContribution() {
        RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
        data.setProjectCode(RegionCollectProjectEnum.marginal_contribution.getCode());
        data.setProjectName(RegionCollectProjectEnum.marginal_contribution.getName());
        data.setSort(RegionCollectProjectEnum.marginal_contribution.getSort());

        data.setDifferenceRatio(BigDecimal.ZERO);
        data.setDifferenceAmount(BigDecimal.ZERO);
        data.setEstimateAmount(BigDecimal.ZERO);
        data.setEstimateRatio(BigDecimal.ZERO);
        data.setBudgetRatio(BigDecimal.ZERO);
        data.setBudgetAmount(BigDecimal.ZERO);

        //毛利
        BigDecimal grossProfit = estimationRevenueData.getEstimateAmount().subtract(productionCostData.getEstimateAmount());
        //毛利-运输费用-营销费用小计
        BigDecimal estimateAmount = grossProfit.subtract(productTransportCostData.getEstimateAmount()).subtract(peripheryTransportData.getEstimateAmount())
                .subtract(marketingCostData.getEstimateAmount());
        data.setEstimateAmount(estimateAmount);

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = data.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        data.setEstimateRatio(estimateRatio);

        //毛利
        BigDecimal budgetGrossProfit = estimationRevenueData.getBudgetAmount().subtract(productionCostData.getBudgetAmount());
        //毛利-运输费用-营销费用小计
        BigDecimal budgetEstimateAmount = budgetGrossProfit.subtract(productTransportCostData.getBudgetAmount()).subtract(peripheryTransportData.getBudgetAmount())
                .subtract(marketingCostData.getBudgetAmount());
        data.setBudgetAmount(budgetEstimateAmount);

        BigDecimal budgetEstimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetEstimateRatio = data.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        data.setBudgetRatio(budgetEstimateRatio);

        dataList.add(data);
        return this;
    }

    /**
     * 人工、差旅等及其他分摊费用
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder artificialTravelOnBusiness() {
        artificialTravelOnBusinessData.setProjectCode(RegionCollectProjectEnum.artificial_travel_on_business.getCode());
        artificialTravelOnBusinessData.setProjectName(RegionCollectProjectEnum.artificial_travel_on_business.getName());
        artificialTravelOnBusinessData.setSort(RegionCollectProjectEnum.artificial_travel_on_business.getSort());

        artificialTravelOnBusinessData.setDifferenceRatio(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setDifferenceAmount(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setEstimateAmount(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setEstimateFixedPoint(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setEstimateRatio(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setBudgetRatio(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setBudgetFixedPoint(BigDecimal.ZERO);
        artificialTravelOnBusinessData.setBudgetAmount(BigDecimal.ZERO);

        //预计
        BigDecimal ratio = publicShareRatioService.findRatioByCondition(Lists.newArrayList(orgCode), years);
        if (ratio.compareTo(BigDecimal.ZERO) == 0) {
            List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(orgCode);
            List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            ratio = publicShareRatioService.findRatioByCondition(orgCodes, years);
        }
        BigDecimal estimateAmount = estimationRevenueData.getEstimateAmount().multiply(ratio);

        artificialTravelOnBusinessData.setEstimateAmount(estimateAmount);
        artificialTravelOnBusinessData.setEstimateFixedPoint(ratio);

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = artificialTravelOnBusinessData.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        artificialTravelOnBusinessData.setEstimateRatio(estimateRatio);

        //预算
        BigDecimal budgetRatio = publicShareRatioService.findRatioByCondition(Lists.newArrayList(orgCode), years);
        if (budgetRatio.compareTo(BigDecimal.ZERO) == 0) {
            List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(orgCode);
            List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            budgetRatio = publicShareRatioService.findRatioByCondition(orgCodes, years);
        }
        BigDecimal budgetEstimateAmount = estimationRevenueData.getBudgetAmount().multiply(budgetRatio);

        artificialTravelOnBusinessData.setBudgetAmount(budgetEstimateAmount);
        artificialTravelOnBusinessData.setBudgetFixedPoint(budgetRatio);

        BigDecimal budgetEstimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetEstimateRatio = artificialTravelOnBusinessData.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        artificialTravelOnBusinessData.setBudgetRatio(budgetEstimateRatio);

        dataList.add(artificialTravelOnBusinessData);
        return this;
    }

    /**
     * 费用合计
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder costTotal() {
        costData.setProjectCode(RegionCollectProjectEnum.cost_total.getCode());
        costData.setProjectName(RegionCollectProjectEnum.cost_total.getName());
        costData.setSort(RegionCollectProjectEnum.cost_total.getSort());

        costData.setDifferenceRatio(BigDecimal.ZERO);
        costData.setDifferenceAmount(BigDecimal.ZERO);
        costData.setEstimateAmount(BigDecimal.ZERO);
        costData.setEstimateRatio(BigDecimal.ZERO);
        costData.setBudgetRatio(BigDecimal.ZERO);
        costData.setBudgetAmount(BigDecimal.ZERO);

        BigDecimal estimateAmount = marketingCostData.getEstimateAmount().add(artificialTravelOnBusinessData.getEstimateAmount());
        costData.setEstimateAmount(estimateAmount);

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = costData.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        costData.setEstimateRatio(estimateRatio);

        //预算
        BigDecimal budgetEstimateAmount = marketingCostData.getBudgetAmount().add(artificialTravelOnBusinessData.getBudgetAmount());
        costData.setBudgetAmount(budgetEstimateAmount);

        BigDecimal budgetEstimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetEstimateRatio = costData.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        costData.setBudgetRatio(budgetEstimateRatio);
        dataList.add(costData);
        return this;
    }

    /**
     * 利润
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder profit() {
        profit.setProjectCode(RegionCollectProjectEnum.profit.getCode());
        profit.setProjectName(RegionCollectProjectEnum.profit.getName());
        profit.setSort(RegionCollectProjectEnum.profit.getSort());

        profit.setDifferenceRatio(BigDecimal.ZERO);
        profit.setDifferenceAmount(BigDecimal.ZERO);
        profit.setEstimateAmount(BigDecimal.ZERO);
        profit.setEstimateRatio(BigDecimal.ZERO);
        profit.setBudgetRatio(BigDecimal.ZERO);
        profit.setBudgetAmount(BigDecimal.ZERO);

        //毛利
        BigDecimal grossProfit = estimationRevenueData.getEstimateAmount().subtract(productionCostData.getEstimateAmount());

        BigDecimal estimateAmount = grossProfit.subtract(productTransportCostData.getEstimateAmount())
                .subtract(peripheryTransportData.getEstimateAmount())
                .subtract(costData.getEstimateAmount());
        profit.setEstimateAmount(estimateAmount);

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = profit.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        profit.setEstimateRatio(estimateRatio);

        //毛利
        BigDecimal budgetGrossProfit = estimationRevenueData.getBudgetAmount().subtract(productionCostData.getBudgetAmount());

        BigDecimal budgetEstimateAmount = budgetGrossProfit.subtract(productTransportCostData.getBudgetAmount())
                .subtract(peripheryTransportData.getBudgetAmount())
                .subtract(costData.getBudgetAmount());
        profit.setBudgetAmount(budgetEstimateAmount);

        BigDecimal budgetEstimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetEstimateRatio = profit.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        profit.setBudgetRatio(budgetEstimateRatio);

        dataList.add(profit);
        return this;
    }


    /**
     * 费用转移
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder costShift() {
        costShiftData.setProjectCode(RegionCollectProjectEnum.cost_shift.getCode());
        costShiftData.setProjectName(RegionCollectProjectEnum.cost_shift.getName());
        costShiftData.setSort(RegionCollectProjectEnum.cost_shift.getSort());

        costShiftData.setDifferenceRatio(BigDecimal.ZERO);
        costShiftData.setDifferenceAmount(BigDecimal.ZERO);
        costShiftData.setEstimateAmount(BigDecimal.ZERO);
        costShiftData.setEstimateRatio(BigDecimal.ZERO);
        costShiftData.setBudgetRatio(BigDecimal.ZERO);
        costShiftData.setBudgetAmount(BigDecimal.ZERO);

        List<String> bearDepartmentCodeList = caseList.stream().map(x -> x.getBearDepartmentCode()).distinct().collect(Collectors.toList());
        List<OrgVo> bearDepartmentList = orgVoService.findByOrgCodes(bearDepartmentCodeList);
        List<String> belongDepartmentCodeList = caseList.stream().map(x -> x.getBelongDepartmentCode()).distinct().collect(Collectors.toList());
        List<OrgVo> belongDepartmentList = orgVoService.findByOrgCodes(belongDepartmentCodeList);

        Map<String, String> bearDepartmentMap = bearDepartmentList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getRuleCode));
        Map<String, String> belongDepartmentMap = belongDepartmentList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getRuleCode));


        BigDecimal applyAmount = caseList.stream().filter(x -> {
                    String bearDepartmentCode = x.getBearDepartmentCode();
                    String belongDepartmentCode = x.getBelongDepartmentCode();
                    if (StringUtils.isNotBlank(bearDepartmentCode) && StringUtils.isNotBlank(belongDepartmentCode)
                            && bearDepartmentMap.containsKey(bearDepartmentCode) && belongDepartmentMap.containsKey(belongDepartmentCode)) {
                        String bearRuleCode = bearDepartmentMap.get(bearDepartmentCode);
                        String belongRuleCode = belongDepartmentMap.get(belongDepartmentCode);
                        if (ObjectUtils.isNotEmpty(bearRuleCode) && ObjectUtils.isNotEmpty(belongRuleCode) &&
                                bearRuleCode.length() >= 8 && belongRuleCode.length() >= 8) {
                            String bearRuleCodePrefix = bearRuleCode.substring(0, 8);
                            String belongRuleCodePrefix = belongRuleCode.substring(0, 8);
                            return !bearRuleCodePrefix.equals(belongRuleCodePrefix);
                        }
                    }
                    return true;
                })
                .map(x -> x.getLineNoTaxApplyAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        costShiftData.setEstimateAmount(applyAmount);
        dataList.add(costShiftData);
        return this;
    }

    /**
     * 考核利润
     *
     * @return
     */
    @Override
    public MarketingEstimationBuilder accessProfits() {
        RegionCollectMarketingEstimationVo data = new RegionCollectMarketingEstimationVo();
        data.setProjectCode(RegionCollectProjectEnum.access_profits.getCode());
        data.setProjectName(RegionCollectProjectEnum.access_profits.getName());
        data.setSort(RegionCollectProjectEnum.access_profits.getSort());

        data.setDifferenceRatio(BigDecimal.ZERO);
        data.setDifferenceAmount(BigDecimal.ZERO);
        data.setEstimateAmount(BigDecimal.ZERO);
        data.setEstimateRatio(BigDecimal.ZERO);
        data.setBudgetRatio(BigDecimal.ZERO);
        data.setBudgetAmount(BigDecimal.ZERO);


        BigDecimal estimateAmount = costShiftData.getEstimateAmount().add(profit.getEstimateAmount());
        data.setEstimateAmount(estimateAmount);

        BigDecimal budgetAmount = costShiftData.getBudgetAmount().add(profit.getBudgetAmount());
        data.setBudgetAmount(budgetAmount);

        BigDecimal estimateRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getEstimateAmount())
                && estimationRevenueData.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
            estimateRatio = data.getEstimateAmount().divide(estimationRevenueData.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        data.setEstimateRatio(estimateRatio);

        BigDecimal budgetRatio = BigDecimal.ZERO;
        if (Objects.nonNull(estimationRevenueData.getBudgetAmount())
                && estimationRevenueData.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
            budgetRatio = data.getBudgetAmount().divide(estimationRevenueData.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        data.setBudgetRatio(budgetRatio);

        dataList.add(data);
        return this;
    }


    @Override
    public List<RegionCollectMarketingEstimationVo> getList() {
        for (RegionCollectMarketingEstimationVo vo : dataList) {

            BigDecimal differenceAmount = vo.getEstimateAmount().subtract(vo.getBudgetAmount());
            BigDecimal differenceRatio = BigDecimal.ZERO;
            if (vo.getEstimateAmount().compareTo(BigDecimal.ZERO) == 1) {
                differenceRatio = differenceAmount.divide(vo.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_DOWN);
            }
            vo.setDifferenceAmount(differenceAmount);
            vo.setDifferenceRatio(differenceRatio);

            if (ObjectUtils.isNotEmpty(vo.getEstimateAmount())) {
                vo.setEstimateAmount(vo.getEstimateAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetAmount())) {
                vo.setBudgetAmount(vo.getBudgetAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getDifferenceAmount())) {
                vo.setDifferenceAmount(vo.getDifferenceAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }

            if (ObjectUtils.isNotEmpty(vo.getEstimateFixedPoint())) {
                vo.setEstimateFixedPointStr(vo.getEstimateFixedPoint().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetFixedPoint())) {
                vo.setBudgetFixedPointStr(vo.getBudgetFixedPoint().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getEstimateRatio())) {
                vo.setEstimateRatioStr(vo.getEstimateRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetRatio())) {
                vo.setBudgetRatioStr(vo.getBudgetRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDifferenceRatio())) {
                vo.setDifferenceRatioStr(vo.getDifferenceRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }

        }
        return dataList;
    }
}
