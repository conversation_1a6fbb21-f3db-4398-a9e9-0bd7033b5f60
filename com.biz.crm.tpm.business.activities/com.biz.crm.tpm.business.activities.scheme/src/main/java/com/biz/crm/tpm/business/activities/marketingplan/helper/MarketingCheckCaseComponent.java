package com.biz.crm.tpm.business.activities.marketingplan.helper;

import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCaseStrategy;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/31 23:24
 */
@Component
@Slf4j
public class MarketingCheckCaseComponent {

    @Resource
    private List<MarketingPlanCaseStrategy> strategyList;

    @Resource
    private MarketingPlanCaseService planCaseService;

    @Resource
    private LoginUserService loginUserService;

    @Async("tpmMarketingCheckCaseThread")
    public Future<List<MarketingPlanCaseVo>> checkCaseList(List<MarketingPlanCaseVo> caseList, String caseType, String years, String schemeCode,
                                                           String originalSchemeCode, String cacheKey, UserIdentity loginDetails, List<String> loginUserCodes, String userName) {
        //刷新用户
        this.loginUserService.refreshAuthentication(loginDetails);
        //大方向校验
        List<MarketingPlanCaseVo> caseVoList = planCaseService.checkPlanCaseList(caseList, years, loginUserCodes, userName);
        //分模板校验
        for (MarketingPlanCaseStrategy strategy : strategyList) {
            if (strategy.getCaseType().equals(caseType)) {
                strategy.checkCaseList(caseVoList, years, schemeCode, originalSchemeCode, cacheKey);
            }
        }
        return new AsyncResult<>(caseList);
    }

}
