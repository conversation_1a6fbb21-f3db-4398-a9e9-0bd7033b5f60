package com.biz.crm.tpm.business.activities.scheme.service.internal;

import com.biz.crm.tpm.business.activities.scheme.repository.SchemeFilesRepository;
import com.biz.crm.tpm.business.activities.scheme.entity.SchemeFiles;
import com.biz.crm.tpm.business.activities.scheme.service.SchemeFilesVoService;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeFilesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * 方案附件;(tpm_scheme_files)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Service("schemeFilesVoService")
public class SchemeFilesVoServiceImpl implements SchemeFilesVoService {
  @Autowired
  private SchemeFilesRepository schemeFilesRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 通过方案编号查询多条数据
   *
   * @param schemeCode 方案编号
   * @return 多条数据
   */
  @Override
  public Set<SchemeFilesVo> findBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return Collections.emptySet();
    }
    List<SchemeFiles> schemeFiles = this.schemeFilesRepository.findBySchemeCode(schemeCode);
    if (schemeFiles == null) {
      return Collections.emptySet();
    }
    Collection<SchemeFilesVo> schemeFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(schemeFiles, SchemeFiles.class, SchemeFilesVo.class, LinkedHashSet.class, ArrayList.class);
    return Sets.newLinkedHashSet(schemeFilesVos);
  }

  /**
   * 新增数据
   *
   * @param schemeFilesVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public SchemeFilesVo create(SchemeFilesVo schemeFilesVo) {
    this.createValidate(schemeFilesVo);
    SchemeFiles schemeFiles = this.nebulaToolkitService.copyObjectByWhiteList(schemeFilesVo, SchemeFiles.class, LinkedHashSet.class, ArrayList.class);
    schemeFiles.setTenantCode(TenantUtils.getTenantCode());
    this.schemeFilesRepository.saveOrUpdate(schemeFiles);

    return schemeFilesVo;
  }

  @Override
  @Transactional
  public Set<SchemeFilesVo> createBatch(Set<SchemeFilesVo> schemeFilesVos) {
    if (CollectionUtils.isEmpty(schemeFilesVos)) {
      return Collections.emptySet();
    }
    for (SchemeFilesVo schemeFilesVo : schemeFilesVos) {
      this.create(schemeFilesVo);
    }
    return schemeFilesVos;
  }

  @Override
  @Transactional
  public void deleteBySchemeCode(String schemeCode) {
    this.schemeFilesRepository.deleteBySchemeCode(schemeCode);
  }

  /**
   * 创建验证
   *
   * @param schemeFilesVo
   */
  private void createValidate(SchemeFilesVo schemeFilesVo) {
    Validate.notNull(schemeFilesVo, "新增时，对象信息不能为空！");
    schemeFilesVo.setId(null);
    // 验证重复操作
    Validate.notBlank(schemeFilesVo.getSchemeCode(), "新增数据时，方案编号不能为空！");
    Validate.notBlank(schemeFilesVo.getFileCode(), "新增数据时，文件唯一识别号不能为空！");
  }
}