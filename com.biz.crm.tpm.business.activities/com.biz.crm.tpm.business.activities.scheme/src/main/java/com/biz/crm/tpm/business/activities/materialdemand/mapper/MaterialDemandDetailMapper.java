package com.biz.crm.tpm.business.activities.materialdemand.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandDetail;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandQueryVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandReportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:31
 */
public interface MaterialDemandDetailMapper extends BaseMapper<MaterialDemandDetail> {

    Page<MaterialDemandReportVo> findMaterialDemandReportList(Page<MaterialDemandReportVo> page, @Param("vo")MaterialDemandReportVo vo,@Param("tenantCode")String tenantCode);

    Page<MarketingPlanCaseVo> queryDetailList(Page<MarketingPlanCaseVo> page, @Param("vo") MaterialDemandQueryVo vo,@Param("tenantCode")String tenantCode);

    List<MaterialDemandDetail> findListByMaterialCodesAndOrgCodes(@Param("materialCodes") List<String> materialCodes,@Param("orgCodes") List<String> orgCodes,
                                                                  @Param("tenantCode") String tenantCode);
}
