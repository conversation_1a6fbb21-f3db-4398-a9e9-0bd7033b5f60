package com.biz.crm.tpm.business.activities.scheme.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.scheme.dto.SchemeDto;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.biz.crm.tpm.business.activities.scheme.entity.Scheme;

/**
 * 方案;(tpm_scheme)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Mapper
public interface SchemeMapper extends BaseMapper<Scheme> {
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto  动态查询条件
   * @return 分页对象列表
   */
  Page<SchemeVo> findByConditions(@Param("page") Page<SchemeVo> page, @Param("dto") SchemeDto dto);

  /**
   * 根据费用预算编号统计有效绑定的方案数量
   *
   * @param dto
   * @return
   */
  Integer countByCostBudget(@Param("dto") SchemeDto dto);
}