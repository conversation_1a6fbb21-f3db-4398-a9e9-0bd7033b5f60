package com.biz.crm.tpm.business.activities.scheme.service.internal;

import com.biz.crm.tpm.business.activities.scheme.repository.SchemeCostBudgetRepository;
import com.biz.crm.tpm.business.activities.scheme.service.SchemeCostBudgetVoService;
import com.biz.crm.tpm.business.activities.scheme.entity.SchemeCostBudget;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeCostBudgetVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * 方案预算成本;(tpm_scheme_cost_budget)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Service("schemeCostBudgetVoService")
public class SchemeCostBudgetVoServiceImpl implements SchemeCostBudgetVoService {
  @Autowired
  private SchemeCostBudgetRepository schemeCostBudgetRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public Set<SchemeCostBudgetVo> findBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return Collections.emptySet();
    }
    List<SchemeCostBudget> schemeCostBudgets = this.schemeCostBudgetRepository.findBySchemeCode(schemeCode);
    if (schemeCostBudgets == null) {
      return Collections.emptySet();
    }
    Collection<SchemeCostBudgetVo> schemeFilesVos = this.nebulaToolkitService.copyCollectionByWhiteList(schemeCostBudgets, SchemeCostBudget.class, SchemeCostBudgetVo.class, LinkedHashSet.class, ArrayList.class);
    return Sets.newLinkedHashSet(schemeFilesVos);
  }

  /**
   * 新增数据
   *
   * @param schemeCostBudgetVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public SchemeCostBudgetVo create(SchemeCostBudgetVo schemeCostBudgetVo) {
    this.createValidate(schemeCostBudgetVo);
    SchemeCostBudget schemeCostBudget = this.nebulaToolkitService.copyObjectByWhiteList(schemeCostBudgetVo, SchemeCostBudget.class, LinkedHashSet.class, ArrayList.class);
    schemeCostBudget.setTenantCode(TenantUtils.getTenantCode());
    this.schemeCostBudgetRepository.saveOrUpdate(schemeCostBudget);

    return schemeCostBudgetVo;
  }

  @Override
  @Transactional
  public void deleteBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return;
    }
    this.schemeCostBudgetRepository.deleteBySchemeCode(schemeCode);
  }

  /**
   * 创建验证
   *
   * @param schemeCostBudgetVo
   */
  private void createValidate(SchemeCostBudgetVo schemeCostBudgetVo) {
    Validate.notNull(schemeCostBudgetVo, "新增时，对象信息不能为空！");
    schemeCostBudgetVo.setId(null);
    // 验证重复操作
    Validate.notBlank(schemeCostBudgetVo.getCostBudgetCode(), "新增数据时，费用预算编号不能为空！");
    Validate.notBlank(schemeCostBudgetVo.getSchemeCode(), "新增数据时，方案编号不能为空！");
  }
}