package com.biz.crm.tpm.business.activities.scheme.vo;

import com.biz.crm.business.common.sdk.vo.FileVo;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Vo：方案附件;
 * <AUTHOR> <PERSON>
 * @date : 2022-5-31
 */
@ApiModel(value = "SchemeFiles",description = "方案附件")
@Getter
@Setter
public class SchemeFilesVo extends FileVo {
  /** 方案编号 */
  @ApiModelProperty(name = "schemeCode",notes = "方案编号", value= "方案编号")
  private String schemeCode;
}