package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.activities.marketingplan.constant.MarketingPlanConstant;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.activities.stagingscheme.vo.TpmStagingSchemeVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/17 21:33
 */
@Component
public class MarketingChangeSchemeComponent {

    @Resource
    private ITpmStagingSchemeService stagingSchemeService;

    @Resource
    private MarketingPlanCaseService caseService;

    @Resource
    private MarketingPlanCaseRepository caseRepository;

    @Resource
    private MarketingPlanProductRepository productRepository;

    @Resource
    private MarketingPlanService service;

    @Resource
    private GenerateCodeService generateCodeService;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    public List<String> changeSchemeSyncOriginScheme(String schemeCode) {
        //此处查询的都是变更方案的
        MarketingPlanVo vo = service.queryDetails(null, schemeCode);
        Validate.notNull(vo.getOriginalSchemeCode(), "不是变更方案不可执行此方法");
        Validate.notNull(ProcessStatusEnum.PASS.getDictCode().equals(vo.getProcessStatus()), "未审批通过的变更方案不可执行此方法");
        String originalSchemeCode = vo.getOriginalSchemeCode();
        //查询出原来的销售计划
        List<MarketingSalesPlanVo> originalSalesPlanVoList = marketingSalesPlanService.findListBySchemeCode(originalSchemeCode);
        if (!CollectionUtils.isEmpty(originalSalesPlanVoList)) {
            String jsonStr = JSONObject.toJSONString(originalSalesPlanVoList);
            stagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
                this.setJsonStr(jsonStr);
                this.setReleaseId(originalSchemeCode);
                this.setFromType(OverallPlanSchemeTypeEnum.SALES_PLAN.getCode());
            }});
        }
        //先删除原方案的销售计划
        marketingSalesPlanService.deleteBySchemeCodes(Lists.newArrayList(originalSchemeCode));
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findListBySchemeCode(schemeCode);
        marketingSalesPlanService.saveBatchSalesPlan(salesPlanVoList, originalSchemeCode, Boolean.FALSE, BooleanEnum.FALSE.getCapital());
        List<MarketingPlanCase> caseList = caseService.findListBySchemeCodes(Lists.newArrayList(schemeCode));
        //先查询出当前关联的 并且放入缓存 在删除原来的关联
        List<String> originalSchemeDetailCodes = caseList.stream().map(MarketingPlanCase::getOriginalSchemeDetailCode).collect(Collectors.toList());
        //原始方案明细编码对应活动执行编码
        Map<String, String> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(originalSchemeDetailCodes)) {
            List<MarketingPlanCase> originalCaseList = caseService.findListBySchemeDetailCodes(originalSchemeDetailCodes);
            for (MarketingPlanCase planCase : originalCaseList) {
                map.put(planCase.getSchemeDetailCode(), planCase.getActExecuteCode());
                String jsonStr = JSONObject.toJSONString(planCase);
                stagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
                    this.setJsonStr(jsonStr);
                    this.setReleaseId(planCase.getSchemeDetailCode());
                    this.setFromType(OverallPlanSchemeTypeEnum.PLAN_CASE.getCode());
                }});
            }
            caseService.deleteBySchemeDetailCodes(originalSchemeDetailCodes);
        }
        List<String> schemeDetailCodes = Lists.newArrayList();
        List<MarketingPlanProduct> productList = Lists.newArrayList();
        List<MarketingPlanCase> planCaseList = Lists.newArrayList();
        for (MarketingPlanCase caseVo : caseList) {
            String schemeDetailCode = ObjectUtils.isNotEmpty(caseVo.getOriginalSchemeDetailCode()) ? caseVo.getOriginalSchemeDetailCode() :
                    generateCodeService.generateCode(MarketingPlanConstant.CHANGE_SCHEME_DETAIL_CODE_RULE);

            MarketingPlanCase planCase = JSONObject.parseObject(JSONObject.toJSONString(caseVo), MarketingPlanCase.class);
            String actExecuteCode = map.getOrDefault(schemeDetailCode, generateCodeService.generateCode(MarketingPlanConstant.SCHEME_ACT_EXECUTE_CODE_RULE));
            planCase.setSchemeDetailCode(schemeDetailCode);
            planCase.setActExecuteCode(actExecuteCode);
            planCase.setOriginalSchemeDetailCode(null);
            planCase.setSchemeCode(originalSchemeCode);
            planCase.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            planCase.setId(null);
            schemeDetailCodes.add(schemeDetailCode);
            if (CollectionUtils.isNotEmpty(planCase.getProductAndItemList())) {
                for (MarketingPlanProduct product : planCase.getProductAndItemList()) {
                    product.setId(null);
                    product.setSchemeCode(originalSchemeCode);
                    product.setSchemeDetailCode(schemeDetailCode);
                }
                productList.addAll(planCase.getProductAndItemList());
            }
            planCaseList.add(planCase);
            //创建一个关联关系
            caseVo.setOriginalSchemeDetailCode(schemeDetailCode);
        }
        //修改变更方案的
        caseRepository.updateBatchById(caseList);
        //修改原方案的
        caseRepository.saveBatchList(planCaseList);
        if (CollectionUtils.isNotEmpty(productList)) {
            productRepository.saveBatch(productList);
        }
        return schemeDetailCodes;
    }
}
