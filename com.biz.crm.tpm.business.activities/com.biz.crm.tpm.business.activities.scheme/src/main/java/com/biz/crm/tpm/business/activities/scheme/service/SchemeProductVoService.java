package com.biz.crm.tpm.business.activities.scheme.service;

import com.biz.crm.tpm.business.activities.scheme.vo.SchemeProductVo;

import java.util.Set;

/**
 * 方案产品;(tpm_scheme_product)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
public interface SchemeProductVoService {
  /**
   * 通过方案编号查询多条数据
   *
   * @param schemeCode 方案编号
   * @return 多条数据
   */
  Set<SchemeProductVo> findBySchemeCode(String schemeCode);

  /**
   * 新增数据
   *
   * @param schemeProductVo 实体对象
   * @return 新增结果
   */
  SchemeProductVo create(SchemeProductVo schemeProductVo);

  /**
   * 删除数据
   *
   * @param schemeCode 方案编号
   */
  void deleteBySchemeCode(String schemeCode);
}