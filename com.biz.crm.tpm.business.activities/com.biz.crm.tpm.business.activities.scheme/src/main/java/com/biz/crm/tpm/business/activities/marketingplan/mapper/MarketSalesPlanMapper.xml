<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingSalesPlanMapper">


    <select id="findCollectList" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo">
        SELECT
        a.customer_code,
        a.customer_name,
        a.product_group_code,
        a.channel_code,
        a.erp_code,
        a.company_code,
        a.item_code,
        a.item_name,
        a.product_code,
        a.product_name,
        a.years,
        a.cost_center_code,
        a.cost_center_name,
        a.transport_type,
        sum(a.estimated_cost) estimated_cost,
        sum(a.tax_estimated_cost) tax_estimated_cost,
        sum(a.estimated_sales_volume) estimated_sales_volume
        FROM
        tpm_marketing_sales_plan a left join tpm_marketing_plan b on a.scheme_code = b.scheme_code
        <where>
            b.tenant_code = #{tenantCode} and b.process_status = '3' and a.change_flag = 'N'
            and a.del_flag = '${@<EMAIL>()}'
            <if test="vo.years != null and vo.years != ''">
                and a.years = #{vo.years}
            </if>
            <if test="vo.customerCode != null and vo.customerCode != ''">
                <bind name="customerCode" value="'%' + vo.customerCode + '%'"/>
                and a.customer_code like #{customerCode}
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                <bind name="customerName" value="'%' + vo.customerName + '%'"/>
                and a.customer_name like #{customerName}
            </if>
            <if test="vo.costCenterName != null and vo.costCenterName != ''">
                <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
                and a.cost_center_name like #{costCenterName}
            </if>
            <if test="vo.companyCode != null and vo.companyCode != ''">
                <bind name="companyCode" value="'%' + vo.companyCode + '%'"/>
                and a.company_code = #{companyCode}
            </if>
            <if test="vo.itemCode != null and vo.itemCode != ''">
                <bind name="itemCode" value="'%' + vo.itemCode + '%'"/>
                and a.item_code like #{itemCode}
            </if>
            <if test="vo.itemName != null and vo.itemName != ''">
                <bind name="itemName" value="'%' + vo.itemName + '%'"/>
                and a.item_name like #{itemName}
            </if>
            <if test="vo.productCode != null and vo.productCode != ''">
                <bind name="productCode" value="'%' + vo.productCode + '%'"/>
                and a.product_code like #{productCode}
            </if>
            <if test="vo.productName != null and vo.productName != ''">
                <bind name="productName" value="'%' + vo.productName + '%'"/>
                and a.product_name like #{productName}
            </if>
            <if test="vo.transportType != null and vo.transportType != ''">
                and a.transport_type = #{vo.transportType}
            </if>
        </where>
        GROUP BY
        a.customer_code,
        a.customer_name,
        a.product_group_code,
        a.channel_code,
        a.erp_code,
        a.company_code,
        a.item_code,
        a.item_name,
        a.product_code,
        a.product_name,
        a.years,
        a.cost_center_code,
        a.transport_type,
        a.cost_center_name
        order by a.customer_code desc
    </select>

    <select id="findListBySalesPlanQueryVo"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo">
        select a.*
        from tpm_marketing_sales_plan a
        left join tpm_marketing_plan b on a.scheme_code = b.scheme_code
        where b.del_flag = '${@<EMAIL>()}'
        and a.check_flag = '1'
        <choose>
            <when test="'Y'.toString()==vo.changeFlag">

            </when>
            <otherwise>
                and b.scheme_type != 'change'
            </otherwise>
        </choose>
        <if test="vo.years != null and vo.years != ''">
            and a.years = #{vo.years}
        </if>
        <if test="vo.startYears != null and vo.startYears != ''">
            <![CDATA[and a.years >= #{vo.startYears}]]>
        </if>
        <if test="vo.endYears != null and vo.endYears != ''">
            <![CDATA[and a.years <= #{vo.endYears}]]>
        </if>
        <!--            <if test="vo.orgCode != null and vo.orgCode != ''">-->
        <!--                and department_code = #{vo.orgCode}-->
        <!--            </if>-->
        <if test="vo.customerCode != null and vo.customerCode != ''">
            and a.customer_code = #{vo.customerCode}
        </if>
        <if test="vo.customerCodeSet != null and vo.customerCodeSet.size()>0">
            and a.customer_code in
            <foreach collection="vo.customerCodeSet" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.itemCodeSet != null and vo.itemCodeSet.size()>0">
            and a.item_code in
            <foreach collection="vo.itemCodeSet" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.productCodeSet != null and vo.productCodeSet.size()>0">
            and a.product_code in
            <foreach collection="vo.productCodeSet" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.costCenterCodeSet != null and vo.costCenterCodeSet.size()>0">
            and a.cost_center_code in
            <foreach collection="vo.costCenterCodeSet" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.excludeSchemeCodes != null and vo.excludeSchemeCodes.size()>0">
            and a.scheme_code not in
            <foreach collection="vo.excludeSchemeCodes" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vo.yearsList != null and vo.yearsList.size()>0">
            and a.years in
            <foreach collection="vo.yearsList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findListByCondition"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo">
        select tmsp.*
        from tpm_marketing_sales_plan tmsp
        left join tpm_marketing_plan tmmp on tmsp.scheme_code = tmmp.scheme_code
        where tmsp.del_flag = '${@<EMAIL>()}'
        and tmsp.check_flag = '1'
        <choose>
            <when test="'Y'.toString()== changeFlag">

            </when>
            <otherwise>
                and tmmp.scheme_type != 'change'
            </otherwise>
        </choose>
        <if test="confirmStatus != null and confirmStatus != ''">
            and tmmp.confirm_status = #{confirmStatus}
        </if>
        <if test="yearsSet != null and yearsSet.size()>0">
            and tmsp.years in
            <foreach collection="yearsSet" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="costCenterCodes != null and costCenterCodes.size()>0">
            and tmsp.cost_center_code in
            <foreach collection="costCenterCodes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="customerCodes != null and customerCodes.size()>0">
            and tmsp.customer_code in
            <foreach collection="customerCodes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="itemCodes != null and itemCodes.size()>0">
            and tmsp.item_code in
            <foreach collection="itemCodes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="productCodes != null and productCodes.size()>0">
            and tmsp.product_code in
            <foreach collection="productCodes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="excludeSchemeCodes != null and excludeSchemeCodes.size()>0">
            and tmsp.scheme_code not in
            <foreach collection="excludeSchemeCodes" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--        <if test="includeSchemeCodes != null and includeSchemeCodes.size()>0">-->
        <!--            and tmsp.scheme_code in-->
        <!--            <foreach collection="includeSchemeCodes" open="(" close=")" separator="," item="item" index="index">-->
        <!--                #{item}-->
        <!--            </foreach>-->
        <!--        </if>-->
    </select>
    <select id="findByCollectCode"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo">
        SELECT
            a.*
        FROM
            tpm_marketing_sales_plan a
        WHERE
            a.scheme_code in (
                SELECT
                    scheme_code FROM tpm_region_collect_scheme
                WHERE
                    scheme_code IS NOT NULL
                  AND collect_code = #{collectCode})
    </select>

</mapper>

