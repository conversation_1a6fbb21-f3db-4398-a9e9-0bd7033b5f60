package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/5 16:09
 */
@Data
@ApiModel(value = "MarketingPlanChangeLog", description = "营销方案规划变更日志表")
public class MarketingPlanChangeLogVo {

    @ApiModelProperty("原始方案编码")
    private String originalSchemeCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("流程状态")
    private String processStatus;

    @ApiModelProperty("流程编码")
    private String processNumber;

    @ApiModelProperty(name = "createAccount", value = "创建人账号")
    private String createAccount;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(name = "createName", value = "创建人名称")
    private String createName;

    @ApiModelProperty("变更前的vo")
    private MarketingPlanVo originalVo;

    @ApiModelProperty("变更的vo")
    private MarketingPlanVo newestVo;

}
