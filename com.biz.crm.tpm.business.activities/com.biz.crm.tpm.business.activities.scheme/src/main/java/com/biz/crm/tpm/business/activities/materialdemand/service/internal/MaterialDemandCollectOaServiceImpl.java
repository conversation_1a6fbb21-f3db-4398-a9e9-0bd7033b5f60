package com.biz.crm.tpm.business.activities.materialdemand.service.internal;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandCollect;
import com.biz.crm.tpm.business.activities.materialdemand.enums.MaterialDemandCollectDetailFieldsEnum;
import com.biz.crm.tpm.business.activities.materialdemand.enums.MaterialDemandCollectFieldsEnum;
import com.biz.crm.tpm.business.activities.materialdemand.repository.MaterialDemandCollectRepository;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandCollectOaService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectDetailVo;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.MaterialDemandDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.MaterialDemandMainDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;


@Service
@RefreshScope
public class MaterialDemandCollectOaServiceImpl implements MaterialDemandCollectOaService {

    @Autowired(required = false)
    private PushOaService pushOaService;

    @Autowired(required = false)
    private MaterialDemandCollectRepository materialDemandCollectRepository;

    @Autowired(required = false)
    private UserVoService userVoService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;

    @Value("${domain-name:}")
    private String domainName;

    @Autowired(required = false)
    private LoginUserService loginUserService;
    
    /**
     * 推送OA
     *
     * @param collect@return
     */
    @Override
    public JSONObject pushOa(MaterialDemandCollect collect) {
        List<MaterialDemandCollectDetailVo> demandCollectDetailVos = collect.getDemandCollectDetailVos();
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(collect));
        orderJsonObject.put("businessCode", collect.getCollectCode());
        orderJsonObject.put("demandCode", collect.getCollectCode());
        orderJsonObject.put("demandName", collect.getCollectName());

        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        orderJsonObject.put("title", collect.getCollectName() + "-" + userVo.getFullName() + "-" + DateUtil.format(new Date(), "yyyy-MM-dd"));

        // 主表
        // 业务类型
        String businessType = MqConstant.TAG_TPM_MATERIAL_DEMAND;
        orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.ACTIVITY_MATERIAL_SUBMIT_FORM.getUrlCode() + "?id=" + collect.getId());
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        JSONObject response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, Arrays.asList(JSONArray.parseArray(JSON.toJSONString(demandCollectDetailVos))), workflowId, workflowName,
                mainTableMethod, detailTableMethod);

        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        if (response.containsKey("code")) {
            Integer resultCode = response.getInteger("code");
            if (resultCode == 100) {
                JSONObject ja = response.getJSONObject("data");
                MaterialDemandCollect entity = materialDemandCollectRepository.queryByIdOrCode(collect.getId(), null);
                entity.setProcessNumber(ja.getString("out"));
                entity.setProcessDate(new Date());
                entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
                entity.setOaId(response.getString("oaId"));
                entity.setOaUserName(response.getString("oaUserName"));
                materialDemandCollectRepository.updateById(entity);
            }
        } else {
            Validate.isTrue(false, "OA流程提交失败，错误信息：" + response.getString("msg"));
        }

        return response;
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (MaterialDemandCollectFieldsEnum value : MaterialDemandCollectFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod = (tableDetailFields, detailJsonObject) -> {
        for (MaterialDemandCollectDetailFieldsEnum value : MaterialDemandCollectDetailFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };

    /**
     * 重新提交OA
     *
     * @param collect@return
     */
    @Override
    public JSONObject resubmitOa(MaterialDemandCollect collect) {
        // 获取当前登录人信息
        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);

        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType = MqConstant.TAG_TPM_MATERIAL_DEMAND;
        collect.setBusinessCode(collect.getId());
        collect.setTpmUrl(domainName + TpmOaPageEnum.ACTIVITY_MATERIAL_SUBMIT_FORM.getUrlCode() + "?id=" + collect.getId());
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();

        OaResubmitDto dto = new OaResubmitDto();
        dto.setBusinessCode(businessType);
        dto.setRequestId(collect.getProcessNumber());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        MaterialDemandMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(collect, MaterialDemandMainDto.class, LinkedHashSet.class, ArrayList.class);
        dto.setRequestName(collect.getCollectName() + "-" + userVo.getFullName() + "-" + DateUtil.format(new Date(), "yyyy-MM-dd"));
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));
        List<MaterialDemandDetailDto> detailDtoList = (List<MaterialDemandDetailDto>) nebulaToolkitService.copyCollectionByBlankList(collect.getDemandCollectDetailVos(), MaterialDemandCollectDetailVo.class, MaterialDemandDetailDto.class, LinkedHashSet.class, ArrayList.class);
        OaDetailDto oaDetailDto = new OaDetailDto();
        oaDetailDto.setDetailList(JSONUtil.toJsonStr(detailDtoList));
        oaDetailDto.setDetailClass(MaterialDemandDetailDto.class);
        dto.setDetailList(Arrays.asList(oaDetailDto));
        if (ryOaProcessService.resubmit(dto)) {
            MaterialDemandCollect entity = materialDemandCollectRepository.queryByIdOrCode(collect.getId(), null);
            entity.setProcessDate(new Date());
            entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
            materialDemandCollectRepository.updateById(entity);
        }
        return null;
    }

    /**
     * OA撤回
     *
     * @param collect
     * @param remark
     * @return
     */
    @Override
    public boolean oaWithdraw(MaterialDemandCollect collect, String remark) {
        Validate.isTrue(collect.getProcessStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(collect.getProcessNumber()));
        dto.setProcessCreateId(collect.getOaId());
        dto.setUserName(collect.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }
}
