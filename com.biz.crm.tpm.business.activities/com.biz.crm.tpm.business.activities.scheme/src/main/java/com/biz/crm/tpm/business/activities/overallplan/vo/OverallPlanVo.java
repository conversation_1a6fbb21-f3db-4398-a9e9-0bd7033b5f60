package com.biz.crm.tpm.business.activities.overallplan.vo;

import com.biz.crm.business.common.sdk.vo.WorkflowFlagOpVo;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanBudget;
import com.biz.crm.tpm.business.activities.schemefile.entity.SchemeCaseFiles;
import com.biz.crm.tpm.business.activities.schemefile.vo.SchemeCaseFilesVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("统筹方案vo")
public class OverallPlanVo extends WorkflowFlagOpVo {

    private String tenantCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案主题")
    private String schemeTheme;

    @ApiModelProperty("方案类型:数据字典(scheme_type)")
    private String schemeType;

    @ApiModelProperty("公司代码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司代码'")
    private String companyCode;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("方案说明")
    private String schemeDesc;

    @ApiModelProperty("费用合计")
    private BigDecimal costTotal;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimateSalesVolume;

    @ApiModelProperty("方案投产比")
    private BigDecimal productionRatio;

    @ApiModelProperty("创建人组织编码")
    private String orgCode;

    @ApiModelProperty("创建人组织名称")
    private String orgName;

    @ApiModelProperty("创建人职位编码")
    private String positionCode;

    @ApiModelProperty("创建人职位名称")
    private String positionName;

    private String departmentStr;

    @ApiModelProperty("推送日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    private String oaUserName;

    @ApiModelProperty("回传TPM接口唯一标识（接口用）")
    private String businessCode;

    @ApiModelProperty("url")
    private String tpmUrl;

//    @ApiModelProperty("承接方案明细")
//    private List<BearOverallPlanCaseVo> bearList;

    @ApiModelProperty("方案明细")
    private List<OverallPlanCaseVo> caseList;

    private String cacheKey;

    @ApiModelProperty("费用部门")
    private List<OverallPlanDepartmentVo> departmentList;

    @ApiModelProperty("费用部门")
    private String departmentNameStr;

    @ApiModelProperty("预算列表")
    private List<OverallPlanBudget> budgetList;

    @ApiModelProperty("文件列表")
    private List<SchemeCaseFilesVo> filesList;
}
