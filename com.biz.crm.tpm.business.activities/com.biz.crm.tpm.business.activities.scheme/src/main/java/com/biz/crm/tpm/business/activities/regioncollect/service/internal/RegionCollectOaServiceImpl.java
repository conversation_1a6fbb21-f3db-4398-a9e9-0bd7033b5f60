package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollect;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectFieldsEnum;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectProjectEnum;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectOaService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.enums.CostCenterFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketPlanCaseFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketingEstimationFieldsEnum;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.CostCenterDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.MarketPlanCaseDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.RegionCollectDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.RegionCollectMainDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import liquibase.util.StringUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class RegionCollectOaServiceImpl implements RegionCollectOaService {

    @Autowired(required = false)
    private RegionCollectRepository regionCollectRepository;

    @Autowired(required = false)
    private MarketingPlanRepository marketingPlanRepository;

    @Autowired(required = false)
    private PushOaService pushOaService;

    @Autowired(required = false)
    private UserVoService userVoService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;

    @Value("${domain-name:}")
    private String domainName;

    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private BudgetSubjectsVoService budgetSubjectsVoService;

    @Autowired(required = false)
    private CostTypeCategoryVoService costTypeCategoryVoService;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    /**
     * 推送OA
     *
     * @param
     * @return
     */
    @Override
    public String pushOa(RegionCollectVo order) {
        List<MarketingPlanCaseVo> caseVoList;
        if (StringUtils.isNotBlank(order.getCollectCode())) {
            order.setBusinessCode(order.getCollectCode());
            caseVoList = marketingPlanCaseService.findByCollectCode(order.getCollectCode());
        } else {
            order.setBusinessCode(order.getSchemeCode());
            order.setRemark(order.getSchemeDesc());
            caseVoList = marketingPlanCaseService.findBySchemeCode(order.getSchemeCode());
        }
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(order));

        Set<String> orgCodeList = caseVoList.stream().map(MarketingPlanCaseVo::getBearDepartmentCode).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            orderJsonObject.put("deptCode", String.join(",", oaOrgCodeSet));
        }

        List<RegionCollectMarketingEstimationVo> regionCollectMarketingEstimationVos = order.getMarketingEstimationList();
        regionCollectMarketingEstimationVos = Optional.ofNullable(regionCollectMarketingEstimationVos).orElse(Lists.newArrayList());
        Map<String, RegionCollectMarketingEstimationVo> marketingMap = regionCollectMarketingEstimationVos.stream()
                .collect(Collectors.toMap(RegionCollectMarketingEstimationVo::getProjectCode, v -> v, (a, b) -> a));
        RegionCollectMarketingEstimationVo estimationRevenue = marketingMap.get(RegionCollectProjectEnum.estimation_revenue.getCode());

        if (!CollectionUtils.isEmpty(order.getMarketingEstimationList())) {
            //回写预估费用、预估销售额、费率
            Map<String, BigDecimal> map = order.getMarketingEstimationList().stream().collect(Collectors.toMap(x -> x.getProjectCode(), x -> Optional.ofNullable(x.getEstimateAmount()).orElse(BigDecimal.ZERO)));
            BigDecimal applyAmount = map.getOrDefault(RegionCollectProjectEnum.marketing_cost_total.getCode(), BigDecimal.ZERO);
            BigDecimal estimatedSalesVolume = map.getOrDefault(RegionCollectProjectEnum.estimation_revenue.getCode(), BigDecimal.ZERO);
            orderJsonObject.put("applyAmount", applyAmount);
            orderJsonObject.put("estimatedSalesVolume", estimatedSalesVolume);
            orderJsonObject.put("ratio", BigDecimal.ZERO);
            if (estimatedSalesVolume.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = applyAmount.divide(estimatedSalesVolume, 4, BigDecimal.ROUND_HALF_DOWN);
                orderJsonObject.put("ratio", ratio);
            }

            RegionCollectMarketingEstimationVo marketingCostTotal = marketingMap.get(RegionCollectProjectEnum.marketing_cost_total.getCode());
            RegionCollectMarketingEstimationVo profit = marketingMap.get(RegionCollectProjectEnum.profit.getCode());

            //预算费率=营销费用预算达成金额/预计收入预算达成金额
            BigDecimal ratioBudget = (estimationRevenue.getBudgetAmount() == null || estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO :
                    (marketingCostTotal.getBudgetAmount() == null ? BigDecimal.ZERO : marketingCostTotal.getBudgetAmount()).divide(estimationRevenue.getBudgetAmount(), 2, BigDecimal.ROUND_HALF_UP);
            orderJsonObject.put("ratioBudget", ratioBudget);
            //规划业绩预算达成率=预计达成金额/预算达成金额
            BigDecimal achieveRatio = (estimationRevenue.getBudgetAmount() == null || estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO :
                    (estimationRevenue.getEstimateAmount() == null ? BigDecimal.ZERO : estimationRevenue.getEstimateAmount()).divide(estimationRevenue.getBudgetAmount(), 2, BigDecimal.ROUND_HALF_UP);
            orderJsonObject.put("achieveRatio", achieveRatio);
            //利润预计达成金额
            orderJsonObject.put("profitMargin", profit.getEstimateAmount());
            //利润预算达成金额
            orderJsonObject.put("profitMarginBudget", profit.getBudgetAmount());
            //规划收入
            orderJsonObject.put("estimateAmount", estimationRevenue.getEstimateAmount());
            //预算收入
            orderJsonObject.put("budgetAmount", estimationRevenue.getBudgetAmount());
            //营销费用小计-预算达成
            orderJsonObject.put("marketingBudgetAmount", marketingCostTotal.getBudgetAmount());
            //超费率=规划费率-预算费率（如果规划收入为0，则费率默认为0）
            orderJsonObject.put("cflghflysfl", orderJsonObject.getBigDecimal("ratio").subtract(ratioBudget));
            //规划利润率=规划利润额/规划收入（如果规划收入为0，则费率默认为0）
            orderJsonObject.put("lrl", estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : profit.getEstimateAmount().divide(estimationRevenue.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_UP));
            //预算利润率=预算利润率/预算收入（如果规划收入为0，则费率默认为0）
            orderJsonObject.put("yslrl", estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : profit.getBudgetAmount().divide(estimationRevenue.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_UP));
            //超亏利润额=规划利润额-预算利润额
            orderJsonObject.put("cklrelreyslre", profit.getEstimateAmount().subtract(profit.getBudgetAmount()));
        }
//
//        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
//        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        if (StringUtils.isNotBlank(order.getCollectCode())) {
            //大区方案
            orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.ACTIVITY_REGION_MANAGE_FORM.getUrlCode() + "?id=" + order.getId());
            orderJsonObject.put("title", "大区方案汇总：" + order.getYears() + "-" + order.getOrgName() + "-营销活动方案申请");
        } else {
            orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.ACTIVITY_MARKET_PLAN_CREATE.getUrlCode() + "?code=" + order.getSchemeCode());
            orderJsonObject.put("title", "营销方案申请：" + order.getSchemeName());
        }

        // 主表
        // 业务类型
        String businessType = MqConstant.TAG_TPM_REGION_COLLECT;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        List<String> projectCodeList = regionCollectMarketingEstimationVos.stream().map(RegionCollectMarketingEstimationVo::getProjectCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListByCategoryCodes(projectCodeList);
        Map<String, String> budgetSubjectsCategoryCodeMap = costTypeCategoryVos.stream().filter(k -> StringUtil.isNotEmpty(k.getBudgetSubjectsCode()))
                .collect(Collectors.toMap(CostTypeCategoryVo::getBudgetSubjectsCode, CostTypeCategoryVo::getCategoryCode, (n, o) -> n));
        Set<String> categoryCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getCategoryCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        Set<String> budgetSubjectsCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getBudgetSubjectsCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        List<BudgetSubjectsVo> budgetSubjectsVos = budgetSubjectsVoService.findByCodes(budgetSubjectsCodeSet);
        List<RegionCollectDetailDto> detailDtoList = (List<RegionCollectDetailDto>) nebulaToolkitService.copyCollectionByBlankList(
                regionCollectMarketingEstimationVos.stream().filter(k -> StringUtil.isNotEmpty(k.getProjectCode()))
                        .filter(k -> !categoryCodeSet.contains(k.getProjectCode())).collect(Collectors.toList()),
                RegionCollectMarketingEstimationVo.class, RegionCollectDetailDto.class, LinkedHashSet.class, ArrayList.class);
        AtomicInteger index = new AtomicInteger(20);
        budgetSubjectsVos.stream().filter(k -> StringUtil.isNotEmpty(k.getParentBudgetSubjectsCode()))
                .collect(Collectors.groupingBy(BudgetSubjectsVo::getParentBudgetSubjectsCode))
                .forEach((parentCode, childList) -> {
                    RegionCollectDetailDto detailDto = new RegionCollectDetailDto();
                    AtomicReference<BigDecimal> budgetAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> estimateAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> differenceAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    childList.forEach(k -> {
                        String categoryCode = budgetSubjectsCategoryCodeMap.getOrDefault(k.getBudgetSubjectsCode(), "");
                        RegionCollectMarketingEstimationVo estimationVo = marketingMap.get(categoryCode);
                        if (Objects.isNull(estimationVo)) {
                            return;
                        }
                        if (Objects.nonNull(estimationVo.getBudgetAmount())) {
                            budgetAmountTotal.set(estimationVo.getBudgetAmount().add(budgetAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getEstimateAmount())) {
                            estimateAmountTotal.set(estimationVo.getEstimateAmount().add(estimateAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getDifferenceAmount())) {
                            differenceAmountTotal.set(estimationVo.getDifferenceAmount().add(differenceAmountTotal.get()));
                        }
                    });

                    detailDto.setBudgetAmount(budgetAmountTotal.get());
                    detailDto.setEstimateAmount(estimateAmountTotal.get());
                    detailDto.setDifferenceAmount(differenceAmountTotal.get());
                    if (Objects.nonNull(estimationRevenue)) {
                        if (Objects.nonNull(estimationRevenue.getBudgetAmount())
                                && estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setBudgetRatio(detailDto.getBudgetAmount().divide(estimationRevenue.getBudgetAmount(), 2, RoundingMode.HALF_UP));
                        }
                        if (Objects.nonNull(estimationRevenue.getEstimateAmount())
                                && estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setEstimateRatio(detailDto.getEstimateAmount().divide(estimationRevenue.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                        }
                    }
                    if (Objects.nonNull(detailDto.getEstimateAmount())
                            && Objects.nonNull(detailDto.getDifferenceAmount())
                            && detailDto.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                        detailDto.setDifferenceRatio(detailDto.getDifferenceAmount().divide(detailDto.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                    }
                    detailDto.setProjectCode(parentCode);
                    detailDto.setProjectName(childList.get(0).getParentBudgetSubjectsName());
                    detailDto.setSort(index.getAndIncrement());
                    detailDtoList.add(detailDto);
                });
        detailDtoList.forEach(detailDto -> {
            if (Objects.isNull(detailDto.getSort())) {
                RegionCollectProjectEnum collectProjectEnum = RegionCollectProjectEnum.findByCode(detailDto.getProjectCode());
                if (Objects.nonNull(collectProjectEnum)) {
                    detailDto.setSort(collectProjectEnum.getSort());
                } else {
                    detailDto.setSort(index.getAndIncrement());
                }
            }
        });
        List<RegionCollectDetailDto> detailSendDtoList = detailDtoList.stream().sorted(Comparator.comparingInt(RegionCollectDetailDto::getSort)).collect(Collectors.toList());

        //过滤掉“生产成本”、“产品运输费用”、“周边运输费用”、“边际贡献”
        detailSendDtoList = detailSendDtoList.stream().filter(k -> !Arrays.asList(
                RegionCollectProjectEnum.production_cost.getCode(),
                RegionCollectProjectEnum.product_transport_cost.getCode(),
                RegionCollectProjectEnum.marginal_contribution.getCode(),
                RegionCollectProjectEnum.periphery_transport.getCode()).contains(k.getProjectCode())).collect(Collectors.toList());

        List<JSONArray> details = Lists.newArrayList();
        details.add(JSONArray.parseArray(JSON.toJSONString(detailSendDtoList)));

        //查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
            caseVoList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()))
                    .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
        }
        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        Set<String> bearDepartmentNameSet = new HashSet<>();
        caseVoList.stream().filter(k -> StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                .collect(Collectors.groupingBy(e -> e.getCostCenterCode() + e.getCostTypeCategorySecondName(), Collectors.toList())).forEach((key, list) -> {
                    CostCenterDetailDto detailDto = new CostCenterDetailDto();
                    detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
                    detailDto.setCostCenterName(list.get(0).getCostCenterName());
                    List<OrgOaOrgVo> orgOaOrgVos = orgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
                    if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                        bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
                    } else {
                        detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
                    }
                    detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
                    detailDto.setBearAmount(list.stream().map(MarketingPlanCaseVo::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                    costCenterDetailDtoList.add(detailDto);
                });
        Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);
        details.add(JSONArray.parseArray(JSON.toJSONString(costCenterDetailDtoList)));

        List<MarketPlanCaseDetailDto> marketPlanCaseDetailDtoList = Lists.newArrayList();
        caseVoList.forEach(e -> {
            MarketPlanCaseDetailDto detailDto = new MarketPlanCaseDetailDto();
            detailDto.setFysybm(e.getBelongDepartmentName());
            detailDto.setKhmc(e.getCustomerName());
            detailDto.setMdmc(e.getTerminalName());
            detailDto.setFyxm(e.getDetailName());
            detailDto.setFygzyf(e.getYears());
            detailDto.setSqje(e.getApplyAmount());
            detailDto.setFaghmc(e.getSchemeName());
            detailDto.setHdms(e.getActDesc());
            detailDto.setFamxbm(e.getSchemeDetailCode());
            detailDto.setCbzx(e.getCostCenterName());
            detailDto.setXzjghtybzs(orgOaOrgVoMap.get(e.getBearDepartmentCode()).get(0).getOaOrgCode());
            marketPlanCaseDetailDtoList.add(detailDto);
        });
        details.add(JSONArray.parseArray(JSON.toJSONString(marketPlanCaseDetailDtoList)));

        JSONObject response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, details, workflowId, workflowName,
                mainTableMethod, detailTableMethod, detailTableMethod1, detailTableMethod2);

        String processNUmber = null;
        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        if (response.containsKey("code")) {
            Integer resultCode = response.getInteger("code");
            if (resultCode == 100) {
                JSONObject ja = response.getJSONObject("data");
                RegionCollect entity = regionCollectRepository.queryByIdOrCollectCode(null, order.getCollectCode());
                if (entity != null) {
                    entity.setProcessNumber(ja.getString("out"));
                    entity.setProcessDate(new Date());
                    entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
                    entity.setOaId(response.getString("oaId"));
                    entity.setOaUserName(response.getString("oaUserName"));
                    regionCollectRepository.updateById(entity);
                    processNUmber = entity.getProcessNumber();
                } else {
                    MarketingPlan marketingPlan = marketingPlanRepository.queryById(order.getId());
                    marketingPlan.setProcessNumber(ja.getString("out"));
                    marketingPlan.setProcessDate(new Date());
                    marketingPlan.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
                    marketingPlan.setOaId(response.getString("oaId"));
                    marketingPlan.setOaUserName(response.getString("oaUserName"));
                    marketingPlanRepository.updateById(marketingPlan);
                    processNUmber = marketingPlan.getProcessNumber();

                }
            } else {
                Validate.isTrue(false, "OA流程提交失败，错误信息：" + response.getString("msg"));
            }
        }

        return processNUmber;
    }

    /**
     * 重新提交OA
     *
     * @param
     * @return
     */
    @Override
    public JSONObject resubmitOa(RegionCollectVo order) {
        List<MarketingPlanCaseVo> caseVoList;
        if (StringUtils.isNotBlank(order.getCollectCode())) {
            order.setBusinessCode(order.getCollectCode());
            caseVoList = marketingPlanCaseService.findByCollectCode(order.getCollectCode());
        } else {
            order.setBusinessCode(order.getSchemeCode());
            order.setRemark(order.getSchemeDesc());
            caseVoList = marketingPlanCaseService.findBySchemeCode(order.getSchemeCode());
        }
        OaResubmitDto dto = new OaResubmitDto();

        // 获取当前登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType = MqConstant.TAG_TPM_REGION_COLLECT;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();

        if (StringUtils.isNotBlank(order.getCollectCode())) {
            order.setTpmUrl(domainName + TpmOaPageEnum.ACTIVITY_REGION_MANAGE_FORM.getUrlCode() + "?id=" + order.getId());
            dto.setRequestName("大区方案汇总：" + order.getYears() + "-" + order.getOrgName() + "-营销活动方案申请");
        } else {
            order.setTpmUrl(domainName + TpmOaPageEnum.ACTIVITY_MARKET_PLAN_CREATE.getUrlCode() + "?code=" + order.getSchemeCode());
            dto.setRequestName("营销方案申请：" + order.getSchemeName());
        }
        Set<String> orgCodeList = caseVoList.stream().map(MarketingPlanCaseVo::getBearDepartmentCode).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            order.setDeptCode(String.join(",", oaOrgCodeSet));
        }

        List<RegionCollectMarketingEstimationVo> regionCollectMarketingEstimationVos = order.getMarketingEstimationList();
        regionCollectMarketingEstimationVos = Optional.ofNullable(regionCollectMarketingEstimationVos).orElse(Lists.newArrayList());
        Map<String, RegionCollectMarketingEstimationVo> marketingMap = regionCollectMarketingEstimationVos.stream()
                .collect(Collectors.toMap(RegionCollectMarketingEstimationVo::getProjectCode, v -> v, (a, b) -> a));
        RegionCollectMarketingEstimationVo estimationRevenue = marketingMap.get(RegionCollectProjectEnum.estimation_revenue.getCode());

        if (!CollectionUtils.isEmpty(order.getMarketingEstimationList())) {
            //回写预估费用、预估销售额、费率
            Map<String, BigDecimal> map = order.getMarketingEstimationList().stream().collect(Collectors.toMap(RegionCollectMarketingEstimationVo::getProjectCode, x -> Optional.ofNullable(x.getEstimateAmount()).orElse(BigDecimal.ZERO)));
            BigDecimal applyAmount = map.getOrDefault(RegionCollectProjectEnum.marketing_cost_total.getCode(), BigDecimal.ZERO);
            BigDecimal estimatedSalesVolume = map.getOrDefault(RegionCollectProjectEnum.estimation_revenue.getCode(), BigDecimal.ZERO);
            order.setApplyAmount(applyAmount);
            order.setEstimatedSalesVolume(estimatedSalesVolume);
            order.setRatio(BigDecimal.ZERO);
            if (estimatedSalesVolume.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = applyAmount.divide(estimatedSalesVolume, 4, BigDecimal.ROUND_HALF_DOWN);
                order.setRatio(ratio);
            }

            RegionCollectMarketingEstimationVo marketingCostTotal = marketingMap.get(RegionCollectProjectEnum.marketing_cost_total.getCode());
            RegionCollectMarketingEstimationVo profit = marketingMap.get(RegionCollectProjectEnum.profit.getCode());

            //预算费率=营销费用预算达成金额/预计收入预算达成金额
            BigDecimal ratioBudget = (estimationRevenue.getBudgetAmount() == null || estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO :
                    (marketingCostTotal.getBudgetAmount() == null ? BigDecimal.ZERO : marketingCostTotal.getBudgetAmount()).divide(estimationRevenue.getBudgetAmount(), 2, BigDecimal.ROUND_HALF_UP);
            order.setRatioBudget(ratioBudget);
            //规划业绩预算达成率=预计达成金额/预算达成金额
            BigDecimal achieveRatio = (estimationRevenue.getBudgetAmount() == null || estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO :
                    (estimationRevenue.getEstimateAmount() == null ? BigDecimal.ZERO : estimationRevenue.getEstimateAmount()).divide(estimationRevenue.getBudgetAmount(), 2, BigDecimal.ROUND_HALF_UP);
            order.setAchieveRatio(achieveRatio);
            //利润预计达成金额
            order.setProfitMargin(profit.getEstimateAmount());
            //利润预算达成金额
            order.setProfitMarginBudget(profit.getBudgetAmount());
            //规划收入
            order.setEstimateAmount(estimationRevenue.getEstimateAmount());
            //预算收入
            order.setBudgetAmount(estimationRevenue.getBudgetAmount());
            //营销费用小计-预算达成
            order.setMarketingBudgetAmount(marketingCostTotal.getBudgetAmount());
            //超费率=规划费率-预算费率（如果规划收入为0，则费率默认为0）
            order.setCflghflysfl(order.getRatio().subtract(ratioBudget));
            //规划利润率=规划利润额/规划收入（如果规划收入为0，则费率默认为0）
            order.setLrl(estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : profit.getEstimateAmount().divide(estimationRevenue.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_UP));
            //预算利润率=预算利润率/预算收入（如果规划收入为0，则费率默认为0）
            order.setYslrl(estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : profit.getBudgetAmount().divide(estimationRevenue.getEstimateAmount(), 4, BigDecimal.ROUND_HALF_UP));
            //超亏利润额=规划利润额-预算利润额
            order.setCklrelreyslre(profit.getEstimateAmount().subtract(profit.getBudgetAmount()));
        }

        dto.setBusinessCode(businessType);
        dto.setRequestId(order.getProcessNumber());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        List<String> projectCodeList = regionCollectMarketingEstimationVos.stream().map(RegionCollectMarketingEstimationVo::getProjectCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListByCategoryCodes(projectCodeList);
        Set<String> categoryCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getCategoryCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        Map<String, String> budgetSubjectsCategoryCodeMap = costTypeCategoryVos.stream().filter(k -> StringUtil.isNotEmpty(k.getBudgetSubjectsCode()))
                .collect(Collectors.toMap(CostTypeCategoryVo::getBudgetSubjectsCode, CostTypeCategoryVo::getCategoryCode, (n, o) -> n));
        Set<String> budgetSubjectsCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getBudgetSubjectsCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        List<BudgetSubjectsVo> budgetSubjectsVos = budgetSubjectsVoService.findByCodes(budgetSubjectsCodeSet);
        List<RegionCollectDetailDto> detailDtoList = (List<RegionCollectDetailDto>) nebulaToolkitService.copyCollectionByBlankList(
                regionCollectMarketingEstimationVos.stream().filter(k -> StringUtil.isNotEmpty(k.getProjectCode()))
                        .filter(k -> !categoryCodeSet.contains(k.getProjectCode())).collect(Collectors.toList()),
                RegionCollectMarketingEstimationVo.class, RegionCollectDetailDto.class, LinkedHashSet.class, ArrayList.class);
        AtomicInteger index = new AtomicInteger(20);
        budgetSubjectsVos.stream().filter(k -> StringUtil.isNotEmpty(k.getParentBudgetSubjectsCode()))
                .collect(Collectors.groupingBy(BudgetSubjectsVo::getParentBudgetSubjectsCode))
                .forEach((parentCode, childList) -> {
                    RegionCollectDetailDto detailDto = new RegionCollectDetailDto();
                    detailDto.setProjectName(childList.get(0).getParentBudgetSubjectsName());
                    AtomicReference<BigDecimal> budgetAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> estimateAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> differenceAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    childList.forEach(k -> {
                        String categoryCode = budgetSubjectsCategoryCodeMap.getOrDefault(k.getBudgetSubjectsCode(), "");
                        RegionCollectMarketingEstimationVo estimationVo = marketingMap.get(categoryCode);
                        if (Objects.isNull(estimationVo)) {
                            return;
                        }
                        if (Objects.nonNull(estimationVo.getBudgetAmount())) {
                            budgetAmountTotal.set(estimationVo.getBudgetAmount().add(budgetAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getEstimateAmount())) {
                            estimateAmountTotal.set(estimationVo.getEstimateAmount().add(estimateAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getDifferenceAmount())) {
                            differenceAmountTotal.set(estimationVo.getDifferenceAmount().add(differenceAmountTotal.get()));
                        }
                    });

                    detailDto.setBudgetAmount(budgetAmountTotal.get());
                    detailDto.setEstimateAmount(estimateAmountTotal.get());
                    detailDto.setDifferenceAmount(differenceAmountTotal.get());
                    if (Objects.nonNull(estimationRevenue)) {
                        if (Objects.nonNull(estimationRevenue.getBudgetAmount())
                                && estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setBudgetRatio(detailDto.getBudgetAmount().divide(estimationRevenue.getBudgetAmount(), 2, RoundingMode.HALF_UP));
                        }
                        if (Objects.nonNull(estimationRevenue.getEstimateAmount())
                                && estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setEstimateRatio(detailDto.getEstimateAmount().divide(estimationRevenue.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                        }
                    }
                    if (Objects.nonNull(detailDto.getEstimateAmount())
                            && Objects.nonNull(detailDto.getDifferenceAmount())
                            && detailDto.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                        detailDto.setDifferenceRatio(detailDto.getDifferenceAmount().divide(detailDto.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                    }
                    detailDto.setProjectCode(parentCode);
                    detailDto.setProjectName(childList.get(0).getParentBudgetSubjectsName());
                    detailDto.setSort(index.getAndIncrement());
                    detailDtoList.add(detailDto);
                });
        detailDtoList.forEach(detailDto -> {
            if (Objects.isNull(detailDto.getSort())) {
                RegionCollectProjectEnum collectProjectEnum = RegionCollectProjectEnum.findByCode(detailDto.getProjectCode());
                if (Objects.nonNull(collectProjectEnum)) {
                    detailDto.setSort(collectProjectEnum.getSort());
                } else {
                    detailDto.setSort(index.getAndIncrement());
                }
            }
        });
        List<RegionCollectDetailDto> detailSendDtoList = detailDtoList.stream().sorted(Comparator.comparingInt(RegionCollectDetailDto::getSort)).collect(Collectors.toList());

        //过滤掉“生产成本”、“产品运输费用”、“周边运输费用”、“边际贡献”
        detailSendDtoList = detailSendDtoList.stream().filter(k -> !Arrays.asList(
                RegionCollectProjectEnum.production_cost.getCode(),
                RegionCollectProjectEnum.product_transport_cost.getCode(),
                RegionCollectProjectEnum.marginal_contribution.getCode(),
                RegionCollectProjectEnum.periphery_transport.getCode()).contains(k.getProjectCode())).collect(Collectors.toList());


        RegionCollectMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(order, RegionCollectMainDto.class, LinkedHashSet.class, ArrayList.class);
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));
        //查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
            caseVoList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()))
                    .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
        }
        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        Set<String> bearDepartmentNameSet = new HashSet<>();
        caseVoList.stream().filter(k -> StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                .collect(Collectors.groupingBy(e -> e.getCostCenterCode() + e.getCostTypeCategorySecondName(), Collectors.toList())).forEach((key, list) -> {
            CostCenterDetailDto detailDto = new CostCenterDetailDto();
            detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
            detailDto.setCostCenterName(list.get(0).getCostCenterName());
            List<OrgOaOrgVo> orgOaOrgVos = orgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
            if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
            } else {
                detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
            }
            detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
            detailDto.setBearAmount(list.stream().map(MarketingPlanCaseVo::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            costCenterDetailDtoList.add(detailDto);
        });
        Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);

        List<MarketPlanCaseDetailDto> marketPlanCaseDetailDtoList = Lists.newArrayList();
        caseVoList.forEach(e -> {
            MarketPlanCaseDetailDto detailDto = new MarketPlanCaseDetailDto();
            detailDto.setFysybm(e.getBelongDepartmentName());
            detailDto.setKhmc(e.getCustomerName());
            detailDto.setMdmc(e.getTerminalName());
            detailDto.setFyxm(e.getDetailName());
            detailDto.setFygzyf(e.getYears());
            detailDto.setSqje(e.getApplyAmount());
            detailDto.setFaghmc(e.getSchemeName());
            detailDto.setHdms(e.getActDesc());
            detailDto.setFamxbm(e.getSchemeDetailCode());
            detailDto.setCbzx(e.getCostCenterName());
            detailDto.setXzjghtybzs(orgOaOrgVoMap.get(e.getBearDepartmentCode()).get(0).getOaOrgCode());
            marketPlanCaseDetailDtoList.add(detailDto);
        });

        OaDetailDto oaDetailDto = new OaDetailDto();
        oaDetailDto.setDetailList(JSONUtil.toJsonStr(detailSendDtoList));
        oaDetailDto.setDetailClass(RegionCollectDetailDto.class);
        OaDetailDto costCenterDetailDto = new OaDetailDto();
        costCenterDetailDto.setDetailList(JSONUtil.toJsonStr(costCenterDetailDtoList));
        costCenterDetailDto.setDetailClass(CostCenterDetailDto.class);
        OaDetailDto marketPlanCaseDetailDto = new OaDetailDto();
        marketPlanCaseDetailDto.setDetailList(JSONUtil.toJsonStr(marketPlanCaseDetailDtoList));
        marketPlanCaseDetailDto.setDetailClass(MarketPlanCaseDetailDto.class);
        dto.setDetailList(Arrays.asList(oaDetailDto, costCenterDetailDto, marketPlanCaseDetailDto));


        RegionCollect entity = regionCollectRepository.queryByIdOrCollectCode(null, order.getCollectCode());
        if (ryOaProcessService.resubmit(dto)) {
            if (entity != null) {
                entity.setProcessDate(new Date());
                entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
                regionCollectRepository.updateById(entity);
            } else {
                MarketingPlan marketingPlan = marketingPlanRepository.queryById(order.getId());
                marketingPlan.setProcessDate(new Date());
                marketingPlan.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
                marketingPlanRepository.updateById(marketingPlan);
            }
        }
        return null;
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public boolean oaWithdraw(String code, String remark) {
        RegionCollect entity = regionCollectRepository.queryByIdOrCollectCode(null, code);
        Validate.isTrue(entity.getProcessStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(entity.getProcessNumber()));
        dto.setProcessCreateId(entity.getOaId());
        dto.setUserName(entity.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (RegionCollectFieldsEnum value : RegionCollectFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod = (tableDetailFields, detailJsonObject) -> {
        for (MarketingEstimationFieldsEnum value : MarketingEstimationFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod1 = (tableDetailFields, detailJsonObject) -> {
        for (CostCenterFieldsEnum value : CostCenterFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod2 = (tableDetailFields, detailJsonObject) -> {
        for (MarketPlanCaseFieldsEnum value : MarketPlanCaseFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
}
