package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.entity.PlanBigDateExcution
 * @description: 大日期回调执行计划 商品明细
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 13:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_plan_big_date_execution_collect_detail")
@Table(
        name = "tpm_plan_big_date_execution_collect_detail",
        indexes = {
                @Index(name = "tpm_plan_big_date_execution_collect_detail_index0", columnList = "collect_id"),
                @Index(name = "tpm_plan_big_date_execution_collect_detail_index1", columnList = "product_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_plan_big_date_execution_collect_detail", comment = "大日期回调执行计划商品明细")
@ApiModel(value = "PlanBigDateExecutionCollectDetail", description = "大日期回调执行计划商品明细")
public class PlanBigDateExecutionCollectDetail extends UuidFlagOpEntity {

    @ApiModelProperty("大日期回调采集id")
    @Column(name = "collect_id", columnDefinition = "varchar(32) comment '大日期回调采集id'")
    private String collectId;

    @ApiModelProperty("产品层级编码")
    @Column(name = "product_level_code", columnDefinition = "varchar(32) comment '产品层级编码'")
    private String productLevelCode;

    @ApiModelProperty("产品层级名称")
    @Column(name = "product_level_name", columnDefinition = "varchar(64) comment '产品层级名称'")
    private String productLevelName;

    @ApiModelProperty("商品编码")
    @Column(name = "product_code", columnDefinition = "VARCHAR(32) COMMENT '商品编码'")
    private String productCode;

    @ApiModelProperty("物料编码")
    @Column(name = "material_code", columnDefinition = "VARCHAR(32) COMMENT '物料编码'")
    private String materialCode;

    @ApiModelProperty("条形码")
    @TableField(value = "bar_code")
    @Column(name = "bar_code", columnDefinition = "VARCHAR(32) COMMENT '条形码'")
    private String barCode;

    @ApiModelProperty("产品名称")
    @Column(name = "product_name", columnDefinition = "varchar(64) comment '产品名称'")
    private String productName;

    @ApiModelProperty("图片URL")
    @Column(name = "product_img_url", columnDefinition = "varchar(128) comment '图片URL'")
    private String productImgUrl;

    @ApiModelProperty("产品单位编码")
    @Column(name = "product_unit", columnDefinition = "varchar(32) comment '产品单位编码'")
    private String productUnit;

    @ApiModelProperty("产品单位名称")
    @Column(name = "product_unit_name", columnDefinition = "varchar(64) comment '产品单位名称'")
    private String productUnitName;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("产品批次")
    private List<PlanBigDateExecutionCollectDetailChild> expirationBackBatchs;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("采集图片")
    private List<PlanBigDateExecutionCollectDetailPicture> expirationBackPictures;

}
