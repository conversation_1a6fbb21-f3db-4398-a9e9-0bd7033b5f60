package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimationBack;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectDepartmentEstimationBackMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class RegionCollectDepartmentEstimationBackRepository extends ServiceImpl<RegionCollectDepartmentEstimationBackMapper, RegionCollectDepartmentEstimationBack> {


    public List<RegionCollectDepartmentEstimationBack> findByCollectCode(String collectCode) {
        return this.lambdaQuery()
                .eq(RegionCollectDepartmentEstimationBack::getCollectCode, collectCode).list();
    }


    public void deleteByCollectCodes(List<String> collectCodes) {
        this.lambdaUpdate()
                .in(RegionCollectDepartmentEstimationBack::getCollectCode, collectCodes).remove();
    }
}
