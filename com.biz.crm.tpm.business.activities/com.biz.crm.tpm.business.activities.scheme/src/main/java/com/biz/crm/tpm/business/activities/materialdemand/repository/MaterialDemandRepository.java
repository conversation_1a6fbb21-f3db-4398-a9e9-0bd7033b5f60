package com.biz.crm.tpm.business.activities.materialdemand.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemand;
import com.biz.crm.tpm.business.activities.materialdemand.mapper.MaterialDemandMapper;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:34
 */
@Component
public class MaterialDemandRepository extends ServiceImpl<MaterialDemandMapper, MaterialDemand> {

    public Page<MaterialDemandVo> findList(Page<MaterialDemandVo> page, MaterialDemandVo vo) {
        vo.setTenantCode(TenantUtils.getTenantCode());
        return this.baseMapper.findList(page, vo);
    }

    public MaterialDemand queryByIdOrCode(String id, String code) {
        return this.lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(id), MaterialDemand::getId, id)
                .eq(ObjectUtils.isNotEmpty(code), MaterialDemand::getDemandCode, code)
                .eq(MaterialDemand::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MaterialDemand::getTenantCode, TenantUtils.getTenantCode()).one();
    }

    public List<MaterialDemand> findListByIdList(List<String> idList) {
        return this.lambdaQuery()
                .in(MaterialDemand::getId, idList)
                .eq(MaterialDemand::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MaterialDemand::getTenantCode, TenantUtils.getTenantCode()).list();
    }

    public List<MaterialDemandDetailVo> findListByOrgCodes(List<String> orgCodes, List<String> processStatusList) {
        return this.baseMapper.findListByOrgCodes(orgCodes, processStatusList);
    }

    public List<MaterialDemandDetailVo> selectMaterialDemandDetailList(List<String> inExcludeDemandDetailCodes, List<String> demandCodes) {
        return this.baseMapper.selectMaterialDemandDetailList(inExcludeDemandDetailCodes, demandCodes);
    }

    public MaterialDemand findByOrgCodeAndYears(String orgCode, String years) {
        return this.lambdaQuery()
                .eq(MaterialDemand::getOrgCode, orgCode)
                .eq(MaterialDemand::getYears, years)
                .eq(MaterialDemand::getTenantCode, TenantUtils.getTenantCode())
                .eq(MaterialDemand::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
    }

    public void updateProcessStatus(String processStatus, List<String> demandCodes) {
        this.lambdaUpdate()
                .in(MaterialDemand::getDemandCode, demandCodes)
                .set(MaterialDemand::getProcessStatus, processStatus)
                .update();
    }

    @Transactional
    public void updateCollectStatus(String collectStatus, List<String> demandCodes) {
        if (CollectionUtils.isEmpty(demandCodes)) {
            return;
        }
        this.lambdaUpdate()
            .in(MaterialDemand::getDemandCode, demandCodes)
            .set(MaterialDemand::getCollectStatus, collectStatus)
            .update();
    }
}
