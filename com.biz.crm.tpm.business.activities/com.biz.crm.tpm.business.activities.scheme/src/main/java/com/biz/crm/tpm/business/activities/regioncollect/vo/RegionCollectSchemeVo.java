package com.biz.crm.tpm.business.activities.regioncollect.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "RegionCollectSchemeVo", description = "大区汇总方案明细")
public class RegionCollectSchemeVo extends UuidFlagOpVo {

    @ApiModelProperty("汇总编码")
    private String collectCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    private String schemeName;

    private String userName;

    private String fullName;

    private String orgCode;

    private String orgName;

}
