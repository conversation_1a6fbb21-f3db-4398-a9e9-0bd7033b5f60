<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanExceutionMapper">

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo">
        SELECT
        t.*
        FROM
        tpm_plan_exceution t
        left join tpm_marketing_plan_case tpmc on t.scheme_detail_code = tpmc.scheme_detail_code
        WHERE
        1 =1
        <if test="dto.belongDepartmentCodeList != null and dto.belongDepartmentCodeList.size() > 0">
            and tpmc.belong_department_code in
            <foreach collection="dto.belongDepartmentCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.executor != null and dto.executor != ''">
            <bind name="executorLike" value="dto.executor + '%'"/>
            AND t.executor like #{executorLike}
        </if>
        <if test="dto.result != null and dto.result != ''">
            AND t.result = #{dto.result}
        </if>
        <if test="dto.schemeCode != null and dto.schemeCode != ''">
            <bind name="schemeCodeLike" value="dto.schemeCode + '%'"/>
            AND t.scheme_code like #{schemeCodeLike}
        </if>
        <if test="dto.schemeDetailCode != null and dto.schemeDetailCode != ''">
            <bind name="schemeDetailCodeLike" value="dto.schemeDetailCode + '%'"/>
            AND t.scheme_detail_code like #{schemeDetailCodeLike}
        </if>
        <if test="dto.terminalCode != null and dto.terminalCode != ''">
            <bind name="terminalCodeLike" value="dto.terminalCode + '%'"/>
            AND t.terminal_code like #{terminalCodeLike}
        </if>
        <if test="dto.terminalName != null and dto.terminalName != ''">
            <bind name="terminalNameLike" value="dto.terminalName + '%'"/>
            AND t.terminal_name like #{terminalNameLike}
        </if>
        <if test="dto.executeStartDate != null and dto.executeStartDate != ''">
            <![CDATA[and t.execute_date >= #{dto.executeStartDate}]]>
        </if>
        <if test="dto.executeEndDate != null and dto.executeEndDate != ''">
            <![CDATA[and t.execute_date <= #{dto.executeEndDate}]]>
        </if>
        <if test="dto.customerCode != null and dto.customerCode != ''">
            <bind name="customerCodeLike" value="dto.customerCode + '%'"/>
            AND t.customer_code like #{customerCodeLike}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="customerNameLike" value="dto.customerName + '%'"/>
            AND t.customer_name like #{customerNameLike}
        </if>
        ORDER BY t.create_time desc, t.id desc
    </select>
</mapper>

