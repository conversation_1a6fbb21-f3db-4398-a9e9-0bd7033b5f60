package com.biz.crm.tpm.business.activities.contract.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.contract.entity.ExternalContractProduct;
import com.biz.crm.tpm.business.activities.contract.mapper.ExternalContractProductMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 11:40
 */
@Component
public class ExternalContractProductRepository extends ServiceImpl<ExternalContractProductMapper, ExternalContractProduct> {

    public List<ExternalContractProduct> findProductListByContractCode(String contractCode) {
        return this.lambdaQuery()
                .eq(ExternalContractProduct::getContractCode, contractCode).list();
    }

    public void deleteByContractCode(String contractCode) {
        this.remove(Wrappers.lambdaQuery(ExternalContractProduct.class)
                .eq(ExternalContractProduct::getContractCode, contractCode));
    }

    public List<ExternalContractProduct> findProductListByContractCodeSet(Set<String> contractCodes) {
        return this.lambdaQuery()
                .in(ExternalContractProduct::getContractCode, contractCodes)
                .list();
    }

    public List<ExternalContractProduct> findProductListByOnlyKeySet(Set<String> onlyKeys) {
        return this.lambdaQuery()
                .in(ExternalContractProduct::getOnlyKey, onlyKeys)
                .list();
    }

    public void removeByOnlyKeys(List<String> onlyKeys) {
        if (CollectionUtils.isEmpty(onlyKeys)) {
            return;
        }
        this.lambdaUpdate()
                .eq(ExternalContractProduct::getContractCode, onlyKeys)
                .remove();

    }
}
