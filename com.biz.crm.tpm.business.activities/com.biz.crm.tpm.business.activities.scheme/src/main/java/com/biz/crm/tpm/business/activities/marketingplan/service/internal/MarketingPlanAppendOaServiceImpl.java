package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanAppendOaService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.sdk.enums.CostCenterFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketPlanAppendDetailFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketPlanAppendFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketPlanCaseFieldsEnum;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.CostCenterDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.MarketPlanAdditionalDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.MarketPlanAdditionalMainDto;
import com.biz.crm.workflow.sdk.dto.oa.order.MarketPlanCaseDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class MarketingPlanAppendOaServiceImpl implements MarketingPlanAppendOaService {

    @Autowired(required = false)
    private MarketingPlanRepository marketingPlanRepository;
    @Autowired(required = false)
    private PushOaService pushOaService;
    @Autowired
    private UserVoService userVoService;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Value("${domain-name:}")
    private String domainName;
    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private CostTypeCategoryVoService costTypeCategoryVoService;

    /**
     * 推送OA
     *
     * @param
     * @return
     */
    @Override
    public JSONObject pushOa(MarketingPlanVo order) {
        order.setBusinessCode(order.getSchemeCode());
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(order));
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(order.getDetailCaseMap())) {
            List<MarketingPlanCaseVo> detailCaseList = order.getDetailCaseMap().values().stream().flatMap(List::stream).collect(Collectors.toList());
            Set<String> orgCodeList = detailCaseList.stream().map(e -> e.getBearDepartmentCode()).collect(Collectors.toSet());
            orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
            if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
                Set<String> oaOrgCodeSet = new HashSet<>();
                orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
                orderJsonObject.put("deptCode", oaOrgCodeSet.stream().collect(Collectors.joining(",")));
            }
        }

//        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
//        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        orderJsonObject.put("remark", order.getSchemeDesc());
        orderJsonObject.put("title", "方案追加：" + order.getSchemeName());

        // 主表
        // 业务类型
        String businessType = MqConstant.TAG_TPM_MARKET_PLAN_ADDITIONAL;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.ACTIVITY_MARKET_PLAN_APPENDFORM.getUrlCode() + "?code=" + order.getSchemeCode());
        List<JSONArray> jsonArray = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(order.getDetailCaseMap())){
            List<MarketingPlanCaseVo> caseVoList = new ArrayList<>();
            order.getDetailCaseMap().values().forEach(e -> caseVoList.addAll(e));
            caseVoList.forEach(e -> e.setPlanYears(e.getYears()));
            jsonArray.add(JSONArray.parseArray(JSON.toJSONString(caseVoList)));

            //查询二级科目关联的活动大类
            List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
            //key-二级关联科目
            Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
            for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
                caseVoList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()))
                        .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
            }
            List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
            Set<String> bearDepartmentNameSet = new HashSet<>();
            Map<String, List<OrgOaOrgVo>> finalOrgOaOrgVoMap = orgOaOrgVoMap;
            caseVoList.stream().filter(k -> StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                    .collect(Collectors.groupingBy(e -> e.getCostCenterCode() + e.getCostTypeCategorySecondName(), Collectors.toList())).forEach((key, list) -> {
                CostCenterDetailDto detailDto = new CostCenterDetailDto();
                detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
                detailDto.setCostCenterName(list.get(0).getCostCenterName());
                List<OrgOaOrgVo> orgOaOrgVos = finalOrgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
                if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                    bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
                } else {
                    detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
                }
                detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
                detailDto.setBearAmount(list.stream().map(MarketingPlanCaseVo::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                costCenterDetailDtoList.add(detailDto);
            });
            Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);
            jsonArray.add(JSONArray.parseArray(JSON.toJSONString(costCenterDetailDtoList)));

        }
        JSONObject response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, jsonArray, workflowId, workflowName,
                mainTableMethod, detailTableMethod, detailTableMethod1);

        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        if (response.containsKey("code")) {
            Integer resultCode = response.getInteger("code");
            if (resultCode == 100) {
                JSONObject ja = response.getJSONObject("data");
                MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, order.getSchemeCode());
                entity.setProcessNumber(ja.getString("out"));
                entity.setProcessDate(new Date());
                entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
                entity.setOaId(response.getString("oaId"));
                entity.setOaUserName(response.getString("oaUserName"));
                marketingPlanRepository.updateById(entity);
            } else {
                Validate.isTrue(false, "OA流程提交失败，错误信息：" + response.getString("msg"));
            }
        }

        return response;
    }

    /**
     * 重新提交OA
     *
     * @param
     * @return
     */
    @Override
    public JSONObject resubmitOa(MarketingPlanVo order) {
        order.setBusinessCode(order.getSchemeCode());
        OaResubmitDto dto = new OaResubmitDto();

        // 获取当前登录人信息
        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);

        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType = MqConstant.TAG_TPM_MARKET_PLAN_ADDITIONAL;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        order.setTpmUrl(domainName + TpmOaPageEnum.ACTIVITY_MARKET_PLAN_APPENDFORM.getUrlCode() + "?code=" + order.getSchemeCode());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(order.getDetailCaseMap())) {
            List<MarketingPlanCaseVo> detailCaseList = order.getDetailCaseMap().values().stream().flatMap(List::stream).collect(Collectors.toList());
            Set<String> orgCodeList = detailCaseList.stream().map(e -> e.getBearDepartmentCode()).collect(Collectors.toSet());
            orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
            if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
                Set<String> oaOrgCodeSet = new HashSet<>();
                orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
                order.setDeptCode(oaOrgCodeSet.stream().collect(Collectors.joining(",")));
            }
        }


        dto.setBusinessCode(businessType);
        dto.setRequestId(order.getProcessNumber());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        List<MarketingPlanCaseVo> caseVoList = new ArrayList<>();
        order.getDetailCaseMap().values().forEach(e -> caseVoList.addAll(e));
        caseVoList.forEach(e -> e.setPlanYears(e.getYears()));

        MarketPlanAdditionalMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(order, MarketPlanAdditionalMainDto.class, LinkedHashSet.class, ArrayList.class);
        mainDto.setRemark(order.getSchemeDesc());
        dto.setRequestName("方案追加：" + order.getSchemeName());
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));
        List<MarketPlanAdditionalDetailDto> detailDtoList = (List<MarketPlanAdditionalDetailDto>) nebulaToolkitService.copyCollectionByBlankList(caseVoList, MarketingPlanCaseVo.class, MarketPlanAdditionalDetailDto.class, LinkedHashSet.class, ArrayList.class);

        //查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
            caseVoList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()))
                    .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
        }
        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        Set<String> bearDepartmentNameSet = new HashSet<>();
        Map<String, List<OrgOaOrgVo>> finalOrgOaOrgVoMap = orgOaOrgVoMap;
        caseVoList.stream().filter(k -> StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                .collect(Collectors.groupingBy(MarketingPlanCaseVo::getCostCenterCode, Collectors.toList())).forEach((key, list) -> {
            CostCenterDetailDto detailDto = new CostCenterDetailDto();
            detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
            detailDto.setCostCenterName(list.get(0).getCostCenterName());
            List<OrgOaOrgVo> orgOaOrgVos = finalOrgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
            if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
            } else {
                detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
            }
            detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
            detailDto.setBearAmount(list.stream().map(MarketingPlanCaseVo::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            costCenterDetailDtoList.add(detailDto);
        });
        Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);

        OaDetailDto oaDetailDto = new OaDetailDto();
        oaDetailDto.setDetailList(JSONUtil.toJsonStr(detailDtoList));
        oaDetailDto.setDetailClass(MarketPlanAdditionalDetailDto.class);
        OaDetailDto costCenterDetailDto = new OaDetailDto();
        costCenterDetailDto.setDetailList(JSONUtil.toJsonStr(costCenterDetailDtoList));
        costCenterDetailDto.setDetailClass(CostCenterDetailDto.class);
        dto.setDetailList(Arrays.asList(oaDetailDto, costCenterDetailDto));

        if (ryOaProcessService.resubmit(dto)) {
            MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, order.getSchemeCode());
            entity.setProcessDate(new Date());
            entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
            marketingPlanRepository.updateById(entity);
        }
        return null;
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public boolean oaWithdraw(String code, String remark) {
        MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, code);
        Validate.isTrue(entity.getProcessStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(entity.getProcessNumber()));
        dto.setProcessCreateId(entity.getOaId());
        dto.setUserName(entity.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (MarketPlanAppendFieldsEnum value : MarketPlanAppendFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod = (tableDetailFields, detailJsonObject) -> {
        for (MarketPlanAppendDetailFieldsEnum value : MarketPlanAppendDetailFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod1 = (tableDetailFields, detailJsonObject) -> {
        for (CostCenterFieldsEnum value : CostCenterFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
}
