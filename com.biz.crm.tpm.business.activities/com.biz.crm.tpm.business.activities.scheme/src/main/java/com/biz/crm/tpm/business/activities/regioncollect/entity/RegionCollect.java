package com.biz.crm.tpm.business.activities.regioncollect.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.WorkflowFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_region_collect")
@Table(
        name = "tpm_region_collect",
        indexes = {
                @Index(name = "tpm_region_collect_index0", columnList = "tenant_code,collect_code", unique = true),
                @Index(name = "tpm_region_collect_index1", columnList = "del_flag"),
                @Index(name = "tpm_region_collect_index2", columnList = "org_code,years,del_flag")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_region_collect", comment = "大区汇总")
@ApiModel(value = "RegionCollect", description = "大区汇总")
public class RegionCollect extends WorkflowFlagOpEntity {

    @ApiModelProperty("汇总编码")
    @Column(name = "collect_code", columnDefinition = "varchar(32) comment '汇总编码'")
    private String collectCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", nullable = false, columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(10) comment '年月'")
    private String years;

    @ApiModelProperty("预估销售额")
    @Column(name = "estimated_sales_volume", columnDefinition = "decimal(18,4) comment '预估销售额'")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("预估总费用金额")
    @Column(name = "apply_amount", columnDefinition = "decimal(18,4) comment '预估总费用金额'")
    private BigDecimal applyAmount;

    @ApiModelProperty("费率")
    @Column(name = "ratio", columnDefinition = "decimal(10,4) comment '费率'")
    private BigDecimal ratio;

    @ApiModelProperty("费率")
    @Column(name = "ratio_str", columnDefinition = "varchar(10) comment '费率'")
    private String ratioStr;

    @ApiModelProperty("汇总状态")
    @Column(name = "collect_status", columnDefinition = "varchar(20) comment '汇总状态'")
    private String collectStatus;

    @ApiModelProperty("汇总结果")
    @Column(name = "collect_result", columnDefinition = "text comment '汇总结果'")
    private String collectResult;

    @ApiModelProperty("推送日期")
    @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    @Column(name = "oa_id", columnDefinition = "varchar(32) COMMENT 'OA人员id'")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    @Column(name = "oa_user_name", columnDefinition = "varchar(32) COMMENT 'OA人员账号'")
    private String oaUserName;

    @ApiModelProperty("校验预算")
    @Column(name = "check_budget_flag", columnDefinition = "varchar(10) comment '校验预算'")
    private String checkBudgetFlag;
}
