package com.biz.crm.tpm.business.activities.marketingplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.vo.TerminalExpenseVo
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-30 10:08
 */
@Data
@ApiModel(value = "TerminalExpenseVo", description = "终端费用统计Vo")
public class TerminalExpenseVo {

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("年")
    private String year;

    @ApiModelProperty("客户编号")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("终端编码")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;


}
