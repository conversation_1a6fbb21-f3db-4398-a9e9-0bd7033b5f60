<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanEstimationMapper">

    <select id="findOriginalListBySchemeCode" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanEstimationVo">
        SELECT
        *
        FROM
        tpm_region_collect_marketing_estimation
        WHERE
        collect_code = (
        SELECT
        collect_code
        FROM
        tpm_region_collect_scheme
        WHERE
        scheme_code in (
        <foreach collection="schemCodeList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
            )
        )
    </select>
</mapper>

