package com.biz.crm.tpm.business.activities.materialdemand.helper;

import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.tpm.business.activities.materialdemand.constant.MaterialDemandConstant;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@Component
@Slf4j
public class MaterialDemandPageCacheHelper extends BusinessPageCacheHelper<MaterialDemandDetailVo, MaterialDemandDetailVo> {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private MaterialDemandDetailService materialDemandDetailService;

    @Override
    public String getCacheKeyPrefix() {
        return MaterialDemandConstant.MATERIAL_DEMAND_CACHE_PAGE;
    }

    @Override
    public Class<MaterialDemandDetailVo> getDtoClass() {
        return MaterialDemandDetailVo.class;
    }

    @Override
    public Class<MaterialDemandDetailVo> getVoClass() {
        return MaterialDemandDetailVo.class;
    }

    @Override
    public List<MaterialDemandDetailVo> findDtoListFromRepository(MaterialDemandDetailVo vo, String cacheKey) {
        if (ObjectUtils.isEmpty(vo.getId())) {
            return Lists.newArrayList();
        }
        return materialDemandDetailService.findListById(vo.getId());
    }

    @Override
    public List<MaterialDemandDetailVo> newItem(String cacheKey, List<MaterialDemandDetailVo> itemList) {
        MaterialDemandDetailVo vo = new MaterialDemandDetailVo();
        vo.setId(UuidCrmUtil.general());
        return Lists.newArrayList(vo);
    }

    @Override
    public List<MaterialDemandDetailVo> copyItem(String cacheKey, List<MaterialDemandDetailVo> itemList) {
        List<MaterialDemandDetailVo> newList = (List<MaterialDemandDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(itemList, MaterialDemandDetailVo.class, MaterialDemandDetailVo.class,
                HashSet.class, ArrayList.class);
        for (MaterialDemandDetailVo detailVo : newList) {
            detailVo.setId(UuidCrmUtil.general());
        }
        return newList;
    }

    @Override
    public Object getDtoKey(MaterialDemandDetailVo vo) {
        return vo.getId();
    }

    @Override
    public String getCheckedStatus(MaterialDemandDetailVo vo) {
        return vo.getChecked();
    }

    /**
     * 导入新增数据
     *
     * @param cacheKey
     * @param itemList
     * @return
     */
    public void importNewItem(String cacheKey, List<MaterialDemandDetailVo> itemList) {
        String redisCacheIdKey = this.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = this.getRedisCacheDataKey(cacheKey);

        //导入新增数据
        for (MaterialDemandDetailVo newItem : itemList) {
            newItem.setId(UuidCrmUtil.general());
        }

        Object[] newIdArr = itemList.stream().map(this::getDtoKey).toArray();
        redisService.lPushAll(redisCacheIdKey, this.getExpireTime(), newIdArr);

        Map<Object, MaterialDemandDetailVo> updateMap = itemList.stream().collect(Collectors.toMap(this::getDtoKey, Function.identity()));
        redisTemplate.opsForHash().putAll(redisCacheDataKey, updateMap);
        redisService.expire(redisCacheDataKey, this.getExpireTime());
    }
}
