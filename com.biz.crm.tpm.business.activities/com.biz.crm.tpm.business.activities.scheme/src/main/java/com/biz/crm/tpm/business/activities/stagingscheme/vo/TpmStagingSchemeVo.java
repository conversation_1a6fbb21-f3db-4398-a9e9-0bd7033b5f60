package com.biz.crm.tpm.business.activities.stagingscheme.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
/**
 * 方案相关暂存表请求vo
 *
 * <AUTHOR>
 * @date 2023-12-14 10:50:31
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TpmStagingSchemeReqVo", description = "方案相关暂存表")
public class TpmStagingSchemeVo extends UuidFlagOpVo {

    @ApiModelProperty("ID集合")
    private List<String> ids;

    @ApiModelProperty("来源类型")
    private String fromType;

    @ApiModelProperty("json文本")
    private String jsonStr;

    @ApiModelProperty("关联ID")
    private String releaseId;

    private List<String> releaseIds;

    private List<String> fromTypes;

}