package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/12 15:54
 */
@Data
@ApiModel("营销规划方案明细导入vo")
public class MarketingPlanCaseImportVo extends CrmExcelVo implements Serializable {

    @ApiModelProperty("方案明细类型")
    private String caseType;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("原方案明细编码")
    private String originalSchemeDetailCode;

    @ApiModelProperty("关联统筹方案编码")
    private String releaseCode;

    @ApiModelProperty("关联统筹方案名称")
    private String releaseName;

    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("关联总部方案编码")
    private String headSchemeCode;

    @ApiModelProperty("关联总部方案明细编码")
    private String headSchemeDetailCode;

    @ApiModelProperty("排序")
    private Long sort;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("终端编码")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("终端类型")
    private String terminalType;

    @ApiModelProperty("终端渠道")
    private String terminalChannel;

    @ApiModelProperty("终端所属系统")
    private String terminalSystem;

    @ApiModelProperty("兑付方式")
    private String cashType;

    @ApiModelProperty("执行描述")
    private String executeDesc;

    @ApiModelProperty("申请费用")
    private String applyAmount;

    @ApiModelProperty("预估销售额")
    private String estimatedSalesVolume;

    @ApiModelProperty("预估费率")
    private String ratio;

    @ApiModelProperty("活动描述")
    private String actDesc;

    @ApiModelProperty("年度预算编码")
    private String budgetCode;

    @ApiModelProperty("费用依据")
    private String costBasis;

    @ApiModelProperty("费用依据集合")
    private String costBasisList;

    @ApiModelProperty("执行示例")
    private String executeExample;

    @ApiModelProperty("执行示例集合")
    private String executeExampleList;

    @ApiModelProperty("结案示例")
    private String closeCaseExample;

    @ApiModelProperty("结案示例集合")
    private String closeCaseExampleList;

    @ApiModelProperty("合作类型")
    private String cooperateTypeStr;

    @ApiModelProperty("合作类型标签")
    private String cooperateTypeList;

    @ApiModelProperty("客户标签")
    private String customerTagStr;

    @ApiModelProperty("客户标签")
    private String customerTagList;

    @ApiModelProperty("终端标签")
    private String terminalTagStr;

    @ApiModelProperty("终端标签")
    private String terminalTagList;

    @ApiModelProperty("校验")
    private Boolean checkFlag;

    @ApiModelProperty("错误描述")
    private String errMsg;

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("是否合同费用")
    private String isContractCost;

    @ApiModelProperty("返利类型")
    private String rebateType;

    @ApiModelProperty("返利计算日期")
    private Integer rebateCalDay;

    @ApiModelProperty("返利开始时间")
    private String rebateStartDate;

    @ApiModelProperty("返利结束时间")
    private String rebateEndDate;

    @ApiModelProperty("政策形式编码")
    private String conditionFormula;

    @ApiModelProperty("政策形式名称")
    private String conditionFormulaName;

    @ApiModelProperty("我方承担金额")
    private String bearAmount;

    @ApiModelProperty("客户承担金额")
    private String cusBearAmount;

    @ApiModelProperty("行销物料类型")
    private String sellMaterialType;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料数量")
    private String materialNum;

    @ApiModelProperty("物料成本价格")
    private String materialCostPrice;

    @ApiModelProperty("运输方式")
    private String transportType;

    @ApiModelProperty("投放平台")
    private String platform;

    @ApiModelProperty("投放城市")
    private String placingCity;

    @ApiModelProperty("结案金额")
    private BigDecimal auditAmount;

    @ApiModelProperty("结案状态")
    private String auditStatus;

    @ApiModelProperty("兑付金额")
    private BigDecimal cashAmount;

    @ApiModelProperty("兑付状态")
    private String cashStatus;

    @ApiModelProperty("计提金额")
    private BigDecimal withholdingAmount;

    @ApiModelProperty("是否选中")
    private String checked;

    @ApiModelProperty("条件数量")
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    private String giveNum;

    @ApiModelProperty("优惠数量-上限")
    private String discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    private String discountAmount;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("年月小于")
    private String yearsLess;

    @ApiModelProperty("结束时间小于")
    private String endDateLess;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("审批状态")
    private String processStatus;

    @ApiModelProperty("活动状态")
    private String actStatus;

    @ApiModelProperty("陈列卡板数")
    private String displayCardNum;

    @ApiModelProperty("上上月POS")
    private String lastUpMonthPos;

    @ApiModelProperty("上月POS")
    private String lastMonthPos;

    @ApiModelProperty("当月pos")
    private String monthPos;

    @ApiModelProperty("人员类型")
    private String staffType;

    @ApiModelProperty("出勤天数")
    private String attendanceDay;

    @ApiModelProperty("底薪")
    private String basicSalary;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("产品和品项范围-后台使用")
    private String productAndItemList;

    @ApiModelProperty("核算产品范围-前端使用")
    private String productCodeList;

    @ApiModelProperty("核算品项范围-前端使用")
    private String itemCodeList;

    @ApiModelProperty("核算产品小类范围-前端使用")
    private String levelCodeList;

    @ApiModelProperty("费用产品范围-前端使用")
    private String feeProductCodeList;

    @ApiModelProperty("费用品项范围-前端使用")
    private String feeItemCodeList;

    @ApiModelProperty("费用产品小类范围-前端使用")
    private String feeLevelCodeList;

    @ApiModelProperty("费用归属品项-前端使用")
    private String feeBelongItemCodeList;

    @ApiModelProperty("多部门承担标记")
    private String muchDepartmentMark;


}
