package com.biz.crm.tpm.business.activities.regioncollect.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.tpm.business.activities.marketingplan.entity.CustomerGainsAndLosses;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_region_collect_gains_and_losses_back")
@Table(
        name = "tpm_region_collect_gains_and_losses_back",
        indexes = {
                @Index(name = "tpm_region_collect_gains_and_losses_back_index1", columnList = "collect_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_region_collect_gains_and_losses_back", comment = "大区汇总-客户损益预测备份数据")
@ApiModel(value = "RegionCollectGainsAndLossesBack", description = "大区汇总-客户损益预测备份数据")
public class RegionCollectGainsAndLossesBack extends CustomerGainsAndLosses {

    @ApiModelProperty("汇总编码")
    @Column(name = "collect_code", columnDefinition = "varchar(32) comment '汇总编码'")
    private String collectCode;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户类型'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    @Column(name = "erp_code", columnDefinition = "varchar(32) comment '客户ERP编码'")
    private String erpCode;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(32) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    @Column(name = "product_group_code", columnDefinition = "varchar(32) comment '产品组编码'")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    @Column(name = "channel_code", columnDefinition = "varchar(32) comment '渠道编码'")
    private String channelCode;

}
