package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/5 16:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_plan_change_log")
@Table(
        name = "tpm_marketing_plan_change_log",
        indexes = {
                @Index(name = "tpm_marketing_plan_change_log_index0", columnList = "original_scheme_code,scheme_code", unique = true)
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_plan_change_log", comment = "营销方案规划变更日志表")
@ApiModel(value = "MarketingPlanChangeLog", description = "营销方案规划变更日志表")
public class MarketingPlanChangeLog extends UuidFlagOpEntity {

    @Column(name = "original_scheme_code", columnDefinition = "varchar(32) comment '原始方案编码'")
    private String originalSchemeCode;

    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

}
