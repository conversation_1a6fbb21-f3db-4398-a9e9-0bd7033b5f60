package com.biz.crm.tpm.business.activities.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 09:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_external_contract_detail")
@Table(
        name = "tpm_external_contract_detail",
        indexes = {
                @Index(name = "tpm_external_contract_detail_index0", columnList = "contract_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_external_contract_detail", comment = "外部合同明细")
@ApiModel(value = "ExternalContract", description = "外部合同明细")
public class ExternalContractDetail extends UuidFlagOpEntity {

    @ApiModelProperty("合同编码")
    @Column(name = "contract_code", columnDefinition = "varchar(32) comment '合同编码'")
    private String contractCode;

    @ApiModelProperty("唯一编码")
    @Column(name = "only_key", columnDefinition = "varchar(64) comment '唯一编码'")
    private String onlyKey;

    @ApiModelProperty("合同明细编码")
    @Column(name = "contract_detail_code", columnDefinition = "varchar(32) comment '合同明细编码'")
    private String contractDetailCode;

    @ApiModelProperty("合同明细类型")
    @Column(name = "case_type", columnDefinition = "varchar(20) comment '合同明细类型'")
    private String caseType;

    @ApiModelProperty("活动细类编码")
    @Column(name = "detail_code", columnDefinition = "varchar(32) comment '活动细类编码'")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    @Column(name = "detail_name", columnDefinition = "varchar(200) comment '活动细类名称'")
    private String detailName;

    @ApiModelProperty("归属部门编码")
    @Column(name = "belong_department_code", columnDefinition = "varchar(32) comment '归属部门编码'")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    @Column(name = "belong_department_name", columnDefinition = "varchar(64) comment '归属部门名称'")
    private String belongDepartmentName;

    @ApiModelProperty("成本中心编码")
    @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @Column(name = "cost_center_name", columnDefinition = "varchar(128) comment '成本中心名称'")
    private String costCenterName;

    @Column(name = "estimated_cost", columnDefinition = "decimal(18,4) comment '预估费用'")
    private BigDecimal estimatedCost;

    @Column(name = "cash_type", columnDefinition = "varchar(20) comment '兑付方式'")
    private String cashType;

    @ApiModelProperty("返利类型")
    @Column(name = "rebate_type", columnDefinition = "varchar(32) comment '返利类型'")
    private String rebateType;

    @ApiModelProperty("返利计算日期")
    @Column(name = "rebate_cal_day", columnDefinition = "int(5) comment '返利计算日期'")
    private Integer rebateCalDay;

    @ApiModelProperty("条件公式")
    @Column(name = "condition_formula", columnDefinition = "varchar(500) comment '条件公式'")
    private String conditionFormula;

    @ApiModelProperty("政策形式名称")
    @Column(name = "condition_formula_name", columnDefinition = "varchar(128) comment '政策形式名称'")
    private String conditionFormulaName;

    @ApiModelProperty("结果公式")
    @Column(name = "result_formula", columnDefinition = "varchar(500) comment '结果公式'")
    private String resultFormula;

    @ApiModelProperty("开始时间")
    @Column(name = "start_date", columnDefinition = "varchar(32) comment '开始时间'")
    private String startDate;

    @ApiModelProperty("结束时间")
    @Column(name = "end_date", columnDefinition = "varchar(32) comment '结束时间'")
    private String endDate;

    @ApiModelProperty("返利标准")
    @Column(name = "rebate_standard", columnDefinition = "varchar(32) comment '返利标准'")
    private String rebateStandard;

    @ApiModelProperty("条件数量")
    @Column(name = "condition_num", columnDefinition = "varchar(32) comment '条件数量'")
    private String conditionNum;

    @ApiModelProperty("搭赠数量")
    @Column(name = "give_num", columnDefinition = "varchar(32) comment '搭赠数量'")
    private String giveNum;

    @ApiModelProperty("优惠数量-上限")
    @Column(name = "discount_quantity", columnDefinition = "decimal(10) comment '客户优惠数量-上限'")
    private BigDecimal discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    @Column(name = "discount_amount", columnDefinition = "decimal(18,4) comment '客户优惠金额-上限'")
    private BigDecimal discountAmount;
}
