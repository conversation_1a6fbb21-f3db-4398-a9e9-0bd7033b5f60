package com.biz.crm.tpm.business.activities.scheme.vo;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Vo：方案范围;
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "SchemeRange",description = "方案范围")
@Getter
@Setter
public class SchemeRangeVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
  private String tenantCode;
  /** 方案编号 */
  @ApiModelProperty(name = "schemeCode",notes = "方案编号", value= "方案编号")
  private String schemeCode;
  /** 范围类型(1,组织,2,组织类型) */
  @ApiModelProperty(name = "rangeType",notes = "范围类型(1,组织,2,组织类型)", value= "范围类型(1,组织,2,组织类型)")
  private Integer rangeType;
  /** 范围编号 */
  @ApiModelProperty(name = "rangeCode",notes = "范围编号", value= "范围编号")
  private String rangeCode;
  /** 范围名称 */
  @ApiModelProperty(name = "rangeName",notes = "范围名称", value= "范围名称")
  private String rangeName;
  /** 状态 */
  @ApiModelProperty(name = "enableStatus",notes = "状态", value= "状态")
  private String enableStatus;
}