package com.biz.crm.tpm.business.activities.overallplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase;
import com.biz.crm.tpm.business.activities.overallplan.vo.BearOverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.HeadOverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface OverallPlanCaseMapper extends BaseMapper<OverallPlanCase> {

    Page<OverallPlanCaseVo> findHeadOverallPlanCaseList(Page<HeadOverallPlanCaseVo> page, @Param("vo") HeadOverallPlanCaseVo vo);


    Page<OverallPlanCaseVo> findHeadOverallPlanCaseToMarketing(Page<HeadOverallPlanCaseVo> page, @Param("vo") HeadOverallPlanCaseVo vo);

    /**
     * 查询方案明细
     *
     * @param schemeDetailCodes
     * @return
     */
    List<OverallPlanCase> findCaseListBySchemeDetailCodes(@Param("schemeDetailCodes") List<String> schemeDetailCodes, @Param("tenantCode") String tenantCode,
                                                          @Param("processStatus") String processStatus);

    /**
     * 查询关联的总部方案明细(大区使用)
     *
     * @param schemeDetailCodes
     * @return
     */
    List<BearOverallPlanCaseVo> findReleaseHeadCaseList(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    /**
     * 营销规划方案查询关联的总部方案明细
     *
     * @param schemeDetailCodes
     * @return
     */
    List<BearOverallPlanCaseVo> findReleaseCaseByMarketingPlan(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    /**
     * 查询已经承接的方案明细
     *
     * @param schemeDetailCodes
     * @return
     */
    List<OverallPlanCase> findAlreadyBearCase(@Param("schemeDetailCodes") List<String> schemeDetailCodes);

    Page<OverallPlanCaseVo> findListBySchemeType(Page<OverallPlanCaseVo> page, @Param("vo") OverallPlanCaseVo vo,
                                                 @Param("schemeType") String schemeType, @Param("tenantCode") String tenantCode);

    List<OverallPlanCaseVo> findHeadSchemeListBySchemeType(@Param("vo") OverallPlanCaseVo vo, @Param("schemeType") String schemeType,
                                                           @Param("tenantCode") String tenantCode);

    Page<OverallPlanCaseVo> findNotCaseBySchemeTypeList(Page<OverallPlanCaseVo> page, @Param("vo") OverallPlanCaseVo vo,
                                                        @Param("schemeType") String schemeType, @Param("tenantCode") String tenantCode);

    Set<String> findAlreadyUnderTakeCaseList(@Param("schemeType") String schemeType);

    Page<OverallPlanCaseVo> findRegionOverallPlanCase(Page<OverallPlanCaseVo> page, @Param("headSchemeDetailCode") String headSchemeDetailCode,
                                                      @Param("tenantCode") String tenantCode);

    Page<OverallPlanCaseVo> findBearPlanCaseList(Page<OverallPlanCaseVo> page, @Param("vo") OverallPlanCaseVo vo,
                                                 @Param("orgCodes") Set<String> orgCodes, @Param("tenantCode") String tenantCode);

    List<OverallPlanCaseVo> checkRegionOverall(@Param("orgCodeSet") Set<String> orgCodeSet, @Param("schemeType") String schemeType,
                                               @Param("tenantCode") String tenantCode, @Param("years") String years, @Param("detailFlag") String detailFlag);

    List<OverallPlanCaseVo> findAlreadyUndertakeAmount(@Param("headSchemeDetailCodes") List<String> headSchemeDetailCodes);

    List<OverallPlanCase> findHeadPlanCaseListByOrgCodes(@Param("orgCodes") List<String> orgCodes, @Param("years") String years,
                                                         @Param("bearFlag") String bearFlag, @Param("tenantCode") String tenantCode);

    List<OverallPlanCase> findUnderTakeCaseList(@Param("headSchemeDetailCodes") List<String> headSchemeDetailCodes,
                                                @Param("excludeSchemeCode") String excludeSchemeCode, @Param("tenantCode") String tenantCode);

    List<OverallPlanCase> findListBySchemeCodeAndSchemeDetailCodePair(@Param("list") List<OverallPlanCase> caseQueryList);

    List<MarketingPlanCaseVo> findRegionUnderTakeHeadScheme(@Param("releaseDetailCodes") List<String> releaseDetailCodes,@Param("notCodes")List<String> notCodes);


    Page<OverallPlanCaseVo> findUnderTakeSchemeDetailList(Page<OverallPlanCaseVo> page,@Param("regionUnderTakeList") List<String> regionUnderTakeList,
                                                          @Param("marketingUnderTakeList") List<String> marketingUnderTakeList);

    List<OverallPlanCase> findCaseListByReleaseDetailCodes(@Param("releaseDetailCodes")  List<String> releaseDetailCode, @Param("tenantCode") String tenantCode,@Param("processStatus")  String processStatus);
}
