package com.biz.crm.tpm.business.activities.regioncollect.service.strategy;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/24 19:34
 */
public abstract class DepartmentEstimationBuilder<SchemeCodes, Years> {

    protected SchemeCodes schemeCode;

    protected Years years;

    public DepartmentEstimationBuilder<SchemeCodes, Years> init(SchemeCodes schemeCode, Years years) {
        this.schemeCode = schemeCode;
        this.years = years;
        return this;
    }

    /**
     * 加载初始值
     *
     * @return
     */
    public abstract DepartmentEstimationBuilder loadInitData();

    /**
     * 利润率
     *
     * @return
     */
    public abstract DepartmentEstimationBuilder calProfitRatio();

    /**
     * 毛利率
     *
     * @return
     */
    public abstract DepartmentEstimationBuilder calGrossProfitRatio();

    /**
     * 物流费率
     *
     * @return
     */
    public abstract DepartmentEstimationBuilder calLogisticsRatio();

    /**
     * 营销费率
     *
     * @return
     */
    public abstract DepartmentEstimationBuilder calMarketingRatio();

    /**
     * 公摊费率
     *
     * @return
     */
    public abstract DepartmentEstimationBuilder calPublicShareRatio();

    /**
     * 费用转移
     *
     * @return
     */
    public DepartmentEstimationBuilder calCostShift() {
        return this;
    }

    /**
     * 考核利润
     *
     * @return
     */
    public DepartmentEstimationBuilder calAssessProfits() {
        return this;
    }


    /**
     * 二级费用大类
     *
     * @return
     */
    public abstract DepartmentEstimationBuilder calSecondCategory();
}
