package com.biz.crm.tpm.business.activities.marketingplan.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.tpm.business.activities.marketingplan.dto.MarketingPlanSecondCategoryDto;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.TerminalExpenseVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/22 14:37
 */
@RestController
@RequestMapping("/v1/marketingPlanController")
@Slf4j
public class MarketingPlanVoController {


    @Autowired
    private MarketingPlanService marketingPlanService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private MarketingPlanCaseService marketingPlanCaseService;

    @GetMapping("findMarketingPlanCaseExecuteList")
    @ApiOperation(value = "执行活动分页查询接口")
    public Result<Page<MarketingPlanCaseExecuteVo>> findMarketingPlanCaseExecuteList(@PageableDefault(50) Pageable pageable, MarketingPlanCaseQueryDto vo) {
        return Result.ok(marketingPlanCaseService.findMarketingPlanCaseExecuteList(pageable, vo));
    }

    @GetMapping("findSafActPolicy")
    @ApiOperation(value = "SFA小程序活动政策所需分页接口")
    public Result<Page<MarketingPlanCaseVo>> findSafActPolicy(@PageableDefault(50) Pageable pageable, MarketingPlanCaseQueryDto vo) {
        return Result.ok(marketingPlanCaseService.findSafActPolicy(pageable, vo));
    }

    @PostMapping("findByTerminalMarketingPlanCaseExecuteList")
    @ApiOperation(value = "执行活动查询接口")
    public Result<List<MarketingPlanCaseExecuteVo>> findByTerminalMarketingPlanCaseExecuteList(@RequestBody MarketingPlanCaseQueryDto vo) {
        return Result.ok(marketingPlanCaseService.findByTerminalMarketingPlanCaseExecuteList(vo));
    }

    @PostMapping("findTerminalExpense")
    @ApiOperation(value = "终端费用投入查询")
    public Result<List<TerminalExpenseVo>> findTerminalExpense(@RequestBody MarketingPlanCaseQueryDto vo) {
        return Result.ok(marketingPlanCaseService.findTerminalExpense(vo));
    }

    @PostMapping("findCustomerExpense")
    @ApiOperation(value = "经销商费用投入查询")
    public Result<List<TerminalExpenseVo>> findCustomerExpense(@RequestBody MarketingPlanCaseQueryDto vo) {
        return Result.ok(marketingPlanCaseService.findCustomerExpense(vo));
    }

    @ApiOperation(value = "查询营销方案明细列表-活动预付/结案使用")
    @GetMapping("findCaseList")
    public Result<Page<MarketingPlanCaseVo>> findCaseListByActWithholding(@PageableDefault(50) Pageable pageable, MarketingPlanCaseVo vo) {
        vo.setExcludeWholeAudit(BooleanEnum.TRUE.getCapital());
        vo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        return Result.ok(marketingPlanCaseService.findCaseListByWithholdingEndCase(pageable, vo, ProcessStatusEnum.PASS.getDictCode()));
    }


    /**
     * 创建或更新终端费用投入记录表
     *
     * @param startDate
     * @param endDate
     */
    @ApiOperation(value = "创建或更新终端费用投入记录表")
    @PostMapping("createOrUpdateTerminalCostRecordLog")
    public Result createOrUpdateTerminalCostRecordLog(@ApiParam(name = "startDate", value = "开始时间") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate, @ApiParam(name = "endDate", value = "结束时间") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate) {
        try {
            this.marketingPlanCaseService.createOrUpdateTerminalCostRecordLog(startDate, endDate);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建或更新终端费用投入记录表 定时任务
     */
    @ApiOperation(value = "创建或更新终端费用投入记录表 定时任务")
    @PostMapping("createOrUpdateTerminalCostRecordLogForJob")
    public Result createOrUpdateTerminalCostRecordLog() {
        try {
            this.marketingPlanCaseService.createOrUpdateTerminalCostRecordLog(null, null);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "查询方案明细列表-通过二级费用大类")
    @GetMapping("findPlanCaseListBySecondCategory")
    public Result<Page<MarketingPlanCaseVo>> findPlanCaseListBySecondCategory(@PageableDefault(50) Pageable pageable, MarketingPlanSecondCategoryDto dto) {
        return Result.ok(this.marketingPlanCaseService.findPlanCaseListBySecondCategory(pageable, dto));
    }


    @PostMapping("findCountTerminalByTerminalCodesAndYears")
    @ApiOperation(value = "通过终端编码+年月查询费用规划的终端数(去重)")
    public Result<List<String>> findCountTerminalByTerminalCodesAndYears(@RequestBody List<String> terminalCodeList, @RequestParam String years) {
        return Result.ok(marketingPlanCaseService.findCountTerminalByTerminalCodesAndYears(terminalCodeList, years));
    }


    @PostMapping("findAmountByOrgCodesAndYearsList")
    @ApiOperation(value = "通过年月+组织查询")
    public Result<Map<String, BigDecimal>> findAmountByOrgCodesAndYearsList(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(marketingPlanCaseService.findAmountByOrgCodesAndYearsList(dto));
    }


    @ApiOperation(value = "查询方案明细列表-未读")
    @GetMapping("findCaseListByDmsMiniHomePage")
    public Result<List<MarketingPlanCaseVo>> findCaseListByDmsMiniHomePage(@RequestParam String customerCode) {
        return Result.ok(marketingPlanCaseService.findCaseListByDmsMiniHomePage(customerCode));
    }


    @GetMapping("dmsQueryCaseDetailsBySchemeDetailCode")
    @ApiOperation(value = "查看详情-dms小程序使用")
    public Result<MarketingPlanCaseVo> dmsQueryCaseDetailsBySchemeDetailCode(@RequestParam String schemeDetailCode) {
        return Result.ok(marketingPlanCaseService.dmsQueryCaseDetailsBySchemeDetailCode(schemeDetailCode));
    }


    @ApiOperation(value = "查询二级费用大类对应方案明细-dms使用")
    @GetMapping("dmsQueryCaseListByCondition")
    public Result<Page<MarketingPlanCaseVo>> dmsQueryCaseListByCondition(@PageableDefault(50) Pageable pageable, MarketingPlanSecondCategoryDto dto) {
        return Result.ok(marketingPlanCaseService.dmsQueryCaseListByCondition(pageable, dto));
    }


    @ApiOperation(value = "二级费用大类对应方案明细未读-dms使用")
    @GetMapping("dmsQueryCaseNotRead")
    public Result<Map<String, Long>> dmsQueryCaseNotRead(MarketingPlanSecondCategoryDto dto) {
        return Result.ok(marketingPlanCaseService.dmsQueryCaseNotRead(dto));
    }


    @ApiOperation(value = "查询合同费用类列表")
    @GetMapping("findContractByYearsAndLogin")
    public Result<Page<MarketingPlanCaseVo>> findContractByYearsAndLogin(@PageableDefault Pageable pageable, MarketingPlanVo vo) {
        return Result.ok(marketingPlanCaseService.findContractByYearsAndLogin(pageable, vo));
    }
}
