package com.biz.crm.tpm.business.activities.marketingplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingSalesPlan;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/12 09:38
 */
public interface MarketingSalesPlanMapper extends BaseMapper<MarketingSalesPlan> {

    Page<MarketingSalesPlanVo> findCollectList(Page<MarketingSalesPlanVo> page, @Param("vo") MarketingSalesPlanVo vo, @Param("tenantCode") String tenantCode);

    List<MarketingSalesPlanVo> findListBySalesPlanQueryVo(@Param("vo") SalesPlanQueryVo vo, @Param("tenantCode") String tenantCode);

    List<MarketingSalesPlanVo> findListByCondition(@Param("yearsSet") Set<String> yearsSet, @Param("costCenterCodes") Set<String> costCenterCodes,
                                                   @Param("customerCodes") Set<String> customerCodes, @Param("itemCodes") Set<String> itemCodes,
                                                   @Param("productCodes") Set<String> productCodes, @Param("excludeSchemeCodes") List<String> excludeSchemeCodes,
                                                   @Param("includeSchemeCodes") List<String> includeSchemeCodes, @Param("changeFlag") String changeFlag,
                                                   @Param("confirmStatus") String confirmStatus, @Param("tenantCode") String tenantCode);

    List<MarketingSalesPlanVo> findByCollectCode(String collectCode);
}
