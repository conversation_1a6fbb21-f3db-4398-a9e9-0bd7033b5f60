package com.biz.crm.tpm.business.activities.marketingplan.consumer;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.rocketmq.service.AbstractRocketMqConsumer;
import com.biz.crm.business.common.rocketmq.vo.MqMessageVo;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.workflow.sdk.dto.OaCallbackDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @describe: mq消费示例
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2022.10.14 10:09
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MqConstant.TOPIC_OA_CALLBACK_ORDER + "${rocketmq.environment}",
        /**
         * tag
         * 可用 || 监听多个tag： "tag1 || tag2 || tag3"
         * 请把tag  定义在 *** 内 需要统一维护
        */
        selectorExpression = MqConstant.TAG_OA_CALLBACK + MqConstant.TAG_TPM_MARKET_PLAN_CHANGE,
        /**
         * 相同分组下 consumer 可自动负载均衡
         * 请把consumerGroup  定义在 *** 内 需要统一维护
        */
        consumerGroup = MqConstant.TAG_TPM_MARKET_PLAN_CHANGE_GROUP + "${rocketmq.environment}",
        /**
         * 默认集群消费
         * 可以设置 ConsumeMode.ORDERLY 使用广播消费
         * 也可使用集群模式模拟广播模式：
         * 启动多个不同 consumerGroup 的consumer实例
        */
        consumeMode = ConsumeMode.ORDERLY,
        /**
         * 集群消费or广播消费;默认是集群消费
        */
        messageModel = MessageModel.CLUSTERING)
public class MarketPlanChangeOAConsumer extends AbstractRocketMqConsumer {

    @Autowired(required = false)
    private MarketingPlanService marketingPlanService;

    @Override
    protected Object handleMessage(MqMessageVo message) {
        log.info("order mq message received  : {}", message);
        String msgBody = message.getMsgBody();
        OaCallbackDto dto = JSONObject.parseObject(msgBody, OaCallbackDto.class);

        MarketingPlanVo vo = new MarketingPlanVo();
        vo.setBusinessCode(dto.getBusinessCode());
        vo.setProcessStatus(dto.getProcessStatus());
        marketingPlanService.callback(vo);
        return "顺序消息消费成功.";
    }
}