package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo;

public interface RegionCollectOaService {

    /**
     * 推送OA
     *
     * @param
     * @return
     */
    String pushOa(RegionCollectVo collectVo);

    /**
     * 重新提交OA
     *
     * @param
     * @return
     */
    JSONObject resubmitOa(RegionCollectVo collectVo);

    /**
     * OA撤回
     *
     * @param code
     * @return
     */
    boolean oaWithdraw(String code, String remark);
}
