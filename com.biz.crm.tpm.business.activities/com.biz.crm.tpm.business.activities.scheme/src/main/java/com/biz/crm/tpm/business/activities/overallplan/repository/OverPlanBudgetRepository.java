package com.biz.crm.tpm.business.activities.overallplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanBudget;
import com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanBudgetMapper;
import com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/8 16:17
 */
@Component
public class OverPlanBudgetRepository extends ServiceImpl<OverallPlanBudgetMapper, OverallPlanBudget> {


    public List<OverallPlanBudget> findOverallBudgetByCondition(List<String> orgCodes, List<String> budgetSubjectCodes, String years, List<String> customerCodes,
                                                                List<String> itemCodes, List<String> productCodes) {
        return this.lambdaQuery()
                .in(OverallPlanBudget::getDepartmentCode, orgCodes)
                .in(OverallPlanBudget::getSubjectCode, budgetSubjectCodes)
                .eq(OverallPlanBudget::getYears, years)
                .in(CollectionUtils.isNotEmpty(customerCodes), OverallPlanBudget::getCustomerCode, customerCodes)
                .in(CollectionUtils.isNotEmpty(itemCodes), OverallPlanBudget::getItemCode, itemCodes)
                .in(ObjectUtils.isNotEmpty(productCodes), OverallPlanBudget::getProductCode, productCodes)
                .list();
    }

    public List<OverallPlanBudget> findBudgetListBySchemeCodes(List<String> schemeCodes) {
        return this.lambdaQuery()
                .in(OverallPlanBudget::getSchemeCode, schemeCodes)
                .list();
    }

    public void saveBatchList(String schemeCode, List<OverallPlanBudget> budgets) {
        if (CollectionUtils.isEmpty(budgets)) {
            return;
        }
        for (OverallPlanBudget budget : budgets) {
            budget.setId(null);
            budget.setSchemeCode(schemeCode);
        }
        this.saveBatch(budgets);
    }

    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.lambdaUpdate()
                .in(OverallPlanBudget::getSchemeCode, schemeCodes).remove();
    }
}
