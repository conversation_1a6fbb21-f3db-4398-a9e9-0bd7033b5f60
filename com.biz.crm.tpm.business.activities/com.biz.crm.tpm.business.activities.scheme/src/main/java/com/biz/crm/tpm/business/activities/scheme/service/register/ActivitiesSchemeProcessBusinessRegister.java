package com.biz.crm.tpm.business.activities.scheme.service.register;

import com.biz.crm.tpm.business.activities.scheme.constant.ActivitiesSchemeConstant;
import com.biz.crm.workflow.sdk.register.ProcessBusinessRegister;
import org.springframework.stereotype.Component;

/**
 * 方案活动工作流注册业务编码
 *
 * <AUTHOR>
 * @date 2022/9/5
 */
@Component
public class ActivitiesSchemeProcessBusinessRegister implements ProcessBusinessRegister {
  @Override
  public String getBusinessCode() {
    return ActivitiesSchemeConstant.PROCESS_NAME;
  }

  @Override
  public String getBusinessName() {
    return "方案活动";
  }
}
