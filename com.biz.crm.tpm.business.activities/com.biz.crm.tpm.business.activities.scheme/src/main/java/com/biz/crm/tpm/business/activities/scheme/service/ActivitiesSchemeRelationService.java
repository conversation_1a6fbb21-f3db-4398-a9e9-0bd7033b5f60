package com.biz.crm.tpm.business.activities.scheme.service;

import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeRelation;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeRelationVo;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 描述：</br>
 *
 * <AUTHOR>
 * @date 2022/6/10
 */
public interface ActivitiesSchemeRelationService {

  /**
   * 保存关联关系数据
   * @param activitiesSchemeRelationVo
   * @return
   */
  ActivitiesSchemeRelation save(ActivitiesSchemeRelationVo activitiesSchemeRelationVo);

  /**
   * 批量保存关联关系数据
   */
  void saveBatch(List<ActivitiesSchemeRelationVo> activitiesSchemeRelationVos);

  /**
   * 根据活动编号获取对应的关联表信息
   *
   * @param activityCode
   * @return
   */
  List<ActivitiesSchemeRelationVo> findByActivityCode(String activityCode);

  /**
   * 根据活动编号集合获取对应的关联表信息
   */
  List<ActivitiesSchemeRelationVo> findByActivityCodes(Set<String> activityCodes);

  /**
   * 根据id删除数据
   *
   * @param ids
   */
  void removeByIds(Collection<String> ids);

}
