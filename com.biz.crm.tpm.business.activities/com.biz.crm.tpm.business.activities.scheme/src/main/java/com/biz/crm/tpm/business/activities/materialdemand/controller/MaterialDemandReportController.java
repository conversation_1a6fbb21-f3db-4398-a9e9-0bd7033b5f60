package com.biz.crm.tpm.business.activities.materialdemand.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandQueryVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/3 14:58
 */
@RestController
@RequestMapping("/v1/materialDemandReportController")
@Api(tags = "物资需求跟踪报表")
public class MaterialDemandReportController {

    @Autowired
    private MaterialDemandDetailService materialDemandDetailService;

    @ApiOperation(value = "分页列表")
    @GetMapping("findList")
    public Result<Page<MaterialDemandReportVo>> findList(@PageableDefault(50) Pageable pageable, MaterialDemandReportVo vo) {
        return Result.ok(materialDemandDetailService.findMaterialDemandReportList(pageable, vo));
    }

    @ApiOperation(value = "查询明细")
    @GetMapping("queryDetailList")
    public Result<Page<MarketingPlanCaseVo>> queryDetailList(@PageableDefault(50)Pageable pageable, MaterialDemandQueryVo vo){
        return Result.ok(materialDemandDetailService.queryDetailList(pageable,vo));
    }

}
