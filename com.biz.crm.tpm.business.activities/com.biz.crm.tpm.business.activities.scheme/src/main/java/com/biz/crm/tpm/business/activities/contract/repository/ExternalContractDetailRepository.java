package com.biz.crm.tpm.business.activities.contract.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.contract.entity.ExternalContractDetail;
import com.biz.crm.tpm.business.activities.contract.mapper.ExternalContractDetailMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 11:39
 */
@Component
public class ExternalContractDetailRepository extends ServiceImpl<ExternalContractDetailMapper, ExternalContractDetail> {

    public List<ExternalContractDetail> findDetailListByContractCode(String contractCode) {
        return this.lambdaQuery()
                .eq(ExternalContractDetail::getContractCode, contractCode)
                .list();
    }

    public List<ExternalContractDetail> findDetailListByContractCodes(Set<String> contractCodes) {
        return this.lambdaQuery()
                .in(ExternalContractDetail::getContractCode, contractCodes).list();
    }

    public void deleteByContractCode(String contractCode) {
        this.remove(Wrappers.lambdaQuery(ExternalContractDetail.class)
                .eq(ExternalContractDetail::getContractCode, contractCode));
    }


    public void deleteByContractCodes(List<String> contractCodes) {
        if (CollectionUtils.isEmpty(contractCodes)) {
            return ;
        }
        this.lambdaUpdate()
                .in(ExternalContractDetail::getContractCode, contractCodes)
                .remove();
    }

    public List<ExternalContractDetail> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(ExternalContractDetail::getId, ids)
                .eq(ExternalContractDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }


    public void removeByOnlyKeys(List<String> onlyKeys) {
        if (CollectionUtils.isEmpty(onlyKeys)) {
            return ;
        }
        this.lambdaUpdate()
                .in(ExternalContractDetail::getOnlyKey, onlyKeys)
                .remove();
    }

    /**
     * 批量根据id启禁用
     *
     * @param enable
     * @param ids
     */
    public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
        UpdateWrapper<ExternalContractDetail> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("enable_status", enable.getCode());
        updateWrapper.in("id", ids);
        this.update(updateWrapper);
    }
}
