package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 19:40
 */
@Getter
@AllArgsConstructor
public enum MarketingPlanSchemeTypeEnum {
    plan("plan", "规划"),
    append("append", "追加"),
    change("change", "变更"),
    o_two_o("o_two_o", "o2o"),
    contract("contract", "合同"),
    additional("additional", "补录"),
    ;
    private String code;

    private String desc;
}
