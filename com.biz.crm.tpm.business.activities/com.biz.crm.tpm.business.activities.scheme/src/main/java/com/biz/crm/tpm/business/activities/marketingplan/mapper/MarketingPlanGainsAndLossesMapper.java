package com.biz.crm.tpm.business.activities.marketingplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanGainsAndLosses;
import org.apache.ibatis.annotations.Param;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:40
 */
public interface MarketingPlanGainsAndLossesMapper extends BaseMapper<MarketingPlanGainsAndLosses> {

    Page<MarketingPlanGainsAndLosses> findList(Page<MarketingPlanGainsAndLosses> page, @Param("vo") MarketingPlanGainsAndLosses vo);
}
