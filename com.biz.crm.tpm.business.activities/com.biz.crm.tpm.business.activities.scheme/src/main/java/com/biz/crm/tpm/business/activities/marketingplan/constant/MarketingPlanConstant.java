package com.biz.crm.tpm.business.activities.marketingplan.constant;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 19:09
 */
public class MarketingPlanConstant {

    public static final String MARKETING_PLAN_LOCK = "tpm:marketing_plan:lock:";

    public static final Integer MARKETING_PLAN_LOCK_TIME_20 = 20;

    public static final String MARKETING_PLAN_STAGING_REDIS_KEY = "tpm:marketing_plan:staging:id:%s";

    public static final String SCHEME_CODE_RULE = "YXFA";

    public static final String SCHEME_DETAIL_CODE_RULE = "YXMX";

    public static final String CHANGE_SCHEME_DETAIL_CODE_RULE = "BGMX";

    public static final String SCHEME_ACT_EXECUTE_CODE_RULE = "HDMX";

    public static final String SALES_PLAN_RULE_CODE = "XSJH";

    public static final String MARKETING_PLAN = "marketing_plan";

    public static final String MARKETING_PLAN_CACHE_PAGE = "tpm:marketing:plan:cache:page:";

    public static final String MARKETING_PLAN_PAGE_CACHE_LOCK = "tpm:marketing:plan:page:cache:lockKey:id:";

    public static final String MARKETING_CONTRACT_LOCK = "tpm:marketing:contract:";

    public static final String MARKETING_CHANGE_SCHEME_LOCK = "tpm:marketing:change:scheme:";

    /**
     * 方案变更需要排除方案明细类型
     */
    public static final String TPM_CHANGE_IGNORE_CASE_TYPE = "tpm_change_ignore_case_type";

    public static final String TPM_MARKETING_SCHEME_ESTIMATE_KEY = "tpm:marketing:scheme:estimate:";

    public static final String TPM_MARKETING_SCHEME_ESTIMATE = "tpm_marketing_scheme_estimate";

    public static final String TPM_MARKETING_CHANGE_SCHEME_COMPARE_KEY = "tpm:marketing:change:scheme:compare:";

    public static String getRedisKey(String id) {
        return String.format(MARKETING_PLAN_STAGING_REDIS_KEY, id);
    }

    public static String getRedisLockKey(String id) {
        return MARKETING_PLAN_LOCK + id;
    }


    public static String getRedisPageCacheLockKey(String cacheKey) {
        return MARKETING_PLAN_PAGE_CACHE_LOCK + cacheKey;
    }

    public static String getMarketingContractLockKey(String years) {
        return MARKETING_CONTRACT_LOCK + years;
    }


    public static String getTpmMarketingChangeSchemeCompareKey(String schemeCode) {
        return TPM_MARKETING_CHANGE_SCHEME_COMPARE_KEY + schemeCode;
    }


    public static String getMarketingChangeSchemeLock(String schemeCode) {
        return MARKETING_CHANGE_SCHEME_LOCK + schemeCode;
    }


    public static String getTpmMarketingSchemeEstimateKey(String schemeCode) {
        return TPM_MARKETING_SCHEME_ESTIMATE_KEY + schemeCode;
    }
}
