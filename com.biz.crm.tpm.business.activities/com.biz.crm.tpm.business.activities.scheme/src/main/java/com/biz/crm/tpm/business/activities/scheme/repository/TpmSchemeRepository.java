package com.biz.crm.tpm.business.activities.scheme.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;

import com.biz.crm.tpm.business.activities.scheme.dto.SchemeDto;
import com.biz.crm.tpm.business.activities.scheme.entity.Scheme;
import com.biz.crm.tpm.business.activities.scheme.mapper.SchemeMapper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Collections;

/**
 * 方案;(tpm_scheme)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Component
public class TpmSchemeRepository extends ServiceImpl<SchemeMapper, Scheme> {
  @Autowired
  private SchemeMapper schemeMapper;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   */
  public Page<SchemeVo> findByConditions(Pageable pageable, SchemeDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<SchemeVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return schemeMapper.findByConditions(page, dto);
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids ID集合
   * @return List<Scheme>
   */
  public List<Scheme> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(Scheme::getId, ids)
            .eq(Scheme::getTenantCode, tenantCode)
            .eq(Scheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .list();
  }

  /**
   * 根据编号与租户编号获取对象
   *
   * @param code
   * @return
   */
  public Scheme findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(Scheme::getSchemeCode, code)
            .eq(Scheme::getTenantCode, tenantCode)
            .eq(Scheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
  }

  /**
   * 根据编号集合获取详情集合
   *
   * @param codes 编号集合
   * @return List<Scheme>
   */
  public List<Scheme> findByCodes(List<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(Scheme::getSchemeCode, codes)
            .eq(Scheme::getTenantCode, tenantCode)
            .eq(Scheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .list();
  }

  public boolean removeByIds(Collection<? extends Serializable> idList) {
    String tenantCode = TenantUtils.getTenantCode();
    UpdateWrapper<Scheme> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("del_flag", DelFlagStatusEnum.DELETE.getCode());
    updateWrapper.eq("tenant_code", tenantCode);
    updateWrapper.in("id", idList);
    return this.update(updateWrapper);
  }

  /**
   * 根据费用预算编号查询绑定方案数量
   *
   * @param costBudgetCode
   * @return
   */
  public Integer countByCostBudgetCode(String costBudgetCode) {
    String tenantCode = TenantUtils.getTenantCode();
    SchemeDto dto = new SchemeDto();
    dto.setTenantCode(tenantCode);
    dto.setCostBudgetCode(costBudgetCode);
    return this.schemeMapper.countByCostBudget(dto);
  }

  public Scheme findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(Scheme::getTenantCode,tenantCode)
        .in(Scheme::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(Scheme::getTenantCode,tenantCode)
        .in(Scheme::getId,ids)
        .remove();
  }
}