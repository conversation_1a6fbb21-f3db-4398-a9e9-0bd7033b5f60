package com.biz.crm.tpm.business.activities.contract.service;

import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectStatusEnum;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectCal;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/12 19:43
 */
@Component
@Slf4j
public class ContractCostComponent {

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private ApplicationContext context;

    @Resource
    private ExternalContractService service;

    @Resource
    private LoginUserService loginUserService;


    @Async("tpmRegionCollectThread")
    public void generateContractMarketing(String years, String lockKey, FacturerUserDetails loginDetails) {
        Boolean locked = redisLockService.tryLock(lockKey, TimeUnit.HOURS, 1);
        if (!locked) {
            log.info("合同转营销方案加锁失败===>" + years);
            return;
        }
        try {
            loginUserService.refreshAuthentication(loginDetails);
            ContractCostComponent bean = context.getBean(ContractCostComponent.class);
            bean.execute(years);
        } finally {
            if (redisLockService.isLock(lockKey)) {
                this.redisLockService.unlock(lockKey);
            }
        }
    }


    public void execute(String years) {
        try {
            service.generateContractMarketing(years);
        } catch (IllegalArgumentException i) {
            log.error(years + "合同生成营销方案失败:{}", i.getMessage(), i);
        } catch (Exception e) {
            log.error(years + "合同生成营销方案失败{}", e.getMessage(), e);
        }
    }
}
