package com.biz.crm.tpm.business.activities.regioncollect.vo;

import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 16:24
 */
@Data
@ApiModel(value = "RegionCollectGainsAndLossesVo", description = "大区汇总-客户损益预测")
public class RegionCollectGainsAndLossesVo extends CustomerGainsAndLossesVo {

    @ApiModelProperty("汇总编码")
    private String collect_code;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("渠道类型")
    private List<String> channelTypeList;

    @ApiModelProperty("渠道类型")
    private String channelType;
}
