package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.constant.BusinessPageCacheConstant;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.tpm.business.activities.marketingplan.constant.PlanClosureConstant;
import com.biz.crm.tpm.business.activities.marketingplan.dto.PlanClosureLogEventDto;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosure;
import com.biz.crm.tpm.business.activities.marketingplan.event.PlanClosureLogEventListener;
import com.biz.crm.tpm.business.activities.marketingplan.repository.PlanClosureRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanClosureDetailService;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanClosureOaService;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanClosureService;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureDetailVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureVo;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanCaseService;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.activities.stagingscheme.vo.TpmStagingSchemeVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:24
 */
@Service
@Transactional
@Slf4j
public class PlanClosureServiceImpl extends BusinessPageCacheServiceImpl<PlanClosureDetailVo, PlanClosureDetailVo> implements PlanClosureService {

    @Resource
    private PlanClosureRepository repository;

    @Autowired
    private PlanClosureDetailService detailService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private RedisService redisService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired
    private ITpmStagingSchemeService tpmStagingSchemeService;

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private GenerateCodeService generateCodeService;

    @Autowired
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired
    private OverallPlanCaseService overallPlanCaseService;

    @Autowired
    private PlanClosureOaService planClosureOaService;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;


    /**
     * 分页查询
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<PlanClosureVo> findList(Pageable pageable, PlanClosureVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<PlanClosureVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        vo.setTenantCode(TenantUtils.getTenantCode());
        return repository.findList(page, vo);
    }

    /**
     * 查询详情
     *
     * @param id
     * @param closeCode
     * @return
     */
    @Override
    public PlanClosureVo queryByIdOrCode(String id, String closeCode) {
        PlanClosure planClosure = repository.queryByIdOrCode(id, closeCode);
        Validate.notNull(planClosure, "查询数据为空");
        PlanClosureVo vo = null;
        //判断没有审批通过从缓存中读取数据
        if (StringUtils.equalsAny(planClosure.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode())) {
            String redisKey = PlanClosureConstant.getRedisKey(id);
            Boolean flag = redisService.hasKey(redisKey);
            if (flag) {
                String jsonStr = (String) redisService.get(redisKey);
                vo = JSONObject.parseObject(jsonStr, PlanClosureVo.class);
            } else {
                vo = tpmStagingSchemeService.getJsonStr(new TpmStagingSchemeVo() {{
                    this.setReleaseId(id);
                    this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
                }}, PlanClosureVo.class);
            }
        } else {
            vo = nebulaToolkitService.copyObjectByWhiteList(planClosure, PlanClosureVo.class, HashSet.class, ArrayList.class);
            List<PlanClosureDetailVo> detailVos = detailService.findListByCloseCodes(Lists.newArrayList(vo.getCloseCode()));
            vo.setDetailList(detailVos);
        }
        vo.setCloseCode(planClosure.getCloseCode());

        return vo;
    }

    /**
     * 创建
     *
     * @param vo
     */
    @Override
    public void create(PlanClosureVo vo) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        List<PlanClosureDetailVo> detailVos = this.findCacheList(vo.getCacheKey());
        vo.setDetailList(detailVos);
        //参数校验
        this.validateParam(vo, Boolean.FALSE);
        PlanClosure entity = nebulaToolkitService.copyObjectByWhiteList(vo, PlanClosure.class, HashSet.class, ArrayList.class);
        entity.setCloseCode(generateCodeService.generateCode(PlanClosureConstant.CODE_RULE));
        repository.save(entity);
        vo.setId(entity.getId());
        //放入缓存
        String redisKey = PlanClosureConstant.getRedisKey(entity.getId());
        String jsonStr = JSONObject.toJSONString(vo);
        redisService.set(redisKey, jsonStr);
        //放入临时表
        tpmStagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
            this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
            this.setReleaseId(vo.getId());
            this.setJsonStr(jsonStr);
        }});
        //日志处理
        PlanClosureLogEventDto dto = new PlanClosureLogEventDto();
        dto.setNewest(vo);
        SerializableBiConsumer<PlanClosureLogEventListener, PlanClosureLogEventDto> consumer = PlanClosureLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, PlanClosureLogEventListener.class, consumer);
        //手动清理缓存
        this.clearCache(vo.getCacheKey());
    }

    /**
     * 修改
     *
     * @param vo
     */
    @Override
    public void update(PlanClosureVo vo) {
        Validate.notNull(vo.getId(), "主键ID不能为空");
        PlanClosureVo closure = this.queryByIdOrCode(vo.getId(), null);
        Validate.notNull(closure, "当前数据不存在");
        String ruleCode = closure.getCloseCode();
        Validate.isTrue(StringUtils.equalsAny(closure.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode()), "审批中/审批通过的数据不允许编辑");
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        List<PlanClosureDetailVo> detailVos = this.findCacheList(vo.getCacheKey());
        vo.setDetailList(detailVos);
        //参数校验
        this.validateParam(vo, Boolean.FALSE);
        PlanClosure entity = nebulaToolkitService.copyObjectByWhiteList(vo, PlanClosure.class, HashSet.class, ArrayList.class);
        entity.setCloseCode(ruleCode);
        repository.updateById(entity);
        vo.setId(entity.getId());
        //放入缓存
        String redisKey = PlanClosureConstant.getRedisKey(entity.getId());
        String jsonStr = JSONObject.toJSONString(vo);
        redisService.set(redisKey, jsonStr);
        //放入临时表
        tpmStagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
            this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
            this.setReleaseId(vo.getId());
            this.setJsonStr(jsonStr);
        }});
        //日志处理
        PlanClosureLogEventDto dto = new PlanClosureLogEventDto();
        dto.setOriginal(closure);
        dto.setNewest(vo);
        SerializableBiConsumer<PlanClosureLogEventListener, PlanClosureLogEventDto> consumer = PlanClosureLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, PlanClosureLogEventListener.class, consumer);
        //手动清理缓存
        this.clearCache(vo.getCacheKey());
    }


    /**
     * 创建提交
     *
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitCreate(PlanClosureVo vo) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        List<PlanClosureDetailVo> detailVos = this.findCacheList(vo.getCacheKey());
        vo.setDetailList(detailVos);
        //参数校验
        this.validateParam(vo, Boolean.TRUE);
        PlanClosure entity = nebulaToolkitService.copyObjectByWhiteList(vo, PlanClosure.class, HashSet.class, ArrayList.class);
        entity.setDetailVos(vo.getDetailList());
        entity.setCloseCode(generateCodeService.generateCode(PlanClosureConstant.CODE_RULE));
        repository.save(entity);
        vo.setId(entity.getId());
        //保存数据明细
        detailService.saveBatchList(vo.getDetailList(), entity.getCloseCode());

        //推送OA
        planClosureOaService.pushOa(entity);

        //日志处理
        PlanClosureLogEventDto dto = new PlanClosureLogEventDto();
        dto.setNewest(vo);
        SerializableBiConsumer<PlanClosureLogEventListener, PlanClosureLogEventDto> consumer = PlanClosureLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, PlanClosureLogEventListener.class, consumer);
        //手动清理缓存
        this.clearCache(vo.getCacheKey());
        //修改方案明细状态
        List<String> schemeDetailCodes = vo.getDetailList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getSchemeDetailCode()))
                .map(PlanClosureDetailVo::getSchemeDetailCode).collect(Collectors.toList());
        updateCaseStatusBySchemeDetailCodes(schemeDetailCodes, DelFlagStatusEnum.DELETE.getCode(), vo.getSourceType());
    }

    /**
     * 修改提交
     *
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitUpdate(PlanClosureVo vo) {
        Validate.notNull(vo.getId(), "主键ID不能为空");
        PlanClosureVo closure = this.queryByIdOrCode(vo.getId(), null);
        Validate.notNull(closure, "当前数据不存在");
        String ruleCode = closure.getCloseCode();
        Validate.isTrue(StringUtils.equalsAny(closure.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode()), "审批中/审批通过的数据不允许编辑");
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        List<PlanClosureDetailVo> detailVos = this.findCacheList(vo.getCacheKey());
        vo.setDetailList(detailVos);
        //参数校验
        this.validateParam(vo, Boolean.TRUE);
        PlanClosure entity = nebulaToolkitService.copyObjectByWhiteList(vo, PlanClosure.class, HashSet.class, ArrayList.class);
        entity.setCloseCode(ruleCode);
        entity.setDetailVos(vo.getDetailList());
        repository.updateById(entity);
        vo.setId(entity.getId());
        //保存明细数据
        detailService.saveBatchList(vo.getDetailList(), ruleCode);
        //日志处理
        PlanClosureLogEventDto dto = new PlanClosureLogEventDto();

        //推送OA
        if (ProcessStatusEnum.PREPARE.getDictCode().equals(entity.getProcessStatus())) {
            planClosureOaService.pushOa(entity);
        } else {
            planClosureOaService.resubmitOa(entity);
        }

        dto.setOriginal(closure);
        dto.setNewest(vo);
        SerializableBiConsumer<PlanClosureLogEventListener, PlanClosureLogEventDto> consumer = PlanClosureLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, PlanClosureLogEventListener.class, consumer);
        //手动清理缓存
        this.clearCache(vo.getCacheKey());

        //修改方案明细状态
        List<String> schemeDetailCodes = vo.getDetailList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getSchemeDetailCode()))
                .map(PlanClosureDetailVo::getSchemeDetailCode).collect(Collectors.toList());
        updateCaseStatusBySchemeDetailCodes(schemeDetailCodes, DelFlagStatusEnum.DELETE.getCode(), vo.getSourceType());

        //此处需要删除redis缓存和临时表数据
        String redisKey = OverallPlanConstant.getRedisKey(vo.getId());
        redisService.del(redisKey);
        tpmStagingSchemeService.deleteStaging(new TpmStagingSchemeVo() {{
            this.setReleaseIds(Lists.newArrayList(vo.getId()));
            this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
        }});
    }

    /**
     * 提交审批
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitById(String id) {
        PlanClosureVo vo = queryByIdOrCode(id, null);
        Validate.notNull(vo, "数据不存在,请刷新重试!");
        Validate.isTrue(StringUtils.equalsAny(vo.getProcessStatus(),
                ProcessStatusEnum.PREPARE.getDictCode(),
                ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode()), "审批中/审批通过[%s]数据不允许提交", vo.getCloseCode());
        String ruleCode = vo.getCloseCode();
        boolean prepare = ProcessStatusEnum.PREPARE.getDictCode().equals(vo.getProcessStatus());

        vo = tpmStagingSchemeService.getJsonStr(new TpmStagingSchemeVo() {{
            this.setReleaseId(id);
            this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
        }}, PlanClosureVo.class);

        //参数校验
        this.validateParam(vo, Boolean.TRUE);
        PlanClosure entity = nebulaToolkitService.copyObjectByWhiteList(vo, PlanClosure.class, HashSet.class, ArrayList.class);

        entity.setDetailVos(vo.getDetailList());
        entity.setCloseCode(ruleCode);
        repository.updateById(entity);
        vo.setId(entity.getId());

        //推送OA
        if (prepare) {
            planClosureOaService.pushOa(entity);
        } else {
            planClosureOaService.resubmitOa(entity);
        }

        //保存明细数据
        detailService.saveBatchList(vo.getDetailList(), ruleCode);
        List<String> schemeDetailCodes = vo.getDetailList().stream().filter(x -> {
                    if (ObjectUtils.isNotEmpty(x.getString("schemeDetailCode"))) {
                        x.setSchemeDetailCode(x.getString("schemeDetailCode"));
                        return true;
                    } else {
                        return false;
                    }
                })
                .map(PlanClosureDetailVo::getSchemeDetailCode).collect(Collectors.toList());
        //修改方案明细状态
        updateCaseStatusBySchemeDetailCodes(schemeDetailCodes, DelFlagStatusEnum.DELETE.getCode(), vo.getSourceType());
        //此处需要删除redis缓存和临时表数据
        String redisKey = OverallPlanConstant.getRedisKey(id);
        redisService.del(redisKey);
        tpmStagingSchemeService.deleteStaging(new TpmStagingSchemeVo() {{
            this.setReleaseIds(Lists.newArrayList(id));
            this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
        }});
    }


    /**
     * OA审批回调
     *
     * @param vo
     */
    @Override
    public void callback(PlanClosureVo vo) {
        PlanClosure closure = repository.queryByIdOrCode(null, vo.getCloseCode());
        if (closure == null) {
            return;
        }
        closure.setProcessStatus(vo.getProcessStatus());
        PlanClosureVo closureVo = queryByIdOrCode(closure.getId(), closure.getCloseCode());
        //修改之前查询出数据
        repository.updateById(closure);
        closureVo.setProcessStatus(vo.getProcessStatus());
        List<PlanClosureDetailVo> detailVos = detailService.findListByCloseCodes(Lists.newArrayList(closure.getCloseCode()));
        //如果不是驳回则需要进行二次变更方案明细状态
        if (!ProcessStatusEnum.PASS.getDictCode().equals(vo.getProcessStatus())) {
            List<String> schemeDetailCodes = closureVo.getDetailList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getSchemeDetailCode()))
                    .map(PlanClosureDetailVo::getSchemeDetailCode).collect(Collectors.toList());
            //修改方案明细状态
            updateCaseStatusBySchemeDetailCodes(schemeDetailCodes, DelFlagStatusEnum.NORMAL.getCode(), closureVo.getSourceType());
            //删除明细数据
            detailService.deleteByCloseCodes(Lists.newArrayList(closure.getCloseCode()));
            closureVo.setDetailList(detailVos);

            //放入缓存
            String redisKey = PlanClosureConstant.getRedisKey(closure.getId());
            String jsonStr = JSONObject.toJSONString(closureVo);
            redisService.set(redisKey, jsonStr);
            //放入临时表
            tpmStagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
                this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
                this.setReleaseId(closure.getId());
                this.setJsonStr(jsonStr);
            }});
        }

    }

    /**
     * 参数校验
     *
     * @param vo
     */
    private void validateParam(PlanClosureVo vo, Boolean submitFlag) {
        Validate.notEmpty(vo.getSourceType(), "来源类型错误");
        vo.setTenantCode(TenantUtils.getTenantCode());
        vo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        vo.setOrgCode(loginDetails.getOrgCode());
        vo.setOrgName(loginDetails.getOrgName());
        if (submitFlag) {
            Validate.notNull(vo.getCloseName(), "关闭名称不能为空");
            Validate.isTrue(CollectionUtils.isNotEmpty(vo.getDetailList()), "关闭明细列表不能为空");
            for (PlanClosureDetailVo detailVo : vo.getDetailList()) {
                Validate.isTrue(detailVo.containsKey("schemeDetailCode"), "方案明细编码不能为空");
                detailVo.setSchemeDetailCode(detailVo.getString("schemeDetailCode"));
                Validate.notNull(detailVo.getSchemeDetailCode(), "方案明细编码不能为空");
            }
        } else {
            if (StringUtils.isEmpty(vo.getProcessStatus())) {
                vo.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
            }
        }
    }


    /**
     * 修改方案明细状态
     *
     * @param schemeDetailCodes
     * @param delFlag
     * @param sourceType
     */
    private void updateCaseStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String delFlag, String sourceType) {
        if (OverallPlanSchemeTypeEnum.MARKETING.getCode().equals(sourceType)) {
            marketingPlanCaseService.updateCaseStatusBySchemeDetailCodes(schemeDetailCodes, delFlag);
        } else {
            overallPlanCaseService.updateCaseStatusBySchemeDetailCodes(schemeDetailCodes, delFlag);
        }
    }


    /**
     * 删除
     *
     * @param ids
     */
    @Override
    public void deleteBatchByIds(List<String> ids) {
        List<PlanClosure> list = repository.findListByIdList(ids);
        Long count = list.stream()
                .filter(x -> StringUtils.equalsAny(x.getProcessStatus(),
                        ProcessStatusEnum.COMMIT.getDictCode(),
                        ProcessStatusEnum.PASS.getDictCode()))
                .count();
        Validate.isTrue(count == 0L, "存在审批中/审批通过数据");
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> x.setDelFlag(DelFlagStatusEnum.DELETE.getCode()));
            repository.updateBatchById(list);
            List<String> closeCodes = list.stream().map(PlanClosure::getCloseCode).collect(Collectors.toList());
            detailService.deleteByCloseCodes(closeCodes);

            //删除OA接口
            list = list.stream().filter(x ->
                    StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.REJECT.getDictCode(),
                            ProcessStatusEnum.RECOVER.getDictCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(e -> ryOaProcessService.deleteWorkflow(e.getProcessNumber(), e.getOaId()));
            }

            //日志处理
            PlanClosureLogEventDto dto = new PlanClosureLogEventDto();
            List<PlanClosureVo> planVoList = (List<PlanClosureVo>) nebulaToolkitService.copyCollectionByWhiteList(list, PlanClosure.class,
                    PlanClosureVo.class, HashSet.class, ArrayList.class);
            dto.setNewestList(planVoList);
            SerializableBiConsumer<PlanClosureLogEventListener, PlanClosureLogEventDto> consumer = PlanClosureLogEventListener::onDelete;
            nebulaNetEventClient.publish(dto, PlanClosureLogEventListener.class, consumer);

            for (PlanClosure closure : list) {
                //此处需要删除redis缓存和临时表数据
                String redisKey = OverallPlanConstant.getRedisKey(closure.getId());
                redisService.del(redisKey);
                tpmStagingSchemeService.deleteStaging(new TpmStagingSchemeVo() {{
                    this.setReleaseIds(Lists.newArrayList(closure.getId()));
                    this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
                }});
            }
        }
    }


    /**
     * 查询营销方案规划未关闭方案明细列表
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MarketingPlanCaseVo> findNotMarketingClosedCaseList(Pageable pageable, MarketingPlanCaseQueryDto vo) {
        Set<String> schemeDetailCodeList = findPlanClosureDetailSchemeDetailCodeList(OverallPlanSchemeTypeEnum.MARKETING.getCode());
        if (CollectionUtils.isNotEmpty(schemeDetailCodeList)) {
            vo.setExcludeSchemeDetailCodes(schemeDetailCodeList);
        }
        return marketingPlanCaseService.findNotMarketingClosedCaseList(pageable, vo);
    }

    /**
     * 查询大区未关闭方案明细
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<OverallPlanCaseVo> findNotRegionCaseList(Pageable pageable, OverallPlanCaseVo vo) {
        Set<String> excludeSchemeDetailCodes = Sets.newHashSet();
        //查询已经关闭的
        Set<String> schemeDetailCodeList = findPlanClosureDetailSchemeDetailCodeList(OverallPlanSchemeTypeEnum.REGION.getCode());
        if (CollectionUtils.isNotEmpty(schemeDetailCodeList)) {
            excludeSchemeDetailCodes.addAll(schemeDetailCodeList);
        }
        Set<String> alreadySchemeDetailCodeSet = overallPlanCaseService.findAlreadyUnderTakeCaseList(OverallPlanSchemeTypeEnum.REGION.getCode());
        if (CollectionUtils.isNotEmpty(alreadySchemeDetailCodeSet)) {
            excludeSchemeDetailCodes.addAll(alreadySchemeDetailCodeSet);
        }
        vo.setExcludeSchemeDetailCodes(excludeSchemeDetailCodes);
        return overallPlanCaseService.findNotCaseBySchemeTypeList(pageable, vo, OverallPlanSchemeTypeEnum.REGION.getCode());
    }

    /**
     * 查询总部未关闭方案明细
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<OverallPlanCaseVo> findNotHeadCaseList(Pageable pageable, OverallPlanCaseVo vo) {
        Set<String> excludeSchemeDetailCodes = Sets.newHashSet();
        //查询已经关闭的
        Set<String> schemeDetailCodeList = findPlanClosureDetailSchemeDetailCodeList(OverallPlanSchemeTypeEnum.HEAD.getCode());
        if (CollectionUtils.isNotEmpty(schemeDetailCodeList)) {
            excludeSchemeDetailCodes.addAll(schemeDetailCodeList);
        }
        Set<String> alreadySchemeDetailCodeSet = overallPlanCaseService.findAlreadyUnderTakeCaseList(OverallPlanSchemeTypeEnum.HEAD.getCode());
        if (CollectionUtils.isNotEmpty(alreadySchemeDetailCodeSet)) {
            excludeSchemeDetailCodes.addAll(alreadySchemeDetailCodeSet);
        }
        //查询总部
        vo.setExcludeSchemeDetailCodes(excludeSchemeDetailCodes);
        return overallPlanCaseService.findNotCaseBySchemeTypeList(pageable, vo, OverallPlanSchemeTypeEnum.HEAD.getCode());
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public void recover(String code, String remark) {
        PlanClosure entity = repository.queryByIdOrCode(null, code);
        if (planClosureOaService.oaWithdraw(entity, remark)) {
            entity.setProcessStatus(ProcessStatusEnum.RECOVER.getDictCode());
            entity.setProcessStatus(entity.getProcessStatus());
            PlanClosureVo closureVo = queryByIdOrCode(entity.getId(), entity.getCloseCode());
            repository.saveOrUpdate(entity);
            closureVo.setProcessStatus(entity.getProcessStatus());
            List<PlanClosureDetailVo> detailVos = detailService.findListByCloseCodes(Lists.newArrayList(entity.getCloseCode()));

            List<String> schemeDetailCodes = closureVo.getDetailList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getSchemeDetailCode()))
                    .map(PlanClosureDetailVo::getSchemeDetailCode).collect(Collectors.toList());
            //修改方案明细状态
            updateCaseStatusBySchemeDetailCodes(schemeDetailCodes, DelFlagStatusEnum.NORMAL.getCode(), closureVo.getSourceType());
            //删除明细数据
            detailService.deleteByCloseCodes(Lists.newArrayList(entity.getCloseCode()));
            closureVo.setDetailList(detailVos);

            //放入缓存
            String redisKey = PlanClosureConstant.getRedisKey(entity.getId());
            String jsonStr = JSONObject.toJSONString(closureVo);
            redisService.set(redisKey, jsonStr);
            //放入临时表
            tpmStagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
                this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
                this.setReleaseId(entity.getId());
                this.setJsonStr(jsonStr);
            }});
        }
    }

    @Override
    public List<PlanClosureDetailVo> findListById(String id) {
        PlanClosure planClosure = repository.queryByIdOrCode(id, null);
        Validate.notNull(planClosure, "查询数据为空");
        PlanClosureVo vo = null;
        List<PlanClosureDetailVo> list = Lists.newArrayList();
        //判断没有审批通过从缓存中读取数据
        if (StringUtils.equalsAny(planClosure.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode())) {
            String redisKey = PlanClosureConstant.getRedisKey(id);
            Boolean flag = redisService.hasKey(redisKey);
            if (flag) {
                String jsonStr = (String) redisService.get(redisKey);
                vo = JSONObject.parseObject(jsonStr, PlanClosureVo.class);
            } else {
                vo = tpmStagingSchemeService.getJsonStr(new TpmStagingSchemeVo() {{
                    this.setReleaseId(id);
                    this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
                }}, PlanClosureVo.class);
            }
            list = vo.getDetailList();
        } else {
            list = detailService.findListByCloseCodes(Lists.newArrayList(planClosure.getCloseCode()));
        }
        return list;
    }


    /**
     * 查询待审批、驳回状态的方案关闭明细数据
     *
     * @return
     */
    @Override
    public Set<String> findPlanClosureDetailSchemeDetailCodeList(String sourceType) {
        List<PlanClosure> list = repository.findListByPrepareAndReject(sourceType);
        if (CollectionUtils.isEmpty(list)) {
            return Sets.newHashSet();
        }
        Set<String> schemeDetailCodes = Sets.newHashSet();
        for (PlanClosure closure : list) {
            PlanClosureVo vo = null;
            String redisKey = PlanClosureConstant.getRedisKey(closure.getId());
            Boolean flag = redisService.hasKey(redisKey);
            if (flag) {
                String jsonStr = (String) redisService.get(redisKey);
                vo = JSONObject.parseObject(jsonStr, PlanClosureVo.class);
            } else {
                vo = tpmStagingSchemeService.getJsonStr(new TpmStagingSchemeVo() {{
                    this.setReleaseId(closure.getId());
                    this.setFromType(PlanClosureConstant.PLAN_CLOSURE);
                }}, PlanClosureVo.class);
            }
            if (CollectionUtils.isNotEmpty(vo.getDetailList())) {
                List<String> dataList = vo.getDetailList().stream().filter(x -> {
                            if (ObjectUtils.isNotEmpty(x.getString("schemeDetailCode"))) {
                                x.setSchemeDetailCode(x.getString("schemeDetailCode"));
                                return true;
                            }
                            return false;
                        })
                        .map(PlanClosureDetailVo::getSchemeDetailCode).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dataList)) {
                    schemeDetailCodes.addAll(dataList);
                }
            }
        }
        return schemeDetailCodes;
    }

    @Override
    public List<PlanClosureDetailVo> findCacheList(String cacheKey) {
        String redisCacheIdKey = helper.getRedisCacheIdKey(cacheKey);
        Set<String> idKeys = redisTemplate.keys(redisCacheIdKey + "*");
        List<PlanClosureDetailVo> dataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(idKeys)) {
            for (String idKey : idKeys) {
                String dataKey = idKey.replace(BusinessPageCacheConstant.REDIS_KEY_ID, BusinessPageCacheConstant.REDIS_KEY_DATA);
                List<Object> idList = redisService.lRange(idKey, 0, -1);
                dataList.addAll(redisTemplate.opsForHash().multiGet(dataKey, idList));
            }
        } else {
            String redisCacheInitKey = helper.getRedisCacheInitKey(cacheKey);
            try {
                //标记为已初始化，不重复初始化
                redisService.set(redisCacheInitKey, BooleanEnum.TRUE, helper.getExpireTime());
                //redis里面没有
                List<PlanClosureDetailVo> dtoList = helper.findDtoListFromRepository(new PlanClosureDetailVo() {{
                    this.put("id", cacheKey);
                }}, cacheKey);

                if (helper.initToCacheFromRepository()) {
                    helper.putCache(cacheKey, dtoList);
                }
            } catch (Exception e) {
                redisService.del(redisCacheInitKey);
            }
        }
        return dataList;
    }


    @Override
    public void addItemCache(String cacheKey, List<PlanClosureDetailVo> itemList) {
        String redisCacheIdKey = helper.getRedisCacheIdKey(cacheKey);
        String redisCacheDataKey = helper.getRedisCacheDataKey(cacheKey);

        //过滤出来需要更新的数据
        List<Object> idList = redisService.lRange(redisCacheIdKey, 0, -1);
        List<PlanClosureDetailVo> updateList = itemList.stream().filter(item -> idList.contains(helper.getDtoKey(item))).collect(Collectors.toList());
        helper.updateItem(cacheKey, updateList);
        helper.filterSaveItem(cacheKey, idList, itemList, updateList);

        List<PlanClosureDetailVo> newItemList = helper.newItem(cacheKey, itemList);
        Object[] newIdArr = newItemList.stream().map(helper::getDtoKey).toArray();
        helper.doSaveNewKey(cacheKey, newIdArr);
        if (!org.springframework.util.CollectionUtils.isEmpty(newItemList)) {
            updateList.addAll(newItemList);
        }
        Map<Object, PlanClosureDetailVo> updateMap = Maps.newHashMap();
        for (PlanClosureDetailVo vo : updateList) {
            updateMap.put(vo.get("id"), vo);
        }
        helper.doSaveItem(cacheKey, updateMap);
    }
}
