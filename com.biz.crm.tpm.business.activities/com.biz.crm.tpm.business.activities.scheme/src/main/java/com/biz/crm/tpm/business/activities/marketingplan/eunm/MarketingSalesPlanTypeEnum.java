package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/17 14:34
 */
@Getter
@AllArgsConstructor
public enum MarketingSalesPlanTypeEnum {

    sales_plan("BDMB0019", "销售计划"),
    ;

    private String code;

    private String desc;


    public static Boolean checkCode(String code) {
        Boolean flag = Boolean.FALSE;
        for (MarketingPlanCaseTypeEnum value : MarketingPlanCaseTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                flag = Boolean.TRUE;
                break;
            }
        }
        return flag;
    }

    public static MarketingPlanCaseTypeEnum findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (MarketingPlanCaseTypeEnum item : MarketingPlanCaseTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
