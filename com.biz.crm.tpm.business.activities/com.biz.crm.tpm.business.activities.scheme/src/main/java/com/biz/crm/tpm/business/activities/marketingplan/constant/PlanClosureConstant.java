package com.biz.crm.tpm.business.activities.marketingplan.constant;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 17:01
 */
public class PlanClosureConstant {

    public static final String PLAN_CLOSURE_LOCK = "tpm:plan_closure:lock:";

    public static final Integer MARKETING_PLAN_LOCK_TIME_20 = 20;

    public static final String PLAN_CLOSURE_STAGING_REDIS_KEY = "tpm:plan_closure:staging:id:%s";

    public static final String CODE_RULE = "FAGB";

    public static final String DETAIL_CODE_RULE = "FAGB";

    public static final String PLAN_CLOSURE = "plan_closure";

    public static final String PLAN_CLOSURE_CACHE_PAGE = "tpm:plan:closure:cache:page:";

    public static final String PLAN_CLOSURE_PAGE_CACHE_LOCK = "tpm:plan:closure:page:cache:lockKey:id:";

    public static String getRedisKey(String id) {
        String redisKey = String.format(PLAN_CLOSURE_STAGING_REDIS_KEY, id);
        return redisKey;
    }

    public static String getRedisLockKey(String id) {
        String redisLockKey = PLAN_CLOSURE_LOCK + id;
        return redisLockKey;
    }


    public static String getRedisPageCacheLockKey(String cacheKey) {
        String pageCacheLockKey = PLAN_CLOSURE_PAGE_CACHE_LOCK + cacheKey;
        return pageCacheLockKey;
    }
}
