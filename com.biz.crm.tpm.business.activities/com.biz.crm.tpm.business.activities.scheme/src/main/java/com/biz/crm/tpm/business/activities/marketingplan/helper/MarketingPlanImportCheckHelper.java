package com.biz.crm.tpm.business.activities.marketingplan.helper;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseImportVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanService;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/12 15:52
 */
@Component
@Slf4j
public class MarketingPlanImportCheckHelper implements ImportProcess<MarketingPlanCaseImportVo> {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private TerminalVoService terminalVoService;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private MdmCostCenterVoService costCenterVoService;

    @Resource
    private CostTypeCategoryVoService costTypeCategoryVoService;

    @Resource
    private CostTypeDetailVoService costTypeDetailVoService;

    @Resource
    private MarketingPlanPageCacheHelper pageCacheHelper;

    @Resource
    private OverallPlanService overallPlanService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private MaterialVoService materialVoService;

    @Resource
    private MarketingPlanCaseCheckHelper caseCheckHelper;

    @Resource
    private MarketingPlanCaseRepository planCaseRepository;


    /**
     * 参数校验
     *
     * @param map
     * @param cacheKey
     */
    public void checkData(Map<String, List<MarketingPlanCaseImportVo>> map, String cacheKey, String years, String schemeCode, String originalSchemeCOde, List<String> loginUserOneLevelOrgCodes, String loginUserName) {
        List<MarketingPlanCaseImportVo> list = map.values().stream().flatMap(List::stream).collect(Collectors.toList());
        //不是承接的
        List<MarketingPlanCaseImportVo> importList = list.stream().filter(x -> ObjectUtils.isEmpty(x.getReleaseDetailCode())).collect(Collectors.toList());
        //是需要承接的方案
        Map<String, OverallPlanCaseVo> bearCaseMap = Maps.newHashMap();
        List<String> releaseDetailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getReleaseDetailCode()))
                .map(MarketingPlanCaseImportVo::getReleaseDetailCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(releaseDetailCodes)) {
            List<OverallPlanCaseVo> bearCaseList = overallPlanService.findOverallPlanCaseListBySchemeDetailCodes(releaseDetailCodes);
            bearCaseMap = bearCaseList.stream().collect(Collectors.toMap(OverallPlanCaseVo::getSchemeDetailCode, Function.identity()));
        }
        //取出承接销售部门
        List<String> bearOrgNameList = importList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBearDepartmentName()))
                .map(MarketingPlanCaseImportVo::getBearDepartmentName).collect(Collectors.toList());
        //取出费用使用部门
        List<String> belongOrgNameList = importList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBelongDepartmentName()))
                .map(MarketingPlanCaseImportVo::getBelongDepartmentName).collect(Collectors.toList());
        List<String> orgNameList = Lists.newArrayList();
        orgNameList.addAll(bearOrgNameList);
        orgNameList.addAll(belongOrgNameList);
        //取出品项
        Set<String> allItemSet = Sets.newHashSet();
        List<String> itemList = importList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getItemCodeList()))
                .map(MarketingPlanCaseImportVo::getItemCodeList)
                .flatMap(x -> Stream.of(x.split(","))).collect(Collectors.toList());
        List<String> feeItemList = importList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getFeeItemCodeList()))
                .map(MarketingPlanCaseImportVo::getFeeItemCodeList)
                .flatMap(x -> Stream.of(x.split(","))).collect(Collectors.toList());
        List<String> feeBelongItemList = importList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getFeeBelongItemCodeList()))
                .map(MarketingPlanCaseImportVo::getFeeBelongItemCodeList)
                .flatMap(x -> Stream.of(x.split(","))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemList)) {
            allItemSet.addAll(itemList);
        }
        if (CollectionUtils.isNotEmpty(feeItemList)) {
            allItemSet.addAll(feeItemList);
        }
        if (CollectionUtils.isNotEmpty(feeBelongItemList)) {
            allItemSet.addAll(feeBelongItemList);
        }
        //取出产品
        Set<String> allProductCodes = Sets.newHashSet();
        List<String> productCodes = importList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductCodeList()))
                .map(MarketingPlanCaseImportVo::getProductCodeList)
                .flatMap(x -> Stream.of(x.split(",")))
                .collect(Collectors.toList());
        List<String> feeProductCodes = importList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getFeeProductCodeList()))
                .map(MarketingPlanCaseImportVo::getFeeProductCodeList)
                .flatMap(x -> Stream.of(x.split(",")))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productCodes)) {
            allProductCodes.addAll(productCodes);
        }
        if (CollectionUtils.isNotEmpty(feeProductCodes)) {
            allProductCodes.addAll(feeProductCodes);
        }
        //取出小类
        Set<String> allLevelSet = Sets.newHashSet();
        List<String> levelList = importList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getLevelCodeList()))
                .map(MarketingPlanCaseImportVo::getLevelCodeList)
                .flatMap(x -> Stream.of(x.split(","))).collect(Collectors.toList());
        List<String> feeLevelList = importList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getFeeLevelCodeList()))
                .map(MarketingPlanCaseImportVo::getFeeLevelCodeList)
                .flatMap(x -> Stream.of(x.split(","))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(levelList)) {
            allLevelSet.addAll(levelList);
        }
        if (CollectionUtils.isNotEmpty(feeLevelList)) {
            allLevelSet.addAll(feeLevelList);
        }

        Map<String, List<MarketingPlanCaseVo>> caseListMap = Maps.newHashMap();
        for (Map.Entry<String, List<MarketingPlanCaseImportVo>> entry : map.entrySet()) {
            List<MarketingPlanCaseImportVo> caseImportList = entry.getValue();
            //参数转换
            List<MarketingPlanCaseVo> caseList = this.transObject(caseImportList, entry.getKey(), bearCaseMap);
            caseList.forEach(x -> x.setYears(years));

            List<String> erpCodeList = caseImportList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getErpCode()))
                    .map(MarketingPlanCaseImportVo::getErpCode).distinct().collect(Collectors.toList());
            List<CustomerVo> customerList = customerVoService.findByErpCodes(erpCodeList);
            Map<String, String> customerVoMap = customerList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()) && ObjectUtils.isNotEmpty(x.getErpCode()) && ObjectUtils.isNotEmpty(x.getCompanyCode()))
                    .collect(Collectors.toMap(x -> x.getErpCode() + x.getCompanyCode(), x -> x.getCustomerCode()));
            for (MarketingPlanCaseVo caseVo : caseList) {
                String key = caseVo.getErpCode() + caseVo.getCompanyCode();
                if (customerVoMap.containsKey(key)) {
                    caseVo.setCustomerCode(customerVoMap.get(key));
                } else {
                    caseVo.setCustomerCode(key);
                }
            }
            caseListMap.put(entry.getKey(), caseList);
        }
        pageCacheHelper.importNewItem(cacheKey, caseListMap, years, schemeCode, originalSchemeCOde, loginUserOneLevelOrgCodes, loginUserName);

    }


    /**
     * 转换参数
     *
     * @param caseImportList 导入vo
     * @param caseType       方案类型
     * @param bearCaseMap    承接的明细数据
     * @return
     */
    private List<MarketingPlanCaseVo> transObject(List<MarketingPlanCaseImportVo> caseImportList, String caseType, Map<String, OverallPlanCaseVo> bearCaseMap) {
        List<MarketingPlanCaseVo> list = Lists.newArrayList();
        for (MarketingPlanCaseImportVo importVo : caseImportList) {
            StringJoiner errMsg = new StringJoiner(";");
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(importVo));
            transCheckJsonObject("applyAmount", jsonObject);
            transCheckJsonObject("estimatedSalesVolume", jsonObject);
            transCheckJsonObject("ratio", jsonObject);
            transCheckJsonObject("bearAmount", jsonObject);
            transCheckJsonObject("cusBearAmount", jsonObject);
            transCheckJsonObject("materialNum", jsonObject);
            transCheckJsonObject("materialCostPrice", jsonObject);
            transCheckJsonObject("discountQuantity", jsonObject);
            transCheckJsonObject("discountAmount", jsonObject);

            transCheckJsonObject("displayCardNum", jsonObject);
            transCheckJsonObject("lastUpMonthPos", jsonObject);
            transCheckJsonObject("lastMonthPos", jsonObject);
            transCheckJsonObject("monthPos", jsonObject);
            transCheckJsonObject("attendanceDay", jsonObject);
            transCheckJsonObject("basicSalary", jsonObject);

            MarketingPlanCaseVo caseVo = JSONObject.toJavaObject(jsonObject, MarketingPlanCaseVo.class);
            caseVo.setCaseType(caseType);
            //承接方案转换
            this.transBearCase(caseVo, importVo, bearCaseMap, errMsg);
            //转换品项、产品、小类、数据字典
            this.transProductAndDictData(caseVo, importVo);
            list.add(caseVo);
        }
        return list;
    }


    private void transCheckJsonObject(String filed, JSONObject jsonObject) {
        if (jsonObject.containsKey(filed)) {
            try {
                BigDecimal value = new BigDecimal(jsonObject.getString(filed));
                jsonObject.put(filed, value);
            } catch (Exception e) {
                log.error("转换错误:{}", jsonObject.get(filed));
                log.error(e.getMessage(), e);
                jsonObject.remove(filed);
            }
        }
    }


    /**
     * 基础参数校验
     *
     * @param vo
     * @param importVo
     */
    private void transBearCase(MarketingPlanCaseVo vo, MarketingPlanCaseImportVo importVo, Map<String, OverallPlanCaseVo> bearCaseMap, StringJoiner errMsg) {
        boolean flag = ObjectUtils.isNotEmpty(importVo.getReleaseDetailCode());
        if (flag) {
            if (bearCaseMap.containsKey(importVo.getReleaseDetailCode())) {
                OverallPlanCaseVo bearCaseVo = bearCaseMap.get(importVo.getReleaseDetailCode());
                vo.setReleaseCode(bearCaseVo.getSchemeCode());
                vo.setReleaseDetailCode(bearCaseVo.getSchemeDetailCode());
                vo.setReleaseName(bearCaseVo.getSchemeName());
                if (ObjectUtils.isNotEmpty(bearCaseVo.getDetailCode())) {
                    vo.setDetailCode(bearCaseVo.getDetailCode());
                    vo.setDetailName(bearCaseVo.getDetailName());
                    vo.setCategoryCode(bearCaseVo.getCategoryCode());
                    vo.setCategoryName(bearCaseVo.getCategoryName());
                }
                vo.setStartDate(bearCaseVo.getStartDate());
                vo.setEndDate(bearCaseVo.getEndDate());
                vo.setYears(bearCaseVo.getYears());
            } else {
                errMsg.add(String.format("统筹方案承接明细编码%s不存在", importVo.getReleaseDetailCode()));
            }
        }
    }

    /**
     * 转换产品和数据字典
     *
     * @param vo
     * @param importVo
     */
    private void transProductAndDictData(MarketingPlanCaseVo vo, MarketingPlanCaseImportVo importVo) {
        if (ObjectUtils.isNotEmpty(importVo.getItemCodeList())) {
            List<String> codes = Arrays.asList(importVo.getItemCodeList().split(","));
            List<MarketingPlanProductVo> itemList = codes.stream().map(x -> {
                MarketingPlanProductVo item = new MarketingPlanProductVo();
                item.setCode(x);
                return item;
            }).collect(Collectors.toList());
            vo.setItemList(itemList);
        }
        if (ObjectUtils.isNotEmpty(importVo.getFeeItemCodeList())) {
            List<String> codes = Arrays.asList(importVo.getFeeItemCodeList().split(","));
            List<MarketingPlanProductVo> feeItemList = codes.stream().map(x -> {
                MarketingPlanProductVo item = new MarketingPlanProductVo();
                item.setCode(x);
                return item;
            }).collect(Collectors.toList());
            vo.setFeeItemList(feeItemList);
        }
        if (ObjectUtils.isNotEmpty(importVo.getFeeBelongItemCodeList())) {
            List<String> codes = Arrays.asList(importVo.getFeeBelongItemCodeList().split(","));
            List<MarketingPlanProductVo> feeBelongItemList = codes.stream().map(x -> {
                MarketingPlanProductVo item = new MarketingPlanProductVo();
                item.setCode(x);
                return item;
            }).collect(Collectors.toList());
            vo.setFeeBelongItemList(feeBelongItemList);
        }
        if (ObjectUtils.isNotEmpty(importVo.getProductCodeList())) {
            List<String> codes = Arrays.asList(importVo.getProductCodeList().split(","));
            List<MarketingPlanProductVo> productList = codes.stream().map(x -> {
                MarketingPlanProductVo item = new MarketingPlanProductVo();
                item.setCode(x);
                return item;
            }).collect(Collectors.toList());
            vo.setProductList(productList);
        }
        if (ObjectUtils.isNotEmpty(importVo.getFeeProductCodeList())) {
            List<String> codes = Arrays.asList(importVo.getFeeProductCodeList().split(","));
            List<MarketingPlanProductVo> feeProductList = codes.stream().map(x -> {
                MarketingPlanProductVo item = new MarketingPlanProductVo();
                item.setCode(x);
                return item;
            }).collect(Collectors.toList());
            vo.setFeeProductList(feeProductList);
        }
        if (ObjectUtils.isNotEmpty(importVo.getLevelCodeList())) {
            List<String> codes = Arrays.asList(importVo.getLevelCodeList().split(","));
            List<MarketingPlanProductVo> levelList = codes.stream().map(x -> {
                MarketingPlanProductVo item = new MarketingPlanProductVo();
                item.setCode(x);
                return item;
            }).collect(Collectors.toList());
            vo.setLevelList(levelList);
        }
        if (ObjectUtils.isNotEmpty(importVo.getFeeLevelCodeList())) {
            List<String> codes = Arrays.asList(importVo.getFeeLevelCodeList().split(","));
            List<MarketingPlanProductVo> feeLevelList = codes.stream().map(x -> {
                MarketingPlanProductVo item = new MarketingPlanProductVo();
                item.setCode(x);
                return item;
            }).collect(Collectors.toList());
            vo.setFeeLevelList(feeLevelList);
        }
        //数据字典
        if (ObjectUtils.isNotEmpty(importVo.getCostBasisList())) {
            List<String> codes = Arrays.asList(importVo.getCostBasisList().split(","));
            vo.setCostBasisList(codes);
        }
        if (ObjectUtils.isNotEmpty(importVo.getExecuteExampleList())) {
            List<String> codes = Arrays.asList(importVo.getExecuteExampleList().split(","));
            vo.setExecuteExampleList(codes);
        }
        if (ObjectUtils.isNotEmpty(importVo.getCloseCaseExampleList())) {
            List<String> codes = Arrays.asList(importVo.getCloseCaseExampleList().split(","));
            vo.setCloseCaseExampleList(codes);
        }

        //兑付方式
        if (ObjectUtils.isNotEmpty(vo.getCashType())) {
            Map<String, String> map = cashMap();
            if (map.containsKey(vo.getCashType())) {
                vo.setCashType(map.get(vo.getCashType()));
            }
        }
        //费用依据
        if (CollectionUtils.isNotEmpty(vo.getCostBasisList())) {
            Map<String, String> map = costBasisMap();
            List<String> list = Lists.newArrayList();
            for (String s : vo.getCostBasisList()) {
                list.add(map.getOrDefault(s, s));
            }
            vo.setCostBasisList(list);
        }
        //物料类型
        if (ObjectUtils.isNotEmpty(vo.getSellMaterialType())) {
            Map<String, String> map = sellMaterialTypeMap();
            if (map.containsKey(vo.getSellMaterialType())) {
                vo.setSellMaterialType(map.get(vo.getSellMaterialType()));
            }
        }
        //运输方式
        if (ObjectUtils.isNotEmpty(vo.getTransportType())) {
            Map<String, String> map = transportTypeMap();
            if (map.containsKey(vo.getTransportType())) {
                vo.setTransportType(map.get(vo.getTransportType()));
            }
        }
        //投放平台
        if (ObjectUtils.isNotEmpty(vo.getPlatform())) {
            Map<String, String> map = platFormMap();
            if (map.containsKey(vo.getPlatform())) {
                vo.setPlatform(map.get(vo.getPlatform()));
            }
        }
        //返利类型
        if (ObjectUtils.isNotEmpty(vo.getRebateType())) {
            Map<String, String> map = rebateTypeMap();
            if (map.containsKey(vo.getRebateType())) {
                vo.setRebateType(map.get(vo.getRebateType()));
            }
        }
        //执行示例
        if (CollectionUtils.isNotEmpty(vo.getExecuteExampleList())) {
            Map<String, String> map = excuteExampleMap();
            List<String> list = Lists.newArrayList();
            for (String s : vo.getExecuteExampleList()) {
                list.add(map.getOrDefault(s, s));
            }
            vo.setExecuteExampleList(list);
        }
        //结案示例
        if (CollectionUtils.isNotEmpty(vo.getCloseCaseExampleList())) {
            Map<String, String> map = endCaseExampleMap();
            List<String> list = Lists.newArrayList();
            for (String s : vo.getCloseCaseExampleList()) {
                list.add(map.getOrDefault(s, s));
            }
            vo.setCloseCaseExampleList(list);
        }
        //是否合同
        if (ObjectUtils.isNotEmpty(vo.getIsContractCost())) {
            if (BooleanEnum.TRUE.getSure().equals(vo.getIsContractCost())) {
                vo.setIsContractCost(BooleanEnum.TRUE.getCapital());
            } else {
                vo.setIsContractCost(BooleanEnum.FALSE.getCapital());
            }
        }
    }


    //兑付方式
    private final static ThreadLocal<Map<String, String>> cashThreadLocal = new ThreadLocal<>();
    //费用依据
    private final static ThreadLocal<Map<String, String>> costBasisThreadLocal = new ThreadLocal<>();
    //执行示例
    private final static ThreadLocal<Map<String, String>> excuteExampleThreadLocal = new ThreadLocal<>();
    //结案示例
    private final static ThreadLocal<Map<String, String>> endCaseExampleThreadLocal = new ThreadLocal<>();
    //物料类型
    private final static ThreadLocal<Map<String, String>> sellMaterialTypeThreadLocal = new ThreadLocal<>();
    //周边运输方式
    private final static ThreadLocal<Map<String, String>> transprotTypeThreadLocal = new ThreadLocal<>();
    //投放平台
    private final static ThreadLocal<Map<String, String>> platFormThreadLocal = new ThreadLocal<>();
    //返利类型
    private final static ThreadLocal<Map<String, String>> rebateTypeThreadLocal = new ThreadLocal<>();

    /**
     * 兑付方式
     *
     * @return
     */
    private Map<String, String> cashMap() {
        Map<String, String> map = cashThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_SCHEME_CASH_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            cashThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 费用依据
     *
     * @return
     */
    private Map<String, String> costBasisMap() {
        Map<String, String> map = costBasisThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_SCHEME_COST_BASIS);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            costBasisThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 执行示例
     *
     * @return
     */
    private Map<String, String> excuteExampleMap() {
        Map<String, String> map = excuteExampleThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = planCaseRepository.findCloseCaseExampleList();
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            excuteExampleThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 结案示例
     *
     * @return
     */
    private Map<String, String> endCaseExampleMap() {
        Map<String, String> map = endCaseExampleThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = planCaseRepository.findCloseCaseExampleList();
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            endCaseExampleThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 物料类型
     *
     * @return
     */
    private Map<String, String> sellMaterialTypeMap() {
        Map<String, String> map = sellMaterialTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SELL_MATERIAL_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            sellMaterialTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 运输方式
     *
     * @return
     */
    private Map<String, String> transportTypeMap() {
        Map<String, String> map = transprotTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_TRANSPORT_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            transprotTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 投放平台
     *
     * @return
     */
    private Map<String, String> platFormMap() {
        Map<String, String> map = platFormThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_PLATFORM);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            platFormThreadLocal.set(map);
        }
        return map;
    }


    /**
     * 返利类型
     *
     * @return
     */
    private Map<String, String> rebateTypeMap() {
        Map<String, String> map = rebateTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TPM_REBATE_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
            rebateTypeThreadLocal.set(map);
        }
        return map;
    }

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, MarketingPlanCaseImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    @Override
    public Class<MarketingPlanCaseImportVo> findCrmExcelVoClass() {
        return MarketingPlanCaseImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "tpm_marketing_plan_case_import";
    }

    @Override
    public String getTemplateName() {
        return "TPM-营销规划方案明细导入";
    }
}
