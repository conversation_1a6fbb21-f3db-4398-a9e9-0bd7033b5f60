package com.biz.crm.tpm.business.activities.scheme.service.internal;

import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.activities.scheme.repository.SchemeRangeRepository;
import com.biz.crm.tpm.business.activities.scheme.service.SchemeRangeVoService;
import com.biz.crm.tpm.business.activities.scheme.dto.SchemeRangeDto;
import com.biz.crm.tpm.business.activities.scheme.entity.SchemeRange;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeRangeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 方案范围;(tpm_scheme_range)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Service("schemeRangeVoService")
public class SchemeRangeVoServiceImpl implements SchemeRangeVoService {
  @Autowired
  private SchemeRangeRepository schemeRangeRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public Set<SchemeRangeVo> findBySchemeCode(String schemeCode) {
    List<SchemeRange> schemeRanges = this.schemeRangeRepository.findBySchemeCode(schemeCode);
    Collection<SchemeRangeVo> schemeRangeVos = this.nebulaToolkitService.copyCollectionByWhiteList(schemeRanges, SchemeRange.class, SchemeRangeVo.class, LinkedHashSet.class, ArrayList.class);
    return Sets.newLinkedHashSet(schemeRangeVos);
  }

  @Override
  public Set<SchemeRangeVo> findAll() {
    List<SchemeRange> schemeRanges = this.schemeRangeRepository.findAll();
    Collection<SchemeRangeVo> schemeRangeVos = this.nebulaToolkitService.copyCollectionByWhiteList(schemeRanges, SchemeRange.class, SchemeRangeVo.class, LinkedHashSet.class, ArrayList.class);
    return Sets.newLinkedHashSet(schemeRangeVos);
  }

  /**
   * 新增数据
   *
   * @param schemeRangeVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public SchemeRangeVo create(SchemeRangeVo schemeRangeVo) {
    this.createValidate(schemeRangeVo);
    SchemeRange schemeRange = this.nebulaToolkitService.copyObjectByWhiteList(schemeRangeVo, SchemeRange.class, LinkedHashSet.class, ArrayList.class);
    schemeRange.setTenantCode(TenantUtils.getTenantCode());
    schemeRange.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    this.schemeRangeRepository.saveOrUpdate(schemeRange);

    return schemeRangeVo;
  }

  /**
   * 修改新据
   *
   * @param schemeRangeDto 实体对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public SchemeRangeVo update(SchemeRangeDto schemeRangeDto) {
    this.updateValidate(schemeRangeDto);
    SchemeRange schemeRange = this.schemeRangeRepository.findByIdAndTenantCode(schemeRangeDto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(schemeRange,"修改数据不存在，请检查！");
    schemeRange.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    schemeRange.setTenantCode(TenantUtils.getTenantCode());
    this.schemeRangeRepository.saveOrUpdate(schemeRange);
    SchemeRangeVo schemeRangeVo = this.nebulaToolkitService.copyObjectByWhiteList(schemeRange, SchemeRangeVo.class, LinkedHashSet.class, ArrayList.class);

    return schemeRangeVo;
  }

  @Override
  public void deleteBySchemeCode(String schemeCode) {
    Validate.notBlank(schemeCode, "删除方案范围数据，方案编码不能为空！");
    this.schemeRangeRepository.deleteBySchemeCode(schemeCode);
  }

  @Override
  public void deleteByRangeCodes(Collection<String> codes) {
    this.schemeRangeRepository.deleteByRangeCodes(codes);
  }

  /**
   * 批量根据id启用
   * @param ids
   */
  @Override
  @Transactional
  public void enable(List<String> ids) {
    Validate.notEmpty(ids, "启用时，id不能为空");
    List<SchemeRange> schemeRanges = this.schemeRangeRepository.findByIds(ids);
    if(CollectionUtils.isEmpty(schemeRanges)){
      return;
    }
    Collection<SchemeRangeVo> schemeRangeVos = this.nebulaToolkitService.copyCollectionByWhiteList(schemeRanges, SchemeRange.class, SchemeRangeVo.class, LinkedHashSet.class, ArrayList.class);
    //TODO 自定义验证
    this.schemeRangeRepository.updateEnableStatusByIds(EnableStatusEnum.ENABLE, ids);
  }

  /**
   * 批量根据id禁用
   * @param ids
   */
  @Override
  @Transactional
  public void disable(List<String> ids) {
    Validate.notEmpty(ids, "禁用时，id不能为空");
    List<SchemeRange> schemeRanges = this.schemeRangeRepository.findByIds(ids);
    if(CollectionUtils.isEmpty(schemeRanges)){
      return;
    }
    Collection<SchemeRangeVo> schemeRangeVos = this.nebulaToolkitService.copyCollectionByWhiteList(schemeRanges, SchemeRange.class, SchemeRangeVo.class, LinkedHashSet.class, ArrayList.class);
    //TODO 自定义验证
    this.schemeRangeRepository.updateEnableStatusByIds(EnableStatusEnum.DISABLE, ids);
  }

  public void updateEnableStatus(Set<String> codes, String enableStatus) {
    List<SchemeRange> schemeRanges = this.schemeRangeRepository.findByCodes(codes);
    if (CollectionUtils.isEmpty(schemeRanges)) {
      return;
    }
    codes = schemeRanges.stream().map(SchemeRange::getRangeCode).collect(Collectors.toSet());
    this.schemeRangeRepository.updateEnableStatusByCodes(EnableStatusEnum.codeToEnum(enableStatus), codes);
  }

  /**
   * 创建验证
   *
   * @param schemeRangeVo
   */
  private void createValidate(SchemeRangeVo schemeRangeVo) {
    Validate.notNull(schemeRangeVo, "新增时，对象信息不能为空！");
    schemeRangeVo.setId(null);
    // 验证重复操作
    Validate.notBlank(schemeRangeVo.getSchemeCode(), "新增数据时，方案编号不能为空！");
    Validate.notNull(schemeRangeVo.getRangeType(), "新增数据时，范围类型(1,组织,2,组织类型)不能为空！");
    Validate.notBlank(schemeRangeVo.getRangeCode(), "新增数据时，范围编号不能为空！");
    Validate.notBlank(schemeRangeVo.getRangeName(), "新增数据时，范围名称不能为空！");
  }

  /**
   * 修改验证
   *
   * @param schemeRangeDto
   */
  private void updateValidate(SchemeRangeDto schemeRangeDto) {
    Validate.notNull(schemeRangeDto, "修改时，对象信息不能为空！");
    Validate.notBlank(schemeRangeDto.getId(), "修改数据时，主键不能为空！");
  }
}