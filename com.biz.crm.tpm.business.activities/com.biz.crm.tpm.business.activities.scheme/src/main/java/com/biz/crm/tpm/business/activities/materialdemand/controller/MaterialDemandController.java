package com.biz.crm.tpm.business.activities.materialdemand.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:36
 */
@RestController
@RequestMapping("/v1/materialDemandController")
@Api(tags = "物料需求提报")
public class MaterialDemandController extends BusinessPageCacheController<MaterialDemandDetailVo, MaterialDemandDetailVo> {

    @Autowired
    private MaterialDemandService service;

    @Resource
    private RedisLockService redisLockService;

    @ApiOperation(value = "分页查询")
    @GetMapping("findList")
    public Result<Page<MaterialDemandVo>> findList(@PageableDefault(50) Pageable pageable, MaterialDemandVo vo) {
        return Result.ok(service.findList(pageable, vo));
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("queryByIdOrCodes")
    public Result<MaterialDemandVo> queryByIdOrCodes(@RequestParam(required = false, value = "id") String id,
                                                     @RequestParam(required = false, value = "demandCode") String demandCode) {
        return Result.ok(service.queryByIdOrCode(id, demandCode));
    }

    @ApiOperation(value = "新增/编辑")
    @PostMapping("createOrUpdate")
    public Result createOrUpdate(@RequestBody MaterialDemandVo vo) {
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            service.update(vo);
        } else {
            service.create(vo);
        }
        return Result.ok();
    }


    @DeleteMapping("deleteBatchByIds")
    @ApiOperation(value = "删除")
    public Result deleteBatchByIds(@RequestParam List<String> ids) {
        String codes = service.deleteBatchByIds(ids);
        if (ObjectUtils.isNotEmpty(codes)) {
            return Result.error(String.format("提报编码%s状态为审批中/审批通过的不允许删除",codes));
        }
        return Result.ok();
    }
}
