package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.dto.PlanExecutionSumEditDto;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanExecutionSumDetailVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanExecutionSumVo;
import org.springframework.data.domain.Pageable;

/**
 * @Author: yangrui
 * @Date: 2024-12-31 20:16
 */
public interface PlanExecutionSumService {

    void savePlanExecutionSum(MarketingPlanCase marketingPlanCase);

    Page<PlanExecutionSumVo> queryPage(Pageable pageable, PlanExecutionSumVo param);

    PlanExecutionSumVo queryBySchemeDetailCode(String schemeDetailCode);

    PlanExecutionSumDetailVo queryDetail(String id);

    void edit(PlanExecutionSumEditDto dto);
}
