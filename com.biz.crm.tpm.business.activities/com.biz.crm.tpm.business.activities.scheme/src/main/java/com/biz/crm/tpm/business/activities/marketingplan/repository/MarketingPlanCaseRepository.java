package com.biz.crm.tpm.business.activities.marketingplan.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.BusinessConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.business.activities.marketingplan.dto.CustomerMonthCategoryApplyAmountDTO;
import com.biz.crm.tpm.business.activities.marketingplan.dto.MarketingPlanSecondCategoryDto;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCaseExtend;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanCaseMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.TerminalExpenseVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectImageVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 注：使用此方法使一定要特别注意关联 marketing_plan_case_extend 此方法已经不适用 lambdaQuery 直接查询和直接修改
 * <AUTHOR>
 * @Date 2024/6/3 16:42
 */
@Component
public class MarketingPlanCaseRepository extends ServiceImpl<MarketingPlanCaseMapper, MarketingPlanCase> {

    @Resource
    private MarketingPlanCaseExtendRepository extendRepository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    public void saveBatchList(List<MarketingPlanCase> list) {
        this.saveBatch(list);
        //保存扩展
        List<MarketingPlanCaseExtend> extendList = (List<MarketingPlanCaseExtend>) nebulaToolkitService.copyCollectionByWhiteList(list, MarketingPlanCase.class,
                MarketingPlanCaseExtend.class, HashSet.class, ArrayList.class);
        extendRepository.saveBatch(extendList);
    }

    public List<MarketingPlanCase> findListBySchemeCodes(List<String> schemeCodes) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setSchemeCodeList(schemeCodes);
        return this.baseMapper.findListByBaseCondition(queryVo);
    }

    public List<MarketingPlanCase> findListBySchemeCodesAndCustomerName(List<String> schemeCodes, String customerName) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setSchemeCodeList(schemeCodes);
        queryVo.setCustomerName(customerName);
        return this.baseMapper.findListByBaseCondition(queryVo);
    }

    public List<MarketingPlanCase> findListBySchemeCodesAndDelFlag(List<String> schemeCodes, String delFlag) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setSchemeCodeList(schemeCodes);
        queryVo.setDelFlag(delFlag);
        return this.baseMapper.findListByBaseCondition(queryVo);
    }


    public List<MarketingPlanCase> findListBySchemeDetailCode(List<String> schemeDetailCodes) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setSchemeDetailCodeList(schemeDetailCodes);
        return this.baseMapper.findListByBaseCondition(queryVo);
    }


    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.remove(Wrappers.lambdaQuery(MarketingPlanCase.class)
                .in(MarketingPlanCase::getSchemeCode, schemeCodes));
        extendRepository.deleteBySchemeCodes(schemeCodes);
    }

    public void deleteBySchemeDetailCodes(List<String> schemeDetailCodes) {
        this.remove(Wrappers.lambdaQuery(MarketingPlanCase.class)
                .in(MarketingPlanCase::getSchemeDetailCode, schemeDetailCodes));
        extendRepository.deleteBySchemeDetailCodes(schemeDetailCodes);
    }


    public List<MarketingPlanCase> findAlreadyBearCase(List<String> releaseDetailCodes) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setReleaseDetailCodeList(releaseDetailCodes);
        return this.baseMapper.findListByBaseCondition(queryVo);
    }


    public Page<MarketingPlanCaseVo> findCaseList(Page<MarketingPlanCaseVo> page, MarketingPlanCaseVo vo, String processStatus) {
        return this.baseMapper.findCaseList(page, vo, processStatus);
    }


    public Page<MarketingPlanCaseVo> findCaseListByChangeScheme(Page<MarketingPlanCaseVo> page, MarketingPlanCaseVo vo, String processStatus, List<String> originalSchemeDetailCodes) {
        return this.baseMapper.findCaseListByChangeScheme(page, vo, processStatus, originalSchemeDetailCodes);
    }


    public List<String> findWithholdingBySchemeCodes(List<String> schemeDetailCodes) {
        return this.baseMapper.findWithholdingBySchemeCodes(schemeDetailCodes);
    }

    public List<MarketingPlanCase> findByYearOrg(String years, List<String> orgCodes) {
        return lambdaQuery().eq(MarketingPlanCase::getYears, years)
                .in(MarketingPlanCase::getBelongDepartmentCode, orgCodes)
                .gt(MarketingPlanCase::getCashAmount, BigDecimal.ZERO).list();
    }


    public List<MarketingPlanCase> findReleaseDetailCaseList(String schemeCode) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setSchemeCode(schemeCode);
        List<MarketingPlanCase> caseList = this.baseMapper.findListByBaseCondition(queryVo);
        if (CollectionUtils.isEmpty(caseList)) {
            return Lists.newArrayList();
        }
        return caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getReleaseDetailCode())).collect(Collectors.toList());
    }

    public Set<String> findCaseTypeBySchemeCode(String schemeCode) {
        List<MarketingPlanCase> caseList = this.lambdaQuery()
                .eq(MarketingPlanCase::getSchemeCode, schemeCode)
//                .eq(MarketingPlanCase::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .select(MarketingPlanCase::getSchemeCode, MarketingPlanCase::getCaseType)
                .groupBy(MarketingPlanCase::getCaseType)
                .list();
        Set<String> caseSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(caseList)) {
            caseSet = caseList.stream().map(MarketingPlanCase::getCaseType).collect(Collectors.toSet());
        }
        return caseSet;
    }

    public MarketingPlanCase findByCaseCode(String caseCode) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setSchemeDetailCode(caseCode);
        List<MarketingPlanCase> caseList = this.baseMapper.findListByBaseCondition(queryVo);
        if (CollectionUtils.isEmpty(caseList)) {
            return null;
        }
        return caseList.get(0);
    }

    public List<MarketingPlanCase> findByCaseCodes(List<String> caseCodes) {
        if (CollectionUtils.isEmpty(caseCodes)) {
            return Lists.newArrayList();
        }
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setSchemeDetailCodeList(caseCodes);
        return baseMapper.findListByBaseCondition(queryVo);
    }


    public List<DictDataVo> findCloseCaseExampleList() {
        return this.baseMapper.findCloseCaseExampleList(TenantUtils.getTenantCode());
    }

    public List<MarketingPlanCase> findPushActListBySchemeCodeAndDetailCodes(List<String> schemeDetailCodes, List<String> detailCodes) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setDetailCodeList(detailCodes);
        queryVo.setSchemeDetailCodeList(schemeDetailCodes);
        return this.baseMapper.findListByBaseCondition(queryVo);
    }

    public void rewriteEndCaseOrWithholding(List<MarketingPlanCaseVo> caseVos) {
        for (MarketingPlanCaseVo caseVo : caseVos) {
            this.lambdaUpdate()
                    .set(ObjectUtils.isNotEmpty(caseVo.getAuditAmount()), MarketingPlanCase::getAuditAmount, caseVo.getAuditAmount())
                    .set(ObjectUtils.isNotEmpty(caseVo.getAuditStatus()), MarketingPlanCase::getAuditStatus, caseVo.getAuditStatus())
                    .set(ObjectUtils.isNotEmpty(caseVo.getCashAmount()), MarketingPlanCase::getCashAmount, caseVo.getCashAmount())
                    .set(ObjectUtils.isNotEmpty(caseVo.getCashStatus()), MarketingPlanCase::getCashStatus, caseVo.getCashStatus())
                    .eq(MarketingPlanCase::getSchemeDetailCode, caseVo.getSchemeDetailCode()).update();
        }
    }


    public List<MarketingPlanCaseVo> findMaterialListByMaterialCodesAndOrgCodes(List<String> materialCodes, List<String> orgCodes) {
        if (CollectionUtils.isEmpty(materialCodes) || CollectionUtils.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findMaterialListByMaterialCodesAndOrgCodes(materialCodes, orgCodes, TenantUtils.getTenantCode());
    }


    public List<MarketingPlanCaseVo> findMaterialListByMaterialCodesAndOrgCodesAndExclusiveSchemeCode(List<String> materialCodes, List<String> orgCodes, String schemeCode) {
        if (CollectionUtils.isEmpty(materialCodes) || CollectionUtils.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findMaterialListByMaterialCodesAndOrgCodesAndExclusiveSchemeCode(materialCodes, orgCodes, schemeCode, TenantUtils.getTenantCode());
    }

    public List<MarketingPlanCaseVo> findAuditAndWithholdingAndCashReleaseDetailCodeList(List<String> releaseDetailCodes, String bearFlag) {
        return this.baseMapper.findAuditAndWithholdingAndCashReleaseDetailCodeList(releaseDetailCodes, bearFlag, TenantUtils.getTenantCode());
    }

    public Page<MarketingPlanCaseVo> findRegionMarketingPlanCase(Page<MarketingPlanCaseVo> page, String releaseDetailCode) {
        return this.baseMapper.findRegionMarketingPlanCase(page, releaseDetailCode, TenantUtils.getTenantCode());
    }

    public Page<MarketingPlanCaseVo> findMarketingPlanCaseReportList(Page<MarketingPlanCaseVo> page, MarketingPlanCaseVo vo) {
        return this.baseMapper.findMarketingPlanCaseReportList(page, vo, TenantUtils.getTenantCode());
    }

    public Page<MarketingPlanCaseVo> findNotMarketingClosedCaseList(Page<MarketingPlanCaseVo> page, MarketingPlanCaseQueryDto vo) {
        return this.baseMapper.findNotMarketingClosedCaseList(page, vo, TenantUtils.getTenantCode());
    }

    public Page<MarketingPlanCaseVo> findSafActPolicy(Page<MarketingPlanCaseVo> page, MarketingPlanCaseQueryDto vo) {
        return this.baseMapper.findSafActPolicy(page, vo);
    }

    public void updateCaseStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String delFlag) {
        this.lambdaUpdate()
                .in(MarketingPlanCase::getSchemeDetailCode, schemeDetailCodes)
                .set(MarketingPlanCase::getDelFlag, delFlag)
                .update();
    }

    public List<MarketingPlanCase> findListByFormulaCode(String formulaCode) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setConditionFormula(formulaCode);
        queryVo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        return this.baseMapper.findListByBaseCondition(queryVo);
    }

    public List<MarketingPlanCase> findListByDetailCodes(List<String> detailCodes) {
        MarketingPlanCaseQueryDto queryVo = new MarketingPlanCaseQueryDto();
        queryVo.setTenantCode(TenantUtils.getTenantCode());
        queryVo.setDetailCodeList(detailCodes);
        queryVo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        return this.baseMapper.findListByBaseCondition(queryVo);
    }

    public Page<MarketingPlanCaseExecuteVo> findMarketingPlanCaseExecuteList(Page<MarketingPlanCaseExecuteVo> page, MarketingPlanCaseQueryDto dto) {
        List<String> clientCodeList = Lists.newArrayList();
        if (StringUtil.isNotEmpty(dto.getClientCode())) {
            clientCodeList.add(dto.getClientCode());
        }
        if (CollectionUtil.isNotEmpty(dto.getClientCodeList())) {
            clientCodeList.addAll(dto.getClientCodeList());
        }
        if (CollectionUtil.isEmpty(clientCodeList)) {
            return new Page<>();
        }
        dto.setClientCodeList(clientCodeList);
        return this.baseMapper.findMarketingPlanCaseExecuteList(page, dto);
    }


    public List<MarketingPlanCaseExecuteVo> findByTerminalMarketingPlanCaseExecuteList(MarketingPlanCaseQueryDto dto) {
        List<String> clientCodeList = Lists.newArrayList();
        if (StringUtil.isNotEmpty(dto.getClientCode())) {
            clientCodeList.add(dto.getClientCode());
        }
        if (CollectionUtil.isNotEmpty(dto.getClientCodeList())) {
            clientCodeList.addAll(dto.getClientCodeList());
        }
        if (CollectionUtil.isEmpty(clientCodeList)) {
            return Lists.newArrayList();
        }
        dto.setClientCodeList(clientCodeList);
        return this.baseMapper.findByTerminalMarketingPlanCaseExecuteList(dto);
    }

    public List<String> findSendSfaCostTypeDetail(String executionType) {
        if (StringUtil.isEmpty(executionType)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findSendSfaCostTypeDetail(executionType);
    }

    /**
     * 采集执行实例明细
     *
     * @param detailCodeList
     * @return
     */
    public List<CostTypeDetailCollectVo> findCollectCostTypeDetail(List<String> detailCodeList) {
        if (CollectionUtils.isEmpty(detailCodeList)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findCollectCostTypeDetail(detailCodeList);
    }

    public List<ApprovalCollectImageVo> findCollectImage(List<String> collectCodeList) {
        if (CollectionUtils.isEmpty(collectCodeList)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findCollectImage(collectCodeList);
    }

    /**
     * 终端费用统计
     *
     * @param vo
     * @return
     */
    public List<TerminalExpenseVo> findTerminalExpense(MarketingPlanCaseQueryDto vo) {
        return this.baseMapper.findTerminalExpense(vo);
    }

    public List<TerminalExpenseVo> findCustomerExpense(MarketingPlanCaseQueryDto vo) {
        return this.baseMapper.findCustomerExpense(vo);
    }

    /**
     * 查询合同方案明细通过部门+年月
     *
     * @param departmentCodes
     * @param years
     * @return
     */
    public List<MarketingPlanCaseVo> findContractByDepartmentCodesAndYears(List<String> departmentCodes, String years) {
        return this.baseMapper.findContractByDepartmentCodesAndYears(departmentCodes, years, TenantUtils.getTenantCode());
    }

    /**
     * 活动执行采集 根据执行入参查询待执行的数据
     *
     * @param schemeCode
     * @param schemeDetailCode
     * @param terminalCode
     * @return
     */
    public MarketingPlanCase findByExecute(String schemeCode, String schemeDetailCode, String terminalCode) {
        if (StringUtils.isBlank(schemeCode) || StringUtils.isBlank(schemeDetailCode) || StringUtils.isBlank(terminalCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(MarketingPlanCase::getSchemeCode, schemeCode)
                .eq(MarketingPlanCase::getSchemeDetailCode, schemeDetailCode)
                .eq(MarketingPlanCase::getTerminalCode, terminalCode)
                .eq(MarketingPlanCase::getExecuteStatus, BusinessConstant.BOOLEAN_NO)
                .one();
    }

    public MarketingPlanCase findBySchemeDetailCode(String schemeDetailCode) {
        if (StringUtils.isBlank(schemeDetailCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(MarketingPlanCase::getSchemeDetailCode, schemeDetailCode)
                .one();
    }

    public Integer findExistByContractCodes(List<String> contractCodes) {
        if (CollectionUtils.isEmpty(contractCodes)) {
            return 0;
        }
        return this.lambdaQuery()
                .in(MarketingPlanCase::getContractCode, contractCodes)
                .eq(MarketingPlanCase::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .count();
    }

    public List<MarketingPlanCaseVo> findAlreadyUndertakeAmount(List<String> releaseDetailCodes) {
        if (CollectionUtils.isEmpty(releaseDetailCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findAlreadyUndertakeAmount(releaseDetailCodes);
    }

    public List<MarketingPlanCaseVo> findChanging(List<String> schemeDetailCodes) {
        if (CollectionUtils.isEmpty(schemeDetailCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findChanging(schemeDetailCodes);
    }

    public List<String> findExpenseChangeTerminal(Date startDate, Date endDate) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findExpenseChangeTerminal(startDate, endDate);
    }

    public List<MarketingPlanCase> findListByTerminalCodes(List<String> terminalCodes, String year) {
        if (CollectionUtils.isEmpty(terminalCodes) || StringUtils.isBlank(year)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(MarketingPlanCase::getTerminalCode, terminalCodes)
                .like(MarketingPlanCase::getYears, year)
                .list();
    }

    /**
     * @param pageable
     * @param vo
     * @return
     */
    public Page<MarketingPlanCaseVo> findExpensesCountByConditions(Pageable pageable, MarketingPlanCaseQueryDto vo) {
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findExpensesCountByConditions(page, vo);
    }

    public List<MarketingPlanCaseVo> findByCollectCode(String collectCode) {
        if (StringUtil.isEmpty(collectCode)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findByCollectCode(collectCode);
    }

    public List<MarketingPlanCaseVo> findBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findBySchemeCodes(schemeCodes);
    }

    public List<MarketingPlanCase> findCustomerCodeListBySchemeCode(String schemeCode) {
        return this.lambdaQuery()
                .eq(MarketingPlanCase::getSchemeCode, schemeCode)
                .select(MarketingPlanCase::getSchemeCode, MarketingPlanCase::getCustomerCode)
                .list();
    }

    public List<String> findCategoryCodeBySecondCategory(String secondCategory) {
        return this.baseMapper.findCategoryCodeBySecondCategory(TenantUtils.getTenantCode(), secondCategory);
    }

    public List<String> findCategoryCodeBySecondCategoryList(List<String> secondCategoryList) {
        return this.baseMapper.findCategoryCodeBySecondCategoryList(TenantUtils.getTenantCode(), secondCategoryList);
    }


    public Page<MarketingPlanCaseVo> findPlanCaseListBySecondCategory(Page<MarketingPlanCaseVo> page, MarketingPlanSecondCategoryDto vo) {
        return this.baseMapper.findPlanCaseListBySecondCategory(page, vo);
    }


    public List<MarketingPlanCaseVo> findPlanCaseListBySecondCategoryList(MarketingPlanSecondCategoryDto vo) {
        return this.baseMapper.findPlanCaseListBySecondCategoryList(vo);
    }


    public List<String> findCountTerminalByTerminalCodesAndYears(List<String> terminalCodeList, String years) {
        return this.baseMapper.findCountTerminalByTerminalCodesAndYears(terminalCodeList, years);
    }

    public List<MarketingPlanCaseVo> findAmountByOrgCodesAndYearsList(List<String> orgCodes, List<String> yearsList) {
        return this.baseMapper.findAmountByOrgCodesAndYearsList(orgCodes, yearsList);
    }

    public List<MarketingPlanCaseVo> findCaseListByDmsMiniHomePage(String customerCode, List<String> yearsList) {
        return this.baseMapper.findCaseListByDmsMiniHomePage(customerCode, yearsList);
    }

    public void updateCaseDmsReadFlag(String schemeDetailCode, String dmsReadFlag) {
        this.lambdaUpdate()
                .eq(MarketingPlanCase::getSchemeDetailCode, schemeDetailCode)
                .set(MarketingPlanCase::getDmsReadFlag, dmsReadFlag)
                .update();
    }

    public List<MarketingPlanCaseVo> findCaseListByYearsAndDelFlagProcess(String years) {
        return this.baseMapper.findCaseListByYearsAndDelFlagProcess(years);
    }

    public List<CustomerMonthCategoryApplyAmountDTO> sumApplyAmountByCustomerCodesYearMonth(List<String> customerCodes, String years) {
        return this.baseMapper.sumApplyAmountByCustomerCodesYearMonth(customerCodes, years);
    }


    public Page<MarketingPlanCaseVo> findContractByYearsAndLogin(Page<MarketingPlanCaseVo> page, MarketingPlanVo vo) {
        return this.baseMapper.findContractByYearsAndLogin(page, vo);
    }

    public List<MarketingPlanCaseVo> findCaseListAuditData( List<String> collect) {
          if(CollectionUtils.isEmpty(collect)){
              return Lists.newArrayList();
          }
          return this.baseMapper.findCaseListAuditData(collect);
    }

    public List<MarketingPlanCaseVo> findGiftByYearOrg(String yearMonthLy, List<String> orgCodeList, List<String> itemCodes) {
        return this.baseMapper.findGiftByYearOrg(yearMonthLy, orgCodeList, itemCodes);
    }

    public List<MarketingPlanCaseVo> queryCurMonthApprovedMarketingPlanCases(String startDate, String endDate) {
        return this.baseMapper.queryCurMonthApprovedMarketingPlanCases(startDate, endDate);
    }

    public List<MarketingPlanCase> findByReleaseDetailCodes(List<String> releaseDetailCodes) {
        return this.baseMapper.findByReleaseDetailCodes(releaseDetailCodes);
    }

    public List<MarketingPlanCaseVo> findRegionMarketingPlanCases(List<String> releaseDetailCodes){
        if(CollectionUtils.isEmpty(releaseDetailCodes)){
            return Lists.newArrayList();
        }
        return this.baseMapper.findRegionMarketingPlanCases(releaseDetailCodes,TenantUtils.getTenantCode());
    }

}
