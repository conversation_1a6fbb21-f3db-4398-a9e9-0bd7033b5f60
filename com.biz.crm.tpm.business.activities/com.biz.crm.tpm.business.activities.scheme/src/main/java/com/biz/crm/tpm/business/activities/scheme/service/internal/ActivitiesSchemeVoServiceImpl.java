package com.biz.crm.tpm.business.activities.scheme.service.internal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.common.form.sdk.DynamicFormService;
import com.biz.crm.common.form.sdk.context.DynamicFormContext;
import com.biz.crm.common.form.sdk.context.DynamicFormContextHolder;
import com.biz.crm.tpm.business.activities.dynamic.template.constant.ActivitiesConstant;
import com.biz.crm.tpm.business.activities.dynamic.template.enums.FormTypeEnum;
import com.biz.crm.tpm.business.activities.dynamic.template.service.DynamicFormServiceResolver;
import com.biz.crm.tpm.business.activities.scheme.common.SchemeConstants;
import com.biz.crm.tpm.business.activities.scheme.constant.ActivitiesSchemeConstant;
import com.biz.crm.tpm.business.activities.scheme.dto.ActivitiesSchemeContextDto;
import com.biz.crm.tpm.business.activities.scheme.dto.ActivitiesSchemeDto;
import com.biz.crm.tpm.business.activities.scheme.dto.ActivitiesSchemeLogEventDto;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesScheme;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeDetailRelation;
import com.biz.crm.tpm.business.activities.scheme.event.ActivitiesSchemeEventListener;
import com.biz.crm.tpm.business.activities.scheme.event.ActivitiesSchemeLogEventListener;
import com.biz.crm.tpm.business.activities.scheme.repository.ActivitiesSchemeDetailRelationRepository;
import com.biz.crm.tpm.business.activities.scheme.repository.ActivitiesSchemeRepository;
import com.biz.crm.tpm.business.activities.scheme.service.ActivitiesSchemeDetailRelationService;
import com.biz.crm.tpm.business.activities.scheme.service.ActivitiesSchemeFilesService;
import com.biz.crm.tpm.business.activities.scheme.service.ActivitiesSchemeRelationService;
import com.biz.crm.tpm.business.activities.scheme.service.ActivitiesSchemeVoService;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeDetailRelationVo;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeFilesVo;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeRelationVo;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeVo;
import com.biz.crm.tpm.business.activities.sdk.enums.ActivityStatusEnum;
import com.biz.crm.tpm.business.activities.sdk.event.ActivitiesEventListener;
import com.biz.crm.tpm.business.activities.sdk.service.BasicActivitiesInfoService;
import com.biz.crm.tpm.business.activities.sdk.strategy.close.ActivityItemsClosedStrategy;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BaseActivityItemVo;
import com.biz.crm.tpm.business.activities.sdk.vo.BasicActivityItemVo;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessMappingDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.ProcessBusinessMappingService;
import com.biz.crm.workflow.sdk.service.ProcessBusinessService;
import com.biz.crm.workflow.sdk.vo.ProcessBusinessMappingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.activities.sdk.constant.ActivitiesConstant.DYNAMIC_FORM_FIELD_CODE;
import static com.biz.crm.tpm.business.budget.sdk.enums.RedisKeys.GENERATE_PREFIX;

/**
 * 方案活动;(tpm_activities_scheme)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-6-1
 */
@Service("activitiesSchemeVoService")
public class ActivitiesSchemeVoServiceImpl implements ActivitiesSchemeVoService, BasicActivitiesInfoService {

  @Autowired
  private ActivitiesSchemeRepository activitiesSchemeRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private RedisMutexService redisMutexService;
  @Autowired(required = false)
  private List<ActivitiesSchemeEventListener> activitiesSchemeEventListeners;
  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private DynamicFormServiceResolver dynamicFormServiceResolver;
  @Autowired
  private ActivitiesSchemeRelationService activitiesSchemeRelationService;
  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired(required = false)
  private List<ActivitiesEventListener> activitiesEventListeners;
  @Autowired
  private ActivitiesSchemeFilesService activitiesSchemeFilesService;
  @Autowired(required = false)
  private List<ActivityItemsClosedStrategy> activitiesClosedStrategies;
  @Autowired(required = false)
  private ProcessBusinessService processBusinessService;
  @Autowired(required = false)
  private ProcessBusinessMappingService processBusinessMappingService;
  @Autowired
  private ActivitiesSchemeDetailRelationService activitiesSchemeDetailRelationService;

  /**
   * 分页查询数据
   *
   * @param pageable 分页对象
   * @param dto      实体对象
   * @return
   */
  @Override
  public Page<ActivitiesSchemeVo> findByConditions(Pageable pageable, ActivitiesSchemeDto dto) {
    ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
    if (Objects.isNull(dto)) {
      dto = new ActivitiesSchemeDto();
    }
    return this.activitiesSchemeRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @Override
  public ActivitiesSchemeVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    ActivitiesScheme activitiesScheme = this.activitiesSchemeRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (activitiesScheme == null) {
      return null;
    }
    ActivitiesSchemeVo activitiesSchemeVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesScheme, ActivitiesSchemeVo.class, LinkedHashSet.class, ArrayList.class);

    //2.====查询关联信息
    List<ActivitiesSchemeRelationVo> activitiesSchemeRelationVos = this.activitiesSchemeRelationService.findByActivityCode(activitiesSchemeVo.getActivitiesCode());
    if (CollectionUtils.isEmpty(activitiesSchemeRelationVos)) {
      return activitiesSchemeVo;
    }
    List<ActivitiesSchemeDetailRelationVo> activitiesSchemeDetailRelationVos = activitiesSchemeDetailRelationService.findByActivityCode(activitiesSchemeVo.getActivitiesCode());
    if (CollectionUtils.isEmpty(activitiesSchemeDetailRelationVos)) {
      return null;
    }
    activitiesSchemeVo.setRelations(activitiesSchemeRelationVos);
    activitiesSchemeVo.setDetailRelations(activitiesSchemeDetailRelationVos);

    //3.====查询动态模板明细
    Set<String> dynamicKeys = activitiesSchemeDetailRelationVos.stream().filter(e -> StringUtils.isNotBlank(e.getCostTypeDetailCode())).map(e -> StringUtils.joinWith(":", e.getCostBudgetCode(), e.getCostTypeDetailCode())).collect(Collectors.toSet());
    for (String dynamicKey : dynamicKeys) {
      DynamicFormService<ActivitiesSchemeVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ActivitiesSchemeVo.class,FormTypeEnum.APPLY);
      if (dynamicFormService == null) {
        continue;
      }
      dynamicFormService.perfectDynamicDetails(activitiesSchemeVo, activitiesSchemeVo.getActivitiesCode());
    }
    //4.====查询附件
    List<ActivitiesSchemeFilesVo> activitiesSchemeFilesVos = this.activitiesSchemeFilesService.findByActivitiesCode(activitiesScheme.getActivitiesCode());
    activitiesSchemeVo.setFiles(activitiesSchemeFilesVos);
    return activitiesSchemeVo;
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 主键
   * @return 单条数据
   */
  @Override
  public ActivitiesSchemeVo findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    ActivitiesScheme activitiesScheme = this.activitiesSchemeRepository.findByCode(code);
    if (activitiesScheme == null) {
      return null;
    }
    ActivitiesSchemeVo activitiesSchemeVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesScheme, ActivitiesSchemeVo.class, LinkedHashSet.class, ArrayList.class);
    //2.====查询关联信息
    List<ActivitiesSchemeRelationVo> activitiesSchemeRelationVos = this.activitiesSchemeRelationService.findByActivityCode(activitiesSchemeVo.getActivitiesCode());
    if (CollectionUtils.isEmpty(activitiesSchemeRelationVos)) {
      return activitiesSchemeVo;
    }
    List<ActivitiesSchemeDetailRelationVo> activitiesSchemeDetailRelationVos = activitiesSchemeDetailRelationService.findByActivityCode(activitiesSchemeVo.getActivitiesCode());
    if (CollectionUtils.isEmpty(activitiesSchemeDetailRelationVos)) {
      return activitiesSchemeVo;
    }
    activitiesSchemeVo.setRelations(activitiesSchemeRelationVos);
    activitiesSchemeVo.setDetailRelations(activitiesSchemeDetailRelationVos);

    //3.====查询动态模板明细
    Set<String> dynamicKeys = activitiesSchemeDetailRelationVos.stream().filter(e -> StringUtils.isNotBlank(e.getCostTypeDetailCode())).map(e -> StringUtils.joinWith(":", e.getCostBudgetCode(), e.getCostTypeDetailCode())).collect(Collectors.toSet());
    for (String dynamicKey : dynamicKeys) {
      DynamicFormService<ActivitiesSchemeVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ActivitiesSchemeVo.class,FormTypeEnum.APPLY);
      if (dynamicFormService == null) {
        continue;
      }
      dynamicFormService.perfectDynamicDetails(activitiesSchemeVo, activitiesSchemeVo.getActivitiesCode());
    }
    //4.====查询附件
    List<ActivitiesSchemeFilesVo> activitiesSchemeFilesVos = this.activitiesSchemeFilesService.findByActivitiesCode(activitiesScheme.getActivitiesCode());
    activitiesSchemeVo.setFiles(activitiesSchemeFilesVos);
    return activitiesSchemeVo;
  }

  /**
   * 新增数据
   *
   * @param json Json对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public ActivitiesSchemeVo create(JSONObject json) {
    Validate.notNull(json, "新增方案活动数据为空，请检查！");
    ActivitiesSchemeDto data = JSON.parseObject(JSON.toJSONString(json), ActivitiesSchemeDto.class);
    ActivitiesSchemeVo activitiesSchemeVo = this.nebulaToolkitService.copyObjectByWhiteList(data, ActivitiesSchemeVo.class, HashSet.class, ArrayList.class, "items", "relations", "attachmentVos", "detailRelations");
    activitiesSchemeVo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    activitiesSchemeVo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    String code = this.generateCodeService.generateCode(SchemeConstants.ACTIVITY_SCHEME_LADDER_CODE);
    activitiesSchemeVo.setActivitiesCode(code);
    activitiesSchemeVo.setTenantCode(TenantUtils.getTenantCode());
    //1.1、基础信息验证
    this.createValidate(activitiesSchemeVo);
    ActivitiesScheme activitiesScheme = this.nebulaToolkitService.copyObjectByWhiteList(activitiesSchemeVo, ActivitiesScheme.class, LinkedHashSet.class, ArrayList.class);
    activitiesSchemeVo.setId(activitiesScheme.getId());
    //1.2 基础信息保存
    activitiesScheme.setStatus(this.analysisStatus(data.getBeginTime(), data.getEndTime()).getCode());
    activitiesScheme.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesSchemeRepository.saveOrUpdate(activitiesScheme);

    //2. ====保存关联信息
    activitiesSchemeVo.getRelations().forEach(vo -> {
      vo.setId(null);
      vo.setActivityCode(activitiesScheme.getActivitiesCode());
      vo.setTenantCode(TenantUtils.getTenantCode());
    });
    this.activitiesSchemeRelationService.saveBatch(activitiesSchemeVo.getRelations());
    //3. ====保存细类关联信息
    activitiesSchemeVo.getDetailRelations().forEach(e -> {
      e.setId(null);
      e.setActivityCode(activitiesScheme.getActivitiesCode());
      e.setTenantCode(TenantUtils.getTenantCode());
    });
    activitiesSchemeDetailRelationService.saveBatch(activitiesSchemeVo.getDetailRelations());
    //保存明细
    String parentCode = activitiesScheme.getActivitiesCode();
    Set<DynamicFormService<ActivitiesSchemeVo>> dynamicFormServices = dynamicFormServiceResolver.getDynamicFormServices(json, DYNAMIC_FORM_FIELD_CODE, ActivitiesSchemeVo.class, FormTypeEnum.APPLY);
    Validate.notEmpty(dynamicFormServices, "根据提供的信息，未能获取到匹配的动态模板服务类信息");
    try {
      ActivitiesSchemeContextDto contextDto = this.buildActivityContextDto(null, data, true);
      DynamicFormContext context = this.prepareDynamicFormContext(contextDto);
      for (DynamicFormService<ActivitiesSchemeVo> dynamicFormService : dynamicFormServices) {
        dynamicFormService.createDynamicDetails(activitiesSchemeVo, parentCode);
      }
      //验证总金额
      this.validateTotalApplyAmount(context);
    } finally {
      DynamicFormContextHolder.clearContext();
    }
    // 处理保存附件
    if (!CollectionUtils.isEmpty(data.getFiles())) {
      data.getFiles().forEach(item -> {
        item.setTenantCode(TenantUtils.getTenantCode());
        item.setActivitiesCode(activitiesSchemeVo.getActivitiesCode());
      });
      List<ActivitiesSchemeFilesVo> activitiesSchemeFilesVos = this.activitiesSchemeFilesService.createBatch(data.getFiles());
      activitiesSchemeVo.setFiles(activitiesSchemeFilesVos);
    }

    activitiesSchemeVo.setId(activitiesScheme.getId());
    if (!CollectionUtils.isEmpty(activitiesSchemeEventListeners)) {
      for (ActivitiesSchemeEventListener activitiesSchemeEventListener : activitiesSchemeEventListeners) {
        activitiesSchemeEventListener.onCreated(activitiesSchemeVo);
      }
    }
    //工作流
    if (data.getProcessBusiness() != null) {
      //费用预算占用
      ActivitiesSchemeVo activityVo = this.findByCode(activitiesScheme.getActivitiesCode());
      for (Map.Entry<String, List<BaseActivityItemVo>> item : activityVo.getItems().entrySet()) {
        List<BasicActivityItemVo> basicItems = JSONArray.parseArray(JSONArray.toJSONString(item.getValue()), BasicActivityItemVo.class);
        Validate.notEmpty(basicItems, "明细信息不能为空");
        for (BasicActivityItemVo basicItem : basicItems) {
          costBudgetVoService.occupy(activitiesScheme.getActivitiesCode(), basicItem.getItemCode(), basicItem.getCostBudgetCode(), basicItem.getApplyAmount(), basicItem.getRemark(), CostBudgetItemSourceType.SCHEMA_ACTIVITY.getDescr());
        }
      }
      data.setId(activitiesScheme.getId());
      data.setActivitiesCode(activitiesScheme.getActivitiesCode());
      //提交工作流
      this.commitProcess(data);
    }

    //新增业务日志
    ActivitiesSchemeLogEventDto logEventDto = new ActivitiesSchemeLogEventDto();
    logEventDto.setOriginal(null);
    logEventDto.setNewest(activitiesSchemeVo);
    SerializableBiConsumer<ActivitiesSchemeLogEventListener, ActivitiesSchemeLogEventDto> onCreate =
            ActivitiesSchemeLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(logEventDto, ActivitiesSchemeLogEventListener.class, onCreate);

    return activitiesSchemeVo;
  }

  /**
   * 修改新据
   *
   * @param json Json对象
   * @return 修改结果
   */
  @Transactional
  @Override
  public ActivitiesSchemeVo update(JSONObject json) {
    Validate.notNull(json, "修改方案活动数据为空，请检查！");
    ActivitiesSchemeDto data = JSON.parseObject(JSON.toJSONString(json), ActivitiesSchemeDto.class);
    ActivitiesSchemeVo activitiesSchemeVo = this.nebulaToolkitService.copyObjectByWhiteList(data, ActivitiesSchemeVo.class, HashSet.class, ArrayList.class, "items", "relations", "attachmentVos","detailRelations");
    this.updateValidate(activitiesSchemeVo);
    ActivitiesScheme activitiesScheme = this.activitiesSchemeRepository.findByIdAndTenantCode(activitiesSchemeVo.getId(),TenantUtils.getTenantCode());
    ActivitiesSchemeContextDto contextDto = this.buildActivityContextDto(nebulaToolkitService.copyObjectByWhiteList(activitiesScheme, ActivitiesSchemeVo.class, HashSet.class, ArrayList.class), data, false);
    // 检查修改数据的审核状态，只有待审核数据可以删除
    List<ProcessBusinessMappingVo> processBusinessMappingVoList = this.findProcessBusinessMappingVo(Lists.newArrayList(activitiesScheme.getActivitiesCode()));
    if (!CollectionUtils.isEmpty(processBusinessMappingVoList)) {
      ProcessBusinessMappingVo processBusinessMappingVo = processBusinessMappingVoList.get(0);
      String processStatus = processBusinessMappingVo.getProcessStatus();
      Validate.isTrue(ProcessStatusEnum.RECOVER.getDictCode().equals(processStatus) || ProcessStatusEnum.REJECT.getDictCode().equals(processStatus), "只有状态为【流程追回、审批驳回】活动数据能修改，【%s】活动申请状态为【%s】", activitiesScheme.getActivitiesName(), ProcessStatusEnum.getStatusNameByKey(processStatus));
    }
    ActivitiesSchemeVo oldActivitiesSchemeVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesScheme, ActivitiesSchemeVo.class, LinkedHashSet.class, ArrayList.class);
    Validate.notNull(activitiesScheme, "修改数据不存在，请检查！");

    activitiesScheme.setActivitiesName(activitiesSchemeVo.getActivitiesName());
    activitiesScheme.setSchemeCode(activitiesSchemeVo.getSchemeCode());
    activitiesScheme.setBeginTime(activitiesSchemeVo.getBeginTime());
    activitiesScheme.setEndTime(activitiesSchemeVo.getEndTime());
    activitiesScheme.setRemark(activitiesSchemeVo.getRemark());
    activitiesScheme.setTotalApplyAmount(activitiesSchemeVo.getTotalApplyAmount());
    activitiesScheme.setStatus(this.analysisStatus(data.getBeginTime(), data.getEndTime()).getCode());
    activitiesScheme.setTenantCode(TenantUtils.getTenantCode());
    this.activitiesSchemeRepository.saveOrUpdate(activitiesScheme);

    //2.=====保存关联信息
    List<ActivitiesSchemeRelationVo> dbActivitiesSchemeRelationVo = this.activitiesSchemeRelationService.findByActivityCode(activitiesScheme.getActivitiesCode());
    List<ActivitiesSchemeDetailRelationVo> dbActivitiesSchemeDetailRelationVo = this.activitiesSchemeDetailRelationService.findByActivityCode(activitiesScheme.getActivitiesCode());
    Set<String> currentIds = activitiesSchemeVo.getRelations().stream().map(ActivitiesSchemeRelationVo::getId).collect(Collectors.toSet());
    Set<String> dbIds = dbActivitiesSchemeRelationVo.stream().map(ActivitiesSchemeRelationVo::getId).collect(Collectors.toSet());
    //处理关联信息删除情况
    Set<String> currentDetailIds = activitiesSchemeVo.getDetailRelations().stream().map(ActivitiesSchemeDetailRelationVo::getId).collect(Collectors.toSet());
    Set<String> dbDetailIds = dbActivitiesSchemeDetailRelationVo.stream().map(ActivitiesSchemeDetailRelationVo::getId).collect(Collectors.toSet());
    //处理关联信息删除情况
    Set<String> needDetailDeletes = Sets.difference(dbDetailIds, currentDetailIds);
    Set<String> needDeletes = Sets.difference(dbIds, currentIds);
    if (!CollectionUtils.isEmpty(needDeletes)) {
      this.activitiesSchemeRelationService.removeByIds(needDeletes);
    }
    if (!CollectionUtils.isEmpty(needDetailDeletes)) {
      activitiesSchemeDetailRelationService.removeByIds(needDetailDeletes);
    }

    //处理关联信息新增情况
    List<ActivitiesSchemeRelationVo> needAdds = activitiesSchemeVo.getRelations().stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());
    List<ActivitiesSchemeDetailRelationVo> needDetailAdds = activitiesSchemeVo.getDetailRelations().stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());

    if (!CollectionUtils.isEmpty(needAdds)) {
      for (ActivitiesSchemeRelationVo vo : needAdds) {
        vo.setId(null);
        vo.setActivityCode(activitiesScheme.getActivitiesCode());
        vo.setTenantCode(activitiesScheme.getTenantCode());
        this.activitiesSchemeRelationService.save(vo);
      }
    }
    if (!CollectionUtils.isEmpty(needDetailAdds)) {
      List<ActivitiesSchemeDetailRelationVo> relations = Lists.newArrayList();
      for (ActivitiesSchemeDetailRelationVo e : needDetailAdds) {
        e.setId(null);
        e.setActivityCode(activitiesScheme.getActivitiesCode());
        e.setTenantCode(activitiesScheme.getTenantCode());
        relations.add(e);
      }
      activitiesSchemeDetailRelationService.saveBatch(relations);
    }

    //3.=====保存动态模板明细(对activitiesSchemeVo进行了重新赋值)
    this.processDynamicFormsForUpdate(activitiesSchemeVo, contextDto);

    // 处理保存附件
    if (!CollectionUtils.isEmpty(data.getFiles())) {
      this.activitiesSchemeFilesService.deleteByActivitiesCode(data.getActivitiesCode());
      data.getFiles().forEach(item -> {
        item.setTenantCode(TenantUtils.getTenantCode());
        item.setActivitiesCode(data.getActivitiesCode());
      });
      List<ActivitiesSchemeFilesVo> activitiesSchemeFilesVos = this.activitiesSchemeFilesService.createBatch(data.getFiles());
      activitiesSchemeVo.setFiles(activitiesSchemeFilesVos);
    }
    if (!CollectionUtils.isEmpty(activitiesSchemeEventListeners)) {
      for (ActivitiesSchemeEventListener activitiesSchemeEventListener : activitiesSchemeEventListeners) {
        activitiesSchemeEventListener.onUpdate(oldActivitiesSchemeVo, activitiesSchemeVo);
      }
    }
    //工作流
    if (data.getProcessBusiness() != null) {
      //费用预算占用
      ActivitiesSchemeVo activityVo = this.findByCode(activitiesScheme.getActivitiesCode());
      for (Map.Entry<String, List<BaseActivityItemVo>> item : activityVo.getItems().entrySet()) {
        List<BasicActivityItemVo> basicItems = JSONArray.parseArray(JSONArray.toJSONString(item.getValue()), BasicActivityItemVo.class);
        Validate.notEmpty(basicItems, "明细信息不能为空");
        for (BasicActivityItemVo basicItem : basicItems) {
          costBudgetVoService.occupy(activitiesScheme.getActivitiesCode(), basicItem.getItemCode(), basicItem.getCostBudgetCode(), basicItem.getApplyAmount(), basicItem.getRemark(), CostBudgetItemSourceType.SCHEMA_ACTIVITY.getDescr());
        }
      }
      data.setId(activitiesScheme.getId());
      data.setActivitiesCode(activitiesScheme.getActivitiesCode());
      //提交工作流
      this.commitProcess(data);
    }

    //编辑业务日志
    ActivitiesSchemeLogEventDto logEventDto = new ActivitiesSchemeLogEventDto();
    logEventDto.setOriginal(oldActivitiesSchemeVo);
    logEventDto.setNewest(activitiesSchemeVo);
    SerializableBiConsumer<ActivitiesSchemeLogEventListener, ActivitiesSchemeLogEventDto> onUpdate =
            ActivitiesSchemeLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(logEventDto, ActivitiesSchemeLogEventListener.class, onUpdate);
    return activitiesSchemeVo;
  }

  /**
   * 提交工作流进行审批，提交成功返回流程实例ID，提交失败则抛出异常
   */
  private void commitProcess(ActivitiesSchemeDto dto) {
    ProcessBusinessDto processBusiness = dto.getProcessBusiness();
    Validate.notNull(processBusiness, "提交工作流时，未传工作流对象信息!");
    processBusiness.setBusinessNo(dto.getActivitiesCode());
    processBusiness.setBusinessFormJson(JsonUtils.obj2JsonString(dto));
    processBusiness.setBusinessCode(ActivitiesSchemeConstant.PROCESS_NAME);
    this.processBusinessService.processStart(processBusiness);
  }

  /**
   * 根据业务编码查询工作流业务流程关联信息
   *
   * @param businessNos
   */
  private List<ProcessBusinessMappingVo> findProcessBusinessMappingVo(List<String> businessNos) {
    ProcessBusinessMappingDto processBusinessMappingDto = new ProcessBusinessMappingDto();
    processBusinessMappingDto.setBusinessNos(businessNos);
    processBusinessMappingDto.setBusinessCode(ActivitiesSchemeConstant.PROCESS_NAME);
    return processBusinessMappingService.findMultiByByConditions(processBusinessMappingDto);
  }

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  @Transactional
  @Override
  public void delete(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
    List<ActivitiesScheme> activitiesSchemes = this.activitiesSchemeRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(activitiesSchemes)) {
      return;
    }
    // 检查删除数据的审核状态，只有待审核数据可以删除
    List<String> auditCodes = activitiesSchemes.stream().map(ActivitiesScheme::getActivitiesCode).collect(Collectors.toList());
    List<ProcessBusinessMappingVo> processBusinessMappingVoList = this.findProcessBusinessMappingVo(auditCodes);
    if (CollectionUtils.isEmpty(processBusinessMappingVoList)) {
      Map<String, String> map = activitiesSchemes.stream().collect(Collectors.toMap(ActivitiesScheme::getActivitiesCode, ActivitiesScheme::getActivitiesName));
      processBusinessMappingVoList.forEach(item -> {
        throw new RuntimeException("只有【待提交】状态活动数据能删除，【" + map.get(item.getBusinessNo()) + "】活动状态为【" + ProcessStatusEnum.getStatusNameByKey(item.getProcessStatus()) + "】");
      });
    }
    Collection<ActivitiesSchemeVo> activitiesSchemeVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemes, ActivitiesScheme.class, ActivitiesSchemeVo.class, LinkedHashSet.class, ArrayList.class);

    this.activitiesSchemeRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    if (!CollectionUtils.isEmpty(activitiesSchemeEventListeners)) {
      for (ActivitiesSchemeEventListener activitiesSchemeEventListener : activitiesSchemeEventListeners) {
        for (ActivitiesSchemeVo activitiesSchemeVo : activitiesSchemeVos) {
          activitiesSchemeEventListener.onDeleted(activitiesSchemeVo);
        }
      }
    }
    //删除业务日志
    SerializableBiConsumer<ActivitiesSchemeLogEventListener, ActivitiesSchemeLogEventDto> onDelete =
            ActivitiesSchemeLogEventListener::onDelete;
    for (ActivitiesSchemeVo activitiesSchemeVo : activitiesSchemeVos) {
      ActivitiesSchemeLogEventDto logEventDto = new ActivitiesSchemeLogEventDto();
      logEventDto.setOriginal(activitiesSchemeVo);
      this.nebulaNetEventClient.publish(logEventDto, ActivitiesSchemeLogEventListener.class, onDelete);
    }
  }

  /**
   * 通过启用状态查询数据
   *
   * @param enableStatus 状态
   * @return 集合数据
   */
  @Override
  public List<ActivitiesSchemeVo> findByEnableStatus(String enableStatus) {
    if (StringUtils.isBlank(enableStatus)) {
      return Collections.emptyList();
    }
    List<ActivitiesScheme> activitiesScheme = this.activitiesSchemeRepository.findByEnableStatus(enableStatus);
    if (CollectionUtils.isEmpty(activitiesScheme)) {
      return Collections.emptyList();
    }
    Collection<ActivitiesSchemeVo> activitiesSchemes = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesScheme, ActivitiesScheme.class, ActivitiesSchemeVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesSchemes);
  }

  /**
   * 批量根据id启用
   */
  @Override
  @Transactional
  public void enable(List<String> ids) {
    Validate.notEmpty(ids, "启用时，id不能为空");
    List<ActivitiesScheme> activitiesSchemes = this.activitiesSchemeRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(activitiesSchemes)) {
      return;
    }
    Collection<ActivitiesSchemeVo> activitiesSchemeVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemes, ActivitiesScheme.class, ActivitiesSchemeVo.class, LinkedHashSet.class, ArrayList.class);
    //TODO 自定义验证
    this.activitiesSchemeRepository.updateEnableStatusByIds(EnableStatusEnum.ENABLE, ids);
    if (!CollectionUtils.isEmpty(activitiesSchemeEventListeners)) {
      for (ActivitiesSchemeEventListener activitiesSchemeEventListener : activitiesSchemeEventListeners) {
        for (ActivitiesSchemeVo activitiesSchemeVo : activitiesSchemeVos) {
          activitiesSchemeEventListener.onEnable(activitiesSchemeVo);
        }
      }
    }
    //更新状态业务日志
    SerializableBiConsumer<ActivitiesSchemeLogEventListener, ActivitiesSchemeLogEventDto> onEnable =
            ActivitiesSchemeLogEventListener::onEnable;
    for (ActivitiesSchemeVo activitiesSchemeVo : activitiesSchemeVos) {
      ActivitiesSchemeLogEventDto logEventDto = new ActivitiesSchemeLogEventDto();
      logEventDto.setOriginal(activitiesSchemeVo);
      ActivitiesSchemeVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesSchemeVo, ActivitiesSchemeVo.class, HashSet.class, ArrayList.class);
      newVo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      logEventDto.setNewest(newVo);
      this.nebulaNetEventClient.publish(logEventDto, ActivitiesSchemeLogEventListener.class, onEnable);
    }
  }

  /**
   * 批量根据id禁用
   */
  @Override
  @Transactional
  public void disable(List<String> ids) {
    Validate.notEmpty(ids, "禁用时，id不能为空");
    List<ActivitiesScheme> activitiesSchemes = this.activitiesSchemeRepository.findByIds(ids);
    if (CollectionUtils.isEmpty(activitiesSchemes)) {
      return;
    }
    Collection<ActivitiesSchemeVo> activitiesSchemeVos = this.nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemes, ActivitiesScheme.class, ActivitiesSchemeVo.class, LinkedHashSet.class, ArrayList.class);
    //TODO 自定义验证
    this.activitiesSchemeRepository.updateEnableStatusByIds(EnableStatusEnum.DISABLE, ids);
    if (!CollectionUtils.isEmpty(activitiesSchemeEventListeners)) {
      for (ActivitiesSchemeEventListener activitiesSchemeEventListener : activitiesSchemeEventListeners) {
        for (ActivitiesSchemeVo activitiesSchemeVo : activitiesSchemeVos) {
          activitiesSchemeEventListener.onEnable(activitiesSchemeVo);
        }
      }
    }
    //更新状态业务日志
    SerializableBiConsumer<ActivitiesSchemeLogEventListener, ActivitiesSchemeLogEventDto> onDisable =
            ActivitiesSchemeLogEventListener::onDisable;
    for (ActivitiesSchemeVo activitiesSchemeVo : activitiesSchemeVos) {
      ActivitiesSchemeLogEventDto logEventDto = new ActivitiesSchemeLogEventDto();
      logEventDto.setOriginal(activitiesSchemeVo);
      ActivitiesSchemeVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(activitiesSchemeVo, ActivitiesSchemeVo.class, HashSet.class, ArrayList.class);
      newVo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      logEventDto.setNewest(newVo);
      this.nebulaNetEventClient.publish(logEventDto, ActivitiesSchemeLogEventListener.class, onDisable);
    }
  }

  @Override
  public int countByScheme(String schemeCode) {
    return this.activitiesSchemeRepository.countByScheme(schemeCode);
  }

  /**
   * 生成操作标记
   */
  @Override
  public String preSave() {
    String prefix = UUID.randomUUID().toString();
    // 1天后过期
    this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, TimeUnit.MILLISECONDS.convert(1, TimeUnit.DAYS));
    return prefix;
  }

  /**
   * 验证与操作标记
   */
  private void validationPrefix(ActivitiesSchemeVo activitiesSchemeVo) {
    // 验证重复提交标识
    String prefix = activitiesSchemeVo.getPrefix();
    Validate.notBlank(prefix, "错误的预操作标记，请检查!!");
    Validate.isTrue(StringUtils.isNotBlank(this.redisMutexService.getMCode(GENERATE_PREFIX, prefix)), "没有发现预操作标记，可能是因为重复操作的原因!");
    boolean isLock = false;
    try {
      if (isLock = this.redisMutexService.tryLock(prefix, TimeUnit.MILLISECONDS, 1)) {
        this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, 1L);
      } else {
        throw new IllegalArgumentException("请不要重复操作!!");
      }
    } finally {
      if (isLock) {
        this.redisMutexService.unlock(prefix);
      }
    }
  }

  /**
   * 创建验证
   */
  private void createValidate(ActivitiesSchemeVo activitiesSchemeVo) {
    Validate.notNull(activitiesSchemeVo, "新增时，对象信息不能为空！");
    activitiesSchemeVo.setId(null);
    // 验证重复操作
    this.validationPrefix(activitiesSchemeVo);
    Validate.notEmpty(activitiesSchemeVo.getRelations(), "新增数据时，关联信息不能为空，请检查! ");
    Validate.notBlank(activitiesSchemeVo.getEnableStatus(), "新增数据时，数据业务状态（启用状态）不能为空！");
    Validate.notBlank(activitiesSchemeVo.getDelFlag(), "新增数据时，数据状态（删除状态）不能为空！");
    Validate.notBlank(activitiesSchemeVo.getActivitiesName(), "新增数据时，活动名称不能为空！");
    Validate.notBlank(activitiesSchemeVo.getActivitiesCode(), "新增数据时，活动编号不能为空！");
    Validate.notNull(activitiesSchemeVo.getBeginTime(), "新增数据时，开始时间不能为空！");
    Validate.notNull(activitiesSchemeVo.getEndTime(), "新增数据时，结束时间不能为空！");
    Validate.notBlank(activitiesSchemeVo.getSchemeCode(), "新增数据时，方案编号不能为空！");
    Validate.notNull(activitiesSchemeVo.getTotalApplyAmount(), "新增数据时，总申请金额不能为空！");
    Validate.notEmpty(activitiesSchemeVo.getItems(), "新增数据时，明细表单数据不能为空！");
    for (ActivitiesSchemeRelationVo relation : activitiesSchemeVo.getRelations()) {
      Validate.notBlank(relation.getCostBudgetCode(), "关联的费用预算编码不能为空");
      if (StringUtils.isNotBlank(relation.getCostTypeCategoryCode()) || StringUtils.isNotBlank(relation.getCostTypeCategoryName())) {
        Validate.notBlank(relation.getCostTypeCategoryCode(), "关联的活动大类编码不能为空");
        Validate.notBlank(relation.getCostTypeCategoryName(), "关联的活动大类名称不能为空");
      }
    }
    for (ActivitiesSchemeDetailRelationVo relation : activitiesSchemeVo.getDetailRelations()) {
      Validate.notBlank(relation.getCostBudgetCode(), "关联的费用预算编码不能为空");
      if (StringUtils.isNotBlank(relation.getCostTypeDetailCode()) || StringUtils.isNotBlank(relation.getCostTypeDetailName())) {
        Validate.notBlank(relation.getCostTypeDetailCode(), "关联的活动细类编码不能为空");
        Validate.notBlank(relation.getCostTypeDetailName(), "关联的活动细类名称不能为空");
      }
    }
  }

  /**
   * 修改验证
   */
  private void updateValidate(ActivitiesSchemeVo activitiesSchemeVo) {
    Validate.notNull(activitiesSchemeVo, "修改时，对象信息不能为空！");
    // 验证重复操作
    this.validationPrefix(activitiesSchemeVo);
    for (ActivitiesSchemeRelationVo relation : activitiesSchemeVo.getRelations()) {
      Validate.notBlank(relation.getCostBudgetCode(), "关联的费用预算编码不能为空");
      if (StringUtils.isNotBlank(relation.getCostTypeCategoryCode()) || StringUtils.isNotBlank(relation.getCostTypeCategoryName())) {
        Validate.notBlank(relation.getCostTypeCategoryCode(), "关联的活动大类编码不能为空");
        Validate.notBlank(relation.getCostTypeCategoryName(), "关联的活动大类名称不能为空");
      }
    }
    for (ActivitiesSchemeDetailRelationVo relation : activitiesSchemeVo.getDetailRelations()) {
      Validate.notBlank(relation.getCostBudgetCode(), "关联的费用预算编码不能为空");
      if (StringUtils.isNotBlank(relation.getCostTypeDetailCode()) || StringUtils.isNotBlank(relation.getCostTypeDetailName())) {
        Validate.notBlank(relation.getCostTypeDetailCode(), "关联的活动细类编码不能为空");
        Validate.notBlank(relation.getCostTypeDetailName(), "关联的活动细类名称不能为空");
      }
    }
  }

  private ActivitiesSchemeContextDto buildActivityContextDto(ActivitiesSchemeVo sourceActivity, ActivitiesSchemeDto targetActivity, boolean addOperate) {
    ActivitiesSchemeContextDto contextDto = new ActivitiesSchemeContextDto();
    Validate.notNull(targetActivity, "最新活动信息不能为空");
    contextDto.setTargetActivity(targetActivity);
    if (!addOperate) {
      Validate.notNull(sourceActivity, "历史活动信息不能为空");
      contextDto.setSourceActivity(sourceActivity);
    }
    return contextDto;
  }

  private DynamicFormContext prepareDynamicFormContext(ActivitiesSchemeContextDto contextDto) {
    ActivitiesSchemeDto targetActivity = contextDto.getTargetActivity();
    ActivitiesSchemeVo sourceActivity = contextDto.getSourceActivity();
    Validate.notNull(targetActivity.getTotalApplyAmount(), "总申请金额不能为空");
    Validate.isTrue(targetActivity.getTotalApplyAmount().compareTo(BigDecimal.ZERO) > 0, "总申请金额必须大于0");
    //组装上下文
    DynamicFormContext context = DynamicFormContextHolder.getContext();
    context.put(ActivitiesConstant.TARGET_TOTAL_APPLY_AMOUNT_KEY, targetActivity.getTotalApplyAmount());
    context.put(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY, BigDecimal.ZERO);
    context.put(ActivitiesConstant.ACTIVITY_START_TIME_KEY, targetActivity.getBeginTime());
    context.put(ActivitiesConstant.ACTIVITY_END_TIME_KEY, targetActivity.getEndTime());
    if (sourceActivity != null) {
      context.put(ActivitiesConstant.SOURCE_TOTAL_APPLY_AMOUNT_KEY, sourceActivity.getTotalApplyAmount());
    }
    return context;
  }

  /**
   * 该方法在com.biz.tpm.business.activities.local.service.internal.ActivitiesDetailServiceImpl#closed(java.util.Collection)中已做全局锁，
   * 此处就没有做锁了，因为这个方法一般不会被外界指定调用
   */
  @Override
  @Transactional
  public void onClosed(Map<String, Set<String>> codeMap) {
    Validate.notEmpty(codeMap, "活动关闭时，传入的活动编码信息不能为空");
    List<ActivitiesVo> activitiesVos = this.findDetailsByParentCodes(codeMap.keySet());
    if (CollectionUtils.isEmpty(activitiesVos)) {
      return;
    }
    Collection<ActivitiesSchemeVo> activitiesSchemeVos = nebulaToolkitService.copyCollectionByWhiteList(activitiesVos, ActivitiesVo.class, ActivitiesSchemeVo.class, HashSet.class, ArrayList.class);
    for (ActivitiesSchemeVo activitiesSchemeVo : activitiesSchemeVos) {
      Map<String, List<BaseActivityItemVo>> itemMap = activitiesSchemeVo.getItems();
      Set<String> waiteToCloseCodes = codeMap.get(activitiesSchemeVo.getActivitiesCode());
      Map<String, Boolean> closedStatus = Maps.newHashMap();
      for (Map.Entry<String, List<BaseActivityItemVo>> entry : itemMap.entrySet()) {
        String dynamicFormCode = entry.getValue().get(0).getDynamicFormCode();
        Validate.notBlank(dynamicFormCode, "活动关闭时，方案活动【%s】活动明细对应的动态表单编码不能为空", activitiesSchemeVo.getActivitiesCode());
        Set<String> itemCodes = entry.getValue().stream().map(BaseActivityItemVo::getItemCode).collect(Collectors.toSet());
        ActivityItemsClosedStrategy strategy = null;
        for (ActivityItemsClosedStrategy activityItemsClosedStrategy : activitiesClosedStrategies) {
          if (activityItemsClosedStrategy.dynamicFormCode().equals(dynamicFormCode)) {
            strategy = activityItemsClosedStrategy;
            break;
          }
        }
        Validate.notNull(strategy, "活动关闭时，根据提供的动态表单编码【%s】没有找到对应的策略信息", dynamicFormCode);
        Set<String> intersections = Sets.intersection(waiteToCloseCodes, itemCodes);
        if (CollectionUtils.isEmpty(intersections)) {
          closedStatus.put(entry.getKey(), strategy.allClosed(activitiesSchemeVo.getActivitiesCode()));
          continue;
        }
        //明细活动关闭
        strategy.closed(intersections);
        //判断是否全部关闭或部分关闭
        boolean allClosed = strategy.allClosed(activitiesSchemeVo.getActivitiesCode());
        closedStatus.put(entry.getKey(), allClosed);
      }
      boolean isAllClosed = closedStatus.values().stream().allMatch(e -> e != null && e);
      //更新主表状态
      activitiesSchemeRepository.updateForClose(activitiesSchemeVo.getActivitiesCode(), isAllClosed);
    }
  }

  /**
   * 该方法在com.biz.tpm.business.activities.local.task.ActivitiesTask#autoRefreshActivityStatusForActivityTime()中已做全局锁，
   * 此处就没有做锁了，因为这个方法一般不会被外界指定调用
   */
  @Override
  @Transactional
  public void refreshActivityStatusForActivityTime() {
    List<ActivitiesScheme> activities = activitiesSchemeRepository.findByRefreshStatusTask();
    if (CollectionUtils.isEmpty(activities)) {
      return;
    }
    for (ActivitiesScheme activitiesScheme : activities) {
      ActivityStatusEnum activityStatusEnum = this.analysisStatus(activitiesScheme.getBeginTime(), activitiesScheme.getEndTime());
      activitiesScheme.setStatus(activityStatusEnum.getCode());
      activitiesScheme.setTenantCode(TenantUtils.getTenantCode());
    }
    activitiesSchemeRepository.saveOrUpdateBatch(activities);
  }

  @Override
  public String activityMark() {
    return ActivitiesSchemeConstant.ACTIVITY_MARK;
  }

  @Override
  public Map<String, List<BasicActivityItemVo>> findByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return Maps.newHashMap();
    }
    ActivitiesSchemeVo activitiesSchemeVo = this.findByCode(parentCode);
    if (activitiesSchemeVo == null || CollectionUtils.isEmpty(activitiesSchemeVo.getItems())) {
      return Maps.newHashMap();
    }

    Map<String, List<BasicActivityItemVo>> result = Maps.newHashMap();
    for (Map.Entry<String, List<BaseActivityItemVo>> entry : activitiesSchemeVo.getItems().entrySet()) {
      List<BaseActivityItemVo> itemVos = entry.getValue();
      List<BasicActivityItemVo> items = JSON.parseArray(JSON.toJSONString(itemVos), BasicActivityItemVo.class);
      result.put(entry.getKey(), items);
    }
    return result;
  }

  @Override
  public ActivitiesVo findDetailsByParentCode(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }
    ActivitiesSchemeVo activitiesSchemeVo = this.findByCode(parentCode);
    if (activitiesSchemeVo == null) {
      return null;
    }
    return nebulaToolkitService.copyObjectByWhiteList(activitiesSchemeVo, ActivitiesVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public List<ActivitiesVo> findDetailsByParentCodes(Set<String> parentCodes) {
    if (CollectionUtils.isEmpty(parentCodes)) {
      return Lists.newArrayList();
    }
    List<ActivitiesScheme> activitiesSchemes = activitiesSchemeRepository.findByCodes(Lists.newArrayList(parentCodes));
    if (CollectionUtils.isEmpty(activitiesSchemes)) {
      return Lists.newArrayList();
    }
    Set<String> collect = activitiesSchemes.stream().map(ActivitiesScheme::getActivitiesCode).collect(Collectors.toSet());
    List<ActivitiesSchemeRelationVo> relationVos = activitiesSchemeRelationService.findByActivityCodes(collect);
    if (CollectionUtils.isEmpty(relationVos)) {
      return Lists.newArrayList();
    }
    List<ActivitiesSchemeDetailRelationVo> dbActivitiesSchemeDetailRelationVo = this.activitiesSchemeDetailRelationService.findByActivityCodes(collect);
    if (CollectionUtils.isEmpty(dbActivitiesSchemeDetailRelationVo)) {
      return Lists.newArrayList();
    }
    Collection<ActivitiesSchemeVo> activitiesSchemeVos = nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemes, ActivitiesScheme.class, ActivitiesSchemeVo.class, HashSet.class, ArrayList.class);
    for (ActivitiesSchemeVo activitiesSchemeVo : activitiesSchemeVos) {
      List<ActivitiesSchemeRelationVo> relations = relationVos.stream().filter(e -> StringUtils.equals(e.getActivityCode(), activitiesSchemeVo.getActivitiesCode())).collect(Collectors.toList());
      List<ActivitiesSchemeDetailRelationVo> dbActivitiesSchemeDetailRelation = dbActivitiesSchemeDetailRelationVo.stream().filter(e -> StringUtils.equals(e.getActivityCode(), activitiesSchemeVo.getActivitiesCode())).collect(Collectors.toList());
      if (CollectionUtils.isEmpty(relations)) {
        continue;
      }
      if (CollectionUtils.isEmpty(dbActivitiesSchemeDetailRelation)) {
        continue;
      }
      activitiesSchemeVo.setRelations(relations);
      activitiesSchemeVo.setDetailRelations(dbActivitiesSchemeDetailRelation);
      Set<String> dynamicKeys = dbActivitiesSchemeDetailRelation.stream().filter(e -> StringUtils.isNotBlank(e.getCostTypeDetailCode())).map(e -> StringUtils.joinWith(":", e.getCostBudgetCode(), e.getCostTypeDetailCode())).collect(Collectors.toSet());
      for (String dynamicKey : dynamicKeys) {
        DynamicFormService<ActivitiesSchemeVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ActivitiesSchemeVo.class,FormTypeEnum.APPLY);
        if (dynamicFormService == null) {
          continue;
        }
        dynamicFormService.perfectDynamicDetails(activitiesSchemeVo, activitiesSchemeVo.getActivitiesCode());
      }
    }

    Collection<ActivitiesVo> activitiesVos = nebulaToolkitService.copyCollectionByWhiteList(activitiesSchemeVos, ActivitiesSchemeVo.class, ActivitiesVo.class, HashSet.class, ArrayList.class);
    return Lists.newArrayList(activitiesVos);
  }

  @Override
  public BasicActivityItemVo findByParentCodeAndItemCode(String parentCode, String itemCode) {
    if (StringUtils.isBlank(parentCode)) {
      return null;
    }

    ActivitiesSchemeVo activitiesSchemeVo = this.findByCode(parentCode);
    if (activitiesSchemeVo == null || CollectionUtils.isEmpty(activitiesSchemeVo.getItems())) {
      return null;
    }

    for (Map.Entry<String, List<BaseActivityItemVo>> entry : activitiesSchemeVo.getItems().entrySet()) {
      List<BaseActivityItemVo> itemVos = entry.getValue();
      List<BasicActivityItemVo> items = JSON.parseArray(JSON.toJSONString(itemVos), BasicActivityItemVo.class);
      for (BasicActivityItemVo item : items) {
        if (StringUtils.equals(item.getItemCode(), itemCode)) {
          return item;
        }
      }
    }
    return null;
  }

  private ActivitiesSchemeVo processDynamicFormsForUpdate(ActivitiesSchemeVo activitiesSchemeVo, ActivitiesSchemeContextDto contextDto) {
    String parentCode = activitiesSchemeVo.getActivitiesCode();
    Map<String, List<BasicActivityItemVo>> dbBasicItems = this.findByParentCode(parentCode);
    Validate.notEmpty(dbBasicItems, "根据方案活动编码【%s】，未能获取到相应明细信息", parentCode);
    Set<String> dbDynamicKeys = Sets.newHashSet(dbBasicItems.keySet());
    Set<String> dynamicKeys = Sets.newHashSet(contextDto.getTargetActivity().getItems().keySet());
    try {
      DynamicFormContext context = this.prepareDynamicFormContext(contextDto);
      //处理可能的删除情况（对整个动态表单的明细信息进行删除）
      Set<String> needDeleteDynamicForms = Sets.difference(dbDynamicKeys, dynamicKeys);
      if (!CollectionUtils.isEmpty(needDeleteDynamicForms)) {
        for (String dynamicKey : needDeleteDynamicForms) {
          DynamicFormService<ActivitiesSchemeVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ActivitiesSchemeVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息", dynamicKey);
          dynamicFormService.deleteDynamicDetails(parentCode);
        }
      }
      //处理可能的新增（对某个动态表单的明细新增）
      Set<String> needAddDynamicKeys = Sets.difference(dynamicKeys, dbDynamicKeys);
      if (!CollectionUtils.isEmpty(needAddDynamicKeys)) {
        for (String dynamicKey : needAddDynamicKeys) {
          DynamicFormService<ActivitiesSchemeVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ActivitiesSchemeVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息", dynamicKey);
          dynamicFormService.createDynamicDetails(activitiesSchemeVo, parentCode);
        }
      }

      //处理可能的更新
      Set<String> needUpdateDynamicKeys = Sets.intersection(dynamicKeys, dbDynamicKeys);
      if (!CollectionUtils.isEmpty(needUpdateDynamicKeys)) {
        for (String dynamicKey : needUpdateDynamicKeys) {
          DynamicFormService<ActivitiesSchemeVo> dynamicFormService = dynamicFormServiceResolver.getDynamicFormService(dynamicKey, DYNAMIC_FORM_FIELD_CODE, ActivitiesSchemeVo.class,FormTypeEnum.APPLY);
          Validate.notNull(dynamicFormService, "根据提供的dynamicKey【%s】，未能获取到匹配的动态模板服务类信息", dynamicKey);
          dynamicFormService.modifyDynamicDetails(activitiesSchemeVo, parentCode);
        }
      }
      //验证总金额
      this.validateTotalApplyAmount(context);
    } finally {
      DynamicFormContextHolder.clearContext();
    }
    return activitiesSchemeVo;
  }

  private void validateTotalApplyAmount(DynamicFormContext context) {
    //处理最终的上下文
    if (context.exist(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY) && context.exist(ActivitiesConstant.TARGET_TOTAL_APPLY_AMOUNT_KEY)) {
      BigDecimal targetTotalApplyAmount = (BigDecimal) context.get(ActivitiesConstant.TARGET_TOTAL_APPLY_AMOUNT_KEY);
      BigDecimal sumTotalApplyAmount = (BigDecimal) context.get(ActivitiesConstant.SUM_TOTAL_APPLY_AMOUNT_KEY);
      Validate.isTrue(targetTotalApplyAmount.compareTo(sumTotalApplyAmount) == 0, "累计总金额与申请的总金额不一致，请检查");
      Validate.isTrue(targetTotalApplyAmount.compareTo(BigDecimal.ZERO) > 0, "总申请金额必须大于0");
    }
  }

  private ActivityStatusEnum analysisStatus(Date startTime, Date endTime) {
    Date now = new Date();
    if (NumberUtils.compare(now.getTime(), startTime.getTime()) < 0) {
      return ActivityStatusEnum.UNEXECUTED;
    } else if (NumberUtils.compare(now.getTime(), endTime.getTime()) > 0) {
      return ActivityStatusEnum.ENDED;
    }
    return ActivityStatusEnum.EXECUTING;
  }
}
