package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanEstimationService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanSupplementOaService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollect;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.CostCenterFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketPlanCaseFieldsEnum;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.CostCenterDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.MarketPlanCaseDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.RegionCollectMainDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class MarketingPlanSupplementOaServiceImpl implements MarketingPlanSupplementOaService {

    @Autowired(required = false)
    private MarketingPlanRepository marketingPlanRepository;
    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;
    @Autowired
    private MarketingPlanEstimationService marketingPlanEstimationService;
    @Autowired(required = false)
    private PushOaService pushOaService;
    @Autowired
    private UserVoService userVoService;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Value("${domain-name:}")
    private String domainName;
    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;
    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private CostTypeCategoryVoService costTypeCategoryVoService;

    /**
     * 推送OA
     *
     * @param
     * @return
     */
    @Override
    public JSONObject pushOa(MarketingPlanVo order) {
        List<MarketingPlanCaseVo> caseVoList = marketingPlanCaseService.findBySchemeCode(order.getSchemeCode());

        order.setBusinessCode(order.getSchemeCode());
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(order));
        List<MarketingPlanCaseVo> detailCaseList = marketingPlanCaseService.findBySchemeCode(order.getSchemeCode());
        Set<String> orgCodeList = detailCaseList.stream().map(MarketingPlanCaseVo::getBearDepartmentCode).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            orderJsonObject.put("deptCode", oaOrgCodeSet.stream().collect(Collectors.joining(",")));
        }
        orderJsonObject.put("title", (BooleanEnum.TRUE.getCapital().equals(order.getNewCustomerFlag()) ? "新开客户方案：" : "往期费用补录：") + order.getSchemeName());
        orderJsonObject.put("falx", BooleanEnum.TRUE.getCapital().equals(order.getNewCustomerFlag()) ? "0" : "1");
        orderJsonObject.put("collectCode", order.getSchemeCode());
        BigDecimal marketingBudgetAmount = BigDecimal.ZERO;
        BigDecimal estimateAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(caseVoList)) {
            BigDecimal sumApplyAmount = caseVoList.stream().map(e -> e.getApplyAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            marketingBudgetAmount = sumApplyAmount.divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP);
        }
        if (CollectionUtils.isNotEmpty(order.getSalesPlanList())) {
            BigDecimal sumEstimatedCost = order.getSalesPlanList().stream().map(e -> e.getEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
            estimateAmount = sumEstimatedCost.divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP);
        }
        orderJsonObject.put("marketingBudgetAmount", marketingBudgetAmount);
        orderJsonObject.put("estimateAmount", estimateAmount);
        orderJsonObject.put("remark", order.getSchemeDesc());
        orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.TPM_MARKET_PLAN_SUPPLEMENT.getUrlCode() + "?code=" + order.getSchemeCode());

        // 主表
        // 业务类型
        String businessType = MqConstant.TAG_TPM_MARKET_PLAN_SUPPLEMENT;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");

        List<JSONArray> details = Lists.newArrayList();
        //查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
            caseVoList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()))
                    .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
        }
        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        Set<String> bearDepartmentNameSet = new HashSet<>();
        caseVoList.stream().filter(k -> StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                .collect(Collectors.groupingBy(e -> e.getCostCenterCode() + e.getCostTypeCategorySecondName(), Collectors.toList())).forEach((key, list) -> {
            CostCenterDetailDto detailDto = new CostCenterDetailDto();
            detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
            detailDto.setCostCenterName(list.get(0).getCostCenterName());
            List<OrgOaOrgVo> orgOaOrgVos = orgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
            if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
            } else {
                detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
            }
            detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
            detailDto.setBearAmount(list.stream().map(MarketingPlanCaseVo::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            costCenterDetailDtoList.add(detailDto);
        });
        Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);
        details.add(JSONArray.parseArray(JSON.toJSONString(costCenterDetailDtoList)));

        List<MarketPlanCaseDetailDto> marketPlanCaseDetailDtoList = Lists.newArrayList();
        caseVoList.forEach(e -> {
            MarketPlanCaseDetailDto detailDto = new MarketPlanCaseDetailDto();
            detailDto.setFysybm(e.getBelongDepartmentName());
            detailDto.setKhmc(e.getCustomerName());
            detailDto.setMdmc(e.getTerminalName());
            detailDto.setFyxm(e.getDetailName());
            detailDto.setFygzyf(e.getYears());
            detailDto.setSqje(e.getApplyAmount());
            detailDto.setFaghmc(e.getSchemeName());
            detailDto.setHdms(e.getActDesc());
            detailDto.setFamxbm(e.getSchemeDetailCode());
            detailDto.setCbzx(e.getCostCenterName());
            detailDto.setXzjghtybzs(orgOaOrgVoMap.get(e.getBearDepartmentCode()).get(0).getOaOrgCode());
            marketPlanCaseDetailDtoList.add(detailDto);
        });
        details.add(JSONArray.parseArray(JSON.toJSONString(marketPlanCaseDetailDtoList)));

        JSONObject response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, details, workflowId, workflowName,
                mainTableMethod, detailTableMethod1, detailTableMethod2);
        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        if (response.containsKey("code")) {
            Integer resultCode = response.getInteger("code");
            if (resultCode == 100) {
                JSONObject ja = response.getJSONObject("data");
                MarketingPlan marketingPlan = marketingPlanRepository.queryById(order.getId());
                marketingPlan.setProcessNumber(ja.getString("out"));
                marketingPlan.setProcessDate(new Date());
                marketingPlan.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
                marketingPlan.setOaId(response.getString("oaId"));
                marketingPlan.setOaUserName(response.getString("oaUserName"));
                marketingPlanRepository.updateById(marketingPlan);
            } else {
                Validate.isTrue(false, "OA流程提交失败，错误信息：" + response.getString("msg"));
            }
        }

        return null;
    }

    /**
     * 重新提交OA
     *
     * @param
     * @return
     */
    @Override
    public JSONObject resubmitOa(MarketingPlanVo order) {
        List<MarketingPlanCaseVo> caseVoList = marketingPlanCaseService.findBySchemeCode(order.getSchemeCode());
        order.setBusinessCode(order.getSchemeCode());

        OaResubmitDto dto = new OaResubmitDto();

        // 获取当前登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType = MqConstant.TAG_TPM_MARKET_PLAN_SUPPLEMENT;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        order.setTpmUrl(domainName + TpmOaPageEnum.TPM_MARKET_PLAN_SUPPLEMENT.getUrlCode() + "?code=" + order.getSchemeCode());
        dto.setRequestName((BooleanEnum.TRUE.getCapital().equals(order.getNewCustomerFlag()) ? "新开客户方案：" : "往期费用补录：") + order.getSchemeName());
        Set<String> orgCodeList = caseVoList.stream().map(MarketingPlanCaseVo::getBearDepartmentCode).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            order.setDeptCode(String.join(",", oaOrgCodeSet));
        }
        dto.setBusinessCode(businessType);
        dto.setRequestId(order.getProcessNumber());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        order.setFalx(BooleanEnum.TRUE.getCapital().equals(order.getNewCustomerFlag()) ? "0" : "1");
        order.setCollectCode(order.getSchemeCode());
        order.setRemark(order.getSchemeDesc());
        BigDecimal marketingBudgetAmount = BigDecimal.ZERO;
        BigDecimal estimateAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(caseVoList)) {
            BigDecimal sumApplyAmount = caseVoList.stream().map(e -> e.getApplyAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            marketingBudgetAmount = sumApplyAmount.divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP);
        }
        if (CollectionUtils.isNotEmpty(order.getSalesPlanList())) {
            BigDecimal sumEstimatedCost = order.getSalesPlanList().stream().map(e -> e.getEstimatedCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
            estimateAmount = sumEstimatedCost.divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP);
        }
        order.setMarketingBudgetAmount(marketingBudgetAmount);
        order.setEstimateAmount(estimateAmount);

        RegionCollectMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(order, RegionCollectMainDto.class, LinkedHashSet.class, ArrayList.class);
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));
        //查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
            caseVoList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()))
                    .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
        }
        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        Set<String> bearDepartmentNameSet = new HashSet<>();
        caseVoList.stream().filter(k -> StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                .collect(Collectors.groupingBy(e -> e.getCostCenterCode() + e.getCostTypeCategorySecondName(), Collectors.toList())).forEach((key, list) -> {
            CostCenterDetailDto detailDto = new CostCenterDetailDto();
            detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
            detailDto.setCostCenterName(list.get(0).getCostCenterName());
            List<OrgOaOrgVo> orgOaOrgVos = orgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
            if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
            } else {
                detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
            }
            detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
            detailDto.setBearAmount(list.stream().map(MarketingPlanCaseVo::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            costCenterDetailDtoList.add(detailDto);
        });
        Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);

        List<MarketPlanCaseDetailDto> marketPlanCaseDetailDtoList = Lists.newArrayList();
        caseVoList.forEach(e -> {
            MarketPlanCaseDetailDto detailDto = new MarketPlanCaseDetailDto();
            detailDto.setFysybm(e.getBelongDepartmentName());
            detailDto.setKhmc(e.getCustomerName());
            detailDto.setMdmc(e.getTerminalName());
            detailDto.setFyxm(e.getDetailName());
            detailDto.setFygzyf(e.getYears());
            detailDto.setSqje(e.getApplyAmount());
            detailDto.setFaghmc(e.getSchemeName());
            detailDto.setHdms(e.getActDesc());
            detailDto.setFamxbm(e.getSchemeDetailCode());
            detailDto.setCbzx(e.getCostCenterName());
            detailDto.setXzjghtybzs(orgOaOrgVoMap.get(e.getBearDepartmentCode()).get(0).getOaOrgCode());
            marketPlanCaseDetailDtoList.add(detailDto);
        });

        OaDetailDto costCenterDetailDto = new OaDetailDto();
        costCenterDetailDto.setDetailList(JSONUtil.toJsonStr(costCenterDetailDtoList));
        costCenterDetailDto.setDetailClass(CostCenterDetailDto.class);
        OaDetailDto marketPlanCaseDetailDto = new OaDetailDto();
        marketPlanCaseDetailDto.setDetailList(JSONUtil.toJsonStr(marketPlanCaseDetailDtoList));
        marketPlanCaseDetailDto.setDetailClass(MarketPlanCaseDetailDto.class);
        dto.setDetailList(Arrays.asList(costCenterDetailDto, marketPlanCaseDetailDto));

        if (ryOaProcessService.resubmit(dto)) {
            MarketingPlan marketingPlan = marketingPlanRepository.queryById(order.getId());
            marketingPlan.setProcessDate(new Date());
            marketingPlan.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
            marketingPlanRepository.updateById(marketingPlan);
        }
        return null;
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public boolean oaWithdraw(String code, String remark) {
        MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, code);
        Validate.isTrue(entity.getProcessStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(entity.getProcessNumber()));
        dto.setProcessCreateId(entity.getOaId());
        dto.setUserName(entity.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }



    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (RegionCollectFieldsEnum value : RegionCollectFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod1 = (tableDetailFields, detailJsonObject) -> {
        for (CostCenterFieldsEnum value : CostCenterFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod2 = (tableDetailFields, detailJsonObject) -> {
        for (MarketPlanCaseFieldsEnum value : MarketPlanCaseFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
}
