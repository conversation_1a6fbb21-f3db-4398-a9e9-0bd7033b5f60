package com.biz.crm.tpm.business.activities.materialdemand.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandDetail;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandQueryVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandReportVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:35
 */
public interface MaterialDemandDetailService {

    List<MaterialDemandDetailVo> findListByCodes(List<String> codes);

    void saveBatchList(List<MaterialDemandDetailVo> demandDetailVos, String demandCode);

    void deleteByDemandCodeList(List<String> demandCodeList);

    List<MaterialDemandDetailVo> findListById(String id);

    List<MaterialDemandDetailVo> checkDetails(List<MaterialDemandDetailVo> voList);

    List<MaterialDemandDetailVo> findListByDemandDetailCodes(List<String> demandDetailCodes);

    Page<MaterialDemandReportVo> findMaterialDemandReportList(Pageable pageable, MaterialDemandReportVo vo);

    Page<MarketingPlanCaseVo> queryDetailList(Pageable pageable, MaterialDemandQueryVo vo);

    void checkMaterialDemandNum(List<MaterialDemandDetailVo> detailVos, String schemeCode);
}
