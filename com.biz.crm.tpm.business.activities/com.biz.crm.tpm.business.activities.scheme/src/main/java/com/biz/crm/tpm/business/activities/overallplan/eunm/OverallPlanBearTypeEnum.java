package com.biz.crm.tpm.business.activities.overallplan.eunm;

import liquibase.pro.packaged.B;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum OverallPlanBearTypeEnum {

    head("head", "总部承担"),
    region("region", "大区承担"),
    ;

    private String code;

    private String desc;

    public static String checkName(String name) {
        String code = null;
        for (OverallPlanBearTypeEnum value : OverallPlanBearTypeEnum.values()) {
            if (value.getDesc().equals(name)) {
                code = value.getCode();
                break;
            }
        }
        return code;
    }

    public static OverallPlanBearTypeEnum findByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for(OverallPlanBearTypeEnum type : values()){
            if(StringUtils.equals(type.getCode(),code)){
                return type;
            }
        }
        return null;
    }
}
