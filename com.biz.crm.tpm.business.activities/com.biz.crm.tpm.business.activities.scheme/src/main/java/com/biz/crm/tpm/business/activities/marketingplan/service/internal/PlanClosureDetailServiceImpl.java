package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.activities.marketingplan.constant.PlanClosureConstant;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosure;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosureDetail;
import com.biz.crm.tpm.business.activities.marketingplan.repository.PlanClosureDetailRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanClosureDetailService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureDetailVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:23
 */
@Service
@Transactional
public class PlanClosureDetailServiceImpl implements PlanClosureDetailService {

    @Resource
    private PlanClosureDetailRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private GenerateCodeService generateCodeService;

    @Override
    public List<PlanClosureDetailVo> findListByCloseCodes(List<String> closeCodes) {
        List<PlanClosureDetail> details = repository.findListByCloseCodes(closeCodes);
        if (CollectionUtils.isEmpty(details)) {
            return Lists.newArrayList();
        }
        List<PlanClosureDetailVo> list = Lists.newArrayList();
        for (PlanClosureDetail vo : details) {
            JSONObject object = JSONObject.parseObject(vo.getJsonStr());
            vo.setJsonStr(null);
            PlanClosureDetailVo detailVo = nebulaToolkitService.copyObjectByBlankList(vo, PlanClosureDetailVo.class, HashSet.class, ArrayList.class);
            detailVo.putAll(object);
            list.add(detailVo);
        }
        return list;
    }

    /**
     * 保存明细
     *
     * @param detailVoList
     * @param closeCode
     */
    @Override
    public void saveBatchList(List<PlanClosureDetailVo> detailVoList, String closeCode) {
        List<String> detailCodes = generateCodeService.generateCode(PlanClosureConstant.DETAIL_CODE_RULE, detailVoList.size());
        Integer count = 0;
        for (PlanClosureDetailVo detailVo : detailVoList) {
            String jsonStr = JSONObject.toJSONString(detailVo);
            detailVo.setJsonStr(jsonStr);
            detailVo.setCloseCode(closeCode);
            detailVo.setCloseDetailCode(detailCodes.get(count++));
        }
        List<PlanClosureDetail> detailList = (List<PlanClosureDetail>) nebulaToolkitService.copyCollectionByWhiteList(detailVoList, PlanClosureDetailVo.class, PlanClosureDetail.class, HashSet.class, ArrayList.class);
        repository.saveBatch(detailList);
    }


    /**
     * 删除
     *
     * @param closeCodes
     */
    @Override
    public void deleteByCloseCodes(List<String> closeCodes) {
        repository.deleteByCloseCodes(closeCodes);
    }
}
