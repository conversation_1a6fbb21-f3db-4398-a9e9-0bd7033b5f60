package com.biz.crm.tpm.business.activities.overallplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum OverallPlanCaseFieldsEnum {
    secondCostCategoryName("secondCostCategoryName", "secondCostCategoryName", "二级费用大类", "String"),
    bearDepartmentName("bearDepartmentName", "bearDepartmentName", "承接销售部门名称", "String"),
    bearType("bearType", "bearType", "承担类型", "String"),
    estimatedCost("applyAmount", "estimatedCost", "预估费用", "BigDecimal"),
    cashCondition("cashCondition", "cashCondition", "兑付条件及要求说明", "String"),
    ;

    private String key;
    private String dictCode;
    private String value;
    private String dateType;

    public static OverallPlanCaseFieldsEnum findByCode(String code) {
        Optional<OverallPlanCaseFieldsEnum> first = Stream.of(OverallPlanCaseFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}