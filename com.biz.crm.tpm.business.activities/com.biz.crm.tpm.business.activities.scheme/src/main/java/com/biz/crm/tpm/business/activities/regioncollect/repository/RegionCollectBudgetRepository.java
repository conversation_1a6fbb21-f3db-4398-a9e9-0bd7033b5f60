package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectBudget;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectBudgetMapper;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/30 18:02
 */
@Component
public class RegionCollectBudgetRepository extends ServiceImpl<RegionCollectBudgetMapper, RegionCollectBudget> {

    public void deleteByCollectCode(String collectCode) {
        this.lambdaUpdate()
                .in(RegionCollectBudget::getCollectCode, collectCode)
                .remove();
    }

    public List<RegionCollectBudget> findByCollectCode(String collectCode) {
        return this.lambdaQuery()
                .eq(RegionCollectBudget::getCollectCode, collectCode)
                .list();
    }


    public List<RegionCollectBudgetVo> findCollectByCollectCode(String collectCode){
        return this.baseMapper.findCollectByCollectCode(collectCode);
    }

}
