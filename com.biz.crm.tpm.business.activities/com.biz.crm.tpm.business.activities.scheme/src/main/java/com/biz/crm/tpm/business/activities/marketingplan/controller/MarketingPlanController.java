package com.biz.crm.tpm.business.activities.marketingplan.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.marketingplan.constant.MarketingPlanConstant;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.*;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 17:32
 */
@RestController
@RequestMapping("/v1/marketingPlanController")
@Api(tags = "营销规划方案")
@Slf4j
public class MarketingPlanController extends BusinessPageCacheController<MarketingPlanCaseVo, MarketingPlanCaseVo> {


    @Autowired
    private MarketingPlanService marketingPlanService;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired
    private LoginUserService loginUserService;

    @Autowired
    private UserVoService userVoService;

    @Autowired
    private OrgVoService orgVoService;


    @GetMapping("findList")
    @ApiOperation(value = "查询分页列表")
    public Result<Page<MarketingPlanVo>> findList(@PageableDefault(50) Pageable pageable, MarketingPlanVo vo) {
//        vo.setIsChange(BooleanEnum.FALSE.getCapital());
        return Result.ok(marketingPlanService.findList(pageable, vo));
    }


    @GetMapping("queryById")
    @ApiOperation(value = "查询详情")
    public Result<MarketingPlanVo> queryById(@RequestParam(required = false, value = "id") String id, @RequestParam(required = false, value = "schemeCode") String schemeCode) {
        return Result.ok(marketingPlanService.queryDetails(id, schemeCode));
    }

    /*****-------------------------------------------------------------------------           TODO 新增方案         -------------------------------------------------------------------------*****/

    @ApiOperation(value = "暂存-新增/编辑")
    @PostMapping("stagingSaveOrUpdate")
    public Result stagingSaveOrUpdate(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.plan.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.FALSE, Boolean.FALSE);
        } else {
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.FALSE, Boolean.FALSE);
        }
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }

    @PostMapping("saveOrUpdate")
    @ApiOperation(value = "新增/编辑")
    public Result saveOrUpdate(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.plan.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.TRUE, Boolean.FALSE);
        } else {
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.TRUE, Boolean.FALSE);
        }
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }


    /*****-------------------------------------------------------------------------           TODO O2O方案          -------------------------------------------------------------------------*****/

    @PostMapping("stagingSaveOrUpdateOTwoO")
    @ApiOperation(value = "暂存-新增/编辑o2o")
    public Result stagingSaveOrUpdateOTwoO(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.o_two_o.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.FALSE, Boolean.FALSE);
        } else {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.FALSE, Boolean.FALSE);
        }
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }

    @PostMapping("saveOrUpdateOTwoO")
    @ApiOperation(value = "新增/编辑o2o")
    public Result saveOrUpdateOTwoO(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.o_two_o.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.TRUE, Boolean.FALSE);
        } else {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.TRUE, Boolean.FALSE);
        }
        try {
            //计算客户损益
            marketingPlanService.calCustomerGains(data.getSchemeCode());
            //单方案-营销测算
            marketingPlanService.calMarketingPlanSchemeEstimation(data.getSchemeCode());
            //计算营销测算
            marketingPlanService.calMarketingPlanEstimationOTwoO(data.getSchemeCode());
            //计算品相测算
            marketingPlanService.calMarketingPlanItemEstimation(data.getSchemeCode());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("保存方案成功,但计算客户损益/营销测算失败");
        } finally {
            //重新加载
            marketingPlanService.findCacheList(vo.getCacheKey());
        }
        return queryById(data.getId(), data.getSchemeCode());
    }


    @PostMapping("saveOrUpdateO2OJumpApproval")
    @ApiOperation(value = "新增/编辑o2o并跳转审批")
    public Result saveOrUpdateO2OJumpApproval(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.o_two_o.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.TRUE, Boolean.FALSE);
        } else {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.TRUE, Boolean.FALSE);
        }
        //计算客户损益
        marketingPlanService.calCustomerGains(data.getSchemeCode());
        //单方案-营销测算
        marketingPlanService.calMarketingPlanSchemeEstimation(data.getSchemeCode());
        //计算营销测算
        marketingPlanService.calMarketingPlanEstimationOTwoO(data.getSchemeCode());
        //计算品相测算
        marketingPlanService.calMarketingPlanItemEstimation(data.getSchemeCode());
        //匹配预算管控规则
        marketingPlanService.matchControlBudget(data.getSchemeCode());
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }

    @PostMapping("submitSaveOrUpdateOTwoO")
    @ApiOperation(value = "提交-新增/编辑o2o")
    public Result submitSaveOrUpdateOTwoO(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        //校验预算
        Map<String, String> map = marketingPlanService.checkControlBudget(vo);
        if (ObjectUtils.isNotEmpty(map)) {
            return Result.ok(map);
        }
        MarketingPlanVo data = null;
        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.o_two_o.getCode());
            data = marketingPlanService.submitOTwoOCreate(vo, Boolean.FALSE);
        } else {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.submitOTwoOupdate(vo, Boolean.FALSE);
        }
        return Result.ok(data);
    }


    /*****-------------------------------------------------------------------------           TODO 方案补录          -------------------------------------------------------------------------*****/


    @PostMapping("stagingSaveOrUpdateAdditional")
    @ApiOperation(value = "暂存-新增/编辑方案补录")
    public Result stagingSaveOrUpdateAdditional(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        MarketingPlanVo data = null;
        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.additional.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.FALSE, Boolean.FALSE);
        } else {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.FALSE, Boolean.FALSE);
        }
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }

    @PostMapping("saveOrUpdateAdditionals")
    @ApiOperation(value = "新增/编辑方案补录")
    public Result saveOrUpdateAdditionals(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.additional.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.TRUE, Boolean.FALSE);
        } else {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.TRUE, Boolean.FALSE);
        }
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }


    @PostMapping("saveOrUpdateAdditionalJumpApproval")
    @ApiOperation(value = "新增/编辑方案补录并跳转审批")
    public Result saveOrUpdateAdditionalJumpApproval(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.additional.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.TRUE, Boolean.FALSE);
        } else {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.TRUE, Boolean.FALSE);
        }
        //计算客户损益
        marketingPlanService.calCustomerGains(data.getSchemeCode());
        //计算营销测算
        marketingPlanService.calMarketingPlanEstimationAdditional(data.getSchemeCode());
        //匹配预算管控规则
        marketingPlanService.matchControlBudget(data.getSchemeCode());
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }

    @PostMapping("submitSaveOrUpdateAdditionals")
    @ApiOperation(value = "提交-新增/编辑方案补录")
    public Result submitSaveOrUpdateAdditionals(@RequestBody MarketingPlanVo vo) {

        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        //校验预算
        Map<String, String> map = marketingPlanService.checkControlBudget(vo);
        if (ObjectUtils.isNotEmpty(map)) {
            return Result.ok(map);
        }
        MarketingPlanVo data = null;
        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.additional.getCode());
            data = marketingPlanService.submitOTwoOCreate(vo, Boolean.FALSE);
        } else {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.submitOTwoOupdate(vo, Boolean.FALSE);
        }
        return Result.ok(data);
    }


    /*****-------------------------------------------------------------------------           TODO 方案追加          -------------------------------------------------------------------------*****/

    @PostMapping("stagingSaveOrUpdateAppend")
    @ApiOperation(value = "暂存-新增/编辑-追加")
    public Result stagingSaveOrUpdateAppend(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        MarketingPlanVo data = null;
        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.append.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.FALSE, Boolean.FALSE);
        } else {
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.FALSE, Boolean.FALSE);
        }
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }

    @PostMapping("saveOrUpdateAppend")
    @ApiOperation(value = "新增/编辑-追加")
    public Result saveOrUpdateAppend(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        MarketingPlanVo data = null;
        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.append.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.TRUE, Boolean.FALSE);
        } else {
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.TRUE, Boolean.FALSE);
        }
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }


    @PostMapping("submitSaveOrUpdateAppend")
    @ApiOperation(value = "新增/编辑提交-追加")
    public Result submitSaveOrUpdateAppend(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");

        //重新计算明细
        this.saveOrUpdateCalPlanCaseList(vo.getCacheKey(), vo);

        //先进行保存 主要是为了存预算信息
        MarketingPlanVo data = null;
        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setSchemeType(MarketingPlanSchemeTypeEnum.append.getCode());
            data = marketingPlanService.createPlan(vo, Boolean.TRUE, Boolean.FALSE);
        } else {
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            data = marketingPlanService.updatePlan(vo, Boolean.TRUE, Boolean.FALSE);
        }
        vo.setSchemeCode(data.getSchemeCode());
        vo.setId(data.getId());

        //校验预算
        Map<String, String> map = marketingPlanService.checkControlBudget(vo);
        if (ObjectUtils.isNotEmpty(map)) {
            return Result.ok(map);
        }
        vo.setSchemeType(MarketingPlanSchemeTypeEnum.append.getCode());
        if (ObjectUtils.isEmpty(vo.getId())) {
            marketingPlanService.submitCreate(vo, Boolean.FALSE);
        } else {
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            String redisLockKey = MarketingPlanConstant.getRedisLockKey(vo.getId());
            Boolean lockFlag = redisLockService.tryLock(redisLockKey, TimeUnit.MINUTES, MarketingPlanConstant.MARKETING_PLAN_LOCK_TIME_20);
            Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
            try {
                marketingPlanService.submitUpdate(vo, Boolean.FALSE);
            } finally {
                redisLockService.unlock(redisLockKey);
            }
        }
        return Result.ok();
    }


    @DeleteMapping("deleteBatch")
    @ApiOperation(value = "批量删除")
    public Result<?> deleteBatch(@RequestParam List<String> ids) {
        String errMsg = marketingPlanService.deleteBatch(ids);
        if (ObjectUtils.isNotEmpty(errMsg)) {
            return Result.error(errMsg);
        }
        return Result.ok("删除成功!");
    }


    @ApiOperation(value = "取消确认")
    @PostMapping("cancelConfirm")
    public Result cancelConfirm(@RequestBody List<String> idList) {
        String errMsg = marketingPlanService.cancelConfirm(idList);
        if (ObjectUtils.isNotEmpty(errMsg)) {
            return Result.error(errMsg);
        }
        return Result.ok();
    }


    /*****-------------------------------------------------------------------------           TODO 方案变更          -------------------------------------------------------------------------*****/

    @ApiOperation(value = "暂存-方案变更新增/编辑")
    @PostMapping("stagingSaveOrUpdateChangeScheme")
    public Result stagingSaveOrUpdateChangeScheme(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;
        Validate.notNull(vo.getOriginalSchemeCode(), "原始方案编码不能为空");
        vo.setSchemeType(MarketingPlanSchemeTypeEnum.change.getCode());
        String lockKey = MarketingPlanConstant.getMarketingChangeSchemeLock(vo.getOriginalSchemeCode());
        Boolean schemeLockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, MarketingPlanConstant.MARKETING_PLAN_LOCK_TIME_20);
        try {
            Validate.isTrue(schemeLockFlag, String.format("当前方案%s正在变更,请稍等重试!", vo.getOriginalSchemeCode()));
            if (ObjectUtils.isEmpty(vo.getId())) {
                data = marketingPlanService.createPlan(vo, Boolean.FALSE, Boolean.TRUE);
            } else {
                String redisLockKey = MarketingPlanConstant.getRedisLockKey(vo.getId());
                Boolean lockFlag = redisLockService.tryLock(redisLockKey, TimeUnit.MINUTES, MarketingPlanConstant.MARKETING_PLAN_LOCK_TIME_20);
                Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
                try {
                    data = marketingPlanService.updatePlan(vo, Boolean.FALSE, Boolean.TRUE);
                } finally {
                    redisLockService.unlock(redisLockKey);
                }
            }
        } finally {
            redisLockService.unlock(lockKey);
        }
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }


    @ApiOperation(value = "方案变更新增/编辑")
    @PostMapping("saveOrUpdateChangeScheme")
    public Result saveOrUpdateChangeScheme(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;
        Validate.notNull(vo.getOriginalSchemeCode(), "原始方案编码不能为空");
        vo.setSchemeType(MarketingPlanSchemeTypeEnum.change.getCode());
        String lockKey = MarketingPlanConstant.getMarketingChangeSchemeLock(vo.getOriginalSchemeCode());
        Boolean schemeLockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, MarketingPlanConstant.MARKETING_PLAN_LOCK_TIME_20);
        try {
            Validate.isTrue(schemeLockFlag, String.format("当前方案%s正在变更,请稍等重试!", vo.getOriginalSchemeCode()));
            if (ObjectUtils.isEmpty(vo.getId())) {
                data = marketingPlanService.createPlan(vo, Boolean.TRUE, Boolean.TRUE);
            } else {
                String redisLockKey = MarketingPlanConstant.getRedisLockKey(vo.getId());
                Boolean lockFlag = redisLockService.tryLock(redisLockKey, TimeUnit.MINUTES, MarketingPlanConstant.MARKETING_PLAN_LOCK_TIME_20);
                Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
                try {
                    data = marketingPlanService.updatePlan(vo, Boolean.TRUE, Boolean.TRUE);
                } finally {
                    redisLockService.unlock(redisLockKey);
                }
            }
        } finally {
            redisLockService.unlock(lockKey);
        }
        //重新加载
        marketingPlanService.findCacheList(vo.getCacheKey());
        return queryById(data.getId(), data.getSchemeCode());
    }


    @ApiOperation(value = "变更跳转审批")
    @PostMapping("changeSchemeJumpApproval")
    public Result changeSchemeJumpApproval(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        MarketingPlanVo data = null;
        Validate.notNull(vo.getOriginalSchemeCode(), "原始方案编码不能为空");
        vo.setSchemeType(MarketingPlanSchemeTypeEnum.change.getCode());
        String lockKey = MarketingPlanConstant.getMarketingChangeSchemeLock(vo.getOriginalSchemeCode());
        Boolean schemeLockFlag = redisLockService.tryLock(lockKey, TimeUnit.MINUTES, MarketingPlanConstant.MARKETING_PLAN_LOCK_TIME_20);
        try {
            Validate.isTrue(schemeLockFlag, String.format("当前方案%s正在变更,请稍等重试!", vo.getOriginalSchemeCode()));
            if (ObjectUtils.isEmpty(vo.getId())) {
                data = marketingPlanService.createPlan(vo, Boolean.TRUE, Boolean.TRUE);
            } else {
                String redisLockKey = MarketingPlanConstant.getRedisLockKey(vo.getId());
                Boolean lockFlag = redisLockService.tryLock(redisLockKey, TimeUnit.MINUTES, MarketingPlanConstant.MARKETING_PLAN_LOCK_TIME_20);
                Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
                try {
                    data = marketingPlanService.updatePlan(vo, Boolean.TRUE, Boolean.TRUE);
                } finally {
                    redisLockService.unlock(redisLockKey);
                }
            }
        } finally {
            redisLockService.unlock(lockKey);
        }
        try {
            //计算客户损益预测
            marketingPlanService.calCustomerGains(data.getSchemeCode());
            //营销测算
            marketingPlanService.calMarketingPlanEstimation(data.getSchemeCode());
            //计算品相测算
            marketingPlanService.calMarketingPlanItemEstimation(data.getSchemeCode());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("计算客户损益预测/营销测算失败,失败原因:" + e.getMessage());
        } finally {
            //重新加载
            marketingPlanService.findCacheList(vo.getCacheKey());
        }
        return this.queryById(null, data.getSchemeCode());
    }


    @PostMapping("submitSaveOrUpdateChangeScheme")
    @ApiOperation(value = "提交方案变更新增/编辑")
    public Result submitSaveOrUpdateChangeScheme(@RequestBody MarketingPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
        Validate.notNull(vo.getOriginalSchemeCode(), "原始方案编码不能为空");
        vo.setSchemeType(MarketingPlanSchemeTypeEnum.change.getCode());
        //校验预算
        Map<String, String> map = marketingPlanService.checkChangeControlBudget(vo);
        if (ObjectUtils.isNotEmpty(map)) {
            return Result.ok(map);
        }
        if (ObjectUtils.isEmpty(vo.getId())) {
            marketingPlanService.submitCreate(vo, Boolean.TRUE);
        } else {
            String redisLockKey = MarketingPlanConstant.getRedisLockKey(vo.getId());
            Boolean lockFlag = redisLockService.tryLock(redisLockKey, TimeUnit.MINUTES, MarketingPlanConstant.MARKETING_PLAN_LOCK_TIME_20);
            Validate.isTrue(lockFlag, "业务数据正在操作,请勿重复点击");
            try {
                marketingPlanService.submitUpdate(vo, Boolean.TRUE);
            } finally {
                redisLockService.unlock(redisLockKey);
            }
        }
        return Result.ok();
    }


    /**
     * 流程撤回
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "流程撤回")
    @GetMapping("recover")
    public Result<?> recover(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code, @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
        try {
            this.marketingPlanService.recover(code, remark);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "计算营销测算表-方案变更")
    @GetMapping("calMarketingPlanEstimation")
    public Result<List<MarketingPlanEstimationVo>> calMarketingPlanEstimation(@RequestParam String schemeCode) {
        return Result.ok(marketingPlanService.calMarketingPlanEstimation(schemeCode));
    }


    @ApiOperation(value = "计算营销测算表-O2O")
    @GetMapping("calMarketingPlanEstimationOTwoO")
    public Result<List<MarketingPlanEstimationVo>> calMarketingPlanEstimationOTwoO(@RequestParam String schemeCode) {
        return Result.ok(marketingPlanService.calMarketingPlanEstimationOTwoO(schemeCode));
    }

    @ApiOperation(value = "计算营销测算表-方案补录")
    @GetMapping("calMarketingPlanEstimationAdditional")
    public Result<List<MarketingPlanEstimationVo>> calMarketingPlanEstimationAdditional(@RequestParam String schemeCode) {
        return Result.ok(marketingPlanService.calMarketingPlanEstimationAdditional(schemeCode));
    }

    @ApiOperation(value = "保存当前页数据到缓存并返回指定页数据接口")
    @PostMapping("saveCurrentPageCache")
    @Override
    public Result<Page<MarketingPlanCaseVo>> saveCurrentPageCache(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable, @ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey, @ApiParam(name = "dto", value = "查询实体") MarketingPlanCaseVo dto, @ApiParam(value = "当前页数据") @RequestBody List<MarketingPlanCaseVo> saveList) {

        String years = null;
        String schemeCode = null;
        String originalSchemeCode = null;
        if (ObjectUtils.isNotEmpty(dto)) {
            Validate.notNull(dto.getYears(), "年月不能为空");
            years = dto.getYears();
            schemeCode = ObjectUtils.defaultIfNull(dto.getSchemeCode(), null);
            originalSchemeCode = ObjectUtils.defaultIfNull(dto.getOriginalSchemeCode(), null);
        }
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        UserVo detail = userVoService.findDetailById(userVo.getId());
        List<String> userOrgCodes = com.google.common.collect.Lists.newArrayList();
        detail.getPositionList().forEach(e -> {
            Set<String> orgCodes = e.getOrgCodes();
            if (!org.springframework.util.CollectionUtils.isEmpty(orgCodes)) {
                userOrgCodes.addAll(orgCodes);
            }
        });
        log.info("saveCurrentPageCache.userOrgCodes: {}", JSON.toJSONString(userOrgCodes));
        List<String> loginUserCodes = com.google.common.collect.Lists.newArrayList();
        if (!org.springframework.util.CollectionUtils.isEmpty(userOrgCodes)) {
            List<OrgVo> orgVos = orgVoService.findByOrgCodes(userOrgCodes);
            if (!org.springframework.util.CollectionUtils.isEmpty(orgVos)) {
                orgVos.forEach(e -> {
                    List<OrgVo> nextLevelOrgs = orgVoService.findByRuleCodeLike(e.getRuleCode());
                    if (!org.springframework.util.CollectionUtils.isEmpty(nextLevelOrgs)) {
                        loginUserCodes.addAll(nextLevelOrgs.stream().map(OrgVo::getOrgCode).collect(Collectors.toList()));
                    }
                });
            }
        }

        log.info("当前登录用户组织及下级信息：{}", loginUserCodes);

        try {
            this.marketingPlanService.planCaseSaveCurrentPageCache(cacheKey, saveList, years, schemeCode, originalSchemeCode, loginUserCodes,  userVo.getUserName());
            Page<MarketingPlanCaseVo> page = this.marketingPlanService.findCachePageList(pageable, dto, cacheKey);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "回调")
    @PostMapping("callback")
    public Result callback(@RequestBody MarketingPlanVo vo) {
        marketingPlanService.callback(vo);
        return Result.ok();
    }

    @ApiOperation("查询方案明细列表-方案变更")
    @GetMapping("findCaseListByChangeScheme")
    public Result<Page<MarketingPlanCaseVo>> findCaseListByChangeScheme(@PageableDefault(50) Pageable pageable, MarketingPlanCaseVo vo,
                                                                        @RequestParam(required = false) String cacheKey) {
        Validate.notNull(vo.getOriginalSchemeCode(), "方案编码不能为空");
        vo.setChangeSchemeQueryFlag(BooleanEnum.TRUE.getCapital());
        vo.setChangeFlag(BooleanEnum.FALSE.getCapital());
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(MarketingPlanConstant.TPM_CHANGE_IGNORE_CASE_TYPE);
        List<String> excludeCaseTypeList = dictDataVos.stream().map(DictDataVo::getDictCode).distinct().collect(Collectors.toList());
        vo.setExcludeCaseTypeList(excludeCaseTypeList);

        //过滤已经选择的原方案明细
        List<String> originalSchemeDetailCodeList = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(cacheKey)) {
            List<MarketingPlanCaseVo> caseVoList = marketingPlanService.findCacheList(cacheKey);
            if (CollectionUtils.isNotEmpty(caseVoList)) {
                originalSchemeDetailCodeList = caseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOriginalSchemeDetailCode()))
                        .map(x -> x.getOriginalSchemeDetailCode()).distinct().collect(Collectors.toList());
            }
        }

        Page<MarketingPlanCaseVo> page = marketingPlanCaseService.findCaseListByChangeScheme(pageable, vo, ProcessStatusEnum.PASS.getDictCode(), originalSchemeDetailCodeList);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            for (MarketingPlanCaseVo record : page.getRecords()) {
                record.setOriginalSchemeDetailCode(record.getSchemeDetailCode());
                record.setOriginalSchemeCode(record.getSchemeCode());
                //record.setSchemeDetailCode(null);
                //record.setSchemeCode(null);
                //record.setSchemeName(null);
//                record.setId(null);
            }
        }
        return Result.ok(page);
    }

    @ApiOperation(value = "客户损益预测")
    @GetMapping("calCustomerGains")
    public Result<List<MarketingPlanGainsAndLossesVo>> calCustomerGains(@RequestParam String schemeCode) {
        //客户损益预测
        List<MarketingPlanGainsAndLossesVo> dataList = marketingPlanService.calCustomerGains(schemeCode);
        //单方案-营销测算
        marketingPlanService.calMarketingPlanSchemeEstimation(schemeCode);
        //计算品相测算
        marketingPlanService.calMarketingPlanItemEstimation(schemeCode);
        return Result.ok(dataList);
    }

    @ApiOperation(value = "单方案营销测算")
    @GetMapping("getMarketingPlanSchemeEstimation")
    public Result<List<MarketingPlanSchemeEstimationVo>> getMarketingPlanSchemeEstimation(@RequestParam String schemeCode) {
        return Result.ok(marketingPlanService.getMarketingPlanSchemeEstimation(schemeCode));
    }


    @GetMapping("findChangeGainsAndLossesCompare")
    @ApiOperation(value = "查询变更方案-损益预测")
    public Result<MarketingChangeGainsAndLossesCompareVo> findChangeGainsAndLossesCompare(@RequestParam String schemeCode) {
        return Result.ok(marketingPlanService.findChangeGainsAndLossesCompare(schemeCode));
    }

    @ApiOperation(value = "查询变更方案的客户损益对比")
    @GetMapping("findChangeGainsAndLosses")
    public Result<List<MarketPlanChangeGainsAndLossesVo>> findChangeGainsAndLosses(@RequestParam String schemeCode) {
        return Result.ok(marketingPlanService.findChangeGainsAndLosses(schemeCode));
    }


    @ApiOperation(value = "定时修改客户合作类型")
    @GetMapping("updateCustomerCooperateType")
    public Result updateCustomerCooperateType() {
        marketingPlanService.updateCustomerCooperateType();
        return Result.ok();
    }


    @GetMapping("calMarketingPlanCaseList")
    @ApiOperation(value = "计算方案明细列表")
    public Result calMarketingPlanCaseList(@ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey, @ApiParam(name = "dto", value = "查询实体") MarketingPlanVo dto) {
        this.saveOrUpdateCalPlanCaseList(cacheKey, dto);
        return Result.ok();
    }


    private void saveOrUpdateCalPlanCaseList(String cacheKey, MarketingPlanVo dto) {
        String years = null;
        String schemeCode = null;
        String originalSchemeCode = null;
        if (ObjectUtils.isNotEmpty(dto)) {
            Validate.notNull(dto.getYears(), "年月不能为空");
            years = dto.getYears();
            schemeCode = ObjectUtils.defaultIfNull(dto.getSchemeCode(), null);
            originalSchemeCode = ObjectUtils.defaultIfNull(dto.getOriginalSchemeCode(), null);
        }

        this.marketingPlanService.calMarketingPlanCaseList(cacheKey, years, schemeCode, originalSchemeCode);
    }
}
