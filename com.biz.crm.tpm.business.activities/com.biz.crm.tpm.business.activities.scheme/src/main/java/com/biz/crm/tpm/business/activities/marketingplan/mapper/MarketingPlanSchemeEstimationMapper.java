package com.biz.crm.tpm.business.activities.marketingplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanEstimation;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanSchemeEstimation;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanEstimationVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanSchemeEstimationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
public interface MarketingPlanSchemeEstimationMapper extends BaseMapper<MarketingPlanSchemeEstimation> {

    List<MarketingPlanSchemeEstimationVo> findOriginalListBySchemeCode(@Param("schemCodeList") List<String> schemCodeList);
}
