package com.biz.crm.tpm.business.activities.overallplan.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.constant.BusinessPageCacheConstant;
import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.dto.OverallPlanLogEventDto;
import com.biz.crm.tpm.business.activities.overallplan.entity.*;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanBearTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.event.OverallPlanLogEventListener;
import com.biz.crm.tpm.business.activities.overallplan.repository.*;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallCalBudgetComponent;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanCaseService;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanOaService;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanService;
import com.biz.crm.tpm.business.activities.overallplan.vo.*;
import com.biz.crm.tpm.business.activities.schemefile.service.SchemeCaseFilesService;
import com.biz.crm.tpm.business.activities.schemefile.vo.SchemeCaseFilesVo;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.activities.stagingscheme.vo.TpmStagingSchemeVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.control.sdk.enums.ControlMethodEnum;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlVoService;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mysql.cj.xdevapi.Collection;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OverallPlanServiceImpl extends BusinessPageCacheServiceImpl<OverallPlanCaseVo, OverallPlanCaseVo> implements OverallPlanService {

    @Resource
    private OverPlanRepository overPlanRepository;

    @Resource
    private OverPlanDepartmentRepository overPlanDepartmentRepository;

    @Resource
    private OverPlanProductRepository overPlanProductRepository;

    @Resource
    private OverPlanScopeRepository overPlanScopeRepository;

    @Resource
    private LoginUserService loginUserService;

    @Autowired
    private ITpmStagingSchemeService tpmStagingSchemeService;

    @Resource
    private RedisService redisService;

    @Resource
    private GenerateCodeService generateCodeService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired
    private OverallPlanCaseService overallPlanCaseService;

    @Autowired
    private OverallPlanOaService overallPlanOaService;

    @Autowired
    private OverallPlanService overallPlanService;

    @Resource
    private OrgVoService orgVoService;
    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private OverPlanBudgetRepository planBudgetRepository;

    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;

    @Autowired(required = false)
    private SchemeCaseFilesService schemeCaseFilesService;

    /**
     * 分页列表
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<OverallPlanVo> findList(Pageable pageable, OverallPlanVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<OverallPlanVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<OverallPlanVo> data = overPlanRepository.findPageList(page, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeCodes = data.getRecords().stream()
                    .filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode()))
                    .map(OverallPlanVo::getSchemeCode).collect(Collectors.toList());
            Map<String, String> departmentMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(schemeCodes)) {
                List<OverallPlanDepartment> allDepartmentList = overPlanDepartmentRepository.findListBySchemeCodeList(schemeCodes);
                List<OverallPlanDepartment> departmentList = allDepartmentList.stream().filter(x -> ObjectUtils.isEmpty(x.getSchemeDetailCode())).collect(Collectors.toList());
                departmentMap = departmentList.stream().collect(Collectors.groupingBy(OverallPlanDepartment::getSchemeCode,
                        Collectors.mapping(OverallPlanDepartment::getDepartmentName, Collectors.joining("、"))));
            }
            List<String> ids = data.getRecords().stream()
                    .filter(x -> !StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode()))
                    .map(OverallPlanVo::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                List<OverallPlanVo> overallPlanVos = tpmStagingSchemeService.getJSONStrList(new TpmStagingSchemeVo() {{
                    this.setReleaseIds(ids);
                    this.setFromTypes(Lists.newArrayList(OverallPlanSchemeTypeEnum.HEAD.getCode(), OverallPlanSchemeTypeEnum.REGION.getCode()));
                }}, OverallPlanVo.class);
                for (OverallPlanVo planVo : overallPlanVos) {
                    if (CollectionUtils.isNotEmpty(planVo.getDepartmentList())) {
                        String departmentNameStr = planVo.getDepartmentList().stream().map(OverallPlanDepartmentVo::getDepartmentName)
                                .collect(Collectors.joining("、"));
                        departmentMap.put(planVo.getSchemeCode(), departmentNameStr);
                    }
                }
            }
            for (OverallPlanVo record : data.getRecords()) {
                record.setDepartmentNameStr(departmentMap.getOrDefault(record.getSchemeCode(), null));

            }
        }
        return data;
    }


    /**
     * 查询详情
     *
     * @param id
     * @param schemeType
     * @return
     */
    @Override
    public OverallPlanVo findById(String id, String schemeType) {
        OverallPlan plan = overPlanRepository.findById(id);
        Validate.notNull(plan, "数据不存在或已被删除");
        OverallPlanVo vo = nebulaToolkitService.copyObjectByWhiteList(plan, OverallPlanVo.class, HashSet.class, ArrayList.class);
        if (StringUtils.equalsAny(plan.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode())) {
            String redisKey = OverallPlanConstant.getRedisKey(id);
            Boolean flag = redisService.hasKey(redisKey);
            if (flag) {
                String jsonStr = (String) redisService.get(redisKey);
                vo = JSONObject.parseObject(jsonStr, OverallPlanVo.class);
            } else {
                vo = tpmStagingSchemeService.getJsonStr(new TpmStagingSchemeVo() {{
                    this.setReleaseId(id);
                    this.setFromType(schemeType);
                }}, OverallPlanVo.class);
            }

        } else {
            //1.查询部门（方案归属部门、细案承接部门）
            List<OverallPlanDepartment> allDepartmentList = overPlanDepartmentRepository.findListBySchemeCodeList(Lists.newArrayList(vo.getSchemeCode()));
            List<OverallPlanDepartment> departmentList = allDepartmentList.stream().filter(x -> ObjectUtils.isEmpty(x.getSchemeDetailCode())).collect(Collectors.toList());
            vo.setDepartmentList((List<OverallPlanDepartmentVo>) nebulaToolkitService.copyCollectionByWhiteList(departmentList,
                    OverallPlanDepartment.class, OverallPlanDepartmentVo.class, HashSet.class, ArrayList.class));
            List<OverallPlanBudget> budgetList = planBudgetRepository.findBudgetListBySchemeCodes(Lists.newArrayList(vo.getSchemeCode()));
            vo.setBudgetList(budgetList);
        }
//        if (OverallPlanSchemeTypeEnum.REGION.getCode().equals(schemeType)
//                && Objects.nonNull(vo)) {
//            List<OverallPlanCase> planCaseList = overallPlanCaseService.findListByReleaseDetailNotNull(vo.getSchemeCode());
//            if (CollectionUtils.isNotEmpty(planCaseList)) {
//                List<String> releaseDetailCodes = planCaseList.stream()
//                        .map(OverallPlanCase::getHeadSchemeDetailCode).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(releaseDetailCodes)) {
//                    List<BearOverallPlanCaseVo> bearList = this.findReleaseHeadCaseList(releaseDetailCodes);
//                    Map<String, BigDecimal> caseMap = planCaseList.stream()
//                            .collect(Collectors.toMap(OverallPlanCase::getHeadSchemeDetailCode, OverallPlanCase::getApplyAmount));
//                    for (BearOverallPlanCaseVo caseVo : bearList) {
//                        caseVo.setBearAmount(caseMap.getOrDefault(caseVo.getSchemeDetailCode(), BigDecimal.ZERO));
//                    }
//                    vo.setBearList(bearList);
//                }
//            }
//        }
        if (CollectionUtils.isNotEmpty(vo.getBudgetList())) {
            Map<String, BigDecimal> budgetMap = vo.getBudgetList().stream().collect(Collectors.groupingBy(x -> x.getTrackCode(),
                    Collectors.mapping(x -> x.getApplyAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            List<OverallPlanBudget> newBudgetList = vo.getBudgetList().stream().collect(Collectors.toMap(x -> x.getTrackCode(), Function.identity(), (v1, v2) -> v1))
                    .values().stream().collect(Collectors.toList());
            for (OverallPlanBudget budget : newBudgetList) {
                BigDecimal applyAmount = budgetMap.getOrDefault(budget.getTrackCode(), BigDecimal.ZERO);
                budget.setApplyAmount(applyAmount);
                budget.setUsedAmount(applyAmount);
            }
            vo.setBudgetList(newBudgetList);
        }
        List<SchemeCaseFilesVo> filesVoList = schemeCaseFilesService.findFilesList(vo.getSchemeCode(), vo.getSchemeType());
        if (CollectionUtils.isNotEmpty(filesVoList)) {
            vo.setFilesList(filesVoList);
        }
        return vo;
    }

    @Override
    public String createOverallPlan(OverallPlanVo vo, Boolean flag) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        List<OverallPlanCaseVo> caseVoList = findCacheList(vo.getCacheKey());
        vo.setCaseList(caseVoList);
        if (redisService.hasKey(OverallPlanConstant.getRedisOverPlanBudgetKey(vo.getCacheKey()))) {
            List<OverallPlanBudget> budgetList = (List<OverallPlanBudget>) redisService.get(OverallPlanConstant.getRedisOverPlanBudgetKey(vo.getCacheKey()));
            vo.setBudgetList(budgetList);
        }
        this.validateData(vo, flag);
        //赋值登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        vo.setPositionCode(loginDetails.getPostCode());
        vo.setPositionName(loginDetails.getPostName());
        vo.setOrgCode(loginDetails.getOrgCode());
        vo.setOrgName(loginDetails.getOrgName());
        vo.setProcessStatus(ProcessStatusEnum.PREPARE.getDictCode());
        String schemeCode = null;
        if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(vo.getSchemeType())) {
            schemeCode = generateCodeService.generateCode(OverallPlanConstant.HEAD_SCHEME_CODE_RULE);
        } else {
            schemeCode = generateCodeService.generateCode(OverallPlanConstant.REGION_SCHEME_CODE_RULE);
        }
        vo.setSchemeCode(schemeCode);
        OverallPlan plan = nebulaToolkitService.copyObjectByWhiteList(vo, OverallPlan.class, HashSet.class, ArrayList.class);
        plan.setTenantCode(TenantUtils.getTenantCode());
        plan.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        plan.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        this.overallPlanService.createOverallPlanWithTransactional(vo, plan);
        return plan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOverallPlanWithTransactional(OverallPlanVo vo, OverallPlan plan) {
        overPlanRepository.save(plan);
        vo.setId(plan.getId());
        //存放redis
        String jsonStr = JSONObject.toJSONString(vo);
        String redisKey = OverallPlanConstant.getRedisKey(plan.getId());
        redisService.set(redisKey, jsonStr);
        //存放临时表
        tpmStagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
            this.setFromType(vo.getSchemeType());
            this.setReleaseId(plan.getId());
            this.setJsonStr(jsonStr);
        }});

        //日志处理
        OverallPlanLogEventDto dto = new OverallPlanLogEventDto();
        dto.setNewest(vo);
        SerializableBiConsumer<OverallPlanLogEventListener, OverallPlanLogEventDto> consumer = OverallPlanLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, OverallPlanLogEventListener.class, consumer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateOverallPlan(OverallPlanVo vo, Boolean flag) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        List<OverallPlanCaseVo> caseVoList = findCacheList(vo.getCacheKey());
        vo.setCaseList(caseVoList);
        if (redisService.hasKey(OverallPlanConstant.getRedisOverPlanBudgetKey(vo.getCacheKey()))) {
            List<OverallPlanBudget> budgetList = (List<OverallPlanBudget>) redisService.get(OverallPlanConstant.getRedisOverPlanBudgetKey(vo.getCacheKey()));
            vo.setBudgetList(budgetList);
        }
        this.validateData(vo, flag);
        Validate.notNull(vo.getId(), "主键ID不能为空");
        OverallPlan plan = overPlanRepository.findById(vo.getId());
        //历史数据 做日志处理
        OverallPlanVo oldVo = findById(plan.getId(), plan.getSchemeType());
        Validate.notNull(plan, "查询数据信息不存在或已删除");
        Validate.isTrue(StringUtils.equalsAny(plan.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode()), "不是待提交/驳回/追回状态不可编辑");
        String schemeCode = plan.getSchemeCode();
        vo.setSchemeCode(plan.getSchemeCode());
        OverallPlan newPlan = nebulaToolkitService.copyObjectByWhiteList(vo, OverallPlan.class, HashSet.class, ArrayList.class);
        newPlan.setId(plan.getId());
        newPlan.setSchemeCode(schemeCode);
        newPlan.setTenantCode(TenantUtils.getTenantCode());
        newPlan.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        newPlan.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        newPlan.setProcessStatus(plan.getProcessStatus());
        overPlanRepository.updateById(newPlan);

        //存放redis
        String jsonStr = JSONObject.toJSONString(vo);
        String redisKey = OverallPlanConstant.getRedisKey(plan.getId());
        redisService.set(redisKey, jsonStr);
        //存放临时表
        tpmStagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
            this.setFromType(vo.getSchemeType());
            this.setReleaseId(plan.getId());
            this.setJsonStr(jsonStr);
        }});

        //日志处理
        OverallPlanLogEventDto dto = new OverallPlanLogEventDto();
        dto.setOriginal(oldVo);
        dto.setNewest(vo);
        SerializableBiConsumer<OverallPlanLogEventListener, OverallPlanLogEventDto> consumer = OverallPlanLogEventListener::onUpdate;
        nebulaNetEventClient.publish(dto, OverallPlanLogEventListener.class, consumer);
        return plan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitCreate(OverallPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        List<OverallPlanCaseVo> caseVoList = findCacheList(vo.getCacheKey());
        vo.setCaseList(caseVoList);
        this.validateData(vo, Boolean.TRUE);
        //1.保存主体信息
        OverallPlan plan = nebulaToolkitService.copyObjectByWhiteList(vo, OverallPlan.class, HashSet.class, ArrayList.class);
        //赋值登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        plan.setPositionCode(loginDetails.getPostCode());
        plan.setPositionName(loginDetails.getPostName());
        plan.setOrgCode(loginDetails.getOrgCode());
        plan.setOrgName(loginDetails.getOrgName());
        plan.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
        String schemeCode = null;
        if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(vo.getSchemeType())) {
            schemeCode = generateCodeService.generateCode(OverallPlanConstant.HEAD_SCHEME_CODE_RULE);
        } else {
            schemeCode = generateCodeService.generateCode(OverallPlanConstant.REGION_SCHEME_CODE_RULE);
        }
        plan.setSchemeCode(schemeCode);
        plan.setTenantCode(TenantUtils.getTenantCode());
        plan.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        plan.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        //TODO 此处还需要对接OA
        overPlanRepository.save(plan);
        //保存明细
        this.overallPlanCaseService.saveBatchList(vo, schemeCode);
        if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(vo.getSchemeType())) {
            Long count = caseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCheckFlag()) && x.getCheckFlag()
                    && OverallPlanSchemeTypeEnum.HEAD.getCode().equals(x.getBearType())).count();
            if (count > 0) {
                Validate.isTrue(redisService.hasKey(OverallPlanConstant.getRedisOverPlanBudgetKey(vo.getCacheKey())), "请先手动触发一下更新预算");
                List<OverallPlanBudget> budgetList = (List<OverallPlanBudget>) redisService.get(OverallPlanConstant.getRedisOverPlanBudgetKey(vo.getCacheKey()));
                vo.setBudgetList(budgetList);
                //保存预算
                planBudgetRepository.saveBatchList(schemeCode, budgetList);
            }
        }
        //保存文件
        if (CollectionUtils.isNotEmpty(vo.getFilesList())){
            schemeCaseFilesService.saveOrUpdateFiles(vo.getFilesList(),plan.getSchemeCode(),plan.getSchemeType());
        }

        //创建OA审批
        overallPlanOaService.pushOa(plan);

        //日志处理
        OverallPlanLogEventDto dto = new OverallPlanLogEventDto();
        dto.setNewest(vo);
        SerializableBiConsumer<OverallPlanLogEventListener, OverallPlanLogEventDto> consumer = OverallPlanLogEventListener::onCreate;
        nebulaNetEventClient.publish(dto, OverallPlanLogEventListener.class, consumer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitUpdate(OverallPlanVo vo) {
        Validate.notNull(vo.getCacheKey(), "cachekey不能为空");
        List<OverallPlanCaseVo> caseVoList = findCacheList(vo.getCacheKey());
        vo.setCaseList(caseVoList);
        this.validateData(vo, Boolean.TRUE);
        Validate.notNull(vo.getId(), "主键ID不能为空");
        OverallPlan plan = overPlanRepository.findById(vo.getId());
        Validate.notNull(plan, "查询数据信息不存在或已删除");
        //做日志处理
        OverallPlanVo oldVo = findById(vo.getId(), plan.getSchemeType());
        Validate.isTrue(StringUtils.equalsAny(plan.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode()), "不是待提交/驳回/追回状态不可编辑");
        String schemeCode = plan.getSchemeCode();
        vo.setSchemeCode(plan.getSchemeCode());
        OverallPlan newPlan = nebulaToolkitService.copyObjectByWhiteList(vo, OverallPlan.class, HashSet.class, ArrayList.class);
        newPlan.setId(plan.getId());
        newPlan.setSchemeCode(schemeCode);
        newPlan.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
        newPlan.setTenantCode(TenantUtils.getTenantCode());
        newPlan.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        newPlan.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        //TODO 此处还需要加工作流对接
        overPlanRepository.updateById(newPlan);
        //保存明细
        this.overallPlanCaseService.saveBatchList(vo, schemeCode);
        if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(vo.getSchemeType())) {
            Long count = caseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCheckFlag()) && x.getCheckFlag()
                    && OverallPlanSchemeTypeEnum.HEAD.getCode().equals(x.getBearType())).count();
            if (count > 0) {
                Validate.isTrue(redisService.hasKey(OverallPlanConstant.getRedisOverPlanBudgetKey(vo.getCacheKey())), "请先手动触发一下更新预算");
                List<OverallPlanBudget> budgetList = (List<OverallPlanBudget>) redisService.get(OverallPlanConstant.getRedisOverPlanBudgetKey(vo.getCacheKey()));
                vo.setBudgetList(budgetList);
                //保存预算
                planBudgetRepository.saveBatchList(schemeCode, budgetList);
            }
        }
        //保存文件
        if (CollectionUtils.isNotEmpty(vo.getFilesList())){
            schemeCaseFilesService.saveOrUpdateFiles(vo.getFilesList(),plan.getSchemeCode(),plan.getSchemeType());
        }
        //创建OA审批
        if (ProcessStatusEnum.PREPARE.getDictCode().equals(plan.getProcessStatus())) {
            overallPlanOaService.pushOa(newPlan);
        } else {
            overallPlanOaService.resubmitOa(newPlan);
        }

        //此处需要删除redis缓存和临时表数据
        String redisKey = OverallPlanConstant.getRedisKey(plan.getId());
        redisService.del(redisKey);
        tpmStagingSchemeService.deleteStaging(new TpmStagingSchemeVo() {{
            this.setReleaseIds(Lists.newArrayList(plan.getId()));
            this.setFromType(plan.getSchemeType());
        }});

        //日志处理
        OverallPlanLogEventDto dto = new OverallPlanLogEventDto();
        dto.setOriginal(oldVo);
        dto.setNewest(vo);
        SerializableBiConsumer<OverallPlanLogEventListener, OverallPlanLogEventDto> consumer = OverallPlanLogEventListener::onUpdate;
        nebulaNetEventClient.publish(dto, OverallPlanLogEventListener.class, consumer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitById(String id, String schemeType) {
        try {
            OverallPlanVo vo = findById(id, schemeType);
            Validate.notNull(vo, "查询数据信息不存在或已删除");
            List<OverallPlanCaseVo> caseVoList = findCacheList(vo.getId());
            vo.setCaseList(caseVoList);
            //提交
            this.validateData(vo, Boolean.TRUE);
            Validate.notNull(vo, "查询数据信息不存在或已删除");
            //做日志处理
            OverallPlanVo oldVo = findById(vo.getId(), vo.getSchemeType());
            Validate.isTrue(StringUtils.equalsAny(vo.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                    ProcessStatusEnum.RECOVER.getDictCode()), "不是待提交/驳回/追回状态不可编辑");
            String schemeCode = vo.getSchemeCode();
            vo.setSchemeCode(vo.getSchemeCode());
            boolean prepare = ProcessStatusEnum.PREPARE.getDictCode().equals(vo.getProcessStatus());
            OverallPlan newPlan = nebulaToolkitService.copyObjectByWhiteList(vo, OverallPlan.class, HashSet.class, ArrayList.class);
            newPlan.setId(vo.getId());
            newPlan.setSchemeCode(schemeCode);
            newPlan.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
            newPlan.setTenantCode(TenantUtils.getTenantCode());
            newPlan.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            newPlan.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            //TODO 此处还需要加工作流对接
            overPlanRepository.updateById(newPlan);
            //保存明细
            this.overallPlanCaseService.saveBatchList(vo, schemeCode);

            //创建OA审批
            if (prepare) {
                overallPlanOaService.pushOa(newPlan);
            } else {
                overallPlanOaService.resubmitOa(newPlan);
            }

            //此处需要删除redis缓存和临时表数据
            String redisKey = OverallPlanConstant.getRedisKey(vo.getId());
            redisService.del(redisKey);
            tpmStagingSchemeService.deleteStaging(new TpmStagingSchemeVo() {{
                this.setReleaseIds(Lists.newArrayList(vo.getId()));
                this.setFromType(schemeType);
            }});

            //日志处理
            OverallPlanLogEventDto dto = new OverallPlanLogEventDto();
            dto.setOriginal(oldVo);
            dto.setNewest(vo);
            SerializableBiConsumer<OverallPlanLogEventListener, OverallPlanLogEventDto> consumer = OverallPlanLogEventListener::onUpdate;
            nebulaNetEventClient.publish(dto, OverallPlanLogEventListener.class, consumer);
        } finally {
            //清理分页缓存
            clearCache(id);
        }
    }


    /**
     * 参数校验
     *
     * @param vo
     * @param submitCheck
     */
    private void validateData(OverallPlanVo vo, Boolean submitCheck) {
        Validate.notNull(vo, "数据对象不能为空");
        Validate.notNull(vo.getSchemeName(), "方案名称不能为空");
        Validate.isTrue(vo.getSchemeName().length() <= 100, "方案名称不能超过100字符");
        //判断是提交校验
        if (submitCheck) {
            Validate.isTrue(CollectionUtils.isNotEmpty(vo.getDepartmentList()), "所属部门不能为空");
            if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(vo.getSchemeType())) {
                Validate.notEmpty(vo.getStartDate(), "开始时间不能为空");
                Validate.notEmpty(vo.getEndDate(), "结束时间不能为空");
            } else {
                Validate.notNull(vo.getYears(), "年月不能为空");
            }
            if (ObjectUtils.isNotEmpty(vo.getSchemeTheme())) {
                Validate.isTrue(vo.getSchemeTheme().length() <= 300, "方案说明不能超过300字符");
            }
//            Validate.notNull(vo.getSchemeTheme(), "方案主题不能为空");
//            Validate.notNull(vo.getSchemeDesc(), "方案说明不能为空");
            Validate.isTrue(CollectionUtils.isNotEmpty(vo.getCaseList()), "方案明细列表不能为空");
            BigDecimal estimatedCost = BigDecimal.ZERO;
            BigDecimal estimatedSalesVolume = BigDecimal.ZERO;

            LocalDate startDate = null;
            LocalDate endDate = null;
            if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(vo.getSchemeType())) {
                startDate = LocalDate.parse(vo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                endDate = LocalDate.parse(vo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            } else {
                LocalDate now = LocalDate.parse(vo.getYears() + "-01", DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                // 获取当前年月
                YearMonth currentYearMonth = YearMonth.from(now);
                startDate = currentYearMonth.atDay(1);
                endDate = currentYearMonth.atEndOfMonth();
            }
            for (OverallPlanCaseVo planCase : vo.getCaseList()) {
                Validate.isTrue(planCase.getCheckFlag(), "未校验通过的数据不能提交");
                Validate.notNull(planCase.getSecondCostCategory(), "二级费用大类编码不能为空");
                Validate.notNull(planCase.getSecondCostCategoryName(), "二级费用大类名称不能为空");
                Validate.notNull(planCase.getStartDate(), "细案开始时间不能为空");
                Validate.notNull(planCase.getEndDate(), "结束时间不能为空");
//                Validate.notNull(planCase.getCostCenterCode(), "成本中心编码不能为空");
//                Validate.notNull(planCase.getCostCenterName(), "成本中心名称不能为空");
                Validate.notNull(planCase.getApplyAmount(), "预估费用不能为空");
//                Validate.notNull(planCase.getEstimatedSalesVolume(), "预估销售额不能为空");
                estimatedCost = estimatedCost.add(planCase.getApplyAmount());
                if (ObjectUtils.isNotEmpty(planCase.getEstimatedSalesVolume())) {
                    estimatedSalesVolume = estimatedSalesVolume.add(planCase.getEstimatedSalesVolume());
                }
                LocalDate caseStartDate = LocalDate.parse(planCase.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate caseEndDate = LocalDate.parse(planCase.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                Validate.isTrue(!caseStartDate.isBefore(startDate) && !caseEndDate.isAfter(endDate), "方案明细的活动时间必须在方案活动时间内");
                Validate.isTrue(caseStartDate.getYear() == caseEndDate.getYear() && caseStartDate.getMonth() == caseEndDate.getMonth(), "方案明细的活动时间必须在同一个月");
                //如果是总部需要设置区域是否直接承接的默认值
                if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(vo.getSchemeType())) {
                    planCase.setBearFlag(ObjectUtils.defaultIfNull(planCase.getBearFlag(), BooleanEnum.FALSE.getCapital()));
                }
                if (ObjectUtils.isNotEmpty(planCase.getBearType())) {
                    // 判断承接类型是总部承担
                    if (OverallPlanBearTypeEnum.head.getCode().equals(planCase.getBearType())) {
                        Validate.notNull(planCase.getBearDepartmentCode(), "费用承担部门不能为空");
                        Validate.notNull(planCase.getCostCenterCode(), "成本中心不能为空");
                    }
                }
            }

            BigDecimal productionRatio = BigDecimal.ZERO;
            if (estimatedSalesVolume.compareTo(BigDecimal.ZERO) > 0) {
                productionRatio = estimatedCost.divide(estimatedSalesVolume, 4, BigDecimal.ROUND_HALF_DOWN);
            }
            vo.setEstimateSalesVolume(estimatedSalesVolume);
            vo.setCostTotal(estimatedCost);
            vo.setProductionRatio(productionRatio);
            //如果是大区就进行校验
            if (OverallPlanSchemeTypeEnum.REGION.getCode().equals(vo.getSchemeType())) {
                this.checkSubmitRegion(vo.getCaseList(), ObjectUtils.defaultIfNull(vo.getSchemeCode(), null));
            }
        } else {
            BigDecimal estimatedCost = BigDecimal.ZERO;
            BigDecimal estimatedSalesVolume = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(vo.getCaseList())) {
                for (OverallPlanCaseVo planCase : vo.getCaseList()) {
                    if (ObjectUtils.isNotEmpty(planCase.getApplyAmount())) {
                        estimatedCost = estimatedCost.add(planCase.getApplyAmount());
                    }
                    if (ObjectUtils.isNotEmpty(planCase.getEstimatedSalesVolume())) {
                        estimatedSalesVolume = estimatedSalesVolume.add(planCase.getEstimatedSalesVolume());
                    }
                }
                BigDecimal productionRatio = BigDecimal.ZERO;
                if (estimatedSalesVolume.compareTo(BigDecimal.ZERO) > 0) {
                    productionRatio = estimatedCost.divide(estimatedSalesVolume, 2, BigDecimal.ROUND_HALF_DOWN);
                }
                vo.setEstimateSalesVolume(estimatedSalesVolume);
                vo.setCostTotal(estimatedCost);
                vo.setProductionRatio(productionRatio);
            }
        }
    }


    public void checkSubmitRegion(List<OverallPlanCaseVo> caseList, String schemeCode) {
        String years = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getYears()))
                .map(x -> x.getYears()).findFirst().get();
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        String orgCode = loginDetails.getOrgCode();
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(orgCode);
        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
        List<OverallPlanCase> headCaseList = overallPlanCaseService.findHeadPlanCaseListByOrgCodes(orgCodes, years, BooleanEnum.FALSE.getCapital(), schemeCode);
        if (CollectionUtils.isEmpty(headCaseList)) return;
        Map<String, BigDecimal> headCaseMap = headCaseList.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), x -> x.getApplyAmount()));
        Map<String, BigDecimal> regionCaseMap = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getHeadSchemeDetailCode()))
                .collect(Collectors.groupingBy(OverallPlanCaseVo::getHeadSchemeDetailCode,
                        Collectors.mapping(OverallPlanCaseVo::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        StringJoiner errMsg = new StringJoiner(";");
        for (Map.Entry<String, BigDecimal> entry : headCaseMap.entrySet()) {
            if (regionCaseMap.containsKey(entry.getKey())) {
                BigDecimal amount = regionCaseMap.get(entry.getKey());
                if (amount.compareTo(entry.getValue()) != 0) {
                    BigDecimal different = amount.subtract(entry.getValue());
                    if (different.compareTo(BigDecimal.ZERO) == 1) {
                        errMsg.add(String.format("总部编码%s承接超额,超额承接%s", entry.getKey(), different));
                    } else {
                        errMsg.add(String.format("总部编码%s未完全承接,剩余承接金额%s", entry.getValue(), different.negate()));
                    }
                }
            } else if (entry.getValue().compareTo(BigDecimal.ZERO) == 1) {
                errMsg.add(String.format("总部编码%s未承接", entry.getKey()));
            }
        }
        if (ObjectUtils.isNotEmpty(errMsg.toString())) {
            Validate.isTrue(Boolean.FALSE, errMsg.toString());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manualApproval(String id, String schemeType) {
        OverallPlanVo vo = findById(id, schemeType);
        List<OverallPlanCaseVo> detailList = findCacheList(id);
        vo.setCaseList(detailList);
        this.validateData(vo, Boolean.TRUE);
        OverallPlan plan = overPlanRepository.findById(id);
        plan.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());
        overPlanRepository.updateById(plan);
    }


    /**
     * 审批回调
     *
     * @param vo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void callback(OverallPlanVo vo) {
        OverallPlan plan = overPlanRepository.findBySchemeCode(vo.getSchemeCode());
        if (ObjectUtils.isNotEmpty(plan)) {
            //判断如果是驳回
            if (!ProcessStatusEnum.PASS.getDictCode().equals(vo.getProcessStatus())) {
                OverallPlanVo oldVo = findById(plan.getId(), plan.getSchemeType());
                List<OverallPlanCaseVo> caseVoList = overallPlanCaseService.findCaseListBySchemeCode(oldVo.getSchemeCode());
                oldVo.setCaseList(caseVoList);
                oldVo.setProcessStatus(vo.getProcessStatus());
                //存放redis
                String jsonStr = JSONObject.toJSONString(oldVo);
                String redisKey = OverallPlanConstant.getRedisKey(plan.getId());
                redisService.set(redisKey, jsonStr);
                //存放临时表
                tpmStagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
                    this.setFromType(plan.getSchemeType());
                    this.setReleaseId(plan.getId());
                    this.setJsonStr(jsonStr);
                }});
                //需要把所有明细删除了
                overPlanDepartmentRepository.deleteBySchemeCodes(Lists.newArrayList(oldVo.getSchemeCode()));
                overallPlanCaseService.deleteBySchemeCodes(Lists.newArrayList(oldVo.getSchemeCode()));
                overPlanProductRepository.deleteBySchemeCodes(Lists.newArrayList(oldVo.getSchemeCode()));
                overPlanScopeRepository.deleteBySchemeCodes(Lists.newArrayList(oldVo.getSchemeCode()));
            }
            //先执行以上逻辑 再进行更新状态
            plan.setProcessStatus(vo.getProcessStatus());
            overPlanRepository.updateById(plan);
        }
    }

    /**
     * 删除
     *
     * @param idList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteByIdList(List<String> idList) {
        List<OverallPlan> planList = overPlanRepository.findListByIdList(idList);
        List<String> schemeNames = planList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PASS.getDictCode(), ProcessStatusEnum.COMMIT.getDictCode()))
                .map(OverallPlan::getSchemeName).collect(Collectors.toList());
        planList = planList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                        ProcessStatusEnum.RECOVER.getDictCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(planList)) {
            planList.forEach(x -> x.setDelFlag(DelFlagStatusEnum.DELETE.getCode()));
            overPlanRepository.updateBatchById(planList);
            //删除文件
            List<String> schemeCodes = planList.stream().map(x->x.getSchemeCode()).collect(Collectors.toList());
            List<String> schemeTypes = planList.stream().map(x->x.getSchemeType()).distinct().collect(Collectors.toList());
            schemeCaseFilesService.deleteFiles(schemeCodes,schemeTypes);

            //删除OA接口
            planList = planList.stream().filter(x -> StringUtils.equalsAny(x.getProcessStatus(), ProcessStatusEnum.REJECT.getDictCode(),
                    ProcessStatusEnum.RECOVER.getDictCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(planList)) {
                planList.forEach(e -> ryOaProcessService.deleteWorkflow(e.getProcessNumber(), e.getOaId()));
            }

            //日志处理
            OverallPlanLogEventDto dto = new OverallPlanLogEventDto();
            List<OverallPlanVo> planVoList = (List<OverallPlanVo>) nebulaToolkitService.copyCollectionByWhiteList(planList, OverallPlan.class,
                    OverallPlanVo.class, HashSet.class, ArrayList.class);
            dto.setNewestList(planVoList);
            SerializableBiConsumer<OverallPlanLogEventListener, OverallPlanLogEventDto> consumer = OverallPlanLogEventListener::onDelete;
            nebulaNetEventClient.publish(dto, OverallPlanLogEventListener.class, consumer);
        }
        return schemeNames.stream().collect(Collectors.joining("、"));
    }

    /**
     * 查询总部方案明细 (可以承接的)
     *
     * @param vo
     * @return
     */
    @Override
    public Page<OverallPlanCaseVo> findHeadOverallPlanCaseList(Pageable pageable, HeadOverallPlanCaseVo vo) {
        vo.setSchemeType(OverallPlanSchemeTypeEnum.HEAD.getCode());
        return overallPlanCaseService.findHeadOverallPlanCaseList(pageable, vo);
    }


    @Override
    public Page<OverallPlanCaseVo> findHeadOverallPlanCaseToMarketing(Pageable pageable, HeadOverallPlanCaseVo vo) {
        vo.setSchemeType(OverallPlanSchemeTypeEnum.HEAD.getCode());
        return overallPlanCaseService.findHeadOverallPlanCaseToMarketing(pageable, vo);
    }

    /**
     * 通过方案明细编码查询方案明细
     *
     * @param schemeDetailCodes
     * @return
     */
    @Override
    public List<OverallPlanCaseVo> findOverallPlanCaseListBySchemeDetailCodes(List<String> schemeDetailCodes) {
        List<OverallPlanCase> caseList = overallPlanCaseService.findCaseListBySchemeDetailCodes(schemeDetailCodes, ProcessStatusEnum.PASS.getDictCode());
        List<OverallPlanCaseVo> dataList = (List<OverallPlanCaseVo>) nebulaToolkitService.copyCollectionByWhiteList(caseList, OverallPlanCase.class, OverallPlanCaseVo.class, HashSet.class, ArrayList.class);

        List<OverallPlanDepartment> bearDepartmentList = overPlanDepartmentRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
        List<OverallPlanProduct> productList = overPlanProductRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
        List<OverallPlanScope> scopeList = overPlanScopeRepository.findListBySchemeDetailCodeList(schemeDetailCodes);

        Map<String, List<OverallPlanDepartment>> bearDepartmentMap = bearDepartmentList.stream().collect(Collectors.groupingBy(x -> x.getSchemeDetailCode()));
        Map<String, List<OverallPlanProduct>> productMap = productList.stream().collect(Collectors.groupingBy(OverallPlanProduct::getSchemeDetailCode));
        Map<String, List<OverallPlanScope>> scoepMap = scopeList.stream().collect(Collectors.groupingBy(x -> x.getSchemeDetailCode()));

        for (OverallPlanCaseVo data : dataList) {
            if (bearDepartmentMap.containsKey(data.getSchemeDetailCode())) {
                data.setBearDepartmentList((List<OverallPlanDepartmentVo>) nebulaToolkitService.copyCollectionByWhiteList(bearDepartmentMap.get(data.getSchemeDetailCode()), OverallPlanDepartment.class, OverallPlanDepartmentVo.class, HashSet.class, ArrayList.class));
            }
            if (productMap.containsKey(data.getSchemeDetailCode())) {
                List<OverallPlanProductVo> list = (List<OverallPlanProductVo>) nebulaToolkitService.copyCollectionByWhiteList(productMap.get(data.getSchemeDetailCode()), OverallPlanProduct.class, OverallPlanProductVo.class, HashSet.class, ArrayList.class);
                data.setProductAndItemList(list);
            }
            if (scoepMap.containsKey(data.getSchemeDetailCode())) {
                data.setScopeList((List<OverallPlanScopeVo>) nebulaToolkitService.copyCollectionByWhiteList(scoepMap.get(data.getSchemeDetailCode()), OverallPlanScope.class, OverallPlanScopeVo.class, HashSet.class, ArrayList.class));
            }
        }
        return dataList;
    }

    @Override
    public List<OverallPlanCaseVo> findOverallPlanCaseListByReleaseDetailCode(List<String> releaseDetailCode) {
            List<OverallPlanCase> caseList = overallPlanCaseService.findCaseListByReleaseDetailCodes(releaseDetailCode, ProcessStatusEnum.PASS.getDictCode());
            List<OverallPlanCaseVo> dataList = (List<OverallPlanCaseVo>) nebulaToolkitService.copyCollectionByWhiteList(caseList, OverallPlanCase.class, OverallPlanCaseVo.class, HashSet.class, ArrayList.class);
            return dataList;
    }


    /**
     * 查询关联的总部方案明细(大区使用)
     *
     * @param schemeDetailCodes
     * @return
     */
    @Override
    public List<BearOverallPlanCaseVo> findReleaseHeadCaseList(List<String> schemeDetailCodes) {
        List<BearOverallPlanCaseVo> bearList = overallPlanCaseService.findReleaseHeadCaseList(schemeDetailCodes);
        List<OverallPlanCase> alreadyBearCaseLit = overallPlanCaseService.findAlreadyBearCase(schemeDetailCodes);
        Map<String, BigDecimal> alreadyBearMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(alreadyBearCaseLit)) {
            alreadyBearMap = alreadyBearCaseLit.stream().collect(Collectors.groupingBy(OverallPlanCase::getHeadSchemeDetailCode,
                    Collectors.mapping(OverallPlanCase::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        for (BearOverallPlanCaseVo caseVo : bearList) {
            caseVo.setAlreadyBearAmount(alreadyBearMap.getOrDefault(caseVo.getSchemeDetailCode(), BigDecimal.ZERO));
        }
        return bearList;
    }


    /**
     * 查询关联的方案明细(营销规划方案使用)
     *
     * @param schemeDetailCodes
     * @return
     */
    @Override
    public List<BearOverallPlanCaseVo> findReleaseCaseByMarketingPlan(List<String> schemeDetailCodes) {
        List<BearOverallPlanCaseVo> bearList = overallPlanCaseService.findReleaseCaseByMarketingPlan(schemeDetailCodes);
        return bearList;
    }

    @Override
    public Map<String, Object> getTotalAmount(String cacheKey) {
        List<OverallPlanCaseVo> caseVoList = super.findCacheList(cacheKey);
        BigDecimal estimatedCost = BigDecimal.ZERO;
        BigDecimal estimatedSalesVolume = BigDecimal.ZERO;
        BigDecimal productionRatio = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(caseVoList)) {
            for (OverallPlanCaseVo planCase : caseVoList) {
                if (ObjectUtils.isNotEmpty(planCase.getApplyAmount())) {
                    estimatedCost = estimatedCost.add(planCase.getApplyAmount());
                }
                if (ObjectUtils.isNotEmpty(planCase.getEstimatedSalesVolume())) {
                    estimatedSalesVolume = estimatedSalesVolume.add(planCase.getEstimatedSalesVolume());
                }
            }
            if (estimatedSalesVolume.compareTo(BigDecimal.ZERO) > 0) {
                productionRatio = estimatedCost.divide(estimatedSalesVolume, 2, BigDecimal.ROUND_HALF_DOWN);
            }
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("estimatedCost", estimatedCost);
        map.put("estimatedSalesVolume", estimatedSalesVolume);
        map.put("productionRatio", productionRatio);
        return map;
    }

    /**
     * 查询总部指引汇总-总览
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<HeadOverallSummaryReportVo> findSummaryList(Pageable pageable, HeadOverallSummaryReportVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<HeadOverallSummaryReportVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<HeadOverallSummaryReportVo> data = overPlanRepository.findSummaryList(page, vo);
        return data;
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recover(String code, String remark) {
        OverallPlan plan = overPlanRepository.findBySchemeCode(code);
        if (overallPlanOaService.oaWithdraw(plan, remark)) {
            OverallPlanVo oldVo = findById(plan.getId(), plan.getSchemeType());
            List<OverallPlanCaseVo> caseVoList = overallPlanCaseService.findCaseListBySchemeCode(oldVo.getSchemeCode());
            oldVo.setCaseList(caseVoList);
            oldVo.setProcessStatus(ProcessStatusEnum.RECOVER.getDictCode());
            //存放redis
            String jsonStr = JSONObject.toJSONString(oldVo);
            String redisKey = OverallPlanConstant.getRedisKey(plan.getId());
            redisService.set(redisKey, jsonStr);
            //存放临时表
            tpmStagingSchemeService.saveOrUpdateStaging(new TpmStagingSchemeVo() {{
                this.setFromType(plan.getSchemeType());
                this.setReleaseId(plan.getId());
                this.setJsonStr(jsonStr);
            }});
            //需要把所有明细删除了
            overPlanDepartmentRepository.deleteBySchemeCodes(Lists.newArrayList(oldVo.getSchemeCode()));
            overallPlanCaseService.deleteBySchemeCodes(Lists.newArrayList(oldVo.getSchemeCode()));
            overPlanProductRepository.deleteBySchemeCodes(Lists.newArrayList(oldVo.getSchemeCode()));
            overPlanScopeRepository.deleteBySchemeCodes(Lists.newArrayList(oldVo.getSchemeCode()));
            plan.setProcessStatus(ProcessStatusEnum.RECOVER.getDictCode());
            overPlanRepository.saveOrUpdate(plan);
        } else {
            Validate.isTrue(false, "流程撤回失败");
        }
    }


    @Override
    public void checkRegionOverall(String years) {
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        String detailFlag = BooleanEnum.FALSE.getCapital();
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(OverallPlanConstant.IS_REGIONAL_PROGRAMMES);
        if (CollectionUtils.isEmpty(dictDataVos)) {
            return;
        }
        String dictCode = dictDataVos.get(0).getDictCode();
        if (!StringUtils.equals(OverallPlanConstant.YES, dictCode)) {
            return;
        }
        List<String> schemeDetailCodes = findRegionOverallCaseList(years, null, detailFlag);
        Validate.isTrue(CollectionUtils.isNotEmpty(schemeDetailCodes), String.format("当前登陆人所属组织%s及其上级没有可承接的大区指引明细", loginDetails.getOrgName()));
    }

    /**
     * 查询组织下面的所有大区可承接方案明细
     *
     * @param years
     * @param orgCode
     * @return
     */
    @Override
    public List<String> findRegionOverall(String years, String orgCode) {
        String detailFlag = BooleanEnum.TRUE.getCapital();
        List<String> schemeDetailCodes = findRegionOverallCaseList(years, orgCode, detailFlag);
        return schemeDetailCodes;
    }


    public List<String> findRegionOverallCaseList(String years, String orgCode, String detailFlag) {

        Validate.notBlank(years, "请选择年月");
        List<OrgVo> orgVoList = Lists.newArrayList();
        //如果传入组织不为空 则拿当前组织查询所有下级 反之取登陆查询所有上级
        if (ObjectUtils.isNotEmpty(orgCode)) {
            orgVoList = orgVoService.findAllChildrenByOrgCode(orgCode);
            Validate.isTrue(CollectionUtils.isNotEmpty(orgVoList), String.format("当前所属组织%s未查询到下级组织", orgCode));
        } else {
            FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
            orgCode = loginDetails.getOrgCode();
            orgVoList = orgVoService.findAllParentByOrgCode(orgCode);
            Validate.isTrue(CollectionUtils.isNotEmpty(orgVoList), String.format("当前登陆人所属组织%s未查询到上级组织", loginDetails.getOrgName()));
        }
        Set<String> orgCodeSet = Sets.newHashSet(orgCode);
        orgCodeSet.addAll(orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet()));
        List<OverallPlanCaseVo> list = overallPlanCaseService.checkRegionOverall(orgCodeSet, OverallPlanSchemeTypeEnum.REGION.getCode(), years, detailFlag);
        List<String> schemeDetailCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            schemeDetailCodes = list.stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        }
        return schemeDetailCodes;
    }


    /**
     * 查询全部可承接的方案明细
     *
     * @param years
     * @param orgCode
     * @return
     */
    @Override
    public List<String> findAllUndertakeOverall(String years, String orgCode) {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(OverallPlanConstant.IS_REGIONAL_PROGRAMMES);
        if (CollectionUtils.isEmpty(dictDataVos)) {
            return Lists.newArrayList();
        }
        String dictCode = dictDataVos.get(0).getDictCode();
        if (!StringUtils.equals(OverallPlanConstant.YES, dictCode)) {
            return Lists.newArrayList();
        }

        Validate.notBlank(years, "请选择年月");

        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(orgCode);
        Validate.isTrue(CollectionUtils.isNotEmpty(orgVoList), String.format("当前所属组织%s未查询到下级组织", orgCode));

        Set<String> orgCodeSet = Sets.newHashSet(orgCode);
        orgCodeSet.addAll(orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet()));
        List<OverallPlanCaseVo> list = overallPlanCaseService.checkRegionOverall(orgCodeSet, null, years, BooleanEnum.FALSE.getCapital());
        List<String> schemeDetailCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            schemeDetailCodes = list.stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        }
        return schemeDetailCodes;
    }


    /**
     * 更新预算
     *
     * @param cacheKey
     * @return
     */
    @Override
    public List<OverallPlanBudget> updateOverallBudget(String cacheKey) {
        List<OverallPlanCaseVo> caseVoList = this.findCacheList(cacheKey);
        if (CollectionUtils.isEmpty(caseVoList)) return Lists.newArrayList();
        //只需要校验成功的 并且是总部承担的
        caseVoList = caseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCheckFlag()) && x.getCheckFlag()
                && OverallPlanSchemeTypeEnum.HEAD.getCode().equals(x.getBearType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(caseVoList)) return Lists.newArrayList();

        Map<String, List<BudgetTrackVo>> planCaseControlMap = Maps.newHashMap();
        Map<String, Map<String, List<OrgVo>>> controlBudgetOrgMap = Maps.newHashMap();
        Map<String, List<String>> caseControlCodeMap = Maps.newHashMap();
        Set<String> codeSet = Sets.newHashSet();
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        //匹配管控规则
        for (OverallPlanCaseVo caseVo : caseVoList) {
            caseVo.setSchemeDetailCode(ObjectUtils.defaultIfNull(caseVo.getSchemeDetailCode(), UuidCrmUtil.general()));
            List<String> codes = matchControlBudget(caseVo);
            caseControlCodeMap.put(caseVo.getSchemeDetailCode(), codes);
            codeSet.addAll(codes);
        }
        Map<String, OverallPlanCaseVo> caseMap = caseVoList.stream().collect(Collectors.toMap(OverallPlanCaseVo::getSchemeDetailCode, v -> v));
        List<Future<List<BudgetTrackVo>>> codeFutureList = Lists.newArrayList();
        for (String s : codeSet) {
            OverallCalBudgetComponent bean = ApplicationContextHolder.getContext().getBean(OverallCalBudgetComponent.class);
            Future<List<BudgetTrackVo>> future = bean.syncCalOverallBudget(s, controlBudgetOrgMap, loginDetails);
            codeFutureList.add(future);
        }
        List<BudgetTrackVo> budgetTrackVoList = Lists.newArrayList();
        for (Future<List<BudgetTrackVo>> future : codeFutureList) {
            try {
                budgetTrackVoList.addAll(future.get());
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
        }
        Map<String, List<BudgetTrackVo>> map = budgetTrackVoList.stream().collect(Collectors.groupingBy(x -> x.getBudgetControlCode()));
        //重新分配预算管控
        for (Map.Entry<String, List<String>> entry : caseControlCodeMap.entrySet()) {
            List<BudgetTrackVo> dataList = Lists.newArrayList();
            for (String s : entry.getValue()) {
                if (map.containsKey(s)) {
                    dataList.addAll(map.get(s));
                }
            }
            planCaseControlMap.put(entry.getKey(), dataList);
        }

        List<Future<List<OverallPlanBudget>>> budgetFutureList = Lists.newArrayList();
        for (Map.Entry<String, List<BudgetTrackVo>> entry : planCaseControlMap.entrySet()) {
            OverallCalBudgetComponent bean = ApplicationContextHolder.getContext().getBean(OverallCalBudgetComponent.class);
            Future<List<OverallPlanBudget>> future = bean.syncMatchBudgetByOverallPlan(entry.getValue(), caseMap.get(entry.getKey()), controlBudgetOrgMap, loginDetails);
            budgetFutureList.add(future);
        }
        List<OverallPlanBudget> budgetList = Lists.newArrayList();
        for (Future<List<OverallPlanBudget>> future : budgetFutureList) {
            try {
                budgetList.addAll(future.get());
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
        }
        redisService.set(OverallPlanConstant.getRedisOverPlanBudgetKey(cacheKey), budgetList, 36000l);
        Map<String, BigDecimal> budgetMap = budgetList.stream().collect(Collectors.groupingBy(x -> x.getTrackCode(),
                Collectors.mapping(x -> x.getApplyAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        List<OverallPlanBudget> newBudgetList = budgetList.stream().collect(Collectors.toMap(x -> x.getTrackCode(), Function.identity(), (v1, v2) -> v1))
                .values().stream().collect(Collectors.toList());
        for (OverallPlanBudget budget : newBudgetList) {
            BigDecimal applyAmount = budgetMap.getOrDefault(budget.getTrackCode(), BigDecimal.ZERO);
            budget.setApplyAmount(applyAmount);
            budget.setUsedAmount(applyAmount);
        }
        return newBudgetList;
    }

    /**
     * 校验总部预算
     *
     * @param cacheKey
     * @return
     */
    @Override
    public Map<String, Object> checkHeadBudget(String cacheKey) {
        Map<String, Object> map = Maps.newHashMap();
        //获取预算
        List<OverallPlanBudget> budgetList = this.updateOverallBudget(cacheKey);
        Map<String, String> controlMap = budgetList.stream().collect(Collectors.toMap(x -> x.getTrackCode(), x -> x.getControlCode(), (a, b) -> a));
        //强制管控
        List<OverallPlanBudget> forceBudgetList = budgetList.stream().filter(x -> ControlMethodEnum.force.getCode().equals(x.getControlForm()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(forceBudgetList)) {
            Map<String, BigDecimal> amountMap = forceBudgetList.stream().collect(Collectors.toMap(OverallPlanBudget::getTrackCode,
                    OverallPlanBudget::getSurplusAmount, (v1, v2) -> v1));
            Map<String, BigDecimal> applyAmount = forceBudgetList.stream().collect(Collectors.groupingBy(OverallPlanBudget::getTrackCode,
                    Collectors.mapping(OverallPlanBudget::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            for (Map.Entry<String, BigDecimal> entry : amountMap.entrySet()) {
                BigDecimal differenceAmount = entry.getValue().subtract(applyAmount.get(entry.getKey()));
                Validate.isTrue(differenceAmount.compareTo(BigDecimal.ZERO) > -1, "预算管控规则%s属于强制管控,超金额使用%s", controlMap.get(entry.getKey()), differenceAmount.negate());
            }
        }
        //预警
        List<OverallPlanBudget> waringBudgetList = budgetList.stream().filter(x -> ControlMethodEnum.waring.getCode().equals(x.getControlForm()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(waringBudgetList)) {
            Map<String, BigDecimal> amountMap = waringBudgetList.stream().collect(Collectors.toMap(OverallPlanBudget::getTrackCode,
                    OverallPlanBudget::getSurplusAmount, (v1, v2) -> v1));
            Map<String, BigDecimal> applyAmount = waringBudgetList.stream().collect(Collectors.groupingBy(OverallPlanBudget::getTrackCode,
                    Collectors.mapping(OverallPlanBudget::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            StringJoiner errMsg = new StringJoiner(";");
            for (Map.Entry<String, BigDecimal> entry : amountMap.entrySet()) {
                BigDecimal differenceAmount = entry.getValue().subtract(applyAmount.get(entry.getKey()));
                if (differenceAmount.compareTo(BigDecimal.ZERO) == -1) {
                    errMsg.add(String.format("预算管控规则%s超金额使用%s", controlMap.get(entry.getKey()), differenceAmount.negate()));
                }
            }
            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                map.put("msg", errMsg + "是否继续提交");
            }
        }
        return map;
    }

    @Resource
    private BudgetSubjectsVoService budgetSubjectsVoService;

    @Resource
    private BudgetControlVoService budgetControlVoService;


    public List<String> matchControlBudget(OverallPlanCaseVo caseVo) {
        String years = caseVo.getYears();
        String departmentCode = caseVo.getBearDepartmentCode();
        List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(departmentCode);
        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
        String customerCode = null;
        if (CollectionUtils.isNotEmpty(caseVo.getCustomerList()) && caseVo.getCustomerList().size() == 1) {
            customerCode = caseVo.getCustomerList().get(0).getCustomerCode();
        }

        String budgetSubjectCode = null;
        if (ObjectUtils.isNotEmpty(caseVo.getDetailCode())) {
            budgetSubjectCode = budgetSubjectsVoService.findBudgetSubjectByDetailCode(caseVo.getDetailCode());
        } else {
            budgetSubjectCode = caseVo.getSecondCostCategory();
        }
        Set<String> budgetSubjectCodeSet = budgetSubjectsVoService.findAllParentSubjectCodesByCodes(budgetSubjectCode);
        List<BudgetControlVo> budgetControlVoList = budgetControlVoService.matchBudgetControl(years, orgCodes, customerCode, budgetSubjectCodeSet);
        Validate.isTrue(!CollectionUtils.isEmpty(budgetControlVoList), String.format("方案明细%s未匹配到管控规则", caseVo.getSchemeDetailCode()));
        List<String> controlCode = budgetControlVoList.stream().map(x -> x.getControlCode()).collect(Collectors.toList());
        return controlCode;
    }

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 从缓存中取数据
     *
     * @param cacheKey 缓存key
     * @return
     */
    @Override
    public List<OverallPlanCaseVo> findCacheList(String cacheKey) {
        String redisCacheIdKey = helper.getRedisCacheIdKey(cacheKey);
        Set<String> idKeys = redisTemplate.keys(redisCacheIdKey + "*");
        List<OverallPlanCaseVo> dataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(idKeys)) {
            for (String idKey : idKeys) {
                String dataKey = idKey.replace(BusinessPageCacheConstant.REDIS_KEY_ID, BusinessPageCacheConstant.REDIS_KEY_DATA);
                List<Object> idList = redisService.lRange(idKey, 0, -1);
                dataList.addAll(redisTemplate.opsForHash().multiGet(dataKey, idList));
            }
        } else {
            String redisCacheInitKey = helper.getRedisCacheInitKey(cacheKey);
            try {
                //标记为已初始化，不重复初始化
                redisService.set(redisCacheInitKey, BooleanEnum.TRUE, helper.getExpireTime());
                //redis里面没有
                List<OverallPlanCaseVo> dtoList = helper.findDtoListFromRepository(new OverallPlanCaseVo() {{
                    this.setId(cacheKey);
                }}, cacheKey);
                if (CollectionUtils.isNotEmpty(dtoList)) {
                    dataList.addAll(dtoList);
                }
                if (helper.initToCacheFromRepository()) {
                    helper.putCache(cacheKey, dtoList);
                }
            } catch (Exception e) {
                redisService.del(redisCacheInitKey);
            }
        }
        return dataList;
    }


    /**
     * 保存当前数据到缓存
     *
     * @param cacheKey 缓存key
     * @param saveList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCurrentPageCache(String cacheKey, List<OverallPlanCaseVo> saveList) {
        String schemeType = cacheKey.split(":")[1];
        cacheKey = cacheKey.split(":")[0];
        List<OverallPlanCaseVo> checkList = overallPlanCaseService.checkCaseList(saveList, schemeType);
        super.saveCurrentPageCache(cacheKey, checkList);
    }
}
