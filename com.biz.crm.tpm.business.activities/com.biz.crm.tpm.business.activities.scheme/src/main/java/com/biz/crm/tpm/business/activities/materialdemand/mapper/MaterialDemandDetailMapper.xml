<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.materialdemand.mapper.MaterialDemandDetailMapper">



    <select id="findMaterialDemandReportList" resultType="com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandReportVo">
        SELECT
        concat( b.org_code, ':', b.material_code ) id,
        b.material_code,
        b.material_name,
        b.org_code,
        b.org_name,
        sum( b.material_num ) materialNum,
        sum( b.material_amount ) materialAmount,
        sum( b.material_num ) availMaterialNum,
        sum( b.material_amount ) availMaterialAmount
        FROM
        tpm_material_demand a
        LEFT JOIN tpm_material_demand_detail b ON a.demand_code = b.demand_code
        <where>
            a.process_status = '3'
            AND a.del_flag = '${@<EMAIL>()}'
            AND a.tenant_code = #{tenantCode}
            <if test="vo.orgCode != null and vo.orgCode != ''">
                <bind name="orgCode" value="'%' + vo.orgCode + '%'"/>
                and b.org_code like #{orgCode}
            </if>
            <if test="vo.orgName != null and vo.orgName != ''">
                <bind name="orgName" value="'%' + vo.orgName + '%'"/>
                and b.org_code like #{orgName}
            </if>
            <if test="vo.materialCode != null and vo.materialCode != ''">
                <bind name="materialCode" value="'%' + vo.materialCode + '%'"/>
                and b.material_code like #{materialCode}
            </if>
            <if test="vo.materialName != null and vo.materialName != ''">
                <bind name="materialName" value="'%' + vo.materialName + '%'"/>
                and b.material_name like #{materialName}
            </if>
        </where>
        GROUP BY
        material_code,
        material_name,
        org_code,
        org_name
    </select>

    <select id="queryDetailList" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        a.process_status,
        b.*,
        c.*
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        left join tpm_marketing_plan_case_extend c on b.scheme_detail_code = b.scheme_detail_code and a.scheme_code = c.scheme_code
        <where>
            a.del_flag = '${@<EMAIL>()}'
            AND a.tenant_code = #{tenantCode}
            and c.sell_material_type = '0'
            <if test="vo.materialCode != null and vo.materialCode != ''">
                and c.material_code = #{vo.materialCode}
            </if>
            <if test="vo.orgCode != null and vo.orgCode != ''">
                and b.belong_department_code = #{vo.orgCode}
            </if>
            order by b.create_time desc
        </where>
    </select>

    <select id="findListByMaterialCodesAndOrgCodes" resultType="com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandDetail">
        SELECT
        b.material_code,
        b.org_code,
        sum( b.material_num ) materialNum,
        sum( b.material_amount ) materialAmount
        FROM
        tpm_material_demand a
        LEFT JOIN tpm_material_demand_detail b ON a.demand_code = b.demand_code
        <where>
            a.process_status = '3'
            AND a.del_flag = '${@<EMAIL>()}'
            AND a.tenant_code = #{tenantCode}
            <if test="orgCodes != null and orgCodes.size()>0">
                and b.org_code in
                <foreach collection="orgCodes" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="materialCodes != null and materialCodes.size()>0">
                and b.material_code in
                <foreach collection="materialCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        material_code,
        org_code
    </select>

</mapper>

