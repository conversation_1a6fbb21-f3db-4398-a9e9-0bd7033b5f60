package com.biz.crm.tpm.business.activities.marketingplan.service.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.base.util.BusinessRequiredUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaCusVo;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCaseStrategy;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import liquibase.pro.packaged.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 陈列模板
 * <AUTHOR>
 * @Date 2024/6/20 20:06
 */
@Component
@Slf4j
public class DisplayCheckStrategyImpl implements MarketingPlanCaseStrategy<MarketingPlanCaseVo> {


    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private TerminalVoService terminalVoService;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    @Resource
    private DictDataVoService dictDataVoService;

    //费用项目控制
    private final static String MARKETING_CASE_DETAIL_CODE = "marketing_case_detail_code";

    //陈列
    private final static String DISPLAY = "display";

    //人员
    private final static String STAFF = "staff";

    private static final List<String> needFillBoardCardNumDetailCode = Lists.newArrayList("HDXL00005");


    @Override
    public void checkCaseList(List<MarketingPlanCaseVo> list, String years, String schemeCode, String originalSchemeCode, String cacheKey) {
        //查询费用项目控制
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(MARKETING_CASE_DETAIL_CODE);
        List<String> displayDetailCodeList = dictDataVos.stream().filter(x -> DISPLAY.equals(x.getParentDictCode()))
                .map(l -> l.getDictCode()).collect(Collectors.toList());
        List<String> staffDetailCodeList = dictDataVos.stream().filter(x -> STAFF.equals(x.getParentDictCode()))
                .map(l -> l.getDictCode()).collect(Collectors.toList());
        //获取配置模板信息
        List<ActivitiesTemplateConfigDetailVo> templateFiledList = checkHelper.getTemplateFiled(MarketingPlanCaseTypeEnum.display.getCode());
        Map<String, Boolean> filedMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getRequired));
        Map<String, String> chineseMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getTitle));
        //获取活动细类配置
        Set<String> detailCodeSet = checkHelper.getTemplateRelateDetailCodes(MarketingPlanCaseTypeEnum.display.getCode());
        List<String> originalSchemeDetailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOriginalSchemeDetailCode()))
                .map(x -> x.getOriginalSchemeDetailCode()).collect(Collectors.toList());
        Map<String, MarketingPlanCaseVo> originCaseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(originalSchemeDetailCodes)) {
            List<MarketingPlanCaseVo> marketingPlanCaseVos = checkHelper.findCaseListBySchemeDetailCodes(originalSchemeDetailCodes);
            originCaseMap = marketingPlanCaseVos.stream().collect(Collectors.toMap(MarketingPlanCaseVo::getSchemeDetailCode, Function.identity()));
        }

        List<String> itemCodes = list.stream().filter(x -> CollectionUtils.isNotEmpty(x.getItemList())).map(k -> k.getItemList().get(0).getCode()).distinct().collect(Collectors.toList());

        //标记分组
        Map<String, Long> muchDepartmentMap = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMuchDepartmentMark()))
                .collect(Collectors.groupingBy(MarketingPlanCaseVo::getMuchDepartmentMark, Collectors.counting()));
        if (ObjectUtils.isNotEmpty(muchDepartmentMap)) {
            muchDepartmentMap = muchDepartmentMap.entrySet().stream()
                    .filter(entry -> entry.getValue() == 1)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        //查询组织的成本中心
        Map<String, Set<String>> orgCostCenterMap = checkHelper.findOrgCostCenter(list);
        Set<String> costCenterCodeSet = orgCostCenterMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        Set<String> yearSet = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getYears()))
                .map(MarketingPlanCaseVo::getYears).collect(Collectors.toSet());
        List<String> customerCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()))
                .map(MarketingPlanCaseVo::getCustomerCode).collect(Collectors.toList());
        List<String> excludeSchemeCode = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(schemeCode)) {
            excludeSchemeCode.add(schemeCode);
        }
        if (ObjectUtils.isNotEmpty(originalSchemeCode)) {
            excludeSchemeCode.add(originalSchemeCode);
        }
        List<MarketingSalesPlanVo> salesPlanVos = Lists.newArrayList();
        String salesCacheKey = cacheKey + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + years;
        List<MarketingSalesPlanVo> salesPlanList1 = marketingSalesPlanService.findCacheList(salesCacheKey);
        List<MarketingSalesPlanVo> salesPlanList2 = marketingSalesPlanService.findListByConditionToMarketingScheme(yearSet, costCenterCodeSet, Sets.newHashSet(customerCodes),
                null, null, excludeSchemeCode, null, null);
        if (CollectionUtils.isNotEmpty(salesPlanList1)) {
            salesPlanVos.addAll(salesPlanList1);
        }
        if (CollectionUtils.isNotEmpty(salesPlanList2)) {
            salesPlanVos.addAll(salesPlanList2);
        }
        Map<String, List<MarketingSalesPlanVo>> salesPlanMap = salesPlanVos.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getCostCenterCode()))
                .collect(Collectors.groupingBy(MarketingSalesPlanVo::getCostCenterCode));
        //查询客户
        List<CustomerVo> customerVoList = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerVoMap = customerVoList.stream().filter(x -> EnableStatusEnum.ENABLE.getCode().equals(x.getEnableStatus()) &&
                        BooleanEnum.TRUE.getCapital().equals(x.getContractCustomer()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
        Set<String> companyCodeSet = Sets.newHashSet();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(customerVoList)) {
            companyCodeSet = customerVoList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getCompanyCode()))
                    .map(CustomerVo::getCompanyCode).collect(Collectors.toSet());
        }
        //查询终端
        List<String> terminalCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTerminalCode()))
                .map(MarketingPlanCaseVo::getTerminalCode).collect(Collectors.toList());
        List<TerminalVo> terminalVoList = terminalVoService.findTerminalAndRelaCusByTerminalCodeList(terminalCodes);
        Map<String, TerminalVo> terminalMap = terminalVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTerminalCode()))
                .collect(Collectors.toMap(TerminalVo::getTerminalCode, Function.identity()));

        List<String> productCodes = Lists.newArrayList();
        List<String> items = Lists.newArrayList();
        for (MarketingPlanCaseVo vo : list) {
            if (CollectionUtils.isNotEmpty(vo.getProductList())) {
                productCodes.addAll(vo.getProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(vo.getItemList())) {
                items.addAll(vo.getItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toSet()));
            }
        }
        //品项信息
        Map<String, String> itemMap = checkHelper.findSapLevelMap(items);
        Map<String, ProductVo> productMap = checkHelper.findProductListByCodes(productCodes, companyCodeSet, items);
        Map<String, ProductPhaseVo> productPhaseMap = checkHelper.findProductPhaseMap(itemCodes);

        Map<String, MarketingPlanCaseVo> replaceMap = Maps.newHashMap();
        for (MarketingPlanCaseVo caseVo : list) {
            StringJoiner errMsg = new StringJoiner(";");
            List<MarketingPlanProductVo> itemList = caseVo.getItemList();
            if (CollectionUtils.isNotEmpty(itemList)) {
                itemList.forEach(v -> {
                    ProductPhaseVo productPhaseVo = productPhaseMap.get(v.getCode());
                    if (Objects.nonNull(productPhaseVo)) {
                        v.setTaxRate(productPhaseVo.getTaxRate());
                    }
                });
                caseVo.setItemList(itemList);
            }

            //判断是变更的
            if (ObjectUtils.isNotEmpty(caseVo.getOriginalSchemeDetailCode())) {
                MarketingPlanCaseVo originCaseVo = originCaseMap.get(caseVo.getOriginalSchemeDetailCode());
                originCaseVo.setId(caseVo.getId());
                originCaseVo.setSchemeCode(caseVo.getSchemeCode());
                originCaseVo.setSchemeDetailCode(caseVo.getSchemeDetailCode());
                originCaseVo.setOriginalSchemeDetailCode(caseVo.getOriginalSchemeDetailCode());
                originCaseVo.setOriginalSchemeCode(caseVo.getOriginalSchemeCode());

                LocalDate now = LocalDate.now();
                LocalDate startDate = LocalDate.parse(originCaseVo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                LocalDate endDate = LocalDate.parse(caseVo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
                //如果开始时间晚于当前时间
                if (now.isBefore(startDate)) {
                    originCaseVo.setStartDate(caseVo.getStartDate());
                }
                //如果结束时间早于当前时间
                if (now.isAfter(endDate)) {
                    errMsg.add("结束时间不能早于当前时间");
                } else {
                    originCaseVo.setEndDate(caseVo.getEndDate());
                }
                originCaseVo.setCashType(caseVo.getCashType());
                //费用使用部门
                originCaseVo.setBelongDepartmentCode(caseVo.getBelongDepartmentCode());
                originCaseVo.setBelongDepartmentName(caseVo.getBelongDepartmentName());

                //根据费用项目进行判断
                if (displayDetailCodeList.contains(caseVo.getDetailCode())) {

                } else if (staffDetailCodeList.contains(caseVo.getDetailCode())) {
                    if (checkHelper.paramNotNull(caseVo.getMonthPos(), "规划POS", errMsg)) {
                        checkHelper.isMatchNum(caseVo.getMonthPos(), "规划POS", errMsg, 2);
                        originCaseVo.setMonthPos(caseVo.getMonthPos());
                    }
                    if (checkHelper.paramNotNull(caseVo.getAttendanceDay(), "出勤天数", errMsg)) {
                        checkHelper.isMatchNum(caseVo.getAttendanceDay(), "出勤天数", errMsg, 1);
                        originCaseVo.setAttendanceDay(caseVo.getAttendanceDay());
                    }
                    if (checkHelper.paramNotNull(caseVo.getBasicSalary(), "底薪", errMsg)) {
                        checkHelper.isMatchNum(caseVo.getBasicSalary(), "底薪", errMsg, 2);
                        originCaseVo.setBasicSalary(caseVo.getBasicSalary());
                    }
                    checkHelper.paramNotNull(caseVo.getStaffType(),"人员类型",errMsg);
                    originCaseVo.setStaffType(caseVo.getStaffType());
                }

                if (displayDetailCodeList.contains(caseVo.getDetailCode()) || staffDetailCodeList.contains(caseVo.getDetailCode())) {
                    //部门验证
                    if (checkHelper.paramNotNull(caseVo.getTerminalCode(), "门店编码", errMsg)) {
                        if (terminalMap.containsKey(caseVo.getTerminalCode())) {
                            TerminalVo terminalVo = terminalMap.get(caseVo.getTerminalCode());
                            originCaseVo.setTerminalCode(caseVo.getTerminalCode());
                            originCaseVo.setTerminalName(terminalVo.getTerminalName());
                            originCaseVo.setTerminalType(terminalVo.getTerminalType());
                            caseVo.setTerminalChannel(terminalVo.getChannel());
                            caseVo.setTerminalSystem(terminalVo.getSourceSystem());
                            if (checkHelper.collectionNotNull("终端关联客户", errMsg, terminalVo.getRelaCusVos())) {
                                List<String> relaCusList = terminalVo.getRelaCusVos().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCusCode()))
                                        .map(TerminalRelaCusVo::getCusCode).collect(Collectors.toList());
                                if (!relaCusList.contains(caseVo.getCustomerCode())) {
                                    errMsg.add(String.format("终端关联客户信息不存在当前客户%s", caseVo.getCustomerCode()));
                                }
                            }
                        } else {
                            errMsg.add(String.format("终端信息不存在%s", caseVo.getTerminalCode()));
                        }
                    }
                }
                //执行要求描述
                originCaseVo.setExecuteDesc(caseVo.getExecuteDesc());
                originCaseVo.setActDesc(caseVo.getActDesc());
                //执行、结案示例
                originCaseVo.setExecuteExample(caseVo.getExecuteExample());
                originCaseVo.setExecuteExampleList(caseVo.getExecuteExampleList());
                originCaseVo.setCloseCaseExample(caseVo.getCloseCaseExample());
                originCaseVo.setCloseCaseExampleList(caseVo.getCloseCaseExampleList());
                if (CollectionUtils.isNotEmpty(caseVo.getCostBasisList())) {
                    originCaseVo.setCostBasisList(caseVo.getCostBasisList());
                    originCaseVo.setCostBasis(caseVo.getCostBasis());
                }
                originCaseVo.setCostBasis(caseVo.getCostBasis());
                if (checkHelper.paramNotNull(caseVo.getApplyAmount(), "费用合计", errMsg)) {
                    originCaseVo.setApplyAmount(caseVo.getApplyAmount());
                    if (Lists.newArrayList("wire_transfer", "deductions").contains(caseVo.getCashType())) {
                        originCaseVo.setLineTaxRate(new BigDecimal("0.06"));
                        originCaseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(originCaseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                    } else if (Lists.newArrayList("ticket_buckle", "replenishment").contains(caseVo.getCashType())) {
                        String code = caseVo.getItemList().get(0).getCode();
                        if (StringUtils.isNotEmpty(code) && Lists.newArrayList("BC1001", "BC1002").contains(code)) {
                            originCaseVo.setLineTaxRate(new BigDecimal("0.1"));
                            originCaseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(originCaseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                        } else {
                            ProductPhaseVo productPhaseVo = productPhaseMap.get(code);
                            if (Objects.isNull(productPhaseVo) || Objects.isNull(productPhaseVo.getTaxRate())) {
                                originCaseVo.setErrMsg("未匹配到对应品项或对应品项未维护税率");
                                originCaseVo.setCheckFlag(Boolean.FALSE);
                            } else {
                                originCaseVo.setLineTaxRate(productPhaseVo.getTaxRate());
                                originCaseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(originCaseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                            }
                        }
                    }
                }

                if (Objects.nonNull(originCaseVo.getApplyAmount()) && originCaseVo.getApplyAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    errMsg.add("申请金额必须大于0");
                }

                if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                    originCaseVo.setCheckFlag(Boolean.FALSE);
                    originCaseVo.setErrMsg(errMsg.toString());
                } else {
                    originCaseVo.setCheckFlag(Boolean.TRUE);
                }

                //计算费率
                Set<String> costCenterCodes = orgCostCenterMap.getOrDefault(originCaseVo.getBelongDepartmentCode(), Sets.newHashSet());
                checkHelper.calPlanCaseRatio(salesPlanMap, costCenterCodes, originCaseVo);

                replaceMap.put(caseVo.getOriginalSchemeDetailCode(), originCaseVo);
                continue;
            }

            if (ObjectUtils.isNotEmpty(caseVo.getErrMsg())) {
                errMsg.add(caseVo.getErrMsg());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getDetailCode())) {
                if (!detailCodeSet.contains(caseVo.getDetailCode())) {
                    errMsg.add("费用项目不属于当前表单模板配置");
                }
            }
            //根据费用项目进行判断
            if (displayDetailCodeList.contains(caseVo.getDetailCode())) {

            } else if (staffDetailCodeList.contains(caseVo.getDetailCode())) {
                if (checkHelper.paramNotNull(caseVo.getMonthPos(), "规划POS", errMsg)) {
                    checkHelper.isMatchNum(caseVo.getMonthPos(), "规划POS", errMsg, 2);
                }
                if (checkHelper.paramNotNull(caseVo.getAttendanceDay(), "出勤天数", errMsg)) {
                    checkHelper.isMatchNum(caseVo.getAttendanceDay(), "出勤天数", errMsg, 1);
                }
                if (checkHelper.paramNotNull(caseVo.getBasicSalary(), "底薪", errMsg)) {
                    checkHelper.isMatchNum(caseVo.getBasicSalary(), "底薪", errMsg, 2);
                }
                checkHelper.paramNotNull(caseVo.getStaffType(),"人员类型",errMsg);
            }
            if (checkHelper.paramNotNull(caseVo.getCustomerCode(), "客户编码", errMsg)) {
                if (customerVoMap.containsKey(caseVo.getCustomerCode())) {
                    CustomerVo customerVo = customerVoMap.get(caseVo.getCustomerCode());
                    caseVo.setCustomerName(customerVo.getCustomerName());
                    caseVo.setErpCode(customerVo.getErpCode());
                    caseVo.setCompanyCode(customerVo.getCompanyCode());
                    caseVo.setProductGroupCode(customerVo.getProductGroupCode());
                    caseVo.setChannelCode(customerVo.getChannelCode());
                    caseVo.setCooperateType(customerVo.getCooperateType());
                    caseVo.setHzlx(customerVo.getHzlx());
                    //校验客户公司代码与成本中心公司代码是否一致
                    if (ObjectUtils.isNotEmpty(caseVo.getCostCenterCompanyCode()) && !customerVo.getCompanyCode().equals(caseVo.getCostCenterCompanyCode())) {
                        errMsg.add(String.format("客户公司代码与成本中心公司代码不一致,客户公司代码%s,成本中心公司代码%s", caseVo.getCompanyCode(), caseVo.getCostCenterCompanyCode()));
                    }
                } else {
                    errMsg.add(String.format("客户编码%s在主数据中未查询到或不是合同客户", caseVo.getCustomerCode()));
                }
            }
            if (displayDetailCodeList.contains(caseVo.getDetailCode()) || staffDetailCodeList.contains(caseVo.getDetailCode())) {
                if (checkHelper.paramNotNull(caseVo.getTerminalCode(), "门店编码", errMsg)) {
                    if (terminalMap.containsKey(caseVo.getTerminalCode())) {
                        TerminalVo terminalVo = terminalMap.get(caseVo.getTerminalCode());
                        caseVo.setTerminalName(terminalVo.getTerminalName());
                        caseVo.setTerminalType(terminalVo.getTerminalType());
                        caseVo.setTerminalChannel(terminalVo.getChannel());
                        caseVo.setTerminalSystem(terminalVo.getSourceSystem());
                        if (checkHelper.collectionNotNull("终端关联客户", errMsg, terminalVo.getRelaCusVos())) {
                            List<String> relaCusList = terminalVo.getRelaCusVos().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCusCode()))
                                    .map(TerminalRelaCusVo::getCusCode).collect(Collectors.toList());
                            if (!relaCusList.contains(caseVo.getCustomerCode())) {
                                errMsg.add(String.format("终端关联客户信息不存在当前客户%s", caseVo.getCustomerCode()));
                            }
                        }
                    } else {
                        errMsg.add(String.format("终端信息不存在%s", caseVo.getTerminalCode()));
                    }
                }
            }
            if (checkHelper.collectionNotNull("品项", errMsg, caseVo.getItemList())) {
                for (MarketingPlanProductVo vo : caseVo.getItemList()) {
                    if (itemMap.containsKey(vo.getCode())) {
                        vo.setName(itemMap.get(vo.getCode()));
                    } else {
                        errMsg.add(String.format("品项%s在主数据中未查询到", vo.getCode()));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                for (MarketingPlanProductVo vo : caseVo.getProductList()) {
                    if (productMap.containsKey(vo.getCode())) {
                        if (productMap.containsKey(vo.getCode())) {
                            ProductVo productVo = productMap.get(vo.getCode());
                            vo.setName(productVo.getProductName());
                            Validate.notNull(productVo.getMaterialCode(), String.format("商品%s未存在绑定的物料", productVo.getProductName()));
                            vo.setMaterialCode(productVo.getMaterialCode());
                        } else {
                            errMsg.add(String.format("商品%s不属于品项关联的商品", vo.getCode()));
                        }
                    } else {
                        errMsg.add(String.format("商品%s在主数据中未查询到", vo.getCode()));
                    }
                }
            }
//            checkHelper.paramNotNull(caseVo.getExecuteDesc(), "执行描述", errMsg);
            checkHelper.paramNotNull(caseVo.getApplyAmount(), "申请金额", errMsg);

            if (Objects.nonNull(caseVo.getApplyAmount()) && caseVo.getApplyAmount().compareTo(BigDecimal.ZERO) <= 0) {
                errMsg.add("申请金额必须大于0");
            }
//            checkHelper.paramNotNull(caseVo.getActDesc(), "活动描述", errMsg);
            caseVo.setMuchDepartmentFlag(BooleanEnum.FALSE.getCapital());
            //多部门标记
            if (ObjectUtils.isNotEmpty(caseVo.getMuchDepartmentMark())) {
                caseVo.setMuchDepartmentFlag(BooleanEnum.TRUE.getCapital());
            }
            if (ObjectUtils.isNotEmpty(muchDepartmentMap) && muchDepartmentMap.containsKey(caseVo.getMuchDepartmentMark())) {
                errMsg.add("多部门标记只找到一个,不可进行多部门标记");
            }
            //此处单独调用
            String msg = BusinessRequiredUtil.filedRequiredCheck(filedMap, chineseMap, caseVo);
            if (ObjectUtils.isNotEmpty(msg)) {
                errMsg.add(msg);
            }
            if (StringUtils.isNotEmpty(caseVo.getDetailCode()) && needFillBoardCardNumDetailCode.contains(caseVo.getDetailCode()) && caseVo.getDisplayCardNum() == null) {
                errMsg.add("选择陈列发布费（新零售）时需填写陈列卡板数");
            }

            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                caseVo.setCheckFlag(Boolean.FALSE);
                caseVo.setErrMsg(errMsg.toString());
            }
            if (caseVo.getCheckFlag()) {
                //计算费率
                Set<String> costCenterCodes = orgCostCenterMap.getOrDefault(caseVo.getBelongDepartmentCode(), Sets.newHashSet());
                checkHelper.calPlanCaseRatio(salesPlanMap, costCenterCodes, caseVo);
            }
            if (Lists.newArrayList("wire_transfer", "deductions").contains(caseVo.getCashType())) {
                caseVo.setLineTaxRate(new BigDecimal("0.06"));
                caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
            } else if (Lists.newArrayList("ticket_buckle", "replenishment").contains(caseVo.getCashType())) {
                String code = caseVo.getItemList().get(0).getCode();
                if (StringUtils.isNotEmpty(code) && Lists.newArrayList("BC1001", "BC1002").contains(code)) {
                    caseVo.setLineTaxRate(new BigDecimal("0.1"));
                    caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                } else {
                    ProductPhaseVo productPhaseVo = productPhaseMap.get(code);
                    if (Objects.isNull(productPhaseVo) || Objects.isNull(productPhaseVo.getTaxRate())) {
                        caseVo.setErrMsg("未匹配到对应品项或对应品项未维护税率");
                        caseVo.setCheckFlag(Boolean.FALSE);
                    } else {
                        caseVo.setLineTaxRate(productPhaseVo.getTaxRate());
                        caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                    }
                }
            }
            //寻年度预算
            checkHelper.matchBudget(filedMap, chineseMap, caseVo);
        }

        //进行替换操作
        if (ObjectUtils.isNotEmpty(replaceMap)) {
            list.replaceAll(x -> {
                String originalCode = x.getOriginalSchemeDetailCode();
                return (originalCode != null && !originalCode.isEmpty() && replaceMap.containsKey(originalCode))
                        ? replaceMap.get(originalCode)
                        : x;
            });
        }
        list.forEach(x -> x.setCaseType(getCaseType()));
        //校验多部门
        checkHelper.checkMuchDepartmentMark(list);
    }

    @Override
    public String getCaseType() {
        return MarketingPlanCaseTypeEnum.display.getCode();
    }
}
