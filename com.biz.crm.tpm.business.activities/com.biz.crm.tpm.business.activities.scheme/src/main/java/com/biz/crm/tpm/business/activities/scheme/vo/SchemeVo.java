package com.biz.crm.tpm.business.activities.scheme.vo;

import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.Validate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Vo：方案;
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "Scheme", description = "方案")
@Getter
@Setter
public class SchemeVo implements Serializable {
  /**
   * 操作的预授权标记
   */
  @ApiModelProperty(name = "操作的预授权标记", notes = "操作的预授权标记", value = "操作的预授权标记", required = true)
  private String prefix;
  /**
   * 主键
   */
  @ApiModelProperty(name = "id", notes = "主键", value = "主键")
  private String id;
  /**
   * 备注
   */
  @ApiModelProperty(name = "remark", notes = "备注", value = "备注")
  private String remark;
  /**
   * 创建人账号
   */
  @ApiModelProperty(name = "createAccount", notes = "创建人账号", value = "创建人账号")
  private String createAccount;
  /**
   * 租户编号
   */
  @ApiModelProperty(name = "tenantCode", notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /**
   * 创建时间
   */
  @ApiModelProperty(name = "createTime", notes = "创建时间", value = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /**
   * 创建人名称
   */
  @ApiModelProperty(name = "createName", notes = "创建人名称", value = "创建人名称")
  private String createName;
  /**
   * 修改时间
   */
  @ApiModelProperty(name = "modifyTime", notes = "修改时间", value = "修改时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date modifyTime;
  /**
   * 修改人账号
   */
  @ApiModelProperty(name = "modifyAccount", notes = "修改人账号", value = "修改人账号")
  private String modifyAccount;
  /**
   * 修改人名称
   */
  @ApiModelProperty(name = "modifyName", notes = "修改人名称", value = "修改人名称")
  private String modifyName;
  /**
   * 数据状态（删除状态）
   */
  @ApiModelProperty(name = "delFlag", notes = "数据状态（删除状态）", value = "数据状态（删除状态）")
  private String delFlag;
  /**
   * 方案编号
   */
  @ApiModelProperty(name = "schemeCode", notes = "方案编号", value = "方案编号")
  private String schemeCode;
  /**
   * 方案名称
   */
  @ApiModelProperty(name = "schemeName", notes = "方案名称", value = "方案名称")
  private String schemeName;
  /**
   * 方案类型
   */
  @ApiModelProperty(name = "schemeType", notes = "方案类型", value = "方案类型")
  private String schemeType;
  /**
   * 方案开始时间
   */
  @ApiModelProperty(name = "schemeBeginTime", notes = "方案开始时间", value = "方案开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date schemeBeginTime;
  /**
   * 方案结束时间
   */
  @ApiModelProperty(name = "schemeEndTime", notes = "方案结束时间", value = "方案结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date schemeEndTime;
  /**
   * 方案说明
   */
  @ApiModelProperty(name = "schemeDescription", notes = "方案说明", value = "方案说明")
  private String schemeDescription;
  /**
   * 方案状态
   */
  @ApiModelProperty(name = "schemeStatus", notes = "方案状态（01:待执行, 02:执行中,:03:已结束）", value = "方案状态（01:待执行, 02:执行中,:03:已结束）")
  private String schemeStatus;
  /**
   * 方案文件附件
   */
  @ApiModelProperty(name = "schemeFilesVos", notes = "方案文件附件", value = "方案文件附件")
  private Set<SchemeFilesVo> schemeFilesVos;
  /**
   * 方案商品
   */
  @ApiModelProperty(name = "schemeProductVos", notes = "方案商品", value = "方案商品")
  private Set<SchemeProductVo> schemeProductVos;
  /**
   * 方案费用预算
   */
  @ApiModelProperty(name = "schemeCostBudgetVos", notes = "方案费用预算", value = "方案费用预算")
  private Set<CostBudgetVo> schemeCostBudgetVos;
  /**
   * 方案范围
   */
  @ApiModelProperty(name = "schemeRangeVos", notes = "方案范围", value = "方案范围")
  private Set<SchemeRangeVo> schemeRangeVos;

  @ApiModelProperty("回显编码，字符串，一般用于单选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这条数据")
  private String selectedCode;
  @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCodeList;

  /**
   * 根据开始时间与结束时间进行方案状态的判定
   *
   * @return
   */
  public String getSchemeStatus() {
    LocalDateTime now = LocalDateTime.now();
    Validate.notNull(this.getSchemeBeginTime(), "方案开始时间错误，请检查！");
    Validate.notNull(this.getSchemeEndTime(), "方案结束时间错误，请检查！");
    LocalDateTime begin = this.getSchemeBeginTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    LocalDateTime end = this.getSchemeEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    Validate.isTrue(end.isAfter(begin), "方案结束时间早于方案开始时间，请检查！");
    if (now.isBefore(begin)) {
      this.schemeStatus = "01";
    } else if (now.isAfter(begin) && now.isBefore(end)) {
      this.schemeStatus = "02";
    } else if (now.isAfter(end)) {
      this.schemeStatus = "03";
    }
    return schemeStatus;
  }
}