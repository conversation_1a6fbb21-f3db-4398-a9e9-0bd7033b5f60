package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosureDetail;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanClosureDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:23
 */
@Component
@Slf4j
public class PlanClosureDetailRepository extends ServiceImpl<PlanClosureDetailMapper, PlanClosureDetail> {

    public List<PlanClosureDetail> findListByCloseCodes(List<String> closeCodes) {
        return this.lambdaQuery()
                .in(PlanClosureDetail::getCloseCode, closeCodes)
                .eq(PlanClosureDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    public void deleteByCloseCodes(List<String> closeCodes) {
        this.lambdaUpdate()
                .in(PlanClosureDetail::getCloseCode, closeCodes)
                .remove();
    }

    public List<PlanClosureDetail> findBySchemeDetailCodeList(List<String> schemeDetailCodes) {
        return this.lambdaQuery()
                .in(PlanClosureDetail::getSchemeDetailCode, schemeDetailCodes)
                .eq(PlanClosureDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }
}
