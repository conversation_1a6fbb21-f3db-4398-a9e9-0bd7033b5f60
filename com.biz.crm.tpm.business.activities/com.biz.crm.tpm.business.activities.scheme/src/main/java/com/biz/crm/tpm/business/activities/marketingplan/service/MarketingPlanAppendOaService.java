package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.workflow.sdk.dto.OaCallbackDto;

public interface MarketingPlanAppendOaService {

    /**
     * 推送OA
     *
     * @param
     * @return
     */
    JSONObject pushOa(MarketingPlanVo plan);

    /**
     * 重新提交OA
     *
     * @param
     * @return
     */
    JSONObject resubmitOa(MarketingPlanVo plan);

    /**
     * OA撤回
     *
     * @param
     * @return
     */
    boolean oaWithdraw(String code, String remark);
}
