package com.biz.crm.tpm.business.activities.marketingplan.repository;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanExecution;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanExceutionMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanExecutionDto;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 执行计划(PlanExceution)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-02 20:06:33
 */
@Component
public class PlanExceutionRepository extends ServiceImpl<PlanExceutionMapper, PlanExecution> {

    @Autowired
    private PlanExceutionMapper planExceutionMapper;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    public List<PlanExecutionVo> findActExecuteCode(String code) {
        List<PlanExecution> list = lambdaQuery().eq(PlanExecution::getActExecuteCode, code).list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, PlanExecution.class, PlanExecutionVo.class, LinkedHashSet.class, ArrayList.class));
    }

    public List<PlanExecutionVo> findBySchemeDetailCode(String schemeDetailCode) {
        List<PlanExecution> list = lambdaQuery().eq(PlanExecution::getSchemeDetailCode, schemeDetailCode).list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, PlanExecution.class, PlanExecutionVo.class, LinkedHashSet.class, ArrayList.class));
    }

    //TODO 业务侧修改，同一步骤下可重复对关联该终端的多条明细数据进行执行采集，该方法会导致查询报错，弃用
//  public PlanExecutionVo findByDynamicKey(String parentCode, String dynamicKey) {
//    PlanExecution one = lambdaQuery().eq(PlanExecution::getParentCode, parentCode)
//            .eq(PlanExecution::getDynamicKey, dynamicKey).one();
//    return one == null ? null : nebulaToolkitService.copyObjectByWhiteList(one, PlanExecutionVo.class, LinkedHashSet.class, ArrayList.class);
//  }

    /**
     * 供步骤组件查询执行历史记录使用，因业务侧修改，该方法现在仅查询并至多返回一条数据，仅用于SFA步骤组件在查看历史数据时有返回数据，正常执行（该返回数据无实际意义）
     *
     * @param parentCode
     * @param dynamicKey
     * @return
     */
    public PlanExecutionVo findByDynamicKey(String parentCode, String dynamicKey) {
        List<PlanExecution> list = lambdaQuery().eq(PlanExecution::getParentCode, parentCode)
                .eq(PlanExecution::getDynamicKey, dynamicKey).list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        PlanExecution one = list.get(0);
        return one == null ? null : nebulaToolkitService.copyObjectByWhiteList(one, PlanExecutionVo.class, LinkedHashSet.class, ArrayList.class);

    }

    public PlanExecution findDetail(PlanExecutionDto dto) {
//    if (StringUtils.isBlank(dto.getSchemeCode()) || StringUtils.isBlank(dto.getSchemeDetailCode()) || StringUtils.isBlank(dto.getTerminalCode())){
//      return null;
//    }
        return this.lambdaQuery()
                .eq(PlanExecution::getSchemeDetailCode, dto.getSchemeDetailCode())
                .eq(PlanExecution::getTerminalCode, dto.getTerminalCode())
                .eq(PlanExecution::getCreatePosCode, dto.getCreatePosCode())
                .eq(PlanExecution::getExecutionTime, dto.getExecutionTime())
                .eq(PlanExecution::getDynamicKey, dto.getDynamicKey())
                .eq(PlanExecution::getDynamicFormCode, dto.getDynamicFormCode())
                .eq(PlanExecution::getParentCode, dto.getParentCode())
                .one();
    }

    public PlanExecution findById(String id) {
        return this.lambdaQuery()
                .eq(PlanExecution::getId, id)
                .one();
    }

    /**
     * 活动执行采集分页接口
     * @param pageable
     * @param dto
     * @return
     */
    public Page<PlanExecutionVo> findByConditions(Pageable pageable, PlanExecutionDto dto){
        Page<PlanExecutionVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findByConditions(page,dto);
    }
}

