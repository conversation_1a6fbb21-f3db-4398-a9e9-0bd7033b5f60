package com.biz.crm.tpm.business.activities.regioncollect.helper;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterOrgVo;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanCaseRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCustomerGainsAbstractBuilder;
import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.service.strategy.DepartmentEstimationAbstractBuilder;
import com.biz.crm.tpm.business.activities.regioncollect.service.strategy.ItemEstimationAbstractBuilder;
import com.biz.crm.tpm.business.activities.regioncollect.service.strategy.MarketingEstimationAbstractBuilder;
import com.biz.crm.tpm.business.activities.regioncollect.vo.*;
import com.biz.crm.tpm.business.rebate.sdk.vo.SalesPlanQueryVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.elasticsearch.action.search.SearchTask;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/25 14:38
 */
@Component
@Slf4j
public class RegionCollectHelper {

    @Resource
    private ApplicationContext context;

    @Resource
    private MarketingPlanCaseRepository caseRepository;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private MarketingPlanProductRepository productRepository;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    @Resource
    private MdmCostCenterVoService costCenterVoService;

    /**
     * 组装汇总方案
     *
     * @param planVos
     * @param collectCode
     * @return
     */
    public static List<RegionCollectSchemeVo> buildCollectScheme(List<MarketingPlanVo> planVos, String collectCode) {
        List<RegionCollectSchemeVo> collectSchemeList = planVos.stream().map(x -> {
            RegionCollectSchemeVo scheme = new RegionCollectSchemeVo();
            scheme.setCollectCode(collectCode);
            scheme.setSchemeCode(x.getSchemeCode());
            scheme.setSchemeName(x.getSchemeName());
            scheme.setUserName(x.getCreateAccount());
            scheme.setFullName(x.getCreateName());
            scheme.setOrgCode(x.getDepartmentCode());
            scheme.setOrgName(x.getDepartmentName());
            return scheme;
        }).collect(Collectors.toList());
        return collectSchemeList;
    }

    /**
     * 构建客户损益
     *
     * @param schemeCodes
     * @param years
     * @return
     */
    public List<RegionCollectGainsAndLossesVo> buildCustomerGainsAndLosses(List<String> schemeCodes, String years) {
        List<MarketingPlanCase> caseList = caseRepository.findListBySchemeCodes(schemeCodes);
        //过滤有空值的情况
        caseList = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()) && ObjectUtils.isNotEmpty(x.getBelongDepartmentCode())
                && ObjectUtils.isNotEmpty(x.getYears())).collect(Collectors.toList());
        List<String> indexKeyList = caseList.stream().map(x -> x.getCustomerCode() + ":" + x.getBelongDepartmentCode() + ":" + years)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(indexKeyList)) {
            return Lists.newArrayList();
        }


        List<String> customerCodes = caseList.stream().map(MarketingPlanCase::getCustomerCode).distinct().collect(Collectors.toList());
        List<String> cusOrgCodes = caseList.stream().map(x -> x.getCustomerCode() + x.getBelongDepartmentCode()).distinct().collect(Collectors.toList());

        //查询销售计划
        List<MarketingSalesPlanVo> salePlanList = marketingSalesPlanService.findListBySchemeCodes(schemeCodes);
        //查询成本中心
        List<String> costCenterCodes = salePlanList.stream().map(MarketingSalesPlanVo::getCostCenterCode).distinct().collect(Collectors.toList());

        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(costCenterCodes);
        Validate.isTrue(CollectionUtils.isNotEmpty(costCenterVoList),"查询成本中心数据为空");
        Map<String, MdmCostCenterOrgVo> costCenterMap = costCenterVoList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getOrgList()))
                .collect(Collectors.toMap(x -> x.getCostCenterCode(), y -> y.getOrgList().get(0)));
        for (MarketingSalesPlanVo vo : salePlanList) {
            if (costCenterMap.containsKey(vo.getCostCenterCode())) {
                MdmCostCenterOrgVo costCenterOrgVo = costCenterMap.get(vo.getCostCenterCode());
                vo.setCostCenterOrgCode(costCenterOrgVo.getOrgCode());
                vo.setCostCenterOrgName(costCenterOrgVo.getOrgName());
            }
        }

        List<String> salesCustomerOrgList = salePlanList.stream().map(x -> x.getCustomerCode() + x.getCostCenterOrgCode()).distinct().collect(Collectors.toList());

        List<String> differenceList = salesCustomerOrgList.stream().filter(x -> !cusOrgCodes.contains(x)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(differenceList)) {
            customerCodes.addAll(salePlanList.stream().map(x -> x.getCustomerCode()).distinct().collect(Collectors.toList()));
        }

        List<CustomerVo> customerVoList = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerMap = customerVoList.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
        List<RegionCollectGainsAndLossesVo> dataList = new ArrayList<>();
        for (String s : indexKeyList) {
            String[] keys = s.split(":");
            RegionCollectGainsAndLossesVo data = new RegionCollectGainsAndLossesVo();
            data.setIndexKey(s);
            //基础数据设置
            data.setCustomerCode(keys[0]);
            data.setOrgCode(keys[1]);
            data.setYears(keys[2]);
            CustomerVo customerVo = customerMap.get(data.getCustomerCode());
            data.setCustomerName(customerVo.getCustomerName());
            data.setErpCode(customerVo.getErpCode());
            data.setCompanyCode(customerVo.getCompanyCode());
            data.setChannelCode(customerVo.getChannelCode());
            data.setProductGroupCode(customerVo.getProductGroupCode());
            data.setCalFlag(Boolean.TRUE);
            dataList.add(data);
        }

        Map<String, Set<String>> cusCostCenterMap = salePlanList.stream().filter(x -> differenceList.contains(x.getCustomerCode() + x.getCostCenterOrgCode()))
                .collect(Collectors.groupingBy(x -> x.getCustomerCode() + x.getCostCenterOrgCode(),
                        Collectors.mapping(MarketingSalesPlanVo::getCostCenterCode, Collectors.toSet())));

        Map<String, MarketingSalesPlanVo> salesCusCostCenterMap = salePlanList.stream().filter(x -> differenceList.contains(x.getCustomerCode() + x.getCostCenterOrgCode()))
                .collect(Collectors.toMap(x -> x.getCustomerCode() + ":" + x.getCostCenterCode(), Function.identity(), (a, b) -> a));

        for (Map.Entry<String, MarketingSalesPlanVo> entry : salesCusCostCenterMap.entrySet()) {
            String[] key = entry.getKey().split(":");
            String customerCode = key[0];
            String costCenterCdoe = key[1];

            RegionCollectGainsAndLossesVo data = new RegionCollectGainsAndLossesVo();
            data.setIndexKey(customerCode + ":" + entry.getValue().getCostCenterOrgCode() + ":" + years);
            data.setCustomerCode(customerCode);
            data.setOrgCode(entry.getValue().getCostCenterOrgCode());
            data.setOrgName(entry.getValue().getCostCenterOrgName());
            data.setYears(years);
            CustomerVo customerVo = customerMap.get(data.getCustomerCode());
            data.setCustomerName(customerVo.getCustomerName());
            data.setErpCode(customerVo.getErpCode());
            data.setCompanyCode(customerVo.getCompanyCode());
            data.setChannelCode(customerVo.getChannelCode());
            data.setProductGroupCode(customerVo.getProductGroupCode());
            data.setCalFlag(Boolean.FALSE);
            data.setCostCenterCodes(cusCostCenterMap.get(customerCode + data.getOrgCode()));
            dataList.add(data);
        }


        MarketingPlanCustomerGainsAbstractBuilder.builder(context, dataList, null,Sets.newHashSet(customerCodes))
                .init(schemeCodes, years)
                .loadInitData()
                .calPublicShareRatio()
                .calProfitRatio()
                .calGrossProfitRatio()
                .calLogisticsRatio()
                .calMarketingRatio()
                .calSecondCategory();

        Map<String, List<RegionCollectGainsAndLossesVo>> map = dataList.stream().collect(Collectors.groupingBy(x -> x.getCustomerCode()));
        List<RegionCollectGainsAndLossesVo> resultDataList = Lists.newArrayList();
        for (Map.Entry<String, List<RegionCollectGainsAndLossesVo>> entry : map.entrySet()) {
            if (entry.getValue().size() == 1) {
                resultDataList.addAll(entry.getValue());
            } else {
                RegionCollectGainsAndLossesVo data = new RegionCollectGainsAndLossesVo();
                data.setPublicShareRatio(entry.getValue().get(0).getPublicShareRatio());
                data.setCustomerCode(entry.getValue().get(0).getCustomerCode());
                data.setCustomerName(entry.getValue().get(0).getCustomerName());
                data.setYears(entry.getValue().get(0).getYears());
                BigDecimal budgetIncome = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getBudgetIncome()))
                        .map(RegionCollectGainsAndLossesVo::getBudgetIncome)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal planIncome = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanIncome()))
                        .map(RegionCollectGainsAndLossesVo::getPlanIncome)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal budgetTotalAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getBudgetTotalAmount()))
                        .map(RegionCollectGainsAndLossesVo::getBudgetTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal planTotalAmount = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanTotalAmount()))
                        .map(RegionCollectGainsAndLossesVo::getPlanTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal productionCosts = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductionCosts()))
                        .map(RegionCollectGainsAndLossesVo::getProductionCosts)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal productTransportCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductTransportCost()))
                        .map(RegionCollectGainsAndLossesVo::getProductTransportCost)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal peripheryTransportCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPeripheryTransportCost()))
                        .map(RegionCollectGainsAndLossesVo::getPeripheryTransportCost)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal marketingCost = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getMarketingCost()))
                        .map(RegionCollectGainsAndLossesVo::getMarketingCost)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal planProfitMargin = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanProfitMargin()))
                        .map(RegionCollectGainsAndLossesVo::getPlanProfitMargin)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal planGrossProfitMargin = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPlanGrossProfitMargin()))
                        .map(RegionCollectGainsAndLossesVo::getPlanGrossProfitMargin)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal promotion = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getPromotionAmount()))
                        .map(RegionCollectGainsAndLossesVo::getPromotionAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal callback = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCallbackAmount()))
                        .map(RegionCollectGainsAndLossesVo::getCallbackAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal display = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getDisplayAmount()))
                        .map(RegionCollectGainsAndLossesVo::getDisplayAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal generalization = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getGeneralizationAmount()))
                        .map(RegionCollectGainsAndLossesVo::getGeneralizationAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal disseminate = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getDisseminateAmount()))
                        .map(RegionCollectGainsAndLossesVo::getDisseminateAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal salesReward = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getSalesRewardAmount()))
                        .map(RegionCollectGainsAndLossesVo::getSalesRewardAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal contract = entry.getValue().stream().filter(x -> ObjectUtils.isNotEmpty(x.getContractAmount()))
                        .map(RegionCollectGainsAndLossesVo::getContractAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                data.setBudgetIncome(budgetIncome);
                data.setPlanIncome(planIncome);
                data.setBudgetTotalAmount(budgetTotalAmount);
                data.setPlanTotalAmount(planTotalAmount);
                data.setProductionCosts(productionCosts);
                data.setProductTransportCost(productTransportCost);
                data.setPeripheryTransportCost(peripheryTransportCost);
                data.setMarketingCost(marketingCost);
                data.setPlanProfitMargin(planProfitMargin);
                data.setPlanGrossProfitMargin(planGrossProfitMargin);
                if (ObjectUtils.isNotEmpty(planIncome) && planIncome.compareTo(BigDecimal.ZERO) > 0) {
                    data.setPromotion(promotion.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                    data.setCallback(callback.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                    data.setDisplay(display.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                    data.setGeneralization(generalization.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                    data.setDisseminate(disseminate.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                    data.setSalesReward(salesReward.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                    data.setContract(contract.divide(planIncome, 4, BigDecimal.ROUND_HALF_DOWN));
                }
                if (data.getBudgetIncome().compareTo(BigDecimal.ZERO) == 1) {
                    data.setIncomeAchieveRatio(data.getPlanIncome().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
                if (ObjectUtils.isNotEmpty(data.getBudgetTotalAmount()) && ObjectUtils.isNotEmpty(data.getBudgetIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) > 0) {
                    //预算总额/预算收入
                    data.setBudgetRatio(data.getBudgetTotalAmount().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
                if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && ObjectUtils.isNotEmpty(data.getPlanTotalAmount()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                    //规划总额/规划收入
                    data.setPlanRatio(data.getPlanTotalAmount().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
                //费率偏差 = 规划费率-预算费率
                if (ObjectUtils.isNotEmpty(data.getPlanRatio()) && ObjectUtils.isNotEmpty(data.getBudgetRatio())) {
                    data.setRatioDeviation(data.getPlanRatio().subtract(data.getBudgetRatio()));
                }
                if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                    data.setProfitRatio(data.getPlanProfitMargin().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
                if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                    data.setGrossProfitRatio(data.getPlanGrossProfitMargin().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
                if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                    data.setLogisticsRatio((data.getProductTransportCost().add(data.getPeripheryTransportCost())).divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
                if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                    data.setMarketingRatio(data.getMarketingCost().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
                resultDataList.add(data);
            }
        }

        for (CustomerGainsAndLossesVo vo : resultDataList) {
            if (ObjectUtils.isNotEmpty(vo.getPlanIncome())) {
                vo.setPlanIncome(vo.getPlanIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetIncome())) {
                vo.setBudgetIncome(vo.getBudgetIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanTotalAmount())) {
                vo.setPlanTotalAmount(vo.getPlanTotalAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if(ObjectUtils.isNotEmpty(vo.getBudgetTotalAmount())){
                vo.setBudgetTotalAmount(vo.getBudgetTotalAmount().divide(BigDecimal.valueOf(10000),2,BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getIncomeAchieveRatio())) {
                vo.setIncomeAchieveRatioStr(vo.getIncomeAchieveRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetRatio())) {
                vo.setBudgetRatioStr(vo.getBudgetRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanRatio())) {
                vo.setPlanRatioStr(vo.getPlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getRatioDeviation())) {
                vo.setRatioDeviationStr(vo.getRatioDeviation().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getProfitRatio())) {
                vo.setProfitRatioStr(vo.getProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGrossProfitRatio())) {
                vo.setGrossProfitRatioStr(vo.getGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getLogisticsRatio())) {
                vo.setLogisticsRatioStr(vo.getLogisticsRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getMarketingRatio())) {
                vo.setMarketingRatioStr(vo.getMarketingRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPublicShareRatio())) {
                vo.setPublicShareRatioStr(vo.getPublicShareRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPromotion())) {
                vo.setPromotionStr(vo.getPromotion().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getCallback())) {
                vo.setCallbackStr(vo.getCallback().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisplay())) {
                vo.setDisplayStr(vo.getDisplay().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGeneralization())) {
                vo.setGeneralizationStr(vo.getGeneralization().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisseminate())) {
                vo.setDisseminateStr(vo.getDisseminate().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getSalesReward())) {
                vo.setSalesRewardStr(vo.getSalesReward().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getContract())) {
                vo.setContractStr(vo.getContract().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
        }

        return resultDataList;
    }


    /**
     * 构建三级部门测算
     *
     * @param schemeCodes
     * @param years
     * @return
     */
    public List<RegionCollectDepartmentEstimationVo> buildDepartmentEstimation(List<String> schemeCodes, String years) {
        List<MarketingPlanCase> caseList = caseRepository.findListBySchemeCodes(schemeCodes);
        //过滤有空值的情况
        caseList = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()) && ObjectUtils.isNotEmpty(x.getYears())).collect(Collectors.toList());
        List<String> indexKeyList = caseList.stream().map(x -> x.getBelongDepartmentCode() + ":" + years)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(indexKeyList)) {
            return Lists.newArrayList();
        }
        List<String> orgCodes = caseList.stream().map(MarketingPlanCase::getBelongDepartmentCode).distinct().collect(Collectors.toList());
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
        Map<String, String> orgMap = orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        List<RegionCollectDepartmentEstimationVo> dataList = new ArrayList<>(indexKeyList.size());
        for (String s : indexKeyList) {
            String[] keys = s.split(":");
            RegionCollectDepartmentEstimationVo data = new RegionCollectDepartmentEstimationVo();
            data.setIndexKey(s);
            //基础数据设置
            data.setOrgCode(keys[0]);
            data.setYears(keys[1]);
            data.setOrgName(orgMap.get(data.getOrgCode()));
            dataList.add(data);
        }
        DepartmentEstimationAbstractBuilder.builder(context, dataList)
                .init(schemeCodes, years)
                .loadInitData()
                .calProfitRatio()
                .calGrossProfitRatio()
                .calLogisticsRatio()
                .calMarketingRatio()
                .calPublicShareRatio()
                .calCostShift()
                .calAssessProfits()
                .calSecondCategory();

        return dataList;
    }


    /**
     * 构建品项测算数据
     *
     * @param schemeCodes
     * @param years
     * @return
     */
    public List<RegionCollectItemEstimationVo> buildItemEstimation(List<String> schemeCodes, String years, String orgCode) {
        List<MarketingPlanCase> caseList = caseRepository.findListBySchemeCodes(schemeCodes);
        List<MarketingPlanProduct> planProductList = productRepository.findListBySchemeCodes(schemeCodes);
        if (CollectionUtils.isEmpty(planProductList)) {
            return Lists.newArrayList();
        }
        Map<String, List<MarketingPlanProduct>> planProductMap = planProductList.stream().collect(Collectors.groupingBy(MarketingPlanProduct::getSchemeDetailCode));
        for (MarketingPlanCase planCase : caseList) {
            planCase.setProductAndItemList(planProductMap.get(planCase.getSchemeDetailCode()));
        }
        List<MarketingSalesPlanVo> salesPlanVoList = marketingSalesPlanService.findListBySchemeCodes(schemeCodes);
        //过滤有空值的情况
        caseList = caseList.stream().filter(x -> {
            Boolean flag = Boolean.FALSE;
            if (ObjectUtils.isNotEmpty(x.getYears()) && years.equals(x.getYears())) {
                if (MarketingPlanCaseTypeEnum.back.getCode().equals(x.getCaseType())) {
                    if (CollectionUtils.isNotEmpty(x.getFeeBelongItemList())) {
                        flag = Boolean.TRUE;
                    }
                } else if (CollectionUtils.isNotEmpty(x.getItemList())) {
                    flag = Boolean.TRUE;
                }
            }
            return flag;
        }).collect(Collectors.toList());
        List<String> indexKeyList = salesPlanVoList.stream().map(x -> x.getItemCode() + ":" + x.getItemName() + ":" + years)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(indexKeyList)) {
            return Lists.newArrayList();
        }
        List<RegionCollectItemEstimationVo> dataList = new ArrayList<>(indexKeyList.size());
        for (String s : indexKeyList) {
            String[] keys = s.split(":");
            RegionCollectItemEstimationVo data = new RegionCollectItemEstimationVo();
            String key = keys[0] + ":" + keys[2];
            data.setIndexKey(key);
            data.setOrgCode(orgCode);
            //基础数据设置
            data.setItemCode(keys[0]);
            data.setItemName(keys[1]);
            data.setYears(keys[2]);
            dataList.add(data);
        }
        ItemEstimationAbstractBuilder.builder(context, dataList, caseList, orgCode)
                .init(schemeCodes, years)
                .loadInitData()
                .calProfitRatio()
                .calGrossProfitRatio()
                .calLogisticsRatio()
                .calMarketingRatio()
                .calPublicShareRatio()
                .calSecondCategory();
        return dataList;
    }


    /**
     * 营销费用测算
     *
     * @param schemeCode
     * @param years
     * @param orgCode
     * @return
     */
    public List<RegionCollectMarketingEstimationVo> buildMarketingEstimation(List<String> schemeCode, String years, String orgCode, String originalSchemeCode, String changeSchemeCode,
                                                                             List<MarketingPlanCase> caseList, List<MarketingSalesPlanVo> salePlanList) {
        List<RegionCollectMarketingEstimationVo> dataList = MarketingEstimationAbstractBuilder.builder(context, orgCode, originalSchemeCode, changeSchemeCode, caseList, salePlanList)
                .init(schemeCode, years)
                .estimationRevenue()
                .productionCost()
                .grossProfitMargin()
                .productTransportCost()
                .peripheryTransport()
                .transportTotal()
                .categoryCost()
                .marketingCostTotal()
                .marginalContribution()
                .artificialTravelOnBusiness()
                .costTotal()
                .profit()
                .costShift()
                .accessProfits()
                .getList();
        return dataList;
    }
}
