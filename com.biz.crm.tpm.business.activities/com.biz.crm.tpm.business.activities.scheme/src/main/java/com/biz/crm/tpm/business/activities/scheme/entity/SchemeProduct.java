package com.biz.crm.tpm.business.activities.scheme.entity;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.TenantEntity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * 实体：方案产品;
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "SchemeProduct",description = "方案产品")
@TableName("tpm_scheme_product")
@Getter
@Setter
@Entity(name = "tpm_scheme_product")
@org.hibernate.annotations.Table(appliesTo = "tpm_scheme_product", comment = "方案产品")
public class SchemeProduct  extends TenantEntity{

  /** 商品编号 */
  @ApiModelProperty(name = "商品编号",notes = "商品编号")
  @Column(name = "product_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '商品编号 '")
  private String productCode;

  /** 商品名称 */
  @ApiModelProperty(name = "商品名称",notes = "商品名称")
  @Column(name = "product_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '商品名称 '")
  private String productName;

  /** 方案编号 */
  @ApiModelProperty(name = "方案编号",notes = "")
  @Column(name = "scheme_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '方案编号 '")
  private String schemeCode;

}