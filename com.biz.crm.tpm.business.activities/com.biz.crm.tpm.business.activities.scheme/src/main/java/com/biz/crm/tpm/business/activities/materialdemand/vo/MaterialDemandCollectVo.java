package com.biz.crm.tpm.business.activities.materialdemand.vo;

import com.biz.crm.business.common.sdk.vo.WorkflowFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:13
 */
@Data
@ApiModel(value = "MaterialDemandCollectVo", description = "物料需求汇总")
public class MaterialDemandCollectVo extends WorkflowFlagOpVo {

    @ApiModelProperty("编码")
    private String collectCode;

    @ApiModelProperty("名称")
    private String collectName;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("审批单号")
    private String processNumber;

    @ApiModelProperty("推送日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    private String oaUserName;

    private String tenantCode;

    private String cacheKey;

    @ApiModelProperty("明细列表")
    private List<MaterialDemandCollectDetailVo> detailList;
}
