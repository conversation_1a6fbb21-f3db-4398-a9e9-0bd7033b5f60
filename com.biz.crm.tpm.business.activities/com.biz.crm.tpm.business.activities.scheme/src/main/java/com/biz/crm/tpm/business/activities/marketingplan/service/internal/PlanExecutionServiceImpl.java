package com.biz.crm.tpm.business.activities.marketingplan.service.internal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.constant.BusinessConstant;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.*;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.ActExecutionCollectEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.*;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanExecutionSumService;
import com.biz.crm.tpm.business.activities.marketingplan.utils.PictureUrlUtils;
import com.biz.crm.tpm.business.activities.sdk.dto.AuditExecutionCollectVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanExecutionDto;
import com.biz.crm.tpm.business.activities.sdk.dto.PlanExecutionPictureDto;
import com.biz.crm.tpm.business.activities.sdk.service.PlanExecutionService;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionAggregateVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionPictureVo;
import com.biz.crm.tpm.business.activities.sdk.vo.PlanExecutionVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.JsonUtils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PlanExecutionServiceImpl implements PlanExecutionService {

    @Autowired(required = false)
    private PlanExceutionRepository planExceutionRepository;
    @Autowired(required = false)
    private PlanExceutionPictureRepository planExceutionPictureRepository;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MarketingPlanCaseRepository marketingPlanCaseRepository;

    @Autowired(required = false)
    private MarketingPlanCaseExtendRepository marketingPlanCaseExtendRepository;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private ActExecutionCollectLogRepository actExecutionCollectLogRepository;

    @Autowired(required = false)
    private AuditExecutionCollectRepository auditExecutionCollectRepository;

    @Autowired(required = false)
    private AuditExecutionCollectPictureRepository auditExecutionCollectPictureRepository;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired(required = false)
    private PictureUrlUtils pictureUrlUtils;

    @Autowired(required = false)
    private PlanExecutionSumService planExecutionSumService;

    @Autowired
    private OrgVoService orgVoService;

    private static final String EXE_RESULT_DICT_CODE = "plan_execution_result";

    /**
     * 根据活动明细编码查询活动执行
     *
     * @param code
     * @return
     */
    @Override
    public List<PlanExecutionVo> findByDetailCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return Lists.newArrayList();
        }
        List<PlanExecutionVo> executionVoList = planExceutionRepository.findActExecuteCode(code);
        if (CollectionUtils.isEmpty(executionVoList)) {
            return Lists.newArrayList();
        }
        buildParam(executionVoList);
        return executionVoList;
    }

    public void buildParam(List<PlanExecutionVo> executionVoList) {
        List<PlanExecutionPicture> pictureVoList = planExceutionPictureRepository.findByCollectIds(
                executionVoList.stream().map(PlanExecutionVo::getId).collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(pictureVoList)) {
            Map<String, List<PlanExecutionPicture>> map = pictureVoList.stream()
                    .collect(Collectors.groupingBy(PlanExecutionPicture::getCollectId));
            for (PlanExecutionVo executionVo : executionVoList) {
                if (map.containsKey(executionVo.getId())) {
                    List<PlanExecutionPictureVo> pictureVos =
                            (List<PlanExecutionPictureVo>) this.nebulaToolkitService
                                    .copyCollectionByWhiteList(map.get(executionVo.getId()), PlanExecutionPicture.class,
                                            PlanExecutionPictureVo.class, LinkedHashSet.class, ArrayList.class);
                    executionVo.setPictureList(pictureVos);
                }
            }
        }
        Map<String, String> resultDescMap = this.dictDataVoService.findMapByDictTypeCode(EXE_RESULT_DICT_CODE);
        for (PlanExecutionVo executionVo : executionVoList) {
            if (resultDescMap.containsKey(executionVo.getResult())) {
                executionVo.setResultDesc(resultDescMap.get(executionVo.getResult()));
            }
        }
    }

    @Override
    public List<PlanExecutionVo> findBySchemeDetailCode(String schemeDetailCode) {
        if (StringUtil.isEmpty(schemeDetailCode)) {
            return Lists.newArrayList();
        }
        List<PlanExecutionVo> executionVoList = planExceutionRepository.findBySchemeDetailCode(schemeDetailCode);
        if (CollectionUtils.isEmpty(executionVoList)) {
            return Lists.newArrayList();
        }
        buildParam(executionVoList);
        return executionVoList;
    }

    @Override
    public PlanExecutionAggregateVo findByDetailCodeV1(String code) {
        PlanExecutionAggregateVo aggregateVo = new PlanExecutionAggregateVo();
        if (StringUtil.isEmpty(code)) {
            return aggregateVo;
        }
        List<PlanExecutionVo> executionVoList = planExceutionRepository.findActExecuteCode(code);
        if (CollectionUtils.isEmpty(executionVoList)) {
            return aggregateVo;
        }
        buildAggregateMainVo(aggregateVo, executionVoList);
        Map<String, PlanExecutionVo> executionVoMap = executionVoList.stream().collect(Collectors.toMap(PlanExecutionVo::getId, Function.identity(), (v1, v2) -> v1));
        List<String> collectIds = executionVoList.stream().map(PlanExecutionVo::getId).collect(Collectors.toList());
        List<PlanExecutionPicture> pictureVoList = planExceutionPictureRepository.findByCollectIds(collectIds);
        Map<String, String> dictMap = this.dictDataVoService.findMapByDictTypeCode(EXE_RESULT_DICT_CODE);

        List<PlanExecutionAggregateVo.ExecutionPictureVo> pictureVos = Lists.newArrayList();
        pictureVoList.forEach(picture -> {
            String collectId = picture.getCollectId();
            PlanExecutionVo planExecutionVo = executionVoMap.get(collectId);
            if (null == planExecutionVo) return;
            PlanExecutionAggregateVo.ExecutionPictureVo pictureVo = BeanUtil.copyProperties(picture, PlanExecutionAggregateVo.ExecutionPictureVo.class);
            pictureVo.setCollectId(collectId);
            String result = planExecutionVo.getResult();
            pictureVo.setResult(result);
            if (dictMap.containsKey(result)) {
                pictureVo.setResultDesc(dictMap.get(result));
            }
            pictureVo.setExecutor(planExecutionVo.getExecutor());
            pictureVo.setExecuteDate(planExecutionVo.getExecuteDate());
            pictureVo.setRequireExecute(planExecutionVo.getRequireExecute());
            pictureVo.setExecutionTime(planExecutionVo.getExecutionTime());
            pictureVo.setWatermarkStr(planExecutionVo.getWatermarkStr());
            pictureVo.setWatermarkFlag(planExecutionVo.getWatermarkFlag());
            pictureVos.add(pictureVo);
        });
        aggregateVo.setPictureList(pictureVos);
        return aggregateVo;
    }

    private void buildAggregateMainVo(PlanExecutionAggregateVo aggregateVo, List<PlanExecutionVo> executionVoList) {
        PlanExecutionVo planExecutionVo = executionVoList.get(0);
        aggregateVo.setSchemeCode(planExecutionVo.getSchemeCode());
        aggregateVo.setSchemeDetailCode(planExecutionVo.getSchemeDetailCode());
        aggregateVo.setTerminalCode(planExecutionVo.getTerminalCode());
        aggregateVo.setTerminalName(planExecutionVo.getTerminalName());
        aggregateVo.setTerminalType(planExecutionVo.getTerminalType());
        aggregateVo.setActStandards(planExecutionVo.getActStandards());
        aggregateVo.setCustomerCode(planExecutionVo.getCustomerCode());
        aggregateVo.setCustomerName(planExecutionVo.getCustomerName());
        aggregateVo.setId(planExecutionVo.getId());
    }

    /**
     * 创建活动执行
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPlanExecution(PlanExecutionDto dto) {

        AbstractCrmUserIdentity loginDetails = this.loginUserService.getAbstractLoginUser();
        Validate.notNull(loginDetails, "未查询到当前执行人信息");

        Validate.notNull(dto, "活动执行采集数据不能为空");
        dto.setId(null);
        //查询活动明细所需参数强校验
        Validate.notBlank(dto.getSchemeCode(), "方案编码不能为空");
        Validate.notBlank(dto.getSchemeDetailCode(), "方案明细编码不能为空");
        Validate.notBlank(dto.getTerminalCode(), "终端编码不能为空");
        //活动明细数据更新
        MarketingPlanCase marketingPlanCase = this.marketingPlanCaseRepository.findBySchemeDetailCode(dto.getSchemeDetailCode());
        Validate.notNull(marketingPlanCase, "未查询到采集的方案明细数据");
        MarketingPlanCaseExtend marketingPlanCaseExtend = this.marketingPlanCaseExtendRepository.findBySchemeDetailCode(marketingPlanCase.getSchemeDetailCode());
        Validate.notNull(marketingPlanCaseExtend, "未查询到采集的方案明细数据");

        marketingPlanCase.setExecuteStatus(BusinessConstant.BOOLEAN_YES);
        //执行时间
        dto.setExecuteDate(new Date());
        dto.setCreatePosCode(loginDetails.getPostCode());
        dto.setCreatePosName(loginDetails.getPostName());
        dto.setUserName(loginDetails.getUsername());
        dto.setFullName(loginDetails.getRealName());
        dto.setExecutionTime(DateUtil.dateStrNowYYYYMMDD());
        dto.setExecutor(loginDetails.getRealName());
        dto.setActStandards(marketingPlanCaseExtend.getExecuteDesc());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());

        dto.setCreateTime(new Date());
        dto.setModifyTime(new Date());

        PlanExecution collect = JsonUtils.convert(dto, PlanExecution.class);
        //tpm这边的啥活动执行编码
        collect.setActExecuteCode(marketingPlanCase.getActExecuteCode());

        // 记录客户信息
        collect.setCustomerCode(marketingPlanCase.getCustomerCode());
        collect.setCustomerName(marketingPlanCase.getCustomerName());

        this.planExceutionRepository.save(collect);
        String id = collect.getId();
        String schemeCode = collect.getSchemeCode();
        String schemeDetailCode = collect.getSchemeDetailCode();
        String actExecuteCode = collect.getActExecuteCode();

        //活动执行记录构建
        ActExecutionCollectLog collectLog = new ActExecutionCollectLog();
        collectLog.setExecutionId(id);
        collectLog.setClientCode(collect.getTerminalCode());
        collectLog.setClientName(collect.getTerminalName());
        collectLog.setSchemeCode(schemeCode);
        collectLog.setSchemeDetailCode(schemeDetailCode);
        collectLog.setActExecuteCode(actExecuteCode);
        collectLog.setExecutionType(ActExecutionCollectEnum.TERMINAL_COLLECT.getCode());
        //采集时间 当前年月日
        collectLog.setExecutionTime(DateUtil.dateStrNowYYYYMMDD());
        collectLog.setPositionCode(collect.getCreatePosCode());
        collectLog.setPositionName(collect.getCreatePosName());
        this.actExecutionCollectLogRepository.save(collectLog);
        if (CollectionUtil.isNotEmpty(collect.getPictureList())) {
            collect.getPictureList().forEach(picture -> {
                picture.setCollectId(id);
                picture.setId(null);
                picture.setTenantCode(TenantUtils.getTenantCode());
                picture.setSchemeCode(schemeCode);
                picture.setSchemeDetailCode(schemeDetailCode);
                picture.setActExecuteCode(actExecuteCode);
            });
            //明细行保存
            this.planExceutionPictureRepository.saveBatch(collect.getPictureList());
        }

        // 记录汇总报表
        planExecutionSumService.savePlanExecutionSum(marketingPlanCase);
    }

    /**
     * 修改活动执行
     *
     * @param dto
     */
    @Override
    public void updatePlanExecution(PlanExecutionDto dto) {

        store(dto);
    }

    /**
     * 保存
     *
     * @param dto
     */
    private void store(PlanExecutionDto dto) {

        //TODO 此方法弃用
        if (StringUtils.isBlank(dto.getId())) {
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        } else {
            planExceutionPictureRepository.deleteByCode(dto.getSchemeDetailCode());
        }
        if (!CollectionUtils.isEmpty(dto.getPictureList())) {
            Collection<PlanExecutionPicture> planExecutionPictures = nebulaToolkitService.copyCollectionByWhiteList(dto.getPictureList(), PlanExecutionPictureDto.class, PlanExecutionPicture.class, LinkedHashSet.class, ArrayList.class);
            for (PlanExecutionPicture executionPicture : planExecutionPictures) {
//                executionPicture.setSchemeDetailCode(dto.getSchemeDetailCode());
                executionPicture.setId(null);
                executionPicture.setTenantCode(TenantUtils.getTenantCode());
            }
            planExceutionPictureRepository.saveBatch(planExecutionPictures);
        }
        PlanExecution planExecution = nebulaToolkitService.copyObjectByWhiteList(dto, PlanExecution.class, LinkedHashSet.class, ArrayList.class);
        planExceutionRepository.saveOrUpdate(planExecution);
    }

    /**
     * 查询详情
     *
     * @param dto
     */
    @Override
    public PlanExecutionVo findByDynamicKey(PlanExecutionDto dto) {
        PlanExecutionVo vo = planExceutionRepository.findByDynamicKey(dto.getParentCode(), dto.getDynamicKey());
        if (vo == null) {
            return vo;
        }
        List<PlanExecutionPictureVo> pictureVoList = planExceutionPictureRepository.findByCode(dto.getSchemeDetailCode());
        vo.setPictureList(pictureVoList);
        return vo;
    }

    @Override
    public PlanExecutionVo findDetail(PlanExecutionDto dto) {
        PlanExecution detail = this.planExceutionRepository.findDetail(dto);
        if (Objects.isNull(detail)) {
            return null;
        }
        List<PlanExecutionPicture> executionPictures = this.planExceutionPictureRepository.findByCollectId(detail.getId());
        detail.setPictureList(executionPictures);
        return JsonUtils.convert(detail, PlanExecutionVo.class);
    }

    @Override
    public PlanExecutionVo findDetailById(String id) {
        PlanExecution detail = this.planExceutionRepository.findById(id);
        if (Objects.isNull(detail)) {
            return null;
        }
        List<PlanExecutionPicture> executionPictures = this.planExceutionPictureRepository.findByCollectId(detail.getId());
        detail.setPictureList(executionPictures);
        return JsonUtils.convert(detail, PlanExecutionVo.class);
    }

    @Override
    @Transactional
    public void createAuditExecutionCollect(AuditExecutionCollectVo vo) {
        //基础数据校验
        this.auditBaseCheck(vo);

        MarketingPlanCase marketingPlanCase = this.marketingPlanCaseRepository.findBySchemeDetailCode(vo.getSchemeDetailCode());
        Validate.notNull(marketingPlanCase, "未查询到采集的方案明细数据");
        AbstractCrmUserIdentity loginDetails = this.loginUserService.getAbstractLoginUser();
        Validate.notNull(loginDetails, "未查询到当前执行人信息");
        PositionVo positionVo = this.positionVoService.findByPositionCode(loginDetails.getPostCode());
        Validate.notNull(positionVo, "未查询到当前执行人职位信息");

        AuditExecutionCollect collect = JsonUtils.convert(vo, AuditExecutionCollect.class);

        //基础信息字段赋值
        collect.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        collect.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        collect.setExecutor(positionVo.getFullName());
        collect.setExecuteDate(new Date());
        collect.setCreatePostCode(positionVo.getPositionCode());
        collect.setCreatePostName(positionVo.getPositionName());
        collect.setCreateOrgCode(positionVo.getOrgCode());
        collect.setCreateOrgName(positionVo.getOrgName());
        collect.setExecutionTime(DateUtil.dateStrNowYYYYMMDD());
        //tpm这边的啥活动执行编码
        collect.setActExecuteCode(marketingPlanCase.getActExecuteCode());
        this.auditExecutionCollectRepository.save(collect);
        String id = collect.getId();

        //活动执行记录构建
        ActExecutionCollectLog collectLog = new ActExecutionCollectLog();
        collectLog.setExecutionId(id);
        collectLog.setClientCode(collect.getTerminalCode());
        collectLog.setClientName(collect.getTerminalName());
        collectLog.setSchemeCode(collect.getSchemeCode());
        collectLog.setSchemeDetailCode(collect.getSchemeDetailCode());
        collectLog.setActExecuteCode(marketingPlanCase.getActExecuteCode());
        //记录类型未稽查采集
        collectLog.setExecutionType(ActExecutionCollectEnum.AUDIT_COLLECT.getCode());
        //采集时间 当前年月日
        collectLog.setExecutionTime(DateUtil.dateStrNowYYYYMMDD());
        collectLog.setPositionCode(collect.getCreatePostCode());
        collectLog.setPositionName(collect.getCreatePostName());
        this.actExecutionCollectLogRepository.save(collectLog);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(collect.getPictureList())) {
            collect.getPictureList().forEach(x -> {
                x.setCollectId(id);
                x.setId(null);
                x.setTenantCode(TenantUtils.getTenantCode());
            });
            //明细行保存
            this.auditExecutionCollectPictureRepository.saveBatch(collect.getPictureList());
        }

    }

    private void auditBaseCheck(AuditExecutionCollectVo vo) {
        Validate.notNull(vo, "活动稽查数据不能为空");
        vo.setId(null);
        //查询活动明细所需参数强校验
        Validate.notBlank(vo.getSchemeCode(), "方案编码不能为空");
        Validate.notBlank(vo.getSchemeDetailCode(), "方案明细编码不能为空");
        Validate.notBlank(vo.getTerminalCode(), "终端编码不能为空");
    }

    @Override
    public AuditExecutionCollectVo findAuditExecutionDetail(AuditExecutionCollectVo dto) {
        AuditExecutionCollect collect = this.auditExecutionCollectRepository.findByDto(dto);
        if (Objects.isNull(collect)) {
            return null;
        }
        List<AuditExecutionCollectPicture> pictureList = this.auditExecutionCollectPictureRepository.findByCollectId(collect.getId());
        collect.setPictureList(pictureList);
        return JsonUtils.convert(collect, AuditExecutionCollectVo.class);

    }

    @Override
    public List<MarketingPlanCaseExecuteVo> findByTerminalMarketingPlanCaseExecuteList(MarketingPlanCaseQueryDto dto) {
        return marketingPlanCaseService.findByTerminalMarketingPlanCaseExecuteList(dto);
    }

    @Override
    public Page<PlanExecutionVo> findByConditions(Pageable pageable, PlanExecutionDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(0, 50));
        if (StringUtils.isNotEmpty(dto.getDepartmentOneCode()) || StringUtils.isNotEmpty(dto.getDepartmentOneName())) {
            List<String> departCodesByCode = Lists.newArrayList();
            List<String> departCodesByName = Lists.newArrayList();
            if (StringUtils.isNotEmpty(dto.getDepartmentOneCode())) {
                List<OrgVo> allChildrenByOrgCode = orgVoService.findAllChildrenByOrgCode(dto.getDepartmentOneCode());
                List<String> departCodes = allChildrenByOrgCode.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                departCodesByCode.addAll(departCodes);
            }
            if (StringUtils.isNotEmpty(dto.getDepartmentOneName())) {
                List<OrgVo> allParentByOrgName = orgVoService.findOrgByOrgNameList(Lists.newArrayList(dto.getDepartmentOneName()));
                List<String> departCodes = allParentByOrgName.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                List<OrgVo> allChildrenByOrgCode = orgVoService.findAllChildrenByOrgCodes(departCodes);
                List<String> departCodes2 = allChildrenByOrgCode.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                departCodesByName.addAll(departCodes2);
            }
            if (StringUtils.isNotEmpty(dto.getDepartmentOneCode()) && StringUtils.isNotEmpty(dto.getDepartmentOneName())) {
                departCodesByCode.retainAll(departCodesByName);
                dto.setBelongDepartmentCodeList(departCodesByCode);
            } else if (StringUtils.isNotEmpty(dto.getDepartmentOneCode())) {
                dto.setBelongDepartmentCodeList(departCodesByCode);
            } else {
                dto.setBelongDepartmentCodeList(departCodesByName);
            }
            if (CollectionUtil.isEmpty(dto.getBelongDepartmentCodeList())) {
                return new Page<>();
            }
        }
        Page<PlanExecutionVo> page = this.planExceutionRepository.findByConditions(pageable, dto);
        List<PlanExecutionVo> pageList = page.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            List<String> collectIds = pageList.stream().map(PlanExecutionVo::getId).collect(Collectors.toList());
            List<PlanExecutionPicture> pictureVoList = planExceutionPictureRepository.findByCollectIds(collectIds);
            Map<String, List<PlanExecutionPicture>> picMap = pictureVoList.stream().collect(Collectors.groupingBy(PlanExecutionPicture::getCollectId));

            List<String> schemeDetailCodes = pageList.stream().map(PlanExecutionVo::getSchemeDetailCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<MarketingPlanCase> marketingPlanCaseList = marketingPlanCaseRepository.findListBySchemeDetailCode(schemeDetailCodes);
            Map<String, String> planBelongDepartMap = marketingPlanCaseList.stream().collect(Collectors.toMap(MarketingPlanCase::getSchemeDetailCode, MarketingPlanCase::getBelongDepartmentCode));
            List<String> belongDepartmentCodes = marketingPlanCaseList.stream().map(MarketingPlanCase::getBelongDepartmentCode).distinct().collect(Collectors.toList());
            Map<String, List<OrgVo>> orgParentMap = orgVoService.findAllParentByOrgCodesMap(belongDepartmentCodes);

            for (PlanExecutionVo executionVo : pageList) {
                String schemeDetailCode = executionVo.getSchemeDetailCode();
                if (planBelongDepartMap.containsKey(schemeDetailCode)) {
                    String belongDepartmentCode = planBelongDepartMap.get(schemeDetailCode);
                    Optional<OrgVo> first = orgParentMap.get(belongDepartmentCode).stream().filter(x -> OrgTypeEnum.DIVISION.getDictCode().equals(x.getOrgType())).findFirst();
                    if (first.isPresent()) {
                        OrgVo divisionOrg = first.get();
                        executionVo.setDepartmentOneCode(divisionOrg.getOrgCode());
                        executionVo.setDepartmentOneName(divisionOrg.getOrgName());
                    }
                }
                if (picMap.containsKey(executionVo.getId())){
                    List<PlanExecutionPicture> picList = picMap.get(executionVo.getId());
                    List<String> picUrlList = new ArrayList<>();
                    for (PlanExecutionPicture executionPicture : picList) {
                        String url = this.pictureUrlUtils.getFileUrl(executionPicture.getFileCode());
                        if (StringUtils.isNotBlank(url)){
                            picUrlList.add(url);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(picUrlList)){
                        String url = StringUtils.join(picUrlList, ",");
                        executionVo.setPicUrl(url);
                    }
                }
            }
        }
        return page;
    }

    public void initPlanExecutionCustom() {
        // 初始化客户值
        String time = "2024-12-17 00:00:00";
        LocalDateTime end = LocalDateTime.parse(time, DateTimeFormatter.ofPattern(DateUtil.DEFAULT_DATE_ALL_PATTERN));
        LambdaQueryWrapper<PlanExecution> wrapper =
                new LambdaQueryWrapper<PlanExecution>()
                        .ge(PlanExecution::getCreateTime, end)
                        .isNull(PlanExecution::getCustomerCode)
                        .last("limit 500");
        while (true) {
            List<PlanExecution> planExecutions = planExceutionRepository.list(wrapper);
            if (CollUtil.isEmpty(planExecutions)) {
                break;
            }
            planExecutions.forEach(plan -> {
                if (StrUtil.isBlank(plan.getCustomerCode())) {
                    MarketingPlanCase marketingPlanCase =
                            this.marketingPlanCaseRepository.findBySchemeDetailCode(plan.getSchemeDetailCode());
                    if (Objects.nonNull(marketingPlanCase)) {
                        plan.setCustomerCode(marketingPlanCase.getCustomerCode());
                        plan.setCustomerName(marketingPlanCase.getCustomerName());
                        planExceutionRepository.updateById(plan);
                    }
                }
            });
            if (planExecutions.size() < 500) {
                break;
            }
        }
    }
}
