package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/12 17:13
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_item_share")
@Table(
        name = "tpm_marketing_item_share",
        indexes = {
                @Index(name = "tpm_marketing_item_share_index0", columnList = "scheme_code"),
                @Index(name = "tpm_marketing_item_share_index1", columnList = "scheme_detail_code"),
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_item_share", comment = "品相分摊报表")
@ApiModel(value = "MarketingItemShareEntity", description = "品相分摊报表")
public class MarketingItemShareEntity extends TenantFlagOpEntity {

    @ApiModelProperty("费用使用部门")
    @Column(name = "belong_department_code", columnDefinition = "varchar(32) comment '费用使用部门编码'")
    private String belongDepartmentCode;

    @ApiModelProperty("费用使用部门")
    @Column(name = "belong_department_name", columnDefinition = "varchar(64) comment '费用使用部门名称'")
    private String belongDepartmentName;

    @ApiModelProperty("使用部门编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '费用使用部门编码'")
    private String orgCode;

    @ApiModelProperty("使用部门名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '费用使用部门名称'")
    private String orgName;

    @ApiModelProperty("客户")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户")
    @Column(name = "customer_name", columnDefinition = "varchar(64) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("品相")
    @Column(name = "item_code", columnDefinition = "varchar(32) comment '品相编码'")
    private String itemCode;

    @ApiModelProperty("品相")
    @Column(name = "item_name", columnDefinition = "varchar(64) comment '品相名称'")
    private String itemName;

    @ApiModelProperty("分摊品相")
    @Column(name = "share_item_code", columnDefinition = "varchar(32) comment '分摊品相编码'")
    private String shareItemCode;

    @ApiModelProperty("分摊品相")
    @Column(name = "share_item_name", columnDefinition = "varchar(64) comment '分摊品相名称'")
    private String shareItemName;

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("活动名称")
    @Column(name = "act_name", columnDefinition = "varchar(520) comment '活动名称'")
    private String actName;

    @ApiModelProperty("活动年月")
    @Column(name = "act_years", columnDefinition = "varchar(10) comment '活动年月'")
    private String actYears;

    @ApiModelProperty("活动开始时间")
    @Column(name = "start_date", columnDefinition = "varchar(18) comment '活动开始时间'")
    private String startDate;

    @ApiModelProperty("活动结束时间")
    @Column(name = "end_date", columnDefinition = "varchar(18) comment '活动结束时间'")
    private String endDate;

    @ApiModelProperty("预算年月")
    @Column(name = "years", columnDefinition = "varchar(10) comment '预算年月'")
    private String years;

    @ApiModelProperty("活动大类")
    @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
    private String categoryCode;

    @ApiModelProperty("活动大类")
    @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
    private String categoryName;

    @ApiModelProperty("费用项目")
    @Column(name = "detail_code", columnDefinition = "varchar(32) comment '费用项目编码'")
    private String detailCode;

    @ApiModelProperty("费用项目")
    @Column(name = "detail_name", columnDefinition = "varchar(64) comment '费用项目名称'")
    private String detailName;

    @ApiModelProperty("科目")
    @Column(name = "budget_subject_code", columnDefinition = "varchar(32) comment '科目编码'")
    private String budgetSubjectCode;

    @ApiModelProperty("科目")
    @Column(name = "budget_subject_name", columnDefinition = "varchar(64) comment '科目名称'")
    private String budgetSubjectName;

    @ApiModelProperty("分摊操作年月")
    @Column(name = "share_operation_years", columnDefinition = "varchar(10) comment '分摊操作年月'")
    private String shareOperationYears;

    @ApiModelProperty("是否确认")
    @Column(name = "confirmed", columnDefinition = "varchar(10) comment '是否确认'")
    private String confirmed;

    @ApiModelProperty("费用归属部门成本中心")
    @Column(name = "belong_cost_center_codes", columnDefinition = "varchar(520) comment '费用归属成本中心'")
    private String belongCostCenterCodes;

    @ApiModelProperty("销售数量")
    @Column(name = "sales_quantity", columnDefinition = "decimal(18,2) comment '销售数量'")
    private BigDecimal salesQuantity;

    @ApiModelProperty("收入")
    @Column(name = "income_cost", columnDefinition = "decimal(18,2) comment '收入'")
    private BigDecimal incomeCost;

    @ApiModelProperty("分摊费用")
    @Column(name = "share_cost", columnDefinition = "decimal(18,2) comment '分摊费用'")
    private BigDecimal shareCost;

    @ApiModelProperty("申请费用")
    @Column(name = "apply_amount", columnDefinition = "decimal(18,2) comment '申请费用'")
    private BigDecimal applyAmount;

    @ApiModelProperty("计提金额")
    @Column(name = "withholding_amount", columnDefinition = "decimal(18,2) comment '计提金额'")
    private BigDecimal withholdingAmount;

    @ApiModelProperty("兑付金额")
    @Column(name = "cash_amount", columnDefinition = "decimal(18,2) comment '兑付金额'")
    private BigDecimal cashAmount;
}
