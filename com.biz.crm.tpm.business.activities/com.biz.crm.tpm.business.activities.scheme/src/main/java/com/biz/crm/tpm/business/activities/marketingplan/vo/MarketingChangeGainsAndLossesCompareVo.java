package com.biz.crm.tpm.business.activities.marketingplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/23 16:18
 */
@ApiModel("变更-方案损益对比vo")
@Data
public class MarketingChangeGainsAndLossesCompareVo {

    @ApiModelProperty("规划收入")
    private BigDecimal planIncome;

    @ApiModelProperty("规划费率")
    private BigDecimal planCostRatio;

    @ApiModelProperty("规划费率")
    private String planCostRatioStr;

    @ApiModelProperty("利润率")
    private BigDecimal profitMargin;

    @ApiModelProperty("利润率")
    private String profitMarginStr;

    @ApiModelProperty("毛利率")
    private BigDecimal grossMargin;

    @ApiModelProperty("毛利率")
    private String grossMarginStr;

    @ApiModelProperty("变更-规划收入")
    private BigDecimal changePlanIncome;

    @ApiModelProperty("变更-规划收入")
    private BigDecimal changePlanCostRatio;

    @ApiModelProperty("变更-规划费率")
    private String changePlanCostRatioStr;

    @ApiModelProperty("变更-规划费率")
    private BigDecimal changeProfitMargin;

    @ApiModelProperty("变更-利润率")
    private String changeProfitMarginStr;

    @ApiModelProperty("变更-毛利率")
    private BigDecimal changeGrossMargin;

    @ApiModelProperty("变更-毛利率")
    private String changeGrossMarginStr;

    @ApiModelProperty("差异-规划收入")
    private BigDecimal differencePlanIncome;

    @ApiModelProperty("差异-规划费率")
    private BigDecimal differencePlanCostRatio;

    @ApiModelProperty("差异-规划费率")
    private String differencePlanCostRatioStr;

    @ApiModelProperty("差异-利润率")
    private BigDecimal differenceProfitMargin;

    @ApiModelProperty("差异-利润率")
    private String differenceProfitMarginStr;

    @ApiModelProperty("差异-毛利率")
    private BigDecimal differenceGrossMargin;

    @ApiModelProperty("差异-毛利率")
    private String differenceGrossMarginStr;
}