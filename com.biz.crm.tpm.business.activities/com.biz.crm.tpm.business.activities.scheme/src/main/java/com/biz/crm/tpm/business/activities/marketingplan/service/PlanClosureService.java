package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureDetailVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;

import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:23
 */
public interface PlanClosureService {

    Page<PlanClosureVo> findList(Pageable pageable, PlanClosureVo vo);

    PlanClosureVo queryByIdOrCode(String id, String closeCode);

    void create(PlanClosureVo vo);

    void update(PlanClosureVo vo);

    void submitCreate(PlanClosureVo vo);

    void submitUpdate(PlanClosureVo vo);

    void submitById(String id);

    void callback(PlanClosureVo vo);

    void deleteBatchByIds(List<String> ids);

    List<PlanClosureDetailVo> findListById(String id);

    Set<String> findPlanClosureDetailSchemeDetailCodeList(String sourceType);

    Page<MarketingPlanCaseVo> findNotMarketingClosedCaseList(@PageableDefault(50) Pageable pageable, MarketingPlanCaseQueryDto vo);

    Page<OverallPlanCaseVo> findNotRegionCaseList(@PageableDefault(50) Pageable pageable, OverallPlanCaseVo vo);

    Page<OverallPlanCaseVo> findNotHeadCaseList(@PageableDefault(50) Pageable pageable, OverallPlanCaseVo vo);

    /**
     * OA撤回
     *
     * @param code
     * @return
     */
    void recover(String code, String remark);
}
