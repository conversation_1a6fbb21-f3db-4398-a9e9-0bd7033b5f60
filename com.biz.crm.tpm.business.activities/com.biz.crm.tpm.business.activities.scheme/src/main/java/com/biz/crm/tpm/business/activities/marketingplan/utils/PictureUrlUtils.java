package com.biz.crm.tpm.business.activities.marketingplan.utils;

import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 图片文件编码拼接图片url
 *
 * <AUTHOR>
 * @since 2021-10-13 17:36:03
 */
@Component
@Slf4j
public class PictureUrlUtils {

    private final static String URL = "%s/crm-venus/v1/venus/images/%s.id";

    @Value("${venus.http-head:}")
    private String httpHead;


    public String getFileUrl(String fileId) {
        if (StringUtil.isEmpty(fileId)
                || StringUtil.isEmpty(httpHead)) {
            return "";
        }
        return String.format(URL, httpHead, fileId);
    }
}
