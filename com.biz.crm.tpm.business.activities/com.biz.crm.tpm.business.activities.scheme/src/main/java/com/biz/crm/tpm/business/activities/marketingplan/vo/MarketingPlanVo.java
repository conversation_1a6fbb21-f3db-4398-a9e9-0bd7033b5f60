package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.biz.crm.business.common.sdk.vo.WorkflowFlagOpVo;
import com.biz.crm.tpm.business.activities.schemefile.vo.SchemeCaseFilesVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 14:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MarketingPlan", description = "营销方案规划")
public class MarketingPlanVo extends WorkflowFlagOpVo {

    @ApiModelProperty("租户编码")
    private String tenantCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("原方案编码")
    private String originalSchemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案主题")
    private String schemeTheme;

    @ApiModelProperty("部门编码")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("方案说明")
    private String schemeDesc;

    @ApiModelProperty("变更方案说明")
    private String changeDesc;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("职位名称")
    private String positionName;

    @ApiModelProperty("确认状态")
    private String confirmStatus;

    @ApiModelProperty("方案状态")
    private String schemeStatus;

    @ApiModelProperty("方案类型")
    private String schemeType;

    @ApiModelProperty("是否新开客户")
    private String newCustomerFlag;

    @ApiModelProperty("是否被变更,Y-是,N-否")
    private String changedFlag;

    @ApiModelProperty("缓存key")
    private String cacheKey;

    @ApiModelProperty("是否合同费用")
    private String isContractCost;

    @ApiModelProperty("合同客户")
    private String contractCusCode;

    @ApiModelProperty("申请费用合计")
    private BigDecimal applyTotalAmount;

    @ApiModelProperty("推送日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    private String oaUserName;

    @ApiModelProperty("回传TPM接口唯一标识（接口用）")
    private String businessCode;

    @ApiModelProperty("url")
    private String tpmUrl;

    private String deptCode;
    private BigDecimal ratioBefore;
    private BigDecimal ratioAfter;
    private BigDecimal profitMarginBefore;
    private BigDecimal profitMarginAfter;
    private BigDecimal planIncome;
    private BigDecimal changePlanIncome;
    private BigDecimal estimateAmount;
    private BigDecimal budgetAmount;
    private BigDecimal marketingBudgetAmount;
    private String collectCode;
    private String falx;

    @ApiModelProperty("是否变更方案")
    private String isChange;

    @ApiModelProperty("校验预算")
    private String checkBudgetFlag;

    @ApiModelProperty("是否控制")
    private String controlFlag;

    @ApiModelProperty("方案明细")
    private Map<String, List<MarketingPlanCaseVo>> detailCaseMap;

    @ApiModelProperty("客户损益预测表")
    private List<MarketingPlanGainsAndLossesVo> gainsAndLossesList;

    @ApiModelProperty("销售计划")
    private List<MarketingSalesPlanVo> salesPlanList;

//    @ApiModelProperty("承接方案明细列表")
//    private List<BearOverallPlanCaseVo> bearList;

    @ApiModelProperty("表单类型")
    private Set<String> templateList;

    @ApiModelProperty("营销测算")
    private List<MarketingPlanEstimationVo> marketingEstimationList;

    @ApiModelProperty("单方案-营销测算")
    private List<MarketingPlanSchemeEstimationVo> marketingSchemeEstimationList;

    @ApiModelProperty("客户损益对比")
    private List<MarketPlanChangeGainsAndLossesVo> gainsAndLossesComparsionList;

    @ApiModelProperty("变更-客户损益对比（方案维度）")
    private MarketingChangeGainsAndLossesCompareVo compareChangeGainsAndLossesVo;

    @ApiModelProperty("预算信息")
    private List<MarketingPlanBudgetVo> budgetList;

    @ApiModelProperty("文件列表")
    private List<SchemeCaseFilesVo> filesList;

    @ApiModelProperty("品相测算表")
    private List<MarketingPlanItemEstimationVo> itemEstimationList;

    @ApiModelProperty("客户编码列表")
    private List<String> customerCodeList;

}
