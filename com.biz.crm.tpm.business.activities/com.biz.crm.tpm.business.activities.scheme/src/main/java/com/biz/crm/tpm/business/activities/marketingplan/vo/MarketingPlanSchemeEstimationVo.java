package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "MarketingPlanSchemeEstimationVo", description = "营销规划方案-营销测算表")
public class MarketingPlanSchemeEstimationVo extends UuidFlagOpVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("项目编码")
    private String projectCode;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("预计固定扣点")
    private BigDecimal estimateFixedPoint;

    @ApiModelProperty("预计固定扣点")
    private String estimateFixedPointStr;

    @ApiModelProperty("预计金额(万元)")
    private BigDecimal estimateAmount;

    @ApiModelProperty("预计占比")
    private BigDecimal estimateRatio;

    @ApiModelProperty("预计占比")
    private String estimateRatioStr;

    @ApiModelProperty("预算固定扣点")
    private BigDecimal budgetFixedPoint;

    @ApiModelProperty("预算固定扣点")
    private String budgetFixedPointStr;

    @ApiModelProperty("预算金额(万元)")
    private BigDecimal budgetAmount;

    @ApiModelProperty("预算占比")
    private BigDecimal budgetRatio;

    @ApiModelProperty("预算占比")
    private String budgetRatioStr;

    @ApiModelProperty("差异额")
    private BigDecimal differenceAmount;

    @ApiModelProperty("差异率")
    private BigDecimal differenceRatio;

    @ApiModelProperty("差异率")
    private String differenceRatioStr;
}
