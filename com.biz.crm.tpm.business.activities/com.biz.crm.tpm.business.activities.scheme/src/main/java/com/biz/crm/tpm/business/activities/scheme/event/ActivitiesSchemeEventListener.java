package com.biz.crm.tpm.business.activities.scheme.event;

import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeVo;

/**
 * 方案活动;(tpm_activities_scheme)相关的事件通知
 * <AUTHOR> Keller
 * @date : 2022-6-1
 */
public interface ActivitiesSchemeEventListener{

  /**
   * 当方案活动数据被创建时，该事件被触发
   * @param activitiesSchemeVo
   */
  void onCreated(ActivitiesSchemeVo activitiesSchemeVo);
  /**
   * 当方案活动数据被修改时，该事件被触发
   * @param oldActivitiesSchemeVo 修改前数据
   * @param activitiesSchemeVo  修改后数据
   */
  void onUpdate(ActivitiesSchemeVo oldActivitiesSchemeVo, ActivitiesSchemeVo activitiesSchemeVo);
  /**
   * 当方案活动数据被删除时（逻辑删除），该事件被触发
   * @param activitiesSchemeVo
   */
  void onDeleted(ActivitiesSchemeVo activitiesSchemeVo);
  /**
   * 当方案活动数据被启用时，该事件被触发
   * @param activitiesSchemeVo
   */
  void onEnable(ActivitiesSchemeVo activitiesSchemeVo);
  /**
   * 当方案活动数据被禁用时，该事件被触发
   * @param activitiesSchemeVo
   */
  void onDisable(ActivitiesSchemeVo activitiesSchemeVo);
}