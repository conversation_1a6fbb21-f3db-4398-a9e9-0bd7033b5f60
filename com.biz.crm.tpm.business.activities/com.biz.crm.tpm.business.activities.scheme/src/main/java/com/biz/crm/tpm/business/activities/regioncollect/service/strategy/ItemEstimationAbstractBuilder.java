package com.biz.crm.tpm.business.activities.regioncollect.service.strategy;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialCostVoService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanMaterialTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingTransportEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.enums.SecondCategoryEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 品项测算
 * <AUTHOR>
 * @Date 2024/6/25 17:12
 */
@Slf4j
public class ItemEstimationAbstractBuilder extends ItemEstimationBuilder<List<String>, String> {

    private static final String column = ":";

    private MarketingSalesPlanService marketingSalesPlanService;
    private CostBudgetIncomeService costBudgetIncomeService;
    private CostBudgetVoService costBudgetVoService;
    private MdmCostCenterVoService costCenterVoService;
    private MaterialVoService materialVoService;
    private MaterialCostVoService materialCostVoService;
    private CostTypeCategoryVoService costTypeCategoryVoService;
    private OrgVoService orgVoService;

    private MarketingPlanCaseCheckHelper checkHelper;

    private MarketingPlanRepository marketingPlanRepository;

    private PublicShareRatioService publicShareRatioService;

    private ProductPhaseVoService productPhaseVoService;

    private String year = null;

    private Map<String, BigDecimal> materialMap;

    private Map<String, MdmCostCenterVo> costCenterMap;

    private Map<String, List<String>> costTypeCategoryMap;

    private List<MarketingPlanCase> caseList = Lists.newArrayList();

    private List<? extends CustomerGainsAndLossesVo> dataList = Lists.newArrayList();

    private String regionOrgCode;

    private Map<String, List<MarketingSalesPlanVo>> salesPlanMap;

    private Map<String, MarketingSalesPlanVo> cusProductSalesPlanMap;

    private Map<String, ProductPhaseVo> productPhaseMap = Maps.newHashMap();

    private Map<String, List<String>> productPhaseListMap = Maps.newHashMap();

    private Set<String> orgCodeSet = Sets.newHashSet();

    public static ItemEstimationAbstractBuilder builder(ApplicationContext context, List<? extends CustomerGainsAndLossesVo> dataList, List<MarketingPlanCase> caseList, String regionOrgCode) {
        ItemEstimationAbstractBuilder builder = new ItemEstimationAbstractBuilder();
        builder.orgVoService = context.getBean(OrgVoService.class);
        builder.marketingSalesPlanService = context.getBean(MarketingSalesPlanService.class);
        builder.costBudgetIncomeService = context.getBean(CostBudgetIncomeService.class);
        builder.costBudgetVoService = context.getBean(CostBudgetVoService.class);
        builder.costCenterVoService = context.getBean(MdmCostCenterVoService.class);
        builder.materialVoService = context.getBean(MaterialVoService.class);
        builder.materialCostVoService = context.getBean(MaterialCostVoService.class);
        builder.costTypeCategoryVoService = context.getBean(CostTypeCategoryVoService.class);
        builder.dataList = dataList;
        builder.caseList = caseList;
        builder.regionOrgCode = regionOrgCode;
        builder.checkHelper = context.getBean(MarketingPlanCaseCheckHelper.class);
        builder.marketingPlanRepository = context.getBean(MarketingPlanRepository.class);
        builder.publicShareRatioService = context.getBean(PublicShareRatioService.class);
        builder.productPhaseVoService = context.getBean(ProductPhaseVoService.class);
        return builder;
    }


    @Override
    public ItemEstimationBuilder loadInitData() {
        this.year = years.split("-")[0];

        //3.查询销售计划-全表检索
        List<MarketingSalesPlanVo> salePlanList = marketingSalesPlanService.findListBySchemeCodes(schemeCode);
        List<String> itemCodes = salePlanList.stream().map(x -> x.getItemCode()).collect(Collectors.toList());

        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(regionOrgCode);
        List<String> orgCodes = orgVoList.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());

        List<MarketingPlan> plans = marketingPlanRepository.queryBySchemeCodes(Sets.newHashSet(schemeCode));

        this.orgCodeSet = plans.stream().map(x -> x.getDepartmentCode()).collect(Collectors.toSet());

        //公司代码
        Set<String> companyCodeSet = caseList.stream().map(MarketingPlanCase::getCompanyCode).collect(Collectors.toSet());

        //1.获取预算收入-汇总大区编码+品项+年月
        List<CostBudgetIncomeVo> incomeVos = costBudgetIncomeService.findListByOrgCodeAndItemCodesAndYears(orgCodes, itemCodes, years);
        incomeVos = incomeVos.stream().filter(e -> e.getConfirmStatus().equals(ConfirmStatusEnum.CONFIRMED.getCode())).collect(Collectors.toList());
        List<String> incomeItemCodes = incomeVos.stream().map(CostBudgetIncomeVo::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(incomeItemCodes));
        Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> null == v.getTaxRate() ? BigDecimal.ZERO : v.getTaxRate()));
        Map<String, BigDecimal> incomeMap = incomeVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getItemCode()) && ObjectUtils.isNotEmpty(x.getYearMonthLy()) && ObjectUtils.isNotEmpty(x.getIncomeAmount()))
                .collect(Collectors.groupingBy(x -> x.getItemCode() + column + x.getYearMonthLy(), Collectors.mapping(k -> Optional.ofNullable(k.getIncomeAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(k.getItemCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        //2.获取预算费用
        List<CostBudgetVo> budgetVos = costBudgetVoService.findListByOrgCodeAndItemCodesAndYears(orgCodes, itemCodes, years);
        Map<String, BigDecimal> budgetMap = budgetVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getItemCode()) && ObjectUtils.isNotEmpty(x.getYearMonthLy()) && ObjectUtils.isNotEmpty(x.getInitialAmount())).collect(Collectors.groupingBy(x -> x.getItemCode() + column + x.getYearMonthLy(), Collectors.mapping(CostBudgetVo::getInitialAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        //成本中心
        List<String> costCenterCodes = salePlanList.stream().map(MarketingSalesPlanVo::getCostCenterCode).distinct().collect(Collectors.toList());
        List<MdmCostCenterVo> costCenterVoList = costCenterVoService.findByCodes(costCenterCodes);
        if (CollectionUtils.isNotEmpty(costCenterVoList)) {
            this.costCenterMap = costCenterVoList.stream().collect(Collectors.toMap(MdmCostCenterVo::getCostCenterCode, Function.identity()));
        }

        Map<String, BigDecimal> salesPlanMap = salePlanList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getItemCode()) && ObjectUtils.isNotEmpty(x.getYears())).collect(Collectors.groupingBy(x -> x.getItemCode() + column + x.getYears(), Collectors.mapping(MarketingSalesPlanVo::getEstimatedCost, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        this.cusProductSalesPlanMap = salePlanList.stream().collect(Collectors.toMap(x -> x.getCustomerCode() + x.getProductCode(), Function.identity(), (a, b) -> a));

        List<String> caseItemCodes = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getItemCode())).map(x -> x.getItemCode()).distinct().collect(Collectors.toList());
        Map<String, List<ProductVo>> itemProductMap = checkHelper.findProductByItemCodes(caseItemCodes);
        this.productPhaseMap = checkHelper.findProductPhaseMap(caseItemCodes);

        //4.规划总额
        Map<String, BigDecimal> caseAmountMap = caseList.stream().collect(Collectors.groupingBy(x -> x.getItemCode(), Collectors.mapping(x -> Optional.ofNullable(x.getLineNoTaxApplyAmount()).orElse(BigDecimal.ZERO), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<List<String>, BigDecimal> planAmountMap = Maps.newHashMap();
        Map<List<String>, String> productPhaseCanShareMap = Maps.newHashMap();
        for (Map.Entry<String, BigDecimal> entry : caseAmountMap.entrySet()) {
            if (itemProductMap.containsKey(entry.getKey())) {
                List<ProductVo> productVoList = itemProductMap.get(entry.getKey());
                List<String> phaseCodes = productVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode())).map(ProductVo::getProductPhaseCode).distinct().collect(Collectors.toList());
                planAmountMap.put(phaseCodes, entry.getValue());
                ProductPhaseVo productPhaseVo = productPhaseMap.get(entry.getKey());
                String flag = ObjectUtils.defaultIfNull(productPhaseVo.getCanShare(), BooleanEnum.FALSE.getCapital());
                productPhaseCanShareMap.put(phaseCodes, flag);
                productPhaseListMap.put(entry.getKey(), phaseCodes);
            }
        }
        //随单未税费用品项分配
        Map<String, BigDecimal> noTaxCaseAmountMap = caseList.stream().filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .filter(x -> ObjectUtils.isNotEmpty(x.getItemCode()))
                .collect(Collectors.groupingBy(x -> x.getItemCode(), Collectors.mapping(x -> Optional.ofNullable(x.getNoTaxApplyAmount()).orElse(BigDecimal.ZERO),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<List<String>, BigDecimal> noTaxPlanAmountMap = Maps.newHashMap();
        Map<List<String>, String> noTaxProductPhaseCanShareMap = Maps.newHashMap();
        for (Map.Entry<String, BigDecimal> entry : noTaxCaseAmountMap.entrySet()) {
            if (itemProductMap.containsKey(entry.getKey())) {
                List<ProductVo> productVoList = itemProductMap.get(entry.getKey());
                List<String> phaseCodes = productVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode())).map(ProductVo::getProductPhaseCode).distinct().collect(Collectors.toList());
                noTaxPlanAmountMap.put(phaseCodes, entry.getValue());
                ProductPhaseVo productPhaseVo = productPhaseMap.get(entry.getKey());
                String flag = ObjectUtils.defaultIfNull(productPhaseVo.getCanShare(), BooleanEnum.FALSE.getCapital());
                noTaxProductPhaseCanShareMap.put(phaseCodes, flag);
            }
        }
        //随单生产成本
        //随单未税费用品项分配
        Map<String, BigDecimal> planCaseProductionCostAmountMap = caseList.stream().filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .filter(x -> ObjectUtils.isNotEmpty(x.getItemCode()))
                .collect(Collectors.groupingBy(x -> x.getItemCode(),
                        Collectors.mapping(x -> Optional.ofNullable(x.getPolicyMaterialCostPrice()).orElse(BigDecimal.ZERO)
                                        .multiply(Optional.ofNullable(x.getGiftQuantity()).orElse(BigDecimal.ZERO)),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<List<String>, BigDecimal> productionCostPlanAmountMap = Maps.newHashMap();
        Map<List<String>, String> productionCostProductPhaseCanShareMap = Maps.newHashMap();
        for (Map.Entry<String, BigDecimal> entry : planCaseProductionCostAmountMap.entrySet()) {
            if (itemProductMap.containsKey(entry.getKey())) {
                List<ProductVo> productVoList = itemProductMap.get(entry.getKey());
                List<String> phaseCodes = productVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode())).map(ProductVo::getProductPhaseCode).distinct().collect(Collectors.toList());
                productionCostPlanAmountMap.put(phaseCodes, entry.getValue());
                ProductPhaseVo productPhaseVo = productPhaseMap.get(entry.getKey());
                String flag = ObjectUtils.defaultIfNull(productPhaseVo.getCanShare(), BooleanEnum.FALSE.getCapital());
                productionCostProductPhaseCanShareMap.put(phaseCodes, flag);
            }
        }
        //随单产品运输费用
        //先计算整体的 在拆分计算
        this.calPolicyProductTransportCost();
        Map<String, BigDecimal> planCaseProductionTransportCostAmountMap = caseList.stream().filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .filter(x -> ObjectUtils.isNotEmpty(x.getItemCode()))
                .collect(Collectors.groupingBy(x -> x.getItemCode(),
                        Collectors.mapping(x -> Optional.ofNullable(x.getPolicyProductTransportCost()).orElse(BigDecimal.ZERO),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<List<String>, BigDecimal> productTransportCostPlanAmountMap = Maps.newHashMap();
        Map<List<String>, String> productTransportCostProductPhaseCanShareMap = Maps.newHashMap();
        for (Map.Entry<String, BigDecimal> entry : planCaseProductionTransportCostAmountMap.entrySet()) {
            if (itemProductMap.containsKey(entry.getKey())) {
                List<ProductVo> productVoList = itemProductMap.get(entry.getKey());
                List<String> phaseCodes = productVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode())).map(ProductVo::getProductPhaseCode).distinct().collect(Collectors.toList());
                productTransportCostPlanAmountMap.put(phaseCodes, entry.getValue());
                ProductPhaseVo productPhaseVo = productPhaseMap.get(entry.getKey());
                String flag = ObjectUtils.defaultIfNull(productPhaseVo.getCanShare(), BooleanEnum.FALSE.getCapital());
                productTransportCostProductPhaseCanShareMap.put(phaseCodes, flag);
            }
        }

        //5.物料成本
        Set<String> materialCodes = salePlanList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode())).map(MarketingSalesPlanVo::getMaterialCode).collect(Collectors.toSet());
        MaterialSearchDto searchDto = new MaterialSearchDto();
        searchDto.setMaterialCodeSet(materialCodes);
        searchDto.setCompanyCodeSet(companyCodeSet);
        List<MaterialVo> materialVoList = materialVoService.findBySearchDto(searchDto);
        this.materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO)));
        this.materialMap = materialVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode(), x -> Optional.ofNullable(x.getCostPrice()).orElse(BigDecimal.ZERO)));
        this.salesPlanMap = salePlanList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()) && ObjectUtils.isNotEmpty(x.getEstimatedSalesVolume()) && ObjectUtils.isNotEmpty(x.getCostCenterCode())).collect(Collectors.groupingBy(x -> x.getItemCode() + column + x.getYears()));
        //6.查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        this.costTypeCategoryMap = costTypeCategoryVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        //先分摊随单未税费用到规划收入里面
        this.calPlanIncomeAndNoTaxPlanAmount(noTaxPlanAmountMap, noTaxProductPhaseCanShareMap);
        //先分摊随单的生产成本到生产成本里面
        this.calMatchingGiftProductionCost(productionCostPlanAmountMap, productionCostProductPhaseCanShareMap);
        //先分摊随单的产品运输费用到产品运输里面
        this.calMatchingGiftProductTransportCost(productTransportCostPlanAmountMap, productTransportCostProductPhaseCanShareMap);
        //7.计算收入达成率、预算费率
        for (CustomerGainsAndLossesVo data : dataList) {
            //此处的indexkey = 品项编码+年月
            String indexKey = data.getIndexKey();
            //预算收入
            data.setBudgetIncome(incomeMap.get(indexKey));
            //规划收入= 用成本中心匹配
            data.setPlanIncome(ObjectUtils.defaultIfNull(data.getPlanIncome(), BigDecimal.ZERO).add(salesPlanMap.getOrDefault(indexKey, BigDecimal.ZERO)));
            //收入达成率
            data.setIncomeAchieveRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getBudgetIncome()) && ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) > 0) {
                //规划收入/预算收入
                data.setIncomeAchieveRatio(data.getPlanIncome().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            //预算总额
            data.setBudgetTotalAmount(budgetMap.get(indexKey));
            //预算费率
            data.setBudgetRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getBudgetTotalAmount()) && ObjectUtils.isNotEmpty(data.getBudgetIncome()) && data.getBudgetIncome().compareTo(BigDecimal.ZERO) > 0) {
                //预算总额/预算收入
                data.setBudgetRatio(data.getBudgetTotalAmount().divide(data.getBudgetIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }


        }
        this.calPlanTotalAmount(planAmountMap, productPhaseCanShareMap);
        return this;
    }


    /**
     * 分配未税的随单金额
     *
     * @param noTaxPlanAmountMap
     * @param noTaxProductPhaseCanShareMap
     */
    public void calPlanIncomeAndNoTaxPlanAmount(Map<List<String>, BigDecimal> noTaxPlanAmountMap, Map<List<String>, String> noTaxProductPhaseCanShareMap) {
        for (Map.Entry<List<String>, BigDecimal> entry : noTaxPlanAmountMap.entrySet()) {
            List<String> list = entry.getKey();
            BigDecimal totalPlanAmount = entry.getValue();
            BigDecimal totalPlanIncomeAmount = BigDecimal.ZERO;
            List<String> indexKeyList = Lists.newArrayList();
            for (CustomerGainsAndLossesVo vo : dataList) {
                String itemCode = vo.getIndexKey().split(column)[0];
                if (list.contains(itemCode)) {
                    totalPlanIncomeAmount = totalPlanIncomeAmount.add(ObjectUtils.defaultIfNull(vo.getPlanIncome(), BigDecimal.ZERO));
                    indexKeyList.add(vo.getIndexKey());
                }
            }
            //判断不为空 则按照分摊占比计算
            if (CollectionUtils.isNotEmpty(indexKeyList)) {
                Integer count = 1;
                BigDecimal ratio = BigDecimal.ONE;
                for (CustomerGainsAndLossesVo vo : dataList) {
                    if (indexKeyList.contains(vo.getIndexKey())) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0 && ObjectUtils.isNotEmpty(vo.getPlanIncome())) {
                            calRatio = vo.getPlanIncome().divide(totalPlanIncomeAmount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == indexKeyList.size()) {
                            vo.setPlanIncome(ObjectUtils.defaultIfNull(vo.getPlanIncome(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal planIncomeAmount = entry.getValue().multiply(calRatio);
                            totalPlanAmount = totalPlanAmount.subtract(planIncomeAmount);
                            vo.setPlanIncome(ObjectUtils.defaultIfNull(vo.getPlanIncome(), BigDecimal.ZERO).add(planIncomeAmount));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            } else {
                //判断是需要分摊的
                if (noTaxProductPhaseCanShareMap.containsKey(entry.getKey()) && BooleanEnum.TRUE.getCapital().equals(noTaxProductPhaseCanShareMap.get(entry.getKey()))) {
                    Integer count = 1;
                    BigDecimal ratio = BigDecimal.ONE;
                    BigDecimal amount = dataList.stream().map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                    for (CustomerGainsAndLossesVo vo : dataList) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (amount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(amount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == dataList.size()) {
                            vo.setPlanIncome(ObjectUtils.defaultIfNull(vo.getPlanIncome(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal planIncome = entry.getValue().multiply(ratio);
                            totalPlanAmount = totalPlanAmount.subtract(planIncome);
                            vo.setPlanIncome(ObjectUtils.defaultIfNull(vo.getPlanIncome(), BigDecimal.ZERO).add(planIncome));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            }
        }

    }


    /**
     * 分配随单的生产成本
     *
     * @param productionCostPlanAmountMap
     * @param productionCostProductPhaseCanShareMap
     */
    public void calMatchingGiftProductionCost(Map<List<String>, BigDecimal> productionCostPlanAmountMap, Map<List<String>, String> productionCostProductPhaseCanShareMap) {
        for (Map.Entry<List<String>, BigDecimal> entry : productionCostPlanAmountMap.entrySet()) {
            List<String> list = entry.getKey();
            BigDecimal totalPlanAmount = entry.getValue();
            BigDecimal totalPlanIncomeAmount = BigDecimal.ZERO;
            List<String> indexKeyList = Lists.newArrayList();
            for (CustomerGainsAndLossesVo vo : dataList) {
                String itemCode = vo.getIndexKey().split(column)[0];
                if (list.contains(itemCode)) {
                    totalPlanIncomeAmount = totalPlanIncomeAmount.add(vo.getPlanIncome());
                    indexKeyList.add(vo.getIndexKey());
                }
            }
            //判断不为空 则按照分摊占比计算
            if (CollectionUtils.isNotEmpty(indexKeyList)) {
                Integer count = 1;
                BigDecimal ratio = BigDecimal.ONE;
                for (CustomerGainsAndLossesVo vo : dataList) {
                    if (indexKeyList.contains(vo.getIndexKey())) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(totalPlanIncomeAmount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == indexKeyList.size()) {
                            vo.setProductionCosts(ObjectUtils.defaultIfNull(vo.getProductionCosts(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal productionCosts = entry.getValue().multiply(calRatio);
                            totalPlanAmount = totalPlanAmount.subtract(productionCosts);
                            vo.setProductionCosts(ObjectUtils.defaultIfNull(vo.getProductionCosts(), BigDecimal.ZERO).add(productionCosts));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            } else {
                //判断是需要分摊的
                if (productionCostProductPhaseCanShareMap.containsKey(entry.getKey()) && BooleanEnum.TRUE.getCapital().equals(productionCostProductPhaseCanShareMap.get(entry.getKey()))) {
                    Integer count = 1;
                    BigDecimal ratio = BigDecimal.ONE;
                    BigDecimal amount = dataList.stream().map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                    for (CustomerGainsAndLossesVo vo : dataList) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (amount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(amount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == dataList.size()) {
                            vo.setProductionCosts(ObjectUtils.defaultIfNull(vo.getProductionCosts(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal productionCosts = entry.getValue().multiply(ratio);
                            totalPlanAmount = totalPlanAmount.subtract(productionCosts);
                            vo.setProductionCosts(ObjectUtils.defaultIfNull(vo.getProductionCosts(), BigDecimal.ZERO).add(productionCosts));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            }
        }

    }


    /**
     * 分配随单的运输成本
     *
     * @param productionCostPlanAmountMap
     * @param productionCostProductPhaseCanShareMap
     */
    public void calMatchingGiftProductTransportCost(Map<List<String>, BigDecimal> productionCostPlanAmountMap, Map<List<String>, String> productionCostProductPhaseCanShareMap) {
        for (Map.Entry<List<String>, BigDecimal> entry : productionCostPlanAmountMap.entrySet()) {
            List<String> list = entry.getKey();
            BigDecimal totalPlanAmount = entry.getValue();
            BigDecimal totalPlanIncomeAmount = BigDecimal.ZERO;
            List<String> indexKeyList = Lists.newArrayList();
            for (CustomerGainsAndLossesVo vo : dataList) {
                String itemCode = vo.getIndexKey().split(column)[0];
                if (list.contains(itemCode)) {
                    totalPlanIncomeAmount = totalPlanIncomeAmount.add(vo.getPlanIncome());
                    indexKeyList.add(vo.getIndexKey());
                }
            }
            //判断不为空 则按照分摊占比计算
            if (CollectionUtils.isNotEmpty(indexKeyList)) {
                Integer count = 1;
                BigDecimal ratio = BigDecimal.ONE;
                for (CustomerGainsAndLossesVo vo : dataList) {
                    if (indexKeyList.contains(vo.getIndexKey())) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(totalPlanIncomeAmount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == indexKeyList.size()) {
                            vo.setProductTransportCost(ObjectUtils.defaultIfNull(vo.getProductTransportCost(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal productTransportCost = entry.getValue().multiply(calRatio);
                            totalPlanAmount = totalPlanAmount.subtract(productTransportCost);
                            vo.setProductTransportCost(ObjectUtils.defaultIfNull(vo.getProductTransportCost(), BigDecimal.ZERO).add(productTransportCost));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            } else {
                //判断是需要分摊的
                if (productionCostProductPhaseCanShareMap.containsKey(entry.getKey()) && BooleanEnum.TRUE.getCapital().equals(productionCostProductPhaseCanShareMap.get(entry.getKey()))) {
                    Integer count = 1;
                    BigDecimal ratio = BigDecimal.ONE;
                    BigDecimal amount = dataList.stream().map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                    for (CustomerGainsAndLossesVo vo : dataList) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (amount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(amount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == dataList.size()) {
                            vo.setProductTransportCost(ObjectUtils.defaultIfNull(vo.getProductTransportCost(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal productTransportCost = entry.getValue().multiply(ratio);
                            totalPlanAmount = totalPlanAmount.subtract(productTransportCost);
                            vo.setProductTransportCost(ObjectUtils.defaultIfNull(vo.getProductTransportCost(), BigDecimal.ZERO).add(productTransportCost));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            }
        }

    }


    /**
     * 搭赠运输费用
     */
    public void calPolicyProductTransportCost() {
        List<MarketingPlanCase> policyCaseList = this.caseList.stream()
                .filter(x -> MarketingPlanCaseTypeEnum.matching_gift.getCode().equals(x.getCaseType()))
                .filter(x -> ObjectUtils.isNotEmpty(x.getItemCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(policyCaseList)) return;
        Set<String> materialCodeSet = policyCaseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getPolicyProductCode()))
                .map(MarketingPlanCase::getPolicyProductCode).collect(Collectors.toSet());
        List<String> companyCodes = policyCaseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCompanyCode()))
                .map(MarketingPlanCase::getCompanyCode).distinct().collect(Collectors.toList());

        for (MarketingPlanCase aCase : policyCaseList) {
            String key = aCase.getCustomerCode() + aCase.getPolicyProductCode();
            if (cusProductSalesPlanMap.containsKey(key)) {
                aCase.setTransportType(cusProductSalesPlanMap.get(key).getTransportType());
            }
        }
        List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialCodeSet, companyCodes, year);
        Map<String, MaterialCostVo> materialCostVoMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column +
                x.getCompanyCode() + column + x.getYearStr(), Function.identity()));

        Map<String, BigDecimal> materialCostMap = Maps.newHashMap();
        for (MarketingPlanCase planCase : policyCaseList) {
            String key = planCase.getPolicyProductCode() + column + planCase.getCompanyCode() + column + year;
            if (materialCostVoMap.containsKey(key)) {
                MaterialCostVo materialCostVo = materialCostVoMap.get(key);
                BigDecimal price = BigDecimal.ZERO;
                if (MarketingTransportEnum.bulk_cargo.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getLargeLogisticsPrice();
                } else if (MarketingTransportEnum.express.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getExpressLogisticsPrice();
                } else if (MarketingTransportEnum.cold_chain.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getColdLogisticsPrice();
                } else if (MarketingTransportEnum.factory.getCode().equals(planCase.getTransportType())) {
                    price = materialCostVo.getFactoryStraightPrice();
                }
                materialCostMap.put(planCase.getPolicyProductCode(), price);

            }
        }
        for (MarketingPlanCase aCase : this.caseList) {
            //判断是随单的
            if (aCase.getCaseType().equals(MarketingPlanCaseTypeEnum.matching_gift.getCode())) {
                BigDecimal cost = materialCostMap.getOrDefault(aCase.getPolicyProductCode(), BigDecimal.ZERO);
                aCase.setPolicyProductTransportCost(cost.multiply(ObjectUtils.defaultIfNull(aCase.getGiftQuantity(), BigDecimal.ZERO)));
            }
        }
    }


    /**
     * 计算规划总额
     *
     * @param planAmountMap
     */
    public void calPlanTotalAmount(Map<List<String>, BigDecimal> planAmountMap, Map<List<String>, String> productPhaseCanShareMap) {
        for (Map.Entry<List<String>, BigDecimal> entry : planAmountMap.entrySet()) {
            List<String> list = entry.getKey();
            BigDecimal totalPlanAmount = entry.getValue();
            BigDecimal totalPlanIncomeAmount = BigDecimal.ZERO;
            List<String> indexKeyList = Lists.newArrayList();
            for (CustomerGainsAndLossesVo vo : dataList) {
                String itemCode = vo.getIndexKey().split(column)[0];
                if (list.contains(itemCode)) {
                    totalPlanIncomeAmount = totalPlanIncomeAmount.add(vo.getPlanIncome());
                    indexKeyList.add(vo.getIndexKey());
                }
            }
            //判断不为空 则按照分摊占比计算
            if (CollectionUtils.isNotEmpty(indexKeyList)) {
                Integer count = 1;
                BigDecimal ratio = BigDecimal.ONE;
                for (CustomerGainsAndLossesVo vo : dataList) {
                    if (indexKeyList.contains(vo.getIndexKey())) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(totalPlanIncomeAmount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == indexKeyList.size()) {
                            vo.setPlanTotalAmount(ObjectUtils.defaultIfNull(vo.getPlanTotalAmount(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal planTotalAmount = entry.getValue().multiply(calRatio);
                            totalPlanAmount = totalPlanAmount.subtract(planTotalAmount);
                            vo.setPlanTotalAmount(ObjectUtils.defaultIfNull(vo.getPlanTotalAmount(), BigDecimal.ZERO).add(planTotalAmount));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            } else {
                //判断是需要分摊的
                if (productPhaseCanShareMap.containsKey(entry.getKey()) && BooleanEnum.TRUE.getCapital().equals(productPhaseCanShareMap.get(entry.getKey()))) {
                    Integer count = 1;
                    BigDecimal ratio = BigDecimal.ONE;
                    BigDecimal amount = dataList.stream().map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                    for (CustomerGainsAndLossesVo vo : dataList) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (amount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(amount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == dataList.size()) {
                            vo.setPlanTotalAmount(ObjectUtils.defaultIfNull(vo.getPlanTotalAmount(), BigDecimal.ZERO).add(totalPlanAmount));
                        } else {
                            BigDecimal planTotalAmount = entry.getValue().multiply(ratio);
                            totalPlanAmount = totalPlanAmount.subtract(planTotalAmount);
                            vo.setPlanTotalAmount(ObjectUtils.defaultIfNull(vo.getPlanTotalAmount(), BigDecimal.ZERO).add(planTotalAmount));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            }
        }
        for (CustomerGainsAndLossesVo data : dataList) {
            data.setMarketingCost(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanTotalAmount())) {
                data.setMarketingCost(data.getPlanTotalAmount());
            }
            //规划费率
            data.setPlanRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && ObjectUtils.isNotEmpty(data.getPlanTotalAmount()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                //规划总额/规划收入
                data.setPlanRatio(data.getPlanTotalAmount().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
            //费率偏差 = 规划费率-预算费率
            data.setRatioDeviation(data.getPlanRatio().subtract(data.getBudgetRatio()));
        }
    }


    /**
     * 计算利润率
     *
     * @return
     */
    @Override
    public ItemEstimationBuilder calProfitRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            List<MarketingSalesPlanVo> salesVolumeList = salesPlanMap.entrySet().stream().filter(x -> data.getIndexKey().contains(x.getKey())).flatMap(x -> x.getValue().stream()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(salesVolumeList)) {
                //销售计划的物料-找到物料成本价，用销售计划上的成本中心+年月匹配得到物料 在用预估销售额*物料成本价
                Map<String, BigDecimal> materialSalesVolumeMap = salesVolumeList.stream().collect(Collectors.groupingBy(MarketingSalesPlanVo::getMaterialCode, Collectors.mapping(MarketingSalesPlanVo::getEstimatedSalesVolume, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                BigDecimal productionCosts = BigDecimal.ZERO;
                for (Map.Entry<String, BigDecimal> entry : materialSalesVolumeMap.entrySet()) {
                    BigDecimal costPrice = materialMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
                    productionCosts = productionCosts.add(costPrice.multiply(entry.getValue()));
                }
                //生产成本
                data.setProductionCosts(ObjectUtils.defaultIfNull(data.getProductionCosts(), BigDecimal.ZERO).add(productionCosts));
                //产品运输费用计算
                List<String> companyCodes = Lists.newArrayList();
                for (MarketingSalesPlanVo salesPlan : salesVolumeList) {
                    if (costCenterMap.containsKey(salesPlan.getCostCenterCode())) {
                        MdmCostCenterVo costCenterVo1 = costCenterMap.get(salesPlan.getCostCenterCode());
                        companyCodes.add(costCenterVo1.getCompanyCode());
                        salesPlan.setCostCenterCompanyCode(costCenterVo1.getCompanyCode());
                    }
                }
                String year = years.split("-")[0];
                //查询物料成本
                List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(materialSalesVolumeMap.keySet(), companyCodes, year);
                Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column + x.getCompanyCode() + column + x.getYearStr(), Function.identity()));
                //产品运输费用
                BigDecimal productTransport = BigDecimal.ZERO;
                for (MarketingSalesPlanVo plan : salesVolumeList) {
                    String key = plan.getMaterialCode() + column + plan.getCompanyCode() + column + year;
                    if (materialCostMap.containsKey(key)) {
                        MaterialCostVo materialCostVo = materialCostMap.get(key);
                        BigDecimal price = BigDecimal.ZERO;
                        if (MarketingTransportEnum.bulk_cargo.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getLargeLogisticsPrice();
                        } else if (MarketingTransportEnum.express.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getExpressLogisticsPrice();
                        } else if (MarketingTransportEnum.cold_chain.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getColdLogisticsPrice();
                        } else if (MarketingTransportEnum.factory.getCode().equals(plan.getTransportType())) {
                            price = materialCostVo.getFactoryStraightPrice();
                        }
                        productTransport = productTransport.add(price.multiply(plan.getEstimatedSalesVolume()));

                    }
                }
                data.setProductTransportCost(ObjectUtils.defaultIfNull(data.getProductTransportCost(), BigDecimal.ZERO).add(productTransport));
            }
        }
        //计算周边物料费用
        this.peripheryTransportCost();
        return this;
    }


    public void peripheryTransportCost() {
        //TODO 样品与赠品费用细类
        //计算物料模板的费用运输
        Map<String, List<MarketingPlanCase>> materialCaseListMap = caseList.stream().filter(x -> MarketingPlanCaseTypeEnum.material.getCode().equals(x.getCaseType())).collect(Collectors.groupingBy(x -> x.getItemCode()));
        for (Map.Entry<String, List<MarketingPlanCase>> entry : materialCaseListMap.entrySet()) {
            BigDecimal peripheryTransportCost = BigDecimal.ZERO;
            //标准物料
            BigDecimal materialStandardAmount = entry.getValue().stream().filter(x -> MarketingPlanMaterialTypeEnum.standard_material.getCode().equals(x.getSellMaterialType())).map(MarketingPlanCase::getLineNoTaxApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalStandardMaterialAmount = materialStandardAmount.multiply(BigDecimal.valueOf(0.15));
            peripheryTransportCost = peripheryTransportCost.add(totalStandardMaterialAmount);
            //非标准物料
            List<MarketingPlanCase> notStandardMaterialList = entry.getValue().stream().filter(x -> MarketingPlanMaterialTypeEnum.not_standard_material.getCode().equals(x.getSellMaterialType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notStandardMaterialList)) {
                List<String> companyCodes = Lists.newArrayList();
                Set<String> notStandardMaterialCodes = Sets.newHashSet();
                for (MarketingPlanCase planCase : notStandardMaterialList) {
                    companyCodes.add(planCase.getCompanyCode());
                    notStandardMaterialCodes.add(planCase.getMaterialCode());
                }
                //查询物料成本
                String year = years.split("-")[0];
                List<MaterialCostVo> materialCostVoList = materialCostVoService.findListByCodesAndCompanyCodesAndYear(notStandardMaterialCodes, companyCodes, year);
                Map<String, MaterialCostVo> materialCostMap = materialCostVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + column + x.getCompanyCode() + column + x.getYearStr(), Function.identity()));
                for (MarketingPlanCase planCase : notStandardMaterialList) {
                    String key = planCase.getMaterialCode() + column + planCase.getCompanyCode() + column + year;
                    if (materialCostMap.containsKey(key)) {
                        MaterialCostVo materialCostVo = materialCostMap.get(key);
                        BigDecimal price = BigDecimal.ZERO;
                        if (MarketingTransportEnum.bulk_cargo.getCode().equals(planCase.getTransportType())) {
                            price = materialCostVo.getLargeLogisticsPrice();
                        } else if (MarketingTransportEnum.express.getCode().equals(planCase.getTransportType())) {
                            price = materialCostVo.getExpressLogisticsPrice();
                        } else if (MarketingTransportEnum.cold_chain.getCode().equals(planCase.getTransportType())) {
                            price = materialCostVo.getColdLogisticsPrice();
                        } else if (MarketingTransportEnum.factory.getCode().equals(planCase.getTransportType())) {
                            price = materialCostVo.getFactoryStraightPrice();
                        }
                        peripheryTransportCost = peripheryTransportCost.add(price.multiply(planCase.getMaterialNum()));

                    }
                }
            }
            BigDecimal finanlPeripheryTransportCost = peripheryTransportCost;
            ProductPhaseVo productPhaseVo = productPhaseMap.get(entry.getKey());
            List<String> itemCodes = productPhaseListMap.get(entry.getKey());
            //判断是需要分摊的
            List<String> indexKeyList = Lists.newArrayList();
            BigDecimal totalPlanIncomeAmount = BigDecimal.ZERO;
            for (CustomerGainsAndLossesVo vo : dataList) {
                String itemCode = vo.getIndexKey().split(column)[0];
                if (itemCodes.contains(itemCode)) {
                    indexKeyList.add(vo.getIndexKey());
                    totalPlanIncomeAmount = totalPlanIncomeAmount.add(vo.getPlanIncome());
                }
            }
            //判断不为空 则按照分摊占比计算
            if (CollectionUtils.isNotEmpty(indexKeyList)) {
                Integer count = 1;
                BigDecimal ratio = BigDecimal.ONE;
                for (CustomerGainsAndLossesVo vo : dataList) {
                    if (indexKeyList.contains(vo.getIndexKey())) {
                        BigDecimal calRatio = BigDecimal.ZERO;
                        if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0) {
                            calRatio = vo.getPlanIncome().divide(totalPlanIncomeAmount, 4, BigDecimal.ROUND_HALF_DOWN);
                        }
                        if (count == indexKeyList.size()) {
                            vo.setPeripheryTransportCost(ObjectUtils.defaultIfNull(vo.getPeripheryTransportCost(), BigDecimal.ZERO).add(peripheryTransportCost));
                        } else {
                            BigDecimal value = finanlPeripheryTransportCost.multiply(calRatio);
                            peripheryTransportCost = peripheryTransportCost.subtract(value);
                            vo.setPeripheryTransportCost(ObjectUtils.defaultIfNull(vo.getPeripheryTransportCost(), BigDecimal.ZERO).add(value));
                        }
                        ratio = ratio.subtract(calRatio);
                        count++;
                    }
                }
            } else if (BooleanEnum.TRUE.getCapital().equals(productPhaseVo.getCanShare())) {
                //判断是分摊
                Integer count = 1;
                BigDecimal ratio = BigDecimal.ONE;
                BigDecimal amount = dataList.stream().map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                for (CustomerGainsAndLossesVo vo : dataList) {
                    BigDecimal calRatio = BigDecimal.ZERO;
                    if (amount.compareTo(BigDecimal.ZERO) > 0) {
                        calRatio = vo.getPlanIncome().divide(amount, 4, BigDecimal.ROUND_HALF_DOWN);
                    }
                    if (count == dataList.size()) {
                        vo.setPeripheryTransportCost(ObjectUtils.defaultIfNull(vo.getPeripheryTransportCost(), BigDecimal.ZERO).add(peripheryTransportCost));
                    } else {
                        BigDecimal value = finanlPeripheryTransportCost.multiply(ratio);
                        peripheryTransportCost = peripheryTransportCost.subtract(value);
                        vo.setPeripheryTransportCost(ObjectUtils.defaultIfNull(vo.getPeripheryTransportCost(), BigDecimal.ZERO).add(value));
                    }
                    ratio = ratio.subtract(calRatio);
                    count++;
                }
            }

        }

        for (CustomerGainsAndLossesVo data : dataList) {
            data.setPeripheryTransportCost(ObjectUtils.defaultIfNull(data.getPeripheryTransportCost(), BigDecimal.ZERO));
            //规划利润额=规划收入-生产成本-产品运输费用-周边运输费用-营销费用
            data.setPlanProfitMargin(data.getPlanIncome().subtract(data.getProductionCosts()).subtract(data.getPeripheryTransportCost()).subtract(ObjectUtils.defaultIfNull(data.getMarketingCost(), BigDecimal.ZERO)));
            //利润率=规划利润额/规划收入
            data.setProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                data.setProfitRatio(data.getPlanProfitMargin().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }

    }


    /**
     * 计算毛利率
     *
     * @return
     */
    @Override
    public ItemEstimationBuilder calGrossProfitRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //规划毛利额=规划收入-生产成本
            data.setPlanGrossProfitMargin(data.getPlanIncome().subtract(data.getProductionCosts()));
            //毛利率=规划毛利额/规划收入
            data.setGrossProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(data.getPlanIncome()) && data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setGrossProfitRatio(data.getPlanGrossProfitMargin().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public ItemEstimationBuilder calLogisticsRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //物流费率=(产品运输费用+周边运输费用)/规划收入
            data.setLogisticsRatio(BigDecimal.ZERO);
            if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setLogisticsRatio((data.getProductTransportCost().add(data.getPeripheryTransportCost())).divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public ItemEstimationBuilder calMarketingRatio() {
        for (CustomerGainsAndLossesVo data : dataList) {
            //营销费率=营销费用/规划收入
            data.setMarketingRatio(BigDecimal.ZERO);
            if (data.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
                data.setMarketingRatio(ObjectUtils.defaultIfNull(data.getMarketingCost(), BigDecimal.ZERO).divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public ItemEstimationBuilder calPublicShareRatio() {
        List<OrgVo> list = orgVoService.findAllParentByOrgCodes(Lists.newArrayList(orgCodeSet));
        List<String> list1 = list.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
        BigDecimal ratio = publicShareRatioService.findRatioByCondition(Lists.newArrayList(list1), years);
        for (CustomerGainsAndLossesVo vo : dataList) {
            vo.setPublicShareRatio(ratio);
            BigDecimal publicShare = vo.getPlanIncome().multiply(vo.getPublicShareRatio());
            //规划利润额=规划收入-生产成本-产品运输费用-周边运输费用-营销费用
            vo.setPlanProfitMargin(vo.getPlanIncome().subtract(vo.getProductionCosts()).subtract(vo.getProductTransportCost()).subtract(vo.getPeripheryTransportCost()).subtract(vo.getMarketingCost()).subtract(publicShare));
            //利润率=规划利润额/规划收入
            vo.setProfitRatio(BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(vo.getPlanIncome()) && vo.getPlanIncome().compareTo(BigDecimal.ZERO) != 0) {
                vo.setProfitRatio(vo.getPlanProfitMargin().divide(vo.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        return this;
    }

    @Override
    public ItemEstimationBuilder calSecondCategory() {
        // 通过stream进行分组汇总
        Map<String, Map<String, BigDecimal>> categoryItemApplyAmount = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCategoryCode()) && ObjectUtils.isNotEmpty(x.getItemCode()) && ObjectUtils.isNotEmpty(x.getLineNoTaxApplyAmount())).collect(Collectors.groupingBy(MarketingPlanCase::getCategoryCode, Collectors.toMap(MarketingPlanCase::getItemCode, MarketingPlanCase::getLineNoTaxApplyAmount, BigDecimal::add)));

        // 遍历大类和项目的映射关系
        costTypeCategoryMap.forEach((secondCategory, firstCategoryList) -> {
            categoryItemApplyAmount.forEach((category, itemMap) -> {
                if (firstCategoryList.contains(category)) {
                    itemMap.forEach((itemCode, value) -> {
                        ProductPhaseVo productPhaseVo = productPhaseMap.get(itemCode);
                        if (BooleanEnum.TRUE.getCapital().equals(productPhaseVo.getCanShare())) {
                            allocateToDataList(itemCode, value, secondCategory);
                        } else {
                            allocateDirectlyToDataList(itemCode, value, secondCategory);
                        }
                    });
                }
            });
        });
        this.transRatio();
        return this;
    }

    private void allocateToDataList(String itemCode, BigDecimal value, String secondCategory) {
        List<String> itemCodes = productPhaseListMap.get(itemCode);
        BigDecimal totalPlanIncomeAmount = dataList.stream().filter(vo -> itemCodes.contains(vo.getIndexKey().split(column)[0])).map(CustomerGainsAndLossesVo::getPlanIncome).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal remainingValue = value;
            for (int i = 0; i < dataList.size(); i++) {
                CustomerGainsAndLossesVo vo = dataList.get(i);
                if (itemCodes.contains(vo.getIndexKey().split(column)[0])) {
                    BigDecimal calRatio = BigDecimal.ZERO;
                    if (totalPlanIncomeAmount.compareTo(BigDecimal.ZERO) > 0) {
                        calRatio = vo.getPlanIncome().divide(totalPlanIncomeAmount, 4, BigDecimal.ROUND_HALF_DOWN);
                    }
                    BigDecimal allocation = i == dataList.size() - 1 ? remainingValue : value.multiply(calRatio);
                    remainingValue = remainingValue.subtract(allocation);
                    applyAllocation(vo, allocation, secondCategory);
                }
            }
        }
    }

    private void allocateDirectlyToDataList(String itemCode, BigDecimal value, String secondCategory) {
        dataList.stream().filter(vo -> vo.getIndexKey().split(column)[0].equals(itemCode)).forEach(vo -> applyAllocation(vo, value, secondCategory));
    }

    private void applyAllocation(CustomerGainsAndLossesVo vo, BigDecimal value, String secondCategory) {
        BigDecimal ratio = BigDecimal.ZERO;
        if (vo.getPlanIncome().compareTo(BigDecimal.ZERO) > 0) {
            ratio = value.divide(vo.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
        }
        switch (SecondCategoryEnum.value(secondCategory)) {
            case contract:
                vo.setContract(ObjectUtils.defaultIfNull(vo.getContract(), BigDecimal.ZERO).add(ratio));
                break;
            case generalization:
                vo.setGeneralization(ObjectUtils.defaultIfNull(vo.getGeneralization(), BigDecimal.ZERO).add(ratio));
                break;
            case promotion:
                vo.setPromotion(ObjectUtils.defaultIfNull(vo.getPromotion(), BigDecimal.ZERO).add(ratio));
                break;
            case salesReward:
                vo.setSalesReward(ObjectUtils.defaultIfNull(vo.getSalesReward(), BigDecimal.ZERO).add(ratio));
                break;
            case callback:
                vo.setPromotion(ObjectUtils.defaultIfNull(vo.getPromotion(), BigDecimal.ZERO).add(ratio));
                break;
            case display:
                vo.setDisplay(ObjectUtils.defaultIfNull(vo.getDisplay(), BigDecimal.ZERO).add(ratio));
                break;
            case disseminateS:
                vo.setDisseminate(ObjectUtils.defaultIfNull(vo.getDisseminate(), BigDecimal.ZERO).add(ratio));
                break;
            default:
                break;
        }
    }

    private void transRatio() {
        for (CustomerGainsAndLossesVo vo : dataList) {
            if (ObjectUtils.isNotEmpty(vo.getBudgetIncome())) {
                vo.setBudgetIncome(vo.getBudgetIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanIncome())) {
                vo.setPlanIncome(vo.getPlanIncome().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            if (ObjectUtils.isNotEmpty(vo.getIncomeAchieveRatio())) {
                vo.setIncomeAchieveRatioStr(vo.getIncomeAchieveRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getBudgetRatio())) {
                vo.setBudgetRatioStr(vo.getBudgetRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPlanRatio())) {
                vo.setPlanRatioStr(vo.getPlanRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getRatioDeviation())) {
                vo.setRatioDeviationStr(vo.getRatioDeviation().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getProfitRatio())) {
                vo.setProfitRatioStr(vo.getProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGrossProfitRatio())) {
                vo.setGrossProfitRatioStr(vo.getGrossProfitRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getLogisticsRatio())) {
                vo.setLogisticsRatioStr(vo.getLogisticsRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getMarketingRatio())) {
                vo.setMarketingRatioStr(vo.getMarketingRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPublicShareRatio())) {
                vo.setPublicShareRatioStr(vo.getPublicShareRatio().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getPromotion())) {
                vo.setPromotionStr(vo.getPromotion().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getCallback())) {
                vo.setCallbackStr(vo.getCallback().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisplay())) {
                vo.setDisplayStr(vo.getDisplay().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getGeneralization())) {
                vo.setGeneralizationStr(vo.getGeneralization().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getDisseminate())) {
                vo.setDisseminateStr(vo.getDisseminate().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getSalesReward())) {
                vo.setSalesRewardStr(vo.getSalesReward().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
            if (ObjectUtils.isNotEmpty(vo.getContract())) {
                vo.setContractStr(vo.getContract().multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN) + "%");
            }
        }
    }

    //TODO 别删这个！！！
//    public ItemEstimationBuilder calSecondCategory() {
//
//        Map<String, BigDecimal> secondCategoryMap = Maps.newHashMap();
//        Map<String, Map<String, BigDecimal>> categoryItemApplyAmount = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCategoryCode()) &&
//                        ObjectUtils.isNotEmpty(x.getItemCode()) && ObjectUtils.isNotEmpty(x.getApplyAmount()))
//                .collect(Collectors.groupingBy(x -> x.getCategoryCode(),
//                        Collectors.toMap(MarketingPlanCase::getItemCode, MarketingPlanCase::getApplyAmount, BigDecimal::add)));
//
//        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
//            for (Map.Entry<String, Map<String, BigDecimal>> mapEntry : categoryItemApplyAmount.entrySet()) {
//                //判断是包含当前大类
//                if (entry.getValue().contains(mapEntry.getKey())) {
//                    Map<String, BigDecimal> map = mapEntry.getValue();
//                    for (Map.Entry<String, BigDecimal> l : map.entrySet()) {
//                        ProductPhaseVo productPhaseVo = productPhaseMap.get(l.getKey());
//                        //判断是需要分摊的
//                        if (BooleanEnum.TRUE.getCapital().equals(productPhaseVo.getCanShare())) {
//                            List<String> itemCodes = productPhaseListMap.get(l.getKey());
//                            BigDecimal dataValue = l.getValue();
//                            //判断是需要分摊的
//                            List<String> indexKeyList = Lists.newArrayList();
//                            BigDecimal totalPlanIncomeAmount = BigDecimal.ZERO;
//                            for (CustomerGainsAndLossesVo vo : dataList) {
//                                String itemCode = vo.getIndexKey().split(column)[0];
//                                if (itemCodes.contains(itemCode)) {
//                                    indexKeyList.add(vo.getIndexKey());
//                                    totalPlanIncomeAmount = totalPlanIncomeAmount.add(vo.getPlanIncome());
//                                }
//                            }
//                            //判断不为空 则按照分摊占比计算
//                            if (CollectionUtils.isNotEmpty(indexKeyList)) {
//                                Integer count = 1;
//                                BigDecimal ratio = BigDecimal.ONE;
//                                for (CustomerGainsAndLossesVo vo : dataList) {
//                                    if (indexKeyList.contains(vo.getIndexKey())) {
//                                        BigDecimal calRatio = vo.getPlanIncome().divide(totalPlanIncomeAmount, 4, BigDecimal.ROUND_HALF_DOWN);
//                                        if (count == indexKeyList.size()) {
//                                            BigDecimal valueRatio = dataValue.divide(vo.getPlanIncome(),4,BigDecimal.ROUND_HALF_DOWN);
//                                            if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
//                                                vo.setContract(ObjectUtils.defaultIfNull(vo.getContract(), BigDecimal.ZERO).add(valueRatio));
//                                            } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
//                                                vo.setGeneralization(ObjectUtils.defaultIfNull(vo.getGeneralization(), BigDecimal.ZERO).add(valueRatio));
//                                            } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
//                                                vo.setPromotion(ObjectUtils.defaultIfNull(vo.getPromotion(), BigDecimal.ZERO).add(valueRatio));
//                                            } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
//                                                vo.setSalesReward(ObjectUtils.defaultIfNull(vo.getSalesReward(), BigDecimal.ZERO).add(valueRatio));
//                                            }
//                                        } else {
//                                            BigDecimal value = l.getValue().multiply(calRatio);
//                                            dataValue = dataValue.subtract(value);
//                                            BigDecimal valueRatio = value.divide(vo.getPlanIncome(),4,BigDecimal.ROUND_HALF_DOWN);
//                                            if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
//                                                vo.setContract(ObjectUtils.defaultIfNull(vo.getContract(), BigDecimal.ZERO).add(valueRatio));
//                                            } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
//                                                vo.setGeneralization(ObjectUtils.defaultIfNull(vo.getGeneralization(), BigDecimal.ZERO).add(valueRatio));
//                                            } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
//                                                vo.setPromotion(ObjectUtils.defaultIfNull(vo.getPromotion(), BigDecimal.ZERO).add(valueRatio));
//                                            } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
//                                                vo.setSalesReward(ObjectUtils.defaultIfNull(vo.getSalesReward(), BigDecimal.ZERO).add(valueRatio));
//                                            }
//                                        }
//                                        ratio = ratio.subtract(calRatio);
//                                        count++;
//                                    }
//                                }
//                            } else if (BooleanEnum.TRUE.getCapital().equals(productPhaseVo.getCanShare())) {
//                                //判断是分摊
//                                Integer count = 1;
//                                BigDecimal ratio = BigDecimal.ONE;
//                                BigDecimal amount = dataList.stream().map(x -> Optional.ofNullable(x.getPlanIncome()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
//                                for (CustomerGainsAndLossesVo vo : dataList) {
//                                    BigDecimal calRatio = vo.getPlanIncome().divide(amount, 4, BigDecimal.ROUND_HALF_DOWN);
//                                    if (count == dataList.size()) {
//                                        BigDecimal valueRatio = dataValue.divide(vo.getPlanIncome(),4,BigDecimal.ROUND_HALF_DOWN);
//                                        if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
//                                            vo.setContract(ObjectUtils.defaultIfNull(vo.getContract(), BigDecimal.ZERO).add(valueRatio));
//                                        } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
//                                            vo.setGeneralization(ObjectUtils.defaultIfNull(vo.getGeneralization(), BigDecimal.ZERO).add(valueRatio));
//                                        } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
//                                            vo.setPromotion(ObjectUtils.defaultIfNull(vo.getPromotion(), BigDecimal.ZERO).add(valueRatio));
//                                        } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
//                                            vo.setSalesReward(ObjectUtils.defaultIfNull(vo.getSalesReward(), BigDecimal.ZERO).add(valueRatio));
//                                        }
//                                    } else {
//                                        BigDecimal value = l.getValue().multiply(ratio);
//                                        dataValue = dataValue.subtract(value);
//                                        BigDecimal valueRatio = value.divide(vo.getPlanIncome(),4,BigDecimal.ROUND_HALF_DOWN);
//                                        if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
//                                            vo.setContract(ObjectUtils.defaultIfNull(vo.getContract(), BigDecimal.ZERO).add(valueRatio));
//                                        } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
//                                            vo.setGeneralization(ObjectUtils.defaultIfNull(vo.getGeneralization(), BigDecimal.ZERO).add(valueRatio));
//                                        } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
//                                            vo.setPromotion(ObjectUtils.defaultIfNull(vo.getPromotion(), BigDecimal.ZERO).add(valueRatio));
//                                        } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
//                                            vo.setSalesReward(ObjectUtils.defaultIfNull(vo.getSalesReward(), BigDecimal.ZERO).add(valueRatio));
//                                        }
//                                    }
//                                    ratio = ratio.subtract(calRatio);
//                                    count++;
//                                }
//                            }
//                        } else {
//                            for (CustomerGainsAndLossesVo data : dataList) {
//                                String itemCode = data.getIndexKey().split(column)[0];
//                                BigDecimal value = l.getValue().divide(data.getPlanIncome(), 4, BigDecimal.ROUND_HALF_DOWN);
//                                if (itemCode.equals(l.getKey())) {
//                                    if (SecondCategoryEnum.contract.getName().equals(entry.getKey())) {
//                                        data.setContract(ObjectUtils.defaultIfNull(data.getContract(), BigDecimal.ZERO).add(value));
//                                    } else if (SecondCategoryEnum.generalization.getName().equals(entry.getKey())) {
//                                        data.setGeneralization(ObjectUtils.defaultIfNull(data.getGeneralization(), BigDecimal.ZERO).add(value));
//                                    } else if (SecondCategoryEnum.promotion.getName().equals(entry.getKey())) {
//                                        data.setPromotion(ObjectUtils.defaultIfNull(data.getPromotion(), BigDecimal.ZERO).add(value));
//                                    } else if (SecondCategoryEnum.salesReward.getName().equals(entry.getKey())) {
//                                        data.setSalesReward(ObjectUtils.defaultIfNull(data.getSalesReward(), BigDecimal.ZERO).add(value));
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        return this;
//    }
}
