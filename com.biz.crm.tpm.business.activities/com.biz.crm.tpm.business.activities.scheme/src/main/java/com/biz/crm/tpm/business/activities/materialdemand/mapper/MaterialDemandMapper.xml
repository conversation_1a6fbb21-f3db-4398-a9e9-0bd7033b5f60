<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.materialdemand.mapper.MaterialDemandMapper">


    <sql id="conditions">
        <if test="vo.tenantCode != null and vo.tenantCode != ''">
            and a.tenant_code = #{vo.tenantCode}
        </if>
        <if test="vo.demandCode != null and vo.demandCode !=''">
            <bind name="demandCode" value="vo.demandCode + '%'"/>
            and a.demand_code like #{demandCode}
        </if>
        <if test="vo.demandName != null and vo.demandName != ''">
            <bind name="demandName" value="'%' + vo.demandName + '%'"/>
            and a.demand_name like #{demandName}
        </if>
        <if test="vo.orgName != null and vo.orgName != ''">
            <bind name="orgName" value="'%' + vo.orgName + '%'"/>
            and a.org_name like #{orgName}
        </if>
        <if test="vo.processStatus != null and vo.processStatus != ''">
            and a.process_status = #{vo.processStatus}
        </if>
        <if test="vo.processNumber != null and vo.processNumber != ''">
            <bind name="processNumber" value="'%' + vo.processNumber + '%'"/>
            and a.process_number like #{processNumber}
        </if>
        <if test="vo.years != null and vo.years != ''">
            and a.years = #{vo.years}
        </if>
        <if test="vo.collectStatus != null and vo.collectStatus != ''">
            and a.collect_status = #{vo.collectStatus}
        </if>
        order by a.create_time desc,a.id desc
    </sql>
    <select id="findList" resultType="com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandVo">
        select a.* from tpm_material_demand a
        where a.del_flag = '${@<EMAIL>()}'
        <include refid="conditions"/>
    </select>

    <select id="findListByOrgCodes" resultType="com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo">
        select b.* from tpm_material_demand a
        left join tpm_material_demand_detail b on a.demand_code = b.demand_code
        where
        a.del_flag = '${@<EMAIL>()}'
        and a.org_code in
        <foreach collection="orgCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
        <if test="processStatusList != null and processStatusList.size()>0">
            and a.process_status in
            <foreach collection="processStatusList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectMaterialDemandDetailList" resultType="com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo">
        select b.* from tpm_material_demand a
        left join tpm_material_demand_detail b on a.demand_code = b.demand_code
        where
        a.del_flag = '${@<EMAIL>()}'
        and a.demand_code in
        <foreach collection="demandCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
        <if test="inExcludeDemandDetailCodes != null and inExcludeDemandDetailCodes.size()>0">
            and b.demand_detail_code not in
            <foreach collection="inExcludeDemandDetailCodes" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="findDetailExportByDto"
            resultType="com.biz.crm.tpm.business.activities.materialdemand.exports.model.MaterialDemandDetailExportVo">
        select a.demand_name, a.org_name mainOrgName, a.remark mainRemark,b.* from tpm_material_demand a
        left join tpm_material_demand_detail b on a.demand_code = b.demand_code
        where a.del_flag = '${@<EMAIL>()}'
          and b.del_flag = '${@<EMAIL>()}'
        <include refid="conditions"/>
    </select>

</mapper>

