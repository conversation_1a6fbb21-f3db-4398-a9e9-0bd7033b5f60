package com.biz.crm.tpm.business.activities.overallplan.service.internal;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlan;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanCaseFieldsEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanFieldsEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.repository.OverPlanRepository;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanCaseService;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanOaService;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanDepartmentVo;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.OverallPlanDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.OverallPlanMainDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class OverallPlanOaServiceImpl implements OverallPlanOaService {


    @Autowired(required = false)
    private PushOaService pushOaService;
    @Resource
    private OverallPlanCaseService overallPlanCaseService;
    @Resource
    private OverPlanRepository overPlanRepository;
    @Autowired
    private UserVoService userVoService;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Value("${domain-name:}")
    private String domainName;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired
    private OrgVoService orgVoService;

    @Autowired
    private OrgOaOrgVoService orgOaOrgVoService;

    /**
     * 推送OA
     *
     * @param
     * @return
     */
    @Override
    public JSONObject pushOa(OverallPlan plan) {
        List<OverallPlanCaseVo> caseVoList = overallPlanCaseService.findListBySchemeId(plan.getId());
        Set<String> bearDepartmentCodes = Sets.newHashSet();
        for (OverallPlanCaseVo l : caseVoList) {
            List<OverallPlanDepartmentVo> bearDepartmentList = l.getBearDepartmentList();
            if (!CollectionUtils.isEmpty(bearDepartmentList)) {
                bearDepartmentCodes.addAll(bearDepartmentList.stream().map(OverallPlanDepartmentVo::getDepartmentCode).collect(Collectors.toList()));
            }
        }

        List<String> oaDepartOneCodes = getOaDepartOneCodes(Lists.newArrayList(bearDepartmentCodes));
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(plan));
        orderJsonObject.put("businessCode", plan.getSchemeCode());
        orderJsonObject.put("remark", plan.getSchemeDesc());
        // 主表 业务类型
        String businessType;
        if (plan.getSchemeType().equals(OverallPlanSchemeTypeEnum.HEAD.getCode())) {
            businessType = MqConstant.TAG_TPM_HEAD_OVERALL_PLAN;
            orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.ACTIVITY_HEADQUARTERS_GUIDE_FORM.getUrlCode() + "?id=" + plan.getId());
            orderJsonObject.put("title", "总部指引：" + plan.getSchemeName());
            orderJsonObject.put("bearDepartmentOneOaCodes", oaDepartOneCodes.stream().distinct().collect(Collectors.joining(",")));
        } else if (plan.getSchemeType().equals(OverallPlanSchemeTypeEnum.REGION.getCode())) {
            businessType = MqConstant.TAG_TPM_REGION_OVERALL_PLAN;
            orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.ACTIVITY_REGION_GUIDE_FORM.getUrlCode() + "?id=" + plan.getId());
            orderJsonObject.put("title", "大区指引：" + plan.getSchemeName());
        } else {
            return null;
        }
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        // 查询承担类型 TPM_SCHEME_BEAR_TYPE
        List<DictDataVo> bearTypeList = this.dictDataVoService.findByDictTypeCode("TPM_SCHEME_BEAR_TYPE");
        Map<String, String> bearTypeMap = bearTypeList.stream().collect(Collectors.toMap(DictDataVo::getDictCode,
                DictDataVo::getDictValue));
        if (!CollectionUtils.isEmpty(caseVoList)) {
            caseVoList.forEach(e -> {
                // 将承担类型编码转化成名称
                e.setBearType(bearTypeMap.getOrDefault(e.getBearType(), e.getBearType()));
                e.setApplyAmount(e.getApplyAmount());
                if (!CollectionUtils.isEmpty(e.getBearDepartmentList())) {
                    e.setBearDepartmentName(e.getBearDepartmentList().stream().map(m -> m.getDepartmentName()).collect(Collectors.joining(",")));
                }
            });
        }
        JSONObject response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, Arrays.asList(JSONArray.parseArray(JSON.toJSONString(caseVoList))), workflowId, workflowName,
                mainTableMethod, detailTableMethod);

        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        Validate.isTrue(response.containsKey("code"), "OA流程提交失败，OA未正常返回信息");
        Integer resultCode = response.getInteger("code");
        Validate.isTrue(resultCode == 100, String.format("提交OA流程失败,失败原因:%s", response.getString("msg")));
        JSONObject ja = response.getJSONObject("data");
        OverallPlan entity = overPlanRepository.findById(plan.getId());
        entity.setProcessNumber(ja.getString("out"));
        entity.setProcessDate(new Date());
        entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
        entity.setOaId(response.getString("oaId"));
        entity.setOaUserName(response.getString("oaUserName"));
        overPlanRepository.updateById(entity);

        return response;
    }

    private List<String> getOaDepartOneCodes(List<String> bearDepartmentCodes) {
        if (CollectionUtils.isEmpty(bearDepartmentCodes)) {
            return Lists.newArrayList();
        }
        List<OrgVo> allParentByOrgCodes = orgVoService.findAllParentByOrgCodes(bearDepartmentCodes);
        List<String> departOneCodes = Lists.newArrayList();
        allParentByOrgCodes.forEach(orgVo -> {
            if (orgVo.getOrgType().equals(OrgTypeEnum.DIVISION.getDictCode())){
                departOneCodes.add(orgVo.getOrgCode());
            }
        });
        log.info("getOaDepartOneCodes.departOneCodes: {}", departOneCodes);
        List<String> departOneOaCodes = Lists.newArrayList();
        Map<String, List<OrgOaOrgVo>> orgOaMap = orgOaOrgVoService.findByOrgCodes(departOneCodes);
        orgOaMap.values().stream().forEach(l -> {
            List<String> oaOrgCodes = l.stream().map(OrgOaOrgVo::getOaOrgCode).distinct().collect(Collectors.toList());
            departOneOaCodes.addAll(oaOrgCodes);
        });
        log.info("getOaDepartOneCodes.oaCodes: {}", departOneOaCodes);
        return departOneOaCodes;
    }

    /**
     * 重新提交OA
     *
     * @param plan
     * @return
     */
    @Override
    public JSONObject resubmitOa(OverallPlan plan) {
        OaResubmitDto dto = new OaResubmitDto();

        List<OverallPlanCaseVo> caseVoList = overallPlanCaseService.findListBySchemeId(plan.getId());
        List<String> bearDepartmentCodes = caseVoList.stream().map(k -> k.getBearDepartmentCode()).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<String> oaDepartOneCodes = getOaDepartOneCodes(bearDepartmentCodes);

        // 获取当前登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType;
        if (plan.getSchemeType().equals(OverallPlanSchemeTypeEnum.HEAD.getCode())) {
            businessType = MqConstant.TAG_TPM_HEAD_OVERALL_PLAN;
            plan.setTpmUrl(domainName + TpmOaPageEnum.ACTIVITY_HEADQUARTERS_GUIDE_FORM.getUrlCode() + "?id=" + plan.getId());
            dto.setRequestName("总部指引：" + plan.getSchemeName());
        } else if (plan.getSchemeType().equals(OverallPlanSchemeTypeEnum.REGION.getCode())) {
            businessType = MqConstant.TAG_TPM_REGION_OVERALL_PLAN;
            plan.setTpmUrl(domainName + TpmOaPageEnum.ACTIVITY_REGION_GUIDE_FORM.getUrlCode() + "?id=" + plan.getId());
            dto.setRequestName("大区指引：" + plan.getSchemeName());
        } else {
            return null;
        }
        plan.setRemark(plan.getSchemeDesc());
        plan.setBusinessCode(plan.getSchemeCode());
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();

        dto.setBusinessCode(businessType);
        dto.setRequestId(plan.getProcessNumber());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        OverallPlanMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(plan, OverallPlanMainDto.class, LinkedHashSet.class, ArrayList.class);
        mainDto.setBearDepartmentOneOaCodes(oaDepartOneCodes.stream().distinct().collect(Collectors.joining(",")));
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));
        // 查询承担类型 TPM_SCHEME_BEAR_TYPE
        List<DictDataVo> bearTypeList = this.dictDataVoService.findByDictTypeCode("TPM_SCHEME_BEAR_TYPE");
        Map<String, String> bearTypeMap = bearTypeList.stream().collect(Collectors.toMap(DictDataVo::getDictCode,
                DictDataVo::getDictValue));
        if (!CollectionUtils.isEmpty(caseVoList)) {
            caseVoList.forEach(e -> {
                // 将承担类型编码转化成名称
                e.setBearType(bearTypeMap.getOrDefault(e.getBearType(), e.getBearType()));
                e.setEstimatedCost(e.getApplyAmount());
                if (!CollectionUtils.isEmpty(e.getBearDepartmentList())) {
                    e.setBearDepartmentName(e.getBearDepartmentList().stream().map(m -> m.getDepartmentName()).collect(Collectors.joining(",")));
                }
            });
        }
        List<OverallPlanDetailDto> detailDtoList = (List<OverallPlanDetailDto>) nebulaToolkitService.copyCollectionByBlankList(caseVoList, OverallPlanCaseVo.class, OverallPlanDetailDto.class, LinkedHashSet.class, ArrayList.class);
        OaDetailDto oaDetailDto = new OaDetailDto();
        oaDetailDto.setDetailList(JSONUtil.toJsonStr(detailDtoList));
        oaDetailDto.setDetailClass(OverallPlanDetailDto.class);
        dto.setDetailList(Arrays.asList(oaDetailDto));

        if (ryOaProcessService.resubmit(dto)) {
            OverallPlan entity = overPlanRepository.findById(plan.getId());
            entity.setProcessDate(new Date());
            entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
            overPlanRepository.updateById(entity);
        }

        return null;
    }

    /**
     * OA撤回
     *
     * @param plan
     * @return
     */
    @Override
    public boolean oaWithdraw(OverallPlan plan, String remark) {
        Validate.isTrue(plan.getProcessStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(plan.getProcessNumber()));
        dto.setProcessCreateId(plan.getOaId());
        dto.setUserName(plan.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (OverallPlanFieldsEnum value : OverallPlanFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod = (tableDetailFields, detailJsonObject) -> {
        for (OverallPlanCaseFieldsEnum value : OverallPlanCaseFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getKey()) ? "" : detailJsonObject.get(value.getKey()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
}
