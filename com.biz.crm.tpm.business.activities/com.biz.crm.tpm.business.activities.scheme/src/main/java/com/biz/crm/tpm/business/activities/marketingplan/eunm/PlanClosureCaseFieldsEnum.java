package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum PlanClosureCaseFieldsEnum {
    schemeDetailCode("schemeDetailCode"),
    actName("actName"),
    detailName("detailName"),
    startDate("startDate"),
    endDate("endDate"),
    belongDepartmentName("belongDepartmentName"),
    customerName("customerName"),
    costCenterName("costCenterName"),
    applyAmount("applyAmount"),
    ;

    private String dictCode;

    public static PlanClosureCaseFieldsEnum findByCode(String code) {
        Optional<PlanClosureCaseFieldsEnum> first = Stream.of(PlanClosureCaseFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}