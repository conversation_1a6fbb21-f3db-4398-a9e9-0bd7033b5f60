package com.biz.crm.tpm.business.activities.scheme.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeDetailRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 方案活动关联信息(TpmProjectActivityDetailRelation)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-11-17 10:52:34
 */
@Mapper
public interface ActivitiesSchemeDetailRelationMapper extends BaseMapper<ActivitiesSchemeDetailRelation> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页对象
   * @param activitiesSchemeDetailRelation 查询实体
   * @return 所有数据
  */
  public Page<ActivitiesSchemeDetailRelation> findByConditions(@Param("page") Page<ActivitiesSchemeDetailRelation> page, @Param("activitiesSchemeDetailRelation") ActivitiesSchemeDetailRelation activitiesSchemeDetailRelation);
}

