package com.biz.crm.tpm.business.activities.marketingplan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/31 19:57
 **/
@Data
public class MarketingPlanSecondCategoryDto {

    @ApiModelProperty("二级费用大类")
    private String secondCategory;

    @ApiModelProperty("活动编码")
    private String schemeCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("活动明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("活动状态")
    private String actStatus;

    @ApiModelProperty("年月")
    private String years;

    private List<String> yearsList;

    @ApiModelProperty("审批状态")
    private String processStatus;

    private List<String> categoryCodeList;

    private String dmsReadFlag;
}
