package com.biz.crm.tpm.business.activities.scheme.dto;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 参数传递dto：方案;
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "Scheme",description = "方案")
@Getter
@Setter
public class SchemeDto implements Serializable,Cloneable{
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 方案编号 */
  @ApiModelProperty(name = "schemeCode",notes = "方案编号", value = "方案编号")
  private String schemeCode;
  /** 方案名称 */
  @ApiModelProperty(name = "schemeName",notes = "方案名称", value = "方案名称")
  private String schemeName;
  /** 方案类型 */
  @ApiModelProperty(name = "schemeType",notes = "方案类型", value = "方案类型")
  private String schemeType;
  /** 方案开始时间 */
  @ApiModelProperty(name = "schemeBeginTime",notes = "方案开始时间", value = "方案开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date schemeBeginTime;
  /** 方案结束时间 */
  @ApiModelProperty(name = "schemeEndTime",notes = "方案结束时间", value = "方案结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date schemeEndTime;
  /** 方案状态 */
  @ApiModelProperty(name = "schemeStatus",notes = "方案状态（01:待执行, 02:执行中,:03:已结束）", value = "方案状态（01:待执行, 02:执行中,:03:已结束）")
  private String schemeStatus;
  /** 费用预算编号 */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编号", value = "费用预算编号")
  private String costBudgetCode;
  @ApiModelProperty("回显编码，字符串，一般用于单选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这条数据")
  private String selectedCode;
  @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCodeList;
  /** 当前用户管理组织机构编码 */
  @ApiModelProperty("当前用户管理组织机构编码")
  private List<String> orgCodes;
  /** 当前用户管理组织层级编码 */
  @ApiModelProperty("当前用户管理组织层级编码")
  private List<String> orgTypes;
  /** 下拉选择查询使用 */
  @ApiModelProperty("下拉选择查询使用")
  private boolean isSelect;
  /** 方案状态集合 */
  @ApiModelProperty(name = "schemeStatuses",notes = "方案状态（01:待执行, 02:执行中,:03:已结束）", value = "方案状态（01:待执行, 02:执行中,:03:已结束）")
  private Set<String> schemeStatuses;
}