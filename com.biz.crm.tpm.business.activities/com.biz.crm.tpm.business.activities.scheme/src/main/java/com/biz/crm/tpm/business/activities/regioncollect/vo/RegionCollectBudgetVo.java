package com.biz.crm.tpm.business.activities.regioncollect.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/30 16:14
 */
@Data
@ApiModel(value = "RegionCollectBudget", description = "大区汇总预算追踪")
public class RegionCollectBudgetVo extends UuidFlagOpVo {

    @ApiModelProperty("汇总编码")
    private String collectCode;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("部门编码")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("科目编码")
    private String subjectCode;

    @ApiModelProperty("科目名称")
    private String subjectName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("余额")
    private BigDecimal surplusAmount;

    @ApiModelProperty("使用金额")
    private BigDecimal usedAmount;

    @ApiModelProperty("管控编码")
    private String controlCode;

    @ApiModelProperty("管控形式")
    private String controlForm;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("预算跟踪编码")
    private String trackCode;
}
