package com.biz.crm.tpm.business.activities.regioncollect.service.strategy;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/24 19:34
 */
public abstract class ItemEstimationBuilder<SchemeCodes, Years> {

    protected SchemeCodes schemeCode;

    protected Years years;

    public ItemEstimationBuilder<SchemeCodes, Years> init(SchemeCodes schemeCode, Years years) {
        this.schemeCode = schemeCode;
        this.years = years;
        return this;
    }

    /**
     * 加载初始值
     *
     * @return
     */
    public abstract ItemEstimationBuilder loadInitData();

    /**
     * 利润率
     *
     * @return
     */
    public abstract ItemEstimationBuilder calProfitRatio();

    /**
     * 毛利率
     *
     * @return
     */
    public abstract ItemEstimationBuilder calGrossProfitRatio();

    /**
     * 物流费率
     *
     * @return
     */
    public abstract ItemEstimationBuilder calLogisticsRatio();

    /**
     * 营销费率
     *
     * @return
     */
    public abstract ItemEstimationBuilder calMarketingRatio();

    /**
     * 公摊费率
     *
     * @return
     */
    public abstract ItemEstimationBuilder calPublicShareRatio();


    /**
     * 二级费用大类
     *
     * @return
     */
    public abstract ItemEstimationBuilder calSecondCategory();
}
