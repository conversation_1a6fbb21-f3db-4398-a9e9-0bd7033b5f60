package com.biz.crm.tpm.business.activities.materialdemand.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:13
 */
@Data
@ApiModel(value = "MaterialDemandDetail", description = "物料需求提报明细")
public class MaterialDemandCollectDetailVo extends UuidFlagOpVo {

    @ApiModelProperty("编码")
    private String collectCode;

    @ApiModelProperty("物料提报编码")
    private String demandCode;

    @ApiModelProperty("明细编码")
    private String demandDetailCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料数量")
    private BigDecimal materialNum;

    @ApiModelProperty("物料成本价")
    private BigDecimal materialPrice;

    @ApiModelProperty("物料价格")
    private BigDecimal materialAmount;

    @ApiModelProperty("是否选中状态")
    private String checked;

}
