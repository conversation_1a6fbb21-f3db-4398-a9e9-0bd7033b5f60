package com.biz.crm.tpm.business.activities.marketingplan.service.strategy.impl;

import com.biz.crm.business.common.base.util.BusinessRequiredUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCaseStrategy;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 直降模板
 * <AUTHOR>
 * @Date 2024/6/20 20:09
 */
@Component
public class DirectDescentStrategyImpl implements MarketingPlanCaseStrategy<MarketingPlanCaseVo> {

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;

    @Resource
    private OrgVoService orgVoService;

    @Override
    public void checkCaseList(List<MarketingPlanCaseVo> list, String years, String schemeCode, String originalSchemeCode, String cacheKey) {
        //获取配置模板信息
        List<ActivitiesTemplateConfigDetailVo> templateFiledList = checkHelper.getTemplateFiled(MarketingPlanCaseTypeEnum.direct_descent.getCode());
        Map<String, Boolean> filedMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getRequired));
        Map<String, String> chineseMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getTitle));
        //获取活动细类配置
        Set<String> detailCodeSet = checkHelper.getTemplateRelateDetailCodes(MarketingPlanCaseTypeEnum.direct_descent.getCode());
        //标记分组
        Map<String, Long> muchDepartmentMap = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMuchDepartmentMark()))
                .collect(Collectors.groupingBy(MarketingPlanCaseVo::getMuchDepartmentMark, Collectors.counting()));
        if (ObjectUtils.isNotEmpty(muchDepartmentMap)) {
            muchDepartmentMap = muchDepartmentMap.entrySet().stream()
                    .filter(entry -> entry.getValue() == 1)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        Set<String> orgCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBelongDepartmentCode()))
                .map(MarketingPlanCaseVo::getBelongDepartmentCode).collect(Collectors.toSet());
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(Lists.newArrayList(orgCodes));
        Map<String, Set<String>> orgCostCenterMap = orgVoList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getCostCenterCodeSet()))
                .collect(Collectors.toMap(x -> x.getOrgCode(), OrgVo::getCostCenterCodeSet));
        Set<String> costCenterCodeSet = orgCostCenterMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        Set<String> yearSet = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getYears()))
                .map(MarketingPlanCaseVo::getYears).collect(Collectors.toSet());
        List<String> customerCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()))
                .map(MarketingPlanCaseVo::getCustomerCode).collect(Collectors.toList());
        List<MarketingSalesPlanVo> salesPlanVos = marketingSalesPlanService.findListByConditionToMarketingScheme(yearSet, costCenterCodeSet, Sets.newHashSet(customerCodes),
                null, null, null, null,null);
        Map<String, List<MarketingSalesPlanVo>> salesPlanMap = salesPlanVos.stream()
                .filter(k-> StringUtil.isNotEmpty(k.getCostCenterCode()))
                .collect(Collectors.groupingBy(MarketingSalesPlanVo::getCostCenterCode));

        //查询客户
        List<CustomerVo> customerVoList = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerVoMap = customerVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
        Set<String> companyCodeSet = Sets.newHashSet();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(customerVoList)) {
            companyCodeSet = customerVoList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getCompanyCode()))
                    .map(CustomerVo::getCompanyCode).collect(Collectors.toSet());
        }
        List<String> productCodes = Lists.newArrayList();
        Set<String> levels = Sets.newHashSet();
        for (MarketingPlanCaseVo vo : list) {
            if (CollectionUtils.isNotEmpty(vo.getProductList())) {
                productCodes.addAll(vo.getProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(vo.getLevelList())) {
                levels.addAll(vo.getLevelList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toSet()));
            }
        }
        //查询品项
        List<String> items = list.stream().filter(x -> CollectionUtils.isNotEmpty(x.getItemList()))
                .map(x -> x.getItemList()).flatMap(List::stream).filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                .map(x -> x.getCode()).collect(Collectors.toList());
        Map<String, String> itemMap = checkHelper.findSapLevelMap(items);
        //查询商品
        Map<String, ProductVo> productMap = checkHelper.findProductListByCodes(productCodes, companyCodeSet, items);
        //查询小类
//        Map<String, String> levelMap = checkHelper.findSmallLevel(levels);
        for (MarketingPlanCaseVo caseVo : list) {
            caseVo.setCheckFlag(Boolean.TRUE);
            StringJoiner errMsg = new StringJoiner(";");
            if (ObjectUtils.isNotEmpty(caseVo.getErrMsg())) {
                errMsg.add(caseVo.getErrMsg());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getDetailCode())) {
                if (!detailCodeSet.contains(caseVo.getDetailCode())) {
                    errMsg.add("费用项目不属于当前表单模板配置");
                }
            }
            if (checkHelper.paramNotNull(caseVo.getCustomerCode(), "客户编码", errMsg)) {
                if (customerVoMap.containsKey(caseVo.getCustomerCode())) {
                    CustomerVo customerVo = customerVoMap.get(caseVo.getCustomerCode());
                    caseVo.setCustomerName(customerVo.getCustomerName());
                    caseVo.setErpCode(customerVo.getErpCode());
                    caseVo.setCompanyCode(customerVo.getCompanyCode());
                    caseVo.setProductGroupCode(customerVo.getProductGroupCode());
                    caseVo.setChannelCode(customerVo.getChannelCode());
                } else {
                    errMsg.add(String.format("客户编码%s在主数据中未查询到", caseVo.getCustomerCode()));
                }
            }
            if (checkHelper.collectionNotNull("品项", errMsg, caseVo.getItemList())) {
                for (MarketingPlanProductVo vo : caseVo.getItemList()) {
                    if (itemMap.containsKey(vo.getCode())) {
                        vo.setName(itemMap.get(vo.getCode()));
                    } else {
                        errMsg.add(String.format("品项%s在主数据中未查询到", vo.getCode()));
                    }
                }
            }
            List<String> codeList = Lists.newArrayList();
            if (checkHelper.collectionNotNull("直降本品", errMsg, caseVo.getProductList())) {
                if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
                    for (MarketingPlanProductVo vo : caseVo.getProductList()) {
                        if (productMap.containsKey(vo.getCode())) {
                            ProductVo productVo = productMap.get(vo.getCode());
                            vo.setName(productVo.getProductName());
                            Validate.notNull(productVo.getMaterialCode(), String.format("商品%s未存在绑定的物料", productVo.getProductName()));
                            vo.setMaterialCode(productVo.getMaterialCode());
                            codeList.add(productVo.getProductCode());
                        } else {
                            errMsg.add(String.format("直降商品%s在主数据中未查询到", vo.getCode()));
                        }
                    }
                }
            }
            checkHelper.paramNotNull(caseVo.getConditionNum(), "直降本品数量", errMsg);
            checkHelper.paramNotNull(caseVo.getGiveNum(), "整单减少金额", errMsg);
            //获取预估销量
            caseVo.setEstimatedSalesVolume(BigDecimal.ZERO);
            BigDecimal salesVolume = null;
            String belongDepartmentCode = ObjectUtils.isNotEmpty(caseVo.getBelongDepartmentCode()) ? caseVo.getBelongDepartmentCode() : "";
            Set<String> costCenterCodes = orgCostCenterMap.getOrDefault(belongDepartmentCode, Sets.newHashSet());
            List<MarketingSalesPlanVo> salesPlanVoList = salesPlanMap.entrySet().stream().filter(x -> costCenterCodes.contains(x.getKey()))
                    .flatMap(x -> x.getValue().stream())
                    .filter(k-> StringUtil.isNotEmpty(k.getCustomerCode()))
                    .filter(k-> StringUtil.isNotEmpty(k.getYears()))
                    .filter(x -> x.getCustomerCode().equals(caseVo.getCustomerCode())
                            && x.getYears().equals(caseVo.getYears()) && codeList.contains(x.getProductCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(salesPlanVoList)) {
                salesVolume = salesPlanVoList.stream().map(x -> Optional.ofNullable(x.getEstimatedSalesVolume()).orElse(BigDecimal.ZERO))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (salesVolume.compareTo(BigDecimal.ZERO) == 1) {
                    caseVo.setEstimatedSalesVolume(salesVolume);
                }
            }
            checkHelper.paramNotNull(caseVo.getEstimatedSalesVolume(), "预估销量", errMsg);
            caseVo.setApplyAmount(BigDecimal.ZERO);
            //计算申请金额
            try {
                if (ObjectUtils.isNotEmpty(caseVo.getConditionNum()) && ObjectUtils.isNotEmpty(caseVo.getGiveNum())) {
                    BigDecimal estimatedSalesVolume = ObjectUtils.defaultIfNull(caseVo.getEstimatedSalesVolume(), BigDecimal.ZERO);
                    List<BigDecimal> conditionNumList = Lists.newArrayList();
                    for (String s : caseVo.getConditionNum().split(",")) {
                        conditionNumList.add(new BigDecimal(s));
                    }
                    BigDecimal conditionNum = conditionNumList.stream().max(Comparator.naturalOrder()).get();
                    BigDecimal quantity = BigDecimal.ZERO;
                    if (Objects.nonNull(conditionNum)
                            && conditionNum.compareTo(BigDecimal.ZERO) != 0) {
                        quantity = estimatedSalesVolume.divide(conditionNum, 0, RoundingMode.DOWN);
                    }
                    List<BigDecimal> giveNumList = Lists.newArrayList();
                    for (String s : caseVo.getGiveNum().split(",")) {
                        giveNumList.add(new BigDecimal(s));
                    }
                    BigDecimal giveNum = giveNumList.stream().max(Comparator.naturalOrder()).get();
                    BigDecimal applyAmount = quantity.multiply(giveNum);
                    caseVo.setApplyAmount(applyAmount);
                    if (Objects.nonNull(caseVo.getApplyAmount()) && caseVo.getApplyAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        errMsg.add("申请金额必须大于0");
                    }
                }
            } catch (NumberFormatException e) {
                errMsg.add("本品直降数量或整单减少金额不是数字");
            } catch (Exception e) {
                errMsg.add("计算费用合计失败");
            }
            caseVo.setMuchDepartmentFlag(BooleanEnum.FALSE.getCapital());
            //多部门标记
            if (ObjectUtils.isNotEmpty(caseVo.getMuchDepartmentMark())) {
                caseVo.setMuchDepartmentFlag(BooleanEnum.TRUE.getCapital());
            }
            if (ObjectUtils.isNotEmpty(muchDepartmentMap) && muchDepartmentMap.containsKey(caseVo.getMuchDepartmentMark())) {
                errMsg.add("多部门标记只找到一个,不可进行多部门标记");
            }

            //此处单独调用
            String msg = BusinessRequiredUtil.filedRequiredCheck(filedMap, chineseMap, caseVo);
            if (ObjectUtils.isNotEmpty(msg)) {
                errMsg.add(msg);
            }

            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                caseVo.setCheckFlag(Boolean.FALSE);
                caseVo.setErrMsg(errMsg.toString());
            }
            if (caseVo.getCheckFlag()) {
                caseVo.setRatio(BigDecimal.ZERO);
                if (ObjectUtils.isNotEmpty(caseVo.getEstimatedSalesVolume()) && caseVo.getEstimatedSalesVolume().compareTo(BigDecimal.ZERO) > 0) {
                    caseVo.setRatio(caseVo.getApplyAmount().divide(caseVo.getEstimatedSalesVolume(), 2, BigDecimal.ROUND_HALF_DOWN));
                }
            }
            //寻年度预算
            checkHelper.matchBudget(filedMap, chineseMap, caseVo);
        }

        list.forEach(x -> x.setCaseType(getCaseType()));
        //校验多部门
        checkHelper.checkMuchDepartmentMark(list);

    }

    @Override
    public String getCaseType() {
        return MarketingPlanCaseTypeEnum.direct_descent.getCode();
    }
}
