package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLossesBack;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectGainsAndLossesBackRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectGainsAndLossesBackService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectGainsAndLossesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Service
@Transactional
@Slf4j
public class RegionCollectGainsAndLossesBackServiceImpl implements RegionCollectGainsAndLossesBackService {

    @Resource
    private RegionCollectGainsAndLossesBackRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;


    @Override
    public void deleteByCollectCodes(List<String> collectCodes) {
        repository.deleteByCollectCodes(collectCodes);
    }

    @Override
    public void saveList(List<RegionCollectGainsAndLossesVo> list, String collectCode) {
        repository.deleteByCollectCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<RegionCollectGainsAndLossesBack> dataList = list.stream().map(x -> {
            RegionCollectGainsAndLossesBack data = nebulaToolkitService.copyObjectByWhiteList(x, RegionCollectGainsAndLossesBack.class, HashSet.class, ArrayList.class);
            if (!CollectionUtils.isEmpty(data.getSecondCostCategoryMap())) {
                data.setSecondCostCategoryJsonStr(JSONObject.toJSONString(data.getSecondCostCategoryMap()));
            }
            data.setId(null);
            data.setCollectCode(collectCode);
            return data;
        }).collect(Collectors.toList());
        repository.saveBatch(dataList);
    }
}
