package com.biz.crm.tpm.business.activities.regioncollect.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.activities.regioncollect.constant.RegionCollectConstant;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollect;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectStatusEnum;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectCal;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Slf4j
@RestController
@RequestMapping("/v1/regionCollectController")
@Api(tags = "大区汇总")
public class RegionCollectController {

    @Autowired
    private RegionCollectService regionCollectService;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private RegionCollectCal regionCollectCal;

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private RegionCollectRepository collectRepository;


    @GetMapping("findList")
    @ApiOperation(value = "分页查询")
    public Result<Page<RegionCollectVo>> findList(@PageableDefault(50) Pageable pageable, RegionCollectVo vo) {
        return Result.ok(regionCollectService.findList(pageable, vo));
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("queryByIdOrCollectCode")
    public Result<RegionCollectVo> queryByIdOrCollectCode(@RequestParam(value = "id", required = false) String id,
                                                          @RequestParam(value = "collectCode", required = false) String collectCode) {
        return Result.ok(regionCollectService.queryByIdOrCollectCode(id, collectCode));
    }

    @ApiOperation(value = "更新大区汇总数据")
    @PostMapping("modifyRegionCollect")
    public Result modifyRegionCollect(@RequestParam String orgCode, @RequestParam String years) {
        RegionCollect collect = collectRepository.findByOrgCodeAndYears(orgCode, years);
        if (ObjectUtils.isNotEmpty(collect) &&
                StringUtils.equalsAny(collect.getProcessStatus(), ProcessStatusEnum.COMMIT.getDictCode(),ProcessStatusEnum.PASS.getDictCode())){
            Validate.isTrue(Boolean.FALSE,"大区汇总数据已提交/审批通过,不可重复汇总");
        }
        String lockKey = RegionCollectConstant.getRedisLockKey(orgCode, years);
        Boolean locked = redisLockService.isLock(lockKey);
        if (locked) {
            return Result.error(String.format("区域%s加年月%s正在执行中,请勿重复操作!", orgCode, years));
        }
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        //创建一个暂存的数据
        String id = regionCollectService.createRegionCollect(orgCode, years);
        String status = RegionCollectStatusEnum.execute_cal.getCode();
        String result = "大区汇总正在执行计算,执行人" + loginDetails.getUsername();
        //更新状态
        regionCollectService.updateExecuteMsg(id, result, status);
        //执行计算
        regionCollectCal.modifyRegionCollect(id, lockKey, loginDetails.getUsername());
        return Result.ok();
    }

    @GetMapping("modifyOTwoOCollect")
    public Result modifyOTwoOCollect(@RequestParam String id){
        regionCollectService.modifyOTwoOCollect(id);
        return Result.ok();
    }

    @DeleteMapping("deleteBatch")
    @ApiOperation(value = "批量删除")
    public Result deleteBatch(@RequestParam List<String> ids) {
        String collectCodes = regionCollectService.deleteBatch(ids);
        if (ObjectUtils.isNotEmpty(collectCodes)) {
            return Result.error(String.format("汇总方案编码%s是审批中/审批通过状态不允许删除", collectCodes));
        }
        return Result.ok();
    }


//    @ApiOperation(value = "批量提交")
//    @PostMapping("submitBatchId")
//    public Result submitBatchId(@RequestBody List<String> collectCodes) {
//        StringJoiner errMsg = new StringJoiner(";");
//        Boolean locked = redisLockService.batchLock(RegionCollectConstant.REGION_COLLECT_LOCK, collectCodes, TimeUnit.DAYS, 1);
//        try {
//            if (locked) {
//                for (String s : collectCodes) {
//                    try {
//                        regionCollectService.submitByCollectCode(s);
//                    } catch (Exception e) {
//                        log.error(e.getMessage(), e);
//                        errMsg.add(String.format("方案%s提交失败,失败原因:%s", s, e.getMessage()));
//                    }
//                }
//            }
//        } finally {
//            if (locked) {
//                redisLockService.batchUnLock(RegionCollectConstant.REGION_COLLECT_LOCK, collectCodes);
//            }
//        }
//        if (ObjectUtils.isNotEmpty(errMsg.toString())) {
//            return Result.error(errMsg.toString());
//        }
//        return Result.ok();
//    }


    @ApiOperation(value = "校验管控预算")
    @GetMapping("checkControlBudget")
    public Result<?> checkControlBudget(@RequestParam String controlFlag, @RequestParam String collectCode) {
        String lockKey = RegionCollectConstant.getSubmitRedisLockKey(collectCode);
        boolean locked = redisLockService.tryLock(lockKey, TimeUnit.DAYS, 1);
        try {
            Assert.isTrue(locked, String.format("大区汇总[%s]正在提交,请稍后重试!", collectCode));
            Map<String, String> map = regionCollectService.checkControlBudget(controlFlag, collectCode);
            return Result.ok(map);
        } finally {
            if (locked) {
                redisLockService.unlock(lockKey);
            }
        }
    }


    @ApiOperation(value = "提交")
    @PostMapping("submitByCollectCode")
    public Result submitByCollectCode(@RequestBody JSONObject jsonObject) {
        Validate.isTrue(jsonObject.containsKey("collectCode"), "汇总编码必传");
        String collectCode = jsonObject.getString("collectCode");
        String submitFlag = jsonObject.containsKey("submitFlag") ? jsonObject.getString("submitFlag") : BooleanEnum.FALSE.getCapital();
        String remark = jsonObject.containsKey("remark") ? jsonObject.getString("remark") : null;
        String lockKey = RegionCollectConstant.getSubmitRedisLockKey(collectCode);
        Boolean locked = redisLockService.tryLock(lockKey, TimeUnit.DAYS, 1);
        Map<String, String> map = null;
        try {
            Validate.isTrue(locked, String.format("大区汇总[%s]正在提交,请稍后重试!", collectCode));
            map = regionCollectService.submitByCollectCode(collectCode, submitFlag,remark);
        } finally {
            if (locked) {
                redisLockService.unlock(lockKey);
            }
        }
        return Result.ok(map);
    }


    @ApiOperation(value = "手动审批")
    @PostMapping("manualApproval")
    public Result manualApproval(@RequestBody List<String> collectCodes) {
        StringJoiner errMsg = new StringJoiner(";");
        Boolean locked = redisLockService.batchLock(RegionCollectConstant.REGION_COLLECT_LOCK, collectCodes, TimeUnit.DAYS, 1);
        try {
            if (locked) {
                for (String s : collectCodes) {
                    try {
                        regionCollectService.manualApproval(s);
                    } catch (Exception e) {
                        errMsg.add(String.format("方案%s手动审批失败,失败原因:%s", s, e.getMessage()));
                    }
                }
            }
        } finally {
            redisLockService.batchUnLock(RegionCollectConstant.REGION_COLLECT_LOCK, collectCodes);
        }
        if (ObjectUtils.isNotEmpty(errMsg.toString())) {
            return Result.error(errMsg.toString());
        }
        return Result.ok();
    }

    /**
     * 流程撤回
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "流程撤回")
    @GetMapping("recover")
    public Result<?> recover(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                             @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
        try {
            this.regionCollectService.recover(code, remark);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "回调")
    @PostMapping("callback")
    public Result callback(@RequestBody RegionCollectVo vo){
        regionCollectService.callback(vo);
        return Result.ok();
    }
}
