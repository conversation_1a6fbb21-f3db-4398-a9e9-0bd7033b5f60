package com.biz.crm.tpm.business.activities.overallplan.service.internal;

import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanBudget;
import com.biz.crm.tpm.business.activities.overallplan.repository.OverPlanBudgetRepository;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanBudgetService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/8 16:18
 */
@Service
public class OverallPlanBudgetServiceImpl implements OverallPlanBudgetService {

    @Resource
    private OverPlanBudgetRepository overPlanBudgetRepository;

    @Override
    public List<OverallPlanBudget> findOverallBudgetByCondition(List<String> orgCodes, List<String> budgetSubjectCodes, String years, List<String> customerCodes,
                                                                List<String> itemCodes, List<String> productCodes) {
        List<OverallPlanBudget> budgets = overPlanBudgetRepository.findOverallBudgetByCondition(orgCodes, budgetSubjectCodes, years, customerCodes, itemCodes, productCodes);
        if (CollectionUtils.isEmpty(budgets)) {
            return Lists.newArrayList();
        }
        return budgets;
    }
}
