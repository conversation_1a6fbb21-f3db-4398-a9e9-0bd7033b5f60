package com.biz.crm.tpm.business.activities.materialdemand.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandReportVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
public interface MaterialDemandCollectDetailService {

    List<MaterialDemandCollectDetailVo> findListByCodes(List<String> codes);

    void saveBatchList(List<MaterialDemandCollectDetailVo> collectDetailVos,String collectCode);

    void deleteByCollectCodeList(List<String> collectCodes);

    List<MaterialDemandCollectDetailVo> findListById(String id);
}
