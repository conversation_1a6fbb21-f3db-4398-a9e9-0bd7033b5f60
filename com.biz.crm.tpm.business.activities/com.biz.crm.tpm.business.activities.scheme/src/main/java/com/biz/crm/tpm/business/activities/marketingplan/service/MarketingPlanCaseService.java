package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.dto.MarketingPlanSecondCategoryDto;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.TerminalExpenseVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/4 09:53
 */
public interface MarketingPlanCaseService {

    List<MarketingPlanCase> findListBySchemeCodes(List<String> schemeCodes);

    List<MarketingPlanCase> findListBySchemeCodesAndDelFlag(List<String> schemeCodes, String delFlag);

    List<MarketingPlanCase> findListBySchemeDetailCodes(List<String> schemeDetailCodes);

    /**
     * 保存方案明细列表
     *
     * @param caseList
     * @param schemeCode
     */
    void saveBatchCaseList(List<MarketingPlanCaseVo> caseList, String schemeCode, Boolean changeFlag);

    void saveBatchChangeCaseList(List<MarketingPlanCaseVo> cases, String schemeCode);

    void deleteBySchemeCodes(List<String> schemeCodes);

    void deleteBySchemeDetailCodes(List<String> schemeDetailCodes);

    List<MarketingPlanCase> findAlreadyBearCase(List<String> releaseDetailCodes);

    Page<MarketingPlanCaseVo> findCaseList(Pageable pageable, MarketingPlanCaseVo vo, String processStatus);

    Page<MarketingPlanCaseVo> findCaseListByChangeScheme(Pageable pageable, MarketingPlanCaseVo vo, String processStatus, List<String> originalSchemeDetailCodes);

    Page<MarketingPlanCaseVo> findCaseListByWithholdingEndCase(Pageable pageable, MarketingPlanCaseVo vo, String processStatus);

    List<MarketingPlanCaseVo> checkPlanCaseList(List<MarketingPlanCaseVo> list, String years, List<String> loginUserOneLevelOrgCodes, String loginUserName);

    Map<String, List<MarketingPlanCaseVo>> findListBySchemeId(String schemeId,String customerName);

    List<String> findReleaseDetailCodeBySchemeCodeAndProcessStatus(String schemeCode, String schemeId, String processStatus);

    MarketingPlanCaseVo findByCaseCode(String caseCode);

    List<MarketingPlanCaseVo> findByCaseCodes(List<String> caseCodes);

    void pushCaseListToSfa(List<String> schemeDetailCode);

    void rewriteEndCaseOrWithholding(List<MarketingPlanCaseVo> caseVos);

    List<MarketingPlanCaseVo> findMaterialListByMaterialCodesAndOrgCodes(List<String> materialCodes, List<String> orgCodes);

    List<MarketingPlanCaseVo> findMaterialListByMaterialCodesAndOrgCodesAndExclusiveSchemeCode(List<String> materialCodes, List<String> orgCodes, String schemeCode);

    List<MarketingPlanCaseVo> findAuditAndWithholdingAndCashReleaseDetailCodeList(List<String> releaseDetailCodes, String bearFlag);

    Page<MarketingPlanCaseVo> findRegionMarketingPlanCase(Pageable pageable, String releaseDetailCode);

    List<MarketingPlanCaseVo> findRegionMarketingPlanCase(List<String> releaseDetailCode);

    Page<MarketingPlanCaseVo> findMarketingPlanCaseReportList(Pageable pageable, MarketingPlanCaseVo vo);

    Page<MarketingPlanCaseVo> findNotMarketingClosedCaseList(Pageable pageable, MarketingPlanCaseQueryDto vo);

    /**
     * SFA小程序活动政策所需分页接口
     *
     * @param pageable
     * @param vo
     * @return
     */
    Page<MarketingPlanCaseVo> findSafActPolicy(Pageable pageable, MarketingPlanCaseQueryDto vo);

    void updateCaseStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String delFlag);

    List<MarketingPlanCase> findListByFormulaCode(String formulaCode);

    List<MarketingPlanCaseVo> findListByDetailCodes(List<String> detailCodes);

    /**
     * 执行活动分页查询接口
     *
     * @param pageable
     * @param dto
     * @return
     */
    Page<MarketingPlanCaseExecuteVo> findMarketingPlanCaseExecuteList(Pageable pageable, MarketingPlanCaseQueryDto dto);

    /**
     * 执行活动查询接口
     *
     * @param dto
     * @return
     */
    List<MarketingPlanCaseExecuteVo> findByTerminalMarketingPlanCaseExecuteList(MarketingPlanCaseQueryDto dto);

    /**
     * 终端费用投入查询
     *
     * @param vo
     * @return
     */
    List<TerminalExpenseVo> findTerminalExpense(MarketingPlanCaseQueryDto vo);

    /**
     * 终端费用投入查询
     *
     * @param vo
     * @return
     */
    List<TerminalExpenseVo> findCustomerExpense(MarketingPlanCaseQueryDto vo);

    List<MarketingPlanCaseVo> findContractByDepartmentCodesAndYears(List<String> departmentCodes, String years);

    /**
     * 根据合同编码查询是否存在明细
     *
     * @param contractCodes
     * @return
     */
    boolean findExistByContractCodes(List<String> contractCodes);

    Map<String, BigDecimal> findAlreadyUndertakeAmount(List<String> releaseDetailCodes);

    List<MarketingPlanCaseVo> findChanging(List<String> schemeDetailCodes);


    List<MarketingPlanCaseVo> findBySchemeCode(String schemeCode);

    List<MarketingPlanCaseVo> findByCollectCode(String collectCode);

    List<MarketingPlanCaseVo> findBySchemeCodes(List<String> schemeCodes);

    /**
     * 创建或更新终端费用投入记录表
     *
     * @param startDate
     * @param endDate
     */
    void createOrUpdateTerminalCostRecordLog(Date startDate, Date endDate);

    /**
     * SFA移动访销费用投入情况 分页接口
     *
     * @param pageable
     * @param vo
     * @return
     */
    Page<MarketingPlanCaseVo> findExpensesCountByConditions(Pageable pageable, MarketingPlanCaseQueryDto vo);

    void updateWithHoldingStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String withHoldingStatus);

    List<MarketingPlanCase> findListByCustomerCodesAndBelongDepartmentCodes(List<String> customerCodes, List<String> departmentCodes, String schemeCode, String years);

    List<MarketingPlanCaseVo> findMiniCostTotalView(MarketingPlanCaseVo vo);

    Set<String> findCustomerCodeListBySchemeCode(String schemeCode);

    Page<MarketingPlanCaseVo> findPlanCaseListBySecondCategory(Pageable pageable, MarketingPlanSecondCategoryDto dto);

    List<String> findCountTerminalByTerminalCodesAndYears(List<String> terminalCodeList, String years);


    Map<String, BigDecimal> findAmountByOrgCodesAndYearsList(WithholdingIncomeQueryDto dto);


    List<MarketingPlanCaseVo> findCaseListByDmsMiniHomePage(String customerCode);

    MarketingPlanCaseVo dmsQueryCaseDetailsBySchemeDetailCode(String schemeDetailCode);

    Page<MarketingPlanCaseVo> dmsQueryCaseListByCondition(@PageableDefault(50) Pageable pageable, MarketingPlanSecondCategoryDto dto);

    Map<String,Long> dmsQueryCaseNotRead(MarketingPlanSecondCategoryDto dto);

    List<MarketingPlanCaseVo> findCaseListByYears(String years);

    Page<MarketingPlanCaseVo> findContractByYearsAndLogin(@PageableDefault Pageable pageable, MarketingPlanVo vo);

}
