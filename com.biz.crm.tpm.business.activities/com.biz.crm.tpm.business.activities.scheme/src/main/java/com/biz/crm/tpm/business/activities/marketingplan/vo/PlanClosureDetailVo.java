package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:12
 */
@Data
@ApiModel(value = "PlanClosureDetailVo", description = "方案规划关闭明细")
public class PlanClosureDetailVo extends JSONObject {

    @ApiModelProperty("关闭编码")
    private String closeCode;

    @ApiModelProperty("关闭明细编码")
    private String closeDetailCode;

    @ApiModelProperty("来源类型")
    private String sourceType;

    @ApiModelProperty("关联方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("json字符串")
    private String jsonStr;

    @ApiModelProperty("选中")
    private String checked;

    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("json字符串(推OA)")
    private String jsonStrOa;
}
