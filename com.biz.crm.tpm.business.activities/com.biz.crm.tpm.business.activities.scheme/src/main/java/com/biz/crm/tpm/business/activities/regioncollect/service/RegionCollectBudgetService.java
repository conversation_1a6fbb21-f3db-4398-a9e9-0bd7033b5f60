package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/30 18:04
 */
public interface RegionCollectBudgetService {

    void saveBatchList(List<RegionCollectBudgetVo> list,String collectCode);

    List<RegionCollectBudgetVo> findByCollectCode(String collectCode);

    List<RegionCollectBudgetVo> findCollectByCollectCode(String collectCode);

    void deleteByCollectCode(String collectCode);
}
