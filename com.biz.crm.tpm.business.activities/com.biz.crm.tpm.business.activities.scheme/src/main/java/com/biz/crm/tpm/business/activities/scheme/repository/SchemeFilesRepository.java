package com.biz.crm.tpm.business.activities.scheme.repository;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import com.biz.crm.tpm.business.activities.scheme.entity.SchemeFiles;
import com.biz.crm.tpm.business.activities.scheme.mapper.SchemeFilesMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.util.List;

/**
 * 方案附件;(tpm_scheme_files)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Component
public class SchemeFilesRepository extends ServiceImpl<SchemeFilesMapper, SchemeFiles> {
  @Autowired
  private SchemeFilesMapper schemeFilesMapper;

  /**
   * 根据编号与租户编号获取对象
   *
   * @param schemeCode
   * @return
   */
  public List<SchemeFiles> findBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return null;
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(SchemeFiles::getSchemeCode, schemeCode)
            .eq(SchemeFiles::getTenantCode, tenantCode).list();
  }

  /**
   * 根据方案编号删除附件数据
   *
   * @param schemeCode
   */
  public void deleteBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    this.lambdaUpdate().eq(SchemeFiles::getSchemeCode, schemeCode).eq(SchemeFiles::getTenantCode, tenantCode).remove();
  }
}