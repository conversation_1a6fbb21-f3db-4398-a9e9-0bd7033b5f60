package com.biz.crm.tpm.business.activities.scheme.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesScheme;
import com.biz.crm.tpm.business.activities.scheme.dto.ActivitiesSchemeDto;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeVo;

/**
 * 方案活动;(tpm_activities_scheme)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-6-1
 */
@Mapper
public interface ActivitiesSchemeMapper extends BaseMapper<ActivitiesScheme>{
  /**
   * 分页查询所有数据
   *
   * @param page 分页参数
   * @param dto 动态查询条件
   * @return 分页对象列表
   */
  Page<ActivitiesSchemeVo> findByConditions(@Param("page") Page<ActivitiesSchemeVo> page , @Param("dto") ActivitiesSchemeDto dto);
}