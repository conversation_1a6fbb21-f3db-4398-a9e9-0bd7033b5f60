package com.biz.crm.tpm.business.activities.contract.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.contract.dto.ContractCostPageDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDto;
import com.biz.crm.tpm.business.activities.contract.vo.ContractCostDetailVo;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 16:15
 */
public interface ExternalContractService {

    Page<ExternalContractVo> findList(Pageable pageable, ExternalContractDto dto);

    ExternalContractVo queryDetails(String id,String contractCode,String contractStatus);

    void createContract(ExternalContractDto dto,String externalFlag);

    void stopContract(String id);

    void uploadContractFile(String contractCode, String fileCode);

    Page<ContractCostPageDto> findList(Pageable pageable, ContractCostPageDto vo);

    Page<ContractCostPageDto> findExternalContractList(Pageable pageable, ContractCostPageDto vo);

    List<ContractCostDetailVo> findUnExecutedAndNotExternalListByContractCodes(String years);

    void generateContractMarketing(String years);

    /**
     * 批量删除合同费用台账
     * @param ids
     */
    void delete(List<String> ids);

    /**
     * 启用
     *
     * @param idList
     */
    void enable(List<String> idList);

    /**
     * 禁用
     *
     * @param idList
     */
    void disable(List<String> idList);

    /**
     * 根据条件分页查询
     * @param pageable
     * @param vo
     * @return
     */
    Page<ExternalContractVo> findByConditions(Pageable pageable, ContractCostPageDto vo);
}
