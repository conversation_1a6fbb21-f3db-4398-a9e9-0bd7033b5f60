<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanCaseMapper">


    <select id="findHeadOverallPlanCaseList"
            resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        select a.*,c.scheme_name,c.scheme_code,c.scheme_theme,a.apply_amount undertakeAmount from tpm_overall_plan_case a
        left join tpm_overall_plan c on a.scheme_code = c.scheme_code
        <where>
            c.del_flag = '${@<EMAIL>()}'
            and c.process_status = '3'
            and a.del_flag = '${@<EMAIL>()}'
            and (a.bear_flag = 'N' or a.bear_flag is null)
            <if test="vo.orgCodeSet != null and vo.orgCodeSet.size()>0">
                and exists (
                SELECT 1 from tpm_overall_plan_department d where d.scheme_detail_code = a.scheme_detail_code
                and d.scheme_detail_code is not null
                and d.department_code in
                <foreach collection="vo.orgCodeSet" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="vo.schemeCode + '%'"/>
                and a.scheme_code like #{schemeCode}
            </if>
            <if test="vo.schemeType != null and vo.schemeType != ''">
                and c.scheme_type = #{vo.schemeType}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="vo.schemeDetailCode + '%'"/>
                and a.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and c.scheme_name like #{schemeName}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and a.years = #{vo.years}
            </if>
            order by create_time desc
        </where>
    </select>


    <select id="findHeadOverallPlanCaseToMarketing" resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        select a.*,c.scheme_name,c.scheme_code,c.scheme_theme,a.apply_amount undertakeAmount from tpm_overall_plan_case a
        left join tpm_overall_plan c on a.scheme_code = c.scheme_code
        <where>
            c.del_flag = '${@<EMAIL>()}'
            and c.process_status = '3'
            and (a.bear_flag = 'Y')
            <if test="vo.orgCodeSet != null and vo.orgCodeSet.size()>0">
                and exists (
                SELECT 1 from tpm_overall_plan_department d where d.scheme_detail_code = a.scheme_detail_code
                and d.scheme_detail_code is not null
                and d.department_code in
                <foreach collection="vo.orgCodeSet" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="vo.schemeCode + '%'"/>
                and a.scheme_code like #{schemeCode}
            </if>
            <if test="vo.schemeType != null and vo.schemeType != ''">
                and c.scheme_type = #{vo.schemeType}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="vo.schemeDetailCode + '%'"/>
                and a.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and c.scheme_name like #{schemeName}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and a.years = #{vo.years}
            </if>
            order by create_time desc
        </where>
    </select>

    <select id="findCaseListBySchemeDetailCodes"
            resultType="com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase">
        select a.*,b.scheme_name from tpm_overall_plan_case a
        left join tpm_overall_plan b on a.scheme_code = b.scheme_code
        <where>
            b.del_flag = '${@<EMAIL>()}'
            and b.tenant_code = #{tenantCode}
            <if test="processStatus != null and processStatus != ''">
                and b.process_status = #{processStatus}
            </if>
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                and scheme_detail_code in
                <foreach collection="schemeDetailCodes" open="(" close=")" index="index" item="item"
                         separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findReleaseHeadCaseList"
            resultType="com.biz.crm.tpm.business.activities.overallplan.vo.BearOverallPlanCaseVo">
        select
        b.scheme_name,
        b.scheme_theme,
        a.*
        from tpm_overall_plan_case a
        left join tpm_overall_plan b on a.scheme_code = b.scheme_code
        <where>
            b.del_flag = '${@<EMAIL>()}'
            and b.process_status ='3'
            and a.bear_flag = 'Y' and b.scheme_type = 'head'
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                and a.scheme_detail_code in
                <foreach collection="schemeDetailCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findReleaseCaseByMarketingPlan"
            resultType="com.biz.crm.tpm.business.activities.overallplan.vo.BearOverallPlanCaseVo">
        select
        b.scheme_name,
        b.scheme_theme,
        a.*
        from tpm_overall_plan_case a
        left join tpm_overall_plan b on a.scheme_code = b.scheme_code
        <where>
            b.del_flag = '${@<EMAIL>()}'
            and b.process_status ='3'
            and a.bear_flag = 'Y'
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                and a.scheme_detail_code in
                <foreach collection="schemeDetailCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findAlreadyBearCase"
            resultType="com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase">
        select a.head_scheme_detail_code,a.apply_amount from tpm_overall_plan_case a
        left join tpm_overall_plan b on a.scheme_code = b.scheme_code
        <where>
            b.scheme_type = 'region'
            <if test="schemeDetailCodes != null and schemeDetailCodes.size()>0">
                and head_scheme_detail_code in
                <foreach collection="schemeDetailCodes" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findListBySchemeType" resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        SELECT
        b.*,a.scheme_code,a.scheme_name
        FROM
        tpm_overall_plan_case b
        LEFT JOIN tpm_overall_plan a ON a.scheme_code = b.scheme_code
        <if test="vo.bearDepartmentStr != null and vo.bearDepartmentStr != ''">
            LEFT JOIN tpm_overall_plan_department c on b.scheme_detail_code=c.scheme_detail_code
        </if>
        <where>
            a.del_flag = '${@<EMAIL>()}'
            AND a.process_status = '3'
            AND a.scheme_type = #{schemeType}
            and a.tenant_code = #{tenantCode}
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="vo.schemeCode + '%'"/>
                and a.scheme_code like #{schemeCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and a.scheme_name like #{schemeName}
            </if>
            <if test="vo.costCenterName != null and vo.costCenterName != ''">
                <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
                and b.cost_center_name like #{costCenterName}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and b.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.bearDepartmentName != null and vo.bearDepartmentName != ''">
                <bind name="bearDepartmentName" value="'%' + vo.bearDepartmentName + '%'"/>
                and b.bear_department_name like #{bearDepartmentName}
            </if>
            <if test="vo.years != null and vo.years !=''">
                and b.years = #{vo.years}
            </if>
            <if test="vo.bearDepartmentStr != null and vo.bearDepartmentStr != ''">
                and c.department_name=#{vo.bearDepartmentStr}
            </if>
            <if test="vo.createName != null and vo.createName != ''">
                <bind name="createName" value="'%' + vo.createName + '%'"/>
                and b.create_name like #{createName}
            </if>
            <if test="vo.startDate != null and vo.startDate != '' and vo.endDate != null and vo.endDate != ''">
                <![CDATA[and b.start_date >= #{vo.startDate}]]>  <![CDATA[and b.end_date <= #{vo.endDate}]]>
            </if>
            order by b.create_time desc,b.scheme_detail_code desc
        </where>
    </select>


    <select id="findHeadSchemeListBySchemeType" resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        SELECT
        b.*
        FROM
        tpm_overall_plan_case b
        LEFT JOIN tpm_overall_plan a ON a.scheme_code = b.scheme_code
        <where>
            a.del_flag = '${@<EMAIL>()}'
            AND a.process_status = '3'
            AND a.scheme_type = #{schemeType}
            and a.tenant_code = #{tenantCode}
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="vo.schemeCode + '%'"/>
                and a.scheme_code like #{schemeCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and a.scheme_name like #{schemeName}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and b.scheme_detail_code like #{schemeDetailCode}
            </if>
        </where>
    </select>

    <select id="findNotCaseBySchemeTypeList"
            resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        select
        a.scheme_name,b.*
        from tpm_overall_plan a
        left join tpm_overall_plan_case b on a.scheme_code = b.scheme_code
        <where>
            a.del_flag = '${@<EMAIL>()}'
            and a.process_status = '3'
            and a.tenant_code = #{tenantCode}
            and a.scheme_type = #{schemeType}
            <if test="vo.delFlag != null and vo.delFlag != ''">
                and b.del_flag = #{vo.delFlag}
            </if>
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                <bind name="schemeCode" value="vo.schemeCode + '%'"/>
                and a.scheme_code like #{schemeCode}
            </if>
            <if test="vo.excludeSchemeDetailCodes != null and vo.excludeSchemeDetailCodes.size()>0">
                and b.scheme_detail_code not in
                <foreach collection="vo.excludeSchemeDetailCodes" open="(" index="index" item="item" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.startDate != null and vo.startDate != ''">
                <![CDATA[and b.start_date > #{vo.startDate}]]>
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and b.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and a.scheme_name like #{schemeName}
            </if>
            <if test="vo.secondCostCategory != null and vo.secondCostCategory != ''">
                <bind name="secondCostCategory" value="'%' + vo.secondCostCategory + '%'"/>
                and b.second_cost_category like #{secondCostCategory}
            </if>
            <if test="vo.secondCostCategoryName != null and vo.secondCostCategoryName != ''">
                <bind name="secondCostCategoryName" value="'%' + vo.secondCostCategoryName + '%'"/>
                and b.second_cost_category_name like #{secondCostCategoryName}
            </if>
            <if test="vo.detailName != null and vo.detailName != ''">
                <bind name="detailName" value="'%' + vo.detailName + '%'"/>
                and b.detail_name like #{detailName}
            </if>
            <if test="vo.detailCode != null and vo.detailCode != ''">
                <bind name="detailCode" value="'%' + vo.detailCode + '%'"/>
                and b.detail_code like #{detailCode}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and b.years = #{vo.years}
            </if>
            <if test="vo.bearDepartmentName != null and vo.bearDepartmentName != ''">
                <bind name="bearDepartmentName" value="'%' + vo.bearDepartmentName + '%'"/>
                and b.bear_department_name like #{bearDepartmentName}
            </if>
            <if test="vo.bearDepartmentCode != null and vo.bearDepartmentCode != ''">
                <bind name="bearDepartmentCode" value="'%' + vo.bearDepartmentCode + '%'"/>
                and b.bear_department_code like #{bearDepartmentCode}
            </if>
            <if test="vo.bearType != null and vo.bearType != ''">
                and b.bear_type = #{vo.bearType}
            </if>
            <if test="vo.costCenterName != null and vo.costCenterName != ''">
                <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
                and b.cost_center_name like #{costCenterName}
            </if>
            <if test="vo.costCenterCode != null and vo.costCenterCode != ''">
                <bind name="costCenterCode" value="'%' + vo.costCenterCode + '%'"/>
                and b.cost_center_code like #{costCenterCode}
            </if>
            <if test="vo.cooperateTypeStr != null and vo.cooperateTypeStr != ''">
                and b.cooperate_type_str = #{vo.cooperateTypeStr}
            </if>
            order by b.create_time desc,b.scheme_detail_code desc
        </where>
    </select>

    <select id="findAlreadyUnderTakeCaseList" resultType="java.lang.String">
        <choose>
            <when test="'head'.toString() == schemeType">
                SELECT
                head_scheme_detail_code release_code
                FROM
                tpm_overall_plan_case
                WHERE
                head_scheme_detail_code in(
                SELECT
                scheme_detail_code FROM tpm_overall_plan a
                LEFT JOIN tpm_overall_plan_case b ON a.scheme_code = b.scheme_code
                WHERE
                a.process_status = '3'
                AND b.del_flag = '009'
                AND b.bear_flag = 'N'
                AND a.scheme_type = 'head')
                AND del_flag = '009'
                UNION ALL
                SELECT
                release_detail_code
                FROM
                tpm_marketing_plan_case
                WHERE
                release_detail_code in(
                SELECT
                scheme_detail_code FROM tpm_overall_plan a
                LEFT JOIN tpm_overall_plan_case b ON a.scheme_code = b.scheme_code
                WHERE
                a.process_status = '3'
                AND b.del_flag = '009'
                AND a.scheme_type = 'head')
                AND del_flag = '009'
            </when>
            <otherwise>
                SELECT
                release_detail_code
                FROM
                tpm_marketing_plan_case
                WHERE
                release_detail_code in(
                SELECT
                scheme_detail_code FROM tpm_overall_plan a
                LEFT JOIN tpm_overall_plan_case b ON a.scheme_code = b.scheme_code
                WHERE
                a.process_status = '3'
                AND b.del_flag = '009'
                AND a.scheme_type = 'region')
                AND del_flag = '009'
            </otherwise>
        </choose>
    </select>


    <select id="findRegionOverallPlanCase"
            resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        select * from tpm_overall_plan a
        left join tpm_overall_plan_case b on a.scheme_code = b.scheme_code
        <where>
            a.tenant_code = #{tenantCode}
            and a.del_flag = '${@<EMAIL>()}'
            and a.scheme_type = 'region'
            and b.head_scheme_detail_code = #{headSchemeDetailCode}
            order by b.create_time desc,b.scheme_detail_code desc
        </where>
    </select>

    <select id="findBearPlanCaseList" resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        SELECT
        a.scheme_code,
        a.scheme_type,
        a.scheme_name,
        a.scheme_theme,
        a.scheme_desc,
        b.apply_amount undertakeAmount,
        b.*
        FROM
        tpm_overall_plan a
        LEFT JOIN tpm_overall_plan_case b ON a.scheme_code = b.scheme_code
        WHERE
        a.process_status = '3'
        AND a.tenant_code = #{tenantCode}
        AND a.del_flag = '${@<EMAIL>()}'
        AND b.del_flag = '${@<EMAIL>()}'
        AND (
        b.bear_flag = 'Y'
        OR b.bear_flag IS NULL)
        <if test="vo.schemeName != null and vo.schemeName != ''">
            <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
            and a.scheme_name like #{schemeName}
        </if>
        <if test="orgCodes != null and orgCodes.size()>0">
            and exists (
            SELECT
            1
            FROM
            tpm_overall_plan_department d
            WHERE d.scheme_detail_code = b.scheme_detail_code
            and d.scheme_detail_code IS NOT NULL
            and d.department_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="vo.includeCategoryCodeSet != null and vo.includeCategoryCodeSet.size()>0">
            and (
            exists (
            SELECT
            1
            FROM
            tpm_cost_type_category c
            LEFT JOIN tpm_cost_type_detail d ON c.category_code = d.category_code
            WHERE
            d.del_flag = '009'
            AND d.enable_status = '009'
            AND c.enable_status = '009'
            AND c.del_flag = '009'
            and b.detail_code = d.detail_code
            AND b.detail_code IS NOT NULL
            AND c.category_code in
            <foreach collection="vo.includeCategoryCodeSet" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
            or
            exists(
                SELECT
                1 FROM tpm_budget_subjects e
                WHERE
                e.budget_subjects_code = b.second_cost_category
                AND b.detail_code IS NULL
                OR b.detail_code = ''
                AND b.second_cost_category in(
                    SELECT
                    d.parent_budget_subjects_code FROM tpm_cost_type_category c
                    LEFT JOIN tpm_budget_subjects d ON c.budget_subjects_code = d.budget_subjects_code
                    WHERE
                    c.category_code in
                    <foreach collection="vo.includeCategoryCodeSet" open="(" close=")" index="index" item="item" separator=",">
                        #{item}
                    </foreach>
                )
            )
            )
        </if>
        <if test="vo.schemeType != null and vo.schemeType != ''">
            and a.scheme_type = #{vo.schemeType}
        </if>
        <if test="vo.schemeCode != null and vo.schemeCode != ''">
            <bind name="schemeCode" value="vo.schemeCode + '%'"/>
            and a.scheme_code like #{schemeCode}
        </if>
        <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
            <bind name="schemeDetailCode" value="vo.schemeDetailCode + '%'"/>
            and b.scheme_detail_code like #{vo.schemeDetailCode}
        </if>
        <if test="vo.years != null and vo.years != ''">
            and b.years = #{vo.years}
        </if>
        order by b.create_time desc,b.scheme_detail_code desc
    </select>

    <select id="checkRegionOverall" resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        SELECT
        a.scheme_code,
        a.scheme_type,
        a.scheme_name,
        a.scheme_theme,
        b.*
        FROM
        tpm_overall_plan a
        LEFT JOIN tpm_overall_plan_case b ON a.scheme_code = b.scheme_code
        WHERE
        a.process_status = '3'
        AND a.tenant_code = #{tenantCode}
        AND a.del_flag = '${@<EMAIL>()}'
        AND b.del_flag = '${@<EMAIL>()}'
        and b.years = #{years}
        AND (
        b.bear_flag = 'Y'
        OR b.bear_flag IS NULL)
        <if test="orgCodeSet != null and orgCodeSet.size()>0">
            and exists (
            SELECT
            1
            FROM
            tpm_overall_plan_department d
            WHERE d.scheme_code = a.scheme_code
            <choose>
                <when test="'Y'.toString()==detailFlag">
                    and d.scheme_detail_code is not null
                </when>
                <otherwise></otherwise>
            </choose>
            and d.department_code in
            <foreach collection="orgCodeSet" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="schemeType != null and schemeType != ''">
            and a.scheme_type = #{schemeType}
        </if>
        order by b.create_time desc,b.scheme_detail_code desc
    </select>

    <select id="findAlreadyUndertakeAmount"
            resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        SELECT
        apply_amount,
        head_scheme_detail_code
        FROM
        tpm_overall_plan_case
        WHERE
        del_flag = '009'
        <if test="headSchemeDetailCodes != null and headSchemeDetailCodes.size()>0">
            AND head_scheme_detail_code in
            <foreach collection="headSchemeDetailCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findHeadPlanCaseListByOrgCodes" resultType="com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase">
        SELECT b.*
        FROM tpm_overall_plan a
        LEFT JOIN tpm_overall_plan_case b ON a.scheme_code = b.scheme_code
        WHERE a.process_status = '3' and b.del_flag = '009'
        and a.tenant_code = #{tenantCode}
        AND a.scheme_type = 'head'
        AND b.years = #{years}
        and b.bear_flag = #{bearFlag}
        AND EXISTS (SELECT 1
        FROM tpm_overall_plan_department c
        WHERE b.scheme_detail_code = c.scheme_detail_code
        AND c.department_code in
        <foreach collection="orgCodes" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <select id="findUnderTakeCaseList" resultType="com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase">
        SELECT
        a.head_scheme_detail_code,
        sum(a.apply_amount) apply_amount
        FROM
        tpm_overall_plan_case a
        LEFT JOIN tpm_overall_plan b ON a.scheme_code = b.scheme_code
        WHERE
        b.tenant_code = #{tenantCode}
        and a.head_scheme_detail_code in
        <foreach collection="headSchemeDetailCodes" open="(" index="index" item="item" separator="," close=")">
            #{item}
        </foreach>
        <if test="excludeSchemeCode != null and excludeSchemeCode != ''">
            AND a.scheme_detail_code != #{excludeSchemeCode}
        </if>
        AND b.process_status in('2', '3')
        GROUP BY
        a.head_scheme_detail_code
    </select>
    <select id="findListBySchemeCodeAndSchemeDetailCodePair"
            resultType="com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase">
        SELECT
            *
        FROM
            tpm_overall_plan_case
        WHERE
            del_flag = '009'
        and (scheme_code, scheme_detail_code) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
           (#{item.schemeCode},#{item.schemeDetailCode})
        </foreach>
    </select>


    <select id="findRegionUnderTakeHeadScheme" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        b.head_scheme_detail_code releaseDetailCode,
        b.apply_amount
        FROM
        tpm_overall_plan a
        LEFT JOIN tpm_overall_plan_case b ON a.scheme_code = b.scheme_code
        WHERE
        a.process_status = '3'
        AND b.del_flag = '009'
        AND b.head_scheme_detail_code in
        <foreach collection="releaseDetailCodes" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        <if test="notCodes!=null and notCodes.size>0">
            and b.scheme_detail_code not in <foreach collection="notCodes" separator="," open="(" close=")" item="code">#{code}</foreach>
        </if>
    </select>

    <select id="findUnderTakeSchemeDetailList" resultType="com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanCaseVo">
        SELECT * from (
                          SELECT
                              b.id,
                              b.scheme_detail_code,
                              b.scheme_code,
                              a.scheme_name,
                              b.detail_name,
                              b.detail_code,
                              b.start_date,
                              b.end_date,
                              b.bear_department_code belong_department_code,
                              b.bear_department_name belong_department_name,
                              c.department_name bear_department_name,
                              b.cost_center_code,
                              b.cost_center_name,
                              d.customer_name,
                              d.terminal_name,
                              CASE
                                  WHEN e.type = 'cal_item' THEN
                                      NAME
                                  END item_name,
                              CASE
                                  WHEN e.type = 'cal_product' THEN
                                      NAME
                                  END product_name,
                              b.apply_amount
                          FROM
                              tpm_overall_plan a
                                  LEFT JOIN tpm_overall_plan_case b ON a.scheme_code = b.scheme_code
                                  LEFT JOIN ( SELECT GROUP_CONCAT( department_name ) department_name, scheme_detail_code FROM tpm_overall_plan_department GROUP BY scheme_detail_code ) c ON b.scheme_detail_code = c.scheme_detail_code
                                  LEFT JOIN ( SELECT group_concat( customer_name ) customer_name, GROUP_CONCAT( terminal_name ) terminal_name, scheme_detail_code FROM tpm_overall_plan_scope GROUP BY scheme_detail_code ) d ON b.scheme_detail_code = d.scheme_detail_code
                                  LEFT JOIN ( SELECT GROUP_CONCAT( NAME ) NAME, type, scheme_detail_code FROM tpm_overall_plan_product GROUP BY scheme_detail_code, type ) e ON e.scheme_detail_code = b.scheme_detail_code
                          WHERE
                              a.process_status = '3' and b.del_flag = '009'
                            and b.head_scheme_detail_code IN
                          <foreach collection="regionUnderTakeList" separator="," item="item" index="index" open="(" close=")">
                                #{item}
                          </foreach>
                          union all
                          SELECT
                              b.id,
                              b.scheme_detail_code,
                              b.scheme_code,
                              a.scheme_name,
                              b.detail_name,
                              b.detail_code,
                              b.start_date,
                              b.end_date,
                              b.belong_department_code,
                              b.belong_department_name,
                              b.bear_department_name,
                              b.cost_center_code,
                              b.cost_center_name,
                              b.customer_name,
                              b.terminal_name,
                              CASE
                              WHEN e.type = 'cal_item'
                              AND b.case_type != 'BDMB0006' THEN
                              NAME
                              WHEN e.type = 'fee_belong_item'
                              AND b.case_type = 'BDMB0006' THEN
                              NAME
                              END item_name,
                              CASE
                              WHEN e.type = 'cal_product' THEN
                              NAME
                              END product_name,
                              b.apply_amount
                          FROM
                              tpm_marketing_plan a
                              LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
                              LEFT JOIN ( SELECT GROUP_CONCAT( NAME ) NAME, type, scheme_detail_code FROM tpm_marketing_plan_product GROUP BY scheme_detail_code, type ) e ON e.scheme_detail_code = b.scheme_detail_code
                          WHERE
                              a.process_status = '3' and b.del_flag = '009'
                              and b.head_scheme_detail_code IN
                            <foreach collection="marketingUnderTakeList" separator="," item="item" index="index" open="(" close=")">
                                #{item}
                            </foreach>
                      ) a
    </select>
    <select id="findCaseListByReleaseDetailCodes"  resultType="com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase">
        SELECT
        b.scheme_detail_code,
        b.apply_amount
        FROM
        tpm_overall_plan a
        LEFT JOIN tpm_overall_plan_case b ON a.scheme_code = b.scheme_code
        WHERE
        a.process_status in ('0','1','2','3')
        AND b.del_flag = '009'
        AND b.scheme_detail_code in
        <foreach collection="releaseDetailCodes" open="(" close=")" index="index" item="item" separator=",">#{item}</foreach>
    </select>
</mapper>

