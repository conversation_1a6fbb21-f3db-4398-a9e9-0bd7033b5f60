package com.biz.crm.tpm.business.activities.materialdemand.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandDetail;
import com.biz.crm.tpm.business.activities.materialdemand.mapper.MaterialDemandDetailMapper;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandQueryVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandReportVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:35
 */
@Component
public class MaterialDemandDetailRepository extends ServiceImpl<MaterialDemandDetailMapper, MaterialDemandDetail> {

    public List<MaterialDemandDetail> findListByCodes(List<String> codes) {
        return this.lambdaQuery()
                .in(MaterialDemandDetail::getDemandCode, codes)
                .list();
    }

    public void deleteByCodes(List<String> codes) {
        this.lambdaUpdate()
                .in(MaterialDemandDetail::getDemandCode, codes).remove();
    }

    public List<MaterialDemandDetail> findListByDemandDetailCodes(List<String> demandDetailCodes) {
        return this.lambdaQuery()
                .in(MaterialDemandDetail::getDemandDetailCode, demandDetailCodes)
                .list();
    }


    public Page<MaterialDemandReportVo> findMaterialDemandReportList(Page<MaterialDemandReportVo> page, MaterialDemandReportVo vo) {
        String tenantCode = TenantUtils.getTenantCode();
        return this.baseMapper.findMaterialDemandReportList(page, vo, tenantCode);
    }

    public Page<MarketingPlanCaseVo> queryDetailList(Page<MarketingPlanCaseVo> page, MaterialDemandQueryVo vo) {
        return this.baseMapper.queryDetailList(page, vo, TenantUtils.getTenantCode());
    }

    public List<MaterialDemandDetail> findListByMaterialCodesAndOrgCodes(List<String> materialCodes,List<String> orgCodes){
        return this.baseMapper.findListByMaterialCodesAndOrgCodes(materialCodes,orgCodes,TenantUtils.getTenantCode());
    }
}
