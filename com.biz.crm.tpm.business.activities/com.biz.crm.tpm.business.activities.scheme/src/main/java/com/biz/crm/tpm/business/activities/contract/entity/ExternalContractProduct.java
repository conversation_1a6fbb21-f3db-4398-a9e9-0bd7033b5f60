package com.biz.crm.tpm.business.activities.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 11:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_external_contract_product")
@Table(
        name = "tpm_external_contract_product",
        indexes = {
                @Index(name = "tpm_external_contract_product_index0", columnList = "contract_code"),
                @Index(name = "tpm_external_contract_product_index1", columnList = "contract_detail_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_external_contract_product", comment = "外部合同产品范围")
@ApiModel(value = "ExternalContractProduct", description = "外部合同产品范围")
public class ExternalContractProduct extends UuidFlagOpEntity {

    @Column(name = "contract_code", columnDefinition = "varchar(32) comment '合同编码'")
    private String contractCode;

    @Column(name = "contract_detail_code", columnDefinition = "varchar(32) comment '合同明细编码'")
    private String contractDetailCode;

    @ApiModelProperty("唯一编码")
    @Column(name = "only_key", columnDefinition = "varchar(64) comment '唯一编码'")
    private String onlyKey;

    @ApiModelProperty("编码")
    @Column(name = "code", columnDefinition = "varchar(32) comment '编码'")
    private String code;

    @ApiModelProperty("物料编码")
    @Column(name = "material_code", columnDefinition = "varchar(32) comment '物料编码'")
    private String materialCode;

    @ApiModelProperty("名称")
    @Column(name = "name", columnDefinition = "varchar(64) comment '名称'")
    private String name;

    @ApiModelProperty("数量")
    @Column(name = "num", columnDefinition = "decimal(10) comment '数量'")
    private BigDecimal num;

    @ApiModelProperty("类型")
    @Column(name = "type", columnDefinition = "varchar(20) comment '类型'")
    private String type;
}
