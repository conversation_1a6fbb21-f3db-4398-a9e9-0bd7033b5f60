package com.biz.crm.tpm.business.activities.contract.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.vo.OssFileVo;
import com.biz.crm.tpm.business.activities.contract.entity.ExternalContractFile;
import com.biz.crm.tpm.business.activities.contract.mapper.ExternalContractFileMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 11:39
 */
@Component
public class ExternalContractFileRepository extends ServiceImpl<ExternalContractFileMapper, ExternalContractFile> {

    public void saveBatchList(List<OssFileVo> fileUrlList, String contractCode) {
        deleteByContractCodes(Lists.newArrayList(contractCode));
        if (CollectionUtils.isEmpty(fileUrlList)) {
            return;
        }
        List<ExternalContractFile> files = fileUrlList.stream().map(x -> {
            ExternalContractFile file = new ExternalContractFile();
            file.setContractCode(contractCode);
            file.setUrl(x.getFileUrl());
            file.setFileName(x.getFileName());
            return file;
        }).collect(Collectors.toList());
        this.saveBatch(files);
    }

    public void deleteByContractCodes(List<String> contractCodes) {
        this.lambdaUpdate()
                .in(ExternalContractFile::getContractCode, contractCodes)
                .remove();
    }

    public List<ExternalContractFile> findListByContractCodes(List<String> contractCodes) {
        return this.lambdaQuery()
                .in(ExternalContractFile::getContractCode, contractCodes)
                .list();
    }

    public void removeByOnlyKeys(List<String> onlyKeys) {
        if (CollectionUtils.isEmpty(onlyKeys)) {
            return;
        }
        this.lambdaUpdate()
                .in(ExternalContractFile::getOnlyKey, onlyKeys)
                .remove();
    }
}
