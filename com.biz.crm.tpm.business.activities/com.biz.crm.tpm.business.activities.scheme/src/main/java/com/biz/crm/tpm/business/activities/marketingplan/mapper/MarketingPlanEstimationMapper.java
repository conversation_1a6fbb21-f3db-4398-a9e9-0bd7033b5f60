package com.biz.crm.tpm.business.activities.marketingplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanEstimation;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectMarketingEstimation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
public interface MarketingPlanEstimationMapper extends BaseMapper<MarketingPlanEstimation> {

    List<MarketingPlanEstimationVo> findOriginalListBySchemeCode(@Param("schemCodeList") List<String> schemCodeList);
}
