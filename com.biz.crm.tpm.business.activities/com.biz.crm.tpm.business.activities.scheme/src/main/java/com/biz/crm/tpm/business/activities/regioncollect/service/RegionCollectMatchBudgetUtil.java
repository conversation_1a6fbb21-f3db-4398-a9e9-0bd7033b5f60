package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.regioncollect.constant.RegionCollectConstant;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlVoService;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import com.biz.crm.tpm.business.track.sdk.service.BudgetTrackService;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/30 14:47
 */
public class RegionCollectMatchBudgetUtil {

    private MarketingPlanCase caseVo;

    private BudgetControlVoService budgetControlVoService;

    private BudgetSubjectsVoService budgetSubjectsVoService;

    private OrgVoService orgVoService;

    private BudgetTrackService budgetTrackService;

    private RegionCollectBudgetService regionCollectBudgetService;

    private List<BudgetControlVo> budgetControlVoList = Lists.newArrayList();

    private Map<String, String> budgetControlMap = Maps.newHashMap();

    private Set<String> budgetSubjectCodeSet = Sets.newHashSet();

    private RedisService redisService;

    private String collectCode;

    private Map<String, List<String>> planCaseControlCodeMap;

    public static RegionCollectMatchBudgetUtil build(ApplicationContext context, MarketingPlanCase vo, String collectCode, Map<String, List<String>> planCaseControlCodeMap) {
        RegionCollectMatchBudgetUtil util = new RegionCollectMatchBudgetUtil();
        util.caseVo = vo;
        util.budgetControlVoService = context.getBean(BudgetControlVoService.class);
        util.orgVoService = context.getBean(OrgVoService.class);
        util.budgetTrackService = context.getBean(BudgetTrackService.class);
        util.budgetSubjectsVoService = context.getBean(BudgetSubjectsVoService.class);
        util.collectCode = collectCode;
        util.regionCollectBudgetService = context.getBean(RegionCollectBudgetService.class);
        util.redisService = context.getBean(RedisService.class);
        util.planCaseControlCodeMap = planCaseControlCodeMap;
        return util;
    }

    /**
     * 匹配规则
     *
     * @return
     */
    public RegionCollectMatchBudgetUtil matchBudget() {
        String years = caseVo.getYears();
        String departmentCode = caseVo.getBearDepartmentCode();
        List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(departmentCode);
        List<String> orgCodes = orgVoList.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
        String customerCode = caseVo.getCustomerCode();
        String budgetSubjectCode = caseVo.getBudgetSubjectCode();
        this.budgetSubjectCodeSet = budgetSubjectsVoService.findAllParentSubjectCodesByCodes(budgetSubjectCode);
        this.budgetControlVoList = budgetControlVoService.matchBudgetControl(years, orgCodes, customerCode, budgetSubjectCodeSet);
        Validate.isTrue(!CollectionUtils.isEmpty(budgetControlVoList), String.format("方案%s的方案明细%s未匹配到管控规则", caseVo.getSchemeCode(), caseVo.getSchemeDetailCode()));
        budgetControlMap = budgetControlVoList.stream().collect(Collectors.toMap(BudgetControlVo::getControlCode, BudgetControlVo::getControlForm));
        return this;
    }


    /**
     * 计算费用跟踪
     *
     * @return
     */
    public RegionCollectBudgetVo calBudgetTrack() {
        budgetControlVoList = Optional.ofNullable(budgetControlVoList).orElse(Lists.newArrayList());
        List<String> codes = budgetControlVoList.stream().map(BudgetControlVo::getControlCode).collect(Collectors.toList());
        budgetTrackService.generateBudgetTrackByCodes(codes);
        List<BudgetTrackVo> budgetTrackVos = budgetTrackService.findByBudgetControlCodes(codes);
        //匹配最实际的管控规则
        List<String> orgCodes = budgetTrackVos.stream().map(BudgetTrackVo::getDepartmentOneCode).collect(Collectors.toList());
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllChildrenByOrgCodesMap(orgCodes);
        List<BudgetTrackVo> filterList = budgetTrackVos.stream().filter(x -> {
            List<OrgVo> orgVoList = orgMap.get(x.getDepartmentOneCode());
            Set<String> orgCodeSet = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet());
            if (orgCodeSet.contains(caseVo.getBearDepartmentCode()) && caseVo.getYears().equals(x.getYearMonthLy()) &&
                    //判断科目 可以为空 但是如果有值 则必须匹配上
                    (ObjectUtils.isEmpty(x.getBudgetSubjectCode()) || (ObjectUtils.isNotEmpty(x.getBudgetSubjectCode())
                            && this.budgetSubjectCodeSet.contains(x.getBudgetSubjectCode())))) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        Validate.isTrue(!CollectionUtils.isEmpty(filterList), String.format("方案编码%s明细编码%s年月+部门+科目未找到对应的预算管控规则",
                caseVo.getSchemeCode(), caseVo.getSchemeDetailCode()));
        BudgetTrackVo budgetTrackVo = filterList.stream().min(Comparator.comparing(BudgetTrackVo::getMonthBalanceAmount)).get();
        RegionCollectBudgetVo vo = new RegionCollectBudgetVo();
        vo.setApplyAmount(caseVo.getApplyAmount());
        vo.setSchemeCode(caseVo.getSchemeCode());
        vo.setSchemeDetailCode(caseVo.getSchemeDetailCode());
        vo.setYears(budgetTrackVo.getYearMonthLy());
        vo.setDepartmentCode(budgetTrackVo.getDepartmentOneCode());
        vo.setDepartmentName(budgetTrackVo.getDepartmentOneName());
        vo.setCustomerCode(budgetTrackVo.getCustomerCode());
        vo.setCustomerName(budgetTrackVo.getCustomerName());
        vo.setItemCode(budgetTrackVo.getItemCode());
        vo.setItemName(budgetTrackVo.getItemName());
        vo.setProductCode(budgetTrackVo.getProductCode());
        vo.setProductName(budgetTrackVo.getProductName());
        vo.setSubjectCode(budgetTrackVo.getBudgetSubjectCode());
        vo.setSubjectName(budgetTrackVo.getBudgetSubjectName());
        vo.setSurplusAmount(budgetTrackVo.getMonthBalanceAmount());
        vo.setUsedAmount(caseVo.getApplyAmount());
        vo.setControlCode(budgetTrackVo.getBudgetControlCode());
        vo.setControlForm(budgetControlMap.get(budgetTrackVo.getBudgetControlCode()));
        if (ObjectUtils.isNotEmpty(collectCode)) {
            regionCollectBudgetService.saveBatchList(Lists.newArrayList(vo), collectCode);
        }
        //修改他的状态 并且存放数据
        String dataKey = RegionCollectConstant.getRegionCollectSchemeDataKey(collectCode, caseVo.getSchemeDetailCode());
        redisService.set(dataKey, vo);
        String key = RegionCollectConstant.getRegionCollectSchemeDetailKey(collectCode, caseVo.getSchemeDetailCode());
        redisService.set(key, BooleanEnum.TRUE.getCapital());
        return vo;
    }


}
