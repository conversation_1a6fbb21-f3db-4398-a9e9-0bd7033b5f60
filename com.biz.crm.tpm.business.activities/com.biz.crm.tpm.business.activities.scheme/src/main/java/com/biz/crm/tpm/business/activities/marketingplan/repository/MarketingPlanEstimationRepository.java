package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanEstimation;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanEstimationMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectMarketingEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectMarketingEstimationMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class MarketingPlanEstimationRepository extends ServiceImpl<MarketingPlanEstimationMapper, MarketingPlanEstimation> {


    public List<MarketingPlanEstimation> findListBySchemeCodes(List<String> schemeCodes) {
        return this.lambdaQuery()
                .in(MarketingPlanEstimation::getSchemeCode, schemeCodes)
                .orderByAsc(MarketingPlanEstimation::getSort)
                .list();
    }


    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.lambdaUpdate()
                .in(MarketingPlanEstimation::getSchemeCode, schemeCodes)
                .remove();
    }

    public List<MarketingPlanEstimationVo> findOriginalListBySchemeCode(List<String> schemCodeList) {
        return baseMapper.findOriginalListBySchemeCode(schemCodeList);
    }
}
