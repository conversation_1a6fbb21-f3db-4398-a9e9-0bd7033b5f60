package com.biz.crm.tpm.business.activities.marketingplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosure;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureVo;
import org.apache.ibatis.annotations.Param;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:21
 */
public interface PlanClosureMapper extends BaseMapper<PlanClosure> {

    Page<PlanClosureVo> findList(Page<PlanClosureVo> page, @Param("vo")PlanClosureVo vo);
}
