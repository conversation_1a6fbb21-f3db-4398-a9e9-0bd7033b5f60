package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosure;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanClosureMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:23
 */
@Component
@Slf4j
public class PlanClosureRepository extends ServiceImpl<PlanClosureMapper, PlanClosure> {

    /**
     * 分页查询
     *
     * @param page
     * @param vo
     * @return
     */
    public Page<PlanClosureVo> findList(Page<PlanClosureVo> page, PlanClosureVo vo) {
        return this.baseMapper.findList(page, vo);
    }


    public PlanClosure queryByIdOrCode(String id, String closeCode) {
        return this.lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(id), PlanClosure::getId, id)
                .eq(ObjectUtils.isNotEmpty(closeCode), PlanClosure::getCloseCode, closeCode)
                .eq(PlanClosure::getTenantCode, TenantUtils.getTenantCode())
                .eq(PlanClosure::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .one();
    }

    public List<PlanClosure> findListByIdList(List<String> idList) {
        return this.lambdaQuery()
                .eq(PlanClosure::getTenantCode, TenantUtils.getTenantCode())
                .eq(PlanClosure::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(PlanClosure::getId, idList).list();
    }

    public List<PlanClosure> findListByPrepareAndReject(String sourceType){
        return this.lambdaQuery()
                .in(PlanClosure::getProcessStatus, Lists.newArrayList(ProcessStatusEnum.PREPARE.getDictCode(),ProcessStatusEnum.REJECT.getDictCode()))
                .eq(PlanClosure::getTenantCode,TenantUtils.getTenantCode())
                .eq(PlanClosure::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
                .eq(PlanClosure::getSourceType,sourceType)
                .list();
    }

    public List<PlanClosure> findByCloseCodeList(List<String> closeCodes) {
        if (CollectionUtils.isEmpty(closeCodes)) return Lists.newArrayList();
        return this.lambdaQuery()
                .in(PlanClosure::getCloseCode, closeCodes)
                .eq(PlanClosure::getTenantCode, TenantUtils.getTenantCode())
                .eq(PlanClosure::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }
}
