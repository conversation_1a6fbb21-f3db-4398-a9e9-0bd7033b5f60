package com.biz.crm.tpm.business.activities.schemefile.service.internal;

import com.biz.crm.tpm.business.activities.schemefile.entity.SchemeCaseFiles;
import com.biz.crm.tpm.business.activities.schemefile.repository.SchemeCaseFilesRepository;
import com.biz.crm.tpm.business.activities.schemefile.service.SchemeCaseFilesService;
import com.biz.crm.tpm.business.activities.schemefile.vo.SchemeCaseFilesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/22 18:14
 */
@Service
@Slf4j
public class SchemeCaseFilesServiceImpl implements SchemeCaseFilesService {

    @Resource
    private SchemeCaseFilesRepository schemeCaseFilesRepository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;


    @Override
    public void saveOrUpdateFiles(List<SchemeCaseFilesVo> filesVos, String schemeCode, String schemeType) {
        schemeCaseFilesRepository.deleteBySchemeCodeAndSchemeType(schemeCode, schemeType);
        if (CollectionUtils.isEmpty(filesVos)) {
            return;
        }
        List<SchemeCaseFiles> caseFilesList = (List<SchemeCaseFiles>) nebulaToolkitService.copyCollectionByWhiteList(filesVos, SchemeCaseFilesVo.class,
                SchemeCaseFiles.class, HashSet.class, ArrayList.class);
        for (SchemeCaseFiles files : caseFilesList) {
            files.setSchemeCode(schemeCode);
            files.setSchemeType(schemeType);
            files.setId(null);
            files.setTenantCode(TenantUtils.getTenantCode());
        }
        schemeCaseFilesRepository.saveOrUpdateBatch(caseFilesList);
    }

    @Override
    public List<SchemeCaseFilesVo> findFilesList(String schemeCode, String schemeType) {
        List<SchemeCaseFiles> filesList = schemeCaseFilesRepository.findListBySchemeCodeAndSchemeType(schemeCode, schemeType);
        if (CollectionUtils.isEmpty(filesList)) {
            return Lists.newArrayList();
        }
        return (List<SchemeCaseFilesVo>) nebulaToolkitService.copyCollectionByWhiteList(filesList, SchemeCaseFiles.class, SchemeCaseFilesVo.class, HashSet.class, ArrayList.class);
    }


    @Override
    public void deleteFiles(List<String> schemeCodes,List<String> schemeTypes) {
        schemeCaseFilesRepository.deleteBySchemeCodeAndSchemeTypeList(schemeCodes,schemeTypes);
    }
}
