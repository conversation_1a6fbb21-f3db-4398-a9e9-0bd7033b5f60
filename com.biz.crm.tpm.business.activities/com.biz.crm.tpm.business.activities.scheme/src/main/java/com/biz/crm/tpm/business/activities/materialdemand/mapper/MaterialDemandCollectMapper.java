package com.biz.crm.tpm.business.activities.materialdemand.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandCollect;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectVo;
import org.apache.ibatis.annotations.Param;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:31
 */
public interface MaterialDemandCollectMapper extends BaseMapper<MaterialDemandCollect> {

    Page<MaterialDemandCollectVo> findList(Page<MaterialDemandCollectVo> page, @Param("vo") MaterialDemandCollectVo vo);
}
