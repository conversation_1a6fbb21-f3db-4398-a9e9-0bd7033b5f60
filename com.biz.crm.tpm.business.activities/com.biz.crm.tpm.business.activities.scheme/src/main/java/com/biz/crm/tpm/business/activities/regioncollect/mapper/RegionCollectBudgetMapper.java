package com.biz.crm.tpm.business.activities.regioncollect.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectBudget;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
public interface RegionCollectBudgetMapper extends BaseMapper<RegionCollectBudget> {

    List<RegionCollectBudgetVo> findCollectByCollectCode(@Param("collectCode") String collectCode);
}
