package com.biz.crm.tpm.business.activities.marketingplan.service;

import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanSchemeEstimationVo;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 00:13
 */
public interface MarketingPlanSchemeEstimationService {

    void saveBatchList(List<MarketingPlanSchemeEstimationVo> list, String schemeCode);

    List<MarketingPlanSchemeEstimationVo> findListBySchemeCode(List<String> schemCodeList);

    void deleteBySchemeCodes(List<String> schemeCodes);

    List<MarketingPlanSchemeEstimationVo> findOriginalListBySchemeCode(List<String> schemCodeList);
}
