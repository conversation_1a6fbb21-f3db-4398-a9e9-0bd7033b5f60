package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanBigDateExecutionCollectDetail;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanBigDateExecutionCollectDetailPicture;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanBigDateExecutionCollectDetailPictureMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.repository.PlanBigDateExecutionCollectDetailPictureRepository
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:08
 */
@Component
@Slf4j
public class PlanBigDateExecutionCollectDetailPictureRepository extends ServiceImpl<PlanBigDateExecutionCollectDetailPictureMapper, PlanBigDateExecutionCollectDetailPicture> {

    public List<PlanBigDateExecutionCollectDetailPicture> findByCollectId(String id) {
        return this.lambdaQuery()
                .eq(PlanBigDateExecutionCollectDetailPicture::getCollectId, id).list();
    }

    public void deleteByCollectId(String id) {
        this.remove(Wrappers.lambdaQuery(PlanBigDateExecutionCollectDetailPicture.class)
                .eq(PlanBigDateExecutionCollectDetailPicture::getCollectId, id));
    }

    public List<PlanBigDateExecutionCollectDetailPicture> findListByCollectIds(List<String> collectIds) {
        if (CollectionUtils.isEmpty(collectIds)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(PlanBigDateExecutionCollectDetailPicture::getCollectId, collectIds)
                .list();
    }
}
