package com.biz.crm.tpm.business.activities.scheme.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeDetailRelation;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeDetailRelationVo;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeRelationVo;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 方案活动关联信息(TpmProjectActivityDetailRelation)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-17 10:52:33
 */
public interface ActivitiesSchemeDetailRelationService {

  /**
   * 分页查询数据
   * @param pageable 分页对象
   * @param activitiesSchemeDetailRelation 实体对象
   * @return
   */
  Page<ActivitiesSchemeDetailRelation> findByConditions(Pageable pageable, ActivitiesSchemeDetailRelation activitiesSchemeDetailRelation);
  
   /**
   * 通过主键查询单条数据
   * @param id 主键
   * @return 单条数据
   */
   ActivitiesSchemeDetailRelation findById(String id);

   /**
   * 修改新据
   * @param activitiesSchemeDetailRelation 实体对象
   * @return 修改结果
   */
   ActivitiesSchemeDetailRelation update(ActivitiesSchemeDetailRelation activitiesSchemeDetailRelation);

  /**
   * 根据id删除数据
   *
   * @param ids
   */
  void removeByIds(Collection<String> ids);


  /**
   * 活动编码查询活动关联信息
   * @param activityCode 主键结合
   * @return
   */
  List<ActivitiesSchemeDetailRelationVo> findByActivityCode(String activityCode);



  /**
   * 批量保存关联关系数据
   */
  void saveBatch(List<ActivitiesSchemeDetailRelationVo> activitiesSchemeRelationVos);


  /**
   * 根据活动编号集合获取对应的关联表信息
   */
  List<ActivitiesSchemeDetailRelationVo> findByActivityCodes(Set<String> activityCodes);

}

