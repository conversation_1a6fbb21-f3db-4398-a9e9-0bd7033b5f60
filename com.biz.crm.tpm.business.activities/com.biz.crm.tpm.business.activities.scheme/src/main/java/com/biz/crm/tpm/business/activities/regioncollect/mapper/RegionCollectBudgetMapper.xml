<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectBudgetMapper">


    <select id="findCollectByCollectCode" resultType="com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo">
        SELECT years,
        department_code,
        department_name,
        subject_name,
        control_code,
        control_form,
        surplus_amount,
        customer_name,
        item_name,
        product_name,
        sum(apply_amount) apply_amount,
        sum(used_amount) used_amount
        FROM tpm_region_collect_budget
        <where>
            1=1
            and collect_code = #{collectCode}
        </where>
        group by years,
        department_code,
        department_name,
        surplus_amount,
        control_form,
        control_code,
        customer_name,
        item_name,
        product_name,
        subject_name
    </select>

</mapper>

