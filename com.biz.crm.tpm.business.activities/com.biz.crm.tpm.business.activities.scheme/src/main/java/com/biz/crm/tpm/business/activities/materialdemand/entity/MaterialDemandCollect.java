package com.biz.crm.tpm.business.activities.materialdemand.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.WorkflowFlagOpEntity;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectDetailVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_material_demand_collect")
@Table(
        name = "tpm_material_demand_collect",
        indexes = {
                @Index(name = "tpm_material_demand_collect_index0", columnList = "tenant_code,collect_code", unique = true),
                @Index(name = "tpm_material_demand_collect_index2", columnList = "del_flag")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_material_demand_collect", comment = "物料需求提报")
@ApiModel(value = "MaterialDemandCollect", description = "物料需求提报汇总")
public class MaterialDemandCollect extends WorkflowFlagOpEntity {

    @ApiModelProperty("汇总编码")
    @Column(name = "collect_code",columnDefinition = "varchar(32) comment '汇总编码'")
    private String collectCode;

    @ApiModelProperty("汇总名称")
    @Column(name = "collect_name",columnDefinition = "varchar(64) comment '汇总名称'")
    private String collectName;

    @ApiModelProperty("年月")
    @Column(name = "years",columnDefinition = "varchar(10) comment '年月'")
    private String years;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("审批单号")
    @Column(name = "process_number", columnDefinition = "varchar(64) COMMENT '审批单号'")
    private String processNumber;

    @ApiModelProperty("推送日期")
    @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    @Column(name = "oa_id", columnDefinition = "varchar(32) COMMENT 'OA人员id'")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    @Column(name = "oa_user_name", columnDefinition = "varchar(32) COMMENT 'OA人员账号'")
    private String oaUserName;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("回传TPM接口唯一标识（接口用）")
    private String businessCode;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty("url")
    private String tpmUrl;

    @Transient
    @TableField(exist = false)
    private List<MaterialDemandCollectDetailVo> demandCollectDetailVos;
}
