package com.biz.crm.tpm.business.activities.scheme.event;

import com.biz.crm.tpm.business.activities.scheme.dto.ActivitiesSchemeLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 方案活动 业务日志监听
 * <AUTHOR> <PERSON>
 * @date : 2022-7-1
 */
public interface ActivitiesSchemeLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(ActivitiesSchemeLogEventDto eventDto);
  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(ActivitiesSchemeLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(ActivitiesSchemeLogEventDto eventDto);
  /**
   * 启用
   *
   * @param eventDto
   */
  void onEnable(ActivitiesSchemeLogEventDto eventDto);
  /**
   * 禁用
   *
   * @param eventDto
   */
  void onDisable(ActivitiesSchemeLogEventDto eventDto);
}
