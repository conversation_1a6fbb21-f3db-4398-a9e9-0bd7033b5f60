package com.biz.crm.tpm.business.activities.overallplan.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanDepartment;
import com.biz.crm.tpm.business.activities.overallplan.mapper.OverallPlanDepartmentMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OverPlanDepartmentRepository extends ServiceImpl<OverallPlanDepartmentMapper, OverallPlanDepartment> {


    public List<OverallPlanDepartment> findListBySchemeCodeList(List<String> schemeCodeList) {
        return this.lambdaQuery()
                .in(OverallPlanDepartment::getSchemeCode, schemeCodeList)
                .list();
    }

    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.remove(Wrappers.lambdaQuery(OverallPlanDepartment.class)
                .in(OverallPlanDepartment::getSchemeCode, schemeCodes));
    }

    public List<OverallPlanDepartment> findListBySchemeDetailCodeList(List<String> schemeDetailCodeList) {
        return this.lambdaQuery()
                .in(OverallPlanDepartment::getSchemeDetailCode, schemeDetailCodeList).list();
    }
}
