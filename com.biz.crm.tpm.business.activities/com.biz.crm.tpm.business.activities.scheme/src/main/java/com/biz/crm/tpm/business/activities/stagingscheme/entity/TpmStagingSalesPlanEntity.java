package com.biz.crm.tpm.business.activities.stagingscheme.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @describe
 * @project crm-tpm
 * @package com.biz.crm.tpm.business.sn.local.activity.stagingscheme.entity
 * @date 2023/12/14 10:42
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@Table(name = "tpm_staging_sales_plan", indexes = {
        @Index(name = "release_id_index", columnList = "release_id")
})
@TableName(value = "tpm_staging_sales_plan")
@ApiModel(value = "TpmStagingSalesPlanEntity", description = "销售计划暂存表")
@org.hibernate.annotations.Table(appliesTo = "tpm_staging_sales_plan", comment = "销售计划暂存表")
public class TpmStagingSalesPlanEntity extends UuidFlagOpEntity {

    @Column(name = "release_id",columnDefinition = "varchar(32) comment '关联ID'")
    private String releaseId;

    @Column(name = "json_str",columnDefinition = "longtext comment 'json文本'")
    private String jsonStr;
}
