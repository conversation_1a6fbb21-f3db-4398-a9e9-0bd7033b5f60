package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 15:57
 */
@Data
@CrmExcelImport
public class MarketingSalesPlanImportVo extends CrmExcelVo {

    @CrmExcelColumn(value = "年月")
    private String years;

    @CrmExcelColumn(value = "成本中心编码")
    private String costCenterCode;

    @CrmExcelColumn(value = "成本中心名称")
    private String costCenterName;

    @CrmExcelColumn(value = "Erp编码")
    private String erpCode;

    @CrmExcelColumn(value = "公司编码")
    private String companyCode;

    @CrmExcelColumn(value = "客户编码")
    private String customerCode;

    @CrmExcelColumn(value = "客户名称")
    private String customerName;

    @CrmExcelColumn(value = "品项编码")
    private String itemCode;

    @CrmExcelColumn(value = "品项名称")
    private String itemName;

    @CrmExcelColumn(value = "产品编码")
    private String productCode;

    @CrmExcelColumn(value = "产品名称")
    private String productName;

    @CrmExcelColumn(value = "预估销售量")
    private String estimatedSalesVolume;

    @CrmExcelColumn(value = "预估销售金额")
    private String estimatedCost;

    @CrmExcelColumn(value = "运输方式")
    private String transportType;

    @ApiModelProperty("关联统筹方案编码")
    private String releaseCode;

    @ApiModelProperty("关联统筹方案名称")
    private String releaseName;

    @ApiModelProperty("关联统筹方案明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("关联总部方案编码")
    private String headSchemeCode;

    @ApiModelProperty("关联总部方案明细编码")
    private String headSchemeDetailCode;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("归属成本中心编码")
    private String bdCostCenterCode;

    @ApiModelProperty("归属成本中心编码")
    private String bdCostCenterName;

    @ApiModelProperty("核算品项范围-前端使用")
    private String itemCodeList;

    @ApiModelProperty("核算产品小类范围-前端使用")
    private String levelCodeList;

    @ApiModelProperty("条件数量")
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    private String giveNum;

    @ApiModelProperty("优惠数量-上限")
    private String discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    private String discountAmount;

    @ApiModelProperty("活动描述")
    private String actDesc;


}
