<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.contract.mapper.ExternalContractMapper">


    <select id="findList" resultType="com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo">
        select * from tpm_external_contract
        <where>
            del_flag = '${@<EMAIL>()}' and external_flag = #{dto.externalFlag}
            <if test="dto.contractCode != null and dto.contractCode != ''">
                <bind name="contractCode" value="dto.contractCode + '%'"/>
                and contract_code like #{contractCode}
            </if>
            <if test="dto.contractName != null and dto.contractName != ''">
                <bind name="contractName" value="'%' + dto.contractName + '%'"/>
                and contract_name like #{contractName}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                <bind name="customerCode" value=" dto.customerCode +'%'"/>
                and customer_code like #{customerCode}
            </if>
            <if test="dto.startDate != null and dto.startDate != ''">
                <![CDATA[and start_date >= #{dto.startDate}]]>
            </if>
            <if test="dto.endDate != null and dto.endDate != ''">
                <![CDATA[and end_date <= #{dto.endDate}]]>
            </if>
            <if test="dto.externalFlag != null and dto.externalFlag != ''">
                and external_flag = #{dto.externalFlag}
            </if>
            order by create_time desc,contract_code desc
        </where>
    </select>

    <select id="findContractCostList" resultType="com.biz.crm.tpm.business.activities.contract.dto.ContractCostPageDto">
        select
        b.*,
        a.contract_code,
        a.contract_name,
        a.contract_status,
        a.contract_type,
        a.is_replenish,
        a.original_contract_code,
        a.customer_code,
        a.customer_name,
        a.erp_code,
        a.company_code,
        a.product_group_code,
        a.channel_code,
        a.start_date contractStartDate,
        a.end_date contractEndDate,
        a.execute_status,
        a.log
        from tpm_external_contract_detail b
        left join tpm_external_contract a
        on a.only_key = b.only_key
        <where>
            a.del_flag = '${@<EMAIL>()}' and a.external_flag = #{dto.externalFlag}
            and a.tenant_code = #{tenantCode}
            <if test="dto.contractCode != null and dto.contractCode != ''">
                <bind name="contractCode" value="dto.contractCode + '%'"/>
                and a.contract_code like #{contractCode}
            </if>
            <if test="dto.contractName != null and dto.contractName != ''">
                <bind name="contractName" value="'%' + dto.contractName + '%'"/>
                and a.contract_name like #{contractName}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                <bind name="customerCode" value=" dto.customerCode +'%'"/>
                and a.customer_code like #{customerCode}
            </if>
            <if test="dto.contractStartDate != null and dto.contractStartDate != ''">
                <![CDATA[and a.start_date >= #{dto.contractStartDate}]]>
            </if>
            <if test="dto.contractEndDate != null and dto.contractEndDate != ''">
                <![CDATA[and a.end_date <= #{dto.contractEndDate}]]>
            </if>
            <if test="dto.externalFlag != null and dto.externalFlag != ''">
                and a.external_flag = #{dto.externalFlag}
            </if>
            <if test="dto.contractTypeList != null and dto.contractTypeList.size()>0">
                and a.contract_type in
                <foreach collection="dto.contractTypeList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            order by b.create_time desc,a.contract_code desc
        </where>
    </select>

    <select id="findExternalContractCostList" resultType="com.biz.crm.tpm.business.activities.contract.dto.ContractCostPageDto">
        select
        a.*
        from tpm_external_contract a
        <where>
            a.del_flag = '${@<EMAIL>()}'
            and a.external_flag = #{dto.externalFlag}
            and a.tenant_code = #{tenantCode}
            <if test="dto.contractCode != null and dto.contractCode != ''">
                <bind name="contractCode" value="dto.contractCode + '%'"/>
                and a.contract_code like #{contractCode}
            </if>
            <if test="dto.contractName != null and dto.contractName != ''">
                <bind name="contractName" value="'%' + dto.contractName + '%'"/>
                and a.contract_name like #{contractName}
            </if>
            <if test="dto.customerCode != null and dto.customerCode != ''">
                <bind name="customerCode" value=" dto.customerCode +'%'"/>
                and a.customer_code like #{customerCode}
            </if>
            <if test="dto.contractStartDate != null and dto.contractStartDate != ''">
                <![CDATA[and a.start_date >= #{dto.contractStartDate}]]>
            </if>
            <if test="dto.contractEndDate != null and dto.contractEndDate != ''">
                <![CDATA[and a.end_date <= #{dto.contractEndDate}]]>
            </if>
            <if test="dto.externalFlag != null and dto.externalFlag != ''">
                and a.external_flag = #{dto.externalFlag}
            </if>
            <if test="dto.contractTypeList != null and dto.contractTypeList.size()>0">
                and a.contract_type in
                <foreach collection="dto.contractTypeList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            order by a.create_time desc,a.contract_code desc
        </where>
    </select>

    <select id="findUnExecutedAndNotExternal"
            resultType="com.biz.crm.tpm.business.activities.contract.vo.ContractCostDetailVo">
        select
        a.contract_code,
        a.contract_name,
        a.contract_status,
        a.contract_type,
        a.is_replenish,
        a.original_contract_code,
        a.customer_code,
        a.customer_name,
        a.erp_code,
        a.company_code,
        a.product_group_code,
        a.channel_code,
        a.start_date contractStartDate,
        a.end_date contractEndDate,
        a.only_key,
        b.*
        from tpm_external_contract a
        left join tpm_external_contract_detail b on a.only_key = b.only_key
        <where>
            a.del_flag = '${@<EMAIL>()}'
            and a.tenant_code = #{tenantCode}
            and a.external_flag = 'N'
            and b.enable_status = '${@<EMAIL>()}'
            <![CDATA[and DATE_FORMAT(a.start_date,'%Y-%m') <= #{years}]]>
            <![CDATA[and DATE_FORMAT(a.end_date,'%Y-%m') >= #{years}]]>
            and not exists (
            select 1 from tpm_external_contract_used_log c where b.only_key = c.index_key
            and c.years = #{years}
            )
        </where>
    </select>
    <select id="findByConditions"
            resultType="com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo">
        select * from tpm_external_contract
        where del_flag = '${@<EMAIL>()}'
        and tenant_code = #{dto.tenantCode}
        <if test="dto.contractCode != null and dto.contractCode != ''">
            and contract_code = #{dto.contractCode}
        </if>
        <if test="dto.contractName != null and dto.contractName != ''">
            <bind name="likeContractName" value="dto.contractName + '%'"/>
            and contract_name like #{likeContractName}
        </if>
        <if test="dto.contractType != null and dto.contractType != ''">
            and contract_type = #{dto.contractType}
        </if>
        <if test="dto.isReplenish != null and dto.isReplenish != ''">
            and is_replenish = #{dto.isReplenish}
        </if>
        <if test="dto.originalContractCode != null and dto.originalContractCode != ''">
            and original_contract_code = #{dto.originalContractCode}
        </if>
        <if test="dto.companyCode != null and dto.companyCode != ''">
            and company_code = #{dto.companyCode}
        </if>
        <if test="dto.erpCode != null and dto.erpCode != ''">
            and erp_code = #{dto.erpCode}
        </if>
        <if test="dto.customerCode != null and dto.customerCode != ''">
            and customer_code = #{dto.customerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="likeCustomerName" value="dto.customerName + '%'"/>
            and customer_name like #{likeCustomerName}
        </if>
        <if test="dto.externalFlag != null and dto.externalFlag != ''">
            and external_flag = #{dto.externalFlag}
        </if>
    </select>


    <select id="findContractNearDate" resultType="com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo">
        SELECT
        *,
        DATEDIFF(end_date, DATE_FORMAT(NOW(), '%Y-%m-%d')) day
        FROM
        tpm_external_contract
        WHERE
        end_date IS NOT NULL
        AND end_date != ''
        and (push_sfa_flag = 'N' or push_sfa_flag is null)
        AND external_flag = 'Y'
        <![CDATA[AND DATEDIFF(end_date, DATE_FORMAT(NOW(), '%Y-%m-%d')) <= #{day}]]>
    </select>
</mapper>

