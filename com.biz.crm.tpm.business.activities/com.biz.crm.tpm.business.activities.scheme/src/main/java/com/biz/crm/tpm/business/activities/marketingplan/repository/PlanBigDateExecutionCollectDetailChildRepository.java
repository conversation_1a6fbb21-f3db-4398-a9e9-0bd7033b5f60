package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanBigDateExecutionCollectDetailChild;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanBigDateExecutionCollectDetailChildMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.repository.PlanBigDateExecutionCollectDetailChildRepository
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-31 17:07
 */
@Component
@Slf4j
public class PlanBigDateExecutionCollectDetailChildRepository extends ServiceImpl<PlanBigDateExecutionCollectDetailChildMapper, PlanBigDateExecutionCollectDetailChild> {

    public List<PlanBigDateExecutionCollectDetailChild> findByCollectId(String id) {
        return this.lambdaQuery()
                .eq(PlanBigDateExecutionCollectDetailChild::getCollectId, id).list();
    }

    public void deleteByCollectId(String id) {
        this.remove(Wrappers.lambdaQuery(PlanBigDateExecutionCollectDetailChild.class)
                .eq(PlanBigDateExecutionCollectDetailChild::getCollectId, id));
    }

    public List<PlanBigDateExecutionCollectDetailChild> findListByCollectIds(List<String> collectIds) {
        if (CollectionUtils.isEmpty(collectIds)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(PlanBigDateExecutionCollectDetailChild::getCollectId, collectIds)
                .list();
    }
}
