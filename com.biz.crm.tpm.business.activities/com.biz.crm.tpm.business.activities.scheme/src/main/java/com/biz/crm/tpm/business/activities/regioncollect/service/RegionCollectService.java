package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
public interface RegionCollectService {

    Page<RegionCollectVo> findList(Pageable pageable, RegionCollectVo vo);

    RegionCollectVo queryByIdOrCollectCode(String id, String collectCode);

    String createRegionCollect(String orgCode, String years);

    void modifyCollect(String id);

    void modifyOTwoOCollect(String id);

    void marketingChangeUpdateRegionCollect(String orgCode, String years);

    String deleteBatch(List<String> idList);

    Map<String, String> checkControlBudget(String controlFlag, String collectCode);

    Map<String, String> submitByCollectCode(String collectCode, String submitFlag,String remark);

    void updateExecuteMsg(String id, String msg, String status);

    void callback(RegionCollectVo vo);

    void manualApproval(String collectCode);

    /**
     * 流程撤回
     *
     * @param code
     */
    void recover(String code, String remark);

    List<RegionCollectVo> findCommit(String yearMonthLy);

    RegionCollectVo findBySchemeCode(String schemeCode);

    List<MarketingPlanCaseVo> findRegionMarketingCaseList(String collectCode);
}
