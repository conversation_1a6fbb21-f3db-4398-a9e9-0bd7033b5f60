package com.biz.crm.tpm.business.activities.marketingplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingItemShareEntity;
import com.biz.crm.tpm.business.activities.sdk.vo.MarketingItemShareVo;
import org.apache.ibatis.annotations.Param;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/12 17:51
 **/
public interface MarketingItemShareMapper extends BaseMapper<MarketingItemShareEntity> {

    Page<MarketingItemShareVo> findListByCondition(Page<MarketingItemShareVo> page, @Param("vo") MarketingItemShareVo vo);
}
