package com.biz.crm.tpm.business.activities.overallplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_overall_plan_product")
@Table(
        name = "tpm_overall_plan_product",
        indexes = {
                @Index(name = "tpm_overall_plan_product_index0", columnList = "scheme_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_overall_plan_product", comment = "统筹方案产品范围")
@ApiModel(value = "OverallPlanProduct", description = "统筹方案产品范围")
public class OverallPlanProduct extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("编码")
    @Column(name = "code", columnDefinition = "varchar(32) comment '编码'")
    private String code;

    @ApiModelProperty("名称")
    @Column(name = "name", columnDefinition = "varchar(64) comment '名称'")
    private String name;

    @ApiModelProperty("类型")
    @Column(name = "type", columnDefinition = "varchar(20) comment '类型'")
    private String type;
}
