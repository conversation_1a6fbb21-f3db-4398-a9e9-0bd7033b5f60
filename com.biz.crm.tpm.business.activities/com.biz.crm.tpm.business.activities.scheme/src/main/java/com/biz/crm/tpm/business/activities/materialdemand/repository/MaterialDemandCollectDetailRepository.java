package com.biz.crm.tpm.business.activities.materialdemand.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandCollectDetail;
import com.biz.crm.tpm.business.activities.materialdemand.mapper.MaterialDemandCollectDetailMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:35
 */
@Component
public class MaterialDemandCollectDetailRepository extends ServiceImpl<MaterialDemandCollectDetailMapper, MaterialDemandCollectDetail> {

    public List<MaterialDemandCollectDetail> findListByCodes(List<String> codes) {
        return this.lambdaQuery()
                .in(MaterialDemandCollectDetail::getCollectCode, codes)
                .list();
    }

    public void deleteByCodes(List<String> codes) {
        this.lambdaUpdate()
                .in(MaterialDemandCollectDetail::getCollectCode, codes).remove();
    }
}
