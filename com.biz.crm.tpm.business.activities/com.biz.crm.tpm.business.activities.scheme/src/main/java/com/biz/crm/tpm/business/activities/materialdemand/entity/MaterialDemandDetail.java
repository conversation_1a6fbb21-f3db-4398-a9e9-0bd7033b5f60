package com.biz.crm.tpm.business.activities.materialdemand.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_material_demand_detail")
@Table(
        name = "tpm_material_demand_detail",
        indexes = {
                @Index(name = "tpm_material_demand_detail_index0", columnList = "demand_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_material_demand_detail", comment = "物料需求提报明细")
@ApiModel(value = "MaterialDemandDetail", description = "物料需求提报明细")
public class MaterialDemandDetail extends UuidFlagOpEntity {

    @ApiModelProperty("编码")
    @Column(name = "demand_code", columnDefinition = "varchar(32) comment '编码'")
    private String demandCode;

    @ApiModelProperty("明细编码")
    @Column(name = "demand_detail_code", columnDefinition = "varchar(32) comment '明细编码'")
    private String demandDetailCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '使用部门编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(64) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("物料编码")
    @Column(name = "material_code", columnDefinition = "varchar(32) comment '物料编码'")
    private String materialCode;

    @ApiModelProperty("物料名称")
    @Column(name = "material_name", columnDefinition = "varchar(64) comment '物料名称'")
    private String materialName;

    @ApiModelProperty("物料数量")
    @Column(name = "material_num", columnDefinition = "decimal(10) comment '物料数量'")
    private BigDecimal materialNum;

    @ApiModelProperty("物料成本价")
    @Column(name = "material_price", columnDefinition = "decimal(18,4) comment '物料成本价格'")
    private BigDecimal materialPrice;

    @ApiModelProperty("物料价格")
    @Column(name = "material_amount", columnDefinition = "decimal(18,4) comment '物料价格'")
    private BigDecimal materialAmount;

}
