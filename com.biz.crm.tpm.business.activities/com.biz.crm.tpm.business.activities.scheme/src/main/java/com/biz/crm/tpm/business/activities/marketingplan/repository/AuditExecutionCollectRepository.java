package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.AuditExecutionCollect;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.AuditExecutionCollectMapper;
import com.biz.crm.tpm.business.activities.sdk.dto.AuditExecutionCollectVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.repository.AuditExecutionCollectRepository
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-08-02 14:10
 */
@Component
@Slf4j
public class AuditExecutionCollectRepository extends ServiceImpl<AuditExecutionCollectMapper, AuditExecutionCollect> {

    public AuditExecutionCollect findById(String id) {
        return this.lambdaQuery()
                .eq(AuditExecutionCollect::getId, id)
                .one();
    }

    public AuditExecutionCollect findByDto(AuditExecutionCollectVo dto) {
        return this.lambdaQuery()
                .eq(AuditExecutionCollect::getSchemeDetailCode, dto.getSchemeDetailCode())
                .eq(AuditExecutionCollect::getTerminalCode, dto.getTerminalCode())
                .eq(AuditExecutionCollect::getCreatePostCode, dto.getCreatePostCode())
                .eq(AuditExecutionCollect::getExecutionTime, dto.getExecutionTime())
                .eq(AuditExecutionCollect::getDynamicKey, dto.getDynamicKey())
                .eq(AuditExecutionCollect::getDynamicFormCode, dto.getDynamicFormCode())
                .eq(AuditExecutionCollect::getParentCode, dto.getParentCode())
                .one();
    }
}
