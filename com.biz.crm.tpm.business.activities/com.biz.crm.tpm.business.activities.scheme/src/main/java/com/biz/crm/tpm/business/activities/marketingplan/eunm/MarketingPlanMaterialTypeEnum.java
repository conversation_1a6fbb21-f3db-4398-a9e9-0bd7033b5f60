package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.compress.utils.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/3 18:59
 */
@Getter
@AllArgsConstructor
public enum MarketingPlanMaterialTypeEnum {

    not_standard_material("0", "非标准物料"),
    standard_material("1", "标准物料"),
    ;

    private String code;

    private String desc;

    public static List<String> getCodeList() {
        List<String> list = Arrays.stream(MarketingPlanMaterialTypeEnum.values()).map(MarketingPlanMaterialTypeEnum::getCode)
                .collect(Collectors.toList());
        return list;
    }
}
