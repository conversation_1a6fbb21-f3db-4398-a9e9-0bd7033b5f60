package com.biz.crm.tpm.business.activities.stagingscheme.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.stagingscheme.entity.TpmStagingSchemeEntity;
import com.biz.crm.tpm.business.activities.stagingscheme.mapper.TpmStagingSchemeMapper;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.activities.stagingscheme.vo.TpmStagingSchemeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 方案相关暂存表接口实现
 *
 * <AUTHOR>
 * @date 2023-12-14 10:50:31
 */
@Slf4j
@Service
@Transactional
public class TpmStagingSchemeServiceImpl extends ServiceImpl<TpmStagingSchemeMapper, TpmStagingSchemeEntity> implements ITpmStagingSchemeService {

    @Resource
    private TpmStagingSchemeMapper tpmStagingSchemeMapper;

    @Resource
    private NebulaToolkitService nebulaToolkitService;


    /**
     * 修改或保存
     *
     * @param reqVo
     */
    @Override
    public void saveOrUpdateStaging(TpmStagingSchemeVo reqVo) {
        TpmStagingSchemeEntity entity = this.lambdaQuery()
                .eq(TpmStagingSchemeEntity::getReleaseId, reqVo.getReleaseId())
                .eq(TpmStagingSchemeEntity::getFromType, reqVo.getFromType())
                .one();
        if (entity != null) {
            entity.setJsonStr(reqVo.getJsonStr());
        } else {
            entity = new TpmStagingSchemeEntity() {{
                this.setReleaseId(reqVo.getReleaseId());
                this.setFromType(reqVo.getFromType());
                this.setJsonStr(reqVo.getJsonStr());
            }};
        }
        this.saveOrUpdate(entity);
    }


    /**
     * 返回数据类型
     *
     * @param reqVo
     * @param <T>
     * @return
     */
    @Override
    public <T> T getJsonStr(TpmStagingSchemeVo reqVo, Class<T> clazz) {
        TpmStagingSchemeEntity entity = this.lambdaQuery()
                .eq(TpmStagingSchemeEntity::getReleaseId, reqVo.getReleaseId())
                .eq(TpmStagingSchemeEntity::getFromType, reqVo.getFromType())
                .select(TpmStagingSchemeEntity::getJsonStr, TpmStagingSchemeEntity::getId)
                .one();
        if (entity != null) {
            return JSONObject.parseObject(entity.getJsonStr(), clazz);
        } else {
            return null;
        }
    }

    /**
     * 删除暂存数据
     *
     * @param reqVo
     */
    @Override
    public void deleteStaging(TpmStagingSchemeVo reqVo) {
        this.remove(Wrappers.lambdaQuery(TpmStagingSchemeEntity.class)
                .in(TpmStagingSchemeEntity::getReleaseId, reqVo.getReleaseIds())
                .eq(TpmStagingSchemeEntity::getFromType, reqVo.getFromType()));
    }


    @Override
    public <T> List<T> getJSONStrList(TpmStagingSchemeVo reqVo, Class<T> clazz) {
        List<TpmStagingSchemeEntity> entityList = this.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(reqVo.getReleaseIds()), TpmStagingSchemeEntity::getReleaseId, reqVo.getReleaseIds())
                .eq(ObjectUtils.isNotEmpty(reqVo.getFromType()), TpmStagingSchemeEntity::getFromType, reqVo.getFromType())
                .in(CollectionUtils.isNotEmpty(reqVo.getFromTypes()), TpmStagingSchemeEntity::getFromType, reqVo.getFromTypes())
                .select(TpmStagingSchemeEntity::getJsonStr, TpmStagingSchemeEntity::getId, TpmStagingSchemeEntity::getCreateTime)
                .list();
        List<T> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(entityList)) {
            for (TpmStagingSchemeEntity entity : entityList) {
                list.add(JSONObject.parseObject(entity.getJsonStr(), clazz));
            }
            return list;
        } else {
            return Lists.newArrayList();
        }
    }

    @Override
    public List<TpmStagingSchemeEntity> findListByCondition(String fromType, List<String> releaseIds) {
        List<TpmStagingSchemeEntity> entityList = this.lambdaQuery()
                .in(TpmStagingSchemeEntity::getReleaseId, releaseIds)
                .eq(TpmStagingSchemeEntity::getFromType, fromType)
                .list();
        return entityList;
    }
}
