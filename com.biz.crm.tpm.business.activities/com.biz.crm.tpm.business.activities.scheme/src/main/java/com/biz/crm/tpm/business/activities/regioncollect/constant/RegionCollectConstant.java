package com.biz.crm.tpm.business.activities.regioncollect.constant;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.omg.CORBA.OBJ_ADAPTER;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
public class RegionCollectConstant {

    public static final String REGION_COLLECT_LOCK = "tpm:region_collect:lock:";

    public static final String REGION_COLLECT_RULE_CODE = "HZ";

    public static final String REGION_COLLECT_SCHEME_DETAIL_KEY = "tpm:region_collect:control_budget:scheme_detail:";

    public static final String REGION_COLLECT_SCHEME_DATA_KEY = "tpm:region_collect:control_budget:scheme_data:";

    public static final String REGION_COLLECT_SCHEME_ERROR_KEY = "tpm:region_collect:control_budget:scheme_error:";

    public static String getRedisLockKey(String orgCode, String years) {
        String redisLockKey = REGION_COLLECT_LOCK + orgCode + ":" + years;
        return redisLockKey;
    }

    public static String getSubmitRedisLockKey(String collectCode) {
        String redisLockKey = REGION_COLLECT_LOCK + collectCode;
        return redisLockKey;
    }

    public static String getRegionCollectSchemeDetailKey(String businessCode, String businessDetailCode) {
        String redisKey = null;
        Validate.notNull(businessCode, "业务编码不能为空");
        if (ObjectUtils.isNotEmpty(businessDetailCode)) {
            redisKey = REGION_COLLECT_SCHEME_DETAIL_KEY + businessCode + ":" + businessDetailCode;
        } else {
            redisKey = REGION_COLLECT_SCHEME_DETAIL_KEY + businessCode + ":";
        }
        return redisKey;
    }

    public static String getRegionCollectSchemeDataKey(String businessCode, String businessDetailCode) {
        String redisKey = null;
        Validate.notNull(businessCode, "业务编码不能为空");
        if (ObjectUtils.isNotEmpty(businessDetailCode)) {
            redisKey = REGION_COLLECT_SCHEME_DATA_KEY + businessCode + ":" + businessDetailCode;
        } else {
            redisKey = REGION_COLLECT_SCHEME_DATA_KEY + businessCode + ":";
        }
        return redisKey;
    }

    public static String getRegionCollectSchemeErrorKey(String businessCode, String businessDetailCode) {
        String redisKey = null;
        Validate.notNull(businessCode, "业务编码不能为空");
        if (ObjectUtils.isNotEmpty(businessDetailCode)) {
            redisKey = REGION_COLLECT_SCHEME_ERROR_KEY + businessCode + ":" + businessDetailCode;
        } else {
            redisKey = REGION_COLLECT_SCHEME_ERROR_KEY + businessCode + ":";
        }
        return redisKey;
    }

}
