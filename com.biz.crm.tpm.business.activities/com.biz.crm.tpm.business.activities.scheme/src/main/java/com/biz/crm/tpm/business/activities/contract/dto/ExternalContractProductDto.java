package com.biz.crm.tpm.business.activities.contract.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 11:01
 */
@Data
@ApiModel(value = "ExternalContractProductDto", description = "外部合同产品范围")
public class ExternalContractProductDto {

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("合同明细编码")
    private String contractDetailCode;

    @ApiModelProperty("唯一编码")
    private String onlyKey;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("数量")
    private BigDecimal num;

    @ApiModelProperty("类型")
    private String type;
}
