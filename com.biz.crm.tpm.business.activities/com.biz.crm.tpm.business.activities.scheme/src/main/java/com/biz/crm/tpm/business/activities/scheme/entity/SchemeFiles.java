package com.biz.crm.tpm.business.activities.scheme.entity;

import com.biz.crm.business.common.local.entity.FileEntity;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * 实体：方案附件;
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "SchemeFiles",description = "方案附件")
@TableName("tpm_scheme_files")
@Getter
@Setter
@Entity(name = "tpm_scheme_files")
@org.hibernate.annotations.Table(appliesTo = "tpm_scheme_files", comment = "方案附件")
public class SchemeFiles extends FileEntity {

  /** 方案编号 */
  @ApiModelProperty(name = "方案编号",notes = "方案编号")
  @Column(name = "scheme_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '方案编号 '")
  private String schemeCode;
}