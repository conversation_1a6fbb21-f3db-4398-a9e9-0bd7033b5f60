package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 15:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_marketing_plan_case_extend")
@Table(name = "tpm_marketing_plan_case_extend", indexes = {
        @Index(name = "tpm_marketing_plan_case_extend_index0", columnList = "scheme_detail_code", unique = true),
        @Index(name = "tpm_marketing_plan_case_extend_idx1", columnList = "scheme_code", unique = false),
        @Index(name = "tpm_marketing_plan_case_extend_idx2", columnList = "budget_code", unique = false),
})
@org.hibernate.annotations.Table(appliesTo = "tpm_marketing_plan_case_extend", comment = "营销方案-方案明细扩展表")
@ApiModel(value = "MarketingPlanCaseExtend", description = "营销方案-方案明细扩展表")
public class MarketingPlanCaseExtend extends UuidEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("执行描述")
    @Column(name = "execute_desc", columnDefinition = "varchar(500) comment '执行描述'")
    private String executeDesc;

    @ApiModelProperty("预估费用")
    @Column(name = "estimated_cost", columnDefinition = "decimal(18,4) comment '预估费用'")
    private BigDecimal estimatedCost;

    @ApiModelProperty("预估销售额")
    @Column(name = "estimated_sales_volume", columnDefinition = "decimal(18,4) comment '预估销售额'")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("预估费率")
    @Column(name = "ratio", columnDefinition = "decimal(10,2) comment '预估费率'")
    private BigDecimal ratio;

    @ApiModelProperty("活动描述")
    @Column(name = "act_desc", columnDefinition = "varchar(500) comment '活动描述'")
    private String actDesc;

    @ApiModelProperty("年度预算编码")
    @Column(name = "budget_code", columnDefinition = "varchar(32) comment '年度预算编码'")
    private String budgetCode;

    @ApiModelProperty("费用依据")
    @Column(name = "cost_basis", columnDefinition = "varchar(20) comment '费用依据'")
    private String costBasis;

    @ApiModelProperty("执行示例")
    @Column(name = "execute_example", columnDefinition = "varchar(128) comment '执行示例'")
    private String executeExample;

    @ApiModelProperty("结案示例")
    @Column(name = "close_case_example", columnDefinition = "varchar(128) comment '结案示例'")
    private String closeCaseExample;

    @ApiModelProperty("合作类型")
    @Column(name = "cooperate_type_str", columnDefinition = "varchar(200) comment '合作类型'")
    private String cooperateTypeStr;

    @ApiModelProperty("客户标签")
    @Column(name = "customer_tag_str", columnDefinition = "VARCHAR(200) COMMENT '客户标签;数据字典[mdm_customer_tag]'")
    private String customerTagStr;

    @ApiModelProperty("终端标签")
    @Column(name = "terminal_tag_str", columnDefinition = "varchar(200) comment '终端标签'")
    private String terminalTagStr;

    @ApiModelProperty("错误描述")
    @Column(name = "err_msg", columnDefinition = "varchar(500) comment '错误描述'")
    private String errMsg;

    @ApiModelProperty("校验结果")
    @Column(name = "check_flag", columnDefinition = "tinyint comment '校验结果'")
    private Boolean checkFlag;

    @ApiModelProperty("合同编码")
    @Column(name = "contract_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '合同编码 '")
    private String contractCode;

    @ApiModelProperty("合同名称")
    @Column(name = "contract_name", columnDefinition = "VARCHAR(255) COMMENT '合同名称 '")
    private String contractName;

    @ApiModelProperty("是否合同费用")
    @Column(name = "is_contract_cost", columnDefinition = "varchar(10) comment '是否合同费用'")
    private String isContractCost;

    @ApiModelProperty("返利类型")
    @Column(name = "rebate_type", columnDefinition = "varchar(32) comment '返利类型'")
    private String rebateType;

    @ApiModelProperty("返利计算日期")
    @Column(name = "rebate_cal_day", columnDefinition = "int(5) comment '返利计算日期'")
    private Integer rebateCalDay;

    @ApiModelProperty("返利开始时间")
    @Column(name = "rebate_start_date", columnDefinition = "varchar(20) comment '返利开始时间'")
    private String rebateStartDate;

    @ApiModelProperty("返利结束时间")
    @Column(name = "rebate_end_date", columnDefinition = "varchar(20) comment '返利结束时间'")
    private String rebateEndDate;

    @ApiModelProperty("政策形式编码")
    @Column(name = "condition_formula", columnDefinition = "varchar(32) comment '条件公式'")
    private String conditionFormula;

    @ApiModelProperty("政策形式名称")
    @Column(name = "condition_formula_name", columnDefinition = "varchar(128) comment '政策形式名称'")
    private String conditionFormulaName;

    @ApiModelProperty("我方承担金额")
    @Column(name = "bear_amount", columnDefinition = "decimal(18,4) comment '我方承担金额'")
    private BigDecimal bearAmount;

    @ApiModelProperty("客户承担金额")
    @Column(name = "cus_bear_amount", columnDefinition = "decimal(18,4) comment '客户承担金额'")
    private BigDecimal cusBearAmount;

    @ApiModelProperty("行销物料类型")
    @Column(name = "sell_material_type", columnDefinition = "varchar(32) COMMENT '行销物料类型[数据字典:mdm_sell_material_type]'")
    private String sellMaterialType;

    @ApiModelProperty("物料编码")
    @Column(name = "material_code", columnDefinition = "varchar(32) comment '物料编码'")
    private String materialCode;

    @ApiModelProperty("物料名称")
    @Column(name = "material_name", columnDefinition = "varchar(128) comment '物料名称'")
    private String materialName;

    @ApiModelProperty("物料数量")
    @Column(name = "material_num", columnDefinition = "decimal(10) comment '物料数量'")
    private BigDecimal materialNum;

    @ApiModelProperty("物料成本价格")
    @Column(name = "material_cost_price", columnDefinition = "decimal(10,2) COMMENT '物料成本价格'")
    private BigDecimal materialCostPrice;

    @ApiModelProperty("运输方式")
    @Column(name = "transport_type", columnDefinition = "varchar(32) comment '运输方式'")
    private String transportType;

    @ApiModelProperty("投放平台")
    @Column(name = "platform", columnDefinition = "varchar(32) comment '投放平台'")
    private String platform;

    @ApiModelProperty("投放城市")
    @Column(name = "placing_city", columnDefinition = "varchar(32) comment '投放城市'")
    private String placingCity;

    @ApiModelProperty("投放城市名称")
    @Column(name = "placing_city_name", columnDefinition = "varchar(64) comment '投放城市名称'")
    private String placingCityName;

    @ApiModelProperty("条件数量")
    @Column(name = "condition_num", columnDefinition = "varchar(32) comment '条件数量'")
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    @Column(name = "give_num", columnDefinition = "varchar(32) comment '搭赠/优惠数量'")
    private String giveNum;

    @ApiModelProperty("优惠数量-上限")
    @Column(name = "discount_quantity", columnDefinition = "decimal(10) comment '客户优惠数量-上限'")
    private BigDecimal discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    @Column(name = "discount_amount", columnDefinition = "decimal(18,4) comment '客户优惠金额-上限'")
    private BigDecimal discountAmount;

    @ApiModelProperty("税率")
    @Column(name = "tax_rate", columnDefinition = "decimal(10,4) comment '税率'")
    private BigDecimal taxRate;

    @ApiModelProperty("不含税金额")
    @Column(name = "no_tax_apply_amount", columnDefinition = "decimal(18,4) comment '不含税金额'")
    private BigDecimal noTaxApplyAmount;

    @ApiModelProperty("搭赠物料成本金额")
    @Column(name = "policy_material_cost_price", columnDefinition = "decimal(18,4) comment '搭赠物料成本金额'")
    private BigDecimal policyMaterialCostPrice;

    @ApiModelProperty("搭赠数量")
    @Column(name = "gift_quantity", columnDefinition = "decimal(10) comment '搭赠数量'")
    private BigDecimal giftQuantity;

    @ApiModelProperty("政策商品")
    @Column(name = "policy_product_code", columnDefinition = "varchar(32) comment '政策商品'")
    private String policyProductCode;

    @ApiModelProperty("推送状态（政策/费用池）")
    @Column(name = "push_status", columnDefinition = "varchar(32) comment '推送DMS状态（政策/费用池）'")
    private String pushStatus;

    @ApiModelProperty("推送描述（政策/费用池）")
    @Column(name = "push_msg", columnDefinition = "varchar(500) comment '推送DMS描述（政策/费用池）'")
    private String pushMsg;

    @ApiModelProperty("多部门承担标记")
    @Column(name = "much_department_mark", columnDefinition = "varchar(20) comment '多部门标记'")
    private String muchDepartmentMark;

    @ApiModelProperty("是否多部门")
    @Column(name = "much_department_flag", columnDefinition = "varchar(20) comment '是否多部门'")
    private String muchDepartmentFlag;

    @ApiModelProperty("dms单据号（促销政策编码/货补池编码）")
    @Column(name = "dms_code", columnDefinition = "varchar(32) comment 'dms单据号（促销政策编码/货补池编码）'")
    private String dmsCode;

    @ApiModelProperty("是否计提")
    @Column(name = "with_holding_status", columnDefinition = "varchar(32) comment '是否计提'")
    private String withHoldingStatus;

    @ApiModelProperty("完全兑付时间")
    @Column(name = "whole_cash_date", columnDefinition = "varchar(32) comment '完全兑付时间'")
    private String wholeCashDate;

    @Column(name = "cooperate_type", columnDefinition = "varchar(20) comment '合作类型'")
    @ApiModelProperty("合作类型")
    private String cooperateType;

    @Column(name = "hzlx", columnDefinition = "varchar(20) comment '合作类型'")
    @ApiModelProperty("合作类型")
    private String hzlx;

    @ApiModelProperty("陈列卡板数")
    @Column(name = "display_card_num", columnDefinition = "decimal(12,1) comment '陈列卡板数'")
    private BigDecimal displayCardNum;

    @ApiModelProperty("上上月POS")
    @Column(name = "last_up_month_pos", columnDefinition = "decimal(14,2) comment '上上月pos'")
    private BigDecimal lastUpMonthPos;

    @ApiModelProperty("上月POS")
    @Column(name = "last_month_pos", columnDefinition = "decimal(14,2) comment '上月pos'")
    private BigDecimal lastMonthPos;

    @ApiModelProperty("当月pos")
    @Column(name = "month_pos", columnDefinition = "decimal(14,2) comment '当月pos'")
    private BigDecimal monthPos;

    @ApiModelProperty("人员类型")
    @Column(name = "staff_type", columnDefinition = "varchar(20) comment '人员类型'")
    private String staffType;

    @ApiModelProperty("出勤天数")
    @Column(name = "attendance_day", columnDefinition = "decimal(12,1) comment '出勤天数'")
    private BigDecimal attendanceDay;

    @ApiModelProperty("底薪")
    @Column(name = "basic_salary", columnDefinition = "decimal(14,2) comment '底薪'")
    private BigDecimal basicSalary;
}
