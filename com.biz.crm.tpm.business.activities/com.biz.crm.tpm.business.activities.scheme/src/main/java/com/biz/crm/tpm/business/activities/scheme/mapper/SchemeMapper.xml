<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.activities.scheme.mapper.SchemeMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.scheme.entity.Scheme" id="SchemeMap">
  </resultMap>

  <select id="countByCostBudget" resultType="java.lang.Integer">
    select count(*)
    from tpm_scheme t
           left join tpm_scheme_cost_budget ts
                     on ts.scheme_code = t.scheme_code and ts.tenant_code = t.tenant_code
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.costBudgetCode != null and dto.costBudgetCode != ''">
        and ts.cost_budget_code = #{dto.costBudgetCode}
      </if>
      and t.del_flag = '${@<EMAIL>()}'
    </where>
  </select>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.scheme.vo.SchemeVo">
    select distinct t.*
    from tpm_scheme t
           left join tpm_scheme_range tsr on t.tenant_code = tsr.tenant_code and t.scheme_code = tsr.scheme_code
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != ''">
        and t.tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.schemeCode != null and dto.schemeCode != ''">
        <bind name="schemeCode" value="'%' + dto.schemeCode + '%'"/>
        and t.scheme_code like #{schemeCode}
      </if>
      <if test="dto.schemeName != null and dto.schemeName != ''">
        <bind name="schemeName" value="'%' + dto.schemeName + '%'"/>
        and t.scheme_name like #{schemeName}
      </if>
      <if test="dto.schemeType != null and dto.schemeType != ''">
        and t.scheme_type = #{dto.schemeType}
      </if>
      <if test="dto.schemeBeginTime != null">
        <![CDATA[
        and t.scheme_begin_time <= #{dto.schemeBeginTime}
        ]]>
      </if>
      <if test="dto.schemeEndTime != null">
        <![CDATA[
        and t.scheme_end_time >= #{dto.schemeEndTime}
        ]]>
      </if>
      <if test="dto.schemeStatus != null and dto.schemeStatus != ''">
        and t.scheme_status = #{dto.schemeStatus}
      </if>
      <if test="dto.schemeStatuses != null and dto.schemeStatuses.size > 0">
        and t.scheme_status in
        <foreach collection="dto.schemeStatuses" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="dto.orgTypes != null and dto.orgTypes.size > 0 and dto.orgCodes != null and dto.orgCodes.size > 0">
        and ((tsr.range_type = 2
        and tsr.range_code in
        <foreach collection="dto.orgTypes" item="item" open="(" separator="," close=")">
          #{
        item}
        </foreach>
        ) or
        ( tsr.range_type = 1
          and tsr.range_code in
          <foreach collection="dto.orgCodes" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
        ))
      </if>
      <if test="dto.orgTypes != null and dto.orgTypes.size > 0 and dto.orgCodes == null">
        and tsr.range_type = 2
        and tsr.range_code in
        <foreach collection="dto.orgTypes" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="dto.orgCodes != null and dto.orgCodes.size > 0 and dto.orgTypes == null">
        and tsr.range_type = 1
        and tsr.range_code in
        <foreach collection="dto.orgCodes" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and t.del_flag = '${@<EMAIL>()}'
    </where>
    order by
    <if test="dto.selectedCodeList != null and dto.selectedCodeList.size > 0">
      CASE
      <foreach collection="dto.selectedCodeList" item="item" index="index">
        WHEN t.scheme_code = #{item} THEN ${index}
      </foreach>
      ELSE 99 END asc,
    </if>
    t.create_time desc, t.id
  </select>
</mapper>