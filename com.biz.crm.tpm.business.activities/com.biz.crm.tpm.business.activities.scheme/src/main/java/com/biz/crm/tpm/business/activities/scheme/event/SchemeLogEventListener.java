package com.biz.crm.tpm.business.activities.scheme.event;

import com.biz.crm.tpm.business.activities.scheme.dto.SchemeLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 方案 业务日志监听
 * <AUTHOR> <PERSON>
 * @date : 2022-7-1
 */
public interface SchemeLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(SchemeLogEventDto eventDto);
  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(SchemeLogEventDto eventDto);
  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(SchemeLogEventDto eventDto);
}
