package com.biz.crm.tpm.business.activities.scheme.service;

import com.biz.crm.tpm.business.activities.scheme.dto.ActivitiesSchemeFilesDto;
import com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeFilesVo;

import java.util.List;

/**
 * 方案活动附件;(tpm_activities_scheme_files)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-6-20
 */
public interface ActivitiesSchemeFilesService {
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  ActivitiesSchemeFilesVo findById(String id);

  /**
   * 通过活动编号查询单条数据
   *
   * @param activitiesCode 编号
   * @return 单条数据
   */
  List<ActivitiesSchemeFilesVo> findByActivitiesCode(String activitiesCode);

  /**
   * 新增数据
   *
   * @param activitiesSchemeFilesDto 实体对象
   * @return 新增结果
   */
  ActivitiesSchemeFilesVo create(ActivitiesSchemeFilesDto activitiesSchemeFilesDto);

  /**
   * 新增数据
   *
   * @param activitiesSchemeFilesDtos 实体对象
   * @return 新增结果
   */
  List<ActivitiesSchemeFilesVo> createBatch(List<ActivitiesSchemeFilesDto> activitiesSchemeFilesDtos);

  /**
   * 删除数据
   *
   * @param ids 主键结合
   */
  void delete(List<String> ids);

  /**
   * 根据活动编号删除数据
   *
   * @param activitiesCode
   */
  void deleteByActivitiesCode(String activitiesCode);
}
