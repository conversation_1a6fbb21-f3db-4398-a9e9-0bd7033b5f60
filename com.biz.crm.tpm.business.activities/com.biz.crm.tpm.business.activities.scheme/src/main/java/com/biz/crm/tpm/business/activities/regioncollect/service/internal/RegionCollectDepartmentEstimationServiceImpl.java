package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectDepartmentEstimationRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectDepartmentEstimationService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectDepartmentEstimationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Service("regionCollectDepartmentEstimationService")
@Slf4j
public class RegionCollectDepartmentEstimationServiceImpl implements RegionCollectDepartmentEstimationService {

    @Resource
    private RegionCollectDepartmentEstimationRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public List<RegionCollectDepartmentEstimationVo> findByCollectCode(String collectCode) {
        List<RegionCollectDepartmentEstimation> list = repository.findByCollectCode(collectCode);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        for (RegionCollectDepartmentEstimation data : list) {
            if (ObjectUtils.isNotEmpty(data.getSecondCostCategoryJsonStr())) {
                data.setSecondCostCategoryMap(JSONObject.parseObject(data.getSecondCostCategoryJsonStr(), LinkedHashMap.class));
            }
        }
        return (List<RegionCollectDepartmentEstimationVo>) nebulaToolkitService.copyCollectionByWhiteList(list, RegionCollectDepartmentEstimation.class,
                RegionCollectDepartmentEstimationVo.class, HashSet.class, ArrayList.class, "secondCostCategoryMap");
    }

    @Override
    public void deleteByCollectCodes(List<String> collectCodes) {
        repository.deleteByCollectCodes(collectCodes);
    }

    /**
     * 保存数据
     *
     * @param list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveList(List<RegionCollectDepartmentEstimationVo> list, String collectCode) {
        repository.deleteByCollectCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<RegionCollectDepartmentEstimation> dataList = list.stream().map(x -> {
            RegionCollectDepartmentEstimation data = nebulaToolkitService.copyObjectByWhiteList(x, RegionCollectDepartmentEstimation.class, HashSet.class, ArrayList.class);
            if (!CollectionUtils.isEmpty(data.getSecondCostCategoryMap())) {
                data.setSecondCostCategoryJsonStr(JSONObject.toJSONString(data.getSecondCostCategoryMap()));
            }
            data.setId(null);
            data.setCollectCode(collectCode);
            return data;
        }).collect(Collectors.toList());
        repository.saveBatch(dataList);
    }
}
