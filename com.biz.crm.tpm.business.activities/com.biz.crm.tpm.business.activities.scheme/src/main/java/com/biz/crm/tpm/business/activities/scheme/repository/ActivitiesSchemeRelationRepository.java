package com.biz.crm.tpm.business.activities.scheme.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeDetailRelation;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeRelation;
import com.biz.crm.tpm.business.activities.scheme.mapper.ActivitiesSchemeRelationMapper;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public class ActivitiesSchemeRelationRepository extends ServiceImpl<ActivitiesSchemeRelationMapper, ActivitiesSchemeRelation> {

  public List<ActivitiesSchemeRelation> findByActivityCodeAndTenantCode(String activityCode, String tenantCode) {
    return this.lambdaQuery()
            .eq(ActivitiesSchemeRelation::getTenantCode, tenantCode)
            .eq(ActivitiesSchemeRelation::getActivityCode, activityCode)
            .list();
  }

  public List<ActivitiesSchemeRelation> findByActivityCodesAndTenantCode(Set<String> activityCodes, String tenantCode) {
    return this.lambdaQuery()
            .eq(ActivitiesSchemeRelation::getTenantCode, tenantCode)
            .in(ActivitiesSchemeRelation::getActivityCode, activityCodes)
            .list();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ActivitiesSchemeRelation::getTenantCode,tenantCode)
        .in(ActivitiesSchemeRelation::getId,ids)
        .remove();
  }
}
