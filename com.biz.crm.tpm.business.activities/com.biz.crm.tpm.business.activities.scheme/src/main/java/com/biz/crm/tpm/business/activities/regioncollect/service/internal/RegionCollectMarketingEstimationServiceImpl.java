package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectMarketingEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectMarketingEstimationRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectMarketingEstimationService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Slf4j
@Service("regionCollectMarketingEstimationService")
public class RegionCollectMarketingEstimationServiceImpl implements RegionCollectMarketingEstimationService {

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private RegionCollectMarketingEstimationRepository repository;

    @Override
    public List<RegionCollectMarketingEstimationVo> findByCollectCode(String collectCode) {
        List<RegionCollectMarketingEstimation> list = repository.findByCollectCode(collectCode);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<RegionCollectMarketingEstimationVo> result = (List<RegionCollectMarketingEstimationVo>) nebulaToolkitService.copyCollectionByWhiteList(list, RegionCollectMarketingEstimation.class, RegionCollectMarketingEstimationVo.class,
                HashSet.class, ArrayList.class);
        return result;
    }

    @Override
    public Map<String, List<RegionCollectMarketingEstimationVo>> findByCollectCodes(List<String> collectCodes) {
        List<RegionCollectMarketingEstimation> list = repository.findByCollectCodes(collectCodes);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Collection<RegionCollectMarketingEstimationVo> voList = nebulaToolkitService.copyCollectionByWhiteList(list, RegionCollectMarketingEstimation.class, RegionCollectMarketingEstimationVo.class,
                HashSet.class, ArrayList.class);
        return voList.stream().collect(Collectors.groupingBy(RegionCollectMarketingEstimationVo::getCollectCode));
    }

    @Override
    public void deleteByCollectCodes(List<String> collectCodes) {
        repository.deleteByCollectCodes(collectCodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveList(List<RegionCollectMarketingEstimationVo> list, String collectCode) {
        repository.deleteByCollectCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<RegionCollectMarketingEstimation> dataList = list.stream().map(x -> {
            RegionCollectMarketingEstimation data = nebulaToolkitService.copyObjectByWhiteList(x, RegionCollectMarketingEstimation.class, HashSet.class, ArrayList.class);
            data.setId(null);
            data.setCollectCode(collectCode);
            return data;
        }).collect(Collectors.toList());
        repository.saveBatch(dataList);
    }
}
