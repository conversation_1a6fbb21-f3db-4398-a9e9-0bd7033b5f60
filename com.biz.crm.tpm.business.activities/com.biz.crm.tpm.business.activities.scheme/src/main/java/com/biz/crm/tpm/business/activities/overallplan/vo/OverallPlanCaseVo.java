package com.biz.crm.tpm.business.activities.overallplan.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanScopeTypeEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import liquibase.pro.packaged.Q;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@ApiModel("统筹方案明细VO")
@AllArgsConstructor
@NoArgsConstructor
public class OverallPlanCaseVo extends UuidFlagOpVo {

    @ApiModelProperty("活动状态")
    private String actStatus;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案主题")
    private String schemeTheme;

    @ApiModelProperty("方案类型")
    private String schemeType;

    @ApiModelProperty("方案说明")
    private String schemeDesc;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("关联总部编码")
    private String headSchemeCode;

    private String headSchemeName;

    @ApiModelProperty("关联总部明细编码")
    private String headSchemeDetailCode;

    @ApiModelProperty("二级费用大类编码")
    private String secondCostCategory;

    @ApiModelProperty("二级费用大类名称")
    private String secondCostCategoryName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("是否可以承接")
    private String bearFlag;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("归属年月")
    private String years;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("承接销售部门拼接字段")
    private String bearDepartmentNameStr;

    @ApiModelProperty("承接类型")
    private String bearType;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("合作类型")
    private String cooperateTypeStr;

    @ApiModelProperty("合作类型标签")
    private List<String> cooperateTypeList;

    @ApiModelProperty("客户标签")
    private String customerTagStr;

    @ApiModelProperty("客户标签")
    private List<String> customerTagList;

    @ApiModelProperty("终端标签")
    private String terminalTagStr;

    @ApiModelProperty("终端标签")
    private List<String> terminalTagList;

    @ApiModelProperty("预估费用")
    private BigDecimal applyAmount;
    private BigDecimal estimatedCost;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("兑付条件及说明")
    private String cashCondition;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("销售部门列表")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanDepartmentVo> bearDepartmentList;

    @ApiModelProperty("产品范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProductVo> productAndItemList;

    @ApiModelProperty("客户、终端")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScopeVo> scopeList;

    @ApiModelProperty("品项范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProductVo> itemList;

    @ApiModelProperty("产品范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProductVo> productList;

    @ApiModelProperty("客户")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScopeVo> customerList;

    @ApiModelProperty("客户名称")
    private String customerNameStr;

    @ApiModelProperty("终端")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScopeVo> terminalList;

    @ApiModelProperty("终端名称")
    private String terminalNameStr;

    @ApiModelProperty("错误描述")
    private String errMsg;

    @ApiModelProperty("校验结果")
    private Boolean checkFlag;

    @ApiModelProperty("排序")
    private Long sort;

    @ApiModelProperty("是否选中状态")
    private String checked;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("承接方案")
    private OverallPlanCaseVo bearCase;

    private Set<String> excludeSchemeDetailCodes;

    private Set<String> includeCategoryCodeSet;

    @ApiModelProperty("可承接金额")
    private BigDecimal undertakeAmount;

    @ApiModelProperty("是否完全承接")
    private String undertakeCompleteFlag;

    @ApiModelProperty("兑付方式")
    private Set<String> payBys;

    @ApiModelProperty("渠道类型")
    private List<String> channelTypeList;

    @ApiModelProperty("渠道类型")
    private String channelTypeStr;

    @ApiModelProperty("渠道类型名称")
    private String channelTypeNameStr;

    @ApiModelProperty("关闭编码")
    private String closeCode;

    @ApiModelProperty("关闭审批状态")
    private String closeProcessStatus;

    @ApiModelProperty("承接销售部门")
    private String bearDepartmentStr;


    public void setProductAndItemList(List<OverallPlanProductVo> productAndItemList) {
        if (CollectionUtils.isNotEmpty(productAndItemList)) {
            this.productAndItemList = productAndItemList;
            //设置商品
            List<OverallPlanProductVo> products = productAndItemList.stream().filter(x -> OverallPlanProductEnum.cal_product.getCode().equals(x.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(products)) {
                this.productList = products;
            }
            //设置品项
            List<OverallPlanProductVo> itemList = productAndItemList.stream().filter(x -> OverallPlanProductEnum.cal_item.getCode().equals(x.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemList)) {
                this.itemList = itemList;
                this.itemCode = itemList.stream().filter(k -> StringUtils.isNotEmpty(k.getCode())).map(OverallPlanProductVo::getCode).distinct().collect(Collectors.joining("/"));
                this.itemName = itemList.stream().filter(k -> StringUtils.isNotEmpty(k.getName())).map(OverallPlanProductVo::getName).distinct().collect(Collectors.joining("/"));
            }
        }
    }

    public List<OverallPlanProductVo> getProductAndItemList() {
        List<OverallPlanProductVo> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(this.productList)) {
            list.addAll(this.productList);
        }
        if (CollectionUtils.isNotEmpty(this.itemList)) {
            list.addAll(this.itemList);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            this.productAndItemList = list;
        }
        return this.productAndItemList;
    }

    public void setScopeList(List<OverallPlanScopeVo> scopeList) {
        if (CollectionUtils.isNotEmpty(scopeList)) {
            this.scopeList = scopeList;
            List<OverallPlanScopeVo> customerList = scopeList.stream().filter(x -> OverallPlanScopeTypeEnum.cus.getCode().equals(x.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customerList)) {
                this.customerList = customerList;
                this.customerNameStr = customerList.stream().map(x->x.getCustomerName()).collect(Collectors.joining(","));
            }
            List<OverallPlanScopeVo> terminalList = this.scopeList.stream().filter(x -> OverallPlanScopeTypeEnum.terminal.getCode().equals(x.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(terminalList)) {
                this.terminalList = terminalList;
                this.terminalNameStr = terminalList.stream().map(x->x.getTerminalName()).collect(Collectors.joining(","));
            }
        }
    }

    public List<OverallPlanScopeVo> getScopeList() {
        List<OverallPlanScopeVo> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(this.customerList)) {
            list.addAll(this.customerList);
        }
        if (CollectionUtils.isNotEmpty(this.terminalList)) {
            list.addAll(this.terminalList);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            this.scopeList = list;
        }
        return this.scopeList;
    }
}
