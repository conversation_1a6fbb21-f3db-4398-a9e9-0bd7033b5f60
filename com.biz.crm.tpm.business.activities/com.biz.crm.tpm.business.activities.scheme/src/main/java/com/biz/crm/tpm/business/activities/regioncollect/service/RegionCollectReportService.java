package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectGainsAndLosses;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectItemEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectScheme;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/16 14:20
 */
public interface RegionCollectReportService {

    Page<RegionCollectDepartmentEstimation> findDepartmentReport(Pageable pageable, RegionCollectDepartmentEstimation vo);


    Page<RegionCollectGainsAndLosses> findGainsAndLossesReport(Pageable pageable, RegionCollectGainsAndLosses vo);


    Page<RegionCollectItemEstimation> findItemReport(Pageable pageable, RegionCollectItemEstimation vo);


    Map<String, BigDecimal> findDepartmentReportTotal(RegionCollectDepartmentEstimation vo);


    Map<String, BigDecimal> findGainsAndLossesReportTotal(RegionCollectGainsAndLosses vo);


    Map<String, BigDecimal> findItemReportTotal(RegionCollectItemEstimation vo);

    Page<RegionCollectScheme> findSchemeReport(Pageable pageable, RegionCollectScheme vo);

    Page<MarketingPlanCaseVo> findRegionMarketingCaseReport(Pageable pageable, MarketingPlanCaseVo caseVo,String collectCode);

    Page<MarketingSalesPlanVo> findRegionMarketingSalesPlanList(Pageable pageable, String collectCode);
}
