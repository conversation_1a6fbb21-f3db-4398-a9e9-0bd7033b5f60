<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanGainsAndLossesMapper">


    <select id="findList" resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanGainsAndLosses">
        select * from tpm_marketing_plan_gains_and_losses
        <where>
            1=1
            <if test="vo.schemeCode != null and vo.schemeCode != ''">
                and scheme_code = #{vo.schemeCode}
            </if>
            order by customer_code desc
        </where>
    </select>

</mapper>

