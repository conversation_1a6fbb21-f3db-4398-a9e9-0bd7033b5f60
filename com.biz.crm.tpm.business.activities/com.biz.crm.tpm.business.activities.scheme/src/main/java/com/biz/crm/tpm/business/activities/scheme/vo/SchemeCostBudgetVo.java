package com.biz.crm.tpm.business.activities.scheme.vo;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Vo：方案预算成本;
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "SchemeCostBudget",description = "方案预算成本")
@Getter
@Setter
public class SchemeCostBudgetVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
  private String tenantCode;
  /** 费用预算编号 */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编号", value= "费用预算编号")
  private String costBudgetCode;
  /** 方案编号 */
  @ApiModelProperty(name = "schemeCode",notes = "方案编号", value= "方案编号")
  private String schemeCode;
}