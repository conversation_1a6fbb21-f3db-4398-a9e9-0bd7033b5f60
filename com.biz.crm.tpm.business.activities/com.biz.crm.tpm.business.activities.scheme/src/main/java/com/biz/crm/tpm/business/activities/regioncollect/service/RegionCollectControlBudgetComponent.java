package com.biz.crm.tpm.business.activities.regioncollect.service;

import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanProduct;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.regioncollect.constant.RegionCollectConstant;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectBudgetVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlVoService;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import com.biz.crm.tpm.business.track.sdk.service.BudgetTrackService;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/5 22:18
 */
@Component
@Slf4j
public class RegionCollectControlBudgetComponent {


    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Resource
    private BudgetTrackService budgetTrackService;

    @Resource
    private BudgetSubjectsVoService budgetSubjectsVoService;

    @Resource
    private BudgetControlVoService budgetControlVoService;

    @Resource
    private RegionCollectBudgetService regionCollectBudgetService;

    @Resource
    private PlanCaseMatchControlBudgetComponent matchControlBudgetComponent;


    /**
     * 异步计算
     *
     * @param code
     * @return
     */
    @Async("tpmRegionCollectThread")
    public Future<List<BudgetTrackVo>> syncCalBudgetControl(String collectCode, String code, Map<String,
            Map<String, List<OrgVo>>> controlBudgetOrgMap, FacturerUserDetails loginUserDetails) {
        loginUserService.refreshAuthentication(loginUserDetails);
        List<String> codes = Lists.newArrayList(code);
        budgetTrackService.generateBudgetTrackByCodes(codes);
        List<BudgetTrackVo> budgetTrackVos = budgetTrackService.findByBudgetControlCodes(codes);
        List<String> orgCodes = budgetTrackVos.stream().map(BudgetTrackVo::getDepartmentOneCode).collect(Collectors.toList());
        Set<String> orgCodeSet = Sets.newHashSet();
        for (String orgCode : orgCodes) {
            orgCodeSet.addAll(Arrays.asList(orgCode.split(",")));
        }
        Map<String, List<OrgVo>> orgMap = orgVoService.findAllChildrenByOrgCodesMap(Lists.newArrayList(orgCodeSet));
        controlBudgetOrgMap.put(code, orgMap);
        return new AsyncResult<>(budgetTrackVos);
    }


    @Resource
    private OrgVoService orgVoService;

    @Async("tpmRegionCollectThread")
    public Future<List<RegionCollectBudgetVo>> matchControlBudgetByCase(List<BudgetTrackVo> budgetTrackVos, MarketingPlanCase caseVo, String collectCode,
                                                                        Map<String, Map<String, List<OrgVo>>> controlBudgetOrgMap, FacturerUserDetails loginUserDetails) {
        loginUserService.refreshAuthentication(loginUserDetails);
        //查询管控规则
        List<String> controlCodes = budgetTrackVos.stream().map(BudgetTrackVo::getBudgetControlCode).collect(Collectors.toList());
        List<BudgetControlVo> controlVos = budgetControlVoService.findBudgetControlByCodes(controlCodes);
        Map<String, String> budgetControlMap = controlVos.stream().collect(Collectors.toMap(x -> x.getControlCode(), x -> x.getControlForm()));
        Map<String, List<OrgVo>> orgMap = Maps.newHashMap();
        for (String controlCode : controlCodes) {
            orgMap.putAll(controlBudgetOrgMap.get(controlCode));
        }
        Set<String> budgetSubjectCodeSet = budgetSubjectsVoService.findAllParentSubjectCodesByCodes(caseVo.getBudgetSubjectCode());
        //匹配最实际的管控规则
        List<String> productCodes = Lists.newArrayList();
        List<String> itemCodes = Lists.newArrayList();
        String customerCode = caseVo.getCustomerCode();
        if (!CollectionUtils.isEmpty(caseVo.getProductList())) {
            productCodes = caseVo.getProductList().stream().map(MarketingPlanProduct::getCode).collect(Collectors.toList());
        }
        if (MarketingPlanCaseTypeEnum.back.getCode().equals(caseVo.getCaseType())) {
            if (!CollectionUtils.isEmpty(caseVo.getFeeBelongItemList())) {
                itemCodes = caseVo.getFeeBelongItemList().stream().map(MarketingPlanProduct::getCode).collect(Collectors.toList());
            }
        } else {
            if (!CollectionUtils.isEmpty(caseVo.getItemList())) {
                itemCodes = caseVo.getItemList().stream().map(MarketingPlanProduct::getCode).collect(Collectors.toList());
            }
        }

        List<BudgetTrackVo> filterList = Lists.newArrayList();
        for (BudgetTrackVo x : budgetTrackVos) {
            List<String> orgCodes = Arrays.asList(x.getDepartmentOneCode().split(","));
            List<OrgVo> orgVoList = Lists.newArrayList();
            for (String s : orgCodes) {
                if (orgMap.containsKey(s)) {
                    orgVoList.addAll(orgMap.get(s));
                }
            }
            List<String> commonElements = Lists.newArrayList();
            //找出重复的数据
            if (ObjectUtils.isNotEmpty(x.getBudgetSubjectCode())) {
                List<String> budgetSubjectCodeList = Arrays.asList(x.getBudgetSubjectCode().split(","));
                for (String s : budgetSubjectCodeList) {
                    if (budgetSubjectCodeSet.contains(s)) {
                        commonElements.add(s);
                    }
                }
            }

            Set<String> orgCodeSet = orgVoList.stream().map(l -> l.getOrgCode()).collect(Collectors.toSet());
            if (orgCodeSet.contains(caseVo.getBearDepartmentCode()) && caseVo.getYears().equals(x.getYearMonthLy()) &&
                    //判断科目 可以为空 但是如果有值 则必须匹配上
                    (ObjectUtils.isEmpty(x.getBudgetSubjectCode()) || (commonElements.size() > 0))
                    //判断客户 可以为空 如果有值 则必须匹配上
                    && (ObjectUtils.isEmpty(x.getCustomerCode()) || customerCode.equals(x.getCustomerCode())) &&
                    //判断产品 可以为空 如果有值 则必须匹配上
                    (ObjectUtils.isEmpty(x.getProductCode()) || (!CollectionUtils.isEmpty(productCodes) && productCodes.contains(x.getProductCode()))) &&
                    //判断品项 可以为空 如果有值 则必须匹配上
                    (ObjectUtils.isEmpty(x.getItemCode()) || (!CollectionUtils.isEmpty(itemCodes) && itemCodes.contains(x.getItemCode())))) {
                filterList.add(x);
            }
        }


        Validate.isTrue(!CollectionUtils.isEmpty(filterList), String.format("方案编码%s明细编码%s年月+部门+科目未找到对应的预算管控规则", caseVo.getSchemeCode(), caseVo.getSchemeDetailCode()));

        List<RegionCollectBudgetVo> list = Lists.newArrayList();
        for (BudgetTrackVo trackVo : filterList) {
            RegionCollectBudgetVo vo = new RegionCollectBudgetVo();
            vo.setApplyAmount(caseVo.getApplyAmount());
            vo.setSchemeCode(caseVo.getSchemeCode());
            vo.setSchemeDetailCode(caseVo.getSchemeDetailCode());
            vo.setYears(trackVo.getYearMonthLy());
            vo.setDepartmentCode(trackVo.getDepartmentOneCode());
            vo.setDepartmentName(trackVo.getDepartmentOneName());
            vo.setCustomerCode(trackVo.getCustomerCode());
            vo.setCustomerName(trackVo.getCustomerName());
            vo.setItemCode(trackVo.getItemCode());
            vo.setItemName(trackVo.getItemName());
            vo.setProductCode(trackVo.getProductCode());
            vo.setProductName(trackVo.getProductName());
            vo.setSubjectCode(trackVo.getBudgetSubjectCode());
            vo.setSubjectName(trackVo.getBudgetSubjectName());
            vo.setSurplusAmount(trackVo.getMonthBalanceAmount());
            vo.setUsedAmount(caseVo.getApplyAmount());
            vo.setControlCode(trackVo.getBudgetControlCode());
            vo.setControlForm(budgetControlMap.get(trackVo.getBudgetControlCode()));
            vo.setTrackCode(trackVo.getTrackCode());
            list.add(vo);
        }

        if (ObjectUtils.isNotEmpty(collectCode) && collectCode.contains(RegionCollectConstant.REGION_COLLECT_RULE_CODE)) {
            regionCollectBudgetService.saveBatchList(list, collectCode);
        }
        return new AsyncResult<>(list);
    }


    /**
     * 重新获取管控预算
     *
     * @return
     */
    public List<RegionCollectBudgetVo> reloadRegionCollectControlBudget(List<MarketingPlanCase> caseList, String collectCode) {
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);

        List<RegionCollectBudgetVo> list = Lists.newArrayList();
        caseList.forEach(x -> x.setSchemeDetailCode(ObjectUtils.defaultIfNull(x.getSchemeDetailCode(), UuidCrmUtil.general())));
        Map<String, MarketingPlanCase> caseMap = caseList.stream().collect(Collectors.toMap(MarketingPlanCase::getSchemeDetailCode, Function.identity()));
        //先删除 在进行匹配
        regionCollectBudgetService.deleteByCollectCode(collectCode);
        Map<String, List<BudgetTrackVo>> planCaseControlMap = Maps.newHashMap();
        Map<String, Map<String, List<OrgVo>>> controlBudgetOrgMap = Maps.newHashMap();
        try {
            //先匹配规则 然后单独分开跑
            Map<String, List<String>> planCaseControlCodeMap = Maps.newHashMap();
            //先跑管控规则
            List<BudgetTrackVo> budgetTrackVoList = runControlBudget(caseList, planCaseControlCodeMap, collectCode, controlBudgetOrgMap, loginDetails);
            //匹配管控规则
            this.runCaseMatchControlBudget(budgetTrackVoList, planCaseControlCodeMap, planCaseControlMap, caseMap,
                    list, collectCode, controlBudgetOrgMap, loginDetails);
        } catch (Exception e) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            e.printStackTrace(new PrintStream(baos));
            String exception = baos.toString();
            log.error("大区汇总执行失败错误日志:{},错误堆栈:{}", e.getMessage(), exception);
            throw new RuntimeException("匹配管规则及计算预算费用失败,失败原因:" + e.getMessage());
        }

        return list;
    }


    /**
     * 执行预算管控规则
     *
     * @param caseList
     * @param planCaseControlCodeMap
     * @param collectCode
     * @return
     */
    public List<BudgetTrackVo> runControlBudget(List<MarketingPlanCase> caseList, Map<String, List<String>> planCaseControlCodeMap,
                                                String collectCode, Map<String, Map<String, List<OrgVo>>> controlBudgetOrgMap, FacturerUserDetails loginDetails) {
        List<BudgetTrackVo> budgetTrackVoList = Lists.newArrayList();
        for (MarketingPlanCase planCase : caseList) {
            List<String> controlCodes = matchControlBudgetComponent.matchControlBudgetByPlanCase(planCase);
            planCaseControlCodeMap.put(planCase.getSchemeDetailCode(), controlCodes);
        }
        Set<String> controlCodeSet = Sets.newHashSet();
        Set<String> originalControlCodeSet = Sets.newHashSet();
        for (Map.Entry<String, List<String>> entry : planCaseControlCodeMap.entrySet()) {
            controlCodeSet.addAll(entry.getValue());
            originalControlCodeSet.addAll(entry.getValue());
        }
        List<Future<List<BudgetTrackVo>>> futureBudgetTrackList = Lists.newArrayList();
        for (String s : controlCodeSet) {
            RegionCollectControlBudgetComponent bean = ApplicationContextHolder.getContext().getBean(RegionCollectControlBudgetComponent.class);
            Future<List<BudgetTrackVo>> future = bean.syncCalBudgetControl(collectCode, s, controlBudgetOrgMap, loginDetails);
            futureBudgetTrackList.add(future);
        }
        for (Future<List<BudgetTrackVo>> future : futureBudgetTrackList) {
            try {
                budgetTrackVoList.addAll(future.get());
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
        }
        return budgetTrackVoList;
    }


    /**
     * 匹配管控规则
     *
     * @param budgetTrackVoList
     * @param planCaseControlCodeMap
     * @param planCaseControlMap
     * @param caseMap
     * @param list
     * @param collectCode
     */
    public void runCaseMatchControlBudget(List<BudgetTrackVo> budgetTrackVoList, Map<String, List<String>> planCaseControlCodeMap, Map<String, List<BudgetTrackVo>> planCaseControlMap,
                                          Map<String, MarketingPlanCase> caseMap, List<RegionCollectBudgetVo> list,
                                          String collectCode, Map<String, Map<String, List<OrgVo>>> controlBudgetOrgMap, FacturerUserDetails loginDetails) {

        Map<String, List<BudgetTrackVo>> map = budgetTrackVoList.stream().collect(Collectors.groupingBy(x -> x.getBudgetControlCode()));
        //重新分配预算管控
        for (Map.Entry<String, List<String>> entry : planCaseControlCodeMap.entrySet()) {
            List<BudgetTrackVo> dataList = Lists.newArrayList();
            for (String s : entry.getValue()) {
                if (map.containsKey(s)) {
                    dataList.addAll(map.get(s));
                }
            }
            planCaseControlMap.put(entry.getKey(), dataList);
        }
        List<Future<List<RegionCollectBudgetVo>>> futureList = Lists.newArrayList();
        //跑明细对应管控规则
        for (Map.Entry<String, List<BudgetTrackVo>> entry : planCaseControlMap.entrySet()) {
            RegionCollectControlBudgetComponent bean = ApplicationContextHolder.getContext().getBean(RegionCollectControlBudgetComponent.class);
            Future<List<RegionCollectBudgetVo>> future = bean.matchControlBudgetByCase(entry.getValue(), caseMap.get(entry.getKey()), collectCode, controlBudgetOrgMap, loginDetails);
            futureList.add(future);
        }
        StringJoiner errMsg = new StringJoiner(";");
        for (Future<List<RegionCollectBudgetVo>> future : futureList) {
            try {
                List<RegionCollectBudgetVo> budgetVoList = future.get();
                if (!CollectionUtils.isEmpty(budgetVoList)) {
                    list.addAll(future.get());
                }
            } catch (IllegalArgumentException e) {
                errMsg.add(e.getMessage());
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
        }
        Validate.isTrue(ObjectUtils.isEmpty(errMsg.toString()), errMsg.toString());
    }
}
