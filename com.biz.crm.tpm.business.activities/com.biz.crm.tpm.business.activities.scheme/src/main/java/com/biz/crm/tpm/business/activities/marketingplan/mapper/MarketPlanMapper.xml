<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanMapper">


    <select id="findList" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo">
        select a.* from tpm_marketing_plan a
        where
        a.del_flag = '${@<EMAIL>()}'
        <if test="vo.tenantCode != null and vo.tenantCode != ''">
            and a.tenant_code = #{vo.tenantCode}
        </if>
        <if test="vo.schemeCode != null and vo.schemeCode != ''">
            <bind name="schemeCode" value="vo.schemeCode + '%'"/>
            and a.scheme_code like #{schemeCode}
        </if>
        <if test="vo.schemeName != null and vo.schemeName != ''">
            <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
            and a.scheme_name like #{schemeName}
        </if>
        <if test="vo.departmentCode != null and vo.departmentCode != ''">
            <bind name="departmentCode" value="'%' + vo.departmentCode + '%'"/>
            and a.department_code like #{departmentCode}
        </if>
        <if test="vo.departmentName != null and vo.departmentName != ''">
            <bind name="departmentName" value="'%' + vo.departmentName + '%'"/>
            and a.department_name like #{departmentName}
        </if>
        <if test="vo.years != null and vo.years != ''">
            and a.years = #{vo.years}
        </if>
        <if test="vo.confirmStatus != null and vo.confirmStatus != ''">
            and a.confirm_status = #{vo.confirmStatus}
        </if>
        <if test="vo.isChange != null and vo.isChange != ''">
            <choose>
                <when test="'N'.toString() == vo.isChange ">
                    and a.original_scheme_code is null
                </when>
            </choose>
        </if>
        <if test="vo.schemeStatus != null and vo.schemeStatus != ''">
            and a.scheme_status = #{vo.schemeStatus}
        </if>
        <if test="vo.originalSchemeCode != null and vo.originalSchemeCode != ''">
            and a.original_scheme_code = #{vo.originalSchemeCode}
        </if>
        <if test="vo.processStatus != null and vo.processStatus != ''">
            and a.process_status = #{vo.processStatus}
        </if>
        <if test="vo.schemeType != null and vo.schemeType != ''">
            and a.scheme_type = #{vo.schemeType}
        </if>
        <if test="vo.createName != null and vo.createName != ''">
            <bind name="createName" value="'%' + vo.createName + '%'"/>
            and a.create_name like #{createName}
        </if>
        order by a.create_time desc,a.scheme_code desc
    </select>

    <select id="findListByContractCodes"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan">
        select a.id,a.process_status from tpm_marketing_plan a
        left join tpm_marketing_plan_case b on a.scheme_code = b.scheme_code
        left join tpm_marketing_plan_case_extend c on b.scheme_detail_code = c.scheme_detail_code and a.scheme_code =
        c.scheme_code
        <where>
            a.del_flag = '${@<EMAIL>()}'
            and a.tenant_code = #{tenantCode}
            <if test="contractCodes != null and contractCodes.size()>0">
                and c.contract_code in
                <foreach collection="contractCodes" open="(" close=")" separator="," item="item" index="index">
                    #{item}
                </foreach>
            </if>
            group by a.id,a.process_status
        </where>
    </select>

    <select id="findMarketingPlanCommit"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo">
        select a.*
        from tpm_marketing_plan a
                 inner join tpm_marketing_plan_case b on a.scheme_code = b.scheme_code
        where a.del_flag = '${@<EMAIL>()}'
          and a.process_status = '2'
          and a.scheme_type in ('append', 'change','o_two_o','additional')
          and b.years = #{yearMonthLy}
        order by a.create_time desc, a.scheme_code desc
    </select>


    <select id="findListByCustomerCodesAndBelongDepartmentCodes"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase">
        select a.customer_code,a.customer_name,a.belong_department_code,a.belong_department_name
        from tpm_marketing_plan_case a
        left join tpm_marketing_plan b on a.scheme_code = b.scheme_code
        <where>
            b.del_flag = '${@<EMAIL>()}'
            and b.process_status = '3'
            <if test="schemeCode != null and schemeCode != ''">
                and b.scheme_code != #{schemeCode}
            </if>
            <if test="years != null and years != ''">
                and b.years = #{years}
            </if>
            <if test="customerCodes != null and customerCodes.size()>0">
                and a.customer_code in
                <foreach collection="customerCodes" open="(" close=")" separator="," item="item" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="departmentCodes != null and departmentCodes.size()>0">
                and a.belong_department_code in
                <foreach collection="departmentCodes" open="(" close=")" separator="," item="item" index="index">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findChangeOccupyAmount"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase">
        SELECT
        a.scheme_detail_code,
        IFNULL(b.apply_amount, 0) - a.apply_amount apply_amount
        FROM
        tpm_marketing_plan_case a
        LEFT JOIN tpm_marketing_plan_case b ON a.original_scheme_detail_code = b.scheme_detail_code
        WHERE
        a.scheme_detail_code in
        <foreach collection="schemeDetailCodes" close=")" open="(" separator="," item="item" index="index">
            #{item}
        </foreach>
        and b.scheme_detail_code is not null
    </select>


    <select id="findRepateCaseListByCondition"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        a.scheme_code,
        b.scheme_detail_code,
        b.customer_code,
        b.belong_department_code,
        c.is_contract_cost,
        d.code levelProductCode,
        b.start_date,
        b.end_date
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        LEFT JOIN tpm_marketing_plan_case_extend c on b.scheme_detail_code = c.scheme_detail_code
        LEFT JOIN tpm_marketing_plan_product d ON b.scheme_detail_code = d.scheme_detail_code
        <where>
            a.del_flag = '009' and b.del_flag = '009'
            and b.case_type = #{caseType}
            and b.start_date is not null and b.end_date is not null
            AND b.customer_code in
            <foreach collection="cusCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND b.belong_department_code in
            <foreach collection="belongDepartmentCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            and c.is_contract_cost in
            <foreach collection="isContractCost" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND d.code in
            <foreach collection="levelProductCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test="excludeSchemeDetailCodes != null and excludeSchemeDetailCodes.size()>0">
                and b.scheme_detail_code not in
                <foreach collection="excludeSchemeDetailCodes" index="index" item="item" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="schemeCode != null and schemeCode != ''">
                and b.scheme_detail_code not in (
                SELECT
                b.scheme_detail_code
                FROM
                tpm_marketing_plan a
                LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
                WHERE
                a.process_status in('2', '3')
                AND a.original_scheme_code = #{schemeCode}
                )
            </if>
            group by
            a.scheme_code,
            b.scheme_detail_code,
            b.customer_code,
            b.belong_department_code,
            c.is_contract_cost,
            d.code,
            b.start_date,
            b.end_date
        </where>
    </select>

    <select id="updateCustomerCooperateType">
        update tpm_marketing_plan_case a LEFT JOIN tpm_marketing_plan_case_extend b
        on a.scheme_detail_code= b.scheme_detail_code
            LEFT JOIN tpm_customer_cooperate_type_tmp c on a.customer_code = c.customer_code
            set b.cooperate_type = c.cooperate_type
        where a.customer_code = c.customer_code
          and a.create_time >= (SELECT DATE_SUB(NOW()
            , INTERVAL 20 MINUTE))
          and b.cooperate_type is null
    </select>

    <select id="updateCustomerCooperateTypeByAudit">
        UPDATE
            tpm_marketing_audit_detail a
            LEFT JOIN tpm_customer_cooperate_type_tmp c
        ON a.customer_code = c.customer_code
            SET
                a.cooperate_type = c.cooperate_type
        WHERE
            a.customer_code = c.customer_code
          AND a.create_time >= (
            SELECT
            DATE_SUB(NOW()
            , INTERVAL 20 MINUTE))
          AND a.cooperate_type IS NULL
    </select>


    <select id="findMiniCostTotalView"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        sum(b.apply_amount) applyAmount,
        sum(ifnull(b.cash_amount,0)) cashAmount,
        b.category_name
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        <where>
            a.process_status = '3'
            <if test="vo.customerCode != null and vo.customerCode != ''">
                AND b.customer_code = #{vo.customerCode}
            </if>
            <if test="vo.years != null and vo.years != ''">
                AND b.years = #{vo.years}
            </if>
            <if test="vo.startDate != null and vo.startDate != ''">
                <![CDATA[and b.start_date <= #{vo.startDate}]]>
            </if>
            <if test="vo.endDate != null and vo.endDate != ''">
                <![CDATA[and b.end_date >= #{vo.endDate}]]>
            </if>
        </where>
        group by b.category_name
    </select>

    <select id="findListByCondition"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase">
        SELECT
        b.customer_code,b.customer_name,b.belong_department_code,b.belong_department_name,b.years
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        <where>
            a.del_flag = '009'
            AND b.act_years in
            <foreach collection="actYearsList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
            AND b.customer_code in
            <foreach collection="customerCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND b.belong_department_code in
            <foreach collection="belongDepartmentCodes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
            and a.scheme_type in
            <foreach collection="schemeTypes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="countTerminalNum" resultType="java.lang.String">
        SELECT
            b.terminal_code
        FROM
            tpm_marketing_plan a
             LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_cod
        WHERE
            a.del_flag = '009'
          AND b.del_flag = '009'
          AND a.process_status = '3'
          and b.terminal_code is not NULL
          and b.terminal_code != ''
         <if test="years != null and years != ''">
             and and b.years = #{year}
         </if>
        GROUP BY b.terminal_code
    </select>

    <select id="findAddSchemeListByYearsAndCustomers"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase">
        SELECT a.scheme_code,
        b.customer_code,
        b.customer_name
        FROM tpm_marketing_plan a
        LEFT JOIN tpm_marketing_sales_plan b ON a.scheme_code = b.scheme_code
        WHERE a.del_flag = '009'
        AND a.scheme_type != 'additional'
        AND b.years = #{years}
        AND b.customer_code in
        <foreach collection="customerCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
        group by a.scheme_code,
        b.customer_code,
        b.customer_name
    </select>

</mapper>

