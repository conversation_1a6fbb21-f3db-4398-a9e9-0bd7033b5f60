package com.biz.crm.tpm.business.activities.scheme.service;

import com.biz.crm.tpm.business.activities.scheme.dto.SchemeRangeDto;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeRangeVo;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 方案范围;(tpm_scheme_range)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
public interface SchemeRangeVoService {

  /**
   * 通过方案编号查询单条数据
   *
   * @param schemeCode 方案编号
   * @return 多条数据
   */
  Set<SchemeRangeVo> findBySchemeCode(String schemeCode);

  /**
   * 查询所有的限制信息，由于MDM无法提供直接返回用户允许范围的组织，组织层级只有查询提供给MDM评估
   * @return
   */
  Set<SchemeRangeVo> findAll();

  /**
   * 新增数据
   *
   * @param schemeRangeVo 实体对象
   * @return 新增结果
   */
  SchemeRangeVo create(SchemeRangeVo schemeRangeVo);

  /**
   * 修改数据
   *
   * @param schemeRangeDto 实体对象
   * @return 修改结果
   */
  SchemeRangeVo update(SchemeRangeDto schemeRangeDto);

  /**
   * 删除数据
   *
   * @param schemeCode 方案编号
   */
  void deleteBySchemeCode(String schemeCode);

  /**
   * 根据编号集合删除数据
   * @param codes
   */
  void deleteByRangeCodes(Collection<String> codes);

  /**
   * 批量根据id启用
   * @param ids
   */
  void enable(List<String> ids);
  /**
   * 批量根据id禁用
   * @param ids
   */
  void disable(List<String> ids);

  /**
   * 启禁用
   */
  void updateEnableStatus(Set<String> codes, String enableStatus);
}