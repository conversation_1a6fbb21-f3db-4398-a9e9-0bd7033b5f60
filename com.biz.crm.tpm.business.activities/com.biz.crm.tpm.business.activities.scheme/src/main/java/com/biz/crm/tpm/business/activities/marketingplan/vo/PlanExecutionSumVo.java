package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @Author: yangrui
 * @Date: 2025-01-02 10:31
 */
@Data
public class PlanExecutionSumVo extends UuidFlagOpVo {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("门店编码")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    private String terminalName;

    @ApiModelProperty("所属大区编码")
    private String divisionOrgCode;

    @ApiModelProperty("所属大区")
    private String divisionOrgName;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("指引编码")
    private String releaseCode;

    @ApiModelProperty("指引名称")
    private String releaseName;

    @ApiModelProperty("指引明细编码")
    private String releaseDetailCode;

    @ApiModelProperty("是否百万终端")
    private Integer millionTerminal;

    @ApiModelProperty("终端类型")
    private String terminalType;

    @ApiModelProperty("活动执行编码")
    private String actExecuteCode;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("开始时间起")
    private String startDateBegin;

    @ApiModelProperty("开始时间止")
    private String startDateEnd;

    @ApiModelProperty("结束时间起")
    private String endDateBegin;

    @ApiModelProperty("结束时间止")
    private String endDateEnd;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("执行标准")
    private String actStandards;

    @ApiModelProperty("审核结果")
    private Integer auditResult;

    @ApiModelProperty("审核结果")
    private String auditResultDesc;
}
