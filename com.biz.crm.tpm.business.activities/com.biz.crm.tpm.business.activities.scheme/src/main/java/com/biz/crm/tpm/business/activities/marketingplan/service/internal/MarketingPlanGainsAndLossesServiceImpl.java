package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanGainsAndLossesRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanGainsAndLossesService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanGainsAndLossesVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/4 10:17
 */
@Service("marketingPlanGainsAndLossesService")
public class MarketingPlanGainsAndLossesServiceImpl implements MarketingPlanGainsAndLossesService {

    @Resource
    private MarketingPlanGainsAndLossesRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;


    @Override
    public Page<MarketingPlanGainsAndLosses> findList(Pageable pageable, MarketingPlanGainsAndLosses vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Validate.notNull(vo.getSchemeCode(), "方案编码不能为空");
        Page<MarketingPlanGainsAndLosses> page = new Page<>(pageable.getPageNumber(),pageable.getPageSize());
        return repository.findList(page, vo);
    }

    @Override
    public List<MarketingPlanGainsAndLosses> findListBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return Lists.newArrayList();
        }
        List<MarketingPlanGainsAndLosses> list = repository.findListBySchemeCodes(schemeCodes);
        for (MarketingPlanGainsAndLosses losses : list) {
            if (ObjectUtils.isNotEmpty(losses.getSecondCostCategoryJsonStr())) {
                losses.setSecondCostCategoryMap(JSONObject.parseObject(losses.getSecondCostCategoryJsonStr(), LinkedHashMap.class));
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchList(List<MarketingPlanGainsAndLossesVo> voList, String schemeCode) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        //删除原来的数据
        repository.remove(Wrappers.lambdaQuery(MarketingPlanGainsAndLosses.class)
                .eq(MarketingPlanGainsAndLosses::getSchemeCode, schemeCode));
        List<MarketingPlanGainsAndLosses> list = (List<MarketingPlanGainsAndLosses>) nebulaToolkitService.copyCollectionByWhiteList(voList,
                MarketingPlanGainsAndLossesVo.class, MarketingPlanGainsAndLosses.class, HashSet.class, ArrayList.class);
        list.forEach(x -> {
            x.setSchemeCode(schemeCode);
            if (!CollectionUtils.isEmpty(x.getSecondCostCategoryMap())) {
                x.setSecondCostCategoryJsonStr(JSONObject.toJSONString(x.getSecondCostCategoryMap()));
            }
            x.setId(null);
        });
        repository.saveBatch(list);
    }

    @Override
    public void deleteBySchemeCodes(List<String> schemeCodes) {
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return;
        }
        repository.deleteBySchemeCodes(schemeCodes);
    }
}
