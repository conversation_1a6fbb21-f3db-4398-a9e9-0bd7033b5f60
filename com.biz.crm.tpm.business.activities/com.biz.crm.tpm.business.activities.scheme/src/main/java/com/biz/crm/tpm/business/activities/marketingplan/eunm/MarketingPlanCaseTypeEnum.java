package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 19:56
 */
@Getter
@AllArgsConstructor
public enum MarketingPlanCaseTypeEnum {

    display("BDMB0003", "市场推广类"),
    material("BDMB0004", "周边物料"),
    matching_gift("BDMB0005", "随单搭赠"),
    back("BDMB0006", "进货返利"),
    o_two_o("BDMB0007", "O2O"),
    fixed("BDMB0008", "其他类"),
    direct_descent("BDMB0010","直降"),
    special_price("BDMB0009","特价"),
    staff_cost("BDMB0020","人员费用"),

    ;

    private String code;

    private String desc;


    public static Boolean checkCode(String code) {
        Boolean flag = Boolean.FALSE;
        for (MarketingPlanCaseTypeEnum value : MarketingPlanCaseTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                flag = Boolean.TRUE;
                break;
            }
        }
        return flag;
    }

    public static MarketingPlanCaseTypeEnum findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (MarketingPlanCaseTypeEnum item : MarketingPlanCaseTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static List<String> noWithHolding() {
        return Arrays.asList(material.getCode(), matching_gift.getCode(), back.getCode(), direct_descent.getCode(), special_price.getCode());
    }
}
