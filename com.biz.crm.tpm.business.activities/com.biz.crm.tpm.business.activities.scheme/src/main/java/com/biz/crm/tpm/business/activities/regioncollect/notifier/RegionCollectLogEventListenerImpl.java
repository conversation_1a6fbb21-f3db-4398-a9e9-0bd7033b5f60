package com.biz.crm.tpm.business.activities.regioncollect.notifier;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.activities.regioncollect.dto.RegionCollectLogEventDto;
import com.biz.crm.tpm.business.activities.regioncollect.event.RegionCollectLogEventListener;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Component
@Slf4j
public class RegionCollectLogEventListenerImpl implements RegionCollectLogEventListener {

    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public void onCreate(RegionCollectLogEventDto dto) {
        RegionCollectVo newest = dto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
//        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    @Override
    public void onUpdate(RegionCollectLogEventDto dto) {
        RegionCollectVo original = dto.getOriginal();
        RegionCollectVo newest = dto.getNewest();
        String onlyKey = original.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    @Override
    public void onDelete(RegionCollectLogEventDto dto) {
        List<RegionCollectVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        List<RegionCollectVo> oldList = (List<RegionCollectVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, RegionCollectVo.class,
                RegionCollectVo.class, HashSet.class, ArrayList.class);
        Map<String, RegionCollectVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(RegionCollectVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            RegionCollectVo original = oldMap.getOrDefault(newest.getId(), new RegionCollectVo());
            original.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setDelFlag(DelFlagStatusEnum.DELETE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }
}
