package com.biz.crm.tpm.business.activities.contract.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 09:56
 */
@Data
@ApiModel(value = "ExternalContractDetailDto", description = "外部合同明细")
public class ExternalContractDetailDto {

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("合同明细编码")
    private String contractDetailCode;

    @ApiModelProperty("唯一编码")
    private String onlyKey;

    @ApiModelProperty("合同明细类型")
    private String caseType;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("归属部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("归属部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("预估费用")
    private BigDecimal estimatedCost;

    @ApiModelProperty("兑付方式")
    private String cashType;

    @ApiModelProperty("返利类型")
    private String rebateType;

    @ApiModelProperty("返利计算日期")
    private Integer rebateCalDay;

    @ApiModelProperty("条件公式")
    private String conditionFormula;

    @ApiModelProperty("政策形式名称")
    private String conditionFormulaName;

    @ApiModelProperty("结果公式")
    private String resultFormula;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("返利标准")
    private String rebateStandard;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("条件数量")
    private String conditionNum;

    @ApiModelProperty("搭赠数量")
    private String giveNum;

    @ApiModelProperty("优惠数量-上限")
    private BigDecimal discountQuantity;

    @ApiModelProperty("优惠金额-上限")
    private BigDecimal discountAmount;

    private String productStr;

    private String itemStr;

    private String levelStr;

    private String feeProductStr;

    private String feeItemStr;

    private String feeLevelStr;

    private String feeBelongItemStr;

    @ApiModelProperty("商品-")
    private List<ExternalContractProductDto> productList;

    @ApiModelProperty("品项")
    private List<ExternalContractProductDto> itemList;

    @ApiModelProperty("层级")
    private List<ExternalContractProductDto> levelList;

    @ApiModelProperty("费用商品")
    private List<ExternalContractProductDto> feeProductList;

    @ApiModelProperty("费用品项")
    private List<ExternalContractProductDto> feeItemList;

    @ApiModelProperty("费用层级")
    private List<ExternalContractProductDto> feeLevelList;

    @ApiModelProperty("费用归属品项")
    private List<ExternalContractProductDto> feeBelongItemList;
}
