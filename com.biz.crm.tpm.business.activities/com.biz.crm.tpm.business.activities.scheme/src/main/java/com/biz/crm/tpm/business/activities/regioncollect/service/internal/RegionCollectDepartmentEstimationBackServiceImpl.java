package com.biz.crm.tpm.business.activities.regioncollect.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectDepartmentEstimationBack;
import com.biz.crm.tpm.business.activities.regioncollect.repository.RegionCollectDepartmentEstimationBackRepository;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectDepartmentEstimationBackService;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectDepartmentEstimationService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectDepartmentEstimationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Service
@Transactional
@Slf4j
public class RegionCollectDepartmentEstimationBackServiceImpl implements RegionCollectDepartmentEstimationBackService {

    @Resource
    private RegionCollectDepartmentEstimationBackRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;


    @Override
    public void deleteByCollectCodes(List<String> collectCodes) {
        repository.deleteByCollectCodes(collectCodes);
    }

    /**
     * 保存数据
     *
     * @param list
     */
    @Override
    public void saveList(List<RegionCollectDepartmentEstimationVo> list, String collectCode) {
        repository.deleteByCollectCodes(Lists.newArrayList(collectCode));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<RegionCollectDepartmentEstimationBack> dataList = list.stream().map(x -> {
            RegionCollectDepartmentEstimationBack data = nebulaToolkitService.copyObjectByWhiteList(x, RegionCollectDepartmentEstimationBack.class, HashSet.class, ArrayList.class);
            if (!CollectionUtils.isEmpty(data.getSecondCostCategoryMap())) {
                data.setSecondCostCategoryJsonStr(JSONObject.toJSONString(data.getSecondCostCategoryMap()));
            }
            data.setId(null);
            data.setCollectCode(collectCode);
            return data;
        }).collect(Collectors.toList());
        repository.saveBatch(dataList);
    }
}
