package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_plan_exceution_picture")
@Table(
        name = "tpm_plan_exceution_picture",
        indexes = {
                @Index(name = "plan_exceution_index1", columnList = "collect_id")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_plan_exceution_picture", comment = "执行照片")
@ApiModel(value = "PlanExecutionPicture", description = "执行照片")
public class PlanExecutionPicture extends FileEntity {


    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("活动执行编码")
    @Column(name = "act_execute_code", columnDefinition = "varchar(32) comment '活动执行编码'")
    private String actExecuteCode;

    @ApiModelProperty("采集id")
    @Column(name = "collect_id", columnDefinition = "varchar(32) comment '大日期回调采集id'")
    private String collectId;

}
