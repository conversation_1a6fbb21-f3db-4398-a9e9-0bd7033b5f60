package com.biz.crm.tpm.business.activities.stagingscheme.service.impl;

import com.biz.crm.tpm.business.activities.stagingscheme.entity.TpmStagingSalesPlanEntity;
import com.biz.crm.tpm.business.activities.stagingscheme.repository.TpmStagingSalesPlanRepository;
import com.biz.crm.tpm.business.activities.stagingscheme.service.TpmStagingSalesPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class TpmStagingSalesPlanServiceImpl implements TpmStagingSalesPlanService {

    @Autowired
    private TpmStagingSalesPlanRepository tpmStagingSalesPlanRepository;

    @Override
    public List<TpmStagingSalesPlanEntity> findListByCondition(List<String> releaseIds) {
        return tpmStagingSalesPlanRepository.lambdaQuery().in(TpmStagingSalesPlanEntity::getReleaseId, releaseIds).list();
    }
}
