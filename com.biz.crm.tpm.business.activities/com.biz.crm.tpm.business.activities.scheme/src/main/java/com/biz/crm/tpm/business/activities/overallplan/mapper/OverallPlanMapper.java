package com.biz.crm.tpm.business.activities.overallplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlan;
import com.biz.crm.tpm.business.activities.overallplan.vo.HeadOverallSummaryReportVo;
import com.biz.crm.tpm.business.activities.overallplan.vo.OverallPlanVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OverallPlanMapper extends BaseMapper<OverallPlan> {

    /**
     * 查询分页列表
     *
     * @param page
     * @param vo
     * @return
     */
    Page<OverallPlanVo> findList(Page<OverallPlanVo> page, @Param("vo") OverallPlanVo vo);

    Page<HeadOverallSummaryReportVo> findSummaryList(Page<HeadOverallSummaryReportVo> page, @Param("vo") HeadOverallSummaryReportVo vo,
                                                     @Param("tenantCode") String tenantCode);

    List<OverallPlan> findBySchemeCodes(@Param("schemeCodes") List<String> headSchemeCodes);

    List<String> findBySchemeCodesByName(@Param("headSchemaName") String headSchemaNaMe);
}
