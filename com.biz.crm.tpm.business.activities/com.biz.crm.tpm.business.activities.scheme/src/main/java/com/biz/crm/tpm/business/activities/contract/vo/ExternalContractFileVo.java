package com.biz.crm.tpm.business.activities.contract.vo;

import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/3 14:00
 */
@Data
@ApiModel(value = "ExternalContractFileVo", description = "外部合同附件地址")
public class ExternalContractFileVo extends UuidFlagOpEntity {

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("附件地址")
    private String url;
}
