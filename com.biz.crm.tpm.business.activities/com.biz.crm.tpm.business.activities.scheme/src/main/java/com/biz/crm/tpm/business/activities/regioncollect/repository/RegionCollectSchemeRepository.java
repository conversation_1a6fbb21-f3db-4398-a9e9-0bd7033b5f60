package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectMarketingEstimation;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollectScheme;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectSchemeMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class RegionCollectSchemeRepository extends ServiceImpl<RegionCollectSchemeMapper, RegionCollectScheme> {


    public List<RegionCollectScheme> findByCollectCode(String collectCode) {
        return this.lambdaQuery()
                .eq(RegionCollectScheme::getCollectCode, collectCode).list();
    }

    public void deleteByCollectCodes(List<String> collectCodes) {
        this.lambdaUpdate()
                .in(RegionCollectScheme::getCollectCode, collectCodes).remove();
    }

    public void deleteBySchemeCodes(List<String> schemeCodes) {
        this.lambdaUpdate()
                .in(RegionCollectScheme::getSchemeCode, schemeCodes).remove();
    }


    public RegionCollectScheme findRegionCollectCodeBySchemeCode(String schemeCode) {
        return this.lambdaQuery()
                .eq(RegionCollectScheme::getSchemeCode, schemeCode).one();
    }
}
