package com.biz.crm.tpm.business.activities.contract.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.contract.dto.ContractCostPageDto;
import com.biz.crm.tpm.business.activities.contract.dto.ExternalContractDto;
import com.biz.crm.tpm.business.activities.contract.entity.ExternalContract;
import com.biz.crm.tpm.business.activities.contract.eunm.ExternalContractStatusEnum;
import com.biz.crm.tpm.business.activities.contract.mapper.ExternalContractMapper;
import com.biz.crm.tpm.business.activities.contract.vo.ContractCostDetailVo;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 11:39
 */
@Component
public class ExternalContractRepository extends ServiceImpl<ExternalContractMapper, ExternalContract> {

    public Page<ExternalContractVo> findList(Page<ExternalContractDto> page, ExternalContractDto dto) {
        return this.baseMapper.findList(page, dto);
    }


    /**
     * 此方法调用 必须验证id和contractCode其中一个必不能为空
     *
     * @param id
     * @param contractCode
     * @param contractStatus
     * @return
     */
    public ExternalContract queryDetails(String id, String contractCode, String contractStatus) {
        return this.lambdaQuery()
                .eq(StringUtils.isNotBlank(contractCode), ExternalContract::getContractCode, contractCode)
                .eq(StringUtils.isNotBlank(id), ExternalContract::getId, contractCode)
                .eq(ExternalContract::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ExternalContract::getTenantCode, TenantUtils.getTenantCode())
                .eq(ExternalContract::getContractStatus, contractStatus)
                .one();
    }

    public ExternalContract queryByCondition(String customerCode, String contractCode, String contractStatus) {
        return this.lambdaQuery()
                .eq(ExternalContract::getCustomerCode, customerCode)
                .eq(ExternalContract::getContractCode, contractCode)
                .eq(ExternalContract::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ExternalContract::getTenantCode, TenantUtils.getTenantCode())
                .eq(ExternalContract::getContractStatus, contractStatus)
                .one();
    }

    public void updateContractStatus(String contractCode, String tenantCode) {
        this.lambdaUpdate()
                .set(ExternalContract::getContractStatus, ExternalContractStatusEnum.stop.getCode())
                .eq(ExternalContract::getContractCode, contractCode)
                .eq(ExternalContract::getTenantCode, tenantCode)
                .update();
    }

    public void updateContractFile(String contractCode, String fileCode) {
        this.lambdaUpdate()
                .eq(ExternalContract::getContractCode, contractCode)
                .eq(ExternalContract::getContractStatus, ExternalContractStatusEnum.normal.getCode())
                .eq(ExternalContract::getTenantCode, TenantUtils.getTenantCode())
                .eq(ExternalContract::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .set(ExternalContract::getFileUrl, fileCode).update();
    }

    public Page<ContractCostPageDto> findContractCostList(Page<ContractCostPageDto> page, ContractCostPageDto vo) {
        return this.baseMapper.findContractCostList(page, vo, TenantUtils.getTenantCode());
    }

    public Page<ContractCostPageDto> findExternalContractList(Page<ContractCostPageDto> page, ContractCostPageDto vo) {
        return this.baseMapper.findExternalContractCostList(page, vo, TenantUtils.getTenantCode());
    }

    /**
     * 查询未执行并且不是外部的合同
     *
     * @return
     */
    public List<ContractCostDetailVo> findUnExecutedAndNotExternal(String years) {
        return this.baseMapper.findUnExecutedAndNotExternal(years, TenantUtils.getTenantCode());
    }

    public void updateContractExecuteStatus(List<String> onlyKeys, String executeStatus, String log) {
        this.lambdaUpdate()
                .in(ExternalContract::getOnlyKey, onlyKeys)
                .set(ExternalContract::getExecuteStatus, executeStatus)
                .set(ExternalContract::getLog, log)
                .update();
    }

    public List<ExternalContract> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>(0);
        }
        return this.lambdaQuery()
                .in(ExternalContract::getId, ids)
                .eq(ExternalContract::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ExternalContract::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    public List<ExternalContract> findByOnlyKeys(List<String> onlyKeys) {
        if (CollectionUtils.isEmpty(onlyKeys)) {
            return new ArrayList<>(0);
        }
        return this.lambdaQuery()
                .in(ExternalContract::getOnlyKey, onlyKeys)
                .eq(ExternalContract::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    public void removeByOnlyKeys(List<String> onlyKeys) {
        if (CollectionUtils.isEmpty(onlyKeys)) {
            return;
        }
        this.lambdaUpdate()
                .in(ExternalContract::getOnlyKey, onlyKeys)
                .eq(ExternalContract::getTenantCode, TenantUtils.getTenantCode())
                .remove();
    }

    public Page<ExternalContractVo> findByConditions(Pageable pageable, ContractCostPageDto vo) {
        return this.baseMapper.findByConditions(new Page<ExternalContractVo>(pageable.getPageNumber(), pageable.getPageSize()), vo);
    }


    public List<ExternalContractVo> findContractNearDate(Integer day) {
        return this.baseMapper.findContractNearDate(day);
    }


    public void updateContractPushSfaFlag(List<String> ids){
        this.lambdaUpdate()
                .set(ExternalContract::getPushSfaFlag, BooleanEnum.TRUE.getCapital())
                .in(ExternalContract::getId,ids)
                .update();
    }
}
