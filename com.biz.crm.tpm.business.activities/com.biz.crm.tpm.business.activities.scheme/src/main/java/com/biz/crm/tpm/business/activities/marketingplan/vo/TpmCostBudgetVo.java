package com.biz.crm.tpm.business.activities.marketingplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/2 11:02
 */
@ApiModel("预算vo")
@Data
public class TpmCostBudgetVo {

    @ApiModelProperty("年度预算编码")
    private String code;

    @ApiModelProperty("关联明细编码")
    private String businessDetailCode;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("预提金额")
    private BigDecimal withholdingAmount;

    @ApiModelProperty("结案金额")
    private BigDecimal endCaseAmount;

    @ApiModelProperty("最终结果金额")
    private BigDecimal lastAmount;

    @ApiModelProperty("方案明细类型")
    private String caseType;
}
