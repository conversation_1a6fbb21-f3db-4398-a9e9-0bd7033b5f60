package com.biz.crm.tpm.business.activities.scheme.vo;

import com.biz.crm.business.common.sdk.vo.FileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Vo：方案活动附件;
 * <AUTHOR> <PERSON>
 * @date : 2022-6-20
 */
@ApiModel(value = "ActivitiesSchemeFiles",description = "方案活动附件")
@Getter
@Setter
public class ActivitiesSchemeFilesVo extends FileVo {
  /** 活动编号 */
  @ApiModelProperty(name = "activitiesCode",notes = "活动编号", value= "活动编号")
  private String activitiesCode;
}
