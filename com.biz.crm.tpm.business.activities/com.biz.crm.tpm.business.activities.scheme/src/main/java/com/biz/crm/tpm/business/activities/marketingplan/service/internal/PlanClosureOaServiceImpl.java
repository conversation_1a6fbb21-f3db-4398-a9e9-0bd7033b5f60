package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosure;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.PlanClosureCaseFieldsEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.PlanClosureDetailFieldsEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.PlanClosureFieldsEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.PlanClosureRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanClosureOaService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureDetailVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanBearTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.MarketPlanCloseDetailDto;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.order.OverallPlanCloseMainDto;
import com.biz.crm.workflow.sdk.dto.oa.order.OverallPlanDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;

@Service
@RefreshScope
public class PlanClosureOaServiceImpl implements PlanClosureOaService {


    @Autowired(required = false)
    private PushOaService pushOaService;
    @Autowired(required = false)
    private PlanClosureRepository planClosureRepository;
    @Autowired
    private UserVoService userVoService;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Value("${domain-name:}")
    private String domainName;
    @Autowired(required = false)
    private LoginUserService loginUserService;

    /**
     * 推送OA
     *
     * @param plan@return
     */
    @Override
    public JSONObject pushOa(PlanClosure plan) {
        List<PlanClosureDetailVo> detailVos = plan.getDetailVos();
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(plan));
        orderJsonObject.put("businessCode", plan.getCloseCode());
        BigDecimal sumAmount = detailVos.stream().map(e -> e.getBigDecimal("applyAmount")).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailVos.forEach(e -> {
            e.put("estimatedCost", e.getBigDecimal("applyAmount"));
            if (e.containsKey("bearType")){
                OverallPlanBearTypeEnum bearType = OverallPlanBearTypeEnum.findByCode(e.getString("bearType"));
                e.put("bearType", bearType == null ? e.getString("bearType") : bearType.getDesc());
            }
            JSONArray bearDepartmentList = e.getJSONArray("bearDepartmentList");
            if (!CollectionUtil.isEmpty(bearDepartmentList)) {
                e.put("bearDepartmentName", bearDepartmentList.getJSONObject(0).getString("departmentName"));
            }
            e.setJsonStrOa(JSON.toJSONString(e));
        });
        orderJsonObject.put("closeAmount", sumAmount);

        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        orderJsonObject.put("title", plan.getCloseName() + "-" + userVo.getFullName() + "-" + DateUtil.format(new Date(), "yyyy-MM-dd"));

        // 主表
        // 业务类型
        String businessType;
        if (plan.getSourceType().equals(OverallPlanSchemeTypeEnum.HEAD.getCode())) {
            businessType = MqConstant.TAG_TPM_HEAD_OVERALL_PLAN_CLOSE;
        } else if (plan.getSourceType().equals(OverallPlanSchemeTypeEnum.REGION.getCode())) {
            businessType = MqConstant.TAG_TPM_REGION_OVERALL_PLAN_CLOSE;
        } else {
            businessType = MqConstant.TAG_TPM_MARKET_PLAN_CLOSE;
        }
        orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.ACTIVITY_MARKET_CLOSE_FORM.getUrlCode() + "?id=" + plan.getId());
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        JSONObject response = businessType.equals(MqConstant.TAG_TPM_MARKET_PLAN_CLOSE) ?
                pushOaService.pushOA(orderJsonObject, businessType, oaParam, Arrays.asList(JSONArray.parseArray(JSON.toJSONString(getDetails(detailVos, MarketPlanCloseDetailDto.class)))), workflowId, workflowName,
                        mainTableMethod, caseTableMethod) :
                pushOaService.pushOA(orderJsonObject, businessType, oaParam, Arrays.asList(JSONArray.parseArray(JSON.toJSONString(getDetails(detailVos, OverallPlanDetailDto.class)))), workflowId, workflowName,
                        mainTableMethod, detailTableMethod);

        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        Validate.isTrue(response.containsKey("code"), "OA流程提交失败，OA未正常返回信息");

        Integer resultCode = response.getInteger("code");
        Validate.isTrue(resultCode == 100, String.format("提交OA流程失败,失败原因:%s", response.getString("msg")));

        JSONObject ja = response.getJSONObject("data");
        PlanClosure entity = planClosureRepository.queryByIdOrCode(plan.getId(), null);
        entity.setProcessNumber(ja.getString("out"));
        entity.setProcessDate(new Date());
        entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
        entity.setOaId(response.getString("oaId"));
        entity.setOaUserName(response.getString("oaUserName"));
        planClosureRepository.updateById(entity);

        return response;
    }

    /**
     * JSON转明细对象
     *
     * @param detailVos
     * @param clzz
     * @param <T>
     * @return
     */
    <T> List<T> getDetails(List<PlanClosureDetailVo> detailVos, Class<T> clzz) {
        List<T> list = new ArrayList<>();
        if (CollectionUtil.isEmpty(detailVos)) {
            return list;
        }
        detailVos.forEach(e -> {
            T obj = JSONUtil.toBean(e.getJsonStrOa(), clzz);
            list.add(obj);
        });
        return list;
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (PlanClosureFieldsEnum value : PlanClosureFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod = (tableDetailFields, detailJsonObject) -> {
        for (PlanClosureDetailFieldsEnum value : PlanClosureDetailFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> caseTableMethod = (tableDetailFields, detailJsonObject) -> {
        for (PlanClosureCaseFieldsEnum value : PlanClosureCaseFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };

    /**
     * 重新提交OA
     *
     * @param plan@return
     */
    @Override
    public JSONObject resubmitOa(PlanClosure plan) {
        OaResubmitDto dto = new OaResubmitDto();
        List<PlanClosureDetailVo> detailVos = plan.getDetailVos();

        // 获取当前登录人信息
        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);

        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType;
        if (plan.getSourceType().equals(OverallPlanSchemeTypeEnum.HEAD.getCode())) {
            businessType = MqConstant.TAG_TPM_HEAD_OVERALL_PLAN_CLOSE;
        } else if (plan.getSourceType().equals(OverallPlanSchemeTypeEnum.REGION.getCode())) {
            businessType = MqConstant.TAG_TPM_REGION_OVERALL_PLAN_CLOSE;
        } else {
            businessType = MqConstant.TAG_TPM_MARKET_PLAN_CLOSE;
        }
        BigDecimal sumAmount = detailVos.stream().map(e -> e.getBigDecimal("applyAmount")).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailVos.forEach(e -> {
            e.put("estimatedCost", e.getBigDecimal("applyAmount"));
            if (e.containsKey("bearType")){
                OverallPlanBearTypeEnum bearType = OverallPlanBearTypeEnum.findByCode(e.getString("bearType"));
                e.put("bearType", bearType == null ? e.getString("bearType") : bearType.getDesc());
            }
            JSONArray bearDepartmentList = e.getJSONArray("bearDepartmentList");
            if (!CollectionUtil.isEmpty(bearDepartmentList)) {
                e.put("bearDepartmentName", bearDepartmentList.getJSONObject(0).getString("departmentName"));
            }
            e.setJsonStrOa(JSON.toJSONString(e));
        });
        plan.setCloseAmount(sumAmount);
        plan.setBusinessCode(plan.getId());
        plan.setTpmUrl(domainName + TpmOaPageEnum.ACTIVITY_MARKET_CLOSE_FORM.getUrlCode() + "?id=" + plan.getId());
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();

        dto.setBusinessCode(businessType);
        dto.setRequestId(plan.getProcessNumber());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        OverallPlanCloseMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(plan, OverallPlanCloseMainDto.class, LinkedHashSet.class, ArrayList.class);
        dto.setRequestName(plan.getCloseName() + "-" + userVo.getFullName() + "-" + DateUtil.format(new Date(), "yyyy-MM-dd"));
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));
        OaDetailDto oaDetailDto = new OaDetailDto();
        if (businessType.equals(MqConstant.TAG_TPM_MARKET_PLAN_CLOSE)) {
            oaDetailDto.setDetailList(JSONUtil.toJsonStr(getDetails(detailVos, MarketPlanCloseDetailDto.class)));
            oaDetailDto.setDetailClass(MarketPlanCloseDetailDto.class);
        } else {
            oaDetailDto.setDetailList(JSONUtil.toJsonStr(getDetails(detailVos, OverallPlanDetailDto.class)));
            oaDetailDto.setDetailClass(OverallPlanDetailDto.class);
        }
        dto.setDetailList(Arrays.asList(oaDetailDto));

        if (ryOaProcessService.resubmit(dto)) {
            PlanClosure entity = planClosureRepository.queryByIdOrCode(plan.getId(), null);
            entity.setProcessDate(new Date());
            entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
            planClosureRepository.updateById(entity);
        }
        return null;
    }

    /**
     * OA撤回
     *
     * @param plan
     * @param remark
     * @return
     */
    @Override
    public boolean oaWithdraw(PlanClosure plan, String remark) {
        Validate.isTrue(plan.getProcessStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(plan.getProcessNumber()));
        dto.setProcessCreateId(plan.getOaId());
        dto.setUserName(plan.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }
}
