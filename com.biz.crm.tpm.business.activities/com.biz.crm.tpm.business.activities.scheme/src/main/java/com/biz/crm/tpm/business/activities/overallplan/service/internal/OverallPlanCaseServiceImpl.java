package com.biz.crm.tpm.business.activities.overallplan.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.util.BusinessRequiredUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaCusVo;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosure;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanClosureDetail;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.repository.PlanClosureDetailRepository;
import com.biz.crm.tpm.business.activities.marketingplan.repository.PlanClosureRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.internal.PlanCaseUnderTakeComponent;
import com.biz.crm.tpm.business.activities.overallplan.constant.OverallPlanConstant;
import com.biz.crm.tpm.business.activities.overallplan.entity.*;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanBearTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanScopeTypeEnum;
import com.biz.crm.tpm.business.activities.overallplan.repository.*;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanCaseService;
import com.biz.crm.tpm.business.activities.overallplan.service.OverallPlanService;
import com.biz.crm.tpm.business.activities.overallplan.vo.*;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.activities.stagingscheme.vo.TpmStagingSchemeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.logging.log4j.util.PropertySource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterOrgVo;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/4 20:15
 */
@Slf4j
@Service
public class OverallPlanCaseServiceImpl implements OverallPlanCaseService {


    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private TerminalVoService terminalVoService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private OverPlanCaseRepository overPlanCaseRepository;

    @Resource
    private OverPlanDepartmentRepository overPlanDepartmentRepository;

    @Resource
    private OverPlanProductRepository overPlanProductRepository;

    @Resource
    private OverPlanScopeRepository overPlanScopeRepository;

    @Resource
    private OverPlanRepository overPlanRepository;

    @Resource
    private RedisService redisService;

    @Resource
    private ITpmStagingSchemeService tpmStagingSchemeService;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Resource
    private GenerateCodeService generateCodeService;

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private OrgVoService orgVoService;

    @Autowired
    private OverallPlanService overallPlanService;

    @Resource
    private PlanCaseUnderTakeComponent planCaseUnderTakeComponent;
    @Autowired
    private PlanClosureRepository planClosureRepository;
    @Autowired
    private PlanClosureDetailRepository planClosureDetailRepository;


    /**
     * 保存明细
     *
     * @param vo
     * @param schemeCode
     */
    @Override
    @Transactional(rollbackOn = Exception.class)
    public void saveBatchList(OverallPlanVo vo, String schemeCode) {
        //2.保存费用部门
        List<OverallPlanDepartment> departmentList = (List<OverallPlanDepartment>) nebulaToolkitService.copyCollectionByWhiteList(vo.getDepartmentList(),
                OverallPlanDepartmentVo.class, OverallPlanDepartment.class, HashSet.class, ArrayList.class);
        departmentList.forEach(x -> x.setSchemeCode(schemeCode));
        overPlanDepartmentRepository.saveBatch(departmentList);
        //3.保存细案
        List<OverallPlanScope> scopeList = Lists.newArrayList();
        List<OverallPlanProduct> productList = Lists.newArrayList();
        List<OverallPlanDepartment> bearDepartmentList = Lists.newArrayList();
        List<OverallPlanCase> caseList = Lists.newArrayList();
        List<String> schemeDetailCodeList = null;
        if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(vo.getSchemeType())) {
            schemeDetailCodeList = generateCodeService.generateCode(OverallPlanConstant.HEAD_SCHEME_DETAIL_CODE_RULE, vo.getCaseList().size());
        } else {
            String errMsg = checkUnderTakeAmount(vo.getCaseList());
            Validate.isTrue(ObjectUtils.isEmpty(errMsg), errMsg);
            schemeDetailCodeList = generateCodeService.generateCode(OverallPlanConstant.REGION_SCHEME_DETAIL_CODE_RULE, vo.getCaseList().size());
        }
        Integer index = 0;
        for (OverallPlanCaseVo detailCase : vo.getCaseList()) {
            String schemeDetailCode = schemeDetailCodeList.get(index++);
            OverallPlanCase planCase = nebulaToolkitService.copyObjectByWhiteList(detailCase, OverallPlanCase.class, HashSet.class, ArrayList.class);
            planCase.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            planCase.setSchemeCode(schemeCode);
            planCase.setSchemeDetailCode(schemeDetailCode);
            caseList.add(planCase);
            //细案上面的承接部门
            List<OverallPlanDepartment> list1 = (List<OverallPlanDepartment>) nebulaToolkitService.copyCollectionByWhiteList(detailCase.getBearDepartmentList(),
                    OverallPlanDepartmentVo.class, OverallPlanDepartment.class, HashSet.class, ArrayList.class);
            list1.forEach(x -> {
                x.setSchemeCode(schemeCode);
                x.setSchemeDetailCode(schemeDetailCode);
            });
            bearDepartmentList.addAll(list1);
            //细案上面的产品范围
            if (CollectionUtils.isNotEmpty(detailCase.getProductAndItemList())) {
                List<OverallPlanProduct> list2 = (List<OverallPlanProduct>) nebulaToolkitService.copyCollectionByWhiteList(detailCase.getProductAndItemList(),
                        OverallPlanProductVo.class, OverallPlanProduct.class, HashSet.class, ArrayList.class);
                list2.forEach(x -> {
                    x.setSchemeCode(schemeCode);
                    x.setSchemeDetailCode(schemeDetailCode);
                });
                productList.addAll(list2);
            }
            //细案上面的客户、终端范围
            if (CollectionUtils.isNotEmpty(detailCase.getScopeList())) {
                List<OverallPlanScope> list3 = (List<OverallPlanScope>) nebulaToolkitService.copyCollectionByWhiteList(detailCase.getScopeList(),
                        OverallPlanScopeVo.class, OverallPlanScope.class, HashSet.class, ArrayList.class);
                list3.forEach(x -> {
                    x.setSchemeCode(schemeCode);
                    x.setSchemeDetailCode(schemeDetailCode);
                });
                scopeList.addAll(list3);
            }
        }
        overPlanCaseRepository.saveBatch(caseList);
        overPlanProductRepository.saveBatch(productList);
        overPlanDepartmentRepository.saveBatch(bearDepartmentList);
        overPlanScopeRepository.saveBatch(scopeList);
    }


    private String checkUnderTakeAmount(List<OverallPlanCaseVo> caseList) {
        caseList = caseList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getHeadSchemeDetailCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(caseList)) return null;
        Map<String, BigDecimal> caseMap = caseList.stream().collect(Collectors.groupingBy(OverallPlanCaseVo::getHeadSchemeDetailCode,
                Collectors.mapping(OverallPlanCaseVo::getApplyAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Set<String> releaseDetailCodes = caseMap.keySet();
        List<OverallPlanCaseVo> overallPlanCaseVoList = overallPlanService.findOverallPlanCaseListBySchemeDetailCodes(Lists.newArrayList(releaseDetailCodes));
        StringJoiner errMsg = new StringJoiner(";");
        //承接的指引方案明细
        for (OverallPlanCaseVo caseVo : overallPlanCaseVoList) {
            BigDecimal applyAmount = caseMap.get(caseVo.getSchemeDetailCode());
            //承接明细金额-营销方案承接金额
            BigDecimal differenceAmount = caseVo.getApplyAmount().subtract(applyAmount);
            //未承接完成 剩余金额
            if (differenceAmount.compareTo(BigDecimal.ZERO) == 1) {
                errMsg.add(String.format("总部指引明细%s未承接完成,剩余可承接金额%s", caseVo.getSchemeDetailCode(), differenceAmount));
            }
            //承接超额
            if (differenceAmount.compareTo(BigDecimal.ZERO) == -1) {
                errMsg.add(String.format("总部指引明细%s承接超额,超额%s", caseVo.getSchemeDetailCode(), differenceAmount));
            }
        }
        return errMsg.toString();
    }

    @Override
    public List<OverallPlanCaseVo> checkCaseList(List<OverallPlanCaseVo> list, String schemeType) {
        String templateCode = null;
        if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(schemeType)) {
            templateCode = OverallPlanConstant.HEAD_OVERALL_PLAN_TEMPLATE;
        } else if (OverallPlanSchemeTypeEnum.REGION.getCode().equals(schemeType)) {
            templateCode = OverallPlanConstant.REGION_OVERALL_PLAN_TEMPLATE;
        }
        //获取配置模板信息
        List<ActivitiesTemplateConfigDetailVo> templateFiledList = checkHelper.getTemplateFiled(templateCode);
        Map<String, Boolean> filedMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getRequired));
        Map<String, String> chineseMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getTitle));

        List<String> categoryCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCategoryCode()))
                .map(OverallPlanCaseVo::getCategoryCode).collect(Collectors.toList());
        List<String> detailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getDetailCode()))
                .map(OverallPlanCaseVo::getDetailCode).collect(Collectors.toList());
        List<String> bearDepartmentCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getBearDepartmentCode()))
                .map(OverallPlanCaseVo::getBearDepartmentCode).collect(Collectors.toList());
        List<String> salesBearDepartmentCodes = list.stream().filter(x -> CollectionUtils.isNotEmpty(x.getBearDepartmentList()))
                .map(x -> x.getBearDepartmentList())
                .flatMap(List::stream).map(OverallPlanDepartmentVo::getDepartmentCode).collect(Collectors.toList());
        List<String> costCenterCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCostCenterCode()))
                .map(OverallPlanCaseVo::getCostCenterCode).collect(Collectors.toList());

        //承接数据
        List<String> releaseDetailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getHeadSchemeDetailCode()))
                .map(OverallPlanCaseVo::getHeadSchemeDetailCode).distinct().collect(Collectors.toList());
        Map<String, OverallPlanCaseVo> bearCaseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(releaseDetailCodes)) {
            List<OverallPlanCaseVo> bearCaseList = overallPlanService.findOverallPlanCaseListBySchemeDetailCodes(releaseDetailCodes);
            bearCaseMap = bearCaseList.stream().collect(Collectors.toMap(OverallPlanCaseVo::getSchemeDetailCode, Function.identity()));
        }

        Map<String, String> secondSubjectMap = checkHelper.findSecondBudgetSubject();
        Map<String, CostTypeCategoryVo> categoryMap = checkHelper.findCategoryMap(categoryCodes);
        Map<String, CostTypeDetailVo> detailMap = checkHelper.findDetailMap(detailCodes);
        Set<String> orgCodeSet = Sets.newHashSet();
        orgCodeSet.addAll(bearDepartmentCodes);
        orgCodeSet.addAll(salesBearDepartmentCodes);
        List<String> orgCodes = Lists.newArrayList(orgCodeSet);
        Map<String, OrgVo> orgMap = checkHelper.findOrgMap(orgCodes);
        List<MdmCostCenterOrgVo> centerOrgVos = checkHelper.findCostCenterOrgVoMap(costCenterCodes);
        Map<String, String> costCenterMap = checkHelper.findCostCenterMap(costCenterCodes);
        //记录多次承接的指引map
        Map<String, BigDecimal> underTakeAmountMap = Maps.newHashMap();
        for (OverallPlanCaseVo caseVo : list) {
            StringJoiner errMsg = new StringJoiner(";");
            if (bearCaseMap.containsKey(caseVo.getHeadSchemeDetailCode())) {
                caseVo.setBearCase(bearCaseMap.get(caseVo.getHeadSchemeDetailCode()));
            }
            //校验必填字段
            this.checkMustNeedFiled(caseVo, errMsg, categoryMap, detailMap, orgMap, costCenterMap, secondSubjectMap,centerOrgVos);
            //校验关联关系
            this.checkRelationship(caseVo, errMsg);
            //校验数据字典关系
            this.checkDictData(caseVo, errMsg);
            //此处单独调用
            String msg = BusinessRequiredUtil.filedRequiredCheck(filedMap, chineseMap, caseVo);
            if (ObjectUtils.isNotEmpty(msg)) {
                errMsg.add(msg);
            }
            if (ObjectUtils.isEmpty(errMsg.toString())) {
                caseVo.setCheckFlag(Boolean.TRUE);
                caseVo.setErrMsg(null);
            } else {
                caseVo.setCheckFlag(Boolean.FALSE);
                caseVo.setErrMsg(errMsg.toString());
            }
            //判断是承接的 并且是没有错误数据的 再重新走一次承接验证
            if (ObjectUtils.isNotEmpty(caseVo.getHeadSchemeDetailCode()) && caseVo.getCheckFlag()) {
                planCaseUnderTakeComponent.checkRegionUnderTakeCase(caseVo, underTakeAmountMap);
            }
        }
        return list;
    }


    /**
     * 查询方案明细
     *
     * @param id
     * @return
     */
    @Override
    public List<OverallPlanCaseVo> findListBySchemeId(String id) {
        List<OverallPlanCaseVo> planCaseVoList = null;
        OverallPlan plan = overPlanRepository.findById(id);
        if (StringUtils.equalsAny(plan.getProcessStatus(), ProcessStatusEnum.PREPARE.getDictCode(), ProcessStatusEnum.REJECT.getDictCode(),
                ProcessStatusEnum.RECOVER.getDictCode())) {
            String redisKey = OverallPlanConstant.getRedisKey(plan.getId());
            Boolean flag = redisService.hasKey(redisKey);
            OverallPlanVo vo = null;
            if (flag) {
                String jsonStr = (String) redisService.get(redisKey);
                vo = JSONObject.parseObject(jsonStr, OverallPlanVo.class);
            } else {
                vo = tpmStagingSchemeService.getJsonStr(new TpmStagingSchemeVo() {{
                    this.setReleaseId(plan.getId());
                    this.setFromType(plan.getSchemeType());
                }}, OverallPlanVo.class);
            }
            planCaseVoList = vo.getCaseList();
        } else {
            //1.查询部门（方案归属部门、细案承接部门）
            List<OverallPlanDepartment> allDepartmentList = overPlanDepartmentRepository.findListBySchemeCodeList(Lists.newArrayList(plan.getSchemeCode()));
            List<OverallPlanDepartment> bearDepartmentList = allDepartmentList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getSchemeDetailCode())).collect(Collectors.toList());
            //2.查询细案
            List<OverallPlanCase> caseList = overPlanCaseRepository.findListBySchemeCode(plan.getSchemeCode());
            //3.查询产品
            List<OverallPlanProduct> productList = overPlanProductRepository.findListBySchemeCode(plan.getSchemeCode());
            //4.查询范围
            List<OverallPlanScope> scopeList = overPlanScopeRepository.findListBySchemeCode(plan.getSchemeCode());

            planCaseVoList = (List<OverallPlanCaseVo>) nebulaToolkitService.copyCollectionByWhiteList(caseList, OverallPlanCase.class,
                    OverallPlanCaseVo.class, HashSet.class, ArrayList.class);
            //参数组装
            checkHelper.buildOverallPlanCase(planCaseVoList, bearDepartmentList, productList, scopeList);

        }
        List<String> headSchemeDetailCodes = planCaseVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getHeadSchemeDetailCode()))
                .map(OverallPlanCaseVo::getHeadSchemeDetailCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(headSchemeDetailCodes)) {
            List<OverallPlanCaseVo> overallPlanCaseVoList = this.overallPlanService.findOverallPlanCaseListBySchemeDetailCodes(headSchemeDetailCodes);
            Map<String, OverallPlanCaseVo> overallPlanCaseVoMap = overallPlanCaseVoList.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), Function.identity()));
            for (OverallPlanCaseVo vo : planCaseVoList) {
                if (ObjectUtils.isNotEmpty(vo.getHeadSchemeDetailCode()) && overallPlanCaseVoMap.containsKey(vo.getHeadSchemeDetailCode())) {
                    vo.setBearCase(overallPlanCaseVoMap.get(vo.getHeadSchemeDetailCode()));
                }
            }
        }
        //先按照是否验证排序 在按照时间戳排序
        planCaseVoList = planCaseVoList.stream().sorted(Comparator.comparing(OverallPlanCaseVo::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(OverallPlanCaseVo::getCheckFlag, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
        return planCaseVoList;
    }

    @Override
    public List<OverallPlanCase> findListByReleaseDetailNotNull(String schemeCode) {
        return overPlanCaseRepository.findListByReleaseDetailNotNull(schemeCode);
    }

    @Override
    public void deleteBySchemeCodes(List<String> schemeCodes) {
        overPlanCaseRepository.deleteBySchemeCodes(schemeCodes);
    }

    @Override
    public Page<OverallPlanCaseVo> findHeadOverallPlanCaseList(Pageable pageable, HeadOverallPlanCaseVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<HeadOverallPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        String orgCode = vo.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
            orgCode = loginDetails.getOrgCode();
        }
        Set<String> orgCodeSet = Sets.newHashSet(orgCode);
        List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(orgCode);
        // 当前组织及其下级也能查到
        List<OrgVo> childrenList = orgVoService.findAllChildrenByOrgCode(orgCode);
        if (CollectionUtils.isNotEmpty(orgVoList)) {
            orgCodeSet.addAll(orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet()));
        }
        if (CollectionUtils.isNotEmpty(childrenList)) {
            orgCodeSet.addAll(childrenList.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet()));
        }
        vo.setOrgCodeSet(orgCodeSet);
        Page<OverallPlanCaseVo> data = overPlanCaseRepository.findHeadOverallPlanCaseList(page, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> headSchemeDetailCodes = data.getRecords().stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, BigDecimal> map = findAlreadyUndertakeAmount(headSchemeDetailCodes);
            if (ObjectUtils.isNotEmpty(map)) {
                for (OverallPlanCaseVo record : data.getRecords()) {
                    record.setUndertakeAmount(record.getApplyAmount());
                    record.setUndertakeCompleteFlag(BooleanEnum.FALSE.getCapital());
                    if (map.containsKey(record.getSchemeDetailCode())) {
                        BigDecimal usedAmount = map.get(record.getSchemeDetailCode());
                        record.setUndertakeAmount(record.getApplyAmount().subtract(usedAmount));
                    }
                    if (record.getUndertakeAmount().compareTo(BigDecimal.ZERO) < 1) {
                        record.setUndertakeCompleteFlag(BooleanEnum.TRUE.getCapital());
                    }
                }
            }
            List<String> schemeDetailCodes = data.getRecords().stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<OverallPlanDepartment> departmentList = overPlanDepartmentRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanProduct> productList = overPlanProductRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanScope> scopeList = overPlanScopeRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            //参数组装
            checkHelper.buildOverallPlanCase(data.getRecords(), departmentList, productList, scopeList);
        }
        return data;
    }


    @Override
    public Page<OverallPlanCaseVo> findHeadOverallPlanCaseToMarketing(Pageable pageable, HeadOverallPlanCaseVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<HeadOverallPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        String orgCode = vo.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
            orgCode = loginDetails.getOrgCode();
        }
        Set<String> orgCodeSet = Sets.newHashSet(orgCode);
//        List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(orgCode);
        // 当前组织及其下级也能查到
        List<OrgVo> childrenList = orgVoService.findAllChildrenByOrgCode(orgCode);
//        if (CollectionUtils.isNotEmpty(orgVoList)) {
//            orgCodeSet.addAll(orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet()));
//        }
        if (CollectionUtils.isNotEmpty(childrenList)) {
            orgCodeSet.addAll(childrenList.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet()));
        }
        vo.setOrgCodeSet(orgCodeSet);
        Page<OverallPlanCaseVo> data = overPlanCaseRepository.findHeadOverallPlanCaseToMarketing(page, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> headSchemeDetailCodes = data.getRecords().stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, BigDecimal> map = findAlreadyUndertakeAmount(headSchemeDetailCodes);
            if (ObjectUtils.isNotEmpty(map)) {
                for (OverallPlanCaseVo record : data.getRecords()) {
                    record.setUndertakeAmount(record.getApplyAmount());
                    record.setUndertakeCompleteFlag(BooleanEnum.FALSE.getCapital());
                    if (map.containsKey(record.getSchemeDetailCode())) {
                        BigDecimal usedAmount = map.get(record.getSchemeDetailCode());
                        record.setUndertakeAmount(record.getApplyAmount().subtract(usedAmount));
                    }
                    if (record.getUndertakeAmount().compareTo(BigDecimal.ZERO) < 1) {
                        record.setUndertakeCompleteFlag(BooleanEnum.TRUE.getCapital());
                    }
                }
            }
            List<String> schemeDetailCodes = data.getRecords().stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<OverallPlanDepartment> departmentList = overPlanDepartmentRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanProduct> productList = overPlanProductRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanScope> scopeList = overPlanScopeRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            //参数组装
            checkHelper.buildOverallPlanCase(data.getRecords(), departmentList, productList, scopeList);
        }
        return data;
    }

    /**
     * 查询已经承接的金额
     *
     * @param headSchemeDetailCodes
     * @return
     */
    public Map<String, BigDecimal> findAlreadyUndertakeAmount(List<String> headSchemeDetailCodes) {
        List<OverallPlanCaseVo> allList = Lists.newArrayList();
        List<OverallPlanCaseVo> caseVoList = overPlanCaseRepository.findAlreadyUndertakeAmount(headSchemeDetailCodes);
        if (CollectionUtils.isNotEmpty(caseVoList)) {
            allList.addAll(caseVoList);
        }
        TpmStagingSchemeVo schemeVo = new TpmStagingSchemeVo();
        schemeVo.setFromType(OverallPlanSchemeTypeEnum.REGION.getCode());
        List<OverallPlanVo> planVoList = tpmStagingSchemeService.getJSONStrList(schemeVo, OverallPlanVo.class);
        if (CollectionUtils.isNotEmpty(planVoList)) {
            List<OverallPlanCaseVo> list = planVoList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getCaseList()))
                    .map(x -> x.getCaseList()).flatMap(List::stream)
                    .filter(x -> ObjectUtils.isNotEmpty(x.getApplyAmount()) && ObjectUtils.isNotEmpty(x.getHeadSchemeDetailCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                allList.addAll(list);
            }
        }
        if (CollectionUtils.isNotEmpty(allList)) {
            return allList.stream().collect(Collectors.groupingBy(x -> x.getHeadSchemeDetailCode(),
                    Collectors.mapping(x -> x.getApplyAmount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        }
        return Maps.newHashMap();
    }


    /**
     * 查询审批通过的方案明细列表
     *
     * @param schemeDetailCodes
     * @return
     */
    @Override
    public List<OverallPlanCase> findCaseListBySchemeDetailCodes(List<String> schemeDetailCodes, String processStatus) {
        return overPlanCaseRepository.findCaseListBySchemeDetailCodes(schemeDetailCodes, processStatus);
    }

    @Override
    public List<BearOverallPlanCaseVo> findReleaseHeadCaseList(List<String> schemeDetailCodes) {
        return overPlanCaseRepository.findReleaseHeadCaseList(schemeDetailCodes);
    }

    @Override
    public List<OverallPlanCase> findAlreadyBearCase(List<String> schemeDetailCodes) {
        return overPlanCaseRepository.findAlreadyBearCase(schemeDetailCodes);
    }

    @Override
    public List<BearOverallPlanCaseVo> findReleaseCaseByMarketingPlan(List<String> schemeDetailCodes) {
        return overPlanCaseRepository.findReleaseCaseByMarketingPlan(schemeDetailCodes);
    }

    /**
     * 查询方案未关闭的明细
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<OverallPlanCaseVo> findNotCaseBySchemeTypeList(Pageable pageable, OverallPlanCaseVo vo, String schemeType) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
        Page<OverallPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        vo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
//        vo.setStartDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY)));
        Page<OverallPlanCaseVo> data = overPlanCaseRepository.findNotCaseBySchemeTypeList(page, vo, schemeType);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeDetailCodes = data.getRecords().stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<OverallPlanDepartment> departmentList = overPlanDepartmentRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanProduct> productList = overPlanProductRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanScope> scopeList = overPlanScopeRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            //参数组装
            checkHelper.buildOverallPlanCase(data.getRecords(), departmentList, productList, scopeList);
        }
        return data;
    }


    /**
     * 查询总部已承接的明细编码
     *
     * @return
     */
    @Override
    public Set<String> findAlreadyUnderTakeCaseList(String schemeType) {
        Set<String> caseCodeList = Sets.newHashSet();
        if (OverallPlanSchemeTypeEnum.HEAD.getCode().equals(schemeType)) {
            Set<String> codeSet = this.overPlanCaseRepository.findAlreadyUnderTakeCaseList(schemeType);
            if (CollectionUtils.isNotEmpty(codeSet)) {
                caseCodeList.addAll(codeSet);
            }
            TpmStagingSchemeVo schemeVo = new TpmStagingSchemeVo();
            schemeVo.setFromType(OverallPlanSchemeTypeEnum.HEAD.getCode());
            List<OverallPlanVo> overallPlanVos = tpmStagingSchemeService.getJSONStrList(schemeVo, OverallPlanVo.class);
            if (CollectionUtils.isNotEmpty(overallPlanVos)) {
                Set<String> codeSet1 = overallPlanVos.stream().filter(x -> CollectionUtils.isNotEmpty(x.getCaseList()))
                        .map(x -> x.getCaseList()).flatMap(List::stream).filter(x -> ObjectUtils.isNotEmpty(x.getHeadSchemeDetailCode()))
                        .map(x -> x.getHeadSchemeDetailCode()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(codeSet1)) {
                    caseCodeList.addAll(codeSet1);
                }
            }
        } else {
            Set<String> codeSet = this.overPlanCaseRepository.findAlreadyUnderTakeCaseList(schemeType);
            if (CollectionUtils.isNotEmpty(codeSet)) {
                caseCodeList.addAll(codeSet);
            }
        }
        return caseCodeList;
    }

    /**
     * 修改方案明细状态
     *
     * @param schemeDetailCodes
     * @param delFlag
     */
    @Override
    public void updateCaseStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String delFlag) {
        overPlanCaseRepository.updateCaseStatusBySchemeDetailCodes(schemeDetailCodes, delFlag);
    }


    /**
     * 查询总部承接的数据
     *
     * @param pageable
     * @param headSchemeDetailCode
     * @return
     */
    @Override
    public Page<OverallPlanCaseVo> findRegionOverallPlanCase(Pageable pageable, String headSchemeDetailCode) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<OverallPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<OverallPlanCaseVo> data = overPlanCaseRepository.findRegionOverallPlanCase(page, headSchemeDetailCode);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeDetailCodes = data.getRecords().stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<OverallPlanDepartment> departmentList = overPlanDepartmentRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanProduct> productList = overPlanProductRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanScope> scopeList = overPlanScopeRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<PlanClosureDetail> planClosureDetails =  planClosureDetailRepository.findBySchemeDetailCodeList(schemeDetailCodes);
            List<String> closeCodes = planClosureDetails.stream().map(PlanClosureDetail::getCloseCode).distinct().collect(Collectors.toList());
            List<PlanClosure> planClosures = planClosureRepository.findByCloseCodeList(closeCodes);
            Map<String, PlanClosureDetail> closureDetailMap = planClosureDetails.stream().collect(Collectors.toMap(PlanClosureDetail::getSchemeDetailCode, Function.identity(), (v1, v2) -> v1));
            Map<String, PlanClosure> closureMap = planClosures.stream().collect(Collectors.toMap(PlanClosure::getCloseCode, Function.identity(), (v1, v2) -> v1));
            //参数组装
            checkHelper.buildOverallPlanCase(data.getRecords(), departmentList, productList, scopeList);
            data.getRecords().forEach(e -> {
                PlanClosureDetail planClosureDetail = closureDetailMap.get(e.getSchemeDetailCode());
                if (null != planClosureDetail) {
                    String closeCode = planClosureDetail.getCloseCode();
                    e.setCloseCode(closeCode);
                    PlanClosure planClosure = closureMap.get(closeCode);
                    if (null != planClosure) {
                        e.setCloseProcessStatus(planClosure.getProcessStatus());
                    }
                }
            });
        }
        return data;
    }


    /**
     * 通过方案编码查询方案明细列表(部分字段)
     *
     * @param schemeCodes
     * @return
     */
    @Override
    public List<OverallPlanCaseVo> findCasePartFiledListBySchemeCodes(List<String> schemeCodes) {
        List<OverallPlanCase> list = overPlanCaseRepository.findPartFiledListBySchemeCodes(schemeCodes);
        if (CollectionUtils.isNotEmpty(list)) {
            return (List<OverallPlanCaseVo>) nebulaToolkitService.copyCollectionByWhiteList(list, OverallPlanCase.class, OverallPlanCaseVo.class,
                    HashSet.class, ArrayList.class);
        }
        return Lists.newArrayList();
    }


    /**
     * 查询方案明细列表
     *
     * @param schemeCode
     * @return
     */
    @Override
    public List<OverallPlanCaseVo> findCaseListBySchemeCode(String schemeCode) {
        List<OverallPlanCase> caseList = overPlanCaseRepository.findCaseListBySchemeCode(schemeCode);
        if (CollectionUtils.isEmpty(caseList)) {
            return Lists.newArrayList();
        }
        List<OverallPlanCaseVo> caseVoList = (List<OverallPlanCaseVo>) nebulaToolkitService.copyCollectionByWhiteList(caseList, OverallPlanCase.class, OverallPlanCaseVo.class,
                HashSet.class, ArrayList.class);
        List<String> schemeDetailCodes = caseVoList.stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
        List<OverallPlanDepartment> departmentList = overPlanDepartmentRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
        List<OverallPlanProduct> productList = overPlanProductRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
        List<OverallPlanScope> scopeList = overPlanScopeRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
        checkHelper.buildOverallPlanCase(caseVoList, departmentList, productList, scopeList);
        return caseVoList;
    }

    @Resource
    private MarketingPlanCaseService marketingPlanCaseService;

    /**
     * 查询可承接的方案明细列表-总部、大区
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<OverallPlanCaseVo> findBearPlanCaseList(Pageable pageable, OverallPlanCaseVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<OverallPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Set<String> orgCodes = Sets.newHashSet();
        //编辑和查看
        String orgCode = vo.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            //获取登陆信息
            FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
            orgCode = loginDetails.getOrgCode();
        }
        orgCodes.add(orgCode);
        List<OrgVo> orgVoList = orgVoService.findAllParentByOrgCode(orgCode);
        List<OrgVo> children = orgVoService.findAllChildrenByOrgCode(orgCode);
        if (CollectionUtils.isNotEmpty(orgVoList)) {
            orgCodes.addAll(orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet()));
        }
        if (CollectionUtils.isNotEmpty(children)) {
            orgCodes.addAll(children.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet()));
        }
        Page<OverallPlanCaseVo> data = overPlanCaseRepository.findBearPlanCaseList(page, vo, orgCodes);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> releaseDetailCodes = data.getRecords().stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, BigDecimal> map = marketingPlanCaseService.findAlreadyUndertakeAmount(releaseDetailCodes);
            List<String> detailCodes = data.getRecords().stream().filter(x -> ObjectUtils.isNotEmpty(x.getDetailCode()))
                    .map(OverallPlanCaseVo::getDetailCode).distinct().collect(Collectors.toList());
            Map<String, Set<String>> payBysMap = checkHelper.findCostTypeDetailByDetailCodes(detailCodes);

            for (OverallPlanCaseVo record : data.getRecords()) {
                record.setUndertakeAmount(record.getApplyAmount());
                if (map.containsKey(record.getSchemeDetailCode())) {
                    BigDecimal usedAmount = map.get(record.getSchemeDetailCode());
                    record.setUndertakeAmount(record.getApplyAmount().subtract(usedAmount));
                }
                if (ObjectUtils.isNotEmpty(record.getDetailCode()) && payBysMap.containsKey(record.getDetailCode())) {
                    record.setPayBys(payBysMap.get(record.getDetailCode()));
                }
            }
            List<String> schemeDetailCodes = data.getRecords().stream().map(OverallPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            List<OverallPlanDepartment> departmentList = overPlanDepartmentRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanProduct> productList = overPlanProductRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            List<OverallPlanScope> scopeList = overPlanScopeRepository.findListBySchemeDetailCodeList(schemeDetailCodes);
            checkHelper.buildOverallPlanCase(data.getRecords(), departmentList, productList, scopeList);
        }
        return data;
    }


    /**
     * 查询当前登录人可承接的大区指引方案
     */
    @Override
    public List<OverallPlanCaseVo> checkRegionOverall(Set<String> orgCodeSet, String schemeType, String years, String detailFlag) {
        return overPlanCaseRepository.checkRegionOverall(orgCodeSet, schemeType, years, detailFlag);
    }


    /**
     * 查询组织下的总部指引明细
     *
     * @param orgCodes
     * @return
     */
    @Override
    public List<OverallPlanCase> findHeadPlanCaseListByOrgCodes(List<String> orgCodes, String years, String bearFlag, String schemeCode) {
        List<OverallPlanCase> headCaseList = overPlanCaseRepository.findHeadPlanCaseListByOrgCodes(orgCodes, years, bearFlag);
        if (CollectionUtils.isNotEmpty(headCaseList)) {
            List<String> headSchemeDetailCodes = headCaseList.stream().map(x -> x.getSchemeDetailCode()).collect(Collectors.toList());
            List<OverallPlanCase> underTakeCaseList = overPlanCaseRepository.findUnderTakeCaseList(headSchemeDetailCodes, schemeCode);
            if (CollectionUtils.isNotEmpty(underTakeCaseList)) {
                Map<String, BigDecimal> underTakeMap = underTakeCaseList.stream().collect(Collectors.toMap(x -> x.getHeadSchemeDetailCode(), x -> x.getApplyAmount()));
                for (OverallPlanCase planCase : headCaseList) {
                    if (underTakeMap.containsKey(planCase.getSchemeDetailCode())) {
                        BigDecimal usedAmount = underTakeMap.get(planCase.getSchemeDetailCode());
                        planCase.setApplyAmount(planCase.getApplyAmount().subtract(usedAmount));
                    }
                }
            }

        }
        return headCaseList;
    }

    @Override
    public List<OverallPlanCase> findCaseListByReleaseDetailCodes(List<String> releaseDetailCode, String processStatus) {
        return overPlanCaseRepository.findCaseListByReleaseDetailCodes(releaseDetailCode, processStatus);
    }

    /**
     * 验证必填字段
     *
     * @param vo
     * @param errMsg
     */
    private void checkMustNeedFiled(OverallPlanCaseVo vo, StringJoiner errMsg, Map<String, CostTypeCategoryVo> categoryMap, Map<String, CostTypeDetailVo> detailMap,
                                    Map<String, OrgVo> orgMap, Map<String, String> costCenterMap, Map<String, String> secondSubjectMap,List<MdmCostCenterOrgVo> centerOrgVos) {
        if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getSecondCostCategory(), "二级费用大类编码", errMsg)) {
            if (secondSubjectMap.containsKey(vo.getSecondCostCategory())) {
                vo.setSecondCostCategoryName(secondSubjectMap.get(vo.getSecondCostCategory()));
            } else {
                errMsg.add(String.format("二级费用大类编码%s未查询到", vo.getSecondCostCategory()));
            }
        }

        String costCenterCode =vo.getCostCenterCode();
        String bearDepartmentCode = vo.getBearDepartmentCode();
        if(StringUtils.isNotBlank(costCenterCode)&&StringUtils.isNotBlank(bearDepartmentCode)){
            if(CollectionUtils.isEmpty(centerOrgVos)){
                errMsg.add("费用承担部门与成本中心不一致");
            }else{
                centerOrgVos = centerOrgVos.stream().filter(o -> costCenterCode.equals(o.getCostCenterCode())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(centerOrgVos)){
                    errMsg.add("费用承担部门与成本中心不一致");
                }else {
                    AtomicReference<Boolean> flag = new AtomicReference(false);
                    centerOrgVos.forEach(o -> {
                        if (o.getOrgCode().equals(bearDepartmentCode)) {
                            flag.set(true);
                        }
                    });
                    if (!flag.get()) {
                        errMsg.add("费用承担部门与成本中心不一致");
                    }
                }
            }
        }

        if (ObjectUtils.isNotEmpty(vo.getCategoryCode())) {
            if (categoryMap.containsKey(vo.getCategoryCode())) {
                CostTypeCategoryVo categoryVo = categoryMap.get(vo.getCategoryCode());
                Validate.notNull(categoryVo.getBudgetSubjectsCode(), String.format("活动大类%s未绑定预算科目", categoryVo.getCategoryName()));
                vo.setCategoryName(categoryVo.getCategoryName());
                vo.setBudgetSubjectCode(categoryVo.getBudgetSubjectsCode());
                vo.setBudgetSubjectName(categoryVo.getBudgetSubjectsName());
            } else {
                errMsg.add(String.format("活动大类编码%s未查询到", vo.getCategoryCode()));
            }
        }
        if (ObjectUtils.isNotEmpty(vo.getDetailCode())) {
            if (detailMap.containsKey(vo.getDetailCode())) {
                vo.setDetailName(detailMap.get(vo.getDetailCode()).getDetailName());
            } else {
                errMsg.add(String.format("活动细类编码%s未查询到", vo.getDetailCode()));
            }
        }

        if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getStartDate(), "活动开始时间", errMsg)) {
            try {
                LocalDate.parse(vo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            } catch (Exception e) {
                errMsg.add("活动开始时间格式错误,正确格式:yyyy-MM-dd");
            }
        }
        if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getEndDate(), "活动结束时间", errMsg)) {
            try {
                LocalDate.parse(vo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            } catch (Exception e) {
                errMsg.add("活动结束时间格式错误,正确格式:yyyy-MM-dd");
            }
        }
        if (ObjectUtils.isNotEmpty(vo.getStartDate()) && ObjectUtils.isNotEmpty(vo.getEndDate())) {
            LocalDate startDate = LocalDate.parse(vo.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            LocalDate endDate = LocalDate.parse(vo.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH_DAY));
            vo.setYears(startDate.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH)));
            if (startDate.isAfter(endDate)) {
                errMsg.add("活动开始时间不能晚于活动结束时间");
            }
        }

        if (MarketingPlanCaseCheckHelper.collectionNotNull("销售部门", errMsg, vo.getBearDepartmentList())) {
            for (OverallPlanDepartmentVo departmentVo : vo.getBearDepartmentList()) {
                if (MarketingPlanCaseCheckHelper.paramNotNull(departmentVo.getDepartmentCode(), "销售部门编码", errMsg)) {
                    if (orgMap.containsKey(departmentVo.getDepartmentCode())) {
                        OrgVo orgVo = orgMap.get(departmentVo.getDepartmentCode());
                        departmentVo.setDepartmentName(orgVo.getOrgName());
                    }else{
                        departmentVo.setDepartmentName(null);
                    }
                }
            }
        }

        if (ObjectUtils.isNotEmpty(vo.getBearDepartmentCode())) {
                if(orgMap.containsKey(vo.getBearDepartmentCode())){
                    vo.setBearDepartmentName(orgMap.get(vo.getBearDepartmentCode()).getOrgName());
                }else{
                    vo.setBearDepartmentName(null);
                }
        }

        if (ObjectUtils.isNotEmpty(vo.getBearType())) {
            //判断承接类型是总部承担
            if (OverallPlanBearTypeEnum.head.getCode().equals(vo.getBearType())) {
                MarketingPlanCaseCheckHelper.paramNotNull(vo.getCostCenterCode(), "成本中心", errMsg);
                MarketingPlanCaseCheckHelper.paramNotNull(vo.getBearDepartmentCode(), "费用承担部门", errMsg);
            }
        }

        if (ObjectUtils.isNotEmpty(vo.getCostCenterCode())) {
            if (!costCenterMap.containsKey(vo.getCostCenterCode())) {
                vo.setCostCenterName(null);
                errMsg.add(String.format("成本中心编码%s未查询到", vo.getCostCenterCode()));
            } else {
                vo.setCostCenterName(costCenterMap.get(vo.getCostCenterCode()));
            }
        }
        MarketingPlanCaseCheckHelper.paramNotNull(vo.getApplyAmount(), "预估费用", errMsg);
        if (Objects.nonNull(vo.getApplyAmount()) && vo.getApplyAmount().compareTo(BigDecimal.ZERO) <= 0) {
            errMsg.add("预估费用必须大于0");
        }
        if (Objects.nonNull(vo.getEstimatedSalesVolume()) && vo.getEstimatedSalesVolume().compareTo(BigDecimal.ZERO) <= 0) {
            errMsg.add("预估销售额不为空时必须大于0");
        }


    }


    /**
     * 校验关联关系
     *
     * @param vo
     * @param errMsg
     */
    private void checkRelationship(OverallPlanCaseVo vo, StringJoiner errMsg) {
        //校验客户终端关系
        List<OverallPlanScopeVo> scopeVoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(vo.getCustomerList())) {
            //取出客户编码
            List<String> customerCodeList = vo.getCustomerList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()))
                    .map(OverallPlanScopeVo::getCustomerCode).collect(Collectors.toList());
            List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodeList);
            if (CollectionUtils.isEmpty(customerVos)) {
                errMsg.add(String.format("客户信息%s查询不存在1", customerCodeList));
            } else {
                Map<String, CustomerVo> customerMap = customerVos.stream().filter(x -> EnableStatusEnum.ENABLE.getCode().equals(x.getEnableStatus())
                                && BooleanEnum.TRUE.getCapital().equals(x.getContractCustomer()))
                        .collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
                for (OverallPlanScopeVo planScopeVo : vo.getCustomerList()) {
                    if (customerMap.containsKey(planScopeVo.getCustomerCode())) {
                        CustomerVo customerVo = customerMap.get(planScopeVo.getCustomerCode());
                        OverallPlanScopeVo scopeVo = nebulaToolkitService.copyObjectByBlankList(customerVo, OverallPlanScopeVo.class, HashSet.class, ArrayList.class);
                        scopeVo.setType(OverallPlanScopeTypeEnum.cus.getCode());
                        scopeVoList.add(scopeVo);
                    } else {
                        errMsg.add(String.format("客户信息%s不存在或不是合同客户", planScopeVo.getCustomerCode()));
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(vo.getTerminalList()) && CollectionUtils.isNotEmpty(vo.getCustomerList())) {
            List<String> cusCodes = vo.getCustomerList().stream().map(OverallPlanScopeVo::getCustomerCode).collect(Collectors.toList());
            //取出终端编码
            List<String> terminalCodeList = vo.getTerminalList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getTerminalCode()))
                    .map(OverallPlanScopeVo::getTerminalCode).collect(Collectors.toList());
            List<TerminalVo> terminalVoList = terminalVoService.findTerminalAndRelaCusByTerminalCodeList(terminalCodeList);
            if (CollectionUtils.isEmpty(terminalVoList)) {
                errMsg.add(String.format("门店信息%s查询不存在1", terminalCodeList));
                vo.setTerminalList(null);
            } else {
                Map<String, TerminalVo> terminalMap = terminalVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getTerminalCode()))
                        .collect(Collectors.toMap(TerminalVo::getTerminalCode, v -> v, (n, o) -> n));
                for (OverallPlanScopeVo scopeVo : vo.getTerminalList()) {
                    if (terminalMap.containsKey(scopeVo.getTerminalCode())) {
                        TerminalVo terminalVo = terminalMap.get(scopeVo.getTerminalCode());
                        if (CollectionUtils.isEmpty(terminalVo.getRelaCusVos())) {
                            errMsg.add(String.format("门店编码%s不存在对应的客户1", scopeVo.getTerminalCode()));
                        } else {
                            boolean checkSuccess = Boolean.FALSE;
                            Map<String, OverallPlanScopeVo> scopeVoMap = Maps.newHashMap();
                            for (TerminalRelaCusVo relaCusVo : terminalVo.getRelaCusVos()) {
                                if (cusCodes.contains(relaCusVo.getCusCode())) {
                                    checkSuccess = Boolean.TRUE;
                                    OverallPlanScopeVo terminalScopeVo = new OverallPlanScopeVo();
                                    terminalScopeVo.setTerminalCode(terminalVo.getTerminalCode());
                                    terminalScopeVo.setTerminalName(terminalVo.getTerminalName());
                                    terminalScopeVo.setTerminalType(terminalVo.getTerminalType());
                                    terminalScopeVo.setType(OverallPlanScopeTypeEnum.terminal.getCode());
                                    scopeVoMap.put(terminalScopeVo.getTerminalCode(), terminalScopeVo);
                                }
                            }
                            if (CollectionUtil.isNotEmpty(scopeVoMap)) {
                                scopeVoList.addAll(scopeVoMap.values());
                            }
                            if (!checkSuccess) {
                                errMsg.add(String.format("门店编码%s不存在对应的客户2", scopeVo.getTerminalCode()));
                            }
                        }
                    } else {
                        errMsg.add(String.format("门店信息%s查询不存在2", scopeVo.getTerminalCode()));
                    }
                }
            }
        }
        vo.setScopeList(scopeVoList);
        //品项校验
        if (CollectionUtils.isNotEmpty(vo.getItemList())) {
            //取出品项
            List<String> itemList = vo.getItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                    .map(OverallPlanProductVo::getCode).collect(Collectors.toList());
            //品项信息
            Map<String, String> sapLevelMap = checkHelper.findSapLevelMap(itemList);
            //商品信息-通过品项查询
//            Map<String,ProductVo> productMap = checkHelper.findProductByItemCodes(itemList);

            if (sapLevelMap == null || sapLevelMap.isEmpty()) {
                vo.setProductList(null);
                errMsg.add(String.format("品项%s信息不存在", itemList));
            } else {
                List<OverallPlanProductVo> productAndItemList = Lists.newArrayList();
                for (OverallPlanProductVo itemVo : vo.getItemList()) {
                    if (sapLevelMap.containsKey(itemVo.getCode())) {
                        OverallPlanProductVo item = new OverallPlanProductVo();
                        item.setName(sapLevelMap.get(itemVo.getCode()));
                        item.setCode(itemVo.getCode());
                        item.setType(OverallPlanProductEnum.cal_item.getCode());
                        productAndItemList.add(item);
                    } else {
                        errMsg.add(String.format("品项信息%s查询不存在", itemVo.getCode()));
                    }
                }
                if (CollectionUtils.isNotEmpty(vo.getProductList())) {
                    List<String> productCodes = vo.getProductList().stream().map(OverallPlanProductVo::getCode).collect(Collectors.toList());
                    //查询商品
                    Map<String, ProductVo> productMap = checkHelper.findProductListByCodes(productCodes, null, itemList);
                    for (OverallPlanProductVo p : vo.getProductList()) {
                        if (productMap.containsKey(p.getCode())) {
                            ProductVo productVo = productMap.get(p.getCode());
                            OverallPlanProductVo product = new OverallPlanProductVo();
                            product.setCode(p.getCode());
                            product.setName(productVo.getProductName());
                            Validate.notNull(productVo.getMaterialCode(), String.format("商品%s未绑定物料信息", productVo.getProductName()));
                            product.setType(OverallPlanProductEnum.cal_product.getCode());
                            productAndItemList.add(product);
                        } else {
                            errMsg.add(String.format("当前商品%s不属于以上品项", p.getCode()));
                        }
                    }
                }
                vo.setProductAndItemList(productAndItemList);
            }
        }
    }

    /**
     * 数据字典校验
     *
     * @param vo
     * @param errMsg
     */
    private void checkDictData(OverallPlanCaseVo vo, StringJoiner errMsg) {
        //合作类型
        if (CollectionUtils.isNotEmpty(vo.getCooperateTypeList())) {
            Map<String, String> cooperateTypeMap = cooperateTypeMap();
            for (String s : vo.getCooperateTypeList()) {
                if (!cooperateTypeMap.containsKey(s)) {
                    errMsg.add(String.format("合作类型编码%s错误", s));
                }
            }
            String cooperateTypeStr = vo.getCooperateTypeList().stream().collect(Collectors.joining(","));
            vo.setCooperateTypeStr(cooperateTypeStr);
        }
        //客户标签
        if (CollectionUtils.isNotEmpty(vo.getCustomerTagList())) {
            Map<String, String> customerTagMap = customerTagMap();
            for (String s : vo.getCustomerTagList()) {
                if (!customerTagMap.containsKey(s)) {
                    errMsg.add(String.format("客户标签编码%s错误", s));
                }
            }
            String customerTagStr = vo.getCustomerTagList().stream().collect(Collectors.joining(","));
            vo.setCustomerTagStr(customerTagStr);
        }
        //终端标签
        if (CollectionUtils.isNotEmpty(vo.getTerminalTagList())) {
            Map<String, String> terminalTagMap = terminalTagMap();
            for (String s : vo.getTerminalTagList()) {
                if (!terminalTagMap.containsKey(s)) {
                    errMsg.add(String.format("终端标签%s错误", s));
                }
            }
            String terminalTagStr = vo.getTerminalTagList().stream().collect(Collectors.joining(","));
            vo.setTerminalTagStr(terminalTagStr);
        }
        // 渠道类型
        if (CollectionUtils.isNotEmpty(vo.getChannelTypeList())) {
            Map<String, String> channelTypeMap = channelTypeMap();
            for (String s : vo.getChannelTypeList()) {
                if (!channelTypeMap.containsKey(s)) {
                    errMsg.add(String.format("渠道类型编码%s错误", s));
                }
            }
            String channelTypeStr = vo.getChannelTypeList().stream().collect(Collectors.joining(","));
            vo.setChannelTypeStr(channelTypeStr);
        }
    }

    //合作类型
    private final static ThreadLocal<Map<String, String>> cooperateTypeThreadLocal = new ThreadLocal<>();
    //客户标签
    private final static ThreadLocal<Map<String, String>> customerTagThreadLocal = new ThreadLocal<>();
    //终端标签
    private final static ThreadLocal<Map<String, String>> terminalTagThreadLocal = new ThreadLocal<>();
    // 渠道类型
    private final static ThreadLocal<Map<String, String>> channelTypeThreadLocal = new ThreadLocal<>();


    /**
     * 合作类型标签
     *
     * @return
     */
    private Map<String, String> cooperateTypeMap() {
        Map<String, String> map = cooperateTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_COOPERATE_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            cooperateTypeThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 客户标签
     *
     * @return
     */
    private Map<String, String> customerTagMap() {
        Map<String, String> map = customerTagThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.CUSTOMER_TAG);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            customerTagThreadLocal.set(map);
        }
        return map;
    }


    private Map<String, String> terminalTagMap() {
        Map<String, String> map = terminalTagThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.TERMINAL_TAG);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            terminalTagThreadLocal.set(map);
        }
        return map;
    }

    /**
     * 渠道类型标签
     *
     * @return
     */
    private Map<String, String> channelTypeMap() {
        Map<String, String> map = channelTypeThreadLocal.get();
        if (ObjectUtils.isEmpty(map)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.CHANNEL_TYPE);
            map = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
            channelTypeThreadLocal.set(map);
        }
        return map;
    }
}
