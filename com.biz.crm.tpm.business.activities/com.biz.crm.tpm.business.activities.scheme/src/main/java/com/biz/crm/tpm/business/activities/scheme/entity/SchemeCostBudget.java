package com.biz.crm.tpm.business.activities.scheme.entity;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.TenantEntity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * 实体：方案预算成本;
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "SchemeCostBudget",description = "方案预算成本")
@TableName("tpm_scheme_cost_budget")
@Getter
@Setter
@Entity(name = "tpm_scheme_cost_budget")
@org.hibernate.annotations.Table(appliesTo = "tpm_scheme_cost_budget", comment = "方案预算成本")
public class SchemeCostBudget  extends TenantEntity{

  /** 方案编号 */
  @ApiModelProperty(name = "方案编号",notes = "")
  @Column(name = "scheme_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '方案编号 '")
  private String schemeCode;

  /** 费用预算编号 */
  @ApiModelProperty(name = "费用预算编号",notes = "费用预算编号")
  @Column(name = "cost_budget_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '费用预算编号 '")
  private String costBudgetCode;
}