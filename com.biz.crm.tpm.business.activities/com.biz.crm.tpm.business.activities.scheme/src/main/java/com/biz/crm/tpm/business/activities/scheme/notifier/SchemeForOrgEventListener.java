package com.biz.crm.tpm.business.activities.scheme.notifier;

import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventBatchDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventDto;
import com.biz.crm.mdm.business.org.sdk.event.OrgEventListener;
import com.biz.crm.tpm.business.activities.scheme.service.SchemeRangeVoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述：</br>方案监听MDM中组织变化引起的组织数据失效的问题
 *
 * <AUTHOR>
 * @date 2022/6/24
 */
@Component
public class SchemeForOrgEventListener implements OrgEventListener {

  @Autowired
  private SchemeRangeVoService schemeRangeVoService;

  @Override
  public void onDelete(List<String> orgCodes) {

  }

  @Override
  public void onDeleteBatch(OrgEventBatchDto orgEventBatchDto) {
    if (orgEventBatchDto == null) {
      return;
    }
    List<OrgEventDto> orgEventDtos = orgEventBatchDto.getOrgEventDtoList();
    if (CollectionUtils.isEmpty(orgEventDtos)) {
      return;
    }
    Set<String> orgCodes = orgEventDtos.stream().map(OrgEventDto::getOrgCode).collect(Collectors.toSet());
    this.schemeRangeVoService.deleteByRangeCodes(orgCodes);
  }

  @Override
  public void onEnableBatch(OrgEventBatchDto orgEventBatchDto) {
    if (orgEventBatchDto == null) {
      return;
    }
    List<OrgEventDto> orgEventDtos = orgEventBatchDto.getOrgEventDtoList();
    if (CollectionUtils.isEmpty(orgEventDtos)) {
      return;
    }
    Set<String> orgCodes = orgEventDtos.stream().map(OrgEventDto::getOrgCode).collect(Collectors.toSet());
    this.schemeRangeVoService.updateEnableStatus(orgCodes, EnableStatusEnum.ENABLE.getCode());
  }

  @Override
  public void onDisableBatch(OrgEventBatchDto orgEventBatchDto) {
    if (orgEventBatchDto == null) {
      return;
    }
    List<OrgEventDto> orgEventDtos = orgEventBatchDto.getOrgEventDtoList();
    if (CollectionUtils.isEmpty(orgEventDtos)) {
      return;
    }
    Set<String> orgCodes = orgEventDtos.stream().map(OrgEventDto::getOrgCode).collect(Collectors.toSet());
    this.schemeRangeVoService.updateEnableStatus(orgCodes, EnableStatusEnum.DISABLE.getCode());
  }

  @Override
  public void onUpdate(OrgEventDto orgEventDto) {

  }
}
