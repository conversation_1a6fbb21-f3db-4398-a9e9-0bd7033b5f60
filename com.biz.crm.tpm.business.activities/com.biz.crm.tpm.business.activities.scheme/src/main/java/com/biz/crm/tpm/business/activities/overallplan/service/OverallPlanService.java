package com.biz.crm.tpm.business.activities.overallplan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheService;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlan;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanBudget;
import com.biz.crm.tpm.business.activities.overallplan.vo.*;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;

import java.util.List;
import java.util.Map;

public interface OverallPlanService extends BusinessPageCacheService<OverallPlanCaseVo, OverallPlanCaseVo> {

    /**
     * 分页查询
     *
     * @param pageable
     * @param vo
     * @return
     */
    Page<OverallPlanVo> findList(Pageable pageable, OverallPlanVo vo);

    /**
     * 查询详情
     *
     * @param id
     * @param schemeType
     * @return
     */
    OverallPlanVo findById(String id, String schemeType);

    /**
     * 创建统筹方案
     *
     * @param vo
     */
    String createOverallPlan(OverallPlanVo vo,Boolean flag);

    /**
     * 创建统筹方案保存-事务剥离
     *
     * @param vo
     * @param plan
     */
    void createOverallPlanWithTransactional(OverallPlanVo vo, OverallPlan plan);

    /**
     * 修改统筹方案
     *
     * @param vo
     */
    String updateOverallPlan(OverallPlanVo vo,Boolean flag);

    /**
     * 创建并提交
     *
     * @param vo
     */
    void submitCreate(OverallPlanVo vo);

    /**
     * 修改并提交
     *
     * @param vo
     */
    void submitUpdate(OverallPlanVo vo);

    /**
     * 提交审批
     *
     * @param id
     * @param schemeType
     */
    void submitById(String id, String schemeType);

    /**
     * 手动审批
     *
     * @param id
     */
    void manualApproval(String id, String schemeType);

    /**
     * 审批回调
     *
     * @param vo
     */
    void callback(OverallPlanVo vo);

    /**
     * 删除
     *
     * @param idList
     */
    String deleteByIdList(List<String> idList);

    /**
     * 查询总部方案明细编码(可以承接的)
     *
     * @param vo
     * @return
     */
    Page<OverallPlanCaseVo> findHeadOverallPlanCaseList(Pageable pageable, HeadOverallPlanCaseVo vo);

    Page<OverallPlanCaseVo> findHeadOverallPlanCaseToMarketing(@PageableDefault(50)Pageable pageable, HeadOverallPlanCaseVo vo);

    /**
     * 通过方案明细编码查询方案明细
     *
     * @param schemeDetailCodes
     * @return
     */
    List<OverallPlanCaseVo> findOverallPlanCaseListBySchemeDetailCodes(List<String> schemeDetailCodes);

    /**
     * 通过关联统筹方案明细编码查询明细
     *
     * @param releaseDetailCode
     * @return
     */
    List<OverallPlanCaseVo> findOverallPlanCaseListByReleaseDetailCode(List<String> releaseDetailCode);

    /**
     * 查询关联的总部方案明细
     *
     * @param schemeDetailCodes
     * @return
     */
    List<BearOverallPlanCaseVo> findReleaseHeadCaseList(List<String> schemeDetailCodes);

    /**
     * 查询关联的总部方案明细数据 (营销规划方案使用)
     *
     * @param schemeDetailCodes
     * @return
     */
    List<BearOverallPlanCaseVo> findReleaseCaseByMarketingPlan(List<String> schemeDetailCodes);

    /**
     * 统计总额
     *
     * @param cacheKey
     * @return
     */
    Map<String, Object> getTotalAmount(String cacheKey);

    /**
     * 查询总部指引汇总-总览
     *
     * @param pageable
     * @param vo
     * @return
     */
    Page<HeadOverallSummaryReportVo> findSummaryList(Pageable pageable, HeadOverallSummaryReportVo vo);

    /**
     * OA撤回
     *
     * @param code
     * @return
     */
    void recover(String code, String remark);

    /**
     * 验证是否做了大区指引案
     */
    void checkRegionOverall(String years);

    /**
     * 查询组织下面的所有大区承接方案
     *
     * @param years
     * @param orgCode
     * @return
     */
    List<String> findRegionOverall(String years, String orgCode);

    List<String> findAllUndertakeOverall(String years, String orgCode);

    List<OverallPlanBudget> updateOverallBudget(String cacheKey);

    Map<String,Object> checkHeadBudget(String cacheKey);
}
