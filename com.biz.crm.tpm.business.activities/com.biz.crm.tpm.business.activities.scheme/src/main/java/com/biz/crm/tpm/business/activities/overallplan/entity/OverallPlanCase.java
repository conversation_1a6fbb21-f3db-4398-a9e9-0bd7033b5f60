package com.biz.crm.tpm.business.activities.overallplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanScopeTypeEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_overall_plan_case")
@Table(
        name = "tpm_overall_plan_case",
        indexes = {
                @Index(name = "tpm_overall_plan_case_index0", columnList = "scheme_code"),
                @Index(name = "tpm_overall_plan_case_index1", columnList = "scheme_detail_code", unique = true)
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_overall_plan_case", comment = "统筹方案明细")
@ApiModel(value = "OverallPlanCase", description = "统筹方案明细")
public class OverallPlanCase extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("关联总部编码")
    @Column(name = "head_scheme_code", columnDefinition = "varchar(32) comment '关联总部编码'")
    private String headSchemeCode;

    @ApiModelProperty("关联总部名称")
    @Column(name = "head_scheme_name",columnDefinition = "varchar(32) comment '关联总部名称'")
    private String headSchemeName;

    @ApiModelProperty("关联总部明细编码")
    @Column(name = "head_scheme_detail_code", columnDefinition = "varchar(32) comment '关联总部明细编码'")
    private String headSchemeDetailCode;

    @ApiModelProperty("二级费用大类编码")
    @Column(name = "second_cost_category", columnDefinition = "varchar(32) comment '二级费用大类编码'")
    private String secondCostCategory;

    @ApiModelProperty("二级费用大类名称")
    @Column(name = "second_cost_category_name", columnDefinition = "varchar(64) comment '二级费用大类名称'")
    private String secondCostCategoryName;

    @ApiModelProperty("预算科目编码")
    @Column(name = "budget_subject_code", columnDefinition = "varchar(32) comment '预算科目编码'")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    @Column(name = "budget_subject_name", columnDefinition = "varchar(64) comment '预算科目名称'")
    private String budgetSubjectName;

    @ApiModelProperty("活动大类编码")
    @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    @Column(name = "detail_code", columnDefinition = "varchar(32) comment '活动细类编码'")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    @Column(name = "detail_name", columnDefinition = "varchar(64) comment '活动细类名称'")
    private String detailName;

    @ApiModelProperty("是否可以承接")
    @Column(name = "bear_flag", columnDefinition = "varchar(10) comment '是否可以承接'")
    private String bearFlag;

    @ApiModelProperty("开始时间")
    @Column(name = "start_date", columnDefinition = "varchar(20) comment '开始时间'")
    private String startDate;

    @ApiModelProperty("结束时间")
    @Column(name = "end_date", columnDefinition = "varchar(20) comment '结束时间'")
    private String endDate;

    @ApiModelProperty("归属年月")
    @Column(name = "years", columnDefinition = "varchar(10) comment '归属年月'")
    private String years;

    @ApiModelProperty("承担部门编码")
    @Column(name = "bear_department_code", columnDefinition = "varchar(32) comment '承担部门编码'")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    @Column(name = "bear_department_name", columnDefinition = "varchar(64) comment '承担部门名称'")
    private String bearDepartmentName;

    @ApiModelProperty("承接类型")
    @Column(name = "bear_type", columnDefinition = "varchar(10) comment '承接类型'")
    private String bearType;

    @ApiModelProperty("成本中心编码")
    @Column(name = "cost_center_code", columnDefinition = "varchar(32) comment '成本中心编码'")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @Column(name = "cost_center_name", columnDefinition = "varchar(64) comment '成本中心名称'")
    private String costCenterName;

    @ApiModelProperty("合作类型")
    @Column(name = "cooperate_type_str", columnDefinition = "varchar(200) comment '合作类型'")
    private String cooperateTypeStr;

    @ApiModelProperty("渠道类型")
    @Column(name = "channel_type_str", columnDefinition = "varchar(200) comment '渠道类型'")
    private String channelTypeStr;

    @ApiModelProperty("客户标签")
    @Column(name = "customer_tag_str", columnDefinition = "VARCHAR(200) COMMENT '客户标签;数据字典[mdm_customer_tag]'")
    private String customerTagStr;

    @ApiModelProperty("终端标签")
    @Column(name = "terminal_tag_str", columnDefinition = "varchar(200) comment '终端标签'")
    private String terminalTagStr;

    @ApiModelProperty("预估费用")
    @Column(name = "apply_amount", columnDefinition = "decimal(18,4) comment '预估费用'")
    private BigDecimal applyAmount;

    @ApiModelProperty("预估销售额")
    @Column(name = "estimated_sales_volume", columnDefinition = "decimal(18,4) comment '预估销售额'")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("兑付条件及说明")
    @Column(name = "cash_condition", columnDefinition = "varchar(256) comment '兑付条件及说明'")
    private String cashCondition;

    @ApiModelProperty("错误描述")
    @Column(name = "err_msg", columnDefinition = "varchar(500) comment '错误描述'")
    private String errMsg;

    @ApiModelProperty("校验结果")
    @Column(name = "check_flag", columnDefinition = "tinyint comment '校验结果'")
    private Boolean checkFlag;

    @ApiModelProperty("排序")
    @Column(name = "sort", columnDefinition = "bigint(20) comment '排序'")
    private Long sort;

    @ApiModelProperty("销售部门列表")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanDepartment> bearDepartmentList;

    @ApiModelProperty("产品范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProduct> productAndItemList;

    @ApiModelProperty("客户、终端")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScope> scopeList;

    @ApiModelProperty("品项范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProduct> itemList;

    @ApiModelProperty("产品范围")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanProduct> productList;

    @ApiModelProperty("客户")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScope> customerList;

    @ApiModelProperty("终端")
    @Transient
    @TableField(exist = false)
    private List<OverallPlanScope> terminalList;

    @ApiModelProperty("方案名称")
    @Transient
    @TableField(exist = false)
    private String schemeName;


    public void setProductAndItemList(List<OverallPlanProduct> productAndItemList) {
        this.productAndItemList = productAndItemList;
        if (CollectionUtils.isEmpty(productAndItemList)) {
            return;
        }
        //设置商品
        List<OverallPlanProduct> products = productAndItemList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getType()) &&
                OverallPlanProductEnum.cal_product.getCode().equals(x.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(products)) {
            this.productList = products;
        }
        //设置品项
        List<OverallPlanProduct> itemList = productAndItemList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getType()) &&
                OverallPlanProductEnum.cal_item.getCode().equals(x.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemList)) {
            this.itemList = itemList;
        }
    }

    public List<OverallPlanProduct> getProductAndItemList() {
        List<OverallPlanProduct> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(this.productList)) {
            list.addAll(this.productList);
        }
        if (CollectionUtils.isNotEmpty(this.itemList)) {
            list.addAll(this.itemList);
        }
        this.productAndItemList = list;
        return list;
    }

    public void setScopeList(List<OverallPlanScope> scopeList) {
        this.scopeList = scopeList;
        if (CollectionUtils.isEmpty(scopeList)) {
            return;
        }
        List<OverallPlanScope> customerList = scopeList.stream().filter(x -> OverallPlanScopeTypeEnum.cus.getCode().equals(x.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(customerList)) {
            this.customerList = customerList;
        }
        List<OverallPlanScope> terminalList = this.scopeList.stream().filter(x -> OverallPlanScopeTypeEnum.terminal.getCode().equals(x.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(terminalList)) {
            this.terminalList = terminalList;
        }
    }

    public List<OverallPlanScope> getScopeList() {
        List<OverallPlanScope> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(this.customerList)) {
            list.addAll(this.customerList);
        }
        if (CollectionUtils.isNotEmpty(this.terminalList)) {
            list.addAll(this.terminalList);
        }
        this.scopeList = list;
        return list;
    }
}
