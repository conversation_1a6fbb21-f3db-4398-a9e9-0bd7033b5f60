package com.biz.crm.tpm.business.activities.scheme.service.internal;

import com.biz.crm.tpm.business.activities.scheme.repository.SchemeProductRepository;
import com.biz.crm.tpm.business.activities.scheme.entity.SchemeProduct;
import com.biz.crm.tpm.business.activities.scheme.service.SchemeProductVoService;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeProductVo;
import com.google.common.collect.Sets;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.LinkedHashSet;
import java.util.ArrayList;
import java.util.Set;

/**
 * 方案产品;(tpm_scheme_product)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Service("schemeProductVoService")
public class SchemeProductVoServiceImpl implements SchemeProductVoService {
  @Autowired
  private SchemeProductRepository schemeProductRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public Set<SchemeProductVo> findBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return null;
    }
    List<SchemeProduct> schemeProducts = this.schemeProductRepository.findBySchemeCode(schemeCode);
    if (CollectionUtils.isEmpty(schemeProducts)) {
      return null;
    }
    Collection<SchemeProductVo> schemeProductVos = this.nebulaToolkitService.copyCollectionByWhiteList(schemeProducts, SchemeProduct.class, SchemeProductVo.class, LinkedHashSet.class, ArrayList.class);
    return Sets.newLinkedHashSet(schemeProductVos);
  }

  /**
   * 新增数据
   *
   * @param schemeProductVo 实体对象
   * @return 新增结果
   */
  @Transactional
  @Override
  public SchemeProductVo create(SchemeProductVo schemeProductVo) {
    this.createValidate(schemeProductVo);
    SchemeProduct schemeProduct = this.nebulaToolkitService.copyObjectByWhiteList(schemeProductVo, SchemeProduct.class, LinkedHashSet.class, ArrayList.class);
    schemeProduct.setTenantCode(TenantUtils.getTenantCode());
    this.schemeProductRepository.saveOrUpdate(schemeProduct);

    return schemeProductVo;
  }

  @Override
  @Transactional
  public void deleteBySchemeCode(String schemeCode) {
    Validate.notBlank(schemeCode, "删除方案商品数据，方案编码不能为空！");
    this.schemeProductRepository.deleteBySchemeCode(schemeCode);
  }

  /**
   * 创建验证
   *
   * @param schemeProductVo
   */
  private void createValidate(SchemeProductVo schemeProductVo) {
    Validate.notNull(schemeProductVo, "新增时，对象信息不能为空！");
    schemeProductVo.setId(null);
    // 验证重复操作
    Validate.notBlank(schemeProductVo.getProductCode(), "新增数据时，商品编号不能为空！");
    Validate.notBlank(schemeProductVo.getProductName(), "新增数据时，商品名称不能为空！");
    Validate.notBlank(schemeProductVo.getSchemeCode(), "新增数据时，方案编号不能为空！");
  }
}