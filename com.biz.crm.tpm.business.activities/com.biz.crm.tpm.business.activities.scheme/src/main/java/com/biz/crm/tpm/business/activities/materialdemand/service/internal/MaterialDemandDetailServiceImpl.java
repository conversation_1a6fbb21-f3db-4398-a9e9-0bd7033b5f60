package com.biz.crm.tpm.business.activities.materialdemand.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanProductRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemand;
import com.biz.crm.tpm.business.activities.materialdemand.entity.MaterialDemandDetail;
import com.biz.crm.tpm.business.activities.materialdemand.repository.MaterialDemandDetailRepository;
import com.biz.crm.tpm.business.activities.materialdemand.repository.MaterialDemandRepository;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandDetailService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandQueryVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandReportVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 17:36
 */
@Service
@Transactional
@Slf4j
public class MaterialDemandDetailServiceImpl implements MaterialDemandDetailService {

    @Resource
    private MaterialDemandDetailRepository repository;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private MaterialDemandRepository materialDemandRepository;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private MaterialVoService materialVoService;

    @Autowired
    private MarketingPlanProductRepository marketingPlanProductRepository;

    @Autowired
    private MarketingPlanCaseService marketingPlanCaseService;

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private PositionVoService positionVoService;

    /**
     * 查询列表
     *
     * @param codes
     * @return
     */
    @Override
    public List<MaterialDemandDetailVo> findListByCodes(List<String> codes) {
        List<MaterialDemandDetail> demandDetails = repository.findListByCodes(codes);
        if (CollectionUtils.isEmpty(demandDetails)) {
            return Lists.newArrayList();
        }
        return (List<MaterialDemandDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(demandDetails, MaterialDemandDetail.class, MaterialDemandDetailVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 保存明细
     *
     * @param demandDetailVos
     */
    @Override
    public void saveBatchList(List<MaterialDemandDetailVo> demandDetailVos, String demandCode) {
        repository.deleteByCodes(Lists.newArrayList(demandCode));
        if (CollectionUtils.isNotEmpty(demandDetailVos)) {
            List<MaterialDemandDetail> details = (List<MaterialDemandDetail>) nebulaToolkitService.copyCollectionByWhiteList(demandDetailVos,
                    MaterialDemandDetailVo.class, MaterialDemandDetail.class, HashSet.class, ArrayList.class);
            details.forEach(x -> {
                x.setId(null);
                x.setDemandCode(demandCode);
            });
            repository.saveBatch(details);
        }
    }

    @Override
    public void deleteByDemandCodeList(List<String> demandCodeList) {
        repository.deleteByCodes(demandCodeList);
    }

    /**
     * 查询明细列表
     *
     * @param id
     * @return
     */
    @Override
    public List<MaterialDemandDetailVo> findListById(String id) {
        MaterialDemand demand = materialDemandRepository.queryByIdOrCode(id, null);
        if (ObjectUtils.isEmpty(demand)) {
            return Lists.newArrayList();
        }
        String demandCode = demand.getDemandCode();
        List<MaterialDemandDetail> demandDetails = repository.findListByCodes(Lists.newArrayList(demandCode));
        if (CollectionUtils.isEmpty(demandDetails)) {
            return Lists.newArrayList();
        }
        return (List<MaterialDemandDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(demandDetails, MaterialDemandDetail.class, MaterialDemandDetailVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 数据校验
     *
     * @param voList
     * @return
     */
    @Override
    public List<MaterialDemandDetailVo> checkDetails(List<MaterialDemandDetailVo> voList) {
        AbstractCrmUserIdentity userIdentity = loginUserService.getAbstractLoginUser();
        String positionCode = userIdentity.getPostCode();
        PositionVo positionVo = positionVoService.findByPositionCode(positionCode);
        List<String> orgCodes = voList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode()))
                .map(MaterialDemandDetailVo::getOrgCode).distinct().collect(Collectors.toList());
        Set<String> materialCodes = voList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                .map(MaterialDemandDetailVo::getMaterialCode).collect(Collectors.toSet());
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
        String companyCode = positionVo.getCompanyCode();
        MaterialSearchDto searchDto = new MaterialSearchDto();
        searchDto.setCompanyCodeSet(Sets.newHashSet(companyCode));
        searchDto.setMaterialCodeSet(materialCodes);
        List<MaterialVo> materialVos = materialVoService.findBySearchDto(searchDto);
        Map<String, String> orgMap = Maps.newHashMap();
        Map<String, MaterialVo> materialMap = Maps.newHashMap();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orgVoList)) {
            orgMap = orgVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode())).collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(materialVos)) {
            materialMap = materialVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                    .collect(Collectors.toMap(MaterialVo::getMaterialCode, Function.identity()));
        }
        for (MaterialDemandDetailVo vo : voList) {
            StringJoiner errMsg = new StringJoiner(";");
            if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getOrgCode(), "组织编码", errMsg)) {
                if (orgMap.containsKey(vo.getOrgCode())) {
                    vo.setOrgName(orgMap.get(vo.getOrgCode()));
                }
            }
            if (MarketingPlanCaseCheckHelper.paramNotNull(vo.getMaterialCode(), "物料编码", errMsg)) {
                if (materialMap.containsKey(vo.getMaterialCode())) {
                    MaterialVo materialVo = materialMap.get(vo.getMaterialCode());
                    vo.setMaterialName(materialVo.getMaterialName());
                    vo.setMaterialPrice(ObjectUtils.defaultIfNull(materialVo.getCostPrice(), BigDecimal.ZERO));
                }
            }
            MarketingPlanCaseCheckHelper.paramNotNull(vo.getMaterialNum(), "物料数量", errMsg);
            if (ObjectUtils.isNotEmpty(vo.getMaterialPrice()) && ObjectUtils.isNotEmpty(vo.getMaterialNum())) {
                vo.setMaterialAmount(vo.getMaterialNum().multiply(vo.getMaterialPrice()));
            }
            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                vo.setErrMsg(errMsg.toString());
                vo.setCheckFlag(Boolean.FALSE);
            } else {
                vo.setCheckFlag(Boolean.TRUE);
            }
        }
        voList = voList.stream().sorted(Comparator.comparing(MaterialDemandDetailVo::getCheckFlag)).collect(Collectors.toList());
        return voList;
    }


    /**
     * 通过明细编码查询
     *
     * @param demandDetailCodes
     * @return
     */
    @Override
    public List<MaterialDemandDetailVo> findListByDemandDetailCodes(List<String> demandDetailCodes) {
        List<MaterialDemandDetail> demandDetails = repository.findListByDemandDetailCodes(demandDetailCodes);
        return (List<MaterialDemandDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(demandDetails, MaterialDemandDetail.class,
                MaterialDemandDetailVo.class, HashSet.class, ArrayList.class);
    }


    /**
     * 物料汇总追踪
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MaterialDemandReportVo> findMaterialDemandReportList(Pageable pageable, MaterialDemandReportVo vo) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MaterialDemandReportVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<MaterialDemandReportVo> data = repository.findMaterialDemandReportList(page, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> materialCodes = data.getRecords().stream().map(MaterialDemandReportVo::getMaterialCode).collect(Collectors.toList());
            List<String> orgCodes = data.getRecords().stream().map(MaterialDemandReportVo::getOrgCode).collect(Collectors.toList());
            List<MarketingPlanCaseVo> planCaseVoList = marketingPlanCaseService.findMaterialListByMaterialCodesAndOrgCodes(materialCodes, orgCodes);
            if (CollectionUtils.isNotEmpty(planCaseVoList)) {
                Map<String, MarketingPlanCaseVo> caseMap = planCaseVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + x.getBelongDepartmentCode(), v -> v));
                for (MaterialDemandReportVo record : data.getRecords()) {
                    String key = record.getMaterialCode() + record.getOrgCode();
                    if (caseMap.containsKey(key)) {
                        MarketingPlanCaseVo caseVo = caseMap.get(key);
                        record.setUsedMaterialAmount(caseVo.getApplyAmount());
                        record.setUsedMaterialNum(caseVo.getMaterialNum());
                        record.setAvailMaterialAmount(record.getMaterialAmount().subtract(caseVo.getApplyAmount()));
                        record.setAvailMaterialNum(record.getMaterialNum().subtract(caseVo.getMaterialNum()));
                    }
                }
            }
        }
        return data;
    }

    /**
     * 查询分页列表
     *
     * @param pageable
     * @param vo
     * @return
     */
    @Override
    public Page<MarketingPlanCaseVo> queryDetailList(Pageable pageable, MaterialDemandQueryVo vo) {
        Validate.notNull(vo.getId(), "主键DI不能为空");
        String[] ids = vo.getId().split(":");
        vo.setMaterialCode(ids[1]);
        vo.setOrgCode(ids[0]);
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        Page<MarketingPlanCaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Page<MarketingPlanCaseVo> data = repository.queryDetailList(page, vo);
        if (CollectionUtils.isNotEmpty(data.getRecords())) {
            List<String> schemeDetailCodes = data.getRecords().stream().map(MarketingPlanCaseVo::getSchemeDetailCode).collect(Collectors.toList());
            Map<String, List<MarketingPlanProductVo>> productAndItemMap = marketingPlanProductRepository.findProductListBySchemeDetailCodes(schemeDetailCodes);
            for (MarketingPlanCaseVo record : data.getRecords()) {
                if (productAndItemMap.containsKey(record.getSchemeDetailCode())) {
                    record.setProductAndItemList(productAndItemMap.get(record.getSchemeDetailCode()));
                }
            }
        }
        return data;
    }


    /**
     * 校验物料提报-营销方案使用是否足够的问题
     *
     * @param detailVos
     */
    @Override
    public void checkMaterialDemandNum(List<MaterialDemandDetailVo> detailVos, String schemeCode) {
        List<String> materialCodes = detailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()))
                .map(MaterialDemandDetailVo::getMaterialCode).distinct().collect(Collectors.toList());
        List<String> orgCodes = detailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode()))
                .map(MaterialDemandDetailVo::getOrgCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialCodes) || CollectionUtils.isEmpty(orgCodes)) {
            return;
        }
        //初始数据
        List<MaterialDemandDetail> demandDetails = repository.findListByMaterialCodesAndOrgCodes(materialCodes, orgCodes);
        Validate.isTrue(CollectionUtils.isNotEmpty(demandDetails), "物料提报数据不存在,不允许创建物料营销方案");
        //查询使用量
        List<MarketingPlanCaseVo> caseVoList = marketingPlanCaseService.findMaterialListByMaterialCodesAndOrgCodesAndExclusiveSchemeCode(materialCodes, orgCodes, schemeCode);
        if (CollectionUtils.isNotEmpty(caseVoList)) {
            Map<String, MarketingPlanCaseVo> caseMap = caseVoList.stream().collect(Collectors.toMap(x -> x.getMaterialCode() + x.getBelongDepartmentCode(), v -> v));
            for (MaterialDemandDetail detail : demandDetails) {
                String key = detail.getMaterialCode() + detail.getOrgCode();
                if (caseMap.containsKey(key)) {
                    BigDecimal usedMaterialNum = caseMap.get(key).getMaterialNum();
                    detail.setMaterialNum(detail.getMaterialNum().subtract(usedMaterialNum));
                }
            }
        }
        //分组数据
        Map<String, List<MaterialDemandDetailVo>> materialMap = detailVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMaterialCode()) && ObjectUtils.isNotEmpty(x.getOrgCode()))
                .collect(Collectors.groupingBy(x -> x.getMaterialCode() + x.getOrgCode()));
        for (MaterialDemandDetail detail : demandDetails) {
            String key = detail.getMaterialCode() + detail.getOrgCode();
            if (materialMap.containsKey(key)) {
                List<MaterialDemandDetailVo> list = materialMap.get(key);
                String orgName = list.get(0).getOrgName();
                String materialName = list.get(0).getMaterialName();
                BigDecimal materialNum = list.stream().map(MaterialDemandDetailVo::getMaterialNum)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Validate.isTrue(materialNum.compareTo(detail.getMaterialNum()) < 1,
                        String.format("部门%s下的物料%s数超过物料提报数量,提报数量%s", orgName, materialName, detail.getMaterialNum()));
            }
        }
    }
}
