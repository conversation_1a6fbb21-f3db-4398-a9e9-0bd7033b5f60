package com.biz.crm.tpm.business.activities.marketingplan.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.entity.ActExecutionCollectLog;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.ActExecutionCollectLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @className: com.biz.crm.tpm.business.activities.marketingplan.repository.ActExecutionCollectLogRepository
 * @description: 活动执行采集记录
 * @author: xiaopeng.zhang
 * @create: 2024-08-01 10:43
 */
@Component
@Slf4j
public class ActExecutionCollectLogRepository extends ServiceImpl<ActExecutionCollectLogMapper, ActExecutionCollectLog> {
}
