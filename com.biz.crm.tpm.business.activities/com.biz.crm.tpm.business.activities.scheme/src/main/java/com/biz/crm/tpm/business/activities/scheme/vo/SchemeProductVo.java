package com.biz.crm.tpm.business.activities.scheme.vo;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Vo：方案产品;
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@ApiModel(value = "SchemeProduct",description = "方案产品")
@Getter
@Setter
public class SchemeProductVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
  private String tenantCode;
  /** 商品编号 */
  @ApiModelProperty(name = "productCode",notes = "商品编号", value= "商品编号")
  private String productCode;
  /** 商品名称 */
  @ApiModelProperty(name = "productName",notes = "商品名称", value= "商品名称")
  private String productName;
  /** 方案编号 */
  @ApiModelProperty(name = "schemeCode",notes = "方案编号", value= "方案编号")
  private String schemeCode;

}