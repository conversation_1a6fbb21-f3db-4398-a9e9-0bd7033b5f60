package com.biz.crm.tpm.business.activities.marketingplan.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanGainsAndLosses;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanGainsAndLossesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/16 14:08
 */
@RestController
@RequestMapping("/v1/marketingPlanGainsAndLossesController")
@Api(value = "客户损益预测")
public class MarketingPlanGainsAndLossesController {


    @Autowired
    private MarketingPlanGainsAndLossesService marketingPlanGainsAndLossesService;

    @ApiOperation(value = "客户损益预测")
    @GetMapping("findList")
    public Result<Page<MarketingPlanGainsAndLosses>> findList(@PageableDefault(50) Pageable pageable, MarketingPlanGainsAndLosses vo) {
        return Result.ok(marketingPlanGainsAndLossesService.findList(pageable, vo));
    }
}
