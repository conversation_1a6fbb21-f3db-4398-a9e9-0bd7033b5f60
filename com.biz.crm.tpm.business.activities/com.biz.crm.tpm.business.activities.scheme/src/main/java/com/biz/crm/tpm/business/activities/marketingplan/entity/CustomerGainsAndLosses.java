package com.biz.crm.tpm.business.activities.marketingplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/25 14:56
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@MappedSuperclass
public class CustomerGainsAndLosses extends UuidFlagOpEntity {

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(32) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(128) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("预算收入")
    @Column(name = "budget_income", columnDefinition = "decimal(18,4) comment '预算收入'")
    private BigDecimal budgetIncome;

    @ApiModelProperty("规划收入")
    @Column(name = "plan_income", columnDefinition = "decimal(18,4) comment '规划收入'")
    private BigDecimal planIncome;

    @ApiModelProperty("收入达成率")
    @Column(name = "income_achieve_ratio", columnDefinition = "decimal(18,4) comment '收入达成率'")
    private BigDecimal incomeAchieveRatio;

    @ApiModelProperty("收入达成率")
    @Column(name = "income_achieve_ratio_str", columnDefinition = "varchar(20) comment '收入达成率'")
    private String incomeAchieveRatioStr;

    @ApiModelProperty("预算费率")
    @Column(name = "budget_ratio", columnDefinition = "decimal(18,4) comment '预算费率'")
    private BigDecimal budgetRatio;

    @ApiModelProperty("预算费率")
    @Column(name = "budget_ratio_str", columnDefinition = "varchar(20) comment '预算费率'")
    private String budgetRatioStr;

    @ApiModelProperty("规划费率")
    @Column(name = "plan_ratio", columnDefinition = "decimal(18,4) comment '规划费率'")
    private BigDecimal planRatio;

    @ApiModelProperty("规划费率")
    @Column(name = "plan_ratio_str", columnDefinition = "varchar(20) comment '规划费率'")
    private String planRatioStr;

    @ApiModelProperty("费率偏差")
    @Column(name = "ratio_deviation", columnDefinition = "decimal(18,4) comment '费率偏差'")
    private BigDecimal ratioDeviation;

    @ApiModelProperty("费率偏差")
    @Column(name = "ratio_deviation_str", columnDefinition = "varchar(20) comment '费率偏差'")
    private String ratioDeviationStr;

    @ApiModelProperty("利润率")
    @Column(name = "profit_ratio", columnDefinition = "decimal(18,4) comment '利润率'")
    private BigDecimal profitRatio;

    @ApiModelProperty("利润率")
    @Column(name = "profit_ratio_str", columnDefinition = "varchar(20) comment '利润率'")
    private String profitRatioStr;

    @ApiModelProperty("毛利率")
    @Column(name = "gross_profit_ratio", columnDefinition = "decimal(18,4) comment '毛利率'")
    private BigDecimal grossProfitRatio;

    @ApiModelProperty("毛利率")
    @Column(name = "gross_profit_ratio_str", columnDefinition = "varchar(20) comment '毛利率'")
    private String grossProfitRatioStr;

    @ApiModelProperty("物流费率")
    @Column(name = "logistics_ratio", columnDefinition = "decimal(18,4) comment '物流费率'")
    private BigDecimal logisticsRatio;

    @ApiModelProperty("物流费率")
    @Column(name = "logistics_ratio_str", columnDefinition = "varchar(20) comment '物流费率'")
    private String logisticsRatioStr;

    @ApiModelProperty("营销费率")
    @Column(name = "marketing_ratio", columnDefinition = "decimal(18,4) comment '营销费率'")
    private BigDecimal marketingRatio;

    @ApiModelProperty("营销费率")
    @Column(name = "marketing_ratio_str", columnDefinition = "varchar(20) comment '营销费率'")
    private String marketingRatioStr;

    @ApiModelProperty("公摊费率")
    @Column(name = "public_share_ratio", columnDefinition = "decimal(18,4) comment '公摊费率'")
    private BigDecimal publicShareRatio;

    @ApiModelProperty("公摊费用")
    @Column(name = "public_share", columnDefinition = "decimal(18,4) comment '公摊费用'")
    private BigDecimal publicShare;

    @ApiModelProperty("公摊费率")
    @Column(name = "public_share_ratio_str", columnDefinition = "varchar(20) comment '公摊费率'")
    private String publicShareRatioStr;

    @ApiModelProperty("二级费用大类")
    @Transient
    @TableField(exist = false)
    private Map<String, BigDecimal> secondCostCategoryMap;

    @ApiModelProperty("二级费用大类-json串")
    @Column(name = "second_cost_category_json_str", columnDefinition = "text comment '二级费用大类-json串'")
    private String secondCostCategoryJsonStr;

    @ApiModelProperty("促销")
    @Column(name = "promotion", columnDefinition = "decimal(18,4) comment '促销'")
    private BigDecimal promotion;

    @ApiModelProperty("促销")
    @Column(name = "promotion_str", columnDefinition = "varchar(20) comment '促销'")
    private String promotionStr;

    @ApiModelProperty("回调")
    @Column(name = "callback", columnDefinition = "decimal(18,4) comment '回调'")
    private BigDecimal callback;

    @ApiModelProperty("回调")
    @Column(name = "callback_str", columnDefinition = "varchar(20) comment '回调'")
    private String callbackStr;

    @ApiModelProperty("陈列")
    @Column(name = "display", columnDefinition = "decimal(18,4) comment '陈列'")
    private BigDecimal display;

    @ApiModelProperty("陈列")
    @Column(name = "display_str", columnDefinition = "varchar(20) comment '陈列'")
    private String displayStr;

    @ApiModelProperty("人员推广")
    @Column(name = "generalization", columnDefinition = "decimal(18,4) comment '人员推广'")
    private BigDecimal generalization;

    @ApiModelProperty("人员推广")
    @Column(name = "generalization_str", columnDefinition = "varchar(20) comment '人员推广'")
    private String generalizationStr;

    @ApiModelProperty("品宣")
    @Column(name = "disseminate", columnDefinition = "decimal(18,4) comment '品宣'")
    private BigDecimal disseminate;

    @ApiModelProperty("品宣")
    @Column(name = "disseminate_str", columnDefinition = "varchar(20) comment '品宣'")
    private String disseminateStr;

    @ApiModelProperty("销售奖励")
    @Column(name = "sales_reward", columnDefinition = "decimal(18,4) comment '销售奖励'")
    private BigDecimal salesReward;

    @ApiModelProperty("销售奖励")
    @Column(name = "sales_reward_str", columnDefinition = "varchar(20) comment '销售奖励'")
    private String salesRewardStr;

    @ApiModelProperty("合同")
    @Column(name = "contract", columnDefinition = "decimal(18,4) comment '合同'")
    private BigDecimal contract;

    @ApiModelProperty("合同")
    @Column(name = "contract_str", columnDefinition = "varchar(20) comment '合同'")
    private String contractStr;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(10) comment '年月'")
    private String years;

    @ApiModelProperty("预算总额")
    @Column(name = "budget_total_amount", columnDefinition = "decimal(18,4) comment '预算总额'")
    private BigDecimal budgetTotalAmount;

    @ApiModelProperty("规划总额")
    @Column(name = "plan_total_amount", columnDefinition = "decimal(18,4) comment '规划总额'")
    private BigDecimal planTotalAmount;

    @ApiModelProperty("生产成本")
    @Column(name = "production_costs", columnDefinition = "decimal(18,4) comment '生产成本'")
    private BigDecimal productionCosts;

    @ApiModelProperty("产品运输费用")
    @Column(name = "product_transport_cost", columnDefinition = "decimal(18,4) comment '产品运输费用'")
    private BigDecimal productTransportCost;

    @ApiModelProperty("周边运输费用")
    @Column(name = "periphery_transport_cost", columnDefinition = "decimal(18,4) comment '周边运输费用'")
    private BigDecimal peripheryTransportCost;

    @ApiModelProperty("营销费用")
    @Column(name = "marketing_cost", columnDefinition = "decimal(18,4) comment '营销费用'")
    private BigDecimal marketingCost;

    @ApiModelProperty("规划利润额")
    @Column(name = "plan_profit_margin", columnDefinition = "decimal(18,4) comment '规划利润额'")
    private BigDecimal planProfitMargin;

    @ApiModelProperty("规划毛利额")
    @Column(name = "plan_gross_profit_margin", columnDefinition = "decimal(18,4) comment '规划毛利额'")
    private BigDecimal planGrossProfitMargin;

    @ApiModelProperty("关键唯一字段")
    @Column(name = "index_key", columnDefinition = "varchar(128) comment '唯一关键字段'")
    private String indexKey;


    @ApiModelProperty("费用转移")
    @Column(name = "cost_shift",columnDefinition = "decimal(18,4) comment '费用转移'")
    private BigDecimal costShift;

    @ApiModelProperty("考核利润")
    @Column(name = "assess_profits",columnDefinition = "decimal(18,4) comment '考核利润'")
    private BigDecimal assessProfits;
}
