package com.biz.crm.tpm.business.activities.marketingplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@AllArgsConstructor
@Getter
public enum MarketingPlanSchemeStatusEnum {

    TO_BE_CONFIRMED("00", "待确认"),
    CONFIRMED_TO_COLLECT("10", "已确认待汇总"),
    COLLECTED_TO_PREPARE("1", "已汇总待提交"),
    COMMIT("2", "审批中"),
    PASS("3", "审批通过"),
    REJECT("4", "驳回"),
    RECOVER("5", "追回"),
    ;


    private String code;

    private String desc;
}
