package com.biz.crm.tpm.business.activities.marketingplan.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.marketingplan.entity.PlanExecutionSum;
import com.biz.crm.tpm.business.activities.marketingplan.mapper.PlanExecutionSumMapper;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanExecutionSumVo;
import com.biz.crm.tpm.business.activities.sdk.dto.ActivitiesDetailSerialDto;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: yangrui
 * @Date: 2024-12-31 20:19
 */
@Component
public class PlanExecutionSumRepository extends ServiceImpl<PlanExecutionSumMapper, PlanExecutionSum> {

    public PlanExecutionSum queryBySchemeDetailCode(String schemeDetailCode) {
        if (StrUtil.isBlank(schemeDetailCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(PlanExecutionSum::getSchemeDetailCode, schemeDetailCode)
                .eq(PlanExecutionSum::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .one();
    }

    public Page<PlanExecutionSum> queryPage(Pageable pageable, PlanExecutionSumVo param) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(param)) {
            param = new PlanExecutionSumVo();
        }
        // 构建查询条件
        LambdaQueryWrapper<PlanExecutionSum> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(StrUtil.isNotBlank(param.getSchemeCode()), PlanExecutionSum::getSchemeCode, param.getSchemeCode())
                .eq(StrUtil.isNotBlank(param.getSchemeDetailCode()), PlanExecutionSum::getSchemeDetailCode, param.getSchemeDetailCode())
                .eq(StrUtil.isNotBlank(param.getTerminalCode()), PlanExecutionSum::getTerminalCode, param.getTerminalCode())
                .like(StrUtil.isNotBlank(param.getTerminalName()), PlanExecutionSum::getTerminalName, param.getTerminalName())
                .eq(StrUtil.isNotBlank(param.getDivisionOrgCode()), PlanExecutionSum::getDivisionOrgCode, param.getDivisionOrgCode())
                .like(StrUtil.isNotBlank(param.getDivisionOrgName()), PlanExecutionSum::getDivisionOrgName, param.getDivisionOrgName())
                .eq(StrUtil.isNotBlank(param.getBelongDepartmentCode()), PlanExecutionSum::getBelongDepartmentCode, param.getBelongDepartmentCode())
                .like(StrUtil.isNotBlank(param.getBelongDepartmentName()), PlanExecutionSum::getBelongDepartmentName, param.getBelongDepartmentName())
                .eq(StrUtil.isNotBlank(param.getYears()), PlanExecutionSum::getYears, param.getYears())
                .eq(StrUtil.isNotBlank(param.getReleaseCode()), PlanExecutionSum::getReleaseCode, param.getReleaseCode())
                .like(StrUtil.isNotBlank(param.getReleaseName()), PlanExecutionSum::getReleaseName, param.getReleaseName())
                .eq(StrUtil.isNotBlank(param.getReleaseDetailCode()), PlanExecutionSum::getReleaseDetailCode, param.getReleaseDetailCode())
                .eq(param.getMillionTerminal() != null, PlanExecutionSum::getMillionTerminal, param.getMillionTerminal())
                .eq(StrUtil.isNotBlank(param.getTerminalType()), PlanExecutionSum::getTerminalType, param.getTerminalType())
                .eq(StrUtil.isNotBlank(param.getActExecuteCode()), PlanExecutionSum::getActExecuteCode, param.getActExecuteCode())
                .ge(StrUtil.isNotBlank(param.getStartDateBegin()), PlanExecutionSum::getStartDate, param.getStartDateBegin())
                .le(StrUtil.isNotBlank(param.getStartDateEnd()), PlanExecutionSum::getStartDate, param.getStartDateEnd())
                .ge(StrUtil.isNotBlank(param.getEndDateBegin()), PlanExecutionSum::getEndDate, param.getEndDateBegin())
                .le(StrUtil.isNotBlank(param.getEndDateEnd()), PlanExecutionSum::getEndDate, param.getEndDateEnd())
                .eq(StrUtil.isNotBlank(param.getCustomerCode()), PlanExecutionSum::getCustomerCode, param.getCustomerCode())
                .like(StrUtil.isNotBlank(param.getCustomerName()), PlanExecutionSum::getCustomerName, param.getCustomerName())
                .eq(param.getAuditResult() != null, PlanExecutionSum::getAuditResult, param.getAuditResult());

        Page<PlanExecutionSum> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.page(page, queryWrapper);
    }
}
