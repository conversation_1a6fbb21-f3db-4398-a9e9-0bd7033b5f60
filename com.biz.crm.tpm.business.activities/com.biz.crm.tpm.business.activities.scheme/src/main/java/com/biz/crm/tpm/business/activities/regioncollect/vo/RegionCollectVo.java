package com.biz.crm.tpm.business.activities.regioncollect.vo;

import com.biz.crm.business.common.sdk.vo.WorkflowFlagOpVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "RegionCollectVo", description = "大区汇总")
public class RegionCollectVo extends WorkflowFlagOpVo {

    @ApiModelProperty("汇总编码")
    private String collectCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("年月")
    private String years;

    @ApiModelProperty("预估销售额")
    private BigDecimal estimatedSalesVolume;

    @ApiModelProperty("预估总费用金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("费率")
    private BigDecimal ratio;

    @ApiModelProperty("费率")
    private String ratioStr;

    @ApiModelProperty("汇总状态")
    private String collectStatus;

    @ApiModelProperty("汇总结果")
    private String collectResult;

    @ApiModelProperty("租户编码")
    private String tenantCode;

    @ApiModelProperty("推送日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    private String oaUserName;

    @ApiModelProperty("回传TPM接口唯一标识（接口用）")
    private String businessCode;

    @ApiModelProperty("url")
    private String tpmUrl;

    private String deptCode;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    private BigDecimal ratioBudget;
    private BigDecimal achieveRatio;
    private BigDecimal profitMargin;
    private BigDecimal profitMarginBudget;
    private BigDecimal estimateAmount;
    private BigDecimal budgetAmount;
    private BigDecimal marketingBudgetAmount;
    private String schemeDesc;
    private BigDecimal lrl;
    private BigDecimal yslrl;
    private BigDecimal cflghflysfl;
    private BigDecimal cklrelreyslre;

    @ApiModelProperty("人员提报-关联方案")
    private List<RegionCollectSchemeVo> schemeList;

    @ApiModelProperty("营销预测")
    private List<RegionCollectMarketingEstimationVo> marketingEstimationList;

    @ApiModelProperty("品项预测")
    private List<RegionCollectItemEstimationVo> itemEstimationList;

    @ApiModelProperty("客户损益预测")
    private List<RegionCollectGainsAndLossesVo> gainsAndLossesList;

    @ApiModelProperty("三级部门预测")
    private List<RegionCollectDepartmentEstimationVo> departmentEstimationList;

    @ApiModelProperty("预算列表")
    private List<RegionCollectBudgetVo> budgetList;

    @ApiModelProperty("方案明细")
    private Map<String, List<MarketingPlanCaseVo>> detailCaseMap;

    @ApiModelProperty("销售计划列表")
    private List<MarketingSalesPlanVo> salesPlanList;
}
