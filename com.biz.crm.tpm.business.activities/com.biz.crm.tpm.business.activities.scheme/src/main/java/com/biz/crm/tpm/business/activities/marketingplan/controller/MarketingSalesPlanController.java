package com.biz.crm.tpm.business.activities.marketingplan.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.constant.BusinessPageCacheConstant;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.sdk.dto.MarketingPlanCaseQueryDto;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/12 09:40
 */
@RestController
@RequestMapping("/v1/marketingSalesPlanController")
@Slf4j
public class MarketingSalesPlanController extends BusinessPageCacheController<MarketingSalesPlanVo, MarketingSalesPlanVo> {

    @Autowired
    private MarketingSalesPlanService marketingSalesPlanService;

    @Resource
    private RedisLockService redisLockService;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;

    @Autowired
    private LoginUserService loginUserService;
    @Autowired
    private UserVoService userVoService;

    @Autowired
    private OrgVoService orgVoService;


    @ApiOperation(value = "分页接口")
    @GetMapping("findCollectList")
    public Result<Page<MarketingSalesPlanVo>> findCollectList(@PageableDefault(50) Pageable pageable, MarketingSalesPlanVo vo) {
        return Result.ok(marketingSalesPlanService.findCollectList(pageable, vo));
    }

    @ApiOperation(value = "SFA移动访销费用投入情况")
    @GetMapping("findExpensesCountByConditions")
    public Result<Page<MarketingPlanCaseVo>> findExpensesCountByConditions(@PageableDefault(50) Pageable pageable, MarketingPlanCaseQueryDto vo) {
        return Result.ok(marketingPlanCaseService.findExpensesCountByConditions(pageable, vo));
    }

    @ApiOperation(value = "获取明细列表缓存分页接口")
    @GetMapping("findCachePageList")
    @Override
    public Result<Page<MarketingSalesPlanVo>> findCachePageList(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                @ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                                                @ApiParam(name = "dto", value = "查询实体") MarketingSalesPlanVo dto) {
        String pageCacheLockKey = helper.getRedisPageCacheLockKey(cacheKey);
        String[] cacheKeys = cacheKey.split(":");
        Validate.isTrue(cacheKeys.length == 3, "销售计划缓存key错误");
        try {
            Boolean lockFlag = redisLockService.tryLock(pageCacheLockKey, TimeUnit.MINUTES, BusinessPageCacheConstant.DEFAULT_PAGE_CACHE_EXPIRE_TIME);
            Validate.isTrue(lockFlag, "分页数据正在加载,请不要重复点击");
            Page<MarketingSalesPlanVo> page = this.marketingSalesPlanService.findCachePageList(pageable, dto, cacheKey);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        } finally {
            redisLockService.unlock(pageCacheLockKey);
        }
    }

    /**
     * 2、新增一行接口：保存当前页数据后，在缓存中行首插入一条数据并返回第一页数据
     *
     * @param pageable 分页参数
     * @param cacheKey 缓存key
     * @param itemList 要保存的当前页数据
     * @return
     */
    @Override
    @ApiOperation(value = "新增一行接口，保存当前页数据后，在缓存中行首插入一条数据并返回第一页数据")
    @PostMapping("addItemCache")
    public Result<Page<MarketingSalesPlanVo>> addItemCache(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                           @ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                                           @ApiParam(name = "dto", value = "查询实体") MarketingSalesPlanVo dto,
                                                           @ApiParam(value = "当前页数据") @RequestBody List<MarketingSalesPlanVo> itemList) {
        String[] cacheKeys = cacheKey.split(":");
        Validate.isTrue(cacheKeys.length == 3, "销售计划缓存key错误");
        try {
            this.marketingSalesPlanService.addItemCache(cacheKey, itemList);
            Page<MarketingSalesPlanVo> page = this.marketingSalesPlanService.findCachePageList(pageable, dto, cacheKey);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 3、复制行接口：保存当前页数据后，在缓存中行首复制选中数据并返回第一页数据
     *
     * @param pageable 分页参数
     * @param cacheKey 缓存key
     * @return
     */
    @Override
    @ApiOperation(value = "复制行接口，保存当前页数据后，在缓存中行首复制选中数据并返回第一页数据")
    @PostMapping("copyItemListCache")
    public Result<Page<MarketingSalesPlanVo>> copyItemListCache(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                @ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                                                @ApiParam(name = "dto", value = "查询实体") MarketingSalesPlanVo dto,
                                                                @ApiParam(value = "当前页数据，包含要复制的行勾选信息") @RequestBody List<MarketingSalesPlanVo> itemList) {
        String[] cacheKeys = cacheKey.split(":");
        Validate.isTrue(cacheKeys.length == 3, "销售计划缓存key错误");
        try {
            this.marketingSalesPlanService.copyItemListCache(cacheKey, itemList);
            Page<MarketingSalesPlanVo> page = this.marketingSalesPlanService.findCachePageList(pageable, dto, cacheKey);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 4、保存当前页数据到缓存并返回指定页数据接口
     *
     * @param pageable 分页参数
     * @param cacheKey 缓存key
     * @return
     */
    @Override
    @ApiOperation(value = "保存当前页数据到缓存并返回指定页数据接口")
    @PostMapping("saveCurrentPageCache")
    public Result<Page<MarketingSalesPlanVo>> saveCurrentPageCache(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                   @ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                                                   @ApiParam(name = "dto", value = "查询实体") MarketingSalesPlanVo dto,
                                                                   @ApiParam(value = "当前页数据") @RequestBody List<MarketingSalesPlanVo> saveList) {
        String[] cacheKeys = cacheKey.split(":");
        Validate.isTrue(cacheKeys.length == 3, "销售计划缓存key错误");
        String schemeCode = null;
        String originalSchemeCode = null;
        if (ObjectUtils.isNotEmpty(dto)) {
            schemeCode = ObjectUtils.defaultIfNull(dto.getSchemeCode(), null);
            originalSchemeCode = ObjectUtils.defaultIfNull(dto.getOriginalSchemeCode(), null);
        }
        // 获取当前登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        UserVo detail = userVoService.findDetailById(userVo.getId());
        List<String> userOrgCodes = Lists.newArrayList();
        detail.getPositionList().forEach(e -> {
            Set<String> orgCodes = e.getOrgCodes();
            if (!CollectionUtils.isEmpty(orgCodes)) {
                userOrgCodes.addAll(orgCodes);
            }
        });
        log.info("importMarketingPlanCase.userOrgCodes: {}", JSON.toJSONString(userOrgCodes));
        List<String> loginUserCodes = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(userOrgCodes)) {
            List<OrgVo> orgVos = orgVoService.findByOrgCodes(userOrgCodes);
            if (!CollectionUtils.isEmpty(orgVos)) {
                orgVos.forEach(e -> {
                    List<OrgVo> nextLevelOrgs = orgVoService.findByRuleCodeLike(e.getRuleCode());
                    if (!CollectionUtils.isEmpty(nextLevelOrgs)) {
                        loginUserCodes.addAll(nextLevelOrgs.stream().map(OrgVo::getOrgCode).collect(Collectors.toList()));
                    }
                });
            }
        }

        try {
            this.marketingSalesPlanService.salesPlanSaveCurrentCachePage(cacheKey, saveList, schemeCode, originalSchemeCode, loginUserCodes, userVo.getUserName());
            Page<MarketingSalesPlanVo> page = this.marketingSalesPlanService.findCachePageList(pageable, dto, cacheKey);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 7、删除行接口：删除有选中标记的行，更新未标记数据，并返回指定页数据接口
     *
     * @param pageable 分页参数
     * @param cacheKey 缓存key
     * @return
     */
    @Override
    @ApiOperation(value = "多行删除并返回指定页数据接口")
    @PostMapping("deleteCacheList")
    public Result<Page<MarketingSalesPlanVo>> deleteCacheList(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                              @ApiParam(name = "cacheKey", value = "缓存键") @RequestParam String cacheKey,
                                                              @ApiParam(name = "dto", value = "查询实体") MarketingSalesPlanVo dto,
                                                              @ApiParam(value = "当前页数据，包含要删除的行勾选信息") @RequestBody List<MarketingSalesPlanVo> itemList) {
        String[] cacheKeys = cacheKey.split(":");
        Validate.isTrue(cacheKeys.length == 3, "销售计划缓存key错误");
        try {
            this.marketingSalesPlanService.deleteCacheList(cacheKey, itemList);
            Page<MarketingSalesPlanVo> page = this.marketingSalesPlanService.findCachePageList(pageable, dto, cacheKey);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "通过年月+组织查询")
    @PostMapping("findListByOrgCodesAndYears")
    public Result<List<MarketingSalesPlanVo>> findListByOrgCodesAndYears(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(marketingSalesPlanService.findListByOrgCodesAndYears(dto));
    }

}
