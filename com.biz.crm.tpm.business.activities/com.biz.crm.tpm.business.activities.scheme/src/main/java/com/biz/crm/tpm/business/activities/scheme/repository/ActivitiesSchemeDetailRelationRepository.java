package com.biz.crm.tpm.business.activities.scheme.repository;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeDetailRelation;
import com.biz.crm.tpm.business.activities.scheme.mapper.ActivitiesSchemeDetailRelationMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Set;


/**
 * 方案活动关联信息(ActivitiesSchemeDetailRelation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-17 10:52:34
 */
@Component
public class ActivitiesSchemeDetailRelationRepository extends ServiceImpl<ActivitiesSchemeDetailRelationMapper, ActivitiesSchemeDetailRelation> {

  @Autowired
  private ActivitiesSchemeDetailRelationMapper activitiesSchemeDetailRelationMapper;

  /**
   * 分页查询数据
   * @param pageable 分页对象
   * @param activitiesSchemeDetailRelation 实体对象
   * @return
   */
  public Page<ActivitiesSchemeDetailRelation> findByConditions(Pageable pageable, ActivitiesSchemeDetailRelation activitiesSchemeDetailRelation) {
    Page<ActivitiesSchemeDetailRelation> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    activitiesSchemeDetailRelation.setTenantCode(TenantUtils.getTenantCode());
    Page<ActivitiesSchemeDetailRelation> pageList = this.activitiesSchemeDetailRelationMapper.findByConditions(page, activitiesSchemeDetailRelation);
    return pageList;
  }

  /**
   * 根据关联的活动编码，查询信息
   */
  public List<ActivitiesSchemeDetailRelation> findByActivityCodeAndTenantCode(String activityCode, String tenantCode) {
    return this.lambdaQuery().eq(ActivitiesSchemeDetailRelation::getActivityCode, activityCode).eq(ActivitiesSchemeDetailRelation::getTenantCode, tenantCode).list();
  }

  /**
   * 根据关联的活动编码，删除信息
   */
  public void deleteByActivityCodeAndTenantCode(String activityCode, String tenantCode){
    this.lambdaUpdate().eq(ActivitiesSchemeDetailRelation::getActivityCode,activityCode).eq(ActivitiesSchemeDetailRelation::getTenantCode,tenantCode).remove();
  }

  /**
   * 根据关联的活动编码集合，查询信息
   */
  public List<ActivitiesSchemeDetailRelation> findByActivityCodesAndTenantCode(Set<String> activityCodes, String tenantCode){
    return this.lambdaQuery().in(ActivitiesSchemeDetailRelation::getActivityCode,activityCodes).eq(ActivitiesSchemeDetailRelation::getTenantCode,tenantCode).list();
  }

  public ActivitiesSchemeDetailRelation findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ActivitiesSchemeDetailRelation::getTenantCode,tenantCode)
        .in(ActivitiesSchemeDetailRelation::getId,id)
        .one();
  }

  public void removeByIdsAndTenantCode(Collection<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ActivitiesSchemeDetailRelation::getTenantCode,tenantCode)
        .in(ActivitiesSchemeDetailRelation::getId,ids)
        .remove();
  }
}

