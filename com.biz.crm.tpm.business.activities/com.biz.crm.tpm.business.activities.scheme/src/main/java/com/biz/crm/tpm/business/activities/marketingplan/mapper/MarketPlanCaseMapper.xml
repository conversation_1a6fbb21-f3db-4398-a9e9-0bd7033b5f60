<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.activities.marketingplan.mapper.MarketingPlanCaseMapper">


    <select id="findCaseListAuditData" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
            b.scheme_detail_code,
            sum( b.audit_amount ) audit_amount
            FROM
            tpm_marketing_audit a
            LEFT JOIN tpm_marketing_audit_detail b ON a.audit_code = b.audit_code
            where a.status='3' and b.scheme_detail_code in <foreach collection="list" separator="," open="(" close=")" item="code">#{code} </foreach>
            GROUP BY
            b.scheme_detail_code
    </select>

    <select id="findCaseList" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select
        a.*,c.*,
        b.scheme_name,b.department_name,b.position_code,b.org_code,b.org_name from tpm_marketing_plan_case a
        join tpm_marketing_plan_case_extend c on a.scheme_detail_code = c.scheme_detail_code
        left join tpm_marketing_plan b on a.scheme_code = b.scheme_code
        <where>
            b.del_flag = '${@<EMAIL>()}'
            and a.del_flag = '${@<EMAIL>()}'
            <if test="vo.withholding != null and vo.withholding != ''">
                and a.scheme_detail_code not in
                (
                select
                IFNULL(t1.activities_detail_code,'')
                from tpm_with_holding t1
                where t1.del_flag = '${@<EMAIL>()}'
                and (t1.status in ('2','3') or t1.be_adjust='Y' or t1.confirm_status='confirmed')
                )
            </if>
            <if test="processStatus != null and processStatus != ''">
                and b.process_status = #{processStatus}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and a.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and b.scheme_name like #{schemeName}
            </if>
            <if test="vo.categoryName != null and vo.categoryName != ''">
                <bind name="categoryName" value="vo.categoryName + '%'"/>
                and a.category_name like #{categoryName}
            </if>
            <if test="vo.detailName != null and vo.detailName != ''">
                <bind name="detailName" value="'%' + vo.detailName + '%'"/>
                and a.detail_name like #{detailName}
            </if>
            <if test="vo.belongDepartmentName != null and vo.belongDepartmentName != ''">
                <bind name="belongDepartmentName" value="'%' + vo.belongDepartmentName + '%'"/>
                and a.belong_department_name like #{belongDepartmentName}
            </if>
            <if test="vo.costCenterName != null and vo.costCenterName != ''">
                <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
                and a.cost_center_name like #{costCenterName}
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                <bind name="customerName" value="'%' + vo.customerName + '%'"/>
                and a.customer_name like #{customerName}
            </if>
            <if test="vo.actDesc != null and vo.actDesc != ''">
                <bind name="actDesc" value="'%' + vo.actDesc + '%'"/>
                and c.act_desc like #{actDesc}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and a.years = #{vo.years}
            </if>
            <if test="vo.auditStatus != null and vo.auditStatus != ''">
                and a.audit_status = #{vo.auditStatus}
            </if>
            <if test="vo.yearsLess != null and vo.yearsLess != ''">
                and a.years &lt; #{vo.yearsLess}
            </if>
            <if test="vo.endDateLess != null and vo.endDateLess != ''">
                and a.end_date &lt; #{vo.endDateLess}
            </if>
            <if test="vo.caseType != null and vo.caseType != ''">
                and a.case_type = #{vo.caseType}
            </if>
            <if test="vo.cashType != null and vo.cashType != ''">
                and a.cash_type = #{vo.cashType}
            </if>
            <if test="vo.createName != null and vo.createName != ''">
                and a.create_name = #{vo.createName}
            </if>
            <if test="vo.actName != null and vo.actName != ''">
                <bind name="actName" value="'%' + vo.actName + '%'"/>
                and a.act_name like #{actName}
            </if>
            <if test="vo.excludeWholeAudit == 'Y'.toString()">
                and a.audit_status != '${@com.biz.crm.business.common.base.eunm.AuditStatusEnum@WHOLE_AUDIT.getCode()}'
            </if>
            <if test="vo.changeFlag == 'N'.toString()">
                and b.scheme_type != 'change'
            </if>
            <choose>
                <when test="vo.changeSchemeQueryFlag != null and vo.changeSchemeQueryFlag != '' and 'Y'.toString() == vo.changeSchemeQueryFlag ">
                    <if test="vo.originalSchemeCode != null and vo.originalSchemeCode != ''">
                        <bind name="originalSchemeCode" value="'%' + vo.originalSchemeCode + '%'"/>
                        and b.scheme_code like #{originalSchemeCode}
                    </if>
                    <if test="vo.schemeCode != null and vo.schemeCode != ''">
                        and a.scheme_detail_code not in
                        (
                        SELECT
                        c.original_scheme_detail_code FROM tpm_marketing_plan_case c
                        LEFT JOIN tpm_marketing_plan d ON c.scheme_code = d.scheme_code
                        WHERE
                        d.scheme_code != #{vo.schemeCode}
                        AND d.scheme_type = 'change'
                        AND d.process_status != '3'
                        AND c.original_scheme_detail_code IS NOT NULL
                        GROUP BY
                        c.original_scheme_detail_code
                        )
                    </if>
                    and (
                    not exists (
                    SELECT
                    1
                    FROM
                    tpm_with_holding wh
                    LEFT JOIN tpm_with_holding_write_off wo ON wh.with_holding_code = wo.with_holding_code
                    WHERE
                    wh.del_flag = '009'
                    AND wh.business_code = a.scheme_detail_code
                    )
                    and
                    not exists (
                    SELECT
                    1
                    FROM
                    tpm_marketing_audit ma
                    LEFT JOIN tpm_marketing_audit_detail mab ON ma.audit_code = mab.audit_code
                    WHERE
                    ma.del_flag = '009'
                    AND mab.scheme_detail_code = a.scheme_detail_code
                    )
                    and
                    not exists (
                    SELECT
                    1
                    FROM
                    tpm_fee_cash fc
                    LEFT JOIN tpm_fee_cash_detail fcd ON fc.audit_code = fcd.audit_code
                    WHERE
                    fc.del_flag = '009' and fcd.scheme_detail_code = a.scheme_detail_code
                    )
                    )
                </when>
                <otherwise>
                    <if test="vo.schemeCode != null and vo.schemeCode != ''">
                        <bind name="schemeCode" value="'%' + vo.schemeCode + '%'"/>
                        and b.scheme_code like #{schemeCode}
                    </if>
                </otherwise>
            </choose>
            <if test="vo.companyCode != null and vo.companyCode != ''">
                and a.company_code = #{vo.companyCode}
            </if>
            <if test="vo.excludeCaseTypeList != null and vo.excludeCaseTypeList.size() > 0">
                and a.case_type not in
                <foreach collection="vo.excludeCaseTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.schemeDetailCodeList != null and vo.schemeDetailCodeList.size() > 0">
                and a.scheme_detail_code in
                <foreach collection="vo.schemeDetailCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            order by a.create_time desc,a.scheme_detail_code desc
        </where>

    </select>


    <select id="findCaseListByChangeScheme"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select
        a.*,c.*,
        b.scheme_name,b.department_name,b.position_code,b.org_code,b.org_name from tpm_marketing_plan_case a
        join tpm_marketing_plan_case_extend c on a.scheme_detail_code = c.scheme_detail_code
        left join tpm_marketing_plan b on a.scheme_code = b.scheme_code
        <where>
            b.del_flag = '${@<EMAIL>()}'
            and a.del_flag = '${@<EMAIL>()}'
            <if test="vo.withholding != null and vo.withholding != ''">
                and a.scheme_detail_code not in
                (
                select
                IFNULL(t1.activities_detail_code,'')
                from tpm_with_holding t1
                where t1.del_flag = '${@<EMAIL>()}'
                and (t1.status in ('2','3') or t1.be_adjust='Y')
                )
            </if>
            <if test="processStatus != null and processStatus != ''">
                and b.process_status = #{processStatus}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and a.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and b.scheme_name like #{schemeName}
            </if>
            <if test="vo.categoryName != null and vo.categoryName != ''">
                <bind name="categoryName" value="'%' + vo.categoryName + '%'"/>
                and a.category_name like #{categoryName}
            </if>
            <if test="vo.detailName != null and vo.detailName != ''">
                <bind name="detailName" value="'%' + vo.detailName + '%'"/>
                and a.detail_name like #{detailName}
            </if>
            <if test="vo.belongDepartmentName != null and vo.belongDepartmentName != ''">
                <bind name="belongDepartmentName" value="'%' + vo.belongDepartmentName + '%'"/>
                and a.belong_department_name like #{belongDepartmentName}
            </if>
            <if test="vo.costCenterName != null and vo.costCenterName != ''">
                <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
                and a.cost_center_name like #{costCenterName}
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                <bind name="customerName" value="'%' + vo.customerName + '%'"/>
                and a.customer_name like #{customerName}
            </if>
            <if test="vo.actDesc != null and vo.actDesc != ''">
                <bind name="actDesc" value="'%' + vo.actDesc + '%'"/>
                and c.act_desc like #{actDesc}
            </if>
            <if test="vo.years != null and vo.years != ''">
                and a.years = #{vo.years}
            </if>
            <if test="vo.auditStatus != null and vo.auditStatus != ''">
                and a.audit_status = #{vo.auditStatus}
            </if>
            <if test="vo.yearsLess != null and vo.yearsLess != ''">
                and a.years &lt; #{vo.yearsLess}
            </if>
            <if test="vo.endDateLess != null and vo.endDateLess != ''">
                and a.end_date &lt; #{vo.endDateLess}
            </if>
            <if test="vo.caseType != null and vo.caseType != ''">
                and a.case_type = #{vo.caseType}
            </if>
            <if test="vo.cashType != null and vo.cashType != ''">
                and a.cash_type = #{vo.cashType}
            </if>
            <if test="vo.excludeWholeAudit == 'Y'.toString()">
                and a.audit_status != '${@com.biz.crm.business.common.base.eunm.AuditStatusEnum@WHOLE_AUDIT.getCode()}'
            </if>
            <if test="vo.changeFlag == 'N'.toString()">
                and b.scheme_type != 'change'
            </if>
            <choose>
                <when test="vo.changeSchemeQueryFlag != null and vo.changeSchemeQueryFlag != '' and 'Y'.toString() == vo.changeSchemeQueryFlag ">
                    <if test="vo.originalSchemeCode != null and vo.originalSchemeCode != ''">
                        <bind name="originalSchemeCode" value="'%' + vo.originalSchemeCode + '%'"/>
                        and b.scheme_code like #{originalSchemeCode}
                    </if>
                    <if test="vo.schemeCode != null and vo.schemeCode != ''">
                        and a.scheme_detail_code not in
                        (
                        SELECT
                        c.original_scheme_detail_code FROM tpm_marketing_plan_case c
                        LEFT JOIN tpm_marketing_plan d ON c.scheme_code = d.scheme_code
                        WHERE
                        d.scheme_code != #{vo.schemeCode}
                        AND d.scheme_type = 'change'
                        AND d.process_status != '3'
                        AND c.original_scheme_detail_code IS NOT NULL
                        GROUP BY
                        c.original_scheme_detail_code
                        )
                    </if>
                    and (
                    not exists (
                    SELECT
                    1
                    FROM
                    tpm_with_holding wh
                    LEFT JOIN tpm_with_holding_write_off wo ON wh.with_holding_code = wo.with_holding_code
                    WHERE
                    wh.del_flag = '009'
                    AND wh.business_code = a.scheme_detail_code
                    )
                    and
                    not exists (
                    SELECT
                    1
                    FROM
                    tpm_marketing_audit ma
                    LEFT JOIN tpm_marketing_audit_detail mab ON ma.audit_code = mab.audit_code
                    WHERE
                    ma.del_flag = '009'
                    AND mab.scheme_detail_code = a.scheme_detail_code
                    )
                    and
                    not exists (
                    SELECT
                    1
                    FROM
                    tpm_fee_cash fc
                    LEFT JOIN tpm_fee_cash_detail fcd ON fc.audit_code = fcd.audit_code
                    WHERE
                    fc.del_flag = '009' and fcd.scheme_detail_code = a.scheme_detail_code
                    )
                    )
                </when>
                <otherwise>
                    <if test="vo.schemeCode != null and vo.schemeCode != ''">
                        <bind name="schemeCode" value="'%' + vo.schemeCode + '%'"/>
                        and b.scheme_code like #{schemeCode}
                    </if>
                </otherwise>
            </choose>
            <if test="vo.companyCode != null and vo.companyCode != ''">
                and a.company_code = #{vo.companyCode}
            </if>
            <if test="vo.excludeCaseTypeList != null and vo.excludeCaseTypeList.size() > 0">
                and a.case_type not in
                <foreach collection="vo.excludeCaseTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="originalSchemeDetailCodes != null and originalSchemeDetailCodes.size()>0">
                and a.scheme_detail_code not in
                <foreach collection="originalSchemeDetailCodes" open="(" close=")" separator="," item="item"
                         index="index">
                    #{item}
                </foreach>
            </if>
            <if test="vo.terminalName != null and vo.terminalName != ''">
                <bind name="terminalName" value="'%' + vo.terminalName + '%'"/>
                and a.terminal_name like #{terminalName}
            </if>
            <if test="vo.actExecuteCode">
                <bind name="actExecuteCode" value="'%' + vo.actExecuteCode + '%'"/>
                and a.act_execute_code like #{actExecuteCode}
            </if>
            order by a.create_time desc,a.scheme_detail_code desc
        </where>
    </select>

    <select id="findCloseCaseExampleList" resultType="com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo">
        select code dictCode,name dictValue from tpm_approval_collect
        <where>
            tenant_code = #{tenantCode}
            and del_flag = '${@<EMAIL>()}'
        </where>
    </select>

    <!--  基础sql查询条件  -->
    <sql id="listWhere">
        <if test="vo.delFlag != null and vo.delFlag != ''">
            and a.del_flag = #{vo.delFlag}
        </if>
        <if test="vo.schemeCode != null and vo.schemeCode != ''">
            and a.scheme_code = #{vo.schemeCode}
        </if>
        <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
            and a.scheme_detail_code = #{vo.schemeDetailCode}
        </if>
        <if test="vo.releaseDetailCode != null and vo.releaseDetailCode != ''">
            and a.release_detail_code = #{vo.releaseDetailCode}
        </if>
        <if test="vo.caseType != null and vo.caseType != ''">
            and a.case_type = #{vo.caseType}
        </if>
        <if test="vo.budgetSubjectCode != null and vo.budgetSubjectCode != ''">
            and a.budget_subject_code = #{vo.budgetSubjectCode}
        </if>
        <if test="vo.budgetSubjectName != null and vo.budgetSubjectName != ''">
            and a.budget_subject_name = #{vo.budgetSubjectName}
        </if>
        <if test="vo.categoryCode != null and vo.categoryCode != ''">
            and a.category_code = #{vo.categoryCode}
        </if>
        <if test="vo.detailCode != null and vo.detailCode != ''">
            and a.detail_code = #{vo.detailCode}
        </if>
        <if test="vo.conditionFormula != null and vo.conditionFormula">
            and b.condition_formula = #{vo.conditionFormula}
        </if>
        <if test="vo.lessStartDate != null and vo.lessStartDate != ''">
            <![CDATA[and a.start_date > #{vo.lessStartDate}]]>
        </if>
        <if test="vo.startDate != null and vo.startDate != ''">
            <![CDATA[and a.start_date >= #{vo.startDate}]]>
        </if>
        <if test="vo.endDate != null and vo.endDate != ''">
            <![CDATA[and a.end_date <= #{vo.endDate}]]>
        </if>
        <if test="vo.schemeCodeList != null and vo.schemeCodeList.size()>0">
            and a.scheme_code in
            <foreach collection="vo.schemeCodeList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vo.schemeDetailCodeList != null and vo.schemeDetailCodeList.size()>0">
            and a.scheme_detail_code in
            <foreach collection="vo.schemeDetailCodeList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vo.releaseDetailCodeList != null and vo.releaseDetailCodeList.size()>0">
            and a.release_detail_code in
            <foreach collection="vo.releaseDetailCodeList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vo.categoryCodeList != null and vo.categoryCodeList.size()>0">
            and a.category_code in
            <foreach collection="vo.categoryCodeList" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.detailCodeList != null and vo.detailCodeList.size()>0">
            and a.detail_code in
            <foreach collection="vo.detailCodeList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vo.excludeSchemeDetailCodes != null and vo.excludeSchemeDetailCodes.size()>0">
            and a.scheme_detail_code not in
            <foreach collection="vo.excludeSchemeDetailCodes" separator="," item="item" index="index" open="("
                     close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="findListByBaseCondition"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase">
        select a.*,b.* from tpm_marketing_plan_case a
        join tpm_marketing_plan_case_extend b on a.scheme_detail_code = b.scheme_detail_code
        <where>
            1=1
            <include refid="listWhere"/>
            order by a.create_time desc,a.scheme_detail_code desc
        </where>
    </select>

    <select id="findMaterialListByMaterialCodesAndOrgCodes"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        sum(b.material_num) materialNum,
        sum(a.apply_amount) applyAmount,
        b.material_code,
        a.belong_department_code
        FROM
        tpm_marketing_plan_case a
        JOIN tpm_marketing_plan_case_extend b ON a.scheme_detail_code = b.scheme_detail_code
        LEFT JOIN tpm_marketing_plan c ON a.scheme_code = c.scheme_code
        <where>
            c.del_flag = '${@<EMAIL>()}'
            AND c.tenant_code = #{tenantCode}
            AND a.case_type = 'BDMB0004'
            AND b.sell_material_type = '0'
            <if test="materialCodes != null and materialCodes.size()>0">
                AND b.material_code in
                <foreach collection="materialCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgCodes != null and orgCodes.size()>0">
                AND a.belong_department_code in
                <foreach collection="orgCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            group by
            b.material_code,
            a.belong_department_code
        </where>
    </select>

    <select id="findMaterialListByMaterialCodesAndOrgCodesAndExclusiveSchemeCode"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        sum(b.material_num) materialNum,
        sum(a.apply_amount) applyAmount,
        b.material_code,
        a.belong_department_code
        FROM
        tpm_marketing_plan_case a
        JOIN tpm_marketing_plan_case_extend b ON a.scheme_detail_code = b.scheme_detail_code
        LEFT JOIN tpm_marketing_plan c ON a.scheme_code = c.scheme_code and c.scheme_code = b.scheme_code
        <where>
            c.del_flag = '${@<EMAIL>()}'
            AND c.tenant_code = #{tenantCode}
            AND a.case_type = 'BDMB0004'
            AND b.sell_material_type = '0'
            <if test="materialCodes != null and materialCodes.size()>0">
                AND b.material_code in
                <foreach collection="materialCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgCodes != null and orgCodes.size()>0">
                AND a.belong_department_code in
                <foreach collection="orgCodes" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="schemeCode != null and schemeCode != ''">
                and a.scheme_code != #{schemeCode}
            </if>
            group by
            b.material_code,
            a.belong_department_code
        </where>
    </select>

    <select id="findAuditAndWithholdingAndCashReleaseDetailCodeList"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        <choose>
            <when test="'N'.toString() == bearFlag">
                head_scheme_detail_code releaseDetailCode,
                head_scheme_code releaseCode,
            </when>
            <otherwise>
                release_detail_code,
                release_code,
            </otherwise>
        </choose>
        a.release_detail_code releaseDetailCodeTwo,
        a.scheme_detail_code,
        c.audit_amount auditAmount,
        d.cash_amount cashAmount,
        a.apply_amount
        FROM
        tpm_marketing_plan_case a
        LEFT JOIN tpm_marketing_plan b ON a.scheme_code = b.scheme_code
        left join (
            SELECT
            b.scheme_detail_code,
            sum(ifnull(b.audit_amount,0)) audit_amount
            FROM
            tpm_marketing_audit a
            LEFT JOIN tpm_marketing_audit_detail b ON a.audit_code = b.audit_code
            WHERE
            a.status = '3'
            AND a.del_flag = '009'
            GROUP BY
            b.scheme_detail_code
        ) c on a.scheme_detail_code = c.scheme_detail_code
        left join (
            SELECT
            b.scheme_detail_code,
            sum(IFNULL(b.this_cash_amount,0)) cash_amount
            FROM
            tpm_fee_cash a
            LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
            WHERE
            a.status = '3'
            AND a.del_flag = '009'
            GROUP BY
            b.scheme_detail_code
        ) d on a.scheme_detail_code = d.scheme_detail_code
        WHERE
        a.del_flag = '${@<EMAIL>()}'
        AND b.tenant_code = #{tenantCode}
        AND b.process_status = '3'
        and b.scheme_type != 'change'
        <choose>
            <when test="'Y'.toString() == bearFlag">
                AND release_detail_code IN
                <foreach collection="releaseDetailCodeList" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND head_scheme_detail_code IN
                <foreach collection="releaseDetailCodeList" open="(" close=")" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
    </select>

    <select id="findRegionMarketingPlanCase"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT b.*, a.scheme_name,d.cash_amount cashAmountF,ho.with_holding_amount
        FROM tpm_marketing_plan a
                 LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
                 left join tpm_marketing_plan_case_extend c
                           on b.scheme_detail_code = c.scheme_detail_code
                           LEFT JOIN (
	SELECT
		b.scheme_detail_code,
		sum(
		IFNULL( b.this_cash_amount, 0 )) cash_amount
	FROM
		tpm_fee_cash a
		LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
	WHERE
		a.STATUS = '3'
		AND a.del_flag = '009'
	GROUP BY
		b.scheme_detail_code
	) d ON b.scheme_detail_code = d.scheme_detail_code
	left join tpm_with_holding ho on ho.activities_detail_code=b.scheme_detail_code and ho.del_flag='${@<EMAIL>()}'
        WHERE a.del_flag = '${@<EMAIL>()}'
          AND a.tenant_code = #{tenantCode}
          AND b.release_detail_code = #{releaseDetailCode}
          and a.scheme_type != 'change'
    </select>

    <select id="findMarketingPlanCaseReportList"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        b.*,a.scheme_name, a.scheme_type, c.*
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        left join tpm_marketing_plan_case_extend c on b.scheme_detail_code = c.scheme_detail_code and a.scheme_code =
        c.scheme_code
        where a.del_flag = '${@<EMAIL>()}'
        and b.del_flag = '${@<EMAIL>()}'
        AND a.tenant_code = #{tenantCode}
        and a.process_status = '3'
        <if test="vo.budgetSubjectCode != null and vo.budgetSubjectCode != ''">
            <bind name="budgetSubjectCode" value="'%' + vo.budgetSubjectCode + '%'"/>
            and b.budget_subject_code like #{budgetSubjectCode}
        </if>
        <if test="vo.headSchemeCode != null and vo.headSchemeCode != ''">
            <bind name="headSchemeCode" value="'%' + vo.headSchemeCode + '%'"/>
            and b.head_scheme_code like #{headSchemeCode}
        </if>
        <if test="vo.headSchemeCodes != null and vo.headSchemeCodes.size()>0">
            and b.head_scheme_code in
            <foreach collection="vo.headSchemeCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.budgetSubjectName != null and vo.budgetSubjectName != ''">
            <bind name="budgetSubjectName" value="'%' + vo.budgetSubjectName + '%'"/>
            and b.budget_subject_name like #{budgetSubjectName}
        </if>
        <if test="vo.auditStatus != null and vo.auditStatus != ''">
            and b.audit_status = #{vo.auditStatus}
        </if>
        <if test="vo.cashStatus != null and vo.cashStatus != ''">
            and b.cash_status = #{vo.cashStatus}
        </if>
        <if test="vo.pushStatus != null and vo.pushStatus != ''">
            and c.push_status = #{vo.pushStatus}
        </if>
        <if test="vo.hzlx != null and vo.hzlx != ''">
            and c.hzlx = #{vo.hzlx}
        </if>
        <if test="vo.releaseCode != null and vo.releaseCode != ''">
            <bind name="releaseCode" value="'%' + vo.releaseCode + '%'"/>
            and b.release_code like #{releaseCode}
        </if>
        <if test="vo.releaseName != null and vo.releaseName != ''">
            <bind name="releaseName" value="'%' + vo.releaseName + '%'"/>
            and b.release_name like #{releaseName}
        </if>
        <if test="vo.withHoldingStatus != null and vo.withHoldingStatus != ''">
            and c.with_holding_status = #{vo.withHoldingStatus}
        </if>
        <if test="vo.detailCode != null and vo.detailCode != ''">
            <bind name="detailCode" value="'%' + vo.detailCode + '%'"/>
            and b.detail_code like #{detailCode}
        </if>
        <if test="vo.detailName != null and vo.detailName != ''">
            <bind name="detailName" value="'%' + vo.detailName + '%'"/>
            and b.detail_name like #{detailName}
        </if>
        <if test="vo.schemeName != null and vo.schemeName != ''">
            <bind name="likeSchemeName" value="'%' + vo.schemeName + '%'"/>
            and a.scheme_name like #{likeSchemeName}
        </if>
        <if test="vo.schemeCode != null and vo.schemeCode != ''">
            <bind name="schemeCode" value="'%' + vo.schemeCode + '%'"/>
            and b.scheme_code like #{schemeCode}
        </if>
        <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
            <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
            and b.scheme_detail_code like #{schemeDetailCode}
        </if>
        <if test="vo.schemeDetailCodeList != null and vo.schemeDetailCodeList.size()>0">
            and b.scheme_detail_code in
            <foreach collection="vo.schemeDetailCodeList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.actName != null and vo.actName != ''">
            <bind name="actName" value="'%' + vo.actName + '%'"/>
            and b.act_name like #{actName}
        </if>
        <if test="vo.terminalCode != null and vo.terminalCode != ''">
            <bind name="likeTerminalCode" value="'%' + vo.terminalCode + '%'"/>
            and b.terminal_code like #{likeTerminalCode}
        </if>
        <if test="vo.terminalName != null and vo.terminalName != ''">
            <bind name="likeTerminalName" value="'%' + vo.terminalName + '%'"/>
            and b.terminal_name like #{likeTerminalName}
        </if>
        <if test="vo.customerCode != null and vo.customerCode != ''">
            <bind name="likeCustomerCode" value="'%' + vo.customerCode + '%'"/>
            and b.customer_code like #{likeCustomerCode}
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            <bind name="likeCustomerName" value="'%' + vo.customerName + '%'"/>
            and b.customer_name like #{likeCustomerName}
        </if>
        <if test="vo.years != null and vo.years != ''">
            and b.years = #{vo.years}
        </if>
        <if test="vo.budgetYearsStart != null and vo.budgetYearsStart != ''">
            and b.years <![CDATA[ >= ]]> #{vo.budgetYearsStart}
        </if>
        <if test="vo.budgetYearsEnd != null and vo.budgetYearsEnd != ''">
            and b.years <![CDATA[ <= ]]> #{vo.budgetYearsEnd}
        </if>
        <if test="vo.startDate != null and vo.startDate != ''">
            and b.start_date <![CDATA[ >= ]]> #{vo.startDate}
        </if>
        <if test="vo.endDate != null and vo.endDate != ''">
            and b.end_date <![CDATA[ <= ]]> #{vo.endDate}
        </if>
        <if test="vo.actExecuteCode != null and vo.actExecuteCode != ''">
            and b.act_execute_code = #{vo.actExecuteCode}
        </if>
        <if test="vo.changeFlag == 'N'.toString()">
            and a.scheme_type != 'change'
        </if>
        <if test="vo.bearDepartmentName != null and vo.bearDepartmentName != ''">
            <bind name="bearDepartmentName" value="'%' + vo.bearDepartmentName + '%'"/>
            and b.bear_department_name like #{bearDepartmentName}
        </if>
        <if test="vo.bearDepartmentCode != null and vo.bearDepartmentCode != ''">
            <bind name="bearDepartmentCode" value="'%' + vo.bearDepartmentCode + '%'"/>
            and b.bear_department_code like #{bearDepartmentCode}
        </if>
        <if test="vo.belongDepartmentCode != null and vo.belongDepartmentCode != ''">
            <bind name="belongDepartmentCode" value="'%' + vo.belongDepartmentCode + '%'"/>
            and b.belong_department_code like #{belongDepartmentCode}
        </if>
        <if test="vo.belongDepartmentName != null and vo.belongDepartmentName != ''">
            <bind name="belongDepartmentName" value="'%' + vo.belongDepartmentName + '%'"/>
            and b.belong_department_name like #{belongDepartmentName}
        </if>
        <if test="vo.costCenterCode != null and vo.costCenterCode != ''">
            <bind name="costCenterCode" value="'%' + vo.costCenterCode + '%'"/>
            and b.cost_center_code like #{costCenterCode}
        </if>
        <if test="vo.costCenterName != null and vo.costCenterName != ''">
            <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
            and b.cost_center_name like #{costCenterName}
        </if>
        <if test="vo.caseType != null and vo.caseType != ''">
            and b.case_type = #{vo.caseType}
        </if>
        <if test="vo.cashType != null and vo.cashType != ''">
            and b.cash_type = #{vo.cashType}
        </if>
        <if test="vo.actStatus != null and vo.actStatus != ''">
            <choose>
                <when test=" '0'.toString() == vo.actStatus">
                    <![CDATA[and b.start_date > DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
                </when>
                <when test=" '1'.toString() == vo.actStatus">
                    <![CDATA[and b.start_date <= DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
                    <![CDATA[and b.end_date >= DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
                </when>
                <when test=" '2'.toString() == vo.actStatus">
                    <![CDATA[and b.end_date <= DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
                </when>
            </choose>
        </if>
        <if test="vo.createName != null and vo.createName != ''">
            <bind name="createName" value="'%' + vo.createName + '%'"/>
            and a.create_name like #{createName}
        </if>
        <if test="vo.createAccount != null and vo.createAccount != ''">
            <bind name="createAccount" value="'%' + vo.createAccount + '%'"/>
            and a.create_account like #{createAccount}
        </if>
        <if test="vo.belongDepartmentCodes != null and vo.belongDepartmentCodes.size()>0">
            and b.belong_department_code in
            <foreach collection="vo.belongDepartmentCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.bearDepartmentCodes != null and vo.bearDepartmentCodes.size()>0">
            and b.bear_department_code in
            <foreach collection="vo.bearDepartmentCodes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.actDesc != null and vo.actDesc != ''">
            <bind name="actDesc" value="'%' + vo.actDesc + '%'"/>
            and c.act_desc like #{actDesc}
        </if>
        <if test="vo.orgCodeList != null and vo.orgCodeList.size()>0">
            and b.belong_department_code in
            <foreach collection="vo.orgCodeList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.withholding != null and vo.withholding != ''">
            and b.scheme_detail_code not in
            (
            select
            IFNULL(t1.activities_detail_code,'')
            from tpm_with_holding t1
            where t1.del_flag = '${@<EMAIL>()}'
            and (t1.status in ('2','3') or t1.be_adjust='Y' or t1.confirm_status='confirmed')
            )
        </if>
        <if test="vo.processStatus != null and vo.processStatus != ''">
            and a.process_status = #{vo.processStatus}
        </if>
        <if test="vo.yearsSet != null and vo.yearsSet.size > 0">
            and b.years in
            <foreach item="item" collection="vo.yearsSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="vo.subjectCodes != null and vo.subjectCodes.size > 0">
            and b.budget_subject_code in
            <foreach item="item" collection="vo.subjectCodes" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="vo.customerCodes != null and vo.customerCodes.size > 0">
            and b.customer_code in
            <foreach item="item" collection="vo.customerCodes" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="vo.cashTypeSet != null and vo.cashTypeSet.size > 0">
            and b.cash_type in
            <foreach item="item" collection="vo.cashTypeSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="vo.detailCodeList != null and vo.detailCodeList.size() > 0">
            and b.detail_code in
            <foreach item="item" collection="vo.detailCodeList" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        order by b.scheme_detail_code desc, b.id desc
    </select>

    <select id="findNotMarketingClosedCaseList"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select a.*,b.*,p.scheme_name from tpm_marketing_plan p
        left join tpm_marketing_plan_case a on p.scheme_code = a.scheme_code
        left join tpm_marketing_plan_case_extend b on a.scheme_detail_code = b.scheme_detail_code and p.scheme_code =
        b.scheme_code
        <where>
            p.process_status = '3'
            and p.tenant_code = #{tenantCode}
            <include refid="listWhere"/>
            <if test="vo.schemeName != null and vo.schemeName != ''">
                <bind name="schemeName" value="'%' + vo.schemeName + '%'"/>
                and p.scheme_name like #{schemeName}
            </if>
            <if test="vo.actName != null and vo.actName != ''">
                <bind name="actName" value="'%' + vo.actName + '%'"/>
                and a.act_name like #{actName}
            </if>
            <if test="vo.detailName != null and vo.detailName != ''">
                <bind name="detailName" value="'%' + vo.detailName + '%'"/>
                and a.detail_name like #{detailName}
            </if>
            <if test="vo.belongDepartmentName != null and vo.belongDepartmentName != ''">
                <bind name="belongDepartmentName" value="'%' + vo.belongDepartmentName + '%'"/>
                and a.belong_department_name like #{belongDepartmentName}
            </if>
            <if test="vo.bearDepartmentName != null and vo.bearDepartmentName != ''">
                <bind name="bearDepartmentName" value="'%' + vo.bearDepartmentName + '%'"/>
                and a.bear_department_name like #{bearDepartmentName}
            </if>
            <if test="vo.costCenterName != null and vo.costCenterName != ''">
                <bind name="costCenterName" value="'%' + vo.costCenterName + '%'"/>
                and a.cost_center_name like #{costCenterName}
            </if>
            <if test="vo.customerCode != null and vo.customerCode != ''">
                <bind name="customerCode" value="'%' + vo.customerCode + '%'"/>
                and a.customer_code like #{customerCode}
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                <bind name="customerName" value="'%' + vo.customerName + '%'"/>
                and a.customer_name like #{customerName}
            </if>
            <if test="vo.companyCode != null and vo.companyCode != ''">
                <bind name="companyCode" value="'%' + vo.companyCode + '%'"/>
                and a.company_code like #{companyCode}
            </if>
            <if test="vo.terminalName != null and vo.terminalName != ''">
                <bind name="terminalName" value="'%' + vo.terminalName + '%'"/>
                and a.terminal_name like #{terminalName}
            </if>
            <if test="vo.terminalCode != null and vo.terminalCode != ''">
                <bind name="terminalCode" value="'%' + vo.terminalCode + '%'"/>
                and a.terminal_code like #{terminalCode}
            </if>
            order by a.create_time desc,a.scheme_detail_code desc
        </where>
    </select>

    <select id="findMarketingPlanCaseExecuteList"
            resultType="com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo">
        select * from (
        select
        tpc.id,
        tpc.scheme_code,
        tp.scheme_name,
        tpc.scheme_detail_code,
        tpc.act_execute_code,
        tpc.act_name,
        tpc.category_code,
        tpc.category_name,
        tpc.detail_code,
        tpc.detail_name,
        tpc.start_date,
        tpc.end_date,
        tpc.years,
        tpc.customer_code,
        tpc.customer_name,
        tpc.terminal_code,
        tpc.terminal_name,
        tpc.terminal_type,
        tpc.apply_amount,
        tpc.remark,
        tpc.create_name,
        tpc.create_time
        from tpm_marketing_plan_case tpc
        left join tpm_marketing_plan tp ON tpc.scheme_code = tp.scheme_code
        left join tpm_marketing_plan_case_extend tpce ON tpce.id = tpc.id
        where tpce.much_department_flag = 'N'
        <include refid="sfa_act_where"></include>
        union all
        select
        tpc.id,
        tpc.scheme_code,
        tp.scheme_name,
        tpc.scheme_detail_code,
        tpc.act_execute_code,
        tpc.act_name,
        tpc.category_code,
        tpc.category_name,
        tpc.detail_code,
        tpc.detail_name,
        tpc.start_date,
        tpc.end_date,
        tpc.years,
        tpc.customer_code,
        tpc.customer_name,
        tpc.terminal_code,
        tpc.terminal_name,
        tpc.terminal_type,
        tpc.apply_amount,
        tpc.remark,
        tpc.create_name,
        tpc.create_time
        from tpm_marketing_plan_case tpc
        left join tpm_marketing_plan tp ON tpc.scheme_code = tp.scheme_code
        JOIN ( SELECT tpc1.act_execute_code, MIN(tpc1.scheme_detail_code ) AS scheme_detail_code
        FROM tpm_marketing_plan_case tpc1
        left join tpm_marketing_plan tp1 ON tpc1.scheme_code = tp1.scheme_code
        left join tpm_marketing_plan_case_extend tpce ON tpce.id = tpc1.id
        where tpce.much_department_flag = 'Y'
        <include refid="sfa_act_where_two"></include>
        GROUP BY tpc1.act_execute_code ) dis ON dis.scheme_detail_code = tpc.scheme_detail_code
        where 1=1
        <include refid="sfa_act_where"></include>
        ) tt
        order by tt.create_time desc,tt.id desc
    </select>
    <sql id="sfa_act_where">
        and tpc.del_flag = '${@<EMAIL>()}'
        and tp.process_status = '3'
        and tp.scheme_type in ('plan','append','o_two_o','contract')
        and tpc.years = #{dto.years}
        and tpc.start_date &lt;= #{dto.executionTime}
        and tpc.end_date &gt;= #{dto.executionTime}
        and tpc.act_execute_code
        <if test="dto.executeStatus != 'Y'.toString()">not</if>
        in (
        select taecl.act_execute_code
        from tpm_act_execution_collect_log taecl
        where taecl.execution_type = #{dto.executionType}
        and taecl.client_code in
        <foreach collection="dto.clientCodeList" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        and taecl.position_code = #{dto.positionCode}
        and taecl.execution_time = #{dto.executionTime}
        )
        <if test="dto.schemeCode != null and dto.schemeCode != ''">
            <bind name="schemeCode" value="'%' + dto.schemeCode + '%'"/>
            and tpc.scheme_code like #{schemeCode}
        </if>
        <if test="dto.schemeDetailCode != null and dto.schemeDetailCode != ''">
            <bind name="schemeDetailCode" value="'%' + dto.schemeDetailCode + '%'"/>
            and tpc.scheme_detail_code like #{schemeDetailCode}
        </if>
        <if test="dto.detailCodeList != null and dto.detailCodeList.size()>0">
            and tpc.detail_code in
            <foreach collection="dto.detailCodeList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.actName != null and dto.actName != ''">
            <bind name="actName" value="'%' + dto.actName + '%'"/>
            and tpc.act_name like #{actName}
        </if>
        <if test="dto.terminalCode != null and dto.terminalCode != ''">
            and tpc.terminal_code = #{dto.terminalCode}
        </if>
        <if test="dto.customerCode != null and dto.customerCode != ''">
            and tpc.customer_code = #{dto.customerCode}
        </if>
        <if test="dto.categoryName != null and dto.categoryName != ''">
            <bind name="categoryName" value="'%' + dto.categoryName + '%'"/>
            and tpc.category_name like #{categoryName}
        </if>
    </sql>
    <sql id="sfa_act_where_two">
        and tpc1.del_flag = '${@<EMAIL>()}'
        and tp1.process_status = '3'
        and tp1.scheme_type in ('plan','append','o_two_o','contract')
        and tpc1.years = #{dto.years}
        and tpc1.start_date &lt;= #{dto.executionTime}
        and tpc1.end_date &gt;= #{dto.executionTime}
        and tpc1.act_execute_code
        <if test="dto.executeStatus != 'Y'.toString()">not</if>
        in (
        select taecl.act_execute_code
        from tpm_act_execution_collect_log taecl
        where taecl.execution_type = #{dto.executionType}
        and taecl.client_code in
        <foreach collection="dto.clientCodeList" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        and taecl.position_code = #{dto.positionCode}
        and taecl.execution_time = #{dto.executionTime}
        )
        <if test="dto.schemeCode != null and dto.schemeCode != ''">
            <bind name="schemeCode" value="'%' + dto.schemeCode + '%'"/>
            and tpc1.scheme_code like #{schemeCode}
        </if>
        <if test="dto.schemeDetailCode != null and dto.schemeDetailCode != ''">
            <bind name="schemeDetailCode" value="'%' + dto.schemeDetailCode + '%'"/>
            and tpc1.scheme_detail_code like #{schemeDetailCode}
        </if>
        <if test="dto.detailCodeList != null and dto.detailCodeList.size()>0">
            and tpc1.detail_code in
            <foreach collection="dto.detailCodeList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.actName != null and dto.actName != ''">
            <bind name="actName" value="'%' + dto.actName + '%'"/>
            and tpc1.act_name like #{actName}
        </if>
        <if test="dto.terminalCode != null and dto.terminalCode != ''">
            and tpc1.terminal_code = #{dto.terminalCode}
        </if>
        <if test="dto.customerCode != null and dto.customerCode != ''">
            and tpc1.customer_code = #{dto.customerCode}
        </if>
        <if test="dto.categoryName != null and dto.categoryName != ''">
            <bind name="categoryName" value="'%' + dto.categoryName + '%'"/>
            and tpc1.category_name like #{categoryName}
        </if>
    </sql>
    <select id="findTerminalExpense"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.TerminalExpenseVo">
        SELECT
        tpc.terminal_code,
        tpc.terminal_name,
        tpc.category_code,
        tpc.category_name,
        SUM( tpc.apply_amount ) AS applyAmount,
        SUM( tpc.cash_amount ) AS cashAmount
        FROM
        tpm_marketing_plan_case tpc
        LEFT JOIN tpm_marketing_plan tp ON tpc.scheme_code = tp.scheme_code
        WHERE
        1 = 1
        AND tp.del_flag = '${@<EMAIL>()}'
        AND tp.process_status = '3'
        AND tpc.terminal_code = #{vo.terminalCode}
        <if test="vo.startDate != null and vo.startDate != ''">
            <![CDATA[and tpc.start_date <= #{vo.startDate}]]>
        </if>
        <if test="vo.endDate != null and vo.endDate != ''">
            <![CDATA[and tpc.end_date >= #{vo.endDate}]]>
        </if>
        <if test="vo.years != null and vo.years != ''">
            and tpc.years = #{vo.years}
        </if>
        GROUP BY
        tpc.terminal_code,
        tpc.terminal_name,
        tpc.category_code,
        tpc.category_name
    </select>
    <select id="findCustomerExpense"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.TerminalExpenseVo">
        SELECT
        tpc.customer_code,
        tpc.customer_name,
        tpc.category_code,
        tpc.category_name,
        SUM( tpc.apply_amount ) AS applyAmount,
        SUM( tpc.cash_amount ) AS cashAmount
        FROM
        tpm_marketing_plan_case tpc
        LEFT JOIN tpm_marketing_plan tp ON tpc.scheme_code = tp.scheme_code
        WHERE
        1 = 1
        AND tp.del_flag = '${@<EMAIL>()}'
        AND tp.process_status != '6'
        AND tpc.customer_code = #{vo.customerCode}
        <if test="vo.year != null and vo.year != ''">
            <bind name="yearLike" value="vo.year + '%'"/>
            and tpc.years like #{yearLike}
        </if>
        <if test="vo.years != null and vo.years != ''">
            and tpc.years = #{vo.years}
        </if>
        GROUP BY
        tpc.customer_code,
        tpc.customer_name,
        tpc.category_code,
        tpc.category_name
    </select>


    <select id="findContractByDepartmentCodesAndYears"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select a.*,b.* from tpm_marketing_plan p
        left join tpm_marketing_plan_case a on p.scheme_code = a.scheme_code
        left join tpm_marketing_plan_case_extend b on a.scheme_detail_code = b.scheme_detail_code
        <where>
            1=1
            and p.tenant_code = #{tenantCode}
            and p.del_flag = '009'
            and p.scheme_type = 'contract'
            <if test="years != null and years != ''">
                and a.years = #{years}
            </if>
            <if test="departmentCodes != null and departmentCodes.size()>0">
                and a.belong_department_code in
                <foreach collection="departmentCodes" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            order by a.create_time desc,a.scheme_detail_code desc
        </where>
    </select>

    <select id="findMarketingPlanApplyAmount"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.TpmCostBudgetVo">
        SELECT
        sum(b.apply_amount) applyAmount,
        b.budget_code code,
        b.scheme_detail_code businessDetailCode,
        b.case_type
        FROM (
        SELECT
        CASE d.original_scheme_detail_code IS NOT NULL AND d.original_scheme_detail_code = b.scheme_detail_code
        WHEN d.process_status = '2' AND <![CDATA[d.apply_amount < b.apply_amount]]> THEN b.apply_amount
        WHEN d.process_status = '2' AND <![CDATA[d.apply_amount > b.apply_amount]]> THEN d.apply_amount
        WHEN d.process_status = '3' THEN d.apply_amount
        ELSE b.apply_amount END apply_amount,
        c.budget_code,
        b.scheme_detail_code,
        b.case_type
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        LEFT JOIN tpm_marketing_plan_case_extend c ON a.scheme_code = c.scheme_code
        AND b.scheme_detail_code = c.scheme_detail_code
        LEFT JOIN (
        SELECT
        b.apply_amount,
        c.budget_code,
        b.original_scheme_detail_code,
        a.process_status
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        LEFT JOIN tpm_marketing_plan_case_extend c ON a.scheme_code = c.scheme_code
        AND b.scheme_detail_code = c.scheme_detail_code
        WHERE
        a.tenant_code = 'default'
        AND a.del_flag = '${@<EMAIL>()}'
        AND b.del_flag = '${@<EMAIL>()}'
        AND a.process_status in('2', '3')
        AND c.budget_code IS NOT NULL
        AND c.budget_code != ''
        AND a.scheme_type = 'change'
        AND
        (b.scheme_detail_code = (
            SELECT MAX(sub_b.scheme_detail_code)
            FROM tpm_marketing_plan sub_a
            INNER JOIN tpm_marketing_plan_case sub_b ON sub_a.scheme_code = sub_b.scheme_code
            WHERE sub_b.original_scheme_detail_code = b.original_scheme_detail_code
            ) or b.original_scheme_detail_code is null
        )
        ) d ON b.scheme_detail_code = d.original_scheme_detail_code
        WHERE
        a.tenant_code = 'default'
        AND a.del_flag = '${@<EMAIL>()}'
        AND b.del_flag = '${@<EMAIL>()}'
        AND a.process_status in('2', '3')
        AND c.budget_code in
        <foreach collection="budgetCodes" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
        AND a.scheme_type != 'change') b
        GROUP BY
        b.budget_code,
        b.scheme_detail_code,
        b.case_type
    </select>


    <select id="findMarketingPlanChangeCommitApplyAmount" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.TpmCostBudgetVo">
        SELECT
        sum(b.apply_amount) applyAmount,
        c.budget_code code,
        b.scheme_detail_code businessDetailCode,
        b.case_type
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        LEFT JOIN tpm_marketing_plan_case_extend c ON b.scheme_detail_code = c.scheme_detail_code
        WHERE
        a.scheme_type = 'change'
        AND a.process_status = '2'
        and b.del_flag = '009'
        and a.del_flag = '009'
        AND b.original_scheme_detail_code IS NULL
        AND c.budget_code in
        <foreach collection="budgetCodes" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
        GROUP BY
        c.budget_code,
        b.scheme_detail_code,
        b.case_type
    </select>

    <select id="findHandleWithholdingAmount"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.TpmCostBudgetVo">
        SELECT
        wh.budget_code code,
        SUM(IFNULL(wh.actual_amount, 0)) - SUM(IFNULL(wo.write_off_amount, 0)) withholdingAmount
        FROM
        tpm_with_holding wh
        LEFT JOIN tpm_with_holding_write_off wo ON wh.with_holding_code = wo.with_holding_code
        WHERE
        wh.del_flag = '${@<EMAIL>()}'
        AND wh.status = '3'
        and wh.with_holding_type = 'handle'
        AND wh.budget_code in
        <foreach collection="budgetCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY
        wh.budget_code
    </select>

    <select id="findWithholdingAmount" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.TpmCostBudgetVo">
        SELECT
        a.budget_code code,
        wh.activities_detail_code businessDetailCode,
        SUM(IFNULL(wh.actual_amount, 0)) withholdingAmount
        FROM
        tpm_with_holding wh LEFT JOIN tpm_marketing_plan_case_extend a
        on wh.activities_detail_code = a.scheme_detail_code
        WHERE
        wh.del_flag = '${@<EMAIL>()}'
        AND wh.status = '3'
        and wh.with_holding_type in ('not_audit','not_cash')
        AND a.budget_code in
        <foreach collection="budgetCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY
        a.budget_code,
        wh.activities_detail_code
    </select>


    <select id="findEndCaseAmount" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.TpmCostBudgetVo">
        SELECT
        c.budget_code code,
        a.scheme_detail_code businessDetailCode,
        CASE
        WHEN SUM(CASE WHEN a.be_full_audit = 'Y' THEN 1 ELSE 0 END) > 0 THEN SUM(a.audit_amount)
        ELSE -1
        END AS endCaseAmount
        FROM
        tpm_marketing_audit_detail a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_detail_code = b.scheme_detail_code
        LEFT JOIN tpm_marketing_plan_case_extend c ON b.scheme_detail_code = c.scheme_detail_code
        LEFT JOIN tpm_marketing_audit d ON a.audit_code = d.audit_code
        WHERE
        d.status = '3'
        and c.budget_code in
        <foreach collection="budgetCodes" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
        GROUP BY
        c.budget_code,
        a.scheme_detail_code
    </select>

    <select id="findWithholdingBySchemeCodes" resultType="java.lang.String">
        SELECT
        business_code
        FROM
        tpm_with_holding
        WHERE
        business_code in
        <foreach collection="schemeDetailCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND del_flag = '${@<EMAIL>()}'
    </select>

    <select id="findSendSfaCostTypeDetail" resultType="java.lang.String">
        SELECT tctd.detail_code
        FROM tpm_cost_type_detail tctd
        WHERE tctd.del_flag = '${@<EMAIL>()}'
          and tctd.enable_status = '${@<EMAIL>()}'
          and tctd.is_send_sfa = 'Y'
          and tctd.execution_type = #{executionType}
    </select>

    <select id="findAlreadyUndertakeAmount"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT
        a.release_detail_code,
        CASE WHEN b.process_status = '3'
        AND b.scheme_type = 'change' THEN
        0
        ELSE
        ifnull(a.apply_amount, 0)
        END apply_amount
        FROM
        tpm_marketing_plan_case a
        LEFT JOIN tpm_marketing_plan b ON a.scheme_code = b.scheme_code
        WHERE
        a.del_flag = '009'
        <if test="releaseDetailCodes != null and releaseDetailCodes.size()>0">
            and release_detail_code in
            <foreach collection="releaseDetailCodes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="findCollectCostTypeDetail"
            resultType="com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo">
        SELECT
        t.*
        FROM
        tpm_cost_type_detail_collect t
        WHERE
        t.detail_code IN
        <foreach collection="detailCodeList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND t.del_flag = '009'
        AND t.type = 'collect'
    </select>
    <select id="findCollectImage" resultType="com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectImageVo">
        SELECT
        t.*
        FROM
        tpm_approval_collect_image t
        WHERE
        t.approval_collect_code IN
        <foreach collection="collectCodeList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="findByTerminalMarketingPlanCaseExecuteList"
            resultType="com.biz.crm.tpm.business.activities.sdk.vo.MarketingPlanCaseExecuteVo">
        select * from (
        select
        tpc.id,
        tpc.scheme_code,
        tp.scheme_name,
        tpc.scheme_detail_code,
        tpc.act_execute_code,
        tpc.act_name,
        tpc.category_code,
        tpc.category_name,
        tpc.detail_code,
        tpc.detail_name,
        tpc.start_date,
        tpc.end_date,
        tpc.years,
        tpc.customer_code,
        tpc.customer_name,
        tpc.terminal_code,
        tpc.terminal_name,
        tpc.terminal_type,
        tpc.apply_amount,
        tpc.remark,
        tpc.create_name,
        tpc.create_time
        from tpm_marketing_plan_case tpc
        left join tpm_marketing_plan tp ON tpc.scheme_code = tp.scheme_code
        left join tpm_marketing_plan_case_extend tpce ON tpce.id = tpc.id
        where tpce.much_department_flag = 'N'
        <include refid="sfa_act_where"></include>
        union all
        select
        tpc.id,
        tpc.scheme_code,
        tp.scheme_name,
        tpc.scheme_detail_code,
        tpc.act_execute_code,
        tpc.act_name,
        tpc.category_code,
        tpc.category_name,
        tpc.detail_code,
        tpc.detail_name,
        tpc.start_date,
        tpc.end_date,
        tpc.years,
        tpc.customer_code,
        tpc.customer_name,
        tpc.terminal_code,
        tpc.terminal_name,
        tpc.terminal_type,
        tpc.apply_amount,
        tpc.remark,
        tpc.create_name,
        tpc.create_time
        from tpm_marketing_plan_case tpc
        left join tpm_marketing_plan tp ON tpc.scheme_code = tp.scheme_code
        JOIN ( SELECT tpc1.act_execute_code, MIN(tpc1.scheme_detail_code ) AS scheme_detail_code
        FROM tpm_marketing_plan_case tpc1
        left join tpm_marketing_plan tp1 ON tpc1.scheme_code = tp1.scheme_code
        left join tpm_marketing_plan_case_extend tpce ON tpce.id = tpc1.id
        where tpce.much_department_flag = 'Y'
        <include refid="sfa_act_where_two"></include>
        GROUP BY tpc1.act_execute_code ) dis ON dis.scheme_detail_code = tpc.scheme_detail_code
        where 1=1
        <include refid="sfa_act_where"></include>
        ) tt
        order by tt.create_time desc,tt.id desc
    </select>

    <select id="findChanging" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT c.* FROM tpm_marketing_plan_case c
        INNER JOIN tpm_marketing_plan d ON c.scheme_code = d.scheme_code
        where
        d.process_status != '3'
        and c.scheme_detail_code in
        <foreach collection="schemeDetailCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND c.del_flag = '${@<EMAIL>()}'
        AND d.del_flag = '${@<EMAIL>()}'
    </select>
    <select id="findByCollectCode"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select tmpc.*, tmp.scheme_name, e.act_desc
        from tpm_marketing_plan_case tmpc
                 left join tpm_marketing_plan tmp on tmpc.scheme_code = tmp.scheme_code
                 left join tpm_region_collect_scheme trcs on trcs.scheme_code = tmp.scheme_code
                 LEFT JOIN tpm_marketing_plan_case_extend e ON e.scheme_detail_code = tmpc.scheme_detail_code
        where trcs.collect_code = #{collectCode}
          AND tmpc.del_flag = '${@<EMAIL>()}'
          AND tmp.del_flag = '${@<EMAIL>()}'
          AND trcs.del_flag = '${@<EMAIL>()}'
    </select>
    <select id="findBySchemeCodes"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT c.*,d.scheme_name,e.act_desc FROM tpm_marketing_plan_case c
        INNER JOIN tpm_marketing_plan d ON c.scheme_code = d.scheme_code
        LEFT JOIN tpm_marketing_plan_case_extend e ON e.scheme_detail_code = c.scheme_detail_code
        where c.scheme_code in
        <foreach collection="schemeCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND c.del_flag = '${@<EMAIL>()}'
        AND d.del_flag = '${@<EMAIL>()}'
    </select>
    <select id="findExpensesCountByConditions"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT t.customer_code,
        t.customer_name,
        t.category_code,
        t.category_name,
        SUM(t.apply_amount) applyAmount,
        SUM(t.cash_amount) cashAmount
        FROM tpm_marketing_plan_case t
        INNER JOIN tpm_marketing_plan d ON t.scheme_code = d.scheme_code
        WHERE 1 = 1
        AND t.del_flag = '${@<EMAIL>()}'
        AND d.process_status = '3'
        AND t.years &gt;= #{vo.startDate}
        AND t.years &lt;= #{vo.endDate}
        <if test="vo.customerName != null and vo.customerName != ''">
            <bind name="customerNameLike" value="'%' + vo.customerName + '%'"/>
            and t.customer_name like #{customerNameLike}
        </if>
        <if test="vo.categoryName != null and vo.categoryName != ''">
            <bind name="categoryNameLike" value="'%' + vo.categoryName + '%'"/>
            and t.category_name like #{categoryNameLike}
        </if>
        GROUP BY
        t.customer_code,
        t.customer_name,
        t.category_code,
        t.category_name
        ORDER BY
        t.customer_code DESC,
        t.category_code DESC
    </select>
    <select id="findExpenseChangeTerminal" resultType="java.lang.String">
        SELECT DISTINCT t.terminal_code
        FROM tpm_marketing_plan_case t
                 INNER JOIN tpm_marketing_plan d ON t.scheme_code = d.scheme_code
        WHERE t.terminal_code IS NOT NULL
          AND t.terminal_code != ''
        AND d.process_status != '6'
        AND t.modify_time &gt;= #{startDate}
          AND t.modify_time &lt;= #{endDate}
    </select>


    <select id="findSafActPolicy"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">

        SELECT
        t.*
        FROM
        (
        SELECT
        a.scheme_name
        ,b.*
        ,d.act_desc
        ,d.execute_desc
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        LEFT JOIN tpm_cost_type_detail c ON b.detail_code = c.detail_code
        LEFT JOIN tpm_marketing_plan_case_extend d ON b.scheme_detail_code = d.scheme_detail_code
        WHERE
        a.process_status = '3'
        AND a.scheme_type != 'change'
        AND c.is_send_sfa = 'Y'
        AND ((c.execution_type = 'terminal' and b.case_type = 'BDMB0003') or (c.execution_type = 'customer'))
        <if test="vo.schemeName != null and vo.schemeName != ''">
            <bind name="schemeNameLike" value="'%' + vo.schemeName + '%'"/>
            and a.scheme_name like #{schemeNameLike}
        </if>
        <if test="vo.schemeCode != null and vo.schemeCode != ''">
            <bind name="schemeCodeLike" value="'%' + vo.schemeCode + '%'"/>
            and a.scheme_code like #{schemeCodeLike}
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            <bind name="customerNameLike" value="'%' + vo.customerName + '%'"/>
            and b.customer_name like #{customerNameLike}
        </if>
        <if test="vo.customerCode != null and vo.customerCode != ''">
            <bind name="customerCodeLike" value="'%' + vo.customerCode + '%'"/>
            and b.customer_code like #{customerCodeLike}
        </if>
        <if test="vo.terminalName != null and vo.terminalName != ''">
            <bind name="terminalNameLike" value="'%' + vo.terminalName + '%'"/>
            and b.terminal_name like #{terminalNameLike}
        </if>
        <if test="vo.terminalCode != null and vo.terminalCode != ''">
            <bind name="terminalCodeLike" value="'%' + vo.terminalCode + '%'"/>
            and b.terminal_code like #{terminalCodeLike}
        </if>
        <if test="vo.startDate != null and vo.startDate != ''">
            and (b.start_date &gt;= #{vo.startDate} and b.start_date &lt;= #{vo.endDate})
        </if>
        <if test="vo.endDate != null and vo.endDate != ''">
            and (b.end_date &gt;= #{vo.startDate} and b.end_date &lt;= #{vo.endDate})
        </if>
        ) t
        ORDER BY t.create_time DESC,t.id DESC
    </select>


    <select id="findCategoryCodeBySecondCategory" resultType="java.lang.String">
        SELECT category_code
        FROM tpm_cost_type_category
        WHERE budget_subjects_code in (SELECT b.budget_subjects_code
                                       FROM tpm_budget_subjects a
                                                LEFT JOIN tpm_budget_subjects b
                                                          ON a.budget_subjects_code = b.parent_budget_subjects_code
                                       WHERE a.budget_subjects_code = #{secondCategoryCode}
                                         AND a.tenant_code = #{tenantCode}
                                         AND a.del_flag = '009')
          AND tenant_code = #{tenantCode}
          AND del_flag = '009'
    </select>

    <select id="findCategoryCodeBySecondCategoryList" resultType="java.lang.String">
        SELECT category_code
        FROM tpm_cost_type_category
        WHERE budget_subjects_code in (SELECT b.budget_subjects_code
        FROM tpm_budget_subjects a
        LEFT JOIN tpm_budget_subjects b
        ON a.budget_subjects_code = b.parent_budget_subjects_code
        WHERE a.budget_subjects_code
        <foreach collection="categoryCodeList" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        AND a.tenant_code = #{tenantCode}
        AND a.del_flag = '009')
        AND tenant_code = #{tenantCode}
        AND del_flag = '009'
    </select>

    <select id="findPlanCaseListBySecondCategory"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select a.*,b.* from tpm_marketing_plan p
        left join tpm_marketing_plan_case a on p.scheme_code = a.scheme_code
        left join tpm_marketing_plan_case_extend b on a.scheme_detail_code = b.scheme_detail_code
        <where>
            1=1
            and p.del_flag = '009'
            <if test="vo.processStatus != null and vo.processStatus != ''">
                and p.process_status = #{vo.processStatus}
            </if>
            <if test="vo.categoryCodeList != null and vo.categoryCodeList.size()>0">
                and a.category_code in
                <foreach collection="vo.categoryCodeList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerCode != null and vo.customerCode != ''">
                and a.customer_code = #{vo.customerCode}
            </if>
            <if test="vo.orgCode != null and vo.orgCode != ''">
                and a.belong_department_code = #{vo.orgCode}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and a.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.actName != null and vo.actName != ''">
                <bind name="actName" value="'%' + vo.actName + '%'"/>
                and a.act_name like #{actName}
            </if>
            <if test="vo.terminalName != null and vo.terminalName != ''">
                <bind name="terminalName" value="'%' + vo.terminalName + '%'"/>
                and a.terminal_name like #{terminalName}
            </if>
            <if test="vo.terminalName != null and vo.terminalName != ''">
                <bind name="terminalName" value="'%' + vo.terminalName + '%'"/>
                and a.terminal_name like #{terminalName}
            </if>
            <if test="vo.years != null and vo.years != ''">
                <bind name="years" value="'%' + vo.years + '%'"/>
                and a.years like #{years}
            </if>
            <if test="vo.yearsList != null and vo.yearsList.size()>0 ">
                and a.years in
                <foreach collection="vo.yearsList" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.actStatus != null and vo.actStatus != ''">
                <choose>
                    <when test="'0'.toString() == vo.actStatus">
                        and a.start_date >= now()
                    </when>
                    <when test="'1'.toString() == vo.actStatus">
                        <![CDATA[and a.start_date <= now() and a.end_date >= now()]]>
                    </when>
                    <when test="'2'.toString() == vo.actStatus">
                        <![CDATA[and a.end_date <= now()]]>
                    </when>
                </choose>
            </if>
            <if test="vo.dmsReadFlag != null and vo.dmsReadFlag != ''">
                <choose>
                       <when test="'Y'.toString() == vo.dmsReadFlag">
                            and a.dms_read_flag = #{vo.dmsReadFlag}
                       </when>
                       <when test="'N'.toString() == vo.dmsReadFlag">
                           and (a.dms_read_flag = #{vo.dmsReadFlag} or a.dms_read_flag is null )
                       </when>
                </choose>
            </if>
            order by a.create_time desc,a.scheme_detail_code desc
        </where>
    </select>


    <select id="findPlanCaseListBySecondCategoryList" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select a.* from tpm_marketing_plan p
        left join tpm_marketing_plan_case a on p.scheme_code = a.scheme_code
        left join tpm_marketing_plan_case_extend b on a.scheme_detail_code = b.scheme_detail_code
        <where>
            1=1
            and p.del_flag = '009'
            <if test="vo.processStatus != null and vo.processStatus != ''">
                and p.process_status = #{vo.processStatus}
            </if>
            <if test="vo.categoryCodeList != null and vo.categoryCodeList.size()>0">
                and a.category_code in
                <foreach collection="vo.categoryCodeList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerCode != null and vo.customerCode != ''">
                and a.customer_code = #{vo.customerCode}
            </if>
            <if test="vo.orgCode != null and vo.orgCode != ''">
                and a.belong_department_code = #{vo.orgCode}
            </if>
            <if test="vo.schemeDetailCode != null and vo.schemeDetailCode != ''">
                <bind name="schemeDetailCode" value="'%' + vo.schemeDetailCode + '%'"/>
                and a.scheme_detail_code like #{schemeDetailCode}
            </if>
            <if test="vo.actName != null and vo.actName != ''">
                <bind name="actName" value="'%' + vo.actName + '%'"/>
                and a.act_name like #{actName}
            </if>
            <if test="vo.terminalName != null and vo.terminalName != ''">
                <bind name="terminalName" value="'%' + vo.terminalName + '%'"/>
                and a.terminal_name like #{terminalName}
            </if>
            <if test="vo.terminalName != null and vo.terminalName != ''">
                <bind name="terminalName" value="'%' + vo.terminalName + '%'"/>
                and a.terminal_name like #{terminalName}
            </if>
            <if test="vo.years != null and vo.years != ''">
                <bind name="years" value="'%' + vo.years + '%'"/>
                and a.years like #{years}
            </if>
            <if test="vo.yearsList != null and vo.yearsList.size()>0 ">
                and a.years in
                <foreach collection="vo.yearsList" separator="," item="item" index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.actStatus != null and vo.actStatus != ''">
                <choose>
                    <when test="'0'.toString() == vo.actStatus">
                        and a.start_date >= now()
                    </when>
                    <when test="'1'.toString() == vo.actStatus">
                        <![CDATA[and a.start_date <= now() and a.end_date >= now()]]>
                    </when>
                    <when test="'2'.toString() == vo.actStatus">
                        <![CDATA[and a.end_date <= now()]]>
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="findCountTerminalByTerminalCodesAndYears" resultType="java.lang.String">
        SELECT
        a.terminal_code
        FROM
        tpm_marketing_plan_case a
        LEFT JOIN tpm_marketing_plan b ON a.scheme_code = b.scheme_code
        WHERE
        b.process_status = '3'
        <if test="years != null and years != ''">
            and a.years = #{years}
        </if>
        <if test="terminalCodeList != null and terminalCodeList.size()>0">
            and a.terminal_code in
            <foreach collection="terminalCodeList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
        a.terminal_code
    </select>

    <select id="findAmountByOrgCodesAndYearsList"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select a.*,b.* from tpm_marketing_plan p
        left join tpm_marketing_plan_case a on p.scheme_code = a.scheme_code
        left join tpm_marketing_plan_case_extend b on a.scheme_detail_code = b.scheme_detail_code
        <where>
            1=1
            and p.del_flag = '009'
            and p.process_status = '3'
            and p.scheme_type != 'change'
            <if test="orgCodes != null and orgCodes.size()>0">
                and a.belong_department_code in
                <foreach collection="orgCodes" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="yearsList != null and yearsList.size()>0">
                and a.years in
                <foreach collection="yearsList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findCaseListByDmsMiniHomePage" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select
        a.id,
        a.customer_code,
        a.customer_name,
        a.act_name,
        a.case_type,
        a.scheme_code,
        a.scheme_detail_code,
        a.create_time
        from tpm_marketing_plan p
        left join tpm_marketing_plan_case a on p.scheme_code = a.scheme_code
        left join tpm_marketing_plan_case_extend b on a.scheme_detail_code = b.scheme_detail_code
        <where>
            1=1
            and p.del_flag = '009'
            and p.process_status = '3'
            and p.scheme_type != 'change'
            and (a.dms_read_flag != 'Y' or a.dms_read_flag is null)
            <if test="customerCode != null and customerCode != ''">
                and a.customer_code = #{customerCode}
            </if>
            <if test="yearsList != null and yearsList.size()>0">
                and a.years in
                <foreach collection="yearsList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            order by a.create_time desc
        </where>
    </select>

    <select id="findCaseListByYearsAndDelFlagProcess" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select
        a.*,b.*
        from tpm_marketing_plan p
        left join tpm_marketing_plan_case a on p.scheme_code = a.scheme_code
        left join tpm_marketing_plan_case_extend b on a.scheme_detail_code = b.scheme_detail_code
        <where>
            1=1
            and p.del_flag = '009'
            and a.del_flag = '009'
            and p.process_status = '3'
            and p.scheme_type != 'change'
            and a.act_years = #{years}
        </where>
    </select>
    <select id="sumApplyAmountByCustomerCodesYearMonth" resultType="com.biz.crm.tpm.business.activities.marketingplan.dto.CustomerMonthCategoryApplyAmountDTO">
        select
            pc.customer_code,
            pc.category_code,
            plan.years,
            sum(pc.apply_amount) as totalApplyAmount
        from tpm_marketing_plan_case pc
                 LEFT JOIN tpm_marketing_plan plan on pc.scheme_code = plan.scheme_code
        where pc.customer_code in
          <foreach collection="customerCodes" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
          and plan.years = #{years}
          and plan.process_status = 3
          and plan.scheme_type = 'o_two_o'
        GROUP BY pc.customer_code, pc.category_code
    </select>

    <select id="findContractByYearsAndLogin" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select
        a.*,b.*,p.scheme_name
        from tpm_marketing_plan p
        left join tpm_marketing_plan_case a on p.scheme_code = a.scheme_code
        left join tpm_marketing_plan_case_extend b on a.scheme_detail_code = b.scheme_detail_code
        <where>
            1=1
            and p.del_flag = '009'
            and a.del_flag = '009'
            and p.scheme_type = 'contract'
            and a.act_years = #{vo.years}
            and a.customer_code in
            <foreach collection="vo.customerCodeList" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="findGiftByYearOrg"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        select
        a.*
        from tpm_marketing_plan_case a
        where
        a.del_flag = '009'
        and a.years = #{years}
        and a.belong_department_code in
        <foreach collection="orgCodeList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and a.detail_code in
        <foreach collection="itemCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

    </select>
    <select id="queryCurMonthApprovedMarketingPlanCases"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT tpc.*, tp.scheme_name, tp.scheme_type, tpce.condition_num, tpce.give_num, tpce.condition_formula_name
        FROM tpm_marketing_plan_case tpc
        left join tpm_marketing_plan_case_extend tpce on tpc.scheme_detail_code = tpce.scheme_detail_code
        LEFT JOIN tpm_marketing_plan tp ON tpc.scheme_code = tp.scheme_code
        WHERE  tpc.del_flag = '009'
        AND tp.process_status = '3'
        AND tp.changed_flag = 'N'
        AND tpc.case_type in ('BDMB0005', 'BDMB0006')
        AND tpc.start_date >= #{startDate}
        AND tpc.end_date <![CDATA[ <= ]]> #{endDate}
    </select>
    <select id="findByReleaseDetailCodes"
            resultType="com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase">
        select b.*
        from tpm_marketing_plan_case b
        left join tpm_marketing_plan a on a.scheme_code = b.scheme_code
        WHERE
        a.process_status = '3'
        AND a.scheme_type != 'change'
        and b.release_detail_code in
        <foreach collection="releaseDetailCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="findRegionMarketingPlanCases" resultType="com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo">
        SELECT b.*, a.scheme_name,d.cash_amount cashAmountF,ho.with_holding_amount
        FROM tpm_marketing_plan a
                 LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
                 left join tpm_marketing_plan_case_extend c
                           on b.scheme_detail_code = c.scheme_detail_code
                 LEFT JOIN (
            SELECT
                b.scheme_detail_code,
                sum(
                        IFNULL( b.this_cash_amount, 0 )) cash_amount
            FROM
                tpm_fee_cash a
                    LEFT JOIN tpm_fee_cash_detail b ON a.cash_code = b.cash_code
            WHERE
                a.STATUS = '3'
              AND a.del_flag = '009'
            GROUP BY
                b.scheme_detail_code
        ) d ON b.scheme_detail_code = d.scheme_detail_code
                 left join tpm_with_holding ho on ho.activities_detail_code=b.scheme_detail_code and ho.del_flag='${@<EMAIL>()}'
        WHERE a.del_flag = '${@<EMAIL>()}'
          AND a.tenant_code = #{tenantCode}
        and release_detail_code in
        <foreach collection="releaseDetailCodes" open="(" close=")" separator="," item="item" index="index">   #{item}</foreach>
          and a.scheme_type != 'change'
    </select>

</mapper>

