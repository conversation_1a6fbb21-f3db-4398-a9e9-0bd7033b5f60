package com.biz.crm.tpm.business.activities.overallplan.eunm;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OverallPlanSchemeTypeEnum {

    HEAD("head", "总部"),
    REGION("region", "大区"),
    MARKETING("marketing", "营销规划方案"),
    PLAN_CASE("plan_case", "方案明细"),
    SALES_PLAN("sales_plan", "销售计划"),
    ;

    private String code;

    private String desc;
}
