package com.biz.crm.tpm.business.activities.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/3 14:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_external_contract_file")
@Table(
        name = "tpm_external_contract_file",
        indexes = {
                @Index(name = "tpm_external_contract_file_index0", columnList = "contract_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_external_contract_file", comment = "外部合同附件地址")
@ApiModel(value = "ExternalContractFile", description = "外部合同附件地址")
public class ExternalContractFile extends UuidFlagOpEntity {

    @ApiModelProperty("合同编码")
    @Column(name = "contract_code", columnDefinition = "varchar(32) comment '合同编码'")
    private String contractCode;

    @ApiModelProperty("唯一编码")
    @Column(name = "only_key", columnDefinition = "varchar(64) comment '唯一编码'")
    private String onlyKey;

    @ApiModelProperty("附件地址")
    @Column(name = "url", columnDefinition = "varchar(256) comment '附件地址'")
    private String url;

    @ApiModelProperty("文件名")
    @Column(name = "file_name",columnDefinition = "varchar(128) comment '文件名'")
    private String fileName;
}
