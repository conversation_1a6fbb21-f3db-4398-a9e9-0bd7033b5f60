package com.biz.crm.tpm.business.activities.job.contract;

import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.activities.contract.entity.ExternalContract;
import com.biz.crm.tpm.business.activities.contract.service.ExternalContractService;
import com.biz.crm.tpm.business.activities.contract.vo.ContractCostDetailVo;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractDetailVo;
import com.biz.crm.tpm.business.activities.contract.vo.ExternalContractVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.task.annotations.DynamicTaskService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/12 15:32
 */
@Service
@Slf4j
public class ContractCostJob {


    @Autowired
    private ExternalContractService service;



    /**
     * 生成下个月的合同营销方案
     * 只查询不是外部合同 并且未执行状态的
     */
    public void generateContractMarketing() {
        LocalDate nextMonth = LocalDate.now().plusMonths(1l);
        String years = nextMonth.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_YEAR_MONTH));
        service.generateContractMarketing(years);
    }

}
