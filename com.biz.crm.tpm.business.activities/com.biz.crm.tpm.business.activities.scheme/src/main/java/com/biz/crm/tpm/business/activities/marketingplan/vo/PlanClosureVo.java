package com.biz.crm.tpm.business.activities.marketingplan.vo;

import com.biz.crm.business.common.sdk.vo.WorkflowFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 16:02
 */
@Data
@ApiModel(value = "PlanClosure", description = "方案规划关闭")
public class PlanClosureVo extends WorkflowFlagOpVo {

    @ApiModelProperty("关闭编码")
    private String closeCode;

    @ApiModelProperty("关闭名称")
    private String closeName;

    @ApiModelProperty("关闭原因")
    private String closeReason;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("租户")
    private String tenantCode;

    @ApiModelProperty("缓存key")
    private String cacheKey;

    @ApiModelProperty("来源类型")
    private String sourceType;

    @ApiModelProperty("审批单号")
    private String processNumber;

    @ApiModelProperty("推送日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("OA人员id")
    private String oaId;

    @ApiModelProperty("OA人员账号")
    private String oaUserName;

    @ApiModelProperty("明细列表")
    private List<PlanClosureDetailVo> detailList;
}
