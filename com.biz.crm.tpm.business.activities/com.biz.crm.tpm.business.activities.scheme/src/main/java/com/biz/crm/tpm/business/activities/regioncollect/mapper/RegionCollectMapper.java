package com.biz.crm.tpm.business.activities.regioncollect.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.regioncollect.entity.*;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
public interface RegionCollectMapper extends BaseMapper<RegionCollect> {

    Page<RegionCollectVo> findList(Page<RegionCollectVo> page, @Param("vo") RegionCollectVo vo);


    Page<RegionCollectDepartmentEstimation> findDepartmentReport(Page<RegionCollectDepartmentEstimation> page, @Param("vo") RegionCollectDepartmentEstimation vo);


    Page<RegionCollectGainsAndLosses> findGainsAndLossesReport(Page<RegionCollectGainsAndLosses> page, @Param("vo") RegionCollectGainsAndLosses vo);


    Page<RegionCollectItemEstimation> findItemReport(Page<RegionCollectItemEstimation> page, @Param("vo") RegionCollectItemEstimation vo);

    Page<RegionCollectScheme> findSchemeReport(Page<RegionCollectScheme> page, @Param("vo") RegionCollectScheme vo);

    Page<MarketingPlanCaseVo> findRegionMarketingCaseReport(Page<MarketingPlanCaseVo> page, @Param("vo") MarketingPlanCaseVo vo,@Param("collectCode")String collectCode);

    List<MarketingPlanCaseVo> findRegionMarketingCaseList(@Param("collectCode") String collectCode);

    Map<String, BigDecimal> findDepartmentReportTotal(@Param("vo") RegionCollectDepartmentEstimation vo);

    Map<String, BigDecimal> findGainsAndLossesReportTotal(@Param("vo") RegionCollectGainsAndLosses vo);

    Map<String, BigDecimal> findItemReportTotal(@Param("vo") RegionCollectItemEstimation vo);

    List<RegionCollectVo> findCommit(@Param("yearMonthLy") String yearMonthLy);

    Page<MarketingSalesPlanVo> findRegionMarketingSalesPlanList(@Param("page") Page<MarketingSalesPlanVo> page, @Param("collectCode") String collectCode);
}
