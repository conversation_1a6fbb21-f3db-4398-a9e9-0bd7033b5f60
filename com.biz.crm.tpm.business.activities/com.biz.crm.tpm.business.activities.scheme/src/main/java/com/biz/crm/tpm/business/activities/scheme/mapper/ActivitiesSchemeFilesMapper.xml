<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.activities.scheme.mapper.ActivitiesSchemeFilesMapper">
  <resultMap type="com.biz.crm.tpm.business.activities.scheme.entity.ActivitiesSchemeFiles" id="ActivitiesSchemeFilesMap">
  </resultMap>

  <select id="findByConditions" resultType="com.biz.crm.tpm.business.activities.scheme.vo.ActivitiesSchemeFilesVo">
    select
    t.*
    from tpm_activities_scheme_files t
    <where>
      and t.del_flag = '${@<EMAIL>()}'
    </where>
    order by t.create_time desc,t.id
  </select>
</mapper>