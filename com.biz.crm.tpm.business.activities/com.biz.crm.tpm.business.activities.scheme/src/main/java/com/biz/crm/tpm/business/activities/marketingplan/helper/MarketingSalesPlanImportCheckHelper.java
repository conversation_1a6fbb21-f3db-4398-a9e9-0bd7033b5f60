package com.biz.crm.tpm.business.activities.marketingplan.helper;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanImportVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 16:09
 */
@Component
@Slf4j
public class MarketingSalesPlanImportCheckHelper implements ImportProcess<MarketingSalesPlanImportVo> {


    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Resource
    private MarketingSalesPlanPageCacheHelper salesPlanPageCacheHelper;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private ProductVoService productVoService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    private static final String TPM_TRANSPORT_TYPE = "tpm_transport_type";

    /**
     * 参数校验
     *
     * @param importVoList
     * @param loginUserCodes
     * @param userName
     * @return
     */
    public void checkData(List<MarketingSalesPlanImportVo> importVoList, String cacheKey, String years, String schemeCode, String originalSchemeCode, List<String> loginUserCodes, String userName) {
        Map<String, String> map = getTransportType();
        List<MarketingSalesPlanVo> dataList = importVoList.stream().map(x -> {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(x));
            if (jsonObject.containsKey("estimatedSalesVolume")) {
                try {
                    BigDecimal estimatedSalesVolume = new BigDecimal(jsonObject.getString("estimatedSalesVolume"));
                    jsonObject.put("estimatedSalesVolume", estimatedSalesVolume);
                } catch (Exception e) {
                    log.error("转换错误{}", jsonObject.get("estimatedSalesVolume"));
                    jsonObject.remove("estimatedSalesVolume");
                }
            }
            if (jsonObject.containsKey("estimatedCost")) {
                try {
                    BigDecimal estimatedCost = new BigDecimal(jsonObject.getString("estimatedCost"));
                    jsonObject.put("estimatedCost", estimatedCost);
                } catch (Exception e) {
                    log.error("转换错误{}", jsonObject.get("estimatedCost"));
                    jsonObject.remove("estimatedCost");
                }
            }
            MarketingSalesPlanVo vo = JSONObject.toJavaObject(jsonObject, MarketingSalesPlanVo.class);
            if (map.containsKey(x.getTransportType())) {
                vo.setTransportType(map.get(x.getTransportType()));
            }
            return vo;
        }).collect(Collectors.toList());
        List<String> erpCodeList = dataList.stream().map(MarketingSalesPlanVo::getErpCode).distinct().collect(Collectors.toList());
        List<CustomerVo> customerList = customerVoService.findByErpCodes(erpCodeList);
        Map<String, String> customerMap = customerList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()) && ObjectUtils.isNotEmpty(x.getErpCode()) && ObjectUtils.isNotEmpty(x.getCompanyCode()))
                .collect(Collectors.toMap(x -> x.getErpCode() + x.getCompanyCode(), x -> x.getCustomerCode()));
        for (MarketingSalesPlanVo planVo : dataList) {
            String key = planVo.getErpCode() + planVo.getCompanyCode();
            if (customerMap.containsKey(key)) {
                planVo.setCustomerCode(customerMap.get(key));
            } else {
                planVo.setCustomerCode(key);
            }
        }
        salesPlanPageCacheHelper.importNewItem(cacheKey, years, dataList, schemeCode, originalSchemeCode, loginUserCodes, userName);
    }

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, MarketingSalesPlanImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    @Override
    public Class<MarketingSalesPlanImportVo> findCrmExcelVoClass() {
        return MarketingSalesPlanImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "TPM_MARKETING_SALES_PLAN_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "营销规划方案销售计划导入";
    }

    /**
     * 运输方式
     *
     * @return
     */
    public Map<String, String> getTransportType() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(TPM_TRANSPORT_TYPE);
        if (CollectionUtils.isNotEmpty(dictDataVos)) {
            return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
        }
        return Maps.newHashMap();
    }
}
