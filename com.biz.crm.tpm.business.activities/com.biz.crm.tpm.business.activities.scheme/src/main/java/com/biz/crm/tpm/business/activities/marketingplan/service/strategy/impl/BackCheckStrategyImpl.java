package com.biz.crm.tpm.business.activities.marketingplan.service.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import com.biz.crm.business.common.base.util.BusinessRequiredUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanCaseTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingSalesPlanTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.helper.MarketingPlanCaseCheckHelper;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingSalesPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.service.strategy.MarketingPlanCaseStrategy;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanProductVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingSalesPlanVo;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanProductEnum;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesTemplateConfigDetailVo;
import com.biz.crm.tpm.business.rebate.sdk.enums.FormulaTypeEnum;
import com.biz.crm.tpm.business.rebate.sdk.service.FormulaCalService;
import com.biz.crm.tpm.business.rebate.sdk.service.FormulaConfigVoService;
import com.biz.crm.tpm.business.rebate.sdk.vo.FormulaCalBaseVo;
import com.biz.crm.tpm.business.rebate.sdk.vo.element.FormulaConfigVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 后返模板
 * <AUTHOR>
 * @Date 2024/6/20 20:08
 */
@Component
@Slf4j
public class BackCheckStrategyImpl implements MarketingPlanCaseStrategy<MarketingPlanCaseVo> {

    @Resource
    private CustomerVoService customerVoService;

    @Resource
    private MarketingPlanCaseCheckHelper checkHelper;

    @Resource
    private FormulaConfigVoService formulaConfigVoService;

    @Resource
    private FormulaCalService formulaCalService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private MarketingSalesPlanService marketingSalesPlanService;


    @Override
    public void checkCaseList(List<MarketingPlanCaseVo> list, String years, String schemeCode, String originalSchemeCode, String cacheKey) {
        //获取配置模板信息
        List<ActivitiesTemplateConfigDetailVo> templateFiledList = checkHelper.getTemplateFiled(MarketingPlanCaseTypeEnum.back.getCode());
        Map<String, Boolean> filedMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getRequired));
        Map<String, String> chineseMap = templateFiledList.stream().collect(Collectors.toMap(ActivitiesTemplateConfigDetailVo::getField, ActivitiesTemplateConfigDetailVo::getTitle));
        //获取活动细类配置
        Set<String> detailCodeSet = checkHelper.getTemplateRelateDetailCodes(MarketingPlanCaseTypeEnum.back.getCode());

        List<String> originalSchemeDetailCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOriginalSchemeDetailCode()))
                .map(x -> x.getOriginalSchemeDetailCode()).collect(Collectors.toList());
        Map<String, MarketingPlanCaseVo> originCaseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(originalSchemeDetailCodes)) {
            List<MarketingPlanCaseVo> marketingPlanCaseVos = checkHelper.findCaseListBySchemeDetailCodes(originalSchemeDetailCodes);
            originCaseMap = marketingPlanCaseVos.stream().collect(Collectors.toMap(x -> x.getSchemeDetailCode(), Function.identity()));
        }

        //标记分组
        Map<String, Long> muchDepartmentMap = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getMuchDepartmentMark()))
                .collect(Collectors.groupingBy(MarketingPlanCaseVo::getMuchDepartmentMark, Collectors.counting()));
        if (ObjectUtils.isNotEmpty(muchDepartmentMap)) {
            muchDepartmentMap = muchDepartmentMap.entrySet().stream()
                    .filter(entry -> entry.getValue() == 1)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        //查询组织的成本中心
        Map<String, Set<String>> orgCostCenterMap = checkHelper.findOrgCostCenter(list);
        Set<String> costCenterCodeSet = orgCostCenterMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        Set<String> yearSet = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getYears()))
                .map(MarketingPlanCaseVo::getYears).collect(Collectors.toSet());
        List<String> customerCodes = list.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCustomerCode()))
                .map(MarketingPlanCaseVo::getCustomerCode).collect(Collectors.toList());

        List<String> itemCodes = list.stream().filter(x -> CollectionUtils.isNotEmpty(x.getFeeBelongItemList())).map(k -> k.getFeeBelongItemList().get(0).getCode()).distinct().collect(Collectors.toList());

        List<String> excludeSchemeCode = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(schemeCode)) {
            excludeSchemeCode.add(schemeCode);
        }
        if (ObjectUtils.isNotEmpty(originalSchemeCode)) {
            excludeSchemeCode.add(originalSchemeCode);
        }

        List<MarketingSalesPlanVo> salesPlanVos = Lists.newArrayList();
        String salesCacheKey = cacheKey + ":" + MarketingSalesPlanTypeEnum.sales_plan.getCode() + ":" + years;
        List<MarketingSalesPlanVo> salesPlanList1 = marketingSalesPlanService.findCacheList(salesCacheKey);
        List<MarketingSalesPlanVo> salesPlanList2 = marketingSalesPlanService.findListByConditionToMarketingScheme(yearSet, costCenterCodeSet, Sets.newHashSet(customerCodes),
                null, null, excludeSchemeCode, null, null);
        if (CollectionUtils.isNotEmpty(salesPlanList1)) {
            salesPlanVos.addAll(salesPlanList1);
        }
        if (CollectionUtils.isNotEmpty(salesPlanList2)) {
            salesPlanVos.addAll(salesPlanList2);
        }

        Map<String, List<MarketingSalesPlanVo>> salesPlanMap = salesPlanVos.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getCostCenterCode()))
                .collect(Collectors.groupingBy(MarketingSalesPlanVo::getCostCenterCode));

        //查询客户
        List<CustomerVo> customerVoList = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, CustomerVo> customerVoMap = customerVoList.stream().filter(x -> EnableStatusEnum.ENABLE.getCode().equals(x.getEnableStatus()) &&
                        BooleanEnum.TRUE.getCapital().equals(x.getContractCustomer()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
        Set<String> companyCodeSet = Sets.newHashSet();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(customerVoList)) {
            companyCodeSet = customerVoList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getCompanyCode()))
                    .map(CustomerVo::getCompanyCode).collect(Collectors.toSet());
        }
        //查询商品
        List<String> productCodes = Lists.newArrayList();
        List<String> items = Lists.newArrayList();
        for (MarketingPlanCaseVo vo : list) {
            if (CollectionUtils.isNotEmpty(vo.getFeeProductList())) {
                productCodes.addAll(vo.getFeeProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));
                List<MarketingPlanProductVo> productList = vo.getFeeProductList().stream().map(x -> {
                    MarketingPlanProductVo productVo = nebulaToolkitService.copyObjectByWhiteList(x, MarketingPlanProductVo.class, HashSet.class, ArrayList.class);
                    productVo.setType(OverallPlanProductEnum.cal_product.getCode());
                    return productVo;
                }).collect(Collectors.toList());
                vo.setProductList(productList);
            }
//            if (CollectionUtils.isNotEmpty(vo.getProductList())) {
//                productCodes.addAll(vo.getProductList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
//                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));
//            }

            if (CollectionUtils.isNotEmpty(vo.getFeeItemList())) {
                items.addAll(vo.getFeeItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));

                List<MarketingPlanProductVo> itemList = vo.getFeeItemList().stream().map(x -> {
                    MarketingPlanProductVo productVo = nebulaToolkitService.copyObjectByWhiteList(x, MarketingPlanProductVo.class, HashSet.class, ArrayList.class);
                    productVo.setType(OverallPlanProductEnum.cal_item.getCode());
                    return productVo;
                }).collect(Collectors.toList());
                vo.setItemList(itemList);
            } else {
                vo.setItemList(null);
            }
//            if (CollectionUtils.isNotEmpty(vo.getItemList())) {
//                items.addAll(vo.getItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
//                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));
//            }
            if (CollectionUtils.isNotEmpty(vo.getFeeBelongItemList())) {
                items.addAll(vo.getFeeBelongItemList().stream().filter(x -> ObjectUtils.isNotEmpty(x.getCode()))
                        .map(MarketingPlanProductVo::getCode).collect(Collectors.toList()));
            }
        }
        //品项信息
        Map<String, String> itemMap = checkHelper.findSapLevelMap(items);
        //商品信息-通过品项查询
        Map<String, ProductPhaseVo> productPhaseMap = checkHelper.findProductPhaseMap(itemCodes);
        Map<String, ProductVo> productMap = checkHelper.findProductListByCodes(productCodes, companyCodeSet, null);
        Map<String, MarketingPlanCaseVo> replaceMap = Maps.newHashMap();
        for (MarketingPlanCaseVo caseVo : list) {
            caseVo.setCheckFlag(Boolean.TRUE);
            StringJoiner errMsg = new StringJoiner(";");
            //判断是变更的
            if (ObjectUtils.isNotEmpty(caseVo.getOriginalSchemeDetailCode())) {
                MarketingPlanCaseVo originCaseVo = originCaseMap.get(caseVo.getOriginalSchemeDetailCode());
                originalSchemeCode = originCaseVo.getSchemeCode();
                originCaseVo.setId(caseVo.getId());
                originCaseVo.setSchemeCode(caseVo.getSchemeCode());
                originCaseVo.setSchemeDetailCode(caseVo.getSchemeDetailCode());
                originCaseVo.setOriginalSchemeDetailCode(caseVo.getOriginalSchemeDetailCode());
                originCaseVo.setOriginalSchemeCode(caseVo.getOriginalSchemeCode());

                if (!caseVo.getStartDate().contains(originCaseVo.getYears())) {
                    errMsg.add("开始时间不能跨月份");
                }
                if (!caseVo.getEndDate().contains(originCaseVo.getYears())) {
                    errMsg.add("结束时间不能跨月份");
                }
                originCaseVo.setStartDate(caseVo.getStartDate());
                originCaseVo.setEndDate(caseVo.getEndDate());
                //返利周期
                originCaseVo.setRebateType(caseVo.getRebateType());
                originCaseVo.setRebateCalDay(caseVo.getRebateCalDay());
                originCaseVo.setRebateStartDate(caseVo.getRebateStartDate());
                originCaseVo.setRebateEndDate(caseVo.getRebateEndDate());
                //费用使用部门
                if (checkHelper.paramNotNull(caseVo.getBelongDepartmentCode(), "费用使用部门", errMsg)) {
                    originCaseVo.setBelongDepartmentCode(caseVo.getBelongDepartmentCode());
                    originCaseVo.setBelongDepartmentName(caseVo.getBelongDepartmentName());
                }
                //政策形式
                if (checkHelper.paramNotNull(caseVo.getConditionFormula(), "政策形式", errMsg)) {
                    originCaseVo.setConditionFormula(caseVo.getConditionFormula());
                    originCaseVo.setConditionFormulaName(caseVo.getConditionFormulaName());
                }
                if (checkHelper.paramNotNull(caseVo.getConditionNum(), "达成条件", errMsg)) {
                    originCaseVo.setConditionNum(caseVo.getConditionNum());
                }
                if (checkHelper.paramNotNull(caseVo.getGiveNum(), "返利标准", errMsg)) {
                    originCaseVo.setGiveNum(caseVo.getGiveNum());
                }
                if (ObjectUtils.isNotEmpty(caseVo.getConditionNum()) && ObjectUtils.isNotEmpty(caseVo.getGiveNum())) {
                    List<String> giveNumList = Arrays.asList(caseVo.getGiveNum().split(","));
                    List<String> conditionNumList = Arrays.asList(caseVo.getConditionNum().split(","));
                    if (giveNumList.size() != conditionNumList.size()) {
                        errMsg.add("达成条件阶梯必须和返利标准阶梯保持一致");
                    }
                }
                originCaseVo.setCashType(caseVo.getCashType());
                originCaseVo.setIsContractCost(caseVo.getIsContractCost());
                originCaseVo.setContractCode(caseVo.getContractCode());
                //费用依据
                originCaseVo.setCostBasisList(caseVo.getCostBasisList());
                originCaseVo.setCostBasis(caseVo.getCostBasis());
                //返利描述
                originCaseVo.setActDesc(caseVo.getActDesc());
                if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                    originCaseVo.setCheckFlag(Boolean.FALSE);
                    originCaseVo.setErrMsg(errMsg.toString());
                } else {
                    originCaseVo.setCheckFlag(Boolean.TRUE);
                }
                if (originCaseVo.getCheckFlag()) {
                    //公式计算
                    this.calFormulaAmount(originCaseVo, orgCostCenterMap, salesPlanMap, years, cacheKey, schemeCode, originalSchemeCode, productPhaseMap);
                }
                replaceMap.put(caseVo.getOriginalSchemeDetailCode(), originCaseVo);
                continue;
            }

            if (ObjectUtils.isNotEmpty(caseVo.getErrMsg())) {
                errMsg.add(caseVo.getErrMsg());
            }
            if (ObjectUtils.isNotEmpty(caseVo.getDetailCode())) {
                if (!detailCodeSet.contains(caseVo.getDetailCode())) {
                    errMsg.add("费用项目不属于当前表单模板配置");
                }
            }
            if (checkHelper.paramNotNull(caseVo.getCustomerCode(), "客户编码", errMsg)) {
                if (customerVoMap.containsKey(caseVo.getCustomerCode())) {
                    CustomerVo customerVo = customerVoMap.get(caseVo.getCustomerCode());
                    caseVo.setCustomerName(customerVo.getCustomerName());
                    caseVo.setErpCode(customerVo.getErpCode());
                    caseVo.setCompanyCode(customerVo.getCompanyCode());
                    caseVo.setProductGroupCode(customerVo.getProductGroupCode());
                    caseVo.setChannelCode(customerVo.getChannelCode());
                    caseVo.setCooperateType(customerVo.getCooperateType());
                    caseVo.setHzlx(customerVo.getHzlx());

                    //校验客户公司代码与成本中心公司代码是否一致
                    if (ObjectUtils.isNotEmpty(caseVo.getCostCenterCompanyCode()) && !customerVo.getCompanyCode().equals(caseVo.getCostCenterCompanyCode())){
                        errMsg.add(String.format("客户公司代码与成本中心公司代码不一致,客户公司代码%s,成本中心公司代码%s",caseVo.getCompanyCode(),caseVo.getCostCenterCompanyCode()));
                    }
                } else {
                    errMsg.add(String.format("客户编码%s在主数据中未查询到或不是合同客户", caseVo.getCustomerCode()));
                }
            }
//            if (checkHelper.collectionNotNull("考核品项和考核产品", errMsg, caseVo.getItemList(), caseVo.getProductList())) {
//                if (CollectionUtils.isNotEmpty(caseVo.getItemList())) {
//                    for (MarketingPlanProductVo vo : caseVo.getItemList()) {
//                        if (itemMap.containsKey(vo.getCode())) {
//                            vo.setName(itemMap.get(vo.getCode()));
//                        } else {
//                            errMsg.add(String.format("考核品项%s在主数据未查询到", vo.getCode()));
//                        }
//                    }
//                }
//                if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
//                    for (MarketingPlanProductVo vo : caseVo.getProductList()) {
//                        if (productMap.containsKey(vo.getCode())) {
//                            if (productMap.containsKey(vo.getCode())) {
//                                ProductVo productVo = productMap.get(vo.getCode());
//                                vo.setName(productVo.getProductName());
//                                Validate.notNull(productVo.getMaterialCode(), String.format("商品%s未存在绑定的物料", productVo.getProductName()));
//                                vo.setMaterialCode(productVo.getMaterialCode());
//                            } else {
//                                errMsg.add(String.format("商品%s不属于品项关联的商品", vo.getCode()));
//                            }
//                        } else {
//                            errMsg.add(String.format("考核商品%s在主数据中未查询到", vo.getCode()));
//                        }
//                    }
//                }
//            }
            checkHelper.collectionNotNullAndOnlyOne("返利品项和返利产品", errMsg, caseVo.getFeeItemList(), caseVo.getFeeProductList());
            if (CollectionUtils.isNotEmpty(caseVo.getFeeItemList())) {
                for (MarketingPlanProductVo vo : caseVo.getFeeItemList()) {
                    if (itemMap.containsKey(vo.getCode())) {
                        vo.setName(itemMap.get(vo.getCode()));
                    } else {
                        errMsg.add(String.format("返利品项%s在主数据未查询到", vo.getCode()));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(caseVo.getFeeProductList())) {
                caseVo.setProductList(BeanUtil.copyToList(caseVo.getFeeProductList(), MarketingPlanProductVo.class));
                for (MarketingPlanProductVo vo : caseVo.getFeeProductList()) {
                    if (productMap.containsKey(vo.getCode())) {
                        ProductVo productVo = productMap.get(vo.getCode());
                        vo.setName(productVo.getProductName());
                        Validate.notNull(productVo.getMaterialCode(), String.format("商品%s未存在绑定的物料", productVo.getProductName()));
                        vo.setMaterialCode(productVo.getMaterialCode());
                    } else {
                        errMsg.add(String.format("返利商品%s在主数据中未查询到", vo.getCode()));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(caseVo.getFeeBelongItemList())) {
                for (MarketingPlanProductVo vo : caseVo.getFeeBelongItemList()) {
                    if (itemMap.containsKey(vo.getCode())) {
                        vo.setName(itemMap.get(vo.getCode()));
                    } else {
                        errMsg.add(String.format("费用归属品项%s在主数据未查询到", vo.getCode()));
                    }
                }
            }
            MarketingPlanCaseCheckHelper.matchRules(caseVo.getConditionNum(), "达成条件", errMsg);
            MarketingPlanCaseCheckHelper.matchRules(caseVo.getGiveNum(), "返利标准", errMsg);
            if (ObjectUtils.isNotEmpty(caseVo.getConditionNum()) && ObjectUtils.isNotEmpty(caseVo.getGiveNum())) {
                List<String> giveNumList = Arrays.asList(caseVo.getGiveNum().split(","));
                List<String> conditionNumList = Arrays.asList(caseVo.getConditionNum().split(","));
                if (giveNumList.size() != conditionNumList.size()) {
                    errMsg.add("达成条件阶梯必须和返利标准阶梯保持一致");
                }
            }
            if (checkHelper.paramNotNull(caseVo.getConditionFormula(), "政策形式", errMsg)) {
                FormulaConfigVo formulaConfigVo = formulaConfigVoService.findByConfigCode(caseVo.getConditionFormula());
                if (ObjectUtils.isEmpty(formulaConfigVo)) {
                    errMsg.add("政策形式填写错误");
                } else {
                    caseVo.setConditionFormula(formulaConfigVo.getFormulaConfigCode());
                    caseVo.setConditionFormulaName(formulaConfigVo.getFormulaConfigName());
                }
            }

            caseVo.setMuchDepartmentFlag(BooleanEnum.FALSE.getCapital());
            //多部门标记
            if (ObjectUtils.isNotEmpty(caseVo.getMuchDepartmentMark())) {
                caseVo.setMuchDepartmentFlag(BooleanEnum.TRUE.getCapital());
            }
            //判断不是变更的
            if (CollectionUtils.isEmpty(originalSchemeDetailCodes)) {
                if (ObjectUtils.isNotEmpty(muchDepartmentMap) && muchDepartmentMap.containsKey(caseVo.getMuchDepartmentMark())) {
                    errMsg.add("多部门标记只找到一个,不可进行多部门标记");
                }
            }
            //此处单独调用
            String msg = BusinessRequiredUtil.filedRequiredCheck(filedMap, chineseMap, caseVo);
            if (ObjectUtils.isNotEmpty(msg)) {
                errMsg.add(msg);
            }

            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                caseVo.setCheckFlag(Boolean.FALSE);
                caseVo.setErrMsg(errMsg.toString());
            }
            //寻年度预算
            checkHelper.matchBudget(filedMap, chineseMap, caseVo);
            if (caseVo.getCheckFlag()) {
                caseVo.setRatio(BigDecimal.ZERO);
                //公式计算
                this.calFormulaAmount(caseVo, orgCostCenterMap, salesPlanMap, years, cacheKey, schemeCode, originalSchemeCode, productPhaseMap);
            }
        }

        //进行替换操作
        if (ObjectUtils.isNotEmpty(replaceMap)) {
            list.replaceAll(x -> {
                String originalCode = x.getOriginalSchemeDetailCode();
                return (originalCode != null && !originalCode.isEmpty() && replaceMap.containsKey(originalCode))
                        ? replaceMap.get(originalCode)
                        : x;
            });
        }
        list.forEach(x -> x.setCaseType(getCaseType()));
        //校验多部门
        checkHelper.checkMuchDepartmentMark(list);
    }


    /**
     * 公式计算
     *
     * @param caseVo
     * @param productPhaseMap
     */
    private void calFormulaAmount(MarketingPlanCaseVo caseVo, Map<String, Set<String>> orgCostCenterMap, Map<String, List<MarketingSalesPlanVo>> salesPlanMap,
                                  String years, String cacheKey, String schemeCode, String originalSchemeCode, Map<String, ProductPhaseVo> productPhaseMap) {
        List<MarketingPlanProductVo> feeBelongItemList = caseVo.getFeeBelongItemList();
        if (CollectionUtils.isNotEmpty(feeBelongItemList)) {
            feeBelongItemList.forEach(v -> {
                ProductPhaseVo productPhaseVo = productPhaseMap.get(v.getCode());
                if (Objects.nonNull(productPhaseVo)) {
                    v.setTaxRate(productPhaseVo.getTaxRate());
                }
            });
            caseVo.setFeeBelongItemList(feeBelongItemList);
        }
        FormulaCalBaseVo calBaseVo = nebulaToolkitService.copyObjectByBlankList(caseVo, FormulaCalBaseVo.class, HashSet.class, ArrayList.class);
        calBaseVo.setOrgCode(caseVo.getBelongDepartmentCode());
        calBaseVo.setActCode(ObjectUtils.defaultIfNull(caseVo.getSchemeCode(), null));
        calBaseVo.setActDetailCode(ObjectUtils.defaultIfNull(caseVo.getSchemeDetailCode(), null));
        calBaseVo.setYears(caseVo.getActYears());
        if (CollectionUtils.isNotEmpty(caseVo.getItemList())) {
            Set<String> itemCodes = caseVo.getItemList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toSet());
            calBaseVo.setItemCodeSet(itemCodes);
        }
        if (CollectionUtils.isNotEmpty(caseVo.getProductList())) {
            Set<String> productCodeSet = caseVo.getProductList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toSet());
            calBaseVo.setProductCodeSet(productCodeSet);
        }
        if (CollectionUtils.isNotEmpty(caseVo.getFeeItemList())) {
            Set<String> itemCodes = caseVo.getFeeItemList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toSet());
            calBaseVo.setFeeItemCodeSet(itemCodes);
        }
        if (CollectionUtils.isNotEmpty(caseVo.getFeeProductList())) {
            Set<String> productCodeSet = caseVo.getFeeProductList().stream().map(MarketingPlanProductVo::getCode).collect(Collectors.toSet());
            calBaseVo.setFeeProductCodeSet(productCodeSet);
        }
        calBaseVo.setActDetailCode("测试返利计算-" + UuidCrmUtil.general());
        calBaseVo.setCacheKey(cacheKey);
        List<String> excludeSchemeCode = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(schemeCode)) {
            excludeSchemeCode.add(schemeCode);
        }
        if (ObjectUtils.isNotEmpty(originalSchemeCode)) {
            excludeSchemeCode.add(originalSchemeCode);
        }
        calBaseVo.setExcludeSchemeCodes(excludeSchemeCode);
        try {
            BigDecimal amount = formulaCalService.calResult(FormulaTypeEnum.MARKETING.getCode(), caseVo.getConditionFormula(), calBaseVo);
            caseVo.setApplyAmount(amount);
            //判断合同为空 并且计算结果为0
            if (ObjectUtils.isEmpty(caseVo.getContractCode()) && amount.compareTo(BigDecimal.ZERO) < 1){
                caseVo.setErrMsg("返利计算结果为0,请检查公式是否正确");
                caseVo.setCheckFlag(Boolean.FALSE);
            }
            if (Lists.newArrayList("wire_transfer", "deductions").contains(caseVo.getCashType())) {
                caseVo.setLineTaxRate(new BigDecimal("0.06"));
                caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
            } else if (Lists.newArrayList("ticket_buckle", "replenishment").contains(caseVo.getCashType())) {
                String code = caseVo.getFeeBelongItemList().get(0).getCode();
                log.info("expensePlanItemCode: {}", code);
                if (StringUtils.isNotEmpty(code) && Lists.newArrayList("BC1001", "BC1002").contains(code)) {
                    caseVo.setLineTaxRate(new BigDecimal("0.1"));
                    caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                } else {
                    ProductPhaseVo productPhaseVo = productPhaseMap.get(code);
                    if (Objects.isNull(productPhaseVo) || Objects.isNull(productPhaseVo.getTaxRate())) {
                        caseVo.setErrMsg("未匹配到对应品项或对应品项未维护税率");
                        caseVo.setCheckFlag(Boolean.FALSE);
                    } else {
                        caseVo.setLineTaxRate(productPhaseVo.getTaxRate());
                        caseVo.setLineNoTaxApplyAmount(caseVo.getApplyAmount().divide(BigDecimal.ONE.add(caseVo.getLineTaxRate()), 2, BigDecimal.ROUND_HALF_DOWN));
                    }
                }
            }
        } catch (Exception e) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            e.printStackTrace(new PrintStream(baos));
            String exception = baos.toString();
            log.error("计算错误日志:{},错误堆栈:{}", e.getMessage(), exception);
            caseVo.setErrMsg("返利公式计算错误:" + e.getMessage());
            caseVo.setCheckFlag(Boolean.FALSE);
        }
        //计算费率
        Set<String> costCenterCodes = orgCostCenterMap.getOrDefault(caseVo.getBelongDepartmentCode(), Sets.newHashSet());
        checkHelper.calPlanCaseRatio(salesPlanMap, costCenterCodes, caseVo);

    }


    @Override
    public String getCaseType() {
        return MarketingPlanCaseTypeEnum.back.getCode();
    }
}
