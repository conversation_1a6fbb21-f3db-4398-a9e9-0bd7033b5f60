package com.biz.crm.tpm.business.activities.marketingplan.dto;

import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanClosureVo;
import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/3 20:25
 */
@Data
public class PlanClosureLogEventDto implements NebulaEventDto {

    private PlanClosureVo original;

    private PlanClosureVo newest;

    private List<PlanClosureVo> newestList;

    private String id;
}
