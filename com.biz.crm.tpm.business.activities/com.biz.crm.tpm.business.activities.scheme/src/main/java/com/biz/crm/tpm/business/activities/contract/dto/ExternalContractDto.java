package com.biz.crm.tpm.business.activities.contract.dto;

import com.biz.crm.business.common.base.vo.OssFileVo;
import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/17 09:46
 */
@Data
@ApiModel(value = "ExternalContractVo", description = "外部合同")
public class ExternalContractDto extends TenantFlagOpDto {

    @ApiModelProperty("流程ID")
    private String processId;

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("唯一编码")
    private String onlyKey;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同状态")
    private String contractStatus;

    @ApiModelProperty("辅助列")
    private String auxiliary;

    @ApiModelProperty("合同类型")
    private String contractType;

    @ApiModelProperty("是否补充")
    private String isReplenish;

    @ApiModelProperty("原始合同")
    private String originalContractCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("是否外部合同")
    private String externalFlag;

    @ApiModelProperty("合同附件")
    private List<OssFileVo> fileUrlList;

    @ApiModelProperty("明细列表")
    private List<ExternalContractDetailDto> detailList;

    private List<ExternalContractDetailDto> rebateList;

    private List<ExternalContractDetailDto> promotionList;

    private List<ExternalContractDetailDto> fixedList;
}
