package com.biz.crm.tpm.business.activities.overallplan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/30 16:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_overall_plan_budget")
@Table(
        name = "tpm_overall_plan_budget",
        indexes = {
                @Index(name = "tpm_overall_plan_budget_index0", columnList = "scheme_code")
        })
@org.hibernate.annotations.Table(appliesTo = "tpm_overall_plan_budget", comment = "总部预算追踪")
@ApiModel(value = "OverallPlanBudget", description = "总部预算追踪")
public class OverallPlanBudget extends UuidFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(32) comment '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(32) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年月'")
    private String years;

    @ApiModelProperty("部门编码")
    @Column(name = "department_code", columnDefinition = "varchar(32) comment '部门编码'")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    @Column(name = "department_name", columnDefinition = "varchar(128) comment '部门名称'")
    private String departmentName;

    @ApiModelProperty("科目编码")
    @Column(name = "subject_code", columnDefinition = "varchar(32) comment '科目编码'")
    private String subjectCode;

    @ApiModelProperty("科目名称")
    @Column(name = "subject_name", columnDefinition = "varchar(128) comment '科目名称'")
    private String subjectName;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(32) comment '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(200) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("品项编码")
    @Column(name = "item_code", columnDefinition = "varchar(32) comment '品项编码'")
    private String itemCode;

    @ApiModelProperty("品项名称")
    @Column(name = "item_name", columnDefinition = "varchar(200) comment '品项名称'")
    private String itemName;

    @ApiModelProperty("产品编码")
    @Column(name = "product_code", columnDefinition = "varchar(32) comment '产品编码'")
    private String productCode;

    @ApiModelProperty("产品名称")
    @Column(name = "product_name", columnDefinition = "varchar(200) comment '产品名称'")
    private String productName;

    @ApiModelProperty("余额")
    @Column(name = "surplus_amount", columnDefinition = "decimal(18,4) comment '可用余额'")
    private BigDecimal surplusAmount;

    @ApiModelProperty("使用金额")
    @Column(name = "used_amount", columnDefinition = "decimal(18,4) comment '使用金额'")
    private BigDecimal usedAmount;

    @ApiModelProperty("管控编码")
    @Column(name = "control_code", columnDefinition = "varchar(32) comment '管控编码'")
    private String controlCode;

    @ApiModelProperty("预算追踪编码")
    @Column(name = "track_code", columnDefinition = "varchar(32) comment '预算追踪编码'")
    private String trackCode;

    @ApiModelProperty("管控形式")
    @Column(name = "control_form", columnDefinition = "varchar(32) comment '管控形式'")
    private String controlForm;

    @ApiModelProperty("申请金额")
    @Column(name = "apply_amount", columnDefinition = "decimal(18,4) comment '申请金额'")
    private BigDecimal applyAmount;
}
