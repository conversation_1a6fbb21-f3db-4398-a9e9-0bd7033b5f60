package com.biz.crm.tpm.business.activities.materialdemand.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.materialdemand.service.MaterialDemandCollectService;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectDetailVo;
import com.biz.crm.tpm.business.activities.materialdemand.vo.MaterialDemandCollectVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/2 13:51
 */
@RestController
@RequestMapping("/v1/materialDemandCollectController")
@Api(tags = "物料需求汇总")
public class MaterialDemandCollectController extends BusinessPageCacheController<MaterialDemandCollectDetailVo, MaterialDemandCollectDetailVo> {

    @Autowired
    private MaterialDemandCollectService service;

    @Resource
    private RedisLockService redisLockService;


    @ApiOperation(value = "分页列表")
    @GetMapping("findList")
    public Result<Page<MaterialDemandCollectVo>> findList(@PageableDefault(50) Pageable pageable, MaterialDemandCollectVo vo) {
        return Result.ok(service.findList(pageable, vo));
    }


    @ApiOperation(value = "查询详情")
    @GetMapping("queryByIdOrCode")
    public Result<MaterialDemandCollectVo> queryByIdOrCode(@RequestParam(required = false, value = "id") String id,
                                                           @RequestParam(required = false, value = "collectCode") String collectCode) {
        return Result.ok(service.queryByIdOrCode(id, collectCode));
    }

    @ApiOperation(value = "新增/编辑")
    @PostMapping("createOrUpdate")
    public Result createOrUpdate(@RequestBody MaterialDemandCollectVo vo) {
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            service.update(vo);
        } else {
            service.create(vo);
        }
        return Result.ok();
    }

    @ApiOperation(value = "提交新增/编辑")
    @PostMapping("submitCreateOrUpdate")
    public Result submitCreateOrUpdate(@RequestBody MaterialDemandCollectVo vo) {
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            Validate.notNull(vo.getCacheKey(), "缓存cacheKey不能为空");
            //验证缓存分页锁是否存在
            this.checkPageCacheLock(vo.getCacheKey());
            service.submitUpdate(vo);
        } else {
            service.submitCreate(vo);
        }
        return Result.ok();
    }

    @ApiOperation(value = "手动审批")
    @PostMapping("manualApproval")
    public Result manualApproval(@RequestBody List<String> idList) {
        service.manualApproval(idList);
        return Result.ok();
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("deleteBatchByIds")
    public Result deleteBatchByIds(@RequestParam List<String> ids) {
        String codes = service.deleteBatchByIds(ids);
        if (ObjectUtils.isNotEmpty(codes)) {
            return Result.error(String.format("汇总编码%s状态为审批中/审批通过状态不能删除", codes));
        }
        return Result.ok();
    }

    /**
     * 流程撤回
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "流程撤回")
    @GetMapping("recover")
    public Result<?> recover(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                             @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
        try {
            this.service.recover(code, remark);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "获取当前组织下的物料汇总数据")
    @GetMapping("collectMaterial")
    public Result<List<MaterialDemandCollectDetailVo>> collectMaterial(@RequestParam String orgCode, @RequestParam String cacheKey) {
        return Result.ok(service.collectMaterial(orgCode, cacheKey));
    }

    @ApiOperation(value = "获取物料汇总")
    @GetMapping("findGroupMaterialDemand")
    public Result<Page<MaterialDemandCollectDetailVo>> findGroupMaterialDemand(@PageableDefault(50) Pageable pageable,String cacheKey) {
        return Result.ok(service.findGroupMaterialDemand(pageable, cacheKey));
    }

    @ApiOperation(value = "选择物资需求列表")
    @GetMapping("selectMaterialDemandDetailList")
    public Result selectMaterialDemandDetailList(@RequestParam String cacheKey, @RequestParam List<String> demandCodeList) {
        service.selectMaterialDemandDetailList(cacheKey, demandCodeList);
        return Result.ok();
    }

}
