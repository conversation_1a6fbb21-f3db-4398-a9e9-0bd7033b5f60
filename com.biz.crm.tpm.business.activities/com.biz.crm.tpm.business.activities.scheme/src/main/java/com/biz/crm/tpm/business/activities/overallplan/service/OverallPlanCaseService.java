package com.biz.crm.tpm.business.activities.overallplan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.activities.overallplan.entity.OverallPlanCase;
import com.biz.crm.tpm.business.activities.overallplan.vo.*;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;

import java.util.List;
import java.util.Set;

public interface OverallPlanCaseService {

    void saveBatchList(OverallPlanVo vo, String schemeCode);

    List<OverallPlanCaseVo> checkCaseList(List<OverallPlanCaseVo> list, String schemeType);

    List<OverallPlanCaseVo> findListBySchemeId(String id);

    List<OverallPlanCase> findListByReleaseDetailNotNull(String schemeCode);

    void deleteBySchemeCodes(List<String> schemeCodes);

    Page<OverallPlanCaseVo> findHeadOverallPlanCaseList(Pageable pageable, HeadOverallPlanCaseVo vo);

    Page<OverallPlanCaseVo> findHeadOverallPlanCaseToMarketing(Pageable pageable, HeadOverallPlanCaseVo vo);

    List<OverallPlanCase> findCaseListBySchemeDetailCodes(List<String> schemeDetailCodes, String processStatus);

    List<BearOverallPlanCaseVo> findReleaseHeadCaseList(List<String> schemeDetailCodes);

    List<OverallPlanCase> findAlreadyBearCase(List<String> schemeDetailCodes);

    List<BearOverallPlanCaseVo> findReleaseCaseByMarketingPlan(List<String> schemeDetailCodes);

    Page<OverallPlanCaseVo> findNotCaseBySchemeTypeList(Pageable pageable, OverallPlanCaseVo vo, String schemeType);

    Set<String> findAlreadyUnderTakeCaseList(String schemeType);

    void updateCaseStatusBySchemeDetailCodes(List<String> schemeDetailCodes, String delFlag);

    Page<OverallPlanCaseVo> findRegionOverallPlanCase(Pageable pageable, String headSchemeDetailCode);

    List<OverallPlanCaseVo> findCasePartFiledListBySchemeCodes(List<String> schemeCodes);

    List<OverallPlanCaseVo> findCaseListBySchemeCode(String schemeCode);

    Page<OverallPlanCaseVo> findBearPlanCaseList(@PageableDefault(50) Pageable pageable, OverallPlanCaseVo vo);

    List<OverallPlanCaseVo> checkRegionOverall(Set<String> orgCodeSet, String schemeType, String years, String detailFlag);

    List<OverallPlanCase> findHeadPlanCaseListByOrgCodes(List<String> orgCodes, String years, String bearFlag,String schemeCode);

    List<OverallPlanCase> findCaseListByReleaseDetailCodes(List<String> releaseDetailCode, String dictCode);
}
