package com.biz.crm.tpm.business.activities.regioncollect.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.CustomerGainsAndLossesVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel(value = "RegionCollectItemEstimationVo", description = "大区汇总-品项测算")
public class RegionCollectItemEstimationVo extends CustomerGainsAndLossesVo {

    @ApiModelProperty("大区汇总编码")
    private String collectCode;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;
}
