package com.biz.crm.tpm.business.activities.marketingplan.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlan;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanFieldsEnum;
import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.marketingplan.repository.MarketingPlanRepository;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanEstimationService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanOaService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.*;
import com.biz.crm.tpm.business.activities.overallplan.eunm.OverallPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.activities.regioncollect.eunm.RegionCollectProjectEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketPlanCaseFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketPlanChangeFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.CostCenterFieldsEnum;
import com.biz.crm.tpm.business.activities.sdk.enums.MarketingEstimationFieldsEnum;
import com.biz.crm.tpm.business.activities.stagingscheme.service.ITpmStagingSchemeService;
import com.biz.crm.tpm.business.activities.stagingscheme.vo.TpmStagingSchemeVo;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.*;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import liquibase.util.StringUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class MarketingPlanOaServiceImpl implements MarketingPlanOaService {

    @Autowired(required = false)
    private MarketingPlanRepository marketingPlanRepository;
    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;
    @Autowired
    private MarketingPlanEstimationService marketingPlanEstimationService;
    @Autowired(required = false)
    private PushOaService pushOaService;
    @Autowired
    private UserVoService userVoService;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Value("${domain-name:}")
    private String domainName;
    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;
    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private BudgetSubjectsVoService budgetSubjectsVoService;

    @Autowired(required = false)
    private CostTypeCategoryVoService costTypeCategoryVoService;

    /**
     * 推送OA
     *
     * @param
     * @return
     */
    @Override
    public JSONObject pushOa(MarketingPlanVo order) {
        order.setBusinessCode(order.getSchemeCode());
        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(order));
        List<MarketingPlanCaseVo> detailCaseList = marketingPlanCaseService.findBySchemeCode(order.getSchemeCode());
        Set<String> orgCodeList = detailCaseList.stream().map(MarketingPlanCaseVo::getBearDepartmentCode).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            orderJsonObject.put("deptCode", oaOrgCodeSet.stream().collect(Collectors.joining(",")));
        }
        List<String> originalSchemeDetailCodes = detailCaseList.stream().filter(e -> StringUtils.isNotBlank(e.getOriginalSchemeDetailCode())).map(e -> e.getOriginalSchemeDetailCode()).collect(Collectors.toList());
        List<MarketingPlanCaseVo> changeList = marketingPlanCaseService.findByCaseCodes(originalSchemeDetailCodes);
        Map<String, MarketingPlanCaseVo> changeMap = changeList.stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));

        List<MarketingPlanEstimationVo> regionCollectMarketingEstimationVos = order.getMarketingEstimationList();
        regionCollectMarketingEstimationVos = Optional.ofNullable(regionCollectMarketingEstimationVos).orElse(Lists.newArrayList());
        Map<String, MarketingPlanEstimationVo> marketingMap = regionCollectMarketingEstimationVos.stream()
                .collect(Collectors.toMap(MarketingPlanEstimationVo::getProjectCode, v -> v, (a, b) -> a));
        MarketingPlanEstimationVo estimationRevenue = marketingMap.get(RegionCollectProjectEnum.estimation_revenue.getCode());

        orderJsonObject.put("remark", order.getChangeDesc());
        if (!CollectionUtils.isEmpty(order.getMarketingEstimationList())) {
            MarketingPlanEstimationVo profit = marketingMap.get(RegionCollectProjectEnum.profit.getCode());
            MarketingPlanEstimationVo marketingCostTotal = marketingMap.get(RegionCollectProjectEnum.marketing_cost_total.getCode());

            orderJsonObject.put("ratioAfter", marketingCostTotal.getEstimateRatio());
            //变更后规划利润额=利润预计达成
            BigDecimal profitMarginAfter = profit.getEstimateAmount();
            orderJsonObject.put("profitMarginAfter", profitMarginAfter);
            orderJsonObject.put("changePlanIncome", Optional.ofNullable(estimationRevenue.getEstimateAmount()).orElse(BigDecimal.ZERO));
            //规划收入
            orderJsonObject.put("estimateAmount", estimationRevenue.getEstimateAmount());
            //预算收入
            orderJsonObject.put("budgetAmount", estimationRevenue.getBudgetAmount());
            //营销费用小计-预算达成
            orderJsonObject.put("marketingBudgetAmount", marketingCostTotal.getBudgetAmount());

            //变更前方案为“方案规划”，查方案规划营销测算表，其他查大区营销测算表
            List<MarketingPlanEstimationVo> oldMarketingList;
            MarketingPlan marketingPlanOriginal = marketingPlanRepository.queryByIdOrSchemeCode(null, order.getOriginalSchemeCode());
            if (MarketingPlanSchemeTypeEnum.o_two_o.getCode().equals(marketingPlanOriginal.getSchemeType())) {
                oldMarketingList = marketingPlanEstimationService.findListBySchemeCode(Arrays.asList(order.getOriginalSchemeCode()));
            } else {
                oldMarketingList = marketingPlanEstimationService.findOriginalListBySchemeCode(Arrays.asList(order.getOriginalSchemeCode()));
            }
            if (!CollectionUtils.isEmpty(oldMarketingList)) {
                Map<String, MarketingPlanEstimationVo> oldMarketingMap = oldMarketingList.stream()
                        .collect(Collectors.toMap(MarketingPlanEstimationVo::getProjectCode, Function.identity(), (a, b) -> a));
                MarketingPlanEstimationVo estimationRevenueOld = oldMarketingMap.get(RegionCollectProjectEnum.estimation_revenue.getCode());
                MarketingPlanEstimationVo profitOld = oldMarketingMap.get(RegionCollectProjectEnum.profit.getCode());
                MarketingPlanEstimationVo marketingCostTotalOld = oldMarketingMap.get(RegionCollectProjectEnum.marketing_cost_total.getCode());

                orderJsonObject.put("ratioBefore", marketingCostTotalOld.getEstimateRatio());
                //变更后规划利润额=利润预计达成
                BigDecimal profitMarginBefore = profitOld.getEstimateAmount();
                orderJsonObject.put("profitMarginBefore", profitMarginBefore);
                orderJsonObject.put("planIncome", Optional.ofNullable(estimationRevenueOld.getEstimateAmount()).orElse(BigDecimal.ZERO));
            }
        }

//        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
//        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        orderJsonObject.put("title", "营销方案变更：" + order.getSchemeName());
        List<String> projectCodeList = regionCollectMarketingEstimationVos.stream().map(MarketingPlanEstimationVo::getProjectCode)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListByCategoryCodes(projectCodeList);
        Map<String, String> budgetSubjectsCategoryCodeMap = costTypeCategoryVos.stream().filter(k -> StringUtil.isNotEmpty(k.getBudgetSubjectsCode()))
                .collect(Collectors.toMap(CostTypeCategoryVo::getBudgetSubjectsCode, CostTypeCategoryVo::getCategoryCode, (n, o) -> n));
        Set<String> categoryCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getCategoryCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        Set<String> budgetSubjectsCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getBudgetSubjectsCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        List<BudgetSubjectsVo> budgetSubjectsVos = budgetSubjectsVoService.findByCodes(budgetSubjectsCodeSet);
        List<MarketingPlanEstimationVo> detailDtoList = (List<MarketingPlanEstimationVo>) nebulaToolkitService.copyCollectionByBlankList(
                regionCollectMarketingEstimationVos.stream().filter(k -> StringUtil.isNotEmpty(k.getProjectCode()))
                        .filter(k -> !categoryCodeSet.contains(k.getProjectCode())).collect(Collectors.toList()),
                MarketingPlanEstimationVo.class, MarketingPlanEstimationVo.class, LinkedHashSet.class, ArrayList.class);
        AtomicInteger index = new AtomicInteger(20);
        budgetSubjectsVos.stream().filter(k -> StringUtil.isNotEmpty(k.getParentBudgetSubjectsCode()))
                .collect(Collectors.groupingBy(BudgetSubjectsVo::getParentBudgetSubjectsCode))
                .forEach((parentCode, childList) -> {
                    MarketingPlanEstimationVo detailDto = new MarketingPlanEstimationVo();
                    AtomicReference<BigDecimal> budgetAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> estimateAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> differenceAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    childList.forEach(k -> {
                        String categoryCode = budgetSubjectsCategoryCodeMap.getOrDefault(k.getBudgetSubjectsCode(), "");
                        MarketingPlanEstimationVo estimationVo = marketingMap.get(categoryCode);
                        if (Objects.isNull(estimationVo)) {
                            return;
                        }
                        if (Objects.nonNull(estimationVo.getBudgetAmount())) {
                            budgetAmountTotal.set(estimationVo.getBudgetAmount().add(budgetAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getEstimateAmount())) {
                            estimateAmountTotal.set(estimationVo.getEstimateAmount().add(estimateAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getDifferenceAmount())) {
                            differenceAmountTotal.set(estimationVo.getDifferenceAmount().add(differenceAmountTotal.get()));
                        }
                    });

                    detailDto.setBudgetAmount(budgetAmountTotal.get());
                    detailDto.setEstimateAmount(estimateAmountTotal.get());
                    detailDto.setDifferenceAmount(differenceAmountTotal.get());
                    if (Objects.nonNull(estimationRevenue)) {
                        if (Objects.nonNull(estimationRevenue.getBudgetAmount())
                                && estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setBudgetRatio(detailDto.getBudgetAmount().divide(estimationRevenue.getBudgetAmount(), 2, RoundingMode.HALF_UP));
                        }
                        if (Objects.nonNull(estimationRevenue.getEstimateAmount())
                                && estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setEstimateRatio(detailDto.getEstimateAmount().divide(estimationRevenue.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                        }
                    }
                    if (Objects.nonNull(detailDto.getEstimateAmount())
                            && Objects.nonNull(detailDto.getDifferenceAmount())
                            && detailDto.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                        detailDto.setDifferenceRatio(detailDto.getDifferenceAmount().divide(detailDto.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                    }
                    detailDto.setProjectCode(parentCode);
                    detailDto.setProjectName(childList.get(0).getParentBudgetSubjectsName());
                    detailDto.setSort(index.getAndIncrement());
                    detailDtoList.add(detailDto);
                });
        detailDtoList.forEach(detailDto -> {
            if (Objects.isNull(detailDto.getSort())) {
                RegionCollectProjectEnum collectProjectEnum = RegionCollectProjectEnum.findByCode(detailDto.getProjectCode());
                if (Objects.nonNull(collectProjectEnum)) {
                    detailDto.setSort(collectProjectEnum.getSort());
                } else {
                    detailDto.setSort(index.getAndIncrement());
                }
            }
        });
        List<MarketingPlanEstimationVo> detailSendDtoList = detailDtoList.stream().sorted(Comparator.comparingInt(MarketingPlanEstimationVo::getSort)).collect(Collectors.toList());


        // 主表
        // 业务类型
        String businessType = MqConstant.TAG_TPM_MARKET_PLAN_CHANGE;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.ACTIVITY_MARKET_PLAN_AUDITFORM.getUrlCode() + "?code=" + order.getSchemeCode());
        List<JSONArray> jsonArray = Lists.newArrayList();
        boolean detailMethod1 = false;
        boolean detailMethod2 = false;
        boolean detailMethod3 = false;
        List<BiConsumer<List<WorkflowRequestTableField>, JSONObject>> consumerList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(order.getGainsAndLossesComparsionList())) {
            jsonArray.add(JSONArray.parseArray(JSON.toJSONString(order.getGainsAndLossesComparsionList())));
            detailMethod1 = true;
        }
        if (!CollectionUtils.isEmpty(detailSendDtoList)) {
            jsonArray.add(JSONArray.parseArray(JSON.toJSONString(detailSendDtoList)));
            detailMethod2 = true;
        }

        //查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
            detailCaseList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()))
                    .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
        }
        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        Set<String> bearDepartmentNameSet = new HashSet<>();
        detailCaseList.stream().filter(k -> StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                .collect(Collectors.groupingBy(e -> e.getCostCenterCode() + e.getCostTypeCategorySecondName(), Collectors.toList())).forEach((key, list) -> {
            CostCenterDetailDto detailDto = new CostCenterDetailDto();
            detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
            detailDto.setCostCenterName(list.get(0).getCostCenterName());
            List<OrgOaOrgVo> orgOaOrgVos = orgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
            if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
            } else {
                detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
            }
            detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
            detailDto.setBearAmount(list.stream().map(MarketingPlanCaseVo::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            costCenterDetailDtoList.add(detailDto);
        });
        Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);
        if (!CollectionUtils.isEmpty(costCenterDetailDtoList)) {
            jsonArray.add(JSONArray.parseArray(JSON.toJSONString(costCenterDetailDtoList)));
            detailMethod3 = true;

            List<MarketPlanCaseDetailDto> marketPlanCaseDetailDtoList = Lists.newArrayList();
            detailCaseList.forEach(e -> {
                MarketPlanCaseDetailDto detailDto = new MarketPlanCaseDetailDto();
                detailDto.setFysybm(e.getBelongDepartmentName());
                detailDto.setKhmc(e.getCustomerName());
                detailDto.setMdmc(e.getTerminalName());
                detailDto.setFyxm(e.getDetailName());
                detailDto.setFygzyf(e.getYears());
                detailDto.setSqje(e.getApplyAmount());
                detailDto.setFaghmc(e.getSchemeName());
                detailDto.setHdms(e.getActDesc());
                detailDto.setFamxbm(e.getSchemeDetailCode());
                detailDto.setCbzx(e.getCostCenterName());
                detailDto.setXzjghtybzs(orgOaOrgVoMap.get(e.getBearDepartmentCode()).get(0).getOaOrgCode());
                MarketingPlanCaseVo planCaseVo = changeMap.get(e.getOriginalSchemeDetailCode());
                detailDto.setBgqje(planCaseVo == null ? BigDecimal.ZERO : planCaseVo.getApplyAmount());
                detailDto.setBghje(e.getApplyAmount());
                marketPlanCaseDetailDtoList.add(detailDto);
            });

            jsonArray.add(JSONArray.parseArray(JSON.toJSONString(marketPlanCaseDetailDtoList)));
        }

        JSONObject response;
        if (detailMethod1 && detailMethod2 && detailMethod3) {
            response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, jsonArray, workflowId, workflowName,
                    mainTableMethod, detailTableMethod1, detailTableMethod2, detailTableMethod3, detailTableMethod4);
        } else if (detailMethod1 && detailMethod2) {
            response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, jsonArray, workflowId, workflowName,
                    mainTableMethod, detailTableMethod1, detailTableMethod2);
        } else if (detailMethod1 && detailMethod3) {
            response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, jsonArray, workflowId, workflowName,
                    mainTableMethod, detailTableMethod1, detailTableMethod3, detailTableMethod4);
        } else if (detailMethod2 && detailMethod3) {
            response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, jsonArray, workflowId, workflowName,
                    mainTableMethod, detailTableMethod2, detailTableMethod3, detailTableMethod4);
        } else if (detailMethod1) {
            response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, jsonArray, workflowId, workflowName,
                    mainTableMethod, detailTableMethod1);
        } else if (detailMethod2) {
            response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, jsonArray, workflowId, workflowName,
                    mainTableMethod, detailTableMethod2);
        } else if (detailMethod3) {
            response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, jsonArray, workflowId, workflowName,
                    mainTableMethod, detailTableMethod3, detailTableMethod4);
        } else {
            response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, jsonArray, workflowId, workflowName,
                    mainTableMethod);
        }


        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        Validate.isTrue(response.containsKey("code"), "OA流程提交失败，OA未正常返回信息");

        Integer resultCode = response.getInteger("code");
        Validate.isTrue(resultCode == 100, String.format("提交OA流程失败,失败原因:%s", response.getString("msg")));
        JSONObject ja = response.getJSONObject("data");
        MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, order.getSchemeCode());
        entity.setProcessNumber(ja.getString("out"));
        entity.setProcessDate(new Date());
        entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
        entity.setOaId(response.getString("oaId"));
        entity.setOaUserName(response.getString("oaUserName"));
        marketingPlanRepository.updateById(entity);

        return response;
    }

    /**
     * 重新提交OA
     *
     * @param
     * @return
     */
    @Override
    public JSONObject resubmitOa(MarketingPlanVo order) {
        order.setBusinessCode(order.getSchemeCode());
        OaResubmitDto dto = new OaResubmitDto();

        // 获取当前登录人信息
        FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);

        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType = MqConstant.TAG_TPM_MARKET_PLAN_CHANGE;
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        order.setTpmUrl(domainName + TpmOaPageEnum.ACTIVITY_MARKET_PLAN_AUDITFORM.getUrlCode() + "?code=" + order.getSchemeCode());
        List<MarketingPlanCaseVo> detailCaseList = marketingPlanCaseService.findBySchemeCode(order.getSchemeCode());
        Set<String> orgCodeList = detailCaseList.stream().map(e -> e.getBearDepartmentCode()).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            order.setDeptCode(oaOrgCodeSet.stream().collect(Collectors.joining(",")));
        }
        List<String> originalSchemeDetailCodes = detailCaseList.stream().filter(e -> StringUtils.isNotBlank(e.getOriginalSchemeDetailCode())).map(e -> e.getOriginalSchemeDetailCode()).collect(Collectors.toList());
        List<MarketingPlanCaseVo> changeList = marketingPlanCaseService.findByCaseCodes(originalSchemeDetailCodes);
        Map<String, MarketingPlanCaseVo> changeMap = changeList.stream().collect(Collectors.toMap(e -> e.getSchemeDetailCode(), Function.identity(), (a, b) -> a));

        List<MarketingPlanEstimationVo> regionCollectMarketingEstimationVos = order.getMarketingEstimationList();
        regionCollectMarketingEstimationVos = Optional.ofNullable(regionCollectMarketingEstimationVos).orElse(Lists.newArrayList());
        Map<String, MarketingPlanEstimationVo> marketingMap = regionCollectMarketingEstimationVos.stream()
                .collect(Collectors.toMap(MarketingPlanEstimationVo::getProjectCode, v -> v, (a, b) -> a));
        MarketingPlanEstimationVo estimationRevenue = marketingMap.get(RegionCollectProjectEnum.estimation_revenue.getCode());

        if (!CollectionUtils.isEmpty(order.getMarketingEstimationList())) {
            MarketingPlanEstimationVo profit = marketingMap.get(RegionCollectProjectEnum.profit.getCode());
            MarketingPlanEstimationVo marketingCostTotal = marketingMap.get(RegionCollectProjectEnum.marketing_cost_total.getCode());

            order.setRatioAfter(marketingCostTotal.getEstimateRatio());
            //变更后规划利润额=利润预计达成
            BigDecimal profitMarginAfter = profit.getEstimateAmount();
            order.setProfitMarginAfter(profitMarginAfter);
            order.setChangePlanIncome(Optional.ofNullable(estimationRevenue.getEstimateAmount()).orElse(BigDecimal.ZERO));
            //规划收入
            order.setEstimateAmount(estimationRevenue.getEstimateAmount());
            //预算收入
            order.setBudgetAmount(estimationRevenue.getBudgetAmount());
            //营销费用小计-预算达成
            order.setMarketingBudgetAmount(marketingCostTotal.getBudgetAmount());

            //变更前方案为“方案规划”，查方案规划营销测算表，其他查大区营销测算表
            List<MarketingPlanEstimationVo> oldMarketingList;
            MarketingPlan marketingPlanOriginal = marketingPlanRepository.queryByIdOrSchemeCode(null, order.getOriginalSchemeCode());
            if (MarketingPlanSchemeTypeEnum.o_two_o.getCode().equals(marketingPlanOriginal.getSchemeType())) {
                oldMarketingList = marketingPlanEstimationService.findListBySchemeCode(Arrays.asList(order.getOriginalSchemeCode()));
            } else {
                oldMarketingList = marketingPlanEstimationService.findOriginalListBySchemeCode(Arrays.asList(order.getOriginalSchemeCode()));
            }
            if (!CollectionUtils.isEmpty(oldMarketingList)) {
                Map<String, MarketingPlanEstimationVo> oldMarketingMap = oldMarketingList.stream()
                        .collect(Collectors.toMap(MarketingPlanEstimationVo::getProjectCode, Function.identity(), (a, b) -> a));
                MarketingPlanEstimationVo estimationRevenueOld = oldMarketingMap.get(RegionCollectProjectEnum.estimation_revenue.getCode());
                MarketingPlanEstimationVo profitOld = oldMarketingMap.get(RegionCollectProjectEnum.profit.getCode());
                MarketingPlanEstimationVo marketingCostTotalOld = oldMarketingMap.get(RegionCollectProjectEnum.marketing_cost_total.getCode());

                order.setRatioBefore(marketingCostTotalOld.getEstimateRatio());
                //变更后规划利润额=利润预计达成
                BigDecimal profitMarginBefore = profitOld.getEstimateAmount();
                order.setProfitMarginBefore(profitMarginBefore);
                order.setPlanIncome(Optional.ofNullable(estimationRevenueOld.getEstimateAmount()).orElse(BigDecimal.ZERO));
            }
        }
        order.setRemark(order.getChangeDesc());

        dto.setBusinessCode(businessType);
        dto.setRequestId(order.getProcessNumber());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        List<String> projectCodeList = regionCollectMarketingEstimationVos.stream().map(MarketingPlanEstimationVo::getProjectCode)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<CostTypeCategoryVo> costTypeCategoryVos = costTypeCategoryVoService.findListByCategoryCodes(projectCodeList);
        Map<String, String> budgetSubjectsCategoryCodeMap = costTypeCategoryVos.stream().filter(k -> StringUtil.isNotEmpty(k.getBudgetSubjectsCode()))
                .collect(Collectors.toMap(CostTypeCategoryVo::getBudgetSubjectsCode, CostTypeCategoryVo::getCategoryCode, (n, o) -> n));
        Set<String> categoryCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getCategoryCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        Set<String> budgetSubjectsCodeSet = costTypeCategoryVos.stream().map(CostTypeCategoryVo::getBudgetSubjectsCode)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        List<BudgetSubjectsVo> budgetSubjectsVos = budgetSubjectsVoService.findByCodes(budgetSubjectsCodeSet);
        List<MarketingPlanEstimationVo> detailDtoList = (List<MarketingPlanEstimationVo>) nebulaToolkitService.copyCollectionByBlankList(
                regionCollectMarketingEstimationVos.stream().filter(k -> StringUtil.isNotEmpty(k.getProjectCode()))
                        .filter(k -> !categoryCodeSet.contains(k.getProjectCode())).collect(Collectors.toList()),
                MarketingPlanEstimationVo.class, MarketingPlanEstimationVo.class, LinkedHashSet.class, ArrayList.class);
        AtomicInteger index = new AtomicInteger(20);
        budgetSubjectsVos.stream().filter(k -> StringUtil.isNotEmpty(k.getParentBudgetSubjectsCode()))
                .collect(Collectors.groupingBy(BudgetSubjectsVo::getParentBudgetSubjectsCode))
                .forEach((parentCode, childList) -> {
                    MarketingPlanEstimationVo detailDto = new MarketingPlanEstimationVo();
                    AtomicReference<BigDecimal> budgetAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> estimateAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    AtomicReference<BigDecimal> differenceAmountTotal = new AtomicReference<>(BigDecimal.ZERO);
                    childList.forEach(k -> {
                        String categoryCode = budgetSubjectsCategoryCodeMap.getOrDefault(k.getBudgetSubjectsCode(), "");
                        MarketingPlanEstimationVo estimationVo = marketingMap.get(categoryCode);
                        if (Objects.isNull(estimationVo)) {
                            return;
                        }
                        if (Objects.nonNull(estimationVo.getBudgetAmount())) {
                            budgetAmountTotal.set(estimationVo.getBudgetAmount().add(budgetAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getEstimateAmount())) {
                            estimateAmountTotal.set(estimationVo.getEstimateAmount().add(estimateAmountTotal.get()));
                        }
                        if (Objects.nonNull(estimationVo.getDifferenceAmount())) {
                            differenceAmountTotal.set(estimationVo.getDifferenceAmount().add(differenceAmountTotal.get()));
                        }
                    });

                    detailDto.setBudgetAmount(budgetAmountTotal.get());
                    detailDto.setEstimateAmount(estimateAmountTotal.get());
                    detailDto.setDifferenceAmount(differenceAmountTotal.get());
                    if (Objects.nonNull(estimationRevenue)) {
                        if (Objects.nonNull(estimationRevenue.getBudgetAmount())
                                && estimationRevenue.getBudgetAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setBudgetRatio(detailDto.getBudgetAmount().divide(estimationRevenue.getBudgetAmount(), 2, RoundingMode.HALF_UP));
                        }
                        if (Objects.nonNull(estimationRevenue.getEstimateAmount())
                                && estimationRevenue.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                            detailDto.setEstimateRatio(detailDto.getEstimateAmount().divide(estimationRevenue.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                        }
                    }
                    if (Objects.nonNull(detailDto.getEstimateAmount())
                            && Objects.nonNull(detailDto.getDifferenceAmount())
                            && detailDto.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0) {
                        detailDto.setDifferenceRatio(detailDto.getDifferenceAmount().divide(detailDto.getEstimateAmount(), 2, RoundingMode.HALF_UP));
                    }
                    detailDto.setProjectCode(parentCode);
                    detailDto.setProjectName(childList.get(0).getParentBudgetSubjectsName());
                    detailDto.setSort(index.getAndIncrement());
                    detailDtoList.add(detailDto);
                });
        detailDtoList.forEach(detailDto -> {
            if (Objects.isNull(detailDto.getSort())) {
                RegionCollectProjectEnum collectProjectEnum = RegionCollectProjectEnum.findByCode(detailDto.getProjectCode());
                if (Objects.nonNull(collectProjectEnum)) {
                    detailDto.setSort(collectProjectEnum.getSort());
                } else {
                    detailDto.setSort(index.getAndIncrement());
                }
            }
        });
        List<MarketingPlanEstimationVo> detailSendDtoList = detailDtoList.stream().sorted(Comparator.comparingInt(MarketingPlanEstimationVo::getSort)).collect(Collectors.toList());


        MarketPlanChangeMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(order, MarketPlanChangeMainDto.class, LinkedHashSet.class, ArrayList.class);
        dto.setRequestName("营销方案变更：" + order.getSchemeName());
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));
        List<MarketPlanChangeCompareDetailDto> detailDtoList1 = (List<MarketPlanChangeCompareDetailDto>) nebulaToolkitService.copyCollectionByBlankList(order.getGainsAndLossesComparsionList(), MarketPlanChangeGainsAndLossesVo.class, MarketPlanChangeCompareDetailDto.class, LinkedHashSet.class, ArrayList.class);
        List<RegionCollectDetailDto> detailDtoList2 = (List<RegionCollectDetailDto>) nebulaToolkitService.copyCollectionByBlankList(detailSendDtoList, MarketingPlanEstimationVo.class, RegionCollectDetailDto.class, LinkedHashSet.class, ArrayList.class);

        //查询二级科目关联的活动大类
        List<CostTypeCategoryVo> costTypeCategorySecondVos = costTypeCategoryVoService.findListReleaseSecondBudgetSubjectCodes();
        //key-二级关联科目
        Map<String, List<String>> costTypeCategoryMap = costTypeCategorySecondVos.stream().collect(Collectors.groupingBy(x -> x.getBudgetSubjectsName(), Collectors.mapping(CostTypeCategoryVo::getCategoryCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : costTypeCategoryMap.entrySet()) {
            detailCaseList.stream().filter(x -> entry.getValue().contains(x.getCategoryCode()))
                    .filter(Objects::nonNull).forEach(e -> e.setCostTypeCategorySecondName(entry.getKey()));
        }
        List<CostCenterDetailDto> costCenterDetailDtoList = Lists.newArrayList();
        Set<String> bearDepartmentNameSet = new HashSet<>();

        detailCaseList.stream().filter(k -> org.apache.commons.lang3.StringUtils.isNotBlank(k.getCostCenterCode()) && StringUtils.isNotBlank(k.getCostTypeCategorySecondName()))
                .collect(Collectors.groupingBy(e -> e.getCostCenterCode() + e.getCostTypeCategorySecondName(), Collectors.toList())).forEach((key, list) -> {
            CostCenterDetailDto detailDto = new CostCenterDetailDto();
            detailDto.setCostCenterCode(list.get(0).getCostCenterCode());
            detailDto.setCostCenterName(list.get(0).getCostCenterName());
            List<OrgOaOrgVo> orgOaOrgVos = orgOaOrgVoMap.get(list.get(0).getBearDepartmentCode());
            if (CollectionUtils.isEmpty(orgOaOrgVos)) {
                bearDepartmentNameSet.add(list.get(0).getBearDepartmentName());
            } else {
                detailDto.setXzjghtybzs(orgOaOrgVos.get(0).getOaOrgCode());
            }
            detailDto.setSecondaryCost(list.get(0).getCostTypeCategorySecondName());
            detailDto.setBearAmount(list.stream().map(MarketingPlanCaseVo::getApplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            costCenterDetailDtoList.add(detailDto);
        });
        Validate.isTrue(CollectionUtils.isEmpty(bearDepartmentNameSet), "【%s】未找到对应的OA组织", bearDepartmentNameSet);

        List<MarketPlanCaseDetailDto> marketPlanCaseDetailDtoList = Lists.newArrayList();
        detailCaseList.forEach(e -> {
            MarketPlanCaseDetailDto detailDto = new MarketPlanCaseDetailDto();
            detailDto.setFysybm(e.getBelongDepartmentName());
            detailDto.setKhmc(e.getCustomerName());
            detailDto.setMdmc(e.getTerminalName());
            detailDto.setFyxm(e.getDetailName());
            detailDto.setFygzyf(e.getYears());
            detailDto.setSqje(e.getApplyAmount());
            detailDto.setFaghmc(e.getSchemeName());
            detailDto.setHdms(e.getActDesc());
            detailDto.setFamxbm(e.getSchemeDetailCode());
            detailDto.setCbzx(e.getCostCenterName());
            detailDto.setXzjghtybzs(orgOaOrgVoMap.get(e.getBearDepartmentCode()).get(0).getOaOrgCode());
            MarketingPlanCaseVo planCaseVo = changeMap.get(e.getOriginalSchemeDetailCode());
            detailDto.setBgqje(planCaseVo == null ? BigDecimal.ZERO : planCaseVo.getApplyAmount());
            detailDto.setBghje(e.getApplyAmount());
            marketPlanCaseDetailDtoList.add(detailDto);
        });

        OaDetailDto oaDetailDto1 = new OaDetailDto();
        oaDetailDto1.setDetailList(JSONUtil.toJsonStr(detailDtoList1));
        oaDetailDto1.setDetailClass(MarketPlanChangeCompareDetailDto.class);
        OaDetailDto oaDetailDto2 = new OaDetailDto();
        oaDetailDto2.setDetailList(JSONUtil.toJsonStr(detailDtoList2));
        oaDetailDto2.setDetailClass(RegionCollectDetailDto.class);
        OaDetailDto costCenterDetailDto = new OaDetailDto();
        costCenterDetailDto.setDetailList(JSONUtil.toJsonStr(costCenterDetailDtoList));
        costCenterDetailDto.setDetailClass(CostCenterDetailDto.class);
        OaDetailDto oaDetailDto3 = new OaDetailDto();
        oaDetailDto3.setDetailList(JSONUtil.toJsonStr(marketPlanCaseDetailDtoList));
        oaDetailDto3.setDetailClass(MarketPlanCaseDetailDto.class);
        dto.setDetailList(Arrays.asList(oaDetailDto1, oaDetailDto2, costCenterDetailDto, oaDetailDto3));

        if (ryOaProcessService.resubmit(dto)) {
            MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, order.getSchemeCode());
            entity.setProcessDate(new Date());
            entity.setProcessStatus(ProcessStatusEnum.COMMIT.getDictCode());
            marketingPlanRepository.updateById(entity);
        }
        return null;
    }

    /**
     * OA撤回
     *
     * @param code
     * @param remark
     * @return
     */
    @Override
    public boolean oaWithdraw(String code, String remark) {
        MarketingPlan entity = marketingPlanRepository.queryByIdOrSchemeCode(null, code);
        Validate.isTrue(entity.getProcessStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(entity.getProcessNumber()));
        dto.setProcessCreateId(entity.getOaId());
        dto.setUserName(entity.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields, orderJsonObject) -> {
        for (MarketingPlanFieldsEnum value : MarketingPlanFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod1 = (tableDetailFields, detailJsonObject) -> {
        for (MarketPlanChangeFieldsEnum value : MarketPlanChangeFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod2 = (tableDetailFields, detailJsonObject) -> {
        for (MarketingEstimationFieldsEnum value : MarketingEstimationFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod3 = (tableDetailFields, detailJsonObject) -> {
        for (CostCenterFieldsEnum value : CostCenterFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod4 = (tableDetailFields, detailJsonObject) -> {
        for (MarketPlanCaseFieldsEnum value : MarketPlanCaseFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
}
