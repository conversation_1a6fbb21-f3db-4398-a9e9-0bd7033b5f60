package com.biz.crm.tpm.business.activities.overallplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "BearOverallPlanCaseVo")
public class BearOverallPlanCaseVo {

    @ApiModelProperty("方案类型")
    private String schemeType;

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("方案主题")
    private String schemeTheme;

    @ApiModelProperty("二级费用大类名称")
    private String secondCostCategoryName;

    @ApiModelProperty("预算科目编码")
    private String budgetCode;

    @ApiModelProperty("预算科目名称")
    private String budgetName;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    private String categoryName;

    @ApiModelProperty("活动细类编码")
    private String detailCode;

    @ApiModelProperty("活动细类名称")
    private String detailName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("预估费用")
    private BigDecimal applyAmount;

    @ApiModelProperty("已承接金额")
    private BigDecimal alreadyBearAmount;

    @ApiModelProperty("本次承接金额")
    private BigDecimal bearAmount;

    @ApiModelProperty("兑付条件及说明")
    private String cashCondition;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;
}
