package com.biz.crm.tpm.business.activities.marketingplan.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.marketingplan.dto.PlanExecutionSumEditDto;
import com.biz.crm.tpm.business.activities.marketingplan.service.PlanExecutionSumService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanExecutionSumDetailVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.PlanExecutionSumVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Author: yangrui
 * @Date: 2025-01-02 15:45
 */
@Slf4j
@RestController
@RequestMapping("/v1/planExecutionSum")
@Api(tags = "终端活动执行汇总")
public class PlanExecutionSumController {

    @Resource
    private PlanExecutionSumService planExecutionSumService;

    @GetMapping("findByCondition")
    public Result<Page<PlanExecutionSumVo>> queryPage(
            @PageableDefault(50) Pageable pageable, PlanExecutionSumVo param) {
        try {
            return Result.ok(planExecutionSumService.queryPage(pageable, param));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("queryDetail")
    public Result<PlanExecutionSumDetailVo> queryDetail(@RequestParam(value = "id") String id) {
        try {
            return Result.ok(planExecutionSumService.queryDetail(id));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("edit")
    public Result<?> edit(@RequestBody @ApiParam(
            name = "dto", value = "执行汇总编辑入参") PlanExecutionSumEditDto dto) {
        try {
            planExecutionSumService.edit(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
