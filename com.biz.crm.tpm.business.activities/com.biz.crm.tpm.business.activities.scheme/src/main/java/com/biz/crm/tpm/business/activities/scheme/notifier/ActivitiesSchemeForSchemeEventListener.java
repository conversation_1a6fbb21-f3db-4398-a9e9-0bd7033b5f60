package com.biz.crm.tpm.business.activities.scheme.notifier;

import com.biz.crm.tpm.business.activities.scheme.event.SchemeEventListener;
import com.biz.crm.tpm.business.activities.scheme.service.ActivitiesSchemeVoService;
import com.biz.crm.tpm.business.activities.scheme.vo.SchemeVo;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>方案活动针对方案的删除进行监听
 *
 * <AUTHOR>
 * @date 2022/6/21
 */
@Component
public class ActivitiesSchemeForSchemeEventListener implements SchemeEventListener {
  @Autowired
  private ActivitiesSchemeVoService activitiesSchemeVoService;

  @Override
  public void onCreated(SchemeVo schemeVo) {
    // 无业务
  }

  @Override
  public void onUpdate(SchemeVo oldSchemeVo, SchemeVo schemeVo) {
    int count = activitiesSchemeVoService.countByScheme(schemeVo.getSchemeCode());
    Validate.isTrue(count == 0, "方案【%s】已被使用，无法编辑！", schemeVo.getSchemeCode());
  }

  @Override
  public void onDeleted(SchemeVo schemeVo) {
    int count = activitiesSchemeVoService.countByScheme(schemeVo.getSchemeCode());
    Validate.isTrue(count == 0, "方案【%s】已被使用，无法删除！", schemeVo.getSchemeCode());
  }
}
