package com.biz.crm.tpm.business.activities.scheme.repository;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import com.biz.crm.tpm.business.activities.scheme.entity.SchemeProduct;
import com.biz.crm.tpm.business.activities.scheme.mapper.SchemeProductMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.util.List;
import java.util.Collections;

/**
 * 方案产品;(tpm_scheme_product)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-31
 */
@Component
public class SchemeProductRepository extends ServiceImpl<SchemeProductMapper, SchemeProduct> {
  @Autowired
  private SchemeProductMapper schemeProductMapper;

  /**
   * 根据方案编号获取详情集合
   *
   * @param schemeCode 编号集合
   * @return List<SchemeProduct>
   */
  public List<SchemeProduct> findBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(SchemeProduct::getSchemeCode, schemeCode)
            .eq(SchemeProduct::getTenantCode, tenantCode)
            .list();
  }

  /**
   * 根据方案编号删除附件数据
   *
   * @param schemeCode
   */
  public void deleteBySchemeCode(String schemeCode) {
    if (StringUtils.isBlank(schemeCode)) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    this.lambdaUpdate().eq(SchemeProduct::getSchemeCode, schemeCode).eq(SchemeProduct::getTenantCode, tenantCode).remove();
  }

}