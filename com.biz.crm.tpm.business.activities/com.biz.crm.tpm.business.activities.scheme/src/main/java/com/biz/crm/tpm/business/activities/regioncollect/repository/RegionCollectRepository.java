package com.biz.crm.tpm.business.activities.regioncollect.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.regioncollect.entity.RegionCollect;
import com.biz.crm.tpm.business.activities.regioncollect.mapper.RegionCollectMapper;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectVo;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class RegionCollectRepository extends ServiceImpl<RegionCollectMapper, RegionCollect> {

    @Autowired
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 分页查询
     *
     * @param page
     * @param vo
     * @return
     */
    public Page<RegionCollectVo> findList(Page<RegionCollectVo> page, RegionCollectVo vo) {
        return this.baseMapper.findList(page, vo);
    }

    /**
     * 调用此方法必须有一个值不能为空
     *
     * @param id
     * @param collectCode
     * @return
     */
    public RegionCollect queryByIdOrCollectCode(String id, String collectCode) {
        if (StringUtils.isBlank(id) && StringUtils.isBlank(collectCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(id), RegionCollect::getId, id)
                .eq(ObjectUtils.isNotEmpty(collectCode), RegionCollect::getCollectCode, collectCode)
                .eq(RegionCollect::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(RegionCollect::getTenantCode, TenantUtils.getTenantCode()).one();
    }

    /**
     * 查询
     *
     * @param idList
     * @return
     */
    public List<RegionCollect> findListByIds(List<String> idList) {
        return this.lambdaQuery()
                .in(RegionCollect::getId, idList)
                .eq(RegionCollect::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(RegionCollect::getTenantCode, TenantUtils.getTenantCode()).list();
    }


    public RegionCollect findByOrgCodeAndYears(String orgCode, String years) {
        return this.lambdaQuery()
                .eq(RegionCollect::getOrgCode, orgCode)
                .eq(RegionCollect::getYears, years)
                .eq(RegionCollect::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(RegionCollect::getTenantCode, TenantUtils.getTenantCode()).one();
    }

    public void updateExecuteMsg(String id, String msg, String status) {
        this.lambdaUpdate()
                .eq(RegionCollect::getId, id)
                .set(RegionCollect::getCollectStatus, status)
                .set(RegionCollect::getCollectResult, msg).update();
    }

    public List<RegionCollectVo> findCommit(String yearMonthLy) {
        return baseMapper.findCommit(yearMonthLy);
    }

    public RegionCollect findByYearsAndOrgCode(String years, String orgCode) {
        return this.lambdaQuery()
                .eq(RegionCollect::getYears, years)
                .eq(RegionCollect::getOrgCode, orgCode)
                .in(RegionCollect::getProcessStatus, ProcessStatusEnum.PASS.getDictCode())
                .eq(RegionCollect::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(RegionCollect::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }


    public List<MarketingPlanCaseVo> findRegionMarketingCaseList(String collectCode) {
        return this.baseMapper.findRegionMarketingCaseList(collectCode);
    }
}
