package com.biz.crm.tpm.business.activities.stagingscheme.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.activities.stagingscheme.entity.TpmStagingSalesPlanEntity;
import com.biz.crm.tpm.business.activities.stagingscheme.mapper.TpmStagingSalesPlanMapper;
import org.springframework.stereotype.Component;


@Component
public class TpmStagingSalesPlanRepository extends ServiceImpl<TpmStagingSalesPlanMapper, TpmStagingSalesPlanEntity> {

    public void removeStagingSalesPlan(String schemeCode) {
        lambdaUpdate().eq(TpmStagingSalesPlanEntity::getReleaseId, schemeCode).remove();
    }
}
