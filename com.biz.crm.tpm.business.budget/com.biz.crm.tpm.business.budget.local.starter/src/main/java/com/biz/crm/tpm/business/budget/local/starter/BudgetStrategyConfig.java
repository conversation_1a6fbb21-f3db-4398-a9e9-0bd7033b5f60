package com.biz.crm.tpm.business.budget.local.starter;

import com.biz.crm.tpm.business.budget.local.strategy.controltype.DoNotControlTypeStrategy;
import com.biz.crm.tpm.business.budget.local.strategy.controltype.MonthControlTypeStrategy;
import com.biz.crm.tpm.business.budget.local.strategy.controltype.QuarterControlTypeStrategy;
import com.biz.crm.tpm.business.budget.local.strategy.controltype.YearControlTypeStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 描述：</br>预算科目控制类型配置类
 *
 * <AUTHOR>
 * @date 2022/5/18
 */
@Configuration
public class BudgetStrategyConfig {

  /**
   * 预算科目控制，预算不控制策略
   *
   * @return
   */
  @Bean
  public DoNotControlTypeStrategy getDoNotControlTypeStrategy() {
    return new DoNotControlTypeStrategy();
  }

  /**
   * 预算科目控制，预算全年控制策略
   *
   * @return
   */
  @Bean
  public YearControlTypeStrategy getYearControlTypeStrategy() {
    return new YearControlTypeStrategy();
  }

  /**
   * 预算科目控制，预算季度控制策略
   *
   * @return
   */
  @Bean
  public QuarterControlTypeStrategy getQuarterControlTypeStrategy() {
    return new QuarterControlTypeStrategy();
  }

  /**
   * 预算科目控制，预算月份控制策略
   *
   * @return
   */
  @Bean
  public MonthControlTypeStrategy getMonthControlTypeStrategy() {
    return new MonthControlTypeStrategy();
  }
}
