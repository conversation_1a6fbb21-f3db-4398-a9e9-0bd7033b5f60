package com.biz.crm.tpm.business.budget.local.starter;

import com.biz.crm.tpm.business.budget.sdk.strategy.payby.CashPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.DiscountPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.RestockPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.TransferPayByStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 描述：</br>支付方式配置类
 *
 * <AUTHOR>
 * @date 2022/5/18
 */
@Configuration
public class BudgetPayByStrategyConfig {

  /**
   * 支付方式，现金支付方式策略
   *
   * @return
   */
  @Bean
  public CashPayByStrategy getCashPayByStrategy() {
    return new CashPayByStrategy();
  }

  /**
   * 支付方式，转预付款/账扣支付方式策略
   *
   * @return
   */
  @Bean
  public TransferPayByStrategy getTransferPayByStrategy() {
    return new TransferPayByStrategy();
  }

  /**
   * 支付方式，货补方式策略
   *
   * @return
   */
  @Bean
  public RestockPayByStrategy getRestockPayByStrategy() {
    return new RestockPayByStrategy();
  }

  /**
   * 支付方式，折扣支付方式策略
   *
   * @return
   */
  @Bean
  public DiscountPayByStrategy getDiscountPayByStrategy() {
    return new DiscountPayByStrategy();
  }
}
