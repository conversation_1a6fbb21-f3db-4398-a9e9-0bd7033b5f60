package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "CostBudgetVo", description = "费用预算vo")
public class CostBudgetVo extends UuidFlagOpVo {
    private static final long serialVersionUID = 2392902547153789737L;
    @ApiModelProperty("编码")
    private String code;

    /**
     * 参见com.biz.tpm.business.budget.sdk.enums.CostBudgetType
     */
    @ApiModelProperty("费用预算类型")
    private String type;

    @ApiModelProperty("年")
    private Integer year;

    @ApiModelProperty("季度")
    private Integer quarter;

    @ApiModelProperty("月份")
    private Integer month;

    @ApiModelProperty("预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    private String budgetSubjectName;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("门店编码")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    private String terminalName;

    @ApiModelProperty("产品层级编码")
    private String productLevelCode;

    @ApiModelProperty("产品层级名称")
    private String productLevelName;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("期初金额")
    private BigDecimal initialAmount;

    @ApiModelProperty("最终可用余额")
    private BigDecimal finalBalance;

    @ApiModelProperty("租户编码")
    private String tenantCode;

    @ApiModelProperty(name = "科目预算控制类型是否为不控制")
    private boolean doNot;


    @ApiModelProperty("预算类型")
    private String budgetType;

    @ApiModelProperty("年月")
    private String yearMonthLy;

    @ApiModelProperty("确认状态")
    private String confirmStatus;

    @ApiModelProperty("一级部门编码")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称")
    private String departmentOneName;

    @ApiModelProperty("部门层级")
    private Integer levelNum;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("唯一标识")
    private String uniqueKey;

    @ApiModelProperty("调整金额")
    private BigDecimal adjustAmount;

    @ApiModelProperty("调整后余额")
    private BigDecimal adjustBalanceAmount;

    @ApiModelProperty("月度余额")
    private BigDecimal monthBalanceAmount;

    @ApiModelProperty("冻结金额")
    private BigDecimal freezeAmount;

    @ApiModelProperty("已使用金额")
    private BigDecimal usedAmount;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("关联业务编码")
    private String businessCode;

    /**
     * 客户ERP编码
     */
    @ApiModelProperty("客户ERP编码")
    private String erpCode;

    /**
     * 产品组编码
     */
    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("活动大类编码")
    private String categoryCode;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("税率")
    private String taxRateStr;

    @ApiModelProperty("未税")
    private BigDecimal noTaxAdjustBalanceAmount;
}
