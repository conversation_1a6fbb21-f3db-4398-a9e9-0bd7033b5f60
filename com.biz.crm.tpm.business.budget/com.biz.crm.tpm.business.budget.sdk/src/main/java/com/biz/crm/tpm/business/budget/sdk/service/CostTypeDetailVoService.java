package com.biz.crm.tpm.business.budget.sdk.service;

import com.biz.crm.tpm.business.budget.sdk.strategy.payby.PayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailsDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * TPM-活动明细;(tpm_cost_type_details)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
public interface CostTypeDetailVoService {

    /**
     * 生成操作标记
     */
    String preSave();

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    Page<CostTypeDetailVo> findByConditions(Pageable pageable, CostTypeDetailsDto dto);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    CostTypeDetailVo findById(String id);

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    CostTypeDetailVo findByCode(String code);

    /**
     * 通过编号集合查询集合数据
     *
     * @param code 编号
     * @return 单条数据
     */
    List<CostTypeDetailVo> findByCodes(List<String> code);

    /**
     * 新增数据
     *
     * @param costTypeDetailVo 实体对象
     * @return 新增结果
     */
    CostTypeDetailVo create(CostTypeDetailVo costTypeDetailVo);

    /**
     * 修改新据
     *
     * @param costTypeDetailVo 实体对象
     * @return 修改结果
     */
    CostTypeDetailVo update(CostTypeDetailVo costTypeDetailVo);

    /**
     * 删除数据
     *
     * @param ids 主键结合
     */
    void delete(List<String> ids);

    /**
     * 通过启用状态查询数据
     *
     * @param enableStatus 状态
     * @return 集合数据
     */
    List<CostTypeDetailVo> findByEnableStatus(String enableStatus);

    /**
     * 批量根据id启用
     *
     * @param ids
     */
    void enable(List<String> ids);

    /**
     * 批量根据id禁用
     *
     * @param ids
     */
    void disable(List<String> ids);

    /**
     * 根据活动大类查询活动细类信息
     *
     * @param categoryCode
     * @return
     */
    Set<CostTypeDetailVo> findByCategoryCode(String categoryCode);


    /**
     * 根据活动示例编号查询是否有细类已经关联该示例
     *
     * @param code
     * @return
     */
    boolean existByApprovalCollect(String code);

    /**
     * 查询所有支付方式
     *
     * @return
     */
    List<PayByStrategy> findAllPayBy();

    /**
     * 根据条件查询活动细类编号
     *
     * @param dto
     * @return
     */
    Set<String> findCodeByCondition(CostTypeDetailsDto dto);

    List<CostTypeDetailVo> findListByNames(List<String> nameList);

    List<CostTypeDetailVo> findListByPushSfa();

    /**
     *
     * 通过活动大类编码查
     *
     * @param codes
     * @return
     */
    List<CostTypeDetailVo> findByCategoryCodes(List<String> codes);

    /**
     * 通过活动细类查询采集、结案资料
     * @param codes
     * @return
     */
    Map<String,List<CostTypeDetailCollectVo>> findCollectByCodes(List<String> codes);

    /**
     * 通过条件查询活动细类
     * @param dto
     * @return
     */
    List<String> findListByCondition(CostTypeDetailsDto dto);

    Map<String,Set<String>> findPayBysByDetailCodes(List<String> detailCodes);
}