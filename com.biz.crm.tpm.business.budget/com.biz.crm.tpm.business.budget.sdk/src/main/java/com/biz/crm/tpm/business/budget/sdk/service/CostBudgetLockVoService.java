package com.biz.crm.tpm.business.budget.sdk.service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 预算加锁
 */
public interface CostBudgetLockVoService {

    /**
     * 根据预算编码加锁
     *
     * @param budgetCode
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 14:21
     **/
    boolean lock(String budgetCode, TimeUnit timeUnit, int time);

    /**
     * 根据预算编码批量加锁
     *
     * @param budgetCodeList
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 14:21
     **/
    boolean lock(List<String> budgetCodeList, TimeUnit timeUnit, int time);

    /**
     * 根据预算编码批量加锁
     *
     * @param budgetCodeList
     * @param timeUnit
     * @param lockTime
     * @param waiteTime
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 14:21
     **/
    boolean lock(List<String> budgetCodeList, TimeUnit timeUnit, int lockTime, int waiteTime);

    /**
     * 解锁
     *
     * @param budgetCode
     * <AUTHOR>
     * @date 2022/11/1 14:46
     **/
    void unLock(String budgetCode);

    /**
     * 批量解锁
     *
     * @param budgetCodeList
     * <AUTHOR>
     * @date 2022/11/1 14:46
     **/
    void unLock(List<String> budgetCodeList);
}
