package com.biz.crm.tpm.business.control.sdk.enums;

import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 时间维度
 */
public enum TimeDimensionEnum {
    YEAR("year","年度累计"),
    MONTH("month","月度累计");

    TimeDimensionEnum(String code, String descr){
        this.code = code;
        this.descr = descr;
    }

    private String code;

    private String descr;

    public static TimeDimensionEnum findByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for(TimeDimensionEnum type : values()){
            if(StringUtils.equals(type.getCode(),code)){
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }
}
