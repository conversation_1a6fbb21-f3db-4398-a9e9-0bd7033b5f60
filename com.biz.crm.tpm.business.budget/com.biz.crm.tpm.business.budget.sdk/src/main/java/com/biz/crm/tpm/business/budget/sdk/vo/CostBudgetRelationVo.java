package com.biz.crm.tpm.business.budget.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 描述：</br>费用预算相关信息vo
 *
 * <AUTHOR>
 * @date 2022/6/10
 */
@Setter
@Getter
@ApiModel(value = "CostBudgetRelationVo", description = "费用预算相关信息vo")
public class CostBudgetRelationVo implements Serializable {
  /** 费用预算编号  */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编号", value= "费用预算编号")
  private String costBudgetCode;
  /** 预算科目编号  */
  @ApiModelProperty(name = "BudgetSubjectsCode",notes = "预算科目编号", value= "预算科目编号")
  private String budgetSubjectsCode;
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value= "预算科目名称")
  private String budgetSubjectsName;
  /** 活动大类编号  */
  @ApiModelProperty(name = "categoryCode",notes = "活动大类编号", value= "活动大类编号")
  private String categoryCode;
  /** 活动大类名称 */
  @ApiModelProperty(name = "categoryName",notes = "活动大类名称", value= "活动大类名称")
  private String categoryName;
  /** 唯一键值 */
  @ApiModelProperty(name = "uniqueKey",notes = "唯一键值", value= "唯一键值")
  private String uniqueKey;
}
