package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingExecutorOprtType;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType.AUDIT;

@Component
public class AuditStrategySettingExecutor implements BusinessStrategySettingExecutor {
  @Override
  public boolean matchedOprtType(List<StrategySettingStruct> settingStrategies, String oprtType, Object standardValue) {
    if(CollectionUtils.isEmpty(settingStrategies) || StringUtils.isBlank(oprtType)){
      return false;
    }
    String strategySettingCode = this.transferOprtType(oprtType);
    if(StringUtils.isBlank(strategySettingCode)){
      return false;
    }
    List<StrategySettingStruct> allStructs = Lists.newArrayList();
    for(StrategySettingStruct struct : settingStrategies){
      this.flatStrategySettingStruct(allStructs,struct);
    }

    StrategySettingStruct costTypeDetailSettingStrategyVo = allStructs.stream().filter(e -> StringUtils.equals(e.getType(),AUDIT.name()) && StringUtils.equals(strategySettingCode,e.getCode())).findFirst().orElse(null);
    if(costTypeDetailSettingStrategyVo == null){
      return false;
    }
    return matchedValue(costTypeDetailSettingStrategyVo,standardValue);
  }

  @Override
  public Object getValueByOprtType(List<StrategySettingStruct> settingStrategies, String oprtType) {
    if(CollectionUtils.isEmpty(settingStrategies) || StringUtils.isBlank(oprtType)){
      return null;
    }
    String strategySettingCode = this.transferOprtType(oprtType);
    if(StringUtils.isBlank(strategySettingCode)){
      return null;
    }
    settingStrategies = settingStrategies.stream().filter(e -> StringUtils.equals(e.getType(),AUDIT.name())).collect(Collectors.toList());
    return this.getValue(settingStrategies,strategySettingCode);
  }

  @Override
  public String executorName() {
    return AUDIT.name();
  }

  private String transferOprtType(String oprtType){
    StrategySettingExecutorOprtType strategySettingExecutorOprtType = StrategySettingExecutorOprtType.getOprtType(oprtType);
    if(strategySettingExecutorOprtType == null){
      return null;
    }
    switch (strategySettingExecutorOprtType){
      case IS_AUDIT:
        return AuditSetting.class.getSimpleName();
      case AUTO_AUDIT:
        return AutoAuditSetting.class.getSimpleName();
      case VALIDITY_TIME_AUDIT:
        return AuditEffectiveDateSetting.class.getSimpleName();
      case EXCEEDING_AUDIT_RATE:
        return AuditRatioSetting.class.getSimpleName();
      case MULTIPLE_AUDIT:
        return AuditMulitSetting.class.getSimpleName();
      default:
        throw new IllegalArgumentException("未知的策略配置操作类型【" + oprtType + "】，请检查");
    }
  }

}
