package com.biz.crm.tpm.business.budget.sdk.event;


import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;

/**
 * TPM-活动大类;(tpm_cost_type_category)相关的事件通知
 *
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
public interface CostTypeCategoryEventListener {

  /**
   * 当TPM-活动大类数据被创建时，该事件被触发
   *
   * @param costTypeCategoryVo
   */
  default void onCreated(CostTypeCategoryVo costTypeCategoryVo){};

  /**
   * 当TPM-活动大类数据被修改时，该事件被触发
   *
   * @param oldCostTypeCategoryVo 修改前数据
   * @param costTypeCategoryVo    修改后数据
   */
  default void onUpdate(CostTypeCategoryVo oldCostTypeCategoryVo, CostTypeCategoryVo costTypeCategoryVo){};

  /**
   * 当TPM-活动大类数据被删除时（逻辑删除），该事件被触发
   *
   * @param costTypeCategoryVo
   */
  default void onDeleted(CostTypeCategoryVo costTypeCategoryVo){};

  /**
   * 当TPM-活动大类数据被启用时，该事件被触发
   *
   * @param costTypeCategoryVo
   */
  default void onEnable(CostTypeCategoryVo costTypeCategoryVo){};

  /**
   * 当TPM-活动大类数据被禁用时，该事件被触发
   *
   * @param costTypeCategoryVo
   */
  default void onDisable(CostTypeCategoryVo costTypeCategoryVo){};
}