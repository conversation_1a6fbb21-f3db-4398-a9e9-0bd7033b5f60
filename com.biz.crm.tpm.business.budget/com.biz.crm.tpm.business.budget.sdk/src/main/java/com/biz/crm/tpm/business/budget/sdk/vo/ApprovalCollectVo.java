package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ApprovalCollectVo", description = "核销采集vo")
public class ApprovalCollectVo extends TenantFlagOpVo {
  @ApiModelProperty("编码")
  private String code;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("排序(值越小越靠前)")
  private Integer sortIndex;

  @ApiModelProperty("描述")
  private String descr;

  @ApiModelProperty("核销采集图片")
  private List<ApprovalCollectImageVo> images;

  @ApiModelProperty("类型信息")
  private List<ApprovalCollectTypeVo> typeList;
}
