package com.biz.crm.tpm.business.adjust.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2023-06-13 18:25
 * @description：
 */
@Data
@CrmExcelImport(startRow = 4)
public class BudgetAdjustImportVo extends CrmExcelVo {

  @CrmExcelColumn("操作名称*")
  private String adjustName;

  @CrmExcelColumn("备注")
  private String remark;

  @CrmExcelColumn("预算年月*")
  private String yearMonthLy;

  @CrmExcelColumn("公司编码*")
  private String companyCode;

  @CrmExcelColumn("部门编码*")
  private String departmentOneCode;

  @CrmExcelColumn("部门名称")
  private String departmentOneName;

//  @CrmExcelColumn("部门层级*")
//  private String levelNumStr;
  private Integer levelNum;

  @CrmExcelColumn("成本中心编码")
  private String costCenterCode;

  @CrmExcelColumn("成本中心名称")
  private String costCenterName;

//  @CrmExcelColumn("预算科目编码*")
  private String budgetSubjectCode;

  @CrmExcelColumn("预算科目名称*")
  private String budgetSubjectName;

  @CrmExcelColumn("客户编码")
  private String customerCode;

  @CrmExcelColumn("客户名称")
  private String customerName;

  @CrmExcelColumn("品项名称")
  private String itemName;

  @CrmExcelColumn("产品编码")
  private String productCode;

  @CrmExcelColumn("产品名称")
  private String productName;

  @CrmExcelColumn("操作类型*")
  private String operateType;

  @CrmExcelColumn("操作金额*")
  private String adjustAmountStr;

  @CrmExcelColumn("明细备注")
  private String remarkDetail;


  /**
   * 预算编码
   */
  private String budgetCode;

}
