package com.biz.crm.tpm.business.budget.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 描述：</br>表单策略属性参数传递dto
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@ApiModel(value = "FormStrategyPropertiesDto",description = "TPM-表单策略属性参数传递dto")
@Getter
@Setter
public class FormStrategyPropertiesDto {

  /**
   * 策略编号
   */
  @ApiModelProperty("策略编号")
  private String code;

  /**
   * 关联业务编号
   */
  @ApiModelProperty("关联业务编号")
  private String businessCode;

  /**
   * json格式数据
   */
  @ApiModelProperty("json格式数据")
  private String data;
}
