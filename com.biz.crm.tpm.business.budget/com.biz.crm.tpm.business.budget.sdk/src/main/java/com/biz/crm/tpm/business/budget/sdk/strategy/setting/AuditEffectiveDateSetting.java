package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.NUMBER;

@Component
public class AuditEffectiveDateSetting extends AbstractStrategySetting {

  @Autowired
  private AutoAuditSetting autoAuditSetting;

  public AuditEffectiveDateSetting() {
    super("核销有效期(月)", AuditEffectiveDateSetting.class.getSimpleName());
    this.type = StrategySettingType.AUDIT.name();
    this.necessary = false;
    this.sortIndex = 3;
    this.valueType = NUMBER.getCode();
    this.parentCode = AutoAuditSetting.class.getSimpleName();
    this.tip = "核销有效期从活动结束时间开始生效，超过核销有效期的活动无法核销，不填写默认可一直核销";
  }

  public AbstractStrategySetting getParent() {
    return autoAuditSetting;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList();
  }

  @Override
  public Object getDefaultValue() {
    return null;
  }
}
