package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.BOOLEAN;

@Component
public class AuditSetting extends AbstractStrategySetting {

  @Autowired
  private AutoAuditSetting autoAuditSetting;


  public AuditSetting() {
    super("是否核销", AuditSetting.class.getSimpleName());
    this.type = StrategySettingType.AUDIT.name();;
    this.necessary = true;
    this.sortIndex = 1;
    this.valueType = BOOLEAN.getCode();
    this.tip = "勾选\"是\",则该活动细类的活动可在审批通过后执行核销申请操作";
  }


  public AbstractStrategySetting getParent() {
    return null;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList(autoAuditSetting);
  }

  @Override
  public Object getDefaultValue() {
    return true;
  }


}
