package com.biz.crm.tpm.business.adjust.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BudgetAdjustDetail", description = "预算调整明细")
public class BudgetAdjustDetailDto extends TenantFlagOpDto {

  @ApiModelProperty("调整编码")
  private String adjustCode;

  @ApiModelProperty("调整名称")
  private String adjustName;

  @ApiModelProperty("调整明细编码")
  private String adjustDetailCode;

  @ApiModelProperty("预算编码")
  private String budgetCode;

  @ApiModelProperty("预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("年月")
  private String yearMonthLy;

  @ApiModelProperty("事业部编码")
  private String divisionCode;

  @ApiModelProperty("事业部名称")
  private String divisionName;

  @ApiModelProperty("中心编码")
  private String centerCode;

  @ApiModelProperty("中心名称")
  private String centerName;

  @ApiModelProperty("一级部门编码")
  private String departmentOneCode;

  @ApiModelProperty("一级部门名称")
  private String departmentOneName;

  @ApiModelProperty("部门层级")
  private Integer levelNum;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("公司名称")
  private String companyName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("产品编码")
  private String productCode;

  @ApiModelProperty("产品名称")
  private String productName;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("调整金额")
  private BigDecimal adjustAmount;

  @ApiModelProperty("期初金额")
  private BigDecimal initialAmount;

  @ApiModelProperty("月度余额")
  private BigDecimal monthBalanceAmount;

  @ApiModelProperty("调整后余额")
  private BigDecimal finalAmount;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("变更类型")
  private String changeType;

}
