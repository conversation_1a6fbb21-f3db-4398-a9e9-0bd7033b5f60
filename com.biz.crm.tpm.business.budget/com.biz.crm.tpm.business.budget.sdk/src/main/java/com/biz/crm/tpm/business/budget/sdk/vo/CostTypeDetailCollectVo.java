package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 采集信息
 */
@ApiModel(value = "CostTypeDetailCollectVo",description = "采集信息")
@Getter
@Setter
public class CostTypeDetailCollectVo extends TenantFlagOpVo {

    /** 活动细类名称 */
    @ApiModelProperty(name = "活动细类名称",notes = "")
    private String detailName;

    /** 活动细类编号 */
    @ApiModelProperty(name = "活动细类编号",notes = "")
    private String detailCode;

    /** 采集示例名称 */
    @ApiModelProperty("采集示例名称")
    private String collectName;

    /** 采集示例编号 */
    @ApiModelProperty("采集示例编号")
    private String collectCode;

    /**
     * 参见com.biz.tpm.business.budget.sdk.enums.ApprovalCollectType
     */
    @ApiModelProperty("核销采集类型")
    private String type;
}
