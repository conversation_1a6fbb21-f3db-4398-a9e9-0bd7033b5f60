package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PublicShareRatio", description = "公摊费率")
public class PublicShareRatioVo extends TenantFlagOpVo {

    @ApiModelProperty("公摊费率编码")
    private String publicShareCode;

    @ApiModelProperty("年")
    private String yearStr;

    @ApiModelProperty("月")
    private String monthStr;

    @ApiModelProperty("部门编码")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("部门层级")
    private Integer levelNum;

    @ApiModelProperty("费率")
    private BigDecimal ratio;

    @ApiModelProperty("费率（百分比）")
    private String ratioStr;

    @ApiModelProperty("唯一标识")
    private String uniqueKey;

}
