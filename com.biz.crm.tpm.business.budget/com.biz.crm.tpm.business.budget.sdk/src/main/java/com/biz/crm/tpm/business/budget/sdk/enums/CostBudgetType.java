package com.biz.crm.tpm.business.budget.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 费用预算类型
 */
public enum CostBudgetType {
  ORG_BUDGET("department_budget","部门预算"),
  CUSTOMER_BUDGET("customer_budget","客户预算"),
  CHANNEL_BUDGET("channel_budget","渠道预算"),
  TERMINAL_BUDGET("terminal_budget","门店预算");

  CostBudgetType(String code, String descr){
    this.code = code;
    this.descr = descr;
  }

  private String code;

  private String descr;

  public static CostBudgetType findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    for(CostBudgetType type : values()){
      if(StringUtils.equals(type.getCode(),code)){
        return type;
      }
    }
    return null;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDescr() {
    return descr;
  }

  public void setDescr(String descr) {
    this.descr = descr;
  }
}
