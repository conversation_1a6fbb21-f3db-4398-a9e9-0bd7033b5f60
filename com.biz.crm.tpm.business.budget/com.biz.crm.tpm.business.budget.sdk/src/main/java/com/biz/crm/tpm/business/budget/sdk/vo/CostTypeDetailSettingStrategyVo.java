package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.business.common.sdk.vo.UuidVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "CostTypeDetailSettingStrategyVo",description = "TPM-活动细类与其关联的策略项")
@Getter
@Setter
public class CostTypeDetailSettingStrategyVo extends UuidVo {

   @ApiModelProperty(name = "关联的活动细类编码")
   private String detailCode;

   @ApiModelProperty(name = "关联的策略配置编码")
   private String settingManageCode;

   @ApiModelProperty(name = "策略配置明细名称")
   private String name;

   @ApiModelProperty(name = "策略配置明细code")
   private String code;

   @ApiModelProperty(name = "策略配置明细父级code")
   private String parentCode;

   @ApiModelProperty("策略项的类型")
   private String type;

   @ApiModelProperty("是否必需")
   private Boolean necessary;

   @ApiModelProperty("排序值(值越小越靠前)")
   private Integer sortIndex;

   @ApiModelProperty("策略项的值类型")
   private String valueType;

   @ApiModelProperty("是否显示")
   private Boolean display;

   @ApiModelProperty("策略项的值")
   private String value;

   @ApiModelProperty("策略项可能的默认值")
   private String defaultValue;
}