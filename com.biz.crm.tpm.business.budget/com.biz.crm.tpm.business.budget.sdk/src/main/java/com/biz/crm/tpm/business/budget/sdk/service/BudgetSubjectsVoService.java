package com.biz.crm.tpm.business.budget.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.sdk.dto.BudgetSubjectsDto;
import com.biz.crm.tpm.business.budget.sdk.strategy.BudgetControlTypeStrategy;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsCrmImportVo;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * TPM-预算科目;(tpm_budget_subjects)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-18
 */
public interface BudgetSubjectsVoService {

    /**
     * 生成操作标记
     */
    String preSave();

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    Page<BudgetSubjectsVo> findByConditions(Pageable pageable, BudgetSubjectsDto dto);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    BudgetSubjectsVo findById(String id);

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    BudgetSubjectsVo findByCode(String code);

    /**
     * 通过编号查询数据
     *
     * @param codes 编号
     * @return 单条数据
     */
    List<BudgetSubjectsVo> findByCodes(Set<String> codes);

    /**
     * 通过状态查询单条数据
     *
     * @param enableStatus 状态
     * @return 单条数据
     */
    List<BudgetSubjectsVo> findByEnableStatus(String enableStatus);

    /**
     * 新增数据
     *
     * @param budgetSubjectsVo 实体对象
     * @return 新增结果
     */
    BudgetSubjectsVo create(BudgetSubjectsVo budgetSubjectsVo);

    /**
     * 批量新增数据
     *
     * @param budgetSubjectsVoList 实体对象
     * @return 新增结果
     */
    void createBatch(List<BudgetSubjectsCrmImportVo> budgetSubjectsVoList);

    /**
     * 修改新据
     *
     * @param budgetSubjectsVo 实体对象
     * @return 修改结果
     */
    BudgetSubjectsVo update(BudgetSubjectsVo budgetSubjectsVo);

    /**
     * 删除数据
     *
     * @param ids 主键结合
     */
    void delete(List<String> ids);

    /**
     * 批量根据id启用
     *
     * @param ids
     */
    void enable(List<String> ids);

    /**
     * 批量根据id禁用
     *
     * @param ids
     */
    void disable(List<String> ids);

    /**
     * 根据预算科目编码获取对应的策略
     *
     * @param budgetSubjectCode 预算科目编码
     * @return 控制类型策略
     */
    BudgetControlTypeStrategy findBudgetControlTypeStrategyByCode(String budgetSubjectCode);

    /**
     * 根据控制类型编号获取对应的策略
     *
     * @param controlTypeCode 科目控制类型编码
     * @return 控制类型策略
     */
    BudgetControlTypeStrategy findBudgetControlTypeStrategyByControlTypeCode(String controlTypeCode);

    List<BudgetSubjectsVo> findListByBudgetSubjectNames(List<String> budgetSubjectNameList, String budgetSubjectLevel);

    List<BudgetSubjectsVo> findSecondBudgetSubject();

    Set<String> findAllParentSubjectCodesByCodes(String budgetSubjectCode);

    String findBudgetSubjectByDetailCode(String detailCode);

    List<BudgetSubjectsVo> findChildrensByCodes(List<String> budgetSubjectsCodes);

    List<BudgetSubjectsVo> findByCodeList(List<String> subjectCodes);
}