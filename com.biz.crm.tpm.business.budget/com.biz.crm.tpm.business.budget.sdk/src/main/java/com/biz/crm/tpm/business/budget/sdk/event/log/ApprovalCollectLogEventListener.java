package com.biz.crm.tpm.business.budget.sdk.event.log;

import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * <AUTHOR>
 * @describe: 核销 业务日志监听
 * @createTime 2022年06月23日 15:45:00
 */
public interface ApprovalCollectLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(ApprovalCollectLogEventDto eventDto);

  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(ApprovalCollectLogEventDto eventDto);

  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(ApprovalCollectLogEventDto eventDto);

  /**
   * 启用
   *
   * @param eventDto
   */
  void onUpdateEnable(ApprovalCollectLogEventDto eventDto);


}
