package com.biz.crm.tpm.business.budget.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 活动细类策略配置明细的值类型枚举(前端支持)
 */
public enum StrategySettingValueType {
  STRING("string","字符串类型"),
  DATE("date","日期类型"),
  NUMBER("number","数字类型"),
  ARRAY("array","数组类型"),
  OBJECT("object","对象类型"),
  BOOLEAN("boolean","布尔类型");

  StrategySettingValueType(String code, String descr){
    this.code = code;
    this.descr = descr;
  }

  private String code;

  private String descr;

  public static StrategySettingValueType findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    for(StrategySettingValueType type : values()){
      if(StringUtils.equals(type.getCode(),code)){
        return type;
      }
    }
    return null;
  }

  public static boolean contains(String value){
    if(StringUtils.isBlank(value)){
      return false;
    }
    for(StrategySettingValueType type : values()){
      if(StringUtils.equals(type.code,value)){
        return true;
      }
    }
    return false;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDescr() {
    return descr;
  }

  public void setDescr(String descr) {
    this.descr = descr;
  }
}
