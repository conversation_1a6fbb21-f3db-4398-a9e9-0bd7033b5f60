package com.biz.crm.tpm.business.budget.sdk.register;

import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormEventStrategy;
import org.springframework.core.Ordered;

import java.util.Collection;

/**
 * 描述：</br>表单维度信息
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
public interface FormDimension extends Ordered {

  /**
   * 表单维度编号（需要全局唯一）
   */
  String getCode();

  /**
   * 获取表单维度名称
   *
   * @return
   */
  String getName();


  /**
   * 关联的动态表单模块编码
   * @return
   */
  String getModelCode();

  /**
   * 获取表单事件触发处理策略
   *
   * @return
   */
  Collection<Class<? extends FormEventStrategy>> getFormEventStrategies();

}
