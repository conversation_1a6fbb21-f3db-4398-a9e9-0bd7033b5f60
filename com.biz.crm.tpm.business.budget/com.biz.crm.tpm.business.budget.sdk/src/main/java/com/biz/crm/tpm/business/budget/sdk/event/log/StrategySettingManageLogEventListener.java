package com.biz.crm.tpm.business.budget.sdk.event.log;

import com.biz.crm.tpm.business.budget.sdk.dto.StrategySettingManageLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

public interface StrategySettingManageLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   *
   * @param eventDto
   */
  void onCreate(StrategySettingManageLogEventDto eventDto);

  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(StrategySettingManageLogEventDto eventDto);

  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(StrategySettingManageLogEventDto eventDto);

  /**
   * 启用
   *
   * @param eventDto
   */
  void onUpdateEnable(StrategySettingManageLogEventDto eventDto);
}
