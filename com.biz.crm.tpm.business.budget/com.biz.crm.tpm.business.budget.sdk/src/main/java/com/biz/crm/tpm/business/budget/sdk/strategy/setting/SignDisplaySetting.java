package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.BOOLEAN;

@Component
public class SignDisplaySetting extends AbstractStrategySetting {

  @Autowired
  private PushSfaSetting pushSfaSettingStruct;

  public SignDisplaySetting() {
    super("是否签署陈列协议", SignDisplaySetting.class.getSimpleName());
    this.type = StrategySettingType.ACTIVITY.name();
    this.necessary = true;
    this.sortIndex = 3;
    this.valueType = BOOLEAN.getCode();
    this.parentCode = PushSfaSetting.class.getSimpleName();
    this.tip = "勾选\"是\",SFA端可执行签署协议操作";
  }


  public AbstractStrategySetting getParent() {
    return pushSfaSettingStruct;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList();
  }

  @Override
  public Object getDefaultValue() {
    return true;
  }
}
