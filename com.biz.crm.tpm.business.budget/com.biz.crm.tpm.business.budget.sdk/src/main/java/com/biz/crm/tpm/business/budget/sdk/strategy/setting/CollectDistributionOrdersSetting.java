package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.BOOLEAN;

@Component
public class CollectDistributionOrdersSetting extends AbstractStrategySetting {

  @Autowired
  private PushSfaSetting pushSfaSettingStruct;

  public CollectDistributionOrdersSetting() {
    super("是否采集分销订单", CollectDistributionOrdersSetting.class.getSimpleName());
    this.type = StrategySettingType.ACTIVITY.name();
    this.necessary = true;
    this.sortIndex = 2;
    this.valueType = BOOLEAN.getCode();
    this.parentCode = PushSfaSetting.class.getSimpleName();
    this.tip = "勾选\"是\",SFA端可执行下单操作";
  }

  public AbstractStrategySetting getParent() {
    return pushSfaSettingStruct;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList();
  }

  @Override
  public Object getDefaultValue() {
    return true;
  }

}
