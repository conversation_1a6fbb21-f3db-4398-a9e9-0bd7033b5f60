package com.biz.crm.tpm.business.budget.sdk.service;

import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectDto;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectImageDto;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectImageVo;

import java.util.List;
import java.util.Set;

public interface ApprovalCollectImageVoService {
  List<ApprovalCollectImageVo> create(ApprovalCollectDto approvalCollect, List<ApprovalCollectImageDto> images);

  void deleteByIds(Set<String> ids);

  List<ApprovalCollectImageVo> findByIds (List<String> ids);

  List<ApprovalCollectImageVo> findByApprovalCollectCode(String code);
}
