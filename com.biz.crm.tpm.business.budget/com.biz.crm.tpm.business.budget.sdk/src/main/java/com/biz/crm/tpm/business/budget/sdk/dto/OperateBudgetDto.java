package com.biz.crm.tpm.business.budget.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 预算操作dto
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "OperateBudgetDto", description = "预算操作dto")
public class OperateBudgetDto extends TenantFlagOpDto {

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 月度预算编码
     */
    private String budgetCode;

    /**
     * 操作金额
     */
    private BigDecimal operationAmount;

    /**
     * 业务编码
     */
    private String businessCode;
}
