package com.biz.crm.tpm.business.control.sdk.service;

import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

public interface BudgetControlLogEventListener extends NebulaEvent {

    /**
     * 创建事件
     *
     * @param dto
     */
    void onCreate(BudgetControlLogEventDto dto);

    /**
     * 编辑事件
     *
     * @param dto
     */
    void onUpdate(BudgetControlLogEventDto dto);

    /**
     * 删除事件
     *
     * @param dto
     */
    void onDelete(BudgetControlLogEventDto dto);

    /**
     * 启用事件
     *
     * @param dto
     */
    void onEnable(BudgetControlLogEventDto dto);

    /**
     * 禁用事件
     *
     * @param dto
     */
    void onDisable(BudgetControlLogEventDto dto);
}
