package com.biz.crm.tpm.business.budget.sdk.constant;

public interface CostBudgetConstant {
  /**
   * 费用预算编号生成前缀
   * redis生成费用预算编码编码，编码规则为NDYS+年月+4位顺序数。每天都从0001开始
   */
  String COSTBUDGET_RULE_CODE = "NDYS";
  /**
   * 分析预算
   */
  String ANALYSIS_RULE_CODE = "FXYS";
  /**
   * 收入预算
   */
  String INCOME_RULE_CODE = "SRYS";
  /**
   * 公摊费率
   */
  String PUBLIC_SHARE_RATIO_RULE_CODE = "GTFL";

  /**
   * 年度预算锁
   */
  String COST_BUDGET_LOCK = "cost_budget:lock:";

  /**
   * 预算管控锁
   */
  String BUDGET_CONTROL_LOCK = "budget_control:lock:";

  /**
   * 默认加锁时间
   */
  int DEFAULT_LOCK_TIME = 20 * 60;

  /**
   * 加锁时间：5
   */
  int LOCK_TIME_FIVE = 5;

  /**
   * 加锁时间：10
   */
  int LOCK_TIME_TEN = 10;

  /**
   * 等待时间：3
   */
  int DEFAULT_WAITE_TIME_FIVE = 3;
}
