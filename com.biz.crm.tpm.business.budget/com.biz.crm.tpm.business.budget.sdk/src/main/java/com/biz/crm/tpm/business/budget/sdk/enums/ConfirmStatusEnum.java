package com.biz.crm.tpm.business.budget.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 确认状态
 */
public enum ConfirmStatusEnum {
    CONFIRMED("confirmed","已确认"),
  UNCONFIRMED("unconfirmed","未确认");

  ConfirmStatusEnum(String code, String descr){
    this.code = code;
    this.descr = descr;
  }

  private String code;

  private String descr;

  public static ConfirmStatusEnum findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    for(ConfirmStatusEnum type : values()){
      if(StringUtils.equals(type.getCode(),code)){
        return type;
      }
    }
    return null;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDescr() {
    return descr;
  }

  public void setDescr(String descr) {
    this.descr = descr;
  }
}
