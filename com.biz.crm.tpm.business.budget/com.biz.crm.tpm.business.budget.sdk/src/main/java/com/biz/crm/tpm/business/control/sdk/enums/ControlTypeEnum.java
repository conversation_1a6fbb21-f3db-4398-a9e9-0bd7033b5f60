package com.biz.crm.tpm.business.control.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 管控类型
 */
public enum ControlTypeEnum {
    RATE("rate","按率管控"),
    AMOUNT("amount","按额管控");

    ControlTypeEnum(String code, String descr){
        this.code = code;
        this.descr = descr;
    }

    private String code;

    private String descr;

    public static ControlTypeEnum findByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for(ControlTypeEnum type : values()){
            if(StringUtils.equals(type.getCode(),code)){
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }
}
