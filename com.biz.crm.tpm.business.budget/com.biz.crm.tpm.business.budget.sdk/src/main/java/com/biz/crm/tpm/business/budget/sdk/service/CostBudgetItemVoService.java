package com.biz.crm.tpm.business.budget.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetItemDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 费用预算明细(CostBudgetItem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
public interface CostBudgetItemVoService {

    /**
     * 分页查询数据
     *
     * @param pageable          分页对象
     * @param costBudgetItemDto 实体对象
     */
    Page<CostBudgetItemVo> findByConditions(Pageable pageable, CostBudgetItemDto costBudgetItemDto);

    /**
     * 分页查询数据
     *
     * @param pageable      分页对象
     * @param costBudgetDto 实体对象
     */
    Page<CostBudgetItemVo> findByConditionsItem(Pageable pageable, CostBudgetDto costBudgetDto);

    /**
     * 新增数据
     *
     * @param costBudgetItemDtos 实体对象
     * @return 新增结果
     */
    List<CostBudgetItemVo> create(List<CostBudgetItemDto> costBudgetItemDtos);

    /**
     * 更新期初明细数据
     *
     * @param costBudgetItemDto 实体对象
     * @return 新增结果
     */
    CostBudgetItemVo updateForInitializationItem(CostBudgetItemDto costBudgetItemDto);

    /**
     * 根据费用预算编码，查询明细信息
     */
    List<CostBudgetItemVo> findByCostBudgetCode(String costBudgetCode);


    /**
     * 根据费用预算编码集合，查询明细信息
     * k-费用预算编码 v-相关明细信息
     */
    Map<String, List<CostBudgetItemVo>> findByCostBudgetCodes(List<String> costBudgetCodes);

    /**
     * 根据费用预算编码，统计是否存在"非期初"明细项
     */
    boolean existNoInitItemByCostBudgetCode(String costBudgetCode);

    /**
     * 根据业务编码查询信息
     */
    List<CostBudgetItemVo> findByBusinessCode(String businessCode);

    /**
     * 根据业务编码和业务明细编码，查询信息
     */
    List<CostBudgetItemVo> findByBusinessCodeAndBusinessItemCode(String businessCode, String businessItemCode);
}

