package com.biz.crm.tpm.business.budget.sdk.dto;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 参数传递dto：TPM-活动大类;
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
@ApiModel(value = "CostTypeCategory",description = "TPM-活动大类")
@Getter
@Setter
public class CostTypeCategoryDto implements Serializable,Cloneable{
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 数据业务状态（启用状态） */
  @ApiModelProperty(name = "enableStatus",notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
  private String enableStatus;
  /** 活动大类编号 */
  @ApiModelProperty(name = "categoryCode",notes = "活动大类编号", value = "活动大类编号")
  private String categoryCode;
  /** 活动大类名称 */
  @ApiModelProperty(name = "categoryName",notes = "活动大类名称", value = "活动大类名称")
  private String categoryName;
  /** 预算科目编号 */
  @ApiModelProperty(name = "预算科目编号",notes = "")
  private String budgetSubjectsCode;
  /** 预算科目编号 */
  @ApiModelProperty(name = "预算科目编号级联",notes = "")
  private String budgetSubjectsCodeTree;
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value = "预算科目名称")
  private String budgetSubjectsName;
  /** 当前用户管理组织机构编码 */
  @ApiModelProperty("当前用户管理组织机构编码")
  private List<String> orgCodes;
  /** 当前用户管理组织层级编码 */
  @ApiModelProperty("当前用户管理组织层级编码")
  private List<String> orgTypes;
  /** 下拉选择查询使用 */
  @ApiModelProperty("下拉选择查询使用")
  private boolean isSelect;
  @ApiModelProperty("回显编码，字符串，一般用于单选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这条数据")
  private String selectedCode;
  @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCodeList;
  private Set<String> budgetSubjectsCodeSet;
}