package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.business.common.sdk.vo.FileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ApprovalCollectImageVo", description = "核销采集图片vo")
public class ApprovalCollectImageVo extends FileVo {
  @ApiModelProperty("核销采集编码")
  private String approvalCollectCode;
}
