package com.biz.crm.tpm.business.budget.sdk.register;

import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormEventStrategy;

import java.util.Collection;

/**
 * 描述:表单事件注册器(当前版本只支持动态表单，页面引擎配置的表单待页面引擎改版后考虑支持)
 * 1、注册活动可以使用的表单事件策略
 *
 * <AUTHOR>
 *
 */
public interface FormEventRegister {

  /**
   * 表单维度 标品默认实现 活动申请表单，活动执行表单，活动核销表单
   *
   * @return
   */
  Collection<Class<? extends FormDimension>> getFormDimensions();
}
