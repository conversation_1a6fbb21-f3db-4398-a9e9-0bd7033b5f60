package com.biz.crm.tpm.business.budget.sdk.dto;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 参数传递dto：TPM-活动明细;
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@ApiModel(value = "CostTypeDetails",description = "TPM-活动明细")
@Getter
@Setter
public class CostTypeDetailsDto implements Serializable,Cloneable{
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /** 数据业务状态（启用状态） */
  @ApiModelProperty(name = "enableStatus",notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
  private String enableStatus;
  /** 活动细类名称 */
  @ApiModelProperty(name = "detailName",notes = "活动细类名称", value = "活动细类名称")
  private String detailName;
  /** 活动细类编号 */
  @ApiModelProperty(name = "detailCode",notes = "活动细类编号", value = "活动细类编号")
  private String detailCode;
   /** 是否核销 */
   @ApiModelProperty(name = "isAudit",notes = "是否核销", value = "是否核销")
  private String isAudit;
  /** 是否自动核销(Y/N) */
  @ApiModelProperty(name = "isAutoAudit",notes = "是否自动核销(Y/N)", value= "是否自动核销(Y/N)")
  private String isAutoAudit;
  /** 是否多次核销(Y/N) */
  @ApiModelProperty(name = "isMultipleAudit",notes = "是否多次核销(Y/N)", value= "是否多次核销(Y/N)")
  private String isMultipleAudit;
  /** 是否计提(Y/N) */
  @ApiModelProperty(name = "是否计提(Y/N)",notes = "")
  private String isWithHolding;

  /**
   * 是否允许提前结案(Y/N)
   */
  @ApiModelProperty("是否允许提前结案(Y/N)")
  private String beAuditEarly;

  @ApiModelProperty("活动大类编码")
  private String categoryCode;
  /** 执行类型 */
  @ApiModelProperty("执行类型")
  private String executionType;

  @ApiModelProperty("回显编码，字符串，一般用于单选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这条数据")
  private String selectedCode;
  @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCodeList;

  @ApiModelProperty("二级费用大类")
  private String secondCostCategory;

  @ApiModelProperty("活动大类编码")
  private Set<String> categoryCodeSet;


}