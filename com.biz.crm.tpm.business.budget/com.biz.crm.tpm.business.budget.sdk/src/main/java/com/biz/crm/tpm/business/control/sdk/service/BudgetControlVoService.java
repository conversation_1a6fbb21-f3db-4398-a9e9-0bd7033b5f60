package com.biz.crm.tpm.business.control.sdk.service;

import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlDto;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface BudgetControlVoService {

    /**
     * 创建
     *
     * @param budgetControlDto
     */
    void create(BudgetControlDto budgetControlDto,
                String cacheKeyCus, String cacheKeyDept, String cacheKeyCusExclude, String cacheKeyDeptExclude, String cacheKeySubject);

    /**
     * 修改
     *
     * @param budgetControlDto
     */
    void update(BudgetControlDto budgetControlDto,
                String cacheKeyCus, String cacheKeyDept, String cacheKeyCusExclude, String cacheKeyDeptExclude, String cacheKeySubject);

    /**
     * 删除
     *
     * @param idList
     */
    void delete(List<String> idList);

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    BudgetControlVo findByCode(String code);

    /**
     * 启用
     *
     * @param idList
     */
    void enable(List<String> idList);

    /**
     * 禁用
     *
     * @param idList
     */
    void disable(List<String> idList);

    /**
     * 保存
     *
     * @param budgetControlDto
     * @param beUpdate
     */
    void saveOrUpdate(BudgetControlDto budgetControlDto, boolean beUpdate);

    /**
     * 查询生效中的管控配置
     *
     * @param
     * @return
     */
    List<BudgetControlVo> findBudgetControlEnable(String yearMonthLy);


    List<BudgetControlVo> findBudgetControlByCodes(List<String> codes);

    /**
     * 匹配规则，年月+部门+客户+预算科目
     * @param years
     * @param orgCodes
     * @param customerCode
     * @param budgetSubjectCodeSet
     * @return
     */
    List<BudgetControlVo> matchBudgetControl(String years, List<String> orgCodes, String customerCode, Set<String> budgetSubjectCodeSet);
}
