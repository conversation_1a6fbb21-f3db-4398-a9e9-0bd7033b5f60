package com.biz.crm.tpm.business.adjust.sdk.event;

import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * @Description 预算调整事件服务
 * <AUTHOR>
 * @Date 2024/7/31 19:16
 */
public interface BudgetAdjustEventListener extends NebulaEvent {

    /**
     * 创建事件
     *
     * @param dto
     */
    default void onCreate(BudgetAdjustEventDto dto) {
    }

    /**
     * 编辑事件
     *
     * @param dto
     */
    default void onUpdate(BudgetAdjustEventDto dto) {
    }

    /**
     * 删除事件
     *
     * @param dto
     */
    default void onDelete(BudgetAdjustEventDto dto) {
    }

    /**
     * 启用事件
     *
     * @param dto
     */
    default void onEnable(BudgetAdjustEventDto dto) {
    }

    /**
     * 禁用事件
     *
     * @param dto
     */
    default void onDisable(BudgetAdjustEventDto dto) {
    }
}
