package com.biz.crm.tpm.business.budget.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 费用预算操作类型
 */
public enum CostBudgetOperateType {
  INITIALIZATION("init","期初"),
  USED("use","使用"),
  BACK("return_back","退回"),
  APPEND("additional","追加"),
  REDUCE("cut_out","削减"),
  WIPE_IN("wipe_in","划入"),
  WIPE_OUT("wipe_out","划出"),
  TRANSFER_IN("adjust_in","调入"),
  TRANSFER_IN_LOCK("transfer_in_lock","调入锁定"),
  TRANSFER_IN_UNLOCK("transfer_in_unlock","调入解锁"),
  TRANSFER_OUT("adjust_out","调出"),
  FREEZE("freeze","冻结"),
  UNFREEZE("unfreeze","解冻");

  CostBudgetOperateType(String code, String descr){
    this.code = code;
    this.descr = descr;
  }

  private String code;

  private String descr;

  public static CostBudgetOperateType findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    for(CostBudgetOperateType type : values()){
      if(StringUtils.equals(type.getCode(),code)){
        return type;
      }
    }
    return null;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDescr() {
    return descr;
  }

  public void setDescr(String descr) {
    this.descr = descr;
  }
}
