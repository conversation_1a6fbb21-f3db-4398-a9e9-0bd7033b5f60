package com.biz.crm.tpm.business.budget.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectDto;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

public interface ApprovalCollectVoService {
  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param approvalCollect 实体对象
   */
  Page<ApprovalCollectVo> findByConditions(Pageable pageable, ApprovalCollectDto approvalCollect);

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  ApprovalCollectVo findById(String id);

  /**
   * 通过主键 批量查询数据
   *
   * @param ids 主键集合
   */
  List<ApprovalCollectVo> findByIds(Set<String> ids);

  /**
   * 通过主键查询数据详情
   */
  ApprovalCollectVo findDetailsById(String id);

  /**
   * 通过编码查询单条数据
   * @param code 编码
   * @return 单条数据
   */
  ApprovalCollectVo findByCode(String code);

  /**
   * 通过编码查询数据详情
   */
  ApprovalCollectVo findDetailsByCode(String code);

  /**
   * 通过编码集合查询数据详情
   */
  List<ApprovalCollectVo> findDetailsByCodes(Set<String> codes);

  /**
   * 新增数据
   *
   * @param approvalCollect 实体对象
   * @return 新增结果
   */
  ApprovalCollectVo create(ApprovalCollectDto approvalCollect);

  /**
   * 修改新据
   *
   * @param approvalCollect 实体对象
   * @return 修改结果
   */
  ApprovalCollectVo update(ApprovalCollectDto approvalCollect);

  /**
   * 删除数据
   *
   * @param idList 主键结合
   */
  void delete(Set<String> idList);

  /**
   * 启禁用
   */
  void updateEnableStatus(Set<String> ids, String enableStatus);
}
