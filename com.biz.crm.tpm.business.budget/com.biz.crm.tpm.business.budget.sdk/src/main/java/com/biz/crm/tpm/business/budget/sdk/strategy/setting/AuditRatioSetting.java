package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.NUMBER;

@Component
public class AuditRatioSetting extends AbstractStrategySetting {

  @Autowired
  private AutoAuditSetting autoAuditSetting;

  public AuditRatioSetting() {
    super("超额核销比例(%)", AuditRatioSetting.class.getSimpleName());
    this.type = StrategySettingType.AUDIT.name();
    this.necessary = true;
    this.sortIndex = 2;
    this.valueType = NUMBER.getCode();
    this.parentCode = AutoAuditSetting.class.getSimpleName();
    this.tip = "最大可核销金额=申请金额*(1+超额核销比%)";
  }

  public AbstractStrategySetting getParent() {
    return autoAuditSetting;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList();
  }

  @Override
  public Object getDefaultValue() {
    return null;
  }
}
