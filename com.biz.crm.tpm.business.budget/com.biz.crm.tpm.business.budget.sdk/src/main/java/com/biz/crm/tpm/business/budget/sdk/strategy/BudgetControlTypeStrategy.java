package com.biz.crm.tpm.business.budget.sdk.strategy;

import org.springframework.core.Ordered;

import java.math.BigDecimal;

/**
 * 描述：</br>预算科目控制类型策略，具体的活动绑定预算科目后可以通过该策略来实现对预算的周期进行控制
 * <pre>
 *   默认实现的策略包括：
 *   1、不控制
 *   2、全年
 *   3、季度
 *   4、月份
 *   新增活动收，活动的申请金额根当前费用预算（渠道、组织、科目均匹配）进行验证.
 *   根据getOrder()方法确定排列顺序，数字越小优先级越高
 * </pre>
 *
 * <AUTHOR>
 * @date 2022/5/18
 */
public interface BudgetControlTypeStrategy extends Ordered {

  String BUDGET_GLOBAL_REDIS_LOCK_KEY = "BUDGET_GLOBAL_REDIS_LOCK_KEY";

  Integer MAX_TIMES = 20;

  /**
   * 控制类型编号（需要全局唯一）
   */
  String getCode();

  /**
   * 控制类型名称
   */
  String getName();

  /**
   * 正向扣减
   */
  void forward(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source);

  /**
   * 逆向返还
   */
  void reverse(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source);
}
