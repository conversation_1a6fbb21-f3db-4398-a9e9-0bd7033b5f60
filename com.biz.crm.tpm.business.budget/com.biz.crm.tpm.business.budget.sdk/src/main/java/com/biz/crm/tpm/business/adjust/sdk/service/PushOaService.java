package com.biz.crm.tpm.business.adjust.sdk.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;

import java.util.List;
import java.util.function.BiConsumer;

public interface PushOaService {

    /**
     * 创建OA流程
     *
     * @param orderJsonObject 业务对象
     * @param businessType
     * @param oaParam OA参数
     * @param details 业务对象明细
     * @param workflowId 流程id
     * @param workflowName 流程名称
     * @param mainTableMethod 主列表枚举封装
     * @param detailTableMethod 明细列表枚举封装
     * @param <T>
     * @return
     */
    <T> JSONObject pushOA(JSONObject orderJsonObject, String businessType, JSONObject oaParam, List<JSONArray> details, String workflowId, String workflowName,
                          BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod, BiConsumer<List<WorkflowRequestTableField>, JSONObject> ... detailTableMethod);
}
