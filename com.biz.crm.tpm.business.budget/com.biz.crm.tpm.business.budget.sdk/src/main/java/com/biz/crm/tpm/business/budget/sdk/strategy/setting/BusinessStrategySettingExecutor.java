package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

public interface BusinessStrategySettingExecutor extends StrategySettingExecutor{

  static BusinessStrategySettingExecutor getExecutor(List<BusinessStrategySettingExecutor> executors, String strategySettingType){
    if(CollectionUtils.isEmpty(executors) || StringUtils.isBlank(strategySettingType)){
      return null;
    }
    for(BusinessStrategySettingExecutor executor : executors){
      if(StringUtils.equals(executor.executorName(),strategySettingType)){
        return executor;
      }
    }
    return null;
  }

  boolean matchedOprtType(List<StrategySettingStruct> settingStrategies, String oprtType, Object standardValue);

  Object getValueByOprtType(List<StrategySettingStruct> settingStrategies, String oprtType);

  String executorName();
}
