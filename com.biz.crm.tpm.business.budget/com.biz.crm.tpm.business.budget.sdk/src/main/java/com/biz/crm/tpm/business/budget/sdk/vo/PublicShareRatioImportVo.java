package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;

import java.math.BigDecimal;

@Data
@CrmExcelImport(startRow = 4)
public class PublicShareRatioImportVo extends CrmExcelVo {

    @CrmExcelColumn("年")
    private String yearStr;

    @CrmExcelColumn("月")
    private String monthStr;

    @CrmExcelColumn("部门编码")
    private String departmentCode;

    @CrmExcelColumn("部门名称")
    private String departmentName;
    private Integer levelNum;

    @CrmExcelColumn("费率")
    private String ratioStr;
    private BigDecimal ratio;
    
    @CrmExcelColumn("备注")
    private String remark;

    private String uniqueKey;

}
