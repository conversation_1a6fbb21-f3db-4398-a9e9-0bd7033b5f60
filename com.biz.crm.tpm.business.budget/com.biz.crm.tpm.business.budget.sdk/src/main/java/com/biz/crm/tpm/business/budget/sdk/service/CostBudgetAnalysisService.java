package com.biz.crm.tpm.business.budget.sdk.service;


import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetAnalysisImportVo;

import java.util.List;

/**
 * 分析预算
 */
public interface CostBudgetAnalysisService {

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    void delete(List<String> idList);

    /**
     * 批量保存
     *
     * @param importVoList
     */
    void saveBatch(List<CostBudgetAnalysisImportVo>  importVoList);
}
