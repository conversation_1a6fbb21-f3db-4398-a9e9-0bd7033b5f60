package com.biz.crm.tpm.business.budget.sdk.event.log;

import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * <AUTHOR>
 * @describe: 活动细类 业务日志事件接口
 * @createTime 2022年06月23日 14:53:00
 */
@Deprecated
public interface CostTypeDetailLogEventListener extends NebulaEvent {
  /**
   * 创建事件
   */
  void onCreate(CostTypeDetailLogEventDto eventDto);

  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(CostTypeDetailLogEventDto eventDto);

  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(CostTypeDetailLogEventDto eventDto);

  /**
   * 启用
   *
   * @param eventDto
   */
  void onEnable(CostTypeDetailLogEventDto eventDto);

  /**
   * 禁用
   *
   * @param eventDto
   */
  void onDisable(CostTypeDetailLogEventDto eventDto);
}
