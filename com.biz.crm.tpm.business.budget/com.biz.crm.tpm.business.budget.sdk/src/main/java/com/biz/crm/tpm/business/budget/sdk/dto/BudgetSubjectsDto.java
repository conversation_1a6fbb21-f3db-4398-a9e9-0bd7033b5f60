package com.biz.crm.tpm.business.budget.sdk.dto;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 参数传递dto：TPM-预算科目;
 * <AUTHOR> Keller
 * @date : 2022-5-18
 */
@ApiModel(value = "BudgetSubjects",description = "TPM-预算科目")
@Getter
@Setter
public class BudgetSubjectsDto implements Serializable,Cloneable{
  /** 数据业务状态（启用状态） */
  @ApiModelProperty(name = "enableStatus",notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
  private String enableStatus;
  /** 预算科目编码 */
  @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编码", value = "预算科目编码")
  private String budgetSubjectsCode;
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value = "预算科目名称")
  private String budgetSubjectsName;
  /** 预算科目类型(数据字典) */
  @ApiModelProperty(name = "budgetSubjectsType",notes = "预算科目类型(数据字典)", value = "预算科目类型(数据字典)")
  private String budgetSubjectsType;
  /** 控制类型编码 */
  @ApiModelProperty(name = "controlTypeCode",notes = "控制类型编码", value = "控制类型编码")
  private String controlTypeCode;
  /** 预算科目分组(数据字典) */
  @ApiModelProperty(name = "groupCode",notes = "预算科目分组(数据字典)", value = "预算科目分组(数据字典)")
  private String groupCode;
  /** 租户编号 */
  private String tenantCode;
  /** 合作类型 */
  @ApiModelProperty(name = "cooperateType",notes = "合作类型", value = "合作类型")
  private String cooperateType;

  @ApiModelProperty("回显编码，字符串，一般用于单选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这条数据")
  private String selectedCode;
  @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCodeList;


  /** 上级预算科目编码 */
  @ApiModelProperty("上级预算科目编码")
  private String parentBudgetSubjectsCode;

  /** 上级预算科目名称 */
  @ApiModelProperty("上级预算科目名称")
  private String parentBudgetSubjectsName;

  /** 科目层级 */
  @ApiModelProperty("科目层级")
  private String level;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("预算科目编码(排除)")
  private List<String> excludeBudgetSubjectsCodes;
}