package com.biz.crm.tpm.business.adjust.sdk.service;

import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDto;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustVo;

import java.util.List;

/**
 * 预算调整
 */
public interface BudgetAdjustService {

    /**
     * 通过编码查询单条数据
     *
     * @param code 编码
     * @return 单条数据
     */
    BudgetAdjustVo findByCode(String code);

    /**
     * 调整
     *
     * @param dto
     */
    void adjust(BudgetAdjustDto dto, boolean beBatch);

    /**
     * 变更
     *
     * @param dto
     */
    void change(BudgetAdjustDto dto, boolean beBatch);

    /**
     * 编辑
     *
     * @param dto
     */
    void update(BudgetAdjustDto dto);

    /**
     * 审批通过
     *
     * @param code
     */
    void approve(String code, String processStatus);

    /**
     * 提交
     *
     * @param idList
     */
    void submit(List<String> idList);

    /**
     * 流程撤回
     *
     * @param code
     */
    void recover(String code, String remark);

    /**
     * 删除
     *
     * @param ids
     */
    void delete(List<String> ids);
}
