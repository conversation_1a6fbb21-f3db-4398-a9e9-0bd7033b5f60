package com.biz.crm.tpm.business.budget.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel(value = "StrategySettingManageDto",description = "TPM-活动细类策略配置Dto")
@Getter
@Setter
public class StrategySettingManageDto extends TenantFlagOpDto {

   @ApiModelProperty(name = "活动细类策略配置名称")
   private String name;

   @ApiModelProperty(name = "活动细类策略配置编码")
   private String code;

   @ApiModelProperty(name = "是否需要启用")
   private Boolean enableOprt;

   @ApiModelProperty(name = "活动相关的策略明细集")
   private List<StrategySettingStruct> activitySettings;

   @ApiModelProperty(name = "核销相关的策略明细集")
   private List<StrategySettingStruct> auditSettings;
}