package com.biz.crm.tpm.business.budget.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.sdk.dto.StrategySettingManageDto;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.AbstractStrategySetting;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;
import com.biz.crm.tpm.business.budget.sdk.vo.StrategySettingManageVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

public interface StrategySettingManageVoService {

  /**
   * 策略配置-分页查询
   */
  Page<StrategySettingManageVo> findByConditions(Pageable pageable, StrategySettingManageDto dto);

  /**
   * 根据主键id集合，查询信息
   */
  List<StrategySettingManageVo> findByIds(Set<String> ids);

  /**
   * 根据策略配置编码查询信息
   */
  StrategySettingManageVo findByCode(String code);

  /**
   * 新增
   */
  void create(StrategySettingManageDto dto);

  /**
   * 更新
   */
  void update(StrategySettingManageDto dto);

  /**
   * 令指定的策略配置作为"正在"使用的策略
   * 注：有且只有一个策略配置"正在使用"，启用了一个，其余的都禁用
   */
  void enableStatus(String id);

  /**
   * 禁用
   */
  void disabled(Set<String> ids);

  /**
   * 获取指定策略配置结构信息，树形结构
   */
  List<StrategySettingStruct> structs(Set<String> codes);

  /**
   * 获取最新的"正在使用"的策略配置
   */
  StrategySettingManageVo findEnabled();
}
