package com.biz.crm.tpm.business.adjust.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BudgetAdjust", description = "预算调整")
public class BudgetAdjustVo extends TenantFlagOpVo {

  @ApiModelProperty("调整编码")
  private String adjustCode;

  @ApiModelProperty("调整名称")
  private String adjustName;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("公司名称")
  private String companyName;

  @ApiModelProperty("ERP编码")
  private String erpCode;

  @ApiModelProperty("操作类型")
  private String operateType;

  @ApiModelProperty("审批状态")
  private String status;

  @ApiModelProperty("审批单号")
  private String processNumber;

  @ApiModelProperty("推送日期")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("OA人员id")
  private String oaId;

  @ApiModelProperty("OA人员账号")
  private String oaUserName;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("追加预算金额汇总")
  private BigDecimal total;

  @ApiModelProperty("明细行最大追加金额")
  private BigDecimal maxAmount;

  @ApiModelProperty("部门编码")
  private String deptCode;

  @ApiModelProperty("回传TPM接口唯一标识（接口用）")
  private String businessCode;

  @ApiModelProperty("url")
  private String tpmUrl;

  @ApiModelProperty("调整明细")
  private List<BudgetAdjustDetailVo> details;
}
