package com.biz.crm.tpm.business.budget.sdk.event;

import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;

import java.util.Set;

public interface ApprovalCollectEventListener {
  /**
   * 当TPM-核销采集示例数据被删除时（逻辑删除），该事件被触发
   */
  void onDeleted(ApprovalCollectVo approvalCollectVo);

  /**
   * 当TPM-核销采集示例数据更新时，该事件被触发
   */
  void onUpdate(ApprovalCollectVo oldApprovalCollectVo, ApprovalCollectVo newApprovalCollectVo);
}
