package com.biz.crm.tpm.business.budget.sdk.event;

import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;

public interface CostBudgetEventListener {
  /**
   * 当TPM-费用预算数据被删除时（逻辑删除），该事件被触发
   */
  default void onDeleted(CostBudgetVo costBudgetVo){};

  /**
   * 当TPM-费用预算数据被禁用时，该事件被触发
   */
  default void onDisable(CostBudgetVo costBudgetVo){};

  /**
   * 当TPM-费用预算数据被激活时，该事件被触发
   */
  default void onEnable(CostBudgetVo costBudgetVo){};

  /**
   * 当TPM-费用预算数据被编辑时，该事件被触发
   */
  default void onUpdate(CostBudgetVo costBudgetVo){};
}
