package com.biz.crm.tpm.business.budget.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/2 19:33
 */
@Getter
@AllArgsConstructor
public enum SecondCategoryEnum {

    disseminateS("B8007", "品宣"),
    contract("B8005", "合同"),
    generalization("B8008", "人员推广"),
    display("B8009", "陈列"),
    callback("B8006", "回调类"),
    salesReward("B6003", "销售奖励"),
    promotion("B8001", "促销");

    private String code;

    private String name;

    public static List<String> getCodes() {
        List<String> codes = new ArrayList<>();
        for (SecondCategoryEnum secondCategoryEnum : SecondCategoryEnum.values()) {
            codes.add(secondCategoryEnum.getCode());
        }
        return codes;
    }

    public static SecondCategoryEnum value(String value) {
        for (SecondCategoryEnum secondCategoryEnum : SecondCategoryEnum.values()) {
            if (secondCategoryEnum.getName().equals(value)) {
                return secondCategoryEnum;
            }
        }
        return null;
    }
}
