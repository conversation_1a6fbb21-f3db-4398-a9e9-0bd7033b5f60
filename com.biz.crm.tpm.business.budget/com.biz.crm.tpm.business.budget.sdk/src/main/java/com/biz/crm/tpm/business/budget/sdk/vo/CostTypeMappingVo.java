package com.biz.crm.tpm.business.budget.sdk.vo;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

 /**
 * Vo：TPM-活动大类与活动细类关联;
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@ApiModel(value = "CostTypeMapping",description = "TPM-活动大类与活动细类关联")
@Getter
@Setter
public class CostTypeMappingVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 活动大类编号 */
  @ApiModelProperty(name = "categoryCode",notes = "活动大类编号", value= "活动大类编号")
  private String categoryCode;
  /** 活动大类名称 */
  @ApiModelProperty(name = "categoryName",notes = "活动大类名称", value= "活动大类名称")
  private String categoryName;
  /** 活动细类编号 */
  @ApiModelProperty(name = "detailCode",notes = "活动细类编号", value= "活动细类编号")
  private String detailCode;
  /** 预算科目编码 */
  @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编码", value= "预算科目编码")
  private String budgetSubjectsCode;
  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value= "预算科目名称")
  private String budgetSubjectsName;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
  private String tenantCode;

}