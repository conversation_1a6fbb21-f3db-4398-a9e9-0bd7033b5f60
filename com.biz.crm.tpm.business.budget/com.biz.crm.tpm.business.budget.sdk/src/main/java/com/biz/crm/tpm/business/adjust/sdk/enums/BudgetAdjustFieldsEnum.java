package com.biz.crm.tpm.business.adjust.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum BudgetAdjustFieldsEnum {
    adjustCode("adjustCode", "adjustCode", "预算调整编码", "String"),
    adjustName("adjustName", "adjustName", "预算调整名称", "String"),
    total("total", "total", "追加预算金额汇总", "BigDecimal"),
    remark("remark", "remark", "备注", "String"),
    maxAmount("maxAmount", "maxAmount", "明细行最大追加金额", "BigDecimal"),
    deptCode("deptCode", "deptCode", "部门编码", "String"),
    companyName("companyName", "companyName", "公司名称", "String"),
    title("title", "title", "标题", "String"),
    ;

    private String key;
    private String dictCode;
    private String value;
    private String dateType;

    public static BudgetAdjustFieldsEnum findByCode(String code) {
        Optional<BudgetAdjustFieldsEnum> first = Stream.of(BudgetAdjustFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }
}