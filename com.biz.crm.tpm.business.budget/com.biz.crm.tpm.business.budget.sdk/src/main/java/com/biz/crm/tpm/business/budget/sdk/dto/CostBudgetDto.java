package com.biz.crm.tpm.business.budget.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@ApiModel(value = "CostBudgetDto", description = "费用预算参数信息")
public class CostBudgetDto extends TenantFlagOpDto {
  @ApiModelProperty("编码")
  private String code;

  /**
   * 参见com.biz.tpm.business.budget.sdk.enums.CostBudgetType
   */
  @ApiModelProperty("费用预算类型")
  private String type;

  @ApiModelProperty("年")
  private Integer year;

  @ApiModelProperty("季度")
  private Integer quarter;

  @ApiModelProperty("月份")
  private Integer month;

  @ApiModelProperty("预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("渠道名称")
  private String channelName;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("门店编码")
  private String terminalCode;

  @ApiModelProperty("门店名称")
  private String terminalName;

  @ApiModelProperty("产品层级编码")
  private String productLevelCode;

  @ApiModelProperty("产品层级名称")
  private String productLevelName;

  @ApiModelProperty("产品编码")
  private String productCode;

  @ApiModelProperty("产品名称")
  private String productName;

  @ApiModelProperty("期初金额")
  private BigDecimal initialAmount;

  @ApiModelProperty("操作金额")
  private BigDecimal operateAmount;

  @ApiModelProperty("操作备注")
  private String operateRemark;

  @ApiModelProperty("是否有可用余额")
  private Boolean hasAbleBalance;

  @ApiModelProperty("组织编码集合")
  private Set<String> orgCodes;

  @ApiModelProperty("需要排除的费用预算编码")
  private Set<String> excludeCodes;



  @ApiModelProperty("预算类型")
  private String budgetType;

  @ApiModelProperty("年月")
  private String yearMonthLy;

  @ApiModelProperty("确认状态")
  private String confirmStatus;

  @ApiModelProperty("一级部门编码")
  private String departmentOneCode;

  @ApiModelProperty("一级部门名称")
  private String departmentOneName;

  @ApiModelProperty("部门层级")
  private Integer levelNum;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("公司名称")
  private String companyName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("唯一标识")
  private String uniqueKey;

  @ApiModelProperty("调整金额")
  private BigDecimal adjustAmount;

  @ApiModelProperty("调整后余额")
  private BigDecimal adjustBalanceAmount;

  @ApiModelProperty("月度余额")
  private BigDecimal monthBalanceAmount;

  @ApiModelProperty("冻结金额")
  private BigDecimal freezeAmount;

  @ApiModelProperty("已使用金额")
  private BigDecimal usedAmount;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("关联业务编码")
  private String businessCode;

  /**
   * 客户ERP编码
   */
  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  /**
   * 产品组编码
   */
  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("需要排除的费用预算id")
  private String excludeId;
  @ApiModelProperty("获取子机构账户名")
  private String userName;
  private List<String> itemCodes;

  private List<String> productCodes;

  /**查询*/
  private String hasDept = BooleanEnum.FALSE.getCapital();
  private String hasCus = BooleanEnum.FALSE.getCapital();
  private String hasItem = BooleanEnum.FALSE.getCapital();
  private String hasProduct = BooleanEnum.FALSE.getCapital();
  private Set<String> headDeptCodeSet;
  private Set<String> deptCodeSet;
  private Set<String> cusCodeSet;
  private Set<String> deptCodeExcludeSet;
  private Set<String> cusCodeExcludeSet;
  private Set<String> subjectCodeSet;
  private List<String> yearMonthList;

  private Set<String> uniqueKeySet;
}
