package com.biz.crm.tpm.business.budget.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 核销采集类型
 */
public enum ApprovalCollectType {
  APPROVAL("approval","活动核销"),
  COLLECT("collect","活动采集");

  ApprovalCollectType(String code, String descr){
    this.code = code;
    this.descr = descr;
  }

  private String code;

  private String descr;

  public static ApprovalCollectType findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    for(ApprovalCollectType type : values()){
      if(StringUtils.equals(type.getCode(),code)){
        return type;
      }
    }
    return null;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDescr() {
    return descr;
  }

  public void setDescr(String descr) {
    this.descr = descr;
  }
}
