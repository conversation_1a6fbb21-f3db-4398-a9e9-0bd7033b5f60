package com.biz.crm.tpm.business.control.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BudgetControl", description = "预算管控")
public class BudgetControlDto extends TenantFlagOpDto {

  @ApiModelProperty("预算管控编码")
  private String controlCode;

  @ApiModelProperty("预算管控名称")
  private String controlName;

  @ApiModelProperty("适用部门编码")
  private String departmentCode;

  @ApiModelProperty("适用部门名称")
  private String departmentName;

  @ApiModelProperty("生效开始年月")
  private String startYearMonth;

  @ApiModelProperty("生效结束年月")
  private String endYearMonth;

  @ApiModelProperty("时间维度")
  private String timeDimension;

  @ApiModelProperty("管控类型")
  private String controlType;

  @ApiModelProperty("管控形式")
  private String controlForm;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("部门层级")
  private Integer levelNum;

  @ApiModelProperty("管控维度")
  List<BudgetControlDimensionDto> dimensionList;
  @ApiModelProperty("管控范围（部门）")
  List<BudgetControlRangeDto> rangeDeptList;
  @ApiModelProperty("管控范围（客户）")
  List<BudgetControlRangeDto> rangeCusList;
  @ApiModelProperty("管控范围（部门剔除）")
  List<BudgetControlRangeDto> rangeDeptExcludeList;
  @ApiModelProperty("管控范围（客户剔除）")
  List<BudgetControlRangeDto> rangeCusExcludeList;
  @ApiModelProperty("预算科目")
  List<BudgetControlSubjectDto> subjectList;
}
