package com.biz.crm.tpm.business.budget.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CostBudgetIncome", description = "收入预算")
public class CostBudgetIncomeDto extends TenantFlagOpDto {

  @ApiModelProperty("编码")
  private String incomeCode;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("渠道名称")
  private String channelName;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("产品编码")
  private String productCode;

  @ApiModelProperty("产品名称")
  private String productName;

  @ApiModelProperty("数量")
  private BigDecimal quantity;

  @ApiModelProperty("收入金额")
  private BigDecimal incomeAmount;

  @ApiModelProperty("成本")
  private BigDecimal costAmount;

  @ApiModelProperty("物流费")
  private BigDecimal logisticsAmount;



  @ApiModelProperty("年月")
  private String yearMonthLy;

  @ApiModelProperty("确认状态")
  private String confirmStatus;

  @ApiModelProperty("事业部编码")
  private String divisionCode;

  @ApiModelProperty("事业部名称")
  private String divisionName;

  @ApiModelProperty("中心编码")
  private String centerCode;

  @ApiModelProperty("中心名称")
  private String centerName;

  @ApiModelProperty("一级部门编码")
  private String departmentOneCode;

  @ApiModelProperty("一级部门名称")
  private String departmentOneName;

  @ApiModelProperty("二级部门编码")
  private String departmentTwoCode;

  @ApiModelProperty("二级部门名称")
  private String departmentTwoName;

  @ApiModelProperty("三级部门编码")
  private String departmentThreeCode;

  @ApiModelProperty("三级部门名称")
  private String departmentThreeName;

  @ApiModelProperty("部门层级")
  private Integer levelNum;

  @ApiModelProperty("公司编码")
  private String companyCode;

  @ApiModelProperty("公司名称")
  private String companyName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("唯一标识")
  private String uniqueKey;

  @ApiModelProperty("品项编码")
  private String itemCode;

  @ApiModelProperty("品项名称")
  private String itemName;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("关联业务编码")
  private String businessCode;

  /**
   * 客户ERP编码
   */
  @ApiModelProperty("客户ERP编码")
  private String erpCode;

  /**
   * 产品组编码
   */
  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("部门编码集合")
  private Set<String> deptCodeSet;

}
