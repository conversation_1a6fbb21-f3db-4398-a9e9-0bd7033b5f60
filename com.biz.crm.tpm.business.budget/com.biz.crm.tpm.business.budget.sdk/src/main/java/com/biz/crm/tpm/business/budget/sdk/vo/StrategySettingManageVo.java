package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel(value = "StrategySettingManageVo",description = "TPM-活动细类策略配置Vo")
@Getter
@Setter
public class StrategySettingManageVo extends TenantFlagOpVo {

   @ApiModelProperty(name = "活动细类策略配置名称")
   private String name;

   @ApiModelProperty(name = "活动细类策略配置编码")
   private String code;

   @ApiModelProperty(name = "活动相关的策略明细集")
   private List<StrategySettingStruct> activitySettings;

   @ApiModelProperty(name = "核销相关的策略明细集")
   private List<StrategySettingStruct> auditSettings;
}