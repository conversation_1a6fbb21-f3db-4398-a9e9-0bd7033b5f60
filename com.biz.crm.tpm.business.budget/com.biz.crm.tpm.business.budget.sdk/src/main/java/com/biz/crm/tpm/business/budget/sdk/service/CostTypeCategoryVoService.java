package com.biz.crm.tpm.business.budget.sdk.service;

import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeCategoryDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Set;

/**
 * TPM-活动大类;(tpm_cost_type_category)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
public interface CostTypeCategoryVoService {

    /**
     * 生成操作标记
     */
    String preSave();

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    Page<CostTypeCategoryVo> findByConditions(Pageable pageable, CostTypeCategoryDto dto);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    CostTypeCategoryVo findById(String id);

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    CostTypeCategoryVo findByCode(String code);

    /**
     * 新增数据
     *
     * @param costTypeCategoryVo 实体对象
     * @return 新增结果
     */
    CostTypeCategoryVo create(CostTypeCategoryVo costTypeCategoryVo);

    /**
     * 修改新据
     *
     * @param costTypeCategoryVo 实体对象
     * @return 修改结果
     */
    CostTypeCategoryVo update(CostTypeCategoryVo costTypeCategoryVo);

    /**
     * 删除数据
     *
     * @param ids 主键结合
     */
    void delete(List<String> ids);

    /**
     * 通过启用状态查询数据
     *
     * @param enableStatus 状态
     * @return 集合数据
     */
    List<CostTypeCategoryVo> findByEnableStatus(String enableStatus);

    /**
     * 批量根据id启用
     *
     * @param ids
     */
    void enable(List<String> ids);

    /**
     * 批量根据id禁用
     *
     * @param ids
     */
    void disable(List<String> ids);

    /**
     * 根据预算科目编号获取关联的活动大类数据
     *
     * @param budgetSubjectsCode
     * @return
     */
    List<CostTypeCategoryVo> findByBudgetSubjectsCode(String budgetSubjectsCode);

    /**
     * 根据预算科目编号获取关联的活动大类数据
     *
     * @param budgetSubjectsCodes
     * @return
     */
    List<CostTypeCategoryVo> findByBudgetSubjectsCodes(List<String> budgetSubjectsCodes);

    List<CostTypeCategoryVo> findListByNames(List<String> nameList);

    List<CostTypeCategoryVo> findListByCategoryCodes(List<String> categoryCodes);

    List<CostTypeCategoryVo> findListReleaseSecondBudgetSubjectCodes();

}