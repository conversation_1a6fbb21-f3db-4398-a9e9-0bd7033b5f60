package com.biz.crm.tpm.business.budget.sdk.strategy.form;

import com.biz.crm.common.form.sdk.widget.WidgetKey;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 表单属性字段配置注释
 */
@Documented
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FormPropertiesField {

  /**
   * 字段的中文说明（这个中文说明，将可以展示在前端页面上）
   *
   * @return
   */
  String name();

  /**
   * 字段在页面上使用的控件信息（控件的唯一标识），
   * 请使用已有的控件类型描述类
   */
  Class<? extends WidgetKey> controllKey() default WidgetKey.class;

  /**
   * 字段在前端页面上，是否必填
   *
   * @return
   */
  boolean required() default false;

  /**
   * 字段信息是否显示
   */
  boolean display() default true;
}
