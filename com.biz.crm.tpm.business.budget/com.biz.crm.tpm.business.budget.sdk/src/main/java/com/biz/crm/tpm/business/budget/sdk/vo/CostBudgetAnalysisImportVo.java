package com.biz.crm.tpm.business.budget.sdk.vo;


import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@CrmExcelImport(startRow = 4)
public class CostBudgetAnalysisImportVo extends CrmExcelVo {


    @CrmExcelColumn("分组")
    private String groupCode;

    @CrmExcelColumn("年月")
    private String yearMonthLy;

    @CrmExcelColumn("部门编码")
    private String departmentOneCode;

    @CrmExcelColumn("部门名称")
    private String departmentOneName;

    @CrmExcelColumn("部门层级")
    private String levelNumStr;
    private Integer levelNum;

    @CrmExcelColumn("成本中心编码")
    private String costCenterCode;

    @CrmExcelColumn("成本中心名称")
    private String costCenterName;

    @CrmExcelColumn("预算科目编码")
    private String budgetSubjectCode;

    @CrmExcelColumn("预算科目名称")
    private String budgetSubjectName;

    @CrmExcelColumn("公司编码")
    private String companyCode;

    @CrmExcelColumn("客户ERP编码")
    private String erpCode;
    private String customerCode;

    @CrmExcelColumn("客户名称")
    private String customerName;

    @CrmExcelColumn("品项编码")
    private String itemCode;

    @CrmExcelColumn("品项名称")
    private String itemName;

    @CrmExcelColumn("产品编码")
    private String productCode;

    @CrmExcelColumn("产品名称")
    private String productName;

    @CrmExcelColumn("期初金额")
    private String initialAmountStr;
    private BigDecimal initialAmount;

    @CrmExcelColumn("备注")
    private String remark;

    @ApiModelProperty("确认状态")
    private String confirmStatus;
}
