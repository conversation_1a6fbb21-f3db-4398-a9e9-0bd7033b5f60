package com.biz.crm.tpm.business.budget.sdk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
* Vo：TPM-活动大类;
* <AUTHOR> Keller
* @date : 2022-5-19
*/
@ApiModel(value = "CostTypeCategory",description = "TPM-活动大类")
@Getter
@Setter
public class CostTypeCategoryVo implements Serializable{
 /** 操作的预授权标记 */
 @ApiModelProperty(name = "操作的预授权标记", notes = "操作的预授权标记", value = "操作的预授权标记", required=true)
 private String prefix;
 /** 主键 */
 @ApiModelProperty(name = "id",notes = "主键", value= "主键")
 private String id;
 /** 租户编号 */
 @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
 private String tenantCode;
 /** 数据业务状态（启用状态） */
 @ApiModelProperty(name = "enableStatus",notes = "数据业务状态（启用状态）", value= "数据业务状态（启用状态）")
 private String enableStatus;
 /** 修改人名称 */
 @ApiModelProperty(name = "modifyName",notes = "修改人名称", value= "修改人名称")
 private String modifyName;
 /** 修改人账号 */
 @ApiModelProperty(name = "modifyAccount",notes = "修改人账号", value= "修改人账号")
 private String modifyAccount;
 /** 修改时间 */
 @ApiModelProperty(name = "modifyTime",notes = "修改时间", value= "修改时间")
 @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
 private Date modifyTime;
 /** 创建人名称 */
 @ApiModelProperty(name = "createName",notes = "创建人名称", value= "创建人名称")
 private String createName;
 /** 创建人账号 */
 @ApiModelProperty(name = "createAccount",notes = "创建人账号", value= "创建人账号")
 private String createAccount;
 /** 创建时间 */
 @ApiModelProperty(name = "createTime",notes = "创建时间", value= "创建时间")
 @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
 private Date createTime;
 /** 备注 */
 @ApiModelProperty(name = "remark",notes = "备注", value= "备注")
 private String remark;
 /** 活动大类编号 */
 @ApiModelProperty(name = "categoryCode",notes = "活动大类编号", value= "活动大类编号")
 private String categoryCode;
 /** 活动大类名称 */
 @ApiModelProperty(name = "categoryName",notes = "活动大类名称", value= "活动大类名称")
 private String categoryName;
 /** 预算科目名称 */
 @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value= "预算科目名称")
 private String budgetSubjectsName;
 /** 预算科目编号 */
 @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编号", value= "预算科目编号")
 private String budgetSubjectsCode;
 /** 活动细类 */
 @ApiModelProperty(name = "costTypeDetails",notes = "活动细类", value= "活动细类")
 private Set<CostTypeDetailVo> costTypeDetails;
 @ApiModelProperty("回显编码，字符串，一般用于单选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这条数据")
 private String selectedCode;
 @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
 private List<String> selectedCodeList;
}