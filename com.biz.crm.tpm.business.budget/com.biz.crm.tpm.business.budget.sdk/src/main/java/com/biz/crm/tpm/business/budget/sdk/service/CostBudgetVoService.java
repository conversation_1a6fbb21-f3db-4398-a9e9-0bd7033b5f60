package com.biz.crm.tpm.business.budget.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetImportsVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetRelationVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 费用预算(CostBudget)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
public interface CostBudgetVoService {

    /**
     * 分页查询数据
     *
     * @param pageable   分页对象
     * @param costBudget 实体对象
     * @return
     */
    Page<CostBudgetVo> findByConditions(Pageable pageable, CostBudgetDto costBudget);

    /**
     * 根据条件查询数据
     */
    List<CostBudgetVo> findByConditions(CostBudgetDto dto);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    CostBudgetVo findById(String id);

    /**
     * 通过主键ids集合，查询数据
     */
    List<CostBudgetVo> findByIds(Set<String> ids);

    /**
     * 通过编码查询单条数据
     *
     * @param code 编码
     * @return 单条数据
     */
    CostBudgetVo findByCode(String code);

    /**
     * 根据科目编码查询数据
     *
     * @param budgetSubjectCode 预算科目编码
     */
    List<CostBudgetVo> findByBudgetSubjectCode(String budgetSubjectCode);

    /**
     * 通过编码查询多条数据
     *
     * @param codes 编码
     * @return 单条数据
     */
    List<CostBudgetVo> findByCodes(Set<String> codes);

    /**
     * 新增数据
     *
     * @param costBudget 实体对象
     * @return 新增结果
     */
    CostBudgetVo create(CostBudgetDto costBudget);

    /**
     * 修改新据
     *
     * @param costBudget 实体对象
     * @return 修改结果
     */
    CostBudgetVo update(CostBudgetDto costBudget);

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    void delete(List<String> idList);

    /**
     * 启禁用
     */
    void updateEnableStatus(Set<String> ids, String enableStatus);

    /**
     * 调整操作
     * 操作类型：transfer_in、transfer_out
     * 参考：com.biz.tpm.business.budget.sdk.enums.CostBudgetOperateType
     */
    void tranfer(String costBudgetCodeOut, String costBudgetCodeIn, BigDecimal operateAmount, String operateRemark);

    /**
     * 变更操作
     * 操作类型：append、reduce
     * 参考：com.biz.tpm.business.budget.sdk.enums.CostBudgetOperateType
     */
    void change(String costBudgetCode, BigDecimal operateAmount, String operateRemark, String operateType);

    /**
     * 划拨操作
     * 操作类型：wipe_in、wipe_out
     * 参考：com.biz.tpm.business.budget.sdk.enums.CostBudgetOperateType
     */
    void wipe(String costBudgetCodeOut, List<CostBudgetDto> costBudgetIns);

    /**
     * 使用操作
     * 操作类型：used
     * 参考：com.biz.tpm.business.budget.sdk.enums.CostBudgetOperateType
     */
    void occupy(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source);

    /**
     * 退回操作
     * 操作类型：back
     * 参考：com.biz.tpm.business.budget.sdk.enums.CostBudgetOperateType
     */
    void back(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source);

    /**
     * 根据费用预算编号查询费用预算与活动大类，预算科目的关系
     */
    Set<CostBudgetRelationVo> findRelationByCodes(Set<String> codes);

    /**
     * 根据费用预算编号确认
     *
     * @param codeList
     */
    void confirm(List<String> codeList);

    /**
     * 批量保存
     *
     * @param importList
     */
    void createBatch(List<CostBudgetImportsVo> importList);

    /**
     * 按条件查询
     *
     * @param dto
     * @return
     */
    List<CostBudgetVo> findBudgetCodeByDto(CostBudgetDto dto);

    /**
     * 预算管控查询预算
     *
     * @param dto
     * @return
     */
    List<CostBudgetVo> findByDto(CostBudgetDto dto);

    List<CostBudgetVo> findListByCustomerCodesAndCostCenterCodesAndYears(List<String> customerCodes, List<String> costCenterCodes, List<String> deptCodes, String years);

    List<CostBudgetVo> findListByOrgCodesAndYears(List<String> orgCodes, String years);

    List<CostBudgetVo> findListByOrgCodeAndItemCodesAndYears(List<String> orgCodes, List<String> itemCodes, String years);

    List<CostBudgetVo> findListByOrgCodeAndCategoryCodesAndYears(List<String> orgCodes, List<String> budgetSubjectCodes, String years);

    String findCodeByConditions(CostBudgetDto dto);

    Map<String, BigDecimal> findListByOrgCodesAndYearsList(List<String> orgCodes, List<String> yearsList);

    Map<String, BigDecimal> findListByOrgCodesAndYearsListNoTax(List<String> orgCodes, List<String> yearsList);

    List<CostBudgetVo> findListByOrgCodesAndCustomerCodeAndBudgetSubjectCodesAndYears(List<String> orgCodes, String customerCode, List<String> budgetSubjectCodes, String years);
}

