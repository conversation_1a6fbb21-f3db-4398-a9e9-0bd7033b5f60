package com.biz.crm.tpm.business.budget.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ApprovalCollectDto", description = "核销采集参数信息")
public class ApprovalCollectDto extends TenantFlagOpDto {
  @ApiModelProperty("编码")
  private String code;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("排序(值越小越靠前)")
  private Integer sortIndex;

  @ApiModelProperty("描述")
  private String descr;

  @ApiModelProperty("图片信息")
  private List<ApprovalCollectImageDto> images;

  @ApiModelProperty("类型信息")
  private List<ApprovalCollectTypeDto> typeList;

  @ApiModelProperty("类型信息编码")
  private List<String> typeCodeList;

}
