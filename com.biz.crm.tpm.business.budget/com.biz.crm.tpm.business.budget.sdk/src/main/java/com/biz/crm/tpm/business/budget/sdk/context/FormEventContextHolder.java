package com.biz.crm.tpm.business.budget.sdk.context;

import com.google.common.collect.Maps;
import lombok.experimental.UtilityClass;

import java.util.Map;

/**
 * 描述：</br>表单事件执行上下文holder，提供线程维度的表单事件执行上下文持有者
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@UtilityClass
public class FormEventContextHolder {
  private static final ThreadLocal<Map<String, Object>> CONTEXTHOLDER = new ThreadLocal<>();

  /**
   * 获取参数
   * @param key
   * @return
   */
  public Object get(String key) {
    return get().get(key);
  }

  /**
   * 设置参数
   * @param key
   * @param value
   */
  public void put(String key, Object value) {
    get().put(key, value);
  }

  /**
   * 清理参数
   */
  public void clear() {
    CONTEXTHOLDER.remove();
  }

  private Map<String, Object> get() {
    if (CONTEXTHOLDER.get() == null) {
      CONTEXTHOLDER.set(Maps.newHashMap());
    }
    return CONTEXTHOLDER.get();
  }

}
