package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 抽象：活动细类的具体策略项
 */
@ApiModel(value = "AbstractStrategySetting", description = "活动细类的具体策略项抽象类")
public abstract class AbstractStrategySetting {
  @ApiModelProperty("策略项的名称")
  private String name;

  @ApiModelProperty("策略项的编码")
  private String code;

  @ApiModelProperty("策略项的类型(区分用途：目前有活动和核销两类)")
  protected String type;

  @ApiModelProperty("是否必需")
  protected Boolean necessary;

  @ApiModelProperty("排序值(值越小越靠前)")
  protected Integer sortIndex;

  @ApiModelProperty("策略项的值类型")
  protected String valueType;

  @ApiModelProperty("策略项的父级编码")
  protected String parentCode;

  @ApiModelProperty("可能的时间格式")
  protected String dateFormat;

  @ApiModelProperty("可能的提示语")
  protected String tip;

  @ApiModelProperty("策略项的父级")
  protected AbstractStrategySetting parent;

  @ApiModelProperty("策略项的子级信息")
  protected List<AbstractStrategySetting> children;

  protected AbstractStrategySetting(String name, String code) {
    this.name = name;
    this.code = code;
  }

  public abstract AbstractStrategySetting getParent();

  public abstract List<AbstractStrategySetting> getChildren();

  public abstract Object getDefaultValue();

  public String getName() {
    return name;
  }

  public String getCode() {
    return code;
  }

  public String getType() {
    return type;
  }

  public Boolean getNecessary() {
    return necessary;
  }

  public Integer getSortIndex() {
    return sortIndex;
  }

  public String getValueType() {
    return valueType;
  }

  public String getDateFormat() {
    return dateFormat;
  }

  public String getParentCode() {
    return parentCode;
  }

  public String getTip() {
    return tip;
  }
}
