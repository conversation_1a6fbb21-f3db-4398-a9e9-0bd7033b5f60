package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.BOOLEAN;

@Component
public class PushSfaSetting extends AbstractStrategySetting {

  @Autowired
  private CollectActivityDataSetting collectActivityDataSettingStruct;
  @Autowired
  private SignDisplaySetting signDisplaySettingStruct;
  @Autowired
  private CollectDistributionOrdersSetting collectDistributionOrdersSettingStruct;
  @Autowired
  private ControlActivityExpensesSetting controlActivityExpensesSetting;


  public PushSfaSetting() {
    super("是否推送SFA", PushSfaSetting.class.getSimpleName());
    this.type = StrategySettingType.ACTIVITY.name();;
    this.necessary = true;
    this.sortIndex = 1;
    this.valueType = BOOLEAN.getCode();
    this.tip = "勾选\"是\",该活动细类的活动明细审批通过后自动推送至业务员SFA";
  }


  public AbstractStrategySetting getParent() {
    return null;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList(collectActivityDataSettingStruct, collectDistributionOrdersSettingStruct, signDisplaySettingStruct,controlActivityExpensesSetting);
  }

  @Override
  public Object getDefaultValue() {
    return true;
  }


}
