package com.biz.crm.tpm.business.adjust.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 核销采集类型
 */
public enum BudgetAdjustEnum {
  ADJUST("adjust","调整"),
  TRANSFER("adjust","转移"),
  CHANGE("change","变更"),
  TRANSFER_IN("adjust_in","调入"),
  TRANSFER_OUT("adjust_out","调出"),
  ADDITIONAL("additional","追加"),
  CUT_OUT("cut_out","削减");

  BudgetAdjustEnum(String code, String descr){
    this.code = code;
    this.descr = descr;
  }

  private String code;

  private String descr;

  public static BudgetAdjustEnum findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    for(BudgetAdjustEnum type : values()){
      if(StringUtils.equals(type.getCode(),code)){
        return type;
      }
    }
    return null;
  }

  public static BudgetAdjustEnum findByName(String name){
    if(StringUtils.isBlank(name)){
      return null;
    }
    for(BudgetAdjustEnum type : values()){
      if(StringUtils.equals(type.getDescr(),name)){
        return type;
      }
    }
    return null;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDescr() {
    return descr;
  }

  public void setDescr(String descr) {
    this.descr = descr;
  }
}
