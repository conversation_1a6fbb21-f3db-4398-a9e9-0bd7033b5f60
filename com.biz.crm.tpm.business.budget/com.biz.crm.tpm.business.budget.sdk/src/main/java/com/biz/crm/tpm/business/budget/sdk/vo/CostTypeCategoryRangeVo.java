package com.biz.crm.tpm.business.budget.sdk.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

import static org.apache.ibatis.type.JdbcType.VARCHAR;

/**
 * Vo：TPM-活动大类范围;
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
@ApiModel(value = "CostTypeCategoryRange",description = "TPM-活动大类范围")
@Getter
@Setter
public class CostTypeCategoryRangeVo implements Serializable{
  /** 主键 */
  @ApiModelProperty(name = "id",notes = "主键", value= "主键")
  private String id;
  /** 活动大类编号 */
  @ApiModelProperty(name = "categoryCode",notes = "活动大类编号", value= "活动大类编号")
  private String categoryCode;
  /** 活动大类名称 */
  @ApiModelProperty(name = "categoryName",notes = "活动大类名称", value= "活动大类名称")
  private String categoryName;
  /** 范围类型(1,组织,2,组织类型) */
  @ApiModelProperty(name = "rangeType",notes = "范围类型(1,组织,2,组织类型)", value= "范围类型(1,组织,2,组织类型)")
  private Integer rangeType;
  /** 范围编码 */
  @ApiModelProperty(name = "rangeCode",notes = "范围编码", value= "范围编码")
  private String rangeCode;
  /** 范围名称 */
  @ApiModelProperty(name = "rangeName",notes = "范围名称", value= "范围名称")
  private String rangeName;
  /** 租户编号 */
  @ApiModelProperty(name = "tenantCode",notes = "租户编号", value= "租户编号")
  private String tenantCode;
  /** 数据业务状态（启用状态） */
  @ApiModelProperty(name = "enableStatus",notes = "数据业务状态（启用状态）", value= "数据业务状态（启用状态）")
  private String enableStatus;

}