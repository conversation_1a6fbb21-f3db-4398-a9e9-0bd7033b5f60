package com.biz.crm.tpm.business.budget.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 预算类型
 */
public enum BudgetTypeEnum {
  YEAR("year","年度预算"),
  ANALYSIS("analysis","分析预算"),
  INCOME("income","收入预算");

  BudgetTypeEnum(String code, String descr){
    this.code = code;
    this.descr = descr;
  }

  private String code;

  private String descr;

  public static BudgetTypeEnum findByCode(String code){
    if(StringUtils.isBlank(code)){
      return null;
    }
    for(BudgetTypeEnum type : values()){
      if(StringUtils.equals(type.getCode(),code)){
        return type;
      }
    }
    return null;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDescr() {
    return descr;
  }

  public void setDescr(String descr) {
    this.descr = descr;
  }
}
