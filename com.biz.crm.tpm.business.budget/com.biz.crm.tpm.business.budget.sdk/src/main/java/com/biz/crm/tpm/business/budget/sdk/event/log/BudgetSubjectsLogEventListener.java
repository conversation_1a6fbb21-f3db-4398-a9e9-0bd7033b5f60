package com.biz.crm.tpm.business.budget.sdk.event.log;

import com.biz.crm.tpm.business.budget.sdk.dto.BudgetSubjectsLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * <AUTHOR> BudgetSubjectsLogEventDto
 * @describe: 预算科目 业务日志事件接口
 * @createTime 2022年06月22日 11:09:00
 */
public interface BudgetSubjectsLogEventListener  extends NebulaEvent {


  /**
   * 创建事件
   */
  void onCreate(BudgetSubjectsLogEventDto eventDto);

  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(BudgetSubjectsLogEventDto eventDto);

  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(BudgetSubjectsLogEventDto eventDto);

  /**
   * 启用
   *
   * @param eventDto
   */
  void onEnable(BudgetSubjectsLogEventDto eventDto);

  /**
   * 禁用
   *
   * @param eventDto
   */
  void onDisable(BudgetSubjectsLogEventDto eventDto);
}
