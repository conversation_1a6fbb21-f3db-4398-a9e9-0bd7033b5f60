package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.BOOLEAN;

@Component
public class AuditMulitSetting extends AbstractStrategySetting {

  @Autowired
  private AutoAuditSetting autoAuditSetting;

  public AuditMulitSetting() {
    super("允许多次核销", AuditMulitSetting.class.getSimpleName());
    this.type = StrategySettingType.AUDIT.name();
    this.necessary = true;
    this.sortIndex = 1;
    this.valueType = BOOLEAN.getCode();
    this.parentCode = AutoAuditSetting.class.getSimpleName();
    this.tip = "勾选\"是\",则该活动细类的活动在核销时可分多次提交核销申请，直到核销金额达到最大可核销金额为止";
  }

  public AbstractStrategySetting getParent() {
    return autoAuditSetting;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList();
  }

  @Override
  public Object getDefaultValue() {
    return true;
  }
}
