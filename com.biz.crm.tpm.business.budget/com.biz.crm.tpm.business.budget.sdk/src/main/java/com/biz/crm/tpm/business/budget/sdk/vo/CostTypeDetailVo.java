package com.biz.crm.tpm.business.budget.sdk.vo;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Vo：TPM-活动明细;
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@ApiModel(value = "CostTypeDetails", description = "TPM-活动明细")
@Getter
@Setter
public class CostTypeDetailVo implements Serializable {
  /**
   * 操作的预授权标记
   */
  @ApiModelProperty(name = "操作的预授权标记", notes = "操作的预授权标记", value = "操作的预授权标记", required = true)
  private String prefix;
  /**
   * 主键
   */
  @ApiModelProperty(name = "id", notes = "主键", value = "主键")
  private String id;
  /**
   * 租户编号
   */
  @ApiModelProperty(name = "tenantCode", notes = "租户编号", value = "租户编号")
  private String tenantCode;
  /**
   * 修改人名称
   */
  @ApiModelProperty(name = "modifyName", notes = "修改人名称", value = "修改人名称")
  private String modifyName;
  /**
   * 修改人账号
   */
  @ApiModelProperty(name = "modifyAccount", notes = "修改人账号", value = "修改人账号")
  private String modifyAccount;
  /**
   * 修改时间
   */
  @ApiModelProperty(name = "modifyTime", notes = "修改时间", value = "修改时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date modifyTime;
  /**
   * 创建人名称
   */
  @ApiModelProperty(name = "createName", notes = "创建人名称", value = "创建人名称")
  private String createName;
  /**
   * 创建人账号
   */
  @ApiModelProperty(name = "createAccount", notes = "创建人账号", value = "创建人账号")
  private String createAccount;
  /**
   * 创建时间
   */
  @ApiModelProperty(name = "createTime", notes = "创建时间", value = "创建时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  /**
   * 数据状态（删除状态）
   */
  @ApiModelProperty(name = "delFlag", notes = "数据状态（删除状态）", value = "数据状态（删除状态）")
  private String delFlag;
  /**
   * 数据业务状态（启用状态）
   */
  @ApiModelProperty(name = "enableStatus", notes = "数据业务状态（启用状态）", value = "数据业务状态（启用状态）")
  private String enableStatus;

  /**
   * 备注
   */
  @ApiModelProperty(name = "remark", notes = "备注", value = "备注")
  private String remark;

  @ApiModelProperty("活动大类编码")
  private String categoryCode;

  @ApiModelProperty("活动大类名称")
  private String categoryName;

  /** 活动细类名称 */
  @ApiModelProperty(name = "活动细类名称",notes = "")
  private String detailName;

  /** 活动细类编号 */
  @ApiModelProperty(name = "活动细类编号",notes = "")
  private String detailCode;

  /** 分摊费用到产品(Y/N) */
  @ApiModelProperty(name = "分摊费用到产品(Y/N)",notes = "")
  private String isShareToProduct;

  /** 支付方式 */
  @ApiModelProperty(name = "支付方式",notes = "")
  private String payBy;

  /** 是否推送SFA */
  @ApiModelProperty(name = "是否推送SFA",notes = "")
  private String isSendSfa;

  /** 是否自动核销(Y/N) */
  @ApiModelProperty(name = "是否自动核销(Y/N)",notes = "")
  private String isAutoAudit;

  /** 是否多次核销(Y/N) */
  @ApiModelProperty(name = "是否多次核销(Y/N)",notes = "")
  private String isMultipleAudit;

  /** 是否计提(Y/N) */
  @ApiModelProperty(name = "是否计提(Y/N)",notes = "")
  private String isWithHolding;

  /**
   * 是否允许提前结案(Y/N)
   */
  @ApiModelProperty("是否允许提前结案(Y/N)")
  private String beAuditEarly;

  /** 核销有效期(天) */
  @ApiModelProperty(name = "核销有效期(天)",notes = "")
  private Integer auditValidityTime;

  /** 兑付有效期(天) */
  @ApiModelProperty(name = "兑付有效期(天)",notes = "")
  private Integer payValidityTime;

  /**
   * 支付方式
   */
  @ApiModelProperty(name = "payBys", notes = "支付方式", value = "支付方式")
  private Set<String> payBys;

  @ApiModelProperty(name = "settingStrategies", notes = "策略项信息", value = "策略项信息")
  private List<StrategySettingStruct> settingStrategies;
  //<---------------------------    申请表单    ---------------------------------------->
  /**
   * 申请表单编码
   */
  @ApiModelProperty(name = "applyFromCode", notes = "申请表单编码", value = "申请表单编码")
  private String applyFromCode;
  /**
   * 申请表单名称
   */
  @ApiModelProperty(name = "applyFromName", notes = "申请表单名称", value = "申请表单名称")
  private String applyFromName;
  /**
   * 申请表单内容
   */
  @ApiModelProperty(name = "applyFromCode", notes = "申请表单内容", value = "申请表单内容")
  private JSONObject applyFromBody;
  /**
   * 申请表单动态字段绑定
   */
  @ApiModelProperty(name = "applyFromExtend", notes = "申请表单动态字段绑定", value = "申请表单动态字段绑定")
  private JSONObject applyFromExtend;

  /**
   * 申请动态表单关联编码
   */
  @ApiModelProperty(name = "applyMappingCode", notes = "申请动态表单关联编码", value = "申请动态表单关联编码")
  private String applyMappingCode;
  //<---------------------------    执行表单    ---------------------------------------->
  /**
   * 执行表单编码
   */
  @ApiModelProperty(name = "executionFromCode", notes = "执行表单编码", value = "执行表单编码")
  private String executionFromCode;
  /**
   * 执行表单名称
   */
  @ApiModelProperty(name = "executionFromName", notes = "执行表单名称", value = "执行表单名称")
  private String executionFromName;
  /**
   * 执行表单内容
   */
  @ApiModelProperty(name = "executionFromCode", notes = "执行表单内容", value = "执行表单内容")
  private JSONObject executionFromBody;
  /**
   * 执行表单动态字段绑定
   */
  @ApiModelProperty(name = "executionFromExtend", notes = "执行表单动态字段绑定", value = "执行表单动态字段绑定")
  private JSONObject executionFromExtend;
  /**
   * 执行动态表单关联编码
   */
  @ApiModelProperty(name = "executionMappingCode", notes = "执行动态表单关联编码", value = "执行动态表单关联编码")
  private String executionMappingCode;
  //<---------------------------    核销表单    ---------------------------------------->
  /**
   * 核销表单编码
   */
  @ApiModelProperty(name = "budgetFromCode", notes = "核销表单编码", value = "核销表单编码")
  private String budgetFromCode;
  /**
   * 核销表单名称
   */
  @ApiModelProperty(name = "budgetFromName", notes = "核销表单名称", value = "核销表单名称")
  private String budgetFromName;
  /**
   * 核销表单内容
   */
  @ApiModelProperty(name = "budgetFromCode", notes = "核销表单内容", value = "核销表单内容")
  private JSONObject budgetFromBody;
  /**
   * 核销表单动态字段绑定
   */
  @ApiModelProperty(name = "budgetFromExtend", notes = "核销表单动态字段绑定", value = "核销表单动态字段绑定")
  private JSONObject budgetFromExtend;

  /**
   * 核销动态表单关联编码
   */
  @ApiModelProperty(name = "budgetMappingCode", notes = "核销动态表单关联编码", value = "核销动态表单关联编码")
  private String budgetMappingCode;

  /** 执行类型 */
  @ApiModelProperty("执行类型")
  private String executionType;

  /**
   * 活动大类关联
   */
  @ApiModelProperty(name = "costTypeMappings", notes = "活动大类关联", value = "活动大类关联")
  private Set<CostTypeMappingVo> costTypeMappings;
  @ApiModelProperty("回显编码，字符串，一般用于单选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这条数据")
  private String selectedCode;
  @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCodeList;

  @ApiModelProperty("活动材料")
  private List<CostTypeDetailCollectVo> collectList;
  @ApiModelProperty("核销材料")
  private List<CostTypeDetailCollectVo> approvalList;
}