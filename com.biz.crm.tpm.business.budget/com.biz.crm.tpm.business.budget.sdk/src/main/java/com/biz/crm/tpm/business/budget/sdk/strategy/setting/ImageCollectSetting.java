package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.STRING;

@Component
public class ImageCollectSetting extends AbstractStrategySetting {

  @Autowired
  private CollectActivityDataSetting collectActivityDataSetting;

  public ImageCollectSetting() {
    super("拍照要求", ImageCollectSetting.class.getSimpleName());
    this.type = StrategySettingType.ACTIVITY.name();
    this.necessary = true;
    this.sortIndex = 1;
    this.valueType = STRING.getCode();
    this.parentCode = CollectActivityDataSetting.class.getSimpleName();
    this.tip = "用于选择活动拍照的拍照要求";
  }

  public AbstractStrategySetting getParent() {
    return collectActivityDataSetting;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList();
  }

  @Override
  public Object getDefaultValue() {
    return null;
  }
}
