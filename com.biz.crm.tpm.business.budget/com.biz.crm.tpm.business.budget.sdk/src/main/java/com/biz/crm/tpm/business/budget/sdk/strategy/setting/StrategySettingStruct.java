package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.vo.UuidVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "StrategySettingStruct", description = "活动细类的具体策略项结构体")
public class StrategySettingStruct extends UuidVo {
  @ApiModelProperty(name = "关联的活动细类编码")
  private String detailCode;

  @ApiModelProperty("策略配置编码")
  private String settingManageCode;

  @ApiModelProperty("策略项的名称")
  private String name;

  @ApiModelProperty("策略项的编码")
  private String code;

  @ApiModelProperty("策略项的类型")
  private String type;

  @ApiModelProperty("是否必需")
  private Boolean necessary;

  @ApiModelProperty("排序值(值越小越靠前)")
  private Integer sortIndex;

  @ApiModelProperty("策略项的值类型")
  private String valueType;

  @ApiModelProperty("是否显示")
  private Boolean display;

  @ApiModelProperty("可能的时间格式")
  private String dateFormat;

  @ApiModelProperty("可能的提示语")
  protected String tip;

  @ApiModelProperty("策略项的值")
  private Object value;

  @ApiModelProperty("策略项可能的默认值")
  private Object defaultValue;

  @ApiModelProperty("策略项扩展值")
  private JSONObject extendValue;

  @ApiModelProperty("策略项父级code")
  private String parentCode;

  @ApiModelProperty("策略项的父级编码")
  private StrategySettingStruct parent;

  @ApiModelProperty("策略项的子级信息")
  private List<StrategySettingStruct> children;

  public String getSettingManageCode() {
    return settingManageCode;
  }

  public void setSettingManageCode(String settingManageCode) {
    this.settingManageCode = settingManageCode;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public Boolean getNecessary() {
    return necessary;
  }

  public void setNecessary(Boolean necessary) {
    this.necessary = necessary;
  }

  public Integer getSortIndex() {
    return sortIndex;
  }

  public void setSortIndex(Integer sortIndex) {
    this.sortIndex = sortIndex;
  }

  public String getValueType() {
    return valueType;
  }

  public void setValueType(String valueType) {
    this.valueType = valueType;
  }

  public Boolean getDisplay() {
    return display;
  }

  public void setDisplay(Boolean display) {
    this.display = display;
  }

  public Object getValue() {
    return value;
  }

  public void setValue(Object value) {
    this.value = value;
  }

  public StrategySettingStruct getParent() {
    return parent;
  }

  public void setParent(StrategySettingStruct parent) {
    this.parent = parent;
  }

  public List<StrategySettingStruct> getChildren() {
    return children;
  }

  public void setChildren(List<StrategySettingStruct> children) {
    this.children = children;
  }

  public String getParentCode() {
    return parentCode;
  }

  public void setParentCode(String parentCode) {
    this.parentCode = parentCode;
  }

  public String getDateFormat() {
    return dateFormat;
  }

  public void setDateFormat(String dateFormat) {
    this.dateFormat = dateFormat;
  }

  public String getDetailCode() {
    return detailCode;
  }

  public void setDetailCode(String detailCode) {
    this.detailCode = detailCode;
  }

  public String getTip() {
    return tip;
  }

  public void setTip(String tip) {
    this.tip = tip;
  }

  public Object getDefaultValue() {
    return defaultValue;
  }

  public void setDefaultValue(Object defaultValue) {
    this.defaultValue = defaultValue;
  }

  public JSONObject getExtendValue() {
    return extendValue;
  }

  public void setExtendValue(JSONObject extendValue) {
    this.extendValue = extendValue;
  }
}
