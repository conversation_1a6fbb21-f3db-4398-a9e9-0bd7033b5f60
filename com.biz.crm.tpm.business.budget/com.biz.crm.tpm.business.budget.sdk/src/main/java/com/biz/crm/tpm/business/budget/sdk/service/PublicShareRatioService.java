package com.biz.crm.tpm.business.budget.sdk.service;

import com.biz.crm.tpm.business.budget.sdk.vo.PublicShareRatioImportVo;
import com.biz.crm.tpm.business.budget.sdk.vo.PublicShareRatioVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public interface PublicShareRatioService {


    /**
     * 删除数据
     *
     * @param ids 主键结合
     */
    void delete(List<String> ids);

    /**
     * 批量创建
     *
     * @param list
     */
    void createBatch(List<PublicShareRatioImportVo> list);

    /**
     * 按唯一键查询
     *
     * @param codes
     * @return
     */
    List<PublicShareRatioVo> findByUniqueKeys(Set<String> codes);

    BigDecimal findRatioByCondition(List<String> orgCodes,String years);
}
