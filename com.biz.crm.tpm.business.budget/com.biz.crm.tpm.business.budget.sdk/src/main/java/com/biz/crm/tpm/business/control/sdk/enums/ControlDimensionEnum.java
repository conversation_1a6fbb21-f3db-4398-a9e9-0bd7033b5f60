package com.biz.crm.tpm.business.control.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 管控维度
 */
public enum ControlDimensionEnum {
    DEPARTMENT("department","部门"),
    CUSTOMER("customer","客户"),
    ITEM("item","品项"),
    PRODUCT("product","产品");

    ControlDimensionEnum(String code, String descr){
        this.code = code;
        this.descr = descr;
    }

    private String code;

    private String descr;

    public static ControlDimensionEnum findByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for(ControlDimensionEnum type : values()){
            if(StringUtils.equals(type.getCode(),code)){
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }
}
