package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.BOOLEAN;

@Component
public class ControlActivityExpensesSetting extends AbstractStrategySetting {

  @Autowired
  private PushSfaSetting pushSfaSetting;

  public ControlActivityExpensesSetting() {
    super("是否控制活动费用", ControlActivityExpensesSetting.class.getSimpleName());
    this.type = StrategySettingType.ACTIVITY.name();
    this.necessary = true;
    this.sortIndex = 4;
    this.valueType = BOOLEAN.getCode();
    this.parentCode = PushSfaSetting.class.getSimpleName();
    this.tip = "勾选\"是\",SFA端下单及协议金额不可超过活动申请金额";
  }

  public AbstractStrategySetting getParent() {
    return pushSfaSetting;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList();
  }

  @Override
  public Object getDefaultValue() {
    return true;
  }
}
