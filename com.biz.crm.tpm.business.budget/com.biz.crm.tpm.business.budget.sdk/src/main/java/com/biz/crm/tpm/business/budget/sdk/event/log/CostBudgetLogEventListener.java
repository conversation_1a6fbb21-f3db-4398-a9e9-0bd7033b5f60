package com.biz.crm.tpm.business.budget.sdk.event.log;

import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * <AUTHOR>
 * @describe: 费用预算 业务日志监听
 * @createTime 2022年06月23日 16:58:00
 */
public interface CostBudgetLogEventListener extends NebulaEvent {
  /**
   * 创建日志
   *
   * @param eventDto
   */
  void onCreate(CostBudgetLogEventDto eventDto);

  /**
   * 删除日志
   *
   * @param eventDto
   */
  void onDelete(CostBudgetLogEventDto eventDto);

  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(CostBudgetLogEventDto eventDto);

  /**
   * 启用
   *
   * @param eventDto
   */
  void onUpdateEnable(CostBudgetLogEventDto eventDto);

}
