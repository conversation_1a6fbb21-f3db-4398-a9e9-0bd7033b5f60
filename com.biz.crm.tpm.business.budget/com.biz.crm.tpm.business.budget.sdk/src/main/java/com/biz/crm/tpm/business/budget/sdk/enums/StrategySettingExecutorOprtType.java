package com.biz.crm.tpm.business.budget.sdk.enums;

import org.apache.commons.lang3.StringUtils;

public enum StrategySettingExecutorOprtType {
  //活动相关
  PUSH_INFO,
  COLLECT_DATA,
  SIGN_DISPLAY_AGREEMENT,
  COLLECT_ORDER,
  COLLECT_FIELDS,
  COLLECT_IMAGE,
  CONTROL_COSTS,
  //核销相关
  IS_AUDIT,
  AUTO_AUDIT,
  VALIDITY_TIME_AUDIT,
  EXCEEDING_AUDIT_RATE,
  MULTIPLE_AUDIT;

  public static StrategySettingExecutorOprtType getOprtType(String oprtType){
    if(StringUtils.isBlank(oprtType)){
      return null;
    }
    for(StrategySettingExecutorOprtType e : values()){
      if(StringUtils.equals(oprtType,e.name())){
        return e;
      }
    }
    return null;
  }
}
