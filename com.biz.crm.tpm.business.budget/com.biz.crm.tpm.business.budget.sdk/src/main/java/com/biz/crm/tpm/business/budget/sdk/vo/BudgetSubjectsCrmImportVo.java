package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2023-06-13 18:25
 * @description：
 */
@Data
@CrmExcelImport(startRow = 4)
public class BudgetSubjectsCrmImportVo extends CrmExcelVo {

  /** 预算科目编码 */
  @CrmExcelColumn("预算科目编码*")
  private String budgetSubjectsCode;
  /** 预算科目名称 */
  @CrmExcelColumn("预算科目名称*")
  private String budgetSubjectsName;
  /** 科目层级 */
  @CrmExcelColumn("科目层级")
  private String level;
  /** 上级预算科目编码 */
  @CrmExcelColumn("上级预算科目编码")
  private String parentBudgetSubjectsCode;
  /** 上级预算科目名称 */
  @CrmExcelColumn("上级预算科目名称")
  private String parentBudgetSubjectsName;
  /** 合作类型 */
  @CrmExcelColumn("合作类型*")
  private String cooperateType;
  /** 备注 */
  @CrmExcelColumn("备注")
  private String remark;


}
