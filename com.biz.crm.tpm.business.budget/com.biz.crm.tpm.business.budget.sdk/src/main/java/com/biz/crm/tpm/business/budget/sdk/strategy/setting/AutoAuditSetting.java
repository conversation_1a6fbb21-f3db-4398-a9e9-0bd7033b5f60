package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.BOOLEAN;

@Component
public class AutoAuditSetting extends AbstractStrategySetting {

  @Autowired
  private AuditSetting auditSetting;
  @Autowired
  private AuditRatioSetting auditRatioSetting;
  @Autowired
  private AuditMulitSetting auditMulitSetting;
  @Autowired
  private AuditEffectiveDateSetting auditEffectiveDateSetting;
  @Autowired
  private AuditDataSetting auditDataSetting;

  public AutoAuditSetting() {
    super("是否自动核销", AutoAuditSetting.class.getSimpleName());
    this.type = StrategySettingType.AUDIT.name();
    this.necessary = true;
    this.sortIndex = 1;
    this.valueType = BOOLEAN.getCode();
    this.parentCode = AuditSetting.class.getSimpleName();
    this.tip = "勾选\"是\",则该活动细类的活动在审批通过后自动生成审批通过状态的核销申请，并且为完全核销，“否”则需手动提交核销申请";
  }

  public AbstractStrategySetting getParent() {
    return auditSetting;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList(auditMulitSetting,auditRatioSetting,auditEffectiveDateSetting,auditDataSetting);
  }

  @Override
  public Object getDefaultValue() {
    return true;
  }
}
