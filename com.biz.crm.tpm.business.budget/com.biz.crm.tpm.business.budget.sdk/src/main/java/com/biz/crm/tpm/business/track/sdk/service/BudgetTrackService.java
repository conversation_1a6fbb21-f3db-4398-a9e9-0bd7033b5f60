package com.biz.crm.tpm.business.track.sdk.service;

import com.biz.crm.tpm.business.track.sdk.dto.BudgetTrackDto;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;

import java.util.List;

/**
 * 预算跟踪
 */
public interface BudgetTrackService {

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    BudgetTrackVo findByCode(String code);

    /**
     * 查询费用预算跟踪
     * @param budgetControlCodes
     * @return
     */
    List<BudgetTrackVo> findByBudgetControlCodes(List<String> budgetControlCodes);

    /**
     * 修改税率
     *
     * @param dto
     */
    void updateRate(BudgetTrackDto dto);

    /**
     * 费率计算
     *
     * @param dto
     */
    void calculateRate(BudgetTrackDto dto);

    /**
     * 预算跟踪生成
     */
    void generateBudgetTrack();

    /**
     * 预算跟踪按年月生成
     */
    void generateBudgetTrackYearMonth(String yearMonthLy, String controlCode);

    void generateBudgetTrackByCodes(List<String> codes);
}
