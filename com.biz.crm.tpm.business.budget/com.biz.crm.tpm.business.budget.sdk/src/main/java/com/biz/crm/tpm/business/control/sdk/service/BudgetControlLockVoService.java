package com.biz.crm.tpm.business.control.sdk.service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 预算管控加锁
 */
public interface BudgetControlLockVoService {

    /**
     * 根据管控编码加锁
     *
     * @param controlCode
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 14:21
     **/
    boolean lock(String controlCode, TimeUnit timeUnit, int time);

    /**
     * 根据管控编码批量加锁
     *
     * @param controlCodeList
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 14:21
     **/
    boolean lock(List<String> controlCodeList, TimeUnit timeUnit, int time);

    /**
     * 根据管控编码批量加锁
     *
     * @param controlCodeList
     * @param timeUnit
     * @param lockTime
     * @param waiteTime
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 14:21
     **/
    boolean lock(List<String> controlCodeList, TimeUnit timeUnit, int lockTime, int waiteTime);

    /**
     * 解锁
     *
     * @param controlCode
     * <AUTHOR>
     * @date 2022/11/1 14:46
     **/
    void unLock(String controlCode);

    /**
     * 批量解锁
     *
     * @param controlCodeList
     * <AUTHOR>
     * @date 2022/11/1 14:46
     **/
    void unLock(List<String> controlCodeList);
}
