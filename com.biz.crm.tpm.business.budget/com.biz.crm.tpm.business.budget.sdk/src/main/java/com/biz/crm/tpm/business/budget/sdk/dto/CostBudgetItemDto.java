package com.biz.crm.tpm.business.budget.sdk.dto;

import com.biz.crm.business.common.sdk.vo.TenantOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "CostBudgetItemDto", description = "费用预算明细参数信息")
public class CostBudgetItemDto extends TenantOpVo {
  @ApiModelProperty("费用预算编码")
  private String costBudgetCode;

  /**
   * 参见com.biz.tpm.business.budget.sdk.enums.CostBudgetOperateType
   */
  @ApiModelProperty("费用预算操作类型")
  private String operateType;

  @ApiModelProperty("业务编码")
  private String businessCode;

  @ApiModelProperty("业务明细编码")
  private String businessItemCode;

  @ApiModelProperty("操作前余额")
  private BigDecimal balance;

  @ApiModelProperty("最终可用余额")
  private BigDecimal finalBalance;

  @ApiModelProperty("当前操作金额")
  private BigDecimal operateAmount;

  @ApiModelProperty("来源")
  private String source;

  @ApiModelProperty("备注")
  private String itemRemark;

  @ApiModelProperty("排序值")
  private Integer sortIndex;



  @ApiModelProperty("期初金额")
  private BigDecimal initialAmount;

  @ApiModelProperty("预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("费用使用部门编码")
  private String departmentCode;

  @ApiModelProperty("费用使用部门名称")
  private String departmentName;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;
}
