package com.biz.crm.tpm.business.adjust.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum BudgetAdjustDetailFieldsEnum {
    customerName("customerName", "customerName", "客户名称", "String"),
    budgetSubjectName("budgetSubjectName", "budgetSubjectName", "预算科目名称", "String"),
    costCenterName("costCenterName", "costCenterName", "成本中心名称", "String"),
    yearMonthLy("yearMonthLy", "yearMonthLy", "预算年月", "String"),
    monthBalanceAmount("monthBalanceAmount", "monthBalanceAmount", "月度余额", "BigDecimal"),
    adjustAmount("adjustAmount", "adjustAmount", "转移金额", "BigDecimal"),
    remark("remark", "remark", "备注", "String"),
    ;

    private String key;
    private String dictCode;
    private String value;
    private String dateType;

    public static BudgetAdjustDetailFieldsEnum findByCode(String code) {
        Optional<BudgetAdjustDetailFieldsEnum> first = Stream.of(BudgetAdjustDetailFieldsEnum.values()).filter(t -> t.dictCode.equals(code)).findFirst();
        return first.orElse(null);
    }

}