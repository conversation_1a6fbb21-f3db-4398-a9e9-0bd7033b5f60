package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.BOOLEAN;

@Component
public class CollectActivityDataSetting extends AbstractStrategySetting {

  @Autowired
  private PushSfaSetting pushSfaSettingStruct;
  @Autowired
  private ImageCollectSetting imageCollectSetting;
  @Autowired
  private FieldCollectSetting fieldCollectSetting;

  public CollectActivityDataSetting() {
    super("是否采集活动数据", CollectActivityDataSetting.class.getSimpleName());
    this.type = StrategySettingType.ACTIVITY.name();
    this.necessary = true;
    this.sortIndex = 1;
    this.valueType = BOOLEAN.getCode();
    this.parentCode = PushSfaSetting.class.getSimpleName();
    this.tip = "勾选\"是\",SFA端可执行拍照和内容采集操作";
  }

  public AbstractStrategySetting getParent() {
    return pushSfaSettingStruct;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList(imageCollectSetting,fieldCollectSetting);
  }

  @Override
  public Object getDefaultValue() {
    return true;
  }
}
