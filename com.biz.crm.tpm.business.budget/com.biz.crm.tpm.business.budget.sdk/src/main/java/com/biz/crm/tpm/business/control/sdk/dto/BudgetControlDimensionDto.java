package com.biz.crm.tpm.business.control.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BudgetControlDimension", description = "预算管控维度")
public class BudgetControlDimensionDto extends TenantFlagOpDto {

  @ApiModelProperty("预算管控编码")
  private String controlCode;

  @ApiModelProperty("维度编码")
  private String dimensionCode;

  @ApiModelProperty("维度名称")
  private String dimensionName;
}
