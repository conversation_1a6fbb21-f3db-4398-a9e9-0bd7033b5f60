package com.biz.crm.tpm.business.budget.sdk.service;

import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetIncomeDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeImportVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;

import java.util.List;
import java.util.Set;

/**
 * 收入预算
 */
public interface CostBudgetIncomeService {

    /**
     * 编辑
     *
     * @param dto
     */
    void update(CostBudgetIncomeDto dto);

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    void delete(List<String> idList);

    /**
     * 启禁用
     */
    void updateEnableStatus(Set<String> ids, String enableStatus);

    /**
     * 根据费用预算编号确认
     *
     * @param codeList
     */
    void confirm(List<String> codeList);

    /**
     * 批量保存
     *
     * @param importVoList
     */
    void saveBatch(List<CostBudgetIncomeImportVo> importVoList);

    /**
     * 按条件查询
     *
     * @param dto
     */
    List<CostBudgetIncomeVo> findByDto(CostBudgetIncomeDto dto);

    List<CostBudgetIncomeVo> findListByCustomerCodesAndOrgCodesAndYears(List<String> customerCodes, List<String> costCenterCodes, List<String> deptCodes, String years);

    List<CostBudgetIncomeVo> findListByOrgCodesAndYears(List<String> orgCodes, String years);

    List<CostBudgetIncomeVo> findListByOrgCodesAndYearsValid(List<String> orgCodes, String years);

    List<CostBudgetIncomeVo> findListByOrgCodeAndItemCodesAndYears(List<String> regionOrgCodes, List<String> itemCodes, String years);

    List<CostBudgetIncomeVo> findIncomeAmountByOrgCodeAndYears(List<String> orgCodes, String years);

    List<CostBudgetIncomeVo> findListAllOrgCodeChildrenAndYears(List<String> orgCodes, String years, List<String> yearsList, List<String> channelDepartmentCodeList);

    List<CostBudgetIncomeVo> findListAllOrgCodeChildrenAndYearsValid(List<String> orgCodes, String years, List<String> yearsList, List<String> channelDepartmentCodeList);
}
