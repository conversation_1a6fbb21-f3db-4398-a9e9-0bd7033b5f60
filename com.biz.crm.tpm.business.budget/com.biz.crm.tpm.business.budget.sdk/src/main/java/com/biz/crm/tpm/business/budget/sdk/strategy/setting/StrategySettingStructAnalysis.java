package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import java.util.List;

public interface StrategySettingStructAnalysis {

  /**
   * 验证指定的策略项结构体(递归向下验证)
   */
  void validateBaseStruct(StrategySettingStruct struct, AbstractStrategySetting setting);

  /**
   * (递归向下)将指定的策略项信息，转化结构信息
   */
  StrategySettingStruct transferToStruct(AbstractStrategySetting setting);

  /**
   * 验证值信息（单项）
   */
  void validateValue(StrategySettingStruct struct);

  /**
   * 获取默认值（单项）
   */
  Object getDefaultValue(String code);

  void validateDisplaySpecialForStruct(StrategySettingStruct struct, List<StrategySettingStruct> specialStructs);

  /**
   * 把策略项值(字符串)转换为对象类型
   */
  Object transferValue(String value, String valueType);

  /**
   * 将由数据库查询出来的数据(扁平化的)，转换为树形结构
   */
  List<StrategySettingStruct> structTree(List<StrategySettingStruct> trees, List<StrategySettingStruct> sourceStructs, StrategySettingStruct currentStruct);
}
