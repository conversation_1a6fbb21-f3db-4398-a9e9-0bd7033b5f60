package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailSettingStrategyVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

public interface StrategySettingExecutor {
  default boolean matchedValue(StrategySettingStruct settingStrategy, Object standardValue){
    if(settingStrategy == null || standardValue == null){
      return false;
    }
    StrategySettingValueType strategySettingValueType = StrategySettingValueType.findByCode(settingStrategy.getValueType());
    if(strategySettingValueType == null){
      return false;
    }
    return StringUtils.equals(settingStrategy.getValue() == null ? null : settingStrategy.getValue().toString(),standardValue.toString());
  }

  default boolean hasStrategySetting(List<StrategySettingStruct> settingStrategies, String strategySettingCode){
    if(CollectionUtils.isEmpty(settingStrategies) || StringUtils.isBlank(strategySettingCode)){
      return false;
    }
    StrategySettingStruct settingStrategy = settingStrategies.stream().filter(e -> StringUtils.equals(strategySettingCode,e.getCode())).findFirst().orElse(null);
    return settingStrategy != null;
  }

  default Object getValue(List<StrategySettingStruct> settingStrategies, String strategySettingCode){
    if(CollectionUtils.isEmpty(settingStrategies) || StringUtils.isBlank(strategySettingCode)){
      return null;
    }
    StrategySettingStruct settingStrategy = settingStrategies.stream().filter(e -> StringUtils.equals(strategySettingCode,e.getCode())).findFirst().orElse(null);
    if(settingStrategy == null){
      return null;
    }
    return settingStrategy.getValue();
  }

  default void flatStrategySettingStruct(List<StrategySettingStruct> result, StrategySettingStruct settingStruct){
    if(settingStruct == null || result == null){
      return;
    }
    result.add(settingStruct);
    if(!CollectionUtils.isEmpty(settingStruct.getChildren())){
      for(StrategySettingStruct e : settingStruct.getChildren()){
        this.flatStrategySettingStruct(result,e);
      }
    }
  }
}
