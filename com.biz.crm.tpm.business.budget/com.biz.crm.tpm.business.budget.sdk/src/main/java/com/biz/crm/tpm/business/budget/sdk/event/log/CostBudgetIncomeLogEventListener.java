package com.biz.crm.tpm.business.budget.sdk.event.log;

import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetIncomeLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * <AUTHOR>
 * @describe: 费用预算 业务日志监听
 * @createTime 2022年06月23日 16:58:00
 */
public interface CostBudgetIncomeLogEventListener extends NebulaEvent {

  /**
   * 删除日志
   *
   * @param eventDto
   */
  void onDelete(CostBudgetIncomeLogEventDto eventDto);

  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(CostBudgetIncomeLogEventDto eventDto);

  /**
   * 启用事件
   *
   * @param dto
   */
  void onEnable(CostBudgetIncomeLogEventDto dto);

  /**
   * 禁用事件
   *
   * @param dto
   */
  void onDisable(CostBudgetIncomeLogEventDto dto);

}
