package com.biz.crm.tpm.business.budget.sdk.event.log;

import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeCategoryLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * <AUTHOR>
 * @describe: 活动大类 业务日志事件接口
 * @createTime 2022年06月22日 16:24:00
 */
public interface CostTypeCategoryLogEventListener extends NebulaEvent {

  /**
   * 创建事件
   */
  void onCreate(CostTypeCategoryLogEventDto eventDto);

  /**
   * 删除事件
   *
   * @param eventDto
   */
  void onDelete(CostTypeCategoryLogEventDto eventDto);

  /**
   * 更新日志
   *
   * @param eventDto
   */
  void onUpdate(CostTypeCategoryLogEventDto eventDto);

  /**
   * 启用
   *
   * @param eventDto
   */
  void onEnable(CostTypeCategoryLogEventDto eventDto);

  /**
   * 禁用
   *
   * @param eventDto
   */
  void onDisable(CostTypeCategoryLogEventDto eventDto);
}
