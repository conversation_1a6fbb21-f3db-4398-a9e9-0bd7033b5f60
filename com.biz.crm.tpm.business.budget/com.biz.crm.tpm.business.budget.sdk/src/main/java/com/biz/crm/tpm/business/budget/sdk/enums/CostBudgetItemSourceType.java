package com.biz.crm.tpm.business.budget.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 费用预算明细来源类型
 */
public enum CostBudgetItemSourceType {
  YEAR_COST_BUDGET("year_cost_budget","年度预算"),
  ANALYSIS_COST_BUDGET("analysis_cost_budget","分析预算"),
  INCOME_COST_BUDGET("income_cost_budget","收入预算"),
  ORDINARY_ACTIVITY("ordinary_activity","活动申请"),
  PROJECT_ACTIVITY("project_activity","项目活动"),
  SCHEMA_ACTIVITY("schema_activity","方案活动"),
  ADJUST("ADJUST","预算调整"),
  AUDIT("audit","费用核销");

  CostBudgetItemSourceType(String code, String descr){
    this.code = code;
    this.descr = descr;
  }

  private String code;

  private String descr;

  public static boolean contains(String descr){
    if(StringUtils.isBlank(descr)){
      return false;
    }
    for(CostBudgetItemSourceType type : values()){
      if(StringUtils.equals(type.getDescr(),descr)){
        return true;
      }
    }
    return false;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDescr() {
    return descr;
  }

  public void setDescr(String descr) {
    this.descr = descr;
  }
}
