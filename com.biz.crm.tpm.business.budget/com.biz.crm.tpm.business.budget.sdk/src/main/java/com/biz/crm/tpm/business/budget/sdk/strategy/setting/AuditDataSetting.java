package com.biz.crm.tpm.business.budget.sdk.strategy.setting;

import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType.STRING;

@Component
public class AuditDataSetting extends AbstractStrategySetting {

  @Autowired
  private AutoAuditSetting autoAuditSetting;

  public AuditDataSetting() {
    super("核销资料", AuditDataSetting.class.getSimpleName());
    this.type = StrategySettingType.AUDIT.name();
    this.necessary = true;
    this.sortIndex = 4;
    this.valueType = STRING.getCode();
    this.parentCode = AutoAuditSetting.class.getSimpleName();
    this.tip = "用于配置核销申请页面资料上传的上传要求";
  }

  public AbstractStrategySetting getParent() {
    return autoAuditSetting;
  }

  public List<AbstractStrategySetting> getChildren() {
    return Lists.newArrayList();
  }

  @Override
  public Object getDefaultValue() {
    return null;
  }
}
