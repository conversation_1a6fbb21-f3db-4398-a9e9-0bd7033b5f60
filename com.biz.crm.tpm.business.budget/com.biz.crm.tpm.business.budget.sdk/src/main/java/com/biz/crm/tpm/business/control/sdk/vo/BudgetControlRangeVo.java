package com.biz.crm.tpm.business.control.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BudgetControlRange", description = "预算管控范围")
public class BudgetControlRangeVo extends TenantFlagOpVo {

  @ApiModelProperty("预算管控编码")
  private String controlCode;

  @ApiModelProperty("管控部门明细编码")
  private String controlDetailCode;

  @ApiModelProperty("范围类型")
  private String rangeType;

  @ApiModelProperty("部门编码")
  private String departmentCode;

  @ApiModelProperty("部门名称")
  private String departmentName;

  @ApiModelProperty("部门层级")
  private Integer levelNum;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

}
