package com.biz.crm.tpm.business.budget.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class ApprovalCollectTypeDto extends TenantDto {

  @ApiModelProperty("采集编码")
  private String collectCode;


  @ApiModelProperty("类型编码")
  private String typeCode;


  @ApiModelProperty("类型名称")
  private String typeName;
}
