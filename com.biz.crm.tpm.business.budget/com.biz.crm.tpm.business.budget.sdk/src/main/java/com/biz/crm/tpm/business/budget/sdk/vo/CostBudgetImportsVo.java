package com.biz.crm.tpm.business.budget.sdk.vo;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月09日 14:18:00
 */
@Data
@CrmExcelImport(startRow = 4)
public class CostBudgetImportsVo extends CrmExcelVo {


  @CrmExcelColumn("年月")
  private String yearMonthLy;

  @CrmExcelColumn("部门编码")
  private String departmentOneCode;

  @CrmExcelColumn("部门名称")
  private String departmentOneName;

  @CrmExcelColumn("部门层级")
  private String levelNumStr;
  private Integer levelNum;

  @CrmExcelColumn("公司编码")
  private String companyCode;
  private String companyName;

  @CrmExcelColumn("成本中心编码")
  private String costCenterCode;

  @CrmExcelColumn("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("预算科目编码")
  @CrmExcelColumn("*预算科目编码")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  @CrmExcelColumn("预算科目名称")
  private String budgetSubjectName;

  @ApiModelProperty("客户ERP编码")
  @CrmExcelColumn("客户ERP编码")
  private String erpCode;
  private String customerCode;

  @ApiModelProperty("客户名称")
  @CrmExcelColumn("客户名称")
  private String customerName;

  @CrmExcelColumn("品项编码")
  private String itemCode;

  @CrmExcelColumn("品项名称")
  private String itemName;

  @ApiModelProperty("产品编码")
  @CrmExcelColumn("产品编码")
  private String productCode;

  @ApiModelProperty("产品名称")
  @CrmExcelColumn("产品名称")
  private String productName;

  @ApiModelProperty("期初金额")
  @CrmExcelColumn("*期初金额")
  private String initialAmountStr;
  private BigDecimal initialAmount;

  @ApiModelProperty("备注")
  @CrmExcelColumn("备注")
  private String remark;

  @ApiModelProperty("确认状态")
  private String confirmStatus;

  private String uniqueKey;
}
