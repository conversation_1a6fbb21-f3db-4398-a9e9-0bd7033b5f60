package com.biz.crm.tpm.business.budget.sdk.event;

import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;

/**
 * TPM-预算科目;(tpm_budget_subjects)相关的事件通知
 *
 * <AUTHOR> Keller
 * @date : 2022-5-18
 */
public interface BudgetSubjectsEventListener {

  /**
   * 当TPM-预算科目数据被创建时，该事件被触发
   *
   * @param budgetSubjectsVo
   */
  void onCreated(BudgetSubjectsVo budgetSubjectsVo);

  /**
   * 当TPM-预算科目数据被修改时，该事件被触发
   *
   * @param oldBudgetSubjectsVo
   * @param budgetSubjectsVo
   */
  void onUpdate(BudgetSubjectsVo oldBudgetSubjectsVo, BudgetSubjectsVo budgetSubjectsVo);

  /**
   * 当TPM-预算科目数据被删除时（逻辑删除），该事件被触发
   *
   * @param budgetSubjectsVo
   */
  void onDeleted(BudgetSubjectsVo budgetSubjectsVo);

  /**
   * 当TPM-预算科目数据被启用时，该事件被触发
   *
   * @param budgetSubjectsVo
   */
  void onEnable(BudgetSubjectsVo budgetSubjectsVo);

  /**
   * 当TPM-预算科目数据被禁用时，该事件被触发
   *
   * @param budgetSubjectsVo
   */
  void onDisable(BudgetSubjectsVo budgetSubjectsVo);
}