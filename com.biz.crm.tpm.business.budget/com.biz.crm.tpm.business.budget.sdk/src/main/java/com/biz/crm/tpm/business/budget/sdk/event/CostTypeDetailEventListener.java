package com.biz.crm.tpm.business.budget.sdk.event;


import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;

/**
 * TPM-活动明细;(tpm_cost_type_details)相关的事件通知
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
public interface CostTypeDetailEventListener {
  
  /**
   * 当TPM-活动明细数据被创建时，该事件被触发
   * @param costTypeDetailVo
   */
  default void onCreated(CostTypeDetailVo costTypeDetailVo){};
  /**
   * 当TPM-活动明细数据被修改时，该事件被触发
   * @param oldCostTypeDetailVo 修改前数据
   * @param costTypeDetailVo  修改后数据
   */
  default void onUpdate(CostTypeDetailVo oldCostTypeDetailVo, CostTypeDetailVo costTypeDetailVo){};
  /**
   * 当TPM-活动明细数据被删除时（逻辑删除），该事件被触发
   * @param costTypeDetailVo
   */
  default void onDeleted(CostTypeDetailVo costTypeDetailVo){};
  /**
   * 当TPM-活动明细数据被启用时，该事件被触发
   * @param costTypeDetailVo
   */
  default void onEnable(CostTypeDetailVo costTypeDetailVo){};
  /**
   * 当TPM-活动明细数据被禁用时，该事件被触发
   * @param costTypeDetailVo
   */
  default void onDisable(CostTypeDetailVo costTypeDetailVo){};
}