package com.biz.crm.tpm.business.budget.sdk.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.vo.TenantOpVo;
import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.field.annotation.Validate;
import com.biz.crm.common.form.sdk.field.validate.NotBlankValidateStrategy;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 动态表单model抽象基础类类
 *
 * <AUTHOR>
 */
@Data
public abstract class AbstractDynamicTemplateModel extends TenantOpVo implements DynamicForm {
  private static final long serialVersionUID = -9137642152343032974L;

  @ApiModelProperty("执行计划业务编码")
  @TableField(value = "parent_code")
  @DynamicField(fieldName = "执行计划业务编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String parentCode;

  @ApiModelProperty("步骤业务编码stepCode")
  @TableField(value = "dynamic_key")
  @DynamicField(fieldName = "步骤业务编码stepCode", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String dynamicKey;

}
