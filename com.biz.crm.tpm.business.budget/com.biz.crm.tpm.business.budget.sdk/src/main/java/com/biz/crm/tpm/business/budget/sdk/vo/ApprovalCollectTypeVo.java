package com.biz.crm.tpm.business.budget.sdk.vo;

import com.bizunited.nebula.common.vo.TenantVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class ApprovalCollectTypeVo extends TenantVo {

  @ApiModelProperty("采集编码")
  private String collectCode;


  @ApiModelProperty("类型编码")
  private String typeCode;


  @ApiModelProperty("类型名称")
  private String typeName;
}
