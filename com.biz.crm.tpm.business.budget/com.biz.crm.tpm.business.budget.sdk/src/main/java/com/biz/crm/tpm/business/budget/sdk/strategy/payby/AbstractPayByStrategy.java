package com.biz.crm.tpm.business.budget.sdk.strategy.payby;

import com.biz.crm.tpm.business.budget.sdk.strategy.account.AccountPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.validator.GenericValidator;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 描述：</br>支付方式策略基础抽象类
 *
 * <AUTHOR>
 * @date 2022/6/20
 */
public abstract class AbstractPayByStrategy implements PayByStrategy {

  /**
   * 支付方式验证器
   */
  protected final Collection<GenericValidator> genericValidators = Lists.newArrayList();

  protected final Collection<AccountPayByStrategy> accountPayByStrategies =Lists.newArrayList();

  public void addGenericValidator(GenericValidator genericValidator) {
    genericValidators.add(genericValidator);
  }

  /**
   * 获取验证器
   */
  public Collection<GenericValidator> getValidators() {
    return this.genericValidators;
  }

  public void addAccountPayByStrategy(AccountPayByStrategy accountPayByStrategy) {
    accountPayByStrategies.add(accountPayByStrategy);
  }
  /**
   * 获取支付策略实现
   */
  public Collection<AccountPayByStrategy> getAccountPayByStrategies() {
    return this.accountPayByStrategies;
  }
}
