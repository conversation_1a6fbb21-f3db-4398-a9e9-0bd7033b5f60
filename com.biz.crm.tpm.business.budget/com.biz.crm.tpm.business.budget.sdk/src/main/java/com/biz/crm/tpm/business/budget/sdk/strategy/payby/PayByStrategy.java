package com.biz.crm.tpm.business.budget.sdk.strategy.payby;

import com.biz.crm.tpm.business.budget.sdk.strategy.account.AccountPayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.validator.GenericValidator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.core.Ordered;

import java.util.Collection;

/**
 * 描述：</br>活动细类中的支付方式策略，具体的活动绑定预算科目后可以通过该策略来实现对预算的周期进行控制
 * <pre>
 *   默认实现的策略包括：
 *   1、现金
 *   2、转预付/账扣
 *   3、货补
 *   4、折扣
 *   新增活动收，活动的申请金额根当前费用预算（渠道、组织、科目均匹配）进行验证.
 *   根据getOrder()方法确定排列顺序，数字越小优先级越高
 * </pre>
 *
 * <AUTHOR>
 * @date 2022/5/18
 */
public interface PayByStrategy extends Ordered {
  /**
   * 支付方式编号（需要全局唯一）
   *
   * @return
   */
  String getCode();

  /**
   * 支付方式名称
   *
   * @return
   */
  String getName();

  /**
   * 获取验证器
   */
  @JsonIgnore
  Collection<GenericValidator> getValidators();

  /**
   * 获取上账策略实现
   * @return
   */
  @JsonIgnore
  Collection<AccountPayByStrategy> getAccountPayByStrategies();
}
