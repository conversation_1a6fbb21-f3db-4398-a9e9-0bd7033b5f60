package com.biz.crm.tpm.business.budget.sdk.service;

import com.biz.crm.tpm.business.budget.sdk.dto.OperateBudgetDto;

import java.util.List;

public interface CostBudgetOperateVoService {


    /**
     * 批量预算调整-默认检查预算占用情况
     *
     * @param operateList
     */
    void operateBudget(List<OperateBudgetDto> operateList);

    /**
     *
     * 批量预算调整-指定是否检查预算占用情况
     *
     * @param operateList
     * @param checkOccupy 是否检查预算占用情况，true 检查 false 不检查
     */
    void operateBudget(List<OperateBudgetDto> operateList, boolean checkOccupy);
}
