package com.biz.crm.tpm.business.budget.sdk.strategy.form;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.core.Ordered;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 描述：</br>表单事件策略
 * 表单数据转换为json object传递
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
public interface FormEventStrategy extends Ordered {

  /**
   * 获取表单事件编号 （需要全局唯一）
   *
   * @return
   */
  String getCode();

  /**
   * 获取表单事件名称
   *
   * @return
   */
  String getName();

  /**
   * 获取表单配置属性信息
   *
   * @return
   */
  Class<? extends FormProperties> getFormPropertiesInfo();

  /**
   * 数据保存后处理逻辑
   *
   * @param businessCode 活动细类编号
   * @param data         业务数据
   * @param params       扩展参数
   */
  <T extends DynamicForm> Object handle(String businessCode, T data, Map<String, ?> params);

  /**
   * 是否匹配该策略
   *
   * @param businessCode
   * @param data
   * @param params
   */
  <T extends DynamicForm> boolean match(String businessCode, T data, Map<String, ?> params);

  /**
   * 根据业务编号保存表单事件绑定配置属性
   *
   * @param businessCode
   */
  <T extends FormProperties> void saveProperties(String businessCode, JSONObject data);

  /**
   * 根据业务编号获取表单事件绑定配置属性
   *
   * @param businessCode
   */
  <T extends FormProperties> T findProperties(String businessCode);

  /**
   * 执行优先级
   *
   * @return
   */
  @Override
  default int getOrder() {
    return LOWEST_PRECEDENCE;
  }

  /**
   * 默认提供的的检查方法
   *
   * @param properties
   * @param <T>
   */
  default <T extends FormProperties> void validateProperties(T properties) {
    if (properties == null) {
      return;
    }
    List<Field> fieldList = new ArrayList<>();
    Class<?> clazz = properties.getClass();
    while (clazz != null) {
      fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
      clazz = clazz.getSuperclass();
    }
    fieldList.forEach(field -> {
      FormPropertiesField formPropertiesField = field.getAnnotation(FormPropertiesField.class);
      if (formPropertiesField != null) {
        if (formPropertiesField.required()) {
          field.setAccessible(true);
          Object value = ReflectionUtils.getField(field, properties);
          Validate.isTrue(!ObjectUtils.isEmpty(value), "必填字段[%s]为空，请检查", formPropertiesField.name());
        }
      }
    });
  }
}
