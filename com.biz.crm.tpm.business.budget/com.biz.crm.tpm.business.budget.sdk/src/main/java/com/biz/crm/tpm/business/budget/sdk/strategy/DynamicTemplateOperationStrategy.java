package com.biz.crm.tpm.business.budget.sdk.strategy;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.tpm.business.budget.sdk.model.AbstractDynamicTemplateModel;

/**
 * 动态表单模板操作策略类
 *
 * <AUTHOR>
 * @date 2022/6/22
 */
public interface DynamicTemplateOperationStrategy {
  /**
   * 必须设定动态表单的业务编码（全系统唯一）。</br>
   * 那么可以采用“子系统_功能名”的方式，来对动态表单进行标识
   *
   * @return
   */
  String dynamicFormCode();

  /**
   * 要求对应的动态表单进行创建操作时，该动态表单模型的操作策略的onDynamicTemplatesCreate方法将会被触发
   *
   * @param parentCode 父级业务唯一编码
   * @param dynamicKey 步骤编码
   * @param jsonObject 本次添加操作将进行添加的动态表单数据集合
   */
  void onDynamicTemplateCreate(JSONObject jsonObject, String dynamicKey, String parentCode);

  /**
   * 要求对应的动态表单进行修改操作时，该动态表单模型的操作策略的onDynamicTemplatesModify方法将会被触发
   *
   * @param parentCode 父级业务唯一编码
   * @param dynamicKey 步骤编码
   * @param jsonObject 本次添加操作将进行添加的动态表单数据集合
   */
  void onDynamicTemplateModify(JSONObject jsonObject, String dynamicKey, String parentCode);

  /**
   * 需要关联完善对应的动态表单数据时，该动态表单模型的操作策略的findByParentCode方法将会被触发
   *
   * @param dynamicKey Map性质的属性/字段，其Key值
   * @param parentCode 这些动态表单数据对应上层主业务模型的业务编号
   * @return
   */
  Object findByParentCode(String dynamicKey, String parentCode);
}
