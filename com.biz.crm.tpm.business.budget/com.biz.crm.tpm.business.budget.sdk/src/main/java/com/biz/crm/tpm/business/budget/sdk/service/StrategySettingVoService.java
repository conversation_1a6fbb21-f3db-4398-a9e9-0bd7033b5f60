package com.biz.crm.tpm.business.budget.sdk.service;

import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;

import java.util.List;

public interface StrategySettingVoService {

//  List<CostTypeDetailSettingItemVo> findByParentKey(String parentKey);

//  List<CostTypeDetailSettingItemVo> findBySettingCodeAndType(String settingCode, String type);

  List<StrategySettingStruct> findBySettingManageCode(String settingManageCode);

  void create(List<StrategySettingStruct> items);

  void update(List<StrategySettingStruct> items);

//  void deleteBySettingCodeAndType(String settingCode, String type);
}
