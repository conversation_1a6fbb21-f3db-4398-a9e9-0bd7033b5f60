package com.biz.crm.tpm.business.control.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BudgetControlSubject", description = "预算管控科目")
public class BudgetControlSubjectDto extends TenantFlagOpDto {

  @ApiModelProperty("预算管控编码")
  private String controlCode;

  /** 管控科目明细编码 */
  @ApiModelProperty("管控科目明细编码")
  private String controlDetailCode;

  /** 预算科目编码 */
  @ApiModelProperty("预算科目编码")
  private String budgetSubjectsCode;

  /** 预算科目名称 */
  @ApiModelProperty("预算科目名称")
  private String budgetSubjectsName;

  /** 上级预算科目编码 */
  @ApiModelProperty("上级预算科目编码")
  private String parentBudgetSubjectsCode;

  /** 上级预算科目名称 */
  @ApiModelProperty("上级预算科目名称")
  private String parentBudgetSubjectsName;

  /** 科目层级 */
  @ApiModelProperty("科目层级")
  private String level;

  /** 合作类型 */
  @ApiModelProperty(name = "cooperateType",notes = "合作类型", value = "合作类型")
  private String cooperateType;


  /**
   * 是否选中，0否1是
   */
  @ApiModelProperty("是否选中，0否1是")
  private String checked;
}
