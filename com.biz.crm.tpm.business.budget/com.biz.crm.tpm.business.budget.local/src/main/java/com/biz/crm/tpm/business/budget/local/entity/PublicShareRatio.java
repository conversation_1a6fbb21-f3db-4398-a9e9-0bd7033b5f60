package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_public_share_ratio")
@Table(name = "tpm_public_share_ratio", indexes = {
        @Index(name = "public_share_ratio_index1", columnList = "public_share_code")})
@ApiModel(value = "PublicShareRatio", description = "公摊费率")
@org.hibernate.annotations.Table(appliesTo = "tpm_public_share_ratio", comment = "公摊费率")
public class PublicShareRatio extends TenantFlagOpEntity {

    @ApiModelProperty("公摊费率编码")
    @Column(name = "public_share_code", columnDefinition = "varchar(32) comment '公摊费率编码'")
    private String publicShareCode;

    @ApiModelProperty("年")
    @Column(name = "year_str", length = 4, columnDefinition = "int(10) COMMENT '年'")
    private String yearStr;

    @ApiModelProperty("月")
    @Column(name = "month_str", length = 4, columnDefinition = "int(10) COMMENT '月'")
    private String monthStr;

    @ApiModelProperty("部门编码")
    @Column(name = "department_code", columnDefinition = "varchar(32) comment '部门编码'")
    private String departmentCode;

    @ApiModelProperty("部门名称")
    @Column(name = "department_name", columnDefinition = "varchar(128) comment '部门名称'")
    private String departmentName;

    @ApiModelProperty("部门层级")
    @Column(name = "level_num", columnDefinition = "int COMMENT '部门层级'")
    private Integer levelNum;

    @ApiModelProperty("费率")
    @Column(name = "ratio", columnDefinition = "decimal(20,6) COMMENT '费率'")
    private BigDecimal ratio;

    @ApiModelProperty("费率（百分比）")
    @Column(name = "ratio_str", columnDefinition = "varchar(32) COMMENT '费率（百分比）'")
    private String ratioStr;

    @ApiModelProperty("唯一标识")
    @Column(name = "unique_key", columnDefinition = "VARCHAR(128) COMMENT '唯一标识'")
    private String uniqueKey;

}
