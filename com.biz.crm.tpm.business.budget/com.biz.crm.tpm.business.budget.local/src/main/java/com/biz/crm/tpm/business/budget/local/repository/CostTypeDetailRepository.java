package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeDetail;
import com.biz.crm.tpm.business.budget.local.mapper.CostTypeDetailMapper;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailsDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * TPM-活动明细;(tpm_cost_type_details)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@Component
@Deprecated
public class CostTypeDetailRepository extends ServiceImpl<CostTypeDetailMapper, CostTypeDetail> {
    @Autowired
    private CostTypeDetailMapper costTypeDetailMapper;

    /**
     * 批量根据id禁用
     *
     * @param enable
     * @param ids
     */
    public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
        UpdateWrapper<CostTypeDetail> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("enable_status", enable.getCode());
        updateWrapper.eq("tenant_code", TenantUtils.getTenantCode());
        updateWrapper.in("id", ids);
        this.update(updateWrapper);
    }

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    public Page<CostTypeDetailVo> findByConditions(Pageable pageable, CostTypeDetailsDto dto) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 50);
        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<CostTypeDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return costTypeDetailMapper.findByConditions(page, dto);
    }


    public List<String> findListByCondition(CostTypeDetailsDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        return costTypeDetailMapper.findListByCondition(dto);
    }

    public List<CostTypeDetail> findPayBysByDetailCodes(List<String> detailCodes) {
        return this.lambdaQuery()
                .in(CostTypeDetail::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostTypeDetail::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(CostTypeDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(CostTypeDetail::getDetailCode, detailCodes)
                .list();
    }


    /**
     * 根据id集合获取详情集合
     *
     * @param ids ID集合
     * @return List<CostTypeDetail>
     */
    public List<CostTypeDetail> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(CostTypeDetail::getId, ids)
                .eq(CostTypeDetail::getTenantCode, tenantCode)
                .eq(CostTypeDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据编号集合获取详情集合
     *
     * @param codes 编号集合
     * @return List<CostTypeDetail>
     */
    public List<CostTypeDetail> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(CostTypeDetail::getDetailCode, codes)
                .eq(CostTypeDetail::getTenantCode, tenantCode)
                .eq(CostTypeDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据编号与租户编号获取对象
     *
     * @param code
     * @return
     */
    public CostTypeDetail findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .eq(CostTypeDetail::getDetailCode, code)
                .eq(CostTypeDetail::getTenantCode, tenantCode)
                .eq(CostTypeDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
    }

    /**
     * 根据启用状态与租户编号查询对象
     *
     * @param enableStatus
     * @return
     */
    public List<CostTypeDetail> findByEnableStatus(String enableStatus) {
        String tenantCode = TenantUtils.getTenantCode();
        if (StringUtils.isBlank(enableStatus)) {
            return this.lambdaQuery()
                    .eq(CostTypeDetail::getTenantCode, tenantCode)
                    .eq(CostTypeDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
        }
        return this.lambdaQuery()
                .eq(CostTypeDetail::getEnableStatus, enableStatus)
                .eq(CostTypeDetail::getTenantCode, tenantCode)
                .eq(CostTypeDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }

    /**
     * 根据主键逻辑删除数据
     *
     * @param idList 主键ID列表
     * @return
     */
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        String tenantCode = TenantUtils.getTenantCode();
        UpdateWrapper<CostTypeDetail> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("del_flag", DelFlagStatusEnum.DELETE.getCode());
        updateWrapper.eq("tenant_code", tenantCode);
        updateWrapper.in("id", idList);
        return this.update(updateWrapper);
    }

    /**
     * 通过示例编号查询是否已经在细类中使用
     *
     * @param code
     * @return
     */
//  public Integer countByApprovalCollect(String code) {
//    String tenantCode = TenantUtils.getTenantCode();
//    return this.lambdaQuery().eq(CostTypeDetail::getTenantCode, tenantCode)
//            .and(i -> i.like(CostTypeDetail::getPhotoRequire, code)
//                    .or()
//                    .like(CostTypeDetail::getAuditRequire, code)).count();
//
//  }

    /**
     * 根据条件查询活动细类编号
     *
     * @param dto
     * @return
     */
    public Set<String> findCodeByCondition(CostTypeDetailsDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        return costTypeDetailMapper.findCodeByCondition(dto);
    }

    public CostTypeDetail findByIdAndTenantCode(String id, String tenantCode) {
        return this.lambdaQuery()
                .eq(CostTypeDetail::getTenantCode, tenantCode)
                .in(CostTypeDetail::getId, id)
                .one();
    }

    public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
        this.lambdaUpdate()
                .eq(CostTypeDetail::getTenantCode, tenantCode)
                .in(CostTypeDetail::getId, ids)
                .remove();
    }

    public List<CostTypeDetail> findListByNames(List<String> nameList, String tenantCodes) {
        return this.lambdaQuery()
                .in(CostTypeDetail::getDetailName, nameList)
                .eq(CostTypeDetail::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(CostTypeDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostTypeDetail::getTenantCode, tenantCodes)
                .list();
    }

    public List<CostTypeDetail> findListByPushSfa() {
        return this.lambdaQuery()
                .eq(CostTypeDetail::getIsSendSfa, BooleanEnum.TRUE.getCapital())
                .eq(CostTypeDetail::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(CostTypeDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostTypeDetail::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    /**
     * 根据大类编号集合获取详情集合
     *
     * @param categoryCodes 大类编号集合
     * @return List<CostTypeDetail>
     */
    public List<CostTypeDetail> findByCategoryCodes(List<String> categoryCodes) {
        if (CollectionUtils.isEmpty(categoryCodes)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(CostTypeDetail::getCategoryCode, categoryCodes)
                .eq(CostTypeDetail::getTenantCode, tenantCode)
                .eq(CostTypeDetail::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }
}