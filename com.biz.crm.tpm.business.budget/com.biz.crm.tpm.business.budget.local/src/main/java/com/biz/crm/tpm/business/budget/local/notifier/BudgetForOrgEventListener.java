package com.biz.crm.tpm.business.budget.local.notifier;

import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventBatchDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventDto;
import com.biz.crm.mdm.business.org.sdk.event.OrgEventListener;
import com.biz.crm.tpm.business.budget.local.service.CostTypeCategoryRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述：</br>预算科目监听MDM中组织变化引起的组织数据失效的问题
 *
 * <AUTHOR>
 * @date 2022/5/27
 */
@Component
public class BudgetForOrgEventListener implements OrgEventListener {
  @Autowired
  private CostTypeCategoryRangeService costTypeCategoryRangeService;

  @Override
  public void onDeleteBatch(OrgEventBatchDto orgEventBatchDto) {
    if (orgEventBatchDto == null) {
      return;
    }
    List<OrgEventDto> orgEventDtos = orgEventBatchDto.getOrgEventDtoList();
    if (CollectionUtils.isEmpty(orgEventDtos)) {
      return;
    }
    Set<String> orgCodes = orgEventDtos.stream().map(OrgEventDto::getOrgCode).collect(Collectors.toSet());
    this.costTypeCategoryRangeService.deleteByRangeCodes(orgCodes);
  }

  @Override
  public void onEnableBatch(OrgEventBatchDto orgEventBatchDto) {
    if (orgEventBatchDto == null) {
      return;
    }
    List<OrgEventDto> orgEventDtos = orgEventBatchDto.getOrgEventDtoList();
    if (CollectionUtils.isEmpty(orgEventDtos)) {
      return;
    }
    Set<String> orgCodes = orgEventDtos.stream().map(OrgEventDto::getOrgCode).collect(Collectors.toSet());
    this.costTypeCategoryRangeService.updateEnableStatus(orgCodes, EnableStatusEnum.ENABLE.getCode());
  }

  @Override
  public void onDisableBatch(OrgEventBatchDto orgEventBatchDto) {
    if (orgEventBatchDto == null) {
      return;
    }
    List<OrgEventDto> orgEventDtos = orgEventBatchDto.getOrgEventDtoList();
    if (CollectionUtils.isEmpty(orgEventDtos)) {
      return;
    }
    Set<String> orgCodes = orgEventDtos.stream().map(OrgEventDto::getOrgCode).collect(Collectors.toSet());
    this.costTypeCategoryRangeService.updateEnableStatus(orgCodes, EnableStatusEnum.DISABLE.getCode());
  }

  @Override
  public void onUpdate(OrgEventDto orgEventDto) {
    // 无业务处理，在组织结构数据变化后原数据不进行同步更新，和产品陈科确认过后的需求
  }

  @Override
  public void onDelete(List<String> orgCodes) {
    // 无业务处理
  }
}
