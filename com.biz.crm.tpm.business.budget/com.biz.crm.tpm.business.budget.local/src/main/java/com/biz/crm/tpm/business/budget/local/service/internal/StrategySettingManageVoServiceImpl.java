package com.biz.crm.tpm.business.budget.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.budget.local.entity.StrategySettingManage;
import com.biz.crm.tpm.business.budget.local.repository.StrategySettingManageRepository;
import com.biz.crm.tpm.business.budget.sdk.dto.StrategySettingManageDto;
import com.biz.crm.tpm.business.budget.sdk.dto.StrategySettingManageLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.event.StrategySettingManageEventListener;
import com.biz.crm.tpm.business.budget.sdk.event.log.StrategySettingManageLogEventListener;
import com.biz.crm.tpm.business.budget.sdk.service.StrategySettingManageVoService;
import com.biz.crm.tpm.business.budget.sdk.service.StrategySettingVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.AbstractStrategySetting;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStructAnalysis;
import com.biz.crm.tpm.business.budget.sdk.vo.StrategySettingManageVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.constant.StrategySettingConstant.STRATEGY_SETTING_RULE_CODE;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType.ACTIVITY;
import static com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingType.AUDIT;

@Service
public class StrategySettingManageVoServiceImpl implements StrategySettingManageVoService {

  @Autowired
  private StrategySettingManageRepository strategySettingManageRepository;
  @Autowired
  private StrategySettingVoService strategySettingVoService;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired(required = false)
  private List<AbstractStrategySetting> abstractStrategySettings;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired
  private StrategySettingStructAnalysis strategySettingStructAnalysis;
  @Autowired(required = false)
  private List<StrategySettingManageEventListener> strategySettingManageEventListeners;

  @Override
  @Transactional
  public void create(StrategySettingManageDto dto) {
    this.createValidation(dto);
    List<StrategySettingManage> all = strategySettingManageRepository.list();
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    //策略配置有且只有一个生效
    if(CollectionUtils.isEmpty(all)){
      dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    }else{
      dto.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
    }
    StrategySettingManage entity = nebulaToolkitService.copyObjectByWhiteList(dto, StrategySettingManage.class, HashSet.class, ArrayList.class);
    entity.setTenantCode(TenantUtils.getTenantCode());
    strategySettingManageRepository.save(entity);
    dto.setCode(entity.getCode());
    //保存明细
    List<StrategySettingStruct> items = Lists.newArrayList();
    if(!CollectionUtils.isEmpty(dto.getActivitySettings())){
      dto.getActivitySettings().forEach(e -> this.fillSettingManageCode(entity.getCode(),e));
      items.addAll(dto.getActivitySettings());
    }
    if(!CollectionUtils.isEmpty(dto.getAuditSettings())){
      dto.getAuditSettings().forEach(e -> this.fillSettingManageCode(entity.getCode(),e));
      items.addAll(dto.getAuditSettings());
    }
    strategySettingVoService.create(items);

    //是否使用
    if(dto.getEnableOprt() != null && dto.getEnableOprt() == Boolean.TRUE){
      this.enableStatus(entity.getId());
    }

    //新增业务日志
    StrategySettingManageVo strategySettingManageVo = nebulaToolkitService.copyObjectByWhiteList(entity,StrategySettingManageVo.class,HashSet.class,ArrayList.class);
    strategySettingManageVo.setActivitySettings(items.stream().filter(e -> StringUtils.equals(e.getType(),ACTIVITY.name())).collect(Collectors.toList()));
    strategySettingManageVo.setAuditSettings(items.stream().filter(e -> StringUtils.equals(e.getType(),AUDIT.name())).collect(Collectors.toList()));
    StrategySettingManageLogEventDto logEventDto = new StrategySettingManageLogEventDto();
    logEventDto.setOriginal(null);
    logEventDto.setNewest(strategySettingManageVo);
    SerializableBiConsumer<StrategySettingManageLogEventListener, StrategySettingManageLogEventDto> onCreate =
            StrategySettingManageLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(logEventDto, StrategySettingManageLogEventListener.class, onCreate);
  }

  @Override
  @Transactional
  public void update(StrategySettingManageDto dto) {
    this.updateValidation(dto);
    StrategySettingManage dbSetting = strategySettingManageRepository.findByIdAndTenantCode(dto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(dbSetting, "根据指定的id键值，未能获取到相应信息");
    if(!CollectionUtils.isEmpty(strategySettingManageEventListeners)){
      for(StrategySettingManageEventListener listener : strategySettingManageEventListeners){
        listener.onUpdate(dbSetting.getCode());
      }
    }
    dbSetting.setName(dto.getName());
    dbSetting.setTenantCode(TenantUtils.getTenantCode());
    strategySettingManageRepository.saveOrUpdate(dbSetting);
    //保存明细
    List<StrategySettingStruct> items = Lists.newArrayList();
    if(!CollectionUtils.isEmpty(dto.getActivitySettings())){
      dto.getActivitySettings().forEach(e -> this.fillSettingManageCode(dbSetting.getCode(),e));
      //活动相关的数据
      items.addAll(dto.getActivitySettings());
    }
    if(!CollectionUtils.isEmpty(dto.getAuditSettings())){
      dto.getAuditSettings().forEach(e -> this.fillSettingManageCode(dbSetting.getCode(),e));
      //核销相关的数据
      items.addAll(dto.getAuditSettings());
    }
    strategySettingVoService.update(items);

    //是否使用
    if(dto.getEnableOprt() != null && dto.getEnableOprt() == Boolean.TRUE){
      this.enableStatus(dbSetting.getId());
    }

    //编辑业务日志
    StrategySettingManageVo strategySettingManageVo = nebulaToolkitService.copyObjectByWhiteList(dto,StrategySettingManageVo.class,HashSet.class,ArrayList.class);
    StrategySettingManageVo newestStrategySettingManageVo = nebulaToolkitService.copyObjectByWhiteList(dbSetting,StrategySettingManageVo.class,HashSet.class,ArrayList.class);
    newestStrategySettingManageVo.setActivitySettings(items.stream().filter(e -> StringUtils.equals(e.getType(),ACTIVITY.name())).collect(Collectors.toList()));
    newestStrategySettingManageVo.setAuditSettings(items.stream().filter(e -> StringUtils.equals(e.getType(),AUDIT.name())).collect(Collectors.toList()));
    StrategySettingManageLogEventDto logEventDto = new StrategySettingManageLogEventDto();
    logEventDto.setOriginal(strategySettingManageVo);
    logEventDto.setNewest(newestStrategySettingManageVo);
    SerializableBiConsumer<StrategySettingManageLogEventListener, StrategySettingManageLogEventDto> onUpdate =
            StrategySettingManageLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(logEventDto, StrategySettingManageLogEventListener.class, onUpdate);
  }

  @Override
  public Page<StrategySettingManageVo> findByConditions(Pageable pageable, StrategySettingManageDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    if (dto == null) {
      dto = new StrategySettingManageDto();
    }
    if (StringUtils.isBlank(dto.getTenantCode())) {
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    if (StringUtils.isBlank(dto.getDelFlag())) {
      dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    }
    return strategySettingManageRepository.findByConditions(pageable,dto);
  }

  @Override
  public List<StrategySettingManageVo> findByIds(Set<String> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Lists.newArrayList();
    }
    List<StrategySettingManage> settings = strategySettingManageRepository.findByIds(ids);
    if(CollectionUtils.isEmpty(settings)){
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(settings, StrategySettingManage.class, StrategySettingManageVo.class,HashSet.class,ArrayList.class));
  }

  @Override
  public StrategySettingManageVo findByCode(String code) {
    if(StringUtils.isBlank(code)){
      return null;
    }
    StrategySettingManage strategySettingManage = strategySettingManageRepository.findByCodeAndTenantCode(code, TenantUtils.getTenantCode());
    if(strategySettingManage == null){
      return null;
    }
    List<StrategySettingStruct> itemVos = strategySettingVoService.findBySettingManageCode(strategySettingManage.getCode());
    StrategySettingManageVo settingVo = nebulaToolkitService.copyObjectByWhiteList(strategySettingManage, StrategySettingManageVo.class, HashSet.class, ArrayList.class);
    if(CollectionUtils.isEmpty(itemVos)){
      return settingVo;
    }

    List<StrategySettingStruct> activityItems = itemVos.stream().filter(e -> StringUtils.equals(e.getType(), ACTIVITY.name())).collect(Collectors.toList());
    List<StrategySettingStruct> activityTrees = Lists.newArrayList();
    this.buildItemTrees(activityItems,activityTrees);
    settingVo.setActivitySettings(activityTrees);

    List<StrategySettingStruct> auditItems = itemVos.stream().filter(e -> StringUtils.equals(e.getType(), AUDIT.name())).collect(Collectors.toList());
    List<StrategySettingStruct> auditTrees = Lists.newArrayList();
    this.buildItemTrees(auditItems,auditTrees);
    settingVo.setAuditSettings(auditTrees);
    return settingVo;
  }

  @Override
  public List<StrategySettingStruct> structs(Set<String> codes) {
    if(CollectionUtils.isEmpty(codes) || CollectionUtils.isEmpty(abstractStrategySettings)){
      return Lists.newArrayList();
    }

    List<AbstractStrategySetting> settings = abstractStrategySettings.stream().filter(e -> codes.contains(e.getCode())).collect(Collectors.toList());
    if(CollectionUtils.isEmpty(settings)){
      return Lists.newArrayList();
    }

    List<StrategySettingStruct> result = Lists.newArrayList();
    for(AbstractStrategySetting setting : settings){
      StrategySettingStruct struct = strategySettingStructAnalysis.transferToStruct(setting);
      if(struct != null){
        result.add(struct);
      }
    }
    return result;
  }

  @Override
  public StrategySettingManageVo findEnabled() {
    StrategySettingManage strategySettingManage = strategySettingManageRepository.findEnabled();
    if(strategySettingManage == null){
      return null;
    }
    List<StrategySettingStruct> strategySettingVos = strategySettingVoService.findBySettingManageCode(strategySettingManage.getCode());
    StrategySettingManageVo strategySettingManageVo = nebulaToolkitService.copyObjectByWhiteList(strategySettingManage, StrategySettingManageVo.class, HashSet.class, ArrayList.class);
    if(CollectionUtils.isEmpty(strategySettingVos)){
      return null;
    }

    List<StrategySettingStruct> activityItems = strategySettingVos.stream().filter(e -> StringUtils.equals(e.getType(), ACTIVITY.name())).collect(Collectors.toList());
    List<StrategySettingStruct> activityTrees = Lists.newArrayList();
    this.buildItemTrees(activityItems,activityTrees);
    strategySettingManageVo.setActivitySettings(activityTrees);

    List<StrategySettingStruct> auditItems = strategySettingVos.stream().filter(e -> StringUtils.equals(e.getType(), AUDIT.name())).collect(Collectors.toList());
    List<StrategySettingStruct> auditTrees = Lists.newArrayList();
    this.buildItemTrees(auditItems,auditTrees);
    strategySettingManageVo.setAuditSettings(auditTrees);
    return strategySettingManageVo;
  }

  @Override
  @Transactional
  public void enableStatus(String id) {
    Validate.notBlank(id, "策略配置id主键不能为空");
    StrategySettingManage strategySettingManage = strategySettingManageRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    Validate.notNull(strategySettingManage, "根据提供的策略配置主键id【%s】，未能获取到相应信息",id);
    Validate.isTrue(StringUtils.equals(strategySettingManage.getEnableStatus(),EnableStatusEnum.DISABLE.getCode()),"策略配置【%s】已是使用状态",strategySettingManage.getName());
    List<StrategySettingManage> all = strategySettingManageRepository.list();
    Validate.notEmpty(all,"未能获取到任何策略配置信息，请检查");
    List<StrategySettingManage> needDisableds = all.stream().filter(e -> !StringUtils.equals(e.getCode(),strategySettingManage.getCode())).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(needDisableds)){
      needDisableds.forEach(e -> e.setEnableStatus(EnableStatusEnum.DISABLE.getCode()));
      needDisableds.forEach(e -> e.setTenantCode(TenantUtils.getTenantCode()));
      strategySettingManageRepository.saveOrUpdateBatch(needDisableds);
    }
    strategySettingManage.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    strategySettingManage.setTenantCode(TenantUtils.getTenantCode());
    strategySettingManageRepository.saveOrUpdate(strategySettingManage);
  }

  @Override
  @Transactional
  public void disabled(Set<String> ids) {
    Validate.notEmpty(ids, "禁用时，策略配置主键id集合不能为空");
    List<StrategySettingManage> strategySettingManages = strategySettingManageRepository.findByIds(ids);
    Validate.notEmpty(strategySettingManages, "根据指定的策略配置主键id集合，未能获取到相应信息");
    strategySettingManages.forEach(e -> e.setEnableStatus(EnableStatusEnum.DISABLE.getCode()));
    strategySettingManages.forEach(e -> e.setTenantCode(TenantUtils.getTenantCode()));
    strategySettingManageRepository.saveOrUpdateBatch(strategySettingManages);
  }

  private void createValidation(StrategySettingManageDto settingDto){
    this.validateBase(settingDto);
    Validate.isTrue(StringUtils.isBlank(settingDto.getId()),"新增时，策略配置主键id不能有值");
    settingDto.setId(null);
    if(StringUtils.isBlank(settingDto.getTenantCode())) {
      settingDto.setTenantCode(TenantUtils.getTenantCode());
    }
    // redis生成策略配置编码，编码规则为XLCL+年月日+5位顺序数。每天都从00001开始编
    List<String> codeList = this.generateCodeService.generateCode(STRATEGY_SETTING_RULE_CODE, 1);
    Validate.notEmpty(codeList, "添加信息时，生成策略配置编码失败！");
    settingDto.setCode(codeList.get(0));
    String pattern = "^[A-Z]{1}[A-Z0-9]*$";
    Validate.matchesPattern(settingDto.getCode(), pattern, "编码只能是字母和数字构成，且首字母不能是数字，最终编码都将被大写");
    StrategySettingManageVo strategySettingManageVo = this.findByCode(settingDto.getCode());
    Validate.isTrue(strategySettingManageVo == null, "策略配置编码重复");
    boolean exist = strategySettingManageRepository.existByName(settingDto.getName());
    Validate.isTrue(!exist, "策略配置名称【%s】已存在",settingDto.getName());
  }

  private void updateValidation(StrategySettingManageDto settingDto){
    this.validateBase(settingDto);
    Validate.notBlank(settingDto.getId(),"更新时，策略配置主键id必须有值");
    StrategySettingManage setting = strategySettingManageRepository.findByName(settingDto.getName());
    Validate.isTrue(setting == null || StringUtils.equals(setting.getName(),settingDto.getName()), "策略配置名称【%s】已存在",settingDto.getName());
  }

  private void validateBase(StrategySettingManageDto settingDto){
    Validate.notNull(settingDto,"活动细类策略配置信息不能为空");
    Validate.notBlank(settingDto.getName(),"策略配置名称不能为空");
    Validate.isTrue(!(CollectionUtils.isEmpty(settingDto.getActivitySettings()) && CollectionUtils.isEmpty(settingDto.getAuditSettings())),"活动执行相关的策略与核销相关的策略信息不能同时为空");
  }

  private void buildItemTrees(List<StrategySettingStruct> items, List<StrategySettingStruct> trees){
    List<StrategySettingStruct> tops = items.stream().filter(e -> StringUtils.isBlank(e.getParentCode())).collect(Collectors.toList());
    if(CollectionUtils.isEmpty(tops)){
      return;
    }
    for(StrategySettingStruct top : tops){
      trees.add(top);
      this.buildItemTreeNodes(top,items);
    }
  }

  private void buildItemTreeNodes(StrategySettingStruct current, List<StrategySettingStruct> allItems){
    if(current == null){
      return;
    }
    List<StrategySettingStruct> children = allItems.stream().filter(e -> StringUtils.equals(e.getParentCode(),current.getCode())).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(children)){
      current.setChildren(children);
      for(StrategySettingStruct child : current.getChildren()){
        this.buildItemTreeNodes(child,allItems);
      }
    }
  }

  private void fillSettingManageCode(String settingManageCode, StrategySettingStruct item){
    item.setSettingManageCode(settingManageCode);
    if(!CollectionUtils.isEmpty(item.getChildren())){
      for(StrategySettingStruct child : item.getChildren()){
        this.fillSettingManageCode(settingManageCode,child);
      }
    }
  }
}
