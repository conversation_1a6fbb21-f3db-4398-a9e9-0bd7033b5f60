package com.biz.crm.tpm.business.control.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_budget_control_subject")
@Table(name = "tpm_budget_control_subject",
        indexes = {
                @Index(name = "tpm_budget_control_subject_idx1", columnList = "control_code",unique = false),
                @Index(name = "tpm_budget_control_subject_idx2", columnList = "control_detail_code",unique = false),
                @Index(name = "tpm_budget_control_subject_idx3", columnList = "budget_subjects_code",unique = false),
        })
@ApiModel(value = "BudgetControlSubject", description = "预算管控科目")
@org.hibernate.annotations.Table(appliesTo = "tpm_budget_control_subject", comment = "预算管控科目")
public class BudgetControlSubject extends TenantFlagOpEntity {

  @ApiModelProperty("预算管控编码")
  @Column(name = "control_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '预算管控编码'")
  private String controlCode;

  /** 管控科目明细编码 */
  @ApiModelProperty("管控科目明细编码")
  @Column(name = "control_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '管控科目明细编码 '")
  private String controlDetailCode;

  /** 预算科目编码 */
  @ApiModelProperty("预算科目编码")
  @Column(name = "budget_subjects_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
  private String budgetSubjectsCode;

  /** 预算科目名称 */
  @ApiModelProperty("预算科目名称")
  @Column(name = "budget_subjects_name", length = 128, columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;

  /** 上级预算科目编码 */
  @ApiModelProperty("上级预算科目编码")
  @Column(name = "parent_budget_subjects_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '上级预算科目编码 '")
  private String parentBudgetSubjectsCode;

  /** 上级预算科目名称 */
  @ApiModelProperty("上级预算科目名称")
  @Column(name = "parent_budget_subjects_name", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '上级预算科目名称 '")
  private String parentBudgetSubjectsName;

  /** 科目层级 */
  @ApiModelProperty("科目层级")
  @Column(name = "level", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '科目层级 '")
  private String level;

  /** 合作类型 */
  @ApiModelProperty(name = "cooperateType",notes = "合作类型", value = "合作类型")
  @Column(name = "cooperate_type", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '合作类型 '")
  private String cooperateType;

}
