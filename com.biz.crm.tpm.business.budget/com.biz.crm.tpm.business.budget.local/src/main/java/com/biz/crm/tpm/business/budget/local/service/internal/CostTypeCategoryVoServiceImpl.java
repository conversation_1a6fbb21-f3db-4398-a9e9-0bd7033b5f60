package com.biz.crm.tpm.business.budget.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.user.feign.feign.UserVoFeign;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeCategory;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeCategoryRange;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeDetail;
import com.biz.crm.tpm.business.budget.local.repository.CostTypeCategoryRepository;
import com.biz.crm.tpm.business.budget.local.repository.CostTypeDetailRepository;
import com.biz.crm.tpm.business.budget.local.service.CostTypeCategoryRangeService;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeCategoryDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeCategoryLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.event.CostTypeCategoryEventListener;
import com.biz.crm.tpm.business.budget.sdk.event.log.CostTypeCategoryLogEventListener;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.enums.RedisKeys.GENERATE_PREFIX;

/**
 * TPM-活动大类;(tpm_cost_type_category)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
@Service("costTypeCategoryVoService")
public class CostTypeCategoryVoServiceImpl implements CostTypeCategoryVoService {
    @Autowired
    private CostTypeCategoryRepository costTypeCategoryRepository;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired
    private RedisMutexService redisMutexService;
    @Autowired
    private BudgetSubjectsVoService budgetSubjectsVoService;
    @Autowired
    private CostTypeCategoryRangeService costTypeCategoryRangeService;
    @Autowired(required = false)
    private List<CostTypeCategoryEventListener> costTypeCategoryEventListeners;
    @Autowired
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired
    private LoginUserService loginUserService;
    @Autowired
    private UserVoFeign userVoFeign;
    @Autowired
    private CostTypeDetailRepository costTypeDetailRepository;

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     * @return
     */
    @Override
    public Page<CostTypeCategoryVo> findByConditions(Pageable pageable, CostTypeCategoryDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, Integer.MAX_VALUE));
        if (Objects.isNull(dto)) {
            dto = new CostTypeCategoryDto();
        }
        List<String> selectedCodeList =
                Optional.ofNullable(dto.getSelectedCodeList()).orElse(new ArrayList<>());
        if (StringUtils.isNotEmpty(dto.getSelectedCode())) {
            selectedCodeList.add(dto.getSelectedCode());
        }
        if (!CollectionUtils.isEmpty(selectedCodeList)) {
            dto.setSelectedCodeList(selectedCodeList);
        }
        if (dto.isSelect()) {
            Set<CostTypeCategoryRange> schemeRangeVos = this.costTypeCategoryRangeService.findAll();
            List<String> schemeOrgCodes = schemeRangeVos.stream().filter(item -> item.getRangeType() == 1).map(CostTypeCategoryRange::getRangeCode).distinct().collect(Collectors.toList());
            List<String> schemeOrgTypes = schemeRangeVos.stream().filter(item -> item.getRangeType() == 2).map(CostTypeCategoryRange::getRangeCode).distinct().collect(Collectors.toList());
            UserIdentity loginDetails = loginUserService.getLoginUser();
            Result<UserVo> userVoOrgCodeResult = userVoFeign.findRelationByUserNameAndOrgCodesOrOrgTypes(loginDetails.getAccount(), schemeOrgCodes, schemeOrgTypes);
            UserVo userVo = userVoOrgCodeResult.getResult();
            if (userVo != null) {
                dto.setOrgCodes(userVo.getRelationOrgCodes());
                dto.setOrgTypes(userVo.getRelationOrgTypes());
            }
        }
        if (StringUtils.isNotBlank(dto.getBudgetSubjectsCodeTree())) {
            List<BudgetSubjectsVo> subjectsVos = budgetSubjectsVoService.findByEnableStatus(EnableStatusEnum.ENABLE.getCode());
            Set<String> codes = new HashSet<>();
            codes.add(dto.getBudgetSubjectsCodeTree());
            findSonSubject(subjectsVos, codes);
            dto.setBudgetSubjectsCodeSet(codes);
        }
        return this.costTypeCategoryRepository.findByConditions(pageable, dto);
    }

    /**
     * 获取预算项目下所有的下级项目
     *
     * @param subjectsVos
     * @param codes
     */
    private void findSonSubject(List<BudgetSubjectsVo> subjectsVos, Set<String> codes) {
        List<BudgetSubjectsVo> sonList = subjectsVos.stream().filter(e -> codes.contains(e.getParentBudgetSubjectsCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sonList)) {
            return;
        } else {
            codes.addAll(sonList.stream().map(e -> e.getBudgetSubjectsCode()).collect(Collectors.toSet()));
            subjectsVos.removeAll(sonList);
            findSonSubject(subjectsVos, codes);
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @Override
    public CostTypeCategoryVo findById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        CostTypeCategory costTypeCategory = this.costTypeCategoryRepository.findByIdAndTenantCode(id, TenantUtils.getTenantCode());
        if (costTypeCategory == null) {
            return null;
        }
        CostTypeCategoryVo costTypeCategoryVo = this.nebulaToolkitService.copyObjectByWhiteList(costTypeCategory, CostTypeCategoryVo.class, LinkedHashSet.class, ArrayList.class);
        return costTypeCategoryVo;
    }

    /**
     * 通过编号查询单条数据
     *
     * @param code 主键
     * @return 单条数据
     */
    @Override
    public CostTypeCategoryVo findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        CostTypeCategory costTypeCategory = this.costTypeCategoryRepository.findByCode(code);
        if (costTypeCategory == null) {
            return null;
        }
        CostTypeCategoryVo costTypeCategoryVo = this.nebulaToolkitService.copyObjectByWhiteList(costTypeCategory, CostTypeCategoryVo.class, LinkedHashSet.class, ArrayList.class);
        return costTypeCategoryVo;
    }

    /**
     * 新增数据
     *
     * @param costTypeCategoryVo 实体对象
     * @return 新增结果
     */
    @Transactional
    @Override
    public CostTypeCategoryVo create(CostTypeCategoryVo costTypeCategoryVo) {
        costTypeCategoryVo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        this.createValidate(costTypeCategoryVo);
        //获取预算科目，设置预算科目名称
        BudgetSubjectsVo budgetSubjectsVo = budgetSubjectsVoService.findByCode(costTypeCategoryVo.getBudgetSubjectsCode());
        costTypeCategoryVo.setBudgetSubjectsName(budgetSubjectsVo.getBudgetSubjectsName());
        CostTypeCategory costTypeCategory = this.nebulaToolkitService.copyObjectByWhiteList(costTypeCategoryVo, CostTypeCategory.class, LinkedHashSet.class, ArrayList.class);
        costTypeCategory.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        costTypeCategory.setTenantCode(TenantUtils.getTenantCode());
        this.costTypeCategoryRepository.saveOrUpdate(costTypeCategory);

        costTypeCategoryVo.setId(costTypeCategory.getId());
        if (!CollectionUtils.isEmpty(costTypeCategoryEventListeners)) {
            for (CostTypeCategoryEventListener costTypeCategoryEventListener : costTypeCategoryEventListeners) {
                costTypeCategoryEventListener.onCreated(costTypeCategoryVo);
            }
        }
        //新增业务日志
        CostTypeCategoryLogEventDto logEventDto = new CostTypeCategoryLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(costTypeCategoryVo);
        SerializableBiConsumer<CostTypeCategoryLogEventListener, CostTypeCategoryLogEventDto> onCreate =
                CostTypeCategoryLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, CostTypeCategoryLogEventListener.class, onCreate);
        return costTypeCategoryVo;
    }

    /**
     * 修改新据
     *
     * @param costTypeCategoryVo 实体对象
     * @return 修改结果
     */
    @Transactional
    @Override
    public CostTypeCategoryVo update(CostTypeCategoryVo costTypeCategoryVo) {
        this.updateValidate(costTypeCategoryVo);
        CostTypeCategory costTypeCategory = this.costTypeCategoryRepository.findByIdAndTenantCode(costTypeCategoryVo.getId(), TenantUtils.getTenantCode());
        CostTypeCategoryVo oldCostTypeCategoryVo = this.nebulaToolkitService.copyObjectByWhiteList(costTypeCategory, CostTypeCategoryVo.class, LinkedHashSet.class, ArrayList.class);
        Validate.notNull(costTypeCategory, "修改数据不存在，请检查！");
        // 具体修改的内容
        costTypeCategory.setCategoryName(costTypeCategoryVo.getCategoryName());
        costTypeCategory.setBudgetSubjectsCode(costTypeCategoryVo.getBudgetSubjectsCode());
        BudgetSubjectsVo budgetSubjectsVo = budgetSubjectsVoService.findByCode(costTypeCategoryVo.getBudgetSubjectsCode());
        costTypeCategory.setBudgetSubjectsName(budgetSubjectsVo.getBudgetSubjectsName());
        costTypeCategory.setRemark(costTypeCategoryVo.getRemark());
        costTypeCategory.setTenantCode(TenantUtils.getTenantCode());
        this.costTypeCategoryRepository.saveOrUpdate(costTypeCategory);

        if (!CollectionUtils.isEmpty(costTypeCategoryEventListeners)) {
            for (CostTypeCategoryEventListener costTypeCategoryEventListener : costTypeCategoryEventListeners) {
                costTypeCategoryEventListener.onUpdate(oldCostTypeCategoryVo, costTypeCategoryVo);
            }
        }
        //更新业务日志
        CostTypeCategoryLogEventDto logEventDto = new CostTypeCategoryLogEventDto();
        logEventDto.setOriginal(oldCostTypeCategoryVo);
        logEventDto.setNewest(costTypeCategoryVo);
        SerializableBiConsumer<CostTypeCategoryLogEventListener, CostTypeCategoryLogEventDto> onUpdate =
                CostTypeCategoryLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, CostTypeCategoryLogEventListener.class, onUpdate);
        return costTypeCategoryVo;
    }

    /**
     * 删除数据
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @Transactional
    @Override
    public void delete(List<String> ids) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
        List<CostTypeCategory> costTypeCategorys = this.costTypeCategoryRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(costTypeCategorys)) {
            return;
        }
        // 查询是否关联细类
        List<String> categoryCodes = costTypeCategorys.stream().map(CostTypeCategory::getCategoryCode).collect(Collectors.toList());
        List<CostTypeDetail> details = this.costTypeDetailRepository.findByCategoryCodes(categoryCodes);
        if (!CollectionUtils.isEmpty(details)) {
            Set<String> collect = details.stream().map(CostTypeDetail::getCategoryCode).collect(Collectors.toSet());
            throw new IllegalArgumentException(String.format("大类【%s】被细类关联，请先删除关联细类", String.join(",", collect)));
        }
        Collection<CostTypeCategoryVo> costTypeCategoryVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeCategorys, CostTypeCategory.class, CostTypeCategoryVo.class, LinkedHashSet.class, ArrayList.class);
        this.costTypeCategoryRepository.removeByIdsAndTenantCode(ids, TenantUtils.getTenantCode());
        if (!CollectionUtils.isEmpty(costTypeCategoryEventListeners)) {
            for (CostTypeCategoryEventListener costTypeCategoryEventListener : costTypeCategoryEventListeners) {
                for (CostTypeCategoryVo costTypeCategoryVo : costTypeCategoryVos) {
                    costTypeCategoryEventListener.onDeleted(costTypeCategoryVo);
                }
            }
        }
        //删除业务日志
        SerializableBiConsumer<CostTypeCategoryLogEventListener, CostTypeCategoryLogEventDto> onDelete =
                CostTypeCategoryLogEventListener::onDelete;
        for (CostTypeCategoryVo costTypeCategoryVo : costTypeCategoryVos) {
            CostTypeCategoryLogEventDto logEventDto = new CostTypeCategoryLogEventDto();
            logEventDto.setOriginal(costTypeCategoryVo);
            logEventDto.setNewest(null);
            this.nebulaNetEventClient.publish(logEventDto, CostTypeCategoryLogEventListener.class, onDelete);
        }
    }

    /**
     * 通过启用状态查询数据
     *
     * @param enableStatus 状态
     * @return 集合数据
     */
    @Override
    public List<CostTypeCategoryVo> findByEnableStatus(String enableStatus) {
        if (StringUtils.isBlank(enableStatus)) {
            return Collections.emptyList();
        }
        List<CostTypeCategory> costTypeCategory = this.costTypeCategoryRepository.findByEnableStatus(enableStatus);
        if (CollectionUtils.isEmpty(costTypeCategory)) {
            return Collections.emptyList();
        }
        Collection<CostTypeCategoryVo> costTypeCategorys = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeCategory, CostTypeCategory.class, CostTypeCategoryVo.class, LinkedHashSet.class, ArrayList.class);
        return Lists.newArrayList(costTypeCategorys);
    }

    /**
     * 批量根据id启用
     *
     * @param ids
     */
    @Override
    @Transactional
    public void enable(List<String> ids) {
        Validate.notEmpty(ids, "启用时，id不能为空");
        List<CostTypeCategory> costTypeCategorys = this.costTypeCategoryRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(costTypeCategorys)) {
            return;
        }
        Collection<CostTypeCategoryVo> costTypeCategoryVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeCategorys, CostTypeCategory.class, CostTypeCategoryVo.class, LinkedHashSet.class, ArrayList.class);
        this.costTypeCategoryRepository.updateEnableStatusByIds(EnableStatusEnum.ENABLE, ids);
        if (!CollectionUtils.isEmpty(costTypeCategoryEventListeners)) {
            for (CostTypeCategoryEventListener costTypeCategoryEventListener : costTypeCategoryEventListeners) {
                for (CostTypeCategoryVo costTypeCategoryVo : costTypeCategoryVos) {
                    costTypeCategoryEventListener.onEnable(costTypeCategoryVo);
                }
            }
        }
        //启用 业务日志
        SerializableBiConsumer<CostTypeCategoryLogEventListener, CostTypeCategoryLogEventDto> onEnable =
                CostTypeCategoryLogEventListener::onEnable;
        for (CostTypeCategoryVo costTypeCategoryVo : costTypeCategoryVos) {
            CostTypeCategoryLogEventDto logEventDto = new CostTypeCategoryLogEventDto();
            logEventDto.setOriginal(costTypeCategoryVo);
            logEventDto.setNewest(null);
            this.nebulaNetEventClient.publish(logEventDto, CostTypeCategoryLogEventListener.class, onEnable);
        }
    }

    /**
     * 批量根据id禁用
     *
     * @param ids
     */
    @Override
    @Transactional
    public void disable(List<String> ids) {
        Validate.notEmpty(ids, "禁用时，id不能为空");
        List<CostTypeCategory> costTypeCategories = this.costTypeCategoryRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(costTypeCategories)) {
            return;
        }
        Collection<CostTypeCategoryVo> costTypeCategoryVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeCategories, CostTypeCategory.class, CostTypeCategoryVo.class, LinkedHashSet.class, ArrayList.class);
        this.costTypeCategoryRepository.updateEnableStatusByIds(EnableStatusEnum.DISABLE, ids);
        if (!CollectionUtils.isEmpty(costTypeCategoryEventListeners)) {
            for (CostTypeCategoryEventListener costTypeCategoryEventListener : costTypeCategoryEventListeners) {
                for (CostTypeCategoryVo costTypeCategoryVo : costTypeCategoryVos) {
                    costTypeCategoryEventListener.onEnable(costTypeCategoryVo);
                }
            }
        }
        //禁用 业务日志
        SerializableBiConsumer<CostTypeCategoryLogEventListener, CostTypeCategoryLogEventDto> onDisable =
                CostTypeCategoryLogEventListener::onDisable;
        for (CostTypeCategoryVo costTypeCategoryVo : costTypeCategoryVos) {
            CostTypeCategoryLogEventDto logEventDto = new CostTypeCategoryLogEventDto();
            logEventDto.setOriginal(costTypeCategoryVo);
            logEventDto.setNewest(null);
            this.nebulaNetEventClient.publish(logEventDto, CostTypeCategoryLogEventListener.class, onDisable);
        }
    }

    @Override
    public List<CostTypeCategoryVo> findByBudgetSubjectsCode(String budgetSubjectsCode) {
        if (StringUtils.isBlank(budgetSubjectsCode)) {
            return Collections.emptyList();
        }
        List<CostTypeCategory> costTypeCategories = this.costTypeCategoryRepository.findByBudgetSubjectsCode(budgetSubjectsCode);
        Collection<CostTypeCategoryVo> costTypeCategoryVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeCategories, CostTypeCategory.class, CostTypeCategoryVo.class, LinkedHashSet.class, ArrayList.class);
        return Lists.newArrayList(costTypeCategoryVos);
    }

    @Override
    public List<CostTypeCategoryVo> findByBudgetSubjectsCodes(List<String> budgetSubjectsCodes) {
        if (CollectionUtils.isEmpty(budgetSubjectsCodes)) {
            return Collections.emptyList();
        }
        List<CostTypeCategory> costTypeCategories = this.costTypeCategoryRepository.findByBudgetSubjectsCodes(budgetSubjectsCodes);
        Collection<CostTypeCategoryVo> costTypeCategoryVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeCategories, CostTypeCategory.class, CostTypeCategoryVo.class, LinkedHashSet.class, ArrayList.class);
        return Lists.newArrayList(costTypeCategoryVos);
    }

    /**
     * 生成操作标记
     */
    @Override
    public String preSave() {
        String prefix = UUID.randomUUID().toString();
        // 1天后过期
        this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, TimeUnit.MILLISECONDS.convert(1, TimeUnit.DAYS));
        return prefix;
    }


    @Override
    public List<CostTypeCategoryVo> findListByNames(List<String> nameList) {
        List<List<String>> partitionList = Lists.partition(nameList, 800);
        String tenantCode = TenantUtils.getTenantCode();
        List<CostTypeCategory> dataList = Lists.newArrayList();
        for (List<String> strings : partitionList) {
            List<CostTypeCategory> list = costTypeCategoryRepository.findListByNames(strings, tenantCode);
            if (!CollectionUtils.isEmpty(list)) {
                dataList.addAll(list);
            }
        }
        return (List<CostTypeCategoryVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostTypeCategory.class, CostTypeCategoryVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<CostTypeCategoryVo> findListByCategoryCodes(List<String> categoryCodes) {
        List<CostTypeCategory> categoryVoList = costTypeCategoryRepository.findListByCategoryCodes(categoryCodes, TenantUtils.getTenantCode());
        return (List<CostTypeCategoryVo>) nebulaToolkitService.copyCollectionByWhiteList(categoryVoList, CostTypeCategory.class, CostTypeCategoryVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 查询关联的预算科目编码的活动大类
     *
     * @return
     */
    @Override
    public List<CostTypeCategoryVo> findListReleaseSecondBudgetSubjectCodes() {
        return costTypeCategoryRepository.findListReleaseSecondBudgetSubjectCodes();
    }

    /**
     * 验证与操作标记
     */
    private void validationPrefix(CostTypeCategoryVo costTypeCategoryVo) {
        // 验证重复提交标识
        String prefix = costTypeCategoryVo.getPrefix();
        Validate.notBlank(prefix, "错误的预操作标记，请检查!!");
        Validate.isTrue(StringUtils.isNotBlank(this.redisMutexService.getMCode(GENERATE_PREFIX, prefix)), "没有发现预操作标记，可能是因为重复操作的原因!");
        boolean isLock = false;
        try {
            if (isLock = this.redisMutexService.tryLock(prefix, TimeUnit.MILLISECONDS, 1)) {
                this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, 1L);
            } else {
                throw new IllegalArgumentException("请不要重复操作!!");
            }
        } finally {
            if (isLock) {
                this.redisMutexService.unlock(prefix);
            }
        }
    }

    /**
     * 创建验证
     *
     * @param costTypeCategoryVo
     */
    private void createValidate(CostTypeCategoryVo costTypeCategoryVo) {
        Validate.notNull(costTypeCategoryVo, "新增时，对象信息不能为空！");
        costTypeCategoryVo.setId(null);
        // 验证重复操作
        this.validationPrefix(costTypeCategoryVo);
        Validate.notBlank(costTypeCategoryVo.getEnableStatus(), "新增数据时，数据业务状态（启用状态）不能为空！");
        Validate.notBlank(costTypeCategoryVo.getCategoryCode(), "新增数据时，活动大类编号不能为空！");
        String pattern = "^[A-Z0-9]+$";
        Validate.matchesPattern(costTypeCategoryVo.getCategoryCode(), pattern, "活动大类编号格式非法，由大写字母及数字组成");
        Validate.notBlank(costTypeCategoryVo.getCategoryName(), "新增数据时，活动大类名称不能为空！");
        Validate.notBlank(costTypeCategoryVo.getBudgetSubjectsCode(), "新增数据时，预算科目编号不能为空！");
        CostTypeCategoryVo current = this.findByCode(costTypeCategoryVo.getCategoryCode());
        Validate.isTrue(current == null, "新增数据时，编号重复请检查！");
        Validate.isTrue(!costTypeCategoryVo.getCategoryCode().contains("-"), "活动大类名称不能包含特殊符号\"-\",请检查！");
    }

    /**
     * 修改验证
     *
     * @param costTypeCategoryVo
     */
    private void updateValidate(CostTypeCategoryVo costTypeCategoryVo) {
        Validate.notNull(costTypeCategoryVo, "修改时，对象信息不能为空！");
        // 验证重复操作
        this.validationPrefix(costTypeCategoryVo);
        Validate.notBlank(costTypeCategoryVo.getId(), "修改数据时，主键不能为空！");
        Validate.notBlank(costTypeCategoryVo.getCategoryCode(), "修改数据时，活动大类编号不能为空！");
        Validate.notBlank(costTypeCategoryVo.getCategoryName(), "修改数据时，活动大类名称不能为空！");
        Validate.isTrue(!costTypeCategoryVo.getCategoryCode().contains("-"), "活动大类名称不能包含特殊符号\"-\",请检查！");
    }
}