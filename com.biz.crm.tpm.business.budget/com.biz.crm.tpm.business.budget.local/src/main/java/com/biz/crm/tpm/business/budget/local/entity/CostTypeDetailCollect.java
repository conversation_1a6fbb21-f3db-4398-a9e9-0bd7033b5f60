package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * 采集信息
 */
@ApiModel(value = "CostTypeDetailCollect",description = "采集信息")
@TableName("tpm_cost_type_detail_collect")
@Getter
@Setter
@Entity(name = "tpm_cost_type_detail_collect")
@org.hibernate.annotations.Table(appliesTo = "tpm_cost_type_detail_collect", comment = "采集信息")
public class CostTypeDetailCollect extends TenantFlagOpEntity {

    /** 活动细类名称 */
    @ApiModelProperty(name = "活动细类名称",notes = "")
    @Column(name = "detail_name", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '活动细类名称 '")
    private String detailName;

    /** 活动细类编号 */
    @ApiModelProperty(name = "活动细类编号",notes = "")
    @Column(name = "detail_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动细类编号 '")
    private String detailCode;

    /** 采集示例名称 */
    @ApiModelProperty("采集示例名称")
    @Column(name = "collect_name", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '采集示例名称 '")
    private String collectName;

    /** 采集示例编号 */
    @ApiModelProperty("采集示例编号")
    @Column(name = "collect_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '采集示例编号 '")
    private String collectCode;

    /**
     * 参见com.biz.tpm.business.budget.sdk.enums.ApprovalCollectType
     */
    @ApiModelProperty("核销采集类型")
    @TableField(value = "type")
    @Column(name = "type", length = 64, columnDefinition = "varchar(64) COMMENT '核销采集类型'")
    private String type;
}
