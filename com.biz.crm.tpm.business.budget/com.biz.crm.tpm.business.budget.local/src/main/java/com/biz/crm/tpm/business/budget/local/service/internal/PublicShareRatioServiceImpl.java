package com.biz.crm.tpm.business.budget.local.service.internal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.budget.local.entity.PublicShareRatio;
import com.biz.crm.tpm.business.budget.local.repository.PublicShareRatioRepository;
import com.biz.crm.tpm.business.budget.sdk.constant.CostBudgetConstant;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.budget.sdk.vo.PublicShareRatioImportVo;
import com.biz.crm.tpm.business.budget.sdk.vo.PublicShareRatioVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class PublicShareRatioServiceImpl implements PublicShareRatioService {

    @Autowired(required = false)
    private PublicShareRatioRepository publicShareRatioRepository;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    /**
     * 删除数据
     *
     * @param ids 主键结合
     */
    @Override
    public void delete(List<String> ids) {
        Validate.notEmpty(ids, "主键id不能为空");
        publicShareRatioRepository.removeByIds(ids);
    }

    /**
     * 批量创建
     *
     * @param list
     */
    @Override
    public void createBatch(List<PublicShareRatioImportVo> list) {
        Collection<PublicShareRatio> entities = nebulaToolkitService.copyCollectionByWhiteList(list, PublicShareRatioImportVo.class, PublicShareRatio.class, LinkedHashSet.class, ArrayList.class);
        List<String> codes = generateCodeService.generateCodeYearMonth(CostBudgetConstant.PUBLIC_SHARE_RATIO_RULE_CODE, entities.size());
        AtomicInteger index = new AtomicInteger(0);
        entities.forEach(e -> {
            e.setPublicShareCode(codes.get(index.get()));
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setTenantCode(TenantUtils.getTenantCode());
            index.getAndAdd(1);
        });
        publicShareRatioRepository.saveBatch(entities);
    }

    /**
     * 按唯一键查询
     *
     * @param codes
     * @return
     */
    @Override
    public List<PublicShareRatioVo> findByUniqueKeys(Set<String> codes) {
        return publicShareRatioRepository.findByUniqueKeys(codes);
    }


    @Override
    public BigDecimal findRatioByCondition(List<String> orgCodes, String years) {
        String[] strs = years.split("-");
        String year = strs[0];
        String month = strs[1];
        List<PublicShareRatio> list = publicShareRatioRepository.findRatioByCondition(year, month, orgCodes);
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }
        return list.get(0).getRatio();
    }
}
