package com.biz.crm.tpm.business.track.local.service;

import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import com.biz.crm.tpm.business.track.local.entity.BudgetTrack;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;

/**
 * 预算跟踪
 */
public interface BudgetTrackAsyncService {
    /**
     * 计算
     *
     * @param list
     * @param orgVoMap
     */
    void calculate(List<BudgetTrack> list, Map<String, List<OrgVo>> orgVoMap);
    /**
     * 预算跟踪按率计算
     *
     * @param track
     * @param historyTrackList
     */
    void trackRate(BudgetTrack track, List<BudgetTrack> historyTrackList, Map<String, List<OrgVo>> orgVoMap);

    /**
     * 单条管控生成对应的预算跟踪
     *
     * @param controlVo
     * @param crmUserIdentity
     */
    Future<List<BudgetTrack>> generateBudgetTrackSingle(BudgetControlVo controlVo, Map<String, Set<String>> subjectMap, AbstractCrmUserIdentity crmUserIdentity);

}
