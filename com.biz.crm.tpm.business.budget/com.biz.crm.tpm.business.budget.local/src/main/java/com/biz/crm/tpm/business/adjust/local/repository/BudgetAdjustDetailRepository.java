package com.biz.crm.tpm.business.adjust.local.repository;



import com.biz.crm.tpm.business.adjust.local.entity.BudgetAdjustDetail;
import com.biz.crm.tpm.business.adjust.local.mapper.BudgetAdjustDetailMapper;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDetailDto;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustDetailVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 预算调整明细(BudgetAdjustDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-23 14:49:55
 */
@Component
public class BudgetAdjustDetailRepository extends ServiceImpl<BudgetAdjustDetailMapper, BudgetAdjustDetail> {

  @Autowired
  private BudgetAdjustDetailMapper budgetAdjustDetailMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  
   /**
   * 分页查询数据
   * @param pageable 分页对象
   * @param budgetAdjustDetail 实体对象
   * @return
   */
  public Page<BudgetAdjustDetailVo> findByConditions(Pageable pageable, BudgetAdjustDetailDto budgetAdjustDetail) {
    Page<BudgetAdjustDetailVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    Page<BudgetAdjustDetailVo> pageList = this.budgetAdjustDetailMapper.findByConditions(page, budgetAdjustDetail);
    return pageList;
  }

  /**
   * 按编码查询
   *
   * @param code
   * @return
   */
  public List<BudgetAdjustDetailVo> findByCode(String code) {
    List<BudgetAdjustDetail> list = lambdaQuery().eq(BudgetAdjustDetail::getAdjustCode, code)
            .eq(BudgetAdjustDetail::getTenantCode, TenantUtils.getTenantCode())
            .list();

    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, BudgetAdjustDetail.class, BudgetAdjustDetailVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }

  /**
   * 按编码删除
   *
   * @param codes
   * @return
   */
  public void deleteByCodes(List<String> codes) {
    this.lambdaUpdate().in(BudgetAdjustDetail::getAdjustCode, codes).remove();
  }
}

