package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.budget.local.entity.ApprovalCollectImage;
import com.biz.crm.tpm.business.budget.local.mapper.ApprovalCollectImageMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;


/**
 * 核销采集信息(ApprovalCollectImage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:02
 */
@Component
public class ApprovalCollectImageRepository extends ServiceImpl<ApprovalCollectImageMapper, ApprovalCollectImage> {

  public void deleteByApprovalCollectCode(String approvalCollectCode){
    this.lambdaUpdate()
        .eq(ApprovalCollectImage::getApprovalCollectCode,approvalCollectCode)
        .eq(ApprovalCollectImage::getTenantCode, TenantUtils.getTenantCode())
        .remove();
  }

  public List<ApprovalCollectImage> findByApprovalCollectCode(String approvalCollectCode){
    return this.lambdaQuery()
        .eq(ApprovalCollectImage::getApprovalCollectCode,approvalCollectCode)
        .eq(ApprovalCollectImage::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }

  public List<ApprovalCollectImage> findByIds(List<String> ids) {
    return this.lambdaQuery()
        .in(ApprovalCollectImage::getId,ids)
        .eq(ApprovalCollectImage::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }

  public void removeByIdsAndTenantCode(Set<String> ids, String tenantCode) {
    this.lambdaUpdate()
        .eq(ApprovalCollectImage::getTenantCode,tenantCode)
        .in(ApprovalCollectImage::getId,ids)
        .remove();
  }
}

