package com.biz.crm.tpm.business.budget.local.notifier;

import com.biz.crm.tpm.business.budget.sdk.event.ApprovalCollectEventListener;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 描述：</br>活动细类针对采集示例变动监听
 *
 * <AUTHOR>
 * @date 2022/5/30
 */
@Component
public class CostTypeDetailForApprovalCollectEventListener implements ApprovalCollectEventListener {
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;

  @Override
  public void onDeleted(ApprovalCollectVo approvalCollectVo) {
    String code = approvalCollectVo.getCode();
    boolean exist = this.costTypeDetailVoService.existByApprovalCollect(code);
    Validate.isTrue(!exist,"该示例已关联细类活动，无法删除");
  }

  @Override
  public void onUpdate(ApprovalCollectVo oldApprovalCollectVo, ApprovalCollectVo newApprovalCollectVo) {

  }
}
