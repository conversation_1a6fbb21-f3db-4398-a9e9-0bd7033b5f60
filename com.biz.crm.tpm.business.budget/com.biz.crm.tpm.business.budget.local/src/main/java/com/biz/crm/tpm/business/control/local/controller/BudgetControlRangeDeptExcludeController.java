package com.biz.crm.tpm.business.control.local.controller;

import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlDeptExcludeRangeDto;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlDeptExcludeRangeVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "预算管控部门剔除范围")
@RestController
@RequestMapping("/v1/budget/budgetControl/deptExclude")
@Slf4j
public class BudgetControlRangeDeptExcludeController extends BusinessPageCacheController<BudgetControlDeptExcludeRangeVo, BudgetControlDeptExcludeRangeDto> {
}
