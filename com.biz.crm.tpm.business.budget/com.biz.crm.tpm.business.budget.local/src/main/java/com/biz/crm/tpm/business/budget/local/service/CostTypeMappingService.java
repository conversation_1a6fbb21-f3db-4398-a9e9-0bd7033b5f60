package com.biz.crm.tpm.business.budget.local.service;

import com.biz.crm.tpm.business.budget.local.entity.CostTypeMapping;

import java.util.Collection;
import java.util.List;

/**
 * TPM-活动明细与活动大类关联服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
public interface CostTypeMappingService {

  /**
   * 根据活动细类编号查询关联信息
   *
   * @param detailCode
   * @return
   */
  List<CostTypeMapping> findByDetailCode(String detailCode);

  /**
   * 根据活动大类编号查询关联信息
   *
   * @param categoryCode
   * @return
   */
  List<CostTypeMapping> findByCategoryCode(String categoryCode);

  /**
   * 批量保存
   *
   * @param costTypeMappings
   */
  void saveBatch(Collection<CostTypeMapping> costTypeMappings);

  /**
   * 通过活动明细编号删除关联范围数据
   *
   * @param detailCode
   */
  void deleteByDetailCode(String detailCode);
}