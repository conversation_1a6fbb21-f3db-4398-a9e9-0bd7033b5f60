package com.biz.crm.tpm.business.budget.local.service.process;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import com.biz.crm.tpm.business.budget.sdk.vo.PublicShareRatioImportVo;
import com.biz.crm.tpm.business.budget.sdk.vo.PublicShareRatioVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PublicShareRatioImportProcess implements ImportProcess<PublicShareRatioImportVo> {

    @Autowired
    private OrgVoService orgVoService;
    @Autowired
    private PublicShareRatioService publicShareRatioService;

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, PublicShareRatioImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, PublicShareRatioImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        if (CollectionUtil.isEmpty(data)) {
            throw new IllegalArgumentException("导入数据不能为空!");
        }
        Map<Integer, String> errMap = new HashMap<>();

        for (Map.Entry<Integer, PublicShareRatioImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();

            PublicShareRatioImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getYearStr()), "年，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getMonthStr()), "月，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getDepartmentCode()), "部门编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getRatioStr()), "公摊费率，不能为空！");

            try {
                new BigDecimal(vo.getRatioStr().trim());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                this.validateIsTrue(false, "公摊费率类型转换失败！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, PublicShareRatioImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();

        Set<String> orgCodeSet = new HashSet<>();
        Set<String> uniqueKeySet = new HashSet<>();
        for (Map.Entry<Integer, PublicShareRatioImportVo> row : data.entrySet()) {
            PublicShareRatioImportVo vo = row.getValue();

            orgCodeSet.add(vo.getDepartmentCode());
            vo.setUniqueKey(vo.getYearStr() + vo.getMonthStr() + vo.getDepartmentCode());
        }
        Map<String, OrgVo> orgVoMap = orgVoService.findByOrgCodes(new ArrayList<>(orgCodeSet)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), Function.identity(), (a, b) -> a));

        for (Map.Entry<Integer, PublicShareRatioImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();
            PublicShareRatioImportVo vo = row.getValue();

            vo.setRatio(new BigDecimal(vo.getRatioStr()));
            vo.setRatioStr(vo.getRatio().multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
            if (orgVoMap.containsKey(vo.getDepartmentCode())) {
                vo.setDepartmentName(orgVoMap.get(vo.getDepartmentCode()).getOrgName());
                vo.setLevelNum(orgVoMap.get(vo.getDepartmentCode()).getLevelNum());
            } else {
                this.validateIsTrue(false, "部门，未找到！");
            }

            this.validateIsTrue(!uniqueKeySet.contains(vo.getUniqueKey()), "导入文件中存在重复的数据！");
            uniqueKeySet.add(vo.getUniqueKey());

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        List<PublicShareRatioVo> voList = publicShareRatioService.findByUniqueKeys(uniqueKeySet);
        if (CollectionUtils.isNotEmpty(voList)) {
            List<String> keyList = voList.stream().map(e -> e.getUniqueKey()).collect(Collectors.toList());
            for (Map.Entry<Integer, PublicShareRatioImportVo> row : data.entrySet()) {
                int rowNum = row.getKey();
                PublicShareRatioImportVo vo = row.getValue();

                this.validateIsTrue(!keyList.contains(vo.getUniqueKey()), "该数据在系统中已存在！");

                String errInfo = this.validateGetErrorInfo();
                if (errInfo != null) {
                    errMap.put(rowNum, (errMap.get(rowNum) != null ? (errMap.get(rowNum) + errInfo) : errInfo));
                }
            }
        }

        if (!errMap.isEmpty()) {
            return errMap;
        }

        publicShareRatioService.createBatch(new ArrayList<>(data.values()));
        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<PublicShareRatioImportVo> findCrmExcelVoClass() {
        return PublicShareRatioImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "PUBLIC_SHARE_RATIO_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "公摊费率导入模板";
    }
}
