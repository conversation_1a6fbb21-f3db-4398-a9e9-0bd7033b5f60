package com.biz.crm.tpm.business.budget.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetItemDto;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetItemVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 费用预算明细项(CostBudgetItem)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
@RestController
@RequestMapping("/v1/budget/costBudgetItem")
@Slf4j
@Api(tags = "费用预算明细项")
public class CostBudgetItemController {
  /**
   * 服务对象
   */
  @Autowired
  private CostBudgetItemVoService costBudgetItemVoService;

  /**
   * 分页查询所有数据
   * @param pageable   分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "费用预算查看明细-分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<CostBudgetItemVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                         @ApiParam(name = "costBudgetItem", value = "费用预算明细项参数") CostBudgetItemDto dto) {
    try {
      Page<CostBudgetItemVo> page = this.costBudgetItemVoService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
