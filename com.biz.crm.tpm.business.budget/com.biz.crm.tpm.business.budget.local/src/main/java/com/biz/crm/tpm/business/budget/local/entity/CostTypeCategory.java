package com.biz.crm.tpm.business.budget.local.entity;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;

 /**
 * 实体：TPM-活动大类;
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
@ApiModel(value = "CostTypeCategory",description = "TPM-活动大类")
@TableName("tpm_cost_type_category")
@Getter
@Setter
@Entity(name = "tpm_cost_type_category")
@org.hibernate.annotations.Table(appliesTo = "tpm_cost_type_category", comment = "TPM-活动大类")
public class CostTypeCategory  extends TenantFlagOpEntity{
  /** 活动大类编号 */
  @ApiModelProperty(name = "活动大类编号",notes = "")
  @Column(name = "category_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动大类编号 '")
  private String categoryCode;
  
  /** 活动大类名称 */
  @ApiModelProperty(name = "活动大类名称",notes = "")
  @Column(name = "category_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '活动大类名称 '")
  private String categoryName;

  /** 预算科目名称 */
  @ApiModelProperty(name = "预算科目名称",notes = "")
  @Column(name = "budget_subjects_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;
  
  /** 预算科目编号 */
  @ApiModelProperty(name = "预算科目编号",notes = "")
  @Column(name = "budget_subjects_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目编号 '")
  private String budgetSubjectsCode;

}