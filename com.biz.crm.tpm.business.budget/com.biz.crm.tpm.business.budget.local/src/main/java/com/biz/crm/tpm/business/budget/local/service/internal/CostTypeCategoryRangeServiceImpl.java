package com.biz.crm.tpm.business.budget.local.service.internal;

import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeCategoryRange;
import com.biz.crm.tpm.business.budget.local.repository.CostTypeCategoryRangeRepository;
import com.biz.crm.tpm.business.budget.local.service.CostTypeCategoryRangeService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryRangeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * TPM-活动大类范围;(tpm_cost_type_category_range)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@Service("costTypeCategoryRangeService")
public class CostTypeCategoryRangeServiceImpl implements CostTypeCategoryRangeService {
  @Autowired
  private CostTypeCategoryRangeRepository costTypeCategoryRangeRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public List<CostTypeCategoryRangeVo> findByCategoryCode(String categoryCode) {
    if (StringUtils.isBlank(categoryCode)) {
      return Collections.emptyList();
    }
    List<CostTypeCategoryRange> costTypeCategoryRanges = this.costTypeCategoryRangeRepository.findByCategoryCode(categoryCode);
    if (CollectionUtils.isEmpty(costTypeCategoryRanges)) {
      return Collections.emptyList();
    }
    Collection<CostTypeCategoryRangeVo> costTypeCategoryRangeVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeCategoryRanges, CostTypeCategoryRange.class, CostTypeCategoryRangeVo.class, LinkedHashSet.class, ArrayList.class);
    return Lists.newArrayList(costTypeCategoryRangeVos);
  }

  @Transactional
  @Override
  public void saveBatch(Collection<CostTypeCategoryRange> costTypeCategoryRanges) {
    costTypeCategoryRanges.forEach(costTypeCategoryRange -> costTypeCategoryRange.setTenantCode(TenantUtils.getTenantCode()));
    this.costTypeCategoryRangeRepository.saveBatch(costTypeCategoryRanges);
  }

  @Transactional
  @Override
  public void deleteByCategoryCode(String categoryCode) {
    this.costTypeCategoryRangeRepository.deleteByCategoryCode(categoryCode);
  }

  @Override
  public void deleteByRangeCodes(Set<String> rangeCodes) {
    this.costTypeCategoryRangeRepository.deleteByRangeCodes(rangeCodes);
  }

  @Override
  public void updateEnableStatus(Set<String> codes, String enableStatus) {
    List<CostTypeCategoryRange> costTypeCategoryRanges = this.costTypeCategoryRangeRepository.findByCodes(codes);
    if (CollectionUtils.isEmpty(costTypeCategoryRanges)) {
      return;
    }
    codes = costTypeCategoryRanges.stream().map(CostTypeCategoryRange::getRangeCode).collect(Collectors.toSet());
    this.costTypeCategoryRangeRepository.updateEnableStatusByCodes(EnableStatusEnum.codeToEnum(enableStatus), codes);
  }

  @Transactional
  @Override
  public void updateName(String code, String name) {
    if (StringUtils.isAnyBlank(code, name)) {
      return;
    }
    List<CostTypeCategoryRange> costTypeCategoryRanges = this.costTypeCategoryRangeRepository.findByCodes(Sets.newHashSet(code));
    if (CollectionUtils.isEmpty(costTypeCategoryRanges)) {
      return;
    }
    this.costTypeCategoryRangeRepository.updateName(code, name);
  }

  /**
   * 查询全部
   * @return
   */
  @Override
  public Set<CostTypeCategoryRange> findAll() {
    List<CostTypeCategoryRange> all = this.costTypeCategoryRangeRepository.findAll();
    Set<CostTypeCategoryRange> costTypeCategoryRanges = Sets.newHashSet(all);
    return costTypeCategoryRanges;
  }
}
