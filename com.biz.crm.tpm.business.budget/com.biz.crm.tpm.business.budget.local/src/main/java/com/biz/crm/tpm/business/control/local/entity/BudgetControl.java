package com.biz.crm.tpm.business.control.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_budget_control")
@Table(name = "tpm_budget_control",
        indexes = {
                @Index(name = "tpm_budget_control_uq1", columnList = "control_code",unique = true),
                @Index(name = "tpm_budget_control_idx1", columnList = "start_year_month",unique = false),
                @Index(name = "tpm_budget_control_idx2", columnList = "end_year_month",unique = false),
        })
@ApiModel(value = "BudgetControl", description = "预算管控")
@org.hibernate.annotations.Table(appliesTo = "tpm_budget_control", comment = "预算管控")
public class BudgetControl extends TenantFlagOpEntity {

  @ApiModelProperty("预算管控编码")
  @Column(name = "control_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '预算管控编码'")
  private String controlCode;

  @ApiModelProperty("预算管控名称")
  @Column(name = "control_name", length = 128, columnDefinition = "varchar(128) COMMENT '预算管控名称'")
  private String controlName;

  @ApiModelProperty("适用部门编码")
  @Column(name = "department_code", columnDefinition = "VARCHAR(32) COMMENT '适用部门编码'")
  private String departmentCode;

  @ApiModelProperty("适用部门名称")
  @Column(name = "department_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '适用部门名称'")
  private String departmentName;

  @ApiModelProperty("生效开始年月")
  @Column(name = "start_year_month", columnDefinition = "VARCHAR(32) COMMENT '生效开始年月 '")
  private String startYearMonth;

  @ApiModelProperty("生效结束年月")
  @Column(name = "end_year_month", columnDefinition = "VARCHAR(32) COMMENT '生效结束年月 '")
  private String endYearMonth;

  @ApiModelProperty("时间维度")
  @Column(name = "time_dimension", columnDefinition = "VARCHAR(32) COMMENT '时间维度'")
  private String timeDimension;

  @ApiModelProperty("管控类型")
  @Column(name = "control_type", columnDefinition = "VARCHAR(32) COMMENT '管控类型'")
  private String controlType;

  @ApiModelProperty("管控形式")
  @Column(name = "control_form", columnDefinition = "VARCHAR(32) COMMENT '管控形式'")
  private String controlForm;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("组织编码")
  @Column(name = "org_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编码'")
  private String orgCode;

  @ApiModelProperty("部门层级")
  @Column(name = "level_num", columnDefinition = "int COMMENT '部门层级'")
  private Integer levelNum;
}
