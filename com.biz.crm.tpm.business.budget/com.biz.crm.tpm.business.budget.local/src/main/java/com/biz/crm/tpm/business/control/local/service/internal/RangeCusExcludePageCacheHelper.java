package com.biz.crm.tpm.business.control.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.control.local.repository.BudgetControlRangeRepository;
import com.biz.crm.tpm.business.control.sdk.constant.BudgetControlConstant;
import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlCusExcludeRangeDto;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlCusExcludeRangeVo;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlRangeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 分页缓存
 */
@Slf4j
@Component
public class RangeCusExcludePageCacheHelper extends BusinessPageCacheHelper<BudgetControlCusExcludeRangeVo, BudgetControlCusExcludeRangeDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private BudgetControlRangeRepository budgetControlRangeRepository;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return BudgetControlConstant.CACHE_KEY_CUS_EXCLUDE_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<BudgetControlCusExcludeRangeDto> getDtoClass() {
        return BudgetControlCusExcludeRangeDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<BudgetControlCusExcludeRangeVo> getVoClass() {
        return BudgetControlCusExcludeRangeVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param dto
     * @param cacheKey
     */
    @Override
    public List<BudgetControlCusExcludeRangeDto> findDtoListFromRepository(BudgetControlCusExcludeRangeDto dto, String cacheKey) {
        if (StringUtils.isBlank(dto.getControlCode())) {
            return new ArrayList<>();
        }
        List<BudgetControlRangeVo> vos = budgetControlRangeRepository.findByCode(dto.getControlCode());
        vos = vos.stream().filter(e -> e.getRangeType().equals("cusExclude")).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(vos) ? (List<BudgetControlCusExcludeRangeDto>) nebulaToolkitService.copyCollectionByBlankList(vos, BudgetControlRangeVo.class, BudgetControlCusExcludeRangeDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlCusExcludeRangeDto> newItem(String cacheKey, List<BudgetControlCusExcludeRangeDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlCusExcludeRangeDto> copyItem(String cacheKey, List<BudgetControlCusExcludeRangeDto> itemList) {
        List<BudgetControlCusExcludeRangeDto> newItemList = (List<BudgetControlCusExcludeRangeDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, BudgetControlCusExcludeRangeDto.class, BudgetControlCusExcludeRangeDto.class, HashSet.class, ArrayList.class);
        for (BudgetControlCusExcludeRangeDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 获取Dto中的主键
     *
     * @param dto
     * @return 主键
     */
    @Override
    public Object getDtoKey(BudgetControlCusExcludeRangeDto dto) {
        return dto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param dto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(BudgetControlCusExcludeRangeDto dto) {
        return dto.getChecked();
    }

}
