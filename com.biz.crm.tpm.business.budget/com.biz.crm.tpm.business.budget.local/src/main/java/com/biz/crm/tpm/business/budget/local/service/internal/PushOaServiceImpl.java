package com.biz.crm.tpm.business.budget.local.service.internal;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.workflow.sdk.constant.oa.OABridgeConstants;
import com.biz.crm.workflow.sdk.dto.oa.request.*;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.BiConsumer;

@Slf4j
@Service
public class PushOaServiceImpl implements PushOaService {

    @Autowired
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Autowired
    private UserVoService userVoService;

    /**
     * 创建OA流程
     *
     * @param orderJsonObject 业务对象
     * @param businessType
     * @param oaParam OA参数
     * @param details 业务对象明细
     * @param workflowId 流程id
     * @param workflowName 流程名称
     * @param mainTableMethod 主列表枚举封装
     * @param detailTableMethod 明细列表枚举封装
     * @param <T>
     * @return
     */
    @Override
    public <T> JSONObject pushOA(JSONObject orderJsonObject, String businessType, JSONObject oaParam, List<JSONArray> details, String workflowId, String workflowName,
                                 BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod, BiConsumer<List<WorkflowRequestTableField>, JSONObject> ... detailTableMethod) {
        // 获取当前登录人信息
        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);

        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        WorkflowMainTableInfo mainTableInfo = this.getWorkflowMainTableInfo(userVo, orderJsonObject, businessType, mainTableMethod);
        // 从表
        WorkflowDetailTableInfos workflowDetailTableInfos = this.getWorkflowDetailTableInfos(details, detailTableMethod);
        // 基础组装
        WorkflowBase flowBase = this.getWorkflowBase(userVo, mainTableInfo, workflowDetailTableInfos, workflowId, workflowName);
        // 额外结构组装，和业务本身无关，不用关注
        JSONObject fb = this.getOtherJsonObject(flowBase);

        oaParam.put("in0", fb);
        oaParam.put("in1", userVo.getUserName());
        // eventkey 用于动态传入当前的操作类型，推送时会去掉
        oaParam.put("eventkey", OABridgeConstants.DO_CREAT_REQUEST);
        log.warn("+++++++++++++++++++++++++++++++++++++++++参数：" + oaParam.toJSONString());
        JSONObject response = ryOaProcessService.oaCrmRequest(oaParam);
        log.warn("+++++++++++++++++++++++++++++++++++++++++响应：" + response.toJSONString());

        response.put("oaId", userVo.getOaId());
        response.put("oaUserName", userVo.getUserName());

        return response;
    }

    private WorkflowMainTableInfo getWorkflowMainTableInfo(UserVo userVo, JSONObject orderJsonObject, String businessType, BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod) {
        // 把需要的字段放枚举了，偷个懒直接整，就是丑了点，但他简单啊
        List<WorkflowRequestTableField> tableFields = new ArrayList<>();
        mainTableMethod.accept(tableFields, orderJsonObject);
        // 非订单追加字段处理loginDetails.getUsername()
        this.addOtherColumns2MainTable(tableFields, userVo, businessType, orderJsonObject.getString("tpmUrl"), orderJsonObject.getString("businessCode"), orderJsonObject.getString("bearDepartmentOneOaCodes"));
        WorkflowRequestTableFields orderTableFields = new WorkflowRequestTableFields();
        orderTableFields.setWorkflowRequestTableField(tableFields);
        WorkFlowRequestTableRecord workFlowRequestTableRecord = new WorkFlowRequestTableRecord();
        workFlowRequestTableRecord.setWorkflowRequestTableFields(orderTableFields);
        workFlowRequestTableRecord.setRecordOrder(tableFields.size() + "");
        List<WorkFlowRequestTableRecord> workFlowRequestTableRecordList = Arrays.asList(workFlowRequestTableRecord);
        RequestRecords requestRecords = new RequestRecords();
        requestRecords.setWorkflowRequestTableRecord(workFlowRequestTableRecordList);
        WorkflowMainTableInfo mainTableInfo = new WorkflowMainTableInfo();
        mainTableInfo.setRequestRecords(requestRecords);
        return mainTableInfo;
    }

    /**
     * 追加OA流程审批的字段
     *
     * @param tableFields
     * @param bearDepartmentOneOaCodes
     */
    private void addOtherColumns2MainTable(List<WorkflowRequestTableField> tableFields, UserVo userVo, String businessType, String url, String businessCode, String bearDepartmentOneOaCodes) {
        // 操作类型
        this.addMainTableColumns(tableFields, "businessType", businessType);
        // 申请人
        this.addMainTableColumns(tableFields, "sqr", userVo.getOaId());
        // 申请部门
        this.addMainTableColumns(tableFields, "sqbm", userVo.getDepartmentId());
        // 申请公司
        this.addMainTableColumns(tableFields, "sqgs", userVo.getSubCompanyId());
        // 申请时间
        this.addMainTableColumns(tableFields, "sqrq", DateUtil.format(new Date(), "yyyy-MM-dd"));

        // TPM单据链接
        this.addMainTableColumns(tableFields, "tpmUrl", url);
        // 单据编码
        this.addMainTableColumns(tableFields, "businessCode", businessCode);

        this.addMainTableColumns(tableFields, "bearDepartmentOneOaCodes", bearDepartmentOneOaCodes);
    }

    private void addMainTableColumns(List<WorkflowRequestTableField> tableFields, String fieldName, String fieldValue) {
        WorkflowRequestTableField tableField = new WorkflowRequestTableField();
        tableField.setFieldName(fieldName);
        tableField.setFieldValue(fieldValue);
        tableField.setView("true");
        tableField.setEdit("true");
        tableField.setMand("false");
        tableFields.add(tableField);
    }

    private <T> WorkflowDetailTableInfos getWorkflowDetailTableInfos(List<JSONArray> list, BiConsumer<List<WorkflowRequestTableField>, JSONObject>  ... method) {
        if (CollectionUtils.isEmpty(list)) {
            return new WorkflowDetailTableInfos();
        }
        WorkflowDetailTableInfos workflowDetailTableInfos = new WorkflowDetailTableInfos();
        List<WorkflowDetailTableInfo> workflowDetailTableInfoList = new ArrayList();
        for (int i = 0; i < method.length; i++) {
            List<WorkFlowRequestTableRecord> recordList = new ArrayList<>();
            JSONArray detailList = list.get(i);
            for (int j = 0; j < detailList.size(); j++) {
                // 把detail转成JSONObject
                List<WorkflowRequestTableField> tableDetailFields = new ArrayList<>();
                JSONObject detail = detailList.getJSONObject(j);
                method[i].accept(tableDetailFields, detail);

                WorkflowRequestTableFields workflowRequestTableFields = new WorkflowRequestTableFields();
                workflowRequestTableFields.setWorkflowRequestTableField(tableDetailFields);

                WorkFlowRequestTableRecord detailRecord = new WorkFlowRequestTableRecord();
                detailRecord.setWorkflowRequestTableFields(workflowRequestTableFields);
                detailRecord.setRecordOrder(tableDetailFields.size() + "");
                recordList.add(detailRecord);
            }
            WorkFlowRequestTableRecords records = new WorkFlowRequestTableRecords();
            records.setWorkflowRequestTableRecord(recordList);
            WorkflowDetailTableInfo detailTableInfo = new WorkflowDetailTableInfo();
            detailTableInfo.setWorkflowRequestTableRecords(records);
            workflowDetailTableInfoList.add(detailTableInfo);
        }
        workflowDetailTableInfos.setWorkflowDetailTableInfo(workflowDetailTableInfoList);
        return workflowDetailTableInfos;
    }

    private WorkflowBase getWorkflowBase(UserVo userVo, WorkflowMainTableInfo mainTableInfo, WorkflowDetailTableInfos workflowDetailTableInfos, String workflowId, String workflowName) {
        WorkflowBaseInfo workflowBaseInfo = new WorkflowBaseInfo();
        workflowBaseInfo.setWorkflowName(workflowName);
        workflowBaseInfo.setWorkflowTypeName("CRM");
        workflowBaseInfo.setWorkflowId(workflowId); // 可变 做成数据字典
        WorkflowBase flowBase = new WorkflowBase();
        flowBase.setWorkflowMainTableInfo(mainTableInfo);
        flowBase.setWorkflowDetailTableInfos(workflowDetailTableInfos);
        flowBase.setWorkflowBaseInfo(workflowBaseInfo);
        flowBase.setCreatorId(userVo.getUserName());
        flowBase.setCreatorName(userVo.getFullName());
        flowBase.setCanEdit("true");
        flowBase.setCanView("true");
        flowBase.setRequestLevel("1");
        flowBase.setNeedAffirmance("false");
        flowBase.setMustInputRemark("false");
        flowBase.setRequestLevel("0");
        flowBase.setRequestName(workflowName);
        flowBase.setRemark("");
        flowBase.setIsnextflow("1");
        flowBase.setCreateTime(DateUtils.format(new Date(), "yyyy-MM-dd"));
        return flowBase;
    }

    private JSONObject getOtherJsonObject(WorkflowBase flowBase) {
        String[] strArr = {};
        JSONObject obj1 = new JSONObject();
        obj1.put("WorkflowRequestLog", strArr);
        JSONObject fb = JSONObject.parseObject(JSONObject.toJSONString(flowBase));
        fb.put("workflowRequestLogs", obj1);
        return fb;
    }
}
