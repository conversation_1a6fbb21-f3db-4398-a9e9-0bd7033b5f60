package com.biz.crm.tpm.business.control.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.control.local.repository.BudgetControlRangeRepository;
import com.biz.crm.tpm.business.control.sdk.constant.BudgetControlConstant;
import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlCusRangeDto;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlCusRangeVo;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlRangeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 分页缓存
 */
@Slf4j
@Component
public class RangeCusPageCacheHelper extends BusinessPageCacheHelper<BudgetControlCusRangeVo, BudgetControlCusRangeDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private BudgetControlRangeRepository budgetControlRangeRepository;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return BudgetControlConstant.CACHE_KEY_CUS_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<BudgetControlCusRangeDto> getDtoClass() {
        return BudgetControlCusRangeDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<BudgetControlCusRangeVo> getVoClass() {
        return BudgetControlCusRangeVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param dto
     * @param cacheKey
     */
    @Override
    public List<BudgetControlCusRangeDto> findDtoListFromRepository(BudgetControlCusRangeDto dto, String cacheKey) {
        if (StringUtils.isBlank(dto.getControlCode())) {
            return new ArrayList<>();
        }
        List<BudgetControlRangeVo> vos = budgetControlRangeRepository.findByCode(dto.getControlCode());
        vos = vos.stream().filter(e -> e.getRangeType().equals("cus")).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(vos) ? (List<BudgetControlCusRangeDto>) nebulaToolkitService.copyCollectionByBlankList(vos, BudgetControlRangeVo.class, BudgetControlCusRangeDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlCusRangeDto> newItem(String cacheKey, List<BudgetControlCusRangeDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlCusRangeDto> copyItem(String cacheKey, List<BudgetControlCusRangeDto> itemList) {
        List<BudgetControlCusRangeDto> newItemList = (List<BudgetControlCusRangeDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, BudgetControlCusRangeDto.class, BudgetControlCusRangeDto.class, HashSet.class, ArrayList.class);
        for (BudgetControlCusRangeDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 获取Dto中的主键
     *
     * @param dto
     * @return 主键
     */
    @Override
    public Object getDtoKey(BudgetControlCusRangeDto dto) {
        return dto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param dto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(BudgetControlCusRangeDto dto) {
        return dto.getChecked();
    }

}
