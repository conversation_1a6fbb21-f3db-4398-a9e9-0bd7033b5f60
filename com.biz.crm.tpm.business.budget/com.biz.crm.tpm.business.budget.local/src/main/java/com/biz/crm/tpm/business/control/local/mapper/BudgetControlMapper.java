package com.biz.crm.tpm.business.control.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.control.local.entity.BudgetControl;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 预算管控(TpmBudgetControl)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-05-22 16:19:54
 */
public interface BudgetControlMapper extends BaseMapper<BudgetControl> {


    List<BudgetControlVo> matchBudgetControl(@Param("years") String years, @Param("orgCodes") List<String> orgCodes, @Param("customerCode") String customerCode,
                                             @Param("budgetSubjectCodeSet") Set<String> budgetSubjectCodeSet, @Param("tenantCode")String tenantCode);
}

