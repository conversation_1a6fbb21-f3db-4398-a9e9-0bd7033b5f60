package com.biz.crm.tpm.business.budget.local.service.internal;

import com.biz.crm.tpm.business.budget.local.entity.CostTypeMapping;
import com.biz.crm.tpm.business.budget.local.repository.CostTypeMappingRepository;
import com.biz.crm.tpm.business.budget.local.service.CostTypeMappingService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 描述：</br>TPM-活动细类与活动大类关联关系服务实现类
 *
 * <AUTHOR>
 * @date 2022/5/20
 */
@Service("costTypeMappingService")
public class CostTypeMappingServiceImpl implements CostTypeMappingService {
  @Autowired
  private CostTypeMappingRepository costTypeMappingRepository;

  @Override
  public List<CostTypeMapping> findByDetailCode(String detailCode) {
    return this.costTypeMappingRepository.lambdaQuery()
            .eq(CostTypeMapping::getDetailCode, detailCode)
            .eq(CostTypeMapping::getTenantCode, TenantUtils.getTenantCode()).list();
  }

  @Override
  public List<CostTypeMapping> findByCategoryCode(String categoryCode) {
    return this.costTypeMappingRepository.lambdaQuery()
            .eq(CostTypeMapping::getCategoryCode, categoryCode)
            .eq(CostTypeMapping::getTenantCode, TenantUtils.getTenantCode()).list();
  }

  @Override
  public void saveBatch(Collection<CostTypeMapping> costTypeMappings) {
    costTypeMappings.forEach(costTypeMapping -> costTypeMapping.setTenantCode(TenantUtils.getTenantCode()));
    this.costTypeMappingRepository.saveBatch(costTypeMappings);
  }

  @Override
  public void deleteByDetailCode(String detailCode) {
    this.costTypeMappingRepository.deleteByDetailCode(detailCode);
  }
}
