package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.budget.local.entity.ApprovalCollect;
import com.biz.crm.tpm.business.budget.local.mapper.ApprovalCollectMapper;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectDto;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;


/**
 * 核销采集信息(ApprovalCollect)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:02
 */
@Component
public class ApprovalCollectRepository extends ServiceImpl<ApprovalCollectMapper, ApprovalCollect> {

  @Autowired
  private ApprovalCollectMapper approvalCollectMapper;

  /**
   * 分页查询数据
   *
   * @param pageable        分页对象
   * @param approvalCollect 实体对象
   */
  public Page<ApprovalCollectVo> findByConditions(Pageable pageable, ApprovalCollectDto approvalCollect) {
    if(pageable == null){
      pageable = PageRequest.of(0,50);
    }
    approvalCollect.setTenantCode(TenantUtils.getTenantCode());
    Page<ApprovalCollectVo> page = new Page<ApprovalCollectVo>(pageable.getPageNumber(), pageable.getPageSize());
    return approvalCollectMapper.findByConditions(page, approvalCollect);
  }

  /**
   * 根据编码查询信息
   */
  public ApprovalCollect findByCode(String code){
    return this.lambdaQuery()
        .eq(ApprovalCollect::getCode,code)
        .eq(ApprovalCollect::getTenantCode,TenantUtils.getTenantCode())
        .one();
  }

  /**
   * 根据编码集查询信息
   */
  public List<ApprovalCollect> findByCodes(Set<String> codes){
    return this.lambdaQuery()
        .in(ApprovalCollect::getCode,codes)
        .eq(ApprovalCollect::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }


  /**
   * 启禁用
   */
  public void updateEnableStatus(Set<String> ids, String enableStatus){
    this.lambdaUpdate()
        .in(ApprovalCollect::getId,ids)
        .eq(ApprovalCollect::getTenantCode,TenantUtils.getTenantCode())
        .set(ApprovalCollect::getEnableStatus,enableStatus)
        .update();
  }

  /**
   * 逻辑删除
   */
  public void delete(Set<String> ids){
    this.lambdaUpdate()
        .in(ApprovalCollect::getId,ids)
        .eq(ApprovalCollect::getTenantCode,TenantUtils.getTenantCode())
        .set(ApprovalCollect::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  /**
   * 通过id和租户编号查询
   * @param id
   * @param tenantCode
   * @return
   */
  public ApprovalCollect findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(ApprovalCollect::getTenantCode,tenantCode)
        .in(ApprovalCollect::getId,id)
        .one();
  }

  public List<ApprovalCollect> listByIdsAndTenantCode(Set<String> ids, String tenantCode) {
    return this.lambdaQuery()
        .eq(ApprovalCollect::getTenantCode,tenantCode)
        .in(ApprovalCollect::getId,ids)
        .list();
  }
}

