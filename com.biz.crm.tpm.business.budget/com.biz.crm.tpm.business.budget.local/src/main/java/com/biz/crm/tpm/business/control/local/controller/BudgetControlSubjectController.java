package com.biz.crm.tpm.business.control.local.controller;

import com.biz.crm.business.common.page.cache.controller.BusinessPageCacheController;
import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlSubjectDto;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlSubjectVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "预算管控预算科目")
@RestController
@RequestMapping("/v1/budget/budgetControl/subject")
@Slf4j
public class BudgetControlSubjectController extends BusinessPageCacheController<BudgetControlSubjectVo, BudgetControlSubjectDto> {
}
