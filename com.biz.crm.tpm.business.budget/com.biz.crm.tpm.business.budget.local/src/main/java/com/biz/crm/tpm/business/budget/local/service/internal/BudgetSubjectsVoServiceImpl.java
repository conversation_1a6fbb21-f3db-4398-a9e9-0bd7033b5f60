package com.biz.crm.tpm.business.budget.local.service.internal;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.budget.local.entity.BudgetSubjects;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeCategory;
import com.biz.crm.tpm.business.budget.local.repository.BudgetSubjectsRepository;
import com.biz.crm.tpm.business.budget.local.repository.CostTypeCategoryRepository;
import com.biz.crm.tpm.business.budget.sdk.dto.BudgetSubjectsDto;
import com.biz.crm.tpm.business.budget.sdk.dto.BudgetSubjectsLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.event.BudgetSubjectsEventListener;
import com.biz.crm.tpm.business.budget.sdk.event.log.BudgetSubjectsLogEventListener;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.BudgetControlTypeStrategy;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsCrmImportVo;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.enums.RedisKeys.GENERATE_PREFIX;

/**
 * TPM-预算科目;(tpm_budget_subjects)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-5-18
 */
@Service("budgetSubjectsVoService")
@Slf4j
public class BudgetSubjectsVoServiceImpl implements BudgetSubjectsVoService {
    @Autowired
    private BudgetSubjectsRepository budgetSubjectsRepository;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired
    private RedisMutexService redisMutexService;
    @Autowired(required = false)
    private List<BudgetSubjectsEventListener> budgetSubjectsEventListeners;
    @Autowired(required = false)
    private List<BudgetControlTypeStrategy> budgetControlTypeStrategics;
    @Resource
    private LoginUserService loginUserService;
    @Resource
    private CostTypeCategoryRepository costTypeCategoryRepository;

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     * @return
     */
    @Override
    public Page<BudgetSubjectsVo> findByConditions(Pageable pageable, BudgetSubjectsDto dto) {
        ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new BudgetSubjectsDto();
        }
        List<String> selectedCodeList =
                Optional.ofNullable(dto.getSelectedCodeList()).orElse(new ArrayList<>());
        if (StringUtils.isNotEmpty(dto.getSelectedCode())) {
            selectedCodeList.add(dto.getSelectedCode());
        }
        if (!CollectionUtils.isEmpty(selectedCodeList)) {
            dto.setSelectedCodeList(selectedCodeList);
        }
        return this.budgetSubjectsRepository.findByConditions(pageable, dto);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @Override
    public BudgetSubjectsVo findById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        BudgetSubjects budgetSubjects = this.budgetSubjectsRepository.findByIdAndTenantCode(id, TenantUtils.getTenantCode());
        BudgetSubjectsVo budgetSubjectsVo = this.nebulaToolkitService.copyObjectByWhiteList(budgetSubjects, BudgetSubjectsVo.class, LinkedHashSet.class, ArrayList.class);
        return budgetSubjectsVo;
    }

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    @Override
    public BudgetSubjectsVo findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        BudgetSubjects budgetSubjects = this.budgetSubjectsRepository.findByCode(code);
        if (budgetSubjects == null) {
            return null;
        }
        BudgetSubjectsVo budgetSubjectsVo = this.nebulaToolkitService.copyObjectByWhiteList(budgetSubjects, BudgetSubjectsVo.class, LinkedHashSet.class, ArrayList.class);
        return budgetSubjectsVo;
    }

    @Override
    public List<BudgetSubjectsVo> findByCodes(Set<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        List<BudgetSubjects> budgetSubjects = budgetSubjectsRepository.findByCodesAndTenantCode(codes, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(budgetSubjects)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(budgetSubjects, BudgetSubjects.class, BudgetSubjectsVo.class, LinkedHashSet.class, ArrayList.class));
    }

    /**
     * 通过启用状态查询数据
     *
     * @param enableStatus 状态
     * @return 集合数据
     */
    @Override
    public List<BudgetSubjectsVo> findByEnableStatus(String enableStatus) {
        if (StringUtils.isBlank(enableStatus)) {
            return Collections.emptyList();
        }
        List<BudgetSubjects> budgetSubjects = this.budgetSubjectsRepository.findByEnableStatus(enableStatus);
        if (CollectionUtils.isEmpty(budgetSubjects)) {
            return Collections.emptyList();
        }
        Collection<BudgetSubjectsVo> budgetSubjectsVos = this.nebulaToolkitService.copyCollectionByWhiteList(budgetSubjects, BudgetSubjects.class, BudgetSubjectsVo.class, LinkedHashSet.class, ArrayList.class);
        return Lists.newArrayList(budgetSubjectsVos);
    }

    /**
     * 新增数据
     *
     * @param budgetSubjectsVo 实体对象
     * @return 新增结果
     */
    @Transactional(rollbackOn = Exception.class)
    @Override
    public BudgetSubjectsVo create(BudgetSubjectsVo budgetSubjectsVo) {
        FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
        Validate.notNull(loginUserDetails, "未找到当前登陆人信息！");
        budgetSubjectsVo.setOrgCode(loginUserDetails.getOrgCode());
        budgetSubjectsVo.setPositionCode(loginUserDetails.getPostCode());

        budgetSubjectsVo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        this.createValidate(budgetSubjectsVo);
        BudgetSubjects budgetSubjects = this.nebulaToolkitService.copyObjectByWhiteList(budgetSubjectsVo, BudgetSubjects.class, LinkedHashSet.class, ArrayList.class);
        budgetSubjects.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        budgetSubjects.setTenantCode(TenantUtils.getTenantCode());
        this.budgetSubjectsRepository.saveOrUpdate(budgetSubjects);
        budgetSubjectsVo.setId(budgetSubjects.getId());
        if (!CollectionUtils.isEmpty(budgetSubjectsEventListeners)) {
            for (BudgetSubjectsEventListener budgetSubjectsEventListener : budgetSubjectsEventListeners) {
                budgetSubjectsEventListener.onCreated(budgetSubjectsVo);
            }
        }
        // 业务日志新增
        BudgetSubjectsLogEventDto logEventDto = new BudgetSubjectsLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(budgetSubjectsVo);
        SerializableBiConsumer<BudgetSubjectsLogEventListener, BudgetSubjectsLogEventDto> onCreate =
                BudgetSubjectsLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, BudgetSubjectsLogEventListener.class, onCreate);
        return budgetSubjectsVo;
    }

    @Transactional(rollbackOn = Exception.class)
    @Override
    public void createBatch(List<BudgetSubjectsCrmImportVo> budgetSubjectsVoList) {
        FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
        Validate.notNull(loginUserDetails, "未找到当前登陆人信息！");
        Collection<BudgetSubjects> budgetSubjectsEntities = nebulaToolkitService.copyCollectionByWhiteList(budgetSubjectsVoList, BudgetSubjectsCrmImportVo.class, BudgetSubjects.class, LinkedHashSet.class, ArrayList.class);
        budgetSubjectsEntities.forEach(e -> {
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setTenantCode(TenantUtils.getTenantCode());
            e.setOrgCode(loginUserDetails.getOrgCode());
            e.setPositionCode(loginUserDetails.getPostCode());
        });
        budgetSubjectsRepository.saveBatch(budgetSubjectsEntities);
    }

    /**
     * 修改新据
     *
     * @param budgetSubjectsVo 实体对象
     * @return 修改结果
     */
    @Transactional
    @Override
    public BudgetSubjectsVo update(BudgetSubjectsVo budgetSubjectsVo) {
        this.updateValidate(budgetSubjectsVo);
        BudgetSubjects budgetSubjects = this.budgetSubjectsRepository.findByIdAndTenantCode(budgetSubjectsVo.getId(), TenantUtils.getTenantCode());
        BudgetSubjectsVo oldBudgetSubjectsVo = this.nebulaToolkitService.copyObjectByWhiteList(budgetSubjects, BudgetSubjectsVo.class, LinkedHashSet.class, ArrayList.class);
        Validate.notNull(budgetSubjects, "修改数据不存在，请检查！");
        // 修改字段
        budgetSubjects.setBudgetSubjectsName(budgetSubjectsVo.getBudgetSubjectsName());
        budgetSubjects.setRemark(budgetSubjectsVo.getRemark());
        budgetSubjects.setBudgetSubjectsType(budgetSubjectsVo.getBudgetSubjectsType());
        budgetSubjects.setGroupCode(budgetSubjectsVo.getGroupCode());
        budgetSubjects.setTenantCode(TenantUtils.getTenantCode());
        budgetSubjects.setCooperateType(budgetSubjectsVo.getCooperateType());
        budgetSubjects.setParentBudgetSubjectsCode(budgetSubjectsVo.getParentBudgetSubjectsCode());
        budgetSubjects.setParentBudgetSubjectsName(budgetSubjectsVo.getParentBudgetSubjectsName());
        budgetSubjects.setLevel(budgetSubjectsVo.getLevel());
        budgetSubjects.setTaxRate(budgetSubjectsVo.getTaxRate());
        this.budgetSubjectsRepository.saveOrUpdate(budgetSubjects);
        if (!CollectionUtils.isEmpty(budgetSubjectsEventListeners)) {
            for (BudgetSubjectsEventListener budgetSubjectsEventListener : budgetSubjectsEventListeners) {
                budgetSubjectsEventListener.onUpdate(oldBudgetSubjectsVo, budgetSubjectsVo);
            }
        }
        //业务日志编辑
        BudgetSubjectsVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(budgetSubjects, BudgetSubjectsVo.class, HashSet.class, ArrayList.class);
        BudgetSubjectsLogEventDto logEventDto = new BudgetSubjectsLogEventDto();
        logEventDto.setOriginal(oldBudgetSubjectsVo);
        logEventDto.setNewest(newVo);
        SerializableBiConsumer<BudgetSubjectsLogEventListener, BudgetSubjectsLogEventDto> onUpdate =
                BudgetSubjectsLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, BudgetSubjectsLogEventListener.class, onUpdate);
        return budgetSubjectsVo;
    }

    /**
     * 删除数据
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @Transactional
    @Override
    public void delete(List<String> ids) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
        List<BudgetSubjects> budgetSubjectss = this.budgetSubjectsRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(budgetSubjectss)) {
            return;
        }
        Collection<BudgetSubjectsVo> budgetSubjectsVos = this.nebulaToolkitService.copyCollectionByWhiteList(budgetSubjectss, BudgetSubjects.class, BudgetSubjectsVo.class, LinkedHashSet.class, ArrayList.class);
        this.budgetSubjectsRepository.removeByIdsAndTenantCode(ids, TenantUtils.getTenantCode());
        //有下级预算科目不能删除。必需在上一步删除后再查询判断，可排除本次也删除了下级的问题
        List<String> budgetSubjectsCodes = budgetSubjectsVos.stream().map(BudgetSubjectsVo::getBudgetSubjectsCode).collect(Collectors.toList());
        List<String> hasChildSubjectCodeList = budgetSubjectsRepository.findHasChildSubjectCodeList(budgetSubjectsCodes);

        //是否存在关联的活动大类。
        List<CostTypeCategory> costTypeCategoryList = costTypeCategoryRepository.findAllByBudgetSubjectsCodes(budgetSubjectsCodes);
        Map<String, String> costTypeCategoryMap = costTypeCategoryList.stream()
                .collect(Collectors.groupingBy(CostTypeCategory::getBudgetSubjectsCode,
                        Collectors.mapping(CostTypeCategory::getCategoryCode, Collectors.joining(","))));

        for (BudgetSubjectsVo budgetSubjectsVo : budgetSubjectsVos) {
            Validate.isTrue(!hasChildSubjectCodeList.contains(budgetSubjectsVo.getBudgetSubjectsCode()),
                    "预算科目【%s】已经有下级预算科目，不能删除", budgetSubjectsVo.getBudgetSubjectsCode());

            Validate.isTrue(!costTypeCategoryMap.containsKey(budgetSubjectsVo.getBudgetSubjectsCode()),
                    "预算科目%s已被活动大类%s关联，请先取消关联后再删除", budgetSubjectsVo.getBudgetSubjectsCode(), costTypeCategoryMap.get(budgetSubjectsVo.getBudgetSubjectsCode()));
        }
        if (!CollectionUtils.isEmpty(budgetSubjectsEventListeners)) {
            for (BudgetSubjectsEventListener budgetSubjectsEventListener : budgetSubjectsEventListeners) {
                for (BudgetSubjectsVo budgetSubjectsVo : budgetSubjectsVos) {
                    budgetSubjectsEventListener.onDeleted(budgetSubjectsVo);
                }
            }
        }
        //业务日志删除
        SerializableBiConsumer<BudgetSubjectsLogEventListener, BudgetSubjectsLogEventDto> onDelete =
                BudgetSubjectsLogEventListener::onDelete;
        for (BudgetSubjectsVo budgetSubjectsVo : budgetSubjectsVos) {
            BudgetSubjectsLogEventDto logEventDto = new BudgetSubjectsLogEventDto();
            logEventDto.setOriginal(budgetSubjectsVo);
            logEventDto.setNewest(null);
            this.nebulaNetEventClient.publish(logEventDto, BudgetSubjectsLogEventListener.class, onDelete);
        }
    }

    /**
     * 批量根据id启用
     *
     * @param ids
     */
    @Override
    @Transactional
    public void enable(List<String> ids) {
        Validate.notEmpty(ids, "启用时，id不能为空");
        List<BudgetSubjects> budgetSubjectss = this.budgetSubjectsRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(budgetSubjectss)) {
            return;
        }
        Collection<BudgetSubjectsVo> budgetSubjectsVos = this.nebulaToolkitService.copyCollectionByWhiteList(budgetSubjectss, BudgetSubjects.class, BudgetSubjectsVo.class, LinkedHashSet.class, ArrayList.class);
        this.budgetSubjectsRepository.updateEnableStatusByIds(EnableStatusEnum.ENABLE, ids);
        if (!CollectionUtils.isEmpty(budgetSubjectsEventListeners)) {
            for (BudgetSubjectsEventListener budgetSubjectsEventListener : budgetSubjectsEventListeners) {
                for (BudgetSubjectsVo budgetSubjectsVo : budgetSubjectsVos) {
                    budgetSubjectsEventListener.onEnable(budgetSubjectsVo);
                }
            }
        }
        //业务日志启用
        SerializableBiConsumer<BudgetSubjectsLogEventListener, BudgetSubjectsLogEventDto> onEnable =
                BudgetSubjectsLogEventListener::onEnable;
        for (BudgetSubjectsVo budgetSubjectsVo : budgetSubjectsVos) {
            BudgetSubjectsLogEventDto logEventDto = new BudgetSubjectsLogEventDto();
            //启用只涉及一个字段变更，传入旧对象只为了获取ID与启用状态,不从新对象中取值
            logEventDto.setOriginal(budgetSubjectsVo);
            logEventDto.setNewest(null);
            this.nebulaNetEventClient.publish(logEventDto, BudgetSubjectsLogEventListener.class, onEnable);
        }
    }

    /**
     * 批量根据id禁用
     *
     * @param ids
     */
    @Override
    @Transactional
    public void disable(List<String> ids) {
        Validate.notEmpty(ids, "禁用时，id不能为空");
        List<BudgetSubjects> budgetSubjectss = this.budgetSubjectsRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(budgetSubjectss)) {
            return;
        }
        Collection<BudgetSubjectsVo> budgetSubjectsVos = this.nebulaToolkitService.copyCollectionByWhiteList(budgetSubjectss, BudgetSubjects.class, BudgetSubjectsVo.class, LinkedHashSet.class, ArrayList.class);
        this.budgetSubjectsRepository.updateEnableStatusByIds(EnableStatusEnum.DISABLE, ids);
        if (!CollectionUtils.isEmpty(budgetSubjectsEventListeners)) {
            for (BudgetSubjectsEventListener budgetSubjectsEventListener : budgetSubjectsEventListeners) {
                for (BudgetSubjectsVo budgetSubjectsVo : budgetSubjectsVos) {
                    budgetSubjectsEventListener.onEnable(budgetSubjectsVo);
                }
            }
        }
        //业务日志禁用
        SerializableBiConsumer<BudgetSubjectsLogEventListener, BudgetSubjectsLogEventDto> onDisable =
                BudgetSubjectsLogEventListener::onDisable;
        for (BudgetSubjectsVo budgetSubjectsVo : budgetSubjectsVos) {
            BudgetSubjectsLogEventDto logEventDto = new BudgetSubjectsLogEventDto();
            //启用只涉及一个字段变更，传入旧对象只为了获取ID与启用状态,不从新对象中取值
            logEventDto.setOriginal(budgetSubjectsVo);
            logEventDto.setNewest(null);
            this.nebulaNetEventClient.publish(logEventDto, BudgetSubjectsLogEventListener.class, onDisable);
        }
    }

    /**
     * 生成操作标记
     */
    @Override
    public String preSave() {
        String prefix = UUID.randomUUID().toString();
        // 1天后过期
        this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, TimeUnit.MILLISECONDS.convert(1, TimeUnit.DAYS));
        return prefix;
    }

    /**
     * 验证与操作标记
     */
    private void validationPrefix(BudgetSubjectsVo budgetSubjectsVo) {
        // 验证重复提交标识
        String prefix = budgetSubjectsVo.getPrefix();
        Validate.notBlank(prefix, "错误的预操作标记，请检查!!");
        Validate.isTrue(StringUtils.isNotBlank(this.redisMutexService.getMCode(GENERATE_PREFIX, prefix)), "没有发现预操作标记，可能是因为重复操作的原因!");
        boolean isLock = false;
        try {
            if (isLock = this.redisMutexService.tryLock(prefix, TimeUnit.MILLISECONDS, 1)) {
                this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, 1L);
            } else {
                throw new IllegalArgumentException("请不要重复操作!!");
            }
        } finally {
            if (isLock) {
                this.redisMutexService.unlock(prefix);
            }
        }
    }

    /**
     * 创建验证
     *
     * @param budgetSubjectsVo
     */
    private void createValidate(BudgetSubjectsVo budgetSubjectsVo) {
        Validate.notNull(budgetSubjectsVo, "新增时，对象信息不能为空！");
        budgetSubjectsVo.setId(null);
        // 验证重复操作
//        this.validationPrefix(budgetSubjectsVo);
        Validate.notBlank(budgetSubjectsVo.getBudgetSubjectsCode(), "新增数据时，预算科目编码不能为空！");
        BudgetSubjectsVo current = this.findByCode(budgetSubjectsVo.getBudgetSubjectsCode());
        Validate.isTrue(current == null, "新增数据时，预算科目编号重复，请检查！");
        Validate.notBlank(budgetSubjectsVo.getLevel(), "新增数据时，科目层级不能为空！");
        if (!budgetSubjectsVo.getLevel().equals("1")) {
            Validate.notBlank(budgetSubjectsVo.getParentBudgetSubjectsCode(), "新增数据时，上级预算科目不能为空！");
            BudgetSubjects parent = budgetSubjectsRepository.findByCode(budgetSubjectsVo.getParentBudgetSubjectsCode());
            Validate.isTrue(Integer.valueOf(budgetSubjectsVo.getLevel()) > Integer.valueOf(parent.getLevel()), "新增数据时，上级预算科目必须大于当前层级！");
        } else {
            Validate.isTrue(StringUtils.isBlank(budgetSubjectsVo.getParentBudgetSubjectsCode()), "新增数据时，上级预算科目必须为空！");
        }
        duplicateValidate(budgetSubjectsVo);
    }

    /**
     * 修改验证
     *
     * @param budgetSubjectsVo
     */
    private void updateValidate(BudgetSubjectsVo budgetSubjectsVo) {
        Validate.notNull(budgetSubjectsVo, "修改时，对象信息不能为空！");
        // 验证重复操作
//        this.validationPrefix(budgetSubjectsVo);
        Validate.notBlank(budgetSubjectsVo.getId(), "修改数据时，主键不能为空！");
        Validate.notBlank(budgetSubjectsVo.getLevel(), "修改数据时，科目层级不能为空！");
        if (!budgetSubjectsVo.getLevel().equals("1")) {
            Validate.notBlank(budgetSubjectsVo.getParentBudgetSubjectsCode(), "修改数据时，上级预算科目不能为空！");
            BudgetSubjects parent = budgetSubjectsRepository.findByCode(budgetSubjectsVo.getParentBudgetSubjectsCode());
            Validate.isTrue(Integer.valueOf(budgetSubjectsVo.getLevel()) > Integer.valueOf(parent.getLevel()), "修改数据时，上级预算科目必须大于当前层级！");
        } else {
            Validate.isTrue(StringUtils.isBlank(budgetSubjectsVo.getParentBudgetSubjectsCode()), "修改数据时，上级预算科目必须为空！");
        }
        duplicateValidate(budgetSubjectsVo);
    }

    /**
     * 重复验证
     *
     * @param budgetSubjectsVo
     */
    private void duplicateValidate(BudgetSubjectsVo budgetSubjectsVo) {
        List<BudgetSubjects> codesExist = budgetSubjectsRepository.findByCodesOrNames(Lists.newArrayList(budgetSubjectsVo.getBudgetSubjectsCode()), null);
        codesExist = codesExist.stream().filter(e -> StringUtils.isBlank(budgetSubjectsVo.getId()) || !StringUtils.equals(e.getId(), budgetSubjectsVo.getId())).collect(Collectors.toList());
        StringJoiner errorMsg = new StringJoiner(",");
        if (!CollectionUtils.isEmpty(codesExist)) {
            errorMsg.add("预算科目编码【" + budgetSubjectsVo.getBudgetSubjectsCode() + "】重复，请重新填写");
        }
//        List<BudgetSubjects> namesExist = budgetSubjectsRepository.findByCodesOrNames(null, Lists.newArrayList(budgetSubjectsVo.getBudgetSubjectsName()));
//        namesExist = namesExist.stream().filter(e -> StringUtils.isBlank(budgetSubjectsVo.getId()) || !StringUtils.equals(e.getId(), budgetSubjectsVo.getId())).collect(Collectors.toList());
//        if (!CollectionUtils.isEmpty(namesExist)) {
//            errorMsg.add("预算科目名称【" + budgetSubjectsVo.getBudgetSubjectsName() + "】重复，请重新填写");
//        }
        Validate.isTrue(StringUtils.isBlank(errorMsg.toString()), errorMsg.toString());
    }

    /**
     * 根据科目预算编号获取对应的策略
     */
    @Override
    public BudgetControlTypeStrategy findBudgetControlTypeStrategyByCode(String budgetSubjectCode) {
        if (CollectionUtils.isEmpty(budgetControlTypeStrategics)) {
            return null;
        }
        BudgetSubjectsVo subjectsVo = this.findByCode(budgetSubjectCode);
        if (subjectsVo == null) {
            return null;
        }
        return budgetControlTypeStrategics.stream().filter(item -> StringUtils.equals(item.getCode(), subjectsVo.getControlTypeCode())).findFirst().orElse(null);
    }

    /**
     * 根据控制类型编号获取对应的策略
     */
    @Override
    public BudgetControlTypeStrategy findBudgetControlTypeStrategyByControlTypeCode(String code) {
        if (CollectionUtils.isEmpty(budgetControlTypeStrategics)) {
            return null;
        }
        return budgetControlTypeStrategics.stream().filter(item -> StringUtils.equals(item.getCode(), code)).findFirst().orElse(null);
    }


    /**
     * 通过名称查询预算科目信息
     *
     * @param budgetSubjectNameList
     * @return
     */
    @Override
    public List<BudgetSubjectsVo> findListByBudgetSubjectNames(List<String> budgetSubjectNameList, String budgetSubjectLevel) {
        List<List<String>> partitionList = Lists.partition(budgetSubjectNameList, 800);
        List<BudgetSubjects> dataList = Lists.newArrayList();
        for (List<String> strings : partitionList) {
            List<BudgetSubjects> list = budgetSubjectsRepository.findListByBudgetSubjectNames(strings, budgetSubjectLevel);
            if (!CollectionUtils.isEmpty(list)) {
                dataList.addAll(list);
            }
        }

        return (List<BudgetSubjectsVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, BudgetSubjects.class, BudgetSubjectsVo.class, HashSet.class, ArrayList.class);
    }


    /**
     * 查询二级预算科目
     *
     * @return
     */
    @Override
    public List<BudgetSubjectsVo> findSecondBudgetSubject() {
        List<BudgetSubjects> dataList = budgetSubjectsRepository.findSecondBudgetSubject();
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return (List<BudgetSubjectsVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, BudgetSubjects.class, BudgetSubjectsVo.class, HashSet.class, ArrayList.class);
    }


    /**
     * 查询所有上级科目
     *
     * @param budgetSubjectCode
     * @return
     */
    @Override
    public Set<String> findAllParentSubjectCodesByCodes(String budgetSubjectCode) {
        return budgetSubjectsRepository.findAllParentSubjectCodesByCodes(budgetSubjectCode);
    }

    @Override
    public String findBudgetSubjectByDetailCode(String detailCode) {
        return budgetSubjectsRepository.findBudgetSubjectByDetailCode(detailCode);
    }

    @Override
    public List<BudgetSubjectsVo> findChildrensByCodes(List<String> budgetSubjectsCodes) {
        if (CollectionUtils.isEmpty(budgetSubjectsCodes)) {
            return Lists.newArrayList();
        }
        List<BudgetSubjects> budgetSubjectsVos = budgetSubjectsRepository.findChildrensByCodes(budgetSubjectsCodes);

        return (List<BudgetSubjectsVo>) nebulaToolkitService.copyCollectionByWhiteList(budgetSubjectsVos,
                BudgetSubjects.class, BudgetSubjectsVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<BudgetSubjectsVo> findByCodeList(List<String> subjectCodes) {
        log.info("findByCodeList:{}", subjectCodes);
        if (CollectionUtils.isEmpty(subjectCodes)) {
            return Lists.newArrayList();
        }
        List<BudgetSubjects> budgetSubjects = budgetSubjectsRepository.findByCodeList(subjectCodes);
        log.info("findByCodeList:{}", JSON.toJSONString(budgetSubjects));
        return (List<BudgetSubjectsVo>) nebulaToolkitService.copyCollectionByWhiteList(budgetSubjects,
                BudgetSubjects.class, BudgetSubjectsVo.class, HashSet.class, ArrayList.class);
    }
}