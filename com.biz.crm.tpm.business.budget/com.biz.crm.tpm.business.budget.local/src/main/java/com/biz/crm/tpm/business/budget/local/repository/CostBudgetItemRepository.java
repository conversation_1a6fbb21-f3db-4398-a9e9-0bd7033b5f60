package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.budget.local.entity.CostBudgetItem;
import com.biz.crm.tpm.business.budget.local.mapper.CostBudgetItemMapper;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetItemDto;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetOperateType;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 费用预算明细(CostBudgetItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class CostBudgetItemRepository extends ServiceImpl<CostBudgetItemMapper, CostBudgetItem> {

    @Autowired
    private CostBudgetItemMapper costBudgetItemMapper;

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    public Page<CostBudgetItemVo> findByConditions(Pageable pageable, CostBudgetItemDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<CostBudgetItemVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        costBudgetItemMapper.findByConditions(page, dto);
        if (page.getTotal() != 0L) {
            List<CostBudgetItemVo> records = page.getRecords();
        }
        return page;
    }

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    public Page<CostBudgetItemVo> findByCostBudgetConditions(Pageable pageable, CostBudgetDto dto) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 50);
        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<CostBudgetItemVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return baseMapper.findByCostBudgetConditions(page, dto);
    }

    /**
     * 根据费用预算编码查询信息
     */
    public List<CostBudgetItem> findByCostBudgetCodeAndTenantCode(String code, String tenantCode) {
        return this.lambdaQuery()
                .eq(CostBudgetItem::getCostBudgetCode, code)
                .eq(CostBudgetItem::getTenantCode, tenantCode)
                .list();
    }

    /**
     * 根据主键id查询数据
     */
    public CostBudgetItem findById(String id) {
        return this.lambdaQuery()
                .eq(CostBudgetItem::getId, id)
                .eq(CostBudgetItem::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }

    /**
     * 统计是否含有非期初信息
     */
    public int countNoInitItemByCostBudgetCodeAndTenantCode(String code, String tenantCode) {
        return this.lambdaQuery().eq(CostBudgetItem::getCostBudgetCode, code).eq(CostBudgetItem::getTenantCode, tenantCode).
                ne(CostBudgetItem::getOperateType, CostBudgetOperateType.INITIALIZATION.getCode()).count();
    }

    /**
     * 根据费用预算集合编码查询信息
     *
     * @param costBudgetCodes
     * @param tenantCode
     * @return
     */
    public List<CostBudgetItem> findByCostBudgetCodesAndTenantCode(List<String> costBudgetCodes, String tenantCode) {
        return this.lambdaQuery()
                .in(CostBudgetItem::getCostBudgetCode, costBudgetCodes)
                .orderByDesc(CostBudgetItem::getSortIndex)
                .eq(CostBudgetItem::getTenantCode, tenantCode)
                .list();
    }

    /**
     * 根据预算费用编码，统计明细总数
     */
    public int countByCostBudgetCodeAndTenantCode(String costBudgetCode, String tenantCode) {
        return this.lambdaQuery()
                .eq(CostBudgetItem::getCostBudgetCode, costBudgetCode)
                .eq(CostBudgetItem::getTenantCode, tenantCode)
                .count();
    }

    /**
     * 根据业务编号，查询信息
     */
    public List<CostBudgetItem> findByBusinessCodeAndTenantCode(String businessCode, String tenantCode) {
        return this.lambdaQuery()
                .eq(CostBudgetItem::getBusinessCode, businessCode)
                .eq(CostBudgetItem::getTenantCode, tenantCode)
                .orderByDesc(CostBudgetItem::getSortIndex)
                .list();
    }

    /**
     * 根据业务编码和业务明细编码，查询信息
     */
    public List<CostBudgetItem> findByBusinessCodeAndBusinessItemCodeAndTenantCode(String businessCode, String businessItemCode, String tenantCode) {
        return this.lambdaQuery()
                .eq(CostBudgetItem::getBusinessCode, businessCode)
                .eq(CostBudgetItem::getBusinessItemCode, businessItemCode)
                .eq(CostBudgetItem::getTenantCode, tenantCode)
                .orderByDesc(CostBudgetItem::getSortIndex)
                .list();
    }



}

