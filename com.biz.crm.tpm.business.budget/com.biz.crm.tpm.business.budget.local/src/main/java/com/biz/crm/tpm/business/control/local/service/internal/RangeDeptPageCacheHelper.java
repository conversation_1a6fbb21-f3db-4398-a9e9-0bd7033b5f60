package com.biz.crm.tpm.business.control.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.control.local.repository.BudgetControlRangeRepository;
import com.biz.crm.tpm.business.control.sdk.constant.BudgetControlConstant;
import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlDeptRangeDto;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlDeptRangeVo;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlRangeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分页缓存
 */
@Slf4j
@Component
public class RangeDeptPageCacheHelper extends BusinessPageCacheHelper<BudgetControlDeptRangeVo, BudgetControlDeptRangeDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private BudgetControlRangeRepository budgetControlRangeRepository;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return BudgetControlConstant.CACHE_KEY_DEPT_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<BudgetControlDeptRangeDto> getDtoClass() {
        return BudgetControlDeptRangeDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<BudgetControlDeptRangeVo> getVoClass() {
        return BudgetControlDeptRangeVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param dto
     * @param cacheKey
     */
    @Override
    public List<BudgetControlDeptRangeDto> findDtoListFromRepository(BudgetControlDeptRangeDto dto, String cacheKey) {
        if (StringUtils.isBlank(dto.getControlCode())) {
            return new ArrayList<>();
        }
        //相同明细编码列转行
        List<BudgetControlRangeVo> vos = budgetControlRangeRepository.findByCode(dto.getControlCode());
        if (CollectionUtils.isEmpty(vos)) {
            return new ArrayList<>();
        }
        vos = vos.stream().filter(e -> e.getRangeType().equals("dept")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vos)) {
            return new ArrayList<>();
        }
        Map<String, List<BudgetControlRangeVo>> deptMap = vos.stream().collect(Collectors.groupingBy(BudgetControlRangeVo::getControlDetailCode));
        List<BudgetControlRangeVo> deptVoList = new ArrayList<>();
        deptMap.forEach((k, v) -> {
            BudgetControlRangeVo deptVo = new BudgetControlRangeVo();
            BeanUtils.copyProperties(v.get(0), deptVo);
            if (v.size() > 1) {
                StringBuilder sbCode = new StringBuilder();
                StringBuilder sbName = new StringBuilder();
                v.forEach(e -> {
                    sbCode.append(e.getDepartmentCode()).append(",");
                    sbName.append(e.getDepartmentName()).append(",");
                });
                deptVo.setDepartmentCode(sbCode.substring(0, sbCode.length() - 1));
                deptVo.setDepartmentName(sbName.substring(0, sbName.length() - 1));
            }
            deptVoList.add(deptVo);
        });
        return (List<BudgetControlDeptRangeDto>) nebulaToolkitService.copyCollectionByBlankList(deptVoList, BudgetControlRangeVo.class, BudgetControlDeptRangeDto.class, HashSet.class, ArrayList.class);
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlDeptRangeDto> newItem(String cacheKey, List<BudgetControlDeptRangeDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlDeptRangeDto> copyItem(String cacheKey, List<BudgetControlDeptRangeDto> itemList) {
        List<BudgetControlDeptRangeDto> newItemList = (List<BudgetControlDeptRangeDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, BudgetControlDeptRangeDto.class, BudgetControlDeptRangeDto.class, HashSet.class, ArrayList.class);
        for (BudgetControlDeptRangeDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 获取Dto中的主键
     *
     * @param dto
     * @return 主键
     */
    @Override
    public Object getDtoKey(BudgetControlDeptRangeDto dto) {
        return dto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param dto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(BudgetControlDeptRangeDto dto) {
        return dto.getChecked();
    }

}
