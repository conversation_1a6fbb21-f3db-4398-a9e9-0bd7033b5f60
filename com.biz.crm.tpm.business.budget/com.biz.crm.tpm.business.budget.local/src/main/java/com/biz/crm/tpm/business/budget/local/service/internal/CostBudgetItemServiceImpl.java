package com.biz.crm.tpm.business.budget.local.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.tpm.business.activities.marketingplan.entity.MarketingPlanCase;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanVo;
import com.biz.crm.tpm.business.activities.sdk.service.MarkingPlanVoService;
import com.biz.crm.tpm.business.budget.local.entity.CostBudgetItem;
import com.biz.crm.tpm.business.budget.local.repository.CostBudgetItemRepository;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetItemDto;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetOperateType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetItemVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingSdkService;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CostBudgetItemServiceImpl implements CostBudgetItemVoService {

    @Autowired
    private CostBudgetItemRepository costBudgetItemRepository;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired
    private CostBudgetVoService costBudgetService;

    @Resource
    private CostBudgetItemComponent costBudgetItemComponent;

    @Autowired
    private MarketingPlanService marketingPlanService;
    @Autowired
    private MarketingPlanCaseService marketingPlanCaseService;
    @Autowired
    WithHoldingSdkService withHoldingSdkService;

    @Override
    public Page<CostBudgetItemVo> findByConditions(Pageable pageable, CostBudgetItemDto dto) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 50);
        }
        if (dto == null) {
            dto = new CostBudgetItemDto();
        }
        if (StringUtils.isBlank(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        List<CostBudgetItem> items = costBudgetItemRepository.findByCostBudgetCodeAndTenantCode(dto.getCostBudgetCode(), dto.getTenantCode());
        if (!CollectionUtils.isEmpty(items)) {
            for (CostBudgetItem item : items) {
                item.setDatabase(BooleanEnum.TRUE.getCapital());
            }
        }
        List<CostBudgetItem> itemList = Lists.newArrayList();
        //查询方案、计提、结案
        costBudgetItemComponent.buildCostBudgetItem(itemList, items, dto.getCostBudgetCode());
        if (CollectionUtils.isEmpty(itemList)) {
            return new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        }
        if (StringUtils.isNotBlank(dto.getOperateType())) {
            final String operateType = dto.getOperateType();
            itemList = itemList.stream().filter(e -> StringUtils.equals(e.getOperateType(), operateType)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(itemList)) {
            return new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        }

        itemList = itemList.stream().sorted(Comparator.comparing(CostBudgetItem::getModifyTime).reversed()).
                skip(pageable.getPageSize() * (pageable.getPageNumber() > 0 ? pageable.getPageNumber() - 1 : 0))
                .limit(pageable.getPageSize()).collect(Collectors.toList());
        long totalSize = itemList.size();
        List<CostBudgetItemVo> itemVos = this.prefect(itemList);
        Page<CostBudgetItemVo> result = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        result.setRecords(itemVos);
        result.setTotal(totalSize);
        changeYXFAProcessingOperationAmount(itemVos);
        changeJTProcessingOperationAmount(itemVos);
        return result;
    }

    private void changeJTProcessingOperationAmount(List<CostBudgetItemVo> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        list = list.stream().filter(item->"计提".equals(item.getSource())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Map<String, BigDecimal> map = withHoldingSdkService.findByBusinessCodes(list.stream().map(CostBudgetItemVo::getBusinessCode).collect(Collectors.toList()));
        if(Objects.isNull(map) || map.size() == 0){
            return;
        }
        for (CostBudgetItemVo costBudgetItemVo : list) {
            String businessCode = costBudgetItemVo.getBusinessCode();
            if(!map.containsKey(businessCode)){
                continue;
            }
            costBudgetItemVo.setOperateAmount(map.get(businessCode));
        }
    }

    private void changeYXFAProcessingOperationAmount(List<CostBudgetItemVo> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        list = list.stream().filter(item->"营销方案".equals(item.getSource())
                && "use".equals(item.getOperateType())
                && item.getBusinessCode().startsWith("BGMX")).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> bizCodes = list.stream().map(CostBudgetItemVo::getBusinessCode).collect(Collectors.toList());
        List<MarketingPlanCase> planCases = marketingPlanCaseService.findListBySchemeDetailCodes(bizCodes);
        if(CollectionUtils.isEmpty(planCases)){
            return;
        }
        List<MarketingPlanVo>  marketingPlanVoList= marketingPlanService.findListBySchemeCodes(planCases.stream().map(item -> item.getSchemeCode()).distinct().collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(marketingPlanVoList)){
            return;
        }
        List<MarketingPlanVo> commitList = marketingPlanVoList.stream().filter(item -> StringUtils.equals(ProcessStatusEnum.COMMIT.getDictCode(), item.getProcessStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(commitList)){
            return;
        }
        // 读取变更前的金额
        List<MarketingPlanCase> needsReadBeforeAmount = planCases.stream().filter(item -> {
            String schemeCode = item.getSchemeCode();
            for (MarketingPlanVo marketingPlanVo : commitList) {
                if (StringUtils.equals(schemeCode, marketingPlanVo.getSchemeCode())) {
                    return Boolean.TRUE;
                }
            }
            return Boolean.FALSE;
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(needsReadBeforeAmount)){

            return;
        }
        HashMap<String,String> map = new HashMap<>();
        List<String> originalSchemeDetailCodes = needsReadBeforeAmount.stream().filter(item -> StringUtils.isNotBlank(item.getOriginalSchemeDetailCode())).map(item -> item.getOriginalSchemeDetailCode()).collect(Collectors.toList());
        for (MarketingPlanCase marketingPlanCase : needsReadBeforeAmount) {
            map.put(marketingPlanCase.getOriginalSchemeDetailCode(),marketingPlanCase.getSchemeDetailCode());
        }
        if(CollectionUtils.isEmpty(originalSchemeDetailCodes)){
            return;
        }
        List<MarketingPlanCase> originList = marketingPlanCaseService.findListBySchemeDetailCodes(originalSchemeDetailCodes);
        if(CollectionUtils.isEmpty(originList)){
            return;
        }
        log.info("  originList {} changeList {}",JSONObject.toJSONString(originList),JSONObject.toJSONString(list));
        for (MarketingPlanCase marketingPlanCase : originList) {
            String schemeDetailCode = marketingPlanCase.getSchemeDetailCode();
            if (map.containsKey(schemeDetailCode)) {
                String originDetailCode = map.get(schemeDetailCode);
                for (CostBudgetItemVo costBudgetItemVo : list) {
                    if(StringUtils.equals(originDetailCode,costBudgetItemVo.getBusinessCode())){
                        if(costBudgetItemVo.getOperateAmount().compareTo(marketingPlanCase.getApplyAmount())>0){
                            costBudgetItemVo.setOperateAmount(costBudgetItemVo.getOperateAmount());
                        }else {
                            costBudgetItemVo.setOperateAmount(marketingPlanCase.getApplyAmount());
                        }
                    }
                }
            }
        }
    }

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    @Override
    public Page<CostBudgetItemVo> findByConditionsItem(Pageable pageable, CostBudgetDto dto) {
        ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        if (Objects.isNull(dto)) {
            dto = new CostBudgetDto();
        }
        return costBudgetItemRepository.findByCostBudgetConditions(pageable, dto);
    }

    @Override
    @Transactional
    public List<CostBudgetItemVo> create(List<CostBudgetItemDto> costBudgetItemDtos) {
        Validate.notEmpty(costBudgetItemDtos, "费用预算明细信息不能为空");
        for (CostBudgetItemDto costBudgetItemDto : costBudgetItemDtos) {
            Validate.notNull(costBudgetItemDto, "费用预算明细信息不能为空");
            Validate.isTrue(StringUtils.isBlank(costBudgetItemDto.getId()), "费用预算明细id主键不能有值");
            costBudgetItemDto.setId(null);
            Validate.notBlank(costBudgetItemDto.getCostBudgetCode(), "费用预算编码不能为空");
            Validate.notBlank(costBudgetItemDto.getOperateType(), "费用预算操作编码不能为空");
            Validate.notBlank(costBudgetItemDto.getTenantCode(), "租户编码不能为空");
            Validate.notBlank(costBudgetItemDto.getBusinessCode(), "业务编码不能为空");
            Validate.notBlank(costBudgetItemDto.getSource(), "费用预算明细来源不能为空");
            Validate.notNull(costBudgetItemDto.getBalance(), "操作前余额不能为空");
            Validate.notNull(costBudgetItemDto.getFinalBalance(), "最终余额不能为空");
            Validate.notNull(costBudgetItemDto.getOperateAmount(), "当前操作金额不能为空");
            Validate.isTrue(CostBudgetItemSourceType.contains(costBudgetItemDto.getSource()), "未知的费用预算明细来源【%s】，请检查", costBudgetItemDto.getSource());
            Validate.isTrue(costBudgetItemDto.getOperateAmount().compareTo(BigDecimal.ZERO) >= 0, "操作金额必须大于0，请检查");
            if (StringUtils.equals(costBudgetItemDto.getOperateType(), CostBudgetOperateType.USED.getCode()) || StringUtils.equals(costBudgetItemDto.getOperateType(), CostBudgetOperateType.BACK.getCode())) {
                Validate.notBlank(costBudgetItemDto.getBusinessItemCode(), "业务明细编码不能为空");
            }
        }

        Collection<CostBudgetItem> items = nebulaToolkitService.copyCollectionByWhiteList(costBudgetItemDtos, CostBudgetItemDto.class, CostBudgetItem.class, HashSet.class, ArrayList.class);
        for (CostBudgetItem item : items) {
            //计算当前明细总数
            int count = costBudgetItemRepository.countByCostBudgetCodeAndTenantCode(item.getCostBudgetCode(), TenantUtils.getTenantCode());
            item.setSortIndex(count + 1);
            item.setTenantCode(TenantUtils.getTenantCode());
            costBudgetItemRepository.save(item);
        }
        Collection<CostBudgetItemVo> itemVos = nebulaToolkitService.copyCollectionByWhiteList(items, CostBudgetItem.class, CostBudgetItemVo.class, HashSet.class, ArrayList.class);
        return Lists.newArrayList(itemVos);
    }

    @Override
    @Transactional
    public CostBudgetItemVo updateForInitializationItem(CostBudgetItemDto costBudgetItemDto) {
        CostBudgetItem costBudgetItem = nebulaToolkitService.copyObjectByWhiteList(costBudgetItemDto, CostBudgetItem.class, HashSet.class, ArrayList.class);
        Validate.notNull(costBudgetItem, "根据提供的主键id值，未能获取到相应信息");
        costBudgetItem.setBalance(costBudgetItemDto.getBalance());
        costBudgetItem.setOperateAmount(costBudgetItemDto.getOperateAmount());
        costBudgetItem.setFinalBalance(costBudgetItemDto.getOperateAmount());
        costBudgetItem.setTenantCode(TenantUtils.getTenantCode());
        costBudgetItemRepository.saveOrUpdate(costBudgetItem);
        return nebulaToolkitService.copyObjectByWhiteList(costBudgetItem, CostBudgetItemVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<CostBudgetItemVo> findByCostBudgetCode(String costBudgetCode) {
        if (StringUtils.isBlank(costBudgetCode)) {
            return Lists.newArrayList();
        }
        List<CostBudgetItem> items = costBudgetItemRepository.findByCostBudgetCodeAndTenantCode(costBudgetCode, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(items)) {
            return Lists.newArrayList();
        }
        Collection<CostBudgetItemVo> itemVos = nebulaToolkitService.copyCollectionByWhiteList(items, CostBudgetItem.class, CostBudgetItemVo.class, HashSet.class, ArrayList.class);
        return Lists.newArrayList(itemVos);
    }

    /**
     * 根据费用预算编码集合，查询明细信息
     * k-费用预算编码 v-相关明细信息
     */
    @Override
    public Map<String, List<CostBudgetItemVo>> findByCostBudgetCodes(List<String> costBudgetCodes) {
        if (CollectionUtils.isEmpty(costBudgetCodes)) {
            return Maps.newHashMap();
        }
        List<CostBudgetVo> costBudgetVos = costBudgetService.findByCodes(Sets.newHashSet(costBudgetCodes));
        if (CollectionUtils.isEmpty(costBudgetVos)) {
            return Maps.newHashMap();
        }
        List<CostBudgetItem> items = costBudgetItemRepository.findByCostBudgetCodesAndTenantCode(costBudgetCodes, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(items)) {
            return Maps.newHashMap();
        }
        Map<String, CostBudgetVo> costBudgetMaps = costBudgetVos.stream().collect(Collectors.toMap(CostBudgetVo::getCode, e -> e));
        List<CostBudgetItemVo> vos = this.prefect(items, costBudgetMaps);
        return vos.stream().collect(Collectors.groupingBy(CostBudgetItemVo::getCostBudgetCode));
    }

    @Override
    public boolean existNoInitItemByCostBudgetCode(String costBudgetCode) {
        if (StringUtils.isBlank(costBudgetCode)) {
            return false;
        }
        return costBudgetItemRepository.countNoInitItemByCostBudgetCodeAndTenantCode(costBudgetCode, TenantUtils.getTenantCode()) > 0;
    }

    @Override
    public List<CostBudgetItemVo> findByBusinessCode(String businessCode) {
        if (StringUtils.isBlank(businessCode)) {
            return Lists.newArrayList();
        }
        List<CostBudgetItem> items = costBudgetItemRepository.findByBusinessCodeAndTenantCode(businessCode, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(items)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(items, CostBudgetItem.class, CostBudgetItemVo.class, HashSet.class, ArrayList.class));
    }

    @Override
    public List<CostBudgetItemVo> findByBusinessCodeAndBusinessItemCode(String businessCode, String businessItemCode) {
        if (StringUtils.isBlank(businessItemCode)) {
            return Lists.newArrayList();
        }
        List<CostBudgetItem> items = costBudgetItemRepository.findByBusinessCodeAndBusinessItemCodeAndTenantCode(businessCode, businessItemCode, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(items)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(items, CostBudgetItem.class, CostBudgetItemVo.class, HashSet.class, ArrayList.class));
    }



    /**
     * 完善费用预算主表数据
     */
    private List<CostBudgetItemVo> prefect(List<CostBudgetItem> items) {
        List<CostBudgetItemVo> results = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(items, CostBudgetItem.class, CostBudgetItemVo.class, HashSet.class, ArrayList.class));
        Set<String> custBudgetCodes = items.stream().map(CostBudgetItem::getCostBudgetCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(custBudgetCodes) || custBudgetCodes.size() != 1) {
            return results;
        }
        CostBudgetVo costBudgetVo = costBudgetService.findByCode(results.get(0).getCostBudgetCode());
        if (costBudgetVo == null) {
            return results;
        }
        for (CostBudgetItemVo itemVo : results) {
            if (BooleanEnum.TRUE.getCapital().equals(itemVo.getDatabase())) {
                itemVo.setChannelCode(costBudgetVo.getChannelCode());
                itemVo.setChannelName(costBudgetVo.getChannelName());
                itemVo.setCode(costBudgetVo.getCode());
                itemVo.setCustomerCode(costBudgetVo.getCustomerCode());
                itemVo.setCustomerName(costBudgetVo.getCustomerName());
                itemVo.setDelFlag(costBudgetVo.getDelFlag());
                itemVo.setEnableStatus(costBudgetVo.getEnableStatus());
                itemVo.setInitialAmount(costBudgetVo.getInitialAmount());
                itemVo.setMonth(costBudgetVo.getMonth());
                itemVo.setOrgCode(costBudgetVo.getOrgCode());
                itemVo.setOrgName(costBudgetVo.getOrgName());
                itemVo.setProductCode(costBudgetVo.getProductCode());
                itemVo.setProductLevelCode(costBudgetVo.getProductLevelCode());
                itemVo.setProductLevelName(costBudgetVo.getProductLevelName());
                itemVo.setProductName(costBudgetVo.getProductName());
                itemVo.setQuarter(costBudgetVo.getQuarter());
                itemVo.setTerminalCode(costBudgetVo.getTerminalCode());
                itemVo.setTerminalName(costBudgetVo.getTerminalName());
                itemVo.setType(costBudgetVo.getType());
                itemVo.setYear(costBudgetVo.getYear());
                itemVo.setBudgetSubjectCode(costBudgetVo.getBudgetSubjectCode());
                itemVo.setBudgetSubjectName(costBudgetVo.getBudgetSubjectName());
                itemVo.setRemark(costBudgetVo.getRemark());
            }
        }
        return results;
    }

    /**
     * 完善费用预算主表数据
     */
    private List<CostBudgetItemVo> prefect(List<CostBudgetItem> items, Map<String, CostBudgetVo> costBudgetVoMap) {
        List<CostBudgetItemVo> results = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(items, CostBudgetItem.class, CostBudgetItemVo.class, HashSet.class, ArrayList.class));
        for (CostBudgetItemVo itemVo : results) {
            CostBudgetVo costBudgetVo = costBudgetVoMap.get(itemVo.getCostBudgetCode());
            if (costBudgetVo == null) {
                continue;
            }
            itemVo.setChannelCode(costBudgetVo.getChannelCode());
            itemVo.setChannelName(costBudgetVo.getChannelName());
            itemVo.setCode(costBudgetVo.getCode());
            itemVo.setCustomerCode(costBudgetVo.getCustomerCode());
            itemVo.setCustomerName(costBudgetVo.getCustomerName());
            itemVo.setDelFlag(costBudgetVo.getDelFlag());
            itemVo.setEnableStatus(costBudgetVo.getEnableStatus());
            itemVo.setInitialAmount(costBudgetVo.getInitialAmount());
            itemVo.setMonth(costBudgetVo.getMonth());
            itemVo.setOrgCode(costBudgetVo.getOrgCode());
            itemVo.setOrgName(costBudgetVo.getOrgName());
            itemVo.setProductCode(costBudgetVo.getProductCode());
            itemVo.setProductLevelCode(costBudgetVo.getProductLevelCode());
            itemVo.setProductLevelName(costBudgetVo.getProductLevelName());
            itemVo.setProductName(costBudgetVo.getProductName());
            itemVo.setQuarter(costBudgetVo.getQuarter());
            itemVo.setTerminalCode(costBudgetVo.getTerminalCode());
            itemVo.setTerminalName(costBudgetVo.getTerminalName());
            itemVo.setType(costBudgetVo.getType());
            itemVo.setYear(costBudgetVo.getYear());
            itemVo.setBudgetSubjectCode(costBudgetVo.getBudgetSubjectCode());
            itemVo.setBudgetSubjectName(costBudgetVo.getBudgetSubjectName());
            itemVo.setRemark(costBudgetVo.getRemark());
        }
        return results;
    }
}
