package com.biz.crm.tpm.business.budget.local.repository;



import com.biz.crm.tpm.business.budget.local.entity.CostBudgetAnalysis;
import com.biz.crm.tpm.business.budget.local.mapper.CostBudgetAnalysisMapper;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;


/**
 * 分析预算(CostBudgetAnalysis)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-27 14:38:52
 */
@Component
public class CostBudgetAnalysisRepository extends ServiceImpl<CostBudgetAnalysisMapper, CostBudgetAnalysis> {

  @Autowired
  private CostBudgetAnalysisMapper costBudgetAnalysisMapper;
  
}

