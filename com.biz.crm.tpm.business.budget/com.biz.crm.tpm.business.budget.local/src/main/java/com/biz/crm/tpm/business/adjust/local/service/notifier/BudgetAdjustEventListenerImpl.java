package com.biz.crm.tpm.business.adjust.local.service.notifier;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustEventDto;
import com.biz.crm.tpm.business.adjust.sdk.event.BudgetAdjustEventListener;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 预算调整事件服务实现
 * <AUTHOR>
 * @Date 2024/7/31 19:19
 */
@Component
public class BudgetAdjustEventListenerImpl implements BudgetAdjustEventListener {

    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public void onCreate(BudgetAdjustEventDto dto) {
        List<BudgetAdjustVo> newList = this.getNewList(dto);
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            crmBusinessLogDto.setOldObject(null);
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    @Override
    public void onUpdate(BudgetAdjustEventDto dto) {
        BudgetAdjustVo original = dto.getOriginal();
        BudgetAdjustVo newest = dto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    @Override
    public void onDelete(BudgetAdjustEventDto dto) {
        List<BudgetAdjustVo> newList = this.getNewList(dto);
        List<BudgetAdjustVo> oldList = (List<BudgetAdjustVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, BudgetAdjustVo.class, BudgetAdjustVo.class, HashSet.class, ArrayList.class);
        Map<String, BudgetAdjustVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(BudgetAdjustVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            BudgetAdjustVo original = oldMap.getOrDefault(newest.getId(), new BudgetAdjustVo());
            original.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setDelFlag(DelFlagStatusEnum.DELETE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });

    }

    @Override
    public void onEnable(BudgetAdjustEventDto dto) {
        List<BudgetAdjustVo> newList = this.getNewList(dto);
        List<BudgetAdjustVo> oldList = (List<BudgetAdjustVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, BudgetAdjustVo.class, BudgetAdjustVo.class, HashSet.class, ArrayList.class);
        Map<String, BudgetAdjustVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(BudgetAdjustVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.ENABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            BudgetAdjustVo original = oldMap.getOrDefault(newest.getId(), new BudgetAdjustVo());
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    @Override
    public void onDisable(BudgetAdjustEventDto dto) {
        List<BudgetAdjustVo> newList = this.getNewList(dto);
        List<BudgetAdjustVo> oldList = (List<BudgetAdjustVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, BudgetAdjustVo.class, BudgetAdjustVo.class, HashSet.class, ArrayList.class);
        Map<String, BudgetAdjustVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(BudgetAdjustVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DISABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            BudgetAdjustVo original = oldMap.getOrDefault(newest.getId(), new BudgetAdjustVo());
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    private List<BudgetAdjustVo> getNewList(BudgetAdjustEventDto dto) {
        List<BudgetAdjustVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        return newList;
    }
}
