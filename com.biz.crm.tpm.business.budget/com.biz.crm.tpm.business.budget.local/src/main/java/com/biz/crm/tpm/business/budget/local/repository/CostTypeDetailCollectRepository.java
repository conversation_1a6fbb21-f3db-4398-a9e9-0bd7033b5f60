package com.biz.crm.tpm.business.budget.local.repository;


import com.biz.crm.tpm.business.budget.local.entity.CostTypeDetailCollect;
import com.biz.crm.tpm.business.budget.local.mapper.CostTypeDetailCollectMapper;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;


/**
 * 采集信息(CostTypeDetailCollect)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-31 14:43:31
 */
@Component
public class CostTypeDetailCollectRepository extends ServiceImpl<CostTypeDetailCollectMapper, CostTypeDetailCollect> {

    @Autowired
    private CostTypeDetailCollectMapper costTypeDetailCollectMapper;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 按编码删除
     *
     * @param code
     */
    public void deleteByCode(String code) {
        lambdaUpdate().eq(CostTypeDetailCollect::getDetailCode, code).remove();
    }

    /**
     * 按编码删除
     *
     * @param codes
     */
    public void deleteByCodes(List<String> codes) {
        lambdaUpdate().in(CostTypeDetailCollect::getDetailCode, codes).remove();
    }

    /**
     * 按编码查询
     *
     * @param code
     */
    public List<CostTypeDetailCollectVo> findByCode(String code) {
        List<CostTypeDetailCollect> list = lambdaQuery().eq(CostTypeDetailCollect::getDetailCode, code).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, CostTypeDetailCollect.class, CostTypeDetailCollectVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }


    public List<CostTypeDetailCollectVo> findListByCodes(List<String> codes){
        List<CostTypeDetailCollect> list = lambdaQuery().in(CostTypeDetailCollect::getDetailCode, codes).list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, CostTypeDetailCollect.class, CostTypeDetailCollectVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }
}

