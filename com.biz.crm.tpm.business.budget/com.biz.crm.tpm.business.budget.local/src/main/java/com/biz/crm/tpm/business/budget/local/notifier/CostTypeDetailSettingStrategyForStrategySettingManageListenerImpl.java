package com.biz.crm.tpm.business.budget.local.notifier;

import com.biz.crm.tpm.business.budget.local.repository.CostTypeDetailSettingStrategyRepository;
import com.biz.crm.tpm.business.budget.sdk.event.StrategySettingManageEventListener;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CostTypeDetailSettingStrategyForStrategySettingManageListenerImpl implements StrategySettingManageEventListener {

  @Autowired
  private CostTypeDetailSettingStrategyRepository costTypeDetailSettingStrategyRepository;

  @Override
  public void onUpdate(String settingManageCode) {
    Validate.notBlank(settingManageCode,"策略配置编码不能为空");
    boolean exist = costTypeDetailSettingStrategyRepository.existBySettingManageCode(settingManageCode);
    Validate.isTrue(!exist,"策略配置【%s】已有活动细类正在使用，不能进行编辑",settingManageCode);
  }
}
