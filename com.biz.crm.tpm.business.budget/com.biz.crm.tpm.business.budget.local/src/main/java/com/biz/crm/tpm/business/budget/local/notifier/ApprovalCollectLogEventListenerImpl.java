package com.biz.crm.tpm.business.budget.local.notifier;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectDto;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.event.log.ApprovalCollectLogEventListener;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年06月23日 15:47:00
 */
@Component
public class ApprovalCollectLogEventListenerImpl implements ApprovalCollectLogEventListener {
  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;

  @Override
  public void onCreate(ApprovalCollectLogEventDto eventDto) {
    ApprovalCollectDto newest = eventDto.getNewest();
    ApprovalCollectDto original = eventDto.getOriginal();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onDelete(ApprovalCollectLogEventDto eventDto) {
    ApprovalCollectDto newest = eventDto.getNewest();
    ApprovalCollectDto original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onUpdate(ApprovalCollectLogEventDto eventDto) {
    ApprovalCollectDto newest = eventDto.getNewest();
    ApprovalCollectDto original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onUpdateEnable(ApprovalCollectLogEventDto eventDto) {
    ApprovalCollectDto newest = eventDto.getNewest();
    ApprovalCollectDto original = eventDto.getOriginal();
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    //只传单个字段
    JSONObject oldObject = new JSONObject();
    JSONObject newObject = new JSONObject();
    oldObject.put("enableStatus", original.getEnableStatus());
    newObject.put("enableStatus", newest.getEnableStatus());
    crmBusinessLogDto.setOldObject(oldObject);
    crmBusinessLogDto.setNewObject(newObject);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

}
