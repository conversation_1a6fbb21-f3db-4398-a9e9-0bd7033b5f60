<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.budget.local.mapper.ApprovalCollectMapper">
    <resultMap id="approvalCollectVoMap" type="com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo"/>


    <select id="findByConditions" resultMap="approvalCollectVoMap">
        select
          distinct t.*
        from tpm_approval_collect t
        inner join tpm_approval_collect_type tt on tt.collect_code=t.code
        where t.tenant_code=#{dto.tenantCode}
        <if test="dto.code !=null and dto.code != '' ">
            and t.code = #{dto.code}
        </if>
        <if test="dto.name !=null and dto.name != '' ">
            and t.name like concat('%',#{dto.name},'%')
        </if>
        <if test="dto.enableStatus !=null and dto.enableStatus != '' ">
            and t.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.delFlag !=null and dto.delFlag != '' ">
            and t.del_flag = #{dto.delFlag}
        </if>
        <if test="dto.typeCodeList != null and dto.typeCodeList.size > 0">
            and tt.type_code in
            <foreach item="item" collection="dto.typeCodeList" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        order by t.create_time desc
    </select>

</mapper>

