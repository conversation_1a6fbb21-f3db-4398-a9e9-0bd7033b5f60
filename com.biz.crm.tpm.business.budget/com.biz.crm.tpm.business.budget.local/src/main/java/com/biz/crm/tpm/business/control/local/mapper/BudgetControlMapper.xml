<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.control.local.mapper.BudgetControlMapper">


    <select id="matchBudgetControl" resultType="com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo">
        SELECT
        a.control_code,
        a.control_form
        FROM
        tpm_budget_control a
        LEFT JOIN tpm_budget_control_range b ON a.control_code = b.control_code
        LEFT JOIN tpm_budget_control_subject c ON a.control_code = c.control_code
        WHERE
        a.del_flag = '${@<EMAIL>()}'
        AND a.enable_status = '${@<EMAIL>()}'
        AND a.tenant_code = #{tenantCode}
        <![CDATA[AND a.start_year_month <= #{years}]]>
        <![CDATA[AND a.end_year_month >= #{years}]]>
        and(a.department_code in
        <foreach collection="orgCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        or(b.department_code in
        <foreach collection="orgCodes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="customerCode != null and customerCode != ''">
            and b.customer_code = #{customerCode}
        </if>
        and (
        c.budget_subjects_code in
        <foreach collection="budgetSubjectCodeSet" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
        or c.budget_subjects_code is null
        )
        ))
        GROUP BY
        a.control_code,
        a.control_form
    </select>

</mapper>

