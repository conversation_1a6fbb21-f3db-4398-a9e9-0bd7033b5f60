package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.budget.local.entity.BudgetSubjects;
import com.biz.crm.tpm.business.budget.local.mapper.BudgetSubjectsMapper;
import com.biz.crm.tpm.business.budget.sdk.dto.BudgetSubjectsDto;
import com.biz.crm.tpm.business.budget.sdk.enums.BudgetSubjectLevelEnum;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * TPM-预算科目;(tpm_budget_subjects)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-18
 */
@Component
public class BudgetSubjectsRepository extends ServiceImpl<BudgetSubjectsMapper, BudgetSubjects> {
    @Autowired
    private BudgetSubjectsMapper budgetSubjectsMapper;

    /**
     * 批量根据id禁用
     *
     * @param enable
     * @param ids
     */
    public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
        UpdateWrapper<BudgetSubjects> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("enable_status", enable.getCode());
        updateWrapper.in("id", ids);
        updateWrapper.eq("tenant_code", TenantUtils.getTenantCode());
        this.update(updateWrapper);
    }

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    public Page<BudgetSubjectsVo> findByConditions(Pageable pageable, BudgetSubjectsDto dto) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 50);
        }
        String tenantCode = TenantUtils.getTenantCode();
        dto.setTenantCode(tenantCode);
        Page<BudgetSubjectsVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return budgetSubjectsMapper.findByConditions(page, dto);
    }

    /**
     * 根据id集合获取详情集合
     *
     * @param ids ID集合
     * @return List<BudgetSubjects>
     */
    public List<BudgetSubjects> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(BudgetSubjects::getId, ids)
                .eq(BudgetSubjects::getTenantCode, tenantCode)
                .eq(BudgetSubjects::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据编号与租户编号获取对象
     *
     * @param code
     * @return
     */
    public BudgetSubjects findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .eq(BudgetSubjects::getBudgetSubjectsCode, code)
                .eq(BudgetSubjects::getTenantCode, tenantCode)
                .eq(BudgetSubjects::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
    }

    /**
     * 根据启用状态与租户编号查询对象
     *
     * @param enableStatus
     * @return
     */
    public List<BudgetSubjects> findByEnableStatus(String enableStatus) {
        String tenantCode = TenantUtils.getTenantCode();
        if (StringUtils.isBlank(enableStatus)) {
            return this.lambdaQuery()
                    .eq(BudgetSubjects::getTenantCode, tenantCode)
                    .eq(BudgetSubjects::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
        }

        return this.lambdaQuery()
                .eq(BudgetSubjects::getEnableStatus, enableStatus)
                .eq(BudgetSubjects::getTenantCode, tenantCode)
                .eq(BudgetSubjects::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        String tenantCode = TenantUtils.getTenantCode();
        UpdateWrapper<BudgetSubjects> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("del_flag", DelFlagStatusEnum.DELETE.getCode());
        updateWrapper.eq("tenant_code", tenantCode);
        updateWrapper.in("id", idList);
        return this.update(updateWrapper);
    }

    public List<BudgetSubjects> findByCodesAndTenantCode(Set<String> codes, String tenantCode) {
        return this.lambdaQuery().in(BudgetSubjects::getBudgetSubjectsCode, codes).
                eq(BudgetSubjects::getTenantCode, tenantCode).
                eq(BudgetSubjects::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).
                list();
    }

    /**
     * 通过id和租户编号查询
     *
     * @param id
     * @param tenantCode
     * @return
     */
    public BudgetSubjects findByIdAndTenantCode(String id, String tenantCode) {
        return this.lambdaQuery()
                .eq(BudgetSubjects::getTenantCode, TenantUtils.getTenantCode())
                .in(BudgetSubjects::getId, id)
                .one();
    }

    public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
        this.lambdaUpdate()
                .eq(BudgetSubjects::getTenantCode, tenantCode)
                .in(BudgetSubjects::getId, ids)
                .remove();
    }

    public List<BudgetSubjects> findListByBudgetSubjectNames(List<String> budgetSubjectNameList, String budgetSubjectLevel) {
        return this.lambdaQuery()
                .in(BudgetSubjects::getBudgetSubjectsName, budgetSubjectNameList)
                .eq(BudgetSubjects::getLevel, budgetSubjectLevel)
                .eq(BudgetSubjects::getTenantCode, TenantUtils.getTenantCode())
                .eq(BudgetSubjects::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(BudgetSubjects::getEnableStatus, EnableStatusEnum.ENABLE.getCode()).list();
    }


    public List<BudgetSubjects> findSecondBudgetSubject() {
        return this.lambdaQuery()
                .eq(BudgetSubjects::getLevel, BudgetSubjectLevelEnum.second_level.getCode())
                .eq(BudgetSubjects::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(BudgetSubjects::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(BudgetSubjects::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    public Set<String> findAllParentSubjectCodesByCodes(String budgetSubjectCode) {
        return this.baseMapper.findAllParentSubjectCodesByCodes(budgetSubjectCode);
    }

    public List<String> findHasChildSubjectCodeList(List<String> budgetSubjectCodes) {
        if (CollectionUtils.isEmpty(budgetSubjectCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findHasChildSubjectCodeList(budgetSubjectCodes);
    }

    public String findBudgetSubjectByDetailCode(String detailCode) {
        return this.baseMapper.findBudgetSubjectByDetailCode(detailCode);
    }

    public List<BudgetSubjects> findByCodesOrNames(List<String> codes, List<String> names) {
        if (CollectionUtils.isEmpty(codes) && CollectionUtils.isEmpty(names)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(BudgetSubjects::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(!CollectionUtils.isEmpty(codes), BudgetSubjects::getBudgetSubjectsCode, codes)
                .in(!CollectionUtils.isEmpty(names), BudgetSubjects::getBudgetSubjectsName, names)
                .list();
    }

    public List<BudgetSubjects> findChildrensByCodes(List<String> budgetSubjectsCodes) {
        if (CollectionUtils.isEmpty(budgetSubjectsCodes)) {
            return Lists.newArrayList();
        }
        budgetSubjectsCodes = budgetSubjectsCodes.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(budgetSubjectsCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(BudgetSubjects::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(BudgetSubjects::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .in(BudgetSubjects::getParentBudgetSubjectsCode, budgetSubjectsCodes)
                .list();
    }

    public List<BudgetSubjects> findByCodeList(List<String> subjectCodes) {
        return budgetSubjectsMapper.findByCodeList(subjectCodes);
    }
}