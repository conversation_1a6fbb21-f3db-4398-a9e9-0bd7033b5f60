package com.biz.crm.tpm.business.budget.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.local.entity.ApprovalCollect;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectDto;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;
import org.apache.ibatis.annotations.Param;

/**
 * 核销采集信息(ApprovalCollect)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
public interface ApprovalCollectMapper extends BaseMapper<ApprovalCollect> {

  /**
   * 分页查询所有数据
   *
   * @param page            分页对象
   * @param approvalCollect 查询实体
   * @return 所有数据
   */
  Page<ApprovalCollectVo> findByConditions(@Param("page") Page<ApprovalCollectVo> page, @Param("dto") ApprovalCollectDto approvalCollect);

}

