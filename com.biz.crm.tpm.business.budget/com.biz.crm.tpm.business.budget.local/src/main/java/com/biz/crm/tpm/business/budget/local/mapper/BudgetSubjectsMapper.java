package com.biz.crm.tpm.business.budget.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.local.entity.BudgetSubjects;
import com.biz.crm.tpm.business.budget.sdk.dto.BudgetSubjectsDto;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * TPM-预算科目;(tpm_budget_subjects)表数据库访问层
 * <AUTHOR> Keller
 * @date : 2022-5-18
 */
@Mapper
public interface BudgetSubjectsMapper extends BaseMapper<BudgetSubjects>{
    /** 
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto 动态查询条件
     * @return 分页对象列表
     */
    Page<BudgetSubjectsVo> findByConditions(@Param("page") Page<BudgetSubjectsVo> page , @Param("dto") BudgetSubjectsDto dto);

    List<String> findHasChildSubjectCodeList(@Param("budgetSubjectCodes") List<String> budgetSubjectCodes);

    Set<String> findAllParentSubjectCodesByCodes(@Param("budgetSubjectCode")String budgetSubjectCode);

    String findBudgetSubjectByDetailCode(@Param("detailCode") String detailCode);

    List<BudgetSubjects> findByCodeList(@Param("subjectCodes") List<String> subjectCodes);
}