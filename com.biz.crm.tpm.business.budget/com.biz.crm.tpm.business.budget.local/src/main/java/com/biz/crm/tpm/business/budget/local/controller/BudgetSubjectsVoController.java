package com.biz.crm.tpm.business.budget.local.controller;

import com.biz.crm.tpm.business.budget.sdk.strategy.BudgetControlTypeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.budget.sdk.dto.BudgetSubjectsDto;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.business.common.sdk.model.Result;

import java.util.List;

/**
 * TPM-预算科目;(tpm_budget_subjects)Vo控制层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-18
 */
@Api(tags = "TPM-预算科目Vo功能接口")
@RestController
@RequestMapping("/v1/budget/budgetSubjects")
@Slf4j
public class BudgetSubjectsVoController {
  /**
   * 服务对象
   */
  @Autowired
  private BudgetSubjectsVoService budgetSubjectsVoService;
  @Autowired(required = false)
  private List<BudgetControlTypeStrategy> budgetControlTypeStrategics;

  /**
   * 由于创建或修改导致的重复提交的问题。在创建和修改前，需要使用该方法获得预授权</br>
   * 预授权成功后，才能通过预授权信息进行添加，
   *
   * @return
   */
  @ApiOperation(value = "由于创建授权标签来避免重复提交的问题。所以在创建前，需要使用该方法获得预授权")
  @PostMapping(value = "/preSave")
  public Result<?> preSave() {
    try {
      Object prefix = this.budgetSubjectsVoService.preSave();
      return Result.ok(prefix);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页查询所有数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<BudgetSubjectsVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                         @ApiParam(name = "budgetSubjects", value = "核销采集信息") BudgetSubjectsDto dto) {
    try {
      Page<BudgetSubjectsVo> page = this.budgetSubjectsVoService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<BudgetSubjectsVo> findById(@RequestParam("id") @ApiParam(name = "id", value = "主键id") String id) {
    try {
      BudgetSubjectsVo budgetSubjectsVo = this.budgetSubjectsVoService.findById(id);
      return Result.ok(budgetSubjectsVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("findByCode")
  public Result<BudgetSubjectsVo> findByCode(@RequestParam("code") @ApiParam(name = "code", value = "编号") String code) {
    try {
      BudgetSubjectsVo budgetSubjectsVo = this.budgetSubjectsVoService.findByCode(code);
      return Result.ok(budgetSubjectsVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param budgetSubjectsVo 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<BudgetSubjectsVo> create(@ApiParam(name = "budgetSubjectsVo", value = "TPM-预算科目") @RequestBody BudgetSubjectsVo budgetSubjectsVo) {
    try {
      BudgetSubjectsVo result = this.budgetSubjectsVoService.create(budgetSubjectsVo);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   *
   * @param budgetSubjectsVo 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<BudgetSubjectsVo> update(@ApiParam(name = "budgetSubjectsVo", value = "TPM-预算科目") @RequestBody BudgetSubjectsVo budgetSubjectsVo) {
    try {
      BudgetSubjectsVo result = this.budgetSubjectsVoService.update(budgetSubjectsVo);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation(value = "删除数据")
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    try {
      this.budgetSubjectsVoService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过状态编号查询单条数据
   *
   * @param enableStatus 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过状态编号查询单条数据")
  @GetMapping("findByEnableStatus")
  public Result<List<BudgetSubjectsVo>> findByEnableStatus(@PathVariable @ApiParam(name = "enableStatus", value = "状态编号") String enableStatus) {
    try {
      List<BudgetSubjectsVo> budgetSubjectsVos = this.budgetSubjectsVoService.findByEnableStatus(enableStatus);
      return Result.ok(budgetSubjectsVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量根据id启用
   */
  @ApiOperation(value = "批量根据id启用")
  @PatchMapping(value = "enable")
  public Result<?> enable(@RequestBody List<String> ids) {
    try {
      this.budgetSubjectsVoService.enable(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量根据id禁用
   */
  @ApiOperation(value = "批量根据id禁用")
  @PatchMapping(value = "disable")
  public Result<?> disable(@RequestBody List<String> ids) {
    try {
      this.budgetSubjectsVoService.disable(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 获取预算科目控制类型
   */
  @ApiOperation(value = "获取预算科目控制类型")
  @GetMapping(value = "controlType")
  public Result<?> controlType() {
    try {
      return Result.ok(budgetControlTypeStrategics);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}