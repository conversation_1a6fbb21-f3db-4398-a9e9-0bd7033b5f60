<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.budget.local.mapper.CostBudgetItemMapper">
    <resultMap id="costBudgetItemVoMap" type="com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo"/>

    <select id="findByConditions" resultMap="costBudgetItemVoMap">
        select
          t.*
        from tpm_cost_budget_item t
        where t.tenant_code=#{dto.tenantCode}
        <if test="dto.businessCode !=null and dto.businessCode != '' ">
            and t.business_code = #{dto.businessCode}
        </if>
        <if test="dto.businessItemCode !=null and dto.businessItemCode != '' ">
            and t.business_item_code = #{dto.businessItemCode}
        </if>
        <if test="dto.operateType !=null and dto.operateType != '' ">
            and t.operate_type = #{dto.operateType}
        </if>
        <if test="dto.costBudgetCode !=null and dto.costBudgetCode != '' ">
            and t.cost_budget_code = #{dto.costBudgetCode}
        </if>
        <if test="dto.source !=null and dto.source != '' ">
            and t.source = #{dto.source}
        </if>
        order by t.create_time desc
    </select>

    <select id="findByCostBudgetConditions" resultMap="costBudgetItemVoMap">
        select
        ti.*
        from tpm_cost_budget_item ti
        inner join tpm_cost_budget t on t.code=ti.cost_budget_code
        where t.tenant_code=#{dto.tenantCode}
        <if test="dto.yearMonthLy !=null and dto.yearMonthLy != '' ">
            and t.year_month_ly = #{dto.yearMonthLy}
        </if>
        <if test="dto.budgetType !=null and dto.budgetType != '' ">
            and t.budget_type = #{dto.budgetType}
        </if>
        <if test="dto.divisionCode !=null and dto.divisionCode != '' ">
            and t.division_code = #{dto.divisionCode}
        </if>
        <if test="dto.centerCode !=null and dto.centerCode != '' ">
            and t.center_code = #{dto.centerCode}
        </if>
        <if test="dto.departmentOneCode !=null and dto.departmentOneCode != '' ">
            and t.department_one_code = #{dto.departmentOneCode}
        </if>
        <if test="dto.companyCode !=null and dto.companyCode != '' ">
            and t.company_code = #{dto.companyCode}
        </if>
        <if test="dto.costCenterCode !=null and dto.costCenterCode != '' ">
            and t.cost_center_code = #{dto.costCenterCode}
        </if>
        <if test="dto.customerName !=null and dto.customerName != '' ">
            and t.customer_name like concat('%',#{dto.customerName},'%')
        </if>
        <if test="dto.budgetSubjectCode !=null and dto.budgetSubjectCode != '' ">
            and t.budget_subject_code = #{dto.budgetSubjectCode}
        </if>
        <if test="dto.budgetSubjectName !=null and dto.budgetSubjectName != '' ">
            and t.budget_subject_name like concat('%',#{dto.budgetSubjectName},'%')
        </if>
    </select>

    <select id="findMarketingPlanCaseList" resultType="com.biz.crm.tpm.business.budget.local.entity.CostBudgetItem">
        SELECT
        CASE d.original_scheme_detail_code IS NOT NULL
        AND d.original_scheme_detail_code = b.scheme_detail_code
        WHEN d.process_status = '2' AND <![CDATA[d.apply_amount < b.apply_amount]]> THEN b.apply_amount
        WHEN d.process_status = '2' AND <![CDATA[d.apply_amount > b.apply_amount]]> THEN d.apply_amount
        WHEN d.process_status = '3' THEN
        d.apply_amount
        ELSE
        b.apply_amount
        END operate_amount,
        c.budget_code cost_budget_code,
        b.scheme_detail_code business_code,
        b.budget_subject_name,
        b.customer_name,
        b.create_name modify_name,
        b.modify_time,
        "营销方案" source,
        'use' operateType,
        a.scheme_type,
        a.process_status,
        b.original_scheme_detail_code
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        LEFT JOIN tpm_marketing_plan_case_extend c ON a.scheme_code = c.scheme_code
        AND b.scheme_detail_code = c.scheme_detail_code
        LEFT JOIN (
        SELECT
        b.apply_amount,
        c.budget_code,
        b.original_scheme_detail_code,
        a.process_status
        FROM
        tpm_marketing_plan a
        LEFT JOIN tpm_marketing_plan_case b ON a.scheme_code = b.scheme_code
        LEFT JOIN tpm_marketing_plan_case_extend c ON a.scheme_code = c.scheme_code
        AND b.scheme_detail_code = c.scheme_detail_code
        WHERE
        a.tenant_code = 'default'
        AND a.del_flag = '009'
        AND b.del_flag = '009'
        AND a.process_status in('2', '3')
        AND c.budget_code IS NOT NULL
        AND c.budget_code != ''
        AND a.scheme_type = 'change'
        AND b.scheme_detail_code = (
        SELECT MAX(sub_b.scheme_detail_code)
        FROM tpm_marketing_plan sub_a
        INNER JOIN tpm_marketing_plan_case sub_b ON sub_a.scheme_code = sub_b.scheme_code
        WHERE sub_b.original_scheme_detail_code = b.original_scheme_detail_code
        )
        ) d ON b.scheme_detail_code = d.original_scheme_detail_code
        WHERE
        a.tenant_code = 'default'
        AND a.del_flag = '009'
        AND b.del_flag = '009'
        AND a.process_status in('2', '3')
        AND c.budget_code = #{costBudgetCode}
    </select>

    <select id="findWithholdingList" resultType="com.biz.crm.tpm.business.budget.local.entity.CostBudgetItem">
        SELECT ifnull(wh.budget_code, c.budget_code)                                        cost_budget_code,
               ifnull(wh.activities_detail_code, wh.with_holding_code)                      business_code,
               SUM(IFNULL(wh.with_holding_amount, 0)) - SUM(IFNULL(wo.write_off_amount, 0)) operate_amount,
               wh.budget_subjects_name,
               wh.customer_name,
               wo.create_name                                                               modify_name,
               wh.modify_time,
               '计提'                                                                       source,
               'use'                                                                        operateType
        FROM tpm_with_holding wh
                 LEFT JOIN tpm_with_holding_write_off wo ON wh.with_holding_code = wo.with_holding_code
                 LEFT JOIN tpm_marketing_plan_case_extend c ON wh.activities_detail_code = c.scheme_detail_code
        WHERE wh.del_flag = '009'
          AND wh.status = '3'
          AND (wh.budget_code = #{costBudgetCode}
            OR c.budget_code = #{costBudgetCode})
        GROUP BY wh.budget_code,
                 wh.activities_detail_code,
                 wh.with_holding_code,
                 wh.budget_subjects_name,
                 wh.customer_name,
                 wo.create_name,
                 wh.modify_time
    </select>

    <select id="findEndCaseList" resultType="com.biz.crm.tpm.business.budget.local.entity.CostBudgetItem">
        SELECT c.budget_code        cost_budget_code,
               a.scheme_detail_code business_code,
               sum(a.audit_amount)  operate_amount,
               b.budget_subject_name,
               b.customer_name,
               a.create_name        modify_name,
               a.modify_time,
               '结案'               source,
               'use'                operateType
        FROM tpm_marketing_audit_detail a
                 LEFT JOIN tpm_marketing_plan_case b ON a.scheme_detail_code = b.scheme_detail_code
                 LEFT JOIN tpm_marketing_plan_case_extend c ON b.scheme_detail_code = c.scheme_detail_code
                 LEFT JOIN tpm_marketing_audit d ON a.audit_code = d.audit_code
        WHERE a.be_full_audit = 'Y'
          AND d.status = '3'
          AND c.budget_code = #{costBudgetCode}
        GROUP BY c.budget_code,
                 a.scheme_detail_code,
                 b.budget_subject_name,
                 b.customer_name,
                 a.create_name,
                 a.modify_time
    </select>
</mapper>

