package com.biz.crm.tpm.business.track.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.elasticsearch.common.collect.HppcMaps;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_budget_track")
@Table(name = "tpm_budget_track", indexes = {
        @Index(name = "tpm_budget_track_idx1", columnList = "budget_control_code"),
        @Index(name = "tpm_budget_track_idx2", columnList = "org_code"),
        @Index(name = "tpm_budget_track_idx3", columnList = "customer_code"),
        @Index(name = "tpm_budget_track_idx3", columnList = "product_code"),
})
@ApiModel(value = "BudgetTrack", description = "预算追踪")
@org.hibernate.annotations.Table(appliesTo = "tpm_budget_track", comment = "预算追踪")
public class BudgetTrack extends TenantFlagOpEntity {

  @ApiModelProperty("预算追踪编码")
  @Column(name = "track_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算追踪编码'")
  private String trackCode;

  @ApiModelProperty("预算管控编码")
  @Column(name = "budget_control_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算管控编码'")
  private String budgetControlCode;

  @ApiModelProperty("预算科目编码")
  @TableField(value = "budget_subject_code")
  @Column(name = "budget_subject_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算科目编码'")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  @TableField(value = "budget_subject_name")
  @Column(name = "budget_subject_name", columnDefinition = "varchar(255) COMMENT '预算科目名称'")
  private String budgetSubjectName;

  @ApiModelProperty("组织编码")
  @TableField(value = "org_code")
  @Column(name = "org_code", length = 64, columnDefinition = "varchar(64) COMMENT '组织编码'")
  private String orgCode;

  @ApiModelProperty("组织名称")
  @TableField(value = "org_name")
  @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
  private String orgName;

  @ApiModelProperty("客户编码")
  @TableField(value = "customer_code")
  @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
  private String customerCode;

  @ApiModelProperty("客户名称")
  @TableField(value = "customer_name")
  @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
  private String customerName;

  @ApiModelProperty("产品编码")
  @TableField(value = "product_code")
  @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
  private String productCode;

  @ApiModelProperty("产品名称")
  @TableField(value = "product_name")
  @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
  private String productName;

  @ApiModelProperty("期初金额")
  @TableField(value = "initial_amount")
  @Column(name = "initial_amount", columnDefinition = "decimal(20,4) COMMENT '期初金额'")
  private BigDecimal initialAmount;

  @ApiModelProperty("当月金额")
  @Column(name = "this_initial_amount", columnDefinition = "decimal(20,4) COMMENT '当月金额'")
  private BigDecimal thisInitialAmount;

  @ApiModelProperty("年月")
  @Column(name = "year_month_ly", length = 32, columnDefinition = "VARCHAR(32) COMMENT '年月'")
  private String yearMonthLy;

  @ApiModelProperty("事业部编码")
  @Column(name = "division_code", columnDefinition = "VARCHAR(32) COMMENT '事业部编码'")
  private String divisionCode;

  @ApiModelProperty("事业部名称")
  @Column(name = "division_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '事业部名称'")
  private String divisionName;

  @ApiModelProperty("中心编码")
  @Column(name = "center_code", columnDefinition = "VARCHAR(32) COMMENT '中心编码'")
  private String centerCode;

  @ApiModelProperty("中心名称")
  @Column(name = "center_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '中心名称'")
  private String centerName;

  @ApiModelProperty("一级部门编码")
  @Column(name = "department_one_code", columnDefinition = "VARCHAR(32) COMMENT '一级部门编码'")
  private String departmentOneCode;

  @ApiModelProperty("一级部门名称")
  @Column(name = "department_one_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '一级部门名称'")
  private String departmentOneName;

  @ApiModelProperty("二级部门编码")
  @Column(name = "department_two_code", columnDefinition = "VARCHAR(32) COMMENT '二级部门编码'")
  private String departmentTwoCode;

  @ApiModelProperty("二级部门名称")
  @Column(name = "department_two_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '二级部门名称'")
  private String departmentTwoName;

  @ApiModelProperty("三级部门编码")
  @Column(name = "department_three_code", columnDefinition = "VARCHAR(32) COMMENT '三级部门编码'")
  private String departmentThreeCode;

  @ApiModelProperty("三级部门名称")
  @Column(name = "department_three_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '三级部门名称'")
  private String departmentThreeName;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "VARCHAR(32) COMMENT '公司编码'")
  private String companyCode;

  @ApiModelProperty("公司名称")
  @Column(name = "company_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '公司名称'")
  private String companyName;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "VARCHAR(32) COMMENT '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '成本中心名称'")
  private String costCenterName;

  @ApiModelProperty("调整金额")
  @Column(name = "adjust_amount", columnDefinition = "decimal(20,6) COMMENT '调整金额'")
  private BigDecimal adjustAmount;

  @ApiModelProperty("调整后余额")
  @Column(name = "adjust_balance_amount", columnDefinition = "decimal(20,6) COMMENT '调整后余额'")
  private BigDecimal adjustBalanceAmount;

  @ApiModelProperty("月度余额")
  @Column(name = "month_balance_amount", columnDefinition = "decimal(20,6) COMMENT '月度余额'")
  private BigDecimal monthBalanceAmount;

  @ApiModelProperty("冻结金额")
  @Column(name = "freeze_amount", columnDefinition = "decimal(20,6) COMMENT '冻结金额'")
  private BigDecimal freezeAmount;

  @ApiModelProperty("已使用金额")
  @Column(name = "used_amount", columnDefinition = "decimal(20,6) COMMENT '已使用金额'")
  private BigDecimal usedAmount;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("关联业务编码")
  @Column(name = "business_code", length = 64, columnDefinition = "varchar(64) COMMENT '关联业务编码'")
  private String businessCode;

  /**
   * 客户ERP编码
   */
  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", length = 32, columnDefinition = "varchar(32) COMMENT '客户ERP编码'")
  private String erpCode;

  /**
   * 产品组编码
   */
  @ApiModelProperty("产品组编码")
  @Column(name = "product_group_code", length = 32, columnDefinition = "varchar(32) COMMENT '产品组编码'")
  private String productGroupCode;

  @ApiModelProperty("预算费率")
  @Column(name = "rate", columnDefinition = "decimal(20,6) COMMENT '预算费率'")
  private BigDecimal rate;

  /**
   * 维度
   */
  @ApiModelProperty("维度")
  @Column(name = "dimension", length = 128, columnDefinition = "varchar(128) COMMENT '维度'")
  private String dimension;

  /**
   * 维度
   */
  @ApiModelProperty("维度值")
  @Column(name = "dimension_value", columnDefinition = "varchar(255) COMMENT '维度值'")
  private String dimensionValue;

  @ApiModelProperty("部门层级")
  @Column(name = "level_num", length = 32, columnDefinition = "varchar(32) COMMENT '部门层级'")
  private String levelNum;

  @ApiModelProperty("当月管报收入")
  @Column(name = "report_amount", columnDefinition = "decimal(20,6) COMMENT '当月管报收入'")
  private BigDecimal reportAmount;

  @ApiModelProperty("当月实际预算费用")
  @Column(name = "actual_amount", length = 20, scale = 6, columnDefinition = "DECIMAL(20,6) COMMENT '当月实际预算费用 '")
  private BigDecimal actualAmount;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("年")
  private String yearLy;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("月")
  private Integer monthInt;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("预算管控类型")
  private String budgetControlType;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("预算管控维度")
  private String budgetTimeDimension;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty("预算编码")
  private List<String> budgetCodes;
}
