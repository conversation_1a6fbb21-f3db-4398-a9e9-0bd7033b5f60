package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;

@ApiModel(value = "StrategySettingManage",description = "TPM-活动细类策略配置")
@TableName("tpm_strategy_setting_manage")
@Getter
@Setter
@Entity(name = "tpm_strategy_setting_manage")
@org.hibernate.annotations.Table(appliesTo = "tpm_strategy_setting_manage", comment = "TPM-活动细类策略配置")
@Deprecated
public class StrategySettingManage extends TenantFlagOpEntity {

   /** 活动细类策略配置名称 */
   @ApiModelProperty(name = "活动细类策略配置名称")
   @TableField(value = "name")
   @Column(name = "name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '活动细类策略配置名称 '")
   private String name;

   /** 活动细类策略配置编码 */
   @ApiModelProperty(name = "活动细类策略配置编码")
   @TableField(value = "code", fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
   @Column(name = "code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动细类策略配置编码 '")
   private String code;

}