package com.biz.crm.tpm.business.budget.local.controller;

import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeCategoryDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import com.biz.crm.tpm.business.budget.sdk.service.CostTypeCategoryVoService;
import com.biz.crm.business.common.sdk.model.Result;

import java.util.List;

/**
 * TPM-活动大类;(tpm_cost_type_category)Vo控制层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
@Api(tags = "TPM-活动大类Vo功能接口")
@RestController
@RequestMapping("/v1/budget/costTypeCategory")
@Slf4j
public class CostTypeCategoryVoController {
  /**
   * 服务对象
   */
  @Autowired
  private CostTypeCategoryVoService costTypeCategoryVoService;

  /**
   * 由于创建或修改导致的重复提交的问题。在创建和修改前，需要使用该方法获得预授权</br></br>
   * 预授权成功后，才能通过预授权信息进行添加，
   *
   * @return
   */
  @ApiOperation(value = "由于创建或修改导致的重复提交的问题。在创建和修改前，需要使用该方法获得预授权</br>")
  @PostMapping(value = "/preSave")
  public Result<?> preSave() {
    try {
      Object prefix = this.costTypeCategoryVoService.preSave();
      return Result.ok(prefix);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页查询所有数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<CostTypeCategoryVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(Integer.MAX_VALUE) Pageable pageable,
                                                           @ApiParam(name = "costTypeCategory", value = "核销采集信息") CostTypeCategoryDto dto) {
    try {
      Page<CostTypeCategoryVo> page = this.costTypeCategoryVoService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<CostTypeCategoryVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id", required = true) String id) {
    try {
      CostTypeCategoryVo costTypeCategoryVo = this.costTypeCategoryVoService.findById(id);
      return Result.ok(costTypeCategoryVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByCode")
  public Result<CostTypeCategoryVo> findByCode(@ApiParam(name = "code", value = "编号", required = true) String code) {
    try {
      CostTypeCategoryVo costTypeCategoryVo = this.costTypeCategoryVoService.findByCode(code);
      return Result.ok(costTypeCategoryVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param costTypeCategoryVo 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<CostTypeCategoryVo> create(@ApiParam(name = "costTypeCategoryVo", value = "TPM-活动大类") @RequestBody CostTypeCategoryVo costTypeCategoryVo) {
    try {
      CostTypeCategoryVo result = this.costTypeCategoryVoService.create(costTypeCategoryVo);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   *
   * @param costTypeCategoryVo 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<CostTypeCategoryVo> update(@ApiParam(name = "costTypeCategoryVo", value = "TPM-活动大类") @RequestBody CostTypeCategoryVo costTypeCategoryVo) {
    try {
      CostTypeCategoryVo result = this.costTypeCategoryVoService.update(costTypeCategoryVo);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation(value = "删除数据")
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
    try {
      this.costTypeCategoryVoService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过状态编号查询单条数据
   *
   * @param enableStatus 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过状态编号查询单条数据")
  @GetMapping("findByEnableStatus")
  public Result<List<CostTypeCategoryVo>> findByEnableStatus(@PathVariable @ApiParam(name = "enableStatus", value = "状态编号") String enableStatus) {
    try {
      List<CostTypeCategoryVo> costTypeCategoryVos = this.costTypeCategoryVoService.findByEnableStatus(enableStatus);
      return Result.ok(costTypeCategoryVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量根据id启用
   */
  @ApiOperation(value = "批量根据id启用")
  @PatchMapping(value = "enable")
  public Result<?> enable(@RequestBody List<String> ids) {
    try {
      this.costTypeCategoryVoService.enable(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量根据id禁用
   */
  @ApiOperation(value = "批量根据id禁用")
  @PatchMapping(value = "disable")
  public Result<?> disable(@RequestBody List<String> ids) {
    try {
      this.costTypeCategoryVoService.disable(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过预算科目编号查询多条数据
   *
   * @param budgetSubjectsCode 预算科目编号
   * @return 多条数据
   */
  @ApiOperation(value = "通过预算科目编号查询多条数据")
  @GetMapping("findByBudgetSubjectsCode")
  public Result<List<CostTypeCategoryVo>> findByBudgetSubjectsCode(@ApiParam(name = "budgetSubjectsCode", value = "预算科目编号", required = true) String budgetSubjectsCode) {
    try {
      List<CostTypeCategoryVo> costTypeCategoryVos = this.costTypeCategoryVoService.findByBudgetSubjectsCode(budgetSubjectsCode);
      return Result.ok(costTypeCategoryVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过多条预算科目编号查询多条数据
   *
   * @param budgetSubjectsCodes 多条预算科目编号
   * @return 多条数据
   */
  @ApiOperation(value = "通过预算科目编号查询多条数据")
  @GetMapping("findByBudgetSubjectsCodes")
  public Result<List<CostTypeCategoryVo>> findByBudgetSubjectsCode(@ApiParam(name = "budgetSubjectsCodes", value = "预算科目编号", required = true) @RequestParam(name = "budgetSubjectsCodes") List<String> budgetSubjectsCodes) {
    try {
      List<CostTypeCategoryVo> costTypeCategoryVos = this.costTypeCategoryVoService.findByBudgetSubjectsCodes(budgetSubjectsCodes);
      return Result.ok(costTypeCategoryVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}