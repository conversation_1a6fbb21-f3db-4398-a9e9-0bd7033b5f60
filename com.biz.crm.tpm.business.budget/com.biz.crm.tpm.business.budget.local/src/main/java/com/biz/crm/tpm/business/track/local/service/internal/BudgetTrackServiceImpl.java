package com.biz.crm.tpm.business.track.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.control.sdk.enums.ControlTypeEnum;
import com.biz.crm.tpm.business.control.sdk.enums.TimeDimensionEnum;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlLockVoService;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlVoService;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import com.biz.crm.tpm.business.track.local.entity.BudgetTrack;
import com.biz.crm.tpm.business.track.local.repository.BudgetTrackRepository;
import com.biz.crm.tpm.business.track.local.service.BudgetTrackAsyncService;
import com.biz.crm.tpm.business.track.sdk.constant.BudgetTrackConstant;
import com.biz.crm.tpm.business.track.sdk.dto.BudgetTrackDto;
import com.biz.crm.tpm.business.track.sdk.service.BudgetTrackService;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BudgetTrackServiceImpl implements BudgetTrackService {

    @Autowired(required = false)
    private BudgetControlVoService budgetControlVoService;
    @Autowired(required = false)
    private BudgetTrackRepository budgetTrackRepository;
    @Autowired(required = false)
    private OrgVoService orgVoService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private BudgetControlLockVoService budgetControlLockVoService;
    @Autowired(required = false)
    private BudgetSubjectsVoService budgetSubjectsVoService;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private BudgetTrackAsyncService budgetTrackAsyncService;

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    @Override
    public BudgetTrackVo findByCode(String code) {
        Validate.notBlank(code, "编码不能为空");
        BudgetTrack entity = budgetTrackRepository.findByCode(code);
        Validate.notNull(entity, "未找到对应的费用跟踪数据");
        return nebulaToolkitService.copyObjectByWhiteList(entity, BudgetTrackVo.class, LinkedHashSet.class, ArrayList.class);
    }


    /**
     * 查询费用预算跟踪
     *
     * @param budgetControlCodes
     * @return
     */
    @Override
    public List<BudgetTrackVo> findByBudgetControlCodes(List<String> budgetControlCodes) {
        if (CollectionUtils.isEmpty(budgetControlCodes)) {
            return Lists.newArrayList();
        }
        List<BudgetTrack> tracks = budgetTrackRepository.findByBudgetControlCodes(budgetControlCodes);
        if (CollectionUtils.isEmpty(tracks)) {
            return Lists.newArrayList();
        }
        return (List<BudgetTrackVo>) nebulaToolkitService.copyCollectionByWhiteList(tracks, BudgetTrack.class, BudgetTrackVo.class,
                HashSet.class, ArrayList.class);
    }

    /**
     * 修改税率
     *
     * @param dto
     */
    @Override
    public void updateRate(BudgetTrackDto dto) {
        Validate.notBlank(dto.getTrackCode(), "编码不能为空");
        Validate.notNull(dto.getRateNew(), "修改费率不能为空");
        BudgetTrack entity = budgetTrackRepository.findByCode(dto.getTrackCode());
        Validate.notNull(entity, "未找到对应的费用跟踪数据");
        entity.setRate(dto.getRateNew());
        budgetTrackRepository.saveOrUpdate(entity);
    }

    /**
     * 费率计算
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void calculateRate(BudgetTrackDto dto) {
        Validate.notBlank(dto.getYearStr(), "年不能为空");
        List<BudgetTrack> list = budgetTrackRepository.findByDto(dto);
        Validate.notEmpty(list, "未找到对应的费用跟踪数据");
        Set<String> deptCodeSet = list.stream().map(BudgetTrack::getDepartmentOneCode).collect(Collectors.toSet());
        Set<String> deptCodeSetDto = new HashSet<>();
        deptCodeSet.forEach(e -> {
            String[] split = e.split(",");
            for (int i = 0; i < split.length; i++) {
                deptCodeSetDto.add(split[i]);
            }
        });
        Map<String, List<OrgVo>> orgVoMap = orgVoService.findAllChildrenByOrgCodesMap(new ArrayList<>(deptCodeSetDto));
        budgetTrackAsyncService.calculate(list, orgVoMap);
        budgetTrackRepository.saveOrUpdateBatch(list);
    }


    /**
     * 预算跟踪生成
     */
    @Override
    public void generateBudgetTrack() {
        String yearMonthLy = DateUtil.format(new Date(), "yyyy-MM");
        generateBudgetTrackYearMonth(yearMonthLy, null);
    }

    /**
     * 预算跟踪按年月生成
     *
     * @param yearMonthLy
     */
    @Override
    public void generateBudgetTrackYearMonth(String yearMonthLy, String controlCode) {
        //查询范围内的预算管控
        List<BudgetControlVo> controlVos = budgetControlVoService.findBudgetControlEnable(yearMonthLy);
        Validate.notEmpty(controlVos, "未找到生效的预算管控");
        if (StringUtils.isNotBlank(controlCode)) {
            controlVos = controlVos.stream().filter(e -> controlCode.equals(e.getControlCode())).collect(Collectors.toList());
            Validate.notEmpty(controlVos, "未找到【%s】对应的预算管控", controlCode);
        }
        //查询预算科目
        List<BudgetSubjectsVo> budgetSubjectsVoList = budgetSubjectsVoService.findByEnableStatus(EnableStatusEnum.ENABLE.getCode());
        Map<String, Set<String>> subjectMap = getSubjectMap(budgetSubjectsVoList);
        generateBudgetTrackBudgetControlVos(controlVos, subjectMap);
    }

    private void generateBudgetTrackBudgetControlVos(List<BudgetControlVo> controlVos, Map<String, Set<String>> subjectMap) {
        List<String> codes = controlVos.stream().map(BudgetControlVo::getControlCode).collect(Collectors.toList());

        Date date = new Date();
        AbstractCrmUserIdentity crmUserIdentity = loginUserService.getAbstractLoginUser();
        String userName = crmUserIdentity.getAccount();
        String realName = crmUserIdentity.getRealName();
        List<Future<List<BudgetTrack>>> futureList = Lists.newArrayList();
        controlVos.forEach(e -> {
            Future<List<BudgetTrack>> future = budgetTrackAsyncService.generateBudgetTrackSingle(e, subjectMap, crmUserIdentity);
            futureList.add(future);
        });
        List<BudgetTrack> budgetTrackAllList = Lists.newArrayList();
        for (Future<List<BudgetTrack>> future : futureList) {
            try {
                budgetTrackAllList.addAll(future.get());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        if (CollectionUtil.isEmpty(budgetTrackAllList)) {
            return;
        }
        calculateAmountByRate(budgetTrackAllList);
        calculateAmountByAmount(budgetTrackAllList);

        boolean lock = budgetControlLockVoService.lock(codes, TimeUnit.MINUTES, 3);
        Validate.isTrue(lock, "操作预算管控，获取操作锁失败！");
        try {
            budgetTrackRepository.deleteByControlCodeList(codes);
            List<String> codeList = generateCodeService.generateCode(BudgetTrackConstant.BUDGET_TRACK_RULE_CODE, budgetTrackAllList.size());
            for (int i = 0; i < budgetTrackAllList.size(); i++) {
                BudgetTrack budgetTrack = budgetTrackAllList.get(i);
                budgetTrack.setId(UuidCrmUtil.general());
                budgetTrack.setCreateAccount(userName);
                budgetTrack.setCreateName(realName);
                budgetTrack.setCreateTime(date);
                budgetTrack.setModifyAccount(userName);
                budgetTrack.setModifyName(realName);
                budgetTrack.setModifyTime(date);
                budgetTrack.setTrackCode(codeList.get(i));
                // 根据部门编码做权限将部门赋值给权限字段
                budgetTrack.setOrgCode(budgetTrack.getDepartmentOneCode());
            }
            budgetTrackRepository.saveBatchXml(budgetTrackAllList);
        } finally {
            budgetControlLockVoService.unLock(codes);
        }
    }

    /**
     * 按率管控
     * <p>
     * 按年管控:
     * 按率 ：算n月预算时：1月的“当月实际预算费用”+……+（n-2）月的“当月实际预算费用”+（n-1）月的“当月金额”+n月的“当月金额”
     * 按率 1月的“当月实际预算费用”+2月的“当月实际预算费用”+3月的“当月金额” + 4月的“当月金额”
     * 按额 1月的“当月金额”+2月的“当月金额”+3月的“当月金额”+ 4月的“当月金额”
     * <p>
     * 按月管控:
     * 当月
     * <p>
     * 计算 当月实际预算费用和当月金额
     */
    private void calculateAmountByRate(List<BudgetTrack> budgetTrackAllList) {
        if (CollectionUtils.isEmpty(budgetTrackAllList)) {
            return;
        }
        budgetTrackAllList.forEach(e -> {
            if (Objects.isNull(e.getActualAmount())) {
                e.setActualAmount(BigDecimal.ZERO);
            }
            if (StringUtils.isNotEmpty(e.getYearMonthLy())
                    && e.getYearMonthLy().length() >= 7) {
                e.setYearLy(e.getYearMonthLy().substring(0, 4));
                e.setMonthInt(Integer.parseInt(e.getYearMonthLy().substring(5, 7)));
            }
        });
        Map<String, List<BudgetTrack>> budgetTrackGroup = budgetTrackAllList.stream()
                .filter(e -> ControlTypeEnum.RATE.getCode().equals(e.getBudgetControlType()))
                .filter(e -> StringUtils.isNotBlank(e.getYearLy()))
                .filter(e -> Objects.nonNull(e.getMonthInt()))
                .filter(e -> StringUtils.isNotBlank(e.getDepartmentOneCode()))
                .filter(e -> StringUtils.isNotBlank(e.getBudgetControlCode()))
                .collect(Collectors.groupingBy(e -> e.getYearLy() + "_" + e.getDepartmentOneCode() + "_" + e.getBudgetControlCode()));
        if (CollectionUtil.isEmpty(budgetTrackGroup)) {
            return;
        }
        // 后续每个步骤相互关联，所以需要先计算当月实际预算费用，再计算一月 预算管控总额，最后计算大于二月 预算管控总额
        budgetTrackGroup.forEach((k, list) -> {
            List<Integer> uuidList = list.stream().map(BudgetTrack::getMonthInt).collect(Collectors.toList());
            // 使用Stream找到最大值
            int max = uuidList.stream().max(Integer::compareTo).orElse(0);
            //1 计算 按年管控 小于当前月份2个月的当月实际预算费用
            list.forEach(e -> {
                if (TimeDimensionEnum.YEAR.getCode().equals(e.getBudgetTimeDimension())) {
                    if (e.getMonthInt() + 2 <= max) {
                        if (Objects.nonNull(e.getRate())
                                && Objects.nonNull(e.getReportAmount())) {
                            e.setActualAmount(e.getRate().multiply(e.getReportAmount()).setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                }
            });
            //2 计算
            // 按年管控 一月和二月 预算管控总额
            // 按月管控 所有 预算管控总额
            list.forEach(e -> {
                if (TimeDimensionEnum.YEAR.getCode().equals(e.getBudgetTimeDimension())) {
                    if (e.getMonthInt() <= 2) {
//                        e.setInitialAmount(list.stream()
//                                .filter(e1 -> e1.getMonthInt() <= e.getMonthInt())
//                                .map(BudgetTrack::getThisInitialAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                } else if (TimeDimensionEnum.MONTH.getCode().equals(e.getBudgetTimeDimension())) {
                    e.setInitialAmount(e.getThisInitialAmount());
                }
            });
            //3 计算 按年管控 大于二月 预算管控总额
            list.forEach(e -> {
                if (TimeDimensionEnum.YEAR.getCode().equals(e.getBudgetTimeDimension())) {
                    if (e.getMonthInt() > 2) {
                        //汇总大于2个月的当月实际预算费用
//                        BigDecimal initialAmount = list.stream()
//                                .filter(e1 -> e.getMonthInt() - e1.getMonthInt() >= 2)
//                                .map(BudgetTrack::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        //大于2个月的当月实际预算费用+近2月的当月金额=预算管控总额
//                        initialAmount = initialAmount.add(list.stream()
//                                .filter(e1 -> e.getMonthInt() - e1.getMonthInt() < 2)
//                                .filter(e1 -> e.getMonthInt() - e1.getMonthInt() >= 0)
//                                .map(BudgetTrack::getThisInitialAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
//                        e.setInitialAmount(initialAmount);
                    }
                }
            });
            //4 计算可用余额=预算管控总额-已使用金额
            list.forEach(e -> {
                if (Objects.nonNull(e.getInitialAmount())
                        && Objects.nonNull(e.getUsedAmount())) {
                    e.setMonthBalanceAmount(e.getInitialAmount().subtract(e.getUsedAmount()));
                }
            });
        });

    }

    /**
     * 按额管控
     * <p>
     * 按年管控:
     * 按率 ：算n月预算时：1月的“当月实际预算费用”+……+（n-2）月的“当月实际预算费用”+（n-1）月的“当月金额”+n月的“当月金额”
     * 按率 1月的“当月实际预算费用”+2月的“当月实际预算费用”+3月的“当月金额” + 4月的“当月金额”
     * 按额 1月的“当月金额”+2月的“当月金额”+3月的“当月金额”+ 4月的“当月金额”
     * <p>
     * 按月管控:
     * 当月
     * <p>
     * 计算 当月实际预算费用和当月金额
     */
    private void calculateAmountByAmount(List<BudgetTrack> budgetTrackAllList) {
        if (CollectionUtils.isEmpty(budgetTrackAllList)) {
            return;
        }
        budgetTrackAllList.forEach(e -> {
            if (Objects.isNull(e.getActualAmount())) {
                e.setActualAmount(BigDecimal.ZERO);
            }
            if (StringUtils.isNotEmpty(e.getYearMonthLy())
                    && e.getYearMonthLy().length() >= 7) {
                e.setYearLy(e.getYearMonthLy().substring(0, 4));
                e.setMonthInt(Integer.parseInt(e.getYearMonthLy().substring(5, 7)));
            }
        });
        Map<String, List<BudgetTrack>> budgetTrackGroup = budgetTrackAllList.stream()
                .filter(e -> ControlTypeEnum.AMOUNT.getCode().equals(e.getBudgetControlType()))
                .filter(e -> StringUtils.isNotBlank(e.getYearLy()))
                .filter(e -> Objects.nonNull(e.getMonthInt()))
                .filter(e -> StringUtils.isNotBlank(e.getDepartmentOneCode()))
                .filter(e -> StringUtils.isNotBlank(e.getBudgetControlCode()))
                .collect(Collectors.groupingBy(e -> e.getYearLy() + "_" + e.getDepartmentOneCode() + "_" + e.getBudgetControlCode()));
        if (CollectionUtil.isEmpty(budgetTrackGroup)) {
            return;
        }
        // 后续每个步骤相互关联，所以需要先计算当月实际预算费用，再计算一月 预算管控总额，最后计算大于二月 预算管控总额
        budgetTrackGroup.forEach((k, list) -> {
            List<Integer> uuidList = list.stream().map(BudgetTrack::getMonthInt).collect(Collectors.toList());
            // 使用Stream找到最大值
            int max = uuidList.stream().max(Integer::compareTo).orElse(0);
            //1 计算小于当前月份2个月的当月实际预算费用
            list.forEach(e -> {
                if (TimeDimensionEnum.YEAR.getCode().equals(e.getBudgetTimeDimension())) {
                    if (e.getMonthInt() + 2 <= max) {
                        if (Objects.nonNull(e.getRate())
                                && Objects.nonNull(e.getReportAmount())) {
                            e.setActualAmount(e.getRate().multiply(e.getReportAmount()).setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                }
            });
            //2 计算 预算管控总额
            list.forEach(e -> {
                if (TimeDimensionEnum.YEAR.getCode().equals(e.getBudgetTimeDimension())) {
                    e.setInitialAmount(list.stream()
                            .filter(e1 -> e1.getMonthInt() <= e.getMonthInt())
                            .map(BudgetTrack::getThisInitialAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                } else if (TimeDimensionEnum.MONTH.getCode().equals(e.getBudgetTimeDimension())) {
                    e.setInitialAmount(e.getThisInitialAmount());
                }
            });
            //3 计算可用余额=预算管控总额-已使用金额
            list.forEach(e -> {
                if (Objects.nonNull(e.getInitialAmount())
                        && Objects.nonNull(e.getUsedAmount())) {
                    e.setMonthBalanceAmount(e.getInitialAmount().subtract(e.getUsedAmount()));
                }
            });
        });

    }

    /**
     * 生成预算管控费用追踪
     *
     * @param codes
     */
    @Override
    public void generateBudgetTrackByCodes(List<String> codes) {
        //查询预算科目
        List<BudgetSubjectsVo> budgetSubjectsVoList = budgetSubjectsVoService.findByEnableStatus(EnableStatusEnum.ENABLE.getCode());
        Map<String, Set<String>> subjectMap = getSubjectMap(budgetSubjectsVoList);
        List<BudgetControlVo> controlVos = budgetControlVoService.findBudgetControlByCodes(codes);
        generateBudgetTrackBudgetControlVos(controlVos, subjectMap);
    }


    /**
     * 预算科目转map
     *
     * @param allList
     * @return
     */
    private Map<String, Set<String>> getSubjectMap(List<BudgetSubjectsVo> allList) {
        Map<String, Set<String>> map = new HashMap<>();
        allList.forEach(e -> map.put(e.getBudgetSubjectsCode(), getChildrenCodes(Arrays.asList(e), allList)));
        return map;
    }

    /**
     * 获取当前预算科目下所有的子级预算科目编码
     *
     * @return
     */
    private Set<String> getChildrenCodes(List<BudgetSubjectsVo> subjectList, List<BudgetSubjectsVo> allList) {
        Set<String> codes = new HashSet<>();
        for (BudgetSubjectsVo subject : subjectList) {
            codes.add(subject.getBudgetSubjectsCode());
            List<BudgetSubjectsVo> subjectChildren = getChildren(subject, allList);
            if (CollectionUtils.isNotEmpty(subjectChildren)) {
                codes.addAll(getChildrenCodes(subjectChildren, allList));
            }
        }
        return codes;
    }

    /**
     * 获取当前预算科目下子级预算科目
     *
     * @param
     * @return
     */
    private List<BudgetSubjectsVo> getChildren(BudgetSubjectsVo subjectChildren, List<BudgetSubjectsVo> allList) {
        return allList.stream().filter(e -> StringUtils.isNotBlank(e.getParentBudgetSubjectsCode()) && e.getParentBudgetSubjectsCode().equals(subjectChildren.getBudgetSubjectsCode()))
                .collect(Collectors.toList());
    }
}
