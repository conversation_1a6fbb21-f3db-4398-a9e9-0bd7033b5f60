<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.budget.local.mapper.CostTypeDetailMapper">
    <resultMap type="com.biz.crm.tpm.business.budget.local.entity.CostTypeDetail" id="CostTypeDetailsMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="modifyName" column="modify_name" jdbcType="VARCHAR"/>
        <result property="modifyAccount" column="modify_account" jdbcType="VARCHAR"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="createName" column="create_name" jdbcType="VARCHAR"/>
        <result property="createAccount" column="create_account" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="enableStatus" column="enable_status" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="detailName" column="detail_name" jdbcType="VARCHAR"/>
        <result property="detailCode" column="detail_code" jdbcType="VARCHAR"/>
        <result property="accountingSubjectsCode" column="accounting_subjects_code" jdbcType="VARCHAR"/>
        <result property="accountingSubjectsName" column="accounting_subjects_name" jdbcType="VARCHAR"/>
        <result property="isShareToProduct" column="is_share_to_product" jdbcType="VARCHAR"/>
        <result property="payBy" column="pay_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="costTypeDetail">
        t
        .
        id
        id,
        t.tenant_code                   tenantCode,
        t.modify_name                   modifyName,
        t.modify_account                modifyAccount,
        t.modify_time                   modifyTime,
        t.create_name                   createName,
        t.create_account                createAccount,
        t.create_time                   createTime,
        t.del_flag                      delFlag,
        t.enable_status                 enableStatus,
        t.remark                        remark,
        t.detail_name                   detailName,
        t.detail_code                   detailCode,
        t.accounting_subjects_code      accountingSubjectsCode,
        t.accounting_subjects_name      accountingSubjectsName,
        t.is_share_to_product           isShareToProduct,
        t.pay_by                        payBy
    </sql>

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo">
        select
        t.*
        from tpm_cost_type_detail t
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.enableStatus != null and dto.enableStatus != ''">
                and t.enable_status = #{dto.enableStatus}
            </if>
            <if test="dto.categoryCode != null and dto.categoryCode != ''">
                and t.category_code = #{dto.categoryCode}
            </if>
            <if test="dto.detailName != null and dto.detailName != ''">
                <bind name="detailName" value="'%' + dto.detailName + '%'"/>
                and t.detail_name like #{detailName}
            </if>
            <if test="dto.detailCode != null and dto.detailCode != ''">
                <bind name="detailCode" value="'%' + dto.detailCode + '%'"/>
                and t.detail_code like #{detailCode}
            </if>
            <if test="dto.categoryCodeSet != null and dto.categoryCodeSet.size()>0">
                and t.category_code in
                <foreach collection="dto.categoryCodeSet" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.secondCostCategory != null and dto.secondCostCategory != ''">
                and exists (
                SELECT
                1
                FROM
                tpm_cost_type_category a
                WHERE
                t.category_code = a.category_code
                and a.del_flag = '${@<EMAIL>()}' and a.enable_status = '${@<EMAIL>()}'
                AND a.budget_subjects_code in(
                SELECT
                budget_subjects_code FROM tpm_budget_subjects
                WHERE
                parent_budget_subjects_code = #{dto.secondCostCategory}
                AND del_flag = '${@<EMAIL>()}'
                AND enable_status = '${@<EMAIL>()}')
                )
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
        order by
        <if test="dto.selectedCodeList != null and dto.selectedCodeList.size > 0">
            CASE
            <foreach collection="dto.selectedCodeList" item="item" index="index">
                WHEN t.detail_code = #{item} THEN ${index}
            </foreach>
            ELSE 99 END asc,
        </if>
        t.create_time desc, t.id
    </select>

    <select id="findCodeByCondition" resultType="java.lang.String">
        select distinct t.detail_code from tpm_cost_type_detail t
        inner join tpm_cost_type_detail_setting_strategy ss on t.detail_code = ss.detail_code
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.enableStatus != null and dto.enableStatus != ''">
                and t.enable_status = #{dto.enableStatus}
            </if>
            <if test="dto.detailName != null and dto.detailName != ''">
                <bind name="detailName" value="'%' + dto.detailName + '%'"/>
                and t.detail_name like #{detailName}
            </if>
            <if test="dto.detailCode != null and dto.detailCode != ''">
                <bind name="detailCode" value="'%' + dto.detailCode + '%'"/>
                and t.detail_code like #{detailCode}
            </if>
            <if test="dto.isAudit != null and dto.isAudit != ''">
                and ss.code = 'AuditSetting' and ss.value = #{dto.isAudit}
            </if>
            <if test="dto.isAutoAudit != null and dto.isAutoAudit != ''">
                and ss.code = 'AutoAuditSetting' and ss.value = #{dto.isAutoAudit}
            </if>
            <if test="dto.isMultipleAudit != null and dto.isMultipleAudit != ''">
                and ss.code = 'AuditMulitSetting' and ss.value = #{dto.isMultipleAudit}
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
    </select>

    <select id="findListByCondition" resultType="java.lang.String">
        select
        t.detail_code
        from tpm_cost_type_detail t
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.enableStatus != null and dto.enableStatus != ''">
                and t.enable_status = #{dto.enableStatus}
            </if>
            <if test="dto.categoryCode != null and dto.categoryCode != ''">
                and t.category_code = #{dto.categoryCode}
            </if>
            <if test="dto.detailName != null and dto.detailName != ''">
                <bind name="detailName" value="'%' + dto.detailName + '%'"/>
                and t.detail_name like #{detailName}
            </if>
            <if test="dto.detailCode != null and dto.detailCode != ''">
                <bind name="detailCode" value="'%' + dto.detailCode + '%'"/>
                and t.detail_code like #{detailCode}
            </if>
            <if test="dto.categoryCodeSet != null and dto.categoryCodeSet.size()>0">
                and t.category_code in
                <foreach collection="dto.categoryCodeSet" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.secondCostCategory != null and dto.secondCostCategory != ''">
                and exists (
                SELECT
                1
                FROM
                tpm_cost_type_category a
                WHERE
                t.category_code = a.category_code
                and a.del_flag = '${@<EMAIL>()}' and a.enable_status = '${@<EMAIL>()}'
                AND a.budget_subjects_code in(
                SELECT
                budget_subjects_code FROM tpm_budget_subjects
                WHERE
                parent_budget_subjects_code = #{dto.secondCostCategory}
                AND del_flag = '${@<EMAIL>()}'
                AND enable_status = '${@<EMAIL>()}')
                )
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
    </select>
</mapper>