<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    
<mapper namespace="com.biz.crm.tpm.business.budget.local.mapper.CostTypeMappingMapper">
  <resultMap type="com.biz.crm.tpm.business.budget.local.entity.CostTypeMapping" id="CostTypeMappingMap">
      <result property="id" column="id" jdbcType="VARCHAR"/>
      <result property="categoryCode" column="category_code" jdbcType="VARCHAR"/>
      <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
      <result property="detailCode" column="detail_code" jdbcType="VARCHAR"/>
      <result property="budgetSubjectsCode" column="budget_subjects_code" jdbcType="VARCHAR"/>
      <result property="budgetSubjectsName" column="budget_subjects_name" jdbcType="VARCHAR"/>
      <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
  </resultMap>
</mapper>