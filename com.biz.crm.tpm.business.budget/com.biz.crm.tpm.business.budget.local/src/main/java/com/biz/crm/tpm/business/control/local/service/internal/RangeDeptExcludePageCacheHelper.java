package com.biz.crm.tpm.business.control.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.control.local.repository.BudgetControlRangeRepository;
import com.biz.crm.tpm.business.control.sdk.constant.BudgetControlConstant;
import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlDeptExcludeRangeDto;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlDeptExcludeRangeVo;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlRangeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 分页缓存
 */
@Slf4j
@Component
public class RangeDeptExcludePageCacheHelper extends BusinessPageCacheHelper<BudgetControlDeptExcludeRangeVo, BudgetControlDeptExcludeRangeDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private BudgetControlRangeRepository budgetControlRangeRepository;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return BudgetControlConstant.CACHE_KEY_DEPT_EXCLUDE_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<BudgetControlDeptExcludeRangeDto> getDtoClass() {
        return BudgetControlDeptExcludeRangeDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<BudgetControlDeptExcludeRangeVo> getVoClass() {
        return BudgetControlDeptExcludeRangeVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param dto
     * @param cacheKey
     */
    @Override
    public List<BudgetControlDeptExcludeRangeDto> findDtoListFromRepository(BudgetControlDeptExcludeRangeDto dto, String cacheKey) {
        if (StringUtils.isBlank(dto.getControlCode())) {
            return new ArrayList<>();
        }
        List<BudgetControlRangeVo> vos = budgetControlRangeRepository.findByCode(dto.getControlCode());
        vos = vos.stream().filter(e -> e.getRangeType().equals("deptExclude")).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(vos) ? (List<BudgetControlDeptExcludeRangeDto>) nebulaToolkitService.copyCollectionByBlankList(vos, BudgetControlRangeVo.class, BudgetControlDeptExcludeRangeDto.class, HashSet.class, ArrayList.class) :
                new ArrayList<>();
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlDeptExcludeRangeDto> newItem(String cacheKey, List<BudgetControlDeptExcludeRangeDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlDeptExcludeRangeDto> copyItem(String cacheKey, List<BudgetControlDeptExcludeRangeDto> itemList) {
        List<BudgetControlDeptExcludeRangeDto> newItemList = (List<BudgetControlDeptExcludeRangeDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, BudgetControlDeptExcludeRangeDto.class, BudgetControlDeptExcludeRangeDto.class, HashSet.class, ArrayList.class);
        for (BudgetControlDeptExcludeRangeDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 获取Dto中的主键
     *
     * @param dto
     * @return 主键
     */
    @Override
    public Object getDtoKey(BudgetControlDeptExcludeRangeDto dto) {
        return dto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param dto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(BudgetControlDeptExcludeRangeDto dto) {
        return dto.getChecked();
    }

}
