package com.biz.crm.tpm.business.budget.local.notifier;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetIncomeLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.event.log.CostBudgetIncomeLogEventListener;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class CostBudgetIncomeLogEventListenerImpl implements CostBudgetIncomeLogEventListener {

    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 删除日志
     *
     * @param eventDto
     */
    @Override
    public void onDelete(CostBudgetIncomeLogEventDto eventDto) {

    }

    /**
     * 更新日志
     *
     * @param eventDto
     */
    @Override
    public void onUpdate(CostBudgetIncomeLogEventDto eventDto) {
        CostBudgetIncomeVo original = eventDto.getOriginal();
        CostBudgetIncomeVo newest = eventDto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    /**
     * 启用事件
     *
     * @param dto
     */
    @Override
    public void onEnable(CostBudgetIncomeLogEventDto dto) {
        List<CostBudgetIncomeVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        List<CostBudgetIncomeVo> oldList = (List<CostBudgetIncomeVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, CostBudgetIncomeVo.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class);
        Map<String, CostBudgetIncomeVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(CostBudgetIncomeVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.ENABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            CostBudgetIncomeVo original = oldMap.getOrDefault(newest.getId(), new CostBudgetIncomeVo());
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    /**
     * 禁用事件
     *
     * @param dto
     */
    @Override
    public void onDisable(CostBudgetIncomeLogEventDto dto) {
        List<CostBudgetIncomeVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        List<CostBudgetIncomeVo> oldList = (List<CostBudgetIncomeVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, CostBudgetIncomeVo.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class);
        Map<String, CostBudgetIncomeVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(CostBudgetIncomeVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DISABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            CostBudgetIncomeVo original = oldMap.getOrDefault(newest.getId(), new CostBudgetIncomeVo());
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }
}
