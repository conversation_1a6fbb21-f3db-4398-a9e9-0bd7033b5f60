package com.biz.crm.tpm.business.budget.local.repository;

import com.biz.crm.tpm.business.budget.local.entity.CostTypeCategory;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeCategoryDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;

import com.biz.crm.tpm.business.budget.local.mapper.CostTypeCategoryMapper;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Collections;
import java.util.Set;

/**
 * TPM-活动大类;(tpm_cost_type_category)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
@Component
@Deprecated
public class CostTypeCategoryRepository extends ServiceImpl<CostTypeCategoryMapper, CostTypeCategory> {
    @Autowired
    private CostTypeCategoryMapper costTypeCategoryMapper;

    /**
     * 批量根据id禁用
     *
     * @param enable
     * @param ids
     */
    public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
        UpdateWrapper<CostTypeCategory> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("enable_status", enable.getCode());
        updateWrapper.eq("tenant_code", TenantUtils.getTenantCode());
        updateWrapper.in("id", ids);
        this.update(updateWrapper);
    }

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    public Page<CostTypeCategoryVo> findByConditions(Pageable pageable, CostTypeCategoryDto dto) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 50);
        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<CostTypeCategoryVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return costTypeCategoryMapper.findByConditions(page, dto);
    }

    /**
     * 根据id集合获取详情集合
     *
     * @param ids ID集合
     * @return List<CostTypeCategory>
     */
    public List<CostTypeCategory> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(CostTypeCategory::getId, ids)
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据编号与租户编号获取对象
     *
     * @param code
     * @return
     */
    public CostTypeCategory findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .eq(CostTypeCategory::getCategoryCode, code)
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
    }

    /**
     * 根据编号与租户编号获取对象
     *
     * @param codes
     * @return
     */
    public List<CostTypeCategory> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return null;
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(CostTypeCategory::getCategoryCode, codes)
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }

    /**
     * 根据启用状态与租户编号查询对象
     *
     * @param enableStatus
     * @return
     */
    public List<CostTypeCategory> findByEnableStatus(String enableStatus) {
        String tenantCode = TenantUtils.getTenantCode();
        if (StringUtils.isBlank(enableStatus)) {
            return this.lambdaQuery()
                    .eq(CostTypeCategory::getTenantCode, tenantCode)
                    .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
        }
        return this.lambdaQuery()
                .eq(CostTypeCategory::getEnableStatus, enableStatus)
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        String tenantCode = TenantUtils.getTenantCode();
        UpdateWrapper<CostTypeCategory> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("del_flag", DelFlagStatusEnum.DELETE.getCode());
        updateWrapper.eq("tenant_code", tenantCode);
        updateWrapper.in("id", idList);
        return this.update(updateWrapper);
    }

    /**
     * 根据预算科目编号获取启用状态的活动大类信息
     *
     * @param budgetSubjectsCode
     * @return
     */
    public List<CostTypeCategory> findByBudgetSubjectsCode(String budgetSubjectsCode) {
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostTypeCategory::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(CostTypeCategory::getBudgetSubjectsCode, budgetSubjectsCode).list();
    }

    /**
     * 根据预算科目编号获取启用状态的活动大类信息
     *
     * @param budgetSubjectsCodes
     * @return
     */
    public List<CostTypeCategory> findByBudgetSubjectsCodes(List<String> budgetSubjectsCodes) {
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostTypeCategory::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .in(CostTypeCategory::getBudgetSubjectsCode, budgetSubjectsCodes).list();
    }

    /**
     * 根据预算科目编号获取未删除的活动大类信息
     * @param budgetSubjectsCodes
     * @return
     */
    public List<CostTypeCategory> findAllByBudgetSubjectsCodes(List<String> budgetSubjectsCodes) {
        if (CollectionUtils.isEmpty(budgetSubjectsCodes)) {
            return Lists.newArrayList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
            .eq(CostTypeCategory::getTenantCode, tenantCode)
            .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .in(CostTypeCategory::getBudgetSubjectsCode, budgetSubjectsCodes).list();
    }

    public CostTypeCategory findByIdAndTenantCode(String id, String tenantCode) {
        return this.lambdaQuery()
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .in(CostTypeCategory::getId, id)
                .one();
    }

    public void removeByIdsAndTenantCode(List<String> ids, String tenantCode) {
        this.lambdaUpdate()
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .in(CostTypeCategory::getId, ids)
                .remove();
    }


    public List<CostTypeCategory> findListByNames(List<String> nameList, String tenantCode) {
        return this.lambdaQuery()
                .in(CostTypeCategory::getCategoryName, nameList)
                .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostTypeCategory::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .list();
    }

    public List<CostTypeCategory> findListByCategoryCodes(List<String> categoryCodes,String tenantCode){
        return this.lambdaQuery()
                .in(CostTypeCategory::getCategoryCode, categoryCodes)
                .eq(CostTypeCategory::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostTypeCategory::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(CostTypeCategory::getTenantCode, tenantCode)
                .list();
    }


    /**
     * 查询关联预算科目的活动大类
     * @return
     */
    public List<CostTypeCategoryVo> findListReleaseSecondBudgetSubjectCodes(){
        return this.baseMapper.findListReleaseSecondBudgetSubjectCodes(TenantUtils.getTenantCode());
    }

}