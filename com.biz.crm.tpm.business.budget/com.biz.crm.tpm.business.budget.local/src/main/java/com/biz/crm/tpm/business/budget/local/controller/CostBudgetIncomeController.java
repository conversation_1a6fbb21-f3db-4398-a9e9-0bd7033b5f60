package com.biz.crm.tpm.business.budget.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 收入预算
 */
@RestController
@RequestMapping("/v1/budget/costBudgetIncome")
@Slf4j
@Api(tags = "收入预算")
public class CostBudgetIncomeController {

    @Autowired
    private CostBudgetIncomeService costBudgetIncomeService;


    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除操作")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
        try {
            this.costBudgetIncomeService.delete(idList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 启禁用
     *
     * @param ids
     * @param enableStatus
     * @return
     */
    @ApiOperation(value = "启禁用")
    @PatchMapping("updateEnableStatus")
    public Result<?> updateEnableStatus(@RequestBody @ApiParam(name = "ids", value = "主键id") Set<String> ids,
                                        @RequestParam("enableStatus") @ApiParam(name = "enableStatus", value = "启禁用状态") String enableStatus) {
        try {
            this.costBudgetIncomeService.updateEnableStatus(ids, enableStatus);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 根据费用预算编号确认
     *
     * @param codeList
     * @return
     */
    @ApiOperation(value = "根据费用预算编号确认")
    @GetMapping("confirm")
    public Result<?> confirm(@RequestParam("codeList") @ApiParam(name = "codeList", value = "费用预算编码") List<String> codeList) {
        try {
            costBudgetIncomeService.confirm(codeList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("findListByOrgCodesAndYears")
    @ApiOperation(value = "通过组织+年月查询预算收入")
    public Result<List<CostBudgetIncomeVo>> findListByOrgCodesAndYears(@RequestBody List<String> orgCodes, @RequestParam("years") String years) {
        return Result.ok(costBudgetIncomeService.findListByOrgCodesAndYears(orgCodes, years));
    }

    @PostMapping("findListByOrgCodesAndYearsValid")
    @ApiOperation(value = "通过组织+年月查询预算收入（已确认）")
    public Result<List<CostBudgetIncomeVo>> findListByOrgCodesAndYearsValid(@RequestBody List<String> orgCodes, @RequestParam("years") String years) {
        return Result.ok(costBudgetIncomeService.findListByOrgCodesAndYearsValid(orgCodes, years));
    }

    @ApiOperation(value = "查询组织下所有的预算收入+年月")
    @PostMapping("findListAllOrgCodeChildrenAndYears")
    public Result<List<CostBudgetIncomeVo>> findListAllOrgCodeChildrenAndYears(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(costBudgetIncomeService.findListAllOrgCodeChildrenAndYears(dto.getOrgCodes(), dto.getYears(), dto.getYearsList(),dto.getChannelDepartmentCodeList()));
    }

    @ApiOperation(value = "查询组织下所有的预算收入+年月")
    @PostMapping("findListAllOrgCodeChildrenAndYearsValid")
    public Result<List<CostBudgetIncomeVo>> findListAllOrgCodeChildrenAndYearsValid(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(costBudgetIncomeService.findListAllOrgCodeChildrenAndYearsValid(dto.getOrgCodes(), dto.getYears(), dto.getYearsList(),dto.getChannelDepartmentCodeList()));
    }

}
