package com.biz.crm.tpm.business.budget.local.strategy.controltype;


import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetItemDto;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetOperateType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetItemVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 描述：</br>预算科目控制类型，提供预算全年控制方法
 *
 * <AUTHOR>
 * @date 2022/5/18
 */
@Slf4j
public class YearControlTypeStrategy extends AbstractBudgetControlTypeStrategy {

  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private CostBudgetItemVoService costBudgetItemVoService;
  @Autowired
  private RedisMutexService redisMutexService;

  @Override
  public String getCode() {
    return "YEAR";
  }

  @Override
  public String getName() {
    return "全年";
  }

  @Override
  public int getOrder() {
    return 1;
  }

  @Override
  public void forward(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source) {
    Validate.notBlank(businessCode, "业务编号不能为空");
    Validate.notBlank(businessItemCode, "业务明细编号不能为空");
    Validate.notBlank(costBudgetCode, "费用预算编码不能为空");
    Validate.notBlank(source, "费用预算明细来源不能为空");
    Validate.notNull(operateAmount, "操作金额不能为空");
    Validate.isTrue(CostBudgetItemSourceType.contains(source),"未知的费用预算明细来源【%s】，请检查",source);
    Validate.isTrue(operateAmount.compareTo(BigDecimal.ZERO) > 0, "使用金额必须大于0");

    //需要进行redis锁操作
    boolean hasLock = false;
    try {
      hasLock = redisMutexService.tryLock(BUDGET_GLOBAL_REDIS_LOCK_KEY, TimeUnit.SECONDS, MAX_TIMES);
      Validate.isTrue(hasLock, "系统网络繁忙，请稍后重试");
      CostBudgetVo costBudgetVo = costBudgetVoService.findByCode(costBudgetCode);
      Validate.notNull(costBudgetVo, "根据指定的费用预算编码【%s】，未能获取到相应信息",costBudgetCode);
      Validate.isTrue(StringUtils.equals(costBudgetVo.getDelFlag(), DelFlagStatusEnum.NORMAL.getCode()), "不能对【已删除】费用预算进行操作");
      Validate.isTrue(StringUtils.equals(costBudgetVo.getEnableStatus(), EnableStatusEnum.ENABLE.getCode()), "不能对【已禁用】费用预算进行操作");
      //获取同年度，同类型，且有效的费用预算数据
      CostBudgetDto costBudgetDto = new CostBudgetDto();
      costBudgetDto.setType(costBudgetVo.getType());
      costBudgetDto.setYear(costBudgetVo.getYear());
      costBudgetDto.setOrgCode(costBudgetVo.getOrgCode());
      costBudgetDto.setCustomerCode(costBudgetVo.getCustomerCode());
      costBudgetDto.setChannelCode(costBudgetVo.getChannelCode());
      costBudgetDto.setTerminalCode(costBudgetVo.getTerminalCode());
      costBudgetDto.setBudgetSubjectCode(costBudgetVo.getBudgetSubjectCode());
      costBudgetDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
      costBudgetDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      List<CostBudgetVo> costBudgetVos = costBudgetVoService.findByConditions(costBudgetDto);
      Validate.notEmpty(costBudgetVos,"根据指定的费用预算编码【%s】，未获取到同年度，同类型，且有效的费用预算数据，请检查",costBudgetCode);
      List<String> effectiveCostBudgetCodes = costBudgetVos.stream().map(CostBudgetVo::getCode).collect(Collectors.toList());
      Map<String,List<CostBudgetItemVo>> itemsMap = costBudgetItemVoService.findByCostBudgetCodes(effectiveCostBudgetCodes);
      Validate.notEmpty(itemsMap,"根据指定的费用预算编码【%s】，未获取到同年度，同类型，且有效的费用预算明细数据，请检查",costBudgetCode);
      List<ProcessData> processDatas = Lists.newArrayList();
      BigDecimal totalFinalBalance = BigDecimal.ZERO;
      for(Map.Entry<String,List<CostBudgetItemVo>> entry : itemsMap.entrySet()){
        CostBudgetItemVo newestItem = entry.getValue().stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
        Validate.notNull(newestItem, "费用预算【%s】，没有相关费用预算明细，请检查",entry.getKey());
        if(newestItem.getFinalBalance().compareTo(BigDecimal.ZERO) > 0){
          ProcessData processData = new ProcessData(newestItem.getFinalBalance(), newestItem.getCostBudgetCode(), this.buildSortKey(newestItem.getYear(), newestItem.getQuarter(), newestItem.getMonth()));
          processDatas.add(processData);
          totalFinalBalance = totalFinalBalance.add(newestItem.getFinalBalance());
        }
      }
      Validate.notEmpty(processDatas,"根据指定的费用预算编码【%s】，发现同年度，同类型的费用预算可用余额已不足，请检查",costBudgetCode);
      Validate.isTrue(totalFinalBalance.compareTo(BigDecimal.ZERO) > 0,"【%d】年度的费用预算可用余额为0，不能进行费用申请",costBudgetVo.getYear());
      Validate.isTrue(totalFinalBalance.compareTo(operateAmount) >= 0,"费用申请金额【%s】，【%d】年度的总费用预算可用余额【%s】不足",operateAmount.toString(),costBudgetVo.getYear(),totalFinalBalance.toString());
      processDatas = processDatas.stream().sorted(Comparator.comparing(ProcessData::getSortKey)).collect(Collectors.toList());
      this.processForward(processDatas,costBudgetCode,businessCode,businessItemCode,operateAmount,itemRemark,source,this.buildSortKey(costBudgetVo.getYear(),costBudgetVo.getQuarter(),costBudgetVo.getMonth()));
    }finally {
      if(hasLock){
        redisMutexService.unlock(BUDGET_GLOBAL_REDIS_LOCK_KEY);
      }
    }
  }

  /**
   * 依次按照顺序执行处理操作金额（扣减）
   */
  private BigDecimal processCostBudgetItems(List<ProcessData> items, BigDecimal operateAmount, String businessCode, String businessItemCode, String itemRemark, String source){
    BigDecimal amount = operateAmount;
    for(ProcessData item : items){
      if(amount.compareTo(BigDecimal.ZERO) == 0){
        return BigDecimal.ZERO;
      }
      if(item.getFinalBalance().compareTo(amount) >= 0){
        this.processCostBudgetItem(item,amount,businessCode,businessItemCode,itemRemark,source);
        return BigDecimal.ZERO;
      }else{
        this.processCostBudgetItem(item,item.getFinalBalance(),businessCode,businessItemCode,itemRemark,source);
        amount = amount.subtract(item.getFinalBalance());
      }
    }
    return amount;
  }

  /**
   * 依次按照顺序执行处理操作金额（回退）
   */
  private BigDecimal processCostBudgetItemsReverse(List<ProcessData> items, BigDecimal operateAmount, String businessCode, String businessItemCode, String itemRemark, String source){
    BigDecimal amount = operateAmount;
    for(ProcessData item : items){
      if(amount.compareTo(BigDecimal.ZERO) == 0){
        return amount;
      }
      if(item.getCurrentOperateAmount().compareTo(amount) >= 0){
        this.processCostBudgetItemReverse(item,amount,businessCode,businessItemCode,itemRemark,source);
        return BigDecimal.ZERO;
      }else{
        this.processCostBudgetItemReverse(item,item.getCurrentOperateAmount(),businessCode,businessItemCode,itemRemark,source);
        amount = amount.subtract(item.getCurrentOperateAmount());
      }
    }
    return amount;
  }

  /**
   * 处理扣减费用预算明细
   */
  private void processCostBudgetItem(ProcessData item, BigDecimal operateAmount, String businessCode, String businessItemCode, String itemRemark,String source){
    CostBudgetItemDto itemDto = new CostBudgetItemDto();
    itemDto.setOperateType(CostBudgetOperateType.USED.getCode());
    itemDto.setBalance(item.getFinalBalance());
    itemDto.setCostBudgetCode(item.getCostBudgetCode());
    itemDto.setFinalBalance(item.getFinalBalance().subtract(operateAmount));
    itemDto.setOperateAmount(operateAmount);
    itemDto.setTenantCode(TenantUtils.getTenantCode());
    itemDto.setBusinessCode(businessCode);
    itemDto.setBusinessItemCode(businessItemCode);
    itemDto.setItemRemark(itemRemark);
    itemDto.setSource(source);
    costBudgetItemVoService.create(Lists.newArrayList(itemDto));
  }

  /**
   * 处理回退费用预算明细
   */
  private void processCostBudgetItemReverse(ProcessData item, BigDecimal operateAmount, String businessCode, String businessItemCode, String itemRemark, String source){
    CostBudgetItemDto itemDto = new CostBudgetItemDto();
    itemDto.setOperateType(CostBudgetOperateType.BACK.getCode());
    itemDto.setBalance(item.getFinalBalance());
    itemDto.setCostBudgetCode(item.getCostBudgetCode());
    itemDto.setFinalBalance(item.getFinalBalance().add(operateAmount));
    itemDto.setOperateAmount(operateAmount);
    itemDto.setTenantCode(TenantUtils.getTenantCode());
    itemDto.setBusinessCode(businessCode);
    itemDto.setBusinessItemCode(businessItemCode);
    itemDto.setItemRemark(itemRemark);
    itemDto.setSource(source);
    costBudgetItemVoService.create(Lists.newArrayList(itemDto));
  }

  @Override
  public void reverse(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source) {
    Validate.notBlank(businessCode, "业务编号不能为空");
    Validate.notBlank(businessItemCode, "业务明细编号不能为空");
    Validate.notBlank(costBudgetCode, "费用预算编码不能为空");
    Validate.notBlank(source, "费用预算明细来源不能为空");
    Validate.notNull(operateAmount, "操作金额不能为空");
    Validate.isTrue(CostBudgetItemSourceType.contains(source),"未知的费用预算明细来源【%s】，请检查",source);
    Validate.isTrue(operateAmount.compareTo(BigDecimal.ZERO) > 0, "操作金额必须大于0");

    //需要进行redis锁操作
    boolean hasLock = false;
    try {
      hasLock = redisMutexService.tryLock(BUDGET_GLOBAL_REDIS_LOCK_KEY, TimeUnit.SECONDS, MAX_TIMES);
      Validate.isTrue(hasLock, "系统网络繁忙，请稍后重试");
      CostBudgetVo costBudgetVo = costBudgetVoService.findByCode(costBudgetCode);
      Validate.notNull(costBudgetVo, "根据指定的费用预算编码【%s】，未能获取到相应信息",costBudgetCode);
      Validate.isTrue(StringUtils.equals(costBudgetVo.getDelFlag(), DelFlagStatusEnum.NORMAL.getCode()), "不能对【已删除】费用预算进行操作");
      Validate.isTrue(StringUtils.equals(costBudgetVo.getEnableStatus(), EnableStatusEnum.ENABLE.getCode()), "不能对【已禁用】费用预算进行操作");
      //获取同年度，同类型，且有效的费用预算数据
      CostBudgetDto costBudgetDto = new CostBudgetDto();
      costBudgetDto.setType(costBudgetVo.getType());
      costBudgetDto.setYear(costBudgetVo.getYear());
      costBudgetDto.setOrgCode(costBudgetVo.getOrgCode());
      costBudgetDto.setCustomerCode(costBudgetVo.getCustomerCode());
      costBudgetDto.setChannelCode(costBudgetVo.getChannelCode());
      costBudgetDto.setTerminalCode(costBudgetVo.getTerminalCode());
      costBudgetDto.setBudgetSubjectCode(costBudgetVo.getBudgetSubjectCode());
      costBudgetDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
      costBudgetDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      List<CostBudgetVo> costBudgetVos = costBudgetVoService.findByConditions(costBudgetDto);
      Validate.notEmpty(costBudgetVos,"根据指定的费用预算编码【%s】，未获取到同年度，同类型，且有效的费用预算数据，请检查",costBudgetCode);
      List<String> effectiveCostBudgetCodes = costBudgetVos.stream().map(CostBudgetVo::getCode).collect(Collectors.toList());
      Map<String,List<CostBudgetItemVo>> itemsMap = costBudgetItemVoService.findByCostBudgetCodes(effectiveCostBudgetCodes);
      Validate.notEmpty(itemsMap,"根据指定的费用预算编码【%s】，未获取到同年度，同类型，且有效的费用预算明细数据，请检查",costBudgetCode);

      List<ProcessData> processDatas = Lists.newArrayList();
      for(Map.Entry<String,List<CostBudgetItemVo>> entry : itemsMap.entrySet()){
        boolean hasBusinessData = entry.getValue().stream().anyMatch(e -> StringUtils.equals(e.getBusinessItemCode(),businessItemCode));
        if(!hasBusinessData){
          continue;
        }
        //提取本entry中的最新费用预算明细信息，获取最新的可用余额
        CostBudgetItemVo newestItem = entry.getValue().stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
        Validate.notNull(newestItem,"费用预算编码【%s】，不存在最新的费用预算明细信息，请检查",entry.getKey());
        //提取具有该业务性质的数据(使用状态的)，并提取最新的一条信息(因为可能含有分摊信息，所以需要分组再提取最新)
        CostBudgetItemVo effectiveItem = entry.getValue().stream().filter(e -> StringUtils.equals(e.getBusinessItemCode(),businessItemCode) &&
                e.getOperateType().equals(CostBudgetOperateType.USED.getCode())).max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
        Validate.notNull(effectiveItem,"费用预算编码【%s】，不存在业务明细编码为【%s】【已使用】信息，请检查",entry.getKey(),businessItemCode);
        ProcessData processData = new ProcessData(newestItem.getFinalBalance(), entry.getKey(), this.buildSortKey(effectiveItem.getYear(), effectiveItem.getQuarter(), effectiveItem.getMonth()), effectiveItem.getOperateAmount());
        processDatas.add(processData);
      }
      if(!CollectionUtils.isEmpty(processDatas)){
        processDatas = processDatas.stream().sorted(Comparator.comparing(ProcessData::getSortKey).reversed()).collect(Collectors.toList());
        this.processReverse(processDatas,costBudgetCode,businessCode,businessItemCode,operateAmount,itemRemark,source,this.buildSortKey(costBudgetVo.getYear(),costBudgetVo.getQuarter(),costBudgetVo.getMonth()));
      }else{
        List<CostBudgetItemVo> costBudgetItemVos = itemsMap.get(costBudgetCode);
        CostBudgetItemVo newestItem = costBudgetItemVos.stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
        Validate.notNull(newestItem,"费用预算编码【%s】，不存在最新的费用预算明细信息，请检查",costBudgetCode);
        ProcessData processData = new ProcessData(newestItem.getFinalBalance(),costBudgetCode,this.buildSortKey(costBudgetVo.getYear(),costBudgetVo.getQuarter(),costBudgetVo.getMonth()),operateAmount);
        this.processCostBudgetItemReverse(processData,operateAmount,businessCode,businessItemCode,itemRemark,source);
      }
    }finally {
      if(hasLock){
        redisMutexService.unlock(BUDGET_GLOBAL_REDIS_LOCK_KEY);
      }
    }
  }

  private String buildSortKey(Integer year, Integer quarter, Integer month){
    if(month >= 10){
      return StringUtils.join(year,quarter,month);
    }else{
      return StringUtils.join(year,quarter,"0",month);
    }
  }

  private void processForward(List<ProcessData> processDatas, String costBudgetCode, String businessCode, String businessItemCode, BigDecimal operateAmount, String itemRemark, String source, String time){
    //1.查看本年当月的费用预算可用余额是否足够，如果足够，则直接占用并退出
    ProcessData currentMonthProcessData = processDatas.stream().filter(e -> StringUtils.equals(costBudgetCode,e.getCostBudgetCode())).findFirst().orElse(null);
    if(currentMonthProcessData != null){
      if(currentMonthProcessData.getFinalBalance().compareTo(operateAmount) >= 0){
        this.processCostBudgetItem(currentMonthProcessData,operateAmount,businessCode,businessItemCode,itemRemark,source);
        return;
      }else{
        this.processCostBudgetItem(currentMonthProcessData,currentMonthProcessData.getFinalBalance(),businessCode,businessItemCode,itemRemark,source);
        operateAmount = operateAmount.subtract(currentMonthProcessData.getFinalBalance());
      }
    }
    //2.依次查看本年当月之后的费用预算可用余额是否满足，直到扣减为0
    List<ProcessData> afterCurrentMonthProcessDatas = processDatas.stream().filter(e -> StringUtils.compare(e.getSortKey(),time) > 0).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(afterCurrentMonthProcessDatas)){
      operateAmount = this.processCostBudgetItems(afterCurrentMonthProcessDatas,operateAmount,businessCode,businessItemCode,itemRemark,source);
      if(operateAmount.compareTo(BigDecimal.ZERO) == 0){
        return;
      }
    }
    //3.依次查看本年当月之前的费用预算可用余额是否满足，直到扣减为0
    List<ProcessData> beforeCurrentMonthProcessDatas = processDatas.stream().filter(e -> StringUtils.compare(e.getSortKey(),time) < 0).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(beforeCurrentMonthProcessDatas)){
      operateAmount = this.processCostBudgetItems(beforeCurrentMonthProcessDatas,operateAmount,businessCode,businessItemCode,itemRemark,source);
      //执行到这里时，一般不会执行到此处，如果此处抛错，则一定时累计扣减的时候有问题
      Validate.isTrue(operateAmount.compareTo(BigDecimal.ZERO) == 0,"执行预算费用扣减时，发现本年度可用余额不足，请检查");
    }
  }

  private void processReverse(List<ProcessData> processDatas, String costBudgetCode, String businessCode, String businessItemCode, BigDecimal operateAmount, String itemRemark, String source, String time){
    //1.依次查看本年当月之前的费用预算可用余额是否满足，直到扣减为0
    List<ProcessData> beforeCurrentMonthProcessDatas = processDatas.stream().filter(e -> StringUtils.compare(e.getSortKey(),time) < 0).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(beforeCurrentMonthProcessDatas)){
      operateAmount = this.processCostBudgetItemsReverse(beforeCurrentMonthProcessDatas,operateAmount,businessCode,businessItemCode,itemRemark,source);
      if(operateAmount.compareTo(BigDecimal.ZERO) == 0){
        return;
      }
    }

    //2.依次查看本年当月之后的费用预算可用余额是否满足，直到扣减为0
    List<ProcessData> afterCurrentMonthProcessDatas = processDatas.stream().filter(e -> StringUtils.compare(e.getSortKey(),time) > 0).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(afterCurrentMonthProcessDatas)){
      operateAmount = this.processCostBudgetItemsReverse(afterCurrentMonthProcessDatas,operateAmount,businessCode,businessItemCode,itemRemark,source);
      if(operateAmount.compareTo(BigDecimal.ZERO) == 0){
        return;
      }
    }
    //3.查看本年当月的费用预算可用余额是否足够，如果足够，则直接占用并退出
    ProcessData currentMonthProcessData = processDatas.stream().filter(e -> StringUtils.equals(costBudgetCode,e.getCostBudgetCode())).findFirst().orElse(null);
    if(currentMonthProcessData != null){
      Validate.isTrue(currentMonthProcessData.getCurrentOperateAmount().compareTo(operateAmount) >= 0,"本年度当前费用预算明细的使用金额与当前的操作金额不一致，请检查");
      this.processCostBudgetItemReverse(currentMonthProcessData,operateAmount,businessCode,businessItemCode,itemRemark,source);
    }
  }


  static class ProcessData{
    private BigDecimal finalBalance;

    private String costBudgetCode;

    private String sortKey;

    private BigDecimal currentOperateAmount;

    public ProcessData(BigDecimal finalBalance, String costBudgetCode, String sortKey) {
      this.finalBalance = finalBalance;
      this.costBudgetCode = costBudgetCode;
      this.sortKey = sortKey;
    }

    public ProcessData(BigDecimal finalBalance, String costBudgetCode, String sortKey, BigDecimal currentOperateAmount) {
      this.finalBalance = finalBalance;
      this.costBudgetCode = costBudgetCode;
      this.sortKey = sortKey;
      this.currentOperateAmount = currentOperateAmount;
    }

    public BigDecimal getFinalBalance() {
      return finalBalance;
    }

    public String getCostBudgetCode() {
      return costBudgetCode;
    }

    public String getSortKey() {
      return sortKey;
    }

    public BigDecimal getCurrentOperateAmount() {
      return currentOperateAmount;
    }
  }
}
