package com.biz.crm.tpm.business.control.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_budget_control_range")
@Table(name = "tpm_budget_control_range",
        indexes = {
                @Index(name = "tpm_budget_control_range_idx1", columnList = "control_code",unique = false),
                @Index(name = "tpm_budget_control_range_idx2", columnList = "control_detail_code",unique = false),
                @Index(name = "tpm_budget_control_range_idx3", columnList = "department_code",unique = false),
                @Index(name = "tpm_budget_control_range_idx4", columnList = "customer_code",unique = false),
        })
@ApiModel(value = "BudgetControlRange", description = "预算管控范围")
@org.hibernate.annotations.Table(appliesTo = "tpm_budget_control_range", comment = "预算管控范围")
public class BudgetControlRange extends TenantFlagOpEntity {

  @ApiModelProperty("预算管控编码")
  @Column(name = "control_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '预算管控编码'")
  private String controlCode;

  @ApiModelProperty("管控部门明细编码")
  @Column(name = "control_detail_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '管控部门明细编码 '")
  private String controlDetailCode;

  @ApiModelProperty("范围类型")
  @Column(name = "range_type", columnDefinition = "VARCHAR(32) COMMENT '范围类型'")
  private String rangeType;

  @ApiModelProperty("部门编码")
  @Column(name = "department_code", columnDefinition = "VARCHAR(32) COMMENT '部门编码'")
  private String departmentCode;

  @ApiModelProperty("部门名称")
  @Column(name = "department_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '部门名称'")
  private String departmentName;

  @ApiModelProperty("部门层级")
  @Column(name = "level_num", columnDefinition = "int COMMENT '部门层级'")
  private Integer levelNum;

  @ApiModelProperty("客户编码")
  @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
  private String customerCode;

  @ApiModelProperty("客户名称")
  @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
  private String customerName;

}
