package com.biz.crm.tpm.business.budget.local.service.internal;

import com.biz.crm.tpm.business.budget.sdk.service.MarketingAuditComponentService;
import com.biz.crm.tpm.business.pay.sdk.service.MarketingAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MarketingAuditComponentServiceImpl implements MarketingAuditComponentService {

    @Autowired(required = false)
    private MarketingAuditService marketingAuditService;

    @Override
    public void autoAuditPlanCasePass(List<String> schemeDetailCodes) {
        marketingAuditService.autoAuditPlanCasePass(schemeDetailCodes);
    }
}
