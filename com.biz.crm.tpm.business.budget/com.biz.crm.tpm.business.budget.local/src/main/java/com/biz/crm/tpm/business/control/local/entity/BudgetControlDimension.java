package com.biz.crm.tpm.business.control.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BudgetControlDimension", description = "预算管控维度")
@TableName("tpm_budget_control_dimension")
@Table(name = "tpm_budget_control_dimension",
        indexes = {
                @Index(name = "budget_control_dimension_idx1", columnList = "control_code"),
        })
@Entity
public class BudgetControlDimension extends TenantFlagOpEntity {

  @ApiModelProperty("预算管控编码")
  @Column(name = "control_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '预算管控编码'")
  private String controlCode;

  @ApiModelProperty("维度编码")
  @Column(name = "dimension_code", columnDefinition = "VARCHAR(32) COMMENT '维度编码'")
  private String dimensionCode;

  @ApiModelProperty("维度名称")
  @Column(name = "dimension_name", columnDefinition = "VARCHAR(32) COMMENT '维度名称'")
  private String dimensionName;
}
