package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_cost_budget_income")
@Table(name = "tpm_cost_budget_income",indexes = {
        @Index(name = "tpm_cost_budget_income_idx1", columnList = "year_month_ly"),
        @Index(name = "tpm_cost_budget_income_idx2", columnList = "department_one_code"),
        @Index(name = "tpm_cost_budget_income_idx3", columnList = "product_code"),
})
@ApiModel(value = "CostBudgetIncome", description = "收入预算")
@org.hibernate.annotations.Table(appliesTo = "tpm_cost_budget_income", comment = "收入预算")
public class CostBudgetIncome extends TenantFlagOpEntity {

  @ApiModelProperty("编码")
  @Column(name = "income_code", length = 64, columnDefinition = "varchar(64) COMMENT '编码'")
  private String incomeCode;

  @ApiModelProperty("组织编码")
  @TableField(value = "org_code")
  @Column(name = "org_code", length = 64, columnDefinition = "varchar(64) COMMENT '组织编码'")
  private String orgCode;

  @ApiModelProperty("组织名称")
  @TableField(value = "org_name")
  @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
  private String orgName;

  @ApiModelProperty("渠道编码")
  @TableField(value = "channel_code")
  @Column(name = "channel_code", length = 64, columnDefinition = "varchar(64) COMMENT '渠道编码'")
  private String channelCode;

  @ApiModelProperty("渠道名称")
  @TableField(value = "channel_name")
  @Column(name = "channel_name", columnDefinition = "varchar(255) COMMENT '渠道名称'")
  private String channelName;

  @ApiModelProperty("客户编码")
  @TableField(value = "customer_code")
  @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
  private String customerCode;

  @ApiModelProperty("客户名称")
  @TableField(value = "customer_name")
  @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
  private String customerName;

  @ApiModelProperty("产品编码")
  @TableField(value = "product_code")
  @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
  private String productCode;

  @ApiModelProperty("产品名称")
  @TableField(value = "product_name")
  @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
  private String productName;

  @ApiModelProperty("数量")
  @TableField(value = "quantity")
  @Column(name = "quantity", columnDefinition = "decimal(20,4) COMMENT '数量'")
  private BigDecimal quantity;

  @ApiModelProperty("收入金额")
  @TableField(value = "income_amount")
  @Column(name = "income_amount", nullable = false, columnDefinition = "decimal(20,4) COMMENT '收入金额'")
  private BigDecimal incomeAmount;

  @ApiModelProperty("成本")
  @Column(name = "cost_amount", columnDefinition = "decimal(20,4) COMMENT '成本'")
  private BigDecimal costAmount;

  @ApiModelProperty("物流费")
  @TableField(value = "logistics_amount")
  @Column(name = "logistics_amount", columnDefinition = "decimal(20,4) COMMENT '物流费'")
  private BigDecimal logisticsAmount;



  @ApiModelProperty("年月")
  @Column(name = "year_month_ly", length = 32, columnDefinition = "VARCHAR(32) COMMENT '年月'")
  private String yearMonthLy;

  @ApiModelProperty("确认状态")
  @Column(name = "confirm_status", length = 64, columnDefinition = "varchar(64) COMMENT '确认状态'")
  private String confirmStatus;

  @ApiModelProperty("事业部编码")
  @Column(name = "division_code", columnDefinition = "VARCHAR(32) COMMENT '事业部编码'")
  private String divisionCode;

  @ApiModelProperty("事业部名称")
  @Column(name = "division_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '事业部名称'")
  private String divisionName;

  @ApiModelProperty("中心编码")
  @Column(name = "center_code", columnDefinition = "VARCHAR(32) COMMENT '中心编码'")
  private String centerCode;

  @ApiModelProperty("中心名称")
  @Column(name = "center_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '中心名称'")
  private String centerName;

  @ApiModelProperty("一级部门编码")
  @Column(name = "department_one_code", columnDefinition = "VARCHAR(32) COMMENT '一级部门编码'")
  private String departmentOneCode;

  @ApiModelProperty("一级部门名称")
  @Column(name = "department_one_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '一级部门名称'")
  private String departmentOneName;

  @ApiModelProperty("二级部门编码")
  @Column(name = "department_two_code", columnDefinition = "VARCHAR(32) COMMENT '二级部门编码'")
  private String departmentTwoCode;

  @ApiModelProperty("二级部门名称")
  @Column(name = "department_two_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '二级部门名称'")
  private String departmentTwoName;

  @ApiModelProperty("三级部门编码")
  @Column(name = "department_three_code", columnDefinition = "VARCHAR(32) COMMENT '三级部门编码'")
  private String departmentThreeCode;

  @ApiModelProperty("三级部门名称")
  @Column(name = "department_three_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '三级部门名称'")
  private String departmentThreeName;

  @ApiModelProperty("部门层级")
  @Column(name = "level_num", columnDefinition = "int COMMENT '部门层级'")
  private Integer levelNum;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "VARCHAR(32) COMMENT '公司编码'")
  private String companyCode;

  @ApiModelProperty("公司名称")
  @Column(name = "company_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '公司名称'")
  private String companyName;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "VARCHAR(32) COMMENT '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '成本中心名称'")
  private String costCenterName;

  @ApiModelProperty("唯一标识")
  @Column(name = "unique_key", columnDefinition = "VARCHAR(128) COMMENT '唯一标识'")
  private String uniqueKey;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("关联业务编码")
  @Column(name = "business_code", length = 64, columnDefinition = "varchar(64) COMMENT '关联业务编码'")
  private String businessCode;

  /**
   * 客户ERP编码
   */
  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", length = 32, columnDefinition = "varchar(32) COMMENT '客户ERP编码'")
  private String erpCode;

  /**
   * 产品组编码
   */
  @ApiModelProperty("产品组编码")
  @Column(name = "product_group_code", length = 32, columnDefinition = "varchar(32) COMMENT '产品组编码'")
  private String productGroupCode;

  @ApiModelProperty("渠道部门")
  @Column(name = "channel_department_code", length = 32, columnDefinition = "varchar(32) COMMENT '渠道部门'")
  private String channelDepartmentCode;

  @Transient
  @ApiModelProperty("未税")
  @TableField(exist = false)
  private BigDecimal noTaxIncomeAmount;

}
