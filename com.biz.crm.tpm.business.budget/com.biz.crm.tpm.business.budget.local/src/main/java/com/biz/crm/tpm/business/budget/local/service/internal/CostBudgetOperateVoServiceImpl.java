package com.biz.crm.tpm.business.budget.local.service.internal;

import com.biz.crm.tpm.business.budget.local.entity.CostBudget;
import com.biz.crm.tpm.business.budget.local.repository.CostBudgetRepository;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetItemDto;
import com.biz.crm.tpm.business.budget.sdk.dto.OperateBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetOperateType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetItemVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetOperateVoService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CostBudgetOperateVoServiceImpl implements CostBudgetOperateVoService {

    @Autowired
    private CostBudgetRepository costBudgetRepository;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired
    private CostBudgetItemVoService costBudgetItemService;

    /**
     * 批量预算调整
     *
     * @param operateList
     */
    @Override
    public void operateBudget(List<OperateBudgetDto> operateList) {
        this.operateBudget(operateList, true);
    }

    @Override
    public void operateBudget(List<OperateBudgetDto> operateList, boolean checkOccupy) {
        Map<String, CostBudget> budgetEntityMap = validateOperateBudget(operateList);
        List<CostBudgetItemDto> detailList = Lists.newArrayList();
        for (OperateBudgetDto operateBudgetDto : operateList) {
            CostBudget budgetEntity = budgetEntityMap.get(operateBudgetDto.getBudgetCode());
            if (checkOccupy) {
                Validate.isTrue(StringUtils.isBlank(budgetEntity.getBusinessCode())
                        || budgetEntity.getBusinessCode().equals(operateBudgetDto.getBusinessCode()),
                    "预算【%s】被【%s】占用，请检查", budgetEntity.getCode(), budgetEntity.getBusinessCode());
            }
            BigDecimal balance = budgetEntity.getMonthBalanceAmount();
            String operationType = operateBudgetDto.getOperationType();
            BigDecimal operationAmount = operateBudgetDto.getOperationAmount().abs();
            if (CostBudgetOperateType.FREEZE.getCode().equals(operationType)) {
                Validate.isTrue(operationAmount.compareTo(budgetEntity.getMonthBalanceAmount()) <= 0, "预算【%s】余额不足，请检查", budgetEntity.getCode());
                budgetEntity.setFreezeAmount(operationAmount.add(Optional.ofNullable(budgetEntity.getFreezeAmount()).orElse(BigDecimal.ZERO)));
                budgetEntity.setBusinessCode(operateBudgetDto.getBusinessCode());
            } else if (CostBudgetOperateType.UNFREEZE.getCode().equals(operationType)) {
                budgetEntity.setFreezeAmount(Optional.ofNullable(budgetEntity.getFreezeAmount()).orElse(BigDecimal.ZERO).subtract(operationAmount));
                budgetEntity.setBusinessCode("");
            } else if (CostBudgetOperateType.APPEND.getCode().equals(operationType) || CostBudgetOperateType.TRANSFER_IN.getCode().equals(operationType)) {
                budgetEntity.setAdjustAmount(Optional.ofNullable(budgetEntity.getAdjustAmount()).orElse(BigDecimal.ZERO).add(operationAmount));
                budgetEntity.setBusinessCode("");
            } else if (CostBudgetOperateType.REDUCE.getCode().equals(operationType) || CostBudgetOperateType.TRANSFER_OUT.getCode().equals(operationType)) {
                budgetEntity.setFreezeAmount(Optional.ofNullable(budgetEntity.getFreezeAmount()).orElse(BigDecimal.ZERO).subtract(operationAmount));
                budgetEntity.setAdjustAmount(Optional.ofNullable(budgetEntity.getAdjustAmount()).orElse(BigDecimal.ZERO).subtract(operationAmount));
                budgetEntity.setBusinessCode("");
            } else if (CostBudgetOperateType.TRANSFER_IN_LOCK.getCode().equals(operationType)) {
                budgetEntity.setBusinessCode(operateBudgetDto.getBusinessCode());
            } else if (CostBudgetOperateType.TRANSFER_IN_UNLOCK.getCode().equals(operationType)) {
                budgetEntity.setBusinessCode("");
            } else {
                throw new RuntimeException("月度预算操作类型有误！");
            }
            budgetEntity.refreshBalanceAmount();
            // 构建操作明细
            CostBudgetItemDto budgetDetailDto = this.buildDetail(budgetEntity, operationAmount, operationType, balance, operateBudgetDto.getRemark());
            if (budgetDetailDto != null) {
                budgetDetailDto.setBusinessCode(operateBudgetDto.getBusinessCode());
                detailList.add(budgetDetailDto);
            }
        }
        costBudgetRepository.saveOrUpdateBatch(budgetEntityMap.values());
        if (CollectionUtils.isNotEmpty(detailList)) {
            costBudgetItemService.create(detailList);
        }
    }

    /**
     * 校验及获取对应预算
     * 
     * @param operateList
     * @return
     */
    public Map<String, CostBudget> validateOperateBudget(List<OperateBudgetDto> operateList) {
        for (OperateBudgetDto operateBudgetDto : operateList) {
            Validate.notEmpty(operateBudgetDto.getBudgetCode(), "操作预算时，预算编码不能为空！");
            Validate.notNull(operateBudgetDto.getOperationAmount(), "操作预算时，操作金额不能为空！");
            Validate.notEmpty(operateBudgetDto.getOperationType(), "操作预算时，操作类型不能为空！");
        }

        Set<String> budgetCodeList = operateList.stream().map(OperateBudgetDto::getBudgetCode).collect(Collectors.toSet());
        List<CostBudget> budgetEntityList = costBudgetRepository.findByCodesAndTenantCode(budgetCodeList, TenantUtils.getTenantCode());
        if (budgetEntityList.size() < budgetCodeList.size()) {
            List<String> existsCodes = budgetEntityList.stream().map(CostBudget::getCode).collect(Collectors.toList());
            String notExistsJoinCodesStr = budgetCodeList.stream().filter(item -> !existsCodes.contains(item)).collect(Collectors.joining(","));
            throw new RuntimeException("预算操作失败，预算[" + notExistsJoinCodesStr + "]查询失败，请检查预算是否确认或是否存在！！");
        }
        return budgetEntityList.stream().collect(Collectors.toMap(CostBudget::getCode, Function.identity()));
    }

    /**
     * 构造费用预算期初明细
     *
     * @param
     */
    private CostBudgetItemDto buildDetail(CostBudget budgetEntity, BigDecimal operationAmount, String operationType, BigDecimal balance, String remark) {
        CostBudgetItemDto itemDto = new CostBudgetItemDto();
        if (CostBudgetOperateType.TRANSFER_IN_LOCK.getCode().equals(operationType)) {
            return null;
        }
        itemDto.setOperateType(operationType);
        itemDto.setBalance(balance);
        itemDto.setCostBudgetCode(budgetEntity.getCode());
        itemDto.setInitialAmount(budgetEntity.getInitialAmount());
        itemDto.setFinalBalance(budgetEntity.getMonthBalanceAmount());
        itemDto.setOperateAmount(operationAmount);
        itemDto.setTenantCode(budgetEntity.getTenantCode());
        itemDto.setBusinessCode(budgetEntity.getCode());
        itemDto.setSource(CostBudgetItemSourceType.ADJUST.getDescr());
        itemDto.setCustomerCode(budgetEntity.getCustomerCode());
        itemDto.setCustomerName(budgetEntity.getCustomerName());
        itemDto.setBudgetSubjectCode(budgetEntity.getBudgetSubjectCode());
        itemDto.setBudgetSubjectName(budgetEntity.getBudgetSubjectName());
        itemDto.setItemRemark(remark);
        itemDto.setDepartmentCode(budgetEntity.getDepartmentOneCode());
        itemDto.setDepartmentName(budgetEntity.getDepartmentOneName());

        return itemDto;
    }
}
