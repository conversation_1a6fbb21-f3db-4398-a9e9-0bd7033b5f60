package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_cost_budget_item")
@Table(name = "tpm_cost_budget_item", indexes = {
        @Index(name = "cost_budget_item_idx1", columnList = "business_code"),
})
@ApiModel(value = "CostBudgetItem", description = "费用预算明细")
@org.hibernate.annotations.Table(appliesTo = "tpm_cost_budget_item", comment = "费用预算明细")
public class CostBudgetItem extends TenantOpEntity {

    @ApiModelProperty("费用预算编码")
    @TableField(value = "cost_budget_code", fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
    @Column(name = "cost_budget_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '费用预算编码'")
    private String costBudgetCode;

    /**
     * 参见com.biz.tpm.business.budget.sdk.enums.CostBudgetOperateType
     */
    @ApiModelProperty("费用预算操作类型")
    @TableField(value = "operate_type")
    @Column(name = "operate_type", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '费用预算操作类型'")
    private String operateType;

    @ApiModelProperty("业务编码")
    @TableField(value = "business_code")
    @Column(name = "business_code", length = 64, columnDefinition = "varchar(64) COMMENT '业务编码'")
    private String businessCode;

    @ApiModelProperty("业务明细编码")
    @TableField(value = "business_item_code")
    @Column(name = "business_item_code", length = 64, columnDefinition = "varchar(64) COMMENT '业务明细编码'")
    private String businessItemCode;

    @ApiModelProperty("操作前余额")
    @TableField(value = "balance")
    @Column(name = "balance", nullable = false, columnDefinition = "decimal(20,4) COMMENT '操作前余额'")
    private BigDecimal balance;

    @ApiModelProperty("最终可用余额")
    @TableField(value = "final_balance")
    @Column(name = "final_balance", nullable = false, columnDefinition = "decimal(20,4) COMMENT '最终可用余额'")
    private BigDecimal finalBalance;

    @ApiModelProperty("当前操作金额")
    @TableField(value = "operate_amount")
    @Column(name = "operate_amount", nullable = false, columnDefinition = "decimal(20,4) COMMENT '当前操作金额'")
    private BigDecimal operateAmount;

    @ApiModelProperty("来源")
    @TableField(value = "source")
    @Column(name = "source", columnDefinition = "varchar(64) COMMENT '来源'")
    private String source;

    @ApiModelProperty("备注")
    @TableField(value = "item_remark")
    @Column(name = "item_remark", columnDefinition = "varchar(255) COMMENT '备注'")
    private String itemRemark;

    @ApiModelProperty("排序值")
    @TableField(value = "sort_index")
    @Column(name = "sort_index", columnDefinition = "int(4) COMMENT '排序值'")
    private Integer sortIndex;


    @ApiModelProperty("期初金额")
    @Column(name = "initial_amount", columnDefinition = "decimal(20,4) COMMENT '期初金额'")
    private BigDecimal initialAmount;

    @ApiModelProperty("预算科目编码")
    @Column(name = "budget_subject_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算科目编码'")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    @Column(name = "budget_subject_name", columnDefinition = "varchar(255) COMMENT '预算科目名称'")
    private String budgetSubjectName;

    @ApiModelProperty("费用使用部门编码")
    @Column(name = "department_code", columnDefinition = "VARCHAR(32) COMMENT '费用使用部门编码'")
    private String departmentCode;

    @ApiModelProperty("费用使用部门名称")
    @Column(name = "department_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '费用使用部门名称'")
    private String departmentName;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
    private String customerName;

    @ApiModelProperty("是否直接属于预算费用")
    @Transient
    @TableField(exist = false)
    private String database;

    @Transient
    @TableField(exist = false)
    private String schemeType;

    @Transient
    @TableField(exist = false)
    private String processStatus;

    @Transient
    @TableField(exist = false)
    private String originalSchemeDetailCode;
}
