package com.biz.crm.tpm.business.budget.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.local.entity.CostBudget;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用预算(CostBudget)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
public interface CostBudgetMapper extends BaseMapper<CostBudget> {

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param dto  查询实体
     * @return 所有数据
     */
    Page<CostBudgetVo> findByConditions(@Param("page") Page<CostBudgetVo> page, @Param("dto") CostBudgetDto dto);

    /**
     * 根据主键查询详情
     */
    CostBudgetVo findDetailsById(@Param("id") String id);

    /**
     * 根据编码查询详情
     */
    CostBudgetVo findDetailsByCode(@Param("code") String code);


    /**
     * 按条件查询
     *
     * @param dto
     * @return
     */
    List<CostBudgetVo> findByDto(@Param("dto") CostBudgetDto dto);


    /**
     * 按条件查询
     *
     * @param dto
     * @return
     */
    List<CostBudgetVo> findBudgetCodeByDto(@Param("dto") CostBudgetDto dto);

    /**
     * 通过条件查询费用预算编码
     *
     * @param dto
     * @param tenantCode
     * @return
     */
    String findCodeByConditions(@Param("dto") CostBudgetDto dto, @Param("tenantCode") String tenantCode);

    List<CostBudgetVo> findListByOrgCodeAndCategoryCodesAndYears(@Param("orgCodes") List<String> orgCodes, @Param("categoryCodes") List<String> categoryCodes,
                                                                 @Param("years") String years, @Param("tenantCode") String tenantCode);

}

