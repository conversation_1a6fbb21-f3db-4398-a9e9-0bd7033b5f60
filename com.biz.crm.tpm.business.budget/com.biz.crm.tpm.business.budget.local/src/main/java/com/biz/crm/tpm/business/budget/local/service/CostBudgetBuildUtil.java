package com.biz.crm.tpm.business.budget.local.service;

import com.biz.crm.business.common.base.service.CostBudgetCalService;
import com.biz.crm.business.common.base.util.ApplicationContextHolder;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/6 20:19
 */
@Slf4j
public class CostBudgetBuildUtil {

    /**
     * 计算预算费用
     *
     * @param dataList
     */
    public static void buildCostBudget(List<CostBudgetVo> dataList) {
        List<String> codes = dataList.stream().map(CostBudgetVo::getCode).collect(Collectors.toList());
        CostBudgetCalService costBudgetCalService = ApplicationContextHolder.getContext().getBean(CostBudgetCalService.class);
        Future<Map<String, BigDecimal>> future = costBudgetCalService.calCostBudgetAmount(codes);
        Map<String, BigDecimal> map;
        try {
            map = future.get();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        for (CostBudgetVo record : dataList) {
            BigDecimal usedAmount = map.getOrDefault(record.getCode(), BigDecimal.ZERO);
            if (ObjectUtils.isNotEmpty(record.getAdjustBalanceAmount())) {
                record.setMonthBalanceAmount(record.getAdjustBalanceAmount().subtract(usedAmount));
            } else {
                record.setMonthBalanceAmount(usedAmount.negate());
            }
            if (ObjectUtils.isNotEmpty(record.getUsedAmount())) {
                record.setUsedAmount(record.getUsedAmount().add(usedAmount));
            } else {
                record.setUsedAmount(usedAmount);
            }
        }
    }
}
