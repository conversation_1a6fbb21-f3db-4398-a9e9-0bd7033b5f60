package com.biz.crm.tpm.business.budget.local.strategy.controltype;


import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetItemDto;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetOperateType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetItemVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 描述：</br>预算科目控制类型，提供预算不控制
 *
 * <AUTHOR>
 * @date 2022/5/18
 */
@Slf4j
public class DoNotControlTypeStrategy extends AbstractBudgetControlTypeStrategy {

  @Autowired
  private CostBudgetVoService costBudgetVoService;
  @Autowired
  private CostBudgetItemVoService costBudgetItemVoService;
  @Autowired
  private RedisMutexService redisMutexService;

  @Override
  public String getCode() {
    return "DO_NOT";
  }

  @Override
  public String getName() {
    return "不控制";
  }

  @Override
  public void forward(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source) {
    Validate.notBlank(businessCode, "业务编号不能为空");
    Validate.notBlank(businessItemCode, "业务明细编号不能为空");
    Validate.notBlank(costBudgetCode, "费用预算编码不能为空");
    Validate.notBlank(source, "费用预算明细来源不能为空");
    Validate.notNull(operateAmount, "使用金额不能为空");
    Validate.isTrue(CostBudgetItemSourceType.contains(source),"未知的费用预算明细来源【%s】，请检查",source);
    Validate.isTrue(operateAmount.compareTo(BigDecimal.ZERO) > 0, "使用金额必须大于0");

    //需要进行redis锁操作
    boolean hasLock = false;
    try {
      hasLock = redisMutexService.tryLock(BUDGET_GLOBAL_REDIS_LOCK_KEY, TimeUnit.SECONDS, MAX_TIMES);
      Validate.isTrue(hasLock, "系统网络繁忙，请稍后重试");
      CostBudgetVo costBudgetVo = costBudgetVoService.findByCode(costBudgetCode);
      Validate.notNull(costBudgetVo, "根据指定的费用预算编码【%s】，未能获取到相应信息",costBudgetCode);
      Validate.isTrue(StringUtils.equals(costBudgetVo.getDelFlag(), DelFlagStatusEnum.NORMAL.getCode()), "不能对【已删除】费用预算进行操作");
      Validate.isTrue(StringUtils.equals(costBudgetVo.getEnableStatus(), EnableStatusEnum.ENABLE.getCode()), "不能对【已禁用】费用预算进行操作");
      List<CostBudgetItemVo> itemVos = costBudgetItemVoService.findByCostBudgetCode(costBudgetCode);
      Validate.notNull(itemVos, "根据指定的费用预算编码【%s】，未能获取到相应明细项信息",costBudgetCode);
      CostBudgetItemVo item = itemVos.stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
      Validate.notNull(item, "费用预算明细项不能为空");

      //使用操作
      CostBudgetItemDto itemDto = new CostBudgetItemDto();
      itemDto.setOperateType(CostBudgetOperateType.USED.getCode());
      itemDto.setBalance(item.getFinalBalance());
      itemDto.setCostBudgetCode(costBudgetCode);
      itemDto.setFinalBalance(item.getFinalBalance().subtract(operateAmount));
      itemDto.setOperateAmount(operateAmount);
      itemDto.setTenantCode(costBudgetVo.getTenantCode());
      itemDto.setBusinessCode(businessCode);
      itemDto.setItemRemark(itemRemark);
      itemDto.setSource(source);
      itemDto.setBusinessItemCode(businessItemCode);
      costBudgetItemVoService.create(Lists.newArrayList(itemDto));
    }finally {
      if(hasLock){
        redisMutexService.unlock(BUDGET_GLOBAL_REDIS_LOCK_KEY);
      }
    }
  }

  @Override
  public void reverse(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source) {
    Validate.notBlank(businessCode, "业务编号不能为空");
    Validate.notBlank(businessItemCode, "业务明细编号不能为空");
    Validate.notBlank(costBudgetCode, "费用预算编码不能为空");
    Validate.notBlank(source, "费用预算明细来源不能为空");
    Validate.notNull(operateAmount, "操作金额不能为空");
    Validate.isTrue(CostBudgetItemSourceType.contains(source),"未知的费用预算明细来源【%s】，请检查",source);
    Validate.isTrue(operateAmount.compareTo(BigDecimal.ZERO) > 0, "操作金额必须大于0");

    //需要进行redis锁操作
    boolean hasLock = false;
    try {
      hasLock = redisMutexService.tryLock(BUDGET_GLOBAL_REDIS_LOCK_KEY, TimeUnit.SECONDS, MAX_TIMES);
      Validate.isTrue(hasLock, "系统网络繁忙，请稍后重试");
      CostBudgetVo costBudgetVo = costBudgetVoService.findByCode(costBudgetCode);
      Validate.notNull(costBudgetVo, "根据指定的费用预算编码【%s】，未能获取到相应信息",costBudgetCode);
      Validate.isTrue(StringUtils.equals(costBudgetVo.getDelFlag(), DelFlagStatusEnum.NORMAL.getCode()), "不能对【已删除】费用预算进行操作");
      Validate.isTrue(StringUtils.equals(costBudgetVo.getEnableStatus(), EnableStatusEnum.ENABLE.getCode()), "不能对【已禁用】费用预算进行操作");

      //获取预算明细最新信息
      List<CostBudgetItemVo> itemVos = costBudgetItemVoService.findByCostBudgetCode(costBudgetCode);
      Validate.notNull(itemVos, "根据指定的费用预算编码【%s】，未能获取到相应明细项信息",costBudgetCode);
      CostBudgetItemVo item = itemVos.stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
      Validate.notNull(item, "费用预算明细项不能为空");
      //逆向退回操作
      CostBudgetItemDto itemDto = new CostBudgetItemDto();
      itemDto.setOperateType(CostBudgetOperateType.BACK.getCode());
      itemDto.setBalance(item.getFinalBalance());
      itemDto.setCostBudgetCode(costBudgetCode);
      itemDto.setFinalBalance(item.getFinalBalance().add(operateAmount));
      itemDto.setOperateAmount(operateAmount);
      itemDto.setTenantCode(costBudgetVo.getTenantCode());
      itemDto.setBusinessCode(businessCode);
      itemDto.setItemRemark(itemRemark);
      itemDto.setSource(source);
      itemDto.setBusinessItemCode(businessItemCode);
      costBudgetItemVoService.create(Lists.newArrayList(itemDto));
    }finally {
      if(hasLock){
        redisMutexService.unlock(BUDGET_GLOBAL_REDIS_LOCK_KEY);
      }
    }
  }


  @Override
  public int getOrder() {
    return 0;
  }
}
