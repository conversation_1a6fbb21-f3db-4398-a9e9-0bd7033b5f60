package com.biz.crm.tpm.business.budget.local.model;


import com.biz.crm.common.form.sdk.field.annotation.DynamicField;
import com.biz.crm.common.form.sdk.field.annotation.Validate;
import com.biz.crm.common.form.sdk.field.validate.NotBlankValidateStrategy;
import com.biz.crm.common.form.sdk.model.DynamicForm;
import com.biz.crm.common.form.sdk.widget.SimpleDateSelectWidget;
import com.biz.crm.common.form.sdk.widget.SimpleInputWidget;
import com.biz.crm.tpm.business.budget.sdk.model.AbstractDynamicTemplateModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 活动细类申请表单model
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CostTypeDetailApplyFormModel", description = "活动细类申请表单model")
public class TpmMeetingActivityModel extends AbstractDynamicTemplateModel implements DynamicForm {

  /** 活动大类编码 */
  @ApiModelProperty(name = "costTypeCategoryCode",notes = "活动大类编码", value= "活动大类编码")
  @DynamicField(fieldName = "活动大类编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String costTypeCategoryCode;

  /** 活动大类名称 */
  @ApiModelProperty(name = "costTypeCategoryName",notes = "活动大类名称", value= "活动大类名称")
  @DynamicField(fieldName = "活动大类名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private Integer costTypeCategoryName;

  /** 费用预算编码 */
  @ApiModelProperty(name = "costBudgetCode",notes = "费用预算编码", value= "费用预算编码")
  @DynamicField(fieldName = "费用预算编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String costBudgetCode;

  /** 活动细类编码 */
  @ApiModelProperty(name = "costTypeDetailCode",notes = "活动细类编码", value= "活动细类编码")
  @DynamicField(fieldName = "活动细类编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String costTypeDetailCode;

  /** 活动细类 */
  @ApiModelProperty(name = "costTypeDetailName",notes = "活动细类名称", value= "活动细类名称")
  @DynamicField(fieldName = "活动细类名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String costTypeDetailName;

  /** 预算科目编码 */
  @ApiModelProperty(name = "budgetSubjectsCode",notes = "预算科目编码", value= "预算科目编码")
  @DynamicField(fieldName = "预算科目编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String budgetSubjectsCode;

  /** 预算科目名称 */
  @ApiModelProperty(name = "budgetSubjectsName",notes = "预算科目名称", value= "预算科目名称")
  @DynamicField(fieldName = "预算科目名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String budgetSubjectsName;

  /** 组织编码 */
  @ApiModelProperty(name = "orgCode",notes = "组织编码", value= "组织编码")
  @DynamicField(fieldName = "组织编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String orgCode;

  /** 组织名称 */
  @ApiModelProperty(name = "orgName",notes = "组织名称", value= "组织名称")
  @DynamicField(fieldName = "组织名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String orgName;

  /** 客户编码 */
  @ApiModelProperty(name = "customerCode",notes = "客户编码", value= "客户编码")
  @DynamicField(fieldName = "客户编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String customerCode;

  /** 客户名称 */
  @ApiModelProperty(name = "customerName",notes = "客户名称", value= "客户名称")
  @DynamicField(fieldName = "客户名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String customerName;

  /** 终端编码 */
  @ApiModelProperty(name = "terminalCode",notes = "终端编码", value= "终端编码")
  @DynamicField(fieldName = "终端编码", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String terminalCode;

  /** 终端名称 */
  @ApiModelProperty(name = "terminalName",notes = "终端名称", value= "终端名称")
  @DynamicField(fieldName = "终端名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String terminalName;

  /** 预估销售额 */
  @ApiModelProperty(name = "estimateSaleAmount",notes = "预估销售额", value= "预估销售额")
  @DynamicField(fieldName = "预估销售额", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String estimateSaleAmount;

  /** 申请金额 */
  @ApiModelProperty(name = "applyAmount",notes = "申请金额", value= "申请金额")
  @DynamicField(fieldName = "申请金额", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private BigDecimal applyAmount;

  /** 支付方式 */
  @ApiModelProperty(name = "payType",notes = "支付方式", value= "支付方式")
  @DynamicField(fieldName = "支付方式", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String payType;

  /** 支付方式名称 */
  @ApiModelProperty(name = "payTypeName",notes = "支付方式名称", value= "支付方式名称")
  @DynamicField(fieldName = "支付方式名称", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String payTypeName;

  /** 费用日期 */
  @ApiModelProperty(name = "feeDate",notes = "费用日期", value= "费用日期")
  @DynamicField(fieldName = "费用日期", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleDateSelectWidget.class)
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date feeDate;

  /** 是否关闭 */
  @ApiModelProperty(name = "colsed",notes = "是否关闭", value= "是否关闭")
  @DynamicField(fieldName = "是否关闭", validates = {@Validate(value = NotBlankValidateStrategy.class)}, controllKey = SimpleInputWidget.class)
  private String colsed;

}
