package com.biz.crm.tpm.business.control.local.service.internal;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.page.cache.service.BusinessPageCacheService;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.control.local.entity.BudgetControl;
import com.biz.crm.tpm.business.control.local.entity.BudgetControlDimension;
import com.biz.crm.tpm.business.control.local.entity.BudgetControlRange;
import com.biz.crm.tpm.business.control.local.entity.BudgetControlSubject;
import com.biz.crm.tpm.business.control.local.repository.BudgetControlDimensionRepository;
import com.biz.crm.tpm.business.control.local.repository.BudgetControlRangeRepository;
import com.biz.crm.tpm.business.control.local.repository.BudgetControlRepository;
import com.biz.crm.tpm.business.control.local.repository.BudgetControlSubjectRepository;
import com.biz.crm.tpm.business.control.sdk.constant.BudgetControlConstant;
import com.biz.crm.tpm.business.control.sdk.dto.*;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlLogEventListener;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlVoService;
import com.biz.crm.tpm.business.control.sdk.vo.*;
import com.biz.crm.tpm.business.pay.sdk.service.*;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Service("budgetControlVoService")
public class BudgetControlVoServiceImpl implements BudgetControlVoService {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private BudgetControlRepository budgetControlRepository;
    @Autowired(required = false)
    private BudgetControlDimensionRepository budgetControlDimensionRepository;
    @Autowired(required = false)
    private BudgetControlRangeRepository budgetControlRangeRepository;
    @Autowired(required = false)
    private BudgetControlSubjectRepository budgetControlSubjectRepository;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Resource
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired(required = false)
    private CusRangBusinessPageCacheService cusRangBusinessPageCacheService;
    @Autowired(required = false)
    private CusRangExcludeBusinessPageCacheService cusRangExcludeBusinessPageCacheService;
    @Autowired(required = false)
    private DeptRangBusinessPageCacheService deptRangBusinessPageCacheService;
    @Autowired(required = false)
    private DeptExcludeRangBusinessPageCacheService deptExcludeRangBusinessPageCacheService;
    @Autowired(required = false)
    private SubjectBusinessPageCacheService subjectBusinessPageCacheService;


    /**
     * 创建
     *
     * @param budgetControlDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(BudgetControlDto budgetControlDto,
                       String cacheKeyCus, String cacheKeyDept, String cacheKeyCusExclude, String cacheKeyDeptExclude, String cacheKeySubject) {
        if (StringUtils.isNotBlank(cacheKeyCus)) {
            List<BudgetControlRangeDto> rangeList = (List<BudgetControlRangeDto>) (List<?>) cusRangBusinessPageCacheService.findCacheList(cacheKeyCus);
            budgetControlDto.setRangeCusList(rangeList);
        }
        if (StringUtils.isNotBlank(cacheKeyDept)) {
            List<BudgetControlRangeDto> rangeList = (List<BudgetControlRangeDto>) (List<?>) deptRangBusinessPageCacheService.findCacheList(cacheKeyDept);
            budgetControlDto.setRangeDeptList(rangeList);
        }
        if (StringUtils.isNotBlank(cacheKeyCusExclude)) {
            List<BudgetControlRangeDto> rangeList = (List<BudgetControlRangeDto>) (List<?>) cusRangExcludeBusinessPageCacheService.findCacheList(cacheKeyCusExclude);
            budgetControlDto.setRangeCusExcludeList(rangeList);
        }
        if (StringUtils.isNotBlank(cacheKeyDeptExclude)) {
            List<BudgetControlRangeDto> rangeList = (List<BudgetControlRangeDto>) (List<?>) cusRangBusinessPageCacheService.findCacheList(cacheKeyDeptExclude);
            budgetControlDto.setRangeDeptExcludeList(rangeList);
        }
        if (StringUtils.isNotBlank(cacheKeySubject)) {
            List<BudgetControlSubjectDto> subList = subjectBusinessPageCacheService.findCacheList(cacheKeySubject);
            budgetControlDto.setSubjectList(subList);
        }
        createValidate(budgetControlDto);
        saveOrUpdate(budgetControlDto, false);

        if (StringUtils.isNotBlank(cacheKeyCus)) {
            cusRangBusinessPageCacheService.clearCache(cacheKeyCus);
        }
        if (StringUtils.isNotBlank(cacheKeyDept)) {
            deptRangBusinessPageCacheService.clearCache(cacheKeyDept);
        }
        if (StringUtils.isNotBlank(cacheKeyCusExclude)) {
            cusRangExcludeBusinessPageCacheService.clearCache(cacheKeyCusExclude);
        }
        if (StringUtils.isNotBlank(cacheKeyDeptExclude)) {
            deptExcludeRangBusinessPageCacheService.clearCache(cacheKeyDeptExclude);
        }
        if (StringUtils.isNotBlank(cacheKeySubject)) {
            subjectBusinessPageCacheService.clearCache(cacheKeySubject);
        }

        //创建业务日志
        BudgetControlLogEventDto logEventDto = new BudgetControlLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(nebulaToolkitService.copyObjectByWhiteList(budgetControlDto, BudgetControlVo.class, LinkedHashSet.class, ArrayList.class));
        SerializableBiConsumer<BudgetControlLogEventListener, BudgetControlLogEventDto> onCreate =
                BudgetControlLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, BudgetControlLogEventListener.class, onCreate);
    }

    /**
     * 修改
     *
     * @param budgetControlDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(BudgetControlDto budgetControlDto,
                       String cacheKeyCus, String cacheKeyDept, String cacheKeyCusExclude, String cacheKeyDeptExclude, String cacheKeySubject) {
        if (StringUtils.isNotBlank(cacheKeyCus)) {
            List<BudgetControlRangeDto> rangeList = (List<BudgetControlRangeDto>) (List<?>) cusRangBusinessPageCacheService.findCacheList(cacheKeyCus);
            budgetControlDto.setRangeCusList(rangeList);
        }
        if (StringUtils.isNotBlank(cacheKeyDept)) {
            List<BudgetControlRangeDto> rangeList = (List<BudgetControlRangeDto>) (List<?>) deptRangBusinessPageCacheService.findCacheList(cacheKeyDept);
            budgetControlDto.setRangeDeptList(rangeList);
        }
        if (StringUtils.isNotBlank(cacheKeyCusExclude)) {
            List<BudgetControlRangeDto> rangeList = (List<BudgetControlRangeDto>) (List<?>) cusRangExcludeBusinessPageCacheService.findCacheList(cacheKeyCusExclude);
            budgetControlDto.setRangeCusExcludeList(rangeList);
        }
        if (StringUtils.isNotBlank(cacheKeyDeptExclude)) {
            List<BudgetControlRangeDto> rangeList = (List<BudgetControlRangeDto>) (List<?>) deptExcludeRangBusinessPageCacheService.findCacheList(cacheKeyDeptExclude);
            budgetControlDto.setRangeDeptExcludeList(rangeList);
        }
        if (StringUtils.isNotBlank(cacheKeySubject)) {
            List<BudgetControlSubjectDto> subList = subjectBusinessPageCacheService.findCacheList(cacheKeySubject);
            budgetControlDto.setSubjectList(subList);
        }
        updateValidate(budgetControlDto);
        BudgetControl entity = budgetControlRepository.findByCode(budgetControlDto.getControlCode());
        Validate.notNull(entity, "未找到对应的数据");
        BudgetControlVo old = nebulaToolkitService.copyObjectByWhiteList(entity, BudgetControlVo.class, LinkedHashSet.class, ArrayList.class);
        saveOrUpdate(budgetControlDto, true);

        if (StringUtils.isNotBlank(cacheKeyCus)) {
            cusRangBusinessPageCacheService.clearCache(cacheKeyCus);
        }
        if (StringUtils.isNotBlank(cacheKeyDept)) {
            deptRangBusinessPageCacheService.clearCache(cacheKeyDept);
        }
        if (StringUtils.isNotBlank(cacheKeyCusExclude)) {
            cusRangExcludeBusinessPageCacheService.clearCache(cacheKeyCusExclude);
        }
        if (StringUtils.isNotBlank(cacheKeyDeptExclude)) {
            deptExcludeRangBusinessPageCacheService.clearCache(cacheKeyDeptExclude);
        }
        if (StringUtils.isNotBlank(cacheKeySubject)) {
            subjectBusinessPageCacheService.clearCache(cacheKeySubject);
        }

        //更新业务日志
        BudgetControlLogEventDto logEventDto = new BudgetControlLogEventDto();
        logEventDto.setOriginal(old);
        logEventDto.setNewest(nebulaToolkitService.copyObjectByWhiteList(budgetControlDto, BudgetControlVo.class, LinkedHashSet.class, ArrayList.class));
        SerializableBiConsumer<BudgetControlLogEventListener, BudgetControlLogEventDto> onUpdate =
                BudgetControlLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, BudgetControlLogEventListener.class, onUpdate);
    }

    /**
     * 保存
     *
     * @param budgetControlDto
     */
    @Override
    public void saveOrUpdate(BudgetControlDto budgetControlDto, boolean beUpdate) {
        if (!beUpdate) {
            FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
            Validate.notNull(loginUserDetails, "未找到当前登陆人信息！");
            budgetControlDto.setOrgCode(loginUserDetails.getOrgCode());
            budgetControlDto.setPositionCode(loginUserDetails.getPostCode());
            List<String> codeList = this.generateCodeService.generateCodeNotDate(BudgetControlConstant.CONTROL_RULE_CODE, 1);
            budgetControlDto.setControlCode(codeList.get(0));
            budgetControlDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            budgetControlDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            budgetControlDto.setTenantCode(TenantUtils.getTenantCode());
        } else {
            budgetControlSubjectRepository.deleteByCode(budgetControlDto.getControlCode());
            budgetControlDimensionRepository.deleteByCode(budgetControlDto.getControlCode());
            budgetControlRangeRepository.deleteByCode(budgetControlDto.getControlCode());
        }

        BudgetControl entity = nebulaToolkitService.copyObjectByWhiteList(budgetControlDto, BudgetControl.class, LinkedHashSet.class, ArrayList.class);
        budgetControlRepository.saveOrUpdate(entity);

        //预算拆分
        if (CollectionUtils.isNotEmpty(budgetControlDto.getSubjectList())) {
            List<BudgetControlSubject> subjectDtoList = new ArrayList<>();

            List<String> codeDetailList = this.generateCodeService.generateCode(BudgetControlConstant.CONTROL_DETAIL_RULE_CODE, budgetControlDto.getSubjectList().size());
            AtomicInteger index = new AtomicInteger(0);
            budgetControlDto.getSubjectList().forEach(e -> {
                String[] splitCode = e.getBudgetSubjectsCode().split(",");
                String[] splitName = e.getBudgetSubjectsName().split(",");
                for (int i = 0; i < splitCode.length; i++) {
                    BudgetControlSubject subjectDto = new BudgetControlSubject();
                    BeanUtils.copyProperties(e, subjectDto);
                    subjectDto.setId(null);
                    subjectDto.setBudgetSubjectsCode(splitCode[i]);
                    subjectDto.setBudgetSubjectsName(splitName[i]);
                    subjectDto.setControlDetailCode(codeDetailList.get(index.get()));
                    subjectDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    subjectDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    subjectDto.setTenantCode(TenantUtils.getTenantCode());
                    subjectDto.setControlCode(budgetControlDto.getControlCode());
                    subjectDtoList.add(subjectDto);
                }
                index.getAndAdd(1);
            });
            budgetControlSubjectRepository.saveBatch(subjectDtoList);
        }
        //管控维度
        if (CollectionUtils.isNotEmpty(budgetControlDto.getDimensionList())) {
            Collection<BudgetControlDimension> budgetControlDimensions = nebulaToolkitService.copyCollectionByWhiteList(budgetControlDto.getDimensionList(), BudgetControlDimensionDto.class, BudgetControlDimension.class, LinkedHashSet.class, ArrayList.class);
            budgetControlDimensions.forEach(e -> {
                e.setId(null);
                e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                e.setTenantCode(TenantUtils.getTenantCode());
                e.setControlCode(budgetControlDto.getControlCode());
            });
            budgetControlDimensionRepository.saveBatch(budgetControlDimensions);
        }
        //管控范围部门
        if (CollectionUtils.isNotEmpty(budgetControlDto.getRangeDeptList())) {
            List<BudgetControlRange> deptDtoList = new ArrayList<>();

            List<String> codeDetailList = this.generateCodeService.generateCode(BudgetControlConstant.CONTROL_DETAIL_DEPT_RULE_CODE, budgetControlDto.getRangeDeptList().size());
            AtomicInteger index = new AtomicInteger(0);
            Collection<BudgetControlRange> budgetControlRanges = nebulaToolkitService.copyCollectionByWhiteList(budgetControlDto.getRangeDeptList(), BudgetControlRangeDto.class, BudgetControlRange.class, LinkedHashSet.class, ArrayList.class);
            budgetControlRanges.forEach(e -> {
                String[] splitCode = e.getDepartmentCode().split(",");
                String[] splitName = e.getDepartmentName().split(",");
                for (int i = 0; i < splitCode.length; i++) {
                    BudgetControlRange deptDto = new BudgetControlRange();
                    BeanUtils.copyProperties(e, deptDto);
                    deptDto.setId(null);
                    deptDto.setDepartmentCode(splitCode[i]);
                    deptDto.setDepartmentName(splitName[i]);
                    deptDto.setControlDetailCode(codeDetailList.get(index.get()));
                    deptDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    deptDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                    deptDto.setTenantCode(TenantUtils.getTenantCode());
                    deptDto.setControlCode(budgetControlDto.getControlCode());
                    deptDto.setRangeType("dept");
                    deptDtoList.add(deptDto);
                }
                index.getAndAdd(1);
            });
            budgetControlRangeRepository.saveBatch(deptDtoList);
        }
        //管控范围客户
        if (CollectionUtils.isNotEmpty(budgetControlDto.getRangeCusList())) {
            Collection<BudgetControlRange> budgetControlRanges = nebulaToolkitService.copyCollectionByWhiteList(budgetControlDto.getRangeCusList(), BudgetControlRangeDto.class, BudgetControlRange.class, LinkedHashSet.class, ArrayList.class);
            budgetControlRanges.forEach(e -> {
                e.setId(null);
                e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                e.setTenantCode(TenantUtils.getTenantCode());
                e.setControlCode(budgetControlDto.getControlCode());
                e.setRangeType("cus");
            });
            budgetControlRangeRepository.saveBatch(budgetControlRanges);
        }
        //管控范围剔除部门
        if (CollectionUtils.isNotEmpty(budgetControlDto.getRangeDeptExcludeList())) {
            Collection<BudgetControlRange> budgetControlRanges = nebulaToolkitService.copyCollectionByWhiteList(budgetControlDto.getRangeDeptExcludeList(), BudgetControlRangeDto.class, BudgetControlRange.class, LinkedHashSet.class, ArrayList.class);
            budgetControlRanges.forEach(e -> {
                e.setId(null);
                e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                e.setTenantCode(TenantUtils.getTenantCode());
                e.setControlCode(budgetControlDto.getControlCode());
                e.setRangeType("deptExclude");
            });
            budgetControlRangeRepository.saveBatch(budgetControlRanges);
        }
        //管控范围剔除客户
        if (CollectionUtils.isNotEmpty(budgetControlDto.getRangeCusExcludeList())) {
            Collection<BudgetControlRange> budgetControlRanges = nebulaToolkitService.copyCollectionByWhiteList(budgetControlDto.getRangeCusExcludeList(), BudgetControlRangeDto.class, BudgetControlRange.class, LinkedHashSet.class, ArrayList.class);
            budgetControlRanges.forEach(e -> {
                e.setId(null);
                e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                e.setTenantCode(TenantUtils.getTenantCode());
                e.setControlCode(budgetControlDto.getControlCode());
                e.setRangeType("cusExclude");
            });
            budgetControlRangeRepository.saveBatch(budgetControlRanges);
        }
    }

    /**
     * 创建验证
     *
     * @param budgetControlDto
     */
    private void createValidate(BudgetControlDto budgetControlDto) {
        budgetControlDto.setId(null);
        commonValidate(budgetControlDto);
    }

    /**
     * 修改验证
     *
     * @param budgetControlDto
     */
    private void updateValidate(BudgetControlDto budgetControlDto) {
        Validate.notBlank(budgetControlDto.getId(), "修改数据时，主键不能为空！");
        commonValidate(budgetControlDto);
    }

    /**
     * 公共验证
     *
     * @param budgetControlDto
     */
    private void commonValidate(BudgetControlDto budgetControlDto) {
        Validate.notBlank(budgetControlDto.getControlName(), "管控配置名称，不能为空！");
        Validate.notBlank(budgetControlDto.getDepartmentCode(), "适用部门，不能为空！");
        Validate.notNull(budgetControlDto.getStartYearMonth(), "生效开始时间，不能为空！");
        Validate.notNull(budgetControlDto.getEndYearMonth(), "生效结束时间，不能为空！");
        Validate.notBlank(budgetControlDto.getTimeDimension(), "时间维度，不能为空！");
        Validate.notBlank(budgetControlDto.getControlType(), "管控类型，不能为空！");
        Validate.notBlank(budgetControlDto.getControlForm(), "管控形式，不能为空！");

        List<BudgetControlRangeDto> deptList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(budgetControlDto.getRangeDeptList())) {
            deptList.addAll(budgetControlDto.getRangeDeptList());
        }
        if (!deptList.stream().map(e -> e.getDepartmentCode()).collect(Collectors.toSet()).contains(budgetControlDto.getDepartmentCode())) {
            BudgetControlRangeDto dept = new BudgetControlRangeDto();
            dept.setDepartmentCode(budgetControlDto.getDepartmentCode());
            dept.setDepartmentName(budgetControlDto.getDepartmentName());
            deptList.add(dept);
        }

        //适用部门，控制部门不能同时存在于剔除部门中
        if (CollectionUtils.isNotEmpty(budgetControlDto.getRangeDeptExcludeList())) {
            List<String> excludeList = budgetControlDto.getRangeDeptExcludeList().stream().map(BudgetControlRangeDto::getDepartmentCode).collect(Collectors.toList());
            String names = deptList.stream().filter(e -> excludeList.contains(e.getDepartmentCode())).map(BudgetControlRangeDto::getDepartmentName).distinct().collect(Collectors.joining(","));
            Validate.isTrue(StringUtils.isBlank(names), "部门已被剔除：%s", names);
        }

        //控制客户不能同时存在于剔除客户中
        if (CollectionUtils.isNotEmpty(budgetControlDto.getRangeCusList())
            && CollectionUtils.isNotEmpty(budgetControlDto.getRangeCusExcludeList())) {
            List<String> excludeList = budgetControlDto.getRangeCusExcludeList().stream().map(BudgetControlRangeDto::getDepartmentCode).collect(Collectors.toList());
            String names = budgetControlDto.getRangeCusList().stream().filter(e -> excludeList.contains(e.getDepartmentCode())).map(BudgetControlRangeDto::getDepartmentName).distinct().collect(Collectors.joining(","));
            Validate.isTrue(StringUtils.isBlank(names), "客户已被剔除：%s", names);
        }
    }

    /**
     * 删除
     *
     * @param idList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> idList) {
        Validate.notEmpty(idList, "删除时，id不能为空");
        List<BudgetControl> budgetControlList = this.budgetControlRepository.findByIds(idList);
        Validate.notEmpty(budgetControlList, "未找到对应的数据");
        budgetControlRepository.removeByIds(idList);
    }

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    @Override
    public BudgetControlVo findByCode(String code) {
        Validate.notBlank(code, "编码不能为空");
        BudgetControl entity = budgetControlRepository.findByCode(code);
        Validate.notNull(entity, "未找到编码所对应的预算管控");

        BudgetControlVo vo = nebulaToolkitService.copyObjectByWhiteList(entity, BudgetControlVo.class, LinkedHashSet.class, ArrayList.class);
        vo.setDimensionList(budgetControlDimensionRepository.findByCode(code));

        return vo;
    }

    /**
     * 启用
     *
     * @param idList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enable(List<String> idList) {
        Validate.notEmpty(idList, "启用时，id不能为空");
        List<BudgetControl> budgetControlList = this.budgetControlRepository.findByIds(idList);
        Validate.notEmpty(budgetControlList, "未找到对应的数据");
        this.budgetControlRepository.updateEnableStatusByIds(EnableStatusEnum.ENABLE, idList);
        Collection<BudgetControlVo> budgetControlVos = this.nebulaToolkitService.copyCollectionByWhiteList(budgetControlList, BudgetControl.class, BudgetControlVo.class, LinkedHashSet.class, ArrayList.class);

        //启用业务日志
        BudgetControlLogEventDto logEventDto = new BudgetControlLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(new ArrayList<>(budgetControlVos));
        SerializableBiConsumer<BudgetControlLogEventListener, BudgetControlLogEventDto> onEnable =
                BudgetControlLogEventListener::onEnable;
        this.nebulaNetEventClient.publish(logEventDto, BudgetControlLogEventListener.class, onEnable);
    }

    /**
     * 禁用
     *
     * @param idList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void disable(List<String> idList) {
        Validate.notEmpty(idList, "禁用时，id不能为空");
        List<BudgetControl> budgetControlList = this.budgetControlRepository.findByIds(idList);
        Validate.notEmpty(budgetControlList, "未找到对应的数据");
        this.budgetControlRepository.updateEnableStatusByIds(EnableStatusEnum.DISABLE, idList);
        Collection<BudgetControlVo> budgetControlVos = this.nebulaToolkitService.copyCollectionByWhiteList(budgetControlList, BudgetControl.class, BudgetControlVo.class, LinkedHashSet.class, ArrayList.class);

        //禁用业务日志
        BudgetControlLogEventDto logEventDto = new BudgetControlLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(new ArrayList<>(budgetControlVos));
        SerializableBiConsumer<BudgetControlLogEventListener, BudgetControlLogEventDto> onDisable =
                BudgetControlLogEventListener::onDisable;
        this.nebulaNetEventClient.publish(logEventDto, BudgetControlLogEventListener.class, onDisable);
    }

    /**
     * 查询生效中的管控配置
     *
     * @return
     */
    @Override
    public List<BudgetControlVo> findBudgetControlEnable(String yearMonthLy) {
        List<BudgetControlVo> controlVoList = budgetControlRepository.findBudgetControlEnable(yearMonthLy);
        Set<String> controlCodeSet = controlVoList.stream().map(BudgetControlVo::getControlCode).collect(Collectors.toSet());
        Map<String, List<BudgetControlDimensionVo>> dimensionMap = budgetControlDimensionRepository.findByCodeSet(controlCodeSet).stream().collect(Collectors.groupingBy(e -> e.getControlCode()));
        Map<String, List<BudgetControlRangeVo>> rangeMap = budgetControlRangeRepository.findByCodeSet(controlCodeSet).stream().collect(Collectors.groupingBy(e -> e.getControlCode()));
        Map<String, List<BudgetControlSubjectVo>> subjectMap = budgetControlSubjectRepository.findByCodeSet(controlCodeSet).stream().collect(Collectors.groupingBy(e -> e.getControlCode()));
        controlVoList.forEach(e -> {
            List<BudgetControlRangeVo> rangeVoList = rangeMap.getOrDefault(e.getControlCode(), new ArrayList<>());
            Map<String, List<BudgetControlRangeVo>> rangeVoMap = rangeVoList.stream().collect(Collectors.groupingBy(r -> r.getRangeType()));
            e.setDimensionList(dimensionMap.getOrDefault(e.getControlCode(), new ArrayList<>()));
            e.setRangeDeptList(rangeVoMap.getOrDefault("dept", new ArrayList<>()));
            e.setRangeCusList(rangeVoMap.getOrDefault("cus", new ArrayList<>()));
            e.setRangeDeptExcludeList(rangeVoMap.getOrDefault("deptExclude", new ArrayList<>()));
            e.setRangeCusExcludeList(rangeVoMap.getOrDefault("cusExclude", new ArrayList<>()));
            e.setSubjectList(subjectMap.getOrDefault(e.getControlCode(), new ArrayList<>()));
        });
        return controlVoList;
    }


    /**
     * 通过编码查询管控规则
     *
     * @param codes
     * @return
     */
    @Override
    public List<BudgetControlVo> findBudgetControlByCodes(List<String> codes) {
        List<BudgetControlVo> controlVos = budgetControlRepository.findBudgetControlByCodes(codes);
        if (CollectionUtils.isEmpty(controlVos)) {
            return Lists.newArrayList();
        }
        Set<String> controlCodeSet = controlVos.stream().map(BudgetControlVo::getControlCode).collect(Collectors.toSet());
        Map<String, List<BudgetControlDimensionVo>> dimensionMap = budgetControlDimensionRepository.findByCodeSet(controlCodeSet).stream().collect(Collectors.groupingBy(e -> e.getControlCode()));
        Map<String, List<BudgetControlRangeVo>> rangeMap = budgetControlRangeRepository.findByCodeSet(controlCodeSet).stream().collect(Collectors.groupingBy(e -> e.getControlCode()));
        Map<String, List<BudgetControlSubjectVo>> subjectMap = budgetControlSubjectRepository.findByCodeSet(controlCodeSet).stream().collect(Collectors.groupingBy(e -> e.getControlCode()));
        controlVos.forEach(e -> {
            List<BudgetControlRangeVo> rangeVoList = rangeMap.getOrDefault(e.getControlCode(), new ArrayList<>());
            Map<String, List<BudgetControlRangeVo>> rangeVoMap = rangeVoList.stream().collect(Collectors.groupingBy(r -> r.getRangeType()));
            e.setDimensionList(dimensionMap.getOrDefault(e.getControlCode(), new ArrayList<>()));
            e.setRangeDeptList(rangeVoMap.getOrDefault("dept", new ArrayList<>()));
            e.setRangeCusList(rangeVoMap.getOrDefault("cus", new ArrayList<>()));
            e.setRangeDeptExcludeList(rangeVoMap.getOrDefault("deptExclude", new ArrayList<>()));
            e.setRangeCusExcludeList(rangeVoMap.getOrDefault("cusExclude", new ArrayList<>()));
            e.setSubjectList(subjectMap.getOrDefault(e.getControlCode(), new ArrayList<>()));
        });
        return controlVos;
    }

    /**
     * 匹配规则，年月+部门+客户+预算科目
     *
     * @param years
     * @param orgCodes
     * @param customerCode
     * @param budgetSubjectCodeSet
     * @return
     */
    @Override
    public List<BudgetControlVo> matchBudgetControl(String years, List<String> orgCodes, String customerCode, Set<String> budgetSubjectCodeSet) {
        if (!(ObjectUtils.isNotEmpty(years) && CollectionUtils.isNotEmpty(orgCodes)
                && ObjectUtils.isNotEmpty(budgetSubjectCodeSet))) {
            return null;
        }
        return budgetControlRepository.matchBudgetControl(years, orgCodes, customerCode, budgetSubjectCodeSet);
    }
}
