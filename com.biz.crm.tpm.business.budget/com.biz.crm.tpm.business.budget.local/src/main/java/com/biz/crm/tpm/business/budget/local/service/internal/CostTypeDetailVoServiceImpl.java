package com.biz.crm.tpm.business.budget.local.service.internal;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.common.form.sdk.DynamicFormFieldMappingService;
import com.biz.crm.tpm.business.activities.sdk.service.ActivitiesConfigVoService;
import com.biz.crm.tpm.business.activities.sdk.vo.ActivitiesConfigVo;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeDetail;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeDetailCollect;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeDetailSettingStrategy;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeMapping;
import com.biz.crm.tpm.business.budget.local.repository.CostTypeDetailCollectRepository;
import com.biz.crm.tpm.business.budget.local.repository.CostTypeDetailRepository;
import com.biz.crm.tpm.business.budget.local.repository.CostTypeDetailSettingStrategyRepository;
import com.biz.crm.tpm.business.budget.local.service.CostTypeMappingService;
import com.biz.crm.tpm.business.budget.sdk.constant.CostTypeDetailConstant;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailsDto;
import com.biz.crm.tpm.business.budget.sdk.enums.ApprovalCollectType;
import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType;
import com.biz.crm.tpm.business.budget.sdk.event.CostTypeDetailEventListener;
import com.biz.crm.tpm.business.budget.sdk.event.FormStrategyPropertiesDeleteService;
import com.biz.crm.tpm.business.budget.sdk.event.log.CostTypeDetailLogEventListener;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.service.StrategySettingManageVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.form.FormEventStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.PayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.*;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailCollectVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeMappingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.enums.RedisKeys.GENERATE_PREFIX;

/**
 * TPM-活动明细;(tpm_cost_type_details)表服务实现类
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@Service("costTypeDetailVoService")
@Deprecated
public class CostTypeDetailVoServiceImpl implements CostTypeDetailVoService {
    @Autowired
    private CostTypeDetailRepository costTypeDetailRepository;
    @Autowired
    private CostTypeMappingService costTypeMappingService;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired
    private RedisMutexService redisMutexService;
    @Autowired(required = false)
    private List<CostTypeDetailEventListener> costTypeDetailEventListeners;

    @Autowired
    private List<FormEventStrategy> formEventStrategies;
    @Autowired
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private List<PayByStrategy> payByStrategies;
    @Autowired
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired
    private StrategySettingStructAnalysis strategySettingStructAnalysis;
    @Autowired
    private CostTypeDetailSettingStrategyRepository costTypeDetailSettingStrategyRepository;
    @Autowired
    private StrategySettingManageVoService strategySettingManageVoService;
    @Autowired
    private DynamicFormFieldMappingService dynamicFormFieldMappingService;

    @Autowired
    private ActivitiesConfigVoService activitiesConfigVoService;

    @Autowired
    private FormStrategyPropertiesDeleteService formStrategyPropertiesDeleteService;

    @Autowired
    private CostTypeDetailCollectRepository costTypeDetailCollectRepository;

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     * @return
     */
    @Override
    public Page<CostTypeDetailVo> findByConditions(Pageable pageable, CostTypeDetailsDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 2000));
        if (Objects.isNull(dto)) {
            dto = new CostTypeDetailsDto();
        }
        List<String> selectedCodeList =
                Optional.ofNullable(dto.getSelectedCodeList()).orElse(new ArrayList<>());
        if (StringUtils.isNotEmpty(dto.getSelectedCode())) {
            selectedCodeList.add(dto.getSelectedCode());
        }
        if (!CollectionUtils.isEmpty(selectedCodeList)) {
            dto.setSelectedCodeList(selectedCodeList);
        }
        Set<String> categoryCodeSet = Sets.newHashSet();
        if (ObjectUtils.isNotEmpty(dto.getCategoryCode())) {
            categoryCodeSet.add(dto.getCategoryCode());
        }
        if (!CollectionUtils.isEmpty(dto.getCategoryCodeSet())) {
            categoryCodeSet.addAll(dto.getCategoryCodeSet());
        }
        dto.setCategoryCodeSet(categoryCodeSet);
        Page<CostTypeDetailVo> page = this.costTypeDetailRepository.findByConditions(pageable, dto);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            for (CostTypeDetailVo x : page.getRecords()) {
                if (ObjectUtils.isNotEmpty(x.getPayBy())) {
                    x.setPayBys(Sets.newHashSet(Arrays.asList(x.getPayBy().split(","))));
                }
            }
            List<String> detailCodes = page.getRecords().stream().map(CostTypeDetailVo::getDetailCode).collect(Collectors.toList());
            List<CostTypeDetailCollectVo> collectVos = costTypeDetailCollectRepository.findListByCodes(detailCodes);
            if (!CollectionUtils.isEmpty(collectVos)) {
                Map<String, List<CostTypeDetailCollectVo>> map = collectVos.stream().collect(Collectors.groupingBy(CostTypeDetailCollectVo::getDetailCode));
                for (CostTypeDetailVo record : page.getRecords()) {
                    if (map.containsKey(record.getDetailCode())) {
                        Map<String, List<CostTypeDetailCollectVo>> collectMap = map.get(record.getDetailCode()).stream()
                                .collect(Collectors.groupingBy(CostTypeDetailCollectVo::getType));
                        record.setCollectList(collectMap.getOrDefault(ApprovalCollectType.COLLECT.getCode(), Lists.newArrayList()));
                        record.setApprovalList(collectMap.getOrDefault(ApprovalCollectType.APPROVAL.getCode(), Lists.newArrayList()));
                    }
                }
            }
        }
        return page;
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @Override
    public CostTypeDetailVo findById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        CostTypeDetail costTypeDetail = this.costTypeDetailRepository.findByIdAndTenantCode(id, TenantUtils.getTenantCode());
        if (costTypeDetail == null) {
            return null;
        }
        CostTypeDetailVo costTypeDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(costTypeDetail, CostTypeDetailVo.class, LinkedHashSet.class, ArrayList.class);
        this.fillDetailData(costTypeDetailVo);

        return costTypeDetailVo;
    }

    /**
     * 通过编号查询单条数据
     *
     * @param code 主键
     * @return 单条数据
     */
    @Override
    public CostTypeDetailVo findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        CostTypeDetail costTypeDetail = this.costTypeDetailRepository.findByCode(code);
        if (costTypeDetail == null) {
            return null;
        }
        CostTypeDetailVo costTypeDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(costTypeDetail, CostTypeDetailVo.class, LinkedHashSet.class, ArrayList.class);
        this.fillDetailData(costTypeDetailVo);
        return costTypeDetailVo;
    }

    /**
     * 补充活动名细类数据
     *
     * @param costTypeDetailVo
     */
    private void fillDetailData(CostTypeDetailVo costTypeDetailVo) {
        // 处理支付方式
        costTypeDetailVo.setPayBys(Sets.newLinkedHashSet(Arrays.asList(StringUtils.split(costTypeDetailVo.getPayBy(), ","))));
        List<CostTypeDetailCollectVo> collectVoList = costTypeDetailCollectRepository.findByCode(costTypeDetailVo.getDetailCode());
        List<CostTypeDetailCollectVo> collect = collectVoList.stream().filter(e -> ApprovalCollectType.COLLECT.getCode().equals(e.getType())).collect(Collectors.toList());
        collectVoList.removeAll(collect);
        costTypeDetailVo.setCollectList(collect);
        costTypeDetailVo.setApprovalList(collectVoList);
    }

    /**
     * 通过编码集合查询
     *
     * @param codes
     * @return
     */
    @Override
    public List<CostTypeDetailVo> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        List<CostTypeDetail> costTypeDetail = this.costTypeDetailRepository.findByCodes(codes);
        if (CollectionUtils.isEmpty(costTypeDetail)) {
            return Lists.newArrayList();
        }
        List<CostTypeDetailVo> result = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(costTypeDetail, CostTypeDetail.class, CostTypeDetailVo.class, HashSet.class, ArrayList.class));
        result.forEach(e -> fillDetailData(e));
        return result;
    }

    /**
     * 新增数据
     *
     * @param costTypeDetailVo 实体对象
     * @return 新增结果
     */
    @Transactional(rollbackOn = Exception.class)
    @Override
    public CostTypeDetailVo create(CostTypeDetailVo costTypeDetailVo) {
        costTypeDetailVo.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        costTypeDetailVo.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        this.createValidate(costTypeDetailVo);
        String code = this.generateCodeService.generateCodeNotDate(CostTypeDetailConstant.COSTTYPEDETAIL_LADDER_CODE, 1,5).get(0);
        costTypeDetailVo.setDetailCode(code);
        CostTypeDetail costTypeDetail = this.nebulaToolkitService.copyObjectByWhiteList(costTypeDetailVo, CostTypeDetail.class, LinkedHashSet.class, ArrayList.class);
        costTypeDetail.setTenantCode(TenantUtils.getTenantCode());
        // 处理支付方式
        String payBy = String.join(",", costTypeDetailVo.getPayBys());
        costTypeDetail.setPayBy(payBy);
        this.costTypeDetailRepository.saveOrUpdate(costTypeDetail);

        if (!CollectionUtils.isEmpty(costTypeDetailVo.getCollectList())) {
            Collection<CostTypeDetailCollect> costTypeDetailCollects = nebulaToolkitService.copyCollectionByWhiteList(costTypeDetailVo.getCollectList(), CostTypeDetailCollectVo.class, CostTypeDetailCollect.class, LinkedHashSet.class, ArrayList.class);
            costTypeDetailCollects.forEach(e -> {
                e.setId(null);
                e.setDetailCode(costTypeDetailVo.getDetailCode());
                e.setDetailName(costTypeDetailVo.getDetailName());
                e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                e.setTenantCode(TenantUtils.getTenantCode());
                e.setType(ApprovalCollectType.COLLECT.getCode());
            });
            costTypeDetailCollectRepository.saveBatch(costTypeDetailCollects);
        }
        if (!CollectionUtils.isEmpty(costTypeDetailVo.getApprovalList())) {
            Collection<CostTypeDetailCollect> costTypeDetailCollects = nebulaToolkitService.copyCollectionByWhiteList(costTypeDetailVo.getApprovalList(), CostTypeDetailCollectVo.class, CostTypeDetailCollect.class, LinkedHashSet.class, ArrayList.class);
            costTypeDetailCollects.forEach(e -> {
                e.setId(null);
                e.setDetailCode(costTypeDetailVo.getDetailCode());
                e.setDetailName(costTypeDetailVo.getDetailName());
                e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                e.setTenantCode(TenantUtils.getTenantCode());
                e.setType(ApprovalCollectType.APPROVAL.getCode());
            });
            costTypeDetailCollectRepository.saveBatch(costTypeDetailCollects);
        }

        costTypeDetailVo.setId(costTypeDetail.getId());
        if (!CollectionUtils.isEmpty(costTypeDetailEventListeners)) {
            for (CostTypeDetailEventListener costTypeDetailEventListener : costTypeDetailEventListeners) {
                costTypeDetailEventListener.onCreated(costTypeDetailVo);
            }
        }
        //新增业务日志
        CostTypeDetailLogEventDto logEventDto = new CostTypeDetailLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewest(costTypeDetailVo);
        SerializableBiConsumer<CostTypeDetailLogEventListener, CostTypeDetailLogEventDto> onCreate =
                CostTypeDetailLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, CostTypeDetailLogEventListener.class, onCreate);
        return costTypeDetailVo;
    }

    /**
     * 重新绑定活动表单列表项
     *
     * @param formCode        表单配置编码
     * @param formMappingCode 动态表单关联编码
     * @param extendValue     列表数据项
     */
    private void remindFormCodes(String formCode, String formMappingCode, JSONObject extendValue) {
        if (StringUtils.isNotBlank(formCode)) {
            ActivitiesConfigVo activitiesConfigVo = this.activitiesConfigVoService.findByActivitiesConfigCode(formCode);
            if (ObjectUtils.isNotEmpty(activitiesConfigVo)) {
                if (Objects.nonNull(extendValue) && extendValue.size() > 0) {
                    this.dynamicFormFieldMappingService.rebinding(activitiesConfigVo.getDynamicFormCode(), formMappingCode,
                            extendValue.getJSONArray(CostTypeDetailConstant.FILED_CODES).toJavaList(String.class),
                            extendValue.getJSONArray(CostTypeDetailConstant.NULL_ABLES).toJavaList(Boolean.class));
                }
            }
        }
    }

    /**
     * 修改新据
     *
     * @param costTypeDetailVo 实体对象
     * @return 修改结果
     */
    @Transactional(rollbackOn = Exception.class)
    @Override
    public CostTypeDetailVo update(CostTypeDetailVo costTypeDetailVo) {
        this.updateValidate(costTypeDetailVo);
        CostTypeDetail oldCostTypeDetail = this.costTypeDetailRepository.getById(costTypeDetailVo.getId());
        CostTypeDetailVo oldCostTypeDetailVo = this.nebulaToolkitService.copyObjectByWhiteList(oldCostTypeDetail, CostTypeDetailVo.class, LinkedHashSet.class, ArrayList.class);
        Validate.notNull(oldCostTypeDetail, "修改数据不存在，请检查！");
        // 处理支付方式
        String payBy = String.join(",", costTypeDetailVo.getPayBys());
        CostTypeDetail newCostTypeDetail = this.nebulaToolkitService.copyObjectByWhiteList(costTypeDetailVo, CostTypeDetail.class, LinkedHashSet.class, ArrayList.class);
        newCostTypeDetail.setPayBy(payBy);
        this.costTypeDetailRepository.saveOrUpdate(newCostTypeDetail);


        costTypeDetailCollectRepository.deleteByCode(newCostTypeDetail.getDetailCode());
        if (!CollectionUtils.isEmpty(costTypeDetailVo.getCollectList())) {
            Collection<CostTypeDetailCollect> costTypeDetailCollects = nebulaToolkitService.copyCollectionByWhiteList(costTypeDetailVo.getCollectList(), CostTypeDetailCollectVo.class, CostTypeDetailCollect.class, LinkedHashSet.class, ArrayList.class);
            costTypeDetailCollects.forEach(e -> {
                e.setId(null);
                e.setDetailCode(costTypeDetailVo.getDetailCode());
                e.setDetailName(costTypeDetailVo.getDetailName());
                e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                e.setTenantCode(TenantUtils.getTenantCode());
                e.setType(ApprovalCollectType.COLLECT.getCode());
            });
            costTypeDetailCollectRepository.saveBatch(costTypeDetailCollects);
        }
        if (!CollectionUtils.isEmpty(costTypeDetailVo.getApprovalList())) {
            Collection<CostTypeDetailCollect> costTypeDetailCollects = nebulaToolkitService.copyCollectionByWhiteList(costTypeDetailVo.getApprovalList(), CostTypeDetailCollectVo.class, CostTypeDetailCollect.class, LinkedHashSet.class, ArrayList.class);
            costTypeDetailCollects.forEach(e -> {
                e.setId(null);
                e.setDetailCode(costTypeDetailVo.getDetailCode());
                e.setDetailName(costTypeDetailVo.getDetailName());
                e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                e.setTenantCode(TenantUtils.getTenantCode());
                e.setType(ApprovalCollectType.APPROVAL.getCode());
            });
            costTypeDetailCollectRepository.saveBatch(costTypeDetailCollects);
        }

        if (!CollectionUtils.isEmpty(costTypeDetailEventListeners)) {
            for (CostTypeDetailEventListener costTypeDetailEventListener : costTypeDetailEventListeners) {
                costTypeDetailEventListener.onUpdate(oldCostTypeDetailVo, costTypeDetailVo);
            }
        }
        //编辑业务日志
        CostTypeDetailLogEventDto logEventDto = new CostTypeDetailLogEventDto();
        logEventDto.setOriginal(oldCostTypeDetailVo);
        CostTypeDetailVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(newCostTypeDetail, CostTypeDetailVo.class, HashSet.class, ArrayList.class);
        logEventDto.setNewest(newVo);
        SerializableBiConsumer<CostTypeDetailLogEventListener, CostTypeDetailLogEventDto> onUpdate =
                CostTypeDetailLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, CostTypeDetailLogEventListener.class, onUpdate);
        return costTypeDetailVo;
    }

    private void transferToCostTypeDetailSettingStrategy(List<CostTypeDetailSettingStrategy> costTypeDetailSettingStrategies, List<StrategySettingStruct> structs, String detailCode) {
        for (StrategySettingStruct strategySettingStruct : structs) {
            CostTypeDetailSettingStrategy costTypeDetailSettingStrategy = new CostTypeDetailSettingStrategy();
            costTypeDetailSettingStrategy.setId(strategySettingStruct.getId());
            costTypeDetailSettingStrategy.setCode(strategySettingStruct.getCode());
            costTypeDetailSettingStrategy.setDateFormat(strategySettingStruct.getDateFormat());
            costTypeDetailSettingStrategy.setDefaultValue(strategySettingStruct.getDefaultValue() == null ? null : String.valueOf(strategySettingStruct.getDefaultValue()));
            costTypeDetailSettingStrategy.setDisplay(strategySettingStruct.getDisplay());
            costTypeDetailSettingStrategy.setDetailCode(detailCode);
            costTypeDetailSettingStrategy.setName(strategySettingStruct.getName());
            costTypeDetailSettingStrategy.setNecessary(strategySettingStruct.getNecessary());
            costTypeDetailSettingStrategy.setParentCode(strategySettingStruct.getParentCode());
            costTypeDetailSettingStrategy.setSettingManageCode(strategySettingStruct.getSettingManageCode());
            costTypeDetailSettingStrategy.setSortIndex(strategySettingStruct.getSortIndex());
            costTypeDetailSettingStrategy.setTip(strategySettingStruct.getTip());
            costTypeDetailSettingStrategy.setType(strategySettingStruct.getType());
            costTypeDetailSettingStrategy.setValue(strategySettingStruct.getValue() == null ? null : strategySettingStruct.getValue().toString());
            costTypeDetailSettingStrategy.setValueType(strategySettingStruct.getValueType());
            costTypeDetailSettingStrategy.setExtendValue(ObjectUtils.defaultIfNull(strategySettingStruct.getExtendValue(), new JSONObject()));
            costTypeDetailSettingStrategies.add(costTypeDetailSettingStrategy);
            if (!CollectionUtils.isEmpty(strategySettingStruct.getChildren())) {
                this.transferToCostTypeDetailSettingStrategy(costTypeDetailSettingStrategies, strategySettingStruct.getChildren(), detailCode);
            }
        }
    }

    private void transferToStrategySettingStruct(List<StrategySettingStruct> structs, List<CostTypeDetailSettingStrategy> settingStrategies) {
        for (CostTypeDetailSettingStrategy settingStrategy : settingStrategies) {
            StrategySettingStruct struct = new StrategySettingStruct();
            struct.setId(settingStrategy.getId());
            struct.setCode(settingStrategy.getCode());
            struct.setDateFormat(settingStrategy.getDateFormat());
            struct.setDefaultValue(StringUtils.isBlank(settingStrategy.getValue()) ? null : strategySettingStructAnalysis.transferValue(settingStrategy.getValue(), settingStrategy.getValueType()));
            struct.setDisplay(settingStrategy.getDisplay());
            struct.setDetailCode(settingStrategy.getDetailCode());
            struct.setName(settingStrategy.getName());
            struct.setNecessary(settingStrategy.getNecessary());
            struct.setParentCode(settingStrategy.getParentCode());
            struct.setSettingManageCode(settingStrategy.getSettingManageCode());
            struct.setSortIndex(settingStrategy.getSortIndex());
            struct.setTip(settingStrategy.getTip());
            struct.setType(settingStrategy.getType());
            struct.setValue(StringUtils.isBlank(settingStrategy.getValue()) ? null : strategySettingStructAnalysis.transferValue(settingStrategy.getValue(), settingStrategy.getValueType()));
            struct.setValueType(settingStrategy.getValueType());
            structs.add(struct);
        }
    }

    /**
     * 删除数据
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @Transactional
    @Override
    public void delete(List<String> ids) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
        List<CostTypeDetail> costTypeDetailsses = this.costTypeDetailRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(costTypeDetailsses)) {
            return;
        }
        Set<String> detailCodes = costTypeDetailsses.stream().map(CostTypeDetail::getDetailCode).collect(Collectors.toSet());
        costTypeDetailSettingStrategyRepository.deleteByDetailCodes(detailCodes);
        costTypeDetailCollectRepository.deleteByCodes(new ArrayList<>(detailCodes));

        Collection<CostTypeDetailVo> costTypeDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeDetailsses, CostTypeDetail.class, CostTypeDetailVo.class, LinkedHashSet.class, ArrayList.class);
        this.costTypeDetailRepository.removeByIdsAndTenantCode(ids, TenantUtils.getTenantCode());
        if (!CollectionUtils.isEmpty(costTypeDetailEventListeners)) {
            for (CostTypeDetailEventListener costTypeDetailEventListener : costTypeDetailEventListeners) {
                for (CostTypeDetailVo costTypeDetailsVo : costTypeDetailVos) {
                    costTypeDetailEventListener.onDeleted(costTypeDetailsVo);
                }
            }
        }
        //删除业务日志
        SerializableBiConsumer<CostTypeDetailLogEventListener, CostTypeDetailLogEventDto> onDelete =
                CostTypeDetailLogEventListener::onDelete;
        for (CostTypeDetailVo vo : costTypeDetailVos) {
            CostTypeDetailLogEventDto logEventDto = new CostTypeDetailLogEventDto();
            logEventDto.setOriginal(vo);
            this.nebulaNetEventClient.publish(logEventDto, CostTypeDetailLogEventListener.class, onDelete);
        }
    }

    /**
     * 通过启用状态查询数据
     *
     * @param enableStatus 状态
     * @return 集合数据
     */
    @Override
    public List<CostTypeDetailVo> findByEnableStatus(String enableStatus) {
        if (StringUtils.isBlank(enableStatus)) {
            return Collections.emptyList();
        }
        List<CostTypeDetail> costTypeDetails = this.costTypeDetailRepository.findByEnableStatus(enableStatus);
        if (CollectionUtils.isEmpty(costTypeDetails)) {
            return Collections.emptyList();
        }
        Collection<CostTypeDetailVo> costTypeDetailss = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeDetails, CostTypeDetail.class, CostTypeDetailVo.class, LinkedHashSet.class, ArrayList.class);
        costTypeDetailss.forEach(item -> {
            // 处理支付方式
            item.setPayBys(Sets.newLinkedHashSet(Arrays.asList(StringUtils.split(item.getPayBy(), ","))));
        });
        return Lists.newArrayList(costTypeDetailss);
    }

    /**
     * 批量根据id启用
     *
     * @param ids
     */
    @Override
    @Transactional
    public void enable(List<String> ids) {
        Validate.notEmpty(ids, "启用时，id不能为空");
        List<CostTypeDetail> costTypeDetails = this.costTypeDetailRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(costTypeDetails)) {
            return;
        }
        Collection<CostTypeDetailVo> costTypeDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeDetails, CostTypeDetail.class, CostTypeDetailVo.class, LinkedHashSet.class, ArrayList.class);
        this.costTypeDetailRepository.updateEnableStatusByIds(EnableStatusEnum.ENABLE, ids);
        if (!CollectionUtils.isEmpty(costTypeDetailEventListeners)) {
            for (CostTypeDetailEventListener costTypeDetailEventListener : costTypeDetailEventListeners) {
                for (CostTypeDetailVo costTypeDetailVo : costTypeDetailVos) {
                    costTypeDetailEventListener.onEnable(costTypeDetailVo);
                }
            }
        }
        //启用业务日志
        SerializableBiConsumer<CostTypeDetailLogEventListener, CostTypeDetailLogEventDto> onEnable =
                CostTypeDetailLogEventListener::onEnable;
        for (CostTypeDetailVo vo : costTypeDetailVos) {
            CostTypeDetailLogEventDto logEventDto = new CostTypeDetailLogEventDto();
            logEventDto.setOriginal(vo);
            this.nebulaNetEventClient.publish(logEventDto, CostTypeDetailLogEventListener.class, onEnable);
        }
    }

    /**
     * 批量根据id禁用
     *
     * @param ids
     */
    @Override
    @Transactional
    public void disable(List<String> ids) {
        Validate.notEmpty(ids, "禁用时，id不能为空");
        List<CostTypeDetail> costTypeDetails = this.costTypeDetailRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(costTypeDetails)) {
            return;
        }
        Collection<CostTypeDetailVo> costTypeDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeDetails, CostTypeDetail.class, CostTypeDetailVo.class, LinkedHashSet.class, ArrayList.class);
        this.costTypeDetailRepository.updateEnableStatusByIds(EnableStatusEnum.DISABLE, ids);
        if (!CollectionUtils.isEmpty(costTypeDetailEventListeners)) {
            for (CostTypeDetailEventListener costTypeDetailEventListener : costTypeDetailEventListeners) {
                for (CostTypeDetailVo costTypeDetailVo : costTypeDetailVos) {
                    costTypeDetailEventListener.onEnable(costTypeDetailVo);
                }
            }
        }
        //禁用业务日志
        SerializableBiConsumer<CostTypeDetailLogEventListener, CostTypeDetailLogEventDto> onDisable =
                CostTypeDetailLogEventListener::onDisable;
        for (CostTypeDetailVo vo : costTypeDetailVos) {
            CostTypeDetailLogEventDto logEventDto = new CostTypeDetailLogEventDto();
            logEventDto.setOriginal(vo);
            this.nebulaNetEventClient.publish(logEventDto, CostTypeDetailLogEventListener.class, onDisable);
        }
    }

    /**
     * 生成操作标记
     */
    @Override
    public String preSave() {
        String prefix = UUID.randomUUID().toString();
        // 1天后过期
        this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, TimeUnit.MILLISECONDS.convert(1, TimeUnit.DAYS));
        return prefix;
    }

    @Override
    public Set<CostTypeDetailVo> findByCategoryCode(String categoryCode) {
        if (StringUtils.isBlank(categoryCode)) {
            return Collections.emptySet();
        }
        List<CostTypeMapping> costTypeMappings = this.costTypeMappingService.findByCategoryCode(categoryCode);
        if (CollectionUtils.isEmpty(costTypeMappings)) {
            return Collections.emptySet();
        }
        List<String> detailCodes = costTypeMappings.stream().map(CostTypeMapping::getDetailCode).collect(Collectors.toList());
        List<CostTypeDetail> costTypeDetails = this.costTypeDetailRepository.findByCodes(detailCodes);
        if (CollectionUtils.isEmpty(costTypeDetails)) {
            return Collections.emptySet();
        }
        Collection<CostTypeDetailVo> costTypeDetailVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeDetails, CostTypeDetail.class, CostTypeDetailVo.class, LinkedHashSet.class, ArrayList.class);
        Collection<CostTypeMappingVo> costTypeMappingVos = this.nebulaToolkitService.copyCollectionByWhiteList(costTypeMappings, CostTypeMapping.class, CostTypeMappingVo.class, LinkedHashSet.class, ArrayList.class);
        Set<CostTypeDetailVo> enableCostTypeDetailVos = costTypeDetailVos.stream().filter(item -> EnableStatusEnum.ENABLE.getCode().equals(item.getEnableStatus())).collect(Collectors.toSet());
        enableCostTypeDetailVos.forEach(item -> {
            // 处理支付方式
            item.setPayBys(Sets.newLinkedHashSet(Arrays.asList(StringUtils.split(item.getPayBy(), ","))));
        });
        return Sets.newLinkedHashSet(enableCostTypeDetailVos);
    }

    @Override
    public boolean existByApprovalCollect(String code) {
        return NumberUtils.compare(this.costTypeDetailSettingStrategyRepository.countByApprovalCollect(code), 0) > 0;
    }

    @Override
    public List<PayByStrategy> findAllPayBy() {
        if (CollectionUtils.isEmpty(payByStrategies)) {
            return Collections.emptyList();
        }
        return this.payByStrategies;
    }

    @Override
    public Set<String> findCodeByCondition(CostTypeDetailsDto dto) {
        return this.costTypeDetailRepository.findCodeByCondition(dto);
    }


    @Override
    public List<CostTypeDetailVo> findListByNames(List<String> nameList) {
        List<List<String>> partitionList = Lists.partition(nameList, 800);
        String tenantCode = TenantUtils.getTenantCode();
        List<CostTypeDetail> dataList = Lists.newArrayList();
        for (List<String> strings : partitionList) {
            List<CostTypeDetail> list = costTypeDetailRepository.findListByNames(nameList, tenantCode);
            if (!CollectionUtils.isEmpty(list)) {
                dataList.addAll(list);
            }
        }
        return (List<CostTypeDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostTypeDetail.class, CostTypeDetailVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 查询需要推送SFA的活动细类
     *
     * @return
     */
    @Override
    public List<CostTypeDetailVo> findListByPushSfa() {
        List<CostTypeDetail> dataList = costTypeDetailRepository.findListByPushSfa();
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return (List<CostTypeDetailVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostTypeDetail.class, CostTypeDetailVo.class,
                HashSet.class, ArrayList.class);
    }

    @Override
    public List<CostTypeDetailVo> findByCategoryCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        List<CostTypeDetail> costTypeDetailList = this.costTypeDetailRepository.findByCategoryCodes(codes);
        return BeanUtil.copyToList(costTypeDetailList, CostTypeDetailVo.class);
    }


    /**
     * 通过活动细类查询
     *
     * @param codes
     * @return
     */
    @Override
    public Map<String, List<CostTypeDetailCollectVo>> findCollectByCodes(List<String> codes) {
        List<CostTypeDetailCollectVo> collectVos = costTypeDetailCollectRepository.findListByCodes(codes);
        if (CollectionUtils.isEmpty(collectVos)) {
            return Maps.newHashMap();
        }
        Map<String, List<CostTypeDetailCollectVo>> map = collectVos.stream().collect(Collectors.groupingBy(CostTypeDetailCollectVo::getDetailCode));
        return map;
    }

    /**
     * 通过条件查询活动细类
     *
     * @param dto
     * @return
     */
    @Override
    public List<String> findListByCondition(CostTypeDetailsDto dto) {
        return costTypeDetailRepository.findListByCondition(dto);
    }


    @Override
    public Map<String, Set<String>> findPayBysByDetailCodes(List<String> detailCodes) {
        List<CostTypeDetail> costTypeDetails = costTypeDetailRepository.findPayBysByDetailCodes(detailCodes);
        Map<String, Set<String>> map = costTypeDetails.stream().filter(x->ObjectUtils.isNotEmpty(x.getPayBy()))
                .collect(Collectors.toMap(x -> x.getDetailCode(), x -> Sets.newHashSet(Arrays.asList(x.getPayBy().split(",")))));
        return map;
    }

    /**
     * 验证与操作标记
     */
    private void validationPrefix(CostTypeDetailVo costTypeDetailVo) {
        // 验证重复提交标识
        String prefix = costTypeDetailVo.getPrefix();
        Validate.notBlank(prefix, "错误的预操作标记，请检查!!");
        Validate.isTrue(StringUtils.isNotBlank(this.redisMutexService.getMCode(GENERATE_PREFIX, prefix)), "没有发现预操作标记，可能是因为重复操作的原因!");
        boolean isLock = false;
        try {
            if (isLock = this.redisMutexService.tryLock(prefix, TimeUnit.MILLISECONDS, 1)) {
                this.redisMutexService.setMCode(GENERATE_PREFIX, prefix, prefix, 1L);
            } else {
                throw new IllegalArgumentException("请不要重复操作!!");
            }
        } finally {
            if (isLock) {
                this.redisMutexService.unlock(prefix);
            }
        }
    }

    /**
     * 创建验证
     *
     * @param costTypeDetailVo
     */
    private void createValidate(CostTypeDetailVo costTypeDetailVo) {
        Validate.notNull(costTypeDetailVo, "新增时，对象信息不能为空！");
        costTypeDetailVo.setId(null);
        Validate.notBlank(costTypeDetailVo.getDetailName(), "新增数据时，活动细类名称不能为空！");
        Validate.notEmpty(costTypeDetailVo.getPayBys(), "新增数据时，支付方式不能为空！");
        if (BooleanEnum.TRUE.getCapital().equals(costTypeDetailVo.getIsSendSfa())) {
            Validate.isTrue(StringUtils.isNotBlank(costTypeDetailVo.getExecutionType()), "请选择“执行类型”！");
        }
    }

    /**
     * 修改验证
     *
     * @param costTypeDetailVo
     */
    private void updateValidate(CostTypeDetailVo costTypeDetailVo) {
        Validate.notNull(costTypeDetailVo, "修改时，对象信息不能为空！");
        Validate.notBlank(costTypeDetailVo.getId(), "修改数据时，主键不能为空！");
        Validate.notBlank(costTypeDetailVo.getDetailName(), "修改数据时，活动细类名称不能为空！");
        Validate.notEmpty(costTypeDetailVo.getPayBys(), "修改数据时，支付方式不能为空！");
        if (BooleanEnum.TRUE.getCapital().equals(costTypeDetailVo.getIsSendSfa())) {
            Validate.isTrue(StringUtils.isNotBlank(costTypeDetailVo.getExecutionType()), "请选择“执行类型”！");
        }
    }

    /**
     * 将树形结构降维
     */
    private void flatStrategySettingStructs(StrategySettingStruct struct, List<StrategySettingStruct> result) {
        result.add(struct);
        if (!CollectionUtils.isEmpty(struct.getChildren())) {
            for (StrategySettingStruct child : struct.getChildren()) {
                this.flatStrategySettingStructs(child, result);
            }
        }
    }

    private void validateBase(CostTypeDetailSettingStrategy settingStrategy) {
        Validate.notBlank(settingStrategy.getName(), "策略项名称不能为空");
        Validate.notBlank(settingStrategy.getCode(), "策略项【%s】编码不能为空", settingStrategy.getName());
        Validate.notBlank(settingStrategy.getType(), "策略项【%s】类型不能为空", settingStrategy.getName());
        Validate.notBlank(settingStrategy.getValueType(), "策略项【%s】值类型不能为空", settingStrategy.getName());
        Validate.notBlank(settingStrategy.getSettingManageCode(), "策略项【%s】配置编码不能为空", settingStrategy.getName());
        Validate.notNull(settingStrategy.getNecessary(), "策略项【%s】是否必需属性不能为空", settingStrategy.getName());
        Validate.notNull(settingStrategy.getDisplay(), "策略项【%s】是否显示属性不能为空", settingStrategy.getName());
        Validate.notNull(settingStrategy.getSortIndex(), "策略项【%s】排序值不能为空", settingStrategy.getName());
        if (StringUtils.equals(settingStrategy.getValueType(), StrategySettingValueType.DATE.getCode())) {
            Validate.notBlank(settingStrategy.getDateFormat(), "策略项结构【%s】时间格式不能为空", settingStrategy.getName());
        }
        if (settingStrategy.getNecessary() && (settingStrategy.getDisplay() != null && Boolean.TRUE.equals(settingStrategy.getDisplay()))) {
            Validate.notBlank(settingStrategy.getValue(), "策略项【%s】值信息不能为空", settingStrategy.getName());
        }
    }

    private void validateDisplaySpecial(CostTypeDetailSettingStrategy struct, List<CostTypeDetailSettingStrategy> specialStructs) {
        if (struct == null) {
            return;
        }
        this.validateDisplaySpecialForStruct(struct, specialStructs);
        if (StringUtils.equals(struct.getCode(), ControlActivityExpensesSetting.class.getSimpleName())) {
            CostTypeDetailSettingStrategy collectDistributionOrdersSetting = specialStructs.stream().filter(e -> StringUtils.equals(e.getCode(), CollectDistributionOrdersSetting.class.getSimpleName())).findFirst().orElse(null);
            CostTypeDetailSettingStrategy signDisplaySetting = specialStructs.stream().filter(e -> StringUtils.equals(e.getCode(), SignDisplaySetting.class.getSimpleName())).findFirst().orElse(null);
            Validate.isTrue(!(collectDistributionOrdersSetting == null && signDisplaySetting == null), "验证是否显示时，【是否签署陈列协议】和【是否采集分销订单】不能同时隐藏");
            if ((collectDistributionOrdersSetting != null && collectDistributionOrdersSetting.getValue() != null && StringUtils.equals(collectDistributionOrdersSetting.getValue().toString(), Boolean.TRUE.toString())) ||
                    signDisplaySetting != null && signDisplaySetting.getValue() != null && StringUtils.equals(signDisplaySetting.getValue().toString(), Boolean.TRUE.toString())) {
                Validate.notNull(struct.getDisplay(), "验证是否显示时，策略项【%s】是否显示属性不能为空", struct.getName());
                Validate.isTrue(StringUtils.equals(struct.getDisplay().toString(), Boolean.TRUE.toString()), "验证是否显示时，策略项【%s】是否显示属性不能为隐藏", struct.getName());
            } else {
                Validate.notNull(struct.getDisplay(), "验证是否显示时，策略项【%s】是否显示属性不能为空", struct.getName());
                Validate.isTrue(StringUtils.equals(struct.getDisplay().toString(), Boolean.FALSE.toString()), "验证是否显示时，策略项【%s】是否显示属性不能为显示", struct.getName());
            }
        }
    }

    private void validateDisplaySpecialForStruct(CostTypeDetailSettingStrategy struct, List<CostTypeDetailSettingStrategy> specialStructs) {
        if (struct == null) {
            return;
        }
        Validate.notEmpty(specialStructs, "验证是否显示时，指定的策略项信息不能为空");
        if (StringUtils.equals(struct.getCode(), ControlActivityExpensesSetting.class.getSimpleName())) {
            boolean matched = specialStructs.stream().anyMatch(e -> StringUtils.equals(e.getCode(), CollectDistributionOrdersSetting.class.getSimpleName()) || StringUtils.equals(e.getCode(), SignDisplaySetting.class.getSimpleName()));
            Validate.isTrue(matched, "验证是否显示时，如果策略项【%s】存在，【是否签署陈列协议】和【是否采集分销订单】不能同时隐藏", struct.getName());
        }
    }
}