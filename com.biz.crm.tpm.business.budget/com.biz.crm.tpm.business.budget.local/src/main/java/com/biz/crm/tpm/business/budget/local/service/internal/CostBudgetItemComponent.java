package com.biz.crm.tpm.business.budget.local.service.internal;

import com.biz.crm.tpm.business.activities.marketingplan.eunm.MarketingPlanSchemeTypeEnum;
import com.biz.crm.tpm.business.budget.local.entity.CostBudgetItem;
import com.biz.crm.tpm.business.budget.local.mapper.CostBudgetItemMapper;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/12 21:52
 */
@Component
public class CostBudgetItemComponent {

    @Resource
    private CostBudgetItemMapper costBudgetItemMapper;

    /**
     * 组装明细
     *
     * @param list
     * @param itemList
     * @param costBudgetCode
     */
    public void buildCostBudgetItem(List<CostBudgetItem> list, List<CostBudgetItem> itemList, String costBudgetCode) {
        List<CostBudgetItem> planCaseList = costBudgetItemMapper.findMarketingPlanCaseList(costBudgetCode);
        List<CostBudgetItem> withholdingList = costBudgetItemMapper.findWithholdingList(costBudgetCode);
        List<CostBudgetItem> endCaseList = costBudgetItemMapper.findEndCaseList(costBudgetCode);
        Map<String, CostBudgetItem> withholdingMap = Maps.newHashMap();
        Map<String, CostBudgetItem> endCaseMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(itemList)) {
            list.addAll(itemList);
        }
        if (CollectionUtils.isEmpty(planCaseList)) {
            if (!CollectionUtils.isEmpty(withholdingList)) {
                list.addAll(withholdingList);
            }
        } else {
            if (!CollectionUtils.isEmpty(withholdingList)) {
                withholdingMap = withholdingList.stream().collect(Collectors.toMap(x -> x.getBusinessCode(), Function.identity()));
            }
            if (!CollectionUtils.isEmpty(endCaseList)) {
                endCaseMap = endCaseList.stream().collect(Collectors.toMap(x -> x.getBusinessCode(), Function.identity()));
            }
            List<String> originalSchemeDetailCodeList = planCaseList.stream().filter(x-> ObjectUtils.isNotEmpty(x.getOriginalSchemeDetailCode()) &&
                            ProcessStatusEnum.COMMIT.getDictCode().equals(x.getProcessStatus()))
                    .map(x->x.getOriginalSchemeDetailCode()).distinct().collect(Collectors.toList());
            List<CostBudgetItem> newPlanCaseList = planCaseList.stream()
                    .filter(item ->
                            !(MarketingPlanSchemeTypeEnum.change.getCode().equals(item.getSchemeType())
                                    && ProcessStatusEnum.PASS.getDictCode().equals(item.getProcessStatus())) &&
                                    !originalSchemeDetailCodeList.contains(item.getBusinessCode()))
                    .collect(Collectors.toList());
            for (CostBudgetItem item : newPlanCaseList) {
                if (endCaseMap.containsKey(item.getBusinessCode())) {
                    list.add(endCaseMap.get(item.getBusinessCode()));
                } else if (withholdingMap.containsKey(item.getBusinessCode())) {
                    list.add(withholdingMap.get(item.getBusinessCode()));
                } else {
                    list.add(item);
                }
            }
            if (!CollectionUtils.isEmpty(withholdingList)) {
                List<String> businessCodes = newPlanCaseList.stream().map(x -> x.getBusinessCode()).collect(Collectors.toList());
                withholdingList = withholdingList.stream().filter(x -> !businessCodes.contains(x.getBusinessCode()))
                        .collect(Collectors.toList());
                list.addAll(withholdingList);
            }
        }
    }
}
