package com.biz.crm.tpm.business.budget.local.service.internal;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.budget.local.entity.CostBudget;
import com.biz.crm.tpm.business.budget.local.repository.CostBudgetRepository;
import com.biz.crm.tpm.business.budget.local.service.CostBudgetBuildUtil;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetItemDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeCategoryDto;
import com.biz.crm.tpm.business.budget.sdk.enums.BudgetTypeEnum;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetItemSourceType;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetOperateType;
import com.biz.crm.tpm.business.budget.sdk.event.CostBudgetEventListener;
import com.biz.crm.tpm.business.budget.sdk.event.log.CostBudgetLogEventListener;
import com.biz.crm.tpm.business.budget.sdk.service.*;
import com.biz.crm.tpm.business.budget.sdk.strategy.BudgetControlTypeStrategy;
import com.biz.crm.tpm.business.budget.sdk.vo.*;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContext;
import com.bizunited.nebula.mars.sdk.context.MarsAuthorityContextHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.biz.crm.tpm.business.budget.sdk.constant.CostBudgetConstant.COSTBUDGET_RULE_CODE;

/**
 * 费用预算(CostBudget)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
@Service
@Slf4j
public class CostBudgetServiceImpl implements CostBudgetVoService {

    private static final String BUDGET_GLOBAL_REDIS_LOCK_KEY = "BUDGET_GLOBAL_REDIS_LOCK_KEY";

    private static final Integer MAX_TIMES = 20;

    @Autowired
    private CostBudgetRepository costBudgetRepository;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired
    private CostBudgetItemVoService costBudgetItemService;
    @Autowired
    private GenerateCodeService generateCodeService;
    @Autowired
    private CostTypeCategoryVoService costTypeCategoryVoService;
    @Autowired(required = false)
    private List<CostBudgetEventListener> costBudgetEventListeners;
    @Autowired
    private RedisMutexService redisMutexService;
    @Autowired
    private BudgetSubjectsVoService budgetSubjectsVoService;
    @Autowired
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired(required = false)
    private CostBudgetLockVoService costBudgetLockVoService;
    @Resource
    private LoginUserService loginUserService;

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    @Override
    public Page<CostBudgetVo> findByConditions(Pageable pageable, CostBudgetDto dto) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 50);
        }
        if (dto == null) {
            dto = new CostBudgetDto();
        }
        if (StringUtils.isBlank(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        if (StringUtils.isBlank(dto.getDelFlag())) {
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        if(StringUtils.isNotBlank(dto.getUserName())){
            List<OrgVo> deptCodes = orgVoService.findAllParentTreeByUserName(dto.getUserName());
            if(!CollectionUtils.isEmpty(deptCodes)){
                dto.setDeptCodeSet(deptCodes.stream().filter(item->StringUtils.isNotBlank(item.getOrgCode())).map(item->item.getOrgCode()).collect(Collectors.toSet()));
            }
        }
        Page<CostBudgetVo> data = costBudgetRepository.findByConditions(pageable, dto);
        List<CostBudgetVo> records = data.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new Page<>();
        }
        CostBudgetBuildUtil.buildCostBudget(records);
        List<String> subjectCodes = records.stream().map(CostBudgetVo::getBudgetSubjectCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, BigDecimal> taxRateMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(subjectCodes)) {
            log.info("budgetManageTenantCode: {}", TenantUtils.getTenantCode());
            log.info("budgetManageSubjectCodes:{}", subjectCodes);
            // 去除权限校验
            MarsAuthorityContext marsAuthorityContext = MarsAuthorityContextHolder.getContext();
            marsAuthorityContext.setListCode(null);
            List<BudgetSubjectsVo> subjectVos = budgetSubjectsVoService.findByCodeList(subjectCodes);
            log.info("budgetManageSubjectVos:{}", JSON.toJSONString(subjectVos));
            taxRateMap = subjectVos.stream().filter(e -> Objects.nonNull(e.getTaxRate())).collect(Collectors.toMap(BudgetSubjectsVo::getBudgetSubjectsCode, BudgetSubjectsVo::getTaxRate));
        }
        for (CostBudgetVo record : records) {
            record.setNoTaxAdjustBalanceAmount(record.getAdjustBalanceAmount());
            if (taxRateMap.containsKey(record.getBudgetSubjectCode())) {
                record.setTaxRate(taxRateMap.get(record.getBudgetSubjectCode()));
                record.setTaxRateStr(null == record.getTaxRate()? "" : record.getTaxRate().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
                record.setNoTaxAdjustBalanceAmount(record.getAdjustBalanceAmount().divide(new BigDecimal(1).add(null == record.getTaxRate()? new BigDecimal(0) : record.getTaxRate()), 2,BigDecimal.ROUND_HALF_UP));
            }
        }
        return data;
    }

    @Override
    public List<CostBudgetVo> findByConditions(CostBudgetDto dto) {
        if (dto == null) {
            dto = new CostBudgetDto();
        }
        if (StringUtils.isBlank(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        if (StringUtils.isBlank(dto.getDelFlag())) {
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        List<CostBudget> costBudgetEntities = costBudgetRepository.findByConditions(dto);
        if (CollectionUtils.isEmpty(costBudgetEntities)) {
            return Lists.newArrayList();
        }

//    Collection<CostBudgetVo> costBudgetVos = this.prefectFinalBanlances(costBudgetEntities);
//    if (dto.getHasAbleBalance() != null) {
//      if (dto.getHasAbleBalance()) {
//        //查询可用余额大于0，且科目预算控制类型为不控制的，即使可用余额不为0，也要展示
//        costBudgetVos = costBudgetVos.stream().filter(e -> e.getFinalBalance().compareTo(BigDecimal.ZERO) > 0 || e.isDoNot()).collect(Collectors.toList());
//      } else {
//        //查询无可用余额数据
//        costBudgetVos = costBudgetVos.stream().filter(e -> e.getFinalBalance().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
//      }
//    }
        List<CostBudgetVo> list = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(costBudgetEntities, CostBudget.class, CostBudgetVo.class, LinkedHashSet.class, ArrayList.class));
        CostBudgetBuildUtil.buildCostBudget(list);
        return list;
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @Override
    public CostBudgetVo findById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        CostBudget costBudget = costBudgetRepository.findByIdAndTenantCode(id, TenantUtils.getTenantCode());
        List<CostBudgetVo> list = Lists.newArrayList(this.prefectFinalBanlance(costBudget));
        CostBudgetBuildUtil.buildCostBudget(list);
        return list.get(0);
    }

    @Override
    public List<CostBudgetVo> findByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<CostBudget> costBudgets = costBudgetRepository.listByIdsAndTenantCode(ids, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(costBudgets)) {
            return Lists.newArrayList();
        }
        List<CostBudgetVo> list = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(costBudgets, CostBudget.class, CostBudgetVo.class, HashSet.class, ArrayList.class));
        CostBudgetBuildUtil.buildCostBudget(list);
        return list;
    }

    private CostBudgetVo prefectFinalBanlance(CostBudget costBudget) {
        if (costBudget == null) {
            return null;
        }
        CostBudgetVo result = nebulaToolkitService.copyObjectByWhiteList(costBudget, CostBudgetVo.class, HashSet.class, ArrayList.class);
        List<CostBudgetItemVo> costBudgetItems = costBudgetItemService.findByCostBudgetCode(costBudget.getCode());
        if (CollectionUtils.isEmpty(costBudgetItems)) {
            return result;
        }
        CostBudgetItemVo item = costBudgetItems.stream().max(Comparator.comparing(CostBudgetItemVo::getCreateTime)).orElse(null);
        if (item == null) {
            return result;
        }
        result.setFinalBalance(item.getFinalBalance());
        return result;
    }

    private List<CostBudgetVo> prefectFinalBanlances(List<CostBudget> costBudgets) {
        if (CollectionUtils.isEmpty(costBudgets)) {
            return Lists.newArrayList();
        }
        List<CostBudgetVo> result = Lists.newArrayList();
        Map<String, List<CostBudgetItemVo>> costBudgetItemMap = costBudgetItemService.findByCostBudgetCodes(costBudgets.stream().map(CostBudget::getCode).collect(Collectors.toList()));
        List<BudgetSubjectsVo> budgetSubjects = budgetSubjectsVoService.findByCodes(costBudgets.stream().map(CostBudget::getBudgetSubjectCode).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(budgetSubjects)) {
            return Lists.newArrayList();
        }
        Map<String, BudgetSubjectsVo> budgetSubjectsMap = budgetSubjects.stream().collect(Collectors.toMap(BudgetSubjectsVo::getBudgetSubjectsCode, e -> e));
        for (CostBudget costBudget : costBudgets) {
            CostBudgetVo costBudgetResult = nebulaToolkitService.copyObjectByWhiteList(costBudget, CostBudgetVo.class, HashSet.class, ArrayList.class);
            result.add(costBudgetResult);
            List<CostBudgetItemVo> costBudgetItemVos = costBudgetItemMap.get(costBudget.getCode());
            if (CollectionUtils.isEmpty(costBudgetItemVos)) {
                continue;
            }
            CostBudgetItemVo item = costBudgetItemVos.stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
            if (item == null) {
                continue;
            }
            costBudgetResult.setFinalBalance(item.getFinalBalance());
            BudgetSubjectsVo budgetSubjectsVo = budgetSubjectsMap.get(costBudgetResult.getBudgetSubjectCode());
            if (budgetSubjectsVo != null) {
                costBudgetResult.setDoNot(budgetSubjectsVo.getControlTypeCode().equals("DO_NOT"));
            }
        }
        return result;
    }

    /**
     * 重复性校验
     * 同年度-同季度-同月份-同预算科目-同预算类型下，有且只有一条生效的费用预算
     */
    private boolean validateRepeatabilityOnCreate(CostBudgetDto costBudget) {

        costBudget.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        List<CostBudget> result = costBudgetRepository.findByConditions(costBudget);
        return !CollectionUtils.isEmpty(result);
    }

    /**
     * 重复性校验
     * 同年度-同季度-同月份-同预算科目-同预算类型下，有且只有一条生效的费用预算
     */
    private boolean validateRepeatabilityOnUpdate(CostBudgetDto costBudget, CostBudget dbCostBudget) {
        Validate.notNull(costBudget, "费用预算数据不能为空");
        Validate.notBlank(costBudget.getType(), "预算类型不能为空");
        Validate.notBlank(costBudget.getBudgetSubjectCode(), "预算科目不能为空");
        Validate.notBlank(costBudget.getTenantCode(), "租户编码不能为空");
        //注：此处其实隐藏了"费用预算类型"所携带的组织、客户、渠道、产品、产品层级的参数信息，都包含在dto中
        costBudget.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        List<CostBudget> result = costBudgetRepository.findByConditions(costBudget);
        if (CollectionUtils.isEmpty(result)) {
            return false;
        } else {
            Validate.isTrue(dbCostBudget.getId().equals(costBudget.getId()), "变更的费用预算信息存在重复数据，请检查");
            return result.size() > 1;
        }
    }

    @Override
    public CostBudgetVo findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        CostBudget costBudget = costBudgetRepository.findByCodeAndTenantCode(code, TenantUtils.getTenantCode());
        CostBudgetVo vo = this.prefectFinalBanlance(costBudget);
        List<CostBudgetVo> list = Lists.newArrayList(vo);
        CostBudgetBuildUtil.buildCostBudget(list);
        return list.get(0);
    }

    @Override
    public List<CostBudgetVo> findByBudgetSubjectCode(String budgetSubjectCode) {
        if (StringUtils.isBlank(budgetSubjectCode)) {
            return Lists.newArrayList();
        }
        List<CostBudget> costBudgets = costBudgetRepository.findByBudgetSubjectCodeAndTenantCode(budgetSubjectCode, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(costBudgets)) {
            return Lists.newArrayList();
        }
        List<CostBudgetVo> list = (List<CostBudgetVo>) nebulaToolkitService.copyCollectionByWhiteList(costBudgets, CostBudget.class, CostBudgetVo.class, HashSet.class, ArrayList.class);
        CostBudgetBuildUtil.buildCostBudget(list);
        return list;

    }

    @Override
    public List<CostBudgetVo> findByCodes(Set<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        List<CostBudget> costBudgets = costBudgetRepository.findByCodesAndTenantCode(codes, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(costBudgets)) {
            return Lists.newArrayList();
        }
        List<CostBudgetVo> costBudgetVos = Lists.newArrayList();
        for (CostBudget costBudget : costBudgets) {
            CostBudgetVo vo = this.prefectFinalBanlance(costBudget);
            if (vo != null) {
                costBudgetVos.add(vo);
            }
        }
        CostBudgetBuildUtil.buildCostBudget(costBudgetVos);
        return costBudgetVos;
    }

    /**
     * 新增数据
     *
     * @param costBudget 实体对象
     * @return 新增结果
     */
    @Transactional
    @Override
    public CostBudgetVo create(CostBudgetDto costBudget) {
        costBudget.setBudgetType(BudgetTypeEnum.YEAR.getCode());
        this.createValidate(costBudget);
        FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
        Validate.notNull(loginUserDetails, "未找到当前登陆人信息！");
        costBudget.setOrgCode(loginUserDetails.getOrgCode());
        costBudget.setOrgName(loginUserDetails.getOrgName());
        costBudget.setPositionCode(loginUserDetails.getPostCode());
        costBudget.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        costBudget.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        costBudget.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
        CostBudget entity = nebulaToolkitService.copyObjectByWhiteList(costBudget, CostBudget.class, HashSet.class, ArrayList.class);
        entity.setTenantCode(TenantUtils.getTenantCode());
        entity.refreshBalanceAmount();
        // 根据部门编码做权限将部门赋值给权限字段
        entity.setOrgCode(entity.getDepartmentOneCode());
        costBudgetRepository.save(entity);
        //构造费用预算期初明细
        CostBudgetItemDto itemDto = new CostBudgetItemDto();
        itemDto.setOperateType(CostBudgetOperateType.INITIALIZATION.getCode());
        itemDto.setBalance(BigDecimal.ZERO);
        itemDto.setCostBudgetCode(costBudget.getCode());
        itemDto.setInitialAmount(costBudget.getInitialAmount());
        itemDto.setFinalBalance(costBudget.getInitialAmount());
        itemDto.setOperateAmount(costBudget.getInitialAmount());
        itemDto.setTenantCode(costBudget.getTenantCode());
        itemDto.setItemRemark(costBudget.getOperateRemark());
        itemDto.setBusinessCode(costBudget.getCode());
        itemDto.setSource(CostBudgetItemSourceType.YEAR_COST_BUDGET.getDescr());
        itemDto.setCustomerCode(costBudget.getCustomerCode());
        itemDto.setCustomerName(costBudget.getCustomerName());
        itemDto.setBudgetSubjectCode(costBudget.getBudgetSubjectCode());
        itemDto.setBudgetSubjectName(costBudget.getBudgetSubjectName());
        itemDto.setDepartmentCode(costBudget.getDepartmentOneCode());
        itemDto.setDepartmentName(costBudget.getDepartmentOneName());
        costBudgetItemService.create(Lists.newArrayList(itemDto));
        //新增业务日志
        CostBudgetLogEventDto logEventDto = new CostBudgetLogEventDto();
        logEventDto.setOriginal(null);
        costBudget.setId(entity.getId());
        logEventDto.setNewest(costBudget);
        SerializableBiConsumer<CostBudgetLogEventListener, CostBudgetLogEventDto> onCreate =
                CostBudgetLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(logEventDto, CostBudgetLogEventListener.class, onCreate);
        return nebulaToolkitService.copyObjectByWhiteList(entity, CostBudgetVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 修改新据
     *
     * @param costBudget 实体对象
     * @return 修改结果
     */
    @Transactional
    @Override
    public CostBudgetVo update(CostBudgetDto costBudget) {
        this.updateValidate(costBudget);
        //需要进行redis锁操作
        CostBudget dbCostBudget;
        CostBudgetDto oldDto;
        boolean lock = costBudgetLockVoService.lock(costBudget.getCode(), TimeUnit.SECONDS, 10);
        Validate.isTrue(lock, "操作预算失败，获取操作锁失败！");
        try {
            dbCostBudget = costBudgetRepository.findByIdAndTenantCode(costBudget.getId(), TenantUtils.getTenantCode());
            Validate.notNull(dbCostBudget, "根据指定的id键值，未能获取到相应信息");
            Validate.isTrue(StringUtils.equals(dbCostBudget.getDelFlag(), DelFlagStatusEnum.NORMAL.getCode()), "不能对【已删除】费用预算进行编辑");
            Validate.isTrue(StringUtils.equals(dbCostBudget.getCode(), costBudget.getCode()), "费用预算编码不一致，请检查");
            List<CostBudgetItemVo> itemVos = costBudgetItemService.findByCostBudgetCode(costBudget.getCode());
            Validate.notEmpty(itemVos, "费用预算明细项不能为空");
            boolean matched = itemVos.stream().anyMatch(e -> !e.getOperateType().equals(CostBudgetOperateType.INITIALIZATION.getCode()));
            Validate.isTrue(!matched, "该费用预算已产生明细信息或为划入明细，不能进行编辑操作");
            CostBudgetItemVo itemVo = itemVos.stream().filter(e -> e.getOperateType().equals(CostBudgetOperateType.INITIALIZATION.getCode())).findFirst().orElse(null);
            Validate.notNull(itemVo, "该费用预算【%s】没有期初信息，请检查", costBudget.getCode());
            oldDto = this.nebulaToolkitService.copyObjectByWhiteList(dbCostBudget, CostBudgetDto.class, HashSet.class, ArrayList.class);
            dbCostBudget.setChannelCode(costBudget.getChannelCode());
            dbCostBudget.setChannelName(costBudget.getChannelName());
            dbCostBudget.setCustomerCode(costBudget.getCustomerCode());
            dbCostBudget.setCustomerName(costBudget.getCustomerName());
            dbCostBudget.setBudgetSubjectCode(costBudget.getBudgetSubjectCode());
            dbCostBudget.setBudgetSubjectName(costBudget.getBudgetSubjectName());
            dbCostBudget.setMonth(costBudget.getMonth());
            dbCostBudget.setOrgCode(costBudget.getOrgCode());
            dbCostBudget.setOrgName(costBudget.getOrgName());
            dbCostBudget.setProductCode(costBudget.getProductCode());
            dbCostBudget.setProductName(costBudget.getProductName());
            dbCostBudget.setProductLevelCode(costBudget.getProductLevelCode());
            dbCostBudget.setProductLevelName(costBudget.getProductLevelName());
            dbCostBudget.setQuarter(costBudget.getQuarter());
            dbCostBudget.setTerminalCode(costBudget.getTerminalCode());
            dbCostBudget.setTerminalName(costBudget.getTerminalName());
            dbCostBudget.setType(costBudget.getType());
            dbCostBudget.setYear(costBudget.getYear());
            dbCostBudget.setRemark(costBudget.getRemark());
            dbCostBudget.setInitialAmount(costBudget.getInitialAmount());
            dbCostBudget.setDepartmentOneCode(costBudget.getDepartmentOneCode());
            dbCostBudget.setDepartmentOneName(costBudget.getDepartmentOneName());
            dbCostBudget.setLevelNum(costBudget.getLevelNum());
            dbCostBudget.setCostCenterCode(costBudget.getCostCenterCode());
            dbCostBudget.setCostCenterName(costBudget.getCostCenterName());
            dbCostBudget.setCompanyCode(costBudget.getCompanyCode());
            dbCostBudget.setCompanyName(costBudget.getCompanyName());
            dbCostBudget.setYearMonthLy(costBudget.getYearMonthLy());
            dbCostBudget.setTenantCode(TenantUtils.getTenantCode());
            dbCostBudget.setUniqueKey(costBudget.getUniqueKey());
            dbCostBudget.refreshBalanceAmount();

            // 根据部门编码做权限将部门赋值给权限字段
            dbCostBudget.setOrgCode(dbCostBudget.getDepartmentOneCode());
            costBudgetRepository.saveOrUpdate(dbCostBudget);
            //更新明细项
            CostBudgetItemDto itemDto = new CostBudgetItemDto();
            itemDto.setId(itemVo.getId());
            itemDto.setOperateType(CostBudgetOperateType.INITIALIZATION.getCode());
            itemDto.setBalance(BigDecimal.ZERO);
            itemDto.setCostBudgetCode(costBudget.getCode());
            itemDto.setInitialAmount(costBudget.getInitialAmount());
            itemDto.setFinalBalance(costBudget.getInitialAmount());
            itemDto.setOperateAmount(costBudget.getInitialAmount());
            itemDto.setTenantCode(costBudget.getTenantCode());
            itemDto.setItemRemark(costBudget.getOperateRemark());
            itemDto.setBusinessCode(costBudget.getCode());
            itemDto.setSource(CostBudgetItemSourceType.YEAR_COST_BUDGET.getDescr());
            itemDto.setCustomerCode(costBudget.getCustomerCode());
            itemDto.setCustomerName(costBudget.getCustomerName());
            itemDto.setBudgetSubjectCode(costBudget.getBudgetSubjectCode());
            itemDto.setBudgetSubjectName(costBudget.getBudgetSubjectName());
            itemDto.setDepartmentCode(costBudget.getDepartmentOneCode());
            itemDto.setDepartmentName(costBudget.getDepartmentOneName());
            costBudgetItemService.updateForInitializationItem(itemDto);
        } finally {
            costBudgetLockVoService.unLock(costBudget.getCode());
        }

        CostBudgetVo result = nebulaToolkitService.copyObjectByWhiteList(dbCostBudget, CostBudgetVo.class, HashSet.class, ArrayList.class);
        //事件通知
        if (!CollectionUtils.isEmpty(costBudgetEventListeners)) {
            for (CostBudgetEventListener listener : costBudgetEventListeners) {
                listener.onUpdate(result);
            }
        }
        //更新业务日志
        CostBudgetLogEventDto logEventDto = new CostBudgetLogEventDto();
        logEventDto.setOriginal(oldDto);
        CostBudgetDto newDto = this.nebulaToolkitService.copyObjectByWhiteList(dbCostBudget, CostBudgetDto.class, HashSet.class, ArrayList.class);
        logEventDto.setNewest(newDto);
        SerializableBiConsumer<CostBudgetLogEventListener, CostBudgetLogEventDto> onUpdate =
                CostBudgetLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, CostBudgetLogEventListener.class, onUpdate);
        return result;
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     */
    @Transactional
    @Override
    public void delete(List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
        List<CostBudgetVo> costBudgetVos = this.findByIds(Sets.newHashSet(idList));
        Validate.notEmpty(costBudgetVos, "根据提供的ids主键集合，未能获取到相应信息");
        for (CostBudgetVo vo : costBudgetVos) {
            boolean exist = costBudgetItemService.existNoInitItemByCostBudgetCode(vo.getCode());
            Validate.isTrue(!exist, "费用预算【%s】存在【非期初】明细项，不能进行删除操作", vo.getCode());
            Validate.isTrue(ConfirmStatusEnum.UNCONFIRMED.getCode().equals(vo.getConfirmStatus()), "费用预算【%s】已确认，不能进行删除操作", vo.getCode());
        }
        //事件通知
        if (!CollectionUtils.isEmpty(costBudgetEventListeners)) {
            for (CostBudgetVo vo : costBudgetVos) {
                for (CostBudgetEventListener listener : costBudgetEventListeners) {
                    listener.onDeleted(vo);
                }
            }
        }
        //主表逻辑删除（明细项信息不做变更）
        this.costBudgetRepository.deleteByIds(idList);
        //删除业务日志
        List<CostBudgetDto> costBudgets = (List<CostBudgetDto>) this.nebulaToolkitService.copyCollectionByWhiteList(costBudgetVos, CostBudgetVo.class, CostBudgetDto.class, HashSet.class, ArrayList.class);
        SerializableBiConsumer<CostBudgetLogEventListener, CostBudgetLogEventDto> onDelete =
                CostBudgetLogEventListener::onDelete;
        for (CostBudgetDto costBudget : costBudgets) {
            CostBudgetLogEventDto logEventDto = new CostBudgetLogEventDto();
            logEventDto.setOriginal(costBudget);
            logEventDto.setNewest(null);
            this.nebulaNetEventClient.publish(logEventDto, CostBudgetLogEventListener.class, onDelete);
        }
    }

    /**
     * 创建验证
     */
    private void createValidate(CostBudgetDto costBudget) {
        Validate.notNull(costBudget, "对象信息不能为空！");
        Validate.isTrue(StringUtils.isBlank(costBudget.getId()), "新增时，主键id不能有值");
        costBudget.setId(null);
        if (StringUtils.isBlank(costBudget.getTenantCode())) {
            costBudget.setTenantCode(TenantUtils.getTenantCode());
        }
        this.validateBase(costBudget);
        this.validateProductInfo(costBudget);
        List<String> codeList = this.generateCodeService.generateCodeYearMonth(COSTBUDGET_RULE_CODE, 1);
        Validate.notEmpty(codeList, "添加信息时，生成费用预算编码失败！");
        costBudget.setCode(codeList.get(0));
    }

    /**
     * 修改验证
     */
    private void updateValidate(CostBudgetDto costBudget) {
        Validate.notNull(costBudget, "对象信息不能为空！");
        Validate.notBlank(costBudget.getId(), "修改时，主键id不能为空！");
        Validate.notBlank(costBudget.getCode(), "费用预算编码不能为空");
        this.validateBase(costBudget);
        this.validateProductInfo(costBudget);
        if (StringUtils.isBlank(costBudget.getTenantCode())) {
            costBudget.setTenantCode(TenantUtils.getTenantCode());
        }
    }

    private void validateBase(CostBudgetDto costBudget) {
        Validate.notNull(costBudget.getInitialAmount(), "期初金额不能为空");
        Validate.isTrue(costBudget.getInitialAmount().compareTo(BigDecimal.ZERO) >= 0, "期初金额必须大于等于0，请检查");
        Validate.notBlank(costBudget.getBudgetSubjectCode(), "预算科目编码不能为空");
        Validate.notBlank(costBudget.getBudgetSubjectName(), "预算科目名称不能为空");

        Validate.notBlank(costBudget.getYearMonthLy(), "预算年月不能为空");
        Validate.notBlank(costBudget.getDepartmentOneCode(), "部门编码，不能为空");
        Validate.notBlank(costBudget.getDepartmentOneName(), "部门名称，不能为空");
        Validate.notNull(costBudget.getLevelNum(), "部门层级，不能为空");
        Validate.notBlank(costBudget.getCostCenterCode(), "成本中心编码，不能为空");
        Validate.notBlank(costBudget.getCostCenterName(), "成本中心名称，不能为空");

        //年月+部门编码+成本中心编码+预算科目编码+公司代码+客户编码+品项编码+产品编码
        costBudget.setUniqueKey(costBudget.getYearMonthLy() + costBudget.getDepartmentOneCode() + costBudget.getCostCenterCode() + costBudget.getBudgetSubjectCode() +
                StringUtils.defaultIfBlank(costBudget.getCompanyCode(), "") +
                StringUtils.defaultIfBlank(costBudget.getCustomerCode(), "") +
                StringUtils.defaultIfBlank(costBudget.getItemCode(), "") +
                StringUtils.defaultIfBlank(costBudget.getProductCode(), ""));
        //注意：此处一定要先验证重复性，因为一旦把验重提后到"生成了编码之后"，那么数据就绝对不可能重复了
        CostBudgetDto dto = new CostBudgetDto();
        dto.setUniqueKey(costBudget.getUniqueKey());
        if (StringUtils.isNotBlank(costBudget.getId())) {
            dto.setExcludeId(costBudget.getId());
        }
        Validate.isTrue(!this.validateRepeatabilityOnCreate(dto), "该年度预算已存在【%s】，请检查", costBudget.getBudgetSubjectName());
    }

    private void validateProductInfo(CostBudgetDto costBudget) {
        boolean productFlag = StringUtils.isNotBlank(costBudget.getProductCode()) || StringUtils.isNotBlank(costBudget.getProductName());
        boolean productLevelFlag = StringUtils.isNotBlank(costBudget.getProductLevelCode()) || StringUtils.isNotBlank(costBudget.getProductLevelName());
        if (productFlag) {
            Validate.isTrue(StringUtils.isNotBlank(costBudget.getProductCode()) && StringUtils.isNotBlank(costBudget.getProductName()), "产品编码或名称不能为空");
        }
        if (productLevelFlag) {
            Validate.isTrue(StringUtils.isNotBlank(costBudget.getProductLevelCode()) && StringUtils.isNotBlank(costBudget.getProductLevelName()), "产品层级编码或名称不能为空");
        }
    }


    /**
     * 启禁用
     */
    @Override
    @Transactional
    public void updateEnableStatus(Set<String> ids, String enableStatus) {
        Validate.notEmpty(ids, "主键id集合不能为空");
        Validate.notBlank(enableStatus, "启禁用状态不能为空");
        Validate.isTrue(EnableStatusEnum.contains(enableStatus), "未知的启禁用状态【%s】，请检查", enableStatus);
        List<CostBudgetVo> costBudgetVos = this.findByIds(ids);
        Validate.notEmpty(costBudgetVos, "根据提供的ids主键集合，未能获取到相应信息");
        //事件通知
        if (!CollectionUtils.isEmpty(costBudgetEventListeners)) {
            for (CostBudgetVo vo : costBudgetVos) {
                for (CostBudgetEventListener listener : costBudgetEventListeners) {
                    if (StringUtils.equals(enableStatus, EnableStatusEnum.DISABLE.getCode())) {
                        listener.onDisable(vo);
                    } else {
                        listener.onEnable(vo);
                    }
                }
            }
        }
        costBudgetRepository.updateEnableStatus(ids, enableStatus);
        //启禁用业务日志
        List<CostBudgetDto> costBudgets = (List<CostBudgetDto>) this.nebulaToolkitService.copyCollectionByWhiteList(costBudgetVos, CostBudgetVo.class, CostBudgetDto.class, HashSet.class, ArrayList.class);
        SerializableBiConsumer<CostBudgetLogEventListener, CostBudgetLogEventDto> onUpdateEnable =
                CostBudgetLogEventListener::onUpdateEnable;
        for (CostBudgetDto costBudget : costBudgets) {
            CostBudgetLogEventDto logEventDto = new CostBudgetLogEventDto();
            logEventDto.setOriginal(costBudget);
            CostBudgetDto newDto = new CostBudgetDto();
            newDto.setEnableStatus(enableStatus);
            logEventDto.setNewest(newDto);
            this.nebulaNetEventClient.publish(logEventDto, CostBudgetLogEventListener.class, onUpdateEnable);
        }
    }

    @Override
    @Transactional
    public void tranfer(String costBudgetCodeOut, String costBudgetCodeIn, BigDecimal operateAmount, String operateRemark) {
        Validate.notBlank(costBudgetCodeOut, "调出方费用预算编码不能为空");
        Validate.notBlank(costBudgetCodeIn, "调入方费用预算编码不能为空");
        Validate.isTrue(!StringUtils.equals(costBudgetCodeIn, costBudgetCodeOut), "调入调出方不能相同");
        Validate.notNull(operateAmount, "操作金额不能为空");
        //需要进行redis锁操作(调入调出是针对两个费用预算进行操作)
        boolean hasLock = false;
        try {
            hasLock = redisMutexService.tryLock(BUDGET_GLOBAL_REDIS_LOCK_KEY, TimeUnit.SECONDS, MAX_TIMES);
            Validate.isTrue(hasLock, "系统网络繁忙，请稍后重试");
            CostBudgetVo out = this.findByCode(costBudgetCodeOut);
            Validate.notNull(out, "根据提供的费用预算编码【%s】，未能获取到相应信息", costBudgetCodeOut);
            Validate.isTrue(StringUtils.equals(out.getDelFlag(), DelFlagStatusEnum.NORMAL.getCode()), "不能对【已删除】费用预算进行操作");
            CostBudgetVo in = this.findByCode(costBudgetCodeIn);
            Validate.notNull(in, "根据提供的费用预算编码【%s】，未能获取到相应信息", costBudgetCodeIn);
            Validate.isTrue(StringUtils.equals(in.getDelFlag(), DelFlagStatusEnum.NORMAL.getCode()), "不能对【已删除】费用预算进行操作");
            //1.=====
            List<CostBudgetItemVo> costBudgetItemOuts = costBudgetItemService.findByCostBudgetCode(costBudgetCodeOut);
            Validate.notEmpty(costBudgetItemOuts, "调出方费用预算明细项不能为空");
            CostBudgetItemVo itemOut = costBudgetItemOuts.stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
            Validate.notNull(itemOut, "调出方费用预算明细项不能为空");
            Validate.isTrue(itemOut.getFinalBalance().compareTo(operateAmount) >= 0, "调出方可用余额不足，请检查");
            //构造调出明细
            CostBudgetItemDto itemOutDto = new CostBudgetItemDto();
            itemOutDto.setOperateType(CostBudgetOperateType.TRANSFER_OUT.getCode());
            itemOutDto.setBalance(itemOut.getFinalBalance());
            itemOutDto.setCostBudgetCode(costBudgetCodeOut);
            itemOutDto.setFinalBalance(itemOut.getFinalBalance().subtract(operateAmount));
            itemOutDto.setOperateAmount(operateAmount);
            itemOutDto.setTenantCode(out.getTenantCode());
            itemOutDto.setItemRemark(operateRemark);
            itemOutDto.setBusinessCode(costBudgetCodeIn);
            itemOutDto.setSource(CostBudgetItemSourceType.YEAR_COST_BUDGET.getDescr());
            costBudgetItemService.create(Lists.newArrayList(itemOutDto));

            //2.=====
            List<CostBudgetItemVo> costBudgetItemIns = costBudgetItemService.findByCostBudgetCode(costBudgetCodeIn);
            Validate.notEmpty(costBudgetItemIns, "调入方费用预算明细项不能为空");
            CostBudgetItemVo itemIn = costBudgetItemIns.stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
            Validate.notNull(itemIn, "调入方费用预算明细项不能为空");
            //构造调入明细
            CostBudgetItemDto itemInDto = new CostBudgetItemDto();
            itemInDto.setOperateType(CostBudgetOperateType.TRANSFER_IN.getCode());
            itemInDto.setBalance(itemIn.getFinalBalance());
            itemInDto.setCostBudgetCode(costBudgetCodeIn);
            itemInDto.setFinalBalance(itemIn.getFinalBalance().add(operateAmount));
            itemInDto.setOperateAmount(operateAmount);
            itemInDto.setTenantCode(in.getTenantCode());
            itemInDto.setBusinessCode(costBudgetCodeOut);
            itemInDto.setSource(CostBudgetItemSourceType.YEAR_COST_BUDGET.getDescr());
            costBudgetItemService.create(Lists.newArrayList(itemInDto));
        } finally {
            if (hasLock) {
                redisMutexService.unlock(BUDGET_GLOBAL_REDIS_LOCK_KEY);
            }
        }
    }


    @Override
    @Transactional
    public void change(String costBudgetCode, BigDecimal operateAmount, String operateRemark, String operateType) {
        Validate.notBlank(costBudgetCode, "费用预算编码不能为空");
        Validate.notBlank(operateType, "操作类型不能为空");
        Validate.notNull(operateAmount, "操作金额不能为空");
        Validate.isTrue(operateAmount.compareTo(BigDecimal.ZERO) > 0, "操作金额必须大于0");
        CostBudgetOperateType operateTypeEnum = CostBudgetOperateType.findByCode(operateType);
        Validate.notNull(operateTypeEnum, "未知的操作类型【%s】，请检查", operateType);
        switch (operateTypeEnum) {
            case APPEND:
                break;
            case REDUCE:
                break;
            default:
                throw new IllegalArgumentException("不支持的操作类型【" + operateType + "】，请检查");
        }
        //需要进行redis锁操作
        boolean hasLock = false;
        try {
            hasLock = redisMutexService.tryLock(BUDGET_GLOBAL_REDIS_LOCK_KEY, TimeUnit.SECONDS, MAX_TIMES);
            Validate.isTrue(hasLock, "系统网络繁忙，请稍后重试");
            CostBudgetVo change = this.findByCode(costBudgetCode);
            Validate.notNull(change, "根据提供的费用预算编码【%s】，未能获取到相应信息", costBudgetCode);
            Validate.isTrue(StringUtils.equals(change.getDelFlag(), DelFlagStatusEnum.NORMAL.getCode()), "不能对【已删除】费用预算进行操作");
            List<CostBudgetItemVo> costBudgetItems = costBudgetItemService.findByCostBudgetCode(costBudgetCode);
            Validate.notEmpty(costBudgetItems, "费用预算明细项不能为空");
            CostBudgetItemVo item = costBudgetItems.stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
            Validate.notNull(item, "费用预算明细项不能为空");
            if (operateTypeEnum == CostBudgetOperateType.REDUCE) {
                Validate.isTrue(item.getFinalBalance().compareTo(operateAmount) >= 0, "可用余额不足，请检查");
            }
            //构造明细
            CostBudgetItemDto itemDto = new CostBudgetItemDto();
            itemDto.setOperateType(operateType);
            itemDto.setBalance(item.getFinalBalance());
            itemDto.setCostBudgetCode(costBudgetCode);
            if (operateTypeEnum == CostBudgetOperateType.REDUCE) {
                itemDto.setFinalBalance(item.getFinalBalance().subtract(operateAmount));
            } else {
                itemDto.setFinalBalance(item.getFinalBalance().add(operateAmount));
            }
            itemDto.setOperateAmount(operateAmount);
            itemDto.setTenantCode(item.getTenantCode());
            itemDto.setItemRemark(operateRemark);
            itemDto.setBusinessCode(costBudgetCode);
            itemDto.setSource(CostBudgetItemSourceType.YEAR_COST_BUDGET.getDescr());
            costBudgetItemService.create(Lists.newArrayList(itemDto));
        } finally {
            if (hasLock) {
                redisMutexService.unlock(BUDGET_GLOBAL_REDIS_LOCK_KEY);
            }
        }
    }

    @Override
    @Transactional
    public void wipe(String costBudgetCodeOut, List<CostBudgetDto> costBudgetIns) {
        Validate.notBlank(costBudgetCodeOut, "划出方费用预算编码不能为空");
        Validate.notEmpty(costBudgetIns, "划入方费用预算信息集不能为空");
        //需要进行redis锁操作
        boolean hasLock = false;
        try {
            hasLock = redisMutexService.tryLock(BUDGET_GLOBAL_REDIS_LOCK_KEY, TimeUnit.SECONDS, MAX_TIMES);
            Validate.isTrue(hasLock, "系统网络繁忙，请稍后重试");
            CostBudgetVo costBudgetVo = this.findByCode(costBudgetCodeOut);
            Validate.notNull(costBudgetVo, "根据提供的费用预算编码【%s】，未能获取到相应信息", costBudgetCodeOut);
            Validate.isTrue(StringUtils.equals(costBudgetVo.getDelFlag(), DelFlagStatusEnum.NORMAL.getCode()), "不能对【已删除】费用预算进行操作");
            this.validateTargetWipeIn(costBudgetIns);

            List<CostBudgetItemVo> itemVos = costBudgetItemService.findByCostBudgetCode(costBudgetCodeOut);
            Validate.notEmpty(itemVos, "划出方费用预算明细项不能为空");
            CostBudgetItemVo itemOut = itemVos.stream().max(Comparator.comparing(CostBudgetItemVo::getSortIndex)).orElse(null);
            Validate.notNull(itemOut, "划出方费用预算明细项不能为空");
            BigDecimal balance = itemOut.getFinalBalance();
            BigDecimal totalOperateAmount = BigDecimal.ZERO;
            for (CostBudgetDto dto : costBudgetIns) {
                dto.setInitialAmount(dto.getOperateAmount());
                //累计总操作金额
                totalOperateAmount = totalOperateAmount.add(dto.getInitialAmount());
                this.createValidate(dto);
                dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
                dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                CostBudget entity = nebulaToolkitService.copyObjectByWhiteList(dto, CostBudget.class, HashSet.class, ArrayList.class);
                entity.setTenantCode(TenantUtils.getTenantCode());
                costBudgetRepository.save(entity);
                //构造费用预算期初明细
                CostBudgetItemDto itemDto = new CostBudgetItemDto();
                itemDto.setOperateType(CostBudgetOperateType.WIPE_IN.getCode());
                itemDto.setBalance(BigDecimal.ZERO);
                itemDto.setCostBudgetCode(dto.getCode());
                itemDto.setFinalBalance(dto.getInitialAmount());
                itemDto.setOperateAmount(dto.getInitialAmount());
                itemDto.setTenantCode(dto.getTenantCode());
                itemDto.setItemRemark(dto.getOperateRemark());
                itemDto.setBusinessCode(costBudgetCodeOut);
                itemDto.setSource(CostBudgetItemSourceType.YEAR_COST_BUDGET.getDescr());
                costBudgetItemService.create(Lists.newArrayList(itemDto));

                //构建划出方费用明细
                CostBudgetItemDto itemOutDto = new CostBudgetItemDto();
                itemOutDto.setOperateType(CostBudgetOperateType.WIPE_OUT.getCode());
                itemOutDto.setBalance(balance);
                itemOutDto.setCostBudgetCode(costBudgetCodeOut);
                Validate.isTrue(balance.compareTo(dto.getInitialAmount()) >= 0, "划出方可用余额不足，请检查");
                itemOutDto.setFinalBalance(balance.subtract(dto.getInitialAmount()));
                itemOutDto.setOperateAmount(dto.getInitialAmount());
                itemOutDto.setTenantCode(costBudgetVo.getTenantCode());
                itemOutDto.setBusinessCode(itemDto.getCostBudgetCode());
                itemOutDto.setSource(CostBudgetItemSourceType.YEAR_COST_BUDGET.getDescr());
                costBudgetItemService.create(Lists.newArrayList(itemOutDto));
                //覆盖计算余额
                balance = balance.subtract(dto.getInitialAmount());
            }
        } finally {
            if (hasLock) {
                redisMutexService.unlock(BUDGET_GLOBAL_REDIS_LOCK_KEY);
            }
        }
    }


    @Override
    @Transactional
    public void occupy(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source) {
        Validate.notBlank(businessCode, "业务编号不能为空");
        Validate.notBlank(businessItemCode, "业务明细编号不能为空");
        Validate.notBlank(costBudgetCode, "费用预算编码不能为空");
        Validate.notNull(operateAmount, "使用金额不能为空");
        Validate.isTrue(operateAmount.compareTo(BigDecimal.ZERO) > 0, "使用金额必须大于0");
        CostBudgetVo costBudgetVo = this.findByCode(costBudgetCode);
        Validate.notNull(costBudgetVo, "根据指定的费用预算编码【%s】，未能获取到相应信息", costBudgetCode);
        BudgetControlTypeStrategy controlTypeStrategy = budgetSubjectsVoService.findBudgetControlTypeStrategyByCode(costBudgetVo.getBudgetSubjectCode());
        Validate.notNull(controlTypeStrategy, "未能获取到科目预算【%s】控制类型策略，请检查", costBudgetVo.getBudgetSubjectCode());
        controlTypeStrategy.forward(businessCode, businessItemCode, costBudgetCode, operateAmount, itemRemark, source);
    }

    @Override
    @Transactional
    public void back(String businessCode, String businessItemCode, String costBudgetCode, BigDecimal operateAmount, String itemRemark, String source) {
        Validate.notBlank(businessCode, "业务编号不能为空");
        Validate.notBlank(businessItemCode, "业务明细编号不能为空");
        Validate.notBlank(costBudgetCode, "费用预算编码不能为空");
        Validate.notNull(operateAmount, "操作金额不能为空");
        Validate.isTrue(operateAmount.compareTo(BigDecimal.ZERO) > 0, "操作金额必须大于0");
        CostBudgetVo costBudgetVo = this.findByCode(costBudgetCode);
        Validate.notNull(costBudgetVo, "根据指定的费用预算编码【%s】，未能获取到相应信息", costBudgetCode);
        BudgetControlTypeStrategy controlTypeStrategy = budgetSubjectsVoService.findBudgetControlTypeStrategyByCode(costBudgetVo.getBudgetSubjectCode());
        Validate.notNull(controlTypeStrategy, "未能获取到科目预算【%s】控制类型策略，请检查", costBudgetVo.getBudgetSubjectCode());
        controlTypeStrategy.reverse(businessCode, businessItemCode, costBudgetCode, operateAmount, itemRemark, source);
    }

    private void validateTargetWipeIn(List<CostBudgetDto> costBudgetIns) {
        long size = costBudgetIns.stream().map(e -> StringUtils.joinWith(":",
                StringUtils.isNotBlank(e.getOrgCode()) ? e.getOrgCode() : StringUtils.EMPTY,
                StringUtils.isNotBlank(e.getCustomerCode()) ? e.getCustomerCode() : StringUtils.EMPTY,
                StringUtils.isNotBlank(e.getTerminalCode()) ? e.getTerminalCode() : StringUtils.EMPTY,
                StringUtils.isNotBlank(e.getChannelCode()) ? e.getChannelCode() : StringUtils.EMPTY)).distinct().count();
        Validate.isTrue(size == costBudgetIns.size(), "划拨对象重复，请重新选择");
    }

    @Override
    public Set<CostBudgetRelationVo> findRelationByCodes(Set<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptySet();
        }
        List<CostBudget> costBudgets = this.costBudgetRepository.findByCodesAndTenantCode(codes, TenantUtils.getTenantCode());
        Set<CostBudgetRelationVo> costBudgetRelationVos = Sets.newHashSet();
        for (CostBudget costBudget : costBudgets) {
            CostTypeCategoryDto costTypeCategoryDto = new CostTypeCategoryDto();
            costTypeCategoryDto.setSelect(true);
            costTypeCategoryDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            costTypeCategoryDto.setBudgetSubjectsName(costBudget.getBudgetSubjectName());
            Page<CostTypeCategoryVo> costTypeCategoryVoPage = this.costTypeCategoryVoService.findByConditions(PageRequest.of(0, Integer.MAX_VALUE), costTypeCategoryDto);
            if (costTypeCategoryVoPage.getSize() <= 0) {
                continue;
            }
            for (CostTypeCategoryVo costTypeCategoryVo : costTypeCategoryVoPage.getRecords()) {
                CostBudgetRelationVo costBudgetRelationVo = new CostBudgetRelationVo();
                costBudgetRelationVo.setCostBudgetCode(costBudget.getCode());
                costBudgetRelationVo.setBudgetSubjectsCode(costBudget.getBudgetSubjectCode());
                costBudgetRelationVo.setBudgetSubjectsName(costBudget.getBudgetSubjectName());
                costBudgetRelationVo.setCategoryCode(costTypeCategoryVo.getCategoryCode());
                costBudgetRelationVo.setCategoryName(costTypeCategoryVo.getCategoryName());
                costBudgetRelationVo.setUniqueKey(StringUtils.joinWith(":", costBudgetRelationVo.getCostBudgetCode(), costBudgetRelationVo.getCategoryCode(), costBudgetRelationVo.getBudgetSubjectsCode()));
                costBudgetRelationVos.add(costBudgetRelationVo);
            }
        }
        return costBudgetRelationVos;
    }

    /**
     * 根据费用预算编号确认
     *
     * @param codeList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirm(List<String> codeList) {
        List<CostBudget> costBudgets = costBudgetRepository.findByCodesAndTenantCode(new HashSet<>(codeList), TenantUtils.getTenantCode());
        Validate.notEmpty(costBudgets, "未找到对应的预算");

        boolean lock = costBudgetLockVoService.lock(codeList, TimeUnit.SECONDS, 30);
        Validate.isTrue(lock, "操作预算失败，获取操作锁失败！");
        try {
            costBudgets.forEach(e -> e.setConfirmStatus(ConfirmStatusEnum.CONFIRMED.getCode()));
            costBudgetRepository.saveOrUpdateBatch(costBudgets);
        } finally {
            costBudgetLockVoService.unLock(codeList);
        }
    }

    /**
     * 批量保存
     *
     * @param importList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createBatch(List<CostBudgetImportsVo> importList) {
        Collection<CostBudget> costBudgets = nebulaToolkitService.copyCollectionByWhiteList(importList, CostBudgetImportsVo.class, CostBudget.class, HashSet.class, ArrayList.class);
        List<String> codeList = this.generateCodeService.generateCodeYearMonth(COSTBUDGET_RULE_CODE, costBudgets.size());

        List<CostBudgetItemDto> itemDtoList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(0);
        costBudgets.forEach(costBudget -> {
            costBudget.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            costBudget.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            costBudget.setTenantCode(TenantUtils.getTenantCode());
            costBudget.setCode(codeList.get(index.get()));
            costBudget.refreshBalanceAmount();
            // 根据部门编码做权限将部门赋值给权限字段
            costBudget.setOrgCode(costBudget.getDepartmentOneCode());
            index.getAndAdd(1);

            CostBudgetItemDto itemDto = new CostBudgetItemDto();
            buildCostBudgetItemDto(itemDto, costBudget);
            itemDtoList.add(itemDto);
        });
        costBudgetRepository.saveOrUpdateBatch(costBudgets);
        costBudgetItemService.create(itemDtoList);
    }

    /**
     * 按条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<CostBudgetVo> findBudgetCodeByDto(CostBudgetDto dto) {
        List<CostBudgetVo> list = costBudgetRepository.findBudgetCodeByDto(dto);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        CostBudgetBuildUtil.buildCostBudget(list);
        return list;
    }

    /**
     * 预算管控查询预算
     *
     * @param dto
     * @return
     */
    @Override
    public List<CostBudgetVo> findByDto(CostBudgetDto dto) {
        List<CostBudgetVo> list = costBudgetRepository.findByDto(dto);
        CostBudgetBuildUtil.buildCostBudget(list);
        return list;
    }


    /**
     * 通过客户编码+成本中心+年月查询费用预算
     *
     * @param customerCodes
     * @param costCenterCodes
     * @param years
     * @return
     */
    @Override
    public List<CostBudgetVo> findListByCustomerCodesAndCostCenterCodesAndYears(List<String> customerCodes, List<String> costCenterCodes, List<String> deptCodes, String years) {
        List<CostBudget> dataList = costBudgetRepository.findListByCustomerCodesAndCostCenterCodesAndYears(customerCodes, costCenterCodes, deptCodes, years);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        List<CostBudgetVo> list = (List<CostBudgetVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostBudget.class, CostBudgetVo.class, HashSet.class, ArrayList.class);
        CostBudgetBuildUtil.buildCostBudget(list);
        return list;

    }


    public List<CostBudgetVo> findListByOrgCodesAndYears(List<String> orgCodes, String years) {
        List<CostBudget> dataList = costBudgetRepository.findListByOrgCodesAndYears(orgCodes, years);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        List<CostBudgetVo> list = (List<CostBudgetVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostBudget.class, CostBudgetVo.class, HashSet.class, ArrayList.class);
        CostBudgetBuildUtil.buildCostBudget(list);
        return list;
    }

    @Override
    public List<CostBudgetVo> findListByOrgCodeAndItemCodesAndYears(List<String> orgCodes, List<String> itemCodes, String years) {
        List<CostBudget> dataList = costBudgetRepository.findListByOrgCodeAndItemCodesAndYears(orgCodes, itemCodes, years);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        List<CostBudgetVo> list = (List<CostBudgetVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostBudget.class, CostBudgetVo.class, HashSet.class, ArrayList.class);
        CostBudgetBuildUtil.buildCostBudget(list);
        return list;
    }


    @Override
    public List<CostBudgetVo> findListByOrgCodeAndCategoryCodesAndYears(List<String> orgCodes, List<String> categoryCodes, String years) {
        List<CostBudgetVo> dataList = costBudgetRepository.findListByOrgCodeAndCategoryCodesAndYears(orgCodes, categoryCodes, years);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        CostBudgetBuildUtil.buildCostBudget(dataList);
        return dataList;
    }


    /**
     * 通过条件查询年度预算编码
     *
     * @param dto
     * @return
     */
    @Override
    public String findCodeByConditions(CostBudgetDto dto) {
        return costBudgetRepository.findCodeByConditions(dto);
    }


    @Resource
    private OrgVoService orgVoService;

    @Override
    public Map<String, BigDecimal> findListByOrgCodesAndYearsList(List<String> orgCodes, List<String> yearsList) {
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCodes(orgCodes);
        if (CollectionUtils.isEmpty(orgVoList)) {
            return Maps.newHashMap();
        }
        orgCodes = orgVoList.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
        List<List<String>> partitionList = Lists.partition(orgCodes, 800);
        List<CostBudget> dataList = Lists.newArrayList();
        for (List<String> list : partitionList) {
            List<CostBudget> costBudgetList = costBudgetRepository.findListByOrgCodeAndYears(list, yearsList);
            if (!CollectionUtils.isEmpty(costBudgetList)) {
                dataList.addAll(costBudgetList);
            }
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return Maps.newHashMap();
        }
        return dataList.stream().collect(Collectors.groupingBy(x -> x.getYearMonthLy(),
                Collectors.mapping(CostBudget::getMonthBalanceAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    @Override
    public Map<String, BigDecimal> findListByOrgCodesAndYearsListNoTax(List<String> orgCodes, List<String> yearsList) {
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCodes(orgCodes);
        if (CollectionUtils.isEmpty(orgVoList)) {
            return Maps.newHashMap();
        }
        orgCodes = orgVoList.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
        List<List<String>> partitionList = Lists.partition(orgCodes, 800);
        List<CostBudget> dataList = Lists.newArrayList();
        for (List<String> list : partitionList) {
            List<CostBudget> costBudgetList = costBudgetRepository.findListByOrgCodeAndYears(list, yearsList);
            if (!CollectionUtils.isEmpty(costBudgetList)) {
                dataList.addAll(costBudgetList);
            }
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return Maps.newHashMap();
        }
        List<CostBudgetVo> costBudgetVos = dataList.stream()
                .map(c -> BeanUtil.copyProperties(c, CostBudgetVo.class))
                .collect(Collectors.toList());

        List<String> subjectCodes = costBudgetVos.stream().map(CostBudgetVo::getBudgetSubjectCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, BigDecimal> taxRateMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(subjectCodes)) {
            // 去除权限校验
            MarsAuthorityContext marsAuthorityContext = MarsAuthorityContextHolder.getContext();
            marsAuthorityContext.setListCode(null);
            List<BudgetSubjectsVo> subjectVos = budgetSubjectsVoService.findByCodeList(subjectCodes);
            taxRateMap = subjectVos.stream().filter(e -> Objects.nonNull(e.getTaxRate()))
                    .collect(Collectors.toMap(BudgetSubjectsVo::getBudgetSubjectsCode, BudgetSubjectsVo::getTaxRate));
        }

        for (CostBudgetVo record : costBudgetVos) {
            record.setNoTaxAdjustBalanceAmount(record.getAdjustBalanceAmount());
            if (taxRateMap.containsKey(record.getBudgetSubjectCode())) {
                record.setTaxRate(taxRateMap.get(record.getBudgetSubjectCode()));
                record.setTaxRateStr(null == record.getTaxRate()? "" : record.getTaxRate().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
                record.setNoTaxAdjustBalanceAmount(record.getAdjustBalanceAmount().divide(new BigDecimal(1).add(null == record.getTaxRate()? new BigDecimal(0) : record.getTaxRate()), 2,BigDecimal.ROUND_HALF_UP));
            }
        }

        return costBudgetVos.stream().collect(Collectors.groupingBy(CostBudgetVo::getYearMonthLy,
                Collectors.mapping(CostBudgetVo::getNoTaxAdjustBalanceAmount,
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    @Override
    public List<CostBudgetVo> findListByOrgCodesAndCustomerCodeAndBudgetSubjectCodesAndYears(List<String> orgCodes, String customerCode, List<String> budgetSubjectCodes, String years) {
        List<CostBudget> costBudgets = costBudgetRepository.findListByOrgCodesAndCustomerCodeAndBudgetSubjectCodesAndYears(orgCodes, customerCode, budgetSubjectCodes, years);
        if (CollectionUtils.isEmpty(costBudgets)) {
            return Lists.newArrayList();
        }
        return (List<CostBudgetVo>) nebulaToolkitService.copyCollectionByWhiteList(costBudgets,CostBudget.class,CostBudgetVo.class, HashSet.class,ArrayList.class);
    }

    /**
     * 构造费用预算期初明细
     *
     * @param itemDto
     */
    private void buildCostBudgetItemDto(CostBudgetItemDto itemDto, CostBudget costBudget) {
        itemDto.setOperateType(CostBudgetOperateType.INITIALIZATION.getCode());
        itemDto.setBalance(BigDecimal.ZERO);
        itemDto.setCostBudgetCode(costBudget.getCode());
        itemDto.setInitialAmount(costBudget.getInitialAmount());
        itemDto.setFinalBalance(costBudget.getInitialAmount());
        itemDto.setOperateAmount(costBudget.getInitialAmount());
        itemDto.setTenantCode(costBudget.getTenantCode());
        itemDto.setBusinessCode(costBudget.getCode());
        itemDto.setSource(CostBudgetItemSourceType.YEAR_COST_BUDGET.getDescr());
        itemDto.setCustomerCode(costBudget.getCustomerCode());
        itemDto.setCustomerName(costBudget.getCustomerName());
        itemDto.setBudgetSubjectCode(costBudget.getBudgetSubjectCode());
        itemDto.setBudgetSubjectName(costBudget.getBudgetSubjectName());
        itemDto.setDepartmentCode(costBudget.getDepartmentOneCode());
        itemDto.setDepartmentName(costBudget.getDepartmentOneName());
    }
}