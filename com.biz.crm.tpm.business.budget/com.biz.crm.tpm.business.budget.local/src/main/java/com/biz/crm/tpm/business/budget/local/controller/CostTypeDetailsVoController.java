package com.biz.crm.tpm.business.budget.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailsDto;
import com.biz.crm.tpm.business.budget.sdk.service.CostTypeDetailVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.payby.PayByStrategy;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * TPM-活动明细;(tpm_cost_type_details)Vo控制层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@Api(tags = "TPM-活动明细Vo功能接口")
@RestController
@RequestMapping("/v1/budget/costTypeDetails")
@Slf4j
public class CostTypeDetailsVoController {
  /**
   * 服务对象
   */
  @Autowired
  private CostTypeDetailVoService costTypeDetailVoService;

  /**
   * 由于创建或修改导致的重复提交的问题。在创建和修改前，需要使用该方法获得预授权</br></br>
   * 预授权成功后，才能通过预授权信息进行添加，
   *
   * @return
   */
  @ApiOperation(value = "由于创建或修改导致的重复提交的问题。在创建和修改前，需要使用该方法获得预授权</br>")
  @PostMapping(value = "/preSave")
  public Result<?> preSave() {
    try {
      Object prefix = this.costTypeDetailVoService.preSave();
      return Result.ok(prefix);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 分页查询所有数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<CostTypeDetailVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(2000) Pageable pageable,
                                                         @ApiParam(name = "costTypeDetails", value = "核销采集信息") CostTypeDetailsDto dto) {
    try {
      Page<CostTypeDetailVo> page = this.costTypeDetailVoService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询单条数据")
  @GetMapping("{id}")
  public Result<CostTypeDetailVo> findById(@PathVariable @ApiParam(name = "id", value = "主键id", required = true) String id) {
    try {
      CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findById(id);
      return Result.ok(costTypeDetailVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过活动大类编号查询活动明细数据
   *
   * @param categoryCode 活动大类编号
   * @return 数据集合
   */
  @ApiOperation(value = "通过活动大类编号查询活动明细数据")
  @GetMapping("findByCategoryCode")
  public Result<Set<CostTypeDetailVo>> findByCategoryCode(@ApiParam(name = "categoryCode", value = "活动大类编号", required = true) String categoryCode) {
    try {
      Set<CostTypeDetailVo> costTypeDetailVos = this.costTypeDetailVoService.findByCategoryCode(categoryCode);
      return Result.ok(costTypeDetailVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编号查询单条数据
   *
   * @param code 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过编号查询单条数据")
  @GetMapping("findByCode")
  public Result<CostTypeDetailVo> findByCode(@ApiParam(name = "code", value = "编号", required = true) String code) {
    try {
      CostTypeDetailVo costTypeDetailVo = this.costTypeDetailVoService.findByCode(code);
      return Result.ok(costTypeDetailVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过多个编号查询多条数据
   *
   * @param codes 多个编号
   * @return 多条数据
   */
  @ApiOperation(value = "通过多个编号查询多条数据")
  @GetMapping("findByCodes")
  public Result<List<CostTypeDetailVo>> findByCodes(@ApiParam(name = "codes", value = "编号", required = true) @RequestParam("codes") List<String> codes) {
    try {
      List<CostTypeDetailVo> costTypeDetailVos = this.costTypeDetailVoService.findByCodes(codes);
      return Result.ok(costTypeDetailVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param costTypeDetailVo 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<CostTypeDetailVo> create(@ApiParam(name = "costTypeDetailsVo", value = "TPM-活动明细") @RequestBody CostTypeDetailVo costTypeDetailVo) {
    try {
      CostTypeDetailVo result = this.costTypeDetailVoService.create(costTypeDetailVo);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   *
   * @param costTypeDetailVo 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<CostTypeDetailVo> update(@ApiParam(name = "costTypeDetailsVo", value = "TPM-活动明细") @RequestBody CostTypeDetailVo costTypeDetailVo) {
    try {
      CostTypeDetailVo result = this.costTypeDetailVoService.update(costTypeDetailVo);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  //@DeleteMapping
  //@ApiOperation(value = "删除数据")
  //public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
  //  try {
  //    this.costTypeDetailVoService.delete(idList);
  //    return Result.ok();
  //  } catch (Exception e) {
  //    log.error(e.getMessage(), e);
  //    return Result.error(e.getMessage());
  //  }
  //}

  /**
   * 通过状态编号查询单条数据
   *
   * @param enableStatus 编号
   * @return 单条数据
   */
  @ApiOperation(value = "通过状态编号查询单条数据")
  @GetMapping("findByEnableStatus")
  public Result<List<CostTypeDetailVo>> findByEnableStatus(@PathVariable @ApiParam(name = "enableStatus", value = "状态编号") String enableStatus) {
    try {
      List<CostTypeDetailVo> costTypeDetailss = this.costTypeDetailVoService.findByEnableStatus(enableStatus);
      return Result.ok(costTypeDetailss);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量根据id启用
   */
  @ApiOperation(value = "批量根据id启用")
  @PatchMapping(value = "enable")
  public Result<?> enable(@RequestBody List<String> ids) {
    try {
      this.costTypeDetailVoService.enable(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 批量根据id禁用
   */
  @ApiOperation(value = "批量根据id禁用")
  @PatchMapping(value = "disable")
  public Result<?> disable(@RequestBody List<String> ids) {
    try {
      this.costTypeDetailVoService.disable(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 查询支付方式信息
   */
  @ApiOperation(value = "查询支付方式信息")
  @GetMapping(value = "findAllPayBy")
  public Result<?> findAllPayBy() {
    try {
      List<PayByStrategy> costTypePayByStrategies = this.costTypeDetailVoService.findAllPayBy();
      return Result.ok(costTypePayByStrategies);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}