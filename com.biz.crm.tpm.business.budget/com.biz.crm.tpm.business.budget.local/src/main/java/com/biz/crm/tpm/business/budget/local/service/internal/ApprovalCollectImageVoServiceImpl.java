package com.biz.crm.tpm.business.budget.local.service.internal;

import com.biz.crm.tpm.business.budget.local.entity.ApprovalCollectImage;
import com.biz.crm.tpm.business.budget.local.repository.ApprovalCollectImageRepository;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectDto;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectImageDto;
import com.biz.crm.tpm.business.budget.sdk.service.ApprovalCollectImageVoService;
import com.biz.crm.tpm.business.budget.sdk.service.ApprovalCollectVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectImageVo;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class ApprovalCollectImageVoServiceImpl implements ApprovalCollectImageVoService {

  @Autowired
  private ApprovalCollectVoService approvalCollectVoService;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private ApprovalCollectImageRepository approvalCollectImageRepository;

  @Override
  @Transactional
  public List<ApprovalCollectImageVo> create(ApprovalCollectDto approvalCollect, List<ApprovalCollectImageDto> images) {
    Validate.notNull(approvalCollect,"核销采集信息不能为空");
    Validate.notBlank(approvalCollect.getCode(),"核销采集编码不能为空");
    ApprovalCollectVo approvalCollectVo = approvalCollectVoService.findByCode(approvalCollect.getCode());
    Validate.notNull(approvalCollectVo,"根据指定的核销编码【%s】，未能获取到相应信息",approvalCollect.getCode());
    Validate.notEmpty(images,"核销采集图片信息不能为空");
    for(ApprovalCollectImageDto image : images){
      Validate.isTrue(StringUtils.isBlank(image.getId()),"id主键不能有值");
      image.setId(null);
      Validate.notBlank(image.getFileCode(),"文件唯一识别号不能为空");
      Validate.notBlank(image.getApprovalCollectCode(),"核销采集编码不能为空");
      /*
      上一任开发将主表的保存校验中采集编码转成了大写，故在采集图片的校验之前，将图片的采集编码也进行大写转成
      主表位置：com.biz.crm.tpm.business.budget.local.service.internal.ApprovalCollectServiceImpl.createValidate
       */
      image.setApprovalCollectCode(StringUtils.upperCase(image.getApprovalCollectCode()));
      Validate.isTrue(StringUtils.equals(image.getApprovalCollectCode(),approvalCollectVo.getCode()),"传入的核销采集编码与核销采集图片关联的信息不一致，请检查");
      image.setApprovalCollectCode(approvalCollect.getCode());
      image.setCreateAccount(approvalCollectVo.getCreateAccount());
      image.setCreateName(approvalCollectVo.getCreateName());
      image.setCreateTime(approvalCollectVo.getCreateTime());
      image.setTenantCode(approvalCollectVo.getTenantCode());
    }

    Collection<ApprovalCollectImage> imageEntities = nebulaToolkitService.copyCollectionByWhiteList(images,ApprovalCollectImageDto.class,ApprovalCollectImage.class, HashSet.class, ArrayList.class);
    imageEntities.forEach(image->image.setTenantCode(TenantUtils.getTenantCode()));
    approvalCollectImageRepository.saveBatch(imageEntities);
    Collection<ApprovalCollectImageVo> result = nebulaToolkitService.copyCollectionByWhiteList(imageEntities,ApprovalCollectImage.class,ApprovalCollectImageVo.class, HashSet.class, ArrayList.class);
    return Lists.newArrayList(result);
  }


  @Override
  public List<ApprovalCollectImageVo> findByApprovalCollectCode(String code) {
    if(StringUtils.isBlank(code)){
      return Lists.newArrayList();
    }
    List<ApprovalCollectImage> images = approvalCollectImageRepository.findByApprovalCollectCode(code);
    if(CollectionUtils.isEmpty(images)){
      return Lists.newArrayList();
    }
    Collection<ApprovalCollectImageVo> collection = nebulaToolkitService.copyCollectionByWhiteList(images,ApprovalCollectImage.class,ApprovalCollectImageVo.class,HashSet.class,ArrayList.class);
    return Lists.newArrayList(collection);
  }

  @Override
  @Transactional
  public void deleteByIds(Set<String> ids) {
    Validate.notEmpty(ids,"主键id不能为空");
    approvalCollectImageRepository.removeByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
  }

  @Override
  public List<ApprovalCollectImageVo> findByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)){
      return new ArrayList<>(0);
    }
    List<ApprovalCollectImage> images = this.approvalCollectImageRepository.findByIds(ids);
    if(CollectionUtils.isEmpty(images)){
      return Lists.newArrayList();
    }
    Collection<ApprovalCollectImageVo> collection = nebulaToolkitService.copyCollectionByWhiteList(images,ApprovalCollectImage.class,ApprovalCollectImageVo.class,HashSet.class,ArrayList.class);
    return Lists.newArrayList(collection);
  }
}
