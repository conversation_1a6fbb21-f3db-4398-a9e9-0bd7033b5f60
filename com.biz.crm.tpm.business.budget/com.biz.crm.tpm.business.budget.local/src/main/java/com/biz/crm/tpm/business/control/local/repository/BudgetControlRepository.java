package com.biz.crm.tpm.business.control.local.repository;


import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.control.local.entity.BudgetControl;
import com.biz.crm.tpm.business.control.local.mapper.BudgetControlMapper;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;


/**
 * 预算管控(BudgetControl)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-22 16:19:50
 */
@Component
public class BudgetControlRepository extends ServiceImpl<BudgetControlMapper, BudgetControl> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 根据id集合获取详情集合
     *
     * @param ids ID集合
     * @return List<BudgetControl>
     */
    public List<BudgetControl> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .in(BudgetControl::getId, ids)
                .eq(BudgetControl::getTenantCode, tenantCode)
                .eq(BudgetControl::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据编码查询
     *
     * @param code
     * @return
     */
    public BudgetControl findByCode(String code) {
        String tenantCode = TenantUtils.getTenantCode();
        return this.lambdaQuery()
                .eq(BudgetControl::getControlCode, code)
                .eq(BudgetControl::getTenantCode, tenantCode)
                .eq(BudgetControl::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .one();
    }

    /**
     * 批量根据id禁用
     *
     * @param enable
     * @param ids
     */
    public void updateEnableStatusByIds(EnableStatusEnum enable, List<String> ids) {
        UpdateWrapper<BudgetControl> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("enable_status", enable.getCode());
        updateWrapper.in("id", ids);
        updateWrapper.eq("tenant_code", TenantUtils.getTenantCode());
        this.update(updateWrapper);
    }

    /**
     * 查询生效中的管控配置
     *
     * @param yearMonthLy
     * @return
     */
    public List<BudgetControlVo> findBudgetControlEnable(String yearMonthLy) {
        List<BudgetControl> list = lambdaQuery().le(BudgetControl::getStartYearMonth, yearMonthLy)
                .eq(BudgetControl::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .ge(BudgetControl::getEndYearMonth, yearMonthLy).list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, BudgetControl.class, BudgetControlVo.class, LinkedHashSet.class, ArrayList.class));
    }


    public List<BudgetControlVo> findBudgetControlByCodes(List<String> codes) {
        List<BudgetControl> controls = this.lambdaQuery()
                .in(BudgetControl::getControlCode, codes)
                .list();
        if (CollectionUtils.isEmpty(controls)) {
            return Lists.newArrayList();
        }
        return (List<BudgetControlVo>) nebulaToolkitService.copyCollectionByWhiteList(controls, BudgetControl.class, BudgetControlVo.class,
                HashSet.class, ArrayList.class);
    }

    public List<BudgetControlVo> matchBudgetControl(String years, List<String> orgCodes, String customerCode, Set<String> budgetSubjectCodeSet) {
        return this.baseMapper.matchBudgetControl(years, orgCodes, customerCode, budgetSubjectCodeSet, TenantUtils.getTenantCode());
    }
}

