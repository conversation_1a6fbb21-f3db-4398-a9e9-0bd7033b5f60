package com.biz.crm.tpm.business.adjust.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_budget_adjust")
@Table(name = "tpm_budget_adjust")
@ApiModel(value = "BudgetAdjust", description = "预算调整")
@org.hibernate.annotations.Table(appliesTo = "tpm_budget_adjust", comment = "预算调整")
public class BudgetAdjust extends TenantFlagOpEntity {

  @ApiModelProperty("调整编码")
  @Column(name = "adjust_code", length = 64, columnDefinition = "varchar(64) COMMENT '调整编码'")
  private String adjustCode;

  @ApiModelProperty("调整名称")
  @Column(name = "adjust_name", length = 128, columnDefinition = "varchar(128) COMMENT '调整名称'")
  private String adjustName;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "VARCHAR(32) COMMENT '公司编码'")
  private String companyCode;

  @ApiModelProperty("公司名称")
  @Column(name = "company_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '公司名称'")
  private String companyName;

  @ApiModelProperty("ERP编码")
  @Column(name = "erp_code", length = 64, columnDefinition = "varchar(64) COMMENT 'ERP编码'")
  private String erpCode;

  @ApiModelProperty("操作类型")
  @Column(name = "operate_type", length = 64, columnDefinition = "varchar(64) COMMENT '操作类型'")
  private String operateType;

  @ApiModelProperty("审批状态")
  @Column(name = "status", length = 64, columnDefinition = "varchar(64) COMMENT '审批状态'")
  private String status;

  @ApiModelProperty("审批单号")
  @Column(name = "process_number", columnDefinition = "varchar(64) COMMENT '审批单号'")
  private String processNumber;

  @ApiModelProperty("推送日期")
  @Column(name = "process_date", columnDefinition = "datetime COMMENT '推送日期'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date processDate;

  @ApiModelProperty("OA人员id")
  @Column(name = "oa_id", columnDefinition = "varchar(32) COMMENT 'OA人员id'")
  private String oaId;

  @ApiModelProperty("OA人员账号")
  @Column(name = "oa_user_name", columnDefinition = "varchar(32) COMMENT 'OA人员账号'")
  private String oaUserName;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("组织编码")
  @Column(name = "org_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编码'")
  private String orgCode;

  @ApiModelProperty("组织名称")
  @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
  private String orgName;
}
