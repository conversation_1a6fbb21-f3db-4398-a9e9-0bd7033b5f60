package com.biz.crm.tpm.business.budget.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.budget.sdk.service.PublicShareRatioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/v1/budget/publicShareRatio")
@Slf4j
@Api(tags = "公摊费率")
public class PublicShareRatioController {

    @Autowired(required = false)
    private PublicShareRatioService publicShareRatioService;


    /**
     * 删除数据
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除操作")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "ids", value = "主键集合") @RequestParam("ids") List<String> ids) {
        try {
            this.publicShareRatioService.delete(ids);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
