package com.biz.crm.tpm.business.budget.local.service.internal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.budget.local.entity.CostBudgetAnalysis;
import com.biz.crm.tpm.business.budget.local.repository.CostBudgetAnalysisRepository;
import com.biz.crm.tpm.business.budget.sdk.constant.CostBudgetConstant;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetAnalysisService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetAnalysisImportVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetAnalysisVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service("costBudgetAnalysisService")
public class CostBudgetAnalysisServiceImpl implements CostBudgetAnalysisService {
    
    @Autowired(required = false)
    private CostBudgetAnalysisRepository costBudgetAnalysisRepository;
    @Autowired(required = false)
    private NebulaToolkitService  nebulaToolkitService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    
    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @Override
    public void delete(List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
        List<CostBudgetAnalysisVo> costBudgetVos = this.findByIds(Sets.newHashSet(idList));
        Validate.notEmpty(costBudgetVos, "根据提供的ids主键集合，未能获取到相应信息");
        costBudgetVos.forEach(vo ->
                Validate.isTrue(ConfirmStatusEnum.UNCONFIRMED.getCode().equals(vo.getConfirmStatus()), "【%s】已确认，不能进行删除操作", vo.getAnalysisCode()));
        costBudgetAnalysisRepository.removeByIds(idList);
    }

    /**
     * 批量保存
     *
     * @param importVoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(List<CostBudgetAnalysisImportVo> importVoList) {
        Collection<CostBudgetAnalysis> entities = nebulaToolkitService.copyCollectionByWhiteList(importVoList, CostBudgetAnalysisImportVo.class, CostBudgetAnalysis.class, LinkedHashSet.class, ArrayList.class);

        List<String> codeList = this.generateCodeService.generateCodeYearMonth(CostBudgetConstant.ANALYSIS_RULE_CODE, entities.size());
        Validate.notEmpty(codeList, "添加信息时，生成预算编码失败！");
        AtomicInteger index = new AtomicInteger(0);
        entities.forEach(costBudget -> {
            costBudget.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            costBudget.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            costBudget.setTenantCode(TenantUtils.getTenantCode());
            costBudget.setAnalysisCode(codeList.get(index.get()));
            // 根据部门编码做权限将部门赋值给权限字段
            costBudget.setOrgCode(costBudget.getDepartmentOneCode());
            index.getAndAdd(1);
        });
        costBudgetAnalysisRepository.saveBatch(entities);
    }

    public List<CostBudgetAnalysisVo> findByIds(Set<String> ids) {
        List<CostBudgetAnalysis> costBudgets = costBudgetAnalysisRepository.listByIds(ids);
        if (CollectionUtils.isEmpty(costBudgets)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(costBudgets, CostBudgetAnalysis.class, CostBudgetAnalysisVo.class, HashSet.class, ArrayList.class));
    }
}
