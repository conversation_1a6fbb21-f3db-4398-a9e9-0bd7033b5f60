package com.biz.crm.tpm.business.adjust.local.service.internal;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgOaOrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.tpm.business.adjust.local.entity.BudgetAdjust;
import com.biz.crm.tpm.business.adjust.local.repository.BudgetAdjustRepository;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustEventDto;
import com.biz.crm.tpm.business.adjust.sdk.enums.BudgetAdjustDetailFieldsEnum;
import com.biz.crm.tpm.business.adjust.sdk.enums.BudgetAdjustEnum;
import com.biz.crm.tpm.business.adjust.sdk.enums.BudgetAdjustFieldsEnum;
import com.biz.crm.tpm.business.adjust.sdk.event.BudgetAdjustEventListener;
import com.biz.crm.tpm.business.adjust.sdk.service.BudgetAdjustOaService;
import com.biz.crm.tpm.business.adjust.sdk.service.BudgetAdjustService;
import com.biz.crm.tpm.business.adjust.sdk.service.PushOaService;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustDetailVo;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustVo;
import com.biz.crm.workflow.sdk.constant.enums.TpmOaPageEnum;
import com.biz.crm.workflow.sdk.dto.oa.OaWithdrawDto;
import com.biz.crm.workflow.sdk.dto.oa.order.AdjustDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.order.AdjustMainDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaDetailDto;
import com.biz.crm.workflow.sdk.dto.oa.request.OaResubmitDto;
import com.biz.crm.workflow.sdk.dto.oa.request.WorkflowRequestTableField;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * OA接口
 */
@Service
@RefreshScope
public class BudgetAdjustOaServiceImpl implements BudgetAdjustOaService {

    @Autowired(required = false)
    private BudgetAdjustRepository budgetAdjustRepository;
    @Autowired(required = false)
    private BudgetAdjustService budgetAdjustService;
    @Autowired(required = false)
    private PushOaService pushOaService;
    @Autowired
    private UserVoService userVoService;
    @Autowired
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;
    @Value("${domain-name:}")
    private String domainName;
    @Autowired(required = false)
    private OrgOaOrgVoService orgOaOrgVoService;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;


    /**
     * 推送OA
     *
     * @param code
     * @return
     */
    @Override
    public JSONObject pushOa(String code, boolean isLog) {
        BudgetAdjustVo order = budgetAdjustService.findByCode(code);
        Set<String> orgCodeList = order.getDetails().stream().map(e -> e.getDepartmentOneCode()).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            order.setDeptCode(oaOrgCodeSet.stream().collect(Collectors.joining(",")));
        }


        // 传OA 有方法，这里只需要组装数据
        JSONObject oaParam = new JSONObject();
        // 将订单转化为JSONObject
        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(order));

        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);
        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());
        orderJsonObject.put("title", order.getAdjustName() + "-" + userVo.getFullName() + "-" + DateUtil.format(new Date(), "yyyy-MM-dd"));

        // 主表
        // 业务类型
        String businessType;
        if (order.getOperateType().equals(BudgetAdjustEnum.ADJUST.getCode())) {
            businessType = MqConstant.TAG_TPM_BUDGET_TRANSFER;
        } else {
            businessType = MqConstant.TAG_TPM_BUDGET_ADDITIONAL;
        }
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        String workflowName = orderJsonObject.getString("title");
        orderJsonObject.put("tpmUrl", domainName + TpmOaPageEnum.BUDGE_ADJUSTMENT_FORM.getUrlCode() + "?code=" + order.getAdjustCode() + "&operateType=" + order.getOperateType());
        JSONObject response = pushOaService.pushOA(orderJsonObject, businessType, oaParam, Arrays.asList(JSONArray.parseArray(JSON.toJSONString(order.getDetails()))), workflowId, workflowName,
                mainTableMethod, detailTableMethod);

        // {"msg":"执行成功","batchKey":"ecology_doCreatRequest_1719900204033_N2DC","code":100,"data":{"out":"1425430"}}
        if (response.containsKey("code")) {
            Integer resultCode = response.getInteger("code");
            if (resultCode == 100) {
                JSONObject ja = response.getJSONObject("data");
                BudgetAdjust entity = budgetAdjustRepository.findByCodeAndTenantCode(code, TenantUtils.getTenantCode());
                entity.setProcessNumber(ja.getString("out"));
                entity.setProcessDate(new Date());
                entity.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
                entity.setOaId(response.getString("oaId"));
                entity.setOaUserName(response.getString("oaUserName"));
                budgetAdjustRepository.updateById(entity);

                if (isLog) {
                    // 更新事件
                    BudgetAdjustEventDto eventDto = new BudgetAdjustEventDto();
                    eventDto.setOriginal(order);
                    BudgetAdjustVo newVo = budgetAdjustService.findByCode(code);
                    eventDto.setNewest(newVo);
                    SerializableBiConsumer<BudgetAdjustEventListener, BudgetAdjustEventDto> consumer =
                            BudgetAdjustEventListener::onUpdate;
                    this.nebulaNetEventClient.publish(eventDto, BudgetAdjustEventListener.class, consumer);
                }

            } else {
                Validate.isTrue(false, "OA流程提交失败，错误信息：" + response.getString("msg"));
            }
        }
        return response;
    }

    /**
     * 重新提交OA
     *
     * @param code
     * @return
     */
    @Override
    public JSONObject resubmitOa(String code, boolean isLog) {
        BudgetAdjustVo order = budgetAdjustService.findByCode(code);

        Set<String> orgCodeList = order.getDetails().stream().map(e -> e.getDepartmentOneCode()).collect(Collectors.toSet());
        Map<String, List<OrgOaOrgVo>> orgOaOrgVoMap = orgOaOrgVoService.findByOrgCodes(new ArrayList<>(orgCodeList));
        if (MapUtils.isNotEmpty(orgOaOrgVoMap)) {
            Set<String> oaOrgCodeSet = new HashSet<>();
            orgOaOrgVoMap.values().forEach(e -> oaOrgCodeSet.add(e.get(0).getOaOrgCode()));
            order.setDeptCode(oaOrgCodeSet.stream().collect(Collectors.joining(",")));
        }

        OaResubmitDto dto = new OaResubmitDto();

        // 获取当前登录人信息
        FacturerUserDetails loginDetails =
                this.loginUserService.getLoginDetails(FacturerUserDetails.class);

        UserVo userVo = userVoService.findByUserName(loginDetails.getUsername());

        String businessType;
        if (order.getOperateType().equals(BudgetAdjustEnum.ADJUST.getCode())) {
            businessType = MqConstant.TAG_TPM_BUDGET_TRANSFER;
        } else {
            businessType = MqConstant.TAG_TPM_BUDGET_ADDITIONAL;
        }
        DictDataVo dict = dictDataVoService.findByDictTypeCodeAndDictCode(BudgetAdjustConstant.OA_WORKFLOW_TYPE, businessType);
        String workflowId = dict.getDictValue();
        order.setTpmUrl(domainName + TpmOaPageEnum.BUDGE_ADJUSTMENT_FORM.getUrlCode() + "?code=" + order.getAdjustCode() + "&operateType=" + order.getOperateType());

        dto.setBusinessCode(businessType);
        dto.setRequestId(order.getProcessNumber());
        dto.setWorkflowId(workflowId);
        dto.setCreateOaId(userVo.getOaId());

        AdjustMainDto mainDto = nebulaToolkitService.copyObjectByWhiteList(order, AdjustMainDto.class, LinkedHashSet.class, ArrayList.class);
        dto.setRequestName(order.getAdjustName() + "-" + userVo.getFullName() + "-" + DateUtil.format(new Date(), "yyyy-MM-dd"));
        dto.setMainDto(JSONUtil.toJsonStr(mainDto));
        List<AdjustDetailDto> detailDtoList = (List<AdjustDetailDto>) nebulaToolkitService.copyCollectionByBlankList(order.getDetails(), BudgetAdjustDetailVo.class, AdjustDetailDto.class, LinkedHashSet.class, ArrayList.class);
        OaDetailDto oaDetailDto = new OaDetailDto();
        oaDetailDto.setDetailList(JSONUtil.toJsonStr(detailDtoList));
        oaDetailDto.setDetailClass(AdjustDetailDto.class);
        dto.setDetailList(Arrays.asList(oaDetailDto));

        if (ryOaProcessService.resubmit(dto)) {
            BudgetAdjust entity = budgetAdjustRepository.findByCodeAndTenantCode(code, TenantUtils.getTenantCode());
            entity.setProcessDate(new Date());
            entity.setStatus(ProcessStatusEnum.COMMIT.getDictCode());
            budgetAdjustRepository.updateById(entity);

            if (isLog) {
                // 更新事件
                BudgetAdjustEventDto eventDto = new BudgetAdjustEventDto();
                eventDto.setOriginal(order);
                BudgetAdjustVo newVo = budgetAdjustService.findByCode(code);
                eventDto.setNewest(newVo);
                SerializableBiConsumer<BudgetAdjustEventListener, BudgetAdjustEventDto> consumer =
                        BudgetAdjustEventListener::onUpdate;
                this.nebulaNetEventClient.publish(eventDto, BudgetAdjustEventListener.class, consumer);
            }
        }

        return null;
    }

    /**
     * OA撤回
     *
     * @param code
     * @return
     */
    @Override
    public boolean oaWithdraw(String code, String remark) {
        BudgetAdjustVo order = budgetAdjustService.findByCode(code);
        Validate.isTrue(order.getStatus().equals(ProcessStatusEnum.COMMIT.getDictCode()), "审批中才能撤回");
        OaWithdrawDto dto = new OaWithdrawDto();
        dto.setRequestId(Integer.valueOf(order.getProcessNumber()));
        dto.setProcessCreateId(order.getOaId());
        dto.setUserName(order.getOaUserName());
        dto.setRemark(remark);
        return ryOaProcessService.oaWithdraw(dto);
    }

    /**
     * 主列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> mainTableMethod = (tableFields,  orderJsonObject) -> {
        for (BudgetAdjustFieldsEnum value : BudgetAdjustFieldsEnum.values()) {
            WorkflowRequestTableField tableField = new WorkflowRequestTableField();
            tableField.setFieldName(value.getDictCode());
            tableField.setFieldValue(null == orderJsonObject.get(value.getDictCode()) ? "" : orderJsonObject.get(value.getDictCode()).toString());
            tableField.setView("true");
            tableField.setEdit("true");
            tableField.setMand("false");
            tableFields.add(tableField);
        }
    };

    /**
     * 明细列表
     */
    BiConsumer<List<WorkflowRequestTableField>, JSONObject> detailTableMethod = (tableDetailFields, detailJsonObject) -> {
        for (BudgetAdjustDetailFieldsEnum value : BudgetAdjustDetailFieldsEnum.values()) {
            WorkflowRequestTableField tableDetailField = new WorkflowRequestTableField();
            tableDetailField.setFieldName(value.getDictCode());
            tableDetailField.setFieldValue(null == detailJsonObject.get(value.getDictCode()) ? "" : detailJsonObject.get(value.getDictCode()).toString());
            tableDetailField.setView("true");
            tableDetailField.setEdit("true");
            tableDetailField.setMand("false");
            tableDetailFields.add(tableDetailField);
        }
    };
}
