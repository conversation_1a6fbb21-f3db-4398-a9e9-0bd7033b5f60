package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_approval_collect")
@Table(name = "tpm_approval_collect", indexes = {@Index(name = "tpm_approval_collect_index1", columnList = "tenant_code, code", unique = true)})
@ApiModel(value = "ApprovalCollect", description = "核销采集信息")
@org.hibernate.annotations.Table(appliesTo = "tpm_approval_collect", comment = "核销采集信息")
public class ApprovalCollect extends TenantFlagOpEntity {

  @ApiModelProperty("编码")
  @TableField(value = "code", fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '编码'")
  private String code;

  @ApiModelProperty("名称")
  @TableField(value = "name")
  @Column(name = "name", nullable = false, columnDefinition = "varchar(255) COMMENT '名称'")
  private String name;

  @ApiModelProperty("排序(值越小越靠前)")
  @TableField(value = "sort_index")
  @Column(name = "sort_index", length = 4, nullable = false, columnDefinition = "int(4) COMMENT '排序(值越小越靠前)'")
  private Integer sortIndex;

  @ApiModelProperty("描述")
  @TableField(value = "descr")
  @Column(name = "descr", columnDefinition = "varchar(255) COMMENT '描述'")
  private String descr;

  @ApiModelProperty("图片信息")
  @TableField(exist = false)
  @Transient
  private List<ApprovalCollectImage> images;
}
