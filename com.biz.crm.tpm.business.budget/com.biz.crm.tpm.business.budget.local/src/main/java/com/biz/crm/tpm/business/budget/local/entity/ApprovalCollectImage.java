package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_approval_collect_image")
@Table(name = "tpm_approval_collect_image")
@ApiModel(value = "ApprovalCollectImage", description = "核销采集图片")
@org.hibernate.annotations.Table(appliesTo = "tpm_approval_collect_image", comment = "核销采集图片")
public class ApprovalCollectImage extends FileEntity {

  @ApiModelProperty("核销采集编码")
  @TableField(value = "approval_collect_code")
  @Column(name = "approval_collect_code", nullable = false, columnDefinition = "varchar(255) COMMENT '核销采集编码'")
  private String approvalCollectCode;
}
