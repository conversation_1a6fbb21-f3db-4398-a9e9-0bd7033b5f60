package com.biz.crm.tpm.business.budget.local.service.internal;

import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.tpm.business.budget.sdk.constant.CostBudgetConstant;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetLockVoService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 预算加锁
 */
@Service
public class CostBudgetLockVoServiceImpl implements CostBudgetLockVoService {
    
    @Autowired(required = false)
    private RedisLockService redisLockService;

    /**
     * 根据预算编码加锁
     *
     * @param budgetCode
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 21:03
     **/
    @Override
    public boolean lock(String budgetCode, TimeUnit timeUnit, int time) {
        if (StringUtils.isEmpty(budgetCode)) {
            throw new RuntimeException("月度预算加锁失败，预算编码不能为空");
        }
        if (ObjectUtils.isEmpty(timeUnit)) {
            timeUnit = TimeUnit.MILLISECONDS;
        }
        if (time <= 0) {
            time = CostBudgetConstant.DEFAULT_LOCK_TIME;
        }
        return this.redisLockService.tryLock(CostBudgetConstant.COST_BUDGET_LOCK + budgetCode, timeUnit, time);
    }

    /**
     * 根据预算编码批量加锁
     *
     * @param budgetCodeList
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public boolean lock(List<String> budgetCodeList, TimeUnit timeUnit, int time) {
        return this.lock(budgetCodeList, timeUnit, time, CostBudgetConstant.DEFAULT_WAITE_TIME_FIVE);
    }


    /**
     * 根据预算编码批量加锁
     *
     * @param budgetCodeList
     * @param timeUnit
     * @param lockTime
     * @param waiteTime
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public boolean lock(List<String> budgetCodeList, TimeUnit timeUnit, int lockTime, int waiteTime) {
        if (CollectionUtils.isEmpty(budgetCodeList)) {
            throw new RuntimeException("月度预算加锁失败，预算编码不能为空");
        }
        if (ObjectUtils.isEmpty(timeUnit)) {
            timeUnit = TimeUnit.MILLISECONDS;
        }
        if (lockTime <= 0) {
            lockTime = CostBudgetConstant.DEFAULT_LOCK_TIME;
        }
        if (waiteTime <= 0) {
            waiteTime = CostBudgetConstant.DEFAULT_WAITE_TIME_FIVE;
        }

        boolean isLock = true;
        List<String> successKeys = new ArrayList<>();
        try {
            // 循环加锁，并记录成功的key
            for (String budgetCode : budgetCodeList) {
                isLock = this.redisLockService.tryLock(CostBudgetConstant.COST_BUDGET_LOCK + budgetCode, timeUnit, lockTime, waiteTime);
                if (!isLock) {
                    return false;
                }
                successKeys.add(budgetCode);
            }
        } finally {
            // 存在加锁失败的情况，则先将成功的解锁
            if (!isLock && !CollectionUtils.isEmpty(successKeys)) {
                successKeys.forEach(key -> {
                    redisLockService.unlock(CostBudgetConstant.COST_BUDGET_LOCK + key);
                });
            }
        }
        return true;
    }

    /**
     * 根据预算编码解锁
     *
     * @param budgetCode
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public void unLock(String budgetCode) {
        if (StringUtils.isEmpty(budgetCode)) {
            throw new RuntimeException("月度预算解锁失败，预算编码不能为空");
        }
        redisLockService.unlock(CostBudgetConstant.COST_BUDGET_LOCK + budgetCode);
    }

    /**
     * 根据预算编码批量解锁
     *
     * @param budgetCodeList
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public void unLock(List<String> budgetCodeList) {
        if (CollectionUtils.isEmpty(budgetCodeList)) {
            throw new RuntimeException("月度预算解锁失败，预算编码不能为空");
        }
        budgetCodeList.forEach(budgetCode -> {
            redisLockService.unlock(CostBudgetConstant.COST_BUDGET_LOCK + budgetCode);
        });
    }
}
