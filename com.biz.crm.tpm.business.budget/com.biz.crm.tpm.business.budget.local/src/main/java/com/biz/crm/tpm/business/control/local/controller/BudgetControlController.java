package com.biz.crm.tpm.business.control.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlDto;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlVoService;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import com.biz.crm.tpm.business.track.sdk.service.BudgetTrackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "预算管控功能接口")
@RestController
@RequestMapping("/v1/budget/budgetControl")
@Slf4j
public class BudgetControlController {

    @Autowired(required = false)
    private BudgetControlVoService budgetControlVoService;

    @Autowired
    private BudgetTrackService budgetTrackService;

    /**
     * 新增数据
     *
     * @param budgetControlDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @PostMapping
    public Result<?> create(@ApiParam(name = "cacheKeyCus", value = "客户缓存键") @RequestParam(required = false) String cacheKeyCus,
                            @ApiParam(name = "cacheKeyDept", value = "部门缓存键") @RequestParam(required = false) String cacheKeyDept,
                            @ApiParam(name = "cacheKeyCusExclude", value = "客户剔除缓存键") @RequestParam(required = false) String cacheKeyCusExclude,
                            @ApiParam(name = "cacheKeyDeptExclude", value = "部门剔除缓存键") @RequestParam(required = false) String cacheKeyDeptExclude,
                            @ApiParam(name = "cacheKeySubject", value = "预算科目缓存键") @RequestParam(required = false) String cacheKeySubject,
                            @ApiParam(name = "budgetControlDto", value = "预算管控") @RequestBody BudgetControlDto budgetControlDto) {
        try {
            this.budgetControlVoService.create(budgetControlDto, cacheKeyCus, cacheKeyDept, cacheKeyCusExclude, cacheKeyDeptExclude, cacheKeySubject);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改数据
     *
     * @param budgetControlDto 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @PatchMapping
    public Result<?> update(@ApiParam(name = "cacheKeyCus", value = "客户缓存键") @RequestParam(required = false) String cacheKeyCus,
                            @ApiParam(name = "cacheKeyDept", value = "部门缓存键") @RequestParam(required = false) String cacheKeyDept,
                            @ApiParam(name = "cacheKeyCusExclude", value = "客户剔除缓存键") @RequestParam(required = false) String cacheKeyCusExclude,
                            @ApiParam(name = "cacheKeyDeptExclude", value = "部门剔除缓存键") @RequestParam(required = false) String cacheKeyDeptExclude,
                            @ApiParam(name = "cacheKeySubject", value = "预算科目缓存键") @RequestParam(required = false) String cacheKeySubject,
                            @ApiParam(name = "budgetControlDto", value = "预算管控") @RequestBody BudgetControlDto budgetControlDto) {
        try {
            this.budgetControlVoService.update(budgetControlDto, cacheKeyCus, cacheKeyDept, cacheKeyCusExclude, cacheKeyDeptExclude, cacheKeySubject);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation(value = "删除数据")
    public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
        try {
            this.budgetControlVoService.delete(idList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过编号查询单条数据
     *
     * @param code 编号
     * @return 单条数据
     */
    @ApiOperation(value = "通过主键查询单条数据")
    @GetMapping("findByCode")
    public Result<BudgetControlVo> findByCode(@RequestParam("code") @ApiParam(name = "code", value = "编号") String code) {
        try {
            BudgetControlVo budgetControlVo = this.budgetControlVoService.findByCode(code);
            return Result.ok(budgetControlVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量根据id启用
     */
    @ApiOperation(value = "批量根据id启用")
    @PatchMapping(value = "enable")
    public Result<?> enable(@RequestBody List<String> ids) {
        try {
            this.budgetControlVoService.enable(ids);
            return Result.ok("启用成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量根据id禁用
     */
    @ApiOperation(value = "批量根据id禁用")
    @PatchMapping(value = "disable")
    public Result<?> disable(@RequestBody List<String> ids) {
        try {
            this.budgetControlVoService.disable(ids);
            return Result.ok("禁用成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "测算管控规则预算费用")
    @PostMapping("generateBudgetTrackByCodes")
    public Result generateBudgetTrackByCodes(@RequestBody List<String> codes) {
        budgetTrackService.generateBudgetTrackByCodes(codes);
        return Result.ok();
    }
}
