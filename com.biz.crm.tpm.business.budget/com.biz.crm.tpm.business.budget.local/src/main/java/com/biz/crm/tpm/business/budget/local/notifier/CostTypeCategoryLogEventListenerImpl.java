package com.biz.crm.tpm.business.budget.local.notifier;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeCategoryLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.event.log.CostTypeCategoryLogEventListener;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @describe: 活动大类业务日志实现类
 * @createTime 2022年06月22日 16:28:00
 */
@Component
public class CostTypeCategoryLogEventListenerImpl implements CostTypeCategoryLogEventListener {
  
  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;

  @Override
  public void onCreate(CostTypeCategoryLogEventDto eventDto) {
    CostTypeCategoryVo newest = eventDto.getNewest();
    CostTypeCategoryVo original = eventDto.getOriginal();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onDelete(CostTypeCategoryLogEventDto eventDto) {
    CostTypeCategoryVo newest = eventDto.getNewest();
    CostTypeCategoryVo original = eventDto.getOriginal();
    String onlyKey =original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onUpdate(CostTypeCategoryLogEventDto eventDto) {
    CostTypeCategoryVo newest = eventDto.getNewest();
    CostTypeCategoryVo original = eventDto.getOriginal();
    String onlyKey =original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onEnable(CostTypeCategoryLogEventDto eventDto) {
    CostTypeCategoryVo newest = eventDto.getNewest();
    CostTypeCategoryVo original = eventDto.getOriginal();
    String onlyKey =original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    //只传单个字段
    JSONObject oldObject =new JSONObject();
    JSONObject newObject =new JSONObject();
    oldObject.put("enableStatus",original.getEnableStatus());
    newObject.put("enableStatus", EnableStatusEnum.ENABLE.getCode());
    crmBusinessLogDto.setOldObject(oldObject);
    crmBusinessLogDto.setNewObject(newObject);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onDisable(CostTypeCategoryLogEventDto eventDto) {
    CostTypeCategoryVo newest = eventDto.getNewest();
    CostTypeCategoryVo original = eventDto.getOriginal();
    String onlyKey =original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    //只传单个字段
    JSONObject oldObject =new JSONObject();
    JSONObject newObject =new JSONObject();
    oldObject.put("enableStatus",original.getEnableStatus());
    newObject.put("enableStatus", EnableStatusEnum.DISABLE.getCode());
    crmBusinessLogDto.setOldObject(oldObject);
    crmBusinessLogDto.setNewObject(newObject);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
}
