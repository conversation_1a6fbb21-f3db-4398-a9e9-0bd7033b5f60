package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeMapping;
import com.biz.crm.tpm.business.budget.local.mapper.CostTypeMappingMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * TPM-活动大类与活动细类关联;(tpm_cost_type_mapping)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@Component
@Deprecated
public class CostTypeMappingRepository extends ServiceImpl<CostTypeMappingMapper, CostTypeMapping> {
  @Autowired
  private CostTypeMappingMapper costTypeMappingMapper;

  /**
   * 根据活动明细编号与租户编号删除明细与活动大类关联数据
   *
   * @param detailCode
   */
  public void deleteByDetailCode(String detailCode) {
    if (StringUtils.isBlank(detailCode)) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    this.lambdaUpdate()
            .eq(CostTypeMapping::getDetailCode, detailCode)
            .eq(CostTypeMapping::getTenantCode, tenantCode).remove();
  }
}