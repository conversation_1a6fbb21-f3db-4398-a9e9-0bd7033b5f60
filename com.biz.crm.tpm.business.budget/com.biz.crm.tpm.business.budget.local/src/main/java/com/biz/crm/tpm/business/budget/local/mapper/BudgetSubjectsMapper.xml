<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.budget.local.mapper.BudgetSubjectsMapper">
    <resultMap type="com.biz.crm.tpm.business.budget.local.entity.BudgetSubjects" id="BudgetSubjectsMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="enableStatus" column="enable_status" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createAccount" column="create_account" jdbcType="VARCHAR"/>
        <result property="createName" column="create_name" jdbcType="VARCHAR"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="modifyName" column="modify_name" jdbcType="VARCHAR"/>
        <result property="modifyAccount" column="modify_account" jdbcType="VARCHAR"/>
        <result property="budgetSubjectsCode" column="budget_subjects_code" jdbcType="VARCHAR"/>
        <result property="budgetSubjectsName" column="budget_subjects_name" jdbcType="VARCHAR"/>
        <result property="budgetSubjectsType" column="budget_subjects_type" jdbcType="VARCHAR"/>
        <result property="controlTypeCode" column="control_type_code" jdbcType="VARCHAR"/>
        <result property="controlTypeName" column="control_type_name" jdbcType="VARCHAR"/>
        <result property="groupCode" column="group_code" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="findByConditions" resultType="com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo">
        select
        t.*
        from tpm_budget_subjects t
        <where>
            <if test="dto.enableStatus != null and dto.enableStatus != ''">
                and t.enable_status = #{dto.enableStatus}
            </if>
            <if test="dto.budgetSubjectsCode != null and dto.budgetSubjectsCode != ''">
                <bind name="budgetSubjectsCode" value="'%' + dto.budgetSubjectsCode + '%'"/>
                and t.budget_subjects_code like #{budgetSubjectsCode}
            </if>
            <if test="dto.budgetSubjectsName != null and dto.budgetSubjectsName != ''">
                <bind name="budgetSubjectsName" value="'%' + dto.budgetSubjectsName + '%'"/>
                and t.budget_subjects_name like #{budgetSubjectsName}
            </if>
            <if test="dto.budgetSubjectsType != null and dto.budgetSubjectsType != ''">
                and t.budget_subjects_type = #{dto.budgetSubjectsType}
            </if>
            <if test="dto.controlTypeCode != null and dto.controlTypeCode != ''">
                and t.control_type_code = #{dto.controlTypeCode}
            </if>
            <if test="dto.groupCode != null and dto.groupCode != ''">
                and t.group_code = #{dto.groupCode}
            </if>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.level != null and dto.level != ''">
                and t.level = #{dto.level}
            </if>
            <if test="dto.excludeBudgetSubjectsCodes != null and dto.excludeBudgetSubjectsCodes.size() > 0">
                and t.budget_subjects_code not in
                <foreach collection="dto.excludeBudgetSubjectsCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
        order by
        <if test="dto.selectedCodeList != null and dto.selectedCodeList.size > 0">
            CASE
            <foreach collection="dto.selectedCodeList" item="item" index="index">
                WHEN t.budget_subjects_code = #{item} THEN ${index}
            </foreach>
            ELSE 99 END asc,
        </if>
        t.create_time desc, t.id
    </select>

    <select id="findHasChildSubjectCodeList" resultType="java.lang.String">
        select
            distinct t.parent_budget_subjects_code
        from tpm_budget_subjects t
        where t.del_flag = '${@<EMAIL>()}'
        and t.parent_budget_subjects_code in
        <foreach collection="budgetSubjectCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findAllParentSubjectCodesByCodes" resultType="java.lang.String">
        WITH RECURSIVE parent_subjects AS (SELECT budget_subjects_code,
                                                  parent_budget_subjects_code
                                           FROM tpm_budget_subjects
                                           WHERE budget_subjects_code = #{budgetSubjectCode}
                                           UNION ALL
                                           SELECT t.budget_subjects_code,
                                                  t.parent_budget_subjects_code
                                           FROM tpm_budget_subjects t
                                                    INNER JOIN
                                                parent_subjects p
                                                ON
                                                    t.budget_subjects_code = p.parent_budget_subjects_code)
        SELECT budget_subjects_code
        FROM parent_subjects
    </select>

    <select id="findBudgetSubjectByDetailCode" resultType="java.lang.String">
        SELECT b.budget_subjects_code
        FROM tpm_cost_type_detail a
                 LEFT JOIN tpm_cost_type_category b ON a.category_code = b.category_code
        WHERE a.detail_code = #{detailCode}
    </select>
    <select id="findByCodeList" resultType="com.biz.crm.tpm.business.budget.local.entity.BudgetSubjects">
        select
        t.*
        from tpm_budget_subjects t
        where t.del_flag = '${@<EMAIL>()}'
        and t.budget_subjects_code in
        <foreach collection="subjectCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>