package com.biz.crm.tpm.business.adjust.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.business.adjust.local.entity.BudgetAdjust;
import com.biz.crm.tpm.business.adjust.local.entity.BudgetAdjustDetail;
import com.biz.crm.tpm.business.adjust.local.repository.BudgetAdjustDetailRepository;
import com.biz.crm.tpm.business.adjust.local.repository.BudgetAdjustRepository;
import com.biz.crm.tpm.business.adjust.sdk.constant.BudgetAdjustConstant;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDetailDto;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDto;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustEventDto;
import com.biz.crm.tpm.business.adjust.sdk.enums.BudgetAdjustEnum;
import com.biz.crm.tpm.business.adjust.sdk.event.BudgetAdjustEventListener;
import com.biz.crm.tpm.business.adjust.sdk.service.BudgetAdjustOaService;
import com.biz.crm.tpm.business.adjust.sdk.service.BudgetAdjustService;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustDetailVo;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustVo;
import com.biz.crm.tpm.business.budget.local.entity.CostBudget;
import com.biz.crm.tpm.business.budget.local.repository.CostBudgetRepository;
import com.biz.crm.tpm.business.budget.sdk.dto.OperateBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.enums.CostBudgetOperateType;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetLockVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetOperateVoService;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.service.RyOaProcessService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BudgetAdjustServiceImpl implements BudgetAdjustService {

    @Autowired(required = false)
    private BudgetAdjustRepository budgetAdjustRepository;
    @Autowired(required = false)
    private BudgetAdjustDetailRepository budgetAdjustDetailRepository;
    @Autowired(required = false)
    private CostBudgetLockVoService costBudgetLockVoService;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private CostBudgetOperateVoService costBudgetOperateVoService;
    @Autowired(required = false)
    private CostBudgetRepository costBudgetRepository;
    @Autowired(required = false)
    private BudgetAdjustOaService budgetAdjustOaService;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired(required = false)
    private RyOaProcessService ryOaProcessService;

    /**
     * 通过编码查询单条数据
     *
     * @param code 编码
     * @return 单条数据
     */
    @Override
    public BudgetAdjustVo findByCode(String code) {
        if (StringUtil.isEmpty(code)){
            return null;
        }
        BudgetAdjust entity = budgetAdjustRepository.findByCodeAndTenantCode(code, TenantUtils.getTenantCode());
        if (Objects.isNull(entity)){
            return null;
        }
        BudgetAdjustVo budgetAdjustVo = nebulaToolkitService.copyObjectByWhiteList(entity, BudgetAdjustVo.class, LinkedHashSet.class, ArrayList.class);
        List<BudgetAdjustDetailVo> detailVoList = budgetAdjustDetailRepository.findByCode(code);
        if (CollectionUtil.isEmpty(detailVoList)){
            return budgetAdjustVo;
        }
        Set<String> budgetCodeList = detailVoList.stream().map(BudgetAdjustDetailVo::getBudgetCode).collect(Collectors.toSet());
        Map<String, CostBudget> budgetMap = costBudgetRepository.findByCodesAndTenantCode(budgetCodeList, TenantUtils.getTenantCode()).stream().collect(Collectors.toMap(CostBudget::getCode, Function.identity()));

        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> maxAmount = new AtomicReference<>(BigDecimal.ZERO);
        detailVoList.forEach(e -> {
            CostBudget costBudget = budgetMap.get(e.getBudgetCode());
            if (Objects.isNull(costBudget)){
                return;
            }
            e.setBudgetSubjectCode(costBudget.getBudgetSubjectCode());
            e.setBudgetSubjectName(costBudget.getBudgetSubjectName());
            e.setYearMonthLy(costBudget.getYearMonthLy());
            e.setDepartmentOneCode(costBudget.getDepartmentOneCode());
            e.setDepartmentOneName(costBudget.getDepartmentOneName());
            e.setCompanyCode(costBudget.getCompanyCode());
            e.setCompanyName(costBudget.getCompanyName());
            e.setCostCenterCode(costBudget.getCostCenterCode());
            e.setCostCenterName(costBudget.getCostCenterName());
            e.setItemCode(costBudget.getItemCode());
            e.setItemName(costBudget.getItemName());
            e.setProductCode(costBudget.getProductCode());
            e.setProductName(costBudget.getProductName());
            e.setCustomerCode(costBudget.getCustomerCode());
            e.setCustomerName(costBudget.getCustomerName());
            if (entity.getOperateType().equals(BudgetAdjustEnum.ADJUST.getCode())) {
                if (e.getAdjustAmount().compareTo(BigDecimal.ZERO) > 0) {
                    total.set(total.get().add(e.getAdjustAmount()));
                    maxAmount.set(maxAmount.get().max(e.getAdjustAmount()));
                }
            } else {
                total.set(total.get().add(e.getAdjustAmount()));
                maxAmount.set(maxAmount.get().max(e.getAdjustAmount()));
            }
        });
        budgetAdjustVo.setDetails(detailVoList);
        budgetAdjustVo.setTotal(total.get());
        budgetAdjustVo.setMaxAmount(maxAmount.get());
        budgetAdjustVo.setBusinessCode(budgetAdjustVo.getAdjustCode());
        return budgetAdjustVo;
    }

    /**
     * 调整
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void adjust(BudgetAdjustDto dto, boolean beBatch) {
        if (!beBatch) {
            validateCommon(dto, true);
        }
        dto.setOperateType(BudgetAdjustEnum.ADJUST.getCode());
        storeAndBudgetOperate(dto, false, beBatch);
        budgetAdjustOaService.pushOa(dto.getAdjustCode(), !beBatch);
    }

    /**
     * 变更
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void change(BudgetAdjustDto dto, boolean beBatch) {
        if (!beBatch) {
            validateCommon(dto, false);
        }
        dto.setOperateType(BudgetAdjustEnum.CHANGE.getCode());
        storeAndBudgetOperate(dto, false, beBatch);
        budgetAdjustOaService.pushOa(dto.getAdjustCode(), !beBatch);
    }

    /**
     * 编辑
     *
     * @param dto
     */
    @Override
    public void update(BudgetAdjustDto dto) {
        validateCommon(dto, BudgetAdjustEnum.ADJUST.getCode().equals(dto.getOperateType()));
        storeAndBudgetOperate(dto, false, false);
    }

    /**
     * 保存及预算操作
     *
     * @param dto
     */
    public void storeAndBudgetOperate(BudgetAdjustDto dto, boolean beReject, boolean beBatch) {
        store(dto, beBatch);
        budgetOperate(dto, true, beReject);
    }

    /**
     * 审批通过
     *
     * @param code
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approve(String code, String processStatus) {
        BudgetAdjustVo vo = findByCode(code);
        if (vo == null) {
            return;
        }
        BudgetAdjustDto dto = nebulaToolkitService.copyObjectByWhiteList(vo, BudgetAdjustDto.class, LinkedHashSet.class, ArrayList.class, "details");
        List<BudgetAdjustDetailDto> details = new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(vo.getDetails(), BudgetAdjustDetailVo.class, BudgetAdjustDetailDto.class, LinkedHashSet.class, ArrayList.class));
        dto.setDetails(details);
        Set<String> budgetCodeList = details.stream().map(BudgetAdjustDetailDto::getBudgetCode).collect(Collectors.toSet());
        List<CostBudget> budgetEntityList = costBudgetRepository.findByCodesAndTenantCode(budgetCodeList, TenantUtils.getTenantCode());
        if (budgetEntityList.size() < budgetCodeList.size()) {
            List<String> existsCodes = budgetEntityList.stream().map(CostBudget::getCode).collect(Collectors.toList());
            String notExistsJoinCodesStr = budgetCodeList.stream().filter(item -> !existsCodes.contains(item)).collect(Collectors.joining(","));
            BudgetAdjust entity = nebulaToolkitService.copyObjectByWhiteList(dto, BudgetAdjust.class, LinkedHashSet.class, ArrayList.class);
            entity.setStatus(processStatus);
            budgetAdjustRepository.updateById(entity);
        } else {
            budgetOperate(dto, false, ProcessStatusEnum.REJECT.getDictCode().equals(processStatus));
            BudgetAdjust entity = nebulaToolkitService.copyObjectByWhiteList(dto, BudgetAdjust.class, LinkedHashSet.class, ArrayList.class);
            entity.setStatus(processStatus);
            budgetAdjustRepository.updateById(entity);
        }
    }

    /**
     * 提交
     *
     * @param idList
     */
    @Override
    public void submit(List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "提交时，主键集合不能为空！");
        List<BudgetAdjust> entities = this.budgetAdjustRepository.findByIds(idList);
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        entities.forEach(item -> Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) || ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】核销数据能提交"));
        entities.forEach(e -> {
            if (ProcessStatusEnum.PREPARE.getDictCode().equals(e.getStatus())) {
                budgetAdjustOaService.pushOa(e.getAdjustCode(), true);
            } else {
                budgetAdjustOaService.resubmitOa(e.getAdjustCode(), true);
            }
        });
    }

    /**
     * 流程撤回
     *
     * @param code
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void recover(String code, String remark) {
        if (budgetAdjustOaService.oaWithdraw(code, remark)) {
            BudgetAdjust entity = budgetAdjustRepository.findByCodeAndTenantCode(code, TenantUtils.getTenantCode());
            entity.setStatus(ProcessStatusEnum.RECOVER.getDictCode());
            budgetAdjustRepository.saveOrUpdate(entity);
        } else {
            Validate.isTrue(false, "流程撤回失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        Validate.isTrue(!CollectionUtils.isEmpty(ids), "删除数据时，主键集合不能为空！");
        List<BudgetAdjust> entities = this.budgetAdjustRepository.findByIds(ids);
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        entities.forEach(item -> Validate.isTrue(ProcessStatusEnum.REJECT.getDictCode().equals(item.getStatus()) || ProcessStatusEnum.PREPARE.getDictCode().equals(item.getStatus())
                        || ProcessStatusEnum.RECOVER.getDictCode().equals(item.getStatus()),
                "只有【待提交】、【驳回】、【追回】核销数据能删除"));
        //删除OA接口
        List<BudgetAdjust> oaDelete = entities.stream().filter(e -> Arrays.asList(ProcessStatusEnum.REJECT.getDictCode(), ProcessStatusEnum.RECOVER.getDictCode()).contains(e.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(oaDelete)) {
            oaDelete.forEach(e -> ryOaProcessService.deleteWorkflow(e.getProcessNumber(), e.getOaId()));
        }
        List<String> codes = entities.stream().map(e -> e.getAdjustCode()).collect(Collectors.toList());
        budgetAdjustDetailRepository.deleteByCodes(codes);
        budgetAdjustRepository.removeByIds(ids);
    }

    /**
     * 预算操作
     *
     * @param dto
     */
    public void budgetOperate(BudgetAdjustDto dto, boolean beSubmit, boolean beReject) {
        List<BudgetAdjustDetailDto> detailDtos = dto.getDetails();
        List<String> budgetCodeAll = detailDtos.stream().map(BudgetAdjustDetailDto::getBudgetCode).collect(Collectors.toList());
        boolean lock = costBudgetLockVoService.lock(budgetCodeAll, TimeUnit.MINUTES, 30);
        Validate.isTrue(lock, "操作预算失败，获取操作锁失败！");
        List<OperateBudgetDto> operateList = new ArrayList<>();
        detailDtos.forEach(e -> {
            OperateBudgetDto operateBudgetDtoOut = new OperateBudgetDto();
            operateBudgetDtoOut.setBudgetCode(e.getBudgetCode());
            operateBudgetDtoOut.setBusinessCode(e.getAdjustDetailCode());
            operateBudgetDtoOut.setOperationAmount(e.getAdjustAmount());
            if (!beReject) {
                if (beSubmit) {
                    if (BudgetAdjustEnum.TRANSFER_IN.getCode().equals(e.getChangeType()) || BudgetAdjustEnum.ADDITIONAL.getCode().equals(e.getChangeType())) {
                        operateBudgetDtoOut.setOperationType(CostBudgetOperateType.TRANSFER_IN_LOCK.getCode());
                    } else {
                        operateBudgetDtoOut.setOperationType(CostBudgetOperateType.FREEZE.getCode());
                    }
                } else {
                    operateBudgetDtoOut.setOperationType(e.getChangeType());
                }
            } else {
                if (BudgetAdjustEnum.TRANSFER_IN.getCode().equals(e.getChangeType()) || BudgetAdjustEnum.ADDITIONAL.getCode().equals(e.getChangeType())) {
                    operateBudgetDtoOut.setOperationType(CostBudgetOperateType.TRANSFER_IN_UNLOCK.getCode());
                } else {
                    operateBudgetDtoOut.setOperationType(CostBudgetOperateType.UNFREEZE.getCode());
                }
            }
            operateList.add(operateBudgetDtoOut);
        });
        try {
            costBudgetOperateVoService.operateBudget(operateList);
        } finally {
            costBudgetLockVoService.unLock(budgetCodeAll);
        }
    }

    /**
     * 保存
     *
     * @param
     */
    public void store(BudgetAdjustDto dto, boolean beBatch) {
        BudgetAdjustVo oldEntity = null;
        if (StringUtils.isBlank(dto.getId())) {
            FacturerUserDetails loginUserDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
            Validate.notNull(loginUserDetails, "未找到当前登陆人信息！");
            dto.setOrgCode(loginUserDetails.getOrgCode());
            dto.setOrgName(loginUserDetails.getOrgName());
            dto.setPositionCode(loginUserDetails.getPostCode());
            List<String> codeList = this.generateCodeService.generateCodeNotDate(BudgetAdjustConstant.BUDGET_ADJUST_RULE_CODE, 1);
            dto.setAdjustCode(codeList.get(0));
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            dto.setTenantCode(TenantUtils.getTenantCode());
            dto.setStatus(ProcessStatusEnum.PREPARE.getDictCode());
        } else {
            oldEntity = this.findByCode(dto.getAdjustCode());
            budgetAdjustDetailRepository.deleteByCodes(Collections.singletonList(dto.getAdjustCode()));
        }
        BudgetAdjust entity = nebulaToolkitService.copyObjectByWhiteList(dto, BudgetAdjust.class, LinkedHashSet.class, ArrayList.class);

        List<BudgetAdjustDetailDto> detailDtoLists = dto.getDetails();
        List<String> detailCodeList = this.generateCodeService.generateCodeNotDate(BudgetAdjustConstant.BUDGET_ADJUST_DETAIL_RULE_CODE, detailDtoLists.size());
        AtomicInteger index = new AtomicInteger(0);

        detailDtoLists.forEach(e -> {
            if (BudgetAdjustEnum.ADJUST.getCode().equals(dto.getOperateType())) {
                if (e.getAdjustAmount().compareTo(BigDecimal.ZERO) > 0) {
                    e.setChangeType(BudgetAdjustEnum.TRANSFER_IN.getCode());
                } else {
                    e.setChangeType(BudgetAdjustEnum.TRANSFER_OUT.getCode());
                }
            } else {
                if (e.getAdjustAmount().compareTo(BigDecimal.ZERO) > 0) {
                    e.setChangeType(BudgetAdjustEnum.ADDITIONAL.getCode());
                } else {
                    e.setChangeType(BudgetAdjustEnum.CUT_OUT.getCode());
                }
            }
            e.setId(null);
            e.setAdjustName(entity.getAdjustName());
            e.setAdjustCode(entity.getAdjustCode());
            e.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            e.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            e.setTenantCode(TenantUtils.getTenantCode());
            e.setAdjustDetailCode(detailCodeList.get(index.get()));
            index.getAndAdd(1);
        });

        budgetAdjustRepository.saveOrUpdate(entity);
        budgetAdjustDetailRepository.saveBatch(nebulaToolkitService.copyCollectionByBlankList(detailDtoLists, BudgetAdjustDetailDto.class, BudgetAdjustDetail.class, LinkedHashSet.class, ArrayList.class));
        dto.setId(entity.getId());


        if (!beBatch) {
            // 创建事件
            if (oldEntity == null) {
                BudgetAdjustEventDto eventDto = new BudgetAdjustEventDto();
                eventDto.setOriginal(null);
                BudgetAdjustVo newVo = this.findByCode(entity.getAdjustCode());
                eventDto.setNewest(newVo);
                SerializableBiConsumer<BudgetAdjustEventListener, BudgetAdjustEventDto> consumer =
                        BudgetAdjustEventListener::onCreate;
                this.nebulaNetEventClient.publish(eventDto, BudgetAdjustEventListener.class, consumer);
                // 修改事件
            } else {
                BudgetAdjustEventDto eventDto = new BudgetAdjustEventDto();
                eventDto.setOriginal(oldEntity);
                BudgetAdjustVo newVo = this.findByCode(entity.getAdjustCode());
                eventDto.setNewest(newVo);
                SerializableBiConsumer<BudgetAdjustEventListener, BudgetAdjustEventDto> consumer =
                        BudgetAdjustEventListener::onUpdate;
                this.nebulaNetEventClient.publish(eventDto, BudgetAdjustEventListener.class, consumer);
            }
        }

    }

    /**
     * 校验
     *
     * @param dto
     */
    private void validateCommon(BudgetAdjustDto dto, boolean beAdjust) {
        Validate.notNull(dto, "对象不能为空");
        Validate.notBlank(dto.getAdjustName(), "名称，不能为空");
        Validate.isTrue(dto.getAdjustName().length() <= 128, "预算调整名称不能超过128个字符！");
        Validate.notEmpty(dto.getDetails(), "明细，不能为空");

        BigDecimal sumAmount = BigDecimal.ZERO;
        for (BudgetAdjustDetailDto e: dto.getDetails()) {
            Validate.notEmpty(e.getBudgetCode(), "预算编码，不能为空");
            Validate.isTrue(e.getAdjustAmount() != null && e.getAdjustAmount().compareTo(BigDecimal.ZERO) != 0, "调整金额不能为空，并且要不等于0");
            if (e.getAdjustAmount().compareTo(BigDecimal.ZERO) < 0) {
                Validate.isTrue(e.getAdjustAmount().abs().compareTo(e.getMonthBalanceAmount()) <= 0, "调减的金额必须小于等于余额");
            }
            sumAmount = sumAmount.add(e.getAdjustAmount());
        }
        if (beAdjust) {
            Validate.isTrue(sumAmount.compareTo(BigDecimal.ZERO) == 0, "所有转移金额之和必须为0");
        }
        if (StringUtils.isNotBlank(dto.getRemark())) {
            Validate.isTrue(dto.getRemark().length() <= 2000, "备注不能超过2000个字符！");
        }
    }
}
