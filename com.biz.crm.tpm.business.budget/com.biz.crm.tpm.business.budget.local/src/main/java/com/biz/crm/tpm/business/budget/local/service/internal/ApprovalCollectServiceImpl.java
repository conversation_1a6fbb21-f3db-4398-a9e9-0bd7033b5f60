package com.biz.crm.tpm.business.budget.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.tpm.business.budget.local.entity.ApprovalCollect;
import com.biz.crm.tpm.business.budget.local.entity.ApprovalCollectTypeEntity;
import com.biz.crm.tpm.business.budget.local.repository.ApprovalCollectRepository;
import com.biz.crm.tpm.business.budget.local.repository.ApprovalCollectTypeRepository;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectDto;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectImageDto;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectTypeDto;
import com.biz.crm.tpm.business.budget.sdk.enums.ApprovalCollectType;
import com.biz.crm.tpm.business.budget.sdk.event.ApprovalCollectEventListener;
import com.biz.crm.tpm.business.budget.sdk.event.log.ApprovalCollectLogEventListener;
import com.biz.crm.tpm.business.budget.sdk.service.ApprovalCollectImageVoService;
import com.biz.crm.tpm.business.budget.sdk.service.ApprovalCollectVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectImageVo;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 核销采集信息(ApprovalCollect)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-17 12:53:55
 */
@Service("approvalCollectService")
public class ApprovalCollectServiceImpl implements ApprovalCollectVoService {

  @Autowired
  private ApprovalCollectRepository approvalCollectRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private ApprovalCollectImageVoService approvalCollectImageService;
  @Autowired(required = false)
  private List<ApprovalCollectEventListener> approvalCollectEventListeners;
  @Autowired
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired
  private GenerateCodeService generateCodeService;
  @Autowired
  private ApprovalCollectTypeRepository approvalCollectTypeRepository;

  /**
   * 分页查询数据
   */
  public Page<ApprovalCollectVo> findByConditions(Pageable pageable, ApprovalCollectDto dto) {
    if (pageable == null) {
      pageable = PageRequest.of(0, 50);
    }
    if (dto == null) {
      dto = new ApprovalCollectDto();
    }
    if (StringUtils.isBlank(dto.getTenantCode())) {
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    if (StringUtils.isBlank(dto.getDelFlag())) {
      dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    }
    return this.approvalCollectRepository.findByConditions(pageable, dto);
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  public ApprovalCollectVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    ApprovalCollect approvalCollect = approvalCollectRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (approvalCollect == null) {
      return null;
    }
    return nebulaToolkitService.copyObjectByWhiteList(approvalCollect, ApprovalCollectVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public List<ApprovalCollectVo> findByIds(Set<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<ApprovalCollect> approvalCollects = approvalCollectRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(approvalCollects)) {
      return Lists.newArrayList();
    }
    return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(approvalCollects, ApprovalCollect.class, ApprovalCollectVo.class, HashSet.class, ArrayList.class));
  }

  /**
   * 根据主键查询详情
   */
  public ApprovalCollectVo findDetailsById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    ApprovalCollect approvalCollect = approvalCollectRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (approvalCollect == null) {
      return null;
    }
    ApprovalCollectVo result = nebulaToolkitService.copyObjectByWhiteList(approvalCollect, ApprovalCollectVo.class, HashSet.class, ArrayList.class);
    List<ApprovalCollectImageVo> imageVos = approvalCollectImageService.findByApprovalCollectCode(approvalCollect.getCode());
    result.setImages(imageVos);
    result.setTypeList(approvalCollectTypeRepository.findByCode(result.getCode()));
    return result;
  }

  /**
   * 根据编码查询信息
   */
  public ApprovalCollectVo findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    ApprovalCollect approvalCollect = approvalCollectRepository.findByCode(code);
    if (approvalCollect == null) {
      return null;
    }
    return nebulaToolkitService.copyObjectByWhiteList(approvalCollect, ApprovalCollectVo.class, HashSet.class, ArrayList.class);
  }

  /**
   * 根据编码查询详情
   */
  public ApprovalCollectVo findDetailsByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    ApprovalCollect approvalCollect = approvalCollectRepository.findByCode(code);
    if (approvalCollect == null) {
      return null;
    }
    ApprovalCollectVo result = nebulaToolkitService.copyObjectByWhiteList(approvalCollect, ApprovalCollectVo.class, HashSet.class, ArrayList.class);
    List<ApprovalCollectImageVo> imageVos = approvalCollectImageService.findByApprovalCollectCode(approvalCollect.getCode());
    result.setImages(imageVos);
    return result;
  }

  @Override
  public List<ApprovalCollectVo> findDetailsByCodes(Set<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return Lists.newArrayList();
    }
    List<ApprovalCollect> approvalCollects = approvalCollectRepository.findByCodes(codes);
    if (CollectionUtils.isEmpty(approvalCollects)) {
      return Lists.newArrayList();
    }

    Collection<ApprovalCollectVo> result = nebulaToolkitService.copyCollectionByWhiteList(approvalCollects, ApprovalCollect.class, ApprovalCollectVo.class, HashSet.class, ArrayList.class);
    for (ApprovalCollectVo approvalCollect : result) {
      List<ApprovalCollectImageVo> imageVos = approvalCollectImageService.findByApprovalCollectCode(approvalCollect.getCode());
      if (!CollectionUtils.isEmpty(imageVos)) {
        approvalCollect.setImages(imageVos);
      }
    }
    return Lists.newArrayList(result);
  }

  /**
   * 新增数据
   *
   * @param approvalCollect 实体对象
   * @return 新增结果
   */
  @Transactional
  public ApprovalCollectVo create(ApprovalCollectDto approvalCollect) {
    this.createValidate(approvalCollect);
    approvalCollect.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    if (StringUtils.isBlank(approvalCollect.getTenantCode())) {
      approvalCollect.setTenantCode(TenantUtils.getTenantCode());
    }
    approvalCollect.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    ApprovalCollect entity = nebulaToolkitService.copyObjectByWhiteList(approvalCollect, ApprovalCollect.class, HashSet.class, ArrayList.class, "images");
    entity.setTenantCode(TenantUtils.getTenantCode());
    approvalCollectRepository.save(entity);
    Collection<ApprovalCollectTypeEntity> typeEntities = nebulaToolkitService.copyCollectionByWhiteList(approvalCollect.getTypeList(), ApprovalCollectTypeDto.class, ApprovalCollectTypeEntity.class, LinkedHashSet.class, ArrayList.class);
    typeEntities.forEach(e -> {
      e.setCollectCode(approvalCollect.getCode());
      e.setTenantCode(TenantUtils.getTenantCode());
    });
    approvalCollectTypeRepository.saveBatch(typeEntities);

    //保存可能的图片信息
    if (!CollectionUtils.isEmpty(approvalCollect.getImages())) {
      approvalCollectImageService.create(approvalCollect, approvalCollect.getImages());
    }
    //新增业务日志
    ApprovalCollectLogEventDto logEventDto = new ApprovalCollectLogEventDto();
    logEventDto.setOriginal(null);
    logEventDto.setNewest(approvalCollect);
    SerializableBiConsumer<ApprovalCollectLogEventListener, ApprovalCollectLogEventDto> onCreate =
        ApprovalCollectLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(logEventDto, ApprovalCollectLogEventListener.class, onCreate);
    return nebulaToolkitService.copyObjectByWhiteList(entity, ApprovalCollectVo.class, HashSet.class, ArrayList.class, "images");
  }


  /**
   * 修改新据
   *
   * @param approvalCollect 实体对象
   * @return 修改结果
   */
  @Transactional
  public ApprovalCollectVo update(ApprovalCollectDto approvalCollect) {
    this.updateValidate(approvalCollect);
    ApprovalCollect dbApprovalCollect = approvalCollectRepository.findByIdAndTenantCode(approvalCollect.getId(),TenantUtils.getTenantCode());
    ApprovalCollectDto oldDto = this.nebulaToolkitService.copyObjectByWhiteList(dbApprovalCollect, ApprovalCollectDto.class, HashSet.class, ArrayList.class);
    Validate.notNull(dbApprovalCollect, "根据指定的id键值，未能获取到相应信息");
    ApprovalCollectVo oldApprovalCollect = nebulaToolkitService.copyObjectByWhiteList(dbApprovalCollect, ApprovalCollectVo.class, HashSet.class, ArrayList.class);
    dbApprovalCollect.setDescr(approvalCollect.getDescr());
    dbApprovalCollect.setName(approvalCollect.getName());
    dbApprovalCollect.setSortIndex(approvalCollect.getSortIndex());
    dbApprovalCollect.setTenantCode(TenantUtils.getTenantCode());
    this.approvalCollectRepository.saveOrUpdate(dbApprovalCollect);

    approvalCollectTypeRepository.deleteByCode(approvalCollect.getCode());
    Collection<ApprovalCollectTypeEntity> typeEntities = nebulaToolkitService.copyCollectionByWhiteList(approvalCollect.getTypeList(), ApprovalCollectTypeDto.class, ApprovalCollectTypeEntity.class, LinkedHashSet.class, ArrayList.class);
    typeEntities.forEach(e -> {
      e.setCollectCode(approvalCollect.getCode());
      e.setTenantCode(TenantUtils.getTenantCode());
    });
    approvalCollectTypeRepository.saveBatch(typeEntities);
    List<ApprovalCollectImageVo> imageVos = approvalCollectImageService.findByApprovalCollectCode(dbApprovalCollect.getCode());
    Set<String> currentIds = Sets.newHashSet();
    Set<String> dbIds = Sets.newHashSet();
    if (!CollectionUtils.isEmpty(approvalCollect.getImages())) {
      currentIds = approvalCollect.getImages().stream().filter(e -> StringUtils.isNotBlank(e.getId())).map(ApprovalCollectImageDto::getId).collect(Collectors.toSet());
    }
    if (!CollectionUtils.isEmpty(imageVos)) {
      dbIds = imageVos.stream().map(ApprovalCollectImageVo::getId).collect(Collectors.toSet());
    }

    //处理图片删除情况
    Set<String> needDeletes = Sets.difference(dbIds, currentIds);
    List<ApprovalCollectImageVo> oldImages = new ArrayList<>();
    if (!CollectionUtils.isEmpty(needDeletes)) {
      oldImages = approvalCollectImageService.findByIds(Lists.newArrayList(needDeletes));
      approvalCollectImageService.deleteByIds(needDeletes);
    }

    //处理图片新增情况
    List<ApprovalCollectImageDto> needAdds = approvalCollect.getImages().stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(needAdds)) {
      approvalCollectImageService.create(approvalCollect, needAdds);
    }

    //通知活动明细，如果已关联活动细类的，需要更新相关信息
    if (!CollectionUtils.isEmpty(approvalCollectEventListeners)) {
      for (ApprovalCollectEventListener listener : approvalCollectEventListeners) {
        ApprovalCollectVo newApprovalCollect = nebulaToolkitService.copyObjectByWhiteList(dbApprovalCollect, ApprovalCollectVo.class, HashSet.class, ArrayList.class);
        listener.onUpdate(oldApprovalCollect, newApprovalCollect);
      }
    }
    //编辑业务日志
    ApprovalCollectLogEventDto logEventDto = new ApprovalCollectLogEventDto();
    if (!CollectionUtils.isEmpty(oldImages)){
      List<ApprovalCollectImageDto> approvalCollectImageDtos =(List<ApprovalCollectImageDto>) this.nebulaToolkitService.copyCollectionByWhiteList(oldImages, ApprovalCollectImageVo.class, ApprovalCollectImageDto.class, HashSet.class, ArrayList.class);
      oldDto.setImages(approvalCollectImageDtos);
    }
    logEventDto.setOriginal(oldDto);
    ApprovalCollectDto newDto = this.nebulaToolkitService.copyObjectByWhiteList(dbApprovalCollect, ApprovalCollectDto.class, HashSet.class, ArrayList.class);
    newDto.setImages(needAdds);
    logEventDto.setNewest(newDto);
    SerializableBiConsumer<ApprovalCollectLogEventListener, ApprovalCollectLogEventDto> onUpdate =
        ApprovalCollectLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(logEventDto, ApprovalCollectLogEventListener.class, onUpdate);
    return this.findDetailsByCode(approvalCollect.getCode());
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   */
  @Transactional
  public void delete(Set<String> idList) {
    Validate.isTrue(!CollectionUtils.isEmpty(idList), "主键集合不能为空！");
    List<ApprovalCollectVo> approvalCollectVos = this.findByIds(idList);
    Validate.notEmpty(approvalCollectVos, "根据提供的主键集合信息，未能获取到相应数据");
    //通知活动明细，如果已关联活动细类的，不允许删除
    if (!CollectionUtils.isEmpty(approvalCollectEventListeners)) {
      for (ApprovalCollectEventListener listener : approvalCollectEventListeners) {
        for (ApprovalCollectVo approvalCollectVo : approvalCollectVos) {
          listener.onDeleted(approvalCollectVo);
        }
      }
    }
    //执行删除核销采集（主表逻辑删除，明细项信息不做变更）
    this.approvalCollectRepository.delete(idList);
    //删除业务日志
    Collection<ApprovalCollectDto> approvalCollectDtos = this.nebulaToolkitService.copyCollectionByWhiteList(approvalCollectVos, ApprovalCollectVo.class, ApprovalCollectDto.class, HashSet.class, ArrayList.class);
    SerializableBiConsumer<ApprovalCollectLogEventListener, ApprovalCollectLogEventDto> onDelete =
        ApprovalCollectLogEventListener::onDelete;
    for (ApprovalCollectDto approvalCollectDto : approvalCollectDtos) {
      ApprovalCollectLogEventDto logEventDto = new ApprovalCollectLogEventDto();
      logEventDto.setOriginal(approvalCollectDto);
      this.nebulaNetEventClient.publish(logEventDto, ApprovalCollectLogEventListener.class, onDelete);
    }
  }

  @Override
  @Transactional
  public void updateEnableStatus(Set<String> ids, String enableStatus) {
    Validate.notEmpty(ids, "主键id不能为空");
    Validate.notBlank(enableStatus, "启禁用状态不能为空");
    List<ApprovalCollectVo> approvalCollects = this.findByIds(ids);
    Validate.notEmpty(approvalCollects, "根据指定的主键集合信息，未能获取到相应数据");
    Validate.isTrue(EnableStatusEnum.contains(enableStatus), "未知的启禁用状态");
    approvalCollectRepository.updateEnableStatus(approvalCollects.stream().map(ApprovalCollectVo::getId).collect(Collectors.toSet()), enableStatus);
    //更新状态业务日志
    List<ApprovalCollectDto> approvalCollectDtos =(List<ApprovalCollectDto>) this.nebulaToolkitService.copyCollectionByWhiteList(approvalCollects, ApprovalCollectVo.class, ApprovalCollectDto.class, HashSet.class, ArrayList.class);
    SerializableBiConsumer<ApprovalCollectLogEventListener, ApprovalCollectLogEventDto> onUpdateEnable =
        ApprovalCollectLogEventListener::onUpdateEnable;
    for (ApprovalCollectDto approvalCollectDto : approvalCollectDtos) {
      ApprovalCollectLogEventDto logEventDto = new ApprovalCollectLogEventDto();
      logEventDto.setOriginal(approvalCollectDto);
      ApprovalCollectDto newDto = new ApprovalCollectDto();
      newDto.setEnableStatus(enableStatus);
      logEventDto.setNewest(newDto);
      this.nebulaNetEventClient.publish(logEventDto, ApprovalCollectLogEventListener.class, onUpdateEnable);
    }
  }

  /**
   * 创建验证
   */
  private void createValidate(ApprovalCollectDto approvalCollect) {
    Validate.notNull(approvalCollect, "对象信息不能为空！");
    Validate.isTrue(StringUtils.isBlank(approvalCollect.getId()), "主键id不能有值");
    approvalCollect.setId(null);
    Validate.notBlank(approvalCollect.getName(), "名称不能为空");
    Validate.notNull(approvalCollect.getSortIndex(), "排序值不能为空");
    Validate.notBlank(approvalCollect.getCode(), "编码不能为空");
    approvalCollect.setCode(StringUtils.upperCase(approvalCollect.getCode()));
    String pattern = "^[A-Z]{1}[A-Z0-9]*$";
    Validate.matchesPattern(approvalCollect.getCode(), pattern, "编码只能是字母和数字构成，且首字母不能是数字，最终编码都将被大写");
    ApprovalCollectVo dbApprovalCollect = this.findByCode(approvalCollect.getCode());
    Validate.isTrue(dbApprovalCollect == null, "编码重复，请检查");
    Validate.notEmpty(approvalCollect.getTypeList(), "类型不能为空");
  }

  /**
   * 修改验证
   */
  private void updateValidate(ApprovalCollectDto approvalCollect) {
    Validate.notNull(approvalCollect, "对象信息不能为空！");
    Validate.notBlank(approvalCollect.getId(), "主键id不能为空！");
    Validate.notBlank(approvalCollect.getName(), "名称不能为空");
    Validate.notNull(approvalCollect.getSortIndex(), "排序值不能为空");
    Validate.notEmpty(approvalCollect.getTypeList(), "类型不能为空");
  }
}

