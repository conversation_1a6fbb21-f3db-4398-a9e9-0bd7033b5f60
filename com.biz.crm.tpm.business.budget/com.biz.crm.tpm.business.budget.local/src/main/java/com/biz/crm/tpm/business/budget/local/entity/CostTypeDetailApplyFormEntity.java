package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 活动细类申请表单执行实体entity
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CostTypeDetailApplyFormEntity", description = "活动细类申请表单执行实体entity")
@Entity
@TableName("sfa_visit_conclusion")
@Table(name = "sfa_visit_conclusion")
@org.hibernate.annotations.Table(appliesTo = "sfa_visit_conclusion", comment = "拜访总结表")
public class CostTypeDetailApplyFormEntity extends TenantFlagOpEntity {

  /**
   * 站点编码
   */
  @Column(name = "client_code", length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '站点编码'")
  @ApiModelProperty("站点编码")
  private String clientCode;

  /**
   * 站点名称
   */
  @Column(name = "client_name", length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '站点名称'")
  @ApiModelProperty("站点名称")
  private String clientName;

  /**
   * 站点类别
   */
  @Column(name = "client_type", length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '站点类别'")
  @ApiModelProperty("站点类别")
  private String clientType;

  /**
   * 用户姓名
   */
  @Column(name = "user_name", columnDefinition = "VARCHAR(64) NOT NULL COMMENT '用户姓名'")
  @ApiModelProperty("用户姓名")
  private String userName;

  /**
   * 用户编码
   */
  @Column(name = "user_code", columnDefinition = "VARCHAR(64) NOT NULL COMMENT '用户编码'")
  @ApiModelProperty("用户编码")
  private String userCode;

  /**
   * 职位编码
   */
  @Column(name = "post_code", columnDefinition = "VARCHAR(64) NOT NULL COMMENT '职位编码'")
  @ApiModelProperty("职位编码")
  private String postCode;

  /**
   * 职位名称
   */
  @Column(name = "post_name", columnDefinition = "VARCHAR(64) NOT NULL COMMENT '职位名称'")
  @ApiModelProperty("职位名称")
  private String postName;

  /**
   * 拜访总结内容
   */
  @Column(name = "conclusion_content", columnDefinition = "VARCHAR(64) NOT NULL COMMENT '拜访总结内容'")
  @ApiModelProperty("拜访总结内容")
  private String conclusionContent;

  @ApiModelProperty("执行计划业务编码")
  @TableField(value = "parent_code")
  @Column(name = "parent_code", length = 64, columnDefinition = "varchar(64) COMMENT '执行计划业务编码'")
  private String parentCode;

  @ApiModelProperty("步骤业务编码stepCode")
  @TableField(value = "dynamic_key")
  @Column(name = "dynamic_key", length = 64, columnDefinition = "varchar(64) COMMENT '步骤业务编码stepCode'")
  private String dynamicKey;

  @ApiModelProperty("动态表单全局唯一编码formCode")
  @TableField(value = "dynamic_form_code")
  @Column(name = "dynamic_form_code", columnDefinition = "varchar(255) COMMENT '动态表单全局唯一编码formCode'")
  private String dynamicFormCode;
}
