package com.biz.crm.tpm.business.budget.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.budget.sdk.dto.StrategySettingManageDto;
import com.biz.crm.tpm.business.budget.sdk.service.StrategySettingManageVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;
import com.biz.crm.tpm.business.budget.sdk.vo.StrategySettingManageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 活动细类策略配置(StrategySettingManage)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
@RestController
@RequestMapping("/v1/budget/strategySettingManage")
@Slf4j
@Api(tags = "活动细类策略配置")
public class StrategySettingManageController {
  /**
   * 服务对象
   */
  @Autowired
  private StrategySettingManageVoService strategySettingManageVoService;

  /**
   * 分页查询所有数据
   *
   * @param pageable 分页对象
   * @param dto      查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<StrategySettingManageVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                                @ApiParam(name = "setting", value = "活动细类策略配置") StrategySettingManageDto dto) {
    try {
      Page<StrategySettingManageVo> page = this.strategySettingManageVoService.findByConditions(pageable, dto);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编码查询单条数据
   *
   * @param code 编码
   * @return 单条数据
   */
  @ApiOperation(value = "通过编码查询单条数据")
  @GetMapping("findByCode")
  public Result<StrategySettingManageVo> findByCode(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code) {
    try {
      StrategySettingManageVo vo = this.strategySettingManageVoService.findByCode(code);
      return Result.ok(vo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "查询正在使用的策略配置")
  @GetMapping("findEnabled")
  public Result<StrategySettingManageVo> findEnabled() {
    try {
      StrategySettingManageVo vo = this.strategySettingManageVoService.findEnabled();
      return Result.ok(vo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<?> create(@ApiParam(name = "setting", value = "活动细类策略配置") @RequestBody StrategySettingManageDto dto) {
    try {
      this.strategySettingManageVoService.create(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<?> update(@ApiParam(name = "setting", value = "活动细类策略配置") @RequestBody StrategySettingManageDto dto) {
    try {
      this.strategySettingManageVoService.update(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 使用
   */
  @ApiOperation(value = "使用")
  @PatchMapping("enableStatus")
  public Result<?> enableStatus(@RequestParam("id") @ApiParam(name = "id", value = "主键id") String id) {
    try {
      this.strategySettingManageVoService.enableStatus(id);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 禁用
   */
  @ApiOperation(value = "禁用")
  @PatchMapping("disabled")
  public Result<?> disabled(@RequestParam("ids")@ApiParam(name = "ids", value = "主键id集合") Set<String> ids) {
    try {
      this.strategySettingManageVoService.disabled(ids);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "通过策略项编码集合，获取策略结构信息")
  @GetMapping("structs")
  public Result<List<StrategySettingStruct>> structs(@RequestParam("codes") @ApiParam(name = "codes", value = "策略项编码集合") Set<String> codes) {
    try {
      List<StrategySettingStruct> structs = this.strategySettingManageVoService.structs(codes);
      return Result.ok(structs);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
