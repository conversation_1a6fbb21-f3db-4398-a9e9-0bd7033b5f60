package com.biz.crm.tpm.business.budget.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.activities.sdk.dto.WithholdingIncomeQueryDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetItemVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetRelationVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 费用预算(CostBudget)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:04
 */
@RestController
@RequestMapping("/v1/budget/costBudget")
@Slf4j
@Api(tags = "费用预算")
public class CostBudgetController {
    /**
     * 服务对象
     */
    @Autowired
    private CostBudgetVoService costBudgetVoService;
    @Autowired(required = false)
    private CostBudgetItemVoService costBudgetItemVoService;


    @ApiOperation(value = "分页查询所有数据")
    @GetMapping("findByConditions")
    public Result<Page<CostBudgetVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                       @ApiParam(name = "costBudget", value = "费用预算") CostBudgetDto dto) {
        try {
            Page<CostBudgetVo> page = this.costBudgetVoService.findByConditions(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过主键查询单条数据")
    @GetMapping("findById")
    public Result<CostBudgetVo> findById(@RequestParam("id") @ApiParam(name = "id", value = "主键id") String id) {
        try {
            CostBudgetVo costBudget = this.costBudgetVoService.findById(id);
            return Result.ok(costBudget);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过编码查询单条数据
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "通过编码查询单条数据")
    @GetMapping("findByCode")
    public Result<CostBudgetVo> findByCode(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code) {
        try {
            CostBudgetVo costBudget = this.costBudgetVoService.findByCode(code);
            return Result.ok(costBudget);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param costBudget 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @PostMapping
    public Result<CostBudgetVo> create(@ApiParam(name = "costBudget", value = "费用预算") @RequestBody CostBudgetDto costBudget) {
        try {
            CostBudgetVo result = this.costBudgetVoService.create(costBudget);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改数据
     *
     * @param costBudget 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @PatchMapping
    public Result<CostBudgetVo> update(@ApiParam(name = "costBudget", value = "费用预算") @RequestBody CostBudgetDto costBudget) {
        try {
            CostBudgetVo result = this.costBudgetVoService.update(costBudget);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除操作")
    @DeleteMapping
    public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") List<String> idList) {
        try {
            this.costBudgetVoService.delete(idList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 启禁用
     */
    @ApiOperation(value = "启禁用")
    @PatchMapping("updateEnableStatus")
    public Result<?> updateEnableStatus(@RequestBody @ApiParam(name = "ids", value = "主键id") Set<String> ids,
                                        @RequestParam("enableStatus") @ApiParam(name = "enableStatus", value = "启禁用状态") String enableStatus) {
        try {
            this.costBudgetVoService.updateEnableStatus(ids, enableStatus);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 调整操作
     */
    @ApiOperation(value = "调整操作")
    @PatchMapping("tranfer")
    public Result<?> tranfer(@RequestParam("costBudgetCodeOut") @ApiParam(name = "costBudgetCodeOut", value = "调出方费用预算编码") String costBudgetCodeOut,
                             @RequestParam("costBudgetCodeIn") @ApiParam(name = "costBudgetCodeIn", value = "调入方费用预算编码") String costBudgetCodeIn,
                             @RequestParam("operateAmount") @ApiParam(name = "operateAmount", value = "操作金额") BigDecimal operateAmount,
                             @RequestParam(name = "operateRemark", required = false) @ApiParam(name = "operateRemark", value = "操作备注") String operateRemark) {
        try {
            costBudgetVoService.tranfer(costBudgetCodeOut, costBudgetCodeIn, operateAmount, operateRemark);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 变更操作
     */
    @ApiOperation(value = "变更操作")
    @PatchMapping("change")
    public Result<?> change(@RequestParam("costBudgetCode") @ApiParam(name = "costBudgetCode", value = "费用预算编码") String costBudgetCode,
                            @RequestParam("operateAmount") @ApiParam(name = "operateAmount", value = "操作金额") BigDecimal operateAmount,
                            @RequestParam(name = "operateRemark", required = false) @ApiParam(name = "operateRemark", value = "操作备注") String operateRemark,
                            @RequestParam("operateType") @ApiParam(name = "operateType", value = "操作类型") String operateType) {
        try {
            costBudgetVoService.change(costBudgetCode, operateAmount, operateRemark, operateType);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 划拨操作
     */
    @ApiOperation(value = "划拨操作")
    @PatchMapping("wipe")
    public Result<?> wipe(@RequestParam("costBudgetCode") @ApiParam(name = "costBudgetCode", value = "划出方费用预算编码") String costBudgetCode,
                          @RequestBody @ApiParam(name = "costBudgetDtoIns", value = "划入方信息") List<CostBudgetDto> costBudgetDtoIns) {
        try {
            costBudgetVoService.wipe(costBudgetCode, costBudgetDtoIns);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 使用
     */
    @ApiOperation(value = "使用")
    @PatchMapping("occupy")
    public Result<?> occupy(@RequestParam("businessCode") @ApiParam(name = "businessCode", value = "业务编码") String businessCode,
                            @RequestParam("businessItemCode") @ApiParam(name = "businessItemCode", value = "业务明细编码") String businessItemCode,
                            @RequestParam("costBudgetCode") @ApiParam(name = "costBudgetCode", value = "费用预算编码") String costBudgetCode,
                            @RequestParam("operateAmount") @ApiParam(name = "operateAmount", value = "操作金额") BigDecimal operateAmount,
                            @RequestParam(name = "itemRemark", required = false) @ApiParam(name = "itemRemark", value = "备注") String itemRemark,
                            @RequestParam(name = "source") @ApiParam(name = "source", value = "业务来源") String source) {
        try {
            costBudgetVoService.occupy(businessCode, businessItemCode, costBudgetCode, operateAmount, itemRemark, source);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 回退
     */
    @ApiOperation(value = "回退")
    @PatchMapping("back")
    public Result<?> back(@RequestParam("businessCode") @ApiParam(name = "businessCode", value = "业务编码") String businessCode,
                          @RequestParam("businessItemCode") @ApiParam(name = "businessItemCode", value = "业务明细编码") String businessItemCode,
                          @RequestParam("costBudgetCode") @ApiParam(name = "costBudgetCode", value = "费用预算编码") String costBudgetCode,
                          @RequestParam("operateAmount") @ApiParam(name = "operateAmount", value = "操作金额") BigDecimal operateAmount,
                          @RequestParam(name = "itemRemark", required = false) @ApiParam(name = "itemRemark", value = "备注") String itemRemark,
                          @RequestParam(name = "source") @ApiParam(name = "source", value = "业务来源") String source) {
        try {
            costBudgetVoService.back(businessCode, businessItemCode, costBudgetCode, operateAmount, itemRemark, source);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据费用预算编号查询费用预算与活动大类，预算科目的关系
     */
    @ApiOperation(value = "根据费用预算编号查询费用预算与活动大类，预算科目的关系")
    @GetMapping("findRelationByCodes")
    public Result<?> findRelationByCodes(@RequestParam("codes") @ApiParam(name = "codes", value = "费用预算编码") Set<String> codes) {
        try {
            Set<CostBudgetRelationVo> costBudgetRelationVos = costBudgetVoService.findRelationByCodes(codes);
            return Result.ok(costBudgetRelationVos);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 根据费用预算编号确认
     */
    @ApiOperation(value = "根据费用预算编号确认")
    @GetMapping("confirm")
    public Result<?> confirm(@RequestParam("codeList") @ApiParam(name = "codeList", value = "费用预算编码") List<String> codeList) {
        try {
            costBudgetVoService.confirm(codeList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分页查询所有明细数据
     *
     * @param pageable 分页对象
     * @param dto      查询实体
     * @return 所有数据
     */
    @ApiOperation(value = "预算查看明细-分页查询所有数据")
    @GetMapping("findByConditionsItem")
    public Result<Page<CostBudgetItemVo>> findByConditionsItem(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                               @ApiParam(name = "costBudget", value = "费用预算") CostBudgetDto dto) {
        try {
            Page<CostBudgetItemVo> page = costBudgetItemVoService.findByConditionsItem(pageable, dto);
            return Result.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "查询列表-通过组织+年月列表")
    @PostMapping("findListByOrgCodesAndYearsList")
    public Result<Map<String, BigDecimal>> findListByOrgCodesAndYearsList(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(costBudgetVoService.findListByOrgCodesAndYearsList(dto.getOrgCodes(), dto.getYearsList()));
    }

    @ApiOperation(value = "查询未税-通过组织+年月列表")
    @PostMapping("findListByOrgCodesAndYearsListNoTax")
    public Result<Map<String, BigDecimal>> findListByOrgCodesAndYearsListNoTax(@RequestBody WithholdingIncomeQueryDto dto) {
        return Result.ok(costBudgetVoService.findListByOrgCodesAndYearsListNoTax(dto.getOrgCodes(), dto.getYearsList()));
    }
}
