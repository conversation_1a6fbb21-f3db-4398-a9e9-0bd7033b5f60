package com.biz.crm.tpm.business.budget.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.budget.sdk.dto.ApprovalCollectDto;
import com.biz.crm.tpm.business.budget.sdk.service.ApprovalCollectVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 核销采集信息(ApprovalCollect)表相关的http接口
 *
 * <AUTHOR>
 * @since 2022-05-17 12:53:38
 */
@RestController
@RequestMapping("/v1/budget/approvalCollect")
@Slf4j
@Api(tags = "核销采集信息")
public class ApprovalCollectController {
  /**
   * 服务对象
   */
  @Autowired
  private ApprovalCollectVoService approvalCollectVoService;

  /**
   * 分页查询所有数据
   *
   * @param pageable        分页对象
   * @param approvalCollect 查询实体
   * @return 所有数据
   */
  @ApiOperation(value = "分页查询所有数据")
  @GetMapping("findByConditions")
  public Result<Page<ApprovalCollectVo>> findByConditions(@ApiParam(name = "pageable", value = "分页对象") @PageableDefault(50) Pageable pageable,
                                                          @ApiParam(name = "approvalCollect", value = "核销采集信息") ApprovalCollectDto approvalCollect) {
    try {
      Page<ApprovalCollectVo> page = this.approvalCollectVoService.findByConditions(pageable, approvalCollect);
      return Result.ok(page);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询基础数据")
  @GetMapping("findById")
  public Result<ApprovalCollectVo> findById(@RequestParam("id") @ApiParam(name = "id", value = "主键id") String id) {
    try {
      ApprovalCollectVo approvalCollect = this.approvalCollectVoService.findById(id);
      return Result.ok(approvalCollect);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编码code查询单条数据
   *
   * @param code 编码
   * @return 单条数据
   */
  @ApiOperation(value = "通过编码code查询基本数据")
  @GetMapping("findByCode")
  public Result<ApprovalCollectVo> findByCode(@RequestParam("code") @ApiParam(name = "code", value = "编码code") String code) {
    try {
      ApprovalCollectVo approvalCollect = this.approvalCollectVoService.findByCode(code);
      return Result.ok(approvalCollect);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过主键查询详情
   *
   * @param id 主键
   * @return 单条数据
   */
  @ApiOperation(value = "通过主键查询详情数据")
  @GetMapping("findDetailsById")
  public Result<ApprovalCollectVo> findDetailsById(@RequestParam("id") @ApiParam(name = "id", value = "主键id") String id) {
    try {
      ApprovalCollectVo approvalCollect = this.approvalCollectVoService.findDetailsById(id);
      return Result.ok(approvalCollect);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编码查询详情
   *
   * @param code 编码
   * @return 单条数据
   */
  @ApiOperation(value = "通过编码code查询详情数据")
  @GetMapping("findDetailsByCode")
  public Result<ApprovalCollectVo> findDetailsByCode(@RequestParam("code") @ApiParam(name = "code", value = "编码code") String code) {
    try {
      ApprovalCollectVo approvalCollect = this.approvalCollectVoService.findDetailsByCode(code);
      return Result.ok(approvalCollect);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过编码集查询详情
   * @param codes 编码集
   */
  @ApiOperation(value = "通过编码code查询详情数据")
  @GetMapping("findDetailsByCodes")
  public Result<List<ApprovalCollectVo>> findDetailsByCodes(@RequestParam("codes") @ApiParam(name = "codes", value = "编码code") Set<String> codes) {
    try {
      List<ApprovalCollectVo> result = this.approvalCollectVoService.findDetailsByCodes(codes);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 新增数据
   *
   * @param approvalCollect 实体对象
   * @return 新增结果
   */
  @ApiOperation(value = "新增数据")
  @PostMapping
  public Result<ApprovalCollectVo> create(@ApiParam(name = "approvalCollect", value = "核销采集信息") @RequestBody ApprovalCollectDto approvalCollect) {
    try {
      ApprovalCollectVo result = this.approvalCollectVoService.create(approvalCollect);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 修改数据
   *
   * @param approvalCollect 实体对象
   * @return 修改结果
   */
  @ApiOperation(value = "修改数据")
  @PatchMapping
  public Result<ApprovalCollectVo> update(@ApiParam(name = "approvalCollect", value = "核销采集信息") @RequestBody ApprovalCollectDto approvalCollect) {
    try {
      ApprovalCollectVo result = this.approvalCollectVoService.update(approvalCollect);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @ApiOperation(value = "逻辑删除数据")
  @DeleteMapping
  public Result<?> delete(@ApiParam(name = "idList", value = "主键集合") @RequestParam("idList") Set<String> idList) {
    try {
      this.approvalCollectVoService.delete(idList);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 启禁用
   */
  @ApiOperation(value = "启禁用")
  @PatchMapping("updateEnableStatus")
  public Result<?> updateEnableStatus(@RequestParam("enableStatus") @ApiParam(name = "enableStatus", value = "启禁用状态") String enableStatus,
                                      @ApiParam(name = "ids", value = "主键集合") @RequestBody Set<String> ids) {
    try {
      this.approvalCollectVoService.updateEnableStatus(ids,enableStatus);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
