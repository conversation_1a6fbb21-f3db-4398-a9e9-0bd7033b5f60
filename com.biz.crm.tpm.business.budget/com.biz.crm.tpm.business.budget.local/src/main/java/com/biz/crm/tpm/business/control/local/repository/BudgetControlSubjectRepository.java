package com.biz.crm.tpm.business.control.local.repository;



import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlSubjectVo;
import com.biz.crm.tpm.business.control.local.entity.BudgetControlSubject;
import com.biz.crm.tpm.business.control.local.mapper.BudgetControlSubjectMapper;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;


/**
 * 预算管控科目(BudgetControlSubject)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-22 16:19:54
 */
@Component
public class BudgetControlSubjectRepository extends ServiceImpl<BudgetControlSubjectMapper, BudgetControlSubject> {
    
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 根据管控编码删除
     *
     * @param code
     */
    public void deleteByCode(String code) {
        this.lambdaUpdate().eq(BudgetControlSubject::getControlCode, code)
                .remove();
    }

    /**
     * 根据管控编码查询
     *
     * @param code
     */
    public List<BudgetControlSubjectVo> findByCode(String code) {
        List<BudgetControlSubject> list = this.lambdaQuery().eq(BudgetControlSubject::getControlCode, code)
                .list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, BudgetControlSubject.class, BudgetControlSubjectVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 根据管控编码查询
     *
     * @param codeSet
     */
    public List<BudgetControlSubjectVo> findByCodeSet(Set<String> codeSet) {
        List<BudgetControlSubject> list = this.lambdaQuery().in(BudgetControlSubject::getControlCode, codeSet)
                .list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, BudgetControlSubject.class, BudgetControlSubjectVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }
}

