<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.budget.local.mapper.CostBudgetMapper">
    <resultMap id="costBudgetVoMap" type="com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo"/>

    <select id="findByConditions" resultMap="costBudgetVoMap">
        select
        t.*
        from tpm_cost_budget t
        where t.tenant_code=#{dto.tenantCode}
        <if test="dto.code !=null and dto.code != '' ">
            and t.code = #{dto.code}
        </if>
        <if test="dto.yearMonthLy !=null and dto.yearMonthLy != '' ">
            and t.year_month_ly = #{dto.yearMonthLy}
        </if>
        <if test="dto.type !=null and dto.type != '' ">
            and t.type = #{dto.type}
        </if>
        <if test="dto.customerName !=null and dto.customerName != '' ">
            and t.customer_name like concat('%',#{dto.customerName},'%')
        </if>
        <if test="dto.budgetSubjectCode !=null and dto.budgetSubjectCode != '' ">
            and t.budget_subject_code = #{dto.budgetSubjectCode}
        </if>
        <if test="dto.budgetSubjectName !=null and dto.budgetSubjectName != '' ">
            and t.budget_subject_name like concat('%',#{dto.budgetSubjectName},'%')
        </if>
        <if test="dto.productCode !=null and dto.productCode != '' ">
            and t.product_code like concat('%',#{dto.productCode},'%')
        </if>
        <if test="dto.productName !=null and dto.productName != '' ">
            and t.product_name like concat('%',#{dto.productName},'%')
        </if>
        <if test="dto.productLevelCode !=null and dto.productLevelCode != '' ">
            and t.product_level_code like concat('%',#{dto.productLevelCode},'%')
        </if>
        <if test="dto.productLevelName !=null and dto.productLevelName != '' ">
            and t.product_level_name like concat('%',#{dto.productLevelName},'%')
        </if>
        <if test="dto.itemName !=null and dto.itemName != '' ">
            and t.item_name like concat('%',#{dto.itemName},'%')
        </if>
        <if test="dto.terminalName !=null and dto.terminalName != '' ">
            and t.terminal_name like concat('%',#{dto.terminalName},'%')
        </if>
        <if test="dto.orgName !=null and dto.orgName != '' ">
            and t.org_name like concat('%',#{dto.orgName},'%')
        </if>
        <if test="dto.channelName !=null and dto.channelName != '' ">
            and t.channel_name like concat('%',#{dto.channelName},'%')
        </if>
        <if test="dto.delFlag !=null and dto.delFlag != '' ">
            and t.del_flag = #{dto.delFlag}
        </if>
        <if test="dto.levelNum !=null ">
            and t.level_num = #{dto.levelNum}
        </if>
        <if test="dto.enableStatus !=null and dto.enableStatus != '' ">
            and t.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.confirmStatus !=null and dto.confirmStatus != '' ">
            and t.confirm_status = #{dto.confirmStatus}
        </if>
        <if test="dto.companyCode != null and dto.companyCode != ''">
            <bind name="companyCode" value="'%' + dto.companyCode + '%'"/>
            and t.company_code like #{companyCode}
        </if>
        <if test="dto.departmentOneCode != null and dto.departmentOneCode != ''">
            <bind name="departmentOneCode" value="'%' + dto.departmentOneCode + '%'"/>
            and t.department_one_code like #{departmentOneCode}
        </if>
        <if test="dto.departmentOneName != null and dto.departmentOneName != ''">
            <bind name="departmentOneName" value="'%' + dto.departmentOneName + '%'"/>
            and t.department_one_name like #{departmentOneName}
        </if>
        <if test="dto.costCenterCode != null and dto.costCenterCode != ''">
            <bind name="costCenterCode" value="'%' + dto.costCenterCode + '%'"/>
            and t.cost_center_code like #{costCenterCode}
        </if>
        <if test="dto.costCenterName != null and dto.costCenterName != ''">
            <bind name="costCenterName" value="'%' + dto.costCenterName + '%'"/>
            and t.cost_center_name like #{costCenterName}
        </if>
        <if test="dto.customerCode !=null and dto.customerCode != '' ">
            <bind name="customerCode" value="'%' + dto.customerCode + '%'"/>
            and t.customer_code like #{customerCode}
        </if>
        <if test="dto.customerName !=null and dto.customerName != '' ">
            <bind name="customerName" value="'%' + dto.customerName + '%'"/>
            and t.customer_name like #{customerName}
        </if>
        <if test="dto.deptCodeSet != null and dto.deptCodeSet.size() > 0">
            and t.department_one_code in
            <foreach item="item" collection="dto.deptCodeSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        order by t.create_time desc, t.id desc
    </select>

    <select id="findBudgetCodeByDto" resultType="com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo">
        select
        t.*
        from tpm_cost_budget t
        where t.tenant_code=#{dto.tenantCode}
        and t.del_flag = '${@<EMAIL>()}'
        and t.enable_status = '${@<EMAIL>()}'
        and t.confirm_status = 'confirmed'
        <if test="dto.yearMonthLy !=null and dto.yearMonthLy != '' ">
            and t.year_month_ly = #{dto.yearMonthLy}
        </if>
        <if test="dto.departmentOneCode !=null and dto.departmentOneCode != '' ">
            and t.department_one_code = #{dto.departmentOneCode}
        </if>
        <if test="dto.departmentOneName !=null and dto.departmentOneName != '' ">
            and t.department_one_name = #{dto.departmentOneName}
        </if>
        <if test="dto.levelNum !=null ">
            and t.level_num = #{dto.levelNum}
        </if>
        <if test="dto.costCenterCode !=null and dto.costCenterCode != '' ">
            and t.cost_center_code = #{dto.costCenterCode}
        </if>
        <if test="dto.costCenterName !=null and dto.costCenterName != '' ">
            and t.cost_center_name = #{dto.costCenterName}
        </if>
        <if test="dto.budgetSubjectCode !=null and dto.budgetSubjectCode != '' ">
            and t.budget_subject_code = #{dto.budgetSubjectCode}
        </if>
        <if test="dto.budgetSubjectName !=null and dto.budgetSubjectName != '' ">
            and t.budget_subject_name = #{dto.budgetSubjectName}
        </if>
        <if test="dto.customerCode !=null and dto.customerCode != '' ">
            and t.customer_code = #{dto.customerCode}
        </if>
        <if test="dto.productLevelCode !=null and dto.productLevelCode != '' ">
            and t.product_level_code = #{dto.productLevelCode}
        </if>
        <if test="dto.productLevelName !=null and dto.productLevelName != '' ">
            and t.product_level_name = #{dto.productLevelName}
        </if>
        <if test="dto.productCode !=null and dto.productCode != '' ">
            and t.product_code = #{dto.productCode}
        </if>
        <if test="dto.companyCode !=null and dto.companyCode != '' ">
            and t.company_code = #{dto.companyCode}
        </if>
    </select>

    <select id="findByDto" resultType="com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo">
        select
        t.*
        from tpm_cost_budget t
        where t.tenant_code=#{dto.tenantCode}
        and t.del_flag = '${@<EMAIL>()}'
        and t.enable_status = '${@<EMAIL>()}'
        and t.confirm_status = 'confirmed'
        <if test="dto.cusCodeSet != null and dto.cusCodeSet.size > 0">
            and t.customer_code in
            <foreach item="item" collection="dto.cusCodeSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.headDeptCodeSet != null and dto.headDeptCodeSet.size > 0">
            and t.department_one_code in
            <foreach item="item" collection="dto.headDeptCodeSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.cusCodeExcludeSet != null and dto.cusCodeExcludeSet.size > 0">
            and t.customer_code not in
            <foreach item="item" collection="dto.cusCodeExcludeSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.deptCodeExcludeSet != null and dto.deptCodeExcludeSet.size > 0">
            and t.department_one_code not in
            <foreach item="item" collection="dto.deptCodeExcludeSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.deptCodeSet != null and dto.deptCodeSet.size > 0">
            and t.department_one_code in
            <foreach item="item" collection="dto.deptCodeSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.subjectCodeSet != null and dto.subjectCodeSet.size > 0">
            and t.budget_subject_code in
            <foreach item="item" collection="dto.subjectCodeSet" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.yearMonthList != null and dto.yearMonthList.size > 0">
            and t.year_month_ly in
            <foreach item="item" collection="dto.yearMonthList" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.hasCus.equals('Y')">
            and t.customer_code is not null
        </if>
        <if test="dto.hasItem.equals('Y')">
            and t.product_level_code is not null
        </if>
        <if test="dto.hasProduct.equals('Y')">
            and t.product_code is not null
        </if>
    </select>

    <select id="findCodeByConditions" resultType="java.lang.String">
        SELECT code from tpm_cost_budget
        where
        del_flag = '${@<EMAIL>()}'
        and enable_status = '${@<EMAIL>()}'
        and tenant_code = #{tenantCode}
        and confirm_status = 'confirmed'
        and (year_month_ly = #{dto.yearMonthLy} or year_month_ly is null or year_month_ly = '')
        and (company_code = #{dto.companyCode} or company_code is null or company_code = '')
        and (customer_code = #{dto.customerCode} or customer_code is null or customer_code = '')
        and (cost_center_code = #{dto.costCenterCode} or cost_center_code is null or cost_center_code = '')
        and (department_one_code = #{dto.departmentOneCode} or department_one_code is null or department_one_code = '')
        and (budget_subject_code = #{dto.budgetSubjectCode} or budget_subject_code is null or budget_subject_code = '')
        <if test="dto.itemCodes != null and dto.itemCodes.size()>0">
            and (item_code in
            <foreach collection="dto.itemCodes" open="(" close=")" item="item" separator="," index="index">
                #{item}
            </foreach>
            or item_code is null)
        </if>
        <if test="dto.productCodes != null and dto.productCodes.size()>0">
            and (product_code in
            <foreach collection="dto.productCodes" index="index" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
            or product_code is null)
        </if>
        ORDER BY
        (CASE WHEN year_month_ly = #{dto.yearMonthLy} THEN 1 ELSE 0 END) +
        (CASE WHEN company_code = #{dto.companyCode} THEN 1 ELSE 0 END) +
        (CASE WHEN customer_code = #{dto.customerCode} THEN 1 ELSE 0 END) +
        (CASE WHEN cost_center_code = #{dto.costCenterCode} THEN 1 ELSE 0 END) +
        (CASE WHEN department_one_code = #{dto.departmentOneCode} THEN 1 ELSE 0 END) +
        (CASE WHEN budget_subject_code = #{dto.budgetSubjectCode} THEN 1 ELSE 0 END)
        <if test="dto.itemCodes != null and dto.itemCodes.size()>0">
            + (CASE WHEN item_code in
            <foreach collection="dto.itemCodes" open="(" close=")" item="item" separator="," index="index">
                #{item}
            </foreach>
            THEN 1 ELSE 0 END)
        </if>
        <if test="dto.productCodes != null and dto.productCodes.size()>0">
            + (CASE WHEN product_code in
            <foreach collection="dto.productCodes" index="index" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
            THEN 1 ELSE 0 END)
        </if>
        DESC
        LIMIT 1
    </select>

    <select id="findListByOrgCodeAndCategoryCodesAndYears" resultType="com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo">
        SELECT
        a.*,b.category_code
        FROM
        tpm_cost_budget a
        LEFT JOIN tpm_cost_type_category b ON a.budget_subject_code = b.budget_subjects_code
        WHERE
        a.tenant_code = #{tenantCode}
        and a.confirm_status = 'confirmed'
        and a.department_one_code in
        <foreach collection="orgCodes" open="(" separator="," item="item" index="index" close=")">
            #{item}
        </foreach>
        AND a.year_month_ly = #{years}
        AND b.category_code in
        <foreach collection="categoryCodes" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>

