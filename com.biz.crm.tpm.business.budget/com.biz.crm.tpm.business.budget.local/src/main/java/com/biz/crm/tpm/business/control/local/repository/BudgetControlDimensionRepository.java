package com.biz.crm.tpm.business.control.local.repository;



import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlDimensionVo;
import com.biz.crm.tpm.business.control.local.entity.BudgetControlDimension;
import com.biz.crm.tpm.business.control.local.mapper.BudgetControlDimensionMapper;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;


/**
 * (BudgetControlDimension)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-22 16:19:54
 */
@Component
public class BudgetControlDimensionRepository extends ServiceImpl<BudgetControlDimensionMapper, BudgetControlDimension> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 根据管控编码删除
     *
     * @param code
     */
    public void deleteByCode(String code) {
        this.lambdaUpdate().eq(BudgetControlDimension::getControlCode, code)
                .remove();
    }

    /**
     * 根据管控编码查询
     *
     * @param code
     */
    public List<BudgetControlDimensionVo> findByCode(String code) {
        List<BudgetControlDimension> list = this.lambdaQuery().eq(BudgetControlDimension::getControlCode, code)
                .list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, BudgetControlDimension.class, BudgetControlDimensionVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 根据管控编码查询
     *
     * @param codeSet
     */
    public List<BudgetControlDimensionVo> findByCodeSet(Set<String> codeSet) {
        List<BudgetControlDimension> list = this.lambdaQuery().in(BudgetControlDimension::getControlCode, codeSet)
                .list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, BudgetControlDimension.class, BudgetControlDimensionVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }
}

