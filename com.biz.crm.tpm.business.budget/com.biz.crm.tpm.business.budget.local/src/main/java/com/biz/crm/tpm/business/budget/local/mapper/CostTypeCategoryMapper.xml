<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.tpm.business.budget.local.mapper.CostTypeCategoryMapper">

    <select id="findByConditions" resultType="com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo">
        select distinct t.*
        from tpm_cost_type_category t left join tpm_cost_type_category_range tsr  on t.tenant_code = tsr.tenant_code and t.category_code = tsr.category_code
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != ''">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.enableStatus != null and dto.enableStatus != ''">
                and t.enable_status = #{dto.enableStatus}
            </if>
            <if test="dto.categoryCode != null and dto.categoryCode != ''">
                <bind name="categoryCode" value="'%' + dto.categoryCode + '%'"/>
                and t.category_code like #{categoryCode}
            </if>
            <if test="dto.categoryName != null and dto.categoryName != ''">
                <bind name="categoryName" value="'%' + dto.categoryName + '%'"/>
                and t.category_name like #{categoryName}
            </if>
            <if test="dto.budgetSubjectsCode != null and dto.budgetSubjectsCode != ''">
                and t.budget_subjects_code = #{dto.budgetSubjectsCode}
            </if>
            <if test="dto.budgetSubjectsName != null and dto.budgetSubjectsName != ''">
                <bind name="budgetSubjectsName" value="'%' + dto.budgetSubjectsName + '%'"/>
                and t.budget_subjects_name like #{budgetSubjectsName}
            </if>
            <if test="dto.orgTypes != null and dto.orgTypes.size > 0 and dto.orgCodes != null and dto.orgCodes.size > 0">
                and ((tsr.range_type = 2
                and tsr.range_code in
                <foreach collection="dto.orgTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                ) or
                ( tsr.range_type = 1
                and tsr.range_code in
                <foreach collection="dto.orgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                ))
            </if>
            <if test="dto.orgTypes != null and dto.orgTypes.size > 0 and (dto.orgCodes == null || dto.orgCodes.size == 0)">
                and tsr.range_type = 2
                and tsr.range_code in
                <foreach collection="dto.orgTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orgCodes != null and dto.orgCodes.size > 0 and (dto.orgTypes == null || dto.orgTypes.size == 0)">
                and tsr.range_type = 1
                and tsr.range_code in
                <foreach collection="dto.orgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.budgetSubjectsCodeSet != null and dto.budgetSubjectsCodeSet.size > 0">
                and t.budget_subjects_code in
                <foreach collection="dto.budgetSubjectsCodeSet" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
        order by
        <if test="dto.selectedCodeList != null and dto.selectedCodeList.size > 0">
            CASE
            <foreach collection="dto.selectedCodeList" item="item" index="index">
                WHEN t.category_code = #{item} THEN ${index}
            </foreach>
            ELSE 99 END asc,
        </if>
        t.create_time desc, t.id
    </select>


    <select id="findListReleaseSecondBudgetSubjectCodes" resultType="com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo">
        SELECT a.category_code,
               a.category_name,
               c.budget_subjects_code,
               c.budget_subjects_name
        FROM tpm_cost_type_category a
                 inner JOIN tpm_budget_subjects b ON a.budget_subjects_code = b.budget_subjects_code
                 inner JOIN tpm_budget_subjects c ON c.budget_subjects_code = b.parent_budget_subjects_code
        WHERE c.enable_status = '${@<EMAIL>()}'
          AND c.del_flag = '${@<EMAIL>()}'
          AND c.tenant_code = #{tenantCode}
          AND c.LEVEL = '2'
          and a.del_flag = '${@<EMAIL>()}'
          and a.enable_status = '${@<EMAIL>()}'
    </select>

</mapper>