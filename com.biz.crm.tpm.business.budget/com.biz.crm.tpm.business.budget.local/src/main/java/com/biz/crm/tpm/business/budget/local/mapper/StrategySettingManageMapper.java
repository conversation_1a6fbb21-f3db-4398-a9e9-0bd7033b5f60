package com.biz.crm.tpm.business.budget.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.local.entity.StrategySettingManage;
import com.biz.crm.tpm.business.budget.sdk.dto.StrategySettingManageDto;
import com.biz.crm.tpm.business.budget.sdk.vo.StrategySettingManageVo;
import org.apache.ibatis.annotations.Param;

/**
 * 活动细类策略配置(StrategySettingManage)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Deprecated
public interface StrategySettingManageMapper extends BaseMapper<StrategySettingManage> {

  /**
   * 分页查询所有数据
   * @param page       分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  Page<StrategySettingManageVo> findByConditions(@Param("page") Page<StrategySettingManageVo> page, @Param("dto") StrategySettingManageDto dto);

}

