package com.biz.crm.tpm.business.track.local.repository;



import com.biz.crm.tpm.business.track.local.entity.BudgetActual;
import com.biz.crm.tpm.business.track.local.mapper.BudgetActualMapper;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;


/**
 * 预算实际(BudgetActual)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-31 21:28:44
 */
@Component
public class BudgetActualRepository extends ServiceImpl<BudgetActualMapper, BudgetActual> {

  @Autowired
  private BudgetActualMapper budgetActualMapper;
  
}

