package com.biz.crm.tpm.business.adjust.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDto;
import com.biz.crm.tpm.business.adjust.sdk.service.BudgetAdjustOaService;
import com.biz.crm.tpm.business.adjust.sdk.service.BudgetAdjustService;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/budget/budgetAdjust")
@Slf4j
@Api(tags = "预算调整")
public class BudgetAdjustController {

    @Autowired(required = false)
    private BudgetAdjustService budgetAdjustService;
    @Autowired(required = false)
    private BudgetAdjustOaService budgetAdjustOaService;

    /**
     * 通过编码查询单条数据
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "通过编码查询单条数据")
    @GetMapping("findByCode")
    public Result<BudgetAdjustVo> findByCode(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code) {
        try {
            BudgetAdjustVo vo = this.budgetAdjustService.findByCode(code);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 流程撤回
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "流程撤回")
    @GetMapping("recover")
    public Result<?> recover(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code,
                             @RequestParam(value = "remark", required = false) @ApiParam(name = "remark", value = "备注") String remark) {
        try {
            this.budgetAdjustService.recover(code, remark);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 调整
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "调整")
    @PostMapping("adjust")
    public Result<?> adjust(@ApiParam(name = "dto", value = "调整信息") @RequestBody BudgetAdjustDto dto) {
        try {
            this.budgetAdjustService.adjust(dto, false);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 变更
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "变更")
    @PostMapping("change")
    public Result<?> change(@ApiParam(name = "dto", value = "调整信息") @RequestBody BudgetAdjustDto dto) {
        try {
            this.budgetAdjustService.change(dto, false);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param dto 实体对象
     * @return
     */
    @ApiOperation(value = "编辑数据")
    @PatchMapping
    public Result<?> update(@ApiParam(name = "dto", value = "调整信息") @RequestBody BudgetAdjustDto dto) {
        try {
            this.budgetAdjustService.update(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 推送OA
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "推送OA")
    @GetMapping("pushOa")
    public Result<?> pushOa(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code) {
        try {
            this.budgetAdjustOaService.pushOa(code, true);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 提交审批
     *
     * @param idList
     * @return
     */
    @ApiOperation(value = "提交审批")
    @PostMapping("submit")
    public Result<?> submit(@RequestBody List<String> idList) {
        try {
            this.budgetAdjustService.submit(idList);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据")
    @PostMapping
    public Result<?> delete(@ApiParam(name = "ids", value = "主键集合") @RequestBody List<String> ids) {
        try {
            this.budgetAdjustService.delete(ids);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
