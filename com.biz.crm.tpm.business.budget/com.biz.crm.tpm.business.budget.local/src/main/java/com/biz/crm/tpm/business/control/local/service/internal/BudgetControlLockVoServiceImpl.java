package com.biz.crm.tpm.business.control.local.service.internal;

import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.tpm.business.budget.sdk.constant.CostBudgetConstant;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlLockVoService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class BudgetControlLockVoServiceImpl implements BudgetControlLockVoService {

    @Autowired(required = false)
    private RedisLockService redisLockService;

    /**
     * 根据管控编码加锁
     *
     * @param controlCode
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 21:03
     **/
    @Override
    public boolean lock(String controlCode, TimeUnit timeUnit, int time) {
        if (StringUtils.isEmpty(controlCode)) {
            throw new RuntimeException("管控加锁失败，管控编码不能为空");
        }
        if (ObjectUtils.isEmpty(timeUnit)) {
            timeUnit = TimeUnit.MILLISECONDS;
        }
        if (time <= 0) {
            time = CostBudgetConstant.DEFAULT_LOCK_TIME;
        }
        return this.redisLockService.tryLock(CostBudgetConstant.BUDGET_CONTROL_LOCK + controlCode, timeUnit, time);
    }

    /**
     * 根据管控编码批量加锁
     *
     * @param controlCodeList
     * @param timeUnit
     * @param time
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public boolean lock(List<String> controlCodeList, TimeUnit timeUnit, int time) {
        return this.lock(controlCodeList, timeUnit, time, CostBudgetConstant.DEFAULT_WAITE_TIME_FIVE);
    }


    /**
     * 根据管控编码批量加锁
     *
     * @param controlCodeList
     * @param timeUnit
     * @param lockTime
     * @param waiteTime
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public boolean lock(List<String> controlCodeList, TimeUnit timeUnit, int lockTime, int waiteTime) {
        if (CollectionUtils.isEmpty(controlCodeList)) {
            throw new RuntimeException("管控加锁失败，管控编码不能为空");
        }
        if (ObjectUtils.isEmpty(timeUnit)) {
            timeUnit = TimeUnit.MILLISECONDS;
        }
        if (lockTime <= 0) {
            lockTime = CostBudgetConstant.DEFAULT_LOCK_TIME;
        }
        if (waiteTime <= 0) {
            waiteTime = CostBudgetConstant.DEFAULT_WAITE_TIME_FIVE;
        }

        boolean isLock = true;
        List<String> successKeys = new ArrayList<>();
        try {
            // 循环加锁，并记录成功的key
            for (String controlCode : controlCodeList) {
                isLock = this.redisLockService.tryLock(CostBudgetConstant.BUDGET_CONTROL_LOCK + controlCode, timeUnit, lockTime, waiteTime);
                if (!isLock) {
                    return false;
                }
                successKeys.add(controlCode);
            }
        } finally {
            // 存在加锁失败的情况，则先将成功的解锁
            if (!isLock && !CollectionUtils.isEmpty(successKeys)) {
                successKeys.forEach(key -> {
                    redisLockService.unlock(CostBudgetConstant.BUDGET_CONTROL_LOCK + key);
                });
            }
        }
        return true;
    }

    /**
     * 根据管控编码解锁
     *
     * @param controlCode
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public void unLock(String controlCode) {
        if (StringUtils.isEmpty(controlCode)) {
            throw new RuntimeException("管控解锁失败，管控编码不能为空");
        }
        redisLockService.unlock(CostBudgetConstant.BUDGET_CONTROL_LOCK + controlCode);
    }

    /**
     * 根据管控编码批量解锁
     *
     * @param controlCodeList
     * <AUTHOR>
     * @date 2022/11/1 21:04
     **/
    @Override
    public void unLock(List<String> controlCodeList) {
        if (CollectionUtils.isEmpty(controlCodeList)) {
            throw new RuntimeException("管控解锁失败，管控编码不能为空");
        }
        controlCodeList.forEach(controlCode -> {
            redisLockService.unlock(CostBudgetConstant.BUDGET_CONTROL_LOCK + controlCode);
        });
    }
}
