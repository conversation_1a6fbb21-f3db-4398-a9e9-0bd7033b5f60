package com.biz.crm.tpm.business.budget.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeDetail;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeDetailsDto;

import java.util.List;
import java.util.Set;

/**
 * TPM-活动明细;(tpm_cost_type_details)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@Mapper
@Deprecated
public interface CostTypeDetailMapper extends BaseMapper<CostTypeDetail> {
    /**
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto  动态查询条件
     * @return 分页对象列表
     */
    Page<CostTypeDetailVo> findByConditions(@Param("page") Page<CostTypeDetailVo> page, @Param("dto") CostTypeDetailsDto dto);

    /**
     * 根据条件查询活动细类编号
     *
     * @param dto
     * @return
     */
    Set<String> findCodeByCondition(@Param("dto") CostTypeDetailsDto dto);

    /**
     * 通过条件查询活动细类编码
     * @param dto
     * @return
     */
    List<String> findListByCondition(@Param("dto") CostTypeDetailsDto dto);
}