package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeDetailSettingStrategy;
import com.biz.crm.tpm.business.budget.local.mapper.CostTypeDetailSettingStrategyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 对象实体无租户编号tenant_code字段
 */
@Component
@Deprecated
public class CostTypeDetailSettingStrategyRepository extends ServiceImpl<CostTypeDetailSettingStrategyMapper, CostTypeDetailSettingStrategy> {

  public List<CostTypeDetailSettingStrategy> findByDetailCode(String detailCode){
    return this.lambdaQuery().eq(CostTypeDetailSettingStrategy::getDetailCode,detailCode).list();
  }

  public List<CostTypeDetailSettingStrategy> findByDetailCodes(Set<String> detailCodes){
    return this.lambdaQuery().in(CostTypeDetailSettingStrategy::getDetailCode,detailCodes).list();
  }

  public void deleteByDetailCodes(Set<String> detailCodes){
    this.lambdaUpdate().in(CostTypeDetailSettingStrategy::getDetailCode,detailCodes).remove();
  }

  public Integer countByApprovalCollect(String code){
    return this.lambdaQuery().like(CostTypeDetailSettingStrategy::getValue,code).count();
  }

  public boolean existBySettingManageCode(String settingManageCode){
    return this.lambdaQuery().eq(CostTypeDetailSettingStrategy::getSettingManageCode,settingManageCode).count() > 0;
  }
}