package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;

 /**
 * 实体：TPM-活动大类与活动细类关联;
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@ApiModel(value = "CostTypeMapping",description = "TPM-活动大类与活动细类关联")
@TableName("tpm_cost_type_mapping")
@Getter
@Setter
@Entity(name = "tpm_cost_type_mapping")
@org.hibernate.annotations.Table(appliesTo = "tpm_cost_type_mapping", comment = "TPM-活动大类与活动细类关联")
@Deprecated
public class CostTypeMapping  extends TenantEntity {
  
  /** 活动大类编号 */
  @ApiModelProperty(name = "活动大类编号",notes = "")
  @Column(name = "category_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动大类编号 '")
  private String categoryCode;
  
  /** 活动大类名称 */
  @ApiModelProperty(name = "活动大类名称",notes = "")
  @Column(name = "category_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '活动大类名称 '")
  private String categoryName;
  
  /** 活动细类编号 */
  @ApiModelProperty(name = "活动细类编号",notes = "")
  @Column(name = "detail_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '活动细类编号 '")
  private String detailCode;
  
  /** 预算科目编码 */
  @ApiModelProperty(name = "预算科目编码",notes = "")
  @Column(name = "budget_subjects_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
  private String budgetSubjectsCode;
  
  /** 预算科目名称 */
  @ApiModelProperty(name = "预算科目名称",notes = "")
  @Column(name = "budget_subjects_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;
  

}