package com.biz.crm.tpm.business.track.local.service.internal;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetIncomeDto;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.biz.crm.tpm.business.control.sdk.enums.ControlDimensionEnum;
import com.biz.crm.tpm.business.control.sdk.enums.ControlTypeEnum;
import com.biz.crm.tpm.business.control.sdk.enums.TimeDimensionEnum;
import com.biz.crm.tpm.business.control.sdk.service.BudgetControlLockVoService;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlDimensionVo;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlRangeVo;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlSubjectVo;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlVo;
import com.biz.crm.tpm.business.pay.sdk.dto.ManageReportDto;
import com.biz.crm.tpm.business.pay.sdk.service.ManageReportService;
import com.biz.crm.tpm.business.pay.sdk.vo.ManageReportVo;
import com.biz.crm.tpm.business.track.local.entity.BudgetTrack;
import com.biz.crm.tpm.business.track.local.repository.BudgetTrackRepository;
import com.biz.crm.tpm.business.track.local.service.BudgetTrackAsyncService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BudgetTrackAsyncServiceImpl implements BudgetTrackAsyncService {

    @Autowired(required = false)
    private CostBudgetVoService costBudgetVoService;
    @Autowired(required = false)
    private OrgVoService orgVoService;
    @Autowired(required = false)
    private CostBudgetIncomeService costBudgetIncomeService;
    @Autowired(required = false)
    private ManageReportService manageReportService;
    @Autowired(required = false)
    private BudgetTrackRepository budgetTrackRepository;
    @Autowired(required = false)
    private LoginUserService loginUserService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private BudgetControlLockVoService budgetControlLockVoService;


    /**
     * 计算
     *
     * @param list
     * @param orgVoMap
     */
    @Override
    public void calculate(List<BudgetTrack> list, Map<String, List<OrgVo>> orgVoMap) {
        for (BudgetTrack track : list) {
            Set<String> orgSetAll = new HashSet<>();
            String[] split = track.getDepartmentOneCode().split(",");
            for (int i = 0; i < split.length; i++) {
                if (CollectionUtils.isEmpty(orgVoMap.get(split[i]))) {
                    continue;
                }
                Set<String> orgSet = orgVoMap.get(split[i]).stream().map(e -> e.getOrgCode()).collect(Collectors.toSet());
                orgSetAll.addAll(orgSet);
            }
            CostBudgetIncomeDto dto = new CostBudgetIncomeDto();
            dto.setDeptCodeSet(orgSetAll);
            dto.setCustomerCode(track.getCustomerCode());
            dto.setItemName(track.getItemName());
            dto.setProductCode(track.getProductCode());
            dto.setYearMonthLy(track.getYearMonthLy());
            List<CostBudgetIncomeVo> incomeVoList = costBudgetIncomeService.findByDto(dto);
            if (CollectionUtils.isEmpty(incomeVoList)) {
                continue;
            }
            BigDecimal incomeAmount = incomeVoList.stream().map(CostBudgetIncomeVo::getIncomeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (Objects.isNull(incomeAmount)
                    || incomeAmount.compareTo(BigDecimal.ZERO) == 0) {
                incomeAmount = BigDecimal.ONE;
            }
            track.setRate(Optional.ofNullable(track.getInitialAmount()).orElse(BigDecimal.ZERO).divide(incomeAmount, 4,
                    BigDecimal.ROUND_HALF_UP));
        }
    }

    /**
     * 预算跟踪按率计算
     *
     * @param track
     * @param historyTrackList
     */
    @Override
    public void trackRate(BudgetTrack track, List<BudgetTrack> historyTrackList, Map<String, List<OrgVo>> orgVoMap) {
        Set<String> orgSetAll = orgVoMap.values().stream().flatMap(List::stream).map(e -> e.getOrgCode()).collect(Collectors.toSet());
        ManageReportDto dto = new ManageReportDto();
        dto.setDeptCodeSet(orgSetAll);
        dto.setCustomerCode(track.getCustomerCode());
        dto.setItemName(track.getItemName());
        dto.setProductCode(track.getProductCode());
        dto.setYearMonthLySet(historyTrackList.stream().map(e -> e.getYearMonthLy()).collect(Collectors.toSet()));
        List<ManageReportVo> reportVoList = manageReportService.findByDto(dto);

        BigDecimal amountTotal = BigDecimal.ZERO;
        BigDecimal freezeAmount = BigDecimal.ZERO;
        BigDecimal usedAmount = BigDecimal.ZERO;
        Map<String, List<ManageReportVo>> reportMap = reportVoList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(e -> e.getYearMonthLy()));
        for (int i = 0; i < historyTrackList.size(); i++) {
            BudgetTrack historyTrack = historyTrackList.get(i);
            freezeAmount = freezeAmount.add(historyTrack.getFreezeAmount());
            usedAmount = usedAmount.add(historyTrack.getUsedAmount());
            BigDecimal thisMonthAmount =BigDecimal.ZERO;
            List<ManageReportVo> list = reportMap != null && reportMap.containsKey(historyTrack.getYearMonthLy()) ? reportMap.get(historyTrack.getYearMonthLy()) : new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                //管报收入金额
                BigDecimal reportAmount = list.stream().map(e -> e.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                track.setReportAmount(reportAmount);
                //管报收入金额*预算费率
                thisMonthAmount = reportAmount.multiply(historyTrack.getRate() == null ? BigDecimal.ZERO : historyTrack.getRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
                track.setActualAmount(thisMonthAmount);
            } else {
                track.setReportAmount(BigDecimal.ZERO);
                track.setActualAmount(BigDecimal.ZERO);
            }
            // 当前月份的前两月，都是取管报收入金额*预算费率，上月及本月取预算金额
            // 修改为 有管报取管报，没有管报取预算金额，本月只有预算金额（管报是下个月出上个月）
            // 再修改 俩月份中间隔月，有管报取管报，否则取0
            if (i >= historyTrackList.size() - 1) {
                amountTotal = amountTotal.add(historyTrack.getThisInitialAmount());
            } else {
                if (i < historyTrackList.size() - 2) {
                    amountTotal = amountTotal.add(list.isEmpty()? BigDecimal.ZERO : thisMonthAmount);
                } else {
                    amountTotal = amountTotal.add(list.isEmpty()? historyTrack.getThisInitialAmount() : thisMonthAmount);
                }
            }
        }
        track.setInitialAmount(amountTotal);
        track.setFreezeAmount(freezeAmount);
        track.setUsedAmount(usedAmount);
        track.setMonthBalanceAmount(amountTotal.subtract(freezeAmount).subtract(usedAmount));
    }


    /**
     * 单条管控生成对应的预算跟踪
     *
     * @param controlVo
     * @param crmUserIdentity
     */
    @Override
    @Async("tpmBudgetTrackThread")
    public Future<List<BudgetTrack>> generateBudgetTrackSingle(BudgetControlVo controlVo, Map<String, Set<String>> subjectMap, AbstractCrmUserIdentity crmUserIdentity) {
        List<BudgetTrack> saveBudgetTrackList = new ArrayList<>();
        String timeDimension = controlVo.getTimeDimension();
        CostBudgetDto dto = new CostBudgetDto();
        //部门所有Map汇总
        Map<String, List<OrgVo>> orgVoMapAll = new HashMap<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //头部部门
        List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(controlVo.getDepartmentCode());
        orgVoMapAll.put(controlVo.getDepartmentCode(), orgVoList);
        Set<String> orgCodeSet = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet());
        dto.setHeadDeptCodeSet(orgCodeSet);
        //管控维度
        List<BudgetControlDimensionVo> dimensionList = controlVo.getDimensionList();
        dimensionList.forEach(e -> {
            if (ControlDimensionEnum.DEPARTMENT.getCode().equals(e.getDimensionCode())) {
                dto.setHasDept(BooleanEnum.TRUE.getCapital());
            } else if (ControlDimensionEnum.CUSTOMER.getCode().equals(e.getDimensionCode())) {
                dto.setHasCus(BooleanEnum.TRUE.getCapital());
            } else if (ControlDimensionEnum.ITEM.getCode().equals(e.getDimensionCode())) {
                dto.setHasItem(BooleanEnum.TRUE.getCapital());
            } else {
                dto.setHasProduct(BooleanEnum.TRUE.getCapital());
            }
        });
        stopWatch.stop();
        log.info("预算管控【{}】查询部门耗时1：{}", controlVo.getControlCode(), stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        stopWatch.start();
        //剔除部门
        Set<String> deptCodeExcludeSet = controlVo.getRangeDeptExcludeList().stream().map(BudgetControlRangeVo::getDepartmentCode).collect(Collectors.toSet());
        List<OrgVo> orgVoListExcludeDetail = orgVoService.findAllChildrenByOrgCodes(new ArrayList<>(deptCodeExcludeSet));
        Set<String> orgCodeDetailExcludeSet = orgVoListExcludeDetail.stream().map(OrgVo::getOrgCode).collect(Collectors.toSet());
        stopWatch.stop();
        log.info("预算管控【{}】查询部门耗时2：{}", controlVo.getControlCode(), stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        stopWatch.start();
        //部门
        if (CollectionUtils.isNotEmpty(controlVo.getRangeDeptList())) {
            Set<String> deptCodeSet = controlVo.getRangeDeptList().stream().map(BudgetControlRangeVo::getDepartmentCode).collect(Collectors.toSet());
            Map<String, List<OrgVo>> orgVoListDetail = orgVoService.findAllChildrenByOrgCodesMap(new ArrayList<>(deptCodeSet));
            orgVoMapAll.putAll(orgVoListDetail);
            Set<String> orgCodeDetailSet = orgVoListDetail.values().stream().flatMap(List::stream).map(OrgVo::getOrgCode).collect(Collectors.toSet());
            orgCodeDetailSet.removeAll(orgCodeDetailExcludeSet);
            dto.setDeptCodeSet(orgCodeDetailSet);
            dto.setHeadDeptCodeSet(null);
        } else {
            dto.getHeadDeptCodeSet().removeAll(orgCodeDetailExcludeSet);
            dto.setDeptCodeSet(dto.getHeadDeptCodeSet());
            dto.setHeadDeptCodeSet(null);
        }
        stopWatch.stop();
        log.info("预算管控【{}】查询部门耗时3：{}", controlVo.getControlCode(), stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        stopWatch.start();
        //客户
        Set<String> cusCodeSet = controlVo.getRangeCusList().stream().map(BudgetControlRangeVo::getCustomerCode).collect(Collectors.toSet());
        dto.setCusCodeSet(cusCodeSet);
        //剔除客户
        Set<String> cusCodeExcludeSet = controlVo.getRangeCusExcludeList().stream().map(BudgetControlRangeVo::getCustomerCode).collect(Collectors.toSet());
        dto.setCusCodeExcludeSet(cusCodeExcludeSet);
        //科目
        Set<String> subCodeSet = new HashSet<>();
        controlVo.getSubjectList().forEach(e -> subCodeSet.addAll(subjectMap.getOrDefault(e.getBudgetSubjectsCode(), new HashSet<>())));
        dto.setSubjectCodeSet(subCodeSet);
        List<String> yearMonthList;
        //月度只用查对应的月份，年度需要查同年1月开始的年度预算
        if (TimeDimensionEnum.MONTH.getCode().equals(timeDimension)) {
            yearMonthList = getMonthBetween(controlVo.getStartYearMonth(), controlVo.getEndYearMonth());
        } else {
            String[] split = controlVo.getStartYearMonth().split("-");
            yearMonthList = getMonthBetween(split[0] + "-01", controlVo.getEndYearMonth());
        }
        dto.setYearMonthList(yearMonthList);
        List<CostBudgetVo> costBudgetVoList = costBudgetVoService.findByDto(dto);
        stopWatch.stop();
        log.info("预算管控【{}】查询部门耗时4：{}", controlVo.getControlCode(), stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        stopWatch.start();
        if (CollectionUtils.isEmpty(costBudgetVoList)) {
            log.warn("预算管控【{}】未找到对应的预算", controlVo.getControlCode());
            return new AsyncResult<>(Lists.newArrayList());
        }

        Map<String, Map<String, List<CostBudgetVo>>> deptMap = new HashMap<>();
        Map<String, Map<String, List<CostBudgetVo>>> cusMap = new HashMap<>();
        Map<String, Map<String, List<CostBudgetVo>>> itemMap = new HashMap<>();
        Map<String, Map<String, List<CostBudgetVo>>> subMap = new HashMap<>();
        //按部门+年月汇总的预算
        if (BooleanEnum.TRUE.getCapital().equals(dto.getHasDept())) {
            deptMap = costBudgetVoList.stream().collect(Collectors.groupingBy(e -> e.getYearMonthLy(), Collectors.groupingBy(e -> e.getDepartmentOneCode())));
        }
        //按客户+年月汇总的预算
        if (BooleanEnum.TRUE.getCapital().equals(dto.getHasCus())) {
            cusMap = costBudgetVoList.stream().collect(Collectors.groupingBy(e -> e.getYearMonthLy(), Collectors.groupingBy(e -> e.getCustomerCode())));
        }

        //按品项、产品+年月汇总的预算，如果既按品项又按产品，那就按产品汇总
        if (BooleanEnum.TRUE.getCapital().equals(dto.getHasItem()) && BooleanEnum.TRUE.getCapital().equals(dto.getHasItem())) {
            itemMap = costBudgetVoList.stream().filter(e -> StringUtils.isNotBlank(e.getProductCode())).collect(Collectors.groupingBy(e -> e.getYearMonthLy(), Collectors.groupingBy(e -> e.getProductCode())));
        } else {
            if (BooleanEnum.TRUE.getCapital().equals(dto.getHasItem())) {
                itemMap = costBudgetVoList.stream().filter(e -> StringUtils.isNotBlank(e.getProductLevelCode())).collect(Collectors.groupingBy(e -> e.getYearMonthLy(), Collectors.groupingBy(e -> e.getProductLevelCode())));
            } else if (BooleanEnum.TRUE.getCapital().equals(dto.getHasProduct())) {
                itemMap = costBudgetVoList.stream().filter(e -> StringUtils.isNotBlank(e.getProductCode())).collect(Collectors.groupingBy(e -> e.getYearMonthLy(), Collectors.groupingBy(e -> e.getProductCode())));
            }
        }
        //按科目+年月汇总的预算
        if (!CollectionUtils.isEmpty(controlVo.getSubjectList())) {
            subMap = costBudgetVoList.stream().collect(Collectors.groupingBy(e -> e.getYearMonthLy(), Collectors.groupingBy(e -> e.getBudgetSubjectCode())));
        }
        stopWatch.stop();
        log.info("预算管控【{}】查询部门耗时5：{}", controlVo.getControlCode(), stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        stopWatch.start();
        Map<String, List<BudgetTrack>> trackMapList = new HashMap<>();
        for (String ym : yearMonthList) {
            List<BudgetTrack> list = new ArrayList<>();
            Map<String, List<CostBudgetVo>> deptMapList = deptMap.get(ym);
            Map<String, List<CostBudgetVo>> cusMapList = cusMap.get(ym);
            Map<String, List<CostBudgetVo>> itemMapList = itemMap.get(ym);
            Map<String, List<CostBudgetVo>> subMapList = subMap.get(ym);
            deptGroup(deptMapList, list, controlVo, ym, orgVoMapAll);
            list = cusGroup(cusMapList, list, controlVo, ym);
            list = itemGroup(itemMapList, list, controlVo, ym);
            list = subGroup(subMapList, list, controlVo, ym, subjectMap);
            trackMapList.put(ym, list);
        }
        loginUserService.refreshAuthentication(crmUserIdentity);
        Date date = new Date();
        String userName = crmUserIdentity.getAccount();
        String realName = crmUserIdentity.getRealName();
        //年度累计
        if (TimeDimensionEnum.YEAR.getCode().equals(timeDimension)) {
            //只保存生效期间的追踪
            List<String> monthBetween = getMonthBetween(controlVo.getStartYearMonth(), controlVo.getEndYearMonth());
            //按率
            if (ControlTypeEnum.RATE.getCode().equals(controlVo.getControlType())) {
                monthBetween.forEach(m -> {
                    List<BudgetTrack> budgetTracks = trackMapList.get(m);
                    if (CollectionUtils.isEmpty(budgetTracks)) {
                        return;
                    }
                    //获取1月到当前月的跟踪
                    String[] split = m.split("-");
                    List<String> historyMonth = getMonthBetween(split[0] + "-01", m);
                    for (BudgetTrack thisTrack : budgetTracks) {
                        List<BudgetTrack> historyTrackList = new ArrayList();
                        historyMonth.forEach(e -> {
                            List<BudgetTrack> budgetTracks1 = trackMapList.get(e);
                            if (CollectionUtils.isEmpty(budgetTracks1)) {
                                return;
                            }
                            for (BudgetTrack historyTrack : budgetTracks1) {
                                if (historyTrack.getDimensionValue().equals(thisTrack.getDimensionValue())) {
                                    historyTrackList.add(historyTrack);
                                }
                            }
                        });

                        Set<String> deptCodeSet = historyTrackList.stream().map(BudgetTrack::getDepartmentOneCode).collect(Collectors.toSet());
                        Set<String> deptCodeSetDto = new HashSet<>();
                        deptCodeSet.forEach(e -> {
                            String[] splitCodes = e.split(",");
                            for (int i = 0; i < splitCodes.length; i++) {
                                deptCodeSetDto.add(splitCodes[i]);
                            }
                        });
                        Map<String, List<OrgVo>> orgVoMap = orgVoMapAll.entrySet().stream()
                                .filter(e -> deptCodeSetDto.contains(e.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                        //计算费率
                        calculate(historyTrackList, orgVoMap);

                        BudgetTrack newTrack = new BudgetTrack();
                        BeanUtils.copyProperties(thisTrack, newTrack);
                        trackRate(newTrack, historyTrackList, orgVoMap);
                        saveBudgetTrackList.add(newTrack);
                    }
                });
                stopWatch.stop();
                log.info("预算管控【{}】查询部门耗时5-1：{}", controlVo.getControlCode(), stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
                stopWatch.start();
                //按额
            } else {
                monthBetween.forEach(m -> {
                    List<BudgetTrack> budgetTracks = trackMapList.get(m);
                    //获取1月到当前月的累计
                    String[] split = m.split("-");
                    List<String> historyMonth = getMonthBetween(split[0] + "-01", m);
                    for (BudgetTrack thisTrack : budgetTracks) {
                        List<BudgetTrack> historyTrackList = new ArrayList();
                        historyMonth.forEach(e -> {
                            List<BudgetTrack> budgetTracks1 = trackMapList.get(e);
                            for (BudgetTrack historyTrack : budgetTracks1) {
                                if (historyTrack.getDimensionValue().equals(thisTrack.getDimensionValue())) {
                                    historyTrackList.add(historyTrack);
                                }
                            }
                        });
                        BigDecimal adjustAmount = BigDecimal.ZERO;
                        BigDecimal adjustBalanceAmount = BigDecimal.ZERO;
                        BigDecimal freezeAmount = BigDecimal.ZERO;
                        BigDecimal usedAmount = BigDecimal.ZERO;
                        BigDecimal monthBalanceAmount = BigDecimal.ZERO;
                        BigDecimal initialAmount = BigDecimal.ZERO;
                        for (BudgetTrack e : historyTrackList) {
                            adjustAmount = adjustAmount.add(e.getAdjustAmount());
                            adjustBalanceAmount = adjustBalanceAmount.add(e.getAdjustBalanceAmount());
                            freezeAmount = freezeAmount.add(e.getFreezeAmount());
                            usedAmount = usedAmount.add(e.getUsedAmount());
                            monthBalanceAmount = monthBalanceAmount.add(e.getMonthBalanceAmount());
                            initialAmount = initialAmount.add(e.getInitialAmount());
                        }
                        BudgetTrack newTrack = new BudgetTrack();
                        BeanUtils.copyProperties(thisTrack, newTrack);
                        newTrack.setInitialAmount(initialAmount);
                        newTrack.setAdjustAmount(adjustAmount);
                        newTrack.setAdjustBalanceAmount(adjustBalanceAmount);
                        newTrack.setFreezeAmount(freezeAmount);
                        newTrack.setUsedAmount(usedAmount);
                        newTrack.setMonthBalanceAmount(monthBalanceAmount);
                        saveBudgetTrackList.add(newTrack);
                    }
                });
                stopWatch.stop();
                log.info("预算管控【{}】查询部门耗时5-2：{}", controlVo.getControlCode(), stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
                stopWatch.start();
            }
        } else {
            trackMapList.forEach((k, v) -> saveBudgetTrackList.addAll(v));
            stopWatch.stop();
            log.info("预算管控【{}】查询部门耗时5-3：{}", controlVo.getControlCode(), stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
            stopWatch.start();
        }
        return new AsyncResult<>(saveBudgetTrackList);
    }


    /**
     * 部门汇总
     *
     * @param deptMapList
     * @param list
     */
    private void deptGroup(Map<String, List<CostBudgetVo>> deptMapList, List<BudgetTrack> list, BudgetControlVo controlVo, String ym, Map<String, List<OrgVo>> allChildrenByOrgCodesMap) {
        //按额 累计管控预算总额=期初金额+调整金额;累计可用余额=期初金额+调整金额-已使用金额
        if (MapUtils.isNotEmpty(deptMapList)) {

            //同一明细单号的多个部门，汇总成一条数据
            if (CollectionUtils.isNotEmpty(controlVo.getRangeDeptList())) {
                List<BudgetControlRangeVo> deptList = controlVo.getRangeDeptList();
                List<CostBudgetVo> cbDeptList = new ArrayList<>();
                deptMapList.values().forEach(e -> cbDeptList.addAll(e));
                Map<String, List<BudgetControlRangeVo>> deptListMap = deptList.stream().collect(Collectors.groupingBy(e -> e.getControlDetailCode()));

                for (Map.Entry<String, List<BudgetControlRangeVo>> entry : deptListMap.entrySet()) {
                    List<BudgetControlRangeVo> v = entry.getValue();
                    List<String> deptCodeList = v.stream().map(e -> e.getDepartmentCode()).collect(Collectors.toList());
                    List<String> deptNameList = v.stream().map(e -> e.getDepartmentName()).collect(Collectors.toList());
                    List<String> levelList = v.stream().map(e -> String.valueOf(e.getLevelNum())).collect(Collectors.toList());

                    //合并该部门下所有的子部门
                    Set<String> deptCodeListAll = new HashSet<>();
                    deptCodeList.forEach(e -> {
                        List<OrgVo> orgVos = allChildrenByOrgCodesMap.get(e);
                        if (CollectionUtils.isNotEmpty(orgVos)) {
                            deptCodeListAll.addAll(orgVos.stream().map(o -> o.getOrgCode()).collect(Collectors.toSet()));
                        }
                    });
                    List<CostBudgetVo> filterList = cbDeptList.stream().filter(e -> deptCodeListAll.contains(e.getDepartmentOneCode())).collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(filterList)) {
                        continue;
                    }

                    BudgetTrack budgetTrack = buildBudgetTrack(controlVo, null, ym);
                    BigDecimal adjustAmount = BigDecimal.ZERO;
                    BigDecimal adjustBalanceAmount = BigDecimal.ZERO;
                    BigDecimal freezeAmount = BigDecimal.ZERO;
                    BigDecimal usedAmount = BigDecimal.ZERO;
                    BigDecimal monthBalanceAmount = BigDecimal.ZERO;
                    BigDecimal initialAmount = BigDecimal.ZERO;
                    List<String> budgetCodes = new ArrayList<>();
                    for (CostBudgetVo vo : filterList) {
                        adjustAmount = adjustAmount.add(Optional.ofNullable(vo.getAdjustAmount()).orElse(BigDecimal.ZERO));
                        adjustBalanceAmount = adjustBalanceAmount.add(Optional.ofNullable(vo.getAdjustBalanceAmount()).orElse(BigDecimal.ZERO));
                        freezeAmount = freezeAmount.add(Optional.ofNullable(vo.getFreezeAmount()).orElse(BigDecimal.ZERO));
                        usedAmount = usedAmount.add(Optional.ofNullable(vo.getUsedAmount()).orElse(BigDecimal.ZERO));
                        monthBalanceAmount = monthBalanceAmount.add(Optional.ofNullable(vo.getMonthBalanceAmount()).orElse(BigDecimal.ZERO));
                        initialAmount = initialAmount.add(Optional.ofNullable(vo.getInitialAmount()).orElse(BigDecimal.ZERO));
                        budgetCodes.add(vo.getCode());
                    }
                    budgetTrack.setDepartmentOneCode(String.join(",", deptCodeList));
                    budgetTrack.setDepartmentOneName(String.join(",", deptNameList));
                    budgetTrack.setLevelNum(String.join(",", levelList));
                    budgetTrack.setThisInitialAmount(adjustBalanceAmount);
                    budgetTrack.setInitialAmount(initialAmount);
                    budgetTrack.setAdjustAmount(adjustAmount);
                    budgetTrack.setAdjustBalanceAmount(adjustBalanceAmount);
                    budgetTrack.setFreezeAmount(freezeAmount);
                    budgetTrack.setUsedAmount(usedAmount);
                    budgetTrack.setMonthBalanceAmount(monthBalanceAmount);
                    budgetTrack.setDimension("部门_");
                    budgetTrack.setDimensionValue(budgetTrack.getDepartmentOneCode() + "_");
                    budgetTrack.setBudgetCodes(budgetCodes);
                    list.add(budgetTrack);
                }
            }

        }
    }

    /**
     * 客户汇总
     *
     * @param cusMapList
     * @param list
     */
    private List<BudgetTrack> cusGroup(Map<String, List<CostBudgetVo>> cusMapList, List<BudgetTrack> list, BudgetControlVo controlVo, String ym) {
        List<BudgetTrack> listAll = new ArrayList<>();
        //按额 累计管控预算总额=期初金额+调整金额;累计可用余额=期初金额+调整金额-已使用金额
        if (MapUtils.isNotEmpty(cusMapList)) {

            if (CollectionUtils.isEmpty(list)) {
                cusMapList.forEach((k, v) -> {
                    BudgetTrack budgetTrack = buildBudgetTrack(controlVo, null, ym);
                    BigDecimal adjustAmount = BigDecimal.ZERO;
                    BigDecimal adjustBalanceAmount = BigDecimal.ZERO;
                    BigDecimal freezeAmount = BigDecimal.ZERO;
                    BigDecimal usedAmount = BigDecimal.ZERO;
                    BigDecimal monthBalanceAmount = BigDecimal.ZERO;
                    BigDecimal initialAmount = BigDecimal.ZERO;
                    List<String> budgetCodes = new ArrayList<>();
                    for (CostBudgetVo vo : v) {
                        adjustAmount = adjustAmount.add(Optional.ofNullable(vo.getAdjustAmount()).orElse(BigDecimal.ZERO));
                        adjustBalanceAmount = adjustBalanceAmount.add(Optional.ofNullable(vo.getAdjustBalanceAmount()).orElse(BigDecimal.ZERO));
                        freezeAmount = freezeAmount.add(Optional.ofNullable(vo.getFreezeAmount()).orElse(BigDecimal.ZERO));
                        usedAmount = usedAmount.add(Optional.ofNullable(vo.getUsedAmount()).orElse(BigDecimal.ZERO));
                        monthBalanceAmount = monthBalanceAmount.add(Optional.ofNullable(vo.getMonthBalanceAmount()).orElse(BigDecimal.ZERO));
                        initialAmount = initialAmount.add(Optional.ofNullable(vo.getInitialAmount()).orElse(BigDecimal.ZERO));
                        budgetCodes.add(vo.getCode());
                    }
                    budgetTrack.setCustomerCode(k);
                    budgetTrack.setCustomerName(v.get(0).getCustomerName());
                    budgetTrack.setThisInitialAmount(adjustBalanceAmount);
                    budgetTrack.setInitialAmount(initialAmount);
                    budgetTrack.setAdjustAmount(adjustAmount);
                    budgetTrack.setAdjustBalanceAmount(adjustBalanceAmount);
                    budgetTrack.setFreezeAmount(freezeAmount);
                    budgetTrack.setUsedAmount(usedAmount);
                    budgetTrack.setMonthBalanceAmount(monthBalanceAmount);
                    budgetTrack.setDimension("客户_");
                    budgetTrack.setDimensionValue(k + "_");
                    budgetTrack.setBudgetCodes(budgetCodes);
                    listAll.add(budgetTrack);
                });
            } else {
                for (BudgetTrack bt : list) {
                    for (Map.Entry<String, List<CostBudgetVo>> entry : cusMapList.entrySet()) {
                        List<CostBudgetVo> filterList = entry.getValue().stream().filter(e -> bt.getBudgetCodes().contains(e.getCode())).collect(Collectors.toList());

                        if (CollectionUtils.isEmpty(filterList)) {
                            continue;
                        }

                        BudgetTrack budgetTrack = buildBudgetTrack(controlVo, bt, ym);
                        BigDecimal adjustAmount = BigDecimal.ZERO;
                        BigDecimal adjustBalanceAmount = BigDecimal.ZERO;
                        BigDecimal freezeAmount = BigDecimal.ZERO;
                        BigDecimal usedAmount = BigDecimal.ZERO;
                        BigDecimal monthBalanceAmount = BigDecimal.ZERO;
                        BigDecimal initialAmount = BigDecimal.ZERO;

                        List<String> budgetCodes = new ArrayList<>();
                        for (CostBudgetVo vo : filterList) {
                            adjustAmount = adjustAmount.add(Optional.ofNullable(vo.getAdjustAmount()).orElse(BigDecimal.ZERO));
                            adjustBalanceAmount = adjustBalanceAmount.add(Optional.ofNullable(vo.getAdjustBalanceAmount()).orElse(BigDecimal.ZERO));
                            freezeAmount = freezeAmount.add(Optional.ofNullable(vo.getFreezeAmount()).orElse(BigDecimal.ZERO));
                            usedAmount = usedAmount.add(Optional.ofNullable(vo.getUsedAmount()).orElse(BigDecimal.ZERO));
                            monthBalanceAmount = monthBalanceAmount.add(Optional.ofNullable(vo.getMonthBalanceAmount()).orElse(BigDecimal.ZERO));
                            initialAmount = initialAmount.add(Optional.ofNullable(vo.getInitialAmount()).orElse(BigDecimal.ZERO));
                            budgetCodes.add(vo.getCode());
                        }

                        budgetTrack.setCustomerCode(entry.getKey());
                        budgetTrack.setCustomerName(entry.getValue().get(0).getCustomerName());
                        budgetTrack.setThisInitialAmount(adjustBalanceAmount);
                        budgetTrack.setInitialAmount(initialAmount);
                        budgetTrack.setAdjustAmount(adjustAmount);
                        budgetTrack.setAdjustBalanceAmount(adjustBalanceAmount);
                        budgetTrack.setFreezeAmount(freezeAmount);
                        budgetTrack.setUsedAmount(usedAmount);
                        budgetTrack.setMonthBalanceAmount(monthBalanceAmount);
                        budgetTrack.setDimension((StringUtils.isNotEmpty(budgetTrack.getDimension()) ? budgetTrack.getDimension() : "") + "客户_");
                        budgetTrack.setDimensionValue((StringUtils.isNotEmpty(budgetTrack.getDimensionValue()) ? budgetTrack.getDimensionValue() : "") +
                                entry.getKey() + "_");
                        budgetTrack.setBudgetCodes(budgetCodes);
                        listAll.add(budgetTrack);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(listAll)) {
            return listAll;
        }
        return list;
    }

    /**
     * 品项、产品汇总
     *
     * @param itemMapList
     * @param list
     */
    private List<BudgetTrack> itemGroup(Map<String, List<CostBudgetVo>> itemMapList, List<BudgetTrack> list, BudgetControlVo controlVo, String ym) {
        List<BudgetTrack> listAll = new ArrayList<>();
        //按额 累计管控预算总额=期初金额+调整金额;累计可用余额=期初金额+调整金额-已使用金额
        if (MapUtils.isNotEmpty(itemMapList)) {
            if (CollectionUtils.isEmpty(list)) {
                itemMapList.forEach((k, v) -> {
                    BudgetTrack budgetTrack = buildBudgetTrack(controlVo, null, ym);
                    BigDecimal adjustAmount = BigDecimal.ZERO;
                    BigDecimal adjustBalanceAmount = BigDecimal.ZERO;
                    BigDecimal freezeAmount = BigDecimal.ZERO;
                    BigDecimal usedAmount = BigDecimal.ZERO;
                    BigDecimal monthBalanceAmount = BigDecimal.ZERO;
                    BigDecimal initialAmount = BigDecimal.ZERO;
                    List<String> budgetCodes = new ArrayList<>();
                    for (CostBudgetVo vo : v) {
                        adjustAmount = adjustAmount.add(Optional.ofNullable(vo.getAdjustAmount()).orElse(BigDecimal.ZERO));
                        adjustBalanceAmount = adjustBalanceAmount.add(Optional.ofNullable(vo.getAdjustBalanceAmount()).orElse(BigDecimal.ZERO));
                        freezeAmount = freezeAmount.add(Optional.ofNullable(vo.getFreezeAmount()).orElse(BigDecimal.ZERO));
                        usedAmount = usedAmount.add(Optional.ofNullable(vo.getUsedAmount()).orElse(BigDecimal.ZERO));
                        monthBalanceAmount = monthBalanceAmount.add(Optional.ofNullable(vo.getMonthBalanceAmount()).orElse(BigDecimal.ZERO));
                        initialAmount = initialAmount.add(Optional.ofNullable(vo.getInitialAmount()).orElse(BigDecimal.ZERO));
                        budgetCodes.add(vo.getCode());
                    }
                    budgetTrack.setItemCode(v.get(0).getProductLevelCode());
                    budgetTrack.setItemName(v.get(0).getProductLevelName());
                    budgetTrack.setProductCode(v.get(0).getProductCode());
                    budgetTrack.setProductName(v.get(0).getProductName());
                    budgetTrack.setThisInitialAmount(adjustBalanceAmount);
                    budgetTrack.setInitialAmount(initialAmount);
                    budgetTrack.setAdjustAmount(adjustAmount);
                    budgetTrack.setAdjustBalanceAmount(adjustBalanceAmount);
                    budgetTrack.setFreezeAmount(freezeAmount);
                    budgetTrack.setUsedAmount(usedAmount);
                    budgetTrack.setMonthBalanceAmount(monthBalanceAmount);
                    budgetTrack.setDimension((StringUtils.isNotEmpty(budgetTrack.getDimension()) ? budgetTrack.getDimension() : "") + "品项产品_");
                    budgetTrack.setDimensionValue((StringUtils.isNotEmpty(budgetTrack.getDimensionValue()) ? budgetTrack.getDimensionValue() : "") +
                            (StringUtils.isNotBlank(budgetTrack.getItemName()) ? budgetTrack.getItemName() : "") +
                            (StringUtils.isNotBlank(budgetTrack.getProductCode()) ? budgetTrack.getProductCode() : ""));
                    budgetTrack.setBudgetCodes(budgetCodes);
                    listAll.add(budgetTrack);
                });
            } else {
                for (BudgetTrack bt : list) {
                    for (Map.Entry<String, List<CostBudgetVo>> entry : itemMapList.entrySet()) {
                        List<CostBudgetVo> filterList = entry.getValue().stream().filter(e -> bt.getBudgetCodes().contains(e.getCode())).collect(Collectors.toList());

                        if (CollectionUtils.isEmpty(filterList)) {
                            continue;
                        }

                        BudgetTrack budgetTrack = buildBudgetTrack(controlVo, bt, ym);
                        BigDecimal adjustAmount = BigDecimal.ZERO;
                        BigDecimal adjustBalanceAmount = BigDecimal.ZERO;
                        BigDecimal freezeAmount = BigDecimal.ZERO;
                        BigDecimal usedAmount = BigDecimal.ZERO;
                        BigDecimal monthBalanceAmount = BigDecimal.ZERO;
                        BigDecimal initialAmount = BigDecimal.ZERO;
                        List<String> budgetCodes = new ArrayList<>();
                        for (CostBudgetVo vo : entry.getValue()) {
                            adjustAmount = adjustAmount.add(Optional.ofNullable(vo.getAdjustAmount()).orElse(BigDecimal.ZERO));
                            adjustBalanceAmount = adjustBalanceAmount.add(Optional.ofNullable(vo.getAdjustBalanceAmount()).orElse(BigDecimal.ZERO));
                            freezeAmount = freezeAmount.add(Optional.ofNullable(vo.getFreezeAmount()).orElse(BigDecimal.ZERO));
                            usedAmount = usedAmount.add(Optional.ofNullable(vo.getUsedAmount()).orElse(BigDecimal.ZERO));
                            monthBalanceAmount = monthBalanceAmount.add(Optional.ofNullable(vo.getMonthBalanceAmount()).orElse(BigDecimal.ZERO));
                            initialAmount = initialAmount.add(Optional.ofNullable(vo.getInitialAmount()).orElse(BigDecimal.ZERO));
                            budgetCodes.add(vo.getCode());
                        }
                        budgetTrack.setItemCode(entry.getValue().get(0).getProductLevelCode());
                        budgetTrack.setItemName(entry.getValue().get(0).getProductLevelName());
                        budgetTrack.setProductCode(entry.getValue().get(0).getProductCode());
                        budgetTrack.setProductName(entry.getValue().get(0).getProductName());
                        budgetTrack.setThisInitialAmount(adjustBalanceAmount);
                        budgetTrack.setInitialAmount(initialAmount);
                        budgetTrack.setAdjustAmount(adjustAmount);
                        budgetTrack.setAdjustBalanceAmount(adjustBalanceAmount);
                        budgetTrack.setFreezeAmount(freezeAmount);
                        budgetTrack.setUsedAmount(usedAmount);
                        budgetTrack.setMonthBalanceAmount(monthBalanceAmount);
                        budgetTrack.setDimension((StringUtils.isNotEmpty(budgetTrack.getDimension()) ? budgetTrack.getDimension() : "") + "品项产品_");
                        budgetTrack.setDimensionValue((StringUtils.isNotEmpty(budgetTrack.getDimensionValue()) ? budgetTrack.getDimensionValue() : "") +
                                (StringUtils.isNotBlank(budgetTrack.getItemName()) ? budgetTrack.getItemName() : "") +
                                (StringUtils.isNotBlank(budgetTrack.getProductCode()) ? budgetTrack.getProductCode() : ""));
                        budgetTrack.setBudgetCodes(budgetCodes);
                        listAll.add(budgetTrack);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(listAll)) {
            return listAll;
        }
        return list;
    }

    /**
     * 预算科目汇总
     *
     * @param subMapList
     * @param list
     */
    private List<BudgetTrack> subGroup(Map<String, List<CostBudgetVo>> subMapList, List<BudgetTrack> list, BudgetControlVo controlVo, String ym, Map<String, Set<String>> subjectMap) {
        List<BudgetTrack> listAll = new ArrayList<>();

        //按额 累计管控预算总额=期初金额+调整金额;累计可用余额=期初金额+调整金额-已使用金额
        if (MapUtils.isNotEmpty(subMapList)) {
            //同一明细单号的多个科目，汇总成一条数据
            List<BudgetControlSubjectVo> subjectList = controlVo.getSubjectList();
            List<CostBudgetVo> cbSubjectList = new ArrayList<>();
            subMapList.values().forEach(e -> cbSubjectList.addAll(e));
            Map<String, List<BudgetControlSubjectVo>> subjectListMap = subjectList.stream().collect(Collectors.groupingBy(e -> e.getControlDetailCode()));

            if (CollectionUtils.isEmpty(list)) {
                for (Map.Entry<String, List<BudgetControlSubjectVo>> entry : subjectListMap.entrySet()) {
                    List<BudgetControlSubjectVo> v = entry.getValue();
                    List<String> subjectCodeList = v.stream().map(e -> e.getBudgetSubjectsCode()).collect(Collectors.toList());
                    List<String> subjectNameList = v.stream().map(e -> e.getBudgetSubjectsName()).collect(Collectors.toList());

                    //合并该预算科目下所有的子科目
                    Set<String> subjectCodeListAll = new HashSet<>();
                    subjectCodeList.forEach(e -> {
                        Set<String> subjectCodes = subjectMap.get(e);
                        if (CollectionUtils.isNotEmpty(subjectCodes)) {
                            subjectCodeListAll.addAll(subjectCodes);
                        }
                    });

                    List<CostBudgetVo> filterList = cbSubjectList.stream().filter(e -> subjectCodeListAll.contains(e.getBudgetSubjectCode())).collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(filterList)) {
                        continue;
                    }

                    BudgetTrack budgetTrack = buildBudgetTrack(controlVo, null, ym);
                    BigDecimal adjustAmount = BigDecimal.ZERO;
                    BigDecimal adjustBalanceAmount = BigDecimal.ZERO;
                    BigDecimal freezeAmount = BigDecimal.ZERO;
                    BigDecimal usedAmount = BigDecimal.ZERO;
                    BigDecimal monthBalanceAmount = BigDecimal.ZERO;
                    BigDecimal initialAmount = BigDecimal.ZERO;

                    List<String> budgetCodes = new ArrayList<>();
                    for (CostBudgetVo vo : filterList) {
                        adjustAmount = adjustAmount.add(Optional.ofNullable(vo.getAdjustAmount()).orElse(BigDecimal.ZERO));
                        adjustBalanceAmount = adjustBalanceAmount.add(Optional.ofNullable(vo.getAdjustBalanceAmount()).orElse(BigDecimal.ZERO));
                        freezeAmount = freezeAmount.add(Optional.ofNullable(vo.getFreezeAmount()).orElse(BigDecimal.ZERO));
                        usedAmount = usedAmount.add(Optional.ofNullable(vo.getUsedAmount()).orElse(BigDecimal.ZERO));
                        monthBalanceAmount = monthBalanceAmount.add(Optional.ofNullable(vo.getMonthBalanceAmount()).orElse(BigDecimal.ZERO));
                        initialAmount = initialAmount.add(Optional.ofNullable(vo.getInitialAmount()).orElse(BigDecimal.ZERO));
                        budgetCodes.add(vo.getCode());
                    }

                    budgetTrack.setBudgetSubjectCode(String.join(",", subjectCodeList));
                    budgetTrack.setBudgetSubjectName(String.join(",", subjectNameList));
                    budgetTrack.setThisInitialAmount(adjustBalanceAmount);
                    budgetTrack.setInitialAmount(initialAmount);
                    budgetTrack.setAdjustAmount(adjustAmount);
                    budgetTrack.setAdjustBalanceAmount(adjustBalanceAmount);
                    budgetTrack.setFreezeAmount(freezeAmount);
                    budgetTrack.setUsedAmount(usedAmount);
                    budgetTrack.setMonthBalanceAmount(monthBalanceAmount);
                    budgetTrack.setDimension((StringUtils.isNotEmpty(budgetTrack.getDimension()) ? budgetTrack.getDimension() : "") + "预算科目");
                    budgetTrack.setDimensionValue((StringUtils.isNotEmpty(budgetTrack.getDimensionValue()) ? budgetTrack.getDimensionValue() : "") +
                            budgetTrack.getBudgetSubjectCode());
                    budgetTrack.setBudgetCodes(budgetCodes);
                    listAll.add(budgetTrack);
                }
            } else {
                for (BudgetTrack bt : list) {
                    for (Map.Entry<String, List<BudgetControlSubjectVo>> entry : subjectListMap.entrySet()) {
                        List<BudgetControlSubjectVo> v = entry.getValue();
                        List<String> subjectCodeList = v.stream().map(e -> e.getBudgetSubjectsCode()).collect(Collectors.toList());
                        List<String> subjectNameList = v.stream().map(e -> e.getBudgetSubjectsName()).collect(Collectors.toList());

                        //合并该预算科目下所有的子科目
                        Set<String> subjectCodeListAll = new HashSet<>();
                        subjectCodeList.forEach(e -> {
                            Set<String> subjectCodes = subjectMap.get(e);
                            if (CollectionUtils.isNotEmpty(subjectCodes)) {
                                subjectCodeListAll.addAll(subjectCodes);
                            }
                        });

                        List<CostBudgetVo> filterList = cbSubjectList.stream().filter(e -> subjectCodeListAll.contains(e.getBudgetSubjectCode())).collect(Collectors.toList());

                        if (CollectionUtils.isEmpty(filterList)) {
                            continue;
                        }

                        BudgetTrack budgetTrack = buildBudgetTrack(controlVo, bt, ym);
                        BigDecimal adjustAmount = BigDecimal.ZERO;
                        BigDecimal adjustBalanceAmount = BigDecimal.ZERO;
                        BigDecimal freezeAmount = BigDecimal.ZERO;
                        BigDecimal usedAmount = BigDecimal.ZERO;
                        BigDecimal monthBalanceAmount = BigDecimal.ZERO;
                        BigDecimal initialAmount = BigDecimal.ZERO;

                        List<String> budgetCodes = new ArrayList<>();
                        List<String> codes = CollectionUtils.isEmpty(budgetTrack.getBudgetCodes()) ? new ArrayList<>() : budgetTrack.getBudgetCodes();
                        for (CostBudgetVo vo : filterList) {
                            if (CollectionUtils.isNotEmpty(codes)) {
                                if (codes.contains(vo.getCode())) {
                                    adjustAmount = adjustAmount.add(Optional.ofNullable(vo.getAdjustAmount()).orElse(BigDecimal.ZERO));
                                    adjustBalanceAmount = adjustBalanceAmount.add(Optional.ofNullable(vo.getAdjustBalanceAmount()).orElse(BigDecimal.ZERO));
                                    freezeAmount = freezeAmount.add(Optional.ofNullable(vo.getFreezeAmount()).orElse(BigDecimal.ZERO));
                                    usedAmount = usedAmount.add(Optional.ofNullable(vo.getUsedAmount()).orElse(BigDecimal.ZERO));
                                    monthBalanceAmount = monthBalanceAmount.add(Optional.ofNullable(vo.getMonthBalanceAmount()).orElse(BigDecimal.ZERO));
                                    initialAmount = initialAmount.add(Optional.ofNullable(vo.getInitialAmount()).orElse(BigDecimal.ZERO));
                                    budgetCodes.add(vo.getCode());
                                }
                            } else {
                                adjustAmount = adjustAmount.add(Optional.ofNullable(vo.getAdjustAmount()).orElse(BigDecimal.ZERO));
                                adjustBalanceAmount = adjustBalanceAmount.add(Optional.ofNullable(vo.getAdjustBalanceAmount()).orElse(BigDecimal.ZERO));
                                freezeAmount = freezeAmount.add(Optional.ofNullable(vo.getFreezeAmount()).orElse(BigDecimal.ZERO));
                                usedAmount = usedAmount.add(Optional.ofNullable(vo.getUsedAmount()).orElse(BigDecimal.ZERO));
                                monthBalanceAmount = monthBalanceAmount.add(Optional.ofNullable(vo.getMonthBalanceAmount()).orElse(BigDecimal.ZERO));
                                initialAmount = initialAmount.add(Optional.ofNullable(vo.getInitialAmount()).orElse(BigDecimal.ZERO));
                                budgetCodes.add(vo.getCode());
                            }
                        }

                        budgetTrack.setBudgetSubjectCode(String.join(",", subjectCodeList));
                        budgetTrack.setBudgetSubjectName(String.join(",", subjectNameList));
                        budgetTrack.setThisInitialAmount(adjustBalanceAmount);
                        budgetTrack.setInitialAmount(initialAmount);
                        budgetTrack.setAdjustAmount(adjustAmount);
                        budgetTrack.setAdjustBalanceAmount(adjustBalanceAmount);
                        budgetTrack.setFreezeAmount(freezeAmount);
                        budgetTrack.setUsedAmount(usedAmount);
                        budgetTrack.setMonthBalanceAmount(monthBalanceAmount);
                        budgetTrack.setDimension((StringUtils.isNotEmpty(budgetTrack.getDimension()) ? budgetTrack.getDimension() : "") + "预算科目");
                        budgetTrack.setDimensionValue((StringUtils.isNotEmpty(budgetTrack.getDimensionValue()) ? budgetTrack.getDimensionValue() : "") +
                                budgetTrack.getBudgetSubjectCode());
                        budgetTrack.setBudgetCodes(budgetCodes);
                        listAll.add(budgetTrack);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(listAll)) {
            return listAll;
        }
        return list;
    }

    /**
     * 构建预算追踪
     *
     * @param controlVo
     * @param bt
     * @return
     */
    private BudgetTrack buildBudgetTrack(BudgetControlVo controlVo, BudgetTrack bt, String ym) {
        BudgetTrack budgetTrack = new BudgetTrack();
        if (bt != null) {
            BeanUtils.copyProperties(bt, budgetTrack);
        } else {
            budgetTrack.setBudgetControlCode(controlVo.getControlCode());
            budgetTrack.setBudgetControlType(controlVo.getControlType());
            budgetTrack.setBudgetTimeDimension(controlVo.getTimeDimension());
            budgetTrack.setDepartmentOneCode(controlVo.getDepartmentCode());
            budgetTrack.setDepartmentOneName(controlVo.getDepartmentName());
            budgetTrack.setLevelNum(String.valueOf(controlVo.getLevelNum()));
            budgetTrack.setYearMonthLy(ym);
            budgetTrack.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            budgetTrack.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            budgetTrack.setTenantCode(TenantUtils.getTenantCode());
        }
        return budgetTrack;
    }

    /**
     * 获取有效年月期间所有的年月
     *
     * @param minDate
     * @param maxDate
     * @return
     * @throws ParseException
     */
    private static List<String> getMonthBetween(String minDate, String maxDate) {
        ArrayList<String> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        try {
            min.setTime(sdf.parse(minDate));
            min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

            max.setTime(sdf.parse(maxDate));
            max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
        } catch (ParseException e) {
            log.error("年月解析异常");
        }

        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }

        return result;
    }

}
