package com.biz.crm.tpm.business.budget.local.service.process;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.business.budget.local.entity.BudgetSubjects;
import com.biz.crm.tpm.business.budget.local.repository.BudgetSubjectsRepository;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsCrmImportVo;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-06-13 18:40
 * @description：预算科目导入
 */
@Component
@Slf4j
public class BudgetSubjectsImportProcess implements ImportProcess<BudgetSubjectsCrmImportVo> {

  @Autowired(required = false)
  private BudgetSubjectsVoService budgetSubjectsVoService;

  @Autowired(required = false)
  private DictDataVoService dictDataVoService;

  @Resource
  private BudgetSubjectsRepository budgetSubjectsRepository;

  private final static String COOPERATE_TYPE = "cooperate_type";
  private final static String BUDGET_SUBJECT_LEVEL = "budget_subject_level";

  @Override
  public Integer getBatchCount() {
    return Integer.MAX_VALUE;
  }

  /**
   * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存)
   * 新逻辑需同时实现接口 tryVerify tryConfirm
   *
   * @return
   */
  @Override
  public boolean importBeforeValidationFlag() {
    return true;
  }

  /**
   * 数据校验
   *
   * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
   * @param paramsVo 任务公共参数
   * @param params   导入任务自定义参数
   * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
   */
  @Override
  public Map<Integer, String> tryVerify(LinkedHashMap<Integer, BudgetSubjectsCrmImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    Map<Integer, String> errMap = new HashMap<>();

    for (Map.Entry<Integer, BudgetSubjectsCrmImportVo> row : data.entrySet()) {
      int rowNum = row.getKey();

      BudgetSubjectsCrmImportVo vo = row.getValue();
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getBudgetSubjectsCode()), "预算科目编码不能为空！");
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getBudgetSubjectsName()), "预算科目名称不能为空！");
      this.validateIsTrue(StringUtils.isNotEmpty(vo.getLevel()), "科目层级不能为空！");
      String errInfo = this.validateGetErrorInfo();
      if (errInfo != null) {
        errMap.put(rowNum, errInfo);
      }
    }

    //校验数据库中是否存在重复数据
    Set<String> codeSet = data.values().stream().map(BudgetSubjectsCrmImportVo::getBudgetSubjectsCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    Set<String> nameSet = data.values().stream().map(BudgetSubjectsCrmImportVo::getBudgetSubjectsName).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    Set<String> parentCodeSet = data.values().stream().map(BudgetSubjectsCrmImportVo::getParentBudgetSubjectsCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

    List<BudgetSubjects> codeExistList = budgetSubjectsRepository.findByCodesOrNames(Lists.newArrayList(codeSet), null);
    List<String> existCodes = codeExistList.stream().map(BudgetSubjects::getBudgetSubjectsCode).collect(Collectors.toList());
    List<BudgetSubjects> nameExistList = budgetSubjectsRepository.findByCodesOrNames(null, Lists.newArrayList(nameSet));
    List<String> existNames = nameExistList.stream().map(BudgetSubjects::getBudgetSubjectsName).collect(Collectors.toList());

    //上级编码
    List<BudgetSubjects> parentList = budgetSubjectsRepository.findByCodesOrNames(Lists.newArrayList(parentCodeSet), null);
    Map<String, BudgetSubjects> parentMap = parentList.stream().collect(Collectors.toMap(BudgetSubjects::getBudgetSubjectsCode, Function.identity(), (v1, v2) -> v1));

    if (CollectionUtils.isNotEmpty(existCodes) || CollectionUtils.isEmpty(existNames)) {
      for (Map.Entry<Integer, BudgetSubjectsCrmImportVo> row : data.entrySet()) {
        int rowNum = row.getKey();

        BudgetSubjectsCrmImportVo vo = row.getValue();
        this.validateIsTrue(!existCodes.contains(vo.getBudgetSubjectsCode()), "预算科目编码已在系统中存在！");
        this.validateIsTrue(!existNames.contains(vo.getBudgetSubjectsName()), "预算科目名称已在系统中存在！");
        if (StringUtils.isNotBlank(vo.getParentBudgetSubjectsCode())) {
          this.validateIsTrue(parentMap.containsKey(vo.getParentBudgetSubjectsCode()), "上级预算科目编码在系统中不存在！");
        }

        String errInfo = this.validateGetErrorInfo();
        if (errInfo != null) {
          errMap.put(rowNum, (errMap.get(rowNum) != null ? (errMap.get(rowNum) + errInfo) : errInfo));
        }
      }
    }
    return errMap;
  }

  /**
   * 数据存储
   *
   * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
   * @param paramsVo 任务公共参数
   * @param params   导入任务自定义参数
   * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
   */
  @Override
  public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, BudgetSubjectsCrmImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    Map<Integer, String> errMap = new HashMap<>();

    // 查询数据字典
    Map<String, List<DictDataVo>> dictDataMap = dictDataVoService.findByDictTypeCodeList(Arrays.asList(COOPERATE_TYPE, BUDGET_SUBJECT_LEVEL));

    //校验数据库中是否存在重复数据
    Set<String> codeSet = data.values().stream().map(BudgetSubjectsCrmImportVo::getBudgetSubjectsCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    Set<String> nameSet = data.values().stream().map(BudgetSubjectsCrmImportVo::getBudgetSubjectsName).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    Set<String> parentCodeSet = data.values().stream().map(BudgetSubjectsCrmImportVo::getParentBudgetSubjectsCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

    List<BudgetSubjects> codeExistList = budgetSubjectsRepository.findByCodesOrNames(Lists.newArrayList(codeSet), null);
    List<String> existCodes = codeExistList.stream().map(BudgetSubjects::getBudgetSubjectsCode).collect(Collectors.toList());
    List<BudgetSubjects> nameExistList = budgetSubjectsRepository.findByCodesOrNames(null, Lists.newArrayList(nameSet));
    List<String> existNames = nameExistList.stream().map(BudgetSubjects::getBudgetSubjectsName).collect(Collectors.toList());

    //上级编码
    List<BudgetSubjects> parentList = budgetSubjectsRepository.findByCodesOrNames(Lists.newArrayList(parentCodeSet), null);
    Map<String, BudgetSubjects> parentMap = parentList.stream().collect(Collectors.toMap(BudgetSubjects::getBudgetSubjectsCode, Function.identity(), (v1, v2) -> v1));

    for (Map.Entry<Integer, BudgetSubjectsCrmImportVo> row : data.entrySet()) {
      int rowNum = row.getKey();

      BudgetSubjectsCrmImportVo vo = row.getValue();

      //合作类型
      if (StringUtils.isNotBlank(vo.getCooperateType())) {
        DictDataVo cooperateType = dictDataMap.get(COOPERATE_TYPE).stream().filter(e -> e.getDictValue().equals(vo.getCooperateType())).findFirst().orElse(null);
        if (cooperateType == null) {
          this.validateIsTrue(false, "合作类型错误！");
        } else {
          vo.setCooperateType(cooperateType.getDictCode());
        }
      }
      //层级
      DictDataVo level = dictDataMap.get(BUDGET_SUBJECT_LEVEL).stream().filter(e -> e.getDictValue().equals(vo.getLevel())).findFirst().orElse(null);
      if (level == null) {
        this.validateIsTrue(false, "科目层级错误！");
      } else {
        vo.setLevel(level.getDictCode());
      }
      if (existCodes.contains(vo.getBudgetSubjectsCode())) {
        this.validateIsTrue(false, "预算科目编码重复！");
      }
      if (existNames.contains(vo.getBudgetSubjectsName())) {
        this.validateIsTrue(false, "预算科目名称重复！");
      }
      if (StringUtils.isNotBlank(vo.getParentBudgetSubjectsCode())) {
        if (parentMap.containsKey(vo.getParentBudgetSubjectsCode())) {
          vo.setParentBudgetSubjectsName(parentMap.get(vo.getParentBudgetSubjectsCode()).getBudgetSubjectsName());
        } else {
          this.validateIsTrue(false, "上级预算科目编码在系统中不存在！");
        }
      }
      String errInfo = this.validateGetErrorInfo();
      if (errInfo != null) {
        errMap.put(rowNum, errInfo);
      }
    }

    if (MapUtils.isNotEmpty(errMap)) {
      return errMap;
    }
    this.budgetSubjectsVoService.createBatch(new ArrayList<>(data.values()));
    return null;
  }

  /**
   * 数据处理
   *
   * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
   * @param paramsVo 任务公共参数
   * @param params   导入任务自定义参数
   * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
   */
  @Override
  public Map<Integer, String> execute(LinkedHashMap<Integer, BudgetSubjectsCrmImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    return null;
  }

  /**
   * 获取数据实体
   *
   * @return
   */
  @Override
  public Class<BudgetSubjectsCrmImportVo> findCrmExcelVoClass() {
    return BudgetSubjectsCrmImportVo.class;
  }

  /**
   * 获取业务对应的模板编码，全局唯一
   *
   * @return
   */
  @Override
  public String getTemplateCode() {
    return "BUDGET_SUBJECTS_IMPORT";
  }

  /**
   * 获取业务对应的模板描述
   *
   * @return
   */
  @Override
  public String getTemplateName() {
    return "预算科目导入";
  }
}
