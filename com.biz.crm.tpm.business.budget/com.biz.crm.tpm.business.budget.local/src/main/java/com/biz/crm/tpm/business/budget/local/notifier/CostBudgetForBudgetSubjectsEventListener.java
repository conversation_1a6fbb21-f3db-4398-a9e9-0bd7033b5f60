package com.biz.crm.tpm.business.budget.local.notifier;

import com.biz.crm.tpm.business.budget.local.entity.CostBudget;
import com.biz.crm.tpm.business.budget.local.repository.CostBudgetRepository;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.event.BudgetSubjectsEventListener;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class CostBudgetForBudgetSubjectsEventListener implements BudgetSubjectsEventListener {

  @Autowired
  private CostBudgetRepository costBudgetRepository;

  @Override
  public void onCreated(BudgetSubjectsVo budgetSubjectsVo) {

  }

  @Override
  public void onUpdate(BudgetSubjectsVo oldBudgetSubjectsVo, BudgetSubjectsVo budgetSubjectsVo) {
    //当预算科目名称变化时，同步更新费用预算中的预算科目名称
    if(!StringUtils.equals(oldBudgetSubjectsVo.getBudgetSubjectsName(),budgetSubjectsVo.getBudgetSubjectsName())){
      CostBudgetDto costBudgetDto = new CostBudgetDto();
      costBudgetDto.setTenantCode(budgetSubjectsVo.getTenantCode());
      costBudgetDto.setBudgetSubjectCode(budgetSubjectsVo.getBudgetSubjectsCode());
      List<CostBudget> costBudgets = costBudgetRepository.findByConditions(costBudgetDto);
      if(!CollectionUtils.isEmpty(costBudgets)){
        costBudgets.forEach(e -> e.setBudgetSubjectName(budgetSubjectsVo.getBudgetSubjectsName()));
        costBudgetRepository.saveOrUpdateBatch(costBudgets);
      }
    }
  }

  @Override
  public void onDeleted(BudgetSubjectsVo budgetSubjectsVo) {
    CostBudgetDto costBudgetDto = new CostBudgetDto();
    costBudgetDto.setTenantCode(budgetSubjectsVo.getTenantCode());
    costBudgetDto.setBudgetSubjectCode(budgetSubjectsVo.getBudgetSubjectsCode());
    List<CostBudget> costBudgets = costBudgetRepository.findByConditions(costBudgetDto);
    Validate.isTrue(CollectionUtils.isEmpty(costBudgets),"预算科目【%s】已有费用预算数据，不能删除",budgetSubjectsVo.getBudgetSubjectsCode());
  }

  @Override
  public void onEnable(BudgetSubjectsVo budgetSubjectsVo) {

  }

  @Override
  public void onDisable(BudgetSubjectsVo budgetSubjectsVo) {

  }
}
