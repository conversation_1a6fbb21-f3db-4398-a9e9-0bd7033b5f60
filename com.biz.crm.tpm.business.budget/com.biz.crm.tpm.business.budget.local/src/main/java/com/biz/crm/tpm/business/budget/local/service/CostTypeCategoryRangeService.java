package com.biz.crm.tpm.business.budget.local.service;

import com.biz.crm.tpm.business.budget.local.entity.CostTypeCategoryRange;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryRangeVo;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * TPM-活动大类范围;(tpm_cost_type_category_range)表服务接口
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
public interface CostTypeCategoryRangeService {
  /**
   * 通过活动大类编号查询单条数据
   *
   * @param categoryCode 活动大类编号
   * @return 单条数据
   */
  List<CostTypeCategoryRangeVo> findByCategoryCode(String categoryCode);

  /**
   * 批量保存
   *
   * @param costTypeCategoryRanges
   */
  void saveBatch(Collection<CostTypeCategoryRange> costTypeCategoryRanges);

  /**
   * 通过活动大类编号删除关联范围数据
   *
   * @param categoryCode
   */
  void deleteByCategoryCode(String categoryCode);

  /**
   * 通过活动大类编号集合删除关联范围数据
   *
   * @param rangeCodes
   */
  void deleteByRangeCodes(Set<String> rangeCodes);

  /**
   * 启禁用
   */
  void updateEnableStatus(Set<String> codes, String enableStatus);

  /**
   * 修改组织名称
   */
  void updateName(String code, String name);

  /**
   * 查询全部
   * @return
   */
  Set<CostTypeCategoryRange> findAll();
}