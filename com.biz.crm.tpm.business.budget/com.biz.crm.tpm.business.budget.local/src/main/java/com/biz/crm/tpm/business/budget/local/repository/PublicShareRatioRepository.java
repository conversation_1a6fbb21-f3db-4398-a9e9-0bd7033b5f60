package com.biz.crm.tpm.business.budget.local.repository;


import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.budget.local.entity.PublicShareRatio;
import com.biz.crm.tpm.business.budget.local.mapper.PublicShareRatioMapper;
import com.biz.crm.tpm.business.budget.sdk.vo.PublicShareRatioVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;


/**
 * 公摊费率(PublicShareRatio)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-17 17:26:22
 */
@Component
public class PublicShareRatioRepository extends ServiceImpl<PublicShareRatioMapper, PublicShareRatio> {

    @Autowired
    private PublicShareRatioMapper publicShareRatioMapper;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 按唯一键查询记录条数
     *
     * @param codes
     * @return
     */
    public List<PublicShareRatioVo> findByUniqueKeys(Set<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }
        List<PublicShareRatio> list = lambdaQuery().in(PublicShareRatio::getUniqueKey, codes)
                .list();
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() :
                new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, PublicShareRatio.class, PublicShareRatioVo.class, LinkedHashSet.class, ArrayList.class));
    }

    public List<PublicShareRatio> findRatioByCondition(String year, String month, List<String> orgCodes) {
        return this.lambdaQuery()
                .eq(PublicShareRatio::getYearStr, year)
                .eq(PublicShareRatio::getMonthStr, month)
                .in(PublicShareRatio::getDepartmentCode, orgCodes)
                .eq(PublicShareRatio::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(PublicShareRatio::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }
}

