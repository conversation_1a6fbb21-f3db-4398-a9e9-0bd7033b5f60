package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

import static org.apache.ibatis.type.JdbcType.VARCHAR;

/**
 * 实体：TPM-活动大类范围;
 *
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
@ApiModel(value = "CostTypeCategoryRange", description = "TPM-活动大类范围")
@TableName("tpm_cost_type_category_range")
@Getter
@Setter
@Entity(name = "tpm_cost_type_category_range")
@Table(name = "tpm_cost_type_category_range", indexes = {@Index(name = "tpm_cost_type_category_range_index1", columnList = "tenant_code")})
@org.hibernate.annotations.Table(appliesTo = "tpm_cost_type_category_range", comment = "TPM-活动大类范围")
@Deprecated
public class CostTypeCategoryRange extends TenantEntity {

  /**
   * 活动大类编号
   */
  @ApiModelProperty(name = "活动大类编号", notes = "")
  @Column(name = "category_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动大类编号 '")
  private String categoryCode;

  /**
   * 活动大类名称
   */
  @ApiModelProperty(name = "活动大类名称", notes = "")
  @Column(name = "category_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动大类名称 '")
  private String categoryName;

  /**
   * 范围类型(1,组织,2,组织类型)
   */
  @ApiModelProperty(name = "范围类型(1,组织,2,组织类型)", notes = "")
  @Column(name = "range_type", nullable = false, columnDefinition = "INT COMMENT '范围类型(1,组织,2,组织类型) '")
  private Integer rangeType;

  /**
   * 范围编码
   */
  @ApiModelProperty(name = "范围编码", notes = "")
  @Column(name = "range_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '范围编码 '")
  private String rangeCode;

  /**
   * 范围名称
   */
  @ApiModelProperty(name = "范围名称", notes = "")
  @Column(name = "range_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '范围名称 '")
  private String rangeName;

  /**
   * 数据业务状态（启用状态）
   */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY, jdbcType = VARCHAR)
  @Column(name = "enable_status", nullable = true, length = 10, columnDefinition = "varchar(10) COMMENT '数据业务状态（启用状态）'")
  private String enableStatus;

  /**
   * 数据状态（删除状态）
   */
  @TableField(fill = FieldFill.INSERT,updateStrategy = FieldStrategy.NOT_EMPTY, jdbcType = VARCHAR)
  @Column(name = "del_flag", nullable = true, length = 10, columnDefinition = "varchar(10) COMMENT '数据状态（删除状态）'")
  private String delFlag;
}