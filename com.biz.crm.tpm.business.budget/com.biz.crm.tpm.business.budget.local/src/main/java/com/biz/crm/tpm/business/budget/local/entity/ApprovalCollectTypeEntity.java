package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_approval_collect_type")
@Table(name = "tpm_approval_collect_type", indexes = {@Index(name = "approval_collect_type_idx1", columnList = "collect_code")})
@ApiModel(value = "ApprovalCollectType", description = "核销采集类型信息")
@org.hibernate.annotations.Table(appliesTo = "tpm_approval_collect_type", comment = "核销采集类型信息")
public class ApprovalCollectTypeEntity extends TenantEntity {

  @ApiModelProperty("采集编码")
  @Column(name = "collect_code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '采集编码'")
  private String collectCode;


  @ApiModelProperty("类型编码")
  @Column(name = "type_code", length = 64, columnDefinition = "varchar(64) COMMENT '类型编码'")
  private String typeCode;


  @ApiModelProperty("类型名称")
  @Column(name = "type_name", length = 128, columnDefinition = "varchar(128) COMMENT '类型名称'")
  private String typeName;
}
