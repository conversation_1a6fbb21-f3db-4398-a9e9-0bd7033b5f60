package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.budget.local.entity.StrategySettingManage;
import com.biz.crm.tpm.business.budget.local.mapper.StrategySettingManageMapper;
import com.biz.crm.tpm.business.budget.sdk.dto.StrategySettingManageDto;
import com.biz.crm.tpm.business.budget.sdk.vo.StrategySettingManageVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 活动细类策略配置(StrategySettingManage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
@Deprecated
public class StrategySettingManageRepository extends ServiceImpl<StrategySettingManageMapper, StrategySettingManage> {

  @Autowired
  private StrategySettingManageMapper strategySettingManageMapper;

  /**
   * 分页查询数据
   *
   * @param pageable   分页对象
   * @param dto 实体对象
   */
  public Page<StrategySettingManageVo> findByConditions(Pageable pageable, StrategySettingManageDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<StrategySettingManageVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return strategySettingManageMapper.findByConditions(page, dto);
  }

  /**
   * 根据编码进行查询
   */
  public StrategySettingManage findByCodeAndTenantCode(String code, String tenantCode){
    return this.lambdaQuery().eq(StrategySettingManage::getCode,code).
            eq(StrategySettingManage::getTenantCode,tenantCode).one();
  }

  /**
   * 查询正在使用的策略配置
   */
  public StrategySettingManage findEnabled(){
    return this.lambdaQuery()
        .eq(StrategySettingManage::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
        .eq(StrategySettingManage::getTenantCode,TenantUtils.getTenantCode())
        .one();
  }

  /**
   * 根据主键id集合查询信息
   */
  public List<StrategySettingManage> findByIds(Set<String> ids){
    return this.lambdaQuery()
        .in(StrategySettingManage::getId,ids)
        .eq(StrategySettingManage::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }

  /**
   * 根据名称，判断是否存在相应信息
   */
  public boolean existByName(String name){
    return this.lambdaQuery()
        .eq(StrategySettingManage::getName,name)
        .eq(StrategySettingManage::getTenantCode,TenantUtils.getTenantCode())
        .count() != 0;
  }

  /**
   * 根据名称，查询相应信息
   */
  public StrategySettingManage findByName(String name){
    return this.lambdaQuery()
        .eq(StrategySettingManage::getName,name)
        .eq(StrategySettingManage::getTenantCode,TenantUtils.getTenantCode())
        .one();
  }

  public StrategySettingManage findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(StrategySettingManage::getTenantCode,tenantCode)
        .in(StrategySettingManage::getId,id)
        .one();
  }
}

