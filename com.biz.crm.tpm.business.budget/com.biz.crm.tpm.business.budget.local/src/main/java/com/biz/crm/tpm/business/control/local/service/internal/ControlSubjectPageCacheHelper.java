package com.biz.crm.tpm.business.control.local.service.internal;

import com.biz.crm.business.common.page.cache.service.BusinessPageCacheHelper;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.control.local.repository.BudgetControlSubjectRepository;
import com.biz.crm.tpm.business.control.sdk.constant.BudgetControlConstant;
import com.biz.crm.tpm.business.control.sdk.dto.BudgetControlSubjectDto;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlSubjectVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分页缓存
 */
@Slf4j
@Component
public class ControlSubjectPageCacheHelper extends BusinessPageCacheHelper<BudgetControlSubjectVo, BudgetControlSubjectDto> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private BudgetControlSubjectRepository budgetControlSubjectRepository;

    /**
     * 缓存键前缀
     */
    @Override
    public String getCacheKeyPrefix() {
        return BudgetControlConstant.CACHE_KEY_SUBJECT_PREFIX;
    }

    /**
     * @return Dto Class对象
     */
    @Override
    public Class<BudgetControlSubjectDto> getDtoClass() {
        return BudgetControlSubjectDto.class;
    }

    /**
     * @return Vo Class对象
     */
    @Override
    public Class<BudgetControlSubjectVo> getVoClass() {
        return BudgetControlSubjectVo.class;
    }

    /**
     * 从数据库查询初始化数据
     *
     * @param dto
     * @param cacheKey
     */
    @Override
    public List<BudgetControlSubjectDto> findDtoListFromRepository(BudgetControlSubjectDto dto, String cacheKey) {
        if (StringUtils.isBlank(dto.getControlCode())) {
            return new ArrayList<>();
        }
        //相同明细编码列转行
        List<BudgetControlSubjectVo> vos = budgetControlSubjectRepository.findByCode(dto.getControlCode());
        if (CollectionUtils.isEmpty(vos)) {
            return new ArrayList<>();
        }
        Map<String, List<BudgetControlSubjectVo>> subjectMap = vos.stream().collect(Collectors.groupingBy(BudgetControlSubjectVo::getControlDetailCode));
        List<BudgetControlSubjectVo> subjectVoList = new ArrayList<>();
        subjectMap.forEach((k, v) -> {
            BudgetControlSubjectVo subjectVo = new BudgetControlSubjectVo();
            BeanUtils.copyProperties(v.get(0), subjectVo);
            if (v.size() > 1) {
                StringBuilder sbCode = new StringBuilder();
                StringBuilder sbName = new StringBuilder();
                v.forEach(e -> {
                    sbCode.append(e.getBudgetSubjectsCode()).append(",");
                    sbName.append(e.getBudgetSubjectsName()).append(",");
                });
                subjectVo.setBudgetSubjectsCode(sbCode.substring(0, sbCode.length() - 1));
                subjectVo.setBudgetSubjectsName(sbName.substring(0, sbName.length() - 1));
            }
            subjectVoList.add(subjectVo);
        });
        return (List<BudgetControlSubjectDto>) nebulaToolkitService.copyCollectionByBlankList(subjectVoList, BudgetControlSubjectVo.class, BudgetControlSubjectDto.class, HashSet.class, ArrayList.class);
    }

    /**
     * 新增数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlSubjectDto> newItem(String cacheKey, List<BudgetControlSubjectDto> itemList) {
        itemList.forEach(newItem -> {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        });
        return itemList;
    }

    /**
     * 复制数据
     *
     * @param cacheKey
     * @param itemList
     */
    @Override
    public List<BudgetControlSubjectDto> copyItem(String cacheKey, List<BudgetControlSubjectDto> itemList) {
        List<BudgetControlSubjectDto> newItemList = (List<BudgetControlSubjectDto>) nebulaToolkitService.copyCollectionByBlankList(itemList, BudgetControlSubjectDto.class, BudgetControlSubjectDto.class, HashSet.class, ArrayList.class);
        for (BudgetControlSubjectDto newItem : newItemList) {
            newItem.setId(UUID.randomUUID().toString().replace("-", ""));
            newItem.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newItem.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return newItemList;
    }

    /**
     * 获取Dto中的主键
     *
     * @param dto
     * @return 主键
     */
    @Override
    public Object getDtoKey(BudgetControlSubjectDto dto) {
        return dto.getId();
    }

    /**
     * 获取是否选中状态
     *
     * @param dto
     * @return 主键
     */
    @Override
    public String getCheckedStatus(BudgetControlSubjectDto dto) {
        return dto.getChecked();
    }

}
