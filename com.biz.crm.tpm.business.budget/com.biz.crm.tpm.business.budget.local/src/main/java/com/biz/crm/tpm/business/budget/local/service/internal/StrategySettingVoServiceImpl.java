package com.biz.crm.tpm.business.budget.local.service.internal;

import com.alibaba.fastjson.JSONArray;
import com.biz.crm.tpm.business.budget.local.entity.StrategySetting;
import com.biz.crm.tpm.business.budget.local.repository.StrategySettingRepository;
import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType;
import com.biz.crm.tpm.business.budget.sdk.service.StrategySettingVoService;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.AbstractStrategySetting;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.ControlActivityExpensesSetting;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.SignDisplaySetting;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStructAnalysis;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class StrategySettingVoServiceImpl implements StrategySettingVoService {

  @Autowired
  private StrategySettingRepository strategySettingRepository;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;
  @Autowired
  private StrategySettingStructAnalysis strategySettingStructAnalysis;
  @Autowired(required = false)
  private List<AbstractStrategySetting> abstractStrategySettings;

  @Override
  public List<StrategySettingStruct> findBySettingManageCode(String settingManageCode) {
    if(StringUtils.isBlank(settingManageCode)){
      return Lists.newArrayList();
    }
    List<StrategySetting> items = strategySettingRepository.findBySettingManageCode(settingManageCode);
    if(CollectionUtils.isEmpty(items)){
      return Lists.newArrayList();
    }

    List<StrategySettingStruct> result = Lists.newArrayList();
    for(StrategySetting setting : items){
      StrategySettingStruct struct = new StrategySettingStruct();
      struct.setId(setting.getId());
      struct.setCode(setting.getCode());
      struct.setDefaultValue(StringUtils.isBlank(setting.getDefaultValue()) ? null : this.transferDefaultValue(setting));
      struct.setDateFormat(setting.getDateFormat());
      struct.setName(setting.getName());
      struct.setNecessary(setting.getNecessary());
      struct.setParentCode(setting.getParentCode());
      struct.setSortIndex(setting.getSortIndex());
      struct.setTip(setting.getTip());
      struct.setType(setting.getType());
      struct.setValueType(setting.getValueType());
      struct.setDisplay(setting.getDisplay());
      struct.setSettingManageCode(setting.getSettingManageCode());
      result.add(struct);
    }
    return result;
  }

  @Override
  @Transactional
  public void create(List<StrategySettingStruct> items) {
    List<StrategySettingStruct> datas = this.createValidation(items);
    List<StrategySetting> entities = Lists.newArrayList();
    for(StrategySettingStruct data : datas){
      StrategySetting item = new StrategySetting();
      item.setSettingManageCode(data.getSettingManageCode());
      item.setParentCode(data.getParentCode());
      item.setCode(data.getCode());
      item.setName(data.getName());
      item.setDisplay(data.getDisplay());
      item.setTip(data.getTip());
      item.setDefaultValue(data.getDefaultValue() == null ? null : data.getDefaultValue().toString());
      item.setDateFormat(data.getDateFormat());
      item.setNecessary(data.getNecessary());
      item.setSortIndex(data.getSortIndex());
      item.setType(data.getType());
      item.setValueType(data.getValueType());
      entities.add(item);
    }
    StrategySettingStruct controlActivityExpensesSetting = datas.stream().filter(e -> StringUtils.equals(e.getCode(), ControlActivityExpensesSetting.class.getSimpleName())).findFirst().orElse(null);
    strategySettingStructAnalysis.validateDisplaySpecialForStruct(controlActivityExpensesSetting,datas);
    strategySettingRepository.saveBatch(entities);
    datas.forEach(e -> entities.stream().filter(entity -> StringUtils.equals(entity.getCode(), e.getCode())).findFirst().ifPresent(data -> e.setId(data.getId())));
  }

  @Override
  @Transactional
  public void update(List<StrategySettingStruct> items) {
    List<StrategySettingStruct> datas = this.updateValidation(items);
    Set<String> settingManageCodes = datas.stream().map(StrategySettingStruct::getSettingManageCode).collect(Collectors.toSet());
    Validate.notEmpty(settingManageCodes,"更新时，策略配置编码不能为空");
    Validate.isTrue(settingManageCodes.size() == 1,"更新时，策略配置编码不一致，请检查");
    StrategySettingStruct controlActivityExpensesSetting = datas.stream().filter(e -> StringUtils.equals(e.getCode(), ControlActivityExpensesSetting.class.getSimpleName())).findFirst().orElse(null);
    strategySettingStructAnalysis.validateDisplaySpecialForStruct(controlActivityExpensesSetting,datas);
    List<StrategySetting> dbItems = strategySettingRepository.findBySettingManageCode(settingManageCodes.iterator().next());
    Set<String> dbIds = dbItems.stream().map(StrategySetting::getId).collect(Collectors.toSet());
    Set<String> ids = datas.stream().map(StrategySettingStruct::getId).collect(Collectors.toSet());
    //删除时
    Set<String> needDeletes = Sets.difference(dbIds,ids);
    if(!CollectionUtils.isEmpty(needDeletes)){
      //实体对象无租户编号
      strategySettingRepository.removeByIds(needDeletes);
    }

    //更新时
    Set<String> needUpdates = Sets.intersection(dbIds,ids);
    if(!CollectionUtils.isEmpty(needUpdates)){
      List<StrategySetting> currentDbItems = dbItems.stream().filter(e -> needUpdates.contains(e.getId())).collect(Collectors.toList());
      List<StrategySettingStruct> currentItemDtos = datas.stream().filter(e -> needUpdates.contains(e.getId())).collect(Collectors.toList());
      for(StrategySetting item : currentDbItems){
        for(StrategySettingStruct dto : currentItemDtos){
          if(StringUtils.equals(item.getId(),dto.getId())){
            item.setCode(dto.getCode());
            item.setName(dto.getName());
            item.setDisplay(dto.getDisplay());
            item.setTip(dto.getTip());
            item.setDefaultValue(dto.getDefaultValue() == null ? null : dto.getDefaultValue().toString());
            item.setDateFormat(dto.getDateFormat());
            item.setNecessary(dto.getNecessary());
            item.setSortIndex(dto.getSortIndex());
            item.setType(dto.getType());
            item.setValueType(dto.getValueType());
            break;
          }
        }
      }
      strategySettingRepository.saveOrUpdateBatch(currentDbItems);
    }

    //新增时
    List<StrategySettingStruct> needAdds = items.stream().filter(e -> StringUtils.isBlank(e.getId())).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(needAdds)){
      this.create(needAdds);
    }
  }

  private List<StrategySettingStruct> createValidation(List<StrategySettingStruct> items){
    List<StrategySettingStruct> flatItems = this.validateBase(items);
    for(StrategySettingStruct item : flatItems){
      Validate.isTrue(StringUtils.isBlank(item.getId()),"新增时，策略项主键id不能有值");
      Validate.notBlank(item.getSettingManageCode(),"策略配置编码不能为空");
      if(item.getDisplay() == null){
        item.setDisplay(Boolean.FALSE);
      }
    }
    return flatItems;
  }

  private List<StrategySettingStruct> updateValidation(List<StrategySettingStruct> items){
    return this.validateBase(items);
  }

  private void buildFlatItems(StrategySettingStruct struct, List<StrategySettingStruct> flatItems){
    flatItems.add(struct);
    if(CollectionUtils.isEmpty(struct.getChildren())){
      return;
    }
    for(StrategySettingStruct child : struct.getChildren()){
      this.buildFlatItems(child,flatItems);
    }
  }

  private List<StrategySettingStruct> validateBase(List<StrategySettingStruct> items){
    Validate.notEmpty(items,"策略项信息不能为空");
    Validate.notEmpty(abstractStrategySettings,"策略项配置策略不能为空");
    List<StrategySettingStruct> tops = items.stream().filter(e -> StringUtils.isBlank(e.getParentCode())).collect(Collectors.toList());
    Validate.notEmpty(tops,"不存在策略项根节点信息，请检查");
    List<StrategySettingStruct> flatItems = Lists.newArrayList();
    for(StrategySettingStruct top : tops){
      String code = top.getCode();
      Validate.notBlank(code,"策略项编码不能为空");
      AbstractStrategySetting abstractStrategySetting = abstractStrategySettings.stream().filter(e -> StringUtils.equals(e.getCode(),code)).findFirst().orElse(null);
      Validate.notNull(abstractStrategySetting,"根据策略项编码【%s】，未能获取到相应策略",code);
      strategySettingStructAnalysis.validateBaseStruct(top,abstractStrategySetting);
      this.buildFlatItems(top,flatItems);
    }
    return flatItems;
  }

  private Object transferDefaultValue(StrategySetting setting){
    StrategySettingValueType valueType = StrategySettingValueType.findByCode(setting.getValueType());
    Validate.notNull(valueType,"未知的策略项值类型【%s】，请检查",setting.getValueType());
    switch (valueType){
      case STRING:
      case OBJECT:
      case DATE:
      case ARRAY:
        return setting.getDefaultValue();
      case NUMBER:
        return NumberUtils.createNumber(setting.getDefaultValue());
      case BOOLEAN:
        return Boolean.valueOf(setting.getDefaultValue());
      default:
        throw new IllegalArgumentException("未知的策略项值类型，请检查");
    }
  }
}
