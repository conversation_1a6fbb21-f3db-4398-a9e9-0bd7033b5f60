package com.biz.crm.tpm.business.adjust.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.adjust.local.entity.BudgetAdjustDetail;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDetailDto;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustDetailVo;
import org.apache.ibatis.annotations.Param;

/**
 * 预算调整明细(BudgetAdjustDetail)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-05-23 14:49:55
 */
public interface BudgetAdjustDetailMapper extends BaseMapper<BudgetAdjustDetail> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页对象
   * @param dto 查询实体
   * @return 所有数据
  */
  Page<BudgetAdjustDetailVo> findByConditions(@Param("page") Page<BudgetAdjustDetailVo> page, @Param("dto") BudgetAdjustDetailDto dto);
}

