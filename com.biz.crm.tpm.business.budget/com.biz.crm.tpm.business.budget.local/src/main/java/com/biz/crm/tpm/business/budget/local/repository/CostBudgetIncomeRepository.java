package com.biz.crm.tpm.business.budget.local.repository;


import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.budget.local.entity.CostBudgetIncome;
import com.biz.crm.tpm.business.budget.local.mapper.CostBudgetIncomeMapper;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetIncomeDto;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;


/**
 * 收入预算(CostBudgetIncome)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-27 14:38:52
 */
@Component
public class CostBudgetIncomeRepository extends ServiceImpl<CostBudgetIncomeMapper, CostBudgetIncome> {

    @Autowired
    private CostBudgetIncomeMapper costBudgetIncomeMapper;

    /**
     * 按条件查询
     *
     * @param dto
     * @return
     */
    public List<CostBudgetIncomeVo> findByCondition(CostBudgetIncomeDto dto) {
        return costBudgetIncomeMapper.findByCondition(dto);
    }

    /**
     * 根据编码查询信息
     */
    public List<CostBudgetIncome> findByCodeAndTenantCode(Set<String> codes, String tenantCode) {
        return this.lambdaQuery().in(CostBudgetIncome::getIncomeCode, codes).eq(CostBudgetIncome::getTenantCode, tenantCode).eq(CostBudgetIncome::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }

    /**
     * 启禁用
     */
    public void updateEnableStatus(Set<String> ids, String enableStatus) {
        this.lambdaUpdate()
                .in(CostBudgetIncome::getId, ids)
                .eq(CostBudgetIncome::getTenantCode, TenantUtils.getTenantCode())
                .set(CostBudgetIncome::getEnableStatus, enableStatus).update();
    }

    public List<CostBudgetIncome> findListByCustomerCodesAndOrgCodesAndYears(List<String> customerCodes, List<String> costCenterCodes, List<String> deptCodes, String years) {
        return this.lambdaQuery()
                .in(CostBudgetIncome::getCustomerCode, customerCodes)
                .in(CollectionUtils.isNotEmpty(costCenterCodes), CostBudgetIncome::getCostCenterCode, costCenterCodes)
                .in(CollectionUtils.isNotEmpty(deptCodes), CostBudgetIncome::getDepartmentOneCode, deptCodes)
                .eq(CostBudgetIncome::getYearMonthLy, years)
                .eq(CostBudgetIncome::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudgetIncome::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudgetIncome::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }

    public List<CostBudgetIncome> findListByOrgCodesAndCostCenterCodesAndYears(List<String> orgCodes, String years) {
        return this.lambdaQuery()
                .in(CostBudgetIncome::getDepartmentOneCode, orgCodes)
                .eq(CostBudgetIncome::getYearMonthLy, years)
                .eq(CostBudgetIncome::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudgetIncome::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudgetIncome::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }

    public List<CostBudgetIncome> findListByOrgCodesAndCostCenterCodesAndYearsConfirm(List<String> orgCodes, String years) {
        return this.lambdaQuery()
                .in(CostBudgetIncome::getDepartmentOneCode, orgCodes)
                .eq(CostBudgetIncome::getYearMonthLy, years)
                .eq(CostBudgetIncome::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudgetIncome::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudgetIncome::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(CostBudgetIncome::getConfirmStatus, ConfirmStatusEnum.CONFIRMED.getCode())
                .list();
    }

    public List<CostBudgetIncome> findListByOrgCodeAndItemCodesAndYears(List<String> regionOrgCodes, List<String> itemCodes, String years) {
        return this.lambdaQuery()
                .in(CostBudgetIncome::getDepartmentOneCode, regionOrgCodes)
                .in(CostBudgetIncome::getItemCode, itemCodes)
                .eq(CostBudgetIncome::getYearMonthLy, years)
                .eq(CostBudgetIncome::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudgetIncome::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudgetIncome::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }

    public List<CostBudgetIncome> findIncomeAmountByOrgCodeAndYears(List<String> orgCodes, String years, List<String> yearsList) {
        return this.lambdaQuery()
                .in(CostBudgetIncome::getDepartmentOneCode, orgCodes)
                .eq(ObjectUtils.isNotEmpty(years), CostBudgetIncome::getYearMonthLy, years)
                .in(CollectionUtils.isNotEmpty(yearsList), CostBudgetIncome::getYearMonthLy, yearsList)
                .eq(CostBudgetIncome::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudgetIncome::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudgetIncome::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }

    public List<CostBudgetIncome> findIncomeAmountByOrgCodeAndYearsConfirm(List<String> orgCodes, String years, List<String> yearsList) {
        return this.lambdaQuery()
                .in(CostBudgetIncome::getDepartmentOneCode, orgCodes)
                .eq(ObjectUtils.isNotEmpty(years), CostBudgetIncome::getYearMonthLy, years)
                .in(CollectionUtils.isNotEmpty(yearsList), CostBudgetIncome::getYearMonthLy, yearsList)
                .eq(CostBudgetIncome::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudgetIncome::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudgetIncome::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(CostBudgetIncome::getConfirmStatus, ConfirmStatusEnum.CONFIRMED.getCode())
                .list();
    }

}

