package com.biz.crm.tpm.business.control.local.repository;



import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.control.sdk.vo.BudgetControlRangeVo;
import com.biz.crm.tpm.business.control.local.entity.BudgetControlRange;
import com.biz.crm.tpm.business.control.local.mapper.BudgetControlRangeMapper;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;


/**
 * 预算管控范围(BudgetControlRange)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-22 16:19:54
 */
@Component
public class BudgetControlRangeRepository extends ServiceImpl<BudgetControlRangeMapper, BudgetControlRange> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 根据管控编码删除
     *
     * @param code
     */
    public void deleteByCode(String code) {
        this.lambdaUpdate().eq(BudgetControlRange::getControlCode, code)
                .remove();
    }

    /**
     * 根据管控编码查询
     *
     * @param code
     */
    public List<BudgetControlRangeVo> findByCode(String code) {
        List<BudgetControlRange> list = this.lambdaQuery().eq(BudgetControlRange::getControlCode, code)
                .list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, BudgetControlRange.class, BudgetControlRangeVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }

    /**
     * 根据管控编码查询
     *
     * @param codeSet
     */
    public List<BudgetControlRangeVo> findByCodeSet(Set<String> codeSet) {
        List<BudgetControlRange> list = this.lambdaQuery().in(BudgetControlRange::getControlCode, codeSet)
                .list();
        return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, BudgetControlRange.class, BudgetControlRangeVo.class, LinkedHashSet.class, ArrayList.class)) :
                new ArrayList<>();
    }
}

