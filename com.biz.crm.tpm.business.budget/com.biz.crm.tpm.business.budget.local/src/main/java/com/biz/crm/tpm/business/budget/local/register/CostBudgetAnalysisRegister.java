package com.biz.crm.tpm.business.budget.local.register;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * 预算费用细分数据视图
 */
@Component
public class CostBudgetAnalysisRegister implements DataviewRegister {

    /**
     * 全系统唯一的数据视图业务编号（例如orderList）</br>
     * 如果不唯一，系统将会报错
     */
    @Override
    public String code() {
        return "tpm_cost_budget_analysis_data_view";
    }

    /**
     * 注册的数据视图描述信息（例如：订单业务主列表视图）
     */
    @Override
    public String desc() {
        return "预算费用细分数据视图";
    }

    /**
     * 这个数据视图所使用的SQL语句，注意这个SQL语句不需要包括任何分页查询的关键字、不需要包括和数据权限有关的任何查询条件，
     * 也不要包括和任何特定数据库有关的关键字。</p>
     * 但是可以加入和必传参数有关的参数绑定位置，例如：</p>
     * <code>
     * select * from user where user.name = :name
     * </code>
     * <p>
     * 这样的话，数据视图的正式执行就必须传入参数名为name的参数信息</p>
     */
    @Override
    public String buildSql() {
        return "SELECT t.* " +
                "    FROM tpm_cost_budget_analysis t " +
                "    WHERE t.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' " +
                "    AND t.tenant_code = :tenantCode ";
    }
}
