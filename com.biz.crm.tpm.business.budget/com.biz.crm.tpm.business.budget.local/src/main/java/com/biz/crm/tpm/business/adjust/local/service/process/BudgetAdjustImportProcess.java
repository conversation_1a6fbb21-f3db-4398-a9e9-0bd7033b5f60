package com.biz.crm.tpm.business.adjust.local.service.process;

import com.alibaba.fastjson.JSON;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDetailDto;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDto;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustEventDto;
import com.biz.crm.tpm.business.adjust.sdk.enums.BudgetAdjustEnum;
import com.biz.crm.tpm.business.adjust.sdk.event.BudgetAdjustEventListener;
import com.biz.crm.tpm.business.adjust.sdk.service.BudgetAdjustService;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustImportVo;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustVo;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预算调整-批量调整
 */
@Component
@Slf4j
public class BudgetAdjustImportProcess implements ImportProcess<BudgetAdjustImportVo> {

    @Autowired(required = false)
    private BudgetAdjustService budgetAdjustService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private CostBudgetVoService costBudgetVoService;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    /**
     * 组织层级
     */
    private static final String MDM_ORG_LEVEL = "mdm_org_level";

    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, BudgetAdjustImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, BudgetAdjustImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
//        Map<String, List<DictDataVo>> dictDataMap = dictDataVoService.findByDictTypeCodeList(Arrays.asList(MDM_ORG_LEVEL));

        for (Map.Entry<Integer, BudgetAdjustImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();

            BudgetAdjustImportVo vo = row.getValue();

            this.validateIsTrue(StringUtils.isNotEmpty(vo.getAdjustName()), "调整名称不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getYearMonthLy()), "预算年月不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCompanyCode()), "公司编码不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getDepartmentOneCode()), "部门编码不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostCenterCode()), "成本中心编码不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getBudgetSubjectName()), "预算科目名称不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getOperateType()), "操作类型不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getAdjustAmountStr()), "操作金额不能为空！");

            BudgetAdjustEnum adjustEnum = BudgetAdjustEnum.findByName(vo.getOperateType());
            this.validateIsTrue(adjustEnum != null, "操作类型不正确！");
            BigDecimal amount = null;
            try {
                amount = new BigDecimal(vo.getAdjustAmountStr().trim());
            } catch (Exception e) {
                this.validateIsTrue(false, "操作金额类型转换失败！");
            }
            this.validateIsTrue(amount.compareTo(BigDecimal.ZERO) != 0, "操作金额不能等于0！");


            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        return errMap;
    }

    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, BudgetAdjustImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        // 查询数据字典
//        Map<String, List<DictDataVo>> dictDataMap = dictDataVoService.findByDictTypeCodeList(Arrays.asList(MDM_ORG_LEVEL));
        for (Map.Entry<Integer, BudgetAdjustImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();

            BudgetAdjustImportVo vo = row.getValue();
            CostBudgetVo budgetCode = findBudgetCode(vo);
            this.validateIsTrue(budgetCode != null, "预算信息不正确，未找到对应的预算！");
            vo.setBudgetCode(budgetCode.getCode());
            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (MapUtils.isNotEmpty(errMap)) {
            return errMap;
        }

        Collection<BudgetAdjustImportVo> allList = data.values();
        List<BudgetAdjustImportVo> adjustList = allList.stream().filter(e -> e.getOperateType().equals(BudgetAdjustEnum.TRANSFER.getDescr())).collect(Collectors.toList());
        allList.removeAll(adjustList);
        Map<String, List<BudgetAdjustImportVo>> mapA = adjustList.stream().collect(Collectors.groupingBy(e -> e.getAdjustName()));

        List<BudgetAdjustDto> dtoList = new ArrayList<>();
        mapA.forEach((k, v) -> {
            BudgetAdjustDto adjustDto = new BudgetAdjustDto();
            adjustDto.setAdjustName(k);
            adjustDto.setRemark(v.get(0).getRemark());
            List<BudgetAdjustDetailDto> detailDtoList = new ArrayList<>();
            v.forEach(e -> {
                BudgetAdjustDetailDto detailDto = new BudgetAdjustDetailDto();
                detailDto.setBudgetCode(e.getBudgetCode());
                detailDto.setAdjustAmount(new BigDecimal(e.getAdjustAmountStr().trim()));
                detailDto.setRemark(e.getRemarkDetail());
                detailDtoList.add(detailDto);
            });
            adjustDto.setDetails(detailDtoList);
            budgetAdjustService.adjust(adjustDto, true);
            dtoList.add(adjustDto);
        });
        Map<String, List<BudgetAdjustImportVo>> mapB = allList.stream().collect(Collectors.groupingBy(e -> e.getAdjustName()));
        mapB.forEach((k, v) -> {
            BudgetAdjustDto adjustDto = new BudgetAdjustDto();
            adjustDto.setAdjustName(k);
            adjustDto.setRemark(v.get(0).getRemark());
            List<BudgetAdjustDetailDto> detailDtoList = new ArrayList<>();
            v.forEach(e -> {
                BudgetAdjustDetailDto detailDto = new BudgetAdjustDetailDto();
                detailDto.setBudgetCode(e.getBudgetCode());
                detailDto.setAdjustAmount(new BigDecimal(e.getAdjustAmountStr().trim()));
                detailDto.setRemark(e.getRemarkDetail());
                detailDtoList.add(detailDto);
            });
            adjustDto.setDetails(detailDtoList);
            budgetAdjustService.change(adjustDto, true);
            dtoList.add(adjustDto);
        });

        // 创建事件
        BudgetAdjustEventDto eventDto = new BudgetAdjustEventDto();
        eventDto.setOriginal(null);
        eventDto.setNewestList(JSON.parseArray(JSON.toJSONString(dtoList), BudgetAdjustVo.class));
        SerializableBiConsumer<BudgetAdjustEventListener, BudgetAdjustEventDto> consumer =
                BudgetAdjustEventListener::onCreate;
        this.nebulaNetEventClient.publish(eventDto, BudgetAdjustEventListener.class, consumer);
        return null;
    }

    /**
     * 查找对应的预算编码
     *
     * @param vo
     * @return
     */
    private CostBudgetVo findBudgetCode(BudgetAdjustImportVo vo) {
        CostBudgetDto dto = new CostBudgetDto();
        dto.setYearMonthLy(vo.getYearMonthLy());
        dto.setCompanyCode(vo.getCompanyCode());
        dto.setDepartmentOneCode(vo.getDepartmentOneCode());
        dto.setCostCenterCode(vo.getCostCenterCode());
        dto.setBudgetSubjectName(vo.getBudgetSubjectName());
        dto.setCustomerCode(vo.getCustomerCode());
        dto.setProductLevelName(vo.getItemName());
        dto.setProductCode(vo.getProductCode());
        dto.setTenantCode(TenantUtils.getTenantCode());
        List<CostBudgetVo> codeList = costBudgetVoService.findBudgetCodeByDto(dto);
        if (CollectionUtils.isEmpty(codeList) || codeList.size() > 1) {
            return null;
        }
        return codeList.get(0);
    }

    @Override
    public Class<BudgetAdjustImportVo> findCrmExcelVoClass() {
        return BudgetAdjustImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "BUDGET_ADJUST_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "预算批量调整导入模板";
    }
}
