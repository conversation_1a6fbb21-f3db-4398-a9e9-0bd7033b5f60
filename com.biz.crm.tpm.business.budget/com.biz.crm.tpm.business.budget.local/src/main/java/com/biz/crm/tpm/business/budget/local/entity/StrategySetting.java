package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;

@ApiModel(value = "StrategySetting",description = "TPM-活动细类策略配置明细")
@TableName("tpm_strategy_setting")
@Getter
@Setter
@Entity(name = "tpm_strategy_setting")
@org.hibernate.annotations.Table(appliesTo = "tpm_strategy_setting", comment = "TPM-活动细类策略配置明细")
@Deprecated
public class StrategySetting extends UuidEntity {

   /** 关联的策略配置编码 */
   @ApiModelProperty(name = "关联的策略配置编码")
   @TableField(value = "setting_manage_code", fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
   @Column(name = "setting_manage_code", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '关联的策略配置编码 '")
   private String settingManageCode;

   /** 策略配置明细名称 */
   @ApiModelProperty(name = "策略配置明细名称")
   @TableField(value = "name")
   @Column(name = "name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '策略配置明细名称 '")
   private String name;

   /** 策略配置明细code */
   @ApiModelProperty(name = "策略配置明细code")
   @TableField(value = "code")
   @Column(name = "code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '策略配置明细code '")
   private String code;

   /** 策略配置明细父级code */
   @ApiModelProperty(name = "策略配置明细父级code")
   @TableField(value = "parent_code")
   @Column(name = "parent_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '策略配置明细父级code '")
   private String parentCode;

   @ApiModelProperty("策略项的类型")
   @TableField(value = "type")
   @Column(name = "type", length = 64,  nullable = false, columnDefinition = "VARCHAR(64) COMMENT '策略项的类型 '")
   private String type;

   @ApiModelProperty("可能的时间格式")
   @TableField(value = "date_format")
   @Column(name = "date_format", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '可能的时间格式 '")
   protected String dateFormat;

   @ApiModelProperty("可能的策略项默认值")
   @TableField(value = "default_value")
   @Column(name = "default_value", length = 512, columnDefinition = "VARCHAR(512) COMMENT '可能的策略项默认值 '")
   private String defaultValue;

   @ApiModelProperty("可能的提示语")
   @TableField(value = "tip")
   @Column(name = "tip", columnDefinition = "VARCHAR(255) COMMENT '可能的提示语 '")
   protected String tip;

   @ApiModelProperty("是否必需")
   @TableField(value = "necessary")
   @Column(name = "necessary", nullable = false, columnDefinition = "int(8) COMMENT '是否必需 '")
   private Boolean necessary;

   @ApiModelProperty("排序值(值越小越靠前)")
   @TableField(value = "sort_index")
   @Column(name = "sort_index", nullable = false, columnDefinition = "int(8) COMMENT '排序值(值越小越靠前) '")
   private Integer sortIndex;

   @ApiModelProperty("策略项的值类型")
   @TableField(value = "value_type")
   @Column(name = "value_type", length = 64,  nullable = false, columnDefinition = "VARCHAR(64) COMMENT '策略项的值类型 '")
   private String valueType;

   @ApiModelProperty("是否显示")
   @TableField(value = "display")
   @Column(name = "display", nullable = false, columnDefinition = "int(8) COMMENT '是否显示 '")
   private Boolean display;

}