package com.biz.crm.tpm.business.budget.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.budget.local.entity.CostBudgetIncome;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetIncomeDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收入预算(CostBudgetIncome)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-05-27 14:38:52
 */
public interface CostBudgetIncomeMapper extends BaseMapper<CostBudgetIncome> {

    List<CostBudgetIncomeVo> findByCondition(@Param("dto") CostBudgetIncomeDto dto);
}

