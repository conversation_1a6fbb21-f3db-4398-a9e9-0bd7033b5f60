package com.biz.crm.tpm.business.adjust.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.adjust.local.entity.BudgetAdjust;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDto;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustVo;
import org.apache.ibatis.annotations.Param;

/**
 * 预算调整(BudgetAdjust)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2024-05-23 14:49:55
 */
public interface BudgetAdjustMapper extends BaseMapper<BudgetAdjust> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页对象
   * @param dto 查询实体
   * @return 所有数据
  */
  Page<BudgetAdjustVo> findByConditions(@Param("page") Page<BudgetAdjustVo> page, @Param("dto") BudgetAdjustDto dto);
}

