<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.tpm.business.budget.local.mapper.CostBudgetIncomeMapper">

    <select id="findByCondition" resultType="com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo">
        select
        t.*
        from tpm_cost_budget_income t
        <where>
            <if test="dto.tenantCode != null and dto.tenantCode != '' ">
                and t.tenant_code = #{dto.tenantCode}
            </if>
            <if test="dto.customerCode !=null and dto.customerCode != '' ">
                and t.customer_code = #{dto.customerCode}
            </if>
            <if test="dto.itemName !=null and dto.itemName != '' ">
                and t.item_name = #{dto.itemName}
            </if>
            <if test="dto.productCode !=null and dto.productCode != '' ">
                and t.product_code = #{dto.productCode}
            </if>
            <if test="dto.yearMonthLy !=null and dto.yearMonthLy != '' ">
                and t.year_month_ly = #{dto.yearMonthLy}
            </if>
            <if test="dto.deptCodeSet != null and dto.deptCodeSet.size > 0">
                and t.department_one_code in
                <foreach item="item" collection="dto.deptCodeSet" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            and t.del_flag = '${@<EMAIL>()}'
        </where>
    </select>
</mapper>

