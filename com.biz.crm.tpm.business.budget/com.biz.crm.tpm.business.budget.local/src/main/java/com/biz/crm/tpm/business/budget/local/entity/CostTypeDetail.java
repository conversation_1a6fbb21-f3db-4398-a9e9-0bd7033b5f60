package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 实体：TPM-活动明细;
 *
 * <AUTHOR> Keller
 * @date : 2022-5-20
 */
@ApiModel(value = "CostTypeDetail", description = "TPM-活动明细")
@TableName("tpm_cost_type_detail")
@Getter
@Setter
@Entity(name = "tpm_cost_type_detail")
@Table(name = "tpm_cost_type_detail", indexes = {
        @Index(name = "tpm_cost_type_detail_idx1", columnList = "detail_code", unique = true),
})
@org.hibernate.annotations.Table(appliesTo = "tpm_cost_type_detail", comment = "TPM-活动明细")
public class CostTypeDetail extends TenantFlagOpEntity {


    @ApiModelProperty("活动大类编码")
    @Column(name = "category_code", columnDefinition = "varchar(32) comment '活动大类编码'")
    private String categoryCode;

    @ApiModelProperty("活动大类名称")
    @Column(name = "category_name", columnDefinition = "varchar(64) comment '活动大类名称'")
    private String categoryName;

    /**
     * 活动细类名称
     */
    @ApiModelProperty(name = "活动细类名称", notes = "")
    @Column(name = "detail_name", nullable = false, length = 128, columnDefinition = "VARCHAR(128) COMMENT '活动细类名称 '")
    private String detailName;

    /**
     * 活动细类编号
     */
    @ApiModelProperty(name = "活动细类编号", notes = "")
    @Column(name = "detail_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '活动细类编号 '")
    private String detailCode;

    /**
     * 分摊费用到产品(Y/N)
     */
    @ApiModelProperty(name = "分摊费用到产品(Y/N)", notes = "")
    @Column(name = "is_share_to_product", length = 10, columnDefinition = "VARCHAR(10) COMMENT '分摊费用到产品(Y/N) '")
    private String isShareToProduct;

    /**
     * 支付方式
     */
    @ApiModelProperty(name = "支付方式", notes = "")
    @Column(name = "pay_by", nullable = false, columnDefinition = "VARCHAR(255) COMMENT '支付方式 '")
    private String payBy;

    /**
     * 是否推送SFA
     */
    @ApiModelProperty(name = "是否推送SFA", notes = "")
    @Column(name = "is_send_sfa", length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否推送SFA '")
    private String isSendSfa;

    /**
     * 是否自动核销(Y/N)
     */
    @ApiModelProperty(name = "是否自动核销(Y/N)", notes = "")
    @Column(name = "is_auto_audit", length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否自动核销(Y/N) '")
    private String isAutoAudit;

    /**
     * 是否多次核销(Y/N)
     */
    @ApiModelProperty(name = "是否多次核销(Y/N)", notes = "")
    @Column(name = "is_multiple_audit", length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否多次核销(Y/N) '")
    private String isMultipleAudit;

    /**
     * 是否计提(Y/N)
     */
    @ApiModelProperty(name = "是否计提(Y/N)", notes = "")
    @Column(name = "is_with_holding", length = 10, columnDefinition = "VARCHAR(10) COMMENT '是否计提(Y/N) '")
    private String isWithHolding;

    /**
     * 是否允许提前结案(Y/N)
     */
    @ApiModelProperty(name = "是否允许提前结案(Y/N)", notes = "")
    @Column(name = "be_audit_early", length = 10, columnDefinition = "VARCHAR(10) default 'N' COMMENT '是否允许提前结案(Y/N) '")
    private String beAuditEarly;

    /**
     * 核销有效期(天)
     */
    @ApiModelProperty(name = "核销有效期(天)", notes = "")
    @Column(name = "audit_validity_time", columnDefinition = "INT COMMENT '核销有效期(月) '")
    private Integer auditValidityTime;

    /**
     * 兑付有效期(天)
     */
    @ApiModelProperty(name = "兑付有效期(天)", notes = "")
    @Column(name = "pay_validity_time", columnDefinition = "INT COMMENT '兑付有效期(月) '")
    private Integer payValidityTime;

    /**
     * 执行类型
     */
    @ApiModelProperty("执行类型")
    @Column(name = "execution_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '执行类型 '")
    private String executionType;
}