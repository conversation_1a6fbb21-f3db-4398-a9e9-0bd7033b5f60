package com.biz.crm.tpm.business.budget.local.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_cost_budget")
@Table(name = "tpm_cost_budget", indexes = {
        @Index(name = "cost_budget_idx1", columnList = "code"),
        @Index(name = "cost_budget_idx2", columnList = "budget_subject_code"),
        @Index(name = "cost_budget_idx3", columnList = "customer_code"),
        @Index(name = "cost_budget_idx4", columnList = "product_code"),
        @Index(name = "cost_budget_idx5", columnList = "year_month_ly"),
        @Index(name = "cost_budget_idx6", columnList = "department_one_code"),
        @Index(name = "cost_budget_idx7", columnList = "company_code"),
        @Index(name = "cost_budget_idx8", columnList = "cost_center_code"),
        @Index(name = "cost_budget_idx9", columnList = "unique_key"),
        @Index(name = "cost_budget_idx10", columnList = "item_code"),
})
@ApiModel(value = "CostBudget", description = "费用预算")
@org.hibernate.annotations.Table(appliesTo = "tpm_cost_budget", comment = "费用预算")
public class CostBudget extends TenantFlagOpEntity {

  private static final long serialVersionUID = -8843778898927203215L;
  @ApiModelProperty("编码")
  @TableField(value = "code", fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_EMPTY)
  @Column(name = "code", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '编码'")
  private String code;

  /**
   * 参见com.biz.tpm.business.budget.sdk.enums.CostBudgetType
   */
  @ApiModelProperty("费用预算类型")
  @TableField(value = "type")
  @Column(name = "type", length = 64, nullable = false, columnDefinition = "varchar(64) COMMENT '费用预算类型'")
  private String type;

  @ApiModelProperty("年")
  @TableField(value = "year")
  @Column(name = "year", length = 4, nullable = false, columnDefinition = "int(4) COMMENT '年'")
  private Integer year;

  @ApiModelProperty("季度")
  @TableField(value = "quarter")
  @Column(name = "quarter", length = 4, nullable = false, columnDefinition = "int(4) COMMENT '季度'")
  private Integer quarter;

  @ApiModelProperty("月份")
  @TableField(value = "month")
  @Column(name = "month", length = 4, columnDefinition = "int(4) COMMENT '月份'")
  private Integer month;

  @ApiModelProperty("预算科目编码")
  @TableField(value = "budget_subject_code")
  @Column(name = "budget_subject_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算科目编码'")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  @TableField(value = "budget_subject_name")
  @Column(name = "budget_subject_name", columnDefinition = "varchar(255) COMMENT '预算科目名称'")
  private String budgetSubjectName;

  @ApiModelProperty("组织编码")
  @TableField(value = "org_code")
  @Column(name = "org_code", length = 64, columnDefinition = "varchar(64) COMMENT '组织编码'")
  private String orgCode;

  @ApiModelProperty("组织名称")
  @TableField(value = "org_name")
  @Column(name = "org_name", columnDefinition = "varchar(255) COMMENT '组织名称'")
  private String orgName;

  @ApiModelProperty("渠道编码")
  @TableField(value = "channel_code")
  @Column(name = "channel_code", length = 64, columnDefinition = "varchar(64) COMMENT '渠道编码'")
  private String channelCode;

  @ApiModelProperty("渠道名称")
  @TableField(value = "channel_name")
  @Column(name = "channel_name", columnDefinition = "varchar(255) COMMENT '渠道名称'")
  private String channelName;

  @ApiModelProperty("客户编码")
  @TableField(value = "customer_code")
  @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
  private String customerCode;

  @ApiModelProperty("客户名称")
  @TableField(value = "customer_name")
  @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
  private String customerName;

  @ApiModelProperty("门店编码")
  @TableField(value = "terminal_code")
  @Column(name = "terminal_code", length = 64, columnDefinition = "varchar(64) COMMENT '门店编码'")
  private String terminalCode;

  @ApiModelProperty("门店名称")
  @TableField(value = "terminal_name")
  @Column(name = "terminal_name", columnDefinition = "varchar(255) COMMENT '门店名称'")
  private String terminalName;

  @ApiModelProperty("产品层级编码")
  @TableField(value = "product_level_code")
  @Column(name = "product_level_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品层级编码'")
  private String productLevelCode;

  @ApiModelProperty("产品层级名称")
  @TableField(value = "product_level_name")
  @Column(name = "product_level_name", columnDefinition = "varchar(255) COMMENT '产品层级名称'")
  private String productLevelName;

  @ApiModelProperty("产品编码")
  @TableField(value = "product_code")
  @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
  private String productCode;

  @ApiModelProperty("产品名称")
  @TableField(value = "product_name")
  @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
  private String productName;

  @ApiModelProperty("期初金额")
  @TableField(value = "initial_amount")
  @Column(name = "initial_amount", nullable = false, columnDefinition = "decimal(20,4) COMMENT '期初金额'")
  private BigDecimal initialAmount;



  @ApiModelProperty("预算类型")
  @Column(name = "budget_type", length = 32, columnDefinition = "VARCHAR(32) COMMENT '预算类型'")
  private String budgetType;

  @ApiModelProperty("年月")
  @Column(name = "year_month_ly", length = 32, columnDefinition = "VARCHAR(32) COMMENT '年月'")
  private String yearMonthLy;

  @ApiModelProperty("确认状态")
  @Column(name = "confirm_status", length = 64, columnDefinition = "varchar(64) COMMENT '确认状态'")
  private String confirmStatus;

  @ApiModelProperty("一级部门编码")
  @Column(name = "department_one_code", columnDefinition = "VARCHAR(32) COMMENT '一级部门编码'")
  private String departmentOneCode;

  @ApiModelProperty("一级部门名称")
  @Column(name = "department_one_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '一级部门名称'")
  private String departmentOneName;

  @ApiModelProperty("部门层级")
  @Column(name = "level_num", columnDefinition = "int COMMENT '部门层级'")
  private Integer levelNum;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "VARCHAR(32) COMMENT '公司编码'")
  private String companyCode;

  @ApiModelProperty("公司名称")
  @Column(name = "company_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '公司名称'")
  private String companyName;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "VARCHAR(32) COMMENT '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '成本中心名称'")
  private String costCenterName;

  @ApiModelProperty("唯一标识")
  @Column(name = "unique_key", columnDefinition = "VARCHAR(128) COMMENT '唯一标识'")
  private String uniqueKey;

  @ApiModelProperty("调整金额")
  @Column(name = "adjust_amount", columnDefinition = "decimal(20,6) COMMENT '调整金额'")
  private BigDecimal adjustAmount;

  @ApiModelProperty("调整后余额")
  @Column(name = "adjust_balance_amount", columnDefinition = "decimal(20,6) COMMENT '调整后余额'")
  private BigDecimal adjustBalanceAmount;

  @ApiModelProperty("月度余额")
  @Column(name = "month_balance_amount", columnDefinition = "decimal(20,6) COMMENT '月度余额'")
  private BigDecimal monthBalanceAmount;

  @ApiModelProperty("冻结金额")
  @Column(name = "freeze_amount", columnDefinition = "decimal(20,6) COMMENT '冻结金额'")
  private BigDecimal freezeAmount;

  @ApiModelProperty("已使用金额")
  @Column(name = "used_amount", columnDefinition = "decimal(20,6) COMMENT '已使用金额'")
  private BigDecimal usedAmount;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("关联业务编码")
  @Column(name = "business_code", length = 64, columnDefinition = "varchar(64) COMMENT '关联业务编码'")
  private String businessCode;

  /**
   * 客户ERP编码
   */
  @ApiModelProperty("客户ERP编码")
  @Column(name = "erp_code", length = 32, columnDefinition = "varchar(32) COMMENT '客户ERP编码'")
  private String erpCode;

  /**
   * 产品组编码
   */
  @ApiModelProperty("产品组编码")
  @Column(name = "product_group_code", length = 32, columnDefinition = "varchar(32) COMMENT '产品组编码'")
  private String productGroupCode;

  public void refreshBalanceAmount() {
    this.adjustBalanceAmount = this.initialAmount
            .add(Optional.ofNullable(this.adjustAmount).orElse(BigDecimal.ZERO))
            .subtract(Optional.ofNullable(this.freezeAmount).orElse(BigDecimal.ZERO));
    this.monthBalanceAmount = this.adjustBalanceAmount
            .subtract(Optional.ofNullable(this.usedAmount).orElse(BigDecimal.ZERO));
  }
}
