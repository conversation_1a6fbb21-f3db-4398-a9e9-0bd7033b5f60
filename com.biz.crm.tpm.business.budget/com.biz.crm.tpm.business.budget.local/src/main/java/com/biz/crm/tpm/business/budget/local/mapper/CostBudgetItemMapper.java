package com.biz.crm.tpm.business.budget.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.local.entity.CostBudgetItem;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetItemDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetItemVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 费用预算明细项(CostBudgetItem)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
public interface CostBudgetItemMapper extends BaseMapper<CostBudgetItem> {

  /**
   * 分页查询所有数据
   * @param page       分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  Page<CostBudgetItemVo> findByConditions(@Param("page") Page<CostBudgetItemVo> page, @Param("dto") CostBudgetItemDto dto);

  /**
   * 分页查询所有数据
   * @param page       分页对象
   * @param dto 查询实体
   * @return 所有数据
   */
  Page<CostBudgetItemVo> findByCostBudgetConditions(@Param("page") Page<CostBudgetItemVo> page, @Param("dto") CostBudgetDto dto);

  List<CostBudgetItem> findMarketingPlanCaseList(@Param("costBudgetCode") String costBudgetCode);

  List<CostBudgetItem> findWithholdingList(@Param("costBudgetCode")String costBudgetCode);

  List<CostBudgetItem> findEndCaseList(@Param("costBudgetCode")String costBudgetCode);
}

