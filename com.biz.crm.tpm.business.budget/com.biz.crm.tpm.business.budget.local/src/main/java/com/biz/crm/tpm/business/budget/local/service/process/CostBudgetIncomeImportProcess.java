package com.biz.crm.tpm.business.budget.local.service.process;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeImportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CostBudgetIncomeImportProcess implements ImportProcess<CostBudgetIncomeImportVo> {

    @Autowired
    private CustomerVoService customerVoServiceFeign;
    @Autowired
    private ProductVoService productVoServiceFeign;
    @Autowired
    private OrgVoService orgVoService;
    @Autowired
    private CostBudgetIncomeService costBudgetIncomeService;
    @Autowired
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private RedisLockService redisLockService;

    private final static String COST_BUDGET_INCOME_IMPORT = "COST_BUDGET_INCOME_IMPORT";

    /**
     * 公司代码
     */
    private static final String TPM_COMPANY = "tpm_company";

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, CostBudgetIncomeImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, CostBudgetIncomeImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        if (CollectionUtil.isEmpty(data)) {
            throw new IllegalArgumentException("导入数据不能为空!");
        }
        Map<Integer, String> errMap = new HashMap<>();
        try {
            for (Map.Entry<Integer, CostBudgetIncomeImportVo> row : data.entrySet()) {
                int rowNum = row.getKey();

                CostBudgetIncomeImportVo vo = row.getValue();
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getYearMonthLy()), "年月，不能为空！");
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getDepartmentOneCode()), "部门编码，不能为空！");
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostCenterCode()), "成本中心编码，不能为空！");
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostCenterName()), "成本中心名称，不能为空！");
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getCompanyCode()), "公司代码，不能为空！");
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getQuantityStr()), "数量，不能为空");
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getIncomeAmountStr()), "收入总金额，不能为空");
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostAmountStr()), "成本，不能为空");
                this.validateIsTrue(StringUtils.isNotEmpty(vo.getLogisticsAmountStr()), "物流费，不能为空");
                BigDecimal quantity = null;
                BigDecimal incomeAmount = null;
                BigDecimal costAmount = null;
                BigDecimal logisticsAmount = null;
                try {
                    quantity = new BigDecimal(vo.getQuantityStr().trim());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    this.validateIsTrue(false, "数量类型转换失败！");
                }
                try {
                    incomeAmount = new BigDecimal(vo.getIncomeAmountStr().trim());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    this.validateIsTrue(false, "收入总金额类型转换失败！");
                }
                try {
                    costAmount = new BigDecimal(vo.getCostAmountStr().trim());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    this.validateIsTrue(false, "成本类型转换失败！");
                }
                try {
                    logisticsAmount = new BigDecimal(vo.getLogisticsAmountStr().trim());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    this.validateIsTrue(false, "物流费类型转换失败！");
                }
                if (quantity != null) {
                    this.validateIsTrue(quantity.compareTo(BigDecimal.ZERO) > 0, "数量必须大于0！");
                }
                if (incomeAmount != null) {
                    this.validateIsTrue(incomeAmount.compareTo(BigDecimal.ZERO) > 0, "收入总金额必须大于0！");
                }
                if (costAmount != null) {
                    this.validateIsTrue(costAmount.compareTo(BigDecimal.ZERO) > 0, "成本必须大于0！");
                }
                if (logisticsAmount != null) {
                    this.validateIsTrue(logisticsAmount.compareTo(BigDecimal.ZERO) >= 0, "物流费必须大于等于0！");
                }

                String errInfo = this.validateGetErrorInfo();
                if (errInfo != null) {
                    errMap.put(rowNum, errInfo);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
        log.warn(JSONUtil.toJsonStr(errMap));
        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, CostBudgetIncomeImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        redisLockService.lock(COST_BUDGET_INCOME_IMPORT, TimeUnit.MINUTES,60);
        Set<String> orgCodeSet = new HashSet<>();
        Set<String> productCodeSet = new HashSet<>();
        Set<String> erpCodeSet = new HashSet<>();
        for (Map.Entry<Integer, CostBudgetIncomeImportVo> row : data.entrySet()) {
            CostBudgetIncomeImportVo vo = row.getValue();

            orgCodeSet.add(vo.getDepartmentOneCode());
            if (StringUtils.isNotBlank(vo.getProductCode())) {
                productCodeSet.add(vo.getProductCode());
            }
            if (StringUtils.isNotBlank(vo.getErpCode())) {
                erpCodeSet.add(vo.getErpCode());
            }
        }

        Map<String, OrgVo> orgVoMap = orgVoService.findByOrgCodes(new ArrayList<>(orgCodeSet)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), Function.identity(), (a, b) -> a));
        Map<String, Map<String, CustomerVo>> customerVoMap = customerVoServiceFeign.findByErpCodes(new ArrayList<>(erpCodeSet)).stream().collect(Collectors.groupingBy(e -> e.getErpCode(), Collectors.toMap(e -> e.getCompanyCode(), Function.identity(), (a, b) -> a)));
        Map<String, ProductVo> productVoMap = productVoServiceFeign.findDetailsByIdsOrProductCodes(null, new ArrayList<>(productCodeSet)).stream().collect(Collectors.toMap(e -> e.getProductCode(), Function.identity(), (a, b) -> a));


        // 查询数据字典
        Map<String, List<DictDataVo>> dictDataMap = dictDataVoService.findByDictTypeCodeList(Collections.singletonList(TPM_COMPANY));

        List<DictDataVo> channelTypeList = dictDataVoService.findByDictTypeCode("channel_manage_type");
        Map<String, String> valueToCodeMap = channelTypeList.stream().collect(Collectors.toMap(DictDataVo::getDictValue, DictDataVo::getDictCode));
        for (Map.Entry<Integer, CostBudgetIncomeImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();

            CostBudgetIncomeImportVo vo = row.getValue();
            vo.setQuantity(new BigDecimal(vo.getQuantityStr()));
            vo.setIncomeAmount(new BigDecimal(vo.getIncomeAmountStr()));
            vo.setCostAmount(new BigDecimal(vo.getCostAmountStr()));
            vo.setLogisticsAmount(new BigDecimal(vo.getLogisticsAmountStr()));
            vo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
            if (orgVoMap.containsKey(vo.getDepartmentOneCode())) {
                vo.setDepartmentOneName(orgVoMap.get(vo.getDepartmentOneCode()).getOrgName());
                vo.setLevelNum(orgVoMap.get(vo.getDepartmentOneCode()).getLevelNum());
            } else {
                this.validateIsTrue(false, "部门，未找到！");
            }
            DictDataVo company = dictDataMap.get(TPM_COMPANY).stream().filter(e -> e.getDictCode().equals(vo.getCompanyCode())).findFirst().orElse(null);
            if (company != null) {
                vo.setCompanyName(company.getDictValue());
            } else {
                this.validateIsTrue(false, "公司，未找到！");
            }


            if (StringUtils.isNotBlank(vo.getProductCode())) {
                if (productVoMap.containsKey(vo.getProductCode())) {
                    vo.setProductName(productVoMap.get(vo.getProductCode()).getProductName());
                } else {
                    this.validateIsTrue(false, "产品，未找到！");
                }
            }

            if (StringUtils.isNotBlank(vo.getErpCode())) {
                if (customerVoMap.containsKey(vo.getErpCode())) {
                    Map<String, CustomerVo> companyVoMap = customerVoMap.get(vo.getErpCode());
                    if (companyVoMap.containsKey(vo.getCompanyCode())) {
                        CustomerVo customerVo = companyVoMap.get(vo.getCompanyCode());
                        this.validateIsTrue(BooleanEnum.TRUE.getCapital().equals(customerVo.getContractCustomer()), "客户不是合同客户");
                        vo.setCustomerCode(customerVo.getCustomerCode());
                        vo.setCustomerName(customerVo.getCustomerName());
                        vo.setChannelDepartmentCode(customerVo.getChannelDepartmentCode());
                    } else {
                        this.validateIsTrue(false, "客户，未找到！");
                    }
                } else {
                    this.validateIsTrue(false, "客户，未找到！");
                }
            }
            if (StringUtils.isEmpty(vo.getErpCode())) {
                String channelDepartmentCode = vo.getChannelDepartmentCode();
                if (StringUtils.isNotEmpty(channelDepartmentCode)) {
                    this.validateIsTrue(valueToCodeMap.containsKey(channelDepartmentCode), "渠道部门超出字典范围");
                }
                vo.setChannelDepartmentCode(valueToCodeMap.get(channelDepartmentCode));
            }



            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (!errMap.isEmpty()) {
            redisLockService.unlock(COST_BUDGET_INCOME_IMPORT);
            return errMap;
        }

        costBudgetIncomeService.saveBatch(new ArrayList<>(data.values()));

        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCompletion(int status) {
                    if (status == STATUS_ROLLED_BACK || status == STATUS_COMMITTED) {
                        // 事务提交或回滚后执行的代码
                        redisLockService.unlock(COST_BUDGET_INCOME_IMPORT);
                    }
                }
            });
        } else {
            redisLockService.unlock(COST_BUDGET_INCOME_IMPORT);
        }
        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<CostBudgetIncomeImportVo> findCrmExcelVoClass() {
        return CostBudgetIncomeImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return COST_BUDGET_INCOME_IMPORT;
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "预算收入导入模板";
    }
}
