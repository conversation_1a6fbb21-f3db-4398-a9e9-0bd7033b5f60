package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.tpm.business.budget.local.entity.BudgetSubjects;
import com.biz.crm.tpm.business.budget.local.entity.StrategySetting;
import com.biz.crm.tpm.business.budget.local.mapper.StrategySettingMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 活动细类策略配置明细(StrategySetting)表数据库访问层
 * 对象实体类无租户编号tenant_code字段
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
@Deprecated
public class StrategySettingRepository extends ServiceImpl<StrategySettingMapper, StrategySetting> {

//  /**
//   * 根据策略配置编码和明细类型，查询相应的策略明细
//   */
//  public List<CostTypeDetailSettingItem> findBySettingCodeAndType(String settingCode, String type){
//    return this.lambdaQuery().eq(CostTypeDetailSettingItem::getSettingCode,settingCode).
//            eq(CostTypeDetailSettingItem::getType,type).list();
//  }

  /**
   * 根据策略配置编码，查询相应的策略明细
   */
  public List<StrategySetting> findBySettingManageCode(String settingManageCode){
    return this.lambdaQuery().eq(StrategySetting::getSettingManageCode,settingManageCode).list();
  }


//  /**
//   * 根据父级编码查询所有策略明细
//   */
//  public List<CostTypeDetailSettingItem> findByParentKey(String parentkey){
//    return this.lambdaQuery().eq(CostTypeDetailSettingItem::getParentKey,parentkey).
//            orderByAsc(CostTypeDetailSettingItem::getSortIndex).list();
//  }

}

