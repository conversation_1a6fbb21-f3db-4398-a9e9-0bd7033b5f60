package com.biz.crm.tpm.business.budget.local.notifier;

import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.dictionary.sdk.event.DictDataEventListener;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.business.budget.local.service.CostTypeCategoryRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述：</br>预算科目针对数据字典数据变动监听
 *
 * <AUTHOR>
 * @date 2022/5/30
 */
@Component
public class BudgetForDictDataEventListener implements DictDataEventListener {
  @Autowired
  private CostTypeCategoryRangeService costTypeCategoryRangeService;

  @Override
  public void onCreate(DictDataVo vo) {

  }

  @Override
  public void onDelete(List<DictDataVo> vos) {

  }

  @Override
  public void onEnable(List<DictDataVo> vos) {
    if (CollectionUtils.isEmpty(vos)) {
      return;
    }
    Set<String> Codes = vos.stream().map(DictDataVo::getDictCode).collect(Collectors.toSet());
    this.costTypeCategoryRangeService.updateEnableStatus(Codes, EnableStatusEnum.ENABLE.getCode());
  }

  @Override
  public void onDisable(List<DictDataVo> vos) {
    if (CollectionUtils.isEmpty(vos)) {
      return;
    }
    Set<String> Codes = vos.stream().map(DictDataVo::getDictCode).collect(Collectors.toSet());
    this.costTypeCategoryRangeService.updateEnableStatus(Codes, EnableStatusEnum.DISABLE.getCode());
  }

  @Override
  public void onChange(DictDataVo oldVo, DictDataVo newVo) {
    // 无业务处理，在组织结构数据变化后原数据不进行同步更新，和产品陈科确认过后的需求
  }
}
