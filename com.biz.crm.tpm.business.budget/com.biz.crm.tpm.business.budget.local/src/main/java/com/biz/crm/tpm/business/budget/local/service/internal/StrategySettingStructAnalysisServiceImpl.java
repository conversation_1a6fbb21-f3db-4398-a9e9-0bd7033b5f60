package com.biz.crm.tpm.business.budget.local.service.internal;

import com.alibaba.fastjson.JSONArray;
import com.biz.crm.tpm.business.budget.sdk.enums.StrategySettingValueType;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.AbstractStrategySetting;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.CollectDistributionOrdersSetting;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.ControlActivityExpensesSetting;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.SignDisplaySetting;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStruct;
import com.biz.crm.tpm.business.budget.sdk.strategy.setting.StrategySettingStructAnalysis;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;


@Component
public class StrategySettingStructAnalysisServiceImpl implements StrategySettingStructAnalysis {

  @Autowired(required = false)
  private List<AbstractStrategySetting> abstractStrategySettings;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;


  @Override
  public void validateBaseStruct(StrategySettingStruct struct, AbstractStrategySetting setting) {
    Validate.notNull(struct,"传入的策略项信息不能为空");
    Validate.notNull(setting,"指定的策略项结构信息不能为空");
    this.validateBase(struct,setting);
  }

  private void validateBase(StrategySettingStruct struct){
    Validate.notBlank(struct.getName(),"策略项名称不能为空");
    Validate.notBlank(struct.getCode(),"策略项【%s】编码不能为空",struct.getName());
    Validate.notBlank(struct.getType(),"策略项【%s】类型不能为空",struct.getName());
    Validate.notBlank(struct.getValueType(),"策略项【%s】值类型不能为空",struct.getName());
    Validate.notBlank(struct.getSettingManageCode(),"策略项【%s】配置编码不能为空",struct.getName());
    Validate.notNull(struct.getNecessary(),"策略项【%s】是否必需属性不能为空",struct.getName());
    Validate.notNull(struct.getDisplay(),"策略项【%s】是否显示属性不能为空",struct.getName());
    Validate.notNull(struct.getSortIndex(),"策略项【%s】排序值不能为空",struct.getName());
    if(StringUtils.equals(struct.getValueType(),StrategySettingValueType.DATE.getCode())){
      Validate.notBlank(struct.getDateFormat(),"策略项结构【%s】时间格式不能为空",struct.getName());
    }
  }

  private void validateBase(StrategySettingStruct struct, AbstractStrategySetting setting){
    this.validateBase(struct);
    Validate.isTrue(StringUtils.equals(struct.getCode(),setting.getCode()),"策略项编码【%s】与策略项结构编码不匹配",struct.getCode());
    Validate.isTrue(StringUtils.equals(struct.getName(),setting.getName()),"策略项名称【%s】与策略项结构名称不匹配",struct.getName());
    Validate.isTrue(StringUtils.equals(struct.getType(),setting.getType()),"策略项类型【%s】与策略项结构类型不匹配",struct.getType());
    Validate.isTrue(StringUtils.equals(struct.getValueType(),setting.getValueType()),"策略项值类型【%s】与策略项结构值类型不匹配",struct.getValueType());
    Validate.isTrue(struct.getNecessary().equals(setting.getNecessary()),"策略项【%s】是否必需属性与策略项结构是否必需属性不匹配",struct.getName());
    Validate.isTrue(struct.getSortIndex().equals(setting.getSortIndex()),"策略项【%s】排序值与策略项结构排序值不匹配",struct.getName());
    if(StringUtils.isBlank(struct.getParentCode())){
      Validate.isTrue(struct.getDisplay() == Boolean.TRUE,"根策略项【%s】必需显示，请检查",struct.getName());
    }
    if(CollectionUtils.isEmpty(struct.getChildren())){
      return;
    }
    if(!CollectionUtils.isEmpty(setting.getChildren()) && !CollectionUtils.isEmpty(struct.getChildren())){
      long displayCount = struct.getChildren().stream().filter(e -> e.getDisplay() == Boolean.TRUE).count();
      Validate.isTrue(displayCount > 0,"策略项【%s】的子节点必需至少含有一个显示项，请检查",struct.getName());
      for(StrategySettingStruct childStruct : struct.getChildren()){
        Validate.notBlank(childStruct.getCode(),"策略项编码不能为空");
        Validate.notBlank(childStruct.getParentCode(),"策略项编码【%s】的父级编码未指定，请检查",childStruct.getCode());
        Validate.isTrue(StringUtils.equals(childStruct.getParentCode(),struct.getCode()),"指定的策略项【%s】父级编码不匹配，请检查",childStruct.getCode());
        AbstractStrategySetting childSetting = setting.getChildren().stream().filter(e -> StringUtils.equals(childStruct.getCode(),e.getCode())).findFirst().orElse(null);
        Validate.notNull(childSetting,"根据指定的策略项编码【%s】，未能获取到相应结构信息",childStruct.getCode());
        this.validateBase(childStruct,childSetting);
      }
    }
  }


  @Override
  public StrategySettingStruct transferToStruct(AbstractStrategySetting setting){
    if(setting == null){
      return null;
    }
    StrategySettingStruct struct = nebulaToolkitService.copyObjectByWhiteList(setting,StrategySettingStruct.class, HashSet.class, ArrayList.class);
    if(CollectionUtils.isEmpty(setting.getChildren())){
      return struct;
    }
    List<StrategySettingStruct> children = Lists.newArrayList();
    struct.setDefaultValue(setting.getDefaultValue());
    struct.setChildren(children);
    for(AbstractStrategySetting child : setting.getChildren()){
      children.add(this.transferToStruct(child));
    }
    return struct;
  }

  @Override
  public void validateValue(StrategySettingStruct struct) {
    Validate.notNull(struct,"策略项信息不能为空");
    //1.基础信息验证
    this.validateBase(struct);
    //2.必需信息验证
    boolean display = struct.getDisplay() == null ? Boolean.FALSE : struct.getDisplay();
    if(struct.getValue() == null || !display){
      struct.setValue(this.getDefaultValue(struct.getCode()));
    }
    if(struct.getNecessary() && display){
      Validate.notNull(struct.getValue(),"策略项【%s】是必需属性，其值不能为空，请检查",struct.getName());
    }
    //3.值类型验证
    if(struct.getValue() != null){
      this.validateValueType(struct);
    }
    //4.递归验证
    if(!CollectionUtils.isEmpty(struct.getChildren())){
      for(StrategySettingStruct child : struct.getChildren()){
        this.validateValue(child);
      }
    }
  }

  @Override
  public Object getDefaultValue(String code) {
    if(StringUtils.isBlank(code)){
      return null;
    }
    AbstractStrategySetting abstractStrategySetting = abstractStrategySettings.stream().filter(e -> StringUtils.equals(e.getCode(),code)).findFirst().orElse(null);
    if(abstractStrategySetting == null){
      return null;
    }
    return abstractStrategySetting.getDefaultValue();
  }

  @Override
  public void validateDisplaySpecialForStruct(StrategySettingStruct struct, List<StrategySettingStruct> specialStructs) {
    if(struct == null){
      return;
    }
    Validate.notEmpty(specialStructs,"验证是否显示时，指定的策略项信息不能为空");
    if(StringUtils.equals(struct.getCode(), ControlActivityExpensesSetting.class.getSimpleName())){
      boolean matched = specialStructs.stream().anyMatch(e -> StringUtils.equals(e.getCode(), CollectDistributionOrdersSetting.class.getSimpleName()) || StringUtils.equals(e.getCode(), SignDisplaySetting.class.getSimpleName()));
      Validate.isTrue(matched,"验证是否显示时，如果策略项【%s】存在，【是否签署陈列协议】和【是否采集分销订单】不能同时隐藏",struct.getName());
    }
  }

  @Override
  public Object transferValue(String value, String valueType) {
    StrategySettingValueType strategySettingValueType = StrategySettingValueType.findByCode(valueType);
    Validate.notNull(strategySettingValueType,"未知的策略项值类型【%s】，请检查",valueType);
    switch (strategySettingValueType){
      case STRING:
      case OBJECT:
      case DATE:
      case ARRAY:
        return value;
      case NUMBER:
        return NumberUtils.createNumber(value);
      case BOOLEAN:
        return Boolean.valueOf(value);
      default:
        throw new IllegalArgumentException("未知的策略项值类型，请检查");
    }
  }

  @Override
  public List<StrategySettingStruct> structTree(List<StrategySettingStruct> trees, List<StrategySettingStruct> sourceStructs, StrategySettingStruct currentStruct) {
    if(CollectionUtils.isEmpty(sourceStructs) || currentStruct == null){
      return trees;
    }
    if(StringUtils.isBlank(currentStruct.getParentCode())){
      trees.add(currentStruct);
    }
    String parentCode = currentStruct.getCode();
    List<StrategySettingStruct> children = sourceStructs.stream().filter(e -> StringUtils.equals(e.getParentCode(),parentCode)).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(children)){
      currentStruct.setChildren(children);
      for(StrategySettingStruct child : children){
        this.structTree(trees,sourceStructs,child);
      }
    }
    return trees;
  }

  private void validateValueType(StrategySettingStruct struct){
    Object value = struct.getValue();
    Validate.notNull(value,"策略项【%s】值信息不能为空",struct.getName());
    StrategySettingValueType valueType = StrategySettingValueType.findByCode(struct.getValueType());
    Validate.notNull(valueType,"未知的策略项值类型【%s】，请检查",struct.getValueType());
    switch (valueType){
      case STRING:
        Validate.notBlank(value.toString(),"策略项【%s】值信息不能为空",struct.getName());
        break;
      case OBJECT:
        break;
      case DATE:
        Validate.notBlank(struct.getDateFormat(),"策略项【%s】日期格式不能为空",struct.getName());
        DateFormat sdf = new SimpleDateFormat(struct.getDateFormat());
        try {
          sdf.parse(value.toString());
        } catch (ParseException e) {
          throw new IllegalArgumentException("策略项" + struct.getName() + "的值不能转换为日期类型，请检查");
        }
        break;
      case NUMBER:
        Validate.isTrue(NumberUtils.isParsable(value.toString()),"策略项【%s】值信息不能转换为数字，请检查",struct.getName());
        break;
      case ARRAY:
        try{
          JSONArray.parseArray(value.toString());
        }catch (Exception e){
          throw new IllegalArgumentException("策略项" + struct.getName() + "的值不能转换为数组类型，请检查");
        }
        break;
      case BOOLEAN:
        Validate.isTrue(StringUtils.equalsAny(value.toString(),Boolean.TRUE.toString(),Boolean.FALSE.toString()),"策略项【%s】值信息不是布尔类型",struct.getName());
        break;
      default:
        throw new IllegalArgumentException("未知的策略项值类型，请检查");
    }
  }
}
