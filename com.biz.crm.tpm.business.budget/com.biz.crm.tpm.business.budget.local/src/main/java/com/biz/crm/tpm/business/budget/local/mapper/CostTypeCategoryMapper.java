package com.biz.crm.tpm.business.budget.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeCategory;
import com.biz.crm.tpm.business.budget.sdk.dto.CostTypeCategoryDto;
import com.biz.crm.tpm.business.budget.sdk.vo.CostTypeCategoryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * TPM-活动大类;(tpm_cost_type_category)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
@Mapper
@Deprecated
public interface CostTypeCategoryMapper extends BaseMapper<CostTypeCategory> {
    /**
     * 分页查询所有数据
     *
     * @param page 分页参数
     * @param dto  动态查询条件
     * @return 分页对象列表
     */
    Page<CostTypeCategoryVo> findByConditions(@Param("page") Page<CostTypeCategoryVo> page, @Param("dto") CostTypeCategoryDto dto);

    List<CostTypeCategoryVo> findListReleaseSecondBudgetSubjectCodes(@Param("tenantCode") String tenantCode);
}