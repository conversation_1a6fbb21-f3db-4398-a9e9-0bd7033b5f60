package com.biz.crm.tpm.business.budget.local.service.internal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.tpm.business.budget.local.entity.CostBudgetIncome;
import com.biz.crm.tpm.business.budget.local.repository.CostBudgetIncomeRepository;
import com.biz.crm.tpm.business.budget.sdk.constant.CostBudgetConstant;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetIncomeDto;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetIncomeLogEventDto;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.event.log.CostBudgetIncomeLogEventListener;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetIncomeService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetLockVoService;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeImportVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetIncomeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Service("costBudgetIncomeService")
public class CostBudgetIncomeServiceImpl implements CostBudgetIncomeService {

    @Autowired(required = false)
    private CostBudgetIncomeRepository costBudgetIncomeRepository;
    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private CostBudgetLockVoService costBudgetLockVoService;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired
    private ProductPhaseVoService productPhaseVoService;

    /**
     * 编辑
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(CostBudgetIncomeDto dto) {
        updateValidate(dto);
        List<CostBudgetIncome> entities = costBudgetIncomeRepository.findByCodeAndTenantCode(Sets.newHashSet(dto.getIncomeCode()), TenantUtils.getTenantCode());
        Validate.notEmpty(entities, "未找到对应的数据");
        CostBudgetIncomeVo oldVo = nebulaToolkitService.copyObjectByWhiteList(entities.get(0), CostBudgetIncomeVo.class, LinkedHashSet.class, ArrayList.class);

        boolean lock = costBudgetLockVoService.lock(dto.getIncomeCode(), TimeUnit.SECONDS, 10);
        Validate.isTrue(lock, "操作预算失败，获取操作锁失败！");
        try {
            // 根据部门编码做权限将部门赋值给权限字段
            dto.setOrgCode(dto.getDepartmentOneCode());
            costBudgetIncomeRepository.saveOrUpdate(nebulaToolkitService.copyObjectByWhiteList(dto, CostBudgetIncome.class, LinkedHashSet.class, ArrayList.class));
        } finally {
            costBudgetLockVoService.unLock(dto.getIncomeCode());
        }

        //更新业务日志
        CostBudgetIncomeLogEventDto logEventDto = new CostBudgetIncomeLogEventDto();
        logEventDto.setOriginal(oldVo);
        logEventDto.setNewest(nebulaToolkitService.copyObjectByWhiteList(dto, CostBudgetIncomeVo.class, LinkedHashSet.class, ArrayList.class));
        SerializableBiConsumer<CostBudgetIncomeLogEventListener, CostBudgetIncomeLogEventDto> onUpdate =
                CostBudgetIncomeLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(logEventDto, CostBudgetIncomeLogEventListener.class, onUpdate);
    }

    /**
     * 编辑验证
     *
     * @param dto
     */
    private void updateValidate(CostBudgetIncomeDto dto) {
        Validate.notNull(dto, "修改时，对象信息不能为空！");
        Validate.notBlank(dto.getId(), "修改数据时，主键不能为空！");
        Validate.notBlank(dto.getDepartmentOneCode(), "部门编码，主键不能为空！");
        Validate.notBlank(dto.getCostCenterCode(), "成本中心编码，主键不能为空！");
        Validate.notBlank(dto.getCompanyCode(), "公司代码，主键不能为空！");
        Validate.notBlank(dto.getCustomerCode(), "客户编码，主键不能为空！");
        Validate.notBlank(dto.getProductCode(), "产品编码，主键不能为空！");
        Validate.notNull(dto.getQuantity(), "数量，主键不能为空！");
        Validate.notNull(dto.getIncomeAmount(), "收入总金额，主键不能为空！");
        Validate.notNull(dto.getCostAmount(), "成本，主键不能为空！");
        Validate.notNull(dto.getLogisticsAmount(), "物流费，主键不能为空！");
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @Override
    public void delete(List<String> idList) {
        Validate.isTrue(!CollectionUtils.isEmpty(idList), "删除数据时，主键集合不能为空！");
        List<CostBudgetIncomeVo> costBudgetVos = this.findByIds(Sets.newHashSet(idList));
        Validate.notEmpty(costBudgetVos, "根据提供的ids主键集合，未能获取到相应信息");
        costBudgetVos.forEach(vo ->
                Validate.isTrue(ConfirmStatusEnum.UNCONFIRMED.getCode().equals(vo.getConfirmStatus()), "【%s】已确认，不能进行删除操作", vo.getIncomeCode()));
        costBudgetIncomeRepository.removeByIds(idList);
    }

    public List<CostBudgetIncomeVo> findByIds(Set<String> ids) {
        List<CostBudgetIncome> costBudgets = costBudgetIncomeRepository.listByIds(ids);
        if (CollectionUtils.isEmpty(costBudgets)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(costBudgets, CostBudgetIncome.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class));
    }

    /**
     * 启禁用
     *
     * @param ids
     * @param enableStatus
     */
    @Override
    public void updateEnableStatus(Set<String> ids, String enableStatus) {
        Validate.notEmpty(ids, "主键id集合不能为空");
        Validate.notBlank(enableStatus, "启禁用状态不能为空");
        Validate.isTrue(EnableStatusEnum.contains(enableStatus), "未知的启禁用状态【%s】，请检查", enableStatus);
        List<CostBudgetIncomeVo> costBudgetVos = this.findByIds(ids);
        Validate.notEmpty(costBudgetVos, "根据提供的ids主键集合，未能获取到相应信息");

        costBudgetIncomeRepository.updateEnableStatus(ids, enableStatus);

        //启禁用业务日志
        CostBudgetIncomeLogEventDto logEventDto = new CostBudgetIncomeLogEventDto();
        logEventDto.setOriginal(null);
        logEventDto.setNewestList(costBudgetVos);
        if (EnableStatusEnum.ENABLE.getCode().equals(enableStatus)) {
            SerializableBiConsumer<CostBudgetIncomeLogEventListener, CostBudgetIncomeLogEventDto> onEnable =
                    CostBudgetIncomeLogEventListener::onEnable;
            this.nebulaNetEventClient.publish(logEventDto, CostBudgetIncomeLogEventListener.class, onEnable);
        } else {
            SerializableBiConsumer<CostBudgetIncomeLogEventListener, CostBudgetIncomeLogEventDto> onDisable =
                    CostBudgetIncomeLogEventListener::onDisable;
            this.nebulaNetEventClient.publish(logEventDto, CostBudgetIncomeLogEventListener.class, onDisable);
        }
    }

    /**
     * 根据费用预算编号确认
     *
     * @param codeList
     */
    @Override
    public void confirm(List<String> codeList) {
        List<CostBudgetIncome> costBudgets = costBudgetIncomeRepository.findByCodeAndTenantCode(new HashSet<>(codeList), TenantUtils.getTenantCode());
        Validate.notEmpty(costBudgets, "未找到对应的预算");

        boolean lock = costBudgetLockVoService.lock(codeList, TimeUnit.SECONDS, 30);
        Validate.isTrue(lock, "操作预算失败，获取操作锁失败！");
        try {
            costBudgets.forEach(e -> e.setConfirmStatus(ConfirmStatusEnum.CONFIRMED.getCode()));
            costBudgetIncomeRepository.saveOrUpdateBatch(costBudgets);
        } finally {
            costBudgetLockVoService.unLock(codeList);
        }
    }

    /**
     * 批量保存
     *
     * @param importVoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(List<CostBudgetIncomeImportVo> importVoList) {
        Collection<CostBudgetIncome> entities = nebulaToolkitService.copyCollectionByWhiteList(importVoList, CostBudgetIncomeImportVo.class, CostBudgetIncome.class, LinkedHashSet.class, ArrayList.class);

        List<String> codeList = this.generateCodeService.generateCodeYearMonth(CostBudgetConstant.INCOME_RULE_CODE, entities.size());
        Validate.notEmpty(codeList, "添加信息时，生成预算编码失败！");
        AtomicInteger index = new AtomicInteger(0);
        entities.forEach(costBudget -> {
            costBudget.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            costBudget.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            costBudget.setTenantCode(TenantUtils.getTenantCode());
            costBudget.setIncomeCode(codeList.get(index.get()));
            // 根据部门编码做权限将部门赋值给权限字段
            costBudget.setOrgCode(costBudget.getDepartmentOneCode());
            index.getAndAdd(1);
        });
        costBudgetIncomeRepository.saveBatch(entities);
    }

    /**
     * 按条件查询
     *
     * @param dto
     */
    @Override
    public List<CostBudgetIncomeVo> findByDto(CostBudgetIncomeDto dto) {
        return costBudgetIncomeRepository.findByCondition(dto);
    }


    /**
     * 通过客户+成本中心+年月查询收入
     *
     * @param customerCodes
     * @param costCenterCodes
     * @param years
     * @return
     */
    @Override
    public List<CostBudgetIncomeVo> findListByCustomerCodesAndOrgCodesAndYears(List<String> customerCodes, List<String> costCenterCodes, List<String> deptCodes, String years) {
        List<CostBudgetIncome> dataList = costBudgetIncomeRepository.findListByCustomerCodesAndOrgCodesAndYears(customerCodes, costCenterCodes, deptCodes, years);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return (List<CostBudgetIncomeVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostBudgetIncome.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class);
    }


    /**
     * 通过组织+成本中心+年月查询收入
     *
     * @param orgCodes
     * @param years
     * @return
     */
    @Override
    public List<CostBudgetIncomeVo> findListByOrgCodesAndYears(List<String> orgCodes, String years) {
        List<CostBudgetIncome> dataList = costBudgetIncomeRepository.findListByOrgCodesAndCostCenterCodesAndYears(orgCodes, years);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        Set<String> itemCodes = dataList.stream()
                .map(CostBudgetIncome::getItemCode)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(itemCodes)) {
            List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(itemCodes);
            Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode,
                    v -> ObjectUtil.defaultIfNull(v.getTaxRate(), BigDecimal.ZERO)));
            dataList.forEach(e -> {
                BigDecimal noTaxIncomeAmount = Optional.ofNullable(e.getIncomeAmount()).orElse(BigDecimal.ZERO)
                        .divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(e.getItemCode())).orElse(BigDecimal.ZERO)),
                                2, RoundingMode.HALF_UP);
                e.setNoTaxIncomeAmount(noTaxIncomeAmount);
            });
        }

        return (List<CostBudgetIncomeVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostBudgetIncome.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<CostBudgetIncomeVo> findListByOrgCodesAndYearsValid(List<String> orgCodes, String years) {
        List<CostBudgetIncome> dataList = costBudgetIncomeRepository
                .findListByOrgCodesAndCostCenterCodesAndYearsConfirm(orgCodes, years);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        Set<String> itemCodes = dataList.stream()
                .map(CostBudgetIncome::getItemCode)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(itemCodes)) {
            List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(itemCodes);
            Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode,
                    v -> ObjectUtil.defaultIfNull(v.getTaxRate(), BigDecimal.ZERO)));
            dataList.forEach(e -> {
                BigDecimal noTaxIncomeAmount = Optional.ofNullable(e.getIncomeAmount()).orElse(BigDecimal.ZERO)
                        .divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(e.getItemCode())).orElse(BigDecimal.ZERO)),
                                2, RoundingMode.HALF_UP);
                e.setNoTaxIncomeAmount(noTaxIncomeAmount);
            });
        }

        return (List<CostBudgetIncomeVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostBudgetIncome.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 通过组织+品项+年月查询收入
     *
     * @param regionOrgCodes
     * @param itemCodes
     * @param years
     * @return
     */
    @Override
    public List<CostBudgetIncomeVo> findListByOrgCodeAndItemCodesAndYears(List<String> regionOrgCodes, List<String> itemCodes, String years) {
        List<CostBudgetIncome> dataList = costBudgetIncomeRepository.findListByOrgCodeAndItemCodesAndYears(regionOrgCodes, itemCodes, years);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return (List<CostBudgetIncomeVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostBudgetIncome.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class);
    }


    @Override
    public List<CostBudgetIncomeVo> findIncomeAmountByOrgCodeAndYears(List<String> orgCodes, String years) {
        List<CostBudgetIncome> dataList = costBudgetIncomeRepository.findIncomeAmountByOrgCodeAndYears(orgCodes, years, null);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return (List<CostBudgetIncomeVo>) nebulaToolkitService.copyCollectionByWhiteList(dataList, CostBudgetIncome.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class);
    }

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private CustomerVoService customerVoService;

    /**
     * 经销商渠道
     */
    private final static String DEALER_CHANNEL = "1";

    @Override
    public List<CostBudgetIncomeVo> findListAllOrgCodeChildrenAndYears(List<String> orgCodes, String years, List<String> yearsList, List<String> channelDepartmentCodeList) {
        List<OrgVo> orgVos = orgVoService.findAllChildrenByOrgCodes(orgCodes);
        List<String> orgCodeList = orgVos.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgCodeList)) {
            return Lists.newArrayList();
        }

        List<String> customerCodes = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(channelDepartmentCodeList)) {
            List<CustomerVo> customerVoList = customerVoService.findCustomerListByChannelDepartmentCodeList(channelDepartmentCodeList);
            customerCodes = customerVoList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toList());
        }
        List<CostBudgetIncome> dataList = costBudgetIncomeRepository.findIncomeAmountByOrgCodeAndYears(orgCodeList, years, yearsList);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        List<CostBudgetIncome> resultList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(channelDepartmentCodeList)) {
            List<String> filterList = Lists.newArrayList();
            if (channelDepartmentCodeList.contains(DEALER_CHANNEL)) {
                //如果是经销商渠道部的直接过滤客户为空的情况
                List<CostBudgetIncome> list = dataList.stream().filter(x -> ObjectUtils.isEmpty(x.getCustomerCode())).collect(Collectors.toList());
                resultList.addAll(list);
                filterList = list.stream().map(x -> x.getCustomerCode()).collect(Collectors.toList());
            }
            for (CostBudgetIncome income : dataList) {
                //判断没有被过滤过的客户并且是包含的客户编码
                if (!filterList.contains(income.getCustomerCode()) && customerCodes.contains(income.getCustomerCode())) {
                    resultList.add(income);
                }
            }
        } else {
            resultList = dataList;
        }
        List<String> itemCodes = resultList.stream().map(CostBudgetIncome::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
        Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> null == v.getTaxRate() ? BigDecimal.ZERO : v.getTaxRate()));
        resultList.forEach(e -> {
            e.setNoTaxIncomeAmount(Optional.ofNullable(e.getIncomeAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(e.getItemCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP));
        });
        return (List<CostBudgetIncomeVo>) nebulaToolkitService.copyCollectionByWhiteList(resultList, CostBudgetIncome.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<CostBudgetIncomeVo> findListAllOrgCodeChildrenAndYearsValid(List<String> orgCodes, String years, List<String> yearsList, List<String> channelDepartmentCodeList) {
        List<OrgVo> orgVos = orgVoService.findAllChildrenByOrgCodes(orgCodes);
        List<String> orgCodeList = orgVos.stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgCodeList)) {
            return Lists.newArrayList();
        }

        List<String> customerCodes = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(channelDepartmentCodeList)) {
            List<CustomerVo> customerVoList = customerVoService.findCustomerListByChannelDepartmentCodeList(channelDepartmentCodeList);
            customerCodes = customerVoList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toList());
        }
        List<CostBudgetIncome> dataList = costBudgetIncomeRepository.findIncomeAmountByOrgCodeAndYearsConfirm(orgCodeList, years, yearsList);
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        List<CostBudgetIncome> resultList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(channelDepartmentCodeList)) {
            List<String> filterList = Lists.newArrayList();
            if (channelDepartmentCodeList.contains(DEALER_CHANNEL)) {
                //如果是经销商渠道部的直接过滤客户为空的情况
                List<CostBudgetIncome> list = dataList.stream().filter(x -> ObjectUtils.isEmpty(x.getCustomerCode())).collect(Collectors.toList());
                resultList.addAll(list);
                filterList = list.stream().map(x -> x.getCustomerCode()).collect(Collectors.toList());
            }
            for (CostBudgetIncome income : dataList) {
                //判断没有被过滤过的客户并且是包含的客户编码
                if (!filterList.contains(income.getCustomerCode()) && customerCodes.contains(income.getCustomerCode())) {
                    resultList.add(income);
                }
            }
        } else {
            resultList = dataList;
        }
        List<String> itemCodes = resultList.stream().map(CostBudgetIncome::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
        Map<String, BigDecimal> itemRateMap = productPhaseVos.stream().collect(Collectors.toMap(ProductPhaseVo::getProductPhaseCode, v -> null == v.getTaxRate() ? BigDecimal.ZERO : v.getTaxRate()));
        resultList.forEach(e -> {
            e.setNoTaxIncomeAmount(Optional.ofNullable(e.getIncomeAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.ONE.add(Optional.ofNullable(itemRateMap.get(e.getItemCode())).orElse(BigDecimal.ZERO)), 2, RoundingMode.HALF_UP));
        });
        return (List<CostBudgetIncomeVo>) nebulaToolkitService.copyCollectionByWhiteList(resultList, CostBudgetIncome.class, CostBudgetIncomeVo.class, HashSet.class, ArrayList.class);
    }
}
