package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.budget.local.entity.CostBudget;
import com.biz.crm.tpm.business.budget.local.mapper.CostBudgetMapper;
import com.biz.crm.tpm.business.budget.sdk.dto.CostBudgetDto;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 费用预算(CostBudget)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-17 12:54:05
 */
@Component
public class CostBudgetRepository extends ServiceImpl<CostBudgetMapper, CostBudget> {

    @Autowired(required = false)
    private CostBudgetMapper costBudgetMapper;

    /**
     * 分页查询数据
     *
     * @param pageable 分页对象
     * @param dto      实体对象
     */
    public Page<CostBudgetVo> findByConditions(Pageable pageable, CostBudgetDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<CostBudgetVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return costBudgetMapper.findByConditions(page, dto);
    }

    /**
     * 根据编码查询信息
     */
    public CostBudget findByCodeAndTenantCode(String code, String tenantCode) {
        return this.lambdaQuery().eq(CostBudget::getCode, code).eq(CostBudget::getTenantCode, tenantCode).eq(CostBudget::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
    }

    /**
     * 根据科目编码查询数据
     *
     * @param budgetSubjectCode 预算科目编码
     */
    public List<CostBudget> findByBudgetSubjectCodeAndTenantCode(String budgetSubjectCode, String tenantCode) {
        return this.lambdaQuery().eq(CostBudget::getBudgetSubjectCode, budgetSubjectCode).eq(CostBudget::getTenantCode, tenantCode).eq(CostBudget::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }

    /**
     * 根据编码查询多条信息
     */
    public List<CostBudget> findByCodesAndTenantCode(Set<String> codes, String tenantCode) {
        return this.lambdaQuery().in(CostBudget::getCode, codes).eq(CostBudget::getTenantCode, tenantCode).eq(CostBudget::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
    }

    /**
     * 启禁用
     */
    public void updateEnableStatus(Set<String> ids, String enableStatus) {
        this.lambdaUpdate()
                .in(CostBudget::getId, ids)
                .eq(CostBudget::getTenantCode, TenantUtils.getTenantCode())
                .set(CostBudget::getEnableStatus, enableStatus).update();
    }

    /**
     * 根据主键id集合，删除数据
     */
    public void deleteByIds(List<String> ids) {
        this.lambdaUpdate()
                .in(CostBudget::getId, ids)
                .eq(CostBudget::getTenantCode, TenantUtils.getTenantCode())
                .set(CostBudget::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
                .update();
    }

    /**
     * 根据组织编码，删除数据
     */
    public void deleteByOrgCodes(Set<String> orgCodes, String tenantCode) {
        this.lambdaUpdate().in(CostBudget::getOrgCode, orgCodes).
                eq(CostBudget::getTenantCode, tenantCode).
                set(CostBudget::getDelFlag, DelFlagStatusEnum.DELETE.getCode()).update();
    }

    /**
     * 根据组织编码和租户编码，变更组织名称
     */
    public void updateByOrgNameAndOrgCodeAndTenantCode(String orgCode, String orgName, String tenantCode) {
        this.lambdaUpdate().eq(CostBudget::getOrgCode, orgCode).
                eq(CostBudget::getTenantCode, tenantCode).
                set(CostBudget::getOrgCode, orgName).update();
    }

    /**
     * 根据组织编码，禁用数据
     */
    public void disableByOrgCodesAndTenantCode(Set<String> orgCodes, String tenantCode) {
        this.lambdaUpdate().in(CostBudget::getOrgCode, orgCodes).
                eq(CostBudget::getTenantCode, tenantCode).
                set(CostBudget::getEnableStatus, EnableStatusEnum.DISABLE.getCode()).update();
    }

    /**
     * 多条件的动态查询
     */
    public List<CostBudget> findByConditions(CostBudgetDto dto) {
        LambdaQueryWrapper<CostBudget> lambdaQuery = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(dto.getCode())) {
            lambdaQuery.eq(CostBudget::getCode, dto.getCode());
        }
        if (StringUtils.isNotBlank(dto.getType())) {
            lambdaQuery.eq(CostBudget::getType, dto.getType());
        }
        if (StringUtils.isNotBlank(dto.getBudgetSubjectCode())) {
            lambdaQuery.eq(CostBudget::getBudgetSubjectCode, dto.getBudgetSubjectCode());
        }
        if (StringUtils.isNotBlank(dto.getBudgetSubjectName())) {
            lambdaQuery.like(CostBudget::getBudgetSubjectName, dto.getBudgetSubjectName());
        }
        if (StringUtils.isNotBlank(dto.getEnableStatus())) {
            lambdaQuery.eq(CostBudget::getEnableStatus, dto.getEnableStatus());
        }
        if (StringUtils.isNotBlank(dto.getTenantCode())) {
            lambdaQuery.eq(CostBudget::getTenantCode, dto.getTenantCode());
        }
        if (StringUtils.isNotBlank(dto.getOrgCode())) {
            lambdaQuery.eq(CostBudget::getOrgCode, dto.getOrgCode());
        }
        if (StringUtils.isNotBlank(dto.getOrgName())) {
            lambdaQuery.like(CostBudget::getOrgName, dto.getOrgName());
        }
        if (StringUtils.isNotBlank(dto.getYearMonthLy())) {
            lambdaQuery.eq(CostBudget::getYearMonthLy, dto.getYearMonthLy());
        }
        if (StringUtils.isNotBlank(dto.getCustomerCode())) {
            lambdaQuery.eq(CostBudget::getCustomerCode, dto.getCustomerCode());
        }
        if (StringUtils.isNotBlank(dto.getCustomerName())) {
            lambdaQuery.like(CostBudget::getCustomerName, dto.getCustomerName());
        }
        if (StringUtils.isNotBlank(dto.getChannelCode())) {
            lambdaQuery.eq(CostBudget::getChannelCode, dto.getChannelCode());
        }
        if (StringUtils.isNotBlank(dto.getChannelName())) {
            lambdaQuery.like(CostBudget::getChannelName, dto.getChannelName());
        }
        if (StringUtils.isNotBlank(dto.getProductCode())) {
            lambdaQuery.eq(CostBudget::getProductCode, dto.getProductCode());
        }
        if (StringUtils.isNotBlank(dto.getItemCode())) {
            lambdaQuery.eq(CostBudget::getItemCode, dto.getItemCode());
        }
        if (StringUtils.isNotBlank(dto.getDepartmentOneCode())) {
            lambdaQuery.eq(CostBudget::getDepartmentOneCode, dto.getDepartmentOneCode());
        }
        if (StringUtils.isNotBlank(dto.getCompanyCode())) {
            lambdaQuery.eq(CostBudget::getCompanyCode, dto.getCompanyCode());
        }
        if (StringUtils.isNotBlank(dto.getCostCenterCode())) {
            lambdaQuery.eq(CostBudget::getCostCenterCode, dto.getCostCenterCode());
        }
        if (StringUtils.isNotBlank(dto.getProductName())) {
            lambdaQuery.like(CostBudget::getProductName, dto.getProductName());
        }
        if (!CollectionUtils.isEmpty(dto.getOrgCodes())) {
            lambdaQuery.in(CostBudget::getOrgCode, dto.getOrgCodes());
        }
        if (!CollectionUtils.isEmpty(dto.getExcludeCodes())) {
            lambdaQuery.notIn(CostBudget::getCode, dto.getExcludeCodes());
        }
        if (StringUtils.isNotBlank(dto.getEnableStatus())) {
            lambdaQuery.eq(CostBudget::getEnableStatus, dto.getEnableStatus());
        }
        if (StringUtils.isBlank(dto.getDelFlag())) {
            lambdaQuery.eq(CostBudget::getDelFlag, DelFlagStatusEnum.NORMAL.getCode());
        } else {
            lambdaQuery.eq(CostBudget::getDelFlag, dto.getDelFlag());
        }
        if (StringUtils.isNotBlank(dto.getUniqueKey())) {
            lambdaQuery.eq(CostBudget::getUniqueKey, dto.getUniqueKey());
        }
        if (StringUtils.isNotBlank(dto.getExcludeId())) {
            lambdaQuery.ne(CostBudget::getId, dto.getExcludeId());
        }
        if (!CollectionUtils.isEmpty(dto.getUniqueKeySet())) {
            lambdaQuery.in(CostBudget::getUniqueKey, dto.getUniqueKeySet());
        }

        return this.baseMapper.selectList(lambdaQuery);
    }

    public CostBudget findByIdAndTenantCode(String id, String tenantCode) {
        return this.lambdaQuery()
                .eq(CostBudget::getTenantCode, tenantCode)
                .in(CostBudget::getId, id)
                .one();
    }

    public List<CostBudget> listByIdsAndTenantCode(Set<String> ids, String tenantCode) {
        return this.lambdaQuery()
                .eq(CostBudget::getTenantCode, tenantCode)
                .in(CostBudget::getId, ids)
                .list();
    }

    /**
     * 按条件查询
     *
     * @param dto
     * @return
     */
    public List<CostBudgetVo> findBudgetCodeByDto(CostBudgetDto dto) {
        return baseMapper.findBudgetCodeByDto(dto);
    }

    /**
     * 预算管控查询预算
     *
     * @param dto
     * @return
     */
    public List<CostBudgetVo> findByDto(CostBudgetDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        List<CostBudgetVo> list = baseMapper.findByDto(dto);
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : list;
    }

    /**
     * 通过客户+成本中心+年月查询费用预算
     *
     * @param customerCodes
     * @param costCenterCodes
     * @param years
     * @return
     */
    public List<CostBudget> findListByCustomerCodesAndCostCenterCodesAndYears(List<String> customerCodes, List<String> costCenterCodes, List<String> deptCodes, String years) {
        return this.lambdaQuery()
                .in(CostBudget::getCustomerCode, customerCodes)
                .in(!CollectionUtils.isEmpty(costCenterCodes), CostBudget::getCostCenterCode, costCenterCodes)
                .in(!CollectionUtils.isEmpty(deptCodes), CostBudget::getDepartmentOneCode, deptCodes)
                .eq(CostBudget::getYearMonthLy, years)
                .eq(CostBudget::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudget::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudget::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }


    public List<CostBudget> findListByOrgCodesAndYears(List<String> orgCodes, String years) {
        return this.lambdaQuery()
                .in(CostBudget::getDepartmentOneCode, orgCodes)
                .eq(CostBudget::getYearMonthLy, years)
                .eq(CostBudget::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudget::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudget::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }

    public List<CostBudget> findListByOrgCodeAndItemCodesAndYears(List<String> regionCodes, List<String> itemCodes, String years) {
        if (CollectionUtils.isEmpty(itemCodes)) {
            return new ArrayList<>();
        }
        return this.lambdaQuery()
                .in(CostBudget::getDepartmentOneCode, regionCodes)
                .in(CostBudget::getItemCode, itemCodes)
                .eq(CostBudget::getYearMonthLy, years)
                .eq(CostBudget::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudget::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudget::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }


    public List<CostBudgetVo> findListByOrgCodeAndCategoryCodesAndYears(List<String> orgCodes, List<String> categoryCodes, String years) {
        return this.baseMapper.findListByOrgCodeAndCategoryCodesAndYears(orgCodes, categoryCodes, years, TenantUtils.getTenantCode());
    }


    public String findCodeByConditions(CostBudgetDto dto) {
        return this.baseMapper.findCodeByConditions(dto, TenantUtils.getTenantCode());
    }


    public List<CostBudget> findListByOrgCodeAndYears(List<String> orgCodes, List<String> yearsList) {
        return this.lambdaQuery()
                .in(CostBudget::getDepartmentOneCode, orgCodes)
                .in(CostBudget::getYearMonthLy, yearsList)
                .eq(CostBudget::getConfirmStatus, ConfirmStatusEnum.CONFIRMED.getCode())
                .eq(CostBudget::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudget::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudget::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }


    public List<CostBudget> findListByOrgCodesAndCustomerCodeAndBudgetSubjectCodesAndYears(List<String> orgCodes, String customerCode, List<String> budgetSubjectCodes, String years){
        return this.lambdaQuery()
                .in(CostBudget::getDepartmentOneCode, orgCodes)
                .eq(CostBudget::getYearMonthLy, years)
                .in(CostBudget::getBudgetSubjectCode,budgetSubjectCodes)
                .eq(CostBudget::getCustomerCode,customerCode)
                .eq(CostBudget::getConfirmStatus, ConfirmStatusEnum.CONFIRMED.getCode())
                .eq(CostBudget::getTenantCode, TenantUtils.getTenantCode())
                .eq(CostBudget::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(CostBudget::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }
}

