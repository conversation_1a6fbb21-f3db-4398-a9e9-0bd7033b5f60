package com.biz.crm.tpm.business.budget.local.service.process;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.tpm.business.budget.sdk.enums.ConfirmStatusEnum;
import com.biz.crm.tpm.business.budget.sdk.service.BudgetSubjectsVoService;
import com.biz.crm.tpm.business.budget.sdk.service.CostBudgetAnalysisService;
import com.biz.crm.tpm.business.budget.sdk.vo.BudgetSubjectsVo;
import com.biz.crm.tpm.business.budget.sdk.vo.CostBudgetAnalysisImportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CostBudgetAnalysisImportProcess implements ImportProcess<CostBudgetAnalysisImportVo> {

    @Autowired
    private CostBudgetAnalysisService costBudgetAnalysisService;
    @Autowired
    private CustomerVoService customerVoServiceFeign;
    @Autowired
    private ProductVoService productVoServiceFeign;
    @Autowired
    private OrgVoService orgVoService;
    @Autowired
    private BudgetSubjectsVoService budgetSubjectsVoService;

    /**
     * 单次缓存的数量
     *
     * @return
     */
    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存) <br/>
     * 新逻辑需同时实现接口 tryVerify tryConfirm（默认方法调用execute）
     *
     * @return
     */
    @Override
    public boolean importBeforeValidationFlag() {
        return Boolean.TRUE.booleanValue();
    }

    /**
     * 数据处理
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, CostBudgetAnalysisImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, CostBudgetAnalysisImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        for (Map.Entry<Integer, CostBudgetAnalysisImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();

            CostBudgetAnalysisImportVo vo = row.getValue();
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getGroupCode()), "分组，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getYearMonthLy()), "年月，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getDepartmentOneCode()), "部门编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostCenterCode()), "成本中心编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCostCenterName()), "成本中心名称，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getBudgetSubjectCode()), "预算科目编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getInitialAmountStr()), "期初金额，不能为空");
            this.validateIsTrue(StringUtils.isNotEmpty(vo.getCompanyCode()), "公司编码，不能为空");
            try {
                BigDecimal amount = new BigDecimal(vo.getInitialAmountStr().trim());
                this.validateIsTrue(amount.compareTo(BigDecimal.ZERO) > 0, "期初金额必须大于0！");
            } catch (Exception e) {
                this.validateIsTrue(false, "期初金额类型转换失败！");
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }
        return errMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, CostBudgetAnalysisImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();

        Set<String> orgCodeSet = new HashSet<>();
        Set<String> productCodeSet = new HashSet<>();
        Set<String> erpCodeSet = new HashSet<>();
        Set<String> budgetSubjectCodeSet = new HashSet<>();
        for (Map.Entry<Integer, CostBudgetAnalysisImportVo> row : data.entrySet()) {
            CostBudgetAnalysisImportVo vo = row.getValue();

            orgCodeSet.add(vo.getDepartmentOneCode());
            if (StringUtils.isNotBlank(vo.getProductCode())) {
                productCodeSet.add(vo.getProductCode());
            }
            if (StringUtils.isNotBlank(vo.getErpCode())) {
                erpCodeSet.add(vo.getErpCode());
            }
            budgetSubjectCodeSet.add(vo.getBudgetSubjectCode());

        }

        Map<String, OrgVo> orgVoMap = orgVoService.findByOrgCodes(new ArrayList<>(orgCodeSet)).stream().collect(Collectors.toMap(e -> e.getOrgCode(), Function.identity(), (a, b) -> a));
        Map<String, Map<String, CustomerVo>> customerVoMap = customerVoServiceFeign.findByErpCodes(new ArrayList<>(erpCodeSet)).stream().collect(Collectors.groupingBy(e -> e.getErpCode(), Collectors.toMap(e -> e.getCompanyCode(), Function.identity(), (a, b) -> a)));
        Map<String, BudgetSubjectsVo> budgetSubjectsVoMap = budgetSubjectsVoService.findByCodes(budgetSubjectCodeSet).stream().collect(Collectors.toMap(e -> e.getBudgetSubjectsCode(), Function.identity(), (a, b) -> a));
        Map<String, ProductVo> productVoMap = productVoServiceFeign.findDetailsByIdsOrProductCodes(null, new ArrayList<>(productCodeSet)).stream().collect(Collectors.toMap(e -> e.getProductCode(), Function.identity(), (a, b) -> a));


        for (Map.Entry<Integer, CostBudgetAnalysisImportVo> row : data.entrySet()) {
            int rowNum = row.getKey();

            CostBudgetAnalysisImportVo vo = row.getValue();
            vo.setInitialAmount(new BigDecimal(vo.getInitialAmountStr()));
            vo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
            if (orgVoMap.containsKey(vo.getDepartmentOneCode())) {
                vo.setDepartmentOneName(orgVoMap.get(vo.getDepartmentOneCode()).getOrgName());
                vo.setLevelNum(orgVoMap.get(vo.getDepartmentOneCode()).getLevelNum());
            } else {
                this.validateIsTrue(false, "部门，未找到！");
            }
            if (budgetSubjectsVoMap.containsKey(vo.getBudgetSubjectCode())) {
                vo.setBudgetSubjectName(budgetSubjectsVoMap.get(vo.getBudgetSubjectCode()).getBudgetSubjectsName());
            } else {
                this.validateIsTrue(false, "预算科目，未找到！");
            }


            if (StringUtils.isNotBlank(vo.getProductCode())) {
                if (productVoMap.containsKey(vo.getProductCode())) {
                    vo.setProductName(productVoMap.get(vo.getProductCode()).getProductName());
                } else {
                    this.validateIsTrue(false, "产品，未找到！");
                }
            }
            if (StringUtils.isNotBlank(vo.getErpCode())) {
                if (customerVoMap.containsKey(vo.getErpCode())) {
                    Map<String, CustomerVo> companyVoMap = customerVoMap.get(vo.getErpCode());
                    if (companyVoMap.containsKey(vo.getCompanyCode())) {
                        CustomerVo customerVo = companyVoMap.get(vo.getCompanyCode());
                        this.validateIsTrue(BooleanEnum.TRUE.getCapital().equals(customerVo.getContractCustomer()), "客户不是合同客户");
                        vo.setCustomerCode(customerVo.getCustomerCode());
                        vo.setCustomerName(customerVo.getCustomerName());
                    } else {
                        this.validateIsTrue(false, "客户，未找到！");
                    }
                } else {
                    this.validateIsTrue(false, "客户，未找到！");
                }
            }

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        }

        if (!errMap.isEmpty()) {
            return errMap;
        }

        costBudgetAnalysisService.saveBatch(new ArrayList<>(data.values()));
        return null;
    }

    /**
     * 获取数据实体
     *
     * @return
     */
    @Override
    public Class<CostBudgetAnalysisImportVo> findCrmExcelVoClass() {
        return CostBudgetAnalysisImportVo.class;
    }

    /**
     * 获取业务对应的模板编码，全局唯一
     *
     * @return
     */
    @Override
    public String getTemplateCode() {
        return "COST_BUDGET_ANALYSIS_IMPORT";
    }

    /**
     * 获取业务对应的模板描述
     *
     * @return
     */
    @Override
    public String getTemplateName() {
        return "预算费用细分导入模板";
    }
}
