package com.biz.crm.tpm.business.track.local.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.track.local.entity.BudgetTrack;
import com.biz.crm.tpm.business.track.local.mapper.BudgetTrackMapper;
import com.biz.crm.tpm.business.track.sdk.dto.BudgetTrackDto;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.List;


/**
 * 预算追踪(BudgetTrack)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-24 15:16:12
 */
@Component
public class BudgetTrackRepository extends ServiceImpl<BudgetTrackMapper, BudgetTrack> {

    @Autowired
    private BudgetTrackMapper budgetTrackMapper;

    /**
     * 按编码查询
     *
     * @param code
     * @return
     */
    public BudgetTrack findByCode(String code) {
        return lambdaQuery().eq(BudgetTrack::getTrackCode, code)
                .eq(BudgetTrack::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(BudgetTrack::getTenantCode, TenantUtils.getTenantCode()).one();
    }

    public List<BudgetTrack> findByBudgetControlCodes(List<String> budgetControlCodes) {
        return lambdaQuery()
                .in(BudgetTrack::getBudgetControlCode, budgetControlCodes)
                .eq(BudgetTrack::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(BudgetTrack::getTenantCode, TenantUtils.getTenantCode()).list();
    }

    /**
     * 按条件查询
     *
     * @param dto
     * @return
     */
    public List<BudgetTrack> findByDto(BudgetTrackDto dto) {
        LambdaQueryWrapper<BudgetTrack> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper
                .eq(BudgetTrack::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(BudgetTrack::getTenantCode, TenantUtils.getTenantCode());
        if (StringUtils.isNotBlank(dto.getMonthStr())) {
            lambdaQueryWrapper.eq(BudgetTrack::getYearMonthLy, dto.getYearStr() + "-" + dto.getMonthStr());
        } else {
            lambdaQueryWrapper.likeRight(BudgetTrack::getYearMonthLy, dto.getYearStr());
        }
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    public void deleteByControlCodeList(List<String> codes) {
        this.lambdaUpdate().in(BudgetTrack::getBudgetControlCode, codes).remove();
    }

    /**
     * 按管控编码删除
     *
     * @param code
     */
    public void deleteByControlCode(String code) {
        this.lambdaUpdate().eq(BudgetTrack::getBudgetControlCode, code).remove();
    }

    public void saveBatchXml(List<BudgetTrack> saveList) {
        if (CollectionUtil.isEmpty(saveList)) {
            return;
        }
        Lists.partition(saveList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
            this.baseMapper.insertBatchSomeColumn(list);
        });
    }
}

