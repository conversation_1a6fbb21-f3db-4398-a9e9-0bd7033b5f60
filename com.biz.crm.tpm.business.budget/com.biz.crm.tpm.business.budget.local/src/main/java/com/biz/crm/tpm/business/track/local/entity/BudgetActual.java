package com.biz.crm.tpm.business.track.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_budget_actual")
@Table(name = "tpm_budget_actual", indexes = {
        @Index(name = "budget_actual_idx1", columnList = "year_month_ly"),
        @Index(name = "tpm_budget_actual_idx1", columnList = "budget_control_code"),
        @Index(name = "tpm_budget_actual_idx2", columnList = "customer_code"),
        @Index(name = "tpm_budget_actual_idx3", columnList = "product_code"),
})
@ApiModel(value = "BudgetActual", description = "预算实际")
@org.hibernate.annotations.Table(appliesTo = "tpm_budget_actual", comment = "预算实际")
public class BudgetActual extends TenantFlagOpEntity {


    @ApiModelProperty("预算管控编码")
    @Column(name = "budget_control_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算管控编码'")
    private String budgetControlCode;

    @ApiModelProperty("预算科目编码")
    @TableField(value = "budget_subject_code")
    @Column(name = "budget_subject_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算科目编码'")
    private String budgetSubjectCode;

    @ApiModelProperty("预算科目名称")
    @TableField(value = "budget_subject_name")
    @Column(name = "budget_subject_name", columnDefinition = "varchar(255) COMMENT '预算科目名称'")
    private String budgetSubjectName;

    @ApiModelProperty("客户编码")
    @TableField(value = "customer_code")
    @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @TableField(value = "customer_name")
    @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
    private String customerName;

    @ApiModelProperty("产品编码")
    @TableField(value = "product_code")
    @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
    private String productCode;

    @ApiModelProperty("产品名称")
    @TableField(value = "product_name")
    @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
    private String productName;

    @ApiModelProperty("年月")
    @Column(name = "year_month_ly", length = 32, columnDefinition = "VARCHAR(32) COMMENT '年月'")
    private String yearMonthLy;

    @ApiModelProperty("一级部门编码")
    @Column(name = "department_one_code", columnDefinition = "VARCHAR(32) COMMENT '一级部门编码'")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称")
    @Column(name = "department_one_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '一级部门名称'")
    private String departmentOneName;

    @ApiModelProperty("部门层级")
    @Column(name = "level_num", length = 32, columnDefinition = "varchar(32) COMMENT '部门层级'")
    private String levelNum;

    @ApiModelProperty("品项编码")
    @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
    private String itemCode;

    @ApiModelProperty("品项名称")
    @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
    private String itemName;

    /**
     * 维度
     */
    @ApiModelProperty("维度")
    @Column(name = "dimension", length = 128, columnDefinition = "varchar(128) COMMENT '维度'")
    private String dimension;

    /**
     * 维度
     */
    @ApiModelProperty("维度值")
    @Column(name = "dimension_value", columnDefinition = "varchar(255) COMMENT '维度值'")
    private String dimensionValue;

    @ApiModelProperty("实际金额")
    @Column(name = "actual_amount", columnDefinition = "decimal(20,4) COMMENT '实际金额'")
    private BigDecimal actualAmount;
}
