package com.biz.crm.tpm.business.adjust.local.repository;



import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.tpm.business.adjust.local.entity.BudgetAdjust;
import com.biz.crm.tpm.business.adjust.local.mapper.BudgetAdjustMapper;
import com.biz.crm.tpm.business.adjust.sdk.dto.BudgetAdjustDto;
import com.biz.crm.tpm.business.adjust.sdk.vo.BudgetAdjustVo;
import com.biz.crm.tpm.business.budget.local.entity.CostBudget;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Pageable;

import java.util.List;


/**
 * 预算调整(BudgetAdjust)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-23 14:49:55
 */
@Component
public class BudgetAdjustRepository extends ServiceImpl<BudgetAdjustMapper, BudgetAdjust> {

  @Autowired
  private BudgetAdjustMapper budgetAdjustMapper;
  
   /**
   * 分页查询数据
   * @param pageable 分页对象
   * @param budgetAdjust 实体对象
   * @return
   */
  public Page<BudgetAdjustVo> findByConditions(Pageable pageable, BudgetAdjustDto budgetAdjust) {
    Page<BudgetAdjustVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    Page<BudgetAdjustVo> pageList = this.budgetAdjustMapper.findByConditions(page, budgetAdjust);
    return pageList;
  }


  /**
   * 根据编码查询信息
   *
   * @param code
   * @param tenantCode
   * @return
   */
  public BudgetAdjust findByCodeAndTenantCode(String code, String tenantCode){
    if (StringUtil.isEmpty(code)){
      return null;
    }
    return this.lambdaQuery().eq(BudgetAdjust::getAdjustCode,code).eq(BudgetAdjust::getTenantCode,tenantCode).eq(BudgetAdjust::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).one();
  }

  public List<BudgetAdjust> findByIds(List<String> ids) {
    return this.lambdaQuery()
            .eq(BudgetAdjust::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
            .eq(BudgetAdjust::getTenantCode, TenantUtils.getTenantCode())
            .in(BudgetAdjust::getId,ids)
            .list();
  }
}

