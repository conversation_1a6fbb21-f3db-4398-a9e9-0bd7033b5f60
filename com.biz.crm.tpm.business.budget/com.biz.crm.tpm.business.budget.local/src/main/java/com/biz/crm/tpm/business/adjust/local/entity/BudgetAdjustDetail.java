package com.biz.crm.tpm.business.adjust.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_budget_adjust_detail")
@Table(name = "tpm_budget_adjust_detail")
@ApiModel(value = "BudgetAdjustDetail", description = "预算调整明细")
@org.hibernate.annotations.Table(appliesTo = "tpm_budget_adjust_detail", comment = "预算调整明细")
public class BudgetAdjustDetail extends TenantFlagOpEntity {

  @ApiModelProperty("调整编码")
  @Column(name = "adjust_code", length = 64, columnDefinition = "varchar(64) COMMENT '调整编码'")
  private String adjustCode;

  @ApiModelProperty("调整名称")
  @Column(name = "adjust_name", length = 128, columnDefinition = "varchar(128) COMMENT '调整名称'")
  private String adjustName;

  @ApiModelProperty("调整明细编码")
  @Column(name = "adjust_detail_code", length = 64, columnDefinition = "varchar(64) COMMENT '调整明细编码'")
  private String adjustDetailCode;

  @ApiModelProperty("预算编码")
  @Column(name = "budget_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算编码'")
  private String budgetCode;

  @ApiModelProperty("预算科目编码")
  @Column(name = "budget_subject_code", length = 64, columnDefinition = "varchar(64) COMMENT '预算科目编码'")
  private String budgetSubjectCode;

  @ApiModelProperty("预算科目名称")
  @Column(name = "budget_subject_name", columnDefinition = "varchar(255) COMMENT '预算科目名称'")
  private String budgetSubjectName;

  @ApiModelProperty("年月")
  @Column(name = "year_month_ly", length = 32, columnDefinition = "VARCHAR(32) COMMENT '年月'")
  private String yearMonthLy;

  @ApiModelProperty("事业部编码")
  @Column(name = "division_code", columnDefinition = "VARCHAR(32) COMMENT '事业部编码'")
  private String divisionCode;

  @ApiModelProperty("事业部名称")
  @Column(name = "division_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '事业部名称'")
  private String divisionName;

  @ApiModelProperty("中心编码")
  @Column(name = "center_code", columnDefinition = "VARCHAR(32) COMMENT '中心编码'")
  private String centerCode;

  @ApiModelProperty("中心名称")
  @Column(name = "center_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '中心名称'")
  private String centerName;

  @ApiModelProperty("一级部门编码")
  @Column(name = "department_one_code", columnDefinition = "VARCHAR(32) COMMENT '一级部门编码'")
  private String departmentOneCode;

  @ApiModelProperty("一级部门名称")
  @Column(name = "department_one_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '一级部门名称'")
  private String departmentOneName;

  @ApiModelProperty("部门层级")
  @Column(name = "level_num", columnDefinition = "int COMMENT '部门层级'")
  private Integer levelNum;

  @ApiModelProperty("公司编码")
  @Column(name = "company_code", columnDefinition = "VARCHAR(32) COMMENT '公司编码'")
  private String companyCode;

  @ApiModelProperty("公司名称")
  @Column(name = "company_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '公司名称'")
  private String companyName;

  @ApiModelProperty("成本中心编码")
  @Column(name = "cost_center_code", columnDefinition = "VARCHAR(32) COMMENT '成本中心编码'")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  @Column(name = "cost_center_name", columnDefinition = "VARCHAR(128) DEFAULT '' COMMENT '成本中心名称'")
  private String costCenterName;

  @ApiModelProperty("品项编码")
  @Column(name = "item_code", length = 32, columnDefinition = "VARCHAR(32) COMMENT '品项编码'")
  private String itemCode;

  @ApiModelProperty("品项名称")
  @Column(name = "item_name", columnDefinition = "VARCHAR(255) COMMENT '品项名称'")
  private String itemName;

  @ApiModelProperty("产品编码")
  @Column(name = "product_code", length = 64, columnDefinition = "varchar(64) COMMENT '产品编码'")
  private String productCode;

  @ApiModelProperty("产品名称")
  @Column(name = "product_name", columnDefinition = "varchar(255) COMMENT '产品名称'")
  private String productName;

  @ApiModelProperty("客户编码")
  @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
  private String customerCode;

  @ApiModelProperty("客户名称")
  @Column(name = "customer_name", columnDefinition = "varchar(255) COMMENT '客户名称'")
  private String customerName;

  @ApiModelProperty("调整金额")
  @Column(name = "adjust_amount", columnDefinition = "decimal(20,6) COMMENT '调整金额'")
  private BigDecimal adjustAmount;

  @ApiModelProperty("期初金额")
  @Column(name = "initial_amount", columnDefinition = "decimal(20,4) COMMENT '期初金额'")
  private BigDecimal initialAmount;

  @ApiModelProperty("月度余额")
  @Column(name = "month_balance_amount", columnDefinition = "decimal(20,6) COMMENT '月度余额'")
  private BigDecimal monthBalanceAmount;

  @ApiModelProperty("调整后余额")
  @Column(name = "final_amount", columnDefinition = "decimal(20,6) COMMENT '调整后余额'")
  private BigDecimal finalAmount;

  @ApiModelProperty("备注")
  @Column(name = "remark", length = 400, columnDefinition = "varchar(400) COMMENT '备注'")
  private String remark;

  @ApiModelProperty("变更类型")
  @Column(name = "change_type", length = 64, columnDefinition = "varchar(64) COMMENT '变更类型'")
  private String changeType;

}
