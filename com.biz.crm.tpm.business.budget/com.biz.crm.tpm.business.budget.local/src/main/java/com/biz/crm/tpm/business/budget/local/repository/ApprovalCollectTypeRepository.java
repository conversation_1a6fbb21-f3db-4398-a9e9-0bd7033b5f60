package com.biz.crm.tpm.business.budget.local.repository;



import com.biz.crm.tpm.business.budget.local.entity.ApprovalCollectTypeEntity;
import com.biz.crm.tpm.business.budget.local.mapper.ApprovalCollectTypeMapper;
import com.biz.crm.tpm.business.budget.sdk.vo.ApprovalCollectTypeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;


/**
 * 核销采集类型信息(ApprovalCollectType)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:32
 */
@Component
public class ApprovalCollectTypeRepository extends ServiceImpl<ApprovalCollectTypeMapper, ApprovalCollectTypeEntity> {

  @Autowired
  private ApprovalCollectTypeMapper approvalCollectTypeMapper;
  @Autowired
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 按编码删除
   *
   * @param code
   */
  public void deleteByCode(String code) {
    this.lambdaUpdate().eq(ApprovalCollectTypeEntity::getCollectCode, code).remove();
  }

  /**
   * 按编码查询
   *
   * @param code
   */
  public List<ApprovalCollectTypeVo> findByCode(String code) {
    List<ApprovalCollectTypeEntity> list = this.lambdaQuery().eq(ApprovalCollectTypeEntity::getCollectCode, code)
            .list();
    return CollectionUtils.isNotEmpty(list) ? new ArrayList<>(nebulaToolkitService.copyCollectionByWhiteList(list, ApprovalCollectTypeEntity.class, ApprovalCollectTypeVo.class, LinkedHashSet.class, ArrayList.class)) :
            new ArrayList<>();
  }
}

