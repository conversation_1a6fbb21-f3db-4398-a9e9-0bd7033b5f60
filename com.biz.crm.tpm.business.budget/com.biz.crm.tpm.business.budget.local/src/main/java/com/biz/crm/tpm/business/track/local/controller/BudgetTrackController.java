package com.biz.crm.tpm.business.track.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.business.track.sdk.dto.BudgetTrackDto;
import com.biz.crm.tpm.business.track.sdk.service.BudgetTrackService;
import com.biz.crm.tpm.business.track.sdk.vo.BudgetTrackVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 预算跟踪
 */
@RestController
@RequestMapping("/v1/budget/budgetTrack")
@Slf4j
@Api(tags = "预算跟踪")
public class BudgetTrackController {

    @Autowired(required = false)
    private BudgetTrackService budgetTrackService;

    /**
     * 通过编码查询单条数据
     *
     * @param code 编码
     * @return 单条数据
     */
    @ApiOperation(value = "通过编码查询单条数据")
    @GetMapping("findByCode")
    public Result<BudgetTrackVo> findByCode(@RequestParam("code") @ApiParam(name = "code", value = "编码") String code) {
        try {
            BudgetTrackVo costBudget = this.budgetTrackService.findByCode(code);
            return Result.ok(costBudget);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改费率
     * 
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改费率")
    @PostMapping("updateRate")
    public Result<?> updateRate(@ApiParam(name = "dto", value = "预算跟踪信息") @RequestBody BudgetTrackDto dto) {
        try {
            this.budgetTrackService.updateRate(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 费率计算
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "费率计算")
    @PostMapping("calculateRate")
    public Result<?> calculateRate(@ApiParam(name = "dto", value = "预算跟踪信息") @RequestBody BudgetTrackDto dto) {
        try {
            this.budgetTrackService.calculateRate(dto);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "更新预算跟踪", httpMethod = "GET")
    @GetMapping("/generateBudgetTrack")
    public Result generateBudgetTrack() {
        try {
            this.budgetTrackService.generateBudgetTrack();
            return Result.ok("更新预算跟踪");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "手动更新预算跟踪", httpMethod = "GET")
    @GetMapping("/generateBudgetTrackYearMonth")
    public Result generateBudgetTrackYearMonth(@RequestParam("yearMonthLy") @ApiParam(name = "yearMonthLy", value = "年月") String yearMonthLy,
                                               @RequestParam(value = "controlCode", required = false) @ApiParam(name = "controlCode", value = "管控编码") String controlCode) {
        try {
            this.budgetTrackService.generateBudgetTrackYearMonth(yearMonthLy, controlCode);
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
