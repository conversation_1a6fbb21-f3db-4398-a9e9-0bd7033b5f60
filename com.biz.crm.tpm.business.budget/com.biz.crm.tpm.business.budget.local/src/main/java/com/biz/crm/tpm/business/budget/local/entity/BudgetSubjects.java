package com.biz.crm.tpm.business.budget.local.entity;

import lombok.Getter;
import lombok.Setter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;

 /**
 * 实体：TPM-预算科目;
 * <AUTHOR> <PERSON>
 * @date : 2022-5-18
 */
@ApiModel(value = "BudgetSubjects",description = "TPM-预算科目")
@TableName("tpm_budget_subjects")
@Getter
@Setter
@Entity(name = "tpm_budget_subjects")
@org.hibernate.annotations.Table(appliesTo = "tpm_budget_subjects", comment = "TPM-预算科目")
public class BudgetSubjects  extends TenantFlagOpEntity{

  /** 预算科目编码 */
  @ApiModelProperty(name = "预算科目编码",notes = "")
  @Column(name = "budget_subjects_code", nullable = false, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目编码 '")
  private String budgetSubjectsCode;
  
  /** 预算科目名称 */
  @ApiModelProperty(name = "预算科目名称",notes = "")
  @Column(name = "budget_subjects_name", nullable = false, length = 128,  columnDefinition = "VARCHAR(128) COMMENT '预算科目名称 '")
  private String budgetSubjectsName;

    /** 上级预算科目编码 */
    @ApiModelProperty("上级预算科目编码")
    @Column(name = "parent_budget_subjects_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '上级预算科目编码 '")
    private String parentBudgetSubjectsCode;

    /** 上级预算科目名称 */
    @ApiModelProperty("上级预算科目名称")
    @Column(name = "parent_budget_subjects_name", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '上级预算科目名称 '")
    private String parentBudgetSubjectsName;

    /** 科目层级 */
    @ApiModelProperty("科目层级,数据字典(budget_subject_level)")
    @Column(name = "level", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '科目层级 '")
    private String level;
  
    /** 预算科目类型(数据字典) */
    @ApiModelProperty(name = "预算科目类型(数据字典)",notes = "")
    @Column(name = "budget_subjects_type", nullable = true, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目类型(数据字典) '")
    private String budgetSubjectsType;

    /** 控制类型编码 */
    @ApiModelProperty(name = "控制类型编码",notes = "")
    @Column(name = "control_type_code", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '控制类型编码 '")
    private String controlTypeCode;

   /** 控制类型名称 */
   @ApiModelProperty(name = "控制类型名称",notes = "")
   @Column(name = "control_type_name", length = 128,  columnDefinition = "VARCHAR(128) COMMENT '控制类型名称 '")
   private String controlTypeName;

   /** 合作类型 */
   @ApiModelProperty(name = "cooperateType",notes = "合作类型", value = "合作类型")
   @Column(name = "cooperate_type", length = 64,  columnDefinition = "VARCHAR(64) COMMENT '合作类型 '")
   private String cooperateType;
  
    /** 预算科目分组(数据字典) */
    @ApiModelProperty(name = "预算科目分组(数据字典)",notes = "")
    @Column(name = "group_code", nullable = true, length = 64,  columnDefinition = "VARCHAR(64) COMMENT '预算科目分组(数据字典) '")
    private String groupCode;

    @ApiModelProperty("职位编码")
    @Column(name = "position_code", length = 32, columnDefinition = "varchar(32) COMMENT '职位编码'")
    private String positionCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '组织编码'")
    private String orgCode;

    @ApiModelProperty(name = "税率")
    @Column(name = "tax_rate")
    private BigDecimal taxRate;
}