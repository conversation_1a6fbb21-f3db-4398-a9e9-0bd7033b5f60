package com.biz.crm.tpm.business.budget.local.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.tpm.business.budget.local.entity.CostTypeCategoryRange;
import com.biz.crm.tpm.business.budget.local.mapper.CostTypeCategoryRangeMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import java.util.Collection;
import java.util.Collections;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * TPM-活动大类范围;(tpm_cost_type_category_range)表数据库访问层
 *
 * <AUTHOR> Keller
 * @date : 2022-5-19
 */
@Component
@Deprecated
public class CostTypeCategoryRangeRepository extends ServiceImpl<CostTypeCategoryRangeMapper, CostTypeCategoryRange> {
  @Autowired
  private CostTypeCategoryRangeMapper costTypeCategoryRangeMapper;

  /**
   * 根据活动大类编号与租户编号获取对象
   *
   * @param categoryCode
   * @return
   */
  public List<CostTypeCategoryRange> findByCategoryCode(String categoryCode) {
    if (StringUtils.isBlank(categoryCode)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .eq(CostTypeCategoryRange::getCategoryCode, categoryCode)
            .eq(CostTypeCategoryRange::getTenantCode, tenantCode)
            .eq(CostTypeCategoryRange::getDelFlag, DelFlagStatusEnum.NORMAL.getCode()).list();
  }

  /**
   * 根据编号与租户编号获取对象
   *
   * @param codes
   * @return
   */
  public List<CostTypeCategoryRange> findByCodes(Set<String> codes) {
    if (CollectionUtils.isEmpty(codes)) {
      return Collections.emptyList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    return this.lambdaQuery()
            .in(CostTypeCategoryRange::getRangeCode, codes)
            .eq(CostTypeCategoryRange::getTenantCode, tenantCode).list();
  }

  /**
   * 根据活动大类编号与租户编号删除范围数据
   *
   * @param categoryCode
   */
  public void deleteByCategoryCode(String categoryCode) {
    if (StringUtils.isBlank(categoryCode)) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    this.lambdaUpdate()
            .eq(CostTypeCategoryRange::getCategoryCode, categoryCode)
            .eq(CostTypeCategoryRange::getTenantCode, tenantCode)
            .remove();
  }

  /**
   * 根据活动大类编号与租户编号删除范围数据
   *
   * @param rangeCodes
   */
  public void deleteByRangeCodes(Collection<String> rangeCodes) {
    if (CollectionUtils.isEmpty(rangeCodes)) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    UpdateWrapper<CostTypeCategoryRange> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("del_flag", DelFlagStatusEnum.DELETE.getCode());
    updateWrapper.in("range_code", rangeCodes);
    updateWrapper.eq("tenant_code", tenantCode);
    this.update(updateWrapper);
  }

  /**
   * 批量根据id禁用
   *
   * @param enable
   * @param codes
   */
  public void updateEnableStatusByCodes(EnableStatusEnum enable, Set<String> codes) {
    UpdateWrapper<CostTypeCategoryRange> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("enable_status", enable.getCode());
    updateWrapper.eq("tenant_code",TenantUtils.getTenantCode());
    updateWrapper.in("range_code", codes);
    this.update(updateWrapper);
  }

  /**
   * 根据编号 修改名称
   *
   * @param code
   * @param name
   */
  public void updateName(String code, String name) {
    UpdateWrapper<CostTypeCategoryRange> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("range_name", code);
    updateWrapper.eq("range_code", name);
    updateWrapper.eq("tenant_code",TenantUtils.getTenantCode());
    this.update(updateWrapper);
  }

  /**
   *  查询全部
   * @return
   */
  public List<CostTypeCategoryRange> findAll() {
    return this.lambdaQuery()
        .eq(CostTypeCategoryRange::getTenantCode,TenantUtils.getTenantCode())
        .list();
  }
}