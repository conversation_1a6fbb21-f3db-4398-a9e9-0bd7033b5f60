### CRM3.0 workflow模块接入说明

#### 1.模块说明

###### workflow模块是基于crm2.0 bpm模块移植而来，实现了与crm3.0一致的脱藕式结构，主要功能保持与crm2.0的一致，故前端页面也基本保持不变。接口调用与数据衔接使用nebula模块的event事件进行交互，不再强依赖于mdm模块。

#### 2.接入配置

###### 接入workflow模块必须接入前置配置 

###### event配置，用于事件通信

```
<dependency>
  <groupId>com.biz-united.nebula.event</groupId>
  <artifactId>event-local-starter</artifactId>
  <version>2.4.2.0</version>
</dependency>
```

###### workflow-sdk，用于继承请求参数以及回调方法

    <dependency>
      <groupId>com.biz.crm</groupId>
      <artifactId>workflow-sdk</artifactId>
      <version>${crm.workflow.version</version>
    </dependency>
crm-com已经提供了workflow的父类entity，vo，dto，可根据需要自行选择继承或添加参数

```
com.biz.crm.business.common.sdk.dto.WorkflowFlagOpDto 
com.biz.crm.business.common.local.entity.WorkflowFlagOpEntity
com.biz.crm.business.common.sdk.vo.WorkflowFlagOpVo
```

传参dto还应额外添加

```
/**
 * 流程附件信息
 */
@ApiModelProperty(value = "流程附件信息")
private List<AttachmentVo> attachmentVos;
```

#### 3.接口调用

构建提交流程DTO，调用创建流程事件进行提交，示例如下

```
//发起流程DTO
StartProcessDto activitiBusinessVo = new StartProcessDto();
//流程KEY,前端传入，必传
activitiBusinessVo.setProcessKey(dto.getProcessKey());
//业务对应id，工作流展示用，非必传
activitiBusinessVo.setBusinessId(dto.getId());
//业务对应编码,例如客户编码。工作流展示用，非必传
activitiBusinessVo.setBusinessNo(dto.getCreditCode());
//流程发起备注，非必传
activitiBusinessVo.setRemark(dto.getProcessRemark());
//流程发起备注，非必传
activitiBusinessVo.setAttachmentVos(dto.getAttachmentVos());
//菜单编码，此参数决定流程业务表单展示，由前端传入，非必传
activitiBusinessVo.setMenuCode(dto.getCompetenceCode());
//业务来源，此参数判定回调业务处理，请自行定义常量并确保唯一，必传
activitiBusinessVo.setFormType(CreditConstant.CREDIT_PROCESS_NAME);
//流程标题，前端传入
activitiBusinessVo.setProcessTitle(dto.getProcessTitle());

//流程业务参数，以map形式进行传参，非必传，结合业务进行操作
Map<String, Object> maps = new HashMap<>();
maps.put("credit_amount", dto.getCreditAmount());
activitiBusinessVo.setVariables(maps);

//流程指标参数，以json形式传参，非必传，结合具体业务操作
JSONObject object = new JSONObject();
object.put("org_code", "admin");
activitiBusinessVo.setKeyTargetListJson(object.toJSONString());

//实例化流程提交事件并进行提交
SerializableBiConsumer<ProcessListener, StartProcessDto> sf = ProcessListener::onStartProcess;
CommitWorkflowResponse response = (CommitWorkflowResponse) nebulaNetEventClient.directPublish(activitiBusinessVo, ProcessListener.class, sf);

//发起流程后正常时会返回唯一的流程编号，建议在业务端进行存储，以便回调查询使用
if (StringUtils.isNotEmpty(response.getProcessInstanceId())) {
  return response.getProcessInstanceId();
} else {
  throw new RuntimeException("发起流程失败！");
}
```

#### 4.回调实现

workflow基于事件进行回调，当流程审批结束/被驳回/追回 等操作更改流程状态时，会触发回调事件，业务端根据业务类型和流程编码，处理对应的业务逻辑

示例如下：

```
@Component
public class CustomerCallbackListener implements CallBackListener {

  @Override
  public void onCallBack(CallBackDto dto) {
    //校验流程业务来源，非本业务来源不进行处理
    if (!dto.getFormType().equals(CustomerConstant.CUSTOMER_PROCESS_NAME)) {
      return;
    }
    //校验单号
    CustomerEntity customerEntity = this.customerRepository.findByProcessNumber(dto.getProcessNo());
    if (ObjectUtils.isEmpty(customerEntity)) {
      return;
    }
    //校验审批状态
    Validate.isTrue(customerEntity.getProcessStatus().equals(ActApproveStatusEnum.APPROVING.getCode()), "此订单非审批中状态，无法进行操作！");
    //根据回调类别处理对应业务
    if (StringUtils.equals(String.valueOf(dto.getProcessState()), ActApproveStatusEnum.APPROVED.getCode())) {
      //审批通过业务处理
    } else if (StringUtils.equals(String.valueOf(dto.getProcessState()), ActApproveStatusEnum.REJECTED.getCode())) {
      //审批驳回业务处理      
    } else if (StringUtils.equals(String.valueOf(dto.getProcessState()), ActApproveStatusEnum.INTERRUPT.getCode())) {
      //流程追回业务处理
     
    }
    //---------------  end -------------------
  }
}
```

#### 5.注意事项

暂无